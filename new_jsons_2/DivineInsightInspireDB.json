[{"id": 10010011, "level": 1, "rank": 1, "limit": 3, "prePlot": "0", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "60", "time": "600", "preLimit": "0|0|0", "columnLimit": "0|0|0"}, {"id": 10010021, "level": 1, "rank": 2, "limit": 3, "prePlot": "10010011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "60", "time": "1200", "preLimit": "3|3|3", "columnLimit": "0|0|0"}, {"id": 10010031, "level": 1, "rank": 3, "limit": 3, "prePlot": "10010021", "skill": "682504", "talent": "0", "fightSkill": "0", "mineNeed": "60", "time": "1800", "preLimit": "3|3|3", "columnLimit": "0|0|0"}, {"id": 10010032, "level": 1, "rank": 3, "limit": 3, "prePlot": "10010021", "skill": "682503", "talent": "0", "fightSkill": "0", "mineNeed": "60", "time": "1800", "preLimit": "3|3|3", "columnLimit": "0|0|0"}, {"id": 10010041, "level": 1, "rank": 4, "limit": 3, "prePlot": "10010031|10010032", "skill": "682513", "talent": "0", "fightSkill": "0", "mineNeed": "60", "time": "3600", "preLimit": "3|3|3", "columnLimit": "0|0|0"}, {"id": 10010051, "level": 1, "rank": 5, "limit": 1, "prePlot": "10010041", "skill": "0", "talent": "680001", "fightSkill": "0", "mineNeed": "60", "time": "5400", "preLimit": "3|3|3", "columnLimit": "0|0|0"}, {"id": 10010061, "level": 1, "rank": 6, "limit": 3, "prePlot": "10010051", "skill": "682505", "talent": "0", "fightSkill": "0", "mineNeed": "60", "time": "6600", "preLimit": "1|1|1", "columnLimit": "0|0|0"}, {"id": 10010062, "level": 1, "rank": 6, "limit": 3, "prePlot": "10010051", "skill": "682507", "talent": "0", "fightSkill": "0", "mineNeed": "60", "time": "6600", "preLimit": "1|1|1", "columnLimit": "0|0|0"}, {"id": 10010071, "level": 1, "rank": 7, "limit": 3, "prePlot": "10010061|10010062", "skill": "682517", "talent": "0", "fightSkill": "0", "mineNeed": "60", "time": "7800", "preLimit": "3|3|3", "columnLimit": "0|0|0"}, {"id": 10010081, "level": 1, "rank": 8, "limit": 1, "prePlot": "10010071", "skill": "682001", "talent": "0", "fightSkill": "0", "mineNeed": "60", "time": "9600", "preLimit": "3|3|3", "columnLimit": "0|0|0"}, {"id": 10010091, "level": 1, "rank": 9, "limit": 1, "prePlot": "10010081", "skill": "0", "talent": "0", "fightSkill": "681001", "mineNeed": "60", "time": "13200", "preLimit": "1|1|1", "columnLimit": "0|0|0"}, {"id": 10020011, "level": 2, "rank": 1, "limit": 3, "prePlot": "10010091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "70", "time": "16800", "preLimit": "1|1|1", "columnLimit": "0|0|0"}, {"id": 10020021, "level": 2, "rank": 2, "limit": 3, "prePlot": "10020011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "70", "time": "16800", "preLimit": "3|3|3", "columnLimit": "0|0|0"}, {"id": 10020031, "level": 2, "rank": 3, "limit": 3, "prePlot": "10020021", "skill": "682506", "talent": "0", "fightSkill": "0", "mineNeed": "70", "time": "16800", "preLimit": "3|3|3", "columnLimit": "0|0|0"}, {"id": 10020032, "level": 2, "rank": 3, "limit": 3, "prePlot": "10020021", "skill": "682508", "talent": "0", "fightSkill": "0", "mineNeed": "70", "time": "16800", "preLimit": "3|3|3", "columnLimit": "0|0|0"}, {"id": 10020041, "level": 2, "rank": 4, "limit": 3, "prePlot": "10020031|10020032", "skill": "682510", "talent": "0", "fightSkill": "0", "mineNeed": "70", "time": "20400", "preLimit": "3|3|3", "columnLimit": "0|0|0"}, {"id": 10020051, "level": 2, "rank": 5, "limit": 1, "prePlot": "10020041", "skill": "0", "talent": "680002", "fightSkill": "0", "mineNeed": "70", "time": "20400", "preLimit": "3|3|3", "columnLimit": "0|0|0"}, {"id": 10020061, "level": 2, "rank": 6, "limit": 3, "prePlot": "10020051", "skill": "682504", "talent": "0", "fightSkill": "0", "mineNeed": "70", "time": "20400", "preLimit": "1|1|1", "columnLimit": "0|0|0"}, {"id": 10020062, "level": 2, "rank": 6, "limit": 3, "prePlot": "10020051", "skill": "682503", "talent": "0", "fightSkill": "0", "mineNeed": "70", "time": "20400", "preLimit": "1|1|1", "columnLimit": "0|0|0"}, {"id": 10020071, "level": 2, "rank": 7, "limit": 3, "prePlot": "10020061|10020062", "skill": "682518", "talent": "0", "fightSkill": "0", "mineNeed": "70", "time": "27600", "preLimit": "3|3|3", "columnLimit": "0|0|0"}, {"id": 10020081, "level": 2, "rank": 8, "limit": 1, "prePlot": "10020071", "skill": "682002", "talent": "0", "fightSkill": "0", "mineNeed": "70", "time": "27600", "preLimit": "3|3|3", "columnLimit": "0|0|0"}, {"id": 10020091, "level": 2, "rank": 9, "limit": 1, "prePlot": "10020081", "skill": "682533", "talent": "0", "fightSkill": "0", "mineNeed": "70", "time": "27600", "preLimit": "1|1|1", "columnLimit": "0|0|0"}, {"id": 10030011, "level": 3, "rank": 1, "limit": 3, "prePlot": "10020091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "80", "time": "42000", "preLimit": "1|1|1", "columnLimit": "0|0|0"}, {"id": 10030021, "level": 3, "rank": 2, "limit": 3, "prePlot": "10030011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "80", "time": "42000", "preLimit": "3|3|3", "columnLimit": "0|0|0"}, {"id": 10030031, "level": 3, "rank": 3, "limit": 3, "prePlot": "10030021", "skill": "682505", "talent": "0", "fightSkill": "0", "mineNeed": "80", "time": "42000", "preLimit": "3|3|3", "columnLimit": "0|0|0"}, {"id": 10030032, "level": 3, "rank": 3, "limit": 3, "prePlot": "10030021", "skill": "682507", "talent": "0", "fightSkill": "0", "mineNeed": "80", "time": "42000", "preLimit": "3|3|3", "columnLimit": "0|0|0"}, {"id": 10030041, "level": 3, "rank": 4, "limit": 3, "prePlot": "10030031|10030032", "skill": "682511", "talent": "0", "fightSkill": "0", "mineNeed": "80", "time": "54000", "preLimit": "3|3|3", "columnLimit": "0|0|0"}, {"id": 10030051, "level": 3, "rank": 5, "limit": 1, "prePlot": "10030041", "skill": "0", "talent": "680015", "fightSkill": "0", "mineNeed": "80", "time": "54000", "preLimit": "3|3|3", "columnLimit": "0|0|0"}, {"id": 10030061, "level": 3, "rank": 6, "limit": 3, "prePlot": "10030051", "skill": "682506", "talent": "0", "fightSkill": "0", "mineNeed": "80", "time": "54000", "preLimit": "1|1|1", "columnLimit": "0|0|0"}, {"id": 10030062, "level": 3, "rank": 6, "limit": 3, "prePlot": "10030051", "skill": "682508", "talent": "0", "fightSkill": "0", "mineNeed": "80", "time": "54000", "preLimit": "1|1|1", "columnLimit": "0|0|0"}, {"id": 10030071, "level": 3, "rank": 7, "limit": 3, "prePlot": "10030061|10030062", "skill": "682517", "talent": "0", "fightSkill": "0", "mineNeed": "80", "time": "54000", "preLimit": "3|3|3", "columnLimit": "0|0|0"}, {"id": 10030081, "level": 3, "rank": 8, "limit": 1, "prePlot": "10030071", "skill": "682003", "talent": "0", "fightSkill": "0", "mineNeed": "80", "time": "54000", "preLimit": "3|3|3", "columnLimit": "0|0|0"}, {"id": 10030091, "level": 3, "rank": 9, "limit": 1, "prePlot": "10030081", "skill": "682536", "talent": "0", "fightSkill": "0", "mineNeed": "80", "time": "54000", "preLimit": "1|1|1", "columnLimit": "0|0|0"}, {"id": 10040011, "level": 4, "rank": 1, "limit": 3, "prePlot": "10030091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "90", "time": "60000", "preLimit": "1|1|1", "columnLimit": "0|0|0"}, {"id": 10040021, "level": 4, "rank": 2, "limit": 3, "prePlot": "10040011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "90", "time": "60000", "preLimit": "3|3|3", "columnLimit": "0|0|0"}, {"id": 10040031, "level": 4, "rank": 3, "limit": 3, "prePlot": "10040021", "skill": "682504", "talent": "0", "fightSkill": "0", "mineNeed": "90", "time": "60000", "preLimit": "3|3|3", "columnLimit": "0|0|0"}, {"id": 10040032, "level": 4, "rank": 3, "limit": 3, "prePlot": "10040021", "skill": "682503", "talent": "0", "fightSkill": "0", "mineNeed": "90", "time": "60000", "preLimit": "3|3|3", "columnLimit": "0|0|0"}, {"id": 10040041, "level": 4, "rank": 4, "limit": 3, "prePlot": "10040031|10040032", "skill": "682514", "talent": "0", "fightSkill": "0", "mineNeed": "90", "time": "60000", "preLimit": "3|3|3", "columnLimit": "0|0|0"}, {"id": 10040051, "level": 4, "rank": 5, "limit": 1, "prePlot": "10040041", "skill": "0", "talent": "680003", "fightSkill": "0", "mineNeed": "90", "time": "60000", "preLimit": "3|3|3", "columnLimit": "0|0|0"}, {"id": 10040061, "level": 4, "rank": 6, "limit": 3, "prePlot": "10040051", "skill": "682505", "talent": "0", "fightSkill": "0", "mineNeed": "90", "time": "60000", "preLimit": "1|1|1", "columnLimit": "0|0|0"}, {"id": 10040062, "level": 4, "rank": 6, "limit": 3, "prePlot": "10040051", "skill": "682507", "talent": "0", "fightSkill": "0", "mineNeed": "90", "time": "60000", "preLimit": "1|1|1", "columnLimit": "0|0|0"}, {"id": 10040071, "level": 4, "rank": 7, "limit": 3, "prePlot": "10040061|10040062", "skill": "682518", "talent": "0", "fightSkill": "0", "mineNeed": "90", "time": "60000", "preLimit": "3|3|3", "columnLimit": "0|0|0"}, {"id": 10040081, "level": 4, "rank": 8, "limit": 1, "prePlot": "10040071", "skill": "682004", "talent": "0", "fightSkill": "0", "mineNeed": "90", "time": "60000", "preLimit": "3|3|3", "columnLimit": "0|0|0"}, {"id": 10040091, "level": 4, "rank": 9, "limit": 1, "prePlot": "10040081", "skill": "682535", "talent": "0", "fightSkill": "0", "mineNeed": "90", "time": "60000", "preLimit": "1|1|1", "columnLimit": "0|0|0"}, {"id": 10050011, "level": 5, "rank": 1, "limit": 3, "prePlot": "10040091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "100", "time": "66000", "preLimit": "1|1|1", "columnLimit": "0|0|0"}, {"id": 10050021, "level": 5, "rank": 2, "limit": 3, "prePlot": "10050011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "100", "time": "66000", "preLimit": "3|3|3", "columnLimit": "0|0|0"}, {"id": 10050031, "level": 5, "rank": 3, "limit": 3, "prePlot": "10050021", "skill": "682506", "talent": "0", "fightSkill": "0", "mineNeed": "100", "time": "66000", "preLimit": "3|3|3", "columnLimit": "0|0|0"}, {"id": 10050032, "level": 5, "rank": 3, "limit": 3, "prePlot": "10050021", "skill": "682508", "talent": "0", "fightSkill": "0", "mineNeed": "100", "time": "66000", "preLimit": "3|3|3", "columnLimit": "0|0|0"}, {"id": 10050041, "level": 5, "rank": 4, "limit": 3, "prePlot": "10050031|10050032", "skill": "682509", "talent": "0", "fightSkill": "0", "mineNeed": "100", "time": "66000", "preLimit": "3|3|3", "columnLimit": "0|0|0"}, {"id": 10050051, "level": 5, "rank": 5, "limit": 1, "prePlot": "10050041", "skill": "0", "talent": "680017", "fightSkill": "0", "mineNeed": "100", "time": "66000", "preLimit": "3|3|3", "columnLimit": "0|0|0"}, {"id": 10050061, "level": 5, "rank": 6, "limit": 3, "prePlot": "10050051", "skill": "682504", "talent": "0", "fightSkill": "0", "mineNeed": "100", "time": "66000", "preLimit": "1|1|1", "columnLimit": "0|0|0"}, {"id": 10050062, "level": 5, "rank": 6, "limit": 3, "prePlot": "10050051", "skill": "682503", "talent": "0", "fightSkill": "0", "mineNeed": "100", "time": "66000", "preLimit": "1|1|1", "columnLimit": "0|0|0"}, {"id": 10050071, "level": 5, "rank": 7, "limit": 3, "prePlot": "10050061|10050062", "skill": "682517", "talent": "0", "fightSkill": "0", "mineNeed": "100", "time": "66000", "preLimit": "3|3|3", "columnLimit": "0|0|0"}, {"id": 10050081, "level": 5, "rank": 8, "limit": 1, "prePlot": "10050071", "skill": "682005", "talent": "0", "fightSkill": "0", "mineNeed": "100", "time": "66000", "preLimit": "3|3|3", "columnLimit": "0|0|0"}, {"id": 10050091, "level": 5, "rank": 9, "limit": 1, "prePlot": "10050081", "skill": "0", "talent": "0", "fightSkill": "681002", "mineNeed": "100", "time": "66000", "preLimit": "1|1|1", "columnLimit": "0|0|0"}, {"id": 10060011, "level": 6, "rank": 1, "limit": 3, "prePlot": "10050091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "110", "time": "72000", "preLimit": "1|1|1", "columnLimit": "5|5|5"}, {"id": 10060021, "level": 6, "rank": 2, "limit": 3, "prePlot": "10060011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "110", "time": "72000", "preLimit": "3|3|3", "columnLimit": "5|5|5"}, {"id": 10060031, "level": 6, "rank": 3, "limit": 3, "prePlot": "10060021", "skill": "682505", "talent": "0", "fightSkill": "0", "mineNeed": "110", "time": "72000", "preLimit": "3|3|3", "columnLimit": "5|5|5"}, {"id": 10060032, "level": 6, "rank": 3, "limit": 3, "prePlot": "10060021", "skill": "682507", "talent": "0", "fightSkill": "0", "mineNeed": "110", "time": "72000", "preLimit": "3|3|3", "columnLimit": "5|5|5"}, {"id": 10060041, "level": 6, "rank": 4, "limit": 3, "prePlot": "10060031|10060032", "skill": "682516", "talent": "0", "fightSkill": "0", "mineNeed": "110", "time": "72000", "preLimit": "3|3|3", "columnLimit": "5|5|5"}, {"id": 10060051, "level": 6, "rank": 5, "limit": 1, "prePlot": "10060041", "skill": "0", "talent": "680004", "fightSkill": "0", "mineNeed": "110", "time": "72000", "preLimit": "3|3|3", "columnLimit": "5|5|5"}, {"id": 10060061, "level": 6, "rank": 6, "limit": 3, "prePlot": "10060051", "skill": "682506", "talent": "0", "fightSkill": "0", "mineNeed": "110", "time": "72000", "preLimit": "1|1|1", "columnLimit": "5|5|5"}, {"id": 10060062, "level": 6, "rank": 6, "limit": 3, "prePlot": "10060051", "skill": "682508", "talent": "0", "fightSkill": "0", "mineNeed": "110", "time": "72000", "preLimit": "1|1|1", "columnLimit": "5|5|5"}, {"id": 10060071, "level": 6, "rank": 7, "limit": 3, "prePlot": "10060061|10060062", "skill": "682518", "talent": "0", "fightSkill": "0", "mineNeed": "110", "time": "72000", "preLimit": "3|3|3", "columnLimit": "5|5|5"}, {"id": 10060081, "level": 6, "rank": 8, "limit": 1, "prePlot": "10060071", "skill": "682006", "talent": "0", "fightSkill": "0", "mineNeed": "110", "time": "72000", "preLimit": "3|3|3", "columnLimit": "5|5|5"}, {"id": 10060091, "level": 6, "rank": 9, "limit": 1, "prePlot": "10060081", "skill": "682533", "talent": "0", "fightSkill": "0", "mineNeed": "110", "time": "72000", "preLimit": "1|1|1", "columnLimit": "5|5|5"}, {"id": 10070011, "level": 7, "rank": 1, "limit": 3, "prePlot": "10060091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "120", "time": "78000", "preLimit": "1|1|1", "columnLimit": "5|5|5"}, {"id": 10070021, "level": 7, "rank": 2, "limit": 3, "prePlot": "10070011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "120", "time": "78000", "preLimit": "3|3|3", "columnLimit": "5|5|5"}, {"id": 10070031, "level": 7, "rank": 3, "limit": 3, "prePlot": "10070021", "skill": "682504", "talent": "0", "fightSkill": "0", "mineNeed": "120", "time": "78000", "preLimit": "3|3|3", "columnLimit": "5|5|5"}, {"id": 10070032, "level": 7, "rank": 3, "limit": 3, "prePlot": "10070021", "skill": "682503", "talent": "0", "fightSkill": "0", "mineNeed": "120", "time": "78000", "preLimit": "3|3|3", "columnLimit": "5|5|5"}, {"id": 10070041, "level": 7, "rank": 4, "limit": 3, "prePlot": "10070031|10070032", "skill": "682515", "talent": "0", "fightSkill": "0", "mineNeed": "120", "time": "78000", "preLimit": "3|3|3", "columnLimit": "5|5|5"}, {"id": 10070051, "level": 7, "rank": 5, "limit": 1, "prePlot": "10070041", "skill": "0", "talent": "680016", "fightSkill": "0", "mineNeed": "120", "time": "78000", "preLimit": "3|3|3", "columnLimit": "5|5|5"}, {"id": 10070061, "level": 7, "rank": 6, "limit": 3, "prePlot": "10070051", "skill": "682505", "talent": "0", "fightSkill": "0", "mineNeed": "120", "time": "78000", "preLimit": "1|1|1", "columnLimit": "5|5|5"}, {"id": 10070062, "level": 7, "rank": 6, "limit": 3, "prePlot": "10070051", "skill": "682507", "talent": "0", "fightSkill": "0", "mineNeed": "120", "time": "78000", "preLimit": "1|1|1", "columnLimit": "5|5|5"}, {"id": 10070071, "level": 7, "rank": 7, "limit": 3, "prePlot": "10070061|10070062", "skill": "682517", "talent": "0", "fightSkill": "0", "mineNeed": "120", "time": "78000", "preLimit": "3|3|3", "columnLimit": "5|5|5"}, {"id": 10070081, "level": 7, "rank": 8, "limit": 1, "prePlot": "10070071", "skill": "682007", "talent": "0", "fightSkill": "0", "mineNeed": "120", "time": "78000", "preLimit": "3|3|3", "columnLimit": "5|5|5"}, {"id": 10070091, "level": 7, "rank": 9, "limit": 1, "prePlot": "10070081", "skill": "682537", "talent": "0", "fightSkill": "0", "mineNeed": "120", "time": "78000", "preLimit": "1|1|1", "columnLimit": "5|5|5"}, {"id": 10080011, "level": 8, "rank": 1, "limit": 3, "prePlot": "10070091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "130", "time": "84000", "preLimit": "1|1|1", "columnLimit": "5|5|5"}, {"id": 10080021, "level": 8, "rank": 2, "limit": 3, "prePlot": "10080011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "130", "time": "84000", "preLimit": "3|3|3", "columnLimit": "5|5|5"}, {"id": 10080031, "level": 8, "rank": 3, "limit": 3, "prePlot": "10080021", "skill": "682506", "talent": "0", "fightSkill": "0", "mineNeed": "130", "time": "84000", "preLimit": "3|3|3", "columnLimit": "5|5|5"}, {"id": 10080032, "level": 8, "rank": 3, "limit": 3, "prePlot": "10080021", "skill": "682508", "talent": "0", "fightSkill": "0", "mineNeed": "130", "time": "84000", "preLimit": "3|3|3", "columnLimit": "5|5|5"}, {"id": 10080041, "level": 8, "rank": 4, "limit": 3, "prePlot": "10080031|10080032", "skill": "682512", "talent": "0", "fightSkill": "0", "mineNeed": "130", "time": "84000", "preLimit": "3|3|3", "columnLimit": "5|5|5"}, {"id": 10080051, "level": 8, "rank": 5, "limit": 1, "prePlot": "10080041", "skill": "0", "talent": "680005", "fightSkill": "0", "mineNeed": "130", "time": "84000", "preLimit": "3|3|3", "columnLimit": "5|5|5"}, {"id": 10080061, "level": 8, "rank": 6, "limit": 3, "prePlot": "10080051", "skill": "682504", "talent": "0", "fightSkill": "0", "mineNeed": "130", "time": "84000", "preLimit": "1|1|1", "columnLimit": "5|5|5"}, {"id": 10080062, "level": 8, "rank": 6, "limit": 3, "prePlot": "10080051", "skill": "682503", "talent": "0", "fightSkill": "0", "mineNeed": "130", "time": "84000", "preLimit": "1|1|1", "columnLimit": "5|5|5"}, {"id": 10080071, "level": 8, "rank": 7, "limit": 3, "prePlot": "10080061|10080062", "skill": "682518", "talent": "0", "fightSkill": "0", "mineNeed": "130", "time": "84000", "preLimit": "3|3|3", "columnLimit": "5|5|5"}, {"id": 10080081, "level": 8, "rank": 8, "limit": 1, "prePlot": "10080071", "skill": "682008", "talent": "0", "fightSkill": "0", "mineNeed": "130", "time": "84000", "preLimit": "3|3|3", "columnLimit": "5|5|5"}, {"id": 10080091, "level": 8, "rank": 9, "limit": 1, "prePlot": "10080081", "skill": "682534", "talent": "0", "fightSkill": "0", "mineNeed": "130", "time": "84000", "preLimit": "1|1|1", "columnLimit": "5|5|5"}, {"id": 10090011, "level": 9, "rank": 1, "limit": 3, "prePlot": "10080091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "140", "time": "90000", "preLimit": "1|1|1", "columnLimit": "5|5|5"}, {"id": 10090021, "level": 9, "rank": 2, "limit": 3, "prePlot": "10090011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "140", "time": "90000", "preLimit": "3|3|3", "columnLimit": "5|5|5"}, {"id": 10090031, "level": 9, "rank": 3, "limit": 3, "prePlot": "10090021", "skill": "682505", "talent": "0", "fightSkill": "0", "mineNeed": "140", "time": "90000", "preLimit": "3|3|3", "columnLimit": "5|5|5"}, {"id": 10090032, "level": 9, "rank": 3, "limit": 3, "prePlot": "10090021", "skill": "682507", "talent": "0", "fightSkill": "0", "mineNeed": "140", "time": "90000", "preLimit": "3|3|3", "columnLimit": "5|5|5"}, {"id": 10090041, "level": 9, "rank": 4, "limit": 3, "prePlot": "10090031|10090032", "skill": "682513", "talent": "0", "fightSkill": "0", "mineNeed": "140", "time": "90000", "preLimit": "3|3|3", "columnLimit": "5|5|5"}, {"id": 10090051, "level": 9, "rank": 5, "limit": 1, "prePlot": "10090041", "skill": "0", "talent": "680011", "fightSkill": "0", "mineNeed": "140", "time": "90000", "preLimit": "3|3|3", "columnLimit": "5|5|5"}, {"id": 10090061, "level": 9, "rank": 6, "limit": 3, "prePlot": "10090051", "skill": "682506", "talent": "0", "fightSkill": "0", "mineNeed": "140", "time": "90000", "preLimit": "1|1|1", "columnLimit": "5|5|5"}, {"id": 10090062, "level": 9, "rank": 6, "limit": 3, "prePlot": "10090051", "skill": "682508", "talent": "0", "fightSkill": "0", "mineNeed": "140", "time": "90000", "preLimit": "1|1|1", "columnLimit": "5|5|5"}, {"id": 10090071, "level": 9, "rank": 7, "limit": 3, "prePlot": "10090061|10090062", "skill": "682517", "talent": "0", "fightSkill": "0", "mineNeed": "140", "time": "90000", "preLimit": "3|3|3", "columnLimit": "5|5|5"}, {"id": 10090081, "level": 9, "rank": 8, "limit": 1, "prePlot": "10090071", "skill": "682009", "talent": "0", "fightSkill": "0", "mineNeed": "140", "time": "90000", "preLimit": "3|3|3", "columnLimit": "5|5|5"}, {"id": 10090091, "level": 9, "rank": 9, "limit": 1, "prePlot": "10090081", "skill": "0", "talent": "0", "fightSkill": "681003", "mineNeed": "140", "time": "90000", "preLimit": "1|1|1", "columnLimit": "5|5|5"}, {"id": 10100011, "level": 10, "rank": 1, "limit": 3, "prePlot": "10090091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "150", "time": "96000", "preLimit": "1|1|1", "columnLimit": "5|5|5"}, {"id": 10100021, "level": 10, "rank": 2, "limit": 3, "prePlot": "10100011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "150", "time": "96000", "preLimit": "3|3|3", "columnLimit": "5|5|5"}, {"id": 10100031, "level": 10, "rank": 3, "limit": 3, "prePlot": "10100021", "skill": "682504", "talent": "0", "fightSkill": "0", "mineNeed": "150", "time": "96000", "preLimit": "3|3|3", "columnLimit": "5|5|5"}, {"id": 10100032, "level": 10, "rank": 3, "limit": 3, "prePlot": "10100021", "skill": "682503", "talent": "0", "fightSkill": "0", "mineNeed": "150", "time": "96000", "preLimit": "3|3|3", "columnLimit": "5|5|5"}, {"id": 10100041, "level": 10, "rank": 4, "limit": 3, "prePlot": "10100031|10100032", "skill": "682510", "talent": "0", "fightSkill": "0", "mineNeed": "150", "time": "96000", "preLimit": "3|3|3", "columnLimit": "5|5|5"}, {"id": 10100051, "level": 10, "rank": 5, "limit": 1, "prePlot": "10100041", "skill": "0", "talent": "680006", "fightSkill": "0", "mineNeed": "150", "time": "96000", "preLimit": "3|3|3", "columnLimit": "5|5|5"}, {"id": 10100061, "level": 10, "rank": 6, "limit": 3, "prePlot": "10100051", "skill": "682505", "talent": "0", "fightSkill": "0", "mineNeed": "150", "time": "96000", "preLimit": "1|1|1", "columnLimit": "5|5|5"}, {"id": 10100062, "level": 10, "rank": 6, "limit": 3, "prePlot": "10100051", "skill": "682507", "talent": "0", "fightSkill": "0", "mineNeed": "150", "time": "96000", "preLimit": "1|1|1", "columnLimit": "5|5|5"}, {"id": 10100071, "level": 10, "rank": 7, "limit": 3, "prePlot": "10100061|10100062", "skill": "682518", "talent": "0", "fightSkill": "0", "mineNeed": "150", "time": "96000", "preLimit": "3|3|3", "columnLimit": "5|5|5"}, {"id": 10100081, "level": 10, "rank": 8, "limit": 1, "prePlot": "10100071", "skill": "682010", "talent": "0", "fightSkill": "0", "mineNeed": "150", "time": "96000", "preLimit": "3|3|3", "columnLimit": "5|5|5"}, {"id": 10100091, "level": 10, "rank": 9, "limit": 1, "prePlot": "10100081", "skill": "682533", "talent": "0", "fightSkill": "0", "mineNeed": "150", "time": "96000", "preLimit": "1|1|1", "columnLimit": "5|5|5"}, {"id": 10110011, "level": 11, "rank": 1, "limit": 3, "prePlot": "10100091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "155", "time": "99000", "preLimit": "1|1|1", "columnLimit": "10|10|10"}, {"id": 10110021, "level": 11, "rank": 2, "limit": 3, "prePlot": "10110011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "155", "time": "99000", "preLimit": "3|3|3", "columnLimit": "10|10|10"}, {"id": 10110031, "level": 11, "rank": 3, "limit": 3, "prePlot": "10110021", "skill": "682506", "talent": "0", "fightSkill": "0", "mineNeed": "155", "time": "99000", "preLimit": "3|3|3", "columnLimit": "10|10|10"}, {"id": 10110032, "level": 11, "rank": 3, "limit": 3, "prePlot": "10110021", "skill": "682508", "talent": "0", "fightSkill": "0", "mineNeed": "155", "time": "99000", "preLimit": "3|3|3", "columnLimit": "10|10|10"}, {"id": 10110041, "level": 11, "rank": 4, "limit": 3, "prePlot": "10110031|10110032", "skill": "682511", "talent": "0", "fightSkill": "0", "mineNeed": "155", "time": "99000", "preLimit": "3|3|3", "columnLimit": "10|10|10"}, {"id": 10110051, "level": 11, "rank": 5, "limit": 1, "prePlot": "10110041", "skill": "0", "talent": "680007", "fightSkill": "0", "mineNeed": "155", "time": "99000", "preLimit": "3|3|3", "columnLimit": "10|10|10"}, {"id": 10110061, "level": 11, "rank": 6, "limit": 3, "prePlot": "10110051", "skill": "682504", "talent": "0", "fightSkill": "0", "mineNeed": "155", "time": "99000", "preLimit": "1|1|1", "columnLimit": "10|10|10"}, {"id": 10110062, "level": 11, "rank": 6, "limit": 3, "prePlot": "10110051", "skill": "682503", "talent": "0", "fightSkill": "0", "mineNeed": "155", "time": "99000", "preLimit": "1|1|1", "columnLimit": "10|10|10"}, {"id": 10110071, "level": 11, "rank": 7, "limit": 3, "prePlot": "10110061|10110062", "skill": "682517", "talent": "0", "fightSkill": "0", "mineNeed": "155", "time": "99000", "preLimit": "3|3|3", "columnLimit": "10|10|10"}, {"id": 10110081, "level": 11, "rank": 8, "limit": 1, "prePlot": "10110071", "skill": "682001", "talent": "0", "fightSkill": "0", "mineNeed": "155", "time": "99000", "preLimit": "3|3|3", "columnLimit": "10|10|10"}, {"id": 10110091, "level": 11, "rank": 9, "limit": 1, "prePlot": "10110081", "skill": "682536", "talent": "0", "fightSkill": "0", "mineNeed": "155", "time": "99000", "preLimit": "1|1|1", "columnLimit": "10|10|10"}, {"id": 10120011, "level": 12, "rank": 1, "limit": 3, "prePlot": "10110091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "160", "time": "102000", "preLimit": "1|1|1", "columnLimit": "10|10|10"}, {"id": 10120021, "level": 12, "rank": 2, "limit": 3, "prePlot": "10120011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "160", "time": "102000", "preLimit": "3|3|3", "columnLimit": "10|10|10"}, {"id": 10120031, "level": 12, "rank": 3, "limit": 3, "prePlot": "10120021", "skill": "682505", "talent": "0", "fightSkill": "0", "mineNeed": "160", "time": "102000", "preLimit": "3|3|3", "columnLimit": "10|10|10"}, {"id": 10120032, "level": 12, "rank": 3, "limit": 3, "prePlot": "10120021", "skill": "682507", "talent": "0", "fightSkill": "0", "mineNeed": "160", "time": "102000", "preLimit": "3|3|3", "columnLimit": "10|10|10"}, {"id": 10120041, "level": 12, "rank": 4, "limit": 3, "prePlot": "10120031|10120032", "skill": "682514", "talent": "0", "fightSkill": "0", "mineNeed": "160", "time": "102000", "preLimit": "3|3|3", "columnLimit": "10|10|10"}, {"id": 10120051, "level": 12, "rank": 5, "limit": 1, "prePlot": "10120041", "skill": "0", "talent": "680017", "fightSkill": "0", "mineNeed": "160", "time": "102000", "preLimit": "3|3|3", "columnLimit": "10|10|10"}, {"id": 10120061, "level": 12, "rank": 6, "limit": 3, "prePlot": "10120051", "skill": "682506", "talent": "0", "fightSkill": "0", "mineNeed": "160", "time": "102000", "preLimit": "1|1|1", "columnLimit": "10|10|10"}, {"id": 10120062, "level": 12, "rank": 6, "limit": 3, "prePlot": "10120051", "skill": "682508", "talent": "0", "fightSkill": "0", "mineNeed": "160", "time": "102000", "preLimit": "1|1|1", "columnLimit": "10|10|10"}, {"id": 10120071, "level": 12, "rank": 7, "limit": 3, "prePlot": "10120061|10120062", "skill": "682518", "talent": "0", "fightSkill": "0", "mineNeed": "160", "time": "102000", "preLimit": "3|3|3", "columnLimit": "10|10|10"}, {"id": 10120081, "level": 12, "rank": 8, "limit": 1, "prePlot": "10120071", "skill": "682002", "talent": "0", "fightSkill": "0", "mineNeed": "160", "time": "102000", "preLimit": "3|3|3", "columnLimit": "10|10|10"}, {"id": 10120091, "level": 12, "rank": 9, "limit": 1, "prePlot": "10120081", "skill": "682535", "talent": "0", "fightSkill": "0", "mineNeed": "160", "time": "102000", "preLimit": "1|1|1", "columnLimit": "10|10|10"}, {"id": 10130011, "level": 13, "rank": 1, "limit": 3, "prePlot": "10120091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "165", "time": "105000", "preLimit": "1|1|1", "columnLimit": "10|10|10"}, {"id": 10130021, "level": 13, "rank": 2, "limit": 3, "prePlot": "10130011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "165", "time": "105000", "preLimit": "3|3|3", "columnLimit": "10|10|10"}, {"id": 10130031, "level": 13, "rank": 3, "limit": 3, "prePlot": "10130021", "skill": "682504", "talent": "0", "fightSkill": "0", "mineNeed": "165", "time": "105000", "preLimit": "3|3|3", "columnLimit": "10|10|10"}, {"id": 10130032, "level": 13, "rank": 3, "limit": 3, "prePlot": "10130021", "skill": "682503", "talent": "0", "fightSkill": "0", "mineNeed": "165", "time": "105000", "preLimit": "3|3|3", "columnLimit": "10|10|10"}, {"id": 10130041, "level": 13, "rank": 4, "limit": 3, "prePlot": "10130031|10130032", "skill": "682509", "talent": "0", "fightSkill": "0", "mineNeed": "165", "time": "105000", "preLimit": "3|3|3", "columnLimit": "10|10|10"}, {"id": 10130051, "level": 13, "rank": 5, "limit": 1, "prePlot": "10130041", "skill": "0", "talent": "680008", "fightSkill": "0", "mineNeed": "165", "time": "105000", "preLimit": "3|3|3", "columnLimit": "10|10|10"}, {"id": 10130061, "level": 13, "rank": 6, "limit": 3, "prePlot": "10130051", "skill": "682505", "talent": "0", "fightSkill": "0", "mineNeed": "165", "time": "105000", "preLimit": "1|1|1", "columnLimit": "10|10|10"}, {"id": 10130062, "level": 13, "rank": 6, "limit": 3, "prePlot": "10130051", "skill": "682507", "talent": "0", "fightSkill": "0", "mineNeed": "165", "time": "105000", "preLimit": "1|1|1", "columnLimit": "10|10|10"}, {"id": 10130071, "level": 13, "rank": 7, "limit": 3, "prePlot": "10130061|10130062", "skill": "682517", "talent": "0", "fightSkill": "0", "mineNeed": "165", "time": "105000", "preLimit": "3|3|3", "columnLimit": "10|10|10"}, {"id": 10130081, "level": 13, "rank": 8, "limit": 1, "prePlot": "10130071", "skill": "682003", "talent": "0", "fightSkill": "0", "mineNeed": "165", "time": "105000", "preLimit": "3|3|3", "columnLimit": "10|10|10"}, {"id": 10130091, "level": 13, "rank": 9, "limit": 1, "prePlot": "10130081", "skill": "0", "talent": "0", "fightSkill": "681004", "mineNeed": "165", "time": "105000", "preLimit": "1|1|1", "columnLimit": "10|10|10"}, {"id": 10140011, "level": 14, "rank": 1, "limit": 3, "prePlot": "10130091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "170", "time": "108000", "preLimit": "1|1|1", "columnLimit": "10|10|10"}, {"id": 10140021, "level": 14, "rank": 2, "limit": 3, "prePlot": "10140011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "170", "time": "108000", "preLimit": "3|3|3", "columnLimit": "10|10|10"}, {"id": 10140031, "level": 14, "rank": 3, "limit": 3, "prePlot": "10140021", "skill": "682506", "talent": "0", "fightSkill": "0", "mineNeed": "170", "time": "108000", "preLimit": "3|3|3", "columnLimit": "10|10|10"}, {"id": 10140032, "level": 14, "rank": 3, "limit": 3, "prePlot": "10140021", "skill": "682508", "talent": "0", "fightSkill": "0", "mineNeed": "170", "time": "108000", "preLimit": "3|3|3", "columnLimit": "10|10|10"}, {"id": 10140041, "level": 14, "rank": 4, "limit": 3, "prePlot": "10140031|10140032", "skill": "682516", "talent": "0", "fightSkill": "0", "mineNeed": "170", "time": "108000", "preLimit": "3|3|3", "columnLimit": "10|10|10"}, {"id": 10140051, "level": 14, "rank": 5, "limit": 1, "prePlot": "10140041", "skill": "0", "talent": "680009", "fightSkill": "0", "mineNeed": "170", "time": "108000", "preLimit": "3|3|3", "columnLimit": "10|10|10"}, {"id": 10140061, "level": 14, "rank": 6, "limit": 3, "prePlot": "10140051", "skill": "682504", "talent": "0", "fightSkill": "0", "mineNeed": "170", "time": "108000", "preLimit": "1|1|1", "columnLimit": "10|10|10"}, {"id": 10140062, "level": 14, "rank": 6, "limit": 3, "prePlot": "10140051", "skill": "682503", "talent": "0", "fightSkill": "0", "mineNeed": "170", "time": "108000", "preLimit": "1|1|1", "columnLimit": "10|10|10"}, {"id": 10140071, "level": 14, "rank": 7, "limit": 3, "prePlot": "10140061|10140062", "skill": "682518", "talent": "0", "fightSkill": "0", "mineNeed": "170", "time": "108000", "preLimit": "3|3|3", "columnLimit": "10|10|10"}, {"id": 10140081, "level": 14, "rank": 8, "limit": 1, "prePlot": "10140071", "skill": "682004", "talent": "0", "fightSkill": "0", "mineNeed": "170", "time": "108000", "preLimit": "3|3|3", "columnLimit": "10|10|10"}, {"id": 10140091, "level": 14, "rank": 9, "limit": 1, "prePlot": "10140081", "skill": "682533", "talent": "0", "fightSkill": "0", "mineNeed": "170", "time": "108000", "preLimit": "1|1|1", "columnLimit": "10|10|10"}, {"id": 10150011, "level": 15, "rank": 1, "limit": 3, "prePlot": "10140091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "175", "time": "111000", "preLimit": "1|1|1", "columnLimit": "10|10|10"}, {"id": 10150021, "level": 15, "rank": 2, "limit": 3, "prePlot": "10150011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "175", "time": "111000", "preLimit": "3|3|3", "columnLimit": "10|10|10"}, {"id": 10150031, "level": 15, "rank": 3, "limit": 3, "prePlot": "10150021", "skill": "682505", "talent": "0", "fightSkill": "0", "mineNeed": "175", "time": "111000", "preLimit": "3|3|3", "columnLimit": "10|10|10"}, {"id": 10150032, "level": 15, "rank": 3, "limit": 3, "prePlot": "10150021", "skill": "682507", "talent": "0", "fightSkill": "0", "mineNeed": "175", "time": "111000", "preLimit": "3|3|3", "columnLimit": "10|10|10"}, {"id": 10150041, "level": 15, "rank": 4, "limit": 3, "prePlot": "10150031|10150032", "skill": "682515", "talent": "0", "fightSkill": "0", "mineNeed": "175", "time": "111000", "preLimit": "3|3|3", "columnLimit": "10|10|10"}, {"id": 10150051, "level": 15, "rank": 5, "limit": 1, "prePlot": "10150041", "skill": "0", "talent": "680012", "fightSkill": "0", "mineNeed": "175", "time": "111000", "preLimit": "3|3|3", "columnLimit": "10|10|10"}, {"id": 10150061, "level": 15, "rank": 6, "limit": 3, "prePlot": "10150051", "skill": "682506", "talent": "0", "fightSkill": "0", "mineNeed": "175", "time": "111000", "preLimit": "1|1|1", "columnLimit": "10|10|10"}, {"id": 10150062, "level": 15, "rank": 6, "limit": 3, "prePlot": "10150051", "skill": "682508", "talent": "0", "fightSkill": "0", "mineNeed": "175", "time": "111000", "preLimit": "1|1|1", "columnLimit": "10|10|10"}, {"id": 10150071, "level": 15, "rank": 7, "limit": 3, "prePlot": "10150061|10150062", "skill": "682517", "talent": "0", "fightSkill": "0", "mineNeed": "175", "time": "111000", "preLimit": "3|3|3", "columnLimit": "10|10|10"}, {"id": 10150081, "level": 15, "rank": 8, "limit": 1, "prePlot": "10150071", "skill": "682005", "talent": "0", "fightSkill": "0", "mineNeed": "175", "time": "111000", "preLimit": "3|3|3", "columnLimit": "10|10|10"}, {"id": 10150091, "level": 15, "rank": 9, "limit": 1, "prePlot": "10150081", "skill": "682537", "talent": "0", "fightSkill": "0", "mineNeed": "175", "time": "111000", "preLimit": "1|1|1", "columnLimit": "10|10|10"}, {"id": 10160011, "level": 16, "rank": 1, "limit": 3, "prePlot": "10150091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "180", "time": "114000", "preLimit": "1|1|1", "columnLimit": "15|15|15"}, {"id": 10160021, "level": 16, "rank": 2, "limit": 3, "prePlot": "10160011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "180", "time": "114000", "preLimit": "3|3|3", "columnLimit": "15|15|15"}, {"id": 10160031, "level": 16, "rank": 3, "limit": 3, "prePlot": "10160021", "skill": "682504", "talent": "0", "fightSkill": "0", "mineNeed": "180", "time": "114000", "preLimit": "3|3|3", "columnLimit": "15|15|15"}, {"id": 10160032, "level": 16, "rank": 3, "limit": 3, "prePlot": "10160021", "skill": "682503", "talent": "0", "fightSkill": "0", "mineNeed": "180", "time": "114000", "preLimit": "3|3|3", "columnLimit": "15|15|15"}, {"id": 10160041, "level": 16, "rank": 4, "limit": 3, "prePlot": "10160031|10160032", "skill": "682512", "talent": "0", "fightSkill": "0", "mineNeed": "180", "time": "114000", "preLimit": "3|3|3", "columnLimit": "15|15|15"}, {"id": 10160051, "level": 16, "rank": 5, "limit": 1, "prePlot": "10160041", "skill": "0", "talent": "680013", "fightSkill": "0", "mineNeed": "180", "time": "114000", "preLimit": "3|3|3", "columnLimit": "15|15|15"}, {"id": 10160061, "level": 16, "rank": 6, "limit": 3, "prePlot": "10160051", "skill": "682505", "talent": "0", "fightSkill": "0", "mineNeed": "180", "time": "114000", "preLimit": "1|1|1", "columnLimit": "15|15|15"}, {"id": 10160062, "level": 16, "rank": 6, "limit": 3, "prePlot": "10160051", "skill": "682507", "talent": "0", "fightSkill": "0", "mineNeed": "180", "time": "114000", "preLimit": "1|1|1", "columnLimit": "15|15|15"}, {"id": 10160071, "level": 16, "rank": 7, "limit": 3, "prePlot": "10160061|10160062", "skill": "682518", "talent": "0", "fightSkill": "0", "mineNeed": "180", "time": "114000", "preLimit": "3|3|3", "columnLimit": "15|15|15"}, {"id": 10160081, "level": 16, "rank": 8, "limit": 1, "prePlot": "10160071", "skill": "682006", "talent": "0", "fightSkill": "0", "mineNeed": "180", "time": "114000", "preLimit": "3|3|3", "columnLimit": "15|15|15"}, {"id": 10160091, "level": 16, "rank": 9, "limit": 1, "prePlot": "10160081", "skill": "682534", "talent": "0", "fightSkill": "0", "mineNeed": "180", "time": "114000", "preLimit": "1|1|1", "columnLimit": "15|15|15"}, {"id": 10170011, "level": 17, "rank": 1, "limit": 3, "prePlot": "10160091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "185", "time": "117000", "preLimit": "1|1|1", "columnLimit": "15|15|15"}, {"id": 10170021, "level": 17, "rank": 2, "limit": 3, "prePlot": "10170011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "185", "time": "117000", "preLimit": "3|3|3", "columnLimit": "15|15|15"}, {"id": 10170031, "level": 17, "rank": 3, "limit": 3, "prePlot": "10170021", "skill": "682506", "talent": "0", "fightSkill": "0", "mineNeed": "185", "time": "117000", "preLimit": "3|3|3", "columnLimit": "15|15|15"}, {"id": 10170032, "level": 17, "rank": 3, "limit": 3, "prePlot": "10170021", "skill": "682508", "talent": "0", "fightSkill": "0", "mineNeed": "185", "time": "117000", "preLimit": "3|3|3", "columnLimit": "15|15|15"}, {"id": 10170041, "level": 17, "rank": 4, "limit": 3, "prePlot": "10170031|10170032", "skill": "682513", "talent": "0", "fightSkill": "0", "mineNeed": "185", "time": "117000", "preLimit": "3|3|3", "columnLimit": "15|15|15"}, {"id": 10170051, "level": 17, "rank": 5, "limit": 1, "prePlot": "10170041", "skill": "0", "talent": "680014", "fightSkill": "0", "mineNeed": "185", "time": "117000", "preLimit": "3|3|3", "columnLimit": "15|15|15"}, {"id": 10170061, "level": 17, "rank": 6, "limit": 3, "prePlot": "10170051", "skill": "682504", "talent": "0", "fightSkill": "0", "mineNeed": "185", "time": "117000", "preLimit": "1|1|1", "columnLimit": "15|15|15"}, {"id": 10170062, "level": 17, "rank": 6, "limit": 3, "prePlot": "10170051", "skill": "682503", "talent": "0", "fightSkill": "0", "mineNeed": "185", "time": "117000", "preLimit": "1|1|1", "columnLimit": "15|15|15"}, {"id": 10170071, "level": 17, "rank": 7, "limit": 3, "prePlot": "10170061|10170062", "skill": "682517", "talent": "0", "fightSkill": "0", "mineNeed": "185", "time": "117000", "preLimit": "3|3|3", "columnLimit": "15|15|15"}, {"id": 10170081, "level": 17, "rank": 8, "limit": 1, "prePlot": "10170071", "skill": "682007", "talent": "0", "fightSkill": "0", "mineNeed": "185", "time": "117000", "preLimit": "3|3|3", "columnLimit": "15|15|15"}, {"id": 10170091, "level": 17, "rank": 9, "limit": 1, "prePlot": "10170081", "skill": "0", "talent": "0", "fightSkill": "681005", "mineNeed": "185", "time": "117000", "preLimit": "1|1|1", "columnLimit": "15|15|15"}, {"id": 10180011, "level": 18, "rank": 1, "limit": 3, "prePlot": "10170091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "190", "time": "120000", "preLimit": "1|1|1", "columnLimit": "15|15|15"}, {"id": 10180021, "level": 18, "rank": 2, "limit": 3, "prePlot": "10180011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "190", "time": "120000", "preLimit": "3|3|3", "columnLimit": "15|15|15"}, {"id": 10180031, "level": 18, "rank": 3, "limit": 3, "prePlot": "10180021", "skill": "682505", "talent": "0", "fightSkill": "0", "mineNeed": "190", "time": "120000", "preLimit": "3|3|3", "columnLimit": "15|15|15"}, {"id": 10180032, "level": 18, "rank": 3, "limit": 3, "prePlot": "10180021", "skill": "682507", "talent": "0", "fightSkill": "0", "mineNeed": "190", "time": "120000", "preLimit": "3|3|3", "columnLimit": "15|15|15"}, {"id": 10180041, "level": 18, "rank": 4, "limit": 3, "prePlot": "10180031|10180032", "skill": "682510", "talent": "0", "fightSkill": "0", "mineNeed": "190", "time": "120000", "preLimit": "3|3|3", "columnLimit": "15|15|15"}, {"id": 10180051, "level": 18, "rank": 5, "limit": 1, "prePlot": "10180041", "skill": "0", "talent": "680010", "fightSkill": "0", "mineNeed": "190", "time": "120000", "preLimit": "3|3|3", "columnLimit": "15|15|15"}, {"id": 10180061, "level": 18, "rank": 6, "limit": 3, "prePlot": "10180051", "skill": "682506", "talent": "0", "fightSkill": "0", "mineNeed": "190", "time": "120000", "preLimit": "1|1|1", "columnLimit": "15|15|15"}, {"id": 10180062, "level": 18, "rank": 6, "limit": 3, "prePlot": "10180051", "skill": "682508", "talent": "0", "fightSkill": "0", "mineNeed": "190", "time": "120000", "preLimit": "1|1|1", "columnLimit": "15|15|15"}, {"id": 10180071, "level": 18, "rank": 7, "limit": 3, "prePlot": "10180061|10180062", "skill": "682518", "talent": "0", "fightSkill": "0", "mineNeed": "190", "time": "120000", "preLimit": "3|3|3", "columnLimit": "15|15|15"}, {"id": 10180081, "level": 18, "rank": 8, "limit": 1, "prePlot": "10180071", "skill": "682008", "talent": "0", "fightSkill": "0", "mineNeed": "190", "time": "120000", "preLimit": "3|3|3", "columnLimit": "15|15|15"}, {"id": 10180091, "level": 18, "rank": 9, "limit": 1, "prePlot": "10180081", "skill": "682533", "talent": "0", "fightSkill": "0", "mineNeed": "190", "time": "120000", "preLimit": "1|1|1", "columnLimit": "15|15|15"}, {"id": 10190011, "level": 19, "rank": 1, "limit": 3, "prePlot": "10180091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "195", "time": "123000", "preLimit": "1|1|1", "columnLimit": "15|15|15"}, {"id": 10190021, "level": 19, "rank": 2, "limit": 3, "prePlot": "10190011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "195", "time": "123000", "preLimit": "3|3|3", "columnLimit": "15|15|15"}, {"id": 10190031, "level": 19, "rank": 3, "limit": 3, "prePlot": "10190021", "skill": "682504", "talent": "0", "fightSkill": "0", "mineNeed": "195", "time": "123000", "preLimit": "3|3|3", "columnLimit": "15|15|15"}, {"id": 10190032, "level": 19, "rank": 3, "limit": 3, "prePlot": "10190021", "skill": "682503", "talent": "0", "fightSkill": "0", "mineNeed": "195", "time": "123000", "preLimit": "3|3|3", "columnLimit": "15|15|15"}, {"id": 10190041, "level": 19, "rank": 4, "limit": 3, "prePlot": "10190031|10190032", "skill": "682511", "talent": "0", "fightSkill": "0", "mineNeed": "195", "time": "123000", "preLimit": "3|3|3", "columnLimit": "15|15|15"}, {"id": 10190051, "level": 19, "rank": 5, "limit": 1, "prePlot": "10190041", "skill": "0", "talent": "680001", "fightSkill": "0", "mineNeed": "195", "time": "123000", "preLimit": "3|3|3", "columnLimit": "15|15|15"}, {"id": 10190061, "level": 19, "rank": 6, "limit": 3, "prePlot": "10190051", "skill": "682505", "talent": "0", "fightSkill": "0", "mineNeed": "195", "time": "123000", "preLimit": "1|1|1", "columnLimit": "15|15|15"}, {"id": 10190062, "level": 19, "rank": 6, "limit": 3, "prePlot": "10190051", "skill": "682507", "talent": "0", "fightSkill": "0", "mineNeed": "195", "time": "123000", "preLimit": "1|1|1", "columnLimit": "15|15|15"}, {"id": 10190071, "level": 19, "rank": 7, "limit": 3, "prePlot": "10190061|10190062", "skill": "682517", "talent": "0", "fightSkill": "0", "mineNeed": "195", "time": "123000", "preLimit": "3|3|3", "columnLimit": "15|15|15"}, {"id": 10190081, "level": 19, "rank": 8, "limit": 1, "prePlot": "10190071", "skill": "682009", "talent": "0", "fightSkill": "0", "mineNeed": "195", "time": "123000", "preLimit": "3|3|3", "columnLimit": "15|15|15"}, {"id": 10190091, "level": 19, "rank": 9, "limit": 1, "prePlot": "10190081", "skill": "682536", "talent": "0", "fightSkill": "0", "mineNeed": "195", "time": "123000", "preLimit": "1|1|1", "columnLimit": "15|15|15"}, {"id": 10200011, "level": 20, "rank": 1, "limit": 3, "prePlot": "10190091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "200", "time": "126000", "preLimit": "1|1|1", "columnLimit": "15|15|15"}, {"id": 10200021, "level": 20, "rank": 2, "limit": 3, "prePlot": "10200011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "200", "time": "126000", "preLimit": "3|3|3", "columnLimit": "15|15|15"}, {"id": 10200031, "level": 20, "rank": 3, "limit": 3, "prePlot": "10200021", "skill": "682506", "talent": "0", "fightSkill": "0", "mineNeed": "200", "time": "126000", "preLimit": "3|3|3", "columnLimit": "15|15|15"}, {"id": 10200032, "level": 20, "rank": 3, "limit": 3, "prePlot": "10200021", "skill": "682508", "talent": "0", "fightSkill": "0", "mineNeed": "200", "time": "126000", "preLimit": "3|3|3", "columnLimit": "15|15|15"}, {"id": 10200041, "level": 20, "rank": 4, "limit": 3, "prePlot": "10200031|10200032", "skill": "682514", "talent": "0", "fightSkill": "0", "mineNeed": "200", "time": "126000", "preLimit": "3|3|3", "columnLimit": "15|15|15"}, {"id": 10200051, "level": 20, "rank": 5, "limit": 1, "prePlot": "10200041", "skill": "0", "talent": "680002", "fightSkill": "0", "mineNeed": "200", "time": "126000", "preLimit": "3|3|3", "columnLimit": "15|15|15"}, {"id": 10200061, "level": 20, "rank": 6, "limit": 3, "prePlot": "10200051", "skill": "682504", "talent": "0", "fightSkill": "0", "mineNeed": "200", "time": "126000", "preLimit": "1|1|1", "columnLimit": "15|15|15"}, {"id": 10200062, "level": 20, "rank": 6, "limit": 3, "prePlot": "10200051", "skill": "682503", "talent": "0", "fightSkill": "0", "mineNeed": "200", "time": "126000", "preLimit": "1|1|1", "columnLimit": "15|15|15"}, {"id": 10200071, "level": 20, "rank": 7, "limit": 3, "prePlot": "10200061|10200062", "skill": "682518", "talent": "0", "fightSkill": "0", "mineNeed": "200", "time": "126000", "preLimit": "3|3|3", "columnLimit": "15|15|15"}, {"id": 10200081, "level": 20, "rank": 8, "limit": 1, "prePlot": "10200071", "skill": "682010", "talent": "0", "fightSkill": "0", "mineNeed": "200", "time": "126000", "preLimit": "3|3|3", "columnLimit": "15|15|15"}, {"id": 10200091, "level": 20, "rank": 9, "limit": 1, "prePlot": "10200081", "skill": "682535", "talent": "0", "fightSkill": "0", "mineNeed": "200", "time": "126000", "preLimit": "1|1|1", "columnLimit": "15|15|15"}, {"id": 10210011, "level": 21, "rank": 1, "limit": 3, "prePlot": "10200091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "203", "time": "127800", "preLimit": "1|1|1", "columnLimit": "20|20|20"}, {"id": 10210021, "level": 21, "rank": 2, "limit": 3, "prePlot": "10210011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "203", "time": "127800", "preLimit": "3|3|3", "columnLimit": "20|20|20"}, {"id": 10210031, "level": 21, "rank": 3, "limit": 3, "prePlot": "10210021", "skill": "682505", "talent": "0", "fightSkill": "0", "mineNeed": "203", "time": "127800", "preLimit": "3|3|3", "columnLimit": "20|20|20"}, {"id": 10210032, "level": 21, "rank": 3, "limit": 3, "prePlot": "10210021", "skill": "682507", "talent": "0", "fightSkill": "0", "mineNeed": "203", "time": "127800", "preLimit": "3|3|3", "columnLimit": "20|20|20"}, {"id": 10210041, "level": 21, "rank": 4, "limit": 3, "prePlot": "10210031|10210032", "skill": "682509", "talent": "0", "fightSkill": "0", "mineNeed": "203", "time": "127800", "preLimit": "3|3|3", "columnLimit": "20|20|20"}, {"id": 10210051, "level": 21, "rank": 5, "limit": 1, "prePlot": "10210041", "skill": "0", "talent": "680015", "fightSkill": "0", "mineNeed": "203", "time": "127800", "preLimit": "3|3|3", "columnLimit": "20|20|20"}, {"id": 10210061, "level": 21, "rank": 6, "limit": 3, "prePlot": "10210051", "skill": "682506", "talent": "0", "fightSkill": "0", "mineNeed": "203", "time": "127800", "preLimit": "1|1|1", "columnLimit": "20|20|20"}, {"id": 10210062, "level": 21, "rank": 6, "limit": 3, "prePlot": "10210051", "skill": "682508", "talent": "0", "fightSkill": "0", "mineNeed": "203", "time": "127800", "preLimit": "1|1|1", "columnLimit": "20|20|20"}, {"id": 10210071, "level": 21, "rank": 7, "limit": 3, "prePlot": "10210061|10210062", "skill": "682517", "talent": "0", "fightSkill": "0", "mineNeed": "203", "time": "127800", "preLimit": "3|3|3", "columnLimit": "20|20|20"}, {"id": 10210081, "level": 21, "rank": 8, "limit": 1, "prePlot": "10210071", "skill": "682001", "talent": "0", "fightSkill": "0", "mineNeed": "203", "time": "127800", "preLimit": "3|3|3", "columnLimit": "20|20|20"}, {"id": 10210091, "level": 21, "rank": 9, "limit": 1, "prePlot": "10210081", "skill": "0", "talent": "0", "fightSkill": "681001", "mineNeed": "203", "time": "127800", "preLimit": "1|1|1", "columnLimit": "20|20|20"}, {"id": 10220011, "level": 22, "rank": 1, "limit": 3, "prePlot": "10210091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "206", "time": "129600", "preLimit": "1|1|1", "columnLimit": "20|20|20"}, {"id": 10220021, "level": 22, "rank": 2, "limit": 3, "prePlot": "10220011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "206", "time": "129600", "preLimit": "3|3|3", "columnLimit": "20|20|20"}, {"id": 10220031, "level": 22, "rank": 3, "limit": 3, "prePlot": "10220021", "skill": "682504", "talent": "0", "fightSkill": "0", "mineNeed": "206", "time": "129600", "preLimit": "3|3|3", "columnLimit": "20|20|20"}, {"id": 10220032, "level": 22, "rank": 3, "limit": 3, "prePlot": "10220021", "skill": "682503", "talent": "0", "fightSkill": "0", "mineNeed": "206", "time": "129600", "preLimit": "3|3|3", "columnLimit": "20|20|20"}, {"id": 10220041, "level": 22, "rank": 4, "limit": 3, "prePlot": "10220031|10220032", "skill": "682516", "talent": "0", "fightSkill": "0", "mineNeed": "206", "time": "129600", "preLimit": "3|3|3", "columnLimit": "20|20|20"}, {"id": 10220051, "level": 22, "rank": 5, "limit": 1, "prePlot": "10220041", "skill": "0", "talent": "680003", "fightSkill": "0", "mineNeed": "206", "time": "129600", "preLimit": "3|3|3", "columnLimit": "20|20|20"}, {"id": 10220061, "level": 22, "rank": 6, "limit": 3, "prePlot": "10220051", "skill": "682505", "talent": "0", "fightSkill": "0", "mineNeed": "206", "time": "129600", "preLimit": "1|1|1", "columnLimit": "20|20|20"}, {"id": 10220062, "level": 22, "rank": 6, "limit": 3, "prePlot": "10220051", "skill": "682507", "talent": "0", "fightSkill": "0", "mineNeed": "206", "time": "129600", "preLimit": "1|1|1", "columnLimit": "20|20|20"}, {"id": 10220071, "level": 22, "rank": 7, "limit": 3, "prePlot": "10220061|10220062", "skill": "682518", "talent": "0", "fightSkill": "0", "mineNeed": "206", "time": "129600", "preLimit": "3|3|3", "columnLimit": "20|20|20"}, {"id": 10220081, "level": 22, "rank": 8, "limit": 1, "prePlot": "10220071", "skill": "682002", "talent": "0", "fightSkill": "0", "mineNeed": "206", "time": "129600", "preLimit": "3|3|3", "columnLimit": "20|20|20"}, {"id": 10220091, "level": 22, "rank": 9, "limit": 1, "prePlot": "10220081", "skill": "682533", "talent": "0", "fightSkill": "0", "mineNeed": "206", "time": "129600", "preLimit": "1|1|1", "columnLimit": "20|20|20"}, {"id": 10230011, "level": 23, "rank": 1, "limit": 3, "prePlot": "10220091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "209", "time": "131400", "preLimit": "1|1|1", "columnLimit": "20|20|20"}, {"id": 10230021, "level": 23, "rank": 2, "limit": 3, "prePlot": "10230011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "209", "time": "131400", "preLimit": "3|3|3", "columnLimit": "20|20|20"}, {"id": 10230031, "level": 23, "rank": 3, "limit": 3, "prePlot": "10230021", "skill": "682506", "talent": "0", "fightSkill": "0", "mineNeed": "209", "time": "131400", "preLimit": "3|3|3", "columnLimit": "20|20|20"}, {"id": 10230032, "level": 23, "rank": 3, "limit": 3, "prePlot": "10230021", "skill": "682508", "talent": "0", "fightSkill": "0", "mineNeed": "209", "time": "131400", "preLimit": "3|3|3", "columnLimit": "20|20|20"}, {"id": 10230041, "level": 23, "rank": 4, "limit": 3, "prePlot": "10230031|10230032", "skill": "682515", "talent": "0", "fightSkill": "0", "mineNeed": "209", "time": "131400", "preLimit": "3|3|3", "columnLimit": "20|20|20"}, {"id": 10230051, "level": 23, "rank": 5, "limit": 1, "prePlot": "10230041", "skill": "0", "talent": "680010", "fightSkill": "0", "mineNeed": "209", "time": "131400", "preLimit": "3|3|3", "columnLimit": "20|20|20"}, {"id": 10230061, "level": 23, "rank": 6, "limit": 3, "prePlot": "10230051", "skill": "682504", "talent": "0", "fightSkill": "0", "mineNeed": "209", "time": "131400", "preLimit": "1|1|1", "columnLimit": "20|20|20"}, {"id": 10230062, "level": 23, "rank": 6, "limit": 3, "prePlot": "10230051", "skill": "682503", "talent": "0", "fightSkill": "0", "mineNeed": "209", "time": "131400", "preLimit": "1|1|1", "columnLimit": "20|20|20"}, {"id": 10230071, "level": 23, "rank": 7, "limit": 3, "prePlot": "10230061|10230062", "skill": "682517", "talent": "0", "fightSkill": "0", "mineNeed": "209", "time": "131400", "preLimit": "3|3|3", "columnLimit": "20|20|20"}, {"id": 10230081, "level": 23, "rank": 8, "limit": 1, "prePlot": "10230071", "skill": "682003", "talent": "0", "fightSkill": "0", "mineNeed": "209", "time": "131400", "preLimit": "3|3|3", "columnLimit": "20|20|20"}, {"id": 10230091, "level": 23, "rank": 9, "limit": 1, "prePlot": "10230081", "skill": "682537", "talent": "0", "fightSkill": "0", "mineNeed": "209", "time": "131400", "preLimit": "1|1|1", "columnLimit": "20|20|20"}, {"id": 10240011, "level": 24, "rank": 1, "limit": 3, "prePlot": "10230091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "212", "time": "133200", "preLimit": "1|1|1", "columnLimit": "20|20|20"}, {"id": 10240021, "level": 24, "rank": 2, "limit": 3, "prePlot": "10240011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "212", "time": "133200", "preLimit": "3|3|3", "columnLimit": "20|20|20"}, {"id": 10240031, "level": 24, "rank": 3, "limit": 3, "prePlot": "10240021", "skill": "682505", "talent": "0", "fightSkill": "0", "mineNeed": "212", "time": "133200", "preLimit": "3|3|3", "columnLimit": "20|20|20"}, {"id": 10240032, "level": 24, "rank": 3, "limit": 3, "prePlot": "10240021", "skill": "682507", "talent": "0", "fightSkill": "0", "mineNeed": "212", "time": "133200", "preLimit": "3|3|3", "columnLimit": "20|20|20"}, {"id": 10240041, "level": 24, "rank": 4, "limit": 3, "prePlot": "10240031|10240032", "skill": "682512", "talent": "0", "fightSkill": "0", "mineNeed": "212", "time": "133200", "preLimit": "3|3|3", "columnLimit": "20|20|20"}, {"id": 10240051, "level": 24, "rank": 5, "limit": 1, "prePlot": "10240041", "skill": "0", "talent": "680004", "fightSkill": "0", "mineNeed": "212", "time": "133200", "preLimit": "3|3|3", "columnLimit": "20|20|20"}, {"id": 10240061, "level": 24, "rank": 6, "limit": 3, "prePlot": "10240051", "skill": "682506", "talent": "0", "fightSkill": "0", "mineNeed": "212", "time": "133200", "preLimit": "1|1|1", "columnLimit": "20|20|20"}, {"id": 10240062, "level": 24, "rank": 6, "limit": 3, "prePlot": "10240051", "skill": "682508", "talent": "0", "fightSkill": "0", "mineNeed": "212", "time": "133200", "preLimit": "1|1|1", "columnLimit": "20|20|20"}, {"id": 10240071, "level": 24, "rank": 7, "limit": 3, "prePlot": "10240061|10240062", "skill": "682518", "talent": "0", "fightSkill": "0", "mineNeed": "212", "time": "133200", "preLimit": "3|3|3", "columnLimit": "20|20|20"}, {"id": 10240081, "level": 24, "rank": 8, "limit": 1, "prePlot": "10240071", "skill": "682004", "talent": "0", "fightSkill": "0", "mineNeed": "212", "time": "133200", "preLimit": "3|3|3", "columnLimit": "20|20|20"}, {"id": 10240091, "level": 24, "rank": 9, "limit": 1, "prePlot": "10240081", "skill": "682535", "talent": "0", "fightSkill": "0", "mineNeed": "212", "time": "133200", "preLimit": "1|1|1", "columnLimit": "20|20|20"}, {"id": 10250011, "level": 25, "rank": 1, "limit": 3, "prePlot": "10240091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "215", "time": "135000", "preLimit": "1|1|1", "columnLimit": "20|20|20"}, {"id": 10250021, "level": 25, "rank": 2, "limit": 3, "prePlot": "10250011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "215", "time": "135000", "preLimit": "3|3|3", "columnLimit": "20|20|20"}, {"id": 10250031, "level": 25, "rank": 3, "limit": 3, "prePlot": "10250021", "skill": "682504", "talent": "0", "fightSkill": "0", "mineNeed": "215", "time": "135000", "preLimit": "3|3|3", "columnLimit": "20|20|20"}, {"id": 10250032, "level": 25, "rank": 3, "limit": 3, "prePlot": "10250021", "skill": "682503", "talent": "0", "fightSkill": "0", "mineNeed": "215", "time": "135000", "preLimit": "3|3|3", "columnLimit": "20|20|20"}, {"id": 10250041, "level": 25, "rank": 4, "limit": 3, "prePlot": "10250031|10250032", "skill": "682513", "talent": "0", "fightSkill": "0", "mineNeed": "215", "time": "135000", "preLimit": "3|3|3", "columnLimit": "20|20|20"}, {"id": 10250051, "level": 25, "rank": 5, "limit": 1, "prePlot": "10250041", "skill": "0", "talent": "680016", "fightSkill": "0", "mineNeed": "215", "time": "135000", "preLimit": "3|3|3", "columnLimit": "20|20|20"}, {"id": 10250061, "level": 25, "rank": 6, "limit": 3, "prePlot": "10250051", "skill": "682505", "talent": "0", "fightSkill": "0", "mineNeed": "215", "time": "135000", "preLimit": "1|1|1", "columnLimit": "20|20|20"}, {"id": 10250062, "level": 25, "rank": 6, "limit": 3, "prePlot": "10250051", "skill": "682507", "talent": "0", "fightSkill": "0", "mineNeed": "215", "time": "135000", "preLimit": "1|1|1", "columnLimit": "20|20|20"}, {"id": 10250071, "level": 25, "rank": 7, "limit": 3, "prePlot": "10250061|10250062", "skill": "682517", "talent": "0", "fightSkill": "0", "mineNeed": "215", "time": "135000", "preLimit": "3|3|3", "columnLimit": "20|20|20"}, {"id": 10250081, "level": 25, "rank": 8, "limit": 1, "prePlot": "10250071", "skill": "682005", "talent": "0", "fightSkill": "0", "mineNeed": "215", "time": "135000", "preLimit": "3|3|3", "columnLimit": "20|20|20"}, {"id": 10250091, "level": 25, "rank": 9, "limit": 1, "prePlot": "10250081", "skill": "0", "talent": "0", "fightSkill": "681002", "mineNeed": "215", "time": "135000", "preLimit": "1|1|1", "columnLimit": "20|20|20"}, {"id": 10260011, "level": 26, "rank": 1, "limit": 3, "prePlot": "10250091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "218", "time": "136800", "preLimit": "1|1|1", "columnLimit": "25|25|25"}, {"id": 10260021, "level": 26, "rank": 2, "limit": 3, "prePlot": "10260011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "218", "time": "136800", "preLimit": "3|3|3", "columnLimit": "25|25|25"}, {"id": 10260031, "level": 26, "rank": 3, "limit": 3, "prePlot": "10260021", "skill": "682506", "talent": "0", "fightSkill": "0", "mineNeed": "218", "time": "136800", "preLimit": "3|3|3", "columnLimit": "25|25|25"}, {"id": 10260032, "level": 26, "rank": 3, "limit": 3, "prePlot": "10260021", "skill": "682508", "talent": "0", "fightSkill": "0", "mineNeed": "218", "time": "136800", "preLimit": "3|3|3", "columnLimit": "25|25|25"}, {"id": 10260041, "level": 26, "rank": 4, "limit": 3, "prePlot": "10260031|10260032", "skill": "682510", "talent": "0", "fightSkill": "0", "mineNeed": "218", "time": "136800", "preLimit": "3|3|3", "columnLimit": "25|25|25"}, {"id": 10260051, "level": 26, "rank": 5, "limit": 1, "prePlot": "10260041", "skill": "0", "talent": "680005", "fightSkill": "0", "mineNeed": "218", "time": "136800", "preLimit": "3|3|3", "columnLimit": "25|25|25"}, {"id": 10260061, "level": 26, "rank": 6, "limit": 3, "prePlot": "10260051", "skill": "682504", "talent": "0", "fightSkill": "0", "mineNeed": "218", "time": "136800", "preLimit": "1|1|1", "columnLimit": "25|25|25"}, {"id": 10260062, "level": 26, "rank": 6, "limit": 3, "prePlot": "10260051", "skill": "682503", "talent": "0", "fightSkill": "0", "mineNeed": "218", "time": "136800", "preLimit": "1|1|1", "columnLimit": "25|25|25"}, {"id": 10260071, "level": 26, "rank": 7, "limit": 3, "prePlot": "10260061|10260062", "skill": "682518", "talent": "0", "fightSkill": "0", "mineNeed": "218", "time": "136800", "preLimit": "3|3|3", "columnLimit": "25|25|25"}, {"id": 10260081, "level": 26, "rank": 8, "limit": 1, "prePlot": "10260071", "skill": "682006", "talent": "0", "fightSkill": "0", "mineNeed": "218", "time": "136800", "preLimit": "3|3|3", "columnLimit": "25|25|25"}, {"id": 10260091, "level": 26, "rank": 9, "limit": 1, "prePlot": "10260081", "skill": "682533", "talent": "0", "fightSkill": "0", "mineNeed": "218", "time": "136800", "preLimit": "1|1|1", "columnLimit": "25|25|25"}, {"id": 10270011, "level": 27, "rank": 1, "limit": 3, "prePlot": "10260091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "221", "time": "138600", "preLimit": "1|1|1", "columnLimit": "25|25|25"}, {"id": 10270021, "level": 27, "rank": 2, "limit": 3, "prePlot": "10270011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "221", "time": "138600", "preLimit": "3|3|3", "columnLimit": "25|25|25"}, {"id": 10270031, "level": 27, "rank": 3, "limit": 3, "prePlot": "10270021", "skill": "682505", "talent": "0", "fightSkill": "0", "mineNeed": "221", "time": "138600", "preLimit": "3|3|3", "columnLimit": "25|25|25"}, {"id": 10270032, "level": 27, "rank": 3, "limit": 3, "prePlot": "10270021", "skill": "682507", "talent": "0", "fightSkill": "0", "mineNeed": "221", "time": "138600", "preLimit": "3|3|3", "columnLimit": "25|25|25"}, {"id": 10270041, "level": 27, "rank": 4, "limit": 3, "prePlot": "10270031|10270032", "skill": "682511", "talent": "0", "fightSkill": "0", "mineNeed": "221", "time": "138600", "preLimit": "3|3|3", "columnLimit": "25|25|25"}, {"id": 10270051, "level": 27, "rank": 5, "limit": 1, "prePlot": "10270041", "skill": "0", "talent": "680011", "fightSkill": "0", "mineNeed": "221", "time": "138600", "preLimit": "3|3|3", "columnLimit": "25|25|25"}, {"id": 10270061, "level": 27, "rank": 6, "limit": 3, "prePlot": "10270051", "skill": "682506", "talent": "0", "fightSkill": "0", "mineNeed": "221", "time": "138600", "preLimit": "1|1|1", "columnLimit": "25|25|25"}, {"id": 10270062, "level": 27, "rank": 6, "limit": 3, "prePlot": "10270051", "skill": "682508", "talent": "0", "fightSkill": "0", "mineNeed": "221", "time": "138600", "preLimit": "1|1|1", "columnLimit": "25|25|25"}, {"id": 10270071, "level": 27, "rank": 7, "limit": 3, "prePlot": "10270061|10270062", "skill": "682517", "talent": "0", "fightSkill": "0", "mineNeed": "221", "time": "138600", "preLimit": "3|3|3", "columnLimit": "25|25|25"}, {"id": 10270081, "level": 27, "rank": 8, "limit": 1, "prePlot": "10270071", "skill": "682007", "talent": "0", "fightSkill": "0", "mineNeed": "221", "time": "138600", "preLimit": "3|3|3", "columnLimit": "25|25|25"}, {"id": 10270091, "level": 27, "rank": 9, "limit": 1, "prePlot": "10270081", "skill": "682536", "talent": "0", "fightSkill": "0", "mineNeed": "221", "time": "138600", "preLimit": "1|1|1", "columnLimit": "25|25|25"}, {"id": 10280011, "level": 28, "rank": 1, "limit": 3, "prePlot": "10270091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "224", "time": "140400", "preLimit": "1|1|1", "columnLimit": "25|25|25"}, {"id": 10280021, "level": 28, "rank": 2, "limit": 3, "prePlot": "10280011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "224", "time": "140400", "preLimit": "3|3|3", "columnLimit": "25|25|25"}, {"id": 10280031, "level": 28, "rank": 3, "limit": 3, "prePlot": "10280021", "skill": "682504", "talent": "0", "fightSkill": "0", "mineNeed": "224", "time": "140400", "preLimit": "3|3|3", "columnLimit": "25|25|25"}, {"id": 10280032, "level": 28, "rank": 3, "limit": 3, "prePlot": "10280021", "skill": "682503", "talent": "0", "fightSkill": "0", "mineNeed": "224", "time": "140400", "preLimit": "3|3|3", "columnLimit": "25|25|25"}, {"id": 10280041, "level": 28, "rank": 4, "limit": 3, "prePlot": "10280031|10280032", "skill": "682514", "talent": "0", "fightSkill": "0", "mineNeed": "224", "time": "140400", "preLimit": "3|3|3", "columnLimit": "25|25|25"}, {"id": 10280051, "level": 28, "rank": 5, "limit": 1, "prePlot": "10280041", "skill": "0", "talent": "680006", "fightSkill": "0", "mineNeed": "224", "time": "140400", "preLimit": "3|3|3", "columnLimit": "25|25|25"}, {"id": 10280061, "level": 28, "rank": 6, "limit": 3, "prePlot": "10280051", "skill": "682505", "talent": "0", "fightSkill": "0", "mineNeed": "224", "time": "140400", "preLimit": "1|1|1", "columnLimit": "25|25|25"}, {"id": 10280062, "level": 28, "rank": 6, "limit": 3, "prePlot": "10280051", "skill": "682507", "talent": "0", "fightSkill": "0", "mineNeed": "224", "time": "140400", "preLimit": "1|1|1", "columnLimit": "25|25|25"}, {"id": 10280071, "level": 28, "rank": 7, "limit": 3, "prePlot": "10280061|10280062", "skill": "682518", "talent": "0", "fightSkill": "0", "mineNeed": "224", "time": "140400", "preLimit": "3|3|3", "columnLimit": "25|25|25"}, {"id": 10280081, "level": 28, "rank": 8, "limit": 1, "prePlot": "10280071", "skill": "682008", "talent": "0", "fightSkill": "0", "mineNeed": "224", "time": "140400", "preLimit": "3|3|3", "columnLimit": "25|25|25"}, {"id": 10280091, "level": 28, "rank": 9, "limit": 1, "prePlot": "10280081", "skill": "682534", "talent": "0", "fightSkill": "0", "mineNeed": "224", "time": "140400", "preLimit": "1|1|1", "columnLimit": "25|25|25"}, {"id": 10290011, "level": 29, "rank": 1, "limit": 3, "prePlot": "10280091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "227", "time": "142200", "preLimit": "1|1|1", "columnLimit": "25|25|25"}, {"id": 10290021, "level": 29, "rank": 2, "limit": 3, "prePlot": "10290011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "227", "time": "142200", "preLimit": "3|3|3", "columnLimit": "25|25|25"}, {"id": 10290031, "level": 29, "rank": 3, "limit": 3, "prePlot": "10290021", "skill": "682506", "talent": "0", "fightSkill": "0", "mineNeed": "227", "time": "142200", "preLimit": "3|3|3", "columnLimit": "25|25|25"}, {"id": 10290032, "level": 29, "rank": 3, "limit": 3, "prePlot": "10290021", "skill": "682508", "talent": "0", "fightSkill": "0", "mineNeed": "227", "time": "142200", "preLimit": "3|3|3", "columnLimit": "25|25|25"}, {"id": 10290041, "level": 29, "rank": 4, "limit": 3, "prePlot": "10290031|10290032", "skill": "682509", "talent": "0", "fightSkill": "0", "mineNeed": "227", "time": "142200", "preLimit": "3|3|3", "columnLimit": "25|25|25"}, {"id": 10290051, "level": 29, "rank": 5, "limit": 1, "prePlot": "10290041", "skill": "0", "talent": "680007", "fightSkill": "0", "mineNeed": "227", "time": "142200", "preLimit": "3|3|3", "columnLimit": "25|25|25"}, {"id": 10290061, "level": 29, "rank": 6, "limit": 3, "prePlot": "10290051", "skill": "682504", "talent": "0", "fightSkill": "0", "mineNeed": "227", "time": "142200", "preLimit": "1|1|1", "columnLimit": "25|25|25"}, {"id": 10290062, "level": 29, "rank": 6, "limit": 3, "prePlot": "10290051", "skill": "682503", "talent": "0", "fightSkill": "0", "mineNeed": "227", "time": "142200", "preLimit": "1|1|1", "columnLimit": "25|25|25"}, {"id": 10290071, "level": 29, "rank": 7, "limit": 3, "prePlot": "10290061|10290062", "skill": "682517", "talent": "0", "fightSkill": "0", "mineNeed": "227", "time": "142200", "preLimit": "3|3|3", "columnLimit": "25|25|25"}, {"id": 10290081, "level": 29, "rank": 8, "limit": 1, "prePlot": "10290071", "skill": "682009", "talent": "0", "fightSkill": "0", "mineNeed": "227", "time": "142200", "preLimit": "3|3|3", "columnLimit": "25|25|25"}, {"id": 10290091, "level": 29, "rank": 9, "limit": 1, "prePlot": "10290081", "skill": "0", "talent": "0", "fightSkill": "681003", "mineNeed": "227", "time": "142200", "preLimit": "1|1|1", "columnLimit": "25|25|25"}, {"id": 10300011, "level": 30, "rank": 1, "limit": 3, "prePlot": "10290091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "230", "time": "144000", "preLimit": "1|1|1", "columnLimit": "25|25|25"}, {"id": 10300021, "level": 30, "rank": 2, "limit": 3, "prePlot": "10300011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "230", "time": "144000", "preLimit": "3|3|3", "columnLimit": "25|25|25"}, {"id": 10300031, "level": 30, "rank": 3, "limit": 3, "prePlot": "10300021", "skill": "682505", "talent": "0", "fightSkill": "0", "mineNeed": "230", "time": "144000", "preLimit": "3|3|3", "columnLimit": "25|25|25"}, {"id": 10300032, "level": 30, "rank": 3, "limit": 3, "prePlot": "10300021", "skill": "682507", "talent": "0", "fightSkill": "0", "mineNeed": "230", "time": "144000", "preLimit": "3|3|3", "columnLimit": "25|25|25"}, {"id": 10300041, "level": 30, "rank": 4, "limit": 3, "prePlot": "10300031|10300032", "skill": "682516", "talent": "0", "fightSkill": "0", "mineNeed": "230", "time": "144000", "preLimit": "3|3|3", "columnLimit": "25|25|25"}, {"id": 10300051, "level": 30, "rank": 5, "limit": 1, "prePlot": "10300041", "skill": "0", "talent": "680008", "fightSkill": "0", "mineNeed": "230", "time": "144000", "preLimit": "3|3|3", "columnLimit": "25|25|25"}, {"id": 10300061, "level": 30, "rank": 6, "limit": 3, "prePlot": "10300051", "skill": "682506", "talent": "0", "fightSkill": "0", "mineNeed": "230", "time": "144000", "preLimit": "1|1|1", "columnLimit": "25|25|25"}, {"id": 10300062, "level": 30, "rank": 6, "limit": 3, "prePlot": "10300051", "skill": "682508", "talent": "0", "fightSkill": "0", "mineNeed": "230", "time": "144000", "preLimit": "1|1|1", "columnLimit": "25|25|25"}, {"id": 10300071, "level": 30, "rank": 7, "limit": 3, "prePlot": "10300061|10300062", "skill": "682518", "talent": "0", "fightSkill": "0", "mineNeed": "230", "time": "144000", "preLimit": "3|3|3", "columnLimit": "25|25|25"}, {"id": 10300081, "level": 30, "rank": 8, "limit": 1, "prePlot": "10300071", "skill": "682010", "talent": "0", "fightSkill": "0", "mineNeed": "230", "time": "144000", "preLimit": "3|3|3", "columnLimit": "25|25|25"}, {"id": 10300091, "level": 30, "rank": 9, "limit": 1, "prePlot": "10300081", "skill": "682533", "talent": "0", "fightSkill": "0", "mineNeed": "230", "time": "144000", "preLimit": "1|1|1", "columnLimit": "25|25|25"}, {"id": 10310011, "level": 31, "rank": 1, "limit": 3, "prePlot": "10300091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "233", "time": "145800", "preLimit": "1|1|1", "columnLimit": "30|30|30"}, {"id": 10310021, "level": 31, "rank": 2, "limit": 3, "prePlot": "10310011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "233", "time": "145800", "preLimit": "3|3|3", "columnLimit": "30|30|30"}, {"id": 10310031, "level": 31, "rank": 3, "limit": 3, "prePlot": "10310021", "skill": "682504", "talent": "0", "fightSkill": "0", "mineNeed": "233", "time": "145800", "preLimit": "3|3|3", "columnLimit": "30|30|30"}, {"id": 10310032, "level": 31, "rank": 3, "limit": 3, "prePlot": "10310021", "skill": "682503", "talent": "0", "fightSkill": "0", "mineNeed": "233", "time": "145800", "preLimit": "3|3|3", "columnLimit": "30|30|30"}, {"id": 10310041, "level": 31, "rank": 4, "limit": 3, "prePlot": "10310031|10310032", "skill": "682515", "talent": "0", "fightSkill": "0", "mineNeed": "233", "time": "145800", "preLimit": "3|3|3", "columnLimit": "30|30|30"}, {"id": 10310051, "level": 31, "rank": 5, "limit": 1, "prePlot": "10310041", "skill": "0", "talent": "680009", "fightSkill": "0", "mineNeed": "233", "time": "145800", "preLimit": "3|3|3", "columnLimit": "30|30|30"}, {"id": 10310061, "level": 31, "rank": 6, "limit": 3, "prePlot": "10310051", "skill": "682505", "talent": "0", "fightSkill": "0", "mineNeed": "233", "time": "145800", "preLimit": "1|1|1", "columnLimit": "30|30|30"}, {"id": 10310062, "level": 31, "rank": 6, "limit": 3, "prePlot": "10310051", "skill": "682507", "talent": "0", "fightSkill": "0", "mineNeed": "233", "time": "145800", "preLimit": "1|1|1", "columnLimit": "30|30|30"}, {"id": 10310071, "level": 31, "rank": 7, "limit": 3, "prePlot": "10310061|10310062", "skill": "682517", "talent": "0", "fightSkill": "0", "mineNeed": "233", "time": "145800", "preLimit": "3|3|3", "columnLimit": "30|30|30"}, {"id": 10310081, "level": 31, "rank": 8, "limit": 1, "prePlot": "10310071", "skill": "682001", "talent": "0", "fightSkill": "0", "mineNeed": "233", "time": "145800", "preLimit": "3|3|3", "columnLimit": "30|30|30"}, {"id": 10310091, "level": 31, "rank": 9, "limit": 1, "prePlot": "10310081", "skill": "682537", "talent": "0", "fightSkill": "0", "mineNeed": "233", "time": "145800", "preLimit": "1|1|1", "columnLimit": "30|30|30"}, {"id": 10320011, "level": 32, "rank": 1, "limit": 3, "prePlot": "10310091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "236", "time": "147600", "preLimit": "1|1|1", "columnLimit": "30|30|30"}, {"id": 10320021, "level": 32, "rank": 2, "limit": 3, "prePlot": "10320011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "236", "time": "147600", "preLimit": "3|3|3", "columnLimit": "30|30|30"}, {"id": 10320031, "level": 32, "rank": 3, "limit": 3, "prePlot": "10320021", "skill": "682506", "talent": "0", "fightSkill": "0", "mineNeed": "236", "time": "147600", "preLimit": "3|3|3", "columnLimit": "30|30|30"}, {"id": 10320032, "level": 32, "rank": 3, "limit": 3, "prePlot": "10320021", "skill": "682508", "talent": "0", "fightSkill": "0", "mineNeed": "236", "time": "147600", "preLimit": "3|3|3", "columnLimit": "30|30|30"}, {"id": 10320041, "level": 32, "rank": 4, "limit": 3, "prePlot": "10320031|10320032", "skill": "682512", "talent": "0", "fightSkill": "0", "mineNeed": "236", "time": "147600", "preLimit": "3|3|3", "columnLimit": "30|30|30"}, {"id": 10320051, "level": 32, "rank": 5, "limit": 1, "prePlot": "10320041", "skill": "0", "talent": "680012", "fightSkill": "0", "mineNeed": "236", "time": "147600", "preLimit": "3|3|3", "columnLimit": "30|30|30"}, {"id": 10320061, "level": 32, "rank": 6, "limit": 3, "prePlot": "10320051", "skill": "682504", "talent": "0", "fightSkill": "0", "mineNeed": "236", "time": "147600", "preLimit": "1|1|1", "columnLimit": "30|30|30"}, {"id": 10320062, "level": 32, "rank": 6, "limit": 3, "prePlot": "10320051", "skill": "682503", "talent": "0", "fightSkill": "0", "mineNeed": "236", "time": "147600", "preLimit": "1|1|1", "columnLimit": "30|30|30"}, {"id": 10320071, "level": 32, "rank": 7, "limit": 3, "prePlot": "10320061|10320062", "skill": "682518", "talent": "0", "fightSkill": "0", "mineNeed": "236", "time": "147600", "preLimit": "3|3|3", "columnLimit": "30|30|30"}, {"id": 10320081, "level": 32, "rank": 8, "limit": 1, "prePlot": "10320071", "skill": "682002", "talent": "0", "fightSkill": "0", "mineNeed": "236", "time": "147600", "preLimit": "3|3|3", "columnLimit": "30|30|30"}, {"id": 10320091, "level": 32, "rank": 9, "limit": 1, "prePlot": "10320081", "skill": "682535", "talent": "0", "fightSkill": "0", "mineNeed": "236", "time": "147600", "preLimit": "1|1|1", "columnLimit": "30|30|30"}, {"id": 10330011, "level": 33, "rank": 1, "limit": 3, "prePlot": "10320091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "239", "time": "149400", "preLimit": "1|1|1", "columnLimit": "30|30|30"}, {"id": 10330021, "level": 33, "rank": 2, "limit": 3, "prePlot": "10330011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "239", "time": "149400", "preLimit": "3|3|3", "columnLimit": "30|30|30"}, {"id": 10330031, "level": 33, "rank": 3, "limit": 3, "prePlot": "10330021", "skill": "682505", "talent": "0", "fightSkill": "0", "mineNeed": "239", "time": "149400", "preLimit": "3|3|3", "columnLimit": "30|30|30"}, {"id": 10330032, "level": 33, "rank": 3, "limit": 3, "prePlot": "10330021", "skill": "682507", "talent": "0", "fightSkill": "0", "mineNeed": "239", "time": "149400", "preLimit": "3|3|3", "columnLimit": "30|30|30"}, {"id": 10330041, "level": 33, "rank": 4, "limit": 3, "prePlot": "10330031|10330032", "skill": "682513", "talent": "0", "fightSkill": "0", "mineNeed": "239", "time": "149400", "preLimit": "3|3|3", "columnLimit": "30|30|30"}, {"id": 10330051, "level": 33, "rank": 5, "limit": 1, "prePlot": "10330041", "skill": "0", "talent": "680013", "fightSkill": "0", "mineNeed": "239", "time": "149400", "preLimit": "3|3|3", "columnLimit": "30|30|30"}, {"id": 10330061, "level": 33, "rank": 6, "limit": 3, "prePlot": "10330051", "skill": "682506", "talent": "0", "fightSkill": "0", "mineNeed": "239", "time": "149400", "preLimit": "1|1|1", "columnLimit": "30|30|30"}, {"id": 10330062, "level": 33, "rank": 6, "limit": 3, "prePlot": "10330051", "skill": "682508", "talent": "0", "fightSkill": "0", "mineNeed": "239", "time": "149400", "preLimit": "1|1|1", "columnLimit": "30|30|30"}, {"id": 10330071, "level": 33, "rank": 7, "limit": 3, "prePlot": "10330061|10330062", "skill": "682517", "talent": "0", "fightSkill": "0", "mineNeed": "239", "time": "149400", "preLimit": "3|3|3", "columnLimit": "30|30|30"}, {"id": 10330081, "level": 33, "rank": 8, "limit": 1, "prePlot": "10330071", "skill": "682003", "talent": "0", "fightSkill": "0", "mineNeed": "239", "time": "149400", "preLimit": "3|3|3", "columnLimit": "30|30|30"}, {"id": 10330091, "level": 33, "rank": 9, "limit": 1, "prePlot": "10330081", "skill": "0", "talent": "0", "fightSkill": "681004", "mineNeed": "239", "time": "149400", "preLimit": "1|1|1", "columnLimit": "30|30|30"}, {"id": 10340011, "level": 34, "rank": 1, "limit": 3, "prePlot": "10330091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "242", "time": "151200", "preLimit": "1|1|1", "columnLimit": "30|30|30"}, {"id": 10340021, "level": 34, "rank": 2, "limit": 3, "prePlot": "10340011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "242", "time": "151200", "preLimit": "3|3|3", "columnLimit": "30|30|30"}, {"id": 10340031, "level": 34, "rank": 3, "limit": 3, "prePlot": "10340021", "skill": "682504", "talent": "0", "fightSkill": "0", "mineNeed": "242", "time": "151200", "preLimit": "3|3|3", "columnLimit": "30|30|30"}, {"id": 10340032, "level": 34, "rank": 3, "limit": 3, "prePlot": "10340021", "skill": "682503", "talent": "0", "fightSkill": "0", "mineNeed": "242", "time": "151200", "preLimit": "3|3|3", "columnLimit": "30|30|30"}, {"id": 10340041, "level": 34, "rank": 4, "limit": 3, "prePlot": "10340031|10340032", "skill": "682510", "talent": "0", "fightSkill": "0", "mineNeed": "242", "time": "151200", "preLimit": "3|3|3", "columnLimit": "30|30|30"}, {"id": 10340051, "level": 34, "rank": 5, "limit": 1, "prePlot": "10340041", "skill": "0", "talent": "680014", "fightSkill": "0", "mineNeed": "242", "time": "151200", "preLimit": "3|3|3", "columnLimit": "30|30|30"}, {"id": 10340061, "level": 34, "rank": 6, "limit": 3, "prePlot": "10340051", "skill": "682505", "talent": "0", "fightSkill": "0", "mineNeed": "242", "time": "151200", "preLimit": "1|1|1", "columnLimit": "30|30|30"}, {"id": 10340062, "level": 34, "rank": 6, "limit": 3, "prePlot": "10340051", "skill": "682507", "talent": "0", "fightSkill": "0", "mineNeed": "242", "time": "151200", "preLimit": "1|1|1", "columnLimit": "30|30|30"}, {"id": 10340071, "level": 34, "rank": 7, "limit": 3, "prePlot": "10340061|10340062", "skill": "682518", "talent": "0", "fightSkill": "0", "mineNeed": "242", "time": "151200", "preLimit": "3|3|3", "columnLimit": "30|30|30"}, {"id": 10340081, "level": 34, "rank": 8, "limit": 1, "prePlot": "10340071", "skill": "682004", "talent": "0", "fightSkill": "0", "mineNeed": "242", "time": "151200", "preLimit": "3|3|3", "columnLimit": "30|30|30"}, {"id": 10340091, "level": 34, "rank": 9, "limit": 1, "prePlot": "10340081", "skill": "682533", "talent": "0", "fightSkill": "0", "mineNeed": "242", "time": "151200", "preLimit": "1|1|1", "columnLimit": "30|30|30"}, {"id": 10350011, "level": 35, "rank": 1, "limit": 3, "prePlot": "10340091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "245", "time": "153000", "preLimit": "1|1|1", "columnLimit": "30|30|30"}, {"id": 10350021, "level": 35, "rank": 2, "limit": 3, "prePlot": "10350011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "245", "time": "153000", "preLimit": "3|3|3", "columnLimit": "30|30|30"}, {"id": 10350031, "level": 35, "rank": 3, "limit": 3, "prePlot": "10350021", "skill": "682506", "talent": "0", "fightSkill": "0", "mineNeed": "245", "time": "153000", "preLimit": "3|3|3", "columnLimit": "30|30|30"}, {"id": 10350032, "level": 35, "rank": 3, "limit": 3, "prePlot": "10350021", "skill": "682508", "talent": "0", "fightSkill": "0", "mineNeed": "245", "time": "153000", "preLimit": "3|3|3", "columnLimit": "30|30|30"}, {"id": 10350041, "level": 35, "rank": 4, "limit": 3, "prePlot": "10350031|10350032", "skill": "682511", "talent": "0", "fightSkill": "0", "mineNeed": "245", "time": "153000", "preLimit": "3|3|3", "columnLimit": "30|30|30"}, {"id": 10350051, "level": 35, "rank": 5, "limit": 1, "prePlot": "10350041", "skill": "682521", "talent": "0", "fightSkill": "0", "mineNeed": "245", "time": "153000", "preLimit": "3|3|3", "columnLimit": "30|30|30"}, {"id": 10350061, "level": 35, "rank": 6, "limit": 3, "prePlot": "10350051", "skill": "682504", "talent": "0", "fightSkill": "0", "mineNeed": "245", "time": "153000", "preLimit": "1|1|1", "columnLimit": "30|30|30"}, {"id": 10350062, "level": 35, "rank": 6, "limit": 3, "prePlot": "10350051", "skill": "682503", "talent": "0", "fightSkill": "0", "mineNeed": "245", "time": "153000", "preLimit": "1|1|1", "columnLimit": "30|30|30"}, {"id": 10350071, "level": 35, "rank": 7, "limit": 3, "prePlot": "10350061|10350062", "skill": "682517", "talent": "0", "fightSkill": "0", "mineNeed": "245", "time": "153000", "preLimit": "3|3|3", "columnLimit": "30|30|30"}, {"id": 10350081, "level": 35, "rank": 8, "limit": 1, "prePlot": "10350071", "skill": "682005", "talent": "0", "fightSkill": "0", "mineNeed": "245", "time": "153000", "preLimit": "3|3|3", "columnLimit": "30|30|30"}, {"id": 10350091, "level": 35, "rank": 9, "limit": 1, "prePlot": "10350081", "skill": "682536", "talent": "0", "fightSkill": "0", "mineNeed": "245", "time": "153000", "preLimit": "1|1|1", "columnLimit": "30|30|30"}, {"id": 10360011, "level": 36, "rank": 1, "limit": 3, "prePlot": "10350091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "248", "time": "154800", "preLimit": "1|1|1", "columnLimit": "35|35|35"}, {"id": 10360021, "level": 36, "rank": 2, "limit": 3, "prePlot": "10360011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "248", "time": "154800", "preLimit": "3|3|3", "columnLimit": "35|35|35"}, {"id": 10360031, "level": 36, "rank": 3, "limit": 3, "prePlot": "10360021", "skill": "682505", "talent": "0", "fightSkill": "0", "mineNeed": "248", "time": "154800", "preLimit": "3|3|3", "columnLimit": "35|35|35"}, {"id": 10360032, "level": 36, "rank": 3, "limit": 3, "prePlot": "10360021", "skill": "682507", "talent": "0", "fightSkill": "0", "mineNeed": "248", "time": "154800", "preLimit": "3|3|3", "columnLimit": "35|35|35"}, {"id": 10360041, "level": 36, "rank": 4, "limit": 3, "prePlot": "10360031|10360032", "skill": "682514", "talent": "0", "fightSkill": "0", "mineNeed": "248", "time": "154800", "preLimit": "3|3|3", "columnLimit": "35|35|35"}, {"id": 10360051, "level": 36, "rank": 5, "limit": 1, "prePlot": "10360041", "skill": "682519", "talent": "0", "fightSkill": "0", "mineNeed": "248", "time": "154800", "preLimit": "3|3|3", "columnLimit": "35|35|35"}, {"id": 10360061, "level": 36, "rank": 6, "limit": 3, "prePlot": "10360051", "skill": "682506", "talent": "0", "fightSkill": "0", "mineNeed": "248", "time": "154800", "preLimit": "1|1|1", "columnLimit": "35|35|35"}, {"id": 10360062, "level": 36, "rank": 6, "limit": 3, "prePlot": "10360051", "skill": "682508", "talent": "0", "fightSkill": "0", "mineNeed": "248", "time": "154800", "preLimit": "1|1|1", "columnLimit": "35|35|35"}, {"id": 10360071, "level": 36, "rank": 7, "limit": 3, "prePlot": "10360061|10360062", "skill": "682518", "talent": "0", "fightSkill": "0", "mineNeed": "248", "time": "154800", "preLimit": "3|3|3", "columnLimit": "35|35|35"}, {"id": 10360081, "level": 36, "rank": 8, "limit": 1, "prePlot": "10360071", "skill": "682006", "talent": "0", "fightSkill": "0", "mineNeed": "248", "time": "154800", "preLimit": "3|3|3", "columnLimit": "35|35|35"}, {"id": 10360091, "level": 36, "rank": 9, "limit": 1, "prePlot": "10360081", "skill": "682534", "talent": "0", "fightSkill": "0", "mineNeed": "248", "time": "154800", "preLimit": "1|1|1", "columnLimit": "35|35|35"}, {"id": 10370011, "level": 37, "rank": 1, "limit": 3, "prePlot": "10360091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "251", "time": "156600", "preLimit": "1|1|1", "columnLimit": "35|35|35"}, {"id": 10370021, "level": 37, "rank": 2, "limit": 3, "prePlot": "10370011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "251", "time": "156600", "preLimit": "3|3|3", "columnLimit": "35|35|35"}, {"id": 10370031, "level": 37, "rank": 3, "limit": 3, "prePlot": "10370021", "skill": "682504", "talent": "0", "fightSkill": "0", "mineNeed": "251", "time": "156600", "preLimit": "3|3|3", "columnLimit": "35|35|35"}, {"id": 10370032, "level": 37, "rank": 3, "limit": 3, "prePlot": "10370021", "skill": "682503", "talent": "0", "fightSkill": "0", "mineNeed": "251", "time": "156600", "preLimit": "3|3|3", "columnLimit": "35|35|35"}, {"id": 10370041, "level": 37, "rank": 4, "limit": 3, "prePlot": "10370031|10370032", "skill": "682509", "talent": "0", "fightSkill": "0", "mineNeed": "251", "time": "156600", "preLimit": "3|3|3", "columnLimit": "35|35|35"}, {"id": 10370051, "level": 37, "rank": 5, "limit": 1, "prePlot": "10370041", "skill": "682522", "talent": "0", "fightSkill": "0", "mineNeed": "251", "time": "156600", "preLimit": "3|3|3", "columnLimit": "35|35|35"}, {"id": 10370061, "level": 37, "rank": 6, "limit": 3, "prePlot": "10370051", "skill": "682505", "talent": "0", "fightSkill": "0", "mineNeed": "251", "time": "156600", "preLimit": "1|1|1", "columnLimit": "35|35|35"}, {"id": 10370062, "level": 37, "rank": 6, "limit": 3, "prePlot": "10370051", "skill": "682507", "talent": "0", "fightSkill": "0", "mineNeed": "251", "time": "156600", "preLimit": "1|1|1", "columnLimit": "35|35|35"}, {"id": 10370071, "level": 37, "rank": 7, "limit": 3, "prePlot": "10370061|10370062", "skill": "682517", "talent": "0", "fightSkill": "0", "mineNeed": "251", "time": "156600", "preLimit": "3|3|3", "columnLimit": "35|35|35"}, {"id": 10370081, "level": 37, "rank": 8, "limit": 1, "prePlot": "10370071", "skill": "682007", "talent": "0", "fightSkill": "0", "mineNeed": "251", "time": "156600", "preLimit": "3|3|3", "columnLimit": "35|35|35"}, {"id": 10370091, "level": 37, "rank": 9, "limit": 1, "prePlot": "10370081", "skill": "0", "talent": "0", "fightSkill": "681005", "mineNeed": "251", "time": "156600", "preLimit": "1|1|1", "columnLimit": "35|35|35"}, {"id": 10380011, "level": 38, "rank": 1, "limit": 3, "prePlot": "10370091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "254", "time": "158400", "preLimit": "1|1|1", "columnLimit": "35|35|35"}, {"id": 10380021, "level": 38, "rank": 2, "limit": 3, "prePlot": "10380011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "254", "time": "158400", "preLimit": "3|3|3", "columnLimit": "35|35|35"}, {"id": 10380031, "level": 38, "rank": 3, "limit": 3, "prePlot": "10380021", "skill": "682506", "talent": "0", "fightSkill": "0", "mineNeed": "254", "time": "158400", "preLimit": "3|3|3", "columnLimit": "35|35|35"}, {"id": 10380032, "level": 38, "rank": 3, "limit": 3, "prePlot": "10380021", "skill": "682508", "talent": "0", "fightSkill": "0", "mineNeed": "254", "time": "158400", "preLimit": "3|3|3", "columnLimit": "35|35|35"}, {"id": 10380041, "level": 38, "rank": 4, "limit": 3, "prePlot": "10380031|10380032", "skill": "682516", "talent": "0", "fightSkill": "0", "mineNeed": "254", "time": "158400", "preLimit": "3|3|3", "columnLimit": "35|35|35"}, {"id": 10380051, "level": 38, "rank": 5, "limit": 1, "prePlot": "10380041", "skill": "682520", "talent": "0", "fightSkill": "0", "mineNeed": "254", "time": "158400", "preLimit": "3|3|3", "columnLimit": "35|35|35"}, {"id": 10380061, "level": 38, "rank": 6, "limit": 3, "prePlot": "10380051", "skill": "682504", "talent": "0", "fightSkill": "0", "mineNeed": "254", "time": "158400", "preLimit": "1|1|1", "columnLimit": "35|35|35"}, {"id": 10380062, "level": 38, "rank": 6, "limit": 3, "prePlot": "10380051", "skill": "682503", "talent": "0", "fightSkill": "0", "mineNeed": "254", "time": "158400", "preLimit": "1|1|1", "columnLimit": "35|35|35"}, {"id": 10380071, "level": 38, "rank": 7, "limit": 3, "prePlot": "10380061|10380062", "skill": "682518", "talent": "0", "fightSkill": "0", "mineNeed": "254", "time": "158400", "preLimit": "3|3|3", "columnLimit": "35|35|35"}, {"id": 10380081, "level": 38, "rank": 8, "limit": 1, "prePlot": "10380071", "skill": "682008", "talent": "0", "fightSkill": "0", "mineNeed": "254", "time": "158400", "preLimit": "3|3|3", "columnLimit": "35|35|35"}, {"id": 10380091, "level": 38, "rank": 9, "limit": 1, "prePlot": "10380081", "skill": "682533", "talent": "0", "fightSkill": "0", "mineNeed": "254", "time": "158400", "preLimit": "1|1|1", "columnLimit": "35|35|35"}, {"id": 10390011, "level": 39, "rank": 1, "limit": 3, "prePlot": "10380091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "257", "time": "160200", "preLimit": "1|1|1", "columnLimit": "35|35|35"}, {"id": 10390021, "level": 39, "rank": 2, "limit": 3, "prePlot": "10390011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "257", "time": "160200", "preLimit": "3|3|3", "columnLimit": "35|35|35"}, {"id": 10390031, "level": 39, "rank": 3, "limit": 3, "prePlot": "10390021", "skill": "682505", "talent": "0", "fightSkill": "0", "mineNeed": "257", "time": "160200", "preLimit": "3|3|3", "columnLimit": "35|35|35"}, {"id": 10390032, "level": 39, "rank": 3, "limit": 3, "prePlot": "10390021", "skill": "682507", "talent": "0", "fightSkill": "0", "mineNeed": "257", "time": "160200", "preLimit": "3|3|3", "columnLimit": "35|35|35"}, {"id": 10390041, "level": 39, "rank": 4, "limit": 3, "prePlot": "10390031|10390032", "skill": "682515", "talent": "0", "fightSkill": "0", "mineNeed": "257", "time": "160200", "preLimit": "3|3|3", "columnLimit": "35|35|35"}, {"id": 10390051, "level": 39, "rank": 5, "limit": 1, "prePlot": "10390041", "skill": "682521", "talent": "0", "fightSkill": "0", "mineNeed": "257", "time": "160200", "preLimit": "3|3|3", "columnLimit": "35|35|35"}, {"id": 10390061, "level": 39, "rank": 6, "limit": 3, "prePlot": "10390051", "skill": "682506", "talent": "0", "fightSkill": "0", "mineNeed": "257", "time": "160200", "preLimit": "1|1|1", "columnLimit": "35|35|35"}, {"id": 10390062, "level": 39, "rank": 6, "limit": 3, "prePlot": "10390051", "skill": "682508", "talent": "0", "fightSkill": "0", "mineNeed": "257", "time": "160200", "preLimit": "1|1|1", "columnLimit": "35|35|35"}, {"id": 10390071, "level": 39, "rank": 7, "limit": 3, "prePlot": "10390061|10390062", "skill": "682517", "talent": "0", "fightSkill": "0", "mineNeed": "257", "time": "160200", "preLimit": "3|3|3", "columnLimit": "35|35|35"}, {"id": 10390081, "level": 39, "rank": 8, "limit": 1, "prePlot": "10390071", "skill": "682009", "talent": "0", "fightSkill": "0", "mineNeed": "257", "time": "160200", "preLimit": "3|3|3", "columnLimit": "35|35|35"}, {"id": 10390091, "level": 39, "rank": 9, "limit": 1, "prePlot": "10390081", "skill": "682537", "talent": "0", "fightSkill": "0", "mineNeed": "257", "time": "160200", "preLimit": "1|1|1", "columnLimit": "35|35|35"}, {"id": 10400011, "level": 40, "rank": 1, "limit": 3, "prePlot": "10390091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "260", "time": "162000", "preLimit": "1|1|1", "columnLimit": "35|35|35"}, {"id": 10400021, "level": 40, "rank": 2, "limit": 3, "prePlot": "10400011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "260", "time": "162000", "preLimit": "3|3|3", "columnLimit": "35|35|35"}, {"id": 10400031, "level": 40, "rank": 3, "limit": 3, "prePlot": "10400021", "skill": "682504", "talent": "0", "fightSkill": "0", "mineNeed": "260", "time": "162000", "preLimit": "3|3|3", "columnLimit": "35|35|35"}, {"id": 10400032, "level": 40, "rank": 3, "limit": 3, "prePlot": "10400021", "skill": "682503", "talent": "0", "fightSkill": "0", "mineNeed": "260", "time": "162000", "preLimit": "3|3|3", "columnLimit": "35|35|35"}, {"id": 10400041, "level": 40, "rank": 4, "limit": 3, "prePlot": "10400031|10400032", "skill": "682512", "talent": "0", "fightSkill": "0", "mineNeed": "260", "time": "162000", "preLimit": "3|3|3", "columnLimit": "35|35|35"}, {"id": 10400051, "level": 40, "rank": 5, "limit": 1, "prePlot": "10400041", "skill": "682519", "talent": "0", "fightSkill": "0", "mineNeed": "260", "time": "162000", "preLimit": "3|3|3", "columnLimit": "35|35|35"}, {"id": 10400061, "level": 40, "rank": 6, "limit": 3, "prePlot": "10400051", "skill": "682505", "talent": "0", "fightSkill": "0", "mineNeed": "260", "time": "162000", "preLimit": "1|1|1", "columnLimit": "35|35|35"}, {"id": 10400062, "level": 40, "rank": 6, "limit": 3, "prePlot": "10400051", "skill": "682507", "talent": "0", "fightSkill": "0", "mineNeed": "260", "time": "162000", "preLimit": "1|1|1", "columnLimit": "35|35|35"}, {"id": 10400071, "level": 40, "rank": 7, "limit": 3, "prePlot": "10400061|10400062", "skill": "682518", "talent": "0", "fightSkill": "0", "mineNeed": "260", "time": "162000", "preLimit": "3|3|3", "columnLimit": "35|35|35"}, {"id": 10400081, "level": 40, "rank": 8, "limit": 1, "prePlot": "10400071", "skill": "682010", "talent": "0", "fightSkill": "0", "mineNeed": "260", "time": "162000", "preLimit": "3|3|3", "columnLimit": "35|35|35"}, {"id": 10400091, "level": 40, "rank": 9, "limit": 1, "prePlot": "10400081", "skill": "682535", "talent": "0", "fightSkill": "0", "mineNeed": "260", "time": "162000", "preLimit": "1|1|1", "columnLimit": "35|35|35"}, {"id": 10410011, "level": 41, "rank": 1, "limit": 3, "prePlot": "10400091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "263", "time": "163800", "preLimit": "1|1|1", "columnLimit": "40|40|40"}, {"id": 10410021, "level": 41, "rank": 2, "limit": 3, "prePlot": "10410011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "263", "time": "163800", "preLimit": "3|3|3", "columnLimit": "40|40|40"}, {"id": 10410031, "level": 41, "rank": 3, "limit": 3, "prePlot": "10410021", "skill": "682506", "talent": "0", "fightSkill": "0", "mineNeed": "263", "time": "163800", "preLimit": "3|3|3", "columnLimit": "40|40|40"}, {"id": 10410032, "level": 41, "rank": 3, "limit": 3, "prePlot": "10410021", "skill": "682508", "talent": "0", "fightSkill": "0", "mineNeed": "263", "time": "163800", "preLimit": "3|3|3", "columnLimit": "40|40|40"}, {"id": 10410041, "level": 41, "rank": 4, "limit": 3, "prePlot": "10410031|10410032", "skill": "682513", "talent": "0", "fightSkill": "0", "mineNeed": "263", "time": "163800", "preLimit": "3|3|3", "columnLimit": "40|40|40"}, {"id": 10410051, "level": 41, "rank": 5, "limit": 1, "prePlot": "10410041", "skill": "682522", "talent": "0", "fightSkill": "0", "mineNeed": "263", "time": "163800", "preLimit": "3|3|3", "columnLimit": "40|40|40"}, {"id": 10410061, "level": 41, "rank": 6, "limit": 3, "prePlot": "10410051", "skill": "682504", "talent": "0", "fightSkill": "0", "mineNeed": "263", "time": "163800", "preLimit": "1|1|1", "columnLimit": "40|40|40"}, {"id": 10410062, "level": 41, "rank": 6, "limit": 3, "prePlot": "10410051", "skill": "682503", "talent": "0", "fightSkill": "0", "mineNeed": "263", "time": "163800", "preLimit": "1|1|1", "columnLimit": "40|40|40"}, {"id": 10410071, "level": 41, "rank": 7, "limit": 3, "prePlot": "10410061|10410062", "skill": "682517", "talent": "0", "fightSkill": "0", "mineNeed": "263", "time": "163800", "preLimit": "3|3|3", "columnLimit": "40|40|40"}, {"id": 10410081, "level": 41, "rank": 8, "limit": 1, "prePlot": "10410071", "skill": "682001", "talent": "0", "fightSkill": "0", "mineNeed": "263", "time": "163800", "preLimit": "3|3|3", "columnLimit": "40|40|40"}, {"id": 10410091, "level": 41, "rank": 9, "limit": 1, "prePlot": "10410081", "skill": "0", "talent": "0", "fightSkill": "681001", "mineNeed": "263", "time": "163800", "preLimit": "1|1|1", "columnLimit": "40|40|40"}, {"id": 10420011, "level": 42, "rank": 1, "limit": 3, "prePlot": "10410091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "266", "time": "165600", "preLimit": "1|1|1", "columnLimit": "40|40|40"}, {"id": 10420021, "level": 42, "rank": 2, "limit": 3, "prePlot": "10420011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "266", "time": "165600", "preLimit": "3|3|3", "columnLimit": "40|40|40"}, {"id": 10420031, "level": 42, "rank": 3, "limit": 3, "prePlot": "10420021", "skill": "682505", "talent": "0", "fightSkill": "0", "mineNeed": "266", "time": "165600", "preLimit": "3|3|3", "columnLimit": "40|40|40"}, {"id": 10420032, "level": 42, "rank": 3, "limit": 3, "prePlot": "10420021", "skill": "682507", "talent": "0", "fightSkill": "0", "mineNeed": "266", "time": "165600", "preLimit": "3|3|3", "columnLimit": "40|40|40"}, {"id": 10420041, "level": 42, "rank": 4, "limit": 3, "prePlot": "10420031|10420032", "skill": "682510", "talent": "0", "fightSkill": "0", "mineNeed": "266", "time": "165600", "preLimit": "3|3|3", "columnLimit": "40|40|40"}, {"id": 10420051, "level": 42, "rank": 5, "limit": 1, "prePlot": "10420041", "skill": "682520", "talent": "0", "fightSkill": "0", "mineNeed": "266", "time": "165600", "preLimit": "3|3|3", "columnLimit": "40|40|40"}, {"id": 10420061, "level": 42, "rank": 6, "limit": 3, "prePlot": "10420051", "skill": "682506", "talent": "0", "fightSkill": "0", "mineNeed": "266", "time": "165600", "preLimit": "1|1|1", "columnLimit": "40|40|40"}, {"id": 10420062, "level": 42, "rank": 6, "limit": 3, "prePlot": "10420051", "skill": "682508", "talent": "0", "fightSkill": "0", "mineNeed": "266", "time": "165600", "preLimit": "1|1|1", "columnLimit": "40|40|40"}, {"id": 10420071, "level": 42, "rank": 7, "limit": 3, "prePlot": "10420061|10420062", "skill": "682518", "talent": "0", "fightSkill": "0", "mineNeed": "266", "time": "165600", "preLimit": "3|3|3", "columnLimit": "40|40|40"}, {"id": 10420081, "level": 42, "rank": 8, "limit": 1, "prePlot": "10420071", "skill": "682002", "talent": "0", "fightSkill": "0", "mineNeed": "266", "time": "165600", "preLimit": "3|3|3", "columnLimit": "40|40|40"}, {"id": 10420091, "level": 42, "rank": 9, "limit": 1, "prePlot": "10420081", "skill": "682533", "talent": "0", "fightSkill": "0", "mineNeed": "266", "time": "165600", "preLimit": "1|1|1", "columnLimit": "40|40|40"}, {"id": 10430011, "level": 43, "rank": 1, "limit": 3, "prePlot": "10420091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "269", "time": "167400", "preLimit": "1|1|1", "columnLimit": "40|40|40"}, {"id": 10430021, "level": 43, "rank": 2, "limit": 3, "prePlot": "10430011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "269", "time": "167400", "preLimit": "3|3|3", "columnLimit": "40|40|40"}, {"id": 10430031, "level": 43, "rank": 3, "limit": 3, "prePlot": "10430021", "skill": "682504", "talent": "0", "fightSkill": "0", "mineNeed": "269", "time": "167400", "preLimit": "3|3|3", "columnLimit": "40|40|40"}, {"id": 10430032, "level": 43, "rank": 3, "limit": 3, "prePlot": "10430021", "skill": "682503", "talent": "0", "fightSkill": "0", "mineNeed": "269", "time": "167400", "preLimit": "3|3|3", "columnLimit": "40|40|40"}, {"id": 10430041, "level": 43, "rank": 4, "limit": 3, "prePlot": "10430031|10430032", "skill": "682511", "talent": "0", "fightSkill": "0", "mineNeed": "269", "time": "167400", "preLimit": "3|3|3", "columnLimit": "40|40|40"}, {"id": 10430051, "level": 43, "rank": 5, "limit": 1, "prePlot": "10430041", "skill": "682521", "talent": "0", "fightSkill": "0", "mineNeed": "269", "time": "167400", "preLimit": "3|3|3", "columnLimit": "40|40|40"}, {"id": 10430061, "level": 43, "rank": 6, "limit": 3, "prePlot": "10430051", "skill": "682505", "talent": "0", "fightSkill": "0", "mineNeed": "269", "time": "167400", "preLimit": "1|1|1", "columnLimit": "40|40|40"}, {"id": 10430062, "level": 43, "rank": 6, "limit": 3, "prePlot": "10430051", "skill": "682507", "talent": "0", "fightSkill": "0", "mineNeed": "269", "time": "167400", "preLimit": "1|1|1", "columnLimit": "40|40|40"}, {"id": 10430071, "level": 43, "rank": 7, "limit": 3, "prePlot": "10430061|10430062", "skill": "682517", "talent": "0", "fightSkill": "0", "mineNeed": "269", "time": "167400", "preLimit": "3|3|3", "columnLimit": "40|40|40"}, {"id": 10430081, "level": 43, "rank": 8, "limit": 1, "prePlot": "10430071", "skill": "682003", "talent": "0", "fightSkill": "0", "mineNeed": "269", "time": "167400", "preLimit": "3|3|3", "columnLimit": "40|40|40"}, {"id": 10430091, "level": 43, "rank": 9, "limit": 1, "prePlot": "10430081", "skill": "682536", "talent": "0", "fightSkill": "0", "mineNeed": "269", "time": "167400", "preLimit": "1|1|1", "columnLimit": "40|40|40"}, {"id": 10440011, "level": 44, "rank": 1, "limit": 3, "prePlot": "10430091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "272", "time": "169200", "preLimit": "1|1|1", "columnLimit": "40|40|40"}, {"id": 10440021, "level": 44, "rank": 2, "limit": 3, "prePlot": "10440011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "272", "time": "169200", "preLimit": "3|3|3", "columnLimit": "40|40|40"}, {"id": 10440031, "level": 44, "rank": 3, "limit": 3, "prePlot": "10440021", "skill": "682506", "talent": "0", "fightSkill": "0", "mineNeed": "272", "time": "169200", "preLimit": "3|3|3", "columnLimit": "40|40|40"}, {"id": 10440032, "level": 44, "rank": 3, "limit": 3, "prePlot": "10440021", "skill": "682508", "talent": "0", "fightSkill": "0", "mineNeed": "272", "time": "169200", "preLimit": "3|3|3", "columnLimit": "40|40|40"}, {"id": 10440041, "level": 44, "rank": 4, "limit": 3, "prePlot": "10440031|10440032", "skill": "682514", "talent": "0", "fightSkill": "0", "mineNeed": "272", "time": "169200", "preLimit": "3|3|3", "columnLimit": "40|40|40"}, {"id": 10440051, "level": 44, "rank": 5, "limit": 1, "prePlot": "10440041", "skill": "682519", "talent": "0", "fightSkill": "0", "mineNeed": "272", "time": "169200", "preLimit": "3|3|3", "columnLimit": "40|40|40"}, {"id": 10440061, "level": 44, "rank": 6, "limit": 3, "prePlot": "10440051", "skill": "682504", "talent": "0", "fightSkill": "0", "mineNeed": "272", "time": "169200", "preLimit": "1|1|1", "columnLimit": "40|40|40"}, {"id": 10440062, "level": 44, "rank": 6, "limit": 3, "prePlot": "10440051", "skill": "682503", "talent": "0", "fightSkill": "0", "mineNeed": "272", "time": "169200", "preLimit": "1|1|1", "columnLimit": "40|40|40"}, {"id": 10440071, "level": 44, "rank": 7, "limit": 3, "prePlot": "10440061|10440062", "skill": "682518", "talent": "0", "fightSkill": "0", "mineNeed": "272", "time": "169200", "preLimit": "3|3|3", "columnLimit": "40|40|40"}, {"id": 10440081, "level": 44, "rank": 8, "limit": 1, "prePlot": "10440071", "skill": "682004", "talent": "0", "fightSkill": "0", "mineNeed": "272", "time": "169200", "preLimit": "3|3|3", "columnLimit": "40|40|40"}, {"id": 10440091, "level": 44, "rank": 9, "limit": 1, "prePlot": "10440081", "skill": "682535", "talent": "0", "fightSkill": "0", "mineNeed": "272", "time": "169200", "preLimit": "1|1|1", "columnLimit": "40|40|40"}, {"id": 10450011, "level": 45, "rank": 1, "limit": 3, "prePlot": "10440091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "275", "time": "171000", "preLimit": "1|1|1", "columnLimit": "40|40|40"}, {"id": 10450021, "level": 45, "rank": 2, "limit": 3, "prePlot": "10450011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "275", "time": "171000", "preLimit": "3|3|3", "columnLimit": "40|40|40"}, {"id": 10450031, "level": 45, "rank": 3, "limit": 3, "prePlot": "10450021", "skill": "682505", "talent": "0", "fightSkill": "0", "mineNeed": "275", "time": "171000", "preLimit": "3|3|3", "columnLimit": "40|40|40"}, {"id": 10450032, "level": 45, "rank": 3, "limit": 3, "prePlot": "10450021", "skill": "682507", "talent": "0", "fightSkill": "0", "mineNeed": "275", "time": "171000", "preLimit": "3|3|3", "columnLimit": "40|40|40"}, {"id": 10450041, "level": 45, "rank": 4, "limit": 3, "prePlot": "10450031|10450032", "skill": "682509", "talent": "0", "fightSkill": "0", "mineNeed": "275", "time": "171000", "preLimit": "3|3|3", "columnLimit": "40|40|40"}, {"id": 10450051, "level": 45, "rank": 5, "limit": 1, "prePlot": "10450041", "skill": "682522", "talent": "0", "fightSkill": "0", "mineNeed": "275", "time": "171000", "preLimit": "3|3|3", "columnLimit": "40|40|40"}, {"id": 10450061, "level": 45, "rank": 6, "limit": 3, "prePlot": "10450051", "skill": "682506", "talent": "0", "fightSkill": "0", "mineNeed": "275", "time": "171000", "preLimit": "1|1|1", "columnLimit": "40|40|40"}, {"id": 10450062, "level": 45, "rank": 6, "limit": 3, "prePlot": "10450051", "skill": "682508", "talent": "0", "fightSkill": "0", "mineNeed": "275", "time": "171000", "preLimit": "1|1|1", "columnLimit": "40|40|40"}, {"id": 10450071, "level": 45, "rank": 7, "limit": 3, "prePlot": "10450061|10450062", "skill": "682517", "talent": "0", "fightSkill": "0", "mineNeed": "275", "time": "171000", "preLimit": "3|3|3", "columnLimit": "40|40|40"}, {"id": 10450081, "level": 45, "rank": 8, "limit": 1, "prePlot": "10450071", "skill": "682005", "talent": "0", "fightSkill": "0", "mineNeed": "275", "time": "171000", "preLimit": "3|3|3", "columnLimit": "40|40|40"}, {"id": 10450091, "level": 45, "rank": 9, "limit": 1, "prePlot": "10450081", "skill": "0", "talent": "0", "fightSkill": "681002", "mineNeed": "275", "time": "171000", "preLimit": "1|1|1", "columnLimit": "40|40|40"}, {"id": 10460011, "level": 46, "rank": 1, "limit": 3, "prePlot": "10450091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "278", "time": "172800", "preLimit": "1|1|1", "columnLimit": "45|45|45"}, {"id": 10460021, "level": 46, "rank": 2, "limit": 3, "prePlot": "10460011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "278", "time": "172800", "preLimit": "3|3|3", "columnLimit": "45|45|45"}, {"id": 10460031, "level": 46, "rank": 3, "limit": 3, "prePlot": "10460021", "skill": "682504", "talent": "0", "fightSkill": "0", "mineNeed": "278", "time": "172800", "preLimit": "3|3|3", "columnLimit": "45|45|45"}, {"id": 10460032, "level": 46, "rank": 3, "limit": 3, "prePlot": "10460021", "skill": "682503", "talent": "0", "fightSkill": "0", "mineNeed": "278", "time": "172800", "preLimit": "3|3|3", "columnLimit": "45|45|45"}, {"id": 10460041, "level": 46, "rank": 4, "limit": 3, "prePlot": "10460031|10460032", "skill": "682516", "talent": "0", "fightSkill": "0", "mineNeed": "278", "time": "172800", "preLimit": "3|3|3", "columnLimit": "45|45|45"}, {"id": 10460051, "level": 46, "rank": 5, "limit": 1, "prePlot": "10460041", "skill": "682520", "talent": "0", "fightSkill": "0", "mineNeed": "278", "time": "172800", "preLimit": "3|3|3", "columnLimit": "45|45|45"}, {"id": 10460061, "level": 46, "rank": 6, "limit": 3, "prePlot": "10460051", "skill": "682505", "talent": "0", "fightSkill": "0", "mineNeed": "278", "time": "172800", "preLimit": "1|1|1", "columnLimit": "45|45|45"}, {"id": 10460062, "level": 46, "rank": 6, "limit": 3, "prePlot": "10460051", "skill": "682507", "talent": "0", "fightSkill": "0", "mineNeed": "278", "time": "172800", "preLimit": "1|1|1", "columnLimit": "45|45|45"}, {"id": 10460071, "level": 46, "rank": 7, "limit": 3, "prePlot": "10460061|10460062", "skill": "682518", "talent": "0", "fightSkill": "0", "mineNeed": "278", "time": "172800", "preLimit": "3|3|3", "columnLimit": "45|45|45"}, {"id": 10460081, "level": 46, "rank": 8, "limit": 1, "prePlot": "10460071", "skill": "682006", "talent": "0", "fightSkill": "0", "mineNeed": "278", "time": "172800", "preLimit": "3|3|3", "columnLimit": "45|45|45"}, {"id": 10460091, "level": 46, "rank": 9, "limit": 1, "prePlot": "10460081", "skill": "682533", "talent": "0", "fightSkill": "0", "mineNeed": "278", "time": "172800", "preLimit": "1|1|1", "columnLimit": "45|45|45"}, {"id": 10470011, "level": 47, "rank": 1, "limit": 3, "prePlot": "10460091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "281", "time": "174600", "preLimit": "1|1|1", "columnLimit": "45|45|45"}, {"id": 10470021, "level": 47, "rank": 2, "limit": 3, "prePlot": "10470011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "281", "time": "174600", "preLimit": "3|3|3", "columnLimit": "45|45|45"}, {"id": 10470031, "level": 47, "rank": 3, "limit": 3, "prePlot": "10470021", "skill": "682506", "talent": "0", "fightSkill": "0", "mineNeed": "281", "time": "174600", "preLimit": "3|3|3", "columnLimit": "45|45|45"}, {"id": 10470032, "level": 47, "rank": 3, "limit": 3, "prePlot": "10470021", "skill": "682508", "talent": "0", "fightSkill": "0", "mineNeed": "281", "time": "174600", "preLimit": "3|3|3", "columnLimit": "45|45|45"}, {"id": 10470041, "level": 47, "rank": 4, "limit": 3, "prePlot": "10470031|10470032", "skill": "682515", "talent": "0", "fightSkill": "0", "mineNeed": "281", "time": "174600", "preLimit": "3|3|3", "columnLimit": "45|45|45"}, {"id": 10470051, "level": 47, "rank": 5, "limit": 1, "prePlot": "10470041", "skill": "682521", "talent": "0", "fightSkill": "0", "mineNeed": "281", "time": "174600", "preLimit": "3|3|3", "columnLimit": "45|45|45"}, {"id": 10470061, "level": 47, "rank": 6, "limit": 3, "prePlot": "10470051", "skill": "682504", "talent": "0", "fightSkill": "0", "mineNeed": "281", "time": "174600", "preLimit": "1|1|1", "columnLimit": "45|45|45"}, {"id": 10470062, "level": 47, "rank": 6, "limit": 3, "prePlot": "10470051", "skill": "682503", "talent": "0", "fightSkill": "0", "mineNeed": "281", "time": "174600", "preLimit": "1|1|1", "columnLimit": "45|45|45"}, {"id": 10470071, "level": 47, "rank": 7, "limit": 3, "prePlot": "10470061|10470062", "skill": "682517", "talent": "0", "fightSkill": "0", "mineNeed": "281", "time": "174600", "preLimit": "3|3|3", "columnLimit": "45|45|45"}, {"id": 10470081, "level": 47, "rank": 8, "limit": 1, "prePlot": "10470071", "skill": "682007", "talent": "0", "fightSkill": "0", "mineNeed": "281", "time": "174600", "preLimit": "3|3|3", "columnLimit": "45|45|45"}, {"id": 10470091, "level": 47, "rank": 9, "limit": 1, "prePlot": "10470081", "skill": "682537", "talent": "0", "fightSkill": "0", "mineNeed": "281", "time": "174600", "preLimit": "1|1|1", "columnLimit": "45|45|45"}, {"id": 10480011, "level": 48, "rank": 1, "limit": 3, "prePlot": "10470091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "284", "time": "176400", "preLimit": "1|1|1", "columnLimit": "45|45|45"}, {"id": 10480021, "level": 48, "rank": 2, "limit": 3, "prePlot": "10480011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "284", "time": "176400", "preLimit": "3|3|3", "columnLimit": "45|45|45"}, {"id": 10480031, "level": 48, "rank": 3, "limit": 3, "prePlot": "10480021", "skill": "682505", "talent": "0", "fightSkill": "0", "mineNeed": "284", "time": "176400", "preLimit": "3|3|3", "columnLimit": "45|45|45"}, {"id": 10480032, "level": 48, "rank": 3, "limit": 3, "prePlot": "10480021", "skill": "682507", "talent": "0", "fightSkill": "0", "mineNeed": "284", "time": "176400", "preLimit": "3|3|3", "columnLimit": "45|45|45"}, {"id": 10480041, "level": 48, "rank": 4, "limit": 3, "prePlot": "10480031|10480032", "skill": "682512", "talent": "0", "fightSkill": "0", "mineNeed": "284", "time": "176400", "preLimit": "3|3|3", "columnLimit": "45|45|45"}, {"id": 10480051, "level": 48, "rank": 5, "limit": 1, "prePlot": "10480041", "skill": "682519", "talent": "0", "fightSkill": "0", "mineNeed": "284", "time": "176400", "preLimit": "3|3|3", "columnLimit": "45|45|45"}, {"id": 10480061, "level": 48, "rank": 6, "limit": 3, "prePlot": "10480051", "skill": "682506", "talent": "0", "fightSkill": "0", "mineNeed": "284", "time": "176400", "preLimit": "1|1|1", "columnLimit": "45|45|45"}, {"id": 10480062, "level": 48, "rank": 6, "limit": 3, "prePlot": "10480051", "skill": "682508", "talent": "0", "fightSkill": "0", "mineNeed": "284", "time": "176400", "preLimit": "1|1|1", "columnLimit": "45|45|45"}, {"id": 10480071, "level": 48, "rank": 7, "limit": 3, "prePlot": "10480061|10480062", "skill": "682518", "talent": "0", "fightSkill": "0", "mineNeed": "284", "time": "176400", "preLimit": "3|3|3", "columnLimit": "45|45|45"}, {"id": 10480081, "level": 48, "rank": 8, "limit": 1, "prePlot": "10480071", "skill": "682008", "talent": "0", "fightSkill": "0", "mineNeed": "284", "time": "176400", "preLimit": "3|3|3", "columnLimit": "45|45|45"}, {"id": 10480091, "level": 48, "rank": 9, "limit": 1, "prePlot": "10480081", "skill": "682534", "talent": "0", "fightSkill": "0", "mineNeed": "284", "time": "176400", "preLimit": "1|1|1", "columnLimit": "45|45|45"}, {"id": 10490011, "level": 49, "rank": 1, "limit": 3, "prePlot": "10480091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "287", "time": "178200", "preLimit": "1|1|1", "columnLimit": "45|45|45"}, {"id": 10490021, "level": 49, "rank": 2, "limit": 3, "prePlot": "10490011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "287", "time": "178200", "preLimit": "3|3|3", "columnLimit": "45|45|45"}, {"id": 10490031, "level": 49, "rank": 3, "limit": 3, "prePlot": "10490021", "skill": "682504", "talent": "0", "fightSkill": "0", "mineNeed": "287", "time": "178200", "preLimit": "3|3|3", "columnLimit": "45|45|45"}, {"id": 10490032, "level": 49, "rank": 3, "limit": 3, "prePlot": "10490021", "skill": "682503", "talent": "0", "fightSkill": "0", "mineNeed": "287", "time": "178200", "preLimit": "3|3|3", "columnLimit": "45|45|45"}, {"id": 10490041, "level": 49, "rank": 4, "limit": 3, "prePlot": "10490031|10490032", "skill": "682513", "talent": "0", "fightSkill": "0", "mineNeed": "287", "time": "178200", "preLimit": "3|3|3", "columnLimit": "45|45|45"}, {"id": 10490051, "level": 49, "rank": 5, "limit": 1, "prePlot": "10490041", "skill": "682522", "talent": "0", "fightSkill": "0", "mineNeed": "287", "time": "178200", "preLimit": "3|3|3", "columnLimit": "45|45|45"}, {"id": 10490061, "level": 49, "rank": 6, "limit": 3, "prePlot": "10490051", "skill": "682505", "talent": "0", "fightSkill": "0", "mineNeed": "287", "time": "178200", "preLimit": "1|1|1", "columnLimit": "45|45|45"}, {"id": 10490062, "level": 49, "rank": 6, "limit": 3, "prePlot": "10490051", "skill": "682507", "talent": "0", "fightSkill": "0", "mineNeed": "287", "time": "178200", "preLimit": "1|1|1", "columnLimit": "45|45|45"}, {"id": 10490071, "level": 49, "rank": 7, "limit": 3, "prePlot": "10490061|10490062", "skill": "682517", "talent": "0", "fightSkill": "0", "mineNeed": "287", "time": "178200", "preLimit": "3|3|3", "columnLimit": "45|45|45"}, {"id": 10490081, "level": 49, "rank": 8, "limit": 1, "prePlot": "10490071", "skill": "682009", "talent": "0", "fightSkill": "0", "mineNeed": "287", "time": "178200", "preLimit": "3|3|3", "columnLimit": "45|45|45"}, {"id": 10490091, "level": 49, "rank": 9, "limit": 1, "prePlot": "10490081", "skill": "0", "talent": "0", "fightSkill": "681003", "mineNeed": "287", "time": "178200", "preLimit": "1|1|1", "columnLimit": "45|45|45"}, {"id": 10500011, "level": 50, "rank": 1, "limit": 3, "prePlot": "10490091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "290", "time": "180000", "preLimit": "1|1|1", "columnLimit": "45|45|45"}, {"id": 10500021, "level": 50, "rank": 2, "limit": 3, "prePlot": "10500011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "290", "time": "180000", "preLimit": "3|3|3", "columnLimit": "45|45|45"}, {"id": 10500031, "level": 50, "rank": 3, "limit": 3, "prePlot": "10500021", "skill": "682506", "talent": "0", "fightSkill": "0", "mineNeed": "290", "time": "180000", "preLimit": "3|3|3", "columnLimit": "45|45|45"}, {"id": 10500032, "level": 50, "rank": 3, "limit": 3, "prePlot": "10500021", "skill": "682508", "talent": "0", "fightSkill": "0", "mineNeed": "290", "time": "180000", "preLimit": "3|3|3", "columnLimit": "45|45|45"}, {"id": 10500041, "level": 50, "rank": 4, "limit": 3, "prePlot": "10500031|10500032", "skill": "682510", "talent": "0", "fightSkill": "0", "mineNeed": "290", "time": "180000", "preLimit": "3|3|3", "columnLimit": "45|45|45"}, {"id": 10500051, "level": 50, "rank": 5, "limit": 1, "prePlot": "10500041", "skill": "682520", "talent": "0", "fightSkill": "0", "mineNeed": "290", "time": "180000", "preLimit": "3|3|3", "columnLimit": "45|45|45"}, {"id": 10500061, "level": 50, "rank": 6, "limit": 3, "prePlot": "10500051", "skill": "682504", "talent": "0", "fightSkill": "0", "mineNeed": "290", "time": "180000", "preLimit": "1|1|1", "columnLimit": "45|45|45"}, {"id": 10500062, "level": 50, "rank": 6, "limit": 3, "prePlot": "10500051", "skill": "682503", "talent": "0", "fightSkill": "0", "mineNeed": "290", "time": "180000", "preLimit": "1|1|1", "columnLimit": "45|45|45"}, {"id": 10500071, "level": 50, "rank": 7, "limit": 3, "prePlot": "10500061|10500062", "skill": "682518", "talent": "0", "fightSkill": "0", "mineNeed": "290", "time": "180000", "preLimit": "3|3|3", "columnLimit": "45|45|45"}, {"id": 10500081, "level": 50, "rank": 8, "limit": 1, "prePlot": "10500071", "skill": "682010", "talent": "0", "fightSkill": "0", "mineNeed": "290", "time": "180000", "preLimit": "3|3|3", "columnLimit": "45|45|45"}, {"id": 10500091, "level": 50, "rank": 9, "limit": 1, "prePlot": "10500081", "skill": "682533", "talent": "0", "fightSkill": "0", "mineNeed": "290", "time": "180000", "preLimit": "1|1|1", "columnLimit": "45|45|45"}, {"id": 10510011, "level": 51, "rank": 1, "limit": 3, "prePlot": "10500091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "293", "time": "181800", "preLimit": "1|1|1", "columnLimit": "50|50|50"}, {"id": 10510021, "level": 51, "rank": 2, "limit": 3, "prePlot": "10510011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "293", "time": "181800", "preLimit": "3|3|3", "columnLimit": "50|50|50"}, {"id": 10510031, "level": 51, "rank": 3, "limit": 3, "prePlot": "10510021", "skill": "682505", "talent": "0", "fightSkill": "0", "mineNeed": "293", "time": "181800", "preLimit": "3|3|3", "columnLimit": "50|50|50"}, {"id": 10510032, "level": 51, "rank": 3, "limit": 3, "prePlot": "10510021", "skill": "682507", "talent": "0", "fightSkill": "0", "mineNeed": "293", "time": "181800", "preLimit": "3|3|3", "columnLimit": "50|50|50"}, {"id": 10510041, "level": 51, "rank": 4, "limit": 3, "prePlot": "10510031|10510032", "skill": "682511", "talent": "0", "fightSkill": "0", "mineNeed": "293", "time": "181800", "preLimit": "3|3|3", "columnLimit": "50|50|50"}, {"id": 10510051, "level": 51, "rank": 5, "limit": 1, "prePlot": "10510041", "skill": "682521", "talent": "0", "fightSkill": "0", "mineNeed": "293", "time": "181800", "preLimit": "3|3|3", "columnLimit": "50|50|50"}, {"id": 10510061, "level": 51, "rank": 6, "limit": 3, "prePlot": "10510051", "skill": "682506", "talent": "0", "fightSkill": "0", "mineNeed": "293", "time": "181800", "preLimit": "1|1|1", "columnLimit": "50|50|50"}, {"id": 10510062, "level": 51, "rank": 6, "limit": 3, "prePlot": "10510051", "skill": "682508", "talent": "0", "fightSkill": "0", "mineNeed": "293", "time": "181800", "preLimit": "1|1|1", "columnLimit": "50|50|50"}, {"id": 10510071, "level": 51, "rank": 7, "limit": 3, "prePlot": "10510061|10510062", "skill": "682517", "talent": "0", "fightSkill": "0", "mineNeed": "293", "time": "181800", "preLimit": "3|3|3", "columnLimit": "50|50|50"}, {"id": 10510081, "level": 51, "rank": 8, "limit": 1, "prePlot": "10510071", "skill": "682001", "talent": "0", "fightSkill": "0", "mineNeed": "293", "time": "181800", "preLimit": "3|3|3", "columnLimit": "50|50|50"}, {"id": 10510091, "level": 51, "rank": 9, "limit": 1, "prePlot": "10510081", "skill": "682536", "talent": "0", "fightSkill": "0", "mineNeed": "293", "time": "181800", "preLimit": "1|1|1", "columnLimit": "50|50|50"}, {"id": 10520011, "level": 52, "rank": 1, "limit": 3, "prePlot": "10510091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "296", "time": "183600", "preLimit": "1|1|1", "columnLimit": "50|50|50"}, {"id": 10520021, "level": 52, "rank": 2, "limit": 3, "prePlot": "10520011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "296", "time": "183600", "preLimit": "3|3|3", "columnLimit": "50|50|50"}, {"id": 10520031, "level": 52, "rank": 3, "limit": 3, "prePlot": "10520021", "skill": "682504", "talent": "0", "fightSkill": "0", "mineNeed": "296", "time": "183600", "preLimit": "3|3|3", "columnLimit": "50|50|50"}, {"id": 10520032, "level": 52, "rank": 3, "limit": 3, "prePlot": "10520021", "skill": "682503", "talent": "0", "fightSkill": "0", "mineNeed": "296", "time": "183600", "preLimit": "3|3|3", "columnLimit": "50|50|50"}, {"id": 10520041, "level": 52, "rank": 4, "limit": 3, "prePlot": "10520031|10520032", "skill": "682514", "talent": "0", "fightSkill": "0", "mineNeed": "296", "time": "183600", "preLimit": "3|3|3", "columnLimit": "50|50|50"}, {"id": 10520051, "level": 52, "rank": 5, "limit": 1, "prePlot": "10520041", "skill": "682519", "talent": "0", "fightSkill": "0", "mineNeed": "296", "time": "183600", "preLimit": "3|3|3", "columnLimit": "50|50|50"}, {"id": 10520061, "level": 52, "rank": 6, "limit": 3, "prePlot": "10520051", "skill": "682505", "talent": "0", "fightSkill": "0", "mineNeed": "296", "time": "183600", "preLimit": "1|1|1", "columnLimit": "50|50|50"}, {"id": 10520062, "level": 52, "rank": 6, "limit": 3, "prePlot": "10520051", "skill": "682507", "talent": "0", "fightSkill": "0", "mineNeed": "296", "time": "183600", "preLimit": "1|1|1", "columnLimit": "50|50|50"}, {"id": 10520071, "level": 52, "rank": 7, "limit": 3, "prePlot": "10520061|10520062", "skill": "682518", "talent": "0", "fightSkill": "0", "mineNeed": "296", "time": "183600", "preLimit": "3|3|3", "columnLimit": "50|50|50"}, {"id": 10520081, "level": 52, "rank": 8, "limit": 1, "prePlot": "10520071", "skill": "682002", "talent": "0", "fightSkill": "0", "mineNeed": "296", "time": "183600", "preLimit": "3|3|3", "columnLimit": "50|50|50"}, {"id": 10520091, "level": 52, "rank": 9, "limit": 1, "prePlot": "10520081", "skill": "682535", "talent": "0", "fightSkill": "0", "mineNeed": "296", "time": "183600", "preLimit": "1|1|1", "columnLimit": "50|50|50"}, {"id": 10530011, "level": 53, "rank": 1, "limit": 3, "prePlot": "10520091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "299", "time": "185400", "preLimit": "1|1|1", "columnLimit": "50|50|50"}, {"id": 10530021, "level": 53, "rank": 2, "limit": 3, "prePlot": "10530011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "299", "time": "185400", "preLimit": "3|3|3", "columnLimit": "50|50|50"}, {"id": 10530031, "level": 53, "rank": 3, "limit": 3, "prePlot": "10530021", "skill": "682506", "talent": "0", "fightSkill": "0", "mineNeed": "299", "time": "185400", "preLimit": "3|3|3", "columnLimit": "50|50|50"}, {"id": 10530032, "level": 53, "rank": 3, "limit": 3, "prePlot": "10530021", "skill": "682508", "talent": "0", "fightSkill": "0", "mineNeed": "299", "time": "185400", "preLimit": "3|3|3", "columnLimit": "50|50|50"}, {"id": 10530041, "level": 53, "rank": 4, "limit": 3, "prePlot": "10530031|10530032", "skill": "682509", "talent": "0", "fightSkill": "0", "mineNeed": "299", "time": "185400", "preLimit": "3|3|3", "columnLimit": "50|50|50"}, {"id": 10530051, "level": 53, "rank": 5, "limit": 1, "prePlot": "10530041", "skill": "682522", "talent": "0", "fightSkill": "0", "mineNeed": "299", "time": "185400", "preLimit": "3|3|3", "columnLimit": "50|50|50"}, {"id": 10530061, "level": 53, "rank": 6, "limit": 3, "prePlot": "10530051", "skill": "682504", "talent": "0", "fightSkill": "0", "mineNeed": "299", "time": "185400", "preLimit": "1|1|1", "columnLimit": "50|50|50"}, {"id": 10530062, "level": 53, "rank": 6, "limit": 3, "prePlot": "10530051", "skill": "682503", "talent": "0", "fightSkill": "0", "mineNeed": "299", "time": "185400", "preLimit": "1|1|1", "columnLimit": "50|50|50"}, {"id": 10530071, "level": 53, "rank": 7, "limit": 3, "prePlot": "10530061|10530062", "skill": "682517", "talent": "0", "fightSkill": "0", "mineNeed": "299", "time": "185400", "preLimit": "3|3|3", "columnLimit": "50|50|50"}, {"id": 10530081, "level": 53, "rank": 8, "limit": 1, "prePlot": "10530071", "skill": "682003", "talent": "0", "fightSkill": "0", "mineNeed": "299", "time": "185400", "preLimit": "3|3|3", "columnLimit": "50|50|50"}, {"id": 10530091, "level": 53, "rank": 9, "limit": 1, "prePlot": "10530081", "skill": "0", "talent": "0", "fightSkill": "681004", "mineNeed": "299", "time": "185400", "preLimit": "1|1|1", "columnLimit": "50|50|50"}, {"id": 10540011, "level": 54, "rank": 1, "limit": 3, "prePlot": "10530091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "302", "time": "187200", "preLimit": "1|1|1", "columnLimit": "50|50|50"}, {"id": 10540021, "level": 54, "rank": 2, "limit": 3, "prePlot": "10540011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "302", "time": "187200", "preLimit": "3|3|3", "columnLimit": "50|50|50"}, {"id": 10540031, "level": 54, "rank": 3, "limit": 3, "prePlot": "10540021", "skill": "682505", "talent": "0", "fightSkill": "0", "mineNeed": "302", "time": "187200", "preLimit": "3|3|3", "columnLimit": "50|50|50"}, {"id": 10540032, "level": 54, "rank": 3, "limit": 3, "prePlot": "10540021", "skill": "682507", "talent": "0", "fightSkill": "0", "mineNeed": "302", "time": "187200", "preLimit": "3|3|3", "columnLimit": "50|50|50"}, {"id": 10540041, "level": 54, "rank": 4, "limit": 3, "prePlot": "10540031|10540032", "skill": "682516", "talent": "0", "fightSkill": "0", "mineNeed": "302", "time": "187200", "preLimit": "3|3|3", "columnLimit": "50|50|50"}, {"id": 10540051, "level": 54, "rank": 5, "limit": 1, "prePlot": "10540041", "skill": "682520", "talent": "0", "fightSkill": "0", "mineNeed": "302", "time": "187200", "preLimit": "3|3|3", "columnLimit": "50|50|50"}, {"id": 10540061, "level": 54, "rank": 6, "limit": 3, "prePlot": "10540051", "skill": "682506", "talent": "0", "fightSkill": "0", "mineNeed": "302", "time": "187200", "preLimit": "1|1|1", "columnLimit": "50|50|50"}, {"id": 10540062, "level": 54, "rank": 6, "limit": 3, "prePlot": "10540051", "skill": "682508", "talent": "0", "fightSkill": "0", "mineNeed": "302", "time": "187200", "preLimit": "1|1|1", "columnLimit": "50|50|50"}, {"id": 10540071, "level": 54, "rank": 7, "limit": 3, "prePlot": "10540061|10540062", "skill": "682518", "talent": "0", "fightSkill": "0", "mineNeed": "302", "time": "187200", "preLimit": "3|3|3", "columnLimit": "50|50|50"}, {"id": 10540081, "level": 54, "rank": 8, "limit": 1, "prePlot": "10540071", "skill": "682004", "talent": "0", "fightSkill": "0", "mineNeed": "302", "time": "187200", "preLimit": "3|3|3", "columnLimit": "50|50|50"}, {"id": 10540091, "level": 54, "rank": 9, "limit": 1, "prePlot": "10540081", "skill": "682533", "talent": "0", "fightSkill": "0", "mineNeed": "302", "time": "187200", "preLimit": "1|1|1", "columnLimit": "50|50|50"}, {"id": 10550011, "level": 55, "rank": 1, "limit": 3, "prePlot": "10540091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "305", "time": "189000", "preLimit": "1|1|1", "columnLimit": "50|50|50"}, {"id": 10550021, "level": 55, "rank": 2, "limit": 3, "prePlot": "10550011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "305", "time": "189000", "preLimit": "3|3|3", "columnLimit": "50|50|50"}, {"id": 10550031, "level": 55, "rank": 3, "limit": 3, "prePlot": "10550021", "skill": "682504", "talent": "0", "fightSkill": "0", "mineNeed": "305", "time": "189000", "preLimit": "3|3|3", "columnLimit": "50|50|50"}, {"id": 10550032, "level": 55, "rank": 3, "limit": 3, "prePlot": "10550021", "skill": "682503", "talent": "0", "fightSkill": "0", "mineNeed": "305", "time": "189000", "preLimit": "3|3|3", "columnLimit": "50|50|50"}, {"id": 10550041, "level": 55, "rank": 4, "limit": 3, "prePlot": "10550031|10550032", "skill": "682515", "talent": "0", "fightSkill": "0", "mineNeed": "305", "time": "189000", "preLimit": "3|3|3", "columnLimit": "50|50|50"}, {"id": 10550051, "level": 55, "rank": 5, "limit": 1, "prePlot": "10550041", "skill": "682521", "talent": "0", "fightSkill": "0", "mineNeed": "305", "time": "189000", "preLimit": "3|3|3", "columnLimit": "50|50|50"}, {"id": 10550061, "level": 55, "rank": 6, "limit": 3, "prePlot": "10550051", "skill": "682505", "talent": "0", "fightSkill": "0", "mineNeed": "305", "time": "189000", "preLimit": "1|1|1", "columnLimit": "50|50|50"}, {"id": 10550062, "level": 55, "rank": 6, "limit": 3, "prePlot": "10550051", "skill": "682507", "talent": "0", "fightSkill": "0", "mineNeed": "305", "time": "189000", "preLimit": "1|1|1", "columnLimit": "50|50|50"}, {"id": 10550071, "level": 55, "rank": 7, "limit": 3, "prePlot": "10550061|10550062", "skill": "682517", "talent": "0", "fightSkill": "0", "mineNeed": "305", "time": "189000", "preLimit": "3|3|3", "columnLimit": "50|50|50"}, {"id": 10550081, "level": 55, "rank": 8, "limit": 1, "prePlot": "10550071", "skill": "682005", "talent": "0", "fightSkill": "0", "mineNeed": "305", "time": "189000", "preLimit": "3|3|3", "columnLimit": "50|50|50"}, {"id": 10550091, "level": 55, "rank": 9, "limit": 1, "prePlot": "10550081", "skill": "682537", "talent": "0", "fightSkill": "0", "mineNeed": "305", "time": "189000", "preLimit": "1|1|1", "columnLimit": "50|50|50"}, {"id": 10560011, "level": 56, "rank": 1, "limit": 3, "prePlot": "10550091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "308", "time": "190800", "preLimit": "1|1|1", "columnLimit": "55|55|55"}, {"id": 10560021, "level": 56, "rank": 2, "limit": 3, "prePlot": "10560011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "308", "time": "190800", "preLimit": "3|3|3", "columnLimit": "55|55|55"}, {"id": 10560031, "level": 56, "rank": 3, "limit": 3, "prePlot": "10560021", "skill": "682506", "talent": "0", "fightSkill": "0", "mineNeed": "308", "time": "190800", "preLimit": "3|3|3", "columnLimit": "55|55|55"}, {"id": 10560032, "level": 56, "rank": 3, "limit": 3, "prePlot": "10560021", "skill": "682508", "talent": "0", "fightSkill": "0", "mineNeed": "308", "time": "190800", "preLimit": "3|3|3", "columnLimit": "55|55|55"}, {"id": 10560041, "level": 56, "rank": 4, "limit": 3, "prePlot": "10560031|10560032", "skill": "682512", "talent": "0", "fightSkill": "0", "mineNeed": "308", "time": "190800", "preLimit": "3|3|3", "columnLimit": "55|55|55"}, {"id": 10560051, "level": 56, "rank": 5, "limit": 1, "prePlot": "10560041", "skill": "682519", "talent": "0", "fightSkill": "0", "mineNeed": "308", "time": "190800", "preLimit": "3|3|3", "columnLimit": "55|55|55"}, {"id": 10560061, "level": 56, "rank": 6, "limit": 3, "prePlot": "10560051", "skill": "682504", "talent": "0", "fightSkill": "0", "mineNeed": "308", "time": "190800", "preLimit": "1|1|1", "columnLimit": "55|55|55"}, {"id": 10560062, "level": 56, "rank": 6, "limit": 3, "prePlot": "10560051", "skill": "682503", "talent": "0", "fightSkill": "0", "mineNeed": "308", "time": "190800", "preLimit": "1|1|1", "columnLimit": "55|55|55"}, {"id": 10560071, "level": 56, "rank": 7, "limit": 3, "prePlot": "10560061|10560062", "skill": "682518", "talent": "0", "fightSkill": "0", "mineNeed": "308", "time": "190800", "preLimit": "3|3|3", "columnLimit": "55|55|55"}, {"id": 10560081, "level": 56, "rank": 8, "limit": 1, "prePlot": "10560071", "skill": "682006", "talent": "0", "fightSkill": "0", "mineNeed": "308", "time": "190800", "preLimit": "3|3|3", "columnLimit": "55|55|55"}, {"id": 10560091, "level": 56, "rank": 9, "limit": 1, "prePlot": "10560081", "skill": "682534", "talent": "0", "fightSkill": "0", "mineNeed": "308", "time": "190800", "preLimit": "1|1|1", "columnLimit": "55|55|55"}, {"id": 10570011, "level": 57, "rank": 1, "limit": 3, "prePlot": "10560091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "311", "time": "192600", "preLimit": "1|1|1", "columnLimit": "55|55|55"}, {"id": 10570021, "level": 57, "rank": 2, "limit": 3, "prePlot": "10570011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "311", "time": "192600", "preLimit": "3|3|3", "columnLimit": "55|55|55"}, {"id": 10570031, "level": 57, "rank": 3, "limit": 3, "prePlot": "10570021", "skill": "682505", "talent": "0", "fightSkill": "0", "mineNeed": "311", "time": "192600", "preLimit": "3|3|3", "columnLimit": "55|55|55"}, {"id": 10570032, "level": 57, "rank": 3, "limit": 3, "prePlot": "10570021", "skill": "682507", "talent": "0", "fightSkill": "0", "mineNeed": "311", "time": "192600", "preLimit": "3|3|3", "columnLimit": "55|55|55"}, {"id": 10570041, "level": 57, "rank": 4, "limit": 3, "prePlot": "10570031|10570032", "skill": "682513", "talent": "0", "fightSkill": "0", "mineNeed": "311", "time": "192600", "preLimit": "3|3|3", "columnLimit": "55|55|55"}, {"id": 10570051, "level": 57, "rank": 5, "limit": 1, "prePlot": "10570041", "skill": "682522", "talent": "0", "fightSkill": "0", "mineNeed": "311", "time": "192600", "preLimit": "3|3|3", "columnLimit": "55|55|55"}, {"id": 10570061, "level": 57, "rank": 6, "limit": 3, "prePlot": "10570051", "skill": "682506", "talent": "0", "fightSkill": "0", "mineNeed": "311", "time": "192600", "preLimit": "1|1|1", "columnLimit": "55|55|55"}, {"id": 10570062, "level": 57, "rank": 6, "limit": 3, "prePlot": "10570051", "skill": "682508", "talent": "0", "fightSkill": "0", "mineNeed": "311", "time": "192600", "preLimit": "1|1|1", "columnLimit": "55|55|55"}, {"id": 10570071, "level": 57, "rank": 7, "limit": 3, "prePlot": "10570061|10570062", "skill": "682517", "talent": "0", "fightSkill": "0", "mineNeed": "311", "time": "192600", "preLimit": "3|3|3", "columnLimit": "55|55|55"}, {"id": 10570081, "level": 57, "rank": 8, "limit": 1, "prePlot": "10570071", "skill": "682007", "talent": "0", "fightSkill": "0", "mineNeed": "311", "time": "192600", "preLimit": "3|3|3", "columnLimit": "55|55|55"}, {"id": 10570091, "level": 57, "rank": 9, "limit": 1, "prePlot": "10570081", "skill": "0", "talent": "0", "fightSkill": "681005", "mineNeed": "311", "time": "192600", "preLimit": "1|1|1", "columnLimit": "55|55|55"}, {"id": 10580011, "level": 58, "rank": 1, "limit": 3, "prePlot": "10570091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "314", "time": "194400", "preLimit": "1|1|1", "columnLimit": "55|55|55"}, {"id": 10580021, "level": 58, "rank": 2, "limit": 3, "prePlot": "10580011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "314", "time": "194400", "preLimit": "3|3|3", "columnLimit": "55|55|55"}, {"id": 10580031, "level": 58, "rank": 3, "limit": 3, "prePlot": "10580021", "skill": "682504", "talent": "0", "fightSkill": "0", "mineNeed": "314", "time": "194400", "preLimit": "3|3|3", "columnLimit": "55|55|55"}, {"id": 10580032, "level": 58, "rank": 3, "limit": 3, "prePlot": "10580021", "skill": "682503", "talent": "0", "fightSkill": "0", "mineNeed": "314", "time": "194400", "preLimit": "3|3|3", "columnLimit": "55|55|55"}, {"id": 10580041, "level": 58, "rank": 4, "limit": 3, "prePlot": "10580031|10580032", "skill": "682510", "talent": "0", "fightSkill": "0", "mineNeed": "314", "time": "194400", "preLimit": "3|3|3", "columnLimit": "55|55|55"}, {"id": 10580051, "level": 58, "rank": 5, "limit": 1, "prePlot": "10580041", "skill": "682520", "talent": "0", "fightSkill": "0", "mineNeed": "314", "time": "194400", "preLimit": "3|3|3", "columnLimit": "55|55|55"}, {"id": 10580061, "level": 58, "rank": 6, "limit": 3, "prePlot": "10580051", "skill": "682505", "talent": "0", "fightSkill": "0", "mineNeed": "314", "time": "194400", "preLimit": "1|1|1", "columnLimit": "55|55|55"}, {"id": 10580062, "level": 58, "rank": 6, "limit": 3, "prePlot": "10580051", "skill": "682507", "talent": "0", "fightSkill": "0", "mineNeed": "314", "time": "194400", "preLimit": "1|1|1", "columnLimit": "55|55|55"}, {"id": 10580071, "level": 58, "rank": 7, "limit": 3, "prePlot": "10580061|10580062", "skill": "682518", "talent": "0", "fightSkill": "0", "mineNeed": "314", "time": "194400", "preLimit": "3|3|3", "columnLimit": "55|55|55"}, {"id": 10580081, "level": 58, "rank": 8, "limit": 1, "prePlot": "10580071", "skill": "682008", "talent": "0", "fightSkill": "0", "mineNeed": "314", "time": "194400", "preLimit": "3|3|3", "columnLimit": "55|55|55"}, {"id": 10580091, "level": 58, "rank": 9, "limit": 1, "prePlot": "10580081", "skill": "682533", "talent": "0", "fightSkill": "0", "mineNeed": "314", "time": "194400", "preLimit": "1|1|1", "columnLimit": "55|55|55"}, {"id": 10590011, "level": 59, "rank": 1, "limit": 3, "prePlot": "10580091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "317", "time": "196200", "preLimit": "1|1|1", "columnLimit": "55|55|55"}, {"id": 10590021, "level": 59, "rank": 2, "limit": 3, "prePlot": "10590011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "317", "time": "196200", "preLimit": "3|3|3", "columnLimit": "55|55|55"}, {"id": 10590031, "level": 59, "rank": 3, "limit": 3, "prePlot": "10590021", "skill": "682506", "talent": "0", "fightSkill": "0", "mineNeed": "317", "time": "196200", "preLimit": "3|3|3", "columnLimit": "55|55|55"}, {"id": 10590032, "level": 59, "rank": 3, "limit": 3, "prePlot": "10590021", "skill": "682508", "talent": "0", "fightSkill": "0", "mineNeed": "317", "time": "196200", "preLimit": "3|3|3", "columnLimit": "55|55|55"}, {"id": 10590041, "level": 59, "rank": 4, "limit": 3, "prePlot": "10590031|10590032", "skill": "682511", "talent": "0", "fightSkill": "0", "mineNeed": "317", "time": "196200", "preLimit": "3|3|3", "columnLimit": "55|55|55"}, {"id": 10590051, "level": 59, "rank": 5, "limit": 1, "prePlot": "10590041", "skill": "682521", "talent": "0", "fightSkill": "0", "mineNeed": "317", "time": "196200", "preLimit": "3|3|3", "columnLimit": "55|55|55"}, {"id": 10590061, "level": 59, "rank": 6, "limit": 3, "prePlot": "10590051", "skill": "682504", "talent": "0", "fightSkill": "0", "mineNeed": "317", "time": "196200", "preLimit": "1|1|1", "columnLimit": "55|55|55"}, {"id": 10590062, "level": 59, "rank": 6, "limit": 3, "prePlot": "10590051", "skill": "682503", "talent": "0", "fightSkill": "0", "mineNeed": "317", "time": "196200", "preLimit": "1|1|1", "columnLimit": "55|55|55"}, {"id": 10590071, "level": 59, "rank": 7, "limit": 3, "prePlot": "10590061|10590062", "skill": "682517", "talent": "0", "fightSkill": "0", "mineNeed": "317", "time": "196200", "preLimit": "3|3|3", "columnLimit": "55|55|55"}, {"id": 10590081, "level": 59, "rank": 8, "limit": 1, "prePlot": "10590071", "skill": "682009", "talent": "0", "fightSkill": "0", "mineNeed": "317", "time": "196200", "preLimit": "3|3|3", "columnLimit": "55|55|55"}, {"id": 10590091, "level": 59, "rank": 9, "limit": 1, "prePlot": "10590081", "skill": "682536", "talent": "0", "fightSkill": "0", "mineNeed": "317", "time": "196200", "preLimit": "1|1|1", "columnLimit": "55|55|55"}, {"id": 10600011, "level": 60, "rank": 1, "limit": 3, "prePlot": "10590091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "320", "time": "198000", "preLimit": "1|1|1", "columnLimit": "55|55|55"}, {"id": 10600021, "level": 60, "rank": 2, "limit": 3, "prePlot": "10600011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "320", "time": "198000", "preLimit": "3|3|3", "columnLimit": "55|55|55"}, {"id": 10600031, "level": 60, "rank": 3, "limit": 3, "prePlot": "10600021", "skill": "682505", "talent": "0", "fightSkill": "0", "mineNeed": "320", "time": "198000", "preLimit": "3|3|3", "columnLimit": "55|55|55"}, {"id": 10600032, "level": 60, "rank": 3, "limit": 3, "prePlot": "10600021", "skill": "682507", "talent": "0", "fightSkill": "0", "mineNeed": "320", "time": "198000", "preLimit": "3|3|3", "columnLimit": "55|55|55"}, {"id": 10600041, "level": 60, "rank": 4, "limit": 3, "prePlot": "10600031|10600032", "skill": "682514", "talent": "0", "fightSkill": "0", "mineNeed": "320", "time": "198000", "preLimit": "3|3|3", "columnLimit": "55|55|55"}, {"id": 10600051, "level": 60, "rank": 5, "limit": 1, "prePlot": "10600041", "skill": "682519", "talent": "0", "fightSkill": "0", "mineNeed": "320", "time": "198000", "preLimit": "3|3|3", "columnLimit": "55|55|55"}, {"id": 10600061, "level": 60, "rank": 6, "limit": 3, "prePlot": "10600051", "skill": "682506", "talent": "0", "fightSkill": "0", "mineNeed": "320", "time": "198000", "preLimit": "1|1|1", "columnLimit": "55|55|55"}, {"id": 10600062, "level": 60, "rank": 6, "limit": 3, "prePlot": "10600051", "skill": "682508", "talent": "0", "fightSkill": "0", "mineNeed": "320", "time": "198000", "preLimit": "1|1|1", "columnLimit": "55|55|55"}, {"id": 10600071, "level": 60, "rank": 7, "limit": 3, "prePlot": "10600061|10600062", "skill": "682518", "talent": "0", "fightSkill": "0", "mineNeed": "320", "time": "198000", "preLimit": "3|3|3", "columnLimit": "55|55|55"}, {"id": 10600081, "level": 60, "rank": 8, "limit": 1, "prePlot": "10600071", "skill": "682010", "talent": "0", "fightSkill": "0", "mineNeed": "320", "time": "198000", "preLimit": "3|3|3", "columnLimit": "55|55|55"}, {"id": 10600091, "level": 60, "rank": 9, "limit": 1, "prePlot": "10600081", "skill": "682535", "talent": "0", "fightSkill": "0", "mineNeed": "320", "time": "198000", "preLimit": "1|1|1", "columnLimit": "55|55|55"}, {"id": 10610011, "level": 61, "rank": 1, "limit": 3, "prePlot": "10600091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "323", "time": "199800", "preLimit": "1|1|1", "columnLimit": "60|60|60"}, {"id": 10610021, "level": 61, "rank": 2, "limit": 3, "prePlot": "10610011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "323", "time": "199800", "preLimit": "3|3|3", "columnLimit": "60|60|60"}, {"id": 10610031, "level": 61, "rank": 3, "limit": 3, "prePlot": "10610021", "skill": "682504", "talent": "0", "fightSkill": "0", "mineNeed": "323", "time": "199800", "preLimit": "3|3|3", "columnLimit": "60|60|60"}, {"id": 10610032, "level": 61, "rank": 3, "limit": 3, "prePlot": "10610021", "skill": "682503", "talent": "0", "fightSkill": "0", "mineNeed": "323", "time": "199800", "preLimit": "3|3|3", "columnLimit": "60|60|60"}, {"id": 10610041, "level": 61, "rank": 4, "limit": 3, "prePlot": "10610031|10610032", "skill": "682509", "talent": "0", "fightSkill": "0", "mineNeed": "323", "time": "199800", "preLimit": "3|3|3", "columnLimit": "60|60|60"}, {"id": 10610051, "level": 61, "rank": 5, "limit": 1, "prePlot": "10610041", "skill": "682522", "talent": "0", "fightSkill": "0", "mineNeed": "323", "time": "199800", "preLimit": "3|3|3", "columnLimit": "60|60|60"}, {"id": 10610061, "level": 61, "rank": 6, "limit": 3, "prePlot": "10610051", "skill": "682505", "talent": "0", "fightSkill": "0", "mineNeed": "323", "time": "199800", "preLimit": "1|1|1", "columnLimit": "60|60|60"}, {"id": 10610062, "level": 61, "rank": 6, "limit": 3, "prePlot": "10610051", "skill": "682507", "talent": "0", "fightSkill": "0", "mineNeed": "323", "time": "199800", "preLimit": "1|1|1", "columnLimit": "60|60|60"}, {"id": 10610071, "level": 61, "rank": 7, "limit": 3, "prePlot": "10610061|10610062", "skill": "682517", "talent": "0", "fightSkill": "0", "mineNeed": "323", "time": "199800", "preLimit": "3|3|3", "columnLimit": "60|60|60"}, {"id": 10610081, "level": 61, "rank": 8, "limit": 1, "prePlot": "10610071", "skill": "682001", "talent": "0", "fightSkill": "0", "mineNeed": "323", "time": "199800", "preLimit": "3|3|3", "columnLimit": "60|60|60"}, {"id": 10610091, "level": 61, "rank": 9, "limit": 1, "prePlot": "10610081", "skill": "0", "talent": "0", "fightSkill": "681001", "mineNeed": "323", "time": "199800", "preLimit": "1|1|1", "columnLimit": "60|60|60"}, {"id": 10620011, "level": 62, "rank": 1, "limit": 3, "prePlot": "10610091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "326", "time": "201600", "preLimit": "1|1|1", "columnLimit": "60|60|60"}, {"id": 10620021, "level": 62, "rank": 2, "limit": 3, "prePlot": "10620011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "326", "time": "201600", "preLimit": "3|3|3", "columnLimit": "60|60|60"}, {"id": 10620031, "level": 62, "rank": 3, "limit": 3, "prePlot": "10620021", "skill": "682506", "talent": "0", "fightSkill": "0", "mineNeed": "326", "time": "201600", "preLimit": "3|3|3", "columnLimit": "60|60|60"}, {"id": 10620032, "level": 62, "rank": 3, "limit": 3, "prePlot": "10620021", "skill": "682508", "talent": "0", "fightSkill": "0", "mineNeed": "326", "time": "201600", "preLimit": "3|3|3", "columnLimit": "60|60|60"}, {"id": 10620041, "level": 62, "rank": 4, "limit": 3, "prePlot": "10620031|10620032", "skill": "682516", "talent": "0", "fightSkill": "0", "mineNeed": "326", "time": "201600", "preLimit": "3|3|3", "columnLimit": "60|60|60"}, {"id": 10620051, "level": 62, "rank": 5, "limit": 1, "prePlot": "10620041", "skill": "682520", "talent": "0", "fightSkill": "0", "mineNeed": "326", "time": "201600", "preLimit": "3|3|3", "columnLimit": "60|60|60"}, {"id": 10620061, "level": 62, "rank": 6, "limit": 3, "prePlot": "10620051", "skill": "682504", "talent": "0", "fightSkill": "0", "mineNeed": "326", "time": "201600", "preLimit": "1|1|1", "columnLimit": "60|60|60"}, {"id": 10620062, "level": 62, "rank": 6, "limit": 3, "prePlot": "10620051", "skill": "682503", "talent": "0", "fightSkill": "0", "mineNeed": "326", "time": "201600", "preLimit": "1|1|1", "columnLimit": "60|60|60"}, {"id": 10620071, "level": 62, "rank": 7, "limit": 3, "prePlot": "10620061|10620062", "skill": "682518", "talent": "0", "fightSkill": "0", "mineNeed": "326", "time": "201600", "preLimit": "3|3|3", "columnLimit": "60|60|60"}, {"id": 10620081, "level": 62, "rank": 8, "limit": 1, "prePlot": "10620071", "skill": "682002", "talent": "0", "fightSkill": "0", "mineNeed": "326", "time": "201600", "preLimit": "3|3|3", "columnLimit": "60|60|60"}, {"id": 10620091, "level": 62, "rank": 9, "limit": 1, "prePlot": "10620081", "skill": "682533", "talent": "0", "fightSkill": "0", "mineNeed": "326", "time": "201600", "preLimit": "1|1|1", "columnLimit": "60|60|60"}, {"id": 10630011, "level": 63, "rank": 1, "limit": 3, "prePlot": "10620091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "329", "time": "203400", "preLimit": "1|1|1", "columnLimit": "60|60|60"}, {"id": 10630021, "level": 63, "rank": 2, "limit": 3, "prePlot": "10630011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "329", "time": "203400", "preLimit": "3|3|3", "columnLimit": "60|60|60"}, {"id": 10630031, "level": 63, "rank": 3, "limit": 3, "prePlot": "10630021", "skill": "682505", "talent": "0", "fightSkill": "0", "mineNeed": "329", "time": "203400", "preLimit": "3|3|3", "columnLimit": "60|60|60"}, {"id": 10630032, "level": 63, "rank": 3, "limit": 3, "prePlot": "10630021", "skill": "682507", "talent": "0", "fightSkill": "0", "mineNeed": "329", "time": "203400", "preLimit": "3|3|3", "columnLimit": "60|60|60"}, {"id": 10630041, "level": 63, "rank": 4, "limit": 3, "prePlot": "10630031|10630032", "skill": "682515", "talent": "0", "fightSkill": "0", "mineNeed": "329", "time": "203400", "preLimit": "3|3|3", "columnLimit": "60|60|60"}, {"id": 10630051, "level": 63, "rank": 5, "limit": 1, "prePlot": "10630041", "skill": "682521", "talent": "0", "fightSkill": "0", "mineNeed": "329", "time": "203400", "preLimit": "3|3|3", "columnLimit": "60|60|60"}, {"id": 10630061, "level": 63, "rank": 6, "limit": 3, "prePlot": "10630051", "skill": "682506", "talent": "0", "fightSkill": "0", "mineNeed": "329", "time": "203400", "preLimit": "1|1|1", "columnLimit": "60|60|60"}, {"id": 10630062, "level": 63, "rank": 6, "limit": 3, "prePlot": "10630051", "skill": "682508", "talent": "0", "fightSkill": "0", "mineNeed": "329", "time": "203400", "preLimit": "1|1|1", "columnLimit": "60|60|60"}, {"id": 10630071, "level": 63, "rank": 7, "limit": 3, "prePlot": "10630061|10630062", "skill": "682517", "talent": "0", "fightSkill": "0", "mineNeed": "329", "time": "203400", "preLimit": "3|3|3", "columnLimit": "60|60|60"}, {"id": 10630081, "level": 63, "rank": 8, "limit": 1, "prePlot": "10630071", "skill": "682003", "talent": "0", "fightSkill": "0", "mineNeed": "329", "time": "203400", "preLimit": "3|3|3", "columnLimit": "60|60|60"}, {"id": 10630091, "level": 63, "rank": 9, "limit": 1, "prePlot": "10630081", "skill": "682537", "talent": "0", "fightSkill": "0", "mineNeed": "329", "time": "203400", "preLimit": "1|1|1", "columnLimit": "60|60|60"}, {"id": 10640011, "level": 64, "rank": 1, "limit": 3, "prePlot": "10630091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "332", "time": "205200", "preLimit": "1|1|1", "columnLimit": "60|60|60"}, {"id": 10640021, "level": 64, "rank": 2, "limit": 3, "prePlot": "10640011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "332", "time": "205200", "preLimit": "3|3|3", "columnLimit": "60|60|60"}, {"id": 10640031, "level": 64, "rank": 3, "limit": 3, "prePlot": "10640021", "skill": "682504", "talent": "0", "fightSkill": "0", "mineNeed": "332", "time": "205200", "preLimit": "3|3|3", "columnLimit": "60|60|60"}, {"id": 10640032, "level": 64, "rank": 3, "limit": 3, "prePlot": "10640021", "skill": "682503", "talent": "0", "fightSkill": "0", "mineNeed": "332", "time": "205200", "preLimit": "3|3|3", "columnLimit": "60|60|60"}, {"id": 10640041, "level": 64, "rank": 4, "limit": 3, "prePlot": "10640031|10640032", "skill": "682512", "talent": "0", "fightSkill": "0", "mineNeed": "332", "time": "205200", "preLimit": "3|3|3", "columnLimit": "60|60|60"}, {"id": 10640051, "level": 64, "rank": 5, "limit": 1, "prePlot": "10640041", "skill": "682519", "talent": "0", "fightSkill": "0", "mineNeed": "332", "time": "205200", "preLimit": "3|3|3", "columnLimit": "60|60|60"}, {"id": 10640061, "level": 64, "rank": 6, "limit": 3, "prePlot": "10640051", "skill": "682505", "talent": "0", "fightSkill": "0", "mineNeed": "332", "time": "205200", "preLimit": "1|1|1", "columnLimit": "60|60|60"}, {"id": 10640062, "level": 64, "rank": 6, "limit": 3, "prePlot": "10640051", "skill": "682507", "talent": "0", "fightSkill": "0", "mineNeed": "332", "time": "205200", "preLimit": "1|1|1", "columnLimit": "60|60|60"}, {"id": 10640071, "level": 64, "rank": 7, "limit": 3, "prePlot": "10640061|10640062", "skill": "682518", "talent": "0", "fightSkill": "0", "mineNeed": "332", "time": "205200", "preLimit": "3|3|3", "columnLimit": "60|60|60"}, {"id": 10640081, "level": 64, "rank": 8, "limit": 1, "prePlot": "10640071", "skill": "682004", "talent": "0", "fightSkill": "0", "mineNeed": "332", "time": "205200", "preLimit": "3|3|3", "columnLimit": "60|60|60"}, {"id": 10640091, "level": 64, "rank": 9, "limit": 1, "prePlot": "10640081", "skill": "682535", "talent": "0", "fightSkill": "0", "mineNeed": "332", "time": "205200", "preLimit": "1|1|1", "columnLimit": "60|60|60"}, {"id": 10650011, "level": 65, "rank": 1, "limit": 3, "prePlot": "10640091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "335", "time": "207000", "preLimit": "1|1|1", "columnLimit": "60|60|60"}, {"id": 10650021, "level": 65, "rank": 2, "limit": 3, "prePlot": "10650011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "335", "time": "207000", "preLimit": "3|3|3", "columnLimit": "60|60|60"}, {"id": 10650031, "level": 65, "rank": 3, "limit": 3, "prePlot": "10650021", "skill": "682506", "talent": "0", "fightSkill": "0", "mineNeed": "335", "time": "207000", "preLimit": "3|3|3", "columnLimit": "60|60|60"}, {"id": 10650032, "level": 65, "rank": 3, "limit": 3, "prePlot": "10650021", "skill": "682508", "talent": "0", "fightSkill": "0", "mineNeed": "335", "time": "207000", "preLimit": "3|3|3", "columnLimit": "60|60|60"}, {"id": 10650041, "level": 65, "rank": 4, "limit": 3, "prePlot": "10650031|10650032", "skill": "682513", "talent": "0", "fightSkill": "0", "mineNeed": "335", "time": "207000", "preLimit": "3|3|3", "columnLimit": "60|60|60"}, {"id": 10650051, "level": 65, "rank": 5, "limit": 1, "prePlot": "10650041", "skill": "682522", "talent": "0", "fightSkill": "0", "mineNeed": "335", "time": "207000", "preLimit": "3|3|3", "columnLimit": "60|60|60"}, {"id": 10650061, "level": 65, "rank": 6, "limit": 3, "prePlot": "10650051", "skill": "682504", "talent": "0", "fightSkill": "0", "mineNeed": "335", "time": "207000", "preLimit": "1|1|1", "columnLimit": "60|60|60"}, {"id": 10650062, "level": 65, "rank": 6, "limit": 3, "prePlot": "10650051", "skill": "682503", "talent": "0", "fightSkill": "0", "mineNeed": "335", "time": "207000", "preLimit": "1|1|1", "columnLimit": "60|60|60"}, {"id": 10650071, "level": 65, "rank": 7, "limit": 3, "prePlot": "10650061|10650062", "skill": "682517", "talent": "0", "fightSkill": "0", "mineNeed": "335", "time": "207000", "preLimit": "3|3|3", "columnLimit": "60|60|60"}, {"id": 10650081, "level": 65, "rank": 8, "limit": 1, "prePlot": "10650071", "skill": "682005", "talent": "0", "fightSkill": "0", "mineNeed": "335", "time": "207000", "preLimit": "3|3|3", "columnLimit": "60|60|60"}, {"id": 10650091, "level": 65, "rank": 9, "limit": 1, "prePlot": "10650081", "skill": "0", "talent": "0", "fightSkill": "681002", "mineNeed": "335", "time": "207000", "preLimit": "1|1|1", "columnLimit": "60|60|60"}, {"id": 10660011, "level": 66, "rank": 1, "limit": 3, "prePlot": "10650091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "338", "time": "208800", "preLimit": "1|1|1", "columnLimit": "65|65|65"}, {"id": 10660021, "level": 66, "rank": 2, "limit": 3, "prePlot": "10660011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "338", "time": "208800", "preLimit": "3|3|3", "columnLimit": "65|65|65"}, {"id": 10660031, "level": 66, "rank": 3, "limit": 3, "prePlot": "10660021", "skill": "682505", "talent": "0", "fightSkill": "0", "mineNeed": "338", "time": "208800", "preLimit": "3|3|3", "columnLimit": "65|65|65"}, {"id": 10660032, "level": 66, "rank": 3, "limit": 3, "prePlot": "10660021", "skill": "682507", "talent": "0", "fightSkill": "0", "mineNeed": "338", "time": "208800", "preLimit": "3|3|3", "columnLimit": "65|65|65"}, {"id": 10660041, "level": 66, "rank": 4, "limit": 3, "prePlot": "10660031|10660032", "skill": "682510", "talent": "0", "fightSkill": "0", "mineNeed": "338", "time": "208800", "preLimit": "3|3|3", "columnLimit": "65|65|65"}, {"id": 10660051, "level": 66, "rank": 5, "limit": 1, "prePlot": "10660041", "skill": "682520", "talent": "0", "fightSkill": "0", "mineNeed": "338", "time": "208800", "preLimit": "3|3|3", "columnLimit": "65|65|65"}, {"id": 10660061, "level": 66, "rank": 6, "limit": 3, "prePlot": "10660051", "skill": "682506", "talent": "0", "fightSkill": "0", "mineNeed": "338", "time": "208800", "preLimit": "1|1|1", "columnLimit": "65|65|65"}, {"id": 10660062, "level": 66, "rank": 6, "limit": 3, "prePlot": "10660051", "skill": "682508", "talent": "0", "fightSkill": "0", "mineNeed": "338", "time": "208800", "preLimit": "1|1|1", "columnLimit": "65|65|65"}, {"id": 10660071, "level": 66, "rank": 7, "limit": 3, "prePlot": "10660061|10660062", "skill": "682518", "talent": "0", "fightSkill": "0", "mineNeed": "338", "time": "208800", "preLimit": "3|3|3", "columnLimit": "65|65|65"}, {"id": 10660081, "level": 66, "rank": 8, "limit": 1, "prePlot": "10660071", "skill": "682006", "talent": "0", "fightSkill": "0", "mineNeed": "338", "time": "208800", "preLimit": "3|3|3", "columnLimit": "65|65|65"}, {"id": 10660091, "level": 66, "rank": 9, "limit": 1, "prePlot": "10660081", "skill": "682533", "talent": "0", "fightSkill": "0", "mineNeed": "338", "time": "208800", "preLimit": "1|1|1", "columnLimit": "65|65|65"}, {"id": 10670011, "level": 67, "rank": 1, "limit": 3, "prePlot": "10660091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "341", "time": "210600", "preLimit": "1|1|1", "columnLimit": "65|65|65"}, {"id": 10670021, "level": 67, "rank": 2, "limit": 3, "prePlot": "10670011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "341", "time": "210600", "preLimit": "3|3|3", "columnLimit": "65|65|65"}, {"id": 10670031, "level": 67, "rank": 3, "limit": 3, "prePlot": "10670021", "skill": "682504", "talent": "0", "fightSkill": "0", "mineNeed": "341", "time": "210600", "preLimit": "3|3|3", "columnLimit": "65|65|65"}, {"id": 10670032, "level": 67, "rank": 3, "limit": 3, "prePlot": "10670021", "skill": "682503", "talent": "0", "fightSkill": "0", "mineNeed": "341", "time": "210600", "preLimit": "3|3|3", "columnLimit": "65|65|65"}, {"id": 10670041, "level": 67, "rank": 4, "limit": 3, "prePlot": "10670031|10670032", "skill": "682511", "talent": "0", "fightSkill": "0", "mineNeed": "341", "time": "210600", "preLimit": "3|3|3", "columnLimit": "65|65|65"}, {"id": 10670051, "level": 67, "rank": 5, "limit": 1, "prePlot": "10670041", "skill": "682521", "talent": "0", "fightSkill": "0", "mineNeed": "341", "time": "210600", "preLimit": "3|3|3", "columnLimit": "65|65|65"}, {"id": 10670061, "level": 67, "rank": 6, "limit": 3, "prePlot": "10670051", "skill": "682505", "talent": "0", "fightSkill": "0", "mineNeed": "341", "time": "210600", "preLimit": "1|1|1", "columnLimit": "65|65|65"}, {"id": 10670062, "level": 67, "rank": 6, "limit": 3, "prePlot": "10670051", "skill": "682507", "talent": "0", "fightSkill": "0", "mineNeed": "341", "time": "210600", "preLimit": "1|1|1", "columnLimit": "65|65|65"}, {"id": 10670071, "level": 67, "rank": 7, "limit": 3, "prePlot": "10670061|10670062", "skill": "682517", "talent": "0", "fightSkill": "0", "mineNeed": "341", "time": "210600", "preLimit": "3|3|3", "columnLimit": "65|65|65"}, {"id": 10670081, "level": 67, "rank": 8, "limit": 1, "prePlot": "10670071", "skill": "682007", "talent": "0", "fightSkill": "0", "mineNeed": "341", "time": "210600", "preLimit": "3|3|3", "columnLimit": "65|65|65"}, {"id": 10670091, "level": 67, "rank": 9, "limit": 1, "prePlot": "10670081", "skill": "682536", "talent": "0", "fightSkill": "0", "mineNeed": "341", "time": "210600", "preLimit": "1|1|1", "columnLimit": "65|65|65"}, {"id": 10680011, "level": 68, "rank": 1, "limit": 3, "prePlot": "10670091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "344", "time": "212400", "preLimit": "1|1|1", "columnLimit": "65|65|65"}, {"id": 10680021, "level": 68, "rank": 2, "limit": 3, "prePlot": "10680011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "344", "time": "212400", "preLimit": "3|3|3", "columnLimit": "65|65|65"}, {"id": 10680031, "level": 68, "rank": 3, "limit": 3, "prePlot": "10680021", "skill": "682506", "talent": "0", "fightSkill": "0", "mineNeed": "344", "time": "212400", "preLimit": "3|3|3", "columnLimit": "65|65|65"}, {"id": 10680032, "level": 68, "rank": 3, "limit": 3, "prePlot": "10680021", "skill": "682508", "talent": "0", "fightSkill": "0", "mineNeed": "344", "time": "212400", "preLimit": "3|3|3", "columnLimit": "65|65|65"}, {"id": 10680041, "level": 68, "rank": 4, "limit": 3, "prePlot": "10680031|10680032", "skill": "682514", "talent": "0", "fightSkill": "0", "mineNeed": "344", "time": "212400", "preLimit": "3|3|3", "columnLimit": "65|65|65"}, {"id": 10680051, "level": 68, "rank": 5, "limit": 1, "prePlot": "10680041", "skill": "682519", "talent": "0", "fightSkill": "0", "mineNeed": "344", "time": "212400", "preLimit": "3|3|3", "columnLimit": "65|65|65"}, {"id": 10680061, "level": 68, "rank": 6, "limit": 3, "prePlot": "10680051", "skill": "682504", "talent": "0", "fightSkill": "0", "mineNeed": "344", "time": "212400", "preLimit": "1|1|1", "columnLimit": "65|65|65"}, {"id": 10680062, "level": 68, "rank": 6, "limit": 3, "prePlot": "10680051", "skill": "682503", "talent": "0", "fightSkill": "0", "mineNeed": "344", "time": "212400", "preLimit": "1|1|1", "columnLimit": "65|65|65"}, {"id": 10680071, "level": 68, "rank": 7, "limit": 3, "prePlot": "10680061|10680062", "skill": "682518", "talent": "0", "fightSkill": "0", "mineNeed": "344", "time": "212400", "preLimit": "3|3|3", "columnLimit": "65|65|65"}, {"id": 10680081, "level": 68, "rank": 8, "limit": 1, "prePlot": "10680071", "skill": "682008", "talent": "0", "fightSkill": "0", "mineNeed": "344", "time": "212400", "preLimit": "3|3|3", "columnLimit": "65|65|65"}, {"id": 10680091, "level": 68, "rank": 9, "limit": 1, "prePlot": "10680081", "skill": "682534", "talent": "0", "fightSkill": "0", "mineNeed": "344", "time": "212400", "preLimit": "1|1|1", "columnLimit": "65|65|65"}, {"id": 10690011, "level": 69, "rank": 1, "limit": 3, "prePlot": "10680091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "347", "time": "214200", "preLimit": "1|1|1", "columnLimit": "65|65|65"}, {"id": 10690021, "level": 69, "rank": 2, "limit": 3, "prePlot": "10690011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "347", "time": "214200", "preLimit": "3|3|3", "columnLimit": "65|65|65"}, {"id": 10690031, "level": 69, "rank": 3, "limit": 3, "prePlot": "10690021", "skill": "682505", "talent": "0", "fightSkill": "0", "mineNeed": "347", "time": "214200", "preLimit": "3|3|3", "columnLimit": "65|65|65"}, {"id": 10690032, "level": 69, "rank": 3, "limit": 3, "prePlot": "10690021", "skill": "682507", "talent": "0", "fightSkill": "0", "mineNeed": "347", "time": "214200", "preLimit": "3|3|3", "columnLimit": "65|65|65"}, {"id": 10690041, "level": 69, "rank": 4, "limit": 3, "prePlot": "10690031|10690032", "skill": "682509", "talent": "0", "fightSkill": "0", "mineNeed": "347", "time": "214200", "preLimit": "3|3|3", "columnLimit": "65|65|65"}, {"id": 10690051, "level": 69, "rank": 5, "limit": 1, "prePlot": "10690041", "skill": "682522", "talent": "0", "fightSkill": "0", "mineNeed": "347", "time": "214200", "preLimit": "3|3|3", "columnLimit": "65|65|65"}, {"id": 10690061, "level": 69, "rank": 6, "limit": 3, "prePlot": "10690051", "skill": "682506", "talent": "0", "fightSkill": "0", "mineNeed": "347", "time": "214200", "preLimit": "1|1|1", "columnLimit": "65|65|65"}, {"id": 10690062, "level": 69, "rank": 6, "limit": 3, "prePlot": "10690051", "skill": "682508", "talent": "0", "fightSkill": "0", "mineNeed": "347", "time": "214200", "preLimit": "1|1|1", "columnLimit": "65|65|65"}, {"id": 10690071, "level": 69, "rank": 7, "limit": 3, "prePlot": "10690061|10690062", "skill": "682517", "talent": "0", "fightSkill": "0", "mineNeed": "347", "time": "214200", "preLimit": "3|3|3", "columnLimit": "65|65|65"}, {"id": 10690081, "level": 69, "rank": 8, "limit": 1, "prePlot": "10690071", "skill": "682009", "talent": "0", "fightSkill": "0", "mineNeed": "347", "time": "214200", "preLimit": "3|3|3", "columnLimit": "65|65|65"}, {"id": 10690091, "level": 69, "rank": 9, "limit": 1, "prePlot": "10690081", "skill": "0", "talent": "0", "fightSkill": "681003", "mineNeed": "347", "time": "214200", "preLimit": "1|1|1", "columnLimit": "65|65|65"}, {"id": 10700011, "level": 70, "rank": 1, "limit": 3, "prePlot": "10690091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "350", "time": "216000", "preLimit": "1|1|1", "columnLimit": "65|65|65"}, {"id": 10700021, "level": 70, "rank": 2, "limit": 3, "prePlot": "10700011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "350", "time": "216000", "preLimit": "3|3|3", "columnLimit": "65|65|65"}, {"id": 10700031, "level": 70, "rank": 3, "limit": 3, "prePlot": "10700021", "skill": "682504", "talent": "0", "fightSkill": "0", "mineNeed": "350", "time": "216000", "preLimit": "3|3|3", "columnLimit": "65|65|65"}, {"id": 10700032, "level": 70, "rank": 3, "limit": 3, "prePlot": "10700021", "skill": "682503", "talent": "0", "fightSkill": "0", "mineNeed": "350", "time": "216000", "preLimit": "3|3|3", "columnLimit": "65|65|65"}, {"id": 10700041, "level": 70, "rank": 4, "limit": 3, "prePlot": "10700031|10700032", "skill": "682516", "talent": "0", "fightSkill": "0", "mineNeed": "350", "time": "216000", "preLimit": "3|3|3", "columnLimit": "65|65|65"}, {"id": 10700051, "level": 70, "rank": 5, "limit": 1, "prePlot": "10700041", "skill": "682520", "talent": "0", "fightSkill": "0", "mineNeed": "350", "time": "216000", "preLimit": "3|3|3", "columnLimit": "65|65|65"}, {"id": 10700061, "level": 70, "rank": 6, "limit": 3, "prePlot": "10700051", "skill": "682505", "talent": "0", "fightSkill": "0", "mineNeed": "350", "time": "216000", "preLimit": "1|1|1", "columnLimit": "65|65|65"}, {"id": 10700062, "level": 70, "rank": 6, "limit": 3, "prePlot": "10700051", "skill": "682507", "talent": "0", "fightSkill": "0", "mineNeed": "350", "time": "216000", "preLimit": "1|1|1", "columnLimit": "65|65|65"}, {"id": 10700071, "level": 70, "rank": 7, "limit": 3, "prePlot": "10700061|10700062", "skill": "682518", "talent": "0", "fightSkill": "0", "mineNeed": "350", "time": "216000", "preLimit": "3|3|3", "columnLimit": "65|65|65"}, {"id": 10700081, "level": 70, "rank": 8, "limit": 1, "prePlot": "10700071", "skill": "682010", "talent": "0", "fightSkill": "0", "mineNeed": "350", "time": "216000", "preLimit": "3|3|3", "columnLimit": "65|65|65"}, {"id": 10700091, "level": 70, "rank": 9, "limit": 1, "prePlot": "10700081", "skill": "682533", "talent": "0", "fightSkill": "0", "mineNeed": "350", "time": "216000", "preLimit": "1|1|1", "columnLimit": "65|65|65"}, {"id": 10710011, "level": 71, "rank": 1, "limit": 3, "prePlot": "10700091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "353", "time": "217800", "preLimit": "1|1|1", "columnLimit": "70|70|70"}, {"id": 10710021, "level": 71, "rank": 2, "limit": 3, "prePlot": "10710011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "353", "time": "217800", "preLimit": "3|3|3", "columnLimit": "70|70|70"}, {"id": 10710031, "level": 71, "rank": 3, "limit": 3, "prePlot": "10710021", "skill": "682506", "talent": "0", "fightSkill": "0", "mineNeed": "353", "time": "217800", "preLimit": "3|3|3", "columnLimit": "70|70|70"}, {"id": 10710032, "level": 71, "rank": 3, "limit": 3, "prePlot": "10710021", "skill": "682508", "talent": "0", "fightSkill": "0", "mineNeed": "353", "time": "217800", "preLimit": "3|3|3", "columnLimit": "70|70|70"}, {"id": 10710041, "level": 71, "rank": 4, "limit": 3, "prePlot": "10710031|10710032", "skill": "682515", "talent": "0", "fightSkill": "0", "mineNeed": "353", "time": "217800", "preLimit": "3|3|3", "columnLimit": "70|70|70"}, {"id": 10710051, "level": 71, "rank": 5, "limit": 1, "prePlot": "10710041", "skill": "682521", "talent": "0", "fightSkill": "0", "mineNeed": "353", "time": "217800", "preLimit": "3|3|3", "columnLimit": "70|70|70"}, {"id": 10710061, "level": 71, "rank": 6, "limit": 3, "prePlot": "10710051", "skill": "682504", "talent": "0", "fightSkill": "0", "mineNeed": "353", "time": "217800", "preLimit": "1|1|1", "columnLimit": "70|70|70"}, {"id": 10710062, "level": 71, "rank": 6, "limit": 3, "prePlot": "10710051", "skill": "682503", "talent": "0", "fightSkill": "0", "mineNeed": "353", "time": "217800", "preLimit": "1|1|1", "columnLimit": "70|70|70"}, {"id": 10710071, "level": 71, "rank": 7, "limit": 3, "prePlot": "10710061|10710062", "skill": "682517", "talent": "0", "fightSkill": "0", "mineNeed": "353", "time": "217800", "preLimit": "3|3|3", "columnLimit": "70|70|70"}, {"id": 10710081, "level": 71, "rank": 8, "limit": 1, "prePlot": "10710071", "skill": "682001", "talent": "0", "fightSkill": "0", "mineNeed": "353", "time": "217800", "preLimit": "3|3|3", "columnLimit": "70|70|70"}, {"id": 10710091, "level": 71, "rank": 9, "limit": 1, "prePlot": "10710081", "skill": "682537", "talent": "0", "fightSkill": "0", "mineNeed": "353", "time": "217800", "preLimit": "1|1|1", "columnLimit": "70|70|70"}, {"id": 10720011, "level": 72, "rank": 1, "limit": 3, "prePlot": "10710091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "356", "time": "219600", "preLimit": "1|1|1", "columnLimit": "70|70|70"}, {"id": 10720021, "level": 72, "rank": 2, "limit": 3, "prePlot": "10720011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "356", "time": "219600", "preLimit": "3|3|3", "columnLimit": "70|70|70"}, {"id": 10720031, "level": 72, "rank": 3, "limit": 3, "prePlot": "10720021", "skill": "682505", "talent": "0", "fightSkill": "0", "mineNeed": "356", "time": "219600", "preLimit": "3|3|3", "columnLimit": "70|70|70"}, {"id": 10720032, "level": 72, "rank": 3, "limit": 3, "prePlot": "10720021", "skill": "682507", "talent": "0", "fightSkill": "0", "mineNeed": "356", "time": "219600", "preLimit": "3|3|3", "columnLimit": "70|70|70"}, {"id": 10720041, "level": 72, "rank": 4, "limit": 3, "prePlot": "10720031|10720032", "skill": "682512", "talent": "0", "fightSkill": "0", "mineNeed": "356", "time": "219600", "preLimit": "3|3|3", "columnLimit": "70|70|70"}, {"id": 10720051, "level": 72, "rank": 5, "limit": 1, "prePlot": "10720041", "skill": "682519", "talent": "0", "fightSkill": "0", "mineNeed": "356", "time": "219600", "preLimit": "3|3|3", "columnLimit": "70|70|70"}, {"id": 10720061, "level": 72, "rank": 6, "limit": 3, "prePlot": "10720051", "skill": "682506", "talent": "0", "fightSkill": "0", "mineNeed": "356", "time": "219600", "preLimit": "1|1|1", "columnLimit": "70|70|70"}, {"id": 10720062, "level": 72, "rank": 6, "limit": 3, "prePlot": "10720051", "skill": "682508", "talent": "0", "fightSkill": "0", "mineNeed": "356", "time": "219600", "preLimit": "1|1|1", "columnLimit": "70|70|70"}, {"id": 10720071, "level": 72, "rank": 7, "limit": 3, "prePlot": "10720061|10720062", "skill": "682518", "talent": "0", "fightSkill": "0", "mineNeed": "356", "time": "219600", "preLimit": "3|3|3", "columnLimit": "70|70|70"}, {"id": 10720081, "level": 72, "rank": 8, "limit": 1, "prePlot": "10720071", "skill": "682002", "talent": "0", "fightSkill": "0", "mineNeed": "356", "time": "219600", "preLimit": "3|3|3", "columnLimit": "70|70|70"}, {"id": 10720091, "level": 72, "rank": 9, "limit": 1, "prePlot": "10720081", "skill": "682535", "talent": "0", "fightSkill": "0", "mineNeed": "356", "time": "219600", "preLimit": "1|1|1", "columnLimit": "70|70|70"}, {"id": 10730011, "level": 73, "rank": 1, "limit": 3, "prePlot": "10720091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "359", "time": "221400", "preLimit": "1|1|1", "columnLimit": "70|70|70"}, {"id": 10730021, "level": 73, "rank": 2, "limit": 3, "prePlot": "10730011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "359", "time": "221400", "preLimit": "3|3|3", "columnLimit": "70|70|70"}, {"id": 10730031, "level": 73, "rank": 3, "limit": 3, "prePlot": "10730021", "skill": "682504", "talent": "0", "fightSkill": "0", "mineNeed": "359", "time": "221400", "preLimit": "3|3|3", "columnLimit": "70|70|70"}, {"id": 10730032, "level": 73, "rank": 3, "limit": 3, "prePlot": "10730021", "skill": "682503", "talent": "0", "fightSkill": "0", "mineNeed": "359", "time": "221400", "preLimit": "3|3|3", "columnLimit": "70|70|70"}, {"id": 10730041, "level": 73, "rank": 4, "limit": 3, "prePlot": "10730031|10730032", "skill": "682513", "talent": "0", "fightSkill": "0", "mineNeed": "359", "time": "221400", "preLimit": "3|3|3", "columnLimit": "70|70|70"}, {"id": 10730051, "level": 73, "rank": 5, "limit": 1, "prePlot": "10730041", "skill": "682522", "talent": "0", "fightSkill": "0", "mineNeed": "359", "time": "221400", "preLimit": "3|3|3", "columnLimit": "70|70|70"}, {"id": 10730061, "level": 73, "rank": 6, "limit": 3, "prePlot": "10730051", "skill": "682505", "talent": "0", "fightSkill": "0", "mineNeed": "359", "time": "221400", "preLimit": "1|1|1", "columnLimit": "70|70|70"}, {"id": 10730062, "level": 73, "rank": 6, "limit": 3, "prePlot": "10730051", "skill": "682507", "talent": "0", "fightSkill": "0", "mineNeed": "359", "time": "221400", "preLimit": "1|1|1", "columnLimit": "70|70|70"}, {"id": 10730071, "level": 73, "rank": 7, "limit": 3, "prePlot": "10730061|10730062", "skill": "682517", "talent": "0", "fightSkill": "0", "mineNeed": "359", "time": "221400", "preLimit": "3|3|3", "columnLimit": "70|70|70"}, {"id": 10730081, "level": 73, "rank": 8, "limit": 1, "prePlot": "10730071", "skill": "682003", "talent": "0", "fightSkill": "0", "mineNeed": "359", "time": "221400", "preLimit": "3|3|3", "columnLimit": "70|70|70"}, {"id": 10730091, "level": 73, "rank": 9, "limit": 1, "prePlot": "10730081", "skill": "0", "talent": "0", "fightSkill": "681004", "mineNeed": "359", "time": "221400", "preLimit": "1|1|1", "columnLimit": "70|70|70"}, {"id": 10740011, "level": 74, "rank": 1, "limit": 3, "prePlot": "10730091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "362", "time": "223200", "preLimit": "1|1|1", "columnLimit": "70|70|70"}, {"id": 10740021, "level": 74, "rank": 2, "limit": 3, "prePlot": "10740011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "362", "time": "223200", "preLimit": "3|3|3", "columnLimit": "70|70|70"}, {"id": 10740031, "level": 74, "rank": 3, "limit": 3, "prePlot": "10740021", "skill": "682506", "talent": "0", "fightSkill": "0", "mineNeed": "362", "time": "223200", "preLimit": "3|3|3", "columnLimit": "70|70|70"}, {"id": 10740032, "level": 74, "rank": 3, "limit": 3, "prePlot": "10740021", "skill": "682508", "talent": "0", "fightSkill": "0", "mineNeed": "362", "time": "223200", "preLimit": "3|3|3", "columnLimit": "70|70|70"}, {"id": 10740041, "level": 74, "rank": 4, "limit": 3, "prePlot": "10740031|10740032", "skill": "682510", "talent": "0", "fightSkill": "0", "mineNeed": "362", "time": "223200", "preLimit": "3|3|3", "columnLimit": "70|70|70"}, {"id": 10740051, "level": 74, "rank": 5, "limit": 1, "prePlot": "10740041", "skill": "682520", "talent": "0", "fightSkill": "0", "mineNeed": "362", "time": "223200", "preLimit": "3|3|3", "columnLimit": "70|70|70"}, {"id": 10740061, "level": 74, "rank": 6, "limit": 3, "prePlot": "10740051", "skill": "682504", "talent": "0", "fightSkill": "0", "mineNeed": "362", "time": "223200", "preLimit": "1|1|1", "columnLimit": "70|70|70"}, {"id": 10740062, "level": 74, "rank": 6, "limit": 3, "prePlot": "10740051", "skill": "682503", "talent": "0", "fightSkill": "0", "mineNeed": "362", "time": "223200", "preLimit": "1|1|1", "columnLimit": "70|70|70"}, {"id": 10740071, "level": 74, "rank": 7, "limit": 3, "prePlot": "10740061|10740062", "skill": "682518", "talent": "0", "fightSkill": "0", "mineNeed": "362", "time": "223200", "preLimit": "3|3|3", "columnLimit": "70|70|70"}, {"id": 10740081, "level": 74, "rank": 8, "limit": 1, "prePlot": "10740071", "skill": "682004", "talent": "0", "fightSkill": "0", "mineNeed": "362", "time": "223200", "preLimit": "3|3|3", "columnLimit": "70|70|70"}, {"id": 10740091, "level": 74, "rank": 9, "limit": 1, "prePlot": "10740081", "skill": "682533", "talent": "0", "fightSkill": "0", "mineNeed": "362", "time": "223200", "preLimit": "1|1|1", "columnLimit": "70|70|70"}, {"id": 10750011, "level": 75, "rank": 1, "limit": 3, "prePlot": "10740091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "365", "time": "225000", "preLimit": "1|1|1", "columnLimit": "70|70|70"}, {"id": 10750021, "level": 75, "rank": 2, "limit": 3, "prePlot": "10750011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "365", "time": "225000", "preLimit": "3|3|3", "columnLimit": "70|70|70"}, {"id": 10750031, "level": 75, "rank": 3, "limit": 3, "prePlot": "10750021", "skill": "682505", "talent": "0", "fightSkill": "0", "mineNeed": "365", "time": "225000", "preLimit": "3|3|3", "columnLimit": "70|70|70"}, {"id": 10750032, "level": 75, "rank": 3, "limit": 3, "prePlot": "10750021", "skill": "682507", "talent": "0", "fightSkill": "0", "mineNeed": "365", "time": "225000", "preLimit": "3|3|3", "columnLimit": "70|70|70"}, {"id": 10750041, "level": 75, "rank": 4, "limit": 3, "prePlot": "10750031|10750032", "skill": "682511", "talent": "0", "fightSkill": "0", "mineNeed": "365", "time": "225000", "preLimit": "3|3|3", "columnLimit": "70|70|70"}, {"id": 10750051, "level": 75, "rank": 5, "limit": 1, "prePlot": "10750041", "skill": "682521", "talent": "0", "fightSkill": "0", "mineNeed": "365", "time": "225000", "preLimit": "3|3|3", "columnLimit": "70|70|70"}, {"id": 10750061, "level": 75, "rank": 6, "limit": 3, "prePlot": "10750051", "skill": "682506", "talent": "0", "fightSkill": "0", "mineNeed": "365", "time": "225000", "preLimit": "1|1|1", "columnLimit": "70|70|70"}, {"id": 10750062, "level": 75, "rank": 6, "limit": 3, "prePlot": "10750051", "skill": "682508", "talent": "0", "fightSkill": "0", "mineNeed": "365", "time": "225000", "preLimit": "1|1|1", "columnLimit": "70|70|70"}, {"id": 10750071, "level": 75, "rank": 7, "limit": 3, "prePlot": "10750061|10750062", "skill": "682517", "talent": "0", "fightSkill": "0", "mineNeed": "365", "time": "225000", "preLimit": "3|3|3", "columnLimit": "70|70|70"}, {"id": 10750081, "level": 75, "rank": 8, "limit": 1, "prePlot": "10750071", "skill": "682005", "talent": "0", "fightSkill": "0", "mineNeed": "365", "time": "225000", "preLimit": "3|3|3", "columnLimit": "70|70|70"}, {"id": 10750091, "level": 75, "rank": 9, "limit": 1, "prePlot": "10750081", "skill": "682536", "talent": "0", "fightSkill": "0", "mineNeed": "365", "time": "225000", "preLimit": "1|1|1", "columnLimit": "70|70|70"}, {"id": 10760011, "level": 76, "rank": 1, "limit": 3, "prePlot": "10750091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "368", "time": "226800", "preLimit": "1|1|1", "columnLimit": "75|75|75"}, {"id": 10760021, "level": 76, "rank": 2, "limit": 3, "prePlot": "10760011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "368", "time": "226800", "preLimit": "3|3|3", "columnLimit": "75|75|75"}, {"id": 10760031, "level": 76, "rank": 3, "limit": 3, "prePlot": "10760021", "skill": "682504", "talent": "0", "fightSkill": "0", "mineNeed": "368", "time": "226800", "preLimit": "3|3|3", "columnLimit": "75|75|75"}, {"id": 10760032, "level": 76, "rank": 3, "limit": 3, "prePlot": "10760021", "skill": "682503", "talent": "0", "fightSkill": "0", "mineNeed": "368", "time": "226800", "preLimit": "3|3|3", "columnLimit": "75|75|75"}, {"id": 10760041, "level": 76, "rank": 4, "limit": 3, "prePlot": "10760031|10760032", "skill": "682514", "talent": "0", "fightSkill": "0", "mineNeed": "368", "time": "226800", "preLimit": "3|3|3", "columnLimit": "75|75|75"}, {"id": 10760051, "level": 76, "rank": 5, "limit": 1, "prePlot": "10760041", "skill": "682519", "talent": "0", "fightSkill": "0", "mineNeed": "368", "time": "226800", "preLimit": "3|3|3", "columnLimit": "75|75|75"}, {"id": 10760061, "level": 76, "rank": 6, "limit": 3, "prePlot": "10760051", "skill": "682505", "talent": "0", "fightSkill": "0", "mineNeed": "368", "time": "226800", "preLimit": "1|1|1", "columnLimit": "75|75|75"}, {"id": 10760062, "level": 76, "rank": 6, "limit": 3, "prePlot": "10760051", "skill": "682507", "talent": "0", "fightSkill": "0", "mineNeed": "368", "time": "226800", "preLimit": "1|1|1", "columnLimit": "75|75|75"}, {"id": 10760071, "level": 76, "rank": 7, "limit": 3, "prePlot": "10760061|10760062", "skill": "682518", "talent": "0", "fightSkill": "0", "mineNeed": "368", "time": "226800", "preLimit": "3|3|3", "columnLimit": "75|75|75"}, {"id": 10760081, "level": 76, "rank": 8, "limit": 1, "prePlot": "10760071", "skill": "682006", "talent": "0", "fightSkill": "0", "mineNeed": "368", "time": "226800", "preLimit": "3|3|3", "columnLimit": "75|75|75"}, {"id": 10760091, "level": 76, "rank": 9, "limit": 1, "prePlot": "10760081", "skill": "682534", "talent": "0", "fightSkill": "0", "mineNeed": "368", "time": "226800", "preLimit": "1|1|1", "columnLimit": "75|75|75"}, {"id": 10770011, "level": 77, "rank": 1, "limit": 3, "prePlot": "10760091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "371", "time": "228600", "preLimit": "1|1|1", "columnLimit": "75|75|75"}, {"id": 10770021, "level": 77, "rank": 2, "limit": 3, "prePlot": "10770011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "371", "time": "228600", "preLimit": "3|3|3", "columnLimit": "75|75|75"}, {"id": 10770031, "level": 77, "rank": 3, "limit": 3, "prePlot": "10770021", "skill": "682506", "talent": "0", "fightSkill": "0", "mineNeed": "371", "time": "228600", "preLimit": "3|3|3", "columnLimit": "75|75|75"}, {"id": 10770032, "level": 77, "rank": 3, "limit": 3, "prePlot": "10770021", "skill": "682508", "talent": "0", "fightSkill": "0", "mineNeed": "371", "time": "228600", "preLimit": "3|3|3", "columnLimit": "75|75|75"}, {"id": 10770041, "level": 77, "rank": 4, "limit": 3, "prePlot": "10770031|10770032", "skill": "682509", "talent": "0", "fightSkill": "0", "mineNeed": "371", "time": "228600", "preLimit": "3|3|3", "columnLimit": "75|75|75"}, {"id": 10770051, "level": 77, "rank": 5, "limit": 1, "prePlot": "10770041", "skill": "682522", "talent": "0", "fightSkill": "0", "mineNeed": "371", "time": "228600", "preLimit": "3|3|3", "columnLimit": "75|75|75"}, {"id": 10770061, "level": 77, "rank": 6, "limit": 3, "prePlot": "10770051", "skill": "682504", "talent": "0", "fightSkill": "0", "mineNeed": "371", "time": "228600", "preLimit": "1|1|1", "columnLimit": "75|75|75"}, {"id": 10770062, "level": 77, "rank": 6, "limit": 3, "prePlot": "10770051", "skill": "682503", "talent": "0", "fightSkill": "0", "mineNeed": "371", "time": "228600", "preLimit": "1|1|1", "columnLimit": "75|75|75"}, {"id": 10770071, "level": 77, "rank": 7, "limit": 3, "prePlot": "10770061|10770062", "skill": "682517", "talent": "0", "fightSkill": "0", "mineNeed": "371", "time": "228600", "preLimit": "3|3|3", "columnLimit": "75|75|75"}, {"id": 10770081, "level": 77, "rank": 8, "limit": 1, "prePlot": "10770071", "skill": "682007", "talent": "0", "fightSkill": "0", "mineNeed": "371", "time": "228600", "preLimit": "3|3|3", "columnLimit": "75|75|75"}, {"id": 10770091, "level": 77, "rank": 9, "limit": 1, "prePlot": "10770081", "skill": "0", "talent": "0", "fightSkill": "681005", "mineNeed": "371", "time": "228600", "preLimit": "1|1|1", "columnLimit": "75|75|75"}, {"id": 10780011, "level": 78, "rank": 1, "limit": 3, "prePlot": "10770091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "374", "time": "230400", "preLimit": "1|1|1", "columnLimit": "75|75|75"}, {"id": 10780021, "level": 78, "rank": 2, "limit": 3, "prePlot": "10780011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "374", "time": "230400", "preLimit": "3|3|3", "columnLimit": "75|75|75"}, {"id": 10780031, "level": 78, "rank": 3, "limit": 3, "prePlot": "10780021", "skill": "682505", "talent": "0", "fightSkill": "0", "mineNeed": "374", "time": "230400", "preLimit": "3|3|3", "columnLimit": "75|75|75"}, {"id": 10780032, "level": 78, "rank": 3, "limit": 3, "prePlot": "10780021", "skill": "682507", "talent": "0", "fightSkill": "0", "mineNeed": "374", "time": "230400", "preLimit": "3|3|3", "columnLimit": "75|75|75"}, {"id": 10780041, "level": 78, "rank": 4, "limit": 3, "prePlot": "10780031|10780032", "skill": "682516", "talent": "0", "fightSkill": "0", "mineNeed": "374", "time": "230400", "preLimit": "3|3|3", "columnLimit": "75|75|75"}, {"id": 10780051, "level": 78, "rank": 5, "limit": 1, "prePlot": "10780041", "skill": "682520", "talent": "0", "fightSkill": "0", "mineNeed": "374", "time": "230400", "preLimit": "3|3|3", "columnLimit": "75|75|75"}, {"id": 10780061, "level": 78, "rank": 6, "limit": 3, "prePlot": "10780051", "skill": "682506", "talent": "0", "fightSkill": "0", "mineNeed": "374", "time": "230400", "preLimit": "1|1|1", "columnLimit": "75|75|75"}, {"id": 10780062, "level": 78, "rank": 6, "limit": 3, "prePlot": "10780051", "skill": "682508", "talent": "0", "fightSkill": "0", "mineNeed": "374", "time": "230400", "preLimit": "1|1|1", "columnLimit": "75|75|75"}, {"id": 10780071, "level": 78, "rank": 7, "limit": 3, "prePlot": "10780061|10780062", "skill": "682518", "talent": "0", "fightSkill": "0", "mineNeed": "374", "time": "230400", "preLimit": "3|3|3", "columnLimit": "75|75|75"}, {"id": 10780081, "level": 78, "rank": 8, "limit": 1, "prePlot": "10780071", "skill": "682008", "talent": "0", "fightSkill": "0", "mineNeed": "374", "time": "230400", "preLimit": "3|3|3", "columnLimit": "75|75|75"}, {"id": 10780091, "level": 78, "rank": 9, "limit": 1, "prePlot": "10780081", "skill": "682533", "talent": "0", "fightSkill": "0", "mineNeed": "374", "time": "230400", "preLimit": "1|1|1", "columnLimit": "75|75|75"}, {"id": 10790011, "level": 79, "rank": 1, "limit": 3, "prePlot": "10780091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "377", "time": "232200", "preLimit": "1|1|1", "columnLimit": "75|75|75"}, {"id": 10790021, "level": 79, "rank": 2, "limit": 3, "prePlot": "10790011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "377", "time": "232200", "preLimit": "3|3|3", "columnLimit": "75|75|75"}, {"id": 10790031, "level": 79, "rank": 3, "limit": 3, "prePlot": "10790021", "skill": "682504", "talent": "0", "fightSkill": "0", "mineNeed": "377", "time": "232200", "preLimit": "3|3|3", "columnLimit": "75|75|75"}, {"id": 10790032, "level": 79, "rank": 3, "limit": 3, "prePlot": "10790021", "skill": "682503", "talent": "0", "fightSkill": "0", "mineNeed": "377", "time": "232200", "preLimit": "3|3|3", "columnLimit": "75|75|75"}, {"id": 10790041, "level": 79, "rank": 4, "limit": 3, "prePlot": "10790031|10790032", "skill": "682515", "talent": "0", "fightSkill": "0", "mineNeed": "377", "time": "232200", "preLimit": "3|3|3", "columnLimit": "75|75|75"}, {"id": 10790051, "level": 79, "rank": 5, "limit": 1, "prePlot": "10790041", "skill": "682521", "talent": "0", "fightSkill": "0", "mineNeed": "377", "time": "232200", "preLimit": "3|3|3", "columnLimit": "75|75|75"}, {"id": 10790061, "level": 79, "rank": 6, "limit": 3, "prePlot": "10790051", "skill": "682505", "talent": "0", "fightSkill": "0", "mineNeed": "377", "time": "232200", "preLimit": "1|1|1", "columnLimit": "75|75|75"}, {"id": 10790062, "level": 79, "rank": 6, "limit": 3, "prePlot": "10790051", "skill": "682507", "talent": "0", "fightSkill": "0", "mineNeed": "377", "time": "232200", "preLimit": "1|1|1", "columnLimit": "75|75|75"}, {"id": 10790071, "level": 79, "rank": 7, "limit": 3, "prePlot": "10790061|10790062", "skill": "682517", "talent": "0", "fightSkill": "0", "mineNeed": "377", "time": "232200", "preLimit": "3|3|3", "columnLimit": "75|75|75"}, {"id": 10790081, "level": 79, "rank": 8, "limit": 1, "prePlot": "10790071", "skill": "682009", "talent": "0", "fightSkill": "0", "mineNeed": "377", "time": "232200", "preLimit": "3|3|3", "columnLimit": "75|75|75"}, {"id": 10790091, "level": 79, "rank": 9, "limit": 1, "prePlot": "10790081", "skill": "682537", "talent": "0", "fightSkill": "0", "mineNeed": "377", "time": "232200", "preLimit": "1|1|1", "columnLimit": "75|75|75"}, {"id": 10800011, "level": 80, "rank": 1, "limit": 3, "prePlot": "10790091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "380", "time": "234000", "preLimit": "1|1|1", "columnLimit": "75|75|75"}, {"id": 10800021, "level": 80, "rank": 2, "limit": 3, "prePlot": "10800011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "380", "time": "234000", "preLimit": "3|3|3", "columnLimit": "75|75|75"}, {"id": 10800031, "level": 80, "rank": 3, "limit": 3, "prePlot": "10800021", "skill": "682506", "talent": "0", "fightSkill": "0", "mineNeed": "380", "time": "234000", "preLimit": "3|3|3", "columnLimit": "75|75|75"}, {"id": 10800032, "level": 80, "rank": 3, "limit": 3, "prePlot": "10800021", "skill": "682508", "talent": "0", "fightSkill": "0", "mineNeed": "380", "time": "234000", "preLimit": "3|3|3", "columnLimit": "75|75|75"}, {"id": 10800041, "level": 80, "rank": 4, "limit": 3, "prePlot": "10800031|10800032", "skill": "682512", "talent": "0", "fightSkill": "0", "mineNeed": "380", "time": "234000", "preLimit": "3|3|3", "columnLimit": "75|75|75"}, {"id": 10800051, "level": 80, "rank": 5, "limit": 1, "prePlot": "10800041", "skill": "682519", "talent": "0", "fightSkill": "0", "mineNeed": "380", "time": "234000", "preLimit": "3|3|3", "columnLimit": "75|75|75"}, {"id": 10800061, "level": 80, "rank": 6, "limit": 3, "prePlot": "10800051", "skill": "682504", "talent": "0", "fightSkill": "0", "mineNeed": "380", "time": "234000", "preLimit": "1|1|1", "columnLimit": "75|75|75"}, {"id": 10800062, "level": 80, "rank": 6, "limit": 3, "prePlot": "10800051", "skill": "682503", "talent": "0", "fightSkill": "0", "mineNeed": "380", "time": "234000", "preLimit": "1|1|1", "columnLimit": "75|75|75"}, {"id": 10800071, "level": 80, "rank": 7, "limit": 3, "prePlot": "10800061|10800062", "skill": "682518", "talent": "0", "fightSkill": "0", "mineNeed": "380", "time": "234000", "preLimit": "3|3|3", "columnLimit": "75|75|75"}, {"id": 10800081, "level": 80, "rank": 8, "limit": 1, "prePlot": "10800071", "skill": "682010", "talent": "0", "fightSkill": "0", "mineNeed": "380", "time": "234000", "preLimit": "3|3|3", "columnLimit": "75|75|75"}, {"id": 10800091, "level": 80, "rank": 9, "limit": 1, "prePlot": "10800081", "skill": "682535", "talent": "0", "fightSkill": "0", "mineNeed": "380", "time": "234000", "preLimit": "1|1|1", "columnLimit": "75|75|75"}, {"id": 10810011, "level": 81, "rank": 1, "limit": 3, "prePlot": "10800091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "383", "time": "235800", "preLimit": "1|1|1", "columnLimit": "80|80|80"}, {"id": 10810021, "level": 81, "rank": 2, "limit": 3, "prePlot": "10810011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "383", "time": "235800", "preLimit": "3|3|3", "columnLimit": "80|80|80"}, {"id": 10810031, "level": 81, "rank": 3, "limit": 3, "prePlot": "10810021", "skill": "682505", "talent": "0", "fightSkill": "0", "mineNeed": "383", "time": "235800", "preLimit": "3|3|3", "columnLimit": "80|80|80"}, {"id": 10810032, "level": 81, "rank": 3, "limit": 3, "prePlot": "10810021", "skill": "682507", "talent": "0", "fightSkill": "0", "mineNeed": "383", "time": "235800", "preLimit": "3|3|3", "columnLimit": "80|80|80"}, {"id": 10810041, "level": 81, "rank": 4, "limit": 3, "prePlot": "10810031|10810032", "skill": "682513", "talent": "0", "fightSkill": "0", "mineNeed": "383", "time": "235800", "preLimit": "3|3|3", "columnLimit": "80|80|80"}, {"id": 10810051, "level": 81, "rank": 5, "limit": 1, "prePlot": "10810041", "skill": "682522", "talent": "0", "fightSkill": "0", "mineNeed": "383", "time": "235800", "preLimit": "3|3|3", "columnLimit": "80|80|80"}, {"id": 10810061, "level": 81, "rank": 6, "limit": 3, "prePlot": "10810051", "skill": "682506", "talent": "0", "fightSkill": "0", "mineNeed": "383", "time": "235800", "preLimit": "1|1|1", "columnLimit": "80|80|80"}, {"id": 10810062, "level": 81, "rank": 6, "limit": 3, "prePlot": "10810051", "skill": "682508", "talent": "0", "fightSkill": "0", "mineNeed": "383", "time": "235800", "preLimit": "1|1|1", "columnLimit": "80|80|80"}, {"id": 10810071, "level": 81, "rank": 7, "limit": 3, "prePlot": "10810061|10810062", "skill": "682517", "talent": "0", "fightSkill": "0", "mineNeed": "383", "time": "235800", "preLimit": "3|3|3", "columnLimit": "80|80|80"}, {"id": 10810081, "level": 81, "rank": 8, "limit": 1, "prePlot": "10810071", "skill": "682001", "talent": "0", "fightSkill": "0", "mineNeed": "383", "time": "235800", "preLimit": "3|3|3", "columnLimit": "80|80|80"}, {"id": 10810091, "level": 81, "rank": 9, "limit": 1, "prePlot": "10810081", "skill": "0", "talent": "0", "fightSkill": "681001", "mineNeed": "383", "time": "235800", "preLimit": "1|1|1", "columnLimit": "80|80|80"}, {"id": 10820011, "level": 82, "rank": 1, "limit": 3, "prePlot": "10810091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "386", "time": "237600", "preLimit": "1|1|1", "columnLimit": "80|80|80"}, {"id": 10820021, "level": 82, "rank": 2, "limit": 3, "prePlot": "10820011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "386", "time": "237600", "preLimit": "3|3|3", "columnLimit": "80|80|80"}, {"id": 10820031, "level": 82, "rank": 3, "limit": 3, "prePlot": "10820021", "skill": "682504", "talent": "0", "fightSkill": "0", "mineNeed": "386", "time": "237600", "preLimit": "3|3|3", "columnLimit": "80|80|80"}, {"id": 10820032, "level": 82, "rank": 3, "limit": 3, "prePlot": "10820021", "skill": "682503", "talent": "0", "fightSkill": "0", "mineNeed": "386", "time": "237600", "preLimit": "3|3|3", "columnLimit": "80|80|80"}, {"id": 10820041, "level": 82, "rank": 4, "limit": 3, "prePlot": "10820031|10820032", "skill": "682510", "talent": "0", "fightSkill": "0", "mineNeed": "386", "time": "237600", "preLimit": "3|3|3", "columnLimit": "80|80|80"}, {"id": 10820051, "level": 82, "rank": 5, "limit": 1, "prePlot": "10820041", "skill": "682520", "talent": "0", "fightSkill": "0", "mineNeed": "386", "time": "237600", "preLimit": "3|3|3", "columnLimit": "80|80|80"}, {"id": 10820061, "level": 82, "rank": 6, "limit": 3, "prePlot": "10820051", "skill": "682505", "talent": "0", "fightSkill": "0", "mineNeed": "386", "time": "237600", "preLimit": "1|1|1", "columnLimit": "80|80|80"}, {"id": 10820062, "level": 82, "rank": 6, "limit": 3, "prePlot": "10820051", "skill": "682507", "talent": "0", "fightSkill": "0", "mineNeed": "386", "time": "237600", "preLimit": "1|1|1", "columnLimit": "80|80|80"}, {"id": 10820071, "level": 82, "rank": 7, "limit": 3, "prePlot": "10820061|10820062", "skill": "682518", "talent": "0", "fightSkill": "0", "mineNeed": "386", "time": "237600", "preLimit": "3|3|3", "columnLimit": "80|80|80"}, {"id": 10820081, "level": 82, "rank": 8, "limit": 1, "prePlot": "10820071", "skill": "682002", "talent": "0", "fightSkill": "0", "mineNeed": "386", "time": "237600", "preLimit": "3|3|3", "columnLimit": "80|80|80"}, {"id": 10820091, "level": 82, "rank": 9, "limit": 1, "prePlot": "10820081", "skill": "682533", "talent": "0", "fightSkill": "0", "mineNeed": "386", "time": "237600", "preLimit": "1|1|1", "columnLimit": "80|80|80"}, {"id": 10830011, "level": 83, "rank": 1, "limit": 3, "prePlot": "10820091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "389", "time": "239400", "preLimit": "1|1|1", "columnLimit": "80|80|80"}, {"id": 10830021, "level": 83, "rank": 2, "limit": 3, "prePlot": "10830011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "389", "time": "239400", "preLimit": "3|3|3", "columnLimit": "80|80|80"}, {"id": 10830031, "level": 83, "rank": 3, "limit": 3, "prePlot": "10830021", "skill": "682506", "talent": "0", "fightSkill": "0", "mineNeed": "389", "time": "239400", "preLimit": "3|3|3", "columnLimit": "80|80|80"}, {"id": 10830032, "level": 83, "rank": 3, "limit": 3, "prePlot": "10830021", "skill": "682508", "talent": "0", "fightSkill": "0", "mineNeed": "389", "time": "239400", "preLimit": "3|3|3", "columnLimit": "80|80|80"}, {"id": 10830041, "level": 83, "rank": 4, "limit": 3, "prePlot": "10830031|10830032", "skill": "682511", "talent": "0", "fightSkill": "0", "mineNeed": "389", "time": "239400", "preLimit": "3|3|3", "columnLimit": "80|80|80"}, {"id": 10830051, "level": 83, "rank": 5, "limit": 1, "prePlot": "10830041", "skill": "682521", "talent": "0", "fightSkill": "0", "mineNeed": "389", "time": "239400", "preLimit": "3|3|3", "columnLimit": "80|80|80"}, {"id": 10830061, "level": 83, "rank": 6, "limit": 3, "prePlot": "10830051", "skill": "682504", "talent": "0", "fightSkill": "0", "mineNeed": "389", "time": "239400", "preLimit": "1|1|1", "columnLimit": "80|80|80"}, {"id": 10830062, "level": 83, "rank": 6, "limit": 3, "prePlot": "10830051", "skill": "682503", "talent": "0", "fightSkill": "0", "mineNeed": "389", "time": "239400", "preLimit": "1|1|1", "columnLimit": "80|80|80"}, {"id": 10830071, "level": 83, "rank": 7, "limit": 3, "prePlot": "10830061|10830062", "skill": "682517", "talent": "0", "fightSkill": "0", "mineNeed": "389", "time": "239400", "preLimit": "3|3|3", "columnLimit": "80|80|80"}, {"id": 10830081, "level": 83, "rank": 8, "limit": 1, "prePlot": "10830071", "skill": "682003", "talent": "0", "fightSkill": "0", "mineNeed": "389", "time": "239400", "preLimit": "3|3|3", "columnLimit": "80|80|80"}, {"id": 10830091, "level": 83, "rank": 9, "limit": 1, "prePlot": "10830081", "skill": "682536", "talent": "0", "fightSkill": "0", "mineNeed": "389", "time": "239400", "preLimit": "1|1|1", "columnLimit": "80|80|80"}, {"id": 10840011, "level": 84, "rank": 1, "limit": 3, "prePlot": "10830091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "392", "time": "241200", "preLimit": "1|1|1", "columnLimit": "80|80|80"}, {"id": 10840021, "level": 84, "rank": 2, "limit": 3, "prePlot": "10840011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "392", "time": "241200", "preLimit": "3|3|3", "columnLimit": "80|80|80"}, {"id": 10840031, "level": 84, "rank": 3, "limit": 3, "prePlot": "10840021", "skill": "682505", "talent": "0", "fightSkill": "0", "mineNeed": "392", "time": "241200", "preLimit": "3|3|3", "columnLimit": "80|80|80"}, {"id": 10840032, "level": 84, "rank": 3, "limit": 3, "prePlot": "10840021", "skill": "682507", "talent": "0", "fightSkill": "0", "mineNeed": "392", "time": "241200", "preLimit": "3|3|3", "columnLimit": "80|80|80"}, {"id": 10840041, "level": 84, "rank": 4, "limit": 3, "prePlot": "10840031|10840032", "skill": "682514", "talent": "0", "fightSkill": "0", "mineNeed": "392", "time": "241200", "preLimit": "3|3|3", "columnLimit": "80|80|80"}, {"id": 10840051, "level": 84, "rank": 5, "limit": 1, "prePlot": "10840041", "skill": "682519", "talent": "0", "fightSkill": "0", "mineNeed": "392", "time": "241200", "preLimit": "3|3|3", "columnLimit": "80|80|80"}, {"id": 10840061, "level": 84, "rank": 6, "limit": 3, "prePlot": "10840051", "skill": "682506", "talent": "0", "fightSkill": "0", "mineNeed": "392", "time": "241200", "preLimit": "1|1|1", "columnLimit": "80|80|80"}, {"id": 10840062, "level": 84, "rank": 6, "limit": 3, "prePlot": "10840051", "skill": "682508", "talent": "0", "fightSkill": "0", "mineNeed": "392", "time": "241200", "preLimit": "1|1|1", "columnLimit": "80|80|80"}, {"id": 10840071, "level": 84, "rank": 7, "limit": 3, "prePlot": "10840061|10840062", "skill": "682518", "talent": "0", "fightSkill": "0", "mineNeed": "392", "time": "241200", "preLimit": "3|3|3", "columnLimit": "80|80|80"}, {"id": 10840081, "level": 84, "rank": 8, "limit": 1, "prePlot": "10840071", "skill": "682004", "talent": "0", "fightSkill": "0", "mineNeed": "392", "time": "241200", "preLimit": "3|3|3", "columnLimit": "80|80|80"}, {"id": 10840091, "level": 84, "rank": 9, "limit": 1, "prePlot": "10840081", "skill": "682535", "talent": "0", "fightSkill": "0", "mineNeed": "392", "time": "241200", "preLimit": "1|1|1", "columnLimit": "80|80|80"}, {"id": 10850011, "level": 85, "rank": 1, "limit": 3, "prePlot": "10840091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "395", "time": "243000", "preLimit": "1|1|1", "columnLimit": "80|80|80"}, {"id": 10850021, "level": 85, "rank": 2, "limit": 3, "prePlot": "10850011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "395", "time": "243000", "preLimit": "3|3|3", "columnLimit": "80|80|80"}, {"id": 10850031, "level": 85, "rank": 3, "limit": 3, "prePlot": "10850021", "skill": "682504", "talent": "0", "fightSkill": "0", "mineNeed": "395", "time": "243000", "preLimit": "3|3|3", "columnLimit": "80|80|80"}, {"id": 10850032, "level": 85, "rank": 3, "limit": 3, "prePlot": "10850021", "skill": "682503", "talent": "0", "fightSkill": "0", "mineNeed": "395", "time": "243000", "preLimit": "3|3|3", "columnLimit": "80|80|80"}, {"id": 10850041, "level": 85, "rank": 4, "limit": 3, "prePlot": "10850031|10850032", "skill": "682509", "talent": "0", "fightSkill": "0", "mineNeed": "395", "time": "243000", "preLimit": "3|3|3", "columnLimit": "80|80|80"}, {"id": 10850051, "level": 85, "rank": 5, "limit": 1, "prePlot": "10850041", "skill": "682522", "talent": "0", "fightSkill": "0", "mineNeed": "395", "time": "243000", "preLimit": "3|3|3", "columnLimit": "80|80|80"}, {"id": 10850061, "level": 85, "rank": 6, "limit": 3, "prePlot": "10850051", "skill": "682505", "talent": "0", "fightSkill": "0", "mineNeed": "395", "time": "243000", "preLimit": "1|1|1", "columnLimit": "80|80|80"}, {"id": 10850062, "level": 85, "rank": 6, "limit": 3, "prePlot": "10850051", "skill": "682507", "talent": "0", "fightSkill": "0", "mineNeed": "395", "time": "243000", "preLimit": "1|1|1", "columnLimit": "80|80|80"}, {"id": 10850071, "level": 85, "rank": 7, "limit": 3, "prePlot": "10850061|10850062", "skill": "682517", "talent": "0", "fightSkill": "0", "mineNeed": "395", "time": "243000", "preLimit": "3|3|3", "columnLimit": "80|80|80"}, {"id": 10850081, "level": 85, "rank": 8, "limit": 1, "prePlot": "10850071", "skill": "682005", "talent": "0", "fightSkill": "0", "mineNeed": "395", "time": "243000", "preLimit": "3|3|3", "columnLimit": "80|80|80"}, {"id": 10850091, "level": 85, "rank": 9, "limit": 1, "prePlot": "10850081", "skill": "0", "talent": "0", "fightSkill": "681002", "mineNeed": "395", "time": "243000", "preLimit": "1|1|1", "columnLimit": "80|80|80"}, {"id": 10860011, "level": 86, "rank": 1, "limit": 3, "prePlot": "10850091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "398", "time": "244800", "preLimit": "1|1|1", "columnLimit": "85|85|85"}, {"id": 10860021, "level": 86, "rank": 2, "limit": 3, "prePlot": "10860011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "398", "time": "244800", "preLimit": "3|3|3", "columnLimit": "85|85|85"}, {"id": 10860031, "level": 86, "rank": 3, "limit": 3, "prePlot": "10860021", "skill": "682506", "talent": "0", "fightSkill": "0", "mineNeed": "398", "time": "244800", "preLimit": "3|3|3", "columnLimit": "85|85|85"}, {"id": 10860032, "level": 86, "rank": 3, "limit": 3, "prePlot": "10860021", "skill": "682508", "talent": "0", "fightSkill": "0", "mineNeed": "398", "time": "244800", "preLimit": "3|3|3", "columnLimit": "85|85|85"}, {"id": 10860041, "level": 86, "rank": 4, "limit": 3, "prePlot": "10860031|10860032", "skill": "682516", "talent": "0", "fightSkill": "0", "mineNeed": "398", "time": "244800", "preLimit": "3|3|3", "columnLimit": "85|85|85"}, {"id": 10860051, "level": 86, "rank": 5, "limit": 1, "prePlot": "10860041", "skill": "682520", "talent": "0", "fightSkill": "0", "mineNeed": "398", "time": "244800", "preLimit": "3|3|3", "columnLimit": "85|85|85"}, {"id": 10860061, "level": 86, "rank": 6, "limit": 3, "prePlot": "10860051", "skill": "682504", "talent": "0", "fightSkill": "0", "mineNeed": "398", "time": "244800", "preLimit": "1|1|1", "columnLimit": "85|85|85"}, {"id": 10860062, "level": 86, "rank": 6, "limit": 3, "prePlot": "10860051", "skill": "682503", "talent": "0", "fightSkill": "0", "mineNeed": "398", "time": "244800", "preLimit": "1|1|1", "columnLimit": "85|85|85"}, {"id": 10860071, "level": 86, "rank": 7, "limit": 3, "prePlot": "10860061|10860062", "skill": "682518", "talent": "0", "fightSkill": "0", "mineNeed": "398", "time": "244800", "preLimit": "3|3|3", "columnLimit": "85|85|85"}, {"id": 10860081, "level": 86, "rank": 8, "limit": 1, "prePlot": "10860071", "skill": "682006", "talent": "0", "fightSkill": "0", "mineNeed": "398", "time": "244800", "preLimit": "3|3|3", "columnLimit": "85|85|85"}, {"id": 10860091, "level": 86, "rank": 9, "limit": 1, "prePlot": "10860081", "skill": "682533", "talent": "0", "fightSkill": "0", "mineNeed": "398", "time": "244800", "preLimit": "1|1|1", "columnLimit": "85|85|85"}, {"id": 10870011, "level": 87, "rank": 1, "limit": 3, "prePlot": "10860091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "401", "time": "246600", "preLimit": "1|1|1", "columnLimit": "85|85|85"}, {"id": 10870021, "level": 87, "rank": 2, "limit": 3, "prePlot": "10870011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "401", "time": "246600", "preLimit": "3|3|3", "columnLimit": "85|85|85"}, {"id": 10870031, "level": 87, "rank": 3, "limit": 3, "prePlot": "10870021", "skill": "682505", "talent": "0", "fightSkill": "0", "mineNeed": "401", "time": "246600", "preLimit": "3|3|3", "columnLimit": "85|85|85"}, {"id": 10870032, "level": 87, "rank": 3, "limit": 3, "prePlot": "10870021", "skill": "682507", "talent": "0", "fightSkill": "0", "mineNeed": "401", "time": "246600", "preLimit": "3|3|3", "columnLimit": "85|85|85"}, {"id": 10870041, "level": 87, "rank": 4, "limit": 3, "prePlot": "10870031|10870032", "skill": "682515", "talent": "0", "fightSkill": "0", "mineNeed": "401", "time": "246600", "preLimit": "3|3|3", "columnLimit": "85|85|85"}, {"id": 10870051, "level": 87, "rank": 5, "limit": 1, "prePlot": "10870041", "skill": "682521", "talent": "0", "fightSkill": "0", "mineNeed": "401", "time": "246600", "preLimit": "3|3|3", "columnLimit": "85|85|85"}, {"id": 10870061, "level": 87, "rank": 6, "limit": 3, "prePlot": "10870051", "skill": "682506", "talent": "0", "fightSkill": "0", "mineNeed": "401", "time": "246600", "preLimit": "1|1|1", "columnLimit": "85|85|85"}, {"id": 10870062, "level": 87, "rank": 6, "limit": 3, "prePlot": "10870051", "skill": "682508", "talent": "0", "fightSkill": "0", "mineNeed": "401", "time": "246600", "preLimit": "1|1|1", "columnLimit": "85|85|85"}, {"id": 10870071, "level": 87, "rank": 7, "limit": 3, "prePlot": "10870061|10870062", "skill": "682517", "talent": "0", "fightSkill": "0", "mineNeed": "401", "time": "246600", "preLimit": "3|3|3", "columnLimit": "85|85|85"}, {"id": 10870081, "level": 87, "rank": 8, "limit": 1, "prePlot": "10870071", "skill": "682007", "talent": "0", "fightSkill": "0", "mineNeed": "401", "time": "246600", "preLimit": "3|3|3", "columnLimit": "85|85|85"}, {"id": 10870091, "level": 87, "rank": 9, "limit": 1, "prePlot": "10870081", "skill": "682537", "talent": "0", "fightSkill": "0", "mineNeed": "401", "time": "246600", "preLimit": "1|1|1", "columnLimit": "85|85|85"}, {"id": 10880011, "level": 88, "rank": 1, "limit": 3, "prePlot": "10870091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "404", "time": "248400", "preLimit": "1|1|1", "columnLimit": "85|85|85"}, {"id": 10880021, "level": 88, "rank": 2, "limit": 3, "prePlot": "10880011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "404", "time": "248400", "preLimit": "3|3|3", "columnLimit": "85|85|85"}, {"id": 10880031, "level": 88, "rank": 3, "limit": 3, "prePlot": "10880021", "skill": "682504", "talent": "0", "fightSkill": "0", "mineNeed": "404", "time": "248400", "preLimit": "3|3|3", "columnLimit": "85|85|85"}, {"id": 10880032, "level": 88, "rank": 3, "limit": 3, "prePlot": "10880021", "skill": "682503", "talent": "0", "fightSkill": "0", "mineNeed": "404", "time": "248400", "preLimit": "3|3|3", "columnLimit": "85|85|85"}, {"id": 10880041, "level": 88, "rank": 4, "limit": 3, "prePlot": "10880031|10880032", "skill": "682512", "talent": "0", "fightSkill": "0", "mineNeed": "404", "time": "248400", "preLimit": "3|3|3", "columnLimit": "85|85|85"}, {"id": 10880051, "level": 88, "rank": 5, "limit": 1, "prePlot": "10880041", "skill": "682519", "talent": "0", "fightSkill": "0", "mineNeed": "404", "time": "248400", "preLimit": "3|3|3", "columnLimit": "85|85|85"}, {"id": 10880061, "level": 88, "rank": 6, "limit": 3, "prePlot": "10880051", "skill": "682505", "talent": "0", "fightSkill": "0", "mineNeed": "404", "time": "248400", "preLimit": "1|1|1", "columnLimit": "85|85|85"}, {"id": 10880062, "level": 88, "rank": 6, "limit": 3, "prePlot": "10880051", "skill": "682507", "talent": "0", "fightSkill": "0", "mineNeed": "404", "time": "248400", "preLimit": "1|1|1", "columnLimit": "85|85|85"}, {"id": 10880071, "level": 88, "rank": 7, "limit": 3, "prePlot": "10880061|10880062", "skill": "682518", "talent": "0", "fightSkill": "0", "mineNeed": "404", "time": "248400", "preLimit": "3|3|3", "columnLimit": "85|85|85"}, {"id": 10880081, "level": 88, "rank": 8, "limit": 1, "prePlot": "10880071", "skill": "682008", "talent": "0", "fightSkill": "0", "mineNeed": "404", "time": "248400", "preLimit": "3|3|3", "columnLimit": "85|85|85"}, {"id": 10880091, "level": 88, "rank": 9, "limit": 1, "prePlot": "10880081", "skill": "682534", "talent": "0", "fightSkill": "0", "mineNeed": "404", "time": "248400", "preLimit": "1|1|1", "columnLimit": "85|85|85"}, {"id": 10890011, "level": 89, "rank": 1, "limit": 3, "prePlot": "10880091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "407", "time": "250200", "preLimit": "1|1|1", "columnLimit": "85|85|85"}, {"id": 10890021, "level": 89, "rank": 2, "limit": 3, "prePlot": "10890011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "407", "time": "250200", "preLimit": "3|3|3", "columnLimit": "85|85|85"}, {"id": 10890031, "level": 89, "rank": 3, "limit": 3, "prePlot": "10890021", "skill": "682506", "talent": "0", "fightSkill": "0", "mineNeed": "407", "time": "250200", "preLimit": "3|3|3", "columnLimit": "85|85|85"}, {"id": 10890032, "level": 89, "rank": 3, "limit": 3, "prePlot": "10890021", "skill": "682508", "talent": "0", "fightSkill": "0", "mineNeed": "407", "time": "250200", "preLimit": "3|3|3", "columnLimit": "85|85|85"}, {"id": 10890041, "level": 89, "rank": 4, "limit": 3, "prePlot": "10890031|10890032", "skill": "682513", "talent": "0", "fightSkill": "0", "mineNeed": "407", "time": "250200", "preLimit": "3|3|3", "columnLimit": "85|85|85"}, {"id": 10890051, "level": 89, "rank": 5, "limit": 1, "prePlot": "10890041", "skill": "682522", "talent": "0", "fightSkill": "0", "mineNeed": "407", "time": "250200", "preLimit": "3|3|3", "columnLimit": "85|85|85"}, {"id": 10890061, "level": 89, "rank": 6, "limit": 3, "prePlot": "10890051", "skill": "682504", "talent": "0", "fightSkill": "0", "mineNeed": "407", "time": "250200", "preLimit": "1|1|1", "columnLimit": "85|85|85"}, {"id": 10890062, "level": 89, "rank": 6, "limit": 3, "prePlot": "10890051", "skill": "682503", "talent": "0", "fightSkill": "0", "mineNeed": "407", "time": "250200", "preLimit": "1|1|1", "columnLimit": "85|85|85"}, {"id": 10890071, "level": 89, "rank": 7, "limit": 3, "prePlot": "10890061|10890062", "skill": "682517", "talent": "0", "fightSkill": "0", "mineNeed": "407", "time": "250200", "preLimit": "3|3|3", "columnLimit": "85|85|85"}, {"id": 10890081, "level": 89, "rank": 8, "limit": 1, "prePlot": "10890071", "skill": "682009", "talent": "0", "fightSkill": "0", "mineNeed": "407", "time": "250200", "preLimit": "3|3|3", "columnLimit": "85|85|85"}, {"id": 10890091, "level": 89, "rank": 9, "limit": 1, "prePlot": "10890081", "skill": "0", "talent": "0", "fightSkill": "681003", "mineNeed": "407", "time": "250200", "preLimit": "1|1|1", "columnLimit": "85|85|85"}, {"id": 10900011, "level": 90, "rank": 1, "limit": 3, "prePlot": "10890091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "410", "time": "252000", "preLimit": "1|1|1", "columnLimit": "85|85|85"}, {"id": 10900021, "level": 90, "rank": 2, "limit": 3, "prePlot": "10900011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "410", "time": "252000", "preLimit": "3|3|3", "columnLimit": "85|85|85"}, {"id": 10900031, "level": 90, "rank": 3, "limit": 3, "prePlot": "10900021", "skill": "682505", "talent": "0", "fightSkill": "0", "mineNeed": "410", "time": "252000", "preLimit": "3|3|3", "columnLimit": "85|85|85"}, {"id": 10900032, "level": 90, "rank": 3, "limit": 3, "prePlot": "10900021", "skill": "682507", "talent": "0", "fightSkill": "0", "mineNeed": "410", "time": "252000", "preLimit": "3|3|3", "columnLimit": "85|85|85"}, {"id": 10900041, "level": 90, "rank": 4, "limit": 3, "prePlot": "10900031|10900032", "skill": "682510", "talent": "0", "fightSkill": "0", "mineNeed": "410", "time": "252000", "preLimit": "3|3|3", "columnLimit": "85|85|85"}, {"id": 10900051, "level": 90, "rank": 5, "limit": 1, "prePlot": "10900041", "skill": "682520", "talent": "0", "fightSkill": "0", "mineNeed": "410", "time": "252000", "preLimit": "3|3|3", "columnLimit": "85|85|85"}, {"id": 10900061, "level": 90, "rank": 6, "limit": 3, "prePlot": "10900051", "skill": "682506", "talent": "0", "fightSkill": "0", "mineNeed": "410", "time": "252000", "preLimit": "1|1|1", "columnLimit": "85|85|85"}, {"id": 10900062, "level": 90, "rank": 6, "limit": 3, "prePlot": "10900051", "skill": "682508", "talent": "0", "fightSkill": "0", "mineNeed": "410", "time": "252000", "preLimit": "1|1|1", "columnLimit": "85|85|85"}, {"id": 10900071, "level": 90, "rank": 7, "limit": 3, "prePlot": "10900061|10900062", "skill": "682518", "talent": "0", "fightSkill": "0", "mineNeed": "410", "time": "252000", "preLimit": "3|3|3", "columnLimit": "85|85|85"}, {"id": 10900081, "level": 90, "rank": 8, "limit": 1, "prePlot": "10900071", "skill": "682010", "talent": "0", "fightSkill": "0", "mineNeed": "410", "time": "252000", "preLimit": "3|3|3", "columnLimit": "85|85|85"}, {"id": 10900091, "level": 90, "rank": 9, "limit": 1, "prePlot": "10900081", "skill": "682533", "talent": "0", "fightSkill": "0", "mineNeed": "410", "time": "252000", "preLimit": "1|1|1", "columnLimit": "85|85|85"}, {"id": 10910011, "level": 91, "rank": 1, "limit": 3, "prePlot": "10900091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "413", "time": "253800", "preLimit": "1|1|1", "columnLimit": "90|90|90"}, {"id": 10910021, "level": 91, "rank": 2, "limit": 3, "prePlot": "10910011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "413", "time": "253800", "preLimit": "3|3|3", "columnLimit": "90|90|90"}, {"id": 10910031, "level": 91, "rank": 3, "limit": 3, "prePlot": "10910021", "skill": "682504", "talent": "0", "fightSkill": "0", "mineNeed": "413", "time": "253800", "preLimit": "3|3|3", "columnLimit": "90|90|90"}, {"id": 10910032, "level": 91, "rank": 3, "limit": 3, "prePlot": "10910021", "skill": "682503", "talent": "0", "fightSkill": "0", "mineNeed": "413", "time": "253800", "preLimit": "3|3|3", "columnLimit": "90|90|90"}, {"id": 10910041, "level": 91, "rank": 4, "limit": 3, "prePlot": "10910031|10910032", "skill": "682511", "talent": "0", "fightSkill": "0", "mineNeed": "413", "time": "253800", "preLimit": "3|3|3", "columnLimit": "90|90|90"}, {"id": 10910051, "level": 91, "rank": 5, "limit": 1, "prePlot": "10910041", "skill": "682521", "talent": "0", "fightSkill": "0", "mineNeed": "413", "time": "253800", "preLimit": "3|3|3", "columnLimit": "90|90|90"}, {"id": 10910061, "level": 91, "rank": 6, "limit": 3, "prePlot": "10910051", "skill": "682505", "talent": "0", "fightSkill": "0", "mineNeed": "413", "time": "253800", "preLimit": "1|1|1", "columnLimit": "90|90|90"}, {"id": 10910062, "level": 91, "rank": 6, "limit": 3, "prePlot": "10910051", "skill": "682507", "talent": "0", "fightSkill": "0", "mineNeed": "413", "time": "253800", "preLimit": "1|1|1", "columnLimit": "90|90|90"}, {"id": 10910071, "level": 91, "rank": 7, "limit": 3, "prePlot": "10910061|10910062", "skill": "682517", "talent": "0", "fightSkill": "0", "mineNeed": "413", "time": "253800", "preLimit": "3|3|3", "columnLimit": "90|90|90"}, {"id": 10910081, "level": 91, "rank": 8, "limit": 1, "prePlot": "10910071", "skill": "682001", "talent": "0", "fightSkill": "0", "mineNeed": "413", "time": "253800", "preLimit": "3|3|3", "columnLimit": "90|90|90"}, {"id": 10910091, "level": 91, "rank": 9, "limit": 1, "prePlot": "10910081", "skill": "682536", "talent": "0", "fightSkill": "0", "mineNeed": "413", "time": "253800", "preLimit": "1|1|1", "columnLimit": "90|90|90"}, {"id": 10920011, "level": 92, "rank": 1, "limit": 3, "prePlot": "10910091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "416", "time": "255600", "preLimit": "1|1|1", "columnLimit": "90|90|90"}, {"id": 10920021, "level": 92, "rank": 2, "limit": 3, "prePlot": "10920011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "416", "time": "255600", "preLimit": "3|3|3", "columnLimit": "90|90|90"}, {"id": 10920031, "level": 92, "rank": 3, "limit": 3, "prePlot": "10920021", "skill": "682506", "talent": "0", "fightSkill": "0", "mineNeed": "416", "time": "255600", "preLimit": "3|3|3", "columnLimit": "90|90|90"}, {"id": 10920032, "level": 92, "rank": 3, "limit": 3, "prePlot": "10920021", "skill": "682508", "talent": "0", "fightSkill": "0", "mineNeed": "416", "time": "255600", "preLimit": "3|3|3", "columnLimit": "90|90|90"}, {"id": 10920041, "level": 92, "rank": 4, "limit": 3, "prePlot": "10920031|10920032", "skill": "682514", "talent": "0", "fightSkill": "0", "mineNeed": "416", "time": "255600", "preLimit": "3|3|3", "columnLimit": "90|90|90"}, {"id": 10920051, "level": 92, "rank": 5, "limit": 1, "prePlot": "10920041", "skill": "682519", "talent": "0", "fightSkill": "0", "mineNeed": "416", "time": "255600", "preLimit": "3|3|3", "columnLimit": "90|90|90"}, {"id": 10920061, "level": 92, "rank": 6, "limit": 3, "prePlot": "10920051", "skill": "682504", "talent": "0", "fightSkill": "0", "mineNeed": "416", "time": "255600", "preLimit": "1|1|1", "columnLimit": "90|90|90"}, {"id": 10920062, "level": 92, "rank": 6, "limit": 3, "prePlot": "10920051", "skill": "682503", "talent": "0", "fightSkill": "0", "mineNeed": "416", "time": "255600", "preLimit": "1|1|1", "columnLimit": "90|90|90"}, {"id": 10920071, "level": 92, "rank": 7, "limit": 3, "prePlot": "10920061|10920062", "skill": "682518", "talent": "0", "fightSkill": "0", "mineNeed": "416", "time": "255600", "preLimit": "3|3|3", "columnLimit": "90|90|90"}, {"id": 10920081, "level": 92, "rank": 8, "limit": 1, "prePlot": "10920071", "skill": "682002", "talent": "0", "fightSkill": "0", "mineNeed": "416", "time": "255600", "preLimit": "3|3|3", "columnLimit": "90|90|90"}, {"id": 10920091, "level": 92, "rank": 9, "limit": 1, "prePlot": "10920081", "skill": "682535", "talent": "0", "fightSkill": "0", "mineNeed": "416", "time": "255600", "preLimit": "1|1|1", "columnLimit": "90|90|90"}, {"id": 10930011, "level": 93, "rank": 1, "limit": 3, "prePlot": "10920091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "419", "time": "257400", "preLimit": "1|1|1", "columnLimit": "90|90|90"}, {"id": 10930021, "level": 93, "rank": 2, "limit": 3, "prePlot": "10930011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "419", "time": "257400", "preLimit": "3|3|3", "columnLimit": "90|90|90"}, {"id": 10930031, "level": 93, "rank": 3, "limit": 3, "prePlot": "10930021", "skill": "682505", "talent": "0", "fightSkill": "0", "mineNeed": "419", "time": "257400", "preLimit": "3|3|3", "columnLimit": "90|90|90"}, {"id": 10930032, "level": 93, "rank": 3, "limit": 3, "prePlot": "10930021", "skill": "682507", "talent": "0", "fightSkill": "0", "mineNeed": "419", "time": "257400", "preLimit": "3|3|3", "columnLimit": "90|90|90"}, {"id": 10930041, "level": 93, "rank": 4, "limit": 3, "prePlot": "10930031|10930032", "skill": "682509", "talent": "0", "fightSkill": "0", "mineNeed": "419", "time": "257400", "preLimit": "3|3|3", "columnLimit": "90|90|90"}, {"id": 10930051, "level": 93, "rank": 5, "limit": 1, "prePlot": "10930041", "skill": "682522", "talent": "0", "fightSkill": "0", "mineNeed": "419", "time": "257400", "preLimit": "3|3|3", "columnLimit": "90|90|90"}, {"id": 10930061, "level": 93, "rank": 6, "limit": 3, "prePlot": "10930051", "skill": "682506", "talent": "0", "fightSkill": "0", "mineNeed": "419", "time": "257400", "preLimit": "1|1|1", "columnLimit": "90|90|90"}, {"id": 10930062, "level": 93, "rank": 6, "limit": 3, "prePlot": "10930051", "skill": "682508", "talent": "0", "fightSkill": "0", "mineNeed": "419", "time": "257400", "preLimit": "1|1|1", "columnLimit": "90|90|90"}, {"id": 10930071, "level": 93, "rank": 7, "limit": 3, "prePlot": "10930061|10930062", "skill": "682517", "talent": "0", "fightSkill": "0", "mineNeed": "419", "time": "257400", "preLimit": "3|3|3", "columnLimit": "90|90|90"}, {"id": 10930081, "level": 93, "rank": 8, "limit": 1, "prePlot": "10930071", "skill": "682003", "talent": "0", "fightSkill": "0", "mineNeed": "419", "time": "257400", "preLimit": "3|3|3", "columnLimit": "90|90|90"}, {"id": 10930091, "level": 93, "rank": 9, "limit": 1, "prePlot": "10930081", "skill": "0", "talent": "0", "fightSkill": "681004", "mineNeed": "419", "time": "257400", "preLimit": "1|1|1", "columnLimit": "90|90|90"}, {"id": 10940011, "level": 94, "rank": 1, "limit": 3, "prePlot": "10930091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "422", "time": "259200", "preLimit": "1|1|1", "columnLimit": "90|90|90"}, {"id": 10940021, "level": 94, "rank": 2, "limit": 3, "prePlot": "10940011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "422", "time": "259200", "preLimit": "3|3|3", "columnLimit": "90|90|90"}, {"id": 10940031, "level": 94, "rank": 3, "limit": 3, "prePlot": "10940021", "skill": "682504", "talent": "0", "fightSkill": "0", "mineNeed": "422", "time": "259200", "preLimit": "3|3|3", "columnLimit": "90|90|90"}, {"id": 10940032, "level": 94, "rank": 3, "limit": 3, "prePlot": "10940021", "skill": "682503", "talent": "0", "fightSkill": "0", "mineNeed": "422", "time": "259200", "preLimit": "3|3|3", "columnLimit": "90|90|90"}, {"id": 10940041, "level": 94, "rank": 4, "limit": 3, "prePlot": "10940031|10940032", "skill": "682516", "talent": "0", "fightSkill": "0", "mineNeed": "422", "time": "259200", "preLimit": "3|3|3", "columnLimit": "90|90|90"}, {"id": 10940051, "level": 94, "rank": 5, "limit": 1, "prePlot": "10940041", "skill": "682520", "talent": "0", "fightSkill": "0", "mineNeed": "422", "time": "259200", "preLimit": "3|3|3", "columnLimit": "90|90|90"}, {"id": 10940061, "level": 94, "rank": 6, "limit": 3, "prePlot": "10940051", "skill": "682505", "talent": "0", "fightSkill": "0", "mineNeed": "422", "time": "259200", "preLimit": "1|1|1", "columnLimit": "90|90|90"}, {"id": 10940062, "level": 94, "rank": 6, "limit": 3, "prePlot": "10940051", "skill": "682507", "talent": "0", "fightSkill": "0", "mineNeed": "422", "time": "259200", "preLimit": "1|1|1", "columnLimit": "90|90|90"}, {"id": 10940071, "level": 94, "rank": 7, "limit": 3, "prePlot": "10940061|10940062", "skill": "682518", "talent": "0", "fightSkill": "0", "mineNeed": "422", "time": "259200", "preLimit": "3|3|3", "columnLimit": "90|90|90"}, {"id": 10940081, "level": 94, "rank": 8, "limit": 1, "prePlot": "10940071", "skill": "682004", "talent": "0", "fightSkill": "0", "mineNeed": "422", "time": "259200", "preLimit": "3|3|3", "columnLimit": "90|90|90"}, {"id": 10940091, "level": 94, "rank": 9, "limit": 1, "prePlot": "10940081", "skill": "682533", "talent": "0", "fightSkill": "0", "mineNeed": "422", "time": "259200", "preLimit": "1|1|1", "columnLimit": "90|90|90"}, {"id": 10950011, "level": 95, "rank": 1, "limit": 3, "prePlot": "10940091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "425", "time": "261000", "preLimit": "1|1|1", "columnLimit": "90|90|90"}, {"id": 10950021, "level": 95, "rank": 2, "limit": 3, "prePlot": "10950011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "425", "time": "261000", "preLimit": "3|3|3", "columnLimit": "90|90|90"}, {"id": 10950031, "level": 95, "rank": 3, "limit": 3, "prePlot": "10950021", "skill": "682506", "talent": "0", "fightSkill": "0", "mineNeed": "425", "time": "261000", "preLimit": "3|3|3", "columnLimit": "90|90|90"}, {"id": 10950032, "level": 95, "rank": 3, "limit": 3, "prePlot": "10950021", "skill": "682508", "talent": "0", "fightSkill": "0", "mineNeed": "425", "time": "261000", "preLimit": "3|3|3", "columnLimit": "90|90|90"}, {"id": 10950041, "level": 95, "rank": 4, "limit": 3, "prePlot": "10950031|10950032", "skill": "682515", "talent": "0", "fightSkill": "0", "mineNeed": "425", "time": "261000", "preLimit": "3|3|3", "columnLimit": "90|90|90"}, {"id": 10950051, "level": 95, "rank": 5, "limit": 1, "prePlot": "10950041", "skill": "682521", "talent": "0", "fightSkill": "0", "mineNeed": "425", "time": "261000", "preLimit": "3|3|3", "columnLimit": "90|90|90"}, {"id": 10950061, "level": 95, "rank": 6, "limit": 3, "prePlot": "10950051", "skill": "682504", "talent": "0", "fightSkill": "0", "mineNeed": "425", "time": "261000", "preLimit": "1|1|1", "columnLimit": "90|90|90"}, {"id": 10950062, "level": 95, "rank": 6, "limit": 3, "prePlot": "10950051", "skill": "682503", "talent": "0", "fightSkill": "0", "mineNeed": "425", "time": "261000", "preLimit": "1|1|1", "columnLimit": "90|90|90"}, {"id": 10950071, "level": 95, "rank": 7, "limit": 3, "prePlot": "10950061|10950062", "skill": "682517", "talent": "0", "fightSkill": "0", "mineNeed": "425", "time": "261000", "preLimit": "3|3|3", "columnLimit": "90|90|90"}, {"id": 10950081, "level": 95, "rank": 8, "limit": 1, "prePlot": "10950071", "skill": "682005", "talent": "0", "fightSkill": "0", "mineNeed": "425", "time": "261000", "preLimit": "3|3|3", "columnLimit": "90|90|90"}, {"id": 10950091, "level": 95, "rank": 9, "limit": 1, "prePlot": "10950081", "skill": "682537", "talent": "0", "fightSkill": "0", "mineNeed": "425", "time": "261000", "preLimit": "1|1|1", "columnLimit": "90|90|90"}, {"id": 10960011, "level": 96, "rank": 1, "limit": 3, "prePlot": "10950091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "428", "time": "262800", "preLimit": "1|1|1", "columnLimit": "95|95|95"}, {"id": 10960021, "level": 96, "rank": 2, "limit": 3, "prePlot": "10960011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "428", "time": "262800", "preLimit": "3|3|3", "columnLimit": "95|95|95"}, {"id": 10960031, "level": 96, "rank": 3, "limit": 3, "prePlot": "10960021", "skill": "682505", "talent": "0", "fightSkill": "0", "mineNeed": "428", "time": "262800", "preLimit": "3|3|3", "columnLimit": "95|95|95"}, {"id": 10960032, "level": 96, "rank": 3, "limit": 3, "prePlot": "10960021", "skill": "682507", "talent": "0", "fightSkill": "0", "mineNeed": "428", "time": "262800", "preLimit": "3|3|3", "columnLimit": "95|95|95"}, {"id": 10960041, "level": 96, "rank": 4, "limit": 3, "prePlot": "10960031|10960032", "skill": "682512", "talent": "0", "fightSkill": "0", "mineNeed": "428", "time": "262800", "preLimit": "3|3|3", "columnLimit": "95|95|95"}, {"id": 10960051, "level": 96, "rank": 5, "limit": 1, "prePlot": "10960041", "skill": "682519", "talent": "0", "fightSkill": "0", "mineNeed": "428", "time": "262800", "preLimit": "3|3|3", "columnLimit": "95|95|95"}, {"id": 10960061, "level": 96, "rank": 6, "limit": 3, "prePlot": "10960051", "skill": "682506", "talent": "0", "fightSkill": "0", "mineNeed": "428", "time": "262800", "preLimit": "1|1|1", "columnLimit": "95|95|95"}, {"id": 10960062, "level": 96, "rank": 6, "limit": 3, "prePlot": "10960051", "skill": "682508", "talent": "0", "fightSkill": "0", "mineNeed": "428", "time": "262800", "preLimit": "1|1|1", "columnLimit": "95|95|95"}, {"id": 10960071, "level": 96, "rank": 7, "limit": 3, "prePlot": "10960061|10960062", "skill": "682518", "talent": "0", "fightSkill": "0", "mineNeed": "428", "time": "262800", "preLimit": "3|3|3", "columnLimit": "95|95|95"}, {"id": 10960081, "level": 96, "rank": 8, "limit": 1, "prePlot": "10960071", "skill": "682006", "talent": "0", "fightSkill": "0", "mineNeed": "428", "time": "262800", "preLimit": "3|3|3", "columnLimit": "95|95|95"}, {"id": 10960091, "level": 96, "rank": 9, "limit": 1, "prePlot": "10960081", "skill": "682534", "talent": "0", "fightSkill": "0", "mineNeed": "428", "time": "262800", "preLimit": "1|1|1", "columnLimit": "95|95|95"}, {"id": 10970011, "level": 97, "rank": 1, "limit": 3, "prePlot": "10960091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "431", "time": "264600", "preLimit": "1|1|1", "columnLimit": "95|95|95"}, {"id": 10970021, "level": 97, "rank": 2, "limit": 3, "prePlot": "10970011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "431", "time": "264600", "preLimit": "3|3|3", "columnLimit": "95|95|95"}, {"id": 10970031, "level": 97, "rank": 3, "limit": 3, "prePlot": "10970021", "skill": "682504", "talent": "0", "fightSkill": "0", "mineNeed": "431", "time": "264600", "preLimit": "3|3|3", "columnLimit": "95|95|95"}, {"id": 10970032, "level": 97, "rank": 3, "limit": 3, "prePlot": "10970021", "skill": "682503", "talent": "0", "fightSkill": "0", "mineNeed": "431", "time": "264600", "preLimit": "3|3|3", "columnLimit": "95|95|95"}, {"id": 10970041, "level": 97, "rank": 4, "limit": 3, "prePlot": "10970031|10970032", "skill": "682513", "talent": "0", "fightSkill": "0", "mineNeed": "431", "time": "264600", "preLimit": "3|3|3", "columnLimit": "95|95|95"}, {"id": 10970051, "level": 97, "rank": 5, "limit": 1, "prePlot": "10970041", "skill": "682522", "talent": "0", "fightSkill": "0", "mineNeed": "431", "time": "264600", "preLimit": "3|3|3", "columnLimit": "95|95|95"}, {"id": 10970061, "level": 97, "rank": 6, "limit": 3, "prePlot": "10970051", "skill": "682505", "talent": "0", "fightSkill": "0", "mineNeed": "431", "time": "264600", "preLimit": "1|1|1", "columnLimit": "95|95|95"}, {"id": 10970062, "level": 97, "rank": 6, "limit": 3, "prePlot": "10970051", "skill": "682507", "talent": "0", "fightSkill": "0", "mineNeed": "431", "time": "264600", "preLimit": "1|1|1", "columnLimit": "95|95|95"}, {"id": 10970071, "level": 97, "rank": 7, "limit": 3, "prePlot": "10970061|10970062", "skill": "682517", "talent": "0", "fightSkill": "0", "mineNeed": "431", "time": "264600", "preLimit": "3|3|3", "columnLimit": "95|95|95"}, {"id": 10970081, "level": 97, "rank": 8, "limit": 1, "prePlot": "10970071", "skill": "682007", "talent": "0", "fightSkill": "0", "mineNeed": "431", "time": "264600", "preLimit": "3|3|3", "columnLimit": "95|95|95"}, {"id": 10970091, "level": 97, "rank": 9, "limit": 1, "prePlot": "10970081", "skill": "0", "talent": "0", "fightSkill": "681005", "mineNeed": "431", "time": "264600", "preLimit": "1|1|1", "columnLimit": "95|95|95"}, {"id": 10980011, "level": 98, "rank": 1, "limit": 3, "prePlot": "10970091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "434", "time": "266400", "preLimit": "1|1|1", "columnLimit": "95|95|95"}, {"id": 10980021, "level": 98, "rank": 2, "limit": 3, "prePlot": "10980011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "434", "time": "266400", "preLimit": "3|3|3", "columnLimit": "95|95|95"}, {"id": 10980031, "level": 98, "rank": 3, "limit": 3, "prePlot": "10980021", "skill": "682506", "talent": "0", "fightSkill": "0", "mineNeed": "434", "time": "266400", "preLimit": "3|3|3", "columnLimit": "95|95|95"}, {"id": 10980032, "level": 98, "rank": 3, "limit": 3, "prePlot": "10980021", "skill": "682508", "talent": "0", "fightSkill": "0", "mineNeed": "434", "time": "266400", "preLimit": "3|3|3", "columnLimit": "95|95|95"}, {"id": 10980041, "level": 98, "rank": 4, "limit": 3, "prePlot": "10980031|10980032", "skill": "682510", "talent": "0", "fightSkill": "0", "mineNeed": "434", "time": "266400", "preLimit": "3|3|3", "columnLimit": "95|95|95"}, {"id": 10980051, "level": 98, "rank": 5, "limit": 1, "prePlot": "10980041", "skill": "682520", "talent": "0", "fightSkill": "0", "mineNeed": "434", "time": "266400", "preLimit": "3|3|3", "columnLimit": "95|95|95"}, {"id": 10980061, "level": 98, "rank": 6, "limit": 3, "prePlot": "10980051", "skill": "682504", "talent": "0", "fightSkill": "0", "mineNeed": "434", "time": "266400", "preLimit": "1|1|1", "columnLimit": "95|95|95"}, {"id": 10980062, "level": 98, "rank": 6, "limit": 3, "prePlot": "10980051", "skill": "682503", "talent": "0", "fightSkill": "0", "mineNeed": "434", "time": "266400", "preLimit": "1|1|1", "columnLimit": "95|95|95"}, {"id": 10980071, "level": 98, "rank": 7, "limit": 3, "prePlot": "10980061|10980062", "skill": "682518", "talent": "0", "fightSkill": "0", "mineNeed": "434", "time": "266400", "preLimit": "3|3|3", "columnLimit": "95|95|95"}, {"id": 10980081, "level": 98, "rank": 8, "limit": 1, "prePlot": "10980071", "skill": "682008", "talent": "0", "fightSkill": "0", "mineNeed": "434", "time": "266400", "preLimit": "3|3|3", "columnLimit": "95|95|95"}, {"id": 10980091, "level": 98, "rank": 9, "limit": 1, "prePlot": "10980081", "skill": "682533", "talent": "0", "fightSkill": "0", "mineNeed": "434", "time": "266400", "preLimit": "1|1|1", "columnLimit": "95|95|95"}, {"id": 10990011, "level": 99, "rank": 1, "limit": 3, "prePlot": "10980091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "437", "time": "268200", "preLimit": "1|1|1", "columnLimit": "95|95|95"}, {"id": 10990021, "level": 99, "rank": 2, "limit": 3, "prePlot": "10990011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "437", "time": "268200", "preLimit": "3|3|3", "columnLimit": "95|95|95"}, {"id": 10990031, "level": 99, "rank": 3, "limit": 3, "prePlot": "10990021", "skill": "682505", "talent": "0", "fightSkill": "0", "mineNeed": "437", "time": "268200", "preLimit": "3|3|3", "columnLimit": "95|95|95"}, {"id": 10990032, "level": 99, "rank": 3, "limit": 3, "prePlot": "10990021", "skill": "682507", "talent": "0", "fightSkill": "0", "mineNeed": "437", "time": "268200", "preLimit": "3|3|3", "columnLimit": "95|95|95"}, {"id": 10990041, "level": 99, "rank": 4, "limit": 3, "prePlot": "10990031|10990032", "skill": "682511", "talent": "0", "fightSkill": "0", "mineNeed": "437", "time": "268200", "preLimit": "3|3|3", "columnLimit": "95|95|95"}, {"id": 10990051, "level": 99, "rank": 5, "limit": 1, "prePlot": "10990041", "skill": "682521", "talent": "0", "fightSkill": "0", "mineNeed": "437", "time": "268200", "preLimit": "3|3|3", "columnLimit": "95|95|95"}, {"id": 10990061, "level": 99, "rank": 6, "limit": 3, "prePlot": "10990051", "skill": "682506", "talent": "0", "fightSkill": "0", "mineNeed": "437", "time": "268200", "preLimit": "1|1|1", "columnLimit": "95|95|95"}, {"id": 10990062, "level": 99, "rank": 6, "limit": 3, "prePlot": "10990051", "skill": "682508", "talent": "0", "fightSkill": "0", "mineNeed": "437", "time": "268200", "preLimit": "1|1|1", "columnLimit": "95|95|95"}, {"id": 10990071, "level": 99, "rank": 7, "limit": 3, "prePlot": "10990061|10990062", "skill": "682517", "talent": "0", "fightSkill": "0", "mineNeed": "437", "time": "268200", "preLimit": "3|3|3", "columnLimit": "95|95|95"}, {"id": 10990081, "level": 99, "rank": 8, "limit": 1, "prePlot": "10990071", "skill": "682009", "talent": "0", "fightSkill": "0", "mineNeed": "437", "time": "268200", "preLimit": "3|3|3", "columnLimit": "95|95|95"}, {"id": 10990091, "level": 99, "rank": 9, "limit": 1, "prePlot": "10990081", "skill": "682536", "talent": "0", "fightSkill": "0", "mineNeed": "437", "time": "268200", "preLimit": "1|1|1", "columnLimit": "95|95|95"}, {"id": 11000011, "level": 100, "rank": 1, "limit": 3, "prePlot": "10990091", "skill": "682502", "talent": "0", "fightSkill": "0", "mineNeed": "440", "time": "270000", "preLimit": "1|1|1", "columnLimit": "95|95|95"}, {"id": 11000021, "level": 100, "rank": 2, "limit": 3, "prePlot": "11000011", "skill": "682501", "talent": "0", "fightSkill": "0", "mineNeed": "440", "time": "270000", "preLimit": "3|3|3", "columnLimit": "95|95|95"}, {"id": 11000031, "level": 100, "rank": 3, "limit": 3, "prePlot": "11000021", "skill": "682504", "talent": "0", "fightSkill": "0", "mineNeed": "440", "time": "270000", "preLimit": "3|3|3", "columnLimit": "95|95|95"}, {"id": 11000032, "level": 100, "rank": 3, "limit": 3, "prePlot": "11000021", "skill": "682503", "talent": "0", "fightSkill": "0", "mineNeed": "440", "time": "270000", "preLimit": "3|3|3", "columnLimit": "95|95|95"}, {"id": 11000041, "level": 100, "rank": 4, "limit": 3, "prePlot": "11000031|11000032", "skill": "682514", "talent": "0", "fightSkill": "0", "mineNeed": "440", "time": "270000", "preLimit": "3|3|3", "columnLimit": "95|95|95"}, {"id": 11000051, "level": 100, "rank": 5, "limit": 1, "prePlot": "11000041", "skill": "682519", "talent": "0", "fightSkill": "0", "mineNeed": "440", "time": "270000", "preLimit": "3|3|3", "columnLimit": "95|95|95"}, {"id": 11000061, "level": 100, "rank": 6, "limit": 3, "prePlot": "11000051", "skill": "682505", "talent": "0", "fightSkill": "0", "mineNeed": "440", "time": "270000", "preLimit": "1|1|1", "columnLimit": "95|95|95"}, {"id": 11000062, "level": 100, "rank": 6, "limit": 3, "prePlot": "11000051", "skill": "682507", "talent": "0", "fightSkill": "0", "mineNeed": "440", "time": "270000", "preLimit": "1|1|1", "columnLimit": "95|95|95"}, {"id": 11000071, "level": 100, "rank": 7, "limit": 3, "prePlot": "11000061|11000062", "skill": "682518", "talent": "0", "fightSkill": "0", "mineNeed": "440", "time": "270000", "preLimit": "3|3|3", "columnLimit": "95|95|95"}, {"id": 11000081, "level": 100, "rank": 8, "limit": 1, "prePlot": "11000071", "skill": "682010", "talent": "0", "fightSkill": "0", "mineNeed": "440", "time": "270000", "preLimit": "3|3|3", "columnLimit": "95|95|95"}, {"id": 11000091, "level": 100, "rank": 9, "limit": 1, "prePlot": "11000081", "skill": "682535", "talent": "0", "fightSkill": "0", "mineNeed": "440", "time": "270000", "preLimit": "1|1|1", "columnLimit": "95|95|95"}]