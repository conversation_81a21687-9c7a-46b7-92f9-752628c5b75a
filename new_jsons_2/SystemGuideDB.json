[{"id": 10000, "type": 2, "triggerType": 1, "triggerPar": "100000", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "你终于来修行啦~这是一棵神奇强大的仙树，它可以对你的力量进行锤炼~拿起你的斧头开始你的锤炼，仙树感知到你的力量，会给你丰厚的回馈哦~", "nextID": 10001, "returnID": "10000", "npcPos": 0}, {"id": 10001, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "每次与仙树的锤炼，装备就会显现出来~再锤炼一次，还会有更新的装备~", "nextID": 10002, "returnID": "10001", "npcPos": 0}, {"id": 10002, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "btnBox", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 10003, "returnID": "0", "npcPos": 0}, {"id": 10003, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "EquipmentReplaceView", "clickRange": "btnEquip", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 10004, "returnID": "0", "npcPos": 0}, {"id": 10004, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "很简单吧~继续挥动斧头吧！", "nextID": 10005, "returnID": "0", "npcPos": 0}, {"id": 10005, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "btnBox", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 10006, "returnID": "0", "npcPos": 0}, {"id": 10006, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "EquipmentReplaceView", "clickRange": "btnEquip", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 10007, "returnID": "0", "npcPos": 0}, {"id": 10007, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "如果遇到用不上的装备便分解掉，还能得到一些灵石和修为~", "nextID": 10008, "returnID": "0", "npcPos": 0}, {"id": 10008, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "btnBox", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 10009, "returnID": "0", "npcPos": 0}, {"id": 10009, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "EquipmentReplaceView", "clickRange": "btnReplace", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 10010, "returnID": "0", "npcPos": 0}, {"id": 10010, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "EquipmentReplaceView", "clickRange": "btnSell", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 11000, "type": 2, "triggerType": 1, "triggerPar": "100007", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "修行之人，总是要外出寻找机缘的。妖也是一样的嘛。你要小心哦！", "nextID": 11001, "returnID": "11000", "npcPos": 0}, {"id": 11001, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "btnFight", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 11002, "returnID": "11000", "npcPos": 0}, {"id": 11002, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "Chapter<PERSON><PERSON><PERSON>iew", "clickRange": "btnBattle", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 12000, "type": 2, "triggerType": 1, "triggerPar": "100029", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "你好厉害呀~修为提升这么快！和我去参加斗法吧!", "nextID": 12001, "returnID": "0", "npcPos": 0}, {"id": 12001, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "在这里你可以和各路小妖进行比试，赢了的话还将获得大量灵石哦！", "nextID": 12002, "returnID": "0", "npcPos": 0}, {"id": 12002, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "btnChallenge", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 12003, "returnID": "12000", "npcPos": 0}, {"id": 12003, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "SystemListView", "clickRange": "btnYaoLing", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 12004, "returnID": "12000", "npcPos": 0}, {"id": 12004, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RankBattleMainView", "clickRange": "btnBattle", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 13000, "type": 2, "triggerType": 1, "triggerPar": "100030", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "哇~你的修为已经能御器飞行了，快来看看都有哪些座驾吧！", "nextID": 13001, "returnID": "0", "npcPos": 0}, {"id": 13001, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "btnCloud", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 13002, "returnID": "13000", "npcPos": 0}, {"id": 13002, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "CloudMainView", "clickRange": "btnUpLv", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 14000, "type": 2, "triggerType": 1, "triggerPar": "100064", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "嗯……想送礼物给姐姐们？好呀，我帮你挑一挑。", "nextID": 14001, "returnID": "0", "npcPos": 0}, {"id": 14001, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "btnHome", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 14002, "returnID": "14000", "npcPos": 0}, {"id": 14002, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "SystemListView", "clickRange": "btnDestiny", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 14003, "returnID": "14000", "npcPos": 0}, {"id": 14003, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "DestinyView", "clickRange": "item0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 14004, "returnID": "14000", "npcPos": 0}, {"id": 14004, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "DestinyDetailView", "clickRange": "btn_send", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 14100, "type": 2, "triggerType": 1, "triggerPar": "100065", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "有体力时~记得多到尘世中走走呀！不仅有好风光，还能遇到仙友呢！", "nextID": 14101, "returnID": "0", "npcPos": 0}, {"id": 14101, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "btnHome", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 14102, "returnID": "14100", "npcPos": 0}, {"id": 14102, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "SystemListView", "clickRange": "btnDestiny", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 14103, "returnID": "14100", "npcPos": 0}, {"id": 14103, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "DestinyView", "clickRange": "tag1", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 14104, "returnID": "14100", "npcPos": 0}, {"id": 14104, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "DestinyView", "clickRange": "btn_travel", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 15000, "type": 2, "triggerType": 1, "triggerPar": "100068", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "又有异兽入侵了！快随我来，也不知道这次来的是……", "nextID": 15001, "returnID": "0", "npcPos": 0}, {"id": 15001, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "btnChallenge", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 15002, "returnID": "15000", "npcPos": 0}, {"id": 15002, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "SystemListView", "clickRange": "btnInvade", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 15003, "returnID": "15000", "npcPos": 0}, {"id": 15003, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "InvadeMain<PERSON>iew", "clickRange": "btnBattle", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 16000, "type": 2, "triggerType": 1, "triggerPar": "100070", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "召唤灵兽后，便能驱使灵兽，和你一起去冒险！", "nextID": 16001, "returnID": "0", "npcPos": 0}, {"id": 16001, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "btnPet", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 16002, "returnID": "16000", "npcPos": 0}, {"id": 16002, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "<PERSON><PERSON><PERSON><PERSON>", "clickRange": "btnCatch", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 16003, "returnID": "16000", "npcPos": 0}, {"id": 16003, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "<PERSON><PERSON><PERSON><PERSON>", "clickRange": "catchBtnCatch", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 16100, "type": 2, "triggerType": 1, "triggerPar": "100069", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "冒险的时候不要忘记带上灵兽呀，快安排灵兽上阵吧", "nextID": 16101, "returnID": "0", "npcPos": 0}, {"id": 16101, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "btnPet", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 16102, "returnID": "16000", "npcPos": 0}, {"id": 16102, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "<PERSON><PERSON><PERSON><PERSON>", "clickRange": "item0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 16103, "returnID": "0", "npcPos": 0}, {"id": 16103, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "<PERSON><PERSON><PERSON><PERSON>", "clickRange": "btnGotoBattle", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 16200, "type": 2, "triggerType": 2, "triggerPar": "51", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "俗话说，兄弟同心，其利断金！灵兽们也能并肩作战哦~快来安排他们一齐出阵吧！", "nextID": 16201, "returnID": "0", "npcPos": 0}, {"id": 16201, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "btnPet", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 16300, "type": 2, "triggerType": 2, "triggerPar": "57", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "你还不知道吧？塑魂会激活灵兽的潜力，让灵兽爆发出超乎寻常的力量哦！", "nextID": 16301, "returnID": "0", "npcPos": 0}, {"id": 16301, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "据说塑魂到一定程度的灵兽，还会变成超酷超酷的姿态呢！", "nextID": 16302, "returnID": "0", "npcPos": 0}, {"id": 16302, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "btnPet", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 17000, "type": 2, "triggerType": 1, "triggerPar": "100060", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "妖王又来了！怎么办，我必定不是妖王的对手……", "nextID": 17001, "returnID": "0", "npcPos": 0}, {"id": 17001, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "对了，你如今修为大涨，一定可以打败妖王！", "nextID": 17002, "returnID": "0", "npcPos": 0}, {"id": 17002, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "btnChallenge", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 17003, "returnID": "17000", "npcPos": 0}, {"id": 17003, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "SystemListView", "clickRange": "btnWildBoss", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 17004, "returnID": "17000", "npcPos": 0}, {"id": 17004, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "WildBossMain<PERSON>iew", "clickRange": "item0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 18000, "type": 2, "triggerType": 1, "triggerPar": "100098", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "咦，你竟然得了召唤令？哇……我最喜欢了！", "nextID": 18001, "returnID": "0", "npcPos": 0}, {"id": 18001, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "我的朋友们也很喜欢呢，带着召唤令，可以吸引我的朋友们~", "nextID": 18002, "returnID": "0", "npcPos": 0}, {"id": 18002, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "btnSpirit", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 18003, "returnID": "18000", "npcPos": 0}, {"id": 18003, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "SpiritMain", "clickRange": "btnOpenDrawCard", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 18004, "returnID": "18000", "npcPos": 0}, {"id": 18004, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "SpiritDrawView", "clickRange": "btnOne", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 18100, "type": 2, "triggerType": 1, "triggerPar": "100099", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "冒险的时候带上精怪一起吧~每个小伙伴都有不同技能呢！", "nextID": 18101, "returnID": "0", "npcPos": 0}, {"id": 18101, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "btnSpirit", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 18102, "returnID": "18100", "npcPos": 0}, {"id": 18102, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "SpiritMain", "clickRange": "btnCombime", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 18103, "returnID": "0", "npcPos": 0}, {"id": 18103, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "SpiritDetailView", "clickRange": "btnCreat", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 18104, "returnID": "0", "npcPos": 0}, {"id": 18104, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "SpiritGetSuccessPopUp", "clickRange": "btnClose", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 18105, "returnID": "0", "npcPos": 0}, {"id": 18105, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "SpiritDetailView", "clickRange": "btnClose", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 18106, "returnID": "0", "npcPos": 0}, {"id": 18106, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "SpiritMain", "clickRange": "btnDispatch", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 18107, "returnID": "0", "npcPos": 0}, {"id": 18107, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "SpiritSelectView", "clickRange": "item0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 18108, "returnID": "0", "npcPos": 0}, {"id": 18108, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "SpiritSelectView", "clickRange": "btnConfirm", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 18200, "type": 2, "triggerType": 1, "triggerPar": "100100", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "得到精怪碎片后，可不要忘了给精怪升级哦~", "nextID": 18201, "returnID": "0", "npcPos": 0}, {"id": 18201, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "btnSpirit", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 18202, "returnID": "18200", "npcPos": 0}, {"id": 18202, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "SpiritMain", "clickRange": "btnUpgrade", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 18203, "returnID": "0", "npcPos": 0}, {"id": 18203, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "SpiritDetailView", "clickRange": "btnUp", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 19000, "type": 2, "triggerType": 1, "triggerPar": "100163", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "镇妖塔是道友们磨练自己实力的地方。正适合如今的你练手。", "nextID": 19001, "returnID": "0", "npcPos": 0}, {"id": 19001, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "塔中的灵芝和灵草都是极好的，能激发灵脉的法宝~年纪比我还大呢。", "nextID": 19002, "returnID": "0", "npcPos": 0}, {"id": 19002, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "btnChallenge", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 19003, "returnID": "19000", "npcPos": 0}, {"id": 19003, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "SystemListView", "clickRange": "btnTower", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 19004, "returnID": "19000", "npcPos": 0}, {"id": 19004, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "TowerMainView", "clickRange": "btnBattle", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 20000, "type": 2, "triggerType": 1, "triggerPar": "100164", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "在镇妖塔得来灵芝灵草可不要浪费呀，它能帮你激发神兽灵脉，我还等你带我去冒险呢。", "nextID": 20001, "returnID": "0", "npcPos": 0}, {"id": 20001, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "btnTalent", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 20002, "returnID": "20000", "npcPos": 0}, {"id": 20002, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "TalentMainView", "clickRange": "btnClick", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 21000, "type": 2, "triggerType": 1, "triggerPar": "100048", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "百年一遇的福地今日开放咯，让我带你去挖点宝贝！", "nextID": 21001, "returnID": "0", "npcPos": 0}, {"id": 21001, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "btnHome", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 21002, "returnID": "21000", "npcPos": 0}, {"id": 21002, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "SystemListView", "clickRange": "btnHomeLand", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 21003, "returnID": "21000", "npcPos": 0}, {"id": 21003, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "HomeLandMainView", "clickRange": "ListItem0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 21004, "returnID": "21000", "npcPos": 0}, {"id": 21004, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "HomeLandTreasureDetail", "clickRange": "btnDispatch", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 21100, "type": 2, "triggerType": 1, "triggerPar": "100050", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "鼠宝不仅可以采集自家福地的资源，还可以采集别人的哦……嘘，别出声，我悄悄带你去。", "nextID": 21101, "returnID": "0", "npcPos": 0}, {"id": 21101, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "btnHome", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 21102, "returnID": "21100", "npcPos": 0}, {"id": 21102, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "SystemListView", "clickRange": "btnHomeLand", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 21103, "returnID": "21100", "npcPos": 0}, {"id": 21103, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "HomeLandMainView", "clickRange": "btnSearch", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 21104, "returnID": "21100", "npcPos": 0}, {"id": 21104, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "HomeLandExploreView", "clickRange": "item0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 21105, "returnID": "21100", "npcPos": 0}, {"id": 21105, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "HomeLandMainView", "clickRange": "ListItem0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 21106, "returnID": "21000", "npcPos": 0}, {"id": 21106, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "HomeLandTreasureDetail", "clickRange": "btnDispatch", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 21200, "type": 2, "triggerType": 1, "triggerPar": "100049", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "福地这么大，你可以多养几只鼠宝，多多采集资源！让我带你去雇佣更多的鼠宝吧！", "nextID": 21201, "returnID": "0", "npcPos": 0}, {"id": 21201, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "btnHome", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 21202, "returnID": "21200", "npcPos": 0}, {"id": 21202, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "SystemListView", "clickRange": "btnHomeLand", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 21203, "returnID": "21200", "npcPos": 0}, {"id": 21203, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "HomeLandMainView", "clickRange": "btnOpenManage", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 21204, "returnID": "21200", "npcPos": 0}, {"id": 21204, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "HomeLandWorkerManageView", "clickRange": "btnBuy", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 22000, "type": 2, "triggerType": 1, "triggerPar": "100013", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "嘘，我告诉你一个得到高级装备的秘诀。", "nextID": 22001, "returnID": "22000", "npcPos": 0}, {"id": 22001, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "仙树也是需要养护的哦，仙树的等级越高，掉落的装备就越好。", "nextID": 22002, "returnID": "22000", "npcPos": 0}, {"id": 22002, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "btnFurnaceLv", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 22003, "returnID": "22001", "npcPos": 0}, {"id": 22003, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "DreamlandView", "clickRange": "btnLvUp", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 23000, "type": 2, "triggerType": 1, "triggerPar": "100005", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "修炼时，也要多多关注自己哦。", "nextID": 23001, "returnID": "0", "npcPos": 0}, {"id": 23001, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "btnMore", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 23002, "returnID": "23000", "npcPos": 0}, {"id": 23002, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "寻常装备能提升你的生命，攻击等，但有些装备还有其他效用，可千万不要错过了。", "nextID": 23003, "returnID": "0", "npcPos": 0}, {"id": 23003, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "比如？比如连击、暴击……嘻嘻，还有很多惊喜呢！不要心急。", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 24000, "type": 2, "triggerType": 1, "triggerPar": "100036", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "锤炼累了不妨解放双手，只需要轻轻一点~就不会错过好装备哦~", "nextID": 24001, "returnID": "0", "npcPos": 0}, {"id": 24001, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "btnAuto", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 24002, "returnID": "24000", "npcPos": 0}, {"id": 24002, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "EquipmentAutoSettingView", "clickRange": "btnSet", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 25000, "type": 2, "triggerType": 1, "triggerPar": "100019", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "恭喜你，你的雷劫已经来了！渡劫后将去往更高的境界，快去准备吧！", "nextID": 25001, "returnID": "0", "npcPos": 0}, {"id": 25001, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "btnAdvanced", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 25002, "returnID": "25000", "npcPos": 0}, {"id": 25002, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RealmsMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "修为达到上限并且完成渡劫任务后，就可以进行渡劫啦！", "nextID": 25003, "returnID": "0", "npcPos": 0}, {"id": 25003, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RealmsMainView", "clickRange": "btn_realms", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 26000, "type": 2, "triggerType": 2, "triggerPar": "37", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "群英榜上群妖云集，如今你也可以大显身手啦！", "nextID": 26001, "returnID": "0", "npcPos": 0}, {"id": 26001, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "btnChallenge", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 26002, "returnID": "0", "npcPos": 0}, {"id": 26002, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "SystemListView", "clickRange": "btnHeroRank", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 27000, "type": 2, "triggerType": 2, "triggerPar": "44", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "哇，你的修为已经可以施放神通啦!好羡慕~", "nextID": 27001, "returnID": "0", "npcPos": 0}, {"id": 27001, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "btnTalent", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 27002, "returnID": "0", "npcPos": 0}, {"id": 27002, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "TalentMainView", "clickRange": "tagListItem1", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 27003, "returnID": "0", "npcPos": 0}, {"id": 27003, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MagicDrawMainView", "clickRange": "btnDraw", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 28000, "type": 2, "triggerType": 2, "triggerPar": "55", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "精炼能提升每个装备位置的效果，能极大的提升装备的属性效果哦。", "nextID": 28001, "returnID": "0", "npcPos": 0}, {"id": 28001, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "btnHome", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 28002, "returnID": "0", "npcPos": 0}, {"id": 28002, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "SystemListView", "clickRange": "btnEquipmentAdvance", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 29000, "type": 2, "triggerType": 2, "triggerPar": "56", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "咦，这是秘境的封印松动了？", "nextID": 29001, "returnID": "0", "npcPos": 0}, {"id": 29001, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "听说秘境里藏着许多宝物，我们快去找找吧。", "nextID": 29002, "returnID": "0", "npcPos": 0}, {"id": 29002, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "btnChallenge", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 29003, "returnID": "0", "npcPos": 0}, {"id": 29003, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "SystemListView", "clickRange": "btnSecretTower", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 30000, "type": 2, "triggerType": 2, "triggerPar": "54", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "仙乐飘飘，天理昭昭。", "nextID": 30001, "returnID": "0", "npcPos": 0}, {"id": 30001, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "仙宫是各位仙官晋升的地方，我们快去观礼吧！", "nextID": 30002, "returnID": "0", "npcPos": 0}, {"id": 30002, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "btnHome", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 30003, "returnID": "0", "npcPos": 0}, {"id": 30003, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "SystemListView", "clickRange": "btnPalace", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 31000, "type": 2, "triggerType": 2, "triggerPar": "63", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "你的修为已经到了能使用法宝的时候了！", "nextID": 31001, "returnID": "0", "npcPos": 0}, {"id": 31001, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "我们快去看看仙域里能找到什么好东西！", "nextID": 31002, "returnID": "0", "npcPos": 0}, {"id": 31002, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "btnMagicTreasure", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 31003, "returnID": "0", "npcPos": 0}, {"id": 31003, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MagicTreasureMainView", "clickRange": "tagListItem1", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 31004, "returnID": "0", "npcPos": 0}, {"id": 31004, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MagicTreasureMainView", "clickRange": "btnOne", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 32000, "type": 2, "triggerType": 2, "triggerPar": "69", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "挑战星宿，勇战群星，方能检验实力，参悟玄机！", "nextID": 32001, "returnID": "0", "npcPos": 0}, {"id": 32001, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "星宿试炼已开启，道友快随我来看看吧！", "nextID": 32002, "returnID": "0", "npcPos": 0}, {"id": 32002, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "btnChallenge", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 32003, "returnID": "0", "npcPos": 0}, {"id": 32003, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "SystemListView", "clickRange": "btnRemoteStarTrial", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 32004, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteStarTrialMainView", "clickRange": "btnAttack", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 33000, "type": 2, "triggerType": 2, "triggerPar": "70", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "腾蛇圣地是小妖修行的圣地，唤醒灵阵即可进行修行，跟我来看看吧！", "nextID": 33001, "returnID": "0", "npcPos": 0}, {"id": 33001, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "btnHome", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 33002, "returnID": "0", "npcPos": 0}, {"id": 33002, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "SystemListView", "clickRange": "btnGatherEnergy", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 33003, "returnID": "0", "npcPos": 0}, {"id": 33003, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "GatherEnergyMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "这里便是腾蛇圣地，我们消耗信物即可开启灵阵", "nextID": 33004, "returnID": "0", "npcPos": 0}, {"id": 33004, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "GatherEnergyMainView", "clickRange": "btnEntrance2", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 33005, "returnID": "0", "npcPos": 0}, {"id": 33005, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "GatherEnergyOpenView", "clickRange": "btnOpen", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 33006, "returnID": "0", "npcPos": 0}, {"id": 33006, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "GatherEnergyResultWindow1", "clickRange": "btnGo", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 33007, "returnID": "0", "npcPos": 0}, {"id": 33007, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "GatherEnergyInsideView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "灵阵成功开启，快点喊上好友一起来修行吧！", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 34000, "type": 2, "triggerType": 2, "triggerPar": "76", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "瑞气冲霄，仙台降临？你的修为已经大圆满了！这个世界对你来说太脆弱，是时候飞升到上界，再续修真之旅啦！", "nextID": 34001, "returnID": "0", "npcPos": 0}, {"id": 34001, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "btnAscensionTest", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 35000, "type": 2, "triggerType": 2, "triggerPar": "80", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "塑造分身成功！现在你可以借助分身再度筑基！尝试搭配不同的组合，开拓属于你的证道之路吧！", "nextID": 35001, "returnID": "0", "npcPos": 0}, {"id": 35001, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "btnGodBody", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 36100, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "ImmortalIslandGameView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "蓬莱仙岛灵气充沛，或者前面的灵脉中就有你的机缘~快去看看吧。", "nextID": 36101, "returnID": "0", "npcPos": 0}, {"id": 36101, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "ImmortalIslandGameView", "clickRange": "cityItem1", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 36102, "returnID": "0", "npcPos": 0}, {"id": 36102, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "ImmortalIslandGameView", "clickRange": "btn_go", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 36103, "returnID": "0", "npcPos": 0}, {"id": 36103, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "ImmortalIslandGameView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "天地有常，灵脉有定量，修仙者人人争夺，各处皆有守卫把守，小心前往~", "nextID": 36104, "returnID": "0", "npcPos": 0}, {"id": 36104, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "ImmortalIslandGameView", "clickRange": "btn_line0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 36105, "returnID": "0", "npcPos": 0}, {"id": 36105, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "ImmortalIslandCityLineView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "进攻时遇到其他妖盟的道友，不妨以法会友，切磋一二~", "nextID": 36106, "returnID": "0", "npcPos": 0}, {"id": 36106, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "ImmortalIslandCityLineView", "clickRange": "btn_defend", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 36107, "returnID": "0", "npcPos": 0}, {"id": 36107, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "ImmortalIslandCityLineView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "当进攻时未遇其他妖盟的道友，可以和防守队列的守卫交手哦。清除守卫后可以占领该灵脉。", "nextID": 36108, "returnID": "0", "npcPos": 0}, {"id": 36108, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "ImmortalIslandCityLineView", "clickRange": "btn_battle", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 36109, "returnID": "0", "npcPos": 0}, {"id": 36109, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "ImmortalIslandCityLineView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "战斗需比拼灵力，每场战斗持续固定时间。战败后灵力将被清空，且回到大本营休养吧~", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 36200, "type": 1, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "ImmortalIslandGameView", "clickRange": "btn_ball", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 36201, "returnID": "0", "npcPos": 0}, {"id": 36201, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "ImmortalIslandSpiritualBallView", "clickRange": "btn_ball", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 36202, "returnID": "0", "npcPos": 0}, {"id": 36202, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "ImmortalIslandUseSpiritualView", "clickRange": "btnAdd", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 37000, "type": 2, "triggerType": 2, "triggerPar": "84", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "世界这么大，想不想去看看？征战诸天已开启，四野八荒任你闯荡，快去征战八荒，问鼎诸天吧！", "nextID": 37001, "returnID": "0", "npcPos": 0}, {"id": 37001, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "btnChallenge", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 37002, "returnID": "0", "npcPos": 0}, {"id": 37002, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "SystemListView", "clickRange": "btnSkyWar", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 37003, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "SkyWarMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "我们成功达到十界，将开启仙魔分身对战，比拼分身实力的时候到了！", "nextID": 37004, "returnID": "0", "npcPos": 0}, {"id": 37004, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "SkyWarMainView", "clickRange": "btnPosition", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 38000, "type": 2, "triggerType": 2, "triggerPar": "91", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "法则之下，五行各有其道，传闻内有五行元素，各自守护宝藏，愿道友挑战成功，夺得宝藏！", "nextID": 38001, "returnID": "0", "npcPos": 0}, {"id": 38001, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "btnChallenge", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 38002, "returnID": "0", "npcPos": 0}, {"id": 38002, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "SystemListView", "clickRange": "btnRuleTrial", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 39000, "type": 2, "triggerType": 2, "triggerPar": "90", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友的修行已经可以参悟天地法则，感悟天地法则可以大大提升实力哦！", "nextID": 39001, "returnID": "0", "npcPos": 0}, {"id": 39001, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "btnWorldRule", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 39002, "returnID": "0", "npcPos": 0}, {"id": 39002, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "WorldRuleMainView", "clickRange": "comTouch", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 39003, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "WorldRuleMainView", "clickRange": "btnPerception", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 39004, "returnID": "0", "npcPos": 0}, {"id": 39004, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "WorldRulePerceptionView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "开始感悟前，我们可以对想要的法则品质和属性进行设置，感悟到相应的品质或属性将会提示道友~", "nextID": 39005, "returnID": "0", "npcPos": 0}, {"id": 39005, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "WorldRulePerceptionView", "clickRange": "btnAutoIntercept", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 40000, "type": 4, "triggerType": 2, "triggerPar": "98", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 124003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 41000, "type": 2, "triggerType": 2, "triggerPar": "97", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友，新的挑战来临，山海万象已经开启，跟我来一起探索一番。", "nextID": 41001, "returnID": "0", "npcPos": 0}, {"id": 41001, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "btnChallenge", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 41002, "returnID": "0", "npcPos": 0}, {"id": 41002, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "SystemListView", "clickRange": "btnMountainSea", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 41003, "returnID": "0", "npcPos": 0}, {"id": 41003, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteVientianeMain<PERSON>iew", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "山海中充满着各种上古异兽，记得和其他道友组队挑战哦！", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 42000, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "TeamBattleMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "作为队长只有知己知彼，调整对应的策略，首先我们查看山海异兽属性。", "nextID": 42001, "returnID": "0", "npcPos": 0}, {"id": 42001, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "TeamBattleMainView", "clickRange": "btnInfo", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 42002, "returnID": "0", "npcPos": 0}, {"id": 42002, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteVientianeBossDetailView", "clickRange": "btnSkill", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 42003, "returnID": "0", "npcPos": 0}, {"id": 42003, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteVientianeBossDetailView", "clickRange": "btnClose", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 42004, "returnID": "0", "npcPos": 0}, {"id": 42004, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "TeamBattleMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "只有第一回合可以更换分身，只有最佳分身搭配才能事半功倍。", "nextID": 42005, "returnID": "0", "npcPos": 0}, {"id": 42005, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "TeamBattleMainView", "clickRange": "btnHead", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 42006, "returnID": "0", "npcPos": 0}, {"id": 42006, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "TeamBattleAllotView", "clickRange": "btnClose", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 42007, "returnID": "0", "npcPos": 0}, {"id": 42007, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "TeamBattleMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "记得根据山海异兽效果配置对应的技能哦。", "nextID": 42008, "returnID": "0", "npcPos": 0}, {"id": 42008, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "TeamBattleMainView", "clickRange": "btnSkill", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 42009, "returnID": "0", "npcPos": 0}, {"id": 42009, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "TeamBattleTeamSkillView", "clickRange": "btnSkill", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 42010, "returnID": "0", "npcPos": 0}, {"id": 42010, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "TeamBattleTeamSkillView", "clickRange": "btnClose", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 42011, "returnID": "0", "npcPos": 0}, {"id": 42011, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "TeamBattleMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "准备完成，可以点击开战！", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 43000, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友，红包来，好运到。为你准备了一些红包，快随我来！", "nextID": 43001, "returnID": "0", "npcPos": 0}, {"id": 43001, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "comNewYear", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 43002, "returnID": "0", "npcPos": 0}, {"id": 43002, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "NewYearBagMainView", "clickRange": "btnGet", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 44000, "type": 2, "triggerType": 2, "triggerPar": "8", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "嗨呀，道友修为精进的如此之快，已经在妖界传开啦，就连传说中的应龙都希望能追随于你，快去见见它吧！", "nextID": 44001, "returnID": "0", "npcPos": 0}, {"id": 44001, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "btnFirstRecharge", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 44002, "returnID": "0", "npcPos": 0}, {"id": 44002, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "FirstRechargeView", "clickRange": "btn_preview", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 45000, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "RemoteDragonHomeMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "龙窟内危机重重，但又有丰厚的宝藏等待道友发现", "nextID": 45001, "returnID": "0", "npcPos": 0}, {"id": 45001, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteDragonHomeMainView", "clickRange": "btnChapterReward", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 45002, "returnID": "0", "npcPos": 0}, {"id": 45002, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "RemoteDragonHomeMapBonusView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友进入龙窟探索，通过对应层后可以获得该层奖励", "nextID": 45003, "returnID": "0", "npcPos": 0}, {"id": 45003, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "RemoteDragonHomeMapBonusView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友不断进入龙窟更深层后，将会发现龙族秘宝，可以获得丰厚的秘宝层奖励!", "nextID": 45004, "returnID": "0", "npcPos": 0}, {"id": 45004, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteDragonHomeMapBonusView", "clickRange": "btnClose", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 45005, "returnID": "0", "npcPos": 0}, {"id": 45005, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteDragonHomeMainView", "clickRange": "btnShop", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 45006, "returnID": "0", "npcPos": 0}, {"id": 45006, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "RemoteDragonHomeShopView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友进入龙窟探索，触发事件后可获得龙纹石", "nextID": 45007, "returnID": "0", "npcPos": 0}, {"id": 45007, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "RemoteDragonHomeShopView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "龙纹石可用于兑换丰厚奖励，部分奖励需要道友探索更深层后才能兑换", "nextID": 45008, "returnID": "0", "npcPos": 0}, {"id": 45008, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "RemoteDragonHomeShopView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "快去寻找队友共同探索龙窟秘境吧，前往更深层，获得更多丰厚奖励!", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 46001, "type": 1, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "FairyRabbitMainView", "clickRange": "ShopTag", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 46002, "returnID": "0", "npcPos": 0}, {"id": 46002, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "FairyRabbitMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友获得的兑换货币，可在商店内兑换丰厚奖励！", "nextID": 46003, "returnID": "0", "npcPos": 0}, {"id": 46003, "type": 1, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "FairyRabbitMainView", "clickRange": "UnlockTag", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 46004, "returnID": "0", "npcPos": 0}, {"id": 46004, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "FairyRabbitMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "商店内有许多丰厚奖励，需要满足条件后解锁，解锁后请道友及时兑换~", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 47000, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "TalentMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "可以在神通预设中配置不同的神通组合，用来应对各种的状况。", "nextID": 47001, "returnID": "0", "npcPos": 0}, {"id": 47001, "type": 1, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "TalentMainView", "clickRange": "comboBox", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 48000, "type": 2, "triggerType": 2, "triggerPar": "118", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友，山上有一荒废的宗门，不如将它改头换面，开辟新宗门，快随我来！", "nextID": 48001, "returnID": "0", "npcPos": 0}, {"id": 48001, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "btnHome", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 48002, "returnID": "0", "npcPos": 0}, {"id": 48002, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "SystemListView", "clickRange": "btnPupil", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 48003, "returnID": "0", "npcPos": 0}, {"id": 48003, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemotePupilMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友，点击招收，仙友自会带着寻访的小妖上门！", "nextID": 48004, "returnID": "0", "npcPos": 0}, {"id": 48004, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemotePupilMainView", "clickRange": "btn_recruit", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 48005, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "RemotePupilGetPupilView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "天道酬勤，招收到弟子之后，记得修炼哦！", "nextID": 48006, "returnID": "0", "npcPos": 0}, {"id": 48006, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemotePupilGetPupilView", "clickRange": "btn_goto", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 48007, "returnID": "0", "npcPos": 0}, {"id": 48007, "type": 1, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "RemotePupilMainView", "clickRange": "btn_train", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 48010, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "RemotePupilGraduationView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "弟子出师后当广结同道，其他道友的弟子之中，若有投缘之人，不妨结为伙伴。", "nextID": 48011, "returnID": "0", "npcPos": 0}, {"id": 48011, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemotePupilGraduationView", "clickRange": "btn_goto_marry", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 48012, "type": 1, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "RemotePupilMainView", "clickRange": "btnMarry", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 48013, "type": 1, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "RemotePupilMarryView", "clickRange": "btnMarry", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 48014, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "RemotePupilMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "弟子结伴后可获得对方弟子的属性加成，快随我来设为宗门的长老！", "nextID": 48015, "returnID": "0", "npcPos": 0}, {"id": 48015, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemotePupilMainView", "clickRange": "tag_train", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 48016, "returnID": "0", "npcPos": 0}, {"id": 48016, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemotePupilMainView", "clickRange": "btn_fight", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 48017, "returnID": "0", "npcPos": 0}, {"id": 48017, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemotePupilFightView", "clickRange": "position_1", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 48018, "type": 1, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "RemotePupilFightPositionView", "clickRange": "btn_position", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 48019, "returnID": "0", "npcPos": 0}, {"id": 48019, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemotePupilFightPositionView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "长老就位，宗门已初具规模，再接再励！", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 49000, "type": 2, "triggerType": 2, "triggerPar": "117", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "灵兽可以装备内丹了，快去看看吧~", "nextID": 49001, "returnID": "49000", "npcPos": 0}, {"id": 49001, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "btnPet", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 49002, "returnID": "0", "npcPos": 0}, {"id": 49002, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "<PERSON><PERSON><PERSON><PERSON>", "clickRange": "kernelTag", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 49003, "returnID": "0", "npcPos": 0}, {"id": 49003, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "<PERSON><PERSON><PERSON><PERSON>", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "目前你的灵兽还没有内丹，快去抽取内丹吧", "nextID": 49004, "returnID": "0", "npcPos": 0}, {"id": 49004, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "<PERSON><PERSON><PERSON><PERSON>", "clickRange": "kernelDrawBtn", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 49005, "returnID": "0", "npcPos": 0}, {"id": 49005, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "PetKernelDrawMainView", "clickRange": "btnOne", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 49100, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "PetKernelDrawMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "哇哦！恭喜你获得灵兽内丹，我们快去让灵兽把它装备上吧！", "nextID": 49101, "returnID": "0", "npcPos": 0}, {"id": 49101, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "PetKernelDrawMainView", "clickRange": "btnClose", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 49102, "returnID": "0", "npcPos": 0}, {"id": 49102, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "<PERSON><PERSON><PERSON><PERSON>", "clickRange": "kernelListBtn", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 49103, "returnID": "0", "npcPos": 0}, {"id": 49103, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "<PERSON><PERSON><PERSON><PERSON>", "clickRange": "listFirst", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 49104, "returnID": "0", "npcPos": 0}, {"id": 49104, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "PetsPetKernelDetailView", "clickRange": "btnAdd", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 49105, "returnID": "0", "npcPos": 0}, {"id": 49105, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "PetsPetKernelDetailView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "恭喜已装备内丹，接下来收集更多强力的内丹来提高灵兽的实力吧~", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 49200, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "恭喜道友获得洪荒灵兽，洪荒灵兽拥有特殊的灵脉和天赋，随我来看看！", "nextID": 49201, "returnID": "0", "npcPos": 0}, {"id": 49201, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "btnPet", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 49202, "returnID": "0", "npcPos": 0}, {"id": 49202, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "<PERSON><PERSON><PERSON><PERSON>", "clickRange": "btnAwaken", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 49203, "returnID": "0", "npcPos": 0}, {"id": 49203, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteChaoticPetMain", "clickRange": "comAddtition", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 49204, "returnID": "0", "npcPos": 0}, {"id": 49204, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteChaoticPetMain", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "洪荒灵兽可以激活灵脉获得拥有加成，拥有加成无需灵兽上阵即可生效！", "nextID": 49205, "returnID": "0", "npcPos": 0}, {"id": 49205, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteChaoticPetMain", "clickRange": "btnSelect2", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 49206, "returnID": "0", "npcPos": 0}, {"id": 49206, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteChaoticPetMain", "clickRange": "comAddtition2", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 49207, "returnID": "0", "npcPos": 0}, {"id": 49207, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteChaoticPetMain", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "洪荒天赋有特殊的天赋技能效果，无需上阵即可在战斗中触发！", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 49300, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "RemoteCastSwordMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "欢迎道友来到炼器大会，在炼器大会中，道友可以逐步从材料合成到仙器！", "nextID": 49301, "returnID": "0", "npcPos": 0}, {"id": 49301, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteCastSwordMainView", "clickRange": "guide1", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 49302, "returnID": "0", "npcPos": 0}, {"id": 49302, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteCastSwordMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "合成到对应阶段后，道友可以获得合成奖励，合成奖励每日0点重置，请注意及时领取！", "nextID": 49303, "returnID": "0", "npcPos": 0}, {"id": 49303, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteCastSwordMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "如果在合成过程中遇到阻碍，道友可以使用炼器道具辅助道友合成更高级仙器！", "nextID": 49304, "returnID": "0", "npcPos": 0}, {"id": 49304, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteCastSwordMainView", "clickRange": "guide2", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 49305, "returnID": "0", "npcPos": 0}, {"id": 49305, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "GoodsUseSimpleView", "clickRange": "btnClose", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 50000, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "位面的通道已经打通了，现在妖盟可以招收其它位面的道友啦！", "nextID": 50001, "returnID": "0", "npcPos": 0}, {"id": 50001, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "btnUnion", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 50002, "returnID": "0", "npcPos": 0}, {"id": 50002, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "UnionMainView", "clickRange": "btnRank", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 50003, "returnID": "0", "npcPos": 0}, {"id": 50003, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "UnionRankView", "clickRange": "btn_rate", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 50004, "returnID": "0", "npcPos": 0}, {"id": 50004, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "UnionRateRankView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "此处可查看各个道友的实力评级", "nextID": 50005, "returnID": "0", "npcPos": 0}, {"id": 50005, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "UnionRateRankView", "clickRange": "item", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 50006, "returnID": "0", "npcPos": 0}, {"id": 50006, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "UnionRateRankView", "clickRange": "btnClose", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 50007, "returnID": "0", "npcPos": 0}, {"id": 50007, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "UnionMainView", "clickRange": "btnHall", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 50008, "returnID": "0", "npcPos": 0}, {"id": 50008, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "UnionHallView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "此处可查看妖盟可招收各评级道友的数量", "nextID": 50009, "returnID": "0", "npcPos": 0}, {"id": 50009, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "UnionHallView", "clickRange": "com_rateVec", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 50100, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "UnionHallView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "快去结识更多其它位面的道友吧", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 50200, "type": 1, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "UnionHallView", "clickRange": "btnNotice", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 50201, "returnID": "0", "npcPos": 0}, {"id": 50201, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "UnionNoticeView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "盟主/副盟主可设置妖盟里招募的各评级人数", "nextID": 50202, "returnID": "0", "npcPos": 0}, {"id": 50202, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "UnionNoticeView", "clickRange": "btn_recruit", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 50203, "returnID": "0", "npcPos": 0}, {"id": 50203, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "UnionSetRecruitView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "此处显示妖盟目前各评级人数上限", "nextID": 50204, "returnID": "0", "npcPos": 0}, {"id": 50204, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "UnionSetRecruitView", "clickRange": "com_guide", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 50205, "returnID": "0", "npcPos": 0}, {"id": 50205, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "UnionSetRecruitView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "这里可以将妖盟内高评级的人数上限分配到低评级的人数上限", "nextID": 50206, "returnID": "0", "npcPos": 0}, {"id": 50206, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "UnionSetRecruitView", "clickRange": "list", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 50207, "returnID": "0", "npcPos": 0}, {"id": 50207, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "UnionSetRecruitView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "快去邀请更多其它位面的道友加入妖盟吧！", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 50300, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "位面的通道已经打通了，现在可以加入其他位面的妖盟啦", "nextID": 50301, "returnID": "0", "npcPos": 0}, {"id": 50301, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "btnUnion", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 50302, "returnID": "0", "npcPos": 0}, {"id": 50302, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "UnionEnter", "clickRange": "btn_join", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 50303, "returnID": "0", "npcPos": 0}, {"id": 50303, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "UnionJoinView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "此处显示自己的实力评级", "nextID": 50304, "returnID": "0", "npcPos": 0}, {"id": 50304, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "UnionJoinView", "clickRange": "com_rate", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 50305, "returnID": "0", "npcPos": 0}, {"id": 50305, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "UnionJoinView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "此处可查看妖盟内各个评级人数和招收上限", "nextID": 50306, "returnID": "0", "npcPos": 0}, {"id": 50306, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "UnionJoinView", "clickRange": "com_rateVec", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 50307, "returnID": "0", "npcPos": 0}, {"id": 50307, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "UnionJoinView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友快去加入自己心仪的妖盟吧！", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 50401, "type": 2, "triggerType": 2, "triggerPar": "129", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "座驾可以注灵了，快去看看吧~", "nextID": 50402, "returnID": "0", "npcPos": 0}, {"id": 50402, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "btnCloudSmail", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 50403, "returnID": "0", "npcPos": 0}, {"id": 50403, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "CloudMainView", "clickRange": "listTag", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 50404, "returnID": "0", "npcPos": 0}, {"id": 50404, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "CloudMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "拥有材料后可进行座驾注灵", "nextID": 50405, "returnID": "0", "npcPos": 0}, {"id": 50405, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "CloudMainView", "clickRange": "btnCharge", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 50406, "returnID": "0", "npcPos": 0}, {"id": 50406, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "CloudMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "座驾注灵将在6个抗性中随机一个进行升级", "nextID": 50407, "returnID": "0", "npcPos": 0}, {"id": 50407, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "CloudMainView", "clickRange": "comResist", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 50408, "returnID": "0", "npcPos": 0}, {"id": 50408, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "CloudMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "座驾注灵同时将提升注灵经验，经验满后可升级", "nextID": 50409, "returnID": "0", "npcPos": 0}, {"id": 50409, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "CloudMainView", "clickRange": "progressCharge", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 50410, "returnID": "0", "npcPos": 0}, {"id": 50410, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "CloudMainView", "clickRange": "btnInfo", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 50411, "returnID": "0", "npcPos": 0}, {"id": 50411, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "CloudChargeAdditionView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "这里可以查看座驾注灵的加成预览，快去收集注灵晶石进行座驾注灵吧~", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 50500, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "恭喜获得了【邀请帖】，快去尝试开宴吧", "nextID": 50501, "returnID": "0", "npcPos": 0}, {"id": 50501, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "btnHome", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 50502, "returnID": "0", "npcPos": 0}, {"id": 50502, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "SystemListView", "clickRange": "btnBag", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 50503, "returnID": "0", "npcPos": 0}, {"id": 50503, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "PropBagMainView", "clickRange": "tagSpecial", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 50504, "type": 1, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "PropBagMainView", "clickRange": "peachBanquetItem", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 50505, "returnID": "0", "npcPos": 0}, {"id": 50505, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "GoodsUseSpecialView", "clickRange": "btn_use", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 50506, "returnID": "0", "npcPos": 0}, {"id": 50506, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemotePeachBanquetOpenView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "消耗【邀请帖】可开设宴会，快去试试吧", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 50600, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "RemoteExploreMainView", "clickRange": "0", "clickNum": 1, "character": 144043, "sound": "0", "desc": "这里就是血色禁地了，筑基丹所需的药材唯有此处才能找到..", "nextID": 50601, "returnID": "0", "npcPos": 0}, {"id": 50601, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteExploreMainView", "clickRange": "0", "clickNum": 1, "character": 144043, "sound": "0", "desc": "不过，这里面危机重重，栖息着各类强大的妖兽，还得万事小心才行。", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 50602, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "RemoteExploreMainView", "clickRange": "0", "clickNum": 1, "character": 144043, "sound": "0", "desc": "呼，终于成功了...多亏婉儿小姐的相助，我们才能击败墨蛟。", "nextID": 50603, "returnID": "0", "npcPos": 0}, {"id": 50603, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteExploreMainView", "clickRange": "0", "clickNum": 1, "character": 10016, "sound": "0", "desc": "哼，今日之事权当是一场梦，如果你敢让第三个人知道..我会让你明白后果的。", "nextID": 50604, "returnID": "0", "npcPos": 1}, {"id": 50604, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteExploreMainView", "clickRange": "0", "clickNum": 1, "character": 144043, "sound": "0", "desc": "呃...一定一定。", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 50700, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "RankBattleMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "位面的通道已经打通了，现在可以与来自大千位面的道友进行斗法比拼！", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 50800, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteMonopolyGameMapView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友首次进入宝域，宝域内蕴含许多未知的宝藏~", "nextID": 50801, "returnID": "50800", "npcPos": 0}, {"id": 50801, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteMonopolyGameMapView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "在宝域内可使用罗盘进行寻宝~", "nextID": 50802, "returnID": "50800", "npcPos": 0}, {"id": 50802, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteMonopolyGameMapView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "寻宝会获得化外通宝、聚福灵璧以及触发对应的事件~", "nextID": 50803, "returnID": "0", "npcPos": 0}, {"id": 50803, "type": 3, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "RemoteMonopolyGameMapView", "clickRange": "firstBuilding", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 50804, "returnID": "0", "npcPos": 0}, {"id": 50804, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteMonopolyGameMapView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "移动到四圣雕像处可获得四圣之力~", "nextID": 50805, "returnID": "0", "npcPos": 0}, {"id": 50805, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteMonopolyGameMapView", "clickRange": "achievementComp", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 50806, "returnID": "0", "npcPos": 0}, {"id": 50806, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteMonopolyAchievementsView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "妖盟全体成员累计四圣之力可以获得妖盟成就奖励！", "nextID": 50807, "returnID": "0", "npcPos": 0}, {"id": 50807, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteMonopolyAchievementsView", "clickRange": "closeBtn", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 50808, "returnID": "0", "npcPos": 0}, {"id": 50808, "type": 3, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "RemoteMonopolyGameMapView", "clickRange": "spePath<PERSON>ove", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 50809, "returnID": "0", "npcPos": 0}, {"id": 50809, "type": 1, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "RemoteMonopolyGameMapView", "clickRange": "spe<PERSON><PERSON>", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 50810, "returnID": "0", "npcPos": 0}, {"id": 50810, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteMonopolyMapEventView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友若运气满满，则有机会进入密道获得更为丰厚的宝域奖励~", "nextID": 50811, "returnID": "0", "npcPos": 0}, {"id": 50811, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteMonopolyMapEventView", "clickRange": "closeBtn", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 50812, "returnID": "0", "npcPos": 0}, {"id": 50812, "type": 3, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "RemoteMonopolyGameMapView", "clickRange": "player", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 50813, "returnID": "0", "npcPos": 0}, {"id": 50813, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "RemoteMonopolyGameMapView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "宝域内还有许多奇遇事件等着道友前去探索！", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 50900, "type": 2, "triggerType": 2, "triggerPar": "136", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友，妖盟里发布了最新的悬赏任务，完成之后可以获得丰厚奖励，快随我来看看!", "nextID": 50901, "returnID": "0", "npcPos": 0}, {"id": 50901, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "btnUnion", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 50902, "returnID": "0", "npcPos": 0}, {"id": 50902, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "UnionMainView", "clickRange": "btnUnionBounty", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 50903, "returnID": "0", "npcPos": 0}, {"id": 50903, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "UnionBountyGameView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友，快看已经好多成员接取了悬赏任务，我们也出发吧。", "nextID": 50904, "returnID": "0", "npcPos": 0}, {"id": 50904, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "UnionBountyGameView", "clickRange": "btnBounty", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 50905, "returnID": "0", "npcPos": 0}, {"id": 50905, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "RemoteUnionBountyEscortMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "悬赏任务品质越高奖励越丰厚，悬赏任务不能更换!", "nextID": 50906, "returnID": "0", "npcPos": 0}, {"id": 50906, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteUnionBountyEscortMainView", "clickRange": "btnEscort", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 50907, "returnID": "0", "npcPos": 0}, {"id": 50907, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "UnionBountyGameView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "我们已经成功接取悬赏任务，目前地图上可以查看其他道友的任务，并且可以进行挑战。", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 51001, "type": 2, "triggerType": 2, "triggerPar": "143", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友可以通过点击箭头或滑动查看小世界面板哦！", "nextID": 51002, "returnID": "0", "npcPos": 0}, {"id": 51002, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "btnRight", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 51003, "returnID": "0", "npcPos": 0}, {"id": 51003, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "小世界已开启，快去看看吧~", "nextID": 51004, "returnID": "0", "npcPos": 0}, {"id": 51004, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "comUniverseTouch", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 51005, "returnID": "0", "npcPos": 0}, {"id": 51005, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteUniverseMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "小世界会产出造化石，造化石可用于天道轮台衍取先天灵气", "nextID": 51006, "returnID": "0", "npcPos": 0}, {"id": 51006, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteUniverseMainView", "clickRange": "comGoods", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 51007, "returnID": "0", "npcPos": 0}, {"id": 51007, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteUniverseMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "小世界通过消耗先天灵气进行升级，升级后可以带来属性加成", "nextID": 51008, "returnID": "0", "npcPos": 0}, {"id": 51008, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteUniverseMainView", "clickRange": "btnlvUpInfo", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 51009, "returnID": "0", "npcPos": 0}, {"id": 51009, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteUniverseMainView", "clickRange": "tag1", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 51010, "returnID": "0", "npcPos": 0}, {"id": 51010, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteUniverseMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "消耗造化石可开启天道轮台，抽取到的先天灵气可用于小世界升级", "nextID": 51011, "returnID": "0", "npcPos": 0}, {"id": 51011, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteUniverseMainView", "clickRange": "comRoulette3", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 51012, "returnID": "0", "npcPos": 0}, {"id": 51012, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteUniverseMainView", "clickRange": "tag2", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 51013, "returnID": "0", "npcPos": 0}, {"id": 51013, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteUniverseMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "小世界将作为一个战斗单位，影响到玩家的日常对战", "nextID": 51014, "returnID": "0", "npcPos": 0}, {"id": 51014, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteUniverseMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "小世界带有3个类型的天地玄诀，分别为仙咒、攻伐、守御，需要携带仙咒后，战斗中才会生效", "nextID": 51015, "returnID": "0", "npcPos": 0}, {"id": 51015, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteUniverseMainView", "clickRange": "comSlot0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 51016, "returnID": "0", "npcPos": 0}, {"id": 51016, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteUniverseMainView", "clickRange": "tag3", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 51017, "returnID": "0", "npcPos": 0}, {"id": 51017, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteUniverseMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友可以消耗太虚元石在这里参悟世界本源，获取天地玄诀", "nextID": 51018, "returnID": "0", "npcPos": 0}, {"id": 51018, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteUniverseMainView", "clickRange": "btnOne", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 51019, "returnID": "0", "npcPos": 0}, {"id": 51019, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteUniverseDrawOne", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "哇哦！恭喜你获得仙咒，我们快去把把它镶嵌上在小世界上吧！", "nextID": 51020, "returnID": "0", "npcPos": 0}, {"id": 51020, "type": 3, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteUniverseSkillActivatedView", "clickRange": "waitEvent", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 51021, "returnID": "0", "npcPos": 0}, {"id": 51021, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteUniverseMainView", "clickRange": "tag2", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 51022, "returnID": "0", "npcPos": 0}, {"id": 51022, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteUniverseMainView", "clickRange": "listSkill", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 51023, "returnID": "0", "npcPos": 0}, {"id": 51023, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteUniverseSkillDetailView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "小世界玄诀激活后可以获得属性加成效果，出战后可以生效玄诀效果", "nextID": 51024, "returnID": "0", "npcPos": 0}, {"id": 51024, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteUniverseSkillDetailView", "clickRange": "btnChangeSkill", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 52000, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "RemotePaintingFairylandGameView", "clickRange": "0", "clickNum": 1, "character": 125017, "sound": "0", "desc": "此地乃山河社稷图之内，虽然灵气充盈，但若想纳为己用，普通修士至少也需在此修行个百八十载。", "nextID": 52001, "returnID": "0", "npcPos": 0}, {"id": 52001, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemotePaintingFairylandGameView", "clickRange": "0", "clickNum": 1, "character": 125017, "sound": "0", "desc": "不过若能学会吾所传授的心法，最多十年八载，汝便可随心吸纳此地之灵气。", "nextID": 52002, "returnID": "0", "npcPos": 0}, {"id": 52002, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemotePaintingFairylandGameView", "clickRange": "0", "clickNum": 1, "character": 125017, "sound": "0", "desc": "来，与吾同念……思山即山，思水即水，如四象变化，有无穷之妙……", "nextID": 52003, "returnID": "0", "npcPos": 0}, {"id": 52003, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemotePaintingFairylandGameView", "clickRange": "0", "clickNum": 1, "character": -1, "sound": "0", "desc": "思山即山，思水即水，如四象…噢，我好像已经感受到灵气进入到我的丹田内了！", "nextID": 52004, "returnID": "0", "npcPos": 1}, {"id": 52004, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemotePaintingFairylandGameView", "clickRange": "0", "clickNum": 1, "character": 125017, "sound": "0", "desc": "我滴个乖乖，居然一学就会，天纵奇才！", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 53001, "type": 2, "triggerType": 2, "triggerPar": "142", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "心魔劫已滋生，需净化并将其彻底斩除", "nextID": 53002, "returnID": "0", "npcPos": 0}, {"id": 53002, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "btnFairyLand", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 53003, "returnID": "0", "npcPos": 0}, {"id": 53003, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "FairyLandMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友心魔滋生，成功突破魔障后可以飞升", "nextID": 53004, "returnID": "0", "npcPos": 0}, {"id": 53004, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "FairyLandMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "心魔即为道友的另一面，将拥有道友一定的实力", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 54000, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "RemoteSkyTradeGameMapView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "四海八荒，珍奇无数。倘若道友能以商证道，让各域的珍宝，通流诸界，那又会怎样一番奇景呢？快来与我共赴仙域商途吧！", "nextID": 54001, "returnID": "0", "npcPos": 0}, {"id": 54001, "type": 3, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "RemoteSkyTradeGameMapView", "clickRange": "btnFirst", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 54002, "returnID": "0", "npcPos": 0}, {"id": 54002, "type": 4, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "RemoteSkyTradeFirstEnterView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 54003, "returnID": "0", "npcPos": 1}, {"id": 54003, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteSkyTradeGameMapView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友，获得灵玉后我们先去购买当前仙域的特产吧。", "nextID": 54004, "returnID": "0", "npcPos": 0}, {"id": 54004, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteSkyTradeGameMapView", "clickRange": "btnTrade", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 54005, "returnID": "0", "npcPos": 1}, {"id": 54005, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteSkyTradeTradeView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友，特产代表该地价格最实惠的珍宝，是我们每次贸易的必购项。", "nextID": 54006, "returnID": "0", "npcPos": 0}, {"id": 54006, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteSkyTradeTradeView", "clickRange": "btnClose", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 54007, "returnID": "0", "npcPos": 0}, {"id": 54007, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteSkyTradeGameMapView", "clickRange": "btnLog", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 54008, "returnID": "0", "npcPos": 0}, {"id": 54008, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteSkyTradeLogView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友，这边可以看所有仙域的信息，并前往当前紧缺珍宝的所在仙域。", "nextID": 54009, "returnID": "0", "npcPos": 0}, {"id": 54009, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteSkyTradeLogView", "clickRange": "btnClose", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 54010, "returnID": "0", "npcPos": 0}, {"id": 54010, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteSkyTradeGameMapView", "clickRange": "btnHead", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 54011, "returnID": "0", "npcPos": 0}, {"id": 54011, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteSkyTradeBoatLevelView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友，点击头像可以查看飞行舟等级升级效果", "nextID": 54012, "returnID": "0", "npcPos": 0}, {"id": 54012, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteSkyTradeBoatLevelView", "clickRange": "btnClose", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 54013, "returnID": "0", "npcPos": 0}, {"id": 54013, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteSkyTradeGameMapView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友，一切准备就绪，准备起航。别忘了完成每日任务获得玄晶，可以减少航行时间。", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 54019, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "RemoteSkyTradeBattleMapView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "在仙域中不仅存在着合作，还存在着竞争。每个妖盟都有一个专属据点，攻击据点可以减少该妖盟的本轮声望。", "nextID": 54020, "returnID": "0", "npcPos": 0}, {"id": 54020, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteSkyTradeBattleMapView", "clickRange": "btnUnionFlag", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 54021, "returnID": "0", "npcPos": 0}, {"id": 54021, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "RemoteSkyTradeUnionLevelView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "据点等级越高则耐久值越高，只有进行贸易获取声望提升据点等级，耐久值上限才能提升。", "nextID": 54022, "returnID": "0", "npcPos": 0}, {"id": 54022, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteSkyTradeUnionLevelView", "clickRange": "btnClose", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 54023, "returnID": "0", "npcPos": 0}, {"id": 54023, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "RemoteSkyTradeBattleMapView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "活动期间，19-22点才能挑战其他妖盟据点，每轮有5次挑战机会，每日均会重置，道友可别错过了哦！", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 55000, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "NExploreMainView", "clickRange": "0", "clickNum": 1, "character": 144052, "sound": "0", "desc": "呼…总算溜出来了，看样子没有被师傅发现。", "nextID": 55001, "returnID": "0", "npcPos": 1}, {"id": 55001, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "NExploreMainView", "clickRange": "0", "clickNum": 1, "character": 144052, "sound": "0", "desc": "咦，有股好强的妖气，正好…就让小爷我来会会！", "nextID": 55002, "returnID": "0", "npcPos": 1}, {"id": 55002, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "NExploreMainView", "clickRange": "0", "clickNum": 1, "character": 144052, "sound": "0", "desc": "说不定还能遇到有趣的妖怪，嘿嘿！", "nextID": 0, "returnID": "0", "npcPos": 1}, {"id": 55010, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "NExploreMainView", "clickRange": "0", "clickNum": 1, "character": 144052, "sound": "0", "desc": "（这只妖怪，好像跟别的妖怪不太一样……）", "nextID": 55011, "returnID": "0", "npcPos": 1}, {"id": 55011, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "NExploreMainView", "clickRange": "0", "clickNum": 1, "character": 141001, "sound": "0", "desc": "啊…你你你…你是什么人！？", "nextID": 55012, "returnID": "0", "npcPos": 0}, {"id": 55012, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "NExploreMainView", "clickRange": "0", "clickNum": 1, "character": 144052, "sound": "0", "desc": "我？我是小妖怪，逍遥又自在！", "nextID": 55013, "returnID": "0", "npcPos": 1}, {"id": 55013, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "NExploreMainView", "clickRange": "0", "clickNum": 1, "character": 141001, "sound": "0", "desc": "噢，原来你也是妖怪啊…请你帮帮我，我跟我的两个同伴来此历练，但途中我们走散了……", "nextID": 55014, "returnID": "0", "npcPos": 0}, {"id": 55014, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "NExploreMainView", "clickRange": "0", "clickNum": 1, "character": 144052, "sound": "0", "desc": "没问题，包在小爷我身上了，带我去你们走散的地方吧！", "nextID": 0, "returnID": "0", "npcPos": 1}, {"id": 55020, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "NExploreMainView", "clickRange": "0", "clickNum": 1, "character": 141001, "sound": "0", "desc": "我的好兄弟！我终于找到你了！", "nextID": 55021, "returnID": "0", "npcPos": 0}, {"id": 55021, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "NExploreMainView", "clickRange": "0", "clickNum": 1, "character": 142001, "sound": "0", "desc": "呃，我头好痛…这不是小猪吗，你跑哪去了，他是谁？", "nextID": 55022, "returnID": "0", "npcPos": 0}, {"id": 55022, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "NExploreMainView", "clickRange": "0", "clickNum": 1, "character": 141001, "sound": "0", "desc": "他是我大哥，多亏了他，我才能找到你的！", "nextID": 55023, "returnID": "0", "npcPos": 0}, {"id": 55023, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "NExploreMainView", "clickRange": "0", "clickNum": 1, "character": 142001, "sound": "0", "desc": "噢！既然是小猪的大哥，那也就是我的大哥！请受小弟一拜！", "nextID": 55024, "returnID": "0", "npcPos": 0}, {"id": 55024, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "NExploreMainView", "clickRange": "0", "clickNum": 1, "character": 144052, "sound": "0", "desc": "嘿嘿…好了，我们快出发吧，该去找你们的第三个同伴了！", "nextID": 0, "returnID": "0", "npcPos": 1}, {"id": 55030, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "NExploreMainView", "clickRange": "0", "clickNum": 1, "character": 143001, "sound": "0", "desc": "啊…不要吃我！天蓬元帅跟金翅大鹏可都是我的好兄弟！吃了我他们不会放过你的……", "nextID": 55031, "returnID": "0", "npcPos": 0}, {"id": 55031, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "NExploreMainView", "clickRange": "0", "clickNum": 1, "character": 141001, "sound": "0", "desc": "快醒醒，别说梦话了，是我们啊！", "nextID": 55032, "returnID": "0", "npcPos": 0}, {"id": 55032, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "NExploreMainView", "clickRange": "0", "clickNum": 1, "character": 143001, "sound": "0", "desc": "啊…终于找到你们了！你们到底跑哪去了！", "nextID": 55033, "returnID": "0", "npcPos": 0}, {"id": 55033, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "NExploreMainView", "clickRange": "0", "clickNum": 1, "character": 0, "sound": "0", "desc": "（说完，三只小妖怪便紧紧的拥抱在了一起）", "nextID": 55034, "returnID": "0", "npcPos": 0}, {"id": 55034, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "NExploreMainView", "clickRange": "0", "clickNum": 1, "character": 141001, "sound": "0", "desc": "大哥，为了报答您的恩德！我们三兄弟决定了，要追随于您！从此听从大哥的差遣！", "nextID": 55035, "returnID": "0", "npcPos": 0}, {"id": 55035, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "NExploreMainView", "clickRange": "0", "clickNum": 1, "character": 144052, "sound": "0", "desc": "嘿嘿，那就随我一起去查查这股强大妖气的源头吧！", "nextID": 0, "returnID": "0", "npcPos": 1}, {"id": 55040, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "NExploreMainView", "clickRange": "0", "clickNum": 1, "character": 144052, "sound": "0", "desc": "难怪妖气会如此之强，原来这段时间在岸上强抢童男童女的就是你！受死吧！", "nextID": 55041, "returnID": "0", "npcPos": 1}, {"id": 55041, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "NExploreMainView", "clickRange": "0", "clickNum": 1, "character": 144052, "sound": "0", "desc": "可恶…区区凡人…河妖大人，绝不会放过你的！！", "nextID": 55042, "returnID": "0", "npcPos": 0}, {"id": 55042, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "NExploreMainView", "clickRange": "0", "clickNum": 1, "character": 144052, "sound": "0", "desc": "哼，他要是敢来，小爷我让他知道失败是什么意思！", "nextID": 55043, "returnID": "0", "npcPos": 1}, {"id": 55043, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "NExploreMainView", "clickRange": "0", "clickNum": 1, "character": 144052, "sound": "0", "desc": "岂有此理……（说完便倒下了）", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 55050, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "NExploreMainView", "clickRange": "headItemClick", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 0, "returnID": "55050", "npcPos": 0}, {"id": 55051, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "NExploreMainView", "clickRange": "btnGuide", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 56000, "type": 2, "triggerType": 2, "triggerPar": "162", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友！道友！你知道吗，妖盟中有位历练归来的盟友，意外拾得了一幅上古仙卷！", "nextID": 56001, "returnID": "0", "npcPos": 0}, {"id": 56001, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "btnUnion", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 56002, "returnID": "0", "npcPos": 0}, {"id": 56002, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "UnionMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "妖盟里突然感应到了上古仙人的遗物！我们快去看看吧？", "nextID": 56003, "returnID": "0", "npcPos": 0}, {"id": 56003, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "UnionMainView", "clickRange": "btnFight", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 56004, "returnID": "0", "npcPos": 0}, {"id": 56004, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "UnionActivityListView", "clickRange": "btnUnionTreasure", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 56005, "returnID": "0", "npcPos": 0}, {"id": 56005, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteUnionTreasureMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "我感觉仙人的宝物离我们很近了！快感应一下宝物的线索吧！", "nextID": 56006, "returnID": "0", "npcPos": 0}, {"id": 56006, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteUnionTreasureMainView", "clickRange": "exploreBtn", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 56007, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "RemoteUnionTreasureMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "我们发现了一个宝藏地点！看来要集齐所有的宝藏地点，才能确定宝藏的位置。", "nextID": 56008, "returnID": "0", "npcPos": 0}, {"id": 56008, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteUnionTreasureMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "如果有发现相同地点的幻卷，记得要交换给妖盟里的其他道友。大家一起来寻宝，才能更快的发现宝藏呀！", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 57000, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYellowMountainStageView", "clickRange": "0", "clickNum": 1, "character": 144072, "sound": "0", "desc": "现身吧，你身上的气息，早已令这里秽浊不堪了。", "nextID": 57001, "returnID": "0", "npcPos": 0}, {"id": 57001, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYellowMountainStageView", "clickRange": "0", "clickNum": 1, "character": 144060, "sound": "0", "desc": "这一路还真是威风啊……毛头小道，你可知踏入本座领地的下场？", "nextID": 57002, "returnID": "0", "npcPos": 1}, {"id": 57002, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYellowMountainStageView", "clickRange": "0", "clickNum": 1, "character": 144072, "sound": "0", "desc": "这黄岳仙乡，又何时成了化外邪修领地？我知道你们的目标乃是此地的本源灵脉……今天我绝不会让你们得逞。", "nextID": 57003, "returnID": "0", "npcPos": 0}, {"id": 57003, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYellowMountainStageView", "clickRange": "0", "clickNum": 1, "character": 144060, "sound": "0", "desc": "可笑！凭我八百年的修为，一瞬间就足以让你灰飞烟灭！接招吧！", "nextID": 0, "returnID": "0", "npcPos": 1}, {"id": 57100, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYellowMountainStageView", "clickRange": "0", "clickNum": 1, "character": 144065, "sound": "0", "desc": "咳……你、你不是普通的修士！你究竟是什么人……", "nextID": 57101, "returnID": "0", "npcPos": 1}, {"id": 57101, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYellowMountainStageView", "clickRange": "0", "clickNum": 1, "character": 144072, "sound": "0", "desc": "天尊奉命邀登殿，飘然醉步拜凌霄……", "nextID": 57102, "returnID": "0", "npcPos": 0}, {"id": 57102, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYellowMountainStageView", "clickRange": "0", "clickNum": 1, "character": 144072, "sound": "0", "desc": "带上你底下的小妖，永世不准再踏足黄山。否则，就没有下次了。", "nextID": 57103, "returnID": "0", "npcPos": 0}, {"id": 57103, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYellowMountainStageView", "clickRange": "0", "clickNum": 1, "character": 144065, "sound": "0", "desc": "是……是，多谢上仙饶命之恩，我等这就离去，此生绝不再有妄念！", "nextID": 0, "returnID": "0", "npcPos": 1}, {"id": 58000, "type": 2, "triggerType": 2, "triggerPar": "160", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友，新的挑战已至，三界征途开启。随我一同前往天地人三界秘境，共探仙道玄机吧！", "nextID": 58001, "returnID": "0", "npcPos": 0}, {"id": 58001, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "btnChallenge", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 58002, "returnID": "0", "npcPos": 0}, {"id": 58002, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "SystemListView", "clickRange": "btnPlanesTrial", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 59000, "type": 2, "triggerType": 2, "triggerPar": "170", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友，有位谪仙游经此地！据传此仙曾掌四方之财，但因故而受谪贬，不过依旧财缘无量，快去见见他吧！", "nextID": 59001, "returnID": "0", "npcPos": 0}, {"id": 59001, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "btnYueBao", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 59002, "type": 1, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "ActivityStorageMainView", "clickRange": "btnYueBao", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 59003, "returnID": "0", "npcPos": 0}, {"id": 59003, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "ActivityStorageMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "仙家当敬崇以待，道友可选择招财金呱来供奉仙玉，供奉期结束后，即可获得来自呱仙的奉礼。", "nextID": 59004, "returnID": "0", "npcPos": 0}, {"id": 59004, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "ActivityStorageMainView", "clickRange": "listItem", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 59005, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYueBaoMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "仙家当敬崇以待，道友可选择招财金呱来供奉仙玉，供奉期结束后，即可获得来自呱仙的奉礼。", "nextID": 59006, "returnID": "0", "npcPos": 0}, {"id": 59006, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYueBaoMainView", "clickRange": "listItem", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 60000, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "RemoteUnionTreasureMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "啊，你发现了幻卷。这样我们就可以将重复感应到的相同幻卷交换给其他人了。", "nextID": 60001, "returnID": "0", "npcPos": 0}, {"id": 60001, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteUnionTreasureMainView", "clickRange": "bagBtn", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 60002, "returnID": "0", "npcPos": 0}, {"id": 60002, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteUnionTreasureBagView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "在这边可以查看自己已经获得和没有获得的幻卷。", "nextID": 60003, "returnID": "0", "npcPos": 0}, {"id": 60003, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteUnionTreasureBagView", "clickRange": "sendTag", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 60004, "returnID": "0", "npcPos": 0}, {"id": 60004, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteUnionTreasureBagView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "找到想赠送幻卷的朋友，点赠送就可以啦。", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 47100, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "MagicMarkView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "一键拆卸可以帮助道友拆卸指定预设的所有印记", "nextID": 47101, "returnID": "0", "npcPos": 0}, {"id": 47101, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MagicMarkView", "clickRange": "btnDetach", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 47102, "returnID": "0", "npcPos": 0}, {"id": 47102, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "TalentMaskDetachView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友可以根据自己的需要，切换需要拆卸的分身预设", "nextID": 47103, "returnID": "0", "npcPos": 0}, {"id": 47103, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "TalentMaskDetachView", "clickRange": "btnSortMask", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 61000, "type": 2, "triggerType": 2, "triggerPar": "158", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友！南边发现了一块无名仙林，那里灵气充裕、四季如春，十分适合作为您仙居的良地，快去看看吧！", "nextID": 61001, "returnID": "0", "npcPos": 0}, {"id": 61001, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "btnHome", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 61002, "returnID": "0", "npcPos": 0}, {"id": 61002, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "SystemListView", "clickRange": "btnJiayuan", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 61003, "returnID": "0", "npcPos": 0}, {"id": 61003, "type": 3, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardMain<PERSON>iew", "clickRange": "-1", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 61004, "returnID": "0", "npcPos": 0}, {"id": 61004, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardMain<PERSON>iew", "clickRange": "0", "clickNum": 1, "character": 998988977, "sound": "0", "desc": "终于等到您了……道友！小仙乃此处土地。如您所见，此地虽灵气充裕，但也引来了化外邪修的觊觎……", "nextID": 61005, "returnID": "0", "npcPos": 0}, {"id": 61005, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardMain<PERSON>iew", "clickRange": "0", "clickNum": 1, "character": 998988977, "sound": "0", "desc": "小仙恳请您，驱逐那作歹的邪修！否则此地定将灵韵尽散，沦为凡墟！只要事成，小仙愿为道友鞍前马后！", "nextID": 61006, "returnID": "0", "npcPos": 0}, {"id": 61006, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardMain<PERSON>iew", "clickRange": "btnAreaMonster1", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 61007, "returnID": "0", "npcPos": 0}, {"id": 61007, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardAreaUnlockBattleView", "clickRange": "0", "clickNum": 1, "character": 998988977, "sound": "0", "desc": "就是它……企图将此地据为己有，独享灵韵！道友你可万万要小心啊！", "nextID": 61008, "returnID": "0", "npcPos": 0}, {"id": 61008, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardAreaUnlockBattleView", "clickRange": "btnBattle", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 61009, "returnID": "0", "npcPos": 0}, {"id": 61009, "type": 5, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "BattleMainView", "clickRange": "0", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 61010, "returnID": "0", "npcPos": 0}, {"id": 61010, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardMain<PERSON>iew", "clickRange": "0", "clickNum": 1, "character": 998988977, "sound": "0", "desc": "道友果然超凡！小仙恳请您能长居于此，庇佑此地！为方便道友筑建仙居，小仙适才还将周边的植木清理一净，请随我来看……", "nextID": 61011, "returnID": "0", "npcPos": 0}, {"id": 61011, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardMain<PERSON>iew", "clickRange": "0", "clickNum": 1, "character": 998988977, "sound": "0", "desc": "道友您看，此树乃的万年仙桃树，是这的本源灵脉所在，您之后筑居于此，这仙桃树定能为您带来莫大的帮助。", "nextID": 61012, "returnID": "0", "npcPos": 0}, {"id": 61012, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardMain<PERSON>iew", "clickRange": "build_1003", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 61013, "returnID": "0", "npcPos": 0}, {"id": 61013, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardTreeView", "clickRange": "txtCurProduct", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 61014, "returnID": "0", "npcPos": 0}, {"id": 61014, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardTreeView", "clickRange": "0", "clickNum": 1, "character": 998988977, "sound": "0", "desc": "道友您看，仙桃树每日可自动生产仙桃，当前生产的仙桃数量在此处显示。", "nextID": 61015, "returnID": "0", "npcPos": 0}, {"id": 61015, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardTreeView", "clickRange": "txtHelpProduct", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 61016, "returnID": "0", "npcPos": 0}, {"id": 61016, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardTreeView", "clickRange": "0", "clickNum": 1, "character": 998988977, "sound": "0", "desc": "如果有其他道友前来协助，可额外生产仙桃。其他道友协助生产的仙桃数量显示在这里。", "nextID": 61017, "returnID": "0", "npcPos": 0}, {"id": 61017, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardTreeView", "clickRange": "0", "clickNum": 1, "character": 998988977, "sound": "0", "desc": "仙桃产出还需要一段时间，咱们先去开垦一块灵田看看吧。", "nextID": 61018, "returnID": "0", "npcPos": 0}, {"id": 61018, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardTreeView", "clickRange": "btnClose", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 61019, "returnID": "0", "npcPos": 0}, {"id": 61019, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardMain<PERSON>iew", "clickRange": "btnLayout", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 61020, "returnID": "0", "npcPos": 0}, {"id": 61020, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardMain<PERSON>iew", "clickRange": "btnstorage", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 61021, "returnID": "0", "npcPos": 0}, {"id": 61021, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardMain<PERSON>iew", "clickRange": "list_1001", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 61022, "returnID": "0", "npcPos": 0}, {"id": 61022, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardMain<PERSON>iew", "clickRange": "btnSure_205", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 61023, "returnID": "0", "npcPos": 0}, {"id": 61023, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardMain<PERSON>iew", "clickRange": "btnSave", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 61024, "returnID": "0", "npcPos": 0}, {"id": 61024, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardMain<PERSON>iew", "clickRange": "0", "clickNum": 1, "character": 998988977, "sound": "0", "desc": "灵田建造完了，快来看看！", "nextID": 61025, "returnID": "0", "npcPos": 0}, {"id": 61025, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardMain<PERSON>iew", "clickRange": "buildIcon_1001", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 61026, "returnID": "0", "npcPos": 0}, {"id": 61026, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardMain<PERSON>iew", "clickRange": "0", "clickNum": 1, "character": 998988977, "sound": "0", "desc": "灵田每隔一段时间会自动产出仙草，咱们稍等一会~", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 61100, "type": 2, "triggerType": 4, "triggerPar": "280002", "eventPar": "0", "scene": "RemoteYardMain<PERSON>iew", "clickRange": "0", "clickNum": 1, "character": 998988977, "sound": "0", "desc": "恭喜道友，造物功能开放了，快随我来一看究竟！", "nextID": 61101, "returnID": "0", "npcPos": 0}, {"id": 61101, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardMain<PERSON>iew", "clickRange": "btnDraw", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 61102, "returnID": "0", "npcPos": 0}, {"id": 61102, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardDrawView", "clickRange": "0", "clickNum": 1, "character": 998988977, "sound": "0", "desc": "造物可以获得装饰建筑，道友不妨试试看。", "nextID": 61103, "returnID": "0", "npcPos": 0}, {"id": 61103, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardDrawView", "clickRange": "btnOne", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 61104, "returnID": "0", "npcPos": 0}, {"id": 61104, "type": 4, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardDrawResultView", "clickRange": "0", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 61105, "returnID": "0", "npcPos": 0}, {"id": 61105, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardDrawView", "clickRange": "0", "clickNum": 1, "character": 998988977, "sound": "0", "desc": "噢！恭喜道友您获得了新的装饰建筑，现在可以将装饰建筑摆放在仙居内啦！", "nextID": 61106, "returnID": "0", "npcPos": 0}, {"id": 61106, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardDrawView", "clickRange": "btnClose", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 61107, "returnID": "0", "npcPos": 0}, {"id": 61107, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardMain<PERSON>iew", "clickRange": "btnBuilding", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 61108, "returnID": "0", "npcPos": 0}, {"id": 61108, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardBuildingView", "clickRange": "list_41002", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 61109, "returnID": "0", "npcPos": 0}, {"id": 61109, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardBuildingDetailView", "clickRange": "btnUpgrade", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 61110, "returnID": "0", "npcPos": 0}, {"id": 61110, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardBuildingDetailView", "clickRange": "btnClose", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 61111, "returnID": "0", "npcPos": 0}, {"id": 61111, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardBuildingView", "clickRange": "btnClose", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 61112, "returnID": "0", "npcPos": 0}, {"id": 61112, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardMain<PERSON>iew", "clickRange": "btnLayout", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 61113, "returnID": "0", "npcPos": 0}, {"id": 61113, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardMain<PERSON>iew", "clickRange": "btnstorage", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 61114, "returnID": "0", "npcPos": 0}, {"id": 61114, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardMain<PERSON>iew", "clickRange": "list_41002", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 61115, "returnID": "0", "npcPos": 0}, {"id": 61115, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardMain<PERSON>iew", "clickRange": "btnSure_240", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 61116, "returnID": "0", "npcPos": 0}, {"id": 61116, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardMain<PERSON>iew", "clickRange": "btnSave", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 61117, "returnID": "0", "npcPos": 0}, {"id": 61117, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardMain<PERSON>iew", "clickRange": "0", "clickNum": 1, "character": 998988977, "sound": "0", "desc": "这样就种植好沐春柳啦，以后如果有获得建筑就可以自由装扮仙居啦！", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 61200, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardMain<PERSON>iew", "clickRange": "0", "clickNum": 1, "character": 998988977, "sound": "0", "desc": "这是小仙昔日游历四方时所拾得的丹炉，道友若有所需，方可自行使用。", "nextID": 61201, "returnID": "0", "npcPos": 0}, {"id": 61201, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardMain<PERSON>iew", "clickRange": "btnLayout", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 61202, "returnID": "0", "npcPos": 0}, {"id": 61202, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardMain<PERSON>iew", "clickRange": "btnstorage", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 61203, "returnID": "0", "npcPos": 0}, {"id": 61203, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardMain<PERSON>iew", "clickRange": "list_1002", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 61204, "returnID": "0", "npcPos": 0}, {"id": 61204, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardMain<PERSON>iew", "clickRange": "btnSure_205", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 61205, "returnID": "0", "npcPos": 0}, {"id": 61205, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardMain<PERSON>iew", "clickRange": "btnSave", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 61206, "returnID": "0", "npcPos": 0}, {"id": 61206, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardMain<PERSON>iew", "clickRange": "build_1002", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 61207, "returnID": "0", "npcPos": 0}, {"id": 61207, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardStoveDetailView", "clickRange": "btnStart", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 61208, "returnID": "0", "npcPos": 0}, {"id": 61208, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardStoveDetailView", "clickRange": "0", "clickNum": 1, "character": 998988977, "sound": "0", "desc": "想要炼制出好的丹药需要一定的时间，这段时间不妨先耐心等待……", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 61300, "type": 2, "triggerType": 4, "triggerPar": "280011", "eventPar": "0", "scene": "RemoteYardMain<PERSON>iew", "clickRange": "0", "clickNum": 1, "character": 998988977, "sound": "0", "desc": "恭喜获得了丹药，快去服用试试看~", "nextID": 61301, "returnID": "0", "npcPos": 0}, {"id": 61301, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardMain<PERSON>iew", "clickRange": "build_1002", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 61302, "returnID": "0", "npcPos": 0}, {"id": 61302, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardStoveDetailView", "clickRange": "tagList_0", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 61303, "returnID": "0", "npcPos": 0}, {"id": 61303, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardStoveDetailView", "clickRange": "0", "clickNum": 1, "character": 998988977, "sound": "0", "desc": "服用丹药可提高自身修为~", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 61400, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardMain<PERSON>iew", "clickRange": "0", "clickNum": 1, "character": 998988977, "sound": "0", "desc": "此地有着一片化外灵池，刚才清理的时候被我收起来了，道友快放出来看看吧~", "nextID": 61401, "returnID": "0", "npcPos": 0}, {"id": 61401, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardMain<PERSON>iew", "clickRange": "btnLayout", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 61402, "returnID": "0", "npcPos": 0}, {"id": 61402, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardMain<PERSON>iew", "clickRange": "btnstorage", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 61403, "returnID": "0", "npcPos": 0}, {"id": 61403, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardMain<PERSON>iew", "clickRange": "list_1004", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 61404, "returnID": "0", "npcPos": 0}, {"id": 61404, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardMain<PERSON>iew", "clickRange": "btnSure_205", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 61405, "returnID": "0", "npcPos": 0}, {"id": 61405, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardMain<PERSON>iew", "clickRange": "btnSave", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 61406, "returnID": "0", "npcPos": 0}, {"id": 61406, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardMain<PERSON>iew", "clickRange": "build_1004", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 61407, "returnID": "0", "npcPos": 0}, {"id": 61407, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardWishPoolDetailView", "clickRange": "0", "clickNum": 1, "character": 998988977, "sound": "0", "desc": "化外灵池可孕育天材地宝，现在就去试试看吧~", "nextID": 61408, "returnID": "0", "npcPos": 0}, {"id": 61408, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardWishPoolDetailView", "clickRange": "btnStart", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 61409, "returnID": "0", "npcPos": 0}, {"id": 61409, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardWishPoolDetailView", "clickRange": "0", "clickNum": 1, "character": 998988977, "sound": "0", "desc": "天材地宝的孕育需要一段时间，咱们等一段时间后再来看看~", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 61500, "type": 2, "triggerType": 4, "triggerPar": "280016", "eventPar": "0", "scene": "RemoteYardMain<PERSON>iew", "clickRange": "0", "clickNum": 1, "character": 998988977, "sound": "0", "desc": "噢，想不到珍宝阁阁主竟也来到了此地……道友，您若有多余的产物可以出售给她换取“桃妖币”。", "nextID": 61501, "returnID": "0", "npcPos": 0}, {"id": 61501, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardMain<PERSON>iew", "clickRange": "YardShopItem", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 61502, "returnID": "0", "npcPos": 0}, {"id": 61502, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardTraderView", "clickRange": "0", "clickNum": 1, "character": 998988977, "sound": "0", "desc": "珍宝阁阁主会根据产物稀缺度的不同，来为产物定价，有些稀缺的天材地宝会以更高的价格收购。", "nextID": 61503, "returnID": "0", "npcPos": 0}, {"id": 61503, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardTraderView", "clickRange": "0", "clickNum": 1, "character": 998988977, "sound": "0", "desc": "如果手上的产物想卖个好价钱，也可以去其他道友的仙居看看他们的珍宝阁阁主有没有高价收购。", "nextID": 61504, "returnID": "0", "npcPos": 0}, {"id": 61504, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardTraderView", "clickRange": "0", "clickNum": 1, "character": 998988977, "sound": "0", "desc": "现在就将手上的产物出售给珍宝阁阁主吧。", "nextID": 61505, "returnID": "0", "npcPos": 0}, {"id": 61505, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardTraderView", "clickRange": "list_1", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 61600, "type": 2, "triggerType": 4, "triggerPar": "280017", "eventPar": "0", "scene": "RemoteYardMain<PERSON>iew", "clickRange": "0", "clickNum": 1, "character": 998988977, "sound": "0", "desc": "恭喜道友获得了桃妖币，可以拿桃妖币兑换装饰建筑啦。", "nextID": 61601, "returnID": "0", "npcPos": 0}, {"id": 61601, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardMain<PERSON>iew", "clickRange": "btnShop", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 61602, "returnID": "0", "npcPos": 0}, {"id": 61602, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYardShopView", "clickRange": "0", "clickNum": 1, "character": 998988977, "sound": "0", "desc": "天工坊内有许多可兑换的装饰建筑，兑换完成后将放在库存中。", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 61700, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "RemoteRebornTrialExploreView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友！北域发现了一处秘境！里面虽遍地天材地宝，但有着神秘结界，它会抑制外来者的修为，道友务必小心！", "nextID": 61701, "returnID": "0", "npcPos": 0}, {"id": 61701, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteRebornTrialExploreView", "clickRange": "expIcon", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 61702, "returnID": "0", "npcPos": 0}, {"id": 61702, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteRebornTrialExploreView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友，气运不仅决定修行进度，还可以消耗气运处理各种事件。", "nextID": 61703, "returnID": "0", "npcPos": 0}, {"id": 61703, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteRebornTrialExploreView", "clickRange": "comPlayerInfo", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 61704, "returnID": "0", "npcPos": 0}, {"id": 61704, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteRebornTrialRealmsViewView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友，气运达到境界升级要求后即可自动升级，境界提升可以增加挑战获得积分。", "nextID": 61705, "returnID": "0", "npcPos": 0}, {"id": 61705, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteRebornTrialRealmsViewView", "clickRange": "btnClose", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 61706, "returnID": "0", "npcPos": 0}, {"id": 61706, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteRebornTrialExploreView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友，我们点击历练，来触发第一个考验。", "nextID": 61707, "returnID": "0", "npcPos": 0}, {"id": 61707, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteRebornTrialExploreView", "clickRange": "btnExplore", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 61800, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "RemoteRebornTrialExploreView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友，事件分为3个选项，每个选项有不同成功率。", "nextID": 61801, "returnID": "0", "npcPos": 0}, {"id": 61801, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteRebornTrialExploreView", "clickRange": "txtValue", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 61802, "returnID": "0", "npcPos": 0}, {"id": 61802, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteRebornTrialExploreView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "要注意对方的耐心值，如果耐心值等于0，对方将会离开。", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 61900, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "RemoteRebornTrialExploreView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友，镇压妖兽需要选择对应的符咒，每个符咒有一定镇压概率。", "nextID": 61901, "returnID": "0", "npcPos": 0}, {"id": 61901, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteRebornTrialExploreView", "clickRange": "comBar", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 61902, "returnID": "0", "npcPos": 0}, {"id": 61902, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteRebornTrialExploreView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "要注意妖兽的怒气值，怒气满了之后本次镇压将会失败。", "nextID": 61903, "returnID": "0", "npcPos": 0}, {"id": 61903, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteRebornTrialExploreView", "clickRange": "btnInfo", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 61904, "returnID": "0", "npcPos": 0}, {"id": 61904, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteRebornTrialExploreView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "这里是镇压妖兽的奖励，只有成功镇压才会扣除单次气运和获得奖励。", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 62000, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "RemoteRebornTrialExploreView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友，救死扶伤需要选择对应的符咒，每个符咒有一定治愈概率。", "nextID": 62001, "returnID": "0", "npcPos": 0}, {"id": 62001, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteRebornTrialExploreView", "clickRange": "comBar", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 62002, "returnID": "0", "npcPos": 0}, {"id": 62002, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteRebornTrialExploreView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "要注意小妖的伤势，伤势为100后本次救助将会失败。", "nextID": 62003, "returnID": "0", "npcPos": 0}, {"id": 62003, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteRebornTrialExploreView", "clickRange": "btnInfo", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 62004, "returnID": "0", "npcPos": 0}, {"id": 62004, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteRebornTrialExploreView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "这里是救死扶伤的奖励，只有成功治愈才会扣除单次气运和获得奖励。", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 62100, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "RemoteRebornTrialExploreView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友，接触禁制需要选择对应的符咒，每个符咒有一定解除概率。", "nextID": 62101, "returnID": "0", "npcPos": 0}, {"id": 62101, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteRebornTrialExploreView", "clickRange": "comBar", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 62102, "returnID": "0", "npcPos": 0}, {"id": 62102, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteRebornTrialExploreView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "要注意禁制的保护盾，保护盾满了之后本次解除将会失败。", "nextID": 62103, "returnID": "0", "npcPos": 0}, {"id": 62103, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteRebornTrialExploreView", "clickRange": "btnInfo", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 62104, "returnID": "0", "npcPos": 0}, {"id": 62104, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteRebornTrialExploreView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "这里是解除禁制的奖励，只有成功解除才会扣除单次气运和获得奖励。", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 62200, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteRebornTrialBattleMapView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友，在轮回秘境中还可以与妖盟成员一起挑战其他妖盟。", "nextID": 62201, "returnID": "0", "npcPos": 0}, {"id": 62201, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteRebornTrialBattleMapView", "clickRange": "btnPreview", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 62202, "returnID": "0", "npcPos": 0}, {"id": 62202, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteRebornTrialBattleMapView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "每次挑战都会等量扣除气运，直到气运为0即挑战停止", "nextID": 62203, "returnID": "0", "npcPos": 0}, {"id": 62203, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteRebornTrialBattleMapView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "这里可以查看每个妖盟的每个人气运。", "nextID": 62204, "returnID": "0", "npcPos": 0}, {"id": 62204, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteRebornTrialBattleMapView", "clickRange": "btnUnionInfo", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 62205, "returnID": "0", "npcPos": 0}, {"id": 62205, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteRebornTrialBattleMapView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "每次挑战获得积分由自身境界决定，对方扣除积分由击败数量决定。道友，开始挑战试试。", "nextID": 62206, "returnID": "0", "npcPos": 0}, {"id": 62206, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteRebornTrialBattleMapView", "clickRange": "btnPreBattle", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 62300, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "RemoteGuardFairyTreeGameView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友首次守护仙树，让我们看看怎么守护吧~", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 62301, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "RemoteGuardFairyTreeGameView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友快来手动操作试试吧！", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 62400, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "EquipmentAutoSettingView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友，只要勾选这儿，就能在锤炼过程中获取该分身的装备噢。", "nextID": 62401, "returnID": "0", "npcPos": 0}, {"id": 62401, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "EquipmentAutoSettingView", "clickRange": "btnToggle8", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 62402, "returnID": "0", "npcPos": 0}, {"id": 62402, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "EquipmentAutoSettingView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "点击这儿，可切换道友的不同分身，无需上阵就能为不同的分身获取装备，嘻嘻~是不是很方便呢？", "nextID": 62403, "returnID": "0", "npcPos": 0}, {"id": 62403, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "EquipmentAutoSettingView", "clickRange": "godBodyTagItem2", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 62500, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "TalentAutoSettingView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友，只要勾选这儿，就能在激发过程中获取该分身的灵脉噢。", "nextID": 62501, "returnID": "0", "npcPos": 0}, {"id": 62501, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "TalentAutoSettingView", "clickRange": "btnToggle8", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 62502, "returnID": "0", "npcPos": 0}, {"id": 62502, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "TalentAutoSettingView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "点击这儿，可切换道友的不同分身，无需上阵就能为不同的分身获取灵脉，嘻嘻~是不是很方便呢？", "nextID": 62503, "returnID": "0", "npcPos": 0}, {"id": 62503, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "TalentAutoSettingView", "clickRange": "godBodyTagItem2", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 62600, "type": 2, "triggerType": 2, "triggerPar": "181", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "混沌降临，神兽释厄。混沌神兽已经降临，快去看看吧", "nextID": 62601, "returnID": "0", "npcPos": 0}, {"id": 62601, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "btnPet", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 62602, "returnID": "62600", "npcPos": 0}, {"id": 62602, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "<PERSON><PERSON><PERSON><PERSON>", "clickRange": "btnCatch", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 62603, "returnID": "62600", "npcPos": 0}, {"id": 62603, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "<PERSON><PERSON><PERSON><PERSON>", "clickRange": "btnAncient", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 62604, "returnID": "62600", "npcPos": 0}, {"id": 62604, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteAncientPetMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "点击任意混沌神兽，可查看神兽详情", "nextID": 62605, "returnID": "62600", "npcPos": 0}, {"id": 62605, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteAncientPetMainView", "clickRange": "touch3", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 62606, "returnID": "62600", "npcPos": 0}, {"id": 62606, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteAncientPetDetailView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "觉醒混沌神兽需要消耗神兽精魄，觉醒后无法返还，还请道友好好考虑！", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 62700, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "<PERSON><PERSON><PERSON><PERSON>", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "内丹新开放了共鸣功能，快去看看吧", "nextID": 62701, "returnID": "0", "npcPos": 0}, {"id": 62701, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "<PERSON><PERSON><PERSON><PERSON>", "clickRange": "listBtn", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 62702, "returnID": "0", "npcPos": 0}, {"id": 62702, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "<PERSON><PERSON><PERSON><PERSON>", "clickRange": "star5", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 62703, "returnID": "0", "npcPos": 0}, {"id": 62703, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "PetsPetKernelDetailView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "内丹共鸣可将内丹的装备属性转换成拥有属性，共鸣等级越高，转换的比例越高。咱们这就去试试看吧", "nextID": 62704, "returnID": "0", "npcPos": 0}, {"id": 62704, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "PetsPetKernelDetailView", "clickRange": "tagItem1", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 62705, "returnID": "0", "npcPos": 0}, {"id": 62705, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "PetsPetKernelDetailView", "clickRange": "activeItem", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 62706, "returnID": "0", "npcPos": 0}, {"id": 62706, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "PetsPetKernelDetailView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "激活内丹共鸣，即使内丹未装备在灵兽上也会有拥有属性。", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 62800, "type": 2, "triggerType": 2, "triggerPar": "178", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友，荣誉堂记录妖盟获得仙像和荣誉，快随我来一起看看！", "nextID": 62801, "returnID": "0", "npcPos": 0}, {"id": 62801, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "btnUnion", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 62802, "returnID": "0", "npcPos": 0}, {"id": 62802, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "UnionMainView", "clickRange": "btnHonorHall", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 62804, "returnID": "0", "npcPos": 0}, {"id": 62804, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteUnionHonorHallMainView", "clickRange": "trophy_2", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 62805, "returnID": "0", "npcPos": 0}, {"id": 62805, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteUnionHonorHallMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "仙像通过对应活动中获得，后续收集可以提高仙像的星级和品质", "nextID": 62806, "returnID": "0", "npcPos": 0}, {"id": 62806, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteUnionHonorHallMainView", "clickRange": "honor<PERSON>lick", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 62807, "returnID": "0", "npcPos": 0}, {"id": 62807, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteUnionHonorHallMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "荣誉点通过副本活动获得，荣誉点达到即可解锁新的场景和商店兑换道具。 ", "nextID": 62808, "returnID": "0", "npcPos": 0}, {"id": 62808, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteUnionHonorHallMainView", "clickRange": "honorTag", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 62809, "returnID": "0", "npcPos": 0}, {"id": 62809, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteUnionHonorHallMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "在这里道友可以进行举宗飞升，举宗飞升需要荣誉点和妖力达到指定要求。", "nextID": 62810, "returnID": "0", "npcPos": 0}, {"id": 62810, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteUnionHonorHallMainView", "clickRange": "flyItem", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 62811, "returnID": "0", "npcPos": 0}, {"id": 62811, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteUnionHonorHallFlyDetailView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "举宗飞升后妖盟将前往灵界，并解锁全新的妖盟场景。道友，快和盟友一起努力吧！", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 63001, "type": 2, "triggerType": 2, "triggerPar": "180", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友开始修行，逐步感悟天道，踏上道途，随我一同开始修行吧！", "nextID": 63002, "returnID": "0", "npcPos": 0}, {"id": 63002, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "btnHome", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 63003, "returnID": "0", "npcPos": 0}, {"id": 63003, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "SystemListView", "clickRange": "btnProfession", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 63004, "returnID": "0", "npcPos": 0}, {"id": 63004, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteProfessionMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "要真正的踏上道途，需先贯通自身的奇经八脉~", "nextID": 63005, "returnID": "0", "npcPos": 0}, {"id": 63005, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteProfessionMainView", "clickRange": "star1", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 63006, "returnID": "0", "npcPos": 0}, {"id": 63006, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteProfessionMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友可以通过完成修行任务辅助贯通奇经八脉~", "nextID": 63007, "returnID": "0", "npcPos": 0}, {"id": 63007, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteProfessionMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "所有经脉贯通之后，即可选择道途，铸无上仙路！", "nextID": 63008, "returnID": "0", "npcPos": 0}, {"id": 63008, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteProfessionMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友可以在这里提前查看具体有哪些可以选择的道途~", "nextID": 63009, "returnID": "0", "npcPos": 0}, {"id": 63009, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteProfessionMainView", "clickRange": "professionBtn", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 63101, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "RemoteProfessionMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友已成功踏上道途，修仙之旅自此开启~", "nextID": 63102, "returnID": "0", "npcPos": 0}, {"id": 63102, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteProfessionMainView", "clickRange": "trailBtn", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 63103, "returnID": "0", "npcPos": 0}, {"id": 63103, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteProfessionTalentView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友可以修行天赋道纹，获得属性和以及解锁相应道途的技能！", "nextID": 63104, "returnID": "0", "npcPos": 0}, {"id": 63104, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteProfessionTalentView", "clickRange": "com_title", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 63105, "returnID": "0", "npcPos": 0}, {"id": 63105, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteProfessionMainView", "clickRange": "btnReset", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 63106, "returnID": "0", "npcPos": 0}, {"id": 63106, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteProfessionProfessionViewView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "若道友想重新选择其他道途，可消耗仙玉进行忘念重修~", "nextID": 63107, "returnID": "0", "npcPos": 0}, {"id": 63107, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteProfessionProfessionViewView", "clickRange": "com_title", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 63108, "returnID": "0", "npcPos": 0}, {"id": 63108, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteProfessionMainView", "clickRange": "btnBoss", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 63109, "returnID": "0", "npcPos": 0}, {"id": 63109, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteProfessionBossView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友可以前往道途试炼挑战，可获得修行天赋道纹的修仙材料哦~", "nextID": 63110, "returnID": "0", "npcPos": 0}, {"id": 63110, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteProfessionBossView", "clickRange": "btnClose", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 63200, "type": 2, "triggerType": 2, "triggerPar": "182", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友，传说中的云光山重现天日啦！你知道吗，在那里不仅可以与道友们论弈寻道，更有可能寻得仙珍异宝哦。", "nextID": 63201, "returnID": "0", "npcPos": 0}, {"id": 63201, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "btnHome", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 63202, "returnID": "0", "npcPos": 0}, {"id": 63202, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "SystemListView", "clickRange": "btnElementalBonds", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 63203, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "RemoteElementalBondsMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "这儿就是云光山啦，此地以乾坤为棋盘，执星辰作落子。快起局试试吧！", "nextID": 63204, "returnID": "0", "npcPos": 0}, {"id": 63204, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteElementalBondsMainView", "clickRange": "btnFight", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 63205, "returnID": "0", "npcPos": 0}, {"id": 63205, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteElementalBondsChooseCardView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "这些灵符中蕴含着四海八荒的妖灵们的力量，道友可在对弈中借助这份力量，来帮助你取得优胜。", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 63206, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteElementalBondsChooseCardView", "clickRange": "card0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 63207, "returnID": "0", "npcPos": 0}, {"id": 63207, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteElementalBondsChooseCardView", "clickRange": "btnFight", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 63208, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteElementalBondsGameView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "敌我双方在同个盘面中轮流操作，滑动使相同颜色的棋子相连即可将之消除，并获得相应分数。", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 63209, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteElementalBondsGameView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "每局对战分5个回合，每回合双方可各自操作2步，最终得分高者胜。", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 63210, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteElementalBondsGameView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "尽量达成四个及以上的棋子消除，这样可以生成特殊棋子并增加一个步数", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 63211, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteElementalBondsGameView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "注意！蓝色的棋子中蕴含强大的灵力，消除后即可将之吸纳，灵力是施放灵符技能的必要条件。", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 63212, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteElementalBondsGameView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "灵力已满，现在来尝试施展妖灵的神通吧，让对手见识一下咱们的厉害。", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 63213, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteElementalBondsGameView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "每一回合中，每方操作都有时长限制，注意不要超时哦~", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 63214, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteElementalBondsGameView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "下方还有凝聚了少许上古灵力的符印可用，一局对战中，每个符印仅能使用一次。", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 63215, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteElementalBondsGameView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友可在对战中自行摸索，多多熟悉，祝道友玩的开心！", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 63219, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteElementalBondsMainView", "clickRange": "btnScoreRank", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 63220, "returnID": "0", "npcPos": 0}, {"id": 63220, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteElementalBondsScoreRankView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "此为云光天阶，沿途风景秀丽，可在此收获奖励、结识伙伴，请加油攀登，步步登高！", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 63221, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteElementalBondsMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "为了增加修行之路的乐趣，青丘大妖全面开放玩法，请迎接更多挑战来袭！", "nextID": 63222, "returnID": "0", "npcPos": 0}, {"id": 63222, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteElementalBondsMainView", "clickRange": "btnTableMode", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 63223, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteElementalBondsChooseCardView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "符印同样凝聚了些许上古灵力，可为我方对战增益，来更换使用下吧！", "nextID": 63224, "returnID": "0", "npcPos": 0}, {"id": 63224, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteElementalBondsChooseCardView", "clickRange": "btnSkillChange", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 63225, "returnID": "0", "npcPos": 0}, {"id": 63225, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteElementalBondsChooseSkillView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "点击使用即可替换携带的符印，每个符印在上场后灵力即会消散，请珍惜使用哦！", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 63226, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteElementalBondsMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "新得灵符，可以来练习一试！", "nextID": 63227, "returnID": "0", "npcPos": 0}, {"id": 63227, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteElementalBondsMainView", "clickRange": "btnPratice", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 63300, "type": 3, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWarSeasonGameMapView", "clickRange": "imgMap", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 63301, "returnID": "0", "npcPos": 0}, {"id": 63301, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWarSeasonGameMapView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友需要在自身所在初始古域与其他妖盟竞争，夺得前往中央玄境的资格！最终进军鸿蒙古界！", "nextID": 63302, "returnID": "0", "npcPos": 0}, {"id": 63302, "type": 3, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWarSeasonGameMapView", "clickRange": "showEnter", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 63303, "returnID": "0", "npcPos": 0}, {"id": 63303, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWarSeasonFirstEnterView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友受到了上古之力的影响，实力大幅度缩减，道友随我来看看如何恢复实力~", "nextID": 63304, "returnID": "0", "npcPos": 0}, {"id": 63304, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWarSeasonFirstEnterView", "clickRange": "detailBtn", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 63305, "returnID": "0", "npcPos": 0}, {"id": 63305, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWarSeasonInheritView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友在战场中可以提升自身的战场等级，恢复基础属性；提升境界可恢复道友的战斗属性、战斗抗性以及特殊属性！", "nextID": 63306, "returnID": "0", "npcPos": 0}, {"id": 63306, "type": 3, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWarSeasonInheritView", "clickRange": "close", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 63307, "returnID": "0", "npcPos": 0}, {"id": 63307, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWarSeasonFirstEnterView", "clickRange": "closeBtn", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 63308, "returnID": "0", "npcPos": 0}, {"id": 63308, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWarSeasonGameMapView", "clickRange": "closeComp", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 63309, "returnID": "0", "npcPos": 0}, {"id": 63309, "type": 3, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWarSeasonGameMapView", "clickRange": "player", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 63400, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWarSeasonGameMapView", "clickRange": "battleBtn", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 63401, "returnID": "0", "npcPos": 0}, {"id": 63401, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWarSeasonChallengeView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友可以在这里挑战妖兽，挑战妖兽可以获得上古灵气，用于提升战场等级~", "nextID": 63402, "returnID": "0", "npcPos": 0}, {"id": 63402, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWarSeasonChallengeView", "clickRange": "first", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 63403, "returnID": "0", "npcPos": 0}, {"id": 63403, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWarSeasonChallengeView", "clickRange": "btn_fight", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 63404, "returnID": "0", "npcPos": 0}, {"id": 63404, "type": 3, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWarSeasonChallengeView", "clickRange": "waitFight", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 63405, "returnID": "0", "npcPos": 0}, {"id": 63405, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWarSeasonChallengeView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "挑战成功后，道友可以扫荡该妖兽获得更多的上古灵气，扫荡有每日免费次数，请及时使用哦！", "nextID": 63406, "returnID": "0", "npcPos": 0}, {"id": 63406, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWarSeasonChallengeView", "clickRange": "sec", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 63407, "returnID": "0", "npcPos": 0}, {"id": 63407, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWarSeasonChallengeView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "等级越高的妖兽，蕴含的上古灵气越多，但同时挑战难度也会增加，更高级的妖兽有一定的解锁条件！", "nextID": 63408, "returnID": "0", "npcPos": 0}, {"id": 63408, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWarSeasonChallengeView", "clickRange": "close", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 63500, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWarSeasonGameMapView", "clickRange": "manor", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 63501, "returnID": "0", "npcPos": 0}, {"id": 63501, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWarSeasonGameMapView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友已拥有了一定实力了，可以前来挑战领地，领地可根据时间产出上古灵气~", "nextID": 63502, "returnID": "0", "npcPos": 0}, {"id": 63502, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWarSeasonGameMapView", "clickRange": "fight", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 63503, "returnID": "0", "npcPos": 0}, {"id": 63503, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWarSeasonGameMapView", "clickRange": "select0", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 63504, "returnID": "0", "npcPos": 0}, {"id": 63504, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWarSeasonGameMapView", "clickRange": "select1", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 63505, "returnID": "0", "npcPos": 0}, {"id": 63505, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWarSeasonGameMapView", "clickRange": "select2", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 63506, "returnID": "0", "npcPos": 0}, {"id": 63506, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWarSeasonGameMapView", "clickRange": "toFight", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 63507, "returnID": "0", "npcPos": 0}, {"id": 63507, "type": 3, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWarSeasonGameMapView", "clickRange": "waitFight", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 63508, "returnID": "0", "npcPos": 0}, {"id": 63508, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWarSeasonGameMapView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友占据领地后，可以前往此处获取收益，没有成功攻下也不用灰心，继续扫荡副本提升战场等级后再前来挑战！", "nextID": 63509, "returnID": "0", "npcPos": 0}, {"id": 63509, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWarSeasonGameMapView", "clickRange": "manorBtn", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 63510, "returnID": "0", "npcPos": 0}, {"id": 63510, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWarSeasonManorCtrlView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "领地产出有累计收益时长，超过时长将无法继续获取收益，道友要及时前来领取哦！", "nextID": 63511, "returnID": "0", "npcPos": 0}, {"id": 63511, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWarSeasonManorCtrlView", "clickRange": "close", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 63512, "returnID": "0", "npcPos": 0}, {"id": 63512, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWarSeasonGameMapView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友还可和盟友一起攻城掠地，获得更多的资源恢复实力，最终将与来自其他各位面的妖盟决战鸿蒙古界！", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 63601, "type": 1, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWarSeasonUnionDomainView", "clickRange": "btn_SendGift", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 63602, "returnID": "0", "npcPos": 0}, {"id": 63602, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWarSeasonSendGiftView", "clickRange": "btnInfo", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 63603, "returnID": "0", "npcPos": 0}, {"id": 63603, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWarSeasonSendGiftView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友在战场中达到一定的条件，可获得妖盟赠礼，该赠礼发放后妖盟内所有成员均可拾取一份赠礼宝箱!", "nextID": 63604, "returnID": "0", "npcPos": 0}, {"id": 63604, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWarSeasonSendGiftView", "clickRange": "btnClose", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 63605, "returnID": "0", "npcPos": 0}, {"id": 63605, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWarSeasonUnionDomainView", "clickRange": "btn_Bag", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 63606, "returnID": "0", "npcPos": 0}, {"id": 63606, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWarSeasonBoxCollectListView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "拾取到的赠礼宝箱可在此处开启，开启后可获得丰厚的战场资源!", "nextID": 63607, "returnID": "0", "npcPos": 0}, {"id": 63607, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWarSeasonBoxCollectListView", "clickRange": "btnClose", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 63608, "returnID": "0", "npcPos": 0}, {"id": 63608, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWarSeasonUnionDomainView", "clickRange": "btnMainCity", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 63609, "returnID": "0", "npcPos": 0}, {"id": 63609, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWarSeasonHallView", "clickRange": "btn_sign", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 63610, "returnID": "0", "npcPos": 0}, {"id": 63610, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWarSeasonHallView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友每日需要及时前来此处签到，累计妖盟经验，提升妖盟战场等级，妖盟等级越高，带来的战场特权将会越多", "nextID": 63611, "returnID": "0", "npcPos": 0}, {"id": 63611, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWarSeasonHallView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友记得每日前来大本营内签到，及时领取地上的宝箱以免宝箱消失", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 63701, "type": 2, "triggerType": 2, "triggerPar": "187", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友，法象蕴含着强大力量，战斗中还可以变化成法象攻击，随我来看看！", "nextID": 63702, "returnID": "0", "npcPos": 0}, {"id": 63702, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "btnRight", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 63703, "returnID": "0", "npcPos": 0}, {"id": 63703, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "comLawLooksTouch", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 63704, "returnID": "0", "npcPos": 0}, {"id": 63704, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteLawLooksMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友，我们需要花费引灵灯来召唤法象。", "nextID": 63705, "returnID": "0", "npcPos": 0}, {"id": 63705, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteLawLooksMainView", "clickRange": "btnOne", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 63706, "returnID": "0", "npcPos": 0}, {"id": 63706, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteLawLooksDrawResultView", "clickRange": "btnClose", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 63707, "returnID": "0", "npcPos": 0}, {"id": 63707, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteLawLooksMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "法象需要上阵后才能生效，别忘了点击上阵哦。", "nextID": 63708, "returnID": "0", "npcPos": 0}, {"id": 63708, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteLawLooksMainView", "clickRange": "btnMain", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 63709, "returnID": "0", "npcPos": 0}, {"id": 63709, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteLawLooksMainView", "clickRange": "btnGotoBattle", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 63710, "returnID": "0", "npcPos": 0}, {"id": 63710, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteLawLooksMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "法象还可以洗练技能，获得高额属性加成。", "nextID": 63711, "returnID": "0", "npcPos": 0}, {"id": 63711, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteLawLooksMainView", "clickRange": "btnWashTag", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 63712, "returnID": "0", "npcPos": 0}, {"id": 63712, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteLawLooksMainView", "clickRange": "btnWashDetail", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 63713, "returnID": "0", "npcPos": 0}, {"id": 63713, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteLawLooksWeightView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "洗练技能分为多个不同类型，每个类型可以洗练出的技能不同。", "nextID": 63714, "returnID": "0", "npcPos": 0}, {"id": 63714, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteLawLooksWeightView", "clickRange": "btnClose", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 63715, "returnID": "0", "npcPos": 0}, {"id": 63715, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteLawLooksMainView", "clickRange": "btnAuto", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 63716, "returnID": "0", "npcPos": 0}, {"id": 63716, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteLawLooksAutoView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友可以设置要拦截的品质和技能，这样子就不会错过心仪的属性了。", "nextID": 63717, "returnID": "0", "npcPos": 0}, {"id": 63717, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteLawLooksAutoView", "clickRange": "btnClose", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 63718, "returnID": "0", "npcPos": 0}, {"id": 63718, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteLawLooksMainView", "clickRange": "btnSkill", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 63719, "returnID": "0", "npcPos": 0}, {"id": 63719, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteLawLooksMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "点击技能即可洗练，洗练后道友可以选择是否替换。", "nextID": 63720, "returnID": "0", "npcPos": 0}, {"id": 63720, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteLawLooksMainView", "clickRange": "btnCompound", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 63721, "returnID": "0", "npcPos": 0}, {"id": 63721, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteLawLooksMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "低阶法象可以合成高阶法象，只需要选择要求法象即可！", "nextID": 63722, "returnID": "0", "npcPos": 0}, {"id": 63722, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteLawLooksMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "法象已初步准备完毕，道友需要不断提升法象，触发更强大法象之力。", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 63801, "type": 2, "triggerType": 2, "triggerPar": "188", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "恭喜道友，因为和仙友之间结下了深厚的情意，解锁与仙友之间的仙缘", "nextID": 63802, "returnID": "0", "npcPos": 0}, {"id": 63802, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "btnHome", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 63803, "returnID": "0", "npcPos": 0}, {"id": 63803, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "SystemListView", "clickRange": "btnDestiny", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 63804, "returnID": "0", "npcPos": 0}, {"id": 63804, "type": 3, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "DestinyView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 63805, "returnID": "0", "npcPos": 0}, {"id": 63805, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "DestinyView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "仙友青叶好像有话对你说", "nextID": 63806, "returnID": "0", "npcPos": 0}, {"id": 63806, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "DestinyView", "clickRange": "itemQY", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 63807, "returnID": "0", "npcPos": 0}, {"id": 63807, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "DestinyDetailView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "原来是仙友青叶想和道友一起续写仙缘", "nextID": 63808, "returnID": "0", "npcPos": 0}, {"id": 63808, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "DestinyDetailView", "clickRange": "btnCard", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 63809, "returnID": "0", "npcPos": 0}, {"id": 63809, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteDestinyCardMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "集齐所有仙缘可以获得对应仙友的强力组合羁绊", "nextID": 63810, "returnID": "0", "npcPos": 0}, {"id": 63810, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteDestinyCardMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "每个仙缘都会为道友提供助力", "nextID": 63811, "returnID": "0", "npcPos": 0}, {"id": 63811, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteDestinyCardMainView", "clickRange": "card0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 63812, "returnID": "0", "npcPos": 0}, {"id": 63812, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteDestinyCardDetailView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "一起收集和仙友之间的仙缘吧", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 63901, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWeYoundMapView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "漫漫航行，总算是来到这未央仙境了。未央仙境中的岛屿星罗棋布，道友且看，我们脚下的正是其中一个岛屿。", "nextID": 63902, "returnID": "0", "npcPos": 0}, {"id": 63902, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWeYoundMapView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友需要与妖盟盟友一同探索岛屿。每个岛屿均有一名岛屿守卫，击败守卫将发现下一岛屿的传送法阵同时自己的妖盟将获得仙境福泽。", "nextID": 63903, "returnID": "0", "npcPos": 0}, {"id": 63903, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWeYoundMapView", "clickRange": "hpText", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 63904, "returnID": "0", "npcPos": 0}, {"id": 63904, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWeYoundMapView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "此处显示道友当前的体力，每次探索将会消耗一定数量的体力，体力不足将无法进行探索，体力将随时间进行恢复。", "nextID": 63905, "returnID": "0", "npcPos": 0}, {"id": 63905, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWeYoundMapView", "clickRange": "powerText", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 63906, "returnID": "0", "npcPos": 0}, {"id": 63906, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWeYoundMapView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "受到未央仙境内的影响，道友的实力将以灵力的方式表现。与仙境内的妖兽进行战斗时，有几率获得仙丹可增加自身的灵力。", "nextID": 63907, "returnID": "0", "npcPos": 0}, {"id": 63907, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWeYoundMapView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "在未央仙境内，每日固定时间将开启云阙战场，我们现在先去看一看", "nextID": 63908, "returnID": "0", "npcPos": 0}, {"id": 63908, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWeYoundMapView", "clickRange": "btnPvp", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 63909, "returnID": "0", "npcPos": 0}, {"id": 63909, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWeYoundPvpView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "在云阙战场中道友需要与其它妖盟进行对战，将其它妖盟大本营的耐久攻击至0时，该妖盟将失去一层仙境福泽。", "nextID": 63910, "returnID": "0", "npcPos": 0}, {"id": 63910, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWeYoundPvpView", "clickRange": "btnDetail", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 63911, "returnID": "0", "npcPos": 0}, {"id": 63911, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWeYoundCourseView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "此处可查看赛程详情，当日在云阙战场表现良好的道友在明日可进入更高级别的战场。", "nextID": 63912, "returnID": "0", "npcPos": 0}, {"id": 63912, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWeYoundCourseView", "clickRange": "btnClose", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 63913, "returnID": "0", "npcPos": 0}, {"id": 63913, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWeYoundPvpView", "clickRange": "btnClose", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 63914, "returnID": "0", "npcPos": 0}, {"id": 63914, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWeYoundMapView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "接下来就请道友自行探索，祝道友在该仙境内收获多多！", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 64000, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYellowMountainStageView", "clickRange": "0", "clickNum": 1, "character": 144072, "sound": "0", "desc": "现身吧，你身上的气息，早已令这里秽浊不堪了。", "nextID": 64001, "returnID": "0", "npcPos": 0}, {"id": 64001, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYellowMountainStageView", "clickRange": "0", "clickNum": 1, "character": 144060, "sound": "0", "desc": "这一路还真是威风啊……毛头小道，你可知踏入本座领地的下场？", "nextID": 64002, "returnID": "0", "npcPos": 1}, {"id": 64002, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYellowMountainStageView", "clickRange": "0", "clickNum": 1, "character": 144072, "sound": "0", "desc": "这云巅武当，又何时成了化外邪修领地？我知道你们的目标乃是此地的本源灵脉……今天我绝不会让你们得逞。", "nextID": 64003, "returnID": "0", "npcPos": 0}, {"id": 64003, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYellowMountainStageView", "clickRange": "0", "clickNum": 1, "character": 144060, "sound": "0", "desc": "可笑！凭我八百年的修为，一瞬间就足以让你灰飞烟灭！接招吧！", "nextID": 0, "returnID": "0", "npcPos": 1}, {"id": 64100, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYellowMountainStageView", "clickRange": "0", "clickNum": 1, "character": 144065, "sound": "0", "desc": "咳……你、你不是普通的修士！你究竟是什么人……", "nextID": 64101, "returnID": "0", "npcPos": 1}, {"id": 64101, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYellowMountainStageView", "clickRange": "0", "clickNum": 1, "character": 144072, "sound": "0", "desc": "天尊奉命邀登殿，飘然醉步拜凌霄……", "nextID": 64102, "returnID": "0", "npcPos": 0}, {"id": 64102, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYellowMountainStageView", "clickRange": "0", "clickNum": 1, "character": 144072, "sound": "0", "desc": "带上你底下的小妖，永世不准再踏足武当山。否则，就没有下次了。", "nextID": 64103, "returnID": "0", "npcPos": 0}, {"id": 64103, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteYellowMountainStageView", "clickRange": "0", "clickNum": 1, "character": 144065, "sound": "0", "desc": "是……是，多谢上仙饶命之恩，我等这就离去，此生绝不再有妄念！", "nextID": 0, "returnID": "0", "npcPos": 1}, {"id": 64201, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "PharmacyEnterView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友，药师对决已经开启啦，这是炼药师的必经之路，随我来看看吧！", "nextID": 64202, "returnID": "0", "npcPos": 0}, {"id": 64202, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "PharmacyEnterView", "clickRange": "enter_1_1", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 64203, "returnID": "0", "npcPos": 0}, {"id": 64203, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "PharmacyOneGameView", "clickRange": "born<PERSON><PERSON>", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 64204, "returnID": "0", "npcPos": 0}, {"id": 64204, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "PharmacyOneGameView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友，每次炼药随机获得1-6级的药材，当盘面满15个之后自动合成。", "nextID": 64205, "returnID": "0", "npcPos": 0}, {"id": 64205, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "PharmacyOneGameView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友，2个及以上6级药材后即可合成丹药，丹药可以前往参与比赛。", "nextID": 64206, "returnID": "0", "npcPos": 0}, {"id": 64206, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "PharmacyOneGameView", "clickRange": "btnGo", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 64207, "returnID": "0", "npcPos": 0}, {"id": 64207, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "PharmacyOneMatchView", "clickRange": "btnMatch", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 64208, "returnID": "0", "npcPos": 0}, {"id": 64208, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "PharmacyOneMatchView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友，每场比赛有50个丹药即可比赛，按照每个道友的最高评分丹药进行排名。", "nextID": 64209, "returnID": "0", "npcPos": 0}, {"id": 64209, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "PharmacyOneMatchView", "clickRange": "closeBtn", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 64210, "returnID": "0", "npcPos": 0}, {"id": 64210, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "PharmacyEnterView", "clickRange": "0", "clickNum": 1, "character": 0, "sound": "0", "desc": "道友，斗气大陆炼药师众多，唯有全力以赴，方能在比赛中脱颖而出。", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 64301, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "PharmacyEnterView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友，恭喜你成功晋级丹塔大会，接下来请全力以赴做好准备吧。", "nextID": 64302, "returnID": "0", "npcPos": 0}, {"id": 64302, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "PharmacyEnterView", "clickRange": "enter_2_1", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 64303, "returnID": "0", "npcPos": 0}, {"id": 64303, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemotePharmacyTwoCookingView", "clickRange": "btn_lianzhi", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 64304, "returnID": "0", "npcPos": 0}, {"id": 64304, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemotePharmacyTwoCookingView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友，每个生灵之焱子火可以随机获得一味药材，4个药材可以随机合成丹药。", "nextID": 64305, "returnID": "0", "npcPos": 0}, {"id": 64305, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemotePharmacyTwoCookingView", "clickRange": "closeBtn", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 64306, "returnID": "0", "npcPos": 0}, {"id": 64306, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "PharmacyEnterView", "clickRange": "enter_2_2", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 64307, "returnID": "0", "npcPos": 0}, {"id": 64307, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemotePharmacyTwoGameView", "clickRange": "tagBtn2", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 64308, "returnID": "0", "npcPos": 0}, {"id": 64308, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemotePharmacyTwoGameView", "clickRange": "two_add", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 64309, "returnID": "0", "npcPos": 0}, {"id": 64309, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemotePharmacyTwoGameView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友，获得丹药后即可前往丹塔开启丹会。", "nextID": 64310, "returnID": "0", "npcPos": 0}, {"id": 64310, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemotePharmacyTwoGameView", "clickRange": "two_tys", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 64311, "returnID": "0", "npcPos": 0}, {"id": 64311, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemotePharmacyTwoGameView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友，对于可参与的丹会，点击“评鉴”即可进行点评，双方均可获得评鉴积分。", "nextID": 64312, "returnID": "0", "npcPos": 0}, {"id": 64312, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemotePharmacyTwoGameView", "clickRange": "closeBtn", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 64313, "returnID": "0", "npcPos": 0}, {"id": 64313, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "PharmacyEnterView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友，接下来需全力以赴，唯有跻身前列名次，方能晋级药族大典哦。", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 64401, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "PharmacyEnterView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友，恭喜你成功晋级药族大典，接下来请开始全力准备吧。", "nextID": 64402, "returnID": "0", "npcPos": 0}, {"id": 64402, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "PharmacyEnterView", "clickRange": "enter_3_1", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 64403, "returnID": "0", "npcPos": 0}, {"id": 64403, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemotePharmacyThreeGameView", "clickRange": "btnPresent", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 64404, "returnID": "0", "npcPos": 0}, {"id": 64404, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "PharmacyPresentView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友，药族大典需以9品丹药方可开启，记得通过礼包进行购买哦。", "nextID": 64405, "returnID": "0", "npcPos": 0}, {"id": 64405, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "PharmacyPresentView", "clickRange": "closeBtn", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 64406, "returnID": "0", "npcPos": 0}, {"id": 64406, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemotePharmacyThreeGameView", "clickRange": "btnCook", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 64407, "returnID": "0", "npcPos": 0}, {"id": 64407, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemotePharmacyThreeGameView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友，购买丹药后，选择对应的9品丹药即可开启丹会。", "nextID": 64408, "returnID": "0", "npcPos": 0}, {"id": 64408, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemotePharmacyThreeGameView", "clickRange": "closeBtn", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 64409, "returnID": "0", "npcPos": 0}, {"id": 64409, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "PharmacyEnterView", "clickRange": "0", "clickNum": 1, "character": 0, "sound": "0", "desc": "道友，已经准备完毕，快开启大典，让全服玩家一起评鉴吧！", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 64600, "type": 2, "triggerType": 2, "triggerPar": "302", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 670001, "sound": "0", "desc": "呜呜…你们在妾身的小店砍价毫不心慈手软，小店已经快难以为继了，为此妾身只能再拓新业，道友不了解一下吗?", "nextID": 64601, "returnID": "0", "npcPos": 0}, {"id": 64601, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "comAssistan", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 64602, "returnID": "0", "npcPos": 0}, {"id": 64602, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteAssistantMainView", "clickRange": "0", "clickNum": 1, "character": 670001, "sound": "0", "desc": "此乃妾身为道友整理的修行事务，只需选定事务，轻点‘打理’，妾身便可为道友打理一切！", "nextID": 64603, "returnID": "0", "npcPos": 0}, {"id": 64603, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteAssistantMainView", "clickRange": "btnSystem", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 64604, "returnID": "0", "npcPos": 0}, {"id": 64604, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteAssistantFunctionPreviewView", "clickRange": "0", "clickNum": 1, "character": 670001, "sound": "0", "desc": "这些修行琐事妾身皆可代劳，道友只需支付少量酬劳，便可解我珍宝阁如今的燃眉之急。", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 64700, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 670001, "sound": "0", "desc": "道友，据说霓裳阁内珍藏有不少仙绣羽衣，件件皆为灵丝织就、流光溢彩的珍品！不知可否随妾身前去一看呢？", "nextID": 64701, "returnID": "0", "npcPos": 0}, {"id": 64701, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "comAssistan", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 64702, "returnID": "0", "npcPos": 0}, {"id": 64702, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteAssistantMainView", "clickRange": "btnSkin", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 64800, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 670001, "sound": "0", "desc": "道友，妾身的位置可随你心意调整，只需轻轻一拖，便可吩咐妾身去往最合适的地方！", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 64900, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "RemoteKunlunWarMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友，昆仑争霸已开启，角逐最强妖盟，问鼎三界之巅！", "nextID": 64901, "returnID": "0", "npcPos": 0}, {"id": 64901, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteKunlunWarMainView", "clickRange": "btnInto", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 64902, "returnID": "0", "npcPos": 0}, {"id": 64902, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteKunlunWarGameView", "clickRange": "unionCity", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 64903, "returnID": "0", "npcPos": 0}, {"id": 64903, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteKunlunWarGameView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友，此地乃你妖盟的根基！率领麾下精锐，攻占敌盟大本营，将他们逐出昆仑大陆！", "nextID": 64904, "returnID": "0", "npcPos": 0}, {"id": 64904, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteKunlunWarGameView", "clickRange": "otherCity", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 64905, "returnID": "0", "npcPos": 0}, {"id": 64905, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteKunlunWarGameView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友，每一个地块均可进行探索，待进度圆满，即可将其纳入妖盟版图！", "nextID": 64906, "returnID": "0", "npcPos": 0}, {"id": 64906, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteKunlunWarGameView", "clickRange": "bridgeCity", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 64907, "returnID": "0", "npcPos": 0}, {"id": 64907, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteKunlunWarGameView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友，当占领的地块与城池相连，即可发起攻城！", "nextID": 64908, "returnID": "0", "npcPos": 0}, {"id": 64908, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteKunlunWarGameView", "clickRange": "mainCity", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 64909, "returnID": "0", "npcPos": 0}, {"id": 64909, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteKunlunWarGameView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "每日20点，中心城争夺战正式打响！集结妖盟之力，问鼎昆仑，成就三界霸主！", "nextID": 64910, "returnID": "0", "npcPos": 0}, {"id": 64910, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteKunlunWarGameView", "clickRange": "playerIcon", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 64911, "returnID": "0", "npcPos": 0}, {"id": 64911, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteKunlunWarGameView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友，万事俱备，踏上昆仑大陆，开启属于你的征途！", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 66000, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "RemoteRoadDefendFightView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "灵界正在遭到域外天魔的攻击，我们需要支援", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 65000, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWarSeasonMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "上古战场开启了第二赛季：幽冥战场，让我看看有哪些改动吧！", "nextID": 65001, "returnID": "0", "npcPos": 0}, {"id": 65001, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWarSeasonMainView", "clickRange": "btnRankReward", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 65002, "returnID": "0", "npcPos": 0}, {"id": 65002, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWarSeasonRankRewardView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "第二赛季去除了个人榜奖励，新增妖盟战功榜奖励~", "nextID": 65003, "returnID": "0", "npcPos": 0}, {"id": 65003, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWarSeasonRankRewardView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "妖盟战功榜奖励需要先以妖盟的名次为主，再根据妖盟内的战功排名进行奖励的领取！", "nextID": 65004, "returnID": "0", "npcPos": 0}, {"id": 65004, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWarSeasonRankRewardView", "clickRange": "comboBox", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 65005, "returnID": "0", "npcPos": 0}, {"id": 65005, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWarSeasonRankRewardView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "在右上角下拉可选择查看不同妖盟名次的奖励哦~", "nextID": 65006, "returnID": "0", "npcPos": 0}, {"id": 65006, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWarSeasonRankRewardView", "clickRange": "closeBtn", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 65100, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWarSeasonMainView", "clickRange": "btnUnion", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 65101, "returnID": "0", "npcPos": 0}, {"id": 65101, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWarSeasonUnionDomainView", "clickRange": "strategyBtn", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 65102, "returnID": "0", "npcPos": 0}, {"id": 65102, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWarSeasonStrategyView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "幽冥战场新增计策玩法，指挥官们可以发动奇诡计策，在战斗中起到决胜的作用！", "nextID": 65103, "returnID": "0", "npcPos": 0}, {"id": 65103, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteWarSeasonStrategyView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "更多功能优化，道友可自行前往玩法内体验！", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 65200, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "DestinyDetailView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "恭喜道友和仙友建立了深厚的仙缘", "nextID": 65201, "returnID": "0", "npcPos": 0}, {"id": 65201, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "DestinyDetailView", "clickRange": "btn_skin", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 65202, "returnID": "0", "npcPos": 0}, {"id": 65202, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "DestinySkinView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "现在道友可以进行仙缘幻化", "nextID": 65203, "returnID": "0", "npcPos": 0}, {"id": 65203, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "DestinySkinView", "clickRange": "tagBtn1", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 65204, "returnID": "0", "npcPos": 0}, {"id": 65204, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "DestinySkinView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友试试为仙友幻化上不同的仙缘形象吧", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 66101, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemotePupilRogueMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "掌门，你终于来啦！", "nextID": 66102, "returnID": "0", "npcPos": 0}, {"id": 66102, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemotePupilRogueMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "近日宗门周遭邪修频现，山下的村民深受其扰。", "nextID": 66103, "returnID": "0", "npcPos": 0}, {"id": 66103, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemotePupilRogueMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "不如借此机会，遣弟子们下山驱逐邪修，不仅可保村民平安，更可磨砺他们的修为。", "nextID": 66104, "returnID": "0", "npcPos": 0}, {"id": 66104, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemotePupilRogueMainView", "clickRange": "btn_explore", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 66105, "returnID": "0", "npcPos": 0}, {"id": 66105, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemotePupilRogueExploreMapView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "冒险需消耗体力，体力会随时间自然恢复，达到上限后不再增加。", "nextID": 66106, "returnID": "0", "npcPos": 0}, {"id": 66106, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemotePupilRogueExploreMapView", "clickRange": "com_energy", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 66107, "returnID": "0", "npcPos": 0}, {"id": 66107, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemotePupilRogueExploreMapView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "一切就绪，现在就让弟子开始冒险吧！", "nextID": 66108, "returnID": "0", "npcPos": 0}, {"id": 66108, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemotePupilRogueExploreMapView", "clickRange": "btn_explore", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 66109, "returnID": "0", "npcPos": 0}, {"id": 66109, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemotePupilRogueLevelView", "clickRange": "btn_start", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 66201, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemotePupilRogueMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "此次历练，弟子们收获了不少的修炼材料噢，现在就去修炼试试看吧！", "nextID": 66202, "returnID": "0", "npcPos": 0}, {"id": 66202, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemotePupilRogueMainView", "clickRange": "btn_cultivate", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 66203, "returnID": "0", "npcPos": 0}, {"id": 66203, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemotePupilRogueSectView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "不同流派修炼需要消耗的材料不同。每修炼7次将提升该流派的一个技能效果。", "nextID": 66204, "returnID": "0", "npcPos": 0}, {"id": 66204, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemotePupilRogueSectView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "每次修炼，弟子还可获得属性加成，即便是不同流派的属性加成也均会生效噢。", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 66301, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemotePupilRogueExploreMapView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "哇噢，在掌门的英明指引下，众弟子圆满完成首次历练。现在可派遣弟子神游，重温过往险境。", "nextID": 66302, "returnID": "0", "npcPos": 0}, {"id": 66302, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemotePupilRogueExploreMapView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "神游同样也需要消耗体力，但能够获得修炼材料噢。", "nextID": 66303, "returnID": "0", "npcPos": 0}, {"id": 66303, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemotePupilRogueExploreMapView", "clickRange": "btn_travel", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 66304, "returnID": "0", "npcPos": 0}, {"id": 66304, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemotePupilRogueTravelView", "clickRange": "speed_combo", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 66305, "returnID": "0", "npcPos": 0}, {"id": 66305, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemotePupilRogueTravelView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "可选择多倍神游，根据选择的倍数决定消耗的体力；同样多倍神游可获得多倍的奖励噢。", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 67000, "type": 2, "triggerType": 2, "triggerPar": "307", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友，九天瑶池重现天日，据传此地仙色旖旎，仙友们憧憬已久，快带上她们一起去看看吧！", "nextID": 67001, "returnID": "0", "npcPos": 0}, {"id": 67001, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "btnHome", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 67002, "returnID": "0", "npcPos": 0}, {"id": 67002, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "SystemListView", "clickRange": "btnDestiny", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 67013, "returnID": "0", "npcPos": 0}, {"id": 67013, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "DestinyView", "clickRange": "btn_lotus", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 67014, "type": 1, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "DestinyView", "clickRange": "lakeBtn", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 67003, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "DestinyFightMainView", "clickRange": "cloud1", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 67004, "returnID": "0", "npcPos": 0}, {"id": 67004, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "DestinyFightTaskDetailView", "clickRange": "btn_go", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 67005, "returnID": "0", "npcPos": 0}, {"id": 67005, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "DestinyFightDestinyDelegateView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "此处可派遣仙友来完成仙家们的委托，仙友的总魅力值代表了这只队伍的战力，每个仙友每周接受委托的次数有限，还望道友注意噢。", "nextID": 67006, "returnID": "0", "npcPos": 0}, {"id": 67006, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "DestinyFightDestinyDelegateView", "clickRange": "btn_close", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 67007, "returnID": "0", "npcPos": 0}, {"id": 67007, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "DestinyFightTaskDetailView", "clickRange": "btn_close", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 67008, "returnID": "0", "npcPos": 0}, {"id": 67008, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "DestinyFightMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "除了仙家的委托，道友还可以进入瑶池禁地，挑战那儿的守卫来获取奖励噢。", "nextID": 67009, "returnID": "0", "npcPos": 0}, {"id": 67009, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "DestinyFightMainView", "clickRange": "openBtn", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 67010, "type": 1, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "DestinyFightGameView", "clickRange": "playerItem", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 67011, "returnID": "0", "npcPos": 0}, {"id": 67011, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "DestinyFightZonePlayerDetail", "clickRange": "btn_fight", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 67012, "returnID": "0", "npcPos": 0}, {"id": 67012, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "DestinyFightDestinyDelegateView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友可派遣仙友出战，注意出战仙友的挑战次数，仙友的每周出战次数有上限，该挑战次数将会每周重置。", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 67100, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "DestinyView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "真是莫大机缘，仙家们为感谢道友的帮忙，特送来了一朵赐福神花，快去看看吧！", "nextID": 67101, "returnID": "0", "npcPos": 0}, {"id": 67101, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "DestinyView", "clickRange": "btn_lotus", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 67102, "returnID": "0", "npcPos": 0}, {"id": 67102, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "DestinyView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "此神花名天池瑶光花，道友可收集琼浆来为其滋养，待神花绽放后更能带来莫大的福缘！", "nextID": 67103, "returnID": "0", "npcPos": 0}, {"id": 67103, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "DestinyView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "天池瑶光花绽放后开启的槽位可为天赋相合的仙友带来修为加成，快邀请仙友们一起承纳神花的赐福吧。", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 67200, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "DestinyFightMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "真是莫大机缘，仙家们为感谢道友的帮忙，特送来了一朵赐福神花，快去看看吧！", "nextID": 67201, "returnID": "0", "npcPos": 0}, {"id": 67201, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "DestinyFightMainView", "clickRange": "btn_close", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 67202, "returnID": "0", "npcPos": 0}, {"id": 67202, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "DestinyView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "此神花名天池瑶光花，道友可收集琼浆来为其滋养，待神花绽放后更能带来莫大的福缘！", "nextID": 67203, "returnID": "0", "npcPos": 0}, {"id": 67203, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "DestinyView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "天池瑶光花绽放后开启的槽位可为天赋相合的仙友带来修为加成，快邀请仙友们一起承纳神花的赐福吧。", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 68000, "type": 1, "triggerType": 2, "triggerPar": "306", "eventPar": "0", "scene": "MainView", "clickRange": "btnCloudSmail", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 68001, "returnID": "0", "npcPos": 0}, {"id": 68001, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "CloudMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "经过道友长时间对座驾的灵力注入，座驾诞生了器灵", "nextID": 68002, "returnID": "0", "npcPos": 0}, {"id": 68002, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "CloudMainView", "clickRange": "listTag2", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 68003, "returnID": "0", "npcPos": 0}, {"id": 68003, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "CloudMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "座驾器灵可以为道友在战斗中提供额外的助力，解锁一个器灵看看吧", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 69001, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "RemoteHeavenBattleGameView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友以阵营为单位，从四个方位的四大天门出发", "nextID": 69002, "returnID": "0", "npcPos": 0}, {"id": 69002, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteHeavenBattleGameView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友需要与自己阵营共同推进，前往四大天王所在位置击败四大天王！", "nextID": 69003, "returnID": "0", "npcPos": 0}, {"id": 69003, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteHeavenBattleGameView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "杨戬开启后，道友需要与其他阵营道友共同击败杨戬，结束本次仙妖大战！", "nextID": 69004, "returnID": "0", "npcPos": 0}, {"id": 69004, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteHeavenBattleGameView", "clickRange": "btnHead", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 69005, "returnID": "0", "npcPos": 0}, {"id": 69005, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "SeparationMainViewEx", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友进入战场后，受到诛妖大阵的影响，装备属性失效，特殊属性也受到压制", "nextID": 69006, "returnID": "0", "npcPos": 0}, {"id": 69006, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "SeparationMainViewEx", "clickRange": "btnInfo", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 69007, "returnID": "0", "npcPos": 0}, {"id": 69007, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteHeavenBattleInheritView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友可在占领战格的过程中恢复基础属性，而占领地图内的大阵阵眼可恢复特殊属性！", "nextID": 69008, "returnID": "0", "npcPos": 0}, {"id": 69008, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteHeavenBattleInheritView", "clickRange": "btnClose", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 69009, "returnID": "0", "npcPos": 0}, {"id": 69009, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "SeparationMainViewEx", "clickRange": "btnHeavenBattleClose", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 69010, "returnID": "0", "npcPos": 0}, {"id": 69010, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteHeavenBattleGameView", "clickRange": "btnExp", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 69011, "returnID": "0", "npcPos": 0}, {"id": 69011, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteHeavenBattleExpView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友可在占领战格的过程中获得灵元碎片，获得后会自动献祭给妖神幡获得基础属性提升！", "nextID": 69012, "returnID": "0", "npcPos": 0}, {"id": 69012, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteHeavenBattleExpView", "clickRange": "btnClose", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 69013, "returnID": "0", "npcPos": 0}, {"id": 69013, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteHeavenBattleGameView", "clickRange": "btnStragy", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 69014, "returnID": "0", "npcPos": 0}, {"id": 69014, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteHeavenBattleStrategyView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友和指挥官可在这查看/标记每日的战略目标~", "nextID": 69015, "returnID": "0", "npcPos": 0}, {"id": 69015, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteHeavenBattleStrategyView", "clickRange": "btnClose", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 69016, "returnID": "0", "npcPos": 0}, {"id": 69016, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteHeavenBattleGameView", "clickRange": "btnBuff", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 69017, "returnID": "0", "npcPos": 0}, {"id": 69017, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteHeavenBattleBuffView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友可在这给不同分身装配技能，阵营祝福则获得后可直接生效！", "nextID": 69018, "returnID": "0", "npcPos": 0}, {"id": 69018, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteHeavenBattleBuffView", "clickRange": "btnClose", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 69019, "returnID": "0", "npcPos": 0}, {"id": 69019, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteHeavenBattleGameView", "clickRange": "btnPreview", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 69020, "returnID": "0", "npcPos": 0}, {"id": 69020, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteHeavenBattlePreviewView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友可在此处查看各个战格战令后的作用和获得的奖励！", "nextID": 69021, "returnID": "0", "npcPos": 0}, {"id": 69021, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteHeavenBattlePreviewView", "clickRange": "btnClose", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 69022, "returnID": "0", "npcPos": 0}, {"id": 69022, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteHeavenBattleGameView", "clickRange": "btnReward", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 69023, "returnID": "0", "npcPos": 0}, {"id": 69023, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteHeavenBattleRewardView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友可在此处领取战格占据后获得的奖励，请及时领取！", "nextID": 69024, "returnID": "0", "npcPos": 0}, {"id": 69024, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteHeavenBattleRewardView", "clickRange": "btnClose", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 69025, "returnID": "0", "npcPos": 0}, {"id": 69025, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteHeavenBattleGameView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友可以点击头像回到自身所在位置，道友会停留在自身上一次攻打的地块处~", "nextID": 69026, "returnID": "0", "npcPos": 0}, {"id": 69026, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteHeavenBattleGameView", "clickRange": "btnMySelf", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 69027, "returnID": "0", "npcPos": 0}, {"id": 69027, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteHeavenBattleGameView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友快去同自身阵营其他道友，攻破天庭，战胜杨戬，结束本次大战吧！", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 69100, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteCalabashBrothersMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "不好啦！蛇妖来犯！道友，恳请你帮帮葫芦娃他们！", "nextID": 69101, "returnID": "0", "npcPos": 0}, {"id": 69101, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteCalabashBrothersEnhanceView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "这里是葫芦娃强化区，每轮会刷新属性或技能加强词条来供道友排布。", "nextID": 69102, "returnID": "0", "npcPos": 0}, {"id": 69102, "type": 5, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteCalabashBrothersEnhanceView", "clickRange": "0", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 69103, "returnID": "0", "npcPos": 0}, {"id": 69103, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteCalabashBrothersMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "糟了糟了，蛇妖来了！葫芦娃登场后会在固定的位置对蛇妖发起攻击。登场后的葫芦娃不可移动，请道友注意排兵布阵哦！", "nextID": 69104, "returnID": "0", "npcPos": 0}, {"id": 69104, "type": 3, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "RemoteCalabashBrothersMainView", "clickRange": "-1", "clickNum": 1, "character": 0, "sound": "0", "desc": "0", "nextID": 69105, "returnID": "0", "npcPos": 0}, {"id": 69105, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteCalabashBrothersMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "蛇妖来势汹汹，葫芦娃的攻击方向可随道友指尖所点即时调整！不过要注意——正在飞射中的攻击不会中途转向，新的方向将在下次出手时生效！", "nextID": 69106, "returnID": "0", "npcPos": 0}, {"id": 69106, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteCalabashBrothersMainView", "clickRange": "btnTouch", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 69107, "returnID": "0", "npcPos": 0}, {"id": 69107, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteCalabashBrothersMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "完成上方的进度条可获得强化奖励，蛇妖身上也有可能携有强化奖励噢，只需打落其身上的宝袋即可获得！", "nextID": 69108, "returnID": "0", "npcPos": 0}, {"id": 69108, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteCalabashBrothersMainView", "clickRange": "compNatureTip", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 69109, "returnID": "0", "npcPos": 0}, {"id": 69109, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteCalabashBrothersNatureTipView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "当心！蛇妖的某些部位暗藏玄机，若用同属性葫芦娃攻击，不仅伤害锐减，还可能让蛇妖愈发猖狂！", "nextID": 69110, "returnID": "0", "npcPos": 0}, {"id": 69110, "type": 4, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteCalabashBrothersNatureTipView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 69111, "returnID": "0", "npcPos": 0}, {"id": 69111, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteCalabashBrothersMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友务必运筹帷幄，助葫芦娃克敌制胜，守护家园！", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 69201, "type": 2, "triggerType": 2, "triggerPar": "310", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友，以你目前的修为，是时候修炼神躯，步入更高的境界了！现在，先让我们进行内视吧！", "nextID": 69202, "returnID": "0", "npcPos": 0}, {"id": 69202, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "btnLeft", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 69203, "returnID": "0", "npcPos": 0}, {"id": 69203, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "comDivineTouch", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 69204, "returnID": "0", "npcPos": 0}, {"id": 69204, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteDivineInsightMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友，这里就是你神躯内的盛景噢，现在，让我们先去收集丹田里汇聚的真元吧！", "nextID": 69205, "returnID": "0", "npcPos": 0}, {"id": 69205, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteDivineInsightMainView", "clickRange": "touchDantian1", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 69206, "type": 2, "triggerType": 3, "triggerPar": "0", "eventPar": "0", "scene": "RemoteDivineInsightMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "收集到的真元，可以用来修炼我们的神藏噢！", "nextID": 69207, "returnID": "0", "npcPos": 0}, {"id": 69207, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteDivineInsightMainView", "clickRange": "touchBuilding1", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 69208, "returnID": "0", "npcPos": 0}, {"id": 69208, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteDivineInsightBuildingView", "clickRange": "touchMainBuilding", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 69209, "returnID": "0", "npcPos": 0}, {"id": 69209, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteDivineInsightBuildingView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "每个神藏内都供奉着相应的神祇，神祇境界越高，对道友修为的提升也越大。", "nextID": 69210, "returnID": "0", "npcPos": 0}, {"id": 69210, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteDivineInsightBuildingView", "clickRange": "btnUpgrade", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 69211, "returnID": "0", "npcPos": 0}, {"id": 69211, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteDivineInsightBuildingView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "除了神祇，神藏内还有着4个道宫，当4个道宫都达到等级要求时，便可提升神祇的境界。", "nextID": 69212, "returnID": "0", "npcPos": 0}, {"id": 69212, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteDivineInsightBuildingView", "clickRange": "btnMain", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 69213, "returnID": "0", "npcPos": 0}, {"id": 69213, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteDivineInsightBuildingView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "神祇境界的提升，能够激活特殊属性噢，但这个提升的过程，需要一定的时间。", "nextID": 69214, "returnID": "0", "npcPos": 0}, {"id": 69214, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteDivineInsightBuildingView", "clickRange": "btnClose", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 69215, "returnID": "0", "npcPos": 0}, {"id": 69215, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteDivineInsightMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "丹田气海可依靠本源元婴，来源源不断的汇聚真元噢。", "nextID": 69216, "returnID": "0", "npcPos": 0}, {"id": 69216, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteDivineInsightMainView", "clickRange": "touchDantian2", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 69217, "returnID": "0", "npcPos": 0}, {"id": 69217, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteDivineInsightDantianBuildingView", "clickRange": "touchUnlockBuilding", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 69218, "returnID": "0", "npcPos": 0}, {"id": 69218, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteDivineInsightDantianBuildingView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "升级气海丹田可以解锁更多的本源元婴，本源元婴越多，真元汇聚速度越快噢。", "nextID": 69219, "returnID": "0", "npcPos": 0}, {"id": 69219, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteDivineInsightDantianBuildingView", "clickRange": "btnClose", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 69220, "returnID": "0", "npcPos": 0}, {"id": 69220, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteDivineInsightMainView", "clickRange": "btnSpinal", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 69221, "returnID": "0", "npcPos": 0}, {"id": 69221, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteDivineInsightSpinalView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "神藏总等级达到要求后，即可对神脊进行进阶，进阶后会获得稀有属性。", "nextID": 69222, "returnID": "0", "npcPos": 0}, {"id": 69222, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteDivineInsightSpinalView", "clickRange": "btnClose", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 69223, "returnID": "0", "npcPos": 0}, {"id": 69223, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteDivineInsightMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "天道感悟可以获得全局属性加成，另外也可提高神躯试炼中的属性。", "nextID": 69224, "returnID": "0", "npcPos": 0}, {"id": 69224, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteDivineInsightMainView", "clickRange": "btnInspire", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 69225, "returnID": "0", "npcPos": 0}, {"id": 69225, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteDivineInsightInspireView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "天道感悟只能逐一领悟，每个感悟满级后才能领悟下一层。", "nextID": 69226, "returnID": "0", "npcPos": 0}, {"id": 69226, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteDivineInsightInspireView", "clickRange": "touchInspire1", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 69227, "returnID": "0", "npcPos": 0}, {"id": 69227, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteDivineInsightInspireDetailView", "clickRange": "btnUpgrade", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 69228, "returnID": "0", "npcPos": 0}, {"id": 69228, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteDivineInsightInspireView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "天道感悟需要时间进行领悟，领悟时间结束后将会自动完成。", "nextID": 69229, "returnID": "0", "npcPos": 0}, {"id": 69229, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteDivineInsightInspireView", "clickRange": "btnClose", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 69230, "returnID": "0", "npcPos": 0}, {"id": 69230, "type": 2, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "RemoteDivineInsightMainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "神躯修炼已经准备完成，道友努力修炼神躯，飞升上界！", "nextID": 0, "returnID": "0", "npcPos": 0}, {"id": 69300, "type": 2, "triggerType": 2, "triggerPar": "309", "eventPar": "0", "scene": "MainView", "clickRange": "0", "clickNum": 1, "character": 121003, "sound": "0", "desc": "道友，五行之力汇聚，神躯开始觉醒，新的试炼开启，随我一同前往神躯试炼共探神的境界！", "nextID": 69301, "returnID": "0", "npcPos": 0}, {"id": 69301, "type": 1, "triggerType": 0, "triggerPar": "0", "eventPar": "0", "scene": "MainView", "clickRange": "btnChallenge", "clickNum": 1, "character": 121003, "sound": "0", "desc": "0", "nextID": 0, "returnID": "0", "npcPos": 0}]