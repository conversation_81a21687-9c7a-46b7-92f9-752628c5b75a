{"17": {"name": "S_SYSTEMSHIELD_DATA_SYNC", "smMethod": "SystemShieldSync"}, "20001": {"name": "S_PLAYER_LOGIN", "cmMethod": "ReqLogin<PERSON>g", "smMethod": "RspLoginMsg"}, "20002": {"name": "S_LOGIN_SYNC_OVER", "smMethod": "LoginOverMsg"}, "20003": {"name": "S_PLAYER_PING", "cmMethod": "ReqPingMsg", "smMethod": "RspPingMsg"}, "20011": {"name": "S_OTHER_LOGIN_YOUR_ROLE", "smMethod": "OtherLoginMsg"}, "20016": {"name": "S_SYN_BACKEND_CONFIG", "smMethod": "RspConfigMsg"}, "20031": {"name": "S_GET_POLICY_INFO_REQ", "cmMethod": "GetPolicyInfoReq", "smMethod": "GetPolicyInfoRsp"}, "20032": {"name": "S_BACK_INFO_SYNC", "smMethod": "SyncRecallPlayerMsg"}, "20033": {"name": "S_GET_BIND_STATUS_REQ", "cmMethod": "GetBindStatusReq", "smMethod": "GetBindStatusResp"}, "20034": {"name": "S_GET_BIND_CODE_REQ", "cmMethod": "GetBindCodeReq", "smMethod": "GetBindCodeResp"}, "20035": {"name": "S_UNTIE_BIND_CODE_REQ", "cmMethod": "UntieBindCodeReq", "smMethod": "UntieBindCodeResp"}, "20036": {"name": "S_GET_BIND_CODE_INFO_REQ", "cmMethod": "GetBindCodeInfoReq", "smMethod": "GetBindCodeInfoResp"}, "20040": {"name": "S_GET_GROUP_INFO", "cmMethod": "GetSystemGroupInfoReq", "smMethod": "GetSystemGroupInfoResp"}, "20037": {"name": "S_DELETE_PLAYER", "cmMethod": "DeletePlayerReq", "smMethod": "DeletePlayerResp"}, "20004": {"name": "S_PRECHARGE", "cmMethod": "PreChargeReq", "smMethod": "PreChargeResp"}, "20005": {"name": "S_NOTIFY_RECHARGE_SUCCESS", "smMethod": "NotifyRechargeSuccess"}, "20604": {"name": "S_MALL_CHANCE_BUY", "cmMethod": "MallChanceUseReqMsg", "smMethod": "MallChanceUseRespMsg"}, "20101": {"name": "S_PLAYER_DATA_SYNC_MSG", "smMethod": "PlayerDataMsg"}, "20102": {"name": "S_SYSTEM_UNLOCK_SYNC_MSG", "smMethod": "SystemUnlockSync"}, "20103": {"name": "S_SYSTEM_GET_REWARD_MSG", "cmMethod": "GetSysRewardReq", "smMethod": "GetSysRewardResp"}, "20006": {"name": "S_CREATE_ROLE", "cmMethod": "CreateRoleReq", "smMethod": "CreateRoleResp"}, "20007": {"name": "S_GET_RANDOM_NICKNAME", "cmMethod": "", "smMethod": "RandomNickNameResp"}, "20106": {"name": "S_PLAYER_CHARA_DATA_SYNC", "cmMethod": "", "smMethod": "PlayerCharaDataMsg"}, "20107": {"name": "S_PLAYER_CHARA_CHANGE", "cmMethod": "ChangePlayerCharaReq", "smMethod": "ChangePlayerCharaResp"}, "20108": {"name": "S_PLAYER_GET_CHARA_LIST", "cmMethod": "", "smMethod": "GetPlayerCharaListResp"}, "20109": {"name": "S_GET_PLAYER_DETAIL_DATA", "cmMethod": "getPlayerDetailDataReq", "smMethod": "getPlayerDetailDataResp"}, "20134": {"name": "S_GET_PLAYER_DETAIL_DATA_BY_SYSTEM", "cmMethod": "getLockedPlayerDetailDataReq", "smMethod": "getLockedPlayerDetailDataResp"}, "20135": {"name": "S_GET_PLAYER_PILOT_SHOW_DATA_MSG_LIST", "cmMethod": "getPlayerPilotShowDataMsgListReq", "smMethod": "getPlayerPilotShowDataMsgListResp"}, "20136": {"name": "S_PLAYER_COMMON_SEARCH_PLAYER", "cmMethod": "CommonSearchPlayerReq", "smMethod": "CommonSearchPlayerResp"}, "20150": {"name": "S_PLAYER_CHARA_COLLECT", "cmMethod": "CollectPlayerCharaReq", "smMethod": "CollectPlayerCharaResp"}, "20151": {"name": "S_PLAYER_CHARA_UPGRADE", "cmMethod": "UpgradePlayerCharaReq", "smMethod": "UpgradePlayerCharaResp"}, "20008": {"name": "S_CHANGE_NICK_NAME", "cmMethod": "ChangeNickNameReq", "smMethod": "ChangeNickNameResp"}, "20009": {"name": "S_EXCHANGE_CODE", "cmMethod": "UseExchangeKeyReq", "smMethod": "UseExchangeKeyResp"}, "20010": {"name": "S_GET_SERVER_CONFIG_VERSION", "cmMethod": "", "smMethod": "GetServerConfigVersionResp"}, "20125": {"name": "S_SHIELD_MALL_RECHARGE_ID", "smMethod": "MallRechargeIdShieldSync"}, "20118": {"name": "S_GET_TOP_PLAYER_INFO", "cmMethod": "GetTopPlayerInfoReq", "smMethod": "GetTopPlayerInfoResp"}, "20119": {"name": "S_SYNC_TITLE_MSG", "cmMethod": "", "smMethod": "TitleSyncMsg"}, "20120": {"name": "S_USE_TITLE", "cmMethod": "UseTitleReq", "smMethod": "UseTitleRsp"}, "20121": {"name": "S_GOODSSHIELD_DATA_SYNC", "smMethod": "GoodsShieldSync"}, "20122": {"name": "S_SYNC_MEDAL_MSG", "cmMethod": "", "smMethod": "MedalSyncMsg"}, "20123": {"name": "S_USE_MEDAL", "cmMethod": "UseMedalReq", "smMethod": "UseMedalRsp"}, "20124": {"name": "S_ACTIVITY_POSTER_SHIELD_SYNC", "smMethod": "ActivityPosterShieldSync"}, "21201": {"name": "S_EMOTICON_DATA_SYNC", "smMethod": "PlayerEmoticonDataSync"}, "24801": {"name": "S_PALACE_SYNC", "cmMethod": "", "smMethod": "PalaceSyncMsg"}, "24802": {"name": "S_PALACE_WORSHIP", "cmMethod": "PalaceWorshipReq", "smMethod": "PalaceWorshipRsp"}, "24803": {"name": "S_PALACE_ENTER_OUTER", "cmMethod": "EnterPalaceReq", "smMethod": "EnterPalaceRsp"}, "24804": {"name": "S_PALACE_ENTER_INNER", "cmMethod": "EnterPalaceInnerReq", "smMethod": "EnterPalaceInnerRsp"}, "24805": {"name": "S_PALACE_BOOK", "cmMethod": "PalaceInnerBookReq", "smMethod": "PalaceInnerBookRsp"}, "24806": {"name": "S_PALACE_SEND_GIFT", "cmMethod": "SendGiftReq", "smMethod": "SendGiftRsp"}, "24807": {"name": "S_PALACE_SEND_GIFT_GET_REWARD", "cmMethod": "GetSendGiftRewardReq", "smMethod": "GetSendGiftRewardRsp"}, "24808": {"name": "S_PALACE_SEND_GIFT_SYNC_DATA", "cmMethod": "", "smMethod": "SendGiftSyncMsg"}, "24809": {"name": "S_PALACE_WORSHIP_MIRACLE_SYNC_DATA", "cmMethod": "", "smMethod": "PalaceMiracleDataMsg"}, "24857": {"name": "S_PALACE_ACHIEVE_GET_REWARD", "cmMethod": "PalaceAchievementGetRewardReq", "smMethod": "PalaceAchievementGetRewardRsp"}, "24810": {"name": "S_PALACE_GET_TITLE_ID_LIST", "cmMethod": "GetTitleIdListReq", "smMethod": "GetTitleIdListRsp"}, "20201": {"name": "S_ATTRIBUTE_DATA_SYNC_MSG", "smMethod": "PlayerAttributeDataMsg"}, "20202": {"name": "S_ATTRIBUTE_EQUIPMENT_DEAL_MSG", "cmMethod": "ReqDealEquipmentMsg", "smMethod": "RspDealEquipmentMsg"}, "20203": {"name": "S_ATTRIBUTE_DREAM_MSG", "cmMethod": "ReqDreamMsg", "smMethod": "RspDreamMsg"}, "20204": {"name": "S_ATTRIBUTE_REALMS_LEVE_UP_MSG", "cmMethod": "ReqRealmsLeveUpMsg", "smMethod": "RspRealmsLeveUpMsg"}, "20205": {"name": "S_ATTRIBUTE_DREAM_LV_UP", "cmMethod": "DreamLvUpReq", "smMethod": "DreamLvUpResp"}, "20206": {"name": "S_ATTRIBUTE_DREAM_LV_UP_SPEED_UP", "cmMethod": "DreamLvUpSpeedUpReq", "smMethod": "DreamLvUpSpeedUpResp"}, "20207": {"name": "S_DREAM_DATA_SYNC", "smMethod": "DreamDataMsg"}, "20208": {"name": "S_ATTRIBUTE_GET_TRIBULATION_SUCCESS_PRO", "cmMethod": "GetTribulationSuccessProReq", "smMethod": "GetTribulationSuccessProResp"}, "20209": {"name": "S_ATTRIBUTE_GET_UNDEAL_EQUIPMENT_MSG", "cmMethod": "GetUnDealEquipmentMsgReq", "smMethod": "GetUnDealEquipmentMsgResp"}, "20212": {"name": "S_ATTRIBUTE_SOARING_REQ", "cmMethod": "SoaringReq", "smMethod": "SoaringResp"}, "20213": {"name": "S_ATTRIBUTE_SET_SEPARATION_NAME_REQ", "cmMethod": "SetSeparationNameReq", "smMethod": "SetSeparationNameResp"}, "20214": {"name": "S_ATTRIBUTE_SWITCH_SEPARATION_REQ", "cmMethod": "SwitchSeparationReq", "smMethod": "SwitchSeparationResp"}, "20215": {"name": "S_ATTRIBUTE_GET_SEPARATION_DATAA_MSG_LIST_REQ", "cmMethod": "GetSeparationDataMsgListReq", "smMethod": "GetSeparationDataMsgListResp"}, "20216": {"name": "S_GOLD_PEACH_CUT_TREE", "cmMethod": "MotionSenseDreamReq", "smMethod": "MotionSenseDreamResp"}, "20501": {"name": "S_TASK_DATA_SEND", "smMethod": "TaskDataListMsg"}, "20502": {"name": "S_TASK_DATA_SYNC", "smMethod": "TaskDataListMsg"}, "20503": {"name": "S_TASK_GET_REWARD", "cmMethod": "TaskGetRewardReqMsg", "smMethod": "TaskGetRewardRespMsg"}, "20019": {"name": "S_WATCH_AD_TASK", "cmMethod": "WatchAdReq", "smMethod": "WatchAdResp"}, "20301": {"name": "S_BAG_DATA_SYNC_MSG", "smMethod": "SyncBagMsg"}, "20302": {"name": "S_BAG_USE_PROP", "cmMethod": "UsePropReq", "smMethod": "UsePropResp"}, "20303": {"name": "S_BAG_COMPOUND_PROP", "cmMethod": "CompoundPropReq", "smMethod": "CompoundPropResp"}, "20401": {"name": "S_BATTLE_TEST", "smMethod": "BattleRecordMsg"}, "20405": {"name": "S_TEAM_BATTLE_TEST", "smMethod": "TeamBattleRecordMsg"}, "20402": {"name": "S_STAGE_CHALLENGE", "smMethod": "ChallengeRspMsg"}, "20403": {"name": "S_STAGE_DATA_SYNC", "smMethod": "PlayerStageData"}, "20404": {"name": "S_STAGE_VIEW_MONSTER_ATTR", "smMethod": "ViewMonsterAttrResp"}, "21401": {"name": "S_INVADE_CHALLENGE", "smMethod": "InvadeChallengeResp"}, "21402": {"name": "S_INVADE_DATA_SYNC", "smMethod": "InvadeDataMsg"}, "20551": {"name": "S_MAIL_LIST", "smMethod": "MailListMsg"}, "20552": {"name": "S_MAIL_SYNC", "smMethod": "MailListMsg"}, "20553": {"name": "S_MAIL_DELETE_ALL_MAIL", "cmMethod": "MailDeleteAllReq", "smMethod": "MailDeleteAllResp"}, "20554": {"name": "S_MAIL_DELETE_MAIL", "cmMethod": "MailDeleteReq", "smMethod": "MailDeleteResp"}, "20555": {"name": "S_MAIL_GET_ALL_REWARD", "cmMethod": "MailGetAllRewardReq", "smMethod": "MailGetAllRewardResp"}, "20556": {"name": "S_MAIL_GET_REWARD", "cmMethod": "MailGetRewardReq", "smMethod": "MailGetRewardResp"}, "20557": {"name": "S_MAIL_READ_MAIL", "cmMethod": "MailReadReq", "smMethod": "MailReadResp"}, "20601": {"name": "S_MALL_BUY_GOODS", "cmMethod": "BuyGoodsReq", "smMethod": "BuyGoodsResp"}, "20602": {"name": "S_MALL_BUY_COUNT_SYNC", "smMethod": "MallBuyCountListMsg"}, "20603": {"name": "S_MALL_RANDOM_GOODS", "cmMethod": "MallRandomGoodsReqMsg", "smMethod": "MallRandomGoodsRespMsg"}, "20014": {"name": "S_RECHARGE_MALL_TIMES_SYNC", "smMethod": "RechargeMallTimesSync"}, "2015401": {"name": "S_MAGIC_SHOP_ZERO_TIME_REFRESH", "cmMethod": "ActivityMagicShopLoopGetPoolDataReq", "smMethod": "ActivityMagicShopLoopGetPoolDataResp"}, "20012": {"name": "S_SYSTEM_RED_POINT_SYNC", "smMethod": "SystemRedPointSync"}, "20013": {"name": "S_ACTIVITY_RED_POINT_SYNC", "smMethod": "ActivityRedPointSync"}, "21001": {"name": "S_ACTIVITY_SYNC_MAIN_CONFIG_LIST", "smMethod": "PushActivityList"}, "21002": {"name": "S_ACTIVITY_SYNC_DETAIL_CONFIG", "smMethod": "ActivityCommonDataListSync"}, "21027": {"name": "S_ACTIVITY_SELECT_DATA_LIST_SYNC", "smMethod": "ActivityMallSelectDataListSync"}, "21003": {"name": "S_ACTIVITY_GET_DATA", "cmMethod": "ReqGetActivityDetail", "smMethod": "RspGetActivityDetail"}, "21004": {"name": "S_ACTIVITY_GET_CONDITION_REWARD", "cmMethod": "ReqGetActivityConditionReward", "smMethod": "RspGetActivityConditionReward"}, "21005": {"name": "S_ACTIVITY_BUY_MALL_GOODS", "cmMethod": "ReqBuyActivityGoods", "smMethod": "RspBuyActivityGoods"}, "21007": {"name": "S_ACTIVITY_SYNC_CONDITION_LIST", "smMethod": "ActivityConditionDataListSync"}, "21008": {"name": "S_ACTIVITY_SYNC_MALL_BUY_COUNT_LIST", "smMethod": "ActivityMallBuyCountDataListSync"}, "21009": {"name": "S_ACTIVITY_SYNC_ACTIVITY_DATA", "smMethod": "ActivityPlayerDataSync"}, "21010": {"name": "S_ACTIVITY_FUNDS_GET_REWARD", "cmMethod": "ReqGetFundsConditionReward", "smMethod": "RspGetFundsConditionReward"}, "21015": {"name": "S_ACTIVITY_SYNC_ACTIVITY_SCORE_LIST", "smMethod": "ActivityScoreDataMsgSync"}, "21016": {"name": "S_ACTIVITY_SHARE", "cmMethod": "ReqShareTaskDone", "smMethod": "RespShareTaskDone"}, "21030": {"name": "S_ACTIVITY_BBS", "cmMethod": "ReqShareTaskDone", "smMethod": "RespShareTaskDone"}, "21031": {"name": "S_ACTIVITY_GAME_CIRCLE", "cmMethod": "ReqShareTaskDone", "smMethod": "RespShareTaskDone"}, "21032": {"name": "S_ACTIVITY_SELECT_MALL_GOODS", "cmMethod": "SelectActivityGoodsReq", "smMethod": "SelectActivityGoodsResp"}, "21017": {"name": "S_ACTIVITY_GET_RANK_STATE", "cmMethod": "ReqGetActivityRankState", "smMethod": "RespGetActivityRankState"}, "21022": {"name": "S_ACTIVITY_GET_CONDITION_REWARD_BY_ARR", "cmMethod": "ReqGetActivityConditionRewardByArr", "smMethod": "RspGetActivityConditionReward"}, "21006": {"name": "S_ACTIVITY_GET_RANK_REWARD", "cmMethod": "ReqGetActivityRankReward", "smMethod": "RespGetActivityRankReward"}, "21040": {"name": "S_ACTIVITY_GUESS_INFO_LOAD", "cmMethod": "ReqGuessInfoLoadMsg", "smMethod": "RespGuessInfoLoadMsg"}, "21041": {"name": "S_ACTIVITY_GUESS", "cmMethod": "ReqGuessMsg", "smMethod": "RespGuessMsg"}, "21042": {"name": "S_ACTIVITY_GUESS_REWARD", "cmMethod": "ReqGuessRewardMsg", "smMethod": "RespGuessRewardMsg"}, "21043": {"name": "S_ACTIVITY_GUESS_RESULT_DETAIL", "cmMethod": "ReqGuessResultDetailMsg", "smMethod": "RespGuessResultDetailMsg"}, "21044": {"name": "S_ACTIVITY_SHIELD_SYNC", "smMethod": "ActivityShieldSync"}, "21045": {"name": "S_ACTIVITY_RECEIVE_CROSS_UNION_RECHARGE", "cmMethod": "ReceiveCrossUnionRechargeRewardReq", "smMethod": "ReceiveCrossUnionRechargeRewardResp"}, "21046": {"name": "S_ACTIVITY_GET_JOIN_MEMBER_LIST_REQ", "cmMethod": "ActivityGetJoinMemberListReq", "smMethod": "ActivityGetJoinMemberListRsp"}, "21033": {"name": "S_ACTIVITY_GET_BATTLE_PASS_CONDITION_REWARD", "cmMethod": "ReqGetActivityConditionReward", "smMethod": "RspGetActivityConditionReward"}, "21047": {"name": "S_ACTIVITY_OPEN_TO_DESK", "cmMethod": "ReqShareTaskDone", "smMethod": "RespShareTaskDone"}, "20801": {"name": "S_RANK_GET_LIST", "cmMethod": "GetRankListReq", "smMethod": "GetRankListResp"}, "20803": {"name": "S_RANK_GET_UNION_MEMBER_SCORE", "cmMethod": "GetUnionMemberScoreReq", "smMethod": "GetUnionMemberScoreRsp"}, "20711": {"name": "S_CLOUD_PLAYER_DATA_SYNC", "smMethod": "PlayerCloudDataMsg"}, "20712": {"name": "S_CLOUD_UNLOCK", "cmMethod": "UnlockCloudReq", "smMethod": "UnlockCloudResp"}, "20713": {"name": "S_CLOUD_LV_UP", "cmMethod": "CloudLvUpReq", "smMethod": "CloudLvUpResp"}, "20714": {"name": "S_CLOUD_STAGE_UP", "cmMethod": "CloudStageUpReq", "smMethod": "CloudStageUpResp"}, "20715": {"name": "S_CLOUD_EQUIP", "cmMethod": "CloudEquipReq", "smMethod": "CloudEquipResp"}, "20716": {"name": "S_CLOUD_EQUIP_SKIN", "cmMethod": "CloudEquipSkinReq", "smMethod": "CloudEquipSkinResp"}, "20717": {"name": "S_CLOUD_SKIN_LV_UP", "cmMethod": "CloudSkinLvUpReq", "smMethod": "CloudSkinLvUpResp"}, "212900": {"name": "S_CLOUD_REFINE_PLAYER_DATA_SYNC", "smMethod": "CloudRefinePlayerDataMsg"}, "212901": {"name": "S_CLOUD_REFINE_CHARGE", "cmMethod": "CloudRefineReq", "smMethod": "CloudRefineResp"}, "212902": {"name": "S_CLOUD_REFINE_STARLVUP", "cmMethod": "CloudRefineStarLvUpReq", "smMethod": "CloudRefineStarLvUpResp"}, "20731": {"name": "S_WILDBOSS_SYNC", "smMethod": "WildBossDataSync"}, "20732": {"name": "S_WILDBOSS_CHALLENGE", "cmMethod": "WildBossChallengeReq", "smMethod": "WildBossChallengeResp"}, "20733": {"name": "S_WILDBOSS_REPEAT", "cmMethod": "WildBossRepeatReq", "smMethod": "WildBossRepeatResp"}, "2011901": {"name": "S_CASTSWORD_ENTER", "cmMethod": "CastSwordEnterReq", "smMethod": "CastSwordEnterResp"}, "2011902": {"name": "S_CASTSWORD_MOVE", "cmMethod": "CastSwordMoveSwordReq", "smMethod": "CastSwordMoveSwordResp"}, "2011903": {"name": "S_CASTSWORD_SETTLEMENT", "cmMethod": "CastSwordSettlementSwordReq", "smMethod": "CastSwordSettlementSwordResp"}, "2011904": {"name": "S_CASTSWORD_USETOOL", "cmMethod": "CastSwordToolUseReq", "smMethod": "CastSwordToolUseResp"}, "2011905": {"name": "S_CASTSWORD_GETREWARD", "cmMethod": "CastSwordGetRewardReq", "smMethod": "CastSwordGetRewardResp"}, "2011906": {"name": "S_CASTSWORD_REFRESHSTRENGTH", "smMethod": "CastSwordRefreshStrengthMsg"}, "2011907": {"name": "S_CASTSWORD_GAMEOVER", "cmMethod": "CastSwordGameEndReq", "smMethod": "CastSwordGameEndResp"}, "2011908": {"name": "S_CASTSWORD_RANKSYNC", "smMethod": "CastSwordRankListSyncMsg"}, "209101": {"name": "S_RULE_TRIAL_CHALLENGE", "cmMethod": "RuleTrialChallengeReq", "smMethod": "RuleTrialChallengeResp"}, "209103": {"name": "S_RULE_ONE_KEY_TRIAL_REPEAT", "cmMethod": "RuleTrialRepeatAllReq", "smMethod": "RuleTrialRepeatAllResp"}, "209104": {"name": "U_RULE_TRIAL_SYNC_BOSS_CONFIG", "cmMethod": "", "smMethod": "RuleTrialBossConfigSyncMsg"}, "209105": {"name": "U_RULE_TRIAL_PLAYER_DATA_SYNC", "smMethod": "RuleTrialDataSync"}, "209106": {"name": "S_RULE_TRIAL_VIEW_MONSTER_ATTR", "cmMethod": "RuleTrialMonsterAttrReq", "smMethod": "RuleTrialMonsterAttrResp"}, "20740": {"name": "S_PET_DATA_SYNC", "smMethod": "PlayerPetDataSync"}, "20741": {"name": "S_PET_CATCH", "cmMethod": "CatchPetReq", "smMethod": "CatchPetResp"}, "20742": {"name": "S_PET_REFRESH_POOL", "cmMethod": "RefreshPetPoolReq", "smMethod": "RefreshPetPoolResp"}, "20743": {"name": "S_PET_ADD_BAG_COUNT", "cmMethod": "AddPetBagCountReq", "smMethod": "AddPetBagCountResp"}, "20744": {"name": "S_PET_RELEASE", "cmMethod": "ReleasePetReq", "smMethod": "ReleasePetResp"}, "20745": {"name": "S_PET_LEVEL_UP", "cmMethod": "PetLevelUpReq", "smMethod": "PetLevelUpResp"}, "20746": {"name": "S_PET_GOBBLE_UP", "cmMethod": "PetGobbleUpReq", "smMethod": "PetGobbleUpResp"}, "20747": {"name": "S_PET_EQUIP", "cmMethod": "EquipPetReq", "smMethod": "EquipPetResp"}, "20748": {"name": "S_PET_RESET", "cmMethod": "PetResetReq", "smMethod": "PetResetResp"}, "20749": {"name": "S_CHOOSE_WISH_PET", "cmMethod": "ChooseWishPetReq", "smMethod": "ChooseWishPetResp"}, "20750": {"name": "S_PET_ASSISTANT", "cmMethod": "PetAssistantReq", "smMethod": "PetAssistantResp"}, "20751": {"name": "S_PET_SOUL_SHAPE", "cmMethod": "PetSoulShapeReq", "smMethod": "PetSoulShapeResp"}, "20752": {"name": "S_PET_SELECT_SKILL", "cmMethod": "SelectSoulShapeSkillReq", "smMethod": "SelectSoulShapeSkillResp"}, "20753": {"name": "S_PET_REFLESH_SKILL_VIEW", "cmMethod": "PetSkillRefreshViewReq", "smMethod": "PetSkillRefreshViewResp"}, "20754": {"name": "S_PET_REFLESH_SKILL", "cmMethod": "PetSkillRefreshReq", "smMethod": "PetSkillRefreshResp"}, "20755": {"name": "S_PET_REFLESH_SKILL_RESULT", "cmMethod": "PetSkillRefreshResultReq", "smMethod": "PetSkillRefreshResultResp"}, "20756": {"name": "S_PET_LOCK_STATE_CHANGE", "cmMethod": "PetLockStateReq", "smMethod": "PetLockStateResp"}, "20757": {"name": "S_PET_SWITCH_LINKAGE_SKIN", "cmMethod": "PetSwitchLinkageSkinReq", "smMethod": "PetSwitchLinkageSkinResp"}, "20758": {"name": "S_PET_LOCK_NEED_PASSWORD", "cmMethod": "PetLockReq", "smMethod": "PetLockResp"}, "212201": {"name": "S_PET_ROOTUP", "cmMethod": "PetRootUpReq", "smMethod": "PetRootUpResp"}, "212202": {"name": "S_PET_AWAKE", "cmMethod": "PetAwakeReq", "smMethod": "PetAwakeResp"}, "212203": {"name": "S_PET_FATEUP", "cmMethod": "PetFateUpReq", "smMethod": "PetFateUpResp"}, "212204": {"name": "S_PET_SKIN_SWITCH", "cmMethod": "PetSwitchSkinReq", "smMethod": "PetSwitchSkinResp"}, "212205": {"name": "S_PET_SKIN_UP", "cmMethod": "PetUpSkinLevelReq", "smMethod": "PetUpSkinLevelResp"}, "212206": {"name": "S_PET_SKIN_COMBINE", "cmMethod": "PetUpSkinCombineReq", "smMethod": "PetUpSkinCombineResp"}, "218101": {"name": "S_GOD_PET_STAGE_UP", "cmMethod": "GodPetStageUpReq", "smMethod": "GodPetStageUpResp"}, "218102": {"name": "S_GOD_PET_STAGE_RESET", "cmMethod": "GodPetStageResetReq", "smMethod": "GodPetStageResetResp"}, "212207": {"name": "S_PET_RESONANCE_ACTIVE", "cmMethod": "ResonanceActiveReq", "smMethod": "ResonanceActiveResp"}, "20110": {"name": "S_WORLD_MESSAGE_CHAT", "cmMethod": "WorldChatReqMsg", "smMethod": "WorldChatRespMsg"}, "20111": {"name": "S_WORLD_MESSAGE_SYNC", "smMethod": "WorldMessageListMsg"}, "20112": {"name": "S_WORLD_MESSAGE_LIST", "smMethod": "WorldMessageListMsg"}, "20113": {"name": "S_UNION_MESSAGE_SYNC", "cmMethod": "", "smMethod": "WorldMessageListMsg"}, "20114": {"name": "S_UNION_MESSAGE_LIST", "cmMethod": "", "smMethod": "WorldMessageListMsg"}, "20115": {"name": "S_ACTIVITY_MESSAGE_SYNC", "cmMethod": "", "smMethod": "WorldMessageListMsg"}, "20116": {"name": "S_ACTIVITY_MESSAGE_LIST", "cmMethod": "ActivityChatReqMsg", "smMethod": "WorldMessageListMsg"}, "20130": {"name": "S_REPORT_PLAYER", "cmMethod": "WorldChatBlockReqMsg", "smMethod": "WorldChatBlockRespMsg"}, "20131": {"name": "S_SYSTEM_MESSAGE_SYNC", "smMethod": "WorldMessageListMsg"}, "20132": {"name": "S_SYSTEM_MESSAGE_LIST", "cmMethod": "SystemChatReqMsg", "smMethod": "WorldMessageListMsg"}, "203205": {"name": "S_WORLD_MESSAGE_REMOVE_BLACK", "cmMethod": "RemoveBlackReqMsg", "smMethod": "RemoveBlackRespMsg"}, "203206": {"name": "S_WORLD_MESSAGE_GET_BLACK_LIST", "cmMethod": "GetBlackPlayerListReqMsg", "smMethod": "GetBlackPlayerListRespMsg"}, "203207": {"name": "S_WORLD_MESSAGE_SYNC_BLACK_LIST", "smMethod": "SyncBlackPlayerIdListMsg"}, "203208": {"name": "S_WORLD_MESSAGE_CHECK_EFFECTIVE_REQ", "cmMethod": "WorldMessageCheckEffectiveReq", "smMethod": "WorldMessageCheckEffectiveRsp"}, "203210": {"name": "S_WORLD_MESSAGE_SYNC_CHAT_SHIELD_TIME", "smMethod": "SyncPlayerChatShieldMsg"}, "20140": {"name": "S_RED_PACKET_STATE_MSG_SYNC", "smMethod": "RedPacketStateMsgSync"}, "20141": {"name": "S_RED_PACKET_OPEN_REQ", "cmMethod": "RedPacketOpenReq", "smMethod": "RedPacketOpenResp"}, "20142": {"name": "S_RED_PACKET_DRAW_RESULT", "cmMethod": "RedPacketDrawReq", "smMethod": "RedPacketDrawResp"}, "209201": {"name": "S_WELFARE_GIFT_KR", "cmMethod": "ADTimeTriggerReq", "smMethod": ""}, "209202": {"name": "S_KILLTIME_GIFT_KR", "cmMethod": "ADTimeUseReq", "smMethod": ""}, "20133": {"name": "S_REPORT_MESSAGE", "cmMethod": "ReportMessageReqMsg", "smMethod": "ReportMessageRespMsg"}, "20761": {"name": "S_TOWER_SYNC_DATA", "smMethod": "TowerDataMsg"}, "20762": {"name": "S_TOWER_CHALLENGE", "smMethod": "TowerChallengeResp"}, "20763": {"name": "S_TOWER_QUICK_CHANLLENGE", "smMethod": "QuickChallengeResp"}, "20764": {"name": "S_TOWER_SELECT_BUFF", "cmMethod": "SelectBuffReq", "smMethod": "SelectBuffResp"}, "20765": {"name": "S_TOWER_VIEW_MONSTER_ATTR", "cmMethod": "", "smMethod": "ViewMonsterAttrResp"}, "20766": {"name": "S_TOWER_VIEW_GET_SELECT", "cmMethod": "GetSelectOptionReq", "smMethod": "GetSelectOptionResp"}, "20767": {"name": "S_TOWER_VIEW_SAVE_SELECT", "cmMethod": "SaveSelectOptionReq", "smMethod": "SaveSelectOptionResp"}, "20621": {"name": "S_TALENT_SYNC_DATA", "smMethod": "TalentPlayerDataMsg"}, "20622": {"name": "S_TALENT_RANDOM_TALENT", "cmMethod": "RandomTalentReq", "smMethod": "RandomTalentResp"}, "20623": {"name": "S_TALENT_DEAL_TALENT", "cmMethod": "DealTalentReq", "smMethod": "DealTalentResp"}, "20624": {"name": "S_TALENT_READ_BOOK", "cmMethod": "ReadBookReq", "smMethod": "ReadBookResp"}, "20625": {"name": "S_TALENT_GET_UNDEAL_TALENT_MSG", "cmMethod": "GetUnDealTalentMsgReq", "smMethod": "GetUnDealTalentMsgResp"}, "20821": {"name": "S_SPIRIT_SYNC_DATA", "smMethod": "SpiritPlayerDataMsg"}, "20822": {"name": "S_SPIRIT_DRAW", "cmMethod": "SpiritDrawReq", "smMethod": "SpiritDrawResp"}, "20823": {"name": "S_SPIRIT_UNLOCK", "cmMethod": "SpiritUnlockReq", "smMethod": "SpiritUnlockResp"}, "20824": {"name": "S_SPIRIT_LV_UP", "cmMethod": "SpiritLvUpReq", "smMethod": "SpiritLvUpResp"}, "20825": {"name": "S_SPIRIT_SWITCH_BATTLE_TEAM", "cmMethod": "SwitchBattleTeamReq", "smMethod": "SwitchBattleTeamResp"}, "20826": {"name": "S_SPIRIT_BATTLE", "cmMethod": "SpiritBattleReq", "smMethod": "SpiritBattleResp"}, "20827": {"name": "S_SPIRIT_COMBINE_LV_UP", "cmMethod": "SpiritCombineLvUpReq", "smMethod": "SpiritCombineLvUpResp"}, "20828": {"name": "S_SPIRIT_SWITCH_LINKAGE_SKIN", "cmMethod": "SpiritSwitchLinkageSkinReq", "smMethod": "SpiritSwitchLinkageSkinResp"}, "20829": {"name": "S_SPIRIT_LV_UNLOCK_SHOW", "cmMethod": "SpiritLevelUnlockShowReq", "smMethod": "SpiritLevelUnlockShowResp"}, "20104": {"name": "S_PRIVILEGE_CARD_SYNC_DATA", "cmMethod": "", "smMethod": "PrivilegeCardDataMsg"}, "20105": {"name": "S_PRIVILEGE_CARD_RECEIVE_REWARD", "cmMethod": "PrivilegeCardReceiveRewardReq", "smMethod": "PrivilegeCardReceiveRewardRsp"}, "21101": {"name": "S_BALLGVG_APPLICATION", "cmMethod": "BallGVGApplicationReqMsg", "smMethod": "BallGVGApplicationRespMsg"}, "21102": {"name": "S_BALLGVG_RANK_NUM", "cmMethod": "BallGVGAbilityRankNumReqMsg", "smMethod": "BallGVGAbilityRankNumRespMsg"}, "21103": {"name": "S_BALLGVG_ENTER_ACT", "cmMethod": "BallGVGEnterActivityReqMsg", "smMethod": "BallGVGEnterActivityRespMsg"}, "21104": {"name": "S_BALLGVG_ENTER_ROOM", "cmMethod": "BallGVGEnterPlaceReqMsg", "smMethod": "BallGVGEnterPlaceRespMsg"}, "21105": {"name": "S_BALLGVG_LEAVE_ROOM", "cmMethod": "BallGVGLeavePlaceReqMsg", "smMethod": "BallGVGLeavePlaceRespMsg"}, "21106": {"name": "S_BALLGVG_MOVE", "cmMethod": "BallGVGMoveReqMsg", "smMethod": "BallGVGMoveRespMsg"}, "21107": {"name": "S_BALLGVG_ATTACK", "cmMethod": "BallGVGAttackReqMsg", "smMethod": "BallGVGAttackRespMsg"}, "21108": {"name": "S_BALLGVG_APPOINT", "cmMethod": "BallGVGAppointMajorUserReqMsg", "smMethod": "BallGVGAppointMajorUserRespMsg"}, "21109": {"name": "S_BALLGVG_MARK", "cmMethod": "BallGVGMarkPlaceReqMsg", "smMethod": "BallGVGMarkPlaceRespMsg"}, "21110": {"name": "S_BALLGVG_LEAVE_ACT"}, "21111": {"name": "S_BALLGVG_GET_PLACE", "cmMethod": "BallGVGGetPlaceInfoReqMsg", "smMethod": "BallGVGGetPlaceInfoRespMsg"}, "21113": {"name": "S_BALLGVG_REQ_USER_LIST", "cmMethod": "BallGVGGetUserRankReqMsg", "smMethod": "BallGVGGetUserRankRespMsg"}, "21116": {"name": "S_BALLGVG_SYNC_ATTACKED", "cmMethod": "", "smMethod": "BallGVGAttackedSyncMsg"}, "21117": {"name": "S_BALLGVG_SYNC_SCORE", "cmMethod": "", "smMethod": "BallGVGCampScoreSyncMsg"}, "21118": {"name": "S_BALLGVG_SYNC_PLACE", "cmMethod": "", "smMethod": "BallGVGPlaceSyncMsg"}, "21119": {"name": "S_BALLGVG_SYNC_NOTICE", "cmMethod": "", "smMethod": "BallGVGNoticeSyncMsg"}, "21120": {"name": "S_BALLGVG_SYNC_CAMP", "cmMethod": "", "smMethod": "BallGVGCampSyncMsg"}, "21121": {"name": "S_BALLGVG_SYNC_BEATC", "cmMethod": "", "smMethod": "BallGVGAttackedUserSyncMsg"}, "21122": {"name": "S_BALLGVG_SYNC_SEIZE", "cmMethod": "", "smMethod": "BallGVGPlaceSeizeSyncMsg"}, "21123": {"name": "S_BALLGVG_SYNC_ENTER_ROOM", "cmMethod": "", "smMethod": "BallGVGEnterPlaceSync"}, "21124": {"name": "S_BALLGVG_SYNC_LEAVE_ROOM", "cmMethod": "", "smMethod": "BallGVGLeavePlaceSync"}, "21126": {"name": "S_BALLGVG_RANK", "cmMethod": "BallGVGAbilityRankReqMsg", "smMethod": "BallGVGAbilityRankRespMsg"}, "21127": {"name": "S_BALLGVG_RESULT_SYNC", "cmMethod": "", "smMethod": "BallGVGEndDataSyncMsg"}, "21128": {"name": "S_BALLGVG_UNION_NAME_LIST", "cmMethod": "BallGVGUnionNameListReqMsg", "smMethod": "BallGVGUnionNameListRespMsg"}, "21129": {"name": "S_BALLGVG_GET_UNION_USER_LIST", "cmMethod": "BallGVGGetUnionUserListReqMsg", "smMethod": "BallGVGGetUnionUserListRespMsg"}, "21131": {"name": "S_BALLGVG_REDDOT", "cmMethod": "BallGVGRedDotReqMsg", "smMethod": "BallGVGRedDotRespMsg"}, "21051": {"name": "S_HOMELAND_SYNC_MSG", "cmMethod": "", "smMethod": "SyncHomelandMsg"}, "21062": {"name": "S_HOMELAND_SYNC_HAS_REWARD", "cmMethod": "", "smMethod": "SyncHomelandHasRewardMsg"}, "21052": {"name": "S_HOMELAND_ENTER", "cmMethod": "HomelandEnterReq", "smMethod": "HomelandEnterResp"}, "21053": {"name": "S_HOMELAND_MANAGE", "cmMethod": "HomelandManageReq", "smMethod": "HomelandManageResp"}, "21054": {"name": "S_HOMELAND_LEVEL_UP", "cmMethod": "HomelandLevelUpReq", "smMethod": "HomelandLevelUpResp"}, "21055": {"name": "S_HOMELAND_REFRESH_RESOURCE", "cmMethod": "HomelandRefreshReq", "smMethod": "HomelandRefreshResp"}, "21056": {"name": "S_HOMELAND_BUY_WORKER", "cmMethod": "HomelandBuyWorkerReq", "smMethod": "HomelandBuyWorkerResp"}, "21057": {"name": "S_HOMELAND_RECORD", "cmMethod": "HomelandGetRecordReq", "smMethod": "HomelandGetRecordResp"}, "21058": {"name": "S_HOMELAND_EXPLORE", "cmMethod": "HomelandExploreReq", "smMethod": "HomelandExploreResp"}, "21059": {"name": "S_HOMELAND_EXPLORE_REFRESH", "cmMethod": "HomelandRefreshExploreReq", "smMethod": "HomelandRefreshExploreResp"}, "21060": {"name": "S_HOMELAND_DISPATCH_WORKER", "cmMethod": "HomelandDispatchWorkerReq", "smMethod": "HomelandDispatchWorkerResp"}, "21061": {"name": "S_HOMELAND_DISPATCH_WORKER_PREVIEW", "cmMethod": "HomelandDispatchPreviewReq", "smMethod": "HomelandDispatchPreviewResp"}, "21063": {"name": "S_HOMELAND_LEAVE", "cmMethod": "HomelandLeaveReq", "smMethod": "HomelandLeaveResp"}, "21064": {"name": "S_HOMELAND_SYNC_HOMELAND_DATA", "cmMethod": "", "smMethod": "SyncPlayerHomelandChangeMsg"}, "21065": {"name": "S_HOMELAND_GET_REWARD", "cmMethod": "HomelandGetRewardReq", "smMethod": "HomelandGetRewardResp"}, "21066": {"name": "S_HOMELAND_SYNC_CORNUCOPIA", "cmMethod": "", "smMethod": "SyncPlayerCornucopiaChangeMsg"}, "21067": {"name": "S_HOMELAND_CORNUCOPIA_GET_REWARD", "cmMethod": "CornucopiaGetRewardReq", "smMethod": "CornucopiaGetRewardRsp"}, "21068": {"name": "S_HOMELAND_MANAGER_BATTLE", "cmMethod": "HomelandAutoDispatchWorkerReq", "smMethod": "HomelandAutoDispatchWorkerRsp"}, "21069": {"name": "S_HOMELAND_MANAGER_FREE_USE", "cmMethod": "HomelandUseFreeMouseManagerReq", "smMethod": "HomelandUseFreeMouseManagerRsp"}, "21070": {"name": "S_HOMELAND_MANAGER_SAVE_SETTINGS", "cmMethod": "HomelandSaveSettingsReq", "smMethod": "HomelandSaveSettingsRsp"}, "21071": {"name": "S_HOMELAND_DISPATCH_ALL_WORKER", "cmMethod": "HomelandDispatchAllWorkerReq", "smMethod": "HomelandDispatchAllWorkerResp"}, "21072": {"name": "S_HOMELAND_SYNC_NOTICE_LEAVE", "cmMethod": "", "smMethod": "SyncLeaveHomeLand"}, "22101": {"name": "S_UNION_CREATE", "cmMethod": "ReqUnionCreate", "smMethod": "RspUnionCreate"}, "22102": {"name": "S_UNION_ENTER", "cmMethod": "ReqUnionEnter", "smMethod": "RspUnionEnter"}, "22103": {"name": "S_UNION_LIST", "cmMethod": "ReqUnionList", "smMethod": "RspUnionList"}, "22104": {"name": "S_UNION_JOIN", "cmMethod": "ReqUnionJoin", "smMethod": "RspUnionJoin"}, "22105": {"name": "S_UNION_RANDOMJOIN", "cmMethod": "ReqUnionRandomJoin", "smMethod": "RspUnionRandomJoin"}, "22106": {"name": "S_UNION_DETAIL", "cmMethod": "ReqUnionDetail", "smMethod": "RspUnionDetail"}, "22107": {"name": "S_UNION_POSITION", "cmMethod": "ReqUnionPosition", "smMethod": "RspUnionPosition"}, "22108": {"name": "S_UNION_APPLYPLAYERLIST", "cmMethod": "ReqUnionApplyPlayerList", "smMethod": "RspUnionApplyPlayerList"}, "22109": {"name": "S_UNION_RANDOMSTATE", "cmMethod": "ReqUnionRandomState", "smMethod": "RspUnionRandomState"}, "22110": {"name": "S_UNION_LIMITREALMSID", "cmMethod": "ReqUnionLimitRealmsId", "smMethod": "RspUnionLimitRealmsId"}, "22111": {"name": "S_UNION_CLEARAPPLY", "cmMethod": "ReqUnionClearApply", "smMethod": "RspUnionClearApply"}, "22112": {"name": "S_UNION_PASSAPPLY", "cmMethod": "ReqUnionPassApply", "smMethod": "RspUnionPassApply"}, "22113": {"name": "S_UNION_REMOVE", "cmMethod": "ReqUnionRemove", "smMethod": "RspUnionRemove"}, "22114": {"name": "S_UNION_EXIT", "cmMethod": "ReqUnionExit", "smMethod": "RspUnionExit"}, "22115": {"name": "S_UNION_TRENDS", "cmMethod": "ReqUnionTrends", "smMethod": "RspUnionTrends"}, "22116": {"name": "S_UNION_MODIFY", "cmMethod": "ReqUnionModify", "smMethod": "RspUnionModify"}, "22117": {"name": "S_UNION_DAILYTASK", "cmMethod": "ReqUnionDailyTask", "smMethod": "RspUnionDailyTask"}, "22118": {"name": "S_UNION_GETDAILYTASK", "cmMethod": "ReqUnionGetDailyTask", "smMethod": "RspUnionGetDailyTask"}, "22119": {"name": "S_UNION_PUSHEVICTION", "cmMethod": "", "smMethod": "PushUnionEvictionMsg"}, "22120": {"name": "S_UNION_DONATEDAILYTASK", "cmMethod": "ReqUnionDailyDonate", "smMethod": "RspUnionDailyDonate"}, "22121": {"name": "S_UNION_DAILYTASKPROGRESS", "cmMethod": "ReqUnionDailyProgress", "smMethod": "RspUnionDailyProgress"}, "22122": {"name": "S_UNION_EVICTION", "cmMethod": "ReqUnionEviction", "smMethod": "RspUnionEviction"}, "22123": {"name": "S_UNION_JOINLIST", "cmMethod": "ReqUnionJoinList", "smMethod": "RspUnionJoinList"}, "22124": {"name": "S_UNION_PUSHMYDATA", "cmMethod": "", "smMethod": "MyUnionData"}, "22125": {"name": "S_GET_UNION_HELP_DATA_LIST", "cmMethod": "GetUnionHelpDataListReq", "smMethod": "GetUnionHelpDataListResp"}, "22126": {"name": "S_REQUEST_UNION_HELP", "cmMethod": "RequestUnionHelpReq", "smMethod": "RequestUnionHelpResp"}, "22127": {"name": "S_UNION_HELP", "cmMethod": "UnionHelpReq", "smMethod": "UnionHelpResp"}, "22128": {"name": "S_GET_UNION_HELP_STATE", "cmMethod": "GetUnionHelpStateReq", "smMethod": "GetUnionHelpStateResp"}, "22129": {"name": "S_UNION_WATCH_REDDOT", "cmMethod": "ReqUnionWatchRedDot", "smMethod": "RspUnionWatchRedDot"}, "21501": {"name": "S_UNION_SAME_NAME_ACTIVITY_NAME_LIST", "cmMethod": "ReqUnionSameNameListMsg", "smMethod": "RspUnionSameNameListMsg"}, "21511": {"name": "S_GET_UNION_RECHARGE_ACTIVITY_RECHARGE_DATA", "cmMethod": "UnionRechargeUserReqMsg", "smMethod": "UnionRechargeUserRspMsg"}, "21512": {"name": "S_UNION_RECHARGE_ACTIVITY_RECHARGE_DATA_SYNC", "cmMethod": "", "smMethod": "UnionRechargeDataSync"}, "22168": {"name": "S_UNION_TIME_SYNC", "cmMethod": "", "smMethod": "UnionTimeMsg"}, "201654": {"name": "S_UNION_PUBLISH_RECRUIT", "cmMethod": "UnionPublishRecruitReqMsg", "smMethod": "UnionPublishRecruitRespMsg"}, "201655": {"name": "S_UNION_PUBLISH_RECRUIT_SYNC", "smMethod": "NotifyUnionRecruitCountMsg"}, "201630": {"name": "S_UNION_PLAYER_STRENGTH_RANK", "cmMethod": "UnionPlayerGradeRankReqMsg", "smMethod": "UnionPlayerGradeRankRespMsg"}, "201651": {"name": "S_UNION_GET_CROSS_SERVER", "cmMethod": "GetCrossUnionGroupServersReq", "smMethod": "GetCrossUnionGroupServersResp"}, "201650": {"name": "S_UNION_SET_RECRUIT_INFO", "cmMethod": "UnionMemberGradeSettingReq", "smMethod": "UnionMemberGradeSettingResp"}, "201604": {"name": "S_UNION_DETAIL_IN_GROUP", "cmMethod": "ReqUnionDetail", "smMethod": "RspUnionDetail"}, "201656": {"name": "S_UNION_PLAYER_RATE_SYNC", "smMethod": "NotifyPlayerGradeMsg"}, "22170": {"name": "S_UNION_CHANGE_NAME_TITLE", "cmMethod": "UnionChangeUnionNameTitleReq", "smMethod": "UnionChangeUnionNameTitleResp"}, "22169": {"name": "S_UNION_HONOR_DATA_SYNC", "smMethod": "UnionHonorSyncMsg"}, "22171": {"name": "S_UNION_FLY", "cmMethod": "UnionHonorHallFlyReq", "smMethod": "UnionHonorHallFlyResp"}, "22172": {"name": "S_UNION_BUILD_UPGRADE", "cmMethod": "UpgradeUnionBuildReq", "smMethod": "UpgradeUnionBuildResp"}, "22173": {"name": "S_UNION_HONOR_GROUP_SYNC", "smMethod": "UnionHonorPeakRankGroupSync"}, "20410": {"name": "S_RANK_BATTLE_GET_BATTLE_LIST", "cmMethod": "", "smMethod": "GetBattleListResp"}, "20411": {"name": "S_RANK_BATTLE_REFRESH_BATTLE_LIST", "cmMethod": "", "smMethod": "RefreshBattleListResp"}, "20412": {"name": "S_RANK_BATTLE_CHALLENGE", "cmMethod": "RankBattleChallengeReq", "smMethod": "RankBattleChallengeResp"}, "20413": {"name": "S_RANK_BATTLE_GET_LOG", "cmMethod": "", "smMethod": "GetRankBattleLogResp"}, "20414": {"name": "S_RANK_BATTLE_REVENGE", "cmMethod": "RankBattleRevengeReq", "smMethod": "RankBattleRevengeResp"}, "20415": {"name": "S_RANK_BATTLE_LOGIN_SYNC", "smMethod": "RankBattleSync"}, "20418": {"name": "S_RANK_BATTLE_SERVERLIST", "cmMethod": "RankBattleServerListReq", "smMethod": "RankBattleServerListResp"}, "20651": {"name": "S_DESTINY_DATA_SYNC", "cmMethod": "", "smMethod": "DestinyData"}, "20652": {"name": "S_DESTINY_GIFT", "cmMethod": "ReqDestinyGift", "smMethod": "RspDestinyGift"}, "20653": {"name": "S_DESTINY_TRAVEL", "cmMethod": "ReqDestinyTravel", "smMethod": "RspDestinyTravel"}, "20654": {"name": "S_DESTINY_CHALLENGE", "cmMethod": "ReqDestinyChallenge", "smMethod": "RspDestinyChallenge"}, "20655": {"name": "S_DESTINY_UNLOCK", "cmMethod": "ReqUnlockDestinyByItem", "smMethod": "RspUnlockDestinyByItem"}, "20657": {"name": "S_DESTINY_SKIN_UNLOCK", "cmMethod": "ReqUnlockDestinySkinByItem", "smMethod": "RspUnlockDestinySkinByItem"}, "20658": {"name": "S_DESTINY_SKIN_ENHANCE", "cmMethod": "ReqEnhanceDestinySkin", "smMethod": "RspEnhanceDestinySkin"}, "20659": {"name": "S_DESTINY_SKIN_WEAR", "cmMethod": "ReqWearDestinySkin", "smMethod": "RspWearDestinySkin"}, "20660": {"name": "S_DESTINY_SWITCH_LINKAGE_SKIN", "cmMethod": "DestinySwitchLinkageSkinReq", "smMethod": "DestinySwitchLinkageSkinResp"}, "22401": {"name": "S_BIND_INVITE_CODE", "cmMethod": "ReqBindInviteCode", "smMethod": "RspBindInviteCode"}, "22402": {"name": "S_GET_BIND_INVITE_CODE", "cmMethod": "ReqGetBindInviteCode", "smMethod": "RspGetBindInviteCode"}, "21013": {"name": "S_ACTIVITY_LUCKY_DRAW", "cmMethod": "LuckyDrawReq", "smMethod": "LuckyDrawResp"}, "21014": {"name": "S_ACTIVITY_OPTIONAL_GIFT_SELECT", "cmMethod": "SelectItemsReq", "smMethod": "SelectItemsResp"}, "20250": {"name": "S_GET_LOGIN_MESSAGE_SUBSCRIBE", "cmMethod": "", "smMethod": "MessageSubscribeInfo"}, "20251": {"name": "S_SET_MESSAGE_SUBSCRIBE", "cmMethod": "SetMessageSubscribeDataReqMsg", "smMethod": "SetMessageSubscribeDataRspMsg"}, "20252": {"name": "S_AUTHORIZE_WECHAT_HEAD_URL", "cmMethod": "AuthorizePlayerHeadReq", "smMethod": "AuthorizePlayerHeadResp"}, "20117": {"name": "S_HORSE_RACE_LAMP_MSG_ADD", "cmMethod": "", "smMethod": "HorseRaceLampMsgAdd"}, "20210": {"name": "S_AD_REWARD_DATA_SYNC", "cmMethod": "", "smMethod": "PlayerAdRewardDataMsg"}, "20211": {"name": "S_AD_REWARD_GET_REWARD", "cmMethod": "GetAdRewardReq", "smMethod": "GetAdRewardResp"}, "22165": {"name": "S_CUT_PRICE_SYNC", "smMethod": "CutPriceDataMsg"}, "22166": {"name": "S_CUT_PRICE_BARGAIN", "cmMethod": "CutPriceBargainReqMsg", "smMethod": "CutPriceBargainRespMsg"}, "22167": {"name": "S_CUT_PRICE_BUY", "cmMethod": "CutPriceBuyReqMsg", "smMethod": "CutPriceBuyRespMsg"}, "23700": {"name": "S_HERORANK_ENTER", "cmMethod": "", "smMethod": "RspHeroRankEnter"}, "23701": {"name": "S_HERORANK_SYNC_PLAYERDATA", "cmMethod": "", "smMethod": "SynHeroRankPlayerInfo"}, "23702": {"name": "S_HERORANK_GET_FIGHT_LIST", "cmMethod": "ReqHeroRankFightPlayerList", "smMethod": "RspHeroRankFightPlayerList"}, "23703": {"name": "S_HERORANK_FIGHT", "cmMethod": "ReqHeroRankFight", "smMethod": "RspHeroRankFight"}, "23704": {"name": "S_HERORANK_CLEAR", "cmMethod": "ReqHeroRankClear", "smMethod": "RspHeroRankClear"}, "23705": {"name": "S_HERORANK_GET_RECORD", "cmMethod": "", "smMethod": "RspHeroRankRecord"}, "23706": {"name": "S_HERORANK_GET_ACHIEVE_REWARD", "cmMethod": "ReqHeroRankGetAchieve", "smMethod": "RspHeroRankGetAchieve"}, "23707": {"name": "S_HERORANK_BUY_ENERGY", "cmMethod": "ReqHeroRankBuyEnergy", "smMethod": "RspHeroRankBuyEnergy"}, "24201": {"name": "S_SPIRIT_TRIAL_REFRESH_EVIL", "cmMethod": "RefreshEvilThingReq", "smMethod": "RefreshEvilThingResp"}, "24202": {"name": "S_SPIRIT_TRIAL_BATTLE_EVIL", "cmMethod": "BattleEviThingReq", "smMethod": "BattleEvilThingResp"}, "24400": {"name": "S_MAGIC_PLAYER_DATA_SYNC", "smMethod": "PlayerMagicDataMsg"}, "24401": {"name": "S_MAGIC_EQUIP", "cmMethod": "MagicEquipReq", "smMethod": "MagicEquipResp"}, "24402": {"name": "S_MAGIC_RESET", "cmMethod": "MagicResetReq", "smMethod": "MagicResetResp"}, "24403": {"name": "S_MAGIC_STAGE_UP", "cmMethod": "MagicStageUpReq", "smMethod": "MagicStageUpResp"}, "24404": {"name": "S_MAGIC_LEVEL_UP", "cmMethod": "MagicLvUpReq", "smMethod": "MagicLvUpResp"}, "24405": {"name": "S_MAGIC_EQUIP_MARK", "cmMethod": "MagicEquipMarkReq", "smMethod": "MagicEquipMarkResp"}, "24406": {"name": "S_MAGIC_UNSNATCH_MARK", "cmMethod": "MagicUnsnatchMarkReq", "smMethod": "MagicUnsnatchMarkResp"}, "24407": {"name": "S_MAGIC_FUSION_MARK", "cmMethod": "MagicFusionMarkReq", "smMethod": "MagicFusionMarkResp"}, "24408": {"name": "S_MAGIC_DERIVATION", "cmMethod": "MagicDerivationReq", "smMethod": "MagicDerivationResp"}, "24409": {"name": "S_MAGIC_COMBINE_LEVEL_UP", "cmMethod": "MagicCombineLvUpReq", "smMethod": "MagicCombineLvUpResp"}, "24410": {"name": "S_MAGIC_SWITCH_PRESETS_REQ", "cmMethod": "MagicSwitchPresetsReq", "smMethod": "MagicSwitchPresetsResp"}, "24411": {"name": "S_MAGIC_STAGE_UP_ALL", "cmMethod": "MagicStageUpAllReq", "smMethod": "MagicStageUpResp"}, "24412": {"name": "S_MAGIC_GET_PRESET_MAGIC_INFO_REQ", "cmMethod": "GetPresetMagicInfoReq", "smMethod": "GetPresetMagicInfoResp"}, "24413": {"name": "S_MAGIC_UN_EQUIP_STONE_REQ", "cmMethod": "UnEquipStoneReq", "smMethod": "UnEquipStoneResp"}, "24101": {"name": "S_PET_DREAMLAND_DRAW", "cmMethod": "ReqPetDreamLandDraw", "smMethod": "RspPetDreamLandDraw"}, "24102": {"name": "S_PET_DREAMLAND_PLACE_UNLOCK", "cmMethod": "ReqPetDreamLandAdventurePlaceUnlock", "smMethod": "RspPetDreamLandAdventurePlaceUnlock"}, "24103": {"name": "S_PET_DREAMLAND_SLOT_UNLOCK", "cmMethod": "ReqPetDreamLandAdventureSlotUnlock", "smMethod": "RspPetDreamLandAdventurePlaceSlot"}, "24104": {"name": "S_PET_DREAMLAND_DISPATCH", "cmMethod": "ReqPetDreamLandAdventureDispatch", "smMethod": "RspPetDreamLandAdventureDispatch"}, "24105": {"name": "S_PET_DREAMLAND_GET_PROFIT", "cmMethod": "ReqPetDreamLandAdventureGetAward", "smMethod": "RspPetDreamLandAdventureGetAward"}, "25001": {"name": "S_ACTIVITY_WILD_ZONE_ENTER_PANEL", "cmMethod": "ManHuangEnterPanelReqMsg", "smMethod": "ManHuangEnterPanelRespMsg"}, "25002": {"name": "S_ACTIVITY_WILD_ZONE_TEAM_CREATE", "cmMethod": "ManHuangCreateTeamReqMsg", "smMethod": "ManHuangCreateTeamRespMsg"}, "25003": {"name": "S_ACTIVITY_WILD_ZONE_TEAM_LIST", "cmMethod": "ManHuangGetTeamListReqMsg", "smMethod": "ManHuangGetTeamListRespMsg"}, "25004": {"name": "S_ACTIVITY_WILD_ZONE_TEAM_INFO", "cmMethod": "ManHuangGetTeamInfoReqMsg", "smMethod": "ManHuangGetTeamInfoRespMsg"}, "25005": {"name": "S_ACTIVITY_WILD_ZONE_TEAM_APPLY_DO", "cmMethod": "ManHuangJoinTeamReqMsg", "smMethod": "ManHuangJoinTeamRespMsg"}, "25006": {"name": "S_ACTIVITY_WILD_ZONE_TEAM_APPLY_CANCEL", "cmMethod": "ManHuangCancelTeamApplyReqMsg", "smMethod": "ManHuangCancelTeamApplyRespMsg"}, "25007": {"name": "S_ACTIVITY_WILD_ZONE_TEAM_OUT", "cmMethod": "ManHuangQuitTeamReqMsg", "smMethod": "ManHuangQuitTeamRespMsg"}, "25008": {"name": "S_ACTIVITY_WILD_ZONE_LEAVE_PANEL", "cmMethod": "ManHuangLeavePanelReqMsg", "smMethod": "ManHuangLeavePanelRespMsg"}, "25009": {"name": "S_ACTIVITY_WILD_ZONE_TEAM_APPLY_AGREE", "cmMethod": "ManHuangApplyJoinTeamAgreeReqMsg", "smMethod": "ManHuangApplyJoinTeamAgreeRespMsg"}, "25010": {"name": "S_ACTIVITY_WILD_ZONE_TEAM_APPLY_REFUSED", "cmMethod": "ManHuangApplyJoinTeamRefusedReqMsg", "smMethod": "ManHuangApplyJoinTeamRefusedRespMsg"}, "25011": {"name": "S_ACTIVITY_WILD_ZONE_TEAM_KICK_OUT", "cmMethod": "ManHuangKickOutTeamReqMsg", "smMethod": "ManHuangKickOutTeamRespMsg"}, "25012": {"name": "S_ACTIVITY_WILD_ENTER_REGION", "cmMethod": "ManHuangEnterRegionReqMsg", "smMethod": "ManHuangEnterRegionRespMsg"}, "25013": {"name": "S_ACTIVITY_WILD_PLAYER_EXPLORE", "cmMethod": "ManHuangExploreReqMsg", "smMethod": "ManHuangExploreRespMsg"}, "25014": {"name": "S_ACTIVITY_WILD_PLAYER_EXECUTE_EXPLORE", "cmMethod": "ManHuangEventHandleReqMsg", "smMethod": "ManHuangEventHandleRespMsg"}, "25015": {"name": "S_ACTIVITY_WILD_PLAYER_ACTION_EXPLORE", "cmMethod": "ManHuangEventActionReqMsg", "smMethod": "ManHuangEventActionRespMsg"}, "25016": {"name": "S_ACTIVITY_WILD_LOG_MEMBER_HELP", "cmMethod": "ManHuangLogHelpReqMsg", "smMethod": "ManHuangLogHelpRespMsg"}, "25017": {"name": "S_ACTIVITY_WILD_LOG_BATTLEGROUND", "cmMethod": "ManHuangLogBattlegroundReqMsg", "smMethod": "ManHuangLogBattlegroundRespMsg"}, "25018": {"name": "S_ACTIVITY_WILD_LOG_PERSON", "cmMethod": "ManHuangLogPersonReqMsg", "smMethod": "ManHuangLogPersonRespMsg"}, "25019": {"name": "S_ACTIVITY_WILD_RANK_PERSON", "cmMethod": "ManHuangRankPersonReqMsg", "smMethod": "ManHuangRankPersonRespMsg"}, "25020": {"name": "S_ACTIVITY_WILD_RANK_TEAM", "cmMethod": "ManHuangRankTeamReqMsg", "smMethod": "ManHuangRankTeamRespMsg"}, "25022": {"name": "S_ACTIVITY_WILD_SYNC_ENEMY_INFO", "smMethod": "ManHuangEnemyNotify"}, "25023": {"name": "S_ACTIVITY_WILD_SYNC_TEAM_LEADER", "smMethod": "ManHuangTeamLeaderNotify"}, "25024": {"name": "S_ACTIVITY_WILD_SYNC_TEAM_MEMBER", "smMethod": "ManHuangTeamMemberNotify"}, "25029": {"name": "S_ACTIVITY_WILD_SYNC_EVENT_INFO", "smMethod": "ManHuangEventInfoNotify"}, "25025": {"name": "S_ACTIVITY_WILD_PLAYER_RECOVER_ENERGY", "cmMethod": "ManHuangRecoverEnergyReqMsg", "smMethod": "ManHuangRecoverEnergyRespMsg"}, "25026": {"name": "S_ACTIVITY_WILD_PLAYER_DO_HELP", "cmMethod": "ManHuangHelpActionAttReqMsg", "smMethod": "ManHuangHelpActionAttRespMsg"}, "25027": {"name": "S_ACTIVITY_WILD_OPEN_BOX", "cmMethod": "ManHuangOpenBoxReqMsg", "smMethod": "ManHuangOpenBoxRespMsg"}, "25028": {"name": "S_ACTIVITY_WILD_PLAYER_DO_HELP_REWARD", "cmMethod": "ManHuangHelpActionRewardReqMsg", "smMethod": "ManHuangHelpActionRewardRespMsg"}, "25030": {"name": "S_ACTIVITY_WILD_SEND_SYNC_USER_DATA", "cmMethod": "ManHuangGetUserDataReqMsg", "smMethod": "ManHuangGetUserDataRespMsg"}, "25031": {"name": "S_ACTIVITY_WILD_SYNC_MARQUEE", "smMethod": "ManHuangMarqueeNotify"}, "21421": {"name": "S_WEEK_CARD_ROUND_REWARD", "cmMethod": "XyFundGetRewardReq", "smMethod": "XyFundGetRewardResp"}, "210701": {"name": "S_GOOD_FORTUNE_GET_REWARD_REQ", "cmMethod": "GoodFortuneGetRewardReq", "smMethod": "GoodFortuneGetRewardRsp"}, "24501": {"name": "S_CLIENT_STORAGE_DATA", "cmMethod": "SaveToServiceReq", "smMethod": "SaveToServiceRsp"}, "24502": {"name": "S_CLIENT_STORAGE_SYNC", "smMethod": "AllClientDataSync"}, "205301": {"name": "S_UNION_BATTLE_ENTER", "cmMethod": "UnionBattleEnterReq", "smMethod": "UnionBattleEnterRsp"}, "205302": {"name": "S_UNION_BATTLE_GET_JOIN_MEMBER_LIST", "cmMethod": "UnionBattleGetJoinMemberListReq", "smMethod": "UnionBattleGetJoinMemberListRsp"}, "205303": {"name": "S_UNION_BATTLE_MATCH", "cmMethod": "UnionBattleMatchReq", "smMethod": "UnionBattleMatchRsp"}, "205304": {"name": "S_UNION_BATTLE_CHALLENGE", "cmMethod": "UnionBattleChallengeReq", "smMethod": "UnionBattleChallengeRsp"}, "205306": {"name": "S_UNION_BATTLE_GET_REPORT", "cmMethod": "UnionBattleGetReportReq", "smMethod": "UnionBattleGetReportRsp"}, "205307": {"name": "S_UNION_BATTLE_BUY_BUFF", "cmMethod": "UnionBattleBuyOpenBuffReq", "smMethod": "UnionBattleBuyOpenBuffRsp"}, "205308": {"name": "S_UNION_BATTLE_SELECT_BUFF", "cmMethod": "UnionBattleSelectBuffReq", "smMethod": "UnionBattleSelectBuffRsp"}, "205309": {"name": "S_UNION_BATTLE_GET_PLAY_BACK_LIST", "cmMethod": "UnionBattleGetPlayBackListReq", "smMethod": "UnionBattleGetPlayBackListRsp"}, "205310": {"name": "S_UNION_BATTLE_RECEIVE_UNION_ACHIEVE_REWARD", "cmMethod": "UnionBattleReceiveUnionAchieveRewardReq", "smMethod": "UnionBattleReceiveUnionAchieveRewardRsp"}, "205311": {"name": "S_UnionBattlePb_SynPlayerInfo", "smMethod": "SynPlayerInfo"}, "205312": {"name": "S_UNION_BATTLE_GET_UNION_MEMBER_SCORE", "cmMethod": "UnionBattleGetUnionMemberScoreReq", "smMethod": "UnionBattleGetUnionMemberScoreRsp"}, "205313": {"name": "S_UNION_BATTLE_REDDOT", "cmMethod": "UnionBattleRedDotReqMsg", "smMethod": "UnionBattleRedDotRespMsg"}, "25501": {"name": "S_EQUIPMENT_REFINE", "cmMethod": "ReqEquipmentRefine", "smMethod": "RespEquipmentRefine"}, "25502": {"name": "S_EQUIPMENT_ADVANCE", "cmMethod": "ReqEquipmentAdvance", "smMethod": "RespEquipmentAdvance"}, "25503": {"name": "S_EQUIPMENT_SKILL_UNLOCK", "cmMethod": "ReqEquipmentActivate", "smMethod": "RespEquipmentActivate"}, "25504": {"name": "S_EQUIPMENT_ADVANCE_DATA_SYNC", "smMethod": "EquipmentAdvanceDataMsg"}, "25601": {"name": "S_SECRETTOWER_ENTER", "cmMethod": "SecretTowerEnterReq", "smMethod": "SecretTowerEnterRsp"}, "25602": {"name": "S_SECRETTOWER_FIGHT", "cmMethod": "SecretTowerFightReq", "smMethod": "SecretTowerFightResp"}, "25603": {"name": "S_SECRETTOWER_GETSTAGE", "cmMethod": "SecretTowerGetStageRewardReq", "smMethod": "SecretTowerGetStageRewardRsp"}, "25604": {"name": "S_SECRETTOWER_VIEWMONSTER", "cmMethod": "SecretTowerViewMonsterAttrReq", "smMethod": "SecretTowerViewMonsterAttrResp"}, "25605": {"name": "S_SECRETTOWER_SYNC", "smMethod": "SynSecretTowerInfo"}, "25606": {"name": "S_SECRETTOWER_VIEWACHI", "cmMethod": "SecretTowerAchievementReq", "smMethod": "SecretTowerAchievementRsp"}, "25607": {"name": "S_SECRETTOWER_GETACHI", "cmMethod": "SecretTowerGetAchiRewardReq", "smMethod": "SecretTowerGetAchiRewardRsp"}, "25801": {"name": "S_UNION_BOSS_ENTER", "cmMethod": "UnionBossInfoReqMsg", "smMethod": "UnionBossInfoRespMsg"}, "25802": {"name": "S_UNION_BOSS_ARRAYING", "cmMethod": "UnionBossBuffReqMsg", "smMethod": "UnionBossBuffRespMsg"}, "25803": {"name": "S_UNION_BOSS_GET_REWARD_INFO", "cmMethod": "UnionBossRewardReqMsg", "smMethod": "UnionBossRewardRespMsg"}, "25804": {"name": "S_UNION_BOSS_RECEIVE_REWARD", "cmMethod": "UnionBossRewardReceiveReqMsg", "smMethod": "UnionBossRewardReceiveRespMsg"}, "25805": {"name": "S_UNION_BOSS_BATTLE", "cmMethod": "UnionBossBattleReqMsg", "smMethod": "UnionBossBattleRespMsg"}, "25806": {"name": "S_UNION_BOSS_RECEIVE_ACHIEVE_REWARD", "cmMethod": "UnionBossAchieveRewardReqMsg", "smMethod": "UnionBossAchieveRewardRespMsg"}, "25807": {"name": "S_UNION_BOSS_BOSS_INFO_MSG_SYNC", "smMethod": "UnionBossMsg"}, "25808": {"name": "S_UNION_BOSS_MSG_BUFF_INFO_SYNC", "smMethod": "UnionBossBuff"}, "25809": {"name": "S_UNION_BOSS_GET_ADD_BUFF_PLAYER", "cmMethod": "UnionBossAddBuffPlayerReqMsg", "smMethod": "UnionBossAddBuffPlayerRespMsg"}, "214201": {"name": "S_FAIRYLAND_HEARTDEVIL_BATTLE", "cmMethod": "FairyLandHeartDevilBattleReq", "smMethod": "FairyLandHeartDevilBattleResp"}, "214202": {"name": "S_FAIRYLAND_SYNC_PLAYER_DATA", "cmMethod": "", "smMethod": "PlayerFairyLandDataMsg"}, "214204": {"name": "S_FAIRYLAND_GOTO_SOUTH_DOOR", "cmMethod": "FairyLandGoToSouthReq", "smMethod": "FairyLandGoToSouthResp"}, "214205": {"name": "S_FAIRYLAND_ENTER_SOUTH_DOOR", "cmMethod": "FairyLandEnterSouthReq", "smMethod": "FairyLandEnterSouthResp"}, "214206": {"name": "S_FAIRYLAND_SOUTH_DOOR_BATTLE", "cmMethod": "FairyLandSouthDoorBattleReq", "smMethod": "FairyLandSouthDoorBattleResp"}, "214207": {"name": "S_FAIRYLAND_SOUTH_DOOR_INVITE", "cmMethod": "FairyLandSouthDoorInviteReq", "smMethod": "FairyLandSouthDoorInviteResp"}, "214208": {"name": "S_FAIRYLAND_SOUTH_DOOR_HELP", "cmMethod": "FairyLandSouthDoorHelpReq", "smMethod": "FairyLandSouthDoorHelpResp"}, "214209": {"name": "S_FAIRYLAND_SOAR", "cmMethod": "FairyLandSoarReq", "smMethod": "FairyLandSoarResp"}, "214210": {"name": "S_FAIRYLAND_HEARTDEVIL_MONSTER", "cmMethod": "FairyLandMonsterReq", "smMethod": "FairyLandMonsterResp"}, "214211": {"name": "S_FAIRYLAND_SYNC_SERVERS_DATA", "cmMethod": "", "smMethod": "FairyLandServerSync"}, "206001": {"name": "S_UNION_AREA_WAR_BASE_INFO", "cmMethod": "UnionAreaWarReqMsg", "smMethod": "UnionAreaWarRespMsg"}, "206003": {"name": "S_UNION_AREA_WAR_BASE_GROUP", "cmMethod": "UnionAreaWarGroupReqMsg", "smMethod": "UnionAreaWarGroupRespMsg"}, "206004": {"name": "S_UNION_AREA_WAR_DEFENDERS", "cmMethod": "UnionAreaWarDefendersReqMsg", "smMethod": "UnionAreaWarDefendersRespMsg"}, "206005": {"name": "S_UNION_AREA_WAR_DEFENDERS_UPDATE", "cmMethod": "UnionAreaWarDefendersUpdateReqMsg", "smMethod": "UnionAreaWarDefendersUpdateRespMsg"}, "206006": {"name": "S_UNION_AREA_WAR_AREA_LIST", "cmMethod": "UnionAreWarListReqMsg", "smMethod": "UnionAreaWarListRespMsg"}, "206007": {"name": "S_UNION_AREA_WAR_AREA_LIST_UPDATE", "cmMethod": "UnionAreWarListUpdateReqMsg", "smMethod": "UnionAreaWarListUpdateRespMsg"}, "206009": {"name": "S_UNION_AREA_WAR_ATTACK", "cmMethod": "UnionAreaWarAttackReqMsg", "smMethod": "UnionAreaWarAttackRespMsg"}, "206011": {"name": "S_UNION_AREA_WAR_DEVELOP", "cmMethod": "UnionAreaWarDevelopReqMsg", "smMethod": "UnionAreaWarDevelopRespMsg"}, "206013": {"name": "S_UNION_AREA_WAR_TREASURE_INFO", "cmMethod": "UnionAreaWarTreasuryReqMsg", "smMethod": "UnionAreaWarTreasuryRespMsg"}, "206014": {"name": "S_UNION_AREA_WAR_TREASURE_DRAW", "cmMethod": "UnionAreaWarTreasuryDrawReqMsg", "smMethod": "UnionAreaWarTreasuryDrawRespMsg"}, "206015": {"name": "S_UNION_AREA_WAR_SUMMON_DRAGON", "cmMethod": "UnionAreaWarSummonDragonReqMsg", "smMethod": "UnionAreaWarSummonDragonRespMsg"}, "206016": {"name": "S_UNION_AREA_WAR_BASE_WORSHIP", "cmMethod": "UnionAreaWarWorshipReq", "smMethod": "UnionAreaWarWorshipRsp"}, "206019": {"name": "S_UNION_AREA_WAR_FIGHT_SCENE", "cmMethod": "UnionAreaWarFightSceneReqMsg", "smMethod": "UnionAreaWarFightSceneRespMsg"}, "206020": {"name": "S_UNION_AREA_WAR_CONSTRUCT_DATA", "cmMethod": "UnionAreaWarConstructReqMsg", "smMethod": "UnionAreaWarConstructRespMsg"}, "206021": {"name": "S_UNION_AREA_WAR_REPORT_DATA", "cmMethod": "UnionAreaWarReportReqMsg", "smMethod": "UnionAreaWarReportRespMsg"}, "206022": {"name": "S_UNION_AREA_WAR_REPORT_DETAIL", "cmMethod": "UnionAreaWarReportDetailReqMsg", "smMethod": "UnionAreaWarReportDetailRespMsg"}, "206023": {"name": "S_UNION_AREA_WAR_UNION_DAMAGE_INFO", "cmMethod": "UnionAreaWarUnionDamageReqMsg", "smMethod": "UnionAreaWarUnionDamageRespMsg"}, "206024": {"name": "S_UNION_AREA_WAR_UNION_BET_DATA", "cmMethod": "UnionAreaWarBetDataReqMsg", "smMethod": "UnionAreaWarBetDataRespMsg"}, "206025": {"name": "S_UNION_AREA_WAR_UNION_BET_SELECT", "cmMethod": "UnionAreaWarBetSelectReqMsg", "smMethod": "UnionAreaWarBetSelectRespMsg"}, "206026": {"name": "S_UNION_AREA_WAR_UNION_BET_REWARD", "cmMethod": "UnionAreaWarBetRewardReqMsg", "smMethod": "UnionAreaWarBetRewardRespMsg"}, "206027": {"name": "S_UNION_AREA_WAR_UNION_GET_MEMBER_LIST", "cmMethod": "UnionAreaWarGetJoinMemberListReq", "smMethod": "UnionAreaWarGetJoinMemberListRsp"}, "206028": {"name": "S_UNION_AREA_WAR_DRAGON_ATTACK_SYNC", "cmMethod": "", "smMethod": "SyncUnionAreaWarDragonAttackMsg"}, "206029": {"name": "S_UNION_AREA_WAR_UNION_GROUP_DAMAGE_INFO", "cmMethod": "UnionAreaWarUnionGroupDamageReqMsg", "smMethod": "UnionAreaWarUnionGroupDamageRespMsg"}, "206030": {"name": "S_UNION_AREA_WAR_UNION_RED_DOT_DATA", "cmMethod": "UnionAreaWarRedDotReqMsg", "smMethod": "UnionAreaWarRedDotRespMsg"}, "206031": {"name": "S_UNION_AREA_WAR_GUESS_PLAYER_LIST", "cmMethod": "UnionAreaWarGuessPlayersReqMsg", "smMethod": "UnionAreaWarGuessPlayersRespMsg"}, "206032": {"name": "S_UNION_AREA_WAR_DAN_GROUP_COUNT", "cmMethod": "UnionAreaWarDanGroupCountReq", "smMethod": "UnionAreaWarDanGroupCountResp"}, "216207": {"name": "S_UNION_TREASURE_ENTER", "cmMethod": "UnionTreasureEnterReq", "smMethod": "UnionTreasureEnterResp"}, "216202": {"name": "S_UNION_TREASURE_ASK_FOR", "cmMethod": "UnionTreasureAskForReq", "smMethod": "UnionTreasureAskForResp"}, "216203": {"name": "S_UNION_TREASURE_SEND_CHIP", "cmMethod": "UnionTreasureSendChipReq", "smMethod": "UnionTreasureSendChipResp"}, "216205": {"name": "S_UNION_TREASURE_BATTALE_RELIC", "cmMethod": "UnionTreasureBattleRelicReq", "smMethod": "UnionTreasureBattleRelicResp"}, "216206": {"name": "S_UNION_TREASURE_HUNT_TREASURE", "cmMethod": "UnionTreasureHuntTreasureReq", "smMethod": "UnionTreasureHuntTreasureResp"}, "216208": {"name": "S_UNION_TREASURE_RELIC_DATA_SYNC", "cmMethod": "", "smMethod": "UnionTreasureCollectMsg"}, "216209": {"name": "S_UNION_TREASURE_GET_CHIP", "cmMethod": "UnionTreasureGetGiveChipReq", "smMethod": "UnionTreasureGetGiveChipResp"}, "216201": {"name": "S_UNION_TREASURE_DRWA_CHIP", "cmMethod": "UnionTreasureDrawChipReq", "smMethod": "UnionTreasureDrawChipResp"}, "216216": {"name": "S_UNION_TREASURE_BOSS_HELP", "cmMethod": "UnionTreasureShareRelicReq", "smMethod": "UnionTreasureShareRelicResp"}, "216213": {"name": "S_UNION_TREASURE_RUINS_DATA", "cmMethod": "UnionTreasureAskForRelicReq", "smMethod": "UnionTreasureAskForRelicResp"}, "216218": {"name": "S_UNION_TREASURE_FILL_CHIP", "cmMethod": "UnionTreasureFillChipReq", "smMethod": "UnionTreasureFillChipResp"}, "216220": {"name": "S_UNION_TREASURE_TIME_ASYNC", "cmMethod": "", "smMethod": "UnionTreasuretLoginSync"}, "216222": {"name": "S_UNION_TREASURE_MESSAGE_VALID", "cmMethod": "checkMessageValidReq", "smMethod": "checkMessageValidResp"}, "216224": {"name": "S_UNION_TREASURE_GIVEN_LIST", "cmMethod": "UnionTreasureGetCollectGiveMsgReq", "smMethod": "UnionTreasureGetCollectGiveMsgResp"}, "216225": {"name": "S_UNION_TREASURE_EXIT", "cmMethod": "UnionTreasureExitReq", "smMethod": "UnionTreasureExitResp"}, "216227": {"name": "S_UNION_TREASURE_LEVEL_UP", "cmMethod": "", "smMethod": "UnionTreasureLvUpWindowsMsg"}, "216223": {"name": "S_UNION_TREASURE_BATTLE_RECORD", "cmMethod": "UnionTreasureGetBattleRecordReq", "smMethod": "UnionTreasureGetBattleRecordResp"}, "26301": {"name": "S_MAGIC_TREASURE_PLAYER_DATA_MSG", "smMethod": "MagicTreasurePlayerDataMsg"}, "26302": {"name": "S_MAGIC_TREASURE_DRAW_REQ", "cmMethod": "MagicTreasureDrawReq", "smMethod": "MagicTreasureDrawRsp"}, "26303": {"name": "S_MAGIC_TREASURE_LV_UP_REQ", "cmMethod": "MagicTreasureLvUpReq", "smMethod": "MagicTreasureLvUpRsp"}, "26304": {"name": "S_MAGIC_TREASURE_ACTIVE_REQ", "cmMethod": "MagicTreasureActiveReq", "smMethod": "MagicTreasureActiveRsp"}, "26305": {"name": "S_MAGIC_TREASURE_STAR_UP_REQ", "cmMethod": "MagicTreasureStarUpReq", "smMethod": "MagicTreasureStarUpRsp"}, "26306": {"name": "S_MAGIC_TREASURE_SYNC_DATA_MSG", "smMethod": "SyncMagicTreasureDataMsg"}, "26307": {"name": "S_MAGIC_TREASURE_LINK_CHANGE_REQ", "cmMethod": "MagicTreasureSwitchLinkageSkinReq", "smMethod": "MagicTreasureSwitchLinkageSkinResp"}, "21023": {"name": "S_ACTIVITY_SEEKTREASURE_DRAW", "cmMethod": "SeekTreasureDrawReq", "smMethod": "SeekTreasureDrawResp"}, "21024": {"name": "S_ACTIVITY_SEEKTREASURE_VIEW", "cmMethod": "SeekTreasureViewReq", "smMethod": "SeekTreasureViewResp"}, "21025": {"name": "S_ACTIVITY_SEEKTREASURE_RARE_REWARD", "cmMethod": "SeekTreasureChooseRareRewardReq", "smMethod": "SeekTreasureChooseRareRewardResp"}, "21026": {"name": "S_ACTIVITY_SEEKTREASURE_DRAW_NOTICE_SYNC", "cmMethod": "", "smMethod": "SeekTreasureNoticeSyncInfo"}, "21028": {"name": "S_ACTIVITY_SEEKTREASURE_SELECT_REWARD", "cmMethod": "SeekTreasureSelectRewardReqMsg", "smMethod": "SeekTreasureSelectRewardRespMsg"}, "217003": {"name": "S_YUE_BAO_INTERACTE", "cmMethod": "YueBaoInteractReq", "smMethod": "YueBaoInteractResp"}, "217001": {"name": "S_YUE_BAO_ENTER", "cmMethod": "YueBaoEnterReq", "smMethod": "YueBaoEnterResp"}, "217002": {"name": "S_YUE_BAO_DEPOSIT", "cmMethod": "YueBaoDepositReq", "smMethod": "YueBaoDepositResp"}, "217004": {"name": "s_YUE_BAO_DATA_SYNC", "smMethod": "RechargeItemData"}, "205901": {"name": "S_ASKDING_APPLICATION", "cmMethod": "AskDingApplicationReqMsg", "smMethod": "AskDingApplicationRespMsg"}, "205903": {"name": "S_ASKDING_ENTER_ACT", "cmMethod": "AskDingEnterActivityReqMsg", "smMethod": "AskDingEnterActivityRespMsg"}, "205904": {"name": "S_ASKDING_ENTER_ROOM", "cmMethod": "AskDingEnterPlaceReqMsg", "smMethod": "AskDingEnterPlaceRespMsg"}, "205905": {"name": "S_ASKDING_LEAVE_ROOM", "cmMethod": "AskDingLeavePlaceReqMsg", "smMethod": "AskDingLeavePlaceRespMsg"}, "205906": {"name": "S_ASKDING_MOVE", "cmMethod": "AskDingMoveReqMsg", "smMethod": "AskDingMoveRespMsg"}, "205907": {"name": "S_ASKDING_ATTACK", "cmMethod": "AskDingAttackReqMsg", "smMethod": "AskDingAttackRespMsg"}, "205908": {"name": "S_ASKDING_GET_BOX_REWARD", "cmMethod": "AskDingGetBoxRewardReq", "smMethod": "AskDingGetBoxRewardRsp"}, "205911": {"name": "S_ASKDING_GET_PLACE", "cmMethod": "AskDingGetPlaceInfoReqMsg", "smMethod": "AskDingGetPlaceInfoRespMsg"}, "205913": {"name": "S_ASKDING_SYNC_ATTACKED", "cmMethod": "", "smMethod": "AskDingAttackedSyncMsg"}, "205914": {"name": "S_ASKDING_SYNC_SCORE", "cmMethod": "", "smMethod": "AskDingUnionScoreSyncMsg"}, "205915": {"name": "S_ASKDING_SYNC_PLACE", "cmMethod": "", "smMethod": "AskDingPlaceSyncMsg"}, "205916": {"name": "S_ASKDING_SYNC_NOTICE", "cmMethod": "", "smMethod": "AskDingNoticeSyncMsg"}, "205918": {"name": "S_ASKDING_SYNC_BEATC", "cmMethod": "", "smMethod": "AskDingAttackedUserSyncMsg"}, "205919": {"name": "S_ASKDING_SYNC_SEIZE", "cmMethod": "", "smMethod": "AskDingPlaceSeizeSyncMsg"}, "205920": {"name": "S_ASKDING_SYNC_ENTER_ROOM", "cmMethod": "", "smMethod": "AskDingEnterPlaceSync"}, "205921": {"name": "S_ASKDING_SYNC_LEAVE_ROOM", "cmMethod": "", "smMethod": "AskDingLeavePlaceSync"}, "205922": {"name": "S_ASKDING_SYNC_BLOWOUT", "cmMethod": "", "smMethod": "AskDingSyncBlowoutMsg"}, "205923": {"name": "S_ASKDING_RESULT_SYNC", "cmMethod": "", "smMethod": "AskDingEndDataSyncMsg"}, "205924": {"name": "S_ASKDING_UNION_NAME_LIST", "cmMethod": "AskDingUnionNameListReqMsg", "smMethod": "AskDingUnionNameListRespMsg"}, "205925": {"name": "S_ASKDING_GET_UNION_USER_LIST", "cmMethod": "AskDingGetUnionUserListReqMsg", "smMethod": "AskDingGetUnionUserListRespMsg"}, "205926": {"name": "S_ASKDING_GET_BREAKOUT_LIST", "cmMethod": "AskDingBreakGameListReq", "smMethod": "AskDingBreakGameListResp"}, "205927": {"name": "S_ASKDING_GET_BREAKOUT_ALL_LIST", "cmMethod": "AskDingBreakGameListAllReq", "smMethod": "AskDingBreakGameListAllResp"}, "205928": {"name": "S_ASKDING_GAME_SETTLE_INFO", "cmMethod": "AskDingGameSettleInfoReq", "smMethod": "AskDingGameSettleInfoResp"}, "205929": {"name": "S_ASKDING_FINAL_GAME_ALL_LIST", "cmMethod": "AskDingFinalGameListAllReq", "smMethod": "AskDingFinalGameListAllResp"}, "205930": {"name": "S_ASKDING_WORSHIP_INFO", "cmMethod": "AskDingWorshipInfoReq", "smMethod": "AskDingWorshipInfoResp"}, "205931": {"name": "S_ASKDING_WORSHIP", "cmMethod": "AskDingWorshipReq", "smMethod": "AskDingWorshipRsp"}, "205932": {"name": "S_ASKDING_FINAL_GAME_INFO", "cmMethod": "AskDingFinalGameInfoReq", "smMethod": "AskDingFinalGameInfoResp"}, "205933": {"name": "S_ASKDING_GET_CHAMPION_HISTORY", "cmMethod": "AskDingChampionHistoryReqMsg", "smMethod": "AskDingChampionHistoryRespMsg"}, "205934": {"name": "S_ASKDING_BREAKOUT_SCORE_LIST", "cmMethod": "AskDingBreakRoundScoreListReqMsg", "smMethod": "AskDingBreakRoundScoreListRespMsg"}, "205935": {"name": "S_ASKDING_CHAMPION_INFO", "cmMethod": "AskDingGetChampionInfoReq", "smMethod": "AskDingGetChampionInfoResp"}, "205936": {"name": "S_ASKDING_RED_POINT", "cmMethod": "AskDingRedPointReqMsg", "smMethod": "AskDingRedPointRespMsg"}, "205937": {"name": "S_ASKDING_FINAL_GAME_END_SYNC", "cmMethod": "", "smMethod": "AskDingFinalGameEndSyncMsg"}, "205938": {"name": "S_ASKDING_PLAYER_JOIN_SYNC", "smMethod": "AskDingSyncPlayerJoinMsg"}, "205939": {"name": "S_ASKDING_WATCH_GAME", "cmMethod": "AskDingWatchGameReqMsg", "smMethod": "AskDingWatchGameRespMsg"}, "207002": {"name": "S_SYNC_GATHER_ENERGY_MSG", "cmMethod": "", "smMethod": "SyncGatherEnergyMsg"}, "207003": {"name": "S_GATHER_ENERGY_OPEN_VIEW", "cmMethod": "GatherEnergyOpenViewReq", "smMethod": "GatherEnergyOpenViewResp"}, "207004": {"name": "S_GATHER_ENERGY_OPEN", "cmMethod": "GatherEnergyOpenReq", "smMethod": "GatherEnergyOpenResp"}, "207007": {"name": "S_GATHER_ENERGY_FIGHT", "cmMethod": "GatherEnergyFightReq", "smMethod": "GatherEnergyFightResp"}, "207010": {"name": "S_GATHER_ENERGY_REWARD_SHOW", "cmMethod": "", "smMethod": "GatherEnergyRewardShowResp"}, "207011": {"name": "S_GATHER_ENERGY_GET_REWARD", "cmMethod": "GatherEnergyRewardReq", "smMethod": "GatherEnergyRewardResp"}, "207012": {"name": "S_GATHER_ENERGY_LIKE", "cmMethod": "GatherEnergyLikeReq", "smMethod": "GatherEnergyLikeResp"}, "207015": {"name": "S_GATHER_ENERGY_TRANSFORM", "cmMethod": "GatherEnergyTransformReq", "smMethod": "GatherEnergyTransformResp"}, "207017": {"name": "S_GATHER_ENERGY_LEAVE", "cmMethod": "GatherEnergyLeaveReq", "smMethod": "GatherEnergyLeaveResp"}, "207018": {"name": "S_GATHER_ENERGY_NOTICE", "cmMethod": "GatherEnergyNoticeReq", "smMethod": "GatherEnergyNoticeResp"}, "207019": {"name": "S_GATHER_ENERGY_GET_AD_AWARD", "cmMethod": "GatherEnergyGetADAwardReq", "smMethod": "GatherEnergyGetADAwardResp"}, "207001": {"name": "S_GATHER_ENERGY_ENTER_NEW", "cmMethod": "GatherEnergyEnterNewReq", "smMethod": "GatherEnergyEnterNewResp"}, "207020": {"name": "S_GATHER_ENERGY_FIRST_LIST_VIEW", "cmMethod": "GatherEnergyFirstListViewReq", "smMethod": "GatherEnergyFirstListViewResp"}, "207021": {"name": "S_GATHER_ENERGY_SECOND_LIST_VIEW", "cmMethod": "GatherEnergySecondListViewReq", "smMethod": "GatherEnergySecondListViewResp"}, "207022": {"name": "S_GATHER_ENERGY_INSIDE_VIEW_NEW", "cmMethod": "GatherEnergyInsideViewNewReq", "smMethod": "GatherEnergyInsideViewNewResp"}, "207023": {"name": "S_GATHER_ENERGY_FIGHT_REPORT_NEW", "cmMethod": "GatherEnergyFightReportNewReq", "smMethod": "GatherEnergyFightReportNewResp"}, "207024": {"name": "S_GATHER_ENERGY_SEARCH_NEW", "cmMethod": "GatherEnergySearchNewReq", "smMethod": "GatherEnergySearchNewResp"}, "207025": {"name": "S_GATHER_ENERGY_ATTEND_NEW", "cmMethod": "GatherEnergyAttendNewReq", "smMethod": "GatherEnergyAttendNewResp"}, "206101": {"name": "S_ASKWAY_ENTER", "cmMethod": "AskWayEnterReq", "smMethod": "AskWayEnterRsp"}, "206102": {"name": "S_ASKWAY_MATCH", "cmMethod": "AskWayMatchReq", "smMethod": "AskWayMatchRsp"}, "206103": {"name": "S_ASKWAY_BATTLE", "cmMethod": "AskWayBattleReq", "smMethod": "AskWayBattleRsp"}, "206104": {"name": "S_ASKWAY_GETREPORT", "cmMethod": "AskWayGetReportReq", "smMethod": "AskWayGetReportRsp"}, "206105": {"name": "S_ASKWAY_BATTLEREPLY", "cmMethod": "AskWayBattleReplyReq", "smMethod": "AskWayBattleReplyRsp"}, "206106": {"name": "S_ASKWAY_RECEIVETIERREWAWARD", "cmMethod": "AskWayReceiveTierRewardReq", "smMethod": "AskWayReceiveTierRewardRsp"}, "206107": {"name": "S_ASKWAY_ReceiveScoreReward", "cmMethod": "AskWayReceiveScoreRewardReq", "smMethod": "AskWayReceiveScoreRewardRsp"}, "206108": {"name": "S_ASKWAY_BUYCHALLENGETIME", "cmMethod": "AskWayBuyFightTicketReq", "smMethod": "AskWayBuyFightTicketRsp"}, "206109": {"name": "S_ASKWAY_GETPLAYERINFO", "cmMethod": "AskWayGetPlayerDetailReq", "smMethod": "AskWayGetPlayerDetailRsp"}, "206110": {"name": "S_ASKWAY_GetGuessCoin", "cmMethod": "AskWayGetGuessCoinReq", "smMethod": "AskWayGetGuessCoinRsp"}, "206111": {"name": "S_ASKWAY_ToSkyGuess", "cmMethod": "AskWayToSkyGuessReq", "smMethod": "AskWayToSkyGuessRsp"}, "206112": {"name": "S_ASKWAY_ToSkyBattleReply", "cmMethod": "AskWayToSkyBattleReplyReq", "smMethod": "AskWayToSkyBattleReplyRsp"}, "206113": {"name": "S_ASKWAY_Worship", "cmMethod": "AskWayWorshipReq", "smMethod": "AskWayWorshipRsp"}, "206114": {"name": "S_ASKWAY_ToSkyRankReward", "cmMethod": "AskWayReceiveToSkyRankRewardReq", "smMethod": "AskWayReceiveToSkyRankRewardRsp"}, "206115": {"name": "S_ASKWAY_ToSkyRoster", "cmMethod": "AskWayToSkyRosterReq", "smMethod": "AskWayToSkyRosterRsp"}, "206116": {"name": "S_ASKWAY_ToSkyGetGoupData", "cmMethod": "AskWayToSkyGetBattleResultReq", "smMethod": "AskWayToSkyGetBattleResultRsp"}, "206117": {"name": "S_ASKWAY_GetToSkyGuessInfo", "cmMethod": "AskWayGetGuessInfoReq", "smMethod": "AskWayGetGuessInfoRsp"}, "206118": {"name": "S_ASKWAY_GetToSkyReport", "cmMethod": "AskWayToSkyGetReportReq", "smMethod": "AskWayToSkyGetReportRsp"}, "206132": {"name": "S_ASKWAY_GetCurStateInfo", "cmMethod": "AskWayGetGetCurStateInfoReq", "smMethod": "AskWayGetGetCurStateInfoRsp"}, "206801": {"name": "S_GOD_ISLAND_BASE_INFO_LOAD", "cmMethod": "GodIslandBaseInfoReqMsg", "smMethod": "GodIslandBaseInfoRespMsg"}, "206802": {"name": "S_GOD_ISLAND_GROUP", "cmMethod": "GodIslandGroupReqMsg", "smMethod": "GodIslandGroupRespMsg"}, "206803": {"name": "S_GOD_ISLAND_REPORT_DATA", "cmMethod": "GodIslandReportReqMsg", "smMethod": "GodIslandReportRespMsg"}, "206804": {"name": "S_GOD_ISLAND_REPORT_DETAIL", "cmMethod": "GodIslandReportDetailReqMsg", "smMethod": "GodIslandReportDetailRespMsg"}, "206805": {"name": "S_GOD_ISLAND_UNION_DAMAGE_INFO", "cmMethod": "GodIslandUnionDamageReqMsg", "smMethod": "GodIslandUnionDamageRespMsg"}, "206806": {"name": "S_GOD_ISLAND_UNION_GET_MEMBER_LIST", "cmMethod": "GodIslandGetJoinMemberListReq", "smMethod": "GodIslandGetJoinMemberListRsp"}, "206807": {"name": "S_GOD_ISLAND_UNION_GROUP_DAMAGE_INFO", "cmMethod": "GodIslandUnionGroupDamageReqMsg", "smMethod": "GodIslandUnionGroupDamageRespMsg"}, "206808": {"name": "S_GOD_ISLAND_RED_DOT_DATA", "cmMethod": "GodIslandRedDotReqMsg", "smMethod": "GodIslandRedDotRespMsg"}, "206809": {"name": "S_GOD_ISLAND_WORSHIP", "cmMethod": "GodIslandWorshipReq", "smMethod": "GodIslandWorshipResp"}, "206810": {"name": "S_GOD_ISLAND_PLAYER_BATTLE_REPORT", "cmMethod": "GodIslandPlayerReportReqMsg", "smMethod": "GodIslandPlayerReportRespMsg"}, "206811": {"name": "S_GOD_ISLAND_UPDATE_POWER", "cmMethod": "GodIslandUpdatePowerReq", "smMethod": "GodIslandUpdatePowerResp"}, "206812": {"name": "S_GOD_ISLAND_UNION_BATTLE_SCORE_LIST", "cmMethod": "GodIslandUnionBattleScoreListReq", "smMethod": "GodIslandUnionBattleScoreListResp"}, "206813": {"name": "S_GOD_ISLAND_UNION_LIQUID_RECEIVE_RECORD", "cmMethod": "GodIslandLiquidReceiveRecordReq", "smMethod": "GodIslandLiquidReceiveRecordResp"}, "206814": {"name": "S_GOD_ISLAND_UNION_MEMBER_SCORE_LIST", "cmMethod": "GodIslandUnionMemberScoreListReq", "smMethod": "GodIslandUnionMemberScoreListResp"}, "206819": {"name": "S_GOD_ISLAND_HEART_BEAT", "cmMethod": "GodIslandHeartBeatReqMsg", "smMethod": "GodIslandHeartBeatRespMsg"}, "206820": {"name": "S_GOD_ISLAND_GAME_INFO_LOAD", "cmMethod": "GodIslandGameInfoReqMsg", "smMethod": "GodIslandGameInfoRespMsg"}, "206822": {"name": "S_GOD_ISLAND_GAME_ROUTE_INFO_SYNC", "cmMethod": "", "smMethod": "GodIslandGameRouteInfoSyncMsg"}, "206823": {"name": "S_GOD_ISLAND_GAME_MAP_CITY_INFO", "cmMethod": "GodIslandGameCityInfoReqMsg", "smMethod": "GodIslandGameCityInfoRespMsg"}, "206824": {"name": "S_GOD_ISLAND_GAME_EVENT", "cmMethod": "GodIslandGameEventReqMsg", "smMethod": "GodIslandGameEventRespMsg"}, "206825": {"name": "S_GOD_ISLAND_GAME_CITY_BUFF_SYNC", "cmMethod": "", "smMethod": "GodIslandGameCityBuffSync"}, "206826": {"name": "S_GOD_ISLAND_GAME_SPIRITUALBALL_INFO", "cmMethod": "GodIslandGameSpiritualBallInfoReqMsg", "smMethod": "GodIslandGameSpiritualBallInfoRespMsg"}, "206827": {"name": "S_GOD_ISLAND_GAME_USE_SPIRITUALBALL", "cmMethod": "GodIslandGameUseSpiritualBallReqMsg", "smMethod": "GodIslandGameUseSpiritualBallRespMsg"}, "206828": {"name": "S_GOD_ISLAND_GAME_TARGET_CITY_INFO", "cmMethod": "GodIslandGameTargetCityInfoReqMsg", "smMethod": "GodIslandGameTargetCityInfoRespMsg"}, "206829": {"name": "S_GOD_ISLAND_GAME_TARGET_CITY_LINE_INFO", "cmMethod": "GodIslandGameTargetCityLineInfoReqMsg", "smMethod": "GodIslandGameTargetCityLineInfoRespMsg"}, "206830": {"name": "S_GOD_ISLAND_GAME_ATTACK", "cmMethod": "GodIslandGameAttackReqMsg", "smMethod": "GodIslandGameAttackRespMsg"}, "206831": {"name": "S_GOD_ISLAND_GAME_AUTO_ATTACK", "cmMethod": "GodIslandGameAutoAttackReqMsg", "smMethod": "GodIslandGameAutoAttackRespMsg"}, "206832": {"name": "S_GOD_ISLAND_GAME_MOVE", "cmMethod": "GodIslandGameMoveReqMsg", "smMethod": "GodIslandGameMoveRespMsg"}, "206833": {"name": "S_GOD_ISLAND_GAME_EVENT_SYNC", "cmMethod": "", "smMethod": "GodIslandGameEventSyncMsg"}, "206834": {"name": "S_GOD_ISLAND_GAME_MINIMAP_INFO", "cmMethod": "GodIslandGameMiniMapInfoReqMsg", "smMethod": "GodIslandGameMiniMapInfoRespMsg"}, "206835": {"name": "S_GOD_ISLAND_GAME_SET_UNION_TYPE", "cmMethod": "GodIslandGameSetUnionTypeReqMsg", "smMethod": "GodIslandGameSetUnionTypeRespMsg"}, "206836": {"name": "S_GOD_ISLAND_GAME_PLANT_INFO", "cmMethod": "GodIslandGamePlantInfoReqMsg", "smMethod": "GodIslandGamePlantInfoRespMsg"}, "206837": {"name": "S_GOD_ISLAND_GAME_USE_FRUIT", "cmMethod": "GodIslandUseFruitReq", "smMethod": "GodIslandUseFruitResp"}, "206838": {"name": "S_GOD_ISLAND_GAME_PLANT_RECEIVE_FRUITS", "cmMethod": "GodIslandGamePlantReceiveFruitsReqMsg", "smMethod": "GodIslandGamePlantReceiveFruitsRespMsg"}, "206839": {"name": "S_GOD_ISLAND_GAME_PLANT_WATER", "cmMethod": "GodIslandGamePlantWaterReqMsg", "smMethod": "GodIslandGamePlantWaterRespMsg"}, "206840": {"name": "S_GOD_ISLAND_CRYSTAL_INFO", "cmMethod": "GodIslandCrystalInfoReqMsg", "smMethod": "GodIslandCrystalInfoRespMsg"}, "206841": {"name": "S_GOD_ISLAND_CRYSTAL_RECEIVE", "cmMethod": "GodIslandCrystalReceiveReqMsg", "smMethod": "GodIslandCrystalReceiveRespMsg"}, "206842": {"name": "S_GOD_ISLAND_GAME_STRATEGY_INFO", "cmMethod": "GodIslandGameStrategyInfoReqMsg", "smMethod": "GodIslandGameStrategyInfoRespMsg"}, "206843": {"name": "S_GOD_ISLAND_GAME_COMMANDER_SET", "cmMethod": "GodIslandGameCommanderSetReqMsg", "smMethod": "GodIslandGameCommanderSetRespMsg"}, "206844": {"name": "S_GOD_ISLAND_GAME_SET_UNION_TARGET", "cmMethod": "GodIslandGameSetUnionTargetReqMsg", "smMethod": "GodIslandGameSetUnionTargetRespMsg"}, "206845": {"name": "S_GOD_ISLAND_GAME_FOR_HELP", "cmMethod": "GodIslandGameForHelpReqMsg", "smMethod": "GodIslandGameForHelpRespMsg"}, "206846": {"name": "S_GOD_ISLAND_GAME_CITY_CHANGE_SYNC", "cmMethod": "", "smMethod": "GodIslandGameCityChangeSyncMsg"}, "206847": {"name": "S_GOD_ISLAND_GAME_PLAYER_WIN_SYNC", "cmMethod": "", "smMethod": "GodIslandGamePlayerWinSyncMsg"}, "206848": {"name": "S_GOD_ISLAND_GAME_LINE_INFO_SYNC", "cmMethod": "", "smMethod": "GodIslandGameLineInfoSyncMsg"}, "206849": {"name": "S_GOD_ISLAND_GAME_ACCELERATE_MOVE", "cmMethod": "GodIslandGameAcclerateMoveMsg", "smMethod": "GodIslandGameAcclerateMoveRespMsg"}, "206850": {"name": "S_GOD_ISLAND_GAME_MYGAMEINFO_SYNC", "cmMethod": "", "smMethod": "GodIslandGameMyGameInfoSyncMsg"}, "206851": {"name": "S_GOD_ISLAND_GAME_PLANT_SEND_LIQUID", "cmMethod": "GodIslandGameSendLiquidReqMsg", "smMethod": "GodIslandGameSendLiquidRespMsg"}, "206852": {"name": "S_GOD_ISLAND_GAME_PLANT_SEND_LIQUID_RECORD", "cmMethod": "GodIslandGameSendLiquidRecordReqMsg", "smMethod": "GodIslandGameSendLiquidRecordRespMsg"}, "206853": {"name": "S_GOD_ISLAND_GAME_HORSE_LAMP_SYNC", "cmMethod": "", "smMethod": "GodIslandGameHorseLampSyncMsg"}, "206854": {"name": "S_GOD_ISLAND_SUPPRESS_BUFF_SYNC", "cmMethod": "", "smMethod": "GodIslandSuppressBuffSyncMsg"}, "206855": {"name": "S_GOD_ISLAND_GAME_CITY_BUFF_INFO", "cmMethod": "GodIslandGameCityBuffInfoReqMsg", "smMethod": "GodIslandGameCityBuffInfoRespMsg"}, "206856": {"name": "S_GOD_ISLAND_GAME_BACK_HOME", "cmMethod": "GodIslandBackHomeReq", "smMethod": "GodIslandBackHomeResp"}, "206857": {"name": "S_GOD_ISLAND_GAME_LEFT_OUT_SYNC", "cmMethod": "", "smMethod": "GodIslandLeftOutSyncInfo"}, "206858": {"name": "S_GOD_ISLAND_GAME_SET_FRIEND_UNION", "cmMethod": "GodIslandSetFriendUnionReq", "smMethod": "GodIslandSetFriendUnionResp"}, "206859": {"name": "S_GOD_ISLAND_GAME_FRIEND_UNION_SYNC", "cmMethod": "", "smMethod": "GodIslandSyncFriendUnionMsg"}, "206860": {"name": "S_GOD_ISLAND_GAME_COMMANDER_CHANGE_SYNC", "cmMethod": "", "smMethod": "GodIslandCommanderChangeSyncMsg"}, "206861": {"name": "S_GOD_ISLAND_BEEN_KILL_SYNC", "cmMethod": "", "smMethod": "GodIslandBeenKillSyncMsg"}, "206862": {"name": "S_GOD_ISLAND_GAME_CITY_BATTLE", "cmMethod": "", "smMethod": "GodIslandGameCityBattleSyncMsg"}, "206863": {"name": "S_GOD_ISLAND_AUTO_BATTLE_STOP_SYNC", "cmMethod": "", "smMethod": "GodIslandAutoBattleStopSyncMsg"}, "206864": {"name": "S_GOD_ISLAND_GHOST_CITY_UNLOCK_SYNC", "cmMethod": "", "smMethod": "GodIslandGhostCityUnlockSyncMsg"}, "206730": {"name": "S_UNION_FIGHT_LOGIN_SYNC", "smMethod": "UnionFightApplyDataSync"}, "206701": {"name": "S_UNION_FIGHT_MAIN", "cmMethod": "UnionFightMainReq", "smMethod": "UnionFightMainRsp"}, "206702": {"name": "S_UNION_FIGHT_APPLY", "cmMethod": "UnionFightApplyReq", "smMethod": "UnionFightApplyRsp"}, "206703": {"name": "S_UNION_FIGHT_ENTER", "cmMethod": "UnionFightEnterReq", "smMethod": "UnionFightEnterRsp"}, "206704": {"name": "S_UNION_FIGHT_REQUEST", "cmMethod": "UnionFightRequestReq", "smMethod": "UnionFightRequestRsp"}, "206705": {"name": "S_UNION_FIGHT_UP", "cmMethod": "UnionFightPositionReq", "smMethod": "UnionFightPositionRsp"}, "206706": {"name": "S_UNION_FIGHT_DOWN", "cmMethod": "UnionFightUnPositionReq", "smMethod": "UnionFightUnPositionRsp"}, "206707": {"name": "S_UNION_FIGHT_GROUP_RANK", "cmMethod": "UnionFightGroupRankReq", "smMethod": "UnionFightGroupRankRsp"}, "206721": {"name": "S_UNION_FIGHT_DATA_SYNC", "smMethod": "UnionFightPrepareDataSync"}, "206708": {"name": "S_UNION_FIGHT_LEAVE", "cmMethod": "UnionFightLeaveReq"}, "206709": {"name": "S_UNION_FIGHT_SUPREMACY", "cmMethod": "UnionFightSupremacyListReq", "smMethod": "UnionFightSupremacyListRsp"}, "206710": {"name": "S_UNION_FIGHT_WORSHIP", "cmMethod": "UnionFightWorshipReq", "smMethod": "UnionFightWorshipRsp"}, "206711": {"name": "S_UNION_FIGHT_PLAY_BACK", "cmMethod": "UnionFightFightPlaybackReq", "smMethod": "UnionFightFightPlaybackRsp"}, "206712": {"name": "S_UNION_FIGHT_RECORD", "cmMethod": "UnionFightRecordReq", "smMethod": "UnionFightRecordRsp"}, "206713": {"name": "S_UNION_FIGHT_HISTORY", "cmMethod": "UnionFightGetHistoryDataReq", "smMethod": "UnionFightGetHistoryDataRsp"}, "206714": {"name": "S_UNION_FIGHT_LOCKED_DETAIL", "cmMethod": "UnionFightGetLockedDetailReq", "smMethod": "UnionFightGetLockedDetailRsp"}, "206715": {"name": "S_UNION_FIGHT_SET_LOCK", "cmMethod": "UnionFightChangeLockStatusReq", "smMethod": "UnionFightChangeLockStatusRsp"}, "206716": {"name": "S_UNION_FIGHT_TODAY_DETAIL", "cmMethod": "UnionFightGetTodayResultReq", "smMethod": "UnionFightGetTodayResultRsp"}, "206717": {"name": "S_UNION_FIGHT_GET_UNION_RANK_LIST", "cmMethod": "UnionFightGetUnionRankListReq", "smMethod": "RspUnionList"}, "208501": {"name": "SYNC_UNION_DUEL_MSG", "cmMethod": "UnionDuelSyncMsgReq", "smMethod": "SyncUnionDuelMsg"}, "208502": {"name": "S_UNION_DUEL_OPEN_VIEW", "cmMethod": "UnionDuelOpenViewReq", "smMethod": "UnionDuelOpenViewResp"}, "208503": {"name": "S_UNION_DUEL_APPLY", "cmMethod": "UnionDuelApplyReq", "smMethod": "UnionDuelApplyResp"}, "208504": {"name": "S_UNION_DUEL_OPEN_LIST_VIEW", "cmMethod": "UnionDuelOpenListViewReq", "smMethod": "UnionDuelOpenListViewResp"}, "208505": {"name": "S_UNION_DUEL_FIGHT", "cmMethod": "UnionDuelFightReq", "smMethod": "UnionDuelFightResp"}, "208508": {"name": "S_UNION_DUEL_OPEN_BATTLE_NOTICE", "cmMethod": "UnionDuelOpenBattleNoticeReq", "smMethod": "UnionDuelOpenBattleNoticeResp"}, "208509": {"name": "S_UNION_DUEL_OPEN_CONTRIBUTE_VIEW", "cmMethod": "UnionDuelContributeReq", "smMethod": "UnionDuelContributeResp"}, "208510": {"name": "S_UNION_DUEL_OPEN_REWARD_PREVIEW", "cmMethod": "UnionDuelRewardPreviewReq", "smMethod": "UnionDuelRewardPreviewResp"}, "208511": {"name": "S_UNION_DUEL_SYNC_DATA", "cmMethod": "UnionDuelSyncDataReq", "smMethod": "UnionDuelSyncDataResp"}, "213602": {"name": "S_UNION_BOUNTY_ENTRY_MAP", "cmMethod": "UnionBountyEnterMapReq", "smMethod": "UnionBountyEnterMapResp"}, "213603": {"name": "S_UNION_BOUNTY_EXIT_MAP", "cmMethod": "UnionBountyExitMapReq", "smMethod": "UnionBountyExitMapResp"}, "213604": {"name": "S_UNION_BOUNTY_PLUNDER", "cmMethod": "UnionBountyPlunderReq", "smMethod": "UnionBountyPlunderResp"}, "213605": {"name": "S_UNION_BOUNTY_DEAL_BOUNTY", "cmMethod": "UnionBountyDealBountyReq", "smMethod": "UnionBountyDealBountyResp"}, "213606": {"name": "S_UNION_BOUNTY_GET_REWARD_ESCORT", "cmMethod": "UnionBountyGetRewardEscortReq", "smMethod": "UnionBountyGetRewardEscortResp"}, "213608": {"name": "S_UNION_BOUNTY_CHECK_ESCORT", "cmMethod": "UnionBountyCheckEscortReq", "smMethod": "UnionBountyCheckEscortResp"}, "213609": {"name": "S_UNION_BOUNTY_GET_REPORT", "cmMethod": "UnionBountyGetReportReq", "smMethod": "UnionBountyGetReportResp"}, "213611": {"name": "S_UNION_BOUNTY_REPORT_CHECK_ESCORT", "cmMethod": "UnionBountyReportCheckEscortReq", "smMethod": "UnionBountyReportCheckEscortResp"}, "213612": {"name": "S_UNION_BOUNTY_WORSHIP", "cmMethod": "UnionBountyWorshipReq", "smMethod": "UnionBountyWorshipResp"}, "213613": {"name": "S_UNION_BOUNTY_REFRESH_MAP", "cmMethod": "UnionBountyRefreshMapReq", "smMethod": "UnionBountyRefreshMapResp"}, "213614": {"name": "S_UNION_BOUNTY_OPEN_BOUNTY_EVENT", "cmMethod": "UnionBountyOpenBountyEventReq", "smMethod": "UnionBountyOpenBountyEventResp"}, "213615": {"name": "S_UNION_BOUNTY_OPEN_BOUNTY_RETALIATE", "cmMethod": "UnionBountyRetaliateReq", "smMethod": "UnionBountyRetaliateResp"}, "213616": {"name": "S_UNION_BOUNTY_GET_MEMBER_SCORE", "cmMethod": "UnionBountyGetMemberScoreReq", "smMethod": "UnionBountyGetMemberScoreResp"}, "213617": {"name": "S_UNION_BOUNTY_HAVE_ESCORT", "cmMethod": "UnionBountyHaveEscortReq", "smMethod": "UnionBountyHaveEscortResp"}, "213618": {"name": "S_UNION_BOUNTY_OPEN_ESCORT_DETAIL", "cmMethod": "UnionBountyOpenEscortCartDetailReq", "smMethod": "UnionBountyOpenEscortCartDetailRsp"}, "213619": {"name": "S_UNION_BOUNTY_OPEN_MONSTER", "cmMethod": "UnionBountyOpenMonsterReq", "smMethod": "UnionBountyOpenMonsterResp"}, "213620": {"name": "S_UNION_BOUNTY_ATTACK_MONSTER", "cmMethod": "UnionBountyAttackMonsterReq", "smMethod": "UnionBountyAttackMonsterResp"}, "213621": {"name": "S_UNION_BOUNTY_ASK_HELP", "cmMethod": "UnionBountyAskHelpReq", "smMethod": "UnionBountyAskHelpResp"}, "213622": {"name": "S_UNION_BOUNTY_OPEN_ASK_HELP", "cmMethod": "UnionBountyOpenAskHelpReq", "smMethod": "UnionBountyOpenAskHelpResp"}, "213623": {"name": "S_UNION_BOUNTY_DEAL_ASK_HELP", "cmMethod": "UnionBountyDealAskHelpReq", "smMethod": "UnionBountyDealAskHelpResp"}, "213624": {"name": "S_UNION_BOUNTY_MANAGE_TEAM", "cmMethod": "UnionBountyManageAskHelpReq", "smMethod": "UnionBountyManageAskHelpResp"}, "213625": {"name": "S_UNION_BOUNTY_SYN_MONSTER_INFO", "cmMethod": "", "smMethod": "UnionBountySynMonsterInfoMsg"}, "213626": {"name": "S_UNION_BOUNTY_GET_MONSTER_ATTRIBUTE", "cmMethod": "UnionBountyGetMonsterAttributeReq", "smMethod": "UnionBountyGetMonsterAttributeResp"}, "213627": {"name": "S_UNION_BOUNTY_MONSTER_CHANGE_POS", "cmMethod": "UnionBountyMonsterChangePosReq", "smMethod": "UnionBountyMonsterChangePosRsp"}, "213628": {"name": "S_UNION_BOUNTY_GET_EXIST_MONSTER", "cmMethod": "UnionBountyGetExistMonsterReq", "smMethod": "UnionBountyGetExistMonsterResp"}, "209403": {"name": "S_DOURO_OPEN_VIEW", "cmMethod": "GetPlayerDouroDataReq", "smMethod": "GetPlayerDouroDataResp"}, "209401": {"name": "S_DOURO_USE_SOUL_POWER", "cmMethod": "DouroInjectSoulPowerReq", "smMethod": "DouroInjectSoulPowerResp"}, "209402": {"name": "S_DOURO_GET_SCORE_REWARD", "cmMethod": "GetDouroScoreRewardReq", "smMethod": "GetDouroScoreRewardResp"}, "209404": {"name": "S_DOURO_OPEN_TASK", "cmMethod": "GetDouroItemGainNumReq", "smMethod": "GetDouroItemGainNumResp"}, "2013401": {"name": "S_FANREN_USE_SWORD_POWER", "cmMethod": "FanrenInjectPowerReq", "smMethod": "FanrenInjectPowerResp_Test"}, "200023": {"name": "S_PLAYER_FORBIDDEN_TRAILS_GET_REWARD", "cmMethod": "ForbiddenTrailsGetRewardReq", "smMethod": "ForbiddenTrailsGetRewardResp"}, "206901": {"name": "S_STARTRIAL_DATA_SYNC", "smMethod": "StarTrialDataMsg"}, "206902": {"name": "S_STARTRIAL_Fight", "cmMethod": "StarTrialChallengeReq", "smMethod": "StarTrialChallengeResp"}, "206903": {"name": "S_STARTRIAL_GetRecord", "cmMethod": "StarTrialRecordReq", "smMethod": "StarTrialRecordResp"}, "206904": {"name": "S_STARTRIAL_BattleReply", "cmMethod": "StarTrialBattleReplyReq", "smMethod": "StarTrialBattleReplyResp"}, "206905": {"name": "S_STARTRIAL_CodexEnter", "cmMethod": "EnterStarTrialCodexMsgReq", "smMethod": "EnterStarTrialCodexMsgResp"}, "206906": {"name": "S_STARTRIAL_GetCodexMsg", "cmMethod": "PlayerStarTrialCodexMsgReq", "smMethod": "RspPlayerStarTrialCodexMsg"}, "206907": {"name": "S_STARTRIAL_GetBossAttrMsg", "cmMethod": "GetBossDetailDataReq", "smMethod": "GetBossDetailDataResp"}, "206908": {"name": "S_STARTRIAL_GetDailyFightReward", "cmMethod": "GetDailyFightRewardReq", "smMethod": "GetDailyFightRewardResp"}, "206909": {"name": "S_STARTRIAL_GetGroupInfo", "cmMethod": "GetStarTrialGroupInfoReq", "smMethod": "GetStarTrialGroupInfoResp"}, "206910": {"name": "S_STARTRIAL_GetPlayerDetailInfo", "cmMethod": "StarTrialGetPlayerDetailReq", "smMethod": "StarTrialGetPlayerDetailResp"}, "206911": {"name": "S_STARTRIAL_EnterMainPanel", "cmMethod": "StarTrialEnterMainPanelReq", "smMethod": "StarTrialEnterMainPanelResp"}, "4600": {"name": "S_HOLD_PET_EGG_DATA_SYNC", "smMethod": "HoldPetEggDataSync"}, "4601": {"name": "U_HOLD_SPECIAL_ITEM_DATA_SYNC", "smMethod": "HoldNestedBoxDataSync"}, "20020": {"name": "S_PIG_ESCAPE_GET_REWARD", "cmMethod": "CutRopeGetRewardReq", "smMethod": "CutRopeGetRewardRsp"}, "27101": {"name": "S_FAIRY_RABBIT_DRAW_REQ", "cmMethod": "FairyRabbitDrawReq", "smMethod": "FairyRabbitDrawResp"}, "27102": {"name": "S_FAIRY_RABBIT_SELECT_BIG_REWARD_REQ", "cmMethod": "FairyRabbitSelectBigRewardReq", "smMethod": "FairyRabbitSelectBigRewardResp"}, "210901": {"name": "S_WEST_TRAVEL_PASS_GAME", "cmMethod": "WestTravelPassGameReq", "smMethod": "WestTravelPassGameResp"}, "207201": {"name": "S_RECEIVE_SDK_REWARD_SYN", "smMethod": "ReceiveSdkRewardSyn"}, "207202": {"name": "S_RECEIVE_SDK_REWARD", "cmMethod": "ReceiveSdkRewardReq", "smMethod": "ReceiveSdkRewardRsp"}, "207203": {"name": "S_QQCARD_GET_REWARD", "cmMethod": "QQCardGetRewardReq", "smMethod": "QQCardGetRewardRsp"}, "207204": {"name": "S_ALIPAY_SET_START_PARAM", "cmMethod": "AlipayStartParamReq", "smMethod": "AlipayStartParamRsp"}, "207205": {"name": "S_RECEIVE_SDK_DAILY_REWARD", "cmMethod": "ReceiveSdkDailyRewardReq", "smMethod": "ReceiveSdkDailyRewardRsp"}, "207206": {"name": "S_MEITUAN_SET_START_PARAM", "cmMethod": "MeiTuanStartParamReq", "smMethod": "MeiTuanStartParamRsp"}, "215201": {"name": "S_ADGIFTPB_TRIGGER", "cmMethod": "ADGiftTriggerReq", "smMethod": "ADGiftTriggerResp"}, "215202": {"name": "S_ADGIFTPB_GET_REWARD", "cmMethod": "ADGiftGetRewardReq", "smMethod": "ADGiftGetRewardResp"}, "200021": {"name": "S_MINI_GAMES_REWARD", "cmMethod": "ReceiveMiniGamesRewardReq", "smMethod": "ReceiveMiniGamesRewardResp"}, "200022": {"name": "S_MINI_GAMES_STAGE_MAP_CHALLENGE", "cmMethod": "StageMapChallengeReq", "smMethod": "StageMapChallengeRsp"}, "200026": {"name": "S_MINI_GAMES_GET_REWARD", "cmMethod": "GetWestJourneyRewardReq", "smMethod": "GetWestJourneyRewardResp"}, "200024": {"name": "S_MINI_GAME_ENTER", "cmMethod": "EnterWestJourneyReq", "smMethod": "EnterWestJourneyResp"}, "200025": {"name": "S_MINI_GAME_LEVEL_DATA_SYNC", "smMethod": "MiniGamesWestJourneyData"}, "208401": {"name": "S_SKY_WAR_ENTER", "cmMethod": "SkyWarEnterReq", "smMethod": "SkyWarEnterRsp"}, "208402": {"name": "S_SKY_WAR_REFRESH_ENEMY", "cmMethod": "SkyWarRefreshEnemyReq", "smMethod": "SkyWarRefreshEnemyRsp"}, "208403": {"name": "S_SKY_WAR_FIGHT", "cmMethod": "SkyWarFightReq", "smMethod": "SkyWarFightRsp"}, "208404": {"name": "S_SKY_WAR_RANK", "cmMethod": "SkyWarRankReq", "smMethod": "SkyWarRankRsp"}, "208405": {"name": "S_SKY_WAR_LOG", "cmMethod": "SkyWarLogReq", "smMethod": "SkyWarLogRsp"}, "208406": {"name": "S_SKY_WAR_PLAY_BACK", "cmMethod": "SkyWarLogPlaybackReq", "smMethod": "SkyWarLogPlaybackRsp"}, "208407": {"name": "S_SKY_WAR_FORMATION", "cmMethod": "SkyWarFormationReq", "smMethod": "SkyWarFormationRsp"}, "208408": {"name": "S_SKY_WAR_SET_ORDER", "cmMethod": "SkyWarSetOrderReq", "smMethod": "SkyWarSetOrderRsp"}, "208409": {"name": "S_SKY_WAR_SKY_RANK", "cmMethod": "SkyWarSkyRankReq", "smMethod": "SkyWarSkyRankRsp"}, "208410": {"name": "S_SKY_WAR_WORSHIP", "cmMethod": "SkyWarWorshipReq", "smMethod": "SkyWarWorshipRsp"}, "208411": {"name": "S_SKY_WAR_BUY_TIMES", "cmMethod": "SkyWarBuyTimesReq", "smMethod": "SkyWarBuyTimesRsp"}, "208413": {"name": "S_SKY_WAR_DATA_SYNC", "smMethod": "SkyWarDataSync"}, "208414": {"name": "S_SKY_WAR_DATA_LOGIN_SYNC", "smMethod": "SkyWarDataLoginSync"}, "209005": {"name": "S_WORLD_RULE_PLAYER_DATA_SYNC", "smMethod": "WorldRulePlayerDataMsg"}, "209001": {"name": "S_WORLD_RULE_PERCEPTION_REQ", "cmMethod": "WorldRulePerceptionReq", "smMethod": "WorldRulePerceptionResp"}, "209002": {"name": "S_WORLD_RULE_PERCEPTION_REPLACE_REQ", "cmMethod": "WorldRulePerceptionReplaceReq", "smMethod": "WorldRulePerceptionReplaceResp"}, "209003": {"name": "S_WORLD_RULE_GET_PERCEPTION_LOG_REQ", "cmMethod": "WorldRuleGetPerceptionLogReq", "smMethod": "WorldRuleGetPerceptionLogResp"}, "209004": {"name": "S_WORLD_RULE_SWITCH_PROGRAMME_REQ", "cmMethod": "WorldRuleSwitchProgrammeReq", "smMethod": "WorldRuleSwitchProgrammeResp"}, "209750": {"name": "S_MOUNTAIN_SEA_ENTER_BATTLE", "cmMethod": "MountainSeaEnterBattleReq", "smMethod": "MountainSeaEnterBattleResp"}, "209752": {"name": "S_MOUNTAIN_SEA_ENTER_SWITCH_SEPARATION", "cmMethod": "MountainSeaEnterSwitchSeparationReq", "smMethod": "MountainSeaEnterSwitchSeparationRsp"}, "209753": {"name": "S_MOUNTAIN_SEA_SWITCH_SEPARATION", "cmMethod": "MountainSeaSwitchSeparationReq", "smMethod": "MountainSeaSwitchSeparationRsp"}, "209754": {"name": "S_MOUNTAIN_SEA_SEPARATION_DETAIL", "cmMethod": "MountainSeaSeparationDetailReq", "smMethod": "MountainSeaSeparationDetailRsp"}, "209755": {"name": "S_MOUNTAIN_SEA_CHANGE_POS", "cmMethod": "MountainSeaChangePosReq", "smMethod": "MountainSeaChangePosRsp"}, "209756": {"name": "S_MOUNTAIN_SEA_CHANGE_TEAM_SKILL", "cmMethod": "MountainSeaChangeTeamSkillReq", "smMethod": "MountainSeaChangeTeamSkillRsp"}, "209757": {"name": "S_MOUNTAIN_SEA_DO_BATTLE", "cmMethod": "MountainSeaDoBattleReq", "smMethod": "MountainSeaDoBattleRsp"}, "209770": {"name": "S_MOUNTAIN_SEA_ENTER_BATTLE_NOTIFY", "cmMethod": "", "smMethod": "MountainSeaEnterBattleNotify"}, "209771": {"name": "S_MOUNTAIN_SEA_SWITCH_SEPARATION_NOTIFY", "cmMethod": "", "smMethod": "MountainSeaSwitchSeparationNotify"}, "209772": {"name": "S_MOUNTAIN_SEA_CHANGE_TEAM_SKILL_NOTIFY", "cmMethod": "", "smMethod": "MountainSeaChangeTeamSkillNotify"}, "209773": {"name": "S_MOUNTAIN_SEA_DO_BATTLE_NOTIFY", "cmMethod": "", "smMethod": "MountainSeaDoBattleNotify"}, "209774": {"name": "S_MOUNTAIN_SEA_CHANGE_POS_NOTIFY", "cmMethod": "", "smMethod": "MountainSeaChangePosNotify"}, "209783": {"name": "S_MOUNTAIN_SEA_INVITE_LIST", "cmMethod": "MountainSeaInviteListReq", "smMethod": "MountainSeaInviteListResp"}, "209784": {"name": "S_MOUNTAIN_SEA_RED_INFO", "cmMethod": "", "smMethod": "MountainSeaRedPointResp"}, "209785": {"name": "S_MOUNTAIN_SEA_INVITE_REFUSE", "cmMethod": "MountainSeaInviteRefuseReq", "smMethod": "MountainSeaInviteRefuseResp"}, "209786": {"name": "S_MOUNTAIN_SEA_SEARCH_SET_APPOINT", "cmMethod": "MountainSeaSetAppointReq", "smMethod": "MountainSeaSetAppointResp"}, "209787": {"name": "S_MOUNTAIN_SEA_INVITE_JOIN_TEAM", "cmMethod": "MountainSeaApplyJoinTeamReq", "smMethod": "MountainSeaApplyJoinTeamRsp"}, "208601": {"name": "S_SYNC_MARK_STATE_MSG", "smMethod": "SyncMarkStateMsg"}, "208602": {"name": "S_MARK_FINISH", "cmMethod": "MarkFinishReq", "smMethod": "MarkFinishResp"}, "209701": {"name": "S_MOUNTAIN_SEA_ENTER", "cmMethod": "EnterMountainSeaReq", "smMethod": "EnterMountainSeaRsp"}, "209702": {"name": "S_MOUNTAIN_SEA_ENTER_TEAM", "cmMethod": "EnterMountainSeaTeamReq", "smMethod": "EnterMountainSeaTeamRsp"}, "209703": {"name": "S_MOUNTAIN_SEA_TEAM_START", "cmMethod": "MountainSeaTeamStartReq", "smMethod": "MountainSeaTeamStartRsp"}, "209704": {"name": "S_MOUNTAIN_SEA_CREATE_TEAM", "cmMethod": "MountainSeaCreateTeamReq", "smMethod": "MountainSeaCreateTeamRsp"}, "209705": {"name": "S_MOUNTAIN_SEA_GET_TEAM_LIST", "cmMethod": "MountainSeaGetTeamListReq", "smMethod": "MountainSeaGetTeamListRsp"}, "209706": {"name": "S_MOUNTAIN_SEA_GET_TEAM_INFO", "cmMethod": "MountainSeaGetTeamInfoReq", "smMethod": "MountainSeaGetTeamInfoRsp"}, "209707": {"name": "S_MOUNTAIN_SEA_JOIN_TEAM", "cmMethod": "MountainSeaJoinTeamReq", "smMethod": "MountainSeaJoinTeamRsp"}, "209708": {"name": "S_MOUNTAIN_SEA_CANCEL_TEAM_APPLY", "cmMethod": "MountainSeaCancelTeamApplyReq", "smMethod": "MountainSeaCancelTeamApplyRsp"}, "209709": {"name": "S_MOUNTAIN_SEA_APPLY_JOIN_TEAM_AGREE", "cmMethod": "MountainSeaApplyJoinTeamAgreeReq", "smMethod": "MountainSeaApplyJoinTeamAgreeRsp"}, "209710": {"name": "S_MOUNTAIN_SEA_APPLY_JOIN_TEAM_REFUSED", "cmMethod": "MountainSeaApplyJoinTeamRefusedReq", "smMethod": "MountainSeaApplyJoinTeamRefusedRsp"}, "209711": {"name": "S_MOUNTAIN_SEA_QUIT_TEAM", "cmMethod": "MountainSeaQuitTeamReq", "smMethod": "MountainSeaQuitTeamRsp"}, "209712": {"name": "S_MOUNTAIN_SEA_KICK_OUT_TEAM", "cmMethod": "MountainSeaKickOutTeamReq", "smMethod": "MountainSeaKickOutTeamRsp"}, "209713": {"name": "S_MOUNTAIN_SEA_CHANGE_LEADER", "cmMethod": "MountainSeaChangeLeaderReq", "smMethod": "MountainSeaChangeLeaderRsp"}, "209714": {"name": "S_MOUNTAIN_SEA_TEAM_PREPARE", "cmMethod": "MountainSeaTeamPrepareReq", "smMethod": "MountainSeaTeamPrepareRsp"}, "209715": {"name": "S_MOUNTAIN_SEA_MATCH_MEMBER", "cmMethod": "MountainSeaMatchMemberReq", "smMethod": "MountainSeaMatchMemberRsp"}, "209716": {"name": "S_MOUNTAIN_SEA_START_BATTLE", "cmMethod": "MountainSeaStartBattleReq", "smMethod": "MountainSeaStartBattleRsp"}, "209717": {"name": "S_MOUNTAIN_SEA_RANK_PLAYER_INFO", "cmMethod": "MountainSeaGetPlayerInfoReq", "smMethod": "MountainSeaGetPlayerInfoRsp"}, "209718": {"name": "S_MOUNTAIN_SEA_WORSHIP", "cmMethod": "MountainSeaWorshipReq", "smMethod": "MountainSeaWorshipRsp"}, "209719": {"name": "S_MOUNTAIN_SEA_GET_BATTLE_REPLAY", "cmMethod": "MountainSeaGetBattleVideoReq", "smMethod": "MountainSeaGetBattleVideoRsp"}, "209720": {"name": "S_MOUNTAIN_SEA_GET_BOSS_INFO", "cmMethod": "MountainSeaGetBossInfoReq", "smMethod": "MountainSeaGetBossInfoRsp"}, "209721": {"name": "S_MOUNTAIN_SEA_CHALLENGE_TIME", "cmMethod": "MountainSeaChallengeTimeReq", "smMethod": "MountainSeaChallengeTimeRsp"}, "209722": {"name": "S_MOUNTAIN_SEA_START_MATCH", "cmMethod": "MountainSeaStartMatchReq", "smMethod": "MountainSeaStartMatchRsp"}, "209723": {"name": "S_MOUNTAIN_SEA_INVITE", "cmMethod": "MountainSeaInviteReq", "smMethod": "MountainSeaInviteRsp"}, "209724": {"name": "S_MOUNTAIN_SEA_LEAVE", "cmMethod": "LeaveMountainSeaReq", "smMethod": "LeaveMountainSeaRsp"}, "209725": {"name": "S_MOUNTAIN_SEA_GET_BOSS_POWER", "cmMethod": "MountainSeaGetBossPowerReq", "smMethod": "MountainSeaGetBossPowerRsp"}, "209731": {"name": "S_MOUNTAIN_SEA_TEAM_LEADER_NOTIFY_SYNC", "cmMethod": "", "smMethod": "MountainSeaTeamLeaderNotify"}, "209732": {"name": "S_MOUNTAIN_SEA_TEAM_MEMBER_NOTIFY_SYNC", "cmMethod": "", "smMethod": "MountainSeaTeamMemberNotify"}, "216001": {"name": "S_PLANES_TRIAL_ENTER", "cmMethod": "EnterPlanesTrialReq", "smMethod": "EnterPlanesTrialRsp"}, "216002": {"name": "S_PLANES_TRIAL_ENTER_TEAM", "cmMethod": "EnterPlanesTrialTeamReq", "smMethod": "EnterPlanesTrialTeamRsp"}, "216003": {"name": "S_PLANES_TRIAL_TEAM_START", "cmMethod": "PlanesTrialTeamStartReq", "smMethod": "PlanesTrialTeamStartRsp"}, "216004": {"name": "S_PLANES_TRIAL_CREATE_TEAM", "cmMethod": "PlanesTrialCreateTeamReq", "smMethod": "PlanesTrialCreateTeamRsp"}, "216005": {"name": "S_PLANES_TRIAL_GET_TEAM_LIST", "cmMethod": "PlanesTrialGetTeamListReq", "smMethod": "PlanesTrialGetTeamListRsp"}, "216006": {"name": "S_PLANES_TRIAL_GET_TEAM_INFO", "cmMethod": "PlanesTrialGetTeamInfoReq", "smMethod": "PlanesTrialGetTeamInfoRsp"}, "216007": {"name": "S_PLANES_TRIAL_JOIN_TEAM", "cmMethod": "PlanesTrialJoinTeamReq", "smMethod": "PlanesTrialJoinTeamRsp"}, "216008": {"name": "S_PLANES_TRIAL_CANCEL_TEAM_APPLY", "cmMethod": "PlanesTrialCancelTeamApplyReq", "smMethod": "PlanesTrialCancelTeamApplyRsp"}, "216009": {"name": "S_PLANES_TRIAL_APPLY_JOIN_TEAM_AGREE", "cmMethod": "PlanesTrialApplyJoinTeamAgreeReq", "smMethod": "PlanesTrialApplyJoinTeamAgreeRsp"}, "216010": {"name": "S_PLANES_TRIAL_APPLY_JOIN_TEAM_REFUSED", "cmMethod": "PlanesTrialApplyJoinTeamRefusedReq", "smMethod": "PlanesTrialApplyJoinTeamRefusedRsp"}, "216011": {"name": "S_PLANES_TRIAL_QUIT_TEAM", "cmMethod": "PlanesTrialQuitTeamReq", "smMethod": "PlanesTrialQuitTeamRsp"}, "216012": {"name": "S_PLANES_TRIAL_KICK_OUT_TEAM", "cmMethod": "PlanesTrialKickOutTeamReq", "smMethod": "PlanesTrialKickOutTeamRsp"}, "216013": {"name": "S_PLANES_TRIAL_CHANGE_LEADER", "cmMethod": "PlanesTrialChangeLeaderReq", "smMethod": "PlanesTrialChangeLeaderRsp"}, "216014": {"name": "S_PLANES_TRIAL_TEAM_PREPARE", "cmMethod": "PlanesTrialTeamPrepareReq", "smMethod": "PlanesTrialTeamPrepareRsp"}, "216015": {"name": "S_PLANES_TRIAL_MATCH_MEMBER", "cmMethod": "PlanesTrialMatchMemberReq", "smMethod": "PlanesTrialMatchMemberRsp"}, "216016": {"name": "S_PLANES_TRIAL_START_BATTLE", "cmMethod": "PlanesTrialStartBattleReq", "smMethod": "PlanesTrialStartBattleRsp"}, "216017": {"name": "S_PLANES_TRIAL_RANK_PLAYER_INFO", "cmMethod": "PlanesTrialGetPlayerInfoReq", "smMethod": "PlanesTrialGetPlayerInfoRsp"}, "216019": {"name": "S_PLANES_TRIAL_GET_BATTLE_REPLAY", "cmMethod": "PlanesTrialGetBattleVideoReq", "smMethod": "PlanesTrialGetBattleVideoRsp"}, "216020": {"name": "S_PLANES_TRIAL_GET_BOSS_INFO", "cmMethod": "PlanesTrialGetBossInfoReq", "smMethod": "PlanesTrialGetBossInfoRsp"}, "216021": {"name": "S_PLANES_TRIAL_CHALLENGE_TIME", "cmMethod": "PlanesTrialChallengeTimeReq", "smMethod": "PlanesTrialChallengeTimeRsp"}, "216022": {"name": "S_PLANES_TRIAL_START_MATCH", "cmMethod": "PlanesTrialStartMatchReq", "smMethod": "PlanesTrialStartMatchRsp"}, "216023": {"name": "S_PLANES_TRIAL_INVITE", "cmMethod": "PlanesTrialInviteReq", "smMethod": "PlanesTrialInviteRsp"}, "216024": {"name": "S_PLANES_TRIAL_LEAVE", "cmMethod": "LeavePlanesTrialReq", "smMethod": "LeavePlanesTrialRsp"}, "216025": {"name": "S_PLANES_TRIAL_GET_BOSS_POWER", "cmMethod": "PlanesTrialGetBossPowerReq", "smMethod": "PlanesTrialGetBossPowerRsp"}, "216026": {"name": "S_PLANES_TRIAL_LEADER_JUMP_ANIM", "cmMethod": "PlanesTrialSkipBattleReq", "smMethod": "PlanesTrialSkipBattleRsp"}, "216027": {"name": "S_PLANES_TRIAL_LEADER_REQ_SELECT_BUFF", "cmMethod": "PlanesTrialStartSelectBuffReq", "smMethod": "PlanesTrialStartSelectBuffRsp"}, "216028": {"name": "S_PLANES_TRIAL_TEAM_SELECT_BUFF", "cmMethod": "PlanesTrialSelectBuffReq", "smMethod": "PlanesTrialSelectBuffRsp"}, "216029": {"name": "S_PLANES_TRIAL_SET_BUFF_PRE", "cmMethod": "PlanesTrialSetBuffPreferenceReq", "smMethod": "PlanesTrialSetBuffPreferenceRsp"}, "216030": {"name": "S_PLANES_TRIAL_GET_SELECTED_BUFF", "cmMethod": "PlanesTrialGetSelectedBuffReq", "smMethod": "PlanesTrialGetSelectedBuffRsp"}, "216031": {"name": "S_PLANES_TRIAL_GET_ACHIEVEMENT_REWARD", "cmMethod": "PlanesTrialGetAchievementRewardReq", "smMethod": "PlanesTrialGetAchievementRewardRsp"}, "216033": {"name": "S_PLANES_TRIAL_GET_BUFF_PRE", "cmMethod": "PlanesTrialGetBuffPreferenceReq", "smMethod": "PlanesTrialGetBuffPreferenceRsp"}, "216040": {"name": "S_PLANES_TRIAL_TEAM_LEADER_NOTIFY_SYNC", "cmMethod": "", "smMethod": "PlanesTrialTeamLeaderNotify"}, "216041": {"name": "S_PLANES_TRIAL_TEAM_MEMBER_NOTIFY_SYNC", "cmMethod": "", "smMethod": "PlanesTrialTeamMemberNotify"}, "216053": {"name": "S_PLANES_TRIAL_ENTER_BATTLE", "cmMethod": "PlanesTrialEnterBattleReq", "smMethod": "PlanesTrialEnterBattleResp"}, "216054": {"name": "S_PLANES_TRIAL_ENTER_SWITCH_SEPARATION", "cmMethod": "PlanesTrialEnterSwitchSeparationReq", "smMethod": "PlanesTrialEnterSwitchSeparationRsp"}, "216055": {"name": "S_PLANES_TRIAL_SWITCH_SEPARATION", "cmMethod": "PlanesTrialSwitchSeparationReq", "smMethod": "PlanesTrialSwitchSeparationRsp"}, "216056": {"name": "S_PLANES_TRIAL_SEPARATION_DETAIL", "cmMethod": "PlanesTrialSeparationDetailReq", "smMethod": "PlanesTrialSeparationDetailRsp"}, "216057": {"name": "S_PLANES_TRIAL_CHANGE_POS", "cmMethod": "PlanesTrialChangePosReq", "smMethod": "PlanesTrialChangePosRsp"}, "216058": {"name": "S_PLANES_TRIAL_DO_BATTLE", "cmMethod": "PlanesTrialDoBattleReq", "smMethod": "PlanesTrialDoBattleRsp"}, "216094": {"name": "S_PLANES_TRIAL_VIDEO_INFO", "cmMethod": "PlanesTrialVideoInfoReq", "smMethod": "PlanesTrialVideoInfoRsp"}, "216095": {"name": "S_PLANES_TRIAL_PLAY_VIDEO", "cmMethod": "PlanesTrialPlayVideoReq", "smMethod": "PlanesTrialPlayVideoRsp"}, "216096": {"name": "S_PLANES_TRIAL_CLOSE_RESULE", "cmMethod": "PlanesTrialCloseSettleScreenReq", "smMethod": "PlanesTrialCloseSettleScreenResp"}, "216070": {"name": "S_PLANES_TRIAL_ENTER_BATTLE_NOTIFY", "cmMethod": "", "smMethod": "PlanesTrialEnterBattleNotify"}, "216071": {"name": "S_PLANES_TRIAL_SWITCH_SEPARATION_NOTIFY", "cmMethod": "", "smMethod": "PlanesTrialSwitchSeparationNotify"}, "216065": {"name": "S_PLANES_TRIAL_DO_BATTLE_NOTIFY", "cmMethod": "", "smMethod": "PlanesTrialDoBattleNotify"}, "216074": {"name": "S_PLANES_TRIAL_CHANGE_POS_NOTIFY", "cmMethod": "", "smMethod": "PlanesTrialChangePosNotify"}, "216089": {"name": "S_PLANES_TRIAL_GET_TRIALmEMBER_COUNT_REQ", "cmMethod": "getPlanesTrialTrialMemberCountReq", "smMethod": "getPlanesTrialTrialMemberCountResp"}, "216088": {"name": "S_PLANES_TRIAL_GET_PLAYER_RESTRAIN_INFO_MSG_REQ", "cmMethod": "PlayerRestrainInfoMsgReq", "smMethod": "PlayerRestrainInfoMsgResp"}, "216090": {"name": "S_PLANES_TRIAL_GET_RANK", "cmMethod": "PlanesTrialRankGetReq", "smMethod": "PlanesTrialRankGetResp"}, "216091": {"name": "S_PLANES_TRIAL_HEARTBEAT", "cmMethod": "PlanesTrialHeartbeatReq", "smMethod": "PlanesTrialHeartbeatResp"}, "216092": {"name": "S_PLANES_TRIAL_GET_SELECT_REWARD_DETAIL_REQ", "cmMethod": "PlanesTrialGetSelectRewardDetailReq", "smMethod": "PlanesTrialGetSelectRewardDetailResp"}, "216093": {"name": "S_PLANES_TRIAL_SELECT_REWARD_REQ", "cmMethod": "PlanesTrialSelectRewardReq", "smMethod": "PlanesTrialSelectRewardResp"}, "216034": {"name": "S_PLANES_TRIAL_GET_GOD_BODY_DETAIL_DATA_MSG", "cmMethod": "PlanesTrialGetGodBodyDataReq", "smMethod": "PlanesTrialGetGodBodyDataResp"}, "216072": {"name": "S_PLANES_TRIAL_GET_TRIAL_UPDATE_LOCK_DATA", "cmMethod": "PlanesTrialUpdateLockDataReq", "smMethod": "PlanesTrialUpdateLockDataResp"}, "216035": {"name": "S_PLANES_TRIAL_GET_GRAND_PRIZE_INFO", "cmMethod": "PlanesTrialGetGrandPrizeInfoReq", "smMethod": "PlanesTrialGetGrandPrizeInfoResp"}, "216036": {"name": "S_PLANES_TRIAL_RECEIVE_GRAND_PRIZE", "cmMethod": "PlanesTrialReceiveGrandPrizeReq", "smMethod": "PlanesTrialReceiveGrandPrizeResp"}, "209501": {"name": "S_HOLY_LAND_BASE_INFO_LOAD", "cmMethod": "HolyLandBaseInfoReqMsg", "smMethod": "HolyLandBaseInfoRespMsg"}, "209502": {"name": "S_HOLY_LAND_GROUP", "cmMethod": "HolyLandGroupReqMsg", "smMethod": "HolyLandGroupRespMsg"}, "209503": {"name": "S_HOLY_LAND_UNION_GET_MEMBER_LIST", "cmMethod": "HolyLandGetJoinMemberListReq", "smMethod": "HolyLandGetJoinMemberListResp"}, "209504": {"name": "S_HOLY_LAND_BATTLE_REPORT", "cmMethod": "HolyLandBattleReportReqMsg", "smMethod": "HolyLandBattleReportRespMsg"}, "209507": {"name": "S_HOLY_LAND_UNION_GROUP_POINT_INFO", "cmMethod": "HolyLandUnionGroupDamageReqMsg", "smMethod": "HolyLandUnionGroupDamageRespMsg"}, "209508": {"name": "S_HOLY_LAND_PLAYER_UNION_RANK_REWARD", "cmMethod": "HolyLandPlayerUnionRankRewardReqMsg", "smMethod": "HolyLandPlayerUnionRankRewardRespMsg"}, "209509": {"name": "S_HOLY_LAND_GET_PLAYER_UNION_RANK_REWARD", "cmMethod": "HolyLandGetPlayerUnionRankRewardReqMsg", "smMethod": "HolyLandGetPlayerUnionRankRewardRespMsg"}, "209510": {"name": "S_HOLY_LAND_RED_DOT_DATA", "cmMethod": "HolyLandRedDotReqMsg", "smMethod": "HolyLandRedDotRespMsg"}, "209511": {"name": "S_HOLY_LAND_WORSHIP", "cmMethod": "HolyLandWorshipReq", "smMethod": "HolyLandWorshipResp"}, "209512": {"name": "S_HOLY_LAND_SET_SEPARATION", "cmMethod": "HolyLandSetSeparationReq", "smMethod": "HolyLandSetSeparationResp"}, "209513": {"name": "S_HOLY_LAND_GUESS_INFO", "cmMethod": "HolyLandGetGuessInfoReq", "smMethod": "HolyLandGetGuessInfoResp"}, "209514": {"name": "S_HOLY_LAND_SET_GUESS", "cmMethod": "HolyLandSetGuessReq", "smMethod": "HolyLandSetGuessResp"}, "209515": {"name": "S_HOLY_LAND_CHAMPION_UNIONS", "cmMethod": "HolyLandAllChampionUnionsReq", "smMethod": "HolyLandAllChampionUnionsResp"}, "209516": {"name": "S_HOLY_LAND_CHAMPION_MEMBERS", "cmMethod": "HolyLandChampionMemberInfoReq", "smMethod": "HolyLandChampionMemberInfoResp"}, "209517": {"name": "S_HOLY_LAND_UNION_RANK_LIST", "cmMethod": "HolyLandUnionRankListReqMsg", "smMethod": "HolyLandUnionRankListRespMsg"}, "209518": {"name": "S_HOLY_LAND_GET_SEPARATION", "cmMethod": "HolyLandGetSeparationDetailReq", "smMethod": "HolyLandGetSeparationDetailRsp"}, "209519": {"name": "S_HOLY_LAND_ALL_JOIN_SERVER", "cmMethod": "HolyLandAllJoinServerReq", "smMethod": "HolyLandAllJoinServerResp"}, "209536": {"name": "S_HOLY_LAND_RECENLTY_CHAMPION", "cmMethod": "HolyLandGetNowChampionReq", "smMethod": "HolyLandGetNowChampionResp"}, "209538": {"name": "S_HOLY_LAND_GET_GUESS_COIN", "cmMethod": "HolyLandGameGetGuessCoinReq", "smMethod": "HolyLandGameGetGuessCoinRsp"}, "209520": {"name": "S_HOLY_LAND_GAME_HEART_BEAT", "cmMethod": "HolyLandHeartBeatReqMsg", "smMethod": "HolyLandHeartBeatRespMsg"}, "209521": {"name": "S_HOLY_LAND_GAME_INFO_LOAD", "cmMethod": "HolyLandGameInfoReqMsg", "smMethod": "HolyLandGameInfoRespMsg"}, "209522": {"name": "S_HOLY_LAND_GAME_MAP_CITY_INFO", "cmMethod": "HolyLandGameCityInfoReqMsg", "smMethod": "HolyLandGameCityInfoRespMsg"}, "209523": {"name": "S_HOLY_LAND_GAME_TARGET_CITY_INFO", "cmMethod": "HolyLandGameTargetCityInfoReqMsg", "smMethod": "HolyLandGameTargetCityInfoRespMsg"}, "209524": {"name": "S_HOLY_LAND_GAME_TARGET_CITY_LINE_INFO", "cmMethod": "HolyLandGameTargetCityLineInfoReqMsg", "smMethod": "HolyLandGameTargetCityLineInfoRespMsg"}, "209525": {"name": "S_HOLY_LAND_GAME_MOVE", "cmMethod": "HolyLandGameMoveReqMsg", "smMethod": "HolyLandGameMoveRespMsg"}, "209526": {"name": "S_HOLY_LAND_GAME_MINIMAP_INFO", "cmMethod": "HolyLandGameMiniMapInfoReqMsg", "smMethod": "HolyLandGameMiniMapInfoRespMsg"}, "209527": {"name": "S_HOLY_LAND_GAME_STRATEGY_INFO", "cmMethod": "HolyLandGameStrategyInfoReqMsg", "smMethod": "HolyLandGameStrategyInfoRespMsg"}, "209528": {"name": "S_HOLY_LAND_GAME_COMMANDER_SET", "cmMethod": "HolyLandGameCommanderSetReqMsg", "smMethod": "HolyLandGameCommanderSetRespMsg"}, "209529": {"name": "S_HOLY_LAND_GAME_SET_UNION_TARGET", "cmMethod": "HolyLandGameSetUnionTargetReqMsg", "smMethod": "HolyLandGameSetUnionTargetRespMsg"}, "209530": {"name": "S_HOLY_LAND_GAME_FOR_HELP", "cmMethod": "HolyLandGameForHelpReqMsg", "smMethod": "HolyLandGameForHelpRespMsg"}, "209531": {"name": "S_HOLY_LAND_GAME_ACCELERATE_MOVE", "cmMethod": "HolyLandGameAcclerateMoveMsg", "smMethod": "HolyLandGameAcclerateMoveRespMsg"}, "209532": {"name": "S_HOLY_LAND_GAME_BACK_HOME", "cmMethod": "HolyLandGameBackHomeReq", "smMethod": "HolyLandGameBackHomeResp"}, "209533": {"name": "S_HOLY_LAND_GAME_BATTLE", "cmMethod": "HolyLandGameAtkReqMsg", "smMethod": "HolyLandGameAtkRespMsg"}, "209534": {"name": "S_HOLY_LAND_GAME_UNION_DETAIL", "cmMethod": "HolyLandGameUnionDetailReqMsg", "smMethod": "HolyLandGameUnionDetailRespMsg"}, "209535": {"name": "S_HOLY_LAND_GAME_WAR_SITUATION", "cmMethod": "HolyLandGameWarSituationReqMsg", "smMethod": "HolyLandGameWarSituationRespMsg"}, "209537": {"name": "S_HOLY_LAND_GAME_AUTO_ATTACK", "cmMethod": "HolyLandGameGameAutoAttackReqMsg", "smMethod": "HolyLandGameGameAutoAttackRespMsg"}, "209550": {"name": "S_H<PERSON>Y_LAND_GAME_CITY_CHANGE_SYNC", "smMethod": "HolyLandGameCityChangeSyncMsg"}, "209551": {"name": "S_HOLY_LAND_GAME_PLAYER_WIN_SYNC", "smMethod": "HolyLandGamePlayerWinSyncMsg"}, "209552": {"name": "S_HOLY_LAND_GAME_LINE_INFO_SYNC", "smMethod": "HolyLandGameLineInfoSyncMsg"}, "209553": {"name": "S_HOLY_LAND_GAME_MYGAMEINFO_SYNC", "smMethod": "HolyLandGameMyGameInfoSyncMsg"}, "209554": {"name": "S_HOLY_LAND_GAME_OCCUPYCITY_SYNC", "smMethod": "HolyLandGameOccupyCitySyncMsg"}, "209555": {"name": "S_HOLY_LAND_GAME_LEFT_OUT_SYNC", "smMethod": "HolyLandLeftOutSyncInfo"}, "209556": {"name": "S_HOLY_LAND_GAME_COMMANDER_CHANGE_SYNC", "smMethod": "HolyLandCommanderChangeSyncMsg"}, "209557": {"name": "S_HOLY_LAND_GAME_CITY_BATTLE_SYNC", "smMethod": "HolyLandGameCityBattleSyncMsg"}, "209558": {"name": "S_HOLY_LAND_GAME_BEEN_KILL_SYNC", "smMethod": "HolyLandBeenKillSyncMsg"}, "209559": {"name": "S_HOLY_LAND_GAME_ROUTE_INFO_SYNC", "smMethod": "HolyLandGameRouteInfoSyncMsg"}, "209560": {"name": "S_HOLY_LAND_GAME_AUTO_BATTLE_STOP_SYNC", "smMethod": "HolyLandGameAutoBattleStopSyncMsg"}, "209561": {"name": "S_HOLY_LAND_GAME_RED_DOT_SYNC", "smMethod": "HolyLandGameReddotSyncMsg"}, "209570": {"name": "S_HOLY_LAND_BATTLE_APPLY_DATA_SYNC", "cmMethod": "HolyLandBattleApplyDataSyncReq", "smMethod": "HolyLandBattleApplyDataSync"}, "209575": {"name": "S_HOLY_LAND_BATTLE_TIME_STAMPS", "smMethod": "HolyLandBattleTimeStampsDataSync"}, "209801": {"name": "S_SOUL_LIQUID_DATA_MSG", "smMethod": "PlayerSoulLiQuidDataMsg"}, "209802": {"name": "S_SOUL_LIQUID_ACTIVE", "cmMethod": "SoulLiquidActiveReq", "smMethod": "SoulLiquidActiveResp"}, "210001": {"name": "S_REGRESSION_ACTIVITY_SHARE", "cmMethod": "RegressionShareReq", "smMethod": "RegressionShareResp"}, "210002": {"name": "S_REGRESSION_ACTIVITY_LOTTERY", "cmMethod": "RegressionLotteryReq", "smMethod": "RegressionLotteryResp"}, "210003": {"name": "S_REGRESSION_ACTIVITY_BIND_LIST", "cmMethod": "GetRegressionPlayerDataReq", "smMethod": "GetRegressionPlayerDataResp"}, "210004": {"name": "S_REGRESSION_ACTIVITY_TASK_REWARD", "cmMethod": "GetRegressionReceiveRewardReq", "smMethod": "GetRegressionReceiveRewardResp"}, "210005": {"name": "S_REGRESSION_SAVE_SELECT_ITEM", "cmMethod": "RegressionSaveSelectItemReq", "smMethod": "RegressionSaveSelectItemResp"}, "210101": {"name": "S_RECALL_ACTIVITY_GETREWARD", "cmMethod": "GetRecallConditionRewardReq", "smMethod": "GetRecallConditionRewardResp"}, "210102": {"name": "S_RECALL_ACTIVITY_RECEIVEREWARD", "cmMethod": "GetRecallReceiveRewardReq", "smMethod": "GetRecallReceiveRewardResp"}, "210103": {"name": "S_RECALL_ACTIVITY_BINDRECALLCODE", "cmMethod": "RecallBindActivePlayerReq", "smMethod": "RecallBindActivePlayerResp"}, "210104": {"name": "S_RECALL_ACTIVITY_MONTHCARDFREE", "cmMethod": "RecallPrivilegeFreeReq", "smMethod": "RecallPrivilegeFreeResp"}, "210201": {"name": "S_RENEW_ACTIVITY_GETPLAYERLIST", "cmMethod": "GetRenewPlayerListReq", "smMethod": "GetRenewPlayerListResp"}, "210202": {"name": "S_RENEW_ACTIVITY_BINDPLAYER", "cmMethod": "RenewBindPlayerReq", "smMethod": "GetRenewBindPlayerResp"}, "210203": {"name": "S_RENEW_ACTIVITY_LIFECARDACTIVE", "cmMethod": "RenewLifeCardActiveReq", "smMethod": "RenewLifeCardActiveResp"}, "210204": {"name": "S_RENEW_ACTIVITY_SYNCPLAYERINFO", "cmMethod": "", "smMethod": "SyncRenewPlayerInfoMsg"}, "209601": {"name": "S_SIGN_FUND_REPAIR", "cmMethod": "ReqSignInFundRepair", "smMethod": "RspSignInFundRepair"}, "209901": {"name": "S_TREASURE_BOWL_SIGN", "cmMethod": "ReqGetConditionReward", "smMethod": "RspGetConditionReward"}, "209910": {"name": "S_SKY_PRESENT_DATA_SYNC", "smMethod": "SkyPresentDataSync"}, "210601": {"name": "S_UNION_BLESSING_SEND_GIFT", "cmMethod": "UnionBlessingSendGiftReq", "smMethod": "UnionBlessingSendGiftResp"}, "210602": {"name": "S_UNION_BLESSING_RECEIVE_GIFT", "cmMethod": "UnionBlessingRewardReqMsg", "smMethod": "UnionBlessingRewardRespMsg"}, "210603": {"name": "S_UNION_BLESSING_SYNC_GIFT", "cmMethod": "", "smMethod": "UnionBlessingGiftSyncList"}, "210604": {"name": "S_UNION_BLESSING_SYNC_BLESSING_COUNT", "cmMethod": "", "smMethod": "UnionBlessingCountSyncList"}, "210800": {"name": "S_DRAGON_HOME_ENTER_ACTIVITY", "cmMethod": "DragonHomeEnterActivityReq", "smMethod": "DragonHomeEnterActivityResp"}, "210802": {"name": "S_DRAGON_HOME_TEAM_LIST", "cmMethod": "DragonHomeTeamListReq", "smMethod": "DragonHomeTeamListResp"}, "210803": {"name": "S_DRAGON_HOME_TEAM_CREATE", "cmMethod": "DragonHomeTeamCreateReq", "smMethod": "DragonHomeTeamCreateResp"}, "210804": {"name": "S_DRAGON_HOME_TEAM_JOIN", "cmMethod": "DragonHomeTeamApplyReq", "smMethod": "DragonHomeTeamApplyResp"}, "210805": {"name": "S_DRAGON_HOME_TEAM_JOIN_AGREE", "cmMethod": "DragonHomeTeamAgreeJoinReq", "smMethod": "DragonHomeTeamAgreeJoinResp"}, "210806": {"name": "S_DRAGON_HOME_TEAM_LEAVE", "cmMethod": "DragonHomeLeaveTeamReq", "smMethod": "DragonHomeLeaveTeamResp"}, "210807": {"name": "S_DRAGON_HOME_TEAM_KICK_OUT", "cmMethod": "DragonHomeTeamKickOutReq", "smMethod": "DragonHomeTeamKickOutResp"}, "210808": {"name": "S_DRAGON_HOME_TEAM_DISSOLVE", "cmMethod": "DragonHomeDissolveTeamReq", "smMethod": "DragonHomeDissolveTeamResp"}, "210809": {"name": "S_DRAGON_HOME_TEAM_SEARCH", "cmMethod": "DragonHomeTeamSearchReq", "smMethod": "DragonHomeTeamSearchResp"}, "210810": {"name": "S_DRAGON_HOME_TEAM_DETAIL", "cmMethod": "DragonHomeDetailReq", "smMethod": "DragonHomeDetailResp"}, "210811": {"name": "S_DRAGON_HOME_TEAM_APPLYLIST", "cmMethod": "DragonHomeTeamApplyListReq", "smMethod": "DragonHomeTeamApplyListResp"}, "210812": {"name": "S_DRAGON_HOME_TEAM_CANCEL_APPLY", "cmMethod": "DragonHomeCancelApplyReq", "smMethod": "DragonHomeCancelApplyResp"}, "210813": {"name": "S_DRAGON_HOME_TEAM_CHANGE_NOTIFY", "cmMethod": "", "smMethod": "DragonHomeTeamChangeNotifyMsg"}, "210820": {"name": "S_DRAGON_HOME_EXPLORE_DATA", "cmMethod": "DragonHomeExploreDataReq", "smMethod": "DragonHomeExploreDataResp"}, "210821": {"name": "S_DRAGON_HOME_EXPLORE_MOVE", "cmMethod": "DragonHomeMoveReq", "smMethod": "DragonHomeMoveResp"}, "210823": {"name": "S_DRAGON_HOME_EXPLORE_GO_NEXT_FLOOR", "cmMethod": "DragonHomeGoNextFloorReq", "smMethod": "DragonHomeGoNextFloorResp"}, "210824": {"name": "S_DRAGON_HOME_EXPLORE", "cmMethod": "DragonHomeExploreReq", "smMethod": "DragonHomeExploreResp"}, "210830": {"name": "S_DRAGON_HOME_RECEIVE_REWARD", "cmMethod": "DragonHomeReceiveRewardReq", "smMethod": "DragonHomeReceiveRewardResp"}, "210840": {"name": "S_DRAGON_HOME_LOG_LIST", "cmMethod": "DragonHomeLogListReq", "smMethod": "DragonHomeLogListResp"}, "210841": {"name": "S_DRAGON_HOME_REPLENISH_STRENGTH", "cmMethod": "DragonHomeReplenishStrengthReq", "smMethod": "DragonHomeReplenishStrengthResp"}, "210842": {"name": "S_DRAGON_HOME_BREAK_FREE", "cmMethod": "DragonHomeBreakFreeReq", "smMethod": "DragonHomeBreakFreeResp"}, "210843": {"name": "S_DRAGON_HOME_USE_LIGHT", "cmMethod": "DragonHomeUseLightReq", "smMethod": "DragonHomeUseLightResp"}, "210844": {"name": "S_DRAGON_HOME_UNLOCK_AUTO", "cmMethod": "DragonHomeUnlockAutoHandleReq", "smMethod": "DragonHomeUnlockAutoHandleResp"}, "210845": {"name": "S_DRAGON_HOME_NOTIFY_GO_NEXT", "cmMethod": "", "smMethod": "DragonHomePlayerEnterExploreNotify"}, "210846": {"name": "S_DRAGON_HOME_NOTIFY_DO_MOVE", "cmMethod": "", "smMethod": "DragonHomeExploreDoMoveNotify"}, "210847": {"name": "S_DRAGON_HOME_NOTIFY_TRIGGER_EVENT", "cmMethod": "", "smMethod": "DragonHomeExploreTriggerEventNotify"}, "210848": {"name": "S_DRAGON_HOME_TEAM_LOG_LIST", "cmMethod": "DragonHomeTeamLogListReq", "smMethod": "DragonHomeTeamLogListResp"}, "210851": {"name": "S_DRAGON_HOME_RESCUE_TRAP", "cmMethod": "DragonHomeRescueTrapReq", "smMethod": "DragonHomeRescueTrapResp"}, "210852": {"name": "S_DRAGON_HOME_ASSIST_ATTACK_MONSTER", "cmMethod": "DragonHomeAssistAttackMonsterReq", "smMethod": "DragonHomeAssistAttackMonsterResp"}, "210853": {"name": "S_DRAGON_HOME_RECEIVE_ASSIST_REWARD", "cmMethod": "DragonHomeReceiveMonsterRewardReq", "smMethod": "DragonHomeReceiveMonsterRewardResp"}, "210854": {"name": "S_DRAGON_HOME_NOTIFY_BE_RESCUE_TRAP", "cmMethod": "", "smMethod": "DragonHomeNotifyPlayerRescuedMsg"}, "210855": {"name": "S_DRAGON_HOME_NOTIFY_MONSTER_DISAPPEAR", "cmMethod": "", "smMethod": "DragonHomeNotifyMonsterDisappearMsg"}, "210856": {"name": "S_DRAGON_HOME_NOTIFY_END_TRAP", "cmMethod": "", "smMethod": "DragonHomeNotifyPlayerEndTrap"}, "210857": {"name": "S_DRAGON_HOME_INVITE_PLAYER", "cmMethod": "DragonHomeInvitePlayerReq", "smMethod": "DragonHomeInvitePlayerResp"}, "210858": {"name": "S_DRAGON_HOME_MONSTER_ATTR", "cmMethod": "DragonHomeMonsterAttrReq", "smMethod": "DragonHomeMonsterAttrResp"}, "210859": {"name": "S_DRAGON_HOME_RED_POINT", "cmMethod": "DragonHomeRedPointReqMsg", "smMethod": "DragonHomeRedPointRespMsg"}, "210860": {"name": "S_DRAGON_HOME_REMOVE_ASSIST", "cmMethod": "DragonHomeRemoveAssistReq", "smMethod": "DragonHomeRemoveAssistResp"}, "210861": {"name": "S_DRAGON_HOME_BEST_TEAM_INFO", "cmMethod": "DragoHomeBestTeamReq", "smMethod": "DragonHomeBestTeamResp"}, "210862": {"name": "S_DRAGON_HOME_XIE_ZHU_NOTIFY", "cmMethod": "", "smMethod": "DragonHomeTeammateTrapNotify"}, "210501": {"name": "S_AUSPICIOUS_BLESS_GET_REWARD", "cmMethod": "AuspiciousBlessGetRewardReq", "smMethod": "AuspiciousBlessGetRewardResp"}, "210502": {"name": "S_AUSPICIOUS_BLESS_LOCK_RARE_REWARD", "cmMethod": "AuspiciousBlessLockRareRewardReq", "smMethod": "AuspiciousBlessLockRareRewardResp"}, "20802": {"name": "S_FIRST_RECHARGE_GET_REWARD", "cmMethod": "GetMallRewardReq", "smMethod": "GetMallRewardResp"}, "135651": {"name": "S_FESTIVAL_CELEBRATIONS_PLAYER_INFO_MSG", "smMethod": "FestivalCelebrationsPlayerInfoMsg"}, "211001": {"name": "S_FESTIVAL_CELEBRATIONS_USE_BANQUET_ITEM_REQ", "cmMethod": "FestivalCelebrationsUseBanquetItemReq", "smMethod": "FestivalCelebrationsUseBanquetItemRsp"}, "211002": {"name": "S_FESTIVAL_CELEBRATIONS_DRAW_BANQUET_REWARD_REQ", "cmMethod": "FestivalCelebrationsDrawBanquetRewardReq", "smMethod": "FestivalCelebrationsDrawBanquetRewardRsp"}, "211003": {"name": "S_FESTIVAL_CELEBRATIONS_GET_BANQUET_SCORE_DETAIL_REQ", "cmMethod": "FestivalCelebrationsGetBanquetScoreDetailReq", "smMethod": "FestivalCelebrationsGetBanquetScoreDetailRsp"}, "211004": {"name": "S_FESTIVAL_CELEBRATIONS_USE_LUCKY_FATE_ITEM_REQ", "cmMethod": "FestivalCelebrationsUseLuckyFateItemReq", "smMethod": "FestivalCelebrationsUseLuckyFateItemRsp"}, "211005": {"name": "S_FESTIVAL_CELEBRATIONS_GET_SIGN_REWARD_REQ", "cmMethod": "FestivalCelebrationsGetSignRewardReq", "smMethod": "FestivalCelebrationsGetSignRewardRsp"}, "211006": {"name": "S_FESTIVAL_CELEBRATIONS_COLLECT_SYNTHESIS_REQ", "cmMethod": "FestivalCelebrationsCollectSynthesisReq", "smMethod": "FestivalCelebrationsCollectSynthesisRsp"}, "211007": {"name": "S_FESTIVAL_CELEBRATIONS_COLLECT_FILL_REQ", "cmMethod": "FestivalCelebrationsCollectFillReq", "smMethod": "FestivalCelebrationsCollectFillRsp"}, "211008": {"name": "S_FESTIVAL_CELEBRATIONS_COLLECT_DRAW_BIG_REWARD_REQ", "cmMethod": "FestivalCelebrationsCollectDrawBigRewardReq", "smMethod": "FestivalCelebrationsCollectDrawBigRewardRsp"}, "211009": {"name": "S_FESTIVAL_CELEBRATIONS_COLLECT_GET_CLAIM_LIST_REQ", "cmMethod": "FestivalCelebrationsCollectGetClaimListReq", "smMethod": "FestivalCelebrationsCollectGetClaimListRsp"}, "211010": {"name": "S_FESTIVAL_CELEBRATIONS_COLLECT_SEARCH_PLAYER_REQ", "cmMethod": "FestivalCelebrationsCollectSearchPlayerReq", "smMethod": "FestivalCelebrationsCollectSearchPlayerRsp"}, "211011": {"name": "S_FESTIVAL_CELEBRATIONS_COLLECT_GIVE_REQ", "cmMethod": "FestivalCelebrationsCollectGiveReq", "smMethod": "FestivalCelebrationsCollectGiveRsp"}, "211012": {"name": "S_FESTIVAL_CELEBRATIONS_COLLECT_ASK_FOR_REQ", "cmMethod": "FestivalCelebrationsCollectAskForReq", "smMethod": "FestivalCelebrationsCollectAskForRsp"}, "211014": {"name": "S_FESTIVAL_CELEBRATIONS_COLLECT_GET_GIVEN_REQ", "cmMethod": "FestivalCelebrationsCollectGetGivenReq", "smMethod": "FestivalCelebrationsCollectGetGivenResp"}, "211013": {"name": "S_FESTIVAL_CELEBRATIONS_EASTER_EGG_GET_REWARD_REQ", "cmMethod": "FestivalCelebrationsEasterEggGetRewardReq", "smMethod": "FestivalCelebrationsEasterEggGetRewardRsp"}, "211015": {"name": "S_FESTIVAL_CELEBRATIONS_GET_LUCKY_FATE_REWARD_REQ", "cmMethod": "FestivalCelebrationsFuYuanGetRewardReq", "smMethod": "FestivalCelebrationsFuYuanGetRewardResp"}, "211016": {"name": "S_FESTIVAL_CELEBRATIONS_GET_BIND_UNION_MEMBER_DATA_LIST_REQ", "cmMethod": "FestivalCelebrationsGetBindUnionMemberDataListReq", "smMethod": "FestivalCelebrationsGetBindUnionMemberDataListRsp"}, "211021": {"name": "S_FESTIVAL_CELEBRATIONS_GET_ANNUAL_MEMORY_REQ", "cmMethod": "FestivalCelebrationsGetAnnualMemoryReq", "smMethod": "FestivalCelebrationsGetAnnualMemoryRsp"}, "211022": {"name": "S_FESTIVAL_CELEBRATIONS_WORSHIP_REQ", "cmMethod": "FestivalCelebrationsWorshipReq", "smMethod": "FestivalCelebrationsWorshipResp"}, "211111": {"name": "S_DOUBLE_DEMONS_DATA_LOGIN_SYNC", "smMethod": "DoubleDemonsDataLoginSync"}, "211112": {"name": "S_DOUBLE_DEMONS_AGREE", "cmMethod": "DoubleDemonsAgreeReq", "smMethod": "DoubleDemonsAgreeResp"}, "211113": {"name": "S_DOUBLE_DEMONS_REFUSE", "cmMethod": "DoubleDemonsRefuseReq", "smMethod": "DoubleDemonsRefuseResp"}, "211114": {"name": "S_DOUBLE_DEMONS_RECEIVE_PRESENT", "cmMethod": "DoubleDemonsReceivePresentReq", "smMethod": "DoubleDemonsReceivePresentResp"}, "211101": {"name": "S_DOUBLE_DEMONS_ENTER", "cmMethod": "DoubleDemonsEnterReq", "smMethod": "DoubleDemonsEnterResp"}, "211102": {"name": "S_DOUBLE_DEMONS_DATA_SYNC", "smMethod": "DoubleDemonsDataSync"}, "211103": {"name": "S_DOUBLE_DEMONS_GET_REWARD", "cmMethod": "DoubleDemonsGetRewardReq", "smMethod": "DoubleDemonsGetRewardResp"}, "211104": {"name": "S_DOUBLE_DEMONS_FLY_UP", "cmMethod": "DoubleDemonsFlyUpReq", "smMethod": "DoubleDemonsFlyUpResp"}, "211105": {"name": "S_DOUBLE_DEMONS_SHARE_INVITE", "cmMethod": "DoubleDemonsShareInviteReq", "smMethod": "DoubleDemonsShareInviteResp"}, "211106": {"name": "S_DOUBLE_DEMONS_APPOINT_INVITE", "cmMethod": "DoubleDemonsAppointInviteReq", "smMethod": "DoubleDemonsAppointInviteResp"}, "211107": {"name": "S_DOUBLE_DEMONS_FIND_FRIEND", "cmMethod": "DoubleDemonsFindFriendReq", "smMethod": "DoubleDemonsFindFriendResp"}, "211108": {"name": "S_DOUBLE_DEMONS_SEND_PRESENT", "cmMethod": "DoubleDemonsSendPresentReq", "smMethod": "DoubleDemonsSendPresentResp"}, "211115": {"name": "S_DOUBLE_DEMONS_RANK_SYNC", "smMethod": "DoubleDemonsRankSync"}, "211601": {"name": "S_DEMON_TOWER_BASE_INFO", "cmMethod": "DemonTowerBaseInfoReqMsg", "smMethod": "DemonTowerBaseInfoRespMsg"}, "211602": {"name": "S_DEMON_TOWER_DRAW", "cmMethod": "DemonTowerDrawReqMsg", "smMethod": "DemonTowerDrawRespMsg"}, "211603": {"name": "S_DEMON_TOWER_CHOOSE_RAREREWARD", "cmMethod": "DemonTowerChooseRareRewardReqMsg", "smMethod": "DemonTowerChooseRareRewardRespMsg"}, "211604": {"name": "S_DEMON_TOWER_SELECT_REWARD", "cmMethod": "DemonTowerSelectRewardReqMsg", "smMethod": "DemonTowerSelectRewardRespMsg"}, "211500": {"name": "NewYearRedBagPb_PlayerData", "smMethod": "NewYearRedBagPlayerData"}, "211501": {"name": "NewYearRedBagPb_Enter", "cmMethod": "NewYearRedBagEnterReqMsg", "smMethod": "NewYearRedBagEnterRespMsg"}, "211502": {"name": "NewYearRedBagPb_Exit", "cmMethod": "NewYearRedBagExitReqMsg", "smMethod": "NewYearRedBagExitRespMsg"}, "211503": {"name": "NewYearRedBagPb_OPEN", "cmMethod": "NewYearRedBagOpenReqMsg", "smMethod": "NewYearRedBagOpenRespMsg"}, "211504": {"name": "NewYearRedBagPb_SYNC_NOTICE", "smMethod": "NewYearSyncNoticeRespMsg"}, "212601": {"name": "S_GET_WECHAT_RANK_WHITE_LIST", "cmMethod": "GetWechatRankWhiteListReq", "smMethod": "GetWechatRankWhiteListRsp"}, "211813": {"name": "S_PUPIL_LOGIN_SYNC", "smMethod": "PupilSystemLoginSync"}, "211801": {"name": "S_PUPIL_ENTER", "cmMethod": "EnterPupilSystemReq", "smMethod": "EnterPupilSystemResp"}, "211802": {"name": "S_PUPIL_TRAIN", "cmMethod": "PupilTrainReq", "smMethod": "PupilTrainResp"}, "211803": {"name": "S_PUPIL_TRAIN_TIMES_RECOVER", "cmMethod": "PupilSiteTrainTimesRecoverReq", "smMethod": "PupilSiteTrainTimesRecoverResp"}, "211804": {"name": "S_PUPIL_LV_UP", "cmMethod": "SectLvUpReq", "smMethod": "SectLvUpResp"}, "211805": {"name": "S_PUPIL_RECRUIT", "cmMethod": "PupilRecruitReq", "smMethod": "PupilRecruitResp"}, "211806": {"name": "S_PUPIL_GRADUATE", "cmMethod": "PupilGraduateReq", "smMethod": "PupilGraduateResp"}, "211807": {"name": "S_PUPIL_GET_GRADUATE_LIST", "cmMethod": "PupilGetGraduatedListReq", "smMethod": "PupilGetGraduatedListResp"}, "211808": {"name": "S_PUPIL_GET_FIGHT_LIST", "cmMethod": "PupilGetFightListReq", "smMethod": "PupilGetFightListResp"}, "211809": {"name": "S_PUPIL_FIGHT_ON", "cmMethod": "PupilFightOnReq", "smMethod": "PupilFightOnResp"}, "211810": {"name": "S_PUPIL_GET_SECT_INFO", "cmMethod": "PupilGetSectInfoReq", "smMethod": "PupilGetSectInfoResp"}, "211814": {"name": "S_PUPIL_GET_AD_REWARD", "cmMethod": "PupilGetAdRewardReq", "smMethod": "PupilGetAdRewardResp"}, "211815": {"name": "S_PUPIL_LOCK", "cmMethod": "PupilLockReq", "smMethod": "PupilLockResp"}, "211816": {"name": "S_PUPIL_EXACT", "cmMethod": "PupilExactSearchReq", "smMethod": "PupilExactSearchResp"}, "211850": {"name": "S_PUPIL_MARRIAGE_USER_DATA_SYNC", "smMethod": "MarriageUserDataMsgSync"}, "211851": {"name": "S_PUPIL_MARRIAGE_APPOINT_APPLY_SYNC", "smMethod": "MarriageGetAppointApplySync"}, "211852": {"name": "S_PUPIL_MARRIAGE_GRADUATED_UN_MARRIAGE_LIST_SYNC", "smMethod": "PupilGraduatedUnMarryListSync"}, "211853": {"name": "S_PUPIL_MARRIAGE_RECORD_SYNC", "smMethod": "MarriageRecordTempSync"}, "211854": {"name": "S_PUPIL_MARRIAGE_REFUSE_NOTIFY_SYNC", "smMethod": "MarriageRefuseNotifyMsgSync"}, "211855": {"name": "S_PUPIL_MARRIAGE_GRADUATED_UN_MARRIAGE_LIST", "cmMethod": "PupilGraduatedUnMarryListReq", "smMethod": "PupilGraduatedUnMarryListResp"}, "211856": {"name": "S_PUPIL_MARRIAGE_RECORD_LIST", "cmMethod": "MarriageRecordListReq", "smMethod": "MarriageRecordListResp"}, "211857": {"name": "S_PUPIL_MARRIAGE_GET_SERVER_APPLY", "cmMethod": "MarriageGetServerApplyReq", "smMethod": "MarriageGetServerApplyResp"}, "211858": {"name": "S_PUPIL_MARRIAGE_RECOMMEND_PLAYER", "cmMethod": "MarriageRecommendPlayerReq", "smMethod": "MarriageRecommendPlayerResp"}, "211859": {"name": "S_PUPIL_MARRIAGE_GET_APPOINT_APPLY", "cmMethod": "MarriageGetAppointApplyReq", "smMethod": "MarriageGetAppointApplyResp"}, "211860": {"name": "S_PUPIL_MARRIAGE_APPLY_DEAL", "cmMethod": "MarriageApplyDealReq", "smMethod": "MarriageApplyDealResp"}, "211861": {"name": "S_PUPIL_MARRIAGE_APPLY_PUBLISH", "cmMethod": "MarriageApplyPublishReq", "smMethod": "MarriageApplyPublishResp"}, "211862": {"name": "S_PUPIL_MARRIAGE_APPLY_CANCEL", "cmMethod": "MarriageApplyCancelReq", "smMethod": "MarriageApplyCancelResp"}, "211863": {"name": "S_PUPIL_MARRIAGE_REFRESH_SERVER_APPLY", "cmMethod": "MarriageRefreshServerApplyReq", "smMethod": "MarriageRefreshServerApplyResp"}, "211864": {"name": "S_PUPIL_MARRIAGE_SET_APPOINT", "cmMethod": "MarriageSetAppointReq", "smMethod": "MarriageSetAppointResp"}, "211865": {"name": "S_PUPIL_MARRIAGE_GET_APPOINT_APPLY_TIMES_LIST", "smMethod": "MarriageGetAppointApplyTimeSync"}, "211811": {"name": "S_SEARCH_PLAYER", "cmMethod": "SearchPlayerReq", "smMethod": "SearchPlayerResp"}, "211702": {"name": "S_PET_KERNEL_CARRY", "cmMethod": "PetKerneCarryReq", "smMethod": "PetKerneCarryResp"}, "211703": {"name": "S_PET_KERNEL_UP_LEVEL", "cmMethod": "PetKerneUpLevelReq", "smMethod": "PetKerneUpLevelResp"}, "211704": {"name": "S_PET_KERNEL_UP_GRADE", "cmMethod": "PetKerneUpStarReq", "smMethod": "PetKerneUpStarResp"}, "211705": {"name": "S_PET_KERNEL_ACTIVE", "cmMethod": "PetKerneActiveReq", "smMethod": "PetKerneActiveResp"}, "211706": {"name": "S_PET_KERNEL_DRAW", "cmMethod": "PetKernelDrawReq", "smMethod": "PetKernelDrawResp"}, "211707": {"name": "S_PET_KERNEL_COMBINE_UPGRADE", "cmMethod": "PetKerneCombineUpLevelReq", "smMethod": "PetKerneCombineUpLevelResp"}, "211708": {"name": "S_PET_KERNEL_SYNC_DATA", "cmMethod": "", "smMethod": "PetKernelPlayerDataMsg"}, "212101": {"name": "S_UNION_ASSEMBLE_USE_ITEM_REQ", "cmMethod": "UnionAssembleUseItemReq", "smMethod": "UnionAssembleUseItemRsp"}, "212102": {"name": "S_UNION_ASSEMBLE_GET_REWARD_REQ", "cmMethod": "UnionAssembleGetRewardReq", "smMethod": "UnionAssembleGetRewardResp"}, "212003": {"name": "S_LIMIT_TIME_GROUP_BUY_SYNC_PROGRESS_DATA_REQ", "cmMethod": "TeamBuySyncProgressDataReq", "smMethod": "TeamBuySyncProgressDataRsp"}, "212004": {"name": "S_LIMIT_TIME_GROUP_BUY_GET_CONDITION_REWARD_REQ", "cmMethod": "TeamBuyGetConditionRewardReq", "smMethod": "TeamBuyGetConditionRewardRsp"}, "213101": {"name": "S_MONOPOLY_ENTER_ACTIVITY", "cmMethod": "MonopolyEnterActivityReq", "smMethod": "MonopolyEnterActivityResp"}, "213102": {"name": "S_MONOPOLY_ENTER_MAP", "cmMethod": "MonopolyEnterMapReq", "smMethod": "MonopolyEnterMapResp"}, "213103": {"name": "S_MONOPOLY_ROLL_DICE", "cmMethod": "MonopolyRollDiceReq", "smMethod": "MonopolyRollDiceResp"}, "213104": {"name": "S_MONOPOLY_LOG_LIST", "cmMethod": "MonopolyAssistListReq", "smMethod": "MonopolyAssistListResp"}, "213105": {"name": "S_MONOPOLY_USE_STRENGHTH_ITEM", "cmMethod": "MonopolyReplenishStrengthReq", "smMethod": "MonopolyReplenishStrengthResp"}, "213107": {"name": "S_MONOPOLY_RESCUE_TRAP", "cmMethod": "MonopolyRescueTrapReq", "smMethod": "MonopolyRescueTrapResp"}, "213108": {"name": "S_MONOPOLY_REQ_ROB_LIST", "cmMethod": "MonopolyRobListReq", "smMethod": "MonopolyRobListResp"}, "213110": {"name": "S_MONOPOLY_DO_ROB", "cmMethod": "MonopolyRobReq", "smMethod": "MonopolyRobResp"}, "213111": {"name": "S_MONOPOLY_ROB_LOG", "cmMethod": "MonopolyUnionLogListReq", "smMethod": "MonopolyUnionLogListResp"}, "213112": {"name": "S_MONOPOLY_ROB_LOG_DETAIL", "cmMethod": "MonopolyPlayerLogDetailReq", "smMethod": "MonopolyPlayerLogDetailResp"}, "213113": {"name": "S_MONOPOLY_ASSIST_ATTACK_MONSTER", "cmMethod": "MonopolyAssistAttackMonsterReq", "smMethod": "MonopolyAssistAttackMonsterResp"}, "213114": {"name": "S_MONOPOLY_RECEIVE_ASSIST_REWARD", "cmMethod": "MonopolyReceiveMonsterRewardReq", "smMethod": "MonopolyReceiveMonsterRewardResp"}, "213115": {"name": "S_MONOPOLY_RED_POINT", "cmMethod": "MonopolyRedPointReqMsg", "smMethod": "MonopolyRedPointRespMsg"}, "213116": {"name": "S_MONOPOLY_USE_REMOTE_ROLL", "cmMethod": "MonopolyRemoteRollDiceReq", "smMethod": "MonopolyRemoteRollDiceResp"}, "213117": {"name": "S_MONOPOLY_ENTER_ROB", "cmMethod": "MonopolyEnterRobMapReq", "smMethod": "MonopolyEnterRobMapResp"}, "213118": {"name": "S_MONOPOLY_AUTO_UNLOCK", "cmMethod": "MonopolyAutoUnlockReq", "smMethod": "MonopolyAutoUnlockResp"}, "213121": {"name": "S_MONOPOLY_USE_MOVE_BUFF", "cmMethod": "MonopolyQuickMoveReq", "smMethod": "MonopolyQuickMoveResp"}, "213122": {"name": "S_MONOPOLY_SPE_MOVE", "cmMethod": "MonopolyDarkGridMoveReq", "smMethod": "MonopolyDarkGridMoveResp"}, "213123": {"name": "S_MONOPOLY_EVENT_HANDLE", "cmMethod": "MonopolyEventHandleReq", "smMethod": "MonopolyEventHandleResp"}, "213124": {"name": "S_MONOPOLY_MONSTER_INFO", "cmMethod": "MonopolyMonsterAttrReq", "smMethod": "MonopolyMonsterAttrResp"}, "213125": {"name": "S_MONOPOLY_REQ_SELF_RANK", "cmMethod": "MonopolyGetPlayerRankReq", "smMethod": "MonopolyGetPlayerRankResp"}, "213126": {"name": "S_MONOPOLY_REQ_ENEMY_LIST", "cmMethod": "MonopolyGetEnemyListReq", "smMethod": "MonopolyGetEnemyListResp"}, "213127": {"name": "S_MONOPOLY_SCORE_DETAIL", "cmMethod": "MonopolyScoreDetailReq", "smMethod": "MonopolyScoreDetailResp"}, "213128": {"name": "S_MONOPOLY_BUILDING_SCORE_DETAIL", "cmMethod": "MonopolyBuildingScoreDetailReq", "smMethod": "MonopolyBuildingScoreDetailResp"}, "213141": {"name": "S_MONOPOLY_GROUP_DETAIL", "cmMethod": "MonopolyGetGroupDetailReq", "smMethod": "MonopolyGetGroupDetailResp"}, "213142": {"name": "S_MONOPOLY_WORSHIP", "cmMethod": "MonopolyWorshipReq", "smMethod": "MonopolyWorshipResp"}, "213130": {"name": "S_MONOPOLY_NOTIFY_BULLET_TEXT", "cmMethod": "", "smMethod": "MonopolyBarrageNotify"}, "213131": {"name": "S_MONOPOLY_NOTIFY_PLAYER_MOVE", "cmMethod": "", "smMethod": "MonopolyMoveNotify"}, "213132": {"name": "S_MONOPOLY_NOTIFY_PLAYER_BREAK_TRAP", "cmMethod": "", "smMethod": "MonopolyNotifyPlayerEndTrap"}, "213133": {"name": "S_MONOPOLY_NOTIFY_BUILDING_UP_LEVEL", "cmMethod": "", "smMethod": "MonopolyNotifyBuildingUpgrade"}, "213136": {"name": "S_MONOPOLY_TEAM_SCORE_CHANGED", "cmMethod": "", "smMethod": "MonopolyScoreNotify"}, "213137": {"name": "S_MONOPOLY_SELF_RANK_CHANGED", "cmMethod": "", "smMethod": "MonopolyRankNotify"}, "213119": {"name": "S_MONOPOLY_BRESSINGLIST", "cmMethod": "MonopolyBlessingListReq", "smMethod": "MonopolyBlessingListResp"}, "213120": {"name": "S_MONOPOLY_REQ_GETBRESSING", "cmMethod": "MonopolyReceiveBlessingReq", "smMethod": "MonopolyReceiveBlessingResp"}, "213135": {"name": "S_MONOPOLY_BLESSING_ASYNC", "smMethod": "MonopolySendBlessingNotify"}, "213139": {"name": "S_MONOPOLY_MY_BLESSING", "smMethod": "MonopolyMyBlessingNotify"}, "213140": {"name": "S_MONOPOLY_ASSIST_RED", "smMethod": "MonopolyAssistRedNotify"}, "213144": {"name": "S_MONOPOLY_ASSIST_SCORE_CHANGED", "smMethod": "MonopolyPlayerScoreChangeNotify"}, "213145": {"name": "S_MONOPOLY_GUESS_PLAYERS", "cmMethod": "MonopolyGuessPlayersReq", "smMethod": "MonopolyGuessPlayersResp"}, "213146": {"name": "S_MONOPOLY_GET_GUESS_DATA", "cmMethod": "MonopolyGetGuessDataReq", "smMethod": "MonopolyGetGuessDataResp"}, "213147": {"name": "S_MONOPOLY_GUESS_SELECT", "cmMethod": "MonopolyGuessSelectReq", "smMethod": "MonopolyGuessSelectResp"}, "213148": {"name": "S_MONOPOLY_GUESS_REWARD", "cmMethod": "MonopolyGuessRewardReq", "smMethod": "MonopolyGuessRewardResp"}, "213201": {"name": "S_SUPPRESSDEMON_BASE_INFO_LOAD", "cmMethod": "TownDemonBaseInfoReqMsg", "smMethod": "TownDemonBaseInfoRespMsg"}, "213202": {"name": "S_SUPPRESSDEMON_ALL_JOIN_SERVER", "cmMethod": "TownDemonAllJoinServerReq", "smMethod": "TownDemonAllJoinServerResp"}, "213203": {"name": "S_SUPPRESSDEMON_RED_DOT_DATA", "cmMethod": "TownDemonRedDotReqMsg", "smMethod": "TownDemonRedDotRespMsg"}, "213204": {"name": "S_SUPPRESSDEMON_GET_ACHIEVE_INFO", "cmMethod": "TowerDemonGetAchieveInfoReqMsg", "smMethod": "TowerDemonGetAchieveInfoRespMsg"}, "213205": {"name": "S_SUPPRESSDEMON_ACHIEVE_REWARD", "cmMethod": "TownDemonAchieveRewardReqMsg", "smMethod": "TownDemonAchieveRewardRespMsg"}, "213206": {"name": "S_SUPPRESSDEMON_GET_RANK_REWARD", "cmMethod": "TownDemonGetRankRewardReqMsg", "smMethod": "TownDemonGetRankRewardRespMsg"}, "213207": {"name": "S_SUPPRESSDEMON_GET_REWARD_INFO", "cmMethod": "TownDemonGetRewardInfoReqMsg", "smMethod": "TownDemonGetRewardInfoRespMsg"}, "213208": {"name": "S_SUPPRESSDEMON_BATTLE", "cmMethod": "TownDemonBattleReqMsg", "smMethod": "TownDemonBattleRespMsg"}, "213209": {"name": "S_SUPPRESSDEMON_GET_ATTACK_UNION_LIST", "cmMethod": "TownDemonGetAttackUnionListReqMsg", "smMethod": "TownDemonGetAttackUnionListRespMsg"}, "213210": {"name": "S_SUPPRESSDEMON_UNION_PLAYER_LIST", "cmMethod": "TownDemonGetPlayerListReq", "smMethod": "TownDemonGetPlayerListResp"}, "213211": {"name": "S_SUPPRESSDEMON_SET_UNION_TARGET", "cmMethod": "TownDemonSetUnionTargetReqMsg", "smMethod": "TownDemonSetUnionTargetRespMsg"}, "213212": {"name": "S_SUPPRESSDEMON_PILLAGE", "cmMethod": "TownDemonPillageReqMsg", "smMethod": "TownDemonPillageRespMsg"}, "213213": {"name": "S_SUPPRESSDEMON_REPORT", "cmMethod": "TownDemonReportReq", "smMethod": "TownDemonReportRsp"}, "213214": {"name": "S_SUPPRESSDEMON_LOG_PLAY_BACK", "cmMethod": "TownDemonLogPlaybackReq", "smMethod": "TownDemonLogPlaybackRsp"}, "213215": {"name": "S_SUPPRESSDEMON_GET_SIMPLE_LOG", "cmMethod": "TownDemonGetSimpleLogReq", "smMethod": "TownDemonGetSimpleLogResp"}, "213216": {"name": "S_SUPPRESSDEMON_GUESS_INFO", "cmMethod": "TownDemonGetGuessInfoReq", "smMethod": "TownDemonGetGuessInfoResp"}, "213217": {"name": "S_SUPPRESSDEMON_SET_GUESS", "cmMethod": "TownDemonSetGuessReq", "smMethod": "TownDemonSetGuessResp"}, "213218": {"name": "S_SUPPRESSDEMON_GET_GUESS_REWARD", "cmMethod": "TownDemonGetGuessRewardReqMsg", "smMethod": "TownDemonGetGuessRewardRespMsg"}, "213219": {"name": "S_SUPPRESSDEMON_GET_BOSS_ATTR", "cmMethod": "TownDemonGetBossDetailDataReq", "smMethod": "TownDemonGetBossDetailDataResp"}, "213220": {"name": "S_SUPPRESSDEMON_GUESS_PLAYER_LIST", "cmMethod": "TownDemonWarGuessPlayersReqMsg", "smMethod": "TownDemonWarGuessPlayersRespMsg"}, "213221": {"name": "S_SUPPRESSDEMON_GET_TOP3PLAYER", "cmMethod": "TownDemonGetTop3PlayerReq", "smMethod": "TownDemonGetTop3PlayerResp"}, "213250": {"name": "S_SUPPRESSDEMON_LOGIN_SYNC", "cmMethod": "TownDemonApplyDataSyncReq", "smMethod": "TownDemonApplyDataSync"}, "213251": {"name": "S_SUPPRESSDEMON_TIME_SYNC", "smMethod": "TownDemonTimeStampsDataSync"}, "213515": {"name": "S_PEACH_BANQUET_LOGIN_SYNC", "smMethod": "PeachBanquetLoginSync"}, "213510": {"name": "S_PEACH_BANQUET_OPEN_SYNC", "smMethod": "PeachBanquetOpenSync"}, "213500": {"name": "S_PEACH_BANQUET_JOIN_LIST_SYNC", "smMethod": "JoinPeachBanquetListSync"}, "213512": {"name": "S_PEACH_BANQUET_REWARD_SYNC", "smMethod": "SeniorSeatRewardSync"}, "213511": {"name": "S_PEACH_BANQUET_SEAT_INFO_SYNC", "smMethod": "SeniorSeatInfoSync"}, "213501": {"name": "S_PEACH_BANQUET_GET_LIST", "cmMethod": "GetPeachBanquetListReq", "smMethod": "GetPeachBanquetListResp"}, "213503": {"name": "S_PEACH_BANQUET_ENTER", "cmMethod": "EnterPeachBanquetReq", "smMethod": "EnterPeachBanquetResp"}, "213504": {"name": "S_PEACH_BANQUET_EXIT", "cmMethod": "ExitPeachBanquetReq", "smMethod": "ExitPeachBanquetResp"}, "213502": {"name": "S_PEACH_BANQUET_SITDOWN", "cmMethod": "SitDownReq", "smMethod": "SitDownResp"}, "213505": {"name": "S_PEACH_BANQUET_SET_AUTO", "cmMethod": "SetAutoReq", "smMethod": "SetAutoResp"}, "213506": {"name": "S_PEACH_BANQUET_GET_APPLY_LIST", "cmMethod": "GetApplyListReq", "smMethod": "GetApplyListResp"}, "213507": {"name": "S_PEACH_BANQUET_AGREE_APPLY", "cmMethod": "AgreeApplyReq", "smMethod": "AgreeApplyResp"}, "213508": {"name": "S_PEACH_BANQUET_REFUSE_ALL", "cmMethod": "RefuseAllReq", "smMethod": "RefuseAllResp"}, "213509": {"name": "S_PEACH_BANQUET_SEARCH", "cmMethod": "SearchBanquetApplyPlayerReq", "smMethod": "SearchBanquetApplyPlayerResp"}, "213518": {"name": "S_PEACH_BANQUET_APPLY_SYNC", "smMethod": "PeachBanquetApplySync"}, "213519": {"name": "S_PEACH_BANQUET_ITEM_SYNC", "smMethod": "PeachBanquetItemSync"}, "213520": {"name": "S_PEACH_BANQUET_ITEM_USE", "cmMethod": "UsePeachBanquetItemReq", "smMethod": "UsePeachBanquetItemResp"}, "213001": {"name": "S_COMPOSE_BALL_ENTER_REQ", "cmMethod": "ComposeBallEnterGameReq", "smMethod": "ComposeBallEnterGameResp"}, "213002": {"name": "S_COMPOSE_BALL_COMPOSE_REQ", "cmMethod": "ComposeBallComposeReq", "smMethod": "ComposeBallComposeResp"}, "213003": {"name": "S_COMPOSE_BALL_USE_ITEM_REQ", "cmMethod": "ComposeBallUseItemReq", "smMethod": "ComposeBallUseItemResp"}, "213004": {"name": "S_COMPOSE_BALL_HP_ITEM_REQ", "cmMethod": "ComposeBallHpItemReq", "smMethod": "ComposeBallHpItemResp"}, "213005": {"name": "S_COMPOSE_BALL_SETTLE_REQ", "cmMethod": "ComposeBallSettleReq", "smMethod": "ComposeBallSettleResp"}, "213701": {"name": "S_HaoActivity_LUCKY_DRAW", "cmMethod": "HaoLuckyDrawReq", "smMethod": "HaoLuckyDrawResp"}, "2013901": {"name": "S_EXPLORE_CHALLENGE_BOSS", "cmMethod": "challengeBossReq", "smMethod": "challengeBossResp"}, "2013902": {"name": "S_EXPLORE_TRANSFER_TO_NEXT_STAGE", "cmMethod": "transferToNextStageReq", "smMethod": "transferToNextStageResp"}, "2013903": {"name": "S_EXPLORE_VIEW_BOSS_DETAIL", "cmMethod": "viewBloodForbiddenBossReq", "smMethod": "viewBloodForbiddenBossResp"}, "2013904": {"name": "S_EXPLORE_CLEAR_MONSTERS_POS", "cmMethod": "clearMonsterPosReq", "smMethod": "clearMonsterPosResp"}, "213801": {"name": "S_PUPIL_RANK_ENTER", "cmMethod": "EnterPupilRankActivityReq", "smMethod": "EnterPupilRankActivityResp"}, "213802": {"name": "S_PUPIL_RANK_GET_INCREASE_RECORD", "cmMethod": "GetIncreaseRecordReq", "smMethod": "GetIncreaseRecordResp"}, "213803": {"name": "S_PUPIL_RANK_DETAIL", "cmMethod": "PupilRankDetailReq", "smMethod": "PupilRankDetailResp"}, "214101": {"name": "S_FRIEND_GET_LIST", "cmMethod": "FriendGetListReq", "smMethod": "FriendGetListResp"}, "214102": {"name": "S_FRIEND_SEND", "cmMethod": "FriendSendReq", "smMethod": "FriendSendResp"}, "214103": {"name": "S_FRIEND_RECEIVE", "cmMethod": "FriendReceiveReq", "smMethod": "FriendReceiveResp"}, "214104": {"name": "S_FRIEND_ONE_KEY", "cmMethod": "FriendReceiveAllReq", "smMethod": "FriendReceiveAllResp"}, "214105": {"name": "S_FRIEND_REMOVE", "cmMethod": "FriendDeleteReq", "smMethod": "FriendDeleteResp"}, "214130": {"name": "S_FRIEND_SYNC", "smMethod": "SyncFriendList"}, "214132": {"name": "S_FRIEND_SYNC_MODIFY", "smMethod": "SyncFriendInfo"}, "214131": {"name": "S_FRIEND_MESSAGE_SYNC", "cmMethod": "", "smMethod": "WorldMessageListMsg"}, "214120": {"name": "S_FRIEND_MESSAGE_LIST", "cmMethod": "FriendChatReqMsg", "smMethod": "WorldMessageListMsg"}, "136675": {"name": "S_SKY_TRADE_ENTER", "cmMethod": "SkyTradeEnterActivityReq", "smMethod": "SkyTradeEnterActivityResp"}, "215001": {"name": "S_SKY_TRADE_ENTER_MAP", "cmMethod": "SkyTradeEnterReq", "smMethod": "SkyTradeEnterResp"}, "215002": {"name": "S_SKY_TRADE_GROUP_INFO", "cmMethod": "SkyTradeGroupInfoReq", "smMethod": "SkyTradeGroupInfoResp"}, "215003": {"name": "S_SKY_TRADE_SPEED_UP", "cmMethod": "SkyTradeAddSpeedReq", "smMethod": "SkyTradeAddSpeedResp"}, "215004": {"name": "S_SKY_TRADE_MOVE", "cmMethod": "SkyTradeGotoPortReq", "smMethod": "SkyTradeGotoPortResp"}, "215005": {"name": "S_SKY_TRADE_BUY", "cmMethod": "SkyTradeDealReq", "smMethod": "SkyTradeDealResp"}, "215006": {"name": "S_SKY_TRADE_CHALLENGE_LIST", "cmMethod": "SkyTradeChallengeListReq", "smMethod": "SkyTradeChallengeListResp"}, "215007": {"name": "S_SKY_TRADE_CHALLENGE_INFO", "cmMethod": "SkyTradeChallengeReq", "smMethod": "SkyTradeChallengeResp"}, "215008": {"name": "S_SKY_TRADE_PLAYER_INFO", "cmMethod": "SkyTradeUnionFameReq", "smMethod": "SkyTradeUnionFameResp"}, "215009": {"name": "S_<PERSON>Y_TRADE_REQ_MOVE_FINISH", "cmMethod": "SkyTradeGetRewardReq", "smMethod": "SkyTradeGetRewardResp"}, "215010": {"name": "S_SKY_TRADE_REPORT", "cmMethod": "SkyTradeReportReq", "smMethod": "SkyTradeReportResp"}, "215011": {"name": "S_SKY_TRADE_LOG", "cmMethod": "SkyTradePortLogReq", "smMethod": "SkyTradeLogResp"}, "215012": {"name": "S_SKY_TRADE_PLANT_INFO", "cmMethod": "SkyTradeSparInfoReq", "smMethod": "SkyTradeSparInfoResp"}, "215013": {"name": "S_SKY_TRADE_PLANT_NOTICE", "cmMethod": "SkyTradeGetSparPowerReq", "smMethod": "SkyTradeGetSparPowerResp"}, "215014": {"name": "S_SKY_TRADE_PLANT_WATER", "cmMethod": "SkyTradeGetWelfareReq", "smMethod": "SkyTradeGetWelfareResp"}, "215015": {"name": "S_SKY_TRADE_PLANT_SEND", "cmMethod": "SkyTradeSendWelfareReq", "smMethod": "SkyTradeSendWelfareResp"}, "215016": {"name": "S_SKY_TRADE_PLANT_SEND_LOG", "cmMethod": "SkyTradeWelfareRecordReq", "smMethod": "SkyTradeWelfareRecordResp"}, "215017": {"name": "S_SKY_TRADE_KEEP_HEART", "cmMethod": "SkyTradeChallengeHeartBeatReq", "smMethod": "SkyTradeChallengeHeartBeatResp"}, "215018": {"name": "S_SKY_TRADE_USE_ITEM", "cmMethod": "SkyTradeUseRobItemReq", "smMethod": "SkyTradeUseRobItemResp"}, "215019": {"name": "S_SKY_TRADE_REPORT_DETAIL", "cmMethod": "SkyTradeReportDetailReq", "smMethod": "SkyTradeReportDetailResp"}, "215020": {"name": "S_SKY_TRADE_GUESS_PLAYER_LIST", "cmMethod": "SkyTradeGuessPlayersReq", "smMethod": "SkyTradeGuessPlayersResp"}, "215021": {"name": "S_SKY_TRADE_BET_DATA", "cmMethod": "SkyTradeGuessDataReq", "smMethod": "SkyTradeGuessDataResp"}, "215022": {"name": "S_SKY_TRADE_BET_SELECT", "cmMethod": "SkyTradeGuessSelectReq", "smMethod": "SkyTradeGuessSelectResp"}, "215023": {"name": "S_SKY_TRADE_BET_REWARD", "cmMethod": "SkyTradeGuessRewardReq", "smMethod": "SkyTradeGuessRewardResp"}, "215024": {"name": "S_SKY_TRADE_REPORT_GOODS", "cmMethod": "SkyTradeReportGoodsReq", "smMethod": "SkyTradeReportGoodsResp"}, "215025": {"name": "S_SKY_TRADE_RESET_STOCK", "cmMethod": "SkyTradeResetStockReq", "smMethod": "SkyTradeResetStockResp"}, "215026": {"name": "S_SKY_TRADE_RED", "cmMethod": "SkyTradeRedPointReq", "smMethod": "SkyTradeRedPointResp"}, "215027": {"name": "S_SKY_TRADE_UNION_GROUP_DAMAGE", "cmMethod": "SkyTradeUnionGroupDamageReq", "smMethod": "SkyTradeUnionGroupDamageResp"}, "215050": {"name": "S_SKY_TRADE_SCORE_CHANGED", "cmMethod": "", "smMethod": "SkyTradeUnionDataSync"}, "215051": {"name": "S_SKY_TRADE_BUILDING_REST_NOTIFY", "cmMethod": "", "smMethod": "SkyTradeResetSync"}, "215052": {"name": "S_SKY_TRADE_SHIP_DATA_NOTIFY", "cmMethod": "", "smMethod": "SkyTradeAirshipInfoSync"}, "215053": {"name": "S_SKY_TRADE_CHANLLENGE_NOTIFY", "cmMethod": "", "smMethod": "SkyTradeChallengeInfoSync"}, "215054": {"name": "S_SKY_TRADE_RAREGOODS_NOTIFY", "cmMethod": "", "smMethod": "SkyTradeRareGoodsSync"}, "215055": {"name": "S_SKY_TRADE_FAME_RANK_NOTIFY", "cmMethod": "", "smMethod": "SkyTradeFameRankSync"}, "214501": {"name": "S_GOD_DEMON_BATTLE_BASE_INFO", "cmMethod": "GodDemonCommonReq", "smMethod": "GodDemonBattleBaseInfoResp"}, "214502": {"name": "S_GOD_DEMON_BATTLE_RECEIVE_CAMP_REWARD", "cmMethod": "GodDemonCommonReq", "smMethod": "GodDemonBattleReceiveCampRewardResp"}, "214503": {"name": "S_GOD_DEMON_BATTLE_GET_CAMP_PLAYERNUM", "cmMethod": "GodDemonCommonReq", "smMethod": "GodDemonBattleGetCampPlayerNumResp"}, "214505": {"name": "S_GOD_DEMON_BATTLE_GET_SEPARATION_INFO", "cmMethod": "GodDemonBattleGetSeparationInfoReq", "smMethod": "GodDemonBattleGetSeparationInfoResp"}, "214506": {"name": "S_GOD_DEMON_BATTLE_SET_SEPARATION_INFO", "cmMethod": "GodDemonCommonReq", "smMethod": "GodDemonBattleGetSeparationInfoResp"}, "214507": {"name": "S_GOD_DEMON_BATTLE_ENTER_ROOM_INFO", "cmMethod": "GodDemonBattleEnterRoomReq", "smMethod": "GodDemonBattleEnterRoomResp"}, "214508": {"name": "S_GOD_DEMON_BATTLE_SET_PREPARE", "cmMethod": "GodDemonCommonReq", "smMethod": "GodDemonBattleSetPrepareResp"}, "214510": {"name": "S_GOD_DEMON_BATTLE_GET_BUFF_INFO", "cmMethod": "GodDemonCommonReq", "smMethod": "GodDemonBattleGetBuffInfoResp"}, "214511": {"name": "S_GOD_DEMON_BATTLE_CHOOSE_BUFF", "cmMethod": "GodDemonBattleChooseBuffReq", "smMethod": "GodDemonBattleChooseBuffResp"}, "214512": {"name": "S_GOD_DEMON_BATTLE_GET_PLAYER_BUFF", "cmMethod": "GodDemonBattleGetPlayerBuffReq", "smMethod": "GodDemonBattleGetPlayerBuffResp"}, "214513": {"name": "S_GOD_DEMON_BATTLE_GET_GOD_DEMON_RANK_INFO", "cmMethod": "GodDemonBattleGetGodDemonRankInfoReq", "smMethod": "GodDemonBattleGetGodDemonRankInfoResp"}, "214514": {"name": "S_GOD_DEMON_BATTLE_VARIOUS_WARS_INFO", "cmMethod": "GodDemonBattleVariousWarsInfoReq", "smMethod": "GodDemonBattleVariousWarsInfoResp"}, "214515": {"name": "S_GOD_DEMON_BATTLE_VARIOUS_WARS_PROMOTE_INFO", "cmMethod": "GodDemonBattleVariousWarsPromoteInfoReq", "smMethod": "GodDemonBattleVariousWarsPromoteInfoResp"}, "214516": {"name": "S_GOD_DEMON_BATTLE_DUEL_PLAYER_INFO", "cmMethod": "GodDemonBattleDuelPlayerInfoReq", "smMethod": "GodDemonBattleDuelPlayerInfoResp"}, "214517": {"name": "S_GOD_DEMON_BATTLE_SET_BATTLE_ORDER", "cmMethod": "GodDemonBattleSetBattleOrderReq", "smMethod": "GodDemonBattleSetBattleOrderResp"}, "214518": {"name": "S_GOD_DEMON_BATTLE_DUEL_VIDEO_INFO", "cmMethod": "GodDemonBattleDuelVideoInfoReq", "smMethod": "GodDemonBattleDuelVideoInfoResp"}, "214520": {"name": "S_GOD_DEMON_BATTLE_SEND_BARRAGE", "cmMethod": "GodDemonBattleSendBarrageReq", "smMethod": "GodDemonBattleSendBarrageResp"}, "214521": {"name": "S_GOD_DEMON_BATTLE_BET_INFO", "cmMethod": "GodDemonGetGuessInfoReq", "smMethod": "GodDemonGetGuessInfoRsp"}, "214522": {"name": "S_GOD_DEMON_BATTLE_RECEIVE_BET_COIN", "cmMethod": "GodDemonGetGuessCoinReq", "smMethod": "GodDemonGetGuessCoinRsp"}, "214523": {"name": "S_GOD_DEMON_BATTLE_SET_BET", "cmMethod": "GodDemonBeginGuessReq", "smMethod": "GodDemonBeginGuessRsp"}, "214524": {"name": "S_GOD_DEMON_BATTLE_LOG_INFO", "cmMethod": "GodDemonReportReq", "smMethod": "GodDemonReportRsp"}, "214525": {"name": "S_GOD_DEMON_BATTLE_RED", "cmMethod": "GodDemonCommonReq", "smMethod": "GodDemonBattleGetRedResp"}, "214526": {"name": "S_GOD_DEMON_BATTLE_PREVIOUS_EDITIONS_INFO", "cmMethod": "GodDemonBattlePreviousEditionsInfoReq", "smMethod": "GodDemonBattlePreviousEditionsInfoResp"}, "214527": {"name": "S_GOD_DEMON_BATTLE_GET_PLAYER_SETTLEMENT", "cmMethod": "GodDemonBattleGetPlayerSettlementReq", "smMethod": "GodDemonBattleGetPlayerSettlementResp"}, "214528": {"name": "S_GOD_DEMON_BATTLE_GET_CAMP_SETTLEMENT", "cmMethod": "GodDemonBattleGetCampSettlementReq", "smMethod": "GodDemonBattleGetCampSettlementResp"}, "214529": {"name": "S_GOD_DEMON_BATTLE_HEARTBEAT", "cmMethod": "GodDemonCommonReq", "smMethod": "GodDemonBattleHeartBeatResp"}, "214531": {"name": "S_GOD_DEMON_BATTLE_WORSHIP", "cmMethod": "GodDemonCommonReq", "smMethod": "GodDemonBattleWorshipResp"}, "214532": {"name": "S_GOD_DEMON_BATTLE_REPORT", "cmMethod": "GodDemonBattleReplyReq", "smMethod": "GodDemonBattleReplyRsp"}, "214534": {"name": "S_GOD_DEMON_BATTLE_GET_SUPRESS_DATA", "cmMethod": "GodDemonSuppressedReq", "smMethod": "GodDemonSuppressedRespMsg"}, "214538": {"name": "S_GOD_DEMON_LOOK_BATTLE_VIDEO", "cmMethod": "GodDemonLookBattleVideoReq", "smMethod": "GodDemonLookBattleVideoResp"}, "214540": {"name": "S_GOD_DEMON_BATTLE_GET_BET_REWRD", "smMethod": "GodDemonSendGuessRewardMsg"}, "214550": {"name": "S_GOD_DEMON_BATTLE_ROOM_PLAYER_PREPARE_SYNC", "smMethod": "GodDemonBattlePlayerPrepareSync"}, "214551": {"name": "S_GOD_DEMON_BATTLE_ROOM_BATTLE_SYNC", "smMethod": "GodDemonRoomBattleSync"}, "214552": {"name": "S_GOD_DEMON_BATTLE_ROOM_ROUND_SYNC", "smMethod": "GodDemonRoomUpdateSync"}, "214553": {"name": "S_GOD_DEMON_PLAYER_ROOM_STATUS_SYNC", "smMethod": "GodDemonPlayerRoomStatusSync"}, "214554": {"name": "S_GOD_DEMON_PLAYER_ROOM_BATTLE_END_SYNC", "smMethod": "GodDemonRoomBattleFinishSync"}, "21034": {"name": "S_ACTIVITY_CONDITION_SELECT_GOODS", "cmMethod": "SelectActivityConditionGoodsReq", "smMethod": "SelectActivityConditionGoodsResp"}, "21035": {"name": "S_ACTIVITY_SYNC_CONDITION_SELECT_LIST", "smMethod": "ActivityConditionSelectDataListSync"}, "214302": {"name": "S_UNIVERSE_SYNC_PLAYER_DATA", "smMethod": "UniverseDataMsgSync"}, "214303": {"name": "S_UNIVERSE_LEVEL_UP", "cmMethod": "UniverseLevelUpReq", "smMethod": "UniverseLevelUpResp"}, "214304": {"name": "S_UNIVERSE_DRAW", "cmMethod": "UniverseDrawReq", "smMethod": "UniverseDrawResp"}, "214305": {"name": "S_UNIVERSE_SKILL_UNLOCK", "cmMethod": "UniverseSkillUnlockReq", "smMethod": "UniverseSkillUnlockResp"}, "214306": {"name": "S_UNIVERSE_SKILL_EQUIP", "cmMethod": "EquipUniverseSkillReq", "smMethod": "EquipUniverseSkillResp"}, "214307": {"name": "S_UNIVERSE_SKILL_CONBINE", "cmMethod": "UniverseSkillCombineLvUpReq", "smMethod": "UniverseSkillCombineLvUpResp"}, "214308": {"name": "S_UNIVERSE_SKILL_DRAW", "cmMethod": "UniverseSkillDrawReq", "smMethod": "UniverseSkillDrawResp"}, "214309": {"name": "S_UNIVERSE_SKILL_LV_UP", "cmMethod": "UniverseSkillLvUpReq", "smMethod": "UniverseSkillLvUpResp"}, "214310": {"name": "S_UNIVERSE_DRAW_TWICE", "cmMethod": "UniverseDrawTwiceReq", "smMethod": "UniverseDrawTwiceResp"}, "218001": {"name": "S_PROFESSION_SYNC_CHANGE_PROFRESSION", "cmMethod": "CareerSwitchCareerReq", "smMethod": "CareerSwitchCareerResp"}, "218002": {"name": "S_PROFESSION_SYNC_PLAYER_DATA", "smMethod": "CareerPlayerDataMsg"}, "218003": {"name": "S_PROFESSION_SYNC_TRAIN", "cmMethod": "CareerTrainReq", "smMethod": "CareerTrainResp"}, "218004": {"name": "S_PROFESSION_SYNC_BATTLE_BOSS", "cmMethod": "CareerBattleBossReq", "smMethod": "CareerBattleBossResp"}, "218005": {"name": "S_PROFESSION_SYNC_BOSS_ATTR", "cmMethod": "CareerGetBossAttrReq", "smMethod": "CareerGetBossAttrResp"}, "218006": {"name": "S_PROFESSION_SYNC_PLAYER_ATTR", "cmMethod": "CareerGetAttrMapReq", "smMethod": "CareerGetAttrMapResp"}, "218007": {"name": "S_PROFESSION_SYNC_TASK_REWARD", "cmMethod": "CareerGetPreTaskRewardReq", "smMethod": "CareerGetPreTaskRewardResp"}, "214640": {"name": "S_PAINT_FAIRYLAND_ENTER_MAIN", "cmMethod": "PaintingFairyEnterMainReq", "smMethod": "PaintingFairyEnterMainResp"}, "214601": {"name": "S_PAINT_FAIRYLAND_ENTER", "cmMethod": "EnterPaintingFairylandReq", "smMethod": "EnterPaintingFairylandResp"}, "214602": {"name": "S_PAINT_FAIRYLAND_GET_BATTLE_RECORD", "cmMethod": "GetFairyBattleRecordReq", "smMethod": "GetFairyBattleRecordResp"}, "214603": {"name": "S_PAINT_FAIRYLAND_COLLECT_ANIMA", "cmMethod": "CollectAnimaReq", "smMethod": "CollectAnimaResp"}, "214605": {"name": "S_PAINT_FAIRYLAND_LEAVE", "cmMethod": "LeavePaintingFairylandReq", "smMethod": "LeavePaintingFairylandResp"}, "214604": {"name": "S_PAINT_FAIRYLAND_ENTER_SHOP", "cmMethod": "EnterFairyLandShopReq", "smMethod": "EnterFairyLandShopResp"}, "214610": {"name": "S_PAINT_FAIRYLAND_MOUNTAIN_ENTER_FAIRY", "cmMethod": "EnterFairyMountainReq", "smMethod": "EnterFairyMountainResp"}, "214611": {"name": "S_PAINT_FAIRYLAND_MOUNTAIN_OCCUPY_FAIRY", "cmMethod": "OccupyFairylandReq", "smMethod": "OccupyFairylandResp"}, "214620": {"name": "S_PAINT_FAIRYLAND_POOL_ENTER_FAIRY", "cmMethod": "EnterFairyPoolReq", "smMethod": "EnterFairyPoolResp"}, "214621": {"name": "S_PAINT_FAIRYLAND_POOL_FAIRY_POOL_INVITE", "cmMethod": "GetFairyPoolInviteDataReq", "smMethod": "GetFairyPoolInviteDataResp"}, "214622": {"name": "S_PAINT_FAIRYLAND_POOL_INVITE_FRIEND", "cmMethod": "InviteFriendReq", "smMethod": "InviteFriendResp"}, "214623": {"name": "S_PAINT_FAIRYLAND_POOL_INVITE_UNION", "cmMethod": "InviteUnionReq", "smMethod": "InviteUnionResp"}, "214624": {"name": "S_PAINT_FAIRYLAND_POOL_APPLY_JOIN", "cmMethod": "ApplyJoinFairyPoolReq", "smMethod": "ApplyJoinFairyPoolResp"}, "214625": {"name": "S_PAINT_FAIRYLAND_POOL_APPLY_LIST", "cmMethod": "GetFairyPoolApplyListReq", "smMethod": "GetFairyPoolApplyListResp"}, "214626": {"name": "S_PAINT_FAIRYLAND_POOL_APPLY_AGREE", "cmMethod": "AgreeFairyPoolApplyReq", "smMethod": "AgreeFairyPoolApplyResp"}, "214627": {"name": "S_PAINT_FAIRYLAND_POOL_APPLY_REFUSE", "cmMethod": "RefuseAllFairyPoolApplyReq", "smMethod": "RefuseAllFairyPoolApplyResp"}, "214628": {"name": "S_PAINT_FAIRYLAND_POOL_OCCUPY", "cmMethod": "OccupyFairyPoolReq", "smMethod": "OccupyFairyPoolResp"}, "214629": {"name": "S_PAINT_FAIRYLAND_POOL_ENTER_INSIDE", "cmMethod": "EnterFairyPoolInsideReq", "smMethod": "EnterFairyPoolInsideResp"}, "214630": {"name": "S_PAINT_FAIRYLAND_POOL_JOIN", "cmMethod": "joinFairyPoolReq", "smMethod": "joinFairyPoolResp"}, "214634": {"name": "S_PAINT_FAIRYLAND_PALACE_ENTER", "cmMethod": "EnterFairyPalaceReq", "smMethod": "EnterFairyPalaceResp"}, "214635": {"name": "S_PAINT_FAIRYLAND_PALACE_JOIN", "cmMethod": "JoinFairyPalaceReq", "smMethod": "JoinFairyPalaceResp"}, "214636": {"name": "S_PAINT_FAIRYLAND_PALACE_OCCUPY", "cmMethod": "OccupyFairyPalaceReq", "smMethod": "OccupyFairyPalaceResp"}, "214699": {"name": "S_PAINT_FAIRYLAND_BEING_SYNC", "smMethod": "BeingSnatchedSync"}, "214637": {"name": "S_PAINT_FAIRYLAND_REFRESH_SYNC", "smMethod": "SyncRefresh"}, "214639": {"name": "S_PAINT_REPORT_LAST_APPLY_SYNC", "smMethod": "SyncLastApplyData"}, "214638": {"name": "S_PAINT_REPORT_GET_PLAYER_INFO", "cmMethod": "GetPlayerCultivationDataReq", "smMethod": "GetPlayerCultivationDataResp"}, "214698": {"name": "S_PAINT_FAIRYLAND_CHECK_POOL", "cmMethod": "CheckFairyPoolReq", "smMethod": "CheckFairyPoolResp"}, "214901": {"name": "S_WISHPOOL_GET_BUYER_INFO", "smMethod": "WishPoolSyncBuyInfoMsg"}, "214902": {"name": "S_WISHPOOL_ENTER", "cmMethod": "WishPoolEnterReq", "smMethod": "WishPoolEnterResp"}, "214903": {"name": "S_WISHPOOL_SYNC_REWARD", "smMethod": "WishPoolSyncRewardMsg"}, "214904": {"name": "S_WISHPOOL_GET_BIG_PRIZE_INFO", "cmMethod": "WishPoolGetWinnerListReq", "smMethod": "WishPoolGetWinnerListResp"}, "214905": {"name": "S_WISHPOOL_EXIT", "cmMethod": "WishPoolExitReq", "smMethod": "WishPoolExitResp"}, "214910": {"name": "S_WISHPOOL_DRAW_INFO_SYNC", "cmMethod": "", "smMethod": "WishPoolDrawInfoMsg"}, "216101": {"name": "VALENTINESDAY_PLAYER_LIST", "cmMethod": "DoubleSeventhPlayerListReq", "smMethod": "DoubleSeventhPlayerListResp"}, "216102": {"name": "VALENTINESDAY_SEND_FLOWER", "cmMethod": "DoubleSeventhSendFlowerReq", "smMethod": "DoubleSeventhSendFlowerResp"}, "216103": {"name": "VALENTINESDAY_GIFT_LIST", "cmMethod": "DoubleSeventhGiftListReq", "smMethod": "DoubleSeventhGiftListResp"}, "216104": {"name": "VALENTINESDAY_GIFT_RECEIVE", "cmMethod": "DoubleSeventhGiftReceiveReq", "smMethod": "DoubleSeventhGiftReceiveResp"}, "216105": {"name": "VALENTINESDAY_GIFT_SEARCH_PLAYER", "cmMethod": "DoubleSeventhSearchPlayerReq", "smMethod": "DoubleSeventhSearchPlayerResp"}, "216108": {"name": "VALENTINESDAY_ENTER", "cmMethod": "DoubleSeventhEnterReq", "smMethod": "DoubleSeventhEnterResp"}, "216109": {"name": "VALENTINESDAY_RECEIVE_GIFT_DETAIL", "cmMethod": "DoubleSeventhPlayerScoreRankReq", "smMethod": "DoubleSeventhPlayerScoreRankResp"}, "215901": {"name": "S_SHURA_BATTLE_ENTER", "cmMethod": "ShuraBattleCommonReq", "smMethod": "EnterShuraBattleMainResp"}, "215902": {"name": "S_SHURA_BATTLE_WORSHIP", "cmMethod": "ShuraBattleCommonReq", "smMethod": "ShuraBattleWorshipResp"}, "215903": {"name": "S_SHURA_BATTLE_ENTER_BATTLE_FIELD", "cmMethod": "ShuraBattleCommonReq", "smMethod": "EnterShuraBattlefieldResp"}, "215904": {"name": "S_SHURA_BATTLE_GET_BATTLE_ATT", "cmMethod": "ShuraBattleGetBattleMainReq", "smMethod": "ShuraPlayerBattleMainInfoResp"}, "215905": {"name": "S_SHURA_BATTLE_LOCK_BATTLE_ATT", "cmMethod": "ShuraBattleGetBattleMainReq", "smMethod": "ShuraPlayerBattleMainInfoResp"}, "215906": {"name": "S_SHURA_BATTLE_CREATE_TEAM", "cmMethod": "ShuraBattleCreateTeamReq", "smMethod": "ShuraBattleCreateTeamRsp"}, "215908": {"name": "S_SHURA_BATTLE_GET_TEAM_LIST", "cmMethod": "ShuraBattleGetTeamListReq", "smMethod": "ShuraBattleGetTeamListResp"}, "215910": {"name": "S_SHURA_BATTLE_APPLY_CANCEL", "cmMethod": "ShuraBattleApplyReq", "smMethod": "ShuraBattleApplyResp"}, "215907": {"name": "S_SHURA_BATTLE_TEAM_DETAIL", "cmMethod": "ShuraBattleTeamDetailReq", "smMethod": "ShuraBattleTeamDetailResp"}, "215916": {"name": "S_SHURA_BATTLE_GET_INVITE_LIST", "cmMethod": "ShuraBattleCommonReq", "smMethod": "ShuraBattleGetInviteListResp"}, "215914": {"name": "S_SHURA_BATTLE_AGREE_REFUSED_INVITE", "cmMethod": "ShuraBattleAgreeInviteReq", "smMethod": "ShuraBattleAgreeInviteResp"}, "215917": {"name": "S_SHURA_BATTLE_SEARCH_TEAM", "cmMethod": "ShuraBattleSearchTeamReq", "smMethod": "ShuraBattleSearchTeamResp"}, "215909": {"name": "S_SHURA_BATTLE_QUIT", "cmMethod": "ShuraBattleCommonReq", "smMethod": "ShuraBattleQuitTeamRsp"}, "215915": {"name": "S_SHURA_BATTLE_GET_APPLY", "cmMethod": "ShuraBattleCommonReq", "smMethod": "ShuraBattleGetApplyListResp"}, "215918": {"name": "S_SHURA_BATTLE_GET_POSITIN_INFO", "cmMethod": "GetShuraBattlePositionReq", "smMethod": "GetShuraBattlePositionResp"}, "215919": {"name": "S_SHURA_BATTLE_SET_POSITION_INDEX", "cmMethod": "SetShuraBattlePositionIndexReq", "smMethod": "SetShuraBattlePositionIndexResp"}, "215920": {"name": "S_SHURA_BATTLE_SET_CONTROL", "cmMethod": "SetShuraBattleControlReq", "smMethod": "SetShuraBattleControlResp"}, "215911": {"name": "S_SHURA_BATTLE_AGREE_REFUSED_APPLY", "cmMethod": "ShuraBattleApplyJoinTeamAgreeReq", "smMethod": "ShuraBattleApplyJoinTeamAgreeRsp"}, "215951": {"name": "S_SHURA_BATTLE_HEART", "cmMethod": "ShuraBattleCommonReq", "smMethod": "EnterShuraBattleMainResp"}, "215921": {"name": "S_SHURA_BATTLE_SUPPRESS", "cmMethod": "ShuraBattleCommonReq", "smMethod": "ShuraBattleSuppressedRespMsg"}, "215922": {"name": "S_SHURA_BATTLE_TEAM_STATE_SYNC", "smMethod": "ShuraBattleTeamSync"}, "215923": {"name": "S_SHURA_BATTLE_KICK_OUT", "cmMethod": "ShuraBattleKickOutTeamReq", "smMethod": "ShuraBattleKickOutTeamRsp"}, "215913": {"name": "S_SHURA_BATTLE_SEND_INVITE", "cmMethod": "ShuraBattleSendInviteReq", "smMethod": "ShuraBattleSendInviteResp"}, "215924": {"name": "S_SHURA_BATTLE_GET_INVITE_DATA", "cmMethod": "ShuraBattleCommonReq", "smMethod": "ShuraBattleInviteDataResp"}, "215925": {"name": "S_SHURA_BATTLE_MOVE", "cmMethod": "ShuraBattleMoveReq", "smMethod": "ShuraBattleMoveResp"}, "215927": {"name": "S_SHURA_BATTLE_POS_SYNC", "smMethod": "ShuraBattleSyncMsg"}, "215928": {"name": "S_SHURA_BATTLE_FIGHT", "cmMethod": "ShuraBattleFightReq", "smMethod": "ShuraBattleFightResp"}, "215929": {"name": "S_SHURA_BATTLE_FIGHT_SYNC", "smMethod": "ShuraBattleDoBattleNotify"}, "215930": {"name": "S_SHURA_BATTLE_INNER_TEAM_SYNC", "smMethod": "ShuraBattleInnerTeamSync"}, "215931": {"name": "S_SHURA_BATTLE_TEAM_BATTLE_SYNC", "smMethod": "ShuraBattleTeamBattleEndTimeSync"}, "215932": {"name": "S_SHURA_BATTLE_CHANGE_FLOOR", "smMethod": "ShuraBattleFloorChangeSyncMsg"}, "215933": {"name": "S_SHURA_BATTLE_SETTLE_SYNC", "smMethod": "ShuraBattleSettleSyncMsg"}, "215934": {"name": "S_SHURA_BATTLE_GET_FIELD_RANK", "cmMethod": "ShuraBattleCommonReq", "smMethod": "ShuraBattleCurFieldRankInfoResp"}, "215935": {"name": "S_SHURA_BATTLE_GET_BREAKOUT_SCORE", "cmMethod": "GetShuraBattleBreakOutScoreReq", "smMethod": "GetShuraBattleBreakOutScoreResp"}, "215936": {"name": "S_SHURA_BATTLE_GET_GROUP_TEAM_DATAS", "cmMethod": "GetShuraBattleGroupTeamDatasReq", "smMethod": "GetShuraBattleGroupTeamDatasResp"}, "215940": {"name": "S_SHURA_BATTLE_GET_ALL_FLOOR_NUMS", "cmMethod": "ShuraBattleCommonReq", "smMethod": "GetShuraBattleAllFloorNumsInfoResp"}, "215941": {"name": "S_SHURA_BATTLE_GET_JOIN_REWARD", "cmMethod": "GetJoinRewardReq", "smMethod": "GetJoinRewardResp"}, "215937": {"name": "S_SHURA_BATTLE_GET_GUESS_DATA", "cmMethod": "ShuraBattleGetGuessDataReq", "smMethod": "ShuraBattleGetGuessDataResp"}, "215938": {"name": "S_SHURA_BATTLE_SET_GUESS_SELECT", "cmMethod": "ShuraBattleSetGuessSelectReq", "smMethod": "ShuraBattleSetGuessSelectResp"}, "215939": {"name": "S_SHURA_BATTLE_GET_GUESS_REWARD", "cmMethod": "ShuraBattleGetGuessRewardReq", "smMethod": "ShuraBattleGetGuessRewardRsp"}, "215942": {"name": "S_SHURA_BATTLE_GET_GUESS_PLAYER", "cmMethod": "ShuraBattleGuessPlayersReqMsg", "smMethod": "ShuraBattleGuessPlayersRespMsg"}, "215943": {"name": "S_SHURA_BATTLE_GET_REWARD_TEAM", "cmMethod": "getRewardTeamDataReq", "smMethod": "getRewardTeamDataResp"}, "215944": {"name": "S_SHURA_BATTLE_GET_REPORT", "cmMethod": "GetShuraBattleReportReq", "smMethod": "GetShuraBattleReportResp"}, "215946": {"name": "S_SHURA_BATTLE_GET_BACK_PLAY", "cmMethod": "ShuraBattleRecordBackPlayReq", "smMethod": "ShuraBattleRecordBackPlayResp"}, "215945": {"name": "S_SHURA_BATTLE_GET_RED", "cmMethod": "ShuraBattleCommonReq", "smMethod": "ShuraBattleRedPointResp"}, "215947": {"name": "S_SHURA_BATTLE_GET_TEAM_POSITION", "cmMethod": "ShuraBattleGetTeamPositionReq", "smMethod": "ShuraBattleGetTeamPositionResp"}, "215501": {"name": "S_KIDDING_SEA_SAVE_PROPERTY", "cmMethod": "KiddingSeaSavePropertyReq", "smMethod": "KiddingSeaSavePropertyResp"}, "215502": {"name": "S_KIDDING_SEA_RESET_PROPERTY", "cmMethod": "KiddingSeaResetPropertyReq", "smMethod": "KiddingSeaResetPropertyResp"}, "215503": {"name": "S_KIDDING_SEA_RELEASE_PARTNER", "cmMethod": "KiddingSeaReleasePartnerReq", "smMethod": "KiddingSeaReleasePartnerResp"}, "215504": {"name": "S_KIDDING_SEA_KILL_MONSTER", "cmMethod": "KiddingSeaKillMonsterReq", "smMethod": "KiddingSeaKillMonsterResp"}, "215505": {"name": "S_KIDDING_SEA_KILL_BOSS", "cmMethod": "KiddingSeaKillBossReq", "smMethod": "KiddingSeaKillBossResp"}, "215506": {"name": "S_KIDDING_SEA_GET_ONHOOK_EXP", "cmMethod": "KiddingSeaGetOnHookExpReq", "smMethod": "KiddingSeaGetOnHookExpResp"}, "215507": {"name": "S_KIDDING_SEA_TO_NEXT_STAGE", "cmMethod": "KiddingSeaToNextStageReq", "smMethod": "KiddingSeaToNextStageResp"}, "215508": {"name": "S_KIDDING_SEA_ACTIVE_ON_HOOK", "cmMethod": "KiddingSeaActiveOnHookReq", "smMethod": "KiddingSeaActiveOnHookResp"}, "215801": {"name": "S_YARDPB_ENTER", "cmMethod": "YardEnterReq", "smMethod": "YardEnterResp"}, "215802": {"name": "S_YARDPB_BUILD_BATTLE", "cmMethod": "YardBattleReq", "smMethod": "YardBattleResp"}, "215803": {"name": "S_YARDPB_BUILD_POS_ACITION", "cmMethod": "YardPosActionReq", "smMethod": "YardPosActionResp"}, "215804": {"name": "S_YARDPB_LEVEL_UP", "cmMethod": "YardLevelUpReq", "smMethod": "YardLevelUpResp"}, "215805": {"name": "S_YARDPB_CLEAR_GARBAGE", "cmMethod": "YardClearReq", "smMethod": "YardClearResp"}, "215820": {"name": "S_YARDPB_GET_LIKE_LIST", "cmMethod": "YardCommonReq", "smMethod": "YardGetLikeListResp"}, "215821": {"name": "S_YARDPB_BUILD_CONBINE", "cmMethod": "YardSkillCombineLvUpReq", "smMethod": "YardSkillCombineLvUpResp"}, "215822": {"name": "S_YARDPB_BUILD_DRAW", "cmMethod": "YardDrawReq", "smMethod": "YardDrawResp"}, "215823": {"name": "S_YARDPB_BUILD_LEVEL_UP", "cmMethod": "BuildUpLevelReq", "smMethod": "BuildUpLevelResp"}, "215824": {"name": "S_YARDPB_BUILD_UNLOCK", "cmMethod": "BuildUnlockReq", "smMethod": "BuildUnlockResp"}, "215825": {"name": "S_YARDPB_BUILD_MAKE", "cmMethod": "YardBuildMakeReq", "smMethod": "YardBuildMakeResp"}, "215826": {"name": "S_YARDPB_TREE_HELP", "cmMethod": "YardBuildHelpReq", "smMethod": "YardBuildhHelpResp"}, "215827": {"name": "S_YARDPB_BUILD_GAIN_REWARD", "cmMethod": "YardBuildGainRewardReq", "smMethod": "YardBuildGainRewardResp"}, "215828": {"name": "S_YARDPB_BUILD_SPEED", "cmMethod": "YardBuildSpeedUpReq", "smMethod": "YardBuildhSpeedUpResp"}, "215829": {"name": "S_YARDPB_BUILD_UNION_HELP_LIST", "cmMethod": "GetYardUnionHelpDataListReq", "smMethod": "GetYardUnionHelpDataListResp"}, "215830": {"name": "S_YARDPB_BUILD_REQUEST_UNION_HELP", "cmMethod": "RequestYardUnionHelpReq", "smMethod": "RequestYardUnionHelpResp"}, "215831": {"name": "S_YARDPB_BUILD_UNION_HELP", "cmMethod": "YardUnionHelpReq", "smMethod": "YardUnionHelpResp"}, "215833": {"name": "S_YARDPB_BUILD_ONE_KEY_LEVEL_UP", "cmMethod": "YardOneKeyLevelUpReq", "smMethod": "YardOneKeyLevelUpResp"}, "215834": {"name": "S_YARDPB_PEACH_RECORD_LIST", "cmMethod": "YardPeachRecordListReq", "smMethod": "YardPeachRecordListResp"}, "215835": {"name": "S_YARDPB_MERCHANT_INFO", "cmMethod": "YardMerchantInfoReq", "smMethod": "YardMerchantInfoResp"}, "215836": {"name": "S_YARDPB_MERCHANT_EXCHANGE", "cmMethod": "YardMerchantExchangeReq", "smMethod": "YardMerchantExchangeResp"}, "215837": {"name": "S_YARDPB_CHAT_SWITCH", "cmMethod": "YardChatSwitchReq", "smMethod": "YardChatSwitchResp"}, "215839": {"name": "S_YARDPB_CHAT_ACTION", "cmMethod": "YardChatActionReq", "smMethod": "YardChatActionResp"}, "215840": {"name": "S_YARDPB_CHAT_RECORD", "cmMethod": "YardChatRecordReq", "smMethod": "YardChatRecordResp"}, "215841": {"name": "S_YARDPB_CHAT_RECORD_LIST", "cmMethod": "YardChatRecordListReq", "smMethod": "YardChatRecordListResp"}, "215842": {"name": "S_YARDPB_GIVE_LIKE", "cmMethod": "YardGiveLikeReq", "smMethod": "YardGiveLikeResp"}, "215843": {"name": "S_YARD_LOGIN_SYNC", "smMethod": "YardLoginSync"}, "215844": {"name": "S_YARDPB_DECORATEDATA_SYNC", "smMethod": "YardBuildInfoMsg"}, "215845": {"name": "S_YARDPB_PEACH_HELP_SYNC", "smMethod": "YardPeachHelpDataSync"}, "215847": {"name": "S_YARDPB_BUILD_BASICDATA_SYNC", "smMethod": "YardBaseMsgSync"}, "215848": {"name": "S_YARDPB_BUILD_STATUS_SYNC", "smMethod": "YardBuildInfoMsgSync"}, "215849": {"name": "S_YARDPB_BUILD_MAKE_NUM_SYNC", "smMethod": "YardMakeMsgSync"}, "215850": {"name": "S_YARDPB_DATA_SYNC", "smMethod": "YardRefreshDataSync"}, "215851": {"name": "S_YARD_BUILD_UP_SYNC", "smMethod": "YardBuildUpSync"}, "215806": {"name": "S_YARDPB_INVITE", "cmMethod": "YardInviteReq", "smMethod": "YardInviteResp"}, "215807": {"name": "S_YARDPB_ENTER_SHOP", "cmMethod": "YardEnterShopReq", "smMethod": "YardEnterShopResp"}, "215808": {"name": "S_YARDPB_REFRESH_SHOP", "cmMethod": "YardRefreshShopReq", "smMethod": "YardRefreshShopResp"}, "215852": {"name": "S_YARDPB_REPORT", "cmMethod": "YardReportReq", "smMethod": "YardReportResp"}, "216700": {"name": "S_MEI_TUAN_SUBSCRIBE_SYNC", "smMethod": "SyncMeiTuanSubscribeMsg"}, "216701": {"name": "S_MEI_TUAN_SUBSCRIBE_SUCCEED", "cmMethod": "MeiTuanSubscribeSucceedReq", "smMethod": "MeiTuanSubscribeSucceedResp"}, "216702": {"name": "S_MEI_TUAN_SUBSCRIBE_REWARD", "cmMethod": "MeiTuanSubscribeRewardReq", "smMethod": "MeiTuanSubscribeRewardResp"}, "216800": {"name": "S_MEI_TUAN_DAILY_SYNC", "smMethod": "SyncMeiTuanDailyMsg"}, "216801": {"name": "S_MEI_TUAN_DAILY_LOGIN", "cmMethod": "MeiTuanDailyLoginReq", "smMethod": "MeiTuanDailyLoginResp"}, "216802": {"name": "S_MEI_TUAN_DAILY_REWARD", "cmMethod": "MeiTuanDailyRewardReq", "smMethod": "MeiTuanDailyRewardResp"}, "216301": {"name": "S_YELLOW_MOUNTAIN_PASS_STAGE", "cmMethod": "YellowMountainPassStageReq", "smMethod": "YellowMountainPassStageResp"}, "216302": {"name": "S_YELLOW_MOUNTAIN_HANDBOOK_GET", "cmMethod": "YellowMountainHandbookGetReq", "smMethod": "YellowMountainHandbookGetResp"}, "217401": {"name": "S_MEMORY_COLLECT_SYNC_DATA", "smMethod": "MemoryCollectSyncData"}, "217402": {"name": "S_MEMORY_COLLECT_ENTER", "cmMethod": "MemoryCollectEnterReq", "smMethod": "MemoryCollectEnterResp"}, "217403": {"name": "S_MEMORY_COLLECT_RELEASE", "cmMethod": "MemoryCollectReleaseReq", "smMethod": "MemoryCollectReleaseResp"}, "217404": {"name": "S_MEMORY_COLLECT_CHANGE", "cmMethod": "MemoryCollectChangeReq", "smMethod": "MemoryCollectChangeResp"}, "217405": {"name": "S_MEMORY_COLLECT_CHANGELIST", "cmMethod": "MemoryCollectChangeListReq", "smMethod": "MemoryCollectChangeListResp"}, "217406": {"name": "S_MEMORY_COLLECT_OPENCARD", "cmMethod": "MemoryCollectOpenCardReq", "smMethod": "MemoryCollectOpenCardResp"}, "217407": {"name": "S_MEMORY_COLLECT_RECYCLE", "cmMethod": "MemoryCollectCardRecycleReq", "smMethod": "MemoryCollectCardRecycleResp"}, "217408": {"name": "S_MEMORY_COLLECT_CHANGESEASON", "cmMethod": "MemoryCollectChangeSeasonReq", "smMethod": "MemoryCollectChangeSeasonResp"}, "217409": {"name": "S_MEMORY_COLLECT_SEARCHPLAYER", "cmMethod": "MemoryCollectSearchPlayerReq", "smMethod": "MemoryCollectSearchPlayerResp"}, "217410": {"name": "S_MEMORY_COLLECT_USEABLECARDLIST", "cmMethod": "MemoryCollectUseAbleCardListReq", "smMethod": "MemoryCollectUseAbleCardListResp"}, "217411": {"name": "S_MEMORY_COLLECT_PLAYERCOLLECT_RECORDLIST", "cmMethod": "MemoryCollectPlayerCollectRecordListReq", "smMethod": "MemoryCollectPlayerCollectRecordListResp"}, "217412": {"name": "S_MEMORY_COLLECT_PLAYERSEASON_RECORDLIST", "cmMethod": "MemoryCollectPlayerSeasonListReq", "smMethod": "MemoryCollectPlayerSeasonListResp"}, "217413": {"name": "S_MEMORY_COLLECT_LOGIN_SYNC_DATA", "cmMethod": "", "smMethod": "MemoryCollectLoginSyncData"}, "217414": {"name": "S_MEMORY_COLLECT_LEAVE", "cmMethod": "MemoryCollectLeaveReq", "smMethod": "MemoryCollectLeaveResp"}, "217415": {"name": "S_MEMORY_COLLECT_CARD_GROUP_FULL", "smMethod": "MemoryCollectCardGroupFull"}, "217416": {"name": "S_MEMORY_COLLECT_SYNC_LOST_DATA", "smMethod": "MemoryCollectLoseChange"}, "217301": {"name": "S_GUARD_FAIRY_TREE_ENTER_ACTIVITY", "cmMethod": "GuardFairyTreeEnterReq", "smMethod": "GuardFairyTreeEnterResp"}, "217302": {"name": "S_GUARD_FAIRY_TREE_ENTER_GAME", "cmMethod": "GuardFairyTreeEnterGameReq", "smMethod": "GuardFairyTreeEnterGameResp"}, "217303": {"name": "S_GUARD_FAIRY_TREE_GOODS_PLACE", "cmMethod": "GuardFairyTreeGoodsPlaceReq", "smMethod": "GuardFairyTreeGoodsPlaceResp"}, "217304": {"name": "S_GUARD_FAIRY_TREE_GOODS_COMPOSE", "cmMethod": "GuardFairyTreeGoodsComposeReq", "smMethod": "GuardFairyTreeGoodsComposeResp"}, "217305": {"name": "S_GUARD_FAIRY_TREE_PLATE_EXPAND", "cmMethod": "GuardFairyTreePlateExpandReq", "smMethod": "GuardFairyTreePlateExpandResp"}, "217306": {"name": "S_GUARD_FAIRY_TREE_UPDATE_SHOPS_GOODS", "cmMethod": "GuardFairyTreeRefreshShopsReq", "smMethod": "GuardFairyTreeRefreshShopsResp"}, "217307": {"name": "S_GUARD_FAIRY_TREE_UPDATE_GOODS_AD", "cmMethod": "GuardFairyTreeUpdateAdGoodsReq", "smMethod": "GuardFairyTreeUpdateAdGoodsResp"}, "217308": {"name": "S_GUARD_FAIRY_TREE_SAVE_GOODS", "cmMethod": "GuardFairyTreeGoodsSaveReq", "smMethod": "GuardFairyTreeGoodsSaveResp"}, "217309": {"name": "S_GUARD_FAIRY_TREE_FIGHT_RESULT", "cmMethod": "GuardFairyTreeFightResultReq", "smMethod": "GuardFairyTreeFightWinResp"}, "217310": {"name": "S_GUARD_FAIRY_TREE_SELECT_BUFF", "cmMethod": "GuardFairyTreeSelectBuffReq", "smMethod": "GuardFairyTreeSelectBuffResp"}, "217311": {"name": "S_GUARD_FAIRY_TREE_RANDOM_BUFF_LIST", "cmMethod": "GuardFairyTreeRandomBuffListReq", "smMethod": "GuardFairyTreeRandomBuffListResp"}, "217312": {"name": "S_GUARD_FAIRY_TREE_START_BATTLE", "cmMethod": "GuardFairyTreeStartBattleReq", "smMethod": "GuardFairyTreeStartBattleResp"}, "217313": {"name": "S_GUARD_FAIRY_TREE_SET_WEAPON", "cmMethod": "GuardFairyTreeSetWeaponReq", "smMethod": "GuardFairyTreeSetWeaponResp"}, "217200": {"name": "S_REBORN_TRIAL_ENTER_ACTIVITY_REQ", "cmMethod": "RebornTrialEnterActivityReq", "smMethod": "RebornTrialEnterActivityResp"}, "217201": {"name": "S_REBORN_TRIAL_ENTER_REQ", "cmMethod": "RebornTrialEnterReq", "smMethod": "RebornTrialEnterResp"}, "217202": {"name": "S_REBORN_TRIAL_GROUP_INFO_REQ", "cmMethod": "RebornTrialGroupInfoReq", "smMethod": "RebornTrialGroupInfoResp"}, "217203": {"name": "S_REBORN_TRIAL_WORSHIP", "cmMethod": "RebornTrialWorshipReq", "smMethod": "RebornTrialWorshipResp"}, "217204": {"name": "S_REBORN_TRIAL_UP_LEVEL_REQ", "cmMethod": "RebornTrialUpLevelReq", "smMethod": "RebornTrialUpLevelResp"}, "217205": {"name": "S_REBORN_TRIAL_EXPLORE_REQ", "cmMethod": "RebornTrialExploreReq", "smMethod": "RebornTrialExploreResp"}, "217206": {"name": "S_REBORN_TRIAL_SELECT_OPTION_REQ", "cmMethod": "RebornTrialSelectOptionReq", "smMethod": "RebornTrialSelectOptionResp"}, "217207": {"name": "S_REBORN_TRIAL_USE_MAGIC_REQ", "cmMethod": "RebornTrialUseMagicReq", "smMethod": "RebornTrialUseMagicResp"}, "217208": {"name": "S_REBORN_TRIAL_USE_ITEM_REQ", "cmMethod": "RebornTrialUseItemReq", "smMethod": "RebornTrialUseItemResp"}, "217209": {"name": "S_REBORN_TRIAL_SKIP_EVENT_REQ", "cmMethod": "RebornTrialSkipEventReq", "smMethod": "RebornTrialSkipEventResp"}, "217211": {"name": "S_REBORN_TRIAL_UNION_ROUND_SUMMARY_REQ", "cmMethod": "RebornTrialUnionRoundSummaryReq", "smMethod": "RebornTrialUnionRoundSummaryResp"}, "217236": {"name": "S_REBORN_TRIAL_GET_RED_POINT_REQ", "cmMethod": "RebornTrialRedPointReq", "smMethod": "RebornTrialRedPointResp"}, "217220": {"name": "S_REBORN_TRIAL_BONUS_REQ", "cmMethod": "RebornTrialBonusReq", "smMethod": "RebornTrialBonusResp"}, "217221": {"name": "S_REBORN_TRIAL_GET_BONUS_RESULT_REQ", "cmMethod": "RebornTrialGetBonusResultReq", "smMethod": "RebornTrialGetBonusResultResp"}, "217222": {"name": "S_REBORN_TRIAL_GET_BONUS_ITEM_REQ", "cmMethod": "RebornTrialGetBonusItemReq", "smMethod": "RebornTrialGetBonusItemResp"}, "217223": {"name": "S_REBORN_TRIAL_SEND_BONUS_ITEM_REQ", "cmMethod": "RebornTrialSendBonusItemReq", "smMethod": "RebornTrialSendBonusItemResp"}, "217224": {"name": "S_REBORN_TRIAL_BONUS_RECORD_REQ", "cmMethod": "RebornTrialBonusRecordReq", "smMethod": "RebornTrialBonusRecordResp"}, "217225": {"name": "S_REBORN_TRIAL_GUESS_PLAYERS_REQ", "cmMethod": "RebornTrialGuessPlayersReq", "smMethod": "RebornTrialGuessPlayersResp"}, "217226": {"name": "S_REBORN_TRIAL_GUESS_DATA_REQ", "cmMethod": "RebornTrialGuessDataReq", "smMethod": "RebornTrialGuessDataResp"}, "217227": {"name": "S_REBORN_TRIAL_GUESS_SELECT_REQ", "cmMethod": "RebornTrialGuessSelectReq", "smMethod": "RebornTrialGuessSelectResp"}, "217228": {"name": "S_REBORN_TRIAL_GUESS_REWARD_REQ", "cmMethod": "RebornTrialGuessRewardReq", "smMethod": "RebornTrialGuessRewardResp"}, "217230": {"name": "S_REBORN_TRIAL_BATTLE_ENTER_REQ", "cmMethod": "RebornTrialChallengeListReq", "smMethod": "RebornTrialChallengeListResp"}, "217232": {"name": "S_REBORN_TRIAL_BATTLE_HEART_REQ", "cmMethod": "RebornTrialChallengeHeartBeatReq", "smMethod": "RebornTrialChallengeHeartBeatResp"}, "217233": {"name": "S_REBORN_TRIAL_BATTLE_UNIONPLAYER_REQ", "cmMethod": "RebornTrialUnionPlayerListReq", "smMethod": "RebornTrialUnionPlayerListResp"}, "217231": {"name": "S_REBORN_TRIAL_BATTLE_CHALLENGE_REQ", "cmMethod": "RebornTrialChallengeReq", "smMethod": "RebornTrialChallengeResp"}, "217250": {"name": "S_REBORN_TRIAL_BATTLE_NOTIC_SYNC", "smMethod": "RebornTrialChallengeInfoSync"}, "217234": {"name": "S_REBORN_TRIAL_REPORT", "cmMethod": "RebornTrialReportReq", "smMethod": "RebornTrialReportResp"}, "217235": {"name": "S_REBORN_TRIAL_REPORT_DETAIL", "cmMethod": "RebornTrialReportDetailReq", "smMethod": "RebornTrialReportDetailResp"}, "217210": {"name": "S_REBORN_TRIAL_UNION_SCORE", "cmMethod": "RebornTrialUnionScoreReq", "smMethod": "RebornTrialUnionScoreResp"}, "216901": {"name": "S_MATCH_3_ENTER_GAME", "cmMethod": "SecretRealmSealedDemonEnterGameReq", "smMethod": "SecretRealmSealedDemonEnterGameResp"}, "216902": {"name": "S_MATCH_3_ELIMINATE_TIPS", "cmMethod": "SecretRealmSealedDemonEliminateTipsReq", "smMethod": "SecretRealmSealedDemonEliminateTipsResp"}, "216903": {"name": "S_MATCH_3_ELIMINATE_BLOCK", "cmMethod": "SecretRealmSealedDemonEliminateBlockReq", "smMethod": "SecretRealmSealedDemonEliminateBlockResp"}, "216904": {"name": "S_MATCH_3_USE_ENERGY_ITEM", "cmMethod": "SecretRealmSealedDemonUseEnergyItemReq", "smMethod": "SecretRealmSealedDemonUseEnergyItemResp"}, "216905": {"name": "S_MATCH_3_USE_BOMB_ITEM", "cmMethod": "SecretRealmSealedDemonUseBombItemReq", "smMethod": "SecretRealmSealedDemonUseBombItemResp"}, "216906": {"name": "S_MATCH_3_USE_ELIMINATE_ITEM", "cmMethod": "SecretRealmSealedDemonUseEliminateItemReq", "smMethod": "SecretRealmSealedDemonUseEliminateItemResp"}, "216907": {"name": "S_MATCH_3_USE_STEP_ITEM", "cmMethod": "SecretRealmSealedDemonUseStepItemReq", "smMethod": "SecretRealmSealedDemonUseStepItemResp"}, "216908": {"name": "S_MATCH_3_GET_RANK", "cmMethod": "SecretRealmSealedDemonGetRankReq", "smMethod": "SecretRealmSealedDemonGetRankResp"}, "216909": {"name": "S_MATCH_3_RESTART", "cmMethod": "SecretRealmSealedDemonRestartReq", "smMethod": "SecretRealmSealedDemonRestartResp"}, "217701": {"name": "S_LIVE_SHOW_NOTIFY_REQ", "cmMethod": "LiveShowNotifyReqMsg", "smMethod": "LiveShowNotifyRespMsg"}, "218301": {"name": "S_GOD_PET_BORN_ENTER", "cmMethod": "GodPetBornEnterReq", "smMethod": "GodPetBornEnterResp"}, "217990": {"name": "S_CHAT_RED_PACKET_OPEN", "cmMethod": "ChatRedPacketOpenReq", "smMethod": "ChatRedPacketOpenResp"}, "217991": {"name": "S_CHAT_RED_PACKET_DRAW", "cmMethod": "ChatRedPacketDrawReq", "smMethod": "ChatRedPacketDrawResp"}, "217992": {"name": "S_CHAT_RED_PACKET_STATE_REQ", "cmMethod": "ChatRedPacketStateReq", "smMethod": "ChatRedPacketStateSync"}, "217899": {"name": "S_UNION_HONOR_HALL_LOGIN_SYNC", "smMethod": "UnionHonorHallLoginSync"}, "217801": {"name": "S_UNION_HONOR_HALL_ENTER", "cmMethod": "UnionHonorHallEnterReq", "smMethod": "UnionHonorHallEnterResp"}, "217802": {"name": "S_UNION_HONOR_HALL_GET_ACHIEVE", "cmMethod": "UnionHonorHallGetAchieveReq", "smMethod": "UnionHonorHallGetAchieveResp"}, "217803": {"name": "S_UNION_HONOR_HALL_GET_ACHIEVE_DETAIL", "cmMethod": "UnionHonorHallGetAchieveRewardDetailReq", "smMethod": "UnionHonorHallGetAchieveRewardDetailResp"}, "217804": {"name": "S_UNION_HONOR_HALL_GET_ACHIEVE_REWARD", "cmMethod": "UnionHonorHallGetAchieveRewardReq", "smMethod": "UnionHonorHallGetAchieveRewardResp"}, "217805": {"name": "S_UNION_HONOR_HALL_HISTORY_HONOR", "cmMethod": "UnionHonorHallHistoryHonorReq", "smMethod": "UnionHonorHallHistoryHonorResp"}, "217806": {"name": "S_UNION_HONOR_HALL_USE_TROPHY", "cmMethod": "UnionHonorHallUseTrophyReq", "smMethod": "UnionHonorHallUseTrophyResp"}, "217807": {"name": "S_UNION_HONOR_HALL_GET_PEAK_RANK", "cmMethod": "UnionHonorHallGetPeakRankReq", "smMethod": "UnionHonorHallGetPeakRankResp"}, "217808": {"name": "S_UNION_HONOR_HALL_PEAK_RANK_WORSHIP", "cmMethod": "UnionHonorHallPeakRankWorshipReq", "smMethod": "UnionHonorHallPeakRankWorshipResp"}, "217809": {"name": "S_UNION_HONOR_HALL_PEAK_RANK_GET_REWARD", "cmMethod": "UnionHonorHallPeakRankGetRewardReq", "smMethod": "UnionHonorHallPeakRankGetRewardResp"}, "217810": {"name": "S_UNION_HONOR_HALL_GET_HONOE_RANK", "cmMethod": "UnionHonorHallGetHonorRankReq", "smMethod": "UnionHonorHallGetHonorRankResp"}, "217999": {"name": "S_UNION_CEREMONY_LOGIN_SYNC", "smMethod": "UnionCeremonyLoginSync"}, "217901": {"name": "S_UNION_CEREMONY_GET_LIST", "cmMethod": "UnionCeremonyListReq", "smMethod": "UnionCeremonyListResp"}, "217902": {"name": "S_UNION_CEREMONY_VISIT", "cmMethod": "UnionCeremonyVisitReq", "smMethod": "UnionCeremonyVisitResp"}, "217903": {"name": "S_UNION_CEREMONY_CONGRATULATIONS", "cmMethod": "UnionCeremonyCongratulationsReq", "smMethod": "UnionCeremonyCongratulationsResp"}, "217904": {"name": "S_UNION_CEREMONY_CONGRATULATIONS_GET", "cmMethod": "UnionCeremonyCongratulationsGetReq", "smMethod": "UnionCeremonyCongratulationsGetResp"}, "217905": {"name": "S_UNION_CEREMONY_OVER", "cmMethod": "UnionCeremonyOverReq", "smMethod": "UnionCeremonyOverResp"}}