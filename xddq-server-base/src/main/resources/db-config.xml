<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE configuration PUBLIC "-//mybatis.org//DTD Config 3.0//EN" "http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>
    <settings>
        <setting name="defaultStatementTimeout" value="60" />
    </settings>
    <!-- c3p0连接池环境配置 -->
    <environments default="development">
        <environment id="development">
            <transactionManager type="JDBC" />
            <dataSource type="xddq.db.datasource.DruidDataSourceFactory">
                <property name="url"
                          value="************************************************************************************" />
                <property name="username" value="111" />
                <property name="password" value="111" />

                <property name="maxActive" value="20" />
                <property name="initialSize" value="1" />
                <property name="maxWait" value="60000" />
                <property name="minIdle" value="1" />

                <property name="timeBetweenEvictionRunsMillis" value="60000" />
                <property name="minEvictableIdleTimeMillis" value="300000" />

                <property name="validationQuery" value="SELECT 1" />

                <property name="testWhileIdle" value="true" />
                <property name="testOnBorrow" value="false" />
                <property name="testOnReturn" value="false" />

                <property name="poolPreparedStatements" value="true" />
                <property name="maxOpenPreparedStatements" value="20" />

                <property name="asyncInit" value="true" />
            </dataSource>
        </environment>
    </environments>

    <mappers>
        <mapper resource="xddq/db/local/sqlmap/player.xml"/>
        <mapper resource="xddq/db/local/sqlmap/union.xml"/>
    </mappers>
</configuration>