// RspLoginMsg 登錄結果信息
    message RspLoginMsg{
        required int32 ret = 1; 錯誤碼 0-成功
        optional TimeDataMsg timeData = 2; 時間信息
    }
// RspConfigMsg 配置信息同步
message ConfigParam {
    required string key = 1; 配置key
    required string value = 2; 配置值
}
// GoodsShieldSync 物品同步信息
message GoodsShieldSync{
    repeated int32 shieldList = 1;
}
// MallRechargeIdShieldSync(商城充值同步，估計同步充值過的商品ID)
message MallRechargeIdShieldSync{
    repeated int32 shieldMallRechargeIdList = 1;
}
// ActivityShieldSync(活動同步，估計同步開放中的活動)
// message ActivityShieldData {
//    optional int32 type =1; // 活動類型
//    optional int32 childType =2; // 活動子類型
//    optional int32 platformType =3; //平臺類型，所屬平臺
//}
// SystemShieldSync(系統同步？)
message SystemShieldSync {
    repeated int32 shieldList = 1;
}
// ActivityPosterShieldSync 活動公佈同步（界面展示哪些活動？）
message ActivityPosterShieldSync{
    repeated int32 posterId = 1;
}
// PlayerDataMsg 玩家數據
// message PlayerDataMsg {
//    optional int64  playerId = 1; 玩家ID
//    optional string nickName = 2; 呢稱
//    optional int32 roleId = 3; 賬號ID
//    optional int64 serverId = 4; 服務器ID
//    optional int64 lastLoginTime = 5; 最後登錄時間
//    optional string playerBelong = 6; 玩家國家
//    optional int32 hideAddress = 7;
//    optional int64 unionId = 8;       未知
//    optional string myInviteCode = 9; 邀請碼
//    optional string wxHeadUrl = 10;   微信頭像地址
//    optional int32 changeNameTimes = 11; 改名次數
//    optional PlayerEnterType enterType = 12;
//    optional int64 registerTime = 13; 注冊時間
//    optional int32 isSubscribeMail = 15; 訂閲郵件？
//    optional int32 changeNums = 16;
//    optional int64 startTime = 17; 開始時間
//}
//enum PlayerEnterType {
//    Normal = 0; 普通進入
//    DouluoAd = 1; 斗罗引流？
//  }
// SyncBagMsg 同步背包
//message SyncBagMsg{
//        repeated BagDataMsg bagData = 1;
//    }
//
//message BagDataMsg{
//        required int32 propId = 1; 物品id
//        required string num = 2;   數量
//    }
//MessageSubscribeInfo 消息訂閲信息
//message MessageSubscribeInfo {
//    repeated MessageSubscribeConfig messageSubscribeConfigList = 1;
//    optional MessageSubscribePlayerData playerData = 2;
//}
//message MessageSubscribeConfig {
//    optional int32 id = 1;
//    optional string templateId = 2; 模板？
//    optional string title = 3; 標題
//    optional string content = 4; 内容
//    optional int32 pushRate = 5; 推送頻率
//    optional string dyTemplateId = 6;
//    optional string zfbTemplateId = 7;
//}
// SyncBlackPlayerIdListMsg 黑名單
//message SyncBlackPlayerIdListMsg {
//        repeated int64 playerId = 1; 玩家ID
//    }
// WorldMessageListMsg 世界消息記錄
//message WorldMessageTempMsg {
//        required int32   type =1; 類型
//        required int64   sendTime =2; 發送時間
//        optional string  content =3; 内容
//        optional int64   playerId =4; 發送者
//        optional PlayerBaseDataMsg playerBaseData =5; 發送者基礎信息
//        optional int32   activityId =6; 活動id
//        optional int32 contentType = 7; 内容類型
//        optional string extraParams = 8; 參數
//        optional bytes  extraData = 9; 額外數據
//        optional string logId = 10; 日志id
//        optional bool  Reported = 11;
//        optional string ip = 12; 發送者ip
//        optional AtPlayerInfo atPlayerInfo = 13; @玩家
//        optional int64 friendPlayerId = 14; 好友id
//    }
// TaskDataListMsg 任務列表
     message TaskDataTempMsg{
           required int32   taskId =1; 任務id
           required string  value =2;  任務完成數量
           required int32   state =3;  任務狀態
       }
// SystemUnlockSync 解鎖系統
    message SystemUnlockSync{
            optional string unlockInfo = 1; 解鎖信息
            optional string getRewardInfo = 2; 領取獎勵信息
        }
// PlayerAttributeDataMsg 玩家屬性
    message PlayerAttributeDataMsg {
        required int32 realmsId = 1;
        required string exp = 2; 經驗值
        optional int64 fightValue = 3; 戰鬥力
        repeated AttributeDataMsg playerAttributeDataList = 4; 玩家屬性
        repeated EquipmentDataMsg equipmentList = 5; 裝備信息
        optional int32 useSeparationIdx = 6; 分解？
        optional int32 soaringState = 7; 飛升狀態
        optional int32 resetSoaringTimes = 8; 重置飛升次數
        optional int32 todaySoaringTimes = 9; 今天飛升次數
    }
    message AttributeDataMsg{
        required int32 type = 1; 屬性類型
        required int64 value = 2; 屬性值
    }
    message EquipmentDataMsg{
        required int64 id = 1;
        required int32 equipmentId = 2; 裝備配置id
        required int32 level = 3;   裝備等級
        required int32 quality = 4; 裝備品質
        repeated AttributeDataMsg attributeList = 5; 裝備屬性
        required int32 src = 6; 來源
    }

// DreamDataMsg 樹升級信息？{"dreamLv": 1, "dreamLvUpEndTime": "0", "freeSpeedUpCdEndTime": "1728293574338", "freeSpeedUpTimes": 0, "dailyGoldenPeachCost": 0}
    message DreamDataMsg {
        optional int32 dreamLv = 1;  // 樹等級？
        repeated UnDealEquipmentDataMsg unDealEquipmentDataMsg = 2;
        optional int64 dreamLvUpEndTime = 3; // 升級結束時間
        optional int64 freeSpeedUpCdEndTime = 4; // 免費升級CD結束時間
        optional int32 freeSpeedUpTimes = 5;     // 免費升級次數
        optional int32 dailyGoldenPeachCost = 6;    //本日元寶花費
    }

    message UnDealEquipmentDataMsg { //未獲得的裝備信息
            required EquipmentDataMsg unDealEquipmentData = 1; 裝備基礎數據
            repeated AttributeDataMsg playerAttributeDataList = 2; 裝備屬性數據
            required int64 fightValue = 3; 戰鬥力
        }
// PlayerCharaDataMsg 裝備相關？{"equipHeadIcon": 143001, "equipAppearanceId": 143001, "haveSpecialChara": [{"charaId": 144075, "expireTime": "0", "lv": 1}, {"charaId": 143001, "expireTime": "0"}], "titleId": 0, "equipHeadIconFrame": 0, "equipChatBubble": 0, "equipAtkEffect": 0, "medalId": 0}
    message PlayerCharaDataMsg {
        optional int32 equipHeadIcon = 1;
        optional int32 equipAppearanceId = 2;
        repeated PlayerHaveCharaMsg haveSpecialChara = 3;
        optional int32 titleId = 4;
        optional int32 equipHeadIconFrame = 5;
        optional int32 equipChatBubble = 6;
        optional int32 equipAtkEffect = 7; 裝備攻擊特效
        optional int32 medalId = 8;
        repeated AppearanceCollect appearanceCollect = 9;
    }
    message PlayerHaveCharaMsg {
        required int32 charaId = 1;
        required int64 expireTime = 2;
        optional int32 lv = 3;
    }
    message AppearanceCollect{
        required int32 type = 1;
        required int32 id = 2;
    }
// TitleSyncMsg 頭銜同步信息
    message TitleDataMsg{
        required int32 titleId = 1; 頭銜id
        required int64 expiredTime = 2; 過期時間
    }
// MedalSyncMsg 獎章同步信息
    message MedalDataMsg{
        required int32 medalId = 1; 獎章id
        required int64 expiredTime = 2; 過期時間
    }
// GetCrossUnionGroupServersResp 跨服服務器組？
    message GetCrossUnionGroupServersResp {
      optional int32 ret = 1; //錯誤碼
      repeated int64 serverList = 2; //服務器列表
    }
// NotifyPlayerGradeMsg 通知玩家級別
    message NotifyPlayerGradeMsg {
       optional PlayerGradeEnum myGrade = 1;
    }
    enum PlayerGradeEnum{
        D = 0;
        C = 1;
        B = 2;
        A = 3;
        S = 4;
    }
// PushActivityList 活動信息
    message PushActivityList {
        repeated ActivityMainConfig mainConfig = 1; 活動主配置
        repeated ActivityPosterConfig posterConfig= 2; 活動公佈配置
        repeated ActivityParamConfig paramConfig = 3; 活動參數配置
    }
    message ActivityMainConfig {
        optional int32 activityId = 1; 活動id
        optional int32 type = 2; 活動類型
        optional int32 childType = 3; 活動子類型
        optional int64 beginShowTime = 4; 開始展示時間
        optional int64 endShowTime = 5; 結束展示時間
        optional int64 beginTime = 6; 開始時間
        optional int64 endTime = 7; 結束時間
        repeated int64 serverId = 8;  開放服務器id
        optional int32 groupType = 9; 活動組類型
    }

    message ActivityPosterConfig {
        required int32 activityId = 1;
        required int32 postId = 2; 展示id
        required string showReward = 3; 獎勵展示
        required string activityJump = 4; 跳轉地址
        optional string param = 5; 參數
    }

    message ActivityParamConfig {
        optional int32 activityId = 1; 活動id
        optional string param = 2; 參數
        optional string value = 3; 值
    }

// ActivityCommonDataListSync 活動數據同步
    message ActivityCommonData {
        optional int32 activityId = 1; 活動id
        repeated ActivityConditionData conditionDataList = 2; 活動條件
        repeated ActivityMallBuyCountData mallBuyCountList = 3; 活動商城購買數量
        optional ActivityDetailConfig detailConfig = 4;
        optional bytes playerData = 5;  玩家數據
        repeated ActivityScoreDataMsg activityScoreDataMsg = 6; 活動記錄數據
        repeated UnionServerIdData unionServerIdList = 7; 跨服活動服務器裏欸包
        optional int64 unionLockTime = 8; 活動鎖定時間
    }
// PalaceSyncMsg 皇宮信息？玩法沒玩過 {"outerWorship": false, "sendGiftTimes": 1, "achievementData": {"num": 0}}
// AllClientDataSync 客戶端保存信息
// UnionFightApplyDataSync 跨服戰鬥信息？
// SyncUnionDuelMsg 同步跨服信息
message SyncUnionDuelMsg{
    optional int32 ret = 1; 錯誤碼
    optional bool hasQualification = 2; 是否擁有資格
    optional bool masterOrDeputy = 3; 是主機還是副機
    optional int32 state = 4;  戰鬥結束狀態
    optional UnionDuelBattleResultMsg myUnion = 5; 我的跨服戰鬥結果信息
    optional UnionDuelBattleResultMsg oppoUnion = 6; 對手跨服戰鬥結果信息?
    optional int32 battleState = 7;
}
// ReceiveSdkRewardSyn sdk獎勵領取同步
// SkyWarDataLoginSync 某種玩法數據信息？玩法沒玩過 {"battleTimes": 0, "isJoinWar": false, "waitNextWeek": true}
// HolyLandBattleApplyDataSync 某種玩法數據信息？玩法沒玩過 {"ret": 0, "isApply": false, "hasQuality": false, "isOpen": false, "scheduleTimestamp": ["1728057600000", "1728662400000", "1729267200000", "1729872000000", "1729958400000"], "notFoundCrossId": false}
// LoginOverMsg 登錄結束信息