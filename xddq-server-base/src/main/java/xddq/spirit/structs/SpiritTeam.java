package xddq.spirit.structs;

import java.util.concurrent.ConcurrentHashMap;

public class SpiritTeam {

    private int id;

    private ConcurrentHashMap<Integer, Integer> spirits = new ConcurrentHashMap<>();

    public int getId() {
        return id;
    }

    public SpiritTeam setId(int id) {
        this.id = id;
        return this;
    }

    public ConcurrentHashMap<Integer, Integer> getSpirits() {
        return spirits;
    }

    public SpiritTeam setSpirits(ConcurrentHashMap<Integer, Integer> spirits) {
        this.spirits = spirits;
        return this;
    }
}
