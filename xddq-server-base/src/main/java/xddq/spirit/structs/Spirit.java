package xddq.spirit.structs;

import xddq.structs.GameObject;

public class Spirit extends GameObject {

    private int level;

    private int equipLinkageId;

    private transient int skillLevel;

    public int getLevel() {
        return level;
    }

    public Spirit setLevel(int level) {
        this.level = level;
        return this;
    }

    public int getEquipLinkageId() {
        return equipLinkageId;
    }

    public Spirit setEquipLinkageId(int equipLinkageId) {
        this.equipLinkageId = equipLinkageId;
        return this;
    }

    public int getSkillLevel() {
        return skillLevel;
    }

    public Spirit setSkillLevel(int skillLevel) {
        this.skillLevel = skillLevel;
        return this;
    }

    public Spirit clone() {
        Spirit spirit = new Spirit();
        spirit.setId(this.getId());
        spirit.setModelId(this.getModelId());
        spirit.setLevel(this.getLevel());
        spirit.setEquipLinkageId(this.getEquipLinkageId());
        return spirit;
    }
}
