package xddq.spirit.structs;

import java.util.concurrent.ConcurrentHashMap;

public class SpiritData {

    private ConcurrentHashMap<Integer, Spirit> spirits = new ConcurrentHashMap<>();

    private ConcurrentHashMap<Integer, SpiritTeam> teams = new ConcurrentHashMap<>();

    private ConcurrentHashMap<Integer, SpiritCombine> combines = new ConcurrentHashMap<>();

    private boolean pieceShopOpen = false;

    private int spiritLevelState = 0;

    public boolean isPieceShopOpen() {
        return pieceShopOpen;
    }

    public void setPieceShopOpen(boolean pieceShopOpen) {
        this.pieceShopOpen = pieceShopOpen;
    }

    public int getSpiritLevelState() {
        return spiritLevelState;
    }

    public void setSpiritLevelState(int spiritLevelState) {
        this.spiritLevelState = spiritLevelState;
    }

    public ConcurrentHashMap<Integer, SpiritCombine> getCombines() {
        return combines;
    }

    public void setCombines(ConcurrentHashMap<Integer, SpiritCombine> combines) {
        this.combines = combines;
    }

    // 上阵队伍ID
    private int battleTeamNo = 1;
    // 总抽卡次数
    private int drawTimesTotal;
    // 最后广告时间
    private long lastAdTime;
    // 抽卡碎片次数
    private int drawPieceTimes;
    // 抽卡整个次数
    private int drawFullTimes;


    // 必出神话次数
    private int drawTimes;
    // 获得神话次数
    private int protectTimes;


    public ConcurrentHashMap<Integer, Spirit> getSpirits() {
        return spirits;
    }

    public SpiritData setSpirits(ConcurrentHashMap<Integer, Spirit> spirits) {
        this.spirits = spirits;
        return this;
    }

    public ConcurrentHashMap<Integer, SpiritTeam> getTeams() {
        return teams;
    }

    public int getDrawTimes() {
        return drawTimes;
    }

    public void setDrawTimes(int drawTimes) {
        this.drawTimes = drawTimes;
    }

    public SpiritData setTeams(ConcurrentHashMap<Integer, SpiritTeam> teams) {
        this.teams = teams;
        return this;
    }

    public int getProtectTimes() {
        return protectTimes;
    }

    public void setProtectTimes(int protectTimes) {
        this.protectTimes = protectTimes;
    }

    public int getBattleTeamNo() {
        return battleTeamNo;
    }

    public SpiritData setBattleTeamNo(int battleTeamNo) {
        this.battleTeamNo = battleTeamNo;
        return this;
    }

    public int getDrawTimesTotal() {
        return drawTimesTotal;
    }

    public SpiritData setDrawTimesTotal(int drawTimesTotal) {
        this.drawTimesTotal = drawTimesTotal;
        return this;
    }

    public long getLastAdTime() {
        return lastAdTime;
    }

    public SpiritData setLastAdTime(long lastAdTime) {
        this.lastAdTime = lastAdTime;
        return this;
    }

    public int getDrawPieceTimes() {
        return drawPieceTimes;
    }

    public SpiritData setDrawPieceTimes(int drawPieceTimes) {
        this.drawPieceTimes = drawPieceTimes;
        return this;
    }

    public int getDrawFullTimes() {
        return drawFullTimes;
    }

    public SpiritData setDrawFullTimes(int drawFullTimes) {
        this.drawFullTimes = drawFullTimes;
        return this;
    }
}
