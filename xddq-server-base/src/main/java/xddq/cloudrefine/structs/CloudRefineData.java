package xddq.cloudrefine.structs;

import xddq.pupil.structs.AttributeData;

import java.util.concurrent.ConcurrentHashMap;

//message PlayerCloudDataMsg {
//        optional int32 equipCloudId = 1;
//        repeated CloudDataMsg cloudDataMsg = 2;
//        required int32 stage = 3;
//        required int32 lv = 4;
//        required int32 exp = 5;
//        repeated CloudSkinDataMsg cloudSkinDataMsg = 6;
//        optional int32 equipCloudSkinId = 7;
//    }
public class CloudRefineData {

    private int bigType;
    private int starLv;
    private int refineLv;

    private int exp;

    private int cur;

    private ConcurrentHashMap<Integer, CloudRefineSkill> refineHoleSkills = new ConcurrentHashMap<>();

    private ConcurrentHashMap<Integer, AttributeData> cloudRefineAttrDataList = new ConcurrentHashMap<>();

    public int getBigType() {
        return bigType;
    }

    public void setBigType(int bigType) {
        this.bigType = bigType;
    }

    public int getStarLv() {
        return starLv;
    }

    public void setStarLv(int starLv) {
        this.starLv = starLv;
    }

    public int getRefineLv() {
        return refineLv;
    }

    public void setRefineLv(int refineLv) {
        this.refineLv = refineLv;
    }

    public int getExp() {
        return exp;
    }

    public void setExp(int exp) {
        this.exp = exp;
    }

    public ConcurrentHashMap<Integer, CloudRefineSkill> getRefineHoleSkills() {
        return refineHoleSkills;
    }

    public void setRefineHoleSkills(ConcurrentHashMap<Integer, CloudRefineSkill> refineHoleSkills) {
        this.refineHoleSkills = refineHoleSkills;
    }

    public ConcurrentHashMap<Integer, AttributeData> getCloudRefineAttrDataList() {
        return cloudRefineAttrDataList;
    }

    public void setCloudRefineAttrDataList(ConcurrentHashMap<Integer, AttributeData> cloudRefineAttrDataList) {
        this.cloudRefineAttrDataList = cloudRefineAttrDataList;
    }

    public int getCur() {
        return cur;
    }

    public void setCur(int cur) {
        this.cur = cur;
    }
}
