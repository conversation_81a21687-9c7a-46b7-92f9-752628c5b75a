package xddq.consts;

import java.util.concurrent.ConcurrentHashMap;

public class BlockId {
    public static final ConcurrentHashMap<Integer, Integer> BAN_PET = new ConcurrentHashMap();
    public static final ConcurrentHashMap<Integer, Integer> BAN_SPIRIT = new ConcurrentHashMap();
    public static final ConcurrentHashMap<Integer, Integer> BAN_MAGIC = new ConcurrentHashMap();
    public static final ConcurrentHashMap<Integer, Integer> BAN_TALENT = new ConcurrentHashMap(); // 屏蔽的灵脉

    static {
        BAN_PET.put(115023, 1);
        BAN_PET.put(115024, 1);
        BAN_PET.put(115025, 1);
        BAN_PET.put(115026, 1);
        BAN_PET.put(115027, 1);
        BAN_PET.put(115028, 1);
        BAN_PET.put(115029, 1);
        BAN_PET.put(115030, 1);
        BAN_PET.put(115031, 1);
        BAN_PET.put(115032, 1);
        BAN_PET.put(115033, 1);
        BAN_PET.put(115034, 1);
        BAN_PET.put(115035, 1);
        BAN_PET.put(115037, 1);
        BAN_PET.put(115038, 1);

        BAN_SPIRIT.put(125023, 1);
        BAN_SPIRIT.put(125024, 1);
        BAN_SPIRIT.put(125025, 1);
        BAN_SPIRIT.put(125026, 1);
        BAN_SPIRIT.put(125027, 1);

        BAN_MAGIC.put(1407, 1);
        BAN_MAGIC.put(1408, 1);
        BAN_MAGIC.put(5001, 1);
        BAN_MAGIC.put(5002, 1);

        //BAN_TALENT.put();
    }
}
