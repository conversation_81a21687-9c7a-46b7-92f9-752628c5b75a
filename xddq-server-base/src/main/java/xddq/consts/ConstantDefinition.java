package xddq.consts;

import io.netty.util.AttributeKey;
import xddq.player.structs.Player;

public class ConstantDefinition {

    public static final AttributeKey<String> SESSION_IP = AttributeKey.newInstance("sessionIp");

    public static final AttributeKey<Long> USER_ID = AttributeKey.newInstance("userId");

    public static final AttributeKey<Long> PLAYER_ID = AttributeKey.newInstance("playerId");

    public static final AttributeKey<Long> HEART_TIME = AttributeKey.newInstance("heartTime");
    public static final AttributeKey<Long> HEART_COUNT = AttributeKey.newInstance("heartCount");

    public static final AttributeKey<Player> PLAYER = AttributeKey.newInstance("player");

}
