package xddq.consts;

import com.google.protobuf.Message;
import com.google.protobuf.util.JsonFormat;

import java.io.FileReader;
import java.io.IOException;
import java.util.HashMap;
import java.util.HashSet;
import java.util.concurrent.ConcurrentHashMap;

public class Global {
    public static int KAIKOU = 100;
    public static int MAX_PKG_CNT = 30;
    public static int MAX_PKG_TIME = 5;
    public static boolean BAN_CHAT = false;
    public static boolean PRINT_LOG = false; // tmpTest
    // nickName -> playerId
    public static ConcurrentHashMap<String, Long> name2IdMap = new ConcurrentHashMap<>();

    // playerId -> nickName
    public static ConcurrentHashMap<Long, String> id2NameMap = new ConcurrentHashMap<>();

    // key: playerId
    public static ConcurrentHashMap<Long, Integer> TRACK_MAP = new ConcurrentHashMap<>();

    public static final String GM_CODE = "OoB8a8b4s8";

    public static boolean DEBUG = false;

    public static double THOUSAND = 1000;

    public static int THOUSAND_INT = 1000;
    /**
     * 最大承载session
     */
    public static int MAX_LOGIN_SESSION = 10000;

    /**
     * 最大人數
     */
    public static int MAX_LOGIN_PLAYER = 30000;

    /**
     * 最大在线人数
     */
    public static int MAX_ONLINE_NUM = 6000;

    /**
     * 每日刷新时间（距离0点秒数）
     */
    public static long DAY_REFRESH_TIME = 0;

    /**
     * 战斗力计算参数
     */
    public static HashMap<Integer, Float> FIGHT_POWER_PATAMETERS = new HashMap<>();

    /**
     * 最大部位位置
     */
    public static int MAX_EQUIP_TYPE = 12;

    public static <T extends Message.Builder> T transferJson2Protobuf(String filename, T builder) {
        try (FileReader reader = new FileReader("./resources/jsons/" + filename + ".json")) {
            JsonFormat.parser().merge(reader, builder);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return builder;
    }
}
