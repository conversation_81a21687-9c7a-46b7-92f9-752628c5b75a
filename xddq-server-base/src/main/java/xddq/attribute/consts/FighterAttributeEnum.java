package xddq.attribute.consts;

import java.util.HashMap;

//t[(t.None = 0)] = "None";
//    t[(t.Attack = 1)] = "Attack";
//    t[(t.HP = 2)] = "HP";
//    t[(t.Defense = 3)] = "Defense";
//    t[(t.Speed = 4)] = "Speed";
//    t[(t.Stun = 5)] = "Stun";
//    t[(t.CriticalHit = 6)] = "CriticalHit";
//    t[(t.DoubleAttack = 7)] = "DoubleAttack";
//    t[(t.Dodge = 8)] = "Dodge";
//    t[(t.AttackBack = 9)] = "AttackBack";
//    t[(t.LifeSteal = 10)] = "LifeSteal";
//    t[(t.ResistStun = 11)] = "ResistStun";
//    t[(t.ResistCriticalHit = 12)] = "ResistCriticalHit";
//    t[(t.ResistDoubleAttack = 13)] = "ResistDoubleAttack";
//    t[(t.ResistDodge = 14)] = "ResistDodge";
//    t[(t.ResistAttackBack = 15)] = "ResistAttackBack";
//    t[(t.ResistLifeSteal = 16)] = "ResistLifeSteal";
//    t[(t.Attack_Percentage = 17)] = "Attack_Percentage";
//    t[(t.HP_Percentage = 18)] = "HP_Percentage";
//    t[(t.Defense_Percentage = 19)] = "Defense_Percentage";
//    t[(t.Speed_Percentage = 20)] = "Speed_Percentage";
//    t[(t.FinalAddDamage = 21)] = "FinalAddDamage";
//    t[(t.FinalReduceDamage = 22)] = "FinalReduceDamage";
//    t[(t.AddCriticalDamage = 23)] = "AddCriticalDamage";
//    t[(t.ReduceCriticalDamage = 24)] = "ReduceCriticalDamage";
//    t[(t.AddTreatEffect = 25)] = "AddTreatEffect";
//    t[(t.ReduceTreatEffect = 26)] = "ReduceTreatEffect";
//    t[(t.AddPetEffect = 27)] = "AddPetEffect";
//    t[(t.ReducePetEffect = 28)] = "ReducePetEffect";
//    t[(t.AddAllEffect = 29)] = "AddAllEffect";
//    t[(t.AddAllResistEffect = 30)] = "AddAllResistEffect";
//    t[(t.AddDowDamage = 31)] = "AddDowDamage";
//    t[(t.ReduceAllEffect = 32)] = "ReduceAllEffect";
//    t[(t.ReduceAllResistEffect = 33)] = "ReduceAllResistEffect";
//    t[(t.ReduceDowDamage = 34)] = "ReduceDowDamage";
//    t[(t.Parry = 35)] = "Parry";
//    t[(t.ReduceParry = 36)] = "ReduceParry";
//    t[(t.BreakingArmor = 37)] = "BreakingArmor";
//    t[(t.ReduceBreakingArmor = 38)] = "ReduceBreakingArmor";
//    t[(t.Shield = 39)] = "Shield";
//    t[(t.ReduceShield = 40)] = "ReduceShield";
//    t[(t.DowDouble = 41)] = "DowDouble";
//    t[(t.ReduceDowDouble = 42)] = "ReduceDowDouble";
//    t[(t.PetStifle = 43)] = "PetStifle";
//    t[(t.ReducePetStifle = 44)] = "ReducePetStifle";
//    t[(t.PetInspire = 45)] = "PetInspire";
//    t[(t.ReducePetInspire = 46)] = "ReducePetInspire";
//    t[(t.UniverseSkillAddDamage = 48)] = "UniverseSkillAddDamage";
//    t[(t.UniverseSkillReduceDamage = 49)] = "UniverseSkillReduceDamage";
//    t[(t.ProfessionSkillAddDamage = 50)] = "ProfessionSkillAddDamage";
//    t[(t.ProfessionSkillReduceDamage = 51)] = "ProfessionSkillReduceDamage";
public enum FighterAttributeEnum {
    NONE(0, "无"),
    Attack(1, "攻击"),
    HP(2, "生命"),
    Defense(3, "防御"),
    Speed(4, "速度(敏捷)"),
    //     AttributeEnum_Stun = 5;                 // 击晕
    //     AttributeEnum_CriticalHit = 6;          // 暴击
    //     AttributeEnum_DoubleAttack = 7;         // 连击
    //     AttributeEnum_Dodge = 8;                // 闪避
    //     AttributeEnum_AttackBack = 9;           // 反击
    //     AttributeEnum_LifeSteal = 10;           // 吸血
    Stun(5, "擊暈"),
    CriticalHit(6, "暴擊"),
    DoubleAttack(7, "連擊"),
    Dodge(8, "閃避"),
    AttackBack(9, "反擊"),
    LifeSteal(10, "吸血"),
    //     AttributeEnum_ResistStun = 11;          // 抗击晕
    //     AttributeEnum_ResistCriticalHit = 12;   // 抗暴击
    //     AttributeEnum_ResistDoubleAttack = 13;  // 抗连击
    //     AttributeEnum_ResistDodge = 14;         // 抗闪避
    //     AttributeEnum_ResistAttackBack = 15;    // 抗反击
    //     AttributeEnum_ResistLifeSteal = 16;     // 抗吸血
    ResistStun(11, "抗擊暈"),
    ResistCriticalHit(12, "抗暴擊"),
    ResistDoubleAttack(13, "抗連擊"),
    ResistDodge(14, "抗閃避"),
    ResistAttackBack(15, "抗反擊"),
    ResistLifeSteal(16, "抗吸血"),
    //     AttributeEnum_Attack_Percentage = 17;   // 攻击力千分比
    //     AttributeEnum_HP_Percentage = 18;       // 血量千分比
    //     AttributeEnum_Defense_Percentage = 19;  // 防御千分比
    //     AttributeEnum_Speed_Percentage = 20;    // 敏捷千分比
    AttackPercentage(17, "攻击力千分比"),
    HPPercentage(18, "血量千分比"),
    DefensePercentage(19, "防御千分比"),
    SpeedPercentage(20, "敏捷千分比"),
    //     AttributeEnum_FinalAddDamage = 21;      // 最终增伤百分比
    //     AttributeEnum_FinalReduceDamage = 22;   // 最终减伤百分比
    //     AttributeEnum_AddCriticalDamage = 23;   // 强化暴伤百分比
    //     AttributeEnum_ReduceCriticalDamage = 24;// 弱化暴伤百分比
    //     AttributeEnum_AddTreatEffect = 25;      // 强化治疗百分比
    //     AttributeEnum_ReduceTreatEffect = 26;   // 弱化治疗百分比
    //     AttributeEnum_AddPetEffect = 27;        // 强化灵兽效果
    //     AttributeEnum_ReducePetEffect = 28;     // 弱化灵兽效果
    FinalAddDamage(21, "最終增傷"),
    FinalReduceDamage(22, "最總減傷"),
    AddCriticalDamage(23, "强化爆傷"),
    ReduceCriticalDamage(24, "弱化爆傷"),
    AddTreatEffect(25, "强化治療"),
    ReduceTreatEffect(26, "弱化治療"),
    AddPetEffect(27, "强化靈獸"),
    ReducePetEffect(28, "弱化靈獸"),


    NONE5(29, "增加所有战斗效果？？"),
    NONE6(30, "增加所有战斗抗性？？"),
    AddDowDamage(31, "强化道法傷害"),
    ReduceAllEffect(32, "无视战斗属性"),
    ReduceAllResistEffect(33, "无视战斗抗性"),
    ReduceDowDamage(34, "弱化道法傷害"),
    Parry(35, "格擋"),
    ReduceParry(36, "抗格擋"),
    BreakingArmor(37, "破甲"),
    ReduceBreakingArmor(38, "抗破甲"),
    Shield(39, "護盾"),
    ReduceShield(40, "弱化護盾"),
    DowDouble(41, "道法連擊"),
    ReduceDowDouble(42, "抗道法聯機"),
    PetStifle(43, "靈獸壓制"),
    ReducePetStifle(44, "抗靈獸壓制"),
    PetInspire(45, "靈獸鼓舞"),
    ReducePetInspire(46, "抗靈獸鼓舞"),
    NONE7(47, "无"),
    UniverseSkillAddDamage(48, "强化玄訣"),
    UniverseSkillReduceDamage(49, "弱化玄訣"),
    ProfessionSkillAddDamage(50, "道力强化"),
    ProfessionSkillReduceDamage(51, "道力抗性"),
    FaXiangAddDamage(52, "强化法相"),
    FaXiangReduceDamage(53, "弱化法相"),
    WuShuangAttack(54, "无双击破"),
    WuShuangDefense(55, "无双守护"),

//    DevilHurt(1035, "强化玄訣？？"),
//    Immortal (1036, "弱化玄訣？？"),

    ;

    // 成员变量
    private final int id;
    private final String chinese;

    public static final FighterAttributeEnum[] fighterAttributeEnum = FighterAttributeEnum.values();

    public static final FighterAttributeEnum[][] fighterPercentAttributeEnum = new FighterAttributeEnum[][]{
            {Attack, AttackPercentage},
            {HP, HPPercentage},
            {Defense, DefensePercentage},
            {Speed, SpeedPercentage}
    };

    public static final FighterAttributeEnum[] fighterBaseAttributeEnum = new FighterAttributeEnum[]{
            Attack,
            HP,
            Defense,
            Speed,
    };

    public static final FighterAttributeEnum[] fighterSecAttributeEnum = new FighterAttributeEnum[]{
            Stun,
            CriticalHit,
            DoubleAttack,
            Dodge,
            AttackBack,
            LifeSteal,
    };

    public static final FighterAttributeEnum[] fighterResistAttributeEnum = new FighterAttributeEnum[]{
            ResistStun,
            ResistCriticalHit,
            ResistDoubleAttack,
            ResistDodge,
            ResistAttackBack,
            ResistLifeSteal,
    };

    // 战力值
    public static final HashMap<Integer, Float> fightValues = new HashMap<>();

    // 构造方法
    FighterAttributeEnum(int id, String chinese) {
        this.id = id;
        this.chinese = chinese;
    }

    public int getId() {
        return id;
    }

    public String getChinese() {
        return chinese;
    }
}
