package xddq.attribute.consts;

public enum CalculatorTypeEnum {

    NONE(0),
    // 基础数值
    BASE(1),
    // 装备
    EQUIP(2),
    // 云台
    CLOUD(3),
    // 灵兽
    PET(4),
    //神通
    MAGIC(5),
    //法宝
    MAGIC_TREASURE(6),
    //天赋
    TALENT(7),
    //精怪
    SPIRIT(8),
    //镇妖塔临时属性
    TOWER(9),
	 //宗门
    PUPIL(10),
    //道途
    CAREER(11),
    //仙友
    DESTINY(12),
    CHARA(13),

    RULE(14),
    //GM
    GM(999),
    ;

    private final int value;

    public static final CalculatorTypeEnum[] calculatorTypeEnum = CalculatorTypeEnum.values();

    CalculatorTypeEnum(int type) {
        this.value = type;
    }

    public int getValue() {
        return this.value;
    }
}
