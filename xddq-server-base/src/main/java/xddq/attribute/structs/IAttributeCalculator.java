package xddq.attribute.structs;

import xddq.attribute.consts.CalculatorTypeEnum;
import xddq.player.structs.Player;

import java.util.concurrent.ConcurrentHashMap;

public interface IAttributeCalculator {

    /**
     * 类型
     */
    CalculatorTypeEnum getType();
    /**
     * 临时
     */
    boolean isTemp();
    /**
     * 获取玩家属性
     */
    void countAttribute(Player player, ConcurrentHashMap<Integer, Long> attr, ConcurrentHashMap<Integer, Integer> skills, Object... parameters);
}
