package xddq.script.manager;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import xddq.data.manager.ReloadManager;
import xddq.script.IScript;

import java.io.File;
import java.util.HashMap;
import java.util.concurrent.ConcurrentHashMap;

public class ScriptManager {

    private static final Logger log = LoggerFactory.getLogger(ScriptManager.class);

    private ConcurrentHashMap<Integer, IScript> scripts = new ConcurrentHashMap<>();

    public IScript getScript(int id) {
        return scripts.get(id);
    }

    private final String SCRIPT_PATH = "./scripts/";

    public void loadScripts() {
        HashMap<String, String> files = getPathFiles(new File(SCRIPT_PATH));
        files.forEach((k, v) -> {
            log.info("加载脚本文件:" + v);
            try {
                Class<?> clazz = Class.forName(k);
                IScript script = (IScript) clazz.getDeclaredConstructor().newInstance();
                scripts.put(script.getId(), script);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        });
    }

    public HashMap<String, String> getPathFiles(File path) {
        HashMap<String, String> files = new HashMap<>();
        if (path.exists()) {
            File[] files0 = path.listFiles();
            if (files0 != null) {
                for (File file : files0) {
                    if (file.isFile()) {
                        String packageName = file.getPath().substring(SCRIPT_PATH.length());
                        String javaName = packageName.substring(0, packageName.indexOf(".")).replaceAll("\\\\", ".").replace("/", ".");
                        files.put(javaName, javaName);
                    } else {
                        files.putAll(getPathFiles(file));
                    }
                }
            }
        }
        return files;
    }
}
