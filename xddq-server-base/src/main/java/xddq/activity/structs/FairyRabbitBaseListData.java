package xddq.activity.structs;

import java.util.ArrayList;
import java.util.List;

//某圈的阵基数据
public class FairyRabbitBaseListData {
    private int round;
    private List<Integer> baseList = new ArrayList<>();

    public int getRound() {
        return round;
    }

    public void setRound(int round) {
        this.round = round;
    }

    public List<Integer> getBaseList() {
        return baseList;
    }

    public void setBaseList(List<Integer> baseList) {
        this.baseList = baseList;
    }
}
