package xddq.activity.structs;

public class FairyRabbitSelectRewardData {
    public int floor;
    public int selectIndex;
    public int rewardIndex;

    public int getFloor() {
        return floor;
    }

    public void setFloor(int floor) {
        this.floor = floor;
    }

    public int getSelectIndex() {
        return selectIndex;
    }

    public void setSelectIndex(int selectIndex) {
        this.selectIndex = selectIndex;
    }

    public int getRewardIndex() {
        return rewardIndex;
    }

    public void setRewardIndex(int rewardIndex) {
        this.rewardIndex = rewardIndex;
    }
}
