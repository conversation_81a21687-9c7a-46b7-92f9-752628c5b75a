package xddq.activity.structs;

public class BigRewardConfig {
    private  int activityId;
    private  int Id;
    private  String reward;
    private  int floor;
    private  int drawCost;
    private  String selectReward;

    public int getActivityId() {
        return activityId;
    }

    public void setActivityId(int activityId) {
        this.activityId = activityId;
    }

    public int getId() {
        return Id;
    }

    public void setId(int id) {
        Id = id;
    }

    public String getReward() {
        return reward;
    }

    public void setReward(String reward) {
        this.reward = reward;
    }

    public int getFloor() {
        return floor;
    }

    public void setFloor(int floor) {
        this.floor = floor;
    }

    public int getDrawCost() {
        return drawCost;
    }

    public void setDrawCost(int drawCost) {
        this.drawCost = drawCost;
    }

    public String getSelectReward() {
        return selectReward;
    }

    public void setSelectReward(String selectReward) {
        this.selectReward = selectReward;
    }
}
