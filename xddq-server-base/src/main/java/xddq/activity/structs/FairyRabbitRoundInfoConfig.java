package xddq.activity.structs;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

public class FairyRabbitRoundInfoConfig {

    private List<FairyRabbitDrawConfig> FairyRabbitDrawConfigList =new ArrayList<>();

    private  int totalweight ;

    public List<FairyRabbitDrawConfig> getFairyRabbitDrawConfigList() {
        return FairyRabbitDrawConfigList;
    }

    public void setFairyRabbitDrawConfigList(List<FairyRabbitDrawConfig> fairyRabbitDrawConfigList) {
        FairyRabbitDrawConfigList = fairyRabbitDrawConfigList;
    }

    public int getTotalweight() {
        return totalweight;
    }

    public void setTotalweight(int totalweight) {
        this.totalweight = totalweight;
    }
}
