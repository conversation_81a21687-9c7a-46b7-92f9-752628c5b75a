package xddq.activity.structs;

public class AuspiciousBlessDrawConfig {
    //    message AuspiciousBlessDrawConfig {
//        optional int32 id = 1;
//        optional string reward = 2;
//        optional int32 weight = 3;
//        optional int32 big = 4;
//    }

    private int id;
    private String reward;
    private int weight;
    private int big;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getReward() {
        return reward;
    }

    public void setReward(String reward) {
        this.reward = reward;
    }

    public int getWeight() {
        return weight;
    }

    public void setWeight(int weight) {
        this.weight = weight;
    }

    public int getBig() {
        return big;
    }

    public void setBig(int big) {
        this.big = big;
    }
}
