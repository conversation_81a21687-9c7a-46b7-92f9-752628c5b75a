package xddq.activity.structs;

import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

public class FairyRabbitRoundConfig {

    private ConcurrentHashMap<Integer, FairyRabbitRoundInfoConfig> FairyRabbitRoundConfig = new ConcurrentHashMap<>();

    public ConcurrentHashMap<Integer, FairyRabbitRoundInfoConfig> getFairyRabbitRoundConfig() {
        return FairyRabbitRoundConfig;
    }

    public void setFairyRabbitRoundConfig(ConcurrentHashMap<Integer, FairyRabbitRoundInfoConfig> fairyRabbitRoundConfig) {
        FairyRabbitRoundConfig = fairyRabbitRoundConfig;
    }
}
