package xddq.activity.structs;

import java.util.concurrent.ConcurrentHashMap;

public class SignData {

    private int dayTimes ;
    private long lastSignTime;
    private ConcurrentHashMap<Integer, Boolean> isGetReaward;
    private ConcurrentHashMap<Integer, String> rewards;

    public int getDayTimes() {
        return dayTimes;
    }

    public void setDayTimes(int dayTimes) {
        this.dayTimes = dayTimes;
    }

    public long getLastSignTime() {
        return lastSignTime;
    }

    public void setLastSignTime(long lastSignTime) {
        this.lastSignTime = lastSignTime;
    }

    public ConcurrentHashMap<Integer, Boolean> getIsGetReaward() {
        return isGetReaward;
    }

    public void setIsGetReaward(ConcurrentHashMap<Integer, Boolean> isGetReaward) {
        this.isGetReaward = isGetReaward;
    }

    public ConcurrentHashMap<Integer, String> getRewards() {
        return rewards;
    }

    public void setRewards(ConcurrentHashMap<Integer, String> rewards) {
        this.rewards = rewards;
    }
}
