package xddq.activity.structs;

import java.util.concurrent.ConcurrentHashMap;

public class PetDreamLandAdventureData {
//    //遗迹探索信息
//    message PetDreamLandAdventureData{
//        optional int32 curProfit = 1; //当前的收益
//        optional int64 calculateStartTime = 2;//计算收益的时间起点
//        repeated PetDreamLandAdventurePlaceData placeData = 3; //地点信息
//    }

    // 上次结算收益时除以5分钟多余的时间（ms）
    public long deltaTime;
    private int curProfit;
    private long calculateStartTime;
    private ConcurrentHashMap<Integer, PetDreamLandAdventurePlaceData> placeData = new ConcurrentHashMap<Integer, PetDreamLandAdventurePlaceData>();

    public int getCurProfit() {
        return curProfit;
    }

    public void setCurProfit(int curProfit) {
        this.curProfit = curProfit;
    }

    public long getCalculateStartTime() {
        return calculateStartTime;
    }

    public void setCalculateStartTime(long calculateStartTime) {
        this.calculateStartTime = calculateStartTime;
    }

    public ConcurrentHashMap<Integer, PetDreamLandAdventurePlaceData> getPlaceData() {
        return placeData;
    }

    public void setPlaceData(ConcurrentHashMap<Integer, PetDreamLandAdventurePlaceData> placeData) {
        this.placeData = placeData;
    }
}

