package xddq.activity.structs;


import xddq.activity.data.ActivityConditionData;
import xddq.activity.data.ActivityScoreData;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class ActivityData {
    // 自选礼包数据
    private OptionalGiftActivityDataMsg optionalGiftActivityDataMsg = null;

    // 灵兽幻境玩家数据
    private PetDreamLandDataMsg petDreamLandDataMsg = null;

    // 精怪历练玩家数据
    private SpiritTrialActivityData spiritTrialActivityData = null;

    // 瑞兽赐福数据
    private AuspiciousBlessData auspiciousBlessData = null;

    // 妖灵宝塔玩家数据
    private DemonTowerUserData demonTowerUserData = null;

    // 秘境探宝玩家数据
    private FairyRabbitActivityData mjtbPlayerData = null;

    public PetDreamLandDataMsg getPetDreamLandDataMsg() {
        return petDreamLandDataMsg;
    }

    public SpiritTrialActivityData getSpiritTrialActivityData() {
        return spiritTrialActivityData;
    }

    public void setPetDreamLandDataMsg(PetDreamLandDataMsg petDreamLandDataMsg) {
        this.petDreamLandDataMsg = petDreamLandDataMsg;
    }

    public void setSpiritTrialActivityData(SpiritTrialActivityData data) {
        this.spiritTrialActivityData = data;
    }

    public AuspiciousBlessData getAuspiciousBlessData() {
        return auspiciousBlessData;
    }

    public void setAuspiciousBlessData(AuspiciousBlessData auspiciousBlessData) {
        this.auspiciousBlessData = auspiciousBlessData;
    }

    public OptionalGiftActivityDataMsg getOptionalGiftActivityDataMsg() {
        return optionalGiftActivityDataMsg;
    }

    public void setOptionalGiftActivityDataMsg(OptionalGiftActivityDataMsg optionalGiftActivityDataMsg) {
        this.optionalGiftActivityDataMsg = optionalGiftActivityDataMsg;
    }

    private ConcurrentHashMap<String, ActivityReward> rewards = new ConcurrentHashMap<>();

    public ConcurrentHashMap<String, ActivityReward> getRewards() {
        return rewards;
    }

    // key: mallId(conditionId), value: endTime
    private ConcurrentHashMap<Integer, Integer> mallEndTimeMap = new ConcurrentHashMap<>();

    // key: activityId, value: playerData.b64
    private final ConcurrentHashMap<Integer, byte[]> ACTIVITY_PLAYER_DATA_MAP = new ConcurrentHashMap<>();

    // activity -> (conditionId -> conditionData)
    private ConcurrentHashMap<Integer, Map<Integer, ActivityConditionData>> activityConditionDataMap = new ConcurrentHashMap<>();

    // activity -> (mallId -> count)
    //private ConcurrentHashMap<Integer, Map<Integer, Integer>> mallBuyCountMap = new ConcurrentHashMap<>();

    // key: activityId
    private ConcurrentHashMap<Integer, ActivityScoreData> activityScoreDataMap = new ConcurrentHashMap<>();

    // key: TriggerType
    private ConcurrentHashMap<Integer, Integer> condValueMap = new ConcurrentHashMap<>();

    // key: activityId
    private ConcurrentHashMap<Integer, LuckyDrawActivityData> luckyDrawActivityDataMap = new ConcurrentHashMap<Integer, LuckyDrawActivityData>();

    public LuckyDrawActivityData getLuckyDrawActivityData(int activityId) {
        return luckyDrawActivityDataMap.get(activityId);
    }

    public void putLuckyDrawActivityData(int activityId, LuckyDrawActivityData data) {
        luckyDrawActivityDataMap.put(activityId, data);
    }

    public ActivityData setRewards(ConcurrentHashMap<String, ActivityReward> rewards) {
        this.rewards = rewards;
        return this;
    }

    public ConcurrentHashMap<Integer, Integer> getMallEndTimeMap() {
        return this.mallEndTimeMap;
    }

    public ConcurrentHashMap<Integer, byte[]> getACTIVITY_PLAYER_DATA_MAP() {
        return ACTIVITY_PLAYER_DATA_MAP;
    }

    public ConcurrentHashMap<Integer, Map<Integer, ActivityConditionData>> getActivityConditionDataMap() {
        return activityConditionDataMap;
    }

    public ConcurrentHashMap<Integer, ActivityScoreData> getActivityScoreDataMap() {
        return activityScoreDataMap;
    }

    public void setActivityScoreDataMap(ConcurrentHashMap<Integer, ActivityScoreData> activityScoreDataMap) {
        this.activityScoreDataMap = activityScoreDataMap;
    }

    public ConcurrentHashMap<Integer, Integer> getCondValueMap() {
        return condValueMap;
    }

    public DemonTowerUserData getDemonTowerUserData() {
        return demonTowerUserData;
    }

    public void setDemonTowerUserData(DemonTowerUserData demonTowerUserData) {
        this.demonTowerUserData = demonTowerUserData;
    }

    public FairyRabbitActivityData getMjtbPlayerData() {
        return mjtbPlayerData;
    }

    public void setMjtbPlayerData(FairyRabbitActivityData mjtbPlayerData) {
        this.mjtbPlayerData = mjtbPlayerData;
    }
}
