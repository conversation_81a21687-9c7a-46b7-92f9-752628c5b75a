package xddq.activity.structs;

public class CellSelectItemMsg {
//    message CellSelectItemMsg {
//        required int32 index = 1;   //格子下标
//        required int32 selectIndex = 2; //选择道具下标
//    }
    private int index;
    private int selectIndex;

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public int getSelectIndex() {
        return selectIndex;
    }

    public void setSelectIndex(int selectIndex) {
        this.selectIndex = selectIndex;
    }
}
