package xddq.activity.structs;

import java.util.concurrent.ConcurrentHashMap;

public class FairyRabbitDrawConfig {

//    required int32 activityId = 1;
//    required int32 Id = 2;
//    required string reward = 3;
//    required int32 floor = 4;
//    required int32 round = 5;
//    required int32 weight = 6;
    private  int activityId;
    private  int Id;
    private  String reward;
    private  int floor;
    private  int round;
    private  int weight;

    public int getActivityId() {
        return activityId;
    }

    public void setActivityId(int activityId) {
        this.activityId = activityId;
    }

    public int getId() {
        return Id;
    }

    public void setId(int id) {
        Id = id;
    }

    public String getReward() {
        return reward;
    }

    public void setReward(String reward) {
        this.reward = reward;
    }

    public int getFloor() {
        return floor;
    }

    public void setFloor(int floor) {
        this.floor = floor;
    }

    public int getRound() {
        return round;
    }

    public void setRound(int round) {
        this.round = round;
    }

    public int getWeight() {
        return weight;
    }

    public void setWeight(int weight) {
        this.weight = weight;
    }
}
