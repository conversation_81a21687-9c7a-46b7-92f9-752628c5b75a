package xddq.activity.structs;

import java.util.concurrent.ConcurrentHashMap;

public class OptionalGiftActivityDataMsg {
    private long nextResetTime = 0L;
    // key: mallId
    private ConcurrentHashMap<Integer, Integer> mallBuyCount = new ConcurrentHashMap<>();
    private ConcurrentHashMap<Integer, MallSelectItemsMsg> mallSelectItemsMsg = new ConcurrentHashMap<>();

    public ConcurrentHashMap<Integer, MallSelectItemsMsg> getMallSelectItemsMsg() {
        return mallSelectItemsMsg;
    }

    public void setMallSelectItemsMsg(ConcurrentHashMap<Integer, MallSelectItemsMsg> mallSelectItemsMsg) {
        this.mallSelectItemsMsg = mallSelectItemsMsg;
    }

    public ConcurrentHashMap<Integer, Integer> getMallBuyCount() {
        return mallBuyCount;
    }

    public void setMallBuyCount(ConcurrentHashMap<Integer, Integer> mallBuyCount) {
        this.mallBuyCount = mallBuyCount;
    }

    public long getNextResetTime() {
        return nextResetTime;
    }

    public void setNextResetTime(long nextResetTime) {
        this.nextResetTime = nextResetTime;
    }
}
