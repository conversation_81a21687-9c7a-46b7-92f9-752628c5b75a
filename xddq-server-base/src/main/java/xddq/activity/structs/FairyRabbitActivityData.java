package xddq.activity.structs;

import java.util.concurrent.ConcurrentHashMap;

public class FairyRabbitActivityData {
//    message FairyRabbitActivityData{
//        optional int32 floor = 1;
//        optional int32 round = 2;
//        repeated FairyRabbitBaseListData baseListDataList = 3;
//        optional int32 drawTimes = 4;//累计次数
//        optional int32 selectBigRewardId = 5; //自选大奖id (-1表示未选择)
//        repeated FairyRabbitSelectRewardData selectRewardData = 6;
//    }
    private int floor;
    private int round;

    // 所有圈的阵基数据, key: round
    private ConcurrentHashMap<Integer, FairyRabbitBaseListData> baseListDataList = new ConcurrentHashMap<>();

    private int drawTimes;
    private int costItems;

    private int selectBigRewardId;

    private ConcurrentHashMap<Integer, FairyRabbitSelectRewardData> selectRewardData = new ConcurrentHashMap<>();

    public int getFloor() {
        return floor;
    }

    public void setFloor(int floor) {
        this.floor = floor;
    }

    public int getRound() {
        return round;
    }

    public void setRound(int round) {
        this.round = round;
    }

    public ConcurrentHashMap<Integer, FairyRabbitBaseListData> getBaseListDataList() {
        return baseListDataList;
    }

    public void setBaseListDataList(ConcurrentHashMap<Integer, FairyRabbitBaseListData> baseListDataList) {
        this.baseListDataList = baseListDataList;
    }

    public int getDrawTimes() {
        return drawTimes;
    }

    public void setDrawTimes(int drawTimes) {
        this.drawTimes = drawTimes;
    }

    public int getSelectBigRewardId() {
        return selectBigRewardId;
    }

    public void setSelectBigRewardId(int selectBigRewardId) {
        this.selectBigRewardId = selectBigRewardId;
    }

    public ConcurrentHashMap<Integer, FairyRabbitSelectRewardData> getSelectRewardData() {
        return selectRewardData;
    }

    public void setSelectRewardData(ConcurrentHashMap<Integer, FairyRabbitSelectRewardData> selectRewardData) {
        this.selectRewardData = selectRewardData;
    }

    public int getCostItems() {
        return costItems;
    }

    public void setCostItems(int costItems) {
        this.costItems = costItems;
    }
}
