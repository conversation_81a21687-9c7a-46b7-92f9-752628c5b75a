package xddq.activity.structs;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

public class ActivityDrawConfig {
    private ConcurrentHashMap<Integer, LuckyDrawConfig> luckyDrawConfig = new ConcurrentHashMap<>();

    private int totalWeight;
    private List<LuckyDrawConfig> weights = new ArrayList<>();

    public ConcurrentHashMap<Integer, LuckyDrawConfig> getLuckyDrawConfig() {
        return luckyDrawConfig;
    }

    public void setLuckyDrawConfig(ConcurrentHashMap<Integer, LuckyDrawConfig> luckyDrawConfig) {
        this.luckyDrawConfig = luckyDrawConfig;
    }



    public void init() {
        totalWeight = 0;
        weights = new ArrayList<>();
        luckyDrawConfig.forEach((k,v) -> {
            totalWeight += v.getWeight();
            weights.add(v);
        });
    }

    public int getTotalWeight() {
        return totalWeight;
    }

    public void setTotalWeight(int totalWeight) {
        this.totalWeight = totalWeight;
    }

    public List<LuckyDrawConfig> getWeights() {
        return weights;
    }

    public void setWeights(List<LuckyDrawConfig> weights) {
        this.weights = weights;
    }
}
