package xddq.activity.structs;

public class PetDreamLandDataMsg {
//    //灵兽幻境数据
//    message PetDreamLandDataMsg {
//        optional int32 activityScore = 1; //活动积分
//        optional PetDreamLandTurnTableData turnTable = 2; //转盘数据
//        optional PetDreamLandAdventureData adventure = 3; //遗迹探险数据
//    }
    private int activityScore;
    private PetDreamLandTurnTableData turnTable;
    private PetDreamLandAdventureData adventure;

    public int getActivityScore() {
        return activityScore;
    }

    public void setActivityScore(int activityScore) {
        this.activityScore = activityScore;
    }

    public PetDreamLandTurnTableData getTurnTable() {
        return turnTable;
    }

    public void setTurnTable(PetDreamLandTurnTableData turnTable) {
        this.turnTable = turnTable;
    }

    public PetDreamLandAdventureData getAdventure() {
        return adventure;
    }

    public void setAdventure(PetDreamLandAdventureData adventure) {
        this.adventure = adventure;
    }

    // 每5分钟收益
    private int profitPer5m;


    public int getProfitPer5m() {
        return profitPer5m;
    }

    public void setProfitPer5m(int profitPer5m) {
        this.profitPer5m = profitPer5m;
    }

    private long lastGetTime = 0L;

    public long getLastGetTime() {
        return lastGetTime;
    }

    public void setLastGetTime(long lastGetTime) {
        this.lastGetTime = lastGetTime;
    }
}
