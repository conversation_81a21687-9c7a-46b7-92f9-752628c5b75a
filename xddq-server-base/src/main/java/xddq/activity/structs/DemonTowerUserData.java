package xddq.activity.structs;

import java.util.concurrent.ConcurrentHashMap;

public class DemonTowerUserData {
    //    message DemonTowerUserDataSync{
//        optional int32 floor = 1;//当前所在层数
//        optional string rewardStr = 2;//所在层数奖励获取情况 1已获取0未获得，位数为当前层奖品数量，例如0110表示当前层数4个奖品，第2、3个奖品已获得
//        optional int32 rareIndex = 3;//已选择的稀有奖励下标
//        repeated DemonTowerRareData rareData = 4;//稀有奖品已获取数量
//        optional int32 point = 5; //总积分
//        optional int32 todayPoint = 6; //今日积分
//        optional int32 round = 7; //当前轮次
//        optional int32 chooseIndex = 8; //自选选择的下标
//        repeated RareRewardSelectData selectRareRewardItem = 9; //已选择的限定奖励列表
//    }
    private int floor;
    private String rewardStr;
    private int rareIndex;
    private ConcurrentHashMap<Integer, DemonTowerRareData> rareData = new ConcurrentHashMap<Integer, DemonTowerRareData>();
    private int point;
    private int todayPoint;
    private int round;
    private int chooseIndex;
    private int costitem;
    private ConcurrentHashMap<Integer, RareRewardSelectData> selectRareRewardItem = new ConcurrentHashMap<Integer, RareRewardSelectData>();

    private ConcurrentHashMap<Integer, Integer> rewardlist = new ConcurrentHashMap<>();


    public int getFloor() {
        return floor;
    }

    public void setFloor(int floor) {
        this.floor = floor;
    }

    public String getRewardStr() {
        return rewardStr;
    }

    public void setRewardStr(String rewardStr) {
        this.rewardStr = rewardStr;
    }

    public int getRareIndex() {
        return rareIndex;
    }

    public void setRareIndex(int rareIndex) {
        this.rareIndex = rareIndex;
    }

    public ConcurrentHashMap<Integer, DemonTowerRareData> getRareData() {
        return rareData;
    }

    public void setRareData(ConcurrentHashMap<Integer, DemonTowerRareData> rareData) {
        this.rareData = rareData;
    }

    public int getPoint() {
        return point;
    }

    public void setPoint(int point) {
        this.point = point;
    }

    public int getTodayPoint() {
        return todayPoint;
    }

    public void setTodayPoint(int todayPoint) {
        this.todayPoint = todayPoint;
    }

    public int getRound() {
        return round;
    }

    public void setRound(int round) {
        this.round = round;
    }

    public int getChooseIndex() {
        return chooseIndex;
    }

    public void setChooseIndex(int chooseIndex) {
        this.chooseIndex = chooseIndex;
    }

    public ConcurrentHashMap<Integer, RareRewardSelectData> getSelectRareRewardItem() {
        return selectRareRewardItem;
    }

    public void setSelectRareRewardItem(ConcurrentHashMap<Integer, RareRewardSelectData> selectRareRewardItem) {
        this.selectRareRewardItem = selectRareRewardItem;
    }

    public ConcurrentHashMap<Integer, Integer> getRewardlist() {
        return rewardlist;
    }

    public void setRewardlist(ConcurrentHashMap<Integer, Integer> rewardlist) {
        this.rewardlist = rewardlist;
    }

    public int getCostitem() {
        return costitem;
    }

    public void setCostitem(int costitem) {
        this.costitem = costitem;
    }
}
