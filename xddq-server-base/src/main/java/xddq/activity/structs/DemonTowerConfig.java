package xddq.activity.structs;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

public class DemonTowerConfig {


    private ConcurrentHashMap<Integer, DemonTowerDrawConfig> DemonTowerDrawConfig = new ConcurrentHashMap<>();

    private ConcurrentHashMap<Integer, CoreRewardConfig> coreRewardConfig = new ConcurrentHashMap<>();

    public ConcurrentHashMap<Integer, DemonTowerDrawConfig> getDemonTowerDrawConfig() {
        return DemonTowerDrawConfig;
    }

    public void setDemonTowerDrawConfig(ConcurrentHashMap<Integer, DemonTowerDrawConfig> demonTowerDrawConfig) {
        DemonTowerDrawConfig = demonTowerDrawConfig;
    }

    public ConcurrentHashMap<Integer, CoreRewardConfig> getCoreRewardConfig() {
        return coreRewardConfig;
    }

    public void setCoreRewardConfig(ConcurrentHashMap<Integer, CoreRewardConfig> coreRewardConfig) {
        this.coreRewardConfig = coreRewardConfig;
    }
}
