package xddq.activity.structs;

import java.util.concurrent.ConcurrentHashMap;

public class SpiritTrialActivityData {
//    //精怪历练活动数据
//    message SpiritTrialActivityDataMsg {
//        optional int32 battleItemNum = 1; //镇魂符数量
//        optional int64 battleItemLastRecoveryTime = 2; //镇魂符上一次恢复时间
//        repeated EvilThingDataMsg evilThingDataMsg = 3; //活动邪祟数据
//        repeated int32 restSpiritId = 4; //休息中的精怪列表
//        optional int32 activityScore = 5; //活动积分
//    }
    private int battleItemNum;
    private long battleItemLastRecoveryTime;

    // key:index -- 位置
    private ConcurrentHashMap<Integer, EvilThingDataMsg> evilThingDataMsg = new ConcurrentHashMap<>();
    private ConcurrentHashMap<Integer, Integer> restSpiritId = new ConcurrentHashMap<>();
    private int activityScore;

    public int getBattleItemNum() {
        return battleItemNum;
    }

    public void setBattleItemNum(int battleItemNum) {
        this.battleItemNum = battleItemNum;
    }

    public long getBattleItemLastRecoveryTime() {
        return battleItemLastRecoveryTime;
    }

    public void setBattleItemLastRecoveryTime(long battleItemLastRecoveryTime) {
        this.battleItemLastRecoveryTime = battleItemLastRecoveryTime;
    }

    public ConcurrentHashMap<Integer, EvilThingDataMsg> getEvilThingDataMsg() {
        return evilThingDataMsg;
    }

    public void setEvilThingDataMsg(ConcurrentHashMap<Integer, EvilThingDataMsg> evilThingDataMsg) {
        this.evilThingDataMsg = evilThingDataMsg;
    }

    public ConcurrentHashMap<Integer, Integer> getRestSpiritId() {
        return restSpiritId;
    }

    public void setRestSpiritId(ConcurrentHashMap<Integer, Integer> restSpiritId) {
        this.restSpiritId = restSpiritId;
    }

    public int getActivityScore() {
        return activityScore;
    }

    public void setActivityScore(int activityScore) {
        this.activityScore = activityScore;
    }
}
