package xddq.activity.structs;

public class EvilThingDataMsg {
//    //邪祟数据
//    message EvilThingDataMsg {
//        optional int32 index = 1; //位置
//        optional int32 id = 2; //bossId
//        optional bool defeat = 3; //已击败
//    }

    private int index;
    private int id;
    private boolean defeat;

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public boolean isDefeat() {
        return defeat;
    }

    public void setDefeat(boolean defeat) {
        this.defeat = defeat;
    }
}
