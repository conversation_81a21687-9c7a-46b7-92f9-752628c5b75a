package xddq.activity.structs;

import java.util.Vector;
import java.util.concurrent.ConcurrentHashMap;

public class ActivityReward {

    private int activityId;

    private int mallId;

    private String rechargeId;

    private ConcurrentHashMap<Integer, Integer> rewardDays = new ConcurrentHashMap<>();

    private Vector<String> rewards = new Vector<>();

    private long lastRewardTime;

    private long nextRewardTime;

    public int getActivityId() {
        return activityId;
    }

    public ActivityReward setActivityId(int activityId) {
        this.activityId = activityId;
        return this;
    }

    public int getMallId() {
        return mallId;
    }

    public ActivityReward setMallId(int mallId) {
        this.mallId = mallId;
        return this;
    }

    public String getRechargeId() {
        return rechargeId;
    }

    public ActivityReward setRechargeId(String rechargeId) {
        this.rechargeId = rechargeId;
        return this;
    }

    public ConcurrentHashMap<Integer, Integer> getRewardDays() {
        return rewardDays;
    }

    public ActivityReward setRewardDays(ConcurrentHashMap<Integer, Integer> rewardDays) {
        this.rewardDays = rewardDays;
        return this;
    }

    public Vector<String> getRewards() {
        return rewards;
    }

    public ActivityReward setRewards(Vector<String> rewards) {
        this.rewards = rewards;
        return this;
    }

    public long getLastRewardTime() {
        return lastRewardTime;
    }

    public ActivityReward setLastRewardTime(long lastRewardTime) {
        this.lastRewardTime = lastRewardTime;
        return this;
    }

    public long getNextRewardTime() {
        return nextRewardTime;
    }

    public ActivityReward setNextRewardTime(long nextRewardTime) {
        this.nextRewardTime = nextRewardTime;
        return this;
    }
}
