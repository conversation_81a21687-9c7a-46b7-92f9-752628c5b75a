package xddq.activity.structs;

public class LuckyDrawActivityData {
//    message LuckyDrawActivityDataMsg {
//        repeated LimitRewardTimes limitRewardTimes = 1;
//        optional int32 addUpDrawTimes = 2; //累计抽奖次数
//        optional int32 protectDrawTimes = 3; //保底抽奖次数
//    }
    private int addUpDrawTimes;
    private int protectDrawTimes;

    public int getAddUpDrawTimes() {
        return addUpDrawTimes;
    }

    public void setAddUpDrawTimes(int addUpDrawTimes) {
        this.addUpDrawTimes = addUpDrawTimes;
    }

    public int getProtectDrawTimes() {
        return protectDrawTimes;
    }

    public void setProtectDrawTimes(int protectDrawTimes) {
        this.protectDrawTimes = protectDrawTimes;
    }
}
