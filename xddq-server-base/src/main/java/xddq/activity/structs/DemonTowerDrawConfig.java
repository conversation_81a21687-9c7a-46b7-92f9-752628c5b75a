package xddq.activity.structs;

import xddq.pb.message.RspDestinyTravel_653Impl;

public class DemonTowerDrawConfig {

    //      "activityId": 1160001,
//              "round": "1|999",
//              "floor": 1,
//              "reward": "105086=9|1;100025=2|1;100007=5|1;100016=2|1;100005=3|1",
//              "cost": 1
    private int activityId ;
    private String round;
    private int floor;
    private String reward;
    private int cost;

    public int getActivityId() {
        return activityId;
    }

    public void setActivityId(int activityId) {
        this.activityId = activityId;
    }

    public String getRound() {
        return round;
    }

    public void setRound(String round) {
        this.round = round;
    }

    public int getFloor() {
        return floor;
    }

    public void setFloor(int floor) {
        this.floor = floor;
    }

    public String getReward() {
        return reward;
    }

    public void setReward(String reward) {
        this.reward = reward;
    }

    public int getCost() {
        return cost;
    }

    public void setCost(int cost) {
        this.cost = cost;
    }
}
