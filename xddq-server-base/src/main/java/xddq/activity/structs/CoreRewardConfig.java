package xddq.activity.structs;

public class CoreRewardConfig {
    //      "activityId": 1160001,
//              "id": 1,
//              "reward": "100004=175",
//              "times": 1,
//              "unlock": 1,
//              "great": 0
    private int activityId ;
    private int id;
    private String reward;
    private int times;
    private int unlock;
    private int great;

    public int getActivityId() {
        return activityId;
    }

    public void setActivityId(int activityId) {
        this.activityId = activityId;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getReward() {
        return reward;
    }

    public void setReward(String reward) {
        this.reward = reward;
    }

    public int getTimes() {
        return times;
    }

    public void setTimes(int times) {
        this.times = times;
    }

    public int getUnlock() {
        return unlock;
    }

    public void setUnlock(int unlock) {
        this.unlock = unlock;
    }

    public int getGreat() {
        return great;
    }

    public void setGreat(int great) {
        this.great = great;
    }
}
