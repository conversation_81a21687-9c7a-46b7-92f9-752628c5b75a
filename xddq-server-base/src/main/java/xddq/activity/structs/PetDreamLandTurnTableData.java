package xddq.activity.structs;


import java.util.concurrent.ConcurrentHashMap;

public class PetDreamLandTurnTableData {
//    //转盘信息
//    message PetDreamLandTurnTableData {
//        optional int32 times = 1;//抽奖次数
//        repeated PetDreamLandTurnTableGetReward getRewardList = 2; //大奖获取详情
//    }

    private int times;
    private ConcurrentHashMap<Integer, PetDreamLandTurnTableGetReward> getRewardList = new ConcurrentHashMap<Integer, PetDreamLandTurnTableGetReward>();

    public int getTimes() {
        return times;
    }

    public void setTimes(int times) {
        this.times = times;
    }

    public ConcurrentHashMap<Integer, PetDreamLandTurnTableGetReward> getGetRewardList() {
        return getRewardList;
    }

    public void setGetRewardList(ConcurrentHashMap<Integer, PetDreamLandTurnTableGetReward> getRewardList) {
        this.getRewardList = getRewardList;
    }
}
