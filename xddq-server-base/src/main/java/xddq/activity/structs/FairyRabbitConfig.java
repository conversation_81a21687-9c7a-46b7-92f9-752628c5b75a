package xddq.activity.structs;

import java.util.concurrent.ConcurrentHashMap;

public class FairyRabbitConfig {


    private ConcurrentHashMap<Integer, FairyRabbitRoundConfig> FairyRabbitFloorConfig = new ConcurrentHashMap<>();
    private ConcurrentHashMap<Integer, BigRewardConfig> bigRewardConfigList = new ConcurrentHashMap<>();

    public ConcurrentHashMap<Integer, FairyRabbitRoundConfig> getFairyRabbitFloorConfig() {
        return FairyRabbitFloorConfig;
    }

    public void setFairyRabbitFloorConfig(ConcurrentHashMap<Integer, FairyRabbitRoundConfig> fairyRabbitFloorConfig) {
        FairyRabbitFloorConfig = fairyRabbitFloorConfig;
    }

    public ConcurrentHashMap<Integer, BigRewardConfig> getBigRewardConfigList() {
        return bigRewardConfigList;
    }

    public void setBigRewardConfigList(ConcurrentHashMap<Integer, BigRewardConfig> bigRewardConfigList) {
        this.bigRewardConfigList = bigRewardConfigList;
    }
}
