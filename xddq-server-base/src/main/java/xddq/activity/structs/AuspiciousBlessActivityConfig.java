package xddq.activity.structs;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

public class AuspiciousBlessActivityConfig {
//    message  AuspiciousBlessActivityConfig {
//        repeated  AuspiciousBlessDrawConfig luckyDrawConfig = 1; //抽奖配置
//    }

    private ConcurrentHashMap<Integer, AuspiciousBlessDrawConfig> luckyDrawConfig = new ConcurrentHashMap<>();

    public ConcurrentHashMap<Integer, AuspiciousBlessDrawConfig> getLuckyDrawConfig() {
        return luckyDrawConfig;
    }

    private List<AuspiciousBlessDrawConfig> weights = new ArrayList<>();

    private int totalWeight;


    public List<AuspiciousBlessDrawConfig> getWeights() {
        return weights;
    }

    public void setWeights(List<AuspiciousBlessDrawConfig> weights) {
        this.weights = weights;
    }

    public int getTotalWeight() {
        return totalWeight;
    }

    public void setTotalWeight(int totalWeight) {
        this.totalWeight = totalWeight;
    }


    public void init() {
        totalWeight = 0;
        weights = new ArrayList<>();
        luckyDrawConfig.forEach((k,v) -> {
            totalWeight += v.getWeight();
            weights.add(v);
        });


    }

}
