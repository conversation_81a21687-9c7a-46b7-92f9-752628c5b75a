package xddq.activity.structs;

public class PetDreamLandDrawConfig {
//    //抽奖配置
//    message PetDreamLandDrawConfig {
//        optional int32 id = 1;
//        optional string reward = 2;
//        optional int32 weight = 3;
//        optional int32 limit = 4;
//        optional int32 rewardType = 5; //奖励类型 1大奖 2稀有 3普通
//    }

    private int id;
    private String reward;
    private int weight;
    private int limit;
    private int rewardType;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getReward() {
        return reward;
    }

    public void setReward(String reward) {
        this.reward = reward;
    }

    public int getWeight() {
        return weight;
    }

    public void setWeight(int weight) {
        this.weight = weight;
    }

    public int getLimit() {
        return limit;
    }

    public void setLimit(int limit) {
        this.limit = limit;
    }

    public int getRewardType() {
        return rewardType;
    }

    public void setRewardType(int rewardType) {
        this.rewardType = rewardType;
    }
}
