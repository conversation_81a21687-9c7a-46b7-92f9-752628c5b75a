package xddq.activity.structs;

public class AuspiciousBlessData {
//    message AuspiciousBlessDataSyncResp{
//        required int32 ret = 1;
//        required int32 activityId = 2;
//        required int32 sumDraw = 3;//总抽奖次数
//        required int32 dailyCount = 4;//每日剩余抽奖次数
//        required int32 dailySumCount = 5;//每日总次数
//        required int64 cdEndTime = 6;//cd结束时间
//        required int64 restTime = 7;//活动重置时间
//        required int32 rareRewardId = 8;//锁定的稀有奖品
//    }
    private int activityId;
    private int sumDraw;
    private int dailyCount;
    private int dailySumCount;
    private long cdEndTime;
    private long restTime;
    private int rareRewardId;

    public int getActivityId() {
        return activityId;
    }

    public void setActivityId(int activityId) {
        this.activityId = activityId;
    }

    public int getSumDraw() {
        return sumDraw;
    }

    public void setSumDraw(int sumDraw) {
        this.sumDraw = sumDraw;
    }

    public int getDailyCount() {
        return dailyCount;
    }

    public void setDailyCount(int dailyCount) {
        this.dailyCount = dailyCount;
    }

    public int getDailySumCount() {
        return dailySumCount;
    }

    public void setDailySumCount(int dailySumCount) {
        this.dailySumCount = dailySumCount;
    }

    public long getCdEndTime() {
        return cdEndTime;
    }

    public void setCdEndTime(long cdEndTime) {
        this.cdEndTime = cdEndTime;
    }

    public long getRestTime() {
        return restTime;
    }

    public void setRestTime(long restTime) {
        this.restTime = restTime;
    }

    public int getRareRewardId() {
        return rareRewardId;
    }

    public void setRareRewardId(int rareRewardId) {
        this.rareRewardId = rareRewardId;
    }
}
