package xddq.activity.structs;

public class ActivityPlaceConfig {
//    message ActivityPlaceConfig {
//        optional int32 id = 1;
//        optional string name = 2;
//        optional string unlockCost = 3;
//        optional string seatUnlockCost = 4;
//        optional int32 petType = 5;
//        optional int32 speedup = 6;
//    }
    private int id;
    private String name;
    private String unlockCost;
    private String seatUnlockCost;
    private int petType;
    private int speedup;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUnlockCost() {
        return unlockCost;
    }

    public void setUnlockCost(String unlockCost) {
        this.unlockCost = unlockCost;
    }

    public String getSeatUnlockCost() {
        return seatUnlockCost;
    }

    public void setSeatUnlockCost(String seatUnlockCost) {
        this.seatUnlockCost = seatUnlockCost;
    }

    public int getPetType() {
        return petType;
    }

    public void setPetType(int petType) {
        this.petType = petType;
    }

    public int getSpeedup() {
        return speedup;
    }

    public void setSpeedup(int speedup) {
        this.speedup = speedup;
    }
}
