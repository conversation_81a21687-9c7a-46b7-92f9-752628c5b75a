package xddq.activity.structs;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

public class PetDreamLandAdventurePlaceData {
//    //地点的详情
//    message PetDreamLandAdventurePlaceData{
//        required int32 id = 1; //地点id
//        repeated int32 petData = 2; //派遣的灵兽
//        optional int32 slotNum = 3;//解锁的格子数
//    }

    private int id;

    // key: slotIdx, value: petId
    private ConcurrentHashMap<Integer, Integer> petData = new ConcurrentHashMap<>();
    private int slotNum;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public ConcurrentHashMap<Integer, Integer> getPetData() {
        return petData;
    }

    public void setPetData(ConcurrentHashMap<Integer, Integer> petData) {
        this.petData = petData;
    }

    public int getSlotNum() {
        return slotNum;
    }

    public void setSlotNum(int slotNum) {
        this.slotNum = slotNum;
    }


}
