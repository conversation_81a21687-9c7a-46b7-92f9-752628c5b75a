package xddq.activity.structs;

import java.util.concurrent.ConcurrentHashMap;

public class MallSelectItemsMsg {
//    message MallSelectItemsMsg {
//        required int32 mallId = 1;  //商品id
//        repeated CellSelectItemMsg selectItem = 2;   //选择道具
//    }
    private int mallId;
    private ConcurrentHashMap<Integer, CellSelectItemMsg> selectItem = new ConcurrentHashMap<Integer, CellSelectItemMsg>();

    public int getMallId() {
        return mallId;
    }

    public void setMallId(int mallId) {
        this.mallId = mallId;
    }

    public ConcurrentHashMap<Integer, CellSelectItemMsg> getSelectItem() {
        return selectItem;
    }

    public void setSelectItem(ConcurrentHashMap<Integer, CellSelectItemMsg> selectItem) {
        this.selectItem = selectItem;
    }
}
