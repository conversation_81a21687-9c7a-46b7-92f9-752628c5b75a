package xddq.activity.structs;

public class LuckyDrawConfig {
//    //运势抽奖配置
//    message LuckyDrawConfig {
//        optional int32 id = 1;
//        optional string reward = 2;
//        optional int32 weight = 3;
//        optional int32 limit = 4;
//        optional int32 big = 5;
//        optional int32 protectTimes = 6;
//    }

    private int id;
    private String reward;
    private int weight;
    private int limit;
    private int big;
    private int protectTimes;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getReward() {
        return reward;
    }

    public void setReward(String reward) {
        this.reward = reward;
    }

    public int getWeight() {
        return weight;
    }

    public void setWeight(int weight) {
        this.weight = weight;
    }

    public int getLimit() {
        return limit;
    }

    public void setLimit(int limit) {
        this.limit = limit;
    }

    public int getBig() {
        return big;
    }

    public void setBig(int big) {
        this.big = big;
    }

    public int getProtectTimes() {
        return protectTimes;
    }

    public void setProtectTimes(int protectTimes) {
        this.protectTimes = protectTimes;
    }
}
