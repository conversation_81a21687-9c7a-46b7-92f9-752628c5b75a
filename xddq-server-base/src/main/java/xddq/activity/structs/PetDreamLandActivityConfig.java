package xddq.activity.structs;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

public class PetDreamLandActivityConfig {
    private ConcurrentHashMap<Integer, PetDreamLandDrawConfig> luckDrawConfig = new ConcurrentHashMap<Integer, PetDreamLandDrawConfig>();
    private ConcurrentHashMap<Integer, ActivityPlaceConfig> activityPalaceConfig = new ConcurrentHashMap<Integer, ActivityPlaceConfig>();

    private List<PetDreamLandDrawConfig> weights = new ArrayList<>();

    private int totalWeight;

    public ConcurrentHashMap<Integer, PetDreamLandDrawConfig> getLuckDrawConfig() {
        return luckDrawConfig;
    }

    public void setLuckDrawConfig(ConcurrentHashMap<Integer, PetDreamLandDrawConfig> luckDrawConfig) {
        this.luckDrawConfig = luckDrawConfig;
    }

    public ConcurrentHashMap<Integer, ActivityPlaceConfig> getActivityPalaceConfig() {
        return activityPalaceConfig;
    }

    public void setActivityPalaceConfig(ConcurrentHashMap<Integer, ActivityPlaceConfig> activityPalaceConfig) {
        this.activityPalaceConfig = activityPalaceConfig;
    }

    public List<PetDreamLandDrawConfig> getWeights() {
        return weights;
    }

    public void setWeights(List<PetDreamLandDrawConfig> weights) {
        this.weights = weights;
    }

    public int getTotalWeight() {
        return totalWeight;
    }

    public void setTotalWeight(int totalWeight) {
        this.totalWeight = totalWeight;
    }


    public String getUnlockCost(int id) {
        if (activityPalaceConfig.get(id) == null) {
            return null;
        }
        return activityPalaceConfig.get(id).getUnlockCost();
    }

    public String getSeatUnlockCost(int id, int index) {
        if (activityPalaceConfig.get(id) == null) {
            return null;
        }
        String[] seatUnlockCost = activityPalaceConfig.get(id).getSeatUnlockCost().split(";");
        return seatUnlockCost[index];
    }

    public int getPetType(int id) {
        if (activityPalaceConfig.get(id) == null) {
            return 0;
        }
        return activityPalaceConfig.get(id).getPetType();
    }

    public int getSpeedup(int id) {
        if (activityPalaceConfig.get(id) == null) {
            return 0;
        }
        return activityPalaceConfig.get(id).getSpeedup();
    }



    public void init() {
        totalWeight = 0;
        weights = new ArrayList<>();
        luckDrawConfig.forEach((k,v) -> {
            totalWeight += v.getWeight();
            weights.add(v);
        });


    }
}
