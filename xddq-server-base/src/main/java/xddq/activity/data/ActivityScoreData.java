package xddq.activity.data;

public class ActivityScoreData {
//    //活动积分数据
//    message ActivityScoreDataMsg {
//        optional int32 scorePropId = 1; //积分道具id
//        optional int64 todayCount = 2; //今日积分
//        optional int64 totalCount = 3; //活动积分
//    }
    private int scorePropId;
    private long todayCount;
    private long totalCount;

    public int getScorePropId() {
        return scorePropId;
    }

    public void setScorePropId(int scorePropId) {
        this.scorePropId = scorePropId;
    }

    public long getTodayCount() {
        return todayCount;
    }

    public void setTodayCount(long todayCount) {
        this.todayCount = todayCount;
    }

    public long getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(long totalCount) {
        this.totalCount = totalCount;
    }
}
