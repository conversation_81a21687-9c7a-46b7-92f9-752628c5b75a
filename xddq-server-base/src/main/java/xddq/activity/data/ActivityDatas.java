package xddq.activity.data;

import xddq.pb.Xddq;

import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

public class ActivityDatas {

    private final ConcurrentHashMap<Integer, List<xddq.pb.ActivityMallConfig>> ACTIVITY_ID_MAP = new ConcurrentHashMap<>();

    private final ConcurrentHashMap<Integer, xddq.pb.ActivityMallConfig> MALL_ID_MAP = new ConcurrentHashMap<>();

    private final ConcurrentHashMap<String, xddq.pb.ActivityMallConfig> RECHARGE_ID_MAP = new ConcurrentHashMap<>();

    private final ConcurrentHashMap<Integer, xddq.pb.DreamDataMsg> EXTRA_EQU_MAP = new ConcurrentHashMap<>();

    public ConcurrentHashMap<Integer, List<xddq.pb.ActivityMallConfig>> getACTIVITY_ID_MAP() {
        return ACTIVITY_ID_MAP;
    }

    public ConcurrentHashMap<Integer, xddq.pb.ActivityMallConfig> getMALL_ID_MAP() {
        return MALL_ID_MAP;
    }

    public ConcurrentHashMap<String, xddq.pb.ActivityMallConfig> getRECHARGE_ID_MAP() {
        return RECHARGE_ID_MAP;
    }

    public ConcurrentHashMap<Integer, xddq.pb.DreamDataMsg> getEXTRA_EQU_MAP() {
        return EXTRA_EQU_MAP;
    }
}
