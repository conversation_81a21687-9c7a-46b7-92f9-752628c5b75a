package xddq.activity.data;

import java.util.concurrent.ConcurrentHashMap;

public class ActivityConditionData {
    //活动条件数据
//    message ActivityConditionData {
//        optional int32 conditionId = 1; //条件id
//        optional string value = 2; //条件进度值
//        optional bool isGetReward = 3; //是否领奖
//        optional bool isGetMasterRewards = 4; //是否领取附加奖励
//        repeated ActivityMultiReward multiReward = 5; //多档奖励
//        optional int32 infinites = 6; //无限档位数
//        optional int32 triggerDropTimes = 7; //触发掉落次数
//        optional int64 completeTime = 8;//完成时间
//    }
    private int conditionId;
    private int value;
    private boolean isGetReward;
    private boolean isGetMasterRewards;

    private ConcurrentHashMap<Integer, ActivityMultiReward> multiReward = new ConcurrentHashMap<>();

    private int infinites;

    private int triggerDropTimes;

    private long completeTime;

    private int triggerType;

    private int param;

    public int getConditionId() {
        return conditionId;
    }

    public void setConditionId(int conditionId) {
        this.conditionId = conditionId;
    }

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public boolean isGetReward() {
        return isGetReward;
    }

    public void setGetReward(boolean getReward) {
        isGetReward = getReward;
    }

    public boolean isGetMasterRewards() {
        return isGetMasterRewards;
    }

    public void setGetMasterRewards(boolean getMasterRewards) {
        isGetMasterRewards = getMasterRewards;
    }

    public ConcurrentHashMap<Integer, ActivityMultiReward> getMultiReward() {
        return multiReward;
    }

    public void setMultiReward(ConcurrentHashMap<Integer, ActivityMultiReward> multiReward) {
        this.multiReward = multiReward;
    }

    public int getInfinites() {
        return infinites;
    }

    public void setInfinites(int infinites) {
        this.infinites = infinites;
    }

    public int getTriggerDropTimes() {
        return triggerDropTimes;
    }

    public void setTriggerDropTimes(int triggerDropTimes) {
        this.triggerDropTimes = triggerDropTimes;
    }

    public long getCompleteTime() {
        return completeTime;
    }

    public void setCompleteTime(long completeTime) {
        this.completeTime = completeTime;
    }

    public int getTriggerType() {
        return triggerType;
    }

    public void setTriggerType(int triggerType) {
        this.triggerType = triggerType;
    }

    public int getParam() {
        return param;
    }

    public void setParam(int param) {
        this.param = param;
    }
}
