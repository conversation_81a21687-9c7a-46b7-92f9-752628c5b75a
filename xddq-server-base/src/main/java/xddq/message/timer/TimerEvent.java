package xddq.message.timer;

import io.netty.buffer.ByteBuf;
import xddq.message.ICommand;
import xddq.message.ITimerEvent;
import xddq.message.pb.PbMessage;
import xddq.server.thread.ServerThread;

public abstract class TimerEvent extends PbMessage implements ITimerEvent {
    // 定时结束时间
    private long end;
    // 定时剩余时间
    private long remain;
    // 执行次数
    private int loop;
    // 间隔时间
    private long delay;
    // 创建时间
    private long createTime;
    // 执行线程
    private ServerThread thread;
    // 是否结束
    private boolean finish;

    /**
     * 计时事件
     */
    protected TimerEvent(long end) {
        this.end = end;
        this.loop = 1;
    }

    /**
     * 循环事件
     */
    protected TimerEvent(int loop, long delay) {
        this.loop = loop;
        this.delay = delay;
        this.end = System.currentTimeMillis() + delay;
    }

    public long remain() {
        return this.end - System.currentTimeMillis();
    }

    public long getEnd() {
        return end;
    }

    public void setEnd(long end) {
        this.end = end;
    }

    public long getRemain() {
        return remain;
    }

    public void setRemain(long remain) {
        this.remain = remain;
    }

    public int getLoop() {
        return loop;
    }

    public void setLoop(int loop) {
        this.loop = loop;
        this.end = System.currentTimeMillis() + delay;
    }

    public long getDelay() {
        return delay;
    }

    public void setDelay(long delay) {
        this.delay = delay;
    }

    public long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(long createTime) {
        this.createTime = createTime;
    }

    public ServerThread getThread() {
        return thread;
    }

    public void setThread(ServerThread thread) {
        this.thread = thread;
    }

    public boolean isFinish() {
        return finish;
    }

    public void setFinish(boolean finish) {
        this.finish = finish;
    }

    @Override
    public Object getExecutor() {
        return thread;
    }

    // 获取长度
    public int length() throws Exception{
        return 0;
    }

    // 写入
    public boolean write(ByteBuf buf) throws Exception{return true;}

    //读取
    public boolean read(ByteBuf buf) throws Exception{return true;}
}
