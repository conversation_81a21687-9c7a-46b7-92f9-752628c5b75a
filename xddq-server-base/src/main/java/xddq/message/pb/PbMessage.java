package xddq.message.pb;

import com.alibaba.fastjson.annotation.JSONField;
import io.netty.channel.ChannelHandlerContext;
import xddq.message.IMessage;

/**
 * 消息
 */
public abstract class PbMessage implements IMessage {

    // 接收消息context
    private transient ChannelHandlerContext ctx;
    // 参数
    private transient Object executor;
    // 賬號ID
    private long accountId;

    @Override
    public ChannelHandlerContext getCtx() {
        return ctx;
    }

    @Override
    public void setCtx(ChannelHandlerContext ctx) {
        this.ctx = ctx;
    }

    public Object getExecutor() {
        return executor;
    }

    public void setExecutor(Object executor) {
        this.executor = executor;
    }

    public long getAccountId() {
        return accountId;
    }

    public void setAccountId(long accountId) {
        this.accountId = accountId;
    }

    protected String format(String json){
        return json.replaceAll(" {4}", "").replaceAll(" {2}", "").replaceAll("\t", "").replaceAll(",\r", ", ").replaceAll(",\n", ", ").replaceAll("\r", "").replaceAll("\n", "");
    }

    protected String getName(){
        return this.getClass().getSimpleName().split("_")[0];
    }
}

