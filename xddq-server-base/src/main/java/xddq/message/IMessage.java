package xddq.message;

import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;

public interface IMessage extends ICommand {

    // 获取长度
    int length() throws Exception;

    // 写入
    boolean write(ByteBuf buf) throws Exception;

    //读取
    boolean read(ByteBuf buf) throws Exception;

    ChannelHandlerContext getCtx();

    void setCtx(ChannelHandlerContext ctx);

    Object getExecutor();

    void setExecutor(Object executor);

    long getAccountId();

    void setAccountId(long accountId);
}
