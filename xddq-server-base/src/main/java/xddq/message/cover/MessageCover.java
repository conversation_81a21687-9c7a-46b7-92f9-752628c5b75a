package xddq.message.cover;

public class MessageCover {

//    /**
//     * 转换消息到bytes
//     */
//    public ByteBuf castMessageToBuffer(ByteBufAllocator alloc, boolean direct, IMessage message) throws Exception {
//
//        byte[] bytes = null;
//        // 计算头长度
//        int headLength = countS2CMessageHeadLength(message);
//        // 计算内容长度
//        int length = message.length();
//
//        ByteBuf buf;
//        if (direct) {
//            buf = alloc.directBuffer(headLength + length);
//        } else {
//            buf = alloc.heapBuffer(headLength + length);
//        }
//        // 写入消息头
//        ProtobufUtil.writeFixed32NoTag(buf, headLength + length - Integer.BYTES);
//        ProtobufUtil.writeFixed32NoTag(buf, message.getId());
//        ProtobufUtil.writeUInt32NoTag(buf, message.getState());
//
//        message.write(buf);
//
//        return buf;
//    }
//
//    /**
//     * 计算C2S消息头长度
//     */
//    private int countC2SMessageHeadLength(IMessage message) {
//        int headSize = 0;
//        headSize += ProtobufUtil.computeFixed32SizeNoTag(0);
//        headSize += ProtobufUtil.computeFixed32SizeNoTag(message.getSendIndex());
//        headSize += ProtobufUtil.computeFixed32SizeNoTag(message.getId());
//        if (clientNeedOrder) {
//            headSize += ProtobufUtil.computeUInt32SizeNoTag(message.getClientOrder());
//        }
//        return headSize;
//    }
//
//    /**
//     * 转换客户端发送消息到bytes
//     */
//    public ByteBuf castC2SMessageToBuffer(ByteBufAllocator alloc, boolean direct, int sendIndex, IMessage message) throws Exception {
//        // 计算头长度
//        int headLength = countS2CMessageHeadLength(message);
//        // 计算内容长度
//        int length = message.length();
//
//        ByteBuf buf;
//        if (direct) {
//            buf = alloc.directBuffer(headLength + length);
//        } else {
//            buf = alloc.heapBuffer(headLength + length);
//        }
//
//        ProtobufUtil.writeFixed32NoTag(buf, headLength + length - Integer.BYTES);
//        ProtobufUtil.writeFixed32NoTag(buf, sendIndex);
//        ProtobufUtil.writeFixed32NoTag(buf, message.getId());
//        if (clientNeedOrder) {
//            ProtobufUtil.writeUInt32NoTag(buf, message.getClientOrder());
//        }
//        message.write(buf);
//
//        return buf;
//    }
}
