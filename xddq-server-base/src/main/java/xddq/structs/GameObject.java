package xddq.structs;

public abstract class GameObject {

    // 唯一编号
    protected long id;
    // 模型编号
    protected int modelId;

    public GameObject() {
    }

    public GameObject(long id, int modelId) {
        super();
        this.id = id;
        this.modelId = modelId;
    }

    public long getId() {
        return id;
    }

    public GameObject setId(long id) {
        this.id = id;
        return this;
    }

    public int getModelId() {
        return modelId;
    }

    public GameObject setModelId(int modelId) {
        this.modelId = modelId;
        return this;
    }
}
