package xddq.structs;

import java.util.HashMap;

public class Weight {

    private int id;

    private int value;

    private HashMap<String, String> parameters = new HashMap<>();

    private int weight;

    public int getId() {
        return id;
    }

    public Weight setId(int id) {
        this.id = id;
        return this;
    }

    public int getValue() {
        return value;
    }

    public Weight setValue(int value) {
        this.value = value;
        return this;
    }

    public HashMap<String, String> getParameters() {
        return parameters;
    }

    public Weight setParameters(HashMap<String, String> parameters) {
        this.parameters = parameters;
        return this;
    }

    public int getWeight() {
        return weight;
    }

    public Weight setWeight(int weight) {
        this.weight = weight;
        return this;
    }
}
