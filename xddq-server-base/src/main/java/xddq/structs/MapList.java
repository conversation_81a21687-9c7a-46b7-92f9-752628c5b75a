package xddq.structs;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

public class MapList<T, V> {

    private final HashMap<T, V> map = new HashMap<>();

    private final List<T> list = new ArrayList<>();

    public synchronized void add(T key, V value) {
        if (!map.containsKey(key)) {
            list.add(key);
        }
        map.put(key, value);
    }

    public synchronized List<V> pop(int size) {
        List<V> values = new ArrayList<>();
        int num = 0;
        while (num < size && !list.isEmpty()) {
            T key = list.remove(0);
            V value = map.remove(key);
            values.add(value);
            num++;
        }
        return values;
    }
}
