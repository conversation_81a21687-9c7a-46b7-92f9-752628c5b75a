package xddq.herorank.datas;

import java.util.TreeMap;
import java.util.concurrent.ConcurrentHashMap;

public class HeroRankDatas {

    private final ConcurrentHashMap<Integer, Object> ranks = new ConcurrentHashMap<>();

    private final TreeMap<Integer, String> weekRewards = new TreeMap<>();

    private final TreeMap<Integer, String> dailyRewards = new TreeMap<>();

    private long lastRankRefreshTime;

    private long lastRankDailyRewardTime;

    private long lastRankWeekRewardTime;

    public ConcurrentHashMap<Integer, Object> getRanks() {
        return ranks;
    }

    public TreeMap<Integer, String> getWeekRewards() {
        return weekRewards;
    }

    public TreeMap<Integer, String> getDailyRewards() {
        return dailyRewards;
    }

    public long getLastRankRefreshTime() {
        return lastRankRefreshTime;
    }

    public HeroRankDatas setLastRankRefreshTime(long lastRankRefreshTime) {
        this.lastRankRefreshTime = lastRankRefreshTime;
        return this;
    }

    public long getLastRankDailyRewardTime() {
        return lastRankDailyRewardTime;
    }

    public HeroRankDatas setLastRankDailyRewardTime(long lastRankDailyRewardTime) {
        this.lastRankDailyRewardTime = lastRankDailyRewardTime;
        return this;
    }

    public long getLastRankWeekRewardTime() {
        return lastRankWeekRewardTime;
    }

    public HeroRankDatas setLastRankWeekRewardTime(long lastRankWeekRewardTime) {
        this.lastRankWeekRewardTime = lastRankWeekRewardTime;
        return this;
    }
}
