package xddq.herorank.structs;

public class RankRecord {

    private RankPlayer fightPlayer;

    private RankPlayer defensePlayer;

    private RankMonster defenseMonster;

    private boolean win;

    private int fightPlayerRealms;

    private int defensePlayerRealms;

    private int fightPlayerRank;

    private int defensePlayerRank;

    private long createTime;

    public RankPlayer getFightPlayer() {
        return fightPlayer;
    }

    public RankRecord setFightPlayer(RankPlayer fightPlayer) {
        this.fightPlayer = fightPlayer;
        return this;
    }

    public RankPlayer getDefensePlayer() {
        return defensePlayer;
    }

    public RankRecord setDefensePlayer(RankPlayer defensePlayer) {
        this.defensePlayer = defensePlayer;
        return this;
    }

    public RankMonster getDefenseMonster() {
        return defenseMonster;
    }

    public RankRecord setDefenseMonster(RankMonster defenseMonster) {
        this.defenseMonster = defenseMonster;
        return this;
    }

    public boolean isWin() {
        return win;
    }

    public RankRecord setWin(boolean win) {
        this.win = win;
        return this;
    }

    public int getFightPlayerRealms() {
        return fightPlayerRealms;
    }

    public RankRecord setFightPlayerRealms(int fightPlayerRealms) {
        this.fightPlayerRealms = fightPlayerRealms;
        return this;
    }

    public int getDefensePlayerRealms() {
        return defensePlayerRealms;
    }

    public RankRecord setDefensePlayerRealms(int defensePlayerRealms) {
        this.defensePlayerRealms = defensePlayerRealms;
        return this;
    }

    public int getFightPlayerRank() {
        return fightPlayerRank;
    }

    public RankRecord setFightPlayerRank(int fightPlayerRank) {
        this.fightPlayerRank = fightPlayerRank;
        return this;
    }

    public int getDefensePlayerRank() {
        return defensePlayerRank;
    }

    public RankRecord setDefensePlayerRank(int defensePlayerRank) {
        this.defensePlayerRank = defensePlayerRank;
        return this;
    }

    public long getCreateTime() {
        return createTime;
    }

    public RankRecord setCreateTime(long createTime) {
        this.createTime = createTime;
        return this;
    }
}













































