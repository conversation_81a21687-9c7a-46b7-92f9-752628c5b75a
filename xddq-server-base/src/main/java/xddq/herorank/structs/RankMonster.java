package xddq.herorank.structs;

public class RankMonster {

    private int monsterId;

    private int level;

    private String name;

    private int appearId;

    private int headId;

    public RankMonster() {
    }

    public RankMonster(int monsterId, int level, String name) {
        this.monsterId = monsterId;
        this.level = level;
        this.name = name;
    }

    public int getMonsterId() {
        return monsterId;
    }

    public RankMonster setMonsterId(int monsterId) {
        this.monsterId = monsterId;
        return this;
    }

    public int getLevel() {
        return level;
    }

    public RankMonster setLevel(int level) {
        this.level = level;
        return this;
    }

    public String getName() {
        return name;
    }

    public RankMonster setName(String name) {
        this.name = name;
        return this;
    }

    public int getHeadId() {
        return headId;
    }

    public RankMonster setHeadId(int headId) {
        this.headId = headId;
        return this;
    }

    public int getAppearId() {
        return appearId;
    }

    public RankMonster setAppearId(int appearId) {
        this.appearId = appearId;
        return this;
    }
}
