package xddq.herorank.structs;

import java.util.Vector;
import java.util.concurrent.ConcurrentHashMap;

public class HeroRankData {
    private long lastRefreshTime;
    private int historyRank;
    private ConcurrentHashMap<Integer, Integer> tasks = new ConcurrentHashMap<>();
    private long dailyRefreshTime;
    private long lastWeekRewardTime;
    private long lastGetWeekRewardTime;
    private Vector<Integer> targetRanks = new Vector<>();
    private Vector<RankRecord> records = new Vector<>();

    public long getLastRefreshTime() {
        return lastRefreshTime;
    }

    public HeroRankData setLastRefreshTime(long lastRefreshTime) {
        this.lastRefreshTime = lastRefreshTime;
        return this;
    }

    public int getHistoryRank() {
        return historyRank;
    }

    public HeroRankData setHistoryRank(int historyRank) {
        this.historyRank = historyRank;
        return this;
    }

    public long getDailyRefreshTime() {
        return dailyRefreshTime;
    }

    public HeroRankData setDailyRefreshTime(long dailyRefreshTime) {
        this.dailyRefreshTime = dailyRefreshTime;
        return this;
    }

    public ConcurrentHashMap<Integer, Integer> getTasks() {
        return tasks;
    }

    public HeroRankData setTasks(ConcurrentHashMap<Integer, Integer> tasks) {
        this.tasks = tasks;
        return this;
    }

    public long getLastWeekRewardTime() {
        return lastWeekRewardTime;
    }

    public HeroRankData setLastWeekRewardTime(long lastWeekRewardTime) {
        this.lastWeekRewardTime = lastWeekRewardTime;
        return this;
    }

    public long getLastGetWeekRewardTime() {
        return lastGetWeekRewardTime;
    }

    public HeroRankData setLastGetWeekRewardTime(long lastGetWeekRewardTime) {
        this.lastGetWeekRewardTime = lastGetWeekRewardTime;
        return this;
    }

    public Vector<Integer> getTargetRanks() {
        return targetRanks;
    }

    public HeroRankData setTargetRanks(Vector<Integer> targetRanks) {
        this.targetRanks = targetRanks;
        return this;
    }

    public Vector<RankRecord> getRecords() {
        return records;
    }

    public HeroRankData setRecords(Vector<RankRecord> records) {
        this.records = records;
        return this;
    }
}
