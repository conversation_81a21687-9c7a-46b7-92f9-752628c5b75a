package xddq.player.structs;

public class PrivilegeCard {
    private long smallEndTime = 0L;
    private long smallRewardTime = 0L;
    private long bigEndTIme = 0L;
    private long bigRewardTime = 0L;

    public long getSmallEndTime() {
        return smallEndTime;
    }

    public void setSmallEndTime(long smallEndTime) {
        this.smallEndTime = smallEndTime;
    }

    public long getSmallRewardTime() {
        return smallRewardTime;
    }

    public void setSmallRewardTime(long smallRewardTime) {
        this.smallRewardTime = smallRewardTime;
    }

    public long getBigEndTIme() {
        return bigEndTIme;
    }

    public void setBigEndTIme(long bigEndTIme) {
        this.bigEndTIme = bigEndTIme;
    }

    public long getBigRewardTime() {
        return bigRewardTime;
    }

    public void setBigRewardTime(long bigRewardTime) {
        this.bigRewardTime = bigRewardTime;
    }

}
