package xddq.player.structs;

import xddq.activity.structs.ActivityData;
import xddq.activity.structs.SignData;
import xddq.attribute.consts.CalculatorTypeEnum;
import xddq.battle.structs.BattleData;
import xddq.career.structs.CareerData;
import xddq.chara.structs.CharaData;
import xddq.chara.structs.TitleData;
import xddq.cloud.structs.CloudData;
import xddq.cloudrefine.structs.CloudRefineData;
import xddq.count.structs.Count;
import xddq.count.structs.ICountable;
import xddq.destiny.structs.Destiny;
import xddq.herorank.structs.HeroRankData;
import xddq.homeland.structs.Homeland;
import xddq.homeland.structs.HomelandAutoCollectItem;
import xddq.item.structs.Item;
import xddq.magic.structs.MagicData;
import xddq.magictreasure.structs.MagicTreasureData;
import xddq.mail.structs.MailData;
import xddq.model.LawLooksEnterMsg;
import xddq.palace.structs.PalaceData;
import xddq.pet.structs.PetBackpack;
import xddq.pupil.structs.PupilSystem;
import xddq.rank.structs.RankData;
import xddq.recharge.structs.RechargeData;
import xddq.rule.structs.RuleTrialData;
import xddq.rule.structs.WorldRuleData;
import xddq.secrettower.structs.SecretTowerData;
import xddq.spirit.structs.SpiritData;
import xddq.starTrial.StarTrialData;
import xddq.talent.structs.TalnetData;
import xddq.task.structs.Task;
import xddq.tower.structs.TowerData;
import xddq.union.structs.UnionData;
import xddq.wildboss.structs.WildBoss;
import xddq.worldrule.structs.WorldRule;
import xddq.yard.structs.YardData;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

public class Player extends Person implements ICountable {
    private long rt = 0L; // 下次刷新累天充值时间

    public long getRt() {
        return rt;
    }

    public void setRt(long rt) {
        this.rt = rt;
    }

    //角色版本哈
    private int version;
    // 创建角色时所在服务器Key
    private int createServerKey;
    // 创建角色时所在平台名
    private String createWebName;
    // 登录角色时所在平台名
    private String loginWebName;
    // 登录角色时所在服务器Key
    private int loginServerKey;
    // 虚拟服ID
    private int virtualGameServerId;
    // 账号id
    private long userId;
    // 邀请码
    private transient String code;
    // token
    private transient String token;
    // 账号名字
    private String userName;
    // 玩家登录登出状态
    private transient int loginState;
    // 是否首次登陆
    private boolean firstLogin;
    // 是否内部人员登录
    private transient boolean insider;
    // 创建IP
    private String createIp;
    // 登陆类型
    private int lastLoginType;
    // 登录IP
    private String lastLoginIp;
    // 最后一次登录时间
    private long lastLoginTime;
    private PrivilegeCard privilegeCard = new PrivilegeCard();
    // 删除状态
    private boolean delete;
    // 封停状态
    private boolean forbid;
    // 封停时间
    private long forbidTime;
    // 是否在线
    private transient boolean online = false;
    // 下线时间
    private long quitTime = 0;
    // 强制下线时间
    private transient long kickTime = 0;

    // 每日下一次刷新时间
    private long nextDailyRefreshTime;
    // 每周下一次刷新时间
    private long nextWeekRefreshTime;

    // 职业
    private int job;
    // 最后保存时间
    private long lastSaveTime;

    /************************ 玩家属性开始 ************************/
    // 当前境界id
    private int realmsId;
    // 妖气
    private long exp;
    // 当前使用的分身下标
    private int useSeparationIdx;
    //飞升状态 0未飞升1修仙2修魔
    private int soaringState;
    //斩道次数(计算消耗用)
    private int resetSoaringTimes;
    //标记当前界面
    private int currentPage;
    private boolean isAutoHomeland;
    private List<HomelandAutoCollectItem> homelandAutoCollectItem = new ArrayList<>();

    private Homeland homeland = null;

    /************************ 玩家属性结束 ************************/
    /************************ 梦境属性开始 ************************/
    //梦境等级
    private int dreamLv;
    //梦境升级结束时间
    private long dreamLvUpEndTime;
    //下次可免费加速时间
    private long freeSpeedUpCdEndTime;


    /************************ 梦境属性开始 ************************/
    /************************ 计数属性开始 ************************/
    // 计数
    private ConcurrentHashMap<String, Count> counts = new ConcurrentHashMap<>();
    /************************ 计数属性结束 ************************/
    /************************ 模块属性计算开始 ************************/
    // 各个模块加成
    private transient ConcurrentHashMap<CalculatorTypeEnum, ConcurrentHashMap<Integer, Long>> attributeCalculators = new ConcurrentHashMap<>();
    // 各个模块技能
    private transient ConcurrentHashMap<CalculatorTypeEnum, ConcurrentHashMap<Integer, Integer>> skillCalculators = new ConcurrentHashMap<>();
    // 全部属性
    private transient ConcurrentHashMap<Integer, Long> totalAttributes = new ConcurrentHashMap<>();
    // 全部技能
    private transient ConcurrentHashMap<Integer, Integer> totalSkills = new ConcurrentHashMap<>();
    // 临时全部属性
    private transient ConcurrentHashMap<Integer, Long> tempTotalAttributes = new ConcurrentHashMap<>();
    // Gm增加属性(登录清空)
    private transient ConcurrentHashMap<Integer, Long> gmAttributes;
    // 玩家总战力
    private long fightValue;
    // 玩家临时总战力
    private long tempFightValue;
    /************************ 模块属性计算结束 ************************/
    /************************ 外观数据开始 ************************/
    // 各个背包对应物品， key为背包类型
    private CharaData charaData = new CharaData();

    private TitleData titleData = new TitleData();

    public TitleData getTitleData() {
        return titleData;
    }

    public void setTitleData(TitleData titleData) {
        this.titleData = titleData;
    }
    /************************ 弯管数据结束 ************************/
    /************************ 背包数据开始 ************************/
    // 各个背包对应物品， key为背包类型
    private ConcurrentHashMap<Integer, Long> backpack = new ConcurrentHashMap<>();
    /************************ 背包数据结束 ************************/
    /************************ 装备数据开始 ************************/
    private AtomicInteger equipId = new AtomicInteger();
    private SignData signData = new SignData();

    public SignData getSignData() {
        return signData;
    }

    public void setSignData(SignData signData) {
        this.signData = signData;
    }

    private ConcurrentHashMap<Long, Item> unDealEquips = new ConcurrentHashMap<>();
    // 部位 -> 装备信息
    private ConcurrentHashMap<Integer, Item> equipMap = new ConcurrentHashMap<>();
    // 部位 -> 装备精炼等级
    private ConcurrentHashMap<Integer, Integer> equipRefine = new ConcurrentHashMap<>();
    // 精良等阶
    private int equipAdvance;

    private ConcurrentHashMap<Integer, Integer> equipActivate = new ConcurrentHashMap<>();
    /************************ 装备数据结束 ************************/
    /************************ 任务数据开始 ************************/
    // 任务数据
    private ConcurrentHashMap<Integer, Task> tasks = new ConcurrentHashMap<>();
    // 完成主线任务数量
    private int finishMainTask;
    /************************ 任务数据结束 ************************/
    /************************ 聊天数据开始 ************************/
    // 禁言时间
    private long forbiddenTime;
    /************************ 聊天数据结束 ************************/
    /************************ 通关数据开始 ************************/
    // 通过关卡
    private int passStageId;
    /************************ 通关数据结束 ************************/
    /************************ 系统解锁数据开始 ************************/
    // 最近解锁时间
    private transient long lastUnlockTime;

    // 砍树掉落上限
    private ConcurrentHashMap<Integer, Integer> dropMap = new ConcurrentHashMap<>();

    // 兑换码使用情况,value 0表示没兑换
    // key: ONL, value: 今日在线时间（单位：秒）
    private ConcurrentHashMap<String, Integer> keyMap = new ConcurrentHashMap<>();

    public ConcurrentHashMap<String, Integer> getKeyMap() {
        return keyMap;
    }

    public void setKeyMap(ConcurrentHashMap<String, Integer> keyMap) {
        this.keyMap = keyMap;
    }

    private ConcurrentHashMap<Integer, Long> actMap = new ConcurrentHashMap<>();

    public ConcurrentHashMap<Integer, Long> getActMap() {
        return actMap;
    }

    public void setActMap(ConcurrentHashMap<Integer, Long> actMap) {
        this.actMap = actMap;
    }

    public ConcurrentHashMap<Integer, Integer> getDropMap() {
        return dropMap;
    }

    public void setDropMap(ConcurrentHashMap<Integer, Integer> dropMap) {
        this.dropMap = dropMap;
    }

    // 商城限时物品列表
    //private ConcurrentHashMap<Integer, ChargeData> mallBuyCountList = new ConcurrentHashMap<>();

    // 解锁系统
    private ConcurrentHashMap<Integer, Integer> unlock = new ConcurrentHashMap<>();
    // 解锁系统奖励
    private ConcurrentHashMap<Integer, Integer> sysReward = new ConcurrentHashMap<>();
    /************************ 系统解锁数据结束 ************************/
    /************************ 客户端数据开始 ************************/
    // 客户端
    private ConcurrentHashMap<String, String> clientSettings = new ConcurrentHashMap<>();
    /************************ 客户端数据结束 ************************/
    /************************ 仙友数据开始 ************************/
    private ConcurrentHashMap<Integer, Destiny> relatives = new ConcurrentHashMap<>();

    private int destinyEnergy;

    private long lastDestinyRecoverTime;
    /************************ 仙友数据结束 ************************/
    /************************ 云台数据开始 ************************/
    private CloudData cloud = new CloudData();
    private CloudRefineData cloudRefineData = new CloudRefineData();
    /************************ 云台数据结束 ************************/
    /************************ 妖王数据开始 ************************/
    private WildBoss wildBoss = new WildBoss();
    /************************ 妖王数据结束 ************************/
    /************************ 灵兽数据开始 ************************/
    private AtomicInteger petId = new AtomicInteger();

    private PetBackpack petBackpack = new PetBackpack();
    /************************ 灵兽数据结束 ************************/
    /************************ 灵兽数据开始 ************************/
    private SpiritData spiritData = new SpiritData();
    /************************ 灵兽数据结束 ************************/
    /************************ 天赋数据开始 ************************/
    private TalnetData talnetData = new TalnetData();
    /************************ 天赋数据结束 ************************/
    /************************ 神通数据开始 ************************/
    private MagicData magicData = new MagicData();
    /************************ 神通数据结束 ************************/
    /************************ 法宝数据开始 ************************/
    private MagicTreasureData magicTreasureData = new MagicTreasureData();
    /************************ 法宝数据结束 ************************/
    /************************ 邮件数据开始 ************************/
    private MailData mailData = new MailData();
    /************************ 邮件数据结束 ************************/
    /************************ 排行数据开始 ************************/
    private RankData rankData = new RankData();
    /************************ 排行数据结束 ************************/
    /************************ 道途数据开始 ************************/
    private CareerData careerData = new CareerData();
    /************************ 道途数据结束 ************************/
    /************************ 六道数据开始 ************************/
    private SecretTowerData secretTowerData = new SecretTowerData();
    /************************ 六道数据结束 ************************/
    /************************ 镇妖塔数据开始 ************************/
    private TowerData towerData = new TowerData();
    /************************ 镇妖塔数据结束 ************************/
    /************************ 镇妖塔数据开始 ************************/
    private HeroRankData heroRankData = new HeroRankData();
    /************************ 镇妖塔数据结束 ************************/
    /************************ 斗法数据开始 ************************/
    private BattleData battleData = new BattleData();
    /************************ 斗法数据结束 ************************/
    /************************ 斗法数据开始 ************************/
    private RechargeData rechargeData = new RechargeData();
    /************************ 斗法数据结束 ************************/
    /************************ 妖盟数据开始 ************************/
    private UnionData unionData = new UnionData();
    /************************ 妖盟数据结束 ************************/
    /************************ 宗门数据开始 ************************/
    private PupilSystem pupil = new PupilSystem();
    private AtomicInteger pupilId = new AtomicInteger();
    /************************ 宗门数据结束 ************************/
    /************************ 充值活动数据开始 ************************/
    private ActivityData activityData = new ActivityData();
    /************************ 充值活动数据结束 ************************/
    /************************ 仙居数据开始 ************************/
    private YardData yardData = new YardData();

    private LawLooksEnterMsg lawLookData = new LawLooksEnterMsg();

    /************************ 仙居数据结束 ************************/

    private PalaceData palaceData = new PalaceData();

    private WorldRule worldRule = new WorldRule();

    private StarTrialData starTrialData = new StarTrialData();

    public StarTrialData getStarTrialData() {
        return starTrialData;
    }

    public Player setStarTrialData(StarTrialData starTrialData) {
        this.starTrialData = starTrialData;
        return this;
    }

//    private WorldRuleData worldRuleData = new WorldRuleData();
//
//    public WorldRuleData getWorldRuleData() {
//        return worldRuleData;
//    }
//
//    public Player setWorldRuleData(WorldRuleData worldRuleData) {
//        this.worldRuleData = worldRuleData;
//        return this;
//    }

    public LawLooksEnterMsg getLawLookData() {
        return lawLookData;
    }

    public void setLawLookData(LawLooksEnterMsg lawLookData) {
        this.lawLookData = lawLookData;
    }

    private RuleTrialData ruleTrialData = new RuleTrialData();

    public RuleTrialData getRuleTrialData() {
        return ruleTrialData;
    }

    public void setRuleTrialData(RuleTrialData ruleTrialData) {
        this.ruleTrialData = ruleTrialData;
    }

    public int getVersion() {
        return version;
    }

    public Player setVersion(int version) {
        this.version = version;
        return this;
    }

    public int getCreateServerKey() {
        return createServerKey;
    }

    public Player setCreateServerKey(int createServerKey) {
        this.createServerKey = createServerKey;
        return this;
    }

    public String getCreateWebName() {
        return createWebName;
    }

    public int getCurrentPage() {
        return currentPage;
    }

    public void setCurrentPage(int currentPage) {
        this.currentPage = currentPage;
    }

    public Player setCreateWebName(String createWebName) {
        this.createWebName = createWebName;
        return this;
    }

    public String getLoginWebName() {
        return loginWebName;
    }

    public Player setLoginWebName(String loginWebName) {
        this.loginWebName = loginWebName;
        return this;
    }

    public int getLoginServerKey() {
        return loginServerKey;
    }

    public Player setLoginServerKey(int loginServerKey) {
        this.loginServerKey = loginServerKey;
        return this;
    }

    public int getVirtualGameServerId() {
        return virtualGameServerId;
    }

    public Player setVirtualGameServerId(int virtualGameServerId) {
        this.virtualGameServerId = virtualGameServerId;
        return this;
    }

    public long getUserId() {
        return userId;
    }

    public Player setUserId(long userId) {
        this.userId = userId;
        return this;
    }

    public String getCode() {
        return code;
    }

    public Player setCode(String code) {
        this.code = code;
        return this;
    }

    public String getToken() {
        return token;
    }

    public Player setToken(String token) {
        this.token = token;
        return this;
    }

    public String getUserName() {
        return userName;
    }

    public Player setUserName(String userName) {
        this.userName = userName;
        return this;
    }

    public int getLoginState() {
        return loginState;
    }

    public Player setLoginState(int loginState) {
        this.loginState = loginState;
        return this;
    }

    public boolean isFirstLogin() {
        return firstLogin;
    }

    public Player setFirstLogin(boolean firstLogin) {
        this.firstLogin = firstLogin;
        return this;
    }

    public boolean isInsider() {
        return insider;
    }

    public Player setInsider(boolean insider) {
        this.insider = insider;
        return this;
    }

    public String getCreateIp() {
        return createIp;
    }

    public Player setCreateIp(String createIp) {
        this.createIp = createIp;
        return this;
    }

    public int getLastLoginType() {
        return lastLoginType;
    }

    public Player setLastLoginType(int lastLoginType) {
        this.lastLoginType = lastLoginType;
        return this;
    }

    public String getLastLoginIp() {
        return lastLoginIp;
    }

    public Player setLastLoginIp(String lastLoginIp) {
        this.lastLoginIp = lastLoginIp;
        return this;
    }

    public long getLastLoginTime() {
        return lastLoginTime;
    }

    public Player setLastLoginTime(long lastLoginTime) {
        this.lastLoginTime = lastLoginTime;
        return this;
    }

    public boolean isDelete() {
        return delete;
    }

    public Player setDelete(boolean delete) {
        this.delete = delete;
        return this;
    }

    public boolean isForbid() {
        return forbid;
    }

    public Player setForbid(boolean forbid) {
        this.forbid = forbid;
        return this;
    }

    public long getForbidTime() {
        return forbidTime;
    }

    public Player setForbidTime(long forbidTime) {
        this.forbidTime = forbidTime;
        return this;
    }

    public boolean isOnline() {
        return online;
    }

    public Player setOnline(boolean online) {
        this.online = online;
        return this;
    }

    public long getQuitTime() {
        return quitTime;
    }

    public Player setQuitTime(long quitTime) {
        this.quitTime = quitTime;
        return this;
    }

    public long getKickTime() {
        return kickTime;
    }

    public Player setKickTime(long kickTime) {
        this.kickTime = kickTime;
        return this;
    }

    public long getNextDailyRefreshTime() {
        return nextDailyRefreshTime;
    }

    public Player setNextDailyRefreshTime(long nextDailyRefreshTime) {
        this.nextDailyRefreshTime = nextDailyRefreshTime;
        return this;
    }

    public long getNextWeekRefreshTime() {
        return nextWeekRefreshTime;
    }

    public Player setNextWeekRefreshTime(long nextWeekRefreshTime) {
        this.nextWeekRefreshTime = nextWeekRefreshTime;
        return this;
    }

    @Override
    public int getJob() {
        return job;
    }

    @Override
    public Player setJob(int job) {
        this.job = job;
        return this;
    }

    public ConcurrentHashMap<Integer, Long> getBackpack() {
        return backpack;
    }

    public Player setBackpack(ConcurrentHashMap<Integer, Long> backpack) {
        this.backpack = backpack;
        return this;
    }

    public ConcurrentHashMap<Integer, Item> getEquipMap() {
        return equipMap;
    }

    public Player setEquipMap(ConcurrentHashMap<Integer, Item> equipMap) {
        this.equipMap = equipMap;
        return this;
    }

    public ConcurrentHashMap<CalculatorTypeEnum, ConcurrentHashMap<Integer, Long>> getAttributeCalculators() {
        return attributeCalculators;
    }

    public Player setAttributeCalculators(ConcurrentHashMap<CalculatorTypeEnum, ConcurrentHashMap<Integer, Long>> attributeCalculators) {
        this.attributeCalculators = attributeCalculators;
        return this;
    }

    public ConcurrentHashMap<CalculatorTypeEnum, ConcurrentHashMap<Integer, Integer>> getSkillCalculators() {
        return skillCalculators;
    }

    public Player setSkillCalculators(ConcurrentHashMap<CalculatorTypeEnum, ConcurrentHashMap<Integer, Integer>> skillCalculators) {
        this.skillCalculators = skillCalculators;
        return this;
    }

    public ConcurrentHashMap<Integer, Long> getTotalAttributes() {
        return totalAttributes;
    }

    public Player setTotalAttributes(ConcurrentHashMap<Integer, Long> totalAttributes) {
        this.totalAttributes = totalAttributes;
        return this;
    }

    public ConcurrentHashMap<Integer, Integer> getTotalSkills() {
        return totalSkills;
    }

    public Player setTotalSkills(ConcurrentHashMap<Integer, Integer> totalSkills) {
        this.totalSkills = totalSkills;
        return this;
    }

    public ConcurrentHashMap<Integer, Long> getTempTotalAttributes() {
        return tempTotalAttributes;
    }

    public Player setTempTotalAttributes(ConcurrentHashMap<Integer, Long> tempTotalAttributes) {
        this.tempTotalAttributes = tempTotalAttributes;
        return this;
    }

    public ConcurrentHashMap<Integer, Long> getGmAttributes() {
        return gmAttributes;
    }

    public Player setGmAttributes(ConcurrentHashMap<Integer, Long> gmAttributes) {
        this.gmAttributes = gmAttributes;
        return this;
    }

    public long getTempFightValue() {
        return tempFightValue;
    }

    public Player setTempFightValue(long tempFightValue) {
        this.tempFightValue = tempFightValue;
        return this;
    }

    public int getRealmsId() {
        return realmsId;
    }

    public Player setRealmsId(int realmsId) {
        this.realmsId = realmsId;
        return this;
    }

    public long getExp() {
        return exp;
    }

    public Player setExp(long exp) {
        this.exp = exp;
        return this;
    }

    public long getFightValue() {
        return fightValue;
    }

    public Player setFightValue(long fightValue) {
        this.fightValue = fightValue;
        return this;
    }

    public int getUseSeparationIdx() {
        return useSeparationIdx;
    }

    public Player setUseSeparationIdx(int useSeparationIdx) {
        this.useSeparationIdx = useSeparationIdx;
        return this;
    }

    public int getSoaringState() {
        return soaringState;
    }

    public Player setSoaringState(int soaringState) {
        this.soaringState = soaringState;
        return this;
    }

    public int getResetSoaringTimes() {
        return resetSoaringTimes;
    }

    public Player setResetSoaringTimes(int resetSoaringTimes) {
        this.resetSoaringTimes = resetSoaringTimes;
        return this;
    }

    @Override
    public ConcurrentHashMap<String, Count> getCounts() {
        return counts;
    }

    public Player setCounts(ConcurrentHashMap<String, Count> counts) {
        this.counts = counts;
        return this;
    }

    public AtomicInteger getEquipId() {
        return equipId;
    }

    public Player setEquipId(AtomicInteger equipId) {
        this.equipId = equipId;
        return this;
    }

    public ConcurrentHashMap<Long, Item> getUnDealEquips() {
        return unDealEquips;
    }

    public Player setUnDealEquips(ConcurrentHashMap<Long, Item> unDealEquips) {
        this.unDealEquips = unDealEquips;
        return this;
    }

    public int getDreamLv() {
        return dreamLv;
    }

    public Player setDreamLv(int dreamLv) {
        this.dreamLv = dreamLv;
        return this;
    }

    public long getDreamLvUpEndTime() {
        return dreamLvUpEndTime;
    }

    public Player setDreamLvUpEndTime(long dreamLvUpEndTime) {
        this.dreamLvUpEndTime = dreamLvUpEndTime;
        return this;
    }

    public long getFreeSpeedUpCdEndTime() {
        return freeSpeedUpCdEndTime;
    }

    public Player setFreeSpeedUpCdEndTime(long freeSpeedUpCdEndTime) {
        this.freeSpeedUpCdEndTime = freeSpeedUpCdEndTime;
        return this;
    }

    public ConcurrentHashMap<Integer, Task> getTasks() {
        return tasks;
    }

    public Player setTasks(ConcurrentHashMap<Integer, Task> tasks) {
        this.tasks = tasks;
        return this;
    }

    public long getForbiddenTime() {
        return forbiddenTime;
    }

    public Player setForbiddenTime(long forbiddenTime) {
        this.forbiddenTime = forbiddenTime;
        return this;
    }

    public int getPassStageId() {
        return passStageId;
    }

    public Player setPassStageId(int passStageId) {
        this.passStageId = passStageId;
        return this;
    }

    public ConcurrentHashMap<Integer, Integer> getUnlock() {
        return unlock;
    }

    public Player setUnlock(ConcurrentHashMap<Integer, Integer> unlock) {
        this.unlock = unlock;
        return this;
    }

    public long getLastUnlockTime() {
        return lastUnlockTime;
    }

    public Player setLastUnlockTime(long lastUnlockTime) {
        this.lastUnlockTime = lastUnlockTime;
        return this;
    }

    public int getFinishMainTask() {
        return finishMainTask;
    }

    public Player setFinishMainTask(int finishMainTask) {
        this.finishMainTask = finishMainTask;
        return this;
    }

    public ConcurrentHashMap<Integer, Integer> getSysReward() {
        return sysReward;
    }

    public Player setSysReward(ConcurrentHashMap<Integer, Integer> sysReward) {
        this.sysReward = sysReward;
        return this;
    }

    public ConcurrentHashMap<String, String> getClientSettings() {
        return clientSettings;
    }

    public Player setClientSettings(ConcurrentHashMap<String, String> clientSettings) {
        this.clientSettings = clientSettings;
        return this;
    }

    public ConcurrentHashMap<Integer, Destiny> getRelatives() {
        return relatives;
    }

    public Player setRelatives(ConcurrentHashMap<Integer, Destiny> relatives) {
        this.relatives = relatives;
        return this;
    }

    public int getDestinyEnergy() {
        return destinyEnergy;
    }

    public Player setDestinyEnergy(int destinyEnergy) {
        this.destinyEnergy = destinyEnergy;
        return this;
    }

    public long getLastDestinyRecoverTime() {
        return lastDestinyRecoverTime;
    }

    public Player setLastDestinyRecoverTime(long lastDestinyRecoverTime) {
        this.lastDestinyRecoverTime = lastDestinyRecoverTime;
        return this;
    }

    public CloudData getCloud() {
        return cloud;
    }

    public Player setCloud(CloudData cloud) {
        this.cloud = cloud;
        return this;
    }

    public WildBoss getWildBoss() {
        return wildBoss;
    }

    public Player setWildBoss(WildBoss wildBoss) {
        this.wildBoss = wildBoss;
        return this;
    }

    public PetBackpack getPetBackpack() {
        return petBackpack;
    }

    public Player setPetBackpack(PetBackpack petBackpack) {
        this.petBackpack = petBackpack;
        return this;
    }

    public AtomicInteger getPetId() {
        return petId;
    }

    public Player setPetId(AtomicInteger petId) {
        this.petId = petId;
        return this;
    }

    public SpiritData getSpiritData() {
        return spiritData;
    }

    public Player setSpiritData(SpiritData spiritData) {
        this.spiritData = spiritData;
        return this;
    }

    public long getLastSaveTime() {
        return lastSaveTime;
    }

    public Player setLastSaveTime(long lastSaveTime) {
        this.lastSaveTime = lastSaveTime;
        return this;
    }

    public WorldRule getWorldRule() {
        return worldRule;
    }

    public void setWorldRule(WorldRule worldRule) {
        this.worldRule = worldRule;
    }

    public TalnetData getTalnetData() {
        return talnetData;
    }

    public Player setTalnetData(TalnetData talnetData) {
        this.talnetData = talnetData;
        return this;
    }

    public MagicData getMagicData() {
        return magicData;
    }

    public Player setMagicData(MagicData magicData) {
        this.magicData = magicData;
        return this;
    }

    public MagicTreasureData getMagicTreasureData() {
        return magicTreasureData;
    }

    public Player setMagicTreasureData(MagicTreasureData magicTreasureData) {
        this.magicTreasureData = magicTreasureData;
        return this;
    }

    public ConcurrentHashMap<Integer, Integer> getEquipActivate() {
        return equipActivate;
    }

    public Player setEquipActivate(ConcurrentHashMap<Integer, Integer> equipActivate) {
        this.equipActivate = equipActivate;
        return this;
    }

    public ConcurrentHashMap<Integer, Integer> getEquipRefine() {
        return equipRefine;
    }

    public Player setEquipRefine(ConcurrentHashMap<Integer, Integer> equipRefine) {
        this.equipRefine = equipRefine;
        return this;
    }

    public int getEquipAdvance() {
        return equipAdvance;
    }

    public Player setEquipAdvance(int equipAdvance) {
        this.equipAdvance = equipAdvance;
        return this;
    }

    public MailData getMailData() {
        return mailData;
    }

    public Player setMailData(MailData mailData) {
        this.mailData = mailData;
        return this;
    }

    public RankData getRankData() {
        return rankData;
    }

    public Player setRankData(RankData rankData) {
        this.rankData = rankData;
        return this;
    }

    public CareerData getCareerData() {
        return careerData;
    }

    public Player setCareerData(CareerData careerData) {
        this.careerData = careerData;
        return this;
    }

    public SecretTowerData getSecretTowerData() {
        return secretTowerData;
    }

    public Player setSecretTowerData(SecretTowerData secretTowerData) {
        this.secretTowerData = secretTowerData;
        return this;
    }

    public TowerData getTowerData() {
        return towerData;
    }

    public Player setTowerData(TowerData towerData) {
        this.towerData = towerData;
        return this;
    }

    public HeroRankData getHeroRankData() {
        return heroRankData;
    }

    public Player setHeroRankData(HeroRankData heroRankData) {
        this.heroRankData = heroRankData;
        return this;
    }

    public BattleData getBattleData() {
        return battleData;
    }

    public Player setBattleData(BattleData battleData) {
        this.battleData = battleData;
        return this;
    }

    public RechargeData getRechargeData() {
        return rechargeData;
    }

    public Player setRechargeData(RechargeData rechargeData) {
        this.rechargeData = rechargeData;
        return this;
    }

    public UnionData getUnionData() {
        return unionData;
    }

    public Player setUnionData(UnionData unionData) {
        this.unionData = unionData;
        return this;
    }

    public PupilSystem getPupil() {
        return pupil;
    }

    public void setPupil(PupilSystem pupil) {
        this.pupil = pupil;
    }

    public CharaData getCharaData() {
        return charaData;
    }

    public void setCharaData(CharaData charaData) {
        this.charaData = charaData;
    }

    public AtomicInteger getPupilId() {
        return pupilId;
    }

    public void setPupilId(AtomicInteger pupilId) {
        this.pupilId = pupilId;
    }

    public ActivityData getActivityData() {
        return activityData;
    }

    public Player setActivityData(ActivityData activityData) {
        this.activityData = activityData;
        return this;
    }

    public YardData getYardData() {
        return yardData;
    }

    public Player setYardData(YardData yardData) {
        this.yardData = yardData;
        return this;
    }

//    public ConcurrentHashMap<Integer, ChargeData> getMallBuyCountList() {
//        return mallBuyCountList;
//    }
//
//    public void setMallBuyCountList(ConcurrentHashMap<Integer, ChargeData> mallBuyCountList) {
//        this.mallBuyCountList = mallBuyCountList;
//    }

    public List<HomelandAutoCollectItem> getHomelandAutoCollectItem() {
        return homelandAutoCollectItem;
    }

    public Player setHomelandAutoCollectItem(List<HomelandAutoCollectItem> homelandAutoCollectItem) {
        this.homelandAutoCollectItem = homelandAutoCollectItem;
        return this;
    }

    public boolean isAutoHomeland() {
        return isAutoHomeland;
    }

    public Player setAutoHomeland(boolean autoHomeland) {
        isAutoHomeland = autoHomeland;
        return this;
    }

    public Homeland getHomeland() {
        return homeland;
    }

    public Player setHomeland(Homeland homeland) {
        this.homeland = homeland;
        return this;
    }

    public PalaceData getPalaceData() {
        return palaceData;
    }

    public Player setPalaceData(PalaceData palaceData) {
        this.palaceData = palaceData;
        return this;
    }

    public PrivilegeCard getPrivilegeCard() {
        return privilegeCard;
    }

    public Player setPrivilegeCard(PrivilegeCard privilegeCard) {
        this.privilegeCard = privilegeCard;
        return this;
    }

    public CloudRefineData getCloudRefineData() {
        return cloudRefineData;
    }

    public void setCloudRefineData(CloudRefineData cloudRefineData) {
        this.cloudRefineData = cloudRefineData;
    }
}
