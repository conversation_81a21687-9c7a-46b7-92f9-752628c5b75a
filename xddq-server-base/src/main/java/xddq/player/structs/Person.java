package xddq.player.structs;

import xddq.map.structs.MapObject;

public abstract class Person extends MapObject {
    // 名字
    protected String name;
    // 职业
    protected int job;
    // 种族
    protected int race;
    // 创建时间
    protected long createTime;

    public String toString() {
        return "[" + name + "](" + id + ")";
    }

    public String getName() {
        return name;
    }

    public Person setName(String name) {
        this.name = name;
        return this;
    }

    public int getJob() {
        return job;
    }

    public Person setJob(int job) {
        this.job = job;
        return this;
    }

    public int getRace() {
        return race;
    }

    public Person setRace(int race) {
        this.race = race;
        return this;
    }

    public long getCreateTime() {
        return createTime;
    }

    public Person setCreateTime(long createTime) {
        this.createTime = createTime;
        return this;
    }
}
