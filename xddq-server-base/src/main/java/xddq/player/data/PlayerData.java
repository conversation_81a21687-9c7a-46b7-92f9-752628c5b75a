package xddq.player.data;

import xddq.player.structs.Player;

import java.util.concurrent.ConcurrentHashMap;

public class PlayerData {

    /**
     * 玩家数据缓存
     */
    private ConcurrentHashMap<Long, Player> players = new ConcurrentHashMap<>();
    /**
     * 玩家在线列表（key为角色编号）
     */
    private ConcurrentHashMap<Long, Player> online = new ConcurrentHashMap<>();
    /**
     * 账号在线列表（key为账号名称）
     */
    private ConcurrentHashMap<Long, Player> user = new ConcurrentHashMap<>();
    /**
     * 封禁的角色列表
     */
    private ConcurrentHashMap<Long, Long> forbidList = new ConcurrentHashMap<>();
    /**
     * 等待保存的玩家
     */
    private ConcurrentHashMap<Long, Player> waitingSave = new ConcurrentHashMap<>();

    public ConcurrentHashMap<Long, Player> getPlayers() {
        return players;
    }

    public ConcurrentHashMap<Long, Player> getOnline() {
        return online;
    }

    public ConcurrentHashMap<Long, Player> getUser() {
        return user;
    }

    public ConcurrentHashMap<Long, Long> getForbidList() {
        return forbidList;
    }

    public ConcurrentHashMap<Long, Player> getWaitingSave() {
        return waitingSave;
    }
}
