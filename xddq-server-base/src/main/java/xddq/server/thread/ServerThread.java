package xddq.server.thread;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import xddq.message.ICommand;
import xddq.message.timer.TimerEvent;
import xddq.server.ICommandExecutor;
import xddq.server.ICommandFilter;

import java.util.ArrayList;
import java.util.concurrent.LinkedBlockingQueue;

public class ServerThread extends Thread {

    // 日志
    private final Logger log = LoggerFactory.getLogger(ServerThread.class);

    private final Logger handlerlog = LoggerFactory.getLogger("HandlerLog");
    // 执行队列
    private final LinkedBlockingQueue<ICommand> command_queue = new LinkedBlockingQueue<>();
    // 计时线程
    private final TimerThread timer;
    // 执行器
    private ICommandExecutor executor;
    // 过滤器
    private final ArrayList<ICommandFilter> beforeFilters = new ArrayList<>();
    // 运行标志
    private volatile boolean stop;

    private boolean processingCompleted = false;

    public ServerThread(ThreadGroup group, String threadName,
                        ICommandExecutor executor, TimerThread timer) {
        super(group, threadName);
        this.executor = executor;
        this.timer = timer;
        this.setUncaughtExceptionHandler((t, e) -> {
            log.error("Main Thread UncaughtExceptionHandler:", e);
            stop = true;
            command_queue.clear();
        });
    }

    public void run() {
        stop = false;
        while (!stop) {
            ICommand command = command_queue.poll();
            if (command == null) {
                try {
                    synchronized (this) {
                        processingCompleted = true;
                        wait();
                    }
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
            } else {
                try {
                    processingCompleted = false;
                    long start = System.currentTimeMillis();
                    boolean result = false;
                    for (ICommandFilter iCommandFilter : beforeFilters) {
                        if (!iCommandFilter.filter(command)) {
                            result = true;
                            break;
                        }
                    }
                    if (result)
                        continue;
                    executor.action(command);

                    long end = System.currentTimeMillis();
                    if (end - start > 10)
                        handlerlog.info(this.getName() + "-->" + command.getClass().getSimpleName() + " run:" + (end - start));
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
            }
        }
    }

    public void stop(boolean flag) {
        stop = flag;
        this.command_queue.clear();
        try {
            synchronized (this) {
                if (processingCompleted) {
                    processingCompleted = false;
                    notify();
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 添加命令
     *
     * @param command 命令
     */
    public boolean addCommand(ICommand command) {
        if (stop) {
            return false;
        }
        try {
            this.command_queue.add(command);
            if (!processingCompleted) {
                return true;
            }
            synchronized (this) {
                if (processingCompleted) {
                    processingCompleted = false;
                    notify();
                }
            }
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        return false;
    }

    /**
     * 添加过滤器
     */
    public void addBeforeCommandFitler(ICommandFilter filter) {
        this.beforeFilters.add(filter);
    }

    /**
     * 添加定时事件
     */
    public void addTimerEvent(TimerEvent event) {
        if (timer != null)
            this.timer.addTimerEvent(this, event);
    }

    /**
     * 移除定时事件
     */
    public void removeTimerEvent(TimerEvent event) {
        if (timer != null)
            this.timer.removeTimerEvent(event);
    }

    public boolean isStop() {
        return stop;
    }

    public int getCommandNumber() {
        return command_queue.size();
    }
}
