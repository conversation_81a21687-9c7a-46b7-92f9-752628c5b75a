package xddq.server.thread;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import xddq.message.ICommand;
import xddq.message.timer.TimerEvent;
import xddq.server.ICommandExecutor;
import xddq.server.ICommandFilter;
import xddq.server.map.IMap;

import java.util.concurrent.ConcurrentHashMap;

public abstract class MapServer<T> {

    protected static Logger log = LoggerFactory.getLogger(MapServer.class);
    // 名字
    protected String name;
    // 线程
    protected ServerThread thread;
    // 列表
    protected ConcurrentHashMap<T, IMap<T>> maps;
    // 已经停止
    protected volatile boolean stop;

    protected MapServer() {

    }

    protected MapServer(ThreadGroup group, String name,
                        ICommandExecutor executor, TimerThread timerThread) {
        this.name = name;
        this.maps = new ConcurrentHashMap<>();
        this.thread = new ServerThread(group, this.getName() + "-->Main",
                executor, timerThread);
    }

    protected abstract void init();

    public void start() {
        init();
        this.thread.start();
    }

    public void stop(boolean flag) {
        log.error("服务器" + name + "停止。");
        try {
            throw new Exception();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        this.stop = flag;
        this.thread.stop(flag);
    }

    public void addCommand(ICommand command) {
        if (thread != null && !thread.isStop()) {
            // 添加命令
            thread.addCommand(command);
        } else {
            log.error("服务器" + name + "添加Commmand时不存在或已经停止。");
            try {
                throw new Exception();
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
    }

    public void addTimerEvent(TimerEvent event) {
        if (thread != null && !thread.isStop()) {
            // 添加命令
            thread.addTimerEvent(event);
        } else {
            log.error("服务器" + name + "添加Timer时不存在或已经停止。");
            try {
                throw new Exception();
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
    }

    public String getName() {
        return name;
    }

    /**
     * 添加
     */
    public boolean addMap(T key, IMap<T> map) {
        maps.put(key, map);
        addMapTimerEvent(map);
        return true;
    }

    /**
     * 增加定时事件
     */
    protected abstract void addMapTimerEvent(IMap<T> map);

    /**
     * 移除
     */
    public boolean removeMap(T key) {
        maps.remove(key);
        return true;
    }

    /**
     * 获取
     */
    public boolean containsMap(T key){
        return maps.containsKey(key);
    }

    public ConcurrentHashMap<T, IMap<T>> getMaps() {
        return maps;
    }

    /**
     * 添加过滤器
     */
    public void addBeforeCommandFitler(ICommandFilter filter) {
        this.thread.addBeforeCommandFitler(filter);
    }

    /**
     * 是否已经停止
     */
    public boolean isStop() {
        return stop;
    }
}
