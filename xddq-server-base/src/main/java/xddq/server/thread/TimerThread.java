package xddq.server.thread;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import xddq.message.timer.TimerEvent;

import java.util.Iterator;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.LinkedBlockingQueue;

public class TimerThread  extends Timer {

    private static final Logger log = LoggerFactory.getLogger(TimerThread.class);
    // 事件
    private final LinkedBlockingQueue<TimerEvent> events = new LinkedBlockingQueue<>();
    // 定时任务
    private TimerTask task;
    // 定时间隔
    private final int heart;

    public TimerThread(String name, int heart) {
        super(name + "--Timer");
        this.heart = heart;
    }

    public void start() {
        task = new TimerTask() {
            @Override
            public void run() {
                // 事件迭代器
                Iterator<TimerEvent> it = events.iterator();
                // 派发事件
                while (it.hasNext()) {
                    TimerEvent event = it.next();
                    if (event.isFinish()) {
                        it.remove();
                        log.debug("移除计时事件【" + event.getClass().getSimpleName() + "(" + event.getExecutor() + ")】, 目前事件数量:" + events.size());
                        continue;
                    }
                    if (event.remain() <= 0) {
                        if (event.getLoop() > 0) {
                            event.setLoop(event.getLoop() - 1);
                        } else {
                            event.setLoop(event.getLoop());
                        }
                        if (event.getThread() != null && !event.getThread().isStop()) {
                            // 需要放入主线程
                            event.getThread().addCommand(event);
                        } else {
                            // 结束掉
                            event.setFinish(true);
                        }
                    }
                    if (event.getLoop() == 0 || event.isFinish()) {
                        it.remove();
                        log.debug("移除计时事件【" + event.getClass().getSimpleName() + "(" + event.getExecutor() + ")】, 目前事件数量:" + events.size());
                    }
                }
            }
        };
        this.schedule(task, 0, heart);
    }

    public void stop(boolean flag) {
        events.clear();
        if (task != null)
            task.cancel();
        this.cancel();
    }

    /**
     * 添加定时事件
     */
    public void addTimerEvent(ServerThread thread, TimerEvent event) {
        event.setThread(thread);
        events.add(event);
        log.debug("添加计时事件【" + event.getClass().getSimpleName() + "(" + thread + ")】, 目前事件数量:" + events.size());
    }

    /**
     * 移除定时事件
     */
    public void removeTimerEvent(TimerEvent event) {
        events.remove(event);
        log.debug("移除计时事件【" + event.getClass().getSimpleName() + "(" + event.getThread() + ")】, 目前事件数量:"
                + events.size());
    }

}
