package xddq.server.group;

import xddq.server.ICommandExecutor;
import xddq.server.impl.LogicServer;
import xddq.server.map.IMap;
import xddq.server.thread.MapServer;
import xddq.server.thread.TimerThread;

import java.util.concurrent.atomic.AtomicInteger;

public class GameMapServerGroup extends MapServerGroup<Long> {

    private final AtomicInteger index = new AtomicInteger();

    public GameMapServerGroup(ThreadGroup group, String name,
                              ICommandExecutor executor, TimerThread timerThread) {
        super(group, name, executor, timerThread);
    }

    public GameMapServerGroup(ThreadGroup group, String name, ICommandExecutor executor, TimerThread timerThread,
                              int minServers, int maxServers, int minPerServer) {
        super(group, name, executor, timerThread, minServers, maxServers, minPerServer);
    }

    @Override
    protected MapServer<Long> createMapServer(IMap<Long> map, ThreadGroup group, String name) {
        return new LogicServer(group, name + "_" + index.getAndIncrement() + "_main", executor,
                timerThread);
    }

    @Override
    protected void removeMapServer(IMap<Long> map, MapServer<Long> mapServer) {

    }

}
