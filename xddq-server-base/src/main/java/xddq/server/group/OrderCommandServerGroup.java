package xddq.server.group;

import xddq.message.ICommand;
import xddq.message.timer.TimerEvent;
import xddq.server.ICommandExecutor;
import xddq.server.exceptions.UnsupportedKeyTypeException;
import xddq.server.thread.ServerThread;
import xddq.server.thread.TimerThread;

public class OrderCommandServerGroup extends CommandServerGroup {

    private final int threadCount;

    public OrderCommandServerGroup(ThreadGroup group, String name, ICommandExecutor executor, TimerThread timer, int threadCount) {
        super(group, name, executor, timer, threadCount, threadCount, 0);
        this.threadCount = threadCount;
    }

    public synchronized boolean addCommand(ICommand command) {
        throw new UnsupportedOperationException("OrderCommandServerGroup not support addCommand(ICommand command)");
    }

    public synchronized boolean addCommand(Object key, ICommand command) throws UnsupportedKeyTypeException {
        ServerThread thread = threads.get(getShard(key));
        thread.addCommand(command);
        return true;
    }

    /**
     * 添加定时事件
     *
     * @param event 定时事件
     */
    public void addTimerEvent(Object key, TimerEvent event) throws UnsupportedKeyTypeException {
        ServerThread thread = threads.get(getShard(key));
        if (timer != null)
            this.timer.addTimerEvent(thread, event);
    }

    private int getShard(Object key) throws UnsupportedKeyTypeException {
        if (key == null) {
            throw new NullPointerException("key can not be null");
        }
        if (key instanceof String) {
            return getShardInfo(key.hashCode());
        } else if (key instanceof Integer) {
            return getShardInfo((int) key);
        } else if (key instanceof Long) {
            return getShardInfo((long) key);
        }

        throw new UnsupportedKeyTypeException("key type not support" + key);
    }

    private int getShardInfo(long key) {
        if ((threadCount & (threadCount - 1)) == 0) {
            key = Math.abs(key & (threadCount - 1));
        } else {
            key = Math.abs(key % threadCount);
        }
        return (int) key;
    }
}
