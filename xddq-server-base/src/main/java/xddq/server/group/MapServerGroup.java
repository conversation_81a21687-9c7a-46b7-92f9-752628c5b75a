package xddq.server.group;

import xddq.server.ICommandExecutor;
import xddq.server.map.IMap;
import xddq.server.thread.MapServer;
import xddq.server.thread.TimerThread;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public abstract class MapServerGroup<T> {
    // 最小服务器数
    private static final int MIN_SERVERS = 1;
    // 最大服务器数
    private static final int MAX_SERVERS = 10;
    // 最小服务器承载量
    private static final int MIN_PER_SERVER = 5;
    // 服务器列表
    protected ConcurrentHashMap<MapServer<T>, Boolean> serverSet = new ConcurrentHashMap<>();
    // 组所在服务器（保证同一副本地图在同一服务器中创建）
    protected ConcurrentHashMap<Long, MapServer<T>> servers = new ConcurrentHashMap<>();
    // 组中地图数量
    protected ConcurrentHashMap<Long, Integer> groupCount = new ConcurrentHashMap<>();
    // 线程组
    protected ThreadGroup group;
    // 命令执行器
    protected ICommandExecutor executor;
    // 计时线程
    protected TimerThread timerThread;
    // 线程名称
    protected String name;

    // 最小服务器数
    protected int minServers = 1;
    // 最大服务器数
    protected int maxServers = 10;
    // 最小服务器承载量
    protected int minPerServer = 5;

    protected MapServerGroup(ThreadGroup group, String name, ICommandExecutor executor, TimerThread timerThread) {
        this(group, name, executor, timerThread, MIN_SERVERS, MAX_SERVERS, MIN_PER_SERVER);
    }

    protected MapServerGroup(ThreadGroup group, String name, ICommandExecutor executor, TimerThread timerThread, int minServers, int maxServers, int minPerServer) {
        this.group = group;
        this.name = name;
        this.executor = executor;
        this.timerThread = timerThread;
        this.minServers = minServers;
        this.maxServers = maxServers;
        this.minPerServer = minPerServer;
    }

    /**
     * 添加
     */
    public synchronized MapServer<T> addMap(IMap<T> map) {
        long groupId = map.getGroupId();
        // 当前群组是否已经分配线程
        MapServer<T> mapServer = servers.get(groupId);

        // 未分配线程
        if (mapServer == null) {
            boolean full = true;
            int minMap = Integer.MAX_VALUE;
            // 循环遍历已有服务器线程
            for (Map.Entry<MapServer<T>, Boolean> entry : serverSet.entrySet()) {
                MapServer<T> server = entry.getKey();
                // 未达到最小副本数量
                int size = server.getMaps().size();
                if (size < minPerServer) {
                    full = false;
                }
                // 找到副本数量最小的线程
                if (size < minMap) {
                    mapServer = server;
                    minMap = size;
                }
            }

            if (mapServer != null && !full) {
                if (groupId != 0) {
                    servers.put(groupId, mapServer);
                }
            } else if (serverSet.size() < maxServers) {
                mapServer = createMapServer(map, group, name);
                mapServer.start();

                if (groupId != 0) {
                    servers.put(groupId, mapServer);
                }
                serverSet.put(mapServer, true);
            } else if (mapServer != null) {
                if (groupId != 0) {
                    servers.put(groupId, mapServer);
                }
            }
        }

        if (mapServer == null) {
            return null;
        }
        mapServer.addMap(map.getMapId(), map);

        if (groupId != 0) {
            if (groupCount.containsKey(groupId)) {
                groupCount.put(groupId, groupCount.get(groupId) + 1);
            } else {
                groupCount.put(groupId, 1);
            }
        }

        return mapServer;
    }

    /**
     * 移除
     */
    public synchronized MapServer<T> removeMap(IMap<T> map) {
        long groupId = map.getGroupId();
        // 当前群组是否已经分配线程
        MapServer<T> mapServer = servers.get(groupId);
        if (mapServer != null) {
            int mapCount = 0;
            if (groupId != 0) {
                if (groupCount.containsKey(groupId)) {
                    mapCount = groupCount.get(groupId);
                    mapCount--;
                }
                if (mapCount <= 0) {
                    groupCount.remove(groupId);
                    servers.remove(map.getGroupId());
                } else {
                    groupCount.put(groupId, mapCount);
                }
            }
            mapServer.removeMap(map.getMapId());

            if (serverSet.size() > minServers && mapServer.getMaps().isEmpty() && mapServer.containsMap(map.getMapId())) {
                serverSet.remove(mapServer);
                mapServer.stop(true);
                removeMapServer(map, mapServer);
            }
        }

        return mapServer;
    }

    /**
     * 获取服务器
     */
    public MapServer<T> getServer(long groupId) {
        // 当前群组是否已经分配线程
        return servers.get(groupId);
    }

    /**
     * 创建服务器
     */
    protected abstract MapServer<T> createMapServer(IMap<T> map, ThreadGroup group, String name);

    /**
     * 创建服务器
     */
    protected abstract void removeMapServer(IMap<T> map, MapServer<T> mapServer);
}
