package xddq.server.group;

import xddq.message.ICommand;
import xddq.server.ICommandExecutor;
import xddq.server.ICommandFilter;
import xddq.server.thread.ServerThread;
import xddq.server.thread.TimerThread;

import java.util.concurrent.CopyOnWriteArrayList;

public class CommandServerGroup {
    // 最小服务器数
    protected static int MIN_SERVERS = 1;
    // 最大服务器数
    protected static int MAX_SERVERS = 10;
    // 最小服务器承载量
    protected static int MIN_PER_SERVER = 5;
    // 线程组
    protected ThreadGroup group;
    // 命令执行器
    protected ICommandExecutor executor;
    // 计时线程
    protected TimerThread timer;
    // 线程名称
    protected String name;
    // 最小服务器数
    protected int minServers = 1;
    // 最大服务器数
    protected int maxServers = 10;
    // 最小服务器承载量
    protected int minPerServer = 5;
    // 运行标志
    protected boolean stop;
    // 执行线程
    protected CopyOnWriteArrayList<ServerThread> threads = new CopyOnWriteArrayList<>();
    // 过滤器
    protected CopyOnWriteArrayList<ICommandFilter> beforeFilters = new CopyOnWriteArrayList<>();

    public CommandServerGroup(ThreadGroup group, String name, ICommandExecutor executor, TimerThread timer) {
        this(group, name, executor, timer, MIN_SERVERS, MAX_SERVERS, MIN_PER_SERVER);
    }

    public CommandServerGroup(ThreadGroup group, String name, ICommandExecutor executor, TimerThread timer, int minServers,
                              int maxServers, int minPerServer) {
        this.group = group;
        this.name = name;
        this.executor = executor;
        this.timer = timer;
        this.minServers = minServers;
        this.maxServers = maxServers;
        this.minPerServer = minPerServer;

        for (int i = 0; i < minServers; i++) {
            ServerThread thread = createSimpleServerThread();
            threads.add(thread);
        }
    }

    public void stop(boolean flag) {
        stop = flag;
        for (ServerThread thread : threads) {
            thread.stop(flag);
        }
    }

    /**
     * 添加命令
     */
    public synchronized boolean addCommand(ICommand command) {
        if (stop) {
            return false;
        }
        int min = Integer.MAX_VALUE;
        ServerThread excuteThread = null;
        for (ServerThread thread : threads) {
            int size = thread.getCommandNumber();
            if (size < this.minPerServer || this.minPerServer < 0) {
                thread.addCommand(command);
                return true;
            }
            if (size < min) {
                min = size;
                excuteThread = thread;
            }
        }

        if (threads.size() < maxServers) {
            ServerThread thread = createSimpleServerThread();
            threads.add(thread);
            thread.addCommand(command);
            return true;
        } else if (excuteThread != null) {
            excuteThread.addCommand(command);
            return true;
        }

        return false;
    }

    /**
     * 添加过滤器
     */
    public void addBeforeCommandFitler(ICommandFilter filter) {
        this.beforeFilters.add(filter);
        for (ServerThread thread : threads) {
            thread.addBeforeCommandFitler(filter);
        }
    }


    /**
     * 创建新执行线程
     */
    private ServerThread createSimpleServerThread() {
        ServerThread thread = new ServerThread(group, name, executor, timer);
        for (ICommandFilter filter : beforeFilters) {
            thread.addBeforeCommandFitler(filter);
        }
        thread.start();
        return thread;
    }
}
