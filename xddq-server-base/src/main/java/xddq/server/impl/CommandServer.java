package xddq.server.impl;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import xddq.server.ICommandServer;

public abstract class CommandServer implements ICommandServer, Runnable {

    protected CommandServer() {
    }


    // 运行
    public void run() {
        // 注册服务器关闭线程
        Runtime.getRuntime().addShutdownHook(new Thread(new CloseByExit()));
    }

    /**
     * 关闭
     */
    protected abstract void stop();

    // 服务器关闭线程
    private class CloseByExit implements Runnable {

        // 日志
        private final Logger log = LoggerFactory.getLogger(CloseByExit.class);

        public CloseByExit() {
        }

        @Override
        public void run() {
            // 执行关闭事件
            stop();
            log.info("Server Stop!");
        }
    }
}