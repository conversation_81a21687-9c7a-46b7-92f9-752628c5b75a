package xddq.server.impl;

import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.group.ChannelGroup;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import xddq.config.manager.ServerPropertiesManager;
import xddq.data.DataPool;
import xddq.db.MysqlServer;
import xddq.http.HttpServer;
import xddq.manager.BaseManagerPool;
import xddq.message.IMessage;
import xddq.message.timer.TimerEvent;
import xddq.network.NettyServer;
import xddq.player.structs.Player;
import xddq.server.IServerScript;
import xddq.server.exceptions.UnsupportedKeyTypeException;
import xddq.server.executor.CommandExecutor;
import xddq.server.group.GameMapServerGroup;
import xddq.server.group.OrderCommandServerGroup;
import xddq.server.thread.ServerThread;
import xddq.server.thread.TimerThread;
import xddq.server.timer.ServerHeartTimer;
import xddq.timer.message.MessageStatisticsTimer;
import xddq.utils.ConfigPropertiesUtil;
import xddq.utils.TimeUtil;

import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class GameServer extends NettyServer {

    // 默认服务器信息文件
    private static final String defaultConfig = "app.properties";

    // 最大线程处理数量
    public static int MAX_THREAD = 16;
    // 最大连接数
    private volatile int MAX_CONNECT = 0;
    // 服务器启动时间
    public static long START_TIME = System.currentTimeMillis();
    // 游戏服唯一标识
    public static int SERVER_KEY = 0;
    // 开服时间
    public static Date SERVER_OPEN_TIME;

    // 本服冲榜奖励是否发放 1 已发放 0 没发放
    public static int SERVER_RANK_REWARD = 0;

    // 服务器心跳
    public static int HEART = 50;
    // 版本号(这里未使用)
    public static int GAME_VERSION = 1;
    // 是否正在调试
    public static boolean DEBUG = false;

    // 单例
    private static final Object obj = new Object();
    // 服务器实例
    private static volatile GameServer server;

    private static final Logger log = LoggerFactory.getLogger(GameServer.class);

    // 服务器线程组
    private ThreadGroup group;
    // 计时线程
    private TimerThread timerThread;
    // 数据库服务器
    private MysqlServer mysqlServer;
    // 服务器统计线程
    private ServerThread statisticsThread;

    // 服务器线程
    private ServerThread serverThread;
    // 登录线程
    private OrderCommandServerGroup loginGroup;
    // 普通地图线程组
    private GameMapServerGroup cityServerGroup;
    // 异步线程组
    private ExecutorService asynchronousGroup;

    private IServerScript serverScript;

    private GameServer(int port) {
        super(port);
    }

    public static GameServer getInstance(String serverConfig) {
        if (server == null) {
            synchronized (obj) {
                if (server == null) {
                    newInstance(serverConfig);
                }
            }
        }
        return server;
    }

    public static GameServer getInstance() {
        if (server == null) {
            synchronized (obj) {
                if (server == null) {
                    newInstance(defaultConfig);
                }
            }
        }
        return server;
    }

    public static void newInstance(String serverConfig) {
        try {
            ServerPropertiesManager.getInstance().initialize(serverConfig);

            int port = Integer.parseInt(ConfigPropertiesUtil.getConfigProperty("game.server.port"));
            server = new GameServer(port);
        } catch (IOException e) {
            log.error("配置文件初始化失败", e);
            System.exit(0);
        }
    }

    @Override
    public void run() {
        long starttime = System.currentTimeMillis();
        try {
            commandServer = new CommandServerImpl();
            new Thread((CommandServerImpl) commandServer).start();
        } catch (Exception e) {
            log.error("命令服务器初始失败", e);
            System.exit(0);
        }

        try {
            mysqlServer = new MysqlServer();
        } catch (Exception e) {
            log.error("数据库连接错误", e);
            System.exit(0);
        }

        log.info("读取区服配置开始");
        // 加载config
        SERVER_KEY = Integer
                .parseInt(ConfigPropertiesUtil.getConfigProperty("game.server.key"));

        try {
            SERVER_OPEN_TIME = new SimpleDateFormat("yyyy-MM-dd").parse(ConfigPropertiesUtil.getConfigProperty("game.server.open"));
            SERVER_RANK_REWARD = Integer.parseInt(ConfigPropertiesUtil.getConfigProperty("game.server.reward"));

            long now = TimeUtil.getCurrentTimeMillis();
            long rewardTime = GameServer.SERVER_OPEN_TIME.getTime() + TimeUtil.WEEK;

            if (now > rewardTime) {
                SERVER_RANK_REWARD = 1;
            } else {
                SERVER_RANK_REWARD = 0;
            }
            System.out.println("Server open time=" + SERVER_OPEN_TIME.getTime() + ", rewarded=" + SERVER_RANK_REWARD);
        } catch (ParseException e) {
            log.error("开始时间错误", e);
            System.exit(0);
        }

        log.info("读取区服配置结束");

        log.info("管理器初始数据开始");
        // 初始化管理器初始数据
        try {
            BaseManagerPool.getInstance().init();
        } catch (Exception e) {
            log.error("BaseManagerPool init error", e);
            System.exit(0);
        }

        try {
            DataPool.getInstance().init();
        } catch (Exception e) {
            log.error("DataPool init error", e);
            System.exit(0);
        }

        try {
            BaseManagerPool.getInstance().scriptManager.loadScripts();
        } catch (Exception e) {
            log.error("Script init error", e);
            System.exit(0);
        }

        log.info("管理器初始数据结束");
        group = new ThreadGroup("group");
        // 初始化线程
        log.info("初始化线程开始");

        // 计时线程
        timerThread = new TimerThread("计时", HEART);
        timerThread.start();

        // 命令执行器
        CommandExecutor executor = new CommandExecutor();

        // 普通地图线程组
        cityServerGroup = new GameMapServerGroup(group, "city", executor, timerThread, MAX_THREAD, MAX_THREAD, 1);

        // 登录线程，执行登录登出功能，计时线程实现延时登出
        loginGroup = new OrderCommandServerGroup(group, "Login", executor, timerThread, 10);

        // 服务器线程，执行服务器级逻辑
        serverThread = new ServerThread(group, "Server", executor, timerThread);
        serverThread.start();
        serverThread.addTimerEvent(new ServerHeartTimer());

        /** 服务器统计线程 */
        statisticsThread = new ServerThread(group, "Statistics", executor, timerThread);
        statisticsThread.start();
        statisticsThread.addTimerEvent(new MessageStatisticsTimer());

        // 异步处理线程
        asynchronousGroup = Executors.newFixedThreadPool(16, new BasicThreadFactory.Builder().namingPattern("Asynchronous-%d").build());

        log.info("初始化线程结束");

        try {
            serverScript.init();
        } catch (Exception e) {
            log.error("ManagerPool init error", e);
            System.exit(0);
        }

        // 启动端口开始监听
        super.run();

        // http启动
        String httpPort = ConfigPropertiesUtil.getConfigProperty("server.http.port");
        if (!StringUtils.isEmpty(httpPort)) {
            new Thread(new HttpServer(Integer.parseInt(httpPort)), "HttpServer").start();
        }

        log.info("服务器启动成功! 启动耗时:{}", System.currentTimeMillis() - starttime);

    }

    /**
     * 增加登录命令
     */
    public boolean addLoginCommand(long userId, IMessage message) throws UnsupportedKeyTypeException {
        return loginGroup.addCommand(userId, message);
    }

    /**
     * 增加登录定时事件
     */
    public void addLoginEvent(long userId, TimerEvent event) throws UnsupportedKeyTypeException {
        loginGroup.addTimerEvent(userId, event);
    }

    /**
     * 增加服务器命令
     */
    public boolean addServerCommand(IMessage message) {
        return serverThread.addCommand(message);
    }

    /**
     * 增加服务器命令
     */
    public void addPlayerCommand(Player player, IMessage message) {
        serverScript.addPlayerCommand(player, message);
    }

//    /**
//     * 重新加载逻辑jar
//     */
//    public void reloadLogic() {
//        try {
//            BaseManagerPool.getInstance().scriptManager.loadJar();
//        } catch (Exception e) {
//            log.error(e.getMessage(), e);
//        }
//    }

    public MysqlServer getMysqlServer() {
        return mysqlServer;
    }

    public TimerThread getTimerThread() {
        return timerThread;
    }

    public void setServerScript(IServerScript serverScript) {
        this.serverScript = serverScript;
    }

    public IServerScript getServerScript() {
        return serverScript;
    }

    public ChannelGroup getChannelGroup() {
        return channelGroup;
    }

    public ThreadGroup getGroup() {
        return group;
    }

    public int getGameVersion() {
        return GAME_VERSION;
    }

//    public SaveServer getSaveServer() {
//        return saveServer;
//    }

    public GameMapServerGroup getCityServerGroup() {
        return cityServerGroup;
    }

    public ExecutorService getAsynchronousThreadGroup() {
        return asynchronousGroup;
    }

//    public SavePlayerServer getSavePlayerServer() {
//        return savePlayerServer;
//    }
//
//    public SaveServer getSaveGoldServer() {
//        return saveGoldServer;
//    }

    @Override
    public void channelActive(ChannelHandlerContext ctx) throws Exception {
        serverScript.channelActive(ctx);
        if (MAX_CONNECT < channelGroup.size()) {
            MAX_CONNECT = channelGroup.size();
        }
    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) throws Exception {
        serverScript.channelInactive(ctx);
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
        serverScript.exceptionCaught(ctx, cause);
    }

    private class CommandServerImpl extends CommandServer {

        public CommandServerImpl() {
            super();
        }

        @Override
        public IMessage getMessage(int id) {
            return serverScript.getMessage(id);
        }

        @Override
        public void doCommand(IMessage message) {
            serverScript.doCommand(message);
        }

        @Override
        protected void stop() {
            serverScript.stop();
        }
    }
}
