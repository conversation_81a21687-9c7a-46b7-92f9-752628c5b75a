package xddq.server.impl;

import xddq.map.structs.GameMap;
import xddq.server.ICommandExecutor;
import xddq.server.filter.HandlerFilter;
import xddq.server.map.IMap;
import xddq.server.thread.MapServer;
import xddq.server.thread.TimerThread;
import xddq.timer.message.PlayerHeartTimer;

public class LogicServer extends MapServer<Long> {

    public LogicServer(ThreadGroup group, String name, ICommandExecutor executor, TimerThread timerThread) {
        super(group, name, executor, timerThread);
    }

    @Override
    protected void init() {
        this.addBeforeCommandFitler(new HandlerFilter());
    }

    @Override
    protected void addMapTimerEvent(IMap<Long> map) {
			// 地图心跳
			this.addTimerEvent(new PlayerHeartTimer((GameMap) map));
//			// 加上移除buff
//			this.addTimerEvent(new PlayerBuffRemoveTimer((Map) map));
//			// 加上排行榜刷新
//			this.addTimerEvent(new RankUpdateTimer((Map) map));
    }

}
