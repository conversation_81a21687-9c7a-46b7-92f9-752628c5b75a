package xddq.server;

import io.netty.channel.ChannelHandlerContext;
import xddq.message.ICommand;
import xddq.message.IMessage;
import xddq.player.structs.Player;

public interface IServerScript {

    /**
     * 初始化
     */
    void init() throws Exception;

    /**
     * 获得注册的消息
     */
    IMessage getMessage(int id);

    /**
     * 消息处理
     */
    void doCommand(IMessage message);

    /**
     * 连接异常
     */
    void exceptionCaught(ChannelHandlerContext ctx, Throwable cause);

    /**
     * 连接创建
     */
    void channelActive(ChannelHandlerContext ctx);

    /**
     * 连接关闭
     */
    void channelInactive(ChannelHandlerContext ctx);

    /**
     * 服务器停止
     */
    void stop();

    /**
     * 执行命令
     */
    void action(ICommand command);

    /**
     * 增加玩家指令
     */
    void addPlayerCommand(Player player, ICommand command);

}
