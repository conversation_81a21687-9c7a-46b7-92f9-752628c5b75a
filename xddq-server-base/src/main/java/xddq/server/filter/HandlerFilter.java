package xddq.server.filter;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import xddq.manager.BaseManagerPool;
import xddq.message.ICommand;
import xddq.script.ScriptEnum;
import xddq.server.ICommandFilter;
import xddq.server.script.IServerHandlerScript;

public class <PERSON>lerFilter implements ICommandFilter {

    private static final Logger log = LoggerFactory.getLogger(HandlerFilter.class);

    public HandlerFilter() {
    }

    @Override
    public boolean filter(ICommand command) {
        IServerHandlerScript script = (IServerHandlerScript) BaseManagerPool.getInstance().scriptManager
                .getScript(ScriptEnum.HANDLER_ACTION);
        if (script != null) {
            try {
                boolean result = script.check(command);
                if (!result) {
                    return false;
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
        return true;
    }

}
