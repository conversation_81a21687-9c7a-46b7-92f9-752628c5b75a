package xddq.talent.structs;

import xddq.structs.GameObject;

import java.util.concurrent.ConcurrentHashMap;

public class Talent extends GameObject {

    private int type;
    private int level;
    private int quality;
    private ConcurrentHashMap<Integer, Long> attributes = new ConcurrentHashMap<>();
    private int skillId;

    public int getType() {
        return type;
    }

    public Talent setType(int type) {
        this.type = type;
        return this;
    }

    public int getLevel() {
        return level;
    }

    public Talent setLevel(int level) {
        this.level = level;
        return this;
    }

    public int getQuality() {
        return quality;
    }

    public Talent setQuality(int quality) {
        this.quality = quality;
        return this;
    }

    public ConcurrentHashMap<Integer, Long> getAttributes() {
        return attributes;
    }

    public Talent setAttributes(ConcurrentHashMap<Integer, Long> attributes) {
        this.attributes = attributes;
        return this;
    }

    public int getSkillId() {
        return skillId;
    }

    public Talent setSkillId(int skillId) {
        this.skillId = skillId;
        return this;
    }
}
