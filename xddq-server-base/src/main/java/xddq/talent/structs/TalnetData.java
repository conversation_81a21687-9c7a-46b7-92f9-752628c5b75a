package xddq.talent.structs;

import java.util.Vector;
import java.util.concurrent.ConcurrentHashMap;

public class TalnetData {

    private int talentCreateLevel = 1;
    private int exp;
    private int readBookTimes;

    private ConcurrentHashMap<Integer, Talent> talents = new ConcurrentHashMap<>();
    private Vector<Talent> unDealTalent = new Vector<>();

    public int getTalentCreateLevel() {
        return talentCreateLevel;
    }

    public TalnetData setTalentCreateLevel(int talentCreateLevel) {
        this.talentCreateLevel = talentCreateLevel;
        return this;
    }

    public int getExp() {
        return exp;
    }

    public TalnetData setExp(int exp) {
        this.exp = exp;
        return this;
    }

    public int getReadBookTimes() {
        return readBookTimes;
    }

    public TalnetData setReadBookTimes(int readBookTimes) {
        this.readBookTimes = readBookTimes;
        return this;
    }

    public ConcurrentHashMap<Integer, Talent> getTalents() {
        return talents;
    }

    public TalnetData setTalents(ConcurrentHashMap<Integer, Talent> talents) {
        this.talents = talents;
        return this;
    }

    public Vector<Talent> getUnDealTalent() {
        return unDealTalent;
    }

    public TalnetData setUnDealTalent(Vector<Talent> unDealTalent) {
        this.unDealTalent = unDealTalent;
        return this;
    }
}
