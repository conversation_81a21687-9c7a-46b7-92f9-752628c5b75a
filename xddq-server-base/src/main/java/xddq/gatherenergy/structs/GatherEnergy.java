package xddq.gatherenergy.structs;

import java.util.Vector;

public class GatherEnergy {
    //    optional int32 state = 1;
    //    optional int32 openNum = 2;
    //    optional int32 attendNum = 3;
    //    optional bool red = 4;
    //    optional bool hadLike = 5;
    //    repeated int64 enemyList = 6;
    //    optional int32 getTimes = 7;
    private int state;
    private int openNum;
    private int attendNum;
    private boolean red;
    private boolean hadLike;
    private Vector<Long> enemyList = new Vector<>();
    private int getTimes;

    public int getState() {
        return state;
    }

    public GatherEnergy setState(int state) {
        this.state = state;
        return this;
    }

    public int getOpenNum() {
        return openNum;
    }

    public GatherEnergy setOpenNum(int openNum) {
        this.openNum = openNum;
        return this;
    }

    public int getAttendNum() {
        return attendNum;
    }

    public GatherEnergy setAttendNum(int attendNum) {
        this.attendNum = attendNum;
        return this;
    }

    public boolean isRed() {
        return red;
    }

    public GatherEnergy setRed(boolean red) {
        this.red = red;
        return this;
    }

    public boolean isHadLike() {
        return hadLike;
    }

    public GatherEnergy setHadLike(boolean hadLike) {
        this.hadLike = hadLike;
        return this;
    }

    public Vector<Long> getEnemyList() {
        return enemyList;
    }

    public GatherEnergy setEnemyList(Vector<Long> enemyList) {
        this.enemyList = enemyList;
        return this;
    }

    public int getGetTimes() {
        return getTimes;
    }

    public GatherEnergy setGetTimes(int getTimes) {
        this.getTimes = getTimes;
        return this;
    }
}
