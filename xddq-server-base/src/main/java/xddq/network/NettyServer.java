package xddq.network;

import io.netty.bootstrap.ServerBootstrap;
import io.netty.buffer.ByteBuf;
import io.netty.channel.*;
import io.netty.channel.group.ChannelGroup;
import io.netty.channel.group.DefaultChannelGroup;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import io.netty.util.ReferenceCountUtil;
import io.netty.util.concurrent.GlobalEventExecutor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import xddq.message.IMessage;
import xddq.network.channel.ServerChannelGroup;
import xddq.network.coder.ProtocolDecoder;
import xddq.network.handler.ProtocolHandler;
import xddq.network.handler.SessionPacketLimiter;
import xddq.server.ICommandServer;

/**
 * 服务器
 */
public abstract class NettyServer implements INettyServer, Runnable {

    protected static Logger log = LoggerFactory.getLogger(NettyServer.class);

    // 服务器数据监听端口
    private int port;
    // 命令执行服务器
    protected ICommandServer commandServer;

    protected ChannelGroup channelGroup = new DefaultChannelGroup("client-channel-group", GlobalEventExecutor.INSTANCE);

    /**
     * 服务器初始化
     */
    protected NettyServer(int port) {
        this.port = port;
    }

    public void run() {
        if (this.port == 0) {
            throw new RuntimeException("端口号未配置");
        }
        // Netty连接线程启动
        new Thread(new ConnectServer(), "ConnectServer").start();
    }

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
        ByteBuf buf = (ByteBuf) msg;
        try {
            // 获取消息id
            int id = buf.readInt();
            // 用戶id
            long accountId = buf.readLong();
            // 获取消息体
            IMessage message = commandServer.getMessage(id);
            // 获取消息内容
            message.read(buf);

            message.setCtx(ctx);
            message.setAccountId(accountId);
            // 执行命令
            commandServer.doCommand(message);
        } finally {
            ReferenceCountUtil.release(buf);
        }
    }

    /**
     * Mina数据连接线程
     *
     * <AUTHOR>
     */
    private class ConnectServer implements Runnable {
        // 日志
        private Logger log = LoggerFactory.getLogger(ConnectServer.class);

        public ConnectServer() {
        }

        public void run() {
            EventLoopGroup bossGroup = new NioEventLoopGroup(1);
            EventLoopGroup workerGroup = new NioEventLoopGroup();

            // ServerBootstrap负责建立服务端
            // 你可以直接使用Channel去建立服务端，但是大多数情况下你无需做这种乏味的事情
            ServerBootstrap b = new ServerBootstrap();
            b.group(bossGroup, workerGroup)
                    // 指定使用NioServerSocketChannel产生一个Channel用来接收连接
                    .channel(NioServerSocketChannel.class)
                    // ChannelInitializer用于配置一个新的Channel
                    // 用于向你的Channel当中添加ChannelInboundHandler的实现
                    .childHandler(new ChannelInitializer<SocketChannel>() {
                        public void initChannel(SocketChannel ch) throws Exception {
                            // ChannelPipeline用于存放管理ChannelHandel
                            // ChannelHandler用于处理请求响应的业务逻辑相关代码
                            ch.pipeline().addLast(new SessionPacketLimiter());
                            ch.pipeline().addLast(new ServerChannelGroup(channelGroup));
                            ch.pipeline().addLast(new ProtocolDecoder());
                            ch.pipeline().addLast(new ProtocolHandler(NettyServer.this));
                        }
                    })
                    // 对Channel进行一些配置
                    // 注意以下是socket的标准参数
                    // BACKLOG用于构造服务端套接字ServerSocket对象，标识当服务器请求处理线程全满时，用于临时存放已完成三次握手的请求的队列的最大长度。如果未设置或所设置的值小于1，Java将使用默认值50。
                    // Option是为了NioServerSocketChannel设置的，用来接收传入连接的
                    .option(ChannelOption.SO_BACKLOG, 128)
                    // 是否启用心跳保活机制。在双方TCP套接字建立连接后（即都进入ESTABLISHED状态）并且在两个小时左右上层没有任何数据传输的情况下，这套机制才会被激活。
                    // childOption是用来给父级ServerChannel之下的Channels设置参数的
                    .childOption(ChannelOption.SO_KEEPALIVE, false);
            try {
                // Bind and start to accept incoming connections.
                ChannelFuture f = b.bind(port).sync();
                if (f.isSuccess()) {
                    log.info("Netty Server Start At Port " + port);
                }
                // Wait until the server socket is closed.
                // In this example, this does not happen, but you can do that to gracefully
                // shut down your server.
                // sync()会同步等待连接操作结果，用户线程将在此wait()，直到连接操作完成之后，线程被notify(),用户代码继续执行
                // closeFuture()当Channel关闭时返回一个ChannelFuture,用于链路检测
                f.channel().closeFuture().sync();
            } catch (InterruptedException e) {
                log.error(e.getMessage(), e);
            } finally {
                bossGroup.shutdownGracefully();
                workerGroup.shutdownGracefully();
            }
        }
    }
}

