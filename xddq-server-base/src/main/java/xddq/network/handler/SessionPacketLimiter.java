package xddq.network.handler;

import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import io.netty.util.AttributeKey;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import xddq.consts.ConstantDefinition;
import xddq.consts.Global;
import xddq.network.NettyServer;
import xddq.pb.Xddq;
import xddq.pb.message.RspLoginMsg_1Impl;
import xddq.player.structs.Player;
import xddq.utils.MessageUtil;
import xddq.utils.TimeUtil;

import java.util.concurrent.atomic.AtomicInteger;

public class SessionPacketLimiter extends ChannelInboundHandlerAdapter {
    protected static Logger log = LoggerFactory.getLogger(NettyServer.class);

    // Channel属性键，用于存储计数器和时间戳
    private static final AttributeKey<AtomicInteger> PACKET_COUNTER = AttributeKey.valueOf("packetCounter");
    private static final AttributeKey<Long> WINDOW_START_TIME = AttributeKey.valueOf("windowStartTime");

    // 配置参数 -> 改成动态调控
//    private final int maxPacketsPerWindow; // 窗口期内允许的最大数据包数
//    private final int windowSeconds;       // 窗口期大小(秒)

    /**
     * 创建一个会话包速率限制器
     * @param maxPacketsPerWindow 每个窗口期允许的最大数据包数
     * @param windowSeconds 窗口期大小(秒)，推荐3-5秒
     */
//    public SessionPacketLimiter(int maxPacketsPerWindow, int windowSeconds) {
//        this.maxPacketsPerWindow = maxPacketsPerWindow;
//        this.windowSeconds = windowSeconds;
//    }

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
        // 获取通道的数据包计数器
        AtomicInteger counter = ctx.channel().attr(PACKET_COUNTER).get();
        if (counter == null) {
            counter = new AtomicInteger(0);
            ctx.channel().attr(PACKET_COUNTER).set(counter);
        }

        // 获取窗口开始时间
        Long windowStart = ctx.channel().attr(WINDOW_START_TIME).get();
        long currentTime = System.currentTimeMillis();

        if (windowStart == null) {
            // 首次请求，初始化窗口开始时间
            ctx.channel().attr(WINDOW_START_TIME).set(currentTime);
            counter.set(1);
        } else if (currentTime - windowStart > Global.MAX_PKG_TIME * 1000) {
            // 当前窗口已结束，开始新窗口
            ctx.channel().attr(WINDOW_START_TIME).set(currentTime);
            counter.set(1);
        } else {
            // 在当前窗口内
            int count = counter.incrementAndGet();

            if (count > Global.MAX_PKG_CNT + 10) {
                log.error("packetCnt=10=" + count);
                // 封号然后踢出
                Player player = ctx.channel().attr(ConstantDefinition.PLAYER).get();
                xddq.pb.RspLoginMsg.Builder builder = xddq.pb.RspLoginMsg.newBuilder();
                builder.setRet(48);
                MessageUtil.tellPlayer(player, new RspLoginMsg_1Impl(builder));
                //player.setForbidTime(TimeUtil.getCurrentTimeMillis() + TimeUtil.MINUTE*10);

                ctx.close();

                 return;

            } else if (count > Global.MAX_PKG_CNT + 5) {
                log.error("packetCnt-5-" + count);
                // 踢出
                ctx.close();

                return;
            } else if (count > Global.MAX_PKG_CNT) {
                // 丢弃数据包
                log.error("packetCnt-0-" + count);

                return;
            }
        }

        // 传递消息到下一个处理器
        ctx.fireChannelRead(msg);
    }

    @Override
    public void channelActive(ChannelHandlerContext ctx) throws Exception {
        // 初始化计数器和时间戳
        ctx.channel().attr(PACKET_COUNTER).set(new AtomicInteger(0));
        ctx.channel().attr(WINDOW_START_TIME).set(System.currentTimeMillis());
        super.channelActive(ctx);
    }
}