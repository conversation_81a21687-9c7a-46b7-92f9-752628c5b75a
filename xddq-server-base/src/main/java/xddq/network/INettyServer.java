package xddq.network;

import io.netty.channel.ChannelHandlerContext;

public interface INettyServer {
    /**
     * Calls {@link ChannelHandlerContext#fireChannelActive()} to forward
     * to the next {@link ChannelInboundHandler} in the {@link ChannelPipeline}.
     */
    void channelActive(ChannelHandlerContext ctx) throws Exception;

    /**
     * Calls {@link ChannelHandlerContext#fireChannelInactive()} to forward
     * to the next {@link ChannelInboundHandler} in the {@link ChannelPipeline}.
     */
    void channelInactive(ChannelHandlerContext ctx) throws Exception;

    /**
     * Calls {@link ChannelHandlerContext#fireExceptionCaught(Throwable)} to forward
     * to the next {@link ChannelHandler} in the {@link ChannelPipeline}.
     */

    void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception;

    /**
     * Calls ChannelHandlerContext.fireChannelRead(Object) to forward to the next
     * ChannelInboundHandler in the ChannelPipeline. Sub-classes may override this
     * method to change behavior.
     */
    void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception;
}
