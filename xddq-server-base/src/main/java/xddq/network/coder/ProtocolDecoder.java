package xddq.network.coder;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.LengthFieldBasedFrameDecoder;
import io.netty.handler.codec.TooLongFrameException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import xddq.consts.ConstantDefinition;
import xddq.consts.Global;
import xddq.pb.Xddq;
import xddq.pb.message.RspLoginMsg_1Impl;
import xddq.player.structs.Player;
import xddq.utils.MessageUtil;
import xddq.utils.SessionUtil;
import xddq.utils.TimeUtil;

public class ProtocolDecoder extends LengthFieldBasedFrameDecoder {

    protected final static Logger log = LoggerFactory.getLogger(ProtocolDecoder.class);

    protected final static Logger messagelog = LoggerFactory.getLogger("MessageLog");

    private static final int maxFrameLength = 10 * 1024 * 1024;

    private static final int lengthFieldOffset = 2;

    private static final int lengthFieldLength = 4;

    private static final int lengthAdjustment = -6;

    private static final int initialBytesToStrip = 6;

//    public static int BETWEEN = 5000;
//
//    public static int MAX_COUNT = 20;
    // 计时开始时间
    private long startTime = 0;
    // 单位时间发送量
    private int count = 0;


    public ProtocolDecoder() {
        /**
         * BEFORE DECODE (20 bytes)                       AFTER DECODE (12 bytes)
         * +------------+-----------+----------------+      +----------------+
         * | Length     | Order     | Actual Content |----->| Actual Content |
         * | 0x00000010 | 0x00000001| "HELLO, WORLD" |      | "HELLO, WORLD" |
         * +------------+-----------+----------------+      +----------------+
         * 所以
         * lengthFieldOffset = 0
         * lengthFieldLength = 4
         * lengthAdjustment = 0
         * initialBytesToStrip = 8
         */
        super(maxFrameLength, lengthFieldOffset, lengthFieldLength, lengthAdjustment, initialBytesToStrip, true);
    }

    @Override
    protected Object decode(ChannelHandlerContext ctx, ByteBuf in) throws Exception {
//        if (System.currentTimeMillis() / (Global.MAX_PKG_TIME*1000) != startTime) {
//            if (count > Global.MAX_PKG_CNT)
//                messagelog.info("{} --> sendmsg:{}", ctx, count);
//            startTime = System.currentTimeMillis() / (Global.MAX_PKG_TIME*1000);
//            count = 0;
//        }
//
//        count++;
//
//        if (count > Global.MAX_PKG_CNT) {
//            //messagelog.info("{} --> sendmsg:{}-->close", ctx, count);
//            in.readerIndex(in.writerIndex());
//            log.error("pkgCnt0=" + count);
//			//SessionUtil.closeSession(ctx, "发送消息数量过多(" + count + ")");
//            return null;
//        } else if (count > Global.MAX_PKG_CNT + 5) {
//            in.readerIndex(in.writerIndex());
//            log.error("pkgCnt5=" + count);
//            SessionUtil.closeSession(ctx, "发送消息数量过多(" + count + ")");
//            return null;
//        } else if (count > Global.MAX_PKG_CNT + 10) {
//            in.readerIndex(in.writerIndex());
//            log.error("pkgCnt10=" + count);
//            if (ctx.channel() != null && ctx.channel().hasAttr(ConstantDefinition.PLAYER)) {
//                Player player = ctx.channel().attr(ConstantDefinition.PLAYER).get();
//                if (player != null) {
//                    Xddq.RspLoginMsg.Builder builder = Xddq.RspLoginMsg.newBuilder();
//                    builder.setRet(48);
//                    MessageUtil.tellPlayer(player, new RspLoginMsg_1Impl(builder));
//                    player.setForbidTime(TimeUtil.getCurrentTimeMillis() + TimeUtil.MINUTE * 10);
//                }
//
//            }
//            SessionUtil.closeSession(ctx, "发送消息数量过多(" + count + ")");
//            return null;
//        }

        if (in.readableBytes() < initialBytesToStrip) {
            return null;
        }

//        StringBuilder dump = new StringBuilder();
//        // 在dump后面附加buffer内容的多行美化过的十六进制表示的字符串
//        ByteBufUtil.appendPrettyHexDump(dump, in);


        Object decode = null;
        try{
            decode = super.decode(ctx, in);
        }catch (TooLongFrameException e) {
            SessionUtil.closeSession(ctx, "发送消息出错过长");
            return null;
        }
        if(decode == null) {
            return null;
        }

        return decode;
    }
}
