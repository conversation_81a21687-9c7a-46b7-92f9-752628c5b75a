package xddq.utils;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;

public class AESUtil {

    private static final String encoding = "UTF-8";

    private static final String IV_STRING = "xddq.game.com";

    /**
     * 解密
     */
    // 解密
    public static String decrypt(String content, String key) throws UnsupportedEncodingException, NoSuchAlgorithmException, NoSuchPaddingException, InvalidKeyException, InvalidAlgorithmParameterException, IllegalBlockSizeException, BadPaddingException {
        //URL解码
        content = URLDecoder.decode(content,encoding);
        // base64 解码
        byte[] encryptedBytes = Base64.getDecoder().decode(content);

        byte[] keybytes = key.getBytes(encoding);
        byte[] raw = new byte[16];
        System.arraycopy(keybytes, 0, raw, 0, Math.min(keybytes.length, raw.length));
        SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
        byte[] ivbytes = IV_STRING.getBytes(encoding);
        byte[] ivParameter = new byte[16];
        System.arraycopy(ivbytes, 0, ivParameter, 0, Math.min(ivbytes.length, ivParameter.length));
        IvParameterSpec iv = new IvParameterSpec(ivParameter);
        cipher.init(Cipher.DECRYPT_MODE, skeySpec, iv);
        byte[] original = cipher.doFinal(encryptedBytes);
        String originalString = new String(original, encoding);
        return originalString;
    }

    /**
     * 加密
     */
    // 解密
    public static String encrypt(String content, String key) throws UnsupportedEncodingException, NoSuchAlgorithmException, NoSuchPaddingException, InvalidKeyException, InvalidAlgorithmParameterException, IllegalBlockSizeException, BadPaddingException {
        byte[] byteContent = content.getBytes(encoding);
        // 注意，为了能与 iOS 统一
        // 这里的 key 不可以使用 KeyGenerator、SecureRandom、SecretKey 生成
        byte[] keybytes = key.getBytes(encoding);
        byte[] raw = new byte[16];
        System.arraycopy(keybytes, 0, raw, 0, Math.min(keybytes.length, raw.length));
        SecretKeySpec secretKeySpec = new SecretKeySpec(raw, "AES");
        byte[] ivbytes = IV_STRING.getBytes(encoding);
        byte[] ivParameter = new byte[16];
        System.arraycopy(ivbytes, 0, ivParameter, 0, Math.min(ivbytes.length, ivParameter.length));
        IvParameterSpec ivParameterSpec = new IvParameterSpec(ivParameter);
        // 指定加密的算法、工作模式和填充方式
        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
        cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec, ivParameterSpec);
        byte[] encryptedBytes = cipher.doFinal(byteContent);
        // 同样对加密后数据进行 base64 编码
        String base64 = Base64.getEncoder().encodeToString(encryptedBytes);
        //进行url编码 去掉= ? &
        return URLEncoder.encode(base64, encoding);
    }

}
