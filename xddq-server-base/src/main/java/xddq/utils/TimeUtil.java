package xddq.utils;

import com.cronutils.model.Cron;
import com.cronutils.model.CronType;
import com.cronutils.model.definition.CronDefinitionBuilder;
import com.cronutils.model.time.ExecutionTime;
import com.cronutils.parser.CronParser;
import org.apache.commons.lang3.StringUtils;
import org.quartz.CronExpression;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import xddq.server.impl.GameServer;

import java.text.SimpleDateFormat;
import java.time.*;
import java.util.Calendar;
import java.util.Date;

public class TimeUtil {
    public static final long SECOND = 1000;
    public static final long MINUTE = 60 * SECOND;
    public static final long HOUR = 60 * MINUTE;
    public static final long DAY = 24 * HOUR;
    public static final long WEEK = 7 * DAY;
    public static final long MONTH = 30 * DAY;
    /**
     * 秒数
     */
    public static final int MINUTE_SECOND = 60;
    public static final int HOUR_SECOND = 60 * MINUTE_SECOND;
    public static final int DAY_SECOND = 24 * HOUR_SECOND;

    private static Logger log = LoggerFactory.getLogger(TimeUtil.class);
    private static Clock clock = Clock.systemDefaultZone();

    public static final CronParser parser = new CronParser(CronDefinitionBuilder.instanceDefinitionFor(CronType.QUARTZ));

    /**
     * 修改日期时间
     */
    public static void useFixedClock(long time) {
        clock = Clock.offset(Clock.systemDefaultZone(), Duration.ofMillis(time - Clock.systemDefaultZone().millis()));
    }

    public static ZoneId getTimeZone() {
        return Clock.systemDefaultZone().getZone();
    }

    /**
     * 使用默认系统时间
     */
    public static void useSystemDefaultZoneClock() {
        clock = Clock.systemDefaultZone();
    }

    /**
     * 获取当前毫秒时刻
     */
    public static long getCurrentTimeMillis() {
        return clock.millis();
    }

    public static long getCurrentTimeSecond() {
        return clock.millis() / 1000;
    }

    public static String getCurrentDay() {
        return new SimpleDateFormat("yyyyMMdd").format(new Date(getCurrentTimeMillis()));
    }

    public static long getNextDateTime(long time) {
        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(time);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        cal.add(Calendar.DAY_OF_YEAR, 1);
        return cal.getTimeInMillis();
    }

    public static long getNextNDateTime(long time, int n) {
        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(time);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        cal.add(Calendar.DAY_OF_YEAR, n);
        return cal.getTimeInMillis();
    }

    public static long getNextWeekTime(long time) {
        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(time);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        cal.add(Calendar.WEEK_OF_YEAR, 1);
        cal.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
        return cal.getTimeInMillis();
    }

    public static int getServerOpenTime(long time) {
        Calendar cal0 = Calendar.getInstance();
        cal0.setTime(GameServer.SERVER_OPEN_TIME);
        cal0.set(Calendar.HOUR_OF_DAY, 0);
        cal0.set(Calendar.MINUTE, 0);
        cal0.set(Calendar.SECOND, 0);
        cal0.set(Calendar.MILLISECOND, 0);

        Calendar cal1 = Calendar.getInstance();
        cal1.setTimeInMillis(time);
        cal1.set(Calendar.HOUR_OF_DAY, 0);
        cal1.set(Calendar.MINUTE, 0);
        cal1.set(Calendar.SECOND, 0);
        cal1.set(Calendar.MILLISECOND, 0);

        return (int) ((cal1.getTimeInMillis() - cal0.getTimeInMillis()) / 24 / 60 / 60 / 1000) + 1;
    }

    /**
     * 获得下一次触发时间，单位毫秒
     */
    public static long getNextTriggerTime(CronExpression time) {
        Date nextDate = time.getNextValidTimeAfter(new Date(getCurrentTimeMillis()));
        if (nextDate == null) {
            nextDate = time.getNextValidTimeAfter(new Date(0));
            if (nextDate == null) {
                return 0;
            }
        }
        return nextDate.getTime();
    }

    /**
     * 获得下一次触发时间，单位毫秒
     */
    public static long getNextTriggerTime(CronExpression crontime, long time) {
        Date nextDate = crontime.getNextValidTimeAfter(new Date(time));
        if (nextDate == null) {
            return 0;
        }
        return nextDate.getTime();
    }

    /**
     * 获得下一次触发时间，单位毫秒
     */
    public static long getNextRefreshTime(String time) {
        long nextStartTime = 0;
        if (StringUtils.isEmpty(time)) {
            return -1;
        }
        try {
            nextStartTime = TimeUtil.getNextTriggerTime(new CronExpression(time));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return nextStartTime;
    }

    /**
     * 获得下一次触发时间，单位毫秒
     */
    public static long getNextRefreshTime(String time, long startTime) {
        long nextStartTime = 0;
        if (StringUtils.isEmpty(time)) {
            return -1;
        }
        try {
            nextStartTime = TimeUtil.getNextTriggerTime(new CronExpression(time), startTime);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return nextStartTime;
    }

    /**
     * 获得上一次触发时间，单位毫秒
     */
    public static long getPreRefreshTime(String time) {
        return getRelativePreRefreshTime(time, TimeUtil.getCurrentTimeMillis());
    }

    /**
     * 获得上一次触发时间，单位毫秒
     */
    public static long getPreRefreshTime(String time, long relativeTime) {
        return getRelativePreRefreshTime(time, relativeTime);
    }

    /**
     * 获得上一次触发时间，单位毫秒
     */
    public static long getRelativePreRefreshTime(String time, long relativeTime) {
        Cron cron = parser.parse(time);
        ExecutionTime executionTime = ExecutionTime.forCron(cron);
        // 对应日期
        Date date = new Date(relativeTime);
        ZonedDateTime dateTime = date.toInstant().atZone(ZoneId.systemDefault());
        // 上一次执行时间
        ZonedDateTime lastTime = executionTime.lastExecution(dateTime).get();
        return Date.from(lastTime.toInstant()).getTime();
    }

    /**
     * 返回相差天数，当天算0天
     */
    public static int between(long startTime, long endTime) {
        Calendar cal0 = Calendar.getInstance();
        cal0.setTimeInMillis(startTime);
        cal0.set(Calendar.HOUR_OF_DAY, 0);
        cal0.set(Calendar.MINUTE, 0);
        cal0.set(Calendar.SECOND, 0);
        cal0.set(Calendar.MILLISECOND, 0);

        Calendar cal1 = Calendar.getInstance();
        cal1.setTimeInMillis(endTime);
        cal1.set(Calendar.HOUR_OF_DAY, 0);
        cal1.set(Calendar.MINUTE, 0);
        cal1.set(Calendar.SECOND, 0);
        cal1.set(Calendar.MILLISECOND, 0);
        long diffMillis = Math.abs(cal1.getTimeInMillis() - cal0.getTimeInMillis());
        if (diffMillis == 0)
            return 0;

        return (int) (diffMillis / (60 * 60 * 24 * 1000L));
    }

    public static long dateStringToMillis(String dateStr) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
            sdf.setLenient(false); // 严格校验
            return sdf.parse(dateStr).getTime();
        } catch (Exception e) {
            e.printStackTrace();
            return 0L;
        }
    }

    public static boolean isSameDay(long timestamp1, long timestamp2) {
        if (timestamp1 == 0L || timestamp2 == 0L) {
            return false;
        }
        ZoneId zone = ZoneId.systemDefault();
        LocalDate date1 = Instant.ofEpochMilli(timestamp1).atZone(zone).toLocalDate();
        LocalDate date2 = Instant.ofEpochMilli(timestamp2).atZone(zone).toLocalDate();
        return date1.equals(date2);
    }

    public static void main(String[] args) {
//        long l1 = 1750155774000L; // 2025-06-17 18:22:54
//        long l2 = 1750094574000L; // 2025-06-17 01:22:54
//
//        long l3 = 1750262401000L; // 2025-06-19 00:00:01
//        long l4 = 1750262399000L; // 2025-06-18 23:59:59
//
//        System.out.println(isSameDay(l1, l2));
//        System.out.println(isSameDay(l3, l4));
    }
}
