package xddq.utils;

import io.netty.channel.ChannelHandlerContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class SessionUtil {

    private static final Logger closelog = LoggerFactory.getLogger("SESSIONCLOSE");

    public static void closeSession(ChannelHandlerContext ctx, String reason) {
        if (reason != null) {
            closelog.error("{}-->close [because] {}", ctx, reason);
        }
        ctx.close();
    }
}
