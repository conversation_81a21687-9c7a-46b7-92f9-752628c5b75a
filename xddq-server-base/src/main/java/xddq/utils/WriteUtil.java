package xddq.utils;

import com.google.protobuf.Message;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;
import io.netty.buffer.UnpooledHeapByteBuf;
import io.netty.channel.Channel;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.http.websocketx.TextWebSocketFrame;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import xddq.message.IMessage;

public class WriteUtil {

    protected static Logger log = LoggerFactory.getLogger(WriteUtil.class);
    // 是否统计流量
    public static boolean STATISTIC = false;

    /**
     * 发送
     */
    public static void write(ChannelHandlerContext ctx, long accountId, IMessage message) throws Exception {
        writeNoDelay(ctx.channel(), accountId, message);
    }

    public static void write(Channel channel, long accountId, IMessage message) throws Exception {
        writeNoDelay(channel, accountId, message);
    }

    public static void write(ChannelHandlerContext ctx, long accountId, int messageId, Message.Builder builder) throws Exception {
        writeNoDelay(ctx.channel(), accountId, messageId, builder);
    }

    public static void write(ChannelHandlerContext ctx, long accountId, byte[] bytes) throws Exception {
        write(ctx.channel(), accountId, bytes);
    }

    public static void write(Channel channel, long accountId, byte[] bytes) throws Exception {
        if (channel == null) {
            log.error("发送消息(writeNoDelay)session为空");
            return;
        }

        ByteBuf buf = ByteBufAllocator.DEFAULT.heapBuffer(bytes.length);
        buf.writeBytes(bytes);
        log.info(String.valueOf(buf.getLong(10)));
        buf.setLong(10, accountId);
        log.info(String.valueOf(accountId));
        log.info(String.valueOf(buf.getLong(10)));
        channel.writeAndFlush(buf);
    }

    /**
     * 直接发送
     */
    protected static void writeNoDelay(Channel channel, long accountId, IMessage message) throws Exception {
        if (channel == null) {
            log.error("发送消息(writeNoDelay)session为空, 消息为:" + message);
            return;
        }

        ByteBuf buf = castMessageToBuffer(accountId, message);

        if (STATISTIC)
            MessageStatisticUtil.statistic(message, buf, 1);

        channel.writeAndFlush(buf);
    }

    /**
     * 直接发送
     */
    protected static void writeNoDelay(Channel channel, long accountId, int messageId, Message.Builder builder) throws Exception {
        if (channel == null) {
            log.error("发送消息(writeNoDelay)session为空, 消息为:" + messageId);
            return;
        }
        ByteBuf buf = castMessageToBuffer(accountId, messageId, builder);

        if (STATISTIC)
            MessageStatisticUtil.statistic(messageId, buf, 1);

        channel.writeAndFlush(buf);
    }

    /**
     * 转换消息到bytes
     */
    protected static ByteBuf castMessageToBuffer(long accountId, IMessage message) throws Exception {
        // 计算头长度
        int headLength = 18;
        // 计算内容长度
        int length = message.length();

        ByteBuf buf = ByteBufAllocator.DEFAULT.heapBuffer(headLength + length);

        // 写入消息头
        buf.writeShort(0x71ab);
        buf.writeInt(headLength + length);
        buf.writeInt(message.getId());
        buf.writeLong(accountId);

        message.write(buf);

        return buf;
    }

    /**
     * 转换消息到bytes
     */
    protected static ByteBuf castMessageToBuffer(long accountId, int messageId, Message.Builder builder) throws Exception {
        // 计算头长度
        int headLength = 18;
        Message msg = builder.build();
        // 计算内容长度
        int length = msg.getSerializedSize();

        ByteBuf buf = ByteBufAllocator.DEFAULT.heapBuffer(headLength + length);

        // 写入消息头
        buf.writeShort(0x71ab);
        buf.writeInt(headLength + length);
        buf.writeInt(messageId);
        buf.writeLong(accountId);

        buf.writeBytes(msg.toByteArray());

        return buf;
    }
}
