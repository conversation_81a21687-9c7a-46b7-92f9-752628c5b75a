package xddq.utils;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import xddq.config.manager.ServerPropertiesManager;

public class ConfigPropertiesUtil {
    private static final Logger log = LoggerFactory.getLogger(ConfigPropertiesUtil.class);

    /**
     * 读取配置参数
     */
    public static String getConfigProperty(String property){
        return getConfigProperty(property, null);
    }

    /**
     * 读取配置参数
     */
    public static String getConfigProperty(String property, String defaultValue) {
        // 1. Get from System Property
        String propertyValue = System.getProperty(property);
        if (StringUtils.isEmpty(propertyValue)) {
            // 2. Get from OS environment variable, which could not contain dot and is
            // normally in UPPER case
            propertyValue = System.getenv(property);
        }
        if (StringUtils.isEmpty(propertyValue)) {
            // 3. Get from app.properties
            propertyValue = ServerPropertiesManager.getInstance().getProperty(property, defaultValue);
        }

        if (StringUtils.isEmpty(propertyValue)) {
            log.warn("Could not find server property, because it is not available in neither (1) JVM system property, (2) OS env variable nor (3) property '{}' from app.properties", property);
        } else {
            propertyValue = propertyValue.trim();
        }

        return propertyValue;
    }

    /**
     * 读取配置参数
     */
    public static boolean containsConfigProperty(String property) {
        // 1. Get from System Property
        String propertyValue = System.getProperty(property);
        if (!StringUtils.isEmpty(propertyValue)) {
            return true;
        }
        // 2. Get from OS environment variable, which could not contain dot and is
        // normally in UPPER case
        propertyValue = System.getenv(property);
        if (!StringUtils.isEmpty(propertyValue)) {
            return true;
        }
        // 3. Get from app.properties
        propertyValue = ServerPropertiesManager.getInstance().getProperty(property, null);
        return !StringUtils.isEmpty(propertyValue);
    }

    /**
     * 设置配置参数
     */
    public static void setConfigProperty(String property, String value) {
        if (StringUtils.isEmpty(property) || StringUtils.isEmpty(value)) {
            throw new IllegalArgumentException("property name or value is null");
        }
        // 1. Set to app.properties
        ServerPropertiesManager.getInstance().setProperty(property, value);
    }
}
