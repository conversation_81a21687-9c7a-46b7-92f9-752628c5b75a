package xddq.utils;

import java.util.concurrent.atomic.AtomicLong;

public class IdUtil {
    //计数
    private static final AtomicLong id = new AtomicLong();

    /**
     * 获得唯一id
     */
    public static long getId(int serverKey){
        return (((long)(serverKey & 0x001FFFFF)) << 43) | (((System.currentTimeMillis() / 1000) & 0x000000001FFFFFFFL) << 14) | (id.getAndIncrement() & 0x0000000000003FFFL);
    }
}
