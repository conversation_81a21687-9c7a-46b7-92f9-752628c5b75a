package xddq.utils;

import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.tuple.Pair;
import xddq.structs.Weight;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

public class RandomUtil {

    private static final String[] WARRIOR_PREFIX = {
            "华山", "武当", "峨眉", "少林", "丐帮", "明教", "昆仑", "崆峒",
            "青城", "点苍", "雪山", "天山", "华音", "凌霄", "凌烟", "天龙",
            "铁剑", "玄铁", "青锋", "宝刀", "长剑", "寒光", "赤焰", "碧血",
            "王", "李", "张", "赵", "钱", "孙", "周", "吴", "郑", "王富贵"
    };

    private static final String[] WARRIOR_MIDDLE = {
            "飞", "惊", "狂", "啸", "影", "月", "星", "风", "云", "霜", "雪",
            "寒", "烈", "破", "灭", "斩", "狂", "傲", "隐", "孤", "绝",
            "无始", "无敌", "小飞", "哥霸", "神", "圣", "仙", "魂", "魄"
    };

    private static final String[] WARRIOR_SUFFIX = {
            "剑", "掌", "刀", "心法", "秘籍", "诀", "功", "影", "流", "派",
            "客", "尊", "神", "皇", "圣", "仙", "魂", "魄", "侠", "哥", "无敌"
    };

    /**
     * 生成长度为 minLen 到 maxLen 的武侠风格昵称
     *
     * @param minLen 最小长度
     * @param maxLen 最大长度
     * @return 随机生成的武侠风格昵称
     */
    public static String generateEnhancedWarriorNickname(int minLen, int maxLen) {
        if (minLen > maxLen || minLen < 2 || maxLen > 6) {
            throw new IllegalArgumentException("参数无效，minLen 应大于等于 2，maxLen 应小于等于 6");
        }

        Random random = new Random();
        List<String> components = new ArrayList<>();

        // 根据长度动态构建昵称
        int length = random.nextInt(maxLen - minLen + 1) + minLen;

        // 添加前缀
        components.add(WARRIOR_PREFIX[random.nextInt(WARRIOR_PREFIX.length)]);

        // 添加中间部分
        for (int i = 0; i < length - 2; i++) {
            components.add(WARRIOR_MIDDLE[random.nextInt(WARRIOR_MIDDLE.length)]);
        }

        // 添加后缀
        components.add(WARRIOR_SUFFIX[random.nextInt(WARRIOR_SUFFIX.length)]);

        // 打乱顺序以增加多样性
        for (int i = components.size() - 1; i > 0; i--) {
            int j = random.nextInt(i + 1);
            String temp = components.get(i);
            components.set(i, components.get(j));
            components.set(j, temp);
        }

        // 拼接成最终昵称
        StringBuilder nickname = new StringBuilder();
        for (String component : components) {
            nickname.append(component);
        }

        if (nickname.length() > 6) {
            return nickname.substring(0, 6);
        } else {
            return nickname.toString();
        }
    }

    public static void main(String[] args) {
        // 测试生成不同长度的昵称

    }
    public static int randomLevel(String levelStr){
        String[] levels = levelStr.split("\\|");
        List<Pair<Integer, Integer>> levelList = new ArrayList<>();
        int total = 0;
        for (String level : levels) {
            String[] s = level.split(";");
            int weight = Integer.parseInt(s[1]);
            levelList.add(Pair.of(Integer.parseInt(s[0]), weight));
            total += weight;
        }
        int random = RandomUtils.nextInt(0, total);
        for (int i = 0; i < levelList.size(); i++) {
            Pair<Integer, Integer> p = levelList.get(i);
            if (random < p.getRight()) {
                return p.getLeft();
            } else {
                random -= p.getRight();
            }
        }

        return 0;
    }

    public static Weight randomWeight(List<Weight> weights, int totalWeight) {
        int w = RandomUtils.nextInt(0, totalWeight);
        for (Weight bean : weights) {
            if (w < bean.getWeight()) {
                return bean;
            } else {
                w -= bean.getWeight();
            }
        }
        return null;
    }

    public static <T> T randomList(List<T> list) {
        return list.get(RandomUtils.nextInt(0, list.size()));
    }
}
