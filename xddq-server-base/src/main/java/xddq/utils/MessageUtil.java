package xddq.utils;

import com.google.protobuf.Message;
import io.netty.channel.Channel;
import io.netty.channel.ChannelHandlerContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import xddq.consts.ConstantDefinition;
import xddq.consts.Global;
import xddq.manager.BaseManagerPool;
import xddq.message.IMessage;
import xddq.pb.message.RspPingMsg_3Impl;
import xddq.player.structs.Player;

public class MessageUtil {
    protected static Logger log = LoggerFactory.getLogger(MessageUtil.class);

    public static void tellSession(ChannelHandlerContext ctx, long accountId, IMessage message) {
        tellSession(ctx.channel(), accountId, message);
    }

    public static void tellSession(Channel channel, long accountId, IMessage message) {
        // 服务端日志
        if (!(message instanceof RspPingMsg_3Impl)) {
            if (Global.PRINT_LOG) {
                log.info("send " + message.toString());
            } else {
                if (message.getCtx() != null && message.getCtx().channel() != null) {
                    if (message.getCtx().channel().hasAttr(ConstantDefinition.PLAYER_ID)) {
                        if (Global.TRACK_MAP.getOrDefault(channel.attr(ConstantDefinition.PLAYER_ID).get(), 0) == 1) {
                            log.info("send " + message.toString());
                        }
                    }
                }
            }
        }
        try {
            WriteUtil.write(channel, accountId, message);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    public static void tellSession(ChannelHandlerContext ctx, long accountId, byte[] bytes) {
        try {
            WriteUtil.write(ctx, accountId, bytes);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    public static void tellPlayer(Player player, IMessage message) {
        // 服务端日志
        if (Global.PRINT_LOG) {
            log.info("send " + message.toString());
        } else {
            if (!(message instanceof RspPingMsg_3Impl)) {
                if (Global.TRACK_MAP.getOrDefault(player.getId(), 0) == 1) {
                    log.info("send " + message.toString());
                }
            }
        }
        try {
            ChannelHandlerContext channel = BaseManagerPool.getInstance().sessionManager.getPlayerSession(player.getId());
            if (channel == null) {
                return;
            }
            WriteUtil.write(channel, player.getUserId(), message);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    public static void tellPlayer(Player player, int messageId, Message.Builder builder) {
        // 服务端日志
        if (Global.PRINT_LOG) {
            log.info("send " + messageId + " " + builder.toString());
        } else {
            if (Global.TRACK_MAP.getOrDefault(player.getId(), 0) == 1) {
                log.info("send " + messageId + " " + builder.toString());
            }
        }
        try {
            ChannelHandlerContext channel = BaseManagerPool.getInstance().sessionManager.getPlayerSession(player.getId());
            if (channel == null) {
                return;
            }
            WriteUtil.write(channel, player.getUserId(), messageId, builder);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }
}
