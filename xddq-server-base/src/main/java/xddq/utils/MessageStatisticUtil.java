package xddq.utils;

import io.netty.buffer.ByteBuf;
import xddq.message.IMessage;

import java.util.concurrent.ConcurrentHashMap;

public class MessageStatisticUtil {

    public static ConcurrentHashMap<Integer, Long> packages = new ConcurrentHashMap<>();

    public static ConcurrentHashMap<Integer, Integer> packagenums = new ConcurrentHashMap<>();

    public static ConcurrentHashMap<Integer, Integer> packagemax = new ConcurrentHashMap<>();

    public static ConcurrentHashMap<Integer, Integer> packagemin = new ConcurrentHashMap<>();

    public static void statistic(IMessage message, ByteBuf buf, int size) {
        statistic(message.getId(), buf, size);
    }

    public static void statistic(int messageId, ByteBuf buf, int size) {
        if (buf.readableBytes() > 0) {
            if (packages.containsKey(messageId)) {
                packages.put(messageId, packages.get(messageId) + (long) (buf.readableBytes() + 20) * size);
                packagenums.put(messageId, packagenums.get(messageId) + size);
                int max = packagemax.get(messageId);
                if (max < buf.readableBytes()) {
                    packagemax.put(messageId, buf.readableBytes());
                }
                int min = packagemin.get(messageId);
                if (min > buf.readableBytes()) {
                    packagemin.put(messageId, buf.readableBytes());
                }
            } else {
                packagenums.put(messageId, size);
                packagemax.put(messageId, buf.readableBytes());
                packagemin.put(messageId, buf.readableBytes());
                packages.put(messageId, ((long) buf.readableBytes() + 20) * size);
            }
        }
    }
}
