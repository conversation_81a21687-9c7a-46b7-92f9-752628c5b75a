package xddq.starTrial;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

public class StarTrialData {
//    message StarTrialDataMsg {
//        required int32 bossId = 1;   //当前BossId
//        optional int32 challengeTimes = 2; //剩余挑战次数
//        repeated PlayerHeadAndNameMsg playerHeadAndNameMsg=3; // 前三杀手信息
//        optional int32 prevBossId =4; // 上次通关的BossId 今日挑战 今日通过
//        repeated string powerList = 5; // 当前境界各个星级妖力
//        optional int32 rewardState = 6; // 奖励状态 0 未完成 1代表已完成未领奖 2代表已领奖
//    }
    private int bossId;
    private int challengeTimes;
    private int prevBossId;

    private ConcurrentHashMap<Integer, Long> bosses = new ConcurrentHashMap<>();

    public int getBossId() {
        return bossId;
    }

    public void setBossId(int bossId) {
        this.bossId = bossId;
    }

    public int getChallengeTimes() {
        return challengeTimes;
    }

    public void setChallengeTimes(int challengeTimes) {
        this.challengeTimes = challengeTimes;
    }

    public int getPrevBossId() {
        return prevBossId;
    }

    public void setPrevBossId(int prevBossId) {
        this.prevBossId = prevBossId;
    }

    public ConcurrentHashMap<Integer, Long> getBosses() {
        return bosses;
    }

    public StarTrialData setBosses(ConcurrentHashMap<Integer, Long> bosses) {
        this.bosses = bosses;
        return this;
    }
}
