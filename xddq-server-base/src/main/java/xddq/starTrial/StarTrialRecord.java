package xddq.starTrial;

import xddq.pb.Xddq;

public class StarTrialRecord {
    private long playerId;
    private long time;
    private int recordId;
    private int titleId;
    private byte[] record;

    public long getPlayerId() {
        return playerId;
    }

    public StarTrialRecord setPlayerId(long playerId) {
        this.playerId = playerId;
        return this;
    }

    public long getTime() {
        return time;
    }

    public StarTrialRecord setTime(long time) {
        this.time = time;
        return this;
    }

    public int getRecordId() {
        return recordId;
    }

    public StarTrialRecord setRecordId(int recordId) {
        this.recordId = recordId;
        return this;
    }

    public int getTitleId() {
        return titleId;
    }

    public StarTrialRecord setTitleId(int titleId) {
        this.titleId = titleId;
        return this;
    }

    public byte[] getRecord() {
        return record;
    }

    public StarTrialRecord setRecord(byte[] record) {
        this.record = record;
        return this;
    }
}
