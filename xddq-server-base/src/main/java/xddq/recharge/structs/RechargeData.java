package xddq.recharge.structs;

import java.util.concurrent.ConcurrentHashMap;

public class RechargeData {
    // 领过奖励的订单
    private ConcurrentHashMap<String, Boolean> rewards = new ConcurrentHashMap<>();

    public ConcurrentHashMap<String, Boolean> getRewards() {
        return rewards;
    }

    public RechargeData setRewards(ConcurrentHashMap<String, Boolean> rewards) {
        this.rewards = rewards;
        return this;
    }
}
