package xddq.recharge.structs;

public class RechargeOrder {

    private String type;

    private String orderId;

    private long accountId;

    private long playerId;

    private String code;

    private String money;

    private String name;

    private String url;

    private int mallId;

    private int activityId;

    private String signParam;

    private String extraParam;

    private String clientPackageId;

    private long createTime;

    private String rechargePlatform;

    public String getType() {
        return type;
    }

    public RechargeOrder setType(String type) {
        this.type = type;
        return this;
    }

    public String getOrderId() {
        return orderId;
    }

    public RechargeOrder setOrderId(String orderId) {
        this.orderId = orderId;
        return this;
    }

    public String getCode() {
        return code;
    }

    public long getAccountId() {
        return accountId;
    }

    public RechargeOrder setAccountId(long accountId) {
        this.accountId = accountId;
        return this;
    }

    public long getPlayerId() {
        return playerId;
    }

    public RechargeOrder setPlayerId(long playerId) {
        this.playerId = playerId;
        return this;
    }

    public RechargeOrder setCode(String code) {
        this.code = code;
        return this;
    }

    public String getMoney() {
        return money;
    }

    public RechargeOrder setMoney(String money) {
        this.money = money;
        return this;
    }

    public String getName() {
        return name;
    }

    public RechargeOrder setName(String name) {
        this.name = name;
        return this;
    }

    public String getUrl() {
        return url;
    }

    public RechargeOrder setUrl(String url) {
        this.url = url;
        return this;
    }

    public int getMallId() {
        return mallId;
    }

    public RechargeOrder setMallId(int mallId) {
        this.mallId = mallId;
        return this;
    }

    public int getActivityId() {
        return activityId;
    }

    public RechargeOrder setActivityId(int activityId) {
        this.activityId = activityId;
        return this;
    }

    public String getSignParam() {
        return signParam;
    }

    public RechargeOrder setSignParam(String signParam) {
        this.signParam = signParam;
        return this;
    }

    public String getExtraParam() {
        return extraParam;
    }

    public RechargeOrder setExtraParam(String extraParam) {
        this.extraParam = extraParam;
        return this;
    }

    public String getClientPackageId() {
        return clientPackageId;
    }

    public RechargeOrder setClientPackageId(String clientPackageId) {
        this.clientPackageId = clientPackageId;
        return this;
    }

    public long getCreateTime() {
        return createTime;
    }

    public RechargeOrder setCreateTime(long createTime) {
        this.createTime = createTime;
        return this;
    }

    public String getRechargePlatform() {
        return rechargePlatform;
    }

    public RechargeOrder setRechargePlatform(String rechargePlatform) {
        this.rechargePlatform = rechargePlatform;
        return this;
    }
}
