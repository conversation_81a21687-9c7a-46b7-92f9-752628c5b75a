package xddq.manager;

import xddq.data.manager.DataManager;
import xddq.data.manager.ReloadManager;
import xddq.redis.RedisManager;
import xddq.script.manager.ScriptManager;
import xddq.session.manager.SessionManager;

public class BaseManagerPool {

    private static final BaseManagerPool server = new BaseManagerPool();

	/** 数据管理类 */
	public DataManager dataManager = new DataManager();

	/** 数据重加载管理类 */
	public ReloadManager reloadManager = new ReloadManager();

    /** 脚本管理类 */
    public ScriptManager scriptManager = new ScriptManager();

    /** 会话管理类 */
    public SessionManager sessionManager = new SessionManager();

	/** redis管理类 */
	public RedisManager redisManager = new RedisManager();

    private BaseManagerPool() {
    }

    public static BaseManagerPool getInstance() {
        return server;
    }

    public synchronized void init() throws Exception {
		dataManager.load();
    }
}