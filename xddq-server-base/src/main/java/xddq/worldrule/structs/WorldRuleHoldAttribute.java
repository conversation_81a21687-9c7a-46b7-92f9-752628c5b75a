package xddq.worldrule.structs;

import xddq.pupil.structs.AttributeData;

import java.util.concurrent.ConcurrentHashMap;

public class WorldRuleHoldAttribute {

    private int quality;

    private ConcurrentHashMap<Integer, AttributeData> attributeData = new ConcurrentHashMap<>();

    public int getQuality() {
        return quality;
    }

    public void setQuality(int quality) {
        this.quality = quality;
    }

    public ConcurrentHashMap<Integer, AttributeData> getAttributeData() {
        return attributeData;
    }

    public void setAttributeData(ConcurrentHashMap<Integer, AttributeData> attributeData) {
        this.attributeData = attributeData;
    }
}
