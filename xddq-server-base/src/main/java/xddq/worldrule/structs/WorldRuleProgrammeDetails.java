package xddq.worldrule.structs;

import java.util.concurrent.ConcurrentHashMap;

public class WorldRuleProgrammeDetails {

    private int programmeIndex;

    private ConcurrentHashMap<Integer, WorldRuleHoleDetails> holeListMsg = new ConcurrentHashMap<>();

    public int getProgrammeIndex() {
        return programmeIndex;
    }

    public void setProgrammeIndex(int programmeIndex) {
        this.programmeIndex = programmeIndex;
    }

    public ConcurrentHashMap<Integer, WorldRuleHoleDetails> getHoleListMsg() {
        return holeListMsg;
    }

    public void setHoleListMsg(ConcurrentHashMap<Integer, WorldRuleHoleDetails> holeListMsg) {
        this.holeListMsg = holeListMsg;
    }
}
