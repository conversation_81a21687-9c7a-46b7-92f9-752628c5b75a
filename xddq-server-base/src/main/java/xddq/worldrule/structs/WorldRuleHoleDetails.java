package xddq.worldrule.structs;

import java.util.concurrent.ConcurrentHashMap;

public class WorldRuleHoleDetails {

    private int index;
    private boolean islock;
    private ConcurrentHashMap<Integer, WorldRuleHoldAttribute> curHoldMsg = new ConcurrentHashMap<>();

    private ConcurrentHashMap<Integer, WorldRuleHoldAttribute> perceptionHoldMsg = new ConcurrentHashMap<>();

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public ConcurrentHashMap<Integer, WorldRuleHoldAttribute> getCurHoldMsg() {
        return curHoldMsg;
    }

    public void setCurHoldMsg(ConcurrentHashMap<Integer, WorldRuleHoldAttribute> curHoldMsg) {
        this.curHoldMsg = curHoldMsg;
    }

    public ConcurrentHashMap<Integer, WorldRuleHoldAttribute> getPerceptionHoldMsg() {
        return perceptionHoldMsg;
    }

    public void setPerceptionHoldMsg(ConcurrentHashMap<Integer, WorldRuleHoldAttribute> perceptionHoldMsg) {
        this.perceptionHoldMsg = perceptionHoldMsg;
    }

    public boolean isIslock() {
        return islock;
    }

    public void setIslock(boolean islock) {
        this.islock = islock;
    }
}
