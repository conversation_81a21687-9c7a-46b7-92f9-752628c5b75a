package xddq.worldrule.structs;

import xddq.pb.Xddq;
import java.util.concurrent.ConcurrentHashMap;

public class WorldRule {

    private ConcurrentHashMap<Integer,WorldRuleDetails> ruleListMsg = new ConcurrentHashMap<>();
    private int allTimes;
    private int curRuleProgrammeIdx; //化身
    private int adTimes;
    private ConcurrentHashMap<Integer,SkillMsg> baseAtt = new ConcurrentHashMap<>();


    public int getCurRuleProgrammeIdx() {
        return curRuleProgrammeIdx;
    }

    public void setCurRuleProgrammeIdx(int curRuleProgrammeIdx) {
        this.curRuleProgrammeIdx = curRuleProgrammeIdx;
    }

    public int getAdTimes() {
        return adTimes;
    }

    public void setAdTimes(int adTimes) {
        this.adTimes = adTimes;
    }

    public ConcurrentHashMap<Integer, WorldRuleDetails> getRuleListMsg() {
        return ruleListMsg;
    }

    public void setRuleListMsg(ConcurrentHashMap<Integer, WorldRuleDetails> ruleListMsg) {
        this.ruleListMsg = ruleListMsg;
    }

    public ConcurrentHashMap<Integer, SkillMsg> getBaseAtt() {
        return baseAtt;
    }

    public void setBaseAtt(ConcurrentHashMap<Integer, SkillMsg> baseAtt) {
        this.baseAtt = baseAtt;
    }

    public int getAllTimes() {
        return allTimes;
    }

    public void setAllTimes(int allTimes) {
        this.allTimes = allTimes;
    }
}
