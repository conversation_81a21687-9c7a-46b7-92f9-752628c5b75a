package xddq.worldrule.structs;

import java.util.concurrent.ConcurrentHashMap;

public class WorldRuleDetails {

    private int id;
    private int perceptionTimes;

    private ConcurrentHashMap<Integer, WorldRuleProgrammeDetails> programmeDetailsListMsg = new ConcurrentHashMap<>();

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getPerceptionTimes() {
        return perceptionTimes;
    }

    public void setPerceptionTimes(int perceptionTimes) {
        this.perceptionTimes = perceptionTimes;
    }

    public ConcurrentHashMap<Integer, WorldRuleProgrammeDetails> getProgrammeDetailsListMsg() {
        return programmeDetailsListMsg;
    }

    public void setProgrammeDetailsListMsg(ConcurrentHashMap<Integer, WorldRuleProgrammeDetails> programmeDetailsListMsg) {
        this.programmeDetailsListMsg = programmeDetailsListMsg;
    }
}
