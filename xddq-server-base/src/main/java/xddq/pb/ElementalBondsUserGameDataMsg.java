// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.ElementalBondsUserGameDataMsg}
 */
public final class ElementalBondsUserGameDataMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.ElementalBondsUserGameDataMsg)
    ElementalBondsUserGameDataMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      ElementalBondsUserGameDataMsg.class.getName());
  }
  // Use ElementalBondsUserGameDataMsg.newBuilder() to construct.
  private ElementalBondsUserGameDataMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private ElementalBondsUserGameDataMsg() {
    skill_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_ElementalBondsUserGameDataMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_ElementalBondsUserGameDataMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.ElementalBondsUserGameDataMsg.class, xddq.pb.ElementalBondsUserGameDataMsg.Builder.class);
  }

  private int bitField0_;
  public static final int SIDE_FIELD_NUMBER = 1;
  private int side_ = 0;
  /**
   * <code>required int32 side = 1;</code>
   * @return Whether the side field is set.
   */
  @java.lang.Override
  public boolean hasSide() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 side = 1;</code>
   * @return The side.
   */
  @java.lang.Override
  public int getSide() {
    return side_;
  }

  public static final int SCORE_FIELD_NUMBER = 2;
  private long score_ = 0L;
  /**
   * <code>required int64 score = 2;</code>
   * @return Whether the score field is set.
   */
  @java.lang.Override
  public boolean hasScore() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>required int64 score = 2;</code>
   * @return The score.
   */
  @java.lang.Override
  public long getScore() {
    return score_;
  }

  public static final int STEP_FIELD_NUMBER = 3;
  private int step_ = 0;
  /**
   * <code>required int32 step = 3;</code>
   * @return Whether the step field is set.
   */
  @java.lang.Override
  public boolean hasStep() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>required int32 step = 3;</code>
   * @return The step.
   */
  @java.lang.Override
  public int getStep() {
    return step_;
  }

  public static final int CARD_FIELD_NUMBER = 4;
  private xddq.pb.ElementalBondsUserCardMsg card_;
  /**
   * <code>required .xddq.pb.ElementalBondsUserCardMsg card = 4;</code>
   * @return Whether the card field is set.
   */
  @java.lang.Override
  public boolean hasCard() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>required .xddq.pb.ElementalBondsUserCardMsg card = 4;</code>
   * @return The card.
   */
  @java.lang.Override
  public xddq.pb.ElementalBondsUserCardMsg getCard() {
    return card_ == null ? xddq.pb.ElementalBondsUserCardMsg.getDefaultInstance() : card_;
  }
  /**
   * <code>required .xddq.pb.ElementalBondsUserCardMsg card = 4;</code>
   */
  @java.lang.Override
  public xddq.pb.ElementalBondsUserCardMsgOrBuilder getCardOrBuilder() {
    return card_ == null ? xddq.pb.ElementalBondsUserCardMsg.getDefaultInstance() : card_;
  }

  public static final int SKILL_FIELD_NUMBER = 5;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.ElementalBondsUserSkillMsg> skill_;
  /**
   * <code>repeated .xddq.pb.ElementalBondsUserSkillMsg skill = 5;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.ElementalBondsUserSkillMsg> getSkillList() {
    return skill_;
  }
  /**
   * <code>repeated .xddq.pb.ElementalBondsUserSkillMsg skill = 5;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.ElementalBondsUserSkillMsgOrBuilder> 
      getSkillOrBuilderList() {
    return skill_;
  }
  /**
   * <code>repeated .xddq.pb.ElementalBondsUserSkillMsg skill = 5;</code>
   */
  @java.lang.Override
  public int getSkillCount() {
    return skill_.size();
  }
  /**
   * <code>repeated .xddq.pb.ElementalBondsUserSkillMsg skill = 5;</code>
   */
  @java.lang.Override
  public xddq.pb.ElementalBondsUserSkillMsg getSkill(int index) {
    return skill_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.ElementalBondsUserSkillMsg skill = 5;</code>
   */
  @java.lang.Override
  public xddq.pb.ElementalBondsUserSkillMsgOrBuilder getSkillOrBuilder(
      int index) {
    return skill_.get(index);
  }

  public static final int USEBOARDSKIN_FIELD_NUMBER = 6;
  private int useBoardSkin_ = 0;
  /**
   * <code>optional int32 useBoardSkin = 6;</code>
   * @return Whether the useBoardSkin field is set.
   */
  @java.lang.Override
  public boolean hasUseBoardSkin() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>optional int32 useBoardSkin = 6;</code>
   * @return The useBoardSkin.
   */
  @java.lang.Override
  public int getUseBoardSkin() {
    return useBoardSkin_;
  }

  public static final int USEANIMATION_FIELD_NUMBER = 7;
  private int useAnimation_ = 0;
  /**
   * <code>optional int32 useAnimation = 7;</code>
   * @return Whether the useAnimation field is set.
   */
  @java.lang.Override
  public boolean hasUseAnimation() {
    return ((bitField0_ & 0x00000020) != 0);
  }
  /**
   * <code>optional int32 useAnimation = 7;</code>
   * @return The useAnimation.
   */
  @java.lang.Override
  public int getUseAnimation() {
    return useAnimation_;
  }

  public static final int USEEFFECT_FIELD_NUMBER = 8;
  private int useEffect_ = 0;
  /**
   * <code>optional int32 useEffect = 8;</code>
   * @return Whether the useEffect field is set.
   */
  @java.lang.Override
  public boolean hasUseEffect() {
    return ((bitField0_ & 0x00000040) != 0);
  }
  /**
   * <code>optional int32 useEffect = 8;</code>
   * @return The useEffect.
   */
  @java.lang.Override
  public int getUseEffect() {
    return useEffect_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasSide()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasScore()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasStep()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasCard()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!getCard().isInitialized()) {
      memoizedIsInitialized = 0;
      return false;
    }
    for (int i = 0; i < getSkillCount(); i++) {
      if (!getSkill(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, side_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt64(2, score_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt32(3, step_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeMessage(4, getCard());
    }
    for (int i = 0; i < skill_.size(); i++) {
      output.writeMessage(5, skill_.get(i));
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      output.writeInt32(6, useBoardSkin_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      output.writeInt32(7, useAnimation_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      output.writeInt32(8, useEffect_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, side_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(2, score_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(3, step_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(4, getCard());
    }
    for (int i = 0; i < skill_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(5, skill_.get(i));
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(6, useBoardSkin_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(7, useAnimation_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(8, useEffect_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.ElementalBondsUserGameDataMsg)) {
      return super.equals(obj);
    }
    xddq.pb.ElementalBondsUserGameDataMsg other = (xddq.pb.ElementalBondsUserGameDataMsg) obj;

    if (hasSide() != other.hasSide()) return false;
    if (hasSide()) {
      if (getSide()
          != other.getSide()) return false;
    }
    if (hasScore() != other.hasScore()) return false;
    if (hasScore()) {
      if (getScore()
          != other.getScore()) return false;
    }
    if (hasStep() != other.hasStep()) return false;
    if (hasStep()) {
      if (getStep()
          != other.getStep()) return false;
    }
    if (hasCard() != other.hasCard()) return false;
    if (hasCard()) {
      if (!getCard()
          .equals(other.getCard())) return false;
    }
    if (!getSkillList()
        .equals(other.getSkillList())) return false;
    if (hasUseBoardSkin() != other.hasUseBoardSkin()) return false;
    if (hasUseBoardSkin()) {
      if (getUseBoardSkin()
          != other.getUseBoardSkin()) return false;
    }
    if (hasUseAnimation() != other.hasUseAnimation()) return false;
    if (hasUseAnimation()) {
      if (getUseAnimation()
          != other.getUseAnimation()) return false;
    }
    if (hasUseEffect() != other.hasUseEffect()) return false;
    if (hasUseEffect()) {
      if (getUseEffect()
          != other.getUseEffect()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasSide()) {
      hash = (37 * hash) + SIDE_FIELD_NUMBER;
      hash = (53 * hash) + getSide();
    }
    if (hasScore()) {
      hash = (37 * hash) + SCORE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getScore());
    }
    if (hasStep()) {
      hash = (37 * hash) + STEP_FIELD_NUMBER;
      hash = (53 * hash) + getStep();
    }
    if (hasCard()) {
      hash = (37 * hash) + CARD_FIELD_NUMBER;
      hash = (53 * hash) + getCard().hashCode();
    }
    if (getSkillCount() > 0) {
      hash = (37 * hash) + SKILL_FIELD_NUMBER;
      hash = (53 * hash) + getSkillList().hashCode();
    }
    if (hasUseBoardSkin()) {
      hash = (37 * hash) + USEBOARDSKIN_FIELD_NUMBER;
      hash = (53 * hash) + getUseBoardSkin();
    }
    if (hasUseAnimation()) {
      hash = (37 * hash) + USEANIMATION_FIELD_NUMBER;
      hash = (53 * hash) + getUseAnimation();
    }
    if (hasUseEffect()) {
      hash = (37 * hash) + USEEFFECT_FIELD_NUMBER;
      hash = (53 * hash) + getUseEffect();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.ElementalBondsUserGameDataMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ElementalBondsUserGameDataMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ElementalBondsUserGameDataMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ElementalBondsUserGameDataMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ElementalBondsUserGameDataMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ElementalBondsUserGameDataMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ElementalBondsUserGameDataMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.ElementalBondsUserGameDataMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.ElementalBondsUserGameDataMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.ElementalBondsUserGameDataMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.ElementalBondsUserGameDataMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.ElementalBondsUserGameDataMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.ElementalBondsUserGameDataMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.ElementalBondsUserGameDataMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.ElementalBondsUserGameDataMsg)
      xddq.pb.ElementalBondsUserGameDataMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ElementalBondsUserGameDataMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ElementalBondsUserGameDataMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.ElementalBondsUserGameDataMsg.class, xddq.pb.ElementalBondsUserGameDataMsg.Builder.class);
    }

    // Construct using xddq.pb.ElementalBondsUserGameDataMsg.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetCardFieldBuilder();
        internalGetSkillFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      side_ = 0;
      score_ = 0L;
      step_ = 0;
      card_ = null;
      if (cardBuilder_ != null) {
        cardBuilder_.dispose();
        cardBuilder_ = null;
      }
      if (skillBuilder_ == null) {
        skill_ = java.util.Collections.emptyList();
      } else {
        skill_ = null;
        skillBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000010);
      useBoardSkin_ = 0;
      useAnimation_ = 0;
      useEffect_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ElementalBondsUserGameDataMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.ElementalBondsUserGameDataMsg getDefaultInstanceForType() {
      return xddq.pb.ElementalBondsUserGameDataMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.ElementalBondsUserGameDataMsg build() {
      xddq.pb.ElementalBondsUserGameDataMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.ElementalBondsUserGameDataMsg buildPartial() {
      xddq.pb.ElementalBondsUserGameDataMsg result = new xddq.pb.ElementalBondsUserGameDataMsg(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.ElementalBondsUserGameDataMsg result) {
      if (skillBuilder_ == null) {
        if (((bitField0_ & 0x00000010) != 0)) {
          skill_ = java.util.Collections.unmodifiableList(skill_);
          bitField0_ = (bitField0_ & ~0x00000010);
        }
        result.skill_ = skill_;
      } else {
        result.skill_ = skillBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.ElementalBondsUserGameDataMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.side_ = side_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.score_ = score_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.step_ = step_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.card_ = cardBuilder_ == null
            ? card_
            : cardBuilder_.build();
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.useBoardSkin_ = useBoardSkin_;
        to_bitField0_ |= 0x00000010;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.useAnimation_ = useAnimation_;
        to_bitField0_ |= 0x00000020;
      }
      if (((from_bitField0_ & 0x00000080) != 0)) {
        result.useEffect_ = useEffect_;
        to_bitField0_ |= 0x00000040;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.ElementalBondsUserGameDataMsg) {
        return mergeFrom((xddq.pb.ElementalBondsUserGameDataMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.ElementalBondsUserGameDataMsg other) {
      if (other == xddq.pb.ElementalBondsUserGameDataMsg.getDefaultInstance()) return this;
      if (other.hasSide()) {
        setSide(other.getSide());
      }
      if (other.hasScore()) {
        setScore(other.getScore());
      }
      if (other.hasStep()) {
        setStep(other.getStep());
      }
      if (other.hasCard()) {
        mergeCard(other.getCard());
      }
      if (skillBuilder_ == null) {
        if (!other.skill_.isEmpty()) {
          if (skill_.isEmpty()) {
            skill_ = other.skill_;
            bitField0_ = (bitField0_ & ~0x00000010);
          } else {
            ensureSkillIsMutable();
            skill_.addAll(other.skill_);
          }
          onChanged();
        }
      } else {
        if (!other.skill_.isEmpty()) {
          if (skillBuilder_.isEmpty()) {
            skillBuilder_.dispose();
            skillBuilder_ = null;
            skill_ = other.skill_;
            bitField0_ = (bitField0_ & ~0x00000010);
            skillBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetSkillFieldBuilder() : null;
          } else {
            skillBuilder_.addAllMessages(other.skill_);
          }
        }
      }
      if (other.hasUseBoardSkin()) {
        setUseBoardSkin(other.getUseBoardSkin());
      }
      if (other.hasUseAnimation()) {
        setUseAnimation(other.getUseAnimation());
      }
      if (other.hasUseEffect()) {
        setUseEffect(other.getUseEffect());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasSide()) {
        return false;
      }
      if (!hasScore()) {
        return false;
      }
      if (!hasStep()) {
        return false;
      }
      if (!hasCard()) {
        return false;
      }
      if (!getCard().isInitialized()) {
        return false;
      }
      for (int i = 0; i < getSkillCount(); i++) {
        if (!getSkill(i).isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              side_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              score_ = input.readInt64();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 24: {
              step_ = input.readInt32();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 34: {
              input.readMessage(
                  internalGetCardFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000008;
              break;
            } // case 34
            case 42: {
              xddq.pb.ElementalBondsUserSkillMsg m =
                  input.readMessage(
                      xddq.pb.ElementalBondsUserSkillMsg.parser(),
                      extensionRegistry);
              if (skillBuilder_ == null) {
                ensureSkillIsMutable();
                skill_.add(m);
              } else {
                skillBuilder_.addMessage(m);
              }
              break;
            } // case 42
            case 48: {
              useBoardSkin_ = input.readInt32();
              bitField0_ |= 0x00000020;
              break;
            } // case 48
            case 56: {
              useAnimation_ = input.readInt32();
              bitField0_ |= 0x00000040;
              break;
            } // case 56
            case 64: {
              useEffect_ = input.readInt32();
              bitField0_ |= 0x00000080;
              break;
            } // case 64
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int side_ ;
    /**
     * <code>required int32 side = 1;</code>
     * @return Whether the side field is set.
     */
    @java.lang.Override
    public boolean hasSide() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 side = 1;</code>
     * @return The side.
     */
    @java.lang.Override
    public int getSide() {
      return side_;
    }
    /**
     * <code>required int32 side = 1;</code>
     * @param value The side to set.
     * @return This builder for chaining.
     */
    public Builder setSide(int value) {

      side_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 side = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearSide() {
      bitField0_ = (bitField0_ & ~0x00000001);
      side_ = 0;
      onChanged();
      return this;
    }

    private long score_ ;
    /**
     * <code>required int64 score = 2;</code>
     * @return Whether the score field is set.
     */
    @java.lang.Override
    public boolean hasScore() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>required int64 score = 2;</code>
     * @return The score.
     */
    @java.lang.Override
    public long getScore() {
      return score_;
    }
    /**
     * <code>required int64 score = 2;</code>
     * @param value The score to set.
     * @return This builder for chaining.
     */
    public Builder setScore(long value) {

      score_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>required int64 score = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearScore() {
      bitField0_ = (bitField0_ & ~0x00000002);
      score_ = 0L;
      onChanged();
      return this;
    }

    private int step_ ;
    /**
     * <code>required int32 step = 3;</code>
     * @return Whether the step field is set.
     */
    @java.lang.Override
    public boolean hasStep() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>required int32 step = 3;</code>
     * @return The step.
     */
    @java.lang.Override
    public int getStep() {
      return step_;
    }
    /**
     * <code>required int32 step = 3;</code>
     * @param value The step to set.
     * @return This builder for chaining.
     */
    public Builder setStep(int value) {

      step_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 step = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearStep() {
      bitField0_ = (bitField0_ & ~0x00000004);
      step_ = 0;
      onChanged();
      return this;
    }

    private xddq.pb.ElementalBondsUserCardMsg card_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.ElementalBondsUserCardMsg, xddq.pb.ElementalBondsUserCardMsg.Builder, xddq.pb.ElementalBondsUserCardMsgOrBuilder> cardBuilder_;
    /**
     * <code>required .xddq.pb.ElementalBondsUserCardMsg card = 4;</code>
     * @return Whether the card field is set.
     */
    public boolean hasCard() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>required .xddq.pb.ElementalBondsUserCardMsg card = 4;</code>
     * @return The card.
     */
    public xddq.pb.ElementalBondsUserCardMsg getCard() {
      if (cardBuilder_ == null) {
        return card_ == null ? xddq.pb.ElementalBondsUserCardMsg.getDefaultInstance() : card_;
      } else {
        return cardBuilder_.getMessage();
      }
    }
    /**
     * <code>required .xddq.pb.ElementalBondsUserCardMsg card = 4;</code>
     */
    public Builder setCard(xddq.pb.ElementalBondsUserCardMsg value) {
      if (cardBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        card_ = value;
      } else {
        cardBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>required .xddq.pb.ElementalBondsUserCardMsg card = 4;</code>
     */
    public Builder setCard(
        xddq.pb.ElementalBondsUserCardMsg.Builder builderForValue) {
      if (cardBuilder_ == null) {
        card_ = builderForValue.build();
      } else {
        cardBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>required .xddq.pb.ElementalBondsUserCardMsg card = 4;</code>
     */
    public Builder mergeCard(xddq.pb.ElementalBondsUserCardMsg value) {
      if (cardBuilder_ == null) {
        if (((bitField0_ & 0x00000008) != 0) &&
          card_ != null &&
          card_ != xddq.pb.ElementalBondsUserCardMsg.getDefaultInstance()) {
          getCardBuilder().mergeFrom(value);
        } else {
          card_ = value;
        }
      } else {
        cardBuilder_.mergeFrom(value);
      }
      if (card_ != null) {
        bitField0_ |= 0x00000008;
        onChanged();
      }
      return this;
    }
    /**
     * <code>required .xddq.pb.ElementalBondsUserCardMsg card = 4;</code>
     */
    public Builder clearCard() {
      bitField0_ = (bitField0_ & ~0x00000008);
      card_ = null;
      if (cardBuilder_ != null) {
        cardBuilder_.dispose();
        cardBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>required .xddq.pb.ElementalBondsUserCardMsg card = 4;</code>
     */
    public xddq.pb.ElementalBondsUserCardMsg.Builder getCardBuilder() {
      bitField0_ |= 0x00000008;
      onChanged();
      return internalGetCardFieldBuilder().getBuilder();
    }
    /**
     * <code>required .xddq.pb.ElementalBondsUserCardMsg card = 4;</code>
     */
    public xddq.pb.ElementalBondsUserCardMsgOrBuilder getCardOrBuilder() {
      if (cardBuilder_ != null) {
        return cardBuilder_.getMessageOrBuilder();
      } else {
        return card_ == null ?
            xddq.pb.ElementalBondsUserCardMsg.getDefaultInstance() : card_;
      }
    }
    /**
     * <code>required .xddq.pb.ElementalBondsUserCardMsg card = 4;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.ElementalBondsUserCardMsg, xddq.pb.ElementalBondsUserCardMsg.Builder, xddq.pb.ElementalBondsUserCardMsgOrBuilder> 
        internalGetCardFieldBuilder() {
      if (cardBuilder_ == null) {
        cardBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.ElementalBondsUserCardMsg, xddq.pb.ElementalBondsUserCardMsg.Builder, xddq.pb.ElementalBondsUserCardMsgOrBuilder>(
                getCard(),
                getParentForChildren(),
                isClean());
        card_ = null;
      }
      return cardBuilder_;
    }

    private java.util.List<xddq.pb.ElementalBondsUserSkillMsg> skill_ =
      java.util.Collections.emptyList();
    private void ensureSkillIsMutable() {
      if (!((bitField0_ & 0x00000010) != 0)) {
        skill_ = new java.util.ArrayList<xddq.pb.ElementalBondsUserSkillMsg>(skill_);
        bitField0_ |= 0x00000010;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.ElementalBondsUserSkillMsg, xddq.pb.ElementalBondsUserSkillMsg.Builder, xddq.pb.ElementalBondsUserSkillMsgOrBuilder> skillBuilder_;

    /**
     * <code>repeated .xddq.pb.ElementalBondsUserSkillMsg skill = 5;</code>
     */
    public java.util.List<xddq.pb.ElementalBondsUserSkillMsg> getSkillList() {
      if (skillBuilder_ == null) {
        return java.util.Collections.unmodifiableList(skill_);
      } else {
        return skillBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsUserSkillMsg skill = 5;</code>
     */
    public int getSkillCount() {
      if (skillBuilder_ == null) {
        return skill_.size();
      } else {
        return skillBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsUserSkillMsg skill = 5;</code>
     */
    public xddq.pb.ElementalBondsUserSkillMsg getSkill(int index) {
      if (skillBuilder_ == null) {
        return skill_.get(index);
      } else {
        return skillBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsUserSkillMsg skill = 5;</code>
     */
    public Builder setSkill(
        int index, xddq.pb.ElementalBondsUserSkillMsg value) {
      if (skillBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureSkillIsMutable();
        skill_.set(index, value);
        onChanged();
      } else {
        skillBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsUserSkillMsg skill = 5;</code>
     */
    public Builder setSkill(
        int index, xddq.pb.ElementalBondsUserSkillMsg.Builder builderForValue) {
      if (skillBuilder_ == null) {
        ensureSkillIsMutable();
        skill_.set(index, builderForValue.build());
        onChanged();
      } else {
        skillBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsUserSkillMsg skill = 5;</code>
     */
    public Builder addSkill(xddq.pb.ElementalBondsUserSkillMsg value) {
      if (skillBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureSkillIsMutable();
        skill_.add(value);
        onChanged();
      } else {
        skillBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsUserSkillMsg skill = 5;</code>
     */
    public Builder addSkill(
        int index, xddq.pb.ElementalBondsUserSkillMsg value) {
      if (skillBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureSkillIsMutable();
        skill_.add(index, value);
        onChanged();
      } else {
        skillBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsUserSkillMsg skill = 5;</code>
     */
    public Builder addSkill(
        xddq.pb.ElementalBondsUserSkillMsg.Builder builderForValue) {
      if (skillBuilder_ == null) {
        ensureSkillIsMutable();
        skill_.add(builderForValue.build());
        onChanged();
      } else {
        skillBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsUserSkillMsg skill = 5;</code>
     */
    public Builder addSkill(
        int index, xddq.pb.ElementalBondsUserSkillMsg.Builder builderForValue) {
      if (skillBuilder_ == null) {
        ensureSkillIsMutable();
        skill_.add(index, builderForValue.build());
        onChanged();
      } else {
        skillBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsUserSkillMsg skill = 5;</code>
     */
    public Builder addAllSkill(
        java.lang.Iterable<? extends xddq.pb.ElementalBondsUserSkillMsg> values) {
      if (skillBuilder_ == null) {
        ensureSkillIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, skill_);
        onChanged();
      } else {
        skillBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsUserSkillMsg skill = 5;</code>
     */
    public Builder clearSkill() {
      if (skillBuilder_ == null) {
        skill_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000010);
        onChanged();
      } else {
        skillBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsUserSkillMsg skill = 5;</code>
     */
    public Builder removeSkill(int index) {
      if (skillBuilder_ == null) {
        ensureSkillIsMutable();
        skill_.remove(index);
        onChanged();
      } else {
        skillBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsUserSkillMsg skill = 5;</code>
     */
    public xddq.pb.ElementalBondsUserSkillMsg.Builder getSkillBuilder(
        int index) {
      return internalGetSkillFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsUserSkillMsg skill = 5;</code>
     */
    public xddq.pb.ElementalBondsUserSkillMsgOrBuilder getSkillOrBuilder(
        int index) {
      if (skillBuilder_ == null) {
        return skill_.get(index);  } else {
        return skillBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsUserSkillMsg skill = 5;</code>
     */
    public java.util.List<? extends xddq.pb.ElementalBondsUserSkillMsgOrBuilder> 
         getSkillOrBuilderList() {
      if (skillBuilder_ != null) {
        return skillBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(skill_);
      }
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsUserSkillMsg skill = 5;</code>
     */
    public xddq.pb.ElementalBondsUserSkillMsg.Builder addSkillBuilder() {
      return internalGetSkillFieldBuilder().addBuilder(
          xddq.pb.ElementalBondsUserSkillMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsUserSkillMsg skill = 5;</code>
     */
    public xddq.pb.ElementalBondsUserSkillMsg.Builder addSkillBuilder(
        int index) {
      return internalGetSkillFieldBuilder().addBuilder(
          index, xddq.pb.ElementalBondsUserSkillMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsUserSkillMsg skill = 5;</code>
     */
    public java.util.List<xddq.pb.ElementalBondsUserSkillMsg.Builder> 
         getSkillBuilderList() {
      return internalGetSkillFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.ElementalBondsUserSkillMsg, xddq.pb.ElementalBondsUserSkillMsg.Builder, xddq.pb.ElementalBondsUserSkillMsgOrBuilder> 
        internalGetSkillFieldBuilder() {
      if (skillBuilder_ == null) {
        skillBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.ElementalBondsUserSkillMsg, xddq.pb.ElementalBondsUserSkillMsg.Builder, xddq.pb.ElementalBondsUserSkillMsgOrBuilder>(
                skill_,
                ((bitField0_ & 0x00000010) != 0),
                getParentForChildren(),
                isClean());
        skill_ = null;
      }
      return skillBuilder_;
    }

    private int useBoardSkin_ ;
    /**
     * <code>optional int32 useBoardSkin = 6;</code>
     * @return Whether the useBoardSkin field is set.
     */
    @java.lang.Override
    public boolean hasUseBoardSkin() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional int32 useBoardSkin = 6;</code>
     * @return The useBoardSkin.
     */
    @java.lang.Override
    public int getUseBoardSkin() {
      return useBoardSkin_;
    }
    /**
     * <code>optional int32 useBoardSkin = 6;</code>
     * @param value The useBoardSkin to set.
     * @return This builder for chaining.
     */
    public Builder setUseBoardSkin(int value) {

      useBoardSkin_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 useBoardSkin = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearUseBoardSkin() {
      bitField0_ = (bitField0_ & ~0x00000020);
      useBoardSkin_ = 0;
      onChanged();
      return this;
    }

    private int useAnimation_ ;
    /**
     * <code>optional int32 useAnimation = 7;</code>
     * @return Whether the useAnimation field is set.
     */
    @java.lang.Override
    public boolean hasUseAnimation() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <code>optional int32 useAnimation = 7;</code>
     * @return The useAnimation.
     */
    @java.lang.Override
    public int getUseAnimation() {
      return useAnimation_;
    }
    /**
     * <code>optional int32 useAnimation = 7;</code>
     * @param value The useAnimation to set.
     * @return This builder for chaining.
     */
    public Builder setUseAnimation(int value) {

      useAnimation_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 useAnimation = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearUseAnimation() {
      bitField0_ = (bitField0_ & ~0x00000040);
      useAnimation_ = 0;
      onChanged();
      return this;
    }

    private int useEffect_ ;
    /**
     * <code>optional int32 useEffect = 8;</code>
     * @return Whether the useEffect field is set.
     */
    @java.lang.Override
    public boolean hasUseEffect() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <code>optional int32 useEffect = 8;</code>
     * @return The useEffect.
     */
    @java.lang.Override
    public int getUseEffect() {
      return useEffect_;
    }
    /**
     * <code>optional int32 useEffect = 8;</code>
     * @param value The useEffect to set.
     * @return This builder for chaining.
     */
    public Builder setUseEffect(int value) {

      useEffect_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 useEffect = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearUseEffect() {
      bitField0_ = (bitField0_ & ~0x00000080);
      useEffect_ = 0;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.ElementalBondsUserGameDataMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.ElementalBondsUserGameDataMsg)
  private static final xddq.pb.ElementalBondsUserGameDataMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.ElementalBondsUserGameDataMsg();
  }

  public static xddq.pb.ElementalBondsUserGameDataMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ElementalBondsUserGameDataMsg>
      PARSER = new com.google.protobuf.AbstractParser<ElementalBondsUserGameDataMsg>() {
    @java.lang.Override
    public ElementalBondsUserGameDataMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<ElementalBondsUserGameDataMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ElementalBondsUserGameDataMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.ElementalBondsUserGameDataMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

