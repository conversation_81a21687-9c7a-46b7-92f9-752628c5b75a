// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.RspHeroRankFightPlayerList}
 */
public final class RspHeroRankFightPlayerList extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.RspHeroRankFightPlayerList)
    RspHeroRankFightPlayerListOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      RspHeroRankFightPlayerList.class.getName());
  }
  // Use RspHeroRankFightPlayerList.newBuilder() to construct.
  private RspHeroRankFightPlayerList(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private RspHeroRankFightPlayerList() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_RspHeroRankFightPlayerList_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_RspHeroRankFightPlayerList_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.RspHeroRankFightPlayerList.class, xddq.pb.RspHeroRankFightPlayerList.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>optional int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int FIGHTPLAYERLIST_FIELD_NUMBER = 2;
  private xddq.pb.HeroRankFightPlayerDataListMsg fightPlayerList_;
  /**
   * <code>optional .xddq.pb.HeroRankFightPlayerDataListMsg fightPlayerList = 2;</code>
   * @return Whether the fightPlayerList field is set.
   */
  @java.lang.Override
  public boolean hasFightPlayerList() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional .xddq.pb.HeroRankFightPlayerDataListMsg fightPlayerList = 2;</code>
   * @return The fightPlayerList.
   */
  @java.lang.Override
  public xddq.pb.HeroRankFightPlayerDataListMsg getFightPlayerList() {
    return fightPlayerList_ == null ? xddq.pb.HeroRankFightPlayerDataListMsg.getDefaultInstance() : fightPlayerList_;
  }
  /**
   * <code>optional .xddq.pb.HeroRankFightPlayerDataListMsg fightPlayerList = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.HeroRankFightPlayerDataListMsgOrBuilder getFightPlayerListOrBuilder() {
    return fightPlayerList_ == null ? xddq.pb.HeroRankFightPlayerDataListMsg.getDefaultInstance() : fightPlayerList_;
  }

  public static final int TYPE_FIELD_NUMBER = 3;
  private int type_ = 0;
  /**
   * <code>optional int32 type = 3;</code>
   * @return Whether the type field is set.
   */
  @java.lang.Override
  public boolean hasType() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional int32 type = 3;</code>
   * @return The type.
   */
  @java.lang.Override
  public int getType() {
    return type_;
  }

  public static final int RANK_FIELD_NUMBER = 4;
  private int rank_ = 0;
  /**
   * <code>optional int32 rank = 4;</code>
   * @return Whether the rank field is set.
   */
  @java.lang.Override
  public boolean hasRank() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional int32 rank = 4;</code>
   * @return The rank.
   */
  @java.lang.Override
  public int getRank() {
    return rank_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeMessage(2, getFightPlayerList());
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt32(3, type_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt32(4, rank_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getFightPlayerList());
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(3, type_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(4, rank_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.RspHeroRankFightPlayerList)) {
      return super.equals(obj);
    }
    xddq.pb.RspHeroRankFightPlayerList other = (xddq.pb.RspHeroRankFightPlayerList) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (hasFightPlayerList() != other.hasFightPlayerList()) return false;
    if (hasFightPlayerList()) {
      if (!getFightPlayerList()
          .equals(other.getFightPlayerList())) return false;
    }
    if (hasType() != other.hasType()) return false;
    if (hasType()) {
      if (getType()
          != other.getType()) return false;
    }
    if (hasRank() != other.hasRank()) return false;
    if (hasRank()) {
      if (getRank()
          != other.getRank()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (hasFightPlayerList()) {
      hash = (37 * hash) + FIGHTPLAYERLIST_FIELD_NUMBER;
      hash = (53 * hash) + getFightPlayerList().hashCode();
    }
    if (hasType()) {
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
    }
    if (hasRank()) {
      hash = (37 * hash) + RANK_FIELD_NUMBER;
      hash = (53 * hash) + getRank();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.RspHeroRankFightPlayerList parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.RspHeroRankFightPlayerList parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.RspHeroRankFightPlayerList parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.RspHeroRankFightPlayerList parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.RspHeroRankFightPlayerList parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.RspHeroRankFightPlayerList parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.RspHeroRankFightPlayerList parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.RspHeroRankFightPlayerList parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.RspHeroRankFightPlayerList parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.RspHeroRankFightPlayerList parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.RspHeroRankFightPlayerList parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.RspHeroRankFightPlayerList parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.RspHeroRankFightPlayerList prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.RspHeroRankFightPlayerList}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.RspHeroRankFightPlayerList)
      xddq.pb.RspHeroRankFightPlayerListOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_RspHeroRankFightPlayerList_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_RspHeroRankFightPlayerList_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.RspHeroRankFightPlayerList.class, xddq.pb.RspHeroRankFightPlayerList.Builder.class);
    }

    // Construct using xddq.pb.RspHeroRankFightPlayerList.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetFightPlayerListFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      fightPlayerList_ = null;
      if (fightPlayerListBuilder_ != null) {
        fightPlayerListBuilder_.dispose();
        fightPlayerListBuilder_ = null;
      }
      type_ = 0;
      rank_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_RspHeroRankFightPlayerList_descriptor;
    }

    @java.lang.Override
    public xddq.pb.RspHeroRankFightPlayerList getDefaultInstanceForType() {
      return xddq.pb.RspHeroRankFightPlayerList.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.RspHeroRankFightPlayerList build() {
      xddq.pb.RspHeroRankFightPlayerList result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.RspHeroRankFightPlayerList buildPartial() {
      xddq.pb.RspHeroRankFightPlayerList result = new xddq.pb.RspHeroRankFightPlayerList(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.RspHeroRankFightPlayerList result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.fightPlayerList_ = fightPlayerListBuilder_ == null
            ? fightPlayerList_
            : fightPlayerListBuilder_.build();
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.type_ = type_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.rank_ = rank_;
        to_bitField0_ |= 0x00000008;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.RspHeroRankFightPlayerList) {
        return mergeFrom((xddq.pb.RspHeroRankFightPlayerList)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.RspHeroRankFightPlayerList other) {
      if (other == xddq.pb.RspHeroRankFightPlayerList.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (other.hasFightPlayerList()) {
        mergeFightPlayerList(other.getFightPlayerList());
      }
      if (other.hasType()) {
        setType(other.getType());
      }
      if (other.hasRank()) {
        setRank(other.getRank());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              input.readMessage(
                  internalGetFightPlayerListFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 24: {
              type_ = input.readInt32();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 32: {
              rank_ = input.readInt32();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>optional int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>optional int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private xddq.pb.HeroRankFightPlayerDataListMsg fightPlayerList_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.HeroRankFightPlayerDataListMsg, xddq.pb.HeroRankFightPlayerDataListMsg.Builder, xddq.pb.HeroRankFightPlayerDataListMsgOrBuilder> fightPlayerListBuilder_;
    /**
     * <code>optional .xddq.pb.HeroRankFightPlayerDataListMsg fightPlayerList = 2;</code>
     * @return Whether the fightPlayerList field is set.
     */
    public boolean hasFightPlayerList() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .xddq.pb.HeroRankFightPlayerDataListMsg fightPlayerList = 2;</code>
     * @return The fightPlayerList.
     */
    public xddq.pb.HeroRankFightPlayerDataListMsg getFightPlayerList() {
      if (fightPlayerListBuilder_ == null) {
        return fightPlayerList_ == null ? xddq.pb.HeroRankFightPlayerDataListMsg.getDefaultInstance() : fightPlayerList_;
      } else {
        return fightPlayerListBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.HeroRankFightPlayerDataListMsg fightPlayerList = 2;</code>
     */
    public Builder setFightPlayerList(xddq.pb.HeroRankFightPlayerDataListMsg value) {
      if (fightPlayerListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        fightPlayerList_ = value;
      } else {
        fightPlayerListBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.HeroRankFightPlayerDataListMsg fightPlayerList = 2;</code>
     */
    public Builder setFightPlayerList(
        xddq.pb.HeroRankFightPlayerDataListMsg.Builder builderForValue) {
      if (fightPlayerListBuilder_ == null) {
        fightPlayerList_ = builderForValue.build();
      } else {
        fightPlayerListBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.HeroRankFightPlayerDataListMsg fightPlayerList = 2;</code>
     */
    public Builder mergeFightPlayerList(xddq.pb.HeroRankFightPlayerDataListMsg value) {
      if (fightPlayerListBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0) &&
          fightPlayerList_ != null &&
          fightPlayerList_ != xddq.pb.HeroRankFightPlayerDataListMsg.getDefaultInstance()) {
          getFightPlayerListBuilder().mergeFrom(value);
        } else {
          fightPlayerList_ = value;
        }
      } else {
        fightPlayerListBuilder_.mergeFrom(value);
      }
      if (fightPlayerList_ != null) {
        bitField0_ |= 0x00000002;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.HeroRankFightPlayerDataListMsg fightPlayerList = 2;</code>
     */
    public Builder clearFightPlayerList() {
      bitField0_ = (bitField0_ & ~0x00000002);
      fightPlayerList_ = null;
      if (fightPlayerListBuilder_ != null) {
        fightPlayerListBuilder_.dispose();
        fightPlayerListBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.HeroRankFightPlayerDataListMsg fightPlayerList = 2;</code>
     */
    public xddq.pb.HeroRankFightPlayerDataListMsg.Builder getFightPlayerListBuilder() {
      bitField0_ |= 0x00000002;
      onChanged();
      return internalGetFightPlayerListFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.HeroRankFightPlayerDataListMsg fightPlayerList = 2;</code>
     */
    public xddq.pb.HeroRankFightPlayerDataListMsgOrBuilder getFightPlayerListOrBuilder() {
      if (fightPlayerListBuilder_ != null) {
        return fightPlayerListBuilder_.getMessageOrBuilder();
      } else {
        return fightPlayerList_ == null ?
            xddq.pb.HeroRankFightPlayerDataListMsg.getDefaultInstance() : fightPlayerList_;
      }
    }
    /**
     * <code>optional .xddq.pb.HeroRankFightPlayerDataListMsg fightPlayerList = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.HeroRankFightPlayerDataListMsg, xddq.pb.HeroRankFightPlayerDataListMsg.Builder, xddq.pb.HeroRankFightPlayerDataListMsgOrBuilder> 
        internalGetFightPlayerListFieldBuilder() {
      if (fightPlayerListBuilder_ == null) {
        fightPlayerListBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.HeroRankFightPlayerDataListMsg, xddq.pb.HeroRankFightPlayerDataListMsg.Builder, xddq.pb.HeroRankFightPlayerDataListMsgOrBuilder>(
                getFightPlayerList(),
                getParentForChildren(),
                isClean());
        fightPlayerList_ = null;
      }
      return fightPlayerListBuilder_;
    }

    private int type_ ;
    /**
     * <code>optional int32 type = 3;</code>
     * @return Whether the type field is set.
     */
    @java.lang.Override
    public boolean hasType() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int32 type = 3;</code>
     * @return The type.
     */
    @java.lang.Override
    public int getType() {
      return type_;
    }
    /**
     * <code>optional int32 type = 3;</code>
     * @param value The type to set.
     * @return This builder for chaining.
     */
    public Builder setType(int value) {

      type_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 type = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearType() {
      bitField0_ = (bitField0_ & ~0x00000004);
      type_ = 0;
      onChanged();
      return this;
    }

    private int rank_ ;
    /**
     * <code>optional int32 rank = 4;</code>
     * @return Whether the rank field is set.
     */
    @java.lang.Override
    public boolean hasRank() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int32 rank = 4;</code>
     * @return The rank.
     */
    @java.lang.Override
    public int getRank() {
      return rank_;
    }
    /**
     * <code>optional int32 rank = 4;</code>
     * @param value The rank to set.
     * @return This builder for chaining.
     */
    public Builder setRank(int value) {

      rank_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 rank = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearRank() {
      bitField0_ = (bitField0_ & ~0x00000008);
      rank_ = 0;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.RspHeroRankFightPlayerList)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.RspHeroRankFightPlayerList)
  private static final xddq.pb.RspHeroRankFightPlayerList DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.RspHeroRankFightPlayerList();
  }

  public static xddq.pb.RspHeroRankFightPlayerList getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<RspHeroRankFightPlayerList>
      PARSER = new com.google.protobuf.AbstractParser<RspHeroRankFightPlayerList>() {
    @java.lang.Override
    public RspHeroRankFightPlayerList parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<RspHeroRankFightPlayerList> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<RspHeroRankFightPlayerList> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.RspHeroRankFightPlayerList getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

