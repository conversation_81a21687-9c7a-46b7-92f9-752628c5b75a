// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

public interface WarSeasonMoveCapitalReqOrBuilder extends
    // @@protoc_insertion_point(interface_extends:xddq.pb.WarSeasonMoveCapitalReq)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>optional int64 groupId = 1;</code>
   * @return Whether the groupId field is set.
   */
  boolean hasGroupId();
  /**
   * <code>optional int64 groupId = 1;</code>
   * @return The groupId.
   */
  long getGroupId();

  /**
   * <code>optional int32 targetCityId = 2;</code>
   * @return Whether the targetCityId field is set.
   */
  boolean hasTargetCityId();
  /**
   * <code>optional int32 targetCityId = 2;</code>
   * @return The targetCityId.
   */
  int getTargetCityId();
}
