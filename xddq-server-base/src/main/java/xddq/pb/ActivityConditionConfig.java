// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.ActivityConditionConfig}
 */
public final class ActivityConditionConfig extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.ActivityConditionConfig)
    ActivityConditionConfigOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      ActivityConditionConfig.class.getName());
  }
  // Use ActivityConditionConfig.newBuilder() to construct.
  private ActivityConditionConfig(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private ActivityConditionConfig() {
    title_ = "";
    desc_ = "";
    rewards_ = "";
    masterRewards_ = "";
    param_ =
        com.google.protobuf.LazyStringArrayList.emptyList();
    extendParam_ = "";
    jumpId_ = "";
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_ActivityConditionConfig_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_ActivityConditionConfig_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.ActivityConditionConfig.class, xddq.pb.ActivityConditionConfig.Builder.class);
  }

  private int bitField0_;
  public static final int ACTIVITYID_FIELD_NUMBER = 1;
  private int activityId_ = 0;
  /**
   * <code>optional int32 activityId = 1;</code>
   * @return Whether the activityId field is set.
   */
  @java.lang.Override
  public boolean hasActivityId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int32 activityId = 1;</code>
   * @return The activityId.
   */
  @java.lang.Override
  public int getActivityId() {
    return activityId_;
  }

  public static final int CONDITIONID_FIELD_NUMBER = 2;
  private int conditionId_ = 0;
  /**
   * <code>optional int32 conditionId = 2;</code>
   * @return Whether the conditionId field is set.
   */
  @java.lang.Override
  public boolean hasConditionId() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int32 conditionId = 2;</code>
   * @return The conditionId.
   */
  @java.lang.Override
  public int getConditionId() {
    return conditionId_;
  }

  public static final int TITLE_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private volatile java.lang.Object title_ = "";
  /**
   * <code>optional string title = 3;</code>
   * @return Whether the title field is set.
   */
  @java.lang.Override
  public boolean hasTitle() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional string title = 3;</code>
   * @return The title.
   */
  @java.lang.Override
  public java.lang.String getTitle() {
    java.lang.Object ref = title_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        title_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string title = 3;</code>
   * @return The bytes for title.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getTitleBytes() {
    java.lang.Object ref = title_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      title_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int DESC_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private volatile java.lang.Object desc_ = "";
  /**
   * <code>optional string desc = 4;</code>
   * @return Whether the desc field is set.
   */
  @java.lang.Override
  public boolean hasDesc() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional string desc = 4;</code>
   * @return The desc.
   */
  @java.lang.Override
  public java.lang.String getDesc() {
    java.lang.Object ref = desc_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        desc_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string desc = 4;</code>
   * @return The bytes for desc.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getDescBytes() {
    java.lang.Object ref = desc_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      desc_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int TYPE_FIELD_NUMBER = 5;
  private int type_ = 0;
  /**
   * <code>optional int32 type = 5;</code>
   * @return Whether the type field is set.
   */
  @java.lang.Override
  public boolean hasType() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>optional int32 type = 5;</code>
   * @return The type.
   */
  @java.lang.Override
  public int getType() {
    return type_;
  }

  public static final int REWARDS_FIELD_NUMBER = 6;
  @SuppressWarnings("serial")
  private volatile java.lang.Object rewards_ = "";
  /**
   * <code>optional string rewards = 6;</code>
   * @return Whether the rewards field is set.
   */
  @java.lang.Override
  public boolean hasRewards() {
    return ((bitField0_ & 0x00000020) != 0);
  }
  /**
   * <code>optional string rewards = 6;</code>
   * @return The rewards.
   */
  @java.lang.Override
  public java.lang.String getRewards() {
    java.lang.Object ref = rewards_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        rewards_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string rewards = 6;</code>
   * @return The bytes for rewards.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getRewardsBytes() {
    java.lang.Object ref = rewards_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      rewards_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int MASTERREWARDS_FIELD_NUMBER = 7;
  @SuppressWarnings("serial")
  private volatile java.lang.Object masterRewards_ = "";
  /**
   * <code>optional string masterRewards = 7;</code>
   * @return Whether the masterRewards field is set.
   */
  @java.lang.Override
  public boolean hasMasterRewards() {
    return ((bitField0_ & 0x00000040) != 0);
  }
  /**
   * <code>optional string masterRewards = 7;</code>
   * @return The masterRewards.
   */
  @java.lang.Override
  public java.lang.String getMasterRewards() {
    java.lang.Object ref = masterRewards_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        masterRewards_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string masterRewards = 7;</code>
   * @return The bytes for masterRewards.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getMasterRewardsBytes() {
    java.lang.Object ref = masterRewards_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      masterRewards_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PARAM_FIELD_NUMBER = 8;
  @SuppressWarnings("serial")
  private com.google.protobuf.LazyStringArrayList param_ =
      com.google.protobuf.LazyStringArrayList.emptyList();
  /**
   * <code>repeated string param = 8;</code>
   * @return A list containing the param.
   */
  public com.google.protobuf.ProtocolStringList
      getParamList() {
    return param_;
  }
  /**
   * <code>repeated string param = 8;</code>
   * @return The count of param.
   */
  public int getParamCount() {
    return param_.size();
  }
  /**
   * <code>repeated string param = 8;</code>
   * @param index The index of the element to return.
   * @return The param at the given index.
   */
  public java.lang.String getParam(int index) {
    return param_.get(index);
  }
  /**
   * <code>repeated string param = 8;</code>
   * @param index The index of the value to return.
   * @return The bytes of the param at the given index.
   */
  public com.google.protobuf.ByteString
      getParamBytes(int index) {
    return param_.getByteString(index);
  }

  public static final int EXTENDPARAM_FIELD_NUMBER = 9;
  @SuppressWarnings("serial")
  private volatile java.lang.Object extendParam_ = "";
  /**
   * <code>optional string extendParam = 9;</code>
   * @return Whether the extendParam field is set.
   */
  @java.lang.Override
  public boolean hasExtendParam() {
    return ((bitField0_ & 0x00000080) != 0);
  }
  /**
   * <code>optional string extendParam = 9;</code>
   * @return The extendParam.
   */
  @java.lang.Override
  public java.lang.String getExtendParam() {
    java.lang.Object ref = extendParam_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        extendParam_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string extendParam = 9;</code>
   * @return The bytes for extendParam.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getExtendParamBytes() {
    java.lang.Object ref = extendParam_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      extendParam_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int JUMPID_FIELD_NUMBER = 10;
  @SuppressWarnings("serial")
  private volatile java.lang.Object jumpId_ = "";
  /**
   * <code>optional string jumpId = 10;</code>
   * @return Whether the jumpId field is set.
   */
  @java.lang.Override
  public boolean hasJumpId() {
    return ((bitField0_ & 0x00000100) != 0);
  }
  /**
   * <code>optional string jumpId = 10;</code>
   * @return The jumpId.
   */
  @java.lang.Override
  public java.lang.String getJumpId() {
    java.lang.Object ref = jumpId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        jumpId_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string jumpId = 10;</code>
   * @return The bytes for jumpId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getJumpIdBytes() {
    java.lang.Object ref = jumpId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      jumpId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int REFRESH_FIELD_NUMBER = 11;
  private int refresh_ = 0;
  /**
   * <code>optional int32 refresh = 11;</code>
   * @return Whether the refresh field is set.
   */
  @java.lang.Override
  public boolean hasRefresh() {
    return ((bitField0_ & 0x00000200) != 0);
  }
  /**
   * <code>optional int32 refresh = 11;</code>
   * @return The refresh.
   */
  @java.lang.Override
  public int getRefresh() {
    return refresh_;
  }

  public static final int TOPIC_FIELD_NUMBER = 12;
  private int topic_ = 0;
  /**
   * <code>optional int32 topic = 12;</code>
   * @return Whether the topic field is set.
   */
  @java.lang.Override
  public boolean hasTopic() {
    return ((bitField0_ & 0x00000400) != 0);
  }
  /**
   * <code>optional int32 topic = 12;</code>
   * @return The topic.
   */
  @java.lang.Override
  public int getTopic() {
    return topic_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, activityId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(2, conditionId_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 3, title_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 4, desc_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      output.writeInt32(5, type_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 6, rewards_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 7, masterRewards_);
    }
    for (int i = 0; i < param_.size(); i++) {
      com.google.protobuf.GeneratedMessage.writeString(output, 8, param_.getRaw(i));
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 9, extendParam_);
    }
    if (((bitField0_ & 0x00000100) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 10, jumpId_);
    }
    if (((bitField0_ & 0x00000200) != 0)) {
      output.writeInt32(11, refresh_);
    }
    if (((bitField0_ & 0x00000400) != 0)) {
      output.writeInt32(12, topic_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, activityId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, conditionId_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(3, title_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(4, desc_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(5, type_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(6, rewards_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(7, masterRewards_);
    }
    {
      int dataSize = 0;
      for (int i = 0; i < param_.size(); i++) {
        dataSize += computeStringSizeNoTag(param_.getRaw(i));
      }
      size += dataSize;
      size += 1 * getParamList().size();
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(9, extendParam_);
    }
    if (((bitField0_ & 0x00000100) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(10, jumpId_);
    }
    if (((bitField0_ & 0x00000200) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(11, refresh_);
    }
    if (((bitField0_ & 0x00000400) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(12, topic_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.ActivityConditionConfig)) {
      return super.equals(obj);
    }
    xddq.pb.ActivityConditionConfig other = (xddq.pb.ActivityConditionConfig) obj;

    if (hasActivityId() != other.hasActivityId()) return false;
    if (hasActivityId()) {
      if (getActivityId()
          != other.getActivityId()) return false;
    }
    if (hasConditionId() != other.hasConditionId()) return false;
    if (hasConditionId()) {
      if (getConditionId()
          != other.getConditionId()) return false;
    }
    if (hasTitle() != other.hasTitle()) return false;
    if (hasTitle()) {
      if (!getTitle()
          .equals(other.getTitle())) return false;
    }
    if (hasDesc() != other.hasDesc()) return false;
    if (hasDesc()) {
      if (!getDesc()
          .equals(other.getDesc())) return false;
    }
    if (hasType() != other.hasType()) return false;
    if (hasType()) {
      if (getType()
          != other.getType()) return false;
    }
    if (hasRewards() != other.hasRewards()) return false;
    if (hasRewards()) {
      if (!getRewards()
          .equals(other.getRewards())) return false;
    }
    if (hasMasterRewards() != other.hasMasterRewards()) return false;
    if (hasMasterRewards()) {
      if (!getMasterRewards()
          .equals(other.getMasterRewards())) return false;
    }
    if (!getParamList()
        .equals(other.getParamList())) return false;
    if (hasExtendParam() != other.hasExtendParam()) return false;
    if (hasExtendParam()) {
      if (!getExtendParam()
          .equals(other.getExtendParam())) return false;
    }
    if (hasJumpId() != other.hasJumpId()) return false;
    if (hasJumpId()) {
      if (!getJumpId()
          .equals(other.getJumpId())) return false;
    }
    if (hasRefresh() != other.hasRefresh()) return false;
    if (hasRefresh()) {
      if (getRefresh()
          != other.getRefresh()) return false;
    }
    if (hasTopic() != other.hasTopic()) return false;
    if (hasTopic()) {
      if (getTopic()
          != other.getTopic()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasActivityId()) {
      hash = (37 * hash) + ACTIVITYID_FIELD_NUMBER;
      hash = (53 * hash) + getActivityId();
    }
    if (hasConditionId()) {
      hash = (37 * hash) + CONDITIONID_FIELD_NUMBER;
      hash = (53 * hash) + getConditionId();
    }
    if (hasTitle()) {
      hash = (37 * hash) + TITLE_FIELD_NUMBER;
      hash = (53 * hash) + getTitle().hashCode();
    }
    if (hasDesc()) {
      hash = (37 * hash) + DESC_FIELD_NUMBER;
      hash = (53 * hash) + getDesc().hashCode();
    }
    if (hasType()) {
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
    }
    if (hasRewards()) {
      hash = (37 * hash) + REWARDS_FIELD_NUMBER;
      hash = (53 * hash) + getRewards().hashCode();
    }
    if (hasMasterRewards()) {
      hash = (37 * hash) + MASTERREWARDS_FIELD_NUMBER;
      hash = (53 * hash) + getMasterRewards().hashCode();
    }
    if (getParamCount() > 0) {
      hash = (37 * hash) + PARAM_FIELD_NUMBER;
      hash = (53 * hash) + getParamList().hashCode();
    }
    if (hasExtendParam()) {
      hash = (37 * hash) + EXTENDPARAM_FIELD_NUMBER;
      hash = (53 * hash) + getExtendParam().hashCode();
    }
    if (hasJumpId()) {
      hash = (37 * hash) + JUMPID_FIELD_NUMBER;
      hash = (53 * hash) + getJumpId().hashCode();
    }
    if (hasRefresh()) {
      hash = (37 * hash) + REFRESH_FIELD_NUMBER;
      hash = (53 * hash) + getRefresh();
    }
    if (hasTopic()) {
      hash = (37 * hash) + TOPIC_FIELD_NUMBER;
      hash = (53 * hash) + getTopic();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.ActivityConditionConfig parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ActivityConditionConfig parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ActivityConditionConfig parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ActivityConditionConfig parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ActivityConditionConfig parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ActivityConditionConfig parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ActivityConditionConfig parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.ActivityConditionConfig parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.ActivityConditionConfig parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.ActivityConditionConfig parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.ActivityConditionConfig parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.ActivityConditionConfig parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.ActivityConditionConfig prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.ActivityConditionConfig}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.ActivityConditionConfig)
      xddq.pb.ActivityConditionConfigOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ActivityConditionConfig_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ActivityConditionConfig_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.ActivityConditionConfig.class, xddq.pb.ActivityConditionConfig.Builder.class);
    }

    // Construct using xddq.pb.ActivityConditionConfig.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      activityId_ = 0;
      conditionId_ = 0;
      title_ = "";
      desc_ = "";
      type_ = 0;
      rewards_ = "";
      masterRewards_ = "";
      param_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      extendParam_ = "";
      jumpId_ = "";
      refresh_ = 0;
      topic_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ActivityConditionConfig_descriptor;
    }

    @java.lang.Override
    public xddq.pb.ActivityConditionConfig getDefaultInstanceForType() {
      return xddq.pb.ActivityConditionConfig.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.ActivityConditionConfig build() {
      xddq.pb.ActivityConditionConfig result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.ActivityConditionConfig buildPartial() {
      xddq.pb.ActivityConditionConfig result = new xddq.pb.ActivityConditionConfig(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.ActivityConditionConfig result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.activityId_ = activityId_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.conditionId_ = conditionId_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.title_ = title_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.desc_ = desc_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.type_ = type_;
        to_bitField0_ |= 0x00000010;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.rewards_ = rewards_;
        to_bitField0_ |= 0x00000020;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.masterRewards_ = masterRewards_;
        to_bitField0_ |= 0x00000040;
      }
      if (((from_bitField0_ & 0x00000080) != 0)) {
        param_.makeImmutable();
        result.param_ = param_;
      }
      if (((from_bitField0_ & 0x00000100) != 0)) {
        result.extendParam_ = extendParam_;
        to_bitField0_ |= 0x00000080;
      }
      if (((from_bitField0_ & 0x00000200) != 0)) {
        result.jumpId_ = jumpId_;
        to_bitField0_ |= 0x00000100;
      }
      if (((from_bitField0_ & 0x00000400) != 0)) {
        result.refresh_ = refresh_;
        to_bitField0_ |= 0x00000200;
      }
      if (((from_bitField0_ & 0x00000800) != 0)) {
        result.topic_ = topic_;
        to_bitField0_ |= 0x00000400;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.ActivityConditionConfig) {
        return mergeFrom((xddq.pb.ActivityConditionConfig)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.ActivityConditionConfig other) {
      if (other == xddq.pb.ActivityConditionConfig.getDefaultInstance()) return this;
      if (other.hasActivityId()) {
        setActivityId(other.getActivityId());
      }
      if (other.hasConditionId()) {
        setConditionId(other.getConditionId());
      }
      if (other.hasTitle()) {
        title_ = other.title_;
        bitField0_ |= 0x00000004;
        onChanged();
      }
      if (other.hasDesc()) {
        desc_ = other.desc_;
        bitField0_ |= 0x00000008;
        onChanged();
      }
      if (other.hasType()) {
        setType(other.getType());
      }
      if (other.hasRewards()) {
        rewards_ = other.rewards_;
        bitField0_ |= 0x00000020;
        onChanged();
      }
      if (other.hasMasterRewards()) {
        masterRewards_ = other.masterRewards_;
        bitField0_ |= 0x00000040;
        onChanged();
      }
      if (!other.param_.isEmpty()) {
        if (param_.isEmpty()) {
          param_ = other.param_;
          bitField0_ |= 0x00000080;
        } else {
          ensureParamIsMutable();
          param_.addAll(other.param_);
        }
        onChanged();
      }
      if (other.hasExtendParam()) {
        extendParam_ = other.extendParam_;
        bitField0_ |= 0x00000100;
        onChanged();
      }
      if (other.hasJumpId()) {
        jumpId_ = other.jumpId_;
        bitField0_ |= 0x00000200;
        onChanged();
      }
      if (other.hasRefresh()) {
        setRefresh(other.getRefresh());
      }
      if (other.hasTopic()) {
        setTopic(other.getTopic());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              activityId_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              conditionId_ = input.readInt32();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 26: {
              title_ = input.readBytes();
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 34: {
              desc_ = input.readBytes();
              bitField0_ |= 0x00000008;
              break;
            } // case 34
            case 40: {
              type_ = input.readInt32();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            case 50: {
              rewards_ = input.readBytes();
              bitField0_ |= 0x00000020;
              break;
            } // case 50
            case 58: {
              masterRewards_ = input.readBytes();
              bitField0_ |= 0x00000040;
              break;
            } // case 58
            case 66: {
              com.google.protobuf.ByteString bs = input.readBytes();
              ensureParamIsMutable();
              param_.add(bs);
              break;
            } // case 66
            case 74: {
              extendParam_ = input.readBytes();
              bitField0_ |= 0x00000100;
              break;
            } // case 74
            case 82: {
              jumpId_ = input.readBytes();
              bitField0_ |= 0x00000200;
              break;
            } // case 82
            case 88: {
              refresh_ = input.readInt32();
              bitField0_ |= 0x00000400;
              break;
            } // case 88
            case 96: {
              topic_ = input.readInt32();
              bitField0_ |= 0x00000800;
              break;
            } // case 96
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int activityId_ ;
    /**
     * <code>optional int32 activityId = 1;</code>
     * @return Whether the activityId field is set.
     */
    @java.lang.Override
    public boolean hasActivityId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 activityId = 1;</code>
     * @return The activityId.
     */
    @java.lang.Override
    public int getActivityId() {
      return activityId_;
    }
    /**
     * <code>optional int32 activityId = 1;</code>
     * @param value The activityId to set.
     * @return This builder for chaining.
     */
    public Builder setActivityId(int value) {

      activityId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 activityId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearActivityId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      activityId_ = 0;
      onChanged();
      return this;
    }

    private int conditionId_ ;
    /**
     * <code>optional int32 conditionId = 2;</code>
     * @return Whether the conditionId field is set.
     */
    @java.lang.Override
    public boolean hasConditionId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 conditionId = 2;</code>
     * @return The conditionId.
     */
    @java.lang.Override
    public int getConditionId() {
      return conditionId_;
    }
    /**
     * <code>optional int32 conditionId = 2;</code>
     * @param value The conditionId to set.
     * @return This builder for chaining.
     */
    public Builder setConditionId(int value) {

      conditionId_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 conditionId = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearConditionId() {
      bitField0_ = (bitField0_ & ~0x00000002);
      conditionId_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object title_ = "";
    /**
     * <code>optional string title = 3;</code>
     * @return Whether the title field is set.
     */
    public boolean hasTitle() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional string title = 3;</code>
     * @return The title.
     */
    public java.lang.String getTitle() {
      java.lang.Object ref = title_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          title_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string title = 3;</code>
     * @return The bytes for title.
     */
    public com.google.protobuf.ByteString
        getTitleBytes() {
      java.lang.Object ref = title_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        title_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string title = 3;</code>
     * @param value The title to set.
     * @return This builder for chaining.
     */
    public Builder setTitle(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      title_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional string title = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearTitle() {
      title_ = getDefaultInstance().getTitle();
      bitField0_ = (bitField0_ & ~0x00000004);
      onChanged();
      return this;
    }
    /**
     * <code>optional string title = 3;</code>
     * @param value The bytes for title to set.
     * @return This builder for chaining.
     */
    public Builder setTitleBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      title_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }

    private java.lang.Object desc_ = "";
    /**
     * <code>optional string desc = 4;</code>
     * @return Whether the desc field is set.
     */
    public boolean hasDesc() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional string desc = 4;</code>
     * @return The desc.
     */
    public java.lang.String getDesc() {
      java.lang.Object ref = desc_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          desc_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string desc = 4;</code>
     * @return The bytes for desc.
     */
    public com.google.protobuf.ByteString
        getDescBytes() {
      java.lang.Object ref = desc_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        desc_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string desc = 4;</code>
     * @param value The desc to set.
     * @return This builder for chaining.
     */
    public Builder setDesc(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      desc_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional string desc = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearDesc() {
      desc_ = getDefaultInstance().getDesc();
      bitField0_ = (bitField0_ & ~0x00000008);
      onChanged();
      return this;
    }
    /**
     * <code>optional string desc = 4;</code>
     * @param value The bytes for desc to set.
     * @return This builder for chaining.
     */
    public Builder setDescBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      desc_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }

    private int type_ ;
    /**
     * <code>optional int32 type = 5;</code>
     * @return Whether the type field is set.
     */
    @java.lang.Override
    public boolean hasType() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional int32 type = 5;</code>
     * @return The type.
     */
    @java.lang.Override
    public int getType() {
      return type_;
    }
    /**
     * <code>optional int32 type = 5;</code>
     * @param value The type to set.
     * @return This builder for chaining.
     */
    public Builder setType(int value) {

      type_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 type = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearType() {
      bitField0_ = (bitField0_ & ~0x00000010);
      type_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object rewards_ = "";
    /**
     * <code>optional string rewards = 6;</code>
     * @return Whether the rewards field is set.
     */
    public boolean hasRewards() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional string rewards = 6;</code>
     * @return The rewards.
     */
    public java.lang.String getRewards() {
      java.lang.Object ref = rewards_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          rewards_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string rewards = 6;</code>
     * @return The bytes for rewards.
     */
    public com.google.protobuf.ByteString
        getRewardsBytes() {
      java.lang.Object ref = rewards_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        rewards_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string rewards = 6;</code>
     * @param value The rewards to set.
     * @return This builder for chaining.
     */
    public Builder setRewards(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      rewards_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>optional string rewards = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearRewards() {
      rewards_ = getDefaultInstance().getRewards();
      bitField0_ = (bitField0_ & ~0x00000020);
      onChanged();
      return this;
    }
    /**
     * <code>optional string rewards = 6;</code>
     * @param value The bytes for rewards to set.
     * @return This builder for chaining.
     */
    public Builder setRewardsBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      rewards_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }

    private java.lang.Object masterRewards_ = "";
    /**
     * <code>optional string masterRewards = 7;</code>
     * @return Whether the masterRewards field is set.
     */
    public boolean hasMasterRewards() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <code>optional string masterRewards = 7;</code>
     * @return The masterRewards.
     */
    public java.lang.String getMasterRewards() {
      java.lang.Object ref = masterRewards_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          masterRewards_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string masterRewards = 7;</code>
     * @return The bytes for masterRewards.
     */
    public com.google.protobuf.ByteString
        getMasterRewardsBytes() {
      java.lang.Object ref = masterRewards_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        masterRewards_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string masterRewards = 7;</code>
     * @param value The masterRewards to set.
     * @return This builder for chaining.
     */
    public Builder setMasterRewards(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      masterRewards_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>optional string masterRewards = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearMasterRewards() {
      masterRewards_ = getDefaultInstance().getMasterRewards();
      bitField0_ = (bitField0_ & ~0x00000040);
      onChanged();
      return this;
    }
    /**
     * <code>optional string masterRewards = 7;</code>
     * @param value The bytes for masterRewards to set.
     * @return This builder for chaining.
     */
    public Builder setMasterRewardsBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      masterRewards_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }

    private com.google.protobuf.LazyStringArrayList param_ =
        com.google.protobuf.LazyStringArrayList.emptyList();
    private void ensureParamIsMutable() {
      if (!param_.isModifiable()) {
        param_ = new com.google.protobuf.LazyStringArrayList(param_);
      }
      bitField0_ |= 0x00000080;
    }
    /**
     * <code>repeated string param = 8;</code>
     * @return A list containing the param.
     */
    public com.google.protobuf.ProtocolStringList
        getParamList() {
      param_.makeImmutable();
      return param_;
    }
    /**
     * <code>repeated string param = 8;</code>
     * @return The count of param.
     */
    public int getParamCount() {
      return param_.size();
    }
    /**
     * <code>repeated string param = 8;</code>
     * @param index The index of the element to return.
     * @return The param at the given index.
     */
    public java.lang.String getParam(int index) {
      return param_.get(index);
    }
    /**
     * <code>repeated string param = 8;</code>
     * @param index The index of the value to return.
     * @return The bytes of the param at the given index.
     */
    public com.google.protobuf.ByteString
        getParamBytes(int index) {
      return param_.getByteString(index);
    }
    /**
     * <code>repeated string param = 8;</code>
     * @param index The index to set the value at.
     * @param value The param to set.
     * @return This builder for chaining.
     */
    public Builder setParam(
        int index, java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      ensureParamIsMutable();
      param_.set(index, value);
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }
    /**
     * <code>repeated string param = 8;</code>
     * @param value The param to add.
     * @return This builder for chaining.
     */
    public Builder addParam(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      ensureParamIsMutable();
      param_.add(value);
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }
    /**
     * <code>repeated string param = 8;</code>
     * @param values The param to add.
     * @return This builder for chaining.
     */
    public Builder addAllParam(
        java.lang.Iterable<java.lang.String> values) {
      ensureParamIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, param_);
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }
    /**
     * <code>repeated string param = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearParam() {
      param_ =
        com.google.protobuf.LazyStringArrayList.emptyList();
      bitField0_ = (bitField0_ & ~0x00000080);;
      onChanged();
      return this;
    }
    /**
     * <code>repeated string param = 8;</code>
     * @param value The bytes of the param to add.
     * @return This builder for chaining.
     */
    public Builder addParamBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      ensureParamIsMutable();
      param_.add(value);
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }

    private java.lang.Object extendParam_ = "";
    /**
     * <code>optional string extendParam = 9;</code>
     * @return Whether the extendParam field is set.
     */
    public boolean hasExtendParam() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <code>optional string extendParam = 9;</code>
     * @return The extendParam.
     */
    public java.lang.String getExtendParam() {
      java.lang.Object ref = extendParam_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          extendParam_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string extendParam = 9;</code>
     * @return The bytes for extendParam.
     */
    public com.google.protobuf.ByteString
        getExtendParamBytes() {
      java.lang.Object ref = extendParam_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        extendParam_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string extendParam = 9;</code>
     * @param value The extendParam to set.
     * @return This builder for chaining.
     */
    public Builder setExtendParam(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      extendParam_ = value;
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }
    /**
     * <code>optional string extendParam = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearExtendParam() {
      extendParam_ = getDefaultInstance().getExtendParam();
      bitField0_ = (bitField0_ & ~0x00000100);
      onChanged();
      return this;
    }
    /**
     * <code>optional string extendParam = 9;</code>
     * @param value The bytes for extendParam to set.
     * @return This builder for chaining.
     */
    public Builder setExtendParamBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      extendParam_ = value;
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }

    private java.lang.Object jumpId_ = "";
    /**
     * <code>optional string jumpId = 10;</code>
     * @return Whether the jumpId field is set.
     */
    public boolean hasJumpId() {
      return ((bitField0_ & 0x00000200) != 0);
    }
    /**
     * <code>optional string jumpId = 10;</code>
     * @return The jumpId.
     */
    public java.lang.String getJumpId() {
      java.lang.Object ref = jumpId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          jumpId_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string jumpId = 10;</code>
     * @return The bytes for jumpId.
     */
    public com.google.protobuf.ByteString
        getJumpIdBytes() {
      java.lang.Object ref = jumpId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        jumpId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string jumpId = 10;</code>
     * @param value The jumpId to set.
     * @return This builder for chaining.
     */
    public Builder setJumpId(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      jumpId_ = value;
      bitField0_ |= 0x00000200;
      onChanged();
      return this;
    }
    /**
     * <code>optional string jumpId = 10;</code>
     * @return This builder for chaining.
     */
    public Builder clearJumpId() {
      jumpId_ = getDefaultInstance().getJumpId();
      bitField0_ = (bitField0_ & ~0x00000200);
      onChanged();
      return this;
    }
    /**
     * <code>optional string jumpId = 10;</code>
     * @param value The bytes for jumpId to set.
     * @return This builder for chaining.
     */
    public Builder setJumpIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      jumpId_ = value;
      bitField0_ |= 0x00000200;
      onChanged();
      return this;
    }

    private int refresh_ ;
    /**
     * <code>optional int32 refresh = 11;</code>
     * @return Whether the refresh field is set.
     */
    @java.lang.Override
    public boolean hasRefresh() {
      return ((bitField0_ & 0x00000400) != 0);
    }
    /**
     * <code>optional int32 refresh = 11;</code>
     * @return The refresh.
     */
    @java.lang.Override
    public int getRefresh() {
      return refresh_;
    }
    /**
     * <code>optional int32 refresh = 11;</code>
     * @param value The refresh to set.
     * @return This builder for chaining.
     */
    public Builder setRefresh(int value) {

      refresh_ = value;
      bitField0_ |= 0x00000400;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 refresh = 11;</code>
     * @return This builder for chaining.
     */
    public Builder clearRefresh() {
      bitField0_ = (bitField0_ & ~0x00000400);
      refresh_ = 0;
      onChanged();
      return this;
    }

    private int topic_ ;
    /**
     * <code>optional int32 topic = 12;</code>
     * @return Whether the topic field is set.
     */
    @java.lang.Override
    public boolean hasTopic() {
      return ((bitField0_ & 0x00000800) != 0);
    }
    /**
     * <code>optional int32 topic = 12;</code>
     * @return The topic.
     */
    @java.lang.Override
    public int getTopic() {
      return topic_;
    }
    /**
     * <code>optional int32 topic = 12;</code>
     * @param value The topic to set.
     * @return This builder for chaining.
     */
    public Builder setTopic(int value) {

      topic_ = value;
      bitField0_ |= 0x00000800;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 topic = 12;</code>
     * @return This builder for chaining.
     */
    public Builder clearTopic() {
      bitField0_ = (bitField0_ & ~0x00000800);
      topic_ = 0;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.ActivityConditionConfig)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.ActivityConditionConfig)
  private static final xddq.pb.ActivityConditionConfig DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.ActivityConditionConfig();
  }

  public static xddq.pb.ActivityConditionConfig getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ActivityConditionConfig>
      PARSER = new com.google.protobuf.AbstractParser<ActivityConditionConfig>() {
    @java.lang.Override
    public ActivityConditionConfig parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<ActivityConditionConfig> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ActivityConditionConfig> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.ActivityConditionConfig getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

