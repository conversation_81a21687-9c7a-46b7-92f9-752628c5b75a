// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.BallGVGPlaceSimpleTemp}
 */
public final class BallGVGPlaceSimpleTemp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.BallGVGPlaceSimpleTemp)
    BallGVGPlaceSimpleTempOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      BallGVGPlaceSimpleTemp.class.getName());
  }
  // Use BallGVGPlaceSimpleTemp.newBuilder() to construct.
  private BallGVGPlaceSimpleTemp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private BallGVGPlaceSimpleTemp() {
    campMember_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_BallGVGPlaceSimpleTemp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_BallGVGPlaceSimpleTemp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.BallGVGPlaceSimpleTemp.class, xddq.pb.BallGVGPlaceSimpleTemp.Builder.class);
  }

  private int bitField0_;
  public static final int PLACEID_FIELD_NUMBER = 1;
  private int placeId_ = 0;
  /**
   * <code>required int32 placeId = 1;</code>
   * @return Whether the placeId field is set.
   */
  @java.lang.Override
  public boolean hasPlaceId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 placeId = 1;</code>
   * @return The placeId.
   */
  @java.lang.Override
  public int getPlaceId() {
    return placeId_;
  }

  public static final int SEIZECAMPID_FIELD_NUMBER = 2;
  private int seizeCampId_ = 0;
  /**
   * <code>required int32 seizeCampId = 2;</code>
   * @return Whether the seizeCampId field is set.
   */
  @java.lang.Override
  public boolean hasSeizeCampId() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>required int32 seizeCampId = 2;</code>
   * @return The seizeCampId.
   */
  @java.lang.Override
  public int getSeizeCampId() {
    return seizeCampId_;
  }

  public static final int CAMPMEMBER_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.BallGVGPlaceCampMemberTemp> campMember_;
  /**
   * <code>repeated .xddq.pb.BallGVGPlaceCampMemberTemp campMember = 3;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.BallGVGPlaceCampMemberTemp> getCampMemberList() {
    return campMember_;
  }
  /**
   * <code>repeated .xddq.pb.BallGVGPlaceCampMemberTemp campMember = 3;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.BallGVGPlaceCampMemberTempOrBuilder> 
      getCampMemberOrBuilderList() {
    return campMember_;
  }
  /**
   * <code>repeated .xddq.pb.BallGVGPlaceCampMemberTemp campMember = 3;</code>
   */
  @java.lang.Override
  public int getCampMemberCount() {
    return campMember_.size();
  }
  /**
   * <code>repeated .xddq.pb.BallGVGPlaceCampMemberTemp campMember = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.BallGVGPlaceCampMemberTemp getCampMember(int index) {
    return campMember_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.BallGVGPlaceCampMemberTemp campMember = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.BallGVGPlaceCampMemberTempOrBuilder getCampMemberOrBuilder(
      int index) {
    return campMember_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasPlaceId()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasSeizeCampId()) {
      memoizedIsInitialized = 0;
      return false;
    }
    for (int i = 0; i < getCampMemberCount(); i++) {
      if (!getCampMember(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, placeId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(2, seizeCampId_);
    }
    for (int i = 0; i < campMember_.size(); i++) {
      output.writeMessage(3, campMember_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, placeId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, seizeCampId_);
    }
    for (int i = 0; i < campMember_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, campMember_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.BallGVGPlaceSimpleTemp)) {
      return super.equals(obj);
    }
    xddq.pb.BallGVGPlaceSimpleTemp other = (xddq.pb.BallGVGPlaceSimpleTemp) obj;

    if (hasPlaceId() != other.hasPlaceId()) return false;
    if (hasPlaceId()) {
      if (getPlaceId()
          != other.getPlaceId()) return false;
    }
    if (hasSeizeCampId() != other.hasSeizeCampId()) return false;
    if (hasSeizeCampId()) {
      if (getSeizeCampId()
          != other.getSeizeCampId()) return false;
    }
    if (!getCampMemberList()
        .equals(other.getCampMemberList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasPlaceId()) {
      hash = (37 * hash) + PLACEID_FIELD_NUMBER;
      hash = (53 * hash) + getPlaceId();
    }
    if (hasSeizeCampId()) {
      hash = (37 * hash) + SEIZECAMPID_FIELD_NUMBER;
      hash = (53 * hash) + getSeizeCampId();
    }
    if (getCampMemberCount() > 0) {
      hash = (37 * hash) + CAMPMEMBER_FIELD_NUMBER;
      hash = (53 * hash) + getCampMemberList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.BallGVGPlaceSimpleTemp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.BallGVGPlaceSimpleTemp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.BallGVGPlaceSimpleTemp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.BallGVGPlaceSimpleTemp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.BallGVGPlaceSimpleTemp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.BallGVGPlaceSimpleTemp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.BallGVGPlaceSimpleTemp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.BallGVGPlaceSimpleTemp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.BallGVGPlaceSimpleTemp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.BallGVGPlaceSimpleTemp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.BallGVGPlaceSimpleTemp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.BallGVGPlaceSimpleTemp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.BallGVGPlaceSimpleTemp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.BallGVGPlaceSimpleTemp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.BallGVGPlaceSimpleTemp)
      xddq.pb.BallGVGPlaceSimpleTempOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_BallGVGPlaceSimpleTemp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_BallGVGPlaceSimpleTemp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.BallGVGPlaceSimpleTemp.class, xddq.pb.BallGVGPlaceSimpleTemp.Builder.class);
    }

    // Construct using xddq.pb.BallGVGPlaceSimpleTemp.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      placeId_ = 0;
      seizeCampId_ = 0;
      if (campMemberBuilder_ == null) {
        campMember_ = java.util.Collections.emptyList();
      } else {
        campMember_ = null;
        campMemberBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000004);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_BallGVGPlaceSimpleTemp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.BallGVGPlaceSimpleTemp getDefaultInstanceForType() {
      return xddq.pb.BallGVGPlaceSimpleTemp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.BallGVGPlaceSimpleTemp build() {
      xddq.pb.BallGVGPlaceSimpleTemp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.BallGVGPlaceSimpleTemp buildPartial() {
      xddq.pb.BallGVGPlaceSimpleTemp result = new xddq.pb.BallGVGPlaceSimpleTemp(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.BallGVGPlaceSimpleTemp result) {
      if (campMemberBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0)) {
          campMember_ = java.util.Collections.unmodifiableList(campMember_);
          bitField0_ = (bitField0_ & ~0x00000004);
        }
        result.campMember_ = campMember_;
      } else {
        result.campMember_ = campMemberBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.BallGVGPlaceSimpleTemp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.placeId_ = placeId_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.seizeCampId_ = seizeCampId_;
        to_bitField0_ |= 0x00000002;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.BallGVGPlaceSimpleTemp) {
        return mergeFrom((xddq.pb.BallGVGPlaceSimpleTemp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.BallGVGPlaceSimpleTemp other) {
      if (other == xddq.pb.BallGVGPlaceSimpleTemp.getDefaultInstance()) return this;
      if (other.hasPlaceId()) {
        setPlaceId(other.getPlaceId());
      }
      if (other.hasSeizeCampId()) {
        setSeizeCampId(other.getSeizeCampId());
      }
      if (campMemberBuilder_ == null) {
        if (!other.campMember_.isEmpty()) {
          if (campMember_.isEmpty()) {
            campMember_ = other.campMember_;
            bitField0_ = (bitField0_ & ~0x00000004);
          } else {
            ensureCampMemberIsMutable();
            campMember_.addAll(other.campMember_);
          }
          onChanged();
        }
      } else {
        if (!other.campMember_.isEmpty()) {
          if (campMemberBuilder_.isEmpty()) {
            campMemberBuilder_.dispose();
            campMemberBuilder_ = null;
            campMember_ = other.campMember_;
            bitField0_ = (bitField0_ & ~0x00000004);
            campMemberBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetCampMemberFieldBuilder() : null;
          } else {
            campMemberBuilder_.addAllMessages(other.campMember_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasPlaceId()) {
        return false;
      }
      if (!hasSeizeCampId()) {
        return false;
      }
      for (int i = 0; i < getCampMemberCount(); i++) {
        if (!getCampMember(i).isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              placeId_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              seizeCampId_ = input.readInt32();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 26: {
              xddq.pb.BallGVGPlaceCampMemberTemp m =
                  input.readMessage(
                      xddq.pb.BallGVGPlaceCampMemberTemp.parser(),
                      extensionRegistry);
              if (campMemberBuilder_ == null) {
                ensureCampMemberIsMutable();
                campMember_.add(m);
              } else {
                campMemberBuilder_.addMessage(m);
              }
              break;
            } // case 26
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int placeId_ ;
    /**
     * <code>required int32 placeId = 1;</code>
     * @return Whether the placeId field is set.
     */
    @java.lang.Override
    public boolean hasPlaceId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 placeId = 1;</code>
     * @return The placeId.
     */
    @java.lang.Override
    public int getPlaceId() {
      return placeId_;
    }
    /**
     * <code>required int32 placeId = 1;</code>
     * @param value The placeId to set.
     * @return This builder for chaining.
     */
    public Builder setPlaceId(int value) {

      placeId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 placeId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearPlaceId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      placeId_ = 0;
      onChanged();
      return this;
    }

    private int seizeCampId_ ;
    /**
     * <code>required int32 seizeCampId = 2;</code>
     * @return Whether the seizeCampId field is set.
     */
    @java.lang.Override
    public boolean hasSeizeCampId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>required int32 seizeCampId = 2;</code>
     * @return The seizeCampId.
     */
    @java.lang.Override
    public int getSeizeCampId() {
      return seizeCampId_;
    }
    /**
     * <code>required int32 seizeCampId = 2;</code>
     * @param value The seizeCampId to set.
     * @return This builder for chaining.
     */
    public Builder setSeizeCampId(int value) {

      seizeCampId_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 seizeCampId = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearSeizeCampId() {
      bitField0_ = (bitField0_ & ~0x00000002);
      seizeCampId_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.BallGVGPlaceCampMemberTemp> campMember_ =
      java.util.Collections.emptyList();
    private void ensureCampMemberIsMutable() {
      if (!((bitField0_ & 0x00000004) != 0)) {
        campMember_ = new java.util.ArrayList<xddq.pb.BallGVGPlaceCampMemberTemp>(campMember_);
        bitField0_ |= 0x00000004;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.BallGVGPlaceCampMemberTemp, xddq.pb.BallGVGPlaceCampMemberTemp.Builder, xddq.pb.BallGVGPlaceCampMemberTempOrBuilder> campMemberBuilder_;

    /**
     * <code>repeated .xddq.pb.BallGVGPlaceCampMemberTemp campMember = 3;</code>
     */
    public java.util.List<xddq.pb.BallGVGPlaceCampMemberTemp> getCampMemberList() {
      if (campMemberBuilder_ == null) {
        return java.util.Collections.unmodifiableList(campMember_);
      } else {
        return campMemberBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.BallGVGPlaceCampMemberTemp campMember = 3;</code>
     */
    public int getCampMemberCount() {
      if (campMemberBuilder_ == null) {
        return campMember_.size();
      } else {
        return campMemberBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.BallGVGPlaceCampMemberTemp campMember = 3;</code>
     */
    public xddq.pb.BallGVGPlaceCampMemberTemp getCampMember(int index) {
      if (campMemberBuilder_ == null) {
        return campMember_.get(index);
      } else {
        return campMemberBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.BallGVGPlaceCampMemberTemp campMember = 3;</code>
     */
    public Builder setCampMember(
        int index, xddq.pb.BallGVGPlaceCampMemberTemp value) {
      if (campMemberBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureCampMemberIsMutable();
        campMember_.set(index, value);
        onChanged();
      } else {
        campMemberBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.BallGVGPlaceCampMemberTemp campMember = 3;</code>
     */
    public Builder setCampMember(
        int index, xddq.pb.BallGVGPlaceCampMemberTemp.Builder builderForValue) {
      if (campMemberBuilder_ == null) {
        ensureCampMemberIsMutable();
        campMember_.set(index, builderForValue.build());
        onChanged();
      } else {
        campMemberBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.BallGVGPlaceCampMemberTemp campMember = 3;</code>
     */
    public Builder addCampMember(xddq.pb.BallGVGPlaceCampMemberTemp value) {
      if (campMemberBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureCampMemberIsMutable();
        campMember_.add(value);
        onChanged();
      } else {
        campMemberBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.BallGVGPlaceCampMemberTemp campMember = 3;</code>
     */
    public Builder addCampMember(
        int index, xddq.pb.BallGVGPlaceCampMemberTemp value) {
      if (campMemberBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureCampMemberIsMutable();
        campMember_.add(index, value);
        onChanged();
      } else {
        campMemberBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.BallGVGPlaceCampMemberTemp campMember = 3;</code>
     */
    public Builder addCampMember(
        xddq.pb.BallGVGPlaceCampMemberTemp.Builder builderForValue) {
      if (campMemberBuilder_ == null) {
        ensureCampMemberIsMutable();
        campMember_.add(builderForValue.build());
        onChanged();
      } else {
        campMemberBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.BallGVGPlaceCampMemberTemp campMember = 3;</code>
     */
    public Builder addCampMember(
        int index, xddq.pb.BallGVGPlaceCampMemberTemp.Builder builderForValue) {
      if (campMemberBuilder_ == null) {
        ensureCampMemberIsMutable();
        campMember_.add(index, builderForValue.build());
        onChanged();
      } else {
        campMemberBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.BallGVGPlaceCampMemberTemp campMember = 3;</code>
     */
    public Builder addAllCampMember(
        java.lang.Iterable<? extends xddq.pb.BallGVGPlaceCampMemberTemp> values) {
      if (campMemberBuilder_ == null) {
        ensureCampMemberIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, campMember_);
        onChanged();
      } else {
        campMemberBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.BallGVGPlaceCampMemberTemp campMember = 3;</code>
     */
    public Builder clearCampMember() {
      if (campMemberBuilder_ == null) {
        campMember_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
      } else {
        campMemberBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.BallGVGPlaceCampMemberTemp campMember = 3;</code>
     */
    public Builder removeCampMember(int index) {
      if (campMemberBuilder_ == null) {
        ensureCampMemberIsMutable();
        campMember_.remove(index);
        onChanged();
      } else {
        campMemberBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.BallGVGPlaceCampMemberTemp campMember = 3;</code>
     */
    public xddq.pb.BallGVGPlaceCampMemberTemp.Builder getCampMemberBuilder(
        int index) {
      return internalGetCampMemberFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.BallGVGPlaceCampMemberTemp campMember = 3;</code>
     */
    public xddq.pb.BallGVGPlaceCampMemberTempOrBuilder getCampMemberOrBuilder(
        int index) {
      if (campMemberBuilder_ == null) {
        return campMember_.get(index);  } else {
        return campMemberBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.BallGVGPlaceCampMemberTemp campMember = 3;</code>
     */
    public java.util.List<? extends xddq.pb.BallGVGPlaceCampMemberTempOrBuilder> 
         getCampMemberOrBuilderList() {
      if (campMemberBuilder_ != null) {
        return campMemberBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(campMember_);
      }
    }
    /**
     * <code>repeated .xddq.pb.BallGVGPlaceCampMemberTemp campMember = 3;</code>
     */
    public xddq.pb.BallGVGPlaceCampMemberTemp.Builder addCampMemberBuilder() {
      return internalGetCampMemberFieldBuilder().addBuilder(
          xddq.pb.BallGVGPlaceCampMemberTemp.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.BallGVGPlaceCampMemberTemp campMember = 3;</code>
     */
    public xddq.pb.BallGVGPlaceCampMemberTemp.Builder addCampMemberBuilder(
        int index) {
      return internalGetCampMemberFieldBuilder().addBuilder(
          index, xddq.pb.BallGVGPlaceCampMemberTemp.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.BallGVGPlaceCampMemberTemp campMember = 3;</code>
     */
    public java.util.List<xddq.pb.BallGVGPlaceCampMemberTemp.Builder> 
         getCampMemberBuilderList() {
      return internalGetCampMemberFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.BallGVGPlaceCampMemberTemp, xddq.pb.BallGVGPlaceCampMemberTemp.Builder, xddq.pb.BallGVGPlaceCampMemberTempOrBuilder> 
        internalGetCampMemberFieldBuilder() {
      if (campMemberBuilder_ == null) {
        campMemberBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.BallGVGPlaceCampMemberTemp, xddq.pb.BallGVGPlaceCampMemberTemp.Builder, xddq.pb.BallGVGPlaceCampMemberTempOrBuilder>(
                campMember_,
                ((bitField0_ & 0x00000004) != 0),
                getParentForChildren(),
                isClean());
        campMember_ = null;
      }
      return campMemberBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.BallGVGPlaceSimpleTemp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.BallGVGPlaceSimpleTemp)
  private static final xddq.pb.BallGVGPlaceSimpleTemp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.BallGVGPlaceSimpleTemp();
  }

  public static xddq.pb.BallGVGPlaceSimpleTemp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<BallGVGPlaceSimpleTemp>
      PARSER = new com.google.protobuf.AbstractParser<BallGVGPlaceSimpleTemp>() {
    @java.lang.Override
    public BallGVGPlaceSimpleTemp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<BallGVGPlaceSimpleTemp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<BallGVGPlaceSimpleTemp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.BallGVGPlaceSimpleTemp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

