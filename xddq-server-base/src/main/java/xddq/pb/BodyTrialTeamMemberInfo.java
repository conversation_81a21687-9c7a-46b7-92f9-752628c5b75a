// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.BodyTrialTeamMemberInfo}
 */
public final class BodyTrialTeamMemberInfo extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.BodyTrialTeamMemberInfo)
    BodyTrialTeamMemberInfoOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      BodyTrialTeamMemberInfo.class.getName());
  }
  // Use BodyTrialTeamMemberInfo.newBuilder() to construct.
  private BodyTrialTeamMemberInfo(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private BodyTrialTeamMemberInfo() {
    wxHeadUrl_ = "";
    buffVec_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_BodyTrialTeamMemberInfo_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_BodyTrialTeamMemberInfo_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.BodyTrialTeamMemberInfo.class, xddq.pb.BodyTrialTeamMemberInfo.Builder.class);
  }

  private int bitField0_;
  public static final int MEMBERINFO_FIELD_NUMBER = 1;
  private xddq.pb.PlayerAppearanceDataMsg memberInfo_;
  /**
   * <code>optional .xddq.pb.PlayerAppearanceDataMsg memberInfo = 1;</code>
   * @return Whether the memberInfo field is set.
   */
  @java.lang.Override
  public boolean hasMemberInfo() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional .xddq.pb.PlayerAppearanceDataMsg memberInfo = 1;</code>
   * @return The memberInfo.
   */
  @java.lang.Override
  public xddq.pb.PlayerAppearanceDataMsg getMemberInfo() {
    return memberInfo_ == null ? xddq.pb.PlayerAppearanceDataMsg.getDefaultInstance() : memberInfo_;
  }
  /**
   * <code>optional .xddq.pb.PlayerAppearanceDataMsg memberInfo = 1;</code>
   */
  @java.lang.Override
  public xddq.pb.PlayerAppearanceDataMsgOrBuilder getMemberInfoOrBuilder() {
    return memberInfo_ == null ? xddq.pb.PlayerAppearanceDataMsg.getDefaultInstance() : memberInfo_;
  }

  public static final int STATUS_FIELD_NUMBER = 2;
  private int status_ = 0;
  /**
   * <code>optional int32 status = 2;</code>
   * @return Whether the status field is set.
   */
  @java.lang.Override
  public boolean hasStatus() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int32 status = 2;</code>
   * @return The status.
   */
  @java.lang.Override
  public int getStatus() {
    return status_;
  }

  public static final int HISTORYMAXFLOOR_FIELD_NUMBER = 3;
  private int historyMaxFloor_ = 0;
  /**
   * <code>optional int32 historyMaxFloor = 3;</code>
   * @return Whether the historyMaxFloor field is set.
   */
  @java.lang.Override
  public boolean hasHistoryMaxFloor() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional int32 historyMaxFloor = 3;</code>
   * @return The historyMaxFloor.
   */
  @java.lang.Override
  public int getHistoryMaxFloor() {
    return historyMaxFloor_;
  }

  public static final int CHANGETEAMSTATUSTIME_FIELD_NUMBER = 4;
  private long changeTeamStatusTime_ = 0L;
  /**
   * <code>optional int64 changeTeamStatusTime = 4;</code>
   * @return Whether the changeTeamStatusTime field is set.
   */
  @java.lang.Override
  public boolean hasChangeTeamStatusTime() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional int64 changeTeamStatusTime = 4;</code>
   * @return The changeTeamStatusTime.
   */
  @java.lang.Override
  public long getChangeTeamStatusTime() {
    return changeTeamStatusTime_;
  }

  public static final int ISROBOT_FIELD_NUMBER = 5;
  private boolean isRobot_ = false;
  /**
   * <code>optional bool isRobot = 5;</code>
   * @return Whether the isRobot field is set.
   */
  @java.lang.Override
  public boolean hasIsRobot() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>optional bool isRobot = 5;</code>
   * @return The isRobot.
   */
  @java.lang.Override
  public boolean getIsRobot() {
    return isRobot_;
  }

  public static final int POS_FIELD_NUMBER = 6;
  private int pos_ = 0;
  /**
   * <code>optional int32 pos = 6;</code>
   * @return Whether the pos field is set.
   */
  @java.lang.Override
  public boolean hasPos() {
    return ((bitField0_ & 0x00000020) != 0);
  }
  /**
   * <code>optional int32 pos = 6;</code>
   * @return The pos.
   */
  @java.lang.Override
  public int getPos() {
    return pos_;
  }

  public static final int CHALLENGETIMES_FIELD_NUMBER = 7;
  private int challengeTimes_ = 0;
  /**
   * <code>optional int32 challengeTimes = 7;</code>
   * @return Whether the challengeTimes field is set.
   */
  @java.lang.Override
  public boolean hasChallengeTimes() {
    return ((bitField0_ & 0x00000040) != 0);
  }
  /**
   * <code>optional int32 challengeTimes = 7;</code>
   * @return The challengeTimes.
   */
  @java.lang.Override
  public int getChallengeTimes() {
    return challengeTimes_;
  }

  public static final int SELECTEDBUFF_FIELD_NUMBER = 8;
  private boolean selectedBuff_ = false;
  /**
   * <code>optional bool selectedBuff = 8;</code>
   * @return Whether the selectedBuff field is set.
   */
  @java.lang.Override
  public boolean hasSelectedBuff() {
    return ((bitField0_ & 0x00000080) != 0);
  }
  /**
   * <code>optional bool selectedBuff = 8;</code>
   * @return The selectedBuff.
   */
  @java.lang.Override
  public boolean getSelectedBuff() {
    return selectedBuff_;
  }

  public static final int HEADICONID_FIELD_NUMBER = 9;
  private int headIconId_ = 0;
  /**
   * <code>optional int32 headIconId = 9;</code>
   * @return Whether the headIconId field is set.
   */
  @java.lang.Override
  public boolean hasHeadIconId() {
    return ((bitField0_ & 0x00000100) != 0);
  }
  /**
   * <code>optional int32 headIconId = 9;</code>
   * @return The headIconId.
   */
  @java.lang.Override
  public int getHeadIconId() {
    return headIconId_;
  }

  public static final int WXHEADURL_FIELD_NUMBER = 10;
  @SuppressWarnings("serial")
  private volatile java.lang.Object wxHeadUrl_ = "";
  /**
   * <code>optional string wxHeadUrl = 10;</code>
   * @return Whether the wxHeadUrl field is set.
   */
  @java.lang.Override
  public boolean hasWxHeadUrl() {
    return ((bitField0_ & 0x00000200) != 0);
  }
  /**
   * <code>optional string wxHeadUrl = 10;</code>
   * @return The wxHeadUrl.
   */
  @java.lang.Override
  public java.lang.String getWxHeadUrl() {
    java.lang.Object ref = wxHeadUrl_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        wxHeadUrl_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string wxHeadUrl = 10;</code>
   * @return The bytes for wxHeadUrl.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getWxHeadUrlBytes() {
    java.lang.Object ref = wxHeadUrl_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      wxHeadUrl_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int HEADFRAMEID_FIELD_NUMBER = 11;
  private int headFrameId_ = 0;
  /**
   * <code>optional int32 headFrameId = 11;</code>
   * @return Whether the headFrameId field is set.
   */
  @java.lang.Override
  public boolean hasHeadFrameId() {
    return ((bitField0_ & 0x00000400) != 0);
  }
  /**
   * <code>optional int32 headFrameId = 11;</code>
   * @return The headFrameId.
   */
  @java.lang.Override
  public int getHeadFrameId() {
    return headFrameId_;
  }

  public static final int BUFFVEC_FIELD_NUMBER = 12;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.BodyTrialBuffObj> buffVec_;
  /**
   * <code>repeated .xddq.pb.BodyTrialBuffObj buffVec = 12;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.BodyTrialBuffObj> getBuffVecList() {
    return buffVec_;
  }
  /**
   * <code>repeated .xddq.pb.BodyTrialBuffObj buffVec = 12;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.BodyTrialBuffObjOrBuilder> 
      getBuffVecOrBuilderList() {
    return buffVec_;
  }
  /**
   * <code>repeated .xddq.pb.BodyTrialBuffObj buffVec = 12;</code>
   */
  @java.lang.Override
  public int getBuffVecCount() {
    return buffVec_.size();
  }
  /**
   * <code>repeated .xddq.pb.BodyTrialBuffObj buffVec = 12;</code>
   */
  @java.lang.Override
  public xddq.pb.BodyTrialBuffObj getBuffVec(int index) {
    return buffVec_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.BodyTrialBuffObj buffVec = 12;</code>
   */
  @java.lang.Override
  public xddq.pb.BodyTrialBuffObjOrBuilder getBuffVecOrBuilder(
      int index) {
    return buffVec_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeMessage(1, getMemberInfo());
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(2, status_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt32(3, historyMaxFloor_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt64(4, changeTeamStatusTime_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      output.writeBool(5, isRobot_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      output.writeInt32(6, pos_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      output.writeInt32(7, challengeTimes_);
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      output.writeBool(8, selectedBuff_);
    }
    if (((bitField0_ & 0x00000100) != 0)) {
      output.writeInt32(9, headIconId_);
    }
    if (((bitField0_ & 0x00000200) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 10, wxHeadUrl_);
    }
    if (((bitField0_ & 0x00000400) != 0)) {
      output.writeInt32(11, headFrameId_);
    }
    for (int i = 0; i < buffVec_.size(); i++) {
      output.writeMessage(12, buffVec_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, getMemberInfo());
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, status_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(3, historyMaxFloor_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(4, changeTeamStatusTime_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(5, isRobot_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(6, pos_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(7, challengeTimes_);
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(8, selectedBuff_);
    }
    if (((bitField0_ & 0x00000100) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(9, headIconId_);
    }
    if (((bitField0_ & 0x00000200) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(10, wxHeadUrl_);
    }
    if (((bitField0_ & 0x00000400) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(11, headFrameId_);
    }
    for (int i = 0; i < buffVec_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(12, buffVec_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.BodyTrialTeamMemberInfo)) {
      return super.equals(obj);
    }
    xddq.pb.BodyTrialTeamMemberInfo other = (xddq.pb.BodyTrialTeamMemberInfo) obj;

    if (hasMemberInfo() != other.hasMemberInfo()) return false;
    if (hasMemberInfo()) {
      if (!getMemberInfo()
          .equals(other.getMemberInfo())) return false;
    }
    if (hasStatus() != other.hasStatus()) return false;
    if (hasStatus()) {
      if (getStatus()
          != other.getStatus()) return false;
    }
    if (hasHistoryMaxFloor() != other.hasHistoryMaxFloor()) return false;
    if (hasHistoryMaxFloor()) {
      if (getHistoryMaxFloor()
          != other.getHistoryMaxFloor()) return false;
    }
    if (hasChangeTeamStatusTime() != other.hasChangeTeamStatusTime()) return false;
    if (hasChangeTeamStatusTime()) {
      if (getChangeTeamStatusTime()
          != other.getChangeTeamStatusTime()) return false;
    }
    if (hasIsRobot() != other.hasIsRobot()) return false;
    if (hasIsRobot()) {
      if (getIsRobot()
          != other.getIsRobot()) return false;
    }
    if (hasPos() != other.hasPos()) return false;
    if (hasPos()) {
      if (getPos()
          != other.getPos()) return false;
    }
    if (hasChallengeTimes() != other.hasChallengeTimes()) return false;
    if (hasChallengeTimes()) {
      if (getChallengeTimes()
          != other.getChallengeTimes()) return false;
    }
    if (hasSelectedBuff() != other.hasSelectedBuff()) return false;
    if (hasSelectedBuff()) {
      if (getSelectedBuff()
          != other.getSelectedBuff()) return false;
    }
    if (hasHeadIconId() != other.hasHeadIconId()) return false;
    if (hasHeadIconId()) {
      if (getHeadIconId()
          != other.getHeadIconId()) return false;
    }
    if (hasWxHeadUrl() != other.hasWxHeadUrl()) return false;
    if (hasWxHeadUrl()) {
      if (!getWxHeadUrl()
          .equals(other.getWxHeadUrl())) return false;
    }
    if (hasHeadFrameId() != other.hasHeadFrameId()) return false;
    if (hasHeadFrameId()) {
      if (getHeadFrameId()
          != other.getHeadFrameId()) return false;
    }
    if (!getBuffVecList()
        .equals(other.getBuffVecList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasMemberInfo()) {
      hash = (37 * hash) + MEMBERINFO_FIELD_NUMBER;
      hash = (53 * hash) + getMemberInfo().hashCode();
    }
    if (hasStatus()) {
      hash = (37 * hash) + STATUS_FIELD_NUMBER;
      hash = (53 * hash) + getStatus();
    }
    if (hasHistoryMaxFloor()) {
      hash = (37 * hash) + HISTORYMAXFLOOR_FIELD_NUMBER;
      hash = (53 * hash) + getHistoryMaxFloor();
    }
    if (hasChangeTeamStatusTime()) {
      hash = (37 * hash) + CHANGETEAMSTATUSTIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getChangeTeamStatusTime());
    }
    if (hasIsRobot()) {
      hash = (37 * hash) + ISROBOT_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getIsRobot());
    }
    if (hasPos()) {
      hash = (37 * hash) + POS_FIELD_NUMBER;
      hash = (53 * hash) + getPos();
    }
    if (hasChallengeTimes()) {
      hash = (37 * hash) + CHALLENGETIMES_FIELD_NUMBER;
      hash = (53 * hash) + getChallengeTimes();
    }
    if (hasSelectedBuff()) {
      hash = (37 * hash) + SELECTEDBUFF_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getSelectedBuff());
    }
    if (hasHeadIconId()) {
      hash = (37 * hash) + HEADICONID_FIELD_NUMBER;
      hash = (53 * hash) + getHeadIconId();
    }
    if (hasWxHeadUrl()) {
      hash = (37 * hash) + WXHEADURL_FIELD_NUMBER;
      hash = (53 * hash) + getWxHeadUrl().hashCode();
    }
    if (hasHeadFrameId()) {
      hash = (37 * hash) + HEADFRAMEID_FIELD_NUMBER;
      hash = (53 * hash) + getHeadFrameId();
    }
    if (getBuffVecCount() > 0) {
      hash = (37 * hash) + BUFFVEC_FIELD_NUMBER;
      hash = (53 * hash) + getBuffVecList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.BodyTrialTeamMemberInfo parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.BodyTrialTeamMemberInfo parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.BodyTrialTeamMemberInfo parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.BodyTrialTeamMemberInfo parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.BodyTrialTeamMemberInfo parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.BodyTrialTeamMemberInfo parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.BodyTrialTeamMemberInfo parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.BodyTrialTeamMemberInfo parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.BodyTrialTeamMemberInfo parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.BodyTrialTeamMemberInfo parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.BodyTrialTeamMemberInfo parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.BodyTrialTeamMemberInfo parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.BodyTrialTeamMemberInfo prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.BodyTrialTeamMemberInfo}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.BodyTrialTeamMemberInfo)
      xddq.pb.BodyTrialTeamMemberInfoOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_BodyTrialTeamMemberInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_BodyTrialTeamMemberInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.BodyTrialTeamMemberInfo.class, xddq.pb.BodyTrialTeamMemberInfo.Builder.class);
    }

    // Construct using xddq.pb.BodyTrialTeamMemberInfo.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetMemberInfoFieldBuilder();
        internalGetBuffVecFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      memberInfo_ = null;
      if (memberInfoBuilder_ != null) {
        memberInfoBuilder_.dispose();
        memberInfoBuilder_ = null;
      }
      status_ = 0;
      historyMaxFloor_ = 0;
      changeTeamStatusTime_ = 0L;
      isRobot_ = false;
      pos_ = 0;
      challengeTimes_ = 0;
      selectedBuff_ = false;
      headIconId_ = 0;
      wxHeadUrl_ = "";
      headFrameId_ = 0;
      if (buffVecBuilder_ == null) {
        buffVec_ = java.util.Collections.emptyList();
      } else {
        buffVec_ = null;
        buffVecBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000800);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_BodyTrialTeamMemberInfo_descriptor;
    }

    @java.lang.Override
    public xddq.pb.BodyTrialTeamMemberInfo getDefaultInstanceForType() {
      return xddq.pb.BodyTrialTeamMemberInfo.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.BodyTrialTeamMemberInfo build() {
      xddq.pb.BodyTrialTeamMemberInfo result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.BodyTrialTeamMemberInfo buildPartial() {
      xddq.pb.BodyTrialTeamMemberInfo result = new xddq.pb.BodyTrialTeamMemberInfo(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.BodyTrialTeamMemberInfo result) {
      if (buffVecBuilder_ == null) {
        if (((bitField0_ & 0x00000800) != 0)) {
          buffVec_ = java.util.Collections.unmodifiableList(buffVec_);
          bitField0_ = (bitField0_ & ~0x00000800);
        }
        result.buffVec_ = buffVec_;
      } else {
        result.buffVec_ = buffVecBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.BodyTrialTeamMemberInfo result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.memberInfo_ = memberInfoBuilder_ == null
            ? memberInfo_
            : memberInfoBuilder_.build();
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.status_ = status_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.historyMaxFloor_ = historyMaxFloor_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.changeTeamStatusTime_ = changeTeamStatusTime_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.isRobot_ = isRobot_;
        to_bitField0_ |= 0x00000010;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.pos_ = pos_;
        to_bitField0_ |= 0x00000020;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.challengeTimes_ = challengeTimes_;
        to_bitField0_ |= 0x00000040;
      }
      if (((from_bitField0_ & 0x00000080) != 0)) {
        result.selectedBuff_ = selectedBuff_;
        to_bitField0_ |= 0x00000080;
      }
      if (((from_bitField0_ & 0x00000100) != 0)) {
        result.headIconId_ = headIconId_;
        to_bitField0_ |= 0x00000100;
      }
      if (((from_bitField0_ & 0x00000200) != 0)) {
        result.wxHeadUrl_ = wxHeadUrl_;
        to_bitField0_ |= 0x00000200;
      }
      if (((from_bitField0_ & 0x00000400) != 0)) {
        result.headFrameId_ = headFrameId_;
        to_bitField0_ |= 0x00000400;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.BodyTrialTeamMemberInfo) {
        return mergeFrom((xddq.pb.BodyTrialTeamMemberInfo)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.BodyTrialTeamMemberInfo other) {
      if (other == xddq.pb.BodyTrialTeamMemberInfo.getDefaultInstance()) return this;
      if (other.hasMemberInfo()) {
        mergeMemberInfo(other.getMemberInfo());
      }
      if (other.hasStatus()) {
        setStatus(other.getStatus());
      }
      if (other.hasHistoryMaxFloor()) {
        setHistoryMaxFloor(other.getHistoryMaxFloor());
      }
      if (other.hasChangeTeamStatusTime()) {
        setChangeTeamStatusTime(other.getChangeTeamStatusTime());
      }
      if (other.hasIsRobot()) {
        setIsRobot(other.getIsRobot());
      }
      if (other.hasPos()) {
        setPos(other.getPos());
      }
      if (other.hasChallengeTimes()) {
        setChallengeTimes(other.getChallengeTimes());
      }
      if (other.hasSelectedBuff()) {
        setSelectedBuff(other.getSelectedBuff());
      }
      if (other.hasHeadIconId()) {
        setHeadIconId(other.getHeadIconId());
      }
      if (other.hasWxHeadUrl()) {
        wxHeadUrl_ = other.wxHeadUrl_;
        bitField0_ |= 0x00000200;
        onChanged();
      }
      if (other.hasHeadFrameId()) {
        setHeadFrameId(other.getHeadFrameId());
      }
      if (buffVecBuilder_ == null) {
        if (!other.buffVec_.isEmpty()) {
          if (buffVec_.isEmpty()) {
            buffVec_ = other.buffVec_;
            bitField0_ = (bitField0_ & ~0x00000800);
          } else {
            ensureBuffVecIsMutable();
            buffVec_.addAll(other.buffVec_);
          }
          onChanged();
        }
      } else {
        if (!other.buffVec_.isEmpty()) {
          if (buffVecBuilder_.isEmpty()) {
            buffVecBuilder_.dispose();
            buffVecBuilder_ = null;
            buffVec_ = other.buffVec_;
            bitField0_ = (bitField0_ & ~0x00000800);
            buffVecBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetBuffVecFieldBuilder() : null;
          } else {
            buffVecBuilder_.addAllMessages(other.buffVec_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              input.readMessage(
                  internalGetMemberInfoFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 16: {
              status_ = input.readInt32();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 24: {
              historyMaxFloor_ = input.readInt32();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 32: {
              changeTeamStatusTime_ = input.readInt64();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            case 40: {
              isRobot_ = input.readBool();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            case 48: {
              pos_ = input.readInt32();
              bitField0_ |= 0x00000020;
              break;
            } // case 48
            case 56: {
              challengeTimes_ = input.readInt32();
              bitField0_ |= 0x00000040;
              break;
            } // case 56
            case 64: {
              selectedBuff_ = input.readBool();
              bitField0_ |= 0x00000080;
              break;
            } // case 64
            case 72: {
              headIconId_ = input.readInt32();
              bitField0_ |= 0x00000100;
              break;
            } // case 72
            case 82: {
              wxHeadUrl_ = input.readBytes();
              bitField0_ |= 0x00000200;
              break;
            } // case 82
            case 88: {
              headFrameId_ = input.readInt32();
              bitField0_ |= 0x00000400;
              break;
            } // case 88
            case 98: {
              xddq.pb.BodyTrialBuffObj m =
                  input.readMessage(
                      xddq.pb.BodyTrialBuffObj.parser(),
                      extensionRegistry);
              if (buffVecBuilder_ == null) {
                ensureBuffVecIsMutable();
                buffVec_.add(m);
              } else {
                buffVecBuilder_.addMessage(m);
              }
              break;
            } // case 98
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private xddq.pb.PlayerAppearanceDataMsg memberInfo_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PlayerAppearanceDataMsg, xddq.pb.PlayerAppearanceDataMsg.Builder, xddq.pb.PlayerAppearanceDataMsgOrBuilder> memberInfoBuilder_;
    /**
     * <code>optional .xddq.pb.PlayerAppearanceDataMsg memberInfo = 1;</code>
     * @return Whether the memberInfo field is set.
     */
    public boolean hasMemberInfo() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .xddq.pb.PlayerAppearanceDataMsg memberInfo = 1;</code>
     * @return The memberInfo.
     */
    public xddq.pb.PlayerAppearanceDataMsg getMemberInfo() {
      if (memberInfoBuilder_ == null) {
        return memberInfo_ == null ? xddq.pb.PlayerAppearanceDataMsg.getDefaultInstance() : memberInfo_;
      } else {
        return memberInfoBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.PlayerAppearanceDataMsg memberInfo = 1;</code>
     */
    public Builder setMemberInfo(xddq.pb.PlayerAppearanceDataMsg value) {
      if (memberInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        memberInfo_ = value;
      } else {
        memberInfoBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerAppearanceDataMsg memberInfo = 1;</code>
     */
    public Builder setMemberInfo(
        xddq.pb.PlayerAppearanceDataMsg.Builder builderForValue) {
      if (memberInfoBuilder_ == null) {
        memberInfo_ = builderForValue.build();
      } else {
        memberInfoBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerAppearanceDataMsg memberInfo = 1;</code>
     */
    public Builder mergeMemberInfo(xddq.pb.PlayerAppearanceDataMsg value) {
      if (memberInfoBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0) &&
          memberInfo_ != null &&
          memberInfo_ != xddq.pb.PlayerAppearanceDataMsg.getDefaultInstance()) {
          getMemberInfoBuilder().mergeFrom(value);
        } else {
          memberInfo_ = value;
        }
      } else {
        memberInfoBuilder_.mergeFrom(value);
      }
      if (memberInfo_ != null) {
        bitField0_ |= 0x00000001;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerAppearanceDataMsg memberInfo = 1;</code>
     */
    public Builder clearMemberInfo() {
      bitField0_ = (bitField0_ & ~0x00000001);
      memberInfo_ = null;
      if (memberInfoBuilder_ != null) {
        memberInfoBuilder_.dispose();
        memberInfoBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerAppearanceDataMsg memberInfo = 1;</code>
     */
    public xddq.pb.PlayerAppearanceDataMsg.Builder getMemberInfoBuilder() {
      bitField0_ |= 0x00000001;
      onChanged();
      return internalGetMemberInfoFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.PlayerAppearanceDataMsg memberInfo = 1;</code>
     */
    public xddq.pb.PlayerAppearanceDataMsgOrBuilder getMemberInfoOrBuilder() {
      if (memberInfoBuilder_ != null) {
        return memberInfoBuilder_.getMessageOrBuilder();
      } else {
        return memberInfo_ == null ?
            xddq.pb.PlayerAppearanceDataMsg.getDefaultInstance() : memberInfo_;
      }
    }
    /**
     * <code>optional .xddq.pb.PlayerAppearanceDataMsg memberInfo = 1;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PlayerAppearanceDataMsg, xddq.pb.PlayerAppearanceDataMsg.Builder, xddq.pb.PlayerAppearanceDataMsgOrBuilder> 
        internalGetMemberInfoFieldBuilder() {
      if (memberInfoBuilder_ == null) {
        memberInfoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.PlayerAppearanceDataMsg, xddq.pb.PlayerAppearanceDataMsg.Builder, xddq.pb.PlayerAppearanceDataMsgOrBuilder>(
                getMemberInfo(),
                getParentForChildren(),
                isClean());
        memberInfo_ = null;
      }
      return memberInfoBuilder_;
    }

    private int status_ ;
    /**
     * <code>optional int32 status = 2;</code>
     * @return Whether the status field is set.
     */
    @java.lang.Override
    public boolean hasStatus() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 status = 2;</code>
     * @return The status.
     */
    @java.lang.Override
    public int getStatus() {
      return status_;
    }
    /**
     * <code>optional int32 status = 2;</code>
     * @param value The status to set.
     * @return This builder for chaining.
     */
    public Builder setStatus(int value) {

      status_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 status = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearStatus() {
      bitField0_ = (bitField0_ & ~0x00000002);
      status_ = 0;
      onChanged();
      return this;
    }

    private int historyMaxFloor_ ;
    /**
     * <code>optional int32 historyMaxFloor = 3;</code>
     * @return Whether the historyMaxFloor field is set.
     */
    @java.lang.Override
    public boolean hasHistoryMaxFloor() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int32 historyMaxFloor = 3;</code>
     * @return The historyMaxFloor.
     */
    @java.lang.Override
    public int getHistoryMaxFloor() {
      return historyMaxFloor_;
    }
    /**
     * <code>optional int32 historyMaxFloor = 3;</code>
     * @param value The historyMaxFloor to set.
     * @return This builder for chaining.
     */
    public Builder setHistoryMaxFloor(int value) {

      historyMaxFloor_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 historyMaxFloor = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearHistoryMaxFloor() {
      bitField0_ = (bitField0_ & ~0x00000004);
      historyMaxFloor_ = 0;
      onChanged();
      return this;
    }

    private long changeTeamStatusTime_ ;
    /**
     * <code>optional int64 changeTeamStatusTime = 4;</code>
     * @return Whether the changeTeamStatusTime field is set.
     */
    @java.lang.Override
    public boolean hasChangeTeamStatusTime() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int64 changeTeamStatusTime = 4;</code>
     * @return The changeTeamStatusTime.
     */
    @java.lang.Override
    public long getChangeTeamStatusTime() {
      return changeTeamStatusTime_;
    }
    /**
     * <code>optional int64 changeTeamStatusTime = 4;</code>
     * @param value The changeTeamStatusTime to set.
     * @return This builder for chaining.
     */
    public Builder setChangeTeamStatusTime(long value) {

      changeTeamStatusTime_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 changeTeamStatusTime = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearChangeTeamStatusTime() {
      bitField0_ = (bitField0_ & ~0x00000008);
      changeTeamStatusTime_ = 0L;
      onChanged();
      return this;
    }

    private boolean isRobot_ ;
    /**
     * <code>optional bool isRobot = 5;</code>
     * @return Whether the isRobot field is set.
     */
    @java.lang.Override
    public boolean hasIsRobot() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional bool isRobot = 5;</code>
     * @return The isRobot.
     */
    @java.lang.Override
    public boolean getIsRobot() {
      return isRobot_;
    }
    /**
     * <code>optional bool isRobot = 5;</code>
     * @param value The isRobot to set.
     * @return This builder for chaining.
     */
    public Builder setIsRobot(boolean value) {

      isRobot_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional bool isRobot = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearIsRobot() {
      bitField0_ = (bitField0_ & ~0x00000010);
      isRobot_ = false;
      onChanged();
      return this;
    }

    private int pos_ ;
    /**
     * <code>optional int32 pos = 6;</code>
     * @return Whether the pos field is set.
     */
    @java.lang.Override
    public boolean hasPos() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional int32 pos = 6;</code>
     * @return The pos.
     */
    @java.lang.Override
    public int getPos() {
      return pos_;
    }
    /**
     * <code>optional int32 pos = 6;</code>
     * @param value The pos to set.
     * @return This builder for chaining.
     */
    public Builder setPos(int value) {

      pos_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 pos = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearPos() {
      bitField0_ = (bitField0_ & ~0x00000020);
      pos_ = 0;
      onChanged();
      return this;
    }

    private int challengeTimes_ ;
    /**
     * <code>optional int32 challengeTimes = 7;</code>
     * @return Whether the challengeTimes field is set.
     */
    @java.lang.Override
    public boolean hasChallengeTimes() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <code>optional int32 challengeTimes = 7;</code>
     * @return The challengeTimes.
     */
    @java.lang.Override
    public int getChallengeTimes() {
      return challengeTimes_;
    }
    /**
     * <code>optional int32 challengeTimes = 7;</code>
     * @param value The challengeTimes to set.
     * @return This builder for chaining.
     */
    public Builder setChallengeTimes(int value) {

      challengeTimes_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 challengeTimes = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearChallengeTimes() {
      bitField0_ = (bitField0_ & ~0x00000040);
      challengeTimes_ = 0;
      onChanged();
      return this;
    }

    private boolean selectedBuff_ ;
    /**
     * <code>optional bool selectedBuff = 8;</code>
     * @return Whether the selectedBuff field is set.
     */
    @java.lang.Override
    public boolean hasSelectedBuff() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <code>optional bool selectedBuff = 8;</code>
     * @return The selectedBuff.
     */
    @java.lang.Override
    public boolean getSelectedBuff() {
      return selectedBuff_;
    }
    /**
     * <code>optional bool selectedBuff = 8;</code>
     * @param value The selectedBuff to set.
     * @return This builder for chaining.
     */
    public Builder setSelectedBuff(boolean value) {

      selectedBuff_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }
    /**
     * <code>optional bool selectedBuff = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearSelectedBuff() {
      bitField0_ = (bitField0_ & ~0x00000080);
      selectedBuff_ = false;
      onChanged();
      return this;
    }

    private int headIconId_ ;
    /**
     * <code>optional int32 headIconId = 9;</code>
     * @return Whether the headIconId field is set.
     */
    @java.lang.Override
    public boolean hasHeadIconId() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <code>optional int32 headIconId = 9;</code>
     * @return The headIconId.
     */
    @java.lang.Override
    public int getHeadIconId() {
      return headIconId_;
    }
    /**
     * <code>optional int32 headIconId = 9;</code>
     * @param value The headIconId to set.
     * @return This builder for chaining.
     */
    public Builder setHeadIconId(int value) {

      headIconId_ = value;
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 headIconId = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearHeadIconId() {
      bitField0_ = (bitField0_ & ~0x00000100);
      headIconId_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object wxHeadUrl_ = "";
    /**
     * <code>optional string wxHeadUrl = 10;</code>
     * @return Whether the wxHeadUrl field is set.
     */
    public boolean hasWxHeadUrl() {
      return ((bitField0_ & 0x00000200) != 0);
    }
    /**
     * <code>optional string wxHeadUrl = 10;</code>
     * @return The wxHeadUrl.
     */
    public java.lang.String getWxHeadUrl() {
      java.lang.Object ref = wxHeadUrl_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          wxHeadUrl_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string wxHeadUrl = 10;</code>
     * @return The bytes for wxHeadUrl.
     */
    public com.google.protobuf.ByteString
        getWxHeadUrlBytes() {
      java.lang.Object ref = wxHeadUrl_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        wxHeadUrl_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string wxHeadUrl = 10;</code>
     * @param value The wxHeadUrl to set.
     * @return This builder for chaining.
     */
    public Builder setWxHeadUrl(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      wxHeadUrl_ = value;
      bitField0_ |= 0x00000200;
      onChanged();
      return this;
    }
    /**
     * <code>optional string wxHeadUrl = 10;</code>
     * @return This builder for chaining.
     */
    public Builder clearWxHeadUrl() {
      wxHeadUrl_ = getDefaultInstance().getWxHeadUrl();
      bitField0_ = (bitField0_ & ~0x00000200);
      onChanged();
      return this;
    }
    /**
     * <code>optional string wxHeadUrl = 10;</code>
     * @param value The bytes for wxHeadUrl to set.
     * @return This builder for chaining.
     */
    public Builder setWxHeadUrlBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      wxHeadUrl_ = value;
      bitField0_ |= 0x00000200;
      onChanged();
      return this;
    }

    private int headFrameId_ ;
    /**
     * <code>optional int32 headFrameId = 11;</code>
     * @return Whether the headFrameId field is set.
     */
    @java.lang.Override
    public boolean hasHeadFrameId() {
      return ((bitField0_ & 0x00000400) != 0);
    }
    /**
     * <code>optional int32 headFrameId = 11;</code>
     * @return The headFrameId.
     */
    @java.lang.Override
    public int getHeadFrameId() {
      return headFrameId_;
    }
    /**
     * <code>optional int32 headFrameId = 11;</code>
     * @param value The headFrameId to set.
     * @return This builder for chaining.
     */
    public Builder setHeadFrameId(int value) {

      headFrameId_ = value;
      bitField0_ |= 0x00000400;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 headFrameId = 11;</code>
     * @return This builder for chaining.
     */
    public Builder clearHeadFrameId() {
      bitField0_ = (bitField0_ & ~0x00000400);
      headFrameId_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.BodyTrialBuffObj> buffVec_ =
      java.util.Collections.emptyList();
    private void ensureBuffVecIsMutable() {
      if (!((bitField0_ & 0x00000800) != 0)) {
        buffVec_ = new java.util.ArrayList<xddq.pb.BodyTrialBuffObj>(buffVec_);
        bitField0_ |= 0x00000800;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.BodyTrialBuffObj, xddq.pb.BodyTrialBuffObj.Builder, xddq.pb.BodyTrialBuffObjOrBuilder> buffVecBuilder_;

    /**
     * <code>repeated .xddq.pb.BodyTrialBuffObj buffVec = 12;</code>
     */
    public java.util.List<xddq.pb.BodyTrialBuffObj> getBuffVecList() {
      if (buffVecBuilder_ == null) {
        return java.util.Collections.unmodifiableList(buffVec_);
      } else {
        return buffVecBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.BodyTrialBuffObj buffVec = 12;</code>
     */
    public int getBuffVecCount() {
      if (buffVecBuilder_ == null) {
        return buffVec_.size();
      } else {
        return buffVecBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.BodyTrialBuffObj buffVec = 12;</code>
     */
    public xddq.pb.BodyTrialBuffObj getBuffVec(int index) {
      if (buffVecBuilder_ == null) {
        return buffVec_.get(index);
      } else {
        return buffVecBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.BodyTrialBuffObj buffVec = 12;</code>
     */
    public Builder setBuffVec(
        int index, xddq.pb.BodyTrialBuffObj value) {
      if (buffVecBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureBuffVecIsMutable();
        buffVec_.set(index, value);
        onChanged();
      } else {
        buffVecBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.BodyTrialBuffObj buffVec = 12;</code>
     */
    public Builder setBuffVec(
        int index, xddq.pb.BodyTrialBuffObj.Builder builderForValue) {
      if (buffVecBuilder_ == null) {
        ensureBuffVecIsMutable();
        buffVec_.set(index, builderForValue.build());
        onChanged();
      } else {
        buffVecBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.BodyTrialBuffObj buffVec = 12;</code>
     */
    public Builder addBuffVec(xddq.pb.BodyTrialBuffObj value) {
      if (buffVecBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureBuffVecIsMutable();
        buffVec_.add(value);
        onChanged();
      } else {
        buffVecBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.BodyTrialBuffObj buffVec = 12;</code>
     */
    public Builder addBuffVec(
        int index, xddq.pb.BodyTrialBuffObj value) {
      if (buffVecBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureBuffVecIsMutable();
        buffVec_.add(index, value);
        onChanged();
      } else {
        buffVecBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.BodyTrialBuffObj buffVec = 12;</code>
     */
    public Builder addBuffVec(
        xddq.pb.BodyTrialBuffObj.Builder builderForValue) {
      if (buffVecBuilder_ == null) {
        ensureBuffVecIsMutable();
        buffVec_.add(builderForValue.build());
        onChanged();
      } else {
        buffVecBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.BodyTrialBuffObj buffVec = 12;</code>
     */
    public Builder addBuffVec(
        int index, xddq.pb.BodyTrialBuffObj.Builder builderForValue) {
      if (buffVecBuilder_ == null) {
        ensureBuffVecIsMutable();
        buffVec_.add(index, builderForValue.build());
        onChanged();
      } else {
        buffVecBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.BodyTrialBuffObj buffVec = 12;</code>
     */
    public Builder addAllBuffVec(
        java.lang.Iterable<? extends xddq.pb.BodyTrialBuffObj> values) {
      if (buffVecBuilder_ == null) {
        ensureBuffVecIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, buffVec_);
        onChanged();
      } else {
        buffVecBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.BodyTrialBuffObj buffVec = 12;</code>
     */
    public Builder clearBuffVec() {
      if (buffVecBuilder_ == null) {
        buffVec_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000800);
        onChanged();
      } else {
        buffVecBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.BodyTrialBuffObj buffVec = 12;</code>
     */
    public Builder removeBuffVec(int index) {
      if (buffVecBuilder_ == null) {
        ensureBuffVecIsMutable();
        buffVec_.remove(index);
        onChanged();
      } else {
        buffVecBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.BodyTrialBuffObj buffVec = 12;</code>
     */
    public xddq.pb.BodyTrialBuffObj.Builder getBuffVecBuilder(
        int index) {
      return internalGetBuffVecFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.BodyTrialBuffObj buffVec = 12;</code>
     */
    public xddq.pb.BodyTrialBuffObjOrBuilder getBuffVecOrBuilder(
        int index) {
      if (buffVecBuilder_ == null) {
        return buffVec_.get(index);  } else {
        return buffVecBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.BodyTrialBuffObj buffVec = 12;</code>
     */
    public java.util.List<? extends xddq.pb.BodyTrialBuffObjOrBuilder> 
         getBuffVecOrBuilderList() {
      if (buffVecBuilder_ != null) {
        return buffVecBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(buffVec_);
      }
    }
    /**
     * <code>repeated .xddq.pb.BodyTrialBuffObj buffVec = 12;</code>
     */
    public xddq.pb.BodyTrialBuffObj.Builder addBuffVecBuilder() {
      return internalGetBuffVecFieldBuilder().addBuilder(
          xddq.pb.BodyTrialBuffObj.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.BodyTrialBuffObj buffVec = 12;</code>
     */
    public xddq.pb.BodyTrialBuffObj.Builder addBuffVecBuilder(
        int index) {
      return internalGetBuffVecFieldBuilder().addBuilder(
          index, xddq.pb.BodyTrialBuffObj.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.BodyTrialBuffObj buffVec = 12;</code>
     */
    public java.util.List<xddq.pb.BodyTrialBuffObj.Builder> 
         getBuffVecBuilderList() {
      return internalGetBuffVecFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.BodyTrialBuffObj, xddq.pb.BodyTrialBuffObj.Builder, xddq.pb.BodyTrialBuffObjOrBuilder> 
        internalGetBuffVecFieldBuilder() {
      if (buffVecBuilder_ == null) {
        buffVecBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.BodyTrialBuffObj, xddq.pb.BodyTrialBuffObj.Builder, xddq.pb.BodyTrialBuffObjOrBuilder>(
                buffVec_,
                ((bitField0_ & 0x00000800) != 0),
                getParentForChildren(),
                isClean());
        buffVec_ = null;
      }
      return buffVecBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.BodyTrialTeamMemberInfo)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.BodyTrialTeamMemberInfo)
  private static final xddq.pb.BodyTrialTeamMemberInfo DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.BodyTrialTeamMemberInfo();
  }

  public static xddq.pb.BodyTrialTeamMemberInfo getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<BodyTrialTeamMemberInfo>
      PARSER = new com.google.protobuf.AbstractParser<BodyTrialTeamMemberInfo>() {
    @java.lang.Override
    public BodyTrialTeamMemberInfo parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<BodyTrialTeamMemberInfo> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<BodyTrialTeamMemberInfo> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.BodyTrialTeamMemberInfo getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

