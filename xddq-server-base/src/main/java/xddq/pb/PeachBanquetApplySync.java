// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.PeachBanquetApplySync}
 */
public final class PeachBanquetApplySync extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.PeachBanquetApplySync)
    PeachBanquetApplySyncOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      PeachBanquetApplySync.class.getName());
  }
  // Use PeachBanquetApplySync.newBuilder() to construct.
  private PeachBanquetApplySync(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private PeachBanquetApplySync() {
    banquetId_ = "";
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_PeachBanquetApplySync_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_PeachBanquetApplySync_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.PeachBanquetApplySync.class, xddq.pb.PeachBanquetApplySync.Builder.class);
  }

  private int bitField0_;
  public static final int BANQUETID_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private volatile java.lang.Object banquetId_ = "";
  /**
   * <code>optional string banquetId = 1;</code>
   * @return Whether the banquetId field is set.
   */
  @java.lang.Override
  public boolean hasBanquetId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional string banquetId = 1;</code>
   * @return The banquetId.
   */
  @java.lang.Override
  public java.lang.String getBanquetId() {
    java.lang.Object ref = banquetId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        banquetId_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string banquetId = 1;</code>
   * @return The bytes for banquetId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getBanquetIdBytes() {
    java.lang.Object ref = banquetId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      banquetId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int HASAPPLY_FIELD_NUMBER = 2;
  private boolean hasApply_ = false;
  /**
   * <code>optional bool hasApply = 2;</code>
   * @return Whether the hasApply field is set.
   */
  @java.lang.Override
  public boolean hasHasApply() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional bool hasApply = 2;</code>
   * @return The hasApply.
   */
  @java.lang.Override
  public boolean getHasApply() {
    return hasApply_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 1, banquetId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeBool(2, hasApply_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(1, banquetId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(2, hasApply_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.PeachBanquetApplySync)) {
      return super.equals(obj);
    }
    xddq.pb.PeachBanquetApplySync other = (xddq.pb.PeachBanquetApplySync) obj;

    if (hasBanquetId() != other.hasBanquetId()) return false;
    if (hasBanquetId()) {
      if (!getBanquetId()
          .equals(other.getBanquetId())) return false;
    }
    if (hasHasApply() != other.hasHasApply()) return false;
    if (hasHasApply()) {
      if (getHasApply()
          != other.getHasApply()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasBanquetId()) {
      hash = (37 * hash) + BANQUETID_FIELD_NUMBER;
      hash = (53 * hash) + getBanquetId().hashCode();
    }
    if (hasHasApply()) {
      hash = (37 * hash) + HASAPPLY_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getHasApply());
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.PeachBanquetApplySync parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PeachBanquetApplySync parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PeachBanquetApplySync parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PeachBanquetApplySync parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PeachBanquetApplySync parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PeachBanquetApplySync parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PeachBanquetApplySync parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.PeachBanquetApplySync parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.PeachBanquetApplySync parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.PeachBanquetApplySync parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.PeachBanquetApplySync parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.PeachBanquetApplySync parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.PeachBanquetApplySync prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.PeachBanquetApplySync}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.PeachBanquetApplySync)
      xddq.pb.PeachBanquetApplySyncOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PeachBanquetApplySync_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PeachBanquetApplySync_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.PeachBanquetApplySync.class, xddq.pb.PeachBanquetApplySync.Builder.class);
    }

    // Construct using xddq.pb.PeachBanquetApplySync.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      banquetId_ = "";
      hasApply_ = false;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PeachBanquetApplySync_descriptor;
    }

    @java.lang.Override
    public xddq.pb.PeachBanquetApplySync getDefaultInstanceForType() {
      return xddq.pb.PeachBanquetApplySync.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.PeachBanquetApplySync build() {
      xddq.pb.PeachBanquetApplySync result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.PeachBanquetApplySync buildPartial() {
      xddq.pb.PeachBanquetApplySync result = new xddq.pb.PeachBanquetApplySync(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.PeachBanquetApplySync result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.banquetId_ = banquetId_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.hasApply_ = hasApply_;
        to_bitField0_ |= 0x00000002;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.PeachBanquetApplySync) {
        return mergeFrom((xddq.pb.PeachBanquetApplySync)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.PeachBanquetApplySync other) {
      if (other == xddq.pb.PeachBanquetApplySync.getDefaultInstance()) return this;
      if (other.hasBanquetId()) {
        banquetId_ = other.banquetId_;
        bitField0_ |= 0x00000001;
        onChanged();
      }
      if (other.hasHasApply()) {
        setHasApply(other.getHasApply());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              banquetId_ = input.readBytes();
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 16: {
              hasApply_ = input.readBool();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.lang.Object banquetId_ = "";
    /**
     * <code>optional string banquetId = 1;</code>
     * @return Whether the banquetId field is set.
     */
    public boolean hasBanquetId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional string banquetId = 1;</code>
     * @return The banquetId.
     */
    public java.lang.String getBanquetId() {
      java.lang.Object ref = banquetId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          banquetId_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string banquetId = 1;</code>
     * @return The bytes for banquetId.
     */
    public com.google.protobuf.ByteString
        getBanquetIdBytes() {
      java.lang.Object ref = banquetId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        banquetId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string banquetId = 1;</code>
     * @param value The banquetId to set.
     * @return This builder for chaining.
     */
    public Builder setBanquetId(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      banquetId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional string banquetId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearBanquetId() {
      banquetId_ = getDefaultInstance().getBanquetId();
      bitField0_ = (bitField0_ & ~0x00000001);
      onChanged();
      return this;
    }
    /**
     * <code>optional string banquetId = 1;</code>
     * @param value The bytes for banquetId to set.
     * @return This builder for chaining.
     */
    public Builder setBanquetIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      banquetId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }

    private boolean hasApply_ ;
    /**
     * <code>optional bool hasApply = 2;</code>
     * @return Whether the hasApply field is set.
     */
    @java.lang.Override
    public boolean hasHasApply() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional bool hasApply = 2;</code>
     * @return The hasApply.
     */
    @java.lang.Override
    public boolean getHasApply() {
      return hasApply_;
    }
    /**
     * <code>optional bool hasApply = 2;</code>
     * @param value The hasApply to set.
     * @return This builder for chaining.
     */
    public Builder setHasApply(boolean value) {

      hasApply_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional bool hasApply = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearHasApply() {
      bitField0_ = (bitField0_ & ~0x00000002);
      hasApply_ = false;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.PeachBanquetApplySync)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.PeachBanquetApplySync)
  private static final xddq.pb.PeachBanquetApplySync DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.PeachBanquetApplySync();
  }

  public static xddq.pb.PeachBanquetApplySync getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<PeachBanquetApplySync>
      PARSER = new com.google.protobuf.AbstractParser<PeachBanquetApplySync>() {
    @java.lang.Override
    public PeachBanquetApplySync parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<PeachBanquetApplySync> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<PeachBanquetApplySync> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.PeachBanquetApplySync getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

