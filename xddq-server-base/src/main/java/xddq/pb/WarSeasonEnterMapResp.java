// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.WarSeasonEnterMapResp}
 */
public final class WarSeasonEnterMapResp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.WarSeasonEnterMapResp)
    WarSeasonEnterMapRespOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      WarSeasonEnterMapResp.class.getName());
  }
  // Use WarSeasonEnterMapResp.newBuilder() to construct.
  private WarSeasonEnterMapResp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private WarSeasonEnterMapResp() {
    bodyStatus_ = java.util.Collections.emptyList();
    markInfo_ = java.util.Collections.emptyList();
    unionData_ = java.util.Collections.emptyList();
    moveDatas_ = java.util.Collections.emptyList();
    collectionCityArr_ = emptyIntList();
    fightData_ = java.util.Collections.emptyList();
    carData_ = java.util.Collections.emptyList();
    manorData_ = java.util.Collections.emptyList();
    params_ = "";
    passStage_ = emptyIntList();
    cityLevel_ = emptyIntList();
    curFightValue_ = "";
    orderFightData_ = java.util.Collections.emptyList();
    otherUnionStrategyList_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_WarSeasonEnterMapResp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_WarSeasonEnterMapResp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.WarSeasonEnterMapResp.class, xddq.pb.WarSeasonEnterMapResp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int CURCITYID_FIELD_NUMBER = 2;
  private int curCityId_ = 0;
  /**
   * <code>optional int32 curCityId = 2;</code>
   * @return Whether the curCityId field is set.
   */
  @java.lang.Override
  public boolean hasCurCityId() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int32 curCityId = 2;</code>
   * @return The curCityId.
   */
  @java.lang.Override
  public int getCurCityId() {
    return curCityId_;
  }

  public static final int SEASONEXP_FIELD_NUMBER = 3;
  private long seasonExp_ = 0L;
  /**
   * <code>optional int64 seasonExp = 3;</code>
   * @return Whether the seasonExp field is set.
   */
  @java.lang.Override
  public boolean hasSeasonExp() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional int64 seasonExp = 3;</code>
   * @return The seasonExp.
   */
  @java.lang.Override
  public long getSeasonExp() {
    return seasonExp_;
  }

  public static final int BODYSTATUS_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.WarSeasonGodBodyStatusDatsMsg> bodyStatus_;
  /**
   * <code>repeated .xddq.pb.WarSeasonGodBodyStatusDatsMsg bodyStatus = 4;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.WarSeasonGodBodyStatusDatsMsg> getBodyStatusList() {
    return bodyStatus_;
  }
  /**
   * <code>repeated .xddq.pb.WarSeasonGodBodyStatusDatsMsg bodyStatus = 4;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.WarSeasonGodBodyStatusDatsMsgOrBuilder> 
      getBodyStatusOrBuilderList() {
    return bodyStatus_;
  }
  /**
   * <code>repeated .xddq.pb.WarSeasonGodBodyStatusDatsMsg bodyStatus = 4;</code>
   */
  @java.lang.Override
  public int getBodyStatusCount() {
    return bodyStatus_.size();
  }
  /**
   * <code>repeated .xddq.pb.WarSeasonGodBodyStatusDatsMsg bodyStatus = 4;</code>
   */
  @java.lang.Override
  public xddq.pb.WarSeasonGodBodyStatusDatsMsg getBodyStatus(int index) {
    return bodyStatus_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.WarSeasonGodBodyStatusDatsMsg bodyStatus = 4;</code>
   */
  @java.lang.Override
  public xddq.pb.WarSeasonGodBodyStatusDatsMsgOrBuilder getBodyStatusOrBuilder(
      int index) {
    return bodyStatus_.get(index);
  }

  public static final int MARKINFO_FIELD_NUMBER = 5;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.WarSeasonCityDetailMsg> markInfo_;
  /**
   * <code>repeated .xddq.pb.WarSeasonCityDetailMsg markInfo = 5;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.WarSeasonCityDetailMsg> getMarkInfoList() {
    return markInfo_;
  }
  /**
   * <code>repeated .xddq.pb.WarSeasonCityDetailMsg markInfo = 5;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.WarSeasonCityDetailMsgOrBuilder> 
      getMarkInfoOrBuilderList() {
    return markInfo_;
  }
  /**
   * <code>repeated .xddq.pb.WarSeasonCityDetailMsg markInfo = 5;</code>
   */
  @java.lang.Override
  public int getMarkInfoCount() {
    return markInfo_.size();
  }
  /**
   * <code>repeated .xddq.pb.WarSeasonCityDetailMsg markInfo = 5;</code>
   */
  @java.lang.Override
  public xddq.pb.WarSeasonCityDetailMsg getMarkInfo(int index) {
    return markInfo_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.WarSeasonCityDetailMsg markInfo = 5;</code>
   */
  @java.lang.Override
  public xddq.pb.WarSeasonCityDetailMsgOrBuilder getMarkInfoOrBuilder(
      int index) {
    return markInfo_.get(index);
  }

  public static final int UNIONDATA_FIELD_NUMBER = 6;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.WarSeasonMapUnionDataMsg> unionData_;
  /**
   * <code>repeated .xddq.pb.WarSeasonMapUnionDataMsg unionData = 6;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.WarSeasonMapUnionDataMsg> getUnionDataList() {
    return unionData_;
  }
  /**
   * <code>repeated .xddq.pb.WarSeasonMapUnionDataMsg unionData = 6;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.WarSeasonMapUnionDataMsgOrBuilder> 
      getUnionDataOrBuilderList() {
    return unionData_;
  }
  /**
   * <code>repeated .xddq.pb.WarSeasonMapUnionDataMsg unionData = 6;</code>
   */
  @java.lang.Override
  public int getUnionDataCount() {
    return unionData_.size();
  }
  /**
   * <code>repeated .xddq.pb.WarSeasonMapUnionDataMsg unionData = 6;</code>
   */
  @java.lang.Override
  public xddq.pb.WarSeasonMapUnionDataMsg getUnionData(int index) {
    return unionData_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.WarSeasonMapUnionDataMsg unionData = 6;</code>
   */
  @java.lang.Override
  public xddq.pb.WarSeasonMapUnionDataMsgOrBuilder getUnionDataOrBuilder(
      int index) {
    return unionData_.get(index);
  }

  public static final int MOVEDATAS_FIELD_NUMBER = 7;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.WarSeasonMapMoveDataMsg> moveDatas_;
  /**
   * <code>repeated .xddq.pb.WarSeasonMapMoveDataMsg moveDatas = 7;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.WarSeasonMapMoveDataMsg> getMoveDatasList() {
    return moveDatas_;
  }
  /**
   * <code>repeated .xddq.pb.WarSeasonMapMoveDataMsg moveDatas = 7;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.WarSeasonMapMoveDataMsgOrBuilder> 
      getMoveDatasOrBuilderList() {
    return moveDatas_;
  }
  /**
   * <code>repeated .xddq.pb.WarSeasonMapMoveDataMsg moveDatas = 7;</code>
   */
  @java.lang.Override
  public int getMoveDatasCount() {
    return moveDatas_.size();
  }
  /**
   * <code>repeated .xddq.pb.WarSeasonMapMoveDataMsg moveDatas = 7;</code>
   */
  @java.lang.Override
  public xddq.pb.WarSeasonMapMoveDataMsg getMoveDatas(int index) {
    return moveDatas_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.WarSeasonMapMoveDataMsg moveDatas = 7;</code>
   */
  @java.lang.Override
  public xddq.pb.WarSeasonMapMoveDataMsgOrBuilder getMoveDatasOrBuilder(
      int index) {
    return moveDatas_.get(index);
  }

  public static final int COLLECTIONCITYARR_FIELD_NUMBER = 8;
  @SuppressWarnings("serial")
  private com.google.protobuf.Internal.IntList collectionCityArr_ =
      emptyIntList();
  /**
   * <code>repeated int32 collectionCityArr = 8;</code>
   * @return A list containing the collectionCityArr.
   */
  @java.lang.Override
  public java.util.List<java.lang.Integer>
      getCollectionCityArrList() {
    return collectionCityArr_;
  }
  /**
   * <code>repeated int32 collectionCityArr = 8;</code>
   * @return The count of collectionCityArr.
   */
  public int getCollectionCityArrCount() {
    return collectionCityArr_.size();
  }
  /**
   * <code>repeated int32 collectionCityArr = 8;</code>
   * @param index The index of the element to return.
   * @return The collectionCityArr at the given index.
   */
  public int getCollectionCityArr(int index) {
    return collectionCityArr_.getInt(index);
  }

  public static final int FIGHTDATA_FIELD_NUMBER = 9;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.WarSeasonMapDeclareFightDataMsg> fightData_;
  /**
   * <code>repeated .xddq.pb.WarSeasonMapDeclareFightDataMsg fightData = 9;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.WarSeasonMapDeclareFightDataMsg> getFightDataList() {
    return fightData_;
  }
  /**
   * <code>repeated .xddq.pb.WarSeasonMapDeclareFightDataMsg fightData = 9;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.WarSeasonMapDeclareFightDataMsgOrBuilder> 
      getFightDataOrBuilderList() {
    return fightData_;
  }
  /**
   * <code>repeated .xddq.pb.WarSeasonMapDeclareFightDataMsg fightData = 9;</code>
   */
  @java.lang.Override
  public int getFightDataCount() {
    return fightData_.size();
  }
  /**
   * <code>repeated .xddq.pb.WarSeasonMapDeclareFightDataMsg fightData = 9;</code>
   */
  @java.lang.Override
  public xddq.pb.WarSeasonMapDeclareFightDataMsg getFightData(int index) {
    return fightData_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.WarSeasonMapDeclareFightDataMsg fightData = 9;</code>
   */
  @java.lang.Override
  public xddq.pb.WarSeasonMapDeclareFightDataMsgOrBuilder getFightDataOrBuilder(
      int index) {
    return fightData_.get(index);
  }

  public static final int CARDATA_FIELD_NUMBER = 10;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.WarSeasonMapDeclareWarCarDataMsg> carData_;
  /**
   * <code>repeated .xddq.pb.WarSeasonMapDeclareWarCarDataMsg carData = 10;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.WarSeasonMapDeclareWarCarDataMsg> getCarDataList() {
    return carData_;
  }
  /**
   * <code>repeated .xddq.pb.WarSeasonMapDeclareWarCarDataMsg carData = 10;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.WarSeasonMapDeclareWarCarDataMsgOrBuilder> 
      getCarDataOrBuilderList() {
    return carData_;
  }
  /**
   * <code>repeated .xddq.pb.WarSeasonMapDeclareWarCarDataMsg carData = 10;</code>
   */
  @java.lang.Override
  public int getCarDataCount() {
    return carData_.size();
  }
  /**
   * <code>repeated .xddq.pb.WarSeasonMapDeclareWarCarDataMsg carData = 10;</code>
   */
  @java.lang.Override
  public xddq.pb.WarSeasonMapDeclareWarCarDataMsg getCarData(int index) {
    return carData_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.WarSeasonMapDeclareWarCarDataMsg carData = 10;</code>
   */
  @java.lang.Override
  public xddq.pb.WarSeasonMapDeclareWarCarDataMsgOrBuilder getCarDataOrBuilder(
      int index) {
    return carData_.get(index);
  }

  public static final int MANORDATA_FIELD_NUMBER = 11;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.WarSeasonManorDataMsg> manorData_;
  /**
   * <code>repeated .xddq.pb.WarSeasonManorDataMsg manorData = 11;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.WarSeasonManorDataMsg> getManorDataList() {
    return manorData_;
  }
  /**
   * <code>repeated .xddq.pb.WarSeasonManorDataMsg manorData = 11;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.WarSeasonManorDataMsgOrBuilder> 
      getManorDataOrBuilderList() {
    return manorData_;
  }
  /**
   * <code>repeated .xddq.pb.WarSeasonManorDataMsg manorData = 11;</code>
   */
  @java.lang.Override
  public int getManorDataCount() {
    return manorData_.size();
  }
  /**
   * <code>repeated .xddq.pb.WarSeasonManorDataMsg manorData = 11;</code>
   */
  @java.lang.Override
  public xddq.pb.WarSeasonManorDataMsg getManorData(int index) {
    return manorData_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.WarSeasonManorDataMsg manorData = 11;</code>
   */
  @java.lang.Override
  public xddq.pb.WarSeasonManorDataMsgOrBuilder getManorDataOrBuilder(
      int index) {
    return manorData_.get(index);
  }

  public static final int LASTMOVECAPITALTIME_FIELD_NUMBER = 12;
  private long lastMoveCapitalTime_ = 0L;
  /**
   * <code>optional int64 lastMoveCapitalTime = 12;</code>
   * @return Whether the lastMoveCapitalTime field is set.
   */
  @java.lang.Override
  public boolean hasLastMoveCapitalTime() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional int64 lastMoveCapitalTime = 12;</code>
   * @return The lastMoveCapitalTime.
   */
  @java.lang.Override
  public long getLastMoveCapitalTime() {
    return lastMoveCapitalTime_;
  }

  public static final int PARAMS_FIELD_NUMBER = 13;
  @SuppressWarnings("serial")
  private volatile java.lang.Object params_ = "";
  /**
   * <code>optional string params = 13;</code>
   * @return Whether the params field is set.
   */
  @java.lang.Override
  public boolean hasParams() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>optional string params = 13;</code>
   * @return The params.
   */
  @java.lang.Override
  public java.lang.String getParams() {
    java.lang.Object ref = params_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        params_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string params = 13;</code>
   * @return The bytes for params.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getParamsBytes() {
    java.lang.Object ref = params_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      params_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PASSSTAGE_FIELD_NUMBER = 14;
  @SuppressWarnings("serial")
  private com.google.protobuf.Internal.IntList passStage_ =
      emptyIntList();
  /**
   * <code>repeated int32 passStage = 14;</code>
   * @return A list containing the passStage.
   */
  @java.lang.Override
  public java.util.List<java.lang.Integer>
      getPassStageList() {
    return passStage_;
  }
  /**
   * <code>repeated int32 passStage = 14;</code>
   * @return The count of passStage.
   */
  public int getPassStageCount() {
    return passStage_.size();
  }
  /**
   * <code>repeated int32 passStage = 14;</code>
   * @param index The index of the element to return.
   * @return The passStage at the given index.
   */
  public int getPassStage(int index) {
    return passStage_.getInt(index);
  }

  public static final int CHALLENGETIMES_FIELD_NUMBER = 15;
  private int challengeTimes_ = 0;
  /**
   * <code>optional int32 challengeTimes = 15;</code>
   * @return Whether the challengeTimes field is set.
   */
  @java.lang.Override
  public boolean hasChallengeTimes() {
    return ((bitField0_ & 0x00000020) != 0);
  }
  /**
   * <code>optional int32 challengeTimes = 15;</code>
   * @return The challengeTimes.
   */
  @java.lang.Override
  public int getChallengeTimes() {
    return challengeTimes_;
  }

  public static final int ITEMCHALLENGETIMES_FIELD_NUMBER = 16;
  private int itemChallengeTimes_ = 0;
  /**
   * <code>optional int32 itemChallengeTimes = 16;</code>
   * @return Whether the itemChallengeTimes field is set.
   */
  @java.lang.Override
  public boolean hasItemChallengeTimes() {
    return ((bitField0_ & 0x00000040) != 0);
  }
  /**
   * <code>optional int32 itemChallengeTimes = 16;</code>
   * @return The itemChallengeTimes.
   */
  @java.lang.Override
  public int getItemChallengeTimes() {
    return itemChallengeTimes_;
  }

  public static final int BATTLETIMES_FIELD_NUMBER = 17;
  private int battleTimes_ = 0;
  /**
   * <code>optional int32 battleTimes = 17;</code>
   * @return Whether the battleTimes field is set.
   */
  @java.lang.Override
  public boolean hasBattleTimes() {
    return ((bitField0_ & 0x00000080) != 0);
  }
  /**
   * <code>optional int32 battleTimes = 17;</code>
   * @return The battleTimes.
   */
  @java.lang.Override
  public int getBattleTimes() {
    return battleTimes_;
  }

  public static final int CITYLEVEL_FIELD_NUMBER = 18;
  @SuppressWarnings("serial")
  private com.google.protobuf.Internal.IntList cityLevel_ =
      emptyIntList();
  /**
   * <code>repeated int32 cityLevel = 18;</code>
   * @return A list containing the cityLevel.
   */
  @java.lang.Override
  public java.util.List<java.lang.Integer>
      getCityLevelList() {
    return cityLevel_;
  }
  /**
   * <code>repeated int32 cityLevel = 18;</code>
   * @return The count of cityLevel.
   */
  public int getCityLevelCount() {
    return cityLevel_.size();
  }
  /**
   * <code>repeated int32 cityLevel = 18;</code>
   * @param index The index of the element to return.
   * @return The cityLevel at the given index.
   */
  public int getCityLevel(int index) {
    return cityLevel_.getInt(index);
  }

  public static final int CURFIGHTVALUE_FIELD_NUMBER = 19;
  @SuppressWarnings("serial")
  private volatile java.lang.Object curFightValue_ = "";
  /**
   * <code>optional string curFightValue = 19;</code>
   * @return Whether the curFightValue field is set.
   */
  @java.lang.Override
  public boolean hasCurFightValue() {
    return ((bitField0_ & 0x00000100) != 0);
  }
  /**
   * <code>optional string curFightValue = 19;</code>
   * @return The curFightValue.
   */
  @java.lang.Override
  public java.lang.String getCurFightValue() {
    java.lang.Object ref = curFightValue_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        curFightValue_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string curFightValue = 19;</code>
   * @return The bytes for curFightValue.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getCurFightValueBytes() {
    java.lang.Object ref = curFightValue_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      curFightValue_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ORDERFIGHTDATA_FIELD_NUMBER = 20;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.WarSeasonOrderDeclareFightDataMsg> orderFightData_;
  /**
   * <code>repeated .xddq.pb.WarSeasonOrderDeclareFightDataMsg orderFightData = 20;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.WarSeasonOrderDeclareFightDataMsg> getOrderFightDataList() {
    return orderFightData_;
  }
  /**
   * <code>repeated .xddq.pb.WarSeasonOrderDeclareFightDataMsg orderFightData = 20;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.WarSeasonOrderDeclareFightDataMsgOrBuilder> 
      getOrderFightDataOrBuilderList() {
    return orderFightData_;
  }
  /**
   * <code>repeated .xddq.pb.WarSeasonOrderDeclareFightDataMsg orderFightData = 20;</code>
   */
  @java.lang.Override
  public int getOrderFightDataCount() {
    return orderFightData_.size();
  }
  /**
   * <code>repeated .xddq.pb.WarSeasonOrderDeclareFightDataMsg orderFightData = 20;</code>
   */
  @java.lang.Override
  public xddq.pb.WarSeasonOrderDeclareFightDataMsg getOrderFightData(int index) {
    return orderFightData_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.WarSeasonOrderDeclareFightDataMsg orderFightData = 20;</code>
   */
  @java.lang.Override
  public xddq.pb.WarSeasonOrderDeclareFightDataMsgOrBuilder getOrderFightDataOrBuilder(
      int index) {
    return orderFightData_.get(index);
  }

  public static final int OTHERUNIONSTRATEGYLIST_FIELD_NUMBER = 21;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.WarSeasonEffectStrategyInfo> otherUnionStrategyList_;
  /**
   * <code>repeated .xddq.pb.WarSeasonEffectStrategyInfo otherUnionStrategyList = 21;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.WarSeasonEffectStrategyInfo> getOtherUnionStrategyListList() {
    return otherUnionStrategyList_;
  }
  /**
   * <code>repeated .xddq.pb.WarSeasonEffectStrategyInfo otherUnionStrategyList = 21;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.WarSeasonEffectStrategyInfoOrBuilder> 
      getOtherUnionStrategyListOrBuilderList() {
    return otherUnionStrategyList_;
  }
  /**
   * <code>repeated .xddq.pb.WarSeasonEffectStrategyInfo otherUnionStrategyList = 21;</code>
   */
  @java.lang.Override
  public int getOtherUnionStrategyListCount() {
    return otherUnionStrategyList_.size();
  }
  /**
   * <code>repeated .xddq.pb.WarSeasonEffectStrategyInfo otherUnionStrategyList = 21;</code>
   */
  @java.lang.Override
  public xddq.pb.WarSeasonEffectStrategyInfo getOtherUnionStrategyList(int index) {
    return otherUnionStrategyList_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.WarSeasonEffectStrategyInfo otherUnionStrategyList = 21;</code>
   */
  @java.lang.Override
  public xddq.pb.WarSeasonEffectStrategyInfoOrBuilder getOtherUnionStrategyListOrBuilder(
      int index) {
    return otherUnionStrategyList_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    for (int i = 0; i < getFightDataCount(); i++) {
      if (!getFightData(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    for (int i = 0; i < getOtherUnionStrategyListCount(); i++) {
      if (!getOtherUnionStrategyList(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(2, curCityId_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt64(3, seasonExp_);
    }
    for (int i = 0; i < bodyStatus_.size(); i++) {
      output.writeMessage(4, bodyStatus_.get(i));
    }
    for (int i = 0; i < markInfo_.size(); i++) {
      output.writeMessage(5, markInfo_.get(i));
    }
    for (int i = 0; i < unionData_.size(); i++) {
      output.writeMessage(6, unionData_.get(i));
    }
    for (int i = 0; i < moveDatas_.size(); i++) {
      output.writeMessage(7, moveDatas_.get(i));
    }
    for (int i = 0; i < collectionCityArr_.size(); i++) {
      output.writeInt32(8, collectionCityArr_.getInt(i));
    }
    for (int i = 0; i < fightData_.size(); i++) {
      output.writeMessage(9, fightData_.get(i));
    }
    for (int i = 0; i < carData_.size(); i++) {
      output.writeMessage(10, carData_.get(i));
    }
    for (int i = 0; i < manorData_.size(); i++) {
      output.writeMessage(11, manorData_.get(i));
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt64(12, lastMoveCapitalTime_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 13, params_);
    }
    for (int i = 0; i < passStage_.size(); i++) {
      output.writeInt32(14, passStage_.getInt(i));
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      output.writeInt32(15, challengeTimes_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      output.writeInt32(16, itemChallengeTimes_);
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      output.writeInt32(17, battleTimes_);
    }
    for (int i = 0; i < cityLevel_.size(); i++) {
      output.writeInt32(18, cityLevel_.getInt(i));
    }
    if (((bitField0_ & 0x00000100) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 19, curFightValue_);
    }
    for (int i = 0; i < orderFightData_.size(); i++) {
      output.writeMessage(20, orderFightData_.get(i));
    }
    for (int i = 0; i < otherUnionStrategyList_.size(); i++) {
      output.writeMessage(21, otherUnionStrategyList_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, curCityId_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(3, seasonExp_);
    }
    for (int i = 0; i < bodyStatus_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(4, bodyStatus_.get(i));
    }
    for (int i = 0; i < markInfo_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(5, markInfo_.get(i));
    }
    for (int i = 0; i < unionData_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(6, unionData_.get(i));
    }
    for (int i = 0; i < moveDatas_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(7, moveDatas_.get(i));
    }
    {
      int dataSize = 0;
      for (int i = 0; i < collectionCityArr_.size(); i++) {
        dataSize += com.google.protobuf.CodedOutputStream
          .computeInt32SizeNoTag(collectionCityArr_.getInt(i));
      }
      size += dataSize;
      size += 1 * getCollectionCityArrList().size();
    }
    for (int i = 0; i < fightData_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(9, fightData_.get(i));
    }
    for (int i = 0; i < carData_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(10, carData_.get(i));
    }
    for (int i = 0; i < manorData_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(11, manorData_.get(i));
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(12, lastMoveCapitalTime_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(13, params_);
    }
    {
      int dataSize = 0;
      for (int i = 0; i < passStage_.size(); i++) {
        dataSize += com.google.protobuf.CodedOutputStream
          .computeInt32SizeNoTag(passStage_.getInt(i));
      }
      size += dataSize;
      size += 1 * getPassStageList().size();
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(15, challengeTimes_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(16, itemChallengeTimes_);
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(17, battleTimes_);
    }
    {
      int dataSize = 0;
      for (int i = 0; i < cityLevel_.size(); i++) {
        dataSize += com.google.protobuf.CodedOutputStream
          .computeInt32SizeNoTag(cityLevel_.getInt(i));
      }
      size += dataSize;
      size += 2 * getCityLevelList().size();
    }
    if (((bitField0_ & 0x00000100) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(19, curFightValue_);
    }
    for (int i = 0; i < orderFightData_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(20, orderFightData_.get(i));
    }
    for (int i = 0; i < otherUnionStrategyList_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(21, otherUnionStrategyList_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.WarSeasonEnterMapResp)) {
      return super.equals(obj);
    }
    xddq.pb.WarSeasonEnterMapResp other = (xddq.pb.WarSeasonEnterMapResp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (hasCurCityId() != other.hasCurCityId()) return false;
    if (hasCurCityId()) {
      if (getCurCityId()
          != other.getCurCityId()) return false;
    }
    if (hasSeasonExp() != other.hasSeasonExp()) return false;
    if (hasSeasonExp()) {
      if (getSeasonExp()
          != other.getSeasonExp()) return false;
    }
    if (!getBodyStatusList()
        .equals(other.getBodyStatusList())) return false;
    if (!getMarkInfoList()
        .equals(other.getMarkInfoList())) return false;
    if (!getUnionDataList()
        .equals(other.getUnionDataList())) return false;
    if (!getMoveDatasList()
        .equals(other.getMoveDatasList())) return false;
    if (!getCollectionCityArrList()
        .equals(other.getCollectionCityArrList())) return false;
    if (!getFightDataList()
        .equals(other.getFightDataList())) return false;
    if (!getCarDataList()
        .equals(other.getCarDataList())) return false;
    if (!getManorDataList()
        .equals(other.getManorDataList())) return false;
    if (hasLastMoveCapitalTime() != other.hasLastMoveCapitalTime()) return false;
    if (hasLastMoveCapitalTime()) {
      if (getLastMoveCapitalTime()
          != other.getLastMoveCapitalTime()) return false;
    }
    if (hasParams() != other.hasParams()) return false;
    if (hasParams()) {
      if (!getParams()
          .equals(other.getParams())) return false;
    }
    if (!getPassStageList()
        .equals(other.getPassStageList())) return false;
    if (hasChallengeTimes() != other.hasChallengeTimes()) return false;
    if (hasChallengeTimes()) {
      if (getChallengeTimes()
          != other.getChallengeTimes()) return false;
    }
    if (hasItemChallengeTimes() != other.hasItemChallengeTimes()) return false;
    if (hasItemChallengeTimes()) {
      if (getItemChallengeTimes()
          != other.getItemChallengeTimes()) return false;
    }
    if (hasBattleTimes() != other.hasBattleTimes()) return false;
    if (hasBattleTimes()) {
      if (getBattleTimes()
          != other.getBattleTimes()) return false;
    }
    if (!getCityLevelList()
        .equals(other.getCityLevelList())) return false;
    if (hasCurFightValue() != other.hasCurFightValue()) return false;
    if (hasCurFightValue()) {
      if (!getCurFightValue()
          .equals(other.getCurFightValue())) return false;
    }
    if (!getOrderFightDataList()
        .equals(other.getOrderFightDataList())) return false;
    if (!getOtherUnionStrategyListList()
        .equals(other.getOtherUnionStrategyListList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (hasCurCityId()) {
      hash = (37 * hash) + CURCITYID_FIELD_NUMBER;
      hash = (53 * hash) + getCurCityId();
    }
    if (hasSeasonExp()) {
      hash = (37 * hash) + SEASONEXP_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getSeasonExp());
    }
    if (getBodyStatusCount() > 0) {
      hash = (37 * hash) + BODYSTATUS_FIELD_NUMBER;
      hash = (53 * hash) + getBodyStatusList().hashCode();
    }
    if (getMarkInfoCount() > 0) {
      hash = (37 * hash) + MARKINFO_FIELD_NUMBER;
      hash = (53 * hash) + getMarkInfoList().hashCode();
    }
    if (getUnionDataCount() > 0) {
      hash = (37 * hash) + UNIONDATA_FIELD_NUMBER;
      hash = (53 * hash) + getUnionDataList().hashCode();
    }
    if (getMoveDatasCount() > 0) {
      hash = (37 * hash) + MOVEDATAS_FIELD_NUMBER;
      hash = (53 * hash) + getMoveDatasList().hashCode();
    }
    if (getCollectionCityArrCount() > 0) {
      hash = (37 * hash) + COLLECTIONCITYARR_FIELD_NUMBER;
      hash = (53 * hash) + getCollectionCityArrList().hashCode();
    }
    if (getFightDataCount() > 0) {
      hash = (37 * hash) + FIGHTDATA_FIELD_NUMBER;
      hash = (53 * hash) + getFightDataList().hashCode();
    }
    if (getCarDataCount() > 0) {
      hash = (37 * hash) + CARDATA_FIELD_NUMBER;
      hash = (53 * hash) + getCarDataList().hashCode();
    }
    if (getManorDataCount() > 0) {
      hash = (37 * hash) + MANORDATA_FIELD_NUMBER;
      hash = (53 * hash) + getManorDataList().hashCode();
    }
    if (hasLastMoveCapitalTime()) {
      hash = (37 * hash) + LASTMOVECAPITALTIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getLastMoveCapitalTime());
    }
    if (hasParams()) {
      hash = (37 * hash) + PARAMS_FIELD_NUMBER;
      hash = (53 * hash) + getParams().hashCode();
    }
    if (getPassStageCount() > 0) {
      hash = (37 * hash) + PASSSTAGE_FIELD_NUMBER;
      hash = (53 * hash) + getPassStageList().hashCode();
    }
    if (hasChallengeTimes()) {
      hash = (37 * hash) + CHALLENGETIMES_FIELD_NUMBER;
      hash = (53 * hash) + getChallengeTimes();
    }
    if (hasItemChallengeTimes()) {
      hash = (37 * hash) + ITEMCHALLENGETIMES_FIELD_NUMBER;
      hash = (53 * hash) + getItemChallengeTimes();
    }
    if (hasBattleTimes()) {
      hash = (37 * hash) + BATTLETIMES_FIELD_NUMBER;
      hash = (53 * hash) + getBattleTimes();
    }
    if (getCityLevelCount() > 0) {
      hash = (37 * hash) + CITYLEVEL_FIELD_NUMBER;
      hash = (53 * hash) + getCityLevelList().hashCode();
    }
    if (hasCurFightValue()) {
      hash = (37 * hash) + CURFIGHTVALUE_FIELD_NUMBER;
      hash = (53 * hash) + getCurFightValue().hashCode();
    }
    if (getOrderFightDataCount() > 0) {
      hash = (37 * hash) + ORDERFIGHTDATA_FIELD_NUMBER;
      hash = (53 * hash) + getOrderFightDataList().hashCode();
    }
    if (getOtherUnionStrategyListCount() > 0) {
      hash = (37 * hash) + OTHERUNIONSTRATEGYLIST_FIELD_NUMBER;
      hash = (53 * hash) + getOtherUnionStrategyListList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.WarSeasonEnterMapResp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.WarSeasonEnterMapResp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.WarSeasonEnterMapResp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.WarSeasonEnterMapResp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.WarSeasonEnterMapResp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.WarSeasonEnterMapResp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.WarSeasonEnterMapResp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.WarSeasonEnterMapResp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.WarSeasonEnterMapResp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.WarSeasonEnterMapResp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.WarSeasonEnterMapResp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.WarSeasonEnterMapResp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.WarSeasonEnterMapResp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.WarSeasonEnterMapResp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.WarSeasonEnterMapResp)
      xddq.pb.WarSeasonEnterMapRespOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_WarSeasonEnterMapResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_WarSeasonEnterMapResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.WarSeasonEnterMapResp.class, xddq.pb.WarSeasonEnterMapResp.Builder.class);
    }

    // Construct using xddq.pb.WarSeasonEnterMapResp.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      curCityId_ = 0;
      seasonExp_ = 0L;
      if (bodyStatusBuilder_ == null) {
        bodyStatus_ = java.util.Collections.emptyList();
      } else {
        bodyStatus_ = null;
        bodyStatusBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000008);
      if (markInfoBuilder_ == null) {
        markInfo_ = java.util.Collections.emptyList();
      } else {
        markInfo_ = null;
        markInfoBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000010);
      if (unionDataBuilder_ == null) {
        unionData_ = java.util.Collections.emptyList();
      } else {
        unionData_ = null;
        unionDataBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000020);
      if (moveDatasBuilder_ == null) {
        moveDatas_ = java.util.Collections.emptyList();
      } else {
        moveDatas_ = null;
        moveDatasBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000040);
      collectionCityArr_ = emptyIntList();
      if (fightDataBuilder_ == null) {
        fightData_ = java.util.Collections.emptyList();
      } else {
        fightData_ = null;
        fightDataBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000100);
      if (carDataBuilder_ == null) {
        carData_ = java.util.Collections.emptyList();
      } else {
        carData_ = null;
        carDataBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000200);
      if (manorDataBuilder_ == null) {
        manorData_ = java.util.Collections.emptyList();
      } else {
        manorData_ = null;
        manorDataBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000400);
      lastMoveCapitalTime_ = 0L;
      params_ = "";
      passStage_ = emptyIntList();
      challengeTimes_ = 0;
      itemChallengeTimes_ = 0;
      battleTimes_ = 0;
      cityLevel_ = emptyIntList();
      curFightValue_ = "";
      if (orderFightDataBuilder_ == null) {
        orderFightData_ = java.util.Collections.emptyList();
      } else {
        orderFightData_ = null;
        orderFightDataBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00080000);
      if (otherUnionStrategyListBuilder_ == null) {
        otherUnionStrategyList_ = java.util.Collections.emptyList();
      } else {
        otherUnionStrategyList_ = null;
        otherUnionStrategyListBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00100000);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_WarSeasonEnterMapResp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.WarSeasonEnterMapResp getDefaultInstanceForType() {
      return xddq.pb.WarSeasonEnterMapResp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.WarSeasonEnterMapResp build() {
      xddq.pb.WarSeasonEnterMapResp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.WarSeasonEnterMapResp buildPartial() {
      xddq.pb.WarSeasonEnterMapResp result = new xddq.pb.WarSeasonEnterMapResp(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.WarSeasonEnterMapResp result) {
      if (bodyStatusBuilder_ == null) {
        if (((bitField0_ & 0x00000008) != 0)) {
          bodyStatus_ = java.util.Collections.unmodifiableList(bodyStatus_);
          bitField0_ = (bitField0_ & ~0x00000008);
        }
        result.bodyStatus_ = bodyStatus_;
      } else {
        result.bodyStatus_ = bodyStatusBuilder_.build();
      }
      if (markInfoBuilder_ == null) {
        if (((bitField0_ & 0x00000010) != 0)) {
          markInfo_ = java.util.Collections.unmodifiableList(markInfo_);
          bitField0_ = (bitField0_ & ~0x00000010);
        }
        result.markInfo_ = markInfo_;
      } else {
        result.markInfo_ = markInfoBuilder_.build();
      }
      if (unionDataBuilder_ == null) {
        if (((bitField0_ & 0x00000020) != 0)) {
          unionData_ = java.util.Collections.unmodifiableList(unionData_);
          bitField0_ = (bitField0_ & ~0x00000020);
        }
        result.unionData_ = unionData_;
      } else {
        result.unionData_ = unionDataBuilder_.build();
      }
      if (moveDatasBuilder_ == null) {
        if (((bitField0_ & 0x00000040) != 0)) {
          moveDatas_ = java.util.Collections.unmodifiableList(moveDatas_);
          bitField0_ = (bitField0_ & ~0x00000040);
        }
        result.moveDatas_ = moveDatas_;
      } else {
        result.moveDatas_ = moveDatasBuilder_.build();
      }
      if (fightDataBuilder_ == null) {
        if (((bitField0_ & 0x00000100) != 0)) {
          fightData_ = java.util.Collections.unmodifiableList(fightData_);
          bitField0_ = (bitField0_ & ~0x00000100);
        }
        result.fightData_ = fightData_;
      } else {
        result.fightData_ = fightDataBuilder_.build();
      }
      if (carDataBuilder_ == null) {
        if (((bitField0_ & 0x00000200) != 0)) {
          carData_ = java.util.Collections.unmodifiableList(carData_);
          bitField0_ = (bitField0_ & ~0x00000200);
        }
        result.carData_ = carData_;
      } else {
        result.carData_ = carDataBuilder_.build();
      }
      if (manorDataBuilder_ == null) {
        if (((bitField0_ & 0x00000400) != 0)) {
          manorData_ = java.util.Collections.unmodifiableList(manorData_);
          bitField0_ = (bitField0_ & ~0x00000400);
        }
        result.manorData_ = manorData_;
      } else {
        result.manorData_ = manorDataBuilder_.build();
      }
      if (orderFightDataBuilder_ == null) {
        if (((bitField0_ & 0x00080000) != 0)) {
          orderFightData_ = java.util.Collections.unmodifiableList(orderFightData_);
          bitField0_ = (bitField0_ & ~0x00080000);
        }
        result.orderFightData_ = orderFightData_;
      } else {
        result.orderFightData_ = orderFightDataBuilder_.build();
      }
      if (otherUnionStrategyListBuilder_ == null) {
        if (((bitField0_ & 0x00100000) != 0)) {
          otherUnionStrategyList_ = java.util.Collections.unmodifiableList(otherUnionStrategyList_);
          bitField0_ = (bitField0_ & ~0x00100000);
        }
        result.otherUnionStrategyList_ = otherUnionStrategyList_;
      } else {
        result.otherUnionStrategyList_ = otherUnionStrategyListBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.WarSeasonEnterMapResp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.curCityId_ = curCityId_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.seasonExp_ = seasonExp_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000080) != 0)) {
        collectionCityArr_.makeImmutable();
        result.collectionCityArr_ = collectionCityArr_;
      }
      if (((from_bitField0_ & 0x00000800) != 0)) {
        result.lastMoveCapitalTime_ = lastMoveCapitalTime_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00001000) != 0)) {
        result.params_ = params_;
        to_bitField0_ |= 0x00000010;
      }
      if (((from_bitField0_ & 0x00002000) != 0)) {
        passStage_.makeImmutable();
        result.passStage_ = passStage_;
      }
      if (((from_bitField0_ & 0x00004000) != 0)) {
        result.challengeTimes_ = challengeTimes_;
        to_bitField0_ |= 0x00000020;
      }
      if (((from_bitField0_ & 0x00008000) != 0)) {
        result.itemChallengeTimes_ = itemChallengeTimes_;
        to_bitField0_ |= 0x00000040;
      }
      if (((from_bitField0_ & 0x00010000) != 0)) {
        result.battleTimes_ = battleTimes_;
        to_bitField0_ |= 0x00000080;
      }
      if (((from_bitField0_ & 0x00020000) != 0)) {
        cityLevel_.makeImmutable();
        result.cityLevel_ = cityLevel_;
      }
      if (((from_bitField0_ & 0x00040000) != 0)) {
        result.curFightValue_ = curFightValue_;
        to_bitField0_ |= 0x00000100;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.WarSeasonEnterMapResp) {
        return mergeFrom((xddq.pb.WarSeasonEnterMapResp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.WarSeasonEnterMapResp other) {
      if (other == xddq.pb.WarSeasonEnterMapResp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (other.hasCurCityId()) {
        setCurCityId(other.getCurCityId());
      }
      if (other.hasSeasonExp()) {
        setSeasonExp(other.getSeasonExp());
      }
      if (bodyStatusBuilder_ == null) {
        if (!other.bodyStatus_.isEmpty()) {
          if (bodyStatus_.isEmpty()) {
            bodyStatus_ = other.bodyStatus_;
            bitField0_ = (bitField0_ & ~0x00000008);
          } else {
            ensureBodyStatusIsMutable();
            bodyStatus_.addAll(other.bodyStatus_);
          }
          onChanged();
        }
      } else {
        if (!other.bodyStatus_.isEmpty()) {
          if (bodyStatusBuilder_.isEmpty()) {
            bodyStatusBuilder_.dispose();
            bodyStatusBuilder_ = null;
            bodyStatus_ = other.bodyStatus_;
            bitField0_ = (bitField0_ & ~0x00000008);
            bodyStatusBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetBodyStatusFieldBuilder() : null;
          } else {
            bodyStatusBuilder_.addAllMessages(other.bodyStatus_);
          }
        }
      }
      if (markInfoBuilder_ == null) {
        if (!other.markInfo_.isEmpty()) {
          if (markInfo_.isEmpty()) {
            markInfo_ = other.markInfo_;
            bitField0_ = (bitField0_ & ~0x00000010);
          } else {
            ensureMarkInfoIsMutable();
            markInfo_.addAll(other.markInfo_);
          }
          onChanged();
        }
      } else {
        if (!other.markInfo_.isEmpty()) {
          if (markInfoBuilder_.isEmpty()) {
            markInfoBuilder_.dispose();
            markInfoBuilder_ = null;
            markInfo_ = other.markInfo_;
            bitField0_ = (bitField0_ & ~0x00000010);
            markInfoBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetMarkInfoFieldBuilder() : null;
          } else {
            markInfoBuilder_.addAllMessages(other.markInfo_);
          }
        }
      }
      if (unionDataBuilder_ == null) {
        if (!other.unionData_.isEmpty()) {
          if (unionData_.isEmpty()) {
            unionData_ = other.unionData_;
            bitField0_ = (bitField0_ & ~0x00000020);
          } else {
            ensureUnionDataIsMutable();
            unionData_.addAll(other.unionData_);
          }
          onChanged();
        }
      } else {
        if (!other.unionData_.isEmpty()) {
          if (unionDataBuilder_.isEmpty()) {
            unionDataBuilder_.dispose();
            unionDataBuilder_ = null;
            unionData_ = other.unionData_;
            bitField0_ = (bitField0_ & ~0x00000020);
            unionDataBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetUnionDataFieldBuilder() : null;
          } else {
            unionDataBuilder_.addAllMessages(other.unionData_);
          }
        }
      }
      if (moveDatasBuilder_ == null) {
        if (!other.moveDatas_.isEmpty()) {
          if (moveDatas_.isEmpty()) {
            moveDatas_ = other.moveDatas_;
            bitField0_ = (bitField0_ & ~0x00000040);
          } else {
            ensureMoveDatasIsMutable();
            moveDatas_.addAll(other.moveDatas_);
          }
          onChanged();
        }
      } else {
        if (!other.moveDatas_.isEmpty()) {
          if (moveDatasBuilder_.isEmpty()) {
            moveDatasBuilder_.dispose();
            moveDatasBuilder_ = null;
            moveDatas_ = other.moveDatas_;
            bitField0_ = (bitField0_ & ~0x00000040);
            moveDatasBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetMoveDatasFieldBuilder() : null;
          } else {
            moveDatasBuilder_.addAllMessages(other.moveDatas_);
          }
        }
      }
      if (!other.collectionCityArr_.isEmpty()) {
        if (collectionCityArr_.isEmpty()) {
          collectionCityArr_ = other.collectionCityArr_;
          collectionCityArr_.makeImmutable();
          bitField0_ |= 0x00000080;
        } else {
          ensureCollectionCityArrIsMutable();
          collectionCityArr_.addAll(other.collectionCityArr_);
        }
        onChanged();
      }
      if (fightDataBuilder_ == null) {
        if (!other.fightData_.isEmpty()) {
          if (fightData_.isEmpty()) {
            fightData_ = other.fightData_;
            bitField0_ = (bitField0_ & ~0x00000100);
          } else {
            ensureFightDataIsMutable();
            fightData_.addAll(other.fightData_);
          }
          onChanged();
        }
      } else {
        if (!other.fightData_.isEmpty()) {
          if (fightDataBuilder_.isEmpty()) {
            fightDataBuilder_.dispose();
            fightDataBuilder_ = null;
            fightData_ = other.fightData_;
            bitField0_ = (bitField0_ & ~0x00000100);
            fightDataBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetFightDataFieldBuilder() : null;
          } else {
            fightDataBuilder_.addAllMessages(other.fightData_);
          }
        }
      }
      if (carDataBuilder_ == null) {
        if (!other.carData_.isEmpty()) {
          if (carData_.isEmpty()) {
            carData_ = other.carData_;
            bitField0_ = (bitField0_ & ~0x00000200);
          } else {
            ensureCarDataIsMutable();
            carData_.addAll(other.carData_);
          }
          onChanged();
        }
      } else {
        if (!other.carData_.isEmpty()) {
          if (carDataBuilder_.isEmpty()) {
            carDataBuilder_.dispose();
            carDataBuilder_ = null;
            carData_ = other.carData_;
            bitField0_ = (bitField0_ & ~0x00000200);
            carDataBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetCarDataFieldBuilder() : null;
          } else {
            carDataBuilder_.addAllMessages(other.carData_);
          }
        }
      }
      if (manorDataBuilder_ == null) {
        if (!other.manorData_.isEmpty()) {
          if (manorData_.isEmpty()) {
            manorData_ = other.manorData_;
            bitField0_ = (bitField0_ & ~0x00000400);
          } else {
            ensureManorDataIsMutable();
            manorData_.addAll(other.manorData_);
          }
          onChanged();
        }
      } else {
        if (!other.manorData_.isEmpty()) {
          if (manorDataBuilder_.isEmpty()) {
            manorDataBuilder_.dispose();
            manorDataBuilder_ = null;
            manorData_ = other.manorData_;
            bitField0_ = (bitField0_ & ~0x00000400);
            manorDataBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetManorDataFieldBuilder() : null;
          } else {
            manorDataBuilder_.addAllMessages(other.manorData_);
          }
        }
      }
      if (other.hasLastMoveCapitalTime()) {
        setLastMoveCapitalTime(other.getLastMoveCapitalTime());
      }
      if (other.hasParams()) {
        params_ = other.params_;
        bitField0_ |= 0x00001000;
        onChanged();
      }
      if (!other.passStage_.isEmpty()) {
        if (passStage_.isEmpty()) {
          passStage_ = other.passStage_;
          passStage_.makeImmutable();
          bitField0_ |= 0x00002000;
        } else {
          ensurePassStageIsMutable();
          passStage_.addAll(other.passStage_);
        }
        onChanged();
      }
      if (other.hasChallengeTimes()) {
        setChallengeTimes(other.getChallengeTimes());
      }
      if (other.hasItemChallengeTimes()) {
        setItemChallengeTimes(other.getItemChallengeTimes());
      }
      if (other.hasBattleTimes()) {
        setBattleTimes(other.getBattleTimes());
      }
      if (!other.cityLevel_.isEmpty()) {
        if (cityLevel_.isEmpty()) {
          cityLevel_ = other.cityLevel_;
          cityLevel_.makeImmutable();
          bitField0_ |= 0x00020000;
        } else {
          ensureCityLevelIsMutable();
          cityLevel_.addAll(other.cityLevel_);
        }
        onChanged();
      }
      if (other.hasCurFightValue()) {
        curFightValue_ = other.curFightValue_;
        bitField0_ |= 0x00040000;
        onChanged();
      }
      if (orderFightDataBuilder_ == null) {
        if (!other.orderFightData_.isEmpty()) {
          if (orderFightData_.isEmpty()) {
            orderFightData_ = other.orderFightData_;
            bitField0_ = (bitField0_ & ~0x00080000);
          } else {
            ensureOrderFightDataIsMutable();
            orderFightData_.addAll(other.orderFightData_);
          }
          onChanged();
        }
      } else {
        if (!other.orderFightData_.isEmpty()) {
          if (orderFightDataBuilder_.isEmpty()) {
            orderFightDataBuilder_.dispose();
            orderFightDataBuilder_ = null;
            orderFightData_ = other.orderFightData_;
            bitField0_ = (bitField0_ & ~0x00080000);
            orderFightDataBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetOrderFightDataFieldBuilder() : null;
          } else {
            orderFightDataBuilder_.addAllMessages(other.orderFightData_);
          }
        }
      }
      if (otherUnionStrategyListBuilder_ == null) {
        if (!other.otherUnionStrategyList_.isEmpty()) {
          if (otherUnionStrategyList_.isEmpty()) {
            otherUnionStrategyList_ = other.otherUnionStrategyList_;
            bitField0_ = (bitField0_ & ~0x00100000);
          } else {
            ensureOtherUnionStrategyListIsMutable();
            otherUnionStrategyList_.addAll(other.otherUnionStrategyList_);
          }
          onChanged();
        }
      } else {
        if (!other.otherUnionStrategyList_.isEmpty()) {
          if (otherUnionStrategyListBuilder_.isEmpty()) {
            otherUnionStrategyListBuilder_.dispose();
            otherUnionStrategyListBuilder_ = null;
            otherUnionStrategyList_ = other.otherUnionStrategyList_;
            bitField0_ = (bitField0_ & ~0x00100000);
            otherUnionStrategyListBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetOtherUnionStrategyListFieldBuilder() : null;
          } else {
            otherUnionStrategyListBuilder_.addAllMessages(other.otherUnionStrategyList_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      for (int i = 0; i < getFightDataCount(); i++) {
        if (!getFightData(i).isInitialized()) {
          return false;
        }
      }
      for (int i = 0; i < getOtherUnionStrategyListCount(); i++) {
        if (!getOtherUnionStrategyList(i).isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              curCityId_ = input.readInt32();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 24: {
              seasonExp_ = input.readInt64();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 34: {
              xddq.pb.WarSeasonGodBodyStatusDatsMsg m =
                  input.readMessage(
                      xddq.pb.WarSeasonGodBodyStatusDatsMsg.parser(),
                      extensionRegistry);
              if (bodyStatusBuilder_ == null) {
                ensureBodyStatusIsMutable();
                bodyStatus_.add(m);
              } else {
                bodyStatusBuilder_.addMessage(m);
              }
              break;
            } // case 34
            case 42: {
              xddq.pb.WarSeasonCityDetailMsg m =
                  input.readMessage(
                      xddq.pb.WarSeasonCityDetailMsg.parser(),
                      extensionRegistry);
              if (markInfoBuilder_ == null) {
                ensureMarkInfoIsMutable();
                markInfo_.add(m);
              } else {
                markInfoBuilder_.addMessage(m);
              }
              break;
            } // case 42
            case 50: {
              xddq.pb.WarSeasonMapUnionDataMsg m =
                  input.readMessage(
                      xddq.pb.WarSeasonMapUnionDataMsg.parser(),
                      extensionRegistry);
              if (unionDataBuilder_ == null) {
                ensureUnionDataIsMutable();
                unionData_.add(m);
              } else {
                unionDataBuilder_.addMessage(m);
              }
              break;
            } // case 50
            case 58: {
              xddq.pb.WarSeasonMapMoveDataMsg m =
                  input.readMessage(
                      xddq.pb.WarSeasonMapMoveDataMsg.parser(),
                      extensionRegistry);
              if (moveDatasBuilder_ == null) {
                ensureMoveDatasIsMutable();
                moveDatas_.add(m);
              } else {
                moveDatasBuilder_.addMessage(m);
              }
              break;
            } // case 58
            case 64: {
              int v = input.readInt32();
              ensureCollectionCityArrIsMutable();
              collectionCityArr_.addInt(v);
              break;
            } // case 64
            case 66: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              ensureCollectionCityArrIsMutable();
              while (input.getBytesUntilLimit() > 0) {
                collectionCityArr_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            } // case 66
            case 74: {
              xddq.pb.WarSeasonMapDeclareFightDataMsg m =
                  input.readMessage(
                      xddq.pb.WarSeasonMapDeclareFightDataMsg.parser(),
                      extensionRegistry);
              if (fightDataBuilder_ == null) {
                ensureFightDataIsMutable();
                fightData_.add(m);
              } else {
                fightDataBuilder_.addMessage(m);
              }
              break;
            } // case 74
            case 82: {
              xddq.pb.WarSeasonMapDeclareWarCarDataMsg m =
                  input.readMessage(
                      xddq.pb.WarSeasonMapDeclareWarCarDataMsg.parser(),
                      extensionRegistry);
              if (carDataBuilder_ == null) {
                ensureCarDataIsMutable();
                carData_.add(m);
              } else {
                carDataBuilder_.addMessage(m);
              }
              break;
            } // case 82
            case 90: {
              xddq.pb.WarSeasonManorDataMsg m =
                  input.readMessage(
                      xddq.pb.WarSeasonManorDataMsg.parser(),
                      extensionRegistry);
              if (manorDataBuilder_ == null) {
                ensureManorDataIsMutable();
                manorData_.add(m);
              } else {
                manorDataBuilder_.addMessage(m);
              }
              break;
            } // case 90
            case 96: {
              lastMoveCapitalTime_ = input.readInt64();
              bitField0_ |= 0x00000800;
              break;
            } // case 96
            case 106: {
              params_ = input.readBytes();
              bitField0_ |= 0x00001000;
              break;
            } // case 106
            case 112: {
              int v = input.readInt32();
              ensurePassStageIsMutable();
              passStage_.addInt(v);
              break;
            } // case 112
            case 114: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              ensurePassStageIsMutable();
              while (input.getBytesUntilLimit() > 0) {
                passStage_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            } // case 114
            case 120: {
              challengeTimes_ = input.readInt32();
              bitField0_ |= 0x00004000;
              break;
            } // case 120
            case 128: {
              itemChallengeTimes_ = input.readInt32();
              bitField0_ |= 0x00008000;
              break;
            } // case 128
            case 136: {
              battleTimes_ = input.readInt32();
              bitField0_ |= 0x00010000;
              break;
            } // case 136
            case 144: {
              int v = input.readInt32();
              ensureCityLevelIsMutable();
              cityLevel_.addInt(v);
              break;
            } // case 144
            case 146: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              ensureCityLevelIsMutable();
              while (input.getBytesUntilLimit() > 0) {
                cityLevel_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            } // case 146
            case 154: {
              curFightValue_ = input.readBytes();
              bitField0_ |= 0x00040000;
              break;
            } // case 154
            case 162: {
              xddq.pb.WarSeasonOrderDeclareFightDataMsg m =
                  input.readMessage(
                      xddq.pb.WarSeasonOrderDeclareFightDataMsg.parser(),
                      extensionRegistry);
              if (orderFightDataBuilder_ == null) {
                ensureOrderFightDataIsMutable();
                orderFightData_.add(m);
              } else {
                orderFightDataBuilder_.addMessage(m);
              }
              break;
            } // case 162
            case 170: {
              xddq.pb.WarSeasonEffectStrategyInfo m =
                  input.readMessage(
                      xddq.pb.WarSeasonEffectStrategyInfo.parser(),
                      extensionRegistry);
              if (otherUnionStrategyListBuilder_ == null) {
                ensureOtherUnionStrategyListIsMutable();
                otherUnionStrategyList_.add(m);
              } else {
                otherUnionStrategyListBuilder_.addMessage(m);
              }
              break;
            } // case 170
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private int curCityId_ ;
    /**
     * <code>optional int32 curCityId = 2;</code>
     * @return Whether the curCityId field is set.
     */
    @java.lang.Override
    public boolean hasCurCityId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 curCityId = 2;</code>
     * @return The curCityId.
     */
    @java.lang.Override
    public int getCurCityId() {
      return curCityId_;
    }
    /**
     * <code>optional int32 curCityId = 2;</code>
     * @param value The curCityId to set.
     * @return This builder for chaining.
     */
    public Builder setCurCityId(int value) {

      curCityId_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 curCityId = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearCurCityId() {
      bitField0_ = (bitField0_ & ~0x00000002);
      curCityId_ = 0;
      onChanged();
      return this;
    }

    private long seasonExp_ ;
    /**
     * <code>optional int64 seasonExp = 3;</code>
     * @return Whether the seasonExp field is set.
     */
    @java.lang.Override
    public boolean hasSeasonExp() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int64 seasonExp = 3;</code>
     * @return The seasonExp.
     */
    @java.lang.Override
    public long getSeasonExp() {
      return seasonExp_;
    }
    /**
     * <code>optional int64 seasonExp = 3;</code>
     * @param value The seasonExp to set.
     * @return This builder for chaining.
     */
    public Builder setSeasonExp(long value) {

      seasonExp_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 seasonExp = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearSeasonExp() {
      bitField0_ = (bitField0_ & ~0x00000004);
      seasonExp_ = 0L;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.WarSeasonGodBodyStatusDatsMsg> bodyStatus_ =
      java.util.Collections.emptyList();
    private void ensureBodyStatusIsMutable() {
      if (!((bitField0_ & 0x00000008) != 0)) {
        bodyStatus_ = new java.util.ArrayList<xddq.pb.WarSeasonGodBodyStatusDatsMsg>(bodyStatus_);
        bitField0_ |= 0x00000008;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.WarSeasonGodBodyStatusDatsMsg, xddq.pb.WarSeasonGodBodyStatusDatsMsg.Builder, xddq.pb.WarSeasonGodBodyStatusDatsMsgOrBuilder> bodyStatusBuilder_;

    /**
     * <code>repeated .xddq.pb.WarSeasonGodBodyStatusDatsMsg bodyStatus = 4;</code>
     */
    public java.util.List<xddq.pb.WarSeasonGodBodyStatusDatsMsg> getBodyStatusList() {
      if (bodyStatusBuilder_ == null) {
        return java.util.Collections.unmodifiableList(bodyStatus_);
      } else {
        return bodyStatusBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonGodBodyStatusDatsMsg bodyStatus = 4;</code>
     */
    public int getBodyStatusCount() {
      if (bodyStatusBuilder_ == null) {
        return bodyStatus_.size();
      } else {
        return bodyStatusBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonGodBodyStatusDatsMsg bodyStatus = 4;</code>
     */
    public xddq.pb.WarSeasonGodBodyStatusDatsMsg getBodyStatus(int index) {
      if (bodyStatusBuilder_ == null) {
        return bodyStatus_.get(index);
      } else {
        return bodyStatusBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonGodBodyStatusDatsMsg bodyStatus = 4;</code>
     */
    public Builder setBodyStatus(
        int index, xddq.pb.WarSeasonGodBodyStatusDatsMsg value) {
      if (bodyStatusBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureBodyStatusIsMutable();
        bodyStatus_.set(index, value);
        onChanged();
      } else {
        bodyStatusBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonGodBodyStatusDatsMsg bodyStatus = 4;</code>
     */
    public Builder setBodyStatus(
        int index, xddq.pb.WarSeasonGodBodyStatusDatsMsg.Builder builderForValue) {
      if (bodyStatusBuilder_ == null) {
        ensureBodyStatusIsMutable();
        bodyStatus_.set(index, builderForValue.build());
        onChanged();
      } else {
        bodyStatusBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonGodBodyStatusDatsMsg bodyStatus = 4;</code>
     */
    public Builder addBodyStatus(xddq.pb.WarSeasonGodBodyStatusDatsMsg value) {
      if (bodyStatusBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureBodyStatusIsMutable();
        bodyStatus_.add(value);
        onChanged();
      } else {
        bodyStatusBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonGodBodyStatusDatsMsg bodyStatus = 4;</code>
     */
    public Builder addBodyStatus(
        int index, xddq.pb.WarSeasonGodBodyStatusDatsMsg value) {
      if (bodyStatusBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureBodyStatusIsMutable();
        bodyStatus_.add(index, value);
        onChanged();
      } else {
        bodyStatusBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonGodBodyStatusDatsMsg bodyStatus = 4;</code>
     */
    public Builder addBodyStatus(
        xddq.pb.WarSeasonGodBodyStatusDatsMsg.Builder builderForValue) {
      if (bodyStatusBuilder_ == null) {
        ensureBodyStatusIsMutable();
        bodyStatus_.add(builderForValue.build());
        onChanged();
      } else {
        bodyStatusBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonGodBodyStatusDatsMsg bodyStatus = 4;</code>
     */
    public Builder addBodyStatus(
        int index, xddq.pb.WarSeasonGodBodyStatusDatsMsg.Builder builderForValue) {
      if (bodyStatusBuilder_ == null) {
        ensureBodyStatusIsMutable();
        bodyStatus_.add(index, builderForValue.build());
        onChanged();
      } else {
        bodyStatusBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonGodBodyStatusDatsMsg bodyStatus = 4;</code>
     */
    public Builder addAllBodyStatus(
        java.lang.Iterable<? extends xddq.pb.WarSeasonGodBodyStatusDatsMsg> values) {
      if (bodyStatusBuilder_ == null) {
        ensureBodyStatusIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, bodyStatus_);
        onChanged();
      } else {
        bodyStatusBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonGodBodyStatusDatsMsg bodyStatus = 4;</code>
     */
    public Builder clearBodyStatus() {
      if (bodyStatusBuilder_ == null) {
        bodyStatus_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000008);
        onChanged();
      } else {
        bodyStatusBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonGodBodyStatusDatsMsg bodyStatus = 4;</code>
     */
    public Builder removeBodyStatus(int index) {
      if (bodyStatusBuilder_ == null) {
        ensureBodyStatusIsMutable();
        bodyStatus_.remove(index);
        onChanged();
      } else {
        bodyStatusBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonGodBodyStatusDatsMsg bodyStatus = 4;</code>
     */
    public xddq.pb.WarSeasonGodBodyStatusDatsMsg.Builder getBodyStatusBuilder(
        int index) {
      return internalGetBodyStatusFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonGodBodyStatusDatsMsg bodyStatus = 4;</code>
     */
    public xddq.pb.WarSeasonGodBodyStatusDatsMsgOrBuilder getBodyStatusOrBuilder(
        int index) {
      if (bodyStatusBuilder_ == null) {
        return bodyStatus_.get(index);  } else {
        return bodyStatusBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonGodBodyStatusDatsMsg bodyStatus = 4;</code>
     */
    public java.util.List<? extends xddq.pb.WarSeasonGodBodyStatusDatsMsgOrBuilder> 
         getBodyStatusOrBuilderList() {
      if (bodyStatusBuilder_ != null) {
        return bodyStatusBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(bodyStatus_);
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonGodBodyStatusDatsMsg bodyStatus = 4;</code>
     */
    public xddq.pb.WarSeasonGodBodyStatusDatsMsg.Builder addBodyStatusBuilder() {
      return internalGetBodyStatusFieldBuilder().addBuilder(
          xddq.pb.WarSeasonGodBodyStatusDatsMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonGodBodyStatusDatsMsg bodyStatus = 4;</code>
     */
    public xddq.pb.WarSeasonGodBodyStatusDatsMsg.Builder addBodyStatusBuilder(
        int index) {
      return internalGetBodyStatusFieldBuilder().addBuilder(
          index, xddq.pb.WarSeasonGodBodyStatusDatsMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonGodBodyStatusDatsMsg bodyStatus = 4;</code>
     */
    public java.util.List<xddq.pb.WarSeasonGodBodyStatusDatsMsg.Builder> 
         getBodyStatusBuilderList() {
      return internalGetBodyStatusFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.WarSeasonGodBodyStatusDatsMsg, xddq.pb.WarSeasonGodBodyStatusDatsMsg.Builder, xddq.pb.WarSeasonGodBodyStatusDatsMsgOrBuilder> 
        internalGetBodyStatusFieldBuilder() {
      if (bodyStatusBuilder_ == null) {
        bodyStatusBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.WarSeasonGodBodyStatusDatsMsg, xddq.pb.WarSeasonGodBodyStatusDatsMsg.Builder, xddq.pb.WarSeasonGodBodyStatusDatsMsgOrBuilder>(
                bodyStatus_,
                ((bitField0_ & 0x00000008) != 0),
                getParentForChildren(),
                isClean());
        bodyStatus_ = null;
      }
      return bodyStatusBuilder_;
    }

    private java.util.List<xddq.pb.WarSeasonCityDetailMsg> markInfo_ =
      java.util.Collections.emptyList();
    private void ensureMarkInfoIsMutable() {
      if (!((bitField0_ & 0x00000010) != 0)) {
        markInfo_ = new java.util.ArrayList<xddq.pb.WarSeasonCityDetailMsg>(markInfo_);
        bitField0_ |= 0x00000010;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.WarSeasonCityDetailMsg, xddq.pb.WarSeasonCityDetailMsg.Builder, xddq.pb.WarSeasonCityDetailMsgOrBuilder> markInfoBuilder_;

    /**
     * <code>repeated .xddq.pb.WarSeasonCityDetailMsg markInfo = 5;</code>
     */
    public java.util.List<xddq.pb.WarSeasonCityDetailMsg> getMarkInfoList() {
      if (markInfoBuilder_ == null) {
        return java.util.Collections.unmodifiableList(markInfo_);
      } else {
        return markInfoBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonCityDetailMsg markInfo = 5;</code>
     */
    public int getMarkInfoCount() {
      if (markInfoBuilder_ == null) {
        return markInfo_.size();
      } else {
        return markInfoBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonCityDetailMsg markInfo = 5;</code>
     */
    public xddq.pb.WarSeasonCityDetailMsg getMarkInfo(int index) {
      if (markInfoBuilder_ == null) {
        return markInfo_.get(index);
      } else {
        return markInfoBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonCityDetailMsg markInfo = 5;</code>
     */
    public Builder setMarkInfo(
        int index, xddq.pb.WarSeasonCityDetailMsg value) {
      if (markInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMarkInfoIsMutable();
        markInfo_.set(index, value);
        onChanged();
      } else {
        markInfoBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonCityDetailMsg markInfo = 5;</code>
     */
    public Builder setMarkInfo(
        int index, xddq.pb.WarSeasonCityDetailMsg.Builder builderForValue) {
      if (markInfoBuilder_ == null) {
        ensureMarkInfoIsMutable();
        markInfo_.set(index, builderForValue.build());
        onChanged();
      } else {
        markInfoBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonCityDetailMsg markInfo = 5;</code>
     */
    public Builder addMarkInfo(xddq.pb.WarSeasonCityDetailMsg value) {
      if (markInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMarkInfoIsMutable();
        markInfo_.add(value);
        onChanged();
      } else {
        markInfoBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonCityDetailMsg markInfo = 5;</code>
     */
    public Builder addMarkInfo(
        int index, xddq.pb.WarSeasonCityDetailMsg value) {
      if (markInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMarkInfoIsMutable();
        markInfo_.add(index, value);
        onChanged();
      } else {
        markInfoBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonCityDetailMsg markInfo = 5;</code>
     */
    public Builder addMarkInfo(
        xddq.pb.WarSeasonCityDetailMsg.Builder builderForValue) {
      if (markInfoBuilder_ == null) {
        ensureMarkInfoIsMutable();
        markInfo_.add(builderForValue.build());
        onChanged();
      } else {
        markInfoBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonCityDetailMsg markInfo = 5;</code>
     */
    public Builder addMarkInfo(
        int index, xddq.pb.WarSeasonCityDetailMsg.Builder builderForValue) {
      if (markInfoBuilder_ == null) {
        ensureMarkInfoIsMutable();
        markInfo_.add(index, builderForValue.build());
        onChanged();
      } else {
        markInfoBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonCityDetailMsg markInfo = 5;</code>
     */
    public Builder addAllMarkInfo(
        java.lang.Iterable<? extends xddq.pb.WarSeasonCityDetailMsg> values) {
      if (markInfoBuilder_ == null) {
        ensureMarkInfoIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, markInfo_);
        onChanged();
      } else {
        markInfoBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonCityDetailMsg markInfo = 5;</code>
     */
    public Builder clearMarkInfo() {
      if (markInfoBuilder_ == null) {
        markInfo_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000010);
        onChanged();
      } else {
        markInfoBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonCityDetailMsg markInfo = 5;</code>
     */
    public Builder removeMarkInfo(int index) {
      if (markInfoBuilder_ == null) {
        ensureMarkInfoIsMutable();
        markInfo_.remove(index);
        onChanged();
      } else {
        markInfoBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonCityDetailMsg markInfo = 5;</code>
     */
    public xddq.pb.WarSeasonCityDetailMsg.Builder getMarkInfoBuilder(
        int index) {
      return internalGetMarkInfoFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonCityDetailMsg markInfo = 5;</code>
     */
    public xddq.pb.WarSeasonCityDetailMsgOrBuilder getMarkInfoOrBuilder(
        int index) {
      if (markInfoBuilder_ == null) {
        return markInfo_.get(index);  } else {
        return markInfoBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonCityDetailMsg markInfo = 5;</code>
     */
    public java.util.List<? extends xddq.pb.WarSeasonCityDetailMsgOrBuilder> 
         getMarkInfoOrBuilderList() {
      if (markInfoBuilder_ != null) {
        return markInfoBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(markInfo_);
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonCityDetailMsg markInfo = 5;</code>
     */
    public xddq.pb.WarSeasonCityDetailMsg.Builder addMarkInfoBuilder() {
      return internalGetMarkInfoFieldBuilder().addBuilder(
          xddq.pb.WarSeasonCityDetailMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonCityDetailMsg markInfo = 5;</code>
     */
    public xddq.pb.WarSeasonCityDetailMsg.Builder addMarkInfoBuilder(
        int index) {
      return internalGetMarkInfoFieldBuilder().addBuilder(
          index, xddq.pb.WarSeasonCityDetailMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonCityDetailMsg markInfo = 5;</code>
     */
    public java.util.List<xddq.pb.WarSeasonCityDetailMsg.Builder> 
         getMarkInfoBuilderList() {
      return internalGetMarkInfoFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.WarSeasonCityDetailMsg, xddq.pb.WarSeasonCityDetailMsg.Builder, xddq.pb.WarSeasonCityDetailMsgOrBuilder> 
        internalGetMarkInfoFieldBuilder() {
      if (markInfoBuilder_ == null) {
        markInfoBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.WarSeasonCityDetailMsg, xddq.pb.WarSeasonCityDetailMsg.Builder, xddq.pb.WarSeasonCityDetailMsgOrBuilder>(
                markInfo_,
                ((bitField0_ & 0x00000010) != 0),
                getParentForChildren(),
                isClean());
        markInfo_ = null;
      }
      return markInfoBuilder_;
    }

    private java.util.List<xddq.pb.WarSeasonMapUnionDataMsg> unionData_ =
      java.util.Collections.emptyList();
    private void ensureUnionDataIsMutable() {
      if (!((bitField0_ & 0x00000020) != 0)) {
        unionData_ = new java.util.ArrayList<xddq.pb.WarSeasonMapUnionDataMsg>(unionData_);
        bitField0_ |= 0x00000020;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.WarSeasonMapUnionDataMsg, xddq.pb.WarSeasonMapUnionDataMsg.Builder, xddq.pb.WarSeasonMapUnionDataMsgOrBuilder> unionDataBuilder_;

    /**
     * <code>repeated .xddq.pb.WarSeasonMapUnionDataMsg unionData = 6;</code>
     */
    public java.util.List<xddq.pb.WarSeasonMapUnionDataMsg> getUnionDataList() {
      if (unionDataBuilder_ == null) {
        return java.util.Collections.unmodifiableList(unionData_);
      } else {
        return unionDataBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapUnionDataMsg unionData = 6;</code>
     */
    public int getUnionDataCount() {
      if (unionDataBuilder_ == null) {
        return unionData_.size();
      } else {
        return unionDataBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapUnionDataMsg unionData = 6;</code>
     */
    public xddq.pb.WarSeasonMapUnionDataMsg getUnionData(int index) {
      if (unionDataBuilder_ == null) {
        return unionData_.get(index);
      } else {
        return unionDataBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapUnionDataMsg unionData = 6;</code>
     */
    public Builder setUnionData(
        int index, xddq.pb.WarSeasonMapUnionDataMsg value) {
      if (unionDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureUnionDataIsMutable();
        unionData_.set(index, value);
        onChanged();
      } else {
        unionDataBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapUnionDataMsg unionData = 6;</code>
     */
    public Builder setUnionData(
        int index, xddq.pb.WarSeasonMapUnionDataMsg.Builder builderForValue) {
      if (unionDataBuilder_ == null) {
        ensureUnionDataIsMutable();
        unionData_.set(index, builderForValue.build());
        onChanged();
      } else {
        unionDataBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapUnionDataMsg unionData = 6;</code>
     */
    public Builder addUnionData(xddq.pb.WarSeasonMapUnionDataMsg value) {
      if (unionDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureUnionDataIsMutable();
        unionData_.add(value);
        onChanged();
      } else {
        unionDataBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapUnionDataMsg unionData = 6;</code>
     */
    public Builder addUnionData(
        int index, xddq.pb.WarSeasonMapUnionDataMsg value) {
      if (unionDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureUnionDataIsMutable();
        unionData_.add(index, value);
        onChanged();
      } else {
        unionDataBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapUnionDataMsg unionData = 6;</code>
     */
    public Builder addUnionData(
        xddq.pb.WarSeasonMapUnionDataMsg.Builder builderForValue) {
      if (unionDataBuilder_ == null) {
        ensureUnionDataIsMutable();
        unionData_.add(builderForValue.build());
        onChanged();
      } else {
        unionDataBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapUnionDataMsg unionData = 6;</code>
     */
    public Builder addUnionData(
        int index, xddq.pb.WarSeasonMapUnionDataMsg.Builder builderForValue) {
      if (unionDataBuilder_ == null) {
        ensureUnionDataIsMutable();
        unionData_.add(index, builderForValue.build());
        onChanged();
      } else {
        unionDataBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapUnionDataMsg unionData = 6;</code>
     */
    public Builder addAllUnionData(
        java.lang.Iterable<? extends xddq.pb.WarSeasonMapUnionDataMsg> values) {
      if (unionDataBuilder_ == null) {
        ensureUnionDataIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, unionData_);
        onChanged();
      } else {
        unionDataBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapUnionDataMsg unionData = 6;</code>
     */
    public Builder clearUnionData() {
      if (unionDataBuilder_ == null) {
        unionData_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000020);
        onChanged();
      } else {
        unionDataBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapUnionDataMsg unionData = 6;</code>
     */
    public Builder removeUnionData(int index) {
      if (unionDataBuilder_ == null) {
        ensureUnionDataIsMutable();
        unionData_.remove(index);
        onChanged();
      } else {
        unionDataBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapUnionDataMsg unionData = 6;</code>
     */
    public xddq.pb.WarSeasonMapUnionDataMsg.Builder getUnionDataBuilder(
        int index) {
      return internalGetUnionDataFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapUnionDataMsg unionData = 6;</code>
     */
    public xddq.pb.WarSeasonMapUnionDataMsgOrBuilder getUnionDataOrBuilder(
        int index) {
      if (unionDataBuilder_ == null) {
        return unionData_.get(index);  } else {
        return unionDataBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapUnionDataMsg unionData = 6;</code>
     */
    public java.util.List<? extends xddq.pb.WarSeasonMapUnionDataMsgOrBuilder> 
         getUnionDataOrBuilderList() {
      if (unionDataBuilder_ != null) {
        return unionDataBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(unionData_);
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapUnionDataMsg unionData = 6;</code>
     */
    public xddq.pb.WarSeasonMapUnionDataMsg.Builder addUnionDataBuilder() {
      return internalGetUnionDataFieldBuilder().addBuilder(
          xddq.pb.WarSeasonMapUnionDataMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapUnionDataMsg unionData = 6;</code>
     */
    public xddq.pb.WarSeasonMapUnionDataMsg.Builder addUnionDataBuilder(
        int index) {
      return internalGetUnionDataFieldBuilder().addBuilder(
          index, xddq.pb.WarSeasonMapUnionDataMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapUnionDataMsg unionData = 6;</code>
     */
    public java.util.List<xddq.pb.WarSeasonMapUnionDataMsg.Builder> 
         getUnionDataBuilderList() {
      return internalGetUnionDataFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.WarSeasonMapUnionDataMsg, xddq.pb.WarSeasonMapUnionDataMsg.Builder, xddq.pb.WarSeasonMapUnionDataMsgOrBuilder> 
        internalGetUnionDataFieldBuilder() {
      if (unionDataBuilder_ == null) {
        unionDataBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.WarSeasonMapUnionDataMsg, xddq.pb.WarSeasonMapUnionDataMsg.Builder, xddq.pb.WarSeasonMapUnionDataMsgOrBuilder>(
                unionData_,
                ((bitField0_ & 0x00000020) != 0),
                getParentForChildren(),
                isClean());
        unionData_ = null;
      }
      return unionDataBuilder_;
    }

    private java.util.List<xddq.pb.WarSeasonMapMoveDataMsg> moveDatas_ =
      java.util.Collections.emptyList();
    private void ensureMoveDatasIsMutable() {
      if (!((bitField0_ & 0x00000040) != 0)) {
        moveDatas_ = new java.util.ArrayList<xddq.pb.WarSeasonMapMoveDataMsg>(moveDatas_);
        bitField0_ |= 0x00000040;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.WarSeasonMapMoveDataMsg, xddq.pb.WarSeasonMapMoveDataMsg.Builder, xddq.pb.WarSeasonMapMoveDataMsgOrBuilder> moveDatasBuilder_;

    /**
     * <code>repeated .xddq.pb.WarSeasonMapMoveDataMsg moveDatas = 7;</code>
     */
    public java.util.List<xddq.pb.WarSeasonMapMoveDataMsg> getMoveDatasList() {
      if (moveDatasBuilder_ == null) {
        return java.util.Collections.unmodifiableList(moveDatas_);
      } else {
        return moveDatasBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapMoveDataMsg moveDatas = 7;</code>
     */
    public int getMoveDatasCount() {
      if (moveDatasBuilder_ == null) {
        return moveDatas_.size();
      } else {
        return moveDatasBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapMoveDataMsg moveDatas = 7;</code>
     */
    public xddq.pb.WarSeasonMapMoveDataMsg getMoveDatas(int index) {
      if (moveDatasBuilder_ == null) {
        return moveDatas_.get(index);
      } else {
        return moveDatasBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapMoveDataMsg moveDatas = 7;</code>
     */
    public Builder setMoveDatas(
        int index, xddq.pb.WarSeasonMapMoveDataMsg value) {
      if (moveDatasBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMoveDatasIsMutable();
        moveDatas_.set(index, value);
        onChanged();
      } else {
        moveDatasBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapMoveDataMsg moveDatas = 7;</code>
     */
    public Builder setMoveDatas(
        int index, xddq.pb.WarSeasonMapMoveDataMsg.Builder builderForValue) {
      if (moveDatasBuilder_ == null) {
        ensureMoveDatasIsMutable();
        moveDatas_.set(index, builderForValue.build());
        onChanged();
      } else {
        moveDatasBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapMoveDataMsg moveDatas = 7;</code>
     */
    public Builder addMoveDatas(xddq.pb.WarSeasonMapMoveDataMsg value) {
      if (moveDatasBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMoveDatasIsMutable();
        moveDatas_.add(value);
        onChanged();
      } else {
        moveDatasBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapMoveDataMsg moveDatas = 7;</code>
     */
    public Builder addMoveDatas(
        int index, xddq.pb.WarSeasonMapMoveDataMsg value) {
      if (moveDatasBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMoveDatasIsMutable();
        moveDatas_.add(index, value);
        onChanged();
      } else {
        moveDatasBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapMoveDataMsg moveDatas = 7;</code>
     */
    public Builder addMoveDatas(
        xddq.pb.WarSeasonMapMoveDataMsg.Builder builderForValue) {
      if (moveDatasBuilder_ == null) {
        ensureMoveDatasIsMutable();
        moveDatas_.add(builderForValue.build());
        onChanged();
      } else {
        moveDatasBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapMoveDataMsg moveDatas = 7;</code>
     */
    public Builder addMoveDatas(
        int index, xddq.pb.WarSeasonMapMoveDataMsg.Builder builderForValue) {
      if (moveDatasBuilder_ == null) {
        ensureMoveDatasIsMutable();
        moveDatas_.add(index, builderForValue.build());
        onChanged();
      } else {
        moveDatasBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapMoveDataMsg moveDatas = 7;</code>
     */
    public Builder addAllMoveDatas(
        java.lang.Iterable<? extends xddq.pb.WarSeasonMapMoveDataMsg> values) {
      if (moveDatasBuilder_ == null) {
        ensureMoveDatasIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, moveDatas_);
        onChanged();
      } else {
        moveDatasBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapMoveDataMsg moveDatas = 7;</code>
     */
    public Builder clearMoveDatas() {
      if (moveDatasBuilder_ == null) {
        moveDatas_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000040);
        onChanged();
      } else {
        moveDatasBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapMoveDataMsg moveDatas = 7;</code>
     */
    public Builder removeMoveDatas(int index) {
      if (moveDatasBuilder_ == null) {
        ensureMoveDatasIsMutable();
        moveDatas_.remove(index);
        onChanged();
      } else {
        moveDatasBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapMoveDataMsg moveDatas = 7;</code>
     */
    public xddq.pb.WarSeasonMapMoveDataMsg.Builder getMoveDatasBuilder(
        int index) {
      return internalGetMoveDatasFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapMoveDataMsg moveDatas = 7;</code>
     */
    public xddq.pb.WarSeasonMapMoveDataMsgOrBuilder getMoveDatasOrBuilder(
        int index) {
      if (moveDatasBuilder_ == null) {
        return moveDatas_.get(index);  } else {
        return moveDatasBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapMoveDataMsg moveDatas = 7;</code>
     */
    public java.util.List<? extends xddq.pb.WarSeasonMapMoveDataMsgOrBuilder> 
         getMoveDatasOrBuilderList() {
      if (moveDatasBuilder_ != null) {
        return moveDatasBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(moveDatas_);
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapMoveDataMsg moveDatas = 7;</code>
     */
    public xddq.pb.WarSeasonMapMoveDataMsg.Builder addMoveDatasBuilder() {
      return internalGetMoveDatasFieldBuilder().addBuilder(
          xddq.pb.WarSeasonMapMoveDataMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapMoveDataMsg moveDatas = 7;</code>
     */
    public xddq.pb.WarSeasonMapMoveDataMsg.Builder addMoveDatasBuilder(
        int index) {
      return internalGetMoveDatasFieldBuilder().addBuilder(
          index, xddq.pb.WarSeasonMapMoveDataMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapMoveDataMsg moveDatas = 7;</code>
     */
    public java.util.List<xddq.pb.WarSeasonMapMoveDataMsg.Builder> 
         getMoveDatasBuilderList() {
      return internalGetMoveDatasFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.WarSeasonMapMoveDataMsg, xddq.pb.WarSeasonMapMoveDataMsg.Builder, xddq.pb.WarSeasonMapMoveDataMsgOrBuilder> 
        internalGetMoveDatasFieldBuilder() {
      if (moveDatasBuilder_ == null) {
        moveDatasBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.WarSeasonMapMoveDataMsg, xddq.pb.WarSeasonMapMoveDataMsg.Builder, xddq.pb.WarSeasonMapMoveDataMsgOrBuilder>(
                moveDatas_,
                ((bitField0_ & 0x00000040) != 0),
                getParentForChildren(),
                isClean());
        moveDatas_ = null;
      }
      return moveDatasBuilder_;
    }

    private com.google.protobuf.Internal.IntList collectionCityArr_ = emptyIntList();
    private void ensureCollectionCityArrIsMutable() {
      if (!collectionCityArr_.isModifiable()) {
        collectionCityArr_ = makeMutableCopy(collectionCityArr_);
      }
      bitField0_ |= 0x00000080;
    }
    /**
     * <code>repeated int32 collectionCityArr = 8;</code>
     * @return A list containing the collectionCityArr.
     */
    public java.util.List<java.lang.Integer>
        getCollectionCityArrList() {
      collectionCityArr_.makeImmutable();
      return collectionCityArr_;
    }
    /**
     * <code>repeated int32 collectionCityArr = 8;</code>
     * @return The count of collectionCityArr.
     */
    public int getCollectionCityArrCount() {
      return collectionCityArr_.size();
    }
    /**
     * <code>repeated int32 collectionCityArr = 8;</code>
     * @param index The index of the element to return.
     * @return The collectionCityArr at the given index.
     */
    public int getCollectionCityArr(int index) {
      return collectionCityArr_.getInt(index);
    }
    /**
     * <code>repeated int32 collectionCityArr = 8;</code>
     * @param index The index to set the value at.
     * @param value The collectionCityArr to set.
     * @return This builder for chaining.
     */
    public Builder setCollectionCityArr(
        int index, int value) {

      ensureCollectionCityArrIsMutable();
      collectionCityArr_.setInt(index, value);
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 collectionCityArr = 8;</code>
     * @param value The collectionCityArr to add.
     * @return This builder for chaining.
     */
    public Builder addCollectionCityArr(int value) {

      ensureCollectionCityArrIsMutable();
      collectionCityArr_.addInt(value);
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 collectionCityArr = 8;</code>
     * @param values The collectionCityArr to add.
     * @return This builder for chaining.
     */
    public Builder addAllCollectionCityArr(
        java.lang.Iterable<? extends java.lang.Integer> values) {
      ensureCollectionCityArrIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, collectionCityArr_);
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 collectionCityArr = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearCollectionCityArr() {
      collectionCityArr_ = emptyIntList();
      bitField0_ = (bitField0_ & ~0x00000080);
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.WarSeasonMapDeclareFightDataMsg> fightData_ =
      java.util.Collections.emptyList();
    private void ensureFightDataIsMutable() {
      if (!((bitField0_ & 0x00000100) != 0)) {
        fightData_ = new java.util.ArrayList<xddq.pb.WarSeasonMapDeclareFightDataMsg>(fightData_);
        bitField0_ |= 0x00000100;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.WarSeasonMapDeclareFightDataMsg, xddq.pb.WarSeasonMapDeclareFightDataMsg.Builder, xddq.pb.WarSeasonMapDeclareFightDataMsgOrBuilder> fightDataBuilder_;

    /**
     * <code>repeated .xddq.pb.WarSeasonMapDeclareFightDataMsg fightData = 9;</code>
     */
    public java.util.List<xddq.pb.WarSeasonMapDeclareFightDataMsg> getFightDataList() {
      if (fightDataBuilder_ == null) {
        return java.util.Collections.unmodifiableList(fightData_);
      } else {
        return fightDataBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapDeclareFightDataMsg fightData = 9;</code>
     */
    public int getFightDataCount() {
      if (fightDataBuilder_ == null) {
        return fightData_.size();
      } else {
        return fightDataBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapDeclareFightDataMsg fightData = 9;</code>
     */
    public xddq.pb.WarSeasonMapDeclareFightDataMsg getFightData(int index) {
      if (fightDataBuilder_ == null) {
        return fightData_.get(index);
      } else {
        return fightDataBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapDeclareFightDataMsg fightData = 9;</code>
     */
    public Builder setFightData(
        int index, xddq.pb.WarSeasonMapDeclareFightDataMsg value) {
      if (fightDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureFightDataIsMutable();
        fightData_.set(index, value);
        onChanged();
      } else {
        fightDataBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapDeclareFightDataMsg fightData = 9;</code>
     */
    public Builder setFightData(
        int index, xddq.pb.WarSeasonMapDeclareFightDataMsg.Builder builderForValue) {
      if (fightDataBuilder_ == null) {
        ensureFightDataIsMutable();
        fightData_.set(index, builderForValue.build());
        onChanged();
      } else {
        fightDataBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapDeclareFightDataMsg fightData = 9;</code>
     */
    public Builder addFightData(xddq.pb.WarSeasonMapDeclareFightDataMsg value) {
      if (fightDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureFightDataIsMutable();
        fightData_.add(value);
        onChanged();
      } else {
        fightDataBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapDeclareFightDataMsg fightData = 9;</code>
     */
    public Builder addFightData(
        int index, xddq.pb.WarSeasonMapDeclareFightDataMsg value) {
      if (fightDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureFightDataIsMutable();
        fightData_.add(index, value);
        onChanged();
      } else {
        fightDataBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapDeclareFightDataMsg fightData = 9;</code>
     */
    public Builder addFightData(
        xddq.pb.WarSeasonMapDeclareFightDataMsg.Builder builderForValue) {
      if (fightDataBuilder_ == null) {
        ensureFightDataIsMutable();
        fightData_.add(builderForValue.build());
        onChanged();
      } else {
        fightDataBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapDeclareFightDataMsg fightData = 9;</code>
     */
    public Builder addFightData(
        int index, xddq.pb.WarSeasonMapDeclareFightDataMsg.Builder builderForValue) {
      if (fightDataBuilder_ == null) {
        ensureFightDataIsMutable();
        fightData_.add(index, builderForValue.build());
        onChanged();
      } else {
        fightDataBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapDeclareFightDataMsg fightData = 9;</code>
     */
    public Builder addAllFightData(
        java.lang.Iterable<? extends xddq.pb.WarSeasonMapDeclareFightDataMsg> values) {
      if (fightDataBuilder_ == null) {
        ensureFightDataIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, fightData_);
        onChanged();
      } else {
        fightDataBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapDeclareFightDataMsg fightData = 9;</code>
     */
    public Builder clearFightData() {
      if (fightDataBuilder_ == null) {
        fightData_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000100);
        onChanged();
      } else {
        fightDataBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapDeclareFightDataMsg fightData = 9;</code>
     */
    public Builder removeFightData(int index) {
      if (fightDataBuilder_ == null) {
        ensureFightDataIsMutable();
        fightData_.remove(index);
        onChanged();
      } else {
        fightDataBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapDeclareFightDataMsg fightData = 9;</code>
     */
    public xddq.pb.WarSeasonMapDeclareFightDataMsg.Builder getFightDataBuilder(
        int index) {
      return internalGetFightDataFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapDeclareFightDataMsg fightData = 9;</code>
     */
    public xddq.pb.WarSeasonMapDeclareFightDataMsgOrBuilder getFightDataOrBuilder(
        int index) {
      if (fightDataBuilder_ == null) {
        return fightData_.get(index);  } else {
        return fightDataBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapDeclareFightDataMsg fightData = 9;</code>
     */
    public java.util.List<? extends xddq.pb.WarSeasonMapDeclareFightDataMsgOrBuilder> 
         getFightDataOrBuilderList() {
      if (fightDataBuilder_ != null) {
        return fightDataBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(fightData_);
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapDeclareFightDataMsg fightData = 9;</code>
     */
    public xddq.pb.WarSeasonMapDeclareFightDataMsg.Builder addFightDataBuilder() {
      return internalGetFightDataFieldBuilder().addBuilder(
          xddq.pb.WarSeasonMapDeclareFightDataMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapDeclareFightDataMsg fightData = 9;</code>
     */
    public xddq.pb.WarSeasonMapDeclareFightDataMsg.Builder addFightDataBuilder(
        int index) {
      return internalGetFightDataFieldBuilder().addBuilder(
          index, xddq.pb.WarSeasonMapDeclareFightDataMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapDeclareFightDataMsg fightData = 9;</code>
     */
    public java.util.List<xddq.pb.WarSeasonMapDeclareFightDataMsg.Builder> 
         getFightDataBuilderList() {
      return internalGetFightDataFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.WarSeasonMapDeclareFightDataMsg, xddq.pb.WarSeasonMapDeclareFightDataMsg.Builder, xddq.pb.WarSeasonMapDeclareFightDataMsgOrBuilder> 
        internalGetFightDataFieldBuilder() {
      if (fightDataBuilder_ == null) {
        fightDataBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.WarSeasonMapDeclareFightDataMsg, xddq.pb.WarSeasonMapDeclareFightDataMsg.Builder, xddq.pb.WarSeasonMapDeclareFightDataMsgOrBuilder>(
                fightData_,
                ((bitField0_ & 0x00000100) != 0),
                getParentForChildren(),
                isClean());
        fightData_ = null;
      }
      return fightDataBuilder_;
    }

    private java.util.List<xddq.pb.WarSeasonMapDeclareWarCarDataMsg> carData_ =
      java.util.Collections.emptyList();
    private void ensureCarDataIsMutable() {
      if (!((bitField0_ & 0x00000200) != 0)) {
        carData_ = new java.util.ArrayList<xddq.pb.WarSeasonMapDeclareWarCarDataMsg>(carData_);
        bitField0_ |= 0x00000200;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.WarSeasonMapDeclareWarCarDataMsg, xddq.pb.WarSeasonMapDeclareWarCarDataMsg.Builder, xddq.pb.WarSeasonMapDeclareWarCarDataMsgOrBuilder> carDataBuilder_;

    /**
     * <code>repeated .xddq.pb.WarSeasonMapDeclareWarCarDataMsg carData = 10;</code>
     */
    public java.util.List<xddq.pb.WarSeasonMapDeclareWarCarDataMsg> getCarDataList() {
      if (carDataBuilder_ == null) {
        return java.util.Collections.unmodifiableList(carData_);
      } else {
        return carDataBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapDeclareWarCarDataMsg carData = 10;</code>
     */
    public int getCarDataCount() {
      if (carDataBuilder_ == null) {
        return carData_.size();
      } else {
        return carDataBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapDeclareWarCarDataMsg carData = 10;</code>
     */
    public xddq.pb.WarSeasonMapDeclareWarCarDataMsg getCarData(int index) {
      if (carDataBuilder_ == null) {
        return carData_.get(index);
      } else {
        return carDataBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapDeclareWarCarDataMsg carData = 10;</code>
     */
    public Builder setCarData(
        int index, xddq.pb.WarSeasonMapDeclareWarCarDataMsg value) {
      if (carDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureCarDataIsMutable();
        carData_.set(index, value);
        onChanged();
      } else {
        carDataBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapDeclareWarCarDataMsg carData = 10;</code>
     */
    public Builder setCarData(
        int index, xddq.pb.WarSeasonMapDeclareWarCarDataMsg.Builder builderForValue) {
      if (carDataBuilder_ == null) {
        ensureCarDataIsMutable();
        carData_.set(index, builderForValue.build());
        onChanged();
      } else {
        carDataBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapDeclareWarCarDataMsg carData = 10;</code>
     */
    public Builder addCarData(xddq.pb.WarSeasonMapDeclareWarCarDataMsg value) {
      if (carDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureCarDataIsMutable();
        carData_.add(value);
        onChanged();
      } else {
        carDataBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapDeclareWarCarDataMsg carData = 10;</code>
     */
    public Builder addCarData(
        int index, xddq.pb.WarSeasonMapDeclareWarCarDataMsg value) {
      if (carDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureCarDataIsMutable();
        carData_.add(index, value);
        onChanged();
      } else {
        carDataBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapDeclareWarCarDataMsg carData = 10;</code>
     */
    public Builder addCarData(
        xddq.pb.WarSeasonMapDeclareWarCarDataMsg.Builder builderForValue) {
      if (carDataBuilder_ == null) {
        ensureCarDataIsMutable();
        carData_.add(builderForValue.build());
        onChanged();
      } else {
        carDataBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapDeclareWarCarDataMsg carData = 10;</code>
     */
    public Builder addCarData(
        int index, xddq.pb.WarSeasonMapDeclareWarCarDataMsg.Builder builderForValue) {
      if (carDataBuilder_ == null) {
        ensureCarDataIsMutable();
        carData_.add(index, builderForValue.build());
        onChanged();
      } else {
        carDataBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapDeclareWarCarDataMsg carData = 10;</code>
     */
    public Builder addAllCarData(
        java.lang.Iterable<? extends xddq.pb.WarSeasonMapDeclareWarCarDataMsg> values) {
      if (carDataBuilder_ == null) {
        ensureCarDataIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, carData_);
        onChanged();
      } else {
        carDataBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapDeclareWarCarDataMsg carData = 10;</code>
     */
    public Builder clearCarData() {
      if (carDataBuilder_ == null) {
        carData_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000200);
        onChanged();
      } else {
        carDataBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapDeclareWarCarDataMsg carData = 10;</code>
     */
    public Builder removeCarData(int index) {
      if (carDataBuilder_ == null) {
        ensureCarDataIsMutable();
        carData_.remove(index);
        onChanged();
      } else {
        carDataBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapDeclareWarCarDataMsg carData = 10;</code>
     */
    public xddq.pb.WarSeasonMapDeclareWarCarDataMsg.Builder getCarDataBuilder(
        int index) {
      return internalGetCarDataFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapDeclareWarCarDataMsg carData = 10;</code>
     */
    public xddq.pb.WarSeasonMapDeclareWarCarDataMsgOrBuilder getCarDataOrBuilder(
        int index) {
      if (carDataBuilder_ == null) {
        return carData_.get(index);  } else {
        return carDataBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapDeclareWarCarDataMsg carData = 10;</code>
     */
    public java.util.List<? extends xddq.pb.WarSeasonMapDeclareWarCarDataMsgOrBuilder> 
         getCarDataOrBuilderList() {
      if (carDataBuilder_ != null) {
        return carDataBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(carData_);
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapDeclareWarCarDataMsg carData = 10;</code>
     */
    public xddq.pb.WarSeasonMapDeclareWarCarDataMsg.Builder addCarDataBuilder() {
      return internalGetCarDataFieldBuilder().addBuilder(
          xddq.pb.WarSeasonMapDeclareWarCarDataMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapDeclareWarCarDataMsg carData = 10;</code>
     */
    public xddq.pb.WarSeasonMapDeclareWarCarDataMsg.Builder addCarDataBuilder(
        int index) {
      return internalGetCarDataFieldBuilder().addBuilder(
          index, xddq.pb.WarSeasonMapDeclareWarCarDataMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapDeclareWarCarDataMsg carData = 10;</code>
     */
    public java.util.List<xddq.pb.WarSeasonMapDeclareWarCarDataMsg.Builder> 
         getCarDataBuilderList() {
      return internalGetCarDataFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.WarSeasonMapDeclareWarCarDataMsg, xddq.pb.WarSeasonMapDeclareWarCarDataMsg.Builder, xddq.pb.WarSeasonMapDeclareWarCarDataMsgOrBuilder> 
        internalGetCarDataFieldBuilder() {
      if (carDataBuilder_ == null) {
        carDataBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.WarSeasonMapDeclareWarCarDataMsg, xddq.pb.WarSeasonMapDeclareWarCarDataMsg.Builder, xddq.pb.WarSeasonMapDeclareWarCarDataMsgOrBuilder>(
                carData_,
                ((bitField0_ & 0x00000200) != 0),
                getParentForChildren(),
                isClean());
        carData_ = null;
      }
      return carDataBuilder_;
    }

    private java.util.List<xddq.pb.WarSeasonManorDataMsg> manorData_ =
      java.util.Collections.emptyList();
    private void ensureManorDataIsMutable() {
      if (!((bitField0_ & 0x00000400) != 0)) {
        manorData_ = new java.util.ArrayList<xddq.pb.WarSeasonManorDataMsg>(manorData_);
        bitField0_ |= 0x00000400;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.WarSeasonManorDataMsg, xddq.pb.WarSeasonManorDataMsg.Builder, xddq.pb.WarSeasonManorDataMsgOrBuilder> manorDataBuilder_;

    /**
     * <code>repeated .xddq.pb.WarSeasonManorDataMsg manorData = 11;</code>
     */
    public java.util.List<xddq.pb.WarSeasonManorDataMsg> getManorDataList() {
      if (manorDataBuilder_ == null) {
        return java.util.Collections.unmodifiableList(manorData_);
      } else {
        return manorDataBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonManorDataMsg manorData = 11;</code>
     */
    public int getManorDataCount() {
      if (manorDataBuilder_ == null) {
        return manorData_.size();
      } else {
        return manorDataBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonManorDataMsg manorData = 11;</code>
     */
    public xddq.pb.WarSeasonManorDataMsg getManorData(int index) {
      if (manorDataBuilder_ == null) {
        return manorData_.get(index);
      } else {
        return manorDataBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonManorDataMsg manorData = 11;</code>
     */
    public Builder setManorData(
        int index, xddq.pb.WarSeasonManorDataMsg value) {
      if (manorDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureManorDataIsMutable();
        manorData_.set(index, value);
        onChanged();
      } else {
        manorDataBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonManorDataMsg manorData = 11;</code>
     */
    public Builder setManorData(
        int index, xddq.pb.WarSeasonManorDataMsg.Builder builderForValue) {
      if (manorDataBuilder_ == null) {
        ensureManorDataIsMutable();
        manorData_.set(index, builderForValue.build());
        onChanged();
      } else {
        manorDataBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonManorDataMsg manorData = 11;</code>
     */
    public Builder addManorData(xddq.pb.WarSeasonManorDataMsg value) {
      if (manorDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureManorDataIsMutable();
        manorData_.add(value);
        onChanged();
      } else {
        manorDataBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonManorDataMsg manorData = 11;</code>
     */
    public Builder addManorData(
        int index, xddq.pb.WarSeasonManorDataMsg value) {
      if (manorDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureManorDataIsMutable();
        manorData_.add(index, value);
        onChanged();
      } else {
        manorDataBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonManorDataMsg manorData = 11;</code>
     */
    public Builder addManorData(
        xddq.pb.WarSeasonManorDataMsg.Builder builderForValue) {
      if (manorDataBuilder_ == null) {
        ensureManorDataIsMutable();
        manorData_.add(builderForValue.build());
        onChanged();
      } else {
        manorDataBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonManorDataMsg manorData = 11;</code>
     */
    public Builder addManorData(
        int index, xddq.pb.WarSeasonManorDataMsg.Builder builderForValue) {
      if (manorDataBuilder_ == null) {
        ensureManorDataIsMutable();
        manorData_.add(index, builderForValue.build());
        onChanged();
      } else {
        manorDataBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonManorDataMsg manorData = 11;</code>
     */
    public Builder addAllManorData(
        java.lang.Iterable<? extends xddq.pb.WarSeasonManorDataMsg> values) {
      if (manorDataBuilder_ == null) {
        ensureManorDataIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, manorData_);
        onChanged();
      } else {
        manorDataBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonManorDataMsg manorData = 11;</code>
     */
    public Builder clearManorData() {
      if (manorDataBuilder_ == null) {
        manorData_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000400);
        onChanged();
      } else {
        manorDataBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonManorDataMsg manorData = 11;</code>
     */
    public Builder removeManorData(int index) {
      if (manorDataBuilder_ == null) {
        ensureManorDataIsMutable();
        manorData_.remove(index);
        onChanged();
      } else {
        manorDataBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonManorDataMsg manorData = 11;</code>
     */
    public xddq.pb.WarSeasonManorDataMsg.Builder getManorDataBuilder(
        int index) {
      return internalGetManorDataFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonManorDataMsg manorData = 11;</code>
     */
    public xddq.pb.WarSeasonManorDataMsgOrBuilder getManorDataOrBuilder(
        int index) {
      if (manorDataBuilder_ == null) {
        return manorData_.get(index);  } else {
        return manorDataBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonManorDataMsg manorData = 11;</code>
     */
    public java.util.List<? extends xddq.pb.WarSeasonManorDataMsgOrBuilder> 
         getManorDataOrBuilderList() {
      if (manorDataBuilder_ != null) {
        return manorDataBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(manorData_);
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonManorDataMsg manorData = 11;</code>
     */
    public xddq.pb.WarSeasonManorDataMsg.Builder addManorDataBuilder() {
      return internalGetManorDataFieldBuilder().addBuilder(
          xddq.pb.WarSeasonManorDataMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonManorDataMsg manorData = 11;</code>
     */
    public xddq.pb.WarSeasonManorDataMsg.Builder addManorDataBuilder(
        int index) {
      return internalGetManorDataFieldBuilder().addBuilder(
          index, xddq.pb.WarSeasonManorDataMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonManorDataMsg manorData = 11;</code>
     */
    public java.util.List<xddq.pb.WarSeasonManorDataMsg.Builder> 
         getManorDataBuilderList() {
      return internalGetManorDataFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.WarSeasonManorDataMsg, xddq.pb.WarSeasonManorDataMsg.Builder, xddq.pb.WarSeasonManorDataMsgOrBuilder> 
        internalGetManorDataFieldBuilder() {
      if (manorDataBuilder_ == null) {
        manorDataBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.WarSeasonManorDataMsg, xddq.pb.WarSeasonManorDataMsg.Builder, xddq.pb.WarSeasonManorDataMsgOrBuilder>(
                manorData_,
                ((bitField0_ & 0x00000400) != 0),
                getParentForChildren(),
                isClean());
        manorData_ = null;
      }
      return manorDataBuilder_;
    }

    private long lastMoveCapitalTime_ ;
    /**
     * <code>optional int64 lastMoveCapitalTime = 12;</code>
     * @return Whether the lastMoveCapitalTime field is set.
     */
    @java.lang.Override
    public boolean hasLastMoveCapitalTime() {
      return ((bitField0_ & 0x00000800) != 0);
    }
    /**
     * <code>optional int64 lastMoveCapitalTime = 12;</code>
     * @return The lastMoveCapitalTime.
     */
    @java.lang.Override
    public long getLastMoveCapitalTime() {
      return lastMoveCapitalTime_;
    }
    /**
     * <code>optional int64 lastMoveCapitalTime = 12;</code>
     * @param value The lastMoveCapitalTime to set.
     * @return This builder for chaining.
     */
    public Builder setLastMoveCapitalTime(long value) {

      lastMoveCapitalTime_ = value;
      bitField0_ |= 0x00000800;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 lastMoveCapitalTime = 12;</code>
     * @return This builder for chaining.
     */
    public Builder clearLastMoveCapitalTime() {
      bitField0_ = (bitField0_ & ~0x00000800);
      lastMoveCapitalTime_ = 0L;
      onChanged();
      return this;
    }

    private java.lang.Object params_ = "";
    /**
     * <code>optional string params = 13;</code>
     * @return Whether the params field is set.
     */
    public boolean hasParams() {
      return ((bitField0_ & 0x00001000) != 0);
    }
    /**
     * <code>optional string params = 13;</code>
     * @return The params.
     */
    public java.lang.String getParams() {
      java.lang.Object ref = params_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          params_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string params = 13;</code>
     * @return The bytes for params.
     */
    public com.google.protobuf.ByteString
        getParamsBytes() {
      java.lang.Object ref = params_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        params_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string params = 13;</code>
     * @param value The params to set.
     * @return This builder for chaining.
     */
    public Builder setParams(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      params_ = value;
      bitField0_ |= 0x00001000;
      onChanged();
      return this;
    }
    /**
     * <code>optional string params = 13;</code>
     * @return This builder for chaining.
     */
    public Builder clearParams() {
      params_ = getDefaultInstance().getParams();
      bitField0_ = (bitField0_ & ~0x00001000);
      onChanged();
      return this;
    }
    /**
     * <code>optional string params = 13;</code>
     * @param value The bytes for params to set.
     * @return This builder for chaining.
     */
    public Builder setParamsBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      params_ = value;
      bitField0_ |= 0x00001000;
      onChanged();
      return this;
    }

    private com.google.protobuf.Internal.IntList passStage_ = emptyIntList();
    private void ensurePassStageIsMutable() {
      if (!passStage_.isModifiable()) {
        passStage_ = makeMutableCopy(passStage_);
      }
      bitField0_ |= 0x00002000;
    }
    /**
     * <code>repeated int32 passStage = 14;</code>
     * @return A list containing the passStage.
     */
    public java.util.List<java.lang.Integer>
        getPassStageList() {
      passStage_.makeImmutable();
      return passStage_;
    }
    /**
     * <code>repeated int32 passStage = 14;</code>
     * @return The count of passStage.
     */
    public int getPassStageCount() {
      return passStage_.size();
    }
    /**
     * <code>repeated int32 passStage = 14;</code>
     * @param index The index of the element to return.
     * @return The passStage at the given index.
     */
    public int getPassStage(int index) {
      return passStage_.getInt(index);
    }
    /**
     * <code>repeated int32 passStage = 14;</code>
     * @param index The index to set the value at.
     * @param value The passStage to set.
     * @return This builder for chaining.
     */
    public Builder setPassStage(
        int index, int value) {

      ensurePassStageIsMutable();
      passStage_.setInt(index, value);
      bitField0_ |= 0x00002000;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 passStage = 14;</code>
     * @param value The passStage to add.
     * @return This builder for chaining.
     */
    public Builder addPassStage(int value) {

      ensurePassStageIsMutable();
      passStage_.addInt(value);
      bitField0_ |= 0x00002000;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 passStage = 14;</code>
     * @param values The passStage to add.
     * @return This builder for chaining.
     */
    public Builder addAllPassStage(
        java.lang.Iterable<? extends java.lang.Integer> values) {
      ensurePassStageIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, passStage_);
      bitField0_ |= 0x00002000;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 passStage = 14;</code>
     * @return This builder for chaining.
     */
    public Builder clearPassStage() {
      passStage_ = emptyIntList();
      bitField0_ = (bitField0_ & ~0x00002000);
      onChanged();
      return this;
    }

    private int challengeTimes_ ;
    /**
     * <code>optional int32 challengeTimes = 15;</code>
     * @return Whether the challengeTimes field is set.
     */
    @java.lang.Override
    public boolean hasChallengeTimes() {
      return ((bitField0_ & 0x00004000) != 0);
    }
    /**
     * <code>optional int32 challengeTimes = 15;</code>
     * @return The challengeTimes.
     */
    @java.lang.Override
    public int getChallengeTimes() {
      return challengeTimes_;
    }
    /**
     * <code>optional int32 challengeTimes = 15;</code>
     * @param value The challengeTimes to set.
     * @return This builder for chaining.
     */
    public Builder setChallengeTimes(int value) {

      challengeTimes_ = value;
      bitField0_ |= 0x00004000;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 challengeTimes = 15;</code>
     * @return This builder for chaining.
     */
    public Builder clearChallengeTimes() {
      bitField0_ = (bitField0_ & ~0x00004000);
      challengeTimes_ = 0;
      onChanged();
      return this;
    }

    private int itemChallengeTimes_ ;
    /**
     * <code>optional int32 itemChallengeTimes = 16;</code>
     * @return Whether the itemChallengeTimes field is set.
     */
    @java.lang.Override
    public boolean hasItemChallengeTimes() {
      return ((bitField0_ & 0x00008000) != 0);
    }
    /**
     * <code>optional int32 itemChallengeTimes = 16;</code>
     * @return The itemChallengeTimes.
     */
    @java.lang.Override
    public int getItemChallengeTimes() {
      return itemChallengeTimes_;
    }
    /**
     * <code>optional int32 itemChallengeTimes = 16;</code>
     * @param value The itemChallengeTimes to set.
     * @return This builder for chaining.
     */
    public Builder setItemChallengeTimes(int value) {

      itemChallengeTimes_ = value;
      bitField0_ |= 0x00008000;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 itemChallengeTimes = 16;</code>
     * @return This builder for chaining.
     */
    public Builder clearItemChallengeTimes() {
      bitField0_ = (bitField0_ & ~0x00008000);
      itemChallengeTimes_ = 0;
      onChanged();
      return this;
    }

    private int battleTimes_ ;
    /**
     * <code>optional int32 battleTimes = 17;</code>
     * @return Whether the battleTimes field is set.
     */
    @java.lang.Override
    public boolean hasBattleTimes() {
      return ((bitField0_ & 0x00010000) != 0);
    }
    /**
     * <code>optional int32 battleTimes = 17;</code>
     * @return The battleTimes.
     */
    @java.lang.Override
    public int getBattleTimes() {
      return battleTimes_;
    }
    /**
     * <code>optional int32 battleTimes = 17;</code>
     * @param value The battleTimes to set.
     * @return This builder for chaining.
     */
    public Builder setBattleTimes(int value) {

      battleTimes_ = value;
      bitField0_ |= 0x00010000;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 battleTimes = 17;</code>
     * @return This builder for chaining.
     */
    public Builder clearBattleTimes() {
      bitField0_ = (bitField0_ & ~0x00010000);
      battleTimes_ = 0;
      onChanged();
      return this;
    }

    private com.google.protobuf.Internal.IntList cityLevel_ = emptyIntList();
    private void ensureCityLevelIsMutable() {
      if (!cityLevel_.isModifiable()) {
        cityLevel_ = makeMutableCopy(cityLevel_);
      }
      bitField0_ |= 0x00020000;
    }
    /**
     * <code>repeated int32 cityLevel = 18;</code>
     * @return A list containing the cityLevel.
     */
    public java.util.List<java.lang.Integer>
        getCityLevelList() {
      cityLevel_.makeImmutable();
      return cityLevel_;
    }
    /**
     * <code>repeated int32 cityLevel = 18;</code>
     * @return The count of cityLevel.
     */
    public int getCityLevelCount() {
      return cityLevel_.size();
    }
    /**
     * <code>repeated int32 cityLevel = 18;</code>
     * @param index The index of the element to return.
     * @return The cityLevel at the given index.
     */
    public int getCityLevel(int index) {
      return cityLevel_.getInt(index);
    }
    /**
     * <code>repeated int32 cityLevel = 18;</code>
     * @param index The index to set the value at.
     * @param value The cityLevel to set.
     * @return This builder for chaining.
     */
    public Builder setCityLevel(
        int index, int value) {

      ensureCityLevelIsMutable();
      cityLevel_.setInt(index, value);
      bitField0_ |= 0x00020000;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 cityLevel = 18;</code>
     * @param value The cityLevel to add.
     * @return This builder for chaining.
     */
    public Builder addCityLevel(int value) {

      ensureCityLevelIsMutable();
      cityLevel_.addInt(value);
      bitField0_ |= 0x00020000;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 cityLevel = 18;</code>
     * @param values The cityLevel to add.
     * @return This builder for chaining.
     */
    public Builder addAllCityLevel(
        java.lang.Iterable<? extends java.lang.Integer> values) {
      ensureCityLevelIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, cityLevel_);
      bitField0_ |= 0x00020000;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 cityLevel = 18;</code>
     * @return This builder for chaining.
     */
    public Builder clearCityLevel() {
      cityLevel_ = emptyIntList();
      bitField0_ = (bitField0_ & ~0x00020000);
      onChanged();
      return this;
    }

    private java.lang.Object curFightValue_ = "";
    /**
     * <code>optional string curFightValue = 19;</code>
     * @return Whether the curFightValue field is set.
     */
    public boolean hasCurFightValue() {
      return ((bitField0_ & 0x00040000) != 0);
    }
    /**
     * <code>optional string curFightValue = 19;</code>
     * @return The curFightValue.
     */
    public java.lang.String getCurFightValue() {
      java.lang.Object ref = curFightValue_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          curFightValue_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string curFightValue = 19;</code>
     * @return The bytes for curFightValue.
     */
    public com.google.protobuf.ByteString
        getCurFightValueBytes() {
      java.lang.Object ref = curFightValue_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        curFightValue_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string curFightValue = 19;</code>
     * @param value The curFightValue to set.
     * @return This builder for chaining.
     */
    public Builder setCurFightValue(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      curFightValue_ = value;
      bitField0_ |= 0x00040000;
      onChanged();
      return this;
    }
    /**
     * <code>optional string curFightValue = 19;</code>
     * @return This builder for chaining.
     */
    public Builder clearCurFightValue() {
      curFightValue_ = getDefaultInstance().getCurFightValue();
      bitField0_ = (bitField0_ & ~0x00040000);
      onChanged();
      return this;
    }
    /**
     * <code>optional string curFightValue = 19;</code>
     * @param value The bytes for curFightValue to set.
     * @return This builder for chaining.
     */
    public Builder setCurFightValueBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      curFightValue_ = value;
      bitField0_ |= 0x00040000;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.WarSeasonOrderDeclareFightDataMsg> orderFightData_ =
      java.util.Collections.emptyList();
    private void ensureOrderFightDataIsMutable() {
      if (!((bitField0_ & 0x00080000) != 0)) {
        orderFightData_ = new java.util.ArrayList<xddq.pb.WarSeasonOrderDeclareFightDataMsg>(orderFightData_);
        bitField0_ |= 0x00080000;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.WarSeasonOrderDeclareFightDataMsg, xddq.pb.WarSeasonOrderDeclareFightDataMsg.Builder, xddq.pb.WarSeasonOrderDeclareFightDataMsgOrBuilder> orderFightDataBuilder_;

    /**
     * <code>repeated .xddq.pb.WarSeasonOrderDeclareFightDataMsg orderFightData = 20;</code>
     */
    public java.util.List<xddq.pb.WarSeasonOrderDeclareFightDataMsg> getOrderFightDataList() {
      if (orderFightDataBuilder_ == null) {
        return java.util.Collections.unmodifiableList(orderFightData_);
      } else {
        return orderFightDataBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonOrderDeclareFightDataMsg orderFightData = 20;</code>
     */
    public int getOrderFightDataCount() {
      if (orderFightDataBuilder_ == null) {
        return orderFightData_.size();
      } else {
        return orderFightDataBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonOrderDeclareFightDataMsg orderFightData = 20;</code>
     */
    public xddq.pb.WarSeasonOrderDeclareFightDataMsg getOrderFightData(int index) {
      if (orderFightDataBuilder_ == null) {
        return orderFightData_.get(index);
      } else {
        return orderFightDataBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonOrderDeclareFightDataMsg orderFightData = 20;</code>
     */
    public Builder setOrderFightData(
        int index, xddq.pb.WarSeasonOrderDeclareFightDataMsg value) {
      if (orderFightDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureOrderFightDataIsMutable();
        orderFightData_.set(index, value);
        onChanged();
      } else {
        orderFightDataBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonOrderDeclareFightDataMsg orderFightData = 20;</code>
     */
    public Builder setOrderFightData(
        int index, xddq.pb.WarSeasonOrderDeclareFightDataMsg.Builder builderForValue) {
      if (orderFightDataBuilder_ == null) {
        ensureOrderFightDataIsMutable();
        orderFightData_.set(index, builderForValue.build());
        onChanged();
      } else {
        orderFightDataBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonOrderDeclareFightDataMsg orderFightData = 20;</code>
     */
    public Builder addOrderFightData(xddq.pb.WarSeasonOrderDeclareFightDataMsg value) {
      if (orderFightDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureOrderFightDataIsMutable();
        orderFightData_.add(value);
        onChanged();
      } else {
        orderFightDataBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonOrderDeclareFightDataMsg orderFightData = 20;</code>
     */
    public Builder addOrderFightData(
        int index, xddq.pb.WarSeasonOrderDeclareFightDataMsg value) {
      if (orderFightDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureOrderFightDataIsMutable();
        orderFightData_.add(index, value);
        onChanged();
      } else {
        orderFightDataBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonOrderDeclareFightDataMsg orderFightData = 20;</code>
     */
    public Builder addOrderFightData(
        xddq.pb.WarSeasonOrderDeclareFightDataMsg.Builder builderForValue) {
      if (orderFightDataBuilder_ == null) {
        ensureOrderFightDataIsMutable();
        orderFightData_.add(builderForValue.build());
        onChanged();
      } else {
        orderFightDataBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonOrderDeclareFightDataMsg orderFightData = 20;</code>
     */
    public Builder addOrderFightData(
        int index, xddq.pb.WarSeasonOrderDeclareFightDataMsg.Builder builderForValue) {
      if (orderFightDataBuilder_ == null) {
        ensureOrderFightDataIsMutable();
        orderFightData_.add(index, builderForValue.build());
        onChanged();
      } else {
        orderFightDataBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonOrderDeclareFightDataMsg orderFightData = 20;</code>
     */
    public Builder addAllOrderFightData(
        java.lang.Iterable<? extends xddq.pb.WarSeasonOrderDeclareFightDataMsg> values) {
      if (orderFightDataBuilder_ == null) {
        ensureOrderFightDataIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, orderFightData_);
        onChanged();
      } else {
        orderFightDataBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonOrderDeclareFightDataMsg orderFightData = 20;</code>
     */
    public Builder clearOrderFightData() {
      if (orderFightDataBuilder_ == null) {
        orderFightData_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00080000);
        onChanged();
      } else {
        orderFightDataBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonOrderDeclareFightDataMsg orderFightData = 20;</code>
     */
    public Builder removeOrderFightData(int index) {
      if (orderFightDataBuilder_ == null) {
        ensureOrderFightDataIsMutable();
        orderFightData_.remove(index);
        onChanged();
      } else {
        orderFightDataBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonOrderDeclareFightDataMsg orderFightData = 20;</code>
     */
    public xddq.pb.WarSeasonOrderDeclareFightDataMsg.Builder getOrderFightDataBuilder(
        int index) {
      return internalGetOrderFightDataFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonOrderDeclareFightDataMsg orderFightData = 20;</code>
     */
    public xddq.pb.WarSeasonOrderDeclareFightDataMsgOrBuilder getOrderFightDataOrBuilder(
        int index) {
      if (orderFightDataBuilder_ == null) {
        return orderFightData_.get(index);  } else {
        return orderFightDataBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonOrderDeclareFightDataMsg orderFightData = 20;</code>
     */
    public java.util.List<? extends xddq.pb.WarSeasonOrderDeclareFightDataMsgOrBuilder> 
         getOrderFightDataOrBuilderList() {
      if (orderFightDataBuilder_ != null) {
        return orderFightDataBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(orderFightData_);
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonOrderDeclareFightDataMsg orderFightData = 20;</code>
     */
    public xddq.pb.WarSeasonOrderDeclareFightDataMsg.Builder addOrderFightDataBuilder() {
      return internalGetOrderFightDataFieldBuilder().addBuilder(
          xddq.pb.WarSeasonOrderDeclareFightDataMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonOrderDeclareFightDataMsg orderFightData = 20;</code>
     */
    public xddq.pb.WarSeasonOrderDeclareFightDataMsg.Builder addOrderFightDataBuilder(
        int index) {
      return internalGetOrderFightDataFieldBuilder().addBuilder(
          index, xddq.pb.WarSeasonOrderDeclareFightDataMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonOrderDeclareFightDataMsg orderFightData = 20;</code>
     */
    public java.util.List<xddq.pb.WarSeasonOrderDeclareFightDataMsg.Builder> 
         getOrderFightDataBuilderList() {
      return internalGetOrderFightDataFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.WarSeasonOrderDeclareFightDataMsg, xddq.pb.WarSeasonOrderDeclareFightDataMsg.Builder, xddq.pb.WarSeasonOrderDeclareFightDataMsgOrBuilder> 
        internalGetOrderFightDataFieldBuilder() {
      if (orderFightDataBuilder_ == null) {
        orderFightDataBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.WarSeasonOrderDeclareFightDataMsg, xddq.pb.WarSeasonOrderDeclareFightDataMsg.Builder, xddq.pb.WarSeasonOrderDeclareFightDataMsgOrBuilder>(
                orderFightData_,
                ((bitField0_ & 0x00080000) != 0),
                getParentForChildren(),
                isClean());
        orderFightData_ = null;
      }
      return orderFightDataBuilder_;
    }

    private java.util.List<xddq.pb.WarSeasonEffectStrategyInfo> otherUnionStrategyList_ =
      java.util.Collections.emptyList();
    private void ensureOtherUnionStrategyListIsMutable() {
      if (!((bitField0_ & 0x00100000) != 0)) {
        otherUnionStrategyList_ = new java.util.ArrayList<xddq.pb.WarSeasonEffectStrategyInfo>(otherUnionStrategyList_);
        bitField0_ |= 0x00100000;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.WarSeasonEffectStrategyInfo, xddq.pb.WarSeasonEffectStrategyInfo.Builder, xddq.pb.WarSeasonEffectStrategyInfoOrBuilder> otherUnionStrategyListBuilder_;

    /**
     * <code>repeated .xddq.pb.WarSeasonEffectStrategyInfo otherUnionStrategyList = 21;</code>
     */
    public java.util.List<xddq.pb.WarSeasonEffectStrategyInfo> getOtherUnionStrategyListList() {
      if (otherUnionStrategyListBuilder_ == null) {
        return java.util.Collections.unmodifiableList(otherUnionStrategyList_);
      } else {
        return otherUnionStrategyListBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonEffectStrategyInfo otherUnionStrategyList = 21;</code>
     */
    public int getOtherUnionStrategyListCount() {
      if (otherUnionStrategyListBuilder_ == null) {
        return otherUnionStrategyList_.size();
      } else {
        return otherUnionStrategyListBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonEffectStrategyInfo otherUnionStrategyList = 21;</code>
     */
    public xddq.pb.WarSeasonEffectStrategyInfo getOtherUnionStrategyList(int index) {
      if (otherUnionStrategyListBuilder_ == null) {
        return otherUnionStrategyList_.get(index);
      } else {
        return otherUnionStrategyListBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonEffectStrategyInfo otherUnionStrategyList = 21;</code>
     */
    public Builder setOtherUnionStrategyList(
        int index, xddq.pb.WarSeasonEffectStrategyInfo value) {
      if (otherUnionStrategyListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureOtherUnionStrategyListIsMutable();
        otherUnionStrategyList_.set(index, value);
        onChanged();
      } else {
        otherUnionStrategyListBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonEffectStrategyInfo otherUnionStrategyList = 21;</code>
     */
    public Builder setOtherUnionStrategyList(
        int index, xddq.pb.WarSeasonEffectStrategyInfo.Builder builderForValue) {
      if (otherUnionStrategyListBuilder_ == null) {
        ensureOtherUnionStrategyListIsMutable();
        otherUnionStrategyList_.set(index, builderForValue.build());
        onChanged();
      } else {
        otherUnionStrategyListBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonEffectStrategyInfo otherUnionStrategyList = 21;</code>
     */
    public Builder addOtherUnionStrategyList(xddq.pb.WarSeasonEffectStrategyInfo value) {
      if (otherUnionStrategyListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureOtherUnionStrategyListIsMutable();
        otherUnionStrategyList_.add(value);
        onChanged();
      } else {
        otherUnionStrategyListBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonEffectStrategyInfo otherUnionStrategyList = 21;</code>
     */
    public Builder addOtherUnionStrategyList(
        int index, xddq.pb.WarSeasonEffectStrategyInfo value) {
      if (otherUnionStrategyListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureOtherUnionStrategyListIsMutable();
        otherUnionStrategyList_.add(index, value);
        onChanged();
      } else {
        otherUnionStrategyListBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonEffectStrategyInfo otherUnionStrategyList = 21;</code>
     */
    public Builder addOtherUnionStrategyList(
        xddq.pb.WarSeasonEffectStrategyInfo.Builder builderForValue) {
      if (otherUnionStrategyListBuilder_ == null) {
        ensureOtherUnionStrategyListIsMutable();
        otherUnionStrategyList_.add(builderForValue.build());
        onChanged();
      } else {
        otherUnionStrategyListBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonEffectStrategyInfo otherUnionStrategyList = 21;</code>
     */
    public Builder addOtherUnionStrategyList(
        int index, xddq.pb.WarSeasonEffectStrategyInfo.Builder builderForValue) {
      if (otherUnionStrategyListBuilder_ == null) {
        ensureOtherUnionStrategyListIsMutable();
        otherUnionStrategyList_.add(index, builderForValue.build());
        onChanged();
      } else {
        otherUnionStrategyListBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonEffectStrategyInfo otherUnionStrategyList = 21;</code>
     */
    public Builder addAllOtherUnionStrategyList(
        java.lang.Iterable<? extends xddq.pb.WarSeasonEffectStrategyInfo> values) {
      if (otherUnionStrategyListBuilder_ == null) {
        ensureOtherUnionStrategyListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, otherUnionStrategyList_);
        onChanged();
      } else {
        otherUnionStrategyListBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonEffectStrategyInfo otherUnionStrategyList = 21;</code>
     */
    public Builder clearOtherUnionStrategyList() {
      if (otherUnionStrategyListBuilder_ == null) {
        otherUnionStrategyList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00100000);
        onChanged();
      } else {
        otherUnionStrategyListBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonEffectStrategyInfo otherUnionStrategyList = 21;</code>
     */
    public Builder removeOtherUnionStrategyList(int index) {
      if (otherUnionStrategyListBuilder_ == null) {
        ensureOtherUnionStrategyListIsMutable();
        otherUnionStrategyList_.remove(index);
        onChanged();
      } else {
        otherUnionStrategyListBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonEffectStrategyInfo otherUnionStrategyList = 21;</code>
     */
    public xddq.pb.WarSeasonEffectStrategyInfo.Builder getOtherUnionStrategyListBuilder(
        int index) {
      return internalGetOtherUnionStrategyListFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonEffectStrategyInfo otherUnionStrategyList = 21;</code>
     */
    public xddq.pb.WarSeasonEffectStrategyInfoOrBuilder getOtherUnionStrategyListOrBuilder(
        int index) {
      if (otherUnionStrategyListBuilder_ == null) {
        return otherUnionStrategyList_.get(index);  } else {
        return otherUnionStrategyListBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonEffectStrategyInfo otherUnionStrategyList = 21;</code>
     */
    public java.util.List<? extends xddq.pb.WarSeasonEffectStrategyInfoOrBuilder> 
         getOtherUnionStrategyListOrBuilderList() {
      if (otherUnionStrategyListBuilder_ != null) {
        return otherUnionStrategyListBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(otherUnionStrategyList_);
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonEffectStrategyInfo otherUnionStrategyList = 21;</code>
     */
    public xddq.pb.WarSeasonEffectStrategyInfo.Builder addOtherUnionStrategyListBuilder() {
      return internalGetOtherUnionStrategyListFieldBuilder().addBuilder(
          xddq.pb.WarSeasonEffectStrategyInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonEffectStrategyInfo otherUnionStrategyList = 21;</code>
     */
    public xddq.pb.WarSeasonEffectStrategyInfo.Builder addOtherUnionStrategyListBuilder(
        int index) {
      return internalGetOtherUnionStrategyListFieldBuilder().addBuilder(
          index, xddq.pb.WarSeasonEffectStrategyInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonEffectStrategyInfo otherUnionStrategyList = 21;</code>
     */
    public java.util.List<xddq.pb.WarSeasonEffectStrategyInfo.Builder> 
         getOtherUnionStrategyListBuilderList() {
      return internalGetOtherUnionStrategyListFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.WarSeasonEffectStrategyInfo, xddq.pb.WarSeasonEffectStrategyInfo.Builder, xddq.pb.WarSeasonEffectStrategyInfoOrBuilder> 
        internalGetOtherUnionStrategyListFieldBuilder() {
      if (otherUnionStrategyListBuilder_ == null) {
        otherUnionStrategyListBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.WarSeasonEffectStrategyInfo, xddq.pb.WarSeasonEffectStrategyInfo.Builder, xddq.pb.WarSeasonEffectStrategyInfoOrBuilder>(
                otherUnionStrategyList_,
                ((bitField0_ & 0x00100000) != 0),
                getParentForChildren(),
                isClean());
        otherUnionStrategyList_ = null;
      }
      return otherUnionStrategyListBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.WarSeasonEnterMapResp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.WarSeasonEnterMapResp)
  private static final xddq.pb.WarSeasonEnterMapResp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.WarSeasonEnterMapResp();
  }

  public static xddq.pb.WarSeasonEnterMapResp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<WarSeasonEnterMapResp>
      PARSER = new com.google.protobuf.AbstractParser<WarSeasonEnterMapResp>() {
    @java.lang.Override
    public WarSeasonEnterMapResp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<WarSeasonEnterMapResp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<WarSeasonEnterMapResp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.WarSeasonEnterMapResp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

