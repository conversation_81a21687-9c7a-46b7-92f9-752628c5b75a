// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

public interface PupilRogueBattleCloudMsgOrBuilder extends
    // @@protoc_insertion_point(interface_extends:xddq.pb.PupilRogueBattleCloudMsg)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>optional int32 objId = 1;</code>
   * @return Whether the objId field is set.
   */
  boolean hasObjId();
  /**
   * <code>optional int32 objId = 1;</code>
   * @return The objId.
   */
  int getObjId();

  /**
   * <code>optional int32 cloudId = 2;</code>
   * @return Whether the cloudId field is set.
   */
  boolean hasCloudId();
  /**
   * <code>optional int32 cloudId = 2;</code>
   * @return The cloudId.
   */
  int getCloudId();

  /**
   * <code>optional int32 skillId = 3;</code>
   * @return Whether the skillId field is set.
   */
  boolean hasSkillId();
  /**
   * <code>optional int32 skillId = 3;</code>
   * @return The skillId.
   */
  int getSkillId();
}
