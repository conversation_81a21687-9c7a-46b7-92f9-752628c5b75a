// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.GodDemonBattleBaseInfoResp}
 */
public final class GodDemonBattleBaseInfoResp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.GodDemonBattleBaseInfoResp)
    GodDemonBattleBaseInfoRespOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      GodDemonBattleBaseInfoResp.class.getName());
  }
  // Use GodDemonBattleBaseInfoResp.newBuilder() to construct.
  private GodDemonBattleBaseInfoResp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private GodDemonBattleBaseInfoResp() {
    campPlayerNum_ = java.util.Collections.emptyList();
    serverIdArray_ = emptyLongList();
    godRankData_ = java.util.Collections.emptyList();
    demonrankData_ = java.util.Collections.emptyList();
    realsMatchList_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_GodDemonBattleBaseInfoResp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_GodDemonBattleBaseInfoResp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.GodDemonBattleBaseInfoResp.class, xddq.pb.GodDemonBattleBaseInfoResp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int CAMPPLAYERNUM_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.GodDemonPlayerCountMsg> campPlayerNum_;
  /**
   * <code>repeated .xddq.pb.GodDemonPlayerCountMsg campPlayerNum = 2;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.GodDemonPlayerCountMsg> getCampPlayerNumList() {
    return campPlayerNum_;
  }
  /**
   * <code>repeated .xddq.pb.GodDemonPlayerCountMsg campPlayerNum = 2;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.GodDemonPlayerCountMsgOrBuilder> 
      getCampPlayerNumOrBuilderList() {
    return campPlayerNum_;
  }
  /**
   * <code>repeated .xddq.pb.GodDemonPlayerCountMsg campPlayerNum = 2;</code>
   */
  @java.lang.Override
  public int getCampPlayerNumCount() {
    return campPlayerNum_.size();
  }
  /**
   * <code>repeated .xddq.pb.GodDemonPlayerCountMsg campPlayerNum = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.GodDemonPlayerCountMsg getCampPlayerNum(int index) {
    return campPlayerNum_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.GodDemonPlayerCountMsg campPlayerNum = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.GodDemonPlayerCountMsgOrBuilder getCampPlayerNumOrBuilder(
      int index) {
    return campPlayerNum_.get(index);
  }

  public static final int SUPPRESSED_FIELD_NUMBER = 3;
  private boolean suppressed_ = false;
  /**
   * <code>optional bool suppressed = 3;</code>
   * @return Whether the suppressed field is set.
   */
  @java.lang.Override
  public boolean hasSuppressed() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional bool suppressed = 3;</code>
   * @return The suppressed.
   */
  @java.lang.Override
  public boolean getSuppressed() {
    return suppressed_;
  }

  public static final int SERVERIDARRAY_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private com.google.protobuf.Internal.LongList serverIdArray_ =
      emptyLongList();
  /**
   * <code>repeated int64 serverIdArray = 4;</code>
   * @return A list containing the serverIdArray.
   */
  @java.lang.Override
  public java.util.List<java.lang.Long>
      getServerIdArrayList() {
    return serverIdArray_;
  }
  /**
   * <code>repeated int64 serverIdArray = 4;</code>
   * @return The count of serverIdArray.
   */
  public int getServerIdArrayCount() {
    return serverIdArray_.size();
  }
  /**
   * <code>repeated int64 serverIdArray = 4;</code>
   * @param index The index of the element to return.
   * @return The serverIdArray at the given index.
   */
  public long getServerIdArray(int index) {
    return serverIdArray_.getLong(index);
  }

  public static final int PLAYERINFO_FIELD_NUMBER = 5;
  private xddq.pb.GodDemonBattlePlayerInfo playerInfo_;
  /**
   * <code>optional .xddq.pb.GodDemonBattlePlayerInfo playerInfo = 5;</code>
   * @return Whether the playerInfo field is set.
   */
  @java.lang.Override
  public boolean hasPlayerInfo() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional .xddq.pb.GodDemonBattlePlayerInfo playerInfo = 5;</code>
   * @return The playerInfo.
   */
  @java.lang.Override
  public xddq.pb.GodDemonBattlePlayerInfo getPlayerInfo() {
    return playerInfo_ == null ? xddq.pb.GodDemonBattlePlayerInfo.getDefaultInstance() : playerInfo_;
  }
  /**
   * <code>optional .xddq.pb.GodDemonBattlePlayerInfo playerInfo = 5;</code>
   */
  @java.lang.Override
  public xddq.pb.GodDemonBattlePlayerInfoOrBuilder getPlayerInfoOrBuilder() {
    return playerInfo_ == null ? xddq.pb.GodDemonBattlePlayerInfo.getDefaultInstance() : playerInfo_;
  }

  public static final int GODRANKDATA_FIELD_NUMBER = 6;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.GodDemonRankData> godRankData_;
  /**
   * <code>repeated .xddq.pb.GodDemonRankData godRankData = 6;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.GodDemonRankData> getGodRankDataList() {
    return godRankData_;
  }
  /**
   * <code>repeated .xddq.pb.GodDemonRankData godRankData = 6;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.GodDemonRankDataOrBuilder> 
      getGodRankDataOrBuilderList() {
    return godRankData_;
  }
  /**
   * <code>repeated .xddq.pb.GodDemonRankData godRankData = 6;</code>
   */
  @java.lang.Override
  public int getGodRankDataCount() {
    return godRankData_.size();
  }
  /**
   * <code>repeated .xddq.pb.GodDemonRankData godRankData = 6;</code>
   */
  @java.lang.Override
  public xddq.pb.GodDemonRankData getGodRankData(int index) {
    return godRankData_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.GodDemonRankData godRankData = 6;</code>
   */
  @java.lang.Override
  public xddq.pb.GodDemonRankDataOrBuilder getGodRankDataOrBuilder(
      int index) {
    return godRankData_.get(index);
  }

  public static final int DEMONRANKDATA_FIELD_NUMBER = 7;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.GodDemonRankData> demonrankData_;
  /**
   * <code>repeated .xddq.pb.GodDemonRankData DemonrankData = 7;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.GodDemonRankData> getDemonrankDataList() {
    return demonrankData_;
  }
  /**
   * <code>repeated .xddq.pb.GodDemonRankData DemonrankData = 7;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.GodDemonRankDataOrBuilder> 
      getDemonrankDataOrBuilderList() {
    return demonrankData_;
  }
  /**
   * <code>repeated .xddq.pb.GodDemonRankData DemonrankData = 7;</code>
   */
  @java.lang.Override
  public int getDemonrankDataCount() {
    return demonrankData_.size();
  }
  /**
   * <code>repeated .xddq.pb.GodDemonRankData DemonrankData = 7;</code>
   */
  @java.lang.Override
  public xddq.pb.GodDemonRankData getDemonrankData(int index) {
    return demonrankData_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.GodDemonRankData DemonrankData = 7;</code>
   */
  @java.lang.Override
  public xddq.pb.GodDemonRankDataOrBuilder getDemonrankDataOrBuilder(
      int index) {
    return demonrankData_.get(index);
  }

  public static final int SESSION_FIELD_NUMBER = 8;
  private int session_ = 0;
  /**
   * <code>optional int32 session = 8;</code>
   * @return Whether the session field is set.
   */
  @java.lang.Override
  public boolean hasSession() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional int32 session = 8;</code>
   * @return The session.
   */
  @java.lang.Override
  public int getSession() {
    return session_;
  }

  public static final int INROOMBATTLE_FIELD_NUMBER = 9;
  private boolean inRoomBattle_ = false;
  /**
   * <code>optional bool inRoomBattle = 9;</code>
   * @return Whether the inRoomBattle field is set.
   */
  @java.lang.Override
  public boolean hasInRoomBattle() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>optional bool inRoomBattle = 9;</code>
   * @return The inRoomBattle.
   */
  @java.lang.Override
  public boolean getInRoomBattle() {
    return inRoomBattle_;
  }

  public static final int REALSMATCHLIST_FIELD_NUMBER = 10;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.GodDemonRealsMatchMsg> realsMatchList_;
  /**
   * <code>repeated .xddq.pb.GodDemonRealsMatchMsg realsMatchList = 10;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.GodDemonRealsMatchMsg> getRealsMatchListList() {
    return realsMatchList_;
  }
  /**
   * <code>repeated .xddq.pb.GodDemonRealsMatchMsg realsMatchList = 10;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.GodDemonRealsMatchMsgOrBuilder> 
      getRealsMatchListOrBuilderList() {
    return realsMatchList_;
  }
  /**
   * <code>repeated .xddq.pb.GodDemonRealsMatchMsg realsMatchList = 10;</code>
   */
  @java.lang.Override
  public int getRealsMatchListCount() {
    return realsMatchList_.size();
  }
  /**
   * <code>repeated .xddq.pb.GodDemonRealsMatchMsg realsMatchList = 10;</code>
   */
  @java.lang.Override
  public xddq.pb.GodDemonRealsMatchMsg getRealsMatchList(int index) {
    return realsMatchList_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.GodDemonRealsMatchMsg realsMatchList = 10;</code>
   */
  @java.lang.Override
  public xddq.pb.GodDemonRealsMatchMsgOrBuilder getRealsMatchListOrBuilder(
      int index) {
    return realsMatchList_.get(index);
  }

  public static final int WINCAMP_FIELD_NUMBER = 11;
  private int winCamp_ = 0;
  /**
   * <code>optional int32 winCamp = 11;</code>
   * @return Whether the winCamp field is set.
   */
  @java.lang.Override
  public boolean hasWinCamp() {
    return ((bitField0_ & 0x00000020) != 0);
  }
  /**
   * <code>optional int32 winCamp = 11;</code>
   * @return The winCamp.
   */
  @java.lang.Override
  public int getWinCamp() {
    return winCamp_;
  }

  public static final int CANRECEIVECAMPREWARD_FIELD_NUMBER = 12;
  private boolean canReceiveCampReward_ = false;
  /**
   * <code>optional bool canReceiveCampReward = 12;</code>
   * @return Whether the canReceiveCampReward field is set.
   */
  @java.lang.Override
  public boolean hasCanReceiveCampReward() {
    return ((bitField0_ & 0x00000040) != 0);
  }
  /**
   * <code>optional bool canReceiveCampReward = 12;</code>
   * @return The canReceiveCampReward.
   */
  @java.lang.Override
  public boolean getCanReceiveCampReward() {
    return canReceiveCampReward_;
  }

  public static final int MAXJOINREALMS_FIELD_NUMBER = 13;
  private int maxJoinRealms_ = 0;
  /**
   * <code>optional int32 maxJoinRealms = 13;</code>
   * @return Whether the maxJoinRealms field is set.
   */
  @java.lang.Override
  public boolean hasMaxJoinRealms() {
    return ((bitField0_ & 0x00000080) != 0);
  }
  /**
   * <code>optional int32 maxJoinRealms = 13;</code>
   * @return The maxJoinRealms.
   */
  @java.lang.Override
  public int getMaxJoinRealms() {
    return maxJoinRealms_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    for (int i = 0; i < getRealsMatchListCount(); i++) {
      if (!getRealsMatchList(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    for (int i = 0; i < campPlayerNum_.size(); i++) {
      output.writeMessage(2, campPlayerNum_.get(i));
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeBool(3, suppressed_);
    }
    for (int i = 0; i < serverIdArray_.size(); i++) {
      output.writeInt64(4, serverIdArray_.getLong(i));
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeMessage(5, getPlayerInfo());
    }
    for (int i = 0; i < godRankData_.size(); i++) {
      output.writeMessage(6, godRankData_.get(i));
    }
    for (int i = 0; i < demonrankData_.size(); i++) {
      output.writeMessage(7, demonrankData_.get(i));
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt32(8, session_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      output.writeBool(9, inRoomBattle_);
    }
    for (int i = 0; i < realsMatchList_.size(); i++) {
      output.writeMessage(10, realsMatchList_.get(i));
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      output.writeInt32(11, winCamp_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      output.writeBool(12, canReceiveCampReward_);
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      output.writeInt32(13, maxJoinRealms_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    for (int i = 0; i < campPlayerNum_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, campPlayerNum_.get(i));
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(3, suppressed_);
    }
    {
      int dataSize = 0;
      for (int i = 0; i < serverIdArray_.size(); i++) {
        dataSize += com.google.protobuf.CodedOutputStream
          .computeInt64SizeNoTag(serverIdArray_.getLong(i));
      }
      size += dataSize;
      size += 1 * getServerIdArrayList().size();
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(5, getPlayerInfo());
    }
    for (int i = 0; i < godRankData_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(6, godRankData_.get(i));
    }
    for (int i = 0; i < demonrankData_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(7, demonrankData_.get(i));
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(8, session_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(9, inRoomBattle_);
    }
    for (int i = 0; i < realsMatchList_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(10, realsMatchList_.get(i));
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(11, winCamp_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(12, canReceiveCampReward_);
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(13, maxJoinRealms_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.GodDemonBattleBaseInfoResp)) {
      return super.equals(obj);
    }
    xddq.pb.GodDemonBattleBaseInfoResp other = (xddq.pb.GodDemonBattleBaseInfoResp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (!getCampPlayerNumList()
        .equals(other.getCampPlayerNumList())) return false;
    if (hasSuppressed() != other.hasSuppressed()) return false;
    if (hasSuppressed()) {
      if (getSuppressed()
          != other.getSuppressed()) return false;
    }
    if (!getServerIdArrayList()
        .equals(other.getServerIdArrayList())) return false;
    if (hasPlayerInfo() != other.hasPlayerInfo()) return false;
    if (hasPlayerInfo()) {
      if (!getPlayerInfo()
          .equals(other.getPlayerInfo())) return false;
    }
    if (!getGodRankDataList()
        .equals(other.getGodRankDataList())) return false;
    if (!getDemonrankDataList()
        .equals(other.getDemonrankDataList())) return false;
    if (hasSession() != other.hasSession()) return false;
    if (hasSession()) {
      if (getSession()
          != other.getSession()) return false;
    }
    if (hasInRoomBattle() != other.hasInRoomBattle()) return false;
    if (hasInRoomBattle()) {
      if (getInRoomBattle()
          != other.getInRoomBattle()) return false;
    }
    if (!getRealsMatchListList()
        .equals(other.getRealsMatchListList())) return false;
    if (hasWinCamp() != other.hasWinCamp()) return false;
    if (hasWinCamp()) {
      if (getWinCamp()
          != other.getWinCamp()) return false;
    }
    if (hasCanReceiveCampReward() != other.hasCanReceiveCampReward()) return false;
    if (hasCanReceiveCampReward()) {
      if (getCanReceiveCampReward()
          != other.getCanReceiveCampReward()) return false;
    }
    if (hasMaxJoinRealms() != other.hasMaxJoinRealms()) return false;
    if (hasMaxJoinRealms()) {
      if (getMaxJoinRealms()
          != other.getMaxJoinRealms()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (getCampPlayerNumCount() > 0) {
      hash = (37 * hash) + CAMPPLAYERNUM_FIELD_NUMBER;
      hash = (53 * hash) + getCampPlayerNumList().hashCode();
    }
    if (hasSuppressed()) {
      hash = (37 * hash) + SUPPRESSED_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getSuppressed());
    }
    if (getServerIdArrayCount() > 0) {
      hash = (37 * hash) + SERVERIDARRAY_FIELD_NUMBER;
      hash = (53 * hash) + getServerIdArrayList().hashCode();
    }
    if (hasPlayerInfo()) {
      hash = (37 * hash) + PLAYERINFO_FIELD_NUMBER;
      hash = (53 * hash) + getPlayerInfo().hashCode();
    }
    if (getGodRankDataCount() > 0) {
      hash = (37 * hash) + GODRANKDATA_FIELD_NUMBER;
      hash = (53 * hash) + getGodRankDataList().hashCode();
    }
    if (getDemonrankDataCount() > 0) {
      hash = (37 * hash) + DEMONRANKDATA_FIELD_NUMBER;
      hash = (53 * hash) + getDemonrankDataList().hashCode();
    }
    if (hasSession()) {
      hash = (37 * hash) + SESSION_FIELD_NUMBER;
      hash = (53 * hash) + getSession();
    }
    if (hasInRoomBattle()) {
      hash = (37 * hash) + INROOMBATTLE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getInRoomBattle());
    }
    if (getRealsMatchListCount() > 0) {
      hash = (37 * hash) + REALSMATCHLIST_FIELD_NUMBER;
      hash = (53 * hash) + getRealsMatchListList().hashCode();
    }
    if (hasWinCamp()) {
      hash = (37 * hash) + WINCAMP_FIELD_NUMBER;
      hash = (53 * hash) + getWinCamp();
    }
    if (hasCanReceiveCampReward()) {
      hash = (37 * hash) + CANRECEIVECAMPREWARD_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getCanReceiveCampReward());
    }
    if (hasMaxJoinRealms()) {
      hash = (37 * hash) + MAXJOINREALMS_FIELD_NUMBER;
      hash = (53 * hash) + getMaxJoinRealms();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.GodDemonBattleBaseInfoResp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GodDemonBattleBaseInfoResp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GodDemonBattleBaseInfoResp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GodDemonBattleBaseInfoResp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GodDemonBattleBaseInfoResp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GodDemonBattleBaseInfoResp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GodDemonBattleBaseInfoResp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.GodDemonBattleBaseInfoResp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.GodDemonBattleBaseInfoResp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.GodDemonBattleBaseInfoResp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.GodDemonBattleBaseInfoResp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.GodDemonBattleBaseInfoResp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.GodDemonBattleBaseInfoResp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.GodDemonBattleBaseInfoResp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.GodDemonBattleBaseInfoResp)
      xddq.pb.GodDemonBattleBaseInfoRespOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GodDemonBattleBaseInfoResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GodDemonBattleBaseInfoResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.GodDemonBattleBaseInfoResp.class, xddq.pb.GodDemonBattleBaseInfoResp.Builder.class);
    }

    // Construct using xddq.pb.GodDemonBattleBaseInfoResp.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetCampPlayerNumFieldBuilder();
        internalGetPlayerInfoFieldBuilder();
        internalGetGodRankDataFieldBuilder();
        internalGetDemonrankDataFieldBuilder();
        internalGetRealsMatchListFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      if (campPlayerNumBuilder_ == null) {
        campPlayerNum_ = java.util.Collections.emptyList();
      } else {
        campPlayerNum_ = null;
        campPlayerNumBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000002);
      suppressed_ = false;
      serverIdArray_ = emptyLongList();
      playerInfo_ = null;
      if (playerInfoBuilder_ != null) {
        playerInfoBuilder_.dispose();
        playerInfoBuilder_ = null;
      }
      if (godRankDataBuilder_ == null) {
        godRankData_ = java.util.Collections.emptyList();
      } else {
        godRankData_ = null;
        godRankDataBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000020);
      if (demonrankDataBuilder_ == null) {
        demonrankData_ = java.util.Collections.emptyList();
      } else {
        demonrankData_ = null;
        demonrankDataBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000040);
      session_ = 0;
      inRoomBattle_ = false;
      if (realsMatchListBuilder_ == null) {
        realsMatchList_ = java.util.Collections.emptyList();
      } else {
        realsMatchList_ = null;
        realsMatchListBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000200);
      winCamp_ = 0;
      canReceiveCampReward_ = false;
      maxJoinRealms_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GodDemonBattleBaseInfoResp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.GodDemonBattleBaseInfoResp getDefaultInstanceForType() {
      return xddq.pb.GodDemonBattleBaseInfoResp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.GodDemonBattleBaseInfoResp build() {
      xddq.pb.GodDemonBattleBaseInfoResp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.GodDemonBattleBaseInfoResp buildPartial() {
      xddq.pb.GodDemonBattleBaseInfoResp result = new xddq.pb.GodDemonBattleBaseInfoResp(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.GodDemonBattleBaseInfoResp result) {
      if (campPlayerNumBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          campPlayerNum_ = java.util.Collections.unmodifiableList(campPlayerNum_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.campPlayerNum_ = campPlayerNum_;
      } else {
        result.campPlayerNum_ = campPlayerNumBuilder_.build();
      }
      if (godRankDataBuilder_ == null) {
        if (((bitField0_ & 0x00000020) != 0)) {
          godRankData_ = java.util.Collections.unmodifiableList(godRankData_);
          bitField0_ = (bitField0_ & ~0x00000020);
        }
        result.godRankData_ = godRankData_;
      } else {
        result.godRankData_ = godRankDataBuilder_.build();
      }
      if (demonrankDataBuilder_ == null) {
        if (((bitField0_ & 0x00000040) != 0)) {
          demonrankData_ = java.util.Collections.unmodifiableList(demonrankData_);
          bitField0_ = (bitField0_ & ~0x00000040);
        }
        result.demonrankData_ = demonrankData_;
      } else {
        result.demonrankData_ = demonrankDataBuilder_.build();
      }
      if (realsMatchListBuilder_ == null) {
        if (((bitField0_ & 0x00000200) != 0)) {
          realsMatchList_ = java.util.Collections.unmodifiableList(realsMatchList_);
          bitField0_ = (bitField0_ & ~0x00000200);
        }
        result.realsMatchList_ = realsMatchList_;
      } else {
        result.realsMatchList_ = realsMatchListBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.GodDemonBattleBaseInfoResp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.suppressed_ = suppressed_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        serverIdArray_.makeImmutable();
        result.serverIdArray_ = serverIdArray_;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.playerInfo_ = playerInfoBuilder_ == null
            ? playerInfo_
            : playerInfoBuilder_.build();
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000080) != 0)) {
        result.session_ = session_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000100) != 0)) {
        result.inRoomBattle_ = inRoomBattle_;
        to_bitField0_ |= 0x00000010;
      }
      if (((from_bitField0_ & 0x00000400) != 0)) {
        result.winCamp_ = winCamp_;
        to_bitField0_ |= 0x00000020;
      }
      if (((from_bitField0_ & 0x00000800) != 0)) {
        result.canReceiveCampReward_ = canReceiveCampReward_;
        to_bitField0_ |= 0x00000040;
      }
      if (((from_bitField0_ & 0x00001000) != 0)) {
        result.maxJoinRealms_ = maxJoinRealms_;
        to_bitField0_ |= 0x00000080;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.GodDemonBattleBaseInfoResp) {
        return mergeFrom((xddq.pb.GodDemonBattleBaseInfoResp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.GodDemonBattleBaseInfoResp other) {
      if (other == xddq.pb.GodDemonBattleBaseInfoResp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (campPlayerNumBuilder_ == null) {
        if (!other.campPlayerNum_.isEmpty()) {
          if (campPlayerNum_.isEmpty()) {
            campPlayerNum_ = other.campPlayerNum_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureCampPlayerNumIsMutable();
            campPlayerNum_.addAll(other.campPlayerNum_);
          }
          onChanged();
        }
      } else {
        if (!other.campPlayerNum_.isEmpty()) {
          if (campPlayerNumBuilder_.isEmpty()) {
            campPlayerNumBuilder_.dispose();
            campPlayerNumBuilder_ = null;
            campPlayerNum_ = other.campPlayerNum_;
            bitField0_ = (bitField0_ & ~0x00000002);
            campPlayerNumBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetCampPlayerNumFieldBuilder() : null;
          } else {
            campPlayerNumBuilder_.addAllMessages(other.campPlayerNum_);
          }
        }
      }
      if (other.hasSuppressed()) {
        setSuppressed(other.getSuppressed());
      }
      if (!other.serverIdArray_.isEmpty()) {
        if (serverIdArray_.isEmpty()) {
          serverIdArray_ = other.serverIdArray_;
          serverIdArray_.makeImmutable();
          bitField0_ |= 0x00000008;
        } else {
          ensureServerIdArrayIsMutable();
          serverIdArray_.addAll(other.serverIdArray_);
        }
        onChanged();
      }
      if (other.hasPlayerInfo()) {
        mergePlayerInfo(other.getPlayerInfo());
      }
      if (godRankDataBuilder_ == null) {
        if (!other.godRankData_.isEmpty()) {
          if (godRankData_.isEmpty()) {
            godRankData_ = other.godRankData_;
            bitField0_ = (bitField0_ & ~0x00000020);
          } else {
            ensureGodRankDataIsMutable();
            godRankData_.addAll(other.godRankData_);
          }
          onChanged();
        }
      } else {
        if (!other.godRankData_.isEmpty()) {
          if (godRankDataBuilder_.isEmpty()) {
            godRankDataBuilder_.dispose();
            godRankDataBuilder_ = null;
            godRankData_ = other.godRankData_;
            bitField0_ = (bitField0_ & ~0x00000020);
            godRankDataBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetGodRankDataFieldBuilder() : null;
          } else {
            godRankDataBuilder_.addAllMessages(other.godRankData_);
          }
        }
      }
      if (demonrankDataBuilder_ == null) {
        if (!other.demonrankData_.isEmpty()) {
          if (demonrankData_.isEmpty()) {
            demonrankData_ = other.demonrankData_;
            bitField0_ = (bitField0_ & ~0x00000040);
          } else {
            ensureDemonrankDataIsMutable();
            demonrankData_.addAll(other.demonrankData_);
          }
          onChanged();
        }
      } else {
        if (!other.demonrankData_.isEmpty()) {
          if (demonrankDataBuilder_.isEmpty()) {
            demonrankDataBuilder_.dispose();
            demonrankDataBuilder_ = null;
            demonrankData_ = other.demonrankData_;
            bitField0_ = (bitField0_ & ~0x00000040);
            demonrankDataBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetDemonrankDataFieldBuilder() : null;
          } else {
            demonrankDataBuilder_.addAllMessages(other.demonrankData_);
          }
        }
      }
      if (other.hasSession()) {
        setSession(other.getSession());
      }
      if (other.hasInRoomBattle()) {
        setInRoomBattle(other.getInRoomBattle());
      }
      if (realsMatchListBuilder_ == null) {
        if (!other.realsMatchList_.isEmpty()) {
          if (realsMatchList_.isEmpty()) {
            realsMatchList_ = other.realsMatchList_;
            bitField0_ = (bitField0_ & ~0x00000200);
          } else {
            ensureRealsMatchListIsMutable();
            realsMatchList_.addAll(other.realsMatchList_);
          }
          onChanged();
        }
      } else {
        if (!other.realsMatchList_.isEmpty()) {
          if (realsMatchListBuilder_.isEmpty()) {
            realsMatchListBuilder_.dispose();
            realsMatchListBuilder_ = null;
            realsMatchList_ = other.realsMatchList_;
            bitField0_ = (bitField0_ & ~0x00000200);
            realsMatchListBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetRealsMatchListFieldBuilder() : null;
          } else {
            realsMatchListBuilder_.addAllMessages(other.realsMatchList_);
          }
        }
      }
      if (other.hasWinCamp()) {
        setWinCamp(other.getWinCamp());
      }
      if (other.hasCanReceiveCampReward()) {
        setCanReceiveCampReward(other.getCanReceiveCampReward());
      }
      if (other.hasMaxJoinRealms()) {
        setMaxJoinRealms(other.getMaxJoinRealms());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      for (int i = 0; i < getRealsMatchListCount(); i++) {
        if (!getRealsMatchList(i).isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              xddq.pb.GodDemonPlayerCountMsg m =
                  input.readMessage(
                      xddq.pb.GodDemonPlayerCountMsg.parser(),
                      extensionRegistry);
              if (campPlayerNumBuilder_ == null) {
                ensureCampPlayerNumIsMutable();
                campPlayerNum_.add(m);
              } else {
                campPlayerNumBuilder_.addMessage(m);
              }
              break;
            } // case 18
            case 24: {
              suppressed_ = input.readBool();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 32: {
              long v = input.readInt64();
              ensureServerIdArrayIsMutable();
              serverIdArray_.addLong(v);
              break;
            } // case 32
            case 34: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              ensureServerIdArrayIsMutable();
              while (input.getBytesUntilLimit() > 0) {
                serverIdArray_.addLong(input.readInt64());
              }
              input.popLimit(limit);
              break;
            } // case 34
            case 42: {
              input.readMessage(
                  internalGetPlayerInfoFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000010;
              break;
            } // case 42
            case 50: {
              xddq.pb.GodDemonRankData m =
                  input.readMessage(
                      xddq.pb.GodDemonRankData.parser(),
                      extensionRegistry);
              if (godRankDataBuilder_ == null) {
                ensureGodRankDataIsMutable();
                godRankData_.add(m);
              } else {
                godRankDataBuilder_.addMessage(m);
              }
              break;
            } // case 50
            case 58: {
              xddq.pb.GodDemonRankData m =
                  input.readMessage(
                      xddq.pb.GodDemonRankData.parser(),
                      extensionRegistry);
              if (demonrankDataBuilder_ == null) {
                ensureDemonrankDataIsMutable();
                demonrankData_.add(m);
              } else {
                demonrankDataBuilder_.addMessage(m);
              }
              break;
            } // case 58
            case 64: {
              session_ = input.readInt32();
              bitField0_ |= 0x00000080;
              break;
            } // case 64
            case 72: {
              inRoomBattle_ = input.readBool();
              bitField0_ |= 0x00000100;
              break;
            } // case 72
            case 82: {
              xddq.pb.GodDemonRealsMatchMsg m =
                  input.readMessage(
                      xddq.pb.GodDemonRealsMatchMsg.parser(),
                      extensionRegistry);
              if (realsMatchListBuilder_ == null) {
                ensureRealsMatchListIsMutable();
                realsMatchList_.add(m);
              } else {
                realsMatchListBuilder_.addMessage(m);
              }
              break;
            } // case 82
            case 88: {
              winCamp_ = input.readInt32();
              bitField0_ |= 0x00000400;
              break;
            } // case 88
            case 96: {
              canReceiveCampReward_ = input.readBool();
              bitField0_ |= 0x00000800;
              break;
            } // case 96
            case 104: {
              maxJoinRealms_ = input.readInt32();
              bitField0_ |= 0x00001000;
              break;
            } // case 104
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.GodDemonPlayerCountMsg> campPlayerNum_ =
      java.util.Collections.emptyList();
    private void ensureCampPlayerNumIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        campPlayerNum_ = new java.util.ArrayList<xddq.pb.GodDemonPlayerCountMsg>(campPlayerNum_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.GodDemonPlayerCountMsg, xddq.pb.GodDemonPlayerCountMsg.Builder, xddq.pb.GodDemonPlayerCountMsgOrBuilder> campPlayerNumBuilder_;

    /**
     * <code>repeated .xddq.pb.GodDemonPlayerCountMsg campPlayerNum = 2;</code>
     */
    public java.util.List<xddq.pb.GodDemonPlayerCountMsg> getCampPlayerNumList() {
      if (campPlayerNumBuilder_ == null) {
        return java.util.Collections.unmodifiableList(campPlayerNum_);
      } else {
        return campPlayerNumBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.GodDemonPlayerCountMsg campPlayerNum = 2;</code>
     */
    public int getCampPlayerNumCount() {
      if (campPlayerNumBuilder_ == null) {
        return campPlayerNum_.size();
      } else {
        return campPlayerNumBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.GodDemonPlayerCountMsg campPlayerNum = 2;</code>
     */
    public xddq.pb.GodDemonPlayerCountMsg getCampPlayerNum(int index) {
      if (campPlayerNumBuilder_ == null) {
        return campPlayerNum_.get(index);
      } else {
        return campPlayerNumBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.GodDemonPlayerCountMsg campPlayerNum = 2;</code>
     */
    public Builder setCampPlayerNum(
        int index, xddq.pb.GodDemonPlayerCountMsg value) {
      if (campPlayerNumBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureCampPlayerNumIsMutable();
        campPlayerNum_.set(index, value);
        onChanged();
      } else {
        campPlayerNumBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodDemonPlayerCountMsg campPlayerNum = 2;</code>
     */
    public Builder setCampPlayerNum(
        int index, xddq.pb.GodDemonPlayerCountMsg.Builder builderForValue) {
      if (campPlayerNumBuilder_ == null) {
        ensureCampPlayerNumIsMutable();
        campPlayerNum_.set(index, builderForValue.build());
        onChanged();
      } else {
        campPlayerNumBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodDemonPlayerCountMsg campPlayerNum = 2;</code>
     */
    public Builder addCampPlayerNum(xddq.pb.GodDemonPlayerCountMsg value) {
      if (campPlayerNumBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureCampPlayerNumIsMutable();
        campPlayerNum_.add(value);
        onChanged();
      } else {
        campPlayerNumBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodDemonPlayerCountMsg campPlayerNum = 2;</code>
     */
    public Builder addCampPlayerNum(
        int index, xddq.pb.GodDemonPlayerCountMsg value) {
      if (campPlayerNumBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureCampPlayerNumIsMutable();
        campPlayerNum_.add(index, value);
        onChanged();
      } else {
        campPlayerNumBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodDemonPlayerCountMsg campPlayerNum = 2;</code>
     */
    public Builder addCampPlayerNum(
        xddq.pb.GodDemonPlayerCountMsg.Builder builderForValue) {
      if (campPlayerNumBuilder_ == null) {
        ensureCampPlayerNumIsMutable();
        campPlayerNum_.add(builderForValue.build());
        onChanged();
      } else {
        campPlayerNumBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodDemonPlayerCountMsg campPlayerNum = 2;</code>
     */
    public Builder addCampPlayerNum(
        int index, xddq.pb.GodDemonPlayerCountMsg.Builder builderForValue) {
      if (campPlayerNumBuilder_ == null) {
        ensureCampPlayerNumIsMutable();
        campPlayerNum_.add(index, builderForValue.build());
        onChanged();
      } else {
        campPlayerNumBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodDemonPlayerCountMsg campPlayerNum = 2;</code>
     */
    public Builder addAllCampPlayerNum(
        java.lang.Iterable<? extends xddq.pb.GodDemonPlayerCountMsg> values) {
      if (campPlayerNumBuilder_ == null) {
        ensureCampPlayerNumIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, campPlayerNum_);
        onChanged();
      } else {
        campPlayerNumBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodDemonPlayerCountMsg campPlayerNum = 2;</code>
     */
    public Builder clearCampPlayerNum() {
      if (campPlayerNumBuilder_ == null) {
        campPlayerNum_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        campPlayerNumBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodDemonPlayerCountMsg campPlayerNum = 2;</code>
     */
    public Builder removeCampPlayerNum(int index) {
      if (campPlayerNumBuilder_ == null) {
        ensureCampPlayerNumIsMutable();
        campPlayerNum_.remove(index);
        onChanged();
      } else {
        campPlayerNumBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodDemonPlayerCountMsg campPlayerNum = 2;</code>
     */
    public xddq.pb.GodDemonPlayerCountMsg.Builder getCampPlayerNumBuilder(
        int index) {
      return internalGetCampPlayerNumFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.GodDemonPlayerCountMsg campPlayerNum = 2;</code>
     */
    public xddq.pb.GodDemonPlayerCountMsgOrBuilder getCampPlayerNumOrBuilder(
        int index) {
      if (campPlayerNumBuilder_ == null) {
        return campPlayerNum_.get(index);  } else {
        return campPlayerNumBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.GodDemonPlayerCountMsg campPlayerNum = 2;</code>
     */
    public java.util.List<? extends xddq.pb.GodDemonPlayerCountMsgOrBuilder> 
         getCampPlayerNumOrBuilderList() {
      if (campPlayerNumBuilder_ != null) {
        return campPlayerNumBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(campPlayerNum_);
      }
    }
    /**
     * <code>repeated .xddq.pb.GodDemonPlayerCountMsg campPlayerNum = 2;</code>
     */
    public xddq.pb.GodDemonPlayerCountMsg.Builder addCampPlayerNumBuilder() {
      return internalGetCampPlayerNumFieldBuilder().addBuilder(
          xddq.pb.GodDemonPlayerCountMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.GodDemonPlayerCountMsg campPlayerNum = 2;</code>
     */
    public xddq.pb.GodDemonPlayerCountMsg.Builder addCampPlayerNumBuilder(
        int index) {
      return internalGetCampPlayerNumFieldBuilder().addBuilder(
          index, xddq.pb.GodDemonPlayerCountMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.GodDemonPlayerCountMsg campPlayerNum = 2;</code>
     */
    public java.util.List<xddq.pb.GodDemonPlayerCountMsg.Builder> 
         getCampPlayerNumBuilderList() {
      return internalGetCampPlayerNumFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.GodDemonPlayerCountMsg, xddq.pb.GodDemonPlayerCountMsg.Builder, xddq.pb.GodDemonPlayerCountMsgOrBuilder> 
        internalGetCampPlayerNumFieldBuilder() {
      if (campPlayerNumBuilder_ == null) {
        campPlayerNumBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.GodDemonPlayerCountMsg, xddq.pb.GodDemonPlayerCountMsg.Builder, xddq.pb.GodDemonPlayerCountMsgOrBuilder>(
                campPlayerNum_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        campPlayerNum_ = null;
      }
      return campPlayerNumBuilder_;
    }

    private boolean suppressed_ ;
    /**
     * <code>optional bool suppressed = 3;</code>
     * @return Whether the suppressed field is set.
     */
    @java.lang.Override
    public boolean hasSuppressed() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional bool suppressed = 3;</code>
     * @return The suppressed.
     */
    @java.lang.Override
    public boolean getSuppressed() {
      return suppressed_;
    }
    /**
     * <code>optional bool suppressed = 3;</code>
     * @param value The suppressed to set.
     * @return This builder for chaining.
     */
    public Builder setSuppressed(boolean value) {

      suppressed_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional bool suppressed = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearSuppressed() {
      bitField0_ = (bitField0_ & ~0x00000004);
      suppressed_ = false;
      onChanged();
      return this;
    }

    private com.google.protobuf.Internal.LongList serverIdArray_ = emptyLongList();
    private void ensureServerIdArrayIsMutable() {
      if (!serverIdArray_.isModifiable()) {
        serverIdArray_ = makeMutableCopy(serverIdArray_);
      }
      bitField0_ |= 0x00000008;
    }
    /**
     * <code>repeated int64 serverIdArray = 4;</code>
     * @return A list containing the serverIdArray.
     */
    public java.util.List<java.lang.Long>
        getServerIdArrayList() {
      serverIdArray_.makeImmutable();
      return serverIdArray_;
    }
    /**
     * <code>repeated int64 serverIdArray = 4;</code>
     * @return The count of serverIdArray.
     */
    public int getServerIdArrayCount() {
      return serverIdArray_.size();
    }
    /**
     * <code>repeated int64 serverIdArray = 4;</code>
     * @param index The index of the element to return.
     * @return The serverIdArray at the given index.
     */
    public long getServerIdArray(int index) {
      return serverIdArray_.getLong(index);
    }
    /**
     * <code>repeated int64 serverIdArray = 4;</code>
     * @param index The index to set the value at.
     * @param value The serverIdArray to set.
     * @return This builder for chaining.
     */
    public Builder setServerIdArray(
        int index, long value) {

      ensureServerIdArrayIsMutable();
      serverIdArray_.setLong(index, value);
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int64 serverIdArray = 4;</code>
     * @param value The serverIdArray to add.
     * @return This builder for chaining.
     */
    public Builder addServerIdArray(long value) {

      ensureServerIdArrayIsMutable();
      serverIdArray_.addLong(value);
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int64 serverIdArray = 4;</code>
     * @param values The serverIdArray to add.
     * @return This builder for chaining.
     */
    public Builder addAllServerIdArray(
        java.lang.Iterable<? extends java.lang.Long> values) {
      ensureServerIdArrayIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, serverIdArray_);
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int64 serverIdArray = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearServerIdArray() {
      serverIdArray_ = emptyLongList();
      bitField0_ = (bitField0_ & ~0x00000008);
      onChanged();
      return this;
    }

    private xddq.pb.GodDemonBattlePlayerInfo playerInfo_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.GodDemonBattlePlayerInfo, xddq.pb.GodDemonBattlePlayerInfo.Builder, xddq.pb.GodDemonBattlePlayerInfoOrBuilder> playerInfoBuilder_;
    /**
     * <code>optional .xddq.pb.GodDemonBattlePlayerInfo playerInfo = 5;</code>
     * @return Whether the playerInfo field is set.
     */
    public boolean hasPlayerInfo() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional .xddq.pb.GodDemonBattlePlayerInfo playerInfo = 5;</code>
     * @return The playerInfo.
     */
    public xddq.pb.GodDemonBattlePlayerInfo getPlayerInfo() {
      if (playerInfoBuilder_ == null) {
        return playerInfo_ == null ? xddq.pb.GodDemonBattlePlayerInfo.getDefaultInstance() : playerInfo_;
      } else {
        return playerInfoBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.GodDemonBattlePlayerInfo playerInfo = 5;</code>
     */
    public Builder setPlayerInfo(xddq.pb.GodDemonBattlePlayerInfo value) {
      if (playerInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        playerInfo_ = value;
      } else {
        playerInfoBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.GodDemonBattlePlayerInfo playerInfo = 5;</code>
     */
    public Builder setPlayerInfo(
        xddq.pb.GodDemonBattlePlayerInfo.Builder builderForValue) {
      if (playerInfoBuilder_ == null) {
        playerInfo_ = builderForValue.build();
      } else {
        playerInfoBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.GodDemonBattlePlayerInfo playerInfo = 5;</code>
     */
    public Builder mergePlayerInfo(xddq.pb.GodDemonBattlePlayerInfo value) {
      if (playerInfoBuilder_ == null) {
        if (((bitField0_ & 0x00000010) != 0) &&
          playerInfo_ != null &&
          playerInfo_ != xddq.pb.GodDemonBattlePlayerInfo.getDefaultInstance()) {
          getPlayerInfoBuilder().mergeFrom(value);
        } else {
          playerInfo_ = value;
        }
      } else {
        playerInfoBuilder_.mergeFrom(value);
      }
      if (playerInfo_ != null) {
        bitField0_ |= 0x00000010;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.GodDemonBattlePlayerInfo playerInfo = 5;</code>
     */
    public Builder clearPlayerInfo() {
      bitField0_ = (bitField0_ & ~0x00000010);
      playerInfo_ = null;
      if (playerInfoBuilder_ != null) {
        playerInfoBuilder_.dispose();
        playerInfoBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.GodDemonBattlePlayerInfo playerInfo = 5;</code>
     */
    public xddq.pb.GodDemonBattlePlayerInfo.Builder getPlayerInfoBuilder() {
      bitField0_ |= 0x00000010;
      onChanged();
      return internalGetPlayerInfoFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.GodDemonBattlePlayerInfo playerInfo = 5;</code>
     */
    public xddq.pb.GodDemonBattlePlayerInfoOrBuilder getPlayerInfoOrBuilder() {
      if (playerInfoBuilder_ != null) {
        return playerInfoBuilder_.getMessageOrBuilder();
      } else {
        return playerInfo_ == null ?
            xddq.pb.GodDemonBattlePlayerInfo.getDefaultInstance() : playerInfo_;
      }
    }
    /**
     * <code>optional .xddq.pb.GodDemonBattlePlayerInfo playerInfo = 5;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.GodDemonBattlePlayerInfo, xddq.pb.GodDemonBattlePlayerInfo.Builder, xddq.pb.GodDemonBattlePlayerInfoOrBuilder> 
        internalGetPlayerInfoFieldBuilder() {
      if (playerInfoBuilder_ == null) {
        playerInfoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.GodDemonBattlePlayerInfo, xddq.pb.GodDemonBattlePlayerInfo.Builder, xddq.pb.GodDemonBattlePlayerInfoOrBuilder>(
                getPlayerInfo(),
                getParentForChildren(),
                isClean());
        playerInfo_ = null;
      }
      return playerInfoBuilder_;
    }

    private java.util.List<xddq.pb.GodDemonRankData> godRankData_ =
      java.util.Collections.emptyList();
    private void ensureGodRankDataIsMutable() {
      if (!((bitField0_ & 0x00000020) != 0)) {
        godRankData_ = new java.util.ArrayList<xddq.pb.GodDemonRankData>(godRankData_);
        bitField0_ |= 0x00000020;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.GodDemonRankData, xddq.pb.GodDemonRankData.Builder, xddq.pb.GodDemonRankDataOrBuilder> godRankDataBuilder_;

    /**
     * <code>repeated .xddq.pb.GodDemonRankData godRankData = 6;</code>
     */
    public java.util.List<xddq.pb.GodDemonRankData> getGodRankDataList() {
      if (godRankDataBuilder_ == null) {
        return java.util.Collections.unmodifiableList(godRankData_);
      } else {
        return godRankDataBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRankData godRankData = 6;</code>
     */
    public int getGodRankDataCount() {
      if (godRankDataBuilder_ == null) {
        return godRankData_.size();
      } else {
        return godRankDataBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRankData godRankData = 6;</code>
     */
    public xddq.pb.GodDemonRankData getGodRankData(int index) {
      if (godRankDataBuilder_ == null) {
        return godRankData_.get(index);
      } else {
        return godRankDataBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRankData godRankData = 6;</code>
     */
    public Builder setGodRankData(
        int index, xddq.pb.GodDemonRankData value) {
      if (godRankDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureGodRankDataIsMutable();
        godRankData_.set(index, value);
        onChanged();
      } else {
        godRankDataBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRankData godRankData = 6;</code>
     */
    public Builder setGodRankData(
        int index, xddq.pb.GodDemonRankData.Builder builderForValue) {
      if (godRankDataBuilder_ == null) {
        ensureGodRankDataIsMutable();
        godRankData_.set(index, builderForValue.build());
        onChanged();
      } else {
        godRankDataBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRankData godRankData = 6;</code>
     */
    public Builder addGodRankData(xddq.pb.GodDemonRankData value) {
      if (godRankDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureGodRankDataIsMutable();
        godRankData_.add(value);
        onChanged();
      } else {
        godRankDataBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRankData godRankData = 6;</code>
     */
    public Builder addGodRankData(
        int index, xddq.pb.GodDemonRankData value) {
      if (godRankDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureGodRankDataIsMutable();
        godRankData_.add(index, value);
        onChanged();
      } else {
        godRankDataBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRankData godRankData = 6;</code>
     */
    public Builder addGodRankData(
        xddq.pb.GodDemonRankData.Builder builderForValue) {
      if (godRankDataBuilder_ == null) {
        ensureGodRankDataIsMutable();
        godRankData_.add(builderForValue.build());
        onChanged();
      } else {
        godRankDataBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRankData godRankData = 6;</code>
     */
    public Builder addGodRankData(
        int index, xddq.pb.GodDemonRankData.Builder builderForValue) {
      if (godRankDataBuilder_ == null) {
        ensureGodRankDataIsMutable();
        godRankData_.add(index, builderForValue.build());
        onChanged();
      } else {
        godRankDataBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRankData godRankData = 6;</code>
     */
    public Builder addAllGodRankData(
        java.lang.Iterable<? extends xddq.pb.GodDemonRankData> values) {
      if (godRankDataBuilder_ == null) {
        ensureGodRankDataIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, godRankData_);
        onChanged();
      } else {
        godRankDataBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRankData godRankData = 6;</code>
     */
    public Builder clearGodRankData() {
      if (godRankDataBuilder_ == null) {
        godRankData_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000020);
        onChanged();
      } else {
        godRankDataBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRankData godRankData = 6;</code>
     */
    public Builder removeGodRankData(int index) {
      if (godRankDataBuilder_ == null) {
        ensureGodRankDataIsMutable();
        godRankData_.remove(index);
        onChanged();
      } else {
        godRankDataBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRankData godRankData = 6;</code>
     */
    public xddq.pb.GodDemonRankData.Builder getGodRankDataBuilder(
        int index) {
      return internalGetGodRankDataFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRankData godRankData = 6;</code>
     */
    public xddq.pb.GodDemonRankDataOrBuilder getGodRankDataOrBuilder(
        int index) {
      if (godRankDataBuilder_ == null) {
        return godRankData_.get(index);  } else {
        return godRankDataBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRankData godRankData = 6;</code>
     */
    public java.util.List<? extends xddq.pb.GodDemonRankDataOrBuilder> 
         getGodRankDataOrBuilderList() {
      if (godRankDataBuilder_ != null) {
        return godRankDataBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(godRankData_);
      }
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRankData godRankData = 6;</code>
     */
    public xddq.pb.GodDemonRankData.Builder addGodRankDataBuilder() {
      return internalGetGodRankDataFieldBuilder().addBuilder(
          xddq.pb.GodDemonRankData.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRankData godRankData = 6;</code>
     */
    public xddq.pb.GodDemonRankData.Builder addGodRankDataBuilder(
        int index) {
      return internalGetGodRankDataFieldBuilder().addBuilder(
          index, xddq.pb.GodDemonRankData.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRankData godRankData = 6;</code>
     */
    public java.util.List<xddq.pb.GodDemonRankData.Builder> 
         getGodRankDataBuilderList() {
      return internalGetGodRankDataFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.GodDemonRankData, xddq.pb.GodDemonRankData.Builder, xddq.pb.GodDemonRankDataOrBuilder> 
        internalGetGodRankDataFieldBuilder() {
      if (godRankDataBuilder_ == null) {
        godRankDataBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.GodDemonRankData, xddq.pb.GodDemonRankData.Builder, xddq.pb.GodDemonRankDataOrBuilder>(
                godRankData_,
                ((bitField0_ & 0x00000020) != 0),
                getParentForChildren(),
                isClean());
        godRankData_ = null;
      }
      return godRankDataBuilder_;
    }

    private java.util.List<xddq.pb.GodDemonRankData> demonrankData_ =
      java.util.Collections.emptyList();
    private void ensureDemonrankDataIsMutable() {
      if (!((bitField0_ & 0x00000040) != 0)) {
        demonrankData_ = new java.util.ArrayList<xddq.pb.GodDemonRankData>(demonrankData_);
        bitField0_ |= 0x00000040;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.GodDemonRankData, xddq.pb.GodDemonRankData.Builder, xddq.pb.GodDemonRankDataOrBuilder> demonrankDataBuilder_;

    /**
     * <code>repeated .xddq.pb.GodDemonRankData DemonrankData = 7;</code>
     */
    public java.util.List<xddq.pb.GodDemonRankData> getDemonrankDataList() {
      if (demonrankDataBuilder_ == null) {
        return java.util.Collections.unmodifiableList(demonrankData_);
      } else {
        return demonrankDataBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRankData DemonrankData = 7;</code>
     */
    public int getDemonrankDataCount() {
      if (demonrankDataBuilder_ == null) {
        return demonrankData_.size();
      } else {
        return demonrankDataBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRankData DemonrankData = 7;</code>
     */
    public xddq.pb.GodDemonRankData getDemonrankData(int index) {
      if (demonrankDataBuilder_ == null) {
        return demonrankData_.get(index);
      } else {
        return demonrankDataBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRankData DemonrankData = 7;</code>
     */
    public Builder setDemonrankData(
        int index, xddq.pb.GodDemonRankData value) {
      if (demonrankDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDemonrankDataIsMutable();
        demonrankData_.set(index, value);
        onChanged();
      } else {
        demonrankDataBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRankData DemonrankData = 7;</code>
     */
    public Builder setDemonrankData(
        int index, xddq.pb.GodDemonRankData.Builder builderForValue) {
      if (demonrankDataBuilder_ == null) {
        ensureDemonrankDataIsMutable();
        demonrankData_.set(index, builderForValue.build());
        onChanged();
      } else {
        demonrankDataBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRankData DemonrankData = 7;</code>
     */
    public Builder addDemonrankData(xddq.pb.GodDemonRankData value) {
      if (demonrankDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDemonrankDataIsMutable();
        demonrankData_.add(value);
        onChanged();
      } else {
        demonrankDataBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRankData DemonrankData = 7;</code>
     */
    public Builder addDemonrankData(
        int index, xddq.pb.GodDemonRankData value) {
      if (demonrankDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDemonrankDataIsMutable();
        demonrankData_.add(index, value);
        onChanged();
      } else {
        demonrankDataBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRankData DemonrankData = 7;</code>
     */
    public Builder addDemonrankData(
        xddq.pb.GodDemonRankData.Builder builderForValue) {
      if (demonrankDataBuilder_ == null) {
        ensureDemonrankDataIsMutable();
        demonrankData_.add(builderForValue.build());
        onChanged();
      } else {
        demonrankDataBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRankData DemonrankData = 7;</code>
     */
    public Builder addDemonrankData(
        int index, xddq.pb.GodDemonRankData.Builder builderForValue) {
      if (demonrankDataBuilder_ == null) {
        ensureDemonrankDataIsMutable();
        demonrankData_.add(index, builderForValue.build());
        onChanged();
      } else {
        demonrankDataBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRankData DemonrankData = 7;</code>
     */
    public Builder addAllDemonrankData(
        java.lang.Iterable<? extends xddq.pb.GodDemonRankData> values) {
      if (demonrankDataBuilder_ == null) {
        ensureDemonrankDataIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, demonrankData_);
        onChanged();
      } else {
        demonrankDataBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRankData DemonrankData = 7;</code>
     */
    public Builder clearDemonrankData() {
      if (demonrankDataBuilder_ == null) {
        demonrankData_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000040);
        onChanged();
      } else {
        demonrankDataBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRankData DemonrankData = 7;</code>
     */
    public Builder removeDemonrankData(int index) {
      if (demonrankDataBuilder_ == null) {
        ensureDemonrankDataIsMutable();
        demonrankData_.remove(index);
        onChanged();
      } else {
        demonrankDataBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRankData DemonrankData = 7;</code>
     */
    public xddq.pb.GodDemonRankData.Builder getDemonrankDataBuilder(
        int index) {
      return internalGetDemonrankDataFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRankData DemonrankData = 7;</code>
     */
    public xddq.pb.GodDemonRankDataOrBuilder getDemonrankDataOrBuilder(
        int index) {
      if (demonrankDataBuilder_ == null) {
        return demonrankData_.get(index);  } else {
        return demonrankDataBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRankData DemonrankData = 7;</code>
     */
    public java.util.List<? extends xddq.pb.GodDemonRankDataOrBuilder> 
         getDemonrankDataOrBuilderList() {
      if (demonrankDataBuilder_ != null) {
        return demonrankDataBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(demonrankData_);
      }
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRankData DemonrankData = 7;</code>
     */
    public xddq.pb.GodDemonRankData.Builder addDemonrankDataBuilder() {
      return internalGetDemonrankDataFieldBuilder().addBuilder(
          xddq.pb.GodDemonRankData.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRankData DemonrankData = 7;</code>
     */
    public xddq.pb.GodDemonRankData.Builder addDemonrankDataBuilder(
        int index) {
      return internalGetDemonrankDataFieldBuilder().addBuilder(
          index, xddq.pb.GodDemonRankData.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRankData DemonrankData = 7;</code>
     */
    public java.util.List<xddq.pb.GodDemonRankData.Builder> 
         getDemonrankDataBuilderList() {
      return internalGetDemonrankDataFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.GodDemonRankData, xddq.pb.GodDemonRankData.Builder, xddq.pb.GodDemonRankDataOrBuilder> 
        internalGetDemonrankDataFieldBuilder() {
      if (demonrankDataBuilder_ == null) {
        demonrankDataBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.GodDemonRankData, xddq.pb.GodDemonRankData.Builder, xddq.pb.GodDemonRankDataOrBuilder>(
                demonrankData_,
                ((bitField0_ & 0x00000040) != 0),
                getParentForChildren(),
                isClean());
        demonrankData_ = null;
      }
      return demonrankDataBuilder_;
    }

    private int session_ ;
    /**
     * <code>optional int32 session = 8;</code>
     * @return Whether the session field is set.
     */
    @java.lang.Override
    public boolean hasSession() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <code>optional int32 session = 8;</code>
     * @return The session.
     */
    @java.lang.Override
    public int getSession() {
      return session_;
    }
    /**
     * <code>optional int32 session = 8;</code>
     * @param value The session to set.
     * @return This builder for chaining.
     */
    public Builder setSession(int value) {

      session_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 session = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearSession() {
      bitField0_ = (bitField0_ & ~0x00000080);
      session_ = 0;
      onChanged();
      return this;
    }

    private boolean inRoomBattle_ ;
    /**
     * <code>optional bool inRoomBattle = 9;</code>
     * @return Whether the inRoomBattle field is set.
     */
    @java.lang.Override
    public boolean hasInRoomBattle() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <code>optional bool inRoomBattle = 9;</code>
     * @return The inRoomBattle.
     */
    @java.lang.Override
    public boolean getInRoomBattle() {
      return inRoomBattle_;
    }
    /**
     * <code>optional bool inRoomBattle = 9;</code>
     * @param value The inRoomBattle to set.
     * @return This builder for chaining.
     */
    public Builder setInRoomBattle(boolean value) {

      inRoomBattle_ = value;
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }
    /**
     * <code>optional bool inRoomBattle = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearInRoomBattle() {
      bitField0_ = (bitField0_ & ~0x00000100);
      inRoomBattle_ = false;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.GodDemonRealsMatchMsg> realsMatchList_ =
      java.util.Collections.emptyList();
    private void ensureRealsMatchListIsMutable() {
      if (!((bitField0_ & 0x00000200) != 0)) {
        realsMatchList_ = new java.util.ArrayList<xddq.pb.GodDemonRealsMatchMsg>(realsMatchList_);
        bitField0_ |= 0x00000200;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.GodDemonRealsMatchMsg, xddq.pb.GodDemonRealsMatchMsg.Builder, xddq.pb.GodDemonRealsMatchMsgOrBuilder> realsMatchListBuilder_;

    /**
     * <code>repeated .xddq.pb.GodDemonRealsMatchMsg realsMatchList = 10;</code>
     */
    public java.util.List<xddq.pb.GodDemonRealsMatchMsg> getRealsMatchListList() {
      if (realsMatchListBuilder_ == null) {
        return java.util.Collections.unmodifiableList(realsMatchList_);
      } else {
        return realsMatchListBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRealsMatchMsg realsMatchList = 10;</code>
     */
    public int getRealsMatchListCount() {
      if (realsMatchListBuilder_ == null) {
        return realsMatchList_.size();
      } else {
        return realsMatchListBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRealsMatchMsg realsMatchList = 10;</code>
     */
    public xddq.pb.GodDemonRealsMatchMsg getRealsMatchList(int index) {
      if (realsMatchListBuilder_ == null) {
        return realsMatchList_.get(index);
      } else {
        return realsMatchListBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRealsMatchMsg realsMatchList = 10;</code>
     */
    public Builder setRealsMatchList(
        int index, xddq.pb.GodDemonRealsMatchMsg value) {
      if (realsMatchListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRealsMatchListIsMutable();
        realsMatchList_.set(index, value);
        onChanged();
      } else {
        realsMatchListBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRealsMatchMsg realsMatchList = 10;</code>
     */
    public Builder setRealsMatchList(
        int index, xddq.pb.GodDemonRealsMatchMsg.Builder builderForValue) {
      if (realsMatchListBuilder_ == null) {
        ensureRealsMatchListIsMutable();
        realsMatchList_.set(index, builderForValue.build());
        onChanged();
      } else {
        realsMatchListBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRealsMatchMsg realsMatchList = 10;</code>
     */
    public Builder addRealsMatchList(xddq.pb.GodDemonRealsMatchMsg value) {
      if (realsMatchListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRealsMatchListIsMutable();
        realsMatchList_.add(value);
        onChanged();
      } else {
        realsMatchListBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRealsMatchMsg realsMatchList = 10;</code>
     */
    public Builder addRealsMatchList(
        int index, xddq.pb.GodDemonRealsMatchMsg value) {
      if (realsMatchListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRealsMatchListIsMutable();
        realsMatchList_.add(index, value);
        onChanged();
      } else {
        realsMatchListBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRealsMatchMsg realsMatchList = 10;</code>
     */
    public Builder addRealsMatchList(
        xddq.pb.GodDemonRealsMatchMsg.Builder builderForValue) {
      if (realsMatchListBuilder_ == null) {
        ensureRealsMatchListIsMutable();
        realsMatchList_.add(builderForValue.build());
        onChanged();
      } else {
        realsMatchListBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRealsMatchMsg realsMatchList = 10;</code>
     */
    public Builder addRealsMatchList(
        int index, xddq.pb.GodDemonRealsMatchMsg.Builder builderForValue) {
      if (realsMatchListBuilder_ == null) {
        ensureRealsMatchListIsMutable();
        realsMatchList_.add(index, builderForValue.build());
        onChanged();
      } else {
        realsMatchListBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRealsMatchMsg realsMatchList = 10;</code>
     */
    public Builder addAllRealsMatchList(
        java.lang.Iterable<? extends xddq.pb.GodDemonRealsMatchMsg> values) {
      if (realsMatchListBuilder_ == null) {
        ensureRealsMatchListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, realsMatchList_);
        onChanged();
      } else {
        realsMatchListBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRealsMatchMsg realsMatchList = 10;</code>
     */
    public Builder clearRealsMatchList() {
      if (realsMatchListBuilder_ == null) {
        realsMatchList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000200);
        onChanged();
      } else {
        realsMatchListBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRealsMatchMsg realsMatchList = 10;</code>
     */
    public Builder removeRealsMatchList(int index) {
      if (realsMatchListBuilder_ == null) {
        ensureRealsMatchListIsMutable();
        realsMatchList_.remove(index);
        onChanged();
      } else {
        realsMatchListBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRealsMatchMsg realsMatchList = 10;</code>
     */
    public xddq.pb.GodDemonRealsMatchMsg.Builder getRealsMatchListBuilder(
        int index) {
      return internalGetRealsMatchListFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRealsMatchMsg realsMatchList = 10;</code>
     */
    public xddq.pb.GodDemonRealsMatchMsgOrBuilder getRealsMatchListOrBuilder(
        int index) {
      if (realsMatchListBuilder_ == null) {
        return realsMatchList_.get(index);  } else {
        return realsMatchListBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRealsMatchMsg realsMatchList = 10;</code>
     */
    public java.util.List<? extends xddq.pb.GodDemonRealsMatchMsgOrBuilder> 
         getRealsMatchListOrBuilderList() {
      if (realsMatchListBuilder_ != null) {
        return realsMatchListBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(realsMatchList_);
      }
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRealsMatchMsg realsMatchList = 10;</code>
     */
    public xddq.pb.GodDemonRealsMatchMsg.Builder addRealsMatchListBuilder() {
      return internalGetRealsMatchListFieldBuilder().addBuilder(
          xddq.pb.GodDemonRealsMatchMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRealsMatchMsg realsMatchList = 10;</code>
     */
    public xddq.pb.GodDemonRealsMatchMsg.Builder addRealsMatchListBuilder(
        int index) {
      return internalGetRealsMatchListFieldBuilder().addBuilder(
          index, xddq.pb.GodDemonRealsMatchMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRealsMatchMsg realsMatchList = 10;</code>
     */
    public java.util.List<xddq.pb.GodDemonRealsMatchMsg.Builder> 
         getRealsMatchListBuilderList() {
      return internalGetRealsMatchListFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.GodDemonRealsMatchMsg, xddq.pb.GodDemonRealsMatchMsg.Builder, xddq.pb.GodDemonRealsMatchMsgOrBuilder> 
        internalGetRealsMatchListFieldBuilder() {
      if (realsMatchListBuilder_ == null) {
        realsMatchListBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.GodDemonRealsMatchMsg, xddq.pb.GodDemonRealsMatchMsg.Builder, xddq.pb.GodDemonRealsMatchMsgOrBuilder>(
                realsMatchList_,
                ((bitField0_ & 0x00000200) != 0),
                getParentForChildren(),
                isClean());
        realsMatchList_ = null;
      }
      return realsMatchListBuilder_;
    }

    private int winCamp_ ;
    /**
     * <code>optional int32 winCamp = 11;</code>
     * @return Whether the winCamp field is set.
     */
    @java.lang.Override
    public boolean hasWinCamp() {
      return ((bitField0_ & 0x00000400) != 0);
    }
    /**
     * <code>optional int32 winCamp = 11;</code>
     * @return The winCamp.
     */
    @java.lang.Override
    public int getWinCamp() {
      return winCamp_;
    }
    /**
     * <code>optional int32 winCamp = 11;</code>
     * @param value The winCamp to set.
     * @return This builder for chaining.
     */
    public Builder setWinCamp(int value) {

      winCamp_ = value;
      bitField0_ |= 0x00000400;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 winCamp = 11;</code>
     * @return This builder for chaining.
     */
    public Builder clearWinCamp() {
      bitField0_ = (bitField0_ & ~0x00000400);
      winCamp_ = 0;
      onChanged();
      return this;
    }

    private boolean canReceiveCampReward_ ;
    /**
     * <code>optional bool canReceiveCampReward = 12;</code>
     * @return Whether the canReceiveCampReward field is set.
     */
    @java.lang.Override
    public boolean hasCanReceiveCampReward() {
      return ((bitField0_ & 0x00000800) != 0);
    }
    /**
     * <code>optional bool canReceiveCampReward = 12;</code>
     * @return The canReceiveCampReward.
     */
    @java.lang.Override
    public boolean getCanReceiveCampReward() {
      return canReceiveCampReward_;
    }
    /**
     * <code>optional bool canReceiveCampReward = 12;</code>
     * @param value The canReceiveCampReward to set.
     * @return This builder for chaining.
     */
    public Builder setCanReceiveCampReward(boolean value) {

      canReceiveCampReward_ = value;
      bitField0_ |= 0x00000800;
      onChanged();
      return this;
    }
    /**
     * <code>optional bool canReceiveCampReward = 12;</code>
     * @return This builder for chaining.
     */
    public Builder clearCanReceiveCampReward() {
      bitField0_ = (bitField0_ & ~0x00000800);
      canReceiveCampReward_ = false;
      onChanged();
      return this;
    }

    private int maxJoinRealms_ ;
    /**
     * <code>optional int32 maxJoinRealms = 13;</code>
     * @return Whether the maxJoinRealms field is set.
     */
    @java.lang.Override
    public boolean hasMaxJoinRealms() {
      return ((bitField0_ & 0x00001000) != 0);
    }
    /**
     * <code>optional int32 maxJoinRealms = 13;</code>
     * @return The maxJoinRealms.
     */
    @java.lang.Override
    public int getMaxJoinRealms() {
      return maxJoinRealms_;
    }
    /**
     * <code>optional int32 maxJoinRealms = 13;</code>
     * @param value The maxJoinRealms to set.
     * @return This builder for chaining.
     */
    public Builder setMaxJoinRealms(int value) {

      maxJoinRealms_ = value;
      bitField0_ |= 0x00001000;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 maxJoinRealms = 13;</code>
     * @return This builder for chaining.
     */
    public Builder clearMaxJoinRealms() {
      bitField0_ = (bitField0_ & ~0x00001000);
      maxJoinRealms_ = 0;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.GodDemonBattleBaseInfoResp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.GodDemonBattleBaseInfoResp)
  private static final xddq.pb.GodDemonBattleBaseInfoResp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.GodDemonBattleBaseInfoResp();
  }

  public static xddq.pb.GodDemonBattleBaseInfoResp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<GodDemonBattleBaseInfoResp>
      PARSER = new com.google.protobuf.AbstractParser<GodDemonBattleBaseInfoResp>() {
    @java.lang.Override
    public GodDemonBattleBaseInfoResp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<GodDemonBattleBaseInfoResp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<GodDemonBattleBaseInfoResp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.GodDemonBattleBaseInfoResp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

