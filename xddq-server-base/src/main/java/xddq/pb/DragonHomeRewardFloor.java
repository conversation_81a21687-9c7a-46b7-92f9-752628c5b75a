// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.DragonHomeRewardFloor}
 */
public final class DragonHomeRewardFloor extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.DragonHomeRewardFloor)
    DragonHomeRewardFloorOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      DragonHomeRewardFloor.class.getName());
  }
  // Use DragonHomeRewardFloor.newBuilder() to construct.
  private DragonHomeRewardFloor(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private DragonHomeRewardFloor() {
    rewardTitle_ = java.util.Collections.emptyList();
    teamMsg_ = java.util.Collections.emptyList();
    mallInfo_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_DragonHomeRewardFloor_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_DragonHomeRewardFloor_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.DragonHomeRewardFloor.class, xddq.pb.DragonHomeRewardFloor.Builder.class);
  }

  private int bitField0_;
  public static final int FLOOR_FIELD_NUMBER = 1;
  private int floor_ = 0;
  /**
   * <code>required int32 floor = 1;</code>
   * @return Whether the floor field is set.
   */
  @java.lang.Override
  public boolean hasFloor() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 floor = 1;</code>
   * @return The floor.
   */
  @java.lang.Override
  public int getFloor() {
    return floor_;
  }

  public static final int REWARDTITLE_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.DragonHomeRewardTile> rewardTitle_;
  /**
   * <code>repeated .xddq.pb.DragonHomeRewardTile rewardTitle = 2;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.DragonHomeRewardTile> getRewardTitleList() {
    return rewardTitle_;
  }
  /**
   * <code>repeated .xddq.pb.DragonHomeRewardTile rewardTitle = 2;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.DragonHomeRewardTileOrBuilder> 
      getRewardTitleOrBuilderList() {
    return rewardTitle_;
  }
  /**
   * <code>repeated .xddq.pb.DragonHomeRewardTile rewardTitle = 2;</code>
   */
  @java.lang.Override
  public int getRewardTitleCount() {
    return rewardTitle_.size();
  }
  /**
   * <code>repeated .xddq.pb.DragonHomeRewardTile rewardTitle = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.DragonHomeRewardTile getRewardTitle(int index) {
    return rewardTitle_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.DragonHomeRewardTile rewardTitle = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.DragonHomeRewardTileOrBuilder getRewardTitleOrBuilder(
      int index) {
    return rewardTitle_.get(index);
  }

  public static final int TEAMMSG_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.DragonHomeTeamMemberMsg> teamMsg_;
  /**
   * <code>repeated .xddq.pb.DragonHomeTeamMemberMsg teamMsg = 3;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.DragonHomeTeamMemberMsg> getTeamMsgList() {
    return teamMsg_;
  }
  /**
   * <code>repeated .xddq.pb.DragonHomeTeamMemberMsg teamMsg = 3;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.DragonHomeTeamMemberMsgOrBuilder> 
      getTeamMsgOrBuilderList() {
    return teamMsg_;
  }
  /**
   * <code>repeated .xddq.pb.DragonHomeTeamMemberMsg teamMsg = 3;</code>
   */
  @java.lang.Override
  public int getTeamMsgCount() {
    return teamMsg_.size();
  }
  /**
   * <code>repeated .xddq.pb.DragonHomeTeamMemberMsg teamMsg = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.DragonHomeTeamMemberMsg getTeamMsg(int index) {
    return teamMsg_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.DragonHomeTeamMemberMsg teamMsg = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.DragonHomeTeamMemberMsgOrBuilder getTeamMsgOrBuilder(
      int index) {
    return teamMsg_.get(index);
  }

  public static final int RANK_FIELD_NUMBER = 4;
  private int rank_ = 0;
  /**
   * <code>optional int32 rank = 4;</code>
   * @return Whether the rank field is set.
   */
  @java.lang.Override
  public boolean hasRank() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int32 rank = 4;</code>
   * @return The rank.
   */
  @java.lang.Override
  public int getRank() {
    return rank_;
  }

  public static final int APPEARANCEID_FIELD_NUMBER = 5;
  private int appearanceId_ = 0;
  /**
   * <code>optional int32 appearanceId = 5;</code>
   * @return Whether the appearanceId field is set.
   */
  @java.lang.Override
  public boolean hasAppearanceId() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional int32 appearanceId = 5;</code>
   * @return The appearanceId.
   */
  @java.lang.Override
  public int getAppearanceId() {
    return appearanceId_;
  }

  public static final int CLOUDID_FIELD_NUMBER = 6;
  private int cloudId_ = 0;
  /**
   * <code>optional int32 cloudId = 6;</code>
   * @return Whether the cloudId field is set.
   */
  @java.lang.Override
  public boolean hasCloudId() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional int32 cloudId = 6;</code>
   * @return The cloudId.
   */
  @java.lang.Override
  public int getCloudId() {
    return cloudId_;
  }

  public static final int ISAUTOUNLOCK_FIELD_NUMBER = 7;
  private boolean isAutoUnLock_ = false;
  /**
   * <code>optional bool isAutoUnLock = 7;</code>
   * @return Whether the isAutoUnLock field is set.
   */
  @java.lang.Override
  public boolean hasIsAutoUnLock() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>optional bool isAutoUnLock = 7;</code>
   * @return The isAutoUnLock.
   */
  @java.lang.Override
  public boolean getIsAutoUnLock() {
    return isAutoUnLock_;
  }

  public static final int MALLINFO_FIELD_NUMBER = 8;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.DragonHomeMallInfo> mallInfo_;
  /**
   * <code>repeated .xddq.pb.DragonHomeMallInfo mallInfo = 8;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.DragonHomeMallInfo> getMallInfoList() {
    return mallInfo_;
  }
  /**
   * <code>repeated .xddq.pb.DragonHomeMallInfo mallInfo = 8;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.DragonHomeMallInfoOrBuilder> 
      getMallInfoOrBuilderList() {
    return mallInfo_;
  }
  /**
   * <code>repeated .xddq.pb.DragonHomeMallInfo mallInfo = 8;</code>
   */
  @java.lang.Override
  public int getMallInfoCount() {
    return mallInfo_.size();
  }
  /**
   * <code>repeated .xddq.pb.DragonHomeMallInfo mallInfo = 8;</code>
   */
  @java.lang.Override
  public xddq.pb.DragonHomeMallInfo getMallInfo(int index) {
    return mallInfo_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.DragonHomeMallInfo mallInfo = 8;</code>
   */
  @java.lang.Override
  public xddq.pb.DragonHomeMallInfoOrBuilder getMallInfoOrBuilder(
      int index) {
    return mallInfo_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasFloor()) {
      memoizedIsInitialized = 0;
      return false;
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, floor_);
    }
    for (int i = 0; i < rewardTitle_.size(); i++) {
      output.writeMessage(2, rewardTitle_.get(i));
    }
    for (int i = 0; i < teamMsg_.size(); i++) {
      output.writeMessage(3, teamMsg_.get(i));
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(4, rank_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt32(5, appearanceId_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt32(6, cloudId_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      output.writeBool(7, isAutoUnLock_);
    }
    for (int i = 0; i < mallInfo_.size(); i++) {
      output.writeMessage(8, mallInfo_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, floor_);
    }
    for (int i = 0; i < rewardTitle_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, rewardTitle_.get(i));
    }
    for (int i = 0; i < teamMsg_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, teamMsg_.get(i));
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(4, rank_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(5, appearanceId_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(6, cloudId_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(7, isAutoUnLock_);
    }
    for (int i = 0; i < mallInfo_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(8, mallInfo_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.DragonHomeRewardFloor)) {
      return super.equals(obj);
    }
    xddq.pb.DragonHomeRewardFloor other = (xddq.pb.DragonHomeRewardFloor) obj;

    if (hasFloor() != other.hasFloor()) return false;
    if (hasFloor()) {
      if (getFloor()
          != other.getFloor()) return false;
    }
    if (!getRewardTitleList()
        .equals(other.getRewardTitleList())) return false;
    if (!getTeamMsgList()
        .equals(other.getTeamMsgList())) return false;
    if (hasRank() != other.hasRank()) return false;
    if (hasRank()) {
      if (getRank()
          != other.getRank()) return false;
    }
    if (hasAppearanceId() != other.hasAppearanceId()) return false;
    if (hasAppearanceId()) {
      if (getAppearanceId()
          != other.getAppearanceId()) return false;
    }
    if (hasCloudId() != other.hasCloudId()) return false;
    if (hasCloudId()) {
      if (getCloudId()
          != other.getCloudId()) return false;
    }
    if (hasIsAutoUnLock() != other.hasIsAutoUnLock()) return false;
    if (hasIsAutoUnLock()) {
      if (getIsAutoUnLock()
          != other.getIsAutoUnLock()) return false;
    }
    if (!getMallInfoList()
        .equals(other.getMallInfoList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasFloor()) {
      hash = (37 * hash) + FLOOR_FIELD_NUMBER;
      hash = (53 * hash) + getFloor();
    }
    if (getRewardTitleCount() > 0) {
      hash = (37 * hash) + REWARDTITLE_FIELD_NUMBER;
      hash = (53 * hash) + getRewardTitleList().hashCode();
    }
    if (getTeamMsgCount() > 0) {
      hash = (37 * hash) + TEAMMSG_FIELD_NUMBER;
      hash = (53 * hash) + getTeamMsgList().hashCode();
    }
    if (hasRank()) {
      hash = (37 * hash) + RANK_FIELD_NUMBER;
      hash = (53 * hash) + getRank();
    }
    if (hasAppearanceId()) {
      hash = (37 * hash) + APPEARANCEID_FIELD_NUMBER;
      hash = (53 * hash) + getAppearanceId();
    }
    if (hasCloudId()) {
      hash = (37 * hash) + CLOUDID_FIELD_NUMBER;
      hash = (53 * hash) + getCloudId();
    }
    if (hasIsAutoUnLock()) {
      hash = (37 * hash) + ISAUTOUNLOCK_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getIsAutoUnLock());
    }
    if (getMallInfoCount() > 0) {
      hash = (37 * hash) + MALLINFO_FIELD_NUMBER;
      hash = (53 * hash) + getMallInfoList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.DragonHomeRewardFloor parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.DragonHomeRewardFloor parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.DragonHomeRewardFloor parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.DragonHomeRewardFloor parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.DragonHomeRewardFloor parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.DragonHomeRewardFloor parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.DragonHomeRewardFloor parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.DragonHomeRewardFloor parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.DragonHomeRewardFloor parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.DragonHomeRewardFloor parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.DragonHomeRewardFloor parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.DragonHomeRewardFloor parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.DragonHomeRewardFloor prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.DragonHomeRewardFloor}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.DragonHomeRewardFloor)
      xddq.pb.DragonHomeRewardFloorOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_DragonHomeRewardFloor_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_DragonHomeRewardFloor_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.DragonHomeRewardFloor.class, xddq.pb.DragonHomeRewardFloor.Builder.class);
    }

    // Construct using xddq.pb.DragonHomeRewardFloor.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      floor_ = 0;
      if (rewardTitleBuilder_ == null) {
        rewardTitle_ = java.util.Collections.emptyList();
      } else {
        rewardTitle_ = null;
        rewardTitleBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000002);
      if (teamMsgBuilder_ == null) {
        teamMsg_ = java.util.Collections.emptyList();
      } else {
        teamMsg_ = null;
        teamMsgBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000004);
      rank_ = 0;
      appearanceId_ = 0;
      cloudId_ = 0;
      isAutoUnLock_ = false;
      if (mallInfoBuilder_ == null) {
        mallInfo_ = java.util.Collections.emptyList();
      } else {
        mallInfo_ = null;
        mallInfoBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000080);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_DragonHomeRewardFloor_descriptor;
    }

    @java.lang.Override
    public xddq.pb.DragonHomeRewardFloor getDefaultInstanceForType() {
      return xddq.pb.DragonHomeRewardFloor.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.DragonHomeRewardFloor build() {
      xddq.pb.DragonHomeRewardFloor result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.DragonHomeRewardFloor buildPartial() {
      xddq.pb.DragonHomeRewardFloor result = new xddq.pb.DragonHomeRewardFloor(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.DragonHomeRewardFloor result) {
      if (rewardTitleBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          rewardTitle_ = java.util.Collections.unmodifiableList(rewardTitle_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.rewardTitle_ = rewardTitle_;
      } else {
        result.rewardTitle_ = rewardTitleBuilder_.build();
      }
      if (teamMsgBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0)) {
          teamMsg_ = java.util.Collections.unmodifiableList(teamMsg_);
          bitField0_ = (bitField0_ & ~0x00000004);
        }
        result.teamMsg_ = teamMsg_;
      } else {
        result.teamMsg_ = teamMsgBuilder_.build();
      }
      if (mallInfoBuilder_ == null) {
        if (((bitField0_ & 0x00000080) != 0)) {
          mallInfo_ = java.util.Collections.unmodifiableList(mallInfo_);
          bitField0_ = (bitField0_ & ~0x00000080);
        }
        result.mallInfo_ = mallInfo_;
      } else {
        result.mallInfo_ = mallInfoBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.DragonHomeRewardFloor result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.floor_ = floor_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.rank_ = rank_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.appearanceId_ = appearanceId_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.cloudId_ = cloudId_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.isAutoUnLock_ = isAutoUnLock_;
        to_bitField0_ |= 0x00000010;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.DragonHomeRewardFloor) {
        return mergeFrom((xddq.pb.DragonHomeRewardFloor)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.DragonHomeRewardFloor other) {
      if (other == xddq.pb.DragonHomeRewardFloor.getDefaultInstance()) return this;
      if (other.hasFloor()) {
        setFloor(other.getFloor());
      }
      if (rewardTitleBuilder_ == null) {
        if (!other.rewardTitle_.isEmpty()) {
          if (rewardTitle_.isEmpty()) {
            rewardTitle_ = other.rewardTitle_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureRewardTitleIsMutable();
            rewardTitle_.addAll(other.rewardTitle_);
          }
          onChanged();
        }
      } else {
        if (!other.rewardTitle_.isEmpty()) {
          if (rewardTitleBuilder_.isEmpty()) {
            rewardTitleBuilder_.dispose();
            rewardTitleBuilder_ = null;
            rewardTitle_ = other.rewardTitle_;
            bitField0_ = (bitField0_ & ~0x00000002);
            rewardTitleBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetRewardTitleFieldBuilder() : null;
          } else {
            rewardTitleBuilder_.addAllMessages(other.rewardTitle_);
          }
        }
      }
      if (teamMsgBuilder_ == null) {
        if (!other.teamMsg_.isEmpty()) {
          if (teamMsg_.isEmpty()) {
            teamMsg_ = other.teamMsg_;
            bitField0_ = (bitField0_ & ~0x00000004);
          } else {
            ensureTeamMsgIsMutable();
            teamMsg_.addAll(other.teamMsg_);
          }
          onChanged();
        }
      } else {
        if (!other.teamMsg_.isEmpty()) {
          if (teamMsgBuilder_.isEmpty()) {
            teamMsgBuilder_.dispose();
            teamMsgBuilder_ = null;
            teamMsg_ = other.teamMsg_;
            bitField0_ = (bitField0_ & ~0x00000004);
            teamMsgBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetTeamMsgFieldBuilder() : null;
          } else {
            teamMsgBuilder_.addAllMessages(other.teamMsg_);
          }
        }
      }
      if (other.hasRank()) {
        setRank(other.getRank());
      }
      if (other.hasAppearanceId()) {
        setAppearanceId(other.getAppearanceId());
      }
      if (other.hasCloudId()) {
        setCloudId(other.getCloudId());
      }
      if (other.hasIsAutoUnLock()) {
        setIsAutoUnLock(other.getIsAutoUnLock());
      }
      if (mallInfoBuilder_ == null) {
        if (!other.mallInfo_.isEmpty()) {
          if (mallInfo_.isEmpty()) {
            mallInfo_ = other.mallInfo_;
            bitField0_ = (bitField0_ & ~0x00000080);
          } else {
            ensureMallInfoIsMutable();
            mallInfo_.addAll(other.mallInfo_);
          }
          onChanged();
        }
      } else {
        if (!other.mallInfo_.isEmpty()) {
          if (mallInfoBuilder_.isEmpty()) {
            mallInfoBuilder_.dispose();
            mallInfoBuilder_ = null;
            mallInfo_ = other.mallInfo_;
            bitField0_ = (bitField0_ & ~0x00000080);
            mallInfoBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetMallInfoFieldBuilder() : null;
          } else {
            mallInfoBuilder_.addAllMessages(other.mallInfo_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasFloor()) {
        return false;
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              floor_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              xddq.pb.DragonHomeRewardTile m =
                  input.readMessage(
                      xddq.pb.DragonHomeRewardTile.parser(),
                      extensionRegistry);
              if (rewardTitleBuilder_ == null) {
                ensureRewardTitleIsMutable();
                rewardTitle_.add(m);
              } else {
                rewardTitleBuilder_.addMessage(m);
              }
              break;
            } // case 18
            case 26: {
              xddq.pb.DragonHomeTeamMemberMsg m =
                  input.readMessage(
                      xddq.pb.DragonHomeTeamMemberMsg.parser(),
                      extensionRegistry);
              if (teamMsgBuilder_ == null) {
                ensureTeamMsgIsMutable();
                teamMsg_.add(m);
              } else {
                teamMsgBuilder_.addMessage(m);
              }
              break;
            } // case 26
            case 32: {
              rank_ = input.readInt32();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            case 40: {
              appearanceId_ = input.readInt32();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            case 48: {
              cloudId_ = input.readInt32();
              bitField0_ |= 0x00000020;
              break;
            } // case 48
            case 56: {
              isAutoUnLock_ = input.readBool();
              bitField0_ |= 0x00000040;
              break;
            } // case 56
            case 66: {
              xddq.pb.DragonHomeMallInfo m =
                  input.readMessage(
                      xddq.pb.DragonHomeMallInfo.parser(),
                      extensionRegistry);
              if (mallInfoBuilder_ == null) {
                ensureMallInfoIsMutable();
                mallInfo_.add(m);
              } else {
                mallInfoBuilder_.addMessage(m);
              }
              break;
            } // case 66
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int floor_ ;
    /**
     * <code>required int32 floor = 1;</code>
     * @return Whether the floor field is set.
     */
    @java.lang.Override
    public boolean hasFloor() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 floor = 1;</code>
     * @return The floor.
     */
    @java.lang.Override
    public int getFloor() {
      return floor_;
    }
    /**
     * <code>required int32 floor = 1;</code>
     * @param value The floor to set.
     * @return This builder for chaining.
     */
    public Builder setFloor(int value) {

      floor_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 floor = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearFloor() {
      bitField0_ = (bitField0_ & ~0x00000001);
      floor_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.DragonHomeRewardTile> rewardTitle_ =
      java.util.Collections.emptyList();
    private void ensureRewardTitleIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        rewardTitle_ = new java.util.ArrayList<xddq.pb.DragonHomeRewardTile>(rewardTitle_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.DragonHomeRewardTile, xddq.pb.DragonHomeRewardTile.Builder, xddq.pb.DragonHomeRewardTileOrBuilder> rewardTitleBuilder_;

    /**
     * <code>repeated .xddq.pb.DragonHomeRewardTile rewardTitle = 2;</code>
     */
    public java.util.List<xddq.pb.DragonHomeRewardTile> getRewardTitleList() {
      if (rewardTitleBuilder_ == null) {
        return java.util.Collections.unmodifiableList(rewardTitle_);
      } else {
        return rewardTitleBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeRewardTile rewardTitle = 2;</code>
     */
    public int getRewardTitleCount() {
      if (rewardTitleBuilder_ == null) {
        return rewardTitle_.size();
      } else {
        return rewardTitleBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeRewardTile rewardTitle = 2;</code>
     */
    public xddq.pb.DragonHomeRewardTile getRewardTitle(int index) {
      if (rewardTitleBuilder_ == null) {
        return rewardTitle_.get(index);
      } else {
        return rewardTitleBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeRewardTile rewardTitle = 2;</code>
     */
    public Builder setRewardTitle(
        int index, xddq.pb.DragonHomeRewardTile value) {
      if (rewardTitleBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRewardTitleIsMutable();
        rewardTitle_.set(index, value);
        onChanged();
      } else {
        rewardTitleBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeRewardTile rewardTitle = 2;</code>
     */
    public Builder setRewardTitle(
        int index, xddq.pb.DragonHomeRewardTile.Builder builderForValue) {
      if (rewardTitleBuilder_ == null) {
        ensureRewardTitleIsMutable();
        rewardTitle_.set(index, builderForValue.build());
        onChanged();
      } else {
        rewardTitleBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeRewardTile rewardTitle = 2;</code>
     */
    public Builder addRewardTitle(xddq.pb.DragonHomeRewardTile value) {
      if (rewardTitleBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRewardTitleIsMutable();
        rewardTitle_.add(value);
        onChanged();
      } else {
        rewardTitleBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeRewardTile rewardTitle = 2;</code>
     */
    public Builder addRewardTitle(
        int index, xddq.pb.DragonHomeRewardTile value) {
      if (rewardTitleBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRewardTitleIsMutable();
        rewardTitle_.add(index, value);
        onChanged();
      } else {
        rewardTitleBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeRewardTile rewardTitle = 2;</code>
     */
    public Builder addRewardTitle(
        xddq.pb.DragonHomeRewardTile.Builder builderForValue) {
      if (rewardTitleBuilder_ == null) {
        ensureRewardTitleIsMutable();
        rewardTitle_.add(builderForValue.build());
        onChanged();
      } else {
        rewardTitleBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeRewardTile rewardTitle = 2;</code>
     */
    public Builder addRewardTitle(
        int index, xddq.pb.DragonHomeRewardTile.Builder builderForValue) {
      if (rewardTitleBuilder_ == null) {
        ensureRewardTitleIsMutable();
        rewardTitle_.add(index, builderForValue.build());
        onChanged();
      } else {
        rewardTitleBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeRewardTile rewardTitle = 2;</code>
     */
    public Builder addAllRewardTitle(
        java.lang.Iterable<? extends xddq.pb.DragonHomeRewardTile> values) {
      if (rewardTitleBuilder_ == null) {
        ensureRewardTitleIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, rewardTitle_);
        onChanged();
      } else {
        rewardTitleBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeRewardTile rewardTitle = 2;</code>
     */
    public Builder clearRewardTitle() {
      if (rewardTitleBuilder_ == null) {
        rewardTitle_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        rewardTitleBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeRewardTile rewardTitle = 2;</code>
     */
    public Builder removeRewardTitle(int index) {
      if (rewardTitleBuilder_ == null) {
        ensureRewardTitleIsMutable();
        rewardTitle_.remove(index);
        onChanged();
      } else {
        rewardTitleBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeRewardTile rewardTitle = 2;</code>
     */
    public xddq.pb.DragonHomeRewardTile.Builder getRewardTitleBuilder(
        int index) {
      return internalGetRewardTitleFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeRewardTile rewardTitle = 2;</code>
     */
    public xddq.pb.DragonHomeRewardTileOrBuilder getRewardTitleOrBuilder(
        int index) {
      if (rewardTitleBuilder_ == null) {
        return rewardTitle_.get(index);  } else {
        return rewardTitleBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeRewardTile rewardTitle = 2;</code>
     */
    public java.util.List<? extends xddq.pb.DragonHomeRewardTileOrBuilder> 
         getRewardTitleOrBuilderList() {
      if (rewardTitleBuilder_ != null) {
        return rewardTitleBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(rewardTitle_);
      }
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeRewardTile rewardTitle = 2;</code>
     */
    public xddq.pb.DragonHomeRewardTile.Builder addRewardTitleBuilder() {
      return internalGetRewardTitleFieldBuilder().addBuilder(
          xddq.pb.DragonHomeRewardTile.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeRewardTile rewardTitle = 2;</code>
     */
    public xddq.pb.DragonHomeRewardTile.Builder addRewardTitleBuilder(
        int index) {
      return internalGetRewardTitleFieldBuilder().addBuilder(
          index, xddq.pb.DragonHomeRewardTile.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeRewardTile rewardTitle = 2;</code>
     */
    public java.util.List<xddq.pb.DragonHomeRewardTile.Builder> 
         getRewardTitleBuilderList() {
      return internalGetRewardTitleFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.DragonHomeRewardTile, xddq.pb.DragonHomeRewardTile.Builder, xddq.pb.DragonHomeRewardTileOrBuilder> 
        internalGetRewardTitleFieldBuilder() {
      if (rewardTitleBuilder_ == null) {
        rewardTitleBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.DragonHomeRewardTile, xddq.pb.DragonHomeRewardTile.Builder, xddq.pb.DragonHomeRewardTileOrBuilder>(
                rewardTitle_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        rewardTitle_ = null;
      }
      return rewardTitleBuilder_;
    }

    private java.util.List<xddq.pb.DragonHomeTeamMemberMsg> teamMsg_ =
      java.util.Collections.emptyList();
    private void ensureTeamMsgIsMutable() {
      if (!((bitField0_ & 0x00000004) != 0)) {
        teamMsg_ = new java.util.ArrayList<xddq.pb.DragonHomeTeamMemberMsg>(teamMsg_);
        bitField0_ |= 0x00000004;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.DragonHomeTeamMemberMsg, xddq.pb.DragonHomeTeamMemberMsg.Builder, xddq.pb.DragonHomeTeamMemberMsgOrBuilder> teamMsgBuilder_;

    /**
     * <code>repeated .xddq.pb.DragonHomeTeamMemberMsg teamMsg = 3;</code>
     */
    public java.util.List<xddq.pb.DragonHomeTeamMemberMsg> getTeamMsgList() {
      if (teamMsgBuilder_ == null) {
        return java.util.Collections.unmodifiableList(teamMsg_);
      } else {
        return teamMsgBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeTeamMemberMsg teamMsg = 3;</code>
     */
    public int getTeamMsgCount() {
      if (teamMsgBuilder_ == null) {
        return teamMsg_.size();
      } else {
        return teamMsgBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeTeamMemberMsg teamMsg = 3;</code>
     */
    public xddq.pb.DragonHomeTeamMemberMsg getTeamMsg(int index) {
      if (teamMsgBuilder_ == null) {
        return teamMsg_.get(index);
      } else {
        return teamMsgBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeTeamMemberMsg teamMsg = 3;</code>
     */
    public Builder setTeamMsg(
        int index, xddq.pb.DragonHomeTeamMemberMsg value) {
      if (teamMsgBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureTeamMsgIsMutable();
        teamMsg_.set(index, value);
        onChanged();
      } else {
        teamMsgBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeTeamMemberMsg teamMsg = 3;</code>
     */
    public Builder setTeamMsg(
        int index, xddq.pb.DragonHomeTeamMemberMsg.Builder builderForValue) {
      if (teamMsgBuilder_ == null) {
        ensureTeamMsgIsMutable();
        teamMsg_.set(index, builderForValue.build());
        onChanged();
      } else {
        teamMsgBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeTeamMemberMsg teamMsg = 3;</code>
     */
    public Builder addTeamMsg(xddq.pb.DragonHomeTeamMemberMsg value) {
      if (teamMsgBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureTeamMsgIsMutable();
        teamMsg_.add(value);
        onChanged();
      } else {
        teamMsgBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeTeamMemberMsg teamMsg = 3;</code>
     */
    public Builder addTeamMsg(
        int index, xddq.pb.DragonHomeTeamMemberMsg value) {
      if (teamMsgBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureTeamMsgIsMutable();
        teamMsg_.add(index, value);
        onChanged();
      } else {
        teamMsgBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeTeamMemberMsg teamMsg = 3;</code>
     */
    public Builder addTeamMsg(
        xddq.pb.DragonHomeTeamMemberMsg.Builder builderForValue) {
      if (teamMsgBuilder_ == null) {
        ensureTeamMsgIsMutable();
        teamMsg_.add(builderForValue.build());
        onChanged();
      } else {
        teamMsgBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeTeamMemberMsg teamMsg = 3;</code>
     */
    public Builder addTeamMsg(
        int index, xddq.pb.DragonHomeTeamMemberMsg.Builder builderForValue) {
      if (teamMsgBuilder_ == null) {
        ensureTeamMsgIsMutable();
        teamMsg_.add(index, builderForValue.build());
        onChanged();
      } else {
        teamMsgBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeTeamMemberMsg teamMsg = 3;</code>
     */
    public Builder addAllTeamMsg(
        java.lang.Iterable<? extends xddq.pb.DragonHomeTeamMemberMsg> values) {
      if (teamMsgBuilder_ == null) {
        ensureTeamMsgIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, teamMsg_);
        onChanged();
      } else {
        teamMsgBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeTeamMemberMsg teamMsg = 3;</code>
     */
    public Builder clearTeamMsg() {
      if (teamMsgBuilder_ == null) {
        teamMsg_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
      } else {
        teamMsgBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeTeamMemberMsg teamMsg = 3;</code>
     */
    public Builder removeTeamMsg(int index) {
      if (teamMsgBuilder_ == null) {
        ensureTeamMsgIsMutable();
        teamMsg_.remove(index);
        onChanged();
      } else {
        teamMsgBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeTeamMemberMsg teamMsg = 3;</code>
     */
    public xddq.pb.DragonHomeTeamMemberMsg.Builder getTeamMsgBuilder(
        int index) {
      return internalGetTeamMsgFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeTeamMemberMsg teamMsg = 3;</code>
     */
    public xddq.pb.DragonHomeTeamMemberMsgOrBuilder getTeamMsgOrBuilder(
        int index) {
      if (teamMsgBuilder_ == null) {
        return teamMsg_.get(index);  } else {
        return teamMsgBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeTeamMemberMsg teamMsg = 3;</code>
     */
    public java.util.List<? extends xddq.pb.DragonHomeTeamMemberMsgOrBuilder> 
         getTeamMsgOrBuilderList() {
      if (teamMsgBuilder_ != null) {
        return teamMsgBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(teamMsg_);
      }
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeTeamMemberMsg teamMsg = 3;</code>
     */
    public xddq.pb.DragonHomeTeamMemberMsg.Builder addTeamMsgBuilder() {
      return internalGetTeamMsgFieldBuilder().addBuilder(
          xddq.pb.DragonHomeTeamMemberMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeTeamMemberMsg teamMsg = 3;</code>
     */
    public xddq.pb.DragonHomeTeamMemberMsg.Builder addTeamMsgBuilder(
        int index) {
      return internalGetTeamMsgFieldBuilder().addBuilder(
          index, xddq.pb.DragonHomeTeamMemberMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeTeamMemberMsg teamMsg = 3;</code>
     */
    public java.util.List<xddq.pb.DragonHomeTeamMemberMsg.Builder> 
         getTeamMsgBuilderList() {
      return internalGetTeamMsgFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.DragonHomeTeamMemberMsg, xddq.pb.DragonHomeTeamMemberMsg.Builder, xddq.pb.DragonHomeTeamMemberMsgOrBuilder> 
        internalGetTeamMsgFieldBuilder() {
      if (teamMsgBuilder_ == null) {
        teamMsgBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.DragonHomeTeamMemberMsg, xddq.pb.DragonHomeTeamMemberMsg.Builder, xddq.pb.DragonHomeTeamMemberMsgOrBuilder>(
                teamMsg_,
                ((bitField0_ & 0x00000004) != 0),
                getParentForChildren(),
                isClean());
        teamMsg_ = null;
      }
      return teamMsgBuilder_;
    }

    private int rank_ ;
    /**
     * <code>optional int32 rank = 4;</code>
     * @return Whether the rank field is set.
     */
    @java.lang.Override
    public boolean hasRank() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int32 rank = 4;</code>
     * @return The rank.
     */
    @java.lang.Override
    public int getRank() {
      return rank_;
    }
    /**
     * <code>optional int32 rank = 4;</code>
     * @param value The rank to set.
     * @return This builder for chaining.
     */
    public Builder setRank(int value) {

      rank_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 rank = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearRank() {
      bitField0_ = (bitField0_ & ~0x00000008);
      rank_ = 0;
      onChanged();
      return this;
    }

    private int appearanceId_ ;
    /**
     * <code>optional int32 appearanceId = 5;</code>
     * @return Whether the appearanceId field is set.
     */
    @java.lang.Override
    public boolean hasAppearanceId() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional int32 appearanceId = 5;</code>
     * @return The appearanceId.
     */
    @java.lang.Override
    public int getAppearanceId() {
      return appearanceId_;
    }
    /**
     * <code>optional int32 appearanceId = 5;</code>
     * @param value The appearanceId to set.
     * @return This builder for chaining.
     */
    public Builder setAppearanceId(int value) {

      appearanceId_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 appearanceId = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearAppearanceId() {
      bitField0_ = (bitField0_ & ~0x00000010);
      appearanceId_ = 0;
      onChanged();
      return this;
    }

    private int cloudId_ ;
    /**
     * <code>optional int32 cloudId = 6;</code>
     * @return Whether the cloudId field is set.
     */
    @java.lang.Override
    public boolean hasCloudId() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional int32 cloudId = 6;</code>
     * @return The cloudId.
     */
    @java.lang.Override
    public int getCloudId() {
      return cloudId_;
    }
    /**
     * <code>optional int32 cloudId = 6;</code>
     * @param value The cloudId to set.
     * @return This builder for chaining.
     */
    public Builder setCloudId(int value) {

      cloudId_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 cloudId = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearCloudId() {
      bitField0_ = (bitField0_ & ~0x00000020);
      cloudId_ = 0;
      onChanged();
      return this;
    }

    private boolean isAutoUnLock_ ;
    /**
     * <code>optional bool isAutoUnLock = 7;</code>
     * @return Whether the isAutoUnLock field is set.
     */
    @java.lang.Override
    public boolean hasIsAutoUnLock() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <code>optional bool isAutoUnLock = 7;</code>
     * @return The isAutoUnLock.
     */
    @java.lang.Override
    public boolean getIsAutoUnLock() {
      return isAutoUnLock_;
    }
    /**
     * <code>optional bool isAutoUnLock = 7;</code>
     * @param value The isAutoUnLock to set.
     * @return This builder for chaining.
     */
    public Builder setIsAutoUnLock(boolean value) {

      isAutoUnLock_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>optional bool isAutoUnLock = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearIsAutoUnLock() {
      bitField0_ = (bitField0_ & ~0x00000040);
      isAutoUnLock_ = false;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.DragonHomeMallInfo> mallInfo_ =
      java.util.Collections.emptyList();
    private void ensureMallInfoIsMutable() {
      if (!((bitField0_ & 0x00000080) != 0)) {
        mallInfo_ = new java.util.ArrayList<xddq.pb.DragonHomeMallInfo>(mallInfo_);
        bitField0_ |= 0x00000080;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.DragonHomeMallInfo, xddq.pb.DragonHomeMallInfo.Builder, xddq.pb.DragonHomeMallInfoOrBuilder> mallInfoBuilder_;

    /**
     * <code>repeated .xddq.pb.DragonHomeMallInfo mallInfo = 8;</code>
     */
    public java.util.List<xddq.pb.DragonHomeMallInfo> getMallInfoList() {
      if (mallInfoBuilder_ == null) {
        return java.util.Collections.unmodifiableList(mallInfo_);
      } else {
        return mallInfoBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMallInfo mallInfo = 8;</code>
     */
    public int getMallInfoCount() {
      if (mallInfoBuilder_ == null) {
        return mallInfo_.size();
      } else {
        return mallInfoBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMallInfo mallInfo = 8;</code>
     */
    public xddq.pb.DragonHomeMallInfo getMallInfo(int index) {
      if (mallInfoBuilder_ == null) {
        return mallInfo_.get(index);
      } else {
        return mallInfoBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMallInfo mallInfo = 8;</code>
     */
    public Builder setMallInfo(
        int index, xddq.pb.DragonHomeMallInfo value) {
      if (mallInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMallInfoIsMutable();
        mallInfo_.set(index, value);
        onChanged();
      } else {
        mallInfoBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMallInfo mallInfo = 8;</code>
     */
    public Builder setMallInfo(
        int index, xddq.pb.DragonHomeMallInfo.Builder builderForValue) {
      if (mallInfoBuilder_ == null) {
        ensureMallInfoIsMutable();
        mallInfo_.set(index, builderForValue.build());
        onChanged();
      } else {
        mallInfoBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMallInfo mallInfo = 8;</code>
     */
    public Builder addMallInfo(xddq.pb.DragonHomeMallInfo value) {
      if (mallInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMallInfoIsMutable();
        mallInfo_.add(value);
        onChanged();
      } else {
        mallInfoBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMallInfo mallInfo = 8;</code>
     */
    public Builder addMallInfo(
        int index, xddq.pb.DragonHomeMallInfo value) {
      if (mallInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMallInfoIsMutable();
        mallInfo_.add(index, value);
        onChanged();
      } else {
        mallInfoBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMallInfo mallInfo = 8;</code>
     */
    public Builder addMallInfo(
        xddq.pb.DragonHomeMallInfo.Builder builderForValue) {
      if (mallInfoBuilder_ == null) {
        ensureMallInfoIsMutable();
        mallInfo_.add(builderForValue.build());
        onChanged();
      } else {
        mallInfoBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMallInfo mallInfo = 8;</code>
     */
    public Builder addMallInfo(
        int index, xddq.pb.DragonHomeMallInfo.Builder builderForValue) {
      if (mallInfoBuilder_ == null) {
        ensureMallInfoIsMutable();
        mallInfo_.add(index, builderForValue.build());
        onChanged();
      } else {
        mallInfoBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMallInfo mallInfo = 8;</code>
     */
    public Builder addAllMallInfo(
        java.lang.Iterable<? extends xddq.pb.DragonHomeMallInfo> values) {
      if (mallInfoBuilder_ == null) {
        ensureMallInfoIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, mallInfo_);
        onChanged();
      } else {
        mallInfoBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMallInfo mallInfo = 8;</code>
     */
    public Builder clearMallInfo() {
      if (mallInfoBuilder_ == null) {
        mallInfo_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000080);
        onChanged();
      } else {
        mallInfoBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMallInfo mallInfo = 8;</code>
     */
    public Builder removeMallInfo(int index) {
      if (mallInfoBuilder_ == null) {
        ensureMallInfoIsMutable();
        mallInfo_.remove(index);
        onChanged();
      } else {
        mallInfoBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMallInfo mallInfo = 8;</code>
     */
    public xddq.pb.DragonHomeMallInfo.Builder getMallInfoBuilder(
        int index) {
      return internalGetMallInfoFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMallInfo mallInfo = 8;</code>
     */
    public xddq.pb.DragonHomeMallInfoOrBuilder getMallInfoOrBuilder(
        int index) {
      if (mallInfoBuilder_ == null) {
        return mallInfo_.get(index);  } else {
        return mallInfoBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMallInfo mallInfo = 8;</code>
     */
    public java.util.List<? extends xddq.pb.DragonHomeMallInfoOrBuilder> 
         getMallInfoOrBuilderList() {
      if (mallInfoBuilder_ != null) {
        return mallInfoBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(mallInfo_);
      }
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMallInfo mallInfo = 8;</code>
     */
    public xddq.pb.DragonHomeMallInfo.Builder addMallInfoBuilder() {
      return internalGetMallInfoFieldBuilder().addBuilder(
          xddq.pb.DragonHomeMallInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMallInfo mallInfo = 8;</code>
     */
    public xddq.pb.DragonHomeMallInfo.Builder addMallInfoBuilder(
        int index) {
      return internalGetMallInfoFieldBuilder().addBuilder(
          index, xddq.pb.DragonHomeMallInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMallInfo mallInfo = 8;</code>
     */
    public java.util.List<xddq.pb.DragonHomeMallInfo.Builder> 
         getMallInfoBuilderList() {
      return internalGetMallInfoFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.DragonHomeMallInfo, xddq.pb.DragonHomeMallInfo.Builder, xddq.pb.DragonHomeMallInfoOrBuilder> 
        internalGetMallInfoFieldBuilder() {
      if (mallInfoBuilder_ == null) {
        mallInfoBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.DragonHomeMallInfo, xddq.pb.DragonHomeMallInfo.Builder, xddq.pb.DragonHomeMallInfoOrBuilder>(
                mallInfo_,
                ((bitField0_ & 0x00000080) != 0),
                getParentForChildren(),
                isClean());
        mallInfo_ = null;
      }
      return mallInfoBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.DragonHomeRewardFloor)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.DragonHomeRewardFloor)
  private static final xddq.pb.DragonHomeRewardFloor DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.DragonHomeRewardFloor();
  }

  public static xddq.pb.DragonHomeRewardFloor getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<DragonHomeRewardFloor>
      PARSER = new com.google.protobuf.AbstractParser<DragonHomeRewardFloor>() {
    @java.lang.Override
    public DragonHomeRewardFloor parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<DragonHomeRewardFloor> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<DragonHomeRewardFloor> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.DragonHomeRewardFloor getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

