// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.BodyTrialEnterBattleViewResp}
 */
public final class BodyTrialEnterBattleViewResp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.BodyTrialEnterBattleViewResp)
    BodyTrialEnterBattleViewRespOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      BodyTrialEnterBattleViewResp.class.getName());
  }
  // Use BodyTrialEnterBattleViewResp.newBuilder() to construct.
  private BodyTrialEnterBattleViewResp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private BodyTrialEnterBattleViewResp() {
    robotPlayerIds_ = emptyLongList();
    separationData_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_BodyTrialEnterBattleViewResp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_BodyTrialEnterBattleViewResp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.BodyTrialEnterBattleViewResp.class, xddq.pb.BodyTrialEnterBattleViewResp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int DATA_FIELD_NUMBER = 2;
  private xddq.pb.BodyTrialEnterBattleData data_;
  /**
   * <code>optional .xddq.pb.BodyTrialEnterBattleData data = 2;</code>
   * @return Whether the data field is set.
   */
  @java.lang.Override
  public boolean hasData() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional .xddq.pb.BodyTrialEnterBattleData data = 2;</code>
   * @return The data.
   */
  @java.lang.Override
  public xddq.pb.BodyTrialEnterBattleData getData() {
    return data_ == null ? xddq.pb.BodyTrialEnterBattleData.getDefaultInstance() : data_;
  }
  /**
   * <code>optional .xddq.pb.BodyTrialEnterBattleData data = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.BodyTrialEnterBattleDataOrBuilder getDataOrBuilder() {
    return data_ == null ? xddq.pb.BodyTrialEnterBattleData.getDefaultInstance() : data_;
  }

  public static final int SELECTBUFFDATA_FIELD_NUMBER = 3;
  private xddq.pb.BodyTrialSelectBuffInfo selectBuffData_;
  /**
   * <code>optional .xddq.pb.BodyTrialSelectBuffInfo selectBuffData = 3;</code>
   * @return Whether the selectBuffData field is set.
   */
  @java.lang.Override
  public boolean hasSelectBuffData() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional .xddq.pb.BodyTrialSelectBuffInfo selectBuffData = 3;</code>
   * @return The selectBuffData.
   */
  @java.lang.Override
  public xddq.pb.BodyTrialSelectBuffInfo getSelectBuffData() {
    return selectBuffData_ == null ? xddq.pb.BodyTrialSelectBuffInfo.getDefaultInstance() : selectBuffData_;
  }
  /**
   * <code>optional .xddq.pb.BodyTrialSelectBuffInfo selectBuffData = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.BodyTrialSelectBuffInfoOrBuilder getSelectBuffDataOrBuilder() {
    return selectBuffData_ == null ? xddq.pb.BodyTrialSelectBuffInfo.getDefaultInstance() : selectBuffData_;
  }

  public static final int ROBOTPLAYERIDS_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private com.google.protobuf.Internal.LongList robotPlayerIds_ =
      emptyLongList();
  /**
   * <code>repeated int64 robotPlayerIds = 4;</code>
   * @return A list containing the robotPlayerIds.
   */
  @java.lang.Override
  public java.util.List<java.lang.Long>
      getRobotPlayerIdsList() {
    return robotPlayerIds_;
  }
  /**
   * <code>repeated int64 robotPlayerIds = 4;</code>
   * @return The count of robotPlayerIds.
   */
  public int getRobotPlayerIdsCount() {
    return robotPlayerIds_.size();
  }
  /**
   * <code>repeated int64 robotPlayerIds = 4;</code>
   * @param index The index of the element to return.
   * @return The robotPlayerIds at the given index.
   */
  public long getRobotPlayerIds(int index) {
    return robotPlayerIds_.getLong(index);
  }

  public static final int CURFLOORID_FIELD_NUMBER = 5;
  private int curFloorId_ = 0;
  /**
   * <code>optional int32 curFloorId = 5;</code>
   * @return Whether the curFloorId field is set.
   */
  @java.lang.Override
  public boolean hasCurFloorId() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional int32 curFloorId = 5;</code>
   * @return The curFloorId.
   */
  @java.lang.Override
  public int getCurFloorId() {
    return curFloorId_;
  }

  public static final int NEEDSENDBUFFREQ_FIELD_NUMBER = 6;
  private boolean needSendBuffReq_ = false;
  /**
   * <code>optional bool needSendBuffReq = 6;</code>
   * @return Whether the needSendBuffReq field is set.
   */
  @java.lang.Override
  public boolean hasNeedSendBuffReq() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>optional bool needSendBuffReq = 6;</code>
   * @return The needSendBuffReq.
   */
  @java.lang.Override
  public boolean getNeedSendBuffReq() {
    return needSendBuffReq_;
  }

  public static final int SEPARATIONDATA_FIELD_NUMBER = 7;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.BodyTrialSeparationSimpleData> separationData_;
  /**
   * <code>repeated .xddq.pb.BodyTrialSeparationSimpleData separationData = 7;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.BodyTrialSeparationSimpleData> getSeparationDataList() {
    return separationData_;
  }
  /**
   * <code>repeated .xddq.pb.BodyTrialSeparationSimpleData separationData = 7;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.BodyTrialSeparationSimpleDataOrBuilder> 
      getSeparationDataOrBuilderList() {
    return separationData_;
  }
  /**
   * <code>repeated .xddq.pb.BodyTrialSeparationSimpleData separationData = 7;</code>
   */
  @java.lang.Override
  public int getSeparationDataCount() {
    return separationData_.size();
  }
  /**
   * <code>repeated .xddq.pb.BodyTrialSeparationSimpleData separationData = 7;</code>
   */
  @java.lang.Override
  public xddq.pb.BodyTrialSeparationSimpleData getSeparationData(int index) {
    return separationData_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.BodyTrialSeparationSimpleData separationData = 7;</code>
   */
  @java.lang.Override
  public xddq.pb.BodyTrialSeparationSimpleDataOrBuilder getSeparationDataOrBuilder(
      int index) {
    return separationData_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (hasData()) {
      if (!getData().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    for (int i = 0; i < getSeparationDataCount(); i++) {
      if (!getSeparationData(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeMessage(2, getData());
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeMessage(3, getSelectBuffData());
    }
    for (int i = 0; i < robotPlayerIds_.size(); i++) {
      output.writeInt64(4, robotPlayerIds_.getLong(i));
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt32(5, curFloorId_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      output.writeBool(6, needSendBuffReq_);
    }
    for (int i = 0; i < separationData_.size(); i++) {
      output.writeMessage(7, separationData_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getData());
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, getSelectBuffData());
    }
    {
      int dataSize = 0;
      for (int i = 0; i < robotPlayerIds_.size(); i++) {
        dataSize += com.google.protobuf.CodedOutputStream
          .computeInt64SizeNoTag(robotPlayerIds_.getLong(i));
      }
      size += dataSize;
      size += 1 * getRobotPlayerIdsList().size();
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(5, curFloorId_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(6, needSendBuffReq_);
    }
    for (int i = 0; i < separationData_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(7, separationData_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.BodyTrialEnterBattleViewResp)) {
      return super.equals(obj);
    }
    xddq.pb.BodyTrialEnterBattleViewResp other = (xddq.pb.BodyTrialEnterBattleViewResp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (hasData() != other.hasData()) return false;
    if (hasData()) {
      if (!getData()
          .equals(other.getData())) return false;
    }
    if (hasSelectBuffData() != other.hasSelectBuffData()) return false;
    if (hasSelectBuffData()) {
      if (!getSelectBuffData()
          .equals(other.getSelectBuffData())) return false;
    }
    if (!getRobotPlayerIdsList()
        .equals(other.getRobotPlayerIdsList())) return false;
    if (hasCurFloorId() != other.hasCurFloorId()) return false;
    if (hasCurFloorId()) {
      if (getCurFloorId()
          != other.getCurFloorId()) return false;
    }
    if (hasNeedSendBuffReq() != other.hasNeedSendBuffReq()) return false;
    if (hasNeedSendBuffReq()) {
      if (getNeedSendBuffReq()
          != other.getNeedSendBuffReq()) return false;
    }
    if (!getSeparationDataList()
        .equals(other.getSeparationDataList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (hasData()) {
      hash = (37 * hash) + DATA_FIELD_NUMBER;
      hash = (53 * hash) + getData().hashCode();
    }
    if (hasSelectBuffData()) {
      hash = (37 * hash) + SELECTBUFFDATA_FIELD_NUMBER;
      hash = (53 * hash) + getSelectBuffData().hashCode();
    }
    if (getRobotPlayerIdsCount() > 0) {
      hash = (37 * hash) + ROBOTPLAYERIDS_FIELD_NUMBER;
      hash = (53 * hash) + getRobotPlayerIdsList().hashCode();
    }
    if (hasCurFloorId()) {
      hash = (37 * hash) + CURFLOORID_FIELD_NUMBER;
      hash = (53 * hash) + getCurFloorId();
    }
    if (hasNeedSendBuffReq()) {
      hash = (37 * hash) + NEEDSENDBUFFREQ_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getNeedSendBuffReq());
    }
    if (getSeparationDataCount() > 0) {
      hash = (37 * hash) + SEPARATIONDATA_FIELD_NUMBER;
      hash = (53 * hash) + getSeparationDataList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.BodyTrialEnterBattleViewResp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.BodyTrialEnterBattleViewResp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.BodyTrialEnterBattleViewResp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.BodyTrialEnterBattleViewResp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.BodyTrialEnterBattleViewResp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.BodyTrialEnterBattleViewResp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.BodyTrialEnterBattleViewResp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.BodyTrialEnterBattleViewResp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.BodyTrialEnterBattleViewResp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.BodyTrialEnterBattleViewResp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.BodyTrialEnterBattleViewResp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.BodyTrialEnterBattleViewResp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.BodyTrialEnterBattleViewResp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.BodyTrialEnterBattleViewResp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.BodyTrialEnterBattleViewResp)
      xddq.pb.BodyTrialEnterBattleViewRespOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_BodyTrialEnterBattleViewResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_BodyTrialEnterBattleViewResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.BodyTrialEnterBattleViewResp.class, xddq.pb.BodyTrialEnterBattleViewResp.Builder.class);
    }

    // Construct using xddq.pb.BodyTrialEnterBattleViewResp.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetDataFieldBuilder();
        internalGetSelectBuffDataFieldBuilder();
        internalGetSeparationDataFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      data_ = null;
      if (dataBuilder_ != null) {
        dataBuilder_.dispose();
        dataBuilder_ = null;
      }
      selectBuffData_ = null;
      if (selectBuffDataBuilder_ != null) {
        selectBuffDataBuilder_.dispose();
        selectBuffDataBuilder_ = null;
      }
      robotPlayerIds_ = emptyLongList();
      curFloorId_ = 0;
      needSendBuffReq_ = false;
      if (separationDataBuilder_ == null) {
        separationData_ = java.util.Collections.emptyList();
      } else {
        separationData_ = null;
        separationDataBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000040);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_BodyTrialEnterBattleViewResp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.BodyTrialEnterBattleViewResp getDefaultInstanceForType() {
      return xddq.pb.BodyTrialEnterBattleViewResp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.BodyTrialEnterBattleViewResp build() {
      xddq.pb.BodyTrialEnterBattleViewResp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.BodyTrialEnterBattleViewResp buildPartial() {
      xddq.pb.BodyTrialEnterBattleViewResp result = new xddq.pb.BodyTrialEnterBattleViewResp(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.BodyTrialEnterBattleViewResp result) {
      if (separationDataBuilder_ == null) {
        if (((bitField0_ & 0x00000040) != 0)) {
          separationData_ = java.util.Collections.unmodifiableList(separationData_);
          bitField0_ = (bitField0_ & ~0x00000040);
        }
        result.separationData_ = separationData_;
      } else {
        result.separationData_ = separationDataBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.BodyTrialEnterBattleViewResp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.data_ = dataBuilder_ == null
            ? data_
            : dataBuilder_.build();
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.selectBuffData_ = selectBuffDataBuilder_ == null
            ? selectBuffData_
            : selectBuffDataBuilder_.build();
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        robotPlayerIds_.makeImmutable();
        result.robotPlayerIds_ = robotPlayerIds_;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.curFloorId_ = curFloorId_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.needSendBuffReq_ = needSendBuffReq_;
        to_bitField0_ |= 0x00000010;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.BodyTrialEnterBattleViewResp) {
        return mergeFrom((xddq.pb.BodyTrialEnterBattleViewResp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.BodyTrialEnterBattleViewResp other) {
      if (other == xddq.pb.BodyTrialEnterBattleViewResp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (other.hasData()) {
        mergeData(other.getData());
      }
      if (other.hasSelectBuffData()) {
        mergeSelectBuffData(other.getSelectBuffData());
      }
      if (!other.robotPlayerIds_.isEmpty()) {
        if (robotPlayerIds_.isEmpty()) {
          robotPlayerIds_ = other.robotPlayerIds_;
          robotPlayerIds_.makeImmutable();
          bitField0_ |= 0x00000008;
        } else {
          ensureRobotPlayerIdsIsMutable();
          robotPlayerIds_.addAll(other.robotPlayerIds_);
        }
        onChanged();
      }
      if (other.hasCurFloorId()) {
        setCurFloorId(other.getCurFloorId());
      }
      if (other.hasNeedSendBuffReq()) {
        setNeedSendBuffReq(other.getNeedSendBuffReq());
      }
      if (separationDataBuilder_ == null) {
        if (!other.separationData_.isEmpty()) {
          if (separationData_.isEmpty()) {
            separationData_ = other.separationData_;
            bitField0_ = (bitField0_ & ~0x00000040);
          } else {
            ensureSeparationDataIsMutable();
            separationData_.addAll(other.separationData_);
          }
          onChanged();
        }
      } else {
        if (!other.separationData_.isEmpty()) {
          if (separationDataBuilder_.isEmpty()) {
            separationDataBuilder_.dispose();
            separationDataBuilder_ = null;
            separationData_ = other.separationData_;
            bitField0_ = (bitField0_ & ~0x00000040);
            separationDataBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetSeparationDataFieldBuilder() : null;
          } else {
            separationDataBuilder_.addAllMessages(other.separationData_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      if (hasData()) {
        if (!getData().isInitialized()) {
          return false;
        }
      }
      for (int i = 0; i < getSeparationDataCount(); i++) {
        if (!getSeparationData(i).isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              input.readMessage(
                  internalGetDataFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 26: {
              input.readMessage(
                  internalGetSelectBuffDataFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 32: {
              long v = input.readInt64();
              ensureRobotPlayerIdsIsMutable();
              robotPlayerIds_.addLong(v);
              break;
            } // case 32
            case 34: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              ensureRobotPlayerIdsIsMutable();
              while (input.getBytesUntilLimit() > 0) {
                robotPlayerIds_.addLong(input.readInt64());
              }
              input.popLimit(limit);
              break;
            } // case 34
            case 40: {
              curFloorId_ = input.readInt32();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            case 48: {
              needSendBuffReq_ = input.readBool();
              bitField0_ |= 0x00000020;
              break;
            } // case 48
            case 58: {
              xddq.pb.BodyTrialSeparationSimpleData m =
                  input.readMessage(
                      xddq.pb.BodyTrialSeparationSimpleData.parser(),
                      extensionRegistry);
              if (separationDataBuilder_ == null) {
                ensureSeparationDataIsMutable();
                separationData_.add(m);
              } else {
                separationDataBuilder_.addMessage(m);
              }
              break;
            } // case 58
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private xddq.pb.BodyTrialEnterBattleData data_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.BodyTrialEnterBattleData, xddq.pb.BodyTrialEnterBattleData.Builder, xddq.pb.BodyTrialEnterBattleDataOrBuilder> dataBuilder_;
    /**
     * <code>optional .xddq.pb.BodyTrialEnterBattleData data = 2;</code>
     * @return Whether the data field is set.
     */
    public boolean hasData() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .xddq.pb.BodyTrialEnterBattleData data = 2;</code>
     * @return The data.
     */
    public xddq.pb.BodyTrialEnterBattleData getData() {
      if (dataBuilder_ == null) {
        return data_ == null ? xddq.pb.BodyTrialEnterBattleData.getDefaultInstance() : data_;
      } else {
        return dataBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.BodyTrialEnterBattleData data = 2;</code>
     */
    public Builder setData(xddq.pb.BodyTrialEnterBattleData value) {
      if (dataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        data_ = value;
      } else {
        dataBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.BodyTrialEnterBattleData data = 2;</code>
     */
    public Builder setData(
        xddq.pb.BodyTrialEnterBattleData.Builder builderForValue) {
      if (dataBuilder_ == null) {
        data_ = builderForValue.build();
      } else {
        dataBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.BodyTrialEnterBattleData data = 2;</code>
     */
    public Builder mergeData(xddq.pb.BodyTrialEnterBattleData value) {
      if (dataBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0) &&
          data_ != null &&
          data_ != xddq.pb.BodyTrialEnterBattleData.getDefaultInstance()) {
          getDataBuilder().mergeFrom(value);
        } else {
          data_ = value;
        }
      } else {
        dataBuilder_.mergeFrom(value);
      }
      if (data_ != null) {
        bitField0_ |= 0x00000002;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.BodyTrialEnterBattleData data = 2;</code>
     */
    public Builder clearData() {
      bitField0_ = (bitField0_ & ~0x00000002);
      data_ = null;
      if (dataBuilder_ != null) {
        dataBuilder_.dispose();
        dataBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.BodyTrialEnterBattleData data = 2;</code>
     */
    public xddq.pb.BodyTrialEnterBattleData.Builder getDataBuilder() {
      bitField0_ |= 0x00000002;
      onChanged();
      return internalGetDataFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.BodyTrialEnterBattleData data = 2;</code>
     */
    public xddq.pb.BodyTrialEnterBattleDataOrBuilder getDataOrBuilder() {
      if (dataBuilder_ != null) {
        return dataBuilder_.getMessageOrBuilder();
      } else {
        return data_ == null ?
            xddq.pb.BodyTrialEnterBattleData.getDefaultInstance() : data_;
      }
    }
    /**
     * <code>optional .xddq.pb.BodyTrialEnterBattleData data = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.BodyTrialEnterBattleData, xddq.pb.BodyTrialEnterBattleData.Builder, xddq.pb.BodyTrialEnterBattleDataOrBuilder> 
        internalGetDataFieldBuilder() {
      if (dataBuilder_ == null) {
        dataBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.BodyTrialEnterBattleData, xddq.pb.BodyTrialEnterBattleData.Builder, xddq.pb.BodyTrialEnterBattleDataOrBuilder>(
                getData(),
                getParentForChildren(),
                isClean());
        data_ = null;
      }
      return dataBuilder_;
    }

    private xddq.pb.BodyTrialSelectBuffInfo selectBuffData_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.BodyTrialSelectBuffInfo, xddq.pb.BodyTrialSelectBuffInfo.Builder, xddq.pb.BodyTrialSelectBuffInfoOrBuilder> selectBuffDataBuilder_;
    /**
     * <code>optional .xddq.pb.BodyTrialSelectBuffInfo selectBuffData = 3;</code>
     * @return Whether the selectBuffData field is set.
     */
    public boolean hasSelectBuffData() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional .xddq.pb.BodyTrialSelectBuffInfo selectBuffData = 3;</code>
     * @return The selectBuffData.
     */
    public xddq.pb.BodyTrialSelectBuffInfo getSelectBuffData() {
      if (selectBuffDataBuilder_ == null) {
        return selectBuffData_ == null ? xddq.pb.BodyTrialSelectBuffInfo.getDefaultInstance() : selectBuffData_;
      } else {
        return selectBuffDataBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.BodyTrialSelectBuffInfo selectBuffData = 3;</code>
     */
    public Builder setSelectBuffData(xddq.pb.BodyTrialSelectBuffInfo value) {
      if (selectBuffDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        selectBuffData_ = value;
      } else {
        selectBuffDataBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.BodyTrialSelectBuffInfo selectBuffData = 3;</code>
     */
    public Builder setSelectBuffData(
        xddq.pb.BodyTrialSelectBuffInfo.Builder builderForValue) {
      if (selectBuffDataBuilder_ == null) {
        selectBuffData_ = builderForValue.build();
      } else {
        selectBuffDataBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.BodyTrialSelectBuffInfo selectBuffData = 3;</code>
     */
    public Builder mergeSelectBuffData(xddq.pb.BodyTrialSelectBuffInfo value) {
      if (selectBuffDataBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0) &&
          selectBuffData_ != null &&
          selectBuffData_ != xddq.pb.BodyTrialSelectBuffInfo.getDefaultInstance()) {
          getSelectBuffDataBuilder().mergeFrom(value);
        } else {
          selectBuffData_ = value;
        }
      } else {
        selectBuffDataBuilder_.mergeFrom(value);
      }
      if (selectBuffData_ != null) {
        bitField0_ |= 0x00000004;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.BodyTrialSelectBuffInfo selectBuffData = 3;</code>
     */
    public Builder clearSelectBuffData() {
      bitField0_ = (bitField0_ & ~0x00000004);
      selectBuffData_ = null;
      if (selectBuffDataBuilder_ != null) {
        selectBuffDataBuilder_.dispose();
        selectBuffDataBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.BodyTrialSelectBuffInfo selectBuffData = 3;</code>
     */
    public xddq.pb.BodyTrialSelectBuffInfo.Builder getSelectBuffDataBuilder() {
      bitField0_ |= 0x00000004;
      onChanged();
      return internalGetSelectBuffDataFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.BodyTrialSelectBuffInfo selectBuffData = 3;</code>
     */
    public xddq.pb.BodyTrialSelectBuffInfoOrBuilder getSelectBuffDataOrBuilder() {
      if (selectBuffDataBuilder_ != null) {
        return selectBuffDataBuilder_.getMessageOrBuilder();
      } else {
        return selectBuffData_ == null ?
            xddq.pb.BodyTrialSelectBuffInfo.getDefaultInstance() : selectBuffData_;
      }
    }
    /**
     * <code>optional .xddq.pb.BodyTrialSelectBuffInfo selectBuffData = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.BodyTrialSelectBuffInfo, xddq.pb.BodyTrialSelectBuffInfo.Builder, xddq.pb.BodyTrialSelectBuffInfoOrBuilder> 
        internalGetSelectBuffDataFieldBuilder() {
      if (selectBuffDataBuilder_ == null) {
        selectBuffDataBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.BodyTrialSelectBuffInfo, xddq.pb.BodyTrialSelectBuffInfo.Builder, xddq.pb.BodyTrialSelectBuffInfoOrBuilder>(
                getSelectBuffData(),
                getParentForChildren(),
                isClean());
        selectBuffData_ = null;
      }
      return selectBuffDataBuilder_;
    }

    private com.google.protobuf.Internal.LongList robotPlayerIds_ = emptyLongList();
    private void ensureRobotPlayerIdsIsMutable() {
      if (!robotPlayerIds_.isModifiable()) {
        robotPlayerIds_ = makeMutableCopy(robotPlayerIds_);
      }
      bitField0_ |= 0x00000008;
    }
    /**
     * <code>repeated int64 robotPlayerIds = 4;</code>
     * @return A list containing the robotPlayerIds.
     */
    public java.util.List<java.lang.Long>
        getRobotPlayerIdsList() {
      robotPlayerIds_.makeImmutable();
      return robotPlayerIds_;
    }
    /**
     * <code>repeated int64 robotPlayerIds = 4;</code>
     * @return The count of robotPlayerIds.
     */
    public int getRobotPlayerIdsCount() {
      return robotPlayerIds_.size();
    }
    /**
     * <code>repeated int64 robotPlayerIds = 4;</code>
     * @param index The index of the element to return.
     * @return The robotPlayerIds at the given index.
     */
    public long getRobotPlayerIds(int index) {
      return robotPlayerIds_.getLong(index);
    }
    /**
     * <code>repeated int64 robotPlayerIds = 4;</code>
     * @param index The index to set the value at.
     * @param value The robotPlayerIds to set.
     * @return This builder for chaining.
     */
    public Builder setRobotPlayerIds(
        int index, long value) {

      ensureRobotPlayerIdsIsMutable();
      robotPlayerIds_.setLong(index, value);
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int64 robotPlayerIds = 4;</code>
     * @param value The robotPlayerIds to add.
     * @return This builder for chaining.
     */
    public Builder addRobotPlayerIds(long value) {

      ensureRobotPlayerIdsIsMutable();
      robotPlayerIds_.addLong(value);
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int64 robotPlayerIds = 4;</code>
     * @param values The robotPlayerIds to add.
     * @return This builder for chaining.
     */
    public Builder addAllRobotPlayerIds(
        java.lang.Iterable<? extends java.lang.Long> values) {
      ensureRobotPlayerIdsIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, robotPlayerIds_);
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int64 robotPlayerIds = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearRobotPlayerIds() {
      robotPlayerIds_ = emptyLongList();
      bitField0_ = (bitField0_ & ~0x00000008);
      onChanged();
      return this;
    }

    private int curFloorId_ ;
    /**
     * <code>optional int32 curFloorId = 5;</code>
     * @return Whether the curFloorId field is set.
     */
    @java.lang.Override
    public boolean hasCurFloorId() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional int32 curFloorId = 5;</code>
     * @return The curFloorId.
     */
    @java.lang.Override
    public int getCurFloorId() {
      return curFloorId_;
    }
    /**
     * <code>optional int32 curFloorId = 5;</code>
     * @param value The curFloorId to set.
     * @return This builder for chaining.
     */
    public Builder setCurFloorId(int value) {

      curFloorId_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 curFloorId = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearCurFloorId() {
      bitField0_ = (bitField0_ & ~0x00000010);
      curFloorId_ = 0;
      onChanged();
      return this;
    }

    private boolean needSendBuffReq_ ;
    /**
     * <code>optional bool needSendBuffReq = 6;</code>
     * @return Whether the needSendBuffReq field is set.
     */
    @java.lang.Override
    public boolean hasNeedSendBuffReq() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional bool needSendBuffReq = 6;</code>
     * @return The needSendBuffReq.
     */
    @java.lang.Override
    public boolean getNeedSendBuffReq() {
      return needSendBuffReq_;
    }
    /**
     * <code>optional bool needSendBuffReq = 6;</code>
     * @param value The needSendBuffReq to set.
     * @return This builder for chaining.
     */
    public Builder setNeedSendBuffReq(boolean value) {

      needSendBuffReq_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>optional bool needSendBuffReq = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearNeedSendBuffReq() {
      bitField0_ = (bitField0_ & ~0x00000020);
      needSendBuffReq_ = false;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.BodyTrialSeparationSimpleData> separationData_ =
      java.util.Collections.emptyList();
    private void ensureSeparationDataIsMutable() {
      if (!((bitField0_ & 0x00000040) != 0)) {
        separationData_ = new java.util.ArrayList<xddq.pb.BodyTrialSeparationSimpleData>(separationData_);
        bitField0_ |= 0x00000040;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.BodyTrialSeparationSimpleData, xddq.pb.BodyTrialSeparationSimpleData.Builder, xddq.pb.BodyTrialSeparationSimpleDataOrBuilder> separationDataBuilder_;

    /**
     * <code>repeated .xddq.pb.BodyTrialSeparationSimpleData separationData = 7;</code>
     */
    public java.util.List<xddq.pb.BodyTrialSeparationSimpleData> getSeparationDataList() {
      if (separationDataBuilder_ == null) {
        return java.util.Collections.unmodifiableList(separationData_);
      } else {
        return separationDataBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.BodyTrialSeparationSimpleData separationData = 7;</code>
     */
    public int getSeparationDataCount() {
      if (separationDataBuilder_ == null) {
        return separationData_.size();
      } else {
        return separationDataBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.BodyTrialSeparationSimpleData separationData = 7;</code>
     */
    public xddq.pb.BodyTrialSeparationSimpleData getSeparationData(int index) {
      if (separationDataBuilder_ == null) {
        return separationData_.get(index);
      } else {
        return separationDataBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.BodyTrialSeparationSimpleData separationData = 7;</code>
     */
    public Builder setSeparationData(
        int index, xddq.pb.BodyTrialSeparationSimpleData value) {
      if (separationDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureSeparationDataIsMutable();
        separationData_.set(index, value);
        onChanged();
      } else {
        separationDataBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.BodyTrialSeparationSimpleData separationData = 7;</code>
     */
    public Builder setSeparationData(
        int index, xddq.pb.BodyTrialSeparationSimpleData.Builder builderForValue) {
      if (separationDataBuilder_ == null) {
        ensureSeparationDataIsMutable();
        separationData_.set(index, builderForValue.build());
        onChanged();
      } else {
        separationDataBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.BodyTrialSeparationSimpleData separationData = 7;</code>
     */
    public Builder addSeparationData(xddq.pb.BodyTrialSeparationSimpleData value) {
      if (separationDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureSeparationDataIsMutable();
        separationData_.add(value);
        onChanged();
      } else {
        separationDataBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.BodyTrialSeparationSimpleData separationData = 7;</code>
     */
    public Builder addSeparationData(
        int index, xddq.pb.BodyTrialSeparationSimpleData value) {
      if (separationDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureSeparationDataIsMutable();
        separationData_.add(index, value);
        onChanged();
      } else {
        separationDataBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.BodyTrialSeparationSimpleData separationData = 7;</code>
     */
    public Builder addSeparationData(
        xddq.pb.BodyTrialSeparationSimpleData.Builder builderForValue) {
      if (separationDataBuilder_ == null) {
        ensureSeparationDataIsMutable();
        separationData_.add(builderForValue.build());
        onChanged();
      } else {
        separationDataBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.BodyTrialSeparationSimpleData separationData = 7;</code>
     */
    public Builder addSeparationData(
        int index, xddq.pb.BodyTrialSeparationSimpleData.Builder builderForValue) {
      if (separationDataBuilder_ == null) {
        ensureSeparationDataIsMutable();
        separationData_.add(index, builderForValue.build());
        onChanged();
      } else {
        separationDataBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.BodyTrialSeparationSimpleData separationData = 7;</code>
     */
    public Builder addAllSeparationData(
        java.lang.Iterable<? extends xddq.pb.BodyTrialSeparationSimpleData> values) {
      if (separationDataBuilder_ == null) {
        ensureSeparationDataIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, separationData_);
        onChanged();
      } else {
        separationDataBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.BodyTrialSeparationSimpleData separationData = 7;</code>
     */
    public Builder clearSeparationData() {
      if (separationDataBuilder_ == null) {
        separationData_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000040);
        onChanged();
      } else {
        separationDataBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.BodyTrialSeparationSimpleData separationData = 7;</code>
     */
    public Builder removeSeparationData(int index) {
      if (separationDataBuilder_ == null) {
        ensureSeparationDataIsMutable();
        separationData_.remove(index);
        onChanged();
      } else {
        separationDataBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.BodyTrialSeparationSimpleData separationData = 7;</code>
     */
    public xddq.pb.BodyTrialSeparationSimpleData.Builder getSeparationDataBuilder(
        int index) {
      return internalGetSeparationDataFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.BodyTrialSeparationSimpleData separationData = 7;</code>
     */
    public xddq.pb.BodyTrialSeparationSimpleDataOrBuilder getSeparationDataOrBuilder(
        int index) {
      if (separationDataBuilder_ == null) {
        return separationData_.get(index);  } else {
        return separationDataBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.BodyTrialSeparationSimpleData separationData = 7;</code>
     */
    public java.util.List<? extends xddq.pb.BodyTrialSeparationSimpleDataOrBuilder> 
         getSeparationDataOrBuilderList() {
      if (separationDataBuilder_ != null) {
        return separationDataBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(separationData_);
      }
    }
    /**
     * <code>repeated .xddq.pb.BodyTrialSeparationSimpleData separationData = 7;</code>
     */
    public xddq.pb.BodyTrialSeparationSimpleData.Builder addSeparationDataBuilder() {
      return internalGetSeparationDataFieldBuilder().addBuilder(
          xddq.pb.BodyTrialSeparationSimpleData.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.BodyTrialSeparationSimpleData separationData = 7;</code>
     */
    public xddq.pb.BodyTrialSeparationSimpleData.Builder addSeparationDataBuilder(
        int index) {
      return internalGetSeparationDataFieldBuilder().addBuilder(
          index, xddq.pb.BodyTrialSeparationSimpleData.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.BodyTrialSeparationSimpleData separationData = 7;</code>
     */
    public java.util.List<xddq.pb.BodyTrialSeparationSimpleData.Builder> 
         getSeparationDataBuilderList() {
      return internalGetSeparationDataFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.BodyTrialSeparationSimpleData, xddq.pb.BodyTrialSeparationSimpleData.Builder, xddq.pb.BodyTrialSeparationSimpleDataOrBuilder> 
        internalGetSeparationDataFieldBuilder() {
      if (separationDataBuilder_ == null) {
        separationDataBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.BodyTrialSeparationSimpleData, xddq.pb.BodyTrialSeparationSimpleData.Builder, xddq.pb.BodyTrialSeparationSimpleDataOrBuilder>(
                separationData_,
                ((bitField0_ & 0x00000040) != 0),
                getParentForChildren(),
                isClean());
        separationData_ = null;
      }
      return separationDataBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.BodyTrialEnterBattleViewResp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.BodyTrialEnterBattleViewResp)
  private static final xddq.pb.BodyTrialEnterBattleViewResp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.BodyTrialEnterBattleViewResp();
  }

  public static xddq.pb.BodyTrialEnterBattleViewResp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<BodyTrialEnterBattleViewResp>
      PARSER = new com.google.protobuf.AbstractParser<BodyTrialEnterBattleViewResp>() {
    @java.lang.Override
    public BodyTrialEnterBattleViewResp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<BodyTrialEnterBattleViewResp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<BodyTrialEnterBattleViewResp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.BodyTrialEnterBattleViewResp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

