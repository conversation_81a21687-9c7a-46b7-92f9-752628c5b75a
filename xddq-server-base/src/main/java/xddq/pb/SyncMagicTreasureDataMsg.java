// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.SyncMagicTreasureDataMsg}
 */
public final class SyncMagicTreasureDataMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.SyncMagicTreasureDataMsg)
    SyncMagicTreasureDataMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      SyncMagicTreasureDataMsg.class.getName());
  }
  // Use SyncMagicTreasureDataMsg.newBuilder() to construct.
  private SyncMagicTreasureDataMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private SyncMagicTreasureDataMsg() {
    magicTreasureData_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_SyncMagicTreasureDataMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_SyncMagicTreasureDataMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.SyncMagicTreasureDataMsg.class, xddq.pb.SyncMagicTreasureDataMsg.Builder.class);
  }

  private int bitField0_;
  public static final int MAGICTREASUREDATA_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.MagicTreasureDataMsg> magicTreasureData_;
  /**
   * <code>repeated .xddq.pb.MagicTreasureDataMsg magicTreasureData = 1;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.MagicTreasureDataMsg> getMagicTreasureDataList() {
    return magicTreasureData_;
  }
  /**
   * <code>repeated .xddq.pb.MagicTreasureDataMsg magicTreasureData = 1;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.MagicTreasureDataMsgOrBuilder> 
      getMagicTreasureDataOrBuilderList() {
    return magicTreasureData_;
  }
  /**
   * <code>repeated .xddq.pb.MagicTreasureDataMsg magicTreasureData = 1;</code>
   */
  @java.lang.Override
  public int getMagicTreasureDataCount() {
    return magicTreasureData_.size();
  }
  /**
   * <code>repeated .xddq.pb.MagicTreasureDataMsg magicTreasureData = 1;</code>
   */
  @java.lang.Override
  public xddq.pb.MagicTreasureDataMsg getMagicTreasureData(int index) {
    return magicTreasureData_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.MagicTreasureDataMsg magicTreasureData = 1;</code>
   */
  @java.lang.Override
  public xddq.pb.MagicTreasureDataMsgOrBuilder getMagicTreasureDataOrBuilder(
      int index) {
    return magicTreasureData_.get(index);
  }

  public static final int ISCOMPLETE_FIELD_NUMBER = 2;
  private boolean isComplete_ = false;
  /**
   * <code>optional bool isComplete = 2;</code>
   * @return Whether the isComplete field is set.
   */
  @java.lang.Override
  public boolean hasIsComplete() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional bool isComplete = 2;</code>
   * @return The isComplete.
   */
  @java.lang.Override
  public boolean getIsComplete() {
    return isComplete_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    for (int i = 0; i < getMagicTreasureDataCount(); i++) {
      if (!getMagicTreasureData(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    for (int i = 0; i < magicTreasureData_.size(); i++) {
      output.writeMessage(1, magicTreasureData_.get(i));
    }
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeBool(2, isComplete_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    for (int i = 0; i < magicTreasureData_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, magicTreasureData_.get(i));
    }
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(2, isComplete_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.SyncMagicTreasureDataMsg)) {
      return super.equals(obj);
    }
    xddq.pb.SyncMagicTreasureDataMsg other = (xddq.pb.SyncMagicTreasureDataMsg) obj;

    if (!getMagicTreasureDataList()
        .equals(other.getMagicTreasureDataList())) return false;
    if (hasIsComplete() != other.hasIsComplete()) return false;
    if (hasIsComplete()) {
      if (getIsComplete()
          != other.getIsComplete()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (getMagicTreasureDataCount() > 0) {
      hash = (37 * hash) + MAGICTREASUREDATA_FIELD_NUMBER;
      hash = (53 * hash) + getMagicTreasureDataList().hashCode();
    }
    if (hasIsComplete()) {
      hash = (37 * hash) + ISCOMPLETE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getIsComplete());
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.SyncMagicTreasureDataMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.SyncMagicTreasureDataMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.SyncMagicTreasureDataMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.SyncMagicTreasureDataMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.SyncMagicTreasureDataMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.SyncMagicTreasureDataMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.SyncMagicTreasureDataMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.SyncMagicTreasureDataMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.SyncMagicTreasureDataMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.SyncMagicTreasureDataMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.SyncMagicTreasureDataMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.SyncMagicTreasureDataMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.SyncMagicTreasureDataMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.SyncMagicTreasureDataMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.SyncMagicTreasureDataMsg)
      xddq.pb.SyncMagicTreasureDataMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_SyncMagicTreasureDataMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_SyncMagicTreasureDataMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.SyncMagicTreasureDataMsg.class, xddq.pb.SyncMagicTreasureDataMsg.Builder.class);
    }

    // Construct using xddq.pb.SyncMagicTreasureDataMsg.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      if (magicTreasureDataBuilder_ == null) {
        magicTreasureData_ = java.util.Collections.emptyList();
      } else {
        magicTreasureData_ = null;
        magicTreasureDataBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000001);
      isComplete_ = false;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_SyncMagicTreasureDataMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.SyncMagicTreasureDataMsg getDefaultInstanceForType() {
      return xddq.pb.SyncMagicTreasureDataMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.SyncMagicTreasureDataMsg build() {
      xddq.pb.SyncMagicTreasureDataMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.SyncMagicTreasureDataMsg buildPartial() {
      xddq.pb.SyncMagicTreasureDataMsg result = new xddq.pb.SyncMagicTreasureDataMsg(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.SyncMagicTreasureDataMsg result) {
      if (magicTreasureDataBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          magicTreasureData_ = java.util.Collections.unmodifiableList(magicTreasureData_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.magicTreasureData_ = magicTreasureData_;
      } else {
        result.magicTreasureData_ = magicTreasureDataBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.SyncMagicTreasureDataMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.isComplete_ = isComplete_;
        to_bitField0_ |= 0x00000001;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.SyncMagicTreasureDataMsg) {
        return mergeFrom((xddq.pb.SyncMagicTreasureDataMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.SyncMagicTreasureDataMsg other) {
      if (other == xddq.pb.SyncMagicTreasureDataMsg.getDefaultInstance()) return this;
      if (magicTreasureDataBuilder_ == null) {
        if (!other.magicTreasureData_.isEmpty()) {
          if (magicTreasureData_.isEmpty()) {
            magicTreasureData_ = other.magicTreasureData_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureMagicTreasureDataIsMutable();
            magicTreasureData_.addAll(other.magicTreasureData_);
          }
          onChanged();
        }
      } else {
        if (!other.magicTreasureData_.isEmpty()) {
          if (magicTreasureDataBuilder_.isEmpty()) {
            magicTreasureDataBuilder_.dispose();
            magicTreasureDataBuilder_ = null;
            magicTreasureData_ = other.magicTreasureData_;
            bitField0_ = (bitField0_ & ~0x00000001);
            magicTreasureDataBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetMagicTreasureDataFieldBuilder() : null;
          } else {
            magicTreasureDataBuilder_.addAllMessages(other.magicTreasureData_);
          }
        }
      }
      if (other.hasIsComplete()) {
        setIsComplete(other.getIsComplete());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      for (int i = 0; i < getMagicTreasureDataCount(); i++) {
        if (!getMagicTreasureData(i).isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              xddq.pb.MagicTreasureDataMsg m =
                  input.readMessage(
                      xddq.pb.MagicTreasureDataMsg.parser(),
                      extensionRegistry);
              if (magicTreasureDataBuilder_ == null) {
                ensureMagicTreasureDataIsMutable();
                magicTreasureData_.add(m);
              } else {
                magicTreasureDataBuilder_.addMessage(m);
              }
              break;
            } // case 10
            case 16: {
              isComplete_ = input.readBool();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.util.List<xddq.pb.MagicTreasureDataMsg> magicTreasureData_ =
      java.util.Collections.emptyList();
    private void ensureMagicTreasureDataIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        magicTreasureData_ = new java.util.ArrayList<xddq.pb.MagicTreasureDataMsg>(magicTreasureData_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.MagicTreasureDataMsg, xddq.pb.MagicTreasureDataMsg.Builder, xddq.pb.MagicTreasureDataMsgOrBuilder> magicTreasureDataBuilder_;

    /**
     * <code>repeated .xddq.pb.MagicTreasureDataMsg magicTreasureData = 1;</code>
     */
    public java.util.List<xddq.pb.MagicTreasureDataMsg> getMagicTreasureDataList() {
      if (magicTreasureDataBuilder_ == null) {
        return java.util.Collections.unmodifiableList(magicTreasureData_);
      } else {
        return magicTreasureDataBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.MagicTreasureDataMsg magicTreasureData = 1;</code>
     */
    public int getMagicTreasureDataCount() {
      if (magicTreasureDataBuilder_ == null) {
        return magicTreasureData_.size();
      } else {
        return magicTreasureDataBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.MagicTreasureDataMsg magicTreasureData = 1;</code>
     */
    public xddq.pb.MagicTreasureDataMsg getMagicTreasureData(int index) {
      if (magicTreasureDataBuilder_ == null) {
        return magicTreasureData_.get(index);
      } else {
        return magicTreasureDataBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.MagicTreasureDataMsg magicTreasureData = 1;</code>
     */
    public Builder setMagicTreasureData(
        int index, xddq.pb.MagicTreasureDataMsg value) {
      if (magicTreasureDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMagicTreasureDataIsMutable();
        magicTreasureData_.set(index, value);
        onChanged();
      } else {
        magicTreasureDataBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MagicTreasureDataMsg magicTreasureData = 1;</code>
     */
    public Builder setMagicTreasureData(
        int index, xddq.pb.MagicTreasureDataMsg.Builder builderForValue) {
      if (magicTreasureDataBuilder_ == null) {
        ensureMagicTreasureDataIsMutable();
        magicTreasureData_.set(index, builderForValue.build());
        onChanged();
      } else {
        magicTreasureDataBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MagicTreasureDataMsg magicTreasureData = 1;</code>
     */
    public Builder addMagicTreasureData(xddq.pb.MagicTreasureDataMsg value) {
      if (magicTreasureDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMagicTreasureDataIsMutable();
        magicTreasureData_.add(value);
        onChanged();
      } else {
        magicTreasureDataBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MagicTreasureDataMsg magicTreasureData = 1;</code>
     */
    public Builder addMagicTreasureData(
        int index, xddq.pb.MagicTreasureDataMsg value) {
      if (magicTreasureDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMagicTreasureDataIsMutable();
        magicTreasureData_.add(index, value);
        onChanged();
      } else {
        magicTreasureDataBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MagicTreasureDataMsg magicTreasureData = 1;</code>
     */
    public Builder addMagicTreasureData(
        xddq.pb.MagicTreasureDataMsg.Builder builderForValue) {
      if (magicTreasureDataBuilder_ == null) {
        ensureMagicTreasureDataIsMutable();
        magicTreasureData_.add(builderForValue.build());
        onChanged();
      } else {
        magicTreasureDataBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MagicTreasureDataMsg magicTreasureData = 1;</code>
     */
    public Builder addMagicTreasureData(
        int index, xddq.pb.MagicTreasureDataMsg.Builder builderForValue) {
      if (magicTreasureDataBuilder_ == null) {
        ensureMagicTreasureDataIsMutable();
        magicTreasureData_.add(index, builderForValue.build());
        onChanged();
      } else {
        magicTreasureDataBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MagicTreasureDataMsg magicTreasureData = 1;</code>
     */
    public Builder addAllMagicTreasureData(
        java.lang.Iterable<? extends xddq.pb.MagicTreasureDataMsg> values) {
      if (magicTreasureDataBuilder_ == null) {
        ensureMagicTreasureDataIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, magicTreasureData_);
        onChanged();
      } else {
        magicTreasureDataBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MagicTreasureDataMsg magicTreasureData = 1;</code>
     */
    public Builder clearMagicTreasureData() {
      if (magicTreasureDataBuilder_ == null) {
        magicTreasureData_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        magicTreasureDataBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MagicTreasureDataMsg magicTreasureData = 1;</code>
     */
    public Builder removeMagicTreasureData(int index) {
      if (magicTreasureDataBuilder_ == null) {
        ensureMagicTreasureDataIsMutable();
        magicTreasureData_.remove(index);
        onChanged();
      } else {
        magicTreasureDataBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MagicTreasureDataMsg magicTreasureData = 1;</code>
     */
    public xddq.pb.MagicTreasureDataMsg.Builder getMagicTreasureDataBuilder(
        int index) {
      return internalGetMagicTreasureDataFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.MagicTreasureDataMsg magicTreasureData = 1;</code>
     */
    public xddq.pb.MagicTreasureDataMsgOrBuilder getMagicTreasureDataOrBuilder(
        int index) {
      if (magicTreasureDataBuilder_ == null) {
        return magicTreasureData_.get(index);  } else {
        return magicTreasureDataBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.MagicTreasureDataMsg magicTreasureData = 1;</code>
     */
    public java.util.List<? extends xddq.pb.MagicTreasureDataMsgOrBuilder> 
         getMagicTreasureDataOrBuilderList() {
      if (magicTreasureDataBuilder_ != null) {
        return magicTreasureDataBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(magicTreasureData_);
      }
    }
    /**
     * <code>repeated .xddq.pb.MagicTreasureDataMsg magicTreasureData = 1;</code>
     */
    public xddq.pb.MagicTreasureDataMsg.Builder addMagicTreasureDataBuilder() {
      return internalGetMagicTreasureDataFieldBuilder().addBuilder(
          xddq.pb.MagicTreasureDataMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.MagicTreasureDataMsg magicTreasureData = 1;</code>
     */
    public xddq.pb.MagicTreasureDataMsg.Builder addMagicTreasureDataBuilder(
        int index) {
      return internalGetMagicTreasureDataFieldBuilder().addBuilder(
          index, xddq.pb.MagicTreasureDataMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.MagicTreasureDataMsg magicTreasureData = 1;</code>
     */
    public java.util.List<xddq.pb.MagicTreasureDataMsg.Builder> 
         getMagicTreasureDataBuilderList() {
      return internalGetMagicTreasureDataFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.MagicTreasureDataMsg, xddq.pb.MagicTreasureDataMsg.Builder, xddq.pb.MagicTreasureDataMsgOrBuilder> 
        internalGetMagicTreasureDataFieldBuilder() {
      if (magicTreasureDataBuilder_ == null) {
        magicTreasureDataBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.MagicTreasureDataMsg, xddq.pb.MagicTreasureDataMsg.Builder, xddq.pb.MagicTreasureDataMsgOrBuilder>(
                magicTreasureData_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        magicTreasureData_ = null;
      }
      return magicTreasureDataBuilder_;
    }

    private boolean isComplete_ ;
    /**
     * <code>optional bool isComplete = 2;</code>
     * @return Whether the isComplete field is set.
     */
    @java.lang.Override
    public boolean hasIsComplete() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional bool isComplete = 2;</code>
     * @return The isComplete.
     */
    @java.lang.Override
    public boolean getIsComplete() {
      return isComplete_;
    }
    /**
     * <code>optional bool isComplete = 2;</code>
     * @param value The isComplete to set.
     * @return This builder for chaining.
     */
    public Builder setIsComplete(boolean value) {

      isComplete_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional bool isComplete = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearIsComplete() {
      bitField0_ = (bitField0_ & ~0x00000002);
      isComplete_ = false;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.SyncMagicTreasureDataMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.SyncMagicTreasureDataMsg)
  private static final xddq.pb.SyncMagicTreasureDataMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.SyncMagicTreasureDataMsg();
  }

  public static xddq.pb.SyncMagicTreasureDataMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<SyncMagicTreasureDataMsg>
      PARSER = new com.google.protobuf.AbstractParser<SyncMagicTreasureDataMsg>() {
    @java.lang.Override
    public SyncMagicTreasureDataMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<SyncMagicTreasureDataMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<SyncMagicTreasureDataMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.SyncMagicTreasureDataMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

