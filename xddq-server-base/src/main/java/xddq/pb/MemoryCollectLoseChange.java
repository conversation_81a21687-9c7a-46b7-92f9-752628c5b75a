// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.MemoryCollectLoseChange}
 */
public final class MemoryCollectLoseChange extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.MemoryCollectLoseChange)
    MemoryCollectLoseChangeOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      MemoryCollectLoseChange.class.getName());
  }
  // Use MemoryCollectLoseChange.newBuilder() to construct.
  private MemoryCollectLoseChange(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private MemoryCollectLoseChange() {
    loseChangeInfo_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_MemoryCollectLoseChange_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_MemoryCollectLoseChange_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.MemoryCollectLoseChange.class, xddq.pb.MemoryCollectLoseChange.Builder.class);
  }

  public static final int LOSECHANGEINFO_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.MemoryCollectLoseChangeInfo> loseChangeInfo_;
  /**
   * <code>repeated .xddq.pb.MemoryCollectLoseChangeInfo loseChangeInfo = 1;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.MemoryCollectLoseChangeInfo> getLoseChangeInfoList() {
    return loseChangeInfo_;
  }
  /**
   * <code>repeated .xddq.pb.MemoryCollectLoseChangeInfo loseChangeInfo = 1;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.MemoryCollectLoseChangeInfoOrBuilder> 
      getLoseChangeInfoOrBuilderList() {
    return loseChangeInfo_;
  }
  /**
   * <code>repeated .xddq.pb.MemoryCollectLoseChangeInfo loseChangeInfo = 1;</code>
   */
  @java.lang.Override
  public int getLoseChangeInfoCount() {
    return loseChangeInfo_.size();
  }
  /**
   * <code>repeated .xddq.pb.MemoryCollectLoseChangeInfo loseChangeInfo = 1;</code>
   */
  @java.lang.Override
  public xddq.pb.MemoryCollectLoseChangeInfo getLoseChangeInfo(int index) {
    return loseChangeInfo_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.MemoryCollectLoseChangeInfo loseChangeInfo = 1;</code>
   */
  @java.lang.Override
  public xddq.pb.MemoryCollectLoseChangeInfoOrBuilder getLoseChangeInfoOrBuilder(
      int index) {
    return loseChangeInfo_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    for (int i = 0; i < loseChangeInfo_.size(); i++) {
      output.writeMessage(1, loseChangeInfo_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    for (int i = 0; i < loseChangeInfo_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, loseChangeInfo_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.MemoryCollectLoseChange)) {
      return super.equals(obj);
    }
    xddq.pb.MemoryCollectLoseChange other = (xddq.pb.MemoryCollectLoseChange) obj;

    if (!getLoseChangeInfoList()
        .equals(other.getLoseChangeInfoList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (getLoseChangeInfoCount() > 0) {
      hash = (37 * hash) + LOSECHANGEINFO_FIELD_NUMBER;
      hash = (53 * hash) + getLoseChangeInfoList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.MemoryCollectLoseChange parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.MemoryCollectLoseChange parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.MemoryCollectLoseChange parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.MemoryCollectLoseChange parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.MemoryCollectLoseChange parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.MemoryCollectLoseChange parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.MemoryCollectLoseChange parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.MemoryCollectLoseChange parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.MemoryCollectLoseChange parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.MemoryCollectLoseChange parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.MemoryCollectLoseChange parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.MemoryCollectLoseChange parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.MemoryCollectLoseChange prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.MemoryCollectLoseChange}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.MemoryCollectLoseChange)
      xddq.pb.MemoryCollectLoseChangeOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_MemoryCollectLoseChange_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_MemoryCollectLoseChange_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.MemoryCollectLoseChange.class, xddq.pb.MemoryCollectLoseChange.Builder.class);
    }

    // Construct using xddq.pb.MemoryCollectLoseChange.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      if (loseChangeInfoBuilder_ == null) {
        loseChangeInfo_ = java.util.Collections.emptyList();
      } else {
        loseChangeInfo_ = null;
        loseChangeInfoBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000001);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_MemoryCollectLoseChange_descriptor;
    }

    @java.lang.Override
    public xddq.pb.MemoryCollectLoseChange getDefaultInstanceForType() {
      return xddq.pb.MemoryCollectLoseChange.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.MemoryCollectLoseChange build() {
      xddq.pb.MemoryCollectLoseChange result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.MemoryCollectLoseChange buildPartial() {
      xddq.pb.MemoryCollectLoseChange result = new xddq.pb.MemoryCollectLoseChange(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.MemoryCollectLoseChange result) {
      if (loseChangeInfoBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          loseChangeInfo_ = java.util.Collections.unmodifiableList(loseChangeInfo_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.loseChangeInfo_ = loseChangeInfo_;
      } else {
        result.loseChangeInfo_ = loseChangeInfoBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.MemoryCollectLoseChange result) {
      int from_bitField0_ = bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.MemoryCollectLoseChange) {
        return mergeFrom((xddq.pb.MemoryCollectLoseChange)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.MemoryCollectLoseChange other) {
      if (other == xddq.pb.MemoryCollectLoseChange.getDefaultInstance()) return this;
      if (loseChangeInfoBuilder_ == null) {
        if (!other.loseChangeInfo_.isEmpty()) {
          if (loseChangeInfo_.isEmpty()) {
            loseChangeInfo_ = other.loseChangeInfo_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureLoseChangeInfoIsMutable();
            loseChangeInfo_.addAll(other.loseChangeInfo_);
          }
          onChanged();
        }
      } else {
        if (!other.loseChangeInfo_.isEmpty()) {
          if (loseChangeInfoBuilder_.isEmpty()) {
            loseChangeInfoBuilder_.dispose();
            loseChangeInfoBuilder_ = null;
            loseChangeInfo_ = other.loseChangeInfo_;
            bitField0_ = (bitField0_ & ~0x00000001);
            loseChangeInfoBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetLoseChangeInfoFieldBuilder() : null;
          } else {
            loseChangeInfoBuilder_.addAllMessages(other.loseChangeInfo_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              xddq.pb.MemoryCollectLoseChangeInfo m =
                  input.readMessage(
                      xddq.pb.MemoryCollectLoseChangeInfo.parser(),
                      extensionRegistry);
              if (loseChangeInfoBuilder_ == null) {
                ensureLoseChangeInfoIsMutable();
                loseChangeInfo_.add(m);
              } else {
                loseChangeInfoBuilder_.addMessage(m);
              }
              break;
            } // case 10
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.util.List<xddq.pb.MemoryCollectLoseChangeInfo> loseChangeInfo_ =
      java.util.Collections.emptyList();
    private void ensureLoseChangeInfoIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        loseChangeInfo_ = new java.util.ArrayList<xddq.pb.MemoryCollectLoseChangeInfo>(loseChangeInfo_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.MemoryCollectLoseChangeInfo, xddq.pb.MemoryCollectLoseChangeInfo.Builder, xddq.pb.MemoryCollectLoseChangeInfoOrBuilder> loseChangeInfoBuilder_;

    /**
     * <code>repeated .xddq.pb.MemoryCollectLoseChangeInfo loseChangeInfo = 1;</code>
     */
    public java.util.List<xddq.pb.MemoryCollectLoseChangeInfo> getLoseChangeInfoList() {
      if (loseChangeInfoBuilder_ == null) {
        return java.util.Collections.unmodifiableList(loseChangeInfo_);
      } else {
        return loseChangeInfoBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.MemoryCollectLoseChangeInfo loseChangeInfo = 1;</code>
     */
    public int getLoseChangeInfoCount() {
      if (loseChangeInfoBuilder_ == null) {
        return loseChangeInfo_.size();
      } else {
        return loseChangeInfoBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.MemoryCollectLoseChangeInfo loseChangeInfo = 1;</code>
     */
    public xddq.pb.MemoryCollectLoseChangeInfo getLoseChangeInfo(int index) {
      if (loseChangeInfoBuilder_ == null) {
        return loseChangeInfo_.get(index);
      } else {
        return loseChangeInfoBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.MemoryCollectLoseChangeInfo loseChangeInfo = 1;</code>
     */
    public Builder setLoseChangeInfo(
        int index, xddq.pb.MemoryCollectLoseChangeInfo value) {
      if (loseChangeInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureLoseChangeInfoIsMutable();
        loseChangeInfo_.set(index, value);
        onChanged();
      } else {
        loseChangeInfoBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MemoryCollectLoseChangeInfo loseChangeInfo = 1;</code>
     */
    public Builder setLoseChangeInfo(
        int index, xddq.pb.MemoryCollectLoseChangeInfo.Builder builderForValue) {
      if (loseChangeInfoBuilder_ == null) {
        ensureLoseChangeInfoIsMutable();
        loseChangeInfo_.set(index, builderForValue.build());
        onChanged();
      } else {
        loseChangeInfoBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MemoryCollectLoseChangeInfo loseChangeInfo = 1;</code>
     */
    public Builder addLoseChangeInfo(xddq.pb.MemoryCollectLoseChangeInfo value) {
      if (loseChangeInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureLoseChangeInfoIsMutable();
        loseChangeInfo_.add(value);
        onChanged();
      } else {
        loseChangeInfoBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MemoryCollectLoseChangeInfo loseChangeInfo = 1;</code>
     */
    public Builder addLoseChangeInfo(
        int index, xddq.pb.MemoryCollectLoseChangeInfo value) {
      if (loseChangeInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureLoseChangeInfoIsMutable();
        loseChangeInfo_.add(index, value);
        onChanged();
      } else {
        loseChangeInfoBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MemoryCollectLoseChangeInfo loseChangeInfo = 1;</code>
     */
    public Builder addLoseChangeInfo(
        xddq.pb.MemoryCollectLoseChangeInfo.Builder builderForValue) {
      if (loseChangeInfoBuilder_ == null) {
        ensureLoseChangeInfoIsMutable();
        loseChangeInfo_.add(builderForValue.build());
        onChanged();
      } else {
        loseChangeInfoBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MemoryCollectLoseChangeInfo loseChangeInfo = 1;</code>
     */
    public Builder addLoseChangeInfo(
        int index, xddq.pb.MemoryCollectLoseChangeInfo.Builder builderForValue) {
      if (loseChangeInfoBuilder_ == null) {
        ensureLoseChangeInfoIsMutable();
        loseChangeInfo_.add(index, builderForValue.build());
        onChanged();
      } else {
        loseChangeInfoBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MemoryCollectLoseChangeInfo loseChangeInfo = 1;</code>
     */
    public Builder addAllLoseChangeInfo(
        java.lang.Iterable<? extends xddq.pb.MemoryCollectLoseChangeInfo> values) {
      if (loseChangeInfoBuilder_ == null) {
        ensureLoseChangeInfoIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, loseChangeInfo_);
        onChanged();
      } else {
        loseChangeInfoBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MemoryCollectLoseChangeInfo loseChangeInfo = 1;</code>
     */
    public Builder clearLoseChangeInfo() {
      if (loseChangeInfoBuilder_ == null) {
        loseChangeInfo_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        loseChangeInfoBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MemoryCollectLoseChangeInfo loseChangeInfo = 1;</code>
     */
    public Builder removeLoseChangeInfo(int index) {
      if (loseChangeInfoBuilder_ == null) {
        ensureLoseChangeInfoIsMutable();
        loseChangeInfo_.remove(index);
        onChanged();
      } else {
        loseChangeInfoBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MemoryCollectLoseChangeInfo loseChangeInfo = 1;</code>
     */
    public xddq.pb.MemoryCollectLoseChangeInfo.Builder getLoseChangeInfoBuilder(
        int index) {
      return internalGetLoseChangeInfoFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.MemoryCollectLoseChangeInfo loseChangeInfo = 1;</code>
     */
    public xddq.pb.MemoryCollectLoseChangeInfoOrBuilder getLoseChangeInfoOrBuilder(
        int index) {
      if (loseChangeInfoBuilder_ == null) {
        return loseChangeInfo_.get(index);  } else {
        return loseChangeInfoBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.MemoryCollectLoseChangeInfo loseChangeInfo = 1;</code>
     */
    public java.util.List<? extends xddq.pb.MemoryCollectLoseChangeInfoOrBuilder> 
         getLoseChangeInfoOrBuilderList() {
      if (loseChangeInfoBuilder_ != null) {
        return loseChangeInfoBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(loseChangeInfo_);
      }
    }
    /**
     * <code>repeated .xddq.pb.MemoryCollectLoseChangeInfo loseChangeInfo = 1;</code>
     */
    public xddq.pb.MemoryCollectLoseChangeInfo.Builder addLoseChangeInfoBuilder() {
      return internalGetLoseChangeInfoFieldBuilder().addBuilder(
          xddq.pb.MemoryCollectLoseChangeInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.MemoryCollectLoseChangeInfo loseChangeInfo = 1;</code>
     */
    public xddq.pb.MemoryCollectLoseChangeInfo.Builder addLoseChangeInfoBuilder(
        int index) {
      return internalGetLoseChangeInfoFieldBuilder().addBuilder(
          index, xddq.pb.MemoryCollectLoseChangeInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.MemoryCollectLoseChangeInfo loseChangeInfo = 1;</code>
     */
    public java.util.List<xddq.pb.MemoryCollectLoseChangeInfo.Builder> 
         getLoseChangeInfoBuilderList() {
      return internalGetLoseChangeInfoFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.MemoryCollectLoseChangeInfo, xddq.pb.MemoryCollectLoseChangeInfo.Builder, xddq.pb.MemoryCollectLoseChangeInfoOrBuilder> 
        internalGetLoseChangeInfoFieldBuilder() {
      if (loseChangeInfoBuilder_ == null) {
        loseChangeInfoBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.MemoryCollectLoseChangeInfo, xddq.pb.MemoryCollectLoseChangeInfo.Builder, xddq.pb.MemoryCollectLoseChangeInfoOrBuilder>(
                loseChangeInfo_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        loseChangeInfo_ = null;
      }
      return loseChangeInfoBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.MemoryCollectLoseChange)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.MemoryCollectLoseChange)
  private static final xddq.pb.MemoryCollectLoseChange DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.MemoryCollectLoseChange();
  }

  public static xddq.pb.MemoryCollectLoseChange getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<MemoryCollectLoseChange>
      PARSER = new com.google.protobuf.AbstractParser<MemoryCollectLoseChange>() {
    @java.lang.Override
    public MemoryCollectLoseChange parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<MemoryCollectLoseChange> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<MemoryCollectLoseChange> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.MemoryCollectLoseChange getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

