// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

public interface LawLooksDrawReqOrBuilder extends
    // @@protoc_insertion_point(interface_extends:xddq.pb.LawLooksDrawReq)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>required int32 drawTimes = 1;</code>
   * @return Whether the drawTimes field is set.
   */
  boolean hasDrawTimes();
  /**
   * <code>required int32 drawTimes = 1;</code>
   * @return The drawTimes.
   */
  int getDrawTimes();

  /**
   * <code>optional int32 drawType = 2;</code>
   * @return Whether the drawType field is set.
   */
  boolean hasDrawType();
  /**
   * <code>optional int32 drawType = 2;</code>
   * @return The drawType.
   */
  int getDrawType();
}
