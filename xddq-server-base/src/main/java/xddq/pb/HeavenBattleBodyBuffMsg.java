// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.HeavenBattleBodyBuffMsg}
 */
public final class HeavenBattleBodyBuffMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.HeavenBattleBodyBuffMsg)
    HeavenBattleBodyBuffMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      HeavenBattleBodyBuffMsg.class.getName());
  }
  // Use HeavenBattleBodyBuffMsg.newBuilder() to construct.
  private HeavenBattleBodyBuffMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private HeavenBattleBodyBuffMsg() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_HeavenBattleBodyBuffMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_HeavenBattleBodyBuffMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.HeavenBattleBodyBuffMsg.class, xddq.pb.HeavenBattleBodyBuffMsg.Builder.class);
  }

  private int bitField0_;
  public static final int BUFFID_FIELD_NUMBER = 1;
  private int buffId_ = 0;
  /**
   * <code>optional int32 buffId = 1;</code>
   * @return Whether the buffId field is set.
   */
  @java.lang.Override
  public boolean hasBuffId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int32 buffId = 1;</code>
   * @return The buffId.
   */
  @java.lang.Override
  public int getBuffId() {
    return buffId_;
  }

  public static final int BODYINDEX_FIELD_NUMBER = 2;
  private int bodyIndex_ = 0;
  /**
   * <code>optional int32 bodyIndex = 2;</code>
   * @return Whether the bodyIndex field is set.
   */
  @java.lang.Override
  public boolean hasBodyIndex() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int32 bodyIndex = 2;</code>
   * @return The bodyIndex.
   */
  @java.lang.Override
  public int getBodyIndex() {
    return bodyIndex_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, buffId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(2, bodyIndex_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, buffId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, bodyIndex_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.HeavenBattleBodyBuffMsg)) {
      return super.equals(obj);
    }
    xddq.pb.HeavenBattleBodyBuffMsg other = (xddq.pb.HeavenBattleBodyBuffMsg) obj;

    if (hasBuffId() != other.hasBuffId()) return false;
    if (hasBuffId()) {
      if (getBuffId()
          != other.getBuffId()) return false;
    }
    if (hasBodyIndex() != other.hasBodyIndex()) return false;
    if (hasBodyIndex()) {
      if (getBodyIndex()
          != other.getBodyIndex()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasBuffId()) {
      hash = (37 * hash) + BUFFID_FIELD_NUMBER;
      hash = (53 * hash) + getBuffId();
    }
    if (hasBodyIndex()) {
      hash = (37 * hash) + BODYINDEX_FIELD_NUMBER;
      hash = (53 * hash) + getBodyIndex();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.HeavenBattleBodyBuffMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.HeavenBattleBodyBuffMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.HeavenBattleBodyBuffMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.HeavenBattleBodyBuffMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.HeavenBattleBodyBuffMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.HeavenBattleBodyBuffMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.HeavenBattleBodyBuffMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.HeavenBattleBodyBuffMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.HeavenBattleBodyBuffMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.HeavenBattleBodyBuffMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.HeavenBattleBodyBuffMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.HeavenBattleBodyBuffMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.HeavenBattleBodyBuffMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.HeavenBattleBodyBuffMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.HeavenBattleBodyBuffMsg)
      xddq.pb.HeavenBattleBodyBuffMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_HeavenBattleBodyBuffMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_HeavenBattleBodyBuffMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.HeavenBattleBodyBuffMsg.class, xddq.pb.HeavenBattleBodyBuffMsg.Builder.class);
    }

    // Construct using xddq.pb.HeavenBattleBodyBuffMsg.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      buffId_ = 0;
      bodyIndex_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_HeavenBattleBodyBuffMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.HeavenBattleBodyBuffMsg getDefaultInstanceForType() {
      return xddq.pb.HeavenBattleBodyBuffMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.HeavenBattleBodyBuffMsg build() {
      xddq.pb.HeavenBattleBodyBuffMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.HeavenBattleBodyBuffMsg buildPartial() {
      xddq.pb.HeavenBattleBodyBuffMsg result = new xddq.pb.HeavenBattleBodyBuffMsg(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.HeavenBattleBodyBuffMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.buffId_ = buffId_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.bodyIndex_ = bodyIndex_;
        to_bitField0_ |= 0x00000002;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.HeavenBattleBodyBuffMsg) {
        return mergeFrom((xddq.pb.HeavenBattleBodyBuffMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.HeavenBattleBodyBuffMsg other) {
      if (other == xddq.pb.HeavenBattleBodyBuffMsg.getDefaultInstance()) return this;
      if (other.hasBuffId()) {
        setBuffId(other.getBuffId());
      }
      if (other.hasBodyIndex()) {
        setBodyIndex(other.getBodyIndex());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              buffId_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              bodyIndex_ = input.readInt32();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int buffId_ ;
    /**
     * <code>optional int32 buffId = 1;</code>
     * @return Whether the buffId field is set.
     */
    @java.lang.Override
    public boolean hasBuffId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 buffId = 1;</code>
     * @return The buffId.
     */
    @java.lang.Override
    public int getBuffId() {
      return buffId_;
    }
    /**
     * <code>optional int32 buffId = 1;</code>
     * @param value The buffId to set.
     * @return This builder for chaining.
     */
    public Builder setBuffId(int value) {

      buffId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 buffId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearBuffId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      buffId_ = 0;
      onChanged();
      return this;
    }

    private int bodyIndex_ ;
    /**
     * <code>optional int32 bodyIndex = 2;</code>
     * @return Whether the bodyIndex field is set.
     */
    @java.lang.Override
    public boolean hasBodyIndex() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 bodyIndex = 2;</code>
     * @return The bodyIndex.
     */
    @java.lang.Override
    public int getBodyIndex() {
      return bodyIndex_;
    }
    /**
     * <code>optional int32 bodyIndex = 2;</code>
     * @param value The bodyIndex to set.
     * @return This builder for chaining.
     */
    public Builder setBodyIndex(int value) {

      bodyIndex_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 bodyIndex = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearBodyIndex() {
      bitField0_ = (bitField0_ & ~0x00000002);
      bodyIndex_ = 0;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.HeavenBattleBodyBuffMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.HeavenBattleBodyBuffMsg)
  private static final xddq.pb.HeavenBattleBodyBuffMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.HeavenBattleBodyBuffMsg();
  }

  public static xddq.pb.HeavenBattleBodyBuffMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<HeavenBattleBodyBuffMsg>
      PARSER = new com.google.protobuf.AbstractParser<HeavenBattleBodyBuffMsg>() {
    @java.lang.Override
    public HeavenBattleBodyBuffMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<HeavenBattleBodyBuffMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<HeavenBattleBodyBuffMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.HeavenBattleBodyBuffMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

