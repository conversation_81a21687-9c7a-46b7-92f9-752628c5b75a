// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.ReqGetActivityRankState}
 */
public final class ReqGetActivityRankState extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.ReqGetActivityRankState)
    ReqGetActivityRankStateOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      ReqGetActivityRankState.class.getName());
  }
  // Use ReqGetActivityRankState.newBuilder() to construct.
  private ReqGetActivityRankState(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private ReqGetActivityRankState() {
    extra_ = "";
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_ReqGetActivityRankState_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_ReqGetActivityRankState_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.ReqGetActivityRankState.class, xddq.pb.ReqGetActivityRankState.Builder.class);
  }

  private int bitField0_;
  public static final int ACTIVITYID_FIELD_NUMBER = 1;
  private int activityId_ = 0;
  /**
   * <code>optional int32 activityId = 1;</code>
   * @return Whether the activityId field is set.
   */
  @java.lang.Override
  public boolean hasActivityId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int32 activityId = 1;</code>
   * @return The activityId.
   */
  @java.lang.Override
  public int getActivityId() {
    return activityId_;
  }

  public static final int RANKTYPE_FIELD_NUMBER = 2;
  private int rankType_ = 0;
  /**
   * <code>optional int32 rankType = 2;</code>
   * @return Whether the rankType field is set.
   */
  @java.lang.Override
  public boolean hasRankType() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int32 rankType = 2;</code>
   * @return The rankType.
   */
  @java.lang.Override
  public int getRankType() {
    return rankType_;
  }

  public static final int SYSTEMID_FIELD_NUMBER = 3;
  private int sysTemId_ = 0;
  /**
   * <code>optional int32 sysTemId = 3;</code>
   * @return Whether the sysTemId field is set.
   */
  @java.lang.Override
  public boolean hasSysTemId() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional int32 sysTemId = 3;</code>
   * @return The sysTemId.
   */
  @java.lang.Override
  public int getSysTemId() {
    return sysTemId_;
  }

  public static final int EXTRA_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private volatile java.lang.Object extra_ = "";
  /**
   * <code>optional string extra = 4;</code>
   * @return Whether the extra field is set.
   */
  @java.lang.Override
  public boolean hasExtra() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional string extra = 4;</code>
   * @return The extra.
   */
  @java.lang.Override
  public java.lang.String getExtra() {
    java.lang.Object ref = extra_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        extra_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string extra = 4;</code>
   * @return The bytes for extra.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getExtraBytes() {
    java.lang.Object ref = extra_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      extra_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, activityId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(2, rankType_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt32(3, sysTemId_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 4, extra_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, activityId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, rankType_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(3, sysTemId_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(4, extra_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.ReqGetActivityRankState)) {
      return super.equals(obj);
    }
    xddq.pb.ReqGetActivityRankState other = (xddq.pb.ReqGetActivityRankState) obj;

    if (hasActivityId() != other.hasActivityId()) return false;
    if (hasActivityId()) {
      if (getActivityId()
          != other.getActivityId()) return false;
    }
    if (hasRankType() != other.hasRankType()) return false;
    if (hasRankType()) {
      if (getRankType()
          != other.getRankType()) return false;
    }
    if (hasSysTemId() != other.hasSysTemId()) return false;
    if (hasSysTemId()) {
      if (getSysTemId()
          != other.getSysTemId()) return false;
    }
    if (hasExtra() != other.hasExtra()) return false;
    if (hasExtra()) {
      if (!getExtra()
          .equals(other.getExtra())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasActivityId()) {
      hash = (37 * hash) + ACTIVITYID_FIELD_NUMBER;
      hash = (53 * hash) + getActivityId();
    }
    if (hasRankType()) {
      hash = (37 * hash) + RANKTYPE_FIELD_NUMBER;
      hash = (53 * hash) + getRankType();
    }
    if (hasSysTemId()) {
      hash = (37 * hash) + SYSTEMID_FIELD_NUMBER;
      hash = (53 * hash) + getSysTemId();
    }
    if (hasExtra()) {
      hash = (37 * hash) + EXTRA_FIELD_NUMBER;
      hash = (53 * hash) + getExtra().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.ReqGetActivityRankState parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ReqGetActivityRankState parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ReqGetActivityRankState parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ReqGetActivityRankState parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ReqGetActivityRankState parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ReqGetActivityRankState parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ReqGetActivityRankState parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.ReqGetActivityRankState parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.ReqGetActivityRankState parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.ReqGetActivityRankState parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.ReqGetActivityRankState parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.ReqGetActivityRankState parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.ReqGetActivityRankState prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.ReqGetActivityRankState}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.ReqGetActivityRankState)
      xddq.pb.ReqGetActivityRankStateOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ReqGetActivityRankState_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ReqGetActivityRankState_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.ReqGetActivityRankState.class, xddq.pb.ReqGetActivityRankState.Builder.class);
    }

    // Construct using xddq.pb.ReqGetActivityRankState.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      activityId_ = 0;
      rankType_ = 0;
      sysTemId_ = 0;
      extra_ = "";
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ReqGetActivityRankState_descriptor;
    }

    @java.lang.Override
    public xddq.pb.ReqGetActivityRankState getDefaultInstanceForType() {
      return xddq.pb.ReqGetActivityRankState.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.ReqGetActivityRankState build() {
      xddq.pb.ReqGetActivityRankState result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.ReqGetActivityRankState buildPartial() {
      xddq.pb.ReqGetActivityRankState result = new xddq.pb.ReqGetActivityRankState(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.ReqGetActivityRankState result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.activityId_ = activityId_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.rankType_ = rankType_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.sysTemId_ = sysTemId_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.extra_ = extra_;
        to_bitField0_ |= 0x00000008;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.ReqGetActivityRankState) {
        return mergeFrom((xddq.pb.ReqGetActivityRankState)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.ReqGetActivityRankState other) {
      if (other == xddq.pb.ReqGetActivityRankState.getDefaultInstance()) return this;
      if (other.hasActivityId()) {
        setActivityId(other.getActivityId());
      }
      if (other.hasRankType()) {
        setRankType(other.getRankType());
      }
      if (other.hasSysTemId()) {
        setSysTemId(other.getSysTemId());
      }
      if (other.hasExtra()) {
        extra_ = other.extra_;
        bitField0_ |= 0x00000008;
        onChanged();
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              activityId_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              rankType_ = input.readInt32();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 24: {
              sysTemId_ = input.readInt32();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 34: {
              extra_ = input.readBytes();
              bitField0_ |= 0x00000008;
              break;
            } // case 34
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int activityId_ ;
    /**
     * <code>optional int32 activityId = 1;</code>
     * @return Whether the activityId field is set.
     */
    @java.lang.Override
    public boolean hasActivityId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 activityId = 1;</code>
     * @return The activityId.
     */
    @java.lang.Override
    public int getActivityId() {
      return activityId_;
    }
    /**
     * <code>optional int32 activityId = 1;</code>
     * @param value The activityId to set.
     * @return This builder for chaining.
     */
    public Builder setActivityId(int value) {

      activityId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 activityId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearActivityId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      activityId_ = 0;
      onChanged();
      return this;
    }

    private int rankType_ ;
    /**
     * <code>optional int32 rankType = 2;</code>
     * @return Whether the rankType field is set.
     */
    @java.lang.Override
    public boolean hasRankType() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 rankType = 2;</code>
     * @return The rankType.
     */
    @java.lang.Override
    public int getRankType() {
      return rankType_;
    }
    /**
     * <code>optional int32 rankType = 2;</code>
     * @param value The rankType to set.
     * @return This builder for chaining.
     */
    public Builder setRankType(int value) {

      rankType_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 rankType = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearRankType() {
      bitField0_ = (bitField0_ & ~0x00000002);
      rankType_ = 0;
      onChanged();
      return this;
    }

    private int sysTemId_ ;
    /**
     * <code>optional int32 sysTemId = 3;</code>
     * @return Whether the sysTemId field is set.
     */
    @java.lang.Override
    public boolean hasSysTemId() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int32 sysTemId = 3;</code>
     * @return The sysTemId.
     */
    @java.lang.Override
    public int getSysTemId() {
      return sysTemId_;
    }
    /**
     * <code>optional int32 sysTemId = 3;</code>
     * @param value The sysTemId to set.
     * @return This builder for chaining.
     */
    public Builder setSysTemId(int value) {

      sysTemId_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 sysTemId = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearSysTemId() {
      bitField0_ = (bitField0_ & ~0x00000004);
      sysTemId_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object extra_ = "";
    /**
     * <code>optional string extra = 4;</code>
     * @return Whether the extra field is set.
     */
    public boolean hasExtra() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional string extra = 4;</code>
     * @return The extra.
     */
    public java.lang.String getExtra() {
      java.lang.Object ref = extra_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          extra_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string extra = 4;</code>
     * @return The bytes for extra.
     */
    public com.google.protobuf.ByteString
        getExtraBytes() {
      java.lang.Object ref = extra_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        extra_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string extra = 4;</code>
     * @param value The extra to set.
     * @return This builder for chaining.
     */
    public Builder setExtra(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      extra_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional string extra = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearExtra() {
      extra_ = getDefaultInstance().getExtra();
      bitField0_ = (bitField0_ & ~0x00000008);
      onChanged();
      return this;
    }
    /**
     * <code>optional string extra = 4;</code>
     * @param value The bytes for extra to set.
     * @return This builder for chaining.
     */
    public Builder setExtraBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      extra_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.ReqGetActivityRankState)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.ReqGetActivityRankState)
  private static final xddq.pb.ReqGetActivityRankState DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.ReqGetActivityRankState();
  }

  public static xddq.pb.ReqGetActivityRankState getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ReqGetActivityRankState>
      PARSER = new com.google.protobuf.AbstractParser<ReqGetActivityRankState>() {
    @java.lang.Override
    public ReqGetActivityRankState parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<ReqGetActivityRankState> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ReqGetActivityRankState> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.ReqGetActivityRankState getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

