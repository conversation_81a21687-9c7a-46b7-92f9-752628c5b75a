// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

public interface TowerDemonBattleResultDataOrBuilder extends
    // @@protoc_insertion_point(interface_extends:xddq.pb.TowerDemonBattleResultData)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>optional int64 curScore = 1;</code>
   * @return Whether the curScore field is set.
   */
  boolean hasCurScore();
  /**
   * <code>optional int64 curScore = 1;</code>
   * @return The curScore.
   */
  long getCurScore();

  /**
   * <code>optional int64 changeScore = 2;</code>
   * @return Whether the changeScore field is set.
   */
  boolean hasChangeScore();
  /**
   * <code>optional int64 changeScore = 2;</code>
   * @return The changeScore.
   */
  long getChangeScore();
}
