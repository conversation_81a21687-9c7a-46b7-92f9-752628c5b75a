// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.UnionAreaWarTreasuryRespMsg}
 */
public final class UnionAreaWarTreasuryRespMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.UnionAreaWarTreasuryRespMsg)
    UnionAreaWarTreasuryRespMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      UnionAreaWarTreasuryRespMsg.class.getName());
  }
  // Use UnionAreaWarTreasuryRespMsg.newBuilder() to construct.
  private UnionAreaWarTreasuryRespMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private UnionAreaWarTreasuryRespMsg() {
    unlockInfo_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_UnionAreaWarTreasuryRespMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_UnionAreaWarTreasuryRespMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.UnionAreaWarTreasuryRespMsg.class, xddq.pb.UnionAreaWarTreasuryRespMsg.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int UNLOCKINFO_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.UnionAreaWarTreasureMsg> unlockInfo_;
  /**
   * <code>repeated .xddq.pb.UnionAreaWarTreasureMsg unlockInfo = 2;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.UnionAreaWarTreasureMsg> getUnlockInfoList() {
    return unlockInfo_;
  }
  /**
   * <code>repeated .xddq.pb.UnionAreaWarTreasureMsg unlockInfo = 2;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.UnionAreaWarTreasureMsgOrBuilder> 
      getUnlockInfoOrBuilderList() {
    return unlockInfo_;
  }
  /**
   * <code>repeated .xddq.pb.UnionAreaWarTreasureMsg unlockInfo = 2;</code>
   */
  @java.lang.Override
  public int getUnlockInfoCount() {
    return unlockInfo_.size();
  }
  /**
   * <code>repeated .xddq.pb.UnionAreaWarTreasureMsg unlockInfo = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.UnionAreaWarTreasureMsg getUnlockInfo(int index) {
    return unlockInfo_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.UnionAreaWarTreasureMsg unlockInfo = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.UnionAreaWarTreasureMsgOrBuilder getUnlockInfoOrBuilder(
      int index) {
    return unlockInfo_.get(index);
  }

  public static final int STAGE_FIELD_NUMBER = 3;
  private int stage_ = 0;
  /**
   * <code>optional int32 stage = 3;</code>
   * @return Whether the stage field is set.
   */
  @java.lang.Override
  public boolean hasStage() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int32 stage = 3;</code>
   * @return The stage.
   */
  @java.lang.Override
  public int getStage() {
    return stage_;
  }

  public static final int OPENCOUNT_FIELD_NUMBER = 4;
  private int openCount_ = 0;
  /**
   * <code>optional int32 openCount = 4;</code>
   * @return Whether the openCount field is set.
   */
  @java.lang.Override
  public boolean hasOpenCount() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional int32 openCount = 4;</code>
   * @return The openCount.
   */
  @java.lang.Override
  public int getOpenCount() {
    return openCount_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    for (int i = 0; i < getUnlockInfoCount(); i++) {
      if (!getUnlockInfo(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    for (int i = 0; i < unlockInfo_.size(); i++) {
      output.writeMessage(2, unlockInfo_.get(i));
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(3, stage_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt32(4, openCount_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    for (int i = 0; i < unlockInfo_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, unlockInfo_.get(i));
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(3, stage_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(4, openCount_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.UnionAreaWarTreasuryRespMsg)) {
      return super.equals(obj);
    }
    xddq.pb.UnionAreaWarTreasuryRespMsg other = (xddq.pb.UnionAreaWarTreasuryRespMsg) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (!getUnlockInfoList()
        .equals(other.getUnlockInfoList())) return false;
    if (hasStage() != other.hasStage()) return false;
    if (hasStage()) {
      if (getStage()
          != other.getStage()) return false;
    }
    if (hasOpenCount() != other.hasOpenCount()) return false;
    if (hasOpenCount()) {
      if (getOpenCount()
          != other.getOpenCount()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (getUnlockInfoCount() > 0) {
      hash = (37 * hash) + UNLOCKINFO_FIELD_NUMBER;
      hash = (53 * hash) + getUnlockInfoList().hashCode();
    }
    if (hasStage()) {
      hash = (37 * hash) + STAGE_FIELD_NUMBER;
      hash = (53 * hash) + getStage();
    }
    if (hasOpenCount()) {
      hash = (37 * hash) + OPENCOUNT_FIELD_NUMBER;
      hash = (53 * hash) + getOpenCount();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.UnionAreaWarTreasuryRespMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.UnionAreaWarTreasuryRespMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.UnionAreaWarTreasuryRespMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.UnionAreaWarTreasuryRespMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.UnionAreaWarTreasuryRespMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.UnionAreaWarTreasuryRespMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.UnionAreaWarTreasuryRespMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.UnionAreaWarTreasuryRespMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.UnionAreaWarTreasuryRespMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.UnionAreaWarTreasuryRespMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.UnionAreaWarTreasuryRespMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.UnionAreaWarTreasuryRespMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.UnionAreaWarTreasuryRespMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.UnionAreaWarTreasuryRespMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.UnionAreaWarTreasuryRespMsg)
      xddq.pb.UnionAreaWarTreasuryRespMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_UnionAreaWarTreasuryRespMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_UnionAreaWarTreasuryRespMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.UnionAreaWarTreasuryRespMsg.class, xddq.pb.UnionAreaWarTreasuryRespMsg.Builder.class);
    }

    // Construct using xddq.pb.UnionAreaWarTreasuryRespMsg.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      if (unlockInfoBuilder_ == null) {
        unlockInfo_ = java.util.Collections.emptyList();
      } else {
        unlockInfo_ = null;
        unlockInfoBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000002);
      stage_ = 0;
      openCount_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_UnionAreaWarTreasuryRespMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.UnionAreaWarTreasuryRespMsg getDefaultInstanceForType() {
      return xddq.pb.UnionAreaWarTreasuryRespMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.UnionAreaWarTreasuryRespMsg build() {
      xddq.pb.UnionAreaWarTreasuryRespMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.UnionAreaWarTreasuryRespMsg buildPartial() {
      xddq.pb.UnionAreaWarTreasuryRespMsg result = new xddq.pb.UnionAreaWarTreasuryRespMsg(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.UnionAreaWarTreasuryRespMsg result) {
      if (unlockInfoBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          unlockInfo_ = java.util.Collections.unmodifiableList(unlockInfo_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.unlockInfo_ = unlockInfo_;
      } else {
        result.unlockInfo_ = unlockInfoBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.UnionAreaWarTreasuryRespMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.stage_ = stage_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.openCount_ = openCount_;
        to_bitField0_ |= 0x00000004;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.UnionAreaWarTreasuryRespMsg) {
        return mergeFrom((xddq.pb.UnionAreaWarTreasuryRespMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.UnionAreaWarTreasuryRespMsg other) {
      if (other == xddq.pb.UnionAreaWarTreasuryRespMsg.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (unlockInfoBuilder_ == null) {
        if (!other.unlockInfo_.isEmpty()) {
          if (unlockInfo_.isEmpty()) {
            unlockInfo_ = other.unlockInfo_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureUnlockInfoIsMutable();
            unlockInfo_.addAll(other.unlockInfo_);
          }
          onChanged();
        }
      } else {
        if (!other.unlockInfo_.isEmpty()) {
          if (unlockInfoBuilder_.isEmpty()) {
            unlockInfoBuilder_.dispose();
            unlockInfoBuilder_ = null;
            unlockInfo_ = other.unlockInfo_;
            bitField0_ = (bitField0_ & ~0x00000002);
            unlockInfoBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetUnlockInfoFieldBuilder() : null;
          } else {
            unlockInfoBuilder_.addAllMessages(other.unlockInfo_);
          }
        }
      }
      if (other.hasStage()) {
        setStage(other.getStage());
      }
      if (other.hasOpenCount()) {
        setOpenCount(other.getOpenCount());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      for (int i = 0; i < getUnlockInfoCount(); i++) {
        if (!getUnlockInfo(i).isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              xddq.pb.UnionAreaWarTreasureMsg m =
                  input.readMessage(
                      xddq.pb.UnionAreaWarTreasureMsg.parser(),
                      extensionRegistry);
              if (unlockInfoBuilder_ == null) {
                ensureUnlockInfoIsMutable();
                unlockInfo_.add(m);
              } else {
                unlockInfoBuilder_.addMessage(m);
              }
              break;
            } // case 18
            case 24: {
              stage_ = input.readInt32();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 32: {
              openCount_ = input.readInt32();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.UnionAreaWarTreasureMsg> unlockInfo_ =
      java.util.Collections.emptyList();
    private void ensureUnlockInfoIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        unlockInfo_ = new java.util.ArrayList<xddq.pb.UnionAreaWarTreasureMsg>(unlockInfo_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.UnionAreaWarTreasureMsg, xddq.pb.UnionAreaWarTreasureMsg.Builder, xddq.pb.UnionAreaWarTreasureMsgOrBuilder> unlockInfoBuilder_;

    /**
     * <code>repeated .xddq.pb.UnionAreaWarTreasureMsg unlockInfo = 2;</code>
     */
    public java.util.List<xddq.pb.UnionAreaWarTreasureMsg> getUnlockInfoList() {
      if (unlockInfoBuilder_ == null) {
        return java.util.Collections.unmodifiableList(unlockInfo_);
      } else {
        return unlockInfoBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.UnionAreaWarTreasureMsg unlockInfo = 2;</code>
     */
    public int getUnlockInfoCount() {
      if (unlockInfoBuilder_ == null) {
        return unlockInfo_.size();
      } else {
        return unlockInfoBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.UnionAreaWarTreasureMsg unlockInfo = 2;</code>
     */
    public xddq.pb.UnionAreaWarTreasureMsg getUnlockInfo(int index) {
      if (unlockInfoBuilder_ == null) {
        return unlockInfo_.get(index);
      } else {
        return unlockInfoBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.UnionAreaWarTreasureMsg unlockInfo = 2;</code>
     */
    public Builder setUnlockInfo(
        int index, xddq.pb.UnionAreaWarTreasureMsg value) {
      if (unlockInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureUnlockInfoIsMutable();
        unlockInfo_.set(index, value);
        onChanged();
      } else {
        unlockInfoBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionAreaWarTreasureMsg unlockInfo = 2;</code>
     */
    public Builder setUnlockInfo(
        int index, xddq.pb.UnionAreaWarTreasureMsg.Builder builderForValue) {
      if (unlockInfoBuilder_ == null) {
        ensureUnlockInfoIsMutable();
        unlockInfo_.set(index, builderForValue.build());
        onChanged();
      } else {
        unlockInfoBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionAreaWarTreasureMsg unlockInfo = 2;</code>
     */
    public Builder addUnlockInfo(xddq.pb.UnionAreaWarTreasureMsg value) {
      if (unlockInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureUnlockInfoIsMutable();
        unlockInfo_.add(value);
        onChanged();
      } else {
        unlockInfoBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionAreaWarTreasureMsg unlockInfo = 2;</code>
     */
    public Builder addUnlockInfo(
        int index, xddq.pb.UnionAreaWarTreasureMsg value) {
      if (unlockInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureUnlockInfoIsMutable();
        unlockInfo_.add(index, value);
        onChanged();
      } else {
        unlockInfoBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionAreaWarTreasureMsg unlockInfo = 2;</code>
     */
    public Builder addUnlockInfo(
        xddq.pb.UnionAreaWarTreasureMsg.Builder builderForValue) {
      if (unlockInfoBuilder_ == null) {
        ensureUnlockInfoIsMutable();
        unlockInfo_.add(builderForValue.build());
        onChanged();
      } else {
        unlockInfoBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionAreaWarTreasureMsg unlockInfo = 2;</code>
     */
    public Builder addUnlockInfo(
        int index, xddq.pb.UnionAreaWarTreasureMsg.Builder builderForValue) {
      if (unlockInfoBuilder_ == null) {
        ensureUnlockInfoIsMutable();
        unlockInfo_.add(index, builderForValue.build());
        onChanged();
      } else {
        unlockInfoBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionAreaWarTreasureMsg unlockInfo = 2;</code>
     */
    public Builder addAllUnlockInfo(
        java.lang.Iterable<? extends xddq.pb.UnionAreaWarTreasureMsg> values) {
      if (unlockInfoBuilder_ == null) {
        ensureUnlockInfoIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, unlockInfo_);
        onChanged();
      } else {
        unlockInfoBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionAreaWarTreasureMsg unlockInfo = 2;</code>
     */
    public Builder clearUnlockInfo() {
      if (unlockInfoBuilder_ == null) {
        unlockInfo_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        unlockInfoBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionAreaWarTreasureMsg unlockInfo = 2;</code>
     */
    public Builder removeUnlockInfo(int index) {
      if (unlockInfoBuilder_ == null) {
        ensureUnlockInfoIsMutable();
        unlockInfo_.remove(index);
        onChanged();
      } else {
        unlockInfoBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionAreaWarTreasureMsg unlockInfo = 2;</code>
     */
    public xddq.pb.UnionAreaWarTreasureMsg.Builder getUnlockInfoBuilder(
        int index) {
      return internalGetUnlockInfoFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.UnionAreaWarTreasureMsg unlockInfo = 2;</code>
     */
    public xddq.pb.UnionAreaWarTreasureMsgOrBuilder getUnlockInfoOrBuilder(
        int index) {
      if (unlockInfoBuilder_ == null) {
        return unlockInfo_.get(index);  } else {
        return unlockInfoBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.UnionAreaWarTreasureMsg unlockInfo = 2;</code>
     */
    public java.util.List<? extends xddq.pb.UnionAreaWarTreasureMsgOrBuilder> 
         getUnlockInfoOrBuilderList() {
      if (unlockInfoBuilder_ != null) {
        return unlockInfoBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(unlockInfo_);
      }
    }
    /**
     * <code>repeated .xddq.pb.UnionAreaWarTreasureMsg unlockInfo = 2;</code>
     */
    public xddq.pb.UnionAreaWarTreasureMsg.Builder addUnlockInfoBuilder() {
      return internalGetUnlockInfoFieldBuilder().addBuilder(
          xddq.pb.UnionAreaWarTreasureMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.UnionAreaWarTreasureMsg unlockInfo = 2;</code>
     */
    public xddq.pb.UnionAreaWarTreasureMsg.Builder addUnlockInfoBuilder(
        int index) {
      return internalGetUnlockInfoFieldBuilder().addBuilder(
          index, xddq.pb.UnionAreaWarTreasureMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.UnionAreaWarTreasureMsg unlockInfo = 2;</code>
     */
    public java.util.List<xddq.pb.UnionAreaWarTreasureMsg.Builder> 
         getUnlockInfoBuilderList() {
      return internalGetUnlockInfoFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.UnionAreaWarTreasureMsg, xddq.pb.UnionAreaWarTreasureMsg.Builder, xddq.pb.UnionAreaWarTreasureMsgOrBuilder> 
        internalGetUnlockInfoFieldBuilder() {
      if (unlockInfoBuilder_ == null) {
        unlockInfoBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.UnionAreaWarTreasureMsg, xddq.pb.UnionAreaWarTreasureMsg.Builder, xddq.pb.UnionAreaWarTreasureMsgOrBuilder>(
                unlockInfo_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        unlockInfo_ = null;
      }
      return unlockInfoBuilder_;
    }

    private int stage_ ;
    /**
     * <code>optional int32 stage = 3;</code>
     * @return Whether the stage field is set.
     */
    @java.lang.Override
    public boolean hasStage() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int32 stage = 3;</code>
     * @return The stage.
     */
    @java.lang.Override
    public int getStage() {
      return stage_;
    }
    /**
     * <code>optional int32 stage = 3;</code>
     * @param value The stage to set.
     * @return This builder for chaining.
     */
    public Builder setStage(int value) {

      stage_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 stage = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearStage() {
      bitField0_ = (bitField0_ & ~0x00000004);
      stage_ = 0;
      onChanged();
      return this;
    }

    private int openCount_ ;
    /**
     * <code>optional int32 openCount = 4;</code>
     * @return Whether the openCount field is set.
     */
    @java.lang.Override
    public boolean hasOpenCount() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int32 openCount = 4;</code>
     * @return The openCount.
     */
    @java.lang.Override
    public int getOpenCount() {
      return openCount_;
    }
    /**
     * <code>optional int32 openCount = 4;</code>
     * @param value The openCount to set.
     * @return This builder for chaining.
     */
    public Builder setOpenCount(int value) {

      openCount_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 openCount = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearOpenCount() {
      bitField0_ = (bitField0_ & ~0x00000008);
      openCount_ = 0;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.UnionAreaWarTreasuryRespMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.UnionAreaWarTreasuryRespMsg)
  private static final xddq.pb.UnionAreaWarTreasuryRespMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.UnionAreaWarTreasuryRespMsg();
  }

  public static xddq.pb.UnionAreaWarTreasuryRespMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<UnionAreaWarTreasuryRespMsg>
      PARSER = new com.google.protobuf.AbstractParser<UnionAreaWarTreasuryRespMsg>() {
    @java.lang.Override
    public UnionAreaWarTreasuryRespMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<UnionAreaWarTreasuryRespMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<UnionAreaWarTreasuryRespMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.UnionAreaWarTreasuryRespMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

