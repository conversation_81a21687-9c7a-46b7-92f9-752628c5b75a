// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.HolyLandGameInfoRespMsg}
 */
public final class HolyLandGameInfoRespMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.HolyLandGameInfoRespMsg)
    HolyLandGameInfoRespMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      HolyLandGameInfoRespMsg.class.getName());
  }
  // Use HolyLandGameInfoRespMsg.newBuilder() to construct.
  private HolyLandGameInfoRespMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private HolyLandGameInfoRespMsg() {
    fightValue_ = "";
    lastChampionUnions_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_HolyLandGameInfoRespMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_HolyLandGameInfoRespMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.HolyLandGameInfoRespMsg.class, xddq.pb.HolyLandGameInfoRespMsg.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int BASECAMP_FIELD_NUMBER = 2;
  private int baseCamp_ = 0;
  /**
   * <code>optional int32 baseCamp = 2;</code>
   * @return Whether the baseCamp field is set.
   */
  @java.lang.Override
  public boolean hasBaseCamp() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int32 baseCamp = 2;</code>
   * @return The baseCamp.
   */
  @java.lang.Override
  public int getBaseCamp() {
    return baseCamp_;
  }

  public static final int FIGHTVALUE_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private volatile java.lang.Object fightValue_ = "";
  /**
   * <code>optional string fightValue = 3;</code>
   * @return Whether the fightValue field is set.
   */
  @java.lang.Override
  public boolean hasFightValue() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional string fightValue = 3;</code>
   * @return The fightValue.
   */
  @java.lang.Override
  public java.lang.String getFightValue() {
    java.lang.Object ref = fightValue_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        fightValue_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string fightValue = 3;</code>
   * @return The bytes for fightValue.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getFightValueBytes() {
    java.lang.Object ref = fightValue_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      fightValue_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int REBIRTHDATA_FIELD_NUMBER = 4;
  private xddq.pb.HolyLandBeenKillSyncMsg rebirthData_;
  /**
   * <code>optional .xddq.pb.HolyLandBeenKillSyncMsg rebirthData = 4;</code>
   * @return Whether the rebirthData field is set.
   */
  @java.lang.Override
  public boolean hasRebirthData() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional .xddq.pb.HolyLandBeenKillSyncMsg rebirthData = 4;</code>
   * @return The rebirthData.
   */
  @java.lang.Override
  public xddq.pb.HolyLandBeenKillSyncMsg getRebirthData() {
    return rebirthData_ == null ? xddq.pb.HolyLandBeenKillSyncMsg.getDefaultInstance() : rebirthData_;
  }
  /**
   * <code>optional .xddq.pb.HolyLandBeenKillSyncMsg rebirthData = 4;</code>
   */
  @java.lang.Override
  public xddq.pb.HolyLandBeenKillSyncMsgOrBuilder getRebirthDataOrBuilder() {
    return rebirthData_ == null ? xddq.pb.HolyLandBeenKillSyncMsg.getDefaultInstance() : rebirthData_;
  }

  public static final int MYROUTEINFO_FIELD_NUMBER = 5;
  private xddq.pb.HolyLandGameRouteInfo myRouteInfo_;
  /**
   * <code>optional .xddq.pb.HolyLandGameRouteInfo myRouteInfo = 5;</code>
   * @return Whether the myRouteInfo field is set.
   */
  @java.lang.Override
  public boolean hasMyRouteInfo() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>optional .xddq.pb.HolyLandGameRouteInfo myRouteInfo = 5;</code>
   * @return The myRouteInfo.
   */
  @java.lang.Override
  public xddq.pb.HolyLandGameRouteInfo getMyRouteInfo() {
    return myRouteInfo_ == null ? xddq.pb.HolyLandGameRouteInfo.getDefaultInstance() : myRouteInfo_;
  }
  /**
   * <code>optional .xddq.pb.HolyLandGameRouteInfo myRouteInfo = 5;</code>
   */
  @java.lang.Override
  public xddq.pb.HolyLandGameRouteInfoOrBuilder getMyRouteInfoOrBuilder() {
    return myRouteInfo_ == null ? xddq.pb.HolyLandGameRouteInfo.getDefaultInstance() : myRouteInfo_;
  }

  public static final int ITEMREBIRTHCOUNT_FIELD_NUMBER = 6;
  private int itemRebirthCount_ = 0;
  /**
   * <code>optional int32 itemRebirthCount = 6;</code>
   * @return Whether the itemRebirthCount field is set.
   */
  @java.lang.Override
  public boolean hasItemRebirthCount() {
    return ((bitField0_ & 0x00000020) != 0);
  }
  /**
   * <code>optional int32 itemRebirthCount = 6;</code>
   * @return The itemRebirthCount.
   */
  @java.lang.Override
  public int getItemRebirthCount() {
    return itemRebirthCount_;
  }

  public static final int LEFTOUT_FIELD_NUMBER = 7;
  private boolean leftOut_ = false;
  /**
   * <code>optional bool leftOut = 7;</code>
   * @return Whether the leftOut field is set.
   */
  @java.lang.Override
  public boolean hasLeftOut() {
    return ((bitField0_ & 0x00000040) != 0);
  }
  /**
   * <code>optional bool leftOut = 7;</code>
   * @return The leftOut.
   */
  @java.lang.Override
  public boolean getLeftOut() {
    return leftOut_;
  }

  public static final int LOCATION_FIELD_NUMBER = 8;
  private int location_ = 0;
  /**
   * <code>optional int32 location = 8;</code>
   * @return Whether the location field is set.
   */
  @java.lang.Override
  public boolean hasLocation() {
    return ((bitField0_ & 0x00000080) != 0);
  }
  /**
   * <code>optional int32 location = 8;</code>
   * @return The location.
   */
  @java.lang.Override
  public int getLocation() {
    return location_;
  }

  public static final int SPEEDUPCOUNT_FIELD_NUMBER = 9;
  private int speedUpCount_ = 0;
  /**
   * <code>optional int32 speedUpCount = 9;</code>
   * @return Whether the speedUpCount field is set.
   */
  @java.lang.Override
  public boolean hasSpeedUpCount() {
    return ((bitField0_ & 0x00000100) != 0);
  }
  /**
   * <code>optional int32 speedUpCount = 9;</code>
   * @return The speedUpCount.
   */
  @java.lang.Override
  public int getSpeedUpCount() {
    return speedUpCount_;
  }

  public static final int HAVECOMMAND_FIELD_NUMBER = 11;
  private boolean haveCommand_ = false;
  /**
   * <code>optional bool haveCommand = 11;</code>
   * @return Whether the haveCommand field is set.
   */
  @java.lang.Override
  public boolean hasHaveCommand() {
    return ((bitField0_ & 0x00000200) != 0);
  }
  /**
   * <code>optional bool haveCommand = 11;</code>
   * @return The haveCommand.
   */
  @java.lang.Override
  public boolean getHaveCommand() {
    return haveCommand_;
  }

  public static final int CURPOWER_FIELD_NUMBER = 12;
  private int curPower_ = 0;
  /**
   * <code>optional int32 curPower = 12;</code>
   * @return Whether the curPower field is set.
   */
  @java.lang.Override
  public boolean hasCurPower() {
    return ((bitField0_ & 0x00000400) != 0);
  }
  /**
   * <code>optional int32 curPower = 12;</code>
   * @return The curPower.
   */
  @java.lang.Override
  public int getCurPower() {
    return curPower_;
  }

  public static final int ATTACKCD_FIELD_NUMBER = 13;
  private long attackCd_ = 0L;
  /**
   * <code>optional int64 attackCd = 13;</code>
   * @return Whether the attackCd field is set.
   */
  @java.lang.Override
  public boolean hasAttackCd() {
    return ((bitField0_ & 0x00000800) != 0);
  }
  /**
   * <code>optional int64 attackCd = 13;</code>
   * @return The attackCd.
   */
  @java.lang.Override
  public long getAttackCd() {
    return attackCd_;
  }

  public static final int UNIONPOINTRANK_FIELD_NUMBER = 14;
  private int unionPointRank_ = 0;
  /**
   * <code>optional int32 unionPointRank = 14;</code>
   * @return Whether the unionPointRank field is set.
   */
  @java.lang.Override
  public boolean hasUnionPointRank() {
    return ((bitField0_ & 0x00001000) != 0);
  }
  /**
   * <code>optional int32 unionPointRank = 14;</code>
   * @return The unionPointRank.
   */
  @java.lang.Override
  public int getUnionPointRank() {
    return unionPointRank_;
  }

  public static final int FREEREBIRTHCOUNT_FIELD_NUMBER = 15;
  private int freeRebirthCount_ = 0;
  /**
   * <code>optional int32 freeRebirthCount = 15;</code>
   * @return Whether the freeRebirthCount field is set.
   */
  @java.lang.Override
  public boolean hasFreeRebirthCount() {
    return ((bitField0_ & 0x00002000) != 0);
  }
  /**
   * <code>optional int32 freeRebirthCount = 15;</code>
   * @return The freeRebirthCount.
   */
  @java.lang.Override
  public int getFreeRebirthCount() {
    return freeRebirthCount_;
  }

  public static final int LASTCHAMPIONUNIONS_FIELD_NUMBER = 16;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.UnionRankHistoryMsg> lastChampionUnions_;
  /**
   * <code>repeated .xddq.pb.UnionRankHistoryMsg lastChampionUnions = 16;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.UnionRankHistoryMsg> getLastChampionUnionsList() {
    return lastChampionUnions_;
  }
  /**
   * <code>repeated .xddq.pb.UnionRankHistoryMsg lastChampionUnions = 16;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.UnionRankHistoryMsgOrBuilder> 
      getLastChampionUnionsOrBuilderList() {
    return lastChampionUnions_;
  }
  /**
   * <code>repeated .xddq.pb.UnionRankHistoryMsg lastChampionUnions = 16;</code>
   */
  @java.lang.Override
  public int getLastChampionUnionsCount() {
    return lastChampionUnions_.size();
  }
  /**
   * <code>repeated .xddq.pb.UnionRankHistoryMsg lastChampionUnions = 16;</code>
   */
  @java.lang.Override
  public xddq.pb.UnionRankHistoryMsg getLastChampionUnions(int index) {
    return lastChampionUnions_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.UnionRankHistoryMsg lastChampionUnions = 16;</code>
   */
  @java.lang.Override
  public xddq.pb.UnionRankHistoryMsgOrBuilder getLastChampionUnionsOrBuilder(
      int index) {
    return lastChampionUnions_.get(index);
  }

  public static final int AUTOATKCITYID_FIELD_NUMBER = 17;
  private int autoAtkCityId_ = 0;
  /**
   * <code>optional int32 autoAtkCityId = 17;</code>
   * @return Whether the autoAtkCityId field is set.
   */
  @java.lang.Override
  public boolean hasAutoAtkCityId() {
    return ((bitField0_ & 0x00004000) != 0);
  }
  /**
   * <code>optional int32 autoAtkCityId = 17;</code>
   * @return The autoAtkCityId.
   */
  @java.lang.Override
  public int getAutoAtkCityId() {
    return autoAtkCityId_;
  }

  public static final int REALMSTYPE_FIELD_NUMBER = 18;
  private int realmsType_ = 0;
  /**
   * <code>optional int32 realmsType = 18;</code>
   * @return Whether the realmsType field is set.
   */
  @java.lang.Override
  public boolean hasRealmsType() {
    return ((bitField0_ & 0x00008000) != 0);
  }
  /**
   * <code>optional int32 realmsType = 18;</code>
   * @return The realmsType.
   */
  @java.lang.Override
  public int getRealmsType() {
    return realmsType_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(2, baseCamp_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 3, fightValue_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeMessage(4, getRebirthData());
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      output.writeMessage(5, getMyRouteInfo());
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      output.writeInt32(6, itemRebirthCount_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      output.writeBool(7, leftOut_);
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      output.writeInt32(8, location_);
    }
    if (((bitField0_ & 0x00000100) != 0)) {
      output.writeInt32(9, speedUpCount_);
    }
    if (((bitField0_ & 0x00000200) != 0)) {
      output.writeBool(11, haveCommand_);
    }
    if (((bitField0_ & 0x00000400) != 0)) {
      output.writeInt32(12, curPower_);
    }
    if (((bitField0_ & 0x00000800) != 0)) {
      output.writeInt64(13, attackCd_);
    }
    if (((bitField0_ & 0x00001000) != 0)) {
      output.writeInt32(14, unionPointRank_);
    }
    if (((bitField0_ & 0x00002000) != 0)) {
      output.writeInt32(15, freeRebirthCount_);
    }
    for (int i = 0; i < lastChampionUnions_.size(); i++) {
      output.writeMessage(16, lastChampionUnions_.get(i));
    }
    if (((bitField0_ & 0x00004000) != 0)) {
      output.writeInt32(17, autoAtkCityId_);
    }
    if (((bitField0_ & 0x00008000) != 0)) {
      output.writeInt32(18, realmsType_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, baseCamp_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(3, fightValue_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(4, getRebirthData());
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(5, getMyRouteInfo());
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(6, itemRebirthCount_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(7, leftOut_);
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(8, location_);
    }
    if (((bitField0_ & 0x00000100) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(9, speedUpCount_);
    }
    if (((bitField0_ & 0x00000200) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(11, haveCommand_);
    }
    if (((bitField0_ & 0x00000400) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(12, curPower_);
    }
    if (((bitField0_ & 0x00000800) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(13, attackCd_);
    }
    if (((bitField0_ & 0x00001000) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(14, unionPointRank_);
    }
    if (((bitField0_ & 0x00002000) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(15, freeRebirthCount_);
    }
    for (int i = 0; i < lastChampionUnions_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(16, lastChampionUnions_.get(i));
    }
    if (((bitField0_ & 0x00004000) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(17, autoAtkCityId_);
    }
    if (((bitField0_ & 0x00008000) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(18, realmsType_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.HolyLandGameInfoRespMsg)) {
      return super.equals(obj);
    }
    xddq.pb.HolyLandGameInfoRespMsg other = (xddq.pb.HolyLandGameInfoRespMsg) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (hasBaseCamp() != other.hasBaseCamp()) return false;
    if (hasBaseCamp()) {
      if (getBaseCamp()
          != other.getBaseCamp()) return false;
    }
    if (hasFightValue() != other.hasFightValue()) return false;
    if (hasFightValue()) {
      if (!getFightValue()
          .equals(other.getFightValue())) return false;
    }
    if (hasRebirthData() != other.hasRebirthData()) return false;
    if (hasRebirthData()) {
      if (!getRebirthData()
          .equals(other.getRebirthData())) return false;
    }
    if (hasMyRouteInfo() != other.hasMyRouteInfo()) return false;
    if (hasMyRouteInfo()) {
      if (!getMyRouteInfo()
          .equals(other.getMyRouteInfo())) return false;
    }
    if (hasItemRebirthCount() != other.hasItemRebirthCount()) return false;
    if (hasItemRebirthCount()) {
      if (getItemRebirthCount()
          != other.getItemRebirthCount()) return false;
    }
    if (hasLeftOut() != other.hasLeftOut()) return false;
    if (hasLeftOut()) {
      if (getLeftOut()
          != other.getLeftOut()) return false;
    }
    if (hasLocation() != other.hasLocation()) return false;
    if (hasLocation()) {
      if (getLocation()
          != other.getLocation()) return false;
    }
    if (hasSpeedUpCount() != other.hasSpeedUpCount()) return false;
    if (hasSpeedUpCount()) {
      if (getSpeedUpCount()
          != other.getSpeedUpCount()) return false;
    }
    if (hasHaveCommand() != other.hasHaveCommand()) return false;
    if (hasHaveCommand()) {
      if (getHaveCommand()
          != other.getHaveCommand()) return false;
    }
    if (hasCurPower() != other.hasCurPower()) return false;
    if (hasCurPower()) {
      if (getCurPower()
          != other.getCurPower()) return false;
    }
    if (hasAttackCd() != other.hasAttackCd()) return false;
    if (hasAttackCd()) {
      if (getAttackCd()
          != other.getAttackCd()) return false;
    }
    if (hasUnionPointRank() != other.hasUnionPointRank()) return false;
    if (hasUnionPointRank()) {
      if (getUnionPointRank()
          != other.getUnionPointRank()) return false;
    }
    if (hasFreeRebirthCount() != other.hasFreeRebirthCount()) return false;
    if (hasFreeRebirthCount()) {
      if (getFreeRebirthCount()
          != other.getFreeRebirthCount()) return false;
    }
    if (!getLastChampionUnionsList()
        .equals(other.getLastChampionUnionsList())) return false;
    if (hasAutoAtkCityId() != other.hasAutoAtkCityId()) return false;
    if (hasAutoAtkCityId()) {
      if (getAutoAtkCityId()
          != other.getAutoAtkCityId()) return false;
    }
    if (hasRealmsType() != other.hasRealmsType()) return false;
    if (hasRealmsType()) {
      if (getRealmsType()
          != other.getRealmsType()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (hasBaseCamp()) {
      hash = (37 * hash) + BASECAMP_FIELD_NUMBER;
      hash = (53 * hash) + getBaseCamp();
    }
    if (hasFightValue()) {
      hash = (37 * hash) + FIGHTVALUE_FIELD_NUMBER;
      hash = (53 * hash) + getFightValue().hashCode();
    }
    if (hasRebirthData()) {
      hash = (37 * hash) + REBIRTHDATA_FIELD_NUMBER;
      hash = (53 * hash) + getRebirthData().hashCode();
    }
    if (hasMyRouteInfo()) {
      hash = (37 * hash) + MYROUTEINFO_FIELD_NUMBER;
      hash = (53 * hash) + getMyRouteInfo().hashCode();
    }
    if (hasItemRebirthCount()) {
      hash = (37 * hash) + ITEMREBIRTHCOUNT_FIELD_NUMBER;
      hash = (53 * hash) + getItemRebirthCount();
    }
    if (hasLeftOut()) {
      hash = (37 * hash) + LEFTOUT_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getLeftOut());
    }
    if (hasLocation()) {
      hash = (37 * hash) + LOCATION_FIELD_NUMBER;
      hash = (53 * hash) + getLocation();
    }
    if (hasSpeedUpCount()) {
      hash = (37 * hash) + SPEEDUPCOUNT_FIELD_NUMBER;
      hash = (53 * hash) + getSpeedUpCount();
    }
    if (hasHaveCommand()) {
      hash = (37 * hash) + HAVECOMMAND_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getHaveCommand());
    }
    if (hasCurPower()) {
      hash = (37 * hash) + CURPOWER_FIELD_NUMBER;
      hash = (53 * hash) + getCurPower();
    }
    if (hasAttackCd()) {
      hash = (37 * hash) + ATTACKCD_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getAttackCd());
    }
    if (hasUnionPointRank()) {
      hash = (37 * hash) + UNIONPOINTRANK_FIELD_NUMBER;
      hash = (53 * hash) + getUnionPointRank();
    }
    if (hasFreeRebirthCount()) {
      hash = (37 * hash) + FREEREBIRTHCOUNT_FIELD_NUMBER;
      hash = (53 * hash) + getFreeRebirthCount();
    }
    if (getLastChampionUnionsCount() > 0) {
      hash = (37 * hash) + LASTCHAMPIONUNIONS_FIELD_NUMBER;
      hash = (53 * hash) + getLastChampionUnionsList().hashCode();
    }
    if (hasAutoAtkCityId()) {
      hash = (37 * hash) + AUTOATKCITYID_FIELD_NUMBER;
      hash = (53 * hash) + getAutoAtkCityId();
    }
    if (hasRealmsType()) {
      hash = (37 * hash) + REALMSTYPE_FIELD_NUMBER;
      hash = (53 * hash) + getRealmsType();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.HolyLandGameInfoRespMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.HolyLandGameInfoRespMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.HolyLandGameInfoRespMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.HolyLandGameInfoRespMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.HolyLandGameInfoRespMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.HolyLandGameInfoRespMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.HolyLandGameInfoRespMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.HolyLandGameInfoRespMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.HolyLandGameInfoRespMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.HolyLandGameInfoRespMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.HolyLandGameInfoRespMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.HolyLandGameInfoRespMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.HolyLandGameInfoRespMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.HolyLandGameInfoRespMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.HolyLandGameInfoRespMsg)
      xddq.pb.HolyLandGameInfoRespMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_HolyLandGameInfoRespMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_HolyLandGameInfoRespMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.HolyLandGameInfoRespMsg.class, xddq.pb.HolyLandGameInfoRespMsg.Builder.class);
    }

    // Construct using xddq.pb.HolyLandGameInfoRespMsg.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetRebirthDataFieldBuilder();
        internalGetMyRouteInfoFieldBuilder();
        internalGetLastChampionUnionsFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      baseCamp_ = 0;
      fightValue_ = "";
      rebirthData_ = null;
      if (rebirthDataBuilder_ != null) {
        rebirthDataBuilder_.dispose();
        rebirthDataBuilder_ = null;
      }
      myRouteInfo_ = null;
      if (myRouteInfoBuilder_ != null) {
        myRouteInfoBuilder_.dispose();
        myRouteInfoBuilder_ = null;
      }
      itemRebirthCount_ = 0;
      leftOut_ = false;
      location_ = 0;
      speedUpCount_ = 0;
      haveCommand_ = false;
      curPower_ = 0;
      attackCd_ = 0L;
      unionPointRank_ = 0;
      freeRebirthCount_ = 0;
      if (lastChampionUnionsBuilder_ == null) {
        lastChampionUnions_ = java.util.Collections.emptyList();
      } else {
        lastChampionUnions_ = null;
        lastChampionUnionsBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00004000);
      autoAtkCityId_ = 0;
      realmsType_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_HolyLandGameInfoRespMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.HolyLandGameInfoRespMsg getDefaultInstanceForType() {
      return xddq.pb.HolyLandGameInfoRespMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.HolyLandGameInfoRespMsg build() {
      xddq.pb.HolyLandGameInfoRespMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.HolyLandGameInfoRespMsg buildPartial() {
      xddq.pb.HolyLandGameInfoRespMsg result = new xddq.pb.HolyLandGameInfoRespMsg(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.HolyLandGameInfoRespMsg result) {
      if (lastChampionUnionsBuilder_ == null) {
        if (((bitField0_ & 0x00004000) != 0)) {
          lastChampionUnions_ = java.util.Collections.unmodifiableList(lastChampionUnions_);
          bitField0_ = (bitField0_ & ~0x00004000);
        }
        result.lastChampionUnions_ = lastChampionUnions_;
      } else {
        result.lastChampionUnions_ = lastChampionUnionsBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.HolyLandGameInfoRespMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.baseCamp_ = baseCamp_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.fightValue_ = fightValue_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.rebirthData_ = rebirthDataBuilder_ == null
            ? rebirthData_
            : rebirthDataBuilder_.build();
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.myRouteInfo_ = myRouteInfoBuilder_ == null
            ? myRouteInfo_
            : myRouteInfoBuilder_.build();
        to_bitField0_ |= 0x00000010;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.itemRebirthCount_ = itemRebirthCount_;
        to_bitField0_ |= 0x00000020;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.leftOut_ = leftOut_;
        to_bitField0_ |= 0x00000040;
      }
      if (((from_bitField0_ & 0x00000080) != 0)) {
        result.location_ = location_;
        to_bitField0_ |= 0x00000080;
      }
      if (((from_bitField0_ & 0x00000100) != 0)) {
        result.speedUpCount_ = speedUpCount_;
        to_bitField0_ |= 0x00000100;
      }
      if (((from_bitField0_ & 0x00000200) != 0)) {
        result.haveCommand_ = haveCommand_;
        to_bitField0_ |= 0x00000200;
      }
      if (((from_bitField0_ & 0x00000400) != 0)) {
        result.curPower_ = curPower_;
        to_bitField0_ |= 0x00000400;
      }
      if (((from_bitField0_ & 0x00000800) != 0)) {
        result.attackCd_ = attackCd_;
        to_bitField0_ |= 0x00000800;
      }
      if (((from_bitField0_ & 0x00001000) != 0)) {
        result.unionPointRank_ = unionPointRank_;
        to_bitField0_ |= 0x00001000;
      }
      if (((from_bitField0_ & 0x00002000) != 0)) {
        result.freeRebirthCount_ = freeRebirthCount_;
        to_bitField0_ |= 0x00002000;
      }
      if (((from_bitField0_ & 0x00008000) != 0)) {
        result.autoAtkCityId_ = autoAtkCityId_;
        to_bitField0_ |= 0x00004000;
      }
      if (((from_bitField0_ & 0x00010000) != 0)) {
        result.realmsType_ = realmsType_;
        to_bitField0_ |= 0x00008000;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.HolyLandGameInfoRespMsg) {
        return mergeFrom((xddq.pb.HolyLandGameInfoRespMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.HolyLandGameInfoRespMsg other) {
      if (other == xddq.pb.HolyLandGameInfoRespMsg.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (other.hasBaseCamp()) {
        setBaseCamp(other.getBaseCamp());
      }
      if (other.hasFightValue()) {
        fightValue_ = other.fightValue_;
        bitField0_ |= 0x00000004;
        onChanged();
      }
      if (other.hasRebirthData()) {
        mergeRebirthData(other.getRebirthData());
      }
      if (other.hasMyRouteInfo()) {
        mergeMyRouteInfo(other.getMyRouteInfo());
      }
      if (other.hasItemRebirthCount()) {
        setItemRebirthCount(other.getItemRebirthCount());
      }
      if (other.hasLeftOut()) {
        setLeftOut(other.getLeftOut());
      }
      if (other.hasLocation()) {
        setLocation(other.getLocation());
      }
      if (other.hasSpeedUpCount()) {
        setSpeedUpCount(other.getSpeedUpCount());
      }
      if (other.hasHaveCommand()) {
        setHaveCommand(other.getHaveCommand());
      }
      if (other.hasCurPower()) {
        setCurPower(other.getCurPower());
      }
      if (other.hasAttackCd()) {
        setAttackCd(other.getAttackCd());
      }
      if (other.hasUnionPointRank()) {
        setUnionPointRank(other.getUnionPointRank());
      }
      if (other.hasFreeRebirthCount()) {
        setFreeRebirthCount(other.getFreeRebirthCount());
      }
      if (lastChampionUnionsBuilder_ == null) {
        if (!other.lastChampionUnions_.isEmpty()) {
          if (lastChampionUnions_.isEmpty()) {
            lastChampionUnions_ = other.lastChampionUnions_;
            bitField0_ = (bitField0_ & ~0x00004000);
          } else {
            ensureLastChampionUnionsIsMutable();
            lastChampionUnions_.addAll(other.lastChampionUnions_);
          }
          onChanged();
        }
      } else {
        if (!other.lastChampionUnions_.isEmpty()) {
          if (lastChampionUnionsBuilder_.isEmpty()) {
            lastChampionUnionsBuilder_.dispose();
            lastChampionUnionsBuilder_ = null;
            lastChampionUnions_ = other.lastChampionUnions_;
            bitField0_ = (bitField0_ & ~0x00004000);
            lastChampionUnionsBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetLastChampionUnionsFieldBuilder() : null;
          } else {
            lastChampionUnionsBuilder_.addAllMessages(other.lastChampionUnions_);
          }
        }
      }
      if (other.hasAutoAtkCityId()) {
        setAutoAtkCityId(other.getAutoAtkCityId());
      }
      if (other.hasRealmsType()) {
        setRealmsType(other.getRealmsType());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              baseCamp_ = input.readInt32();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 26: {
              fightValue_ = input.readBytes();
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 34: {
              input.readMessage(
                  internalGetRebirthDataFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000008;
              break;
            } // case 34
            case 42: {
              input.readMessage(
                  internalGetMyRouteInfoFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000010;
              break;
            } // case 42
            case 48: {
              itemRebirthCount_ = input.readInt32();
              bitField0_ |= 0x00000020;
              break;
            } // case 48
            case 56: {
              leftOut_ = input.readBool();
              bitField0_ |= 0x00000040;
              break;
            } // case 56
            case 64: {
              location_ = input.readInt32();
              bitField0_ |= 0x00000080;
              break;
            } // case 64
            case 72: {
              speedUpCount_ = input.readInt32();
              bitField0_ |= 0x00000100;
              break;
            } // case 72
            case 88: {
              haveCommand_ = input.readBool();
              bitField0_ |= 0x00000200;
              break;
            } // case 88
            case 96: {
              curPower_ = input.readInt32();
              bitField0_ |= 0x00000400;
              break;
            } // case 96
            case 104: {
              attackCd_ = input.readInt64();
              bitField0_ |= 0x00000800;
              break;
            } // case 104
            case 112: {
              unionPointRank_ = input.readInt32();
              bitField0_ |= 0x00001000;
              break;
            } // case 112
            case 120: {
              freeRebirthCount_ = input.readInt32();
              bitField0_ |= 0x00002000;
              break;
            } // case 120
            case 130: {
              xddq.pb.UnionRankHistoryMsg m =
                  input.readMessage(
                      xddq.pb.UnionRankHistoryMsg.parser(),
                      extensionRegistry);
              if (lastChampionUnionsBuilder_ == null) {
                ensureLastChampionUnionsIsMutable();
                lastChampionUnions_.add(m);
              } else {
                lastChampionUnionsBuilder_.addMessage(m);
              }
              break;
            } // case 130
            case 136: {
              autoAtkCityId_ = input.readInt32();
              bitField0_ |= 0x00008000;
              break;
            } // case 136
            case 144: {
              realmsType_ = input.readInt32();
              bitField0_ |= 0x00010000;
              break;
            } // case 144
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private int baseCamp_ ;
    /**
     * <code>optional int32 baseCamp = 2;</code>
     * @return Whether the baseCamp field is set.
     */
    @java.lang.Override
    public boolean hasBaseCamp() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 baseCamp = 2;</code>
     * @return The baseCamp.
     */
    @java.lang.Override
    public int getBaseCamp() {
      return baseCamp_;
    }
    /**
     * <code>optional int32 baseCamp = 2;</code>
     * @param value The baseCamp to set.
     * @return This builder for chaining.
     */
    public Builder setBaseCamp(int value) {

      baseCamp_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 baseCamp = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearBaseCamp() {
      bitField0_ = (bitField0_ & ~0x00000002);
      baseCamp_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object fightValue_ = "";
    /**
     * <code>optional string fightValue = 3;</code>
     * @return Whether the fightValue field is set.
     */
    public boolean hasFightValue() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional string fightValue = 3;</code>
     * @return The fightValue.
     */
    public java.lang.String getFightValue() {
      java.lang.Object ref = fightValue_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          fightValue_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string fightValue = 3;</code>
     * @return The bytes for fightValue.
     */
    public com.google.protobuf.ByteString
        getFightValueBytes() {
      java.lang.Object ref = fightValue_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        fightValue_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string fightValue = 3;</code>
     * @param value The fightValue to set.
     * @return This builder for chaining.
     */
    public Builder setFightValue(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      fightValue_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional string fightValue = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearFightValue() {
      fightValue_ = getDefaultInstance().getFightValue();
      bitField0_ = (bitField0_ & ~0x00000004);
      onChanged();
      return this;
    }
    /**
     * <code>optional string fightValue = 3;</code>
     * @param value The bytes for fightValue to set.
     * @return This builder for chaining.
     */
    public Builder setFightValueBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      fightValue_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }

    private xddq.pb.HolyLandBeenKillSyncMsg rebirthData_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.HolyLandBeenKillSyncMsg, xddq.pb.HolyLandBeenKillSyncMsg.Builder, xddq.pb.HolyLandBeenKillSyncMsgOrBuilder> rebirthDataBuilder_;
    /**
     * <code>optional .xddq.pb.HolyLandBeenKillSyncMsg rebirthData = 4;</code>
     * @return Whether the rebirthData field is set.
     */
    public boolean hasRebirthData() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional .xddq.pb.HolyLandBeenKillSyncMsg rebirthData = 4;</code>
     * @return The rebirthData.
     */
    public xddq.pb.HolyLandBeenKillSyncMsg getRebirthData() {
      if (rebirthDataBuilder_ == null) {
        return rebirthData_ == null ? xddq.pb.HolyLandBeenKillSyncMsg.getDefaultInstance() : rebirthData_;
      } else {
        return rebirthDataBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.HolyLandBeenKillSyncMsg rebirthData = 4;</code>
     */
    public Builder setRebirthData(xddq.pb.HolyLandBeenKillSyncMsg value) {
      if (rebirthDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        rebirthData_ = value;
      } else {
        rebirthDataBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.HolyLandBeenKillSyncMsg rebirthData = 4;</code>
     */
    public Builder setRebirthData(
        xddq.pb.HolyLandBeenKillSyncMsg.Builder builderForValue) {
      if (rebirthDataBuilder_ == null) {
        rebirthData_ = builderForValue.build();
      } else {
        rebirthDataBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.HolyLandBeenKillSyncMsg rebirthData = 4;</code>
     */
    public Builder mergeRebirthData(xddq.pb.HolyLandBeenKillSyncMsg value) {
      if (rebirthDataBuilder_ == null) {
        if (((bitField0_ & 0x00000008) != 0) &&
          rebirthData_ != null &&
          rebirthData_ != xddq.pb.HolyLandBeenKillSyncMsg.getDefaultInstance()) {
          getRebirthDataBuilder().mergeFrom(value);
        } else {
          rebirthData_ = value;
        }
      } else {
        rebirthDataBuilder_.mergeFrom(value);
      }
      if (rebirthData_ != null) {
        bitField0_ |= 0x00000008;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.HolyLandBeenKillSyncMsg rebirthData = 4;</code>
     */
    public Builder clearRebirthData() {
      bitField0_ = (bitField0_ & ~0x00000008);
      rebirthData_ = null;
      if (rebirthDataBuilder_ != null) {
        rebirthDataBuilder_.dispose();
        rebirthDataBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.HolyLandBeenKillSyncMsg rebirthData = 4;</code>
     */
    public xddq.pb.HolyLandBeenKillSyncMsg.Builder getRebirthDataBuilder() {
      bitField0_ |= 0x00000008;
      onChanged();
      return internalGetRebirthDataFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.HolyLandBeenKillSyncMsg rebirthData = 4;</code>
     */
    public xddq.pb.HolyLandBeenKillSyncMsgOrBuilder getRebirthDataOrBuilder() {
      if (rebirthDataBuilder_ != null) {
        return rebirthDataBuilder_.getMessageOrBuilder();
      } else {
        return rebirthData_ == null ?
            xddq.pb.HolyLandBeenKillSyncMsg.getDefaultInstance() : rebirthData_;
      }
    }
    /**
     * <code>optional .xddq.pb.HolyLandBeenKillSyncMsg rebirthData = 4;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.HolyLandBeenKillSyncMsg, xddq.pb.HolyLandBeenKillSyncMsg.Builder, xddq.pb.HolyLandBeenKillSyncMsgOrBuilder> 
        internalGetRebirthDataFieldBuilder() {
      if (rebirthDataBuilder_ == null) {
        rebirthDataBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.HolyLandBeenKillSyncMsg, xddq.pb.HolyLandBeenKillSyncMsg.Builder, xddq.pb.HolyLandBeenKillSyncMsgOrBuilder>(
                getRebirthData(),
                getParentForChildren(),
                isClean());
        rebirthData_ = null;
      }
      return rebirthDataBuilder_;
    }

    private xddq.pb.HolyLandGameRouteInfo myRouteInfo_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.HolyLandGameRouteInfo, xddq.pb.HolyLandGameRouteInfo.Builder, xddq.pb.HolyLandGameRouteInfoOrBuilder> myRouteInfoBuilder_;
    /**
     * <code>optional .xddq.pb.HolyLandGameRouteInfo myRouteInfo = 5;</code>
     * @return Whether the myRouteInfo field is set.
     */
    public boolean hasMyRouteInfo() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional .xddq.pb.HolyLandGameRouteInfo myRouteInfo = 5;</code>
     * @return The myRouteInfo.
     */
    public xddq.pb.HolyLandGameRouteInfo getMyRouteInfo() {
      if (myRouteInfoBuilder_ == null) {
        return myRouteInfo_ == null ? xddq.pb.HolyLandGameRouteInfo.getDefaultInstance() : myRouteInfo_;
      } else {
        return myRouteInfoBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.HolyLandGameRouteInfo myRouteInfo = 5;</code>
     */
    public Builder setMyRouteInfo(xddq.pb.HolyLandGameRouteInfo value) {
      if (myRouteInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        myRouteInfo_ = value;
      } else {
        myRouteInfoBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.HolyLandGameRouteInfo myRouteInfo = 5;</code>
     */
    public Builder setMyRouteInfo(
        xddq.pb.HolyLandGameRouteInfo.Builder builderForValue) {
      if (myRouteInfoBuilder_ == null) {
        myRouteInfo_ = builderForValue.build();
      } else {
        myRouteInfoBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.HolyLandGameRouteInfo myRouteInfo = 5;</code>
     */
    public Builder mergeMyRouteInfo(xddq.pb.HolyLandGameRouteInfo value) {
      if (myRouteInfoBuilder_ == null) {
        if (((bitField0_ & 0x00000010) != 0) &&
          myRouteInfo_ != null &&
          myRouteInfo_ != xddq.pb.HolyLandGameRouteInfo.getDefaultInstance()) {
          getMyRouteInfoBuilder().mergeFrom(value);
        } else {
          myRouteInfo_ = value;
        }
      } else {
        myRouteInfoBuilder_.mergeFrom(value);
      }
      if (myRouteInfo_ != null) {
        bitField0_ |= 0x00000010;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.HolyLandGameRouteInfo myRouteInfo = 5;</code>
     */
    public Builder clearMyRouteInfo() {
      bitField0_ = (bitField0_ & ~0x00000010);
      myRouteInfo_ = null;
      if (myRouteInfoBuilder_ != null) {
        myRouteInfoBuilder_.dispose();
        myRouteInfoBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.HolyLandGameRouteInfo myRouteInfo = 5;</code>
     */
    public xddq.pb.HolyLandGameRouteInfo.Builder getMyRouteInfoBuilder() {
      bitField0_ |= 0x00000010;
      onChanged();
      return internalGetMyRouteInfoFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.HolyLandGameRouteInfo myRouteInfo = 5;</code>
     */
    public xddq.pb.HolyLandGameRouteInfoOrBuilder getMyRouteInfoOrBuilder() {
      if (myRouteInfoBuilder_ != null) {
        return myRouteInfoBuilder_.getMessageOrBuilder();
      } else {
        return myRouteInfo_ == null ?
            xddq.pb.HolyLandGameRouteInfo.getDefaultInstance() : myRouteInfo_;
      }
    }
    /**
     * <code>optional .xddq.pb.HolyLandGameRouteInfo myRouteInfo = 5;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.HolyLandGameRouteInfo, xddq.pb.HolyLandGameRouteInfo.Builder, xddq.pb.HolyLandGameRouteInfoOrBuilder> 
        internalGetMyRouteInfoFieldBuilder() {
      if (myRouteInfoBuilder_ == null) {
        myRouteInfoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.HolyLandGameRouteInfo, xddq.pb.HolyLandGameRouteInfo.Builder, xddq.pb.HolyLandGameRouteInfoOrBuilder>(
                getMyRouteInfo(),
                getParentForChildren(),
                isClean());
        myRouteInfo_ = null;
      }
      return myRouteInfoBuilder_;
    }

    private int itemRebirthCount_ ;
    /**
     * <code>optional int32 itemRebirthCount = 6;</code>
     * @return Whether the itemRebirthCount field is set.
     */
    @java.lang.Override
    public boolean hasItemRebirthCount() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional int32 itemRebirthCount = 6;</code>
     * @return The itemRebirthCount.
     */
    @java.lang.Override
    public int getItemRebirthCount() {
      return itemRebirthCount_;
    }
    /**
     * <code>optional int32 itemRebirthCount = 6;</code>
     * @param value The itemRebirthCount to set.
     * @return This builder for chaining.
     */
    public Builder setItemRebirthCount(int value) {

      itemRebirthCount_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 itemRebirthCount = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearItemRebirthCount() {
      bitField0_ = (bitField0_ & ~0x00000020);
      itemRebirthCount_ = 0;
      onChanged();
      return this;
    }

    private boolean leftOut_ ;
    /**
     * <code>optional bool leftOut = 7;</code>
     * @return Whether the leftOut field is set.
     */
    @java.lang.Override
    public boolean hasLeftOut() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <code>optional bool leftOut = 7;</code>
     * @return The leftOut.
     */
    @java.lang.Override
    public boolean getLeftOut() {
      return leftOut_;
    }
    /**
     * <code>optional bool leftOut = 7;</code>
     * @param value The leftOut to set.
     * @return This builder for chaining.
     */
    public Builder setLeftOut(boolean value) {

      leftOut_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>optional bool leftOut = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearLeftOut() {
      bitField0_ = (bitField0_ & ~0x00000040);
      leftOut_ = false;
      onChanged();
      return this;
    }

    private int location_ ;
    /**
     * <code>optional int32 location = 8;</code>
     * @return Whether the location field is set.
     */
    @java.lang.Override
    public boolean hasLocation() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <code>optional int32 location = 8;</code>
     * @return The location.
     */
    @java.lang.Override
    public int getLocation() {
      return location_;
    }
    /**
     * <code>optional int32 location = 8;</code>
     * @param value The location to set.
     * @return This builder for chaining.
     */
    public Builder setLocation(int value) {

      location_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 location = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearLocation() {
      bitField0_ = (bitField0_ & ~0x00000080);
      location_ = 0;
      onChanged();
      return this;
    }

    private int speedUpCount_ ;
    /**
     * <code>optional int32 speedUpCount = 9;</code>
     * @return Whether the speedUpCount field is set.
     */
    @java.lang.Override
    public boolean hasSpeedUpCount() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <code>optional int32 speedUpCount = 9;</code>
     * @return The speedUpCount.
     */
    @java.lang.Override
    public int getSpeedUpCount() {
      return speedUpCount_;
    }
    /**
     * <code>optional int32 speedUpCount = 9;</code>
     * @param value The speedUpCount to set.
     * @return This builder for chaining.
     */
    public Builder setSpeedUpCount(int value) {

      speedUpCount_ = value;
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 speedUpCount = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearSpeedUpCount() {
      bitField0_ = (bitField0_ & ~0x00000100);
      speedUpCount_ = 0;
      onChanged();
      return this;
    }

    private boolean haveCommand_ ;
    /**
     * <code>optional bool haveCommand = 11;</code>
     * @return Whether the haveCommand field is set.
     */
    @java.lang.Override
    public boolean hasHaveCommand() {
      return ((bitField0_ & 0x00000200) != 0);
    }
    /**
     * <code>optional bool haveCommand = 11;</code>
     * @return The haveCommand.
     */
    @java.lang.Override
    public boolean getHaveCommand() {
      return haveCommand_;
    }
    /**
     * <code>optional bool haveCommand = 11;</code>
     * @param value The haveCommand to set.
     * @return This builder for chaining.
     */
    public Builder setHaveCommand(boolean value) {

      haveCommand_ = value;
      bitField0_ |= 0x00000200;
      onChanged();
      return this;
    }
    /**
     * <code>optional bool haveCommand = 11;</code>
     * @return This builder for chaining.
     */
    public Builder clearHaveCommand() {
      bitField0_ = (bitField0_ & ~0x00000200);
      haveCommand_ = false;
      onChanged();
      return this;
    }

    private int curPower_ ;
    /**
     * <code>optional int32 curPower = 12;</code>
     * @return Whether the curPower field is set.
     */
    @java.lang.Override
    public boolean hasCurPower() {
      return ((bitField0_ & 0x00000400) != 0);
    }
    /**
     * <code>optional int32 curPower = 12;</code>
     * @return The curPower.
     */
    @java.lang.Override
    public int getCurPower() {
      return curPower_;
    }
    /**
     * <code>optional int32 curPower = 12;</code>
     * @param value The curPower to set.
     * @return This builder for chaining.
     */
    public Builder setCurPower(int value) {

      curPower_ = value;
      bitField0_ |= 0x00000400;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 curPower = 12;</code>
     * @return This builder for chaining.
     */
    public Builder clearCurPower() {
      bitField0_ = (bitField0_ & ~0x00000400);
      curPower_ = 0;
      onChanged();
      return this;
    }

    private long attackCd_ ;
    /**
     * <code>optional int64 attackCd = 13;</code>
     * @return Whether the attackCd field is set.
     */
    @java.lang.Override
    public boolean hasAttackCd() {
      return ((bitField0_ & 0x00000800) != 0);
    }
    /**
     * <code>optional int64 attackCd = 13;</code>
     * @return The attackCd.
     */
    @java.lang.Override
    public long getAttackCd() {
      return attackCd_;
    }
    /**
     * <code>optional int64 attackCd = 13;</code>
     * @param value The attackCd to set.
     * @return This builder for chaining.
     */
    public Builder setAttackCd(long value) {

      attackCd_ = value;
      bitField0_ |= 0x00000800;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 attackCd = 13;</code>
     * @return This builder for chaining.
     */
    public Builder clearAttackCd() {
      bitField0_ = (bitField0_ & ~0x00000800);
      attackCd_ = 0L;
      onChanged();
      return this;
    }

    private int unionPointRank_ ;
    /**
     * <code>optional int32 unionPointRank = 14;</code>
     * @return Whether the unionPointRank field is set.
     */
    @java.lang.Override
    public boolean hasUnionPointRank() {
      return ((bitField0_ & 0x00001000) != 0);
    }
    /**
     * <code>optional int32 unionPointRank = 14;</code>
     * @return The unionPointRank.
     */
    @java.lang.Override
    public int getUnionPointRank() {
      return unionPointRank_;
    }
    /**
     * <code>optional int32 unionPointRank = 14;</code>
     * @param value The unionPointRank to set.
     * @return This builder for chaining.
     */
    public Builder setUnionPointRank(int value) {

      unionPointRank_ = value;
      bitField0_ |= 0x00001000;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 unionPointRank = 14;</code>
     * @return This builder for chaining.
     */
    public Builder clearUnionPointRank() {
      bitField0_ = (bitField0_ & ~0x00001000);
      unionPointRank_ = 0;
      onChanged();
      return this;
    }

    private int freeRebirthCount_ ;
    /**
     * <code>optional int32 freeRebirthCount = 15;</code>
     * @return Whether the freeRebirthCount field is set.
     */
    @java.lang.Override
    public boolean hasFreeRebirthCount() {
      return ((bitField0_ & 0x00002000) != 0);
    }
    /**
     * <code>optional int32 freeRebirthCount = 15;</code>
     * @return The freeRebirthCount.
     */
    @java.lang.Override
    public int getFreeRebirthCount() {
      return freeRebirthCount_;
    }
    /**
     * <code>optional int32 freeRebirthCount = 15;</code>
     * @param value The freeRebirthCount to set.
     * @return This builder for chaining.
     */
    public Builder setFreeRebirthCount(int value) {

      freeRebirthCount_ = value;
      bitField0_ |= 0x00002000;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 freeRebirthCount = 15;</code>
     * @return This builder for chaining.
     */
    public Builder clearFreeRebirthCount() {
      bitField0_ = (bitField0_ & ~0x00002000);
      freeRebirthCount_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.UnionRankHistoryMsg> lastChampionUnions_ =
      java.util.Collections.emptyList();
    private void ensureLastChampionUnionsIsMutable() {
      if (!((bitField0_ & 0x00004000) != 0)) {
        lastChampionUnions_ = new java.util.ArrayList<xddq.pb.UnionRankHistoryMsg>(lastChampionUnions_);
        bitField0_ |= 0x00004000;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.UnionRankHistoryMsg, xddq.pb.UnionRankHistoryMsg.Builder, xddq.pb.UnionRankHistoryMsgOrBuilder> lastChampionUnionsBuilder_;

    /**
     * <code>repeated .xddq.pb.UnionRankHistoryMsg lastChampionUnions = 16;</code>
     */
    public java.util.List<xddq.pb.UnionRankHistoryMsg> getLastChampionUnionsList() {
      if (lastChampionUnionsBuilder_ == null) {
        return java.util.Collections.unmodifiableList(lastChampionUnions_);
      } else {
        return lastChampionUnionsBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.UnionRankHistoryMsg lastChampionUnions = 16;</code>
     */
    public int getLastChampionUnionsCount() {
      if (lastChampionUnionsBuilder_ == null) {
        return lastChampionUnions_.size();
      } else {
        return lastChampionUnionsBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.UnionRankHistoryMsg lastChampionUnions = 16;</code>
     */
    public xddq.pb.UnionRankHistoryMsg getLastChampionUnions(int index) {
      if (lastChampionUnionsBuilder_ == null) {
        return lastChampionUnions_.get(index);
      } else {
        return lastChampionUnionsBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.UnionRankHistoryMsg lastChampionUnions = 16;</code>
     */
    public Builder setLastChampionUnions(
        int index, xddq.pb.UnionRankHistoryMsg value) {
      if (lastChampionUnionsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureLastChampionUnionsIsMutable();
        lastChampionUnions_.set(index, value);
        onChanged();
      } else {
        lastChampionUnionsBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionRankHistoryMsg lastChampionUnions = 16;</code>
     */
    public Builder setLastChampionUnions(
        int index, xddq.pb.UnionRankHistoryMsg.Builder builderForValue) {
      if (lastChampionUnionsBuilder_ == null) {
        ensureLastChampionUnionsIsMutable();
        lastChampionUnions_.set(index, builderForValue.build());
        onChanged();
      } else {
        lastChampionUnionsBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionRankHistoryMsg lastChampionUnions = 16;</code>
     */
    public Builder addLastChampionUnions(xddq.pb.UnionRankHistoryMsg value) {
      if (lastChampionUnionsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureLastChampionUnionsIsMutable();
        lastChampionUnions_.add(value);
        onChanged();
      } else {
        lastChampionUnionsBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionRankHistoryMsg lastChampionUnions = 16;</code>
     */
    public Builder addLastChampionUnions(
        int index, xddq.pb.UnionRankHistoryMsg value) {
      if (lastChampionUnionsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureLastChampionUnionsIsMutable();
        lastChampionUnions_.add(index, value);
        onChanged();
      } else {
        lastChampionUnionsBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionRankHistoryMsg lastChampionUnions = 16;</code>
     */
    public Builder addLastChampionUnions(
        xddq.pb.UnionRankHistoryMsg.Builder builderForValue) {
      if (lastChampionUnionsBuilder_ == null) {
        ensureLastChampionUnionsIsMutable();
        lastChampionUnions_.add(builderForValue.build());
        onChanged();
      } else {
        lastChampionUnionsBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionRankHistoryMsg lastChampionUnions = 16;</code>
     */
    public Builder addLastChampionUnions(
        int index, xddq.pb.UnionRankHistoryMsg.Builder builderForValue) {
      if (lastChampionUnionsBuilder_ == null) {
        ensureLastChampionUnionsIsMutable();
        lastChampionUnions_.add(index, builderForValue.build());
        onChanged();
      } else {
        lastChampionUnionsBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionRankHistoryMsg lastChampionUnions = 16;</code>
     */
    public Builder addAllLastChampionUnions(
        java.lang.Iterable<? extends xddq.pb.UnionRankHistoryMsg> values) {
      if (lastChampionUnionsBuilder_ == null) {
        ensureLastChampionUnionsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, lastChampionUnions_);
        onChanged();
      } else {
        lastChampionUnionsBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionRankHistoryMsg lastChampionUnions = 16;</code>
     */
    public Builder clearLastChampionUnions() {
      if (lastChampionUnionsBuilder_ == null) {
        lastChampionUnions_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00004000);
        onChanged();
      } else {
        lastChampionUnionsBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionRankHistoryMsg lastChampionUnions = 16;</code>
     */
    public Builder removeLastChampionUnions(int index) {
      if (lastChampionUnionsBuilder_ == null) {
        ensureLastChampionUnionsIsMutable();
        lastChampionUnions_.remove(index);
        onChanged();
      } else {
        lastChampionUnionsBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionRankHistoryMsg lastChampionUnions = 16;</code>
     */
    public xddq.pb.UnionRankHistoryMsg.Builder getLastChampionUnionsBuilder(
        int index) {
      return internalGetLastChampionUnionsFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.UnionRankHistoryMsg lastChampionUnions = 16;</code>
     */
    public xddq.pb.UnionRankHistoryMsgOrBuilder getLastChampionUnionsOrBuilder(
        int index) {
      if (lastChampionUnionsBuilder_ == null) {
        return lastChampionUnions_.get(index);  } else {
        return lastChampionUnionsBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.UnionRankHistoryMsg lastChampionUnions = 16;</code>
     */
    public java.util.List<? extends xddq.pb.UnionRankHistoryMsgOrBuilder> 
         getLastChampionUnionsOrBuilderList() {
      if (lastChampionUnionsBuilder_ != null) {
        return lastChampionUnionsBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(lastChampionUnions_);
      }
    }
    /**
     * <code>repeated .xddq.pb.UnionRankHistoryMsg lastChampionUnions = 16;</code>
     */
    public xddq.pb.UnionRankHistoryMsg.Builder addLastChampionUnionsBuilder() {
      return internalGetLastChampionUnionsFieldBuilder().addBuilder(
          xddq.pb.UnionRankHistoryMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.UnionRankHistoryMsg lastChampionUnions = 16;</code>
     */
    public xddq.pb.UnionRankHistoryMsg.Builder addLastChampionUnionsBuilder(
        int index) {
      return internalGetLastChampionUnionsFieldBuilder().addBuilder(
          index, xddq.pb.UnionRankHistoryMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.UnionRankHistoryMsg lastChampionUnions = 16;</code>
     */
    public java.util.List<xddq.pb.UnionRankHistoryMsg.Builder> 
         getLastChampionUnionsBuilderList() {
      return internalGetLastChampionUnionsFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.UnionRankHistoryMsg, xddq.pb.UnionRankHistoryMsg.Builder, xddq.pb.UnionRankHistoryMsgOrBuilder> 
        internalGetLastChampionUnionsFieldBuilder() {
      if (lastChampionUnionsBuilder_ == null) {
        lastChampionUnionsBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.UnionRankHistoryMsg, xddq.pb.UnionRankHistoryMsg.Builder, xddq.pb.UnionRankHistoryMsgOrBuilder>(
                lastChampionUnions_,
                ((bitField0_ & 0x00004000) != 0),
                getParentForChildren(),
                isClean());
        lastChampionUnions_ = null;
      }
      return lastChampionUnionsBuilder_;
    }

    private int autoAtkCityId_ ;
    /**
     * <code>optional int32 autoAtkCityId = 17;</code>
     * @return Whether the autoAtkCityId field is set.
     */
    @java.lang.Override
    public boolean hasAutoAtkCityId() {
      return ((bitField0_ & 0x00008000) != 0);
    }
    /**
     * <code>optional int32 autoAtkCityId = 17;</code>
     * @return The autoAtkCityId.
     */
    @java.lang.Override
    public int getAutoAtkCityId() {
      return autoAtkCityId_;
    }
    /**
     * <code>optional int32 autoAtkCityId = 17;</code>
     * @param value The autoAtkCityId to set.
     * @return This builder for chaining.
     */
    public Builder setAutoAtkCityId(int value) {

      autoAtkCityId_ = value;
      bitField0_ |= 0x00008000;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 autoAtkCityId = 17;</code>
     * @return This builder for chaining.
     */
    public Builder clearAutoAtkCityId() {
      bitField0_ = (bitField0_ & ~0x00008000);
      autoAtkCityId_ = 0;
      onChanged();
      return this;
    }

    private int realmsType_ ;
    /**
     * <code>optional int32 realmsType = 18;</code>
     * @return Whether the realmsType field is set.
     */
    @java.lang.Override
    public boolean hasRealmsType() {
      return ((bitField0_ & 0x00010000) != 0);
    }
    /**
     * <code>optional int32 realmsType = 18;</code>
     * @return The realmsType.
     */
    @java.lang.Override
    public int getRealmsType() {
      return realmsType_;
    }
    /**
     * <code>optional int32 realmsType = 18;</code>
     * @param value The realmsType to set.
     * @return This builder for chaining.
     */
    public Builder setRealmsType(int value) {

      realmsType_ = value;
      bitField0_ |= 0x00010000;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 realmsType = 18;</code>
     * @return This builder for chaining.
     */
    public Builder clearRealmsType() {
      bitField0_ = (bitField0_ & ~0x00010000);
      realmsType_ = 0;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.HolyLandGameInfoRespMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.HolyLandGameInfoRespMsg)
  private static final xddq.pb.HolyLandGameInfoRespMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.HolyLandGameInfoRespMsg();
  }

  public static xddq.pb.HolyLandGameInfoRespMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<HolyLandGameInfoRespMsg>
      PARSER = new com.google.protobuf.AbstractParser<HolyLandGameInfoRespMsg>() {
    @java.lang.Override
    public HolyLandGameInfoRespMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<HolyLandGameInfoRespMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<HolyLandGameInfoRespMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.HolyLandGameInfoRespMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

