// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.UnionHonorHallSettleSync}
 */
public final class UnionHonorHallSettleSync extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.UnionHonorHallSettleSync)
    UnionHonorHallSettleSyncOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      UnionHonorHallSettleSync.class.getName());
  }
  // Use UnionHonorHallSettleSync.newBuilder() to construct.
  private UnionHonorHallSettleSync(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private UnionHonorHallSettleSync() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_UnionHonorHallSettleSync_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_UnionHonorHallSettleSync_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.UnionHonorHallSettleSync.class, xddq.pb.UnionHonorHallSettleSync.Builder.class);
  }

  private int bitField0_;
  public static final int WEEKSETTLE_FIELD_NUMBER = 1;
  private xddq.pb.UnionHonorHallWeekSettleMsg weekSettle_;
  /**
   * <code>optional .xddq.pb.UnionHonorHallWeekSettleMsg weekSettle = 1;</code>
   * @return Whether the weekSettle field is set.
   */
  @java.lang.Override
  public boolean hasWeekSettle() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional .xddq.pb.UnionHonorHallWeekSettleMsg weekSettle = 1;</code>
   * @return The weekSettle.
   */
  @java.lang.Override
  public xddq.pb.UnionHonorHallWeekSettleMsg getWeekSettle() {
    return weekSettle_ == null ? xddq.pb.UnionHonorHallWeekSettleMsg.getDefaultInstance() : weekSettle_;
  }
  /**
   * <code>optional .xddq.pb.UnionHonorHallWeekSettleMsg weekSettle = 1;</code>
   */
  @java.lang.Override
  public xddq.pb.UnionHonorHallWeekSettleMsgOrBuilder getWeekSettleOrBuilder() {
    return weekSettle_ == null ? xddq.pb.UnionHonorHallWeekSettleMsg.getDefaultInstance() : weekSettle_;
  }

  public static final int MONTHSETTLE_FIELD_NUMBER = 2;
  private xddq.pb.UnionHonorHallMonthSettleMsg monthSettle_;
  /**
   * <code>optional .xddq.pb.UnionHonorHallMonthSettleMsg monthSettle = 2;</code>
   * @return Whether the monthSettle field is set.
   */
  @java.lang.Override
  public boolean hasMonthSettle() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional .xddq.pb.UnionHonorHallMonthSettleMsg monthSettle = 2;</code>
   * @return The monthSettle.
   */
  @java.lang.Override
  public xddq.pb.UnionHonorHallMonthSettleMsg getMonthSettle() {
    return monthSettle_ == null ? xddq.pb.UnionHonorHallMonthSettleMsg.getDefaultInstance() : monthSettle_;
  }
  /**
   * <code>optional .xddq.pb.UnionHonorHallMonthSettleMsg monthSettle = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.UnionHonorHallMonthSettleMsgOrBuilder getMonthSettleOrBuilder() {
    return monthSettle_ == null ? xddq.pb.UnionHonorHallMonthSettleMsg.getDefaultInstance() : monthSettle_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeMessage(1, getWeekSettle());
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeMessage(2, getMonthSettle());
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, getWeekSettle());
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getMonthSettle());
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.UnionHonorHallSettleSync)) {
      return super.equals(obj);
    }
    xddq.pb.UnionHonorHallSettleSync other = (xddq.pb.UnionHonorHallSettleSync) obj;

    if (hasWeekSettle() != other.hasWeekSettle()) return false;
    if (hasWeekSettle()) {
      if (!getWeekSettle()
          .equals(other.getWeekSettle())) return false;
    }
    if (hasMonthSettle() != other.hasMonthSettle()) return false;
    if (hasMonthSettle()) {
      if (!getMonthSettle()
          .equals(other.getMonthSettle())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasWeekSettle()) {
      hash = (37 * hash) + WEEKSETTLE_FIELD_NUMBER;
      hash = (53 * hash) + getWeekSettle().hashCode();
    }
    if (hasMonthSettle()) {
      hash = (37 * hash) + MONTHSETTLE_FIELD_NUMBER;
      hash = (53 * hash) + getMonthSettle().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.UnionHonorHallSettleSync parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.UnionHonorHallSettleSync parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.UnionHonorHallSettleSync parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.UnionHonorHallSettleSync parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.UnionHonorHallSettleSync parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.UnionHonorHallSettleSync parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.UnionHonorHallSettleSync parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.UnionHonorHallSettleSync parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.UnionHonorHallSettleSync parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.UnionHonorHallSettleSync parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.UnionHonorHallSettleSync parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.UnionHonorHallSettleSync parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.UnionHonorHallSettleSync prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.UnionHonorHallSettleSync}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.UnionHonorHallSettleSync)
      xddq.pb.UnionHonorHallSettleSyncOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_UnionHonorHallSettleSync_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_UnionHonorHallSettleSync_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.UnionHonorHallSettleSync.class, xddq.pb.UnionHonorHallSettleSync.Builder.class);
    }

    // Construct using xddq.pb.UnionHonorHallSettleSync.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetWeekSettleFieldBuilder();
        internalGetMonthSettleFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      weekSettle_ = null;
      if (weekSettleBuilder_ != null) {
        weekSettleBuilder_.dispose();
        weekSettleBuilder_ = null;
      }
      monthSettle_ = null;
      if (monthSettleBuilder_ != null) {
        monthSettleBuilder_.dispose();
        monthSettleBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_UnionHonorHallSettleSync_descriptor;
    }

    @java.lang.Override
    public xddq.pb.UnionHonorHallSettleSync getDefaultInstanceForType() {
      return xddq.pb.UnionHonorHallSettleSync.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.UnionHonorHallSettleSync build() {
      xddq.pb.UnionHonorHallSettleSync result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.UnionHonorHallSettleSync buildPartial() {
      xddq.pb.UnionHonorHallSettleSync result = new xddq.pb.UnionHonorHallSettleSync(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.UnionHonorHallSettleSync result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.weekSettle_ = weekSettleBuilder_ == null
            ? weekSettle_
            : weekSettleBuilder_.build();
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.monthSettle_ = monthSettleBuilder_ == null
            ? monthSettle_
            : monthSettleBuilder_.build();
        to_bitField0_ |= 0x00000002;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.UnionHonorHallSettleSync) {
        return mergeFrom((xddq.pb.UnionHonorHallSettleSync)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.UnionHonorHallSettleSync other) {
      if (other == xddq.pb.UnionHonorHallSettleSync.getDefaultInstance()) return this;
      if (other.hasWeekSettle()) {
        mergeWeekSettle(other.getWeekSettle());
      }
      if (other.hasMonthSettle()) {
        mergeMonthSettle(other.getMonthSettle());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              input.readMessage(
                  internalGetWeekSettleFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 18: {
              input.readMessage(
                  internalGetMonthSettleFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private xddq.pb.UnionHonorHallWeekSettleMsg weekSettle_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.UnionHonorHallWeekSettleMsg, xddq.pb.UnionHonorHallWeekSettleMsg.Builder, xddq.pb.UnionHonorHallWeekSettleMsgOrBuilder> weekSettleBuilder_;
    /**
     * <code>optional .xddq.pb.UnionHonorHallWeekSettleMsg weekSettle = 1;</code>
     * @return Whether the weekSettle field is set.
     */
    public boolean hasWeekSettle() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .xddq.pb.UnionHonorHallWeekSettleMsg weekSettle = 1;</code>
     * @return The weekSettle.
     */
    public xddq.pb.UnionHonorHallWeekSettleMsg getWeekSettle() {
      if (weekSettleBuilder_ == null) {
        return weekSettle_ == null ? xddq.pb.UnionHonorHallWeekSettleMsg.getDefaultInstance() : weekSettle_;
      } else {
        return weekSettleBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.UnionHonorHallWeekSettleMsg weekSettle = 1;</code>
     */
    public Builder setWeekSettle(xddq.pb.UnionHonorHallWeekSettleMsg value) {
      if (weekSettleBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        weekSettle_ = value;
      } else {
        weekSettleBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.UnionHonorHallWeekSettleMsg weekSettle = 1;</code>
     */
    public Builder setWeekSettle(
        xddq.pb.UnionHonorHallWeekSettleMsg.Builder builderForValue) {
      if (weekSettleBuilder_ == null) {
        weekSettle_ = builderForValue.build();
      } else {
        weekSettleBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.UnionHonorHallWeekSettleMsg weekSettle = 1;</code>
     */
    public Builder mergeWeekSettle(xddq.pb.UnionHonorHallWeekSettleMsg value) {
      if (weekSettleBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0) &&
          weekSettle_ != null &&
          weekSettle_ != xddq.pb.UnionHonorHallWeekSettleMsg.getDefaultInstance()) {
          getWeekSettleBuilder().mergeFrom(value);
        } else {
          weekSettle_ = value;
        }
      } else {
        weekSettleBuilder_.mergeFrom(value);
      }
      if (weekSettle_ != null) {
        bitField0_ |= 0x00000001;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.UnionHonorHallWeekSettleMsg weekSettle = 1;</code>
     */
    public Builder clearWeekSettle() {
      bitField0_ = (bitField0_ & ~0x00000001);
      weekSettle_ = null;
      if (weekSettleBuilder_ != null) {
        weekSettleBuilder_.dispose();
        weekSettleBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.UnionHonorHallWeekSettleMsg weekSettle = 1;</code>
     */
    public xddq.pb.UnionHonorHallWeekSettleMsg.Builder getWeekSettleBuilder() {
      bitField0_ |= 0x00000001;
      onChanged();
      return internalGetWeekSettleFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.UnionHonorHallWeekSettleMsg weekSettle = 1;</code>
     */
    public xddq.pb.UnionHonorHallWeekSettleMsgOrBuilder getWeekSettleOrBuilder() {
      if (weekSettleBuilder_ != null) {
        return weekSettleBuilder_.getMessageOrBuilder();
      } else {
        return weekSettle_ == null ?
            xddq.pb.UnionHonorHallWeekSettleMsg.getDefaultInstance() : weekSettle_;
      }
    }
    /**
     * <code>optional .xddq.pb.UnionHonorHallWeekSettleMsg weekSettle = 1;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.UnionHonorHallWeekSettleMsg, xddq.pb.UnionHonorHallWeekSettleMsg.Builder, xddq.pb.UnionHonorHallWeekSettleMsgOrBuilder> 
        internalGetWeekSettleFieldBuilder() {
      if (weekSettleBuilder_ == null) {
        weekSettleBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.UnionHonorHallWeekSettleMsg, xddq.pb.UnionHonorHallWeekSettleMsg.Builder, xddq.pb.UnionHonorHallWeekSettleMsgOrBuilder>(
                getWeekSettle(),
                getParentForChildren(),
                isClean());
        weekSettle_ = null;
      }
      return weekSettleBuilder_;
    }

    private xddq.pb.UnionHonorHallMonthSettleMsg monthSettle_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.UnionHonorHallMonthSettleMsg, xddq.pb.UnionHonorHallMonthSettleMsg.Builder, xddq.pb.UnionHonorHallMonthSettleMsgOrBuilder> monthSettleBuilder_;
    /**
     * <code>optional .xddq.pb.UnionHonorHallMonthSettleMsg monthSettle = 2;</code>
     * @return Whether the monthSettle field is set.
     */
    public boolean hasMonthSettle() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .xddq.pb.UnionHonorHallMonthSettleMsg monthSettle = 2;</code>
     * @return The monthSettle.
     */
    public xddq.pb.UnionHonorHallMonthSettleMsg getMonthSettle() {
      if (monthSettleBuilder_ == null) {
        return monthSettle_ == null ? xddq.pb.UnionHonorHallMonthSettleMsg.getDefaultInstance() : monthSettle_;
      } else {
        return monthSettleBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.UnionHonorHallMonthSettleMsg monthSettle = 2;</code>
     */
    public Builder setMonthSettle(xddq.pb.UnionHonorHallMonthSettleMsg value) {
      if (monthSettleBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        monthSettle_ = value;
      } else {
        monthSettleBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.UnionHonorHallMonthSettleMsg monthSettle = 2;</code>
     */
    public Builder setMonthSettle(
        xddq.pb.UnionHonorHallMonthSettleMsg.Builder builderForValue) {
      if (monthSettleBuilder_ == null) {
        monthSettle_ = builderForValue.build();
      } else {
        monthSettleBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.UnionHonorHallMonthSettleMsg monthSettle = 2;</code>
     */
    public Builder mergeMonthSettle(xddq.pb.UnionHonorHallMonthSettleMsg value) {
      if (monthSettleBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0) &&
          monthSettle_ != null &&
          monthSettle_ != xddq.pb.UnionHonorHallMonthSettleMsg.getDefaultInstance()) {
          getMonthSettleBuilder().mergeFrom(value);
        } else {
          monthSettle_ = value;
        }
      } else {
        monthSettleBuilder_.mergeFrom(value);
      }
      if (monthSettle_ != null) {
        bitField0_ |= 0x00000002;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.UnionHonorHallMonthSettleMsg monthSettle = 2;</code>
     */
    public Builder clearMonthSettle() {
      bitField0_ = (bitField0_ & ~0x00000002);
      monthSettle_ = null;
      if (monthSettleBuilder_ != null) {
        monthSettleBuilder_.dispose();
        monthSettleBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.UnionHonorHallMonthSettleMsg monthSettle = 2;</code>
     */
    public xddq.pb.UnionHonorHallMonthSettleMsg.Builder getMonthSettleBuilder() {
      bitField0_ |= 0x00000002;
      onChanged();
      return internalGetMonthSettleFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.UnionHonorHallMonthSettleMsg monthSettle = 2;</code>
     */
    public xddq.pb.UnionHonorHallMonthSettleMsgOrBuilder getMonthSettleOrBuilder() {
      if (monthSettleBuilder_ != null) {
        return monthSettleBuilder_.getMessageOrBuilder();
      } else {
        return monthSettle_ == null ?
            xddq.pb.UnionHonorHallMonthSettleMsg.getDefaultInstance() : monthSettle_;
      }
    }
    /**
     * <code>optional .xddq.pb.UnionHonorHallMonthSettleMsg monthSettle = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.UnionHonorHallMonthSettleMsg, xddq.pb.UnionHonorHallMonthSettleMsg.Builder, xddq.pb.UnionHonorHallMonthSettleMsgOrBuilder> 
        internalGetMonthSettleFieldBuilder() {
      if (monthSettleBuilder_ == null) {
        monthSettleBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.UnionHonorHallMonthSettleMsg, xddq.pb.UnionHonorHallMonthSettleMsg.Builder, xddq.pb.UnionHonorHallMonthSettleMsgOrBuilder>(
                getMonthSettle(),
                getParentForChildren(),
                isClean());
        monthSettle_ = null;
      }
      return monthSettleBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.UnionHonorHallSettleSync)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.UnionHonorHallSettleSync)
  private static final xddq.pb.UnionHonorHallSettleSync DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.UnionHonorHallSettleSync();
  }

  public static xddq.pb.UnionHonorHallSettleSync getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<UnionHonorHallSettleSync>
      PARSER = new com.google.protobuf.AbstractParser<UnionHonorHallSettleSync>() {
    @java.lang.Override
    public UnionHonorHallSettleSync parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<UnionHonorHallSettleSync> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<UnionHonorHallSettleSync> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.UnionHonorHallSettleSync getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

