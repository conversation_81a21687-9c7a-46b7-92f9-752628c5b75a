// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

public interface UnionBossRewardReceiveRespMsgOrBuilder extends
    // @@protoc_insertion_point(interface_extends:xddq.pb.UnionBossRewardReceiveRespMsg)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>optional int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  boolean hasRet();
  /**
   * <code>optional int32 ret = 1;</code>
   * @return The ret.
   */
  int getRet();

  /**
   * <code>optional string rewards = 2;</code>
   * @return Whether the rewards field is set.
   */
  boolean hasRewards();
  /**
   * <code>optional string rewards = 2;</code>
   * @return The rewards.
   */
  java.lang.String getRewards();
  /**
   * <code>optional string rewards = 2;</code>
   * @return The bytes for rewards.
   */
  com.google.protobuf.ByteString
      getRewardsBytes();
}
