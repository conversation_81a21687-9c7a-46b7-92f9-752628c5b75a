// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

public interface LawLooksResetPreviewReqOrBuilder extends
    // @@protoc_insertion_point(interface_extends:xddq.pb.LawLooksResetPreviewReq)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>required int32 lawLooksId = 1;</code>
   * @return Whether the lawLooksId field is set.
   */
  boolean hasLawLooksId();
  /**
   * <code>required int32 lawLooksId = 1;</code>
   * @return The lawLooksId.
   */
  int getLawLooksId();
}
