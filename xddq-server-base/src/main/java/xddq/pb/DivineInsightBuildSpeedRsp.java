// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.DivineInsightBuildSpeedRsp}
 */
public final class DivineInsightBuildSpeedRsp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.DivineInsightBuildSpeedRsp)
    DivineInsightBuildSpeedRspOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      DivineInsightBuildSpeedRsp.class.getName());
  }
  // Use DivineInsightBuildSpeedRsp.newBuilder() to construct.
  private DivineInsightBuildSpeedRsp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private DivineInsightBuildSpeedRsp() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_DivineInsightBuildSpeedRsp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_DivineInsightBuildSpeedRsp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.DivineInsightBuildSpeedRsp.class, xddq.pb.DivineInsightBuildSpeedRsp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int DIVINEBUILDMSG_FIELD_NUMBER = 2;
  private xddq.pb.DivineBuildMsg divineBuildMsg_;
  /**
   * <code>optional .xddq.pb.DivineBuildMsg divineBuildMsg = 2;</code>
   * @return Whether the divineBuildMsg field is set.
   */
  @java.lang.Override
  public boolean hasDivineBuildMsg() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional .xddq.pb.DivineBuildMsg divineBuildMsg = 2;</code>
   * @return The divineBuildMsg.
   */
  @java.lang.Override
  public xddq.pb.DivineBuildMsg getDivineBuildMsg() {
    return divineBuildMsg_ == null ? xddq.pb.DivineBuildMsg.getDefaultInstance() : divineBuildMsg_;
  }
  /**
   * <code>optional .xddq.pb.DivineBuildMsg divineBuildMsg = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.DivineBuildMsgOrBuilder getDivineBuildMsgOrBuilder() {
    return divineBuildMsg_ == null ? xddq.pb.DivineBuildMsg.getDefaultInstance() : divineBuildMsg_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (hasDivineBuildMsg()) {
      if (!getDivineBuildMsg().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeMessage(2, getDivineBuildMsg());
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getDivineBuildMsg());
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.DivineInsightBuildSpeedRsp)) {
      return super.equals(obj);
    }
    xddq.pb.DivineInsightBuildSpeedRsp other = (xddq.pb.DivineInsightBuildSpeedRsp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (hasDivineBuildMsg() != other.hasDivineBuildMsg()) return false;
    if (hasDivineBuildMsg()) {
      if (!getDivineBuildMsg()
          .equals(other.getDivineBuildMsg())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (hasDivineBuildMsg()) {
      hash = (37 * hash) + DIVINEBUILDMSG_FIELD_NUMBER;
      hash = (53 * hash) + getDivineBuildMsg().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.DivineInsightBuildSpeedRsp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.DivineInsightBuildSpeedRsp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.DivineInsightBuildSpeedRsp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.DivineInsightBuildSpeedRsp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.DivineInsightBuildSpeedRsp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.DivineInsightBuildSpeedRsp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.DivineInsightBuildSpeedRsp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.DivineInsightBuildSpeedRsp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.DivineInsightBuildSpeedRsp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.DivineInsightBuildSpeedRsp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.DivineInsightBuildSpeedRsp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.DivineInsightBuildSpeedRsp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.DivineInsightBuildSpeedRsp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.DivineInsightBuildSpeedRsp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.DivineInsightBuildSpeedRsp)
      xddq.pb.DivineInsightBuildSpeedRspOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_DivineInsightBuildSpeedRsp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_DivineInsightBuildSpeedRsp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.DivineInsightBuildSpeedRsp.class, xddq.pb.DivineInsightBuildSpeedRsp.Builder.class);
    }

    // Construct using xddq.pb.DivineInsightBuildSpeedRsp.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetDivineBuildMsgFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      divineBuildMsg_ = null;
      if (divineBuildMsgBuilder_ != null) {
        divineBuildMsgBuilder_.dispose();
        divineBuildMsgBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_DivineInsightBuildSpeedRsp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.DivineInsightBuildSpeedRsp getDefaultInstanceForType() {
      return xddq.pb.DivineInsightBuildSpeedRsp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.DivineInsightBuildSpeedRsp build() {
      xddq.pb.DivineInsightBuildSpeedRsp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.DivineInsightBuildSpeedRsp buildPartial() {
      xddq.pb.DivineInsightBuildSpeedRsp result = new xddq.pb.DivineInsightBuildSpeedRsp(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.DivineInsightBuildSpeedRsp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.divineBuildMsg_ = divineBuildMsgBuilder_ == null
            ? divineBuildMsg_
            : divineBuildMsgBuilder_.build();
        to_bitField0_ |= 0x00000002;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.DivineInsightBuildSpeedRsp) {
        return mergeFrom((xddq.pb.DivineInsightBuildSpeedRsp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.DivineInsightBuildSpeedRsp other) {
      if (other == xddq.pb.DivineInsightBuildSpeedRsp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (other.hasDivineBuildMsg()) {
        mergeDivineBuildMsg(other.getDivineBuildMsg());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      if (hasDivineBuildMsg()) {
        if (!getDivineBuildMsg().isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              input.readMessage(
                  internalGetDivineBuildMsgFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private xddq.pb.DivineBuildMsg divineBuildMsg_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.DivineBuildMsg, xddq.pb.DivineBuildMsg.Builder, xddq.pb.DivineBuildMsgOrBuilder> divineBuildMsgBuilder_;
    /**
     * <code>optional .xddq.pb.DivineBuildMsg divineBuildMsg = 2;</code>
     * @return Whether the divineBuildMsg field is set.
     */
    public boolean hasDivineBuildMsg() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .xddq.pb.DivineBuildMsg divineBuildMsg = 2;</code>
     * @return The divineBuildMsg.
     */
    public xddq.pb.DivineBuildMsg getDivineBuildMsg() {
      if (divineBuildMsgBuilder_ == null) {
        return divineBuildMsg_ == null ? xddq.pb.DivineBuildMsg.getDefaultInstance() : divineBuildMsg_;
      } else {
        return divineBuildMsgBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.DivineBuildMsg divineBuildMsg = 2;</code>
     */
    public Builder setDivineBuildMsg(xddq.pb.DivineBuildMsg value) {
      if (divineBuildMsgBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        divineBuildMsg_ = value;
      } else {
        divineBuildMsgBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.DivineBuildMsg divineBuildMsg = 2;</code>
     */
    public Builder setDivineBuildMsg(
        xddq.pb.DivineBuildMsg.Builder builderForValue) {
      if (divineBuildMsgBuilder_ == null) {
        divineBuildMsg_ = builderForValue.build();
      } else {
        divineBuildMsgBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.DivineBuildMsg divineBuildMsg = 2;</code>
     */
    public Builder mergeDivineBuildMsg(xddq.pb.DivineBuildMsg value) {
      if (divineBuildMsgBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0) &&
          divineBuildMsg_ != null &&
          divineBuildMsg_ != xddq.pb.DivineBuildMsg.getDefaultInstance()) {
          getDivineBuildMsgBuilder().mergeFrom(value);
        } else {
          divineBuildMsg_ = value;
        }
      } else {
        divineBuildMsgBuilder_.mergeFrom(value);
      }
      if (divineBuildMsg_ != null) {
        bitField0_ |= 0x00000002;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.DivineBuildMsg divineBuildMsg = 2;</code>
     */
    public Builder clearDivineBuildMsg() {
      bitField0_ = (bitField0_ & ~0x00000002);
      divineBuildMsg_ = null;
      if (divineBuildMsgBuilder_ != null) {
        divineBuildMsgBuilder_.dispose();
        divineBuildMsgBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.DivineBuildMsg divineBuildMsg = 2;</code>
     */
    public xddq.pb.DivineBuildMsg.Builder getDivineBuildMsgBuilder() {
      bitField0_ |= 0x00000002;
      onChanged();
      return internalGetDivineBuildMsgFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.DivineBuildMsg divineBuildMsg = 2;</code>
     */
    public xddq.pb.DivineBuildMsgOrBuilder getDivineBuildMsgOrBuilder() {
      if (divineBuildMsgBuilder_ != null) {
        return divineBuildMsgBuilder_.getMessageOrBuilder();
      } else {
        return divineBuildMsg_ == null ?
            xddq.pb.DivineBuildMsg.getDefaultInstance() : divineBuildMsg_;
      }
    }
    /**
     * <code>optional .xddq.pb.DivineBuildMsg divineBuildMsg = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.DivineBuildMsg, xddq.pb.DivineBuildMsg.Builder, xddq.pb.DivineBuildMsgOrBuilder> 
        internalGetDivineBuildMsgFieldBuilder() {
      if (divineBuildMsgBuilder_ == null) {
        divineBuildMsgBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.DivineBuildMsg, xddq.pb.DivineBuildMsg.Builder, xddq.pb.DivineBuildMsgOrBuilder>(
                getDivineBuildMsg(),
                getParentForChildren(),
                isClean());
        divineBuildMsg_ = null;
      }
      return divineBuildMsgBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.DivineInsightBuildSpeedRsp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.DivineInsightBuildSpeedRsp)
  private static final xddq.pb.DivineInsightBuildSpeedRsp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.DivineInsightBuildSpeedRsp();
  }

  public static xddq.pb.DivineInsightBuildSpeedRsp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<DivineInsightBuildSpeedRsp>
      PARSER = new com.google.protobuf.AbstractParser<DivineInsightBuildSpeedRsp>() {
    @java.lang.Override
    public DivineInsightBuildSpeedRsp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<DivineInsightBuildSpeedRsp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<DivineInsightBuildSpeedRsp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.DivineInsightBuildSpeedRsp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

