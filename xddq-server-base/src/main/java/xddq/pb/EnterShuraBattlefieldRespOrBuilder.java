// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

public interface EnterShuraBattlefieldRespOrBuilder extends
    // @@protoc_insertion_point(interface_extends:xddq.pb.EnterShuraBattlefieldResp)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  boolean hasRet();
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  int getRet();

  /**
   * <code>optional .xddq.pb.ShuraBttleInnerTeamInfo myTeam = 2;</code>
   * @return Whether the myTeam field is set.
   */
  boolean hasMyTeam();
  /**
   * <code>optional .xddq.pb.ShuraBttleInnerTeamInfo myTeam = 2;</code>
   * @return The myTeam.
   */
  xddq.pb.ShuraBttleInnerTeamInfo getMyTeam();
  /**
   * <code>optional .xddq.pb.ShuraBttleInnerTeamInfo myTeam = 2;</code>
   */
  xddq.pb.ShuraBttleInnerTeamInfoOrBuilder getMyTeamOrBuilder();

  /**
   * <code>repeated .xddq.pb.ShuraBttleInnerTeamInfo otherTeams = 3;</code>
   */
  java.util.List<xddq.pb.ShuraBttleInnerTeamInfo> 
      getOtherTeamsList();
  /**
   * <code>repeated .xddq.pb.ShuraBttleInnerTeamInfo otherTeams = 3;</code>
   */
  xddq.pb.ShuraBttleInnerTeamInfo getOtherTeams(int index);
  /**
   * <code>repeated .xddq.pb.ShuraBttleInnerTeamInfo otherTeams = 3;</code>
   */
  int getOtherTeamsCount();
  /**
   * <code>repeated .xddq.pb.ShuraBttleInnerTeamInfo otherTeams = 3;</code>
   */
  java.util.List<? extends xddq.pb.ShuraBttleInnerTeamInfoOrBuilder> 
      getOtherTeamsOrBuilderList();
  /**
   * <code>repeated .xddq.pb.ShuraBttleInnerTeamInfo otherTeams = 3;</code>
   */
  xddq.pb.ShuraBttleInnerTeamInfoOrBuilder getOtherTeamsOrBuilder(
      int index);

  /**
   * <code>optional int32 highestFloorTeamsNum = 4;</code>
   * @return Whether the highestFloorTeamsNum field is set.
   */
  boolean hasHighestFloorTeamsNum();
  /**
   * <code>optional int32 highestFloorTeamsNum = 4;</code>
   * @return The highestFloorTeamsNum.
   */
  int getHighestFloorTeamsNum();

  /**
   * <code>optional int32 joinRewardState = 5;</code>
   * @return Whether the joinRewardState field is set.
   */
  boolean hasJoinRewardState();
  /**
   * <code>optional int32 joinRewardState = 5;</code>
   * @return The joinRewardState.
   */
  int getJoinRewardState();

  /**
   * <code>optional int32 totalRank = 6;</code>
   * @return Whether the totalRank field is set.
   */
  boolean hasTotalRank();
  /**
   * <code>optional int32 totalRank = 6;</code>
   * @return The totalRank.
   */
  int getTotalRank();

  /**
   * <code>optional int32 lastSettleFloor = 7;</code>
   * @return Whether the lastSettleFloor field is set.
   */
  boolean hasLastSettleFloor();
  /**
   * <code>optional int32 lastSettleFloor = 7;</code>
   * @return The lastSettleFloor.
   */
  int getLastSettleFloor();

  /**
   * <code>repeated string params = 10;</code>
   * @return A list containing the params.
   */
  java.util.List<java.lang.String>
      getParamsList();
  /**
   * <code>repeated string params = 10;</code>
   * @return The count of params.
   */
  int getParamsCount();
  /**
   * <code>repeated string params = 10;</code>
   * @param index The index of the element to return.
   * @return The params at the given index.
   */
  java.lang.String getParams(int index);
  /**
   * <code>repeated string params = 10;</code>
   * @param index The index of the value to return.
   * @return The bytes of the params at the given index.
   */
  com.google.protobuf.ByteString
      getParamsBytes(int index);
}
