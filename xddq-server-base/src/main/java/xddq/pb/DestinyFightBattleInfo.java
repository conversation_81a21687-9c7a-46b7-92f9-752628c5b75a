// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.DestinyFightBattleInfo}
 */
public final class DestinyFightBattleInfo extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.DestinyFightBattleInfo)
    DestinyFightBattleInfoOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      DestinyFightBattleInfo.class.getName());
  }
  // Use DestinyFightBattleInfo.newBuilder() to construct.
  private DestinyFightBattleInfo(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private DestinyFightBattleInfo() {
    destinyBattleInfo_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_DestinyFightBattleInfo_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_DestinyFightBattleInfo_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.DestinyFightBattleInfo.class, xddq.pb.DestinyFightBattleInfo.Builder.class);
  }

  private int bitField0_;
  public static final int DESTINYBATTLEINFO_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.DestinyFightDestinyBattleInfo> destinyBattleInfo_;
  /**
   * <code>repeated .xddq.pb.DestinyFightDestinyBattleInfo destinyBattleInfo = 1;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.DestinyFightDestinyBattleInfo> getDestinyBattleInfoList() {
    return destinyBattleInfo_;
  }
  /**
   * <code>repeated .xddq.pb.DestinyFightDestinyBattleInfo destinyBattleInfo = 1;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.DestinyFightDestinyBattleInfoOrBuilder> 
      getDestinyBattleInfoOrBuilderList() {
    return destinyBattleInfo_;
  }
  /**
   * <code>repeated .xddq.pb.DestinyFightDestinyBattleInfo destinyBattleInfo = 1;</code>
   */
  @java.lang.Override
  public int getDestinyBattleInfoCount() {
    return destinyBattleInfo_.size();
  }
  /**
   * <code>repeated .xddq.pb.DestinyFightDestinyBattleInfo destinyBattleInfo = 1;</code>
   */
  @java.lang.Override
  public xddq.pb.DestinyFightDestinyBattleInfo getDestinyBattleInfo(int index) {
    return destinyBattleInfo_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.DestinyFightDestinyBattleInfo destinyBattleInfo = 1;</code>
   */
  @java.lang.Override
  public xddq.pb.DestinyFightDestinyBattleInfoOrBuilder getDestinyBattleInfoOrBuilder(
      int index) {
    return destinyBattleInfo_.get(index);
  }

  public static final int ENDTIME_FIELD_NUMBER = 2;
  private long endTime_ = 0L;
  /**
   * <code>required int64 endTime = 2;</code>
   * @return Whether the endTime field is set.
   */
  @java.lang.Override
  public boolean hasEndTime() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int64 endTime = 2;</code>
   * @return The endTime.
   */
  @java.lang.Override
  public long getEndTime() {
    return endTime_;
  }

  public static final int TASKID_FIELD_NUMBER = 3;
  private int taskId_ = 0;
  /**
   * <code>required int32 taskId = 3;</code>
   * @return Whether the taskId field is set.
   */
  @java.lang.Override
  public boolean hasTaskId() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>required int32 taskId = 3;</code>
   * @return The taskId.
   */
  @java.lang.Override
  public int getTaskId() {
    return taskId_;
  }

  public static final int PLAYERINFO_FIELD_NUMBER = 4;
  private xddq.pb.PlayerBaseDataMsg playerInfo_;
  /**
   * <code>optional .xddq.pb.PlayerBaseDataMsg playerInfo = 4;</code>
   * @return Whether the playerInfo field is set.
   */
  @java.lang.Override
  public boolean hasPlayerInfo() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional .xddq.pb.PlayerBaseDataMsg playerInfo = 4;</code>
   * @return The playerInfo.
   */
  @java.lang.Override
  public xddq.pb.PlayerBaseDataMsg getPlayerInfo() {
    return playerInfo_ == null ? xddq.pb.PlayerBaseDataMsg.getDefaultInstance() : playerInfo_;
  }
  /**
   * <code>optional .xddq.pb.PlayerBaseDataMsg playerInfo = 4;</code>
   */
  @java.lang.Override
  public xddq.pb.PlayerBaseDataMsgOrBuilder getPlayerInfoOrBuilder() {
    return playerInfo_ == null ? xddq.pb.PlayerBaseDataMsg.getDefaultInstance() : playerInfo_;
  }

  public static final int POS_FIELD_NUMBER = 5;
  private int pos_ = 0;
  /**
   * <code>optional int32 pos = 5;</code>
   * @return Whether the pos field is set.
   */
  @java.lang.Override
  public boolean hasPos() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional int32 pos = 5;</code>
   * @return The pos.
   */
  @java.lang.Override
  public int getPos() {
    return pos_;
  }

  public static final int DOUBLEITEM_FIELD_NUMBER = 6;
  private int doubleItem_ = 0;
  /**
   * <code>optional int32 doubleItem = 6;</code>
   * @return Whether the doubleItem field is set.
   */
  @java.lang.Override
  public boolean hasDoubleItem() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>optional int32 doubleItem = 6;</code>
   * @return The doubleItem.
   */
  @java.lang.Override
  public int getDoubleItem() {
    return doubleItem_;
  }

  public static final int ID_FIELD_NUMBER = 7;
  private long id_ = 0L;
  /**
   * <code>required int64 id = 7;</code>
   * @return Whether the id field is set.
   */
  @java.lang.Override
  public boolean hasId() {
    return ((bitField0_ & 0x00000020) != 0);
  }
  /**
   * <code>required int64 id = 7;</code>
   * @return The id.
   */
  @java.lang.Override
  public long getId() {
    return id_;
  }

  public static final int CANBATTLE_FIELD_NUMBER = 8;
  private boolean canBattle_ = false;
  /**
   * <code>optional bool canBattle = 8;</code>
   * @return Whether the canBattle field is set.
   */
  @java.lang.Override
  public boolean hasCanBattle() {
    return ((bitField0_ & 0x00000040) != 0);
  }
  /**
   * <code>optional bool canBattle = 8;</code>
   * @return The canBattle.
   */
  @java.lang.Override
  public boolean getCanBattle() {
    return canBattle_;
  }

  public static final int ROBNUM_FIELD_NUMBER = 9;
  private int robNum_ = 0;
  /**
   * <code>optional int32 robNum = 9;</code>
   * @return Whether the robNum field is set.
   */
  @java.lang.Override
  public boolean hasRobNum() {
    return ((bitField0_ & 0x00000080) != 0);
  }
  /**
   * <code>optional int32 robNum = 9;</code>
   * @return The robNum.
   */
  @java.lang.Override
  public int getRobNum() {
    return robNum_;
  }

  public static final int USEDNUM_FIELD_NUMBER = 10;
  private int usedNum_ = 0;
  /**
   * <code>optional int32 usedNum = 10;</code>
   * @return Whether the usedNum field is set.
   */
  @java.lang.Override
  public boolean hasUsedNum() {
    return ((bitField0_ & 0x00000100) != 0);
  }
  /**
   * <code>optional int32 usedNum = 10;</code>
   * @return The usedNum.
   */
  @java.lang.Override
  public int getUsedNum() {
    return usedNum_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasEndTime()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasTaskId()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasId()) {
      memoizedIsInitialized = 0;
      return false;
    }
    for (int i = 0; i < getDestinyBattleInfoCount(); i++) {
      if (!getDestinyBattleInfo(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    for (int i = 0; i < destinyBattleInfo_.size(); i++) {
      output.writeMessage(1, destinyBattleInfo_.get(i));
    }
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt64(2, endTime_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(3, taskId_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeMessage(4, getPlayerInfo());
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt32(5, pos_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      output.writeInt32(6, doubleItem_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      output.writeInt64(7, id_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      output.writeBool(8, canBattle_);
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      output.writeInt32(9, robNum_);
    }
    if (((bitField0_ & 0x00000100) != 0)) {
      output.writeInt32(10, usedNum_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    for (int i = 0; i < destinyBattleInfo_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, destinyBattleInfo_.get(i));
    }
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(2, endTime_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(3, taskId_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(4, getPlayerInfo());
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(5, pos_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(6, doubleItem_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(7, id_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(8, canBattle_);
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(9, robNum_);
    }
    if (((bitField0_ & 0x00000100) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(10, usedNum_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.DestinyFightBattleInfo)) {
      return super.equals(obj);
    }
    xddq.pb.DestinyFightBattleInfo other = (xddq.pb.DestinyFightBattleInfo) obj;

    if (!getDestinyBattleInfoList()
        .equals(other.getDestinyBattleInfoList())) return false;
    if (hasEndTime() != other.hasEndTime()) return false;
    if (hasEndTime()) {
      if (getEndTime()
          != other.getEndTime()) return false;
    }
    if (hasTaskId() != other.hasTaskId()) return false;
    if (hasTaskId()) {
      if (getTaskId()
          != other.getTaskId()) return false;
    }
    if (hasPlayerInfo() != other.hasPlayerInfo()) return false;
    if (hasPlayerInfo()) {
      if (!getPlayerInfo()
          .equals(other.getPlayerInfo())) return false;
    }
    if (hasPos() != other.hasPos()) return false;
    if (hasPos()) {
      if (getPos()
          != other.getPos()) return false;
    }
    if (hasDoubleItem() != other.hasDoubleItem()) return false;
    if (hasDoubleItem()) {
      if (getDoubleItem()
          != other.getDoubleItem()) return false;
    }
    if (hasId() != other.hasId()) return false;
    if (hasId()) {
      if (getId()
          != other.getId()) return false;
    }
    if (hasCanBattle() != other.hasCanBattle()) return false;
    if (hasCanBattle()) {
      if (getCanBattle()
          != other.getCanBattle()) return false;
    }
    if (hasRobNum() != other.hasRobNum()) return false;
    if (hasRobNum()) {
      if (getRobNum()
          != other.getRobNum()) return false;
    }
    if (hasUsedNum() != other.hasUsedNum()) return false;
    if (hasUsedNum()) {
      if (getUsedNum()
          != other.getUsedNum()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (getDestinyBattleInfoCount() > 0) {
      hash = (37 * hash) + DESTINYBATTLEINFO_FIELD_NUMBER;
      hash = (53 * hash) + getDestinyBattleInfoList().hashCode();
    }
    if (hasEndTime()) {
      hash = (37 * hash) + ENDTIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getEndTime());
    }
    if (hasTaskId()) {
      hash = (37 * hash) + TASKID_FIELD_NUMBER;
      hash = (53 * hash) + getTaskId();
    }
    if (hasPlayerInfo()) {
      hash = (37 * hash) + PLAYERINFO_FIELD_NUMBER;
      hash = (53 * hash) + getPlayerInfo().hashCode();
    }
    if (hasPos()) {
      hash = (37 * hash) + POS_FIELD_NUMBER;
      hash = (53 * hash) + getPos();
    }
    if (hasDoubleItem()) {
      hash = (37 * hash) + DOUBLEITEM_FIELD_NUMBER;
      hash = (53 * hash) + getDoubleItem();
    }
    if (hasId()) {
      hash = (37 * hash) + ID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getId());
    }
    if (hasCanBattle()) {
      hash = (37 * hash) + CANBATTLE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getCanBattle());
    }
    if (hasRobNum()) {
      hash = (37 * hash) + ROBNUM_FIELD_NUMBER;
      hash = (53 * hash) + getRobNum();
    }
    if (hasUsedNum()) {
      hash = (37 * hash) + USEDNUM_FIELD_NUMBER;
      hash = (53 * hash) + getUsedNum();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.DestinyFightBattleInfo parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.DestinyFightBattleInfo parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.DestinyFightBattleInfo parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.DestinyFightBattleInfo parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.DestinyFightBattleInfo parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.DestinyFightBattleInfo parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.DestinyFightBattleInfo parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.DestinyFightBattleInfo parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.DestinyFightBattleInfo parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.DestinyFightBattleInfo parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.DestinyFightBattleInfo parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.DestinyFightBattleInfo parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.DestinyFightBattleInfo prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.DestinyFightBattleInfo}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.DestinyFightBattleInfo)
      xddq.pb.DestinyFightBattleInfoOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_DestinyFightBattleInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_DestinyFightBattleInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.DestinyFightBattleInfo.class, xddq.pb.DestinyFightBattleInfo.Builder.class);
    }

    // Construct using xddq.pb.DestinyFightBattleInfo.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetDestinyBattleInfoFieldBuilder();
        internalGetPlayerInfoFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      if (destinyBattleInfoBuilder_ == null) {
        destinyBattleInfo_ = java.util.Collections.emptyList();
      } else {
        destinyBattleInfo_ = null;
        destinyBattleInfoBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000001);
      endTime_ = 0L;
      taskId_ = 0;
      playerInfo_ = null;
      if (playerInfoBuilder_ != null) {
        playerInfoBuilder_.dispose();
        playerInfoBuilder_ = null;
      }
      pos_ = 0;
      doubleItem_ = 0;
      id_ = 0L;
      canBattle_ = false;
      robNum_ = 0;
      usedNum_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_DestinyFightBattleInfo_descriptor;
    }

    @java.lang.Override
    public xddq.pb.DestinyFightBattleInfo getDefaultInstanceForType() {
      return xddq.pb.DestinyFightBattleInfo.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.DestinyFightBattleInfo build() {
      xddq.pb.DestinyFightBattleInfo result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.DestinyFightBattleInfo buildPartial() {
      xddq.pb.DestinyFightBattleInfo result = new xddq.pb.DestinyFightBattleInfo(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.DestinyFightBattleInfo result) {
      if (destinyBattleInfoBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          destinyBattleInfo_ = java.util.Collections.unmodifiableList(destinyBattleInfo_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.destinyBattleInfo_ = destinyBattleInfo_;
      } else {
        result.destinyBattleInfo_ = destinyBattleInfoBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.DestinyFightBattleInfo result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.endTime_ = endTime_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.taskId_ = taskId_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.playerInfo_ = playerInfoBuilder_ == null
            ? playerInfo_
            : playerInfoBuilder_.build();
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.pos_ = pos_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.doubleItem_ = doubleItem_;
        to_bitField0_ |= 0x00000010;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.id_ = id_;
        to_bitField0_ |= 0x00000020;
      }
      if (((from_bitField0_ & 0x00000080) != 0)) {
        result.canBattle_ = canBattle_;
        to_bitField0_ |= 0x00000040;
      }
      if (((from_bitField0_ & 0x00000100) != 0)) {
        result.robNum_ = robNum_;
        to_bitField0_ |= 0x00000080;
      }
      if (((from_bitField0_ & 0x00000200) != 0)) {
        result.usedNum_ = usedNum_;
        to_bitField0_ |= 0x00000100;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.DestinyFightBattleInfo) {
        return mergeFrom((xddq.pb.DestinyFightBattleInfo)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.DestinyFightBattleInfo other) {
      if (other == xddq.pb.DestinyFightBattleInfo.getDefaultInstance()) return this;
      if (destinyBattleInfoBuilder_ == null) {
        if (!other.destinyBattleInfo_.isEmpty()) {
          if (destinyBattleInfo_.isEmpty()) {
            destinyBattleInfo_ = other.destinyBattleInfo_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureDestinyBattleInfoIsMutable();
            destinyBattleInfo_.addAll(other.destinyBattleInfo_);
          }
          onChanged();
        }
      } else {
        if (!other.destinyBattleInfo_.isEmpty()) {
          if (destinyBattleInfoBuilder_.isEmpty()) {
            destinyBattleInfoBuilder_.dispose();
            destinyBattleInfoBuilder_ = null;
            destinyBattleInfo_ = other.destinyBattleInfo_;
            bitField0_ = (bitField0_ & ~0x00000001);
            destinyBattleInfoBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetDestinyBattleInfoFieldBuilder() : null;
          } else {
            destinyBattleInfoBuilder_.addAllMessages(other.destinyBattleInfo_);
          }
        }
      }
      if (other.hasEndTime()) {
        setEndTime(other.getEndTime());
      }
      if (other.hasTaskId()) {
        setTaskId(other.getTaskId());
      }
      if (other.hasPlayerInfo()) {
        mergePlayerInfo(other.getPlayerInfo());
      }
      if (other.hasPos()) {
        setPos(other.getPos());
      }
      if (other.hasDoubleItem()) {
        setDoubleItem(other.getDoubleItem());
      }
      if (other.hasId()) {
        setId(other.getId());
      }
      if (other.hasCanBattle()) {
        setCanBattle(other.getCanBattle());
      }
      if (other.hasRobNum()) {
        setRobNum(other.getRobNum());
      }
      if (other.hasUsedNum()) {
        setUsedNum(other.getUsedNum());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasEndTime()) {
        return false;
      }
      if (!hasTaskId()) {
        return false;
      }
      if (!hasId()) {
        return false;
      }
      for (int i = 0; i < getDestinyBattleInfoCount(); i++) {
        if (!getDestinyBattleInfo(i).isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              xddq.pb.DestinyFightDestinyBattleInfo m =
                  input.readMessage(
                      xddq.pb.DestinyFightDestinyBattleInfo.parser(),
                      extensionRegistry);
              if (destinyBattleInfoBuilder_ == null) {
                ensureDestinyBattleInfoIsMutable();
                destinyBattleInfo_.add(m);
              } else {
                destinyBattleInfoBuilder_.addMessage(m);
              }
              break;
            } // case 10
            case 16: {
              endTime_ = input.readInt64();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 24: {
              taskId_ = input.readInt32();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 34: {
              input.readMessage(
                  internalGetPlayerInfoFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000008;
              break;
            } // case 34
            case 40: {
              pos_ = input.readInt32();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            case 48: {
              doubleItem_ = input.readInt32();
              bitField0_ |= 0x00000020;
              break;
            } // case 48
            case 56: {
              id_ = input.readInt64();
              bitField0_ |= 0x00000040;
              break;
            } // case 56
            case 64: {
              canBattle_ = input.readBool();
              bitField0_ |= 0x00000080;
              break;
            } // case 64
            case 72: {
              robNum_ = input.readInt32();
              bitField0_ |= 0x00000100;
              break;
            } // case 72
            case 80: {
              usedNum_ = input.readInt32();
              bitField0_ |= 0x00000200;
              break;
            } // case 80
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.util.List<xddq.pb.DestinyFightDestinyBattleInfo> destinyBattleInfo_ =
      java.util.Collections.emptyList();
    private void ensureDestinyBattleInfoIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        destinyBattleInfo_ = new java.util.ArrayList<xddq.pb.DestinyFightDestinyBattleInfo>(destinyBattleInfo_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.DestinyFightDestinyBattleInfo, xddq.pb.DestinyFightDestinyBattleInfo.Builder, xddq.pb.DestinyFightDestinyBattleInfoOrBuilder> destinyBattleInfoBuilder_;

    /**
     * <code>repeated .xddq.pb.DestinyFightDestinyBattleInfo destinyBattleInfo = 1;</code>
     */
    public java.util.List<xddq.pb.DestinyFightDestinyBattleInfo> getDestinyBattleInfoList() {
      if (destinyBattleInfoBuilder_ == null) {
        return java.util.Collections.unmodifiableList(destinyBattleInfo_);
      } else {
        return destinyBattleInfoBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.DestinyFightDestinyBattleInfo destinyBattleInfo = 1;</code>
     */
    public int getDestinyBattleInfoCount() {
      if (destinyBattleInfoBuilder_ == null) {
        return destinyBattleInfo_.size();
      } else {
        return destinyBattleInfoBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.DestinyFightDestinyBattleInfo destinyBattleInfo = 1;</code>
     */
    public xddq.pb.DestinyFightDestinyBattleInfo getDestinyBattleInfo(int index) {
      if (destinyBattleInfoBuilder_ == null) {
        return destinyBattleInfo_.get(index);
      } else {
        return destinyBattleInfoBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.DestinyFightDestinyBattleInfo destinyBattleInfo = 1;</code>
     */
    public Builder setDestinyBattleInfo(
        int index, xddq.pb.DestinyFightDestinyBattleInfo value) {
      if (destinyBattleInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDestinyBattleInfoIsMutable();
        destinyBattleInfo_.set(index, value);
        onChanged();
      } else {
        destinyBattleInfoBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DestinyFightDestinyBattleInfo destinyBattleInfo = 1;</code>
     */
    public Builder setDestinyBattleInfo(
        int index, xddq.pb.DestinyFightDestinyBattleInfo.Builder builderForValue) {
      if (destinyBattleInfoBuilder_ == null) {
        ensureDestinyBattleInfoIsMutable();
        destinyBattleInfo_.set(index, builderForValue.build());
        onChanged();
      } else {
        destinyBattleInfoBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DestinyFightDestinyBattleInfo destinyBattleInfo = 1;</code>
     */
    public Builder addDestinyBattleInfo(xddq.pb.DestinyFightDestinyBattleInfo value) {
      if (destinyBattleInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDestinyBattleInfoIsMutable();
        destinyBattleInfo_.add(value);
        onChanged();
      } else {
        destinyBattleInfoBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DestinyFightDestinyBattleInfo destinyBattleInfo = 1;</code>
     */
    public Builder addDestinyBattleInfo(
        int index, xddq.pb.DestinyFightDestinyBattleInfo value) {
      if (destinyBattleInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDestinyBattleInfoIsMutable();
        destinyBattleInfo_.add(index, value);
        onChanged();
      } else {
        destinyBattleInfoBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DestinyFightDestinyBattleInfo destinyBattleInfo = 1;</code>
     */
    public Builder addDestinyBattleInfo(
        xddq.pb.DestinyFightDestinyBattleInfo.Builder builderForValue) {
      if (destinyBattleInfoBuilder_ == null) {
        ensureDestinyBattleInfoIsMutable();
        destinyBattleInfo_.add(builderForValue.build());
        onChanged();
      } else {
        destinyBattleInfoBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DestinyFightDestinyBattleInfo destinyBattleInfo = 1;</code>
     */
    public Builder addDestinyBattleInfo(
        int index, xddq.pb.DestinyFightDestinyBattleInfo.Builder builderForValue) {
      if (destinyBattleInfoBuilder_ == null) {
        ensureDestinyBattleInfoIsMutable();
        destinyBattleInfo_.add(index, builderForValue.build());
        onChanged();
      } else {
        destinyBattleInfoBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DestinyFightDestinyBattleInfo destinyBattleInfo = 1;</code>
     */
    public Builder addAllDestinyBattleInfo(
        java.lang.Iterable<? extends xddq.pb.DestinyFightDestinyBattleInfo> values) {
      if (destinyBattleInfoBuilder_ == null) {
        ensureDestinyBattleInfoIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, destinyBattleInfo_);
        onChanged();
      } else {
        destinyBattleInfoBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DestinyFightDestinyBattleInfo destinyBattleInfo = 1;</code>
     */
    public Builder clearDestinyBattleInfo() {
      if (destinyBattleInfoBuilder_ == null) {
        destinyBattleInfo_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        destinyBattleInfoBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DestinyFightDestinyBattleInfo destinyBattleInfo = 1;</code>
     */
    public Builder removeDestinyBattleInfo(int index) {
      if (destinyBattleInfoBuilder_ == null) {
        ensureDestinyBattleInfoIsMutable();
        destinyBattleInfo_.remove(index);
        onChanged();
      } else {
        destinyBattleInfoBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DestinyFightDestinyBattleInfo destinyBattleInfo = 1;</code>
     */
    public xddq.pb.DestinyFightDestinyBattleInfo.Builder getDestinyBattleInfoBuilder(
        int index) {
      return internalGetDestinyBattleInfoFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.DestinyFightDestinyBattleInfo destinyBattleInfo = 1;</code>
     */
    public xddq.pb.DestinyFightDestinyBattleInfoOrBuilder getDestinyBattleInfoOrBuilder(
        int index) {
      if (destinyBattleInfoBuilder_ == null) {
        return destinyBattleInfo_.get(index);  } else {
        return destinyBattleInfoBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.DestinyFightDestinyBattleInfo destinyBattleInfo = 1;</code>
     */
    public java.util.List<? extends xddq.pb.DestinyFightDestinyBattleInfoOrBuilder> 
         getDestinyBattleInfoOrBuilderList() {
      if (destinyBattleInfoBuilder_ != null) {
        return destinyBattleInfoBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(destinyBattleInfo_);
      }
    }
    /**
     * <code>repeated .xddq.pb.DestinyFightDestinyBattleInfo destinyBattleInfo = 1;</code>
     */
    public xddq.pb.DestinyFightDestinyBattleInfo.Builder addDestinyBattleInfoBuilder() {
      return internalGetDestinyBattleInfoFieldBuilder().addBuilder(
          xddq.pb.DestinyFightDestinyBattleInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.DestinyFightDestinyBattleInfo destinyBattleInfo = 1;</code>
     */
    public xddq.pb.DestinyFightDestinyBattleInfo.Builder addDestinyBattleInfoBuilder(
        int index) {
      return internalGetDestinyBattleInfoFieldBuilder().addBuilder(
          index, xddq.pb.DestinyFightDestinyBattleInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.DestinyFightDestinyBattleInfo destinyBattleInfo = 1;</code>
     */
    public java.util.List<xddq.pb.DestinyFightDestinyBattleInfo.Builder> 
         getDestinyBattleInfoBuilderList() {
      return internalGetDestinyBattleInfoFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.DestinyFightDestinyBattleInfo, xddq.pb.DestinyFightDestinyBattleInfo.Builder, xddq.pb.DestinyFightDestinyBattleInfoOrBuilder> 
        internalGetDestinyBattleInfoFieldBuilder() {
      if (destinyBattleInfoBuilder_ == null) {
        destinyBattleInfoBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.DestinyFightDestinyBattleInfo, xddq.pb.DestinyFightDestinyBattleInfo.Builder, xddq.pb.DestinyFightDestinyBattleInfoOrBuilder>(
                destinyBattleInfo_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        destinyBattleInfo_ = null;
      }
      return destinyBattleInfoBuilder_;
    }

    private long endTime_ ;
    /**
     * <code>required int64 endTime = 2;</code>
     * @return Whether the endTime field is set.
     */
    @java.lang.Override
    public boolean hasEndTime() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>required int64 endTime = 2;</code>
     * @return The endTime.
     */
    @java.lang.Override
    public long getEndTime() {
      return endTime_;
    }
    /**
     * <code>required int64 endTime = 2;</code>
     * @param value The endTime to set.
     * @return This builder for chaining.
     */
    public Builder setEndTime(long value) {

      endTime_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>required int64 endTime = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearEndTime() {
      bitField0_ = (bitField0_ & ~0x00000002);
      endTime_ = 0L;
      onChanged();
      return this;
    }

    private int taskId_ ;
    /**
     * <code>required int32 taskId = 3;</code>
     * @return Whether the taskId field is set.
     */
    @java.lang.Override
    public boolean hasTaskId() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>required int32 taskId = 3;</code>
     * @return The taskId.
     */
    @java.lang.Override
    public int getTaskId() {
      return taskId_;
    }
    /**
     * <code>required int32 taskId = 3;</code>
     * @param value The taskId to set.
     * @return This builder for chaining.
     */
    public Builder setTaskId(int value) {

      taskId_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 taskId = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearTaskId() {
      bitField0_ = (bitField0_ & ~0x00000004);
      taskId_ = 0;
      onChanged();
      return this;
    }

    private xddq.pb.PlayerBaseDataMsg playerInfo_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PlayerBaseDataMsg, xddq.pb.PlayerBaseDataMsg.Builder, xddq.pb.PlayerBaseDataMsgOrBuilder> playerInfoBuilder_;
    /**
     * <code>optional .xddq.pb.PlayerBaseDataMsg playerInfo = 4;</code>
     * @return Whether the playerInfo field is set.
     */
    public boolean hasPlayerInfo() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional .xddq.pb.PlayerBaseDataMsg playerInfo = 4;</code>
     * @return The playerInfo.
     */
    public xddq.pb.PlayerBaseDataMsg getPlayerInfo() {
      if (playerInfoBuilder_ == null) {
        return playerInfo_ == null ? xddq.pb.PlayerBaseDataMsg.getDefaultInstance() : playerInfo_;
      } else {
        return playerInfoBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.PlayerBaseDataMsg playerInfo = 4;</code>
     */
    public Builder setPlayerInfo(xddq.pb.PlayerBaseDataMsg value) {
      if (playerInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        playerInfo_ = value;
      } else {
        playerInfoBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerBaseDataMsg playerInfo = 4;</code>
     */
    public Builder setPlayerInfo(
        xddq.pb.PlayerBaseDataMsg.Builder builderForValue) {
      if (playerInfoBuilder_ == null) {
        playerInfo_ = builderForValue.build();
      } else {
        playerInfoBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerBaseDataMsg playerInfo = 4;</code>
     */
    public Builder mergePlayerInfo(xddq.pb.PlayerBaseDataMsg value) {
      if (playerInfoBuilder_ == null) {
        if (((bitField0_ & 0x00000008) != 0) &&
          playerInfo_ != null &&
          playerInfo_ != xddq.pb.PlayerBaseDataMsg.getDefaultInstance()) {
          getPlayerInfoBuilder().mergeFrom(value);
        } else {
          playerInfo_ = value;
        }
      } else {
        playerInfoBuilder_.mergeFrom(value);
      }
      if (playerInfo_ != null) {
        bitField0_ |= 0x00000008;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerBaseDataMsg playerInfo = 4;</code>
     */
    public Builder clearPlayerInfo() {
      bitField0_ = (bitField0_ & ~0x00000008);
      playerInfo_ = null;
      if (playerInfoBuilder_ != null) {
        playerInfoBuilder_.dispose();
        playerInfoBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerBaseDataMsg playerInfo = 4;</code>
     */
    public xddq.pb.PlayerBaseDataMsg.Builder getPlayerInfoBuilder() {
      bitField0_ |= 0x00000008;
      onChanged();
      return internalGetPlayerInfoFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.PlayerBaseDataMsg playerInfo = 4;</code>
     */
    public xddq.pb.PlayerBaseDataMsgOrBuilder getPlayerInfoOrBuilder() {
      if (playerInfoBuilder_ != null) {
        return playerInfoBuilder_.getMessageOrBuilder();
      } else {
        return playerInfo_ == null ?
            xddq.pb.PlayerBaseDataMsg.getDefaultInstance() : playerInfo_;
      }
    }
    /**
     * <code>optional .xddq.pb.PlayerBaseDataMsg playerInfo = 4;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PlayerBaseDataMsg, xddq.pb.PlayerBaseDataMsg.Builder, xddq.pb.PlayerBaseDataMsgOrBuilder> 
        internalGetPlayerInfoFieldBuilder() {
      if (playerInfoBuilder_ == null) {
        playerInfoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.PlayerBaseDataMsg, xddq.pb.PlayerBaseDataMsg.Builder, xddq.pb.PlayerBaseDataMsgOrBuilder>(
                getPlayerInfo(),
                getParentForChildren(),
                isClean());
        playerInfo_ = null;
      }
      return playerInfoBuilder_;
    }

    private int pos_ ;
    /**
     * <code>optional int32 pos = 5;</code>
     * @return Whether the pos field is set.
     */
    @java.lang.Override
    public boolean hasPos() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional int32 pos = 5;</code>
     * @return The pos.
     */
    @java.lang.Override
    public int getPos() {
      return pos_;
    }
    /**
     * <code>optional int32 pos = 5;</code>
     * @param value The pos to set.
     * @return This builder for chaining.
     */
    public Builder setPos(int value) {

      pos_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 pos = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearPos() {
      bitField0_ = (bitField0_ & ~0x00000010);
      pos_ = 0;
      onChanged();
      return this;
    }

    private int doubleItem_ ;
    /**
     * <code>optional int32 doubleItem = 6;</code>
     * @return Whether the doubleItem field is set.
     */
    @java.lang.Override
    public boolean hasDoubleItem() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional int32 doubleItem = 6;</code>
     * @return The doubleItem.
     */
    @java.lang.Override
    public int getDoubleItem() {
      return doubleItem_;
    }
    /**
     * <code>optional int32 doubleItem = 6;</code>
     * @param value The doubleItem to set.
     * @return This builder for chaining.
     */
    public Builder setDoubleItem(int value) {

      doubleItem_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 doubleItem = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearDoubleItem() {
      bitField0_ = (bitField0_ & ~0x00000020);
      doubleItem_ = 0;
      onChanged();
      return this;
    }

    private long id_ ;
    /**
     * <code>required int64 id = 7;</code>
     * @return Whether the id field is set.
     */
    @java.lang.Override
    public boolean hasId() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <code>required int64 id = 7;</code>
     * @return The id.
     */
    @java.lang.Override
    public long getId() {
      return id_;
    }
    /**
     * <code>required int64 id = 7;</code>
     * @param value The id to set.
     * @return This builder for chaining.
     */
    public Builder setId(long value) {

      id_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>required int64 id = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearId() {
      bitField0_ = (bitField0_ & ~0x00000040);
      id_ = 0L;
      onChanged();
      return this;
    }

    private boolean canBattle_ ;
    /**
     * <code>optional bool canBattle = 8;</code>
     * @return Whether the canBattle field is set.
     */
    @java.lang.Override
    public boolean hasCanBattle() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <code>optional bool canBattle = 8;</code>
     * @return The canBattle.
     */
    @java.lang.Override
    public boolean getCanBattle() {
      return canBattle_;
    }
    /**
     * <code>optional bool canBattle = 8;</code>
     * @param value The canBattle to set.
     * @return This builder for chaining.
     */
    public Builder setCanBattle(boolean value) {

      canBattle_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }
    /**
     * <code>optional bool canBattle = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearCanBattle() {
      bitField0_ = (bitField0_ & ~0x00000080);
      canBattle_ = false;
      onChanged();
      return this;
    }

    private int robNum_ ;
    /**
     * <code>optional int32 robNum = 9;</code>
     * @return Whether the robNum field is set.
     */
    @java.lang.Override
    public boolean hasRobNum() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <code>optional int32 robNum = 9;</code>
     * @return The robNum.
     */
    @java.lang.Override
    public int getRobNum() {
      return robNum_;
    }
    /**
     * <code>optional int32 robNum = 9;</code>
     * @param value The robNum to set.
     * @return This builder for chaining.
     */
    public Builder setRobNum(int value) {

      robNum_ = value;
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 robNum = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearRobNum() {
      bitField0_ = (bitField0_ & ~0x00000100);
      robNum_ = 0;
      onChanged();
      return this;
    }

    private int usedNum_ ;
    /**
     * <code>optional int32 usedNum = 10;</code>
     * @return Whether the usedNum field is set.
     */
    @java.lang.Override
    public boolean hasUsedNum() {
      return ((bitField0_ & 0x00000200) != 0);
    }
    /**
     * <code>optional int32 usedNum = 10;</code>
     * @return The usedNum.
     */
    @java.lang.Override
    public int getUsedNum() {
      return usedNum_;
    }
    /**
     * <code>optional int32 usedNum = 10;</code>
     * @param value The usedNum to set.
     * @return This builder for chaining.
     */
    public Builder setUsedNum(int value) {

      usedNum_ = value;
      bitField0_ |= 0x00000200;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 usedNum = 10;</code>
     * @return This builder for chaining.
     */
    public Builder clearUsedNum() {
      bitField0_ = (bitField0_ & ~0x00000200);
      usedNum_ = 0;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.DestinyFightBattleInfo)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.DestinyFightBattleInfo)
  private static final xddq.pb.DestinyFightBattleInfo DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.DestinyFightBattleInfo();
  }

  public static xddq.pb.DestinyFightBattleInfo getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<DestinyFightBattleInfo>
      PARSER = new com.google.protobuf.AbstractParser<DestinyFightBattleInfo>() {
    @java.lang.Override
    public DestinyFightBattleInfo parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<DestinyFightBattleInfo> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<DestinyFightBattleInfo> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.DestinyFightBattleInfo getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

