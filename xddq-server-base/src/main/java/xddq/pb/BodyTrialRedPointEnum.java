// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf enum {@code xddq.pb.BodyTrialRedPointEnum}
 */
public enum BodyTrialRedPointEnum
    implements com.google.protobuf.ProtocolMessageEnum {
  /**
   * <code>bodyTrialHasBattleRedPoint = 0;</code>
   */
  bodyTrialHasBattleRedPoint(0),
  /**
   * <code>bodyTrialFloorAwardRedPoint = 1;</code>
   */
  bodyTrialFloorAwardRedPoint(1),
  /**
   * <code>bodyTrialFloorTeamApplyRedPoint = 2;</code>
   */
  bodyTrialFloorTeamApplyRedPoint(2),
  /**
   * <code>bodyTrialGodBodyUpdateRedPoint = 3;</code>
   */
  bodyTrialGodBodyUpdateRedPoint(3),
  /**
   * <code>bodyTrialWorshipRedPoint = 4;</code>
   */
  bodyTrialWorshipRedPoint(4),
  ;

  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      BodyTrialRedPointEnum.class.getName());
  }
  /**
   * <code>bodyTrialHasBattleRedPoint = 0;</code>
   */
  public static final int bodyTrialHasBattleRedPoint_VALUE = 0;
  /**
   * <code>bodyTrialFloorAwardRedPoint = 1;</code>
   */
  public static final int bodyTrialFloorAwardRedPoint_VALUE = 1;
  /**
   * <code>bodyTrialFloorTeamApplyRedPoint = 2;</code>
   */
  public static final int bodyTrialFloorTeamApplyRedPoint_VALUE = 2;
  /**
   * <code>bodyTrialGodBodyUpdateRedPoint = 3;</code>
   */
  public static final int bodyTrialGodBodyUpdateRedPoint_VALUE = 3;
  /**
   * <code>bodyTrialWorshipRedPoint = 4;</code>
   */
  public static final int bodyTrialWorshipRedPoint_VALUE = 4;


  public final int getNumber() {
    return value;
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  @java.lang.Deprecated
  public static BodyTrialRedPointEnum valueOf(int value) {
    return forNumber(value);
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   */
  public static BodyTrialRedPointEnum forNumber(int value) {
    switch (value) {
      case 0: return bodyTrialHasBattleRedPoint;
      case 1: return bodyTrialFloorAwardRedPoint;
      case 2: return bodyTrialFloorTeamApplyRedPoint;
      case 3: return bodyTrialGodBodyUpdateRedPoint;
      case 4: return bodyTrialWorshipRedPoint;
      default: return null;
    }
  }

  public static com.google.protobuf.Internal.EnumLiteMap<BodyTrialRedPointEnum>
      internalGetValueMap() {
    return internalValueMap;
  }
  private static final com.google.protobuf.Internal.EnumLiteMap<
      BodyTrialRedPointEnum> internalValueMap =
        new com.google.protobuf.Internal.EnumLiteMap<BodyTrialRedPointEnum>() {
          public BodyTrialRedPointEnum findValueByNumber(int number) {
            return BodyTrialRedPointEnum.forNumber(number);
          }
        };

  public final com.google.protobuf.Descriptors.EnumValueDescriptor
      getValueDescriptor() {
    return getDescriptor().getValues().get(ordinal());
  }
  public final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptorForType() {
    return getDescriptor();
  }
  public static final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptor() {
    return xddq.pb.Xddq.getDescriptor().getEnumTypes().get(9);
  }

  private static final BodyTrialRedPointEnum[] VALUES = values();

  public static BodyTrialRedPointEnum valueOf(
      com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
    if (desc.getType() != getDescriptor()) {
      throw new java.lang.IllegalArgumentException(
        "EnumValueDescriptor is not for this type.");
    }
    return VALUES[desc.getIndex()];
  }

  private final int value;

  private BodyTrialRedPointEnum(int value) {
    this.value = value;
  }

  // @@protoc_insertion_point(enum_scope:xddq.pb.BodyTrialRedPointEnum)
}

