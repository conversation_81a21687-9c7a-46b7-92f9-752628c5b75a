// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.WarSeasonMonsterListResp}
 */
public final class WarSeasonMonsterListResp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.WarSeasonMonsterListResp)
    WarSeasonMonsterListRespOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      WarSeasonMonsterListResp.class.getName());
  }
  // Use WarSeasonMonsterListResp.newBuilder() to construct.
  private WarSeasonMonsterListResp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private WarSeasonMonsterListResp() {
    warSeasonMonsterData_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_WarSeasonMonsterListResp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_WarSeasonMonsterListResp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.WarSeasonMonsterListResp.class, xddq.pb.WarSeasonMonsterListResp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>optional int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int WARSEASONMONSTERDATA_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.WarSeasonMonsterData> warSeasonMonsterData_;
  /**
   * <code>repeated .xddq.pb.WarSeasonMonsterData warSeasonMonsterData = 2;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.WarSeasonMonsterData> getWarSeasonMonsterDataList() {
    return warSeasonMonsterData_;
  }
  /**
   * <code>repeated .xddq.pb.WarSeasonMonsterData warSeasonMonsterData = 2;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.WarSeasonMonsterDataOrBuilder> 
      getWarSeasonMonsterDataOrBuilderList() {
    return warSeasonMonsterData_;
  }
  /**
   * <code>repeated .xddq.pb.WarSeasonMonsterData warSeasonMonsterData = 2;</code>
   */
  @java.lang.Override
  public int getWarSeasonMonsterDataCount() {
    return warSeasonMonsterData_.size();
  }
  /**
   * <code>repeated .xddq.pb.WarSeasonMonsterData warSeasonMonsterData = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.WarSeasonMonsterData getWarSeasonMonsterData(int index) {
    return warSeasonMonsterData_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.WarSeasonMonsterData warSeasonMonsterData = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.WarSeasonMonsterDataOrBuilder getWarSeasonMonsterDataOrBuilder(
      int index) {
    return warSeasonMonsterData_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    for (int i = 0; i < getWarSeasonMonsterDataCount(); i++) {
      if (!getWarSeasonMonsterData(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    for (int i = 0; i < warSeasonMonsterData_.size(); i++) {
      output.writeMessage(2, warSeasonMonsterData_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    for (int i = 0; i < warSeasonMonsterData_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, warSeasonMonsterData_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.WarSeasonMonsterListResp)) {
      return super.equals(obj);
    }
    xddq.pb.WarSeasonMonsterListResp other = (xddq.pb.WarSeasonMonsterListResp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (!getWarSeasonMonsterDataList()
        .equals(other.getWarSeasonMonsterDataList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (getWarSeasonMonsterDataCount() > 0) {
      hash = (37 * hash) + WARSEASONMONSTERDATA_FIELD_NUMBER;
      hash = (53 * hash) + getWarSeasonMonsterDataList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.WarSeasonMonsterListResp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.WarSeasonMonsterListResp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.WarSeasonMonsterListResp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.WarSeasonMonsterListResp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.WarSeasonMonsterListResp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.WarSeasonMonsterListResp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.WarSeasonMonsterListResp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.WarSeasonMonsterListResp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.WarSeasonMonsterListResp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.WarSeasonMonsterListResp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.WarSeasonMonsterListResp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.WarSeasonMonsterListResp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.WarSeasonMonsterListResp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.WarSeasonMonsterListResp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.WarSeasonMonsterListResp)
      xddq.pb.WarSeasonMonsterListRespOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_WarSeasonMonsterListResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_WarSeasonMonsterListResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.WarSeasonMonsterListResp.class, xddq.pb.WarSeasonMonsterListResp.Builder.class);
    }

    // Construct using xddq.pb.WarSeasonMonsterListResp.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      if (warSeasonMonsterDataBuilder_ == null) {
        warSeasonMonsterData_ = java.util.Collections.emptyList();
      } else {
        warSeasonMonsterData_ = null;
        warSeasonMonsterDataBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000002);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_WarSeasonMonsterListResp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.WarSeasonMonsterListResp getDefaultInstanceForType() {
      return xddq.pb.WarSeasonMonsterListResp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.WarSeasonMonsterListResp build() {
      xddq.pb.WarSeasonMonsterListResp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.WarSeasonMonsterListResp buildPartial() {
      xddq.pb.WarSeasonMonsterListResp result = new xddq.pb.WarSeasonMonsterListResp(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.WarSeasonMonsterListResp result) {
      if (warSeasonMonsterDataBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          warSeasonMonsterData_ = java.util.Collections.unmodifiableList(warSeasonMonsterData_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.warSeasonMonsterData_ = warSeasonMonsterData_;
      } else {
        result.warSeasonMonsterData_ = warSeasonMonsterDataBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.WarSeasonMonsterListResp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.WarSeasonMonsterListResp) {
        return mergeFrom((xddq.pb.WarSeasonMonsterListResp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.WarSeasonMonsterListResp other) {
      if (other == xddq.pb.WarSeasonMonsterListResp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (warSeasonMonsterDataBuilder_ == null) {
        if (!other.warSeasonMonsterData_.isEmpty()) {
          if (warSeasonMonsterData_.isEmpty()) {
            warSeasonMonsterData_ = other.warSeasonMonsterData_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureWarSeasonMonsterDataIsMutable();
            warSeasonMonsterData_.addAll(other.warSeasonMonsterData_);
          }
          onChanged();
        }
      } else {
        if (!other.warSeasonMonsterData_.isEmpty()) {
          if (warSeasonMonsterDataBuilder_.isEmpty()) {
            warSeasonMonsterDataBuilder_.dispose();
            warSeasonMonsterDataBuilder_ = null;
            warSeasonMonsterData_ = other.warSeasonMonsterData_;
            bitField0_ = (bitField0_ & ~0x00000002);
            warSeasonMonsterDataBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetWarSeasonMonsterDataFieldBuilder() : null;
          } else {
            warSeasonMonsterDataBuilder_.addAllMessages(other.warSeasonMonsterData_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      for (int i = 0; i < getWarSeasonMonsterDataCount(); i++) {
        if (!getWarSeasonMonsterData(i).isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              xddq.pb.WarSeasonMonsterData m =
                  input.readMessage(
                      xddq.pb.WarSeasonMonsterData.parser(),
                      extensionRegistry);
              if (warSeasonMonsterDataBuilder_ == null) {
                ensureWarSeasonMonsterDataIsMutable();
                warSeasonMonsterData_.add(m);
              } else {
                warSeasonMonsterDataBuilder_.addMessage(m);
              }
              break;
            } // case 18
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>optional int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>optional int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.WarSeasonMonsterData> warSeasonMonsterData_ =
      java.util.Collections.emptyList();
    private void ensureWarSeasonMonsterDataIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        warSeasonMonsterData_ = new java.util.ArrayList<xddq.pb.WarSeasonMonsterData>(warSeasonMonsterData_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.WarSeasonMonsterData, xddq.pb.WarSeasonMonsterData.Builder, xddq.pb.WarSeasonMonsterDataOrBuilder> warSeasonMonsterDataBuilder_;

    /**
     * <code>repeated .xddq.pb.WarSeasonMonsterData warSeasonMonsterData = 2;</code>
     */
    public java.util.List<xddq.pb.WarSeasonMonsterData> getWarSeasonMonsterDataList() {
      if (warSeasonMonsterDataBuilder_ == null) {
        return java.util.Collections.unmodifiableList(warSeasonMonsterData_);
      } else {
        return warSeasonMonsterDataBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMonsterData warSeasonMonsterData = 2;</code>
     */
    public int getWarSeasonMonsterDataCount() {
      if (warSeasonMonsterDataBuilder_ == null) {
        return warSeasonMonsterData_.size();
      } else {
        return warSeasonMonsterDataBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMonsterData warSeasonMonsterData = 2;</code>
     */
    public xddq.pb.WarSeasonMonsterData getWarSeasonMonsterData(int index) {
      if (warSeasonMonsterDataBuilder_ == null) {
        return warSeasonMonsterData_.get(index);
      } else {
        return warSeasonMonsterDataBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMonsterData warSeasonMonsterData = 2;</code>
     */
    public Builder setWarSeasonMonsterData(
        int index, xddq.pb.WarSeasonMonsterData value) {
      if (warSeasonMonsterDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureWarSeasonMonsterDataIsMutable();
        warSeasonMonsterData_.set(index, value);
        onChanged();
      } else {
        warSeasonMonsterDataBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMonsterData warSeasonMonsterData = 2;</code>
     */
    public Builder setWarSeasonMonsterData(
        int index, xddq.pb.WarSeasonMonsterData.Builder builderForValue) {
      if (warSeasonMonsterDataBuilder_ == null) {
        ensureWarSeasonMonsterDataIsMutable();
        warSeasonMonsterData_.set(index, builderForValue.build());
        onChanged();
      } else {
        warSeasonMonsterDataBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMonsterData warSeasonMonsterData = 2;</code>
     */
    public Builder addWarSeasonMonsterData(xddq.pb.WarSeasonMonsterData value) {
      if (warSeasonMonsterDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureWarSeasonMonsterDataIsMutable();
        warSeasonMonsterData_.add(value);
        onChanged();
      } else {
        warSeasonMonsterDataBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMonsterData warSeasonMonsterData = 2;</code>
     */
    public Builder addWarSeasonMonsterData(
        int index, xddq.pb.WarSeasonMonsterData value) {
      if (warSeasonMonsterDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureWarSeasonMonsterDataIsMutable();
        warSeasonMonsterData_.add(index, value);
        onChanged();
      } else {
        warSeasonMonsterDataBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMonsterData warSeasonMonsterData = 2;</code>
     */
    public Builder addWarSeasonMonsterData(
        xddq.pb.WarSeasonMonsterData.Builder builderForValue) {
      if (warSeasonMonsterDataBuilder_ == null) {
        ensureWarSeasonMonsterDataIsMutable();
        warSeasonMonsterData_.add(builderForValue.build());
        onChanged();
      } else {
        warSeasonMonsterDataBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMonsterData warSeasonMonsterData = 2;</code>
     */
    public Builder addWarSeasonMonsterData(
        int index, xddq.pb.WarSeasonMonsterData.Builder builderForValue) {
      if (warSeasonMonsterDataBuilder_ == null) {
        ensureWarSeasonMonsterDataIsMutable();
        warSeasonMonsterData_.add(index, builderForValue.build());
        onChanged();
      } else {
        warSeasonMonsterDataBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMonsterData warSeasonMonsterData = 2;</code>
     */
    public Builder addAllWarSeasonMonsterData(
        java.lang.Iterable<? extends xddq.pb.WarSeasonMonsterData> values) {
      if (warSeasonMonsterDataBuilder_ == null) {
        ensureWarSeasonMonsterDataIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, warSeasonMonsterData_);
        onChanged();
      } else {
        warSeasonMonsterDataBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMonsterData warSeasonMonsterData = 2;</code>
     */
    public Builder clearWarSeasonMonsterData() {
      if (warSeasonMonsterDataBuilder_ == null) {
        warSeasonMonsterData_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        warSeasonMonsterDataBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMonsterData warSeasonMonsterData = 2;</code>
     */
    public Builder removeWarSeasonMonsterData(int index) {
      if (warSeasonMonsterDataBuilder_ == null) {
        ensureWarSeasonMonsterDataIsMutable();
        warSeasonMonsterData_.remove(index);
        onChanged();
      } else {
        warSeasonMonsterDataBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMonsterData warSeasonMonsterData = 2;</code>
     */
    public xddq.pb.WarSeasonMonsterData.Builder getWarSeasonMonsterDataBuilder(
        int index) {
      return internalGetWarSeasonMonsterDataFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMonsterData warSeasonMonsterData = 2;</code>
     */
    public xddq.pb.WarSeasonMonsterDataOrBuilder getWarSeasonMonsterDataOrBuilder(
        int index) {
      if (warSeasonMonsterDataBuilder_ == null) {
        return warSeasonMonsterData_.get(index);  } else {
        return warSeasonMonsterDataBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMonsterData warSeasonMonsterData = 2;</code>
     */
    public java.util.List<? extends xddq.pb.WarSeasonMonsterDataOrBuilder> 
         getWarSeasonMonsterDataOrBuilderList() {
      if (warSeasonMonsterDataBuilder_ != null) {
        return warSeasonMonsterDataBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(warSeasonMonsterData_);
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMonsterData warSeasonMonsterData = 2;</code>
     */
    public xddq.pb.WarSeasonMonsterData.Builder addWarSeasonMonsterDataBuilder() {
      return internalGetWarSeasonMonsterDataFieldBuilder().addBuilder(
          xddq.pb.WarSeasonMonsterData.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMonsterData warSeasonMonsterData = 2;</code>
     */
    public xddq.pb.WarSeasonMonsterData.Builder addWarSeasonMonsterDataBuilder(
        int index) {
      return internalGetWarSeasonMonsterDataFieldBuilder().addBuilder(
          index, xddq.pb.WarSeasonMonsterData.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMonsterData warSeasonMonsterData = 2;</code>
     */
    public java.util.List<xddq.pb.WarSeasonMonsterData.Builder> 
         getWarSeasonMonsterDataBuilderList() {
      return internalGetWarSeasonMonsterDataFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.WarSeasonMonsterData, xddq.pb.WarSeasonMonsterData.Builder, xddq.pb.WarSeasonMonsterDataOrBuilder> 
        internalGetWarSeasonMonsterDataFieldBuilder() {
      if (warSeasonMonsterDataBuilder_ == null) {
        warSeasonMonsterDataBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.WarSeasonMonsterData, xddq.pb.WarSeasonMonsterData.Builder, xddq.pb.WarSeasonMonsterDataOrBuilder>(
                warSeasonMonsterData_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        warSeasonMonsterData_ = null;
      }
      return warSeasonMonsterDataBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.WarSeasonMonsterListResp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.WarSeasonMonsterListResp)
  private static final xddq.pb.WarSeasonMonsterListResp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.WarSeasonMonsterListResp();
  }

  public static xddq.pb.WarSeasonMonsterListResp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<WarSeasonMonsterListResp>
      PARSER = new com.google.protobuf.AbstractParser<WarSeasonMonsterListResp>() {
    @java.lang.Override
    public WarSeasonMonsterListResp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<WarSeasonMonsterListResp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<WarSeasonMonsterListResp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.WarSeasonMonsterListResp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

