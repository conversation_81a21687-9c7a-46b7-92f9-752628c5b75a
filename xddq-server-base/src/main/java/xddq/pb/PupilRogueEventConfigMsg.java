// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.PupilRogueEventConfigMsg}
 */
public final class PupilRogueEventConfigMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.PupilRogueEventConfigMsg)
    PupilRogueEventConfigMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      PupilRogueEventConfigMsg.class.getName());
  }
  // Use PupilRogueEventConfigMsg.newBuilder() to construct.
  private PupilRogueEventConfigMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private PupilRogueEventConfigMsg() {
    param_ = "";
    exp_ = "";
    itemRate_ = "";
    label_ = "";
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_PupilRogueEventConfigMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_PupilRogueEventConfigMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.PupilRogueEventConfigMsg.class, xddq.pb.PupilRogueEventConfigMsg.Builder.class);
  }

  private int bitField0_;
  public static final int ID_FIELD_NUMBER = 1;
  private int id_ = 0;
  /**
   * <code>required int32 id = 1;</code>
   * @return Whether the id field is set.
   */
  @java.lang.Override
  public boolean hasId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 id = 1;</code>
   * @return The id.
   */
  @java.lang.Override
  public int getId() {
    return id_;
  }

  public static final int TYPE_FIELD_NUMBER = 2;
  private int type_ = 0;
  /**
   * <code>required int32 type = 2;</code>
   * @return Whether the type field is set.
   */
  @java.lang.Override
  public boolean hasType() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>required int32 type = 2;</code>
   * @return The type.
   */
  @java.lang.Override
  public int getType() {
    return type_;
  }

  public static final int PARAM_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private volatile java.lang.Object param_ = "";
  /**
   * <code>required string param = 3;</code>
   * @return Whether the param field is set.
   */
  @java.lang.Override
  public boolean hasParam() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>required string param = 3;</code>
   * @return The param.
   */
  @java.lang.Override
  public java.lang.String getParam() {
    java.lang.Object ref = param_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        param_ = s;
      }
      return s;
    }
  }
  /**
   * <code>required string param = 3;</code>
   * @return The bytes for param.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getParamBytes() {
    java.lang.Object ref = param_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      param_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int EXP_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private volatile java.lang.Object exp_ = "";
  /**
   * <code>required string exp = 4;</code>
   * @return Whether the exp field is set.
   */
  @java.lang.Override
  public boolean hasExp() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>required string exp = 4;</code>
   * @return The exp.
   */
  @java.lang.Override
  public java.lang.String getExp() {
    java.lang.Object ref = exp_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        exp_ = s;
      }
      return s;
    }
  }
  /**
   * <code>required string exp = 4;</code>
   * @return The bytes for exp.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getExpBytes() {
    java.lang.Object ref = exp_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      exp_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ITEMRATE_FIELD_NUMBER = 5;
  @SuppressWarnings("serial")
  private volatile java.lang.Object itemRate_ = "";
  /**
   * <code>required string itemRate = 5;</code>
   * @return Whether the itemRate field is set.
   */
  @java.lang.Override
  public boolean hasItemRate() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>required string itemRate = 5;</code>
   * @return The itemRate.
   */
  @java.lang.Override
  public java.lang.String getItemRate() {
    java.lang.Object ref = itemRate_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        itemRate_ = s;
      }
      return s;
    }
  }
  /**
   * <code>required string itemRate = 5;</code>
   * @return The bytes for itemRate.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getItemRateBytes() {
    java.lang.Object ref = itemRate_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      itemRate_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int WALKLONG_FIELD_NUMBER = 6;
  private int walkLong_ = 0;
  /**
   * <code>required int32 walkLong = 6;</code>
   * @return Whether the walkLong field is set.
   */
  @java.lang.Override
  public boolean hasWalkLong() {
    return ((bitField0_ & 0x00000020) != 0);
  }
  /**
   * <code>required int32 walkLong = 6;</code>
   * @return The walkLong.
   */
  @java.lang.Override
  public int getWalkLong() {
    return walkLong_;
  }

  public static final int QUALITY_FIELD_NUMBER = 7;
  private int quality_ = 0;
  /**
   * <code>required int32 quality = 7;</code>
   * @return Whether the quality field is set.
   */
  @java.lang.Override
  public boolean hasQuality() {
    return ((bitField0_ & 0x00000040) != 0);
  }
  /**
   * <code>required int32 quality = 7;</code>
   * @return The quality.
   */
  @java.lang.Override
  public int getQuality() {
    return quality_;
  }

  public static final int LABEL_FIELD_NUMBER = 8;
  @SuppressWarnings("serial")
  private volatile java.lang.Object label_ = "";
  /**
   * <code>required string label = 8;</code>
   * @return Whether the label field is set.
   */
  @java.lang.Override
  public boolean hasLabel() {
    return ((bitField0_ & 0x00000080) != 0);
  }
  /**
   * <code>required string label = 8;</code>
   * @return The label.
   */
  @java.lang.Override
  public java.lang.String getLabel() {
    java.lang.Object ref = label_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        label_ = s;
      }
      return s;
    }
  }
  /**
   * <code>required string label = 8;</code>
   * @return The bytes for label.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getLabelBytes() {
    java.lang.Object ref = label_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      label_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasId()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasType()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasParam()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasExp()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasItemRate()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasWalkLong()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasQuality()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasLabel()) {
      memoizedIsInitialized = 0;
      return false;
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, id_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(2, type_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 3, param_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 4, exp_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 5, itemRate_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      output.writeInt32(6, walkLong_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      output.writeInt32(7, quality_);
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 8, label_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, id_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, type_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(3, param_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(4, exp_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(5, itemRate_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(6, walkLong_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(7, quality_);
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(8, label_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.PupilRogueEventConfigMsg)) {
      return super.equals(obj);
    }
    xddq.pb.PupilRogueEventConfigMsg other = (xddq.pb.PupilRogueEventConfigMsg) obj;

    if (hasId() != other.hasId()) return false;
    if (hasId()) {
      if (getId()
          != other.getId()) return false;
    }
    if (hasType() != other.hasType()) return false;
    if (hasType()) {
      if (getType()
          != other.getType()) return false;
    }
    if (hasParam() != other.hasParam()) return false;
    if (hasParam()) {
      if (!getParam()
          .equals(other.getParam())) return false;
    }
    if (hasExp() != other.hasExp()) return false;
    if (hasExp()) {
      if (!getExp()
          .equals(other.getExp())) return false;
    }
    if (hasItemRate() != other.hasItemRate()) return false;
    if (hasItemRate()) {
      if (!getItemRate()
          .equals(other.getItemRate())) return false;
    }
    if (hasWalkLong() != other.hasWalkLong()) return false;
    if (hasWalkLong()) {
      if (getWalkLong()
          != other.getWalkLong()) return false;
    }
    if (hasQuality() != other.hasQuality()) return false;
    if (hasQuality()) {
      if (getQuality()
          != other.getQuality()) return false;
    }
    if (hasLabel() != other.hasLabel()) return false;
    if (hasLabel()) {
      if (!getLabel()
          .equals(other.getLabel())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasId()) {
      hash = (37 * hash) + ID_FIELD_NUMBER;
      hash = (53 * hash) + getId();
    }
    if (hasType()) {
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
    }
    if (hasParam()) {
      hash = (37 * hash) + PARAM_FIELD_NUMBER;
      hash = (53 * hash) + getParam().hashCode();
    }
    if (hasExp()) {
      hash = (37 * hash) + EXP_FIELD_NUMBER;
      hash = (53 * hash) + getExp().hashCode();
    }
    if (hasItemRate()) {
      hash = (37 * hash) + ITEMRATE_FIELD_NUMBER;
      hash = (53 * hash) + getItemRate().hashCode();
    }
    if (hasWalkLong()) {
      hash = (37 * hash) + WALKLONG_FIELD_NUMBER;
      hash = (53 * hash) + getWalkLong();
    }
    if (hasQuality()) {
      hash = (37 * hash) + QUALITY_FIELD_NUMBER;
      hash = (53 * hash) + getQuality();
    }
    if (hasLabel()) {
      hash = (37 * hash) + LABEL_FIELD_NUMBER;
      hash = (53 * hash) + getLabel().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.PupilRogueEventConfigMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PupilRogueEventConfigMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PupilRogueEventConfigMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PupilRogueEventConfigMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PupilRogueEventConfigMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PupilRogueEventConfigMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PupilRogueEventConfigMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.PupilRogueEventConfigMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.PupilRogueEventConfigMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.PupilRogueEventConfigMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.PupilRogueEventConfigMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.PupilRogueEventConfigMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.PupilRogueEventConfigMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.PupilRogueEventConfigMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.PupilRogueEventConfigMsg)
      xddq.pb.PupilRogueEventConfigMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PupilRogueEventConfigMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PupilRogueEventConfigMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.PupilRogueEventConfigMsg.class, xddq.pb.PupilRogueEventConfigMsg.Builder.class);
    }

    // Construct using xddq.pb.PupilRogueEventConfigMsg.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      id_ = 0;
      type_ = 0;
      param_ = "";
      exp_ = "";
      itemRate_ = "";
      walkLong_ = 0;
      quality_ = 0;
      label_ = "";
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PupilRogueEventConfigMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.PupilRogueEventConfigMsg getDefaultInstanceForType() {
      return xddq.pb.PupilRogueEventConfigMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.PupilRogueEventConfigMsg build() {
      xddq.pb.PupilRogueEventConfigMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.PupilRogueEventConfigMsg buildPartial() {
      xddq.pb.PupilRogueEventConfigMsg result = new xddq.pb.PupilRogueEventConfigMsg(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.PupilRogueEventConfigMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.id_ = id_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.type_ = type_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.param_ = param_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.exp_ = exp_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.itemRate_ = itemRate_;
        to_bitField0_ |= 0x00000010;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.walkLong_ = walkLong_;
        to_bitField0_ |= 0x00000020;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.quality_ = quality_;
        to_bitField0_ |= 0x00000040;
      }
      if (((from_bitField0_ & 0x00000080) != 0)) {
        result.label_ = label_;
        to_bitField0_ |= 0x00000080;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.PupilRogueEventConfigMsg) {
        return mergeFrom((xddq.pb.PupilRogueEventConfigMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.PupilRogueEventConfigMsg other) {
      if (other == xddq.pb.PupilRogueEventConfigMsg.getDefaultInstance()) return this;
      if (other.hasId()) {
        setId(other.getId());
      }
      if (other.hasType()) {
        setType(other.getType());
      }
      if (other.hasParam()) {
        param_ = other.param_;
        bitField0_ |= 0x00000004;
        onChanged();
      }
      if (other.hasExp()) {
        exp_ = other.exp_;
        bitField0_ |= 0x00000008;
        onChanged();
      }
      if (other.hasItemRate()) {
        itemRate_ = other.itemRate_;
        bitField0_ |= 0x00000010;
        onChanged();
      }
      if (other.hasWalkLong()) {
        setWalkLong(other.getWalkLong());
      }
      if (other.hasQuality()) {
        setQuality(other.getQuality());
      }
      if (other.hasLabel()) {
        label_ = other.label_;
        bitField0_ |= 0x00000080;
        onChanged();
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasId()) {
        return false;
      }
      if (!hasType()) {
        return false;
      }
      if (!hasParam()) {
        return false;
      }
      if (!hasExp()) {
        return false;
      }
      if (!hasItemRate()) {
        return false;
      }
      if (!hasWalkLong()) {
        return false;
      }
      if (!hasQuality()) {
        return false;
      }
      if (!hasLabel()) {
        return false;
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              id_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              type_ = input.readInt32();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 26: {
              param_ = input.readBytes();
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 34: {
              exp_ = input.readBytes();
              bitField0_ |= 0x00000008;
              break;
            } // case 34
            case 42: {
              itemRate_ = input.readBytes();
              bitField0_ |= 0x00000010;
              break;
            } // case 42
            case 48: {
              walkLong_ = input.readInt32();
              bitField0_ |= 0x00000020;
              break;
            } // case 48
            case 56: {
              quality_ = input.readInt32();
              bitField0_ |= 0x00000040;
              break;
            } // case 56
            case 66: {
              label_ = input.readBytes();
              bitField0_ |= 0x00000080;
              break;
            } // case 66
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int id_ ;
    /**
     * <code>required int32 id = 1;</code>
     * @return Whether the id field is set.
     */
    @java.lang.Override
    public boolean hasId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 id = 1;</code>
     * @return The id.
     */
    @java.lang.Override
    public int getId() {
      return id_;
    }
    /**
     * <code>required int32 id = 1;</code>
     * @param value The id to set.
     * @return This builder for chaining.
     */
    public Builder setId(int value) {

      id_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 id = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      id_ = 0;
      onChanged();
      return this;
    }

    private int type_ ;
    /**
     * <code>required int32 type = 2;</code>
     * @return Whether the type field is set.
     */
    @java.lang.Override
    public boolean hasType() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>required int32 type = 2;</code>
     * @return The type.
     */
    @java.lang.Override
    public int getType() {
      return type_;
    }
    /**
     * <code>required int32 type = 2;</code>
     * @param value The type to set.
     * @return This builder for chaining.
     */
    public Builder setType(int value) {

      type_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 type = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearType() {
      bitField0_ = (bitField0_ & ~0x00000002);
      type_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object param_ = "";
    /**
     * <code>required string param = 3;</code>
     * @return Whether the param field is set.
     */
    public boolean hasParam() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>required string param = 3;</code>
     * @return The param.
     */
    public java.lang.String getParam() {
      java.lang.Object ref = param_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          param_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>required string param = 3;</code>
     * @return The bytes for param.
     */
    public com.google.protobuf.ByteString
        getParamBytes() {
      java.lang.Object ref = param_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        param_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>required string param = 3;</code>
     * @param value The param to set.
     * @return This builder for chaining.
     */
    public Builder setParam(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      param_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>required string param = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearParam() {
      param_ = getDefaultInstance().getParam();
      bitField0_ = (bitField0_ & ~0x00000004);
      onChanged();
      return this;
    }
    /**
     * <code>required string param = 3;</code>
     * @param value The bytes for param to set.
     * @return This builder for chaining.
     */
    public Builder setParamBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      param_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }

    private java.lang.Object exp_ = "";
    /**
     * <code>required string exp = 4;</code>
     * @return Whether the exp field is set.
     */
    public boolean hasExp() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>required string exp = 4;</code>
     * @return The exp.
     */
    public java.lang.String getExp() {
      java.lang.Object ref = exp_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          exp_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>required string exp = 4;</code>
     * @return The bytes for exp.
     */
    public com.google.protobuf.ByteString
        getExpBytes() {
      java.lang.Object ref = exp_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        exp_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>required string exp = 4;</code>
     * @param value The exp to set.
     * @return This builder for chaining.
     */
    public Builder setExp(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      exp_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>required string exp = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearExp() {
      exp_ = getDefaultInstance().getExp();
      bitField0_ = (bitField0_ & ~0x00000008);
      onChanged();
      return this;
    }
    /**
     * <code>required string exp = 4;</code>
     * @param value The bytes for exp to set.
     * @return This builder for chaining.
     */
    public Builder setExpBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      exp_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }

    private java.lang.Object itemRate_ = "";
    /**
     * <code>required string itemRate = 5;</code>
     * @return Whether the itemRate field is set.
     */
    public boolean hasItemRate() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>required string itemRate = 5;</code>
     * @return The itemRate.
     */
    public java.lang.String getItemRate() {
      java.lang.Object ref = itemRate_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          itemRate_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>required string itemRate = 5;</code>
     * @return The bytes for itemRate.
     */
    public com.google.protobuf.ByteString
        getItemRateBytes() {
      java.lang.Object ref = itemRate_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        itemRate_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>required string itemRate = 5;</code>
     * @param value The itemRate to set.
     * @return This builder for chaining.
     */
    public Builder setItemRate(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      itemRate_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>required string itemRate = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearItemRate() {
      itemRate_ = getDefaultInstance().getItemRate();
      bitField0_ = (bitField0_ & ~0x00000010);
      onChanged();
      return this;
    }
    /**
     * <code>required string itemRate = 5;</code>
     * @param value The bytes for itemRate to set.
     * @return This builder for chaining.
     */
    public Builder setItemRateBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      itemRate_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }

    private int walkLong_ ;
    /**
     * <code>required int32 walkLong = 6;</code>
     * @return Whether the walkLong field is set.
     */
    @java.lang.Override
    public boolean hasWalkLong() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>required int32 walkLong = 6;</code>
     * @return The walkLong.
     */
    @java.lang.Override
    public int getWalkLong() {
      return walkLong_;
    }
    /**
     * <code>required int32 walkLong = 6;</code>
     * @param value The walkLong to set.
     * @return This builder for chaining.
     */
    public Builder setWalkLong(int value) {

      walkLong_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 walkLong = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearWalkLong() {
      bitField0_ = (bitField0_ & ~0x00000020);
      walkLong_ = 0;
      onChanged();
      return this;
    }

    private int quality_ ;
    /**
     * <code>required int32 quality = 7;</code>
     * @return Whether the quality field is set.
     */
    @java.lang.Override
    public boolean hasQuality() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <code>required int32 quality = 7;</code>
     * @return The quality.
     */
    @java.lang.Override
    public int getQuality() {
      return quality_;
    }
    /**
     * <code>required int32 quality = 7;</code>
     * @param value The quality to set.
     * @return This builder for chaining.
     */
    public Builder setQuality(int value) {

      quality_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 quality = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearQuality() {
      bitField0_ = (bitField0_ & ~0x00000040);
      quality_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object label_ = "";
    /**
     * <code>required string label = 8;</code>
     * @return Whether the label field is set.
     */
    public boolean hasLabel() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <code>required string label = 8;</code>
     * @return The label.
     */
    public java.lang.String getLabel() {
      java.lang.Object ref = label_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          label_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>required string label = 8;</code>
     * @return The bytes for label.
     */
    public com.google.protobuf.ByteString
        getLabelBytes() {
      java.lang.Object ref = label_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        label_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>required string label = 8;</code>
     * @param value The label to set.
     * @return This builder for chaining.
     */
    public Builder setLabel(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      label_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }
    /**
     * <code>required string label = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearLabel() {
      label_ = getDefaultInstance().getLabel();
      bitField0_ = (bitField0_ & ~0x00000080);
      onChanged();
      return this;
    }
    /**
     * <code>required string label = 8;</code>
     * @param value The bytes for label to set.
     * @return This builder for chaining.
     */
    public Builder setLabelBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      label_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.PupilRogueEventConfigMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.PupilRogueEventConfigMsg)
  private static final xddq.pb.PupilRogueEventConfigMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.PupilRogueEventConfigMsg();
  }

  public static xddq.pb.PupilRogueEventConfigMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<PupilRogueEventConfigMsg>
      PARSER = new com.google.protobuf.AbstractParser<PupilRogueEventConfigMsg>() {
    @java.lang.Override
    public PupilRogueEventConfigMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<PupilRogueEventConfigMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<PupilRogueEventConfigMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.PupilRogueEventConfigMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

