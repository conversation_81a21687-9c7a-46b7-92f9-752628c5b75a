// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

public interface GetBindCodeInfoReqOrBuilder extends
    // @@protoc_insertion_point(interface_extends:xddq.pb.GetBindCodeInfoReq)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>required string appId = 1;</code>
   * @return Whether the appId field is set.
   */
  boolean hasAppId();
  /**
   * <code>required string appId = 1;</code>
   * @return The appId.
   */
  java.lang.String getAppId();
  /**
   * <code>required string appId = 1;</code>
   * @return The bytes for appId.
   */
  com.google.protobuf.ByteString
      getAppIdBytes();

  /**
   * <code>required int32 gameId = 2;</code>
   * @return Whether the gameId field is set.
   */
  boolean hasGameId();
  /**
   * <code>required int32 gameId = 2;</code>
   * @return The gameId.
   */
  int getGameId();
}
