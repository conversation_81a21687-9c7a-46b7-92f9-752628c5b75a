// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.SkyTradeGuessSelectReq}
 */
public final class SkyTradeGuessSelectReq extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.SkyTradeGuessSelectReq)
    SkyTradeGuessSelectReqOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      SkyTradeGuessSelectReq.class.getName());
  }
  // Use SkyTradeGuessSelectReq.newBuilder() to construct.
  private SkyTradeGuessSelectReq(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private SkyTradeGuessSelectReq() {
    unionIdArr_ = emptyLongList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_SkyTradeGuessSelectReq_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_SkyTradeGuessSelectReq_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.SkyTradeGuessSelectReq.class, xddq.pb.SkyTradeGuessSelectReq.Builder.class);
  }

  private int bitField0_;
  public static final int ACTIVITYID_FIELD_NUMBER = 1;
  private int activityId_ = 0;
  /**
   * <code>required int32 activityId = 1;</code>
   * @return Whether the activityId field is set.
   */
  @java.lang.Override
  public boolean hasActivityId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 activityId = 1;</code>
   * @return The activityId.
   */
  @java.lang.Override
  public int getActivityId() {
    return activityId_;
  }

  public static final int UNIONIDARR_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private com.google.protobuf.Internal.LongList unionIdArr_ =
      emptyLongList();
  /**
   * <code>repeated int64 unionIdArr = 3;</code>
   * @return A list containing the unionIdArr.
   */
  @java.lang.Override
  public java.util.List<java.lang.Long>
      getUnionIdArrList() {
    return unionIdArr_;
  }
  /**
   * <code>repeated int64 unionIdArr = 3;</code>
   * @return The count of unionIdArr.
   */
  public int getUnionIdArrCount() {
    return unionIdArr_.size();
  }
  /**
   * <code>repeated int64 unionIdArr = 3;</code>
   * @param index The index of the element to return.
   * @return The unionIdArr at the given index.
   */
  public long getUnionIdArr(int index) {
    return unionIdArr_.getLong(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasActivityId()) {
      memoizedIsInitialized = 0;
      return false;
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, activityId_);
    }
    for (int i = 0; i < unionIdArr_.size(); i++) {
      output.writeInt64(3, unionIdArr_.getLong(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, activityId_);
    }
    {
      int dataSize = 0;
      for (int i = 0; i < unionIdArr_.size(); i++) {
        dataSize += com.google.protobuf.CodedOutputStream
          .computeInt64SizeNoTag(unionIdArr_.getLong(i));
      }
      size += dataSize;
      size += 1 * getUnionIdArrList().size();
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.SkyTradeGuessSelectReq)) {
      return super.equals(obj);
    }
    xddq.pb.SkyTradeGuessSelectReq other = (xddq.pb.SkyTradeGuessSelectReq) obj;

    if (hasActivityId() != other.hasActivityId()) return false;
    if (hasActivityId()) {
      if (getActivityId()
          != other.getActivityId()) return false;
    }
    if (!getUnionIdArrList()
        .equals(other.getUnionIdArrList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasActivityId()) {
      hash = (37 * hash) + ACTIVITYID_FIELD_NUMBER;
      hash = (53 * hash) + getActivityId();
    }
    if (getUnionIdArrCount() > 0) {
      hash = (37 * hash) + UNIONIDARR_FIELD_NUMBER;
      hash = (53 * hash) + getUnionIdArrList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.SkyTradeGuessSelectReq parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.SkyTradeGuessSelectReq parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.SkyTradeGuessSelectReq parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.SkyTradeGuessSelectReq parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.SkyTradeGuessSelectReq parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.SkyTradeGuessSelectReq parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.SkyTradeGuessSelectReq parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.SkyTradeGuessSelectReq parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.SkyTradeGuessSelectReq parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.SkyTradeGuessSelectReq parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.SkyTradeGuessSelectReq parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.SkyTradeGuessSelectReq parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.SkyTradeGuessSelectReq prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.SkyTradeGuessSelectReq}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.SkyTradeGuessSelectReq)
      xddq.pb.SkyTradeGuessSelectReqOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_SkyTradeGuessSelectReq_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_SkyTradeGuessSelectReq_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.SkyTradeGuessSelectReq.class, xddq.pb.SkyTradeGuessSelectReq.Builder.class);
    }

    // Construct using xddq.pb.SkyTradeGuessSelectReq.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      activityId_ = 0;
      unionIdArr_ = emptyLongList();
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_SkyTradeGuessSelectReq_descriptor;
    }

    @java.lang.Override
    public xddq.pb.SkyTradeGuessSelectReq getDefaultInstanceForType() {
      return xddq.pb.SkyTradeGuessSelectReq.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.SkyTradeGuessSelectReq build() {
      xddq.pb.SkyTradeGuessSelectReq result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.SkyTradeGuessSelectReq buildPartial() {
      xddq.pb.SkyTradeGuessSelectReq result = new xddq.pb.SkyTradeGuessSelectReq(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.SkyTradeGuessSelectReq result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.activityId_ = activityId_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        unionIdArr_.makeImmutable();
        result.unionIdArr_ = unionIdArr_;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.SkyTradeGuessSelectReq) {
        return mergeFrom((xddq.pb.SkyTradeGuessSelectReq)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.SkyTradeGuessSelectReq other) {
      if (other == xddq.pb.SkyTradeGuessSelectReq.getDefaultInstance()) return this;
      if (other.hasActivityId()) {
        setActivityId(other.getActivityId());
      }
      if (!other.unionIdArr_.isEmpty()) {
        if (unionIdArr_.isEmpty()) {
          unionIdArr_ = other.unionIdArr_;
          unionIdArr_.makeImmutable();
          bitField0_ |= 0x00000002;
        } else {
          ensureUnionIdArrIsMutable();
          unionIdArr_.addAll(other.unionIdArr_);
        }
        onChanged();
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasActivityId()) {
        return false;
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              activityId_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 24: {
              long v = input.readInt64();
              ensureUnionIdArrIsMutable();
              unionIdArr_.addLong(v);
              break;
            } // case 24
            case 26: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              ensureUnionIdArrIsMutable();
              while (input.getBytesUntilLimit() > 0) {
                unionIdArr_.addLong(input.readInt64());
              }
              input.popLimit(limit);
              break;
            } // case 26
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int activityId_ ;
    /**
     * <code>required int32 activityId = 1;</code>
     * @return Whether the activityId field is set.
     */
    @java.lang.Override
    public boolean hasActivityId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 activityId = 1;</code>
     * @return The activityId.
     */
    @java.lang.Override
    public int getActivityId() {
      return activityId_;
    }
    /**
     * <code>required int32 activityId = 1;</code>
     * @param value The activityId to set.
     * @return This builder for chaining.
     */
    public Builder setActivityId(int value) {

      activityId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 activityId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearActivityId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      activityId_ = 0;
      onChanged();
      return this;
    }

    private com.google.protobuf.Internal.LongList unionIdArr_ = emptyLongList();
    private void ensureUnionIdArrIsMutable() {
      if (!unionIdArr_.isModifiable()) {
        unionIdArr_ = makeMutableCopy(unionIdArr_);
      }
      bitField0_ |= 0x00000002;
    }
    /**
     * <code>repeated int64 unionIdArr = 3;</code>
     * @return A list containing the unionIdArr.
     */
    public java.util.List<java.lang.Long>
        getUnionIdArrList() {
      unionIdArr_.makeImmutable();
      return unionIdArr_;
    }
    /**
     * <code>repeated int64 unionIdArr = 3;</code>
     * @return The count of unionIdArr.
     */
    public int getUnionIdArrCount() {
      return unionIdArr_.size();
    }
    /**
     * <code>repeated int64 unionIdArr = 3;</code>
     * @param index The index of the element to return.
     * @return The unionIdArr at the given index.
     */
    public long getUnionIdArr(int index) {
      return unionIdArr_.getLong(index);
    }
    /**
     * <code>repeated int64 unionIdArr = 3;</code>
     * @param index The index to set the value at.
     * @param value The unionIdArr to set.
     * @return This builder for chaining.
     */
    public Builder setUnionIdArr(
        int index, long value) {

      ensureUnionIdArrIsMutable();
      unionIdArr_.setLong(index, value);
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int64 unionIdArr = 3;</code>
     * @param value The unionIdArr to add.
     * @return This builder for chaining.
     */
    public Builder addUnionIdArr(long value) {

      ensureUnionIdArrIsMutable();
      unionIdArr_.addLong(value);
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int64 unionIdArr = 3;</code>
     * @param values The unionIdArr to add.
     * @return This builder for chaining.
     */
    public Builder addAllUnionIdArr(
        java.lang.Iterable<? extends java.lang.Long> values) {
      ensureUnionIdArrIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, unionIdArr_);
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int64 unionIdArr = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearUnionIdArr() {
      unionIdArr_ = emptyLongList();
      bitField0_ = (bitField0_ & ~0x00000002);
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.SkyTradeGuessSelectReq)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.SkyTradeGuessSelectReq)
  private static final xddq.pb.SkyTradeGuessSelectReq DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.SkyTradeGuessSelectReq();
  }

  public static xddq.pb.SkyTradeGuessSelectReq getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<SkyTradeGuessSelectReq>
      PARSER = new com.google.protobuf.AbstractParser<SkyTradeGuessSelectReq>() {
    @java.lang.Override
    public SkyTradeGuessSelectReq parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<SkyTradeGuessSelectReq> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<SkyTradeGuessSelectReq> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.SkyTradeGuessSelectReq getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

