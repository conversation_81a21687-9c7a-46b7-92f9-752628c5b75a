// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.HeavenBattleLevelConfigMsg}
 */
public final class HeavenBattleLevelConfigMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.HeavenBattleLevelConfigMsg)
    HeavenBattleLevelConfigMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      HeavenBattleLevelConfigMsg.class.getName());
  }
  // Use HeavenBattleLevelConfigMsg.newBuilder() to construct.
  private HeavenBattleLevelConfigMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private HeavenBattleLevelConfigMsg() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_HeavenBattleLevelConfigMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_HeavenBattleLevelConfigMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.HeavenBattleLevelConfigMsg.class, xddq.pb.HeavenBattleLevelConfigMsg.Builder.class);
  }

  private int bitField0_;
  public static final int ACTIVITYID_FIELD_NUMBER = 1;
  private int activityId_ = 0;
  /**
   * <code>optional int32 activityId = 1;</code>
   * @return Whether the activityId field is set.
   */
  @java.lang.Override
  public boolean hasActivityId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int32 activityId = 1;</code>
   * @return The activityId.
   */
  @java.lang.Override
  public int getActivityId() {
    return activityId_;
  }

  public static final int LEVEL_FIELD_NUMBER = 2;
  private int level_ = 0;
  /**
   * <code>optional int32 level = 2;</code>
   * @return Whether the level field is set.
   */
  @java.lang.Override
  public boolean hasLevel() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int32 level = 2;</code>
   * @return The level.
   */
  @java.lang.Override
  public int getLevel() {
    return level_;
  }

  public static final int EXP_FIELD_NUMBER = 3;
  private long exp_ = 0L;
  /**
   * <code>optional int64 exp = 3;</code>
   * @return Whether the exp field is set.
   */
  @java.lang.Override
  public boolean hasExp() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional int64 exp = 3;</code>
   * @return The exp.
   */
  @java.lang.Override
  public long getExp() {
    return exp_;
  }

  public static final int ATTACK_FIELD_NUMBER = 4;
  private long attack_ = 0L;
  /**
   * <code>optional int64 attack = 4;</code>
   * @return Whether the attack field is set.
   */
  @java.lang.Override
  public boolean hasAttack() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional int64 attack = 4;</code>
   * @return The attack.
   */
  @java.lang.Override
  public long getAttack() {
    return attack_;
  }

  public static final int DEFENCE_FIELD_NUMBER = 5;
  private long defence_ = 0L;
  /**
   * <code>optional int64 defence = 5;</code>
   * @return Whether the defence field is set.
   */
  @java.lang.Override
  public boolean hasDefence() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>optional int64 defence = 5;</code>
   * @return The defence.
   */
  @java.lang.Override
  public long getDefence() {
    return defence_;
  }

  public static final int HP_FIELD_NUMBER = 6;
  private long hp_ = 0L;
  /**
   * <code>optional int64 hp = 6;</code>
   * @return Whether the hp field is set.
   */
  @java.lang.Override
  public boolean hasHp() {
    return ((bitField0_ & 0x00000020) != 0);
  }
  /**
   * <code>optional int64 hp = 6;</code>
   * @return The hp.
   */
  @java.lang.Override
  public long getHp() {
    return hp_;
  }

  public static final int SPEED_FIELD_NUMBER = 7;
  private long speed_ = 0L;
  /**
   * <code>optional int64 speed = 7;</code>
   * @return Whether the speed field is set.
   */
  @java.lang.Override
  public boolean hasSpeed() {
    return ((bitField0_ & 0x00000040) != 0);
  }
  /**
   * <code>optional int64 speed = 7;</code>
   * @return The speed.
   */
  @java.lang.Override
  public long getSpeed() {
    return speed_;
  }

  public static final int TYPE_FIELD_NUMBER = 8;
  private int type_ = 0;
  /**
   * <code>optional int32 type = 8;</code>
   * @return Whether the type field is set.
   */
  @java.lang.Override
  public boolean hasType() {
    return ((bitField0_ & 0x00000080) != 0);
  }
  /**
   * <code>optional int32 type = 8;</code>
   * @return The type.
   */
  @java.lang.Override
  public int getType() {
    return type_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, activityId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(2, level_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt64(3, exp_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt64(4, attack_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      output.writeInt64(5, defence_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      output.writeInt64(6, hp_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      output.writeInt64(7, speed_);
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      output.writeInt32(8, type_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, activityId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, level_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(3, exp_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(4, attack_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(5, defence_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(6, hp_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(7, speed_);
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(8, type_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.HeavenBattleLevelConfigMsg)) {
      return super.equals(obj);
    }
    xddq.pb.HeavenBattleLevelConfigMsg other = (xddq.pb.HeavenBattleLevelConfigMsg) obj;

    if (hasActivityId() != other.hasActivityId()) return false;
    if (hasActivityId()) {
      if (getActivityId()
          != other.getActivityId()) return false;
    }
    if (hasLevel() != other.hasLevel()) return false;
    if (hasLevel()) {
      if (getLevel()
          != other.getLevel()) return false;
    }
    if (hasExp() != other.hasExp()) return false;
    if (hasExp()) {
      if (getExp()
          != other.getExp()) return false;
    }
    if (hasAttack() != other.hasAttack()) return false;
    if (hasAttack()) {
      if (getAttack()
          != other.getAttack()) return false;
    }
    if (hasDefence() != other.hasDefence()) return false;
    if (hasDefence()) {
      if (getDefence()
          != other.getDefence()) return false;
    }
    if (hasHp() != other.hasHp()) return false;
    if (hasHp()) {
      if (getHp()
          != other.getHp()) return false;
    }
    if (hasSpeed() != other.hasSpeed()) return false;
    if (hasSpeed()) {
      if (getSpeed()
          != other.getSpeed()) return false;
    }
    if (hasType() != other.hasType()) return false;
    if (hasType()) {
      if (getType()
          != other.getType()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasActivityId()) {
      hash = (37 * hash) + ACTIVITYID_FIELD_NUMBER;
      hash = (53 * hash) + getActivityId();
    }
    if (hasLevel()) {
      hash = (37 * hash) + LEVEL_FIELD_NUMBER;
      hash = (53 * hash) + getLevel();
    }
    if (hasExp()) {
      hash = (37 * hash) + EXP_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getExp());
    }
    if (hasAttack()) {
      hash = (37 * hash) + ATTACK_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getAttack());
    }
    if (hasDefence()) {
      hash = (37 * hash) + DEFENCE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getDefence());
    }
    if (hasHp()) {
      hash = (37 * hash) + HP_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getHp());
    }
    if (hasSpeed()) {
      hash = (37 * hash) + SPEED_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getSpeed());
    }
    if (hasType()) {
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.HeavenBattleLevelConfigMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.HeavenBattleLevelConfigMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.HeavenBattleLevelConfigMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.HeavenBattleLevelConfigMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.HeavenBattleLevelConfigMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.HeavenBattleLevelConfigMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.HeavenBattleLevelConfigMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.HeavenBattleLevelConfigMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.HeavenBattleLevelConfigMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.HeavenBattleLevelConfigMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.HeavenBattleLevelConfigMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.HeavenBattleLevelConfigMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.HeavenBattleLevelConfigMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.HeavenBattleLevelConfigMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.HeavenBattleLevelConfigMsg)
      xddq.pb.HeavenBattleLevelConfigMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_HeavenBattleLevelConfigMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_HeavenBattleLevelConfigMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.HeavenBattleLevelConfigMsg.class, xddq.pb.HeavenBattleLevelConfigMsg.Builder.class);
    }

    // Construct using xddq.pb.HeavenBattleLevelConfigMsg.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      activityId_ = 0;
      level_ = 0;
      exp_ = 0L;
      attack_ = 0L;
      defence_ = 0L;
      hp_ = 0L;
      speed_ = 0L;
      type_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_HeavenBattleLevelConfigMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.HeavenBattleLevelConfigMsg getDefaultInstanceForType() {
      return xddq.pb.HeavenBattleLevelConfigMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.HeavenBattleLevelConfigMsg build() {
      xddq.pb.HeavenBattleLevelConfigMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.HeavenBattleLevelConfigMsg buildPartial() {
      xddq.pb.HeavenBattleLevelConfigMsg result = new xddq.pb.HeavenBattleLevelConfigMsg(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.HeavenBattleLevelConfigMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.activityId_ = activityId_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.level_ = level_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.exp_ = exp_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.attack_ = attack_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.defence_ = defence_;
        to_bitField0_ |= 0x00000010;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.hp_ = hp_;
        to_bitField0_ |= 0x00000020;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.speed_ = speed_;
        to_bitField0_ |= 0x00000040;
      }
      if (((from_bitField0_ & 0x00000080) != 0)) {
        result.type_ = type_;
        to_bitField0_ |= 0x00000080;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.HeavenBattleLevelConfigMsg) {
        return mergeFrom((xddq.pb.HeavenBattleLevelConfigMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.HeavenBattleLevelConfigMsg other) {
      if (other == xddq.pb.HeavenBattleLevelConfigMsg.getDefaultInstance()) return this;
      if (other.hasActivityId()) {
        setActivityId(other.getActivityId());
      }
      if (other.hasLevel()) {
        setLevel(other.getLevel());
      }
      if (other.hasExp()) {
        setExp(other.getExp());
      }
      if (other.hasAttack()) {
        setAttack(other.getAttack());
      }
      if (other.hasDefence()) {
        setDefence(other.getDefence());
      }
      if (other.hasHp()) {
        setHp(other.getHp());
      }
      if (other.hasSpeed()) {
        setSpeed(other.getSpeed());
      }
      if (other.hasType()) {
        setType(other.getType());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              activityId_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              level_ = input.readInt32();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 24: {
              exp_ = input.readInt64();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 32: {
              attack_ = input.readInt64();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            case 40: {
              defence_ = input.readInt64();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            case 48: {
              hp_ = input.readInt64();
              bitField0_ |= 0x00000020;
              break;
            } // case 48
            case 56: {
              speed_ = input.readInt64();
              bitField0_ |= 0x00000040;
              break;
            } // case 56
            case 64: {
              type_ = input.readInt32();
              bitField0_ |= 0x00000080;
              break;
            } // case 64
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int activityId_ ;
    /**
     * <code>optional int32 activityId = 1;</code>
     * @return Whether the activityId field is set.
     */
    @java.lang.Override
    public boolean hasActivityId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 activityId = 1;</code>
     * @return The activityId.
     */
    @java.lang.Override
    public int getActivityId() {
      return activityId_;
    }
    /**
     * <code>optional int32 activityId = 1;</code>
     * @param value The activityId to set.
     * @return This builder for chaining.
     */
    public Builder setActivityId(int value) {

      activityId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 activityId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearActivityId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      activityId_ = 0;
      onChanged();
      return this;
    }

    private int level_ ;
    /**
     * <code>optional int32 level = 2;</code>
     * @return Whether the level field is set.
     */
    @java.lang.Override
    public boolean hasLevel() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 level = 2;</code>
     * @return The level.
     */
    @java.lang.Override
    public int getLevel() {
      return level_;
    }
    /**
     * <code>optional int32 level = 2;</code>
     * @param value The level to set.
     * @return This builder for chaining.
     */
    public Builder setLevel(int value) {

      level_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 level = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearLevel() {
      bitField0_ = (bitField0_ & ~0x00000002);
      level_ = 0;
      onChanged();
      return this;
    }

    private long exp_ ;
    /**
     * <code>optional int64 exp = 3;</code>
     * @return Whether the exp field is set.
     */
    @java.lang.Override
    public boolean hasExp() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int64 exp = 3;</code>
     * @return The exp.
     */
    @java.lang.Override
    public long getExp() {
      return exp_;
    }
    /**
     * <code>optional int64 exp = 3;</code>
     * @param value The exp to set.
     * @return This builder for chaining.
     */
    public Builder setExp(long value) {

      exp_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 exp = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearExp() {
      bitField0_ = (bitField0_ & ~0x00000004);
      exp_ = 0L;
      onChanged();
      return this;
    }

    private long attack_ ;
    /**
     * <code>optional int64 attack = 4;</code>
     * @return Whether the attack field is set.
     */
    @java.lang.Override
    public boolean hasAttack() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int64 attack = 4;</code>
     * @return The attack.
     */
    @java.lang.Override
    public long getAttack() {
      return attack_;
    }
    /**
     * <code>optional int64 attack = 4;</code>
     * @param value The attack to set.
     * @return This builder for chaining.
     */
    public Builder setAttack(long value) {

      attack_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 attack = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearAttack() {
      bitField0_ = (bitField0_ & ~0x00000008);
      attack_ = 0L;
      onChanged();
      return this;
    }

    private long defence_ ;
    /**
     * <code>optional int64 defence = 5;</code>
     * @return Whether the defence field is set.
     */
    @java.lang.Override
    public boolean hasDefence() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional int64 defence = 5;</code>
     * @return The defence.
     */
    @java.lang.Override
    public long getDefence() {
      return defence_;
    }
    /**
     * <code>optional int64 defence = 5;</code>
     * @param value The defence to set.
     * @return This builder for chaining.
     */
    public Builder setDefence(long value) {

      defence_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 defence = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearDefence() {
      bitField0_ = (bitField0_ & ~0x00000010);
      defence_ = 0L;
      onChanged();
      return this;
    }

    private long hp_ ;
    /**
     * <code>optional int64 hp = 6;</code>
     * @return Whether the hp field is set.
     */
    @java.lang.Override
    public boolean hasHp() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional int64 hp = 6;</code>
     * @return The hp.
     */
    @java.lang.Override
    public long getHp() {
      return hp_;
    }
    /**
     * <code>optional int64 hp = 6;</code>
     * @param value The hp to set.
     * @return This builder for chaining.
     */
    public Builder setHp(long value) {

      hp_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 hp = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearHp() {
      bitField0_ = (bitField0_ & ~0x00000020);
      hp_ = 0L;
      onChanged();
      return this;
    }

    private long speed_ ;
    /**
     * <code>optional int64 speed = 7;</code>
     * @return Whether the speed field is set.
     */
    @java.lang.Override
    public boolean hasSpeed() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <code>optional int64 speed = 7;</code>
     * @return The speed.
     */
    @java.lang.Override
    public long getSpeed() {
      return speed_;
    }
    /**
     * <code>optional int64 speed = 7;</code>
     * @param value The speed to set.
     * @return This builder for chaining.
     */
    public Builder setSpeed(long value) {

      speed_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 speed = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearSpeed() {
      bitField0_ = (bitField0_ & ~0x00000040);
      speed_ = 0L;
      onChanged();
      return this;
    }

    private int type_ ;
    /**
     * <code>optional int32 type = 8;</code>
     * @return Whether the type field is set.
     */
    @java.lang.Override
    public boolean hasType() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <code>optional int32 type = 8;</code>
     * @return The type.
     */
    @java.lang.Override
    public int getType() {
      return type_;
    }
    /**
     * <code>optional int32 type = 8;</code>
     * @param value The type to set.
     * @return This builder for chaining.
     */
    public Builder setType(int value) {

      type_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 type = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearType() {
      bitField0_ = (bitField0_ & ~0x00000080);
      type_ = 0;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.HeavenBattleLevelConfigMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.HeavenBattleLevelConfigMsg)
  private static final xddq.pb.HeavenBattleLevelConfigMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.HeavenBattleLevelConfigMsg();
  }

  public static xddq.pb.HeavenBattleLevelConfigMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<HeavenBattleLevelConfigMsg>
      PARSER = new com.google.protobuf.AbstractParser<HeavenBattleLevelConfigMsg>() {
    @java.lang.Override
    public HeavenBattleLevelConfigMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<HeavenBattleLevelConfigMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<HeavenBattleLevelConfigMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.HeavenBattleLevelConfigMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

