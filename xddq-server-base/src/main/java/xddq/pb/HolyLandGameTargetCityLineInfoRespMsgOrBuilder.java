// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

public interface HolyLandGameTargetCityLineInfoRespMsgOrBuilder extends
    // @@protoc_insertion_point(interface_extends:xddq.pb.HolyLandGameTargetCityLineInfoRespMsg)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  boolean hasRet();
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  int getRet();

  /**
   * <code>repeated .xddq.pb.HolyLandGameLinePlayerInfo attackInfo = 2;</code>
   */
  java.util.List<xddq.pb.HolyLandGameLinePlayerInfo> 
      getAttackInfoList();
  /**
   * <code>repeated .xddq.pb.HolyLandGameLinePlayerInfo attackInfo = 2;</code>
   */
  xddq.pb.HolyLandGameLinePlayerInfo getAttackInfo(int index);
  /**
   * <code>repeated .xddq.pb.HolyLandGameLinePlayerInfo attackInfo = 2;</code>
   */
  int getAttackInfoCount();
  /**
   * <code>repeated .xddq.pb.HolyLandGameLinePlayerInfo attackInfo = 2;</code>
   */
  java.util.List<? extends xddq.pb.HolyLandGameLinePlayerInfoOrBuilder> 
      getAttackInfoOrBuilderList();
  /**
   * <code>repeated .xddq.pb.HolyLandGameLinePlayerInfo attackInfo = 2;</code>
   */
  xddq.pb.HolyLandGameLinePlayerInfoOrBuilder getAttackInfoOrBuilder(
      int index);

  /**
   * <code>repeated .xddq.pb.HolyLandGameLinePlayerInfo defendInfo = 3;</code>
   */
  java.util.List<xddq.pb.HolyLandGameLinePlayerInfo> 
      getDefendInfoList();
  /**
   * <code>repeated .xddq.pb.HolyLandGameLinePlayerInfo defendInfo = 3;</code>
   */
  xddq.pb.HolyLandGameLinePlayerInfo getDefendInfo(int index);
  /**
   * <code>repeated .xddq.pb.HolyLandGameLinePlayerInfo defendInfo = 3;</code>
   */
  int getDefendInfoCount();
  /**
   * <code>repeated .xddq.pb.HolyLandGameLinePlayerInfo defendInfo = 3;</code>
   */
  java.util.List<? extends xddq.pb.HolyLandGameLinePlayerInfoOrBuilder> 
      getDefendInfoOrBuilderList();
  /**
   * <code>repeated .xddq.pb.HolyLandGameLinePlayerInfo defendInfo = 3;</code>
   */
  xddq.pb.HolyLandGameLinePlayerInfoOrBuilder getDefendInfoOrBuilder(
      int index);

  /**
   * <code>repeated .xddq.pb.HolyLandBattleMsg battleInfo = 4;</code>
   */
  java.util.List<xddq.pb.HolyLandBattleMsg> 
      getBattleInfoList();
  /**
   * <code>repeated .xddq.pb.HolyLandBattleMsg battleInfo = 4;</code>
   */
  xddq.pb.HolyLandBattleMsg getBattleInfo(int index);
  /**
   * <code>repeated .xddq.pb.HolyLandBattleMsg battleInfo = 4;</code>
   */
  int getBattleInfoCount();
  /**
   * <code>repeated .xddq.pb.HolyLandBattleMsg battleInfo = 4;</code>
   */
  java.util.List<? extends xddq.pb.HolyLandBattleMsgOrBuilder> 
      getBattleInfoOrBuilderList();
  /**
   * <code>repeated .xddq.pb.HolyLandBattleMsg battleInfo = 4;</code>
   */
  xddq.pb.HolyLandBattleMsgOrBuilder getBattleInfoOrBuilder(
      int index);
}
