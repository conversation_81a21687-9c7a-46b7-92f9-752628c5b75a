// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.WarSeasonReportHeadMsg}
 */
public final class WarSeasonReportHeadMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.WarSeasonReportHeadMsg)
    WarSeasonReportHeadMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      WarSeasonReportHeadMsg.class.getName());
  }
  // Use WarSeasonReportHeadMsg.newBuilder() to construct.
  private WarSeasonReportHeadMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private WarSeasonReportHeadMsg() {
    unionName_ = "";
    buffList_ = emptyIntList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_WarSeasonReportHeadMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_WarSeasonReportHeadMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.WarSeasonReportHeadMsg.class, xddq.pb.WarSeasonReportHeadMsg.Builder.class);
  }

  private int bitField0_;
  public static final int PLAYERHEADANDNAMEMSG_FIELD_NUMBER = 1;
  private xddq.pb.PlayerHeadAndNameMsg playerHeadAndNameMsg_;
  /**
   * <code>optional .xddq.pb.PlayerHeadAndNameMsg playerHeadAndNameMsg = 1;</code>
   * @return Whether the playerHeadAndNameMsg field is set.
   */
  @java.lang.Override
  public boolean hasPlayerHeadAndNameMsg() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional .xddq.pb.PlayerHeadAndNameMsg playerHeadAndNameMsg = 1;</code>
   * @return The playerHeadAndNameMsg.
   */
  @java.lang.Override
  public xddq.pb.PlayerHeadAndNameMsg getPlayerHeadAndNameMsg() {
    return playerHeadAndNameMsg_ == null ? xddq.pb.PlayerHeadAndNameMsg.getDefaultInstance() : playerHeadAndNameMsg_;
  }
  /**
   * <code>optional .xddq.pb.PlayerHeadAndNameMsg playerHeadAndNameMsg = 1;</code>
   */
  @java.lang.Override
  public xddq.pb.PlayerHeadAndNameMsgOrBuilder getPlayerHeadAndNameMsgOrBuilder() {
    return playerHeadAndNameMsg_ == null ? xddq.pb.PlayerHeadAndNameMsg.getDefaultInstance() : playerHeadAndNameMsg_;
  }

  public static final int UNIONNAME_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private volatile java.lang.Object unionName_ = "";
  /**
   * <code>optional string unionName = 2;</code>
   * @return Whether the unionName field is set.
   */
  @java.lang.Override
  public boolean hasUnionName() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional string unionName = 2;</code>
   * @return The unionName.
   */
  @java.lang.Override
  public java.lang.String getUnionName() {
    java.lang.Object ref = unionName_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        unionName_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string unionName = 2;</code>
   * @return The bytes for unionName.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getUnionNameBytes() {
    java.lang.Object ref = unionName_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      unionName_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ENERGY_FIELD_NUMBER = 3;
  private int energy_ = 0;
  /**
   * <code>optional int32 energy = 3;</code>
   * @return Whether the energy field is set.
   */
  @java.lang.Override
  public boolean hasEnergy() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional int32 energy = 3;</code>
   * @return The energy.
   */
  @java.lang.Override
  public int getEnergy() {
    return energy_;
  }

  public static final int NPCID_FIELD_NUMBER = 4;
  private int npcId_ = 0;
  /**
   * <code>optional int32 npcId = 4;</code>
   * @return Whether the npcId field is set.
   */
  @java.lang.Override
  public boolean hasNpcId() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional int32 npcId = 4;</code>
   * @return The npcId.
   */
  @java.lang.Override
  public int getNpcId() {
    return npcId_;
  }

  public static final int COSTENERGY_FIELD_NUMBER = 5;
  private int costEnergy_ = 0;
  /**
   * <code>optional int32 costEnergy = 5;</code>
   * @return Whether the costEnergy field is set.
   */
  @java.lang.Override
  public boolean hasCostEnergy() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>optional int32 costEnergy = 5;</code>
   * @return The costEnergy.
   */
  @java.lang.Override
  public int getCostEnergy() {
    return costEnergy_;
  }

  public static final int BODYINDEX_FIELD_NUMBER = 6;
  private int bodyIndex_ = 0;
  /**
   * <code>optional int32 bodyIndex = 6;</code>
   * @return Whether the bodyIndex field is set.
   */
  @java.lang.Override
  public boolean hasBodyIndex() {
    return ((bitField0_ & 0x00000020) != 0);
  }
  /**
   * <code>optional int32 bodyIndex = 6;</code>
   * @return The bodyIndex.
   */
  @java.lang.Override
  public int getBodyIndex() {
    return bodyIndex_;
  }

  public static final int BUFFLIST_FIELD_NUMBER = 7;
  @SuppressWarnings("serial")
  private com.google.protobuf.Internal.IntList buffList_ =
      emptyIntList();
  /**
   * <code>repeated int32 buffList = 7;</code>
   * @return A list containing the buffList.
   */
  @java.lang.Override
  public java.util.List<java.lang.Integer>
      getBuffListList() {
    return buffList_;
  }
  /**
   * <code>repeated int32 buffList = 7;</code>
   * @return The count of buffList.
   */
  public int getBuffListCount() {
    return buffList_.size();
  }
  /**
   * <code>repeated int32 buffList = 7;</code>
   * @param index The index of the element to return.
   * @return The buffList at the given index.
   */
  public int getBuffList(int index) {
    return buffList_.getInt(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeMessage(1, getPlayerHeadAndNameMsg());
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 2, unionName_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt32(3, energy_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt32(4, npcId_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      output.writeInt32(5, costEnergy_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      output.writeInt32(6, bodyIndex_);
    }
    for (int i = 0; i < buffList_.size(); i++) {
      output.writeInt32(7, buffList_.getInt(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, getPlayerHeadAndNameMsg());
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(2, unionName_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(3, energy_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(4, npcId_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(5, costEnergy_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(6, bodyIndex_);
    }
    {
      int dataSize = 0;
      for (int i = 0; i < buffList_.size(); i++) {
        dataSize += com.google.protobuf.CodedOutputStream
          .computeInt32SizeNoTag(buffList_.getInt(i));
      }
      size += dataSize;
      size += 1 * getBuffListList().size();
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.WarSeasonReportHeadMsg)) {
      return super.equals(obj);
    }
    xddq.pb.WarSeasonReportHeadMsg other = (xddq.pb.WarSeasonReportHeadMsg) obj;

    if (hasPlayerHeadAndNameMsg() != other.hasPlayerHeadAndNameMsg()) return false;
    if (hasPlayerHeadAndNameMsg()) {
      if (!getPlayerHeadAndNameMsg()
          .equals(other.getPlayerHeadAndNameMsg())) return false;
    }
    if (hasUnionName() != other.hasUnionName()) return false;
    if (hasUnionName()) {
      if (!getUnionName()
          .equals(other.getUnionName())) return false;
    }
    if (hasEnergy() != other.hasEnergy()) return false;
    if (hasEnergy()) {
      if (getEnergy()
          != other.getEnergy()) return false;
    }
    if (hasNpcId() != other.hasNpcId()) return false;
    if (hasNpcId()) {
      if (getNpcId()
          != other.getNpcId()) return false;
    }
    if (hasCostEnergy() != other.hasCostEnergy()) return false;
    if (hasCostEnergy()) {
      if (getCostEnergy()
          != other.getCostEnergy()) return false;
    }
    if (hasBodyIndex() != other.hasBodyIndex()) return false;
    if (hasBodyIndex()) {
      if (getBodyIndex()
          != other.getBodyIndex()) return false;
    }
    if (!getBuffListList()
        .equals(other.getBuffListList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasPlayerHeadAndNameMsg()) {
      hash = (37 * hash) + PLAYERHEADANDNAMEMSG_FIELD_NUMBER;
      hash = (53 * hash) + getPlayerHeadAndNameMsg().hashCode();
    }
    if (hasUnionName()) {
      hash = (37 * hash) + UNIONNAME_FIELD_NUMBER;
      hash = (53 * hash) + getUnionName().hashCode();
    }
    if (hasEnergy()) {
      hash = (37 * hash) + ENERGY_FIELD_NUMBER;
      hash = (53 * hash) + getEnergy();
    }
    if (hasNpcId()) {
      hash = (37 * hash) + NPCID_FIELD_NUMBER;
      hash = (53 * hash) + getNpcId();
    }
    if (hasCostEnergy()) {
      hash = (37 * hash) + COSTENERGY_FIELD_NUMBER;
      hash = (53 * hash) + getCostEnergy();
    }
    if (hasBodyIndex()) {
      hash = (37 * hash) + BODYINDEX_FIELD_NUMBER;
      hash = (53 * hash) + getBodyIndex();
    }
    if (getBuffListCount() > 0) {
      hash = (37 * hash) + BUFFLIST_FIELD_NUMBER;
      hash = (53 * hash) + getBuffListList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.WarSeasonReportHeadMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.WarSeasonReportHeadMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.WarSeasonReportHeadMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.WarSeasonReportHeadMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.WarSeasonReportHeadMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.WarSeasonReportHeadMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.WarSeasonReportHeadMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.WarSeasonReportHeadMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.WarSeasonReportHeadMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.WarSeasonReportHeadMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.WarSeasonReportHeadMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.WarSeasonReportHeadMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.WarSeasonReportHeadMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.WarSeasonReportHeadMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.WarSeasonReportHeadMsg)
      xddq.pb.WarSeasonReportHeadMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_WarSeasonReportHeadMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_WarSeasonReportHeadMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.WarSeasonReportHeadMsg.class, xddq.pb.WarSeasonReportHeadMsg.Builder.class);
    }

    // Construct using xddq.pb.WarSeasonReportHeadMsg.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetPlayerHeadAndNameMsgFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      playerHeadAndNameMsg_ = null;
      if (playerHeadAndNameMsgBuilder_ != null) {
        playerHeadAndNameMsgBuilder_.dispose();
        playerHeadAndNameMsgBuilder_ = null;
      }
      unionName_ = "";
      energy_ = 0;
      npcId_ = 0;
      costEnergy_ = 0;
      bodyIndex_ = 0;
      buffList_ = emptyIntList();
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_WarSeasonReportHeadMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.WarSeasonReportHeadMsg getDefaultInstanceForType() {
      return xddq.pb.WarSeasonReportHeadMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.WarSeasonReportHeadMsg build() {
      xddq.pb.WarSeasonReportHeadMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.WarSeasonReportHeadMsg buildPartial() {
      xddq.pb.WarSeasonReportHeadMsg result = new xddq.pb.WarSeasonReportHeadMsg(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.WarSeasonReportHeadMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.playerHeadAndNameMsg_ = playerHeadAndNameMsgBuilder_ == null
            ? playerHeadAndNameMsg_
            : playerHeadAndNameMsgBuilder_.build();
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.unionName_ = unionName_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.energy_ = energy_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.npcId_ = npcId_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.costEnergy_ = costEnergy_;
        to_bitField0_ |= 0x00000010;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.bodyIndex_ = bodyIndex_;
        to_bitField0_ |= 0x00000020;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        buffList_.makeImmutable();
        result.buffList_ = buffList_;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.WarSeasonReportHeadMsg) {
        return mergeFrom((xddq.pb.WarSeasonReportHeadMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.WarSeasonReportHeadMsg other) {
      if (other == xddq.pb.WarSeasonReportHeadMsg.getDefaultInstance()) return this;
      if (other.hasPlayerHeadAndNameMsg()) {
        mergePlayerHeadAndNameMsg(other.getPlayerHeadAndNameMsg());
      }
      if (other.hasUnionName()) {
        unionName_ = other.unionName_;
        bitField0_ |= 0x00000002;
        onChanged();
      }
      if (other.hasEnergy()) {
        setEnergy(other.getEnergy());
      }
      if (other.hasNpcId()) {
        setNpcId(other.getNpcId());
      }
      if (other.hasCostEnergy()) {
        setCostEnergy(other.getCostEnergy());
      }
      if (other.hasBodyIndex()) {
        setBodyIndex(other.getBodyIndex());
      }
      if (!other.buffList_.isEmpty()) {
        if (buffList_.isEmpty()) {
          buffList_ = other.buffList_;
          buffList_.makeImmutable();
          bitField0_ |= 0x00000040;
        } else {
          ensureBuffListIsMutable();
          buffList_.addAll(other.buffList_);
        }
        onChanged();
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              input.readMessage(
                  internalGetPlayerHeadAndNameMsgFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 18: {
              unionName_ = input.readBytes();
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 24: {
              energy_ = input.readInt32();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 32: {
              npcId_ = input.readInt32();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            case 40: {
              costEnergy_ = input.readInt32();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            case 48: {
              bodyIndex_ = input.readInt32();
              bitField0_ |= 0x00000020;
              break;
            } // case 48
            case 56: {
              int v = input.readInt32();
              ensureBuffListIsMutable();
              buffList_.addInt(v);
              break;
            } // case 56
            case 58: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              ensureBuffListIsMutable();
              while (input.getBytesUntilLimit() > 0) {
                buffList_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            } // case 58
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private xddq.pb.PlayerHeadAndNameMsg playerHeadAndNameMsg_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PlayerHeadAndNameMsg, xddq.pb.PlayerHeadAndNameMsg.Builder, xddq.pb.PlayerHeadAndNameMsgOrBuilder> playerHeadAndNameMsgBuilder_;
    /**
     * <code>optional .xddq.pb.PlayerHeadAndNameMsg playerHeadAndNameMsg = 1;</code>
     * @return Whether the playerHeadAndNameMsg field is set.
     */
    public boolean hasPlayerHeadAndNameMsg() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadAndNameMsg playerHeadAndNameMsg = 1;</code>
     * @return The playerHeadAndNameMsg.
     */
    public xddq.pb.PlayerHeadAndNameMsg getPlayerHeadAndNameMsg() {
      if (playerHeadAndNameMsgBuilder_ == null) {
        return playerHeadAndNameMsg_ == null ? xddq.pb.PlayerHeadAndNameMsg.getDefaultInstance() : playerHeadAndNameMsg_;
      } else {
        return playerHeadAndNameMsgBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadAndNameMsg playerHeadAndNameMsg = 1;</code>
     */
    public Builder setPlayerHeadAndNameMsg(xddq.pb.PlayerHeadAndNameMsg value) {
      if (playerHeadAndNameMsgBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        playerHeadAndNameMsg_ = value;
      } else {
        playerHeadAndNameMsgBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadAndNameMsg playerHeadAndNameMsg = 1;</code>
     */
    public Builder setPlayerHeadAndNameMsg(
        xddq.pb.PlayerHeadAndNameMsg.Builder builderForValue) {
      if (playerHeadAndNameMsgBuilder_ == null) {
        playerHeadAndNameMsg_ = builderForValue.build();
      } else {
        playerHeadAndNameMsgBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadAndNameMsg playerHeadAndNameMsg = 1;</code>
     */
    public Builder mergePlayerHeadAndNameMsg(xddq.pb.PlayerHeadAndNameMsg value) {
      if (playerHeadAndNameMsgBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0) &&
          playerHeadAndNameMsg_ != null &&
          playerHeadAndNameMsg_ != xddq.pb.PlayerHeadAndNameMsg.getDefaultInstance()) {
          getPlayerHeadAndNameMsgBuilder().mergeFrom(value);
        } else {
          playerHeadAndNameMsg_ = value;
        }
      } else {
        playerHeadAndNameMsgBuilder_.mergeFrom(value);
      }
      if (playerHeadAndNameMsg_ != null) {
        bitField0_ |= 0x00000001;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadAndNameMsg playerHeadAndNameMsg = 1;</code>
     */
    public Builder clearPlayerHeadAndNameMsg() {
      bitField0_ = (bitField0_ & ~0x00000001);
      playerHeadAndNameMsg_ = null;
      if (playerHeadAndNameMsgBuilder_ != null) {
        playerHeadAndNameMsgBuilder_.dispose();
        playerHeadAndNameMsgBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadAndNameMsg playerHeadAndNameMsg = 1;</code>
     */
    public xddq.pb.PlayerHeadAndNameMsg.Builder getPlayerHeadAndNameMsgBuilder() {
      bitField0_ |= 0x00000001;
      onChanged();
      return internalGetPlayerHeadAndNameMsgFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadAndNameMsg playerHeadAndNameMsg = 1;</code>
     */
    public xddq.pb.PlayerHeadAndNameMsgOrBuilder getPlayerHeadAndNameMsgOrBuilder() {
      if (playerHeadAndNameMsgBuilder_ != null) {
        return playerHeadAndNameMsgBuilder_.getMessageOrBuilder();
      } else {
        return playerHeadAndNameMsg_ == null ?
            xddq.pb.PlayerHeadAndNameMsg.getDefaultInstance() : playerHeadAndNameMsg_;
      }
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadAndNameMsg playerHeadAndNameMsg = 1;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PlayerHeadAndNameMsg, xddq.pb.PlayerHeadAndNameMsg.Builder, xddq.pb.PlayerHeadAndNameMsgOrBuilder> 
        internalGetPlayerHeadAndNameMsgFieldBuilder() {
      if (playerHeadAndNameMsgBuilder_ == null) {
        playerHeadAndNameMsgBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.PlayerHeadAndNameMsg, xddq.pb.PlayerHeadAndNameMsg.Builder, xddq.pb.PlayerHeadAndNameMsgOrBuilder>(
                getPlayerHeadAndNameMsg(),
                getParentForChildren(),
                isClean());
        playerHeadAndNameMsg_ = null;
      }
      return playerHeadAndNameMsgBuilder_;
    }

    private java.lang.Object unionName_ = "";
    /**
     * <code>optional string unionName = 2;</code>
     * @return Whether the unionName field is set.
     */
    public boolean hasUnionName() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional string unionName = 2;</code>
     * @return The unionName.
     */
    public java.lang.String getUnionName() {
      java.lang.Object ref = unionName_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          unionName_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string unionName = 2;</code>
     * @return The bytes for unionName.
     */
    public com.google.protobuf.ByteString
        getUnionNameBytes() {
      java.lang.Object ref = unionName_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        unionName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string unionName = 2;</code>
     * @param value The unionName to set.
     * @return This builder for chaining.
     */
    public Builder setUnionName(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      unionName_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional string unionName = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearUnionName() {
      unionName_ = getDefaultInstance().getUnionName();
      bitField0_ = (bitField0_ & ~0x00000002);
      onChanged();
      return this;
    }
    /**
     * <code>optional string unionName = 2;</code>
     * @param value The bytes for unionName to set.
     * @return This builder for chaining.
     */
    public Builder setUnionNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      unionName_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }

    private int energy_ ;
    /**
     * <code>optional int32 energy = 3;</code>
     * @return Whether the energy field is set.
     */
    @java.lang.Override
    public boolean hasEnergy() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int32 energy = 3;</code>
     * @return The energy.
     */
    @java.lang.Override
    public int getEnergy() {
      return energy_;
    }
    /**
     * <code>optional int32 energy = 3;</code>
     * @param value The energy to set.
     * @return This builder for chaining.
     */
    public Builder setEnergy(int value) {

      energy_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 energy = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearEnergy() {
      bitField0_ = (bitField0_ & ~0x00000004);
      energy_ = 0;
      onChanged();
      return this;
    }

    private int npcId_ ;
    /**
     * <code>optional int32 npcId = 4;</code>
     * @return Whether the npcId field is set.
     */
    @java.lang.Override
    public boolean hasNpcId() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int32 npcId = 4;</code>
     * @return The npcId.
     */
    @java.lang.Override
    public int getNpcId() {
      return npcId_;
    }
    /**
     * <code>optional int32 npcId = 4;</code>
     * @param value The npcId to set.
     * @return This builder for chaining.
     */
    public Builder setNpcId(int value) {

      npcId_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 npcId = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearNpcId() {
      bitField0_ = (bitField0_ & ~0x00000008);
      npcId_ = 0;
      onChanged();
      return this;
    }

    private int costEnergy_ ;
    /**
     * <code>optional int32 costEnergy = 5;</code>
     * @return Whether the costEnergy field is set.
     */
    @java.lang.Override
    public boolean hasCostEnergy() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional int32 costEnergy = 5;</code>
     * @return The costEnergy.
     */
    @java.lang.Override
    public int getCostEnergy() {
      return costEnergy_;
    }
    /**
     * <code>optional int32 costEnergy = 5;</code>
     * @param value The costEnergy to set.
     * @return This builder for chaining.
     */
    public Builder setCostEnergy(int value) {

      costEnergy_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 costEnergy = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearCostEnergy() {
      bitField0_ = (bitField0_ & ~0x00000010);
      costEnergy_ = 0;
      onChanged();
      return this;
    }

    private int bodyIndex_ ;
    /**
     * <code>optional int32 bodyIndex = 6;</code>
     * @return Whether the bodyIndex field is set.
     */
    @java.lang.Override
    public boolean hasBodyIndex() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional int32 bodyIndex = 6;</code>
     * @return The bodyIndex.
     */
    @java.lang.Override
    public int getBodyIndex() {
      return bodyIndex_;
    }
    /**
     * <code>optional int32 bodyIndex = 6;</code>
     * @param value The bodyIndex to set.
     * @return This builder for chaining.
     */
    public Builder setBodyIndex(int value) {

      bodyIndex_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 bodyIndex = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearBodyIndex() {
      bitField0_ = (bitField0_ & ~0x00000020);
      bodyIndex_ = 0;
      onChanged();
      return this;
    }

    private com.google.protobuf.Internal.IntList buffList_ = emptyIntList();
    private void ensureBuffListIsMutable() {
      if (!buffList_.isModifiable()) {
        buffList_ = makeMutableCopy(buffList_);
      }
      bitField0_ |= 0x00000040;
    }
    /**
     * <code>repeated int32 buffList = 7;</code>
     * @return A list containing the buffList.
     */
    public java.util.List<java.lang.Integer>
        getBuffListList() {
      buffList_.makeImmutable();
      return buffList_;
    }
    /**
     * <code>repeated int32 buffList = 7;</code>
     * @return The count of buffList.
     */
    public int getBuffListCount() {
      return buffList_.size();
    }
    /**
     * <code>repeated int32 buffList = 7;</code>
     * @param index The index of the element to return.
     * @return The buffList at the given index.
     */
    public int getBuffList(int index) {
      return buffList_.getInt(index);
    }
    /**
     * <code>repeated int32 buffList = 7;</code>
     * @param index The index to set the value at.
     * @param value The buffList to set.
     * @return This builder for chaining.
     */
    public Builder setBuffList(
        int index, int value) {

      ensureBuffListIsMutable();
      buffList_.setInt(index, value);
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 buffList = 7;</code>
     * @param value The buffList to add.
     * @return This builder for chaining.
     */
    public Builder addBuffList(int value) {

      ensureBuffListIsMutable();
      buffList_.addInt(value);
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 buffList = 7;</code>
     * @param values The buffList to add.
     * @return This builder for chaining.
     */
    public Builder addAllBuffList(
        java.lang.Iterable<? extends java.lang.Integer> values) {
      ensureBuffListIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, buffList_);
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 buffList = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearBuffList() {
      buffList_ = emptyIntList();
      bitField0_ = (bitField0_ & ~0x00000040);
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.WarSeasonReportHeadMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.WarSeasonReportHeadMsg)
  private static final xddq.pb.WarSeasonReportHeadMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.WarSeasonReportHeadMsg();
  }

  public static xddq.pb.WarSeasonReportHeadMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<WarSeasonReportHeadMsg>
      PARSER = new com.google.protobuf.AbstractParser<WarSeasonReportHeadMsg>() {
    @java.lang.Override
    public WarSeasonReportHeadMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<WarSeasonReportHeadMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<WarSeasonReportHeadMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.WarSeasonReportHeadMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

