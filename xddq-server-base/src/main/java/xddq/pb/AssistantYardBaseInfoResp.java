// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.AssistantYardBaseInfoResp}
 */
public final class AssistantYardBaseInfoResp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.AssistantYardBaseInfoResp)
    AssistantYardBaseInfoRespOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      AssistantYardBaseInfoResp.class.getName());
  }
  // Use AssistantYardBaseInfoResp.newBuilder() to construct.
  private AssistantYardBaseInfoResp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private AssistantYardBaseInfoResp() {
    buildDetailMsg_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_AssistantYardBaseInfoResp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_AssistantYardBaseInfoResp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.AssistantYardBaseInfoResp.class, xddq.pb.AssistantYardBaseInfoResp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int BUILDDETAILMSG_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.AssistantYardBuildDetailMsg> buildDetailMsg_;
  /**
   * <code>repeated .xddq.pb.AssistantYardBuildDetailMsg buildDetailMsg = 2;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.AssistantYardBuildDetailMsg> getBuildDetailMsgList() {
    return buildDetailMsg_;
  }
  /**
   * <code>repeated .xddq.pb.AssistantYardBuildDetailMsg buildDetailMsg = 2;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.AssistantYardBuildDetailMsgOrBuilder> 
      getBuildDetailMsgOrBuilderList() {
    return buildDetailMsg_;
  }
  /**
   * <code>repeated .xddq.pb.AssistantYardBuildDetailMsg buildDetailMsg = 2;</code>
   */
  @java.lang.Override
  public int getBuildDetailMsgCount() {
    return buildDetailMsg_.size();
  }
  /**
   * <code>repeated .xddq.pb.AssistantYardBuildDetailMsg buildDetailMsg = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.AssistantYardBuildDetailMsg getBuildDetailMsg(int index) {
    return buildDetailMsg_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.AssistantYardBuildDetailMsg buildDetailMsg = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.AssistantYardBuildDetailMsgOrBuilder getBuildDetailMsgOrBuilder(
      int index) {
    return buildDetailMsg_.get(index);
  }

  public static final int CROPLEVEL_FIELD_NUMBER = 3;
  private int cropLevel_ = 0;
  /**
   * <code>optional int32 cropLevel = 3;</code>
   * @return Whether the cropLevel field is set.
   */
  @java.lang.Override
  public boolean hasCropLevel() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int32 cropLevel = 3;</code>
   * @return The cropLevel.
   */
  @java.lang.Override
  public int getCropLevel() {
    return cropLevel_;
  }

  public static final int ALCHEMYLEVEL_FIELD_NUMBER = 4;
  private int alchemyLevel_ = 0;
  /**
   * <code>optional int32 alchemyLevel = 4;</code>
   * @return Whether the alchemyLevel field is set.
   */
  @java.lang.Override
  public boolean hasAlchemyLevel() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional int32 alchemyLevel = 4;</code>
   * @return The alchemyLevel.
   */
  @java.lang.Override
  public int getAlchemyLevel() {
    return alchemyLevel_;
  }

  public static final int YARDLEVEL_FIELD_NUMBER = 5;
  private int yardLevel_ = 0;
  /**
   * <code>optional int32 yardLevel = 5;</code>
   * @return Whether the yardLevel field is set.
   */
  @java.lang.Override
  public boolean hasYardLevel() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional int32 yardLevel = 5;</code>
   * @return The yardLevel.
   */
  @java.lang.Override
  public int getYardLevel() {
    return yardLevel_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    for (int i = 0; i < buildDetailMsg_.size(); i++) {
      output.writeMessage(2, buildDetailMsg_.get(i));
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(3, cropLevel_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt32(4, alchemyLevel_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt32(5, yardLevel_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    for (int i = 0; i < buildDetailMsg_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, buildDetailMsg_.get(i));
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(3, cropLevel_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(4, alchemyLevel_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(5, yardLevel_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.AssistantYardBaseInfoResp)) {
      return super.equals(obj);
    }
    xddq.pb.AssistantYardBaseInfoResp other = (xddq.pb.AssistantYardBaseInfoResp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (!getBuildDetailMsgList()
        .equals(other.getBuildDetailMsgList())) return false;
    if (hasCropLevel() != other.hasCropLevel()) return false;
    if (hasCropLevel()) {
      if (getCropLevel()
          != other.getCropLevel()) return false;
    }
    if (hasAlchemyLevel() != other.hasAlchemyLevel()) return false;
    if (hasAlchemyLevel()) {
      if (getAlchemyLevel()
          != other.getAlchemyLevel()) return false;
    }
    if (hasYardLevel() != other.hasYardLevel()) return false;
    if (hasYardLevel()) {
      if (getYardLevel()
          != other.getYardLevel()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (getBuildDetailMsgCount() > 0) {
      hash = (37 * hash) + BUILDDETAILMSG_FIELD_NUMBER;
      hash = (53 * hash) + getBuildDetailMsgList().hashCode();
    }
    if (hasCropLevel()) {
      hash = (37 * hash) + CROPLEVEL_FIELD_NUMBER;
      hash = (53 * hash) + getCropLevel();
    }
    if (hasAlchemyLevel()) {
      hash = (37 * hash) + ALCHEMYLEVEL_FIELD_NUMBER;
      hash = (53 * hash) + getAlchemyLevel();
    }
    if (hasYardLevel()) {
      hash = (37 * hash) + YARDLEVEL_FIELD_NUMBER;
      hash = (53 * hash) + getYardLevel();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.AssistantYardBaseInfoResp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.AssistantYardBaseInfoResp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.AssistantYardBaseInfoResp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.AssistantYardBaseInfoResp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.AssistantYardBaseInfoResp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.AssistantYardBaseInfoResp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.AssistantYardBaseInfoResp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.AssistantYardBaseInfoResp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.AssistantYardBaseInfoResp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.AssistantYardBaseInfoResp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.AssistantYardBaseInfoResp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.AssistantYardBaseInfoResp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.AssistantYardBaseInfoResp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.AssistantYardBaseInfoResp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.AssistantYardBaseInfoResp)
      xddq.pb.AssistantYardBaseInfoRespOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_AssistantYardBaseInfoResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_AssistantYardBaseInfoResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.AssistantYardBaseInfoResp.class, xddq.pb.AssistantYardBaseInfoResp.Builder.class);
    }

    // Construct using xddq.pb.AssistantYardBaseInfoResp.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      if (buildDetailMsgBuilder_ == null) {
        buildDetailMsg_ = java.util.Collections.emptyList();
      } else {
        buildDetailMsg_ = null;
        buildDetailMsgBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000002);
      cropLevel_ = 0;
      alchemyLevel_ = 0;
      yardLevel_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_AssistantYardBaseInfoResp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.AssistantYardBaseInfoResp getDefaultInstanceForType() {
      return xddq.pb.AssistantYardBaseInfoResp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.AssistantYardBaseInfoResp build() {
      xddq.pb.AssistantYardBaseInfoResp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.AssistantYardBaseInfoResp buildPartial() {
      xddq.pb.AssistantYardBaseInfoResp result = new xddq.pb.AssistantYardBaseInfoResp(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.AssistantYardBaseInfoResp result) {
      if (buildDetailMsgBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          buildDetailMsg_ = java.util.Collections.unmodifiableList(buildDetailMsg_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.buildDetailMsg_ = buildDetailMsg_;
      } else {
        result.buildDetailMsg_ = buildDetailMsgBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.AssistantYardBaseInfoResp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.cropLevel_ = cropLevel_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.alchemyLevel_ = alchemyLevel_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.yardLevel_ = yardLevel_;
        to_bitField0_ |= 0x00000008;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.AssistantYardBaseInfoResp) {
        return mergeFrom((xddq.pb.AssistantYardBaseInfoResp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.AssistantYardBaseInfoResp other) {
      if (other == xddq.pb.AssistantYardBaseInfoResp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (buildDetailMsgBuilder_ == null) {
        if (!other.buildDetailMsg_.isEmpty()) {
          if (buildDetailMsg_.isEmpty()) {
            buildDetailMsg_ = other.buildDetailMsg_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureBuildDetailMsgIsMutable();
            buildDetailMsg_.addAll(other.buildDetailMsg_);
          }
          onChanged();
        }
      } else {
        if (!other.buildDetailMsg_.isEmpty()) {
          if (buildDetailMsgBuilder_.isEmpty()) {
            buildDetailMsgBuilder_.dispose();
            buildDetailMsgBuilder_ = null;
            buildDetailMsg_ = other.buildDetailMsg_;
            bitField0_ = (bitField0_ & ~0x00000002);
            buildDetailMsgBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetBuildDetailMsgFieldBuilder() : null;
          } else {
            buildDetailMsgBuilder_.addAllMessages(other.buildDetailMsg_);
          }
        }
      }
      if (other.hasCropLevel()) {
        setCropLevel(other.getCropLevel());
      }
      if (other.hasAlchemyLevel()) {
        setAlchemyLevel(other.getAlchemyLevel());
      }
      if (other.hasYardLevel()) {
        setYardLevel(other.getYardLevel());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              xddq.pb.AssistantYardBuildDetailMsg m =
                  input.readMessage(
                      xddq.pb.AssistantYardBuildDetailMsg.parser(),
                      extensionRegistry);
              if (buildDetailMsgBuilder_ == null) {
                ensureBuildDetailMsgIsMutable();
                buildDetailMsg_.add(m);
              } else {
                buildDetailMsgBuilder_.addMessage(m);
              }
              break;
            } // case 18
            case 24: {
              cropLevel_ = input.readInt32();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 32: {
              alchemyLevel_ = input.readInt32();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            case 40: {
              yardLevel_ = input.readInt32();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.AssistantYardBuildDetailMsg> buildDetailMsg_ =
      java.util.Collections.emptyList();
    private void ensureBuildDetailMsgIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        buildDetailMsg_ = new java.util.ArrayList<xddq.pb.AssistantYardBuildDetailMsg>(buildDetailMsg_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.AssistantYardBuildDetailMsg, xddq.pb.AssistantYardBuildDetailMsg.Builder, xddq.pb.AssistantYardBuildDetailMsgOrBuilder> buildDetailMsgBuilder_;

    /**
     * <code>repeated .xddq.pb.AssistantYardBuildDetailMsg buildDetailMsg = 2;</code>
     */
    public java.util.List<xddq.pb.AssistantYardBuildDetailMsg> getBuildDetailMsgList() {
      if (buildDetailMsgBuilder_ == null) {
        return java.util.Collections.unmodifiableList(buildDetailMsg_);
      } else {
        return buildDetailMsgBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.AssistantYardBuildDetailMsg buildDetailMsg = 2;</code>
     */
    public int getBuildDetailMsgCount() {
      if (buildDetailMsgBuilder_ == null) {
        return buildDetailMsg_.size();
      } else {
        return buildDetailMsgBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.AssistantYardBuildDetailMsg buildDetailMsg = 2;</code>
     */
    public xddq.pb.AssistantYardBuildDetailMsg getBuildDetailMsg(int index) {
      if (buildDetailMsgBuilder_ == null) {
        return buildDetailMsg_.get(index);
      } else {
        return buildDetailMsgBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.AssistantYardBuildDetailMsg buildDetailMsg = 2;</code>
     */
    public Builder setBuildDetailMsg(
        int index, xddq.pb.AssistantYardBuildDetailMsg value) {
      if (buildDetailMsgBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureBuildDetailMsgIsMutable();
        buildDetailMsg_.set(index, value);
        onChanged();
      } else {
        buildDetailMsgBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.AssistantYardBuildDetailMsg buildDetailMsg = 2;</code>
     */
    public Builder setBuildDetailMsg(
        int index, xddq.pb.AssistantYardBuildDetailMsg.Builder builderForValue) {
      if (buildDetailMsgBuilder_ == null) {
        ensureBuildDetailMsgIsMutable();
        buildDetailMsg_.set(index, builderForValue.build());
        onChanged();
      } else {
        buildDetailMsgBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.AssistantYardBuildDetailMsg buildDetailMsg = 2;</code>
     */
    public Builder addBuildDetailMsg(xddq.pb.AssistantYardBuildDetailMsg value) {
      if (buildDetailMsgBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureBuildDetailMsgIsMutable();
        buildDetailMsg_.add(value);
        onChanged();
      } else {
        buildDetailMsgBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.AssistantYardBuildDetailMsg buildDetailMsg = 2;</code>
     */
    public Builder addBuildDetailMsg(
        int index, xddq.pb.AssistantYardBuildDetailMsg value) {
      if (buildDetailMsgBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureBuildDetailMsgIsMutable();
        buildDetailMsg_.add(index, value);
        onChanged();
      } else {
        buildDetailMsgBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.AssistantYardBuildDetailMsg buildDetailMsg = 2;</code>
     */
    public Builder addBuildDetailMsg(
        xddq.pb.AssistantYardBuildDetailMsg.Builder builderForValue) {
      if (buildDetailMsgBuilder_ == null) {
        ensureBuildDetailMsgIsMutable();
        buildDetailMsg_.add(builderForValue.build());
        onChanged();
      } else {
        buildDetailMsgBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.AssistantYardBuildDetailMsg buildDetailMsg = 2;</code>
     */
    public Builder addBuildDetailMsg(
        int index, xddq.pb.AssistantYardBuildDetailMsg.Builder builderForValue) {
      if (buildDetailMsgBuilder_ == null) {
        ensureBuildDetailMsgIsMutable();
        buildDetailMsg_.add(index, builderForValue.build());
        onChanged();
      } else {
        buildDetailMsgBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.AssistantYardBuildDetailMsg buildDetailMsg = 2;</code>
     */
    public Builder addAllBuildDetailMsg(
        java.lang.Iterable<? extends xddq.pb.AssistantYardBuildDetailMsg> values) {
      if (buildDetailMsgBuilder_ == null) {
        ensureBuildDetailMsgIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, buildDetailMsg_);
        onChanged();
      } else {
        buildDetailMsgBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.AssistantYardBuildDetailMsg buildDetailMsg = 2;</code>
     */
    public Builder clearBuildDetailMsg() {
      if (buildDetailMsgBuilder_ == null) {
        buildDetailMsg_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        buildDetailMsgBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.AssistantYardBuildDetailMsg buildDetailMsg = 2;</code>
     */
    public Builder removeBuildDetailMsg(int index) {
      if (buildDetailMsgBuilder_ == null) {
        ensureBuildDetailMsgIsMutable();
        buildDetailMsg_.remove(index);
        onChanged();
      } else {
        buildDetailMsgBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.AssistantYardBuildDetailMsg buildDetailMsg = 2;</code>
     */
    public xddq.pb.AssistantYardBuildDetailMsg.Builder getBuildDetailMsgBuilder(
        int index) {
      return internalGetBuildDetailMsgFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.AssistantYardBuildDetailMsg buildDetailMsg = 2;</code>
     */
    public xddq.pb.AssistantYardBuildDetailMsgOrBuilder getBuildDetailMsgOrBuilder(
        int index) {
      if (buildDetailMsgBuilder_ == null) {
        return buildDetailMsg_.get(index);  } else {
        return buildDetailMsgBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.AssistantYardBuildDetailMsg buildDetailMsg = 2;</code>
     */
    public java.util.List<? extends xddq.pb.AssistantYardBuildDetailMsgOrBuilder> 
         getBuildDetailMsgOrBuilderList() {
      if (buildDetailMsgBuilder_ != null) {
        return buildDetailMsgBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(buildDetailMsg_);
      }
    }
    /**
     * <code>repeated .xddq.pb.AssistantYardBuildDetailMsg buildDetailMsg = 2;</code>
     */
    public xddq.pb.AssistantYardBuildDetailMsg.Builder addBuildDetailMsgBuilder() {
      return internalGetBuildDetailMsgFieldBuilder().addBuilder(
          xddq.pb.AssistantYardBuildDetailMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.AssistantYardBuildDetailMsg buildDetailMsg = 2;</code>
     */
    public xddq.pb.AssistantYardBuildDetailMsg.Builder addBuildDetailMsgBuilder(
        int index) {
      return internalGetBuildDetailMsgFieldBuilder().addBuilder(
          index, xddq.pb.AssistantYardBuildDetailMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.AssistantYardBuildDetailMsg buildDetailMsg = 2;</code>
     */
    public java.util.List<xddq.pb.AssistantYardBuildDetailMsg.Builder> 
         getBuildDetailMsgBuilderList() {
      return internalGetBuildDetailMsgFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.AssistantYardBuildDetailMsg, xddq.pb.AssistantYardBuildDetailMsg.Builder, xddq.pb.AssistantYardBuildDetailMsgOrBuilder> 
        internalGetBuildDetailMsgFieldBuilder() {
      if (buildDetailMsgBuilder_ == null) {
        buildDetailMsgBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.AssistantYardBuildDetailMsg, xddq.pb.AssistantYardBuildDetailMsg.Builder, xddq.pb.AssistantYardBuildDetailMsgOrBuilder>(
                buildDetailMsg_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        buildDetailMsg_ = null;
      }
      return buildDetailMsgBuilder_;
    }

    private int cropLevel_ ;
    /**
     * <code>optional int32 cropLevel = 3;</code>
     * @return Whether the cropLevel field is set.
     */
    @java.lang.Override
    public boolean hasCropLevel() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int32 cropLevel = 3;</code>
     * @return The cropLevel.
     */
    @java.lang.Override
    public int getCropLevel() {
      return cropLevel_;
    }
    /**
     * <code>optional int32 cropLevel = 3;</code>
     * @param value The cropLevel to set.
     * @return This builder for chaining.
     */
    public Builder setCropLevel(int value) {

      cropLevel_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 cropLevel = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearCropLevel() {
      bitField0_ = (bitField0_ & ~0x00000004);
      cropLevel_ = 0;
      onChanged();
      return this;
    }

    private int alchemyLevel_ ;
    /**
     * <code>optional int32 alchemyLevel = 4;</code>
     * @return Whether the alchemyLevel field is set.
     */
    @java.lang.Override
    public boolean hasAlchemyLevel() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int32 alchemyLevel = 4;</code>
     * @return The alchemyLevel.
     */
    @java.lang.Override
    public int getAlchemyLevel() {
      return alchemyLevel_;
    }
    /**
     * <code>optional int32 alchemyLevel = 4;</code>
     * @param value The alchemyLevel to set.
     * @return This builder for chaining.
     */
    public Builder setAlchemyLevel(int value) {

      alchemyLevel_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 alchemyLevel = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearAlchemyLevel() {
      bitField0_ = (bitField0_ & ~0x00000008);
      alchemyLevel_ = 0;
      onChanged();
      return this;
    }

    private int yardLevel_ ;
    /**
     * <code>optional int32 yardLevel = 5;</code>
     * @return Whether the yardLevel field is set.
     */
    @java.lang.Override
    public boolean hasYardLevel() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional int32 yardLevel = 5;</code>
     * @return The yardLevel.
     */
    @java.lang.Override
    public int getYardLevel() {
      return yardLevel_;
    }
    /**
     * <code>optional int32 yardLevel = 5;</code>
     * @param value The yardLevel to set.
     * @return This builder for chaining.
     */
    public Builder setYardLevel(int value) {

      yardLevel_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 yardLevel = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearYardLevel() {
      bitField0_ = (bitField0_ & ~0x00000010);
      yardLevel_ = 0;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.AssistantYardBaseInfoResp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.AssistantYardBaseInfoResp)
  private static final xddq.pb.AssistantYardBaseInfoResp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.AssistantYardBaseInfoResp();
  }

  public static xddq.pb.AssistantYardBaseInfoResp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<AssistantYardBaseInfoResp>
      PARSER = new com.google.protobuf.AbstractParser<AssistantYardBaseInfoResp>() {
    @java.lang.Override
    public AssistantYardBaseInfoResp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<AssistantYardBaseInfoResp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<AssistantYardBaseInfoResp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.AssistantYardBaseInfoResp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

