// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

public interface BattleRoundLogOrBuilder extends
    // @@protoc_insertion_point(interface_extends:xddq.pb.BattleRoundLog)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>required int32 round = 1;</code>
   * @return Whether the round field is set.
   */
  boolean hasRound();
  /**
   * <code>required int32 round = 1;</code>
   * @return The round.
   */
  int getRound();

  /**
   * <code>repeated .xddq.pb.BattleAction action = 2;</code>
   */
  java.util.List<xddq.pb.BattleAction> 
      getActionList();
  /**
   * <code>repeated .xddq.pb.BattleAction action = 2;</code>
   */
  xddq.pb.BattleAction getAction(int index);
  /**
   * <code>repeated .xddq.pb.BattleAction action = 2;</code>
   */
  int getActionCount();
  /**
   * <code>repeated .xddq.pb.BattleAction action = 2;</code>
   */
  java.util.List<? extends xddq.pb.BattleActionOrBuilder> 
      getActionOrBuilderList();
  /**
   * <code>repeated .xddq.pb.BattleAction action = 2;</code>
   */
  xddq.pb.BattleActionOrBuilder getActionOrBuilder(
      int index);

  /**
   * <code>repeated .xddq.pb.BattleMainState battleMainState = 3;</code>
   */
  java.util.List<xddq.pb.BattleMainState> 
      getBattleMainStateList();
  /**
   * <code>repeated .xddq.pb.BattleMainState battleMainState = 3;</code>
   */
  xddq.pb.BattleMainState getBattleMainState(int index);
  /**
   * <code>repeated .xddq.pb.BattleMainState battleMainState = 3;</code>
   */
  int getBattleMainStateCount();
  /**
   * <code>repeated .xddq.pb.BattleMainState battleMainState = 3;</code>
   */
  java.util.List<? extends xddq.pb.BattleMainStateOrBuilder> 
      getBattleMainStateOrBuilderList();
  /**
   * <code>repeated .xddq.pb.BattleMainState battleMainState = 3;</code>
   */
  xddq.pb.BattleMainStateOrBuilder getBattleMainStateOrBuilder(
      int index);

  /**
   * <code>repeated .xddq.pb.UniverseValue universeValue = 4;</code>
   */
  java.util.List<xddq.pb.UniverseValue> 
      getUniverseValueList();
  /**
   * <code>repeated .xddq.pb.UniverseValue universeValue = 4;</code>
   */
  xddq.pb.UniverseValue getUniverseValue(int index);
  /**
   * <code>repeated .xddq.pb.UniverseValue universeValue = 4;</code>
   */
  int getUniverseValueCount();
  /**
   * <code>repeated .xddq.pb.UniverseValue universeValue = 4;</code>
   */
  java.util.List<? extends xddq.pb.UniverseValueOrBuilder> 
      getUniverseValueOrBuilderList();
  /**
   * <code>repeated .xddq.pb.UniverseValue universeValue = 4;</code>
   */
  xddq.pb.UniverseValueOrBuilder getUniverseValueOrBuilder(
      int index);
}
