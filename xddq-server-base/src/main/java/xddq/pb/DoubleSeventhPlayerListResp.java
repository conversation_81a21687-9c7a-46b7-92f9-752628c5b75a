// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.DoubleSeventhPlayerListResp}
 */
public final class DoubleSeventhPlayerListResp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.DoubleSeventhPlayerListResp)
    DoubleSeventhPlayerListRespOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      DoubleSeventhPlayerListResp.class.getName());
  }
  // Use DoubleSeventhPlayerListResp.newBuilder() to construct.
  private DoubleSeventhPlayerListResp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private DoubleSeventhPlayerListResp() {
    playerData_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_DoubleSeventhPlayerListResp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_DoubleSeventhPlayerListResp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.DoubleSeventhPlayerListResp.class, xddq.pb.DoubleSeventhPlayerListResp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int PLAYERDATA_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.DoubleSeventhPlayerData> playerData_;
  /**
   * <code>repeated .xddq.pb.DoubleSeventhPlayerData playerData = 2;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.DoubleSeventhPlayerData> getPlayerDataList() {
    return playerData_;
  }
  /**
   * <code>repeated .xddq.pb.DoubleSeventhPlayerData playerData = 2;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.DoubleSeventhPlayerDataOrBuilder> 
      getPlayerDataOrBuilderList() {
    return playerData_;
  }
  /**
   * <code>repeated .xddq.pb.DoubleSeventhPlayerData playerData = 2;</code>
   */
  @java.lang.Override
  public int getPlayerDataCount() {
    return playerData_.size();
  }
  /**
   * <code>repeated .xddq.pb.DoubleSeventhPlayerData playerData = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.DoubleSeventhPlayerData getPlayerData(int index) {
    return playerData_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.DoubleSeventhPlayerData playerData = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.DoubleSeventhPlayerDataOrBuilder getPlayerDataOrBuilder(
      int index) {
    return playerData_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    for (int i = 0; i < playerData_.size(); i++) {
      output.writeMessage(2, playerData_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    for (int i = 0; i < playerData_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, playerData_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.DoubleSeventhPlayerListResp)) {
      return super.equals(obj);
    }
    xddq.pb.DoubleSeventhPlayerListResp other = (xddq.pb.DoubleSeventhPlayerListResp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (!getPlayerDataList()
        .equals(other.getPlayerDataList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (getPlayerDataCount() > 0) {
      hash = (37 * hash) + PLAYERDATA_FIELD_NUMBER;
      hash = (53 * hash) + getPlayerDataList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.DoubleSeventhPlayerListResp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.DoubleSeventhPlayerListResp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.DoubleSeventhPlayerListResp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.DoubleSeventhPlayerListResp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.DoubleSeventhPlayerListResp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.DoubleSeventhPlayerListResp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.DoubleSeventhPlayerListResp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.DoubleSeventhPlayerListResp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.DoubleSeventhPlayerListResp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.DoubleSeventhPlayerListResp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.DoubleSeventhPlayerListResp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.DoubleSeventhPlayerListResp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.DoubleSeventhPlayerListResp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.DoubleSeventhPlayerListResp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.DoubleSeventhPlayerListResp)
      xddq.pb.DoubleSeventhPlayerListRespOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_DoubleSeventhPlayerListResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_DoubleSeventhPlayerListResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.DoubleSeventhPlayerListResp.class, xddq.pb.DoubleSeventhPlayerListResp.Builder.class);
    }

    // Construct using xddq.pb.DoubleSeventhPlayerListResp.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      if (playerDataBuilder_ == null) {
        playerData_ = java.util.Collections.emptyList();
      } else {
        playerData_ = null;
        playerDataBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000002);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_DoubleSeventhPlayerListResp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.DoubleSeventhPlayerListResp getDefaultInstanceForType() {
      return xddq.pb.DoubleSeventhPlayerListResp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.DoubleSeventhPlayerListResp build() {
      xddq.pb.DoubleSeventhPlayerListResp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.DoubleSeventhPlayerListResp buildPartial() {
      xddq.pb.DoubleSeventhPlayerListResp result = new xddq.pb.DoubleSeventhPlayerListResp(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.DoubleSeventhPlayerListResp result) {
      if (playerDataBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          playerData_ = java.util.Collections.unmodifiableList(playerData_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.playerData_ = playerData_;
      } else {
        result.playerData_ = playerDataBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.DoubleSeventhPlayerListResp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.DoubleSeventhPlayerListResp) {
        return mergeFrom((xddq.pb.DoubleSeventhPlayerListResp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.DoubleSeventhPlayerListResp other) {
      if (other == xddq.pb.DoubleSeventhPlayerListResp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (playerDataBuilder_ == null) {
        if (!other.playerData_.isEmpty()) {
          if (playerData_.isEmpty()) {
            playerData_ = other.playerData_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensurePlayerDataIsMutable();
            playerData_.addAll(other.playerData_);
          }
          onChanged();
        }
      } else {
        if (!other.playerData_.isEmpty()) {
          if (playerDataBuilder_.isEmpty()) {
            playerDataBuilder_.dispose();
            playerDataBuilder_ = null;
            playerData_ = other.playerData_;
            bitField0_ = (bitField0_ & ~0x00000002);
            playerDataBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetPlayerDataFieldBuilder() : null;
          } else {
            playerDataBuilder_.addAllMessages(other.playerData_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              xddq.pb.DoubleSeventhPlayerData m =
                  input.readMessage(
                      xddq.pb.DoubleSeventhPlayerData.parser(),
                      extensionRegistry);
              if (playerDataBuilder_ == null) {
                ensurePlayerDataIsMutable();
                playerData_.add(m);
              } else {
                playerDataBuilder_.addMessage(m);
              }
              break;
            } // case 18
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.DoubleSeventhPlayerData> playerData_ =
      java.util.Collections.emptyList();
    private void ensurePlayerDataIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        playerData_ = new java.util.ArrayList<xddq.pb.DoubleSeventhPlayerData>(playerData_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.DoubleSeventhPlayerData, xddq.pb.DoubleSeventhPlayerData.Builder, xddq.pb.DoubleSeventhPlayerDataOrBuilder> playerDataBuilder_;

    /**
     * <code>repeated .xddq.pb.DoubleSeventhPlayerData playerData = 2;</code>
     */
    public java.util.List<xddq.pb.DoubleSeventhPlayerData> getPlayerDataList() {
      if (playerDataBuilder_ == null) {
        return java.util.Collections.unmodifiableList(playerData_);
      } else {
        return playerDataBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.DoubleSeventhPlayerData playerData = 2;</code>
     */
    public int getPlayerDataCount() {
      if (playerDataBuilder_ == null) {
        return playerData_.size();
      } else {
        return playerDataBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.DoubleSeventhPlayerData playerData = 2;</code>
     */
    public xddq.pb.DoubleSeventhPlayerData getPlayerData(int index) {
      if (playerDataBuilder_ == null) {
        return playerData_.get(index);
      } else {
        return playerDataBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.DoubleSeventhPlayerData playerData = 2;</code>
     */
    public Builder setPlayerData(
        int index, xddq.pb.DoubleSeventhPlayerData value) {
      if (playerDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePlayerDataIsMutable();
        playerData_.set(index, value);
        onChanged();
      } else {
        playerDataBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DoubleSeventhPlayerData playerData = 2;</code>
     */
    public Builder setPlayerData(
        int index, xddq.pb.DoubleSeventhPlayerData.Builder builderForValue) {
      if (playerDataBuilder_ == null) {
        ensurePlayerDataIsMutable();
        playerData_.set(index, builderForValue.build());
        onChanged();
      } else {
        playerDataBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DoubleSeventhPlayerData playerData = 2;</code>
     */
    public Builder addPlayerData(xddq.pb.DoubleSeventhPlayerData value) {
      if (playerDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePlayerDataIsMutable();
        playerData_.add(value);
        onChanged();
      } else {
        playerDataBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DoubleSeventhPlayerData playerData = 2;</code>
     */
    public Builder addPlayerData(
        int index, xddq.pb.DoubleSeventhPlayerData value) {
      if (playerDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePlayerDataIsMutable();
        playerData_.add(index, value);
        onChanged();
      } else {
        playerDataBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DoubleSeventhPlayerData playerData = 2;</code>
     */
    public Builder addPlayerData(
        xddq.pb.DoubleSeventhPlayerData.Builder builderForValue) {
      if (playerDataBuilder_ == null) {
        ensurePlayerDataIsMutable();
        playerData_.add(builderForValue.build());
        onChanged();
      } else {
        playerDataBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DoubleSeventhPlayerData playerData = 2;</code>
     */
    public Builder addPlayerData(
        int index, xddq.pb.DoubleSeventhPlayerData.Builder builderForValue) {
      if (playerDataBuilder_ == null) {
        ensurePlayerDataIsMutable();
        playerData_.add(index, builderForValue.build());
        onChanged();
      } else {
        playerDataBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DoubleSeventhPlayerData playerData = 2;</code>
     */
    public Builder addAllPlayerData(
        java.lang.Iterable<? extends xddq.pb.DoubleSeventhPlayerData> values) {
      if (playerDataBuilder_ == null) {
        ensurePlayerDataIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, playerData_);
        onChanged();
      } else {
        playerDataBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DoubleSeventhPlayerData playerData = 2;</code>
     */
    public Builder clearPlayerData() {
      if (playerDataBuilder_ == null) {
        playerData_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        playerDataBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DoubleSeventhPlayerData playerData = 2;</code>
     */
    public Builder removePlayerData(int index) {
      if (playerDataBuilder_ == null) {
        ensurePlayerDataIsMutable();
        playerData_.remove(index);
        onChanged();
      } else {
        playerDataBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DoubleSeventhPlayerData playerData = 2;</code>
     */
    public xddq.pb.DoubleSeventhPlayerData.Builder getPlayerDataBuilder(
        int index) {
      return internalGetPlayerDataFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.DoubleSeventhPlayerData playerData = 2;</code>
     */
    public xddq.pb.DoubleSeventhPlayerDataOrBuilder getPlayerDataOrBuilder(
        int index) {
      if (playerDataBuilder_ == null) {
        return playerData_.get(index);  } else {
        return playerDataBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.DoubleSeventhPlayerData playerData = 2;</code>
     */
    public java.util.List<? extends xddq.pb.DoubleSeventhPlayerDataOrBuilder> 
         getPlayerDataOrBuilderList() {
      if (playerDataBuilder_ != null) {
        return playerDataBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(playerData_);
      }
    }
    /**
     * <code>repeated .xddq.pb.DoubleSeventhPlayerData playerData = 2;</code>
     */
    public xddq.pb.DoubleSeventhPlayerData.Builder addPlayerDataBuilder() {
      return internalGetPlayerDataFieldBuilder().addBuilder(
          xddq.pb.DoubleSeventhPlayerData.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.DoubleSeventhPlayerData playerData = 2;</code>
     */
    public xddq.pb.DoubleSeventhPlayerData.Builder addPlayerDataBuilder(
        int index) {
      return internalGetPlayerDataFieldBuilder().addBuilder(
          index, xddq.pb.DoubleSeventhPlayerData.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.DoubleSeventhPlayerData playerData = 2;</code>
     */
    public java.util.List<xddq.pb.DoubleSeventhPlayerData.Builder> 
         getPlayerDataBuilderList() {
      return internalGetPlayerDataFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.DoubleSeventhPlayerData, xddq.pb.DoubleSeventhPlayerData.Builder, xddq.pb.DoubleSeventhPlayerDataOrBuilder> 
        internalGetPlayerDataFieldBuilder() {
      if (playerDataBuilder_ == null) {
        playerDataBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.DoubleSeventhPlayerData, xddq.pb.DoubleSeventhPlayerData.Builder, xddq.pb.DoubleSeventhPlayerDataOrBuilder>(
                playerData_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        playerData_ = null;
      }
      return playerDataBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.DoubleSeventhPlayerListResp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.DoubleSeventhPlayerListResp)
  private static final xddq.pb.DoubleSeventhPlayerListResp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.DoubleSeventhPlayerListResp();
  }

  public static xddq.pb.DoubleSeventhPlayerListResp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<DoubleSeventhPlayerListResp>
      PARSER = new com.google.protobuf.AbstractParser<DoubleSeventhPlayerListResp>() {
    @java.lang.Override
    public DoubleSeventhPlayerListResp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<DoubleSeventhPlayerListResp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<DoubleSeventhPlayerListResp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.DoubleSeventhPlayerListResp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

