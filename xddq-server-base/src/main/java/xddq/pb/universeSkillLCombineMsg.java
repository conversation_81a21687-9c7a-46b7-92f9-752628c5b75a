// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.universeSkillLCombineMsg}
 */
public final class universeSkillLCombineMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.universeSkillLCombineMsg)
    universeSkillLCombineMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      universeSkillLCombineMsg.class.getName());
  }
  // Use universeSkillLCombineMsg.newBuilder() to construct.
  private universeSkillLCombineMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private universeSkillLCombineMsg() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_universeSkillLCombineMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_universeSkillLCombineMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.universeSkillLCombineMsg.class, xddq.pb.universeSkillLCombineMsg.Builder.class);
  }

  private int bitField0_;
  public static final int COMBINEID_FIELD_NUMBER = 1;
  private int combineId_ = 0;
  /**
   * <code>required int32 combineId = 1;</code>
   * @return Whether the combineId field is set.
   */
  @java.lang.Override
  public boolean hasCombineId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 combineId = 1;</code>
   * @return The combineId.
   */
  @java.lang.Override
  public int getCombineId() {
    return combineId_;
  }

  public static final int LV_FIELD_NUMBER = 2;
  private int lv_ = 0;
  /**
   * <code>required int32 lv = 2;</code>
   * @return Whether the lv field is set.
   */
  @java.lang.Override
  public boolean hasLv() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>required int32 lv = 2;</code>
   * @return The lv.
   */
  @java.lang.Override
  public int getLv() {
    return lv_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasCombineId()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasLv()) {
      memoizedIsInitialized = 0;
      return false;
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, combineId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(2, lv_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, combineId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, lv_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.universeSkillLCombineMsg)) {
      return super.equals(obj);
    }
    xddq.pb.universeSkillLCombineMsg other = (xddq.pb.universeSkillLCombineMsg) obj;

    if (hasCombineId() != other.hasCombineId()) return false;
    if (hasCombineId()) {
      if (getCombineId()
          != other.getCombineId()) return false;
    }
    if (hasLv() != other.hasLv()) return false;
    if (hasLv()) {
      if (getLv()
          != other.getLv()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasCombineId()) {
      hash = (37 * hash) + COMBINEID_FIELD_NUMBER;
      hash = (53 * hash) + getCombineId();
    }
    if (hasLv()) {
      hash = (37 * hash) + LV_FIELD_NUMBER;
      hash = (53 * hash) + getLv();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.universeSkillLCombineMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.universeSkillLCombineMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.universeSkillLCombineMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.universeSkillLCombineMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.universeSkillLCombineMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.universeSkillLCombineMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.universeSkillLCombineMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.universeSkillLCombineMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.universeSkillLCombineMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.universeSkillLCombineMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.universeSkillLCombineMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.universeSkillLCombineMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.universeSkillLCombineMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.universeSkillLCombineMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.universeSkillLCombineMsg)
      xddq.pb.universeSkillLCombineMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_universeSkillLCombineMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_universeSkillLCombineMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.universeSkillLCombineMsg.class, xddq.pb.universeSkillLCombineMsg.Builder.class);
    }

    // Construct using xddq.pb.universeSkillLCombineMsg.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      combineId_ = 0;
      lv_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_universeSkillLCombineMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.universeSkillLCombineMsg getDefaultInstanceForType() {
      return xddq.pb.universeSkillLCombineMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.universeSkillLCombineMsg build() {
      xddq.pb.universeSkillLCombineMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.universeSkillLCombineMsg buildPartial() {
      xddq.pb.universeSkillLCombineMsg result = new xddq.pb.universeSkillLCombineMsg(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.universeSkillLCombineMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.combineId_ = combineId_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.lv_ = lv_;
        to_bitField0_ |= 0x00000002;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.universeSkillLCombineMsg) {
        return mergeFrom((xddq.pb.universeSkillLCombineMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.universeSkillLCombineMsg other) {
      if (other == xddq.pb.universeSkillLCombineMsg.getDefaultInstance()) return this;
      if (other.hasCombineId()) {
        setCombineId(other.getCombineId());
      }
      if (other.hasLv()) {
        setLv(other.getLv());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasCombineId()) {
        return false;
      }
      if (!hasLv()) {
        return false;
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              combineId_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              lv_ = input.readInt32();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int combineId_ ;
    /**
     * <code>required int32 combineId = 1;</code>
     * @return Whether the combineId field is set.
     */
    @java.lang.Override
    public boolean hasCombineId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 combineId = 1;</code>
     * @return The combineId.
     */
    @java.lang.Override
    public int getCombineId() {
      return combineId_;
    }
    /**
     * <code>required int32 combineId = 1;</code>
     * @param value The combineId to set.
     * @return This builder for chaining.
     */
    public Builder setCombineId(int value) {

      combineId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 combineId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearCombineId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      combineId_ = 0;
      onChanged();
      return this;
    }

    private int lv_ ;
    /**
     * <code>required int32 lv = 2;</code>
     * @return Whether the lv field is set.
     */
    @java.lang.Override
    public boolean hasLv() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>required int32 lv = 2;</code>
     * @return The lv.
     */
    @java.lang.Override
    public int getLv() {
      return lv_;
    }
    /**
     * <code>required int32 lv = 2;</code>
     * @param value The lv to set.
     * @return This builder for chaining.
     */
    public Builder setLv(int value) {

      lv_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 lv = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearLv() {
      bitField0_ = (bitField0_ & ~0x00000002);
      lv_ = 0;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.universeSkillLCombineMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.universeSkillLCombineMsg)
  private static final xddq.pb.universeSkillLCombineMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.universeSkillLCombineMsg();
  }

  public static xddq.pb.universeSkillLCombineMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<universeSkillLCombineMsg>
      PARSER = new com.google.protobuf.AbstractParser<universeSkillLCombineMsg>() {
    @java.lang.Override
    public universeSkillLCombineMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<universeSkillLCombineMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<universeSkillLCombineMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.universeSkillLCombineMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

