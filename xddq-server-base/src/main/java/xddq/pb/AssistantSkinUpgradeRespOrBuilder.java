// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

public interface AssistantSkinUpgradeRespOrBuilder extends
    // @@protoc_insertion_point(interface_extends:xddq.pb.AssistantSkinUpgradeResp)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  boolean hasRet();
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  int getRet();

  /**
   * <code>optional .xddq.pb.AssistantSkinMsg assistantSkinMsg = 2;</code>
   * @return Whether the assistantSkinMsg field is set.
   */
  boolean hasAssistantSkinMsg();
  /**
   * <code>optional .xddq.pb.AssistantSkinMsg assistantSkinMsg = 2;</code>
   * @return The assistantSkinMsg.
   */
  xddq.pb.AssistantSkinMsg getAssistantSkinMsg();
  /**
   * <code>optional .xddq.pb.AssistantSkinMsg assistantSkinMsg = 2;</code>
   */
  xddq.pb.AssistantSkinMsgOrBuilder getAssistantSkinMsgOrBuilder();
}
