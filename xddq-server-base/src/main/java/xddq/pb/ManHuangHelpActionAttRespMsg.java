// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.ManHuangHelpActionAttRespMsg}
 */
public final class ManHuangHelpActionAttRespMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.ManHuangHelpActionAttRespMsg)
    ManHuangHelpActionAttRespMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      ManHuangHelpActionAttRespMsg.class.getName());
  }
  // Use ManHuangHelpActionAttRespMsg.newBuilder() to construct.
  private ManHuangHelpActionAttRespMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private ManHuangHelpActionAttRespMsg() {
    blood_ = "";
    damage_ = "";
    rewards_ = java.util.Collections.emptyList();
    grass_ = emptyIntList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_ManHuangHelpActionAttRespMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_ManHuangHelpActionAttRespMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.ManHuangHelpActionAttRespMsg.class, xddq.pb.ManHuangHelpActionAttRespMsg.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int ISKILL_FIELD_NUMBER = 2;
  private boolean isKill_ = false;
  /**
   * <code>optional bool isKill = 2;</code>
   * @return Whether the isKill field is set.
   */
  @java.lang.Override
  public boolean hasIsKill() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional bool isKill = 2;</code>
   * @return The isKill.
   */
  @java.lang.Override
  public boolean getIsKill() {
    return isKill_;
  }

  public static final int BLOOD_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private volatile java.lang.Object blood_ = "";
  /**
   * <code>optional string blood = 3;</code>
   * @return Whether the blood field is set.
   */
  @java.lang.Override
  public boolean hasBlood() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional string blood = 3;</code>
   * @return The blood.
   */
  @java.lang.Override
  public java.lang.String getBlood() {
    java.lang.Object ref = blood_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        blood_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string blood = 3;</code>
   * @return The bytes for blood.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getBloodBytes() {
    java.lang.Object ref = blood_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      blood_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int DAMAGE_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private volatile java.lang.Object damage_ = "";
  /**
   * <code>optional string damage = 4;</code>
   * @return Whether the damage field is set.
   */
  @java.lang.Override
  public boolean hasDamage() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional string damage = 4;</code>
   * @return The damage.
   */
  @java.lang.Override
  public java.lang.String getDamage() {
    java.lang.Object ref = damage_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        damage_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string damage = 4;</code>
   * @return The bytes for damage.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getDamageBytes() {
    java.lang.Object ref = damage_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      damage_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int INTEGRAL_FIELD_NUMBER = 5;
  private long integral_ = 0L;
  /**
   * <code>optional int64 integral = 5;</code>
   * @return Whether the integral field is set.
   */
  @java.lang.Override
  public boolean hasIntegral() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>optional int64 integral = 5;</code>
   * @return The integral.
   */
  @java.lang.Override
  public long getIntegral() {
    return integral_;
  }

  public static final int REWARDS_FIELD_NUMBER = 6;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.AwardInfo> rewards_;
  /**
   * <code>repeated .xddq.pb.AwardInfo rewards = 6;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.AwardInfo> getRewardsList() {
    return rewards_;
  }
  /**
   * <code>repeated .xddq.pb.AwardInfo rewards = 6;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.AwardInfoOrBuilder> 
      getRewardsOrBuilderList() {
    return rewards_;
  }
  /**
   * <code>repeated .xddq.pb.AwardInfo rewards = 6;</code>
   */
  @java.lang.Override
  public int getRewardsCount() {
    return rewards_.size();
  }
  /**
   * <code>repeated .xddq.pb.AwardInfo rewards = 6;</code>
   */
  @java.lang.Override
  public xddq.pb.AwardInfo getRewards(int index) {
    return rewards_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.AwardInfo rewards = 6;</code>
   */
  @java.lang.Override
  public xddq.pb.AwardInfoOrBuilder getRewardsOrBuilder(
      int index) {
    return rewards_.get(index);
  }

  public static final int MONSTERBALL_FIELD_NUMBER = 7;
  private xddq.pb.ManHuangUserItemInfoTemp monsterBall_;
  /**
   * <code>optional .xddq.pb.ManHuangUserItemInfoTemp monsterBall = 7;</code>
   * @return Whether the monsterBall field is set.
   */
  @java.lang.Override
  public boolean hasMonsterBall() {
    return ((bitField0_ & 0x00000020) != 0);
  }
  /**
   * <code>optional .xddq.pb.ManHuangUserItemInfoTemp monsterBall = 7;</code>
   * @return The monsterBall.
   */
  @java.lang.Override
  public xddq.pb.ManHuangUserItemInfoTemp getMonsterBall() {
    return monsterBall_ == null ? xddq.pb.ManHuangUserItemInfoTemp.getDefaultInstance() : monsterBall_;
  }
  /**
   * <code>optional .xddq.pb.ManHuangUserItemInfoTemp monsterBall = 7;</code>
   */
  @java.lang.Override
  public xddq.pb.ManHuangUserItemInfoTempOrBuilder getMonsterBallOrBuilder() {
    return monsterBall_ == null ? xddq.pb.ManHuangUserItemInfoTemp.getDefaultInstance() : monsterBall_;
  }

  public static final int LASTATTACKTIME_FIELD_NUMBER = 8;
  private long lastAttackTime_ = 0L;
  /**
   * <code>optional int64 lastAttackTime = 8;</code>
   * @return Whether the lastAttackTime field is set.
   */
  @java.lang.Override
  public boolean hasLastAttackTime() {
    return ((bitField0_ & 0x00000040) != 0);
  }
  /**
   * <code>optional int64 lastAttackTime = 8;</code>
   * @return The lastAttackTime.
   */
  @java.lang.Override
  public long getLastAttackTime() {
    return lastAttackTime_;
  }

  public static final int GRASS_FIELD_NUMBER = 9;
  @SuppressWarnings("serial")
  private com.google.protobuf.Internal.IntList grass_ =
      emptyIntList();
  /**
   * <code>repeated int32 grass = 9;</code>
   * @return A list containing the grass.
   */
  @java.lang.Override
  public java.util.List<java.lang.Integer>
      getGrassList() {
    return grass_;
  }
  /**
   * <code>repeated int32 grass = 9;</code>
   * @return The count of grass.
   */
  public int getGrassCount() {
    return grass_.size();
  }
  /**
   * <code>repeated int32 grass = 9;</code>
   * @param index The index of the element to return.
   * @return The grass at the given index.
   */
  public int getGrass(int index) {
    return grass_.getInt(index);
  }

  public static final int ICESOUL_FIELD_NUMBER = 10;
  private int iceSoul_ = 0;
  /**
   * <code>optional int32 iceSoul = 10;</code>
   * @return Whether the iceSoul field is set.
   */
  @java.lang.Override
  public boolean hasIceSoul() {
    return ((bitField0_ & 0x00000080) != 0);
  }
  /**
   * <code>optional int32 iceSoul = 10;</code>
   * @return The iceSoul.
   */
  @java.lang.Override
  public int getIceSoul() {
    return iceSoul_;
  }

  public static final int BATTLERECORD_FIELD_NUMBER = 11;
  private xddq.pb.BattleRecordMsg battleRecord_;
  /**
   * <code>optional .xddq.pb.BattleRecordMsg battleRecord = 11;</code>
   * @return Whether the battleRecord field is set.
   */
  @java.lang.Override
  public boolean hasBattleRecord() {
    return ((bitField0_ & 0x00000100) != 0);
  }
  /**
   * <code>optional .xddq.pb.BattleRecordMsg battleRecord = 11;</code>
   * @return The battleRecord.
   */
  @java.lang.Override
  public xddq.pb.BattleRecordMsg getBattleRecord() {
    return battleRecord_ == null ? xddq.pb.BattleRecordMsg.getDefaultInstance() : battleRecord_;
  }
  /**
   * <code>optional .xddq.pb.BattleRecordMsg battleRecord = 11;</code>
   */
  @java.lang.Override
  public xddq.pb.BattleRecordMsgOrBuilder getBattleRecordOrBuilder() {
    return battleRecord_ == null ? xddq.pb.BattleRecordMsg.getDefaultInstance() : battleRecord_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (hasMonsterBall()) {
      if (!getMonsterBall().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    if (hasBattleRecord()) {
      if (!getBattleRecord().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeBool(2, isKill_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 3, blood_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 4, damage_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      output.writeInt64(5, integral_);
    }
    for (int i = 0; i < rewards_.size(); i++) {
      output.writeMessage(6, rewards_.get(i));
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      output.writeMessage(7, getMonsterBall());
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      output.writeInt64(8, lastAttackTime_);
    }
    for (int i = 0; i < grass_.size(); i++) {
      output.writeInt32(9, grass_.getInt(i));
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      output.writeInt32(10, iceSoul_);
    }
    if (((bitField0_ & 0x00000100) != 0)) {
      output.writeMessage(11, getBattleRecord());
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(2, isKill_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(3, blood_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(4, damage_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(5, integral_);
    }
    for (int i = 0; i < rewards_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(6, rewards_.get(i));
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(7, getMonsterBall());
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(8, lastAttackTime_);
    }
    {
      int dataSize = 0;
      for (int i = 0; i < grass_.size(); i++) {
        dataSize += com.google.protobuf.CodedOutputStream
          .computeInt32SizeNoTag(grass_.getInt(i));
      }
      size += dataSize;
      size += 1 * getGrassList().size();
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(10, iceSoul_);
    }
    if (((bitField0_ & 0x00000100) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(11, getBattleRecord());
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.ManHuangHelpActionAttRespMsg)) {
      return super.equals(obj);
    }
    xddq.pb.ManHuangHelpActionAttRespMsg other = (xddq.pb.ManHuangHelpActionAttRespMsg) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (hasIsKill() != other.hasIsKill()) return false;
    if (hasIsKill()) {
      if (getIsKill()
          != other.getIsKill()) return false;
    }
    if (hasBlood() != other.hasBlood()) return false;
    if (hasBlood()) {
      if (!getBlood()
          .equals(other.getBlood())) return false;
    }
    if (hasDamage() != other.hasDamage()) return false;
    if (hasDamage()) {
      if (!getDamage()
          .equals(other.getDamage())) return false;
    }
    if (hasIntegral() != other.hasIntegral()) return false;
    if (hasIntegral()) {
      if (getIntegral()
          != other.getIntegral()) return false;
    }
    if (!getRewardsList()
        .equals(other.getRewardsList())) return false;
    if (hasMonsterBall() != other.hasMonsterBall()) return false;
    if (hasMonsterBall()) {
      if (!getMonsterBall()
          .equals(other.getMonsterBall())) return false;
    }
    if (hasLastAttackTime() != other.hasLastAttackTime()) return false;
    if (hasLastAttackTime()) {
      if (getLastAttackTime()
          != other.getLastAttackTime()) return false;
    }
    if (!getGrassList()
        .equals(other.getGrassList())) return false;
    if (hasIceSoul() != other.hasIceSoul()) return false;
    if (hasIceSoul()) {
      if (getIceSoul()
          != other.getIceSoul()) return false;
    }
    if (hasBattleRecord() != other.hasBattleRecord()) return false;
    if (hasBattleRecord()) {
      if (!getBattleRecord()
          .equals(other.getBattleRecord())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (hasIsKill()) {
      hash = (37 * hash) + ISKILL_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getIsKill());
    }
    if (hasBlood()) {
      hash = (37 * hash) + BLOOD_FIELD_NUMBER;
      hash = (53 * hash) + getBlood().hashCode();
    }
    if (hasDamage()) {
      hash = (37 * hash) + DAMAGE_FIELD_NUMBER;
      hash = (53 * hash) + getDamage().hashCode();
    }
    if (hasIntegral()) {
      hash = (37 * hash) + INTEGRAL_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getIntegral());
    }
    if (getRewardsCount() > 0) {
      hash = (37 * hash) + REWARDS_FIELD_NUMBER;
      hash = (53 * hash) + getRewardsList().hashCode();
    }
    if (hasMonsterBall()) {
      hash = (37 * hash) + MONSTERBALL_FIELD_NUMBER;
      hash = (53 * hash) + getMonsterBall().hashCode();
    }
    if (hasLastAttackTime()) {
      hash = (37 * hash) + LASTATTACKTIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getLastAttackTime());
    }
    if (getGrassCount() > 0) {
      hash = (37 * hash) + GRASS_FIELD_NUMBER;
      hash = (53 * hash) + getGrassList().hashCode();
    }
    if (hasIceSoul()) {
      hash = (37 * hash) + ICESOUL_FIELD_NUMBER;
      hash = (53 * hash) + getIceSoul();
    }
    if (hasBattleRecord()) {
      hash = (37 * hash) + BATTLERECORD_FIELD_NUMBER;
      hash = (53 * hash) + getBattleRecord().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.ManHuangHelpActionAttRespMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ManHuangHelpActionAttRespMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ManHuangHelpActionAttRespMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ManHuangHelpActionAttRespMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ManHuangHelpActionAttRespMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ManHuangHelpActionAttRespMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ManHuangHelpActionAttRespMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.ManHuangHelpActionAttRespMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.ManHuangHelpActionAttRespMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.ManHuangHelpActionAttRespMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.ManHuangHelpActionAttRespMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.ManHuangHelpActionAttRespMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.ManHuangHelpActionAttRespMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.ManHuangHelpActionAttRespMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.ManHuangHelpActionAttRespMsg)
      xddq.pb.ManHuangHelpActionAttRespMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ManHuangHelpActionAttRespMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ManHuangHelpActionAttRespMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.ManHuangHelpActionAttRespMsg.class, xddq.pb.ManHuangHelpActionAttRespMsg.Builder.class);
    }

    // Construct using xddq.pb.ManHuangHelpActionAttRespMsg.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetRewardsFieldBuilder();
        internalGetMonsterBallFieldBuilder();
        internalGetBattleRecordFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      isKill_ = false;
      blood_ = "";
      damage_ = "";
      integral_ = 0L;
      if (rewardsBuilder_ == null) {
        rewards_ = java.util.Collections.emptyList();
      } else {
        rewards_ = null;
        rewardsBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000020);
      monsterBall_ = null;
      if (monsterBallBuilder_ != null) {
        monsterBallBuilder_.dispose();
        monsterBallBuilder_ = null;
      }
      lastAttackTime_ = 0L;
      grass_ = emptyIntList();
      iceSoul_ = 0;
      battleRecord_ = null;
      if (battleRecordBuilder_ != null) {
        battleRecordBuilder_.dispose();
        battleRecordBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ManHuangHelpActionAttRespMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.ManHuangHelpActionAttRespMsg getDefaultInstanceForType() {
      return xddq.pb.ManHuangHelpActionAttRespMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.ManHuangHelpActionAttRespMsg build() {
      xddq.pb.ManHuangHelpActionAttRespMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.ManHuangHelpActionAttRespMsg buildPartial() {
      xddq.pb.ManHuangHelpActionAttRespMsg result = new xddq.pb.ManHuangHelpActionAttRespMsg(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.ManHuangHelpActionAttRespMsg result) {
      if (rewardsBuilder_ == null) {
        if (((bitField0_ & 0x00000020) != 0)) {
          rewards_ = java.util.Collections.unmodifiableList(rewards_);
          bitField0_ = (bitField0_ & ~0x00000020);
        }
        result.rewards_ = rewards_;
      } else {
        result.rewards_ = rewardsBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.ManHuangHelpActionAttRespMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.isKill_ = isKill_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.blood_ = blood_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.damage_ = damage_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.integral_ = integral_;
        to_bitField0_ |= 0x00000010;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.monsterBall_ = monsterBallBuilder_ == null
            ? monsterBall_
            : monsterBallBuilder_.build();
        to_bitField0_ |= 0x00000020;
      }
      if (((from_bitField0_ & 0x00000080) != 0)) {
        result.lastAttackTime_ = lastAttackTime_;
        to_bitField0_ |= 0x00000040;
      }
      if (((from_bitField0_ & 0x00000100) != 0)) {
        grass_.makeImmutable();
        result.grass_ = grass_;
      }
      if (((from_bitField0_ & 0x00000200) != 0)) {
        result.iceSoul_ = iceSoul_;
        to_bitField0_ |= 0x00000080;
      }
      if (((from_bitField0_ & 0x00000400) != 0)) {
        result.battleRecord_ = battleRecordBuilder_ == null
            ? battleRecord_
            : battleRecordBuilder_.build();
        to_bitField0_ |= 0x00000100;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.ManHuangHelpActionAttRespMsg) {
        return mergeFrom((xddq.pb.ManHuangHelpActionAttRespMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.ManHuangHelpActionAttRespMsg other) {
      if (other == xddq.pb.ManHuangHelpActionAttRespMsg.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (other.hasIsKill()) {
        setIsKill(other.getIsKill());
      }
      if (other.hasBlood()) {
        blood_ = other.blood_;
        bitField0_ |= 0x00000004;
        onChanged();
      }
      if (other.hasDamage()) {
        damage_ = other.damage_;
        bitField0_ |= 0x00000008;
        onChanged();
      }
      if (other.hasIntegral()) {
        setIntegral(other.getIntegral());
      }
      if (rewardsBuilder_ == null) {
        if (!other.rewards_.isEmpty()) {
          if (rewards_.isEmpty()) {
            rewards_ = other.rewards_;
            bitField0_ = (bitField0_ & ~0x00000020);
          } else {
            ensureRewardsIsMutable();
            rewards_.addAll(other.rewards_);
          }
          onChanged();
        }
      } else {
        if (!other.rewards_.isEmpty()) {
          if (rewardsBuilder_.isEmpty()) {
            rewardsBuilder_.dispose();
            rewardsBuilder_ = null;
            rewards_ = other.rewards_;
            bitField0_ = (bitField0_ & ~0x00000020);
            rewardsBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetRewardsFieldBuilder() : null;
          } else {
            rewardsBuilder_.addAllMessages(other.rewards_);
          }
        }
      }
      if (other.hasMonsterBall()) {
        mergeMonsterBall(other.getMonsterBall());
      }
      if (other.hasLastAttackTime()) {
        setLastAttackTime(other.getLastAttackTime());
      }
      if (!other.grass_.isEmpty()) {
        if (grass_.isEmpty()) {
          grass_ = other.grass_;
          grass_.makeImmutable();
          bitField0_ |= 0x00000100;
        } else {
          ensureGrassIsMutable();
          grass_.addAll(other.grass_);
        }
        onChanged();
      }
      if (other.hasIceSoul()) {
        setIceSoul(other.getIceSoul());
      }
      if (other.hasBattleRecord()) {
        mergeBattleRecord(other.getBattleRecord());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      if (hasMonsterBall()) {
        if (!getMonsterBall().isInitialized()) {
          return false;
        }
      }
      if (hasBattleRecord()) {
        if (!getBattleRecord().isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              isKill_ = input.readBool();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 26: {
              blood_ = input.readBytes();
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 34: {
              damage_ = input.readBytes();
              bitField0_ |= 0x00000008;
              break;
            } // case 34
            case 40: {
              integral_ = input.readInt64();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            case 50: {
              xddq.pb.AwardInfo m =
                  input.readMessage(
                      xddq.pb.AwardInfo.parser(),
                      extensionRegistry);
              if (rewardsBuilder_ == null) {
                ensureRewardsIsMutable();
                rewards_.add(m);
              } else {
                rewardsBuilder_.addMessage(m);
              }
              break;
            } // case 50
            case 58: {
              input.readMessage(
                  internalGetMonsterBallFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000040;
              break;
            } // case 58
            case 64: {
              lastAttackTime_ = input.readInt64();
              bitField0_ |= 0x00000080;
              break;
            } // case 64
            case 72: {
              int v = input.readInt32();
              ensureGrassIsMutable();
              grass_.addInt(v);
              break;
            } // case 72
            case 74: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              ensureGrassIsMutable();
              while (input.getBytesUntilLimit() > 0) {
                grass_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            } // case 74
            case 80: {
              iceSoul_ = input.readInt32();
              bitField0_ |= 0x00000200;
              break;
            } // case 80
            case 90: {
              input.readMessage(
                  internalGetBattleRecordFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000400;
              break;
            } // case 90
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private boolean isKill_ ;
    /**
     * <code>optional bool isKill = 2;</code>
     * @return Whether the isKill field is set.
     */
    @java.lang.Override
    public boolean hasIsKill() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional bool isKill = 2;</code>
     * @return The isKill.
     */
    @java.lang.Override
    public boolean getIsKill() {
      return isKill_;
    }
    /**
     * <code>optional bool isKill = 2;</code>
     * @param value The isKill to set.
     * @return This builder for chaining.
     */
    public Builder setIsKill(boolean value) {

      isKill_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional bool isKill = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearIsKill() {
      bitField0_ = (bitField0_ & ~0x00000002);
      isKill_ = false;
      onChanged();
      return this;
    }

    private java.lang.Object blood_ = "";
    /**
     * <code>optional string blood = 3;</code>
     * @return Whether the blood field is set.
     */
    public boolean hasBlood() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional string blood = 3;</code>
     * @return The blood.
     */
    public java.lang.String getBlood() {
      java.lang.Object ref = blood_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          blood_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string blood = 3;</code>
     * @return The bytes for blood.
     */
    public com.google.protobuf.ByteString
        getBloodBytes() {
      java.lang.Object ref = blood_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        blood_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string blood = 3;</code>
     * @param value The blood to set.
     * @return This builder for chaining.
     */
    public Builder setBlood(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      blood_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional string blood = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearBlood() {
      blood_ = getDefaultInstance().getBlood();
      bitField0_ = (bitField0_ & ~0x00000004);
      onChanged();
      return this;
    }
    /**
     * <code>optional string blood = 3;</code>
     * @param value The bytes for blood to set.
     * @return This builder for chaining.
     */
    public Builder setBloodBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      blood_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }

    private java.lang.Object damage_ = "";
    /**
     * <code>optional string damage = 4;</code>
     * @return Whether the damage field is set.
     */
    public boolean hasDamage() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional string damage = 4;</code>
     * @return The damage.
     */
    public java.lang.String getDamage() {
      java.lang.Object ref = damage_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          damage_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string damage = 4;</code>
     * @return The bytes for damage.
     */
    public com.google.protobuf.ByteString
        getDamageBytes() {
      java.lang.Object ref = damage_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        damage_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string damage = 4;</code>
     * @param value The damage to set.
     * @return This builder for chaining.
     */
    public Builder setDamage(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      damage_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional string damage = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearDamage() {
      damage_ = getDefaultInstance().getDamage();
      bitField0_ = (bitField0_ & ~0x00000008);
      onChanged();
      return this;
    }
    /**
     * <code>optional string damage = 4;</code>
     * @param value The bytes for damage to set.
     * @return This builder for chaining.
     */
    public Builder setDamageBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      damage_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }

    private long integral_ ;
    /**
     * <code>optional int64 integral = 5;</code>
     * @return Whether the integral field is set.
     */
    @java.lang.Override
    public boolean hasIntegral() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional int64 integral = 5;</code>
     * @return The integral.
     */
    @java.lang.Override
    public long getIntegral() {
      return integral_;
    }
    /**
     * <code>optional int64 integral = 5;</code>
     * @param value The integral to set.
     * @return This builder for chaining.
     */
    public Builder setIntegral(long value) {

      integral_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 integral = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearIntegral() {
      bitField0_ = (bitField0_ & ~0x00000010);
      integral_ = 0L;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.AwardInfo> rewards_ =
      java.util.Collections.emptyList();
    private void ensureRewardsIsMutable() {
      if (!((bitField0_ & 0x00000020) != 0)) {
        rewards_ = new java.util.ArrayList<xddq.pb.AwardInfo>(rewards_);
        bitField0_ |= 0x00000020;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.AwardInfo, xddq.pb.AwardInfo.Builder, xddq.pb.AwardInfoOrBuilder> rewardsBuilder_;

    /**
     * <code>repeated .xddq.pb.AwardInfo rewards = 6;</code>
     */
    public java.util.List<xddq.pb.AwardInfo> getRewardsList() {
      if (rewardsBuilder_ == null) {
        return java.util.Collections.unmodifiableList(rewards_);
      } else {
        return rewardsBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.AwardInfo rewards = 6;</code>
     */
    public int getRewardsCount() {
      if (rewardsBuilder_ == null) {
        return rewards_.size();
      } else {
        return rewardsBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.AwardInfo rewards = 6;</code>
     */
    public xddq.pb.AwardInfo getRewards(int index) {
      if (rewardsBuilder_ == null) {
        return rewards_.get(index);
      } else {
        return rewardsBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.AwardInfo rewards = 6;</code>
     */
    public Builder setRewards(
        int index, xddq.pb.AwardInfo value) {
      if (rewardsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRewardsIsMutable();
        rewards_.set(index, value);
        onChanged();
      } else {
        rewardsBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.AwardInfo rewards = 6;</code>
     */
    public Builder setRewards(
        int index, xddq.pb.AwardInfo.Builder builderForValue) {
      if (rewardsBuilder_ == null) {
        ensureRewardsIsMutable();
        rewards_.set(index, builderForValue.build());
        onChanged();
      } else {
        rewardsBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.AwardInfo rewards = 6;</code>
     */
    public Builder addRewards(xddq.pb.AwardInfo value) {
      if (rewardsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRewardsIsMutable();
        rewards_.add(value);
        onChanged();
      } else {
        rewardsBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.AwardInfo rewards = 6;</code>
     */
    public Builder addRewards(
        int index, xddq.pb.AwardInfo value) {
      if (rewardsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRewardsIsMutable();
        rewards_.add(index, value);
        onChanged();
      } else {
        rewardsBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.AwardInfo rewards = 6;</code>
     */
    public Builder addRewards(
        xddq.pb.AwardInfo.Builder builderForValue) {
      if (rewardsBuilder_ == null) {
        ensureRewardsIsMutable();
        rewards_.add(builderForValue.build());
        onChanged();
      } else {
        rewardsBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.AwardInfo rewards = 6;</code>
     */
    public Builder addRewards(
        int index, xddq.pb.AwardInfo.Builder builderForValue) {
      if (rewardsBuilder_ == null) {
        ensureRewardsIsMutable();
        rewards_.add(index, builderForValue.build());
        onChanged();
      } else {
        rewardsBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.AwardInfo rewards = 6;</code>
     */
    public Builder addAllRewards(
        java.lang.Iterable<? extends xddq.pb.AwardInfo> values) {
      if (rewardsBuilder_ == null) {
        ensureRewardsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, rewards_);
        onChanged();
      } else {
        rewardsBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.AwardInfo rewards = 6;</code>
     */
    public Builder clearRewards() {
      if (rewardsBuilder_ == null) {
        rewards_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000020);
        onChanged();
      } else {
        rewardsBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.AwardInfo rewards = 6;</code>
     */
    public Builder removeRewards(int index) {
      if (rewardsBuilder_ == null) {
        ensureRewardsIsMutable();
        rewards_.remove(index);
        onChanged();
      } else {
        rewardsBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.AwardInfo rewards = 6;</code>
     */
    public xddq.pb.AwardInfo.Builder getRewardsBuilder(
        int index) {
      return internalGetRewardsFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.AwardInfo rewards = 6;</code>
     */
    public xddq.pb.AwardInfoOrBuilder getRewardsOrBuilder(
        int index) {
      if (rewardsBuilder_ == null) {
        return rewards_.get(index);  } else {
        return rewardsBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.AwardInfo rewards = 6;</code>
     */
    public java.util.List<? extends xddq.pb.AwardInfoOrBuilder> 
         getRewardsOrBuilderList() {
      if (rewardsBuilder_ != null) {
        return rewardsBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(rewards_);
      }
    }
    /**
     * <code>repeated .xddq.pb.AwardInfo rewards = 6;</code>
     */
    public xddq.pb.AwardInfo.Builder addRewardsBuilder() {
      return internalGetRewardsFieldBuilder().addBuilder(
          xddq.pb.AwardInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.AwardInfo rewards = 6;</code>
     */
    public xddq.pb.AwardInfo.Builder addRewardsBuilder(
        int index) {
      return internalGetRewardsFieldBuilder().addBuilder(
          index, xddq.pb.AwardInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.AwardInfo rewards = 6;</code>
     */
    public java.util.List<xddq.pb.AwardInfo.Builder> 
         getRewardsBuilderList() {
      return internalGetRewardsFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.AwardInfo, xddq.pb.AwardInfo.Builder, xddq.pb.AwardInfoOrBuilder> 
        internalGetRewardsFieldBuilder() {
      if (rewardsBuilder_ == null) {
        rewardsBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.AwardInfo, xddq.pb.AwardInfo.Builder, xddq.pb.AwardInfoOrBuilder>(
                rewards_,
                ((bitField0_ & 0x00000020) != 0),
                getParentForChildren(),
                isClean());
        rewards_ = null;
      }
      return rewardsBuilder_;
    }

    private xddq.pb.ManHuangUserItemInfoTemp monsterBall_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.ManHuangUserItemInfoTemp, xddq.pb.ManHuangUserItemInfoTemp.Builder, xddq.pb.ManHuangUserItemInfoTempOrBuilder> monsterBallBuilder_;
    /**
     * <code>optional .xddq.pb.ManHuangUserItemInfoTemp monsterBall = 7;</code>
     * @return Whether the monsterBall field is set.
     */
    public boolean hasMonsterBall() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <code>optional .xddq.pb.ManHuangUserItemInfoTemp monsterBall = 7;</code>
     * @return The monsterBall.
     */
    public xddq.pb.ManHuangUserItemInfoTemp getMonsterBall() {
      if (monsterBallBuilder_ == null) {
        return monsterBall_ == null ? xddq.pb.ManHuangUserItemInfoTemp.getDefaultInstance() : monsterBall_;
      } else {
        return monsterBallBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.ManHuangUserItemInfoTemp monsterBall = 7;</code>
     */
    public Builder setMonsterBall(xddq.pb.ManHuangUserItemInfoTemp value) {
      if (monsterBallBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        monsterBall_ = value;
      } else {
        monsterBallBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ManHuangUserItemInfoTemp monsterBall = 7;</code>
     */
    public Builder setMonsterBall(
        xddq.pb.ManHuangUserItemInfoTemp.Builder builderForValue) {
      if (monsterBallBuilder_ == null) {
        monsterBall_ = builderForValue.build();
      } else {
        monsterBallBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ManHuangUserItemInfoTemp monsterBall = 7;</code>
     */
    public Builder mergeMonsterBall(xddq.pb.ManHuangUserItemInfoTemp value) {
      if (monsterBallBuilder_ == null) {
        if (((bitField0_ & 0x00000040) != 0) &&
          monsterBall_ != null &&
          monsterBall_ != xddq.pb.ManHuangUserItemInfoTemp.getDefaultInstance()) {
          getMonsterBallBuilder().mergeFrom(value);
        } else {
          monsterBall_ = value;
        }
      } else {
        monsterBallBuilder_.mergeFrom(value);
      }
      if (monsterBall_ != null) {
        bitField0_ |= 0x00000040;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.ManHuangUserItemInfoTemp monsterBall = 7;</code>
     */
    public Builder clearMonsterBall() {
      bitField0_ = (bitField0_ & ~0x00000040);
      monsterBall_ = null;
      if (monsterBallBuilder_ != null) {
        monsterBallBuilder_.dispose();
        monsterBallBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ManHuangUserItemInfoTemp monsterBall = 7;</code>
     */
    public xddq.pb.ManHuangUserItemInfoTemp.Builder getMonsterBallBuilder() {
      bitField0_ |= 0x00000040;
      onChanged();
      return internalGetMonsterBallFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.ManHuangUserItemInfoTemp monsterBall = 7;</code>
     */
    public xddq.pb.ManHuangUserItemInfoTempOrBuilder getMonsterBallOrBuilder() {
      if (monsterBallBuilder_ != null) {
        return monsterBallBuilder_.getMessageOrBuilder();
      } else {
        return monsterBall_ == null ?
            xddq.pb.ManHuangUserItemInfoTemp.getDefaultInstance() : monsterBall_;
      }
    }
    /**
     * <code>optional .xddq.pb.ManHuangUserItemInfoTemp monsterBall = 7;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.ManHuangUserItemInfoTemp, xddq.pb.ManHuangUserItemInfoTemp.Builder, xddq.pb.ManHuangUserItemInfoTempOrBuilder> 
        internalGetMonsterBallFieldBuilder() {
      if (monsterBallBuilder_ == null) {
        monsterBallBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.ManHuangUserItemInfoTemp, xddq.pb.ManHuangUserItemInfoTemp.Builder, xddq.pb.ManHuangUserItemInfoTempOrBuilder>(
                getMonsterBall(),
                getParentForChildren(),
                isClean());
        monsterBall_ = null;
      }
      return monsterBallBuilder_;
    }

    private long lastAttackTime_ ;
    /**
     * <code>optional int64 lastAttackTime = 8;</code>
     * @return Whether the lastAttackTime field is set.
     */
    @java.lang.Override
    public boolean hasLastAttackTime() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <code>optional int64 lastAttackTime = 8;</code>
     * @return The lastAttackTime.
     */
    @java.lang.Override
    public long getLastAttackTime() {
      return lastAttackTime_;
    }
    /**
     * <code>optional int64 lastAttackTime = 8;</code>
     * @param value The lastAttackTime to set.
     * @return This builder for chaining.
     */
    public Builder setLastAttackTime(long value) {

      lastAttackTime_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 lastAttackTime = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearLastAttackTime() {
      bitField0_ = (bitField0_ & ~0x00000080);
      lastAttackTime_ = 0L;
      onChanged();
      return this;
    }

    private com.google.protobuf.Internal.IntList grass_ = emptyIntList();
    private void ensureGrassIsMutable() {
      if (!grass_.isModifiable()) {
        grass_ = makeMutableCopy(grass_);
      }
      bitField0_ |= 0x00000100;
    }
    /**
     * <code>repeated int32 grass = 9;</code>
     * @return A list containing the grass.
     */
    public java.util.List<java.lang.Integer>
        getGrassList() {
      grass_.makeImmutable();
      return grass_;
    }
    /**
     * <code>repeated int32 grass = 9;</code>
     * @return The count of grass.
     */
    public int getGrassCount() {
      return grass_.size();
    }
    /**
     * <code>repeated int32 grass = 9;</code>
     * @param index The index of the element to return.
     * @return The grass at the given index.
     */
    public int getGrass(int index) {
      return grass_.getInt(index);
    }
    /**
     * <code>repeated int32 grass = 9;</code>
     * @param index The index to set the value at.
     * @param value The grass to set.
     * @return This builder for chaining.
     */
    public Builder setGrass(
        int index, int value) {

      ensureGrassIsMutable();
      grass_.setInt(index, value);
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 grass = 9;</code>
     * @param value The grass to add.
     * @return This builder for chaining.
     */
    public Builder addGrass(int value) {

      ensureGrassIsMutable();
      grass_.addInt(value);
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 grass = 9;</code>
     * @param values The grass to add.
     * @return This builder for chaining.
     */
    public Builder addAllGrass(
        java.lang.Iterable<? extends java.lang.Integer> values) {
      ensureGrassIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, grass_);
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 grass = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearGrass() {
      grass_ = emptyIntList();
      bitField0_ = (bitField0_ & ~0x00000100);
      onChanged();
      return this;
    }

    private int iceSoul_ ;
    /**
     * <code>optional int32 iceSoul = 10;</code>
     * @return Whether the iceSoul field is set.
     */
    @java.lang.Override
    public boolean hasIceSoul() {
      return ((bitField0_ & 0x00000200) != 0);
    }
    /**
     * <code>optional int32 iceSoul = 10;</code>
     * @return The iceSoul.
     */
    @java.lang.Override
    public int getIceSoul() {
      return iceSoul_;
    }
    /**
     * <code>optional int32 iceSoul = 10;</code>
     * @param value The iceSoul to set.
     * @return This builder for chaining.
     */
    public Builder setIceSoul(int value) {

      iceSoul_ = value;
      bitField0_ |= 0x00000200;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 iceSoul = 10;</code>
     * @return This builder for chaining.
     */
    public Builder clearIceSoul() {
      bitField0_ = (bitField0_ & ~0x00000200);
      iceSoul_ = 0;
      onChanged();
      return this;
    }

    private xddq.pb.BattleRecordMsg battleRecord_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.BattleRecordMsg, xddq.pb.BattleRecordMsg.Builder, xddq.pb.BattleRecordMsgOrBuilder> battleRecordBuilder_;
    /**
     * <code>optional .xddq.pb.BattleRecordMsg battleRecord = 11;</code>
     * @return Whether the battleRecord field is set.
     */
    public boolean hasBattleRecord() {
      return ((bitField0_ & 0x00000400) != 0);
    }
    /**
     * <code>optional .xddq.pb.BattleRecordMsg battleRecord = 11;</code>
     * @return The battleRecord.
     */
    public xddq.pb.BattleRecordMsg getBattleRecord() {
      if (battleRecordBuilder_ == null) {
        return battleRecord_ == null ? xddq.pb.BattleRecordMsg.getDefaultInstance() : battleRecord_;
      } else {
        return battleRecordBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.BattleRecordMsg battleRecord = 11;</code>
     */
    public Builder setBattleRecord(xddq.pb.BattleRecordMsg value) {
      if (battleRecordBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        battleRecord_ = value;
      } else {
        battleRecordBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000400;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.BattleRecordMsg battleRecord = 11;</code>
     */
    public Builder setBattleRecord(
        xddq.pb.BattleRecordMsg.Builder builderForValue) {
      if (battleRecordBuilder_ == null) {
        battleRecord_ = builderForValue.build();
      } else {
        battleRecordBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000400;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.BattleRecordMsg battleRecord = 11;</code>
     */
    public Builder mergeBattleRecord(xddq.pb.BattleRecordMsg value) {
      if (battleRecordBuilder_ == null) {
        if (((bitField0_ & 0x00000400) != 0) &&
          battleRecord_ != null &&
          battleRecord_ != xddq.pb.BattleRecordMsg.getDefaultInstance()) {
          getBattleRecordBuilder().mergeFrom(value);
        } else {
          battleRecord_ = value;
        }
      } else {
        battleRecordBuilder_.mergeFrom(value);
      }
      if (battleRecord_ != null) {
        bitField0_ |= 0x00000400;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.BattleRecordMsg battleRecord = 11;</code>
     */
    public Builder clearBattleRecord() {
      bitField0_ = (bitField0_ & ~0x00000400);
      battleRecord_ = null;
      if (battleRecordBuilder_ != null) {
        battleRecordBuilder_.dispose();
        battleRecordBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.BattleRecordMsg battleRecord = 11;</code>
     */
    public xddq.pb.BattleRecordMsg.Builder getBattleRecordBuilder() {
      bitField0_ |= 0x00000400;
      onChanged();
      return internalGetBattleRecordFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.BattleRecordMsg battleRecord = 11;</code>
     */
    public xddq.pb.BattleRecordMsgOrBuilder getBattleRecordOrBuilder() {
      if (battleRecordBuilder_ != null) {
        return battleRecordBuilder_.getMessageOrBuilder();
      } else {
        return battleRecord_ == null ?
            xddq.pb.BattleRecordMsg.getDefaultInstance() : battleRecord_;
      }
    }
    /**
     * <code>optional .xddq.pb.BattleRecordMsg battleRecord = 11;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.BattleRecordMsg, xddq.pb.BattleRecordMsg.Builder, xddq.pb.BattleRecordMsgOrBuilder> 
        internalGetBattleRecordFieldBuilder() {
      if (battleRecordBuilder_ == null) {
        battleRecordBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.BattleRecordMsg, xddq.pb.BattleRecordMsg.Builder, xddq.pb.BattleRecordMsgOrBuilder>(
                getBattleRecord(),
                getParentForChildren(),
                isClean());
        battleRecord_ = null;
      }
      return battleRecordBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.ManHuangHelpActionAttRespMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.ManHuangHelpActionAttRespMsg)
  private static final xddq.pb.ManHuangHelpActionAttRespMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.ManHuangHelpActionAttRespMsg();
  }

  public static xddq.pb.ManHuangHelpActionAttRespMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ManHuangHelpActionAttRespMsg>
      PARSER = new com.google.protobuf.AbstractParser<ManHuangHelpActionAttRespMsg>() {
    @java.lang.Override
    public ManHuangHelpActionAttRespMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<ManHuangHelpActionAttRespMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ManHuangHelpActionAttRespMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.ManHuangHelpActionAttRespMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

