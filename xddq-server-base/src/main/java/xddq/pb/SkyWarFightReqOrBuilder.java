// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

public interface SkyWarFightReqOrBuilder extends
    // @@protoc_insertion_point(interface_extends:xddq.pb.SkyWarFightReq)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>required int64 playerId = 1;</code>
   * @return Whether the playerId field is set.
   */
  boolean hasPlayerId();
  /**
   * <code>required int64 playerId = 1;</code>
   * @return The playerId.
   */
  long getPlayerId();

  /**
   * <code>required int64 targetPlayerId = 2;</code>
   * @return Whether the targetPlayerId field is set.
   */
  boolean hasTargetPlayerId();
  /**
   * <code>required int64 targetPlayerId = 2;</code>
   * @return The targetPlayerId.
   */
  long getTargetPlayerId();

  /**
   * <code>required int64 targetServerId = 3;</code>
   * @return Whether the targetServerId field is set.
   */
  boolean hasTargetServerId();
  /**
   * <code>required int64 targetServerId = 3;</code>
   * @return The targetServerId.
   */
  long getTargetServerId();

  /**
   * <code>required int32 position = 4;</code>
   * @return Whether the position field is set.
   */
  boolean hasPosition();
  /**
   * <code>required int32 position = 4;</code>
   * @return The position.
   */
  int getPosition();
}
