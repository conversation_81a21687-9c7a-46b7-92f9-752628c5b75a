// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.MonopolyPlayerLogDetailInfo}
 */
public final class MonopolyPlayerLogDetailInfo extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.MonopolyPlayerLogDetailInfo)
    MonopolyPlayerLogDetailInfoOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      MonopolyPlayerLogDetailInfo.class.getName());
  }
  // Use MonopolyPlayerLogDetailInfo.newBuilder() to construct.
  private MonopolyPlayerLogDetailInfo(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private MonopolyPlayerLogDetailInfo() {
    robUnionName_ = "";
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_MonopolyPlayerLogDetailInfo_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_MonopolyPlayerLogDetailInfo_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.MonopolyPlayerLogDetailInfo.class, xddq.pb.MonopolyPlayerLogDetailInfo.Builder.class);
  }

  private int bitField0_;
  public static final int GAINSCORE_FIELD_NUMBER = 1;
  private int gainScore_ = 0;
  /**
   * <code>optional int32 gainScore = 1;</code>
   * @return Whether the gainScore field is set.
   */
  @java.lang.Override
  public boolean hasGainScore() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int32 gainScore = 1;</code>
   * @return The gainScore.
   */
  @java.lang.Override
  public int getGainScore() {
    return gainScore_;
  }

  public static final int ROBTIME_FIELD_NUMBER = 2;
  private long robTime_ = 0L;
  /**
   * <code>optional int64 robTime = 2;</code>
   * @return Whether the robTime field is set.
   */
  @java.lang.Override
  public boolean hasRobTime() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int64 robTime = 2;</code>
   * @return The robTime.
   */
  @java.lang.Override
  public long getRobTime() {
    return robTime_;
  }

  public static final int ROBUNIONNAME_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private volatile java.lang.Object robUnionName_ = "";
  /**
   * <code>optional string robUnionName = 3;</code>
   * @return Whether the robUnionName field is set.
   */
  @java.lang.Override
  public boolean hasRobUnionName() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional string robUnionName = 3;</code>
   * @return The robUnionName.
   */
  @java.lang.Override
  public java.lang.String getRobUnionName() {
    java.lang.Object ref = robUnionName_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        robUnionName_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string robUnionName = 3;</code>
   * @return The bytes for robUnionName.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getRobUnionNameBytes() {
    java.lang.Object ref = robUnionName_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      robUnionName_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, gainScore_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt64(2, robTime_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 3, robUnionName_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, gainScore_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(2, robTime_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(3, robUnionName_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.MonopolyPlayerLogDetailInfo)) {
      return super.equals(obj);
    }
    xddq.pb.MonopolyPlayerLogDetailInfo other = (xddq.pb.MonopolyPlayerLogDetailInfo) obj;

    if (hasGainScore() != other.hasGainScore()) return false;
    if (hasGainScore()) {
      if (getGainScore()
          != other.getGainScore()) return false;
    }
    if (hasRobTime() != other.hasRobTime()) return false;
    if (hasRobTime()) {
      if (getRobTime()
          != other.getRobTime()) return false;
    }
    if (hasRobUnionName() != other.hasRobUnionName()) return false;
    if (hasRobUnionName()) {
      if (!getRobUnionName()
          .equals(other.getRobUnionName())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasGainScore()) {
      hash = (37 * hash) + GAINSCORE_FIELD_NUMBER;
      hash = (53 * hash) + getGainScore();
    }
    if (hasRobTime()) {
      hash = (37 * hash) + ROBTIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getRobTime());
    }
    if (hasRobUnionName()) {
      hash = (37 * hash) + ROBUNIONNAME_FIELD_NUMBER;
      hash = (53 * hash) + getRobUnionName().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.MonopolyPlayerLogDetailInfo parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.MonopolyPlayerLogDetailInfo parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.MonopolyPlayerLogDetailInfo parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.MonopolyPlayerLogDetailInfo parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.MonopolyPlayerLogDetailInfo parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.MonopolyPlayerLogDetailInfo parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.MonopolyPlayerLogDetailInfo parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.MonopolyPlayerLogDetailInfo parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.MonopolyPlayerLogDetailInfo parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.MonopolyPlayerLogDetailInfo parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.MonopolyPlayerLogDetailInfo parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.MonopolyPlayerLogDetailInfo parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.MonopolyPlayerLogDetailInfo prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.MonopolyPlayerLogDetailInfo}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.MonopolyPlayerLogDetailInfo)
      xddq.pb.MonopolyPlayerLogDetailInfoOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_MonopolyPlayerLogDetailInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_MonopolyPlayerLogDetailInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.MonopolyPlayerLogDetailInfo.class, xddq.pb.MonopolyPlayerLogDetailInfo.Builder.class);
    }

    // Construct using xddq.pb.MonopolyPlayerLogDetailInfo.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      gainScore_ = 0;
      robTime_ = 0L;
      robUnionName_ = "";
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_MonopolyPlayerLogDetailInfo_descriptor;
    }

    @java.lang.Override
    public xddq.pb.MonopolyPlayerLogDetailInfo getDefaultInstanceForType() {
      return xddq.pb.MonopolyPlayerLogDetailInfo.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.MonopolyPlayerLogDetailInfo build() {
      xddq.pb.MonopolyPlayerLogDetailInfo result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.MonopolyPlayerLogDetailInfo buildPartial() {
      xddq.pb.MonopolyPlayerLogDetailInfo result = new xddq.pb.MonopolyPlayerLogDetailInfo(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.MonopolyPlayerLogDetailInfo result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.gainScore_ = gainScore_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.robTime_ = robTime_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.robUnionName_ = robUnionName_;
        to_bitField0_ |= 0x00000004;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.MonopolyPlayerLogDetailInfo) {
        return mergeFrom((xddq.pb.MonopolyPlayerLogDetailInfo)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.MonopolyPlayerLogDetailInfo other) {
      if (other == xddq.pb.MonopolyPlayerLogDetailInfo.getDefaultInstance()) return this;
      if (other.hasGainScore()) {
        setGainScore(other.getGainScore());
      }
      if (other.hasRobTime()) {
        setRobTime(other.getRobTime());
      }
      if (other.hasRobUnionName()) {
        robUnionName_ = other.robUnionName_;
        bitField0_ |= 0x00000004;
        onChanged();
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              gainScore_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              robTime_ = input.readInt64();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 26: {
              robUnionName_ = input.readBytes();
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int gainScore_ ;
    /**
     * <code>optional int32 gainScore = 1;</code>
     * @return Whether the gainScore field is set.
     */
    @java.lang.Override
    public boolean hasGainScore() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 gainScore = 1;</code>
     * @return The gainScore.
     */
    @java.lang.Override
    public int getGainScore() {
      return gainScore_;
    }
    /**
     * <code>optional int32 gainScore = 1;</code>
     * @param value The gainScore to set.
     * @return This builder for chaining.
     */
    public Builder setGainScore(int value) {

      gainScore_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 gainScore = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearGainScore() {
      bitField0_ = (bitField0_ & ~0x00000001);
      gainScore_ = 0;
      onChanged();
      return this;
    }

    private long robTime_ ;
    /**
     * <code>optional int64 robTime = 2;</code>
     * @return Whether the robTime field is set.
     */
    @java.lang.Override
    public boolean hasRobTime() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int64 robTime = 2;</code>
     * @return The robTime.
     */
    @java.lang.Override
    public long getRobTime() {
      return robTime_;
    }
    /**
     * <code>optional int64 robTime = 2;</code>
     * @param value The robTime to set.
     * @return This builder for chaining.
     */
    public Builder setRobTime(long value) {

      robTime_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 robTime = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearRobTime() {
      bitField0_ = (bitField0_ & ~0x00000002);
      robTime_ = 0L;
      onChanged();
      return this;
    }

    private java.lang.Object robUnionName_ = "";
    /**
     * <code>optional string robUnionName = 3;</code>
     * @return Whether the robUnionName field is set.
     */
    public boolean hasRobUnionName() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional string robUnionName = 3;</code>
     * @return The robUnionName.
     */
    public java.lang.String getRobUnionName() {
      java.lang.Object ref = robUnionName_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          robUnionName_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string robUnionName = 3;</code>
     * @return The bytes for robUnionName.
     */
    public com.google.protobuf.ByteString
        getRobUnionNameBytes() {
      java.lang.Object ref = robUnionName_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        robUnionName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string robUnionName = 3;</code>
     * @param value The robUnionName to set.
     * @return This builder for chaining.
     */
    public Builder setRobUnionName(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      robUnionName_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional string robUnionName = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearRobUnionName() {
      robUnionName_ = getDefaultInstance().getRobUnionName();
      bitField0_ = (bitField0_ & ~0x00000004);
      onChanged();
      return this;
    }
    /**
     * <code>optional string robUnionName = 3;</code>
     * @param value The bytes for robUnionName to set.
     * @return This builder for chaining.
     */
    public Builder setRobUnionNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      robUnionName_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.MonopolyPlayerLogDetailInfo)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.MonopolyPlayerLogDetailInfo)
  private static final xddq.pb.MonopolyPlayerLogDetailInfo DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.MonopolyPlayerLogDetailInfo();
  }

  public static xddq.pb.MonopolyPlayerLogDetailInfo getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<MonopolyPlayerLogDetailInfo>
      PARSER = new com.google.protobuf.AbstractParser<MonopolyPlayerLogDetailInfo>() {
    @java.lang.Override
    public MonopolyPlayerLogDetailInfo parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<MonopolyPlayerLogDetailInfo> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<MonopolyPlayerLogDetailInfo> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.MonopolyPlayerLogDetailInfo getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

