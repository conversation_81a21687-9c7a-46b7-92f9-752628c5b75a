// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

public interface PlanesTrialEnterSwitchSeparationRspOrBuilder extends
    // @@protoc_insertion_point(interface_extends:xddq.pb.PlanesTrialEnterSwitchSeparationRsp)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  boolean hasRet();
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  int getRet();

  /**
   * <code>repeated .xddq.pb.PlanesTrialSeparationSimpleData datas = 2;</code>
   */
  java.util.List<xddq.pb.PlanesTrialSeparationSimpleData> 
      getDatasList();
  /**
   * <code>repeated .xddq.pb.PlanesTrialSeparationSimpleData datas = 2;</code>
   */
  xddq.pb.PlanesTrialSeparationSimpleData getDatas(int index);
  /**
   * <code>repeated .xddq.pb.PlanesTrialSeparationSimpleData datas = 2;</code>
   */
  int getDatasCount();
  /**
   * <code>repeated .xddq.pb.PlanesTrialSeparationSimpleData datas = 2;</code>
   */
  java.util.List<? extends xddq.pb.PlanesTrialSeparationSimpleDataOrBuilder> 
      getDatasOrBuilderList();
  /**
   * <code>repeated .xddq.pb.PlanesTrialSeparationSimpleData datas = 2;</code>
   */
  xddq.pb.PlanesTrialSeparationSimpleDataOrBuilder getDatasOrBuilder(
      int index);
}
