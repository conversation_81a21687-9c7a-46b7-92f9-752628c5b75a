// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.PupilRogueCharaConfigMsg}
 */
public final class PupilRogueCharaConfigMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.PupilRogueCharaConfigMsg)
    PupilRogueCharaConfigMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      PupilRogueCharaConfigMsg.class.getName());
  }
  // Use PupilRogueCharaConfigMsg.newBuilder() to construct.
  private PupilRogueCharaConfigMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private PupilRogueCharaConfigMsg() {
    appearanceId_ = "";
    icon_ = "";
    attackHoldType_ = "";
    act_ = "";
    hit_ = "";
    attackHitExcursion_ = "";
    name_ = "";
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_PupilRogueCharaConfigMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_PupilRogueCharaConfigMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.PupilRogueCharaConfigMsg.class, xddq.pb.PupilRogueCharaConfigMsg.Builder.class);
  }

  private int bitField0_;
  public static final int ID_FIELD_NUMBER = 1;
  private int id_ = 0;
  /**
   * <code>required int32 id = 1;</code>
   * @return Whether the id field is set.
   */
  @java.lang.Override
  public boolean hasId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 id = 1;</code>
   * @return The id.
   */
  @java.lang.Override
  public int getId() {
    return id_;
  }

  public static final int APPEARANCEID_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private volatile java.lang.Object appearanceId_ = "";
  /**
   * <code>required string appearanceId = 2;</code>
   * @return Whether the appearanceId field is set.
   */
  @java.lang.Override
  public boolean hasAppearanceId() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>required string appearanceId = 2;</code>
   * @return The appearanceId.
   */
  @java.lang.Override
  public java.lang.String getAppearanceId() {
    java.lang.Object ref = appearanceId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        appearanceId_ = s;
      }
      return s;
    }
  }
  /**
   * <code>required string appearanceId = 2;</code>
   * @return The bytes for appearanceId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getAppearanceIdBytes() {
    java.lang.Object ref = appearanceId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      appearanceId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ICON_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private volatile java.lang.Object icon_ = "";
  /**
   * <code>required string icon = 3;</code>
   * @return Whether the icon field is set.
   */
  @java.lang.Override
  public boolean hasIcon() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>required string icon = 3;</code>
   * @return The icon.
   */
  @java.lang.Override
  public java.lang.String getIcon() {
    java.lang.Object ref = icon_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        icon_ = s;
      }
      return s;
    }
  }
  /**
   * <code>required string icon = 3;</code>
   * @return The bytes for icon.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getIconBytes() {
    java.lang.Object ref = icon_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      icon_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ATTACKHOLDTYPE_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private volatile java.lang.Object attackHoldType_ = "";
  /**
   * <code>required string attackHoldType = 4;</code>
   * @return Whether the attackHoldType field is set.
   */
  @java.lang.Override
  public boolean hasAttackHoldType() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>required string attackHoldType = 4;</code>
   * @return The attackHoldType.
   */
  @java.lang.Override
  public java.lang.String getAttackHoldType() {
    java.lang.Object ref = attackHoldType_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        attackHoldType_ = s;
      }
      return s;
    }
  }
  /**
   * <code>required string attackHoldType = 4;</code>
   * @return The bytes for attackHoldType.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getAttackHoldTypeBytes() {
    java.lang.Object ref = attackHoldType_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      attackHoldType_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ACT_FIELD_NUMBER = 5;
  @SuppressWarnings("serial")
  private volatile java.lang.Object act_ = "";
  /**
   * <code>required string act = 5;</code>
   * @return Whether the act field is set.
   */
  @java.lang.Override
  public boolean hasAct() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>required string act = 5;</code>
   * @return The act.
   */
  @java.lang.Override
  public java.lang.String getAct() {
    java.lang.Object ref = act_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        act_ = s;
      }
      return s;
    }
  }
  /**
   * <code>required string act = 5;</code>
   * @return The bytes for act.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getActBytes() {
    java.lang.Object ref = act_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      act_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int HIT_FIELD_NUMBER = 6;
  @SuppressWarnings("serial")
  private volatile java.lang.Object hit_ = "";
  /**
   * <code>required string hit = 6;</code>
   * @return Whether the hit field is set.
   */
  @java.lang.Override
  public boolean hasHit() {
    return ((bitField0_ & 0x00000020) != 0);
  }
  /**
   * <code>required string hit = 6;</code>
   * @return The hit.
   */
  @java.lang.Override
  public java.lang.String getHit() {
    java.lang.Object ref = hit_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        hit_ = s;
      }
      return s;
    }
  }
  /**
   * <code>required string hit = 6;</code>
   * @return The bytes for hit.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getHitBytes() {
    java.lang.Object ref = hit_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      hit_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ATTACKHITEXCURSION_FIELD_NUMBER = 7;
  @SuppressWarnings("serial")
  private volatile java.lang.Object attackHitExcursion_ = "";
  /**
   * <code>required string attackHitExcursion = 7;</code>
   * @return Whether the attackHitExcursion field is set.
   */
  @java.lang.Override
  public boolean hasAttackHitExcursion() {
    return ((bitField0_ & 0x00000040) != 0);
  }
  /**
   * <code>required string attackHitExcursion = 7;</code>
   * @return The attackHitExcursion.
   */
  @java.lang.Override
  public java.lang.String getAttackHitExcursion() {
    java.lang.Object ref = attackHitExcursion_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        attackHitExcursion_ = s;
      }
      return s;
    }
  }
  /**
   * <code>required string attackHitExcursion = 7;</code>
   * @return The bytes for attackHitExcursion.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getAttackHitExcursionBytes() {
    java.lang.Object ref = attackHitExcursion_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      attackHitExcursion_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int BODYHEIGHT_FIELD_NUMBER = 8;
  private int bodyHeight_ = 0;
  /**
   * <code>required int32 bodyHeight = 8;</code>
   * @return Whether the bodyHeight field is set.
   */
  @java.lang.Override
  public boolean hasBodyHeight() {
    return ((bitField0_ & 0x00000080) != 0);
  }
  /**
   * <code>required int32 bodyHeight = 8;</code>
   * @return The bodyHeight.
   */
  @java.lang.Override
  public int getBodyHeight() {
    return bodyHeight_;
  }

  public static final int BODYSCALE_FIELD_NUMBER = 9;
  private int bodyScale_ = 0;
  /**
   * <code>required int32 bodyScale = 9;</code>
   * @return Whether the bodyScale field is set.
   */
  @java.lang.Override
  public boolean hasBodyScale() {
    return ((bitField0_ & 0x00000100) != 0);
  }
  /**
   * <code>required int32 bodyScale = 9;</code>
   * @return The bodyScale.
   */
  @java.lang.Override
  public int getBodyScale() {
    return bodyScale_;
  }

  public static final int ATTACKBODYSCALE_FIELD_NUMBER = 10;
  private int attackBodyScale_ = 0;
  /**
   * <code>required int32 attackBodyScale = 10;</code>
   * @return Whether the attackBodyScale field is set.
   */
  @java.lang.Override
  public boolean hasAttackBodyScale() {
    return ((bitField0_ & 0x00000200) != 0);
  }
  /**
   * <code>required int32 attackBodyScale = 10;</code>
   * @return The attackBodyScale.
   */
  @java.lang.Override
  public int getAttackBodyScale() {
    return attackBodyScale_;
  }

  public static final int SKILLEFFECT_FIELD_NUMBER = 11;
  private int skillEffect_ = 0;
  /**
   * <code>required int32 skillEffect = 11;</code>
   * @return Whether the skillEffect field is set.
   */
  @java.lang.Override
  public boolean hasSkillEffect() {
    return ((bitField0_ & 0x00000400) != 0);
  }
  /**
   * <code>required int32 skillEffect = 11;</code>
   * @return The skillEffect.
   */
  @java.lang.Override
  public int getSkillEffect() {
    return skillEffect_;
  }

  public static final int NAME_FIELD_NUMBER = 12;
  @SuppressWarnings("serial")
  private volatile java.lang.Object name_ = "";
  /**
   * <code>required string name = 12;</code>
   * @return Whether the name field is set.
   */
  @java.lang.Override
  public boolean hasName() {
    return ((bitField0_ & 0x00000800) != 0);
  }
  /**
   * <code>required string name = 12;</code>
   * @return The name.
   */
  @java.lang.Override
  public java.lang.String getName() {
    java.lang.Object ref = name_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        name_ = s;
      }
      return s;
    }
  }
  /**
   * <code>required string name = 12;</code>
   * @return The bytes for name.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getNameBytes() {
    java.lang.Object ref = name_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      name_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasId()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasAppearanceId()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasIcon()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasAttackHoldType()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasAct()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasHit()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasAttackHitExcursion()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasBodyHeight()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasBodyScale()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasAttackBodyScale()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasSkillEffect()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasName()) {
      memoizedIsInitialized = 0;
      return false;
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, id_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 2, appearanceId_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 3, icon_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 4, attackHoldType_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 5, act_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 6, hit_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 7, attackHitExcursion_);
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      output.writeInt32(8, bodyHeight_);
    }
    if (((bitField0_ & 0x00000100) != 0)) {
      output.writeInt32(9, bodyScale_);
    }
    if (((bitField0_ & 0x00000200) != 0)) {
      output.writeInt32(10, attackBodyScale_);
    }
    if (((bitField0_ & 0x00000400) != 0)) {
      output.writeInt32(11, skillEffect_);
    }
    if (((bitField0_ & 0x00000800) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 12, name_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, id_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(2, appearanceId_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(3, icon_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(4, attackHoldType_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(5, act_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(6, hit_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(7, attackHitExcursion_);
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(8, bodyHeight_);
    }
    if (((bitField0_ & 0x00000100) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(9, bodyScale_);
    }
    if (((bitField0_ & 0x00000200) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(10, attackBodyScale_);
    }
    if (((bitField0_ & 0x00000400) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(11, skillEffect_);
    }
    if (((bitField0_ & 0x00000800) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(12, name_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.PupilRogueCharaConfigMsg)) {
      return super.equals(obj);
    }
    xddq.pb.PupilRogueCharaConfigMsg other = (xddq.pb.PupilRogueCharaConfigMsg) obj;

    if (hasId() != other.hasId()) return false;
    if (hasId()) {
      if (getId()
          != other.getId()) return false;
    }
    if (hasAppearanceId() != other.hasAppearanceId()) return false;
    if (hasAppearanceId()) {
      if (!getAppearanceId()
          .equals(other.getAppearanceId())) return false;
    }
    if (hasIcon() != other.hasIcon()) return false;
    if (hasIcon()) {
      if (!getIcon()
          .equals(other.getIcon())) return false;
    }
    if (hasAttackHoldType() != other.hasAttackHoldType()) return false;
    if (hasAttackHoldType()) {
      if (!getAttackHoldType()
          .equals(other.getAttackHoldType())) return false;
    }
    if (hasAct() != other.hasAct()) return false;
    if (hasAct()) {
      if (!getAct()
          .equals(other.getAct())) return false;
    }
    if (hasHit() != other.hasHit()) return false;
    if (hasHit()) {
      if (!getHit()
          .equals(other.getHit())) return false;
    }
    if (hasAttackHitExcursion() != other.hasAttackHitExcursion()) return false;
    if (hasAttackHitExcursion()) {
      if (!getAttackHitExcursion()
          .equals(other.getAttackHitExcursion())) return false;
    }
    if (hasBodyHeight() != other.hasBodyHeight()) return false;
    if (hasBodyHeight()) {
      if (getBodyHeight()
          != other.getBodyHeight()) return false;
    }
    if (hasBodyScale() != other.hasBodyScale()) return false;
    if (hasBodyScale()) {
      if (getBodyScale()
          != other.getBodyScale()) return false;
    }
    if (hasAttackBodyScale() != other.hasAttackBodyScale()) return false;
    if (hasAttackBodyScale()) {
      if (getAttackBodyScale()
          != other.getAttackBodyScale()) return false;
    }
    if (hasSkillEffect() != other.hasSkillEffect()) return false;
    if (hasSkillEffect()) {
      if (getSkillEffect()
          != other.getSkillEffect()) return false;
    }
    if (hasName() != other.hasName()) return false;
    if (hasName()) {
      if (!getName()
          .equals(other.getName())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasId()) {
      hash = (37 * hash) + ID_FIELD_NUMBER;
      hash = (53 * hash) + getId();
    }
    if (hasAppearanceId()) {
      hash = (37 * hash) + APPEARANCEID_FIELD_NUMBER;
      hash = (53 * hash) + getAppearanceId().hashCode();
    }
    if (hasIcon()) {
      hash = (37 * hash) + ICON_FIELD_NUMBER;
      hash = (53 * hash) + getIcon().hashCode();
    }
    if (hasAttackHoldType()) {
      hash = (37 * hash) + ATTACKHOLDTYPE_FIELD_NUMBER;
      hash = (53 * hash) + getAttackHoldType().hashCode();
    }
    if (hasAct()) {
      hash = (37 * hash) + ACT_FIELD_NUMBER;
      hash = (53 * hash) + getAct().hashCode();
    }
    if (hasHit()) {
      hash = (37 * hash) + HIT_FIELD_NUMBER;
      hash = (53 * hash) + getHit().hashCode();
    }
    if (hasAttackHitExcursion()) {
      hash = (37 * hash) + ATTACKHITEXCURSION_FIELD_NUMBER;
      hash = (53 * hash) + getAttackHitExcursion().hashCode();
    }
    if (hasBodyHeight()) {
      hash = (37 * hash) + BODYHEIGHT_FIELD_NUMBER;
      hash = (53 * hash) + getBodyHeight();
    }
    if (hasBodyScale()) {
      hash = (37 * hash) + BODYSCALE_FIELD_NUMBER;
      hash = (53 * hash) + getBodyScale();
    }
    if (hasAttackBodyScale()) {
      hash = (37 * hash) + ATTACKBODYSCALE_FIELD_NUMBER;
      hash = (53 * hash) + getAttackBodyScale();
    }
    if (hasSkillEffect()) {
      hash = (37 * hash) + SKILLEFFECT_FIELD_NUMBER;
      hash = (53 * hash) + getSkillEffect();
    }
    if (hasName()) {
      hash = (37 * hash) + NAME_FIELD_NUMBER;
      hash = (53 * hash) + getName().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.PupilRogueCharaConfigMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PupilRogueCharaConfigMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PupilRogueCharaConfigMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PupilRogueCharaConfigMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PupilRogueCharaConfigMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PupilRogueCharaConfigMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PupilRogueCharaConfigMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.PupilRogueCharaConfigMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.PupilRogueCharaConfigMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.PupilRogueCharaConfigMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.PupilRogueCharaConfigMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.PupilRogueCharaConfigMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.PupilRogueCharaConfigMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.PupilRogueCharaConfigMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.PupilRogueCharaConfigMsg)
      xddq.pb.PupilRogueCharaConfigMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PupilRogueCharaConfigMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PupilRogueCharaConfigMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.PupilRogueCharaConfigMsg.class, xddq.pb.PupilRogueCharaConfigMsg.Builder.class);
    }

    // Construct using xddq.pb.PupilRogueCharaConfigMsg.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      id_ = 0;
      appearanceId_ = "";
      icon_ = "";
      attackHoldType_ = "";
      act_ = "";
      hit_ = "";
      attackHitExcursion_ = "";
      bodyHeight_ = 0;
      bodyScale_ = 0;
      attackBodyScale_ = 0;
      skillEffect_ = 0;
      name_ = "";
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PupilRogueCharaConfigMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.PupilRogueCharaConfigMsg getDefaultInstanceForType() {
      return xddq.pb.PupilRogueCharaConfigMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.PupilRogueCharaConfigMsg build() {
      xddq.pb.PupilRogueCharaConfigMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.PupilRogueCharaConfigMsg buildPartial() {
      xddq.pb.PupilRogueCharaConfigMsg result = new xddq.pb.PupilRogueCharaConfigMsg(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.PupilRogueCharaConfigMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.id_ = id_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.appearanceId_ = appearanceId_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.icon_ = icon_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.attackHoldType_ = attackHoldType_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.act_ = act_;
        to_bitField0_ |= 0x00000010;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.hit_ = hit_;
        to_bitField0_ |= 0x00000020;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.attackHitExcursion_ = attackHitExcursion_;
        to_bitField0_ |= 0x00000040;
      }
      if (((from_bitField0_ & 0x00000080) != 0)) {
        result.bodyHeight_ = bodyHeight_;
        to_bitField0_ |= 0x00000080;
      }
      if (((from_bitField0_ & 0x00000100) != 0)) {
        result.bodyScale_ = bodyScale_;
        to_bitField0_ |= 0x00000100;
      }
      if (((from_bitField0_ & 0x00000200) != 0)) {
        result.attackBodyScale_ = attackBodyScale_;
        to_bitField0_ |= 0x00000200;
      }
      if (((from_bitField0_ & 0x00000400) != 0)) {
        result.skillEffect_ = skillEffect_;
        to_bitField0_ |= 0x00000400;
      }
      if (((from_bitField0_ & 0x00000800) != 0)) {
        result.name_ = name_;
        to_bitField0_ |= 0x00000800;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.PupilRogueCharaConfigMsg) {
        return mergeFrom((xddq.pb.PupilRogueCharaConfigMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.PupilRogueCharaConfigMsg other) {
      if (other == xddq.pb.PupilRogueCharaConfigMsg.getDefaultInstance()) return this;
      if (other.hasId()) {
        setId(other.getId());
      }
      if (other.hasAppearanceId()) {
        appearanceId_ = other.appearanceId_;
        bitField0_ |= 0x00000002;
        onChanged();
      }
      if (other.hasIcon()) {
        icon_ = other.icon_;
        bitField0_ |= 0x00000004;
        onChanged();
      }
      if (other.hasAttackHoldType()) {
        attackHoldType_ = other.attackHoldType_;
        bitField0_ |= 0x00000008;
        onChanged();
      }
      if (other.hasAct()) {
        act_ = other.act_;
        bitField0_ |= 0x00000010;
        onChanged();
      }
      if (other.hasHit()) {
        hit_ = other.hit_;
        bitField0_ |= 0x00000020;
        onChanged();
      }
      if (other.hasAttackHitExcursion()) {
        attackHitExcursion_ = other.attackHitExcursion_;
        bitField0_ |= 0x00000040;
        onChanged();
      }
      if (other.hasBodyHeight()) {
        setBodyHeight(other.getBodyHeight());
      }
      if (other.hasBodyScale()) {
        setBodyScale(other.getBodyScale());
      }
      if (other.hasAttackBodyScale()) {
        setAttackBodyScale(other.getAttackBodyScale());
      }
      if (other.hasSkillEffect()) {
        setSkillEffect(other.getSkillEffect());
      }
      if (other.hasName()) {
        name_ = other.name_;
        bitField0_ |= 0x00000800;
        onChanged();
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasId()) {
        return false;
      }
      if (!hasAppearanceId()) {
        return false;
      }
      if (!hasIcon()) {
        return false;
      }
      if (!hasAttackHoldType()) {
        return false;
      }
      if (!hasAct()) {
        return false;
      }
      if (!hasHit()) {
        return false;
      }
      if (!hasAttackHitExcursion()) {
        return false;
      }
      if (!hasBodyHeight()) {
        return false;
      }
      if (!hasBodyScale()) {
        return false;
      }
      if (!hasAttackBodyScale()) {
        return false;
      }
      if (!hasSkillEffect()) {
        return false;
      }
      if (!hasName()) {
        return false;
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              id_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              appearanceId_ = input.readBytes();
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 26: {
              icon_ = input.readBytes();
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 34: {
              attackHoldType_ = input.readBytes();
              bitField0_ |= 0x00000008;
              break;
            } // case 34
            case 42: {
              act_ = input.readBytes();
              bitField0_ |= 0x00000010;
              break;
            } // case 42
            case 50: {
              hit_ = input.readBytes();
              bitField0_ |= 0x00000020;
              break;
            } // case 50
            case 58: {
              attackHitExcursion_ = input.readBytes();
              bitField0_ |= 0x00000040;
              break;
            } // case 58
            case 64: {
              bodyHeight_ = input.readInt32();
              bitField0_ |= 0x00000080;
              break;
            } // case 64
            case 72: {
              bodyScale_ = input.readInt32();
              bitField0_ |= 0x00000100;
              break;
            } // case 72
            case 80: {
              attackBodyScale_ = input.readInt32();
              bitField0_ |= 0x00000200;
              break;
            } // case 80
            case 88: {
              skillEffect_ = input.readInt32();
              bitField0_ |= 0x00000400;
              break;
            } // case 88
            case 98: {
              name_ = input.readBytes();
              bitField0_ |= 0x00000800;
              break;
            } // case 98
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int id_ ;
    /**
     * <code>required int32 id = 1;</code>
     * @return Whether the id field is set.
     */
    @java.lang.Override
    public boolean hasId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 id = 1;</code>
     * @return The id.
     */
    @java.lang.Override
    public int getId() {
      return id_;
    }
    /**
     * <code>required int32 id = 1;</code>
     * @param value The id to set.
     * @return This builder for chaining.
     */
    public Builder setId(int value) {

      id_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 id = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      id_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object appearanceId_ = "";
    /**
     * <code>required string appearanceId = 2;</code>
     * @return Whether the appearanceId field is set.
     */
    public boolean hasAppearanceId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>required string appearanceId = 2;</code>
     * @return The appearanceId.
     */
    public java.lang.String getAppearanceId() {
      java.lang.Object ref = appearanceId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          appearanceId_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>required string appearanceId = 2;</code>
     * @return The bytes for appearanceId.
     */
    public com.google.protobuf.ByteString
        getAppearanceIdBytes() {
      java.lang.Object ref = appearanceId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        appearanceId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>required string appearanceId = 2;</code>
     * @param value The appearanceId to set.
     * @return This builder for chaining.
     */
    public Builder setAppearanceId(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      appearanceId_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>required string appearanceId = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearAppearanceId() {
      appearanceId_ = getDefaultInstance().getAppearanceId();
      bitField0_ = (bitField0_ & ~0x00000002);
      onChanged();
      return this;
    }
    /**
     * <code>required string appearanceId = 2;</code>
     * @param value The bytes for appearanceId to set.
     * @return This builder for chaining.
     */
    public Builder setAppearanceIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      appearanceId_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }

    private java.lang.Object icon_ = "";
    /**
     * <code>required string icon = 3;</code>
     * @return Whether the icon field is set.
     */
    public boolean hasIcon() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>required string icon = 3;</code>
     * @return The icon.
     */
    public java.lang.String getIcon() {
      java.lang.Object ref = icon_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          icon_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>required string icon = 3;</code>
     * @return The bytes for icon.
     */
    public com.google.protobuf.ByteString
        getIconBytes() {
      java.lang.Object ref = icon_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        icon_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>required string icon = 3;</code>
     * @param value The icon to set.
     * @return This builder for chaining.
     */
    public Builder setIcon(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      icon_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>required string icon = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearIcon() {
      icon_ = getDefaultInstance().getIcon();
      bitField0_ = (bitField0_ & ~0x00000004);
      onChanged();
      return this;
    }
    /**
     * <code>required string icon = 3;</code>
     * @param value The bytes for icon to set.
     * @return This builder for chaining.
     */
    public Builder setIconBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      icon_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }

    private java.lang.Object attackHoldType_ = "";
    /**
     * <code>required string attackHoldType = 4;</code>
     * @return Whether the attackHoldType field is set.
     */
    public boolean hasAttackHoldType() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>required string attackHoldType = 4;</code>
     * @return The attackHoldType.
     */
    public java.lang.String getAttackHoldType() {
      java.lang.Object ref = attackHoldType_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          attackHoldType_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>required string attackHoldType = 4;</code>
     * @return The bytes for attackHoldType.
     */
    public com.google.protobuf.ByteString
        getAttackHoldTypeBytes() {
      java.lang.Object ref = attackHoldType_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        attackHoldType_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>required string attackHoldType = 4;</code>
     * @param value The attackHoldType to set.
     * @return This builder for chaining.
     */
    public Builder setAttackHoldType(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      attackHoldType_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>required string attackHoldType = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearAttackHoldType() {
      attackHoldType_ = getDefaultInstance().getAttackHoldType();
      bitField0_ = (bitField0_ & ~0x00000008);
      onChanged();
      return this;
    }
    /**
     * <code>required string attackHoldType = 4;</code>
     * @param value The bytes for attackHoldType to set.
     * @return This builder for chaining.
     */
    public Builder setAttackHoldTypeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      attackHoldType_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }

    private java.lang.Object act_ = "";
    /**
     * <code>required string act = 5;</code>
     * @return Whether the act field is set.
     */
    public boolean hasAct() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>required string act = 5;</code>
     * @return The act.
     */
    public java.lang.String getAct() {
      java.lang.Object ref = act_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          act_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>required string act = 5;</code>
     * @return The bytes for act.
     */
    public com.google.protobuf.ByteString
        getActBytes() {
      java.lang.Object ref = act_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        act_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>required string act = 5;</code>
     * @param value The act to set.
     * @return This builder for chaining.
     */
    public Builder setAct(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      act_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>required string act = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearAct() {
      act_ = getDefaultInstance().getAct();
      bitField0_ = (bitField0_ & ~0x00000010);
      onChanged();
      return this;
    }
    /**
     * <code>required string act = 5;</code>
     * @param value The bytes for act to set.
     * @return This builder for chaining.
     */
    public Builder setActBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      act_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }

    private java.lang.Object hit_ = "";
    /**
     * <code>required string hit = 6;</code>
     * @return Whether the hit field is set.
     */
    public boolean hasHit() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>required string hit = 6;</code>
     * @return The hit.
     */
    public java.lang.String getHit() {
      java.lang.Object ref = hit_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          hit_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>required string hit = 6;</code>
     * @return The bytes for hit.
     */
    public com.google.protobuf.ByteString
        getHitBytes() {
      java.lang.Object ref = hit_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        hit_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>required string hit = 6;</code>
     * @param value The hit to set.
     * @return This builder for chaining.
     */
    public Builder setHit(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      hit_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>required string hit = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearHit() {
      hit_ = getDefaultInstance().getHit();
      bitField0_ = (bitField0_ & ~0x00000020);
      onChanged();
      return this;
    }
    /**
     * <code>required string hit = 6;</code>
     * @param value The bytes for hit to set.
     * @return This builder for chaining.
     */
    public Builder setHitBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      hit_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }

    private java.lang.Object attackHitExcursion_ = "";
    /**
     * <code>required string attackHitExcursion = 7;</code>
     * @return Whether the attackHitExcursion field is set.
     */
    public boolean hasAttackHitExcursion() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <code>required string attackHitExcursion = 7;</code>
     * @return The attackHitExcursion.
     */
    public java.lang.String getAttackHitExcursion() {
      java.lang.Object ref = attackHitExcursion_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          attackHitExcursion_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>required string attackHitExcursion = 7;</code>
     * @return The bytes for attackHitExcursion.
     */
    public com.google.protobuf.ByteString
        getAttackHitExcursionBytes() {
      java.lang.Object ref = attackHitExcursion_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        attackHitExcursion_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>required string attackHitExcursion = 7;</code>
     * @param value The attackHitExcursion to set.
     * @return This builder for chaining.
     */
    public Builder setAttackHitExcursion(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      attackHitExcursion_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>required string attackHitExcursion = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearAttackHitExcursion() {
      attackHitExcursion_ = getDefaultInstance().getAttackHitExcursion();
      bitField0_ = (bitField0_ & ~0x00000040);
      onChanged();
      return this;
    }
    /**
     * <code>required string attackHitExcursion = 7;</code>
     * @param value The bytes for attackHitExcursion to set.
     * @return This builder for chaining.
     */
    public Builder setAttackHitExcursionBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      attackHitExcursion_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }

    private int bodyHeight_ ;
    /**
     * <code>required int32 bodyHeight = 8;</code>
     * @return Whether the bodyHeight field is set.
     */
    @java.lang.Override
    public boolean hasBodyHeight() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <code>required int32 bodyHeight = 8;</code>
     * @return The bodyHeight.
     */
    @java.lang.Override
    public int getBodyHeight() {
      return bodyHeight_;
    }
    /**
     * <code>required int32 bodyHeight = 8;</code>
     * @param value The bodyHeight to set.
     * @return This builder for chaining.
     */
    public Builder setBodyHeight(int value) {

      bodyHeight_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 bodyHeight = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearBodyHeight() {
      bitField0_ = (bitField0_ & ~0x00000080);
      bodyHeight_ = 0;
      onChanged();
      return this;
    }

    private int bodyScale_ ;
    /**
     * <code>required int32 bodyScale = 9;</code>
     * @return Whether the bodyScale field is set.
     */
    @java.lang.Override
    public boolean hasBodyScale() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <code>required int32 bodyScale = 9;</code>
     * @return The bodyScale.
     */
    @java.lang.Override
    public int getBodyScale() {
      return bodyScale_;
    }
    /**
     * <code>required int32 bodyScale = 9;</code>
     * @param value The bodyScale to set.
     * @return This builder for chaining.
     */
    public Builder setBodyScale(int value) {

      bodyScale_ = value;
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 bodyScale = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearBodyScale() {
      bitField0_ = (bitField0_ & ~0x00000100);
      bodyScale_ = 0;
      onChanged();
      return this;
    }

    private int attackBodyScale_ ;
    /**
     * <code>required int32 attackBodyScale = 10;</code>
     * @return Whether the attackBodyScale field is set.
     */
    @java.lang.Override
    public boolean hasAttackBodyScale() {
      return ((bitField0_ & 0x00000200) != 0);
    }
    /**
     * <code>required int32 attackBodyScale = 10;</code>
     * @return The attackBodyScale.
     */
    @java.lang.Override
    public int getAttackBodyScale() {
      return attackBodyScale_;
    }
    /**
     * <code>required int32 attackBodyScale = 10;</code>
     * @param value The attackBodyScale to set.
     * @return This builder for chaining.
     */
    public Builder setAttackBodyScale(int value) {

      attackBodyScale_ = value;
      bitField0_ |= 0x00000200;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 attackBodyScale = 10;</code>
     * @return This builder for chaining.
     */
    public Builder clearAttackBodyScale() {
      bitField0_ = (bitField0_ & ~0x00000200);
      attackBodyScale_ = 0;
      onChanged();
      return this;
    }

    private int skillEffect_ ;
    /**
     * <code>required int32 skillEffect = 11;</code>
     * @return Whether the skillEffect field is set.
     */
    @java.lang.Override
    public boolean hasSkillEffect() {
      return ((bitField0_ & 0x00000400) != 0);
    }
    /**
     * <code>required int32 skillEffect = 11;</code>
     * @return The skillEffect.
     */
    @java.lang.Override
    public int getSkillEffect() {
      return skillEffect_;
    }
    /**
     * <code>required int32 skillEffect = 11;</code>
     * @param value The skillEffect to set.
     * @return This builder for chaining.
     */
    public Builder setSkillEffect(int value) {

      skillEffect_ = value;
      bitField0_ |= 0x00000400;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 skillEffect = 11;</code>
     * @return This builder for chaining.
     */
    public Builder clearSkillEffect() {
      bitField0_ = (bitField0_ & ~0x00000400);
      skillEffect_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object name_ = "";
    /**
     * <code>required string name = 12;</code>
     * @return Whether the name field is set.
     */
    public boolean hasName() {
      return ((bitField0_ & 0x00000800) != 0);
    }
    /**
     * <code>required string name = 12;</code>
     * @return The name.
     */
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          name_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>required string name = 12;</code>
     * @return The bytes for name.
     */
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>required string name = 12;</code>
     * @param value The name to set.
     * @return This builder for chaining.
     */
    public Builder setName(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      name_ = value;
      bitField0_ |= 0x00000800;
      onChanged();
      return this;
    }
    /**
     * <code>required string name = 12;</code>
     * @return This builder for chaining.
     */
    public Builder clearName() {
      name_ = getDefaultInstance().getName();
      bitField0_ = (bitField0_ & ~0x00000800);
      onChanged();
      return this;
    }
    /**
     * <code>required string name = 12;</code>
     * @param value The bytes for name to set.
     * @return This builder for chaining.
     */
    public Builder setNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      name_ = value;
      bitField0_ |= 0x00000800;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.PupilRogueCharaConfigMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.PupilRogueCharaConfigMsg)
  private static final xddq.pb.PupilRogueCharaConfigMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.PupilRogueCharaConfigMsg();
  }

  public static xddq.pb.PupilRogueCharaConfigMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<PupilRogueCharaConfigMsg>
      PARSER = new com.google.protobuf.AbstractParser<PupilRogueCharaConfigMsg>() {
    @java.lang.Override
    public PupilRogueCharaConfigMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<PupilRogueCharaConfigMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<PupilRogueCharaConfigMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.PupilRogueCharaConfigMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

