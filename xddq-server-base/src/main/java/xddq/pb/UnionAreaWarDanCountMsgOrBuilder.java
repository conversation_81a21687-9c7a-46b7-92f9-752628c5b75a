// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

public interface UnionAreaWarDanCountMsgOrBuilder extends
    // @@protoc_insertion_point(interface_extends:xddq.pb.UnionAreaWarDanCountMsg)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>optional int32 danId = 1;</code>
   * @return Whether the danId field is set.
   */
  boolean hasDanId();
  /**
   * <code>optional int32 danId = 1;</code>
   * @return The danId.
   */
  int getDanId();

  /**
   * <code>optional int32 groupCount = 2;</code>
   * @return Whether the groupCount field is set.
   */
  boolean hasGroupCount();
  /**
   * <code>optional int32 groupCount = 2;</code>
   * @return The groupCount.
   */
  int getGroupCount();
}
