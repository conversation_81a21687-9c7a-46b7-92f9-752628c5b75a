// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.Pharmacy2UserIdTableRespMsg}
 */
public final class Pharmacy2UserIdTableRespMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.Pharmacy2UserIdTableRespMsg)
    Pharmacy2UserIdTableRespMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      Pharmacy2UserIdTableRespMsg.class.getName());
  }
  // Use Pharmacy2UserIdTableRespMsg.newBuilder() to construct.
  private Pharmacy2UserIdTableRespMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private Pharmacy2UserIdTableRespMsg() {
    tableList_ = java.util.Collections.emptyList();
    forbidSpoonId_ = emptyIntList();
    historyTableList_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_Pharmacy2UserIdTableRespMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_Pharmacy2UserIdTableRespMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.Pharmacy2UserIdTableRespMsg.class, xddq.pb.Pharmacy2UserIdTableRespMsg.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int INTEGRAL_FIELD_NUMBER = 2;
  private long integral_ = 0L;
  /**
   * <code>optional int64 integral = 2;</code>
   * @return Whether the integral field is set.
   */
  @java.lang.Override
  public boolean hasIntegral() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int64 integral = 2;</code>
   * @return The integral.
   */
  @java.lang.Override
  public long getIntegral() {
    return integral_;
  }

  public static final int RANK_FIELD_NUMBER = 3;
  private int rank_ = 0;
  /**
   * <code>optional int32 rank = 3;</code>
   * @return Whether the rank field is set.
   */
  @java.lang.Override
  public boolean hasRank() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional int32 rank = 3;</code>
   * @return The rank.
   */
  @java.lang.Override
  public int getRank() {
    return rank_;
  }

  public static final int DISHNUM_FIELD_NUMBER = 4;
  private int dishNum_ = 0;
  /**
   * <code>optional int32 dishNum = 4;</code>
   * @return Whether the dishNum field is set.
   */
  @java.lang.Override
  public boolean hasDishNum() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional int32 dishNum = 4;</code>
   * @return The dishNum.
   */
  @java.lang.Override
  public int getDishNum() {
    return dishNum_;
  }

  public static final int TABLELIST_FIELD_NUMBER = 5;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.Pharmacy2TableTempMsg> tableList_;
  /**
   * <code>repeated .xddq.pb.Pharmacy2TableTempMsg tableList = 5;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.Pharmacy2TableTempMsg> getTableListList() {
    return tableList_;
  }
  /**
   * <code>repeated .xddq.pb.Pharmacy2TableTempMsg tableList = 5;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.Pharmacy2TableTempMsgOrBuilder> 
      getTableListOrBuilderList() {
    return tableList_;
  }
  /**
   * <code>repeated .xddq.pb.Pharmacy2TableTempMsg tableList = 5;</code>
   */
  @java.lang.Override
  public int getTableListCount() {
    return tableList_.size();
  }
  /**
   * <code>repeated .xddq.pb.Pharmacy2TableTempMsg tableList = 5;</code>
   */
  @java.lang.Override
  public xddq.pb.Pharmacy2TableTempMsg getTableList(int index) {
    return tableList_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.Pharmacy2TableTempMsg tableList = 5;</code>
   */
  @java.lang.Override
  public xddq.pb.Pharmacy2TableTempMsgOrBuilder getTableListOrBuilder(
      int index) {
    return tableList_.get(index);
  }

  public static final int FORBIDSPOONID_FIELD_NUMBER = 6;
  @SuppressWarnings("serial")
  private com.google.protobuf.Internal.IntList forbidSpoonId_ =
      emptyIntList();
  /**
   * <code>repeated int32 forbidSpoonId = 6;</code>
   * @return A list containing the forbidSpoonId.
   */
  @java.lang.Override
  public java.util.List<java.lang.Integer>
      getForbidSpoonIdList() {
    return forbidSpoonId_;
  }
  /**
   * <code>repeated int32 forbidSpoonId = 6;</code>
   * @return The count of forbidSpoonId.
   */
  public int getForbidSpoonIdCount() {
    return forbidSpoonId_.size();
  }
  /**
   * <code>repeated int32 forbidSpoonId = 6;</code>
   * @param index The index of the element to return.
   * @return The forbidSpoonId at the given index.
   */
  public int getForbidSpoonId(int index) {
    return forbidSpoonId_.getInt(index);
  }

  public static final int HISTORYTABLELIST_FIELD_NUMBER = 7;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.Pharmacy2TableTempMsg> historyTableList_;
  /**
   * <code>repeated .xddq.pb.Pharmacy2TableTempMsg historyTableList = 7;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.Pharmacy2TableTempMsg> getHistoryTableListList() {
    return historyTableList_;
  }
  /**
   * <code>repeated .xddq.pb.Pharmacy2TableTempMsg historyTableList = 7;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.Pharmacy2TableTempMsgOrBuilder> 
      getHistoryTableListOrBuilderList() {
    return historyTableList_;
  }
  /**
   * <code>repeated .xddq.pb.Pharmacy2TableTempMsg historyTableList = 7;</code>
   */
  @java.lang.Override
  public int getHistoryTableListCount() {
    return historyTableList_.size();
  }
  /**
   * <code>repeated .xddq.pb.Pharmacy2TableTempMsg historyTableList = 7;</code>
   */
  @java.lang.Override
  public xddq.pb.Pharmacy2TableTempMsg getHistoryTableList(int index) {
    return historyTableList_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.Pharmacy2TableTempMsg historyTableList = 7;</code>
   */
  @java.lang.Override
  public xddq.pb.Pharmacy2TableTempMsgOrBuilder getHistoryTableListOrBuilder(
      int index) {
    return historyTableList_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    for (int i = 0; i < getTableListCount(); i++) {
      if (!getTableList(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    for (int i = 0; i < getHistoryTableListCount(); i++) {
      if (!getHistoryTableList(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt64(2, integral_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt32(3, rank_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt32(4, dishNum_);
    }
    for (int i = 0; i < tableList_.size(); i++) {
      output.writeMessage(5, tableList_.get(i));
    }
    for (int i = 0; i < forbidSpoonId_.size(); i++) {
      output.writeInt32(6, forbidSpoonId_.getInt(i));
    }
    for (int i = 0; i < historyTableList_.size(); i++) {
      output.writeMessage(7, historyTableList_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(2, integral_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(3, rank_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(4, dishNum_);
    }
    for (int i = 0; i < tableList_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(5, tableList_.get(i));
    }
    {
      int dataSize = 0;
      for (int i = 0; i < forbidSpoonId_.size(); i++) {
        dataSize += com.google.protobuf.CodedOutputStream
          .computeInt32SizeNoTag(forbidSpoonId_.getInt(i));
      }
      size += dataSize;
      size += 1 * getForbidSpoonIdList().size();
    }
    for (int i = 0; i < historyTableList_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(7, historyTableList_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.Pharmacy2UserIdTableRespMsg)) {
      return super.equals(obj);
    }
    xddq.pb.Pharmacy2UserIdTableRespMsg other = (xddq.pb.Pharmacy2UserIdTableRespMsg) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (hasIntegral() != other.hasIntegral()) return false;
    if (hasIntegral()) {
      if (getIntegral()
          != other.getIntegral()) return false;
    }
    if (hasRank() != other.hasRank()) return false;
    if (hasRank()) {
      if (getRank()
          != other.getRank()) return false;
    }
    if (hasDishNum() != other.hasDishNum()) return false;
    if (hasDishNum()) {
      if (getDishNum()
          != other.getDishNum()) return false;
    }
    if (!getTableListList()
        .equals(other.getTableListList())) return false;
    if (!getForbidSpoonIdList()
        .equals(other.getForbidSpoonIdList())) return false;
    if (!getHistoryTableListList()
        .equals(other.getHistoryTableListList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (hasIntegral()) {
      hash = (37 * hash) + INTEGRAL_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getIntegral());
    }
    if (hasRank()) {
      hash = (37 * hash) + RANK_FIELD_NUMBER;
      hash = (53 * hash) + getRank();
    }
    if (hasDishNum()) {
      hash = (37 * hash) + DISHNUM_FIELD_NUMBER;
      hash = (53 * hash) + getDishNum();
    }
    if (getTableListCount() > 0) {
      hash = (37 * hash) + TABLELIST_FIELD_NUMBER;
      hash = (53 * hash) + getTableListList().hashCode();
    }
    if (getForbidSpoonIdCount() > 0) {
      hash = (37 * hash) + FORBIDSPOONID_FIELD_NUMBER;
      hash = (53 * hash) + getForbidSpoonIdList().hashCode();
    }
    if (getHistoryTableListCount() > 0) {
      hash = (37 * hash) + HISTORYTABLELIST_FIELD_NUMBER;
      hash = (53 * hash) + getHistoryTableListList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.Pharmacy2UserIdTableRespMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.Pharmacy2UserIdTableRespMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.Pharmacy2UserIdTableRespMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.Pharmacy2UserIdTableRespMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.Pharmacy2UserIdTableRespMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.Pharmacy2UserIdTableRespMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.Pharmacy2UserIdTableRespMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.Pharmacy2UserIdTableRespMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.Pharmacy2UserIdTableRespMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.Pharmacy2UserIdTableRespMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.Pharmacy2UserIdTableRespMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.Pharmacy2UserIdTableRespMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.Pharmacy2UserIdTableRespMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.Pharmacy2UserIdTableRespMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.Pharmacy2UserIdTableRespMsg)
      xddq.pb.Pharmacy2UserIdTableRespMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_Pharmacy2UserIdTableRespMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_Pharmacy2UserIdTableRespMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.Pharmacy2UserIdTableRespMsg.class, xddq.pb.Pharmacy2UserIdTableRespMsg.Builder.class);
    }

    // Construct using xddq.pb.Pharmacy2UserIdTableRespMsg.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      integral_ = 0L;
      rank_ = 0;
      dishNum_ = 0;
      if (tableListBuilder_ == null) {
        tableList_ = java.util.Collections.emptyList();
      } else {
        tableList_ = null;
        tableListBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000010);
      forbidSpoonId_ = emptyIntList();
      if (historyTableListBuilder_ == null) {
        historyTableList_ = java.util.Collections.emptyList();
      } else {
        historyTableList_ = null;
        historyTableListBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000040);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_Pharmacy2UserIdTableRespMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.Pharmacy2UserIdTableRespMsg getDefaultInstanceForType() {
      return xddq.pb.Pharmacy2UserIdTableRespMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.Pharmacy2UserIdTableRespMsg build() {
      xddq.pb.Pharmacy2UserIdTableRespMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.Pharmacy2UserIdTableRespMsg buildPartial() {
      xddq.pb.Pharmacy2UserIdTableRespMsg result = new xddq.pb.Pharmacy2UserIdTableRespMsg(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.Pharmacy2UserIdTableRespMsg result) {
      if (tableListBuilder_ == null) {
        if (((bitField0_ & 0x00000010) != 0)) {
          tableList_ = java.util.Collections.unmodifiableList(tableList_);
          bitField0_ = (bitField0_ & ~0x00000010);
        }
        result.tableList_ = tableList_;
      } else {
        result.tableList_ = tableListBuilder_.build();
      }
      if (historyTableListBuilder_ == null) {
        if (((bitField0_ & 0x00000040) != 0)) {
          historyTableList_ = java.util.Collections.unmodifiableList(historyTableList_);
          bitField0_ = (bitField0_ & ~0x00000040);
        }
        result.historyTableList_ = historyTableList_;
      } else {
        result.historyTableList_ = historyTableListBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.Pharmacy2UserIdTableRespMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.integral_ = integral_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.rank_ = rank_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.dishNum_ = dishNum_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        forbidSpoonId_.makeImmutable();
        result.forbidSpoonId_ = forbidSpoonId_;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.Pharmacy2UserIdTableRespMsg) {
        return mergeFrom((xddq.pb.Pharmacy2UserIdTableRespMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.Pharmacy2UserIdTableRespMsg other) {
      if (other == xddq.pb.Pharmacy2UserIdTableRespMsg.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (other.hasIntegral()) {
        setIntegral(other.getIntegral());
      }
      if (other.hasRank()) {
        setRank(other.getRank());
      }
      if (other.hasDishNum()) {
        setDishNum(other.getDishNum());
      }
      if (tableListBuilder_ == null) {
        if (!other.tableList_.isEmpty()) {
          if (tableList_.isEmpty()) {
            tableList_ = other.tableList_;
            bitField0_ = (bitField0_ & ~0x00000010);
          } else {
            ensureTableListIsMutable();
            tableList_.addAll(other.tableList_);
          }
          onChanged();
        }
      } else {
        if (!other.tableList_.isEmpty()) {
          if (tableListBuilder_.isEmpty()) {
            tableListBuilder_.dispose();
            tableListBuilder_ = null;
            tableList_ = other.tableList_;
            bitField0_ = (bitField0_ & ~0x00000010);
            tableListBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetTableListFieldBuilder() : null;
          } else {
            tableListBuilder_.addAllMessages(other.tableList_);
          }
        }
      }
      if (!other.forbidSpoonId_.isEmpty()) {
        if (forbidSpoonId_.isEmpty()) {
          forbidSpoonId_ = other.forbidSpoonId_;
          forbidSpoonId_.makeImmutable();
          bitField0_ |= 0x00000020;
        } else {
          ensureForbidSpoonIdIsMutable();
          forbidSpoonId_.addAll(other.forbidSpoonId_);
        }
        onChanged();
      }
      if (historyTableListBuilder_ == null) {
        if (!other.historyTableList_.isEmpty()) {
          if (historyTableList_.isEmpty()) {
            historyTableList_ = other.historyTableList_;
            bitField0_ = (bitField0_ & ~0x00000040);
          } else {
            ensureHistoryTableListIsMutable();
            historyTableList_.addAll(other.historyTableList_);
          }
          onChanged();
        }
      } else {
        if (!other.historyTableList_.isEmpty()) {
          if (historyTableListBuilder_.isEmpty()) {
            historyTableListBuilder_.dispose();
            historyTableListBuilder_ = null;
            historyTableList_ = other.historyTableList_;
            bitField0_ = (bitField0_ & ~0x00000040);
            historyTableListBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetHistoryTableListFieldBuilder() : null;
          } else {
            historyTableListBuilder_.addAllMessages(other.historyTableList_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      for (int i = 0; i < getTableListCount(); i++) {
        if (!getTableList(i).isInitialized()) {
          return false;
        }
      }
      for (int i = 0; i < getHistoryTableListCount(); i++) {
        if (!getHistoryTableList(i).isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              integral_ = input.readInt64();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 24: {
              rank_ = input.readInt32();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 32: {
              dishNum_ = input.readInt32();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            case 42: {
              xddq.pb.Pharmacy2TableTempMsg m =
                  input.readMessage(
                      xddq.pb.Pharmacy2TableTempMsg.parser(),
                      extensionRegistry);
              if (tableListBuilder_ == null) {
                ensureTableListIsMutable();
                tableList_.add(m);
              } else {
                tableListBuilder_.addMessage(m);
              }
              break;
            } // case 42
            case 48: {
              int v = input.readInt32();
              ensureForbidSpoonIdIsMutable();
              forbidSpoonId_.addInt(v);
              break;
            } // case 48
            case 50: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              ensureForbidSpoonIdIsMutable();
              while (input.getBytesUntilLimit() > 0) {
                forbidSpoonId_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            } // case 50
            case 58: {
              xddq.pb.Pharmacy2TableTempMsg m =
                  input.readMessage(
                      xddq.pb.Pharmacy2TableTempMsg.parser(),
                      extensionRegistry);
              if (historyTableListBuilder_ == null) {
                ensureHistoryTableListIsMutable();
                historyTableList_.add(m);
              } else {
                historyTableListBuilder_.addMessage(m);
              }
              break;
            } // case 58
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private long integral_ ;
    /**
     * <code>optional int64 integral = 2;</code>
     * @return Whether the integral field is set.
     */
    @java.lang.Override
    public boolean hasIntegral() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int64 integral = 2;</code>
     * @return The integral.
     */
    @java.lang.Override
    public long getIntegral() {
      return integral_;
    }
    /**
     * <code>optional int64 integral = 2;</code>
     * @param value The integral to set.
     * @return This builder for chaining.
     */
    public Builder setIntegral(long value) {

      integral_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 integral = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearIntegral() {
      bitField0_ = (bitField0_ & ~0x00000002);
      integral_ = 0L;
      onChanged();
      return this;
    }

    private int rank_ ;
    /**
     * <code>optional int32 rank = 3;</code>
     * @return Whether the rank field is set.
     */
    @java.lang.Override
    public boolean hasRank() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int32 rank = 3;</code>
     * @return The rank.
     */
    @java.lang.Override
    public int getRank() {
      return rank_;
    }
    /**
     * <code>optional int32 rank = 3;</code>
     * @param value The rank to set.
     * @return This builder for chaining.
     */
    public Builder setRank(int value) {

      rank_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 rank = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearRank() {
      bitField0_ = (bitField0_ & ~0x00000004);
      rank_ = 0;
      onChanged();
      return this;
    }

    private int dishNum_ ;
    /**
     * <code>optional int32 dishNum = 4;</code>
     * @return Whether the dishNum field is set.
     */
    @java.lang.Override
    public boolean hasDishNum() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int32 dishNum = 4;</code>
     * @return The dishNum.
     */
    @java.lang.Override
    public int getDishNum() {
      return dishNum_;
    }
    /**
     * <code>optional int32 dishNum = 4;</code>
     * @param value The dishNum to set.
     * @return This builder for chaining.
     */
    public Builder setDishNum(int value) {

      dishNum_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 dishNum = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearDishNum() {
      bitField0_ = (bitField0_ & ~0x00000008);
      dishNum_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.Pharmacy2TableTempMsg> tableList_ =
      java.util.Collections.emptyList();
    private void ensureTableListIsMutable() {
      if (!((bitField0_ & 0x00000010) != 0)) {
        tableList_ = new java.util.ArrayList<xddq.pb.Pharmacy2TableTempMsg>(tableList_);
        bitField0_ |= 0x00000010;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.Pharmacy2TableTempMsg, xddq.pb.Pharmacy2TableTempMsg.Builder, xddq.pb.Pharmacy2TableTempMsgOrBuilder> tableListBuilder_;

    /**
     * <code>repeated .xddq.pb.Pharmacy2TableTempMsg tableList = 5;</code>
     */
    public java.util.List<xddq.pb.Pharmacy2TableTempMsg> getTableListList() {
      if (tableListBuilder_ == null) {
        return java.util.Collections.unmodifiableList(tableList_);
      } else {
        return tableListBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.Pharmacy2TableTempMsg tableList = 5;</code>
     */
    public int getTableListCount() {
      if (tableListBuilder_ == null) {
        return tableList_.size();
      } else {
        return tableListBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.Pharmacy2TableTempMsg tableList = 5;</code>
     */
    public xddq.pb.Pharmacy2TableTempMsg getTableList(int index) {
      if (tableListBuilder_ == null) {
        return tableList_.get(index);
      } else {
        return tableListBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.Pharmacy2TableTempMsg tableList = 5;</code>
     */
    public Builder setTableList(
        int index, xddq.pb.Pharmacy2TableTempMsg value) {
      if (tableListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureTableListIsMutable();
        tableList_.set(index, value);
        onChanged();
      } else {
        tableListBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.Pharmacy2TableTempMsg tableList = 5;</code>
     */
    public Builder setTableList(
        int index, xddq.pb.Pharmacy2TableTempMsg.Builder builderForValue) {
      if (tableListBuilder_ == null) {
        ensureTableListIsMutable();
        tableList_.set(index, builderForValue.build());
        onChanged();
      } else {
        tableListBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.Pharmacy2TableTempMsg tableList = 5;</code>
     */
    public Builder addTableList(xddq.pb.Pharmacy2TableTempMsg value) {
      if (tableListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureTableListIsMutable();
        tableList_.add(value);
        onChanged();
      } else {
        tableListBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.Pharmacy2TableTempMsg tableList = 5;</code>
     */
    public Builder addTableList(
        int index, xddq.pb.Pharmacy2TableTempMsg value) {
      if (tableListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureTableListIsMutable();
        tableList_.add(index, value);
        onChanged();
      } else {
        tableListBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.Pharmacy2TableTempMsg tableList = 5;</code>
     */
    public Builder addTableList(
        xddq.pb.Pharmacy2TableTempMsg.Builder builderForValue) {
      if (tableListBuilder_ == null) {
        ensureTableListIsMutable();
        tableList_.add(builderForValue.build());
        onChanged();
      } else {
        tableListBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.Pharmacy2TableTempMsg tableList = 5;</code>
     */
    public Builder addTableList(
        int index, xddq.pb.Pharmacy2TableTempMsg.Builder builderForValue) {
      if (tableListBuilder_ == null) {
        ensureTableListIsMutable();
        tableList_.add(index, builderForValue.build());
        onChanged();
      } else {
        tableListBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.Pharmacy2TableTempMsg tableList = 5;</code>
     */
    public Builder addAllTableList(
        java.lang.Iterable<? extends xddq.pb.Pharmacy2TableTempMsg> values) {
      if (tableListBuilder_ == null) {
        ensureTableListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, tableList_);
        onChanged();
      } else {
        tableListBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.Pharmacy2TableTempMsg tableList = 5;</code>
     */
    public Builder clearTableList() {
      if (tableListBuilder_ == null) {
        tableList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000010);
        onChanged();
      } else {
        tableListBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.Pharmacy2TableTempMsg tableList = 5;</code>
     */
    public Builder removeTableList(int index) {
      if (tableListBuilder_ == null) {
        ensureTableListIsMutable();
        tableList_.remove(index);
        onChanged();
      } else {
        tableListBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.Pharmacy2TableTempMsg tableList = 5;</code>
     */
    public xddq.pb.Pharmacy2TableTempMsg.Builder getTableListBuilder(
        int index) {
      return internalGetTableListFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.Pharmacy2TableTempMsg tableList = 5;</code>
     */
    public xddq.pb.Pharmacy2TableTempMsgOrBuilder getTableListOrBuilder(
        int index) {
      if (tableListBuilder_ == null) {
        return tableList_.get(index);  } else {
        return tableListBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.Pharmacy2TableTempMsg tableList = 5;</code>
     */
    public java.util.List<? extends xddq.pb.Pharmacy2TableTempMsgOrBuilder> 
         getTableListOrBuilderList() {
      if (tableListBuilder_ != null) {
        return tableListBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(tableList_);
      }
    }
    /**
     * <code>repeated .xddq.pb.Pharmacy2TableTempMsg tableList = 5;</code>
     */
    public xddq.pb.Pharmacy2TableTempMsg.Builder addTableListBuilder() {
      return internalGetTableListFieldBuilder().addBuilder(
          xddq.pb.Pharmacy2TableTempMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.Pharmacy2TableTempMsg tableList = 5;</code>
     */
    public xddq.pb.Pharmacy2TableTempMsg.Builder addTableListBuilder(
        int index) {
      return internalGetTableListFieldBuilder().addBuilder(
          index, xddq.pb.Pharmacy2TableTempMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.Pharmacy2TableTempMsg tableList = 5;</code>
     */
    public java.util.List<xddq.pb.Pharmacy2TableTempMsg.Builder> 
         getTableListBuilderList() {
      return internalGetTableListFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.Pharmacy2TableTempMsg, xddq.pb.Pharmacy2TableTempMsg.Builder, xddq.pb.Pharmacy2TableTempMsgOrBuilder> 
        internalGetTableListFieldBuilder() {
      if (tableListBuilder_ == null) {
        tableListBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.Pharmacy2TableTempMsg, xddq.pb.Pharmacy2TableTempMsg.Builder, xddq.pb.Pharmacy2TableTempMsgOrBuilder>(
                tableList_,
                ((bitField0_ & 0x00000010) != 0),
                getParentForChildren(),
                isClean());
        tableList_ = null;
      }
      return tableListBuilder_;
    }

    private com.google.protobuf.Internal.IntList forbidSpoonId_ = emptyIntList();
    private void ensureForbidSpoonIdIsMutable() {
      if (!forbidSpoonId_.isModifiable()) {
        forbidSpoonId_ = makeMutableCopy(forbidSpoonId_);
      }
      bitField0_ |= 0x00000020;
    }
    /**
     * <code>repeated int32 forbidSpoonId = 6;</code>
     * @return A list containing the forbidSpoonId.
     */
    public java.util.List<java.lang.Integer>
        getForbidSpoonIdList() {
      forbidSpoonId_.makeImmutable();
      return forbidSpoonId_;
    }
    /**
     * <code>repeated int32 forbidSpoonId = 6;</code>
     * @return The count of forbidSpoonId.
     */
    public int getForbidSpoonIdCount() {
      return forbidSpoonId_.size();
    }
    /**
     * <code>repeated int32 forbidSpoonId = 6;</code>
     * @param index The index of the element to return.
     * @return The forbidSpoonId at the given index.
     */
    public int getForbidSpoonId(int index) {
      return forbidSpoonId_.getInt(index);
    }
    /**
     * <code>repeated int32 forbidSpoonId = 6;</code>
     * @param index The index to set the value at.
     * @param value The forbidSpoonId to set.
     * @return This builder for chaining.
     */
    public Builder setForbidSpoonId(
        int index, int value) {

      ensureForbidSpoonIdIsMutable();
      forbidSpoonId_.setInt(index, value);
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 forbidSpoonId = 6;</code>
     * @param value The forbidSpoonId to add.
     * @return This builder for chaining.
     */
    public Builder addForbidSpoonId(int value) {

      ensureForbidSpoonIdIsMutable();
      forbidSpoonId_.addInt(value);
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 forbidSpoonId = 6;</code>
     * @param values The forbidSpoonId to add.
     * @return This builder for chaining.
     */
    public Builder addAllForbidSpoonId(
        java.lang.Iterable<? extends java.lang.Integer> values) {
      ensureForbidSpoonIdIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, forbidSpoonId_);
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 forbidSpoonId = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearForbidSpoonId() {
      forbidSpoonId_ = emptyIntList();
      bitField0_ = (bitField0_ & ~0x00000020);
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.Pharmacy2TableTempMsg> historyTableList_ =
      java.util.Collections.emptyList();
    private void ensureHistoryTableListIsMutable() {
      if (!((bitField0_ & 0x00000040) != 0)) {
        historyTableList_ = new java.util.ArrayList<xddq.pb.Pharmacy2TableTempMsg>(historyTableList_);
        bitField0_ |= 0x00000040;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.Pharmacy2TableTempMsg, xddq.pb.Pharmacy2TableTempMsg.Builder, xddq.pb.Pharmacy2TableTempMsgOrBuilder> historyTableListBuilder_;

    /**
     * <code>repeated .xddq.pb.Pharmacy2TableTempMsg historyTableList = 7;</code>
     */
    public java.util.List<xddq.pb.Pharmacy2TableTempMsg> getHistoryTableListList() {
      if (historyTableListBuilder_ == null) {
        return java.util.Collections.unmodifiableList(historyTableList_);
      } else {
        return historyTableListBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.Pharmacy2TableTempMsg historyTableList = 7;</code>
     */
    public int getHistoryTableListCount() {
      if (historyTableListBuilder_ == null) {
        return historyTableList_.size();
      } else {
        return historyTableListBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.Pharmacy2TableTempMsg historyTableList = 7;</code>
     */
    public xddq.pb.Pharmacy2TableTempMsg getHistoryTableList(int index) {
      if (historyTableListBuilder_ == null) {
        return historyTableList_.get(index);
      } else {
        return historyTableListBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.Pharmacy2TableTempMsg historyTableList = 7;</code>
     */
    public Builder setHistoryTableList(
        int index, xddq.pb.Pharmacy2TableTempMsg value) {
      if (historyTableListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureHistoryTableListIsMutable();
        historyTableList_.set(index, value);
        onChanged();
      } else {
        historyTableListBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.Pharmacy2TableTempMsg historyTableList = 7;</code>
     */
    public Builder setHistoryTableList(
        int index, xddq.pb.Pharmacy2TableTempMsg.Builder builderForValue) {
      if (historyTableListBuilder_ == null) {
        ensureHistoryTableListIsMutable();
        historyTableList_.set(index, builderForValue.build());
        onChanged();
      } else {
        historyTableListBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.Pharmacy2TableTempMsg historyTableList = 7;</code>
     */
    public Builder addHistoryTableList(xddq.pb.Pharmacy2TableTempMsg value) {
      if (historyTableListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureHistoryTableListIsMutable();
        historyTableList_.add(value);
        onChanged();
      } else {
        historyTableListBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.Pharmacy2TableTempMsg historyTableList = 7;</code>
     */
    public Builder addHistoryTableList(
        int index, xddq.pb.Pharmacy2TableTempMsg value) {
      if (historyTableListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureHistoryTableListIsMutable();
        historyTableList_.add(index, value);
        onChanged();
      } else {
        historyTableListBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.Pharmacy2TableTempMsg historyTableList = 7;</code>
     */
    public Builder addHistoryTableList(
        xddq.pb.Pharmacy2TableTempMsg.Builder builderForValue) {
      if (historyTableListBuilder_ == null) {
        ensureHistoryTableListIsMutable();
        historyTableList_.add(builderForValue.build());
        onChanged();
      } else {
        historyTableListBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.Pharmacy2TableTempMsg historyTableList = 7;</code>
     */
    public Builder addHistoryTableList(
        int index, xddq.pb.Pharmacy2TableTempMsg.Builder builderForValue) {
      if (historyTableListBuilder_ == null) {
        ensureHistoryTableListIsMutable();
        historyTableList_.add(index, builderForValue.build());
        onChanged();
      } else {
        historyTableListBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.Pharmacy2TableTempMsg historyTableList = 7;</code>
     */
    public Builder addAllHistoryTableList(
        java.lang.Iterable<? extends xddq.pb.Pharmacy2TableTempMsg> values) {
      if (historyTableListBuilder_ == null) {
        ensureHistoryTableListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, historyTableList_);
        onChanged();
      } else {
        historyTableListBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.Pharmacy2TableTempMsg historyTableList = 7;</code>
     */
    public Builder clearHistoryTableList() {
      if (historyTableListBuilder_ == null) {
        historyTableList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000040);
        onChanged();
      } else {
        historyTableListBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.Pharmacy2TableTempMsg historyTableList = 7;</code>
     */
    public Builder removeHistoryTableList(int index) {
      if (historyTableListBuilder_ == null) {
        ensureHistoryTableListIsMutable();
        historyTableList_.remove(index);
        onChanged();
      } else {
        historyTableListBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.Pharmacy2TableTempMsg historyTableList = 7;</code>
     */
    public xddq.pb.Pharmacy2TableTempMsg.Builder getHistoryTableListBuilder(
        int index) {
      return internalGetHistoryTableListFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.Pharmacy2TableTempMsg historyTableList = 7;</code>
     */
    public xddq.pb.Pharmacy2TableTempMsgOrBuilder getHistoryTableListOrBuilder(
        int index) {
      if (historyTableListBuilder_ == null) {
        return historyTableList_.get(index);  } else {
        return historyTableListBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.Pharmacy2TableTempMsg historyTableList = 7;</code>
     */
    public java.util.List<? extends xddq.pb.Pharmacy2TableTempMsgOrBuilder> 
         getHistoryTableListOrBuilderList() {
      if (historyTableListBuilder_ != null) {
        return historyTableListBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(historyTableList_);
      }
    }
    /**
     * <code>repeated .xddq.pb.Pharmacy2TableTempMsg historyTableList = 7;</code>
     */
    public xddq.pb.Pharmacy2TableTempMsg.Builder addHistoryTableListBuilder() {
      return internalGetHistoryTableListFieldBuilder().addBuilder(
          xddq.pb.Pharmacy2TableTempMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.Pharmacy2TableTempMsg historyTableList = 7;</code>
     */
    public xddq.pb.Pharmacy2TableTempMsg.Builder addHistoryTableListBuilder(
        int index) {
      return internalGetHistoryTableListFieldBuilder().addBuilder(
          index, xddq.pb.Pharmacy2TableTempMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.Pharmacy2TableTempMsg historyTableList = 7;</code>
     */
    public java.util.List<xddq.pb.Pharmacy2TableTempMsg.Builder> 
         getHistoryTableListBuilderList() {
      return internalGetHistoryTableListFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.Pharmacy2TableTempMsg, xddq.pb.Pharmacy2TableTempMsg.Builder, xddq.pb.Pharmacy2TableTempMsgOrBuilder> 
        internalGetHistoryTableListFieldBuilder() {
      if (historyTableListBuilder_ == null) {
        historyTableListBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.Pharmacy2TableTempMsg, xddq.pb.Pharmacy2TableTempMsg.Builder, xddq.pb.Pharmacy2TableTempMsgOrBuilder>(
                historyTableList_,
                ((bitField0_ & 0x00000040) != 0),
                getParentForChildren(),
                isClean());
        historyTableList_ = null;
      }
      return historyTableListBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.Pharmacy2UserIdTableRespMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.Pharmacy2UserIdTableRespMsg)
  private static final xddq.pb.Pharmacy2UserIdTableRespMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.Pharmacy2UserIdTableRespMsg();
  }

  public static xddq.pb.Pharmacy2UserIdTableRespMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<Pharmacy2UserIdTableRespMsg>
      PARSER = new com.google.protobuf.AbstractParser<Pharmacy2UserIdTableRespMsg>() {
    @java.lang.Override
    public Pharmacy2UserIdTableRespMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<Pharmacy2UserIdTableRespMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<Pharmacy2UserIdTableRespMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.Pharmacy2UserIdTableRespMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

