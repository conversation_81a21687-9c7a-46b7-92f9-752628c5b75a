// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.SpChampBattleReplayResp}
 */
public final class SpChampBattleReplayResp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.SpChampBattleReplayResp)
    SpChampBattleReplayRespOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      SpChampBattleReplayResp.class.getName());
  }
  // Use SpChampBattleReplayResp.newBuilder() to construct.
  private SpChampBattleReplayResp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private SpChampBattleReplayResp() {
    mySeparationInfo_ = java.util.Collections.emptyList();
    otherSeparationInfo_ = java.util.Collections.emptyList();
    battleRecordMsg_ = java.util.Collections.emptyList();
    winList_ = emptyBooleanList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_SpChampBattleReplayResp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_SpChampBattleReplayResp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.SpChampBattleReplayResp.class, xddq.pb.SpChampBattleReplayResp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int MYSEPARATIONINFO_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.SpChampSeparationSimplyInfo> mySeparationInfo_;
  /**
   * <code>repeated .xddq.pb.SpChampSeparationSimplyInfo mySeparationInfo = 2;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.SpChampSeparationSimplyInfo> getMySeparationInfoList() {
    return mySeparationInfo_;
  }
  /**
   * <code>repeated .xddq.pb.SpChampSeparationSimplyInfo mySeparationInfo = 2;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.SpChampSeparationSimplyInfoOrBuilder> 
      getMySeparationInfoOrBuilderList() {
    return mySeparationInfo_;
  }
  /**
   * <code>repeated .xddq.pb.SpChampSeparationSimplyInfo mySeparationInfo = 2;</code>
   */
  @java.lang.Override
  public int getMySeparationInfoCount() {
    return mySeparationInfo_.size();
  }
  /**
   * <code>repeated .xddq.pb.SpChampSeparationSimplyInfo mySeparationInfo = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.SpChampSeparationSimplyInfo getMySeparationInfo(int index) {
    return mySeparationInfo_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.SpChampSeparationSimplyInfo mySeparationInfo = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.SpChampSeparationSimplyInfoOrBuilder getMySeparationInfoOrBuilder(
      int index) {
    return mySeparationInfo_.get(index);
  }

  public static final int OTHERSEPARATIONINFO_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.SpChampSeparationSimplyInfo> otherSeparationInfo_;
  /**
   * <code>repeated .xddq.pb.SpChampSeparationSimplyInfo otherSeparationInfo = 3;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.SpChampSeparationSimplyInfo> getOtherSeparationInfoList() {
    return otherSeparationInfo_;
  }
  /**
   * <code>repeated .xddq.pb.SpChampSeparationSimplyInfo otherSeparationInfo = 3;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.SpChampSeparationSimplyInfoOrBuilder> 
      getOtherSeparationInfoOrBuilderList() {
    return otherSeparationInfo_;
  }
  /**
   * <code>repeated .xddq.pb.SpChampSeparationSimplyInfo otherSeparationInfo = 3;</code>
   */
  @java.lang.Override
  public int getOtherSeparationInfoCount() {
    return otherSeparationInfo_.size();
  }
  /**
   * <code>repeated .xddq.pb.SpChampSeparationSimplyInfo otherSeparationInfo = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.SpChampSeparationSimplyInfo getOtherSeparationInfo(int index) {
    return otherSeparationInfo_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.SpChampSeparationSimplyInfo otherSeparationInfo = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.SpChampSeparationSimplyInfoOrBuilder getOtherSeparationInfoOrBuilder(
      int index) {
    return otherSeparationInfo_.get(index);
  }

  public static final int BATTLERECORDMSG_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.BattleRecordMsg> battleRecordMsg_;
  /**
   * <code>repeated .xddq.pb.BattleRecordMsg battleRecordMsg = 4;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.BattleRecordMsg> getBattleRecordMsgList() {
    return battleRecordMsg_;
  }
  /**
   * <code>repeated .xddq.pb.BattleRecordMsg battleRecordMsg = 4;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.BattleRecordMsgOrBuilder> 
      getBattleRecordMsgOrBuilderList() {
    return battleRecordMsg_;
  }
  /**
   * <code>repeated .xddq.pb.BattleRecordMsg battleRecordMsg = 4;</code>
   */
  @java.lang.Override
  public int getBattleRecordMsgCount() {
    return battleRecordMsg_.size();
  }
  /**
   * <code>repeated .xddq.pb.BattleRecordMsg battleRecordMsg = 4;</code>
   */
  @java.lang.Override
  public xddq.pb.BattleRecordMsg getBattleRecordMsg(int index) {
    return battleRecordMsg_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.BattleRecordMsg battleRecordMsg = 4;</code>
   */
  @java.lang.Override
  public xddq.pb.BattleRecordMsgOrBuilder getBattleRecordMsgOrBuilder(
      int index) {
    return battleRecordMsg_.get(index);
  }

  public static final int WINLIST_FIELD_NUMBER = 5;
  @SuppressWarnings("serial")
  private com.google.protobuf.Internal.BooleanList winList_ =
      emptyBooleanList();
  /**
   * <code>repeated bool winList = 5;</code>
   * @return A list containing the winList.
   */
  @java.lang.Override
  public java.util.List<java.lang.Boolean>
      getWinListList() {
    return winList_;
  }
  /**
   * <code>repeated bool winList = 5;</code>
   * @return The count of winList.
   */
  public int getWinListCount() {
    return winList_.size();
  }
  /**
   * <code>repeated bool winList = 5;</code>
   * @param index The index of the element to return.
   * @return The winList at the given index.
   */
  public boolean getWinList(int index) {
    return winList_.getBoolean(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    for (int i = 0; i < getBattleRecordMsgCount(); i++) {
      if (!getBattleRecordMsg(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    for (int i = 0; i < mySeparationInfo_.size(); i++) {
      output.writeMessage(2, mySeparationInfo_.get(i));
    }
    for (int i = 0; i < otherSeparationInfo_.size(); i++) {
      output.writeMessage(3, otherSeparationInfo_.get(i));
    }
    for (int i = 0; i < battleRecordMsg_.size(); i++) {
      output.writeMessage(4, battleRecordMsg_.get(i));
    }
    for (int i = 0; i < winList_.size(); i++) {
      output.writeBool(5, winList_.getBoolean(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    for (int i = 0; i < mySeparationInfo_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, mySeparationInfo_.get(i));
    }
    for (int i = 0; i < otherSeparationInfo_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, otherSeparationInfo_.get(i));
    }
    for (int i = 0; i < battleRecordMsg_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(4, battleRecordMsg_.get(i));
    }
    {
      int dataSize = 0;
      dataSize = 1 * getWinListList().size();
      size += dataSize;
      size += 1 * getWinListList().size();
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.SpChampBattleReplayResp)) {
      return super.equals(obj);
    }
    xddq.pb.SpChampBattleReplayResp other = (xddq.pb.SpChampBattleReplayResp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (!getMySeparationInfoList()
        .equals(other.getMySeparationInfoList())) return false;
    if (!getOtherSeparationInfoList()
        .equals(other.getOtherSeparationInfoList())) return false;
    if (!getBattleRecordMsgList()
        .equals(other.getBattleRecordMsgList())) return false;
    if (!getWinListList()
        .equals(other.getWinListList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (getMySeparationInfoCount() > 0) {
      hash = (37 * hash) + MYSEPARATIONINFO_FIELD_NUMBER;
      hash = (53 * hash) + getMySeparationInfoList().hashCode();
    }
    if (getOtherSeparationInfoCount() > 0) {
      hash = (37 * hash) + OTHERSEPARATIONINFO_FIELD_NUMBER;
      hash = (53 * hash) + getOtherSeparationInfoList().hashCode();
    }
    if (getBattleRecordMsgCount() > 0) {
      hash = (37 * hash) + BATTLERECORDMSG_FIELD_NUMBER;
      hash = (53 * hash) + getBattleRecordMsgList().hashCode();
    }
    if (getWinListCount() > 0) {
      hash = (37 * hash) + WINLIST_FIELD_NUMBER;
      hash = (53 * hash) + getWinListList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.SpChampBattleReplayResp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.SpChampBattleReplayResp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.SpChampBattleReplayResp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.SpChampBattleReplayResp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.SpChampBattleReplayResp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.SpChampBattleReplayResp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.SpChampBattleReplayResp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.SpChampBattleReplayResp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.SpChampBattleReplayResp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.SpChampBattleReplayResp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.SpChampBattleReplayResp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.SpChampBattleReplayResp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.SpChampBattleReplayResp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.SpChampBattleReplayResp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.SpChampBattleReplayResp)
      xddq.pb.SpChampBattleReplayRespOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_SpChampBattleReplayResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_SpChampBattleReplayResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.SpChampBattleReplayResp.class, xddq.pb.SpChampBattleReplayResp.Builder.class);
    }

    // Construct using xddq.pb.SpChampBattleReplayResp.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      if (mySeparationInfoBuilder_ == null) {
        mySeparationInfo_ = java.util.Collections.emptyList();
      } else {
        mySeparationInfo_ = null;
        mySeparationInfoBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000002);
      if (otherSeparationInfoBuilder_ == null) {
        otherSeparationInfo_ = java.util.Collections.emptyList();
      } else {
        otherSeparationInfo_ = null;
        otherSeparationInfoBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000004);
      if (battleRecordMsgBuilder_ == null) {
        battleRecordMsg_ = java.util.Collections.emptyList();
      } else {
        battleRecordMsg_ = null;
        battleRecordMsgBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000008);
      winList_ = emptyBooleanList();
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_SpChampBattleReplayResp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.SpChampBattleReplayResp getDefaultInstanceForType() {
      return xddq.pb.SpChampBattleReplayResp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.SpChampBattleReplayResp build() {
      xddq.pb.SpChampBattleReplayResp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.SpChampBattleReplayResp buildPartial() {
      xddq.pb.SpChampBattleReplayResp result = new xddq.pb.SpChampBattleReplayResp(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.SpChampBattleReplayResp result) {
      if (mySeparationInfoBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          mySeparationInfo_ = java.util.Collections.unmodifiableList(mySeparationInfo_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.mySeparationInfo_ = mySeparationInfo_;
      } else {
        result.mySeparationInfo_ = mySeparationInfoBuilder_.build();
      }
      if (otherSeparationInfoBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0)) {
          otherSeparationInfo_ = java.util.Collections.unmodifiableList(otherSeparationInfo_);
          bitField0_ = (bitField0_ & ~0x00000004);
        }
        result.otherSeparationInfo_ = otherSeparationInfo_;
      } else {
        result.otherSeparationInfo_ = otherSeparationInfoBuilder_.build();
      }
      if (battleRecordMsgBuilder_ == null) {
        if (((bitField0_ & 0x00000008) != 0)) {
          battleRecordMsg_ = java.util.Collections.unmodifiableList(battleRecordMsg_);
          bitField0_ = (bitField0_ & ~0x00000008);
        }
        result.battleRecordMsg_ = battleRecordMsg_;
      } else {
        result.battleRecordMsg_ = battleRecordMsgBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.SpChampBattleReplayResp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        winList_.makeImmutable();
        result.winList_ = winList_;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.SpChampBattleReplayResp) {
        return mergeFrom((xddq.pb.SpChampBattleReplayResp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.SpChampBattleReplayResp other) {
      if (other == xddq.pb.SpChampBattleReplayResp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (mySeparationInfoBuilder_ == null) {
        if (!other.mySeparationInfo_.isEmpty()) {
          if (mySeparationInfo_.isEmpty()) {
            mySeparationInfo_ = other.mySeparationInfo_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureMySeparationInfoIsMutable();
            mySeparationInfo_.addAll(other.mySeparationInfo_);
          }
          onChanged();
        }
      } else {
        if (!other.mySeparationInfo_.isEmpty()) {
          if (mySeparationInfoBuilder_.isEmpty()) {
            mySeparationInfoBuilder_.dispose();
            mySeparationInfoBuilder_ = null;
            mySeparationInfo_ = other.mySeparationInfo_;
            bitField0_ = (bitField0_ & ~0x00000002);
            mySeparationInfoBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetMySeparationInfoFieldBuilder() : null;
          } else {
            mySeparationInfoBuilder_.addAllMessages(other.mySeparationInfo_);
          }
        }
      }
      if (otherSeparationInfoBuilder_ == null) {
        if (!other.otherSeparationInfo_.isEmpty()) {
          if (otherSeparationInfo_.isEmpty()) {
            otherSeparationInfo_ = other.otherSeparationInfo_;
            bitField0_ = (bitField0_ & ~0x00000004);
          } else {
            ensureOtherSeparationInfoIsMutable();
            otherSeparationInfo_.addAll(other.otherSeparationInfo_);
          }
          onChanged();
        }
      } else {
        if (!other.otherSeparationInfo_.isEmpty()) {
          if (otherSeparationInfoBuilder_.isEmpty()) {
            otherSeparationInfoBuilder_.dispose();
            otherSeparationInfoBuilder_ = null;
            otherSeparationInfo_ = other.otherSeparationInfo_;
            bitField0_ = (bitField0_ & ~0x00000004);
            otherSeparationInfoBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetOtherSeparationInfoFieldBuilder() : null;
          } else {
            otherSeparationInfoBuilder_.addAllMessages(other.otherSeparationInfo_);
          }
        }
      }
      if (battleRecordMsgBuilder_ == null) {
        if (!other.battleRecordMsg_.isEmpty()) {
          if (battleRecordMsg_.isEmpty()) {
            battleRecordMsg_ = other.battleRecordMsg_;
            bitField0_ = (bitField0_ & ~0x00000008);
          } else {
            ensureBattleRecordMsgIsMutable();
            battleRecordMsg_.addAll(other.battleRecordMsg_);
          }
          onChanged();
        }
      } else {
        if (!other.battleRecordMsg_.isEmpty()) {
          if (battleRecordMsgBuilder_.isEmpty()) {
            battleRecordMsgBuilder_.dispose();
            battleRecordMsgBuilder_ = null;
            battleRecordMsg_ = other.battleRecordMsg_;
            bitField0_ = (bitField0_ & ~0x00000008);
            battleRecordMsgBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetBattleRecordMsgFieldBuilder() : null;
          } else {
            battleRecordMsgBuilder_.addAllMessages(other.battleRecordMsg_);
          }
        }
      }
      if (!other.winList_.isEmpty()) {
        if (winList_.isEmpty()) {
          winList_ = other.winList_;
          winList_.makeImmutable();
          bitField0_ |= 0x00000010;
        } else {
          ensureWinListIsMutable();
          winList_.addAll(other.winList_);
        }
        onChanged();
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      for (int i = 0; i < getBattleRecordMsgCount(); i++) {
        if (!getBattleRecordMsg(i).isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              xddq.pb.SpChampSeparationSimplyInfo m =
                  input.readMessage(
                      xddq.pb.SpChampSeparationSimplyInfo.parser(),
                      extensionRegistry);
              if (mySeparationInfoBuilder_ == null) {
                ensureMySeparationInfoIsMutable();
                mySeparationInfo_.add(m);
              } else {
                mySeparationInfoBuilder_.addMessage(m);
              }
              break;
            } // case 18
            case 26: {
              xddq.pb.SpChampSeparationSimplyInfo m =
                  input.readMessage(
                      xddq.pb.SpChampSeparationSimplyInfo.parser(),
                      extensionRegistry);
              if (otherSeparationInfoBuilder_ == null) {
                ensureOtherSeparationInfoIsMutable();
                otherSeparationInfo_.add(m);
              } else {
                otherSeparationInfoBuilder_.addMessage(m);
              }
              break;
            } // case 26
            case 34: {
              xddq.pb.BattleRecordMsg m =
                  input.readMessage(
                      xddq.pb.BattleRecordMsg.parser(),
                      extensionRegistry);
              if (battleRecordMsgBuilder_ == null) {
                ensureBattleRecordMsgIsMutable();
                battleRecordMsg_.add(m);
              } else {
                battleRecordMsgBuilder_.addMessage(m);
              }
              break;
            } // case 34
            case 40: {
              boolean v = input.readBool();
              ensureWinListIsMutable();
              winList_.addBoolean(v);
              break;
            } // case 40
            case 42: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              int alloc = length > 4096 ? 4096 : length;
              ensureWinListIsMutable(alloc / 1);
              while (input.getBytesUntilLimit() > 0) {
                winList_.addBoolean(input.readBool());
              }
              input.popLimit(limit);
              break;
            } // case 42
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.SpChampSeparationSimplyInfo> mySeparationInfo_ =
      java.util.Collections.emptyList();
    private void ensureMySeparationInfoIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        mySeparationInfo_ = new java.util.ArrayList<xddq.pb.SpChampSeparationSimplyInfo>(mySeparationInfo_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.SpChampSeparationSimplyInfo, xddq.pb.SpChampSeparationSimplyInfo.Builder, xddq.pb.SpChampSeparationSimplyInfoOrBuilder> mySeparationInfoBuilder_;

    /**
     * <code>repeated .xddq.pb.SpChampSeparationSimplyInfo mySeparationInfo = 2;</code>
     */
    public java.util.List<xddq.pb.SpChampSeparationSimplyInfo> getMySeparationInfoList() {
      if (mySeparationInfoBuilder_ == null) {
        return java.util.Collections.unmodifiableList(mySeparationInfo_);
      } else {
        return mySeparationInfoBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.SpChampSeparationSimplyInfo mySeparationInfo = 2;</code>
     */
    public int getMySeparationInfoCount() {
      if (mySeparationInfoBuilder_ == null) {
        return mySeparationInfo_.size();
      } else {
        return mySeparationInfoBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.SpChampSeparationSimplyInfo mySeparationInfo = 2;</code>
     */
    public xddq.pb.SpChampSeparationSimplyInfo getMySeparationInfo(int index) {
      if (mySeparationInfoBuilder_ == null) {
        return mySeparationInfo_.get(index);
      } else {
        return mySeparationInfoBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.SpChampSeparationSimplyInfo mySeparationInfo = 2;</code>
     */
    public Builder setMySeparationInfo(
        int index, xddq.pb.SpChampSeparationSimplyInfo value) {
      if (mySeparationInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMySeparationInfoIsMutable();
        mySeparationInfo_.set(index, value);
        onChanged();
      } else {
        mySeparationInfoBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.SpChampSeparationSimplyInfo mySeparationInfo = 2;</code>
     */
    public Builder setMySeparationInfo(
        int index, xddq.pb.SpChampSeparationSimplyInfo.Builder builderForValue) {
      if (mySeparationInfoBuilder_ == null) {
        ensureMySeparationInfoIsMutable();
        mySeparationInfo_.set(index, builderForValue.build());
        onChanged();
      } else {
        mySeparationInfoBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.SpChampSeparationSimplyInfo mySeparationInfo = 2;</code>
     */
    public Builder addMySeparationInfo(xddq.pb.SpChampSeparationSimplyInfo value) {
      if (mySeparationInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMySeparationInfoIsMutable();
        mySeparationInfo_.add(value);
        onChanged();
      } else {
        mySeparationInfoBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.SpChampSeparationSimplyInfo mySeparationInfo = 2;</code>
     */
    public Builder addMySeparationInfo(
        int index, xddq.pb.SpChampSeparationSimplyInfo value) {
      if (mySeparationInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMySeparationInfoIsMutable();
        mySeparationInfo_.add(index, value);
        onChanged();
      } else {
        mySeparationInfoBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.SpChampSeparationSimplyInfo mySeparationInfo = 2;</code>
     */
    public Builder addMySeparationInfo(
        xddq.pb.SpChampSeparationSimplyInfo.Builder builderForValue) {
      if (mySeparationInfoBuilder_ == null) {
        ensureMySeparationInfoIsMutable();
        mySeparationInfo_.add(builderForValue.build());
        onChanged();
      } else {
        mySeparationInfoBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.SpChampSeparationSimplyInfo mySeparationInfo = 2;</code>
     */
    public Builder addMySeparationInfo(
        int index, xddq.pb.SpChampSeparationSimplyInfo.Builder builderForValue) {
      if (mySeparationInfoBuilder_ == null) {
        ensureMySeparationInfoIsMutable();
        mySeparationInfo_.add(index, builderForValue.build());
        onChanged();
      } else {
        mySeparationInfoBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.SpChampSeparationSimplyInfo mySeparationInfo = 2;</code>
     */
    public Builder addAllMySeparationInfo(
        java.lang.Iterable<? extends xddq.pb.SpChampSeparationSimplyInfo> values) {
      if (mySeparationInfoBuilder_ == null) {
        ensureMySeparationInfoIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, mySeparationInfo_);
        onChanged();
      } else {
        mySeparationInfoBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.SpChampSeparationSimplyInfo mySeparationInfo = 2;</code>
     */
    public Builder clearMySeparationInfo() {
      if (mySeparationInfoBuilder_ == null) {
        mySeparationInfo_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        mySeparationInfoBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.SpChampSeparationSimplyInfo mySeparationInfo = 2;</code>
     */
    public Builder removeMySeparationInfo(int index) {
      if (mySeparationInfoBuilder_ == null) {
        ensureMySeparationInfoIsMutable();
        mySeparationInfo_.remove(index);
        onChanged();
      } else {
        mySeparationInfoBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.SpChampSeparationSimplyInfo mySeparationInfo = 2;</code>
     */
    public xddq.pb.SpChampSeparationSimplyInfo.Builder getMySeparationInfoBuilder(
        int index) {
      return internalGetMySeparationInfoFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.SpChampSeparationSimplyInfo mySeparationInfo = 2;</code>
     */
    public xddq.pb.SpChampSeparationSimplyInfoOrBuilder getMySeparationInfoOrBuilder(
        int index) {
      if (mySeparationInfoBuilder_ == null) {
        return mySeparationInfo_.get(index);  } else {
        return mySeparationInfoBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.SpChampSeparationSimplyInfo mySeparationInfo = 2;</code>
     */
    public java.util.List<? extends xddq.pb.SpChampSeparationSimplyInfoOrBuilder> 
         getMySeparationInfoOrBuilderList() {
      if (mySeparationInfoBuilder_ != null) {
        return mySeparationInfoBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(mySeparationInfo_);
      }
    }
    /**
     * <code>repeated .xddq.pb.SpChampSeparationSimplyInfo mySeparationInfo = 2;</code>
     */
    public xddq.pb.SpChampSeparationSimplyInfo.Builder addMySeparationInfoBuilder() {
      return internalGetMySeparationInfoFieldBuilder().addBuilder(
          xddq.pb.SpChampSeparationSimplyInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.SpChampSeparationSimplyInfo mySeparationInfo = 2;</code>
     */
    public xddq.pb.SpChampSeparationSimplyInfo.Builder addMySeparationInfoBuilder(
        int index) {
      return internalGetMySeparationInfoFieldBuilder().addBuilder(
          index, xddq.pb.SpChampSeparationSimplyInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.SpChampSeparationSimplyInfo mySeparationInfo = 2;</code>
     */
    public java.util.List<xddq.pb.SpChampSeparationSimplyInfo.Builder> 
         getMySeparationInfoBuilderList() {
      return internalGetMySeparationInfoFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.SpChampSeparationSimplyInfo, xddq.pb.SpChampSeparationSimplyInfo.Builder, xddq.pb.SpChampSeparationSimplyInfoOrBuilder> 
        internalGetMySeparationInfoFieldBuilder() {
      if (mySeparationInfoBuilder_ == null) {
        mySeparationInfoBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.SpChampSeparationSimplyInfo, xddq.pb.SpChampSeparationSimplyInfo.Builder, xddq.pb.SpChampSeparationSimplyInfoOrBuilder>(
                mySeparationInfo_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        mySeparationInfo_ = null;
      }
      return mySeparationInfoBuilder_;
    }

    private java.util.List<xddq.pb.SpChampSeparationSimplyInfo> otherSeparationInfo_ =
      java.util.Collections.emptyList();
    private void ensureOtherSeparationInfoIsMutable() {
      if (!((bitField0_ & 0x00000004) != 0)) {
        otherSeparationInfo_ = new java.util.ArrayList<xddq.pb.SpChampSeparationSimplyInfo>(otherSeparationInfo_);
        bitField0_ |= 0x00000004;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.SpChampSeparationSimplyInfo, xddq.pb.SpChampSeparationSimplyInfo.Builder, xddq.pb.SpChampSeparationSimplyInfoOrBuilder> otherSeparationInfoBuilder_;

    /**
     * <code>repeated .xddq.pb.SpChampSeparationSimplyInfo otherSeparationInfo = 3;</code>
     */
    public java.util.List<xddq.pb.SpChampSeparationSimplyInfo> getOtherSeparationInfoList() {
      if (otherSeparationInfoBuilder_ == null) {
        return java.util.Collections.unmodifiableList(otherSeparationInfo_);
      } else {
        return otherSeparationInfoBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.SpChampSeparationSimplyInfo otherSeparationInfo = 3;</code>
     */
    public int getOtherSeparationInfoCount() {
      if (otherSeparationInfoBuilder_ == null) {
        return otherSeparationInfo_.size();
      } else {
        return otherSeparationInfoBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.SpChampSeparationSimplyInfo otherSeparationInfo = 3;</code>
     */
    public xddq.pb.SpChampSeparationSimplyInfo getOtherSeparationInfo(int index) {
      if (otherSeparationInfoBuilder_ == null) {
        return otherSeparationInfo_.get(index);
      } else {
        return otherSeparationInfoBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.SpChampSeparationSimplyInfo otherSeparationInfo = 3;</code>
     */
    public Builder setOtherSeparationInfo(
        int index, xddq.pb.SpChampSeparationSimplyInfo value) {
      if (otherSeparationInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureOtherSeparationInfoIsMutable();
        otherSeparationInfo_.set(index, value);
        onChanged();
      } else {
        otherSeparationInfoBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.SpChampSeparationSimplyInfo otherSeparationInfo = 3;</code>
     */
    public Builder setOtherSeparationInfo(
        int index, xddq.pb.SpChampSeparationSimplyInfo.Builder builderForValue) {
      if (otherSeparationInfoBuilder_ == null) {
        ensureOtherSeparationInfoIsMutable();
        otherSeparationInfo_.set(index, builderForValue.build());
        onChanged();
      } else {
        otherSeparationInfoBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.SpChampSeparationSimplyInfo otherSeparationInfo = 3;</code>
     */
    public Builder addOtherSeparationInfo(xddq.pb.SpChampSeparationSimplyInfo value) {
      if (otherSeparationInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureOtherSeparationInfoIsMutable();
        otherSeparationInfo_.add(value);
        onChanged();
      } else {
        otherSeparationInfoBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.SpChampSeparationSimplyInfo otherSeparationInfo = 3;</code>
     */
    public Builder addOtherSeparationInfo(
        int index, xddq.pb.SpChampSeparationSimplyInfo value) {
      if (otherSeparationInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureOtherSeparationInfoIsMutable();
        otherSeparationInfo_.add(index, value);
        onChanged();
      } else {
        otherSeparationInfoBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.SpChampSeparationSimplyInfo otherSeparationInfo = 3;</code>
     */
    public Builder addOtherSeparationInfo(
        xddq.pb.SpChampSeparationSimplyInfo.Builder builderForValue) {
      if (otherSeparationInfoBuilder_ == null) {
        ensureOtherSeparationInfoIsMutable();
        otherSeparationInfo_.add(builderForValue.build());
        onChanged();
      } else {
        otherSeparationInfoBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.SpChampSeparationSimplyInfo otherSeparationInfo = 3;</code>
     */
    public Builder addOtherSeparationInfo(
        int index, xddq.pb.SpChampSeparationSimplyInfo.Builder builderForValue) {
      if (otherSeparationInfoBuilder_ == null) {
        ensureOtherSeparationInfoIsMutable();
        otherSeparationInfo_.add(index, builderForValue.build());
        onChanged();
      } else {
        otherSeparationInfoBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.SpChampSeparationSimplyInfo otherSeparationInfo = 3;</code>
     */
    public Builder addAllOtherSeparationInfo(
        java.lang.Iterable<? extends xddq.pb.SpChampSeparationSimplyInfo> values) {
      if (otherSeparationInfoBuilder_ == null) {
        ensureOtherSeparationInfoIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, otherSeparationInfo_);
        onChanged();
      } else {
        otherSeparationInfoBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.SpChampSeparationSimplyInfo otherSeparationInfo = 3;</code>
     */
    public Builder clearOtherSeparationInfo() {
      if (otherSeparationInfoBuilder_ == null) {
        otherSeparationInfo_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
      } else {
        otherSeparationInfoBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.SpChampSeparationSimplyInfo otherSeparationInfo = 3;</code>
     */
    public Builder removeOtherSeparationInfo(int index) {
      if (otherSeparationInfoBuilder_ == null) {
        ensureOtherSeparationInfoIsMutable();
        otherSeparationInfo_.remove(index);
        onChanged();
      } else {
        otherSeparationInfoBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.SpChampSeparationSimplyInfo otherSeparationInfo = 3;</code>
     */
    public xddq.pb.SpChampSeparationSimplyInfo.Builder getOtherSeparationInfoBuilder(
        int index) {
      return internalGetOtherSeparationInfoFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.SpChampSeparationSimplyInfo otherSeparationInfo = 3;</code>
     */
    public xddq.pb.SpChampSeparationSimplyInfoOrBuilder getOtherSeparationInfoOrBuilder(
        int index) {
      if (otherSeparationInfoBuilder_ == null) {
        return otherSeparationInfo_.get(index);  } else {
        return otherSeparationInfoBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.SpChampSeparationSimplyInfo otherSeparationInfo = 3;</code>
     */
    public java.util.List<? extends xddq.pb.SpChampSeparationSimplyInfoOrBuilder> 
         getOtherSeparationInfoOrBuilderList() {
      if (otherSeparationInfoBuilder_ != null) {
        return otherSeparationInfoBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(otherSeparationInfo_);
      }
    }
    /**
     * <code>repeated .xddq.pb.SpChampSeparationSimplyInfo otherSeparationInfo = 3;</code>
     */
    public xddq.pb.SpChampSeparationSimplyInfo.Builder addOtherSeparationInfoBuilder() {
      return internalGetOtherSeparationInfoFieldBuilder().addBuilder(
          xddq.pb.SpChampSeparationSimplyInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.SpChampSeparationSimplyInfo otherSeparationInfo = 3;</code>
     */
    public xddq.pb.SpChampSeparationSimplyInfo.Builder addOtherSeparationInfoBuilder(
        int index) {
      return internalGetOtherSeparationInfoFieldBuilder().addBuilder(
          index, xddq.pb.SpChampSeparationSimplyInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.SpChampSeparationSimplyInfo otherSeparationInfo = 3;</code>
     */
    public java.util.List<xddq.pb.SpChampSeparationSimplyInfo.Builder> 
         getOtherSeparationInfoBuilderList() {
      return internalGetOtherSeparationInfoFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.SpChampSeparationSimplyInfo, xddq.pb.SpChampSeparationSimplyInfo.Builder, xddq.pb.SpChampSeparationSimplyInfoOrBuilder> 
        internalGetOtherSeparationInfoFieldBuilder() {
      if (otherSeparationInfoBuilder_ == null) {
        otherSeparationInfoBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.SpChampSeparationSimplyInfo, xddq.pb.SpChampSeparationSimplyInfo.Builder, xddq.pb.SpChampSeparationSimplyInfoOrBuilder>(
                otherSeparationInfo_,
                ((bitField0_ & 0x00000004) != 0),
                getParentForChildren(),
                isClean());
        otherSeparationInfo_ = null;
      }
      return otherSeparationInfoBuilder_;
    }

    private java.util.List<xddq.pb.BattleRecordMsg> battleRecordMsg_ =
      java.util.Collections.emptyList();
    private void ensureBattleRecordMsgIsMutable() {
      if (!((bitField0_ & 0x00000008) != 0)) {
        battleRecordMsg_ = new java.util.ArrayList<xddq.pb.BattleRecordMsg>(battleRecordMsg_);
        bitField0_ |= 0x00000008;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.BattleRecordMsg, xddq.pb.BattleRecordMsg.Builder, xddq.pb.BattleRecordMsgOrBuilder> battleRecordMsgBuilder_;

    /**
     * <code>repeated .xddq.pb.BattleRecordMsg battleRecordMsg = 4;</code>
     */
    public java.util.List<xddq.pb.BattleRecordMsg> getBattleRecordMsgList() {
      if (battleRecordMsgBuilder_ == null) {
        return java.util.Collections.unmodifiableList(battleRecordMsg_);
      } else {
        return battleRecordMsgBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.BattleRecordMsg battleRecordMsg = 4;</code>
     */
    public int getBattleRecordMsgCount() {
      if (battleRecordMsgBuilder_ == null) {
        return battleRecordMsg_.size();
      } else {
        return battleRecordMsgBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.BattleRecordMsg battleRecordMsg = 4;</code>
     */
    public xddq.pb.BattleRecordMsg getBattleRecordMsg(int index) {
      if (battleRecordMsgBuilder_ == null) {
        return battleRecordMsg_.get(index);
      } else {
        return battleRecordMsgBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.BattleRecordMsg battleRecordMsg = 4;</code>
     */
    public Builder setBattleRecordMsg(
        int index, xddq.pb.BattleRecordMsg value) {
      if (battleRecordMsgBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureBattleRecordMsgIsMutable();
        battleRecordMsg_.set(index, value);
        onChanged();
      } else {
        battleRecordMsgBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.BattleRecordMsg battleRecordMsg = 4;</code>
     */
    public Builder setBattleRecordMsg(
        int index, xddq.pb.BattleRecordMsg.Builder builderForValue) {
      if (battleRecordMsgBuilder_ == null) {
        ensureBattleRecordMsgIsMutable();
        battleRecordMsg_.set(index, builderForValue.build());
        onChanged();
      } else {
        battleRecordMsgBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.BattleRecordMsg battleRecordMsg = 4;</code>
     */
    public Builder addBattleRecordMsg(xddq.pb.BattleRecordMsg value) {
      if (battleRecordMsgBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureBattleRecordMsgIsMutable();
        battleRecordMsg_.add(value);
        onChanged();
      } else {
        battleRecordMsgBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.BattleRecordMsg battleRecordMsg = 4;</code>
     */
    public Builder addBattleRecordMsg(
        int index, xddq.pb.BattleRecordMsg value) {
      if (battleRecordMsgBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureBattleRecordMsgIsMutable();
        battleRecordMsg_.add(index, value);
        onChanged();
      } else {
        battleRecordMsgBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.BattleRecordMsg battleRecordMsg = 4;</code>
     */
    public Builder addBattleRecordMsg(
        xddq.pb.BattleRecordMsg.Builder builderForValue) {
      if (battleRecordMsgBuilder_ == null) {
        ensureBattleRecordMsgIsMutable();
        battleRecordMsg_.add(builderForValue.build());
        onChanged();
      } else {
        battleRecordMsgBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.BattleRecordMsg battleRecordMsg = 4;</code>
     */
    public Builder addBattleRecordMsg(
        int index, xddq.pb.BattleRecordMsg.Builder builderForValue) {
      if (battleRecordMsgBuilder_ == null) {
        ensureBattleRecordMsgIsMutable();
        battleRecordMsg_.add(index, builderForValue.build());
        onChanged();
      } else {
        battleRecordMsgBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.BattleRecordMsg battleRecordMsg = 4;</code>
     */
    public Builder addAllBattleRecordMsg(
        java.lang.Iterable<? extends xddq.pb.BattleRecordMsg> values) {
      if (battleRecordMsgBuilder_ == null) {
        ensureBattleRecordMsgIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, battleRecordMsg_);
        onChanged();
      } else {
        battleRecordMsgBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.BattleRecordMsg battleRecordMsg = 4;</code>
     */
    public Builder clearBattleRecordMsg() {
      if (battleRecordMsgBuilder_ == null) {
        battleRecordMsg_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000008);
        onChanged();
      } else {
        battleRecordMsgBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.BattleRecordMsg battleRecordMsg = 4;</code>
     */
    public Builder removeBattleRecordMsg(int index) {
      if (battleRecordMsgBuilder_ == null) {
        ensureBattleRecordMsgIsMutable();
        battleRecordMsg_.remove(index);
        onChanged();
      } else {
        battleRecordMsgBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.BattleRecordMsg battleRecordMsg = 4;</code>
     */
    public xddq.pb.BattleRecordMsg.Builder getBattleRecordMsgBuilder(
        int index) {
      return internalGetBattleRecordMsgFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.BattleRecordMsg battleRecordMsg = 4;</code>
     */
    public xddq.pb.BattleRecordMsgOrBuilder getBattleRecordMsgOrBuilder(
        int index) {
      if (battleRecordMsgBuilder_ == null) {
        return battleRecordMsg_.get(index);  } else {
        return battleRecordMsgBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.BattleRecordMsg battleRecordMsg = 4;</code>
     */
    public java.util.List<? extends xddq.pb.BattleRecordMsgOrBuilder> 
         getBattleRecordMsgOrBuilderList() {
      if (battleRecordMsgBuilder_ != null) {
        return battleRecordMsgBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(battleRecordMsg_);
      }
    }
    /**
     * <code>repeated .xddq.pb.BattleRecordMsg battleRecordMsg = 4;</code>
     */
    public xddq.pb.BattleRecordMsg.Builder addBattleRecordMsgBuilder() {
      return internalGetBattleRecordMsgFieldBuilder().addBuilder(
          xddq.pb.BattleRecordMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.BattleRecordMsg battleRecordMsg = 4;</code>
     */
    public xddq.pb.BattleRecordMsg.Builder addBattleRecordMsgBuilder(
        int index) {
      return internalGetBattleRecordMsgFieldBuilder().addBuilder(
          index, xddq.pb.BattleRecordMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.BattleRecordMsg battleRecordMsg = 4;</code>
     */
    public java.util.List<xddq.pb.BattleRecordMsg.Builder> 
         getBattleRecordMsgBuilderList() {
      return internalGetBattleRecordMsgFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.BattleRecordMsg, xddq.pb.BattleRecordMsg.Builder, xddq.pb.BattleRecordMsgOrBuilder> 
        internalGetBattleRecordMsgFieldBuilder() {
      if (battleRecordMsgBuilder_ == null) {
        battleRecordMsgBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.BattleRecordMsg, xddq.pb.BattleRecordMsg.Builder, xddq.pb.BattleRecordMsgOrBuilder>(
                battleRecordMsg_,
                ((bitField0_ & 0x00000008) != 0),
                getParentForChildren(),
                isClean());
        battleRecordMsg_ = null;
      }
      return battleRecordMsgBuilder_;
    }

    private com.google.protobuf.Internal.BooleanList winList_ = emptyBooleanList();
    private void ensureWinListIsMutable() {
      if (!winList_.isModifiable()) {
        winList_ = makeMutableCopy(winList_);
      }
      bitField0_ |= 0x00000010;
    }
    private void ensureWinListIsMutable(int capacity) {
      if (!winList_.isModifiable()) {
        winList_ = makeMutableCopy(winList_, capacity);
      }
      bitField0_ |= 0x00000010;
    }
    /**
     * <code>repeated bool winList = 5;</code>
     * @return A list containing the winList.
     */
    public java.util.List<java.lang.Boolean>
        getWinListList() {
      winList_.makeImmutable();
      return winList_;
    }
    /**
     * <code>repeated bool winList = 5;</code>
     * @return The count of winList.
     */
    public int getWinListCount() {
      return winList_.size();
    }
    /**
     * <code>repeated bool winList = 5;</code>
     * @param index The index of the element to return.
     * @return The winList at the given index.
     */
    public boolean getWinList(int index) {
      return winList_.getBoolean(index);
    }
    /**
     * <code>repeated bool winList = 5;</code>
     * @param index The index to set the value at.
     * @param value The winList to set.
     * @return This builder for chaining.
     */
    public Builder setWinList(
        int index, boolean value) {

      ensureWinListIsMutable();
      winList_.setBoolean(index, value);
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>repeated bool winList = 5;</code>
     * @param value The winList to add.
     * @return This builder for chaining.
     */
    public Builder addWinList(boolean value) {

      ensureWinListIsMutable();
      winList_.addBoolean(value);
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>repeated bool winList = 5;</code>
     * @param values The winList to add.
     * @return This builder for chaining.
     */
    public Builder addAllWinList(
        java.lang.Iterable<? extends java.lang.Boolean> values) {
      ensureWinListIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, winList_);
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>repeated bool winList = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearWinList() {
      winList_ = emptyBooleanList();
      bitField0_ = (bitField0_ & ~0x00000010);
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.SpChampBattleReplayResp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.SpChampBattleReplayResp)
  private static final xddq.pb.SpChampBattleReplayResp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.SpChampBattleReplayResp();
  }

  public static xddq.pb.SpChampBattleReplayResp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<SpChampBattleReplayResp>
      PARSER = new com.google.protobuf.AbstractParser<SpChampBattleReplayResp>() {
    @java.lang.Override
    public SpChampBattleReplayResp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<SpChampBattleReplayResp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<SpChampBattleReplayResp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.SpChampBattleReplayResp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

