// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.MemoryCollectPlayerCollectRecordListReq}
 */
public final class MemoryCollectPlayerCollectRecordListReq extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.MemoryCollectPlayerCollectRecordListReq)
    MemoryCollectPlayerCollectRecordListReqOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      MemoryCollectPlayerCollectRecordListReq.class.getName());
  }
  // Use MemoryCollectPlayerCollectRecordListReq.newBuilder() to construct.
  private MemoryCollectPlayerCollectRecordListReq(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private MemoryCollectPlayerCollectRecordListReq() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_MemoryCollectPlayerCollectRecordListReq_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_MemoryCollectPlayerCollectRecordListReq_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.MemoryCollectPlayerCollectRecordListReq.class, xddq.pb.MemoryCollectPlayerCollectRecordListReq.Builder.class);
  }

  private int bitField0_;
  public static final int SEASON_FIELD_NUMBER = 1;
  private int season_ = 0;
  /**
   * <code>required int32 season = 1;</code>
   * @return Whether the season field is set.
   */
  @java.lang.Override
  public boolean hasSeason() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 season = 1;</code>
   * @return The season.
   */
  @java.lang.Override
  public int getSeason() {
    return season_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasSeason()) {
      memoizedIsInitialized = 0;
      return false;
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, season_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, season_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.MemoryCollectPlayerCollectRecordListReq)) {
      return super.equals(obj);
    }
    xddq.pb.MemoryCollectPlayerCollectRecordListReq other = (xddq.pb.MemoryCollectPlayerCollectRecordListReq) obj;

    if (hasSeason() != other.hasSeason()) return false;
    if (hasSeason()) {
      if (getSeason()
          != other.getSeason()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasSeason()) {
      hash = (37 * hash) + SEASON_FIELD_NUMBER;
      hash = (53 * hash) + getSeason();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.MemoryCollectPlayerCollectRecordListReq parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.MemoryCollectPlayerCollectRecordListReq parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.MemoryCollectPlayerCollectRecordListReq parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.MemoryCollectPlayerCollectRecordListReq parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.MemoryCollectPlayerCollectRecordListReq parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.MemoryCollectPlayerCollectRecordListReq parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.MemoryCollectPlayerCollectRecordListReq parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.MemoryCollectPlayerCollectRecordListReq parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.MemoryCollectPlayerCollectRecordListReq parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.MemoryCollectPlayerCollectRecordListReq parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.MemoryCollectPlayerCollectRecordListReq parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.MemoryCollectPlayerCollectRecordListReq parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.MemoryCollectPlayerCollectRecordListReq prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.MemoryCollectPlayerCollectRecordListReq}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.MemoryCollectPlayerCollectRecordListReq)
      xddq.pb.MemoryCollectPlayerCollectRecordListReqOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_MemoryCollectPlayerCollectRecordListReq_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_MemoryCollectPlayerCollectRecordListReq_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.MemoryCollectPlayerCollectRecordListReq.class, xddq.pb.MemoryCollectPlayerCollectRecordListReq.Builder.class);
    }

    // Construct using xddq.pb.MemoryCollectPlayerCollectRecordListReq.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      season_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_MemoryCollectPlayerCollectRecordListReq_descriptor;
    }

    @java.lang.Override
    public xddq.pb.MemoryCollectPlayerCollectRecordListReq getDefaultInstanceForType() {
      return xddq.pb.MemoryCollectPlayerCollectRecordListReq.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.MemoryCollectPlayerCollectRecordListReq build() {
      xddq.pb.MemoryCollectPlayerCollectRecordListReq result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.MemoryCollectPlayerCollectRecordListReq buildPartial() {
      xddq.pb.MemoryCollectPlayerCollectRecordListReq result = new xddq.pb.MemoryCollectPlayerCollectRecordListReq(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.MemoryCollectPlayerCollectRecordListReq result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.season_ = season_;
        to_bitField0_ |= 0x00000001;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.MemoryCollectPlayerCollectRecordListReq) {
        return mergeFrom((xddq.pb.MemoryCollectPlayerCollectRecordListReq)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.MemoryCollectPlayerCollectRecordListReq other) {
      if (other == xddq.pb.MemoryCollectPlayerCollectRecordListReq.getDefaultInstance()) return this;
      if (other.hasSeason()) {
        setSeason(other.getSeason());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasSeason()) {
        return false;
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              season_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int season_ ;
    /**
     * <code>required int32 season = 1;</code>
     * @return Whether the season field is set.
     */
    @java.lang.Override
    public boolean hasSeason() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 season = 1;</code>
     * @return The season.
     */
    @java.lang.Override
    public int getSeason() {
      return season_;
    }
    /**
     * <code>required int32 season = 1;</code>
     * @param value The season to set.
     * @return This builder for chaining.
     */
    public Builder setSeason(int value) {

      season_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 season = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearSeason() {
      bitField0_ = (bitField0_ & ~0x00000001);
      season_ = 0;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.MemoryCollectPlayerCollectRecordListReq)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.MemoryCollectPlayerCollectRecordListReq)
  private static final xddq.pb.MemoryCollectPlayerCollectRecordListReq DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.MemoryCollectPlayerCollectRecordListReq();
  }

  public static xddq.pb.MemoryCollectPlayerCollectRecordListReq getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<MemoryCollectPlayerCollectRecordListReq>
      PARSER = new com.google.protobuf.AbstractParser<MemoryCollectPlayerCollectRecordListReq>() {
    @java.lang.Override
    public MemoryCollectPlayerCollectRecordListReq parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<MemoryCollectPlayerCollectRecordListReq> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<MemoryCollectPlayerCollectRecordListReq> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.MemoryCollectPlayerCollectRecordListReq getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

