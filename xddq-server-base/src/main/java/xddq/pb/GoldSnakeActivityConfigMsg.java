// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.GoldSnakeActivityConfigMsg}
 */
public final class GoldSnakeActivityConfigMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.GoldSnakeActivityConfigMsg)
    GoldSnakeActivityConfigMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      GoldSnakeActivityConfigMsg.class.getName());
  }
  // Use GoldSnakeActivityConfigMsg.newBuilder() to construct.
  private GoldSnakeActivityConfigMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private GoldSnakeActivityConfigMsg() {
    goldSnakeBuffConfigMsgList_ = java.util.Collections.emptyList();
    goldSnakeConfigMsgList_ = java.util.Collections.emptyList();
    goldSnakeGourdConfigMsgList_ = java.util.Collections.emptyList();
    goldSnakeStageConfigMsgList_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_GoldSnakeActivityConfigMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_GoldSnakeActivityConfigMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.GoldSnakeActivityConfigMsg.class, xddq.pb.GoldSnakeActivityConfigMsg.Builder.class);
  }

  public static final int GOLDSNAKEBUFFCONFIGMSGLIST_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.GoldSnakeBuffConfigMsg> goldSnakeBuffConfigMsgList_;
  /**
   * <code>repeated .xddq.pb.GoldSnakeBuffConfigMsg goldSnakeBuffConfigMsgList = 1;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.GoldSnakeBuffConfigMsg> getGoldSnakeBuffConfigMsgListList() {
    return goldSnakeBuffConfigMsgList_;
  }
  /**
   * <code>repeated .xddq.pb.GoldSnakeBuffConfigMsg goldSnakeBuffConfigMsgList = 1;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.GoldSnakeBuffConfigMsgOrBuilder> 
      getGoldSnakeBuffConfigMsgListOrBuilderList() {
    return goldSnakeBuffConfigMsgList_;
  }
  /**
   * <code>repeated .xddq.pb.GoldSnakeBuffConfigMsg goldSnakeBuffConfigMsgList = 1;</code>
   */
  @java.lang.Override
  public int getGoldSnakeBuffConfigMsgListCount() {
    return goldSnakeBuffConfigMsgList_.size();
  }
  /**
   * <code>repeated .xddq.pb.GoldSnakeBuffConfigMsg goldSnakeBuffConfigMsgList = 1;</code>
   */
  @java.lang.Override
  public xddq.pb.GoldSnakeBuffConfigMsg getGoldSnakeBuffConfigMsgList(int index) {
    return goldSnakeBuffConfigMsgList_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.GoldSnakeBuffConfigMsg goldSnakeBuffConfigMsgList = 1;</code>
   */
  @java.lang.Override
  public xddq.pb.GoldSnakeBuffConfigMsgOrBuilder getGoldSnakeBuffConfigMsgListOrBuilder(
      int index) {
    return goldSnakeBuffConfigMsgList_.get(index);
  }

  public static final int GOLDSNAKECONFIGMSGLIST_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.GoldSnakeConfigMsg> goldSnakeConfigMsgList_;
  /**
   * <code>repeated .xddq.pb.GoldSnakeConfigMsg goldSnakeConfigMsgList = 2;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.GoldSnakeConfigMsg> getGoldSnakeConfigMsgListList() {
    return goldSnakeConfigMsgList_;
  }
  /**
   * <code>repeated .xddq.pb.GoldSnakeConfigMsg goldSnakeConfigMsgList = 2;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.GoldSnakeConfigMsgOrBuilder> 
      getGoldSnakeConfigMsgListOrBuilderList() {
    return goldSnakeConfigMsgList_;
  }
  /**
   * <code>repeated .xddq.pb.GoldSnakeConfigMsg goldSnakeConfigMsgList = 2;</code>
   */
  @java.lang.Override
  public int getGoldSnakeConfigMsgListCount() {
    return goldSnakeConfigMsgList_.size();
  }
  /**
   * <code>repeated .xddq.pb.GoldSnakeConfigMsg goldSnakeConfigMsgList = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.GoldSnakeConfigMsg getGoldSnakeConfigMsgList(int index) {
    return goldSnakeConfigMsgList_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.GoldSnakeConfigMsg goldSnakeConfigMsgList = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.GoldSnakeConfigMsgOrBuilder getGoldSnakeConfigMsgListOrBuilder(
      int index) {
    return goldSnakeConfigMsgList_.get(index);
  }

  public static final int GOLDSNAKEGOURDCONFIGMSGLIST_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.GoldSnakeGourdConfigMsg> goldSnakeGourdConfigMsgList_;
  /**
   * <code>repeated .xddq.pb.GoldSnakeGourdConfigMsg goldSnakeGourdConfigMsgList = 3;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.GoldSnakeGourdConfigMsg> getGoldSnakeGourdConfigMsgListList() {
    return goldSnakeGourdConfigMsgList_;
  }
  /**
   * <code>repeated .xddq.pb.GoldSnakeGourdConfigMsg goldSnakeGourdConfigMsgList = 3;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.GoldSnakeGourdConfigMsgOrBuilder> 
      getGoldSnakeGourdConfigMsgListOrBuilderList() {
    return goldSnakeGourdConfigMsgList_;
  }
  /**
   * <code>repeated .xddq.pb.GoldSnakeGourdConfigMsg goldSnakeGourdConfigMsgList = 3;</code>
   */
  @java.lang.Override
  public int getGoldSnakeGourdConfigMsgListCount() {
    return goldSnakeGourdConfigMsgList_.size();
  }
  /**
   * <code>repeated .xddq.pb.GoldSnakeGourdConfigMsg goldSnakeGourdConfigMsgList = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.GoldSnakeGourdConfigMsg getGoldSnakeGourdConfigMsgList(int index) {
    return goldSnakeGourdConfigMsgList_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.GoldSnakeGourdConfigMsg goldSnakeGourdConfigMsgList = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.GoldSnakeGourdConfigMsgOrBuilder getGoldSnakeGourdConfigMsgListOrBuilder(
      int index) {
    return goldSnakeGourdConfigMsgList_.get(index);
  }

  public static final int GOLDSNAKESTAGECONFIGMSGLIST_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.GoldSnakeStageConfigMsg> goldSnakeStageConfigMsgList_;
  /**
   * <code>repeated .xddq.pb.GoldSnakeStageConfigMsg goldSnakeStageConfigMsgList = 4;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.GoldSnakeStageConfigMsg> getGoldSnakeStageConfigMsgListList() {
    return goldSnakeStageConfigMsgList_;
  }
  /**
   * <code>repeated .xddq.pb.GoldSnakeStageConfigMsg goldSnakeStageConfigMsgList = 4;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.GoldSnakeStageConfigMsgOrBuilder> 
      getGoldSnakeStageConfigMsgListOrBuilderList() {
    return goldSnakeStageConfigMsgList_;
  }
  /**
   * <code>repeated .xddq.pb.GoldSnakeStageConfigMsg goldSnakeStageConfigMsgList = 4;</code>
   */
  @java.lang.Override
  public int getGoldSnakeStageConfigMsgListCount() {
    return goldSnakeStageConfigMsgList_.size();
  }
  /**
   * <code>repeated .xddq.pb.GoldSnakeStageConfigMsg goldSnakeStageConfigMsgList = 4;</code>
   */
  @java.lang.Override
  public xddq.pb.GoldSnakeStageConfigMsg getGoldSnakeStageConfigMsgList(int index) {
    return goldSnakeStageConfigMsgList_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.GoldSnakeStageConfigMsg goldSnakeStageConfigMsgList = 4;</code>
   */
  @java.lang.Override
  public xddq.pb.GoldSnakeStageConfigMsgOrBuilder getGoldSnakeStageConfigMsgListOrBuilder(
      int index) {
    return goldSnakeStageConfigMsgList_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    for (int i = 0; i < goldSnakeBuffConfigMsgList_.size(); i++) {
      output.writeMessage(1, goldSnakeBuffConfigMsgList_.get(i));
    }
    for (int i = 0; i < goldSnakeConfigMsgList_.size(); i++) {
      output.writeMessage(2, goldSnakeConfigMsgList_.get(i));
    }
    for (int i = 0; i < goldSnakeGourdConfigMsgList_.size(); i++) {
      output.writeMessage(3, goldSnakeGourdConfigMsgList_.get(i));
    }
    for (int i = 0; i < goldSnakeStageConfigMsgList_.size(); i++) {
      output.writeMessage(4, goldSnakeStageConfigMsgList_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    for (int i = 0; i < goldSnakeBuffConfigMsgList_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, goldSnakeBuffConfigMsgList_.get(i));
    }
    for (int i = 0; i < goldSnakeConfigMsgList_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, goldSnakeConfigMsgList_.get(i));
    }
    for (int i = 0; i < goldSnakeGourdConfigMsgList_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, goldSnakeGourdConfigMsgList_.get(i));
    }
    for (int i = 0; i < goldSnakeStageConfigMsgList_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(4, goldSnakeStageConfigMsgList_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.GoldSnakeActivityConfigMsg)) {
      return super.equals(obj);
    }
    xddq.pb.GoldSnakeActivityConfigMsg other = (xddq.pb.GoldSnakeActivityConfigMsg) obj;

    if (!getGoldSnakeBuffConfigMsgListList()
        .equals(other.getGoldSnakeBuffConfigMsgListList())) return false;
    if (!getGoldSnakeConfigMsgListList()
        .equals(other.getGoldSnakeConfigMsgListList())) return false;
    if (!getGoldSnakeGourdConfigMsgListList()
        .equals(other.getGoldSnakeGourdConfigMsgListList())) return false;
    if (!getGoldSnakeStageConfigMsgListList()
        .equals(other.getGoldSnakeStageConfigMsgListList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (getGoldSnakeBuffConfigMsgListCount() > 0) {
      hash = (37 * hash) + GOLDSNAKEBUFFCONFIGMSGLIST_FIELD_NUMBER;
      hash = (53 * hash) + getGoldSnakeBuffConfigMsgListList().hashCode();
    }
    if (getGoldSnakeConfigMsgListCount() > 0) {
      hash = (37 * hash) + GOLDSNAKECONFIGMSGLIST_FIELD_NUMBER;
      hash = (53 * hash) + getGoldSnakeConfigMsgListList().hashCode();
    }
    if (getGoldSnakeGourdConfigMsgListCount() > 0) {
      hash = (37 * hash) + GOLDSNAKEGOURDCONFIGMSGLIST_FIELD_NUMBER;
      hash = (53 * hash) + getGoldSnakeGourdConfigMsgListList().hashCode();
    }
    if (getGoldSnakeStageConfigMsgListCount() > 0) {
      hash = (37 * hash) + GOLDSNAKESTAGECONFIGMSGLIST_FIELD_NUMBER;
      hash = (53 * hash) + getGoldSnakeStageConfigMsgListList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.GoldSnakeActivityConfigMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GoldSnakeActivityConfigMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GoldSnakeActivityConfigMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GoldSnakeActivityConfigMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GoldSnakeActivityConfigMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GoldSnakeActivityConfigMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GoldSnakeActivityConfigMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.GoldSnakeActivityConfigMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.GoldSnakeActivityConfigMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.GoldSnakeActivityConfigMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.GoldSnakeActivityConfigMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.GoldSnakeActivityConfigMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.GoldSnakeActivityConfigMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.GoldSnakeActivityConfigMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.GoldSnakeActivityConfigMsg)
      xddq.pb.GoldSnakeActivityConfigMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GoldSnakeActivityConfigMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GoldSnakeActivityConfigMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.GoldSnakeActivityConfigMsg.class, xddq.pb.GoldSnakeActivityConfigMsg.Builder.class);
    }

    // Construct using xddq.pb.GoldSnakeActivityConfigMsg.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      if (goldSnakeBuffConfigMsgListBuilder_ == null) {
        goldSnakeBuffConfigMsgList_ = java.util.Collections.emptyList();
      } else {
        goldSnakeBuffConfigMsgList_ = null;
        goldSnakeBuffConfigMsgListBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000001);
      if (goldSnakeConfigMsgListBuilder_ == null) {
        goldSnakeConfigMsgList_ = java.util.Collections.emptyList();
      } else {
        goldSnakeConfigMsgList_ = null;
        goldSnakeConfigMsgListBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000002);
      if (goldSnakeGourdConfigMsgListBuilder_ == null) {
        goldSnakeGourdConfigMsgList_ = java.util.Collections.emptyList();
      } else {
        goldSnakeGourdConfigMsgList_ = null;
        goldSnakeGourdConfigMsgListBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000004);
      if (goldSnakeStageConfigMsgListBuilder_ == null) {
        goldSnakeStageConfigMsgList_ = java.util.Collections.emptyList();
      } else {
        goldSnakeStageConfigMsgList_ = null;
        goldSnakeStageConfigMsgListBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000008);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GoldSnakeActivityConfigMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.GoldSnakeActivityConfigMsg getDefaultInstanceForType() {
      return xddq.pb.GoldSnakeActivityConfigMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.GoldSnakeActivityConfigMsg build() {
      xddq.pb.GoldSnakeActivityConfigMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.GoldSnakeActivityConfigMsg buildPartial() {
      xddq.pb.GoldSnakeActivityConfigMsg result = new xddq.pb.GoldSnakeActivityConfigMsg(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.GoldSnakeActivityConfigMsg result) {
      if (goldSnakeBuffConfigMsgListBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          goldSnakeBuffConfigMsgList_ = java.util.Collections.unmodifiableList(goldSnakeBuffConfigMsgList_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.goldSnakeBuffConfigMsgList_ = goldSnakeBuffConfigMsgList_;
      } else {
        result.goldSnakeBuffConfigMsgList_ = goldSnakeBuffConfigMsgListBuilder_.build();
      }
      if (goldSnakeConfigMsgListBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          goldSnakeConfigMsgList_ = java.util.Collections.unmodifiableList(goldSnakeConfigMsgList_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.goldSnakeConfigMsgList_ = goldSnakeConfigMsgList_;
      } else {
        result.goldSnakeConfigMsgList_ = goldSnakeConfigMsgListBuilder_.build();
      }
      if (goldSnakeGourdConfigMsgListBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0)) {
          goldSnakeGourdConfigMsgList_ = java.util.Collections.unmodifiableList(goldSnakeGourdConfigMsgList_);
          bitField0_ = (bitField0_ & ~0x00000004);
        }
        result.goldSnakeGourdConfigMsgList_ = goldSnakeGourdConfigMsgList_;
      } else {
        result.goldSnakeGourdConfigMsgList_ = goldSnakeGourdConfigMsgListBuilder_.build();
      }
      if (goldSnakeStageConfigMsgListBuilder_ == null) {
        if (((bitField0_ & 0x00000008) != 0)) {
          goldSnakeStageConfigMsgList_ = java.util.Collections.unmodifiableList(goldSnakeStageConfigMsgList_);
          bitField0_ = (bitField0_ & ~0x00000008);
        }
        result.goldSnakeStageConfigMsgList_ = goldSnakeStageConfigMsgList_;
      } else {
        result.goldSnakeStageConfigMsgList_ = goldSnakeStageConfigMsgListBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.GoldSnakeActivityConfigMsg result) {
      int from_bitField0_ = bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.GoldSnakeActivityConfigMsg) {
        return mergeFrom((xddq.pb.GoldSnakeActivityConfigMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.GoldSnakeActivityConfigMsg other) {
      if (other == xddq.pb.GoldSnakeActivityConfigMsg.getDefaultInstance()) return this;
      if (goldSnakeBuffConfigMsgListBuilder_ == null) {
        if (!other.goldSnakeBuffConfigMsgList_.isEmpty()) {
          if (goldSnakeBuffConfigMsgList_.isEmpty()) {
            goldSnakeBuffConfigMsgList_ = other.goldSnakeBuffConfigMsgList_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureGoldSnakeBuffConfigMsgListIsMutable();
            goldSnakeBuffConfigMsgList_.addAll(other.goldSnakeBuffConfigMsgList_);
          }
          onChanged();
        }
      } else {
        if (!other.goldSnakeBuffConfigMsgList_.isEmpty()) {
          if (goldSnakeBuffConfigMsgListBuilder_.isEmpty()) {
            goldSnakeBuffConfigMsgListBuilder_.dispose();
            goldSnakeBuffConfigMsgListBuilder_ = null;
            goldSnakeBuffConfigMsgList_ = other.goldSnakeBuffConfigMsgList_;
            bitField0_ = (bitField0_ & ~0x00000001);
            goldSnakeBuffConfigMsgListBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetGoldSnakeBuffConfigMsgListFieldBuilder() : null;
          } else {
            goldSnakeBuffConfigMsgListBuilder_.addAllMessages(other.goldSnakeBuffConfigMsgList_);
          }
        }
      }
      if (goldSnakeConfigMsgListBuilder_ == null) {
        if (!other.goldSnakeConfigMsgList_.isEmpty()) {
          if (goldSnakeConfigMsgList_.isEmpty()) {
            goldSnakeConfigMsgList_ = other.goldSnakeConfigMsgList_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureGoldSnakeConfigMsgListIsMutable();
            goldSnakeConfigMsgList_.addAll(other.goldSnakeConfigMsgList_);
          }
          onChanged();
        }
      } else {
        if (!other.goldSnakeConfigMsgList_.isEmpty()) {
          if (goldSnakeConfigMsgListBuilder_.isEmpty()) {
            goldSnakeConfigMsgListBuilder_.dispose();
            goldSnakeConfigMsgListBuilder_ = null;
            goldSnakeConfigMsgList_ = other.goldSnakeConfigMsgList_;
            bitField0_ = (bitField0_ & ~0x00000002);
            goldSnakeConfigMsgListBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetGoldSnakeConfigMsgListFieldBuilder() : null;
          } else {
            goldSnakeConfigMsgListBuilder_.addAllMessages(other.goldSnakeConfigMsgList_);
          }
        }
      }
      if (goldSnakeGourdConfigMsgListBuilder_ == null) {
        if (!other.goldSnakeGourdConfigMsgList_.isEmpty()) {
          if (goldSnakeGourdConfigMsgList_.isEmpty()) {
            goldSnakeGourdConfigMsgList_ = other.goldSnakeGourdConfigMsgList_;
            bitField0_ = (bitField0_ & ~0x00000004);
          } else {
            ensureGoldSnakeGourdConfigMsgListIsMutable();
            goldSnakeGourdConfigMsgList_.addAll(other.goldSnakeGourdConfigMsgList_);
          }
          onChanged();
        }
      } else {
        if (!other.goldSnakeGourdConfigMsgList_.isEmpty()) {
          if (goldSnakeGourdConfigMsgListBuilder_.isEmpty()) {
            goldSnakeGourdConfigMsgListBuilder_.dispose();
            goldSnakeGourdConfigMsgListBuilder_ = null;
            goldSnakeGourdConfigMsgList_ = other.goldSnakeGourdConfigMsgList_;
            bitField0_ = (bitField0_ & ~0x00000004);
            goldSnakeGourdConfigMsgListBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetGoldSnakeGourdConfigMsgListFieldBuilder() : null;
          } else {
            goldSnakeGourdConfigMsgListBuilder_.addAllMessages(other.goldSnakeGourdConfigMsgList_);
          }
        }
      }
      if (goldSnakeStageConfigMsgListBuilder_ == null) {
        if (!other.goldSnakeStageConfigMsgList_.isEmpty()) {
          if (goldSnakeStageConfigMsgList_.isEmpty()) {
            goldSnakeStageConfigMsgList_ = other.goldSnakeStageConfigMsgList_;
            bitField0_ = (bitField0_ & ~0x00000008);
          } else {
            ensureGoldSnakeStageConfigMsgListIsMutable();
            goldSnakeStageConfigMsgList_.addAll(other.goldSnakeStageConfigMsgList_);
          }
          onChanged();
        }
      } else {
        if (!other.goldSnakeStageConfigMsgList_.isEmpty()) {
          if (goldSnakeStageConfigMsgListBuilder_.isEmpty()) {
            goldSnakeStageConfigMsgListBuilder_.dispose();
            goldSnakeStageConfigMsgListBuilder_ = null;
            goldSnakeStageConfigMsgList_ = other.goldSnakeStageConfigMsgList_;
            bitField0_ = (bitField0_ & ~0x00000008);
            goldSnakeStageConfigMsgListBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetGoldSnakeStageConfigMsgListFieldBuilder() : null;
          } else {
            goldSnakeStageConfigMsgListBuilder_.addAllMessages(other.goldSnakeStageConfigMsgList_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              xddq.pb.GoldSnakeBuffConfigMsg m =
                  input.readMessage(
                      xddq.pb.GoldSnakeBuffConfigMsg.parser(),
                      extensionRegistry);
              if (goldSnakeBuffConfigMsgListBuilder_ == null) {
                ensureGoldSnakeBuffConfigMsgListIsMutable();
                goldSnakeBuffConfigMsgList_.add(m);
              } else {
                goldSnakeBuffConfigMsgListBuilder_.addMessage(m);
              }
              break;
            } // case 10
            case 18: {
              xddq.pb.GoldSnakeConfigMsg m =
                  input.readMessage(
                      xddq.pb.GoldSnakeConfigMsg.parser(),
                      extensionRegistry);
              if (goldSnakeConfigMsgListBuilder_ == null) {
                ensureGoldSnakeConfigMsgListIsMutable();
                goldSnakeConfigMsgList_.add(m);
              } else {
                goldSnakeConfigMsgListBuilder_.addMessage(m);
              }
              break;
            } // case 18
            case 26: {
              xddq.pb.GoldSnakeGourdConfigMsg m =
                  input.readMessage(
                      xddq.pb.GoldSnakeGourdConfigMsg.parser(),
                      extensionRegistry);
              if (goldSnakeGourdConfigMsgListBuilder_ == null) {
                ensureGoldSnakeGourdConfigMsgListIsMutable();
                goldSnakeGourdConfigMsgList_.add(m);
              } else {
                goldSnakeGourdConfigMsgListBuilder_.addMessage(m);
              }
              break;
            } // case 26
            case 34: {
              xddq.pb.GoldSnakeStageConfigMsg m =
                  input.readMessage(
                      xddq.pb.GoldSnakeStageConfigMsg.parser(),
                      extensionRegistry);
              if (goldSnakeStageConfigMsgListBuilder_ == null) {
                ensureGoldSnakeStageConfigMsgListIsMutable();
                goldSnakeStageConfigMsgList_.add(m);
              } else {
                goldSnakeStageConfigMsgListBuilder_.addMessage(m);
              }
              break;
            } // case 34
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.util.List<xddq.pb.GoldSnakeBuffConfigMsg> goldSnakeBuffConfigMsgList_ =
      java.util.Collections.emptyList();
    private void ensureGoldSnakeBuffConfigMsgListIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        goldSnakeBuffConfigMsgList_ = new java.util.ArrayList<xddq.pb.GoldSnakeBuffConfigMsg>(goldSnakeBuffConfigMsgList_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.GoldSnakeBuffConfigMsg, xddq.pb.GoldSnakeBuffConfigMsg.Builder, xddq.pb.GoldSnakeBuffConfigMsgOrBuilder> goldSnakeBuffConfigMsgListBuilder_;

    /**
     * <code>repeated .xddq.pb.GoldSnakeBuffConfigMsg goldSnakeBuffConfigMsgList = 1;</code>
     */
    public java.util.List<xddq.pb.GoldSnakeBuffConfigMsg> getGoldSnakeBuffConfigMsgListList() {
      if (goldSnakeBuffConfigMsgListBuilder_ == null) {
        return java.util.Collections.unmodifiableList(goldSnakeBuffConfigMsgList_);
      } else {
        return goldSnakeBuffConfigMsgListBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeBuffConfigMsg goldSnakeBuffConfigMsgList = 1;</code>
     */
    public int getGoldSnakeBuffConfigMsgListCount() {
      if (goldSnakeBuffConfigMsgListBuilder_ == null) {
        return goldSnakeBuffConfigMsgList_.size();
      } else {
        return goldSnakeBuffConfigMsgListBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeBuffConfigMsg goldSnakeBuffConfigMsgList = 1;</code>
     */
    public xddq.pb.GoldSnakeBuffConfigMsg getGoldSnakeBuffConfigMsgList(int index) {
      if (goldSnakeBuffConfigMsgListBuilder_ == null) {
        return goldSnakeBuffConfigMsgList_.get(index);
      } else {
        return goldSnakeBuffConfigMsgListBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeBuffConfigMsg goldSnakeBuffConfigMsgList = 1;</code>
     */
    public Builder setGoldSnakeBuffConfigMsgList(
        int index, xddq.pb.GoldSnakeBuffConfigMsg value) {
      if (goldSnakeBuffConfigMsgListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureGoldSnakeBuffConfigMsgListIsMutable();
        goldSnakeBuffConfigMsgList_.set(index, value);
        onChanged();
      } else {
        goldSnakeBuffConfigMsgListBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeBuffConfigMsg goldSnakeBuffConfigMsgList = 1;</code>
     */
    public Builder setGoldSnakeBuffConfigMsgList(
        int index, xddq.pb.GoldSnakeBuffConfigMsg.Builder builderForValue) {
      if (goldSnakeBuffConfigMsgListBuilder_ == null) {
        ensureGoldSnakeBuffConfigMsgListIsMutable();
        goldSnakeBuffConfigMsgList_.set(index, builderForValue.build());
        onChanged();
      } else {
        goldSnakeBuffConfigMsgListBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeBuffConfigMsg goldSnakeBuffConfigMsgList = 1;</code>
     */
    public Builder addGoldSnakeBuffConfigMsgList(xddq.pb.GoldSnakeBuffConfigMsg value) {
      if (goldSnakeBuffConfigMsgListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureGoldSnakeBuffConfigMsgListIsMutable();
        goldSnakeBuffConfigMsgList_.add(value);
        onChanged();
      } else {
        goldSnakeBuffConfigMsgListBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeBuffConfigMsg goldSnakeBuffConfigMsgList = 1;</code>
     */
    public Builder addGoldSnakeBuffConfigMsgList(
        int index, xddq.pb.GoldSnakeBuffConfigMsg value) {
      if (goldSnakeBuffConfigMsgListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureGoldSnakeBuffConfigMsgListIsMutable();
        goldSnakeBuffConfigMsgList_.add(index, value);
        onChanged();
      } else {
        goldSnakeBuffConfigMsgListBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeBuffConfigMsg goldSnakeBuffConfigMsgList = 1;</code>
     */
    public Builder addGoldSnakeBuffConfigMsgList(
        xddq.pb.GoldSnakeBuffConfigMsg.Builder builderForValue) {
      if (goldSnakeBuffConfigMsgListBuilder_ == null) {
        ensureGoldSnakeBuffConfigMsgListIsMutable();
        goldSnakeBuffConfigMsgList_.add(builderForValue.build());
        onChanged();
      } else {
        goldSnakeBuffConfigMsgListBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeBuffConfigMsg goldSnakeBuffConfigMsgList = 1;</code>
     */
    public Builder addGoldSnakeBuffConfigMsgList(
        int index, xddq.pb.GoldSnakeBuffConfigMsg.Builder builderForValue) {
      if (goldSnakeBuffConfigMsgListBuilder_ == null) {
        ensureGoldSnakeBuffConfigMsgListIsMutable();
        goldSnakeBuffConfigMsgList_.add(index, builderForValue.build());
        onChanged();
      } else {
        goldSnakeBuffConfigMsgListBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeBuffConfigMsg goldSnakeBuffConfigMsgList = 1;</code>
     */
    public Builder addAllGoldSnakeBuffConfigMsgList(
        java.lang.Iterable<? extends xddq.pb.GoldSnakeBuffConfigMsg> values) {
      if (goldSnakeBuffConfigMsgListBuilder_ == null) {
        ensureGoldSnakeBuffConfigMsgListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, goldSnakeBuffConfigMsgList_);
        onChanged();
      } else {
        goldSnakeBuffConfigMsgListBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeBuffConfigMsg goldSnakeBuffConfigMsgList = 1;</code>
     */
    public Builder clearGoldSnakeBuffConfigMsgList() {
      if (goldSnakeBuffConfigMsgListBuilder_ == null) {
        goldSnakeBuffConfigMsgList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        goldSnakeBuffConfigMsgListBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeBuffConfigMsg goldSnakeBuffConfigMsgList = 1;</code>
     */
    public Builder removeGoldSnakeBuffConfigMsgList(int index) {
      if (goldSnakeBuffConfigMsgListBuilder_ == null) {
        ensureGoldSnakeBuffConfigMsgListIsMutable();
        goldSnakeBuffConfigMsgList_.remove(index);
        onChanged();
      } else {
        goldSnakeBuffConfigMsgListBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeBuffConfigMsg goldSnakeBuffConfigMsgList = 1;</code>
     */
    public xddq.pb.GoldSnakeBuffConfigMsg.Builder getGoldSnakeBuffConfigMsgListBuilder(
        int index) {
      return internalGetGoldSnakeBuffConfigMsgListFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeBuffConfigMsg goldSnakeBuffConfigMsgList = 1;</code>
     */
    public xddq.pb.GoldSnakeBuffConfigMsgOrBuilder getGoldSnakeBuffConfigMsgListOrBuilder(
        int index) {
      if (goldSnakeBuffConfigMsgListBuilder_ == null) {
        return goldSnakeBuffConfigMsgList_.get(index);  } else {
        return goldSnakeBuffConfigMsgListBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeBuffConfigMsg goldSnakeBuffConfigMsgList = 1;</code>
     */
    public java.util.List<? extends xddq.pb.GoldSnakeBuffConfigMsgOrBuilder> 
         getGoldSnakeBuffConfigMsgListOrBuilderList() {
      if (goldSnakeBuffConfigMsgListBuilder_ != null) {
        return goldSnakeBuffConfigMsgListBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(goldSnakeBuffConfigMsgList_);
      }
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeBuffConfigMsg goldSnakeBuffConfigMsgList = 1;</code>
     */
    public xddq.pb.GoldSnakeBuffConfigMsg.Builder addGoldSnakeBuffConfigMsgListBuilder() {
      return internalGetGoldSnakeBuffConfigMsgListFieldBuilder().addBuilder(
          xddq.pb.GoldSnakeBuffConfigMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeBuffConfigMsg goldSnakeBuffConfigMsgList = 1;</code>
     */
    public xddq.pb.GoldSnakeBuffConfigMsg.Builder addGoldSnakeBuffConfigMsgListBuilder(
        int index) {
      return internalGetGoldSnakeBuffConfigMsgListFieldBuilder().addBuilder(
          index, xddq.pb.GoldSnakeBuffConfigMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeBuffConfigMsg goldSnakeBuffConfigMsgList = 1;</code>
     */
    public java.util.List<xddq.pb.GoldSnakeBuffConfigMsg.Builder> 
         getGoldSnakeBuffConfigMsgListBuilderList() {
      return internalGetGoldSnakeBuffConfigMsgListFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.GoldSnakeBuffConfigMsg, xddq.pb.GoldSnakeBuffConfigMsg.Builder, xddq.pb.GoldSnakeBuffConfigMsgOrBuilder> 
        internalGetGoldSnakeBuffConfigMsgListFieldBuilder() {
      if (goldSnakeBuffConfigMsgListBuilder_ == null) {
        goldSnakeBuffConfigMsgListBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.GoldSnakeBuffConfigMsg, xddq.pb.GoldSnakeBuffConfigMsg.Builder, xddq.pb.GoldSnakeBuffConfigMsgOrBuilder>(
                goldSnakeBuffConfigMsgList_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        goldSnakeBuffConfigMsgList_ = null;
      }
      return goldSnakeBuffConfigMsgListBuilder_;
    }

    private java.util.List<xddq.pb.GoldSnakeConfigMsg> goldSnakeConfigMsgList_ =
      java.util.Collections.emptyList();
    private void ensureGoldSnakeConfigMsgListIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        goldSnakeConfigMsgList_ = new java.util.ArrayList<xddq.pb.GoldSnakeConfigMsg>(goldSnakeConfigMsgList_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.GoldSnakeConfigMsg, xddq.pb.GoldSnakeConfigMsg.Builder, xddq.pb.GoldSnakeConfigMsgOrBuilder> goldSnakeConfigMsgListBuilder_;

    /**
     * <code>repeated .xddq.pb.GoldSnakeConfigMsg goldSnakeConfigMsgList = 2;</code>
     */
    public java.util.List<xddq.pb.GoldSnakeConfigMsg> getGoldSnakeConfigMsgListList() {
      if (goldSnakeConfigMsgListBuilder_ == null) {
        return java.util.Collections.unmodifiableList(goldSnakeConfigMsgList_);
      } else {
        return goldSnakeConfigMsgListBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeConfigMsg goldSnakeConfigMsgList = 2;</code>
     */
    public int getGoldSnakeConfigMsgListCount() {
      if (goldSnakeConfigMsgListBuilder_ == null) {
        return goldSnakeConfigMsgList_.size();
      } else {
        return goldSnakeConfigMsgListBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeConfigMsg goldSnakeConfigMsgList = 2;</code>
     */
    public xddq.pb.GoldSnakeConfigMsg getGoldSnakeConfigMsgList(int index) {
      if (goldSnakeConfigMsgListBuilder_ == null) {
        return goldSnakeConfigMsgList_.get(index);
      } else {
        return goldSnakeConfigMsgListBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeConfigMsg goldSnakeConfigMsgList = 2;</code>
     */
    public Builder setGoldSnakeConfigMsgList(
        int index, xddq.pb.GoldSnakeConfigMsg value) {
      if (goldSnakeConfigMsgListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureGoldSnakeConfigMsgListIsMutable();
        goldSnakeConfigMsgList_.set(index, value);
        onChanged();
      } else {
        goldSnakeConfigMsgListBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeConfigMsg goldSnakeConfigMsgList = 2;</code>
     */
    public Builder setGoldSnakeConfigMsgList(
        int index, xddq.pb.GoldSnakeConfigMsg.Builder builderForValue) {
      if (goldSnakeConfigMsgListBuilder_ == null) {
        ensureGoldSnakeConfigMsgListIsMutable();
        goldSnakeConfigMsgList_.set(index, builderForValue.build());
        onChanged();
      } else {
        goldSnakeConfigMsgListBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeConfigMsg goldSnakeConfigMsgList = 2;</code>
     */
    public Builder addGoldSnakeConfigMsgList(xddq.pb.GoldSnakeConfigMsg value) {
      if (goldSnakeConfigMsgListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureGoldSnakeConfigMsgListIsMutable();
        goldSnakeConfigMsgList_.add(value);
        onChanged();
      } else {
        goldSnakeConfigMsgListBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeConfigMsg goldSnakeConfigMsgList = 2;</code>
     */
    public Builder addGoldSnakeConfigMsgList(
        int index, xddq.pb.GoldSnakeConfigMsg value) {
      if (goldSnakeConfigMsgListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureGoldSnakeConfigMsgListIsMutable();
        goldSnakeConfigMsgList_.add(index, value);
        onChanged();
      } else {
        goldSnakeConfigMsgListBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeConfigMsg goldSnakeConfigMsgList = 2;</code>
     */
    public Builder addGoldSnakeConfigMsgList(
        xddq.pb.GoldSnakeConfigMsg.Builder builderForValue) {
      if (goldSnakeConfigMsgListBuilder_ == null) {
        ensureGoldSnakeConfigMsgListIsMutable();
        goldSnakeConfigMsgList_.add(builderForValue.build());
        onChanged();
      } else {
        goldSnakeConfigMsgListBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeConfigMsg goldSnakeConfigMsgList = 2;</code>
     */
    public Builder addGoldSnakeConfigMsgList(
        int index, xddq.pb.GoldSnakeConfigMsg.Builder builderForValue) {
      if (goldSnakeConfigMsgListBuilder_ == null) {
        ensureGoldSnakeConfigMsgListIsMutable();
        goldSnakeConfigMsgList_.add(index, builderForValue.build());
        onChanged();
      } else {
        goldSnakeConfigMsgListBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeConfigMsg goldSnakeConfigMsgList = 2;</code>
     */
    public Builder addAllGoldSnakeConfigMsgList(
        java.lang.Iterable<? extends xddq.pb.GoldSnakeConfigMsg> values) {
      if (goldSnakeConfigMsgListBuilder_ == null) {
        ensureGoldSnakeConfigMsgListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, goldSnakeConfigMsgList_);
        onChanged();
      } else {
        goldSnakeConfigMsgListBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeConfigMsg goldSnakeConfigMsgList = 2;</code>
     */
    public Builder clearGoldSnakeConfigMsgList() {
      if (goldSnakeConfigMsgListBuilder_ == null) {
        goldSnakeConfigMsgList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        goldSnakeConfigMsgListBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeConfigMsg goldSnakeConfigMsgList = 2;</code>
     */
    public Builder removeGoldSnakeConfigMsgList(int index) {
      if (goldSnakeConfigMsgListBuilder_ == null) {
        ensureGoldSnakeConfigMsgListIsMutable();
        goldSnakeConfigMsgList_.remove(index);
        onChanged();
      } else {
        goldSnakeConfigMsgListBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeConfigMsg goldSnakeConfigMsgList = 2;</code>
     */
    public xddq.pb.GoldSnakeConfigMsg.Builder getGoldSnakeConfigMsgListBuilder(
        int index) {
      return internalGetGoldSnakeConfigMsgListFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeConfigMsg goldSnakeConfigMsgList = 2;</code>
     */
    public xddq.pb.GoldSnakeConfigMsgOrBuilder getGoldSnakeConfigMsgListOrBuilder(
        int index) {
      if (goldSnakeConfigMsgListBuilder_ == null) {
        return goldSnakeConfigMsgList_.get(index);  } else {
        return goldSnakeConfigMsgListBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeConfigMsg goldSnakeConfigMsgList = 2;</code>
     */
    public java.util.List<? extends xddq.pb.GoldSnakeConfigMsgOrBuilder> 
         getGoldSnakeConfigMsgListOrBuilderList() {
      if (goldSnakeConfigMsgListBuilder_ != null) {
        return goldSnakeConfigMsgListBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(goldSnakeConfigMsgList_);
      }
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeConfigMsg goldSnakeConfigMsgList = 2;</code>
     */
    public xddq.pb.GoldSnakeConfigMsg.Builder addGoldSnakeConfigMsgListBuilder() {
      return internalGetGoldSnakeConfigMsgListFieldBuilder().addBuilder(
          xddq.pb.GoldSnakeConfigMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeConfigMsg goldSnakeConfigMsgList = 2;</code>
     */
    public xddq.pb.GoldSnakeConfigMsg.Builder addGoldSnakeConfigMsgListBuilder(
        int index) {
      return internalGetGoldSnakeConfigMsgListFieldBuilder().addBuilder(
          index, xddq.pb.GoldSnakeConfigMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeConfigMsg goldSnakeConfigMsgList = 2;</code>
     */
    public java.util.List<xddq.pb.GoldSnakeConfigMsg.Builder> 
         getGoldSnakeConfigMsgListBuilderList() {
      return internalGetGoldSnakeConfigMsgListFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.GoldSnakeConfigMsg, xddq.pb.GoldSnakeConfigMsg.Builder, xddq.pb.GoldSnakeConfigMsgOrBuilder> 
        internalGetGoldSnakeConfigMsgListFieldBuilder() {
      if (goldSnakeConfigMsgListBuilder_ == null) {
        goldSnakeConfigMsgListBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.GoldSnakeConfigMsg, xddq.pb.GoldSnakeConfigMsg.Builder, xddq.pb.GoldSnakeConfigMsgOrBuilder>(
                goldSnakeConfigMsgList_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        goldSnakeConfigMsgList_ = null;
      }
      return goldSnakeConfigMsgListBuilder_;
    }

    private java.util.List<xddq.pb.GoldSnakeGourdConfigMsg> goldSnakeGourdConfigMsgList_ =
      java.util.Collections.emptyList();
    private void ensureGoldSnakeGourdConfigMsgListIsMutable() {
      if (!((bitField0_ & 0x00000004) != 0)) {
        goldSnakeGourdConfigMsgList_ = new java.util.ArrayList<xddq.pb.GoldSnakeGourdConfigMsg>(goldSnakeGourdConfigMsgList_);
        bitField0_ |= 0x00000004;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.GoldSnakeGourdConfigMsg, xddq.pb.GoldSnakeGourdConfigMsg.Builder, xddq.pb.GoldSnakeGourdConfigMsgOrBuilder> goldSnakeGourdConfigMsgListBuilder_;

    /**
     * <code>repeated .xddq.pb.GoldSnakeGourdConfigMsg goldSnakeGourdConfigMsgList = 3;</code>
     */
    public java.util.List<xddq.pb.GoldSnakeGourdConfigMsg> getGoldSnakeGourdConfigMsgListList() {
      if (goldSnakeGourdConfigMsgListBuilder_ == null) {
        return java.util.Collections.unmodifiableList(goldSnakeGourdConfigMsgList_);
      } else {
        return goldSnakeGourdConfigMsgListBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeGourdConfigMsg goldSnakeGourdConfigMsgList = 3;</code>
     */
    public int getGoldSnakeGourdConfigMsgListCount() {
      if (goldSnakeGourdConfigMsgListBuilder_ == null) {
        return goldSnakeGourdConfigMsgList_.size();
      } else {
        return goldSnakeGourdConfigMsgListBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeGourdConfigMsg goldSnakeGourdConfigMsgList = 3;</code>
     */
    public xddq.pb.GoldSnakeGourdConfigMsg getGoldSnakeGourdConfigMsgList(int index) {
      if (goldSnakeGourdConfigMsgListBuilder_ == null) {
        return goldSnakeGourdConfigMsgList_.get(index);
      } else {
        return goldSnakeGourdConfigMsgListBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeGourdConfigMsg goldSnakeGourdConfigMsgList = 3;</code>
     */
    public Builder setGoldSnakeGourdConfigMsgList(
        int index, xddq.pb.GoldSnakeGourdConfigMsg value) {
      if (goldSnakeGourdConfigMsgListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureGoldSnakeGourdConfigMsgListIsMutable();
        goldSnakeGourdConfigMsgList_.set(index, value);
        onChanged();
      } else {
        goldSnakeGourdConfigMsgListBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeGourdConfigMsg goldSnakeGourdConfigMsgList = 3;</code>
     */
    public Builder setGoldSnakeGourdConfigMsgList(
        int index, xddq.pb.GoldSnakeGourdConfigMsg.Builder builderForValue) {
      if (goldSnakeGourdConfigMsgListBuilder_ == null) {
        ensureGoldSnakeGourdConfigMsgListIsMutable();
        goldSnakeGourdConfigMsgList_.set(index, builderForValue.build());
        onChanged();
      } else {
        goldSnakeGourdConfigMsgListBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeGourdConfigMsg goldSnakeGourdConfigMsgList = 3;</code>
     */
    public Builder addGoldSnakeGourdConfigMsgList(xddq.pb.GoldSnakeGourdConfigMsg value) {
      if (goldSnakeGourdConfigMsgListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureGoldSnakeGourdConfigMsgListIsMutable();
        goldSnakeGourdConfigMsgList_.add(value);
        onChanged();
      } else {
        goldSnakeGourdConfigMsgListBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeGourdConfigMsg goldSnakeGourdConfigMsgList = 3;</code>
     */
    public Builder addGoldSnakeGourdConfigMsgList(
        int index, xddq.pb.GoldSnakeGourdConfigMsg value) {
      if (goldSnakeGourdConfigMsgListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureGoldSnakeGourdConfigMsgListIsMutable();
        goldSnakeGourdConfigMsgList_.add(index, value);
        onChanged();
      } else {
        goldSnakeGourdConfigMsgListBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeGourdConfigMsg goldSnakeGourdConfigMsgList = 3;</code>
     */
    public Builder addGoldSnakeGourdConfigMsgList(
        xddq.pb.GoldSnakeGourdConfigMsg.Builder builderForValue) {
      if (goldSnakeGourdConfigMsgListBuilder_ == null) {
        ensureGoldSnakeGourdConfigMsgListIsMutable();
        goldSnakeGourdConfigMsgList_.add(builderForValue.build());
        onChanged();
      } else {
        goldSnakeGourdConfigMsgListBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeGourdConfigMsg goldSnakeGourdConfigMsgList = 3;</code>
     */
    public Builder addGoldSnakeGourdConfigMsgList(
        int index, xddq.pb.GoldSnakeGourdConfigMsg.Builder builderForValue) {
      if (goldSnakeGourdConfigMsgListBuilder_ == null) {
        ensureGoldSnakeGourdConfigMsgListIsMutable();
        goldSnakeGourdConfigMsgList_.add(index, builderForValue.build());
        onChanged();
      } else {
        goldSnakeGourdConfigMsgListBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeGourdConfigMsg goldSnakeGourdConfigMsgList = 3;</code>
     */
    public Builder addAllGoldSnakeGourdConfigMsgList(
        java.lang.Iterable<? extends xddq.pb.GoldSnakeGourdConfigMsg> values) {
      if (goldSnakeGourdConfigMsgListBuilder_ == null) {
        ensureGoldSnakeGourdConfigMsgListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, goldSnakeGourdConfigMsgList_);
        onChanged();
      } else {
        goldSnakeGourdConfigMsgListBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeGourdConfigMsg goldSnakeGourdConfigMsgList = 3;</code>
     */
    public Builder clearGoldSnakeGourdConfigMsgList() {
      if (goldSnakeGourdConfigMsgListBuilder_ == null) {
        goldSnakeGourdConfigMsgList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
      } else {
        goldSnakeGourdConfigMsgListBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeGourdConfigMsg goldSnakeGourdConfigMsgList = 3;</code>
     */
    public Builder removeGoldSnakeGourdConfigMsgList(int index) {
      if (goldSnakeGourdConfigMsgListBuilder_ == null) {
        ensureGoldSnakeGourdConfigMsgListIsMutable();
        goldSnakeGourdConfigMsgList_.remove(index);
        onChanged();
      } else {
        goldSnakeGourdConfigMsgListBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeGourdConfigMsg goldSnakeGourdConfigMsgList = 3;</code>
     */
    public xddq.pb.GoldSnakeGourdConfigMsg.Builder getGoldSnakeGourdConfigMsgListBuilder(
        int index) {
      return internalGetGoldSnakeGourdConfigMsgListFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeGourdConfigMsg goldSnakeGourdConfigMsgList = 3;</code>
     */
    public xddq.pb.GoldSnakeGourdConfigMsgOrBuilder getGoldSnakeGourdConfigMsgListOrBuilder(
        int index) {
      if (goldSnakeGourdConfigMsgListBuilder_ == null) {
        return goldSnakeGourdConfigMsgList_.get(index);  } else {
        return goldSnakeGourdConfigMsgListBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeGourdConfigMsg goldSnakeGourdConfigMsgList = 3;</code>
     */
    public java.util.List<? extends xddq.pb.GoldSnakeGourdConfigMsgOrBuilder> 
         getGoldSnakeGourdConfigMsgListOrBuilderList() {
      if (goldSnakeGourdConfigMsgListBuilder_ != null) {
        return goldSnakeGourdConfigMsgListBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(goldSnakeGourdConfigMsgList_);
      }
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeGourdConfigMsg goldSnakeGourdConfigMsgList = 3;</code>
     */
    public xddq.pb.GoldSnakeGourdConfigMsg.Builder addGoldSnakeGourdConfigMsgListBuilder() {
      return internalGetGoldSnakeGourdConfigMsgListFieldBuilder().addBuilder(
          xddq.pb.GoldSnakeGourdConfigMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeGourdConfigMsg goldSnakeGourdConfigMsgList = 3;</code>
     */
    public xddq.pb.GoldSnakeGourdConfigMsg.Builder addGoldSnakeGourdConfigMsgListBuilder(
        int index) {
      return internalGetGoldSnakeGourdConfigMsgListFieldBuilder().addBuilder(
          index, xddq.pb.GoldSnakeGourdConfigMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeGourdConfigMsg goldSnakeGourdConfigMsgList = 3;</code>
     */
    public java.util.List<xddq.pb.GoldSnakeGourdConfigMsg.Builder> 
         getGoldSnakeGourdConfigMsgListBuilderList() {
      return internalGetGoldSnakeGourdConfigMsgListFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.GoldSnakeGourdConfigMsg, xddq.pb.GoldSnakeGourdConfigMsg.Builder, xddq.pb.GoldSnakeGourdConfigMsgOrBuilder> 
        internalGetGoldSnakeGourdConfigMsgListFieldBuilder() {
      if (goldSnakeGourdConfigMsgListBuilder_ == null) {
        goldSnakeGourdConfigMsgListBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.GoldSnakeGourdConfigMsg, xddq.pb.GoldSnakeGourdConfigMsg.Builder, xddq.pb.GoldSnakeGourdConfigMsgOrBuilder>(
                goldSnakeGourdConfigMsgList_,
                ((bitField0_ & 0x00000004) != 0),
                getParentForChildren(),
                isClean());
        goldSnakeGourdConfigMsgList_ = null;
      }
      return goldSnakeGourdConfigMsgListBuilder_;
    }

    private java.util.List<xddq.pb.GoldSnakeStageConfigMsg> goldSnakeStageConfigMsgList_ =
      java.util.Collections.emptyList();
    private void ensureGoldSnakeStageConfigMsgListIsMutable() {
      if (!((bitField0_ & 0x00000008) != 0)) {
        goldSnakeStageConfigMsgList_ = new java.util.ArrayList<xddq.pb.GoldSnakeStageConfigMsg>(goldSnakeStageConfigMsgList_);
        bitField0_ |= 0x00000008;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.GoldSnakeStageConfigMsg, xddq.pb.GoldSnakeStageConfigMsg.Builder, xddq.pb.GoldSnakeStageConfigMsgOrBuilder> goldSnakeStageConfigMsgListBuilder_;

    /**
     * <code>repeated .xddq.pb.GoldSnakeStageConfigMsg goldSnakeStageConfigMsgList = 4;</code>
     */
    public java.util.List<xddq.pb.GoldSnakeStageConfigMsg> getGoldSnakeStageConfigMsgListList() {
      if (goldSnakeStageConfigMsgListBuilder_ == null) {
        return java.util.Collections.unmodifiableList(goldSnakeStageConfigMsgList_);
      } else {
        return goldSnakeStageConfigMsgListBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeStageConfigMsg goldSnakeStageConfigMsgList = 4;</code>
     */
    public int getGoldSnakeStageConfigMsgListCount() {
      if (goldSnakeStageConfigMsgListBuilder_ == null) {
        return goldSnakeStageConfigMsgList_.size();
      } else {
        return goldSnakeStageConfigMsgListBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeStageConfigMsg goldSnakeStageConfigMsgList = 4;</code>
     */
    public xddq.pb.GoldSnakeStageConfigMsg getGoldSnakeStageConfigMsgList(int index) {
      if (goldSnakeStageConfigMsgListBuilder_ == null) {
        return goldSnakeStageConfigMsgList_.get(index);
      } else {
        return goldSnakeStageConfigMsgListBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeStageConfigMsg goldSnakeStageConfigMsgList = 4;</code>
     */
    public Builder setGoldSnakeStageConfigMsgList(
        int index, xddq.pb.GoldSnakeStageConfigMsg value) {
      if (goldSnakeStageConfigMsgListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureGoldSnakeStageConfigMsgListIsMutable();
        goldSnakeStageConfigMsgList_.set(index, value);
        onChanged();
      } else {
        goldSnakeStageConfigMsgListBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeStageConfigMsg goldSnakeStageConfigMsgList = 4;</code>
     */
    public Builder setGoldSnakeStageConfigMsgList(
        int index, xddq.pb.GoldSnakeStageConfigMsg.Builder builderForValue) {
      if (goldSnakeStageConfigMsgListBuilder_ == null) {
        ensureGoldSnakeStageConfigMsgListIsMutable();
        goldSnakeStageConfigMsgList_.set(index, builderForValue.build());
        onChanged();
      } else {
        goldSnakeStageConfigMsgListBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeStageConfigMsg goldSnakeStageConfigMsgList = 4;</code>
     */
    public Builder addGoldSnakeStageConfigMsgList(xddq.pb.GoldSnakeStageConfigMsg value) {
      if (goldSnakeStageConfigMsgListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureGoldSnakeStageConfigMsgListIsMutable();
        goldSnakeStageConfigMsgList_.add(value);
        onChanged();
      } else {
        goldSnakeStageConfigMsgListBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeStageConfigMsg goldSnakeStageConfigMsgList = 4;</code>
     */
    public Builder addGoldSnakeStageConfigMsgList(
        int index, xddq.pb.GoldSnakeStageConfigMsg value) {
      if (goldSnakeStageConfigMsgListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureGoldSnakeStageConfigMsgListIsMutable();
        goldSnakeStageConfigMsgList_.add(index, value);
        onChanged();
      } else {
        goldSnakeStageConfigMsgListBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeStageConfigMsg goldSnakeStageConfigMsgList = 4;</code>
     */
    public Builder addGoldSnakeStageConfigMsgList(
        xddq.pb.GoldSnakeStageConfigMsg.Builder builderForValue) {
      if (goldSnakeStageConfigMsgListBuilder_ == null) {
        ensureGoldSnakeStageConfigMsgListIsMutable();
        goldSnakeStageConfigMsgList_.add(builderForValue.build());
        onChanged();
      } else {
        goldSnakeStageConfigMsgListBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeStageConfigMsg goldSnakeStageConfigMsgList = 4;</code>
     */
    public Builder addGoldSnakeStageConfigMsgList(
        int index, xddq.pb.GoldSnakeStageConfigMsg.Builder builderForValue) {
      if (goldSnakeStageConfigMsgListBuilder_ == null) {
        ensureGoldSnakeStageConfigMsgListIsMutable();
        goldSnakeStageConfigMsgList_.add(index, builderForValue.build());
        onChanged();
      } else {
        goldSnakeStageConfigMsgListBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeStageConfigMsg goldSnakeStageConfigMsgList = 4;</code>
     */
    public Builder addAllGoldSnakeStageConfigMsgList(
        java.lang.Iterable<? extends xddq.pb.GoldSnakeStageConfigMsg> values) {
      if (goldSnakeStageConfigMsgListBuilder_ == null) {
        ensureGoldSnakeStageConfigMsgListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, goldSnakeStageConfigMsgList_);
        onChanged();
      } else {
        goldSnakeStageConfigMsgListBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeStageConfigMsg goldSnakeStageConfigMsgList = 4;</code>
     */
    public Builder clearGoldSnakeStageConfigMsgList() {
      if (goldSnakeStageConfigMsgListBuilder_ == null) {
        goldSnakeStageConfigMsgList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000008);
        onChanged();
      } else {
        goldSnakeStageConfigMsgListBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeStageConfigMsg goldSnakeStageConfigMsgList = 4;</code>
     */
    public Builder removeGoldSnakeStageConfigMsgList(int index) {
      if (goldSnakeStageConfigMsgListBuilder_ == null) {
        ensureGoldSnakeStageConfigMsgListIsMutable();
        goldSnakeStageConfigMsgList_.remove(index);
        onChanged();
      } else {
        goldSnakeStageConfigMsgListBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeStageConfigMsg goldSnakeStageConfigMsgList = 4;</code>
     */
    public xddq.pb.GoldSnakeStageConfigMsg.Builder getGoldSnakeStageConfigMsgListBuilder(
        int index) {
      return internalGetGoldSnakeStageConfigMsgListFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeStageConfigMsg goldSnakeStageConfigMsgList = 4;</code>
     */
    public xddq.pb.GoldSnakeStageConfigMsgOrBuilder getGoldSnakeStageConfigMsgListOrBuilder(
        int index) {
      if (goldSnakeStageConfigMsgListBuilder_ == null) {
        return goldSnakeStageConfigMsgList_.get(index);  } else {
        return goldSnakeStageConfigMsgListBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeStageConfigMsg goldSnakeStageConfigMsgList = 4;</code>
     */
    public java.util.List<? extends xddq.pb.GoldSnakeStageConfigMsgOrBuilder> 
         getGoldSnakeStageConfigMsgListOrBuilderList() {
      if (goldSnakeStageConfigMsgListBuilder_ != null) {
        return goldSnakeStageConfigMsgListBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(goldSnakeStageConfigMsgList_);
      }
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeStageConfigMsg goldSnakeStageConfigMsgList = 4;</code>
     */
    public xddq.pb.GoldSnakeStageConfigMsg.Builder addGoldSnakeStageConfigMsgListBuilder() {
      return internalGetGoldSnakeStageConfigMsgListFieldBuilder().addBuilder(
          xddq.pb.GoldSnakeStageConfigMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeStageConfigMsg goldSnakeStageConfigMsgList = 4;</code>
     */
    public xddq.pb.GoldSnakeStageConfigMsg.Builder addGoldSnakeStageConfigMsgListBuilder(
        int index) {
      return internalGetGoldSnakeStageConfigMsgListFieldBuilder().addBuilder(
          index, xddq.pb.GoldSnakeStageConfigMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.GoldSnakeStageConfigMsg goldSnakeStageConfigMsgList = 4;</code>
     */
    public java.util.List<xddq.pb.GoldSnakeStageConfigMsg.Builder> 
         getGoldSnakeStageConfigMsgListBuilderList() {
      return internalGetGoldSnakeStageConfigMsgListFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.GoldSnakeStageConfigMsg, xddq.pb.GoldSnakeStageConfigMsg.Builder, xddq.pb.GoldSnakeStageConfigMsgOrBuilder> 
        internalGetGoldSnakeStageConfigMsgListFieldBuilder() {
      if (goldSnakeStageConfigMsgListBuilder_ == null) {
        goldSnakeStageConfigMsgListBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.GoldSnakeStageConfigMsg, xddq.pb.GoldSnakeStageConfigMsg.Builder, xddq.pb.GoldSnakeStageConfigMsgOrBuilder>(
                goldSnakeStageConfigMsgList_,
                ((bitField0_ & 0x00000008) != 0),
                getParentForChildren(),
                isClean());
        goldSnakeStageConfigMsgList_ = null;
      }
      return goldSnakeStageConfigMsgListBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.GoldSnakeActivityConfigMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.GoldSnakeActivityConfigMsg)
  private static final xddq.pb.GoldSnakeActivityConfigMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.GoldSnakeActivityConfigMsg();
  }

  public static xddq.pb.GoldSnakeActivityConfigMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<GoldSnakeActivityConfigMsg>
      PARSER = new com.google.protobuf.AbstractParser<GoldSnakeActivityConfigMsg>() {
    @java.lang.Override
    public GoldSnakeActivityConfigMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<GoldSnakeActivityConfigMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<GoldSnakeActivityConfigMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.GoldSnakeActivityConfigMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

