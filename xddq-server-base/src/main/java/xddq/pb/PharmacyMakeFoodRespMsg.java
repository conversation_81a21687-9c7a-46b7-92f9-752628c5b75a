// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.PharmacyMakeFoodRespMsg}
 */
public final class PharmacyMakeFoodRespMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.PharmacyMakeFoodRespMsg)
    PharmacyMakeFoodRespMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      PharmacyMakeFoodRespMsg.class.getName());
  }
  // Use PharmacyMakeFoodRespMsg.newBuilder() to construct.
  private PharmacyMakeFoodRespMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private PharmacyMakeFoodRespMsg() {
    foodList_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_PharmacyMakeFoodRespMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_PharmacyMakeFoodRespMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.PharmacyMakeFoodRespMsg.class, xddq.pb.PharmacyMakeFoodRespMsg.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int FOODLIST_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.PharmacyFoodTempMsg> foodList_;
  /**
   * <code>repeated .xddq.pb.PharmacyFoodTempMsg foodList = 2;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.PharmacyFoodTempMsg> getFoodListList() {
    return foodList_;
  }
  /**
   * <code>repeated .xddq.pb.PharmacyFoodTempMsg foodList = 2;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.PharmacyFoodTempMsgOrBuilder> 
      getFoodListOrBuilderList() {
    return foodList_;
  }
  /**
   * <code>repeated .xddq.pb.PharmacyFoodTempMsg foodList = 2;</code>
   */
  @java.lang.Override
  public int getFoodListCount() {
    return foodList_.size();
  }
  /**
   * <code>repeated .xddq.pb.PharmacyFoodTempMsg foodList = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.PharmacyFoodTempMsg getFoodList(int index) {
    return foodList_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.PharmacyFoodTempMsg foodList = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.PharmacyFoodTempMsgOrBuilder getFoodListOrBuilder(
      int index) {
    return foodList_.get(index);
  }

  public static final int SELFTABLE_FIELD_NUMBER = 3;
  private xddq.pb.PharmacySelfTableSyncMsg selfTable_;
  /**
   * <code>optional .xddq.pb.PharmacySelfTableSyncMsg selfTable = 3;</code>
   * @return Whether the selfTable field is set.
   */
  @java.lang.Override
  public boolean hasSelfTable() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional .xddq.pb.PharmacySelfTableSyncMsg selfTable = 3;</code>
   * @return The selfTable.
   */
  @java.lang.Override
  public xddq.pb.PharmacySelfTableSyncMsg getSelfTable() {
    return selfTable_ == null ? xddq.pb.PharmacySelfTableSyncMsg.getDefaultInstance() : selfTable_;
  }
  /**
   * <code>optional .xddq.pb.PharmacySelfTableSyncMsg selfTable = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.PharmacySelfTableSyncMsgOrBuilder getSelfTableOrBuilder() {
    return selfTable_ == null ? xddq.pb.PharmacySelfTableSyncMsg.getDefaultInstance() : selfTable_;
  }

  public static final int HP_FIELD_NUMBER = 4;
  private int hp_ = 0;
  /**
   * <code>optional int32 hp = 4;</code>
   * @return Whether the hp field is set.
   */
  @java.lang.Override
  public boolean hasHp() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional int32 hp = 4;</code>
   * @return The hp.
   */
  @java.lang.Override
  public int getHp() {
    return hp_;
  }

  public static final int HPTIME_FIELD_NUMBER = 5;
  private long hpTime_ = 0L;
  /**
   * <code>optional int64 hpTime = 5;</code>
   * @return Whether the hpTime field is set.
   */
  @java.lang.Override
  public boolean hasHpTime() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional int64 hpTime = 5;</code>
   * @return The hpTime.
   */
  @java.lang.Override
  public long getHpTime() {
    return hpTime_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    for (int i = 0; i < getFoodListCount(); i++) {
      if (!getFoodList(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    if (hasSelfTable()) {
      if (!getSelfTable().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    for (int i = 0; i < foodList_.size(); i++) {
      output.writeMessage(2, foodList_.get(i));
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeMessage(3, getSelfTable());
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt32(4, hp_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt64(5, hpTime_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    for (int i = 0; i < foodList_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, foodList_.get(i));
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, getSelfTable());
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(4, hp_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(5, hpTime_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.PharmacyMakeFoodRespMsg)) {
      return super.equals(obj);
    }
    xddq.pb.PharmacyMakeFoodRespMsg other = (xddq.pb.PharmacyMakeFoodRespMsg) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (!getFoodListList()
        .equals(other.getFoodListList())) return false;
    if (hasSelfTable() != other.hasSelfTable()) return false;
    if (hasSelfTable()) {
      if (!getSelfTable()
          .equals(other.getSelfTable())) return false;
    }
    if (hasHp() != other.hasHp()) return false;
    if (hasHp()) {
      if (getHp()
          != other.getHp()) return false;
    }
    if (hasHpTime() != other.hasHpTime()) return false;
    if (hasHpTime()) {
      if (getHpTime()
          != other.getHpTime()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (getFoodListCount() > 0) {
      hash = (37 * hash) + FOODLIST_FIELD_NUMBER;
      hash = (53 * hash) + getFoodListList().hashCode();
    }
    if (hasSelfTable()) {
      hash = (37 * hash) + SELFTABLE_FIELD_NUMBER;
      hash = (53 * hash) + getSelfTable().hashCode();
    }
    if (hasHp()) {
      hash = (37 * hash) + HP_FIELD_NUMBER;
      hash = (53 * hash) + getHp();
    }
    if (hasHpTime()) {
      hash = (37 * hash) + HPTIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getHpTime());
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.PharmacyMakeFoodRespMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PharmacyMakeFoodRespMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PharmacyMakeFoodRespMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PharmacyMakeFoodRespMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PharmacyMakeFoodRespMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PharmacyMakeFoodRespMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PharmacyMakeFoodRespMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.PharmacyMakeFoodRespMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.PharmacyMakeFoodRespMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.PharmacyMakeFoodRespMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.PharmacyMakeFoodRespMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.PharmacyMakeFoodRespMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.PharmacyMakeFoodRespMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.PharmacyMakeFoodRespMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.PharmacyMakeFoodRespMsg)
      xddq.pb.PharmacyMakeFoodRespMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PharmacyMakeFoodRespMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PharmacyMakeFoodRespMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.PharmacyMakeFoodRespMsg.class, xddq.pb.PharmacyMakeFoodRespMsg.Builder.class);
    }

    // Construct using xddq.pb.PharmacyMakeFoodRespMsg.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetFoodListFieldBuilder();
        internalGetSelfTableFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      if (foodListBuilder_ == null) {
        foodList_ = java.util.Collections.emptyList();
      } else {
        foodList_ = null;
        foodListBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000002);
      selfTable_ = null;
      if (selfTableBuilder_ != null) {
        selfTableBuilder_.dispose();
        selfTableBuilder_ = null;
      }
      hp_ = 0;
      hpTime_ = 0L;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PharmacyMakeFoodRespMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.PharmacyMakeFoodRespMsg getDefaultInstanceForType() {
      return xddq.pb.PharmacyMakeFoodRespMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.PharmacyMakeFoodRespMsg build() {
      xddq.pb.PharmacyMakeFoodRespMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.PharmacyMakeFoodRespMsg buildPartial() {
      xddq.pb.PharmacyMakeFoodRespMsg result = new xddq.pb.PharmacyMakeFoodRespMsg(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.PharmacyMakeFoodRespMsg result) {
      if (foodListBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          foodList_ = java.util.Collections.unmodifiableList(foodList_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.foodList_ = foodList_;
      } else {
        result.foodList_ = foodListBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.PharmacyMakeFoodRespMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.selfTable_ = selfTableBuilder_ == null
            ? selfTable_
            : selfTableBuilder_.build();
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.hp_ = hp_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.hpTime_ = hpTime_;
        to_bitField0_ |= 0x00000008;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.PharmacyMakeFoodRespMsg) {
        return mergeFrom((xddq.pb.PharmacyMakeFoodRespMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.PharmacyMakeFoodRespMsg other) {
      if (other == xddq.pb.PharmacyMakeFoodRespMsg.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (foodListBuilder_ == null) {
        if (!other.foodList_.isEmpty()) {
          if (foodList_.isEmpty()) {
            foodList_ = other.foodList_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureFoodListIsMutable();
            foodList_.addAll(other.foodList_);
          }
          onChanged();
        }
      } else {
        if (!other.foodList_.isEmpty()) {
          if (foodListBuilder_.isEmpty()) {
            foodListBuilder_.dispose();
            foodListBuilder_ = null;
            foodList_ = other.foodList_;
            bitField0_ = (bitField0_ & ~0x00000002);
            foodListBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetFoodListFieldBuilder() : null;
          } else {
            foodListBuilder_.addAllMessages(other.foodList_);
          }
        }
      }
      if (other.hasSelfTable()) {
        mergeSelfTable(other.getSelfTable());
      }
      if (other.hasHp()) {
        setHp(other.getHp());
      }
      if (other.hasHpTime()) {
        setHpTime(other.getHpTime());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      for (int i = 0; i < getFoodListCount(); i++) {
        if (!getFoodList(i).isInitialized()) {
          return false;
        }
      }
      if (hasSelfTable()) {
        if (!getSelfTable().isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              xddq.pb.PharmacyFoodTempMsg m =
                  input.readMessage(
                      xddq.pb.PharmacyFoodTempMsg.parser(),
                      extensionRegistry);
              if (foodListBuilder_ == null) {
                ensureFoodListIsMutable();
                foodList_.add(m);
              } else {
                foodListBuilder_.addMessage(m);
              }
              break;
            } // case 18
            case 26: {
              input.readMessage(
                  internalGetSelfTableFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 32: {
              hp_ = input.readInt32();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            case 40: {
              hpTime_ = input.readInt64();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.PharmacyFoodTempMsg> foodList_ =
      java.util.Collections.emptyList();
    private void ensureFoodListIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        foodList_ = new java.util.ArrayList<xddq.pb.PharmacyFoodTempMsg>(foodList_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.PharmacyFoodTempMsg, xddq.pb.PharmacyFoodTempMsg.Builder, xddq.pb.PharmacyFoodTempMsgOrBuilder> foodListBuilder_;

    /**
     * <code>repeated .xddq.pb.PharmacyFoodTempMsg foodList = 2;</code>
     */
    public java.util.List<xddq.pb.PharmacyFoodTempMsg> getFoodListList() {
      if (foodListBuilder_ == null) {
        return java.util.Collections.unmodifiableList(foodList_);
      } else {
        return foodListBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.PharmacyFoodTempMsg foodList = 2;</code>
     */
    public int getFoodListCount() {
      if (foodListBuilder_ == null) {
        return foodList_.size();
      } else {
        return foodListBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.PharmacyFoodTempMsg foodList = 2;</code>
     */
    public xddq.pb.PharmacyFoodTempMsg getFoodList(int index) {
      if (foodListBuilder_ == null) {
        return foodList_.get(index);
      } else {
        return foodListBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.PharmacyFoodTempMsg foodList = 2;</code>
     */
    public Builder setFoodList(
        int index, xddq.pb.PharmacyFoodTempMsg value) {
      if (foodListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureFoodListIsMutable();
        foodList_.set(index, value);
        onChanged();
      } else {
        foodListBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PharmacyFoodTempMsg foodList = 2;</code>
     */
    public Builder setFoodList(
        int index, xddq.pb.PharmacyFoodTempMsg.Builder builderForValue) {
      if (foodListBuilder_ == null) {
        ensureFoodListIsMutable();
        foodList_.set(index, builderForValue.build());
        onChanged();
      } else {
        foodListBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PharmacyFoodTempMsg foodList = 2;</code>
     */
    public Builder addFoodList(xddq.pb.PharmacyFoodTempMsg value) {
      if (foodListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureFoodListIsMutable();
        foodList_.add(value);
        onChanged();
      } else {
        foodListBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PharmacyFoodTempMsg foodList = 2;</code>
     */
    public Builder addFoodList(
        int index, xddq.pb.PharmacyFoodTempMsg value) {
      if (foodListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureFoodListIsMutable();
        foodList_.add(index, value);
        onChanged();
      } else {
        foodListBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PharmacyFoodTempMsg foodList = 2;</code>
     */
    public Builder addFoodList(
        xddq.pb.PharmacyFoodTempMsg.Builder builderForValue) {
      if (foodListBuilder_ == null) {
        ensureFoodListIsMutable();
        foodList_.add(builderForValue.build());
        onChanged();
      } else {
        foodListBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PharmacyFoodTempMsg foodList = 2;</code>
     */
    public Builder addFoodList(
        int index, xddq.pb.PharmacyFoodTempMsg.Builder builderForValue) {
      if (foodListBuilder_ == null) {
        ensureFoodListIsMutable();
        foodList_.add(index, builderForValue.build());
        onChanged();
      } else {
        foodListBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PharmacyFoodTempMsg foodList = 2;</code>
     */
    public Builder addAllFoodList(
        java.lang.Iterable<? extends xddq.pb.PharmacyFoodTempMsg> values) {
      if (foodListBuilder_ == null) {
        ensureFoodListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, foodList_);
        onChanged();
      } else {
        foodListBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PharmacyFoodTempMsg foodList = 2;</code>
     */
    public Builder clearFoodList() {
      if (foodListBuilder_ == null) {
        foodList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        foodListBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PharmacyFoodTempMsg foodList = 2;</code>
     */
    public Builder removeFoodList(int index) {
      if (foodListBuilder_ == null) {
        ensureFoodListIsMutable();
        foodList_.remove(index);
        onChanged();
      } else {
        foodListBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PharmacyFoodTempMsg foodList = 2;</code>
     */
    public xddq.pb.PharmacyFoodTempMsg.Builder getFoodListBuilder(
        int index) {
      return internalGetFoodListFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.PharmacyFoodTempMsg foodList = 2;</code>
     */
    public xddq.pb.PharmacyFoodTempMsgOrBuilder getFoodListOrBuilder(
        int index) {
      if (foodListBuilder_ == null) {
        return foodList_.get(index);  } else {
        return foodListBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.PharmacyFoodTempMsg foodList = 2;</code>
     */
    public java.util.List<? extends xddq.pb.PharmacyFoodTempMsgOrBuilder> 
         getFoodListOrBuilderList() {
      if (foodListBuilder_ != null) {
        return foodListBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(foodList_);
      }
    }
    /**
     * <code>repeated .xddq.pb.PharmacyFoodTempMsg foodList = 2;</code>
     */
    public xddq.pb.PharmacyFoodTempMsg.Builder addFoodListBuilder() {
      return internalGetFoodListFieldBuilder().addBuilder(
          xddq.pb.PharmacyFoodTempMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.PharmacyFoodTempMsg foodList = 2;</code>
     */
    public xddq.pb.PharmacyFoodTempMsg.Builder addFoodListBuilder(
        int index) {
      return internalGetFoodListFieldBuilder().addBuilder(
          index, xddq.pb.PharmacyFoodTempMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.PharmacyFoodTempMsg foodList = 2;</code>
     */
    public java.util.List<xddq.pb.PharmacyFoodTempMsg.Builder> 
         getFoodListBuilderList() {
      return internalGetFoodListFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.PharmacyFoodTempMsg, xddq.pb.PharmacyFoodTempMsg.Builder, xddq.pb.PharmacyFoodTempMsgOrBuilder> 
        internalGetFoodListFieldBuilder() {
      if (foodListBuilder_ == null) {
        foodListBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.PharmacyFoodTempMsg, xddq.pb.PharmacyFoodTempMsg.Builder, xddq.pb.PharmacyFoodTempMsgOrBuilder>(
                foodList_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        foodList_ = null;
      }
      return foodListBuilder_;
    }

    private xddq.pb.PharmacySelfTableSyncMsg selfTable_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PharmacySelfTableSyncMsg, xddq.pb.PharmacySelfTableSyncMsg.Builder, xddq.pb.PharmacySelfTableSyncMsgOrBuilder> selfTableBuilder_;
    /**
     * <code>optional .xddq.pb.PharmacySelfTableSyncMsg selfTable = 3;</code>
     * @return Whether the selfTable field is set.
     */
    public boolean hasSelfTable() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional .xddq.pb.PharmacySelfTableSyncMsg selfTable = 3;</code>
     * @return The selfTable.
     */
    public xddq.pb.PharmacySelfTableSyncMsg getSelfTable() {
      if (selfTableBuilder_ == null) {
        return selfTable_ == null ? xddq.pb.PharmacySelfTableSyncMsg.getDefaultInstance() : selfTable_;
      } else {
        return selfTableBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.PharmacySelfTableSyncMsg selfTable = 3;</code>
     */
    public Builder setSelfTable(xddq.pb.PharmacySelfTableSyncMsg value) {
      if (selfTableBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        selfTable_ = value;
      } else {
        selfTableBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PharmacySelfTableSyncMsg selfTable = 3;</code>
     */
    public Builder setSelfTable(
        xddq.pb.PharmacySelfTableSyncMsg.Builder builderForValue) {
      if (selfTableBuilder_ == null) {
        selfTable_ = builderForValue.build();
      } else {
        selfTableBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PharmacySelfTableSyncMsg selfTable = 3;</code>
     */
    public Builder mergeSelfTable(xddq.pb.PharmacySelfTableSyncMsg value) {
      if (selfTableBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0) &&
          selfTable_ != null &&
          selfTable_ != xddq.pb.PharmacySelfTableSyncMsg.getDefaultInstance()) {
          getSelfTableBuilder().mergeFrom(value);
        } else {
          selfTable_ = value;
        }
      } else {
        selfTableBuilder_.mergeFrom(value);
      }
      if (selfTable_ != null) {
        bitField0_ |= 0x00000004;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.PharmacySelfTableSyncMsg selfTable = 3;</code>
     */
    public Builder clearSelfTable() {
      bitField0_ = (bitField0_ & ~0x00000004);
      selfTable_ = null;
      if (selfTableBuilder_ != null) {
        selfTableBuilder_.dispose();
        selfTableBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PharmacySelfTableSyncMsg selfTable = 3;</code>
     */
    public xddq.pb.PharmacySelfTableSyncMsg.Builder getSelfTableBuilder() {
      bitField0_ |= 0x00000004;
      onChanged();
      return internalGetSelfTableFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.PharmacySelfTableSyncMsg selfTable = 3;</code>
     */
    public xddq.pb.PharmacySelfTableSyncMsgOrBuilder getSelfTableOrBuilder() {
      if (selfTableBuilder_ != null) {
        return selfTableBuilder_.getMessageOrBuilder();
      } else {
        return selfTable_ == null ?
            xddq.pb.PharmacySelfTableSyncMsg.getDefaultInstance() : selfTable_;
      }
    }
    /**
     * <code>optional .xddq.pb.PharmacySelfTableSyncMsg selfTable = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PharmacySelfTableSyncMsg, xddq.pb.PharmacySelfTableSyncMsg.Builder, xddq.pb.PharmacySelfTableSyncMsgOrBuilder> 
        internalGetSelfTableFieldBuilder() {
      if (selfTableBuilder_ == null) {
        selfTableBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.PharmacySelfTableSyncMsg, xddq.pb.PharmacySelfTableSyncMsg.Builder, xddq.pb.PharmacySelfTableSyncMsgOrBuilder>(
                getSelfTable(),
                getParentForChildren(),
                isClean());
        selfTable_ = null;
      }
      return selfTableBuilder_;
    }

    private int hp_ ;
    /**
     * <code>optional int32 hp = 4;</code>
     * @return Whether the hp field is set.
     */
    @java.lang.Override
    public boolean hasHp() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int32 hp = 4;</code>
     * @return The hp.
     */
    @java.lang.Override
    public int getHp() {
      return hp_;
    }
    /**
     * <code>optional int32 hp = 4;</code>
     * @param value The hp to set.
     * @return This builder for chaining.
     */
    public Builder setHp(int value) {

      hp_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 hp = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearHp() {
      bitField0_ = (bitField0_ & ~0x00000008);
      hp_ = 0;
      onChanged();
      return this;
    }

    private long hpTime_ ;
    /**
     * <code>optional int64 hpTime = 5;</code>
     * @return Whether the hpTime field is set.
     */
    @java.lang.Override
    public boolean hasHpTime() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional int64 hpTime = 5;</code>
     * @return The hpTime.
     */
    @java.lang.Override
    public long getHpTime() {
      return hpTime_;
    }
    /**
     * <code>optional int64 hpTime = 5;</code>
     * @param value The hpTime to set.
     * @return This builder for chaining.
     */
    public Builder setHpTime(long value) {

      hpTime_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 hpTime = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearHpTime() {
      bitField0_ = (bitField0_ & ~0x00000010);
      hpTime_ = 0L;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.PharmacyMakeFoodRespMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.PharmacyMakeFoodRespMsg)
  private static final xddq.pb.PharmacyMakeFoodRespMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.PharmacyMakeFoodRespMsg();
  }

  public static xddq.pb.PharmacyMakeFoodRespMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<PharmacyMakeFoodRespMsg>
      PARSER = new com.google.protobuf.AbstractParser<PharmacyMakeFoodRespMsg>() {
    @java.lang.Override
    public PharmacyMakeFoodRespMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<PharmacyMakeFoodRespMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<PharmacyMakeFoodRespMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.PharmacyMakeFoodRespMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

