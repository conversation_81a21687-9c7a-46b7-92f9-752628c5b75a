// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.GodIslandGroupBaseMsg}
 */
public final class GodIslandGroupBaseMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.GodIslandGroupBaseMsg)
    GodIslandGroupBaseMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      GodIslandGroupBaseMsg.class.getName());
  }
  // Use GodIslandGroupBaseMsg.newBuilder() to construct.
  private GodIslandGroupBaseMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private GodIslandGroupBaseMsg() {
    unionName_ = "";
    masterName_ = "";
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_GodIslandGroupBaseMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_GodIslandGroupBaseMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.GodIslandGroupBaseMsg.class, xddq.pb.GodIslandGroupBaseMsg.Builder.class);
  }

  private int bitField0_;
  public static final int UNIONID_FIELD_NUMBER = 1;
  private long unionId_ = 0L;
  /**
   * <code>optional int64 unionId = 1;</code>
   * @return Whether the unionId field is set.
   */
  @java.lang.Override
  public boolean hasUnionId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int64 unionId = 1;</code>
   * @return The unionId.
   */
  @java.lang.Override
  public long getUnionId() {
    return unionId_;
  }

  public static final int UNIONNAME_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private volatile java.lang.Object unionName_ = "";
  /**
   * <code>optional string unionName = 2;</code>
   * @return Whether the unionName field is set.
   */
  @java.lang.Override
  public boolean hasUnionName() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional string unionName = 2;</code>
   * @return The unionName.
   */
  @java.lang.Override
  public java.lang.String getUnionName() {
    java.lang.Object ref = unionName_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        unionName_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string unionName = 2;</code>
   * @return The bytes for unionName.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getUnionNameBytes() {
    java.lang.Object ref = unionName_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      unionName_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int FLAG_FIELD_NUMBER = 3;
  private int flag_ = 0;
  /**
   * <code>optional int32 flag = 3;</code>
   * @return Whether the flag field is set.
   */
  @java.lang.Override
  public boolean hasFlag() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional int32 flag = 3;</code>
   * @return The flag.
   */
  @java.lang.Override
  public int getFlag() {
    return flag_;
  }

  public static final int MASTERNAME_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private volatile java.lang.Object masterName_ = "";
  /**
   * <code>optional string masterName = 4;</code>
   * @return Whether the masterName field is set.
   */
  @java.lang.Override
  public boolean hasMasterName() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional string masterName = 4;</code>
   * @return The masterName.
   */
  @java.lang.Override
  public java.lang.String getMasterName() {
    java.lang.Object ref = masterName_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        masterName_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string masterName = 4;</code>
   * @return The bytes for masterName.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getMasterNameBytes() {
    java.lang.Object ref = masterName_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      masterName_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int RANK_FIELD_NUMBER = 5;
  private int rank_ = 0;
  /**
   * <code>optional int32 rank = 5;</code>
   * @return Whether the rank field is set.
   */
  @java.lang.Override
  public boolean hasRank() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>optional int32 rank = 5;</code>
   * @return The rank.
   */
  @java.lang.Override
  public int getRank() {
    return rank_;
  }

  public static final int CITYSCORE_FIELD_NUMBER = 6;
  private long cityScore_ = 0L;
  /**
   * <code>optional int64 cityScore = 6;</code>
   * @return Whether the cityScore field is set.
   */
  @java.lang.Override
  public boolean hasCityScore() {
    return ((bitField0_ & 0x00000020) != 0);
  }
  /**
   * <code>optional int64 cityScore = 6;</code>
   * @return The cityScore.
   */
  @java.lang.Override
  public long getCityScore() {
    return cityScore_;
  }

  public static final int SERVERID_FIELD_NUMBER = 7;
  private long serverId_ = 0L;
  /**
   * <code>optional int64 serverId = 7;</code>
   * @return Whether the serverId field is set.
   */
  @java.lang.Override
  public boolean hasServerId() {
    return ((bitField0_ & 0x00000040) != 0);
  }
  /**
   * <code>optional int64 serverId = 7;</code>
   * @return The serverId.
   */
  @java.lang.Override
  public long getServerId() {
    return serverId_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt64(1, unionId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 2, unionName_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt32(3, flag_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 4, masterName_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      output.writeInt32(5, rank_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      output.writeInt64(6, cityScore_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      output.writeInt64(7, serverId_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(1, unionId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(2, unionName_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(3, flag_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(4, masterName_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(5, rank_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(6, cityScore_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(7, serverId_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.GodIslandGroupBaseMsg)) {
      return super.equals(obj);
    }
    xddq.pb.GodIslandGroupBaseMsg other = (xddq.pb.GodIslandGroupBaseMsg) obj;

    if (hasUnionId() != other.hasUnionId()) return false;
    if (hasUnionId()) {
      if (getUnionId()
          != other.getUnionId()) return false;
    }
    if (hasUnionName() != other.hasUnionName()) return false;
    if (hasUnionName()) {
      if (!getUnionName()
          .equals(other.getUnionName())) return false;
    }
    if (hasFlag() != other.hasFlag()) return false;
    if (hasFlag()) {
      if (getFlag()
          != other.getFlag()) return false;
    }
    if (hasMasterName() != other.hasMasterName()) return false;
    if (hasMasterName()) {
      if (!getMasterName()
          .equals(other.getMasterName())) return false;
    }
    if (hasRank() != other.hasRank()) return false;
    if (hasRank()) {
      if (getRank()
          != other.getRank()) return false;
    }
    if (hasCityScore() != other.hasCityScore()) return false;
    if (hasCityScore()) {
      if (getCityScore()
          != other.getCityScore()) return false;
    }
    if (hasServerId() != other.hasServerId()) return false;
    if (hasServerId()) {
      if (getServerId()
          != other.getServerId()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasUnionId()) {
      hash = (37 * hash) + UNIONID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getUnionId());
    }
    if (hasUnionName()) {
      hash = (37 * hash) + UNIONNAME_FIELD_NUMBER;
      hash = (53 * hash) + getUnionName().hashCode();
    }
    if (hasFlag()) {
      hash = (37 * hash) + FLAG_FIELD_NUMBER;
      hash = (53 * hash) + getFlag();
    }
    if (hasMasterName()) {
      hash = (37 * hash) + MASTERNAME_FIELD_NUMBER;
      hash = (53 * hash) + getMasterName().hashCode();
    }
    if (hasRank()) {
      hash = (37 * hash) + RANK_FIELD_NUMBER;
      hash = (53 * hash) + getRank();
    }
    if (hasCityScore()) {
      hash = (37 * hash) + CITYSCORE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getCityScore());
    }
    if (hasServerId()) {
      hash = (37 * hash) + SERVERID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getServerId());
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.GodIslandGroupBaseMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GodIslandGroupBaseMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GodIslandGroupBaseMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GodIslandGroupBaseMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GodIslandGroupBaseMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GodIslandGroupBaseMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GodIslandGroupBaseMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.GodIslandGroupBaseMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.GodIslandGroupBaseMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.GodIslandGroupBaseMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.GodIslandGroupBaseMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.GodIslandGroupBaseMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.GodIslandGroupBaseMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.GodIslandGroupBaseMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.GodIslandGroupBaseMsg)
      xddq.pb.GodIslandGroupBaseMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GodIslandGroupBaseMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GodIslandGroupBaseMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.GodIslandGroupBaseMsg.class, xddq.pb.GodIslandGroupBaseMsg.Builder.class);
    }

    // Construct using xddq.pb.GodIslandGroupBaseMsg.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      unionId_ = 0L;
      unionName_ = "";
      flag_ = 0;
      masterName_ = "";
      rank_ = 0;
      cityScore_ = 0L;
      serverId_ = 0L;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GodIslandGroupBaseMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.GodIslandGroupBaseMsg getDefaultInstanceForType() {
      return xddq.pb.GodIslandGroupBaseMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.GodIslandGroupBaseMsg build() {
      xddq.pb.GodIslandGroupBaseMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.GodIslandGroupBaseMsg buildPartial() {
      xddq.pb.GodIslandGroupBaseMsg result = new xddq.pb.GodIslandGroupBaseMsg(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.GodIslandGroupBaseMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.unionId_ = unionId_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.unionName_ = unionName_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.flag_ = flag_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.masterName_ = masterName_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.rank_ = rank_;
        to_bitField0_ |= 0x00000010;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.cityScore_ = cityScore_;
        to_bitField0_ |= 0x00000020;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.serverId_ = serverId_;
        to_bitField0_ |= 0x00000040;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.GodIslandGroupBaseMsg) {
        return mergeFrom((xddq.pb.GodIslandGroupBaseMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.GodIslandGroupBaseMsg other) {
      if (other == xddq.pb.GodIslandGroupBaseMsg.getDefaultInstance()) return this;
      if (other.hasUnionId()) {
        setUnionId(other.getUnionId());
      }
      if (other.hasUnionName()) {
        unionName_ = other.unionName_;
        bitField0_ |= 0x00000002;
        onChanged();
      }
      if (other.hasFlag()) {
        setFlag(other.getFlag());
      }
      if (other.hasMasterName()) {
        masterName_ = other.masterName_;
        bitField0_ |= 0x00000008;
        onChanged();
      }
      if (other.hasRank()) {
        setRank(other.getRank());
      }
      if (other.hasCityScore()) {
        setCityScore(other.getCityScore());
      }
      if (other.hasServerId()) {
        setServerId(other.getServerId());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              unionId_ = input.readInt64();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              unionName_ = input.readBytes();
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 24: {
              flag_ = input.readInt32();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 34: {
              masterName_ = input.readBytes();
              bitField0_ |= 0x00000008;
              break;
            } // case 34
            case 40: {
              rank_ = input.readInt32();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            case 48: {
              cityScore_ = input.readInt64();
              bitField0_ |= 0x00000020;
              break;
            } // case 48
            case 56: {
              serverId_ = input.readInt64();
              bitField0_ |= 0x00000040;
              break;
            } // case 56
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private long unionId_ ;
    /**
     * <code>optional int64 unionId = 1;</code>
     * @return Whether the unionId field is set.
     */
    @java.lang.Override
    public boolean hasUnionId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 unionId = 1;</code>
     * @return The unionId.
     */
    @java.lang.Override
    public long getUnionId() {
      return unionId_;
    }
    /**
     * <code>optional int64 unionId = 1;</code>
     * @param value The unionId to set.
     * @return This builder for chaining.
     */
    public Builder setUnionId(long value) {

      unionId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 unionId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearUnionId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      unionId_ = 0L;
      onChanged();
      return this;
    }

    private java.lang.Object unionName_ = "";
    /**
     * <code>optional string unionName = 2;</code>
     * @return Whether the unionName field is set.
     */
    public boolean hasUnionName() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional string unionName = 2;</code>
     * @return The unionName.
     */
    public java.lang.String getUnionName() {
      java.lang.Object ref = unionName_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          unionName_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string unionName = 2;</code>
     * @return The bytes for unionName.
     */
    public com.google.protobuf.ByteString
        getUnionNameBytes() {
      java.lang.Object ref = unionName_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        unionName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string unionName = 2;</code>
     * @param value The unionName to set.
     * @return This builder for chaining.
     */
    public Builder setUnionName(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      unionName_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional string unionName = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearUnionName() {
      unionName_ = getDefaultInstance().getUnionName();
      bitField0_ = (bitField0_ & ~0x00000002);
      onChanged();
      return this;
    }
    /**
     * <code>optional string unionName = 2;</code>
     * @param value The bytes for unionName to set.
     * @return This builder for chaining.
     */
    public Builder setUnionNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      unionName_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }

    private int flag_ ;
    /**
     * <code>optional int32 flag = 3;</code>
     * @return Whether the flag field is set.
     */
    @java.lang.Override
    public boolean hasFlag() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int32 flag = 3;</code>
     * @return The flag.
     */
    @java.lang.Override
    public int getFlag() {
      return flag_;
    }
    /**
     * <code>optional int32 flag = 3;</code>
     * @param value The flag to set.
     * @return This builder for chaining.
     */
    public Builder setFlag(int value) {

      flag_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 flag = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearFlag() {
      bitField0_ = (bitField0_ & ~0x00000004);
      flag_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object masterName_ = "";
    /**
     * <code>optional string masterName = 4;</code>
     * @return Whether the masterName field is set.
     */
    public boolean hasMasterName() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional string masterName = 4;</code>
     * @return The masterName.
     */
    public java.lang.String getMasterName() {
      java.lang.Object ref = masterName_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          masterName_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string masterName = 4;</code>
     * @return The bytes for masterName.
     */
    public com.google.protobuf.ByteString
        getMasterNameBytes() {
      java.lang.Object ref = masterName_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        masterName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string masterName = 4;</code>
     * @param value The masterName to set.
     * @return This builder for chaining.
     */
    public Builder setMasterName(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      masterName_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional string masterName = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearMasterName() {
      masterName_ = getDefaultInstance().getMasterName();
      bitField0_ = (bitField0_ & ~0x00000008);
      onChanged();
      return this;
    }
    /**
     * <code>optional string masterName = 4;</code>
     * @param value The bytes for masterName to set.
     * @return This builder for chaining.
     */
    public Builder setMasterNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      masterName_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }

    private int rank_ ;
    /**
     * <code>optional int32 rank = 5;</code>
     * @return Whether the rank field is set.
     */
    @java.lang.Override
    public boolean hasRank() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional int32 rank = 5;</code>
     * @return The rank.
     */
    @java.lang.Override
    public int getRank() {
      return rank_;
    }
    /**
     * <code>optional int32 rank = 5;</code>
     * @param value The rank to set.
     * @return This builder for chaining.
     */
    public Builder setRank(int value) {

      rank_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 rank = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearRank() {
      bitField0_ = (bitField0_ & ~0x00000010);
      rank_ = 0;
      onChanged();
      return this;
    }

    private long cityScore_ ;
    /**
     * <code>optional int64 cityScore = 6;</code>
     * @return Whether the cityScore field is set.
     */
    @java.lang.Override
    public boolean hasCityScore() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional int64 cityScore = 6;</code>
     * @return The cityScore.
     */
    @java.lang.Override
    public long getCityScore() {
      return cityScore_;
    }
    /**
     * <code>optional int64 cityScore = 6;</code>
     * @param value The cityScore to set.
     * @return This builder for chaining.
     */
    public Builder setCityScore(long value) {

      cityScore_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 cityScore = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearCityScore() {
      bitField0_ = (bitField0_ & ~0x00000020);
      cityScore_ = 0L;
      onChanged();
      return this;
    }

    private long serverId_ ;
    /**
     * <code>optional int64 serverId = 7;</code>
     * @return Whether the serverId field is set.
     */
    @java.lang.Override
    public boolean hasServerId() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <code>optional int64 serverId = 7;</code>
     * @return The serverId.
     */
    @java.lang.Override
    public long getServerId() {
      return serverId_;
    }
    /**
     * <code>optional int64 serverId = 7;</code>
     * @param value The serverId to set.
     * @return This builder for chaining.
     */
    public Builder setServerId(long value) {

      serverId_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 serverId = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearServerId() {
      bitField0_ = (bitField0_ & ~0x00000040);
      serverId_ = 0L;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.GodIslandGroupBaseMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.GodIslandGroupBaseMsg)
  private static final xddq.pb.GodIslandGroupBaseMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.GodIslandGroupBaseMsg();
  }

  public static xddq.pb.GodIslandGroupBaseMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<GodIslandGroupBaseMsg>
      PARSER = new com.google.protobuf.AbstractParser<GodIslandGroupBaseMsg>() {
    @java.lang.Override
    public GodIslandGroupBaseMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<GodIslandGroupBaseMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<GodIslandGroupBaseMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.GodIslandGroupBaseMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

