// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.RankBattleRevengeResp}
 */
public final class RankBattleRevengeResp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.RankBattleRevengeResp)
    RankBattleRevengeRespOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      RankBattleRevengeResp.class.getName());
  }
  // Use RankBattleRevengeResp.newBuilder() to construct.
  private RankBattleRevengeResp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private RankBattleRevengeResp() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_RankBattleRevengeResp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_RankBattleRevengeResp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.RankBattleRevengeResp.class, xddq.pb.RankBattleRevengeResp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int RANKBATTLERESULTMSG_FIELD_NUMBER = 2;
  private xddq.pb.RankBattleResultMsg rankBattleResultMsg_;
  /**
   * <code>optional .xddq.pb.RankBattleResultMsg rankBattleResultMsg = 2;</code>
   * @return Whether the rankBattleResultMsg field is set.
   */
  @java.lang.Override
  public boolean hasRankBattleResultMsg() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional .xddq.pb.RankBattleResultMsg rankBattleResultMsg = 2;</code>
   * @return The rankBattleResultMsg.
   */
  @java.lang.Override
  public xddq.pb.RankBattleResultMsg getRankBattleResultMsg() {
    return rankBattleResultMsg_ == null ? xddq.pb.RankBattleResultMsg.getDefaultInstance() : rankBattleResultMsg_;
  }
  /**
   * <code>optional .xddq.pb.RankBattleResultMsg rankBattleResultMsg = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.RankBattleResultMsgOrBuilder getRankBattleResultMsgOrBuilder() {
    return rankBattleResultMsg_ == null ? xddq.pb.RankBattleResultMsg.getDefaultInstance() : rankBattleResultMsg_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (hasRankBattleResultMsg()) {
      if (!getRankBattleResultMsg().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeMessage(2, getRankBattleResultMsg());
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getRankBattleResultMsg());
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.RankBattleRevengeResp)) {
      return super.equals(obj);
    }
    xddq.pb.RankBattleRevengeResp other = (xddq.pb.RankBattleRevengeResp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (hasRankBattleResultMsg() != other.hasRankBattleResultMsg()) return false;
    if (hasRankBattleResultMsg()) {
      if (!getRankBattleResultMsg()
          .equals(other.getRankBattleResultMsg())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (hasRankBattleResultMsg()) {
      hash = (37 * hash) + RANKBATTLERESULTMSG_FIELD_NUMBER;
      hash = (53 * hash) + getRankBattleResultMsg().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.RankBattleRevengeResp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.RankBattleRevengeResp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.RankBattleRevengeResp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.RankBattleRevengeResp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.RankBattleRevengeResp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.RankBattleRevengeResp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.RankBattleRevengeResp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.RankBattleRevengeResp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.RankBattleRevengeResp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.RankBattleRevengeResp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.RankBattleRevengeResp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.RankBattleRevengeResp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.RankBattleRevengeResp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.RankBattleRevengeResp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.RankBattleRevengeResp)
      xddq.pb.RankBattleRevengeRespOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_RankBattleRevengeResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_RankBattleRevengeResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.RankBattleRevengeResp.class, xddq.pb.RankBattleRevengeResp.Builder.class);
    }

    // Construct using xddq.pb.RankBattleRevengeResp.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetRankBattleResultMsgFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      rankBattleResultMsg_ = null;
      if (rankBattleResultMsgBuilder_ != null) {
        rankBattleResultMsgBuilder_.dispose();
        rankBattleResultMsgBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_RankBattleRevengeResp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.RankBattleRevengeResp getDefaultInstanceForType() {
      return xddq.pb.RankBattleRevengeResp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.RankBattleRevengeResp build() {
      xddq.pb.RankBattleRevengeResp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.RankBattleRevengeResp buildPartial() {
      xddq.pb.RankBattleRevengeResp result = new xddq.pb.RankBattleRevengeResp(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.RankBattleRevengeResp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.rankBattleResultMsg_ = rankBattleResultMsgBuilder_ == null
            ? rankBattleResultMsg_
            : rankBattleResultMsgBuilder_.build();
        to_bitField0_ |= 0x00000002;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.RankBattleRevengeResp) {
        return mergeFrom((xddq.pb.RankBattleRevengeResp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.RankBattleRevengeResp other) {
      if (other == xddq.pb.RankBattleRevengeResp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (other.hasRankBattleResultMsg()) {
        mergeRankBattleResultMsg(other.getRankBattleResultMsg());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      if (hasRankBattleResultMsg()) {
        if (!getRankBattleResultMsg().isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              input.readMessage(
                  internalGetRankBattleResultMsgFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private xddq.pb.RankBattleResultMsg rankBattleResultMsg_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.RankBattleResultMsg, xddq.pb.RankBattleResultMsg.Builder, xddq.pb.RankBattleResultMsgOrBuilder> rankBattleResultMsgBuilder_;
    /**
     * <code>optional .xddq.pb.RankBattleResultMsg rankBattleResultMsg = 2;</code>
     * @return Whether the rankBattleResultMsg field is set.
     */
    public boolean hasRankBattleResultMsg() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .xddq.pb.RankBattleResultMsg rankBattleResultMsg = 2;</code>
     * @return The rankBattleResultMsg.
     */
    public xddq.pb.RankBattleResultMsg getRankBattleResultMsg() {
      if (rankBattleResultMsgBuilder_ == null) {
        return rankBattleResultMsg_ == null ? xddq.pb.RankBattleResultMsg.getDefaultInstance() : rankBattleResultMsg_;
      } else {
        return rankBattleResultMsgBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.RankBattleResultMsg rankBattleResultMsg = 2;</code>
     */
    public Builder setRankBattleResultMsg(xddq.pb.RankBattleResultMsg value) {
      if (rankBattleResultMsgBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        rankBattleResultMsg_ = value;
      } else {
        rankBattleResultMsgBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.RankBattleResultMsg rankBattleResultMsg = 2;</code>
     */
    public Builder setRankBattleResultMsg(
        xddq.pb.RankBattleResultMsg.Builder builderForValue) {
      if (rankBattleResultMsgBuilder_ == null) {
        rankBattleResultMsg_ = builderForValue.build();
      } else {
        rankBattleResultMsgBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.RankBattleResultMsg rankBattleResultMsg = 2;</code>
     */
    public Builder mergeRankBattleResultMsg(xddq.pb.RankBattleResultMsg value) {
      if (rankBattleResultMsgBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0) &&
          rankBattleResultMsg_ != null &&
          rankBattleResultMsg_ != xddq.pb.RankBattleResultMsg.getDefaultInstance()) {
          getRankBattleResultMsgBuilder().mergeFrom(value);
        } else {
          rankBattleResultMsg_ = value;
        }
      } else {
        rankBattleResultMsgBuilder_.mergeFrom(value);
      }
      if (rankBattleResultMsg_ != null) {
        bitField0_ |= 0x00000002;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.RankBattleResultMsg rankBattleResultMsg = 2;</code>
     */
    public Builder clearRankBattleResultMsg() {
      bitField0_ = (bitField0_ & ~0x00000002);
      rankBattleResultMsg_ = null;
      if (rankBattleResultMsgBuilder_ != null) {
        rankBattleResultMsgBuilder_.dispose();
        rankBattleResultMsgBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.RankBattleResultMsg rankBattleResultMsg = 2;</code>
     */
    public xddq.pb.RankBattleResultMsg.Builder getRankBattleResultMsgBuilder() {
      bitField0_ |= 0x00000002;
      onChanged();
      return internalGetRankBattleResultMsgFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.RankBattleResultMsg rankBattleResultMsg = 2;</code>
     */
    public xddq.pb.RankBattleResultMsgOrBuilder getRankBattleResultMsgOrBuilder() {
      if (rankBattleResultMsgBuilder_ != null) {
        return rankBattleResultMsgBuilder_.getMessageOrBuilder();
      } else {
        return rankBattleResultMsg_ == null ?
            xddq.pb.RankBattleResultMsg.getDefaultInstance() : rankBattleResultMsg_;
      }
    }
    /**
     * <code>optional .xddq.pb.RankBattleResultMsg rankBattleResultMsg = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.RankBattleResultMsg, xddq.pb.RankBattleResultMsg.Builder, xddq.pb.RankBattleResultMsgOrBuilder> 
        internalGetRankBattleResultMsgFieldBuilder() {
      if (rankBattleResultMsgBuilder_ == null) {
        rankBattleResultMsgBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.RankBattleResultMsg, xddq.pb.RankBattleResultMsg.Builder, xddq.pb.RankBattleResultMsgOrBuilder>(
                getRankBattleResultMsg(),
                getParentForChildren(),
                isClean());
        rankBattleResultMsg_ = null;
      }
      return rankBattleResultMsgBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.RankBattleRevengeResp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.RankBattleRevengeResp)
  private static final xddq.pb.RankBattleRevengeResp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.RankBattleRevengeResp();
  }

  public static xddq.pb.RankBattleRevengeResp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<RankBattleRevengeResp>
      PARSER = new com.google.protobuf.AbstractParser<RankBattleRevengeResp>() {
    @java.lang.Override
    public RankBattleRevengeResp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<RankBattleRevengeResp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<RankBattleRevengeResp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.RankBattleRevengeResp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

