// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.PeakFightDispatchBodyReq}
 */
public final class PeakFightDispatchBodyReq extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.PeakFightDispatchBodyReq)
    PeakFightDispatchBodyReqOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      PeakFightDispatchBodyReq.class.getName());
  }
  // Use PeakFightDispatchBodyReq.newBuilder() to construct.
  private PeakFightDispatchBodyReq(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private PeakFightDispatchBodyReq() {
    bodySit_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_PeakFightDispatchBodyReq_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_PeakFightDispatchBodyReq_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.PeakFightDispatchBodyReq.class, xddq.pb.PeakFightDispatchBodyReq.Builder.class);
  }

  private int bitField0_;
  public static final int ACTIVITYID_FIELD_NUMBER = 1;
  private int activityId_ = 0;
  /**
   * <code>required int32 activityId = 1;</code>
   * @return Whether the activityId field is set.
   */
  @java.lang.Override
  public boolean hasActivityId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 activityId = 1;</code>
   * @return The activityId.
   */
  @java.lang.Override
  public int getActivityId() {
    return activityId_;
  }

  public static final int BODYSIT_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.PeakFightBodySiteMsg> bodySit_;
  /**
   * <code>repeated .xddq.pb.PeakFightBodySiteMsg bodySit = 2;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.PeakFightBodySiteMsg> getBodySitList() {
    return bodySit_;
  }
  /**
   * <code>repeated .xddq.pb.PeakFightBodySiteMsg bodySit = 2;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.PeakFightBodySiteMsgOrBuilder> 
      getBodySitOrBuilderList() {
    return bodySit_;
  }
  /**
   * <code>repeated .xddq.pb.PeakFightBodySiteMsg bodySit = 2;</code>
   */
  @java.lang.Override
  public int getBodySitCount() {
    return bodySit_.size();
  }
  /**
   * <code>repeated .xddq.pb.PeakFightBodySiteMsg bodySit = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.PeakFightBodySiteMsg getBodySit(int index) {
    return bodySit_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.PeakFightBodySiteMsg bodySit = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.PeakFightBodySiteMsgOrBuilder getBodySitOrBuilder(
      int index) {
    return bodySit_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasActivityId()) {
      memoizedIsInitialized = 0;
      return false;
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, activityId_);
    }
    for (int i = 0; i < bodySit_.size(); i++) {
      output.writeMessage(2, bodySit_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, activityId_);
    }
    for (int i = 0; i < bodySit_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, bodySit_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.PeakFightDispatchBodyReq)) {
      return super.equals(obj);
    }
    xddq.pb.PeakFightDispatchBodyReq other = (xddq.pb.PeakFightDispatchBodyReq) obj;

    if (hasActivityId() != other.hasActivityId()) return false;
    if (hasActivityId()) {
      if (getActivityId()
          != other.getActivityId()) return false;
    }
    if (!getBodySitList()
        .equals(other.getBodySitList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasActivityId()) {
      hash = (37 * hash) + ACTIVITYID_FIELD_NUMBER;
      hash = (53 * hash) + getActivityId();
    }
    if (getBodySitCount() > 0) {
      hash = (37 * hash) + BODYSIT_FIELD_NUMBER;
      hash = (53 * hash) + getBodySitList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.PeakFightDispatchBodyReq parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PeakFightDispatchBodyReq parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PeakFightDispatchBodyReq parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PeakFightDispatchBodyReq parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PeakFightDispatchBodyReq parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PeakFightDispatchBodyReq parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PeakFightDispatchBodyReq parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.PeakFightDispatchBodyReq parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.PeakFightDispatchBodyReq parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.PeakFightDispatchBodyReq parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.PeakFightDispatchBodyReq parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.PeakFightDispatchBodyReq parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.PeakFightDispatchBodyReq prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.PeakFightDispatchBodyReq}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.PeakFightDispatchBodyReq)
      xddq.pb.PeakFightDispatchBodyReqOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PeakFightDispatchBodyReq_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PeakFightDispatchBodyReq_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.PeakFightDispatchBodyReq.class, xddq.pb.PeakFightDispatchBodyReq.Builder.class);
    }

    // Construct using xddq.pb.PeakFightDispatchBodyReq.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      activityId_ = 0;
      if (bodySitBuilder_ == null) {
        bodySit_ = java.util.Collections.emptyList();
      } else {
        bodySit_ = null;
        bodySitBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000002);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PeakFightDispatchBodyReq_descriptor;
    }

    @java.lang.Override
    public xddq.pb.PeakFightDispatchBodyReq getDefaultInstanceForType() {
      return xddq.pb.PeakFightDispatchBodyReq.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.PeakFightDispatchBodyReq build() {
      xddq.pb.PeakFightDispatchBodyReq result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.PeakFightDispatchBodyReq buildPartial() {
      xddq.pb.PeakFightDispatchBodyReq result = new xddq.pb.PeakFightDispatchBodyReq(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.PeakFightDispatchBodyReq result) {
      if (bodySitBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          bodySit_ = java.util.Collections.unmodifiableList(bodySit_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.bodySit_ = bodySit_;
      } else {
        result.bodySit_ = bodySitBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.PeakFightDispatchBodyReq result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.activityId_ = activityId_;
        to_bitField0_ |= 0x00000001;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.PeakFightDispatchBodyReq) {
        return mergeFrom((xddq.pb.PeakFightDispatchBodyReq)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.PeakFightDispatchBodyReq other) {
      if (other == xddq.pb.PeakFightDispatchBodyReq.getDefaultInstance()) return this;
      if (other.hasActivityId()) {
        setActivityId(other.getActivityId());
      }
      if (bodySitBuilder_ == null) {
        if (!other.bodySit_.isEmpty()) {
          if (bodySit_.isEmpty()) {
            bodySit_ = other.bodySit_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureBodySitIsMutable();
            bodySit_.addAll(other.bodySit_);
          }
          onChanged();
        }
      } else {
        if (!other.bodySit_.isEmpty()) {
          if (bodySitBuilder_.isEmpty()) {
            bodySitBuilder_.dispose();
            bodySitBuilder_ = null;
            bodySit_ = other.bodySit_;
            bitField0_ = (bitField0_ & ~0x00000002);
            bodySitBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetBodySitFieldBuilder() : null;
          } else {
            bodySitBuilder_.addAllMessages(other.bodySit_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasActivityId()) {
        return false;
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              activityId_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              xddq.pb.PeakFightBodySiteMsg m =
                  input.readMessage(
                      xddq.pb.PeakFightBodySiteMsg.parser(),
                      extensionRegistry);
              if (bodySitBuilder_ == null) {
                ensureBodySitIsMutable();
                bodySit_.add(m);
              } else {
                bodySitBuilder_.addMessage(m);
              }
              break;
            } // case 18
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int activityId_ ;
    /**
     * <code>required int32 activityId = 1;</code>
     * @return Whether the activityId field is set.
     */
    @java.lang.Override
    public boolean hasActivityId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 activityId = 1;</code>
     * @return The activityId.
     */
    @java.lang.Override
    public int getActivityId() {
      return activityId_;
    }
    /**
     * <code>required int32 activityId = 1;</code>
     * @param value The activityId to set.
     * @return This builder for chaining.
     */
    public Builder setActivityId(int value) {

      activityId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 activityId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearActivityId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      activityId_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.PeakFightBodySiteMsg> bodySit_ =
      java.util.Collections.emptyList();
    private void ensureBodySitIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        bodySit_ = new java.util.ArrayList<xddq.pb.PeakFightBodySiteMsg>(bodySit_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.PeakFightBodySiteMsg, xddq.pb.PeakFightBodySiteMsg.Builder, xddq.pb.PeakFightBodySiteMsgOrBuilder> bodySitBuilder_;

    /**
     * <code>repeated .xddq.pb.PeakFightBodySiteMsg bodySit = 2;</code>
     */
    public java.util.List<xddq.pb.PeakFightBodySiteMsg> getBodySitList() {
      if (bodySitBuilder_ == null) {
        return java.util.Collections.unmodifiableList(bodySit_);
      } else {
        return bodySitBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.PeakFightBodySiteMsg bodySit = 2;</code>
     */
    public int getBodySitCount() {
      if (bodySitBuilder_ == null) {
        return bodySit_.size();
      } else {
        return bodySitBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.PeakFightBodySiteMsg bodySit = 2;</code>
     */
    public xddq.pb.PeakFightBodySiteMsg getBodySit(int index) {
      if (bodySitBuilder_ == null) {
        return bodySit_.get(index);
      } else {
        return bodySitBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.PeakFightBodySiteMsg bodySit = 2;</code>
     */
    public Builder setBodySit(
        int index, xddq.pb.PeakFightBodySiteMsg value) {
      if (bodySitBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureBodySitIsMutable();
        bodySit_.set(index, value);
        onChanged();
      } else {
        bodySitBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PeakFightBodySiteMsg bodySit = 2;</code>
     */
    public Builder setBodySit(
        int index, xddq.pb.PeakFightBodySiteMsg.Builder builderForValue) {
      if (bodySitBuilder_ == null) {
        ensureBodySitIsMutable();
        bodySit_.set(index, builderForValue.build());
        onChanged();
      } else {
        bodySitBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PeakFightBodySiteMsg bodySit = 2;</code>
     */
    public Builder addBodySit(xddq.pb.PeakFightBodySiteMsg value) {
      if (bodySitBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureBodySitIsMutable();
        bodySit_.add(value);
        onChanged();
      } else {
        bodySitBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PeakFightBodySiteMsg bodySit = 2;</code>
     */
    public Builder addBodySit(
        int index, xddq.pb.PeakFightBodySiteMsg value) {
      if (bodySitBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureBodySitIsMutable();
        bodySit_.add(index, value);
        onChanged();
      } else {
        bodySitBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PeakFightBodySiteMsg bodySit = 2;</code>
     */
    public Builder addBodySit(
        xddq.pb.PeakFightBodySiteMsg.Builder builderForValue) {
      if (bodySitBuilder_ == null) {
        ensureBodySitIsMutable();
        bodySit_.add(builderForValue.build());
        onChanged();
      } else {
        bodySitBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PeakFightBodySiteMsg bodySit = 2;</code>
     */
    public Builder addBodySit(
        int index, xddq.pb.PeakFightBodySiteMsg.Builder builderForValue) {
      if (bodySitBuilder_ == null) {
        ensureBodySitIsMutable();
        bodySit_.add(index, builderForValue.build());
        onChanged();
      } else {
        bodySitBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PeakFightBodySiteMsg bodySit = 2;</code>
     */
    public Builder addAllBodySit(
        java.lang.Iterable<? extends xddq.pb.PeakFightBodySiteMsg> values) {
      if (bodySitBuilder_ == null) {
        ensureBodySitIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, bodySit_);
        onChanged();
      } else {
        bodySitBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PeakFightBodySiteMsg bodySit = 2;</code>
     */
    public Builder clearBodySit() {
      if (bodySitBuilder_ == null) {
        bodySit_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        bodySitBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PeakFightBodySiteMsg bodySit = 2;</code>
     */
    public Builder removeBodySit(int index) {
      if (bodySitBuilder_ == null) {
        ensureBodySitIsMutable();
        bodySit_.remove(index);
        onChanged();
      } else {
        bodySitBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PeakFightBodySiteMsg bodySit = 2;</code>
     */
    public xddq.pb.PeakFightBodySiteMsg.Builder getBodySitBuilder(
        int index) {
      return internalGetBodySitFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.PeakFightBodySiteMsg bodySit = 2;</code>
     */
    public xddq.pb.PeakFightBodySiteMsgOrBuilder getBodySitOrBuilder(
        int index) {
      if (bodySitBuilder_ == null) {
        return bodySit_.get(index);  } else {
        return bodySitBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.PeakFightBodySiteMsg bodySit = 2;</code>
     */
    public java.util.List<? extends xddq.pb.PeakFightBodySiteMsgOrBuilder> 
         getBodySitOrBuilderList() {
      if (bodySitBuilder_ != null) {
        return bodySitBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(bodySit_);
      }
    }
    /**
     * <code>repeated .xddq.pb.PeakFightBodySiteMsg bodySit = 2;</code>
     */
    public xddq.pb.PeakFightBodySiteMsg.Builder addBodySitBuilder() {
      return internalGetBodySitFieldBuilder().addBuilder(
          xddq.pb.PeakFightBodySiteMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.PeakFightBodySiteMsg bodySit = 2;</code>
     */
    public xddq.pb.PeakFightBodySiteMsg.Builder addBodySitBuilder(
        int index) {
      return internalGetBodySitFieldBuilder().addBuilder(
          index, xddq.pb.PeakFightBodySiteMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.PeakFightBodySiteMsg bodySit = 2;</code>
     */
    public java.util.List<xddq.pb.PeakFightBodySiteMsg.Builder> 
         getBodySitBuilderList() {
      return internalGetBodySitFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.PeakFightBodySiteMsg, xddq.pb.PeakFightBodySiteMsg.Builder, xddq.pb.PeakFightBodySiteMsgOrBuilder> 
        internalGetBodySitFieldBuilder() {
      if (bodySitBuilder_ == null) {
        bodySitBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.PeakFightBodySiteMsg, xddq.pb.PeakFightBodySiteMsg.Builder, xddq.pb.PeakFightBodySiteMsgOrBuilder>(
                bodySit_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        bodySit_ = null;
      }
      return bodySitBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.PeakFightDispatchBodyReq)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.PeakFightDispatchBodyReq)
  private static final xddq.pb.PeakFightDispatchBodyReq DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.PeakFightDispatchBodyReq();
  }

  public static xddq.pb.PeakFightDispatchBodyReq getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<PeakFightDispatchBodyReq>
      PARSER = new com.google.protobuf.AbstractParser<PeakFightDispatchBodyReq>() {
    @java.lang.Override
    public PeakFightDispatchBodyReq parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<PeakFightDispatchBodyReq> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<PeakFightDispatchBodyReq> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.PeakFightDispatchBodyReq getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

