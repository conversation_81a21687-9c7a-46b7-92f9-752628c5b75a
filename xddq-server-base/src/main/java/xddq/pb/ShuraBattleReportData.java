// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.ShuraBattleReportData}
 */
public final class ShuraBattleReportData extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.ShuraBattleReportData)
    ShuraBattleReportDataOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      ShuraBattleReportData.class.getName());
  }
  // Use ShuraBattleReportData.newBuilder() to construct.
  private ShuraBattleReportData(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private ShuraBattleReportData() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_ShuraBattleReportData_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_ShuraBattleReportData_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.ShuraBattleReportData.class, xddq.pb.ShuraBattleReportData.Builder.class);
  }

  private int bitField0_;
  public static final int TEAM1_FIELD_NUMBER = 1;
  private xddq.pb.ShuraBattleReportTeamInfo team1_;
  /**
   * <code>optional .xddq.pb.ShuraBattleReportTeamInfo team1 = 1;</code>
   * @return Whether the team1 field is set.
   */
  @java.lang.Override
  public boolean hasTeam1() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional .xddq.pb.ShuraBattleReportTeamInfo team1 = 1;</code>
   * @return The team1.
   */
  @java.lang.Override
  public xddq.pb.ShuraBattleReportTeamInfo getTeam1() {
    return team1_ == null ? xddq.pb.ShuraBattleReportTeamInfo.getDefaultInstance() : team1_;
  }
  /**
   * <code>optional .xddq.pb.ShuraBattleReportTeamInfo team1 = 1;</code>
   */
  @java.lang.Override
  public xddq.pb.ShuraBattleReportTeamInfoOrBuilder getTeam1OrBuilder() {
    return team1_ == null ? xddq.pb.ShuraBattleReportTeamInfo.getDefaultInstance() : team1_;
  }

  public static final int TEAM2_FIELD_NUMBER = 2;
  private xddq.pb.ShuraBattleReportTeamInfo team2_;
  /**
   * <code>optional .xddq.pb.ShuraBattleReportTeamInfo team2 = 2;</code>
   * @return Whether the team2 field is set.
   */
  @java.lang.Override
  public boolean hasTeam2() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional .xddq.pb.ShuraBattleReportTeamInfo team2 = 2;</code>
   * @return The team2.
   */
  @java.lang.Override
  public xddq.pb.ShuraBattleReportTeamInfo getTeam2() {
    return team2_ == null ? xddq.pb.ShuraBattleReportTeamInfo.getDefaultInstance() : team2_;
  }
  /**
   * <code>optional .xddq.pb.ShuraBattleReportTeamInfo team2 = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.ShuraBattleReportTeamInfoOrBuilder getTeam2OrBuilder() {
    return team2_ == null ? xddq.pb.ShuraBattleReportTeamInfo.getDefaultInstance() : team2_;
  }

  public static final int FLOOR_FIELD_NUMBER = 3;
  private int floor_ = 0;
  /**
   * <code>optional int32 floor = 3;</code>
   * @return Whether the floor field is set.
   */
  @java.lang.Override
  public boolean hasFloor() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional int32 floor = 3;</code>
   * @return The floor.
   */
  @java.lang.Override
  public int getFloor() {
    return floor_;
  }

  public static final int WINNERID_FIELD_NUMBER = 4;
  private long winnerId_ = 0L;
  /**
   * <code>optional int64 winnerId = 4;</code>
   * @return Whether the winnerId field is set.
   */
  @java.lang.Override
  public boolean hasWinnerId() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional int64 winnerId = 4;</code>
   * @return The winnerId.
   */
  @java.lang.Override
  public long getWinnerId() {
    return winnerId_;
  }

  public static final int TIMEID_FIELD_NUMBER = 5;
  private long timeId_ = 0L;
  /**
   * <code>optional int64 timeId = 5;</code>
   * @return Whether the timeId field is set.
   */
  @java.lang.Override
  public boolean hasTimeId() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>optional int64 timeId = 5;</code>
   * @return The timeId.
   */
  @java.lang.Override
  public long getTimeId() {
    return timeId_;
  }

  public static final int BATTLEID_FIELD_NUMBER = 6;
  private long battleId_ = 0L;
  /**
   * <code>optional int64 battleId = 6;</code>
   * @return Whether the battleId field is set.
   */
  @java.lang.Override
  public boolean hasBattleId() {
    return ((bitField0_ & 0x00000020) != 0);
  }
  /**
   * <code>optional int64 battleId = 6;</code>
   * @return The battleId.
   */
  @java.lang.Override
  public long getBattleId() {
    return battleId_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeMessage(1, getTeam1());
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeMessage(2, getTeam2());
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt32(3, floor_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt64(4, winnerId_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      output.writeInt64(5, timeId_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      output.writeInt64(6, battleId_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, getTeam1());
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getTeam2());
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(3, floor_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(4, winnerId_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(5, timeId_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(6, battleId_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.ShuraBattleReportData)) {
      return super.equals(obj);
    }
    xddq.pb.ShuraBattleReportData other = (xddq.pb.ShuraBattleReportData) obj;

    if (hasTeam1() != other.hasTeam1()) return false;
    if (hasTeam1()) {
      if (!getTeam1()
          .equals(other.getTeam1())) return false;
    }
    if (hasTeam2() != other.hasTeam2()) return false;
    if (hasTeam2()) {
      if (!getTeam2()
          .equals(other.getTeam2())) return false;
    }
    if (hasFloor() != other.hasFloor()) return false;
    if (hasFloor()) {
      if (getFloor()
          != other.getFloor()) return false;
    }
    if (hasWinnerId() != other.hasWinnerId()) return false;
    if (hasWinnerId()) {
      if (getWinnerId()
          != other.getWinnerId()) return false;
    }
    if (hasTimeId() != other.hasTimeId()) return false;
    if (hasTimeId()) {
      if (getTimeId()
          != other.getTimeId()) return false;
    }
    if (hasBattleId() != other.hasBattleId()) return false;
    if (hasBattleId()) {
      if (getBattleId()
          != other.getBattleId()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasTeam1()) {
      hash = (37 * hash) + TEAM1_FIELD_NUMBER;
      hash = (53 * hash) + getTeam1().hashCode();
    }
    if (hasTeam2()) {
      hash = (37 * hash) + TEAM2_FIELD_NUMBER;
      hash = (53 * hash) + getTeam2().hashCode();
    }
    if (hasFloor()) {
      hash = (37 * hash) + FLOOR_FIELD_NUMBER;
      hash = (53 * hash) + getFloor();
    }
    if (hasWinnerId()) {
      hash = (37 * hash) + WINNERID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getWinnerId());
    }
    if (hasTimeId()) {
      hash = (37 * hash) + TIMEID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTimeId());
    }
    if (hasBattleId()) {
      hash = (37 * hash) + BATTLEID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getBattleId());
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.ShuraBattleReportData parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ShuraBattleReportData parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ShuraBattleReportData parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ShuraBattleReportData parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ShuraBattleReportData parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ShuraBattleReportData parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ShuraBattleReportData parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.ShuraBattleReportData parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.ShuraBattleReportData parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.ShuraBattleReportData parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.ShuraBattleReportData parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.ShuraBattleReportData parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.ShuraBattleReportData prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.ShuraBattleReportData}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.ShuraBattleReportData)
      xddq.pb.ShuraBattleReportDataOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ShuraBattleReportData_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ShuraBattleReportData_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.ShuraBattleReportData.class, xddq.pb.ShuraBattleReportData.Builder.class);
    }

    // Construct using xddq.pb.ShuraBattleReportData.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetTeam1FieldBuilder();
        internalGetTeam2FieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      team1_ = null;
      if (team1Builder_ != null) {
        team1Builder_.dispose();
        team1Builder_ = null;
      }
      team2_ = null;
      if (team2Builder_ != null) {
        team2Builder_.dispose();
        team2Builder_ = null;
      }
      floor_ = 0;
      winnerId_ = 0L;
      timeId_ = 0L;
      battleId_ = 0L;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ShuraBattleReportData_descriptor;
    }

    @java.lang.Override
    public xddq.pb.ShuraBattleReportData getDefaultInstanceForType() {
      return xddq.pb.ShuraBattleReportData.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.ShuraBattleReportData build() {
      xddq.pb.ShuraBattleReportData result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.ShuraBattleReportData buildPartial() {
      xddq.pb.ShuraBattleReportData result = new xddq.pb.ShuraBattleReportData(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.ShuraBattleReportData result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.team1_ = team1Builder_ == null
            ? team1_
            : team1Builder_.build();
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.team2_ = team2Builder_ == null
            ? team2_
            : team2Builder_.build();
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.floor_ = floor_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.winnerId_ = winnerId_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.timeId_ = timeId_;
        to_bitField0_ |= 0x00000010;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.battleId_ = battleId_;
        to_bitField0_ |= 0x00000020;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.ShuraBattleReportData) {
        return mergeFrom((xddq.pb.ShuraBattleReportData)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.ShuraBattleReportData other) {
      if (other == xddq.pb.ShuraBattleReportData.getDefaultInstance()) return this;
      if (other.hasTeam1()) {
        mergeTeam1(other.getTeam1());
      }
      if (other.hasTeam2()) {
        mergeTeam2(other.getTeam2());
      }
      if (other.hasFloor()) {
        setFloor(other.getFloor());
      }
      if (other.hasWinnerId()) {
        setWinnerId(other.getWinnerId());
      }
      if (other.hasTimeId()) {
        setTimeId(other.getTimeId());
      }
      if (other.hasBattleId()) {
        setBattleId(other.getBattleId());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              input.readMessage(
                  internalGetTeam1FieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 18: {
              input.readMessage(
                  internalGetTeam2FieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 24: {
              floor_ = input.readInt32();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 32: {
              winnerId_ = input.readInt64();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            case 40: {
              timeId_ = input.readInt64();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            case 48: {
              battleId_ = input.readInt64();
              bitField0_ |= 0x00000020;
              break;
            } // case 48
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private xddq.pb.ShuraBattleReportTeamInfo team1_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.ShuraBattleReportTeamInfo, xddq.pb.ShuraBattleReportTeamInfo.Builder, xddq.pb.ShuraBattleReportTeamInfoOrBuilder> team1Builder_;
    /**
     * <code>optional .xddq.pb.ShuraBattleReportTeamInfo team1 = 1;</code>
     * @return Whether the team1 field is set.
     */
    public boolean hasTeam1() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .xddq.pb.ShuraBattleReportTeamInfo team1 = 1;</code>
     * @return The team1.
     */
    public xddq.pb.ShuraBattleReportTeamInfo getTeam1() {
      if (team1Builder_ == null) {
        return team1_ == null ? xddq.pb.ShuraBattleReportTeamInfo.getDefaultInstance() : team1_;
      } else {
        return team1Builder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.ShuraBattleReportTeamInfo team1 = 1;</code>
     */
    public Builder setTeam1(xddq.pb.ShuraBattleReportTeamInfo value) {
      if (team1Builder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        team1_ = value;
      } else {
        team1Builder_.setMessage(value);
      }
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ShuraBattleReportTeamInfo team1 = 1;</code>
     */
    public Builder setTeam1(
        xddq.pb.ShuraBattleReportTeamInfo.Builder builderForValue) {
      if (team1Builder_ == null) {
        team1_ = builderForValue.build();
      } else {
        team1Builder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ShuraBattleReportTeamInfo team1 = 1;</code>
     */
    public Builder mergeTeam1(xddq.pb.ShuraBattleReportTeamInfo value) {
      if (team1Builder_ == null) {
        if (((bitField0_ & 0x00000001) != 0) &&
          team1_ != null &&
          team1_ != xddq.pb.ShuraBattleReportTeamInfo.getDefaultInstance()) {
          getTeam1Builder().mergeFrom(value);
        } else {
          team1_ = value;
        }
      } else {
        team1Builder_.mergeFrom(value);
      }
      if (team1_ != null) {
        bitField0_ |= 0x00000001;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.ShuraBattleReportTeamInfo team1 = 1;</code>
     */
    public Builder clearTeam1() {
      bitField0_ = (bitField0_ & ~0x00000001);
      team1_ = null;
      if (team1Builder_ != null) {
        team1Builder_.dispose();
        team1Builder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ShuraBattleReportTeamInfo team1 = 1;</code>
     */
    public xddq.pb.ShuraBattleReportTeamInfo.Builder getTeam1Builder() {
      bitField0_ |= 0x00000001;
      onChanged();
      return internalGetTeam1FieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.ShuraBattleReportTeamInfo team1 = 1;</code>
     */
    public xddq.pb.ShuraBattleReportTeamInfoOrBuilder getTeam1OrBuilder() {
      if (team1Builder_ != null) {
        return team1Builder_.getMessageOrBuilder();
      } else {
        return team1_ == null ?
            xddq.pb.ShuraBattleReportTeamInfo.getDefaultInstance() : team1_;
      }
    }
    /**
     * <code>optional .xddq.pb.ShuraBattleReportTeamInfo team1 = 1;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.ShuraBattleReportTeamInfo, xddq.pb.ShuraBattleReportTeamInfo.Builder, xddq.pb.ShuraBattleReportTeamInfoOrBuilder> 
        internalGetTeam1FieldBuilder() {
      if (team1Builder_ == null) {
        team1Builder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.ShuraBattleReportTeamInfo, xddq.pb.ShuraBattleReportTeamInfo.Builder, xddq.pb.ShuraBattleReportTeamInfoOrBuilder>(
                getTeam1(),
                getParentForChildren(),
                isClean());
        team1_ = null;
      }
      return team1Builder_;
    }

    private xddq.pb.ShuraBattleReportTeamInfo team2_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.ShuraBattleReportTeamInfo, xddq.pb.ShuraBattleReportTeamInfo.Builder, xddq.pb.ShuraBattleReportTeamInfoOrBuilder> team2Builder_;
    /**
     * <code>optional .xddq.pb.ShuraBattleReportTeamInfo team2 = 2;</code>
     * @return Whether the team2 field is set.
     */
    public boolean hasTeam2() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .xddq.pb.ShuraBattleReportTeamInfo team2 = 2;</code>
     * @return The team2.
     */
    public xddq.pb.ShuraBattleReportTeamInfo getTeam2() {
      if (team2Builder_ == null) {
        return team2_ == null ? xddq.pb.ShuraBattleReportTeamInfo.getDefaultInstance() : team2_;
      } else {
        return team2Builder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.ShuraBattleReportTeamInfo team2 = 2;</code>
     */
    public Builder setTeam2(xddq.pb.ShuraBattleReportTeamInfo value) {
      if (team2Builder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        team2_ = value;
      } else {
        team2Builder_.setMessage(value);
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ShuraBattleReportTeamInfo team2 = 2;</code>
     */
    public Builder setTeam2(
        xddq.pb.ShuraBattleReportTeamInfo.Builder builderForValue) {
      if (team2Builder_ == null) {
        team2_ = builderForValue.build();
      } else {
        team2Builder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ShuraBattleReportTeamInfo team2 = 2;</code>
     */
    public Builder mergeTeam2(xddq.pb.ShuraBattleReportTeamInfo value) {
      if (team2Builder_ == null) {
        if (((bitField0_ & 0x00000002) != 0) &&
          team2_ != null &&
          team2_ != xddq.pb.ShuraBattleReportTeamInfo.getDefaultInstance()) {
          getTeam2Builder().mergeFrom(value);
        } else {
          team2_ = value;
        }
      } else {
        team2Builder_.mergeFrom(value);
      }
      if (team2_ != null) {
        bitField0_ |= 0x00000002;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.ShuraBattleReportTeamInfo team2 = 2;</code>
     */
    public Builder clearTeam2() {
      bitField0_ = (bitField0_ & ~0x00000002);
      team2_ = null;
      if (team2Builder_ != null) {
        team2Builder_.dispose();
        team2Builder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ShuraBattleReportTeamInfo team2 = 2;</code>
     */
    public xddq.pb.ShuraBattleReportTeamInfo.Builder getTeam2Builder() {
      bitField0_ |= 0x00000002;
      onChanged();
      return internalGetTeam2FieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.ShuraBattleReportTeamInfo team2 = 2;</code>
     */
    public xddq.pb.ShuraBattleReportTeamInfoOrBuilder getTeam2OrBuilder() {
      if (team2Builder_ != null) {
        return team2Builder_.getMessageOrBuilder();
      } else {
        return team2_ == null ?
            xddq.pb.ShuraBattleReportTeamInfo.getDefaultInstance() : team2_;
      }
    }
    /**
     * <code>optional .xddq.pb.ShuraBattleReportTeamInfo team2 = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.ShuraBattleReportTeamInfo, xddq.pb.ShuraBattleReportTeamInfo.Builder, xddq.pb.ShuraBattleReportTeamInfoOrBuilder> 
        internalGetTeam2FieldBuilder() {
      if (team2Builder_ == null) {
        team2Builder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.ShuraBattleReportTeamInfo, xddq.pb.ShuraBattleReportTeamInfo.Builder, xddq.pb.ShuraBattleReportTeamInfoOrBuilder>(
                getTeam2(),
                getParentForChildren(),
                isClean());
        team2_ = null;
      }
      return team2Builder_;
    }

    private int floor_ ;
    /**
     * <code>optional int32 floor = 3;</code>
     * @return Whether the floor field is set.
     */
    @java.lang.Override
    public boolean hasFloor() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int32 floor = 3;</code>
     * @return The floor.
     */
    @java.lang.Override
    public int getFloor() {
      return floor_;
    }
    /**
     * <code>optional int32 floor = 3;</code>
     * @param value The floor to set.
     * @return This builder for chaining.
     */
    public Builder setFloor(int value) {

      floor_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 floor = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearFloor() {
      bitField0_ = (bitField0_ & ~0x00000004);
      floor_ = 0;
      onChanged();
      return this;
    }

    private long winnerId_ ;
    /**
     * <code>optional int64 winnerId = 4;</code>
     * @return Whether the winnerId field is set.
     */
    @java.lang.Override
    public boolean hasWinnerId() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int64 winnerId = 4;</code>
     * @return The winnerId.
     */
    @java.lang.Override
    public long getWinnerId() {
      return winnerId_;
    }
    /**
     * <code>optional int64 winnerId = 4;</code>
     * @param value The winnerId to set.
     * @return This builder for chaining.
     */
    public Builder setWinnerId(long value) {

      winnerId_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 winnerId = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearWinnerId() {
      bitField0_ = (bitField0_ & ~0x00000008);
      winnerId_ = 0L;
      onChanged();
      return this;
    }

    private long timeId_ ;
    /**
     * <code>optional int64 timeId = 5;</code>
     * @return Whether the timeId field is set.
     */
    @java.lang.Override
    public boolean hasTimeId() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional int64 timeId = 5;</code>
     * @return The timeId.
     */
    @java.lang.Override
    public long getTimeId() {
      return timeId_;
    }
    /**
     * <code>optional int64 timeId = 5;</code>
     * @param value The timeId to set.
     * @return This builder for chaining.
     */
    public Builder setTimeId(long value) {

      timeId_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 timeId = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearTimeId() {
      bitField0_ = (bitField0_ & ~0x00000010);
      timeId_ = 0L;
      onChanged();
      return this;
    }

    private long battleId_ ;
    /**
     * <code>optional int64 battleId = 6;</code>
     * @return Whether the battleId field is set.
     */
    @java.lang.Override
    public boolean hasBattleId() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional int64 battleId = 6;</code>
     * @return The battleId.
     */
    @java.lang.Override
    public long getBattleId() {
      return battleId_;
    }
    /**
     * <code>optional int64 battleId = 6;</code>
     * @param value The battleId to set.
     * @return This builder for chaining.
     */
    public Builder setBattleId(long value) {

      battleId_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 battleId = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearBattleId() {
      bitField0_ = (bitField0_ & ~0x00000020);
      battleId_ = 0L;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.ShuraBattleReportData)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.ShuraBattleReportData)
  private static final xddq.pb.ShuraBattleReportData DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.ShuraBattleReportData();
  }

  public static xddq.pb.ShuraBattleReportData getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ShuraBattleReportData>
      PARSER = new com.google.protobuf.AbstractParser<ShuraBattleReportData>() {
    @java.lang.Override
    public ShuraBattleReportData parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<ShuraBattleReportData> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ShuraBattleReportData> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.ShuraBattleReportData getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

