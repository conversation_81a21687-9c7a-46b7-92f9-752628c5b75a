// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.WarSeasonGiftInfoResp}
 */
public final class WarSeasonGiftInfoResp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.WarSeasonGiftInfoResp)
    WarSeasonGiftInfoRespOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      WarSeasonGiftInfoResp.class.getName());
  }
  // Use WarSeasonGiftInfoResp.newBuilder() to construct.
  private WarSeasonGiftInfoResp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private WarSeasonGiftInfoResp() {
    giftInfo_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_WarSeasonGiftInfoResp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_WarSeasonGiftInfoResp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.WarSeasonGiftInfoResp.class, xddq.pb.WarSeasonGiftInfoResp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int GIFTINFO_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.WarSeasonGiftInfo> giftInfo_;
  /**
   * <code>repeated .xddq.pb.WarSeasonGiftInfo giftInfo = 2;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.WarSeasonGiftInfo> getGiftInfoList() {
    return giftInfo_;
  }
  /**
   * <code>repeated .xddq.pb.WarSeasonGiftInfo giftInfo = 2;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.WarSeasonGiftInfoOrBuilder> 
      getGiftInfoOrBuilderList() {
    return giftInfo_;
  }
  /**
   * <code>repeated .xddq.pb.WarSeasonGiftInfo giftInfo = 2;</code>
   */
  @java.lang.Override
  public int getGiftInfoCount() {
    return giftInfo_.size();
  }
  /**
   * <code>repeated .xddq.pb.WarSeasonGiftInfo giftInfo = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.WarSeasonGiftInfo getGiftInfo(int index) {
    return giftInfo_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.WarSeasonGiftInfo giftInfo = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.WarSeasonGiftInfoOrBuilder getGiftInfoOrBuilder(
      int index) {
    return giftInfo_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    for (int i = 0; i < giftInfo_.size(); i++) {
      output.writeMessage(2, giftInfo_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    for (int i = 0; i < giftInfo_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, giftInfo_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.WarSeasonGiftInfoResp)) {
      return super.equals(obj);
    }
    xddq.pb.WarSeasonGiftInfoResp other = (xddq.pb.WarSeasonGiftInfoResp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (!getGiftInfoList()
        .equals(other.getGiftInfoList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (getGiftInfoCount() > 0) {
      hash = (37 * hash) + GIFTINFO_FIELD_NUMBER;
      hash = (53 * hash) + getGiftInfoList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.WarSeasonGiftInfoResp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.WarSeasonGiftInfoResp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.WarSeasonGiftInfoResp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.WarSeasonGiftInfoResp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.WarSeasonGiftInfoResp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.WarSeasonGiftInfoResp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.WarSeasonGiftInfoResp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.WarSeasonGiftInfoResp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.WarSeasonGiftInfoResp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.WarSeasonGiftInfoResp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.WarSeasonGiftInfoResp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.WarSeasonGiftInfoResp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.WarSeasonGiftInfoResp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.WarSeasonGiftInfoResp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.WarSeasonGiftInfoResp)
      xddq.pb.WarSeasonGiftInfoRespOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_WarSeasonGiftInfoResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_WarSeasonGiftInfoResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.WarSeasonGiftInfoResp.class, xddq.pb.WarSeasonGiftInfoResp.Builder.class);
    }

    // Construct using xddq.pb.WarSeasonGiftInfoResp.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      if (giftInfoBuilder_ == null) {
        giftInfo_ = java.util.Collections.emptyList();
      } else {
        giftInfo_ = null;
        giftInfoBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000002);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_WarSeasonGiftInfoResp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.WarSeasonGiftInfoResp getDefaultInstanceForType() {
      return xddq.pb.WarSeasonGiftInfoResp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.WarSeasonGiftInfoResp build() {
      xddq.pb.WarSeasonGiftInfoResp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.WarSeasonGiftInfoResp buildPartial() {
      xddq.pb.WarSeasonGiftInfoResp result = new xddq.pb.WarSeasonGiftInfoResp(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.WarSeasonGiftInfoResp result) {
      if (giftInfoBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          giftInfo_ = java.util.Collections.unmodifiableList(giftInfo_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.giftInfo_ = giftInfo_;
      } else {
        result.giftInfo_ = giftInfoBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.WarSeasonGiftInfoResp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.WarSeasonGiftInfoResp) {
        return mergeFrom((xddq.pb.WarSeasonGiftInfoResp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.WarSeasonGiftInfoResp other) {
      if (other == xddq.pb.WarSeasonGiftInfoResp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (giftInfoBuilder_ == null) {
        if (!other.giftInfo_.isEmpty()) {
          if (giftInfo_.isEmpty()) {
            giftInfo_ = other.giftInfo_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureGiftInfoIsMutable();
            giftInfo_.addAll(other.giftInfo_);
          }
          onChanged();
        }
      } else {
        if (!other.giftInfo_.isEmpty()) {
          if (giftInfoBuilder_.isEmpty()) {
            giftInfoBuilder_.dispose();
            giftInfoBuilder_ = null;
            giftInfo_ = other.giftInfo_;
            bitField0_ = (bitField0_ & ~0x00000002);
            giftInfoBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetGiftInfoFieldBuilder() : null;
          } else {
            giftInfoBuilder_.addAllMessages(other.giftInfo_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              xddq.pb.WarSeasonGiftInfo m =
                  input.readMessage(
                      xddq.pb.WarSeasonGiftInfo.parser(),
                      extensionRegistry);
              if (giftInfoBuilder_ == null) {
                ensureGiftInfoIsMutable();
                giftInfo_.add(m);
              } else {
                giftInfoBuilder_.addMessage(m);
              }
              break;
            } // case 18
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.WarSeasonGiftInfo> giftInfo_ =
      java.util.Collections.emptyList();
    private void ensureGiftInfoIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        giftInfo_ = new java.util.ArrayList<xddq.pb.WarSeasonGiftInfo>(giftInfo_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.WarSeasonGiftInfo, xddq.pb.WarSeasonGiftInfo.Builder, xddq.pb.WarSeasonGiftInfoOrBuilder> giftInfoBuilder_;

    /**
     * <code>repeated .xddq.pb.WarSeasonGiftInfo giftInfo = 2;</code>
     */
    public java.util.List<xddq.pb.WarSeasonGiftInfo> getGiftInfoList() {
      if (giftInfoBuilder_ == null) {
        return java.util.Collections.unmodifiableList(giftInfo_);
      } else {
        return giftInfoBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonGiftInfo giftInfo = 2;</code>
     */
    public int getGiftInfoCount() {
      if (giftInfoBuilder_ == null) {
        return giftInfo_.size();
      } else {
        return giftInfoBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonGiftInfo giftInfo = 2;</code>
     */
    public xddq.pb.WarSeasonGiftInfo getGiftInfo(int index) {
      if (giftInfoBuilder_ == null) {
        return giftInfo_.get(index);
      } else {
        return giftInfoBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonGiftInfo giftInfo = 2;</code>
     */
    public Builder setGiftInfo(
        int index, xddq.pb.WarSeasonGiftInfo value) {
      if (giftInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureGiftInfoIsMutable();
        giftInfo_.set(index, value);
        onChanged();
      } else {
        giftInfoBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonGiftInfo giftInfo = 2;</code>
     */
    public Builder setGiftInfo(
        int index, xddq.pb.WarSeasonGiftInfo.Builder builderForValue) {
      if (giftInfoBuilder_ == null) {
        ensureGiftInfoIsMutable();
        giftInfo_.set(index, builderForValue.build());
        onChanged();
      } else {
        giftInfoBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonGiftInfo giftInfo = 2;</code>
     */
    public Builder addGiftInfo(xddq.pb.WarSeasonGiftInfo value) {
      if (giftInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureGiftInfoIsMutable();
        giftInfo_.add(value);
        onChanged();
      } else {
        giftInfoBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonGiftInfo giftInfo = 2;</code>
     */
    public Builder addGiftInfo(
        int index, xddq.pb.WarSeasonGiftInfo value) {
      if (giftInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureGiftInfoIsMutable();
        giftInfo_.add(index, value);
        onChanged();
      } else {
        giftInfoBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonGiftInfo giftInfo = 2;</code>
     */
    public Builder addGiftInfo(
        xddq.pb.WarSeasonGiftInfo.Builder builderForValue) {
      if (giftInfoBuilder_ == null) {
        ensureGiftInfoIsMutable();
        giftInfo_.add(builderForValue.build());
        onChanged();
      } else {
        giftInfoBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonGiftInfo giftInfo = 2;</code>
     */
    public Builder addGiftInfo(
        int index, xddq.pb.WarSeasonGiftInfo.Builder builderForValue) {
      if (giftInfoBuilder_ == null) {
        ensureGiftInfoIsMutable();
        giftInfo_.add(index, builderForValue.build());
        onChanged();
      } else {
        giftInfoBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonGiftInfo giftInfo = 2;</code>
     */
    public Builder addAllGiftInfo(
        java.lang.Iterable<? extends xddq.pb.WarSeasonGiftInfo> values) {
      if (giftInfoBuilder_ == null) {
        ensureGiftInfoIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, giftInfo_);
        onChanged();
      } else {
        giftInfoBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonGiftInfo giftInfo = 2;</code>
     */
    public Builder clearGiftInfo() {
      if (giftInfoBuilder_ == null) {
        giftInfo_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        giftInfoBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonGiftInfo giftInfo = 2;</code>
     */
    public Builder removeGiftInfo(int index) {
      if (giftInfoBuilder_ == null) {
        ensureGiftInfoIsMutable();
        giftInfo_.remove(index);
        onChanged();
      } else {
        giftInfoBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonGiftInfo giftInfo = 2;</code>
     */
    public xddq.pb.WarSeasonGiftInfo.Builder getGiftInfoBuilder(
        int index) {
      return internalGetGiftInfoFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonGiftInfo giftInfo = 2;</code>
     */
    public xddq.pb.WarSeasonGiftInfoOrBuilder getGiftInfoOrBuilder(
        int index) {
      if (giftInfoBuilder_ == null) {
        return giftInfo_.get(index);  } else {
        return giftInfoBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonGiftInfo giftInfo = 2;</code>
     */
    public java.util.List<? extends xddq.pb.WarSeasonGiftInfoOrBuilder> 
         getGiftInfoOrBuilderList() {
      if (giftInfoBuilder_ != null) {
        return giftInfoBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(giftInfo_);
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonGiftInfo giftInfo = 2;</code>
     */
    public xddq.pb.WarSeasonGiftInfo.Builder addGiftInfoBuilder() {
      return internalGetGiftInfoFieldBuilder().addBuilder(
          xddq.pb.WarSeasonGiftInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonGiftInfo giftInfo = 2;</code>
     */
    public xddq.pb.WarSeasonGiftInfo.Builder addGiftInfoBuilder(
        int index) {
      return internalGetGiftInfoFieldBuilder().addBuilder(
          index, xddq.pb.WarSeasonGiftInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonGiftInfo giftInfo = 2;</code>
     */
    public java.util.List<xddq.pb.WarSeasonGiftInfo.Builder> 
         getGiftInfoBuilderList() {
      return internalGetGiftInfoFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.WarSeasonGiftInfo, xddq.pb.WarSeasonGiftInfo.Builder, xddq.pb.WarSeasonGiftInfoOrBuilder> 
        internalGetGiftInfoFieldBuilder() {
      if (giftInfoBuilder_ == null) {
        giftInfoBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.WarSeasonGiftInfo, xddq.pb.WarSeasonGiftInfo.Builder, xddq.pb.WarSeasonGiftInfoOrBuilder>(
                giftInfo_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        giftInfo_ = null;
      }
      return giftInfoBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.WarSeasonGiftInfoResp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.WarSeasonGiftInfoResp)
  private static final xddq.pb.WarSeasonGiftInfoResp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.WarSeasonGiftInfoResp();
  }

  public static xddq.pb.WarSeasonGiftInfoResp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<WarSeasonGiftInfoResp>
      PARSER = new com.google.protobuf.AbstractParser<WarSeasonGiftInfoResp>() {
    @java.lang.Override
    public WarSeasonGiftInfoResp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<WarSeasonGiftInfoResp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<WarSeasonGiftInfoResp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.WarSeasonGiftInfoResp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

