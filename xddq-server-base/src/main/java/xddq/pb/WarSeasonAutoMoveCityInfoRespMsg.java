// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.WarSeasonAutoMoveCityInfoRespMsg}
 */
public final class WarSeasonAutoMoveCityInfoRespMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.WarSeasonAutoMoveCityInfoRespMsg)
    WarSeasonAutoMoveCityInfoRespMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      WarSeasonAutoMoveCityInfoRespMsg.class.getName());
  }
  // Use WarSeasonAutoMoveCityInfoRespMsg.newBuilder() to construct.
  private WarSeasonAutoMoveCityInfoRespMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private WarSeasonAutoMoveCityInfoRespMsg() {
    infos_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_WarSeasonAutoMoveCityInfoRespMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_WarSeasonAutoMoveCityInfoRespMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.WarSeasonAutoMoveCityInfoRespMsg.class, xddq.pb.WarSeasonAutoMoveCityInfoRespMsg.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int INFOS_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.WarSeasonAutoMoveCityInfo> infos_;
  /**
   * <code>repeated .xddq.pb.WarSeasonAutoMoveCityInfo infos = 2;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.WarSeasonAutoMoveCityInfo> getInfosList() {
    return infos_;
  }
  /**
   * <code>repeated .xddq.pb.WarSeasonAutoMoveCityInfo infos = 2;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.WarSeasonAutoMoveCityInfoOrBuilder> 
      getInfosOrBuilderList() {
    return infos_;
  }
  /**
   * <code>repeated .xddq.pb.WarSeasonAutoMoveCityInfo infos = 2;</code>
   */
  @java.lang.Override
  public int getInfosCount() {
    return infos_.size();
  }
  /**
   * <code>repeated .xddq.pb.WarSeasonAutoMoveCityInfo infos = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.WarSeasonAutoMoveCityInfo getInfos(int index) {
    return infos_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.WarSeasonAutoMoveCityInfo infos = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.WarSeasonAutoMoveCityInfoOrBuilder getInfosOrBuilder(
      int index) {
    return infos_.get(index);
  }

  public static final int SELFINFO_FIELD_NUMBER = 3;
  private xddq.pb.WarSeasonAutoMoveCityInfo selfInfo_;
  /**
   * <code>optional .xddq.pb.WarSeasonAutoMoveCityInfo selfInfo = 3;</code>
   * @return Whether the selfInfo field is set.
   */
  @java.lang.Override
  public boolean hasSelfInfo() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional .xddq.pb.WarSeasonAutoMoveCityInfo selfInfo = 3;</code>
   * @return The selfInfo.
   */
  @java.lang.Override
  public xddq.pb.WarSeasonAutoMoveCityInfo getSelfInfo() {
    return selfInfo_ == null ? xddq.pb.WarSeasonAutoMoveCityInfo.getDefaultInstance() : selfInfo_;
  }
  /**
   * <code>optional .xddq.pb.WarSeasonAutoMoveCityInfo selfInfo = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.WarSeasonAutoMoveCityInfoOrBuilder getSelfInfoOrBuilder() {
    return selfInfo_ == null ? xddq.pb.WarSeasonAutoMoveCityInfo.getDefaultInstance() : selfInfo_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    for (int i = 0; i < infos_.size(); i++) {
      output.writeMessage(2, infos_.get(i));
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeMessage(3, getSelfInfo());
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    for (int i = 0; i < infos_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, infos_.get(i));
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, getSelfInfo());
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.WarSeasonAutoMoveCityInfoRespMsg)) {
      return super.equals(obj);
    }
    xddq.pb.WarSeasonAutoMoveCityInfoRespMsg other = (xddq.pb.WarSeasonAutoMoveCityInfoRespMsg) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (!getInfosList()
        .equals(other.getInfosList())) return false;
    if (hasSelfInfo() != other.hasSelfInfo()) return false;
    if (hasSelfInfo()) {
      if (!getSelfInfo()
          .equals(other.getSelfInfo())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (getInfosCount() > 0) {
      hash = (37 * hash) + INFOS_FIELD_NUMBER;
      hash = (53 * hash) + getInfosList().hashCode();
    }
    if (hasSelfInfo()) {
      hash = (37 * hash) + SELFINFO_FIELD_NUMBER;
      hash = (53 * hash) + getSelfInfo().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.WarSeasonAutoMoveCityInfoRespMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.WarSeasonAutoMoveCityInfoRespMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.WarSeasonAutoMoveCityInfoRespMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.WarSeasonAutoMoveCityInfoRespMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.WarSeasonAutoMoveCityInfoRespMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.WarSeasonAutoMoveCityInfoRespMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.WarSeasonAutoMoveCityInfoRespMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.WarSeasonAutoMoveCityInfoRespMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.WarSeasonAutoMoveCityInfoRespMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.WarSeasonAutoMoveCityInfoRespMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.WarSeasonAutoMoveCityInfoRespMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.WarSeasonAutoMoveCityInfoRespMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.WarSeasonAutoMoveCityInfoRespMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.WarSeasonAutoMoveCityInfoRespMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.WarSeasonAutoMoveCityInfoRespMsg)
      xddq.pb.WarSeasonAutoMoveCityInfoRespMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_WarSeasonAutoMoveCityInfoRespMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_WarSeasonAutoMoveCityInfoRespMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.WarSeasonAutoMoveCityInfoRespMsg.class, xddq.pb.WarSeasonAutoMoveCityInfoRespMsg.Builder.class);
    }

    // Construct using xddq.pb.WarSeasonAutoMoveCityInfoRespMsg.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetInfosFieldBuilder();
        internalGetSelfInfoFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      if (infosBuilder_ == null) {
        infos_ = java.util.Collections.emptyList();
      } else {
        infos_ = null;
        infosBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000002);
      selfInfo_ = null;
      if (selfInfoBuilder_ != null) {
        selfInfoBuilder_.dispose();
        selfInfoBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_WarSeasonAutoMoveCityInfoRespMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.WarSeasonAutoMoveCityInfoRespMsg getDefaultInstanceForType() {
      return xddq.pb.WarSeasonAutoMoveCityInfoRespMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.WarSeasonAutoMoveCityInfoRespMsg build() {
      xddq.pb.WarSeasonAutoMoveCityInfoRespMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.WarSeasonAutoMoveCityInfoRespMsg buildPartial() {
      xddq.pb.WarSeasonAutoMoveCityInfoRespMsg result = new xddq.pb.WarSeasonAutoMoveCityInfoRespMsg(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.WarSeasonAutoMoveCityInfoRespMsg result) {
      if (infosBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          infos_ = java.util.Collections.unmodifiableList(infos_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.infos_ = infos_;
      } else {
        result.infos_ = infosBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.WarSeasonAutoMoveCityInfoRespMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.selfInfo_ = selfInfoBuilder_ == null
            ? selfInfo_
            : selfInfoBuilder_.build();
        to_bitField0_ |= 0x00000002;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.WarSeasonAutoMoveCityInfoRespMsg) {
        return mergeFrom((xddq.pb.WarSeasonAutoMoveCityInfoRespMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.WarSeasonAutoMoveCityInfoRespMsg other) {
      if (other == xddq.pb.WarSeasonAutoMoveCityInfoRespMsg.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (infosBuilder_ == null) {
        if (!other.infos_.isEmpty()) {
          if (infos_.isEmpty()) {
            infos_ = other.infos_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureInfosIsMutable();
            infos_.addAll(other.infos_);
          }
          onChanged();
        }
      } else {
        if (!other.infos_.isEmpty()) {
          if (infosBuilder_.isEmpty()) {
            infosBuilder_.dispose();
            infosBuilder_ = null;
            infos_ = other.infos_;
            bitField0_ = (bitField0_ & ~0x00000002);
            infosBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetInfosFieldBuilder() : null;
          } else {
            infosBuilder_.addAllMessages(other.infos_);
          }
        }
      }
      if (other.hasSelfInfo()) {
        mergeSelfInfo(other.getSelfInfo());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              xddq.pb.WarSeasonAutoMoveCityInfo m =
                  input.readMessage(
                      xddq.pb.WarSeasonAutoMoveCityInfo.parser(),
                      extensionRegistry);
              if (infosBuilder_ == null) {
                ensureInfosIsMutable();
                infos_.add(m);
              } else {
                infosBuilder_.addMessage(m);
              }
              break;
            } // case 18
            case 26: {
              input.readMessage(
                  internalGetSelfInfoFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.WarSeasonAutoMoveCityInfo> infos_ =
      java.util.Collections.emptyList();
    private void ensureInfosIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        infos_ = new java.util.ArrayList<xddq.pb.WarSeasonAutoMoveCityInfo>(infos_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.WarSeasonAutoMoveCityInfo, xddq.pb.WarSeasonAutoMoveCityInfo.Builder, xddq.pb.WarSeasonAutoMoveCityInfoOrBuilder> infosBuilder_;

    /**
     * <code>repeated .xddq.pb.WarSeasonAutoMoveCityInfo infos = 2;</code>
     */
    public java.util.List<xddq.pb.WarSeasonAutoMoveCityInfo> getInfosList() {
      if (infosBuilder_ == null) {
        return java.util.Collections.unmodifiableList(infos_);
      } else {
        return infosBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonAutoMoveCityInfo infos = 2;</code>
     */
    public int getInfosCount() {
      if (infosBuilder_ == null) {
        return infos_.size();
      } else {
        return infosBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonAutoMoveCityInfo infos = 2;</code>
     */
    public xddq.pb.WarSeasonAutoMoveCityInfo getInfos(int index) {
      if (infosBuilder_ == null) {
        return infos_.get(index);
      } else {
        return infosBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonAutoMoveCityInfo infos = 2;</code>
     */
    public Builder setInfos(
        int index, xddq.pb.WarSeasonAutoMoveCityInfo value) {
      if (infosBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureInfosIsMutable();
        infos_.set(index, value);
        onChanged();
      } else {
        infosBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonAutoMoveCityInfo infos = 2;</code>
     */
    public Builder setInfos(
        int index, xddq.pb.WarSeasonAutoMoveCityInfo.Builder builderForValue) {
      if (infosBuilder_ == null) {
        ensureInfosIsMutable();
        infos_.set(index, builderForValue.build());
        onChanged();
      } else {
        infosBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonAutoMoveCityInfo infos = 2;</code>
     */
    public Builder addInfos(xddq.pb.WarSeasonAutoMoveCityInfo value) {
      if (infosBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureInfosIsMutable();
        infos_.add(value);
        onChanged();
      } else {
        infosBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonAutoMoveCityInfo infos = 2;</code>
     */
    public Builder addInfos(
        int index, xddq.pb.WarSeasonAutoMoveCityInfo value) {
      if (infosBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureInfosIsMutable();
        infos_.add(index, value);
        onChanged();
      } else {
        infosBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonAutoMoveCityInfo infos = 2;</code>
     */
    public Builder addInfos(
        xddq.pb.WarSeasonAutoMoveCityInfo.Builder builderForValue) {
      if (infosBuilder_ == null) {
        ensureInfosIsMutable();
        infos_.add(builderForValue.build());
        onChanged();
      } else {
        infosBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonAutoMoveCityInfo infos = 2;</code>
     */
    public Builder addInfos(
        int index, xddq.pb.WarSeasonAutoMoveCityInfo.Builder builderForValue) {
      if (infosBuilder_ == null) {
        ensureInfosIsMutable();
        infos_.add(index, builderForValue.build());
        onChanged();
      } else {
        infosBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonAutoMoveCityInfo infos = 2;</code>
     */
    public Builder addAllInfos(
        java.lang.Iterable<? extends xddq.pb.WarSeasonAutoMoveCityInfo> values) {
      if (infosBuilder_ == null) {
        ensureInfosIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, infos_);
        onChanged();
      } else {
        infosBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonAutoMoveCityInfo infos = 2;</code>
     */
    public Builder clearInfos() {
      if (infosBuilder_ == null) {
        infos_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        infosBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonAutoMoveCityInfo infos = 2;</code>
     */
    public Builder removeInfos(int index) {
      if (infosBuilder_ == null) {
        ensureInfosIsMutable();
        infos_.remove(index);
        onChanged();
      } else {
        infosBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonAutoMoveCityInfo infos = 2;</code>
     */
    public xddq.pb.WarSeasonAutoMoveCityInfo.Builder getInfosBuilder(
        int index) {
      return internalGetInfosFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonAutoMoveCityInfo infos = 2;</code>
     */
    public xddq.pb.WarSeasonAutoMoveCityInfoOrBuilder getInfosOrBuilder(
        int index) {
      if (infosBuilder_ == null) {
        return infos_.get(index);  } else {
        return infosBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonAutoMoveCityInfo infos = 2;</code>
     */
    public java.util.List<? extends xddq.pb.WarSeasonAutoMoveCityInfoOrBuilder> 
         getInfosOrBuilderList() {
      if (infosBuilder_ != null) {
        return infosBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(infos_);
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonAutoMoveCityInfo infos = 2;</code>
     */
    public xddq.pb.WarSeasonAutoMoveCityInfo.Builder addInfosBuilder() {
      return internalGetInfosFieldBuilder().addBuilder(
          xddq.pb.WarSeasonAutoMoveCityInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonAutoMoveCityInfo infos = 2;</code>
     */
    public xddq.pb.WarSeasonAutoMoveCityInfo.Builder addInfosBuilder(
        int index) {
      return internalGetInfosFieldBuilder().addBuilder(
          index, xddq.pb.WarSeasonAutoMoveCityInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonAutoMoveCityInfo infos = 2;</code>
     */
    public java.util.List<xddq.pb.WarSeasonAutoMoveCityInfo.Builder> 
         getInfosBuilderList() {
      return internalGetInfosFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.WarSeasonAutoMoveCityInfo, xddq.pb.WarSeasonAutoMoveCityInfo.Builder, xddq.pb.WarSeasonAutoMoveCityInfoOrBuilder> 
        internalGetInfosFieldBuilder() {
      if (infosBuilder_ == null) {
        infosBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.WarSeasonAutoMoveCityInfo, xddq.pb.WarSeasonAutoMoveCityInfo.Builder, xddq.pb.WarSeasonAutoMoveCityInfoOrBuilder>(
                infos_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        infos_ = null;
      }
      return infosBuilder_;
    }

    private xddq.pb.WarSeasonAutoMoveCityInfo selfInfo_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.WarSeasonAutoMoveCityInfo, xddq.pb.WarSeasonAutoMoveCityInfo.Builder, xddq.pb.WarSeasonAutoMoveCityInfoOrBuilder> selfInfoBuilder_;
    /**
     * <code>optional .xddq.pb.WarSeasonAutoMoveCityInfo selfInfo = 3;</code>
     * @return Whether the selfInfo field is set.
     */
    public boolean hasSelfInfo() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional .xddq.pb.WarSeasonAutoMoveCityInfo selfInfo = 3;</code>
     * @return The selfInfo.
     */
    public xddq.pb.WarSeasonAutoMoveCityInfo getSelfInfo() {
      if (selfInfoBuilder_ == null) {
        return selfInfo_ == null ? xddq.pb.WarSeasonAutoMoveCityInfo.getDefaultInstance() : selfInfo_;
      } else {
        return selfInfoBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.WarSeasonAutoMoveCityInfo selfInfo = 3;</code>
     */
    public Builder setSelfInfo(xddq.pb.WarSeasonAutoMoveCityInfo value) {
      if (selfInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        selfInfo_ = value;
      } else {
        selfInfoBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.WarSeasonAutoMoveCityInfo selfInfo = 3;</code>
     */
    public Builder setSelfInfo(
        xddq.pb.WarSeasonAutoMoveCityInfo.Builder builderForValue) {
      if (selfInfoBuilder_ == null) {
        selfInfo_ = builderForValue.build();
      } else {
        selfInfoBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.WarSeasonAutoMoveCityInfo selfInfo = 3;</code>
     */
    public Builder mergeSelfInfo(xddq.pb.WarSeasonAutoMoveCityInfo value) {
      if (selfInfoBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0) &&
          selfInfo_ != null &&
          selfInfo_ != xddq.pb.WarSeasonAutoMoveCityInfo.getDefaultInstance()) {
          getSelfInfoBuilder().mergeFrom(value);
        } else {
          selfInfo_ = value;
        }
      } else {
        selfInfoBuilder_.mergeFrom(value);
      }
      if (selfInfo_ != null) {
        bitField0_ |= 0x00000004;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.WarSeasonAutoMoveCityInfo selfInfo = 3;</code>
     */
    public Builder clearSelfInfo() {
      bitField0_ = (bitField0_ & ~0x00000004);
      selfInfo_ = null;
      if (selfInfoBuilder_ != null) {
        selfInfoBuilder_.dispose();
        selfInfoBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.WarSeasonAutoMoveCityInfo selfInfo = 3;</code>
     */
    public xddq.pb.WarSeasonAutoMoveCityInfo.Builder getSelfInfoBuilder() {
      bitField0_ |= 0x00000004;
      onChanged();
      return internalGetSelfInfoFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.WarSeasonAutoMoveCityInfo selfInfo = 3;</code>
     */
    public xddq.pb.WarSeasonAutoMoveCityInfoOrBuilder getSelfInfoOrBuilder() {
      if (selfInfoBuilder_ != null) {
        return selfInfoBuilder_.getMessageOrBuilder();
      } else {
        return selfInfo_ == null ?
            xddq.pb.WarSeasonAutoMoveCityInfo.getDefaultInstance() : selfInfo_;
      }
    }
    /**
     * <code>optional .xddq.pb.WarSeasonAutoMoveCityInfo selfInfo = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.WarSeasonAutoMoveCityInfo, xddq.pb.WarSeasonAutoMoveCityInfo.Builder, xddq.pb.WarSeasonAutoMoveCityInfoOrBuilder> 
        internalGetSelfInfoFieldBuilder() {
      if (selfInfoBuilder_ == null) {
        selfInfoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.WarSeasonAutoMoveCityInfo, xddq.pb.WarSeasonAutoMoveCityInfo.Builder, xddq.pb.WarSeasonAutoMoveCityInfoOrBuilder>(
                getSelfInfo(),
                getParentForChildren(),
                isClean());
        selfInfo_ = null;
      }
      return selfInfoBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.WarSeasonAutoMoveCityInfoRespMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.WarSeasonAutoMoveCityInfoRespMsg)
  private static final xddq.pb.WarSeasonAutoMoveCityInfoRespMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.WarSeasonAutoMoveCityInfoRespMsg();
  }

  public static xddq.pb.WarSeasonAutoMoveCityInfoRespMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<WarSeasonAutoMoveCityInfoRespMsg>
      PARSER = new com.google.protobuf.AbstractParser<WarSeasonAutoMoveCityInfoRespMsg>() {
    @java.lang.Override
    public WarSeasonAutoMoveCityInfoRespMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<WarSeasonAutoMoveCityInfoRespMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<WarSeasonAutoMoveCityInfoRespMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.WarSeasonAutoMoveCityInfoRespMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

