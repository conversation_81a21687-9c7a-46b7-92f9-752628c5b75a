// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.GatherEnergyEnterNewResp}
 */
public final class GatherEnergyEnterNewResp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.GatherEnergyEnterNewResp)
    GatherEnergyEnterNewRespOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      GatherEnergyEnterNewResp.class.getName());
  }
  // Use GatherEnergyEnterNewResp.newBuilder() to construct.
  private GatherEnergyEnterNewResp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private GatherEnergyEnterNewResp() {
    reward_ = java.util.Collections.emptyList();
    serverId_ = emptyLongList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_GatherEnergyEnterNewResp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_GatherEnergyEnterNewResp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.GatherEnergyEnterNewResp.class, xddq.pb.GatherEnergyEnterNewResp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int REWARD_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.GatherEnergyRewardMsg> reward_;
  /**
   * <code>repeated .xddq.pb.GatherEnergyRewardMsg reward = 2;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.GatherEnergyRewardMsg> getRewardList() {
    return reward_;
  }
  /**
   * <code>repeated .xddq.pb.GatherEnergyRewardMsg reward = 2;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.GatherEnergyRewardMsgOrBuilder> 
      getRewardOrBuilderList() {
    return reward_;
  }
  /**
   * <code>repeated .xddq.pb.GatherEnergyRewardMsg reward = 2;</code>
   */
  @java.lang.Override
  public int getRewardCount() {
    return reward_.size();
  }
  /**
   * <code>repeated .xddq.pb.GatherEnergyRewardMsg reward = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.GatherEnergyRewardMsg getReward(int index) {
    return reward_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.GatherEnergyRewardMsg reward = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.GatherEnergyRewardMsgOrBuilder getRewardOrBuilder(
      int index) {
    return reward_.get(index);
  }

  public static final int ENERGYLEVEL_FIELD_NUMBER = 3;
  private int energyLevel_ = 0;
  /**
   * <code>optional int32 energyLevel = 3;</code>
   * @return Whether the energyLevel field is set.
   */
  @java.lang.Override
  public boolean hasEnergyLevel() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int32 energyLevel = 3;</code>
   * @return The energyLevel.
   */
  @java.lang.Override
  public int getEnergyLevel() {
    return energyLevel_;
  }

  public static final int SERVERID_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private com.google.protobuf.Internal.LongList serverId_ =
      emptyLongList();
  /**
   * <code>repeated int64 serverId = 4;</code>
   * @return A list containing the serverId.
   */
  @java.lang.Override
  public java.util.List<java.lang.Long>
      getServerIdList() {
    return serverId_;
  }
  /**
   * <code>repeated int64 serverId = 4;</code>
   * @return The count of serverId.
   */
  public int getServerIdCount() {
    return serverId_.size();
  }
  /**
   * <code>repeated int64 serverId = 4;</code>
   * @param index The index of the element to return.
   * @return The serverId at the given index.
   */
  public long getServerId(int index) {
    return serverId_.getLong(index);
  }

  public static final int GATHERENERGY_FIELD_NUMBER = 5;
  private xddq.pb.GatherEnergyMsg gatherEnergy_;
  /**
   * <code>optional .xddq.pb.GatherEnergyMsg gatherEnergy = 5;</code>
   * @return Whether the gatherEnergy field is set.
   */
  @java.lang.Override
  public boolean hasGatherEnergy() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional .xddq.pb.GatherEnergyMsg gatherEnergy = 5;</code>
   * @return The gatherEnergy.
   */
  @java.lang.Override
  public xddq.pb.GatherEnergyMsg getGatherEnergy() {
    return gatherEnergy_ == null ? xddq.pb.GatherEnergyMsg.getDefaultInstance() : gatherEnergy_;
  }
  /**
   * <code>optional .xddq.pb.GatherEnergyMsg gatherEnergy = 5;</code>
   */
  @java.lang.Override
  public xddq.pb.GatherEnergyMsgOrBuilder getGatherEnergyOrBuilder() {
    return gatherEnergy_ == null ? xddq.pb.GatherEnergyMsg.getDefaultInstance() : gatherEnergy_;
  }

  public static final int TOTALNUM_FIELD_NUMBER = 6;
  private int totalNum_ = 0;
  /**
   * <code>optional int32 totalNum = 6;</code>
   * @return Whether the totalNum field is set.
   */
  @java.lang.Override
  public boolean hasTotalNum() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional int32 totalNum = 6;</code>
   * @return The totalNum.
   */
  @java.lang.Override
  public int getTotalNum() {
    return totalNum_;
  }

  public static final int TOTALATTENDNUM_FIELD_NUMBER = 7;
  private int totalAttendNum_ = 0;
  /**
   * <code>optional int32 totalAttendNum = 7;</code>
   * @return Whether the totalAttendNum field is set.
   */
  @java.lang.Override
  public boolean hasTotalAttendNum() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>optional int32 totalAttendNum = 7;</code>
   * @return The totalAttendNum.
   */
  @java.lang.Override
  public int getTotalAttendNum() {
    return totalAttendNum_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    for (int i = 0; i < reward_.size(); i++) {
      output.writeMessage(2, reward_.get(i));
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(3, energyLevel_);
    }
    for (int i = 0; i < serverId_.size(); i++) {
      output.writeInt64(4, serverId_.getLong(i));
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeMessage(5, getGatherEnergy());
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt32(6, totalNum_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      output.writeInt32(7, totalAttendNum_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    for (int i = 0; i < reward_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, reward_.get(i));
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(3, energyLevel_);
    }
    {
      int dataSize = 0;
      for (int i = 0; i < serverId_.size(); i++) {
        dataSize += com.google.protobuf.CodedOutputStream
          .computeInt64SizeNoTag(serverId_.getLong(i));
      }
      size += dataSize;
      size += 1 * getServerIdList().size();
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(5, getGatherEnergy());
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(6, totalNum_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(7, totalAttendNum_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.GatherEnergyEnterNewResp)) {
      return super.equals(obj);
    }
    xddq.pb.GatherEnergyEnterNewResp other = (xddq.pb.GatherEnergyEnterNewResp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (!getRewardList()
        .equals(other.getRewardList())) return false;
    if (hasEnergyLevel() != other.hasEnergyLevel()) return false;
    if (hasEnergyLevel()) {
      if (getEnergyLevel()
          != other.getEnergyLevel()) return false;
    }
    if (!getServerIdList()
        .equals(other.getServerIdList())) return false;
    if (hasGatherEnergy() != other.hasGatherEnergy()) return false;
    if (hasGatherEnergy()) {
      if (!getGatherEnergy()
          .equals(other.getGatherEnergy())) return false;
    }
    if (hasTotalNum() != other.hasTotalNum()) return false;
    if (hasTotalNum()) {
      if (getTotalNum()
          != other.getTotalNum()) return false;
    }
    if (hasTotalAttendNum() != other.hasTotalAttendNum()) return false;
    if (hasTotalAttendNum()) {
      if (getTotalAttendNum()
          != other.getTotalAttendNum()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (getRewardCount() > 0) {
      hash = (37 * hash) + REWARD_FIELD_NUMBER;
      hash = (53 * hash) + getRewardList().hashCode();
    }
    if (hasEnergyLevel()) {
      hash = (37 * hash) + ENERGYLEVEL_FIELD_NUMBER;
      hash = (53 * hash) + getEnergyLevel();
    }
    if (getServerIdCount() > 0) {
      hash = (37 * hash) + SERVERID_FIELD_NUMBER;
      hash = (53 * hash) + getServerIdList().hashCode();
    }
    if (hasGatherEnergy()) {
      hash = (37 * hash) + GATHERENERGY_FIELD_NUMBER;
      hash = (53 * hash) + getGatherEnergy().hashCode();
    }
    if (hasTotalNum()) {
      hash = (37 * hash) + TOTALNUM_FIELD_NUMBER;
      hash = (53 * hash) + getTotalNum();
    }
    if (hasTotalAttendNum()) {
      hash = (37 * hash) + TOTALATTENDNUM_FIELD_NUMBER;
      hash = (53 * hash) + getTotalAttendNum();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.GatherEnergyEnterNewResp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GatherEnergyEnterNewResp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GatherEnergyEnterNewResp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GatherEnergyEnterNewResp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GatherEnergyEnterNewResp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GatherEnergyEnterNewResp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GatherEnergyEnterNewResp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.GatherEnergyEnterNewResp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.GatherEnergyEnterNewResp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.GatherEnergyEnterNewResp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.GatherEnergyEnterNewResp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.GatherEnergyEnterNewResp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.GatherEnergyEnterNewResp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.GatherEnergyEnterNewResp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.GatherEnergyEnterNewResp)
      xddq.pb.GatherEnergyEnterNewRespOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GatherEnergyEnterNewResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GatherEnergyEnterNewResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.GatherEnergyEnterNewResp.class, xddq.pb.GatherEnergyEnterNewResp.Builder.class);
    }

    // Construct using xddq.pb.GatherEnergyEnterNewResp.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetRewardFieldBuilder();
        internalGetGatherEnergyFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      if (rewardBuilder_ == null) {
        reward_ = java.util.Collections.emptyList();
      } else {
        reward_ = null;
        rewardBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000002);
      energyLevel_ = 0;
      serverId_ = emptyLongList();
      gatherEnergy_ = null;
      if (gatherEnergyBuilder_ != null) {
        gatherEnergyBuilder_.dispose();
        gatherEnergyBuilder_ = null;
      }
      totalNum_ = 0;
      totalAttendNum_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GatherEnergyEnterNewResp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.GatherEnergyEnterNewResp getDefaultInstanceForType() {
      return xddq.pb.GatherEnergyEnterNewResp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.GatherEnergyEnterNewResp build() {
      xddq.pb.GatherEnergyEnterNewResp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.GatherEnergyEnterNewResp buildPartial() {
      xddq.pb.GatherEnergyEnterNewResp result = new xddq.pb.GatherEnergyEnterNewResp(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.GatherEnergyEnterNewResp result) {
      if (rewardBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          reward_ = java.util.Collections.unmodifiableList(reward_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.reward_ = reward_;
      } else {
        result.reward_ = rewardBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.GatherEnergyEnterNewResp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.energyLevel_ = energyLevel_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        serverId_.makeImmutable();
        result.serverId_ = serverId_;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.gatherEnergy_ = gatherEnergyBuilder_ == null
            ? gatherEnergy_
            : gatherEnergyBuilder_.build();
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.totalNum_ = totalNum_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.totalAttendNum_ = totalAttendNum_;
        to_bitField0_ |= 0x00000010;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.GatherEnergyEnterNewResp) {
        return mergeFrom((xddq.pb.GatherEnergyEnterNewResp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.GatherEnergyEnterNewResp other) {
      if (other == xddq.pb.GatherEnergyEnterNewResp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (rewardBuilder_ == null) {
        if (!other.reward_.isEmpty()) {
          if (reward_.isEmpty()) {
            reward_ = other.reward_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureRewardIsMutable();
            reward_.addAll(other.reward_);
          }
          onChanged();
        }
      } else {
        if (!other.reward_.isEmpty()) {
          if (rewardBuilder_.isEmpty()) {
            rewardBuilder_.dispose();
            rewardBuilder_ = null;
            reward_ = other.reward_;
            bitField0_ = (bitField0_ & ~0x00000002);
            rewardBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetRewardFieldBuilder() : null;
          } else {
            rewardBuilder_.addAllMessages(other.reward_);
          }
        }
      }
      if (other.hasEnergyLevel()) {
        setEnergyLevel(other.getEnergyLevel());
      }
      if (!other.serverId_.isEmpty()) {
        if (serverId_.isEmpty()) {
          serverId_ = other.serverId_;
          serverId_.makeImmutable();
          bitField0_ |= 0x00000008;
        } else {
          ensureServerIdIsMutable();
          serverId_.addAll(other.serverId_);
        }
        onChanged();
      }
      if (other.hasGatherEnergy()) {
        mergeGatherEnergy(other.getGatherEnergy());
      }
      if (other.hasTotalNum()) {
        setTotalNum(other.getTotalNum());
      }
      if (other.hasTotalAttendNum()) {
        setTotalAttendNum(other.getTotalAttendNum());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              xddq.pb.GatherEnergyRewardMsg m =
                  input.readMessage(
                      xddq.pb.GatherEnergyRewardMsg.parser(),
                      extensionRegistry);
              if (rewardBuilder_ == null) {
                ensureRewardIsMutable();
                reward_.add(m);
              } else {
                rewardBuilder_.addMessage(m);
              }
              break;
            } // case 18
            case 24: {
              energyLevel_ = input.readInt32();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 32: {
              long v = input.readInt64();
              ensureServerIdIsMutable();
              serverId_.addLong(v);
              break;
            } // case 32
            case 34: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              ensureServerIdIsMutable();
              while (input.getBytesUntilLimit() > 0) {
                serverId_.addLong(input.readInt64());
              }
              input.popLimit(limit);
              break;
            } // case 34
            case 42: {
              input.readMessage(
                  internalGetGatherEnergyFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000010;
              break;
            } // case 42
            case 48: {
              totalNum_ = input.readInt32();
              bitField0_ |= 0x00000020;
              break;
            } // case 48
            case 56: {
              totalAttendNum_ = input.readInt32();
              bitField0_ |= 0x00000040;
              break;
            } // case 56
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.GatherEnergyRewardMsg> reward_ =
      java.util.Collections.emptyList();
    private void ensureRewardIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        reward_ = new java.util.ArrayList<xddq.pb.GatherEnergyRewardMsg>(reward_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.GatherEnergyRewardMsg, xddq.pb.GatherEnergyRewardMsg.Builder, xddq.pb.GatherEnergyRewardMsgOrBuilder> rewardBuilder_;

    /**
     * <code>repeated .xddq.pb.GatherEnergyRewardMsg reward = 2;</code>
     */
    public java.util.List<xddq.pb.GatherEnergyRewardMsg> getRewardList() {
      if (rewardBuilder_ == null) {
        return java.util.Collections.unmodifiableList(reward_);
      } else {
        return rewardBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.GatherEnergyRewardMsg reward = 2;</code>
     */
    public int getRewardCount() {
      if (rewardBuilder_ == null) {
        return reward_.size();
      } else {
        return rewardBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.GatherEnergyRewardMsg reward = 2;</code>
     */
    public xddq.pb.GatherEnergyRewardMsg getReward(int index) {
      if (rewardBuilder_ == null) {
        return reward_.get(index);
      } else {
        return rewardBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.GatherEnergyRewardMsg reward = 2;</code>
     */
    public Builder setReward(
        int index, xddq.pb.GatherEnergyRewardMsg value) {
      if (rewardBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRewardIsMutable();
        reward_.set(index, value);
        onChanged();
      } else {
        rewardBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GatherEnergyRewardMsg reward = 2;</code>
     */
    public Builder setReward(
        int index, xddq.pb.GatherEnergyRewardMsg.Builder builderForValue) {
      if (rewardBuilder_ == null) {
        ensureRewardIsMutable();
        reward_.set(index, builderForValue.build());
        onChanged();
      } else {
        rewardBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GatherEnergyRewardMsg reward = 2;</code>
     */
    public Builder addReward(xddq.pb.GatherEnergyRewardMsg value) {
      if (rewardBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRewardIsMutable();
        reward_.add(value);
        onChanged();
      } else {
        rewardBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GatherEnergyRewardMsg reward = 2;</code>
     */
    public Builder addReward(
        int index, xddq.pb.GatherEnergyRewardMsg value) {
      if (rewardBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRewardIsMutable();
        reward_.add(index, value);
        onChanged();
      } else {
        rewardBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GatherEnergyRewardMsg reward = 2;</code>
     */
    public Builder addReward(
        xddq.pb.GatherEnergyRewardMsg.Builder builderForValue) {
      if (rewardBuilder_ == null) {
        ensureRewardIsMutable();
        reward_.add(builderForValue.build());
        onChanged();
      } else {
        rewardBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GatherEnergyRewardMsg reward = 2;</code>
     */
    public Builder addReward(
        int index, xddq.pb.GatherEnergyRewardMsg.Builder builderForValue) {
      if (rewardBuilder_ == null) {
        ensureRewardIsMutable();
        reward_.add(index, builderForValue.build());
        onChanged();
      } else {
        rewardBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GatherEnergyRewardMsg reward = 2;</code>
     */
    public Builder addAllReward(
        java.lang.Iterable<? extends xddq.pb.GatherEnergyRewardMsg> values) {
      if (rewardBuilder_ == null) {
        ensureRewardIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, reward_);
        onChanged();
      } else {
        rewardBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GatherEnergyRewardMsg reward = 2;</code>
     */
    public Builder clearReward() {
      if (rewardBuilder_ == null) {
        reward_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        rewardBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GatherEnergyRewardMsg reward = 2;</code>
     */
    public Builder removeReward(int index) {
      if (rewardBuilder_ == null) {
        ensureRewardIsMutable();
        reward_.remove(index);
        onChanged();
      } else {
        rewardBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GatherEnergyRewardMsg reward = 2;</code>
     */
    public xddq.pb.GatherEnergyRewardMsg.Builder getRewardBuilder(
        int index) {
      return internalGetRewardFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.GatherEnergyRewardMsg reward = 2;</code>
     */
    public xddq.pb.GatherEnergyRewardMsgOrBuilder getRewardOrBuilder(
        int index) {
      if (rewardBuilder_ == null) {
        return reward_.get(index);  } else {
        return rewardBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.GatherEnergyRewardMsg reward = 2;</code>
     */
    public java.util.List<? extends xddq.pb.GatherEnergyRewardMsgOrBuilder> 
         getRewardOrBuilderList() {
      if (rewardBuilder_ != null) {
        return rewardBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(reward_);
      }
    }
    /**
     * <code>repeated .xddq.pb.GatherEnergyRewardMsg reward = 2;</code>
     */
    public xddq.pb.GatherEnergyRewardMsg.Builder addRewardBuilder() {
      return internalGetRewardFieldBuilder().addBuilder(
          xddq.pb.GatherEnergyRewardMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.GatherEnergyRewardMsg reward = 2;</code>
     */
    public xddq.pb.GatherEnergyRewardMsg.Builder addRewardBuilder(
        int index) {
      return internalGetRewardFieldBuilder().addBuilder(
          index, xddq.pb.GatherEnergyRewardMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.GatherEnergyRewardMsg reward = 2;</code>
     */
    public java.util.List<xddq.pb.GatherEnergyRewardMsg.Builder> 
         getRewardBuilderList() {
      return internalGetRewardFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.GatherEnergyRewardMsg, xddq.pb.GatherEnergyRewardMsg.Builder, xddq.pb.GatherEnergyRewardMsgOrBuilder> 
        internalGetRewardFieldBuilder() {
      if (rewardBuilder_ == null) {
        rewardBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.GatherEnergyRewardMsg, xddq.pb.GatherEnergyRewardMsg.Builder, xddq.pb.GatherEnergyRewardMsgOrBuilder>(
                reward_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        reward_ = null;
      }
      return rewardBuilder_;
    }

    private int energyLevel_ ;
    /**
     * <code>optional int32 energyLevel = 3;</code>
     * @return Whether the energyLevel field is set.
     */
    @java.lang.Override
    public boolean hasEnergyLevel() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int32 energyLevel = 3;</code>
     * @return The energyLevel.
     */
    @java.lang.Override
    public int getEnergyLevel() {
      return energyLevel_;
    }
    /**
     * <code>optional int32 energyLevel = 3;</code>
     * @param value The energyLevel to set.
     * @return This builder for chaining.
     */
    public Builder setEnergyLevel(int value) {

      energyLevel_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 energyLevel = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearEnergyLevel() {
      bitField0_ = (bitField0_ & ~0x00000004);
      energyLevel_ = 0;
      onChanged();
      return this;
    }

    private com.google.protobuf.Internal.LongList serverId_ = emptyLongList();
    private void ensureServerIdIsMutable() {
      if (!serverId_.isModifiable()) {
        serverId_ = makeMutableCopy(serverId_);
      }
      bitField0_ |= 0x00000008;
    }
    /**
     * <code>repeated int64 serverId = 4;</code>
     * @return A list containing the serverId.
     */
    public java.util.List<java.lang.Long>
        getServerIdList() {
      serverId_.makeImmutable();
      return serverId_;
    }
    /**
     * <code>repeated int64 serverId = 4;</code>
     * @return The count of serverId.
     */
    public int getServerIdCount() {
      return serverId_.size();
    }
    /**
     * <code>repeated int64 serverId = 4;</code>
     * @param index The index of the element to return.
     * @return The serverId at the given index.
     */
    public long getServerId(int index) {
      return serverId_.getLong(index);
    }
    /**
     * <code>repeated int64 serverId = 4;</code>
     * @param index The index to set the value at.
     * @param value The serverId to set.
     * @return This builder for chaining.
     */
    public Builder setServerId(
        int index, long value) {

      ensureServerIdIsMutable();
      serverId_.setLong(index, value);
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int64 serverId = 4;</code>
     * @param value The serverId to add.
     * @return This builder for chaining.
     */
    public Builder addServerId(long value) {

      ensureServerIdIsMutable();
      serverId_.addLong(value);
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int64 serverId = 4;</code>
     * @param values The serverId to add.
     * @return This builder for chaining.
     */
    public Builder addAllServerId(
        java.lang.Iterable<? extends java.lang.Long> values) {
      ensureServerIdIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, serverId_);
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int64 serverId = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearServerId() {
      serverId_ = emptyLongList();
      bitField0_ = (bitField0_ & ~0x00000008);
      onChanged();
      return this;
    }

    private xddq.pb.GatherEnergyMsg gatherEnergy_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.GatherEnergyMsg, xddq.pb.GatherEnergyMsg.Builder, xddq.pb.GatherEnergyMsgOrBuilder> gatherEnergyBuilder_;
    /**
     * <code>optional .xddq.pb.GatherEnergyMsg gatherEnergy = 5;</code>
     * @return Whether the gatherEnergy field is set.
     */
    public boolean hasGatherEnergy() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional .xddq.pb.GatherEnergyMsg gatherEnergy = 5;</code>
     * @return The gatherEnergy.
     */
    public xddq.pb.GatherEnergyMsg getGatherEnergy() {
      if (gatherEnergyBuilder_ == null) {
        return gatherEnergy_ == null ? xddq.pb.GatherEnergyMsg.getDefaultInstance() : gatherEnergy_;
      } else {
        return gatherEnergyBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.GatherEnergyMsg gatherEnergy = 5;</code>
     */
    public Builder setGatherEnergy(xddq.pb.GatherEnergyMsg value) {
      if (gatherEnergyBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        gatherEnergy_ = value;
      } else {
        gatherEnergyBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.GatherEnergyMsg gatherEnergy = 5;</code>
     */
    public Builder setGatherEnergy(
        xddq.pb.GatherEnergyMsg.Builder builderForValue) {
      if (gatherEnergyBuilder_ == null) {
        gatherEnergy_ = builderForValue.build();
      } else {
        gatherEnergyBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.GatherEnergyMsg gatherEnergy = 5;</code>
     */
    public Builder mergeGatherEnergy(xddq.pb.GatherEnergyMsg value) {
      if (gatherEnergyBuilder_ == null) {
        if (((bitField0_ & 0x00000010) != 0) &&
          gatherEnergy_ != null &&
          gatherEnergy_ != xddq.pb.GatherEnergyMsg.getDefaultInstance()) {
          getGatherEnergyBuilder().mergeFrom(value);
        } else {
          gatherEnergy_ = value;
        }
      } else {
        gatherEnergyBuilder_.mergeFrom(value);
      }
      if (gatherEnergy_ != null) {
        bitField0_ |= 0x00000010;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.GatherEnergyMsg gatherEnergy = 5;</code>
     */
    public Builder clearGatherEnergy() {
      bitField0_ = (bitField0_ & ~0x00000010);
      gatherEnergy_ = null;
      if (gatherEnergyBuilder_ != null) {
        gatherEnergyBuilder_.dispose();
        gatherEnergyBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.GatherEnergyMsg gatherEnergy = 5;</code>
     */
    public xddq.pb.GatherEnergyMsg.Builder getGatherEnergyBuilder() {
      bitField0_ |= 0x00000010;
      onChanged();
      return internalGetGatherEnergyFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.GatherEnergyMsg gatherEnergy = 5;</code>
     */
    public xddq.pb.GatherEnergyMsgOrBuilder getGatherEnergyOrBuilder() {
      if (gatherEnergyBuilder_ != null) {
        return gatherEnergyBuilder_.getMessageOrBuilder();
      } else {
        return gatherEnergy_ == null ?
            xddq.pb.GatherEnergyMsg.getDefaultInstance() : gatherEnergy_;
      }
    }
    /**
     * <code>optional .xddq.pb.GatherEnergyMsg gatherEnergy = 5;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.GatherEnergyMsg, xddq.pb.GatherEnergyMsg.Builder, xddq.pb.GatherEnergyMsgOrBuilder> 
        internalGetGatherEnergyFieldBuilder() {
      if (gatherEnergyBuilder_ == null) {
        gatherEnergyBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.GatherEnergyMsg, xddq.pb.GatherEnergyMsg.Builder, xddq.pb.GatherEnergyMsgOrBuilder>(
                getGatherEnergy(),
                getParentForChildren(),
                isClean());
        gatherEnergy_ = null;
      }
      return gatherEnergyBuilder_;
    }

    private int totalNum_ ;
    /**
     * <code>optional int32 totalNum = 6;</code>
     * @return Whether the totalNum field is set.
     */
    @java.lang.Override
    public boolean hasTotalNum() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional int32 totalNum = 6;</code>
     * @return The totalNum.
     */
    @java.lang.Override
    public int getTotalNum() {
      return totalNum_;
    }
    /**
     * <code>optional int32 totalNum = 6;</code>
     * @param value The totalNum to set.
     * @return This builder for chaining.
     */
    public Builder setTotalNum(int value) {

      totalNum_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 totalNum = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearTotalNum() {
      bitField0_ = (bitField0_ & ~0x00000020);
      totalNum_ = 0;
      onChanged();
      return this;
    }

    private int totalAttendNum_ ;
    /**
     * <code>optional int32 totalAttendNum = 7;</code>
     * @return Whether the totalAttendNum field is set.
     */
    @java.lang.Override
    public boolean hasTotalAttendNum() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <code>optional int32 totalAttendNum = 7;</code>
     * @return The totalAttendNum.
     */
    @java.lang.Override
    public int getTotalAttendNum() {
      return totalAttendNum_;
    }
    /**
     * <code>optional int32 totalAttendNum = 7;</code>
     * @param value The totalAttendNum to set.
     * @return This builder for chaining.
     */
    public Builder setTotalAttendNum(int value) {

      totalAttendNum_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 totalAttendNum = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearTotalAttendNum() {
      bitField0_ = (bitField0_ & ~0x00000040);
      totalAttendNum_ = 0;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.GatherEnergyEnterNewResp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.GatherEnergyEnterNewResp)
  private static final xddq.pb.GatherEnergyEnterNewResp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.GatherEnergyEnterNewResp();
  }

  public static xddq.pb.GatherEnergyEnterNewResp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<GatherEnergyEnterNewResp>
      PARSER = new com.google.protobuf.AbstractParser<GatherEnergyEnterNewResp>() {
    @java.lang.Override
    public GatherEnergyEnterNewResp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<GatherEnergyEnterNewResp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<GatherEnergyEnterNewResp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.GatherEnergyEnterNewResp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

