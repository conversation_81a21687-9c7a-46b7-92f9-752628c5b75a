// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.PlayerFairyLandDataMsg}
 */
public final class PlayerFairyLandDataMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.PlayerFairyLandDataMsg)
    PlayerFairyLandDataMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      PlayerFairyLandDataMsg.class.getName());
  }
  // Use PlayerFairyLandDataMsg.newBuilder() to construct.
  private PlayerFairyLandDataMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private PlayerFairyLandDataMsg() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_PlayerFairyLandDataMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_PlayerFairyLandDataMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.PlayerFairyLandDataMsg.class, xddq.pb.PlayerFairyLandDataMsg.Builder.class);
  }

  private int bitField0_;
  public static final int SOARSTATUS_FIELD_NUMBER = 1;
  private int soarStatus_ = 0;
  /**
   * <code>required int32 soarStatus = 1;</code>
   * @return Whether the soarStatus field is set.
   */
  @java.lang.Override
  public boolean hasSoarStatus() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 soarStatus = 1;</code>
   * @return The soarStatus.
   */
  @java.lang.Override
  public int getSoarStatus() {
    return soarStatus_;
  }

  public static final int HEARTDEVILINFO_FIELD_NUMBER = 2;
  private xddq.pb.FairyLandHeartDevilInfo heartDevilInfo_;
  /**
   * <code>optional .xddq.pb.FairyLandHeartDevilInfo heartDevilInfo = 2;</code>
   * @return Whether the heartDevilInfo field is set.
   */
  @java.lang.Override
  public boolean hasHeartDevilInfo() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional .xddq.pb.FairyLandHeartDevilInfo heartDevilInfo = 2;</code>
   * @return The heartDevilInfo.
   */
  @java.lang.Override
  public xddq.pb.FairyLandHeartDevilInfo getHeartDevilInfo() {
    return heartDevilInfo_ == null ? xddq.pb.FairyLandHeartDevilInfo.getDefaultInstance() : heartDevilInfo_;
  }
  /**
   * <code>optional .xddq.pb.FairyLandHeartDevilInfo heartDevilInfo = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.FairyLandHeartDevilInfoOrBuilder getHeartDevilInfoOrBuilder() {
    return heartDevilInfo_ == null ? xddq.pb.FairyLandHeartDevilInfo.getDefaultInstance() : heartDevilInfo_;
  }

  public static final int BATTLEINFO_FIELD_NUMBER = 3;
  private xddq.pb.FairyLandBattleInfo battleInfo_;
  /**
   * <code>optional .xddq.pb.FairyLandBattleInfo battleInfo = 3;</code>
   * @return Whether the battleInfo field is set.
   */
  @java.lang.Override
  public boolean hasBattleInfo() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional .xddq.pb.FairyLandBattleInfo battleInfo = 3;</code>
   * @return The battleInfo.
   */
  @java.lang.Override
  public xddq.pb.FairyLandBattleInfo getBattleInfo() {
    return battleInfo_ == null ? xddq.pb.FairyLandBattleInfo.getDefaultInstance() : battleInfo_;
  }
  /**
   * <code>optional .xddq.pb.FairyLandBattleInfo battleInfo = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.FairyLandBattleInfoOrBuilder getBattleInfoOrBuilder() {
    return battleInfo_ == null ? xddq.pb.FairyLandBattleInfo.getDefaultInstance() : battleInfo_;
  }

  public static final int HELPTIMES_FIELD_NUMBER = 4;
  private int helpTimes_ = 0;
  /**
   * <code>optional int32 helpTimes = 4;</code>
   * @return Whether the helpTimes field is set.
   */
  @java.lang.Override
  public boolean hasHelpTimes() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional int32 helpTimes = 4;</code>
   * @return The helpTimes.
   */
  @java.lang.Override
  public int getHelpTimes() {
    return helpTimes_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasSoarStatus()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (hasHeartDevilInfo()) {
      if (!getHeartDevilInfo().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    if (hasBattleInfo()) {
      if (!getBattleInfo().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, soarStatus_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeMessage(2, getHeartDevilInfo());
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeMessage(3, getBattleInfo());
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt32(4, helpTimes_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, soarStatus_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getHeartDevilInfo());
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, getBattleInfo());
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(4, helpTimes_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.PlayerFairyLandDataMsg)) {
      return super.equals(obj);
    }
    xddq.pb.PlayerFairyLandDataMsg other = (xddq.pb.PlayerFairyLandDataMsg) obj;

    if (hasSoarStatus() != other.hasSoarStatus()) return false;
    if (hasSoarStatus()) {
      if (getSoarStatus()
          != other.getSoarStatus()) return false;
    }
    if (hasHeartDevilInfo() != other.hasHeartDevilInfo()) return false;
    if (hasHeartDevilInfo()) {
      if (!getHeartDevilInfo()
          .equals(other.getHeartDevilInfo())) return false;
    }
    if (hasBattleInfo() != other.hasBattleInfo()) return false;
    if (hasBattleInfo()) {
      if (!getBattleInfo()
          .equals(other.getBattleInfo())) return false;
    }
    if (hasHelpTimes() != other.hasHelpTimes()) return false;
    if (hasHelpTimes()) {
      if (getHelpTimes()
          != other.getHelpTimes()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasSoarStatus()) {
      hash = (37 * hash) + SOARSTATUS_FIELD_NUMBER;
      hash = (53 * hash) + getSoarStatus();
    }
    if (hasHeartDevilInfo()) {
      hash = (37 * hash) + HEARTDEVILINFO_FIELD_NUMBER;
      hash = (53 * hash) + getHeartDevilInfo().hashCode();
    }
    if (hasBattleInfo()) {
      hash = (37 * hash) + BATTLEINFO_FIELD_NUMBER;
      hash = (53 * hash) + getBattleInfo().hashCode();
    }
    if (hasHelpTimes()) {
      hash = (37 * hash) + HELPTIMES_FIELD_NUMBER;
      hash = (53 * hash) + getHelpTimes();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.PlayerFairyLandDataMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PlayerFairyLandDataMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PlayerFairyLandDataMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PlayerFairyLandDataMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PlayerFairyLandDataMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PlayerFairyLandDataMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PlayerFairyLandDataMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.PlayerFairyLandDataMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.PlayerFairyLandDataMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.PlayerFairyLandDataMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.PlayerFairyLandDataMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.PlayerFairyLandDataMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.PlayerFairyLandDataMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.PlayerFairyLandDataMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.PlayerFairyLandDataMsg)
      xddq.pb.PlayerFairyLandDataMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PlayerFairyLandDataMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PlayerFairyLandDataMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.PlayerFairyLandDataMsg.class, xddq.pb.PlayerFairyLandDataMsg.Builder.class);
    }

    // Construct using xddq.pb.PlayerFairyLandDataMsg.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetHeartDevilInfoFieldBuilder();
        internalGetBattleInfoFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      soarStatus_ = 0;
      heartDevilInfo_ = null;
      if (heartDevilInfoBuilder_ != null) {
        heartDevilInfoBuilder_.dispose();
        heartDevilInfoBuilder_ = null;
      }
      battleInfo_ = null;
      if (battleInfoBuilder_ != null) {
        battleInfoBuilder_.dispose();
        battleInfoBuilder_ = null;
      }
      helpTimes_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PlayerFairyLandDataMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.PlayerFairyLandDataMsg getDefaultInstanceForType() {
      return xddq.pb.PlayerFairyLandDataMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.PlayerFairyLandDataMsg build() {
      xddq.pb.PlayerFairyLandDataMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.PlayerFairyLandDataMsg buildPartial() {
      xddq.pb.PlayerFairyLandDataMsg result = new xddq.pb.PlayerFairyLandDataMsg(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.PlayerFairyLandDataMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.soarStatus_ = soarStatus_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.heartDevilInfo_ = heartDevilInfoBuilder_ == null
            ? heartDevilInfo_
            : heartDevilInfoBuilder_.build();
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.battleInfo_ = battleInfoBuilder_ == null
            ? battleInfo_
            : battleInfoBuilder_.build();
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.helpTimes_ = helpTimes_;
        to_bitField0_ |= 0x00000008;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.PlayerFairyLandDataMsg) {
        return mergeFrom((xddq.pb.PlayerFairyLandDataMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.PlayerFairyLandDataMsg other) {
      if (other == xddq.pb.PlayerFairyLandDataMsg.getDefaultInstance()) return this;
      if (other.hasSoarStatus()) {
        setSoarStatus(other.getSoarStatus());
      }
      if (other.hasHeartDevilInfo()) {
        mergeHeartDevilInfo(other.getHeartDevilInfo());
      }
      if (other.hasBattleInfo()) {
        mergeBattleInfo(other.getBattleInfo());
      }
      if (other.hasHelpTimes()) {
        setHelpTimes(other.getHelpTimes());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasSoarStatus()) {
        return false;
      }
      if (hasHeartDevilInfo()) {
        if (!getHeartDevilInfo().isInitialized()) {
          return false;
        }
      }
      if (hasBattleInfo()) {
        if (!getBattleInfo().isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              soarStatus_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              input.readMessage(
                  internalGetHeartDevilInfoFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 26: {
              input.readMessage(
                  internalGetBattleInfoFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 32: {
              helpTimes_ = input.readInt32();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int soarStatus_ ;
    /**
     * <code>required int32 soarStatus = 1;</code>
     * @return Whether the soarStatus field is set.
     */
    @java.lang.Override
    public boolean hasSoarStatus() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 soarStatus = 1;</code>
     * @return The soarStatus.
     */
    @java.lang.Override
    public int getSoarStatus() {
      return soarStatus_;
    }
    /**
     * <code>required int32 soarStatus = 1;</code>
     * @param value The soarStatus to set.
     * @return This builder for chaining.
     */
    public Builder setSoarStatus(int value) {

      soarStatus_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 soarStatus = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearSoarStatus() {
      bitField0_ = (bitField0_ & ~0x00000001);
      soarStatus_ = 0;
      onChanged();
      return this;
    }

    private xddq.pb.FairyLandHeartDevilInfo heartDevilInfo_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.FairyLandHeartDevilInfo, xddq.pb.FairyLandHeartDevilInfo.Builder, xddq.pb.FairyLandHeartDevilInfoOrBuilder> heartDevilInfoBuilder_;
    /**
     * <code>optional .xddq.pb.FairyLandHeartDevilInfo heartDevilInfo = 2;</code>
     * @return Whether the heartDevilInfo field is set.
     */
    public boolean hasHeartDevilInfo() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .xddq.pb.FairyLandHeartDevilInfo heartDevilInfo = 2;</code>
     * @return The heartDevilInfo.
     */
    public xddq.pb.FairyLandHeartDevilInfo getHeartDevilInfo() {
      if (heartDevilInfoBuilder_ == null) {
        return heartDevilInfo_ == null ? xddq.pb.FairyLandHeartDevilInfo.getDefaultInstance() : heartDevilInfo_;
      } else {
        return heartDevilInfoBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.FairyLandHeartDevilInfo heartDevilInfo = 2;</code>
     */
    public Builder setHeartDevilInfo(xddq.pb.FairyLandHeartDevilInfo value) {
      if (heartDevilInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        heartDevilInfo_ = value;
      } else {
        heartDevilInfoBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.FairyLandHeartDevilInfo heartDevilInfo = 2;</code>
     */
    public Builder setHeartDevilInfo(
        xddq.pb.FairyLandHeartDevilInfo.Builder builderForValue) {
      if (heartDevilInfoBuilder_ == null) {
        heartDevilInfo_ = builderForValue.build();
      } else {
        heartDevilInfoBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.FairyLandHeartDevilInfo heartDevilInfo = 2;</code>
     */
    public Builder mergeHeartDevilInfo(xddq.pb.FairyLandHeartDevilInfo value) {
      if (heartDevilInfoBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0) &&
          heartDevilInfo_ != null &&
          heartDevilInfo_ != xddq.pb.FairyLandHeartDevilInfo.getDefaultInstance()) {
          getHeartDevilInfoBuilder().mergeFrom(value);
        } else {
          heartDevilInfo_ = value;
        }
      } else {
        heartDevilInfoBuilder_.mergeFrom(value);
      }
      if (heartDevilInfo_ != null) {
        bitField0_ |= 0x00000002;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.FairyLandHeartDevilInfo heartDevilInfo = 2;</code>
     */
    public Builder clearHeartDevilInfo() {
      bitField0_ = (bitField0_ & ~0x00000002);
      heartDevilInfo_ = null;
      if (heartDevilInfoBuilder_ != null) {
        heartDevilInfoBuilder_.dispose();
        heartDevilInfoBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.FairyLandHeartDevilInfo heartDevilInfo = 2;</code>
     */
    public xddq.pb.FairyLandHeartDevilInfo.Builder getHeartDevilInfoBuilder() {
      bitField0_ |= 0x00000002;
      onChanged();
      return internalGetHeartDevilInfoFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.FairyLandHeartDevilInfo heartDevilInfo = 2;</code>
     */
    public xddq.pb.FairyLandHeartDevilInfoOrBuilder getHeartDevilInfoOrBuilder() {
      if (heartDevilInfoBuilder_ != null) {
        return heartDevilInfoBuilder_.getMessageOrBuilder();
      } else {
        return heartDevilInfo_ == null ?
            xddq.pb.FairyLandHeartDevilInfo.getDefaultInstance() : heartDevilInfo_;
      }
    }
    /**
     * <code>optional .xddq.pb.FairyLandHeartDevilInfo heartDevilInfo = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.FairyLandHeartDevilInfo, xddq.pb.FairyLandHeartDevilInfo.Builder, xddq.pb.FairyLandHeartDevilInfoOrBuilder> 
        internalGetHeartDevilInfoFieldBuilder() {
      if (heartDevilInfoBuilder_ == null) {
        heartDevilInfoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.FairyLandHeartDevilInfo, xddq.pb.FairyLandHeartDevilInfo.Builder, xddq.pb.FairyLandHeartDevilInfoOrBuilder>(
                getHeartDevilInfo(),
                getParentForChildren(),
                isClean());
        heartDevilInfo_ = null;
      }
      return heartDevilInfoBuilder_;
    }

    private xddq.pb.FairyLandBattleInfo battleInfo_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.FairyLandBattleInfo, xddq.pb.FairyLandBattleInfo.Builder, xddq.pb.FairyLandBattleInfoOrBuilder> battleInfoBuilder_;
    /**
     * <code>optional .xddq.pb.FairyLandBattleInfo battleInfo = 3;</code>
     * @return Whether the battleInfo field is set.
     */
    public boolean hasBattleInfo() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional .xddq.pb.FairyLandBattleInfo battleInfo = 3;</code>
     * @return The battleInfo.
     */
    public xddq.pb.FairyLandBattleInfo getBattleInfo() {
      if (battleInfoBuilder_ == null) {
        return battleInfo_ == null ? xddq.pb.FairyLandBattleInfo.getDefaultInstance() : battleInfo_;
      } else {
        return battleInfoBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.FairyLandBattleInfo battleInfo = 3;</code>
     */
    public Builder setBattleInfo(xddq.pb.FairyLandBattleInfo value) {
      if (battleInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        battleInfo_ = value;
      } else {
        battleInfoBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.FairyLandBattleInfo battleInfo = 3;</code>
     */
    public Builder setBattleInfo(
        xddq.pb.FairyLandBattleInfo.Builder builderForValue) {
      if (battleInfoBuilder_ == null) {
        battleInfo_ = builderForValue.build();
      } else {
        battleInfoBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.FairyLandBattleInfo battleInfo = 3;</code>
     */
    public Builder mergeBattleInfo(xddq.pb.FairyLandBattleInfo value) {
      if (battleInfoBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0) &&
          battleInfo_ != null &&
          battleInfo_ != xddq.pb.FairyLandBattleInfo.getDefaultInstance()) {
          getBattleInfoBuilder().mergeFrom(value);
        } else {
          battleInfo_ = value;
        }
      } else {
        battleInfoBuilder_.mergeFrom(value);
      }
      if (battleInfo_ != null) {
        bitField0_ |= 0x00000004;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.FairyLandBattleInfo battleInfo = 3;</code>
     */
    public Builder clearBattleInfo() {
      bitField0_ = (bitField0_ & ~0x00000004);
      battleInfo_ = null;
      if (battleInfoBuilder_ != null) {
        battleInfoBuilder_.dispose();
        battleInfoBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.FairyLandBattleInfo battleInfo = 3;</code>
     */
    public xddq.pb.FairyLandBattleInfo.Builder getBattleInfoBuilder() {
      bitField0_ |= 0x00000004;
      onChanged();
      return internalGetBattleInfoFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.FairyLandBattleInfo battleInfo = 3;</code>
     */
    public xddq.pb.FairyLandBattleInfoOrBuilder getBattleInfoOrBuilder() {
      if (battleInfoBuilder_ != null) {
        return battleInfoBuilder_.getMessageOrBuilder();
      } else {
        return battleInfo_ == null ?
            xddq.pb.FairyLandBattleInfo.getDefaultInstance() : battleInfo_;
      }
    }
    /**
     * <code>optional .xddq.pb.FairyLandBattleInfo battleInfo = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.FairyLandBattleInfo, xddq.pb.FairyLandBattleInfo.Builder, xddq.pb.FairyLandBattleInfoOrBuilder> 
        internalGetBattleInfoFieldBuilder() {
      if (battleInfoBuilder_ == null) {
        battleInfoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.FairyLandBattleInfo, xddq.pb.FairyLandBattleInfo.Builder, xddq.pb.FairyLandBattleInfoOrBuilder>(
                getBattleInfo(),
                getParentForChildren(),
                isClean());
        battleInfo_ = null;
      }
      return battleInfoBuilder_;
    }

    private int helpTimes_ ;
    /**
     * <code>optional int32 helpTimes = 4;</code>
     * @return Whether the helpTimes field is set.
     */
    @java.lang.Override
    public boolean hasHelpTimes() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int32 helpTimes = 4;</code>
     * @return The helpTimes.
     */
    @java.lang.Override
    public int getHelpTimes() {
      return helpTimes_;
    }
    /**
     * <code>optional int32 helpTimes = 4;</code>
     * @param value The helpTimes to set.
     * @return This builder for chaining.
     */
    public Builder setHelpTimes(int value) {

      helpTimes_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 helpTimes = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearHelpTimes() {
      bitField0_ = (bitField0_ & ~0x00000008);
      helpTimes_ = 0;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.PlayerFairyLandDataMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.PlayerFairyLandDataMsg)
  private static final xddq.pb.PlayerFairyLandDataMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.PlayerFairyLandDataMsg();
  }

  public static xddq.pb.PlayerFairyLandDataMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<PlayerFairyLandDataMsg>
      PARSER = new com.google.protobuf.AbstractParser<PlayerFairyLandDataMsg>() {
    @java.lang.Override
    public PlayerFairyLandDataMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<PlayerFairyLandDataMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<PlayerFairyLandDataMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.PlayerFairyLandDataMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

