// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.ShuraBattleInnerTeamSync}
 */
public final class ShuraBattleInnerTeamSync extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.ShuraBattleInnerTeamSync)
    ShuraBattleInnerTeamSyncOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      ShuraBattleInnerTeamSync.class.getName());
  }
  // Use ShuraBattleInnerTeamSync.newBuilder() to construct.
  private ShuraBattleInnerTeamSync(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private ShuraBattleInnerTeamSync() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_ShuraBattleInnerTeamSync_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_ShuraBattleInnerTeamSync_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.ShuraBattleInnerTeamSync.class, xddq.pb.ShuraBattleInnerTeamSync.Builder.class);
  }

  private int bitField0_;
  public static final int TYPE_FIELD_NUMBER = 1;
  private int type_ = 0;
  /**
   * <code>optional int32 type = 1;</code>
   * @return Whether the type field is set.
   */
  @java.lang.Override
  public boolean hasType() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int32 type = 1;</code>
   * @return The type.
   */
  @java.lang.Override
  public int getType() {
    return type_;
  }

  public static final int FLOOR_FIELD_NUMBER = 2;
  private int floor_ = 0;
  /**
   * <code>optional int32 floor = 2;</code>
   * @return Whether the floor field is set.
   */
  @java.lang.Override
  public boolean hasFloor() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int32 floor = 2;</code>
   * @return The floor.
   */
  @java.lang.Override
  public int getFloor() {
    return floor_;
  }

  public static final int NEWTEAM_FIELD_NUMBER = 3;
  private xddq.pb.ShuraBttleInnerTeamInfo newTeam_;
  /**
   * <code>optional .xddq.pb.ShuraBttleInnerTeamInfo newTeam = 3;</code>
   * @return Whether the newTeam field is set.
   */
  @java.lang.Override
  public boolean hasNewTeam() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional .xddq.pb.ShuraBttleInnerTeamInfo newTeam = 3;</code>
   * @return The newTeam.
   */
  @java.lang.Override
  public xddq.pb.ShuraBttleInnerTeamInfo getNewTeam() {
    return newTeam_ == null ? xddq.pb.ShuraBttleInnerTeamInfo.getDefaultInstance() : newTeam_;
  }
  /**
   * <code>optional .xddq.pb.ShuraBttleInnerTeamInfo newTeam = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.ShuraBttleInnerTeamInfoOrBuilder getNewTeamOrBuilder() {
    return newTeam_ == null ? xddq.pb.ShuraBttleInnerTeamInfo.getDefaultInstance() : newTeam_;
  }

  public static final int REMOVETEAMID_FIELD_NUMBER = 4;
  private long removeTeamId_ = 0L;
  /**
   * <code>optional int64 removeTeamId = 4;</code>
   * @return Whether the removeTeamId field is set.
   */
  @java.lang.Override
  public boolean hasRemoveTeamId() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional int64 removeTeamId = 4;</code>
   * @return The removeTeamId.
   */
  @java.lang.Override
  public long getRemoveTeamId() {
    return removeTeamId_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (hasNewTeam()) {
      if (!getNewTeam().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, type_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(2, floor_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeMessage(3, getNewTeam());
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt64(4, removeTeamId_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, type_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, floor_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, getNewTeam());
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(4, removeTeamId_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.ShuraBattleInnerTeamSync)) {
      return super.equals(obj);
    }
    xddq.pb.ShuraBattleInnerTeamSync other = (xddq.pb.ShuraBattleInnerTeamSync) obj;

    if (hasType() != other.hasType()) return false;
    if (hasType()) {
      if (getType()
          != other.getType()) return false;
    }
    if (hasFloor() != other.hasFloor()) return false;
    if (hasFloor()) {
      if (getFloor()
          != other.getFloor()) return false;
    }
    if (hasNewTeam() != other.hasNewTeam()) return false;
    if (hasNewTeam()) {
      if (!getNewTeam()
          .equals(other.getNewTeam())) return false;
    }
    if (hasRemoveTeamId() != other.hasRemoveTeamId()) return false;
    if (hasRemoveTeamId()) {
      if (getRemoveTeamId()
          != other.getRemoveTeamId()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasType()) {
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
    }
    if (hasFloor()) {
      hash = (37 * hash) + FLOOR_FIELD_NUMBER;
      hash = (53 * hash) + getFloor();
    }
    if (hasNewTeam()) {
      hash = (37 * hash) + NEWTEAM_FIELD_NUMBER;
      hash = (53 * hash) + getNewTeam().hashCode();
    }
    if (hasRemoveTeamId()) {
      hash = (37 * hash) + REMOVETEAMID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getRemoveTeamId());
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.ShuraBattleInnerTeamSync parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ShuraBattleInnerTeamSync parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ShuraBattleInnerTeamSync parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ShuraBattleInnerTeamSync parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ShuraBattleInnerTeamSync parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ShuraBattleInnerTeamSync parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ShuraBattleInnerTeamSync parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.ShuraBattleInnerTeamSync parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.ShuraBattleInnerTeamSync parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.ShuraBattleInnerTeamSync parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.ShuraBattleInnerTeamSync parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.ShuraBattleInnerTeamSync parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.ShuraBattleInnerTeamSync prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.ShuraBattleInnerTeamSync}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.ShuraBattleInnerTeamSync)
      xddq.pb.ShuraBattleInnerTeamSyncOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ShuraBattleInnerTeamSync_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ShuraBattleInnerTeamSync_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.ShuraBattleInnerTeamSync.class, xddq.pb.ShuraBattleInnerTeamSync.Builder.class);
    }

    // Construct using xddq.pb.ShuraBattleInnerTeamSync.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetNewTeamFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      type_ = 0;
      floor_ = 0;
      newTeam_ = null;
      if (newTeamBuilder_ != null) {
        newTeamBuilder_.dispose();
        newTeamBuilder_ = null;
      }
      removeTeamId_ = 0L;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ShuraBattleInnerTeamSync_descriptor;
    }

    @java.lang.Override
    public xddq.pb.ShuraBattleInnerTeamSync getDefaultInstanceForType() {
      return xddq.pb.ShuraBattleInnerTeamSync.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.ShuraBattleInnerTeamSync build() {
      xddq.pb.ShuraBattleInnerTeamSync result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.ShuraBattleInnerTeamSync buildPartial() {
      xddq.pb.ShuraBattleInnerTeamSync result = new xddq.pb.ShuraBattleInnerTeamSync(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.ShuraBattleInnerTeamSync result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.type_ = type_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.floor_ = floor_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.newTeam_ = newTeamBuilder_ == null
            ? newTeam_
            : newTeamBuilder_.build();
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.removeTeamId_ = removeTeamId_;
        to_bitField0_ |= 0x00000008;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.ShuraBattleInnerTeamSync) {
        return mergeFrom((xddq.pb.ShuraBattleInnerTeamSync)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.ShuraBattleInnerTeamSync other) {
      if (other == xddq.pb.ShuraBattleInnerTeamSync.getDefaultInstance()) return this;
      if (other.hasType()) {
        setType(other.getType());
      }
      if (other.hasFloor()) {
        setFloor(other.getFloor());
      }
      if (other.hasNewTeam()) {
        mergeNewTeam(other.getNewTeam());
      }
      if (other.hasRemoveTeamId()) {
        setRemoveTeamId(other.getRemoveTeamId());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (hasNewTeam()) {
        if (!getNewTeam().isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              type_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              floor_ = input.readInt32();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 26: {
              input.readMessage(
                  internalGetNewTeamFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 32: {
              removeTeamId_ = input.readInt64();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int type_ ;
    /**
     * <code>optional int32 type = 1;</code>
     * @return Whether the type field is set.
     */
    @java.lang.Override
    public boolean hasType() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 type = 1;</code>
     * @return The type.
     */
    @java.lang.Override
    public int getType() {
      return type_;
    }
    /**
     * <code>optional int32 type = 1;</code>
     * @param value The type to set.
     * @return This builder for chaining.
     */
    public Builder setType(int value) {

      type_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 type = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearType() {
      bitField0_ = (bitField0_ & ~0x00000001);
      type_ = 0;
      onChanged();
      return this;
    }

    private int floor_ ;
    /**
     * <code>optional int32 floor = 2;</code>
     * @return Whether the floor field is set.
     */
    @java.lang.Override
    public boolean hasFloor() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 floor = 2;</code>
     * @return The floor.
     */
    @java.lang.Override
    public int getFloor() {
      return floor_;
    }
    /**
     * <code>optional int32 floor = 2;</code>
     * @param value The floor to set.
     * @return This builder for chaining.
     */
    public Builder setFloor(int value) {

      floor_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 floor = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearFloor() {
      bitField0_ = (bitField0_ & ~0x00000002);
      floor_ = 0;
      onChanged();
      return this;
    }

    private xddq.pb.ShuraBttleInnerTeamInfo newTeam_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.ShuraBttleInnerTeamInfo, xddq.pb.ShuraBttleInnerTeamInfo.Builder, xddq.pb.ShuraBttleInnerTeamInfoOrBuilder> newTeamBuilder_;
    /**
     * <code>optional .xddq.pb.ShuraBttleInnerTeamInfo newTeam = 3;</code>
     * @return Whether the newTeam field is set.
     */
    public boolean hasNewTeam() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional .xddq.pb.ShuraBttleInnerTeamInfo newTeam = 3;</code>
     * @return The newTeam.
     */
    public xddq.pb.ShuraBttleInnerTeamInfo getNewTeam() {
      if (newTeamBuilder_ == null) {
        return newTeam_ == null ? xddq.pb.ShuraBttleInnerTeamInfo.getDefaultInstance() : newTeam_;
      } else {
        return newTeamBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.ShuraBttleInnerTeamInfo newTeam = 3;</code>
     */
    public Builder setNewTeam(xddq.pb.ShuraBttleInnerTeamInfo value) {
      if (newTeamBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        newTeam_ = value;
      } else {
        newTeamBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ShuraBttleInnerTeamInfo newTeam = 3;</code>
     */
    public Builder setNewTeam(
        xddq.pb.ShuraBttleInnerTeamInfo.Builder builderForValue) {
      if (newTeamBuilder_ == null) {
        newTeam_ = builderForValue.build();
      } else {
        newTeamBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ShuraBttleInnerTeamInfo newTeam = 3;</code>
     */
    public Builder mergeNewTeam(xddq.pb.ShuraBttleInnerTeamInfo value) {
      if (newTeamBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0) &&
          newTeam_ != null &&
          newTeam_ != xddq.pb.ShuraBttleInnerTeamInfo.getDefaultInstance()) {
          getNewTeamBuilder().mergeFrom(value);
        } else {
          newTeam_ = value;
        }
      } else {
        newTeamBuilder_.mergeFrom(value);
      }
      if (newTeam_ != null) {
        bitField0_ |= 0x00000004;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.ShuraBttleInnerTeamInfo newTeam = 3;</code>
     */
    public Builder clearNewTeam() {
      bitField0_ = (bitField0_ & ~0x00000004);
      newTeam_ = null;
      if (newTeamBuilder_ != null) {
        newTeamBuilder_.dispose();
        newTeamBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ShuraBttleInnerTeamInfo newTeam = 3;</code>
     */
    public xddq.pb.ShuraBttleInnerTeamInfo.Builder getNewTeamBuilder() {
      bitField0_ |= 0x00000004;
      onChanged();
      return internalGetNewTeamFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.ShuraBttleInnerTeamInfo newTeam = 3;</code>
     */
    public xddq.pb.ShuraBttleInnerTeamInfoOrBuilder getNewTeamOrBuilder() {
      if (newTeamBuilder_ != null) {
        return newTeamBuilder_.getMessageOrBuilder();
      } else {
        return newTeam_ == null ?
            xddq.pb.ShuraBttleInnerTeamInfo.getDefaultInstance() : newTeam_;
      }
    }
    /**
     * <code>optional .xddq.pb.ShuraBttleInnerTeamInfo newTeam = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.ShuraBttleInnerTeamInfo, xddq.pb.ShuraBttleInnerTeamInfo.Builder, xddq.pb.ShuraBttleInnerTeamInfoOrBuilder> 
        internalGetNewTeamFieldBuilder() {
      if (newTeamBuilder_ == null) {
        newTeamBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.ShuraBttleInnerTeamInfo, xddq.pb.ShuraBttleInnerTeamInfo.Builder, xddq.pb.ShuraBttleInnerTeamInfoOrBuilder>(
                getNewTeam(),
                getParentForChildren(),
                isClean());
        newTeam_ = null;
      }
      return newTeamBuilder_;
    }

    private long removeTeamId_ ;
    /**
     * <code>optional int64 removeTeamId = 4;</code>
     * @return Whether the removeTeamId field is set.
     */
    @java.lang.Override
    public boolean hasRemoveTeamId() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int64 removeTeamId = 4;</code>
     * @return The removeTeamId.
     */
    @java.lang.Override
    public long getRemoveTeamId() {
      return removeTeamId_;
    }
    /**
     * <code>optional int64 removeTeamId = 4;</code>
     * @param value The removeTeamId to set.
     * @return This builder for chaining.
     */
    public Builder setRemoveTeamId(long value) {

      removeTeamId_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 removeTeamId = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearRemoveTeamId() {
      bitField0_ = (bitField0_ & ~0x00000008);
      removeTeamId_ = 0L;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.ShuraBattleInnerTeamSync)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.ShuraBattleInnerTeamSync)
  private static final xddq.pb.ShuraBattleInnerTeamSync DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.ShuraBattleInnerTeamSync();
  }

  public static xddq.pb.ShuraBattleInnerTeamSync getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ShuraBattleInnerTeamSync>
      PARSER = new com.google.protobuf.AbstractParser<ShuraBattleInnerTeamSync>() {
    @java.lang.Override
    public ShuraBattleInnerTeamSync parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<ShuraBattleInnerTeamSync> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ShuraBattleInnerTeamSync> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.ShuraBattleInnerTeamSync getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

