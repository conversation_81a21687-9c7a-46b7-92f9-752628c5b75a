// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.HeavenBattleTreasureGridSummaryResp}
 */
public final class HeavenBattleTreasureGridSummaryResp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.HeavenBattleTreasureGridSummaryResp)
    HeavenBattleTreasureGridSummaryRespOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      HeavenBattleTreasureGridSummaryResp.class.getName());
  }
  // Use HeavenBattleTreasureGridSummaryResp.newBuilder() to construct.
  private HeavenBattleTreasureGridSummaryResp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private HeavenBattleTreasureGridSummaryResp() {
    summaryList_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_HeavenBattleTreasureGridSummaryResp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_HeavenBattleTreasureGridSummaryResp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.HeavenBattleTreasureGridSummaryResp.class, xddq.pb.HeavenBattleTreasureGridSummaryResp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>optional int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int SUMMARYLIST_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.HeavenBattleTreasureSummary> summaryList_;
  /**
   * <code>repeated .xddq.pb.HeavenBattleTreasureSummary summaryList = 2;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.HeavenBattleTreasureSummary> getSummaryListList() {
    return summaryList_;
  }
  /**
   * <code>repeated .xddq.pb.HeavenBattleTreasureSummary summaryList = 2;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.HeavenBattleTreasureSummaryOrBuilder> 
      getSummaryListOrBuilderList() {
    return summaryList_;
  }
  /**
   * <code>repeated .xddq.pb.HeavenBattleTreasureSummary summaryList = 2;</code>
   */
  @java.lang.Override
  public int getSummaryListCount() {
    return summaryList_.size();
  }
  /**
   * <code>repeated .xddq.pb.HeavenBattleTreasureSummary summaryList = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.HeavenBattleTreasureSummary getSummaryList(int index) {
    return summaryList_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.HeavenBattleTreasureSummary summaryList = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.HeavenBattleTreasureSummaryOrBuilder getSummaryListOrBuilder(
      int index) {
    return summaryList_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    for (int i = 0; i < summaryList_.size(); i++) {
      output.writeMessage(2, summaryList_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    for (int i = 0; i < summaryList_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, summaryList_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.HeavenBattleTreasureGridSummaryResp)) {
      return super.equals(obj);
    }
    xddq.pb.HeavenBattleTreasureGridSummaryResp other = (xddq.pb.HeavenBattleTreasureGridSummaryResp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (!getSummaryListList()
        .equals(other.getSummaryListList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (getSummaryListCount() > 0) {
      hash = (37 * hash) + SUMMARYLIST_FIELD_NUMBER;
      hash = (53 * hash) + getSummaryListList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.HeavenBattleTreasureGridSummaryResp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.HeavenBattleTreasureGridSummaryResp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.HeavenBattleTreasureGridSummaryResp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.HeavenBattleTreasureGridSummaryResp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.HeavenBattleTreasureGridSummaryResp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.HeavenBattleTreasureGridSummaryResp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.HeavenBattleTreasureGridSummaryResp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.HeavenBattleTreasureGridSummaryResp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.HeavenBattleTreasureGridSummaryResp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.HeavenBattleTreasureGridSummaryResp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.HeavenBattleTreasureGridSummaryResp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.HeavenBattleTreasureGridSummaryResp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.HeavenBattleTreasureGridSummaryResp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.HeavenBattleTreasureGridSummaryResp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.HeavenBattleTreasureGridSummaryResp)
      xddq.pb.HeavenBattleTreasureGridSummaryRespOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_HeavenBattleTreasureGridSummaryResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_HeavenBattleTreasureGridSummaryResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.HeavenBattleTreasureGridSummaryResp.class, xddq.pb.HeavenBattleTreasureGridSummaryResp.Builder.class);
    }

    // Construct using xddq.pb.HeavenBattleTreasureGridSummaryResp.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      if (summaryListBuilder_ == null) {
        summaryList_ = java.util.Collections.emptyList();
      } else {
        summaryList_ = null;
        summaryListBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000002);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_HeavenBattleTreasureGridSummaryResp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.HeavenBattleTreasureGridSummaryResp getDefaultInstanceForType() {
      return xddq.pb.HeavenBattleTreasureGridSummaryResp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.HeavenBattleTreasureGridSummaryResp build() {
      xddq.pb.HeavenBattleTreasureGridSummaryResp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.HeavenBattleTreasureGridSummaryResp buildPartial() {
      xddq.pb.HeavenBattleTreasureGridSummaryResp result = new xddq.pb.HeavenBattleTreasureGridSummaryResp(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.HeavenBattleTreasureGridSummaryResp result) {
      if (summaryListBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          summaryList_ = java.util.Collections.unmodifiableList(summaryList_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.summaryList_ = summaryList_;
      } else {
        result.summaryList_ = summaryListBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.HeavenBattleTreasureGridSummaryResp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.HeavenBattleTreasureGridSummaryResp) {
        return mergeFrom((xddq.pb.HeavenBattleTreasureGridSummaryResp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.HeavenBattleTreasureGridSummaryResp other) {
      if (other == xddq.pb.HeavenBattleTreasureGridSummaryResp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (summaryListBuilder_ == null) {
        if (!other.summaryList_.isEmpty()) {
          if (summaryList_.isEmpty()) {
            summaryList_ = other.summaryList_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureSummaryListIsMutable();
            summaryList_.addAll(other.summaryList_);
          }
          onChanged();
        }
      } else {
        if (!other.summaryList_.isEmpty()) {
          if (summaryListBuilder_.isEmpty()) {
            summaryListBuilder_.dispose();
            summaryListBuilder_ = null;
            summaryList_ = other.summaryList_;
            bitField0_ = (bitField0_ & ~0x00000002);
            summaryListBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetSummaryListFieldBuilder() : null;
          } else {
            summaryListBuilder_.addAllMessages(other.summaryList_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              xddq.pb.HeavenBattleTreasureSummary m =
                  input.readMessage(
                      xddq.pb.HeavenBattleTreasureSummary.parser(),
                      extensionRegistry);
              if (summaryListBuilder_ == null) {
                ensureSummaryListIsMutable();
                summaryList_.add(m);
              } else {
                summaryListBuilder_.addMessage(m);
              }
              break;
            } // case 18
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>optional int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>optional int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.HeavenBattleTreasureSummary> summaryList_ =
      java.util.Collections.emptyList();
    private void ensureSummaryListIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        summaryList_ = new java.util.ArrayList<xddq.pb.HeavenBattleTreasureSummary>(summaryList_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.HeavenBattleTreasureSummary, xddq.pb.HeavenBattleTreasureSummary.Builder, xddq.pb.HeavenBattleTreasureSummaryOrBuilder> summaryListBuilder_;

    /**
     * <code>repeated .xddq.pb.HeavenBattleTreasureSummary summaryList = 2;</code>
     */
    public java.util.List<xddq.pb.HeavenBattleTreasureSummary> getSummaryListList() {
      if (summaryListBuilder_ == null) {
        return java.util.Collections.unmodifiableList(summaryList_);
      } else {
        return summaryListBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleTreasureSummary summaryList = 2;</code>
     */
    public int getSummaryListCount() {
      if (summaryListBuilder_ == null) {
        return summaryList_.size();
      } else {
        return summaryListBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleTreasureSummary summaryList = 2;</code>
     */
    public xddq.pb.HeavenBattleTreasureSummary getSummaryList(int index) {
      if (summaryListBuilder_ == null) {
        return summaryList_.get(index);
      } else {
        return summaryListBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleTreasureSummary summaryList = 2;</code>
     */
    public Builder setSummaryList(
        int index, xddq.pb.HeavenBattleTreasureSummary value) {
      if (summaryListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureSummaryListIsMutable();
        summaryList_.set(index, value);
        onChanged();
      } else {
        summaryListBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleTreasureSummary summaryList = 2;</code>
     */
    public Builder setSummaryList(
        int index, xddq.pb.HeavenBattleTreasureSummary.Builder builderForValue) {
      if (summaryListBuilder_ == null) {
        ensureSummaryListIsMutable();
        summaryList_.set(index, builderForValue.build());
        onChanged();
      } else {
        summaryListBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleTreasureSummary summaryList = 2;</code>
     */
    public Builder addSummaryList(xddq.pb.HeavenBattleTreasureSummary value) {
      if (summaryListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureSummaryListIsMutable();
        summaryList_.add(value);
        onChanged();
      } else {
        summaryListBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleTreasureSummary summaryList = 2;</code>
     */
    public Builder addSummaryList(
        int index, xddq.pb.HeavenBattleTreasureSummary value) {
      if (summaryListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureSummaryListIsMutable();
        summaryList_.add(index, value);
        onChanged();
      } else {
        summaryListBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleTreasureSummary summaryList = 2;</code>
     */
    public Builder addSummaryList(
        xddq.pb.HeavenBattleTreasureSummary.Builder builderForValue) {
      if (summaryListBuilder_ == null) {
        ensureSummaryListIsMutable();
        summaryList_.add(builderForValue.build());
        onChanged();
      } else {
        summaryListBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleTreasureSummary summaryList = 2;</code>
     */
    public Builder addSummaryList(
        int index, xddq.pb.HeavenBattleTreasureSummary.Builder builderForValue) {
      if (summaryListBuilder_ == null) {
        ensureSummaryListIsMutable();
        summaryList_.add(index, builderForValue.build());
        onChanged();
      } else {
        summaryListBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleTreasureSummary summaryList = 2;</code>
     */
    public Builder addAllSummaryList(
        java.lang.Iterable<? extends xddq.pb.HeavenBattleTreasureSummary> values) {
      if (summaryListBuilder_ == null) {
        ensureSummaryListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, summaryList_);
        onChanged();
      } else {
        summaryListBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleTreasureSummary summaryList = 2;</code>
     */
    public Builder clearSummaryList() {
      if (summaryListBuilder_ == null) {
        summaryList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        summaryListBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleTreasureSummary summaryList = 2;</code>
     */
    public Builder removeSummaryList(int index) {
      if (summaryListBuilder_ == null) {
        ensureSummaryListIsMutable();
        summaryList_.remove(index);
        onChanged();
      } else {
        summaryListBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleTreasureSummary summaryList = 2;</code>
     */
    public xddq.pb.HeavenBattleTreasureSummary.Builder getSummaryListBuilder(
        int index) {
      return internalGetSummaryListFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleTreasureSummary summaryList = 2;</code>
     */
    public xddq.pb.HeavenBattleTreasureSummaryOrBuilder getSummaryListOrBuilder(
        int index) {
      if (summaryListBuilder_ == null) {
        return summaryList_.get(index);  } else {
        return summaryListBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleTreasureSummary summaryList = 2;</code>
     */
    public java.util.List<? extends xddq.pb.HeavenBattleTreasureSummaryOrBuilder> 
         getSummaryListOrBuilderList() {
      if (summaryListBuilder_ != null) {
        return summaryListBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(summaryList_);
      }
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleTreasureSummary summaryList = 2;</code>
     */
    public xddq.pb.HeavenBattleTreasureSummary.Builder addSummaryListBuilder() {
      return internalGetSummaryListFieldBuilder().addBuilder(
          xddq.pb.HeavenBattleTreasureSummary.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleTreasureSummary summaryList = 2;</code>
     */
    public xddq.pb.HeavenBattleTreasureSummary.Builder addSummaryListBuilder(
        int index) {
      return internalGetSummaryListFieldBuilder().addBuilder(
          index, xddq.pb.HeavenBattleTreasureSummary.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleTreasureSummary summaryList = 2;</code>
     */
    public java.util.List<xddq.pb.HeavenBattleTreasureSummary.Builder> 
         getSummaryListBuilderList() {
      return internalGetSummaryListFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.HeavenBattleTreasureSummary, xddq.pb.HeavenBattleTreasureSummary.Builder, xddq.pb.HeavenBattleTreasureSummaryOrBuilder> 
        internalGetSummaryListFieldBuilder() {
      if (summaryListBuilder_ == null) {
        summaryListBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.HeavenBattleTreasureSummary, xddq.pb.HeavenBattleTreasureSummary.Builder, xddq.pb.HeavenBattleTreasureSummaryOrBuilder>(
                summaryList_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        summaryList_ = null;
      }
      return summaryListBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.HeavenBattleTreasureGridSummaryResp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.HeavenBattleTreasureGridSummaryResp)
  private static final xddq.pb.HeavenBattleTreasureGridSummaryResp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.HeavenBattleTreasureGridSummaryResp();
  }

  public static xddq.pb.HeavenBattleTreasureGridSummaryResp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<HeavenBattleTreasureGridSummaryResp>
      PARSER = new com.google.protobuf.AbstractParser<HeavenBattleTreasureGridSummaryResp>() {
    @java.lang.Override
    public HeavenBattleTreasureGridSummaryResp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<HeavenBattleTreasureGridSummaryResp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<HeavenBattleTreasureGridSummaryResp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.HeavenBattleTreasureGridSummaryResp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

