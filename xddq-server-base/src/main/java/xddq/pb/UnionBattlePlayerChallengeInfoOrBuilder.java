// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

public interface UnionBattlePlayerChallengeInfoOrBuilder extends
    // @@protoc_insertion_point(interface_extends:xddq.pb.UnionBattlePlayerChallengeInfo)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>optional string blood = 1;</code>
   * @return Whether the blood field is set.
   */
  boolean hasBlood();
  /**
   * <code>optional string blood = 1;</code>
   * @return The blood.
   */
  java.lang.String getBlood();
  /**
   * <code>optional string blood = 1;</code>
   * @return The bytes for blood.
   */
  com.google.protobuf.ByteString
      getBloodBytes();

  /**
   * <code>optional int32 tired = 2;</code>
   * @return Whether the tired field is set.
   */
  boolean hasTired();
  /**
   * <code>optional int32 tired = 2;</code>
   * @return The tired.
   */
  int getTired();

  /**
   * <code>optional int32 killNum = 3;</code>
   * @return Whether the killNum field is set.
   */
  boolean hasKillNum();
  /**
   * <code>optional int32 killNum = 3;</code>
   * @return The killNum.
   */
  int getKillNum();

  /**
   * <code>optional .xddq.pb.UnionBattleChallengeTargetUnionInfo targetUnionInfo = 4;</code>
   * @return Whether the targetUnionInfo field is set.
   */
  boolean hasTargetUnionInfo();
  /**
   * <code>optional .xddq.pb.UnionBattleChallengeTargetUnionInfo targetUnionInfo = 4;</code>
   * @return The targetUnionInfo.
   */
  xddq.pb.UnionBattleChallengeTargetUnionInfo getTargetUnionInfo();
  /**
   * <code>optional .xddq.pb.UnionBattleChallengeTargetUnionInfo targetUnionInfo = 4;</code>
   */
  xddq.pb.UnionBattleChallengeTargetUnionInfoOrBuilder getTargetUnionInfoOrBuilder();

  /**
   * <code>optional string power = 5;</code>
   * @return Whether the power field is set.
   */
  boolean hasPower();
  /**
   * <code>optional string power = 5;</code>
   * @return The power.
   */
  java.lang.String getPower();
  /**
   * <code>optional string power = 5;</code>
   * @return The bytes for power.
   */
  com.google.protobuf.ByteString
      getPowerBytes();

  /**
   * <code>repeated .xddq.pb.SkillMsg buffList = 6;</code>
   */
  java.util.List<xddq.pb.SkillMsg> 
      getBuffListList();
  /**
   * <code>repeated .xddq.pb.SkillMsg buffList = 6;</code>
   */
  xddq.pb.SkillMsg getBuffList(int index);
  /**
   * <code>repeated .xddq.pb.SkillMsg buffList = 6;</code>
   */
  int getBuffListCount();
  /**
   * <code>repeated .xddq.pb.SkillMsg buffList = 6;</code>
   */
  java.util.List<? extends xddq.pb.SkillMsgOrBuilder> 
      getBuffListOrBuilderList();
  /**
   * <code>repeated .xddq.pb.SkillMsg buffList = 6;</code>
   */
  xddq.pb.SkillMsgOrBuilder getBuffListOrBuilder(
      int index);

  /**
   * <code>repeated .xddq.pb.AttributeDataMsg attributeData = 7;</code>
   */
  java.util.List<xddq.pb.AttributeDataMsg> 
      getAttributeDataList();
  /**
   * <code>repeated .xddq.pb.AttributeDataMsg attributeData = 7;</code>
   */
  xddq.pb.AttributeDataMsg getAttributeData(int index);
  /**
   * <code>repeated .xddq.pb.AttributeDataMsg attributeData = 7;</code>
   */
  int getAttributeDataCount();
  /**
   * <code>repeated .xddq.pb.AttributeDataMsg attributeData = 7;</code>
   */
  java.util.List<? extends xddq.pb.AttributeDataMsgOrBuilder> 
      getAttributeDataOrBuilderList();
  /**
   * <code>repeated .xddq.pb.AttributeDataMsg attributeData = 7;</code>
   */
  xddq.pb.AttributeDataMsgOrBuilder getAttributeDataOrBuilder(
      int index);

  /**
   * <code>optional int32 openBuyBuffIndex = 8;</code>
   * @return Whether the openBuyBuffIndex field is set.
   */
  boolean hasOpenBuyBuffIndex();
  /**
   * <code>optional int32 openBuyBuffIndex = 8;</code>
   * @return The openBuyBuffIndex.
   */
  int getOpenBuyBuffIndex();

  /**
   * <code>repeated .xddq.pb.SkillMsg unSelectBuffList = 9;</code>
   */
  java.util.List<xddq.pb.SkillMsg> 
      getUnSelectBuffListList();
  /**
   * <code>repeated .xddq.pb.SkillMsg unSelectBuffList = 9;</code>
   */
  xddq.pb.SkillMsg getUnSelectBuffList(int index);
  /**
   * <code>repeated .xddq.pb.SkillMsg unSelectBuffList = 9;</code>
   */
  int getUnSelectBuffListCount();
  /**
   * <code>repeated .xddq.pb.SkillMsg unSelectBuffList = 9;</code>
   */
  java.util.List<? extends xddq.pb.SkillMsgOrBuilder> 
      getUnSelectBuffListOrBuilderList();
  /**
   * <code>repeated .xddq.pb.SkillMsg unSelectBuffList = 9;</code>
   */
  xddq.pb.SkillMsgOrBuilder getUnSelectBuffListOrBuilder(
      int index);

  /**
   * <code>optional int32 totalKillScore = 10;</code>
   * @return Whether the totalKillScore field is set.
   */
  boolean hasTotalKillScore();
  /**
   * <code>optional int32 totalKillScore = 10;</code>
   * @return The totalKillScore.
   */
  int getTotalKillScore();

  /**
   * <code>optional int32 lockAppearanceId = 11;</code>
   * @return Whether the lockAppearanceId field is set.
   */
  boolean hasLockAppearanceId();
  /**
   * <code>optional int32 lockAppearanceId = 11;</code>
   * @return The lockAppearanceId.
   */
  int getLockAppearanceId();

  /**
   * <code>optional int32 lockEquipCloudId = 12;</code>
   * @return Whether the lockEquipCloudId field is set.
   */
  boolean hasLockEquipCloudId();
  /**
   * <code>optional int32 lockEquipCloudId = 12;</code>
   * @return The lockEquipCloudId.
   */
  int getLockEquipCloudId();

  /**
   * <code>optional int32 lockEquipPetId = 13;</code>
   * @return Whether the lockEquipPetId field is set.
   */
  boolean hasLockEquipPetId();
  /**
   * <code>optional int32 lockEquipPetId = 13;</code>
   * @return The lockEquipPetId.
   */
  int getLockEquipPetId();

  /**
   * <code>optional int32 lockPetSoulShapeLv = 14;</code>
   * @return Whether the lockPetSoulShapeLv field is set.
   */
  boolean hasLockPetSoulShapeLv();
  /**
   * <code>optional int32 lockPetSoulShapeLv = 14;</code>
   * @return The lockPetSoulShapeLv.
   */
  int getLockPetSoulShapeLv();
}
