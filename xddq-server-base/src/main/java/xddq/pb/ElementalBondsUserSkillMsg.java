// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.ElementalBondsUserSkillMsg}
 */
public final class ElementalBondsUserSkillMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.ElementalBondsUserSkillMsg)
    ElementalBondsUserSkillMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      ElementalBondsUserSkillMsg.class.getName());
  }
  // Use ElementalBondsUserSkillMsg.newBuilder() to construct.
  private ElementalBondsUserSkillMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private ElementalBondsUserSkillMsg() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_ElementalBondsUserSkillMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_ElementalBondsUserSkillMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.ElementalBondsUserSkillMsg.class, xddq.pb.ElementalBondsUserSkillMsg.Builder.class);
  }

  private int bitField0_;
  public static final int SKILLID_FIELD_NUMBER = 1;
  private int skillId_ = 0;
  /**
   * <code>required int32 skillId = 1;</code>
   * @return Whether the skillId field is set.
   */
  @java.lang.Override
  public boolean hasSkillId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 skillId = 1;</code>
   * @return The skillId.
   */
  @java.lang.Override
  public int getSkillId() {
    return skillId_;
  }

  public static final int LEFTTIMES_FIELD_NUMBER = 2;
  private int leftTimes_ = 0;
  /**
   * <code>required int32 leftTimes = 2;</code>
   * @return Whether the leftTimes field is set.
   */
  @java.lang.Override
  public boolean hasLeftTimes() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>required int32 leftTimes = 2;</code>
   * @return The leftTimes.
   */
  @java.lang.Override
  public int getLeftTimes() {
    return leftTimes_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasSkillId()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasLeftTimes()) {
      memoizedIsInitialized = 0;
      return false;
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, skillId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(2, leftTimes_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, skillId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, leftTimes_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.ElementalBondsUserSkillMsg)) {
      return super.equals(obj);
    }
    xddq.pb.ElementalBondsUserSkillMsg other = (xddq.pb.ElementalBondsUserSkillMsg) obj;

    if (hasSkillId() != other.hasSkillId()) return false;
    if (hasSkillId()) {
      if (getSkillId()
          != other.getSkillId()) return false;
    }
    if (hasLeftTimes() != other.hasLeftTimes()) return false;
    if (hasLeftTimes()) {
      if (getLeftTimes()
          != other.getLeftTimes()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasSkillId()) {
      hash = (37 * hash) + SKILLID_FIELD_NUMBER;
      hash = (53 * hash) + getSkillId();
    }
    if (hasLeftTimes()) {
      hash = (37 * hash) + LEFTTIMES_FIELD_NUMBER;
      hash = (53 * hash) + getLeftTimes();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.ElementalBondsUserSkillMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ElementalBondsUserSkillMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ElementalBondsUserSkillMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ElementalBondsUserSkillMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ElementalBondsUserSkillMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ElementalBondsUserSkillMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ElementalBondsUserSkillMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.ElementalBondsUserSkillMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.ElementalBondsUserSkillMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.ElementalBondsUserSkillMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.ElementalBondsUserSkillMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.ElementalBondsUserSkillMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.ElementalBondsUserSkillMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.ElementalBondsUserSkillMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.ElementalBondsUserSkillMsg)
      xddq.pb.ElementalBondsUserSkillMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ElementalBondsUserSkillMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ElementalBondsUserSkillMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.ElementalBondsUserSkillMsg.class, xddq.pb.ElementalBondsUserSkillMsg.Builder.class);
    }

    // Construct using xddq.pb.ElementalBondsUserSkillMsg.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      skillId_ = 0;
      leftTimes_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ElementalBondsUserSkillMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.ElementalBondsUserSkillMsg getDefaultInstanceForType() {
      return xddq.pb.ElementalBondsUserSkillMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.ElementalBondsUserSkillMsg build() {
      xddq.pb.ElementalBondsUserSkillMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.ElementalBondsUserSkillMsg buildPartial() {
      xddq.pb.ElementalBondsUserSkillMsg result = new xddq.pb.ElementalBondsUserSkillMsg(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.ElementalBondsUserSkillMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.skillId_ = skillId_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.leftTimes_ = leftTimes_;
        to_bitField0_ |= 0x00000002;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.ElementalBondsUserSkillMsg) {
        return mergeFrom((xddq.pb.ElementalBondsUserSkillMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.ElementalBondsUserSkillMsg other) {
      if (other == xddq.pb.ElementalBondsUserSkillMsg.getDefaultInstance()) return this;
      if (other.hasSkillId()) {
        setSkillId(other.getSkillId());
      }
      if (other.hasLeftTimes()) {
        setLeftTimes(other.getLeftTimes());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasSkillId()) {
        return false;
      }
      if (!hasLeftTimes()) {
        return false;
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              skillId_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              leftTimes_ = input.readInt32();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int skillId_ ;
    /**
     * <code>required int32 skillId = 1;</code>
     * @return Whether the skillId field is set.
     */
    @java.lang.Override
    public boolean hasSkillId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 skillId = 1;</code>
     * @return The skillId.
     */
    @java.lang.Override
    public int getSkillId() {
      return skillId_;
    }
    /**
     * <code>required int32 skillId = 1;</code>
     * @param value The skillId to set.
     * @return This builder for chaining.
     */
    public Builder setSkillId(int value) {

      skillId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 skillId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearSkillId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      skillId_ = 0;
      onChanged();
      return this;
    }

    private int leftTimes_ ;
    /**
     * <code>required int32 leftTimes = 2;</code>
     * @return Whether the leftTimes field is set.
     */
    @java.lang.Override
    public boolean hasLeftTimes() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>required int32 leftTimes = 2;</code>
     * @return The leftTimes.
     */
    @java.lang.Override
    public int getLeftTimes() {
      return leftTimes_;
    }
    /**
     * <code>required int32 leftTimes = 2;</code>
     * @param value The leftTimes to set.
     * @return This builder for chaining.
     */
    public Builder setLeftTimes(int value) {

      leftTimes_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 leftTimes = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearLeftTimes() {
      bitField0_ = (bitField0_ & ~0x00000002);
      leftTimes_ = 0;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.ElementalBondsUserSkillMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.ElementalBondsUserSkillMsg)
  private static final xddq.pb.ElementalBondsUserSkillMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.ElementalBondsUserSkillMsg();
  }

  public static xddq.pb.ElementalBondsUserSkillMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ElementalBondsUserSkillMsg>
      PARSER = new com.google.protobuf.AbstractParser<ElementalBondsUserSkillMsg>() {
    @java.lang.Override
    public ElementalBondsUserSkillMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<ElementalBondsUserSkillMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ElementalBondsUserSkillMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.ElementalBondsUserSkillMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

