// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.YardEnterResp}
 */
public final class YardEnterResp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.YardEnterResp)
    YardEnterRespOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      YardEnterResp.class.getName());
  }
  // Use YardEnterResp.newBuilder() to construct.
  private YardEnterResp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private YardEnterResp() {
    areaInfo_ = java.util.Collections.emptyList();
    exp_ = "";
    helpData_ = java.util.Collections.emptyList();
    combineList_ = java.util.Collections.emptyList();
    decorateList_ = java.util.Collections.emptyList();
    timeBuildList_ = java.util.Collections.emptyList();
    mainBuildInfo_ = java.util.Collections.emptyList();
    convertPieceVec_ = "";
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_YardEnterResp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_YardEnterResp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.YardEnterResp.class, xddq.pb.YardEnterResp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int AREAINFO_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.YardAreaInfoMsg> areaInfo_;
  /**
   * <code>repeated .xddq.pb.YardAreaInfoMsg areaInfo = 2;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.YardAreaInfoMsg> getAreaInfoList() {
    return areaInfo_;
  }
  /**
   * <code>repeated .xddq.pb.YardAreaInfoMsg areaInfo = 2;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.YardAreaInfoMsgOrBuilder> 
      getAreaInfoOrBuilderList() {
    return areaInfo_;
  }
  /**
   * <code>repeated .xddq.pb.YardAreaInfoMsg areaInfo = 2;</code>
   */
  @java.lang.Override
  public int getAreaInfoCount() {
    return areaInfo_.size();
  }
  /**
   * <code>repeated .xddq.pb.YardAreaInfoMsg areaInfo = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.YardAreaInfoMsg getAreaInfo(int index) {
    return areaInfo_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.YardAreaInfoMsg areaInfo = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.YardAreaInfoMsgOrBuilder getAreaInfoOrBuilder(
      int index) {
    return areaInfo_.get(index);
  }

  public static final int LEVEL_FIELD_NUMBER = 4;
  private int level_ = 0;
  /**
   * <code>optional int32 level = 4;</code>
   * @return Whether the level field is set.
   */
  @java.lang.Override
  public boolean hasLevel() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int32 level = 4;</code>
   * @return The level.
   */
  @java.lang.Override
  public int getLevel() {
    return level_;
  }

  public static final int EXP_FIELD_NUMBER = 5;
  @SuppressWarnings("serial")
  private volatile java.lang.Object exp_ = "";
  /**
   * <code>optional string exp = 5;</code>
   * @return Whether the exp field is set.
   */
  @java.lang.Override
  public boolean hasExp() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional string exp = 5;</code>
   * @return The exp.
   */
  @java.lang.Override
  public java.lang.String getExp() {
    java.lang.Object ref = exp_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        exp_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string exp = 5;</code>
   * @return The bytes for exp.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getExpBytes() {
    java.lang.Object ref = exp_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      exp_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int QUEUENUM_FIELD_NUMBER = 6;
  private int queueNum_ = 0;
  /**
   * <code>optional int32 queueNum = 6;</code>
   * @return Whether the queueNum field is set.
   */
  @java.lang.Override
  public boolean hasQueueNum() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional int32 queueNum = 6;</code>
   * @return The queueNum.
   */
  @java.lang.Override
  public int getQueueNum() {
    return queueNum_;
  }

  public static final int ISOWNER_FIELD_NUMBER = 7;
  private boolean isOwner_ = false;
  /**
   * <code>optional bool isOwner = 7;</code>
   * @return Whether the isOwner field is set.
   */
  @java.lang.Override
  public boolean hasIsOwner() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>optional bool isOwner = 7;</code>
   * @return The isOwner.
   */
  @java.lang.Override
  public boolean getIsOwner() {
    return isOwner_;
  }

  public static final int DRAWDATA_FIELD_NUMBER = 8;
  private xddq.pb.YardDrawDataMsg drawData_;
  /**
   * <code>optional .xddq.pb.YardDrawDataMsg drawData = 8;</code>
   * @return Whether the drawData field is set.
   */
  @java.lang.Override
  public boolean hasDrawData() {
    return ((bitField0_ & 0x00000020) != 0);
  }
  /**
   * <code>optional .xddq.pb.YardDrawDataMsg drawData = 8;</code>
   * @return The drawData.
   */
  @java.lang.Override
  public xddq.pb.YardDrawDataMsg getDrawData() {
    return drawData_ == null ? xddq.pb.YardDrawDataMsg.getDefaultInstance() : drawData_;
  }
  /**
   * <code>optional .xddq.pb.YardDrawDataMsg drawData = 8;</code>
   */
  @java.lang.Override
  public xddq.pb.YardDrawDataMsgOrBuilder getDrawDataOrBuilder() {
    return drawData_ == null ? xddq.pb.YardDrawDataMsg.getDefaultInstance() : drawData_;
  }

  public static final int ISPEACHHELP_FIELD_NUMBER = 9;
  private boolean isPeachHelp_ = false;
  /**
   * <code>optional bool isPeachHelp = 9;</code>
   * @return Whether the isPeachHelp field is set.
   */
  @java.lang.Override
  public boolean hasIsPeachHelp() {
    return ((bitField0_ & 0x00000040) != 0);
  }
  /**
   * <code>optional bool isPeachHelp = 9;</code>
   * @return The isPeachHelp.
   */
  @java.lang.Override
  public boolean getIsPeachHelp() {
    return isPeachHelp_;
  }

  public static final int HELPDATA_FIELD_NUMBER = 10;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.HelpData> helpData_;
  /**
   * <code>repeated .xddq.pb.HelpData helpData = 10;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.HelpData> getHelpDataList() {
    return helpData_;
  }
  /**
   * <code>repeated .xddq.pb.HelpData helpData = 10;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.HelpDataOrBuilder> 
      getHelpDataOrBuilderList() {
    return helpData_;
  }
  /**
   * <code>repeated .xddq.pb.HelpData helpData = 10;</code>
   */
  @java.lang.Override
  public int getHelpDataCount() {
    return helpData_.size();
  }
  /**
   * <code>repeated .xddq.pb.HelpData helpData = 10;</code>
   */
  @java.lang.Override
  public xddq.pb.HelpData getHelpData(int index) {
    return helpData_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.HelpData helpData = 10;</code>
   */
  @java.lang.Override
  public xddq.pb.HelpDataOrBuilder getHelpDataOrBuilder(
      int index) {
    return helpData_.get(index);
  }

  public static final int COMBINELIST_FIELD_NUMBER = 11;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.YardSkillLCombineMsg> combineList_;
  /**
   * <code>repeated .xddq.pb.YardSkillLCombineMsg combineList = 11;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.YardSkillLCombineMsg> getCombineListList() {
    return combineList_;
  }
  /**
   * <code>repeated .xddq.pb.YardSkillLCombineMsg combineList = 11;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.YardSkillLCombineMsgOrBuilder> 
      getCombineListOrBuilderList() {
    return combineList_;
  }
  /**
   * <code>repeated .xddq.pb.YardSkillLCombineMsg combineList = 11;</code>
   */
  @java.lang.Override
  public int getCombineListCount() {
    return combineList_.size();
  }
  /**
   * <code>repeated .xddq.pb.YardSkillLCombineMsg combineList = 11;</code>
   */
  @java.lang.Override
  public xddq.pb.YardSkillLCombineMsg getCombineList(int index) {
    return combineList_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.YardSkillLCombineMsg combineList = 11;</code>
   */
  @java.lang.Override
  public xddq.pb.YardSkillLCombineMsgOrBuilder getCombineListOrBuilder(
      int index) {
    return combineList_.get(index);
  }

  public static final int DECORATELIST_FIELD_NUMBER = 12;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.YardBuildInfoMsg> decorateList_;
  /**
   * <code>repeated .xddq.pb.YardBuildInfoMsg decorateList = 12;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.YardBuildInfoMsg> getDecorateListList() {
    return decorateList_;
  }
  /**
   * <code>repeated .xddq.pb.YardBuildInfoMsg decorateList = 12;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.YardBuildInfoMsgOrBuilder> 
      getDecorateListOrBuilderList() {
    return decorateList_;
  }
  /**
   * <code>repeated .xddq.pb.YardBuildInfoMsg decorateList = 12;</code>
   */
  @java.lang.Override
  public int getDecorateListCount() {
    return decorateList_.size();
  }
  /**
   * <code>repeated .xddq.pb.YardBuildInfoMsg decorateList = 12;</code>
   */
  @java.lang.Override
  public xddq.pb.YardBuildInfoMsg getDecorateList(int index) {
    return decorateList_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.YardBuildInfoMsg decorateList = 12;</code>
   */
  @java.lang.Override
  public xddq.pb.YardBuildInfoMsgOrBuilder getDecorateListOrBuilder(
      int index) {
    return decorateList_.get(index);
  }

  public static final int YESTERDAYHELPNUM_FIELD_NUMBER = 13;
  private int yesterDayHelpNum_ = 0;
  /**
   * <code>optional int32 yesterDayHelpNum = 13;</code>
   * @return Whether the yesterDayHelpNum field is set.
   */
  @java.lang.Override
  public boolean hasYesterDayHelpNum() {
    return ((bitField0_ & 0x00000080) != 0);
  }
  /**
   * <code>optional int32 yesterDayHelpNum = 13;</code>
   * @return The yesterDayHelpNum.
   */
  @java.lang.Override
  public int getYesterDayHelpNum() {
    return yesterDayHelpNum_;
  }

  public static final int TIMEBUILDLIST_FIELD_NUMBER = 14;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.YardLimitTimeBuildMsg> timeBuildList_;
  /**
   * <code>repeated .xddq.pb.YardLimitTimeBuildMsg timeBuildList = 14;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.YardLimitTimeBuildMsg> getTimeBuildListList() {
    return timeBuildList_;
  }
  /**
   * <code>repeated .xddq.pb.YardLimitTimeBuildMsg timeBuildList = 14;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.YardLimitTimeBuildMsgOrBuilder> 
      getTimeBuildListOrBuilderList() {
    return timeBuildList_;
  }
  /**
   * <code>repeated .xddq.pb.YardLimitTimeBuildMsg timeBuildList = 14;</code>
   */
  @java.lang.Override
  public int getTimeBuildListCount() {
    return timeBuildList_.size();
  }
  /**
   * <code>repeated .xddq.pb.YardLimitTimeBuildMsg timeBuildList = 14;</code>
   */
  @java.lang.Override
  public xddq.pb.YardLimitTimeBuildMsg getTimeBuildList(int index) {
    return timeBuildList_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.YardLimitTimeBuildMsg timeBuildList = 14;</code>
   */
  @java.lang.Override
  public xddq.pb.YardLimitTimeBuildMsgOrBuilder getTimeBuildListOrBuilder(
      int index) {
    return timeBuildList_.get(index);
  }

  public static final int PLAYERSHOW_FIELD_NUMBER = 15;
  private xddq.pb.PlayerPilotShowDataMsg playerShow_;
  /**
   * <code>optional .xddq.pb.PlayerPilotShowDataMsg playerShow = 15;</code>
   * @return Whether the playerShow field is set.
   */
  @java.lang.Override
  public boolean hasPlayerShow() {
    return ((bitField0_ & 0x00000100) != 0);
  }
  /**
   * <code>optional .xddq.pb.PlayerPilotShowDataMsg playerShow = 15;</code>
   * @return The playerShow.
   */
  @java.lang.Override
  public xddq.pb.PlayerPilotShowDataMsg getPlayerShow() {
    return playerShow_ == null ? xddq.pb.PlayerPilotShowDataMsg.getDefaultInstance() : playerShow_;
  }
  /**
   * <code>optional .xddq.pb.PlayerPilotShowDataMsg playerShow = 15;</code>
   */
  @java.lang.Override
  public xddq.pb.PlayerPilotShowDataMsgOrBuilder getPlayerShowOrBuilder() {
    return playerShow_ == null ? xddq.pb.PlayerPilotShowDataMsg.getDefaultInstance() : playerShow_;
  }

  public static final int MAINBUILDINFO_FIELD_NUMBER = 16;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.YardMainBuildInfoMsg> mainBuildInfo_;
  /**
   * <code>repeated .xddq.pb.YardMainBuildInfoMsg mainBuildInfo = 16;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.YardMainBuildInfoMsg> getMainBuildInfoList() {
    return mainBuildInfo_;
  }
  /**
   * <code>repeated .xddq.pb.YardMainBuildInfoMsg mainBuildInfo = 16;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.YardMainBuildInfoMsgOrBuilder> 
      getMainBuildInfoOrBuilderList() {
    return mainBuildInfo_;
  }
  /**
   * <code>repeated .xddq.pb.YardMainBuildInfoMsg mainBuildInfo = 16;</code>
   */
  @java.lang.Override
  public int getMainBuildInfoCount() {
    return mainBuildInfo_.size();
  }
  /**
   * <code>repeated .xddq.pb.YardMainBuildInfoMsg mainBuildInfo = 16;</code>
   */
  @java.lang.Override
  public xddq.pb.YardMainBuildInfoMsg getMainBuildInfo(int index) {
    return mainBuildInfo_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.YardMainBuildInfoMsg mainBuildInfo = 16;</code>
   */
  @java.lang.Override
  public xddq.pb.YardMainBuildInfoMsgOrBuilder getMainBuildInfoOrBuilder(
      int index) {
    return mainBuildInfo_.get(index);
  }

  public static final int SPEEDTIMES_FIELD_NUMBER = 17;
  private int speedTimes_ = 0;
  /**
   * <code>optional int32 speedTimes = 17;</code>
   * @return Whether the speedTimes field is set.
   */
  @java.lang.Override
  public boolean hasSpeedTimes() {
    return ((bitField0_ & 0x00000200) != 0);
  }
  /**
   * <code>optional int32 speedTimes = 17;</code>
   * @return The speedTimes.
   */
  @java.lang.Override
  public int getSpeedTimes() {
    return speedTimes_;
  }

  public static final int BANTIME_FIELD_NUMBER = 18;
  private long banTime_ = 0L;
  /**
   * <code>optional int64 banTime = 18;</code>
   * @return Whether the banTime field is set.
   */
  @java.lang.Override
  public boolean hasBanTime() {
    return ((bitField0_ & 0x00000400) != 0);
  }
  /**
   * <code>optional int64 banTime = 18;</code>
   * @return The banTime.
   */
  @java.lang.Override
  public long getBanTime() {
    return banTime_;
  }

  public static final int GIVENUM_FIELD_NUMBER = 19;
  private int giveNum_ = 0;
  /**
   * <code>optional int32 giveNum = 19;</code>
   * @return Whether the giveNum field is set.
   */
  @java.lang.Override
  public boolean hasGiveNum() {
    return ((bitField0_ & 0x00000800) != 0);
  }
  /**
   * <code>optional int32 giveNum = 19;</code>
   * @return The giveNum.
   */
  @java.lang.Override
  public int getGiveNum() {
    return giveNum_;
  }

  public static final int OTHERDATA_FIELD_NUMBER = 20;
  private xddq.pb.YardOtherData otherData_;
  /**
   * <code>optional .xddq.pb.YardOtherData otherData = 20;</code>
   * @return Whether the otherData field is set.
   */
  @java.lang.Override
  public boolean hasOtherData() {
    return ((bitField0_ & 0x00001000) != 0);
  }
  /**
   * <code>optional .xddq.pb.YardOtherData otherData = 20;</code>
   * @return The otherData.
   */
  @java.lang.Override
  public xddq.pb.YardOtherData getOtherData() {
    return otherData_ == null ? xddq.pb.YardOtherData.getDefaultInstance() : otherData_;
  }
  /**
   * <code>optional .xddq.pb.YardOtherData otherData = 20;</code>
   */
  @java.lang.Override
  public xddq.pb.YardOtherDataOrBuilder getOtherDataOrBuilder() {
    return otherData_ == null ? xddq.pb.YardOtherData.getDefaultInstance() : otherData_;
  }

  public static final int ISCONFIRM_FIELD_NUMBER = 21;
  private boolean isConfirm_ = false;
  /**
   * <code>optional bool isConfirm = 21;</code>
   * @return Whether the isConfirm field is set.
   */
  @java.lang.Override
  public boolean hasIsConfirm() {
    return ((bitField0_ & 0x00002000) != 0);
  }
  /**
   * <code>optional bool isConfirm = 21;</code>
   * @return The isConfirm.
   */
  @java.lang.Override
  public boolean getIsConfirm() {
    return isConfirm_;
  }

  public static final int CONVERTPIECEVEC_FIELD_NUMBER = 22;
  @SuppressWarnings("serial")
  private volatile java.lang.Object convertPieceVec_ = "";
  /**
   * <code>optional string convertPieceVec = 22;</code>
   * @return Whether the convertPieceVec field is set.
   */
  @java.lang.Override
  public boolean hasConvertPieceVec() {
    return ((bitField0_ & 0x00004000) != 0);
  }
  /**
   * <code>optional string convertPieceVec = 22;</code>
   * @return The convertPieceVec.
   */
  @java.lang.Override
  public java.lang.String getConvertPieceVec() {
    java.lang.Object ref = convertPieceVec_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        convertPieceVec_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string convertPieceVec = 22;</code>
   * @return The bytes for convertPieceVec.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getConvertPieceVecBytes() {
    java.lang.Object ref = convertPieceVec_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      convertPieceVec_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    for (int i = 0; i < getCombineListCount(); i++) {
      if (!getCombineList(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    for (int i = 0; i < getDecorateListCount(); i++) {
      if (!getDecorateList(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    for (int i = 0; i < areaInfo_.size(); i++) {
      output.writeMessage(2, areaInfo_.get(i));
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(4, level_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 5, exp_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt32(6, queueNum_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      output.writeBool(7, isOwner_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      output.writeMessage(8, getDrawData());
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      output.writeBool(9, isPeachHelp_);
    }
    for (int i = 0; i < helpData_.size(); i++) {
      output.writeMessage(10, helpData_.get(i));
    }
    for (int i = 0; i < combineList_.size(); i++) {
      output.writeMessage(11, combineList_.get(i));
    }
    for (int i = 0; i < decorateList_.size(); i++) {
      output.writeMessage(12, decorateList_.get(i));
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      output.writeInt32(13, yesterDayHelpNum_);
    }
    for (int i = 0; i < timeBuildList_.size(); i++) {
      output.writeMessage(14, timeBuildList_.get(i));
    }
    if (((bitField0_ & 0x00000100) != 0)) {
      output.writeMessage(15, getPlayerShow());
    }
    for (int i = 0; i < mainBuildInfo_.size(); i++) {
      output.writeMessage(16, mainBuildInfo_.get(i));
    }
    if (((bitField0_ & 0x00000200) != 0)) {
      output.writeInt32(17, speedTimes_);
    }
    if (((bitField0_ & 0x00000400) != 0)) {
      output.writeInt64(18, banTime_);
    }
    if (((bitField0_ & 0x00000800) != 0)) {
      output.writeInt32(19, giveNum_);
    }
    if (((bitField0_ & 0x00001000) != 0)) {
      output.writeMessage(20, getOtherData());
    }
    if (((bitField0_ & 0x00002000) != 0)) {
      output.writeBool(21, isConfirm_);
    }
    if (((bitField0_ & 0x00004000) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 22, convertPieceVec_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    for (int i = 0; i < areaInfo_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, areaInfo_.get(i));
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(4, level_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(5, exp_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(6, queueNum_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(7, isOwner_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(8, getDrawData());
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(9, isPeachHelp_);
    }
    for (int i = 0; i < helpData_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(10, helpData_.get(i));
    }
    for (int i = 0; i < combineList_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(11, combineList_.get(i));
    }
    for (int i = 0; i < decorateList_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(12, decorateList_.get(i));
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(13, yesterDayHelpNum_);
    }
    for (int i = 0; i < timeBuildList_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(14, timeBuildList_.get(i));
    }
    if (((bitField0_ & 0x00000100) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(15, getPlayerShow());
    }
    for (int i = 0; i < mainBuildInfo_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(16, mainBuildInfo_.get(i));
    }
    if (((bitField0_ & 0x00000200) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(17, speedTimes_);
    }
    if (((bitField0_ & 0x00000400) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(18, banTime_);
    }
    if (((bitField0_ & 0x00000800) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(19, giveNum_);
    }
    if (((bitField0_ & 0x00001000) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(20, getOtherData());
    }
    if (((bitField0_ & 0x00002000) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(21, isConfirm_);
    }
    if (((bitField0_ & 0x00004000) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(22, convertPieceVec_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.YardEnterResp)) {
      return super.equals(obj);
    }
    xddq.pb.YardEnterResp other = (xddq.pb.YardEnterResp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (!getAreaInfoList()
        .equals(other.getAreaInfoList())) return false;
    if (hasLevel() != other.hasLevel()) return false;
    if (hasLevel()) {
      if (getLevel()
          != other.getLevel()) return false;
    }
    if (hasExp() != other.hasExp()) return false;
    if (hasExp()) {
      if (!getExp()
          .equals(other.getExp())) return false;
    }
    if (hasQueueNum() != other.hasQueueNum()) return false;
    if (hasQueueNum()) {
      if (getQueueNum()
          != other.getQueueNum()) return false;
    }
    if (hasIsOwner() != other.hasIsOwner()) return false;
    if (hasIsOwner()) {
      if (getIsOwner()
          != other.getIsOwner()) return false;
    }
    if (hasDrawData() != other.hasDrawData()) return false;
    if (hasDrawData()) {
      if (!getDrawData()
          .equals(other.getDrawData())) return false;
    }
    if (hasIsPeachHelp() != other.hasIsPeachHelp()) return false;
    if (hasIsPeachHelp()) {
      if (getIsPeachHelp()
          != other.getIsPeachHelp()) return false;
    }
    if (!getHelpDataList()
        .equals(other.getHelpDataList())) return false;
    if (!getCombineListList()
        .equals(other.getCombineListList())) return false;
    if (!getDecorateListList()
        .equals(other.getDecorateListList())) return false;
    if (hasYesterDayHelpNum() != other.hasYesterDayHelpNum()) return false;
    if (hasYesterDayHelpNum()) {
      if (getYesterDayHelpNum()
          != other.getYesterDayHelpNum()) return false;
    }
    if (!getTimeBuildListList()
        .equals(other.getTimeBuildListList())) return false;
    if (hasPlayerShow() != other.hasPlayerShow()) return false;
    if (hasPlayerShow()) {
      if (!getPlayerShow()
          .equals(other.getPlayerShow())) return false;
    }
    if (!getMainBuildInfoList()
        .equals(other.getMainBuildInfoList())) return false;
    if (hasSpeedTimes() != other.hasSpeedTimes()) return false;
    if (hasSpeedTimes()) {
      if (getSpeedTimes()
          != other.getSpeedTimes()) return false;
    }
    if (hasBanTime() != other.hasBanTime()) return false;
    if (hasBanTime()) {
      if (getBanTime()
          != other.getBanTime()) return false;
    }
    if (hasGiveNum() != other.hasGiveNum()) return false;
    if (hasGiveNum()) {
      if (getGiveNum()
          != other.getGiveNum()) return false;
    }
    if (hasOtherData() != other.hasOtherData()) return false;
    if (hasOtherData()) {
      if (!getOtherData()
          .equals(other.getOtherData())) return false;
    }
    if (hasIsConfirm() != other.hasIsConfirm()) return false;
    if (hasIsConfirm()) {
      if (getIsConfirm()
          != other.getIsConfirm()) return false;
    }
    if (hasConvertPieceVec() != other.hasConvertPieceVec()) return false;
    if (hasConvertPieceVec()) {
      if (!getConvertPieceVec()
          .equals(other.getConvertPieceVec())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (getAreaInfoCount() > 0) {
      hash = (37 * hash) + AREAINFO_FIELD_NUMBER;
      hash = (53 * hash) + getAreaInfoList().hashCode();
    }
    if (hasLevel()) {
      hash = (37 * hash) + LEVEL_FIELD_NUMBER;
      hash = (53 * hash) + getLevel();
    }
    if (hasExp()) {
      hash = (37 * hash) + EXP_FIELD_NUMBER;
      hash = (53 * hash) + getExp().hashCode();
    }
    if (hasQueueNum()) {
      hash = (37 * hash) + QUEUENUM_FIELD_NUMBER;
      hash = (53 * hash) + getQueueNum();
    }
    if (hasIsOwner()) {
      hash = (37 * hash) + ISOWNER_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getIsOwner());
    }
    if (hasDrawData()) {
      hash = (37 * hash) + DRAWDATA_FIELD_NUMBER;
      hash = (53 * hash) + getDrawData().hashCode();
    }
    if (hasIsPeachHelp()) {
      hash = (37 * hash) + ISPEACHHELP_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getIsPeachHelp());
    }
    if (getHelpDataCount() > 0) {
      hash = (37 * hash) + HELPDATA_FIELD_NUMBER;
      hash = (53 * hash) + getHelpDataList().hashCode();
    }
    if (getCombineListCount() > 0) {
      hash = (37 * hash) + COMBINELIST_FIELD_NUMBER;
      hash = (53 * hash) + getCombineListList().hashCode();
    }
    if (getDecorateListCount() > 0) {
      hash = (37 * hash) + DECORATELIST_FIELD_NUMBER;
      hash = (53 * hash) + getDecorateListList().hashCode();
    }
    if (hasYesterDayHelpNum()) {
      hash = (37 * hash) + YESTERDAYHELPNUM_FIELD_NUMBER;
      hash = (53 * hash) + getYesterDayHelpNum();
    }
    if (getTimeBuildListCount() > 0) {
      hash = (37 * hash) + TIMEBUILDLIST_FIELD_NUMBER;
      hash = (53 * hash) + getTimeBuildListList().hashCode();
    }
    if (hasPlayerShow()) {
      hash = (37 * hash) + PLAYERSHOW_FIELD_NUMBER;
      hash = (53 * hash) + getPlayerShow().hashCode();
    }
    if (getMainBuildInfoCount() > 0) {
      hash = (37 * hash) + MAINBUILDINFO_FIELD_NUMBER;
      hash = (53 * hash) + getMainBuildInfoList().hashCode();
    }
    if (hasSpeedTimes()) {
      hash = (37 * hash) + SPEEDTIMES_FIELD_NUMBER;
      hash = (53 * hash) + getSpeedTimes();
    }
    if (hasBanTime()) {
      hash = (37 * hash) + BANTIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getBanTime());
    }
    if (hasGiveNum()) {
      hash = (37 * hash) + GIVENUM_FIELD_NUMBER;
      hash = (53 * hash) + getGiveNum();
    }
    if (hasOtherData()) {
      hash = (37 * hash) + OTHERDATA_FIELD_NUMBER;
      hash = (53 * hash) + getOtherData().hashCode();
    }
    if (hasIsConfirm()) {
      hash = (37 * hash) + ISCONFIRM_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getIsConfirm());
    }
    if (hasConvertPieceVec()) {
      hash = (37 * hash) + CONVERTPIECEVEC_FIELD_NUMBER;
      hash = (53 * hash) + getConvertPieceVec().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.YardEnterResp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.YardEnterResp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.YardEnterResp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.YardEnterResp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.YardEnterResp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.YardEnterResp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.YardEnterResp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.YardEnterResp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.YardEnterResp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.YardEnterResp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.YardEnterResp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.YardEnterResp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.YardEnterResp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.YardEnterResp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.YardEnterResp)
      xddq.pb.YardEnterRespOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_YardEnterResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_YardEnterResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.YardEnterResp.class, xddq.pb.YardEnterResp.Builder.class);
    }

    // Construct using xddq.pb.YardEnterResp.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetAreaInfoFieldBuilder();
        internalGetDrawDataFieldBuilder();
        internalGetHelpDataFieldBuilder();
        internalGetCombineListFieldBuilder();
        internalGetDecorateListFieldBuilder();
        internalGetTimeBuildListFieldBuilder();
        internalGetPlayerShowFieldBuilder();
        internalGetMainBuildInfoFieldBuilder();
        internalGetOtherDataFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      if (areaInfoBuilder_ == null) {
        areaInfo_ = java.util.Collections.emptyList();
      } else {
        areaInfo_ = null;
        areaInfoBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000002);
      level_ = 0;
      exp_ = "";
      queueNum_ = 0;
      isOwner_ = false;
      drawData_ = null;
      if (drawDataBuilder_ != null) {
        drawDataBuilder_.dispose();
        drawDataBuilder_ = null;
      }
      isPeachHelp_ = false;
      if (helpDataBuilder_ == null) {
        helpData_ = java.util.Collections.emptyList();
      } else {
        helpData_ = null;
        helpDataBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000100);
      if (combineListBuilder_ == null) {
        combineList_ = java.util.Collections.emptyList();
      } else {
        combineList_ = null;
        combineListBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000200);
      if (decorateListBuilder_ == null) {
        decorateList_ = java.util.Collections.emptyList();
      } else {
        decorateList_ = null;
        decorateListBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000400);
      yesterDayHelpNum_ = 0;
      if (timeBuildListBuilder_ == null) {
        timeBuildList_ = java.util.Collections.emptyList();
      } else {
        timeBuildList_ = null;
        timeBuildListBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00001000);
      playerShow_ = null;
      if (playerShowBuilder_ != null) {
        playerShowBuilder_.dispose();
        playerShowBuilder_ = null;
      }
      if (mainBuildInfoBuilder_ == null) {
        mainBuildInfo_ = java.util.Collections.emptyList();
      } else {
        mainBuildInfo_ = null;
        mainBuildInfoBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00004000);
      speedTimes_ = 0;
      banTime_ = 0L;
      giveNum_ = 0;
      otherData_ = null;
      if (otherDataBuilder_ != null) {
        otherDataBuilder_.dispose();
        otherDataBuilder_ = null;
      }
      isConfirm_ = false;
      convertPieceVec_ = "";
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_YardEnterResp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.YardEnterResp getDefaultInstanceForType() {
      return xddq.pb.YardEnterResp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.YardEnterResp build() {
      xddq.pb.YardEnterResp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.YardEnterResp buildPartial() {
      xddq.pb.YardEnterResp result = new xddq.pb.YardEnterResp(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.YardEnterResp result) {
      if (areaInfoBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          areaInfo_ = java.util.Collections.unmodifiableList(areaInfo_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.areaInfo_ = areaInfo_;
      } else {
        result.areaInfo_ = areaInfoBuilder_.build();
      }
      if (helpDataBuilder_ == null) {
        if (((bitField0_ & 0x00000100) != 0)) {
          helpData_ = java.util.Collections.unmodifiableList(helpData_);
          bitField0_ = (bitField0_ & ~0x00000100);
        }
        result.helpData_ = helpData_;
      } else {
        result.helpData_ = helpDataBuilder_.build();
      }
      if (combineListBuilder_ == null) {
        if (((bitField0_ & 0x00000200) != 0)) {
          combineList_ = java.util.Collections.unmodifiableList(combineList_);
          bitField0_ = (bitField0_ & ~0x00000200);
        }
        result.combineList_ = combineList_;
      } else {
        result.combineList_ = combineListBuilder_.build();
      }
      if (decorateListBuilder_ == null) {
        if (((bitField0_ & 0x00000400) != 0)) {
          decorateList_ = java.util.Collections.unmodifiableList(decorateList_);
          bitField0_ = (bitField0_ & ~0x00000400);
        }
        result.decorateList_ = decorateList_;
      } else {
        result.decorateList_ = decorateListBuilder_.build();
      }
      if (timeBuildListBuilder_ == null) {
        if (((bitField0_ & 0x00001000) != 0)) {
          timeBuildList_ = java.util.Collections.unmodifiableList(timeBuildList_);
          bitField0_ = (bitField0_ & ~0x00001000);
        }
        result.timeBuildList_ = timeBuildList_;
      } else {
        result.timeBuildList_ = timeBuildListBuilder_.build();
      }
      if (mainBuildInfoBuilder_ == null) {
        if (((bitField0_ & 0x00004000) != 0)) {
          mainBuildInfo_ = java.util.Collections.unmodifiableList(mainBuildInfo_);
          bitField0_ = (bitField0_ & ~0x00004000);
        }
        result.mainBuildInfo_ = mainBuildInfo_;
      } else {
        result.mainBuildInfo_ = mainBuildInfoBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.YardEnterResp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.level_ = level_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.exp_ = exp_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.queueNum_ = queueNum_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.isOwner_ = isOwner_;
        to_bitField0_ |= 0x00000010;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.drawData_ = drawDataBuilder_ == null
            ? drawData_
            : drawDataBuilder_.build();
        to_bitField0_ |= 0x00000020;
      }
      if (((from_bitField0_ & 0x00000080) != 0)) {
        result.isPeachHelp_ = isPeachHelp_;
        to_bitField0_ |= 0x00000040;
      }
      if (((from_bitField0_ & 0x00000800) != 0)) {
        result.yesterDayHelpNum_ = yesterDayHelpNum_;
        to_bitField0_ |= 0x00000080;
      }
      if (((from_bitField0_ & 0x00002000) != 0)) {
        result.playerShow_ = playerShowBuilder_ == null
            ? playerShow_
            : playerShowBuilder_.build();
        to_bitField0_ |= 0x00000100;
      }
      if (((from_bitField0_ & 0x00008000) != 0)) {
        result.speedTimes_ = speedTimes_;
        to_bitField0_ |= 0x00000200;
      }
      if (((from_bitField0_ & 0x00010000) != 0)) {
        result.banTime_ = banTime_;
        to_bitField0_ |= 0x00000400;
      }
      if (((from_bitField0_ & 0x00020000) != 0)) {
        result.giveNum_ = giveNum_;
        to_bitField0_ |= 0x00000800;
      }
      if (((from_bitField0_ & 0x00040000) != 0)) {
        result.otherData_ = otherDataBuilder_ == null
            ? otherData_
            : otherDataBuilder_.build();
        to_bitField0_ |= 0x00001000;
      }
      if (((from_bitField0_ & 0x00080000) != 0)) {
        result.isConfirm_ = isConfirm_;
        to_bitField0_ |= 0x00002000;
      }
      if (((from_bitField0_ & 0x00100000) != 0)) {
        result.convertPieceVec_ = convertPieceVec_;
        to_bitField0_ |= 0x00004000;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.YardEnterResp) {
        return mergeFrom((xddq.pb.YardEnterResp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.YardEnterResp other) {
      if (other == xddq.pb.YardEnterResp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (areaInfoBuilder_ == null) {
        if (!other.areaInfo_.isEmpty()) {
          if (areaInfo_.isEmpty()) {
            areaInfo_ = other.areaInfo_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureAreaInfoIsMutable();
            areaInfo_.addAll(other.areaInfo_);
          }
          onChanged();
        }
      } else {
        if (!other.areaInfo_.isEmpty()) {
          if (areaInfoBuilder_.isEmpty()) {
            areaInfoBuilder_.dispose();
            areaInfoBuilder_ = null;
            areaInfo_ = other.areaInfo_;
            bitField0_ = (bitField0_ & ~0x00000002);
            areaInfoBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetAreaInfoFieldBuilder() : null;
          } else {
            areaInfoBuilder_.addAllMessages(other.areaInfo_);
          }
        }
      }
      if (other.hasLevel()) {
        setLevel(other.getLevel());
      }
      if (other.hasExp()) {
        exp_ = other.exp_;
        bitField0_ |= 0x00000008;
        onChanged();
      }
      if (other.hasQueueNum()) {
        setQueueNum(other.getQueueNum());
      }
      if (other.hasIsOwner()) {
        setIsOwner(other.getIsOwner());
      }
      if (other.hasDrawData()) {
        mergeDrawData(other.getDrawData());
      }
      if (other.hasIsPeachHelp()) {
        setIsPeachHelp(other.getIsPeachHelp());
      }
      if (helpDataBuilder_ == null) {
        if (!other.helpData_.isEmpty()) {
          if (helpData_.isEmpty()) {
            helpData_ = other.helpData_;
            bitField0_ = (bitField0_ & ~0x00000100);
          } else {
            ensureHelpDataIsMutable();
            helpData_.addAll(other.helpData_);
          }
          onChanged();
        }
      } else {
        if (!other.helpData_.isEmpty()) {
          if (helpDataBuilder_.isEmpty()) {
            helpDataBuilder_.dispose();
            helpDataBuilder_ = null;
            helpData_ = other.helpData_;
            bitField0_ = (bitField0_ & ~0x00000100);
            helpDataBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetHelpDataFieldBuilder() : null;
          } else {
            helpDataBuilder_.addAllMessages(other.helpData_);
          }
        }
      }
      if (combineListBuilder_ == null) {
        if (!other.combineList_.isEmpty()) {
          if (combineList_.isEmpty()) {
            combineList_ = other.combineList_;
            bitField0_ = (bitField0_ & ~0x00000200);
          } else {
            ensureCombineListIsMutable();
            combineList_.addAll(other.combineList_);
          }
          onChanged();
        }
      } else {
        if (!other.combineList_.isEmpty()) {
          if (combineListBuilder_.isEmpty()) {
            combineListBuilder_.dispose();
            combineListBuilder_ = null;
            combineList_ = other.combineList_;
            bitField0_ = (bitField0_ & ~0x00000200);
            combineListBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetCombineListFieldBuilder() : null;
          } else {
            combineListBuilder_.addAllMessages(other.combineList_);
          }
        }
      }
      if (decorateListBuilder_ == null) {
        if (!other.decorateList_.isEmpty()) {
          if (decorateList_.isEmpty()) {
            decorateList_ = other.decorateList_;
            bitField0_ = (bitField0_ & ~0x00000400);
          } else {
            ensureDecorateListIsMutable();
            decorateList_.addAll(other.decorateList_);
          }
          onChanged();
        }
      } else {
        if (!other.decorateList_.isEmpty()) {
          if (decorateListBuilder_.isEmpty()) {
            decorateListBuilder_.dispose();
            decorateListBuilder_ = null;
            decorateList_ = other.decorateList_;
            bitField0_ = (bitField0_ & ~0x00000400);
            decorateListBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetDecorateListFieldBuilder() : null;
          } else {
            decorateListBuilder_.addAllMessages(other.decorateList_);
          }
        }
      }
      if (other.hasYesterDayHelpNum()) {
        setYesterDayHelpNum(other.getYesterDayHelpNum());
      }
      if (timeBuildListBuilder_ == null) {
        if (!other.timeBuildList_.isEmpty()) {
          if (timeBuildList_.isEmpty()) {
            timeBuildList_ = other.timeBuildList_;
            bitField0_ = (bitField0_ & ~0x00001000);
          } else {
            ensureTimeBuildListIsMutable();
            timeBuildList_.addAll(other.timeBuildList_);
          }
          onChanged();
        }
      } else {
        if (!other.timeBuildList_.isEmpty()) {
          if (timeBuildListBuilder_.isEmpty()) {
            timeBuildListBuilder_.dispose();
            timeBuildListBuilder_ = null;
            timeBuildList_ = other.timeBuildList_;
            bitField0_ = (bitField0_ & ~0x00001000);
            timeBuildListBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetTimeBuildListFieldBuilder() : null;
          } else {
            timeBuildListBuilder_.addAllMessages(other.timeBuildList_);
          }
        }
      }
      if (other.hasPlayerShow()) {
        mergePlayerShow(other.getPlayerShow());
      }
      if (mainBuildInfoBuilder_ == null) {
        if (!other.mainBuildInfo_.isEmpty()) {
          if (mainBuildInfo_.isEmpty()) {
            mainBuildInfo_ = other.mainBuildInfo_;
            bitField0_ = (bitField0_ & ~0x00004000);
          } else {
            ensureMainBuildInfoIsMutable();
            mainBuildInfo_.addAll(other.mainBuildInfo_);
          }
          onChanged();
        }
      } else {
        if (!other.mainBuildInfo_.isEmpty()) {
          if (mainBuildInfoBuilder_.isEmpty()) {
            mainBuildInfoBuilder_.dispose();
            mainBuildInfoBuilder_ = null;
            mainBuildInfo_ = other.mainBuildInfo_;
            bitField0_ = (bitField0_ & ~0x00004000);
            mainBuildInfoBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetMainBuildInfoFieldBuilder() : null;
          } else {
            mainBuildInfoBuilder_.addAllMessages(other.mainBuildInfo_);
          }
        }
      }
      if (other.hasSpeedTimes()) {
        setSpeedTimes(other.getSpeedTimes());
      }
      if (other.hasBanTime()) {
        setBanTime(other.getBanTime());
      }
      if (other.hasGiveNum()) {
        setGiveNum(other.getGiveNum());
      }
      if (other.hasOtherData()) {
        mergeOtherData(other.getOtherData());
      }
      if (other.hasIsConfirm()) {
        setIsConfirm(other.getIsConfirm());
      }
      if (other.hasConvertPieceVec()) {
        convertPieceVec_ = other.convertPieceVec_;
        bitField0_ |= 0x00100000;
        onChanged();
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      for (int i = 0; i < getCombineListCount(); i++) {
        if (!getCombineList(i).isInitialized()) {
          return false;
        }
      }
      for (int i = 0; i < getDecorateListCount(); i++) {
        if (!getDecorateList(i).isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              xddq.pb.YardAreaInfoMsg m =
                  input.readMessage(
                      xddq.pb.YardAreaInfoMsg.parser(),
                      extensionRegistry);
              if (areaInfoBuilder_ == null) {
                ensureAreaInfoIsMutable();
                areaInfo_.add(m);
              } else {
                areaInfoBuilder_.addMessage(m);
              }
              break;
            } // case 18
            case 32: {
              level_ = input.readInt32();
              bitField0_ |= 0x00000004;
              break;
            } // case 32
            case 42: {
              exp_ = input.readBytes();
              bitField0_ |= 0x00000008;
              break;
            } // case 42
            case 48: {
              queueNum_ = input.readInt32();
              bitField0_ |= 0x00000010;
              break;
            } // case 48
            case 56: {
              isOwner_ = input.readBool();
              bitField0_ |= 0x00000020;
              break;
            } // case 56
            case 66: {
              input.readMessage(
                  internalGetDrawDataFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000040;
              break;
            } // case 66
            case 72: {
              isPeachHelp_ = input.readBool();
              bitField0_ |= 0x00000080;
              break;
            } // case 72
            case 82: {
              xddq.pb.HelpData m =
                  input.readMessage(
                      xddq.pb.HelpData.parser(),
                      extensionRegistry);
              if (helpDataBuilder_ == null) {
                ensureHelpDataIsMutable();
                helpData_.add(m);
              } else {
                helpDataBuilder_.addMessage(m);
              }
              break;
            } // case 82
            case 90: {
              xddq.pb.YardSkillLCombineMsg m =
                  input.readMessage(
                      xddq.pb.YardSkillLCombineMsg.parser(),
                      extensionRegistry);
              if (combineListBuilder_ == null) {
                ensureCombineListIsMutable();
                combineList_.add(m);
              } else {
                combineListBuilder_.addMessage(m);
              }
              break;
            } // case 90
            case 98: {
              xddq.pb.YardBuildInfoMsg m =
                  input.readMessage(
                      xddq.pb.YardBuildInfoMsg.parser(),
                      extensionRegistry);
              if (decorateListBuilder_ == null) {
                ensureDecorateListIsMutable();
                decorateList_.add(m);
              } else {
                decorateListBuilder_.addMessage(m);
              }
              break;
            } // case 98
            case 104: {
              yesterDayHelpNum_ = input.readInt32();
              bitField0_ |= 0x00000800;
              break;
            } // case 104
            case 114: {
              xddq.pb.YardLimitTimeBuildMsg m =
                  input.readMessage(
                      xddq.pb.YardLimitTimeBuildMsg.parser(),
                      extensionRegistry);
              if (timeBuildListBuilder_ == null) {
                ensureTimeBuildListIsMutable();
                timeBuildList_.add(m);
              } else {
                timeBuildListBuilder_.addMessage(m);
              }
              break;
            } // case 114
            case 122: {
              input.readMessage(
                  internalGetPlayerShowFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00002000;
              break;
            } // case 122
            case 130: {
              xddq.pb.YardMainBuildInfoMsg m =
                  input.readMessage(
                      xddq.pb.YardMainBuildInfoMsg.parser(),
                      extensionRegistry);
              if (mainBuildInfoBuilder_ == null) {
                ensureMainBuildInfoIsMutable();
                mainBuildInfo_.add(m);
              } else {
                mainBuildInfoBuilder_.addMessage(m);
              }
              break;
            } // case 130
            case 136: {
              speedTimes_ = input.readInt32();
              bitField0_ |= 0x00008000;
              break;
            } // case 136
            case 144: {
              banTime_ = input.readInt64();
              bitField0_ |= 0x00010000;
              break;
            } // case 144
            case 152: {
              giveNum_ = input.readInt32();
              bitField0_ |= 0x00020000;
              break;
            } // case 152
            case 162: {
              input.readMessage(
                  internalGetOtherDataFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00040000;
              break;
            } // case 162
            case 168: {
              isConfirm_ = input.readBool();
              bitField0_ |= 0x00080000;
              break;
            } // case 168
            case 178: {
              convertPieceVec_ = input.readBytes();
              bitField0_ |= 0x00100000;
              break;
            } // case 178
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.YardAreaInfoMsg> areaInfo_ =
      java.util.Collections.emptyList();
    private void ensureAreaInfoIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        areaInfo_ = new java.util.ArrayList<xddq.pb.YardAreaInfoMsg>(areaInfo_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.YardAreaInfoMsg, xddq.pb.YardAreaInfoMsg.Builder, xddq.pb.YardAreaInfoMsgOrBuilder> areaInfoBuilder_;

    /**
     * <code>repeated .xddq.pb.YardAreaInfoMsg areaInfo = 2;</code>
     */
    public java.util.List<xddq.pb.YardAreaInfoMsg> getAreaInfoList() {
      if (areaInfoBuilder_ == null) {
        return java.util.Collections.unmodifiableList(areaInfo_);
      } else {
        return areaInfoBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.YardAreaInfoMsg areaInfo = 2;</code>
     */
    public int getAreaInfoCount() {
      if (areaInfoBuilder_ == null) {
        return areaInfo_.size();
      } else {
        return areaInfoBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.YardAreaInfoMsg areaInfo = 2;</code>
     */
    public xddq.pb.YardAreaInfoMsg getAreaInfo(int index) {
      if (areaInfoBuilder_ == null) {
        return areaInfo_.get(index);
      } else {
        return areaInfoBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.YardAreaInfoMsg areaInfo = 2;</code>
     */
    public Builder setAreaInfo(
        int index, xddq.pb.YardAreaInfoMsg value) {
      if (areaInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureAreaInfoIsMutable();
        areaInfo_.set(index, value);
        onChanged();
      } else {
        areaInfoBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.YardAreaInfoMsg areaInfo = 2;</code>
     */
    public Builder setAreaInfo(
        int index, xddq.pb.YardAreaInfoMsg.Builder builderForValue) {
      if (areaInfoBuilder_ == null) {
        ensureAreaInfoIsMutable();
        areaInfo_.set(index, builderForValue.build());
        onChanged();
      } else {
        areaInfoBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.YardAreaInfoMsg areaInfo = 2;</code>
     */
    public Builder addAreaInfo(xddq.pb.YardAreaInfoMsg value) {
      if (areaInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureAreaInfoIsMutable();
        areaInfo_.add(value);
        onChanged();
      } else {
        areaInfoBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.YardAreaInfoMsg areaInfo = 2;</code>
     */
    public Builder addAreaInfo(
        int index, xddq.pb.YardAreaInfoMsg value) {
      if (areaInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureAreaInfoIsMutable();
        areaInfo_.add(index, value);
        onChanged();
      } else {
        areaInfoBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.YardAreaInfoMsg areaInfo = 2;</code>
     */
    public Builder addAreaInfo(
        xddq.pb.YardAreaInfoMsg.Builder builderForValue) {
      if (areaInfoBuilder_ == null) {
        ensureAreaInfoIsMutable();
        areaInfo_.add(builderForValue.build());
        onChanged();
      } else {
        areaInfoBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.YardAreaInfoMsg areaInfo = 2;</code>
     */
    public Builder addAreaInfo(
        int index, xddq.pb.YardAreaInfoMsg.Builder builderForValue) {
      if (areaInfoBuilder_ == null) {
        ensureAreaInfoIsMutable();
        areaInfo_.add(index, builderForValue.build());
        onChanged();
      } else {
        areaInfoBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.YardAreaInfoMsg areaInfo = 2;</code>
     */
    public Builder addAllAreaInfo(
        java.lang.Iterable<? extends xddq.pb.YardAreaInfoMsg> values) {
      if (areaInfoBuilder_ == null) {
        ensureAreaInfoIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, areaInfo_);
        onChanged();
      } else {
        areaInfoBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.YardAreaInfoMsg areaInfo = 2;</code>
     */
    public Builder clearAreaInfo() {
      if (areaInfoBuilder_ == null) {
        areaInfo_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        areaInfoBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.YardAreaInfoMsg areaInfo = 2;</code>
     */
    public Builder removeAreaInfo(int index) {
      if (areaInfoBuilder_ == null) {
        ensureAreaInfoIsMutable();
        areaInfo_.remove(index);
        onChanged();
      } else {
        areaInfoBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.YardAreaInfoMsg areaInfo = 2;</code>
     */
    public xddq.pb.YardAreaInfoMsg.Builder getAreaInfoBuilder(
        int index) {
      return internalGetAreaInfoFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.YardAreaInfoMsg areaInfo = 2;</code>
     */
    public xddq.pb.YardAreaInfoMsgOrBuilder getAreaInfoOrBuilder(
        int index) {
      if (areaInfoBuilder_ == null) {
        return areaInfo_.get(index);  } else {
        return areaInfoBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.YardAreaInfoMsg areaInfo = 2;</code>
     */
    public java.util.List<? extends xddq.pb.YardAreaInfoMsgOrBuilder> 
         getAreaInfoOrBuilderList() {
      if (areaInfoBuilder_ != null) {
        return areaInfoBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(areaInfo_);
      }
    }
    /**
     * <code>repeated .xddq.pb.YardAreaInfoMsg areaInfo = 2;</code>
     */
    public xddq.pb.YardAreaInfoMsg.Builder addAreaInfoBuilder() {
      return internalGetAreaInfoFieldBuilder().addBuilder(
          xddq.pb.YardAreaInfoMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.YardAreaInfoMsg areaInfo = 2;</code>
     */
    public xddq.pb.YardAreaInfoMsg.Builder addAreaInfoBuilder(
        int index) {
      return internalGetAreaInfoFieldBuilder().addBuilder(
          index, xddq.pb.YardAreaInfoMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.YardAreaInfoMsg areaInfo = 2;</code>
     */
    public java.util.List<xddq.pb.YardAreaInfoMsg.Builder> 
         getAreaInfoBuilderList() {
      return internalGetAreaInfoFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.YardAreaInfoMsg, xddq.pb.YardAreaInfoMsg.Builder, xddq.pb.YardAreaInfoMsgOrBuilder> 
        internalGetAreaInfoFieldBuilder() {
      if (areaInfoBuilder_ == null) {
        areaInfoBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.YardAreaInfoMsg, xddq.pb.YardAreaInfoMsg.Builder, xddq.pb.YardAreaInfoMsgOrBuilder>(
                areaInfo_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        areaInfo_ = null;
      }
      return areaInfoBuilder_;
    }

    private int level_ ;
    /**
     * <code>optional int32 level = 4;</code>
     * @return Whether the level field is set.
     */
    @java.lang.Override
    public boolean hasLevel() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int32 level = 4;</code>
     * @return The level.
     */
    @java.lang.Override
    public int getLevel() {
      return level_;
    }
    /**
     * <code>optional int32 level = 4;</code>
     * @param value The level to set.
     * @return This builder for chaining.
     */
    public Builder setLevel(int value) {

      level_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 level = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearLevel() {
      bitField0_ = (bitField0_ & ~0x00000004);
      level_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object exp_ = "";
    /**
     * <code>optional string exp = 5;</code>
     * @return Whether the exp field is set.
     */
    public boolean hasExp() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional string exp = 5;</code>
     * @return The exp.
     */
    public java.lang.String getExp() {
      java.lang.Object ref = exp_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          exp_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string exp = 5;</code>
     * @return The bytes for exp.
     */
    public com.google.protobuf.ByteString
        getExpBytes() {
      java.lang.Object ref = exp_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        exp_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string exp = 5;</code>
     * @param value The exp to set.
     * @return This builder for chaining.
     */
    public Builder setExp(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      exp_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional string exp = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearExp() {
      exp_ = getDefaultInstance().getExp();
      bitField0_ = (bitField0_ & ~0x00000008);
      onChanged();
      return this;
    }
    /**
     * <code>optional string exp = 5;</code>
     * @param value The bytes for exp to set.
     * @return This builder for chaining.
     */
    public Builder setExpBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      exp_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }

    private int queueNum_ ;
    /**
     * <code>optional int32 queueNum = 6;</code>
     * @return Whether the queueNum field is set.
     */
    @java.lang.Override
    public boolean hasQueueNum() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional int32 queueNum = 6;</code>
     * @return The queueNum.
     */
    @java.lang.Override
    public int getQueueNum() {
      return queueNum_;
    }
    /**
     * <code>optional int32 queueNum = 6;</code>
     * @param value The queueNum to set.
     * @return This builder for chaining.
     */
    public Builder setQueueNum(int value) {

      queueNum_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 queueNum = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearQueueNum() {
      bitField0_ = (bitField0_ & ~0x00000010);
      queueNum_ = 0;
      onChanged();
      return this;
    }

    private boolean isOwner_ ;
    /**
     * <code>optional bool isOwner = 7;</code>
     * @return Whether the isOwner field is set.
     */
    @java.lang.Override
    public boolean hasIsOwner() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional bool isOwner = 7;</code>
     * @return The isOwner.
     */
    @java.lang.Override
    public boolean getIsOwner() {
      return isOwner_;
    }
    /**
     * <code>optional bool isOwner = 7;</code>
     * @param value The isOwner to set.
     * @return This builder for chaining.
     */
    public Builder setIsOwner(boolean value) {

      isOwner_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>optional bool isOwner = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearIsOwner() {
      bitField0_ = (bitField0_ & ~0x00000020);
      isOwner_ = false;
      onChanged();
      return this;
    }

    private xddq.pb.YardDrawDataMsg drawData_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.YardDrawDataMsg, xddq.pb.YardDrawDataMsg.Builder, xddq.pb.YardDrawDataMsgOrBuilder> drawDataBuilder_;
    /**
     * <code>optional .xddq.pb.YardDrawDataMsg drawData = 8;</code>
     * @return Whether the drawData field is set.
     */
    public boolean hasDrawData() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <code>optional .xddq.pb.YardDrawDataMsg drawData = 8;</code>
     * @return The drawData.
     */
    public xddq.pb.YardDrawDataMsg getDrawData() {
      if (drawDataBuilder_ == null) {
        return drawData_ == null ? xddq.pb.YardDrawDataMsg.getDefaultInstance() : drawData_;
      } else {
        return drawDataBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.YardDrawDataMsg drawData = 8;</code>
     */
    public Builder setDrawData(xddq.pb.YardDrawDataMsg value) {
      if (drawDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        drawData_ = value;
      } else {
        drawDataBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.YardDrawDataMsg drawData = 8;</code>
     */
    public Builder setDrawData(
        xddq.pb.YardDrawDataMsg.Builder builderForValue) {
      if (drawDataBuilder_ == null) {
        drawData_ = builderForValue.build();
      } else {
        drawDataBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.YardDrawDataMsg drawData = 8;</code>
     */
    public Builder mergeDrawData(xddq.pb.YardDrawDataMsg value) {
      if (drawDataBuilder_ == null) {
        if (((bitField0_ & 0x00000040) != 0) &&
          drawData_ != null &&
          drawData_ != xddq.pb.YardDrawDataMsg.getDefaultInstance()) {
          getDrawDataBuilder().mergeFrom(value);
        } else {
          drawData_ = value;
        }
      } else {
        drawDataBuilder_.mergeFrom(value);
      }
      if (drawData_ != null) {
        bitField0_ |= 0x00000040;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.YardDrawDataMsg drawData = 8;</code>
     */
    public Builder clearDrawData() {
      bitField0_ = (bitField0_ & ~0x00000040);
      drawData_ = null;
      if (drawDataBuilder_ != null) {
        drawDataBuilder_.dispose();
        drawDataBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.YardDrawDataMsg drawData = 8;</code>
     */
    public xddq.pb.YardDrawDataMsg.Builder getDrawDataBuilder() {
      bitField0_ |= 0x00000040;
      onChanged();
      return internalGetDrawDataFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.YardDrawDataMsg drawData = 8;</code>
     */
    public xddq.pb.YardDrawDataMsgOrBuilder getDrawDataOrBuilder() {
      if (drawDataBuilder_ != null) {
        return drawDataBuilder_.getMessageOrBuilder();
      } else {
        return drawData_ == null ?
            xddq.pb.YardDrawDataMsg.getDefaultInstance() : drawData_;
      }
    }
    /**
     * <code>optional .xddq.pb.YardDrawDataMsg drawData = 8;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.YardDrawDataMsg, xddq.pb.YardDrawDataMsg.Builder, xddq.pb.YardDrawDataMsgOrBuilder> 
        internalGetDrawDataFieldBuilder() {
      if (drawDataBuilder_ == null) {
        drawDataBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.YardDrawDataMsg, xddq.pb.YardDrawDataMsg.Builder, xddq.pb.YardDrawDataMsgOrBuilder>(
                getDrawData(),
                getParentForChildren(),
                isClean());
        drawData_ = null;
      }
      return drawDataBuilder_;
    }

    private boolean isPeachHelp_ ;
    /**
     * <code>optional bool isPeachHelp = 9;</code>
     * @return Whether the isPeachHelp field is set.
     */
    @java.lang.Override
    public boolean hasIsPeachHelp() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <code>optional bool isPeachHelp = 9;</code>
     * @return The isPeachHelp.
     */
    @java.lang.Override
    public boolean getIsPeachHelp() {
      return isPeachHelp_;
    }
    /**
     * <code>optional bool isPeachHelp = 9;</code>
     * @param value The isPeachHelp to set.
     * @return This builder for chaining.
     */
    public Builder setIsPeachHelp(boolean value) {

      isPeachHelp_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }
    /**
     * <code>optional bool isPeachHelp = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearIsPeachHelp() {
      bitField0_ = (bitField0_ & ~0x00000080);
      isPeachHelp_ = false;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.HelpData> helpData_ =
      java.util.Collections.emptyList();
    private void ensureHelpDataIsMutable() {
      if (!((bitField0_ & 0x00000100) != 0)) {
        helpData_ = new java.util.ArrayList<xddq.pb.HelpData>(helpData_);
        bitField0_ |= 0x00000100;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.HelpData, xddq.pb.HelpData.Builder, xddq.pb.HelpDataOrBuilder> helpDataBuilder_;

    /**
     * <code>repeated .xddq.pb.HelpData helpData = 10;</code>
     */
    public java.util.List<xddq.pb.HelpData> getHelpDataList() {
      if (helpDataBuilder_ == null) {
        return java.util.Collections.unmodifiableList(helpData_);
      } else {
        return helpDataBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.HelpData helpData = 10;</code>
     */
    public int getHelpDataCount() {
      if (helpDataBuilder_ == null) {
        return helpData_.size();
      } else {
        return helpDataBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.HelpData helpData = 10;</code>
     */
    public xddq.pb.HelpData getHelpData(int index) {
      if (helpDataBuilder_ == null) {
        return helpData_.get(index);
      } else {
        return helpDataBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.HelpData helpData = 10;</code>
     */
    public Builder setHelpData(
        int index, xddq.pb.HelpData value) {
      if (helpDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureHelpDataIsMutable();
        helpData_.set(index, value);
        onChanged();
      } else {
        helpDataBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.HelpData helpData = 10;</code>
     */
    public Builder setHelpData(
        int index, xddq.pb.HelpData.Builder builderForValue) {
      if (helpDataBuilder_ == null) {
        ensureHelpDataIsMutable();
        helpData_.set(index, builderForValue.build());
        onChanged();
      } else {
        helpDataBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.HelpData helpData = 10;</code>
     */
    public Builder addHelpData(xddq.pb.HelpData value) {
      if (helpDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureHelpDataIsMutable();
        helpData_.add(value);
        onChanged();
      } else {
        helpDataBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.HelpData helpData = 10;</code>
     */
    public Builder addHelpData(
        int index, xddq.pb.HelpData value) {
      if (helpDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureHelpDataIsMutable();
        helpData_.add(index, value);
        onChanged();
      } else {
        helpDataBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.HelpData helpData = 10;</code>
     */
    public Builder addHelpData(
        xddq.pb.HelpData.Builder builderForValue) {
      if (helpDataBuilder_ == null) {
        ensureHelpDataIsMutable();
        helpData_.add(builderForValue.build());
        onChanged();
      } else {
        helpDataBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.HelpData helpData = 10;</code>
     */
    public Builder addHelpData(
        int index, xddq.pb.HelpData.Builder builderForValue) {
      if (helpDataBuilder_ == null) {
        ensureHelpDataIsMutable();
        helpData_.add(index, builderForValue.build());
        onChanged();
      } else {
        helpDataBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.HelpData helpData = 10;</code>
     */
    public Builder addAllHelpData(
        java.lang.Iterable<? extends xddq.pb.HelpData> values) {
      if (helpDataBuilder_ == null) {
        ensureHelpDataIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, helpData_);
        onChanged();
      } else {
        helpDataBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.HelpData helpData = 10;</code>
     */
    public Builder clearHelpData() {
      if (helpDataBuilder_ == null) {
        helpData_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000100);
        onChanged();
      } else {
        helpDataBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.HelpData helpData = 10;</code>
     */
    public Builder removeHelpData(int index) {
      if (helpDataBuilder_ == null) {
        ensureHelpDataIsMutable();
        helpData_.remove(index);
        onChanged();
      } else {
        helpDataBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.HelpData helpData = 10;</code>
     */
    public xddq.pb.HelpData.Builder getHelpDataBuilder(
        int index) {
      return internalGetHelpDataFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.HelpData helpData = 10;</code>
     */
    public xddq.pb.HelpDataOrBuilder getHelpDataOrBuilder(
        int index) {
      if (helpDataBuilder_ == null) {
        return helpData_.get(index);  } else {
        return helpDataBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.HelpData helpData = 10;</code>
     */
    public java.util.List<? extends xddq.pb.HelpDataOrBuilder> 
         getHelpDataOrBuilderList() {
      if (helpDataBuilder_ != null) {
        return helpDataBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(helpData_);
      }
    }
    /**
     * <code>repeated .xddq.pb.HelpData helpData = 10;</code>
     */
    public xddq.pb.HelpData.Builder addHelpDataBuilder() {
      return internalGetHelpDataFieldBuilder().addBuilder(
          xddq.pb.HelpData.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.HelpData helpData = 10;</code>
     */
    public xddq.pb.HelpData.Builder addHelpDataBuilder(
        int index) {
      return internalGetHelpDataFieldBuilder().addBuilder(
          index, xddq.pb.HelpData.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.HelpData helpData = 10;</code>
     */
    public java.util.List<xddq.pb.HelpData.Builder> 
         getHelpDataBuilderList() {
      return internalGetHelpDataFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.HelpData, xddq.pb.HelpData.Builder, xddq.pb.HelpDataOrBuilder> 
        internalGetHelpDataFieldBuilder() {
      if (helpDataBuilder_ == null) {
        helpDataBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.HelpData, xddq.pb.HelpData.Builder, xddq.pb.HelpDataOrBuilder>(
                helpData_,
                ((bitField0_ & 0x00000100) != 0),
                getParentForChildren(),
                isClean());
        helpData_ = null;
      }
      return helpDataBuilder_;
    }

    private java.util.List<xddq.pb.YardSkillLCombineMsg> combineList_ =
      java.util.Collections.emptyList();
    private void ensureCombineListIsMutable() {
      if (!((bitField0_ & 0x00000200) != 0)) {
        combineList_ = new java.util.ArrayList<xddq.pb.YardSkillLCombineMsg>(combineList_);
        bitField0_ |= 0x00000200;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.YardSkillLCombineMsg, xddq.pb.YardSkillLCombineMsg.Builder, xddq.pb.YardSkillLCombineMsgOrBuilder> combineListBuilder_;

    /**
     * <code>repeated .xddq.pb.YardSkillLCombineMsg combineList = 11;</code>
     */
    public java.util.List<xddq.pb.YardSkillLCombineMsg> getCombineListList() {
      if (combineListBuilder_ == null) {
        return java.util.Collections.unmodifiableList(combineList_);
      } else {
        return combineListBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.YardSkillLCombineMsg combineList = 11;</code>
     */
    public int getCombineListCount() {
      if (combineListBuilder_ == null) {
        return combineList_.size();
      } else {
        return combineListBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.YardSkillLCombineMsg combineList = 11;</code>
     */
    public xddq.pb.YardSkillLCombineMsg getCombineList(int index) {
      if (combineListBuilder_ == null) {
        return combineList_.get(index);
      } else {
        return combineListBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.YardSkillLCombineMsg combineList = 11;</code>
     */
    public Builder setCombineList(
        int index, xddq.pb.YardSkillLCombineMsg value) {
      if (combineListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureCombineListIsMutable();
        combineList_.set(index, value);
        onChanged();
      } else {
        combineListBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.YardSkillLCombineMsg combineList = 11;</code>
     */
    public Builder setCombineList(
        int index, xddq.pb.YardSkillLCombineMsg.Builder builderForValue) {
      if (combineListBuilder_ == null) {
        ensureCombineListIsMutable();
        combineList_.set(index, builderForValue.build());
        onChanged();
      } else {
        combineListBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.YardSkillLCombineMsg combineList = 11;</code>
     */
    public Builder addCombineList(xddq.pb.YardSkillLCombineMsg value) {
      if (combineListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureCombineListIsMutable();
        combineList_.add(value);
        onChanged();
      } else {
        combineListBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.YardSkillLCombineMsg combineList = 11;</code>
     */
    public Builder addCombineList(
        int index, xddq.pb.YardSkillLCombineMsg value) {
      if (combineListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureCombineListIsMutable();
        combineList_.add(index, value);
        onChanged();
      } else {
        combineListBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.YardSkillLCombineMsg combineList = 11;</code>
     */
    public Builder addCombineList(
        xddq.pb.YardSkillLCombineMsg.Builder builderForValue) {
      if (combineListBuilder_ == null) {
        ensureCombineListIsMutable();
        combineList_.add(builderForValue.build());
        onChanged();
      } else {
        combineListBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.YardSkillLCombineMsg combineList = 11;</code>
     */
    public Builder addCombineList(
        int index, xddq.pb.YardSkillLCombineMsg.Builder builderForValue) {
      if (combineListBuilder_ == null) {
        ensureCombineListIsMutable();
        combineList_.add(index, builderForValue.build());
        onChanged();
      } else {
        combineListBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.YardSkillLCombineMsg combineList = 11;</code>
     */
    public Builder addAllCombineList(
        java.lang.Iterable<? extends xddq.pb.YardSkillLCombineMsg> values) {
      if (combineListBuilder_ == null) {
        ensureCombineListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, combineList_);
        onChanged();
      } else {
        combineListBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.YardSkillLCombineMsg combineList = 11;</code>
     */
    public Builder clearCombineList() {
      if (combineListBuilder_ == null) {
        combineList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000200);
        onChanged();
      } else {
        combineListBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.YardSkillLCombineMsg combineList = 11;</code>
     */
    public Builder removeCombineList(int index) {
      if (combineListBuilder_ == null) {
        ensureCombineListIsMutable();
        combineList_.remove(index);
        onChanged();
      } else {
        combineListBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.YardSkillLCombineMsg combineList = 11;</code>
     */
    public xddq.pb.YardSkillLCombineMsg.Builder getCombineListBuilder(
        int index) {
      return internalGetCombineListFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.YardSkillLCombineMsg combineList = 11;</code>
     */
    public xddq.pb.YardSkillLCombineMsgOrBuilder getCombineListOrBuilder(
        int index) {
      if (combineListBuilder_ == null) {
        return combineList_.get(index);  } else {
        return combineListBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.YardSkillLCombineMsg combineList = 11;</code>
     */
    public java.util.List<? extends xddq.pb.YardSkillLCombineMsgOrBuilder> 
         getCombineListOrBuilderList() {
      if (combineListBuilder_ != null) {
        return combineListBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(combineList_);
      }
    }
    /**
     * <code>repeated .xddq.pb.YardSkillLCombineMsg combineList = 11;</code>
     */
    public xddq.pb.YardSkillLCombineMsg.Builder addCombineListBuilder() {
      return internalGetCombineListFieldBuilder().addBuilder(
          xddq.pb.YardSkillLCombineMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.YardSkillLCombineMsg combineList = 11;</code>
     */
    public xddq.pb.YardSkillLCombineMsg.Builder addCombineListBuilder(
        int index) {
      return internalGetCombineListFieldBuilder().addBuilder(
          index, xddq.pb.YardSkillLCombineMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.YardSkillLCombineMsg combineList = 11;</code>
     */
    public java.util.List<xddq.pb.YardSkillLCombineMsg.Builder> 
         getCombineListBuilderList() {
      return internalGetCombineListFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.YardSkillLCombineMsg, xddq.pb.YardSkillLCombineMsg.Builder, xddq.pb.YardSkillLCombineMsgOrBuilder> 
        internalGetCombineListFieldBuilder() {
      if (combineListBuilder_ == null) {
        combineListBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.YardSkillLCombineMsg, xddq.pb.YardSkillLCombineMsg.Builder, xddq.pb.YardSkillLCombineMsgOrBuilder>(
                combineList_,
                ((bitField0_ & 0x00000200) != 0),
                getParentForChildren(),
                isClean());
        combineList_ = null;
      }
      return combineListBuilder_;
    }

    private java.util.List<xddq.pb.YardBuildInfoMsg> decorateList_ =
      java.util.Collections.emptyList();
    private void ensureDecorateListIsMutable() {
      if (!((bitField0_ & 0x00000400) != 0)) {
        decorateList_ = new java.util.ArrayList<xddq.pb.YardBuildInfoMsg>(decorateList_);
        bitField0_ |= 0x00000400;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.YardBuildInfoMsg, xddq.pb.YardBuildInfoMsg.Builder, xddq.pb.YardBuildInfoMsgOrBuilder> decorateListBuilder_;

    /**
     * <code>repeated .xddq.pb.YardBuildInfoMsg decorateList = 12;</code>
     */
    public java.util.List<xddq.pb.YardBuildInfoMsg> getDecorateListList() {
      if (decorateListBuilder_ == null) {
        return java.util.Collections.unmodifiableList(decorateList_);
      } else {
        return decorateListBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.YardBuildInfoMsg decorateList = 12;</code>
     */
    public int getDecorateListCount() {
      if (decorateListBuilder_ == null) {
        return decorateList_.size();
      } else {
        return decorateListBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.YardBuildInfoMsg decorateList = 12;</code>
     */
    public xddq.pb.YardBuildInfoMsg getDecorateList(int index) {
      if (decorateListBuilder_ == null) {
        return decorateList_.get(index);
      } else {
        return decorateListBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.YardBuildInfoMsg decorateList = 12;</code>
     */
    public Builder setDecorateList(
        int index, xddq.pb.YardBuildInfoMsg value) {
      if (decorateListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDecorateListIsMutable();
        decorateList_.set(index, value);
        onChanged();
      } else {
        decorateListBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.YardBuildInfoMsg decorateList = 12;</code>
     */
    public Builder setDecorateList(
        int index, xddq.pb.YardBuildInfoMsg.Builder builderForValue) {
      if (decorateListBuilder_ == null) {
        ensureDecorateListIsMutable();
        decorateList_.set(index, builderForValue.build());
        onChanged();
      } else {
        decorateListBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.YardBuildInfoMsg decorateList = 12;</code>
     */
    public Builder addDecorateList(xddq.pb.YardBuildInfoMsg value) {
      if (decorateListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDecorateListIsMutable();
        decorateList_.add(value);
        onChanged();
      } else {
        decorateListBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.YardBuildInfoMsg decorateList = 12;</code>
     */
    public Builder addDecorateList(
        int index, xddq.pb.YardBuildInfoMsg value) {
      if (decorateListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDecorateListIsMutable();
        decorateList_.add(index, value);
        onChanged();
      } else {
        decorateListBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.YardBuildInfoMsg decorateList = 12;</code>
     */
    public Builder addDecorateList(
        xddq.pb.YardBuildInfoMsg.Builder builderForValue) {
      if (decorateListBuilder_ == null) {
        ensureDecorateListIsMutable();
        decorateList_.add(builderForValue.build());
        onChanged();
      } else {
        decorateListBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.YardBuildInfoMsg decorateList = 12;</code>
     */
    public Builder addDecorateList(
        int index, xddq.pb.YardBuildInfoMsg.Builder builderForValue) {
      if (decorateListBuilder_ == null) {
        ensureDecorateListIsMutable();
        decorateList_.add(index, builderForValue.build());
        onChanged();
      } else {
        decorateListBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.YardBuildInfoMsg decorateList = 12;</code>
     */
    public Builder addAllDecorateList(
        java.lang.Iterable<? extends xddq.pb.YardBuildInfoMsg> values) {
      if (decorateListBuilder_ == null) {
        ensureDecorateListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, decorateList_);
        onChanged();
      } else {
        decorateListBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.YardBuildInfoMsg decorateList = 12;</code>
     */
    public Builder clearDecorateList() {
      if (decorateListBuilder_ == null) {
        decorateList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000400);
        onChanged();
      } else {
        decorateListBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.YardBuildInfoMsg decorateList = 12;</code>
     */
    public Builder removeDecorateList(int index) {
      if (decorateListBuilder_ == null) {
        ensureDecorateListIsMutable();
        decorateList_.remove(index);
        onChanged();
      } else {
        decorateListBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.YardBuildInfoMsg decorateList = 12;</code>
     */
    public xddq.pb.YardBuildInfoMsg.Builder getDecorateListBuilder(
        int index) {
      return internalGetDecorateListFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.YardBuildInfoMsg decorateList = 12;</code>
     */
    public xddq.pb.YardBuildInfoMsgOrBuilder getDecorateListOrBuilder(
        int index) {
      if (decorateListBuilder_ == null) {
        return decorateList_.get(index);  } else {
        return decorateListBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.YardBuildInfoMsg decorateList = 12;</code>
     */
    public java.util.List<? extends xddq.pb.YardBuildInfoMsgOrBuilder> 
         getDecorateListOrBuilderList() {
      if (decorateListBuilder_ != null) {
        return decorateListBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(decorateList_);
      }
    }
    /**
     * <code>repeated .xddq.pb.YardBuildInfoMsg decorateList = 12;</code>
     */
    public xddq.pb.YardBuildInfoMsg.Builder addDecorateListBuilder() {
      return internalGetDecorateListFieldBuilder().addBuilder(
          xddq.pb.YardBuildInfoMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.YardBuildInfoMsg decorateList = 12;</code>
     */
    public xddq.pb.YardBuildInfoMsg.Builder addDecorateListBuilder(
        int index) {
      return internalGetDecorateListFieldBuilder().addBuilder(
          index, xddq.pb.YardBuildInfoMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.YardBuildInfoMsg decorateList = 12;</code>
     */
    public java.util.List<xddq.pb.YardBuildInfoMsg.Builder> 
         getDecorateListBuilderList() {
      return internalGetDecorateListFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.YardBuildInfoMsg, xddq.pb.YardBuildInfoMsg.Builder, xddq.pb.YardBuildInfoMsgOrBuilder> 
        internalGetDecorateListFieldBuilder() {
      if (decorateListBuilder_ == null) {
        decorateListBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.YardBuildInfoMsg, xddq.pb.YardBuildInfoMsg.Builder, xddq.pb.YardBuildInfoMsgOrBuilder>(
                decorateList_,
                ((bitField0_ & 0x00000400) != 0),
                getParentForChildren(),
                isClean());
        decorateList_ = null;
      }
      return decorateListBuilder_;
    }

    private int yesterDayHelpNum_ ;
    /**
     * <code>optional int32 yesterDayHelpNum = 13;</code>
     * @return Whether the yesterDayHelpNum field is set.
     */
    @java.lang.Override
    public boolean hasYesterDayHelpNum() {
      return ((bitField0_ & 0x00000800) != 0);
    }
    /**
     * <code>optional int32 yesterDayHelpNum = 13;</code>
     * @return The yesterDayHelpNum.
     */
    @java.lang.Override
    public int getYesterDayHelpNum() {
      return yesterDayHelpNum_;
    }
    /**
     * <code>optional int32 yesterDayHelpNum = 13;</code>
     * @param value The yesterDayHelpNum to set.
     * @return This builder for chaining.
     */
    public Builder setYesterDayHelpNum(int value) {

      yesterDayHelpNum_ = value;
      bitField0_ |= 0x00000800;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 yesterDayHelpNum = 13;</code>
     * @return This builder for chaining.
     */
    public Builder clearYesterDayHelpNum() {
      bitField0_ = (bitField0_ & ~0x00000800);
      yesterDayHelpNum_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.YardLimitTimeBuildMsg> timeBuildList_ =
      java.util.Collections.emptyList();
    private void ensureTimeBuildListIsMutable() {
      if (!((bitField0_ & 0x00001000) != 0)) {
        timeBuildList_ = new java.util.ArrayList<xddq.pb.YardLimitTimeBuildMsg>(timeBuildList_);
        bitField0_ |= 0x00001000;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.YardLimitTimeBuildMsg, xddq.pb.YardLimitTimeBuildMsg.Builder, xddq.pb.YardLimitTimeBuildMsgOrBuilder> timeBuildListBuilder_;

    /**
     * <code>repeated .xddq.pb.YardLimitTimeBuildMsg timeBuildList = 14;</code>
     */
    public java.util.List<xddq.pb.YardLimitTimeBuildMsg> getTimeBuildListList() {
      if (timeBuildListBuilder_ == null) {
        return java.util.Collections.unmodifiableList(timeBuildList_);
      } else {
        return timeBuildListBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.YardLimitTimeBuildMsg timeBuildList = 14;</code>
     */
    public int getTimeBuildListCount() {
      if (timeBuildListBuilder_ == null) {
        return timeBuildList_.size();
      } else {
        return timeBuildListBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.YardLimitTimeBuildMsg timeBuildList = 14;</code>
     */
    public xddq.pb.YardLimitTimeBuildMsg getTimeBuildList(int index) {
      if (timeBuildListBuilder_ == null) {
        return timeBuildList_.get(index);
      } else {
        return timeBuildListBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.YardLimitTimeBuildMsg timeBuildList = 14;</code>
     */
    public Builder setTimeBuildList(
        int index, xddq.pb.YardLimitTimeBuildMsg value) {
      if (timeBuildListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureTimeBuildListIsMutable();
        timeBuildList_.set(index, value);
        onChanged();
      } else {
        timeBuildListBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.YardLimitTimeBuildMsg timeBuildList = 14;</code>
     */
    public Builder setTimeBuildList(
        int index, xddq.pb.YardLimitTimeBuildMsg.Builder builderForValue) {
      if (timeBuildListBuilder_ == null) {
        ensureTimeBuildListIsMutable();
        timeBuildList_.set(index, builderForValue.build());
        onChanged();
      } else {
        timeBuildListBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.YardLimitTimeBuildMsg timeBuildList = 14;</code>
     */
    public Builder addTimeBuildList(xddq.pb.YardLimitTimeBuildMsg value) {
      if (timeBuildListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureTimeBuildListIsMutable();
        timeBuildList_.add(value);
        onChanged();
      } else {
        timeBuildListBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.YardLimitTimeBuildMsg timeBuildList = 14;</code>
     */
    public Builder addTimeBuildList(
        int index, xddq.pb.YardLimitTimeBuildMsg value) {
      if (timeBuildListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureTimeBuildListIsMutable();
        timeBuildList_.add(index, value);
        onChanged();
      } else {
        timeBuildListBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.YardLimitTimeBuildMsg timeBuildList = 14;</code>
     */
    public Builder addTimeBuildList(
        xddq.pb.YardLimitTimeBuildMsg.Builder builderForValue) {
      if (timeBuildListBuilder_ == null) {
        ensureTimeBuildListIsMutable();
        timeBuildList_.add(builderForValue.build());
        onChanged();
      } else {
        timeBuildListBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.YardLimitTimeBuildMsg timeBuildList = 14;</code>
     */
    public Builder addTimeBuildList(
        int index, xddq.pb.YardLimitTimeBuildMsg.Builder builderForValue) {
      if (timeBuildListBuilder_ == null) {
        ensureTimeBuildListIsMutable();
        timeBuildList_.add(index, builderForValue.build());
        onChanged();
      } else {
        timeBuildListBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.YardLimitTimeBuildMsg timeBuildList = 14;</code>
     */
    public Builder addAllTimeBuildList(
        java.lang.Iterable<? extends xddq.pb.YardLimitTimeBuildMsg> values) {
      if (timeBuildListBuilder_ == null) {
        ensureTimeBuildListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, timeBuildList_);
        onChanged();
      } else {
        timeBuildListBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.YardLimitTimeBuildMsg timeBuildList = 14;</code>
     */
    public Builder clearTimeBuildList() {
      if (timeBuildListBuilder_ == null) {
        timeBuildList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00001000);
        onChanged();
      } else {
        timeBuildListBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.YardLimitTimeBuildMsg timeBuildList = 14;</code>
     */
    public Builder removeTimeBuildList(int index) {
      if (timeBuildListBuilder_ == null) {
        ensureTimeBuildListIsMutable();
        timeBuildList_.remove(index);
        onChanged();
      } else {
        timeBuildListBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.YardLimitTimeBuildMsg timeBuildList = 14;</code>
     */
    public xddq.pb.YardLimitTimeBuildMsg.Builder getTimeBuildListBuilder(
        int index) {
      return internalGetTimeBuildListFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.YardLimitTimeBuildMsg timeBuildList = 14;</code>
     */
    public xddq.pb.YardLimitTimeBuildMsgOrBuilder getTimeBuildListOrBuilder(
        int index) {
      if (timeBuildListBuilder_ == null) {
        return timeBuildList_.get(index);  } else {
        return timeBuildListBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.YardLimitTimeBuildMsg timeBuildList = 14;</code>
     */
    public java.util.List<? extends xddq.pb.YardLimitTimeBuildMsgOrBuilder> 
         getTimeBuildListOrBuilderList() {
      if (timeBuildListBuilder_ != null) {
        return timeBuildListBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(timeBuildList_);
      }
    }
    /**
     * <code>repeated .xddq.pb.YardLimitTimeBuildMsg timeBuildList = 14;</code>
     */
    public xddq.pb.YardLimitTimeBuildMsg.Builder addTimeBuildListBuilder() {
      return internalGetTimeBuildListFieldBuilder().addBuilder(
          xddq.pb.YardLimitTimeBuildMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.YardLimitTimeBuildMsg timeBuildList = 14;</code>
     */
    public xddq.pb.YardLimitTimeBuildMsg.Builder addTimeBuildListBuilder(
        int index) {
      return internalGetTimeBuildListFieldBuilder().addBuilder(
          index, xddq.pb.YardLimitTimeBuildMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.YardLimitTimeBuildMsg timeBuildList = 14;</code>
     */
    public java.util.List<xddq.pb.YardLimitTimeBuildMsg.Builder> 
         getTimeBuildListBuilderList() {
      return internalGetTimeBuildListFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.YardLimitTimeBuildMsg, xddq.pb.YardLimitTimeBuildMsg.Builder, xddq.pb.YardLimitTimeBuildMsgOrBuilder> 
        internalGetTimeBuildListFieldBuilder() {
      if (timeBuildListBuilder_ == null) {
        timeBuildListBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.YardLimitTimeBuildMsg, xddq.pb.YardLimitTimeBuildMsg.Builder, xddq.pb.YardLimitTimeBuildMsgOrBuilder>(
                timeBuildList_,
                ((bitField0_ & 0x00001000) != 0),
                getParentForChildren(),
                isClean());
        timeBuildList_ = null;
      }
      return timeBuildListBuilder_;
    }

    private xddq.pb.PlayerPilotShowDataMsg playerShow_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PlayerPilotShowDataMsg, xddq.pb.PlayerPilotShowDataMsg.Builder, xddq.pb.PlayerPilotShowDataMsgOrBuilder> playerShowBuilder_;
    /**
     * <code>optional .xddq.pb.PlayerPilotShowDataMsg playerShow = 15;</code>
     * @return Whether the playerShow field is set.
     */
    public boolean hasPlayerShow() {
      return ((bitField0_ & 0x00002000) != 0);
    }
    /**
     * <code>optional .xddq.pb.PlayerPilotShowDataMsg playerShow = 15;</code>
     * @return The playerShow.
     */
    public xddq.pb.PlayerPilotShowDataMsg getPlayerShow() {
      if (playerShowBuilder_ == null) {
        return playerShow_ == null ? xddq.pb.PlayerPilotShowDataMsg.getDefaultInstance() : playerShow_;
      } else {
        return playerShowBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.PlayerPilotShowDataMsg playerShow = 15;</code>
     */
    public Builder setPlayerShow(xddq.pb.PlayerPilotShowDataMsg value) {
      if (playerShowBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        playerShow_ = value;
      } else {
        playerShowBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00002000;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerPilotShowDataMsg playerShow = 15;</code>
     */
    public Builder setPlayerShow(
        xddq.pb.PlayerPilotShowDataMsg.Builder builderForValue) {
      if (playerShowBuilder_ == null) {
        playerShow_ = builderForValue.build();
      } else {
        playerShowBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00002000;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerPilotShowDataMsg playerShow = 15;</code>
     */
    public Builder mergePlayerShow(xddq.pb.PlayerPilotShowDataMsg value) {
      if (playerShowBuilder_ == null) {
        if (((bitField0_ & 0x00002000) != 0) &&
          playerShow_ != null &&
          playerShow_ != xddq.pb.PlayerPilotShowDataMsg.getDefaultInstance()) {
          getPlayerShowBuilder().mergeFrom(value);
        } else {
          playerShow_ = value;
        }
      } else {
        playerShowBuilder_.mergeFrom(value);
      }
      if (playerShow_ != null) {
        bitField0_ |= 0x00002000;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerPilotShowDataMsg playerShow = 15;</code>
     */
    public Builder clearPlayerShow() {
      bitField0_ = (bitField0_ & ~0x00002000);
      playerShow_ = null;
      if (playerShowBuilder_ != null) {
        playerShowBuilder_.dispose();
        playerShowBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerPilotShowDataMsg playerShow = 15;</code>
     */
    public xddq.pb.PlayerPilotShowDataMsg.Builder getPlayerShowBuilder() {
      bitField0_ |= 0x00002000;
      onChanged();
      return internalGetPlayerShowFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.PlayerPilotShowDataMsg playerShow = 15;</code>
     */
    public xddq.pb.PlayerPilotShowDataMsgOrBuilder getPlayerShowOrBuilder() {
      if (playerShowBuilder_ != null) {
        return playerShowBuilder_.getMessageOrBuilder();
      } else {
        return playerShow_ == null ?
            xddq.pb.PlayerPilotShowDataMsg.getDefaultInstance() : playerShow_;
      }
    }
    /**
     * <code>optional .xddq.pb.PlayerPilotShowDataMsg playerShow = 15;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PlayerPilotShowDataMsg, xddq.pb.PlayerPilotShowDataMsg.Builder, xddq.pb.PlayerPilotShowDataMsgOrBuilder> 
        internalGetPlayerShowFieldBuilder() {
      if (playerShowBuilder_ == null) {
        playerShowBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.PlayerPilotShowDataMsg, xddq.pb.PlayerPilotShowDataMsg.Builder, xddq.pb.PlayerPilotShowDataMsgOrBuilder>(
                getPlayerShow(),
                getParentForChildren(),
                isClean());
        playerShow_ = null;
      }
      return playerShowBuilder_;
    }

    private java.util.List<xddq.pb.YardMainBuildInfoMsg> mainBuildInfo_ =
      java.util.Collections.emptyList();
    private void ensureMainBuildInfoIsMutable() {
      if (!((bitField0_ & 0x00004000) != 0)) {
        mainBuildInfo_ = new java.util.ArrayList<xddq.pb.YardMainBuildInfoMsg>(mainBuildInfo_);
        bitField0_ |= 0x00004000;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.YardMainBuildInfoMsg, xddq.pb.YardMainBuildInfoMsg.Builder, xddq.pb.YardMainBuildInfoMsgOrBuilder> mainBuildInfoBuilder_;

    /**
     * <code>repeated .xddq.pb.YardMainBuildInfoMsg mainBuildInfo = 16;</code>
     */
    public java.util.List<xddq.pb.YardMainBuildInfoMsg> getMainBuildInfoList() {
      if (mainBuildInfoBuilder_ == null) {
        return java.util.Collections.unmodifiableList(mainBuildInfo_);
      } else {
        return mainBuildInfoBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.YardMainBuildInfoMsg mainBuildInfo = 16;</code>
     */
    public int getMainBuildInfoCount() {
      if (mainBuildInfoBuilder_ == null) {
        return mainBuildInfo_.size();
      } else {
        return mainBuildInfoBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.YardMainBuildInfoMsg mainBuildInfo = 16;</code>
     */
    public xddq.pb.YardMainBuildInfoMsg getMainBuildInfo(int index) {
      if (mainBuildInfoBuilder_ == null) {
        return mainBuildInfo_.get(index);
      } else {
        return mainBuildInfoBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.YardMainBuildInfoMsg mainBuildInfo = 16;</code>
     */
    public Builder setMainBuildInfo(
        int index, xddq.pb.YardMainBuildInfoMsg value) {
      if (mainBuildInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMainBuildInfoIsMutable();
        mainBuildInfo_.set(index, value);
        onChanged();
      } else {
        mainBuildInfoBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.YardMainBuildInfoMsg mainBuildInfo = 16;</code>
     */
    public Builder setMainBuildInfo(
        int index, xddq.pb.YardMainBuildInfoMsg.Builder builderForValue) {
      if (mainBuildInfoBuilder_ == null) {
        ensureMainBuildInfoIsMutable();
        mainBuildInfo_.set(index, builderForValue.build());
        onChanged();
      } else {
        mainBuildInfoBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.YardMainBuildInfoMsg mainBuildInfo = 16;</code>
     */
    public Builder addMainBuildInfo(xddq.pb.YardMainBuildInfoMsg value) {
      if (mainBuildInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMainBuildInfoIsMutable();
        mainBuildInfo_.add(value);
        onChanged();
      } else {
        mainBuildInfoBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.YardMainBuildInfoMsg mainBuildInfo = 16;</code>
     */
    public Builder addMainBuildInfo(
        int index, xddq.pb.YardMainBuildInfoMsg value) {
      if (mainBuildInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMainBuildInfoIsMutable();
        mainBuildInfo_.add(index, value);
        onChanged();
      } else {
        mainBuildInfoBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.YardMainBuildInfoMsg mainBuildInfo = 16;</code>
     */
    public Builder addMainBuildInfo(
        xddq.pb.YardMainBuildInfoMsg.Builder builderForValue) {
      if (mainBuildInfoBuilder_ == null) {
        ensureMainBuildInfoIsMutable();
        mainBuildInfo_.add(builderForValue.build());
        onChanged();
      } else {
        mainBuildInfoBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.YardMainBuildInfoMsg mainBuildInfo = 16;</code>
     */
    public Builder addMainBuildInfo(
        int index, xddq.pb.YardMainBuildInfoMsg.Builder builderForValue) {
      if (mainBuildInfoBuilder_ == null) {
        ensureMainBuildInfoIsMutable();
        mainBuildInfo_.add(index, builderForValue.build());
        onChanged();
      } else {
        mainBuildInfoBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.YardMainBuildInfoMsg mainBuildInfo = 16;</code>
     */
    public Builder addAllMainBuildInfo(
        java.lang.Iterable<? extends xddq.pb.YardMainBuildInfoMsg> values) {
      if (mainBuildInfoBuilder_ == null) {
        ensureMainBuildInfoIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, mainBuildInfo_);
        onChanged();
      } else {
        mainBuildInfoBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.YardMainBuildInfoMsg mainBuildInfo = 16;</code>
     */
    public Builder clearMainBuildInfo() {
      if (mainBuildInfoBuilder_ == null) {
        mainBuildInfo_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00004000);
        onChanged();
      } else {
        mainBuildInfoBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.YardMainBuildInfoMsg mainBuildInfo = 16;</code>
     */
    public Builder removeMainBuildInfo(int index) {
      if (mainBuildInfoBuilder_ == null) {
        ensureMainBuildInfoIsMutable();
        mainBuildInfo_.remove(index);
        onChanged();
      } else {
        mainBuildInfoBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.YardMainBuildInfoMsg mainBuildInfo = 16;</code>
     */
    public xddq.pb.YardMainBuildInfoMsg.Builder getMainBuildInfoBuilder(
        int index) {
      return internalGetMainBuildInfoFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.YardMainBuildInfoMsg mainBuildInfo = 16;</code>
     */
    public xddq.pb.YardMainBuildInfoMsgOrBuilder getMainBuildInfoOrBuilder(
        int index) {
      if (mainBuildInfoBuilder_ == null) {
        return mainBuildInfo_.get(index);  } else {
        return mainBuildInfoBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.YardMainBuildInfoMsg mainBuildInfo = 16;</code>
     */
    public java.util.List<? extends xddq.pb.YardMainBuildInfoMsgOrBuilder> 
         getMainBuildInfoOrBuilderList() {
      if (mainBuildInfoBuilder_ != null) {
        return mainBuildInfoBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(mainBuildInfo_);
      }
    }
    /**
     * <code>repeated .xddq.pb.YardMainBuildInfoMsg mainBuildInfo = 16;</code>
     */
    public xddq.pb.YardMainBuildInfoMsg.Builder addMainBuildInfoBuilder() {
      return internalGetMainBuildInfoFieldBuilder().addBuilder(
          xddq.pb.YardMainBuildInfoMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.YardMainBuildInfoMsg mainBuildInfo = 16;</code>
     */
    public xddq.pb.YardMainBuildInfoMsg.Builder addMainBuildInfoBuilder(
        int index) {
      return internalGetMainBuildInfoFieldBuilder().addBuilder(
          index, xddq.pb.YardMainBuildInfoMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.YardMainBuildInfoMsg mainBuildInfo = 16;</code>
     */
    public java.util.List<xddq.pb.YardMainBuildInfoMsg.Builder> 
         getMainBuildInfoBuilderList() {
      return internalGetMainBuildInfoFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.YardMainBuildInfoMsg, xddq.pb.YardMainBuildInfoMsg.Builder, xddq.pb.YardMainBuildInfoMsgOrBuilder> 
        internalGetMainBuildInfoFieldBuilder() {
      if (mainBuildInfoBuilder_ == null) {
        mainBuildInfoBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.YardMainBuildInfoMsg, xddq.pb.YardMainBuildInfoMsg.Builder, xddq.pb.YardMainBuildInfoMsgOrBuilder>(
                mainBuildInfo_,
                ((bitField0_ & 0x00004000) != 0),
                getParentForChildren(),
                isClean());
        mainBuildInfo_ = null;
      }
      return mainBuildInfoBuilder_;
    }

    private int speedTimes_ ;
    /**
     * <code>optional int32 speedTimes = 17;</code>
     * @return Whether the speedTimes field is set.
     */
    @java.lang.Override
    public boolean hasSpeedTimes() {
      return ((bitField0_ & 0x00008000) != 0);
    }
    /**
     * <code>optional int32 speedTimes = 17;</code>
     * @return The speedTimes.
     */
    @java.lang.Override
    public int getSpeedTimes() {
      return speedTimes_;
    }
    /**
     * <code>optional int32 speedTimes = 17;</code>
     * @param value The speedTimes to set.
     * @return This builder for chaining.
     */
    public Builder setSpeedTimes(int value) {

      speedTimes_ = value;
      bitField0_ |= 0x00008000;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 speedTimes = 17;</code>
     * @return This builder for chaining.
     */
    public Builder clearSpeedTimes() {
      bitField0_ = (bitField0_ & ~0x00008000);
      speedTimes_ = 0;
      onChanged();
      return this;
    }

    private long banTime_ ;
    /**
     * <code>optional int64 banTime = 18;</code>
     * @return Whether the banTime field is set.
     */
    @java.lang.Override
    public boolean hasBanTime() {
      return ((bitField0_ & 0x00010000) != 0);
    }
    /**
     * <code>optional int64 banTime = 18;</code>
     * @return The banTime.
     */
    @java.lang.Override
    public long getBanTime() {
      return banTime_;
    }
    /**
     * <code>optional int64 banTime = 18;</code>
     * @param value The banTime to set.
     * @return This builder for chaining.
     */
    public Builder setBanTime(long value) {

      banTime_ = value;
      bitField0_ |= 0x00010000;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 banTime = 18;</code>
     * @return This builder for chaining.
     */
    public Builder clearBanTime() {
      bitField0_ = (bitField0_ & ~0x00010000);
      banTime_ = 0L;
      onChanged();
      return this;
    }

    private int giveNum_ ;
    /**
     * <code>optional int32 giveNum = 19;</code>
     * @return Whether the giveNum field is set.
     */
    @java.lang.Override
    public boolean hasGiveNum() {
      return ((bitField0_ & 0x00020000) != 0);
    }
    /**
     * <code>optional int32 giveNum = 19;</code>
     * @return The giveNum.
     */
    @java.lang.Override
    public int getGiveNum() {
      return giveNum_;
    }
    /**
     * <code>optional int32 giveNum = 19;</code>
     * @param value The giveNum to set.
     * @return This builder for chaining.
     */
    public Builder setGiveNum(int value) {

      giveNum_ = value;
      bitField0_ |= 0x00020000;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 giveNum = 19;</code>
     * @return This builder for chaining.
     */
    public Builder clearGiveNum() {
      bitField0_ = (bitField0_ & ~0x00020000);
      giveNum_ = 0;
      onChanged();
      return this;
    }

    private xddq.pb.YardOtherData otherData_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.YardOtherData, xddq.pb.YardOtherData.Builder, xddq.pb.YardOtherDataOrBuilder> otherDataBuilder_;
    /**
     * <code>optional .xddq.pb.YardOtherData otherData = 20;</code>
     * @return Whether the otherData field is set.
     */
    public boolean hasOtherData() {
      return ((bitField0_ & 0x00040000) != 0);
    }
    /**
     * <code>optional .xddq.pb.YardOtherData otherData = 20;</code>
     * @return The otherData.
     */
    public xddq.pb.YardOtherData getOtherData() {
      if (otherDataBuilder_ == null) {
        return otherData_ == null ? xddq.pb.YardOtherData.getDefaultInstance() : otherData_;
      } else {
        return otherDataBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.YardOtherData otherData = 20;</code>
     */
    public Builder setOtherData(xddq.pb.YardOtherData value) {
      if (otherDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        otherData_ = value;
      } else {
        otherDataBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00040000;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.YardOtherData otherData = 20;</code>
     */
    public Builder setOtherData(
        xddq.pb.YardOtherData.Builder builderForValue) {
      if (otherDataBuilder_ == null) {
        otherData_ = builderForValue.build();
      } else {
        otherDataBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00040000;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.YardOtherData otherData = 20;</code>
     */
    public Builder mergeOtherData(xddq.pb.YardOtherData value) {
      if (otherDataBuilder_ == null) {
        if (((bitField0_ & 0x00040000) != 0) &&
          otherData_ != null &&
          otherData_ != xddq.pb.YardOtherData.getDefaultInstance()) {
          getOtherDataBuilder().mergeFrom(value);
        } else {
          otherData_ = value;
        }
      } else {
        otherDataBuilder_.mergeFrom(value);
      }
      if (otherData_ != null) {
        bitField0_ |= 0x00040000;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.YardOtherData otherData = 20;</code>
     */
    public Builder clearOtherData() {
      bitField0_ = (bitField0_ & ~0x00040000);
      otherData_ = null;
      if (otherDataBuilder_ != null) {
        otherDataBuilder_.dispose();
        otherDataBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.YardOtherData otherData = 20;</code>
     */
    public xddq.pb.YardOtherData.Builder getOtherDataBuilder() {
      bitField0_ |= 0x00040000;
      onChanged();
      return internalGetOtherDataFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.YardOtherData otherData = 20;</code>
     */
    public xddq.pb.YardOtherDataOrBuilder getOtherDataOrBuilder() {
      if (otherDataBuilder_ != null) {
        return otherDataBuilder_.getMessageOrBuilder();
      } else {
        return otherData_ == null ?
            xddq.pb.YardOtherData.getDefaultInstance() : otherData_;
      }
    }
    /**
     * <code>optional .xddq.pb.YardOtherData otherData = 20;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.YardOtherData, xddq.pb.YardOtherData.Builder, xddq.pb.YardOtherDataOrBuilder> 
        internalGetOtherDataFieldBuilder() {
      if (otherDataBuilder_ == null) {
        otherDataBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.YardOtherData, xddq.pb.YardOtherData.Builder, xddq.pb.YardOtherDataOrBuilder>(
                getOtherData(),
                getParentForChildren(),
                isClean());
        otherData_ = null;
      }
      return otherDataBuilder_;
    }

    private boolean isConfirm_ ;
    /**
     * <code>optional bool isConfirm = 21;</code>
     * @return Whether the isConfirm field is set.
     */
    @java.lang.Override
    public boolean hasIsConfirm() {
      return ((bitField0_ & 0x00080000) != 0);
    }
    /**
     * <code>optional bool isConfirm = 21;</code>
     * @return The isConfirm.
     */
    @java.lang.Override
    public boolean getIsConfirm() {
      return isConfirm_;
    }
    /**
     * <code>optional bool isConfirm = 21;</code>
     * @param value The isConfirm to set.
     * @return This builder for chaining.
     */
    public Builder setIsConfirm(boolean value) {

      isConfirm_ = value;
      bitField0_ |= 0x00080000;
      onChanged();
      return this;
    }
    /**
     * <code>optional bool isConfirm = 21;</code>
     * @return This builder for chaining.
     */
    public Builder clearIsConfirm() {
      bitField0_ = (bitField0_ & ~0x00080000);
      isConfirm_ = false;
      onChanged();
      return this;
    }

    private java.lang.Object convertPieceVec_ = "";
    /**
     * <code>optional string convertPieceVec = 22;</code>
     * @return Whether the convertPieceVec field is set.
     */
    public boolean hasConvertPieceVec() {
      return ((bitField0_ & 0x00100000) != 0);
    }
    /**
     * <code>optional string convertPieceVec = 22;</code>
     * @return The convertPieceVec.
     */
    public java.lang.String getConvertPieceVec() {
      java.lang.Object ref = convertPieceVec_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          convertPieceVec_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string convertPieceVec = 22;</code>
     * @return The bytes for convertPieceVec.
     */
    public com.google.protobuf.ByteString
        getConvertPieceVecBytes() {
      java.lang.Object ref = convertPieceVec_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        convertPieceVec_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string convertPieceVec = 22;</code>
     * @param value The convertPieceVec to set.
     * @return This builder for chaining.
     */
    public Builder setConvertPieceVec(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      convertPieceVec_ = value;
      bitField0_ |= 0x00100000;
      onChanged();
      return this;
    }
    /**
     * <code>optional string convertPieceVec = 22;</code>
     * @return This builder for chaining.
     */
    public Builder clearConvertPieceVec() {
      convertPieceVec_ = getDefaultInstance().getConvertPieceVec();
      bitField0_ = (bitField0_ & ~0x00100000);
      onChanged();
      return this;
    }
    /**
     * <code>optional string convertPieceVec = 22;</code>
     * @param value The bytes for convertPieceVec to set.
     * @return This builder for chaining.
     */
    public Builder setConvertPieceVecBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      convertPieceVec_ = value;
      bitField0_ |= 0x00100000;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.YardEnterResp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.YardEnterResp)
  private static final xddq.pb.YardEnterResp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.YardEnterResp();
  }

  public static xddq.pb.YardEnterResp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<YardEnterResp>
      PARSER = new com.google.protobuf.AbstractParser<YardEnterResp>() {
    @java.lang.Override
    public YardEnterResp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<YardEnterResp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<YardEnterResp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.YardEnterResp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

