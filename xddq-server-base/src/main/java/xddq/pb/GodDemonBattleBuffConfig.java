// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.GodDemonBattleBuffConfig}
 */
public final class GodDemonBattleBuffConfig extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.GodDemonBattleBuffConfig)
    GodDemonBattleBuffConfigOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      GodDemonBattleBuffConfig.class.getName());
  }
  // Use GodDemonBattleBuffConfig.newBuilder() to construct.
  private GodDemonBattleBuffConfig(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private GodDemonBattleBuffConfig() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_GodDemonBattleBuffConfig_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_GodDemonBattleBuffConfig_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.GodDemonBattleBuffConfig.class, xddq.pb.GodDemonBattleBuffConfig.Builder.class);
  }

  private int bitField0_;
  public static final int ACTIVITYID_FIELD_NUMBER = 1;
  private int activityId_ = 0;
  /**
   * <code>required int32 activityId = 1;</code>
   * @return Whether the activityId field is set.
   */
  @java.lang.Override
  public boolean hasActivityId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 activityId = 1;</code>
   * @return The activityId.
   */
  @java.lang.Override
  public int getActivityId() {
    return activityId_;
  }

  public static final int ID_FIELD_NUMBER = 2;
  private int id_ = 0;
  /**
   * <code>required int32 id = 2;</code>
   * @return Whether the id field is set.
   */
  @java.lang.Override
  public boolean hasId() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>required int32 id = 2;</code>
   * @return The id.
   */
  @java.lang.Override
  public int getId() {
    return id_;
  }

  public static final int SKILLID_FIELD_NUMBER = 3;
  private int skillId_ = 0;
  /**
   * <code>required int32 skillId = 3;</code>
   * @return Whether the skillId field is set.
   */
  @java.lang.Override
  public boolean hasSkillId() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>required int32 skillId = 3;</code>
   * @return The skillId.
   */
  @java.lang.Override
  public int getSkillId() {
    return skillId_;
  }

  public static final int TURN_FIELD_NUMBER = 4;
  private int turn_ = 0;
  /**
   * <code>required int32 turn = 4;</code>
   * @return Whether the turn field is set.
   */
  @java.lang.Override
  public boolean hasTurn() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>required int32 turn = 4;</code>
   * @return The turn.
   */
  @java.lang.Override
  public int getTurn() {
    return turn_;
  }

  public static final int WEIGHT_FIELD_NUMBER = 5;
  private int weight_ = 0;
  /**
   * <code>required int32 weight = 5;</code>
   * @return Whether the weight field is set.
   */
  @java.lang.Override
  public boolean hasWeight() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>required int32 weight = 5;</code>
   * @return The weight.
   */
  @java.lang.Override
  public int getWeight() {
    return weight_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasActivityId()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasId()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasSkillId()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasTurn()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasWeight()) {
      memoizedIsInitialized = 0;
      return false;
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, activityId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(2, id_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt32(3, skillId_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt32(4, turn_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      output.writeInt32(5, weight_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, activityId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, id_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(3, skillId_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(4, turn_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(5, weight_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.GodDemonBattleBuffConfig)) {
      return super.equals(obj);
    }
    xddq.pb.GodDemonBattleBuffConfig other = (xddq.pb.GodDemonBattleBuffConfig) obj;

    if (hasActivityId() != other.hasActivityId()) return false;
    if (hasActivityId()) {
      if (getActivityId()
          != other.getActivityId()) return false;
    }
    if (hasId() != other.hasId()) return false;
    if (hasId()) {
      if (getId()
          != other.getId()) return false;
    }
    if (hasSkillId() != other.hasSkillId()) return false;
    if (hasSkillId()) {
      if (getSkillId()
          != other.getSkillId()) return false;
    }
    if (hasTurn() != other.hasTurn()) return false;
    if (hasTurn()) {
      if (getTurn()
          != other.getTurn()) return false;
    }
    if (hasWeight() != other.hasWeight()) return false;
    if (hasWeight()) {
      if (getWeight()
          != other.getWeight()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasActivityId()) {
      hash = (37 * hash) + ACTIVITYID_FIELD_NUMBER;
      hash = (53 * hash) + getActivityId();
    }
    if (hasId()) {
      hash = (37 * hash) + ID_FIELD_NUMBER;
      hash = (53 * hash) + getId();
    }
    if (hasSkillId()) {
      hash = (37 * hash) + SKILLID_FIELD_NUMBER;
      hash = (53 * hash) + getSkillId();
    }
    if (hasTurn()) {
      hash = (37 * hash) + TURN_FIELD_NUMBER;
      hash = (53 * hash) + getTurn();
    }
    if (hasWeight()) {
      hash = (37 * hash) + WEIGHT_FIELD_NUMBER;
      hash = (53 * hash) + getWeight();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.GodDemonBattleBuffConfig parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GodDemonBattleBuffConfig parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GodDemonBattleBuffConfig parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GodDemonBattleBuffConfig parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GodDemonBattleBuffConfig parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GodDemonBattleBuffConfig parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GodDemonBattleBuffConfig parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.GodDemonBattleBuffConfig parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.GodDemonBattleBuffConfig parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.GodDemonBattleBuffConfig parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.GodDemonBattleBuffConfig parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.GodDemonBattleBuffConfig parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.GodDemonBattleBuffConfig prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.GodDemonBattleBuffConfig}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.GodDemonBattleBuffConfig)
      xddq.pb.GodDemonBattleBuffConfigOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GodDemonBattleBuffConfig_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GodDemonBattleBuffConfig_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.GodDemonBattleBuffConfig.class, xddq.pb.GodDemonBattleBuffConfig.Builder.class);
    }

    // Construct using xddq.pb.GodDemonBattleBuffConfig.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      activityId_ = 0;
      id_ = 0;
      skillId_ = 0;
      turn_ = 0;
      weight_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GodDemonBattleBuffConfig_descriptor;
    }

    @java.lang.Override
    public xddq.pb.GodDemonBattleBuffConfig getDefaultInstanceForType() {
      return xddq.pb.GodDemonBattleBuffConfig.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.GodDemonBattleBuffConfig build() {
      xddq.pb.GodDemonBattleBuffConfig result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.GodDemonBattleBuffConfig buildPartial() {
      xddq.pb.GodDemonBattleBuffConfig result = new xddq.pb.GodDemonBattleBuffConfig(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.GodDemonBattleBuffConfig result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.activityId_ = activityId_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.id_ = id_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.skillId_ = skillId_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.turn_ = turn_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.weight_ = weight_;
        to_bitField0_ |= 0x00000010;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.GodDemonBattleBuffConfig) {
        return mergeFrom((xddq.pb.GodDemonBattleBuffConfig)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.GodDemonBattleBuffConfig other) {
      if (other == xddq.pb.GodDemonBattleBuffConfig.getDefaultInstance()) return this;
      if (other.hasActivityId()) {
        setActivityId(other.getActivityId());
      }
      if (other.hasId()) {
        setId(other.getId());
      }
      if (other.hasSkillId()) {
        setSkillId(other.getSkillId());
      }
      if (other.hasTurn()) {
        setTurn(other.getTurn());
      }
      if (other.hasWeight()) {
        setWeight(other.getWeight());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasActivityId()) {
        return false;
      }
      if (!hasId()) {
        return false;
      }
      if (!hasSkillId()) {
        return false;
      }
      if (!hasTurn()) {
        return false;
      }
      if (!hasWeight()) {
        return false;
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              activityId_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              id_ = input.readInt32();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 24: {
              skillId_ = input.readInt32();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 32: {
              turn_ = input.readInt32();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            case 40: {
              weight_ = input.readInt32();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int activityId_ ;
    /**
     * <code>required int32 activityId = 1;</code>
     * @return Whether the activityId field is set.
     */
    @java.lang.Override
    public boolean hasActivityId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 activityId = 1;</code>
     * @return The activityId.
     */
    @java.lang.Override
    public int getActivityId() {
      return activityId_;
    }
    /**
     * <code>required int32 activityId = 1;</code>
     * @param value The activityId to set.
     * @return This builder for chaining.
     */
    public Builder setActivityId(int value) {

      activityId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 activityId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearActivityId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      activityId_ = 0;
      onChanged();
      return this;
    }

    private int id_ ;
    /**
     * <code>required int32 id = 2;</code>
     * @return Whether the id field is set.
     */
    @java.lang.Override
    public boolean hasId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>required int32 id = 2;</code>
     * @return The id.
     */
    @java.lang.Override
    public int getId() {
      return id_;
    }
    /**
     * <code>required int32 id = 2;</code>
     * @param value The id to set.
     * @return This builder for chaining.
     */
    public Builder setId(int value) {

      id_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 id = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearId() {
      bitField0_ = (bitField0_ & ~0x00000002);
      id_ = 0;
      onChanged();
      return this;
    }

    private int skillId_ ;
    /**
     * <code>required int32 skillId = 3;</code>
     * @return Whether the skillId field is set.
     */
    @java.lang.Override
    public boolean hasSkillId() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>required int32 skillId = 3;</code>
     * @return The skillId.
     */
    @java.lang.Override
    public int getSkillId() {
      return skillId_;
    }
    /**
     * <code>required int32 skillId = 3;</code>
     * @param value The skillId to set.
     * @return This builder for chaining.
     */
    public Builder setSkillId(int value) {

      skillId_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 skillId = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearSkillId() {
      bitField0_ = (bitField0_ & ~0x00000004);
      skillId_ = 0;
      onChanged();
      return this;
    }

    private int turn_ ;
    /**
     * <code>required int32 turn = 4;</code>
     * @return Whether the turn field is set.
     */
    @java.lang.Override
    public boolean hasTurn() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>required int32 turn = 4;</code>
     * @return The turn.
     */
    @java.lang.Override
    public int getTurn() {
      return turn_;
    }
    /**
     * <code>required int32 turn = 4;</code>
     * @param value The turn to set.
     * @return This builder for chaining.
     */
    public Builder setTurn(int value) {

      turn_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 turn = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearTurn() {
      bitField0_ = (bitField0_ & ~0x00000008);
      turn_ = 0;
      onChanged();
      return this;
    }

    private int weight_ ;
    /**
     * <code>required int32 weight = 5;</code>
     * @return Whether the weight field is set.
     */
    @java.lang.Override
    public boolean hasWeight() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>required int32 weight = 5;</code>
     * @return The weight.
     */
    @java.lang.Override
    public int getWeight() {
      return weight_;
    }
    /**
     * <code>required int32 weight = 5;</code>
     * @param value The weight to set.
     * @return This builder for chaining.
     */
    public Builder setWeight(int value) {

      weight_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 weight = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearWeight() {
      bitField0_ = (bitField0_ & ~0x00000010);
      weight_ = 0;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.GodDemonBattleBuffConfig)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.GodDemonBattleBuffConfig)
  private static final xddq.pb.GodDemonBattleBuffConfig DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.GodDemonBattleBuffConfig();
  }

  public static xddq.pb.GodDemonBattleBuffConfig getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<GodDemonBattleBuffConfig>
      PARSER = new com.google.protobuf.AbstractParser<GodDemonBattleBuffConfig>() {
    @java.lang.Override
    public GodDemonBattleBuffConfig parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<GodDemonBattleBuffConfig> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<GodDemonBattleBuffConfig> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.GodDemonBattleBuffConfig getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

