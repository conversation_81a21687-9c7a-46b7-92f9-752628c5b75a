// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.MountainSeaOutterTeamEntity}
 */
public final class MountainSeaOutterTeamEntity extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.MountainSeaOutterTeamEntity)
    MountainSeaOutterTeamEntityOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      MountainSeaOutterTeamEntity.class.getName());
  }
  // Use MountainSeaOutterTeamEntity.newBuilder() to construct.
  private MountainSeaOutterTeamEntity(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private MountainSeaOutterTeamEntity() {
    teamName_ = "";
    leaderName_ = "";
    fightValue_ = "";
    members_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_MountainSeaOutterTeamEntity_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_MountainSeaOutterTeamEntity_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.MountainSeaOutterTeamEntity.class, xddq.pb.MountainSeaOutterTeamEntity.Builder.class);
  }

  private int bitField0_;
  public static final int TEAMID_FIELD_NUMBER = 1;
  private long teamId_ = 0L;
  /**
   * <code>optional int64 teamId = 1;</code>
   * @return Whether the teamId field is set.
   */
  @java.lang.Override
  public boolean hasTeamId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int64 teamId = 1;</code>
   * @return The teamId.
   */
  @java.lang.Override
  public long getTeamId() {
    return teamId_;
  }

  public static final int TEAMNAME_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private volatile java.lang.Object teamName_ = "";
  /**
   * <code>optional string teamName = 2;</code>
   * @return Whether the teamName field is set.
   */
  @java.lang.Override
  public boolean hasTeamName() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional string teamName = 2;</code>
   * @return The teamName.
   */
  @java.lang.Override
  public java.lang.String getTeamName() {
    java.lang.Object ref = teamName_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        teamName_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string teamName = 2;</code>
   * @return The bytes for teamName.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getTeamNameBytes() {
    java.lang.Object ref = teamName_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      teamName_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int LEADERID_FIELD_NUMBER = 3;
  private long leaderId_ = 0L;
  /**
   * <code>optional int64 leaderId = 3;</code>
   * @return Whether the leaderId field is set.
   */
  @java.lang.Override
  public boolean hasLeaderId() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional int64 leaderId = 3;</code>
   * @return The leaderId.
   */
  @java.lang.Override
  public long getLeaderId() {
    return leaderId_;
  }

  public static final int LEADERNAME_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private volatile java.lang.Object leaderName_ = "";
  /**
   * <code>optional string leaderName = 4;</code>
   * @return Whether the leaderName field is set.
   */
  @java.lang.Override
  public boolean hasLeaderName() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional string leaderName = 4;</code>
   * @return The leaderName.
   */
  @java.lang.Override
  public java.lang.String getLeaderName() {
    java.lang.Object ref = leaderName_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        leaderName_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string leaderName = 4;</code>
   * @return The bytes for leaderName.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getLeaderNameBytes() {
    java.lang.Object ref = leaderName_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      leaderName_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int LEADERSERVERID_FIELD_NUMBER = 5;
  private long leaderServerId_ = 0L;
  /**
   * <code>optional int64 leaderServerId = 5;</code>
   * @return Whether the leaderServerId field is set.
   */
  @java.lang.Override
  public boolean hasLeaderServerId() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>optional int64 leaderServerId = 5;</code>
   * @return The leaderServerId.
   */
  @java.lang.Override
  public long getLeaderServerId() {
    return leaderServerId_;
  }

  public static final int FIGHTVALUE_FIELD_NUMBER = 6;
  @SuppressWarnings("serial")
  private volatile java.lang.Object fightValue_ = "";
  /**
   * <code>optional string fightValue = 6;</code>
   * @return Whether the fightValue field is set.
   */
  @java.lang.Override
  public boolean hasFightValue() {
    return ((bitField0_ & 0x00000020) != 0);
  }
  /**
   * <code>optional string fightValue = 6;</code>
   * @return The fightValue.
   */
  @java.lang.Override
  public java.lang.String getFightValue() {
    java.lang.Object ref = fightValue_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        fightValue_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string fightValue = 6;</code>
   * @return The bytes for fightValue.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getFightValueBytes() {
    java.lang.Object ref = fightValue_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      fightValue_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CREATETIME_FIELD_NUMBER = 7;
  private long createTime_ = 0L;
  /**
   * <code>optional int64 createTime = 7;</code>
   * @return Whether the createTime field is set.
   */
  @java.lang.Override
  public boolean hasCreateTime() {
    return ((bitField0_ & 0x00000040) != 0);
  }
  /**
   * <code>optional int64 createTime = 7;</code>
   * @return The createTime.
   */
  @java.lang.Override
  public long getCreateTime() {
    return createTime_;
  }

  public static final int MEMBERS_FIELD_NUMBER = 8;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.MountainSeaOutterMemberEntity> members_;
  /**
   * <code>repeated .xddq.pb.MountainSeaOutterMemberEntity members = 8;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.MountainSeaOutterMemberEntity> getMembersList() {
    return members_;
  }
  /**
   * <code>repeated .xddq.pb.MountainSeaOutterMemberEntity members = 8;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.MountainSeaOutterMemberEntityOrBuilder> 
      getMembersOrBuilderList() {
    return members_;
  }
  /**
   * <code>repeated .xddq.pb.MountainSeaOutterMemberEntity members = 8;</code>
   */
  @java.lang.Override
  public int getMembersCount() {
    return members_.size();
  }
  /**
   * <code>repeated .xddq.pb.MountainSeaOutterMemberEntity members = 8;</code>
   */
  @java.lang.Override
  public xddq.pb.MountainSeaOutterMemberEntity getMembers(int index) {
    return members_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.MountainSeaOutterMemberEntity members = 8;</code>
   */
  @java.lang.Override
  public xddq.pb.MountainSeaOutterMemberEntityOrBuilder getMembersOrBuilder(
      int index) {
    return members_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt64(1, teamId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 2, teamName_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt64(3, leaderId_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 4, leaderName_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      output.writeInt64(5, leaderServerId_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 6, fightValue_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      output.writeInt64(7, createTime_);
    }
    for (int i = 0; i < members_.size(); i++) {
      output.writeMessage(8, members_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(1, teamId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(2, teamName_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(3, leaderId_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(4, leaderName_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(5, leaderServerId_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(6, fightValue_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(7, createTime_);
    }
    for (int i = 0; i < members_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(8, members_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.MountainSeaOutterTeamEntity)) {
      return super.equals(obj);
    }
    xddq.pb.MountainSeaOutterTeamEntity other = (xddq.pb.MountainSeaOutterTeamEntity) obj;

    if (hasTeamId() != other.hasTeamId()) return false;
    if (hasTeamId()) {
      if (getTeamId()
          != other.getTeamId()) return false;
    }
    if (hasTeamName() != other.hasTeamName()) return false;
    if (hasTeamName()) {
      if (!getTeamName()
          .equals(other.getTeamName())) return false;
    }
    if (hasLeaderId() != other.hasLeaderId()) return false;
    if (hasLeaderId()) {
      if (getLeaderId()
          != other.getLeaderId()) return false;
    }
    if (hasLeaderName() != other.hasLeaderName()) return false;
    if (hasLeaderName()) {
      if (!getLeaderName()
          .equals(other.getLeaderName())) return false;
    }
    if (hasLeaderServerId() != other.hasLeaderServerId()) return false;
    if (hasLeaderServerId()) {
      if (getLeaderServerId()
          != other.getLeaderServerId()) return false;
    }
    if (hasFightValue() != other.hasFightValue()) return false;
    if (hasFightValue()) {
      if (!getFightValue()
          .equals(other.getFightValue())) return false;
    }
    if (hasCreateTime() != other.hasCreateTime()) return false;
    if (hasCreateTime()) {
      if (getCreateTime()
          != other.getCreateTime()) return false;
    }
    if (!getMembersList()
        .equals(other.getMembersList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasTeamId()) {
      hash = (37 * hash) + TEAMID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTeamId());
    }
    if (hasTeamName()) {
      hash = (37 * hash) + TEAMNAME_FIELD_NUMBER;
      hash = (53 * hash) + getTeamName().hashCode();
    }
    if (hasLeaderId()) {
      hash = (37 * hash) + LEADERID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getLeaderId());
    }
    if (hasLeaderName()) {
      hash = (37 * hash) + LEADERNAME_FIELD_NUMBER;
      hash = (53 * hash) + getLeaderName().hashCode();
    }
    if (hasLeaderServerId()) {
      hash = (37 * hash) + LEADERSERVERID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getLeaderServerId());
    }
    if (hasFightValue()) {
      hash = (37 * hash) + FIGHTVALUE_FIELD_NUMBER;
      hash = (53 * hash) + getFightValue().hashCode();
    }
    if (hasCreateTime()) {
      hash = (37 * hash) + CREATETIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getCreateTime());
    }
    if (getMembersCount() > 0) {
      hash = (37 * hash) + MEMBERS_FIELD_NUMBER;
      hash = (53 * hash) + getMembersList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.MountainSeaOutterTeamEntity parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.MountainSeaOutterTeamEntity parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.MountainSeaOutterTeamEntity parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.MountainSeaOutterTeamEntity parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.MountainSeaOutterTeamEntity parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.MountainSeaOutterTeamEntity parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.MountainSeaOutterTeamEntity parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.MountainSeaOutterTeamEntity parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.MountainSeaOutterTeamEntity parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.MountainSeaOutterTeamEntity parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.MountainSeaOutterTeamEntity parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.MountainSeaOutterTeamEntity parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.MountainSeaOutterTeamEntity prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.MountainSeaOutterTeamEntity}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.MountainSeaOutterTeamEntity)
      xddq.pb.MountainSeaOutterTeamEntityOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_MountainSeaOutterTeamEntity_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_MountainSeaOutterTeamEntity_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.MountainSeaOutterTeamEntity.class, xddq.pb.MountainSeaOutterTeamEntity.Builder.class);
    }

    // Construct using xddq.pb.MountainSeaOutterTeamEntity.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      teamId_ = 0L;
      teamName_ = "";
      leaderId_ = 0L;
      leaderName_ = "";
      leaderServerId_ = 0L;
      fightValue_ = "";
      createTime_ = 0L;
      if (membersBuilder_ == null) {
        members_ = java.util.Collections.emptyList();
      } else {
        members_ = null;
        membersBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000080);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_MountainSeaOutterTeamEntity_descriptor;
    }

    @java.lang.Override
    public xddq.pb.MountainSeaOutterTeamEntity getDefaultInstanceForType() {
      return xddq.pb.MountainSeaOutterTeamEntity.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.MountainSeaOutterTeamEntity build() {
      xddq.pb.MountainSeaOutterTeamEntity result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.MountainSeaOutterTeamEntity buildPartial() {
      xddq.pb.MountainSeaOutterTeamEntity result = new xddq.pb.MountainSeaOutterTeamEntity(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.MountainSeaOutterTeamEntity result) {
      if (membersBuilder_ == null) {
        if (((bitField0_ & 0x00000080) != 0)) {
          members_ = java.util.Collections.unmodifiableList(members_);
          bitField0_ = (bitField0_ & ~0x00000080);
        }
        result.members_ = members_;
      } else {
        result.members_ = membersBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.MountainSeaOutterTeamEntity result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.teamId_ = teamId_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.teamName_ = teamName_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.leaderId_ = leaderId_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.leaderName_ = leaderName_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.leaderServerId_ = leaderServerId_;
        to_bitField0_ |= 0x00000010;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.fightValue_ = fightValue_;
        to_bitField0_ |= 0x00000020;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.createTime_ = createTime_;
        to_bitField0_ |= 0x00000040;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.MountainSeaOutterTeamEntity) {
        return mergeFrom((xddq.pb.MountainSeaOutterTeamEntity)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.MountainSeaOutterTeamEntity other) {
      if (other == xddq.pb.MountainSeaOutterTeamEntity.getDefaultInstance()) return this;
      if (other.hasTeamId()) {
        setTeamId(other.getTeamId());
      }
      if (other.hasTeamName()) {
        teamName_ = other.teamName_;
        bitField0_ |= 0x00000002;
        onChanged();
      }
      if (other.hasLeaderId()) {
        setLeaderId(other.getLeaderId());
      }
      if (other.hasLeaderName()) {
        leaderName_ = other.leaderName_;
        bitField0_ |= 0x00000008;
        onChanged();
      }
      if (other.hasLeaderServerId()) {
        setLeaderServerId(other.getLeaderServerId());
      }
      if (other.hasFightValue()) {
        fightValue_ = other.fightValue_;
        bitField0_ |= 0x00000020;
        onChanged();
      }
      if (other.hasCreateTime()) {
        setCreateTime(other.getCreateTime());
      }
      if (membersBuilder_ == null) {
        if (!other.members_.isEmpty()) {
          if (members_.isEmpty()) {
            members_ = other.members_;
            bitField0_ = (bitField0_ & ~0x00000080);
          } else {
            ensureMembersIsMutable();
            members_.addAll(other.members_);
          }
          onChanged();
        }
      } else {
        if (!other.members_.isEmpty()) {
          if (membersBuilder_.isEmpty()) {
            membersBuilder_.dispose();
            membersBuilder_ = null;
            members_ = other.members_;
            bitField0_ = (bitField0_ & ~0x00000080);
            membersBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetMembersFieldBuilder() : null;
          } else {
            membersBuilder_.addAllMessages(other.members_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              teamId_ = input.readInt64();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              teamName_ = input.readBytes();
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 24: {
              leaderId_ = input.readInt64();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 34: {
              leaderName_ = input.readBytes();
              bitField0_ |= 0x00000008;
              break;
            } // case 34
            case 40: {
              leaderServerId_ = input.readInt64();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            case 50: {
              fightValue_ = input.readBytes();
              bitField0_ |= 0x00000020;
              break;
            } // case 50
            case 56: {
              createTime_ = input.readInt64();
              bitField0_ |= 0x00000040;
              break;
            } // case 56
            case 66: {
              xddq.pb.MountainSeaOutterMemberEntity m =
                  input.readMessage(
                      xddq.pb.MountainSeaOutterMemberEntity.parser(),
                      extensionRegistry);
              if (membersBuilder_ == null) {
                ensureMembersIsMutable();
                members_.add(m);
              } else {
                membersBuilder_.addMessage(m);
              }
              break;
            } // case 66
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private long teamId_ ;
    /**
     * <code>optional int64 teamId = 1;</code>
     * @return Whether the teamId field is set.
     */
    @java.lang.Override
    public boolean hasTeamId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 teamId = 1;</code>
     * @return The teamId.
     */
    @java.lang.Override
    public long getTeamId() {
      return teamId_;
    }
    /**
     * <code>optional int64 teamId = 1;</code>
     * @param value The teamId to set.
     * @return This builder for chaining.
     */
    public Builder setTeamId(long value) {

      teamId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 teamId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearTeamId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      teamId_ = 0L;
      onChanged();
      return this;
    }

    private java.lang.Object teamName_ = "";
    /**
     * <code>optional string teamName = 2;</code>
     * @return Whether the teamName field is set.
     */
    public boolean hasTeamName() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional string teamName = 2;</code>
     * @return The teamName.
     */
    public java.lang.String getTeamName() {
      java.lang.Object ref = teamName_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          teamName_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string teamName = 2;</code>
     * @return The bytes for teamName.
     */
    public com.google.protobuf.ByteString
        getTeamNameBytes() {
      java.lang.Object ref = teamName_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        teamName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string teamName = 2;</code>
     * @param value The teamName to set.
     * @return This builder for chaining.
     */
    public Builder setTeamName(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      teamName_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional string teamName = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearTeamName() {
      teamName_ = getDefaultInstance().getTeamName();
      bitField0_ = (bitField0_ & ~0x00000002);
      onChanged();
      return this;
    }
    /**
     * <code>optional string teamName = 2;</code>
     * @param value The bytes for teamName to set.
     * @return This builder for chaining.
     */
    public Builder setTeamNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      teamName_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }

    private long leaderId_ ;
    /**
     * <code>optional int64 leaderId = 3;</code>
     * @return Whether the leaderId field is set.
     */
    @java.lang.Override
    public boolean hasLeaderId() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int64 leaderId = 3;</code>
     * @return The leaderId.
     */
    @java.lang.Override
    public long getLeaderId() {
      return leaderId_;
    }
    /**
     * <code>optional int64 leaderId = 3;</code>
     * @param value The leaderId to set.
     * @return This builder for chaining.
     */
    public Builder setLeaderId(long value) {

      leaderId_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 leaderId = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearLeaderId() {
      bitField0_ = (bitField0_ & ~0x00000004);
      leaderId_ = 0L;
      onChanged();
      return this;
    }

    private java.lang.Object leaderName_ = "";
    /**
     * <code>optional string leaderName = 4;</code>
     * @return Whether the leaderName field is set.
     */
    public boolean hasLeaderName() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional string leaderName = 4;</code>
     * @return The leaderName.
     */
    public java.lang.String getLeaderName() {
      java.lang.Object ref = leaderName_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          leaderName_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string leaderName = 4;</code>
     * @return The bytes for leaderName.
     */
    public com.google.protobuf.ByteString
        getLeaderNameBytes() {
      java.lang.Object ref = leaderName_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        leaderName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string leaderName = 4;</code>
     * @param value The leaderName to set.
     * @return This builder for chaining.
     */
    public Builder setLeaderName(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      leaderName_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional string leaderName = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearLeaderName() {
      leaderName_ = getDefaultInstance().getLeaderName();
      bitField0_ = (bitField0_ & ~0x00000008);
      onChanged();
      return this;
    }
    /**
     * <code>optional string leaderName = 4;</code>
     * @param value The bytes for leaderName to set.
     * @return This builder for chaining.
     */
    public Builder setLeaderNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      leaderName_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }

    private long leaderServerId_ ;
    /**
     * <code>optional int64 leaderServerId = 5;</code>
     * @return Whether the leaderServerId field is set.
     */
    @java.lang.Override
    public boolean hasLeaderServerId() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional int64 leaderServerId = 5;</code>
     * @return The leaderServerId.
     */
    @java.lang.Override
    public long getLeaderServerId() {
      return leaderServerId_;
    }
    /**
     * <code>optional int64 leaderServerId = 5;</code>
     * @param value The leaderServerId to set.
     * @return This builder for chaining.
     */
    public Builder setLeaderServerId(long value) {

      leaderServerId_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 leaderServerId = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearLeaderServerId() {
      bitField0_ = (bitField0_ & ~0x00000010);
      leaderServerId_ = 0L;
      onChanged();
      return this;
    }

    private java.lang.Object fightValue_ = "";
    /**
     * <code>optional string fightValue = 6;</code>
     * @return Whether the fightValue field is set.
     */
    public boolean hasFightValue() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional string fightValue = 6;</code>
     * @return The fightValue.
     */
    public java.lang.String getFightValue() {
      java.lang.Object ref = fightValue_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          fightValue_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string fightValue = 6;</code>
     * @return The bytes for fightValue.
     */
    public com.google.protobuf.ByteString
        getFightValueBytes() {
      java.lang.Object ref = fightValue_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        fightValue_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string fightValue = 6;</code>
     * @param value The fightValue to set.
     * @return This builder for chaining.
     */
    public Builder setFightValue(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      fightValue_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>optional string fightValue = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearFightValue() {
      fightValue_ = getDefaultInstance().getFightValue();
      bitField0_ = (bitField0_ & ~0x00000020);
      onChanged();
      return this;
    }
    /**
     * <code>optional string fightValue = 6;</code>
     * @param value The bytes for fightValue to set.
     * @return This builder for chaining.
     */
    public Builder setFightValueBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      fightValue_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }

    private long createTime_ ;
    /**
     * <code>optional int64 createTime = 7;</code>
     * @return Whether the createTime field is set.
     */
    @java.lang.Override
    public boolean hasCreateTime() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <code>optional int64 createTime = 7;</code>
     * @return The createTime.
     */
    @java.lang.Override
    public long getCreateTime() {
      return createTime_;
    }
    /**
     * <code>optional int64 createTime = 7;</code>
     * @param value The createTime to set.
     * @return This builder for chaining.
     */
    public Builder setCreateTime(long value) {

      createTime_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 createTime = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearCreateTime() {
      bitField0_ = (bitField0_ & ~0x00000040);
      createTime_ = 0L;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.MountainSeaOutterMemberEntity> members_ =
      java.util.Collections.emptyList();
    private void ensureMembersIsMutable() {
      if (!((bitField0_ & 0x00000080) != 0)) {
        members_ = new java.util.ArrayList<xddq.pb.MountainSeaOutterMemberEntity>(members_);
        bitField0_ |= 0x00000080;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.MountainSeaOutterMemberEntity, xddq.pb.MountainSeaOutterMemberEntity.Builder, xddq.pb.MountainSeaOutterMemberEntityOrBuilder> membersBuilder_;

    /**
     * <code>repeated .xddq.pb.MountainSeaOutterMemberEntity members = 8;</code>
     */
    public java.util.List<xddq.pb.MountainSeaOutterMemberEntity> getMembersList() {
      if (membersBuilder_ == null) {
        return java.util.Collections.unmodifiableList(members_);
      } else {
        return membersBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.MountainSeaOutterMemberEntity members = 8;</code>
     */
    public int getMembersCount() {
      if (membersBuilder_ == null) {
        return members_.size();
      } else {
        return membersBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.MountainSeaOutterMemberEntity members = 8;</code>
     */
    public xddq.pb.MountainSeaOutterMemberEntity getMembers(int index) {
      if (membersBuilder_ == null) {
        return members_.get(index);
      } else {
        return membersBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.MountainSeaOutterMemberEntity members = 8;</code>
     */
    public Builder setMembers(
        int index, xddq.pb.MountainSeaOutterMemberEntity value) {
      if (membersBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMembersIsMutable();
        members_.set(index, value);
        onChanged();
      } else {
        membersBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MountainSeaOutterMemberEntity members = 8;</code>
     */
    public Builder setMembers(
        int index, xddq.pb.MountainSeaOutterMemberEntity.Builder builderForValue) {
      if (membersBuilder_ == null) {
        ensureMembersIsMutable();
        members_.set(index, builderForValue.build());
        onChanged();
      } else {
        membersBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MountainSeaOutterMemberEntity members = 8;</code>
     */
    public Builder addMembers(xddq.pb.MountainSeaOutterMemberEntity value) {
      if (membersBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMembersIsMutable();
        members_.add(value);
        onChanged();
      } else {
        membersBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MountainSeaOutterMemberEntity members = 8;</code>
     */
    public Builder addMembers(
        int index, xddq.pb.MountainSeaOutterMemberEntity value) {
      if (membersBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMembersIsMutable();
        members_.add(index, value);
        onChanged();
      } else {
        membersBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MountainSeaOutterMemberEntity members = 8;</code>
     */
    public Builder addMembers(
        xddq.pb.MountainSeaOutterMemberEntity.Builder builderForValue) {
      if (membersBuilder_ == null) {
        ensureMembersIsMutable();
        members_.add(builderForValue.build());
        onChanged();
      } else {
        membersBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MountainSeaOutterMemberEntity members = 8;</code>
     */
    public Builder addMembers(
        int index, xddq.pb.MountainSeaOutterMemberEntity.Builder builderForValue) {
      if (membersBuilder_ == null) {
        ensureMembersIsMutable();
        members_.add(index, builderForValue.build());
        onChanged();
      } else {
        membersBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MountainSeaOutterMemberEntity members = 8;</code>
     */
    public Builder addAllMembers(
        java.lang.Iterable<? extends xddq.pb.MountainSeaOutterMemberEntity> values) {
      if (membersBuilder_ == null) {
        ensureMembersIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, members_);
        onChanged();
      } else {
        membersBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MountainSeaOutterMemberEntity members = 8;</code>
     */
    public Builder clearMembers() {
      if (membersBuilder_ == null) {
        members_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000080);
        onChanged();
      } else {
        membersBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MountainSeaOutterMemberEntity members = 8;</code>
     */
    public Builder removeMembers(int index) {
      if (membersBuilder_ == null) {
        ensureMembersIsMutable();
        members_.remove(index);
        onChanged();
      } else {
        membersBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MountainSeaOutterMemberEntity members = 8;</code>
     */
    public xddq.pb.MountainSeaOutterMemberEntity.Builder getMembersBuilder(
        int index) {
      return internalGetMembersFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.MountainSeaOutterMemberEntity members = 8;</code>
     */
    public xddq.pb.MountainSeaOutterMemberEntityOrBuilder getMembersOrBuilder(
        int index) {
      if (membersBuilder_ == null) {
        return members_.get(index);  } else {
        return membersBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.MountainSeaOutterMemberEntity members = 8;</code>
     */
    public java.util.List<? extends xddq.pb.MountainSeaOutterMemberEntityOrBuilder> 
         getMembersOrBuilderList() {
      if (membersBuilder_ != null) {
        return membersBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(members_);
      }
    }
    /**
     * <code>repeated .xddq.pb.MountainSeaOutterMemberEntity members = 8;</code>
     */
    public xddq.pb.MountainSeaOutterMemberEntity.Builder addMembersBuilder() {
      return internalGetMembersFieldBuilder().addBuilder(
          xddq.pb.MountainSeaOutterMemberEntity.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.MountainSeaOutterMemberEntity members = 8;</code>
     */
    public xddq.pb.MountainSeaOutterMemberEntity.Builder addMembersBuilder(
        int index) {
      return internalGetMembersFieldBuilder().addBuilder(
          index, xddq.pb.MountainSeaOutterMemberEntity.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.MountainSeaOutterMemberEntity members = 8;</code>
     */
    public java.util.List<xddq.pb.MountainSeaOutterMemberEntity.Builder> 
         getMembersBuilderList() {
      return internalGetMembersFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.MountainSeaOutterMemberEntity, xddq.pb.MountainSeaOutterMemberEntity.Builder, xddq.pb.MountainSeaOutterMemberEntityOrBuilder> 
        internalGetMembersFieldBuilder() {
      if (membersBuilder_ == null) {
        membersBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.MountainSeaOutterMemberEntity, xddq.pb.MountainSeaOutterMemberEntity.Builder, xddq.pb.MountainSeaOutterMemberEntityOrBuilder>(
                members_,
                ((bitField0_ & 0x00000080) != 0),
                getParentForChildren(),
                isClean());
        members_ = null;
      }
      return membersBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.MountainSeaOutterTeamEntity)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.MountainSeaOutterTeamEntity)
  private static final xddq.pb.MountainSeaOutterTeamEntity DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.MountainSeaOutterTeamEntity();
  }

  public static xddq.pb.MountainSeaOutterTeamEntity getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<MountainSeaOutterTeamEntity>
      PARSER = new com.google.protobuf.AbstractParser<MountainSeaOutterTeamEntity>() {
    @java.lang.Override
    public MountainSeaOutterTeamEntity parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<MountainSeaOutterTeamEntity> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<MountainSeaOutterTeamEntity> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.MountainSeaOutterTeamEntity getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

