// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.Pharmacy2MakeDishRespMsg}
 */
public final class Pharmacy2MakeDishRespMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.Pharmacy2MakeDishRespMsg)
    Pharmacy2MakeDishRespMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      Pharmacy2MakeDishRespMsg.class.getName());
  }
  // Use Pharmacy2MakeDishRespMsg.newBuilder() to construct.
  private Pharmacy2MakeDishRespMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private Pharmacy2MakeDishRespMsg() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_Pharmacy2MakeDishRespMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_Pharmacy2MakeDishRespMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.Pharmacy2MakeDishRespMsg.class, xddq.pb.Pharmacy2MakeDishRespMsg.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int SELFDISH_FIELD_NUMBER = 2;
  private xddq.pb.Pharmacy2SelfDishSyncMsg selfDish_;
  /**
   * <code>optional .xddq.pb.Pharmacy2SelfDishSyncMsg selfDish = 2;</code>
   * @return Whether the selfDish field is set.
   */
  @java.lang.Override
  public boolean hasSelfDish() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional .xddq.pb.Pharmacy2SelfDishSyncMsg selfDish = 2;</code>
   * @return The selfDish.
   */
  @java.lang.Override
  public xddq.pb.Pharmacy2SelfDishSyncMsg getSelfDish() {
    return selfDish_ == null ? xddq.pb.Pharmacy2SelfDishSyncMsg.getDefaultInstance() : selfDish_;
  }
  /**
   * <code>optional .xddq.pb.Pharmacy2SelfDishSyncMsg selfDish = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.Pharmacy2SelfDishSyncMsgOrBuilder getSelfDishOrBuilder() {
    return selfDish_ == null ? xddq.pb.Pharmacy2SelfDishSyncMsg.getDefaultInstance() : selfDish_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (hasSelfDish()) {
      if (!getSelfDish().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeMessage(2, getSelfDish());
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getSelfDish());
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.Pharmacy2MakeDishRespMsg)) {
      return super.equals(obj);
    }
    xddq.pb.Pharmacy2MakeDishRespMsg other = (xddq.pb.Pharmacy2MakeDishRespMsg) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (hasSelfDish() != other.hasSelfDish()) return false;
    if (hasSelfDish()) {
      if (!getSelfDish()
          .equals(other.getSelfDish())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (hasSelfDish()) {
      hash = (37 * hash) + SELFDISH_FIELD_NUMBER;
      hash = (53 * hash) + getSelfDish().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.Pharmacy2MakeDishRespMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.Pharmacy2MakeDishRespMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.Pharmacy2MakeDishRespMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.Pharmacy2MakeDishRespMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.Pharmacy2MakeDishRespMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.Pharmacy2MakeDishRespMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.Pharmacy2MakeDishRespMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.Pharmacy2MakeDishRespMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.Pharmacy2MakeDishRespMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.Pharmacy2MakeDishRespMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.Pharmacy2MakeDishRespMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.Pharmacy2MakeDishRespMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.Pharmacy2MakeDishRespMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.Pharmacy2MakeDishRespMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.Pharmacy2MakeDishRespMsg)
      xddq.pb.Pharmacy2MakeDishRespMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_Pharmacy2MakeDishRespMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_Pharmacy2MakeDishRespMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.Pharmacy2MakeDishRespMsg.class, xddq.pb.Pharmacy2MakeDishRespMsg.Builder.class);
    }

    // Construct using xddq.pb.Pharmacy2MakeDishRespMsg.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetSelfDishFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      selfDish_ = null;
      if (selfDishBuilder_ != null) {
        selfDishBuilder_.dispose();
        selfDishBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_Pharmacy2MakeDishRespMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.Pharmacy2MakeDishRespMsg getDefaultInstanceForType() {
      return xddq.pb.Pharmacy2MakeDishRespMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.Pharmacy2MakeDishRespMsg build() {
      xddq.pb.Pharmacy2MakeDishRespMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.Pharmacy2MakeDishRespMsg buildPartial() {
      xddq.pb.Pharmacy2MakeDishRespMsg result = new xddq.pb.Pharmacy2MakeDishRespMsg(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.Pharmacy2MakeDishRespMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.selfDish_ = selfDishBuilder_ == null
            ? selfDish_
            : selfDishBuilder_.build();
        to_bitField0_ |= 0x00000002;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.Pharmacy2MakeDishRespMsg) {
        return mergeFrom((xddq.pb.Pharmacy2MakeDishRespMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.Pharmacy2MakeDishRespMsg other) {
      if (other == xddq.pb.Pharmacy2MakeDishRespMsg.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (other.hasSelfDish()) {
        mergeSelfDish(other.getSelfDish());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      if (hasSelfDish()) {
        if (!getSelfDish().isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              input.readMessage(
                  internalGetSelfDishFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private xddq.pb.Pharmacy2SelfDishSyncMsg selfDish_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.Pharmacy2SelfDishSyncMsg, xddq.pb.Pharmacy2SelfDishSyncMsg.Builder, xddq.pb.Pharmacy2SelfDishSyncMsgOrBuilder> selfDishBuilder_;
    /**
     * <code>optional .xddq.pb.Pharmacy2SelfDishSyncMsg selfDish = 2;</code>
     * @return Whether the selfDish field is set.
     */
    public boolean hasSelfDish() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .xddq.pb.Pharmacy2SelfDishSyncMsg selfDish = 2;</code>
     * @return The selfDish.
     */
    public xddq.pb.Pharmacy2SelfDishSyncMsg getSelfDish() {
      if (selfDishBuilder_ == null) {
        return selfDish_ == null ? xddq.pb.Pharmacy2SelfDishSyncMsg.getDefaultInstance() : selfDish_;
      } else {
        return selfDishBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.Pharmacy2SelfDishSyncMsg selfDish = 2;</code>
     */
    public Builder setSelfDish(xddq.pb.Pharmacy2SelfDishSyncMsg value) {
      if (selfDishBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        selfDish_ = value;
      } else {
        selfDishBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.Pharmacy2SelfDishSyncMsg selfDish = 2;</code>
     */
    public Builder setSelfDish(
        xddq.pb.Pharmacy2SelfDishSyncMsg.Builder builderForValue) {
      if (selfDishBuilder_ == null) {
        selfDish_ = builderForValue.build();
      } else {
        selfDishBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.Pharmacy2SelfDishSyncMsg selfDish = 2;</code>
     */
    public Builder mergeSelfDish(xddq.pb.Pharmacy2SelfDishSyncMsg value) {
      if (selfDishBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0) &&
          selfDish_ != null &&
          selfDish_ != xddq.pb.Pharmacy2SelfDishSyncMsg.getDefaultInstance()) {
          getSelfDishBuilder().mergeFrom(value);
        } else {
          selfDish_ = value;
        }
      } else {
        selfDishBuilder_.mergeFrom(value);
      }
      if (selfDish_ != null) {
        bitField0_ |= 0x00000002;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.Pharmacy2SelfDishSyncMsg selfDish = 2;</code>
     */
    public Builder clearSelfDish() {
      bitField0_ = (bitField0_ & ~0x00000002);
      selfDish_ = null;
      if (selfDishBuilder_ != null) {
        selfDishBuilder_.dispose();
        selfDishBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.Pharmacy2SelfDishSyncMsg selfDish = 2;</code>
     */
    public xddq.pb.Pharmacy2SelfDishSyncMsg.Builder getSelfDishBuilder() {
      bitField0_ |= 0x00000002;
      onChanged();
      return internalGetSelfDishFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.Pharmacy2SelfDishSyncMsg selfDish = 2;</code>
     */
    public xddq.pb.Pharmacy2SelfDishSyncMsgOrBuilder getSelfDishOrBuilder() {
      if (selfDishBuilder_ != null) {
        return selfDishBuilder_.getMessageOrBuilder();
      } else {
        return selfDish_ == null ?
            xddq.pb.Pharmacy2SelfDishSyncMsg.getDefaultInstance() : selfDish_;
      }
    }
    /**
     * <code>optional .xddq.pb.Pharmacy2SelfDishSyncMsg selfDish = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.Pharmacy2SelfDishSyncMsg, xddq.pb.Pharmacy2SelfDishSyncMsg.Builder, xddq.pb.Pharmacy2SelfDishSyncMsgOrBuilder> 
        internalGetSelfDishFieldBuilder() {
      if (selfDishBuilder_ == null) {
        selfDishBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.Pharmacy2SelfDishSyncMsg, xddq.pb.Pharmacy2SelfDishSyncMsg.Builder, xddq.pb.Pharmacy2SelfDishSyncMsgOrBuilder>(
                getSelfDish(),
                getParentForChildren(),
                isClean());
        selfDish_ = null;
      }
      return selfDishBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.Pharmacy2MakeDishRespMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.Pharmacy2MakeDishRespMsg)
  private static final xddq.pb.Pharmacy2MakeDishRespMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.Pharmacy2MakeDishRespMsg();
  }

  public static xddq.pb.Pharmacy2MakeDishRespMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<Pharmacy2MakeDishRespMsg>
      PARSER = new com.google.protobuf.AbstractParser<Pharmacy2MakeDishRespMsg>() {
    @java.lang.Override
    public Pharmacy2MakeDishRespMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<Pharmacy2MakeDishRespMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<Pharmacy2MakeDishRespMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.Pharmacy2MakeDishRespMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

