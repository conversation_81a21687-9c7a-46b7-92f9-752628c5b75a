// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.BallGVGNoticeUserTemp}
 */
public final class BallGVGNoticeUserTemp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.BallGVGNoticeUserTemp)
    BallGVGNoticeUserTempOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      BallGVGNoticeUserTemp.class.getName());
  }
  // Use BallGVGNoticeUserTemp.newBuilder() to construct.
  private BallGVGNoticeUserTemp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private BallGVGNoticeUserTemp() {
    nickName_ = "";
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_BallGVGNoticeUserTemp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_BallGVGNoticeUserTemp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.BallGVGNoticeUserTemp.class, xddq.pb.BallGVGNoticeUserTemp.Builder.class);
  }

  private int bitField0_;
  public static final int NICKNAME_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private volatile java.lang.Object nickName_ = "";
  /**
   * <code>optional string nickName = 1;</code>
   * @return Whether the nickName field is set.
   */
  @java.lang.Override
  public boolean hasNickName() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional string nickName = 1;</code>
   * @return The nickName.
   */
  @java.lang.Override
  public java.lang.String getNickName() {
    java.lang.Object ref = nickName_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        nickName_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string nickName = 1;</code>
   * @return The bytes for nickName.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getNickNameBytes() {
    java.lang.Object ref = nickName_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      nickName_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int REALMSID_FIELD_NUMBER = 2;
  private int realmsId_ = 0;
  /**
   * <code>optional int32 realmsId = 2;</code>
   * @return Whether the realmsId field is set.
   */
  @java.lang.Override
  public boolean hasRealmsId() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int32 realmsId = 2;</code>
   * @return The realmsId.
   */
  @java.lang.Override
  public int getRealmsId() {
    return realmsId_;
  }

  public static final int HEADINFO_FIELD_NUMBER = 3;
  private xddq.pb.PlayerHeadDataMsg headInfo_;
  /**
   * <code>optional .xddq.pb.PlayerHeadDataMsg headInfo = 3;</code>
   * @return Whether the headInfo field is set.
   */
  @java.lang.Override
  public boolean hasHeadInfo() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional .xddq.pb.PlayerHeadDataMsg headInfo = 3;</code>
   * @return The headInfo.
   */
  @java.lang.Override
  public xddq.pb.PlayerHeadDataMsg getHeadInfo() {
    return headInfo_ == null ? xddq.pb.PlayerHeadDataMsg.getDefaultInstance() : headInfo_;
  }
  /**
   * <code>optional .xddq.pb.PlayerHeadDataMsg headInfo = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.PlayerHeadDataMsgOrBuilder getHeadInfoOrBuilder() {
    return headInfo_ == null ? xddq.pb.PlayerHeadDataMsg.getDefaultInstance() : headInfo_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 1, nickName_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(2, realmsId_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeMessage(3, getHeadInfo());
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(1, nickName_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, realmsId_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, getHeadInfo());
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.BallGVGNoticeUserTemp)) {
      return super.equals(obj);
    }
    xddq.pb.BallGVGNoticeUserTemp other = (xddq.pb.BallGVGNoticeUserTemp) obj;

    if (hasNickName() != other.hasNickName()) return false;
    if (hasNickName()) {
      if (!getNickName()
          .equals(other.getNickName())) return false;
    }
    if (hasRealmsId() != other.hasRealmsId()) return false;
    if (hasRealmsId()) {
      if (getRealmsId()
          != other.getRealmsId()) return false;
    }
    if (hasHeadInfo() != other.hasHeadInfo()) return false;
    if (hasHeadInfo()) {
      if (!getHeadInfo()
          .equals(other.getHeadInfo())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasNickName()) {
      hash = (37 * hash) + NICKNAME_FIELD_NUMBER;
      hash = (53 * hash) + getNickName().hashCode();
    }
    if (hasRealmsId()) {
      hash = (37 * hash) + REALMSID_FIELD_NUMBER;
      hash = (53 * hash) + getRealmsId();
    }
    if (hasHeadInfo()) {
      hash = (37 * hash) + HEADINFO_FIELD_NUMBER;
      hash = (53 * hash) + getHeadInfo().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.BallGVGNoticeUserTemp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.BallGVGNoticeUserTemp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.BallGVGNoticeUserTemp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.BallGVGNoticeUserTemp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.BallGVGNoticeUserTemp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.BallGVGNoticeUserTemp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.BallGVGNoticeUserTemp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.BallGVGNoticeUserTemp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.BallGVGNoticeUserTemp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.BallGVGNoticeUserTemp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.BallGVGNoticeUserTemp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.BallGVGNoticeUserTemp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.BallGVGNoticeUserTemp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.BallGVGNoticeUserTemp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.BallGVGNoticeUserTemp)
      xddq.pb.BallGVGNoticeUserTempOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_BallGVGNoticeUserTemp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_BallGVGNoticeUserTemp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.BallGVGNoticeUserTemp.class, xddq.pb.BallGVGNoticeUserTemp.Builder.class);
    }

    // Construct using xddq.pb.BallGVGNoticeUserTemp.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetHeadInfoFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      nickName_ = "";
      realmsId_ = 0;
      headInfo_ = null;
      if (headInfoBuilder_ != null) {
        headInfoBuilder_.dispose();
        headInfoBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_BallGVGNoticeUserTemp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.BallGVGNoticeUserTemp getDefaultInstanceForType() {
      return xddq.pb.BallGVGNoticeUserTemp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.BallGVGNoticeUserTemp build() {
      xddq.pb.BallGVGNoticeUserTemp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.BallGVGNoticeUserTemp buildPartial() {
      xddq.pb.BallGVGNoticeUserTemp result = new xddq.pb.BallGVGNoticeUserTemp(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.BallGVGNoticeUserTemp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.nickName_ = nickName_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.realmsId_ = realmsId_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.headInfo_ = headInfoBuilder_ == null
            ? headInfo_
            : headInfoBuilder_.build();
        to_bitField0_ |= 0x00000004;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.BallGVGNoticeUserTemp) {
        return mergeFrom((xddq.pb.BallGVGNoticeUserTemp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.BallGVGNoticeUserTemp other) {
      if (other == xddq.pb.BallGVGNoticeUserTemp.getDefaultInstance()) return this;
      if (other.hasNickName()) {
        nickName_ = other.nickName_;
        bitField0_ |= 0x00000001;
        onChanged();
      }
      if (other.hasRealmsId()) {
        setRealmsId(other.getRealmsId());
      }
      if (other.hasHeadInfo()) {
        mergeHeadInfo(other.getHeadInfo());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              nickName_ = input.readBytes();
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 16: {
              realmsId_ = input.readInt32();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 26: {
              input.readMessage(
                  internalGetHeadInfoFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.lang.Object nickName_ = "";
    /**
     * <code>optional string nickName = 1;</code>
     * @return Whether the nickName field is set.
     */
    public boolean hasNickName() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional string nickName = 1;</code>
     * @return The nickName.
     */
    public java.lang.String getNickName() {
      java.lang.Object ref = nickName_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          nickName_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string nickName = 1;</code>
     * @return The bytes for nickName.
     */
    public com.google.protobuf.ByteString
        getNickNameBytes() {
      java.lang.Object ref = nickName_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        nickName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string nickName = 1;</code>
     * @param value The nickName to set.
     * @return This builder for chaining.
     */
    public Builder setNickName(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      nickName_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional string nickName = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearNickName() {
      nickName_ = getDefaultInstance().getNickName();
      bitField0_ = (bitField0_ & ~0x00000001);
      onChanged();
      return this;
    }
    /**
     * <code>optional string nickName = 1;</code>
     * @param value The bytes for nickName to set.
     * @return This builder for chaining.
     */
    public Builder setNickNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      nickName_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }

    private int realmsId_ ;
    /**
     * <code>optional int32 realmsId = 2;</code>
     * @return Whether the realmsId field is set.
     */
    @java.lang.Override
    public boolean hasRealmsId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 realmsId = 2;</code>
     * @return The realmsId.
     */
    @java.lang.Override
    public int getRealmsId() {
      return realmsId_;
    }
    /**
     * <code>optional int32 realmsId = 2;</code>
     * @param value The realmsId to set.
     * @return This builder for chaining.
     */
    public Builder setRealmsId(int value) {

      realmsId_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 realmsId = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearRealmsId() {
      bitField0_ = (bitField0_ & ~0x00000002);
      realmsId_ = 0;
      onChanged();
      return this;
    }

    private xddq.pb.PlayerHeadDataMsg headInfo_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PlayerHeadDataMsg, xddq.pb.PlayerHeadDataMsg.Builder, xddq.pb.PlayerHeadDataMsgOrBuilder> headInfoBuilder_;
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg headInfo = 3;</code>
     * @return Whether the headInfo field is set.
     */
    public boolean hasHeadInfo() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg headInfo = 3;</code>
     * @return The headInfo.
     */
    public xddq.pb.PlayerHeadDataMsg getHeadInfo() {
      if (headInfoBuilder_ == null) {
        return headInfo_ == null ? xddq.pb.PlayerHeadDataMsg.getDefaultInstance() : headInfo_;
      } else {
        return headInfoBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg headInfo = 3;</code>
     */
    public Builder setHeadInfo(xddq.pb.PlayerHeadDataMsg value) {
      if (headInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        headInfo_ = value;
      } else {
        headInfoBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg headInfo = 3;</code>
     */
    public Builder setHeadInfo(
        xddq.pb.PlayerHeadDataMsg.Builder builderForValue) {
      if (headInfoBuilder_ == null) {
        headInfo_ = builderForValue.build();
      } else {
        headInfoBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg headInfo = 3;</code>
     */
    public Builder mergeHeadInfo(xddq.pb.PlayerHeadDataMsg value) {
      if (headInfoBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0) &&
          headInfo_ != null &&
          headInfo_ != xddq.pb.PlayerHeadDataMsg.getDefaultInstance()) {
          getHeadInfoBuilder().mergeFrom(value);
        } else {
          headInfo_ = value;
        }
      } else {
        headInfoBuilder_.mergeFrom(value);
      }
      if (headInfo_ != null) {
        bitField0_ |= 0x00000004;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg headInfo = 3;</code>
     */
    public Builder clearHeadInfo() {
      bitField0_ = (bitField0_ & ~0x00000004);
      headInfo_ = null;
      if (headInfoBuilder_ != null) {
        headInfoBuilder_.dispose();
        headInfoBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg headInfo = 3;</code>
     */
    public xddq.pb.PlayerHeadDataMsg.Builder getHeadInfoBuilder() {
      bitField0_ |= 0x00000004;
      onChanged();
      return internalGetHeadInfoFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg headInfo = 3;</code>
     */
    public xddq.pb.PlayerHeadDataMsgOrBuilder getHeadInfoOrBuilder() {
      if (headInfoBuilder_ != null) {
        return headInfoBuilder_.getMessageOrBuilder();
      } else {
        return headInfo_ == null ?
            xddq.pb.PlayerHeadDataMsg.getDefaultInstance() : headInfo_;
      }
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg headInfo = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PlayerHeadDataMsg, xddq.pb.PlayerHeadDataMsg.Builder, xddq.pb.PlayerHeadDataMsgOrBuilder> 
        internalGetHeadInfoFieldBuilder() {
      if (headInfoBuilder_ == null) {
        headInfoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.PlayerHeadDataMsg, xddq.pb.PlayerHeadDataMsg.Builder, xddq.pb.PlayerHeadDataMsgOrBuilder>(
                getHeadInfo(),
                getParentForChildren(),
                isClean());
        headInfo_ = null;
      }
      return headInfoBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.BallGVGNoticeUserTemp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.BallGVGNoticeUserTemp)
  private static final xddq.pb.BallGVGNoticeUserTemp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.BallGVGNoticeUserTemp();
  }

  public static xddq.pb.BallGVGNoticeUserTemp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<BallGVGNoticeUserTemp>
      PARSER = new com.google.protobuf.AbstractParser<BallGVGNoticeUserTemp>() {
    @java.lang.Override
    public BallGVGNoticeUserTemp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<BallGVGNoticeUserTemp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<BallGVGNoticeUserTemp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.BallGVGNoticeUserTemp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

