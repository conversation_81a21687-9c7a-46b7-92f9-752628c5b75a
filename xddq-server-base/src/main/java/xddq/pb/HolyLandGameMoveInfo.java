// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.HolyLandGameMoveInfo}
 */
public final class HolyLandGameMoveInfo extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.HolyLandGameMoveInfo)
    HolyLandGameMoveInfoOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      HolyLandGameMoveInfo.class.getName());
  }
  // Use HolyLandGameMoveInfo.newBuilder() to construct.
  private HolyLandGameMoveInfo(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private HolyLandGameMoveInfo() {
    name_ = "";
    unionName_ = "";
    wxHeadUrl_ = "";
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_HolyLandGameMoveInfo_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_HolyLandGameMoveInfo_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.HolyLandGameMoveInfo.class, xddq.pb.HolyLandGameMoveInfo.Builder.class);
  }

  private int bitField0_;
  public static final int PLAYERID_FIELD_NUMBER = 1;
  private long playerId_ = 0L;
  /**
   * <code>optional int64 playerId = 1;</code>
   * @return Whether the playerId field is set.
   */
  @java.lang.Override
  public boolean hasPlayerId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int64 playerId = 1;</code>
   * @return The playerId.
   */
  @java.lang.Override
  public long getPlayerId() {
    return playerId_;
  }

  public static final int NAME_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private volatile java.lang.Object name_ = "";
  /**
   * <code>optional string name = 2;</code>
   * @return Whether the name field is set.
   */
  @java.lang.Override
  public boolean hasName() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional string name = 2;</code>
   * @return The name.
   */
  @java.lang.Override
  public java.lang.String getName() {
    java.lang.Object ref = name_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        name_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string name = 2;</code>
   * @return The bytes for name.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getNameBytes() {
    java.lang.Object ref = name_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      name_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int UNIONID_FIELD_NUMBER = 3;
  private long unionId_ = 0L;
  /**
   * <code>optional int64 unionId = 3;</code>
   * @return Whether the unionId field is set.
   */
  @java.lang.Override
  public boolean hasUnionId() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional int64 unionId = 3;</code>
   * @return The unionId.
   */
  @java.lang.Override
  public long getUnionId() {
    return unionId_;
  }

  public static final int SERVERID_FIELD_NUMBER = 4;
  private long serverId_ = 0L;
  /**
   * <code>optional int64 serverId = 4;</code>
   * @return Whether the serverId field is set.
   */
  @java.lang.Override
  public boolean hasServerId() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional int64 serverId = 4;</code>
   * @return The serverId.
   */
  @java.lang.Override
  public long getServerId() {
    return serverId_;
  }

  public static final int UNIONNAME_FIELD_NUMBER = 5;
  @SuppressWarnings("serial")
  private volatile java.lang.Object unionName_ = "";
  /**
   * <code>optional string unionName = 5;</code>
   * @return Whether the unionName field is set.
   */
  @java.lang.Override
  public boolean hasUnionName() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>optional string unionName = 5;</code>
   * @return The unionName.
   */
  @java.lang.Override
  public java.lang.String getUnionName() {
    java.lang.Object ref = unionName_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        unionName_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string unionName = 5;</code>
   * @return The bytes for unionName.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getUnionNameBytes() {
    java.lang.Object ref = unionName_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      unionName_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int TOCITYID_FIELD_NUMBER = 6;
  private int toCityId_ = 0;
  /**
   * <code>optional int32 toCityId = 6;</code>
   * @return Whether the toCityId field is set.
   */
  @java.lang.Override
  public boolean hasToCityId() {
    return ((bitField0_ & 0x00000020) != 0);
  }
  /**
   * <code>optional int32 toCityId = 6;</code>
   * @return The toCityId.
   */
  @java.lang.Override
  public int getToCityId() {
    return toCityId_;
  }

  public static final int ARRIVAL_FIELD_NUMBER = 7;
  private long arrival_ = 0L;
  /**
   * <code>optional int64 arrival = 7;</code>
   * @return Whether the arrival field is set.
   */
  @java.lang.Override
  public boolean hasArrival() {
    return ((bitField0_ & 0x00000040) != 0);
  }
  /**
   * <code>optional int64 arrival = 7;</code>
   * @return The arrival.
   */
  @java.lang.Override
  public long getArrival() {
    return arrival_;
  }

  public static final int HEADICON_FIELD_NUMBER = 8;
  private int headIcon_ = 0;
  /**
   * <code>optional int32 headIcon = 8;</code>
   * @return Whether the headIcon field is set.
   */
  @java.lang.Override
  public boolean hasHeadIcon() {
    return ((bitField0_ & 0x00000080) != 0);
  }
  /**
   * <code>optional int32 headIcon = 8;</code>
   * @return The headIcon.
   */
  @java.lang.Override
  public int getHeadIcon() {
    return headIcon_;
  }

  public static final int WXHEADURL_FIELD_NUMBER = 9;
  @SuppressWarnings("serial")
  private volatile java.lang.Object wxHeadUrl_ = "";
  /**
   * <code>optional string wxHeadUrl = 9;</code>
   * @return Whether the wxHeadUrl field is set.
   */
  @java.lang.Override
  public boolean hasWxHeadUrl() {
    return ((bitField0_ & 0x00000100) != 0);
  }
  /**
   * <code>optional string wxHeadUrl = 9;</code>
   * @return The wxHeadUrl.
   */
  @java.lang.Override
  public java.lang.String getWxHeadUrl() {
    java.lang.Object ref = wxHeadUrl_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        wxHeadUrl_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string wxHeadUrl = 9;</code>
   * @return The bytes for wxHeadUrl.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getWxHeadUrlBytes() {
    java.lang.Object ref = wxHeadUrl_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      wxHeadUrl_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt64(1, playerId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 2, name_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt64(3, unionId_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt64(4, serverId_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 5, unionName_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      output.writeInt32(6, toCityId_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      output.writeInt64(7, arrival_);
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      output.writeInt32(8, headIcon_);
    }
    if (((bitField0_ & 0x00000100) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 9, wxHeadUrl_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(1, playerId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(2, name_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(3, unionId_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(4, serverId_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(5, unionName_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(6, toCityId_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(7, arrival_);
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(8, headIcon_);
    }
    if (((bitField0_ & 0x00000100) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(9, wxHeadUrl_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.HolyLandGameMoveInfo)) {
      return super.equals(obj);
    }
    xddq.pb.HolyLandGameMoveInfo other = (xddq.pb.HolyLandGameMoveInfo) obj;

    if (hasPlayerId() != other.hasPlayerId()) return false;
    if (hasPlayerId()) {
      if (getPlayerId()
          != other.getPlayerId()) return false;
    }
    if (hasName() != other.hasName()) return false;
    if (hasName()) {
      if (!getName()
          .equals(other.getName())) return false;
    }
    if (hasUnionId() != other.hasUnionId()) return false;
    if (hasUnionId()) {
      if (getUnionId()
          != other.getUnionId()) return false;
    }
    if (hasServerId() != other.hasServerId()) return false;
    if (hasServerId()) {
      if (getServerId()
          != other.getServerId()) return false;
    }
    if (hasUnionName() != other.hasUnionName()) return false;
    if (hasUnionName()) {
      if (!getUnionName()
          .equals(other.getUnionName())) return false;
    }
    if (hasToCityId() != other.hasToCityId()) return false;
    if (hasToCityId()) {
      if (getToCityId()
          != other.getToCityId()) return false;
    }
    if (hasArrival() != other.hasArrival()) return false;
    if (hasArrival()) {
      if (getArrival()
          != other.getArrival()) return false;
    }
    if (hasHeadIcon() != other.hasHeadIcon()) return false;
    if (hasHeadIcon()) {
      if (getHeadIcon()
          != other.getHeadIcon()) return false;
    }
    if (hasWxHeadUrl() != other.hasWxHeadUrl()) return false;
    if (hasWxHeadUrl()) {
      if (!getWxHeadUrl()
          .equals(other.getWxHeadUrl())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasPlayerId()) {
      hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getPlayerId());
    }
    if (hasName()) {
      hash = (37 * hash) + NAME_FIELD_NUMBER;
      hash = (53 * hash) + getName().hashCode();
    }
    if (hasUnionId()) {
      hash = (37 * hash) + UNIONID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getUnionId());
    }
    if (hasServerId()) {
      hash = (37 * hash) + SERVERID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getServerId());
    }
    if (hasUnionName()) {
      hash = (37 * hash) + UNIONNAME_FIELD_NUMBER;
      hash = (53 * hash) + getUnionName().hashCode();
    }
    if (hasToCityId()) {
      hash = (37 * hash) + TOCITYID_FIELD_NUMBER;
      hash = (53 * hash) + getToCityId();
    }
    if (hasArrival()) {
      hash = (37 * hash) + ARRIVAL_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getArrival());
    }
    if (hasHeadIcon()) {
      hash = (37 * hash) + HEADICON_FIELD_NUMBER;
      hash = (53 * hash) + getHeadIcon();
    }
    if (hasWxHeadUrl()) {
      hash = (37 * hash) + WXHEADURL_FIELD_NUMBER;
      hash = (53 * hash) + getWxHeadUrl().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.HolyLandGameMoveInfo parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.HolyLandGameMoveInfo parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.HolyLandGameMoveInfo parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.HolyLandGameMoveInfo parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.HolyLandGameMoveInfo parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.HolyLandGameMoveInfo parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.HolyLandGameMoveInfo parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.HolyLandGameMoveInfo parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.HolyLandGameMoveInfo parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.HolyLandGameMoveInfo parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.HolyLandGameMoveInfo parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.HolyLandGameMoveInfo parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.HolyLandGameMoveInfo prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.HolyLandGameMoveInfo}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.HolyLandGameMoveInfo)
      xddq.pb.HolyLandGameMoveInfoOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_HolyLandGameMoveInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_HolyLandGameMoveInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.HolyLandGameMoveInfo.class, xddq.pb.HolyLandGameMoveInfo.Builder.class);
    }

    // Construct using xddq.pb.HolyLandGameMoveInfo.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      playerId_ = 0L;
      name_ = "";
      unionId_ = 0L;
      serverId_ = 0L;
      unionName_ = "";
      toCityId_ = 0;
      arrival_ = 0L;
      headIcon_ = 0;
      wxHeadUrl_ = "";
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_HolyLandGameMoveInfo_descriptor;
    }

    @java.lang.Override
    public xddq.pb.HolyLandGameMoveInfo getDefaultInstanceForType() {
      return xddq.pb.HolyLandGameMoveInfo.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.HolyLandGameMoveInfo build() {
      xddq.pb.HolyLandGameMoveInfo result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.HolyLandGameMoveInfo buildPartial() {
      xddq.pb.HolyLandGameMoveInfo result = new xddq.pb.HolyLandGameMoveInfo(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.HolyLandGameMoveInfo result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.playerId_ = playerId_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.name_ = name_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.unionId_ = unionId_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.serverId_ = serverId_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.unionName_ = unionName_;
        to_bitField0_ |= 0x00000010;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.toCityId_ = toCityId_;
        to_bitField0_ |= 0x00000020;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.arrival_ = arrival_;
        to_bitField0_ |= 0x00000040;
      }
      if (((from_bitField0_ & 0x00000080) != 0)) {
        result.headIcon_ = headIcon_;
        to_bitField0_ |= 0x00000080;
      }
      if (((from_bitField0_ & 0x00000100) != 0)) {
        result.wxHeadUrl_ = wxHeadUrl_;
        to_bitField0_ |= 0x00000100;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.HolyLandGameMoveInfo) {
        return mergeFrom((xddq.pb.HolyLandGameMoveInfo)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.HolyLandGameMoveInfo other) {
      if (other == xddq.pb.HolyLandGameMoveInfo.getDefaultInstance()) return this;
      if (other.hasPlayerId()) {
        setPlayerId(other.getPlayerId());
      }
      if (other.hasName()) {
        name_ = other.name_;
        bitField0_ |= 0x00000002;
        onChanged();
      }
      if (other.hasUnionId()) {
        setUnionId(other.getUnionId());
      }
      if (other.hasServerId()) {
        setServerId(other.getServerId());
      }
      if (other.hasUnionName()) {
        unionName_ = other.unionName_;
        bitField0_ |= 0x00000010;
        onChanged();
      }
      if (other.hasToCityId()) {
        setToCityId(other.getToCityId());
      }
      if (other.hasArrival()) {
        setArrival(other.getArrival());
      }
      if (other.hasHeadIcon()) {
        setHeadIcon(other.getHeadIcon());
      }
      if (other.hasWxHeadUrl()) {
        wxHeadUrl_ = other.wxHeadUrl_;
        bitField0_ |= 0x00000100;
        onChanged();
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              playerId_ = input.readInt64();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              name_ = input.readBytes();
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 24: {
              unionId_ = input.readInt64();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 32: {
              serverId_ = input.readInt64();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            case 42: {
              unionName_ = input.readBytes();
              bitField0_ |= 0x00000010;
              break;
            } // case 42
            case 48: {
              toCityId_ = input.readInt32();
              bitField0_ |= 0x00000020;
              break;
            } // case 48
            case 56: {
              arrival_ = input.readInt64();
              bitField0_ |= 0x00000040;
              break;
            } // case 56
            case 64: {
              headIcon_ = input.readInt32();
              bitField0_ |= 0x00000080;
              break;
            } // case 64
            case 74: {
              wxHeadUrl_ = input.readBytes();
              bitField0_ |= 0x00000100;
              break;
            } // case 74
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private long playerId_ ;
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }
    /**
     * <code>optional int64 playerId = 1;</code>
     * @param value The playerId to set.
     * @return This builder for chaining.
     */
    public Builder setPlayerId(long value) {

      playerId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearPlayerId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      playerId_ = 0L;
      onChanged();
      return this;
    }

    private java.lang.Object name_ = "";
    /**
     * <code>optional string name = 2;</code>
     * @return Whether the name field is set.
     */
    public boolean hasName() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional string name = 2;</code>
     * @return The name.
     */
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          name_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string name = 2;</code>
     * @return The bytes for name.
     */
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string name = 2;</code>
     * @param value The name to set.
     * @return This builder for chaining.
     */
    public Builder setName(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      name_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional string name = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearName() {
      name_ = getDefaultInstance().getName();
      bitField0_ = (bitField0_ & ~0x00000002);
      onChanged();
      return this;
    }
    /**
     * <code>optional string name = 2;</code>
     * @param value The bytes for name to set.
     * @return This builder for chaining.
     */
    public Builder setNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      name_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }

    private long unionId_ ;
    /**
     * <code>optional int64 unionId = 3;</code>
     * @return Whether the unionId field is set.
     */
    @java.lang.Override
    public boolean hasUnionId() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int64 unionId = 3;</code>
     * @return The unionId.
     */
    @java.lang.Override
    public long getUnionId() {
      return unionId_;
    }
    /**
     * <code>optional int64 unionId = 3;</code>
     * @param value The unionId to set.
     * @return This builder for chaining.
     */
    public Builder setUnionId(long value) {

      unionId_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 unionId = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearUnionId() {
      bitField0_ = (bitField0_ & ~0x00000004);
      unionId_ = 0L;
      onChanged();
      return this;
    }

    private long serverId_ ;
    /**
     * <code>optional int64 serverId = 4;</code>
     * @return Whether the serverId field is set.
     */
    @java.lang.Override
    public boolean hasServerId() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int64 serverId = 4;</code>
     * @return The serverId.
     */
    @java.lang.Override
    public long getServerId() {
      return serverId_;
    }
    /**
     * <code>optional int64 serverId = 4;</code>
     * @param value The serverId to set.
     * @return This builder for chaining.
     */
    public Builder setServerId(long value) {

      serverId_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 serverId = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearServerId() {
      bitField0_ = (bitField0_ & ~0x00000008);
      serverId_ = 0L;
      onChanged();
      return this;
    }

    private java.lang.Object unionName_ = "";
    /**
     * <code>optional string unionName = 5;</code>
     * @return Whether the unionName field is set.
     */
    public boolean hasUnionName() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional string unionName = 5;</code>
     * @return The unionName.
     */
    public java.lang.String getUnionName() {
      java.lang.Object ref = unionName_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          unionName_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string unionName = 5;</code>
     * @return The bytes for unionName.
     */
    public com.google.protobuf.ByteString
        getUnionNameBytes() {
      java.lang.Object ref = unionName_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        unionName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string unionName = 5;</code>
     * @param value The unionName to set.
     * @return This builder for chaining.
     */
    public Builder setUnionName(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      unionName_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional string unionName = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearUnionName() {
      unionName_ = getDefaultInstance().getUnionName();
      bitField0_ = (bitField0_ & ~0x00000010);
      onChanged();
      return this;
    }
    /**
     * <code>optional string unionName = 5;</code>
     * @param value The bytes for unionName to set.
     * @return This builder for chaining.
     */
    public Builder setUnionNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      unionName_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }

    private int toCityId_ ;
    /**
     * <code>optional int32 toCityId = 6;</code>
     * @return Whether the toCityId field is set.
     */
    @java.lang.Override
    public boolean hasToCityId() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional int32 toCityId = 6;</code>
     * @return The toCityId.
     */
    @java.lang.Override
    public int getToCityId() {
      return toCityId_;
    }
    /**
     * <code>optional int32 toCityId = 6;</code>
     * @param value The toCityId to set.
     * @return This builder for chaining.
     */
    public Builder setToCityId(int value) {

      toCityId_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 toCityId = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearToCityId() {
      bitField0_ = (bitField0_ & ~0x00000020);
      toCityId_ = 0;
      onChanged();
      return this;
    }

    private long arrival_ ;
    /**
     * <code>optional int64 arrival = 7;</code>
     * @return Whether the arrival field is set.
     */
    @java.lang.Override
    public boolean hasArrival() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <code>optional int64 arrival = 7;</code>
     * @return The arrival.
     */
    @java.lang.Override
    public long getArrival() {
      return arrival_;
    }
    /**
     * <code>optional int64 arrival = 7;</code>
     * @param value The arrival to set.
     * @return This builder for chaining.
     */
    public Builder setArrival(long value) {

      arrival_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 arrival = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearArrival() {
      bitField0_ = (bitField0_ & ~0x00000040);
      arrival_ = 0L;
      onChanged();
      return this;
    }

    private int headIcon_ ;
    /**
     * <code>optional int32 headIcon = 8;</code>
     * @return Whether the headIcon field is set.
     */
    @java.lang.Override
    public boolean hasHeadIcon() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <code>optional int32 headIcon = 8;</code>
     * @return The headIcon.
     */
    @java.lang.Override
    public int getHeadIcon() {
      return headIcon_;
    }
    /**
     * <code>optional int32 headIcon = 8;</code>
     * @param value The headIcon to set.
     * @return This builder for chaining.
     */
    public Builder setHeadIcon(int value) {

      headIcon_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 headIcon = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearHeadIcon() {
      bitField0_ = (bitField0_ & ~0x00000080);
      headIcon_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object wxHeadUrl_ = "";
    /**
     * <code>optional string wxHeadUrl = 9;</code>
     * @return Whether the wxHeadUrl field is set.
     */
    public boolean hasWxHeadUrl() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <code>optional string wxHeadUrl = 9;</code>
     * @return The wxHeadUrl.
     */
    public java.lang.String getWxHeadUrl() {
      java.lang.Object ref = wxHeadUrl_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          wxHeadUrl_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string wxHeadUrl = 9;</code>
     * @return The bytes for wxHeadUrl.
     */
    public com.google.protobuf.ByteString
        getWxHeadUrlBytes() {
      java.lang.Object ref = wxHeadUrl_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        wxHeadUrl_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string wxHeadUrl = 9;</code>
     * @param value The wxHeadUrl to set.
     * @return This builder for chaining.
     */
    public Builder setWxHeadUrl(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      wxHeadUrl_ = value;
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }
    /**
     * <code>optional string wxHeadUrl = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearWxHeadUrl() {
      wxHeadUrl_ = getDefaultInstance().getWxHeadUrl();
      bitField0_ = (bitField0_ & ~0x00000100);
      onChanged();
      return this;
    }
    /**
     * <code>optional string wxHeadUrl = 9;</code>
     * @param value The bytes for wxHeadUrl to set.
     * @return This builder for chaining.
     */
    public Builder setWxHeadUrlBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      wxHeadUrl_ = value;
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.HolyLandGameMoveInfo)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.HolyLandGameMoveInfo)
  private static final xddq.pb.HolyLandGameMoveInfo DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.HolyLandGameMoveInfo();
  }

  public static xddq.pb.HolyLandGameMoveInfo getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<HolyLandGameMoveInfo>
      PARSER = new com.google.protobuf.AbstractParser<HolyLandGameMoveInfo>() {
    @java.lang.Override
    public HolyLandGameMoveInfo parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<HolyLandGameMoveInfo> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<HolyLandGameMoveInfo> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.HolyLandGameMoveInfo getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

