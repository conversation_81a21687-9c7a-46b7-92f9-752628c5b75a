// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.SecretRealmSealedDemonUseBombItemResp}
 */
public final class SecretRealmSealedDemonUseBombItemResp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.SecretRealmSealedDemonUseBombItemResp)
    SecretRealmSealedDemonUseBombItemRespOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      SecretRealmSealedDemonUseBombItemResp.class.getName());
  }
  // Use SecretRealmSealedDemonUseBombItemResp.newBuilder() to construct.
  private SecretRealmSealedDemonUseBombItemResp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private SecretRealmSealedDemonUseBombItemResp() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_SecretRealmSealedDemonUseBombItemResp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_SecretRealmSealedDemonUseBombItemResp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.SecretRealmSealedDemonUseBombItemResp.class, xddq.pb.SecretRealmSealedDemonUseBombItemResp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int ENERGYINFOMSG_FIELD_NUMBER = 2;
  private xddq.pb.SecretRealmSealedDemonEnergyInfoMsg energyInfoMsg_;
  /**
   * <code>optional .xddq.pb.SecretRealmSealedDemonEnergyInfoMsg energyInfoMsg = 2;</code>
   * @return Whether the energyInfoMsg field is set.
   */
  @java.lang.Override
  public boolean hasEnergyInfoMsg() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional .xddq.pb.SecretRealmSealedDemonEnergyInfoMsg energyInfoMsg = 2;</code>
   * @return The energyInfoMsg.
   */
  @java.lang.Override
  public xddq.pb.SecretRealmSealedDemonEnergyInfoMsg getEnergyInfoMsg() {
    return energyInfoMsg_ == null ? xddq.pb.SecretRealmSealedDemonEnergyInfoMsg.getDefaultInstance() : energyInfoMsg_;
  }
  /**
   * <code>optional .xddq.pb.SecretRealmSealedDemonEnergyInfoMsg energyInfoMsg = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.SecretRealmSealedDemonEnergyInfoMsgOrBuilder getEnergyInfoMsgOrBuilder() {
    return energyInfoMsg_ == null ? xddq.pb.SecretRealmSealedDemonEnergyInfoMsg.getDefaultInstance() : energyInfoMsg_;
  }

  public static final int BLOCKMSG_FIELD_NUMBER = 3;
  private xddq.pb.SecretRealmSealedDemonBlockMsg blockMsg_;
  /**
   * <code>optional .xddq.pb.SecretRealmSealedDemonBlockMsg blockMsg = 3;</code>
   * @return Whether the blockMsg field is set.
   */
  @java.lang.Override
  public boolean hasBlockMsg() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional .xddq.pb.SecretRealmSealedDemonBlockMsg blockMsg = 3;</code>
   * @return The blockMsg.
   */
  @java.lang.Override
  public xddq.pb.SecretRealmSealedDemonBlockMsg getBlockMsg() {
    return blockMsg_ == null ? xddq.pb.SecretRealmSealedDemonBlockMsg.getDefaultInstance() : blockMsg_;
  }
  /**
   * <code>optional .xddq.pb.SecretRealmSealedDemonBlockMsg blockMsg = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.SecretRealmSealedDemonBlockMsgOrBuilder getBlockMsgOrBuilder() {
    return blockMsg_ == null ? xddq.pb.SecretRealmSealedDemonBlockMsg.getDefaultInstance() : blockMsg_;
  }

  public static final int ITEMCOUNTMSG_FIELD_NUMBER = 4;
  private xddq.pb.SecretRealmSealedDemonItemCountMsg itemCountMsg_;
  /**
   * <code>optional .xddq.pb.SecretRealmSealedDemonItemCountMsg itemCountMsg = 4;</code>
   * @return Whether the itemCountMsg field is set.
   */
  @java.lang.Override
  public boolean hasItemCountMsg() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional .xddq.pb.SecretRealmSealedDemonItemCountMsg itemCountMsg = 4;</code>
   * @return The itemCountMsg.
   */
  @java.lang.Override
  public xddq.pb.SecretRealmSealedDemonItemCountMsg getItemCountMsg() {
    return itemCountMsg_ == null ? xddq.pb.SecretRealmSealedDemonItemCountMsg.getDefaultInstance() : itemCountMsg_;
  }
  /**
   * <code>optional .xddq.pb.SecretRealmSealedDemonItemCountMsg itemCountMsg = 4;</code>
   */
  @java.lang.Override
  public xddq.pb.SecretRealmSealedDemonItemCountMsgOrBuilder getItemCountMsgOrBuilder() {
    return itemCountMsg_ == null ? xddq.pb.SecretRealmSealedDemonItemCountMsg.getDefaultInstance() : itemCountMsg_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (hasEnergyInfoMsg()) {
      if (!getEnergyInfoMsg().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    if (hasBlockMsg()) {
      if (!getBlockMsg().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    if (hasItemCountMsg()) {
      if (!getItemCountMsg().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeMessage(2, getEnergyInfoMsg());
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeMessage(3, getBlockMsg());
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeMessage(4, getItemCountMsg());
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getEnergyInfoMsg());
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, getBlockMsg());
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(4, getItemCountMsg());
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.SecretRealmSealedDemonUseBombItemResp)) {
      return super.equals(obj);
    }
    xddq.pb.SecretRealmSealedDemonUseBombItemResp other = (xddq.pb.SecretRealmSealedDemonUseBombItemResp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (hasEnergyInfoMsg() != other.hasEnergyInfoMsg()) return false;
    if (hasEnergyInfoMsg()) {
      if (!getEnergyInfoMsg()
          .equals(other.getEnergyInfoMsg())) return false;
    }
    if (hasBlockMsg() != other.hasBlockMsg()) return false;
    if (hasBlockMsg()) {
      if (!getBlockMsg()
          .equals(other.getBlockMsg())) return false;
    }
    if (hasItemCountMsg() != other.hasItemCountMsg()) return false;
    if (hasItemCountMsg()) {
      if (!getItemCountMsg()
          .equals(other.getItemCountMsg())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (hasEnergyInfoMsg()) {
      hash = (37 * hash) + ENERGYINFOMSG_FIELD_NUMBER;
      hash = (53 * hash) + getEnergyInfoMsg().hashCode();
    }
    if (hasBlockMsg()) {
      hash = (37 * hash) + BLOCKMSG_FIELD_NUMBER;
      hash = (53 * hash) + getBlockMsg().hashCode();
    }
    if (hasItemCountMsg()) {
      hash = (37 * hash) + ITEMCOUNTMSG_FIELD_NUMBER;
      hash = (53 * hash) + getItemCountMsg().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.SecretRealmSealedDemonUseBombItemResp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.SecretRealmSealedDemonUseBombItemResp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.SecretRealmSealedDemonUseBombItemResp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.SecretRealmSealedDemonUseBombItemResp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.SecretRealmSealedDemonUseBombItemResp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.SecretRealmSealedDemonUseBombItemResp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.SecretRealmSealedDemonUseBombItemResp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.SecretRealmSealedDemonUseBombItemResp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.SecretRealmSealedDemonUseBombItemResp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.SecretRealmSealedDemonUseBombItemResp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.SecretRealmSealedDemonUseBombItemResp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.SecretRealmSealedDemonUseBombItemResp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.SecretRealmSealedDemonUseBombItemResp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.SecretRealmSealedDemonUseBombItemResp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.SecretRealmSealedDemonUseBombItemResp)
      xddq.pb.SecretRealmSealedDemonUseBombItemRespOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_SecretRealmSealedDemonUseBombItemResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_SecretRealmSealedDemonUseBombItemResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.SecretRealmSealedDemonUseBombItemResp.class, xddq.pb.SecretRealmSealedDemonUseBombItemResp.Builder.class);
    }

    // Construct using xddq.pb.SecretRealmSealedDemonUseBombItemResp.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetEnergyInfoMsgFieldBuilder();
        internalGetBlockMsgFieldBuilder();
        internalGetItemCountMsgFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      energyInfoMsg_ = null;
      if (energyInfoMsgBuilder_ != null) {
        energyInfoMsgBuilder_.dispose();
        energyInfoMsgBuilder_ = null;
      }
      blockMsg_ = null;
      if (blockMsgBuilder_ != null) {
        blockMsgBuilder_.dispose();
        blockMsgBuilder_ = null;
      }
      itemCountMsg_ = null;
      if (itemCountMsgBuilder_ != null) {
        itemCountMsgBuilder_.dispose();
        itemCountMsgBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_SecretRealmSealedDemonUseBombItemResp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.SecretRealmSealedDemonUseBombItemResp getDefaultInstanceForType() {
      return xddq.pb.SecretRealmSealedDemonUseBombItemResp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.SecretRealmSealedDemonUseBombItemResp build() {
      xddq.pb.SecretRealmSealedDemonUseBombItemResp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.SecretRealmSealedDemonUseBombItemResp buildPartial() {
      xddq.pb.SecretRealmSealedDemonUseBombItemResp result = new xddq.pb.SecretRealmSealedDemonUseBombItemResp(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.SecretRealmSealedDemonUseBombItemResp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.energyInfoMsg_ = energyInfoMsgBuilder_ == null
            ? energyInfoMsg_
            : energyInfoMsgBuilder_.build();
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.blockMsg_ = blockMsgBuilder_ == null
            ? blockMsg_
            : blockMsgBuilder_.build();
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.itemCountMsg_ = itemCountMsgBuilder_ == null
            ? itemCountMsg_
            : itemCountMsgBuilder_.build();
        to_bitField0_ |= 0x00000008;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.SecretRealmSealedDemonUseBombItemResp) {
        return mergeFrom((xddq.pb.SecretRealmSealedDemonUseBombItemResp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.SecretRealmSealedDemonUseBombItemResp other) {
      if (other == xddq.pb.SecretRealmSealedDemonUseBombItemResp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (other.hasEnergyInfoMsg()) {
        mergeEnergyInfoMsg(other.getEnergyInfoMsg());
      }
      if (other.hasBlockMsg()) {
        mergeBlockMsg(other.getBlockMsg());
      }
      if (other.hasItemCountMsg()) {
        mergeItemCountMsg(other.getItemCountMsg());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      if (hasEnergyInfoMsg()) {
        if (!getEnergyInfoMsg().isInitialized()) {
          return false;
        }
      }
      if (hasBlockMsg()) {
        if (!getBlockMsg().isInitialized()) {
          return false;
        }
      }
      if (hasItemCountMsg()) {
        if (!getItemCountMsg().isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              input.readMessage(
                  internalGetEnergyInfoMsgFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 26: {
              input.readMessage(
                  internalGetBlockMsgFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 34: {
              input.readMessage(
                  internalGetItemCountMsgFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000008;
              break;
            } // case 34
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private xddq.pb.SecretRealmSealedDemonEnergyInfoMsg energyInfoMsg_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.SecretRealmSealedDemonEnergyInfoMsg, xddq.pb.SecretRealmSealedDemonEnergyInfoMsg.Builder, xddq.pb.SecretRealmSealedDemonEnergyInfoMsgOrBuilder> energyInfoMsgBuilder_;
    /**
     * <code>optional .xddq.pb.SecretRealmSealedDemonEnergyInfoMsg energyInfoMsg = 2;</code>
     * @return Whether the energyInfoMsg field is set.
     */
    public boolean hasEnergyInfoMsg() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .xddq.pb.SecretRealmSealedDemonEnergyInfoMsg energyInfoMsg = 2;</code>
     * @return The energyInfoMsg.
     */
    public xddq.pb.SecretRealmSealedDemonEnergyInfoMsg getEnergyInfoMsg() {
      if (energyInfoMsgBuilder_ == null) {
        return energyInfoMsg_ == null ? xddq.pb.SecretRealmSealedDemonEnergyInfoMsg.getDefaultInstance() : energyInfoMsg_;
      } else {
        return energyInfoMsgBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.SecretRealmSealedDemonEnergyInfoMsg energyInfoMsg = 2;</code>
     */
    public Builder setEnergyInfoMsg(xddq.pb.SecretRealmSealedDemonEnergyInfoMsg value) {
      if (energyInfoMsgBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        energyInfoMsg_ = value;
      } else {
        energyInfoMsgBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.SecretRealmSealedDemonEnergyInfoMsg energyInfoMsg = 2;</code>
     */
    public Builder setEnergyInfoMsg(
        xddq.pb.SecretRealmSealedDemonEnergyInfoMsg.Builder builderForValue) {
      if (energyInfoMsgBuilder_ == null) {
        energyInfoMsg_ = builderForValue.build();
      } else {
        energyInfoMsgBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.SecretRealmSealedDemonEnergyInfoMsg energyInfoMsg = 2;</code>
     */
    public Builder mergeEnergyInfoMsg(xddq.pb.SecretRealmSealedDemonEnergyInfoMsg value) {
      if (energyInfoMsgBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0) &&
          energyInfoMsg_ != null &&
          energyInfoMsg_ != xddq.pb.SecretRealmSealedDemonEnergyInfoMsg.getDefaultInstance()) {
          getEnergyInfoMsgBuilder().mergeFrom(value);
        } else {
          energyInfoMsg_ = value;
        }
      } else {
        energyInfoMsgBuilder_.mergeFrom(value);
      }
      if (energyInfoMsg_ != null) {
        bitField0_ |= 0x00000002;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.SecretRealmSealedDemonEnergyInfoMsg energyInfoMsg = 2;</code>
     */
    public Builder clearEnergyInfoMsg() {
      bitField0_ = (bitField0_ & ~0x00000002);
      energyInfoMsg_ = null;
      if (energyInfoMsgBuilder_ != null) {
        energyInfoMsgBuilder_.dispose();
        energyInfoMsgBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.SecretRealmSealedDemonEnergyInfoMsg energyInfoMsg = 2;</code>
     */
    public xddq.pb.SecretRealmSealedDemonEnergyInfoMsg.Builder getEnergyInfoMsgBuilder() {
      bitField0_ |= 0x00000002;
      onChanged();
      return internalGetEnergyInfoMsgFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.SecretRealmSealedDemonEnergyInfoMsg energyInfoMsg = 2;</code>
     */
    public xddq.pb.SecretRealmSealedDemonEnergyInfoMsgOrBuilder getEnergyInfoMsgOrBuilder() {
      if (energyInfoMsgBuilder_ != null) {
        return energyInfoMsgBuilder_.getMessageOrBuilder();
      } else {
        return energyInfoMsg_ == null ?
            xddq.pb.SecretRealmSealedDemonEnergyInfoMsg.getDefaultInstance() : energyInfoMsg_;
      }
    }
    /**
     * <code>optional .xddq.pb.SecretRealmSealedDemonEnergyInfoMsg energyInfoMsg = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.SecretRealmSealedDemonEnergyInfoMsg, xddq.pb.SecretRealmSealedDemonEnergyInfoMsg.Builder, xddq.pb.SecretRealmSealedDemonEnergyInfoMsgOrBuilder> 
        internalGetEnergyInfoMsgFieldBuilder() {
      if (energyInfoMsgBuilder_ == null) {
        energyInfoMsgBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.SecretRealmSealedDemonEnergyInfoMsg, xddq.pb.SecretRealmSealedDemonEnergyInfoMsg.Builder, xddq.pb.SecretRealmSealedDemonEnergyInfoMsgOrBuilder>(
                getEnergyInfoMsg(),
                getParentForChildren(),
                isClean());
        energyInfoMsg_ = null;
      }
      return energyInfoMsgBuilder_;
    }

    private xddq.pb.SecretRealmSealedDemonBlockMsg blockMsg_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.SecretRealmSealedDemonBlockMsg, xddq.pb.SecretRealmSealedDemonBlockMsg.Builder, xddq.pb.SecretRealmSealedDemonBlockMsgOrBuilder> blockMsgBuilder_;
    /**
     * <code>optional .xddq.pb.SecretRealmSealedDemonBlockMsg blockMsg = 3;</code>
     * @return Whether the blockMsg field is set.
     */
    public boolean hasBlockMsg() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional .xddq.pb.SecretRealmSealedDemonBlockMsg blockMsg = 3;</code>
     * @return The blockMsg.
     */
    public xddq.pb.SecretRealmSealedDemonBlockMsg getBlockMsg() {
      if (blockMsgBuilder_ == null) {
        return blockMsg_ == null ? xddq.pb.SecretRealmSealedDemonBlockMsg.getDefaultInstance() : blockMsg_;
      } else {
        return blockMsgBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.SecretRealmSealedDemonBlockMsg blockMsg = 3;</code>
     */
    public Builder setBlockMsg(xddq.pb.SecretRealmSealedDemonBlockMsg value) {
      if (blockMsgBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        blockMsg_ = value;
      } else {
        blockMsgBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.SecretRealmSealedDemonBlockMsg blockMsg = 3;</code>
     */
    public Builder setBlockMsg(
        xddq.pb.SecretRealmSealedDemonBlockMsg.Builder builderForValue) {
      if (blockMsgBuilder_ == null) {
        blockMsg_ = builderForValue.build();
      } else {
        blockMsgBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.SecretRealmSealedDemonBlockMsg blockMsg = 3;</code>
     */
    public Builder mergeBlockMsg(xddq.pb.SecretRealmSealedDemonBlockMsg value) {
      if (blockMsgBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0) &&
          blockMsg_ != null &&
          blockMsg_ != xddq.pb.SecretRealmSealedDemonBlockMsg.getDefaultInstance()) {
          getBlockMsgBuilder().mergeFrom(value);
        } else {
          blockMsg_ = value;
        }
      } else {
        blockMsgBuilder_.mergeFrom(value);
      }
      if (blockMsg_ != null) {
        bitField0_ |= 0x00000004;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.SecretRealmSealedDemonBlockMsg blockMsg = 3;</code>
     */
    public Builder clearBlockMsg() {
      bitField0_ = (bitField0_ & ~0x00000004);
      blockMsg_ = null;
      if (blockMsgBuilder_ != null) {
        blockMsgBuilder_.dispose();
        blockMsgBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.SecretRealmSealedDemonBlockMsg blockMsg = 3;</code>
     */
    public xddq.pb.SecretRealmSealedDemonBlockMsg.Builder getBlockMsgBuilder() {
      bitField0_ |= 0x00000004;
      onChanged();
      return internalGetBlockMsgFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.SecretRealmSealedDemonBlockMsg blockMsg = 3;</code>
     */
    public xddq.pb.SecretRealmSealedDemonBlockMsgOrBuilder getBlockMsgOrBuilder() {
      if (blockMsgBuilder_ != null) {
        return blockMsgBuilder_.getMessageOrBuilder();
      } else {
        return blockMsg_ == null ?
            xddq.pb.SecretRealmSealedDemonBlockMsg.getDefaultInstance() : blockMsg_;
      }
    }
    /**
     * <code>optional .xddq.pb.SecretRealmSealedDemonBlockMsg blockMsg = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.SecretRealmSealedDemonBlockMsg, xddq.pb.SecretRealmSealedDemonBlockMsg.Builder, xddq.pb.SecretRealmSealedDemonBlockMsgOrBuilder> 
        internalGetBlockMsgFieldBuilder() {
      if (blockMsgBuilder_ == null) {
        blockMsgBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.SecretRealmSealedDemonBlockMsg, xddq.pb.SecretRealmSealedDemonBlockMsg.Builder, xddq.pb.SecretRealmSealedDemonBlockMsgOrBuilder>(
                getBlockMsg(),
                getParentForChildren(),
                isClean());
        blockMsg_ = null;
      }
      return blockMsgBuilder_;
    }

    private xddq.pb.SecretRealmSealedDemonItemCountMsg itemCountMsg_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.SecretRealmSealedDemonItemCountMsg, xddq.pb.SecretRealmSealedDemonItemCountMsg.Builder, xddq.pb.SecretRealmSealedDemonItemCountMsgOrBuilder> itemCountMsgBuilder_;
    /**
     * <code>optional .xddq.pb.SecretRealmSealedDemonItemCountMsg itemCountMsg = 4;</code>
     * @return Whether the itemCountMsg field is set.
     */
    public boolean hasItemCountMsg() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional .xddq.pb.SecretRealmSealedDemonItemCountMsg itemCountMsg = 4;</code>
     * @return The itemCountMsg.
     */
    public xddq.pb.SecretRealmSealedDemonItemCountMsg getItemCountMsg() {
      if (itemCountMsgBuilder_ == null) {
        return itemCountMsg_ == null ? xddq.pb.SecretRealmSealedDemonItemCountMsg.getDefaultInstance() : itemCountMsg_;
      } else {
        return itemCountMsgBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.SecretRealmSealedDemonItemCountMsg itemCountMsg = 4;</code>
     */
    public Builder setItemCountMsg(xddq.pb.SecretRealmSealedDemonItemCountMsg value) {
      if (itemCountMsgBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        itemCountMsg_ = value;
      } else {
        itemCountMsgBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.SecretRealmSealedDemonItemCountMsg itemCountMsg = 4;</code>
     */
    public Builder setItemCountMsg(
        xddq.pb.SecretRealmSealedDemonItemCountMsg.Builder builderForValue) {
      if (itemCountMsgBuilder_ == null) {
        itemCountMsg_ = builderForValue.build();
      } else {
        itemCountMsgBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.SecretRealmSealedDemonItemCountMsg itemCountMsg = 4;</code>
     */
    public Builder mergeItemCountMsg(xddq.pb.SecretRealmSealedDemonItemCountMsg value) {
      if (itemCountMsgBuilder_ == null) {
        if (((bitField0_ & 0x00000008) != 0) &&
          itemCountMsg_ != null &&
          itemCountMsg_ != xddq.pb.SecretRealmSealedDemonItemCountMsg.getDefaultInstance()) {
          getItemCountMsgBuilder().mergeFrom(value);
        } else {
          itemCountMsg_ = value;
        }
      } else {
        itemCountMsgBuilder_.mergeFrom(value);
      }
      if (itemCountMsg_ != null) {
        bitField0_ |= 0x00000008;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.SecretRealmSealedDemonItemCountMsg itemCountMsg = 4;</code>
     */
    public Builder clearItemCountMsg() {
      bitField0_ = (bitField0_ & ~0x00000008);
      itemCountMsg_ = null;
      if (itemCountMsgBuilder_ != null) {
        itemCountMsgBuilder_.dispose();
        itemCountMsgBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.SecretRealmSealedDemonItemCountMsg itemCountMsg = 4;</code>
     */
    public xddq.pb.SecretRealmSealedDemonItemCountMsg.Builder getItemCountMsgBuilder() {
      bitField0_ |= 0x00000008;
      onChanged();
      return internalGetItemCountMsgFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.SecretRealmSealedDemonItemCountMsg itemCountMsg = 4;</code>
     */
    public xddq.pb.SecretRealmSealedDemonItemCountMsgOrBuilder getItemCountMsgOrBuilder() {
      if (itemCountMsgBuilder_ != null) {
        return itemCountMsgBuilder_.getMessageOrBuilder();
      } else {
        return itemCountMsg_ == null ?
            xddq.pb.SecretRealmSealedDemonItemCountMsg.getDefaultInstance() : itemCountMsg_;
      }
    }
    /**
     * <code>optional .xddq.pb.SecretRealmSealedDemonItemCountMsg itemCountMsg = 4;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.SecretRealmSealedDemonItemCountMsg, xddq.pb.SecretRealmSealedDemonItemCountMsg.Builder, xddq.pb.SecretRealmSealedDemonItemCountMsgOrBuilder> 
        internalGetItemCountMsgFieldBuilder() {
      if (itemCountMsgBuilder_ == null) {
        itemCountMsgBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.SecretRealmSealedDemonItemCountMsg, xddq.pb.SecretRealmSealedDemonItemCountMsg.Builder, xddq.pb.SecretRealmSealedDemonItemCountMsgOrBuilder>(
                getItemCountMsg(),
                getParentForChildren(),
                isClean());
        itemCountMsg_ = null;
      }
      return itemCountMsgBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.SecretRealmSealedDemonUseBombItemResp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.SecretRealmSealedDemonUseBombItemResp)
  private static final xddq.pb.SecretRealmSealedDemonUseBombItemResp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.SecretRealmSealedDemonUseBombItemResp();
  }

  public static xddq.pb.SecretRealmSealedDemonUseBombItemResp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<SecretRealmSealedDemonUseBombItemResp>
      PARSER = new com.google.protobuf.AbstractParser<SecretRealmSealedDemonUseBombItemResp>() {
    @java.lang.Override
    public SecretRealmSealedDemonUseBombItemResp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<SecretRealmSealedDemonUseBombItemResp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<SecretRealmSealedDemonUseBombItemResp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.SecretRealmSealedDemonUseBombItemResp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

