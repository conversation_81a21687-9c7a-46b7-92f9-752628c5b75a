// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.RoadDefendPlateExpandResp}
 */
public final class RoadDefendPlateExpandResp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.RoadDefendPlateExpandResp)
    RoadDefendPlateExpandRespOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      RoadDefendPlateExpandResp.class.getName());
  }
  // Use RoadDefendPlateExpandResp.newBuilder() to construct.
  private RoadDefendPlateExpandResp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private RoadDefendPlateExpandResp() {
    shopGoods_ = java.util.Collections.emptyList();
    newPlateGridList_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_RoadDefendPlateExpandResp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_RoadDefendPlateExpandResp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.RoadDefendPlateExpandResp.class, xddq.pb.RoadDefendPlateExpandResp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int SHOPGOODS_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.RoadDefendGoodsItemInfo> shopGoods_;
  /**
   * <code>repeated .xddq.pb.RoadDefendGoodsItemInfo shopGoods = 2;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.RoadDefendGoodsItemInfo> getShopGoodsList() {
    return shopGoods_;
  }
  /**
   * <code>repeated .xddq.pb.RoadDefendGoodsItemInfo shopGoods = 2;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.RoadDefendGoodsItemInfoOrBuilder> 
      getShopGoodsOrBuilderList() {
    return shopGoods_;
  }
  /**
   * <code>repeated .xddq.pb.RoadDefendGoodsItemInfo shopGoods = 2;</code>
   */
  @java.lang.Override
  public int getShopGoodsCount() {
    return shopGoods_.size();
  }
  /**
   * <code>repeated .xddq.pb.RoadDefendGoodsItemInfo shopGoods = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.RoadDefendGoodsItemInfo getShopGoods(int index) {
    return shopGoods_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.RoadDefendGoodsItemInfo shopGoods = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.RoadDefendGoodsItemInfoOrBuilder getShopGoodsOrBuilder(
      int index) {
    return shopGoods_.get(index);
  }

  public static final int NEWPLATEGRIDLIST_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.RoadDefendGridData> newPlateGridList_;
  /**
   * <code>repeated .xddq.pb.RoadDefendGridData newPlateGridList = 3;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.RoadDefendGridData> getNewPlateGridListList() {
    return newPlateGridList_;
  }
  /**
   * <code>repeated .xddq.pb.RoadDefendGridData newPlateGridList = 3;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.RoadDefendGridDataOrBuilder> 
      getNewPlateGridListOrBuilderList() {
    return newPlateGridList_;
  }
  /**
   * <code>repeated .xddq.pb.RoadDefendGridData newPlateGridList = 3;</code>
   */
  @java.lang.Override
  public int getNewPlateGridListCount() {
    return newPlateGridList_.size();
  }
  /**
   * <code>repeated .xddq.pb.RoadDefendGridData newPlateGridList = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.RoadDefendGridData getNewPlateGridList(int index) {
    return newPlateGridList_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.RoadDefendGridData newPlateGridList = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.RoadDefendGridDataOrBuilder getNewPlateGridListOrBuilder(
      int index) {
    return newPlateGridList_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    for (int i = 0; i < getShopGoodsCount(); i++) {
      if (!getShopGoods(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    for (int i = 0; i < shopGoods_.size(); i++) {
      output.writeMessage(2, shopGoods_.get(i));
    }
    for (int i = 0; i < newPlateGridList_.size(); i++) {
      output.writeMessage(3, newPlateGridList_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    for (int i = 0; i < shopGoods_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, shopGoods_.get(i));
    }
    for (int i = 0; i < newPlateGridList_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, newPlateGridList_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.RoadDefendPlateExpandResp)) {
      return super.equals(obj);
    }
    xddq.pb.RoadDefendPlateExpandResp other = (xddq.pb.RoadDefendPlateExpandResp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (!getShopGoodsList()
        .equals(other.getShopGoodsList())) return false;
    if (!getNewPlateGridListList()
        .equals(other.getNewPlateGridListList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (getShopGoodsCount() > 0) {
      hash = (37 * hash) + SHOPGOODS_FIELD_NUMBER;
      hash = (53 * hash) + getShopGoodsList().hashCode();
    }
    if (getNewPlateGridListCount() > 0) {
      hash = (37 * hash) + NEWPLATEGRIDLIST_FIELD_NUMBER;
      hash = (53 * hash) + getNewPlateGridListList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.RoadDefendPlateExpandResp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.RoadDefendPlateExpandResp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.RoadDefendPlateExpandResp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.RoadDefendPlateExpandResp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.RoadDefendPlateExpandResp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.RoadDefendPlateExpandResp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.RoadDefendPlateExpandResp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.RoadDefendPlateExpandResp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.RoadDefendPlateExpandResp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.RoadDefendPlateExpandResp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.RoadDefendPlateExpandResp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.RoadDefendPlateExpandResp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.RoadDefendPlateExpandResp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.RoadDefendPlateExpandResp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.RoadDefendPlateExpandResp)
      xddq.pb.RoadDefendPlateExpandRespOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_RoadDefendPlateExpandResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_RoadDefendPlateExpandResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.RoadDefendPlateExpandResp.class, xddq.pb.RoadDefendPlateExpandResp.Builder.class);
    }

    // Construct using xddq.pb.RoadDefendPlateExpandResp.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      if (shopGoodsBuilder_ == null) {
        shopGoods_ = java.util.Collections.emptyList();
      } else {
        shopGoods_ = null;
        shopGoodsBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000002);
      if (newPlateGridListBuilder_ == null) {
        newPlateGridList_ = java.util.Collections.emptyList();
      } else {
        newPlateGridList_ = null;
        newPlateGridListBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000004);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_RoadDefendPlateExpandResp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.RoadDefendPlateExpandResp getDefaultInstanceForType() {
      return xddq.pb.RoadDefendPlateExpandResp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.RoadDefendPlateExpandResp build() {
      xddq.pb.RoadDefendPlateExpandResp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.RoadDefendPlateExpandResp buildPartial() {
      xddq.pb.RoadDefendPlateExpandResp result = new xddq.pb.RoadDefendPlateExpandResp(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.RoadDefendPlateExpandResp result) {
      if (shopGoodsBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          shopGoods_ = java.util.Collections.unmodifiableList(shopGoods_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.shopGoods_ = shopGoods_;
      } else {
        result.shopGoods_ = shopGoodsBuilder_.build();
      }
      if (newPlateGridListBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0)) {
          newPlateGridList_ = java.util.Collections.unmodifiableList(newPlateGridList_);
          bitField0_ = (bitField0_ & ~0x00000004);
        }
        result.newPlateGridList_ = newPlateGridList_;
      } else {
        result.newPlateGridList_ = newPlateGridListBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.RoadDefendPlateExpandResp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.RoadDefendPlateExpandResp) {
        return mergeFrom((xddq.pb.RoadDefendPlateExpandResp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.RoadDefendPlateExpandResp other) {
      if (other == xddq.pb.RoadDefendPlateExpandResp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (shopGoodsBuilder_ == null) {
        if (!other.shopGoods_.isEmpty()) {
          if (shopGoods_.isEmpty()) {
            shopGoods_ = other.shopGoods_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureShopGoodsIsMutable();
            shopGoods_.addAll(other.shopGoods_);
          }
          onChanged();
        }
      } else {
        if (!other.shopGoods_.isEmpty()) {
          if (shopGoodsBuilder_.isEmpty()) {
            shopGoodsBuilder_.dispose();
            shopGoodsBuilder_ = null;
            shopGoods_ = other.shopGoods_;
            bitField0_ = (bitField0_ & ~0x00000002);
            shopGoodsBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetShopGoodsFieldBuilder() : null;
          } else {
            shopGoodsBuilder_.addAllMessages(other.shopGoods_);
          }
        }
      }
      if (newPlateGridListBuilder_ == null) {
        if (!other.newPlateGridList_.isEmpty()) {
          if (newPlateGridList_.isEmpty()) {
            newPlateGridList_ = other.newPlateGridList_;
            bitField0_ = (bitField0_ & ~0x00000004);
          } else {
            ensureNewPlateGridListIsMutable();
            newPlateGridList_.addAll(other.newPlateGridList_);
          }
          onChanged();
        }
      } else {
        if (!other.newPlateGridList_.isEmpty()) {
          if (newPlateGridListBuilder_.isEmpty()) {
            newPlateGridListBuilder_.dispose();
            newPlateGridListBuilder_ = null;
            newPlateGridList_ = other.newPlateGridList_;
            bitField0_ = (bitField0_ & ~0x00000004);
            newPlateGridListBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetNewPlateGridListFieldBuilder() : null;
          } else {
            newPlateGridListBuilder_.addAllMessages(other.newPlateGridList_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      for (int i = 0; i < getShopGoodsCount(); i++) {
        if (!getShopGoods(i).isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              xddq.pb.RoadDefendGoodsItemInfo m =
                  input.readMessage(
                      xddq.pb.RoadDefendGoodsItemInfo.parser(),
                      extensionRegistry);
              if (shopGoodsBuilder_ == null) {
                ensureShopGoodsIsMutable();
                shopGoods_.add(m);
              } else {
                shopGoodsBuilder_.addMessage(m);
              }
              break;
            } // case 18
            case 26: {
              xddq.pb.RoadDefendGridData m =
                  input.readMessage(
                      xddq.pb.RoadDefendGridData.parser(),
                      extensionRegistry);
              if (newPlateGridListBuilder_ == null) {
                ensureNewPlateGridListIsMutable();
                newPlateGridList_.add(m);
              } else {
                newPlateGridListBuilder_.addMessage(m);
              }
              break;
            } // case 26
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.RoadDefendGoodsItemInfo> shopGoods_ =
      java.util.Collections.emptyList();
    private void ensureShopGoodsIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        shopGoods_ = new java.util.ArrayList<xddq.pb.RoadDefendGoodsItemInfo>(shopGoods_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.RoadDefendGoodsItemInfo, xddq.pb.RoadDefendGoodsItemInfo.Builder, xddq.pb.RoadDefendGoodsItemInfoOrBuilder> shopGoodsBuilder_;

    /**
     * <code>repeated .xddq.pb.RoadDefendGoodsItemInfo shopGoods = 2;</code>
     */
    public java.util.List<xddq.pb.RoadDefendGoodsItemInfo> getShopGoodsList() {
      if (shopGoodsBuilder_ == null) {
        return java.util.Collections.unmodifiableList(shopGoods_);
      } else {
        return shopGoodsBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.RoadDefendGoodsItemInfo shopGoods = 2;</code>
     */
    public int getShopGoodsCount() {
      if (shopGoodsBuilder_ == null) {
        return shopGoods_.size();
      } else {
        return shopGoodsBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.RoadDefendGoodsItemInfo shopGoods = 2;</code>
     */
    public xddq.pb.RoadDefendGoodsItemInfo getShopGoods(int index) {
      if (shopGoodsBuilder_ == null) {
        return shopGoods_.get(index);
      } else {
        return shopGoodsBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.RoadDefendGoodsItemInfo shopGoods = 2;</code>
     */
    public Builder setShopGoods(
        int index, xddq.pb.RoadDefendGoodsItemInfo value) {
      if (shopGoodsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureShopGoodsIsMutable();
        shopGoods_.set(index, value);
        onChanged();
      } else {
        shopGoodsBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.RoadDefendGoodsItemInfo shopGoods = 2;</code>
     */
    public Builder setShopGoods(
        int index, xddq.pb.RoadDefendGoodsItemInfo.Builder builderForValue) {
      if (shopGoodsBuilder_ == null) {
        ensureShopGoodsIsMutable();
        shopGoods_.set(index, builderForValue.build());
        onChanged();
      } else {
        shopGoodsBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.RoadDefendGoodsItemInfo shopGoods = 2;</code>
     */
    public Builder addShopGoods(xddq.pb.RoadDefendGoodsItemInfo value) {
      if (shopGoodsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureShopGoodsIsMutable();
        shopGoods_.add(value);
        onChanged();
      } else {
        shopGoodsBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.RoadDefendGoodsItemInfo shopGoods = 2;</code>
     */
    public Builder addShopGoods(
        int index, xddq.pb.RoadDefendGoodsItemInfo value) {
      if (shopGoodsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureShopGoodsIsMutable();
        shopGoods_.add(index, value);
        onChanged();
      } else {
        shopGoodsBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.RoadDefendGoodsItemInfo shopGoods = 2;</code>
     */
    public Builder addShopGoods(
        xddq.pb.RoadDefendGoodsItemInfo.Builder builderForValue) {
      if (shopGoodsBuilder_ == null) {
        ensureShopGoodsIsMutable();
        shopGoods_.add(builderForValue.build());
        onChanged();
      } else {
        shopGoodsBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.RoadDefendGoodsItemInfo shopGoods = 2;</code>
     */
    public Builder addShopGoods(
        int index, xddq.pb.RoadDefendGoodsItemInfo.Builder builderForValue) {
      if (shopGoodsBuilder_ == null) {
        ensureShopGoodsIsMutable();
        shopGoods_.add(index, builderForValue.build());
        onChanged();
      } else {
        shopGoodsBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.RoadDefendGoodsItemInfo shopGoods = 2;</code>
     */
    public Builder addAllShopGoods(
        java.lang.Iterable<? extends xddq.pb.RoadDefendGoodsItemInfo> values) {
      if (shopGoodsBuilder_ == null) {
        ensureShopGoodsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, shopGoods_);
        onChanged();
      } else {
        shopGoodsBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.RoadDefendGoodsItemInfo shopGoods = 2;</code>
     */
    public Builder clearShopGoods() {
      if (shopGoodsBuilder_ == null) {
        shopGoods_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        shopGoodsBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.RoadDefendGoodsItemInfo shopGoods = 2;</code>
     */
    public Builder removeShopGoods(int index) {
      if (shopGoodsBuilder_ == null) {
        ensureShopGoodsIsMutable();
        shopGoods_.remove(index);
        onChanged();
      } else {
        shopGoodsBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.RoadDefendGoodsItemInfo shopGoods = 2;</code>
     */
    public xddq.pb.RoadDefendGoodsItemInfo.Builder getShopGoodsBuilder(
        int index) {
      return internalGetShopGoodsFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.RoadDefendGoodsItemInfo shopGoods = 2;</code>
     */
    public xddq.pb.RoadDefendGoodsItemInfoOrBuilder getShopGoodsOrBuilder(
        int index) {
      if (shopGoodsBuilder_ == null) {
        return shopGoods_.get(index);  } else {
        return shopGoodsBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.RoadDefendGoodsItemInfo shopGoods = 2;</code>
     */
    public java.util.List<? extends xddq.pb.RoadDefendGoodsItemInfoOrBuilder> 
         getShopGoodsOrBuilderList() {
      if (shopGoodsBuilder_ != null) {
        return shopGoodsBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(shopGoods_);
      }
    }
    /**
     * <code>repeated .xddq.pb.RoadDefendGoodsItemInfo shopGoods = 2;</code>
     */
    public xddq.pb.RoadDefendGoodsItemInfo.Builder addShopGoodsBuilder() {
      return internalGetShopGoodsFieldBuilder().addBuilder(
          xddq.pb.RoadDefendGoodsItemInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.RoadDefendGoodsItemInfo shopGoods = 2;</code>
     */
    public xddq.pb.RoadDefendGoodsItemInfo.Builder addShopGoodsBuilder(
        int index) {
      return internalGetShopGoodsFieldBuilder().addBuilder(
          index, xddq.pb.RoadDefendGoodsItemInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.RoadDefendGoodsItemInfo shopGoods = 2;</code>
     */
    public java.util.List<xddq.pb.RoadDefendGoodsItemInfo.Builder> 
         getShopGoodsBuilderList() {
      return internalGetShopGoodsFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.RoadDefendGoodsItemInfo, xddq.pb.RoadDefendGoodsItemInfo.Builder, xddq.pb.RoadDefendGoodsItemInfoOrBuilder> 
        internalGetShopGoodsFieldBuilder() {
      if (shopGoodsBuilder_ == null) {
        shopGoodsBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.RoadDefendGoodsItemInfo, xddq.pb.RoadDefendGoodsItemInfo.Builder, xddq.pb.RoadDefendGoodsItemInfoOrBuilder>(
                shopGoods_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        shopGoods_ = null;
      }
      return shopGoodsBuilder_;
    }

    private java.util.List<xddq.pb.RoadDefendGridData> newPlateGridList_ =
      java.util.Collections.emptyList();
    private void ensureNewPlateGridListIsMutable() {
      if (!((bitField0_ & 0x00000004) != 0)) {
        newPlateGridList_ = new java.util.ArrayList<xddq.pb.RoadDefendGridData>(newPlateGridList_);
        bitField0_ |= 0x00000004;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.RoadDefendGridData, xddq.pb.RoadDefendGridData.Builder, xddq.pb.RoadDefendGridDataOrBuilder> newPlateGridListBuilder_;

    /**
     * <code>repeated .xddq.pb.RoadDefendGridData newPlateGridList = 3;</code>
     */
    public java.util.List<xddq.pb.RoadDefendGridData> getNewPlateGridListList() {
      if (newPlateGridListBuilder_ == null) {
        return java.util.Collections.unmodifiableList(newPlateGridList_);
      } else {
        return newPlateGridListBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.RoadDefendGridData newPlateGridList = 3;</code>
     */
    public int getNewPlateGridListCount() {
      if (newPlateGridListBuilder_ == null) {
        return newPlateGridList_.size();
      } else {
        return newPlateGridListBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.RoadDefendGridData newPlateGridList = 3;</code>
     */
    public xddq.pb.RoadDefendGridData getNewPlateGridList(int index) {
      if (newPlateGridListBuilder_ == null) {
        return newPlateGridList_.get(index);
      } else {
        return newPlateGridListBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.RoadDefendGridData newPlateGridList = 3;</code>
     */
    public Builder setNewPlateGridList(
        int index, xddq.pb.RoadDefendGridData value) {
      if (newPlateGridListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureNewPlateGridListIsMutable();
        newPlateGridList_.set(index, value);
        onChanged();
      } else {
        newPlateGridListBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.RoadDefendGridData newPlateGridList = 3;</code>
     */
    public Builder setNewPlateGridList(
        int index, xddq.pb.RoadDefendGridData.Builder builderForValue) {
      if (newPlateGridListBuilder_ == null) {
        ensureNewPlateGridListIsMutable();
        newPlateGridList_.set(index, builderForValue.build());
        onChanged();
      } else {
        newPlateGridListBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.RoadDefendGridData newPlateGridList = 3;</code>
     */
    public Builder addNewPlateGridList(xddq.pb.RoadDefendGridData value) {
      if (newPlateGridListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureNewPlateGridListIsMutable();
        newPlateGridList_.add(value);
        onChanged();
      } else {
        newPlateGridListBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.RoadDefendGridData newPlateGridList = 3;</code>
     */
    public Builder addNewPlateGridList(
        int index, xddq.pb.RoadDefendGridData value) {
      if (newPlateGridListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureNewPlateGridListIsMutable();
        newPlateGridList_.add(index, value);
        onChanged();
      } else {
        newPlateGridListBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.RoadDefendGridData newPlateGridList = 3;</code>
     */
    public Builder addNewPlateGridList(
        xddq.pb.RoadDefendGridData.Builder builderForValue) {
      if (newPlateGridListBuilder_ == null) {
        ensureNewPlateGridListIsMutable();
        newPlateGridList_.add(builderForValue.build());
        onChanged();
      } else {
        newPlateGridListBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.RoadDefendGridData newPlateGridList = 3;</code>
     */
    public Builder addNewPlateGridList(
        int index, xddq.pb.RoadDefendGridData.Builder builderForValue) {
      if (newPlateGridListBuilder_ == null) {
        ensureNewPlateGridListIsMutable();
        newPlateGridList_.add(index, builderForValue.build());
        onChanged();
      } else {
        newPlateGridListBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.RoadDefendGridData newPlateGridList = 3;</code>
     */
    public Builder addAllNewPlateGridList(
        java.lang.Iterable<? extends xddq.pb.RoadDefendGridData> values) {
      if (newPlateGridListBuilder_ == null) {
        ensureNewPlateGridListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, newPlateGridList_);
        onChanged();
      } else {
        newPlateGridListBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.RoadDefendGridData newPlateGridList = 3;</code>
     */
    public Builder clearNewPlateGridList() {
      if (newPlateGridListBuilder_ == null) {
        newPlateGridList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
      } else {
        newPlateGridListBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.RoadDefendGridData newPlateGridList = 3;</code>
     */
    public Builder removeNewPlateGridList(int index) {
      if (newPlateGridListBuilder_ == null) {
        ensureNewPlateGridListIsMutable();
        newPlateGridList_.remove(index);
        onChanged();
      } else {
        newPlateGridListBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.RoadDefendGridData newPlateGridList = 3;</code>
     */
    public xddq.pb.RoadDefendGridData.Builder getNewPlateGridListBuilder(
        int index) {
      return internalGetNewPlateGridListFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.RoadDefendGridData newPlateGridList = 3;</code>
     */
    public xddq.pb.RoadDefendGridDataOrBuilder getNewPlateGridListOrBuilder(
        int index) {
      if (newPlateGridListBuilder_ == null) {
        return newPlateGridList_.get(index);  } else {
        return newPlateGridListBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.RoadDefendGridData newPlateGridList = 3;</code>
     */
    public java.util.List<? extends xddq.pb.RoadDefendGridDataOrBuilder> 
         getNewPlateGridListOrBuilderList() {
      if (newPlateGridListBuilder_ != null) {
        return newPlateGridListBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(newPlateGridList_);
      }
    }
    /**
     * <code>repeated .xddq.pb.RoadDefendGridData newPlateGridList = 3;</code>
     */
    public xddq.pb.RoadDefendGridData.Builder addNewPlateGridListBuilder() {
      return internalGetNewPlateGridListFieldBuilder().addBuilder(
          xddq.pb.RoadDefendGridData.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.RoadDefendGridData newPlateGridList = 3;</code>
     */
    public xddq.pb.RoadDefendGridData.Builder addNewPlateGridListBuilder(
        int index) {
      return internalGetNewPlateGridListFieldBuilder().addBuilder(
          index, xddq.pb.RoadDefendGridData.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.RoadDefendGridData newPlateGridList = 3;</code>
     */
    public java.util.List<xddq.pb.RoadDefendGridData.Builder> 
         getNewPlateGridListBuilderList() {
      return internalGetNewPlateGridListFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.RoadDefendGridData, xddq.pb.RoadDefendGridData.Builder, xddq.pb.RoadDefendGridDataOrBuilder> 
        internalGetNewPlateGridListFieldBuilder() {
      if (newPlateGridListBuilder_ == null) {
        newPlateGridListBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.RoadDefendGridData, xddq.pb.RoadDefendGridData.Builder, xddq.pb.RoadDefendGridDataOrBuilder>(
                newPlateGridList_,
                ((bitField0_ & 0x00000004) != 0),
                getParentForChildren(),
                isClean());
        newPlateGridList_ = null;
      }
      return newPlateGridListBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.RoadDefendPlateExpandResp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.RoadDefendPlateExpandResp)
  private static final xddq.pb.RoadDefendPlateExpandResp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.RoadDefendPlateExpandResp();
  }

  public static xddq.pb.RoadDefendPlateExpandResp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<RoadDefendPlateExpandResp>
      PARSER = new com.google.protobuf.AbstractParser<RoadDefendPlateExpandResp>() {
    @java.lang.Override
    public RoadDefendPlateExpandResp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<RoadDefendPlateExpandResp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<RoadDefendPlateExpandResp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.RoadDefendPlateExpandResp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

