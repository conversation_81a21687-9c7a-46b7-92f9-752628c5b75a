// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.GodIslandGameTargetCityInfoRespMsg}
 */
public final class GodIslandGameTargetCityInfoRespMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.GodIslandGameTargetCityInfoRespMsg)
    GodIslandGameTargetCityInfoRespMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      GodIslandGameTargetCityInfoRespMsg.class.getName());
  }
  // Use GodIslandGameTargetCityInfoRespMsg.newBuilder() to construct.
  private GodIslandGameTargetCityInfoRespMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private GodIslandGameTargetCityInfoRespMsg() {
    attackIcon_ = java.util.Collections.emptyList();
    defendIcon_ = java.util.Collections.emptyList();
    battleInfo_ = java.util.Collections.emptyList();
    npcList_ = emptyLongList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_GodIslandGameTargetCityInfoRespMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_GodIslandGameTargetCityInfoRespMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.GodIslandGameTargetCityInfoRespMsg.class, xddq.pb.GodIslandGameTargetCityInfoRespMsg.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int ISTARGET_FIELD_NUMBER = 2;
  private boolean isTarget_ = false;
  /**
   * <code>optional bool isTarget = 2;</code>
   * @return Whether the isTarget field is set.
   */
  @java.lang.Override
  public boolean hasIsTarget() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional bool isTarget = 2;</code>
   * @return The isTarget.
   */
  @java.lang.Override
  public boolean getIsTarget() {
    return isTarget_;
  }

  public static final int COSTTIME_FIELD_NUMBER = 3;
  private long costTime_ = 0L;
  /**
   * <code>optional int64 costTime = 3;</code>
   * @return Whether the costTime field is set.
   */
  @java.lang.Override
  public boolean hasCostTime() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional int64 costTime = 3;</code>
   * @return The costTime.
   */
  @java.lang.Override
  public long getCostTime() {
    return costTime_;
  }

  public static final int ATTACKICON_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.GodIslandPlayerIconMsg> attackIcon_;
  /**
   * <code>repeated .xddq.pb.GodIslandPlayerIconMsg attackIcon = 4;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.GodIslandPlayerIconMsg> getAttackIconList() {
    return attackIcon_;
  }
  /**
   * <code>repeated .xddq.pb.GodIslandPlayerIconMsg attackIcon = 4;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.GodIslandPlayerIconMsgOrBuilder> 
      getAttackIconOrBuilderList() {
    return attackIcon_;
  }
  /**
   * <code>repeated .xddq.pb.GodIslandPlayerIconMsg attackIcon = 4;</code>
   */
  @java.lang.Override
  public int getAttackIconCount() {
    return attackIcon_.size();
  }
  /**
   * <code>repeated .xddq.pb.GodIslandPlayerIconMsg attackIcon = 4;</code>
   */
  @java.lang.Override
  public xddq.pb.GodIslandPlayerIconMsg getAttackIcon(int index) {
    return attackIcon_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.GodIslandPlayerIconMsg attackIcon = 4;</code>
   */
  @java.lang.Override
  public xddq.pb.GodIslandPlayerIconMsgOrBuilder getAttackIconOrBuilder(
      int index) {
    return attackIcon_.get(index);
  }

  public static final int DEFENDICON_FIELD_NUMBER = 5;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.GodIslandPlayerIconMsg> defendIcon_;
  /**
   * <code>repeated .xddq.pb.GodIslandPlayerIconMsg defendIcon = 5;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.GodIslandPlayerIconMsg> getDefendIconList() {
    return defendIcon_;
  }
  /**
   * <code>repeated .xddq.pb.GodIslandPlayerIconMsg defendIcon = 5;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.GodIslandPlayerIconMsgOrBuilder> 
      getDefendIconOrBuilderList() {
    return defendIcon_;
  }
  /**
   * <code>repeated .xddq.pb.GodIslandPlayerIconMsg defendIcon = 5;</code>
   */
  @java.lang.Override
  public int getDefendIconCount() {
    return defendIcon_.size();
  }
  /**
   * <code>repeated .xddq.pb.GodIslandPlayerIconMsg defendIcon = 5;</code>
   */
  @java.lang.Override
  public xddq.pb.GodIslandPlayerIconMsg getDefendIcon(int index) {
    return defendIcon_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.GodIslandPlayerIconMsg defendIcon = 5;</code>
   */
  @java.lang.Override
  public xddq.pb.GodIslandPlayerIconMsgOrBuilder getDefendIconOrBuilder(
      int index) {
    return defendIcon_.get(index);
  }

  public static final int BATTLEINFO_FIELD_NUMBER = 6;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.GodIslandGameLineBattleInfo> battleInfo_;
  /**
   * <code>repeated .xddq.pb.GodIslandGameLineBattleInfo battleInfo = 6;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.GodIslandGameLineBattleInfo> getBattleInfoList() {
    return battleInfo_;
  }
  /**
   * <code>repeated .xddq.pb.GodIslandGameLineBattleInfo battleInfo = 6;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.GodIslandGameLineBattleInfoOrBuilder> 
      getBattleInfoOrBuilderList() {
    return battleInfo_;
  }
  /**
   * <code>repeated .xddq.pb.GodIslandGameLineBattleInfo battleInfo = 6;</code>
   */
  @java.lang.Override
  public int getBattleInfoCount() {
    return battleInfo_.size();
  }
  /**
   * <code>repeated .xddq.pb.GodIslandGameLineBattleInfo battleInfo = 6;</code>
   */
  @java.lang.Override
  public xddq.pb.GodIslandGameLineBattleInfo getBattleInfo(int index) {
    return battleInfo_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.GodIslandGameLineBattleInfo battleInfo = 6;</code>
   */
  @java.lang.Override
  public xddq.pb.GodIslandGameLineBattleInfoOrBuilder getBattleInfoOrBuilder(
      int index) {
    return battleInfo_.get(index);
  }

  public static final int NPCLIST_FIELD_NUMBER = 7;
  @SuppressWarnings("serial")
  private com.google.protobuf.Internal.LongList npcList_ =
      emptyLongList();
  /**
   * <code>repeated int64 npcList = 7;</code>
   * @return A list containing the npcList.
   */
  @java.lang.Override
  public java.util.List<java.lang.Long>
      getNpcListList() {
    return npcList_;
  }
  /**
   * <code>repeated int64 npcList = 7;</code>
   * @return The count of npcList.
   */
  public int getNpcListCount() {
    return npcList_.size();
  }
  /**
   * <code>repeated int64 npcList = 7;</code>
   * @param index The index of the element to return.
   * @return The npcList at the given index.
   */
  public long getNpcList(int index) {
    return npcList_.getLong(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeBool(2, isTarget_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt64(3, costTime_);
    }
    for (int i = 0; i < attackIcon_.size(); i++) {
      output.writeMessage(4, attackIcon_.get(i));
    }
    for (int i = 0; i < defendIcon_.size(); i++) {
      output.writeMessage(5, defendIcon_.get(i));
    }
    for (int i = 0; i < battleInfo_.size(); i++) {
      output.writeMessage(6, battleInfo_.get(i));
    }
    for (int i = 0; i < npcList_.size(); i++) {
      output.writeInt64(7, npcList_.getLong(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(2, isTarget_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(3, costTime_);
    }
    for (int i = 0; i < attackIcon_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(4, attackIcon_.get(i));
    }
    for (int i = 0; i < defendIcon_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(5, defendIcon_.get(i));
    }
    for (int i = 0; i < battleInfo_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(6, battleInfo_.get(i));
    }
    {
      int dataSize = 0;
      for (int i = 0; i < npcList_.size(); i++) {
        dataSize += com.google.protobuf.CodedOutputStream
          .computeInt64SizeNoTag(npcList_.getLong(i));
      }
      size += dataSize;
      size += 1 * getNpcListList().size();
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.GodIslandGameTargetCityInfoRespMsg)) {
      return super.equals(obj);
    }
    xddq.pb.GodIslandGameTargetCityInfoRespMsg other = (xddq.pb.GodIslandGameTargetCityInfoRespMsg) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (hasIsTarget() != other.hasIsTarget()) return false;
    if (hasIsTarget()) {
      if (getIsTarget()
          != other.getIsTarget()) return false;
    }
    if (hasCostTime() != other.hasCostTime()) return false;
    if (hasCostTime()) {
      if (getCostTime()
          != other.getCostTime()) return false;
    }
    if (!getAttackIconList()
        .equals(other.getAttackIconList())) return false;
    if (!getDefendIconList()
        .equals(other.getDefendIconList())) return false;
    if (!getBattleInfoList()
        .equals(other.getBattleInfoList())) return false;
    if (!getNpcListList()
        .equals(other.getNpcListList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (hasIsTarget()) {
      hash = (37 * hash) + ISTARGET_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getIsTarget());
    }
    if (hasCostTime()) {
      hash = (37 * hash) + COSTTIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getCostTime());
    }
    if (getAttackIconCount() > 0) {
      hash = (37 * hash) + ATTACKICON_FIELD_NUMBER;
      hash = (53 * hash) + getAttackIconList().hashCode();
    }
    if (getDefendIconCount() > 0) {
      hash = (37 * hash) + DEFENDICON_FIELD_NUMBER;
      hash = (53 * hash) + getDefendIconList().hashCode();
    }
    if (getBattleInfoCount() > 0) {
      hash = (37 * hash) + BATTLEINFO_FIELD_NUMBER;
      hash = (53 * hash) + getBattleInfoList().hashCode();
    }
    if (getNpcListCount() > 0) {
      hash = (37 * hash) + NPCLIST_FIELD_NUMBER;
      hash = (53 * hash) + getNpcListList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.GodIslandGameTargetCityInfoRespMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GodIslandGameTargetCityInfoRespMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GodIslandGameTargetCityInfoRespMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GodIslandGameTargetCityInfoRespMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GodIslandGameTargetCityInfoRespMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GodIslandGameTargetCityInfoRespMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GodIslandGameTargetCityInfoRespMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.GodIslandGameTargetCityInfoRespMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.GodIslandGameTargetCityInfoRespMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.GodIslandGameTargetCityInfoRespMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.GodIslandGameTargetCityInfoRespMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.GodIslandGameTargetCityInfoRespMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.GodIslandGameTargetCityInfoRespMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.GodIslandGameTargetCityInfoRespMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.GodIslandGameTargetCityInfoRespMsg)
      xddq.pb.GodIslandGameTargetCityInfoRespMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GodIslandGameTargetCityInfoRespMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GodIslandGameTargetCityInfoRespMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.GodIslandGameTargetCityInfoRespMsg.class, xddq.pb.GodIslandGameTargetCityInfoRespMsg.Builder.class);
    }

    // Construct using xddq.pb.GodIslandGameTargetCityInfoRespMsg.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      isTarget_ = false;
      costTime_ = 0L;
      if (attackIconBuilder_ == null) {
        attackIcon_ = java.util.Collections.emptyList();
      } else {
        attackIcon_ = null;
        attackIconBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000008);
      if (defendIconBuilder_ == null) {
        defendIcon_ = java.util.Collections.emptyList();
      } else {
        defendIcon_ = null;
        defendIconBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000010);
      if (battleInfoBuilder_ == null) {
        battleInfo_ = java.util.Collections.emptyList();
      } else {
        battleInfo_ = null;
        battleInfoBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000020);
      npcList_ = emptyLongList();
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GodIslandGameTargetCityInfoRespMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.GodIslandGameTargetCityInfoRespMsg getDefaultInstanceForType() {
      return xddq.pb.GodIslandGameTargetCityInfoRespMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.GodIslandGameTargetCityInfoRespMsg build() {
      xddq.pb.GodIslandGameTargetCityInfoRespMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.GodIslandGameTargetCityInfoRespMsg buildPartial() {
      xddq.pb.GodIslandGameTargetCityInfoRespMsg result = new xddq.pb.GodIslandGameTargetCityInfoRespMsg(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.GodIslandGameTargetCityInfoRespMsg result) {
      if (attackIconBuilder_ == null) {
        if (((bitField0_ & 0x00000008) != 0)) {
          attackIcon_ = java.util.Collections.unmodifiableList(attackIcon_);
          bitField0_ = (bitField0_ & ~0x00000008);
        }
        result.attackIcon_ = attackIcon_;
      } else {
        result.attackIcon_ = attackIconBuilder_.build();
      }
      if (defendIconBuilder_ == null) {
        if (((bitField0_ & 0x00000010) != 0)) {
          defendIcon_ = java.util.Collections.unmodifiableList(defendIcon_);
          bitField0_ = (bitField0_ & ~0x00000010);
        }
        result.defendIcon_ = defendIcon_;
      } else {
        result.defendIcon_ = defendIconBuilder_.build();
      }
      if (battleInfoBuilder_ == null) {
        if (((bitField0_ & 0x00000020) != 0)) {
          battleInfo_ = java.util.Collections.unmodifiableList(battleInfo_);
          bitField0_ = (bitField0_ & ~0x00000020);
        }
        result.battleInfo_ = battleInfo_;
      } else {
        result.battleInfo_ = battleInfoBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.GodIslandGameTargetCityInfoRespMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.isTarget_ = isTarget_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.costTime_ = costTime_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        npcList_.makeImmutable();
        result.npcList_ = npcList_;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.GodIslandGameTargetCityInfoRespMsg) {
        return mergeFrom((xddq.pb.GodIslandGameTargetCityInfoRespMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.GodIslandGameTargetCityInfoRespMsg other) {
      if (other == xddq.pb.GodIslandGameTargetCityInfoRespMsg.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (other.hasIsTarget()) {
        setIsTarget(other.getIsTarget());
      }
      if (other.hasCostTime()) {
        setCostTime(other.getCostTime());
      }
      if (attackIconBuilder_ == null) {
        if (!other.attackIcon_.isEmpty()) {
          if (attackIcon_.isEmpty()) {
            attackIcon_ = other.attackIcon_;
            bitField0_ = (bitField0_ & ~0x00000008);
          } else {
            ensureAttackIconIsMutable();
            attackIcon_.addAll(other.attackIcon_);
          }
          onChanged();
        }
      } else {
        if (!other.attackIcon_.isEmpty()) {
          if (attackIconBuilder_.isEmpty()) {
            attackIconBuilder_.dispose();
            attackIconBuilder_ = null;
            attackIcon_ = other.attackIcon_;
            bitField0_ = (bitField0_ & ~0x00000008);
            attackIconBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetAttackIconFieldBuilder() : null;
          } else {
            attackIconBuilder_.addAllMessages(other.attackIcon_);
          }
        }
      }
      if (defendIconBuilder_ == null) {
        if (!other.defendIcon_.isEmpty()) {
          if (defendIcon_.isEmpty()) {
            defendIcon_ = other.defendIcon_;
            bitField0_ = (bitField0_ & ~0x00000010);
          } else {
            ensureDefendIconIsMutable();
            defendIcon_.addAll(other.defendIcon_);
          }
          onChanged();
        }
      } else {
        if (!other.defendIcon_.isEmpty()) {
          if (defendIconBuilder_.isEmpty()) {
            defendIconBuilder_.dispose();
            defendIconBuilder_ = null;
            defendIcon_ = other.defendIcon_;
            bitField0_ = (bitField0_ & ~0x00000010);
            defendIconBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetDefendIconFieldBuilder() : null;
          } else {
            defendIconBuilder_.addAllMessages(other.defendIcon_);
          }
        }
      }
      if (battleInfoBuilder_ == null) {
        if (!other.battleInfo_.isEmpty()) {
          if (battleInfo_.isEmpty()) {
            battleInfo_ = other.battleInfo_;
            bitField0_ = (bitField0_ & ~0x00000020);
          } else {
            ensureBattleInfoIsMutable();
            battleInfo_.addAll(other.battleInfo_);
          }
          onChanged();
        }
      } else {
        if (!other.battleInfo_.isEmpty()) {
          if (battleInfoBuilder_.isEmpty()) {
            battleInfoBuilder_.dispose();
            battleInfoBuilder_ = null;
            battleInfo_ = other.battleInfo_;
            bitField0_ = (bitField0_ & ~0x00000020);
            battleInfoBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetBattleInfoFieldBuilder() : null;
          } else {
            battleInfoBuilder_.addAllMessages(other.battleInfo_);
          }
        }
      }
      if (!other.npcList_.isEmpty()) {
        if (npcList_.isEmpty()) {
          npcList_ = other.npcList_;
          npcList_.makeImmutable();
          bitField0_ |= 0x00000040;
        } else {
          ensureNpcListIsMutable();
          npcList_.addAll(other.npcList_);
        }
        onChanged();
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              isTarget_ = input.readBool();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 24: {
              costTime_ = input.readInt64();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 34: {
              xddq.pb.GodIslandPlayerIconMsg m =
                  input.readMessage(
                      xddq.pb.GodIslandPlayerIconMsg.parser(),
                      extensionRegistry);
              if (attackIconBuilder_ == null) {
                ensureAttackIconIsMutable();
                attackIcon_.add(m);
              } else {
                attackIconBuilder_.addMessage(m);
              }
              break;
            } // case 34
            case 42: {
              xddq.pb.GodIslandPlayerIconMsg m =
                  input.readMessage(
                      xddq.pb.GodIslandPlayerIconMsg.parser(),
                      extensionRegistry);
              if (defendIconBuilder_ == null) {
                ensureDefendIconIsMutable();
                defendIcon_.add(m);
              } else {
                defendIconBuilder_.addMessage(m);
              }
              break;
            } // case 42
            case 50: {
              xddq.pb.GodIslandGameLineBattleInfo m =
                  input.readMessage(
                      xddq.pb.GodIslandGameLineBattleInfo.parser(),
                      extensionRegistry);
              if (battleInfoBuilder_ == null) {
                ensureBattleInfoIsMutable();
                battleInfo_.add(m);
              } else {
                battleInfoBuilder_.addMessage(m);
              }
              break;
            } // case 50
            case 56: {
              long v = input.readInt64();
              ensureNpcListIsMutable();
              npcList_.addLong(v);
              break;
            } // case 56
            case 58: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              ensureNpcListIsMutable();
              while (input.getBytesUntilLimit() > 0) {
                npcList_.addLong(input.readInt64());
              }
              input.popLimit(limit);
              break;
            } // case 58
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private boolean isTarget_ ;
    /**
     * <code>optional bool isTarget = 2;</code>
     * @return Whether the isTarget field is set.
     */
    @java.lang.Override
    public boolean hasIsTarget() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional bool isTarget = 2;</code>
     * @return The isTarget.
     */
    @java.lang.Override
    public boolean getIsTarget() {
      return isTarget_;
    }
    /**
     * <code>optional bool isTarget = 2;</code>
     * @param value The isTarget to set.
     * @return This builder for chaining.
     */
    public Builder setIsTarget(boolean value) {

      isTarget_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional bool isTarget = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearIsTarget() {
      bitField0_ = (bitField0_ & ~0x00000002);
      isTarget_ = false;
      onChanged();
      return this;
    }

    private long costTime_ ;
    /**
     * <code>optional int64 costTime = 3;</code>
     * @return Whether the costTime field is set.
     */
    @java.lang.Override
    public boolean hasCostTime() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int64 costTime = 3;</code>
     * @return The costTime.
     */
    @java.lang.Override
    public long getCostTime() {
      return costTime_;
    }
    /**
     * <code>optional int64 costTime = 3;</code>
     * @param value The costTime to set.
     * @return This builder for chaining.
     */
    public Builder setCostTime(long value) {

      costTime_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 costTime = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearCostTime() {
      bitField0_ = (bitField0_ & ~0x00000004);
      costTime_ = 0L;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.GodIslandPlayerIconMsg> attackIcon_ =
      java.util.Collections.emptyList();
    private void ensureAttackIconIsMutable() {
      if (!((bitField0_ & 0x00000008) != 0)) {
        attackIcon_ = new java.util.ArrayList<xddq.pb.GodIslandPlayerIconMsg>(attackIcon_);
        bitField0_ |= 0x00000008;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.GodIslandPlayerIconMsg, xddq.pb.GodIslandPlayerIconMsg.Builder, xddq.pb.GodIslandPlayerIconMsgOrBuilder> attackIconBuilder_;

    /**
     * <code>repeated .xddq.pb.GodIslandPlayerIconMsg attackIcon = 4;</code>
     */
    public java.util.List<xddq.pb.GodIslandPlayerIconMsg> getAttackIconList() {
      if (attackIconBuilder_ == null) {
        return java.util.Collections.unmodifiableList(attackIcon_);
      } else {
        return attackIconBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.GodIslandPlayerIconMsg attackIcon = 4;</code>
     */
    public int getAttackIconCount() {
      if (attackIconBuilder_ == null) {
        return attackIcon_.size();
      } else {
        return attackIconBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.GodIslandPlayerIconMsg attackIcon = 4;</code>
     */
    public xddq.pb.GodIslandPlayerIconMsg getAttackIcon(int index) {
      if (attackIconBuilder_ == null) {
        return attackIcon_.get(index);
      } else {
        return attackIconBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.GodIslandPlayerIconMsg attackIcon = 4;</code>
     */
    public Builder setAttackIcon(
        int index, xddq.pb.GodIslandPlayerIconMsg value) {
      if (attackIconBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureAttackIconIsMutable();
        attackIcon_.set(index, value);
        onChanged();
      } else {
        attackIconBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodIslandPlayerIconMsg attackIcon = 4;</code>
     */
    public Builder setAttackIcon(
        int index, xddq.pb.GodIslandPlayerIconMsg.Builder builderForValue) {
      if (attackIconBuilder_ == null) {
        ensureAttackIconIsMutable();
        attackIcon_.set(index, builderForValue.build());
        onChanged();
      } else {
        attackIconBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodIslandPlayerIconMsg attackIcon = 4;</code>
     */
    public Builder addAttackIcon(xddq.pb.GodIslandPlayerIconMsg value) {
      if (attackIconBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureAttackIconIsMutable();
        attackIcon_.add(value);
        onChanged();
      } else {
        attackIconBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodIslandPlayerIconMsg attackIcon = 4;</code>
     */
    public Builder addAttackIcon(
        int index, xddq.pb.GodIslandPlayerIconMsg value) {
      if (attackIconBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureAttackIconIsMutable();
        attackIcon_.add(index, value);
        onChanged();
      } else {
        attackIconBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodIslandPlayerIconMsg attackIcon = 4;</code>
     */
    public Builder addAttackIcon(
        xddq.pb.GodIslandPlayerIconMsg.Builder builderForValue) {
      if (attackIconBuilder_ == null) {
        ensureAttackIconIsMutable();
        attackIcon_.add(builderForValue.build());
        onChanged();
      } else {
        attackIconBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodIslandPlayerIconMsg attackIcon = 4;</code>
     */
    public Builder addAttackIcon(
        int index, xddq.pb.GodIslandPlayerIconMsg.Builder builderForValue) {
      if (attackIconBuilder_ == null) {
        ensureAttackIconIsMutable();
        attackIcon_.add(index, builderForValue.build());
        onChanged();
      } else {
        attackIconBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodIslandPlayerIconMsg attackIcon = 4;</code>
     */
    public Builder addAllAttackIcon(
        java.lang.Iterable<? extends xddq.pb.GodIslandPlayerIconMsg> values) {
      if (attackIconBuilder_ == null) {
        ensureAttackIconIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, attackIcon_);
        onChanged();
      } else {
        attackIconBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodIslandPlayerIconMsg attackIcon = 4;</code>
     */
    public Builder clearAttackIcon() {
      if (attackIconBuilder_ == null) {
        attackIcon_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000008);
        onChanged();
      } else {
        attackIconBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodIslandPlayerIconMsg attackIcon = 4;</code>
     */
    public Builder removeAttackIcon(int index) {
      if (attackIconBuilder_ == null) {
        ensureAttackIconIsMutable();
        attackIcon_.remove(index);
        onChanged();
      } else {
        attackIconBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodIslandPlayerIconMsg attackIcon = 4;</code>
     */
    public xddq.pb.GodIslandPlayerIconMsg.Builder getAttackIconBuilder(
        int index) {
      return internalGetAttackIconFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.GodIslandPlayerIconMsg attackIcon = 4;</code>
     */
    public xddq.pb.GodIslandPlayerIconMsgOrBuilder getAttackIconOrBuilder(
        int index) {
      if (attackIconBuilder_ == null) {
        return attackIcon_.get(index);  } else {
        return attackIconBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.GodIslandPlayerIconMsg attackIcon = 4;</code>
     */
    public java.util.List<? extends xddq.pb.GodIslandPlayerIconMsgOrBuilder> 
         getAttackIconOrBuilderList() {
      if (attackIconBuilder_ != null) {
        return attackIconBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(attackIcon_);
      }
    }
    /**
     * <code>repeated .xddq.pb.GodIslandPlayerIconMsg attackIcon = 4;</code>
     */
    public xddq.pb.GodIslandPlayerIconMsg.Builder addAttackIconBuilder() {
      return internalGetAttackIconFieldBuilder().addBuilder(
          xddq.pb.GodIslandPlayerIconMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.GodIslandPlayerIconMsg attackIcon = 4;</code>
     */
    public xddq.pb.GodIslandPlayerIconMsg.Builder addAttackIconBuilder(
        int index) {
      return internalGetAttackIconFieldBuilder().addBuilder(
          index, xddq.pb.GodIslandPlayerIconMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.GodIslandPlayerIconMsg attackIcon = 4;</code>
     */
    public java.util.List<xddq.pb.GodIslandPlayerIconMsg.Builder> 
         getAttackIconBuilderList() {
      return internalGetAttackIconFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.GodIslandPlayerIconMsg, xddq.pb.GodIslandPlayerIconMsg.Builder, xddq.pb.GodIslandPlayerIconMsgOrBuilder> 
        internalGetAttackIconFieldBuilder() {
      if (attackIconBuilder_ == null) {
        attackIconBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.GodIslandPlayerIconMsg, xddq.pb.GodIslandPlayerIconMsg.Builder, xddq.pb.GodIslandPlayerIconMsgOrBuilder>(
                attackIcon_,
                ((bitField0_ & 0x00000008) != 0),
                getParentForChildren(),
                isClean());
        attackIcon_ = null;
      }
      return attackIconBuilder_;
    }

    private java.util.List<xddq.pb.GodIslandPlayerIconMsg> defendIcon_ =
      java.util.Collections.emptyList();
    private void ensureDefendIconIsMutable() {
      if (!((bitField0_ & 0x00000010) != 0)) {
        defendIcon_ = new java.util.ArrayList<xddq.pb.GodIslandPlayerIconMsg>(defendIcon_);
        bitField0_ |= 0x00000010;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.GodIslandPlayerIconMsg, xddq.pb.GodIslandPlayerIconMsg.Builder, xddq.pb.GodIslandPlayerIconMsgOrBuilder> defendIconBuilder_;

    /**
     * <code>repeated .xddq.pb.GodIslandPlayerIconMsg defendIcon = 5;</code>
     */
    public java.util.List<xddq.pb.GodIslandPlayerIconMsg> getDefendIconList() {
      if (defendIconBuilder_ == null) {
        return java.util.Collections.unmodifiableList(defendIcon_);
      } else {
        return defendIconBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.GodIslandPlayerIconMsg defendIcon = 5;</code>
     */
    public int getDefendIconCount() {
      if (defendIconBuilder_ == null) {
        return defendIcon_.size();
      } else {
        return defendIconBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.GodIslandPlayerIconMsg defendIcon = 5;</code>
     */
    public xddq.pb.GodIslandPlayerIconMsg getDefendIcon(int index) {
      if (defendIconBuilder_ == null) {
        return defendIcon_.get(index);
      } else {
        return defendIconBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.GodIslandPlayerIconMsg defendIcon = 5;</code>
     */
    public Builder setDefendIcon(
        int index, xddq.pb.GodIslandPlayerIconMsg value) {
      if (defendIconBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDefendIconIsMutable();
        defendIcon_.set(index, value);
        onChanged();
      } else {
        defendIconBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodIslandPlayerIconMsg defendIcon = 5;</code>
     */
    public Builder setDefendIcon(
        int index, xddq.pb.GodIslandPlayerIconMsg.Builder builderForValue) {
      if (defendIconBuilder_ == null) {
        ensureDefendIconIsMutable();
        defendIcon_.set(index, builderForValue.build());
        onChanged();
      } else {
        defendIconBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodIslandPlayerIconMsg defendIcon = 5;</code>
     */
    public Builder addDefendIcon(xddq.pb.GodIslandPlayerIconMsg value) {
      if (defendIconBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDefendIconIsMutable();
        defendIcon_.add(value);
        onChanged();
      } else {
        defendIconBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodIslandPlayerIconMsg defendIcon = 5;</code>
     */
    public Builder addDefendIcon(
        int index, xddq.pb.GodIslandPlayerIconMsg value) {
      if (defendIconBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDefendIconIsMutable();
        defendIcon_.add(index, value);
        onChanged();
      } else {
        defendIconBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodIslandPlayerIconMsg defendIcon = 5;</code>
     */
    public Builder addDefendIcon(
        xddq.pb.GodIslandPlayerIconMsg.Builder builderForValue) {
      if (defendIconBuilder_ == null) {
        ensureDefendIconIsMutable();
        defendIcon_.add(builderForValue.build());
        onChanged();
      } else {
        defendIconBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodIslandPlayerIconMsg defendIcon = 5;</code>
     */
    public Builder addDefendIcon(
        int index, xddq.pb.GodIslandPlayerIconMsg.Builder builderForValue) {
      if (defendIconBuilder_ == null) {
        ensureDefendIconIsMutable();
        defendIcon_.add(index, builderForValue.build());
        onChanged();
      } else {
        defendIconBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodIslandPlayerIconMsg defendIcon = 5;</code>
     */
    public Builder addAllDefendIcon(
        java.lang.Iterable<? extends xddq.pb.GodIslandPlayerIconMsg> values) {
      if (defendIconBuilder_ == null) {
        ensureDefendIconIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, defendIcon_);
        onChanged();
      } else {
        defendIconBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodIslandPlayerIconMsg defendIcon = 5;</code>
     */
    public Builder clearDefendIcon() {
      if (defendIconBuilder_ == null) {
        defendIcon_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000010);
        onChanged();
      } else {
        defendIconBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodIslandPlayerIconMsg defendIcon = 5;</code>
     */
    public Builder removeDefendIcon(int index) {
      if (defendIconBuilder_ == null) {
        ensureDefendIconIsMutable();
        defendIcon_.remove(index);
        onChanged();
      } else {
        defendIconBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodIslandPlayerIconMsg defendIcon = 5;</code>
     */
    public xddq.pb.GodIslandPlayerIconMsg.Builder getDefendIconBuilder(
        int index) {
      return internalGetDefendIconFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.GodIslandPlayerIconMsg defendIcon = 5;</code>
     */
    public xddq.pb.GodIslandPlayerIconMsgOrBuilder getDefendIconOrBuilder(
        int index) {
      if (defendIconBuilder_ == null) {
        return defendIcon_.get(index);  } else {
        return defendIconBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.GodIslandPlayerIconMsg defendIcon = 5;</code>
     */
    public java.util.List<? extends xddq.pb.GodIslandPlayerIconMsgOrBuilder> 
         getDefendIconOrBuilderList() {
      if (defendIconBuilder_ != null) {
        return defendIconBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(defendIcon_);
      }
    }
    /**
     * <code>repeated .xddq.pb.GodIslandPlayerIconMsg defendIcon = 5;</code>
     */
    public xddq.pb.GodIslandPlayerIconMsg.Builder addDefendIconBuilder() {
      return internalGetDefendIconFieldBuilder().addBuilder(
          xddq.pb.GodIslandPlayerIconMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.GodIslandPlayerIconMsg defendIcon = 5;</code>
     */
    public xddq.pb.GodIslandPlayerIconMsg.Builder addDefendIconBuilder(
        int index) {
      return internalGetDefendIconFieldBuilder().addBuilder(
          index, xddq.pb.GodIslandPlayerIconMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.GodIslandPlayerIconMsg defendIcon = 5;</code>
     */
    public java.util.List<xddq.pb.GodIslandPlayerIconMsg.Builder> 
         getDefendIconBuilderList() {
      return internalGetDefendIconFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.GodIslandPlayerIconMsg, xddq.pb.GodIslandPlayerIconMsg.Builder, xddq.pb.GodIslandPlayerIconMsgOrBuilder> 
        internalGetDefendIconFieldBuilder() {
      if (defendIconBuilder_ == null) {
        defendIconBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.GodIslandPlayerIconMsg, xddq.pb.GodIslandPlayerIconMsg.Builder, xddq.pb.GodIslandPlayerIconMsgOrBuilder>(
                defendIcon_,
                ((bitField0_ & 0x00000010) != 0),
                getParentForChildren(),
                isClean());
        defendIcon_ = null;
      }
      return defendIconBuilder_;
    }

    private java.util.List<xddq.pb.GodIslandGameLineBattleInfo> battleInfo_ =
      java.util.Collections.emptyList();
    private void ensureBattleInfoIsMutable() {
      if (!((bitField0_ & 0x00000020) != 0)) {
        battleInfo_ = new java.util.ArrayList<xddq.pb.GodIslandGameLineBattleInfo>(battleInfo_);
        bitField0_ |= 0x00000020;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.GodIslandGameLineBattleInfo, xddq.pb.GodIslandGameLineBattleInfo.Builder, xddq.pb.GodIslandGameLineBattleInfoOrBuilder> battleInfoBuilder_;

    /**
     * <code>repeated .xddq.pb.GodIslandGameLineBattleInfo battleInfo = 6;</code>
     */
    public java.util.List<xddq.pb.GodIslandGameLineBattleInfo> getBattleInfoList() {
      if (battleInfoBuilder_ == null) {
        return java.util.Collections.unmodifiableList(battleInfo_);
      } else {
        return battleInfoBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.GodIslandGameLineBattleInfo battleInfo = 6;</code>
     */
    public int getBattleInfoCount() {
      if (battleInfoBuilder_ == null) {
        return battleInfo_.size();
      } else {
        return battleInfoBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.GodIslandGameLineBattleInfo battleInfo = 6;</code>
     */
    public xddq.pb.GodIslandGameLineBattleInfo getBattleInfo(int index) {
      if (battleInfoBuilder_ == null) {
        return battleInfo_.get(index);
      } else {
        return battleInfoBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.GodIslandGameLineBattleInfo battleInfo = 6;</code>
     */
    public Builder setBattleInfo(
        int index, xddq.pb.GodIslandGameLineBattleInfo value) {
      if (battleInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureBattleInfoIsMutable();
        battleInfo_.set(index, value);
        onChanged();
      } else {
        battleInfoBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodIslandGameLineBattleInfo battleInfo = 6;</code>
     */
    public Builder setBattleInfo(
        int index, xddq.pb.GodIslandGameLineBattleInfo.Builder builderForValue) {
      if (battleInfoBuilder_ == null) {
        ensureBattleInfoIsMutable();
        battleInfo_.set(index, builderForValue.build());
        onChanged();
      } else {
        battleInfoBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodIslandGameLineBattleInfo battleInfo = 6;</code>
     */
    public Builder addBattleInfo(xddq.pb.GodIslandGameLineBattleInfo value) {
      if (battleInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureBattleInfoIsMutable();
        battleInfo_.add(value);
        onChanged();
      } else {
        battleInfoBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodIslandGameLineBattleInfo battleInfo = 6;</code>
     */
    public Builder addBattleInfo(
        int index, xddq.pb.GodIslandGameLineBattleInfo value) {
      if (battleInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureBattleInfoIsMutable();
        battleInfo_.add(index, value);
        onChanged();
      } else {
        battleInfoBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodIslandGameLineBattleInfo battleInfo = 6;</code>
     */
    public Builder addBattleInfo(
        xddq.pb.GodIslandGameLineBattleInfo.Builder builderForValue) {
      if (battleInfoBuilder_ == null) {
        ensureBattleInfoIsMutable();
        battleInfo_.add(builderForValue.build());
        onChanged();
      } else {
        battleInfoBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodIslandGameLineBattleInfo battleInfo = 6;</code>
     */
    public Builder addBattleInfo(
        int index, xddq.pb.GodIslandGameLineBattleInfo.Builder builderForValue) {
      if (battleInfoBuilder_ == null) {
        ensureBattleInfoIsMutable();
        battleInfo_.add(index, builderForValue.build());
        onChanged();
      } else {
        battleInfoBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodIslandGameLineBattleInfo battleInfo = 6;</code>
     */
    public Builder addAllBattleInfo(
        java.lang.Iterable<? extends xddq.pb.GodIslandGameLineBattleInfo> values) {
      if (battleInfoBuilder_ == null) {
        ensureBattleInfoIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, battleInfo_);
        onChanged();
      } else {
        battleInfoBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodIslandGameLineBattleInfo battleInfo = 6;</code>
     */
    public Builder clearBattleInfo() {
      if (battleInfoBuilder_ == null) {
        battleInfo_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000020);
        onChanged();
      } else {
        battleInfoBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodIslandGameLineBattleInfo battleInfo = 6;</code>
     */
    public Builder removeBattleInfo(int index) {
      if (battleInfoBuilder_ == null) {
        ensureBattleInfoIsMutable();
        battleInfo_.remove(index);
        onChanged();
      } else {
        battleInfoBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodIslandGameLineBattleInfo battleInfo = 6;</code>
     */
    public xddq.pb.GodIslandGameLineBattleInfo.Builder getBattleInfoBuilder(
        int index) {
      return internalGetBattleInfoFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.GodIslandGameLineBattleInfo battleInfo = 6;</code>
     */
    public xddq.pb.GodIslandGameLineBattleInfoOrBuilder getBattleInfoOrBuilder(
        int index) {
      if (battleInfoBuilder_ == null) {
        return battleInfo_.get(index);  } else {
        return battleInfoBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.GodIslandGameLineBattleInfo battleInfo = 6;</code>
     */
    public java.util.List<? extends xddq.pb.GodIslandGameLineBattleInfoOrBuilder> 
         getBattleInfoOrBuilderList() {
      if (battleInfoBuilder_ != null) {
        return battleInfoBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(battleInfo_);
      }
    }
    /**
     * <code>repeated .xddq.pb.GodIslandGameLineBattleInfo battleInfo = 6;</code>
     */
    public xddq.pb.GodIslandGameLineBattleInfo.Builder addBattleInfoBuilder() {
      return internalGetBattleInfoFieldBuilder().addBuilder(
          xddq.pb.GodIslandGameLineBattleInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.GodIslandGameLineBattleInfo battleInfo = 6;</code>
     */
    public xddq.pb.GodIslandGameLineBattleInfo.Builder addBattleInfoBuilder(
        int index) {
      return internalGetBattleInfoFieldBuilder().addBuilder(
          index, xddq.pb.GodIslandGameLineBattleInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.GodIslandGameLineBattleInfo battleInfo = 6;</code>
     */
    public java.util.List<xddq.pb.GodIslandGameLineBattleInfo.Builder> 
         getBattleInfoBuilderList() {
      return internalGetBattleInfoFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.GodIslandGameLineBattleInfo, xddq.pb.GodIslandGameLineBattleInfo.Builder, xddq.pb.GodIslandGameLineBattleInfoOrBuilder> 
        internalGetBattleInfoFieldBuilder() {
      if (battleInfoBuilder_ == null) {
        battleInfoBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.GodIslandGameLineBattleInfo, xddq.pb.GodIslandGameLineBattleInfo.Builder, xddq.pb.GodIslandGameLineBattleInfoOrBuilder>(
                battleInfo_,
                ((bitField0_ & 0x00000020) != 0),
                getParentForChildren(),
                isClean());
        battleInfo_ = null;
      }
      return battleInfoBuilder_;
    }

    private com.google.protobuf.Internal.LongList npcList_ = emptyLongList();
    private void ensureNpcListIsMutable() {
      if (!npcList_.isModifiable()) {
        npcList_ = makeMutableCopy(npcList_);
      }
      bitField0_ |= 0x00000040;
    }
    /**
     * <code>repeated int64 npcList = 7;</code>
     * @return A list containing the npcList.
     */
    public java.util.List<java.lang.Long>
        getNpcListList() {
      npcList_.makeImmutable();
      return npcList_;
    }
    /**
     * <code>repeated int64 npcList = 7;</code>
     * @return The count of npcList.
     */
    public int getNpcListCount() {
      return npcList_.size();
    }
    /**
     * <code>repeated int64 npcList = 7;</code>
     * @param index The index of the element to return.
     * @return The npcList at the given index.
     */
    public long getNpcList(int index) {
      return npcList_.getLong(index);
    }
    /**
     * <code>repeated int64 npcList = 7;</code>
     * @param index The index to set the value at.
     * @param value The npcList to set.
     * @return This builder for chaining.
     */
    public Builder setNpcList(
        int index, long value) {

      ensureNpcListIsMutable();
      npcList_.setLong(index, value);
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int64 npcList = 7;</code>
     * @param value The npcList to add.
     * @return This builder for chaining.
     */
    public Builder addNpcList(long value) {

      ensureNpcListIsMutable();
      npcList_.addLong(value);
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int64 npcList = 7;</code>
     * @param values The npcList to add.
     * @return This builder for chaining.
     */
    public Builder addAllNpcList(
        java.lang.Iterable<? extends java.lang.Long> values) {
      ensureNpcListIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, npcList_);
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int64 npcList = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearNpcList() {
      npcList_ = emptyLongList();
      bitField0_ = (bitField0_ & ~0x00000040);
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.GodIslandGameTargetCityInfoRespMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.GodIslandGameTargetCityInfoRespMsg)
  private static final xddq.pb.GodIslandGameTargetCityInfoRespMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.GodIslandGameTargetCityInfoRespMsg();
  }

  public static xddq.pb.GodIslandGameTargetCityInfoRespMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<GodIslandGameTargetCityInfoRespMsg>
      PARSER = new com.google.protobuf.AbstractParser<GodIslandGameTargetCityInfoRespMsg>() {
    @java.lang.Override
    public GodIslandGameTargetCityInfoRespMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<GodIslandGameTargetCityInfoRespMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<GodIslandGameTargetCityInfoRespMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.GodIslandGameTargetCityInfoRespMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

