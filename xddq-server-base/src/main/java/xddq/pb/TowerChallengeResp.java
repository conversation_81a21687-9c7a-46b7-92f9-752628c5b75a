// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.TowerChallengeResp}
 */
public final class TowerChallengeResp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.TowerChallengeResp)
    TowerChallengeRespOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      TowerChallengeResp.class.getName());
  }
  // Use TowerChallengeResp.newBuilder() to construct.
  private TowerChallengeResp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private TowerChallengeResp() {
    rewards_ = "";
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_TowerChallengeResp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_TowerChallengeResp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.TowerChallengeResp.class, xddq.pb.TowerChallengeResp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int ALLBATTLERECORD_FIELD_NUMBER = 2;
  private xddq.pb.BattleRecordMsg allBattleRecord_;
  /**
   * <code>optional .xddq.pb.BattleRecordMsg allBattleRecord = 2;</code>
   * @return Whether the allBattleRecord field is set.
   */
  @java.lang.Override
  public boolean hasAllBattleRecord() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional .xddq.pb.BattleRecordMsg allBattleRecord = 2;</code>
   * @return The allBattleRecord.
   */
  @java.lang.Override
  public xddq.pb.BattleRecordMsg getAllBattleRecord() {
    return allBattleRecord_ == null ? xddq.pb.BattleRecordMsg.getDefaultInstance() : allBattleRecord_;
  }
  /**
   * <code>optional .xddq.pb.BattleRecordMsg allBattleRecord = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.BattleRecordMsgOrBuilder getAllBattleRecordOrBuilder() {
    return allBattleRecord_ == null ? xddq.pb.BattleRecordMsg.getDefaultInstance() : allBattleRecord_;
  }

  public static final int CHALLENGESUCCESS_FIELD_NUMBER = 3;
  private boolean challengeSuccess_ = false;
  /**
   * <code>optional bool challengeSuccess = 3;</code>
   * @return Whether the challengeSuccess field is set.
   */
  @java.lang.Override
  public boolean hasChallengeSuccess() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional bool challengeSuccess = 3;</code>
   * @return The challengeSuccess.
   */
  @java.lang.Override
  public boolean getChallengeSuccess() {
    return challengeSuccess_;
  }

  public static final int REWARDS_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private volatile java.lang.Object rewards_ = "";
  /**
   * <code>optional string rewards = 4;</code>
   * @return Whether the rewards field is set.
   */
  @java.lang.Override
  public boolean hasRewards() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional string rewards = 4;</code>
   * @return The rewards.
   */
  @java.lang.Override
  public java.lang.String getRewards() {
    java.lang.Object ref = rewards_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        rewards_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string rewards = 4;</code>
   * @return The bytes for rewards.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getRewardsBytes() {
    java.lang.Object ref = rewards_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      rewards_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int TOWERDATASYNC_FIELD_NUMBER = 5;
  private xddq.pb.TowerDataMsg towerDataSync_;
  /**
   * <code>optional .xddq.pb.TowerDataMsg towerDataSync = 5;</code>
   * @return Whether the towerDataSync field is set.
   */
  @java.lang.Override
  public boolean hasTowerDataSync() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>optional .xddq.pb.TowerDataMsg towerDataSync = 5;</code>
   * @return The towerDataSync.
   */
  @java.lang.Override
  public xddq.pb.TowerDataMsg getTowerDataSync() {
    return towerDataSync_ == null ? xddq.pb.TowerDataMsg.getDefaultInstance() : towerDataSync_;
  }
  /**
   * <code>optional .xddq.pb.TowerDataMsg towerDataSync = 5;</code>
   */
  @java.lang.Override
  public xddq.pb.TowerDataMsgOrBuilder getTowerDataSyncOrBuilder() {
    return towerDataSync_ == null ? xddq.pb.TowerDataMsg.getDefaultInstance() : towerDataSync_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (hasAllBattleRecord()) {
      if (!getAllBattleRecord().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    if (hasTowerDataSync()) {
      if (!getTowerDataSync().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeMessage(2, getAllBattleRecord());
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeBool(3, challengeSuccess_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 4, rewards_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      output.writeMessage(5, getTowerDataSync());
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getAllBattleRecord());
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(3, challengeSuccess_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(4, rewards_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(5, getTowerDataSync());
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.TowerChallengeResp)) {
      return super.equals(obj);
    }
    xddq.pb.TowerChallengeResp other = (xddq.pb.TowerChallengeResp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (hasAllBattleRecord() != other.hasAllBattleRecord()) return false;
    if (hasAllBattleRecord()) {
      if (!getAllBattleRecord()
          .equals(other.getAllBattleRecord())) return false;
    }
    if (hasChallengeSuccess() != other.hasChallengeSuccess()) return false;
    if (hasChallengeSuccess()) {
      if (getChallengeSuccess()
          != other.getChallengeSuccess()) return false;
    }
    if (hasRewards() != other.hasRewards()) return false;
    if (hasRewards()) {
      if (!getRewards()
          .equals(other.getRewards())) return false;
    }
    if (hasTowerDataSync() != other.hasTowerDataSync()) return false;
    if (hasTowerDataSync()) {
      if (!getTowerDataSync()
          .equals(other.getTowerDataSync())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (hasAllBattleRecord()) {
      hash = (37 * hash) + ALLBATTLERECORD_FIELD_NUMBER;
      hash = (53 * hash) + getAllBattleRecord().hashCode();
    }
    if (hasChallengeSuccess()) {
      hash = (37 * hash) + CHALLENGESUCCESS_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getChallengeSuccess());
    }
    if (hasRewards()) {
      hash = (37 * hash) + REWARDS_FIELD_NUMBER;
      hash = (53 * hash) + getRewards().hashCode();
    }
    if (hasTowerDataSync()) {
      hash = (37 * hash) + TOWERDATASYNC_FIELD_NUMBER;
      hash = (53 * hash) + getTowerDataSync().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.TowerChallengeResp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.TowerChallengeResp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.TowerChallengeResp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.TowerChallengeResp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.TowerChallengeResp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.TowerChallengeResp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.TowerChallengeResp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.TowerChallengeResp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.TowerChallengeResp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.TowerChallengeResp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.TowerChallengeResp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.TowerChallengeResp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.TowerChallengeResp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.TowerChallengeResp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.TowerChallengeResp)
      xddq.pb.TowerChallengeRespOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_TowerChallengeResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_TowerChallengeResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.TowerChallengeResp.class, xddq.pb.TowerChallengeResp.Builder.class);
    }

    // Construct using xddq.pb.TowerChallengeResp.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetAllBattleRecordFieldBuilder();
        internalGetTowerDataSyncFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      allBattleRecord_ = null;
      if (allBattleRecordBuilder_ != null) {
        allBattleRecordBuilder_.dispose();
        allBattleRecordBuilder_ = null;
      }
      challengeSuccess_ = false;
      rewards_ = "";
      towerDataSync_ = null;
      if (towerDataSyncBuilder_ != null) {
        towerDataSyncBuilder_.dispose();
        towerDataSyncBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_TowerChallengeResp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.TowerChallengeResp getDefaultInstanceForType() {
      return xddq.pb.TowerChallengeResp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.TowerChallengeResp build() {
      xddq.pb.TowerChallengeResp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.TowerChallengeResp buildPartial() {
      xddq.pb.TowerChallengeResp result = new xddq.pb.TowerChallengeResp(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.TowerChallengeResp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.allBattleRecord_ = allBattleRecordBuilder_ == null
            ? allBattleRecord_
            : allBattleRecordBuilder_.build();
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.challengeSuccess_ = challengeSuccess_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.rewards_ = rewards_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.towerDataSync_ = towerDataSyncBuilder_ == null
            ? towerDataSync_
            : towerDataSyncBuilder_.build();
        to_bitField0_ |= 0x00000010;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.TowerChallengeResp) {
        return mergeFrom((xddq.pb.TowerChallengeResp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.TowerChallengeResp other) {
      if (other == xddq.pb.TowerChallengeResp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (other.hasAllBattleRecord()) {
        mergeAllBattleRecord(other.getAllBattleRecord());
      }
      if (other.hasChallengeSuccess()) {
        setChallengeSuccess(other.getChallengeSuccess());
      }
      if (other.hasRewards()) {
        rewards_ = other.rewards_;
        bitField0_ |= 0x00000008;
        onChanged();
      }
      if (other.hasTowerDataSync()) {
        mergeTowerDataSync(other.getTowerDataSync());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      if (hasAllBattleRecord()) {
        if (!getAllBattleRecord().isInitialized()) {
          return false;
        }
      }
      if (hasTowerDataSync()) {
        if (!getTowerDataSync().isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              input.readMessage(
                  internalGetAllBattleRecordFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 24: {
              challengeSuccess_ = input.readBool();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 34: {
              rewards_ = input.readBytes();
              bitField0_ |= 0x00000008;
              break;
            } // case 34
            case 42: {
              input.readMessage(
                  internalGetTowerDataSyncFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000010;
              break;
            } // case 42
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private xddq.pb.BattleRecordMsg allBattleRecord_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.BattleRecordMsg, xddq.pb.BattleRecordMsg.Builder, xddq.pb.BattleRecordMsgOrBuilder> allBattleRecordBuilder_;
    /**
     * <code>optional .xddq.pb.BattleRecordMsg allBattleRecord = 2;</code>
     * @return Whether the allBattleRecord field is set.
     */
    public boolean hasAllBattleRecord() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .xddq.pb.BattleRecordMsg allBattleRecord = 2;</code>
     * @return The allBattleRecord.
     */
    public xddq.pb.BattleRecordMsg getAllBattleRecord() {
      if (allBattleRecordBuilder_ == null) {
        return allBattleRecord_ == null ? xddq.pb.BattleRecordMsg.getDefaultInstance() : allBattleRecord_;
      } else {
        return allBattleRecordBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.BattleRecordMsg allBattleRecord = 2;</code>
     */
    public Builder setAllBattleRecord(xddq.pb.BattleRecordMsg value) {
      if (allBattleRecordBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        allBattleRecord_ = value;
      } else {
        allBattleRecordBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.BattleRecordMsg allBattleRecord = 2;</code>
     */
    public Builder setAllBattleRecord(
        xddq.pb.BattleRecordMsg.Builder builderForValue) {
      if (allBattleRecordBuilder_ == null) {
        allBattleRecord_ = builderForValue.build();
      } else {
        allBattleRecordBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.BattleRecordMsg allBattleRecord = 2;</code>
     */
    public Builder mergeAllBattleRecord(xddq.pb.BattleRecordMsg value) {
      if (allBattleRecordBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0) &&
          allBattleRecord_ != null &&
          allBattleRecord_ != xddq.pb.BattleRecordMsg.getDefaultInstance()) {
          getAllBattleRecordBuilder().mergeFrom(value);
        } else {
          allBattleRecord_ = value;
        }
      } else {
        allBattleRecordBuilder_.mergeFrom(value);
      }
      if (allBattleRecord_ != null) {
        bitField0_ |= 0x00000002;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.BattleRecordMsg allBattleRecord = 2;</code>
     */
    public Builder clearAllBattleRecord() {
      bitField0_ = (bitField0_ & ~0x00000002);
      allBattleRecord_ = null;
      if (allBattleRecordBuilder_ != null) {
        allBattleRecordBuilder_.dispose();
        allBattleRecordBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.BattleRecordMsg allBattleRecord = 2;</code>
     */
    public xddq.pb.BattleRecordMsg.Builder getAllBattleRecordBuilder() {
      bitField0_ |= 0x00000002;
      onChanged();
      return internalGetAllBattleRecordFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.BattleRecordMsg allBattleRecord = 2;</code>
     */
    public xddq.pb.BattleRecordMsgOrBuilder getAllBattleRecordOrBuilder() {
      if (allBattleRecordBuilder_ != null) {
        return allBattleRecordBuilder_.getMessageOrBuilder();
      } else {
        return allBattleRecord_ == null ?
            xddq.pb.BattleRecordMsg.getDefaultInstance() : allBattleRecord_;
      }
    }
    /**
     * <code>optional .xddq.pb.BattleRecordMsg allBattleRecord = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.BattleRecordMsg, xddq.pb.BattleRecordMsg.Builder, xddq.pb.BattleRecordMsgOrBuilder> 
        internalGetAllBattleRecordFieldBuilder() {
      if (allBattleRecordBuilder_ == null) {
        allBattleRecordBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.BattleRecordMsg, xddq.pb.BattleRecordMsg.Builder, xddq.pb.BattleRecordMsgOrBuilder>(
                getAllBattleRecord(),
                getParentForChildren(),
                isClean());
        allBattleRecord_ = null;
      }
      return allBattleRecordBuilder_;
    }

    private boolean challengeSuccess_ ;
    /**
     * <code>optional bool challengeSuccess = 3;</code>
     * @return Whether the challengeSuccess field is set.
     */
    @java.lang.Override
    public boolean hasChallengeSuccess() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional bool challengeSuccess = 3;</code>
     * @return The challengeSuccess.
     */
    @java.lang.Override
    public boolean getChallengeSuccess() {
      return challengeSuccess_;
    }
    /**
     * <code>optional bool challengeSuccess = 3;</code>
     * @param value The challengeSuccess to set.
     * @return This builder for chaining.
     */
    public Builder setChallengeSuccess(boolean value) {

      challengeSuccess_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional bool challengeSuccess = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearChallengeSuccess() {
      bitField0_ = (bitField0_ & ~0x00000004);
      challengeSuccess_ = false;
      onChanged();
      return this;
    }

    private java.lang.Object rewards_ = "";
    /**
     * <code>optional string rewards = 4;</code>
     * @return Whether the rewards field is set.
     */
    public boolean hasRewards() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional string rewards = 4;</code>
     * @return The rewards.
     */
    public java.lang.String getRewards() {
      java.lang.Object ref = rewards_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          rewards_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string rewards = 4;</code>
     * @return The bytes for rewards.
     */
    public com.google.protobuf.ByteString
        getRewardsBytes() {
      java.lang.Object ref = rewards_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        rewards_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string rewards = 4;</code>
     * @param value The rewards to set.
     * @return This builder for chaining.
     */
    public Builder setRewards(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      rewards_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional string rewards = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearRewards() {
      rewards_ = getDefaultInstance().getRewards();
      bitField0_ = (bitField0_ & ~0x00000008);
      onChanged();
      return this;
    }
    /**
     * <code>optional string rewards = 4;</code>
     * @param value The bytes for rewards to set.
     * @return This builder for chaining.
     */
    public Builder setRewardsBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      rewards_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }

    private xddq.pb.TowerDataMsg towerDataSync_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.TowerDataMsg, xddq.pb.TowerDataMsg.Builder, xddq.pb.TowerDataMsgOrBuilder> towerDataSyncBuilder_;
    /**
     * <code>optional .xddq.pb.TowerDataMsg towerDataSync = 5;</code>
     * @return Whether the towerDataSync field is set.
     */
    public boolean hasTowerDataSync() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional .xddq.pb.TowerDataMsg towerDataSync = 5;</code>
     * @return The towerDataSync.
     */
    public xddq.pb.TowerDataMsg getTowerDataSync() {
      if (towerDataSyncBuilder_ == null) {
        return towerDataSync_ == null ? xddq.pb.TowerDataMsg.getDefaultInstance() : towerDataSync_;
      } else {
        return towerDataSyncBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.TowerDataMsg towerDataSync = 5;</code>
     */
    public Builder setTowerDataSync(xddq.pb.TowerDataMsg value) {
      if (towerDataSyncBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        towerDataSync_ = value;
      } else {
        towerDataSyncBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.TowerDataMsg towerDataSync = 5;</code>
     */
    public Builder setTowerDataSync(
        xddq.pb.TowerDataMsg.Builder builderForValue) {
      if (towerDataSyncBuilder_ == null) {
        towerDataSync_ = builderForValue.build();
      } else {
        towerDataSyncBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.TowerDataMsg towerDataSync = 5;</code>
     */
    public Builder mergeTowerDataSync(xddq.pb.TowerDataMsg value) {
      if (towerDataSyncBuilder_ == null) {
        if (((bitField0_ & 0x00000010) != 0) &&
          towerDataSync_ != null &&
          towerDataSync_ != xddq.pb.TowerDataMsg.getDefaultInstance()) {
          getTowerDataSyncBuilder().mergeFrom(value);
        } else {
          towerDataSync_ = value;
        }
      } else {
        towerDataSyncBuilder_.mergeFrom(value);
      }
      if (towerDataSync_ != null) {
        bitField0_ |= 0x00000010;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.TowerDataMsg towerDataSync = 5;</code>
     */
    public Builder clearTowerDataSync() {
      bitField0_ = (bitField0_ & ~0x00000010);
      towerDataSync_ = null;
      if (towerDataSyncBuilder_ != null) {
        towerDataSyncBuilder_.dispose();
        towerDataSyncBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.TowerDataMsg towerDataSync = 5;</code>
     */
    public xddq.pb.TowerDataMsg.Builder getTowerDataSyncBuilder() {
      bitField0_ |= 0x00000010;
      onChanged();
      return internalGetTowerDataSyncFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.TowerDataMsg towerDataSync = 5;</code>
     */
    public xddq.pb.TowerDataMsgOrBuilder getTowerDataSyncOrBuilder() {
      if (towerDataSyncBuilder_ != null) {
        return towerDataSyncBuilder_.getMessageOrBuilder();
      } else {
        return towerDataSync_ == null ?
            xddq.pb.TowerDataMsg.getDefaultInstance() : towerDataSync_;
      }
    }
    /**
     * <code>optional .xddq.pb.TowerDataMsg towerDataSync = 5;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.TowerDataMsg, xddq.pb.TowerDataMsg.Builder, xddq.pb.TowerDataMsgOrBuilder> 
        internalGetTowerDataSyncFieldBuilder() {
      if (towerDataSyncBuilder_ == null) {
        towerDataSyncBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.TowerDataMsg, xddq.pb.TowerDataMsg.Builder, xddq.pb.TowerDataMsgOrBuilder>(
                getTowerDataSync(),
                getParentForChildren(),
                isClean());
        towerDataSync_ = null;
      }
      return towerDataSyncBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.TowerChallengeResp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.TowerChallengeResp)
  private static final xddq.pb.TowerChallengeResp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.TowerChallengeResp();
  }

  public static xddq.pb.TowerChallengeResp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<TowerChallengeResp>
      PARSER = new com.google.protobuf.AbstractParser<TowerChallengeResp>() {
    @java.lang.Override
    public TowerChallengeResp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<TowerChallengeResp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<TowerChallengeResp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.TowerChallengeResp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

