// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

public interface ManHuangApplyJoinTeamAgreeRespMsgOrBuilder extends
    // @@protoc_insertion_point(interface_extends:xddq.pb.ManHuangApplyJoinTeamAgreeRespMsg)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  boolean hasRet();
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  int getRet();

  /**
   * <code>optional .xddq.pb.ManHuangTeamEntity teamInfo = 2;</code>
   * @return Whether the teamInfo field is set.
   */
  boolean hasTeamInfo();
  /**
   * <code>optional .xddq.pb.ManHuangTeamEntity teamInfo = 2;</code>
   * @return The teamInfo.
   */
  xddq.pb.ManHuangTeamEntity getTeamInfo();
  /**
   * <code>optional .xddq.pb.ManHuangTeamEntity teamInfo = 2;</code>
   */
  xddq.pb.ManHuangTeamEntityOrBuilder getTeamInfoOrBuilder();
}
