// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.UnionBountyGetExistMonsterResp}
 */
public final class UnionBountyGetExistMonsterResp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.UnionBountyGetExistMonsterResp)
    UnionBountyGetExistMonsterRespOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      UnionBountyGetExistMonsterResp.class.getName());
  }
  // Use UnionBountyGetExistMonsterResp.newBuilder() to construct.
  private UnionBountyGetExistMonsterResp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private UnionBountyGetExistMonsterResp() {
    monsterIdList_ = emptyLongList();
    ownerMonsterIdList_ = emptyLongList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_UnionBountyGetExistMonsterResp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_UnionBountyGetExistMonsterResp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.UnionBountyGetExistMonsterResp.class, xddq.pb.UnionBountyGetExistMonsterResp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>optional int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int MONSTERIDLIST_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private com.google.protobuf.Internal.LongList monsterIdList_ =
      emptyLongList();
  /**
   * <code>repeated int64 monsterIdList = 2;</code>
   * @return A list containing the monsterIdList.
   */
  @java.lang.Override
  public java.util.List<java.lang.Long>
      getMonsterIdListList() {
    return monsterIdList_;
  }
  /**
   * <code>repeated int64 monsterIdList = 2;</code>
   * @return The count of monsterIdList.
   */
  public int getMonsterIdListCount() {
    return monsterIdList_.size();
  }
  /**
   * <code>repeated int64 monsterIdList = 2;</code>
   * @param index The index of the element to return.
   * @return The monsterIdList at the given index.
   */
  public long getMonsterIdList(int index) {
    return monsterIdList_.getLong(index);
  }

  public static final int OWNERMONSTERIDLIST_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private com.google.protobuf.Internal.LongList ownerMonsterIdList_ =
      emptyLongList();
  /**
   * <code>repeated int64 ownerMonsterIdList = 3;</code>
   * @return A list containing the ownerMonsterIdList.
   */
  @java.lang.Override
  public java.util.List<java.lang.Long>
      getOwnerMonsterIdListList() {
    return ownerMonsterIdList_;
  }
  /**
   * <code>repeated int64 ownerMonsterIdList = 3;</code>
   * @return The count of ownerMonsterIdList.
   */
  public int getOwnerMonsterIdListCount() {
    return ownerMonsterIdList_.size();
  }
  /**
   * <code>repeated int64 ownerMonsterIdList = 3;</code>
   * @param index The index of the element to return.
   * @return The ownerMonsterIdList at the given index.
   */
  public long getOwnerMonsterIdList(int index) {
    return ownerMonsterIdList_.getLong(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    for (int i = 0; i < monsterIdList_.size(); i++) {
      output.writeInt64(2, monsterIdList_.getLong(i));
    }
    for (int i = 0; i < ownerMonsterIdList_.size(); i++) {
      output.writeInt64(3, ownerMonsterIdList_.getLong(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    {
      int dataSize = 0;
      for (int i = 0; i < monsterIdList_.size(); i++) {
        dataSize += com.google.protobuf.CodedOutputStream
          .computeInt64SizeNoTag(monsterIdList_.getLong(i));
      }
      size += dataSize;
      size += 1 * getMonsterIdListList().size();
    }
    {
      int dataSize = 0;
      for (int i = 0; i < ownerMonsterIdList_.size(); i++) {
        dataSize += com.google.protobuf.CodedOutputStream
          .computeInt64SizeNoTag(ownerMonsterIdList_.getLong(i));
      }
      size += dataSize;
      size += 1 * getOwnerMonsterIdListList().size();
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.UnionBountyGetExistMonsterResp)) {
      return super.equals(obj);
    }
    xddq.pb.UnionBountyGetExistMonsterResp other = (xddq.pb.UnionBountyGetExistMonsterResp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (!getMonsterIdListList()
        .equals(other.getMonsterIdListList())) return false;
    if (!getOwnerMonsterIdListList()
        .equals(other.getOwnerMonsterIdListList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (getMonsterIdListCount() > 0) {
      hash = (37 * hash) + MONSTERIDLIST_FIELD_NUMBER;
      hash = (53 * hash) + getMonsterIdListList().hashCode();
    }
    if (getOwnerMonsterIdListCount() > 0) {
      hash = (37 * hash) + OWNERMONSTERIDLIST_FIELD_NUMBER;
      hash = (53 * hash) + getOwnerMonsterIdListList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.UnionBountyGetExistMonsterResp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.UnionBountyGetExistMonsterResp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.UnionBountyGetExistMonsterResp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.UnionBountyGetExistMonsterResp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.UnionBountyGetExistMonsterResp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.UnionBountyGetExistMonsterResp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.UnionBountyGetExistMonsterResp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.UnionBountyGetExistMonsterResp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.UnionBountyGetExistMonsterResp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.UnionBountyGetExistMonsterResp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.UnionBountyGetExistMonsterResp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.UnionBountyGetExistMonsterResp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.UnionBountyGetExistMonsterResp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.UnionBountyGetExistMonsterResp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.UnionBountyGetExistMonsterResp)
      xddq.pb.UnionBountyGetExistMonsterRespOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_UnionBountyGetExistMonsterResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_UnionBountyGetExistMonsterResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.UnionBountyGetExistMonsterResp.class, xddq.pb.UnionBountyGetExistMonsterResp.Builder.class);
    }

    // Construct using xddq.pb.UnionBountyGetExistMonsterResp.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      monsterIdList_ = emptyLongList();
      ownerMonsterIdList_ = emptyLongList();
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_UnionBountyGetExistMonsterResp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.UnionBountyGetExistMonsterResp getDefaultInstanceForType() {
      return xddq.pb.UnionBountyGetExistMonsterResp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.UnionBountyGetExistMonsterResp build() {
      xddq.pb.UnionBountyGetExistMonsterResp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.UnionBountyGetExistMonsterResp buildPartial() {
      xddq.pb.UnionBountyGetExistMonsterResp result = new xddq.pb.UnionBountyGetExistMonsterResp(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.UnionBountyGetExistMonsterResp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        monsterIdList_.makeImmutable();
        result.monsterIdList_ = monsterIdList_;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        ownerMonsterIdList_.makeImmutable();
        result.ownerMonsterIdList_ = ownerMonsterIdList_;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.UnionBountyGetExistMonsterResp) {
        return mergeFrom((xddq.pb.UnionBountyGetExistMonsterResp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.UnionBountyGetExistMonsterResp other) {
      if (other == xddq.pb.UnionBountyGetExistMonsterResp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (!other.monsterIdList_.isEmpty()) {
        if (monsterIdList_.isEmpty()) {
          monsterIdList_ = other.monsterIdList_;
          monsterIdList_.makeImmutable();
          bitField0_ |= 0x00000002;
        } else {
          ensureMonsterIdListIsMutable();
          monsterIdList_.addAll(other.monsterIdList_);
        }
        onChanged();
      }
      if (!other.ownerMonsterIdList_.isEmpty()) {
        if (ownerMonsterIdList_.isEmpty()) {
          ownerMonsterIdList_ = other.ownerMonsterIdList_;
          ownerMonsterIdList_.makeImmutable();
          bitField0_ |= 0x00000004;
        } else {
          ensureOwnerMonsterIdListIsMutable();
          ownerMonsterIdList_.addAll(other.ownerMonsterIdList_);
        }
        onChanged();
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              long v = input.readInt64();
              ensureMonsterIdListIsMutable();
              monsterIdList_.addLong(v);
              break;
            } // case 16
            case 18: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              ensureMonsterIdListIsMutable();
              while (input.getBytesUntilLimit() > 0) {
                monsterIdList_.addLong(input.readInt64());
              }
              input.popLimit(limit);
              break;
            } // case 18
            case 24: {
              long v = input.readInt64();
              ensureOwnerMonsterIdListIsMutable();
              ownerMonsterIdList_.addLong(v);
              break;
            } // case 24
            case 26: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              ensureOwnerMonsterIdListIsMutable();
              while (input.getBytesUntilLimit() > 0) {
                ownerMonsterIdList_.addLong(input.readInt64());
              }
              input.popLimit(limit);
              break;
            } // case 26
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>optional int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>optional int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private com.google.protobuf.Internal.LongList monsterIdList_ = emptyLongList();
    private void ensureMonsterIdListIsMutable() {
      if (!monsterIdList_.isModifiable()) {
        monsterIdList_ = makeMutableCopy(monsterIdList_);
      }
      bitField0_ |= 0x00000002;
    }
    /**
     * <code>repeated int64 monsterIdList = 2;</code>
     * @return A list containing the monsterIdList.
     */
    public java.util.List<java.lang.Long>
        getMonsterIdListList() {
      monsterIdList_.makeImmutable();
      return monsterIdList_;
    }
    /**
     * <code>repeated int64 monsterIdList = 2;</code>
     * @return The count of monsterIdList.
     */
    public int getMonsterIdListCount() {
      return monsterIdList_.size();
    }
    /**
     * <code>repeated int64 monsterIdList = 2;</code>
     * @param index The index of the element to return.
     * @return The monsterIdList at the given index.
     */
    public long getMonsterIdList(int index) {
      return monsterIdList_.getLong(index);
    }
    /**
     * <code>repeated int64 monsterIdList = 2;</code>
     * @param index The index to set the value at.
     * @param value The monsterIdList to set.
     * @return This builder for chaining.
     */
    public Builder setMonsterIdList(
        int index, long value) {

      ensureMonsterIdListIsMutable();
      monsterIdList_.setLong(index, value);
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int64 monsterIdList = 2;</code>
     * @param value The monsterIdList to add.
     * @return This builder for chaining.
     */
    public Builder addMonsterIdList(long value) {

      ensureMonsterIdListIsMutable();
      monsterIdList_.addLong(value);
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int64 monsterIdList = 2;</code>
     * @param values The monsterIdList to add.
     * @return This builder for chaining.
     */
    public Builder addAllMonsterIdList(
        java.lang.Iterable<? extends java.lang.Long> values) {
      ensureMonsterIdListIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, monsterIdList_);
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int64 monsterIdList = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearMonsterIdList() {
      monsterIdList_ = emptyLongList();
      bitField0_ = (bitField0_ & ~0x00000002);
      onChanged();
      return this;
    }

    private com.google.protobuf.Internal.LongList ownerMonsterIdList_ = emptyLongList();
    private void ensureOwnerMonsterIdListIsMutable() {
      if (!ownerMonsterIdList_.isModifiable()) {
        ownerMonsterIdList_ = makeMutableCopy(ownerMonsterIdList_);
      }
      bitField0_ |= 0x00000004;
    }
    /**
     * <code>repeated int64 ownerMonsterIdList = 3;</code>
     * @return A list containing the ownerMonsterIdList.
     */
    public java.util.List<java.lang.Long>
        getOwnerMonsterIdListList() {
      ownerMonsterIdList_.makeImmutable();
      return ownerMonsterIdList_;
    }
    /**
     * <code>repeated int64 ownerMonsterIdList = 3;</code>
     * @return The count of ownerMonsterIdList.
     */
    public int getOwnerMonsterIdListCount() {
      return ownerMonsterIdList_.size();
    }
    /**
     * <code>repeated int64 ownerMonsterIdList = 3;</code>
     * @param index The index of the element to return.
     * @return The ownerMonsterIdList at the given index.
     */
    public long getOwnerMonsterIdList(int index) {
      return ownerMonsterIdList_.getLong(index);
    }
    /**
     * <code>repeated int64 ownerMonsterIdList = 3;</code>
     * @param index The index to set the value at.
     * @param value The ownerMonsterIdList to set.
     * @return This builder for chaining.
     */
    public Builder setOwnerMonsterIdList(
        int index, long value) {

      ensureOwnerMonsterIdListIsMutable();
      ownerMonsterIdList_.setLong(index, value);
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int64 ownerMonsterIdList = 3;</code>
     * @param value The ownerMonsterIdList to add.
     * @return This builder for chaining.
     */
    public Builder addOwnerMonsterIdList(long value) {

      ensureOwnerMonsterIdListIsMutable();
      ownerMonsterIdList_.addLong(value);
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int64 ownerMonsterIdList = 3;</code>
     * @param values The ownerMonsterIdList to add.
     * @return This builder for chaining.
     */
    public Builder addAllOwnerMonsterIdList(
        java.lang.Iterable<? extends java.lang.Long> values) {
      ensureOwnerMonsterIdListIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, ownerMonsterIdList_);
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int64 ownerMonsterIdList = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearOwnerMonsterIdList() {
      ownerMonsterIdList_ = emptyLongList();
      bitField0_ = (bitField0_ & ~0x00000004);
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.UnionBountyGetExistMonsterResp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.UnionBountyGetExistMonsterResp)
  private static final xddq.pb.UnionBountyGetExistMonsterResp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.UnionBountyGetExistMonsterResp();
  }

  public static xddq.pb.UnionBountyGetExistMonsterResp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<UnionBountyGetExistMonsterResp>
      PARSER = new com.google.protobuf.AbstractParser<UnionBountyGetExistMonsterResp>() {
    @java.lang.Override
    public UnionBountyGetExistMonsterResp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<UnionBountyGetExistMonsterResp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<UnionBountyGetExistMonsterResp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.UnionBountyGetExistMonsterResp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

