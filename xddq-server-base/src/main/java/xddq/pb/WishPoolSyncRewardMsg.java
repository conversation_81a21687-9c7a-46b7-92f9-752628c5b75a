// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.WishPoolSyncRewardMsg}
 */
public final class WishPoolSyncRewardMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.WishPoolSyncRewardMsg)
    WishPoolSyncRewardMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      WishPoolSyncRewardMsg.class.getName());
  }
  // Use WishPoolSyncRewardMsg.newBuilder() to construct.
  private WishPoolSyncRewardMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private WishPoolSyncRewardMsg() {
    reward_ = "";
    winnerList_ = java.util.Collections.emptyList();
    totalReward_ = "";
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_WishPoolSyncRewardMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_WishPoolSyncRewardMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.WishPoolSyncRewardMsg.class, xddq.pb.WishPoolSyncRewardMsg.Builder.class);
  }

  private int bitField0_;
  public static final int ACTIVITYID_FIELD_NUMBER = 1;
  private int activityId_ = 0;
  /**
   * <code>optional int32 activityId = 1;</code>
   * @return Whether the activityId field is set.
   */
  @java.lang.Override
  public boolean hasActivityId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int32 activityId = 1;</code>
   * @return The activityId.
   */
  @java.lang.Override
  public int getActivityId() {
    return activityId_;
  }

  public static final int REWARD_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private volatile java.lang.Object reward_ = "";
  /**
   * <code>optional string reward = 2;</code>
   * @return Whether the reward field is set.
   */
  @java.lang.Override
  public boolean hasReward() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional string reward = 2;</code>
   * @return The reward.
   */
  @java.lang.Override
  public java.lang.String getReward() {
    java.lang.Object ref = reward_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        reward_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string reward = 2;</code>
   * @return The bytes for reward.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getRewardBytes() {
    java.lang.Object ref = reward_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      reward_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int WINNERLIST_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.WishPoolWinnerInfo> winnerList_;
  /**
   * <code>repeated .xddq.pb.WishPoolWinnerInfo winnerList = 3;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.WishPoolWinnerInfo> getWinnerListList() {
    return winnerList_;
  }
  /**
   * <code>repeated .xddq.pb.WishPoolWinnerInfo winnerList = 3;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.WishPoolWinnerInfoOrBuilder> 
      getWinnerListOrBuilderList() {
    return winnerList_;
  }
  /**
   * <code>repeated .xddq.pb.WishPoolWinnerInfo winnerList = 3;</code>
   */
  @java.lang.Override
  public int getWinnerListCount() {
    return winnerList_.size();
  }
  /**
   * <code>repeated .xddq.pb.WishPoolWinnerInfo winnerList = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.WishPoolWinnerInfo getWinnerList(int index) {
    return winnerList_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.WishPoolWinnerInfo winnerList = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.WishPoolWinnerInfoOrBuilder getWinnerListOrBuilder(
      int index) {
    return winnerList_.get(index);
  }

  public static final int ISOPENREWARD_FIELD_NUMBER = 4;
  private boolean isOpenReward_ = false;
  /**
   * <code>optional bool isOpenReward = 4;</code>
   * @return Whether the isOpenReward field is set.
   */
  @java.lang.Override
  public boolean hasIsOpenReward() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional bool isOpenReward = 4;</code>
   * @return The isOpenReward.
   */
  @java.lang.Override
  public boolean getIsOpenReward() {
    return isOpenReward_;
  }

  public static final int TOTALREWARD_FIELD_NUMBER = 5;
  @SuppressWarnings("serial")
  private volatile java.lang.Object totalReward_ = "";
  /**
   * <code>optional string totalReward = 5;</code>
   * @return Whether the totalReward field is set.
   */
  @java.lang.Override
  public boolean hasTotalReward() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional string totalReward = 5;</code>
   * @return The totalReward.
   */
  @java.lang.Override
  public java.lang.String getTotalReward() {
    java.lang.Object ref = totalReward_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        totalReward_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string totalReward = 5;</code>
   * @return The bytes for totalReward.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getTotalRewardBytes() {
    java.lang.Object ref = totalReward_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      totalReward_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, activityId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 2, reward_);
    }
    for (int i = 0; i < winnerList_.size(); i++) {
      output.writeMessage(3, winnerList_.get(i));
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeBool(4, isOpenReward_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 5, totalReward_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, activityId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(2, reward_);
    }
    for (int i = 0; i < winnerList_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, winnerList_.get(i));
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(4, isOpenReward_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(5, totalReward_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.WishPoolSyncRewardMsg)) {
      return super.equals(obj);
    }
    xddq.pb.WishPoolSyncRewardMsg other = (xddq.pb.WishPoolSyncRewardMsg) obj;

    if (hasActivityId() != other.hasActivityId()) return false;
    if (hasActivityId()) {
      if (getActivityId()
          != other.getActivityId()) return false;
    }
    if (hasReward() != other.hasReward()) return false;
    if (hasReward()) {
      if (!getReward()
          .equals(other.getReward())) return false;
    }
    if (!getWinnerListList()
        .equals(other.getWinnerListList())) return false;
    if (hasIsOpenReward() != other.hasIsOpenReward()) return false;
    if (hasIsOpenReward()) {
      if (getIsOpenReward()
          != other.getIsOpenReward()) return false;
    }
    if (hasTotalReward() != other.hasTotalReward()) return false;
    if (hasTotalReward()) {
      if (!getTotalReward()
          .equals(other.getTotalReward())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasActivityId()) {
      hash = (37 * hash) + ACTIVITYID_FIELD_NUMBER;
      hash = (53 * hash) + getActivityId();
    }
    if (hasReward()) {
      hash = (37 * hash) + REWARD_FIELD_NUMBER;
      hash = (53 * hash) + getReward().hashCode();
    }
    if (getWinnerListCount() > 0) {
      hash = (37 * hash) + WINNERLIST_FIELD_NUMBER;
      hash = (53 * hash) + getWinnerListList().hashCode();
    }
    if (hasIsOpenReward()) {
      hash = (37 * hash) + ISOPENREWARD_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getIsOpenReward());
    }
    if (hasTotalReward()) {
      hash = (37 * hash) + TOTALREWARD_FIELD_NUMBER;
      hash = (53 * hash) + getTotalReward().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.WishPoolSyncRewardMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.WishPoolSyncRewardMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.WishPoolSyncRewardMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.WishPoolSyncRewardMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.WishPoolSyncRewardMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.WishPoolSyncRewardMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.WishPoolSyncRewardMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.WishPoolSyncRewardMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.WishPoolSyncRewardMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.WishPoolSyncRewardMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.WishPoolSyncRewardMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.WishPoolSyncRewardMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.WishPoolSyncRewardMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.WishPoolSyncRewardMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.WishPoolSyncRewardMsg)
      xddq.pb.WishPoolSyncRewardMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_WishPoolSyncRewardMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_WishPoolSyncRewardMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.WishPoolSyncRewardMsg.class, xddq.pb.WishPoolSyncRewardMsg.Builder.class);
    }

    // Construct using xddq.pb.WishPoolSyncRewardMsg.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      activityId_ = 0;
      reward_ = "";
      if (winnerListBuilder_ == null) {
        winnerList_ = java.util.Collections.emptyList();
      } else {
        winnerList_ = null;
        winnerListBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000004);
      isOpenReward_ = false;
      totalReward_ = "";
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_WishPoolSyncRewardMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.WishPoolSyncRewardMsg getDefaultInstanceForType() {
      return xddq.pb.WishPoolSyncRewardMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.WishPoolSyncRewardMsg build() {
      xddq.pb.WishPoolSyncRewardMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.WishPoolSyncRewardMsg buildPartial() {
      xddq.pb.WishPoolSyncRewardMsg result = new xddq.pb.WishPoolSyncRewardMsg(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.WishPoolSyncRewardMsg result) {
      if (winnerListBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0)) {
          winnerList_ = java.util.Collections.unmodifiableList(winnerList_);
          bitField0_ = (bitField0_ & ~0x00000004);
        }
        result.winnerList_ = winnerList_;
      } else {
        result.winnerList_ = winnerListBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.WishPoolSyncRewardMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.activityId_ = activityId_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.reward_ = reward_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.isOpenReward_ = isOpenReward_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.totalReward_ = totalReward_;
        to_bitField0_ |= 0x00000008;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.WishPoolSyncRewardMsg) {
        return mergeFrom((xddq.pb.WishPoolSyncRewardMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.WishPoolSyncRewardMsg other) {
      if (other == xddq.pb.WishPoolSyncRewardMsg.getDefaultInstance()) return this;
      if (other.hasActivityId()) {
        setActivityId(other.getActivityId());
      }
      if (other.hasReward()) {
        reward_ = other.reward_;
        bitField0_ |= 0x00000002;
        onChanged();
      }
      if (winnerListBuilder_ == null) {
        if (!other.winnerList_.isEmpty()) {
          if (winnerList_.isEmpty()) {
            winnerList_ = other.winnerList_;
            bitField0_ = (bitField0_ & ~0x00000004);
          } else {
            ensureWinnerListIsMutable();
            winnerList_.addAll(other.winnerList_);
          }
          onChanged();
        }
      } else {
        if (!other.winnerList_.isEmpty()) {
          if (winnerListBuilder_.isEmpty()) {
            winnerListBuilder_.dispose();
            winnerListBuilder_ = null;
            winnerList_ = other.winnerList_;
            bitField0_ = (bitField0_ & ~0x00000004);
            winnerListBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetWinnerListFieldBuilder() : null;
          } else {
            winnerListBuilder_.addAllMessages(other.winnerList_);
          }
        }
      }
      if (other.hasIsOpenReward()) {
        setIsOpenReward(other.getIsOpenReward());
      }
      if (other.hasTotalReward()) {
        totalReward_ = other.totalReward_;
        bitField0_ |= 0x00000010;
        onChanged();
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              activityId_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              reward_ = input.readBytes();
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 26: {
              xddq.pb.WishPoolWinnerInfo m =
                  input.readMessage(
                      xddq.pb.WishPoolWinnerInfo.parser(),
                      extensionRegistry);
              if (winnerListBuilder_ == null) {
                ensureWinnerListIsMutable();
                winnerList_.add(m);
              } else {
                winnerListBuilder_.addMessage(m);
              }
              break;
            } // case 26
            case 32: {
              isOpenReward_ = input.readBool();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            case 42: {
              totalReward_ = input.readBytes();
              bitField0_ |= 0x00000010;
              break;
            } // case 42
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int activityId_ ;
    /**
     * <code>optional int32 activityId = 1;</code>
     * @return Whether the activityId field is set.
     */
    @java.lang.Override
    public boolean hasActivityId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 activityId = 1;</code>
     * @return The activityId.
     */
    @java.lang.Override
    public int getActivityId() {
      return activityId_;
    }
    /**
     * <code>optional int32 activityId = 1;</code>
     * @param value The activityId to set.
     * @return This builder for chaining.
     */
    public Builder setActivityId(int value) {

      activityId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 activityId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearActivityId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      activityId_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object reward_ = "";
    /**
     * <code>optional string reward = 2;</code>
     * @return Whether the reward field is set.
     */
    public boolean hasReward() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional string reward = 2;</code>
     * @return The reward.
     */
    public java.lang.String getReward() {
      java.lang.Object ref = reward_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          reward_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string reward = 2;</code>
     * @return The bytes for reward.
     */
    public com.google.protobuf.ByteString
        getRewardBytes() {
      java.lang.Object ref = reward_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        reward_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string reward = 2;</code>
     * @param value The reward to set.
     * @return This builder for chaining.
     */
    public Builder setReward(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      reward_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional string reward = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearReward() {
      reward_ = getDefaultInstance().getReward();
      bitField0_ = (bitField0_ & ~0x00000002);
      onChanged();
      return this;
    }
    /**
     * <code>optional string reward = 2;</code>
     * @param value The bytes for reward to set.
     * @return This builder for chaining.
     */
    public Builder setRewardBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      reward_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.WishPoolWinnerInfo> winnerList_ =
      java.util.Collections.emptyList();
    private void ensureWinnerListIsMutable() {
      if (!((bitField0_ & 0x00000004) != 0)) {
        winnerList_ = new java.util.ArrayList<xddq.pb.WishPoolWinnerInfo>(winnerList_);
        bitField0_ |= 0x00000004;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.WishPoolWinnerInfo, xddq.pb.WishPoolWinnerInfo.Builder, xddq.pb.WishPoolWinnerInfoOrBuilder> winnerListBuilder_;

    /**
     * <code>repeated .xddq.pb.WishPoolWinnerInfo winnerList = 3;</code>
     */
    public java.util.List<xddq.pb.WishPoolWinnerInfo> getWinnerListList() {
      if (winnerListBuilder_ == null) {
        return java.util.Collections.unmodifiableList(winnerList_);
      } else {
        return winnerListBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.WishPoolWinnerInfo winnerList = 3;</code>
     */
    public int getWinnerListCount() {
      if (winnerListBuilder_ == null) {
        return winnerList_.size();
      } else {
        return winnerListBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.WishPoolWinnerInfo winnerList = 3;</code>
     */
    public xddq.pb.WishPoolWinnerInfo getWinnerList(int index) {
      if (winnerListBuilder_ == null) {
        return winnerList_.get(index);
      } else {
        return winnerListBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.WishPoolWinnerInfo winnerList = 3;</code>
     */
    public Builder setWinnerList(
        int index, xddq.pb.WishPoolWinnerInfo value) {
      if (winnerListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureWinnerListIsMutable();
        winnerList_.set(index, value);
        onChanged();
      } else {
        winnerListBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WishPoolWinnerInfo winnerList = 3;</code>
     */
    public Builder setWinnerList(
        int index, xddq.pb.WishPoolWinnerInfo.Builder builderForValue) {
      if (winnerListBuilder_ == null) {
        ensureWinnerListIsMutable();
        winnerList_.set(index, builderForValue.build());
        onChanged();
      } else {
        winnerListBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WishPoolWinnerInfo winnerList = 3;</code>
     */
    public Builder addWinnerList(xddq.pb.WishPoolWinnerInfo value) {
      if (winnerListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureWinnerListIsMutable();
        winnerList_.add(value);
        onChanged();
      } else {
        winnerListBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WishPoolWinnerInfo winnerList = 3;</code>
     */
    public Builder addWinnerList(
        int index, xddq.pb.WishPoolWinnerInfo value) {
      if (winnerListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureWinnerListIsMutable();
        winnerList_.add(index, value);
        onChanged();
      } else {
        winnerListBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WishPoolWinnerInfo winnerList = 3;</code>
     */
    public Builder addWinnerList(
        xddq.pb.WishPoolWinnerInfo.Builder builderForValue) {
      if (winnerListBuilder_ == null) {
        ensureWinnerListIsMutable();
        winnerList_.add(builderForValue.build());
        onChanged();
      } else {
        winnerListBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WishPoolWinnerInfo winnerList = 3;</code>
     */
    public Builder addWinnerList(
        int index, xddq.pb.WishPoolWinnerInfo.Builder builderForValue) {
      if (winnerListBuilder_ == null) {
        ensureWinnerListIsMutable();
        winnerList_.add(index, builderForValue.build());
        onChanged();
      } else {
        winnerListBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WishPoolWinnerInfo winnerList = 3;</code>
     */
    public Builder addAllWinnerList(
        java.lang.Iterable<? extends xddq.pb.WishPoolWinnerInfo> values) {
      if (winnerListBuilder_ == null) {
        ensureWinnerListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, winnerList_);
        onChanged();
      } else {
        winnerListBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WishPoolWinnerInfo winnerList = 3;</code>
     */
    public Builder clearWinnerList() {
      if (winnerListBuilder_ == null) {
        winnerList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
      } else {
        winnerListBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WishPoolWinnerInfo winnerList = 3;</code>
     */
    public Builder removeWinnerList(int index) {
      if (winnerListBuilder_ == null) {
        ensureWinnerListIsMutable();
        winnerList_.remove(index);
        onChanged();
      } else {
        winnerListBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WishPoolWinnerInfo winnerList = 3;</code>
     */
    public xddq.pb.WishPoolWinnerInfo.Builder getWinnerListBuilder(
        int index) {
      return internalGetWinnerListFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.WishPoolWinnerInfo winnerList = 3;</code>
     */
    public xddq.pb.WishPoolWinnerInfoOrBuilder getWinnerListOrBuilder(
        int index) {
      if (winnerListBuilder_ == null) {
        return winnerList_.get(index);  } else {
        return winnerListBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.WishPoolWinnerInfo winnerList = 3;</code>
     */
    public java.util.List<? extends xddq.pb.WishPoolWinnerInfoOrBuilder> 
         getWinnerListOrBuilderList() {
      if (winnerListBuilder_ != null) {
        return winnerListBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(winnerList_);
      }
    }
    /**
     * <code>repeated .xddq.pb.WishPoolWinnerInfo winnerList = 3;</code>
     */
    public xddq.pb.WishPoolWinnerInfo.Builder addWinnerListBuilder() {
      return internalGetWinnerListFieldBuilder().addBuilder(
          xddq.pb.WishPoolWinnerInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.WishPoolWinnerInfo winnerList = 3;</code>
     */
    public xddq.pb.WishPoolWinnerInfo.Builder addWinnerListBuilder(
        int index) {
      return internalGetWinnerListFieldBuilder().addBuilder(
          index, xddq.pb.WishPoolWinnerInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.WishPoolWinnerInfo winnerList = 3;</code>
     */
    public java.util.List<xddq.pb.WishPoolWinnerInfo.Builder> 
         getWinnerListBuilderList() {
      return internalGetWinnerListFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.WishPoolWinnerInfo, xddq.pb.WishPoolWinnerInfo.Builder, xddq.pb.WishPoolWinnerInfoOrBuilder> 
        internalGetWinnerListFieldBuilder() {
      if (winnerListBuilder_ == null) {
        winnerListBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.WishPoolWinnerInfo, xddq.pb.WishPoolWinnerInfo.Builder, xddq.pb.WishPoolWinnerInfoOrBuilder>(
                winnerList_,
                ((bitField0_ & 0x00000004) != 0),
                getParentForChildren(),
                isClean());
        winnerList_ = null;
      }
      return winnerListBuilder_;
    }

    private boolean isOpenReward_ ;
    /**
     * <code>optional bool isOpenReward = 4;</code>
     * @return Whether the isOpenReward field is set.
     */
    @java.lang.Override
    public boolean hasIsOpenReward() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional bool isOpenReward = 4;</code>
     * @return The isOpenReward.
     */
    @java.lang.Override
    public boolean getIsOpenReward() {
      return isOpenReward_;
    }
    /**
     * <code>optional bool isOpenReward = 4;</code>
     * @param value The isOpenReward to set.
     * @return This builder for chaining.
     */
    public Builder setIsOpenReward(boolean value) {

      isOpenReward_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional bool isOpenReward = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearIsOpenReward() {
      bitField0_ = (bitField0_ & ~0x00000008);
      isOpenReward_ = false;
      onChanged();
      return this;
    }

    private java.lang.Object totalReward_ = "";
    /**
     * <code>optional string totalReward = 5;</code>
     * @return Whether the totalReward field is set.
     */
    public boolean hasTotalReward() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional string totalReward = 5;</code>
     * @return The totalReward.
     */
    public java.lang.String getTotalReward() {
      java.lang.Object ref = totalReward_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          totalReward_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string totalReward = 5;</code>
     * @return The bytes for totalReward.
     */
    public com.google.protobuf.ByteString
        getTotalRewardBytes() {
      java.lang.Object ref = totalReward_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        totalReward_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string totalReward = 5;</code>
     * @param value The totalReward to set.
     * @return This builder for chaining.
     */
    public Builder setTotalReward(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      totalReward_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional string totalReward = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearTotalReward() {
      totalReward_ = getDefaultInstance().getTotalReward();
      bitField0_ = (bitField0_ & ~0x00000010);
      onChanged();
      return this;
    }
    /**
     * <code>optional string totalReward = 5;</code>
     * @param value The bytes for totalReward to set.
     * @return This builder for chaining.
     */
    public Builder setTotalRewardBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      totalReward_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.WishPoolSyncRewardMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.WishPoolSyncRewardMsg)
  private static final xddq.pb.WishPoolSyncRewardMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.WishPoolSyncRewardMsg();
  }

  public static xddq.pb.WishPoolSyncRewardMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<WishPoolSyncRewardMsg>
      PARSER = new com.google.protobuf.AbstractParser<WishPoolSyncRewardMsg>() {
    @java.lang.Override
    public WishPoolSyncRewardMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<WishPoolSyncRewardMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<WishPoolSyncRewardMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.WishPoolSyncRewardMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

