// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.UnionBountyOpenAskHelpResp}
 */
public final class UnionBountyOpenAskHelpResp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.UnionBountyOpenAskHelpResp)
    UnionBountyOpenAskHelpRespOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      UnionBountyOpenAskHelpResp.class.getName());
  }
  // Use UnionBountyOpenAskHelpResp.newBuilder() to construct.
  private UnionBountyOpenAskHelpResp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private UnionBountyOpenAskHelpResp() {
    askHelpMsgList_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_UnionBountyOpenAskHelpResp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_UnionBountyOpenAskHelpResp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.UnionBountyOpenAskHelpResp.class, xddq.pb.UnionBountyOpenAskHelpResp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>optional int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int ASKHELPMSGLIST_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.UnionBountyAskHelpMsg> askHelpMsgList_;
  /**
   * <code>repeated .xddq.pb.UnionBountyAskHelpMsg askHelpMsgList = 2;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.UnionBountyAskHelpMsg> getAskHelpMsgListList() {
    return askHelpMsgList_;
  }
  /**
   * <code>repeated .xddq.pb.UnionBountyAskHelpMsg askHelpMsgList = 2;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.UnionBountyAskHelpMsgOrBuilder> 
      getAskHelpMsgListOrBuilderList() {
    return askHelpMsgList_;
  }
  /**
   * <code>repeated .xddq.pb.UnionBountyAskHelpMsg askHelpMsgList = 2;</code>
   */
  @java.lang.Override
  public int getAskHelpMsgListCount() {
    return askHelpMsgList_.size();
  }
  /**
   * <code>repeated .xddq.pb.UnionBountyAskHelpMsg askHelpMsgList = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.UnionBountyAskHelpMsg getAskHelpMsgList(int index) {
    return askHelpMsgList_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.UnionBountyAskHelpMsg askHelpMsgList = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.UnionBountyAskHelpMsgOrBuilder getAskHelpMsgListOrBuilder(
      int index) {
    return askHelpMsgList_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    for (int i = 0; i < askHelpMsgList_.size(); i++) {
      output.writeMessage(2, askHelpMsgList_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    for (int i = 0; i < askHelpMsgList_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, askHelpMsgList_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.UnionBountyOpenAskHelpResp)) {
      return super.equals(obj);
    }
    xddq.pb.UnionBountyOpenAskHelpResp other = (xddq.pb.UnionBountyOpenAskHelpResp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (!getAskHelpMsgListList()
        .equals(other.getAskHelpMsgListList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (getAskHelpMsgListCount() > 0) {
      hash = (37 * hash) + ASKHELPMSGLIST_FIELD_NUMBER;
      hash = (53 * hash) + getAskHelpMsgListList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.UnionBountyOpenAskHelpResp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.UnionBountyOpenAskHelpResp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.UnionBountyOpenAskHelpResp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.UnionBountyOpenAskHelpResp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.UnionBountyOpenAskHelpResp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.UnionBountyOpenAskHelpResp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.UnionBountyOpenAskHelpResp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.UnionBountyOpenAskHelpResp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.UnionBountyOpenAskHelpResp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.UnionBountyOpenAskHelpResp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.UnionBountyOpenAskHelpResp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.UnionBountyOpenAskHelpResp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.UnionBountyOpenAskHelpResp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.UnionBountyOpenAskHelpResp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.UnionBountyOpenAskHelpResp)
      xddq.pb.UnionBountyOpenAskHelpRespOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_UnionBountyOpenAskHelpResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_UnionBountyOpenAskHelpResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.UnionBountyOpenAskHelpResp.class, xddq.pb.UnionBountyOpenAskHelpResp.Builder.class);
    }

    // Construct using xddq.pb.UnionBountyOpenAskHelpResp.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      if (askHelpMsgListBuilder_ == null) {
        askHelpMsgList_ = java.util.Collections.emptyList();
      } else {
        askHelpMsgList_ = null;
        askHelpMsgListBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000002);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_UnionBountyOpenAskHelpResp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.UnionBountyOpenAskHelpResp getDefaultInstanceForType() {
      return xddq.pb.UnionBountyOpenAskHelpResp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.UnionBountyOpenAskHelpResp build() {
      xddq.pb.UnionBountyOpenAskHelpResp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.UnionBountyOpenAskHelpResp buildPartial() {
      xddq.pb.UnionBountyOpenAskHelpResp result = new xddq.pb.UnionBountyOpenAskHelpResp(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.UnionBountyOpenAskHelpResp result) {
      if (askHelpMsgListBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          askHelpMsgList_ = java.util.Collections.unmodifiableList(askHelpMsgList_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.askHelpMsgList_ = askHelpMsgList_;
      } else {
        result.askHelpMsgList_ = askHelpMsgListBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.UnionBountyOpenAskHelpResp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.UnionBountyOpenAskHelpResp) {
        return mergeFrom((xddq.pb.UnionBountyOpenAskHelpResp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.UnionBountyOpenAskHelpResp other) {
      if (other == xddq.pb.UnionBountyOpenAskHelpResp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (askHelpMsgListBuilder_ == null) {
        if (!other.askHelpMsgList_.isEmpty()) {
          if (askHelpMsgList_.isEmpty()) {
            askHelpMsgList_ = other.askHelpMsgList_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureAskHelpMsgListIsMutable();
            askHelpMsgList_.addAll(other.askHelpMsgList_);
          }
          onChanged();
        }
      } else {
        if (!other.askHelpMsgList_.isEmpty()) {
          if (askHelpMsgListBuilder_.isEmpty()) {
            askHelpMsgListBuilder_.dispose();
            askHelpMsgListBuilder_ = null;
            askHelpMsgList_ = other.askHelpMsgList_;
            bitField0_ = (bitField0_ & ~0x00000002);
            askHelpMsgListBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetAskHelpMsgListFieldBuilder() : null;
          } else {
            askHelpMsgListBuilder_.addAllMessages(other.askHelpMsgList_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              xddq.pb.UnionBountyAskHelpMsg m =
                  input.readMessage(
                      xddq.pb.UnionBountyAskHelpMsg.parser(),
                      extensionRegistry);
              if (askHelpMsgListBuilder_ == null) {
                ensureAskHelpMsgListIsMutable();
                askHelpMsgList_.add(m);
              } else {
                askHelpMsgListBuilder_.addMessage(m);
              }
              break;
            } // case 18
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>optional int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>optional int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.UnionBountyAskHelpMsg> askHelpMsgList_ =
      java.util.Collections.emptyList();
    private void ensureAskHelpMsgListIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        askHelpMsgList_ = new java.util.ArrayList<xddq.pb.UnionBountyAskHelpMsg>(askHelpMsgList_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.UnionBountyAskHelpMsg, xddq.pb.UnionBountyAskHelpMsg.Builder, xddq.pb.UnionBountyAskHelpMsgOrBuilder> askHelpMsgListBuilder_;

    /**
     * <code>repeated .xddq.pb.UnionBountyAskHelpMsg askHelpMsgList = 2;</code>
     */
    public java.util.List<xddq.pb.UnionBountyAskHelpMsg> getAskHelpMsgListList() {
      if (askHelpMsgListBuilder_ == null) {
        return java.util.Collections.unmodifiableList(askHelpMsgList_);
      } else {
        return askHelpMsgListBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.UnionBountyAskHelpMsg askHelpMsgList = 2;</code>
     */
    public int getAskHelpMsgListCount() {
      if (askHelpMsgListBuilder_ == null) {
        return askHelpMsgList_.size();
      } else {
        return askHelpMsgListBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.UnionBountyAskHelpMsg askHelpMsgList = 2;</code>
     */
    public xddq.pb.UnionBountyAskHelpMsg getAskHelpMsgList(int index) {
      if (askHelpMsgListBuilder_ == null) {
        return askHelpMsgList_.get(index);
      } else {
        return askHelpMsgListBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.UnionBountyAskHelpMsg askHelpMsgList = 2;</code>
     */
    public Builder setAskHelpMsgList(
        int index, xddq.pb.UnionBountyAskHelpMsg value) {
      if (askHelpMsgListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureAskHelpMsgListIsMutable();
        askHelpMsgList_.set(index, value);
        onChanged();
      } else {
        askHelpMsgListBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionBountyAskHelpMsg askHelpMsgList = 2;</code>
     */
    public Builder setAskHelpMsgList(
        int index, xddq.pb.UnionBountyAskHelpMsg.Builder builderForValue) {
      if (askHelpMsgListBuilder_ == null) {
        ensureAskHelpMsgListIsMutable();
        askHelpMsgList_.set(index, builderForValue.build());
        onChanged();
      } else {
        askHelpMsgListBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionBountyAskHelpMsg askHelpMsgList = 2;</code>
     */
    public Builder addAskHelpMsgList(xddq.pb.UnionBountyAskHelpMsg value) {
      if (askHelpMsgListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureAskHelpMsgListIsMutable();
        askHelpMsgList_.add(value);
        onChanged();
      } else {
        askHelpMsgListBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionBountyAskHelpMsg askHelpMsgList = 2;</code>
     */
    public Builder addAskHelpMsgList(
        int index, xddq.pb.UnionBountyAskHelpMsg value) {
      if (askHelpMsgListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureAskHelpMsgListIsMutable();
        askHelpMsgList_.add(index, value);
        onChanged();
      } else {
        askHelpMsgListBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionBountyAskHelpMsg askHelpMsgList = 2;</code>
     */
    public Builder addAskHelpMsgList(
        xddq.pb.UnionBountyAskHelpMsg.Builder builderForValue) {
      if (askHelpMsgListBuilder_ == null) {
        ensureAskHelpMsgListIsMutable();
        askHelpMsgList_.add(builderForValue.build());
        onChanged();
      } else {
        askHelpMsgListBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionBountyAskHelpMsg askHelpMsgList = 2;</code>
     */
    public Builder addAskHelpMsgList(
        int index, xddq.pb.UnionBountyAskHelpMsg.Builder builderForValue) {
      if (askHelpMsgListBuilder_ == null) {
        ensureAskHelpMsgListIsMutable();
        askHelpMsgList_.add(index, builderForValue.build());
        onChanged();
      } else {
        askHelpMsgListBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionBountyAskHelpMsg askHelpMsgList = 2;</code>
     */
    public Builder addAllAskHelpMsgList(
        java.lang.Iterable<? extends xddq.pb.UnionBountyAskHelpMsg> values) {
      if (askHelpMsgListBuilder_ == null) {
        ensureAskHelpMsgListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, askHelpMsgList_);
        onChanged();
      } else {
        askHelpMsgListBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionBountyAskHelpMsg askHelpMsgList = 2;</code>
     */
    public Builder clearAskHelpMsgList() {
      if (askHelpMsgListBuilder_ == null) {
        askHelpMsgList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        askHelpMsgListBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionBountyAskHelpMsg askHelpMsgList = 2;</code>
     */
    public Builder removeAskHelpMsgList(int index) {
      if (askHelpMsgListBuilder_ == null) {
        ensureAskHelpMsgListIsMutable();
        askHelpMsgList_.remove(index);
        onChanged();
      } else {
        askHelpMsgListBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionBountyAskHelpMsg askHelpMsgList = 2;</code>
     */
    public xddq.pb.UnionBountyAskHelpMsg.Builder getAskHelpMsgListBuilder(
        int index) {
      return internalGetAskHelpMsgListFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.UnionBountyAskHelpMsg askHelpMsgList = 2;</code>
     */
    public xddq.pb.UnionBountyAskHelpMsgOrBuilder getAskHelpMsgListOrBuilder(
        int index) {
      if (askHelpMsgListBuilder_ == null) {
        return askHelpMsgList_.get(index);  } else {
        return askHelpMsgListBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.UnionBountyAskHelpMsg askHelpMsgList = 2;</code>
     */
    public java.util.List<? extends xddq.pb.UnionBountyAskHelpMsgOrBuilder> 
         getAskHelpMsgListOrBuilderList() {
      if (askHelpMsgListBuilder_ != null) {
        return askHelpMsgListBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(askHelpMsgList_);
      }
    }
    /**
     * <code>repeated .xddq.pb.UnionBountyAskHelpMsg askHelpMsgList = 2;</code>
     */
    public xddq.pb.UnionBountyAskHelpMsg.Builder addAskHelpMsgListBuilder() {
      return internalGetAskHelpMsgListFieldBuilder().addBuilder(
          xddq.pb.UnionBountyAskHelpMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.UnionBountyAskHelpMsg askHelpMsgList = 2;</code>
     */
    public xddq.pb.UnionBountyAskHelpMsg.Builder addAskHelpMsgListBuilder(
        int index) {
      return internalGetAskHelpMsgListFieldBuilder().addBuilder(
          index, xddq.pb.UnionBountyAskHelpMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.UnionBountyAskHelpMsg askHelpMsgList = 2;</code>
     */
    public java.util.List<xddq.pb.UnionBountyAskHelpMsg.Builder> 
         getAskHelpMsgListBuilderList() {
      return internalGetAskHelpMsgListFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.UnionBountyAskHelpMsg, xddq.pb.UnionBountyAskHelpMsg.Builder, xddq.pb.UnionBountyAskHelpMsgOrBuilder> 
        internalGetAskHelpMsgListFieldBuilder() {
      if (askHelpMsgListBuilder_ == null) {
        askHelpMsgListBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.UnionBountyAskHelpMsg, xddq.pb.UnionBountyAskHelpMsg.Builder, xddq.pb.UnionBountyAskHelpMsgOrBuilder>(
                askHelpMsgList_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        askHelpMsgList_ = null;
      }
      return askHelpMsgListBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.UnionBountyOpenAskHelpResp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.UnionBountyOpenAskHelpResp)
  private static final xddq.pb.UnionBountyOpenAskHelpResp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.UnionBountyOpenAskHelpResp();
  }

  public static xddq.pb.UnionBountyOpenAskHelpResp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<UnionBountyOpenAskHelpResp>
      PARSER = new com.google.protobuf.AbstractParser<UnionBountyOpenAskHelpResp>() {
    @java.lang.Override
    public UnionBountyOpenAskHelpResp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<UnionBountyOpenAskHelpResp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<UnionBountyOpenAskHelpResp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.UnionBountyOpenAskHelpResp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

