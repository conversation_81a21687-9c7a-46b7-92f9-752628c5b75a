// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.MonopolyScoreDetailResp}
 */
public final class MonopolyScoreDetailResp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.MonopolyScoreDetailResp)
    MonopolyScoreDetailRespOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      MonopolyScoreDetailResp.class.getName());
  }
  // Use MonopolyScoreDetailResp.newBuilder() to construct.
  private MonopolyScoreDetailResp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private MonopolyScoreDetailResp() {
    scoreDetail_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_MonopolyScoreDetailResp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_MonopolyScoreDetailResp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.MonopolyScoreDetailResp.class, xddq.pb.MonopolyScoreDetailResp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int SCOREDETAIL_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.MonopolyScoreDetailInfo> scoreDetail_;
  /**
   * <code>repeated .xddq.pb.MonopolyScoreDetailInfo scoreDetail = 2;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.MonopolyScoreDetailInfo> getScoreDetailList() {
    return scoreDetail_;
  }
  /**
   * <code>repeated .xddq.pb.MonopolyScoreDetailInfo scoreDetail = 2;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.MonopolyScoreDetailInfoOrBuilder> 
      getScoreDetailOrBuilderList() {
    return scoreDetail_;
  }
  /**
   * <code>repeated .xddq.pb.MonopolyScoreDetailInfo scoreDetail = 2;</code>
   */
  @java.lang.Override
  public int getScoreDetailCount() {
    return scoreDetail_.size();
  }
  /**
   * <code>repeated .xddq.pb.MonopolyScoreDetailInfo scoreDetail = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.MonopolyScoreDetailInfo getScoreDetail(int index) {
    return scoreDetail_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.MonopolyScoreDetailInfo scoreDetail = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.MonopolyScoreDetailInfoOrBuilder getScoreDetailOrBuilder(
      int index) {
    return scoreDetail_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    for (int i = 0; i < scoreDetail_.size(); i++) {
      output.writeMessage(2, scoreDetail_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    for (int i = 0; i < scoreDetail_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, scoreDetail_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.MonopolyScoreDetailResp)) {
      return super.equals(obj);
    }
    xddq.pb.MonopolyScoreDetailResp other = (xddq.pb.MonopolyScoreDetailResp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (!getScoreDetailList()
        .equals(other.getScoreDetailList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (getScoreDetailCount() > 0) {
      hash = (37 * hash) + SCOREDETAIL_FIELD_NUMBER;
      hash = (53 * hash) + getScoreDetailList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.MonopolyScoreDetailResp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.MonopolyScoreDetailResp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.MonopolyScoreDetailResp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.MonopolyScoreDetailResp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.MonopolyScoreDetailResp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.MonopolyScoreDetailResp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.MonopolyScoreDetailResp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.MonopolyScoreDetailResp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.MonopolyScoreDetailResp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.MonopolyScoreDetailResp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.MonopolyScoreDetailResp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.MonopolyScoreDetailResp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.MonopolyScoreDetailResp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.MonopolyScoreDetailResp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.MonopolyScoreDetailResp)
      xddq.pb.MonopolyScoreDetailRespOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_MonopolyScoreDetailResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_MonopolyScoreDetailResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.MonopolyScoreDetailResp.class, xddq.pb.MonopolyScoreDetailResp.Builder.class);
    }

    // Construct using xddq.pb.MonopolyScoreDetailResp.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      if (scoreDetailBuilder_ == null) {
        scoreDetail_ = java.util.Collections.emptyList();
      } else {
        scoreDetail_ = null;
        scoreDetailBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000002);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_MonopolyScoreDetailResp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.MonopolyScoreDetailResp getDefaultInstanceForType() {
      return xddq.pb.MonopolyScoreDetailResp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.MonopolyScoreDetailResp build() {
      xddq.pb.MonopolyScoreDetailResp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.MonopolyScoreDetailResp buildPartial() {
      xddq.pb.MonopolyScoreDetailResp result = new xddq.pb.MonopolyScoreDetailResp(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.MonopolyScoreDetailResp result) {
      if (scoreDetailBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          scoreDetail_ = java.util.Collections.unmodifiableList(scoreDetail_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.scoreDetail_ = scoreDetail_;
      } else {
        result.scoreDetail_ = scoreDetailBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.MonopolyScoreDetailResp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.MonopolyScoreDetailResp) {
        return mergeFrom((xddq.pb.MonopolyScoreDetailResp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.MonopolyScoreDetailResp other) {
      if (other == xddq.pb.MonopolyScoreDetailResp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (scoreDetailBuilder_ == null) {
        if (!other.scoreDetail_.isEmpty()) {
          if (scoreDetail_.isEmpty()) {
            scoreDetail_ = other.scoreDetail_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureScoreDetailIsMutable();
            scoreDetail_.addAll(other.scoreDetail_);
          }
          onChanged();
        }
      } else {
        if (!other.scoreDetail_.isEmpty()) {
          if (scoreDetailBuilder_.isEmpty()) {
            scoreDetailBuilder_.dispose();
            scoreDetailBuilder_ = null;
            scoreDetail_ = other.scoreDetail_;
            bitField0_ = (bitField0_ & ~0x00000002);
            scoreDetailBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetScoreDetailFieldBuilder() : null;
          } else {
            scoreDetailBuilder_.addAllMessages(other.scoreDetail_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              xddq.pb.MonopolyScoreDetailInfo m =
                  input.readMessage(
                      xddq.pb.MonopolyScoreDetailInfo.parser(),
                      extensionRegistry);
              if (scoreDetailBuilder_ == null) {
                ensureScoreDetailIsMutable();
                scoreDetail_.add(m);
              } else {
                scoreDetailBuilder_.addMessage(m);
              }
              break;
            } // case 18
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.MonopolyScoreDetailInfo> scoreDetail_ =
      java.util.Collections.emptyList();
    private void ensureScoreDetailIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        scoreDetail_ = new java.util.ArrayList<xddq.pb.MonopolyScoreDetailInfo>(scoreDetail_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.MonopolyScoreDetailInfo, xddq.pb.MonopolyScoreDetailInfo.Builder, xddq.pb.MonopolyScoreDetailInfoOrBuilder> scoreDetailBuilder_;

    /**
     * <code>repeated .xddq.pb.MonopolyScoreDetailInfo scoreDetail = 2;</code>
     */
    public java.util.List<xddq.pb.MonopolyScoreDetailInfo> getScoreDetailList() {
      if (scoreDetailBuilder_ == null) {
        return java.util.Collections.unmodifiableList(scoreDetail_);
      } else {
        return scoreDetailBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.MonopolyScoreDetailInfo scoreDetail = 2;</code>
     */
    public int getScoreDetailCount() {
      if (scoreDetailBuilder_ == null) {
        return scoreDetail_.size();
      } else {
        return scoreDetailBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.MonopolyScoreDetailInfo scoreDetail = 2;</code>
     */
    public xddq.pb.MonopolyScoreDetailInfo getScoreDetail(int index) {
      if (scoreDetailBuilder_ == null) {
        return scoreDetail_.get(index);
      } else {
        return scoreDetailBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.MonopolyScoreDetailInfo scoreDetail = 2;</code>
     */
    public Builder setScoreDetail(
        int index, xddq.pb.MonopolyScoreDetailInfo value) {
      if (scoreDetailBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureScoreDetailIsMutable();
        scoreDetail_.set(index, value);
        onChanged();
      } else {
        scoreDetailBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MonopolyScoreDetailInfo scoreDetail = 2;</code>
     */
    public Builder setScoreDetail(
        int index, xddq.pb.MonopolyScoreDetailInfo.Builder builderForValue) {
      if (scoreDetailBuilder_ == null) {
        ensureScoreDetailIsMutable();
        scoreDetail_.set(index, builderForValue.build());
        onChanged();
      } else {
        scoreDetailBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MonopolyScoreDetailInfo scoreDetail = 2;</code>
     */
    public Builder addScoreDetail(xddq.pb.MonopolyScoreDetailInfo value) {
      if (scoreDetailBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureScoreDetailIsMutable();
        scoreDetail_.add(value);
        onChanged();
      } else {
        scoreDetailBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MonopolyScoreDetailInfo scoreDetail = 2;</code>
     */
    public Builder addScoreDetail(
        int index, xddq.pb.MonopolyScoreDetailInfo value) {
      if (scoreDetailBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureScoreDetailIsMutable();
        scoreDetail_.add(index, value);
        onChanged();
      } else {
        scoreDetailBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MonopolyScoreDetailInfo scoreDetail = 2;</code>
     */
    public Builder addScoreDetail(
        xddq.pb.MonopolyScoreDetailInfo.Builder builderForValue) {
      if (scoreDetailBuilder_ == null) {
        ensureScoreDetailIsMutable();
        scoreDetail_.add(builderForValue.build());
        onChanged();
      } else {
        scoreDetailBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MonopolyScoreDetailInfo scoreDetail = 2;</code>
     */
    public Builder addScoreDetail(
        int index, xddq.pb.MonopolyScoreDetailInfo.Builder builderForValue) {
      if (scoreDetailBuilder_ == null) {
        ensureScoreDetailIsMutable();
        scoreDetail_.add(index, builderForValue.build());
        onChanged();
      } else {
        scoreDetailBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MonopolyScoreDetailInfo scoreDetail = 2;</code>
     */
    public Builder addAllScoreDetail(
        java.lang.Iterable<? extends xddq.pb.MonopolyScoreDetailInfo> values) {
      if (scoreDetailBuilder_ == null) {
        ensureScoreDetailIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, scoreDetail_);
        onChanged();
      } else {
        scoreDetailBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MonopolyScoreDetailInfo scoreDetail = 2;</code>
     */
    public Builder clearScoreDetail() {
      if (scoreDetailBuilder_ == null) {
        scoreDetail_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        scoreDetailBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MonopolyScoreDetailInfo scoreDetail = 2;</code>
     */
    public Builder removeScoreDetail(int index) {
      if (scoreDetailBuilder_ == null) {
        ensureScoreDetailIsMutable();
        scoreDetail_.remove(index);
        onChanged();
      } else {
        scoreDetailBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MonopolyScoreDetailInfo scoreDetail = 2;</code>
     */
    public xddq.pb.MonopolyScoreDetailInfo.Builder getScoreDetailBuilder(
        int index) {
      return internalGetScoreDetailFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.MonopolyScoreDetailInfo scoreDetail = 2;</code>
     */
    public xddq.pb.MonopolyScoreDetailInfoOrBuilder getScoreDetailOrBuilder(
        int index) {
      if (scoreDetailBuilder_ == null) {
        return scoreDetail_.get(index);  } else {
        return scoreDetailBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.MonopolyScoreDetailInfo scoreDetail = 2;</code>
     */
    public java.util.List<? extends xddq.pb.MonopolyScoreDetailInfoOrBuilder> 
         getScoreDetailOrBuilderList() {
      if (scoreDetailBuilder_ != null) {
        return scoreDetailBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(scoreDetail_);
      }
    }
    /**
     * <code>repeated .xddq.pb.MonopolyScoreDetailInfo scoreDetail = 2;</code>
     */
    public xddq.pb.MonopolyScoreDetailInfo.Builder addScoreDetailBuilder() {
      return internalGetScoreDetailFieldBuilder().addBuilder(
          xddq.pb.MonopolyScoreDetailInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.MonopolyScoreDetailInfo scoreDetail = 2;</code>
     */
    public xddq.pb.MonopolyScoreDetailInfo.Builder addScoreDetailBuilder(
        int index) {
      return internalGetScoreDetailFieldBuilder().addBuilder(
          index, xddq.pb.MonopolyScoreDetailInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.MonopolyScoreDetailInfo scoreDetail = 2;</code>
     */
    public java.util.List<xddq.pb.MonopolyScoreDetailInfo.Builder> 
         getScoreDetailBuilderList() {
      return internalGetScoreDetailFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.MonopolyScoreDetailInfo, xddq.pb.MonopolyScoreDetailInfo.Builder, xddq.pb.MonopolyScoreDetailInfoOrBuilder> 
        internalGetScoreDetailFieldBuilder() {
      if (scoreDetailBuilder_ == null) {
        scoreDetailBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.MonopolyScoreDetailInfo, xddq.pb.MonopolyScoreDetailInfo.Builder, xddq.pb.MonopolyScoreDetailInfoOrBuilder>(
                scoreDetail_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        scoreDetail_ = null;
      }
      return scoreDetailBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.MonopolyScoreDetailResp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.MonopolyScoreDetailResp)
  private static final xddq.pb.MonopolyScoreDetailResp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.MonopolyScoreDetailResp();
  }

  public static xddq.pb.MonopolyScoreDetailResp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<MonopolyScoreDetailResp>
      PARSER = new com.google.protobuf.AbstractParser<MonopolyScoreDetailResp>() {
    @java.lang.Override
    public MonopolyScoreDetailResp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<MonopolyScoreDetailResp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<MonopolyScoreDetailResp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.MonopolyScoreDetailResp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

