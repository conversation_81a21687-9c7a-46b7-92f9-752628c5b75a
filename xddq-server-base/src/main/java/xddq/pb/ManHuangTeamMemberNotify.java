// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.ManHuangTeamMemberNotify}
 */
public final class ManHuangTeamMemberNotify extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.ManHuangTeamMemberNotify)
    ManHuangTeamMemberNotifyOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      ManHuangTeamMemberNotify.class.getName());
  }
  // Use ManHuangTeamMemberNotify.newBuilder() to construct.
  private ManHuangTeamMemberNotify(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private ManHuangTeamMemberNotify() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_ManHuangTeamMemberNotify_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_ManHuangTeamMemberNotify_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.ManHuangTeamMemberNotify.class, xddq.pb.ManHuangTeamMemberNotify.Builder.class);
  }

  private int bitField0_;
  public static final int TYPE_FIELD_NUMBER = 1;
  private int type_ = 0;
  /**
   * <code>required int32 type = 1;</code>
   * @return Whether the type field is set.
   */
  @java.lang.Override
  public boolean hasType() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 type = 1;</code>
   * @return The type.
   */
  @java.lang.Override
  public int getType() {
    return type_;
  }

  public static final int TEAMINFO_FIELD_NUMBER = 2;
  private xddq.pb.ManHuangTeamEntity teamInfo_;
  /**
   * <code>optional .xddq.pb.ManHuangTeamEntity teamInfo = 2;</code>
   * @return Whether the teamInfo field is set.
   */
  @java.lang.Override
  public boolean hasTeamInfo() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional .xddq.pb.ManHuangTeamEntity teamInfo = 2;</code>
   * @return The teamInfo.
   */
  @java.lang.Override
  public xddq.pb.ManHuangTeamEntity getTeamInfo() {
    return teamInfo_ == null ? xddq.pb.ManHuangTeamEntity.getDefaultInstance() : teamInfo_;
  }
  /**
   * <code>optional .xddq.pb.ManHuangTeamEntity teamInfo = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.ManHuangTeamEntityOrBuilder getTeamInfoOrBuilder() {
    return teamInfo_ == null ? xddq.pb.ManHuangTeamEntity.getDefaultInstance() : teamInfo_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasType()) {
      memoizedIsInitialized = 0;
      return false;
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, type_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeMessage(2, getTeamInfo());
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, type_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getTeamInfo());
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.ManHuangTeamMemberNotify)) {
      return super.equals(obj);
    }
    xddq.pb.ManHuangTeamMemberNotify other = (xddq.pb.ManHuangTeamMemberNotify) obj;

    if (hasType() != other.hasType()) return false;
    if (hasType()) {
      if (getType()
          != other.getType()) return false;
    }
    if (hasTeamInfo() != other.hasTeamInfo()) return false;
    if (hasTeamInfo()) {
      if (!getTeamInfo()
          .equals(other.getTeamInfo())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasType()) {
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
    }
    if (hasTeamInfo()) {
      hash = (37 * hash) + TEAMINFO_FIELD_NUMBER;
      hash = (53 * hash) + getTeamInfo().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.ManHuangTeamMemberNotify parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ManHuangTeamMemberNotify parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ManHuangTeamMemberNotify parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ManHuangTeamMemberNotify parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ManHuangTeamMemberNotify parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ManHuangTeamMemberNotify parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ManHuangTeamMemberNotify parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.ManHuangTeamMemberNotify parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.ManHuangTeamMemberNotify parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.ManHuangTeamMemberNotify parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.ManHuangTeamMemberNotify parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.ManHuangTeamMemberNotify parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.ManHuangTeamMemberNotify prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.ManHuangTeamMemberNotify}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.ManHuangTeamMemberNotify)
      xddq.pb.ManHuangTeamMemberNotifyOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ManHuangTeamMemberNotify_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ManHuangTeamMemberNotify_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.ManHuangTeamMemberNotify.class, xddq.pb.ManHuangTeamMemberNotify.Builder.class);
    }

    // Construct using xddq.pb.ManHuangTeamMemberNotify.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetTeamInfoFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      type_ = 0;
      teamInfo_ = null;
      if (teamInfoBuilder_ != null) {
        teamInfoBuilder_.dispose();
        teamInfoBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ManHuangTeamMemberNotify_descriptor;
    }

    @java.lang.Override
    public xddq.pb.ManHuangTeamMemberNotify getDefaultInstanceForType() {
      return xddq.pb.ManHuangTeamMemberNotify.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.ManHuangTeamMemberNotify build() {
      xddq.pb.ManHuangTeamMemberNotify result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.ManHuangTeamMemberNotify buildPartial() {
      xddq.pb.ManHuangTeamMemberNotify result = new xddq.pb.ManHuangTeamMemberNotify(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.ManHuangTeamMemberNotify result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.type_ = type_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.teamInfo_ = teamInfoBuilder_ == null
            ? teamInfo_
            : teamInfoBuilder_.build();
        to_bitField0_ |= 0x00000002;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.ManHuangTeamMemberNotify) {
        return mergeFrom((xddq.pb.ManHuangTeamMemberNotify)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.ManHuangTeamMemberNotify other) {
      if (other == xddq.pb.ManHuangTeamMemberNotify.getDefaultInstance()) return this;
      if (other.hasType()) {
        setType(other.getType());
      }
      if (other.hasTeamInfo()) {
        mergeTeamInfo(other.getTeamInfo());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasType()) {
        return false;
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              type_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              input.readMessage(
                  internalGetTeamInfoFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int type_ ;
    /**
     * <code>required int32 type = 1;</code>
     * @return Whether the type field is set.
     */
    @java.lang.Override
    public boolean hasType() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 type = 1;</code>
     * @return The type.
     */
    @java.lang.Override
    public int getType() {
      return type_;
    }
    /**
     * <code>required int32 type = 1;</code>
     * @param value The type to set.
     * @return This builder for chaining.
     */
    public Builder setType(int value) {

      type_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 type = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearType() {
      bitField0_ = (bitField0_ & ~0x00000001);
      type_ = 0;
      onChanged();
      return this;
    }

    private xddq.pb.ManHuangTeamEntity teamInfo_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.ManHuangTeamEntity, xddq.pb.ManHuangTeamEntity.Builder, xddq.pb.ManHuangTeamEntityOrBuilder> teamInfoBuilder_;
    /**
     * <code>optional .xddq.pb.ManHuangTeamEntity teamInfo = 2;</code>
     * @return Whether the teamInfo field is set.
     */
    public boolean hasTeamInfo() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .xddq.pb.ManHuangTeamEntity teamInfo = 2;</code>
     * @return The teamInfo.
     */
    public xddq.pb.ManHuangTeamEntity getTeamInfo() {
      if (teamInfoBuilder_ == null) {
        return teamInfo_ == null ? xddq.pb.ManHuangTeamEntity.getDefaultInstance() : teamInfo_;
      } else {
        return teamInfoBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.ManHuangTeamEntity teamInfo = 2;</code>
     */
    public Builder setTeamInfo(xddq.pb.ManHuangTeamEntity value) {
      if (teamInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        teamInfo_ = value;
      } else {
        teamInfoBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ManHuangTeamEntity teamInfo = 2;</code>
     */
    public Builder setTeamInfo(
        xddq.pb.ManHuangTeamEntity.Builder builderForValue) {
      if (teamInfoBuilder_ == null) {
        teamInfo_ = builderForValue.build();
      } else {
        teamInfoBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ManHuangTeamEntity teamInfo = 2;</code>
     */
    public Builder mergeTeamInfo(xddq.pb.ManHuangTeamEntity value) {
      if (teamInfoBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0) &&
          teamInfo_ != null &&
          teamInfo_ != xddq.pb.ManHuangTeamEntity.getDefaultInstance()) {
          getTeamInfoBuilder().mergeFrom(value);
        } else {
          teamInfo_ = value;
        }
      } else {
        teamInfoBuilder_.mergeFrom(value);
      }
      if (teamInfo_ != null) {
        bitField0_ |= 0x00000002;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.ManHuangTeamEntity teamInfo = 2;</code>
     */
    public Builder clearTeamInfo() {
      bitField0_ = (bitField0_ & ~0x00000002);
      teamInfo_ = null;
      if (teamInfoBuilder_ != null) {
        teamInfoBuilder_.dispose();
        teamInfoBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ManHuangTeamEntity teamInfo = 2;</code>
     */
    public xddq.pb.ManHuangTeamEntity.Builder getTeamInfoBuilder() {
      bitField0_ |= 0x00000002;
      onChanged();
      return internalGetTeamInfoFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.ManHuangTeamEntity teamInfo = 2;</code>
     */
    public xddq.pb.ManHuangTeamEntityOrBuilder getTeamInfoOrBuilder() {
      if (teamInfoBuilder_ != null) {
        return teamInfoBuilder_.getMessageOrBuilder();
      } else {
        return teamInfo_ == null ?
            xddq.pb.ManHuangTeamEntity.getDefaultInstance() : teamInfo_;
      }
    }
    /**
     * <code>optional .xddq.pb.ManHuangTeamEntity teamInfo = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.ManHuangTeamEntity, xddq.pb.ManHuangTeamEntity.Builder, xddq.pb.ManHuangTeamEntityOrBuilder> 
        internalGetTeamInfoFieldBuilder() {
      if (teamInfoBuilder_ == null) {
        teamInfoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.ManHuangTeamEntity, xddq.pb.ManHuangTeamEntity.Builder, xddq.pb.ManHuangTeamEntityOrBuilder>(
                getTeamInfo(),
                getParentForChildren(),
                isClean());
        teamInfo_ = null;
      }
      return teamInfoBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.ManHuangTeamMemberNotify)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.ManHuangTeamMemberNotify)
  private static final xddq.pb.ManHuangTeamMemberNotify DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.ManHuangTeamMemberNotify();
  }

  public static xddq.pb.ManHuangTeamMemberNotify getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ManHuangTeamMemberNotify>
      PARSER = new com.google.protobuf.AbstractParser<ManHuangTeamMemberNotify>() {
    @java.lang.Override
    public ManHuangTeamMemberNotify parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<ManHuangTeamMemberNotify> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ManHuangTeamMemberNotify> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.ManHuangTeamMemberNotify getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

