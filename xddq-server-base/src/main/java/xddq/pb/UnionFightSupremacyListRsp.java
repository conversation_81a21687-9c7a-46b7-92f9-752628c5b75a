// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.UnionFightSupremacyListRsp}
 */
public final class UnionFightSupremacyListRsp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.UnionFightSupremacyListRsp)
    UnionFightSupremacyListRspOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      UnionFightSupremacyListRsp.class.getName());
  }
  // Use UnionFightSupremacyListRsp.newBuilder() to construct.
  private UnionFightSupremacyListRsp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private UnionFightSupremacyListRsp() {
    unionDataList_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_UnionFightSupremacyListRsp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_UnionFightSupremacyListRsp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.UnionFightSupremacyListRsp.class, xddq.pb.UnionFightSupremacyListRsp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int UNIONDATALIST_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.UnionFightSupremacyData> unionDataList_;
  /**
   * <code>repeated .xddq.pb.UnionFightSupremacyData unionDataList = 2;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.UnionFightSupremacyData> getUnionDataListList() {
    return unionDataList_;
  }
  /**
   * <code>repeated .xddq.pb.UnionFightSupremacyData unionDataList = 2;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.UnionFightSupremacyDataOrBuilder> 
      getUnionDataListOrBuilderList() {
    return unionDataList_;
  }
  /**
   * <code>repeated .xddq.pb.UnionFightSupremacyData unionDataList = 2;</code>
   */
  @java.lang.Override
  public int getUnionDataListCount() {
    return unionDataList_.size();
  }
  /**
   * <code>repeated .xddq.pb.UnionFightSupremacyData unionDataList = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.UnionFightSupremacyData getUnionDataList(int index) {
    return unionDataList_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.UnionFightSupremacyData unionDataList = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.UnionFightSupremacyDataOrBuilder getUnionDataListOrBuilder(
      int index) {
    return unionDataList_.get(index);
  }

  public static final int MYUNIONDATA_FIELD_NUMBER = 3;
  private xddq.pb.UnionFightSupremacyData myUnionData_;
  /**
   * <code>optional .xddq.pb.UnionFightSupremacyData myUnionData = 3;</code>
   * @return Whether the myUnionData field is set.
   */
  @java.lang.Override
  public boolean hasMyUnionData() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional .xddq.pb.UnionFightSupremacyData myUnionData = 3;</code>
   * @return The myUnionData.
   */
  @java.lang.Override
  public xddq.pb.UnionFightSupremacyData getMyUnionData() {
    return myUnionData_ == null ? xddq.pb.UnionFightSupremacyData.getDefaultInstance() : myUnionData_;
  }
  /**
   * <code>optional .xddq.pb.UnionFightSupremacyData myUnionData = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.UnionFightSupremacyDataOrBuilder getMyUnionDataOrBuilder() {
    return myUnionData_ == null ? xddq.pb.UnionFightSupremacyData.getDefaultInstance() : myUnionData_;
  }

  public static final int WORSHIP_FIELD_NUMBER = 4;
  private boolean worship_ = false;
  /**
   * <code>optional bool worship = 4;</code>
   * @return Whether the worship field is set.
   */
  @java.lang.Override
  public boolean hasWorship() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional bool worship = 4;</code>
   * @return The worship.
   */
  @java.lang.Override
  public boolean getWorship() {
    return worship_;
  }

  public static final int FIRSTNUM_FIELD_NUMBER = 5;
  private int firstNum_ = 0;
  /**
   * <code>optional int32 firstNum = 5;</code>
   * @return Whether the firstNum field is set.
   */
  @java.lang.Override
  public boolean hasFirstNum() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional int32 firstNum = 5;</code>
   * @return The firstNum.
   */
  @java.lang.Override
  public int getFirstNum() {
    return firstNum_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    for (int i = 0; i < getUnionDataListCount(); i++) {
      if (!getUnionDataList(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    if (hasMyUnionData()) {
      if (!getMyUnionData().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    for (int i = 0; i < unionDataList_.size(); i++) {
      output.writeMessage(2, unionDataList_.get(i));
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeMessage(3, getMyUnionData());
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeBool(4, worship_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt32(5, firstNum_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    for (int i = 0; i < unionDataList_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, unionDataList_.get(i));
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, getMyUnionData());
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(4, worship_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(5, firstNum_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.UnionFightSupremacyListRsp)) {
      return super.equals(obj);
    }
    xddq.pb.UnionFightSupremacyListRsp other = (xddq.pb.UnionFightSupremacyListRsp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (!getUnionDataListList()
        .equals(other.getUnionDataListList())) return false;
    if (hasMyUnionData() != other.hasMyUnionData()) return false;
    if (hasMyUnionData()) {
      if (!getMyUnionData()
          .equals(other.getMyUnionData())) return false;
    }
    if (hasWorship() != other.hasWorship()) return false;
    if (hasWorship()) {
      if (getWorship()
          != other.getWorship()) return false;
    }
    if (hasFirstNum() != other.hasFirstNum()) return false;
    if (hasFirstNum()) {
      if (getFirstNum()
          != other.getFirstNum()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (getUnionDataListCount() > 0) {
      hash = (37 * hash) + UNIONDATALIST_FIELD_NUMBER;
      hash = (53 * hash) + getUnionDataListList().hashCode();
    }
    if (hasMyUnionData()) {
      hash = (37 * hash) + MYUNIONDATA_FIELD_NUMBER;
      hash = (53 * hash) + getMyUnionData().hashCode();
    }
    if (hasWorship()) {
      hash = (37 * hash) + WORSHIP_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getWorship());
    }
    if (hasFirstNum()) {
      hash = (37 * hash) + FIRSTNUM_FIELD_NUMBER;
      hash = (53 * hash) + getFirstNum();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.UnionFightSupremacyListRsp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.UnionFightSupremacyListRsp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.UnionFightSupremacyListRsp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.UnionFightSupremacyListRsp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.UnionFightSupremacyListRsp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.UnionFightSupremacyListRsp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.UnionFightSupremacyListRsp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.UnionFightSupremacyListRsp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.UnionFightSupremacyListRsp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.UnionFightSupremacyListRsp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.UnionFightSupremacyListRsp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.UnionFightSupremacyListRsp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.UnionFightSupremacyListRsp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.UnionFightSupremacyListRsp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.UnionFightSupremacyListRsp)
      xddq.pb.UnionFightSupremacyListRspOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_UnionFightSupremacyListRsp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_UnionFightSupremacyListRsp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.UnionFightSupremacyListRsp.class, xddq.pb.UnionFightSupremacyListRsp.Builder.class);
    }

    // Construct using xddq.pb.UnionFightSupremacyListRsp.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetUnionDataListFieldBuilder();
        internalGetMyUnionDataFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      if (unionDataListBuilder_ == null) {
        unionDataList_ = java.util.Collections.emptyList();
      } else {
        unionDataList_ = null;
        unionDataListBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000002);
      myUnionData_ = null;
      if (myUnionDataBuilder_ != null) {
        myUnionDataBuilder_.dispose();
        myUnionDataBuilder_ = null;
      }
      worship_ = false;
      firstNum_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_UnionFightSupremacyListRsp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.UnionFightSupremacyListRsp getDefaultInstanceForType() {
      return xddq.pb.UnionFightSupremacyListRsp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.UnionFightSupremacyListRsp build() {
      xddq.pb.UnionFightSupremacyListRsp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.UnionFightSupremacyListRsp buildPartial() {
      xddq.pb.UnionFightSupremacyListRsp result = new xddq.pb.UnionFightSupremacyListRsp(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.UnionFightSupremacyListRsp result) {
      if (unionDataListBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          unionDataList_ = java.util.Collections.unmodifiableList(unionDataList_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.unionDataList_ = unionDataList_;
      } else {
        result.unionDataList_ = unionDataListBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.UnionFightSupremacyListRsp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.myUnionData_ = myUnionDataBuilder_ == null
            ? myUnionData_
            : myUnionDataBuilder_.build();
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.worship_ = worship_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.firstNum_ = firstNum_;
        to_bitField0_ |= 0x00000008;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.UnionFightSupremacyListRsp) {
        return mergeFrom((xddq.pb.UnionFightSupremacyListRsp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.UnionFightSupremacyListRsp other) {
      if (other == xddq.pb.UnionFightSupremacyListRsp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (unionDataListBuilder_ == null) {
        if (!other.unionDataList_.isEmpty()) {
          if (unionDataList_.isEmpty()) {
            unionDataList_ = other.unionDataList_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureUnionDataListIsMutable();
            unionDataList_.addAll(other.unionDataList_);
          }
          onChanged();
        }
      } else {
        if (!other.unionDataList_.isEmpty()) {
          if (unionDataListBuilder_.isEmpty()) {
            unionDataListBuilder_.dispose();
            unionDataListBuilder_ = null;
            unionDataList_ = other.unionDataList_;
            bitField0_ = (bitField0_ & ~0x00000002);
            unionDataListBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetUnionDataListFieldBuilder() : null;
          } else {
            unionDataListBuilder_.addAllMessages(other.unionDataList_);
          }
        }
      }
      if (other.hasMyUnionData()) {
        mergeMyUnionData(other.getMyUnionData());
      }
      if (other.hasWorship()) {
        setWorship(other.getWorship());
      }
      if (other.hasFirstNum()) {
        setFirstNum(other.getFirstNum());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      for (int i = 0; i < getUnionDataListCount(); i++) {
        if (!getUnionDataList(i).isInitialized()) {
          return false;
        }
      }
      if (hasMyUnionData()) {
        if (!getMyUnionData().isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              xddq.pb.UnionFightSupremacyData m =
                  input.readMessage(
                      xddq.pb.UnionFightSupremacyData.parser(),
                      extensionRegistry);
              if (unionDataListBuilder_ == null) {
                ensureUnionDataListIsMutable();
                unionDataList_.add(m);
              } else {
                unionDataListBuilder_.addMessage(m);
              }
              break;
            } // case 18
            case 26: {
              input.readMessage(
                  internalGetMyUnionDataFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 32: {
              worship_ = input.readBool();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            case 40: {
              firstNum_ = input.readInt32();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.UnionFightSupremacyData> unionDataList_ =
      java.util.Collections.emptyList();
    private void ensureUnionDataListIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        unionDataList_ = new java.util.ArrayList<xddq.pb.UnionFightSupremacyData>(unionDataList_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.UnionFightSupremacyData, xddq.pb.UnionFightSupremacyData.Builder, xddq.pb.UnionFightSupremacyDataOrBuilder> unionDataListBuilder_;

    /**
     * <code>repeated .xddq.pb.UnionFightSupremacyData unionDataList = 2;</code>
     */
    public java.util.List<xddq.pb.UnionFightSupremacyData> getUnionDataListList() {
      if (unionDataListBuilder_ == null) {
        return java.util.Collections.unmodifiableList(unionDataList_);
      } else {
        return unionDataListBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.UnionFightSupremacyData unionDataList = 2;</code>
     */
    public int getUnionDataListCount() {
      if (unionDataListBuilder_ == null) {
        return unionDataList_.size();
      } else {
        return unionDataListBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.UnionFightSupremacyData unionDataList = 2;</code>
     */
    public xddq.pb.UnionFightSupremacyData getUnionDataList(int index) {
      if (unionDataListBuilder_ == null) {
        return unionDataList_.get(index);
      } else {
        return unionDataListBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.UnionFightSupremacyData unionDataList = 2;</code>
     */
    public Builder setUnionDataList(
        int index, xddq.pb.UnionFightSupremacyData value) {
      if (unionDataListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureUnionDataListIsMutable();
        unionDataList_.set(index, value);
        onChanged();
      } else {
        unionDataListBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionFightSupremacyData unionDataList = 2;</code>
     */
    public Builder setUnionDataList(
        int index, xddq.pb.UnionFightSupremacyData.Builder builderForValue) {
      if (unionDataListBuilder_ == null) {
        ensureUnionDataListIsMutable();
        unionDataList_.set(index, builderForValue.build());
        onChanged();
      } else {
        unionDataListBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionFightSupremacyData unionDataList = 2;</code>
     */
    public Builder addUnionDataList(xddq.pb.UnionFightSupremacyData value) {
      if (unionDataListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureUnionDataListIsMutable();
        unionDataList_.add(value);
        onChanged();
      } else {
        unionDataListBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionFightSupremacyData unionDataList = 2;</code>
     */
    public Builder addUnionDataList(
        int index, xddq.pb.UnionFightSupremacyData value) {
      if (unionDataListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureUnionDataListIsMutable();
        unionDataList_.add(index, value);
        onChanged();
      } else {
        unionDataListBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionFightSupremacyData unionDataList = 2;</code>
     */
    public Builder addUnionDataList(
        xddq.pb.UnionFightSupremacyData.Builder builderForValue) {
      if (unionDataListBuilder_ == null) {
        ensureUnionDataListIsMutable();
        unionDataList_.add(builderForValue.build());
        onChanged();
      } else {
        unionDataListBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionFightSupremacyData unionDataList = 2;</code>
     */
    public Builder addUnionDataList(
        int index, xddq.pb.UnionFightSupremacyData.Builder builderForValue) {
      if (unionDataListBuilder_ == null) {
        ensureUnionDataListIsMutable();
        unionDataList_.add(index, builderForValue.build());
        onChanged();
      } else {
        unionDataListBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionFightSupremacyData unionDataList = 2;</code>
     */
    public Builder addAllUnionDataList(
        java.lang.Iterable<? extends xddq.pb.UnionFightSupremacyData> values) {
      if (unionDataListBuilder_ == null) {
        ensureUnionDataListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, unionDataList_);
        onChanged();
      } else {
        unionDataListBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionFightSupremacyData unionDataList = 2;</code>
     */
    public Builder clearUnionDataList() {
      if (unionDataListBuilder_ == null) {
        unionDataList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        unionDataListBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionFightSupremacyData unionDataList = 2;</code>
     */
    public Builder removeUnionDataList(int index) {
      if (unionDataListBuilder_ == null) {
        ensureUnionDataListIsMutable();
        unionDataList_.remove(index);
        onChanged();
      } else {
        unionDataListBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionFightSupremacyData unionDataList = 2;</code>
     */
    public xddq.pb.UnionFightSupremacyData.Builder getUnionDataListBuilder(
        int index) {
      return internalGetUnionDataListFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.UnionFightSupremacyData unionDataList = 2;</code>
     */
    public xddq.pb.UnionFightSupremacyDataOrBuilder getUnionDataListOrBuilder(
        int index) {
      if (unionDataListBuilder_ == null) {
        return unionDataList_.get(index);  } else {
        return unionDataListBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.UnionFightSupremacyData unionDataList = 2;</code>
     */
    public java.util.List<? extends xddq.pb.UnionFightSupremacyDataOrBuilder> 
         getUnionDataListOrBuilderList() {
      if (unionDataListBuilder_ != null) {
        return unionDataListBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(unionDataList_);
      }
    }
    /**
     * <code>repeated .xddq.pb.UnionFightSupremacyData unionDataList = 2;</code>
     */
    public xddq.pb.UnionFightSupremacyData.Builder addUnionDataListBuilder() {
      return internalGetUnionDataListFieldBuilder().addBuilder(
          xddq.pb.UnionFightSupremacyData.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.UnionFightSupremacyData unionDataList = 2;</code>
     */
    public xddq.pb.UnionFightSupremacyData.Builder addUnionDataListBuilder(
        int index) {
      return internalGetUnionDataListFieldBuilder().addBuilder(
          index, xddq.pb.UnionFightSupremacyData.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.UnionFightSupremacyData unionDataList = 2;</code>
     */
    public java.util.List<xddq.pb.UnionFightSupremacyData.Builder> 
         getUnionDataListBuilderList() {
      return internalGetUnionDataListFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.UnionFightSupremacyData, xddq.pb.UnionFightSupremacyData.Builder, xddq.pb.UnionFightSupremacyDataOrBuilder> 
        internalGetUnionDataListFieldBuilder() {
      if (unionDataListBuilder_ == null) {
        unionDataListBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.UnionFightSupremacyData, xddq.pb.UnionFightSupremacyData.Builder, xddq.pb.UnionFightSupremacyDataOrBuilder>(
                unionDataList_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        unionDataList_ = null;
      }
      return unionDataListBuilder_;
    }

    private xddq.pb.UnionFightSupremacyData myUnionData_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.UnionFightSupremacyData, xddq.pb.UnionFightSupremacyData.Builder, xddq.pb.UnionFightSupremacyDataOrBuilder> myUnionDataBuilder_;
    /**
     * <code>optional .xddq.pb.UnionFightSupremacyData myUnionData = 3;</code>
     * @return Whether the myUnionData field is set.
     */
    public boolean hasMyUnionData() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional .xddq.pb.UnionFightSupremacyData myUnionData = 3;</code>
     * @return The myUnionData.
     */
    public xddq.pb.UnionFightSupremacyData getMyUnionData() {
      if (myUnionDataBuilder_ == null) {
        return myUnionData_ == null ? xddq.pb.UnionFightSupremacyData.getDefaultInstance() : myUnionData_;
      } else {
        return myUnionDataBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.UnionFightSupremacyData myUnionData = 3;</code>
     */
    public Builder setMyUnionData(xddq.pb.UnionFightSupremacyData value) {
      if (myUnionDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        myUnionData_ = value;
      } else {
        myUnionDataBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.UnionFightSupremacyData myUnionData = 3;</code>
     */
    public Builder setMyUnionData(
        xddq.pb.UnionFightSupremacyData.Builder builderForValue) {
      if (myUnionDataBuilder_ == null) {
        myUnionData_ = builderForValue.build();
      } else {
        myUnionDataBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.UnionFightSupremacyData myUnionData = 3;</code>
     */
    public Builder mergeMyUnionData(xddq.pb.UnionFightSupremacyData value) {
      if (myUnionDataBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0) &&
          myUnionData_ != null &&
          myUnionData_ != xddq.pb.UnionFightSupremacyData.getDefaultInstance()) {
          getMyUnionDataBuilder().mergeFrom(value);
        } else {
          myUnionData_ = value;
        }
      } else {
        myUnionDataBuilder_.mergeFrom(value);
      }
      if (myUnionData_ != null) {
        bitField0_ |= 0x00000004;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.UnionFightSupremacyData myUnionData = 3;</code>
     */
    public Builder clearMyUnionData() {
      bitField0_ = (bitField0_ & ~0x00000004);
      myUnionData_ = null;
      if (myUnionDataBuilder_ != null) {
        myUnionDataBuilder_.dispose();
        myUnionDataBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.UnionFightSupremacyData myUnionData = 3;</code>
     */
    public xddq.pb.UnionFightSupremacyData.Builder getMyUnionDataBuilder() {
      bitField0_ |= 0x00000004;
      onChanged();
      return internalGetMyUnionDataFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.UnionFightSupremacyData myUnionData = 3;</code>
     */
    public xddq.pb.UnionFightSupremacyDataOrBuilder getMyUnionDataOrBuilder() {
      if (myUnionDataBuilder_ != null) {
        return myUnionDataBuilder_.getMessageOrBuilder();
      } else {
        return myUnionData_ == null ?
            xddq.pb.UnionFightSupremacyData.getDefaultInstance() : myUnionData_;
      }
    }
    /**
     * <code>optional .xddq.pb.UnionFightSupremacyData myUnionData = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.UnionFightSupremacyData, xddq.pb.UnionFightSupremacyData.Builder, xddq.pb.UnionFightSupremacyDataOrBuilder> 
        internalGetMyUnionDataFieldBuilder() {
      if (myUnionDataBuilder_ == null) {
        myUnionDataBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.UnionFightSupremacyData, xddq.pb.UnionFightSupremacyData.Builder, xddq.pb.UnionFightSupremacyDataOrBuilder>(
                getMyUnionData(),
                getParentForChildren(),
                isClean());
        myUnionData_ = null;
      }
      return myUnionDataBuilder_;
    }

    private boolean worship_ ;
    /**
     * <code>optional bool worship = 4;</code>
     * @return Whether the worship field is set.
     */
    @java.lang.Override
    public boolean hasWorship() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional bool worship = 4;</code>
     * @return The worship.
     */
    @java.lang.Override
    public boolean getWorship() {
      return worship_;
    }
    /**
     * <code>optional bool worship = 4;</code>
     * @param value The worship to set.
     * @return This builder for chaining.
     */
    public Builder setWorship(boolean value) {

      worship_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional bool worship = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearWorship() {
      bitField0_ = (bitField0_ & ~0x00000008);
      worship_ = false;
      onChanged();
      return this;
    }

    private int firstNum_ ;
    /**
     * <code>optional int32 firstNum = 5;</code>
     * @return Whether the firstNum field is set.
     */
    @java.lang.Override
    public boolean hasFirstNum() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional int32 firstNum = 5;</code>
     * @return The firstNum.
     */
    @java.lang.Override
    public int getFirstNum() {
      return firstNum_;
    }
    /**
     * <code>optional int32 firstNum = 5;</code>
     * @param value The firstNum to set.
     * @return This builder for chaining.
     */
    public Builder setFirstNum(int value) {

      firstNum_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 firstNum = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearFirstNum() {
      bitField0_ = (bitField0_ & ~0x00000010);
      firstNum_ = 0;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.UnionFightSupremacyListRsp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.UnionFightSupremacyListRsp)
  private static final xddq.pb.UnionFightSupremacyListRsp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.UnionFightSupremacyListRsp();
  }

  public static xddq.pb.UnionFightSupremacyListRsp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<UnionFightSupremacyListRsp>
      PARSER = new com.google.protobuf.AbstractParser<UnionFightSupremacyListRsp>() {
    @java.lang.Override
    public UnionFightSupremacyListRsp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<UnionFightSupremacyListRsp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<UnionFightSupremacyListRsp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.UnionFightSupremacyListRsp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

