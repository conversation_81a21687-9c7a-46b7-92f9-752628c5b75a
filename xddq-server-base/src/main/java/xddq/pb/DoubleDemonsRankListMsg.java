// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.DoubleDemonsRankListMsg}
 */
public final class DoubleDemonsRankListMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.DoubleDemonsRankListMsg)
    DoubleDemonsRankListMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      DoubleDemonsRankListMsg.class.getName());
  }
  // Use DoubleDemonsRankListMsg.newBuilder() to construct.
  private DoubleDemonsRankListMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private DoubleDemonsRankListMsg() {
    rankList_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_DoubleDemonsRankListMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_DoubleDemonsRankListMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.DoubleDemonsRankListMsg.class, xddq.pb.DoubleDemonsRankListMsg.Builder.class);
  }

  public static final int RANKLIST_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.DoubleDemonsRankData> rankList_;
  /**
   * <code>repeated .xddq.pb.DoubleDemonsRankData rankList = 1;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.DoubleDemonsRankData> getRankListList() {
    return rankList_;
  }
  /**
   * <code>repeated .xddq.pb.DoubleDemonsRankData rankList = 1;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.DoubleDemonsRankDataOrBuilder> 
      getRankListOrBuilderList() {
    return rankList_;
  }
  /**
   * <code>repeated .xddq.pb.DoubleDemonsRankData rankList = 1;</code>
   */
  @java.lang.Override
  public int getRankListCount() {
    return rankList_.size();
  }
  /**
   * <code>repeated .xddq.pb.DoubleDemonsRankData rankList = 1;</code>
   */
  @java.lang.Override
  public xddq.pb.DoubleDemonsRankData getRankList(int index) {
    return rankList_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.DoubleDemonsRankData rankList = 1;</code>
   */
  @java.lang.Override
  public xddq.pb.DoubleDemonsRankDataOrBuilder getRankListOrBuilder(
      int index) {
    return rankList_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    for (int i = 0; i < rankList_.size(); i++) {
      output.writeMessage(1, rankList_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    for (int i = 0; i < rankList_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, rankList_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.DoubleDemonsRankListMsg)) {
      return super.equals(obj);
    }
    xddq.pb.DoubleDemonsRankListMsg other = (xddq.pb.DoubleDemonsRankListMsg) obj;

    if (!getRankListList()
        .equals(other.getRankListList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (getRankListCount() > 0) {
      hash = (37 * hash) + RANKLIST_FIELD_NUMBER;
      hash = (53 * hash) + getRankListList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.DoubleDemonsRankListMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.DoubleDemonsRankListMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.DoubleDemonsRankListMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.DoubleDemonsRankListMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.DoubleDemonsRankListMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.DoubleDemonsRankListMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.DoubleDemonsRankListMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.DoubleDemonsRankListMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.DoubleDemonsRankListMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.DoubleDemonsRankListMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.DoubleDemonsRankListMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.DoubleDemonsRankListMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.DoubleDemonsRankListMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.DoubleDemonsRankListMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.DoubleDemonsRankListMsg)
      xddq.pb.DoubleDemonsRankListMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_DoubleDemonsRankListMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_DoubleDemonsRankListMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.DoubleDemonsRankListMsg.class, xddq.pb.DoubleDemonsRankListMsg.Builder.class);
    }

    // Construct using xddq.pb.DoubleDemonsRankListMsg.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      if (rankListBuilder_ == null) {
        rankList_ = java.util.Collections.emptyList();
      } else {
        rankList_ = null;
        rankListBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000001);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_DoubleDemonsRankListMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.DoubleDemonsRankListMsg getDefaultInstanceForType() {
      return xddq.pb.DoubleDemonsRankListMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.DoubleDemonsRankListMsg build() {
      xddq.pb.DoubleDemonsRankListMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.DoubleDemonsRankListMsg buildPartial() {
      xddq.pb.DoubleDemonsRankListMsg result = new xddq.pb.DoubleDemonsRankListMsg(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.DoubleDemonsRankListMsg result) {
      if (rankListBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          rankList_ = java.util.Collections.unmodifiableList(rankList_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.rankList_ = rankList_;
      } else {
        result.rankList_ = rankListBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.DoubleDemonsRankListMsg result) {
      int from_bitField0_ = bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.DoubleDemonsRankListMsg) {
        return mergeFrom((xddq.pb.DoubleDemonsRankListMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.DoubleDemonsRankListMsg other) {
      if (other == xddq.pb.DoubleDemonsRankListMsg.getDefaultInstance()) return this;
      if (rankListBuilder_ == null) {
        if (!other.rankList_.isEmpty()) {
          if (rankList_.isEmpty()) {
            rankList_ = other.rankList_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureRankListIsMutable();
            rankList_.addAll(other.rankList_);
          }
          onChanged();
        }
      } else {
        if (!other.rankList_.isEmpty()) {
          if (rankListBuilder_.isEmpty()) {
            rankListBuilder_.dispose();
            rankListBuilder_ = null;
            rankList_ = other.rankList_;
            bitField0_ = (bitField0_ & ~0x00000001);
            rankListBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetRankListFieldBuilder() : null;
          } else {
            rankListBuilder_.addAllMessages(other.rankList_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              xddq.pb.DoubleDemonsRankData m =
                  input.readMessage(
                      xddq.pb.DoubleDemonsRankData.parser(),
                      extensionRegistry);
              if (rankListBuilder_ == null) {
                ensureRankListIsMutable();
                rankList_.add(m);
              } else {
                rankListBuilder_.addMessage(m);
              }
              break;
            } // case 10
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.util.List<xddq.pb.DoubleDemonsRankData> rankList_ =
      java.util.Collections.emptyList();
    private void ensureRankListIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        rankList_ = new java.util.ArrayList<xddq.pb.DoubleDemonsRankData>(rankList_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.DoubleDemonsRankData, xddq.pb.DoubleDemonsRankData.Builder, xddq.pb.DoubleDemonsRankDataOrBuilder> rankListBuilder_;

    /**
     * <code>repeated .xddq.pb.DoubleDemonsRankData rankList = 1;</code>
     */
    public java.util.List<xddq.pb.DoubleDemonsRankData> getRankListList() {
      if (rankListBuilder_ == null) {
        return java.util.Collections.unmodifiableList(rankList_);
      } else {
        return rankListBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.DoubleDemonsRankData rankList = 1;</code>
     */
    public int getRankListCount() {
      if (rankListBuilder_ == null) {
        return rankList_.size();
      } else {
        return rankListBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.DoubleDemonsRankData rankList = 1;</code>
     */
    public xddq.pb.DoubleDemonsRankData getRankList(int index) {
      if (rankListBuilder_ == null) {
        return rankList_.get(index);
      } else {
        return rankListBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.DoubleDemonsRankData rankList = 1;</code>
     */
    public Builder setRankList(
        int index, xddq.pb.DoubleDemonsRankData value) {
      if (rankListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRankListIsMutable();
        rankList_.set(index, value);
        onChanged();
      } else {
        rankListBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DoubleDemonsRankData rankList = 1;</code>
     */
    public Builder setRankList(
        int index, xddq.pb.DoubleDemonsRankData.Builder builderForValue) {
      if (rankListBuilder_ == null) {
        ensureRankListIsMutable();
        rankList_.set(index, builderForValue.build());
        onChanged();
      } else {
        rankListBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DoubleDemonsRankData rankList = 1;</code>
     */
    public Builder addRankList(xddq.pb.DoubleDemonsRankData value) {
      if (rankListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRankListIsMutable();
        rankList_.add(value);
        onChanged();
      } else {
        rankListBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DoubleDemonsRankData rankList = 1;</code>
     */
    public Builder addRankList(
        int index, xddq.pb.DoubleDemonsRankData value) {
      if (rankListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRankListIsMutable();
        rankList_.add(index, value);
        onChanged();
      } else {
        rankListBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DoubleDemonsRankData rankList = 1;</code>
     */
    public Builder addRankList(
        xddq.pb.DoubleDemonsRankData.Builder builderForValue) {
      if (rankListBuilder_ == null) {
        ensureRankListIsMutable();
        rankList_.add(builderForValue.build());
        onChanged();
      } else {
        rankListBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DoubleDemonsRankData rankList = 1;</code>
     */
    public Builder addRankList(
        int index, xddq.pb.DoubleDemonsRankData.Builder builderForValue) {
      if (rankListBuilder_ == null) {
        ensureRankListIsMutable();
        rankList_.add(index, builderForValue.build());
        onChanged();
      } else {
        rankListBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DoubleDemonsRankData rankList = 1;</code>
     */
    public Builder addAllRankList(
        java.lang.Iterable<? extends xddq.pb.DoubleDemonsRankData> values) {
      if (rankListBuilder_ == null) {
        ensureRankListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, rankList_);
        onChanged();
      } else {
        rankListBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DoubleDemonsRankData rankList = 1;</code>
     */
    public Builder clearRankList() {
      if (rankListBuilder_ == null) {
        rankList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        rankListBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DoubleDemonsRankData rankList = 1;</code>
     */
    public Builder removeRankList(int index) {
      if (rankListBuilder_ == null) {
        ensureRankListIsMutable();
        rankList_.remove(index);
        onChanged();
      } else {
        rankListBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DoubleDemonsRankData rankList = 1;</code>
     */
    public xddq.pb.DoubleDemonsRankData.Builder getRankListBuilder(
        int index) {
      return internalGetRankListFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.DoubleDemonsRankData rankList = 1;</code>
     */
    public xddq.pb.DoubleDemonsRankDataOrBuilder getRankListOrBuilder(
        int index) {
      if (rankListBuilder_ == null) {
        return rankList_.get(index);  } else {
        return rankListBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.DoubleDemonsRankData rankList = 1;</code>
     */
    public java.util.List<? extends xddq.pb.DoubleDemonsRankDataOrBuilder> 
         getRankListOrBuilderList() {
      if (rankListBuilder_ != null) {
        return rankListBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(rankList_);
      }
    }
    /**
     * <code>repeated .xddq.pb.DoubleDemonsRankData rankList = 1;</code>
     */
    public xddq.pb.DoubleDemonsRankData.Builder addRankListBuilder() {
      return internalGetRankListFieldBuilder().addBuilder(
          xddq.pb.DoubleDemonsRankData.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.DoubleDemonsRankData rankList = 1;</code>
     */
    public xddq.pb.DoubleDemonsRankData.Builder addRankListBuilder(
        int index) {
      return internalGetRankListFieldBuilder().addBuilder(
          index, xddq.pb.DoubleDemonsRankData.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.DoubleDemonsRankData rankList = 1;</code>
     */
    public java.util.List<xddq.pb.DoubleDemonsRankData.Builder> 
         getRankListBuilderList() {
      return internalGetRankListFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.DoubleDemonsRankData, xddq.pb.DoubleDemonsRankData.Builder, xddq.pb.DoubleDemonsRankDataOrBuilder> 
        internalGetRankListFieldBuilder() {
      if (rankListBuilder_ == null) {
        rankListBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.DoubleDemonsRankData, xddq.pb.DoubleDemonsRankData.Builder, xddq.pb.DoubleDemonsRankDataOrBuilder>(
                rankList_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        rankList_ = null;
      }
      return rankListBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.DoubleDemonsRankListMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.DoubleDemonsRankListMsg)
  private static final xddq.pb.DoubleDemonsRankListMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.DoubleDemonsRankListMsg();
  }

  public static xddq.pb.DoubleDemonsRankListMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<DoubleDemonsRankListMsg>
      PARSER = new com.google.protobuf.AbstractParser<DoubleDemonsRankListMsg>() {
    @java.lang.Override
    public DoubleDemonsRankListMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<DoubleDemonsRankListMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<DoubleDemonsRankListMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.DoubleDemonsRankListMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

