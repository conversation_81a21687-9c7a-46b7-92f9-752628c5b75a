// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.PeakFightWorshipResp}
 */
public final class PeakFightWorshipResp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.PeakFightWorshipResp)
    PeakFightWorshipRespOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      PeakFightWorshipResp.class.getName());
  }
  // Use PeakFightWorshipResp.newBuilder() to construct.
  private PeakFightWorshipResp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private PeakFightWorshipResp() {
    reward_ = "";
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_PeakFightWorshipResp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_PeakFightWorshipResp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.PeakFightWorshipResp.class, xddq.pb.PeakFightWorshipResp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int REWARD_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private volatile java.lang.Object reward_ = "";
  /**
   * <code>optional string reward = 2;</code>
   * @return Whether the reward field is set.
   */
  @java.lang.Override
  public boolean hasReward() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional string reward = 2;</code>
   * @return The reward.
   */
  @java.lang.Override
  public java.lang.String getReward() {
    java.lang.Object ref = reward_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        reward_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string reward = 2;</code>
   * @return The bytes for reward.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getRewardBytes() {
    java.lang.Object ref = reward_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      reward_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int WORSHIPPLAYER_FIELD_NUMBER = 3;
  private xddq.pb.PalacePlayerShowMsg worshipPlayer_;
  /**
   * <code>optional .xddq.pb.PalacePlayerShowMsg worshipPlayer = 3;</code>
   * @return Whether the worshipPlayer field is set.
   */
  @java.lang.Override
  public boolean hasWorshipPlayer() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional .xddq.pb.PalacePlayerShowMsg worshipPlayer = 3;</code>
   * @return The worshipPlayer.
   */
  @java.lang.Override
  public xddq.pb.PalacePlayerShowMsg getWorshipPlayer() {
    return worshipPlayer_ == null ? xddq.pb.PalacePlayerShowMsg.getDefaultInstance() : worshipPlayer_;
  }
  /**
   * <code>optional .xddq.pb.PalacePlayerShowMsg worshipPlayer = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.PalacePlayerShowMsgOrBuilder getWorshipPlayerOrBuilder() {
    return worshipPlayer_ == null ? xddq.pb.PalacePlayerShowMsg.getDefaultInstance() : worshipPlayer_;
  }

  public static final int ACTIVITYID_FIELD_NUMBER = 4;
  private int activityId_ = 0;
  /**
   * <code>optional int32 activityId = 4;</code>
   * @return Whether the activityId field is set.
   */
  @java.lang.Override
  public boolean hasActivityId() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional int32 activityId = 4;</code>
   * @return The activityId.
   */
  @java.lang.Override
  public int getActivityId() {
    return activityId_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (hasWorshipPlayer()) {
      if (!getWorshipPlayer().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 2, reward_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeMessage(3, getWorshipPlayer());
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt32(4, activityId_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(2, reward_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, getWorshipPlayer());
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(4, activityId_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.PeakFightWorshipResp)) {
      return super.equals(obj);
    }
    xddq.pb.PeakFightWorshipResp other = (xddq.pb.PeakFightWorshipResp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (hasReward() != other.hasReward()) return false;
    if (hasReward()) {
      if (!getReward()
          .equals(other.getReward())) return false;
    }
    if (hasWorshipPlayer() != other.hasWorshipPlayer()) return false;
    if (hasWorshipPlayer()) {
      if (!getWorshipPlayer()
          .equals(other.getWorshipPlayer())) return false;
    }
    if (hasActivityId() != other.hasActivityId()) return false;
    if (hasActivityId()) {
      if (getActivityId()
          != other.getActivityId()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (hasReward()) {
      hash = (37 * hash) + REWARD_FIELD_NUMBER;
      hash = (53 * hash) + getReward().hashCode();
    }
    if (hasWorshipPlayer()) {
      hash = (37 * hash) + WORSHIPPLAYER_FIELD_NUMBER;
      hash = (53 * hash) + getWorshipPlayer().hashCode();
    }
    if (hasActivityId()) {
      hash = (37 * hash) + ACTIVITYID_FIELD_NUMBER;
      hash = (53 * hash) + getActivityId();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.PeakFightWorshipResp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PeakFightWorshipResp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PeakFightWorshipResp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PeakFightWorshipResp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PeakFightWorshipResp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PeakFightWorshipResp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PeakFightWorshipResp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.PeakFightWorshipResp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.PeakFightWorshipResp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.PeakFightWorshipResp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.PeakFightWorshipResp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.PeakFightWorshipResp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.PeakFightWorshipResp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.PeakFightWorshipResp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.PeakFightWorshipResp)
      xddq.pb.PeakFightWorshipRespOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PeakFightWorshipResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PeakFightWorshipResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.PeakFightWorshipResp.class, xddq.pb.PeakFightWorshipResp.Builder.class);
    }

    // Construct using xddq.pb.PeakFightWorshipResp.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetWorshipPlayerFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      reward_ = "";
      worshipPlayer_ = null;
      if (worshipPlayerBuilder_ != null) {
        worshipPlayerBuilder_.dispose();
        worshipPlayerBuilder_ = null;
      }
      activityId_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PeakFightWorshipResp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.PeakFightWorshipResp getDefaultInstanceForType() {
      return xddq.pb.PeakFightWorshipResp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.PeakFightWorshipResp build() {
      xddq.pb.PeakFightWorshipResp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.PeakFightWorshipResp buildPartial() {
      xddq.pb.PeakFightWorshipResp result = new xddq.pb.PeakFightWorshipResp(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.PeakFightWorshipResp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.reward_ = reward_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.worshipPlayer_ = worshipPlayerBuilder_ == null
            ? worshipPlayer_
            : worshipPlayerBuilder_.build();
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.activityId_ = activityId_;
        to_bitField0_ |= 0x00000008;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.PeakFightWorshipResp) {
        return mergeFrom((xddq.pb.PeakFightWorshipResp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.PeakFightWorshipResp other) {
      if (other == xddq.pb.PeakFightWorshipResp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (other.hasReward()) {
        reward_ = other.reward_;
        bitField0_ |= 0x00000002;
        onChanged();
      }
      if (other.hasWorshipPlayer()) {
        mergeWorshipPlayer(other.getWorshipPlayer());
      }
      if (other.hasActivityId()) {
        setActivityId(other.getActivityId());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      if (hasWorshipPlayer()) {
        if (!getWorshipPlayer().isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              reward_ = input.readBytes();
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 26: {
              input.readMessage(
                  internalGetWorshipPlayerFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 32: {
              activityId_ = input.readInt32();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object reward_ = "";
    /**
     * <code>optional string reward = 2;</code>
     * @return Whether the reward field is set.
     */
    public boolean hasReward() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional string reward = 2;</code>
     * @return The reward.
     */
    public java.lang.String getReward() {
      java.lang.Object ref = reward_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          reward_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string reward = 2;</code>
     * @return The bytes for reward.
     */
    public com.google.protobuf.ByteString
        getRewardBytes() {
      java.lang.Object ref = reward_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        reward_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string reward = 2;</code>
     * @param value The reward to set.
     * @return This builder for chaining.
     */
    public Builder setReward(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      reward_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional string reward = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearReward() {
      reward_ = getDefaultInstance().getReward();
      bitField0_ = (bitField0_ & ~0x00000002);
      onChanged();
      return this;
    }
    /**
     * <code>optional string reward = 2;</code>
     * @param value The bytes for reward to set.
     * @return This builder for chaining.
     */
    public Builder setRewardBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      reward_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }

    private xddq.pb.PalacePlayerShowMsg worshipPlayer_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PalacePlayerShowMsg, xddq.pb.PalacePlayerShowMsg.Builder, xddq.pb.PalacePlayerShowMsgOrBuilder> worshipPlayerBuilder_;
    /**
     * <code>optional .xddq.pb.PalacePlayerShowMsg worshipPlayer = 3;</code>
     * @return Whether the worshipPlayer field is set.
     */
    public boolean hasWorshipPlayer() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional .xddq.pb.PalacePlayerShowMsg worshipPlayer = 3;</code>
     * @return The worshipPlayer.
     */
    public xddq.pb.PalacePlayerShowMsg getWorshipPlayer() {
      if (worshipPlayerBuilder_ == null) {
        return worshipPlayer_ == null ? xddq.pb.PalacePlayerShowMsg.getDefaultInstance() : worshipPlayer_;
      } else {
        return worshipPlayerBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.PalacePlayerShowMsg worshipPlayer = 3;</code>
     */
    public Builder setWorshipPlayer(xddq.pb.PalacePlayerShowMsg value) {
      if (worshipPlayerBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        worshipPlayer_ = value;
      } else {
        worshipPlayerBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PalacePlayerShowMsg worshipPlayer = 3;</code>
     */
    public Builder setWorshipPlayer(
        xddq.pb.PalacePlayerShowMsg.Builder builderForValue) {
      if (worshipPlayerBuilder_ == null) {
        worshipPlayer_ = builderForValue.build();
      } else {
        worshipPlayerBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PalacePlayerShowMsg worshipPlayer = 3;</code>
     */
    public Builder mergeWorshipPlayer(xddq.pb.PalacePlayerShowMsg value) {
      if (worshipPlayerBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0) &&
          worshipPlayer_ != null &&
          worshipPlayer_ != xddq.pb.PalacePlayerShowMsg.getDefaultInstance()) {
          getWorshipPlayerBuilder().mergeFrom(value);
        } else {
          worshipPlayer_ = value;
        }
      } else {
        worshipPlayerBuilder_.mergeFrom(value);
      }
      if (worshipPlayer_ != null) {
        bitField0_ |= 0x00000004;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.PalacePlayerShowMsg worshipPlayer = 3;</code>
     */
    public Builder clearWorshipPlayer() {
      bitField0_ = (bitField0_ & ~0x00000004);
      worshipPlayer_ = null;
      if (worshipPlayerBuilder_ != null) {
        worshipPlayerBuilder_.dispose();
        worshipPlayerBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PalacePlayerShowMsg worshipPlayer = 3;</code>
     */
    public xddq.pb.PalacePlayerShowMsg.Builder getWorshipPlayerBuilder() {
      bitField0_ |= 0x00000004;
      onChanged();
      return internalGetWorshipPlayerFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.PalacePlayerShowMsg worshipPlayer = 3;</code>
     */
    public xddq.pb.PalacePlayerShowMsgOrBuilder getWorshipPlayerOrBuilder() {
      if (worshipPlayerBuilder_ != null) {
        return worshipPlayerBuilder_.getMessageOrBuilder();
      } else {
        return worshipPlayer_ == null ?
            xddq.pb.PalacePlayerShowMsg.getDefaultInstance() : worshipPlayer_;
      }
    }
    /**
     * <code>optional .xddq.pb.PalacePlayerShowMsg worshipPlayer = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PalacePlayerShowMsg, xddq.pb.PalacePlayerShowMsg.Builder, xddq.pb.PalacePlayerShowMsgOrBuilder> 
        internalGetWorshipPlayerFieldBuilder() {
      if (worshipPlayerBuilder_ == null) {
        worshipPlayerBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.PalacePlayerShowMsg, xddq.pb.PalacePlayerShowMsg.Builder, xddq.pb.PalacePlayerShowMsgOrBuilder>(
                getWorshipPlayer(),
                getParentForChildren(),
                isClean());
        worshipPlayer_ = null;
      }
      return worshipPlayerBuilder_;
    }

    private int activityId_ ;
    /**
     * <code>optional int32 activityId = 4;</code>
     * @return Whether the activityId field is set.
     */
    @java.lang.Override
    public boolean hasActivityId() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int32 activityId = 4;</code>
     * @return The activityId.
     */
    @java.lang.Override
    public int getActivityId() {
      return activityId_;
    }
    /**
     * <code>optional int32 activityId = 4;</code>
     * @param value The activityId to set.
     * @return This builder for chaining.
     */
    public Builder setActivityId(int value) {

      activityId_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 activityId = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearActivityId() {
      bitField0_ = (bitField0_ & ~0x00000008);
      activityId_ = 0;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.PeakFightWorshipResp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.PeakFightWorshipResp)
  private static final xddq.pb.PeakFightWorshipResp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.PeakFightWorshipResp();
  }

  public static xddq.pb.PeakFightWorshipResp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<PeakFightWorshipResp>
      PARSER = new com.google.protobuf.AbstractParser<PeakFightWorshipResp>() {
    @java.lang.Override
    public PeakFightWorshipResp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<PeakFightWorshipResp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<PeakFightWorshipResp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.PeakFightWorshipResp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

