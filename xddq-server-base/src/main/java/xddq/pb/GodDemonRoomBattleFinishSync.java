// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.GodDemonRoomBattleFinishSync}
 */
public final class GodDemonRoomBattleFinishSync extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.GodDemonRoomBattleFinishSync)
    GodDemonRoomBattleFinishSyncOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      GodDemonRoomBattleFinishSync.class.getName());
  }
  // Use GodDemonRoomBattleFinishSync.newBuilder() to construct.
  private GodDemonRoomBattleFinishSync(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private GodDemonRoomBattleFinishSync() {
    msg_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_GodDemonRoomBattleFinishSync_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_GodDemonRoomBattleFinishSync_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.GodDemonRoomBattleFinishSync.class, xddq.pb.GodDemonRoomBattleFinishSync.Builder.class);
  }

  private int bitField0_;
  public static final int ACTIVITYID_FIELD_NUMBER = 1;
  private int activityId_ = 0;
  /**
   * <code>optional int32 activityId = 1;</code>
   * @return Whether the activityId field is set.
   */
  @java.lang.Override
  public boolean hasActivityId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int32 activityId = 1;</code>
   * @return The activityId.
   */
  @java.lang.Override
  public int getActivityId() {
    return activityId_;
  }

  public static final int ROOMID_FIELD_NUMBER = 2;
  private long roomId_ = 0L;
  /**
   * <code>optional int64 roomId = 2;</code>
   * @return Whether the roomId field is set.
   */
  @java.lang.Override
  public boolean hasRoomId() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int64 roomId = 2;</code>
   * @return The roomId.
   */
  @java.lang.Override
  public long getRoomId() {
    return roomId_;
  }

  public static final int TIMEID_FIELD_NUMBER = 3;
  private int timeId_ = 0;
  /**
   * <code>optional int32 timeId = 3;</code>
   * @return Whether the timeId field is set.
   */
  @java.lang.Override
  public boolean hasTimeId() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional int32 timeId = 3;</code>
   * @return The timeId.
   */
  @java.lang.Override
  public int getTimeId() {
    return timeId_;
  }

  public static final int MSG_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.GodDemonRoomBattleFinishRoundMsg> msg_;
  /**
   * <code>repeated .xddq.pb.GodDemonRoomBattleFinishRoundMsg msg = 4;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.GodDemonRoomBattleFinishRoundMsg> getMsgList() {
    return msg_;
  }
  /**
   * <code>repeated .xddq.pb.GodDemonRoomBattleFinishRoundMsg msg = 4;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.GodDemonRoomBattleFinishRoundMsgOrBuilder> 
      getMsgOrBuilderList() {
    return msg_;
  }
  /**
   * <code>repeated .xddq.pb.GodDemonRoomBattleFinishRoundMsg msg = 4;</code>
   */
  @java.lang.Override
  public int getMsgCount() {
    return msg_.size();
  }
  /**
   * <code>repeated .xddq.pb.GodDemonRoomBattleFinishRoundMsg msg = 4;</code>
   */
  @java.lang.Override
  public xddq.pb.GodDemonRoomBattleFinishRoundMsg getMsg(int index) {
    return msg_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.GodDemonRoomBattleFinishRoundMsg msg = 4;</code>
   */
  @java.lang.Override
  public xddq.pb.GodDemonRoomBattleFinishRoundMsgOrBuilder getMsgOrBuilder(
      int index) {
    return msg_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, activityId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt64(2, roomId_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt32(3, timeId_);
    }
    for (int i = 0; i < msg_.size(); i++) {
      output.writeMessage(4, msg_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, activityId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(2, roomId_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(3, timeId_);
    }
    for (int i = 0; i < msg_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(4, msg_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.GodDemonRoomBattleFinishSync)) {
      return super.equals(obj);
    }
    xddq.pb.GodDemonRoomBattleFinishSync other = (xddq.pb.GodDemonRoomBattleFinishSync) obj;

    if (hasActivityId() != other.hasActivityId()) return false;
    if (hasActivityId()) {
      if (getActivityId()
          != other.getActivityId()) return false;
    }
    if (hasRoomId() != other.hasRoomId()) return false;
    if (hasRoomId()) {
      if (getRoomId()
          != other.getRoomId()) return false;
    }
    if (hasTimeId() != other.hasTimeId()) return false;
    if (hasTimeId()) {
      if (getTimeId()
          != other.getTimeId()) return false;
    }
    if (!getMsgList()
        .equals(other.getMsgList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasActivityId()) {
      hash = (37 * hash) + ACTIVITYID_FIELD_NUMBER;
      hash = (53 * hash) + getActivityId();
    }
    if (hasRoomId()) {
      hash = (37 * hash) + ROOMID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getRoomId());
    }
    if (hasTimeId()) {
      hash = (37 * hash) + TIMEID_FIELD_NUMBER;
      hash = (53 * hash) + getTimeId();
    }
    if (getMsgCount() > 0) {
      hash = (37 * hash) + MSG_FIELD_NUMBER;
      hash = (53 * hash) + getMsgList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.GodDemonRoomBattleFinishSync parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GodDemonRoomBattleFinishSync parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GodDemonRoomBattleFinishSync parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GodDemonRoomBattleFinishSync parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GodDemonRoomBattleFinishSync parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GodDemonRoomBattleFinishSync parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GodDemonRoomBattleFinishSync parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.GodDemonRoomBattleFinishSync parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.GodDemonRoomBattleFinishSync parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.GodDemonRoomBattleFinishSync parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.GodDemonRoomBattleFinishSync parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.GodDemonRoomBattleFinishSync parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.GodDemonRoomBattleFinishSync prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.GodDemonRoomBattleFinishSync}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.GodDemonRoomBattleFinishSync)
      xddq.pb.GodDemonRoomBattleFinishSyncOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GodDemonRoomBattleFinishSync_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GodDemonRoomBattleFinishSync_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.GodDemonRoomBattleFinishSync.class, xddq.pb.GodDemonRoomBattleFinishSync.Builder.class);
    }

    // Construct using xddq.pb.GodDemonRoomBattleFinishSync.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      activityId_ = 0;
      roomId_ = 0L;
      timeId_ = 0;
      if (msgBuilder_ == null) {
        msg_ = java.util.Collections.emptyList();
      } else {
        msg_ = null;
        msgBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000008);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GodDemonRoomBattleFinishSync_descriptor;
    }

    @java.lang.Override
    public xddq.pb.GodDemonRoomBattleFinishSync getDefaultInstanceForType() {
      return xddq.pb.GodDemonRoomBattleFinishSync.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.GodDemonRoomBattleFinishSync build() {
      xddq.pb.GodDemonRoomBattleFinishSync result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.GodDemonRoomBattleFinishSync buildPartial() {
      xddq.pb.GodDemonRoomBattleFinishSync result = new xddq.pb.GodDemonRoomBattleFinishSync(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.GodDemonRoomBattleFinishSync result) {
      if (msgBuilder_ == null) {
        if (((bitField0_ & 0x00000008) != 0)) {
          msg_ = java.util.Collections.unmodifiableList(msg_);
          bitField0_ = (bitField0_ & ~0x00000008);
        }
        result.msg_ = msg_;
      } else {
        result.msg_ = msgBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.GodDemonRoomBattleFinishSync result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.activityId_ = activityId_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.roomId_ = roomId_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.timeId_ = timeId_;
        to_bitField0_ |= 0x00000004;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.GodDemonRoomBattleFinishSync) {
        return mergeFrom((xddq.pb.GodDemonRoomBattleFinishSync)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.GodDemonRoomBattleFinishSync other) {
      if (other == xddq.pb.GodDemonRoomBattleFinishSync.getDefaultInstance()) return this;
      if (other.hasActivityId()) {
        setActivityId(other.getActivityId());
      }
      if (other.hasRoomId()) {
        setRoomId(other.getRoomId());
      }
      if (other.hasTimeId()) {
        setTimeId(other.getTimeId());
      }
      if (msgBuilder_ == null) {
        if (!other.msg_.isEmpty()) {
          if (msg_.isEmpty()) {
            msg_ = other.msg_;
            bitField0_ = (bitField0_ & ~0x00000008);
          } else {
            ensureMsgIsMutable();
            msg_.addAll(other.msg_);
          }
          onChanged();
        }
      } else {
        if (!other.msg_.isEmpty()) {
          if (msgBuilder_.isEmpty()) {
            msgBuilder_.dispose();
            msgBuilder_ = null;
            msg_ = other.msg_;
            bitField0_ = (bitField0_ & ~0x00000008);
            msgBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetMsgFieldBuilder() : null;
          } else {
            msgBuilder_.addAllMessages(other.msg_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              activityId_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              roomId_ = input.readInt64();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 24: {
              timeId_ = input.readInt32();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 34: {
              xddq.pb.GodDemonRoomBattleFinishRoundMsg m =
                  input.readMessage(
                      xddq.pb.GodDemonRoomBattleFinishRoundMsg.parser(),
                      extensionRegistry);
              if (msgBuilder_ == null) {
                ensureMsgIsMutable();
                msg_.add(m);
              } else {
                msgBuilder_.addMessage(m);
              }
              break;
            } // case 34
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int activityId_ ;
    /**
     * <code>optional int32 activityId = 1;</code>
     * @return Whether the activityId field is set.
     */
    @java.lang.Override
    public boolean hasActivityId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 activityId = 1;</code>
     * @return The activityId.
     */
    @java.lang.Override
    public int getActivityId() {
      return activityId_;
    }
    /**
     * <code>optional int32 activityId = 1;</code>
     * @param value The activityId to set.
     * @return This builder for chaining.
     */
    public Builder setActivityId(int value) {

      activityId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 activityId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearActivityId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      activityId_ = 0;
      onChanged();
      return this;
    }

    private long roomId_ ;
    /**
     * <code>optional int64 roomId = 2;</code>
     * @return Whether the roomId field is set.
     */
    @java.lang.Override
    public boolean hasRoomId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int64 roomId = 2;</code>
     * @return The roomId.
     */
    @java.lang.Override
    public long getRoomId() {
      return roomId_;
    }
    /**
     * <code>optional int64 roomId = 2;</code>
     * @param value The roomId to set.
     * @return This builder for chaining.
     */
    public Builder setRoomId(long value) {

      roomId_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 roomId = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearRoomId() {
      bitField0_ = (bitField0_ & ~0x00000002);
      roomId_ = 0L;
      onChanged();
      return this;
    }

    private int timeId_ ;
    /**
     * <code>optional int32 timeId = 3;</code>
     * @return Whether the timeId field is set.
     */
    @java.lang.Override
    public boolean hasTimeId() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int32 timeId = 3;</code>
     * @return The timeId.
     */
    @java.lang.Override
    public int getTimeId() {
      return timeId_;
    }
    /**
     * <code>optional int32 timeId = 3;</code>
     * @param value The timeId to set.
     * @return This builder for chaining.
     */
    public Builder setTimeId(int value) {

      timeId_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 timeId = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearTimeId() {
      bitField0_ = (bitField0_ & ~0x00000004);
      timeId_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.GodDemonRoomBattleFinishRoundMsg> msg_ =
      java.util.Collections.emptyList();
    private void ensureMsgIsMutable() {
      if (!((bitField0_ & 0x00000008) != 0)) {
        msg_ = new java.util.ArrayList<xddq.pb.GodDemonRoomBattleFinishRoundMsg>(msg_);
        bitField0_ |= 0x00000008;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.GodDemonRoomBattleFinishRoundMsg, xddq.pb.GodDemonRoomBattleFinishRoundMsg.Builder, xddq.pb.GodDemonRoomBattleFinishRoundMsgOrBuilder> msgBuilder_;

    /**
     * <code>repeated .xddq.pb.GodDemonRoomBattleFinishRoundMsg msg = 4;</code>
     */
    public java.util.List<xddq.pb.GodDemonRoomBattleFinishRoundMsg> getMsgList() {
      if (msgBuilder_ == null) {
        return java.util.Collections.unmodifiableList(msg_);
      } else {
        return msgBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRoomBattleFinishRoundMsg msg = 4;</code>
     */
    public int getMsgCount() {
      if (msgBuilder_ == null) {
        return msg_.size();
      } else {
        return msgBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRoomBattleFinishRoundMsg msg = 4;</code>
     */
    public xddq.pb.GodDemonRoomBattleFinishRoundMsg getMsg(int index) {
      if (msgBuilder_ == null) {
        return msg_.get(index);
      } else {
        return msgBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRoomBattleFinishRoundMsg msg = 4;</code>
     */
    public Builder setMsg(
        int index, xddq.pb.GodDemonRoomBattleFinishRoundMsg value) {
      if (msgBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMsgIsMutable();
        msg_.set(index, value);
        onChanged();
      } else {
        msgBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRoomBattleFinishRoundMsg msg = 4;</code>
     */
    public Builder setMsg(
        int index, xddq.pb.GodDemonRoomBattleFinishRoundMsg.Builder builderForValue) {
      if (msgBuilder_ == null) {
        ensureMsgIsMutable();
        msg_.set(index, builderForValue.build());
        onChanged();
      } else {
        msgBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRoomBattleFinishRoundMsg msg = 4;</code>
     */
    public Builder addMsg(xddq.pb.GodDemonRoomBattleFinishRoundMsg value) {
      if (msgBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMsgIsMutable();
        msg_.add(value);
        onChanged();
      } else {
        msgBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRoomBattleFinishRoundMsg msg = 4;</code>
     */
    public Builder addMsg(
        int index, xddq.pb.GodDemonRoomBattleFinishRoundMsg value) {
      if (msgBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMsgIsMutable();
        msg_.add(index, value);
        onChanged();
      } else {
        msgBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRoomBattleFinishRoundMsg msg = 4;</code>
     */
    public Builder addMsg(
        xddq.pb.GodDemonRoomBattleFinishRoundMsg.Builder builderForValue) {
      if (msgBuilder_ == null) {
        ensureMsgIsMutable();
        msg_.add(builderForValue.build());
        onChanged();
      } else {
        msgBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRoomBattleFinishRoundMsg msg = 4;</code>
     */
    public Builder addMsg(
        int index, xddq.pb.GodDemonRoomBattleFinishRoundMsg.Builder builderForValue) {
      if (msgBuilder_ == null) {
        ensureMsgIsMutable();
        msg_.add(index, builderForValue.build());
        onChanged();
      } else {
        msgBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRoomBattleFinishRoundMsg msg = 4;</code>
     */
    public Builder addAllMsg(
        java.lang.Iterable<? extends xddq.pb.GodDemonRoomBattleFinishRoundMsg> values) {
      if (msgBuilder_ == null) {
        ensureMsgIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, msg_);
        onChanged();
      } else {
        msgBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRoomBattleFinishRoundMsg msg = 4;</code>
     */
    public Builder clearMsg() {
      if (msgBuilder_ == null) {
        msg_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000008);
        onChanged();
      } else {
        msgBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRoomBattleFinishRoundMsg msg = 4;</code>
     */
    public Builder removeMsg(int index) {
      if (msgBuilder_ == null) {
        ensureMsgIsMutable();
        msg_.remove(index);
        onChanged();
      } else {
        msgBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRoomBattleFinishRoundMsg msg = 4;</code>
     */
    public xddq.pb.GodDemonRoomBattleFinishRoundMsg.Builder getMsgBuilder(
        int index) {
      return internalGetMsgFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRoomBattleFinishRoundMsg msg = 4;</code>
     */
    public xddq.pb.GodDemonRoomBattleFinishRoundMsgOrBuilder getMsgOrBuilder(
        int index) {
      if (msgBuilder_ == null) {
        return msg_.get(index);  } else {
        return msgBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRoomBattleFinishRoundMsg msg = 4;</code>
     */
    public java.util.List<? extends xddq.pb.GodDemonRoomBattleFinishRoundMsgOrBuilder> 
         getMsgOrBuilderList() {
      if (msgBuilder_ != null) {
        return msgBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(msg_);
      }
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRoomBattleFinishRoundMsg msg = 4;</code>
     */
    public xddq.pb.GodDemonRoomBattleFinishRoundMsg.Builder addMsgBuilder() {
      return internalGetMsgFieldBuilder().addBuilder(
          xddq.pb.GodDemonRoomBattleFinishRoundMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRoomBattleFinishRoundMsg msg = 4;</code>
     */
    public xddq.pb.GodDemonRoomBattleFinishRoundMsg.Builder addMsgBuilder(
        int index) {
      return internalGetMsgFieldBuilder().addBuilder(
          index, xddq.pb.GodDemonRoomBattleFinishRoundMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRoomBattleFinishRoundMsg msg = 4;</code>
     */
    public java.util.List<xddq.pb.GodDemonRoomBattleFinishRoundMsg.Builder> 
         getMsgBuilderList() {
      return internalGetMsgFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.GodDemonRoomBattleFinishRoundMsg, xddq.pb.GodDemonRoomBattleFinishRoundMsg.Builder, xddq.pb.GodDemonRoomBattleFinishRoundMsgOrBuilder> 
        internalGetMsgFieldBuilder() {
      if (msgBuilder_ == null) {
        msgBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.GodDemonRoomBattleFinishRoundMsg, xddq.pb.GodDemonRoomBattleFinishRoundMsg.Builder, xddq.pb.GodDemonRoomBattleFinishRoundMsgOrBuilder>(
                msg_,
                ((bitField0_ & 0x00000008) != 0),
                getParentForChildren(),
                isClean());
        msg_ = null;
      }
      return msgBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.GodDemonRoomBattleFinishSync)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.GodDemonRoomBattleFinishSync)
  private static final xddq.pb.GodDemonRoomBattleFinishSync DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.GodDemonRoomBattleFinishSync();
  }

  public static xddq.pb.GodDemonRoomBattleFinishSync getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<GodDemonRoomBattleFinishSync>
      PARSER = new com.google.protobuf.AbstractParser<GodDemonRoomBattleFinishSync>() {
    @java.lang.Override
    public GodDemonRoomBattleFinishSync parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<GodDemonRoomBattleFinishSync> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<GodDemonRoomBattleFinishSync> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.GodDemonRoomBattleFinishSync getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

