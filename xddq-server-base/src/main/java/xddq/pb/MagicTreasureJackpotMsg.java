// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.MagicTreasureJackpotMsg}
 */
public final class MagicTreasureJackpotMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.MagicTreasureJackpotMsg)
    MagicTreasureJackpotMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      MagicTreasureJackpotMsg.class.getName());
  }
  // Use MagicTreasureJackpotMsg.newBuilder() to construct.
  private MagicTreasureJackpotMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private MagicTreasureJackpotMsg() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_MagicTreasureJackpotMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_MagicTreasureJackpotMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.MagicTreasureJackpotMsg.class, xddq.pb.MagicTreasureJackpotMsg.Builder.class);
  }

  private int bitField0_;
  public static final int POOLID_FIELD_NUMBER = 1;
  private int poolId_ = 0;
  /**
   * <code>optional int32 poolId = 1;</code>
   * @return Whether the poolId field is set.
   */
  @java.lang.Override
  public boolean hasPoolId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int32 poolId = 1;</code>
   * @return The poolId.
   */
  @java.lang.Override
  public int getPoolId() {
    return poolId_;
  }

  public static final int DRAWTIMES_FIELD_NUMBER = 2;
  private int drawTimes_ = 0;
  /**
   * <code>optional int32 drawTimes = 2;</code>
   * @return Whether the drawTimes field is set.
   */
  @java.lang.Override
  public boolean hasDrawTimes() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int32 drawTimes = 2;</code>
   * @return The drawTimes.
   */
  @java.lang.Override
  public int getDrawTimes() {
    return drawTimes_;
  }

  public static final int FREEDRAWTIMES_FIELD_NUMBER = 3;
  private int freeDrawTimes_ = 0;
  /**
   * <code>optional int32 freeDrawTimes = 3;</code>
   * @return Whether the freeDrawTimes field is set.
   */
  @java.lang.Override
  public boolean hasFreeDrawTimes() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional int32 freeDrawTimes = 3;</code>
   * @return The freeDrawTimes.
   */
  @java.lang.Override
  public int getFreeDrawTimes() {
    return freeDrawTimes_;
  }

  public static final int ADFREETIMES_FIELD_NUMBER = 4;
  private int adFreeTimes_ = 0;
  /**
   * <code>optional int32 adFreeTimes = 4;</code>
   * @return Whether the adFreeTimes field is set.
   */
  @java.lang.Override
  public boolean hasAdFreeTimes() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional int32 adFreeTimes = 4;</code>
   * @return The adFreeTimes.
   */
  @java.lang.Override
  public int getAdFreeTimes() {
    return adFreeTimes_;
  }

  public static final int LASTADTIME_FIELD_NUMBER = 5;
  private long lastAdTime_ = 0L;
  /**
   * <code>optional int64 lastAdTime = 5;</code>
   * @return Whether the lastAdTime field is set.
   */
  @java.lang.Override
  public boolean hasLastAdTime() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>optional int64 lastAdTime = 5;</code>
   * @return The lastAdTime.
   */
  @java.lang.Override
  public long getLastAdTime() {
    return lastAdTime_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, poolId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(2, drawTimes_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt32(3, freeDrawTimes_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt32(4, adFreeTimes_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      output.writeInt64(5, lastAdTime_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, poolId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, drawTimes_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(3, freeDrawTimes_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(4, adFreeTimes_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(5, lastAdTime_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.MagicTreasureJackpotMsg)) {
      return super.equals(obj);
    }
    xddq.pb.MagicTreasureJackpotMsg other = (xddq.pb.MagicTreasureJackpotMsg) obj;

    if (hasPoolId() != other.hasPoolId()) return false;
    if (hasPoolId()) {
      if (getPoolId()
          != other.getPoolId()) return false;
    }
    if (hasDrawTimes() != other.hasDrawTimes()) return false;
    if (hasDrawTimes()) {
      if (getDrawTimes()
          != other.getDrawTimes()) return false;
    }
    if (hasFreeDrawTimes() != other.hasFreeDrawTimes()) return false;
    if (hasFreeDrawTimes()) {
      if (getFreeDrawTimes()
          != other.getFreeDrawTimes()) return false;
    }
    if (hasAdFreeTimes() != other.hasAdFreeTimes()) return false;
    if (hasAdFreeTimes()) {
      if (getAdFreeTimes()
          != other.getAdFreeTimes()) return false;
    }
    if (hasLastAdTime() != other.hasLastAdTime()) return false;
    if (hasLastAdTime()) {
      if (getLastAdTime()
          != other.getLastAdTime()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasPoolId()) {
      hash = (37 * hash) + POOLID_FIELD_NUMBER;
      hash = (53 * hash) + getPoolId();
    }
    if (hasDrawTimes()) {
      hash = (37 * hash) + DRAWTIMES_FIELD_NUMBER;
      hash = (53 * hash) + getDrawTimes();
    }
    if (hasFreeDrawTimes()) {
      hash = (37 * hash) + FREEDRAWTIMES_FIELD_NUMBER;
      hash = (53 * hash) + getFreeDrawTimes();
    }
    if (hasAdFreeTimes()) {
      hash = (37 * hash) + ADFREETIMES_FIELD_NUMBER;
      hash = (53 * hash) + getAdFreeTimes();
    }
    if (hasLastAdTime()) {
      hash = (37 * hash) + LASTADTIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getLastAdTime());
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.MagicTreasureJackpotMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.MagicTreasureJackpotMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.MagicTreasureJackpotMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.MagicTreasureJackpotMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.MagicTreasureJackpotMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.MagicTreasureJackpotMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.MagicTreasureJackpotMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.MagicTreasureJackpotMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.MagicTreasureJackpotMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.MagicTreasureJackpotMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.MagicTreasureJackpotMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.MagicTreasureJackpotMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.MagicTreasureJackpotMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.MagicTreasureJackpotMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.MagicTreasureJackpotMsg)
      xddq.pb.MagicTreasureJackpotMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_MagicTreasureJackpotMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_MagicTreasureJackpotMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.MagicTreasureJackpotMsg.class, xddq.pb.MagicTreasureJackpotMsg.Builder.class);
    }

    // Construct using xddq.pb.MagicTreasureJackpotMsg.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      poolId_ = 0;
      drawTimes_ = 0;
      freeDrawTimes_ = 0;
      adFreeTimes_ = 0;
      lastAdTime_ = 0L;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_MagicTreasureJackpotMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.MagicTreasureJackpotMsg getDefaultInstanceForType() {
      return xddq.pb.MagicTreasureJackpotMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.MagicTreasureJackpotMsg build() {
      xddq.pb.MagicTreasureJackpotMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.MagicTreasureJackpotMsg buildPartial() {
      xddq.pb.MagicTreasureJackpotMsg result = new xddq.pb.MagicTreasureJackpotMsg(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.MagicTreasureJackpotMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.poolId_ = poolId_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.drawTimes_ = drawTimes_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.freeDrawTimes_ = freeDrawTimes_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.adFreeTimes_ = adFreeTimes_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.lastAdTime_ = lastAdTime_;
        to_bitField0_ |= 0x00000010;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.MagicTreasureJackpotMsg) {
        return mergeFrom((xddq.pb.MagicTreasureJackpotMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.MagicTreasureJackpotMsg other) {
      if (other == xddq.pb.MagicTreasureJackpotMsg.getDefaultInstance()) return this;
      if (other.hasPoolId()) {
        setPoolId(other.getPoolId());
      }
      if (other.hasDrawTimes()) {
        setDrawTimes(other.getDrawTimes());
      }
      if (other.hasFreeDrawTimes()) {
        setFreeDrawTimes(other.getFreeDrawTimes());
      }
      if (other.hasAdFreeTimes()) {
        setAdFreeTimes(other.getAdFreeTimes());
      }
      if (other.hasLastAdTime()) {
        setLastAdTime(other.getLastAdTime());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              poolId_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              drawTimes_ = input.readInt32();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 24: {
              freeDrawTimes_ = input.readInt32();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 32: {
              adFreeTimes_ = input.readInt32();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            case 40: {
              lastAdTime_ = input.readInt64();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int poolId_ ;
    /**
     * <code>optional int32 poolId = 1;</code>
     * @return Whether the poolId field is set.
     */
    @java.lang.Override
    public boolean hasPoolId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 poolId = 1;</code>
     * @return The poolId.
     */
    @java.lang.Override
    public int getPoolId() {
      return poolId_;
    }
    /**
     * <code>optional int32 poolId = 1;</code>
     * @param value The poolId to set.
     * @return This builder for chaining.
     */
    public Builder setPoolId(int value) {

      poolId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 poolId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearPoolId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      poolId_ = 0;
      onChanged();
      return this;
    }

    private int drawTimes_ ;
    /**
     * <code>optional int32 drawTimes = 2;</code>
     * @return Whether the drawTimes field is set.
     */
    @java.lang.Override
    public boolean hasDrawTimes() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 drawTimes = 2;</code>
     * @return The drawTimes.
     */
    @java.lang.Override
    public int getDrawTimes() {
      return drawTimes_;
    }
    /**
     * <code>optional int32 drawTimes = 2;</code>
     * @param value The drawTimes to set.
     * @return This builder for chaining.
     */
    public Builder setDrawTimes(int value) {

      drawTimes_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 drawTimes = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearDrawTimes() {
      bitField0_ = (bitField0_ & ~0x00000002);
      drawTimes_ = 0;
      onChanged();
      return this;
    }

    private int freeDrawTimes_ ;
    /**
     * <code>optional int32 freeDrawTimes = 3;</code>
     * @return Whether the freeDrawTimes field is set.
     */
    @java.lang.Override
    public boolean hasFreeDrawTimes() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int32 freeDrawTimes = 3;</code>
     * @return The freeDrawTimes.
     */
    @java.lang.Override
    public int getFreeDrawTimes() {
      return freeDrawTimes_;
    }
    /**
     * <code>optional int32 freeDrawTimes = 3;</code>
     * @param value The freeDrawTimes to set.
     * @return This builder for chaining.
     */
    public Builder setFreeDrawTimes(int value) {

      freeDrawTimes_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 freeDrawTimes = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearFreeDrawTimes() {
      bitField0_ = (bitField0_ & ~0x00000004);
      freeDrawTimes_ = 0;
      onChanged();
      return this;
    }

    private int adFreeTimes_ ;
    /**
     * <code>optional int32 adFreeTimes = 4;</code>
     * @return Whether the adFreeTimes field is set.
     */
    @java.lang.Override
    public boolean hasAdFreeTimes() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int32 adFreeTimes = 4;</code>
     * @return The adFreeTimes.
     */
    @java.lang.Override
    public int getAdFreeTimes() {
      return adFreeTimes_;
    }
    /**
     * <code>optional int32 adFreeTimes = 4;</code>
     * @param value The adFreeTimes to set.
     * @return This builder for chaining.
     */
    public Builder setAdFreeTimes(int value) {

      adFreeTimes_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 adFreeTimes = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearAdFreeTimes() {
      bitField0_ = (bitField0_ & ~0x00000008);
      adFreeTimes_ = 0;
      onChanged();
      return this;
    }

    private long lastAdTime_ ;
    /**
     * <code>optional int64 lastAdTime = 5;</code>
     * @return Whether the lastAdTime field is set.
     */
    @java.lang.Override
    public boolean hasLastAdTime() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional int64 lastAdTime = 5;</code>
     * @return The lastAdTime.
     */
    @java.lang.Override
    public long getLastAdTime() {
      return lastAdTime_;
    }
    /**
     * <code>optional int64 lastAdTime = 5;</code>
     * @param value The lastAdTime to set.
     * @return This builder for chaining.
     */
    public Builder setLastAdTime(long value) {

      lastAdTime_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 lastAdTime = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearLastAdTime() {
      bitField0_ = (bitField0_ & ~0x00000010);
      lastAdTime_ = 0L;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.MagicTreasureJackpotMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.MagicTreasureJackpotMsg)
  private static final xddq.pb.MagicTreasureJackpotMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.MagicTreasureJackpotMsg();
  }

  public static xddq.pb.MagicTreasureJackpotMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<MagicTreasureJackpotMsg>
      PARSER = new com.google.protobuf.AbstractParser<MagicTreasureJackpotMsg>() {
    @java.lang.Override
    public MagicTreasureJackpotMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<MagicTreasureJackpotMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<MagicTreasureJackpotMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.MagicTreasureJackpotMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

