// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.GetPresetMagicInfoResp}
 */
public final class GetPresetMagicInfoResp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.GetPresetMagicInfoResp)
    GetPresetMagicInfoRespOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      GetPresetMagicInfoResp.class.getName());
  }
  // Use GetPresetMagicInfoResp.newBuilder() to construct.
  private GetPresetMagicInfoResp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private GetPresetMagicInfoResp() {
    magicData_ = java.util.Collections.emptyList();
    equipMagicList_ = emptyIntList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_GetPresetMagicInfoResp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_GetPresetMagicInfoResp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.GetPresetMagicInfoResp.class, xddq.pb.GetPresetMagicInfoResp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int MAGICDATA_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.MagicDataMsg> magicData_;
  /**
   * <code>repeated .xddq.pb.MagicDataMsg magicData = 2;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.MagicDataMsg> getMagicDataList() {
    return magicData_;
  }
  /**
   * <code>repeated .xddq.pb.MagicDataMsg magicData = 2;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.MagicDataMsgOrBuilder> 
      getMagicDataOrBuilderList() {
    return magicData_;
  }
  /**
   * <code>repeated .xddq.pb.MagicDataMsg magicData = 2;</code>
   */
  @java.lang.Override
  public int getMagicDataCount() {
    return magicData_.size();
  }
  /**
   * <code>repeated .xddq.pb.MagicDataMsg magicData = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.MagicDataMsg getMagicData(int index) {
    return magicData_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.MagicDataMsg magicData = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.MagicDataMsgOrBuilder getMagicDataOrBuilder(
      int index) {
    return magicData_.get(index);
  }

  public static final int EQUIPMAGICLIST_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private com.google.protobuf.Internal.IntList equipMagicList_ =
      emptyIntList();
  /**
   * <code>repeated int32 equipMagicList = 3;</code>
   * @return A list containing the equipMagicList.
   */
  @java.lang.Override
  public java.util.List<java.lang.Integer>
      getEquipMagicListList() {
    return equipMagicList_;
  }
  /**
   * <code>repeated int32 equipMagicList = 3;</code>
   * @return The count of equipMagicList.
   */
  public int getEquipMagicListCount() {
    return equipMagicList_.size();
  }
  /**
   * <code>repeated int32 equipMagicList = 3;</code>
   * @param index The index of the element to return.
   * @return The equipMagicList at the given index.
   */
  public int getEquipMagicList(int index) {
    return equipMagicList_.getInt(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    for (int i = 0; i < magicData_.size(); i++) {
      output.writeMessage(2, magicData_.get(i));
    }
    for (int i = 0; i < equipMagicList_.size(); i++) {
      output.writeInt32(3, equipMagicList_.getInt(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    for (int i = 0; i < magicData_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, magicData_.get(i));
    }
    {
      int dataSize = 0;
      for (int i = 0; i < equipMagicList_.size(); i++) {
        dataSize += com.google.protobuf.CodedOutputStream
          .computeInt32SizeNoTag(equipMagicList_.getInt(i));
      }
      size += dataSize;
      size += 1 * getEquipMagicListList().size();
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.GetPresetMagicInfoResp)) {
      return super.equals(obj);
    }
    xddq.pb.GetPresetMagicInfoResp other = (xddq.pb.GetPresetMagicInfoResp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (!getMagicDataList()
        .equals(other.getMagicDataList())) return false;
    if (!getEquipMagicListList()
        .equals(other.getEquipMagicListList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (getMagicDataCount() > 0) {
      hash = (37 * hash) + MAGICDATA_FIELD_NUMBER;
      hash = (53 * hash) + getMagicDataList().hashCode();
    }
    if (getEquipMagicListCount() > 0) {
      hash = (37 * hash) + EQUIPMAGICLIST_FIELD_NUMBER;
      hash = (53 * hash) + getEquipMagicListList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.GetPresetMagicInfoResp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GetPresetMagicInfoResp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GetPresetMagicInfoResp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GetPresetMagicInfoResp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GetPresetMagicInfoResp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GetPresetMagicInfoResp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GetPresetMagicInfoResp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.GetPresetMagicInfoResp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.GetPresetMagicInfoResp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.GetPresetMagicInfoResp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.GetPresetMagicInfoResp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.GetPresetMagicInfoResp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.GetPresetMagicInfoResp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.GetPresetMagicInfoResp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.GetPresetMagicInfoResp)
      xddq.pb.GetPresetMagicInfoRespOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GetPresetMagicInfoResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GetPresetMagicInfoResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.GetPresetMagicInfoResp.class, xddq.pb.GetPresetMagicInfoResp.Builder.class);
    }

    // Construct using xddq.pb.GetPresetMagicInfoResp.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      if (magicDataBuilder_ == null) {
        magicData_ = java.util.Collections.emptyList();
      } else {
        magicData_ = null;
        magicDataBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000002);
      equipMagicList_ = emptyIntList();
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GetPresetMagicInfoResp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.GetPresetMagicInfoResp getDefaultInstanceForType() {
      return xddq.pb.GetPresetMagicInfoResp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.GetPresetMagicInfoResp build() {
      xddq.pb.GetPresetMagicInfoResp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.GetPresetMagicInfoResp buildPartial() {
      xddq.pb.GetPresetMagicInfoResp result = new xddq.pb.GetPresetMagicInfoResp(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.GetPresetMagicInfoResp result) {
      if (magicDataBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          magicData_ = java.util.Collections.unmodifiableList(magicData_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.magicData_ = magicData_;
      } else {
        result.magicData_ = magicDataBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.GetPresetMagicInfoResp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        equipMagicList_.makeImmutable();
        result.equipMagicList_ = equipMagicList_;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.GetPresetMagicInfoResp) {
        return mergeFrom((xddq.pb.GetPresetMagicInfoResp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.GetPresetMagicInfoResp other) {
      if (other == xddq.pb.GetPresetMagicInfoResp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (magicDataBuilder_ == null) {
        if (!other.magicData_.isEmpty()) {
          if (magicData_.isEmpty()) {
            magicData_ = other.magicData_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureMagicDataIsMutable();
            magicData_.addAll(other.magicData_);
          }
          onChanged();
        }
      } else {
        if (!other.magicData_.isEmpty()) {
          if (magicDataBuilder_.isEmpty()) {
            magicDataBuilder_.dispose();
            magicDataBuilder_ = null;
            magicData_ = other.magicData_;
            bitField0_ = (bitField0_ & ~0x00000002);
            magicDataBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetMagicDataFieldBuilder() : null;
          } else {
            magicDataBuilder_.addAllMessages(other.magicData_);
          }
        }
      }
      if (!other.equipMagicList_.isEmpty()) {
        if (equipMagicList_.isEmpty()) {
          equipMagicList_ = other.equipMagicList_;
          equipMagicList_.makeImmutable();
          bitField0_ |= 0x00000004;
        } else {
          ensureEquipMagicListIsMutable();
          equipMagicList_.addAll(other.equipMagicList_);
        }
        onChanged();
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              xddq.pb.MagicDataMsg m =
                  input.readMessage(
                      xddq.pb.MagicDataMsg.parser(),
                      extensionRegistry);
              if (magicDataBuilder_ == null) {
                ensureMagicDataIsMutable();
                magicData_.add(m);
              } else {
                magicDataBuilder_.addMessage(m);
              }
              break;
            } // case 18
            case 24: {
              int v = input.readInt32();
              ensureEquipMagicListIsMutable();
              equipMagicList_.addInt(v);
              break;
            } // case 24
            case 26: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              ensureEquipMagicListIsMutable();
              while (input.getBytesUntilLimit() > 0) {
                equipMagicList_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            } // case 26
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.MagicDataMsg> magicData_ =
      java.util.Collections.emptyList();
    private void ensureMagicDataIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        magicData_ = new java.util.ArrayList<xddq.pb.MagicDataMsg>(magicData_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.MagicDataMsg, xddq.pb.MagicDataMsg.Builder, xddq.pb.MagicDataMsgOrBuilder> magicDataBuilder_;

    /**
     * <code>repeated .xddq.pb.MagicDataMsg magicData = 2;</code>
     */
    public java.util.List<xddq.pb.MagicDataMsg> getMagicDataList() {
      if (magicDataBuilder_ == null) {
        return java.util.Collections.unmodifiableList(magicData_);
      } else {
        return magicDataBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.MagicDataMsg magicData = 2;</code>
     */
    public int getMagicDataCount() {
      if (magicDataBuilder_ == null) {
        return magicData_.size();
      } else {
        return magicDataBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.MagicDataMsg magicData = 2;</code>
     */
    public xddq.pb.MagicDataMsg getMagicData(int index) {
      if (magicDataBuilder_ == null) {
        return magicData_.get(index);
      } else {
        return magicDataBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.MagicDataMsg magicData = 2;</code>
     */
    public Builder setMagicData(
        int index, xddq.pb.MagicDataMsg value) {
      if (magicDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMagicDataIsMutable();
        magicData_.set(index, value);
        onChanged();
      } else {
        magicDataBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MagicDataMsg magicData = 2;</code>
     */
    public Builder setMagicData(
        int index, xddq.pb.MagicDataMsg.Builder builderForValue) {
      if (magicDataBuilder_ == null) {
        ensureMagicDataIsMutable();
        magicData_.set(index, builderForValue.build());
        onChanged();
      } else {
        magicDataBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MagicDataMsg magicData = 2;</code>
     */
    public Builder addMagicData(xddq.pb.MagicDataMsg value) {
      if (magicDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMagicDataIsMutable();
        magicData_.add(value);
        onChanged();
      } else {
        magicDataBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MagicDataMsg magicData = 2;</code>
     */
    public Builder addMagicData(
        int index, xddq.pb.MagicDataMsg value) {
      if (magicDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMagicDataIsMutable();
        magicData_.add(index, value);
        onChanged();
      } else {
        magicDataBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MagicDataMsg magicData = 2;</code>
     */
    public Builder addMagicData(
        xddq.pb.MagicDataMsg.Builder builderForValue) {
      if (magicDataBuilder_ == null) {
        ensureMagicDataIsMutable();
        magicData_.add(builderForValue.build());
        onChanged();
      } else {
        magicDataBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MagicDataMsg magicData = 2;</code>
     */
    public Builder addMagicData(
        int index, xddq.pb.MagicDataMsg.Builder builderForValue) {
      if (magicDataBuilder_ == null) {
        ensureMagicDataIsMutable();
        magicData_.add(index, builderForValue.build());
        onChanged();
      } else {
        magicDataBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MagicDataMsg magicData = 2;</code>
     */
    public Builder addAllMagicData(
        java.lang.Iterable<? extends xddq.pb.MagicDataMsg> values) {
      if (magicDataBuilder_ == null) {
        ensureMagicDataIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, magicData_);
        onChanged();
      } else {
        magicDataBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MagicDataMsg magicData = 2;</code>
     */
    public Builder clearMagicData() {
      if (magicDataBuilder_ == null) {
        magicData_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        magicDataBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MagicDataMsg magicData = 2;</code>
     */
    public Builder removeMagicData(int index) {
      if (magicDataBuilder_ == null) {
        ensureMagicDataIsMutable();
        magicData_.remove(index);
        onChanged();
      } else {
        magicDataBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MagicDataMsg magicData = 2;</code>
     */
    public xddq.pb.MagicDataMsg.Builder getMagicDataBuilder(
        int index) {
      return internalGetMagicDataFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.MagicDataMsg magicData = 2;</code>
     */
    public xddq.pb.MagicDataMsgOrBuilder getMagicDataOrBuilder(
        int index) {
      if (magicDataBuilder_ == null) {
        return magicData_.get(index);  } else {
        return magicDataBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.MagicDataMsg magicData = 2;</code>
     */
    public java.util.List<? extends xddq.pb.MagicDataMsgOrBuilder> 
         getMagicDataOrBuilderList() {
      if (magicDataBuilder_ != null) {
        return magicDataBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(magicData_);
      }
    }
    /**
     * <code>repeated .xddq.pb.MagicDataMsg magicData = 2;</code>
     */
    public xddq.pb.MagicDataMsg.Builder addMagicDataBuilder() {
      return internalGetMagicDataFieldBuilder().addBuilder(
          xddq.pb.MagicDataMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.MagicDataMsg magicData = 2;</code>
     */
    public xddq.pb.MagicDataMsg.Builder addMagicDataBuilder(
        int index) {
      return internalGetMagicDataFieldBuilder().addBuilder(
          index, xddq.pb.MagicDataMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.MagicDataMsg magicData = 2;</code>
     */
    public java.util.List<xddq.pb.MagicDataMsg.Builder> 
         getMagicDataBuilderList() {
      return internalGetMagicDataFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.MagicDataMsg, xddq.pb.MagicDataMsg.Builder, xddq.pb.MagicDataMsgOrBuilder> 
        internalGetMagicDataFieldBuilder() {
      if (magicDataBuilder_ == null) {
        magicDataBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.MagicDataMsg, xddq.pb.MagicDataMsg.Builder, xddq.pb.MagicDataMsgOrBuilder>(
                magicData_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        magicData_ = null;
      }
      return magicDataBuilder_;
    }

    private com.google.protobuf.Internal.IntList equipMagicList_ = emptyIntList();
    private void ensureEquipMagicListIsMutable() {
      if (!equipMagicList_.isModifiable()) {
        equipMagicList_ = makeMutableCopy(equipMagicList_);
      }
      bitField0_ |= 0x00000004;
    }
    /**
     * <code>repeated int32 equipMagicList = 3;</code>
     * @return A list containing the equipMagicList.
     */
    public java.util.List<java.lang.Integer>
        getEquipMagicListList() {
      equipMagicList_.makeImmutable();
      return equipMagicList_;
    }
    /**
     * <code>repeated int32 equipMagicList = 3;</code>
     * @return The count of equipMagicList.
     */
    public int getEquipMagicListCount() {
      return equipMagicList_.size();
    }
    /**
     * <code>repeated int32 equipMagicList = 3;</code>
     * @param index The index of the element to return.
     * @return The equipMagicList at the given index.
     */
    public int getEquipMagicList(int index) {
      return equipMagicList_.getInt(index);
    }
    /**
     * <code>repeated int32 equipMagicList = 3;</code>
     * @param index The index to set the value at.
     * @param value The equipMagicList to set.
     * @return This builder for chaining.
     */
    public Builder setEquipMagicList(
        int index, int value) {

      ensureEquipMagicListIsMutable();
      equipMagicList_.setInt(index, value);
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 equipMagicList = 3;</code>
     * @param value The equipMagicList to add.
     * @return This builder for chaining.
     */
    public Builder addEquipMagicList(int value) {

      ensureEquipMagicListIsMutable();
      equipMagicList_.addInt(value);
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 equipMagicList = 3;</code>
     * @param values The equipMagicList to add.
     * @return This builder for chaining.
     */
    public Builder addAllEquipMagicList(
        java.lang.Iterable<? extends java.lang.Integer> values) {
      ensureEquipMagicListIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, equipMagicList_);
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 equipMagicList = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearEquipMagicList() {
      equipMagicList_ = emptyIntList();
      bitField0_ = (bitField0_ & ~0x00000004);
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.GetPresetMagicInfoResp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.GetPresetMagicInfoResp)
  private static final xddq.pb.GetPresetMagicInfoResp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.GetPresetMagicInfoResp();
  }

  public static xddq.pb.GetPresetMagicInfoResp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<GetPresetMagicInfoResp>
      PARSER = new com.google.protobuf.AbstractParser<GetPresetMagicInfoResp>() {
    @java.lang.Override
    public GetPresetMagicInfoResp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<GetPresetMagicInfoResp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<GetPresetMagicInfoResp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.GetPresetMagicInfoResp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

