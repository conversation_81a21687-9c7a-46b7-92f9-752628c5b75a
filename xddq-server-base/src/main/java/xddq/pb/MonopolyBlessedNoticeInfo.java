// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.MonopolyBlessedNoticeInfo}
 */
public final class MonopolyBlessedNoticeInfo extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.MonopolyBlessedNoticeInfo)
    MonopolyBlessedNoticeInfoOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      MonopolyBlessedNoticeInfo.class.getName());
  }
  // Use MonopolyBlessedNoticeInfo.newBuilder() to construct.
  private MonopolyBlessedNoticeInfo(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private MonopolyBlessedNoticeInfo() {
    giftName_ = "";
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_MonopolyBlessedNoticeInfo_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_MonopolyBlessedNoticeInfo_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.MonopolyBlessedNoticeInfo.class, xddq.pb.MonopolyBlessedNoticeInfo.Builder.class);
  }

  private int bitField0_;
  public static final int ID_FIELD_NUMBER = 1;
  private long id_ = 0L;
  /**
   * <code>required int64 id = 1;</code>
   * @return Whether the id field is set.
   */
  @java.lang.Override
  public boolean hasId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int64 id = 1;</code>
   * @return The id.
   */
  @java.lang.Override
  public long getId() {
    return id_;
  }

  public static final int PLAYERDATA_FIELD_NUMBER = 2;
  private xddq.pb.PlayerCharacterImageMsg playerData_;
  /**
   * <code>required .xddq.pb.PlayerCharacterImageMsg playerData = 2;</code>
   * @return Whether the playerData field is set.
   */
  @java.lang.Override
  public boolean hasPlayerData() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>required .xddq.pb.PlayerCharacterImageMsg playerData = 2;</code>
   * @return The playerData.
   */
  @java.lang.Override
  public xddq.pb.PlayerCharacterImageMsg getPlayerData() {
    return playerData_ == null ? xddq.pb.PlayerCharacterImageMsg.getDefaultInstance() : playerData_;
  }
  /**
   * <code>required .xddq.pb.PlayerCharacterImageMsg playerData = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.PlayerCharacterImageMsgOrBuilder getPlayerDataOrBuilder() {
    return playerData_ == null ? xddq.pb.PlayerCharacterImageMsg.getDefaultInstance() : playerData_;
  }

  public static final int STATUS_FIELD_NUMBER = 3;
  private int status_ = 0;
  /**
   * <code>optional int32 status = 3;</code>
   * @return Whether the status field is set.
   */
  @java.lang.Override
  public boolean hasStatus() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional int32 status = 3;</code>
   * @return The status.
   */
  @java.lang.Override
  public int getStatus() {
    return status_;
  }

  public static final int TIME_FIELD_NUMBER = 4;
  private long time_ = 0L;
  /**
   * <code>optional int64 time = 4;</code>
   * @return Whether the time field is set.
   */
  @java.lang.Override
  public boolean hasTime() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional int64 time = 4;</code>
   * @return The time.
   */
  @java.lang.Override
  public long getTime() {
    return time_;
  }

  public static final int STRENGTH_FIELD_NUMBER = 5;
  private int strength_ = 0;
  /**
   * <code>optional int32 strength = 5;</code>
   * @return Whether the strength field is set.
   */
  @java.lang.Override
  public boolean hasStrength() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>optional int32 strength = 5;</code>
   * @return The strength.
   */
  @java.lang.Override
  public int getStrength() {
    return strength_;
  }

  public static final int GIFTNAME_FIELD_NUMBER = 6;
  @SuppressWarnings("serial")
  private volatile java.lang.Object giftName_ = "";
  /**
   * <code>optional string giftName = 6;</code>
   * @return Whether the giftName field is set.
   */
  @java.lang.Override
  public boolean hasGiftName() {
    return ((bitField0_ & 0x00000020) != 0);
  }
  /**
   * <code>optional string giftName = 6;</code>
   * @return The giftName.
   */
  @java.lang.Override
  public java.lang.String getGiftName() {
    java.lang.Object ref = giftName_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        giftName_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string giftName = 6;</code>
   * @return The bytes for giftName.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getGiftNameBytes() {
    java.lang.Object ref = giftName_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      giftName_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasId()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasPlayerData()) {
      memoizedIsInitialized = 0;
      return false;
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt64(1, id_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeMessage(2, getPlayerData());
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt32(3, status_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt64(4, time_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      output.writeInt32(5, strength_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 6, giftName_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(1, id_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getPlayerData());
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(3, status_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(4, time_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(5, strength_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(6, giftName_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.MonopolyBlessedNoticeInfo)) {
      return super.equals(obj);
    }
    xddq.pb.MonopolyBlessedNoticeInfo other = (xddq.pb.MonopolyBlessedNoticeInfo) obj;

    if (hasId() != other.hasId()) return false;
    if (hasId()) {
      if (getId()
          != other.getId()) return false;
    }
    if (hasPlayerData() != other.hasPlayerData()) return false;
    if (hasPlayerData()) {
      if (!getPlayerData()
          .equals(other.getPlayerData())) return false;
    }
    if (hasStatus() != other.hasStatus()) return false;
    if (hasStatus()) {
      if (getStatus()
          != other.getStatus()) return false;
    }
    if (hasTime() != other.hasTime()) return false;
    if (hasTime()) {
      if (getTime()
          != other.getTime()) return false;
    }
    if (hasStrength() != other.hasStrength()) return false;
    if (hasStrength()) {
      if (getStrength()
          != other.getStrength()) return false;
    }
    if (hasGiftName() != other.hasGiftName()) return false;
    if (hasGiftName()) {
      if (!getGiftName()
          .equals(other.getGiftName())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasId()) {
      hash = (37 * hash) + ID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getId());
    }
    if (hasPlayerData()) {
      hash = (37 * hash) + PLAYERDATA_FIELD_NUMBER;
      hash = (53 * hash) + getPlayerData().hashCode();
    }
    if (hasStatus()) {
      hash = (37 * hash) + STATUS_FIELD_NUMBER;
      hash = (53 * hash) + getStatus();
    }
    if (hasTime()) {
      hash = (37 * hash) + TIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTime());
    }
    if (hasStrength()) {
      hash = (37 * hash) + STRENGTH_FIELD_NUMBER;
      hash = (53 * hash) + getStrength();
    }
    if (hasGiftName()) {
      hash = (37 * hash) + GIFTNAME_FIELD_NUMBER;
      hash = (53 * hash) + getGiftName().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.MonopolyBlessedNoticeInfo parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.MonopolyBlessedNoticeInfo parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.MonopolyBlessedNoticeInfo parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.MonopolyBlessedNoticeInfo parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.MonopolyBlessedNoticeInfo parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.MonopolyBlessedNoticeInfo parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.MonopolyBlessedNoticeInfo parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.MonopolyBlessedNoticeInfo parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.MonopolyBlessedNoticeInfo parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.MonopolyBlessedNoticeInfo parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.MonopolyBlessedNoticeInfo parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.MonopolyBlessedNoticeInfo parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.MonopolyBlessedNoticeInfo prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.MonopolyBlessedNoticeInfo}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.MonopolyBlessedNoticeInfo)
      xddq.pb.MonopolyBlessedNoticeInfoOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_MonopolyBlessedNoticeInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_MonopolyBlessedNoticeInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.MonopolyBlessedNoticeInfo.class, xddq.pb.MonopolyBlessedNoticeInfo.Builder.class);
    }

    // Construct using xddq.pb.MonopolyBlessedNoticeInfo.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetPlayerDataFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      id_ = 0L;
      playerData_ = null;
      if (playerDataBuilder_ != null) {
        playerDataBuilder_.dispose();
        playerDataBuilder_ = null;
      }
      status_ = 0;
      time_ = 0L;
      strength_ = 0;
      giftName_ = "";
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_MonopolyBlessedNoticeInfo_descriptor;
    }

    @java.lang.Override
    public xddq.pb.MonopolyBlessedNoticeInfo getDefaultInstanceForType() {
      return xddq.pb.MonopolyBlessedNoticeInfo.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.MonopolyBlessedNoticeInfo build() {
      xddq.pb.MonopolyBlessedNoticeInfo result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.MonopolyBlessedNoticeInfo buildPartial() {
      xddq.pb.MonopolyBlessedNoticeInfo result = new xddq.pb.MonopolyBlessedNoticeInfo(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.MonopolyBlessedNoticeInfo result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.id_ = id_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.playerData_ = playerDataBuilder_ == null
            ? playerData_
            : playerDataBuilder_.build();
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.status_ = status_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.time_ = time_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.strength_ = strength_;
        to_bitField0_ |= 0x00000010;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.giftName_ = giftName_;
        to_bitField0_ |= 0x00000020;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.MonopolyBlessedNoticeInfo) {
        return mergeFrom((xddq.pb.MonopolyBlessedNoticeInfo)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.MonopolyBlessedNoticeInfo other) {
      if (other == xddq.pb.MonopolyBlessedNoticeInfo.getDefaultInstance()) return this;
      if (other.hasId()) {
        setId(other.getId());
      }
      if (other.hasPlayerData()) {
        mergePlayerData(other.getPlayerData());
      }
      if (other.hasStatus()) {
        setStatus(other.getStatus());
      }
      if (other.hasTime()) {
        setTime(other.getTime());
      }
      if (other.hasStrength()) {
        setStrength(other.getStrength());
      }
      if (other.hasGiftName()) {
        giftName_ = other.giftName_;
        bitField0_ |= 0x00000020;
        onChanged();
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasId()) {
        return false;
      }
      if (!hasPlayerData()) {
        return false;
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              id_ = input.readInt64();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              input.readMessage(
                  internalGetPlayerDataFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 24: {
              status_ = input.readInt32();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 32: {
              time_ = input.readInt64();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            case 40: {
              strength_ = input.readInt32();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            case 50: {
              giftName_ = input.readBytes();
              bitField0_ |= 0x00000020;
              break;
            } // case 50
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private long id_ ;
    /**
     * <code>required int64 id = 1;</code>
     * @return Whether the id field is set.
     */
    @java.lang.Override
    public boolean hasId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int64 id = 1;</code>
     * @return The id.
     */
    @java.lang.Override
    public long getId() {
      return id_;
    }
    /**
     * <code>required int64 id = 1;</code>
     * @param value The id to set.
     * @return This builder for chaining.
     */
    public Builder setId(long value) {

      id_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int64 id = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      id_ = 0L;
      onChanged();
      return this;
    }

    private xddq.pb.PlayerCharacterImageMsg playerData_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PlayerCharacterImageMsg, xddq.pb.PlayerCharacterImageMsg.Builder, xddq.pb.PlayerCharacterImageMsgOrBuilder> playerDataBuilder_;
    /**
     * <code>required .xddq.pb.PlayerCharacterImageMsg playerData = 2;</code>
     * @return Whether the playerData field is set.
     */
    public boolean hasPlayerData() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>required .xddq.pb.PlayerCharacterImageMsg playerData = 2;</code>
     * @return The playerData.
     */
    public xddq.pb.PlayerCharacterImageMsg getPlayerData() {
      if (playerDataBuilder_ == null) {
        return playerData_ == null ? xddq.pb.PlayerCharacterImageMsg.getDefaultInstance() : playerData_;
      } else {
        return playerDataBuilder_.getMessage();
      }
    }
    /**
     * <code>required .xddq.pb.PlayerCharacterImageMsg playerData = 2;</code>
     */
    public Builder setPlayerData(xddq.pb.PlayerCharacterImageMsg value) {
      if (playerDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        playerData_ = value;
      } else {
        playerDataBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>required .xddq.pb.PlayerCharacterImageMsg playerData = 2;</code>
     */
    public Builder setPlayerData(
        xddq.pb.PlayerCharacterImageMsg.Builder builderForValue) {
      if (playerDataBuilder_ == null) {
        playerData_ = builderForValue.build();
      } else {
        playerDataBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>required .xddq.pb.PlayerCharacterImageMsg playerData = 2;</code>
     */
    public Builder mergePlayerData(xddq.pb.PlayerCharacterImageMsg value) {
      if (playerDataBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0) &&
          playerData_ != null &&
          playerData_ != xddq.pb.PlayerCharacterImageMsg.getDefaultInstance()) {
          getPlayerDataBuilder().mergeFrom(value);
        } else {
          playerData_ = value;
        }
      } else {
        playerDataBuilder_.mergeFrom(value);
      }
      if (playerData_ != null) {
        bitField0_ |= 0x00000002;
        onChanged();
      }
      return this;
    }
    /**
     * <code>required .xddq.pb.PlayerCharacterImageMsg playerData = 2;</code>
     */
    public Builder clearPlayerData() {
      bitField0_ = (bitField0_ & ~0x00000002);
      playerData_ = null;
      if (playerDataBuilder_ != null) {
        playerDataBuilder_.dispose();
        playerDataBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>required .xddq.pb.PlayerCharacterImageMsg playerData = 2;</code>
     */
    public xddq.pb.PlayerCharacterImageMsg.Builder getPlayerDataBuilder() {
      bitField0_ |= 0x00000002;
      onChanged();
      return internalGetPlayerDataFieldBuilder().getBuilder();
    }
    /**
     * <code>required .xddq.pb.PlayerCharacterImageMsg playerData = 2;</code>
     */
    public xddq.pb.PlayerCharacterImageMsgOrBuilder getPlayerDataOrBuilder() {
      if (playerDataBuilder_ != null) {
        return playerDataBuilder_.getMessageOrBuilder();
      } else {
        return playerData_ == null ?
            xddq.pb.PlayerCharacterImageMsg.getDefaultInstance() : playerData_;
      }
    }
    /**
     * <code>required .xddq.pb.PlayerCharacterImageMsg playerData = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PlayerCharacterImageMsg, xddq.pb.PlayerCharacterImageMsg.Builder, xddq.pb.PlayerCharacterImageMsgOrBuilder> 
        internalGetPlayerDataFieldBuilder() {
      if (playerDataBuilder_ == null) {
        playerDataBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.PlayerCharacterImageMsg, xddq.pb.PlayerCharacterImageMsg.Builder, xddq.pb.PlayerCharacterImageMsgOrBuilder>(
                getPlayerData(),
                getParentForChildren(),
                isClean());
        playerData_ = null;
      }
      return playerDataBuilder_;
    }

    private int status_ ;
    /**
     * <code>optional int32 status = 3;</code>
     * @return Whether the status field is set.
     */
    @java.lang.Override
    public boolean hasStatus() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int32 status = 3;</code>
     * @return The status.
     */
    @java.lang.Override
    public int getStatus() {
      return status_;
    }
    /**
     * <code>optional int32 status = 3;</code>
     * @param value The status to set.
     * @return This builder for chaining.
     */
    public Builder setStatus(int value) {

      status_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 status = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearStatus() {
      bitField0_ = (bitField0_ & ~0x00000004);
      status_ = 0;
      onChanged();
      return this;
    }

    private long time_ ;
    /**
     * <code>optional int64 time = 4;</code>
     * @return Whether the time field is set.
     */
    @java.lang.Override
    public boolean hasTime() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int64 time = 4;</code>
     * @return The time.
     */
    @java.lang.Override
    public long getTime() {
      return time_;
    }
    /**
     * <code>optional int64 time = 4;</code>
     * @param value The time to set.
     * @return This builder for chaining.
     */
    public Builder setTime(long value) {

      time_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 time = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearTime() {
      bitField0_ = (bitField0_ & ~0x00000008);
      time_ = 0L;
      onChanged();
      return this;
    }

    private int strength_ ;
    /**
     * <code>optional int32 strength = 5;</code>
     * @return Whether the strength field is set.
     */
    @java.lang.Override
    public boolean hasStrength() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional int32 strength = 5;</code>
     * @return The strength.
     */
    @java.lang.Override
    public int getStrength() {
      return strength_;
    }
    /**
     * <code>optional int32 strength = 5;</code>
     * @param value The strength to set.
     * @return This builder for chaining.
     */
    public Builder setStrength(int value) {

      strength_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 strength = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearStrength() {
      bitField0_ = (bitField0_ & ~0x00000010);
      strength_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object giftName_ = "";
    /**
     * <code>optional string giftName = 6;</code>
     * @return Whether the giftName field is set.
     */
    public boolean hasGiftName() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional string giftName = 6;</code>
     * @return The giftName.
     */
    public java.lang.String getGiftName() {
      java.lang.Object ref = giftName_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          giftName_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string giftName = 6;</code>
     * @return The bytes for giftName.
     */
    public com.google.protobuf.ByteString
        getGiftNameBytes() {
      java.lang.Object ref = giftName_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        giftName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string giftName = 6;</code>
     * @param value The giftName to set.
     * @return This builder for chaining.
     */
    public Builder setGiftName(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      giftName_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>optional string giftName = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearGiftName() {
      giftName_ = getDefaultInstance().getGiftName();
      bitField0_ = (bitField0_ & ~0x00000020);
      onChanged();
      return this;
    }
    /**
     * <code>optional string giftName = 6;</code>
     * @param value The bytes for giftName to set.
     * @return This builder for chaining.
     */
    public Builder setGiftNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      giftName_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.MonopolyBlessedNoticeInfo)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.MonopolyBlessedNoticeInfo)
  private static final xddq.pb.MonopolyBlessedNoticeInfo DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.MonopolyBlessedNoticeInfo();
  }

  public static xddq.pb.MonopolyBlessedNoticeInfo getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<MonopolyBlessedNoticeInfo>
      PARSER = new com.google.protobuf.AbstractParser<MonopolyBlessedNoticeInfo>() {
    @java.lang.Override
    public MonopolyBlessedNoticeInfo parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<MonopolyBlessedNoticeInfo> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<MonopolyBlessedNoticeInfo> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.MonopolyBlessedNoticeInfo getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

