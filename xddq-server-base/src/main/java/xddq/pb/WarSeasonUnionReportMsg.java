// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.WarSeasonUnionReportMsg}
 */
public final class WarSeasonUnionReportMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.WarSeasonUnionReportMsg)
    WarSeasonUnionReportMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      WarSeasonUnionReportMsg.class.getName());
  }
  // Use WarSeasonUnionReportMsg.newBuilder() to construct.
  private WarSeasonUnionReportMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private WarSeasonUnionReportMsg() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_WarSeasonUnionReportMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_WarSeasonUnionReportMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.WarSeasonUnionReportMsg.class, xddq.pb.WarSeasonUnionReportMsg.Builder.class);
  }

  private int bitField0_;
  public static final int CITYID_FIELD_NUMBER = 1;
  private int cityId_ = 0;
  /**
   * <code>optional int32 cityId = 1;</code>
   * @return Whether the cityId field is set.
   */
  @java.lang.Override
  public boolean hasCityId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int32 cityId = 1;</code>
   * @return The cityId.
   */
  @java.lang.Override
  public int getCityId() {
    return cityId_;
  }

  public static final int ISWIN_FIELD_NUMBER = 2;
  private boolean isWin_ = false;
  /**
   * <code>optional bool isWin = 2;</code>
   * @return Whether the isWin field is set.
   */
  @java.lang.Override
  public boolean hasIsWin() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional bool isWin = 2;</code>
   * @return The isWin.
   */
  @java.lang.Override
  public boolean getIsWin() {
    return isWin_;
  }

  public static final int SELFDATA_FIELD_NUMBER = 3;
  private xddq.pb.WarSeasonUnionReportDataMsg selfData_;
  /**
   * <code>optional .xddq.pb.WarSeasonUnionReportDataMsg selfData = 3;</code>
   * @return Whether the selfData field is set.
   */
  @java.lang.Override
  public boolean hasSelfData() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional .xddq.pb.WarSeasonUnionReportDataMsg selfData = 3;</code>
   * @return The selfData.
   */
  @java.lang.Override
  public xddq.pb.WarSeasonUnionReportDataMsg getSelfData() {
    return selfData_ == null ? xddq.pb.WarSeasonUnionReportDataMsg.getDefaultInstance() : selfData_;
  }
  /**
   * <code>optional .xddq.pb.WarSeasonUnionReportDataMsg selfData = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.WarSeasonUnionReportDataMsgOrBuilder getSelfDataOrBuilder() {
    return selfData_ == null ? xddq.pb.WarSeasonUnionReportDataMsg.getDefaultInstance() : selfData_;
  }

  public static final int ENEMYDATA_FIELD_NUMBER = 4;
  private xddq.pb.WarSeasonUnionReportDataMsg enemyData_;
  /**
   * <code>optional .xddq.pb.WarSeasonUnionReportDataMsg enemyData = 4;</code>
   * @return Whether the enemyData field is set.
   */
  @java.lang.Override
  public boolean hasEnemyData() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional .xddq.pb.WarSeasonUnionReportDataMsg enemyData = 4;</code>
   * @return The enemyData.
   */
  @java.lang.Override
  public xddq.pb.WarSeasonUnionReportDataMsg getEnemyData() {
    return enemyData_ == null ? xddq.pb.WarSeasonUnionReportDataMsg.getDefaultInstance() : enemyData_;
  }
  /**
   * <code>optional .xddq.pb.WarSeasonUnionReportDataMsg enemyData = 4;</code>
   */
  @java.lang.Override
  public xddq.pb.WarSeasonUnionReportDataMsgOrBuilder getEnemyDataOrBuilder() {
    return enemyData_ == null ? xddq.pb.WarSeasonUnionReportDataMsg.getDefaultInstance() : enemyData_;
  }

  public static final int TIME_FIELD_NUMBER = 5;
  private long time_ = 0L;
  /**
   * <code>optional int64 time = 5;</code>
   * @return Whether the time field is set.
   */
  @java.lang.Override
  public boolean hasTime() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>optional int64 time = 5;</code>
   * @return The time.
   */
  @java.lang.Override
  public long getTime() {
    return time_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, cityId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeBool(2, isWin_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeMessage(3, getSelfData());
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeMessage(4, getEnemyData());
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      output.writeInt64(5, time_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, cityId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(2, isWin_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, getSelfData());
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(4, getEnemyData());
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(5, time_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.WarSeasonUnionReportMsg)) {
      return super.equals(obj);
    }
    xddq.pb.WarSeasonUnionReportMsg other = (xddq.pb.WarSeasonUnionReportMsg) obj;

    if (hasCityId() != other.hasCityId()) return false;
    if (hasCityId()) {
      if (getCityId()
          != other.getCityId()) return false;
    }
    if (hasIsWin() != other.hasIsWin()) return false;
    if (hasIsWin()) {
      if (getIsWin()
          != other.getIsWin()) return false;
    }
    if (hasSelfData() != other.hasSelfData()) return false;
    if (hasSelfData()) {
      if (!getSelfData()
          .equals(other.getSelfData())) return false;
    }
    if (hasEnemyData() != other.hasEnemyData()) return false;
    if (hasEnemyData()) {
      if (!getEnemyData()
          .equals(other.getEnemyData())) return false;
    }
    if (hasTime() != other.hasTime()) return false;
    if (hasTime()) {
      if (getTime()
          != other.getTime()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasCityId()) {
      hash = (37 * hash) + CITYID_FIELD_NUMBER;
      hash = (53 * hash) + getCityId();
    }
    if (hasIsWin()) {
      hash = (37 * hash) + ISWIN_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getIsWin());
    }
    if (hasSelfData()) {
      hash = (37 * hash) + SELFDATA_FIELD_NUMBER;
      hash = (53 * hash) + getSelfData().hashCode();
    }
    if (hasEnemyData()) {
      hash = (37 * hash) + ENEMYDATA_FIELD_NUMBER;
      hash = (53 * hash) + getEnemyData().hashCode();
    }
    if (hasTime()) {
      hash = (37 * hash) + TIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTime());
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.WarSeasonUnionReportMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.WarSeasonUnionReportMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.WarSeasonUnionReportMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.WarSeasonUnionReportMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.WarSeasonUnionReportMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.WarSeasonUnionReportMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.WarSeasonUnionReportMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.WarSeasonUnionReportMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.WarSeasonUnionReportMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.WarSeasonUnionReportMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.WarSeasonUnionReportMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.WarSeasonUnionReportMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.WarSeasonUnionReportMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.WarSeasonUnionReportMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.WarSeasonUnionReportMsg)
      xddq.pb.WarSeasonUnionReportMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_WarSeasonUnionReportMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_WarSeasonUnionReportMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.WarSeasonUnionReportMsg.class, xddq.pb.WarSeasonUnionReportMsg.Builder.class);
    }

    // Construct using xddq.pb.WarSeasonUnionReportMsg.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetSelfDataFieldBuilder();
        internalGetEnemyDataFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      cityId_ = 0;
      isWin_ = false;
      selfData_ = null;
      if (selfDataBuilder_ != null) {
        selfDataBuilder_.dispose();
        selfDataBuilder_ = null;
      }
      enemyData_ = null;
      if (enemyDataBuilder_ != null) {
        enemyDataBuilder_.dispose();
        enemyDataBuilder_ = null;
      }
      time_ = 0L;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_WarSeasonUnionReportMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.WarSeasonUnionReportMsg getDefaultInstanceForType() {
      return xddq.pb.WarSeasonUnionReportMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.WarSeasonUnionReportMsg build() {
      xddq.pb.WarSeasonUnionReportMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.WarSeasonUnionReportMsg buildPartial() {
      xddq.pb.WarSeasonUnionReportMsg result = new xddq.pb.WarSeasonUnionReportMsg(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.WarSeasonUnionReportMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.cityId_ = cityId_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.isWin_ = isWin_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.selfData_ = selfDataBuilder_ == null
            ? selfData_
            : selfDataBuilder_.build();
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.enemyData_ = enemyDataBuilder_ == null
            ? enemyData_
            : enemyDataBuilder_.build();
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.time_ = time_;
        to_bitField0_ |= 0x00000010;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.WarSeasonUnionReportMsg) {
        return mergeFrom((xddq.pb.WarSeasonUnionReportMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.WarSeasonUnionReportMsg other) {
      if (other == xddq.pb.WarSeasonUnionReportMsg.getDefaultInstance()) return this;
      if (other.hasCityId()) {
        setCityId(other.getCityId());
      }
      if (other.hasIsWin()) {
        setIsWin(other.getIsWin());
      }
      if (other.hasSelfData()) {
        mergeSelfData(other.getSelfData());
      }
      if (other.hasEnemyData()) {
        mergeEnemyData(other.getEnemyData());
      }
      if (other.hasTime()) {
        setTime(other.getTime());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              cityId_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              isWin_ = input.readBool();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 26: {
              input.readMessage(
                  internalGetSelfDataFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 34: {
              input.readMessage(
                  internalGetEnemyDataFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000008;
              break;
            } // case 34
            case 40: {
              time_ = input.readInt64();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int cityId_ ;
    /**
     * <code>optional int32 cityId = 1;</code>
     * @return Whether the cityId field is set.
     */
    @java.lang.Override
    public boolean hasCityId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 cityId = 1;</code>
     * @return The cityId.
     */
    @java.lang.Override
    public int getCityId() {
      return cityId_;
    }
    /**
     * <code>optional int32 cityId = 1;</code>
     * @param value The cityId to set.
     * @return This builder for chaining.
     */
    public Builder setCityId(int value) {

      cityId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 cityId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearCityId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      cityId_ = 0;
      onChanged();
      return this;
    }

    private boolean isWin_ ;
    /**
     * <code>optional bool isWin = 2;</code>
     * @return Whether the isWin field is set.
     */
    @java.lang.Override
    public boolean hasIsWin() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional bool isWin = 2;</code>
     * @return The isWin.
     */
    @java.lang.Override
    public boolean getIsWin() {
      return isWin_;
    }
    /**
     * <code>optional bool isWin = 2;</code>
     * @param value The isWin to set.
     * @return This builder for chaining.
     */
    public Builder setIsWin(boolean value) {

      isWin_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional bool isWin = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearIsWin() {
      bitField0_ = (bitField0_ & ~0x00000002);
      isWin_ = false;
      onChanged();
      return this;
    }

    private xddq.pb.WarSeasonUnionReportDataMsg selfData_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.WarSeasonUnionReportDataMsg, xddq.pb.WarSeasonUnionReportDataMsg.Builder, xddq.pb.WarSeasonUnionReportDataMsgOrBuilder> selfDataBuilder_;
    /**
     * <code>optional .xddq.pb.WarSeasonUnionReportDataMsg selfData = 3;</code>
     * @return Whether the selfData field is set.
     */
    public boolean hasSelfData() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional .xddq.pb.WarSeasonUnionReportDataMsg selfData = 3;</code>
     * @return The selfData.
     */
    public xddq.pb.WarSeasonUnionReportDataMsg getSelfData() {
      if (selfDataBuilder_ == null) {
        return selfData_ == null ? xddq.pb.WarSeasonUnionReportDataMsg.getDefaultInstance() : selfData_;
      } else {
        return selfDataBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.WarSeasonUnionReportDataMsg selfData = 3;</code>
     */
    public Builder setSelfData(xddq.pb.WarSeasonUnionReportDataMsg value) {
      if (selfDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        selfData_ = value;
      } else {
        selfDataBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.WarSeasonUnionReportDataMsg selfData = 3;</code>
     */
    public Builder setSelfData(
        xddq.pb.WarSeasonUnionReportDataMsg.Builder builderForValue) {
      if (selfDataBuilder_ == null) {
        selfData_ = builderForValue.build();
      } else {
        selfDataBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.WarSeasonUnionReportDataMsg selfData = 3;</code>
     */
    public Builder mergeSelfData(xddq.pb.WarSeasonUnionReportDataMsg value) {
      if (selfDataBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0) &&
          selfData_ != null &&
          selfData_ != xddq.pb.WarSeasonUnionReportDataMsg.getDefaultInstance()) {
          getSelfDataBuilder().mergeFrom(value);
        } else {
          selfData_ = value;
        }
      } else {
        selfDataBuilder_.mergeFrom(value);
      }
      if (selfData_ != null) {
        bitField0_ |= 0x00000004;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.WarSeasonUnionReportDataMsg selfData = 3;</code>
     */
    public Builder clearSelfData() {
      bitField0_ = (bitField0_ & ~0x00000004);
      selfData_ = null;
      if (selfDataBuilder_ != null) {
        selfDataBuilder_.dispose();
        selfDataBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.WarSeasonUnionReportDataMsg selfData = 3;</code>
     */
    public xddq.pb.WarSeasonUnionReportDataMsg.Builder getSelfDataBuilder() {
      bitField0_ |= 0x00000004;
      onChanged();
      return internalGetSelfDataFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.WarSeasonUnionReportDataMsg selfData = 3;</code>
     */
    public xddq.pb.WarSeasonUnionReportDataMsgOrBuilder getSelfDataOrBuilder() {
      if (selfDataBuilder_ != null) {
        return selfDataBuilder_.getMessageOrBuilder();
      } else {
        return selfData_ == null ?
            xddq.pb.WarSeasonUnionReportDataMsg.getDefaultInstance() : selfData_;
      }
    }
    /**
     * <code>optional .xddq.pb.WarSeasonUnionReportDataMsg selfData = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.WarSeasonUnionReportDataMsg, xddq.pb.WarSeasonUnionReportDataMsg.Builder, xddq.pb.WarSeasonUnionReportDataMsgOrBuilder> 
        internalGetSelfDataFieldBuilder() {
      if (selfDataBuilder_ == null) {
        selfDataBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.WarSeasonUnionReportDataMsg, xddq.pb.WarSeasonUnionReportDataMsg.Builder, xddq.pb.WarSeasonUnionReportDataMsgOrBuilder>(
                getSelfData(),
                getParentForChildren(),
                isClean());
        selfData_ = null;
      }
      return selfDataBuilder_;
    }

    private xddq.pb.WarSeasonUnionReportDataMsg enemyData_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.WarSeasonUnionReportDataMsg, xddq.pb.WarSeasonUnionReportDataMsg.Builder, xddq.pb.WarSeasonUnionReportDataMsgOrBuilder> enemyDataBuilder_;
    /**
     * <code>optional .xddq.pb.WarSeasonUnionReportDataMsg enemyData = 4;</code>
     * @return Whether the enemyData field is set.
     */
    public boolean hasEnemyData() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional .xddq.pb.WarSeasonUnionReportDataMsg enemyData = 4;</code>
     * @return The enemyData.
     */
    public xddq.pb.WarSeasonUnionReportDataMsg getEnemyData() {
      if (enemyDataBuilder_ == null) {
        return enemyData_ == null ? xddq.pb.WarSeasonUnionReportDataMsg.getDefaultInstance() : enemyData_;
      } else {
        return enemyDataBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.WarSeasonUnionReportDataMsg enemyData = 4;</code>
     */
    public Builder setEnemyData(xddq.pb.WarSeasonUnionReportDataMsg value) {
      if (enemyDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        enemyData_ = value;
      } else {
        enemyDataBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.WarSeasonUnionReportDataMsg enemyData = 4;</code>
     */
    public Builder setEnemyData(
        xddq.pb.WarSeasonUnionReportDataMsg.Builder builderForValue) {
      if (enemyDataBuilder_ == null) {
        enemyData_ = builderForValue.build();
      } else {
        enemyDataBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.WarSeasonUnionReportDataMsg enemyData = 4;</code>
     */
    public Builder mergeEnemyData(xddq.pb.WarSeasonUnionReportDataMsg value) {
      if (enemyDataBuilder_ == null) {
        if (((bitField0_ & 0x00000008) != 0) &&
          enemyData_ != null &&
          enemyData_ != xddq.pb.WarSeasonUnionReportDataMsg.getDefaultInstance()) {
          getEnemyDataBuilder().mergeFrom(value);
        } else {
          enemyData_ = value;
        }
      } else {
        enemyDataBuilder_.mergeFrom(value);
      }
      if (enemyData_ != null) {
        bitField0_ |= 0x00000008;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.WarSeasonUnionReportDataMsg enemyData = 4;</code>
     */
    public Builder clearEnemyData() {
      bitField0_ = (bitField0_ & ~0x00000008);
      enemyData_ = null;
      if (enemyDataBuilder_ != null) {
        enemyDataBuilder_.dispose();
        enemyDataBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.WarSeasonUnionReportDataMsg enemyData = 4;</code>
     */
    public xddq.pb.WarSeasonUnionReportDataMsg.Builder getEnemyDataBuilder() {
      bitField0_ |= 0x00000008;
      onChanged();
      return internalGetEnemyDataFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.WarSeasonUnionReportDataMsg enemyData = 4;</code>
     */
    public xddq.pb.WarSeasonUnionReportDataMsgOrBuilder getEnemyDataOrBuilder() {
      if (enemyDataBuilder_ != null) {
        return enemyDataBuilder_.getMessageOrBuilder();
      } else {
        return enemyData_ == null ?
            xddq.pb.WarSeasonUnionReportDataMsg.getDefaultInstance() : enemyData_;
      }
    }
    /**
     * <code>optional .xddq.pb.WarSeasonUnionReportDataMsg enemyData = 4;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.WarSeasonUnionReportDataMsg, xddq.pb.WarSeasonUnionReportDataMsg.Builder, xddq.pb.WarSeasonUnionReportDataMsgOrBuilder> 
        internalGetEnemyDataFieldBuilder() {
      if (enemyDataBuilder_ == null) {
        enemyDataBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.WarSeasonUnionReportDataMsg, xddq.pb.WarSeasonUnionReportDataMsg.Builder, xddq.pb.WarSeasonUnionReportDataMsgOrBuilder>(
                getEnemyData(),
                getParentForChildren(),
                isClean());
        enemyData_ = null;
      }
      return enemyDataBuilder_;
    }

    private long time_ ;
    /**
     * <code>optional int64 time = 5;</code>
     * @return Whether the time field is set.
     */
    @java.lang.Override
    public boolean hasTime() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional int64 time = 5;</code>
     * @return The time.
     */
    @java.lang.Override
    public long getTime() {
      return time_;
    }
    /**
     * <code>optional int64 time = 5;</code>
     * @param value The time to set.
     * @return This builder for chaining.
     */
    public Builder setTime(long value) {

      time_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 time = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearTime() {
      bitField0_ = (bitField0_ & ~0x00000010);
      time_ = 0L;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.WarSeasonUnionReportMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.WarSeasonUnionReportMsg)
  private static final xddq.pb.WarSeasonUnionReportMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.WarSeasonUnionReportMsg();
  }

  public static xddq.pb.WarSeasonUnionReportMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<WarSeasonUnionReportMsg>
      PARSER = new com.google.protobuf.AbstractParser<WarSeasonUnionReportMsg>() {
    @java.lang.Override
    public WarSeasonUnionReportMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<WarSeasonUnionReportMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<WarSeasonUnionReportMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.WarSeasonUnionReportMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

