// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.RoadDefendUseItemResp}
 */
public final class RoadDefendUseItemResp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.RoadDefendUseItemResp)
    RoadDefendUseItemRespOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      RoadDefendUseItemResp.class.getName());
  }
  // Use RoadDefendUseItemResp.newBuilder() to construct.
  private RoadDefendUseItemResp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private RoadDefendUseItemResp() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_RoadDefendUseItemResp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_RoadDefendUseItemResp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.RoadDefendUseItemResp.class, xddq.pb.RoadDefendUseItemResp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int PLAYERDATA_FIELD_NUMBER = 2;
  private xddq.pb.RoadDefendPlayerData playerData_;
  /**
   * <code>optional .xddq.pb.RoadDefendPlayerData playerData = 2;</code>
   * @return Whether the playerData field is set.
   */
  @java.lang.Override
  public boolean hasPlayerData() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional .xddq.pb.RoadDefendPlayerData playerData = 2;</code>
   * @return The playerData.
   */
  @java.lang.Override
  public xddq.pb.RoadDefendPlayerData getPlayerData() {
    return playerData_ == null ? xddq.pb.RoadDefendPlayerData.getDefaultInstance() : playerData_;
  }
  /**
   * <code>optional .xddq.pb.RoadDefendPlayerData playerData = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.RoadDefendPlayerDataOrBuilder getPlayerDataOrBuilder() {
    return playerData_ == null ? xddq.pb.RoadDefendPlayerData.getDefaultInstance() : playerData_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (hasPlayerData()) {
      if (!getPlayerData().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeMessage(2, getPlayerData());
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getPlayerData());
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.RoadDefendUseItemResp)) {
      return super.equals(obj);
    }
    xddq.pb.RoadDefendUseItemResp other = (xddq.pb.RoadDefendUseItemResp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (hasPlayerData() != other.hasPlayerData()) return false;
    if (hasPlayerData()) {
      if (!getPlayerData()
          .equals(other.getPlayerData())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (hasPlayerData()) {
      hash = (37 * hash) + PLAYERDATA_FIELD_NUMBER;
      hash = (53 * hash) + getPlayerData().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.RoadDefendUseItemResp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.RoadDefendUseItemResp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.RoadDefendUseItemResp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.RoadDefendUseItemResp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.RoadDefendUseItemResp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.RoadDefendUseItemResp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.RoadDefendUseItemResp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.RoadDefendUseItemResp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.RoadDefendUseItemResp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.RoadDefendUseItemResp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.RoadDefendUseItemResp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.RoadDefendUseItemResp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.RoadDefendUseItemResp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.RoadDefendUseItemResp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.RoadDefendUseItemResp)
      xddq.pb.RoadDefendUseItemRespOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_RoadDefendUseItemResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_RoadDefendUseItemResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.RoadDefendUseItemResp.class, xddq.pb.RoadDefendUseItemResp.Builder.class);
    }

    // Construct using xddq.pb.RoadDefendUseItemResp.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetPlayerDataFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      playerData_ = null;
      if (playerDataBuilder_ != null) {
        playerDataBuilder_.dispose();
        playerDataBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_RoadDefendUseItemResp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.RoadDefendUseItemResp getDefaultInstanceForType() {
      return xddq.pb.RoadDefendUseItemResp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.RoadDefendUseItemResp build() {
      xddq.pb.RoadDefendUseItemResp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.RoadDefendUseItemResp buildPartial() {
      xddq.pb.RoadDefendUseItemResp result = new xddq.pb.RoadDefendUseItemResp(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.RoadDefendUseItemResp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.playerData_ = playerDataBuilder_ == null
            ? playerData_
            : playerDataBuilder_.build();
        to_bitField0_ |= 0x00000002;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.RoadDefendUseItemResp) {
        return mergeFrom((xddq.pb.RoadDefendUseItemResp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.RoadDefendUseItemResp other) {
      if (other == xddq.pb.RoadDefendUseItemResp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (other.hasPlayerData()) {
        mergePlayerData(other.getPlayerData());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      if (hasPlayerData()) {
        if (!getPlayerData().isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              input.readMessage(
                  internalGetPlayerDataFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private xddq.pb.RoadDefendPlayerData playerData_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.RoadDefendPlayerData, xddq.pb.RoadDefendPlayerData.Builder, xddq.pb.RoadDefendPlayerDataOrBuilder> playerDataBuilder_;
    /**
     * <code>optional .xddq.pb.RoadDefendPlayerData playerData = 2;</code>
     * @return Whether the playerData field is set.
     */
    public boolean hasPlayerData() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .xddq.pb.RoadDefendPlayerData playerData = 2;</code>
     * @return The playerData.
     */
    public xddq.pb.RoadDefendPlayerData getPlayerData() {
      if (playerDataBuilder_ == null) {
        return playerData_ == null ? xddq.pb.RoadDefendPlayerData.getDefaultInstance() : playerData_;
      } else {
        return playerDataBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.RoadDefendPlayerData playerData = 2;</code>
     */
    public Builder setPlayerData(xddq.pb.RoadDefendPlayerData value) {
      if (playerDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        playerData_ = value;
      } else {
        playerDataBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.RoadDefendPlayerData playerData = 2;</code>
     */
    public Builder setPlayerData(
        xddq.pb.RoadDefendPlayerData.Builder builderForValue) {
      if (playerDataBuilder_ == null) {
        playerData_ = builderForValue.build();
      } else {
        playerDataBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.RoadDefendPlayerData playerData = 2;</code>
     */
    public Builder mergePlayerData(xddq.pb.RoadDefendPlayerData value) {
      if (playerDataBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0) &&
          playerData_ != null &&
          playerData_ != xddq.pb.RoadDefendPlayerData.getDefaultInstance()) {
          getPlayerDataBuilder().mergeFrom(value);
        } else {
          playerData_ = value;
        }
      } else {
        playerDataBuilder_.mergeFrom(value);
      }
      if (playerData_ != null) {
        bitField0_ |= 0x00000002;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.RoadDefendPlayerData playerData = 2;</code>
     */
    public Builder clearPlayerData() {
      bitField0_ = (bitField0_ & ~0x00000002);
      playerData_ = null;
      if (playerDataBuilder_ != null) {
        playerDataBuilder_.dispose();
        playerDataBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.RoadDefendPlayerData playerData = 2;</code>
     */
    public xddq.pb.RoadDefendPlayerData.Builder getPlayerDataBuilder() {
      bitField0_ |= 0x00000002;
      onChanged();
      return internalGetPlayerDataFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.RoadDefendPlayerData playerData = 2;</code>
     */
    public xddq.pb.RoadDefendPlayerDataOrBuilder getPlayerDataOrBuilder() {
      if (playerDataBuilder_ != null) {
        return playerDataBuilder_.getMessageOrBuilder();
      } else {
        return playerData_ == null ?
            xddq.pb.RoadDefendPlayerData.getDefaultInstance() : playerData_;
      }
    }
    /**
     * <code>optional .xddq.pb.RoadDefendPlayerData playerData = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.RoadDefendPlayerData, xddq.pb.RoadDefendPlayerData.Builder, xddq.pb.RoadDefendPlayerDataOrBuilder> 
        internalGetPlayerDataFieldBuilder() {
      if (playerDataBuilder_ == null) {
        playerDataBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.RoadDefendPlayerData, xddq.pb.RoadDefendPlayerData.Builder, xddq.pb.RoadDefendPlayerDataOrBuilder>(
                getPlayerData(),
                getParentForChildren(),
                isClean());
        playerData_ = null;
      }
      return playerDataBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.RoadDefendUseItemResp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.RoadDefendUseItemResp)
  private static final xddq.pb.RoadDefendUseItemResp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.RoadDefendUseItemResp();
  }

  public static xddq.pb.RoadDefendUseItemResp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<RoadDefendUseItemResp>
      PARSER = new com.google.protobuf.AbstractParser<RoadDefendUseItemResp>() {
    @java.lang.Override
    public RoadDefendUseItemResp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<RoadDefendUseItemResp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<RoadDefendUseItemResp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.RoadDefendUseItemResp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

