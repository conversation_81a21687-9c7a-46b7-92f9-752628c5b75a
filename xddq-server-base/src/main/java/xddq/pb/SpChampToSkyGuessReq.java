// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.SpChampToSkyGuessReq}
 */
public final class SpChampToSkyGuessReq extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.SpChampToSkyGuessReq)
    SpChampToSkyGuessReqOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      SpChampToSkyGuessReq.class.getName());
  }
  // Use SpChampToSkyGuessReq.newBuilder() to construct.
  private SpChampToSkyGuessReq(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private SpChampToSkyGuessReq() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_SpChampToSkyGuessReq_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_SpChampToSkyGuessReq_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.SpChampToSkyGuessReq.class, xddq.pb.SpChampToSkyGuessReq.Builder.class);
  }

  private int bitField0_;
  public static final int ACTIVITYID_FIELD_NUMBER = 1;
  private int activityId_ = 0;
  /**
   * <code>required int32 activityId = 1;</code>
   * @return Whether the activityId field is set.
   */
  @java.lang.Override
  public boolean hasActivityId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 activityId = 1;</code>
   * @return The activityId.
   */
  @java.lang.Override
  public int getActivityId() {
    return activityId_;
  }

  public static final int PLAYERID_FIELD_NUMBER = 2;
  private long playerId_ = 0L;
  /**
   * <code>required int64 playerId = 2;</code>
   * @return Whether the playerId field is set.
   */
  @java.lang.Override
  public boolean hasPlayerId() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>required int64 playerId = 2;</code>
   * @return The playerId.
   */
  @java.lang.Override
  public long getPlayerId() {
    return playerId_;
  }

  public static final int GUESSCOINCOST_FIELD_NUMBER = 3;
  private int guessCoinCost_ = 0;
  /**
   * <code>required int32 guessCoinCost = 3;</code>
   * @return Whether the guessCoinCost field is set.
   */
  @java.lang.Override
  public boolean hasGuessCoinCost() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>required int32 guessCoinCost = 3;</code>
   * @return The guessCoinCost.
   */
  @java.lang.Override
  public int getGuessCoinCost() {
    return guessCoinCost_;
  }

  public static final int ROUNDID_FIELD_NUMBER = 4;
  private int roundId_ = 0;
  /**
   * <code>required int32 roundId = 4;</code>
   * @return Whether the roundId field is set.
   */
  @java.lang.Override
  public boolean hasRoundId() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>required int32 roundId = 4;</code>
   * @return The roundId.
   */
  @java.lang.Override
  public int getRoundId() {
    return roundId_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasActivityId()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasPlayerId()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasGuessCoinCost()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasRoundId()) {
      memoizedIsInitialized = 0;
      return false;
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, activityId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt64(2, playerId_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt32(3, guessCoinCost_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt32(4, roundId_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, activityId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(2, playerId_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(3, guessCoinCost_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(4, roundId_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.SpChampToSkyGuessReq)) {
      return super.equals(obj);
    }
    xddq.pb.SpChampToSkyGuessReq other = (xddq.pb.SpChampToSkyGuessReq) obj;

    if (hasActivityId() != other.hasActivityId()) return false;
    if (hasActivityId()) {
      if (getActivityId()
          != other.getActivityId()) return false;
    }
    if (hasPlayerId() != other.hasPlayerId()) return false;
    if (hasPlayerId()) {
      if (getPlayerId()
          != other.getPlayerId()) return false;
    }
    if (hasGuessCoinCost() != other.hasGuessCoinCost()) return false;
    if (hasGuessCoinCost()) {
      if (getGuessCoinCost()
          != other.getGuessCoinCost()) return false;
    }
    if (hasRoundId() != other.hasRoundId()) return false;
    if (hasRoundId()) {
      if (getRoundId()
          != other.getRoundId()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasActivityId()) {
      hash = (37 * hash) + ACTIVITYID_FIELD_NUMBER;
      hash = (53 * hash) + getActivityId();
    }
    if (hasPlayerId()) {
      hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getPlayerId());
    }
    if (hasGuessCoinCost()) {
      hash = (37 * hash) + GUESSCOINCOST_FIELD_NUMBER;
      hash = (53 * hash) + getGuessCoinCost();
    }
    if (hasRoundId()) {
      hash = (37 * hash) + ROUNDID_FIELD_NUMBER;
      hash = (53 * hash) + getRoundId();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.SpChampToSkyGuessReq parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.SpChampToSkyGuessReq parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.SpChampToSkyGuessReq parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.SpChampToSkyGuessReq parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.SpChampToSkyGuessReq parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.SpChampToSkyGuessReq parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.SpChampToSkyGuessReq parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.SpChampToSkyGuessReq parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.SpChampToSkyGuessReq parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.SpChampToSkyGuessReq parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.SpChampToSkyGuessReq parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.SpChampToSkyGuessReq parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.SpChampToSkyGuessReq prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.SpChampToSkyGuessReq}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.SpChampToSkyGuessReq)
      xddq.pb.SpChampToSkyGuessReqOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_SpChampToSkyGuessReq_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_SpChampToSkyGuessReq_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.SpChampToSkyGuessReq.class, xddq.pb.SpChampToSkyGuessReq.Builder.class);
    }

    // Construct using xddq.pb.SpChampToSkyGuessReq.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      activityId_ = 0;
      playerId_ = 0L;
      guessCoinCost_ = 0;
      roundId_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_SpChampToSkyGuessReq_descriptor;
    }

    @java.lang.Override
    public xddq.pb.SpChampToSkyGuessReq getDefaultInstanceForType() {
      return xddq.pb.SpChampToSkyGuessReq.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.SpChampToSkyGuessReq build() {
      xddq.pb.SpChampToSkyGuessReq result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.SpChampToSkyGuessReq buildPartial() {
      xddq.pb.SpChampToSkyGuessReq result = new xddq.pb.SpChampToSkyGuessReq(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.SpChampToSkyGuessReq result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.activityId_ = activityId_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.playerId_ = playerId_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.guessCoinCost_ = guessCoinCost_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.roundId_ = roundId_;
        to_bitField0_ |= 0x00000008;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.SpChampToSkyGuessReq) {
        return mergeFrom((xddq.pb.SpChampToSkyGuessReq)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.SpChampToSkyGuessReq other) {
      if (other == xddq.pb.SpChampToSkyGuessReq.getDefaultInstance()) return this;
      if (other.hasActivityId()) {
        setActivityId(other.getActivityId());
      }
      if (other.hasPlayerId()) {
        setPlayerId(other.getPlayerId());
      }
      if (other.hasGuessCoinCost()) {
        setGuessCoinCost(other.getGuessCoinCost());
      }
      if (other.hasRoundId()) {
        setRoundId(other.getRoundId());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasActivityId()) {
        return false;
      }
      if (!hasPlayerId()) {
        return false;
      }
      if (!hasGuessCoinCost()) {
        return false;
      }
      if (!hasRoundId()) {
        return false;
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              activityId_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              playerId_ = input.readInt64();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 24: {
              guessCoinCost_ = input.readInt32();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 32: {
              roundId_ = input.readInt32();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int activityId_ ;
    /**
     * <code>required int32 activityId = 1;</code>
     * @return Whether the activityId field is set.
     */
    @java.lang.Override
    public boolean hasActivityId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 activityId = 1;</code>
     * @return The activityId.
     */
    @java.lang.Override
    public int getActivityId() {
      return activityId_;
    }
    /**
     * <code>required int32 activityId = 1;</code>
     * @param value The activityId to set.
     * @return This builder for chaining.
     */
    public Builder setActivityId(int value) {

      activityId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 activityId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearActivityId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      activityId_ = 0;
      onChanged();
      return this;
    }

    private long playerId_ ;
    /**
     * <code>required int64 playerId = 2;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>required int64 playerId = 2;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }
    /**
     * <code>required int64 playerId = 2;</code>
     * @param value The playerId to set.
     * @return This builder for chaining.
     */
    public Builder setPlayerId(long value) {

      playerId_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>required int64 playerId = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearPlayerId() {
      bitField0_ = (bitField0_ & ~0x00000002);
      playerId_ = 0L;
      onChanged();
      return this;
    }

    private int guessCoinCost_ ;
    /**
     * <code>required int32 guessCoinCost = 3;</code>
     * @return Whether the guessCoinCost field is set.
     */
    @java.lang.Override
    public boolean hasGuessCoinCost() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>required int32 guessCoinCost = 3;</code>
     * @return The guessCoinCost.
     */
    @java.lang.Override
    public int getGuessCoinCost() {
      return guessCoinCost_;
    }
    /**
     * <code>required int32 guessCoinCost = 3;</code>
     * @param value The guessCoinCost to set.
     * @return This builder for chaining.
     */
    public Builder setGuessCoinCost(int value) {

      guessCoinCost_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 guessCoinCost = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearGuessCoinCost() {
      bitField0_ = (bitField0_ & ~0x00000004);
      guessCoinCost_ = 0;
      onChanged();
      return this;
    }

    private int roundId_ ;
    /**
     * <code>required int32 roundId = 4;</code>
     * @return Whether the roundId field is set.
     */
    @java.lang.Override
    public boolean hasRoundId() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>required int32 roundId = 4;</code>
     * @return The roundId.
     */
    @java.lang.Override
    public int getRoundId() {
      return roundId_;
    }
    /**
     * <code>required int32 roundId = 4;</code>
     * @param value The roundId to set.
     * @return This builder for chaining.
     */
    public Builder setRoundId(int value) {

      roundId_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 roundId = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearRoundId() {
      bitField0_ = (bitField0_ & ~0x00000008);
      roundId_ = 0;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.SpChampToSkyGuessReq)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.SpChampToSkyGuessReq)
  private static final xddq.pb.SpChampToSkyGuessReq DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.SpChampToSkyGuessReq();
  }

  public static xddq.pb.SpChampToSkyGuessReq getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<SpChampToSkyGuessReq>
      PARSER = new com.google.protobuf.AbstractParser<SpChampToSkyGuessReq>() {
    @java.lang.Override
    public SpChampToSkyGuessReq parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<SpChampToSkyGuessReq> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<SpChampToSkyGuessReq> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.SpChampToSkyGuessReq getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

