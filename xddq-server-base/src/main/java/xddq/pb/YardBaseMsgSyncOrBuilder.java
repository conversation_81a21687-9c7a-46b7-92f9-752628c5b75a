// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

public interface YardBaseMsgSyncOrBuilder extends
    // @@protoc_insertion_point(interface_extends:xddq.pb.YardBaseMsgSync)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>optional string exp = 1;</code>
   * @return Whether the exp field is set.
   */
  boolean hasExp();
  /**
   * <code>optional string exp = 1;</code>
   * @return The exp.
   */
  java.lang.String getExp();
  /**
   * <code>optional string exp = 1;</code>
   * @return The bytes for exp.
   */
  com.google.protobuf.ByteString
      getExpBytes();

  /**
   * <code>optional int32 level = 2;</code>
   * @return Whether the level field is set.
   */
  boolean hasLevel();
  /**
   * <code>optional int32 level = 2;</code>
   * @return The level.
   */
  int getLevel();
}
