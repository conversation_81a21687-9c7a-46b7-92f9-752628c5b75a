// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

public interface WarSeasonGiftSelectGridMsgOrBuilder extends
    // @@protoc_insertion_point(interface_extends:xddq.pb.WarSeasonGiftSelectGridMsg)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>optional int32 index = 1;</code>
   * @return Whether the index field is set.
   */
  boolean hasIndex();
  /**
   * <code>optional int32 index = 1;</code>
   * @return The index.
   */
  int getIndex();

  /**
   * <code>optional int32 selectIndex = 2;</code>
   * @return Whether the selectIndex field is set.
   */
  boolean hasSelectIndex();
  /**
   * <code>optional int32 selectIndex = 2;</code>
   * @return The selectIndex.
   */
  int getSelectIndex();
}
