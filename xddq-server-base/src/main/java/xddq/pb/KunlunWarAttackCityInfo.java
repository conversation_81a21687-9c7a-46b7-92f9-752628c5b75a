// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.KunlunWarAttackCityInfo}
 */
public final class KunlunWarAttackCityInfo extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.KunlunWarAttackCityInfo)
    KunlunWarAttackCityInfoOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      KunlunWarAttackCityInfo.class.getName());
  }
  // Use KunlunWarAttackCityInfo.newBuilder() to construct.
  private KunlunWarAttackCityInfo(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private KunlunWarAttackCityInfo() {
    playerIds_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_KunlunWarAttackCityInfo_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_KunlunWarAttackCityInfo_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.KunlunWarAttackCityInfo.class, xddq.pb.KunlunWarAttackCityInfo.Builder.class);
  }

  private int bitField0_;
  public static final int CITYID_FIELD_NUMBER = 1;
  private int cityId_ = 0;
  /**
   * <code>optional int32 cityId = 1;</code>
   * @return Whether the cityId field is set.
   */
  @java.lang.Override
  public boolean hasCityId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int32 cityId = 1;</code>
   * @return The cityId.
   */
  @java.lang.Override
  public int getCityId() {
    return cityId_;
  }

  public static final int CURHP_FIELD_NUMBER = 2;
  private int curHp_ = 0;
  /**
   * <code>optional int32 curHp = 2;</code>
   * @return Whether the curHp field is set.
   */
  @java.lang.Override
  public boolean hasCurHp() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int32 curHp = 2;</code>
   * @return The curHp.
   */
  @java.lang.Override
  public int getCurHp() {
    return curHp_;
  }

  public static final int PLAYERIDS_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.KunlunWarPlayerDisplayInfo> playerIds_;
  /**
   * <code>repeated .xddq.pb.KunlunWarPlayerDisplayInfo playerIds = 3;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.KunlunWarPlayerDisplayInfo> getPlayerIdsList() {
    return playerIds_;
  }
  /**
   * <code>repeated .xddq.pb.KunlunWarPlayerDisplayInfo playerIds = 3;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.KunlunWarPlayerDisplayInfoOrBuilder> 
      getPlayerIdsOrBuilderList() {
    return playerIds_;
  }
  /**
   * <code>repeated .xddq.pb.KunlunWarPlayerDisplayInfo playerIds = 3;</code>
   */
  @java.lang.Override
  public int getPlayerIdsCount() {
    return playerIds_.size();
  }
  /**
   * <code>repeated .xddq.pb.KunlunWarPlayerDisplayInfo playerIds = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.KunlunWarPlayerDisplayInfo getPlayerIds(int index) {
    return playerIds_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.KunlunWarPlayerDisplayInfo playerIds = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.KunlunWarPlayerDisplayInfoOrBuilder getPlayerIdsOrBuilder(
      int index) {
    return playerIds_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, cityId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(2, curHp_);
    }
    for (int i = 0; i < playerIds_.size(); i++) {
      output.writeMessage(3, playerIds_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, cityId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, curHp_);
    }
    for (int i = 0; i < playerIds_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, playerIds_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.KunlunWarAttackCityInfo)) {
      return super.equals(obj);
    }
    xddq.pb.KunlunWarAttackCityInfo other = (xddq.pb.KunlunWarAttackCityInfo) obj;

    if (hasCityId() != other.hasCityId()) return false;
    if (hasCityId()) {
      if (getCityId()
          != other.getCityId()) return false;
    }
    if (hasCurHp() != other.hasCurHp()) return false;
    if (hasCurHp()) {
      if (getCurHp()
          != other.getCurHp()) return false;
    }
    if (!getPlayerIdsList()
        .equals(other.getPlayerIdsList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasCityId()) {
      hash = (37 * hash) + CITYID_FIELD_NUMBER;
      hash = (53 * hash) + getCityId();
    }
    if (hasCurHp()) {
      hash = (37 * hash) + CURHP_FIELD_NUMBER;
      hash = (53 * hash) + getCurHp();
    }
    if (getPlayerIdsCount() > 0) {
      hash = (37 * hash) + PLAYERIDS_FIELD_NUMBER;
      hash = (53 * hash) + getPlayerIdsList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.KunlunWarAttackCityInfo parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.KunlunWarAttackCityInfo parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.KunlunWarAttackCityInfo parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.KunlunWarAttackCityInfo parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.KunlunWarAttackCityInfo parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.KunlunWarAttackCityInfo parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.KunlunWarAttackCityInfo parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.KunlunWarAttackCityInfo parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.KunlunWarAttackCityInfo parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.KunlunWarAttackCityInfo parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.KunlunWarAttackCityInfo parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.KunlunWarAttackCityInfo parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.KunlunWarAttackCityInfo prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.KunlunWarAttackCityInfo}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.KunlunWarAttackCityInfo)
      xddq.pb.KunlunWarAttackCityInfoOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_KunlunWarAttackCityInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_KunlunWarAttackCityInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.KunlunWarAttackCityInfo.class, xddq.pb.KunlunWarAttackCityInfo.Builder.class);
    }

    // Construct using xddq.pb.KunlunWarAttackCityInfo.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      cityId_ = 0;
      curHp_ = 0;
      if (playerIdsBuilder_ == null) {
        playerIds_ = java.util.Collections.emptyList();
      } else {
        playerIds_ = null;
        playerIdsBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000004);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_KunlunWarAttackCityInfo_descriptor;
    }

    @java.lang.Override
    public xddq.pb.KunlunWarAttackCityInfo getDefaultInstanceForType() {
      return xddq.pb.KunlunWarAttackCityInfo.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.KunlunWarAttackCityInfo build() {
      xddq.pb.KunlunWarAttackCityInfo result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.KunlunWarAttackCityInfo buildPartial() {
      xddq.pb.KunlunWarAttackCityInfo result = new xddq.pb.KunlunWarAttackCityInfo(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.KunlunWarAttackCityInfo result) {
      if (playerIdsBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0)) {
          playerIds_ = java.util.Collections.unmodifiableList(playerIds_);
          bitField0_ = (bitField0_ & ~0x00000004);
        }
        result.playerIds_ = playerIds_;
      } else {
        result.playerIds_ = playerIdsBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.KunlunWarAttackCityInfo result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.cityId_ = cityId_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.curHp_ = curHp_;
        to_bitField0_ |= 0x00000002;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.KunlunWarAttackCityInfo) {
        return mergeFrom((xddq.pb.KunlunWarAttackCityInfo)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.KunlunWarAttackCityInfo other) {
      if (other == xddq.pb.KunlunWarAttackCityInfo.getDefaultInstance()) return this;
      if (other.hasCityId()) {
        setCityId(other.getCityId());
      }
      if (other.hasCurHp()) {
        setCurHp(other.getCurHp());
      }
      if (playerIdsBuilder_ == null) {
        if (!other.playerIds_.isEmpty()) {
          if (playerIds_.isEmpty()) {
            playerIds_ = other.playerIds_;
            bitField0_ = (bitField0_ & ~0x00000004);
          } else {
            ensurePlayerIdsIsMutable();
            playerIds_.addAll(other.playerIds_);
          }
          onChanged();
        }
      } else {
        if (!other.playerIds_.isEmpty()) {
          if (playerIdsBuilder_.isEmpty()) {
            playerIdsBuilder_.dispose();
            playerIdsBuilder_ = null;
            playerIds_ = other.playerIds_;
            bitField0_ = (bitField0_ & ~0x00000004);
            playerIdsBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetPlayerIdsFieldBuilder() : null;
          } else {
            playerIdsBuilder_.addAllMessages(other.playerIds_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              cityId_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              curHp_ = input.readInt32();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 26: {
              xddq.pb.KunlunWarPlayerDisplayInfo m =
                  input.readMessage(
                      xddq.pb.KunlunWarPlayerDisplayInfo.parser(),
                      extensionRegistry);
              if (playerIdsBuilder_ == null) {
                ensurePlayerIdsIsMutable();
                playerIds_.add(m);
              } else {
                playerIdsBuilder_.addMessage(m);
              }
              break;
            } // case 26
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int cityId_ ;
    /**
     * <code>optional int32 cityId = 1;</code>
     * @return Whether the cityId field is set.
     */
    @java.lang.Override
    public boolean hasCityId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 cityId = 1;</code>
     * @return The cityId.
     */
    @java.lang.Override
    public int getCityId() {
      return cityId_;
    }
    /**
     * <code>optional int32 cityId = 1;</code>
     * @param value The cityId to set.
     * @return This builder for chaining.
     */
    public Builder setCityId(int value) {

      cityId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 cityId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearCityId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      cityId_ = 0;
      onChanged();
      return this;
    }

    private int curHp_ ;
    /**
     * <code>optional int32 curHp = 2;</code>
     * @return Whether the curHp field is set.
     */
    @java.lang.Override
    public boolean hasCurHp() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 curHp = 2;</code>
     * @return The curHp.
     */
    @java.lang.Override
    public int getCurHp() {
      return curHp_;
    }
    /**
     * <code>optional int32 curHp = 2;</code>
     * @param value The curHp to set.
     * @return This builder for chaining.
     */
    public Builder setCurHp(int value) {

      curHp_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 curHp = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearCurHp() {
      bitField0_ = (bitField0_ & ~0x00000002);
      curHp_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.KunlunWarPlayerDisplayInfo> playerIds_ =
      java.util.Collections.emptyList();
    private void ensurePlayerIdsIsMutable() {
      if (!((bitField0_ & 0x00000004) != 0)) {
        playerIds_ = new java.util.ArrayList<xddq.pb.KunlunWarPlayerDisplayInfo>(playerIds_);
        bitField0_ |= 0x00000004;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.KunlunWarPlayerDisplayInfo, xddq.pb.KunlunWarPlayerDisplayInfo.Builder, xddq.pb.KunlunWarPlayerDisplayInfoOrBuilder> playerIdsBuilder_;

    /**
     * <code>repeated .xddq.pb.KunlunWarPlayerDisplayInfo playerIds = 3;</code>
     */
    public java.util.List<xddq.pb.KunlunWarPlayerDisplayInfo> getPlayerIdsList() {
      if (playerIdsBuilder_ == null) {
        return java.util.Collections.unmodifiableList(playerIds_);
      } else {
        return playerIdsBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarPlayerDisplayInfo playerIds = 3;</code>
     */
    public int getPlayerIdsCount() {
      if (playerIdsBuilder_ == null) {
        return playerIds_.size();
      } else {
        return playerIdsBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarPlayerDisplayInfo playerIds = 3;</code>
     */
    public xddq.pb.KunlunWarPlayerDisplayInfo getPlayerIds(int index) {
      if (playerIdsBuilder_ == null) {
        return playerIds_.get(index);
      } else {
        return playerIdsBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarPlayerDisplayInfo playerIds = 3;</code>
     */
    public Builder setPlayerIds(
        int index, xddq.pb.KunlunWarPlayerDisplayInfo value) {
      if (playerIdsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePlayerIdsIsMutable();
        playerIds_.set(index, value);
        onChanged();
      } else {
        playerIdsBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarPlayerDisplayInfo playerIds = 3;</code>
     */
    public Builder setPlayerIds(
        int index, xddq.pb.KunlunWarPlayerDisplayInfo.Builder builderForValue) {
      if (playerIdsBuilder_ == null) {
        ensurePlayerIdsIsMutable();
        playerIds_.set(index, builderForValue.build());
        onChanged();
      } else {
        playerIdsBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarPlayerDisplayInfo playerIds = 3;</code>
     */
    public Builder addPlayerIds(xddq.pb.KunlunWarPlayerDisplayInfo value) {
      if (playerIdsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePlayerIdsIsMutable();
        playerIds_.add(value);
        onChanged();
      } else {
        playerIdsBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarPlayerDisplayInfo playerIds = 3;</code>
     */
    public Builder addPlayerIds(
        int index, xddq.pb.KunlunWarPlayerDisplayInfo value) {
      if (playerIdsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePlayerIdsIsMutable();
        playerIds_.add(index, value);
        onChanged();
      } else {
        playerIdsBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarPlayerDisplayInfo playerIds = 3;</code>
     */
    public Builder addPlayerIds(
        xddq.pb.KunlunWarPlayerDisplayInfo.Builder builderForValue) {
      if (playerIdsBuilder_ == null) {
        ensurePlayerIdsIsMutable();
        playerIds_.add(builderForValue.build());
        onChanged();
      } else {
        playerIdsBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarPlayerDisplayInfo playerIds = 3;</code>
     */
    public Builder addPlayerIds(
        int index, xddq.pb.KunlunWarPlayerDisplayInfo.Builder builderForValue) {
      if (playerIdsBuilder_ == null) {
        ensurePlayerIdsIsMutable();
        playerIds_.add(index, builderForValue.build());
        onChanged();
      } else {
        playerIdsBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarPlayerDisplayInfo playerIds = 3;</code>
     */
    public Builder addAllPlayerIds(
        java.lang.Iterable<? extends xddq.pb.KunlunWarPlayerDisplayInfo> values) {
      if (playerIdsBuilder_ == null) {
        ensurePlayerIdsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, playerIds_);
        onChanged();
      } else {
        playerIdsBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarPlayerDisplayInfo playerIds = 3;</code>
     */
    public Builder clearPlayerIds() {
      if (playerIdsBuilder_ == null) {
        playerIds_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
      } else {
        playerIdsBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarPlayerDisplayInfo playerIds = 3;</code>
     */
    public Builder removePlayerIds(int index) {
      if (playerIdsBuilder_ == null) {
        ensurePlayerIdsIsMutable();
        playerIds_.remove(index);
        onChanged();
      } else {
        playerIdsBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarPlayerDisplayInfo playerIds = 3;</code>
     */
    public xddq.pb.KunlunWarPlayerDisplayInfo.Builder getPlayerIdsBuilder(
        int index) {
      return internalGetPlayerIdsFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarPlayerDisplayInfo playerIds = 3;</code>
     */
    public xddq.pb.KunlunWarPlayerDisplayInfoOrBuilder getPlayerIdsOrBuilder(
        int index) {
      if (playerIdsBuilder_ == null) {
        return playerIds_.get(index);  } else {
        return playerIdsBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarPlayerDisplayInfo playerIds = 3;</code>
     */
    public java.util.List<? extends xddq.pb.KunlunWarPlayerDisplayInfoOrBuilder> 
         getPlayerIdsOrBuilderList() {
      if (playerIdsBuilder_ != null) {
        return playerIdsBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(playerIds_);
      }
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarPlayerDisplayInfo playerIds = 3;</code>
     */
    public xddq.pb.KunlunWarPlayerDisplayInfo.Builder addPlayerIdsBuilder() {
      return internalGetPlayerIdsFieldBuilder().addBuilder(
          xddq.pb.KunlunWarPlayerDisplayInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarPlayerDisplayInfo playerIds = 3;</code>
     */
    public xddq.pb.KunlunWarPlayerDisplayInfo.Builder addPlayerIdsBuilder(
        int index) {
      return internalGetPlayerIdsFieldBuilder().addBuilder(
          index, xddq.pb.KunlunWarPlayerDisplayInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarPlayerDisplayInfo playerIds = 3;</code>
     */
    public java.util.List<xddq.pb.KunlunWarPlayerDisplayInfo.Builder> 
         getPlayerIdsBuilderList() {
      return internalGetPlayerIdsFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.KunlunWarPlayerDisplayInfo, xddq.pb.KunlunWarPlayerDisplayInfo.Builder, xddq.pb.KunlunWarPlayerDisplayInfoOrBuilder> 
        internalGetPlayerIdsFieldBuilder() {
      if (playerIdsBuilder_ == null) {
        playerIdsBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.KunlunWarPlayerDisplayInfo, xddq.pb.KunlunWarPlayerDisplayInfo.Builder, xddq.pb.KunlunWarPlayerDisplayInfoOrBuilder>(
                playerIds_,
                ((bitField0_ & 0x00000004) != 0),
                getParentForChildren(),
                isClean());
        playerIds_ = null;
      }
      return playerIdsBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.KunlunWarAttackCityInfo)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.KunlunWarAttackCityInfo)
  private static final xddq.pb.KunlunWarAttackCityInfo DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.KunlunWarAttackCityInfo();
  }

  public static xddq.pb.KunlunWarAttackCityInfo getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<KunlunWarAttackCityInfo>
      PARSER = new com.google.protobuf.AbstractParser<KunlunWarAttackCityInfo>() {
    @java.lang.Override
    public KunlunWarAttackCityInfo parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<KunlunWarAttackCityInfo> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<KunlunWarAttackCityInfo> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.KunlunWarAttackCityInfo getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

