// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

public interface WarSeasonChallengeReqOrBuilder extends
    // @@protoc_insertion_point(interface_extends:xddq.pb.WarSeasonChallengeReq)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>required int64 groupId = 1;</code>
   * @return Whether the groupId field is set.
   */
  boolean hasGroupId();
  /**
   * <code>required int64 groupId = 1;</code>
   * @return The groupId.
   */
  long getGroupId();

  /**
   * <code>required int32 level = 2;</code>
   * @return Whether the level field is set.
   */
  boolean hasLevel();
  /**
   * <code>required int32 level = 2;</code>
   * @return The level.
   */
  int getLevel();

  /**
   * <code>optional bool isItemCost = 3;</code>
   * @return Whether the isItemCost field is set.
   */
  boolean hasIsItemCost();
  /**
   * <code>optional bool isItemCost = 3;</code>
   * @return The isItemCost.
   */
  boolean getIsItemCost();

  /**
   * <code>optional bool isQuick = 4;</code>
   * @return Whether the isQuick field is set.
   */
  boolean hasIsQuick();
  /**
   * <code>optional bool isQuick = 4;</code>
   * @return The isQuick.
   */
  boolean getIsQuick();

  /**
   * <code>optional int32 bodyIndex = 5;</code>
   * @return Whether the bodyIndex field is set.
   */
  boolean hasBodyIndex();
  /**
   * <code>optional int32 bodyIndex = 5;</code>
   * @return The bodyIndex.
   */
  int getBodyIndex();
}
