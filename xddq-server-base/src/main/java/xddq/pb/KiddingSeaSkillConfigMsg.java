// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.KiddingSeaSkillConfigMsg}
 */
public final class KiddingSeaSkillConfigMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.KiddingSeaSkillConfigMsg)
    KiddingSeaSkillConfigMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      KiddingSeaSkillConfigMsg.class.getName());
  }
  // Use KiddingSeaSkillConfigMsg.newBuilder() to construct.
  private KiddingSeaSkillConfigMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private KiddingSeaSkillConfigMsg() {
    skillAnimName_ = "";
    colliderBoxParamModel_ = "";
    comboModelList_ = "";
    cameraShakeType_ = "";
    hitPowerModel_ = "";
    hitEffectModel_ = "";
    bulletLaunchModelList_ = "";
    barrageLaunchModelList_ = "";
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_KiddingSeaSkillConfigMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_KiddingSeaSkillConfigMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.KiddingSeaSkillConfigMsg.class, xddq.pb.KiddingSeaSkillConfigMsg.Builder.class);
  }

  private int bitField0_;
  public static final int ID_FIELD_NUMBER = 1;
  private int id_ = 0;
  /**
   * <code>required int32 id = 1;</code>
   * @return Whether the id field is set.
   */
  @java.lang.Override
  public boolean hasId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 id = 1;</code>
   * @return The id.
   */
  @java.lang.Override
  public int getId() {
    return id_;
  }

  public static final int SKILLANIMNAME_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private volatile java.lang.Object skillAnimName_ = "";
  /**
   * <code>required string skillAnimName = 2;</code>
   * @return Whether the skillAnimName field is set.
   */
  @java.lang.Override
  public boolean hasSkillAnimName() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>required string skillAnimName = 2;</code>
   * @return The skillAnimName.
   */
  @java.lang.Override
  public java.lang.String getSkillAnimName() {
    java.lang.Object ref = skillAnimName_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        skillAnimName_ = s;
      }
      return s;
    }
  }
  /**
   * <code>required string skillAnimName = 2;</code>
   * @return The bytes for skillAnimName.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getSkillAnimNameBytes() {
    java.lang.Object ref = skillAnimName_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      skillAnimName_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int COLLIDERBOXPARAMMODEL_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private volatile java.lang.Object colliderBoxParamModel_ = "";
  /**
   * <code>required string colliderBoxParamModel = 3;</code>
   * @return Whether the colliderBoxParamModel field is set.
   */
  @java.lang.Override
  public boolean hasColliderBoxParamModel() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>required string colliderBoxParamModel = 3;</code>
   * @return The colliderBoxParamModel.
   */
  @java.lang.Override
  public java.lang.String getColliderBoxParamModel() {
    java.lang.Object ref = colliderBoxParamModel_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        colliderBoxParamModel_ = s;
      }
      return s;
    }
  }
  /**
   * <code>required string colliderBoxParamModel = 3;</code>
   * @return The bytes for colliderBoxParamModel.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getColliderBoxParamModelBytes() {
    java.lang.Object ref = colliderBoxParamModel_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      colliderBoxParamModel_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int LOCKRANGE_FIELD_NUMBER = 4;
  private int lockRange_ = 0;
  /**
   * <code>required int32 lockRange = 4;</code>
   * @return Whether the lockRange field is set.
   */
  @java.lang.Override
  public boolean hasLockRange() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>required int32 lockRange = 4;</code>
   * @return The lockRange.
   */
  @java.lang.Override
  public int getLockRange() {
    return lockRange_;
  }

  public static final int MOVEMENTSCALE_FIELD_NUMBER = 5;
  private int movementScale_ = 0;
  /**
   * <code>required int32 movementScale = 5;</code>
   * @return Whether the movementScale field is set.
   */
  @java.lang.Override
  public boolean hasMovementScale() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>required int32 movementScale = 5;</code>
   * @return The movementScale.
   */
  @java.lang.Override
  public int getMovementScale() {
    return movementScale_;
  }

  public static final int ISAOE_FIELD_NUMBER = 6;
  private int isAOE_ = 0;
  /**
   * <code>required int32 isAOE = 6;</code>
   * @return Whether the isAOE field is set.
   */
  @java.lang.Override
  public boolean hasIsAOE() {
    return ((bitField0_ & 0x00000020) != 0);
  }
  /**
   * <code>required int32 isAOE = 6;</code>
   * @return The isAOE.
   */
  @java.lang.Override
  public int getIsAOE() {
    return isAOE_;
  }

  public static final int AOEHITLIMIT_FIELD_NUMBER = 7;
  private int aoeHitLimit_ = 0;
  /**
   * <code>required int32 aoeHitLimit = 7;</code>
   * @return Whether the aoeHitLimit field is set.
   */
  @java.lang.Override
  public boolean hasAoeHitLimit() {
    return ((bitField0_ & 0x00000040) != 0);
  }
  /**
   * <code>required int32 aoeHitLimit = 7;</code>
   * @return The aoeHitLimit.
   */
  @java.lang.Override
  public int getAoeHitLimit() {
    return aoeHitLimit_;
  }

  public static final int SKILLCD_FIELD_NUMBER = 8;
  private int skillCD_ = 0;
  /**
   * <code>required int32 skillCD = 8;</code>
   * @return Whether the skillCD field is set.
   */
  @java.lang.Override
  public boolean hasSkillCD() {
    return ((bitField0_ & 0x00000080) != 0);
  }
  /**
   * <code>required int32 skillCD = 8;</code>
   * @return The skillCD.
   */
  @java.lang.Override
  public int getSkillCD() {
    return skillCD_;
  }

  public static final int COMBOMODELLIST_FIELD_NUMBER = 9;
  @SuppressWarnings("serial")
  private volatile java.lang.Object comboModelList_ = "";
  /**
   * <code>required string comboModelList = 9;</code>
   * @return Whether the comboModelList field is set.
   */
  @java.lang.Override
  public boolean hasComboModelList() {
    return ((bitField0_ & 0x00000100) != 0);
  }
  /**
   * <code>required string comboModelList = 9;</code>
   * @return The comboModelList.
   */
  @java.lang.Override
  public java.lang.String getComboModelList() {
    java.lang.Object ref = comboModelList_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        comboModelList_ = s;
      }
      return s;
    }
  }
  /**
   * <code>required string comboModelList = 9;</code>
   * @return The bytes for comboModelList.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getComboModelListBytes() {
    java.lang.Object ref = comboModelList_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      comboModelList_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CAMERASHAKETYPE_FIELD_NUMBER = 10;
  @SuppressWarnings("serial")
  private volatile java.lang.Object cameraShakeType_ = "";
  /**
   * <code>required string cameraShakeType = 10;</code>
   * @return Whether the cameraShakeType field is set.
   */
  @java.lang.Override
  public boolean hasCameraShakeType() {
    return ((bitField0_ & 0x00000200) != 0);
  }
  /**
   * <code>required string cameraShakeType = 10;</code>
   * @return The cameraShakeType.
   */
  @java.lang.Override
  public java.lang.String getCameraShakeType() {
    java.lang.Object ref = cameraShakeType_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        cameraShakeType_ = s;
      }
      return s;
    }
  }
  /**
   * <code>required string cameraShakeType = 10;</code>
   * @return The bytes for cameraShakeType.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getCameraShakeTypeBytes() {
    java.lang.Object ref = cameraShakeType_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      cameraShakeType_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int HITPOWERMODEL_FIELD_NUMBER = 11;
  @SuppressWarnings("serial")
  private volatile java.lang.Object hitPowerModel_ = "";
  /**
   * <code>required string hitPowerModel = 11;</code>
   * @return Whether the hitPowerModel field is set.
   */
  @java.lang.Override
  public boolean hasHitPowerModel() {
    return ((bitField0_ & 0x00000400) != 0);
  }
  /**
   * <code>required string hitPowerModel = 11;</code>
   * @return The hitPowerModel.
   */
  @java.lang.Override
  public java.lang.String getHitPowerModel() {
    java.lang.Object ref = hitPowerModel_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        hitPowerModel_ = s;
      }
      return s;
    }
  }
  /**
   * <code>required string hitPowerModel = 11;</code>
   * @return The bytes for hitPowerModel.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getHitPowerModelBytes() {
    java.lang.Object ref = hitPowerModel_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      hitPowerModel_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int HITEFFECTMODEL_FIELD_NUMBER = 12;
  @SuppressWarnings("serial")
  private volatile java.lang.Object hitEffectModel_ = "";
  /**
   * <code>required string hitEffectModel = 12;</code>
   * @return Whether the hitEffectModel field is set.
   */
  @java.lang.Override
  public boolean hasHitEffectModel() {
    return ((bitField0_ & 0x00000800) != 0);
  }
  /**
   * <code>required string hitEffectModel = 12;</code>
   * @return The hitEffectModel.
   */
  @java.lang.Override
  public java.lang.String getHitEffectModel() {
    java.lang.Object ref = hitEffectModel_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        hitEffectModel_ = s;
      }
      return s;
    }
  }
  /**
   * <code>required string hitEffectModel = 12;</code>
   * @return The bytes for hitEffectModel.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getHitEffectModelBytes() {
    java.lang.Object ref = hitEffectModel_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      hitEffectModel_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int BULLETLAUNCHMODELLIST_FIELD_NUMBER = 13;
  @SuppressWarnings("serial")
  private volatile java.lang.Object bulletLaunchModelList_ = "";
  /**
   * <code>required string bulletLaunchModelList = 13;</code>
   * @return Whether the bulletLaunchModelList field is set.
   */
  @java.lang.Override
  public boolean hasBulletLaunchModelList() {
    return ((bitField0_ & 0x00001000) != 0);
  }
  /**
   * <code>required string bulletLaunchModelList = 13;</code>
   * @return The bulletLaunchModelList.
   */
  @java.lang.Override
  public java.lang.String getBulletLaunchModelList() {
    java.lang.Object ref = bulletLaunchModelList_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        bulletLaunchModelList_ = s;
      }
      return s;
    }
  }
  /**
   * <code>required string bulletLaunchModelList = 13;</code>
   * @return The bytes for bulletLaunchModelList.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getBulletLaunchModelListBytes() {
    java.lang.Object ref = bulletLaunchModelList_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      bulletLaunchModelList_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int BARRAGELAUNCHMODELLIST_FIELD_NUMBER = 14;
  @SuppressWarnings("serial")
  private volatile java.lang.Object barrageLaunchModelList_ = "";
  /**
   * <code>required string barrageLaunchModelList = 14;</code>
   * @return Whether the barrageLaunchModelList field is set.
   */
  @java.lang.Override
  public boolean hasBarrageLaunchModelList() {
    return ((bitField0_ & 0x00002000) != 0);
  }
  /**
   * <code>required string barrageLaunchModelList = 14;</code>
   * @return The barrageLaunchModelList.
   */
  @java.lang.Override
  public java.lang.String getBarrageLaunchModelList() {
    java.lang.Object ref = barrageLaunchModelList_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        barrageLaunchModelList_ = s;
      }
      return s;
    }
  }
  /**
   * <code>required string barrageLaunchModelList = 14;</code>
   * @return The bytes for barrageLaunchModelList.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getBarrageLaunchModelListBytes() {
    java.lang.Object ref = barrageLaunchModelList_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      barrageLaunchModelList_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int RAGECOST_FIELD_NUMBER = 15;
  private int rageCost_ = 0;
  /**
   * <code>required int32 rageCost = 15;</code>
   * @return Whether the rageCost field is set.
   */
  @java.lang.Override
  public boolean hasRageCost() {
    return ((bitField0_ & 0x00004000) != 0);
  }
  /**
   * <code>required int32 rageCost = 15;</code>
   * @return The rageCost.
   */
  @java.lang.Override
  public int getRageCost() {
    return rageCost_;
  }

  public static final int HITVFXID_FIELD_NUMBER = 16;
  private int hitVFXId_ = 0;
  /**
   * <code>required int32 hitVFXId = 16;</code>
   * @return Whether the hitVFXId field is set.
   */
  @java.lang.Override
  public boolean hasHitVFXId() {
    return ((bitField0_ & 0x00008000) != 0);
  }
  /**
   * <code>required int32 hitVFXId = 16;</code>
   * @return The hitVFXId.
   */
  @java.lang.Override
  public int getHitVFXId() {
    return hitVFXId_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasId()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasSkillAnimName()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasColliderBoxParamModel()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasLockRange()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasMovementScale()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasIsAOE()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasAoeHitLimit()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasSkillCD()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasComboModelList()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasCameraShakeType()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasHitPowerModel()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasHitEffectModel()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasBulletLaunchModelList()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasBarrageLaunchModelList()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasRageCost()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasHitVFXId()) {
      memoizedIsInitialized = 0;
      return false;
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, id_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 2, skillAnimName_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 3, colliderBoxParamModel_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt32(4, lockRange_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      output.writeInt32(5, movementScale_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      output.writeInt32(6, isAOE_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      output.writeInt32(7, aoeHitLimit_);
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      output.writeInt32(8, skillCD_);
    }
    if (((bitField0_ & 0x00000100) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 9, comboModelList_);
    }
    if (((bitField0_ & 0x00000200) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 10, cameraShakeType_);
    }
    if (((bitField0_ & 0x00000400) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 11, hitPowerModel_);
    }
    if (((bitField0_ & 0x00000800) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 12, hitEffectModel_);
    }
    if (((bitField0_ & 0x00001000) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 13, bulletLaunchModelList_);
    }
    if (((bitField0_ & 0x00002000) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 14, barrageLaunchModelList_);
    }
    if (((bitField0_ & 0x00004000) != 0)) {
      output.writeInt32(15, rageCost_);
    }
    if (((bitField0_ & 0x00008000) != 0)) {
      output.writeInt32(16, hitVFXId_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, id_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(2, skillAnimName_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(3, colliderBoxParamModel_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(4, lockRange_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(5, movementScale_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(6, isAOE_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(7, aoeHitLimit_);
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(8, skillCD_);
    }
    if (((bitField0_ & 0x00000100) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(9, comboModelList_);
    }
    if (((bitField0_ & 0x00000200) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(10, cameraShakeType_);
    }
    if (((bitField0_ & 0x00000400) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(11, hitPowerModel_);
    }
    if (((bitField0_ & 0x00000800) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(12, hitEffectModel_);
    }
    if (((bitField0_ & 0x00001000) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(13, bulletLaunchModelList_);
    }
    if (((bitField0_ & 0x00002000) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(14, barrageLaunchModelList_);
    }
    if (((bitField0_ & 0x00004000) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(15, rageCost_);
    }
    if (((bitField0_ & 0x00008000) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(16, hitVFXId_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.KiddingSeaSkillConfigMsg)) {
      return super.equals(obj);
    }
    xddq.pb.KiddingSeaSkillConfigMsg other = (xddq.pb.KiddingSeaSkillConfigMsg) obj;

    if (hasId() != other.hasId()) return false;
    if (hasId()) {
      if (getId()
          != other.getId()) return false;
    }
    if (hasSkillAnimName() != other.hasSkillAnimName()) return false;
    if (hasSkillAnimName()) {
      if (!getSkillAnimName()
          .equals(other.getSkillAnimName())) return false;
    }
    if (hasColliderBoxParamModel() != other.hasColliderBoxParamModel()) return false;
    if (hasColliderBoxParamModel()) {
      if (!getColliderBoxParamModel()
          .equals(other.getColliderBoxParamModel())) return false;
    }
    if (hasLockRange() != other.hasLockRange()) return false;
    if (hasLockRange()) {
      if (getLockRange()
          != other.getLockRange()) return false;
    }
    if (hasMovementScale() != other.hasMovementScale()) return false;
    if (hasMovementScale()) {
      if (getMovementScale()
          != other.getMovementScale()) return false;
    }
    if (hasIsAOE() != other.hasIsAOE()) return false;
    if (hasIsAOE()) {
      if (getIsAOE()
          != other.getIsAOE()) return false;
    }
    if (hasAoeHitLimit() != other.hasAoeHitLimit()) return false;
    if (hasAoeHitLimit()) {
      if (getAoeHitLimit()
          != other.getAoeHitLimit()) return false;
    }
    if (hasSkillCD() != other.hasSkillCD()) return false;
    if (hasSkillCD()) {
      if (getSkillCD()
          != other.getSkillCD()) return false;
    }
    if (hasComboModelList() != other.hasComboModelList()) return false;
    if (hasComboModelList()) {
      if (!getComboModelList()
          .equals(other.getComboModelList())) return false;
    }
    if (hasCameraShakeType() != other.hasCameraShakeType()) return false;
    if (hasCameraShakeType()) {
      if (!getCameraShakeType()
          .equals(other.getCameraShakeType())) return false;
    }
    if (hasHitPowerModel() != other.hasHitPowerModel()) return false;
    if (hasHitPowerModel()) {
      if (!getHitPowerModel()
          .equals(other.getHitPowerModel())) return false;
    }
    if (hasHitEffectModel() != other.hasHitEffectModel()) return false;
    if (hasHitEffectModel()) {
      if (!getHitEffectModel()
          .equals(other.getHitEffectModel())) return false;
    }
    if (hasBulletLaunchModelList() != other.hasBulletLaunchModelList()) return false;
    if (hasBulletLaunchModelList()) {
      if (!getBulletLaunchModelList()
          .equals(other.getBulletLaunchModelList())) return false;
    }
    if (hasBarrageLaunchModelList() != other.hasBarrageLaunchModelList()) return false;
    if (hasBarrageLaunchModelList()) {
      if (!getBarrageLaunchModelList()
          .equals(other.getBarrageLaunchModelList())) return false;
    }
    if (hasRageCost() != other.hasRageCost()) return false;
    if (hasRageCost()) {
      if (getRageCost()
          != other.getRageCost()) return false;
    }
    if (hasHitVFXId() != other.hasHitVFXId()) return false;
    if (hasHitVFXId()) {
      if (getHitVFXId()
          != other.getHitVFXId()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasId()) {
      hash = (37 * hash) + ID_FIELD_NUMBER;
      hash = (53 * hash) + getId();
    }
    if (hasSkillAnimName()) {
      hash = (37 * hash) + SKILLANIMNAME_FIELD_NUMBER;
      hash = (53 * hash) + getSkillAnimName().hashCode();
    }
    if (hasColliderBoxParamModel()) {
      hash = (37 * hash) + COLLIDERBOXPARAMMODEL_FIELD_NUMBER;
      hash = (53 * hash) + getColliderBoxParamModel().hashCode();
    }
    if (hasLockRange()) {
      hash = (37 * hash) + LOCKRANGE_FIELD_NUMBER;
      hash = (53 * hash) + getLockRange();
    }
    if (hasMovementScale()) {
      hash = (37 * hash) + MOVEMENTSCALE_FIELD_NUMBER;
      hash = (53 * hash) + getMovementScale();
    }
    if (hasIsAOE()) {
      hash = (37 * hash) + ISAOE_FIELD_NUMBER;
      hash = (53 * hash) + getIsAOE();
    }
    if (hasAoeHitLimit()) {
      hash = (37 * hash) + AOEHITLIMIT_FIELD_NUMBER;
      hash = (53 * hash) + getAoeHitLimit();
    }
    if (hasSkillCD()) {
      hash = (37 * hash) + SKILLCD_FIELD_NUMBER;
      hash = (53 * hash) + getSkillCD();
    }
    if (hasComboModelList()) {
      hash = (37 * hash) + COMBOMODELLIST_FIELD_NUMBER;
      hash = (53 * hash) + getComboModelList().hashCode();
    }
    if (hasCameraShakeType()) {
      hash = (37 * hash) + CAMERASHAKETYPE_FIELD_NUMBER;
      hash = (53 * hash) + getCameraShakeType().hashCode();
    }
    if (hasHitPowerModel()) {
      hash = (37 * hash) + HITPOWERMODEL_FIELD_NUMBER;
      hash = (53 * hash) + getHitPowerModel().hashCode();
    }
    if (hasHitEffectModel()) {
      hash = (37 * hash) + HITEFFECTMODEL_FIELD_NUMBER;
      hash = (53 * hash) + getHitEffectModel().hashCode();
    }
    if (hasBulletLaunchModelList()) {
      hash = (37 * hash) + BULLETLAUNCHMODELLIST_FIELD_NUMBER;
      hash = (53 * hash) + getBulletLaunchModelList().hashCode();
    }
    if (hasBarrageLaunchModelList()) {
      hash = (37 * hash) + BARRAGELAUNCHMODELLIST_FIELD_NUMBER;
      hash = (53 * hash) + getBarrageLaunchModelList().hashCode();
    }
    if (hasRageCost()) {
      hash = (37 * hash) + RAGECOST_FIELD_NUMBER;
      hash = (53 * hash) + getRageCost();
    }
    if (hasHitVFXId()) {
      hash = (37 * hash) + HITVFXID_FIELD_NUMBER;
      hash = (53 * hash) + getHitVFXId();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.KiddingSeaSkillConfigMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.KiddingSeaSkillConfigMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.KiddingSeaSkillConfigMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.KiddingSeaSkillConfigMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.KiddingSeaSkillConfigMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.KiddingSeaSkillConfigMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.KiddingSeaSkillConfigMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.KiddingSeaSkillConfigMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.KiddingSeaSkillConfigMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.KiddingSeaSkillConfigMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.KiddingSeaSkillConfigMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.KiddingSeaSkillConfigMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.KiddingSeaSkillConfigMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.KiddingSeaSkillConfigMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.KiddingSeaSkillConfigMsg)
      xddq.pb.KiddingSeaSkillConfigMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_KiddingSeaSkillConfigMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_KiddingSeaSkillConfigMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.KiddingSeaSkillConfigMsg.class, xddq.pb.KiddingSeaSkillConfigMsg.Builder.class);
    }

    // Construct using xddq.pb.KiddingSeaSkillConfigMsg.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      id_ = 0;
      skillAnimName_ = "";
      colliderBoxParamModel_ = "";
      lockRange_ = 0;
      movementScale_ = 0;
      isAOE_ = 0;
      aoeHitLimit_ = 0;
      skillCD_ = 0;
      comboModelList_ = "";
      cameraShakeType_ = "";
      hitPowerModel_ = "";
      hitEffectModel_ = "";
      bulletLaunchModelList_ = "";
      barrageLaunchModelList_ = "";
      rageCost_ = 0;
      hitVFXId_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_KiddingSeaSkillConfigMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.KiddingSeaSkillConfigMsg getDefaultInstanceForType() {
      return xddq.pb.KiddingSeaSkillConfigMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.KiddingSeaSkillConfigMsg build() {
      xddq.pb.KiddingSeaSkillConfigMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.KiddingSeaSkillConfigMsg buildPartial() {
      xddq.pb.KiddingSeaSkillConfigMsg result = new xddq.pb.KiddingSeaSkillConfigMsg(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.KiddingSeaSkillConfigMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.id_ = id_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.skillAnimName_ = skillAnimName_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.colliderBoxParamModel_ = colliderBoxParamModel_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.lockRange_ = lockRange_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.movementScale_ = movementScale_;
        to_bitField0_ |= 0x00000010;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.isAOE_ = isAOE_;
        to_bitField0_ |= 0x00000020;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.aoeHitLimit_ = aoeHitLimit_;
        to_bitField0_ |= 0x00000040;
      }
      if (((from_bitField0_ & 0x00000080) != 0)) {
        result.skillCD_ = skillCD_;
        to_bitField0_ |= 0x00000080;
      }
      if (((from_bitField0_ & 0x00000100) != 0)) {
        result.comboModelList_ = comboModelList_;
        to_bitField0_ |= 0x00000100;
      }
      if (((from_bitField0_ & 0x00000200) != 0)) {
        result.cameraShakeType_ = cameraShakeType_;
        to_bitField0_ |= 0x00000200;
      }
      if (((from_bitField0_ & 0x00000400) != 0)) {
        result.hitPowerModel_ = hitPowerModel_;
        to_bitField0_ |= 0x00000400;
      }
      if (((from_bitField0_ & 0x00000800) != 0)) {
        result.hitEffectModel_ = hitEffectModel_;
        to_bitField0_ |= 0x00000800;
      }
      if (((from_bitField0_ & 0x00001000) != 0)) {
        result.bulletLaunchModelList_ = bulletLaunchModelList_;
        to_bitField0_ |= 0x00001000;
      }
      if (((from_bitField0_ & 0x00002000) != 0)) {
        result.barrageLaunchModelList_ = barrageLaunchModelList_;
        to_bitField0_ |= 0x00002000;
      }
      if (((from_bitField0_ & 0x00004000) != 0)) {
        result.rageCost_ = rageCost_;
        to_bitField0_ |= 0x00004000;
      }
      if (((from_bitField0_ & 0x00008000) != 0)) {
        result.hitVFXId_ = hitVFXId_;
        to_bitField0_ |= 0x00008000;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.KiddingSeaSkillConfigMsg) {
        return mergeFrom((xddq.pb.KiddingSeaSkillConfigMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.KiddingSeaSkillConfigMsg other) {
      if (other == xddq.pb.KiddingSeaSkillConfigMsg.getDefaultInstance()) return this;
      if (other.hasId()) {
        setId(other.getId());
      }
      if (other.hasSkillAnimName()) {
        skillAnimName_ = other.skillAnimName_;
        bitField0_ |= 0x00000002;
        onChanged();
      }
      if (other.hasColliderBoxParamModel()) {
        colliderBoxParamModel_ = other.colliderBoxParamModel_;
        bitField0_ |= 0x00000004;
        onChanged();
      }
      if (other.hasLockRange()) {
        setLockRange(other.getLockRange());
      }
      if (other.hasMovementScale()) {
        setMovementScale(other.getMovementScale());
      }
      if (other.hasIsAOE()) {
        setIsAOE(other.getIsAOE());
      }
      if (other.hasAoeHitLimit()) {
        setAoeHitLimit(other.getAoeHitLimit());
      }
      if (other.hasSkillCD()) {
        setSkillCD(other.getSkillCD());
      }
      if (other.hasComboModelList()) {
        comboModelList_ = other.comboModelList_;
        bitField0_ |= 0x00000100;
        onChanged();
      }
      if (other.hasCameraShakeType()) {
        cameraShakeType_ = other.cameraShakeType_;
        bitField0_ |= 0x00000200;
        onChanged();
      }
      if (other.hasHitPowerModel()) {
        hitPowerModel_ = other.hitPowerModel_;
        bitField0_ |= 0x00000400;
        onChanged();
      }
      if (other.hasHitEffectModel()) {
        hitEffectModel_ = other.hitEffectModel_;
        bitField0_ |= 0x00000800;
        onChanged();
      }
      if (other.hasBulletLaunchModelList()) {
        bulletLaunchModelList_ = other.bulletLaunchModelList_;
        bitField0_ |= 0x00001000;
        onChanged();
      }
      if (other.hasBarrageLaunchModelList()) {
        barrageLaunchModelList_ = other.barrageLaunchModelList_;
        bitField0_ |= 0x00002000;
        onChanged();
      }
      if (other.hasRageCost()) {
        setRageCost(other.getRageCost());
      }
      if (other.hasHitVFXId()) {
        setHitVFXId(other.getHitVFXId());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasId()) {
        return false;
      }
      if (!hasSkillAnimName()) {
        return false;
      }
      if (!hasColliderBoxParamModel()) {
        return false;
      }
      if (!hasLockRange()) {
        return false;
      }
      if (!hasMovementScale()) {
        return false;
      }
      if (!hasIsAOE()) {
        return false;
      }
      if (!hasAoeHitLimit()) {
        return false;
      }
      if (!hasSkillCD()) {
        return false;
      }
      if (!hasComboModelList()) {
        return false;
      }
      if (!hasCameraShakeType()) {
        return false;
      }
      if (!hasHitPowerModel()) {
        return false;
      }
      if (!hasHitEffectModel()) {
        return false;
      }
      if (!hasBulletLaunchModelList()) {
        return false;
      }
      if (!hasBarrageLaunchModelList()) {
        return false;
      }
      if (!hasRageCost()) {
        return false;
      }
      if (!hasHitVFXId()) {
        return false;
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              id_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              skillAnimName_ = input.readBytes();
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 26: {
              colliderBoxParamModel_ = input.readBytes();
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 32: {
              lockRange_ = input.readInt32();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            case 40: {
              movementScale_ = input.readInt32();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            case 48: {
              isAOE_ = input.readInt32();
              bitField0_ |= 0x00000020;
              break;
            } // case 48
            case 56: {
              aoeHitLimit_ = input.readInt32();
              bitField0_ |= 0x00000040;
              break;
            } // case 56
            case 64: {
              skillCD_ = input.readInt32();
              bitField0_ |= 0x00000080;
              break;
            } // case 64
            case 74: {
              comboModelList_ = input.readBytes();
              bitField0_ |= 0x00000100;
              break;
            } // case 74
            case 82: {
              cameraShakeType_ = input.readBytes();
              bitField0_ |= 0x00000200;
              break;
            } // case 82
            case 90: {
              hitPowerModel_ = input.readBytes();
              bitField0_ |= 0x00000400;
              break;
            } // case 90
            case 98: {
              hitEffectModel_ = input.readBytes();
              bitField0_ |= 0x00000800;
              break;
            } // case 98
            case 106: {
              bulletLaunchModelList_ = input.readBytes();
              bitField0_ |= 0x00001000;
              break;
            } // case 106
            case 114: {
              barrageLaunchModelList_ = input.readBytes();
              bitField0_ |= 0x00002000;
              break;
            } // case 114
            case 120: {
              rageCost_ = input.readInt32();
              bitField0_ |= 0x00004000;
              break;
            } // case 120
            case 128: {
              hitVFXId_ = input.readInt32();
              bitField0_ |= 0x00008000;
              break;
            } // case 128
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int id_ ;
    /**
     * <code>required int32 id = 1;</code>
     * @return Whether the id field is set.
     */
    @java.lang.Override
    public boolean hasId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 id = 1;</code>
     * @return The id.
     */
    @java.lang.Override
    public int getId() {
      return id_;
    }
    /**
     * <code>required int32 id = 1;</code>
     * @param value The id to set.
     * @return This builder for chaining.
     */
    public Builder setId(int value) {

      id_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 id = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      id_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object skillAnimName_ = "";
    /**
     * <code>required string skillAnimName = 2;</code>
     * @return Whether the skillAnimName field is set.
     */
    public boolean hasSkillAnimName() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>required string skillAnimName = 2;</code>
     * @return The skillAnimName.
     */
    public java.lang.String getSkillAnimName() {
      java.lang.Object ref = skillAnimName_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          skillAnimName_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>required string skillAnimName = 2;</code>
     * @return The bytes for skillAnimName.
     */
    public com.google.protobuf.ByteString
        getSkillAnimNameBytes() {
      java.lang.Object ref = skillAnimName_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        skillAnimName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>required string skillAnimName = 2;</code>
     * @param value The skillAnimName to set.
     * @return This builder for chaining.
     */
    public Builder setSkillAnimName(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      skillAnimName_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>required string skillAnimName = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearSkillAnimName() {
      skillAnimName_ = getDefaultInstance().getSkillAnimName();
      bitField0_ = (bitField0_ & ~0x00000002);
      onChanged();
      return this;
    }
    /**
     * <code>required string skillAnimName = 2;</code>
     * @param value The bytes for skillAnimName to set.
     * @return This builder for chaining.
     */
    public Builder setSkillAnimNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      skillAnimName_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }

    private java.lang.Object colliderBoxParamModel_ = "";
    /**
     * <code>required string colliderBoxParamModel = 3;</code>
     * @return Whether the colliderBoxParamModel field is set.
     */
    public boolean hasColliderBoxParamModel() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>required string colliderBoxParamModel = 3;</code>
     * @return The colliderBoxParamModel.
     */
    public java.lang.String getColliderBoxParamModel() {
      java.lang.Object ref = colliderBoxParamModel_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          colliderBoxParamModel_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>required string colliderBoxParamModel = 3;</code>
     * @return The bytes for colliderBoxParamModel.
     */
    public com.google.protobuf.ByteString
        getColliderBoxParamModelBytes() {
      java.lang.Object ref = colliderBoxParamModel_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        colliderBoxParamModel_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>required string colliderBoxParamModel = 3;</code>
     * @param value The colliderBoxParamModel to set.
     * @return This builder for chaining.
     */
    public Builder setColliderBoxParamModel(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      colliderBoxParamModel_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>required string colliderBoxParamModel = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearColliderBoxParamModel() {
      colliderBoxParamModel_ = getDefaultInstance().getColliderBoxParamModel();
      bitField0_ = (bitField0_ & ~0x00000004);
      onChanged();
      return this;
    }
    /**
     * <code>required string colliderBoxParamModel = 3;</code>
     * @param value The bytes for colliderBoxParamModel to set.
     * @return This builder for chaining.
     */
    public Builder setColliderBoxParamModelBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      colliderBoxParamModel_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }

    private int lockRange_ ;
    /**
     * <code>required int32 lockRange = 4;</code>
     * @return Whether the lockRange field is set.
     */
    @java.lang.Override
    public boolean hasLockRange() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>required int32 lockRange = 4;</code>
     * @return The lockRange.
     */
    @java.lang.Override
    public int getLockRange() {
      return lockRange_;
    }
    /**
     * <code>required int32 lockRange = 4;</code>
     * @param value The lockRange to set.
     * @return This builder for chaining.
     */
    public Builder setLockRange(int value) {

      lockRange_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 lockRange = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearLockRange() {
      bitField0_ = (bitField0_ & ~0x00000008);
      lockRange_ = 0;
      onChanged();
      return this;
    }

    private int movementScale_ ;
    /**
     * <code>required int32 movementScale = 5;</code>
     * @return Whether the movementScale field is set.
     */
    @java.lang.Override
    public boolean hasMovementScale() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>required int32 movementScale = 5;</code>
     * @return The movementScale.
     */
    @java.lang.Override
    public int getMovementScale() {
      return movementScale_;
    }
    /**
     * <code>required int32 movementScale = 5;</code>
     * @param value The movementScale to set.
     * @return This builder for chaining.
     */
    public Builder setMovementScale(int value) {

      movementScale_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 movementScale = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearMovementScale() {
      bitField0_ = (bitField0_ & ~0x00000010);
      movementScale_ = 0;
      onChanged();
      return this;
    }

    private int isAOE_ ;
    /**
     * <code>required int32 isAOE = 6;</code>
     * @return Whether the isAOE field is set.
     */
    @java.lang.Override
    public boolean hasIsAOE() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>required int32 isAOE = 6;</code>
     * @return The isAOE.
     */
    @java.lang.Override
    public int getIsAOE() {
      return isAOE_;
    }
    /**
     * <code>required int32 isAOE = 6;</code>
     * @param value The isAOE to set.
     * @return This builder for chaining.
     */
    public Builder setIsAOE(int value) {

      isAOE_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 isAOE = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearIsAOE() {
      bitField0_ = (bitField0_ & ~0x00000020);
      isAOE_ = 0;
      onChanged();
      return this;
    }

    private int aoeHitLimit_ ;
    /**
     * <code>required int32 aoeHitLimit = 7;</code>
     * @return Whether the aoeHitLimit field is set.
     */
    @java.lang.Override
    public boolean hasAoeHitLimit() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <code>required int32 aoeHitLimit = 7;</code>
     * @return The aoeHitLimit.
     */
    @java.lang.Override
    public int getAoeHitLimit() {
      return aoeHitLimit_;
    }
    /**
     * <code>required int32 aoeHitLimit = 7;</code>
     * @param value The aoeHitLimit to set.
     * @return This builder for chaining.
     */
    public Builder setAoeHitLimit(int value) {

      aoeHitLimit_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 aoeHitLimit = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearAoeHitLimit() {
      bitField0_ = (bitField0_ & ~0x00000040);
      aoeHitLimit_ = 0;
      onChanged();
      return this;
    }

    private int skillCD_ ;
    /**
     * <code>required int32 skillCD = 8;</code>
     * @return Whether the skillCD field is set.
     */
    @java.lang.Override
    public boolean hasSkillCD() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <code>required int32 skillCD = 8;</code>
     * @return The skillCD.
     */
    @java.lang.Override
    public int getSkillCD() {
      return skillCD_;
    }
    /**
     * <code>required int32 skillCD = 8;</code>
     * @param value The skillCD to set.
     * @return This builder for chaining.
     */
    public Builder setSkillCD(int value) {

      skillCD_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 skillCD = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearSkillCD() {
      bitField0_ = (bitField0_ & ~0x00000080);
      skillCD_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object comboModelList_ = "";
    /**
     * <code>required string comboModelList = 9;</code>
     * @return Whether the comboModelList field is set.
     */
    public boolean hasComboModelList() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <code>required string comboModelList = 9;</code>
     * @return The comboModelList.
     */
    public java.lang.String getComboModelList() {
      java.lang.Object ref = comboModelList_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          comboModelList_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>required string comboModelList = 9;</code>
     * @return The bytes for comboModelList.
     */
    public com.google.protobuf.ByteString
        getComboModelListBytes() {
      java.lang.Object ref = comboModelList_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        comboModelList_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>required string comboModelList = 9;</code>
     * @param value The comboModelList to set.
     * @return This builder for chaining.
     */
    public Builder setComboModelList(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      comboModelList_ = value;
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }
    /**
     * <code>required string comboModelList = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearComboModelList() {
      comboModelList_ = getDefaultInstance().getComboModelList();
      bitField0_ = (bitField0_ & ~0x00000100);
      onChanged();
      return this;
    }
    /**
     * <code>required string comboModelList = 9;</code>
     * @param value The bytes for comboModelList to set.
     * @return This builder for chaining.
     */
    public Builder setComboModelListBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      comboModelList_ = value;
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }

    private java.lang.Object cameraShakeType_ = "";
    /**
     * <code>required string cameraShakeType = 10;</code>
     * @return Whether the cameraShakeType field is set.
     */
    public boolean hasCameraShakeType() {
      return ((bitField0_ & 0x00000200) != 0);
    }
    /**
     * <code>required string cameraShakeType = 10;</code>
     * @return The cameraShakeType.
     */
    public java.lang.String getCameraShakeType() {
      java.lang.Object ref = cameraShakeType_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          cameraShakeType_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>required string cameraShakeType = 10;</code>
     * @return The bytes for cameraShakeType.
     */
    public com.google.protobuf.ByteString
        getCameraShakeTypeBytes() {
      java.lang.Object ref = cameraShakeType_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        cameraShakeType_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>required string cameraShakeType = 10;</code>
     * @param value The cameraShakeType to set.
     * @return This builder for chaining.
     */
    public Builder setCameraShakeType(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      cameraShakeType_ = value;
      bitField0_ |= 0x00000200;
      onChanged();
      return this;
    }
    /**
     * <code>required string cameraShakeType = 10;</code>
     * @return This builder for chaining.
     */
    public Builder clearCameraShakeType() {
      cameraShakeType_ = getDefaultInstance().getCameraShakeType();
      bitField0_ = (bitField0_ & ~0x00000200);
      onChanged();
      return this;
    }
    /**
     * <code>required string cameraShakeType = 10;</code>
     * @param value The bytes for cameraShakeType to set.
     * @return This builder for chaining.
     */
    public Builder setCameraShakeTypeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      cameraShakeType_ = value;
      bitField0_ |= 0x00000200;
      onChanged();
      return this;
    }

    private java.lang.Object hitPowerModel_ = "";
    /**
     * <code>required string hitPowerModel = 11;</code>
     * @return Whether the hitPowerModel field is set.
     */
    public boolean hasHitPowerModel() {
      return ((bitField0_ & 0x00000400) != 0);
    }
    /**
     * <code>required string hitPowerModel = 11;</code>
     * @return The hitPowerModel.
     */
    public java.lang.String getHitPowerModel() {
      java.lang.Object ref = hitPowerModel_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          hitPowerModel_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>required string hitPowerModel = 11;</code>
     * @return The bytes for hitPowerModel.
     */
    public com.google.protobuf.ByteString
        getHitPowerModelBytes() {
      java.lang.Object ref = hitPowerModel_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        hitPowerModel_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>required string hitPowerModel = 11;</code>
     * @param value The hitPowerModel to set.
     * @return This builder for chaining.
     */
    public Builder setHitPowerModel(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      hitPowerModel_ = value;
      bitField0_ |= 0x00000400;
      onChanged();
      return this;
    }
    /**
     * <code>required string hitPowerModel = 11;</code>
     * @return This builder for chaining.
     */
    public Builder clearHitPowerModel() {
      hitPowerModel_ = getDefaultInstance().getHitPowerModel();
      bitField0_ = (bitField0_ & ~0x00000400);
      onChanged();
      return this;
    }
    /**
     * <code>required string hitPowerModel = 11;</code>
     * @param value The bytes for hitPowerModel to set.
     * @return This builder for chaining.
     */
    public Builder setHitPowerModelBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      hitPowerModel_ = value;
      bitField0_ |= 0x00000400;
      onChanged();
      return this;
    }

    private java.lang.Object hitEffectModel_ = "";
    /**
     * <code>required string hitEffectModel = 12;</code>
     * @return Whether the hitEffectModel field is set.
     */
    public boolean hasHitEffectModel() {
      return ((bitField0_ & 0x00000800) != 0);
    }
    /**
     * <code>required string hitEffectModel = 12;</code>
     * @return The hitEffectModel.
     */
    public java.lang.String getHitEffectModel() {
      java.lang.Object ref = hitEffectModel_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          hitEffectModel_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>required string hitEffectModel = 12;</code>
     * @return The bytes for hitEffectModel.
     */
    public com.google.protobuf.ByteString
        getHitEffectModelBytes() {
      java.lang.Object ref = hitEffectModel_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        hitEffectModel_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>required string hitEffectModel = 12;</code>
     * @param value The hitEffectModel to set.
     * @return This builder for chaining.
     */
    public Builder setHitEffectModel(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      hitEffectModel_ = value;
      bitField0_ |= 0x00000800;
      onChanged();
      return this;
    }
    /**
     * <code>required string hitEffectModel = 12;</code>
     * @return This builder for chaining.
     */
    public Builder clearHitEffectModel() {
      hitEffectModel_ = getDefaultInstance().getHitEffectModel();
      bitField0_ = (bitField0_ & ~0x00000800);
      onChanged();
      return this;
    }
    /**
     * <code>required string hitEffectModel = 12;</code>
     * @param value The bytes for hitEffectModel to set.
     * @return This builder for chaining.
     */
    public Builder setHitEffectModelBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      hitEffectModel_ = value;
      bitField0_ |= 0x00000800;
      onChanged();
      return this;
    }

    private java.lang.Object bulletLaunchModelList_ = "";
    /**
     * <code>required string bulletLaunchModelList = 13;</code>
     * @return Whether the bulletLaunchModelList field is set.
     */
    public boolean hasBulletLaunchModelList() {
      return ((bitField0_ & 0x00001000) != 0);
    }
    /**
     * <code>required string bulletLaunchModelList = 13;</code>
     * @return The bulletLaunchModelList.
     */
    public java.lang.String getBulletLaunchModelList() {
      java.lang.Object ref = bulletLaunchModelList_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          bulletLaunchModelList_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>required string bulletLaunchModelList = 13;</code>
     * @return The bytes for bulletLaunchModelList.
     */
    public com.google.protobuf.ByteString
        getBulletLaunchModelListBytes() {
      java.lang.Object ref = bulletLaunchModelList_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        bulletLaunchModelList_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>required string bulletLaunchModelList = 13;</code>
     * @param value The bulletLaunchModelList to set.
     * @return This builder for chaining.
     */
    public Builder setBulletLaunchModelList(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      bulletLaunchModelList_ = value;
      bitField0_ |= 0x00001000;
      onChanged();
      return this;
    }
    /**
     * <code>required string bulletLaunchModelList = 13;</code>
     * @return This builder for chaining.
     */
    public Builder clearBulletLaunchModelList() {
      bulletLaunchModelList_ = getDefaultInstance().getBulletLaunchModelList();
      bitField0_ = (bitField0_ & ~0x00001000);
      onChanged();
      return this;
    }
    /**
     * <code>required string bulletLaunchModelList = 13;</code>
     * @param value The bytes for bulletLaunchModelList to set.
     * @return This builder for chaining.
     */
    public Builder setBulletLaunchModelListBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      bulletLaunchModelList_ = value;
      bitField0_ |= 0x00001000;
      onChanged();
      return this;
    }

    private java.lang.Object barrageLaunchModelList_ = "";
    /**
     * <code>required string barrageLaunchModelList = 14;</code>
     * @return Whether the barrageLaunchModelList field is set.
     */
    public boolean hasBarrageLaunchModelList() {
      return ((bitField0_ & 0x00002000) != 0);
    }
    /**
     * <code>required string barrageLaunchModelList = 14;</code>
     * @return The barrageLaunchModelList.
     */
    public java.lang.String getBarrageLaunchModelList() {
      java.lang.Object ref = barrageLaunchModelList_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          barrageLaunchModelList_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>required string barrageLaunchModelList = 14;</code>
     * @return The bytes for barrageLaunchModelList.
     */
    public com.google.protobuf.ByteString
        getBarrageLaunchModelListBytes() {
      java.lang.Object ref = barrageLaunchModelList_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        barrageLaunchModelList_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>required string barrageLaunchModelList = 14;</code>
     * @param value The barrageLaunchModelList to set.
     * @return This builder for chaining.
     */
    public Builder setBarrageLaunchModelList(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      barrageLaunchModelList_ = value;
      bitField0_ |= 0x00002000;
      onChanged();
      return this;
    }
    /**
     * <code>required string barrageLaunchModelList = 14;</code>
     * @return This builder for chaining.
     */
    public Builder clearBarrageLaunchModelList() {
      barrageLaunchModelList_ = getDefaultInstance().getBarrageLaunchModelList();
      bitField0_ = (bitField0_ & ~0x00002000);
      onChanged();
      return this;
    }
    /**
     * <code>required string barrageLaunchModelList = 14;</code>
     * @param value The bytes for barrageLaunchModelList to set.
     * @return This builder for chaining.
     */
    public Builder setBarrageLaunchModelListBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      barrageLaunchModelList_ = value;
      bitField0_ |= 0x00002000;
      onChanged();
      return this;
    }

    private int rageCost_ ;
    /**
     * <code>required int32 rageCost = 15;</code>
     * @return Whether the rageCost field is set.
     */
    @java.lang.Override
    public boolean hasRageCost() {
      return ((bitField0_ & 0x00004000) != 0);
    }
    /**
     * <code>required int32 rageCost = 15;</code>
     * @return The rageCost.
     */
    @java.lang.Override
    public int getRageCost() {
      return rageCost_;
    }
    /**
     * <code>required int32 rageCost = 15;</code>
     * @param value The rageCost to set.
     * @return This builder for chaining.
     */
    public Builder setRageCost(int value) {

      rageCost_ = value;
      bitField0_ |= 0x00004000;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 rageCost = 15;</code>
     * @return This builder for chaining.
     */
    public Builder clearRageCost() {
      bitField0_ = (bitField0_ & ~0x00004000);
      rageCost_ = 0;
      onChanged();
      return this;
    }

    private int hitVFXId_ ;
    /**
     * <code>required int32 hitVFXId = 16;</code>
     * @return Whether the hitVFXId field is set.
     */
    @java.lang.Override
    public boolean hasHitVFXId() {
      return ((bitField0_ & 0x00008000) != 0);
    }
    /**
     * <code>required int32 hitVFXId = 16;</code>
     * @return The hitVFXId.
     */
    @java.lang.Override
    public int getHitVFXId() {
      return hitVFXId_;
    }
    /**
     * <code>required int32 hitVFXId = 16;</code>
     * @param value The hitVFXId to set.
     * @return This builder for chaining.
     */
    public Builder setHitVFXId(int value) {

      hitVFXId_ = value;
      bitField0_ |= 0x00008000;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 hitVFXId = 16;</code>
     * @return This builder for chaining.
     */
    public Builder clearHitVFXId() {
      bitField0_ = (bitField0_ & ~0x00008000);
      hitVFXId_ = 0;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.KiddingSeaSkillConfigMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.KiddingSeaSkillConfigMsg)
  private static final xddq.pb.KiddingSeaSkillConfigMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.KiddingSeaSkillConfigMsg();
  }

  public static xddq.pb.KiddingSeaSkillConfigMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<KiddingSeaSkillConfigMsg>
      PARSER = new com.google.protobuf.AbstractParser<KiddingSeaSkillConfigMsg>() {
    @java.lang.Override
    public KiddingSeaSkillConfigMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<KiddingSeaSkillConfigMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<KiddingSeaSkillConfigMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.KiddingSeaSkillConfigMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

