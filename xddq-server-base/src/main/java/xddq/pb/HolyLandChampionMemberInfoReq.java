// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.HolyLandChampionMemberInfoReq}
 */
public final class HolyLandChampionMemberInfoReq extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.HolyLandChampionMemberInfoReq)
    HolyLandChampionMemberInfoReqOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      HolyLandChampionMemberInfoReq.class.getName());
  }
  // Use HolyLandChampionMemberInfoReq.newBuilder() to construct.
  private HolyLandChampionMemberInfoReq(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private HolyLandChampionMemberInfoReq() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_HolyLandChampionMemberInfoReq_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_HolyLandChampionMemberInfoReq_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.HolyLandChampionMemberInfoReq.class, xddq.pb.HolyLandChampionMemberInfoReq.Builder.class);
  }

  private int bitField0_;
  public static final int INDEX_FIELD_NUMBER = 1;
  private int index_ = 0;
  /**
   * <code>required int32 index = 1;</code>
   * @return Whether the index field is set.
   */
  @java.lang.Override
  public boolean hasIndex() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 index = 1;</code>
   * @return The index.
   */
  @java.lang.Override
  public int getIndex() {
    return index_;
  }

  public static final int UNIONID_FIELD_NUMBER = 2;
  private long unionId_ = 0L;
  /**
   * <code>required int64 unionId = 2;</code>
   * @return Whether the unionId field is set.
   */
  @java.lang.Override
  public boolean hasUnionId() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>required int64 unionId = 2;</code>
   * @return The unionId.
   */
  @java.lang.Override
  public long getUnionId() {
    return unionId_;
  }

  public static final int WONTIME_FIELD_NUMBER = 3;
  private long wonTime_ = 0L;
  /**
   * <code>required int64 wonTime = 3;</code>
   * @return Whether the wonTime field is set.
   */
  @java.lang.Override
  public boolean hasWonTime() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>required int64 wonTime = 3;</code>
   * @return The wonTime.
   */
  @java.lang.Override
  public long getWonTime() {
    return wonTime_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasIndex()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasUnionId()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasWonTime()) {
      memoizedIsInitialized = 0;
      return false;
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, index_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt64(2, unionId_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt64(3, wonTime_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, index_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(2, unionId_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(3, wonTime_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.HolyLandChampionMemberInfoReq)) {
      return super.equals(obj);
    }
    xddq.pb.HolyLandChampionMemberInfoReq other = (xddq.pb.HolyLandChampionMemberInfoReq) obj;

    if (hasIndex() != other.hasIndex()) return false;
    if (hasIndex()) {
      if (getIndex()
          != other.getIndex()) return false;
    }
    if (hasUnionId() != other.hasUnionId()) return false;
    if (hasUnionId()) {
      if (getUnionId()
          != other.getUnionId()) return false;
    }
    if (hasWonTime() != other.hasWonTime()) return false;
    if (hasWonTime()) {
      if (getWonTime()
          != other.getWonTime()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasIndex()) {
      hash = (37 * hash) + INDEX_FIELD_NUMBER;
      hash = (53 * hash) + getIndex();
    }
    if (hasUnionId()) {
      hash = (37 * hash) + UNIONID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getUnionId());
    }
    if (hasWonTime()) {
      hash = (37 * hash) + WONTIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getWonTime());
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.HolyLandChampionMemberInfoReq parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.HolyLandChampionMemberInfoReq parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.HolyLandChampionMemberInfoReq parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.HolyLandChampionMemberInfoReq parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.HolyLandChampionMemberInfoReq parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.HolyLandChampionMemberInfoReq parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.HolyLandChampionMemberInfoReq parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.HolyLandChampionMemberInfoReq parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.HolyLandChampionMemberInfoReq parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.HolyLandChampionMemberInfoReq parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.HolyLandChampionMemberInfoReq parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.HolyLandChampionMemberInfoReq parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.HolyLandChampionMemberInfoReq prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.HolyLandChampionMemberInfoReq}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.HolyLandChampionMemberInfoReq)
      xddq.pb.HolyLandChampionMemberInfoReqOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_HolyLandChampionMemberInfoReq_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_HolyLandChampionMemberInfoReq_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.HolyLandChampionMemberInfoReq.class, xddq.pb.HolyLandChampionMemberInfoReq.Builder.class);
    }

    // Construct using xddq.pb.HolyLandChampionMemberInfoReq.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      index_ = 0;
      unionId_ = 0L;
      wonTime_ = 0L;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_HolyLandChampionMemberInfoReq_descriptor;
    }

    @java.lang.Override
    public xddq.pb.HolyLandChampionMemberInfoReq getDefaultInstanceForType() {
      return xddq.pb.HolyLandChampionMemberInfoReq.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.HolyLandChampionMemberInfoReq build() {
      xddq.pb.HolyLandChampionMemberInfoReq result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.HolyLandChampionMemberInfoReq buildPartial() {
      xddq.pb.HolyLandChampionMemberInfoReq result = new xddq.pb.HolyLandChampionMemberInfoReq(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.HolyLandChampionMemberInfoReq result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.index_ = index_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.unionId_ = unionId_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.wonTime_ = wonTime_;
        to_bitField0_ |= 0x00000004;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.HolyLandChampionMemberInfoReq) {
        return mergeFrom((xddq.pb.HolyLandChampionMemberInfoReq)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.HolyLandChampionMemberInfoReq other) {
      if (other == xddq.pb.HolyLandChampionMemberInfoReq.getDefaultInstance()) return this;
      if (other.hasIndex()) {
        setIndex(other.getIndex());
      }
      if (other.hasUnionId()) {
        setUnionId(other.getUnionId());
      }
      if (other.hasWonTime()) {
        setWonTime(other.getWonTime());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasIndex()) {
        return false;
      }
      if (!hasUnionId()) {
        return false;
      }
      if (!hasWonTime()) {
        return false;
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              index_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              unionId_ = input.readInt64();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 24: {
              wonTime_ = input.readInt64();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int index_ ;
    /**
     * <code>required int32 index = 1;</code>
     * @return Whether the index field is set.
     */
    @java.lang.Override
    public boolean hasIndex() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 index = 1;</code>
     * @return The index.
     */
    @java.lang.Override
    public int getIndex() {
      return index_;
    }
    /**
     * <code>required int32 index = 1;</code>
     * @param value The index to set.
     * @return This builder for chaining.
     */
    public Builder setIndex(int value) {

      index_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 index = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearIndex() {
      bitField0_ = (bitField0_ & ~0x00000001);
      index_ = 0;
      onChanged();
      return this;
    }

    private long unionId_ ;
    /**
     * <code>required int64 unionId = 2;</code>
     * @return Whether the unionId field is set.
     */
    @java.lang.Override
    public boolean hasUnionId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>required int64 unionId = 2;</code>
     * @return The unionId.
     */
    @java.lang.Override
    public long getUnionId() {
      return unionId_;
    }
    /**
     * <code>required int64 unionId = 2;</code>
     * @param value The unionId to set.
     * @return This builder for chaining.
     */
    public Builder setUnionId(long value) {

      unionId_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>required int64 unionId = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearUnionId() {
      bitField0_ = (bitField0_ & ~0x00000002);
      unionId_ = 0L;
      onChanged();
      return this;
    }

    private long wonTime_ ;
    /**
     * <code>required int64 wonTime = 3;</code>
     * @return Whether the wonTime field is set.
     */
    @java.lang.Override
    public boolean hasWonTime() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>required int64 wonTime = 3;</code>
     * @return The wonTime.
     */
    @java.lang.Override
    public long getWonTime() {
      return wonTime_;
    }
    /**
     * <code>required int64 wonTime = 3;</code>
     * @param value The wonTime to set.
     * @return This builder for chaining.
     */
    public Builder setWonTime(long value) {

      wonTime_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>required int64 wonTime = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearWonTime() {
      bitField0_ = (bitField0_ & ~0x00000004);
      wonTime_ = 0L;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.HolyLandChampionMemberInfoReq)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.HolyLandChampionMemberInfoReq)
  private static final xddq.pb.HolyLandChampionMemberInfoReq DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.HolyLandChampionMemberInfoReq();
  }

  public static xddq.pb.HolyLandChampionMemberInfoReq getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<HolyLandChampionMemberInfoReq>
      PARSER = new com.google.protobuf.AbstractParser<HolyLandChampionMemberInfoReq>() {
    @java.lang.Override
    public HolyLandChampionMemberInfoReq parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<HolyLandChampionMemberInfoReq> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<HolyLandChampionMemberInfoReq> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.HolyLandChampionMemberInfoReq getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

