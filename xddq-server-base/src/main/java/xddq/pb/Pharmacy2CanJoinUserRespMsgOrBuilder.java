// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

public interface Pharmacy2CanJoinUserRespMsgOrBuilder extends
    // @@protoc_insertion_point(interface_extends:xddq.pb.Pharmacy2CanJoinUserRespMsg)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  boolean hasRet();
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  int getRet();

  /**
   * <code>optional .xddq.pb.Pharmacy2CookerTempMsg userInfo = 2;</code>
   * @return Whether the userInfo field is set.
   */
  boolean hasUserInfo();
  /**
   * <code>optional .xddq.pb.Pharmacy2CookerTempMsg userInfo = 2;</code>
   * @return The userInfo.
   */
  xddq.pb.Pharmacy2CookerTempMsg getUserInfo();
  /**
   * <code>optional .xddq.pb.Pharmacy2CookerTempMsg userInfo = 2;</code>
   */
  xddq.pb.Pharmacy2CookerTempMsgOrBuilder getUserInfoOrBuilder();
}
