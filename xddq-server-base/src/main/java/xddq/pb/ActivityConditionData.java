// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.ActivityConditionData}
 */
public final class ActivityConditionData extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.ActivityConditionData)
    ActivityConditionDataOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      ActivityConditionData.class.getName());
  }
  // Use ActivityConditionData.newBuilder() to construct.
  private ActivityConditionData(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private ActivityConditionData() {
    value_ = "";
    multiReward_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_ActivityConditionData_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_ActivityConditionData_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.ActivityConditionData.class, xddq.pb.ActivityConditionData.Builder.class);
  }

  private int bitField0_;
  public static final int CONDITIONID_FIELD_NUMBER = 1;
  private int conditionId_ = 0;
  /**
   * <code>optional int32 conditionId = 1;</code>
   * @return Whether the conditionId field is set.
   */
  @java.lang.Override
  public boolean hasConditionId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int32 conditionId = 1;</code>
   * @return The conditionId.
   */
  @java.lang.Override
  public int getConditionId() {
    return conditionId_;
  }

  public static final int VALUE_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private volatile java.lang.Object value_ = "";
  /**
   * <code>optional string value = 2;</code>
   * @return Whether the value field is set.
   */
  @java.lang.Override
  public boolean hasValue() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional string value = 2;</code>
   * @return The value.
   */
  @java.lang.Override
  public java.lang.String getValue() {
    java.lang.Object ref = value_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        value_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string value = 2;</code>
   * @return The bytes for value.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getValueBytes() {
    java.lang.Object ref = value_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      value_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ISGETREWARD_FIELD_NUMBER = 3;
  private boolean isGetReward_ = false;
  /**
   * <code>optional bool isGetReward = 3;</code>
   * @return Whether the isGetReward field is set.
   */
  @java.lang.Override
  public boolean hasIsGetReward() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional bool isGetReward = 3;</code>
   * @return The isGetReward.
   */
  @java.lang.Override
  public boolean getIsGetReward() {
    return isGetReward_;
  }

  public static final int ISGETMASTERREWARDS_FIELD_NUMBER = 4;
  private boolean isGetMasterRewards_ = false;
  /**
   * <code>optional bool isGetMasterRewards = 4;</code>
   * @return Whether the isGetMasterRewards field is set.
   */
  @java.lang.Override
  public boolean hasIsGetMasterRewards() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional bool isGetMasterRewards = 4;</code>
   * @return The isGetMasterRewards.
   */
  @java.lang.Override
  public boolean getIsGetMasterRewards() {
    return isGetMasterRewards_;
  }

  public static final int MULTIREWARD_FIELD_NUMBER = 5;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.ActivityMultiReward> multiReward_;
  /**
   * <code>repeated .xddq.pb.ActivityMultiReward multiReward = 5;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.ActivityMultiReward> getMultiRewardList() {
    return multiReward_;
  }
  /**
   * <code>repeated .xddq.pb.ActivityMultiReward multiReward = 5;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.ActivityMultiRewardOrBuilder> 
      getMultiRewardOrBuilderList() {
    return multiReward_;
  }
  /**
   * <code>repeated .xddq.pb.ActivityMultiReward multiReward = 5;</code>
   */
  @java.lang.Override
  public int getMultiRewardCount() {
    return multiReward_.size();
  }
  /**
   * <code>repeated .xddq.pb.ActivityMultiReward multiReward = 5;</code>
   */
  @java.lang.Override
  public xddq.pb.ActivityMultiReward getMultiReward(int index) {
    return multiReward_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.ActivityMultiReward multiReward = 5;</code>
   */
  @java.lang.Override
  public xddq.pb.ActivityMultiRewardOrBuilder getMultiRewardOrBuilder(
      int index) {
    return multiReward_.get(index);
  }

  public static final int INFINITES_FIELD_NUMBER = 6;
  private int infinites_ = 0;
  /**
   * <code>optional int32 infinites = 6;</code>
   * @return Whether the infinites field is set.
   */
  @java.lang.Override
  public boolean hasInfinites() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>optional int32 infinites = 6;</code>
   * @return The infinites.
   */
  @java.lang.Override
  public int getInfinites() {
    return infinites_;
  }

  public static final int TRIGGERDROPTIMES_FIELD_NUMBER = 7;
  private int triggerDropTimes_ = 0;
  /**
   * <code>optional int32 triggerDropTimes = 7;</code>
   * @return Whether the triggerDropTimes field is set.
   */
  @java.lang.Override
  public boolean hasTriggerDropTimes() {
    return ((bitField0_ & 0x00000020) != 0);
  }
  /**
   * <code>optional int32 triggerDropTimes = 7;</code>
   * @return The triggerDropTimes.
   */
  @java.lang.Override
  public int getTriggerDropTimes() {
    return triggerDropTimes_;
  }

  public static final int COMPLETETIME_FIELD_NUMBER = 8;
  private long completeTime_ = 0L;
  /**
   * <code>optional int64 completeTime = 8;</code>
   * @return Whether the completeTime field is set.
   */
  @java.lang.Override
  public boolean hasCompleteTime() {
    return ((bitField0_ & 0x00000040) != 0);
  }
  /**
   * <code>optional int64 completeTime = 8;</code>
   * @return The completeTime.
   */
  @java.lang.Override
  public long getCompleteTime() {
    return completeTime_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, conditionId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 2, value_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeBool(3, isGetReward_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeBool(4, isGetMasterRewards_);
    }
    for (int i = 0; i < multiReward_.size(); i++) {
      output.writeMessage(5, multiReward_.get(i));
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      output.writeInt32(6, infinites_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      output.writeInt32(7, triggerDropTimes_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      output.writeInt64(8, completeTime_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, conditionId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(2, value_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(3, isGetReward_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(4, isGetMasterRewards_);
    }
    for (int i = 0; i < multiReward_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(5, multiReward_.get(i));
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(6, infinites_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(7, triggerDropTimes_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(8, completeTime_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.ActivityConditionData)) {
      return super.equals(obj);
    }
    xddq.pb.ActivityConditionData other = (xddq.pb.ActivityConditionData) obj;

    if (hasConditionId() != other.hasConditionId()) return false;
    if (hasConditionId()) {
      if (getConditionId()
          != other.getConditionId()) return false;
    }
    if (hasValue() != other.hasValue()) return false;
    if (hasValue()) {
      if (!getValue()
          .equals(other.getValue())) return false;
    }
    if (hasIsGetReward() != other.hasIsGetReward()) return false;
    if (hasIsGetReward()) {
      if (getIsGetReward()
          != other.getIsGetReward()) return false;
    }
    if (hasIsGetMasterRewards() != other.hasIsGetMasterRewards()) return false;
    if (hasIsGetMasterRewards()) {
      if (getIsGetMasterRewards()
          != other.getIsGetMasterRewards()) return false;
    }
    if (!getMultiRewardList()
        .equals(other.getMultiRewardList())) return false;
    if (hasInfinites() != other.hasInfinites()) return false;
    if (hasInfinites()) {
      if (getInfinites()
          != other.getInfinites()) return false;
    }
    if (hasTriggerDropTimes() != other.hasTriggerDropTimes()) return false;
    if (hasTriggerDropTimes()) {
      if (getTriggerDropTimes()
          != other.getTriggerDropTimes()) return false;
    }
    if (hasCompleteTime() != other.hasCompleteTime()) return false;
    if (hasCompleteTime()) {
      if (getCompleteTime()
          != other.getCompleteTime()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasConditionId()) {
      hash = (37 * hash) + CONDITIONID_FIELD_NUMBER;
      hash = (53 * hash) + getConditionId();
    }
    if (hasValue()) {
      hash = (37 * hash) + VALUE_FIELD_NUMBER;
      hash = (53 * hash) + getValue().hashCode();
    }
    if (hasIsGetReward()) {
      hash = (37 * hash) + ISGETREWARD_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getIsGetReward());
    }
    if (hasIsGetMasterRewards()) {
      hash = (37 * hash) + ISGETMASTERREWARDS_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getIsGetMasterRewards());
    }
    if (getMultiRewardCount() > 0) {
      hash = (37 * hash) + MULTIREWARD_FIELD_NUMBER;
      hash = (53 * hash) + getMultiRewardList().hashCode();
    }
    if (hasInfinites()) {
      hash = (37 * hash) + INFINITES_FIELD_NUMBER;
      hash = (53 * hash) + getInfinites();
    }
    if (hasTriggerDropTimes()) {
      hash = (37 * hash) + TRIGGERDROPTIMES_FIELD_NUMBER;
      hash = (53 * hash) + getTriggerDropTimes();
    }
    if (hasCompleteTime()) {
      hash = (37 * hash) + COMPLETETIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getCompleteTime());
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.ActivityConditionData parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ActivityConditionData parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ActivityConditionData parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ActivityConditionData parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ActivityConditionData parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ActivityConditionData parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ActivityConditionData parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.ActivityConditionData parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.ActivityConditionData parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.ActivityConditionData parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.ActivityConditionData parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.ActivityConditionData parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.ActivityConditionData prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.ActivityConditionData}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.ActivityConditionData)
      xddq.pb.ActivityConditionDataOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ActivityConditionData_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ActivityConditionData_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.ActivityConditionData.class, xddq.pb.ActivityConditionData.Builder.class);
    }

    // Construct using xddq.pb.ActivityConditionData.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      conditionId_ = 0;
      value_ = "";
      isGetReward_ = false;
      isGetMasterRewards_ = false;
      if (multiRewardBuilder_ == null) {
        multiReward_ = java.util.Collections.emptyList();
      } else {
        multiReward_ = null;
        multiRewardBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000010);
      infinites_ = 0;
      triggerDropTimes_ = 0;
      completeTime_ = 0L;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ActivityConditionData_descriptor;
    }

    @java.lang.Override
    public xddq.pb.ActivityConditionData getDefaultInstanceForType() {
      return xddq.pb.ActivityConditionData.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.ActivityConditionData build() {
      xddq.pb.ActivityConditionData result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.ActivityConditionData buildPartial() {
      xddq.pb.ActivityConditionData result = new xddq.pb.ActivityConditionData(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.ActivityConditionData result) {
      if (multiRewardBuilder_ == null) {
        if (((bitField0_ & 0x00000010) != 0)) {
          multiReward_ = java.util.Collections.unmodifiableList(multiReward_);
          bitField0_ = (bitField0_ & ~0x00000010);
        }
        result.multiReward_ = multiReward_;
      } else {
        result.multiReward_ = multiRewardBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.ActivityConditionData result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.conditionId_ = conditionId_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.value_ = value_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.isGetReward_ = isGetReward_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.isGetMasterRewards_ = isGetMasterRewards_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.infinites_ = infinites_;
        to_bitField0_ |= 0x00000010;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.triggerDropTimes_ = triggerDropTimes_;
        to_bitField0_ |= 0x00000020;
      }
      if (((from_bitField0_ & 0x00000080) != 0)) {
        result.completeTime_ = completeTime_;
        to_bitField0_ |= 0x00000040;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.ActivityConditionData) {
        return mergeFrom((xddq.pb.ActivityConditionData)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.ActivityConditionData other) {
      if (other == xddq.pb.ActivityConditionData.getDefaultInstance()) return this;
      if (other.hasConditionId()) {
        setConditionId(other.getConditionId());
      }
      if (other.hasValue()) {
        value_ = other.value_;
        bitField0_ |= 0x00000002;
        onChanged();
      }
      if (other.hasIsGetReward()) {
        setIsGetReward(other.getIsGetReward());
      }
      if (other.hasIsGetMasterRewards()) {
        setIsGetMasterRewards(other.getIsGetMasterRewards());
      }
      if (multiRewardBuilder_ == null) {
        if (!other.multiReward_.isEmpty()) {
          if (multiReward_.isEmpty()) {
            multiReward_ = other.multiReward_;
            bitField0_ = (bitField0_ & ~0x00000010);
          } else {
            ensureMultiRewardIsMutable();
            multiReward_.addAll(other.multiReward_);
          }
          onChanged();
        }
      } else {
        if (!other.multiReward_.isEmpty()) {
          if (multiRewardBuilder_.isEmpty()) {
            multiRewardBuilder_.dispose();
            multiRewardBuilder_ = null;
            multiReward_ = other.multiReward_;
            bitField0_ = (bitField0_ & ~0x00000010);
            multiRewardBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetMultiRewardFieldBuilder() : null;
          } else {
            multiRewardBuilder_.addAllMessages(other.multiReward_);
          }
        }
      }
      if (other.hasInfinites()) {
        setInfinites(other.getInfinites());
      }
      if (other.hasTriggerDropTimes()) {
        setTriggerDropTimes(other.getTriggerDropTimes());
      }
      if (other.hasCompleteTime()) {
        setCompleteTime(other.getCompleteTime());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              conditionId_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              value_ = input.readBytes();
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 24: {
              isGetReward_ = input.readBool();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 32: {
              isGetMasterRewards_ = input.readBool();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            case 42: {
              xddq.pb.ActivityMultiReward m =
                  input.readMessage(
                      xddq.pb.ActivityMultiReward.parser(),
                      extensionRegistry);
              if (multiRewardBuilder_ == null) {
                ensureMultiRewardIsMutable();
                multiReward_.add(m);
              } else {
                multiRewardBuilder_.addMessage(m);
              }
              break;
            } // case 42
            case 48: {
              infinites_ = input.readInt32();
              bitField0_ |= 0x00000020;
              break;
            } // case 48
            case 56: {
              triggerDropTimes_ = input.readInt32();
              bitField0_ |= 0x00000040;
              break;
            } // case 56
            case 64: {
              completeTime_ = input.readInt64();
              bitField0_ |= 0x00000080;
              break;
            } // case 64
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int conditionId_ ;
    /**
     * <code>optional int32 conditionId = 1;</code>
     * @return Whether the conditionId field is set.
     */
    @java.lang.Override
    public boolean hasConditionId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 conditionId = 1;</code>
     * @return The conditionId.
     */
    @java.lang.Override
    public int getConditionId() {
      return conditionId_;
    }
    /**
     * <code>optional int32 conditionId = 1;</code>
     * @param value The conditionId to set.
     * @return This builder for chaining.
     */
    public Builder setConditionId(int value) {

      conditionId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 conditionId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearConditionId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      conditionId_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object value_ = "";
    /**
     * <code>optional string value = 2;</code>
     * @return Whether the value field is set.
     */
    public boolean hasValue() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional string value = 2;</code>
     * @return The value.
     */
    public java.lang.String getValue() {
      java.lang.Object ref = value_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          value_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string value = 2;</code>
     * @return The bytes for value.
     */
    public com.google.protobuf.ByteString
        getValueBytes() {
      java.lang.Object ref = value_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        value_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string value = 2;</code>
     * @param value The value to set.
     * @return This builder for chaining.
     */
    public Builder setValue(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      value_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional string value = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearValue() {
      value_ = getDefaultInstance().getValue();
      bitField0_ = (bitField0_ & ~0x00000002);
      onChanged();
      return this;
    }
    /**
     * <code>optional string value = 2;</code>
     * @param value The bytes for value to set.
     * @return This builder for chaining.
     */
    public Builder setValueBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      value_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }

    private boolean isGetReward_ ;
    /**
     * <code>optional bool isGetReward = 3;</code>
     * @return Whether the isGetReward field is set.
     */
    @java.lang.Override
    public boolean hasIsGetReward() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional bool isGetReward = 3;</code>
     * @return The isGetReward.
     */
    @java.lang.Override
    public boolean getIsGetReward() {
      return isGetReward_;
    }
    /**
     * <code>optional bool isGetReward = 3;</code>
     * @param value The isGetReward to set.
     * @return This builder for chaining.
     */
    public Builder setIsGetReward(boolean value) {

      isGetReward_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional bool isGetReward = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearIsGetReward() {
      bitField0_ = (bitField0_ & ~0x00000004);
      isGetReward_ = false;
      onChanged();
      return this;
    }

    private boolean isGetMasterRewards_ ;
    /**
     * <code>optional bool isGetMasterRewards = 4;</code>
     * @return Whether the isGetMasterRewards field is set.
     */
    @java.lang.Override
    public boolean hasIsGetMasterRewards() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional bool isGetMasterRewards = 4;</code>
     * @return The isGetMasterRewards.
     */
    @java.lang.Override
    public boolean getIsGetMasterRewards() {
      return isGetMasterRewards_;
    }
    /**
     * <code>optional bool isGetMasterRewards = 4;</code>
     * @param value The isGetMasterRewards to set.
     * @return This builder for chaining.
     */
    public Builder setIsGetMasterRewards(boolean value) {

      isGetMasterRewards_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional bool isGetMasterRewards = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearIsGetMasterRewards() {
      bitField0_ = (bitField0_ & ~0x00000008);
      isGetMasterRewards_ = false;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.ActivityMultiReward> multiReward_ =
      java.util.Collections.emptyList();
    private void ensureMultiRewardIsMutable() {
      if (!((bitField0_ & 0x00000010) != 0)) {
        multiReward_ = new java.util.ArrayList<xddq.pb.ActivityMultiReward>(multiReward_);
        bitField0_ |= 0x00000010;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.ActivityMultiReward, xddq.pb.ActivityMultiReward.Builder, xddq.pb.ActivityMultiRewardOrBuilder> multiRewardBuilder_;

    /**
     * <code>repeated .xddq.pb.ActivityMultiReward multiReward = 5;</code>
     */
    public java.util.List<xddq.pb.ActivityMultiReward> getMultiRewardList() {
      if (multiRewardBuilder_ == null) {
        return java.util.Collections.unmodifiableList(multiReward_);
      } else {
        return multiRewardBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.ActivityMultiReward multiReward = 5;</code>
     */
    public int getMultiRewardCount() {
      if (multiRewardBuilder_ == null) {
        return multiReward_.size();
      } else {
        return multiRewardBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.ActivityMultiReward multiReward = 5;</code>
     */
    public xddq.pb.ActivityMultiReward getMultiReward(int index) {
      if (multiRewardBuilder_ == null) {
        return multiReward_.get(index);
      } else {
        return multiRewardBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.ActivityMultiReward multiReward = 5;</code>
     */
    public Builder setMultiReward(
        int index, xddq.pb.ActivityMultiReward value) {
      if (multiRewardBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMultiRewardIsMutable();
        multiReward_.set(index, value);
        onChanged();
      } else {
        multiRewardBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ActivityMultiReward multiReward = 5;</code>
     */
    public Builder setMultiReward(
        int index, xddq.pb.ActivityMultiReward.Builder builderForValue) {
      if (multiRewardBuilder_ == null) {
        ensureMultiRewardIsMutable();
        multiReward_.set(index, builderForValue.build());
        onChanged();
      } else {
        multiRewardBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ActivityMultiReward multiReward = 5;</code>
     */
    public Builder addMultiReward(xddq.pb.ActivityMultiReward value) {
      if (multiRewardBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMultiRewardIsMutable();
        multiReward_.add(value);
        onChanged();
      } else {
        multiRewardBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ActivityMultiReward multiReward = 5;</code>
     */
    public Builder addMultiReward(
        int index, xddq.pb.ActivityMultiReward value) {
      if (multiRewardBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMultiRewardIsMutable();
        multiReward_.add(index, value);
        onChanged();
      } else {
        multiRewardBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ActivityMultiReward multiReward = 5;</code>
     */
    public Builder addMultiReward(
        xddq.pb.ActivityMultiReward.Builder builderForValue) {
      if (multiRewardBuilder_ == null) {
        ensureMultiRewardIsMutable();
        multiReward_.add(builderForValue.build());
        onChanged();
      } else {
        multiRewardBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ActivityMultiReward multiReward = 5;</code>
     */
    public Builder addMultiReward(
        int index, xddq.pb.ActivityMultiReward.Builder builderForValue) {
      if (multiRewardBuilder_ == null) {
        ensureMultiRewardIsMutable();
        multiReward_.add(index, builderForValue.build());
        onChanged();
      } else {
        multiRewardBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ActivityMultiReward multiReward = 5;</code>
     */
    public Builder addAllMultiReward(
        java.lang.Iterable<? extends xddq.pb.ActivityMultiReward> values) {
      if (multiRewardBuilder_ == null) {
        ensureMultiRewardIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, multiReward_);
        onChanged();
      } else {
        multiRewardBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ActivityMultiReward multiReward = 5;</code>
     */
    public Builder clearMultiReward() {
      if (multiRewardBuilder_ == null) {
        multiReward_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000010);
        onChanged();
      } else {
        multiRewardBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ActivityMultiReward multiReward = 5;</code>
     */
    public Builder removeMultiReward(int index) {
      if (multiRewardBuilder_ == null) {
        ensureMultiRewardIsMutable();
        multiReward_.remove(index);
        onChanged();
      } else {
        multiRewardBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ActivityMultiReward multiReward = 5;</code>
     */
    public xddq.pb.ActivityMultiReward.Builder getMultiRewardBuilder(
        int index) {
      return internalGetMultiRewardFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.ActivityMultiReward multiReward = 5;</code>
     */
    public xddq.pb.ActivityMultiRewardOrBuilder getMultiRewardOrBuilder(
        int index) {
      if (multiRewardBuilder_ == null) {
        return multiReward_.get(index);  } else {
        return multiRewardBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.ActivityMultiReward multiReward = 5;</code>
     */
    public java.util.List<? extends xddq.pb.ActivityMultiRewardOrBuilder> 
         getMultiRewardOrBuilderList() {
      if (multiRewardBuilder_ != null) {
        return multiRewardBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(multiReward_);
      }
    }
    /**
     * <code>repeated .xddq.pb.ActivityMultiReward multiReward = 5;</code>
     */
    public xddq.pb.ActivityMultiReward.Builder addMultiRewardBuilder() {
      return internalGetMultiRewardFieldBuilder().addBuilder(
          xddq.pb.ActivityMultiReward.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.ActivityMultiReward multiReward = 5;</code>
     */
    public xddq.pb.ActivityMultiReward.Builder addMultiRewardBuilder(
        int index) {
      return internalGetMultiRewardFieldBuilder().addBuilder(
          index, xddq.pb.ActivityMultiReward.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.ActivityMultiReward multiReward = 5;</code>
     */
    public java.util.List<xddq.pb.ActivityMultiReward.Builder> 
         getMultiRewardBuilderList() {
      return internalGetMultiRewardFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.ActivityMultiReward, xddq.pb.ActivityMultiReward.Builder, xddq.pb.ActivityMultiRewardOrBuilder> 
        internalGetMultiRewardFieldBuilder() {
      if (multiRewardBuilder_ == null) {
        multiRewardBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.ActivityMultiReward, xddq.pb.ActivityMultiReward.Builder, xddq.pb.ActivityMultiRewardOrBuilder>(
                multiReward_,
                ((bitField0_ & 0x00000010) != 0),
                getParentForChildren(),
                isClean());
        multiReward_ = null;
      }
      return multiRewardBuilder_;
    }

    private int infinites_ ;
    /**
     * <code>optional int32 infinites = 6;</code>
     * @return Whether the infinites field is set.
     */
    @java.lang.Override
    public boolean hasInfinites() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional int32 infinites = 6;</code>
     * @return The infinites.
     */
    @java.lang.Override
    public int getInfinites() {
      return infinites_;
    }
    /**
     * <code>optional int32 infinites = 6;</code>
     * @param value The infinites to set.
     * @return This builder for chaining.
     */
    public Builder setInfinites(int value) {

      infinites_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 infinites = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearInfinites() {
      bitField0_ = (bitField0_ & ~0x00000020);
      infinites_ = 0;
      onChanged();
      return this;
    }

    private int triggerDropTimes_ ;
    /**
     * <code>optional int32 triggerDropTimes = 7;</code>
     * @return Whether the triggerDropTimes field is set.
     */
    @java.lang.Override
    public boolean hasTriggerDropTimes() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <code>optional int32 triggerDropTimes = 7;</code>
     * @return The triggerDropTimes.
     */
    @java.lang.Override
    public int getTriggerDropTimes() {
      return triggerDropTimes_;
    }
    /**
     * <code>optional int32 triggerDropTimes = 7;</code>
     * @param value The triggerDropTimes to set.
     * @return This builder for chaining.
     */
    public Builder setTriggerDropTimes(int value) {

      triggerDropTimes_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 triggerDropTimes = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearTriggerDropTimes() {
      bitField0_ = (bitField0_ & ~0x00000040);
      triggerDropTimes_ = 0;
      onChanged();
      return this;
    }

    private long completeTime_ ;
    /**
     * <code>optional int64 completeTime = 8;</code>
     * @return Whether the completeTime field is set.
     */
    @java.lang.Override
    public boolean hasCompleteTime() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <code>optional int64 completeTime = 8;</code>
     * @return The completeTime.
     */
    @java.lang.Override
    public long getCompleteTime() {
      return completeTime_;
    }
    /**
     * <code>optional int64 completeTime = 8;</code>
     * @param value The completeTime to set.
     * @return This builder for chaining.
     */
    public Builder setCompleteTime(long value) {

      completeTime_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 completeTime = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearCompleteTime() {
      bitField0_ = (bitField0_ & ~0x00000080);
      completeTime_ = 0L;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.ActivityConditionData)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.ActivityConditionData)
  private static final xddq.pb.ActivityConditionData DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.ActivityConditionData();
  }

  public static xddq.pb.ActivityConditionData getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ActivityConditionData>
      PARSER = new com.google.protobuf.AbstractParser<ActivityConditionData>() {
    @java.lang.Override
    public ActivityConditionData parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<ActivityConditionData> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ActivityConditionData> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.ActivityConditionData getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

