// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.HeavenBattleMonsterConfigMsg}
 */
public final class HeavenBattleMonsterConfigMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.HeavenBattleMonsterConfigMsg)
    HeavenBattleMonsterConfigMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      HeavenBattleMonsterConfigMsg.class.getName());
  }
  // Use HeavenBattleMonsterConfigMsg.newBuilder() to construct.
  private HeavenBattleMonsterConfigMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private HeavenBattleMonsterConfigMsg() {
    name_ = "";
    desc_ = "";
    appearanceId_ = "";
    icon_ = "";
    sound_ = "";
    lines_ = "";
    attackType_ = "";
    attackExcursion_ = "";
    attackHitExcursion_ = "";
    attack_ = "";
    hp_ = "";
    def_ = "";
    speed_ = "";
    attributeWeight_ = "";
    resistanceWeight_ = "";
    specialPropertyParam_ = "";
    petId_ = "";
    petSkillLevel_ = "";
    pithyId_ = "";
    pithySkillLevel_ = "";
    skillId_ = "";
    skillLevel_ = "";
    bloodSkill_ = "";
    specialSkill_ = "";
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_HeavenBattleMonsterConfigMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_HeavenBattleMonsterConfigMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.HeavenBattleMonsterConfigMsg.class, xddq.pb.HeavenBattleMonsterConfigMsg.Builder.class);
  }

  private int bitField0_;
  public static final int ACTIVITYID_FIELD_NUMBER = 1;
  private int activityId_ = 0;
  /**
   * <code>optional int32 activityId = 1;</code>
   * @return Whether the activityId field is set.
   */
  @java.lang.Override
  public boolean hasActivityId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int32 activityId = 1;</code>
   * @return The activityId.
   */
  @java.lang.Override
  public int getActivityId() {
    return activityId_;
  }

  public static final int ID_FIELD_NUMBER = 2;
  private int id_ = 0;
  /**
   * <code>optional int32 id = 2;</code>
   * @return Whether the id field is set.
   */
  @java.lang.Override
  public boolean hasId() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int32 id = 2;</code>
   * @return The id.
   */
  @java.lang.Override
  public int getId() {
    return id_;
  }

  public static final int ROUND_FIELD_NUMBER = 3;
  private int round_ = 0;
  /**
   * <code>optional int32 round = 3;</code>
   * @return Whether the round field is set.
   */
  @java.lang.Override
  public boolean hasRound() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional int32 round = 3;</code>
   * @return The round.
   */
  @java.lang.Override
  public int getRound() {
    return round_;
  }

  public static final int NAME_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private volatile java.lang.Object name_ = "";
  /**
   * <code>optional string name = 4;</code>
   * @return Whether the name field is set.
   */
  @java.lang.Override
  public boolean hasName() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional string name = 4;</code>
   * @return The name.
   */
  @java.lang.Override
  public java.lang.String getName() {
    java.lang.Object ref = name_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        name_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string name = 4;</code>
   * @return The bytes for name.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getNameBytes() {
    java.lang.Object ref = name_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      name_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int DESC_FIELD_NUMBER = 5;
  @SuppressWarnings("serial")
  private volatile java.lang.Object desc_ = "";
  /**
   * <code>optional string desc = 5;</code>
   * @return Whether the desc field is set.
   */
  @java.lang.Override
  public boolean hasDesc() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>optional string desc = 5;</code>
   * @return The desc.
   */
  @java.lang.Override
  public java.lang.String getDesc() {
    java.lang.Object ref = desc_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        desc_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string desc = 5;</code>
   * @return The bytes for desc.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getDescBytes() {
    java.lang.Object ref = desc_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      desc_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int APPEARANCEID_FIELD_NUMBER = 6;
  @SuppressWarnings("serial")
  private volatile java.lang.Object appearanceId_ = "";
  /**
   * <code>optional string appearanceId = 6;</code>
   * @return Whether the appearanceId field is set.
   */
  @java.lang.Override
  public boolean hasAppearanceId() {
    return ((bitField0_ & 0x00000020) != 0);
  }
  /**
   * <code>optional string appearanceId = 6;</code>
   * @return The appearanceId.
   */
  @java.lang.Override
  public java.lang.String getAppearanceId() {
    java.lang.Object ref = appearanceId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        appearanceId_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string appearanceId = 6;</code>
   * @return The bytes for appearanceId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getAppearanceIdBytes() {
    java.lang.Object ref = appearanceId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      appearanceId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ICON_FIELD_NUMBER = 7;
  @SuppressWarnings("serial")
  private volatile java.lang.Object icon_ = "";
  /**
   * <code>optional string icon = 7;</code>
   * @return Whether the icon field is set.
   */
  @java.lang.Override
  public boolean hasIcon() {
    return ((bitField0_ & 0x00000040) != 0);
  }
  /**
   * <code>optional string icon = 7;</code>
   * @return The icon.
   */
  @java.lang.Override
  public java.lang.String getIcon() {
    java.lang.Object ref = icon_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        icon_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string icon = 7;</code>
   * @return The bytes for icon.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getIconBytes() {
    java.lang.Object ref = icon_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      icon_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int SOUND_FIELD_NUMBER = 8;
  @SuppressWarnings("serial")
  private volatile java.lang.Object sound_ = "";
  /**
   * <code>optional string sound = 8;</code>
   * @return Whether the sound field is set.
   */
  @java.lang.Override
  public boolean hasSound() {
    return ((bitField0_ & 0x00000080) != 0);
  }
  /**
   * <code>optional string sound = 8;</code>
   * @return The sound.
   */
  @java.lang.Override
  public java.lang.String getSound() {
    java.lang.Object ref = sound_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        sound_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string sound = 8;</code>
   * @return The bytes for sound.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getSoundBytes() {
    java.lang.Object ref = sound_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      sound_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int LINES_FIELD_NUMBER = 9;
  @SuppressWarnings("serial")
  private volatile java.lang.Object lines_ = "";
  /**
   * <code>optional string lines = 9;</code>
   * @return Whether the lines field is set.
   */
  @java.lang.Override
  public boolean hasLines() {
    return ((bitField0_ & 0x00000100) != 0);
  }
  /**
   * <code>optional string lines = 9;</code>
   * @return The lines.
   */
  @java.lang.Override
  public java.lang.String getLines() {
    java.lang.Object ref = lines_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        lines_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string lines = 9;</code>
   * @return The bytes for lines.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getLinesBytes() {
    java.lang.Object ref = lines_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      lines_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ATTACKTYPE_FIELD_NUMBER = 10;
  @SuppressWarnings("serial")
  private volatile java.lang.Object attackType_ = "";
  /**
   * <code>optional string attackType = 10;</code>
   * @return Whether the attackType field is set.
   */
  @java.lang.Override
  public boolean hasAttackType() {
    return ((bitField0_ & 0x00000200) != 0);
  }
  /**
   * <code>optional string attackType = 10;</code>
   * @return The attackType.
   */
  @java.lang.Override
  public java.lang.String getAttackType() {
    java.lang.Object ref = attackType_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        attackType_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string attackType = 10;</code>
   * @return The bytes for attackType.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getAttackTypeBytes() {
    java.lang.Object ref = attackType_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      attackType_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ATTACKEXCURSION_FIELD_NUMBER = 11;
  @SuppressWarnings("serial")
  private volatile java.lang.Object attackExcursion_ = "";
  /**
   * <code>optional string attackExcursion = 11;</code>
   * @return Whether the attackExcursion field is set.
   */
  @java.lang.Override
  public boolean hasAttackExcursion() {
    return ((bitField0_ & 0x00000400) != 0);
  }
  /**
   * <code>optional string attackExcursion = 11;</code>
   * @return The attackExcursion.
   */
  @java.lang.Override
  public java.lang.String getAttackExcursion() {
    java.lang.Object ref = attackExcursion_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        attackExcursion_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string attackExcursion = 11;</code>
   * @return The bytes for attackExcursion.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getAttackExcursionBytes() {
    java.lang.Object ref = attackExcursion_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      attackExcursion_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ATTACKHITEXCURSION_FIELD_NUMBER = 12;
  @SuppressWarnings("serial")
  private volatile java.lang.Object attackHitExcursion_ = "";
  /**
   * <code>optional string attackHitExcursion = 12;</code>
   * @return Whether the attackHitExcursion field is set.
   */
  @java.lang.Override
  public boolean hasAttackHitExcursion() {
    return ((bitField0_ & 0x00000800) != 0);
  }
  /**
   * <code>optional string attackHitExcursion = 12;</code>
   * @return The attackHitExcursion.
   */
  @java.lang.Override
  public java.lang.String getAttackHitExcursion() {
    java.lang.Object ref = attackHitExcursion_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        attackHitExcursion_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string attackHitExcursion = 12;</code>
   * @return The bytes for attackHitExcursion.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getAttackHitExcursionBytes() {
    java.lang.Object ref = attackHitExcursion_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      attackHitExcursion_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int BODYHEIGHT_FIELD_NUMBER = 13;
  private int bodyHeight_ = 0;
  /**
   * <code>optional int32 bodyHeight = 13;</code>
   * @return Whether the bodyHeight field is set.
   */
  @java.lang.Override
  public boolean hasBodyHeight() {
    return ((bitField0_ & 0x00001000) != 0);
  }
  /**
   * <code>optional int32 bodyHeight = 13;</code>
   * @return The bodyHeight.
   */
  @java.lang.Override
  public int getBodyHeight() {
    return bodyHeight_;
  }

  public static final int BODYSCALE_FIELD_NUMBER = 14;
  private int bodyScale_ = 0;
  /**
   * <code>optional int32 bodyScale = 14;</code>
   * @return Whether the bodyScale field is set.
   */
  @java.lang.Override
  public boolean hasBodyScale() {
    return ((bitField0_ & 0x00002000) != 0);
  }
  /**
   * <code>optional int32 bodyScale = 14;</code>
   * @return The bodyScale.
   */
  @java.lang.Override
  public int getBodyScale() {
    return bodyScale_;
  }

  public static final int ATTACKBODYSCALE_FIELD_NUMBER = 15;
  private int attackBodyScale_ = 0;
  /**
   * <code>optional int32 attackBodyScale = 15;</code>
   * @return Whether the attackBodyScale field is set.
   */
  @java.lang.Override
  public boolean hasAttackBodyScale() {
    return ((bitField0_ & 0x00004000) != 0);
  }
  /**
   * <code>optional int32 attackBodyScale = 15;</code>
   * @return The attackBodyScale.
   */
  @java.lang.Override
  public int getAttackBodyScale() {
    return attackBodyScale_;
  }

  public static final int ATTACK_FIELD_NUMBER = 16;
  @SuppressWarnings("serial")
  private volatile java.lang.Object attack_ = "";
  /**
   * <code>optional string attack = 16;</code>
   * @return Whether the attack field is set.
   */
  @java.lang.Override
  public boolean hasAttack() {
    return ((bitField0_ & 0x00008000) != 0);
  }
  /**
   * <code>optional string attack = 16;</code>
   * @return The attack.
   */
  @java.lang.Override
  public java.lang.String getAttack() {
    java.lang.Object ref = attack_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        attack_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string attack = 16;</code>
   * @return The bytes for attack.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getAttackBytes() {
    java.lang.Object ref = attack_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      attack_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int HP_FIELD_NUMBER = 17;
  @SuppressWarnings("serial")
  private volatile java.lang.Object hp_ = "";
  /**
   * <code>optional string hp = 17;</code>
   * @return Whether the hp field is set.
   */
  @java.lang.Override
  public boolean hasHp() {
    return ((bitField0_ & 0x00010000) != 0);
  }
  /**
   * <code>optional string hp = 17;</code>
   * @return The hp.
   */
  @java.lang.Override
  public java.lang.String getHp() {
    java.lang.Object ref = hp_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        hp_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string hp = 17;</code>
   * @return The bytes for hp.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getHpBytes() {
    java.lang.Object ref = hp_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      hp_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int DEF_FIELD_NUMBER = 18;
  @SuppressWarnings("serial")
  private volatile java.lang.Object def_ = "";
  /**
   * <code>optional string def = 18;</code>
   * @return Whether the def field is set.
   */
  @java.lang.Override
  public boolean hasDef() {
    return ((bitField0_ & 0x00020000) != 0);
  }
  /**
   * <code>optional string def = 18;</code>
   * @return The def.
   */
  @java.lang.Override
  public java.lang.String getDef() {
    java.lang.Object ref = def_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        def_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string def = 18;</code>
   * @return The bytes for def.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getDefBytes() {
    java.lang.Object ref = def_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      def_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int SPEED_FIELD_NUMBER = 19;
  @SuppressWarnings("serial")
  private volatile java.lang.Object speed_ = "";
  /**
   * <code>optional string speed = 19;</code>
   * @return Whether the speed field is set.
   */
  @java.lang.Override
  public boolean hasSpeed() {
    return ((bitField0_ & 0x00040000) != 0);
  }
  /**
   * <code>optional string speed = 19;</code>
   * @return The speed.
   */
  @java.lang.Override
  public java.lang.String getSpeed() {
    java.lang.Object ref = speed_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        speed_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string speed = 19;</code>
   * @return The bytes for speed.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getSpeedBytes() {
    java.lang.Object ref = speed_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      speed_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ATTRIBUTEWEIGHT_FIELD_NUMBER = 20;
  @SuppressWarnings("serial")
  private volatile java.lang.Object attributeWeight_ = "";
  /**
   * <code>optional string attributeWeight = 20;</code>
   * @return Whether the attributeWeight field is set.
   */
  @java.lang.Override
  public boolean hasAttributeWeight() {
    return ((bitField0_ & 0x00080000) != 0);
  }
  /**
   * <code>optional string attributeWeight = 20;</code>
   * @return The attributeWeight.
   */
  @java.lang.Override
  public java.lang.String getAttributeWeight() {
    java.lang.Object ref = attributeWeight_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        attributeWeight_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string attributeWeight = 20;</code>
   * @return The bytes for attributeWeight.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getAttributeWeightBytes() {
    java.lang.Object ref = attributeWeight_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      attributeWeight_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int RESISTANCEWEIGHT_FIELD_NUMBER = 21;
  @SuppressWarnings("serial")
  private volatile java.lang.Object resistanceWeight_ = "";
  /**
   * <code>optional string resistanceWeight = 21;</code>
   * @return Whether the resistanceWeight field is set.
   */
  @java.lang.Override
  public boolean hasResistanceWeight() {
    return ((bitField0_ & 0x00100000) != 0);
  }
  /**
   * <code>optional string resistanceWeight = 21;</code>
   * @return The resistanceWeight.
   */
  @java.lang.Override
  public java.lang.String getResistanceWeight() {
    java.lang.Object ref = resistanceWeight_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        resistanceWeight_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string resistanceWeight = 21;</code>
   * @return The bytes for resistanceWeight.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getResistanceWeightBytes() {
    java.lang.Object ref = resistanceWeight_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      resistanceWeight_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int SPECIALPROPERTYPARAM_FIELD_NUMBER = 22;
  @SuppressWarnings("serial")
  private volatile java.lang.Object specialPropertyParam_ = "";
  /**
   * <code>optional string specialPropertyParam = 22;</code>
   * @return Whether the specialPropertyParam field is set.
   */
  @java.lang.Override
  public boolean hasSpecialPropertyParam() {
    return ((bitField0_ & 0x00200000) != 0);
  }
  /**
   * <code>optional string specialPropertyParam = 22;</code>
   * @return The specialPropertyParam.
   */
  @java.lang.Override
  public java.lang.String getSpecialPropertyParam() {
    java.lang.Object ref = specialPropertyParam_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        specialPropertyParam_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string specialPropertyParam = 22;</code>
   * @return The bytes for specialPropertyParam.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getSpecialPropertyParamBytes() {
    java.lang.Object ref = specialPropertyParam_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      specialPropertyParam_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ATTRPROB_FIELD_NUMBER = 23;
  private int attrProb_ = 0;
  /**
   * <code>optional int32 attrProb = 23;</code>
   * @return Whether the attrProb field is set.
   */
  @java.lang.Override
  public boolean hasAttrProb() {
    return ((bitField0_ & 0x00400000) != 0);
  }
  /**
   * <code>optional int32 attrProb = 23;</code>
   * @return The attrProb.
   */
  @java.lang.Override
  public int getAttrProb() {
    return attrProb_;
  }

  public static final int PETID_FIELD_NUMBER = 24;
  @SuppressWarnings("serial")
  private volatile java.lang.Object petId_ = "";
  /**
   * <code>optional string petId = 24;</code>
   * @return Whether the petId field is set.
   */
  @java.lang.Override
  public boolean hasPetId() {
    return ((bitField0_ & 0x00800000) != 0);
  }
  /**
   * <code>optional string petId = 24;</code>
   * @return The petId.
   */
  @java.lang.Override
  public java.lang.String getPetId() {
    java.lang.Object ref = petId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        petId_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string petId = 24;</code>
   * @return The bytes for petId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getPetIdBytes() {
    java.lang.Object ref = petId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      petId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PETSKILLLEVEL_FIELD_NUMBER = 25;
  @SuppressWarnings("serial")
  private volatile java.lang.Object petSkillLevel_ = "";
  /**
   * <code>optional string petSkillLevel = 25;</code>
   * @return Whether the petSkillLevel field is set.
   */
  @java.lang.Override
  public boolean hasPetSkillLevel() {
    return ((bitField0_ & 0x01000000) != 0);
  }
  /**
   * <code>optional string petSkillLevel = 25;</code>
   * @return The petSkillLevel.
   */
  @java.lang.Override
  public java.lang.String getPetSkillLevel() {
    java.lang.Object ref = petSkillLevel_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        petSkillLevel_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string petSkillLevel = 25;</code>
   * @return The bytes for petSkillLevel.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getPetSkillLevelBytes() {
    java.lang.Object ref = petSkillLevel_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      petSkillLevel_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PITHYID_FIELD_NUMBER = 26;
  @SuppressWarnings("serial")
  private volatile java.lang.Object pithyId_ = "";
  /**
   * <code>optional string pithyId = 26;</code>
   * @return Whether the pithyId field is set.
   */
  @java.lang.Override
  public boolean hasPithyId() {
    return ((bitField0_ & 0x02000000) != 0);
  }
  /**
   * <code>optional string pithyId = 26;</code>
   * @return The pithyId.
   */
  @java.lang.Override
  public java.lang.String getPithyId() {
    java.lang.Object ref = pithyId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        pithyId_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string pithyId = 26;</code>
   * @return The bytes for pithyId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getPithyIdBytes() {
    java.lang.Object ref = pithyId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      pithyId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PITHYSKILLLEVEL_FIELD_NUMBER = 27;
  @SuppressWarnings("serial")
  private volatile java.lang.Object pithySkillLevel_ = "";
  /**
   * <code>optional string pithySkillLevel = 27;</code>
   * @return Whether the pithySkillLevel field is set.
   */
  @java.lang.Override
  public boolean hasPithySkillLevel() {
    return ((bitField0_ & 0x04000000) != 0);
  }
  /**
   * <code>optional string pithySkillLevel = 27;</code>
   * @return The pithySkillLevel.
   */
  @java.lang.Override
  public java.lang.String getPithySkillLevel() {
    java.lang.Object ref = pithySkillLevel_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        pithySkillLevel_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string pithySkillLevel = 27;</code>
   * @return The bytes for pithySkillLevel.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getPithySkillLevelBytes() {
    java.lang.Object ref = pithySkillLevel_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      pithySkillLevel_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int SKILLID_FIELD_NUMBER = 28;
  @SuppressWarnings("serial")
  private volatile java.lang.Object skillId_ = "";
  /**
   * <code>optional string skillId = 28;</code>
   * @return Whether the skillId field is set.
   */
  @java.lang.Override
  public boolean hasSkillId() {
    return ((bitField0_ & 0x08000000) != 0);
  }
  /**
   * <code>optional string skillId = 28;</code>
   * @return The skillId.
   */
  @java.lang.Override
  public java.lang.String getSkillId() {
    java.lang.Object ref = skillId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        skillId_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string skillId = 28;</code>
   * @return The bytes for skillId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getSkillIdBytes() {
    java.lang.Object ref = skillId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      skillId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int SKILLLEVEL_FIELD_NUMBER = 29;
  @SuppressWarnings("serial")
  private volatile java.lang.Object skillLevel_ = "";
  /**
   * <code>optional string skillLevel = 29;</code>
   * @return Whether the skillLevel field is set.
   */
  @java.lang.Override
  public boolean hasSkillLevel() {
    return ((bitField0_ & 0x10000000) != 0);
  }
  /**
   * <code>optional string skillLevel = 29;</code>
   * @return The skillLevel.
   */
  @java.lang.Override
  public java.lang.String getSkillLevel() {
    java.lang.Object ref = skillLevel_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        skillLevel_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string skillLevel = 29;</code>
   * @return The bytes for skillLevel.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getSkillLevelBytes() {
    java.lang.Object ref = skillLevel_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      skillLevel_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int BLOODSKILL_FIELD_NUMBER = 30;
  @SuppressWarnings("serial")
  private volatile java.lang.Object bloodSkill_ = "";
  /**
   * <code>optional string bloodSkill = 30;</code>
   * @return Whether the bloodSkill field is set.
   */
  @java.lang.Override
  public boolean hasBloodSkill() {
    return ((bitField0_ & 0x20000000) != 0);
  }
  /**
   * <code>optional string bloodSkill = 30;</code>
   * @return The bloodSkill.
   */
  @java.lang.Override
  public java.lang.String getBloodSkill() {
    java.lang.Object ref = bloodSkill_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        bloodSkill_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string bloodSkill = 30;</code>
   * @return The bytes for bloodSkill.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getBloodSkillBytes() {
    java.lang.Object ref = bloodSkill_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      bloodSkill_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int SPECIALSKILL_FIELD_NUMBER = 31;
  @SuppressWarnings("serial")
  private volatile java.lang.Object specialSkill_ = "";
  /**
   * <code>optional string specialSkill = 31;</code>
   * @return Whether the specialSkill field is set.
   */
  @java.lang.Override
  public boolean hasSpecialSkill() {
    return ((bitField0_ & 0x40000000) != 0);
  }
  /**
   * <code>optional string specialSkill = 31;</code>
   * @return The specialSkill.
   */
  @java.lang.Override
  public java.lang.String getSpecialSkill() {
    java.lang.Object ref = specialSkill_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        specialSkill_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string specialSkill = 31;</code>
   * @return The bytes for specialSkill.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getSpecialSkillBytes() {
    java.lang.Object ref = specialSkill_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      specialSkill_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, activityId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(2, id_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt32(3, round_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 4, name_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 5, desc_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 6, appearanceId_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 7, icon_);
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 8, sound_);
    }
    if (((bitField0_ & 0x00000100) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 9, lines_);
    }
    if (((bitField0_ & 0x00000200) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 10, attackType_);
    }
    if (((bitField0_ & 0x00000400) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 11, attackExcursion_);
    }
    if (((bitField0_ & 0x00000800) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 12, attackHitExcursion_);
    }
    if (((bitField0_ & 0x00001000) != 0)) {
      output.writeInt32(13, bodyHeight_);
    }
    if (((bitField0_ & 0x00002000) != 0)) {
      output.writeInt32(14, bodyScale_);
    }
    if (((bitField0_ & 0x00004000) != 0)) {
      output.writeInt32(15, attackBodyScale_);
    }
    if (((bitField0_ & 0x00008000) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 16, attack_);
    }
    if (((bitField0_ & 0x00010000) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 17, hp_);
    }
    if (((bitField0_ & 0x00020000) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 18, def_);
    }
    if (((bitField0_ & 0x00040000) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 19, speed_);
    }
    if (((bitField0_ & 0x00080000) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 20, attributeWeight_);
    }
    if (((bitField0_ & 0x00100000) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 21, resistanceWeight_);
    }
    if (((bitField0_ & 0x00200000) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 22, specialPropertyParam_);
    }
    if (((bitField0_ & 0x00400000) != 0)) {
      output.writeInt32(23, attrProb_);
    }
    if (((bitField0_ & 0x00800000) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 24, petId_);
    }
    if (((bitField0_ & 0x01000000) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 25, petSkillLevel_);
    }
    if (((bitField0_ & 0x02000000) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 26, pithyId_);
    }
    if (((bitField0_ & 0x04000000) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 27, pithySkillLevel_);
    }
    if (((bitField0_ & 0x08000000) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 28, skillId_);
    }
    if (((bitField0_ & 0x10000000) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 29, skillLevel_);
    }
    if (((bitField0_ & 0x20000000) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 30, bloodSkill_);
    }
    if (((bitField0_ & 0x40000000) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 31, specialSkill_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, activityId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, id_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(3, round_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(4, name_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(5, desc_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(6, appearanceId_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(7, icon_);
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(8, sound_);
    }
    if (((bitField0_ & 0x00000100) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(9, lines_);
    }
    if (((bitField0_ & 0x00000200) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(10, attackType_);
    }
    if (((bitField0_ & 0x00000400) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(11, attackExcursion_);
    }
    if (((bitField0_ & 0x00000800) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(12, attackHitExcursion_);
    }
    if (((bitField0_ & 0x00001000) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(13, bodyHeight_);
    }
    if (((bitField0_ & 0x00002000) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(14, bodyScale_);
    }
    if (((bitField0_ & 0x00004000) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(15, attackBodyScale_);
    }
    if (((bitField0_ & 0x00008000) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(16, attack_);
    }
    if (((bitField0_ & 0x00010000) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(17, hp_);
    }
    if (((bitField0_ & 0x00020000) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(18, def_);
    }
    if (((bitField0_ & 0x00040000) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(19, speed_);
    }
    if (((bitField0_ & 0x00080000) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(20, attributeWeight_);
    }
    if (((bitField0_ & 0x00100000) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(21, resistanceWeight_);
    }
    if (((bitField0_ & 0x00200000) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(22, specialPropertyParam_);
    }
    if (((bitField0_ & 0x00400000) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(23, attrProb_);
    }
    if (((bitField0_ & 0x00800000) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(24, petId_);
    }
    if (((bitField0_ & 0x01000000) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(25, petSkillLevel_);
    }
    if (((bitField0_ & 0x02000000) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(26, pithyId_);
    }
    if (((bitField0_ & 0x04000000) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(27, pithySkillLevel_);
    }
    if (((bitField0_ & 0x08000000) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(28, skillId_);
    }
    if (((bitField0_ & 0x10000000) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(29, skillLevel_);
    }
    if (((bitField0_ & 0x20000000) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(30, bloodSkill_);
    }
    if (((bitField0_ & 0x40000000) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(31, specialSkill_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.HeavenBattleMonsterConfigMsg)) {
      return super.equals(obj);
    }
    xddq.pb.HeavenBattleMonsterConfigMsg other = (xddq.pb.HeavenBattleMonsterConfigMsg) obj;

    if (hasActivityId() != other.hasActivityId()) return false;
    if (hasActivityId()) {
      if (getActivityId()
          != other.getActivityId()) return false;
    }
    if (hasId() != other.hasId()) return false;
    if (hasId()) {
      if (getId()
          != other.getId()) return false;
    }
    if (hasRound() != other.hasRound()) return false;
    if (hasRound()) {
      if (getRound()
          != other.getRound()) return false;
    }
    if (hasName() != other.hasName()) return false;
    if (hasName()) {
      if (!getName()
          .equals(other.getName())) return false;
    }
    if (hasDesc() != other.hasDesc()) return false;
    if (hasDesc()) {
      if (!getDesc()
          .equals(other.getDesc())) return false;
    }
    if (hasAppearanceId() != other.hasAppearanceId()) return false;
    if (hasAppearanceId()) {
      if (!getAppearanceId()
          .equals(other.getAppearanceId())) return false;
    }
    if (hasIcon() != other.hasIcon()) return false;
    if (hasIcon()) {
      if (!getIcon()
          .equals(other.getIcon())) return false;
    }
    if (hasSound() != other.hasSound()) return false;
    if (hasSound()) {
      if (!getSound()
          .equals(other.getSound())) return false;
    }
    if (hasLines() != other.hasLines()) return false;
    if (hasLines()) {
      if (!getLines()
          .equals(other.getLines())) return false;
    }
    if (hasAttackType() != other.hasAttackType()) return false;
    if (hasAttackType()) {
      if (!getAttackType()
          .equals(other.getAttackType())) return false;
    }
    if (hasAttackExcursion() != other.hasAttackExcursion()) return false;
    if (hasAttackExcursion()) {
      if (!getAttackExcursion()
          .equals(other.getAttackExcursion())) return false;
    }
    if (hasAttackHitExcursion() != other.hasAttackHitExcursion()) return false;
    if (hasAttackHitExcursion()) {
      if (!getAttackHitExcursion()
          .equals(other.getAttackHitExcursion())) return false;
    }
    if (hasBodyHeight() != other.hasBodyHeight()) return false;
    if (hasBodyHeight()) {
      if (getBodyHeight()
          != other.getBodyHeight()) return false;
    }
    if (hasBodyScale() != other.hasBodyScale()) return false;
    if (hasBodyScale()) {
      if (getBodyScale()
          != other.getBodyScale()) return false;
    }
    if (hasAttackBodyScale() != other.hasAttackBodyScale()) return false;
    if (hasAttackBodyScale()) {
      if (getAttackBodyScale()
          != other.getAttackBodyScale()) return false;
    }
    if (hasAttack() != other.hasAttack()) return false;
    if (hasAttack()) {
      if (!getAttack()
          .equals(other.getAttack())) return false;
    }
    if (hasHp() != other.hasHp()) return false;
    if (hasHp()) {
      if (!getHp()
          .equals(other.getHp())) return false;
    }
    if (hasDef() != other.hasDef()) return false;
    if (hasDef()) {
      if (!getDef()
          .equals(other.getDef())) return false;
    }
    if (hasSpeed() != other.hasSpeed()) return false;
    if (hasSpeed()) {
      if (!getSpeed()
          .equals(other.getSpeed())) return false;
    }
    if (hasAttributeWeight() != other.hasAttributeWeight()) return false;
    if (hasAttributeWeight()) {
      if (!getAttributeWeight()
          .equals(other.getAttributeWeight())) return false;
    }
    if (hasResistanceWeight() != other.hasResistanceWeight()) return false;
    if (hasResistanceWeight()) {
      if (!getResistanceWeight()
          .equals(other.getResistanceWeight())) return false;
    }
    if (hasSpecialPropertyParam() != other.hasSpecialPropertyParam()) return false;
    if (hasSpecialPropertyParam()) {
      if (!getSpecialPropertyParam()
          .equals(other.getSpecialPropertyParam())) return false;
    }
    if (hasAttrProb() != other.hasAttrProb()) return false;
    if (hasAttrProb()) {
      if (getAttrProb()
          != other.getAttrProb()) return false;
    }
    if (hasPetId() != other.hasPetId()) return false;
    if (hasPetId()) {
      if (!getPetId()
          .equals(other.getPetId())) return false;
    }
    if (hasPetSkillLevel() != other.hasPetSkillLevel()) return false;
    if (hasPetSkillLevel()) {
      if (!getPetSkillLevel()
          .equals(other.getPetSkillLevel())) return false;
    }
    if (hasPithyId() != other.hasPithyId()) return false;
    if (hasPithyId()) {
      if (!getPithyId()
          .equals(other.getPithyId())) return false;
    }
    if (hasPithySkillLevel() != other.hasPithySkillLevel()) return false;
    if (hasPithySkillLevel()) {
      if (!getPithySkillLevel()
          .equals(other.getPithySkillLevel())) return false;
    }
    if (hasSkillId() != other.hasSkillId()) return false;
    if (hasSkillId()) {
      if (!getSkillId()
          .equals(other.getSkillId())) return false;
    }
    if (hasSkillLevel() != other.hasSkillLevel()) return false;
    if (hasSkillLevel()) {
      if (!getSkillLevel()
          .equals(other.getSkillLevel())) return false;
    }
    if (hasBloodSkill() != other.hasBloodSkill()) return false;
    if (hasBloodSkill()) {
      if (!getBloodSkill()
          .equals(other.getBloodSkill())) return false;
    }
    if (hasSpecialSkill() != other.hasSpecialSkill()) return false;
    if (hasSpecialSkill()) {
      if (!getSpecialSkill()
          .equals(other.getSpecialSkill())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasActivityId()) {
      hash = (37 * hash) + ACTIVITYID_FIELD_NUMBER;
      hash = (53 * hash) + getActivityId();
    }
    if (hasId()) {
      hash = (37 * hash) + ID_FIELD_NUMBER;
      hash = (53 * hash) + getId();
    }
    if (hasRound()) {
      hash = (37 * hash) + ROUND_FIELD_NUMBER;
      hash = (53 * hash) + getRound();
    }
    if (hasName()) {
      hash = (37 * hash) + NAME_FIELD_NUMBER;
      hash = (53 * hash) + getName().hashCode();
    }
    if (hasDesc()) {
      hash = (37 * hash) + DESC_FIELD_NUMBER;
      hash = (53 * hash) + getDesc().hashCode();
    }
    if (hasAppearanceId()) {
      hash = (37 * hash) + APPEARANCEID_FIELD_NUMBER;
      hash = (53 * hash) + getAppearanceId().hashCode();
    }
    if (hasIcon()) {
      hash = (37 * hash) + ICON_FIELD_NUMBER;
      hash = (53 * hash) + getIcon().hashCode();
    }
    if (hasSound()) {
      hash = (37 * hash) + SOUND_FIELD_NUMBER;
      hash = (53 * hash) + getSound().hashCode();
    }
    if (hasLines()) {
      hash = (37 * hash) + LINES_FIELD_NUMBER;
      hash = (53 * hash) + getLines().hashCode();
    }
    if (hasAttackType()) {
      hash = (37 * hash) + ATTACKTYPE_FIELD_NUMBER;
      hash = (53 * hash) + getAttackType().hashCode();
    }
    if (hasAttackExcursion()) {
      hash = (37 * hash) + ATTACKEXCURSION_FIELD_NUMBER;
      hash = (53 * hash) + getAttackExcursion().hashCode();
    }
    if (hasAttackHitExcursion()) {
      hash = (37 * hash) + ATTACKHITEXCURSION_FIELD_NUMBER;
      hash = (53 * hash) + getAttackHitExcursion().hashCode();
    }
    if (hasBodyHeight()) {
      hash = (37 * hash) + BODYHEIGHT_FIELD_NUMBER;
      hash = (53 * hash) + getBodyHeight();
    }
    if (hasBodyScale()) {
      hash = (37 * hash) + BODYSCALE_FIELD_NUMBER;
      hash = (53 * hash) + getBodyScale();
    }
    if (hasAttackBodyScale()) {
      hash = (37 * hash) + ATTACKBODYSCALE_FIELD_NUMBER;
      hash = (53 * hash) + getAttackBodyScale();
    }
    if (hasAttack()) {
      hash = (37 * hash) + ATTACK_FIELD_NUMBER;
      hash = (53 * hash) + getAttack().hashCode();
    }
    if (hasHp()) {
      hash = (37 * hash) + HP_FIELD_NUMBER;
      hash = (53 * hash) + getHp().hashCode();
    }
    if (hasDef()) {
      hash = (37 * hash) + DEF_FIELD_NUMBER;
      hash = (53 * hash) + getDef().hashCode();
    }
    if (hasSpeed()) {
      hash = (37 * hash) + SPEED_FIELD_NUMBER;
      hash = (53 * hash) + getSpeed().hashCode();
    }
    if (hasAttributeWeight()) {
      hash = (37 * hash) + ATTRIBUTEWEIGHT_FIELD_NUMBER;
      hash = (53 * hash) + getAttributeWeight().hashCode();
    }
    if (hasResistanceWeight()) {
      hash = (37 * hash) + RESISTANCEWEIGHT_FIELD_NUMBER;
      hash = (53 * hash) + getResistanceWeight().hashCode();
    }
    if (hasSpecialPropertyParam()) {
      hash = (37 * hash) + SPECIALPROPERTYPARAM_FIELD_NUMBER;
      hash = (53 * hash) + getSpecialPropertyParam().hashCode();
    }
    if (hasAttrProb()) {
      hash = (37 * hash) + ATTRPROB_FIELD_NUMBER;
      hash = (53 * hash) + getAttrProb();
    }
    if (hasPetId()) {
      hash = (37 * hash) + PETID_FIELD_NUMBER;
      hash = (53 * hash) + getPetId().hashCode();
    }
    if (hasPetSkillLevel()) {
      hash = (37 * hash) + PETSKILLLEVEL_FIELD_NUMBER;
      hash = (53 * hash) + getPetSkillLevel().hashCode();
    }
    if (hasPithyId()) {
      hash = (37 * hash) + PITHYID_FIELD_NUMBER;
      hash = (53 * hash) + getPithyId().hashCode();
    }
    if (hasPithySkillLevel()) {
      hash = (37 * hash) + PITHYSKILLLEVEL_FIELD_NUMBER;
      hash = (53 * hash) + getPithySkillLevel().hashCode();
    }
    if (hasSkillId()) {
      hash = (37 * hash) + SKILLID_FIELD_NUMBER;
      hash = (53 * hash) + getSkillId().hashCode();
    }
    if (hasSkillLevel()) {
      hash = (37 * hash) + SKILLLEVEL_FIELD_NUMBER;
      hash = (53 * hash) + getSkillLevel().hashCode();
    }
    if (hasBloodSkill()) {
      hash = (37 * hash) + BLOODSKILL_FIELD_NUMBER;
      hash = (53 * hash) + getBloodSkill().hashCode();
    }
    if (hasSpecialSkill()) {
      hash = (37 * hash) + SPECIALSKILL_FIELD_NUMBER;
      hash = (53 * hash) + getSpecialSkill().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.HeavenBattleMonsterConfigMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.HeavenBattleMonsterConfigMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.HeavenBattleMonsterConfigMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.HeavenBattleMonsterConfigMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.HeavenBattleMonsterConfigMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.HeavenBattleMonsterConfigMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.HeavenBattleMonsterConfigMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.HeavenBattleMonsterConfigMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.HeavenBattleMonsterConfigMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.HeavenBattleMonsterConfigMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.HeavenBattleMonsterConfigMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.HeavenBattleMonsterConfigMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.HeavenBattleMonsterConfigMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.HeavenBattleMonsterConfigMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.HeavenBattleMonsterConfigMsg)
      xddq.pb.HeavenBattleMonsterConfigMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_HeavenBattleMonsterConfigMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_HeavenBattleMonsterConfigMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.HeavenBattleMonsterConfigMsg.class, xddq.pb.HeavenBattleMonsterConfigMsg.Builder.class);
    }

    // Construct using xddq.pb.HeavenBattleMonsterConfigMsg.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      activityId_ = 0;
      id_ = 0;
      round_ = 0;
      name_ = "";
      desc_ = "";
      appearanceId_ = "";
      icon_ = "";
      sound_ = "";
      lines_ = "";
      attackType_ = "";
      attackExcursion_ = "";
      attackHitExcursion_ = "";
      bodyHeight_ = 0;
      bodyScale_ = 0;
      attackBodyScale_ = 0;
      attack_ = "";
      hp_ = "";
      def_ = "";
      speed_ = "";
      attributeWeight_ = "";
      resistanceWeight_ = "";
      specialPropertyParam_ = "";
      attrProb_ = 0;
      petId_ = "";
      petSkillLevel_ = "";
      pithyId_ = "";
      pithySkillLevel_ = "";
      skillId_ = "";
      skillLevel_ = "";
      bloodSkill_ = "";
      specialSkill_ = "";
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_HeavenBattleMonsterConfigMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.HeavenBattleMonsterConfigMsg getDefaultInstanceForType() {
      return xddq.pb.HeavenBattleMonsterConfigMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.HeavenBattleMonsterConfigMsg build() {
      xddq.pb.HeavenBattleMonsterConfigMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.HeavenBattleMonsterConfigMsg buildPartial() {
      xddq.pb.HeavenBattleMonsterConfigMsg result = new xddq.pb.HeavenBattleMonsterConfigMsg(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.HeavenBattleMonsterConfigMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.activityId_ = activityId_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.id_ = id_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.round_ = round_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.name_ = name_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.desc_ = desc_;
        to_bitField0_ |= 0x00000010;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.appearanceId_ = appearanceId_;
        to_bitField0_ |= 0x00000020;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.icon_ = icon_;
        to_bitField0_ |= 0x00000040;
      }
      if (((from_bitField0_ & 0x00000080) != 0)) {
        result.sound_ = sound_;
        to_bitField0_ |= 0x00000080;
      }
      if (((from_bitField0_ & 0x00000100) != 0)) {
        result.lines_ = lines_;
        to_bitField0_ |= 0x00000100;
      }
      if (((from_bitField0_ & 0x00000200) != 0)) {
        result.attackType_ = attackType_;
        to_bitField0_ |= 0x00000200;
      }
      if (((from_bitField0_ & 0x00000400) != 0)) {
        result.attackExcursion_ = attackExcursion_;
        to_bitField0_ |= 0x00000400;
      }
      if (((from_bitField0_ & 0x00000800) != 0)) {
        result.attackHitExcursion_ = attackHitExcursion_;
        to_bitField0_ |= 0x00000800;
      }
      if (((from_bitField0_ & 0x00001000) != 0)) {
        result.bodyHeight_ = bodyHeight_;
        to_bitField0_ |= 0x00001000;
      }
      if (((from_bitField0_ & 0x00002000) != 0)) {
        result.bodyScale_ = bodyScale_;
        to_bitField0_ |= 0x00002000;
      }
      if (((from_bitField0_ & 0x00004000) != 0)) {
        result.attackBodyScale_ = attackBodyScale_;
        to_bitField0_ |= 0x00004000;
      }
      if (((from_bitField0_ & 0x00008000) != 0)) {
        result.attack_ = attack_;
        to_bitField0_ |= 0x00008000;
      }
      if (((from_bitField0_ & 0x00010000) != 0)) {
        result.hp_ = hp_;
        to_bitField0_ |= 0x00010000;
      }
      if (((from_bitField0_ & 0x00020000) != 0)) {
        result.def_ = def_;
        to_bitField0_ |= 0x00020000;
      }
      if (((from_bitField0_ & 0x00040000) != 0)) {
        result.speed_ = speed_;
        to_bitField0_ |= 0x00040000;
      }
      if (((from_bitField0_ & 0x00080000) != 0)) {
        result.attributeWeight_ = attributeWeight_;
        to_bitField0_ |= 0x00080000;
      }
      if (((from_bitField0_ & 0x00100000) != 0)) {
        result.resistanceWeight_ = resistanceWeight_;
        to_bitField0_ |= 0x00100000;
      }
      if (((from_bitField0_ & 0x00200000) != 0)) {
        result.specialPropertyParam_ = specialPropertyParam_;
        to_bitField0_ |= 0x00200000;
      }
      if (((from_bitField0_ & 0x00400000) != 0)) {
        result.attrProb_ = attrProb_;
        to_bitField0_ |= 0x00400000;
      }
      if (((from_bitField0_ & 0x00800000) != 0)) {
        result.petId_ = petId_;
        to_bitField0_ |= 0x00800000;
      }
      if (((from_bitField0_ & 0x01000000) != 0)) {
        result.petSkillLevel_ = petSkillLevel_;
        to_bitField0_ |= 0x01000000;
      }
      if (((from_bitField0_ & 0x02000000) != 0)) {
        result.pithyId_ = pithyId_;
        to_bitField0_ |= 0x02000000;
      }
      if (((from_bitField0_ & 0x04000000) != 0)) {
        result.pithySkillLevel_ = pithySkillLevel_;
        to_bitField0_ |= 0x04000000;
      }
      if (((from_bitField0_ & 0x08000000) != 0)) {
        result.skillId_ = skillId_;
        to_bitField0_ |= 0x08000000;
      }
      if (((from_bitField0_ & 0x10000000) != 0)) {
        result.skillLevel_ = skillLevel_;
        to_bitField0_ |= 0x10000000;
      }
      if (((from_bitField0_ & 0x20000000) != 0)) {
        result.bloodSkill_ = bloodSkill_;
        to_bitField0_ |= 0x20000000;
      }
      if (((from_bitField0_ & 0x40000000) != 0)) {
        result.specialSkill_ = specialSkill_;
        to_bitField0_ |= 0x40000000;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.HeavenBattleMonsterConfigMsg) {
        return mergeFrom((xddq.pb.HeavenBattleMonsterConfigMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.HeavenBattleMonsterConfigMsg other) {
      if (other == xddq.pb.HeavenBattleMonsterConfigMsg.getDefaultInstance()) return this;
      if (other.hasActivityId()) {
        setActivityId(other.getActivityId());
      }
      if (other.hasId()) {
        setId(other.getId());
      }
      if (other.hasRound()) {
        setRound(other.getRound());
      }
      if (other.hasName()) {
        name_ = other.name_;
        bitField0_ |= 0x00000008;
        onChanged();
      }
      if (other.hasDesc()) {
        desc_ = other.desc_;
        bitField0_ |= 0x00000010;
        onChanged();
      }
      if (other.hasAppearanceId()) {
        appearanceId_ = other.appearanceId_;
        bitField0_ |= 0x00000020;
        onChanged();
      }
      if (other.hasIcon()) {
        icon_ = other.icon_;
        bitField0_ |= 0x00000040;
        onChanged();
      }
      if (other.hasSound()) {
        sound_ = other.sound_;
        bitField0_ |= 0x00000080;
        onChanged();
      }
      if (other.hasLines()) {
        lines_ = other.lines_;
        bitField0_ |= 0x00000100;
        onChanged();
      }
      if (other.hasAttackType()) {
        attackType_ = other.attackType_;
        bitField0_ |= 0x00000200;
        onChanged();
      }
      if (other.hasAttackExcursion()) {
        attackExcursion_ = other.attackExcursion_;
        bitField0_ |= 0x00000400;
        onChanged();
      }
      if (other.hasAttackHitExcursion()) {
        attackHitExcursion_ = other.attackHitExcursion_;
        bitField0_ |= 0x00000800;
        onChanged();
      }
      if (other.hasBodyHeight()) {
        setBodyHeight(other.getBodyHeight());
      }
      if (other.hasBodyScale()) {
        setBodyScale(other.getBodyScale());
      }
      if (other.hasAttackBodyScale()) {
        setAttackBodyScale(other.getAttackBodyScale());
      }
      if (other.hasAttack()) {
        attack_ = other.attack_;
        bitField0_ |= 0x00008000;
        onChanged();
      }
      if (other.hasHp()) {
        hp_ = other.hp_;
        bitField0_ |= 0x00010000;
        onChanged();
      }
      if (other.hasDef()) {
        def_ = other.def_;
        bitField0_ |= 0x00020000;
        onChanged();
      }
      if (other.hasSpeed()) {
        speed_ = other.speed_;
        bitField0_ |= 0x00040000;
        onChanged();
      }
      if (other.hasAttributeWeight()) {
        attributeWeight_ = other.attributeWeight_;
        bitField0_ |= 0x00080000;
        onChanged();
      }
      if (other.hasResistanceWeight()) {
        resistanceWeight_ = other.resistanceWeight_;
        bitField0_ |= 0x00100000;
        onChanged();
      }
      if (other.hasSpecialPropertyParam()) {
        specialPropertyParam_ = other.specialPropertyParam_;
        bitField0_ |= 0x00200000;
        onChanged();
      }
      if (other.hasAttrProb()) {
        setAttrProb(other.getAttrProb());
      }
      if (other.hasPetId()) {
        petId_ = other.petId_;
        bitField0_ |= 0x00800000;
        onChanged();
      }
      if (other.hasPetSkillLevel()) {
        petSkillLevel_ = other.petSkillLevel_;
        bitField0_ |= 0x01000000;
        onChanged();
      }
      if (other.hasPithyId()) {
        pithyId_ = other.pithyId_;
        bitField0_ |= 0x02000000;
        onChanged();
      }
      if (other.hasPithySkillLevel()) {
        pithySkillLevel_ = other.pithySkillLevel_;
        bitField0_ |= 0x04000000;
        onChanged();
      }
      if (other.hasSkillId()) {
        skillId_ = other.skillId_;
        bitField0_ |= 0x08000000;
        onChanged();
      }
      if (other.hasSkillLevel()) {
        skillLevel_ = other.skillLevel_;
        bitField0_ |= 0x10000000;
        onChanged();
      }
      if (other.hasBloodSkill()) {
        bloodSkill_ = other.bloodSkill_;
        bitField0_ |= 0x20000000;
        onChanged();
      }
      if (other.hasSpecialSkill()) {
        specialSkill_ = other.specialSkill_;
        bitField0_ |= 0x40000000;
        onChanged();
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              activityId_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              id_ = input.readInt32();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 24: {
              round_ = input.readInt32();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 34: {
              name_ = input.readBytes();
              bitField0_ |= 0x00000008;
              break;
            } // case 34
            case 42: {
              desc_ = input.readBytes();
              bitField0_ |= 0x00000010;
              break;
            } // case 42
            case 50: {
              appearanceId_ = input.readBytes();
              bitField0_ |= 0x00000020;
              break;
            } // case 50
            case 58: {
              icon_ = input.readBytes();
              bitField0_ |= 0x00000040;
              break;
            } // case 58
            case 66: {
              sound_ = input.readBytes();
              bitField0_ |= 0x00000080;
              break;
            } // case 66
            case 74: {
              lines_ = input.readBytes();
              bitField0_ |= 0x00000100;
              break;
            } // case 74
            case 82: {
              attackType_ = input.readBytes();
              bitField0_ |= 0x00000200;
              break;
            } // case 82
            case 90: {
              attackExcursion_ = input.readBytes();
              bitField0_ |= 0x00000400;
              break;
            } // case 90
            case 98: {
              attackHitExcursion_ = input.readBytes();
              bitField0_ |= 0x00000800;
              break;
            } // case 98
            case 104: {
              bodyHeight_ = input.readInt32();
              bitField0_ |= 0x00001000;
              break;
            } // case 104
            case 112: {
              bodyScale_ = input.readInt32();
              bitField0_ |= 0x00002000;
              break;
            } // case 112
            case 120: {
              attackBodyScale_ = input.readInt32();
              bitField0_ |= 0x00004000;
              break;
            } // case 120
            case 130: {
              attack_ = input.readBytes();
              bitField0_ |= 0x00008000;
              break;
            } // case 130
            case 138: {
              hp_ = input.readBytes();
              bitField0_ |= 0x00010000;
              break;
            } // case 138
            case 146: {
              def_ = input.readBytes();
              bitField0_ |= 0x00020000;
              break;
            } // case 146
            case 154: {
              speed_ = input.readBytes();
              bitField0_ |= 0x00040000;
              break;
            } // case 154
            case 162: {
              attributeWeight_ = input.readBytes();
              bitField0_ |= 0x00080000;
              break;
            } // case 162
            case 170: {
              resistanceWeight_ = input.readBytes();
              bitField0_ |= 0x00100000;
              break;
            } // case 170
            case 178: {
              specialPropertyParam_ = input.readBytes();
              bitField0_ |= 0x00200000;
              break;
            } // case 178
            case 184: {
              attrProb_ = input.readInt32();
              bitField0_ |= 0x00400000;
              break;
            } // case 184
            case 194: {
              petId_ = input.readBytes();
              bitField0_ |= 0x00800000;
              break;
            } // case 194
            case 202: {
              petSkillLevel_ = input.readBytes();
              bitField0_ |= 0x01000000;
              break;
            } // case 202
            case 210: {
              pithyId_ = input.readBytes();
              bitField0_ |= 0x02000000;
              break;
            } // case 210
            case 218: {
              pithySkillLevel_ = input.readBytes();
              bitField0_ |= 0x04000000;
              break;
            } // case 218
            case 226: {
              skillId_ = input.readBytes();
              bitField0_ |= 0x08000000;
              break;
            } // case 226
            case 234: {
              skillLevel_ = input.readBytes();
              bitField0_ |= 0x10000000;
              break;
            } // case 234
            case 242: {
              bloodSkill_ = input.readBytes();
              bitField0_ |= 0x20000000;
              break;
            } // case 242
            case 250: {
              specialSkill_ = input.readBytes();
              bitField0_ |= 0x40000000;
              break;
            } // case 250
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int activityId_ ;
    /**
     * <code>optional int32 activityId = 1;</code>
     * @return Whether the activityId field is set.
     */
    @java.lang.Override
    public boolean hasActivityId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 activityId = 1;</code>
     * @return The activityId.
     */
    @java.lang.Override
    public int getActivityId() {
      return activityId_;
    }
    /**
     * <code>optional int32 activityId = 1;</code>
     * @param value The activityId to set.
     * @return This builder for chaining.
     */
    public Builder setActivityId(int value) {

      activityId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 activityId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearActivityId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      activityId_ = 0;
      onChanged();
      return this;
    }

    private int id_ ;
    /**
     * <code>optional int32 id = 2;</code>
     * @return Whether the id field is set.
     */
    @java.lang.Override
    public boolean hasId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 id = 2;</code>
     * @return The id.
     */
    @java.lang.Override
    public int getId() {
      return id_;
    }
    /**
     * <code>optional int32 id = 2;</code>
     * @param value The id to set.
     * @return This builder for chaining.
     */
    public Builder setId(int value) {

      id_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 id = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearId() {
      bitField0_ = (bitField0_ & ~0x00000002);
      id_ = 0;
      onChanged();
      return this;
    }

    private int round_ ;
    /**
     * <code>optional int32 round = 3;</code>
     * @return Whether the round field is set.
     */
    @java.lang.Override
    public boolean hasRound() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int32 round = 3;</code>
     * @return The round.
     */
    @java.lang.Override
    public int getRound() {
      return round_;
    }
    /**
     * <code>optional int32 round = 3;</code>
     * @param value The round to set.
     * @return This builder for chaining.
     */
    public Builder setRound(int value) {

      round_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 round = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearRound() {
      bitField0_ = (bitField0_ & ~0x00000004);
      round_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object name_ = "";
    /**
     * <code>optional string name = 4;</code>
     * @return Whether the name field is set.
     */
    public boolean hasName() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional string name = 4;</code>
     * @return The name.
     */
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          name_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string name = 4;</code>
     * @return The bytes for name.
     */
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string name = 4;</code>
     * @param value The name to set.
     * @return This builder for chaining.
     */
    public Builder setName(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      name_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional string name = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearName() {
      name_ = getDefaultInstance().getName();
      bitField0_ = (bitField0_ & ~0x00000008);
      onChanged();
      return this;
    }
    /**
     * <code>optional string name = 4;</code>
     * @param value The bytes for name to set.
     * @return This builder for chaining.
     */
    public Builder setNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      name_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }

    private java.lang.Object desc_ = "";
    /**
     * <code>optional string desc = 5;</code>
     * @return Whether the desc field is set.
     */
    public boolean hasDesc() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional string desc = 5;</code>
     * @return The desc.
     */
    public java.lang.String getDesc() {
      java.lang.Object ref = desc_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          desc_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string desc = 5;</code>
     * @return The bytes for desc.
     */
    public com.google.protobuf.ByteString
        getDescBytes() {
      java.lang.Object ref = desc_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        desc_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string desc = 5;</code>
     * @param value The desc to set.
     * @return This builder for chaining.
     */
    public Builder setDesc(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      desc_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional string desc = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearDesc() {
      desc_ = getDefaultInstance().getDesc();
      bitField0_ = (bitField0_ & ~0x00000010);
      onChanged();
      return this;
    }
    /**
     * <code>optional string desc = 5;</code>
     * @param value The bytes for desc to set.
     * @return This builder for chaining.
     */
    public Builder setDescBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      desc_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }

    private java.lang.Object appearanceId_ = "";
    /**
     * <code>optional string appearanceId = 6;</code>
     * @return Whether the appearanceId field is set.
     */
    public boolean hasAppearanceId() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional string appearanceId = 6;</code>
     * @return The appearanceId.
     */
    public java.lang.String getAppearanceId() {
      java.lang.Object ref = appearanceId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          appearanceId_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string appearanceId = 6;</code>
     * @return The bytes for appearanceId.
     */
    public com.google.protobuf.ByteString
        getAppearanceIdBytes() {
      java.lang.Object ref = appearanceId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        appearanceId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string appearanceId = 6;</code>
     * @param value The appearanceId to set.
     * @return This builder for chaining.
     */
    public Builder setAppearanceId(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      appearanceId_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>optional string appearanceId = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearAppearanceId() {
      appearanceId_ = getDefaultInstance().getAppearanceId();
      bitField0_ = (bitField0_ & ~0x00000020);
      onChanged();
      return this;
    }
    /**
     * <code>optional string appearanceId = 6;</code>
     * @param value The bytes for appearanceId to set.
     * @return This builder for chaining.
     */
    public Builder setAppearanceIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      appearanceId_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }

    private java.lang.Object icon_ = "";
    /**
     * <code>optional string icon = 7;</code>
     * @return Whether the icon field is set.
     */
    public boolean hasIcon() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <code>optional string icon = 7;</code>
     * @return The icon.
     */
    public java.lang.String getIcon() {
      java.lang.Object ref = icon_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          icon_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string icon = 7;</code>
     * @return The bytes for icon.
     */
    public com.google.protobuf.ByteString
        getIconBytes() {
      java.lang.Object ref = icon_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        icon_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string icon = 7;</code>
     * @param value The icon to set.
     * @return This builder for chaining.
     */
    public Builder setIcon(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      icon_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>optional string icon = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearIcon() {
      icon_ = getDefaultInstance().getIcon();
      bitField0_ = (bitField0_ & ~0x00000040);
      onChanged();
      return this;
    }
    /**
     * <code>optional string icon = 7;</code>
     * @param value The bytes for icon to set.
     * @return This builder for chaining.
     */
    public Builder setIconBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      icon_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }

    private java.lang.Object sound_ = "";
    /**
     * <code>optional string sound = 8;</code>
     * @return Whether the sound field is set.
     */
    public boolean hasSound() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <code>optional string sound = 8;</code>
     * @return The sound.
     */
    public java.lang.String getSound() {
      java.lang.Object ref = sound_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          sound_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string sound = 8;</code>
     * @return The bytes for sound.
     */
    public com.google.protobuf.ByteString
        getSoundBytes() {
      java.lang.Object ref = sound_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        sound_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string sound = 8;</code>
     * @param value The sound to set.
     * @return This builder for chaining.
     */
    public Builder setSound(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      sound_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }
    /**
     * <code>optional string sound = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearSound() {
      sound_ = getDefaultInstance().getSound();
      bitField0_ = (bitField0_ & ~0x00000080);
      onChanged();
      return this;
    }
    /**
     * <code>optional string sound = 8;</code>
     * @param value The bytes for sound to set.
     * @return This builder for chaining.
     */
    public Builder setSoundBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      sound_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }

    private java.lang.Object lines_ = "";
    /**
     * <code>optional string lines = 9;</code>
     * @return Whether the lines field is set.
     */
    public boolean hasLines() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <code>optional string lines = 9;</code>
     * @return The lines.
     */
    public java.lang.String getLines() {
      java.lang.Object ref = lines_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          lines_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string lines = 9;</code>
     * @return The bytes for lines.
     */
    public com.google.protobuf.ByteString
        getLinesBytes() {
      java.lang.Object ref = lines_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        lines_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string lines = 9;</code>
     * @param value The lines to set.
     * @return This builder for chaining.
     */
    public Builder setLines(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      lines_ = value;
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }
    /**
     * <code>optional string lines = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearLines() {
      lines_ = getDefaultInstance().getLines();
      bitField0_ = (bitField0_ & ~0x00000100);
      onChanged();
      return this;
    }
    /**
     * <code>optional string lines = 9;</code>
     * @param value The bytes for lines to set.
     * @return This builder for chaining.
     */
    public Builder setLinesBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      lines_ = value;
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }

    private java.lang.Object attackType_ = "";
    /**
     * <code>optional string attackType = 10;</code>
     * @return Whether the attackType field is set.
     */
    public boolean hasAttackType() {
      return ((bitField0_ & 0x00000200) != 0);
    }
    /**
     * <code>optional string attackType = 10;</code>
     * @return The attackType.
     */
    public java.lang.String getAttackType() {
      java.lang.Object ref = attackType_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          attackType_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string attackType = 10;</code>
     * @return The bytes for attackType.
     */
    public com.google.protobuf.ByteString
        getAttackTypeBytes() {
      java.lang.Object ref = attackType_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        attackType_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string attackType = 10;</code>
     * @param value The attackType to set.
     * @return This builder for chaining.
     */
    public Builder setAttackType(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      attackType_ = value;
      bitField0_ |= 0x00000200;
      onChanged();
      return this;
    }
    /**
     * <code>optional string attackType = 10;</code>
     * @return This builder for chaining.
     */
    public Builder clearAttackType() {
      attackType_ = getDefaultInstance().getAttackType();
      bitField0_ = (bitField0_ & ~0x00000200);
      onChanged();
      return this;
    }
    /**
     * <code>optional string attackType = 10;</code>
     * @param value The bytes for attackType to set.
     * @return This builder for chaining.
     */
    public Builder setAttackTypeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      attackType_ = value;
      bitField0_ |= 0x00000200;
      onChanged();
      return this;
    }

    private java.lang.Object attackExcursion_ = "";
    /**
     * <code>optional string attackExcursion = 11;</code>
     * @return Whether the attackExcursion field is set.
     */
    public boolean hasAttackExcursion() {
      return ((bitField0_ & 0x00000400) != 0);
    }
    /**
     * <code>optional string attackExcursion = 11;</code>
     * @return The attackExcursion.
     */
    public java.lang.String getAttackExcursion() {
      java.lang.Object ref = attackExcursion_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          attackExcursion_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string attackExcursion = 11;</code>
     * @return The bytes for attackExcursion.
     */
    public com.google.protobuf.ByteString
        getAttackExcursionBytes() {
      java.lang.Object ref = attackExcursion_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        attackExcursion_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string attackExcursion = 11;</code>
     * @param value The attackExcursion to set.
     * @return This builder for chaining.
     */
    public Builder setAttackExcursion(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      attackExcursion_ = value;
      bitField0_ |= 0x00000400;
      onChanged();
      return this;
    }
    /**
     * <code>optional string attackExcursion = 11;</code>
     * @return This builder for chaining.
     */
    public Builder clearAttackExcursion() {
      attackExcursion_ = getDefaultInstance().getAttackExcursion();
      bitField0_ = (bitField0_ & ~0x00000400);
      onChanged();
      return this;
    }
    /**
     * <code>optional string attackExcursion = 11;</code>
     * @param value The bytes for attackExcursion to set.
     * @return This builder for chaining.
     */
    public Builder setAttackExcursionBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      attackExcursion_ = value;
      bitField0_ |= 0x00000400;
      onChanged();
      return this;
    }

    private java.lang.Object attackHitExcursion_ = "";
    /**
     * <code>optional string attackHitExcursion = 12;</code>
     * @return Whether the attackHitExcursion field is set.
     */
    public boolean hasAttackHitExcursion() {
      return ((bitField0_ & 0x00000800) != 0);
    }
    /**
     * <code>optional string attackHitExcursion = 12;</code>
     * @return The attackHitExcursion.
     */
    public java.lang.String getAttackHitExcursion() {
      java.lang.Object ref = attackHitExcursion_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          attackHitExcursion_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string attackHitExcursion = 12;</code>
     * @return The bytes for attackHitExcursion.
     */
    public com.google.protobuf.ByteString
        getAttackHitExcursionBytes() {
      java.lang.Object ref = attackHitExcursion_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        attackHitExcursion_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string attackHitExcursion = 12;</code>
     * @param value The attackHitExcursion to set.
     * @return This builder for chaining.
     */
    public Builder setAttackHitExcursion(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      attackHitExcursion_ = value;
      bitField0_ |= 0x00000800;
      onChanged();
      return this;
    }
    /**
     * <code>optional string attackHitExcursion = 12;</code>
     * @return This builder for chaining.
     */
    public Builder clearAttackHitExcursion() {
      attackHitExcursion_ = getDefaultInstance().getAttackHitExcursion();
      bitField0_ = (bitField0_ & ~0x00000800);
      onChanged();
      return this;
    }
    /**
     * <code>optional string attackHitExcursion = 12;</code>
     * @param value The bytes for attackHitExcursion to set.
     * @return This builder for chaining.
     */
    public Builder setAttackHitExcursionBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      attackHitExcursion_ = value;
      bitField0_ |= 0x00000800;
      onChanged();
      return this;
    }

    private int bodyHeight_ ;
    /**
     * <code>optional int32 bodyHeight = 13;</code>
     * @return Whether the bodyHeight field is set.
     */
    @java.lang.Override
    public boolean hasBodyHeight() {
      return ((bitField0_ & 0x00001000) != 0);
    }
    /**
     * <code>optional int32 bodyHeight = 13;</code>
     * @return The bodyHeight.
     */
    @java.lang.Override
    public int getBodyHeight() {
      return bodyHeight_;
    }
    /**
     * <code>optional int32 bodyHeight = 13;</code>
     * @param value The bodyHeight to set.
     * @return This builder for chaining.
     */
    public Builder setBodyHeight(int value) {

      bodyHeight_ = value;
      bitField0_ |= 0x00001000;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 bodyHeight = 13;</code>
     * @return This builder for chaining.
     */
    public Builder clearBodyHeight() {
      bitField0_ = (bitField0_ & ~0x00001000);
      bodyHeight_ = 0;
      onChanged();
      return this;
    }

    private int bodyScale_ ;
    /**
     * <code>optional int32 bodyScale = 14;</code>
     * @return Whether the bodyScale field is set.
     */
    @java.lang.Override
    public boolean hasBodyScale() {
      return ((bitField0_ & 0x00002000) != 0);
    }
    /**
     * <code>optional int32 bodyScale = 14;</code>
     * @return The bodyScale.
     */
    @java.lang.Override
    public int getBodyScale() {
      return bodyScale_;
    }
    /**
     * <code>optional int32 bodyScale = 14;</code>
     * @param value The bodyScale to set.
     * @return This builder for chaining.
     */
    public Builder setBodyScale(int value) {

      bodyScale_ = value;
      bitField0_ |= 0x00002000;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 bodyScale = 14;</code>
     * @return This builder for chaining.
     */
    public Builder clearBodyScale() {
      bitField0_ = (bitField0_ & ~0x00002000);
      bodyScale_ = 0;
      onChanged();
      return this;
    }

    private int attackBodyScale_ ;
    /**
     * <code>optional int32 attackBodyScale = 15;</code>
     * @return Whether the attackBodyScale field is set.
     */
    @java.lang.Override
    public boolean hasAttackBodyScale() {
      return ((bitField0_ & 0x00004000) != 0);
    }
    /**
     * <code>optional int32 attackBodyScale = 15;</code>
     * @return The attackBodyScale.
     */
    @java.lang.Override
    public int getAttackBodyScale() {
      return attackBodyScale_;
    }
    /**
     * <code>optional int32 attackBodyScale = 15;</code>
     * @param value The attackBodyScale to set.
     * @return This builder for chaining.
     */
    public Builder setAttackBodyScale(int value) {

      attackBodyScale_ = value;
      bitField0_ |= 0x00004000;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 attackBodyScale = 15;</code>
     * @return This builder for chaining.
     */
    public Builder clearAttackBodyScale() {
      bitField0_ = (bitField0_ & ~0x00004000);
      attackBodyScale_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object attack_ = "";
    /**
     * <code>optional string attack = 16;</code>
     * @return Whether the attack field is set.
     */
    public boolean hasAttack() {
      return ((bitField0_ & 0x00008000) != 0);
    }
    /**
     * <code>optional string attack = 16;</code>
     * @return The attack.
     */
    public java.lang.String getAttack() {
      java.lang.Object ref = attack_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          attack_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string attack = 16;</code>
     * @return The bytes for attack.
     */
    public com.google.protobuf.ByteString
        getAttackBytes() {
      java.lang.Object ref = attack_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        attack_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string attack = 16;</code>
     * @param value The attack to set.
     * @return This builder for chaining.
     */
    public Builder setAttack(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      attack_ = value;
      bitField0_ |= 0x00008000;
      onChanged();
      return this;
    }
    /**
     * <code>optional string attack = 16;</code>
     * @return This builder for chaining.
     */
    public Builder clearAttack() {
      attack_ = getDefaultInstance().getAttack();
      bitField0_ = (bitField0_ & ~0x00008000);
      onChanged();
      return this;
    }
    /**
     * <code>optional string attack = 16;</code>
     * @param value The bytes for attack to set.
     * @return This builder for chaining.
     */
    public Builder setAttackBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      attack_ = value;
      bitField0_ |= 0x00008000;
      onChanged();
      return this;
    }

    private java.lang.Object hp_ = "";
    /**
     * <code>optional string hp = 17;</code>
     * @return Whether the hp field is set.
     */
    public boolean hasHp() {
      return ((bitField0_ & 0x00010000) != 0);
    }
    /**
     * <code>optional string hp = 17;</code>
     * @return The hp.
     */
    public java.lang.String getHp() {
      java.lang.Object ref = hp_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          hp_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string hp = 17;</code>
     * @return The bytes for hp.
     */
    public com.google.protobuf.ByteString
        getHpBytes() {
      java.lang.Object ref = hp_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        hp_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string hp = 17;</code>
     * @param value The hp to set.
     * @return This builder for chaining.
     */
    public Builder setHp(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      hp_ = value;
      bitField0_ |= 0x00010000;
      onChanged();
      return this;
    }
    /**
     * <code>optional string hp = 17;</code>
     * @return This builder for chaining.
     */
    public Builder clearHp() {
      hp_ = getDefaultInstance().getHp();
      bitField0_ = (bitField0_ & ~0x00010000);
      onChanged();
      return this;
    }
    /**
     * <code>optional string hp = 17;</code>
     * @param value The bytes for hp to set.
     * @return This builder for chaining.
     */
    public Builder setHpBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      hp_ = value;
      bitField0_ |= 0x00010000;
      onChanged();
      return this;
    }

    private java.lang.Object def_ = "";
    /**
     * <code>optional string def = 18;</code>
     * @return Whether the def field is set.
     */
    public boolean hasDef() {
      return ((bitField0_ & 0x00020000) != 0);
    }
    /**
     * <code>optional string def = 18;</code>
     * @return The def.
     */
    public java.lang.String getDef() {
      java.lang.Object ref = def_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          def_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string def = 18;</code>
     * @return The bytes for def.
     */
    public com.google.protobuf.ByteString
        getDefBytes() {
      java.lang.Object ref = def_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        def_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string def = 18;</code>
     * @param value The def to set.
     * @return This builder for chaining.
     */
    public Builder setDef(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      def_ = value;
      bitField0_ |= 0x00020000;
      onChanged();
      return this;
    }
    /**
     * <code>optional string def = 18;</code>
     * @return This builder for chaining.
     */
    public Builder clearDef() {
      def_ = getDefaultInstance().getDef();
      bitField0_ = (bitField0_ & ~0x00020000);
      onChanged();
      return this;
    }
    /**
     * <code>optional string def = 18;</code>
     * @param value The bytes for def to set.
     * @return This builder for chaining.
     */
    public Builder setDefBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      def_ = value;
      bitField0_ |= 0x00020000;
      onChanged();
      return this;
    }

    private java.lang.Object speed_ = "";
    /**
     * <code>optional string speed = 19;</code>
     * @return Whether the speed field is set.
     */
    public boolean hasSpeed() {
      return ((bitField0_ & 0x00040000) != 0);
    }
    /**
     * <code>optional string speed = 19;</code>
     * @return The speed.
     */
    public java.lang.String getSpeed() {
      java.lang.Object ref = speed_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          speed_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string speed = 19;</code>
     * @return The bytes for speed.
     */
    public com.google.protobuf.ByteString
        getSpeedBytes() {
      java.lang.Object ref = speed_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        speed_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string speed = 19;</code>
     * @param value The speed to set.
     * @return This builder for chaining.
     */
    public Builder setSpeed(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      speed_ = value;
      bitField0_ |= 0x00040000;
      onChanged();
      return this;
    }
    /**
     * <code>optional string speed = 19;</code>
     * @return This builder for chaining.
     */
    public Builder clearSpeed() {
      speed_ = getDefaultInstance().getSpeed();
      bitField0_ = (bitField0_ & ~0x00040000);
      onChanged();
      return this;
    }
    /**
     * <code>optional string speed = 19;</code>
     * @param value The bytes for speed to set.
     * @return This builder for chaining.
     */
    public Builder setSpeedBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      speed_ = value;
      bitField0_ |= 0x00040000;
      onChanged();
      return this;
    }

    private java.lang.Object attributeWeight_ = "";
    /**
     * <code>optional string attributeWeight = 20;</code>
     * @return Whether the attributeWeight field is set.
     */
    public boolean hasAttributeWeight() {
      return ((bitField0_ & 0x00080000) != 0);
    }
    /**
     * <code>optional string attributeWeight = 20;</code>
     * @return The attributeWeight.
     */
    public java.lang.String getAttributeWeight() {
      java.lang.Object ref = attributeWeight_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          attributeWeight_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string attributeWeight = 20;</code>
     * @return The bytes for attributeWeight.
     */
    public com.google.protobuf.ByteString
        getAttributeWeightBytes() {
      java.lang.Object ref = attributeWeight_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        attributeWeight_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string attributeWeight = 20;</code>
     * @param value The attributeWeight to set.
     * @return This builder for chaining.
     */
    public Builder setAttributeWeight(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      attributeWeight_ = value;
      bitField0_ |= 0x00080000;
      onChanged();
      return this;
    }
    /**
     * <code>optional string attributeWeight = 20;</code>
     * @return This builder for chaining.
     */
    public Builder clearAttributeWeight() {
      attributeWeight_ = getDefaultInstance().getAttributeWeight();
      bitField0_ = (bitField0_ & ~0x00080000);
      onChanged();
      return this;
    }
    /**
     * <code>optional string attributeWeight = 20;</code>
     * @param value The bytes for attributeWeight to set.
     * @return This builder for chaining.
     */
    public Builder setAttributeWeightBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      attributeWeight_ = value;
      bitField0_ |= 0x00080000;
      onChanged();
      return this;
    }

    private java.lang.Object resistanceWeight_ = "";
    /**
     * <code>optional string resistanceWeight = 21;</code>
     * @return Whether the resistanceWeight field is set.
     */
    public boolean hasResistanceWeight() {
      return ((bitField0_ & 0x00100000) != 0);
    }
    /**
     * <code>optional string resistanceWeight = 21;</code>
     * @return The resistanceWeight.
     */
    public java.lang.String getResistanceWeight() {
      java.lang.Object ref = resistanceWeight_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          resistanceWeight_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string resistanceWeight = 21;</code>
     * @return The bytes for resistanceWeight.
     */
    public com.google.protobuf.ByteString
        getResistanceWeightBytes() {
      java.lang.Object ref = resistanceWeight_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        resistanceWeight_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string resistanceWeight = 21;</code>
     * @param value The resistanceWeight to set.
     * @return This builder for chaining.
     */
    public Builder setResistanceWeight(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      resistanceWeight_ = value;
      bitField0_ |= 0x00100000;
      onChanged();
      return this;
    }
    /**
     * <code>optional string resistanceWeight = 21;</code>
     * @return This builder for chaining.
     */
    public Builder clearResistanceWeight() {
      resistanceWeight_ = getDefaultInstance().getResistanceWeight();
      bitField0_ = (bitField0_ & ~0x00100000);
      onChanged();
      return this;
    }
    /**
     * <code>optional string resistanceWeight = 21;</code>
     * @param value The bytes for resistanceWeight to set.
     * @return This builder for chaining.
     */
    public Builder setResistanceWeightBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      resistanceWeight_ = value;
      bitField0_ |= 0x00100000;
      onChanged();
      return this;
    }

    private java.lang.Object specialPropertyParam_ = "";
    /**
     * <code>optional string specialPropertyParam = 22;</code>
     * @return Whether the specialPropertyParam field is set.
     */
    public boolean hasSpecialPropertyParam() {
      return ((bitField0_ & 0x00200000) != 0);
    }
    /**
     * <code>optional string specialPropertyParam = 22;</code>
     * @return The specialPropertyParam.
     */
    public java.lang.String getSpecialPropertyParam() {
      java.lang.Object ref = specialPropertyParam_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          specialPropertyParam_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string specialPropertyParam = 22;</code>
     * @return The bytes for specialPropertyParam.
     */
    public com.google.protobuf.ByteString
        getSpecialPropertyParamBytes() {
      java.lang.Object ref = specialPropertyParam_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        specialPropertyParam_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string specialPropertyParam = 22;</code>
     * @param value The specialPropertyParam to set.
     * @return This builder for chaining.
     */
    public Builder setSpecialPropertyParam(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      specialPropertyParam_ = value;
      bitField0_ |= 0x00200000;
      onChanged();
      return this;
    }
    /**
     * <code>optional string specialPropertyParam = 22;</code>
     * @return This builder for chaining.
     */
    public Builder clearSpecialPropertyParam() {
      specialPropertyParam_ = getDefaultInstance().getSpecialPropertyParam();
      bitField0_ = (bitField0_ & ~0x00200000);
      onChanged();
      return this;
    }
    /**
     * <code>optional string specialPropertyParam = 22;</code>
     * @param value The bytes for specialPropertyParam to set.
     * @return This builder for chaining.
     */
    public Builder setSpecialPropertyParamBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      specialPropertyParam_ = value;
      bitField0_ |= 0x00200000;
      onChanged();
      return this;
    }

    private int attrProb_ ;
    /**
     * <code>optional int32 attrProb = 23;</code>
     * @return Whether the attrProb field is set.
     */
    @java.lang.Override
    public boolean hasAttrProb() {
      return ((bitField0_ & 0x00400000) != 0);
    }
    /**
     * <code>optional int32 attrProb = 23;</code>
     * @return The attrProb.
     */
    @java.lang.Override
    public int getAttrProb() {
      return attrProb_;
    }
    /**
     * <code>optional int32 attrProb = 23;</code>
     * @param value The attrProb to set.
     * @return This builder for chaining.
     */
    public Builder setAttrProb(int value) {

      attrProb_ = value;
      bitField0_ |= 0x00400000;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 attrProb = 23;</code>
     * @return This builder for chaining.
     */
    public Builder clearAttrProb() {
      bitField0_ = (bitField0_ & ~0x00400000);
      attrProb_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object petId_ = "";
    /**
     * <code>optional string petId = 24;</code>
     * @return Whether the petId field is set.
     */
    public boolean hasPetId() {
      return ((bitField0_ & 0x00800000) != 0);
    }
    /**
     * <code>optional string petId = 24;</code>
     * @return The petId.
     */
    public java.lang.String getPetId() {
      java.lang.Object ref = petId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          petId_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string petId = 24;</code>
     * @return The bytes for petId.
     */
    public com.google.protobuf.ByteString
        getPetIdBytes() {
      java.lang.Object ref = petId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        petId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string petId = 24;</code>
     * @param value The petId to set.
     * @return This builder for chaining.
     */
    public Builder setPetId(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      petId_ = value;
      bitField0_ |= 0x00800000;
      onChanged();
      return this;
    }
    /**
     * <code>optional string petId = 24;</code>
     * @return This builder for chaining.
     */
    public Builder clearPetId() {
      petId_ = getDefaultInstance().getPetId();
      bitField0_ = (bitField0_ & ~0x00800000);
      onChanged();
      return this;
    }
    /**
     * <code>optional string petId = 24;</code>
     * @param value The bytes for petId to set.
     * @return This builder for chaining.
     */
    public Builder setPetIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      petId_ = value;
      bitField0_ |= 0x00800000;
      onChanged();
      return this;
    }

    private java.lang.Object petSkillLevel_ = "";
    /**
     * <code>optional string petSkillLevel = 25;</code>
     * @return Whether the petSkillLevel field is set.
     */
    public boolean hasPetSkillLevel() {
      return ((bitField0_ & 0x01000000) != 0);
    }
    /**
     * <code>optional string petSkillLevel = 25;</code>
     * @return The petSkillLevel.
     */
    public java.lang.String getPetSkillLevel() {
      java.lang.Object ref = petSkillLevel_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          petSkillLevel_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string petSkillLevel = 25;</code>
     * @return The bytes for petSkillLevel.
     */
    public com.google.protobuf.ByteString
        getPetSkillLevelBytes() {
      java.lang.Object ref = petSkillLevel_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        petSkillLevel_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string petSkillLevel = 25;</code>
     * @param value The petSkillLevel to set.
     * @return This builder for chaining.
     */
    public Builder setPetSkillLevel(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      petSkillLevel_ = value;
      bitField0_ |= 0x01000000;
      onChanged();
      return this;
    }
    /**
     * <code>optional string petSkillLevel = 25;</code>
     * @return This builder for chaining.
     */
    public Builder clearPetSkillLevel() {
      petSkillLevel_ = getDefaultInstance().getPetSkillLevel();
      bitField0_ = (bitField0_ & ~0x01000000);
      onChanged();
      return this;
    }
    /**
     * <code>optional string petSkillLevel = 25;</code>
     * @param value The bytes for petSkillLevel to set.
     * @return This builder for chaining.
     */
    public Builder setPetSkillLevelBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      petSkillLevel_ = value;
      bitField0_ |= 0x01000000;
      onChanged();
      return this;
    }

    private java.lang.Object pithyId_ = "";
    /**
     * <code>optional string pithyId = 26;</code>
     * @return Whether the pithyId field is set.
     */
    public boolean hasPithyId() {
      return ((bitField0_ & 0x02000000) != 0);
    }
    /**
     * <code>optional string pithyId = 26;</code>
     * @return The pithyId.
     */
    public java.lang.String getPithyId() {
      java.lang.Object ref = pithyId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          pithyId_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string pithyId = 26;</code>
     * @return The bytes for pithyId.
     */
    public com.google.protobuf.ByteString
        getPithyIdBytes() {
      java.lang.Object ref = pithyId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        pithyId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string pithyId = 26;</code>
     * @param value The pithyId to set.
     * @return This builder for chaining.
     */
    public Builder setPithyId(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      pithyId_ = value;
      bitField0_ |= 0x02000000;
      onChanged();
      return this;
    }
    /**
     * <code>optional string pithyId = 26;</code>
     * @return This builder for chaining.
     */
    public Builder clearPithyId() {
      pithyId_ = getDefaultInstance().getPithyId();
      bitField0_ = (bitField0_ & ~0x02000000);
      onChanged();
      return this;
    }
    /**
     * <code>optional string pithyId = 26;</code>
     * @param value The bytes for pithyId to set.
     * @return This builder for chaining.
     */
    public Builder setPithyIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      pithyId_ = value;
      bitField0_ |= 0x02000000;
      onChanged();
      return this;
    }

    private java.lang.Object pithySkillLevel_ = "";
    /**
     * <code>optional string pithySkillLevel = 27;</code>
     * @return Whether the pithySkillLevel field is set.
     */
    public boolean hasPithySkillLevel() {
      return ((bitField0_ & 0x04000000) != 0);
    }
    /**
     * <code>optional string pithySkillLevel = 27;</code>
     * @return The pithySkillLevel.
     */
    public java.lang.String getPithySkillLevel() {
      java.lang.Object ref = pithySkillLevel_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          pithySkillLevel_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string pithySkillLevel = 27;</code>
     * @return The bytes for pithySkillLevel.
     */
    public com.google.protobuf.ByteString
        getPithySkillLevelBytes() {
      java.lang.Object ref = pithySkillLevel_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        pithySkillLevel_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string pithySkillLevel = 27;</code>
     * @param value The pithySkillLevel to set.
     * @return This builder for chaining.
     */
    public Builder setPithySkillLevel(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      pithySkillLevel_ = value;
      bitField0_ |= 0x04000000;
      onChanged();
      return this;
    }
    /**
     * <code>optional string pithySkillLevel = 27;</code>
     * @return This builder for chaining.
     */
    public Builder clearPithySkillLevel() {
      pithySkillLevel_ = getDefaultInstance().getPithySkillLevel();
      bitField0_ = (bitField0_ & ~0x04000000);
      onChanged();
      return this;
    }
    /**
     * <code>optional string pithySkillLevel = 27;</code>
     * @param value The bytes for pithySkillLevel to set.
     * @return This builder for chaining.
     */
    public Builder setPithySkillLevelBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      pithySkillLevel_ = value;
      bitField0_ |= 0x04000000;
      onChanged();
      return this;
    }

    private java.lang.Object skillId_ = "";
    /**
     * <code>optional string skillId = 28;</code>
     * @return Whether the skillId field is set.
     */
    public boolean hasSkillId() {
      return ((bitField0_ & 0x08000000) != 0);
    }
    /**
     * <code>optional string skillId = 28;</code>
     * @return The skillId.
     */
    public java.lang.String getSkillId() {
      java.lang.Object ref = skillId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          skillId_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string skillId = 28;</code>
     * @return The bytes for skillId.
     */
    public com.google.protobuf.ByteString
        getSkillIdBytes() {
      java.lang.Object ref = skillId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        skillId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string skillId = 28;</code>
     * @param value The skillId to set.
     * @return This builder for chaining.
     */
    public Builder setSkillId(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      skillId_ = value;
      bitField0_ |= 0x08000000;
      onChanged();
      return this;
    }
    /**
     * <code>optional string skillId = 28;</code>
     * @return This builder for chaining.
     */
    public Builder clearSkillId() {
      skillId_ = getDefaultInstance().getSkillId();
      bitField0_ = (bitField0_ & ~0x08000000);
      onChanged();
      return this;
    }
    /**
     * <code>optional string skillId = 28;</code>
     * @param value The bytes for skillId to set.
     * @return This builder for chaining.
     */
    public Builder setSkillIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      skillId_ = value;
      bitField0_ |= 0x08000000;
      onChanged();
      return this;
    }

    private java.lang.Object skillLevel_ = "";
    /**
     * <code>optional string skillLevel = 29;</code>
     * @return Whether the skillLevel field is set.
     */
    public boolean hasSkillLevel() {
      return ((bitField0_ & 0x10000000) != 0);
    }
    /**
     * <code>optional string skillLevel = 29;</code>
     * @return The skillLevel.
     */
    public java.lang.String getSkillLevel() {
      java.lang.Object ref = skillLevel_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          skillLevel_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string skillLevel = 29;</code>
     * @return The bytes for skillLevel.
     */
    public com.google.protobuf.ByteString
        getSkillLevelBytes() {
      java.lang.Object ref = skillLevel_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        skillLevel_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string skillLevel = 29;</code>
     * @param value The skillLevel to set.
     * @return This builder for chaining.
     */
    public Builder setSkillLevel(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      skillLevel_ = value;
      bitField0_ |= 0x10000000;
      onChanged();
      return this;
    }
    /**
     * <code>optional string skillLevel = 29;</code>
     * @return This builder for chaining.
     */
    public Builder clearSkillLevel() {
      skillLevel_ = getDefaultInstance().getSkillLevel();
      bitField0_ = (bitField0_ & ~0x10000000);
      onChanged();
      return this;
    }
    /**
     * <code>optional string skillLevel = 29;</code>
     * @param value The bytes for skillLevel to set.
     * @return This builder for chaining.
     */
    public Builder setSkillLevelBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      skillLevel_ = value;
      bitField0_ |= 0x10000000;
      onChanged();
      return this;
    }

    private java.lang.Object bloodSkill_ = "";
    /**
     * <code>optional string bloodSkill = 30;</code>
     * @return Whether the bloodSkill field is set.
     */
    public boolean hasBloodSkill() {
      return ((bitField0_ & 0x20000000) != 0);
    }
    /**
     * <code>optional string bloodSkill = 30;</code>
     * @return The bloodSkill.
     */
    public java.lang.String getBloodSkill() {
      java.lang.Object ref = bloodSkill_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          bloodSkill_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string bloodSkill = 30;</code>
     * @return The bytes for bloodSkill.
     */
    public com.google.protobuf.ByteString
        getBloodSkillBytes() {
      java.lang.Object ref = bloodSkill_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        bloodSkill_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string bloodSkill = 30;</code>
     * @param value The bloodSkill to set.
     * @return This builder for chaining.
     */
    public Builder setBloodSkill(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      bloodSkill_ = value;
      bitField0_ |= 0x20000000;
      onChanged();
      return this;
    }
    /**
     * <code>optional string bloodSkill = 30;</code>
     * @return This builder for chaining.
     */
    public Builder clearBloodSkill() {
      bloodSkill_ = getDefaultInstance().getBloodSkill();
      bitField0_ = (bitField0_ & ~0x20000000);
      onChanged();
      return this;
    }
    /**
     * <code>optional string bloodSkill = 30;</code>
     * @param value The bytes for bloodSkill to set.
     * @return This builder for chaining.
     */
    public Builder setBloodSkillBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      bloodSkill_ = value;
      bitField0_ |= 0x20000000;
      onChanged();
      return this;
    }

    private java.lang.Object specialSkill_ = "";
    /**
     * <code>optional string specialSkill = 31;</code>
     * @return Whether the specialSkill field is set.
     */
    public boolean hasSpecialSkill() {
      return ((bitField0_ & 0x40000000) != 0);
    }
    /**
     * <code>optional string specialSkill = 31;</code>
     * @return The specialSkill.
     */
    public java.lang.String getSpecialSkill() {
      java.lang.Object ref = specialSkill_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          specialSkill_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string specialSkill = 31;</code>
     * @return The bytes for specialSkill.
     */
    public com.google.protobuf.ByteString
        getSpecialSkillBytes() {
      java.lang.Object ref = specialSkill_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        specialSkill_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string specialSkill = 31;</code>
     * @param value The specialSkill to set.
     * @return This builder for chaining.
     */
    public Builder setSpecialSkill(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      specialSkill_ = value;
      bitField0_ |= 0x40000000;
      onChanged();
      return this;
    }
    /**
     * <code>optional string specialSkill = 31;</code>
     * @return This builder for chaining.
     */
    public Builder clearSpecialSkill() {
      specialSkill_ = getDefaultInstance().getSpecialSkill();
      bitField0_ = (bitField0_ & ~0x40000000);
      onChanged();
      return this;
    }
    /**
     * <code>optional string specialSkill = 31;</code>
     * @param value The bytes for specialSkill to set.
     * @return This builder for chaining.
     */
    public Builder setSpecialSkillBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      specialSkill_ = value;
      bitField0_ |= 0x40000000;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.HeavenBattleMonsterConfigMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.HeavenBattleMonsterConfigMsg)
  private static final xddq.pb.HeavenBattleMonsterConfigMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.HeavenBattleMonsterConfigMsg();
  }

  public static xddq.pb.HeavenBattleMonsterConfigMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<HeavenBattleMonsterConfigMsg>
      PARSER = new com.google.protobuf.AbstractParser<HeavenBattleMonsterConfigMsg>() {
    @java.lang.Override
    public HeavenBattleMonsterConfigMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<HeavenBattleMonsterConfigMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<HeavenBattleMonsterConfigMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.HeavenBattleMonsterConfigMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

