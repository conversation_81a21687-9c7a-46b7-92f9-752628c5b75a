// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.WorldMessageCheckEffectiveReq}
 */
public final class WorldMessageCheckEffectiveReq extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.WorldMessageCheckEffectiveReq)
    WorldMessageCheckEffectiveReqOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      WorldMessageCheckEffectiveReq.class.getName());
  }
  // Use WorldMessageCheckEffectiveReq.newBuilder() to construct.
  private WorldMessageCheckEffectiveReq(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private WorldMessageCheckEffectiveReq() {
    onlyId_ = "";
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_WorldMessageCheckEffectiveReq_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_WorldMessageCheckEffectiveReq_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.WorldMessageCheckEffectiveReq.class, xddq.pb.WorldMessageCheckEffectiveReq.Builder.class);
  }

  private int bitField0_;
  public static final int ACTIVITYID_FIELD_NUMBER = 1;
  private int activityId_ = 0;
  /**
   * <code>optional int32 activityId = 1;</code>
   * @return Whether the activityId field is set.
   */
  @java.lang.Override
  public boolean hasActivityId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int32 activityId = 1;</code>
   * @return The activityId.
   */
  @java.lang.Override
  public int getActivityId() {
    return activityId_;
  }

  public static final int ONLYID_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private volatile java.lang.Object onlyId_ = "";
  /**
   * <code>optional string onlyId = 2;</code>
   * @return Whether the onlyId field is set.
   */
  @java.lang.Override
  public boolean hasOnlyId() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional string onlyId = 2;</code>
   * @return The onlyId.
   */
  @java.lang.Override
  public java.lang.String getOnlyId() {
    java.lang.Object ref = onlyId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        onlyId_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string onlyId = 2;</code>
   * @return The bytes for onlyId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getOnlyIdBytes() {
    java.lang.Object ref = onlyId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      onlyId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, activityId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 2, onlyId_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, activityId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(2, onlyId_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.WorldMessageCheckEffectiveReq)) {
      return super.equals(obj);
    }
    xddq.pb.WorldMessageCheckEffectiveReq other = (xddq.pb.WorldMessageCheckEffectiveReq) obj;

    if (hasActivityId() != other.hasActivityId()) return false;
    if (hasActivityId()) {
      if (getActivityId()
          != other.getActivityId()) return false;
    }
    if (hasOnlyId() != other.hasOnlyId()) return false;
    if (hasOnlyId()) {
      if (!getOnlyId()
          .equals(other.getOnlyId())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasActivityId()) {
      hash = (37 * hash) + ACTIVITYID_FIELD_NUMBER;
      hash = (53 * hash) + getActivityId();
    }
    if (hasOnlyId()) {
      hash = (37 * hash) + ONLYID_FIELD_NUMBER;
      hash = (53 * hash) + getOnlyId().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.WorldMessageCheckEffectiveReq parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.WorldMessageCheckEffectiveReq parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.WorldMessageCheckEffectiveReq parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.WorldMessageCheckEffectiveReq parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.WorldMessageCheckEffectiveReq parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.WorldMessageCheckEffectiveReq parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.WorldMessageCheckEffectiveReq parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.WorldMessageCheckEffectiveReq parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.WorldMessageCheckEffectiveReq parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.WorldMessageCheckEffectiveReq parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.WorldMessageCheckEffectiveReq parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.WorldMessageCheckEffectiveReq parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.WorldMessageCheckEffectiveReq prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.WorldMessageCheckEffectiveReq}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.WorldMessageCheckEffectiveReq)
      xddq.pb.WorldMessageCheckEffectiveReqOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_WorldMessageCheckEffectiveReq_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_WorldMessageCheckEffectiveReq_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.WorldMessageCheckEffectiveReq.class, xddq.pb.WorldMessageCheckEffectiveReq.Builder.class);
    }

    // Construct using xddq.pb.WorldMessageCheckEffectiveReq.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      activityId_ = 0;
      onlyId_ = "";
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_WorldMessageCheckEffectiveReq_descriptor;
    }

    @java.lang.Override
    public xddq.pb.WorldMessageCheckEffectiveReq getDefaultInstanceForType() {
      return xddq.pb.WorldMessageCheckEffectiveReq.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.WorldMessageCheckEffectiveReq build() {
      xddq.pb.WorldMessageCheckEffectiveReq result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.WorldMessageCheckEffectiveReq buildPartial() {
      xddq.pb.WorldMessageCheckEffectiveReq result = new xddq.pb.WorldMessageCheckEffectiveReq(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.WorldMessageCheckEffectiveReq result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.activityId_ = activityId_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.onlyId_ = onlyId_;
        to_bitField0_ |= 0x00000002;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.WorldMessageCheckEffectiveReq) {
        return mergeFrom((xddq.pb.WorldMessageCheckEffectiveReq)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.WorldMessageCheckEffectiveReq other) {
      if (other == xddq.pb.WorldMessageCheckEffectiveReq.getDefaultInstance()) return this;
      if (other.hasActivityId()) {
        setActivityId(other.getActivityId());
      }
      if (other.hasOnlyId()) {
        onlyId_ = other.onlyId_;
        bitField0_ |= 0x00000002;
        onChanged();
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              activityId_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              onlyId_ = input.readBytes();
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int activityId_ ;
    /**
     * <code>optional int32 activityId = 1;</code>
     * @return Whether the activityId field is set.
     */
    @java.lang.Override
    public boolean hasActivityId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 activityId = 1;</code>
     * @return The activityId.
     */
    @java.lang.Override
    public int getActivityId() {
      return activityId_;
    }
    /**
     * <code>optional int32 activityId = 1;</code>
     * @param value The activityId to set.
     * @return This builder for chaining.
     */
    public Builder setActivityId(int value) {

      activityId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 activityId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearActivityId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      activityId_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object onlyId_ = "";
    /**
     * <code>optional string onlyId = 2;</code>
     * @return Whether the onlyId field is set.
     */
    public boolean hasOnlyId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional string onlyId = 2;</code>
     * @return The onlyId.
     */
    public java.lang.String getOnlyId() {
      java.lang.Object ref = onlyId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          onlyId_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string onlyId = 2;</code>
     * @return The bytes for onlyId.
     */
    public com.google.protobuf.ByteString
        getOnlyIdBytes() {
      java.lang.Object ref = onlyId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        onlyId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string onlyId = 2;</code>
     * @param value The onlyId to set.
     * @return This builder for chaining.
     */
    public Builder setOnlyId(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      onlyId_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional string onlyId = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearOnlyId() {
      onlyId_ = getDefaultInstance().getOnlyId();
      bitField0_ = (bitField0_ & ~0x00000002);
      onChanged();
      return this;
    }
    /**
     * <code>optional string onlyId = 2;</code>
     * @param value The bytes for onlyId to set.
     * @return This builder for chaining.
     */
    public Builder setOnlyIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      onlyId_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.WorldMessageCheckEffectiveReq)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.WorldMessageCheckEffectiveReq)
  private static final xddq.pb.WorldMessageCheckEffectiveReq DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.WorldMessageCheckEffectiveReq();
  }

  public static xddq.pb.WorldMessageCheckEffectiveReq getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<WorldMessageCheckEffectiveReq>
      PARSER = new com.google.protobuf.AbstractParser<WorldMessageCheckEffectiveReq>() {
    @java.lang.Override
    public WorldMessageCheckEffectiveReq parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<WorldMessageCheckEffectiveReq> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<WorldMessageCheckEffectiveReq> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.WorldMessageCheckEffectiveReq getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

