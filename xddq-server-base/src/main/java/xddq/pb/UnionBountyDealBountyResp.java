// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.UnionBountyDealBountyResp}
 */
public final class UnionBountyDealBountyResp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.UnionBountyDealBountyResp)
    UnionBountyDealBountyRespOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      UnionBountyDealBountyResp.class.getName());
  }
  // Use UnionBountyDealBountyResp.newBuilder() to construct.
  private UnionBountyDealBountyResp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private UnionBountyDealBountyResp() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_UnionBountyDealBountyResp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_UnionBountyDealBountyResp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.UnionBountyDealBountyResp.class, xddq.pb.UnionBountyDealBountyResp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>optional int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int PLAYERDATA_FIELD_NUMBER = 2;
  private xddq.pb.UnionBountyPlayerDataMsg playerData_;
  /**
   * <code>optional .xddq.pb.UnionBountyPlayerDataMsg playerData = 2;</code>
   * @return Whether the playerData field is set.
   */
  @java.lang.Override
  public boolean hasPlayerData() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional .xddq.pb.UnionBountyPlayerDataMsg playerData = 2;</code>
   * @return The playerData.
   */
  @java.lang.Override
  public xddq.pb.UnionBountyPlayerDataMsg getPlayerData() {
    return playerData_ == null ? xddq.pb.UnionBountyPlayerDataMsg.getDefaultInstance() : playerData_;
  }
  /**
   * <code>optional .xddq.pb.UnionBountyPlayerDataMsg playerData = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.UnionBountyPlayerDataMsgOrBuilder getPlayerDataOrBuilder() {
    return playerData_ == null ? xddq.pb.UnionBountyPlayerDataMsg.getDefaultInstance() : playerData_;
  }

  public static final int MYCART_FIELD_NUMBER = 3;
  private xddq.pb.UnionBountyEscortCartMsg myCart_;
  /**
   * <code>optional .xddq.pb.UnionBountyEscortCartMsg myCart = 3;</code>
   * @return Whether the myCart field is set.
   */
  @java.lang.Override
  public boolean hasMyCart() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional .xddq.pb.UnionBountyEscortCartMsg myCart = 3;</code>
   * @return The myCart.
   */
  @java.lang.Override
  public xddq.pb.UnionBountyEscortCartMsg getMyCart() {
    return myCart_ == null ? xddq.pb.UnionBountyEscortCartMsg.getDefaultInstance() : myCart_;
  }
  /**
   * <code>optional .xddq.pb.UnionBountyEscortCartMsg myCart = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.UnionBountyEscortCartMsgOrBuilder getMyCartOrBuilder() {
    return myCart_ == null ? xddq.pb.UnionBountyEscortCartMsg.getDefaultInstance() : myCart_;
  }

  public static final int BOUNTYINFO_FIELD_NUMBER = 4;
  private xddq.pb.UnionBountyBountyInfo bountyInfo_;
  /**
   * <code>optional .xddq.pb.UnionBountyBountyInfo bountyInfo = 4;</code>
   * @return Whether the bountyInfo field is set.
   */
  @java.lang.Override
  public boolean hasBountyInfo() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional .xddq.pb.UnionBountyBountyInfo bountyInfo = 4;</code>
   * @return The bountyInfo.
   */
  @java.lang.Override
  public xddq.pb.UnionBountyBountyInfo getBountyInfo() {
    return bountyInfo_ == null ? xddq.pb.UnionBountyBountyInfo.getDefaultInstance() : bountyInfo_;
  }
  /**
   * <code>optional .xddq.pb.UnionBountyBountyInfo bountyInfo = 4;</code>
   */
  @java.lang.Override
  public xddq.pb.UnionBountyBountyInfoOrBuilder getBountyInfoOrBuilder() {
    return bountyInfo_ == null ? xddq.pb.UnionBountyBountyInfo.getDefaultInstance() : bountyInfo_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeMessage(2, getPlayerData());
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeMessage(3, getMyCart());
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeMessage(4, getBountyInfo());
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getPlayerData());
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, getMyCart());
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(4, getBountyInfo());
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.UnionBountyDealBountyResp)) {
      return super.equals(obj);
    }
    xddq.pb.UnionBountyDealBountyResp other = (xddq.pb.UnionBountyDealBountyResp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (hasPlayerData() != other.hasPlayerData()) return false;
    if (hasPlayerData()) {
      if (!getPlayerData()
          .equals(other.getPlayerData())) return false;
    }
    if (hasMyCart() != other.hasMyCart()) return false;
    if (hasMyCart()) {
      if (!getMyCart()
          .equals(other.getMyCart())) return false;
    }
    if (hasBountyInfo() != other.hasBountyInfo()) return false;
    if (hasBountyInfo()) {
      if (!getBountyInfo()
          .equals(other.getBountyInfo())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (hasPlayerData()) {
      hash = (37 * hash) + PLAYERDATA_FIELD_NUMBER;
      hash = (53 * hash) + getPlayerData().hashCode();
    }
    if (hasMyCart()) {
      hash = (37 * hash) + MYCART_FIELD_NUMBER;
      hash = (53 * hash) + getMyCart().hashCode();
    }
    if (hasBountyInfo()) {
      hash = (37 * hash) + BOUNTYINFO_FIELD_NUMBER;
      hash = (53 * hash) + getBountyInfo().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.UnionBountyDealBountyResp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.UnionBountyDealBountyResp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.UnionBountyDealBountyResp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.UnionBountyDealBountyResp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.UnionBountyDealBountyResp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.UnionBountyDealBountyResp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.UnionBountyDealBountyResp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.UnionBountyDealBountyResp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.UnionBountyDealBountyResp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.UnionBountyDealBountyResp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.UnionBountyDealBountyResp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.UnionBountyDealBountyResp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.UnionBountyDealBountyResp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.UnionBountyDealBountyResp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.UnionBountyDealBountyResp)
      xddq.pb.UnionBountyDealBountyRespOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_UnionBountyDealBountyResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_UnionBountyDealBountyResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.UnionBountyDealBountyResp.class, xddq.pb.UnionBountyDealBountyResp.Builder.class);
    }

    // Construct using xddq.pb.UnionBountyDealBountyResp.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetPlayerDataFieldBuilder();
        internalGetMyCartFieldBuilder();
        internalGetBountyInfoFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      playerData_ = null;
      if (playerDataBuilder_ != null) {
        playerDataBuilder_.dispose();
        playerDataBuilder_ = null;
      }
      myCart_ = null;
      if (myCartBuilder_ != null) {
        myCartBuilder_.dispose();
        myCartBuilder_ = null;
      }
      bountyInfo_ = null;
      if (bountyInfoBuilder_ != null) {
        bountyInfoBuilder_.dispose();
        bountyInfoBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_UnionBountyDealBountyResp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.UnionBountyDealBountyResp getDefaultInstanceForType() {
      return xddq.pb.UnionBountyDealBountyResp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.UnionBountyDealBountyResp build() {
      xddq.pb.UnionBountyDealBountyResp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.UnionBountyDealBountyResp buildPartial() {
      xddq.pb.UnionBountyDealBountyResp result = new xddq.pb.UnionBountyDealBountyResp(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.UnionBountyDealBountyResp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.playerData_ = playerDataBuilder_ == null
            ? playerData_
            : playerDataBuilder_.build();
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.myCart_ = myCartBuilder_ == null
            ? myCart_
            : myCartBuilder_.build();
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.bountyInfo_ = bountyInfoBuilder_ == null
            ? bountyInfo_
            : bountyInfoBuilder_.build();
        to_bitField0_ |= 0x00000008;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.UnionBountyDealBountyResp) {
        return mergeFrom((xddq.pb.UnionBountyDealBountyResp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.UnionBountyDealBountyResp other) {
      if (other == xddq.pb.UnionBountyDealBountyResp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (other.hasPlayerData()) {
        mergePlayerData(other.getPlayerData());
      }
      if (other.hasMyCart()) {
        mergeMyCart(other.getMyCart());
      }
      if (other.hasBountyInfo()) {
        mergeBountyInfo(other.getBountyInfo());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              input.readMessage(
                  internalGetPlayerDataFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 26: {
              input.readMessage(
                  internalGetMyCartFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 34: {
              input.readMessage(
                  internalGetBountyInfoFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000008;
              break;
            } // case 34
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>optional int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>optional int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private xddq.pb.UnionBountyPlayerDataMsg playerData_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.UnionBountyPlayerDataMsg, xddq.pb.UnionBountyPlayerDataMsg.Builder, xddq.pb.UnionBountyPlayerDataMsgOrBuilder> playerDataBuilder_;
    /**
     * <code>optional .xddq.pb.UnionBountyPlayerDataMsg playerData = 2;</code>
     * @return Whether the playerData field is set.
     */
    public boolean hasPlayerData() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .xddq.pb.UnionBountyPlayerDataMsg playerData = 2;</code>
     * @return The playerData.
     */
    public xddq.pb.UnionBountyPlayerDataMsg getPlayerData() {
      if (playerDataBuilder_ == null) {
        return playerData_ == null ? xddq.pb.UnionBountyPlayerDataMsg.getDefaultInstance() : playerData_;
      } else {
        return playerDataBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.UnionBountyPlayerDataMsg playerData = 2;</code>
     */
    public Builder setPlayerData(xddq.pb.UnionBountyPlayerDataMsg value) {
      if (playerDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        playerData_ = value;
      } else {
        playerDataBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.UnionBountyPlayerDataMsg playerData = 2;</code>
     */
    public Builder setPlayerData(
        xddq.pb.UnionBountyPlayerDataMsg.Builder builderForValue) {
      if (playerDataBuilder_ == null) {
        playerData_ = builderForValue.build();
      } else {
        playerDataBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.UnionBountyPlayerDataMsg playerData = 2;</code>
     */
    public Builder mergePlayerData(xddq.pb.UnionBountyPlayerDataMsg value) {
      if (playerDataBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0) &&
          playerData_ != null &&
          playerData_ != xddq.pb.UnionBountyPlayerDataMsg.getDefaultInstance()) {
          getPlayerDataBuilder().mergeFrom(value);
        } else {
          playerData_ = value;
        }
      } else {
        playerDataBuilder_.mergeFrom(value);
      }
      if (playerData_ != null) {
        bitField0_ |= 0x00000002;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.UnionBountyPlayerDataMsg playerData = 2;</code>
     */
    public Builder clearPlayerData() {
      bitField0_ = (bitField0_ & ~0x00000002);
      playerData_ = null;
      if (playerDataBuilder_ != null) {
        playerDataBuilder_.dispose();
        playerDataBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.UnionBountyPlayerDataMsg playerData = 2;</code>
     */
    public xddq.pb.UnionBountyPlayerDataMsg.Builder getPlayerDataBuilder() {
      bitField0_ |= 0x00000002;
      onChanged();
      return internalGetPlayerDataFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.UnionBountyPlayerDataMsg playerData = 2;</code>
     */
    public xddq.pb.UnionBountyPlayerDataMsgOrBuilder getPlayerDataOrBuilder() {
      if (playerDataBuilder_ != null) {
        return playerDataBuilder_.getMessageOrBuilder();
      } else {
        return playerData_ == null ?
            xddq.pb.UnionBountyPlayerDataMsg.getDefaultInstance() : playerData_;
      }
    }
    /**
     * <code>optional .xddq.pb.UnionBountyPlayerDataMsg playerData = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.UnionBountyPlayerDataMsg, xddq.pb.UnionBountyPlayerDataMsg.Builder, xddq.pb.UnionBountyPlayerDataMsgOrBuilder> 
        internalGetPlayerDataFieldBuilder() {
      if (playerDataBuilder_ == null) {
        playerDataBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.UnionBountyPlayerDataMsg, xddq.pb.UnionBountyPlayerDataMsg.Builder, xddq.pb.UnionBountyPlayerDataMsgOrBuilder>(
                getPlayerData(),
                getParentForChildren(),
                isClean());
        playerData_ = null;
      }
      return playerDataBuilder_;
    }

    private xddq.pb.UnionBountyEscortCartMsg myCart_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.UnionBountyEscortCartMsg, xddq.pb.UnionBountyEscortCartMsg.Builder, xddq.pb.UnionBountyEscortCartMsgOrBuilder> myCartBuilder_;
    /**
     * <code>optional .xddq.pb.UnionBountyEscortCartMsg myCart = 3;</code>
     * @return Whether the myCart field is set.
     */
    public boolean hasMyCart() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional .xddq.pb.UnionBountyEscortCartMsg myCart = 3;</code>
     * @return The myCart.
     */
    public xddq.pb.UnionBountyEscortCartMsg getMyCart() {
      if (myCartBuilder_ == null) {
        return myCart_ == null ? xddq.pb.UnionBountyEscortCartMsg.getDefaultInstance() : myCart_;
      } else {
        return myCartBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.UnionBountyEscortCartMsg myCart = 3;</code>
     */
    public Builder setMyCart(xddq.pb.UnionBountyEscortCartMsg value) {
      if (myCartBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        myCart_ = value;
      } else {
        myCartBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.UnionBountyEscortCartMsg myCart = 3;</code>
     */
    public Builder setMyCart(
        xddq.pb.UnionBountyEscortCartMsg.Builder builderForValue) {
      if (myCartBuilder_ == null) {
        myCart_ = builderForValue.build();
      } else {
        myCartBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.UnionBountyEscortCartMsg myCart = 3;</code>
     */
    public Builder mergeMyCart(xddq.pb.UnionBountyEscortCartMsg value) {
      if (myCartBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0) &&
          myCart_ != null &&
          myCart_ != xddq.pb.UnionBountyEscortCartMsg.getDefaultInstance()) {
          getMyCartBuilder().mergeFrom(value);
        } else {
          myCart_ = value;
        }
      } else {
        myCartBuilder_.mergeFrom(value);
      }
      if (myCart_ != null) {
        bitField0_ |= 0x00000004;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.UnionBountyEscortCartMsg myCart = 3;</code>
     */
    public Builder clearMyCart() {
      bitField0_ = (bitField0_ & ~0x00000004);
      myCart_ = null;
      if (myCartBuilder_ != null) {
        myCartBuilder_.dispose();
        myCartBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.UnionBountyEscortCartMsg myCart = 3;</code>
     */
    public xddq.pb.UnionBountyEscortCartMsg.Builder getMyCartBuilder() {
      bitField0_ |= 0x00000004;
      onChanged();
      return internalGetMyCartFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.UnionBountyEscortCartMsg myCart = 3;</code>
     */
    public xddq.pb.UnionBountyEscortCartMsgOrBuilder getMyCartOrBuilder() {
      if (myCartBuilder_ != null) {
        return myCartBuilder_.getMessageOrBuilder();
      } else {
        return myCart_ == null ?
            xddq.pb.UnionBountyEscortCartMsg.getDefaultInstance() : myCart_;
      }
    }
    /**
     * <code>optional .xddq.pb.UnionBountyEscortCartMsg myCart = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.UnionBountyEscortCartMsg, xddq.pb.UnionBountyEscortCartMsg.Builder, xddq.pb.UnionBountyEscortCartMsgOrBuilder> 
        internalGetMyCartFieldBuilder() {
      if (myCartBuilder_ == null) {
        myCartBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.UnionBountyEscortCartMsg, xddq.pb.UnionBountyEscortCartMsg.Builder, xddq.pb.UnionBountyEscortCartMsgOrBuilder>(
                getMyCart(),
                getParentForChildren(),
                isClean());
        myCart_ = null;
      }
      return myCartBuilder_;
    }

    private xddq.pb.UnionBountyBountyInfo bountyInfo_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.UnionBountyBountyInfo, xddq.pb.UnionBountyBountyInfo.Builder, xddq.pb.UnionBountyBountyInfoOrBuilder> bountyInfoBuilder_;
    /**
     * <code>optional .xddq.pb.UnionBountyBountyInfo bountyInfo = 4;</code>
     * @return Whether the bountyInfo field is set.
     */
    public boolean hasBountyInfo() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional .xddq.pb.UnionBountyBountyInfo bountyInfo = 4;</code>
     * @return The bountyInfo.
     */
    public xddq.pb.UnionBountyBountyInfo getBountyInfo() {
      if (bountyInfoBuilder_ == null) {
        return bountyInfo_ == null ? xddq.pb.UnionBountyBountyInfo.getDefaultInstance() : bountyInfo_;
      } else {
        return bountyInfoBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.UnionBountyBountyInfo bountyInfo = 4;</code>
     */
    public Builder setBountyInfo(xddq.pb.UnionBountyBountyInfo value) {
      if (bountyInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        bountyInfo_ = value;
      } else {
        bountyInfoBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.UnionBountyBountyInfo bountyInfo = 4;</code>
     */
    public Builder setBountyInfo(
        xddq.pb.UnionBountyBountyInfo.Builder builderForValue) {
      if (bountyInfoBuilder_ == null) {
        bountyInfo_ = builderForValue.build();
      } else {
        bountyInfoBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.UnionBountyBountyInfo bountyInfo = 4;</code>
     */
    public Builder mergeBountyInfo(xddq.pb.UnionBountyBountyInfo value) {
      if (bountyInfoBuilder_ == null) {
        if (((bitField0_ & 0x00000008) != 0) &&
          bountyInfo_ != null &&
          bountyInfo_ != xddq.pb.UnionBountyBountyInfo.getDefaultInstance()) {
          getBountyInfoBuilder().mergeFrom(value);
        } else {
          bountyInfo_ = value;
        }
      } else {
        bountyInfoBuilder_.mergeFrom(value);
      }
      if (bountyInfo_ != null) {
        bitField0_ |= 0x00000008;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.UnionBountyBountyInfo bountyInfo = 4;</code>
     */
    public Builder clearBountyInfo() {
      bitField0_ = (bitField0_ & ~0x00000008);
      bountyInfo_ = null;
      if (bountyInfoBuilder_ != null) {
        bountyInfoBuilder_.dispose();
        bountyInfoBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.UnionBountyBountyInfo bountyInfo = 4;</code>
     */
    public xddq.pb.UnionBountyBountyInfo.Builder getBountyInfoBuilder() {
      bitField0_ |= 0x00000008;
      onChanged();
      return internalGetBountyInfoFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.UnionBountyBountyInfo bountyInfo = 4;</code>
     */
    public xddq.pb.UnionBountyBountyInfoOrBuilder getBountyInfoOrBuilder() {
      if (bountyInfoBuilder_ != null) {
        return bountyInfoBuilder_.getMessageOrBuilder();
      } else {
        return bountyInfo_ == null ?
            xddq.pb.UnionBountyBountyInfo.getDefaultInstance() : bountyInfo_;
      }
    }
    /**
     * <code>optional .xddq.pb.UnionBountyBountyInfo bountyInfo = 4;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.UnionBountyBountyInfo, xddq.pb.UnionBountyBountyInfo.Builder, xddq.pb.UnionBountyBountyInfoOrBuilder> 
        internalGetBountyInfoFieldBuilder() {
      if (bountyInfoBuilder_ == null) {
        bountyInfoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.UnionBountyBountyInfo, xddq.pb.UnionBountyBountyInfo.Builder, xddq.pb.UnionBountyBountyInfoOrBuilder>(
                getBountyInfo(),
                getParentForChildren(),
                isClean());
        bountyInfo_ = null;
      }
      return bountyInfoBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.UnionBountyDealBountyResp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.UnionBountyDealBountyResp)
  private static final xddq.pb.UnionBountyDealBountyResp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.UnionBountyDealBountyResp();
  }

  public static xddq.pb.UnionBountyDealBountyResp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<UnionBountyDealBountyResp>
      PARSER = new com.google.protobuf.AbstractParser<UnionBountyDealBountyResp>() {
    @java.lang.Override
    public UnionBountyDealBountyResp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<UnionBountyDealBountyResp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<UnionBountyDealBountyResp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.UnionBountyDealBountyResp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

