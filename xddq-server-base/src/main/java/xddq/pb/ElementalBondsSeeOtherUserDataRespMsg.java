// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.ElementalBondsSeeOtherUserDataRespMsg}
 */
public final class ElementalBondsSeeOtherUserDataRespMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.ElementalBondsSeeOtherUserDataRespMsg)
    ElementalBondsSeeOtherUserDataRespMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      ElementalBondsSeeOtherUserDataRespMsg.class.getName());
  }
  // Use ElementalBondsSeeOtherUserDataRespMsg.newBuilder() to construct.
  private ElementalBondsSeeOtherUserDataRespMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private ElementalBondsSeeOtherUserDataRespMsg() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_ElementalBondsSeeOtherUserDataRespMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_ElementalBondsSeeOtherUserDataRespMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.ElementalBondsSeeOtherUserDataRespMsg.class, xddq.pb.ElementalBondsSeeOtherUserDataRespMsg.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int CUR_FIELD_NUMBER = 2;
  private xddq.pb.ElementalBondsBadgeMsg cur_;
  /**
   * <code>optional .xddq.pb.ElementalBondsBadgeMsg cur = 2;</code>
   * @return Whether the cur field is set.
   */
  @java.lang.Override
  public boolean hasCur() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional .xddq.pb.ElementalBondsBadgeMsg cur = 2;</code>
   * @return The cur.
   */
  @java.lang.Override
  public xddq.pb.ElementalBondsBadgeMsg getCur() {
    return cur_ == null ? xddq.pb.ElementalBondsBadgeMsg.getDefaultInstance() : cur_;
  }
  /**
   * <code>optional .xddq.pb.ElementalBondsBadgeMsg cur = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.ElementalBondsBadgeMsgOrBuilder getCurOrBuilder() {
    return cur_ == null ? xddq.pb.ElementalBondsBadgeMsg.getDefaultInstance() : cur_;
  }

  public static final int PLAYERDATA_FIELD_NUMBER = 3;
  private xddq.pb.PlayerBaseDataMsg playerData_;
  /**
   * <code>optional .xddq.pb.PlayerBaseDataMsg playerData = 3;</code>
   * @return Whether the playerData field is set.
   */
  @java.lang.Override
  public boolean hasPlayerData() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional .xddq.pb.PlayerBaseDataMsg playerData = 3;</code>
   * @return The playerData.
   */
  @java.lang.Override
  public xddq.pb.PlayerBaseDataMsg getPlayerData() {
    return playerData_ == null ? xddq.pb.PlayerBaseDataMsg.getDefaultInstance() : playerData_;
  }
  /**
   * <code>optional .xddq.pb.PlayerBaseDataMsg playerData = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.PlayerBaseDataMsgOrBuilder getPlayerDataOrBuilder() {
    return playerData_ == null ? xddq.pb.PlayerBaseDataMsg.getDefaultInstance() : playerData_;
  }

  public static final int SCORE_FIELD_NUMBER = 4;
  private long score_ = 0L;
  /**
   * <code>optional int64 score = 4;</code>
   * @return Whether the score field is set.
   */
  @java.lang.Override
  public boolean hasScore() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional int64 score = 4;</code>
   * @return The score.
   */
  @java.lang.Override
  public long getScore() {
    return score_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeMessage(2, getCur());
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeMessage(3, getPlayerData());
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt64(4, score_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getCur());
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, getPlayerData());
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(4, score_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.ElementalBondsSeeOtherUserDataRespMsg)) {
      return super.equals(obj);
    }
    xddq.pb.ElementalBondsSeeOtherUserDataRespMsg other = (xddq.pb.ElementalBondsSeeOtherUserDataRespMsg) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (hasCur() != other.hasCur()) return false;
    if (hasCur()) {
      if (!getCur()
          .equals(other.getCur())) return false;
    }
    if (hasPlayerData() != other.hasPlayerData()) return false;
    if (hasPlayerData()) {
      if (!getPlayerData()
          .equals(other.getPlayerData())) return false;
    }
    if (hasScore() != other.hasScore()) return false;
    if (hasScore()) {
      if (getScore()
          != other.getScore()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (hasCur()) {
      hash = (37 * hash) + CUR_FIELD_NUMBER;
      hash = (53 * hash) + getCur().hashCode();
    }
    if (hasPlayerData()) {
      hash = (37 * hash) + PLAYERDATA_FIELD_NUMBER;
      hash = (53 * hash) + getPlayerData().hashCode();
    }
    if (hasScore()) {
      hash = (37 * hash) + SCORE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getScore());
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.ElementalBondsSeeOtherUserDataRespMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ElementalBondsSeeOtherUserDataRespMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ElementalBondsSeeOtherUserDataRespMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ElementalBondsSeeOtherUserDataRespMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ElementalBondsSeeOtherUserDataRespMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ElementalBondsSeeOtherUserDataRespMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ElementalBondsSeeOtherUserDataRespMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.ElementalBondsSeeOtherUserDataRespMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.ElementalBondsSeeOtherUserDataRespMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.ElementalBondsSeeOtherUserDataRespMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.ElementalBondsSeeOtherUserDataRespMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.ElementalBondsSeeOtherUserDataRespMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.ElementalBondsSeeOtherUserDataRespMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.ElementalBondsSeeOtherUserDataRespMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.ElementalBondsSeeOtherUserDataRespMsg)
      xddq.pb.ElementalBondsSeeOtherUserDataRespMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ElementalBondsSeeOtherUserDataRespMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ElementalBondsSeeOtherUserDataRespMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.ElementalBondsSeeOtherUserDataRespMsg.class, xddq.pb.ElementalBondsSeeOtherUserDataRespMsg.Builder.class);
    }

    // Construct using xddq.pb.ElementalBondsSeeOtherUserDataRespMsg.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetCurFieldBuilder();
        internalGetPlayerDataFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      cur_ = null;
      if (curBuilder_ != null) {
        curBuilder_.dispose();
        curBuilder_ = null;
      }
      playerData_ = null;
      if (playerDataBuilder_ != null) {
        playerDataBuilder_.dispose();
        playerDataBuilder_ = null;
      }
      score_ = 0L;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ElementalBondsSeeOtherUserDataRespMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.ElementalBondsSeeOtherUserDataRespMsg getDefaultInstanceForType() {
      return xddq.pb.ElementalBondsSeeOtherUserDataRespMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.ElementalBondsSeeOtherUserDataRespMsg build() {
      xddq.pb.ElementalBondsSeeOtherUserDataRespMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.ElementalBondsSeeOtherUserDataRespMsg buildPartial() {
      xddq.pb.ElementalBondsSeeOtherUserDataRespMsg result = new xddq.pb.ElementalBondsSeeOtherUserDataRespMsg(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.ElementalBondsSeeOtherUserDataRespMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.cur_ = curBuilder_ == null
            ? cur_
            : curBuilder_.build();
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.playerData_ = playerDataBuilder_ == null
            ? playerData_
            : playerDataBuilder_.build();
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.score_ = score_;
        to_bitField0_ |= 0x00000008;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.ElementalBondsSeeOtherUserDataRespMsg) {
        return mergeFrom((xddq.pb.ElementalBondsSeeOtherUserDataRespMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.ElementalBondsSeeOtherUserDataRespMsg other) {
      if (other == xddq.pb.ElementalBondsSeeOtherUserDataRespMsg.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (other.hasCur()) {
        mergeCur(other.getCur());
      }
      if (other.hasPlayerData()) {
        mergePlayerData(other.getPlayerData());
      }
      if (other.hasScore()) {
        setScore(other.getScore());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              input.readMessage(
                  internalGetCurFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 26: {
              input.readMessage(
                  internalGetPlayerDataFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 32: {
              score_ = input.readInt64();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private xddq.pb.ElementalBondsBadgeMsg cur_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.ElementalBondsBadgeMsg, xddq.pb.ElementalBondsBadgeMsg.Builder, xddq.pb.ElementalBondsBadgeMsgOrBuilder> curBuilder_;
    /**
     * <code>optional .xddq.pb.ElementalBondsBadgeMsg cur = 2;</code>
     * @return Whether the cur field is set.
     */
    public boolean hasCur() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .xddq.pb.ElementalBondsBadgeMsg cur = 2;</code>
     * @return The cur.
     */
    public xddq.pb.ElementalBondsBadgeMsg getCur() {
      if (curBuilder_ == null) {
        return cur_ == null ? xddq.pb.ElementalBondsBadgeMsg.getDefaultInstance() : cur_;
      } else {
        return curBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.ElementalBondsBadgeMsg cur = 2;</code>
     */
    public Builder setCur(xddq.pb.ElementalBondsBadgeMsg value) {
      if (curBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        cur_ = value;
      } else {
        curBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ElementalBondsBadgeMsg cur = 2;</code>
     */
    public Builder setCur(
        xddq.pb.ElementalBondsBadgeMsg.Builder builderForValue) {
      if (curBuilder_ == null) {
        cur_ = builderForValue.build();
      } else {
        curBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ElementalBondsBadgeMsg cur = 2;</code>
     */
    public Builder mergeCur(xddq.pb.ElementalBondsBadgeMsg value) {
      if (curBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0) &&
          cur_ != null &&
          cur_ != xddq.pb.ElementalBondsBadgeMsg.getDefaultInstance()) {
          getCurBuilder().mergeFrom(value);
        } else {
          cur_ = value;
        }
      } else {
        curBuilder_.mergeFrom(value);
      }
      if (cur_ != null) {
        bitField0_ |= 0x00000002;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.ElementalBondsBadgeMsg cur = 2;</code>
     */
    public Builder clearCur() {
      bitField0_ = (bitField0_ & ~0x00000002);
      cur_ = null;
      if (curBuilder_ != null) {
        curBuilder_.dispose();
        curBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ElementalBondsBadgeMsg cur = 2;</code>
     */
    public xddq.pb.ElementalBondsBadgeMsg.Builder getCurBuilder() {
      bitField0_ |= 0x00000002;
      onChanged();
      return internalGetCurFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.ElementalBondsBadgeMsg cur = 2;</code>
     */
    public xddq.pb.ElementalBondsBadgeMsgOrBuilder getCurOrBuilder() {
      if (curBuilder_ != null) {
        return curBuilder_.getMessageOrBuilder();
      } else {
        return cur_ == null ?
            xddq.pb.ElementalBondsBadgeMsg.getDefaultInstance() : cur_;
      }
    }
    /**
     * <code>optional .xddq.pb.ElementalBondsBadgeMsg cur = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.ElementalBondsBadgeMsg, xddq.pb.ElementalBondsBadgeMsg.Builder, xddq.pb.ElementalBondsBadgeMsgOrBuilder> 
        internalGetCurFieldBuilder() {
      if (curBuilder_ == null) {
        curBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.ElementalBondsBadgeMsg, xddq.pb.ElementalBondsBadgeMsg.Builder, xddq.pb.ElementalBondsBadgeMsgOrBuilder>(
                getCur(),
                getParentForChildren(),
                isClean());
        cur_ = null;
      }
      return curBuilder_;
    }

    private xddq.pb.PlayerBaseDataMsg playerData_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PlayerBaseDataMsg, xddq.pb.PlayerBaseDataMsg.Builder, xddq.pb.PlayerBaseDataMsgOrBuilder> playerDataBuilder_;
    /**
     * <code>optional .xddq.pb.PlayerBaseDataMsg playerData = 3;</code>
     * @return Whether the playerData field is set.
     */
    public boolean hasPlayerData() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional .xddq.pb.PlayerBaseDataMsg playerData = 3;</code>
     * @return The playerData.
     */
    public xddq.pb.PlayerBaseDataMsg getPlayerData() {
      if (playerDataBuilder_ == null) {
        return playerData_ == null ? xddq.pb.PlayerBaseDataMsg.getDefaultInstance() : playerData_;
      } else {
        return playerDataBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.PlayerBaseDataMsg playerData = 3;</code>
     */
    public Builder setPlayerData(xddq.pb.PlayerBaseDataMsg value) {
      if (playerDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        playerData_ = value;
      } else {
        playerDataBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerBaseDataMsg playerData = 3;</code>
     */
    public Builder setPlayerData(
        xddq.pb.PlayerBaseDataMsg.Builder builderForValue) {
      if (playerDataBuilder_ == null) {
        playerData_ = builderForValue.build();
      } else {
        playerDataBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerBaseDataMsg playerData = 3;</code>
     */
    public Builder mergePlayerData(xddq.pb.PlayerBaseDataMsg value) {
      if (playerDataBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0) &&
          playerData_ != null &&
          playerData_ != xddq.pb.PlayerBaseDataMsg.getDefaultInstance()) {
          getPlayerDataBuilder().mergeFrom(value);
        } else {
          playerData_ = value;
        }
      } else {
        playerDataBuilder_.mergeFrom(value);
      }
      if (playerData_ != null) {
        bitField0_ |= 0x00000004;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerBaseDataMsg playerData = 3;</code>
     */
    public Builder clearPlayerData() {
      bitField0_ = (bitField0_ & ~0x00000004);
      playerData_ = null;
      if (playerDataBuilder_ != null) {
        playerDataBuilder_.dispose();
        playerDataBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerBaseDataMsg playerData = 3;</code>
     */
    public xddq.pb.PlayerBaseDataMsg.Builder getPlayerDataBuilder() {
      bitField0_ |= 0x00000004;
      onChanged();
      return internalGetPlayerDataFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.PlayerBaseDataMsg playerData = 3;</code>
     */
    public xddq.pb.PlayerBaseDataMsgOrBuilder getPlayerDataOrBuilder() {
      if (playerDataBuilder_ != null) {
        return playerDataBuilder_.getMessageOrBuilder();
      } else {
        return playerData_ == null ?
            xddq.pb.PlayerBaseDataMsg.getDefaultInstance() : playerData_;
      }
    }
    /**
     * <code>optional .xddq.pb.PlayerBaseDataMsg playerData = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PlayerBaseDataMsg, xddq.pb.PlayerBaseDataMsg.Builder, xddq.pb.PlayerBaseDataMsgOrBuilder> 
        internalGetPlayerDataFieldBuilder() {
      if (playerDataBuilder_ == null) {
        playerDataBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.PlayerBaseDataMsg, xddq.pb.PlayerBaseDataMsg.Builder, xddq.pb.PlayerBaseDataMsgOrBuilder>(
                getPlayerData(),
                getParentForChildren(),
                isClean());
        playerData_ = null;
      }
      return playerDataBuilder_;
    }

    private long score_ ;
    /**
     * <code>optional int64 score = 4;</code>
     * @return Whether the score field is set.
     */
    @java.lang.Override
    public boolean hasScore() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int64 score = 4;</code>
     * @return The score.
     */
    @java.lang.Override
    public long getScore() {
      return score_;
    }
    /**
     * <code>optional int64 score = 4;</code>
     * @param value The score to set.
     * @return This builder for chaining.
     */
    public Builder setScore(long value) {

      score_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 score = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearScore() {
      bitField0_ = (bitField0_ & ~0x00000008);
      score_ = 0L;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.ElementalBondsSeeOtherUserDataRespMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.ElementalBondsSeeOtherUserDataRespMsg)
  private static final xddq.pb.ElementalBondsSeeOtherUserDataRespMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.ElementalBondsSeeOtherUserDataRespMsg();
  }

  public static xddq.pb.ElementalBondsSeeOtherUserDataRespMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ElementalBondsSeeOtherUserDataRespMsg>
      PARSER = new com.google.protobuf.AbstractParser<ElementalBondsSeeOtherUserDataRespMsg>() {
    @java.lang.Override
    public ElementalBondsSeeOtherUserDataRespMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<ElementalBondsSeeOtherUserDataRespMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ElementalBondsSeeOtherUserDataRespMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.ElementalBondsSeeOtherUserDataRespMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

