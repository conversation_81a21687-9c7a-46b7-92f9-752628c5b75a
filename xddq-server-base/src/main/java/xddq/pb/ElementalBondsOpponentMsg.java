// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.ElementalBondsOpponentMsg}
 */
public final class ElementalBondsOpponentMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.ElementalBondsOpponentMsg)
    ElementalBondsOpponentMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      ElementalBondsOpponentMsg.class.getName());
  }
  // Use ElementalBondsOpponentMsg.newBuilder() to construct.
  private ElementalBondsOpponentMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private ElementalBondsOpponentMsg() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_ElementalBondsOpponentMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_ElementalBondsOpponentMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.ElementalBondsOpponentMsg.class, xddq.pb.ElementalBondsOpponentMsg.Builder.class);
  }

  private int bitField0_;
  public static final int SCORE_FIELD_NUMBER = 1;
  private long score_ = 0L;
  /**
   * <code>optional int64 score = 1;</code>
   * @return Whether the score field is set.
   */
  @java.lang.Override
  public boolean hasScore() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int64 score = 1;</code>
   * @return The score.
   */
  @java.lang.Override
  public long getScore() {
    return score_;
  }

  public static final int USERID_FIELD_NUMBER = 2;
  private long userId_ = 0L;
  /**
   * <code>optional int64 userId = 2;</code>
   * @return Whether the userId field is set.
   */
  @java.lang.Override
  public boolean hasUserId() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int64 userId = 2;</code>
   * @return The userId.
   */
  @java.lang.Override
  public long getUserId() {
    return userId_;
  }

  public static final int PLAYERINFO_FIELD_NUMBER = 3;
  private xddq.pb.PlayerBaseDataMsg playerInfo_;
  /**
   * <code>optional .xddq.pb.PlayerBaseDataMsg playerInfo = 3;</code>
   * @return Whether the playerInfo field is set.
   */
  @java.lang.Override
  public boolean hasPlayerInfo() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional .xddq.pb.PlayerBaseDataMsg playerInfo = 3;</code>
   * @return The playerInfo.
   */
  @java.lang.Override
  public xddq.pb.PlayerBaseDataMsg getPlayerInfo() {
    return playerInfo_ == null ? xddq.pb.PlayerBaseDataMsg.getDefaultInstance() : playerInfo_;
  }
  /**
   * <code>optional .xddq.pb.PlayerBaseDataMsg playerInfo = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.PlayerBaseDataMsgOrBuilder getPlayerInfoOrBuilder() {
    return playerInfo_ == null ? xddq.pb.PlayerBaseDataMsg.getDefaultInstance() : playerInfo_;
  }

  public static final int CHANNELID_FIELD_NUMBER = 4;
  private int channelId_ = 0;
  /**
   * <code>optional int32 channelId = 4;</code>
   * @return Whether the channelId field is set.
   */
  @java.lang.Override
  public boolean hasChannelId() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional int32 channelId = 4;</code>
   * @return The channelId.
   */
  @java.lang.Override
  public int getChannelId() {
    return channelId_;
  }

  public static final int SERVERID_FIELD_NUMBER = 5;
  private long serverId_ = 0L;
  /**
   * <code>optional int64 serverId = 5;</code>
   * @return Whether the serverId field is set.
   */
  @java.lang.Override
  public boolean hasServerId() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>optional int64 serverId = 5;</code>
   * @return The serverId.
   */
  @java.lang.Override
  public long getServerId() {
    return serverId_;
  }

  public static final int BADGE_FIELD_NUMBER = 6;
  private xddq.pb.ElementalBondsBadgeMsg badge_;
  /**
   * <code>optional .xddq.pb.ElementalBondsBadgeMsg badge = 6;</code>
   * @return Whether the badge field is set.
   */
  @java.lang.Override
  public boolean hasBadge() {
    return ((bitField0_ & 0x00000020) != 0);
  }
  /**
   * <code>optional .xddq.pb.ElementalBondsBadgeMsg badge = 6;</code>
   * @return The badge.
   */
  @java.lang.Override
  public xddq.pb.ElementalBondsBadgeMsg getBadge() {
    return badge_ == null ? xddq.pb.ElementalBondsBadgeMsg.getDefaultInstance() : badge_;
  }
  /**
   * <code>optional .xddq.pb.ElementalBondsBadgeMsg badge = 6;</code>
   */
  @java.lang.Override
  public xddq.pb.ElementalBondsBadgeMsgOrBuilder getBadgeOrBuilder() {
    return badge_ == null ? xddq.pb.ElementalBondsBadgeMsg.getDefaultInstance() : badge_;
  }

  public static final int NOVICE_FIELD_NUMBER = 7;
  private boolean novice_ = false;
  /**
   * <code>optional bool novice = 7;</code>
   * @return Whether the novice field is set.
   */
  @java.lang.Override
  public boolean hasNovice() {
    return ((bitField0_ & 0x00000040) != 0);
  }
  /**
   * <code>optional bool novice = 7;</code>
   * @return The novice.
   */
  @java.lang.Override
  public boolean getNovice() {
    return novice_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt64(1, score_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt64(2, userId_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeMessage(3, getPlayerInfo());
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt32(4, channelId_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      output.writeInt64(5, serverId_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      output.writeMessage(6, getBadge());
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      output.writeBool(7, novice_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(1, score_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(2, userId_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, getPlayerInfo());
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(4, channelId_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(5, serverId_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(6, getBadge());
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(7, novice_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.ElementalBondsOpponentMsg)) {
      return super.equals(obj);
    }
    xddq.pb.ElementalBondsOpponentMsg other = (xddq.pb.ElementalBondsOpponentMsg) obj;

    if (hasScore() != other.hasScore()) return false;
    if (hasScore()) {
      if (getScore()
          != other.getScore()) return false;
    }
    if (hasUserId() != other.hasUserId()) return false;
    if (hasUserId()) {
      if (getUserId()
          != other.getUserId()) return false;
    }
    if (hasPlayerInfo() != other.hasPlayerInfo()) return false;
    if (hasPlayerInfo()) {
      if (!getPlayerInfo()
          .equals(other.getPlayerInfo())) return false;
    }
    if (hasChannelId() != other.hasChannelId()) return false;
    if (hasChannelId()) {
      if (getChannelId()
          != other.getChannelId()) return false;
    }
    if (hasServerId() != other.hasServerId()) return false;
    if (hasServerId()) {
      if (getServerId()
          != other.getServerId()) return false;
    }
    if (hasBadge() != other.hasBadge()) return false;
    if (hasBadge()) {
      if (!getBadge()
          .equals(other.getBadge())) return false;
    }
    if (hasNovice() != other.hasNovice()) return false;
    if (hasNovice()) {
      if (getNovice()
          != other.getNovice()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasScore()) {
      hash = (37 * hash) + SCORE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getScore());
    }
    if (hasUserId()) {
      hash = (37 * hash) + USERID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getUserId());
    }
    if (hasPlayerInfo()) {
      hash = (37 * hash) + PLAYERINFO_FIELD_NUMBER;
      hash = (53 * hash) + getPlayerInfo().hashCode();
    }
    if (hasChannelId()) {
      hash = (37 * hash) + CHANNELID_FIELD_NUMBER;
      hash = (53 * hash) + getChannelId();
    }
    if (hasServerId()) {
      hash = (37 * hash) + SERVERID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getServerId());
    }
    if (hasBadge()) {
      hash = (37 * hash) + BADGE_FIELD_NUMBER;
      hash = (53 * hash) + getBadge().hashCode();
    }
    if (hasNovice()) {
      hash = (37 * hash) + NOVICE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getNovice());
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.ElementalBondsOpponentMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ElementalBondsOpponentMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ElementalBondsOpponentMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ElementalBondsOpponentMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ElementalBondsOpponentMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ElementalBondsOpponentMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ElementalBondsOpponentMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.ElementalBondsOpponentMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.ElementalBondsOpponentMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.ElementalBondsOpponentMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.ElementalBondsOpponentMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.ElementalBondsOpponentMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.ElementalBondsOpponentMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.ElementalBondsOpponentMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.ElementalBondsOpponentMsg)
      xddq.pb.ElementalBondsOpponentMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ElementalBondsOpponentMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ElementalBondsOpponentMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.ElementalBondsOpponentMsg.class, xddq.pb.ElementalBondsOpponentMsg.Builder.class);
    }

    // Construct using xddq.pb.ElementalBondsOpponentMsg.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetPlayerInfoFieldBuilder();
        internalGetBadgeFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      score_ = 0L;
      userId_ = 0L;
      playerInfo_ = null;
      if (playerInfoBuilder_ != null) {
        playerInfoBuilder_.dispose();
        playerInfoBuilder_ = null;
      }
      channelId_ = 0;
      serverId_ = 0L;
      badge_ = null;
      if (badgeBuilder_ != null) {
        badgeBuilder_.dispose();
        badgeBuilder_ = null;
      }
      novice_ = false;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ElementalBondsOpponentMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.ElementalBondsOpponentMsg getDefaultInstanceForType() {
      return xddq.pb.ElementalBondsOpponentMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.ElementalBondsOpponentMsg build() {
      xddq.pb.ElementalBondsOpponentMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.ElementalBondsOpponentMsg buildPartial() {
      xddq.pb.ElementalBondsOpponentMsg result = new xddq.pb.ElementalBondsOpponentMsg(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.ElementalBondsOpponentMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.score_ = score_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.userId_ = userId_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.playerInfo_ = playerInfoBuilder_ == null
            ? playerInfo_
            : playerInfoBuilder_.build();
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.channelId_ = channelId_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.serverId_ = serverId_;
        to_bitField0_ |= 0x00000010;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.badge_ = badgeBuilder_ == null
            ? badge_
            : badgeBuilder_.build();
        to_bitField0_ |= 0x00000020;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.novice_ = novice_;
        to_bitField0_ |= 0x00000040;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.ElementalBondsOpponentMsg) {
        return mergeFrom((xddq.pb.ElementalBondsOpponentMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.ElementalBondsOpponentMsg other) {
      if (other == xddq.pb.ElementalBondsOpponentMsg.getDefaultInstance()) return this;
      if (other.hasScore()) {
        setScore(other.getScore());
      }
      if (other.hasUserId()) {
        setUserId(other.getUserId());
      }
      if (other.hasPlayerInfo()) {
        mergePlayerInfo(other.getPlayerInfo());
      }
      if (other.hasChannelId()) {
        setChannelId(other.getChannelId());
      }
      if (other.hasServerId()) {
        setServerId(other.getServerId());
      }
      if (other.hasBadge()) {
        mergeBadge(other.getBadge());
      }
      if (other.hasNovice()) {
        setNovice(other.getNovice());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              score_ = input.readInt64();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              userId_ = input.readInt64();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 26: {
              input.readMessage(
                  internalGetPlayerInfoFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 32: {
              channelId_ = input.readInt32();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            case 40: {
              serverId_ = input.readInt64();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            case 50: {
              input.readMessage(
                  internalGetBadgeFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000020;
              break;
            } // case 50
            case 56: {
              novice_ = input.readBool();
              bitField0_ |= 0x00000040;
              break;
            } // case 56
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private long score_ ;
    /**
     * <code>optional int64 score = 1;</code>
     * @return Whether the score field is set.
     */
    @java.lang.Override
    public boolean hasScore() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 score = 1;</code>
     * @return The score.
     */
    @java.lang.Override
    public long getScore() {
      return score_;
    }
    /**
     * <code>optional int64 score = 1;</code>
     * @param value The score to set.
     * @return This builder for chaining.
     */
    public Builder setScore(long value) {

      score_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 score = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearScore() {
      bitField0_ = (bitField0_ & ~0x00000001);
      score_ = 0L;
      onChanged();
      return this;
    }

    private long userId_ ;
    /**
     * <code>optional int64 userId = 2;</code>
     * @return Whether the userId field is set.
     */
    @java.lang.Override
    public boolean hasUserId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int64 userId = 2;</code>
     * @return The userId.
     */
    @java.lang.Override
    public long getUserId() {
      return userId_;
    }
    /**
     * <code>optional int64 userId = 2;</code>
     * @param value The userId to set.
     * @return This builder for chaining.
     */
    public Builder setUserId(long value) {

      userId_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 userId = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearUserId() {
      bitField0_ = (bitField0_ & ~0x00000002);
      userId_ = 0L;
      onChanged();
      return this;
    }

    private xddq.pb.PlayerBaseDataMsg playerInfo_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PlayerBaseDataMsg, xddq.pb.PlayerBaseDataMsg.Builder, xddq.pb.PlayerBaseDataMsgOrBuilder> playerInfoBuilder_;
    /**
     * <code>optional .xddq.pb.PlayerBaseDataMsg playerInfo = 3;</code>
     * @return Whether the playerInfo field is set.
     */
    public boolean hasPlayerInfo() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional .xddq.pb.PlayerBaseDataMsg playerInfo = 3;</code>
     * @return The playerInfo.
     */
    public xddq.pb.PlayerBaseDataMsg getPlayerInfo() {
      if (playerInfoBuilder_ == null) {
        return playerInfo_ == null ? xddq.pb.PlayerBaseDataMsg.getDefaultInstance() : playerInfo_;
      } else {
        return playerInfoBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.PlayerBaseDataMsg playerInfo = 3;</code>
     */
    public Builder setPlayerInfo(xddq.pb.PlayerBaseDataMsg value) {
      if (playerInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        playerInfo_ = value;
      } else {
        playerInfoBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerBaseDataMsg playerInfo = 3;</code>
     */
    public Builder setPlayerInfo(
        xddq.pb.PlayerBaseDataMsg.Builder builderForValue) {
      if (playerInfoBuilder_ == null) {
        playerInfo_ = builderForValue.build();
      } else {
        playerInfoBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerBaseDataMsg playerInfo = 3;</code>
     */
    public Builder mergePlayerInfo(xddq.pb.PlayerBaseDataMsg value) {
      if (playerInfoBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0) &&
          playerInfo_ != null &&
          playerInfo_ != xddq.pb.PlayerBaseDataMsg.getDefaultInstance()) {
          getPlayerInfoBuilder().mergeFrom(value);
        } else {
          playerInfo_ = value;
        }
      } else {
        playerInfoBuilder_.mergeFrom(value);
      }
      if (playerInfo_ != null) {
        bitField0_ |= 0x00000004;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerBaseDataMsg playerInfo = 3;</code>
     */
    public Builder clearPlayerInfo() {
      bitField0_ = (bitField0_ & ~0x00000004);
      playerInfo_ = null;
      if (playerInfoBuilder_ != null) {
        playerInfoBuilder_.dispose();
        playerInfoBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerBaseDataMsg playerInfo = 3;</code>
     */
    public xddq.pb.PlayerBaseDataMsg.Builder getPlayerInfoBuilder() {
      bitField0_ |= 0x00000004;
      onChanged();
      return internalGetPlayerInfoFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.PlayerBaseDataMsg playerInfo = 3;</code>
     */
    public xddq.pb.PlayerBaseDataMsgOrBuilder getPlayerInfoOrBuilder() {
      if (playerInfoBuilder_ != null) {
        return playerInfoBuilder_.getMessageOrBuilder();
      } else {
        return playerInfo_ == null ?
            xddq.pb.PlayerBaseDataMsg.getDefaultInstance() : playerInfo_;
      }
    }
    /**
     * <code>optional .xddq.pb.PlayerBaseDataMsg playerInfo = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PlayerBaseDataMsg, xddq.pb.PlayerBaseDataMsg.Builder, xddq.pb.PlayerBaseDataMsgOrBuilder> 
        internalGetPlayerInfoFieldBuilder() {
      if (playerInfoBuilder_ == null) {
        playerInfoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.PlayerBaseDataMsg, xddq.pb.PlayerBaseDataMsg.Builder, xddq.pb.PlayerBaseDataMsgOrBuilder>(
                getPlayerInfo(),
                getParentForChildren(),
                isClean());
        playerInfo_ = null;
      }
      return playerInfoBuilder_;
    }

    private int channelId_ ;
    /**
     * <code>optional int32 channelId = 4;</code>
     * @return Whether the channelId field is set.
     */
    @java.lang.Override
    public boolean hasChannelId() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int32 channelId = 4;</code>
     * @return The channelId.
     */
    @java.lang.Override
    public int getChannelId() {
      return channelId_;
    }
    /**
     * <code>optional int32 channelId = 4;</code>
     * @param value The channelId to set.
     * @return This builder for chaining.
     */
    public Builder setChannelId(int value) {

      channelId_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 channelId = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearChannelId() {
      bitField0_ = (bitField0_ & ~0x00000008);
      channelId_ = 0;
      onChanged();
      return this;
    }

    private long serverId_ ;
    /**
     * <code>optional int64 serverId = 5;</code>
     * @return Whether the serverId field is set.
     */
    @java.lang.Override
    public boolean hasServerId() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional int64 serverId = 5;</code>
     * @return The serverId.
     */
    @java.lang.Override
    public long getServerId() {
      return serverId_;
    }
    /**
     * <code>optional int64 serverId = 5;</code>
     * @param value The serverId to set.
     * @return This builder for chaining.
     */
    public Builder setServerId(long value) {

      serverId_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 serverId = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearServerId() {
      bitField0_ = (bitField0_ & ~0x00000010);
      serverId_ = 0L;
      onChanged();
      return this;
    }

    private xddq.pb.ElementalBondsBadgeMsg badge_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.ElementalBondsBadgeMsg, xddq.pb.ElementalBondsBadgeMsg.Builder, xddq.pb.ElementalBondsBadgeMsgOrBuilder> badgeBuilder_;
    /**
     * <code>optional .xddq.pb.ElementalBondsBadgeMsg badge = 6;</code>
     * @return Whether the badge field is set.
     */
    public boolean hasBadge() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional .xddq.pb.ElementalBondsBadgeMsg badge = 6;</code>
     * @return The badge.
     */
    public xddq.pb.ElementalBondsBadgeMsg getBadge() {
      if (badgeBuilder_ == null) {
        return badge_ == null ? xddq.pb.ElementalBondsBadgeMsg.getDefaultInstance() : badge_;
      } else {
        return badgeBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.ElementalBondsBadgeMsg badge = 6;</code>
     */
    public Builder setBadge(xddq.pb.ElementalBondsBadgeMsg value) {
      if (badgeBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        badge_ = value;
      } else {
        badgeBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ElementalBondsBadgeMsg badge = 6;</code>
     */
    public Builder setBadge(
        xddq.pb.ElementalBondsBadgeMsg.Builder builderForValue) {
      if (badgeBuilder_ == null) {
        badge_ = builderForValue.build();
      } else {
        badgeBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ElementalBondsBadgeMsg badge = 6;</code>
     */
    public Builder mergeBadge(xddq.pb.ElementalBondsBadgeMsg value) {
      if (badgeBuilder_ == null) {
        if (((bitField0_ & 0x00000020) != 0) &&
          badge_ != null &&
          badge_ != xddq.pb.ElementalBondsBadgeMsg.getDefaultInstance()) {
          getBadgeBuilder().mergeFrom(value);
        } else {
          badge_ = value;
        }
      } else {
        badgeBuilder_.mergeFrom(value);
      }
      if (badge_ != null) {
        bitField0_ |= 0x00000020;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.ElementalBondsBadgeMsg badge = 6;</code>
     */
    public Builder clearBadge() {
      bitField0_ = (bitField0_ & ~0x00000020);
      badge_ = null;
      if (badgeBuilder_ != null) {
        badgeBuilder_.dispose();
        badgeBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ElementalBondsBadgeMsg badge = 6;</code>
     */
    public xddq.pb.ElementalBondsBadgeMsg.Builder getBadgeBuilder() {
      bitField0_ |= 0x00000020;
      onChanged();
      return internalGetBadgeFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.ElementalBondsBadgeMsg badge = 6;</code>
     */
    public xddq.pb.ElementalBondsBadgeMsgOrBuilder getBadgeOrBuilder() {
      if (badgeBuilder_ != null) {
        return badgeBuilder_.getMessageOrBuilder();
      } else {
        return badge_ == null ?
            xddq.pb.ElementalBondsBadgeMsg.getDefaultInstance() : badge_;
      }
    }
    /**
     * <code>optional .xddq.pb.ElementalBondsBadgeMsg badge = 6;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.ElementalBondsBadgeMsg, xddq.pb.ElementalBondsBadgeMsg.Builder, xddq.pb.ElementalBondsBadgeMsgOrBuilder> 
        internalGetBadgeFieldBuilder() {
      if (badgeBuilder_ == null) {
        badgeBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.ElementalBondsBadgeMsg, xddq.pb.ElementalBondsBadgeMsg.Builder, xddq.pb.ElementalBondsBadgeMsgOrBuilder>(
                getBadge(),
                getParentForChildren(),
                isClean());
        badge_ = null;
      }
      return badgeBuilder_;
    }

    private boolean novice_ ;
    /**
     * <code>optional bool novice = 7;</code>
     * @return Whether the novice field is set.
     */
    @java.lang.Override
    public boolean hasNovice() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <code>optional bool novice = 7;</code>
     * @return The novice.
     */
    @java.lang.Override
    public boolean getNovice() {
      return novice_;
    }
    /**
     * <code>optional bool novice = 7;</code>
     * @param value The novice to set.
     * @return This builder for chaining.
     */
    public Builder setNovice(boolean value) {

      novice_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>optional bool novice = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearNovice() {
      bitField0_ = (bitField0_ & ~0x00000040);
      novice_ = false;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.ElementalBondsOpponentMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.ElementalBondsOpponentMsg)
  private static final xddq.pb.ElementalBondsOpponentMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.ElementalBondsOpponentMsg();
  }

  public static xddq.pb.ElementalBondsOpponentMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ElementalBondsOpponentMsg>
      PARSER = new com.google.protobuf.AbstractParser<ElementalBondsOpponentMsg>() {
    @java.lang.Override
    public ElementalBondsOpponentMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<ElementalBondsOpponentMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ElementalBondsOpponentMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.ElementalBondsOpponentMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

