// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.SpiritTrialActivityConfig}
 */
public final class SpiritTrialActivityConfig extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.SpiritTrialActivityConfig)
    SpiritTrialActivityConfigOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      SpiritTrialActivityConfig.class.getName());
  }
  // Use SpiritTrialActivityConfig.newBuilder() to construct.
  private SpiritTrialActivityConfig(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private SpiritTrialActivityConfig() {
    activityBossConfig_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_SpiritTrialActivityConfig_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_SpiritTrialActivityConfig_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.SpiritTrialActivityConfig.class, xddq.pb.SpiritTrialActivityConfig.Builder.class);
  }

  public static final int ACTIVITYBOSSCONFIG_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.ActivityBossConfig> activityBossConfig_;
  /**
   * <code>repeated .xddq.pb.ActivityBossConfig activityBossConfig = 1;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.ActivityBossConfig> getActivityBossConfigList() {
    return activityBossConfig_;
  }
  /**
   * <code>repeated .xddq.pb.ActivityBossConfig activityBossConfig = 1;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.ActivityBossConfigOrBuilder> 
      getActivityBossConfigOrBuilderList() {
    return activityBossConfig_;
  }
  /**
   * <code>repeated .xddq.pb.ActivityBossConfig activityBossConfig = 1;</code>
   */
  @java.lang.Override
  public int getActivityBossConfigCount() {
    return activityBossConfig_.size();
  }
  /**
   * <code>repeated .xddq.pb.ActivityBossConfig activityBossConfig = 1;</code>
   */
  @java.lang.Override
  public xddq.pb.ActivityBossConfig getActivityBossConfig(int index) {
    return activityBossConfig_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.ActivityBossConfig activityBossConfig = 1;</code>
   */
  @java.lang.Override
  public xddq.pb.ActivityBossConfigOrBuilder getActivityBossConfigOrBuilder(
      int index) {
    return activityBossConfig_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    for (int i = 0; i < activityBossConfig_.size(); i++) {
      output.writeMessage(1, activityBossConfig_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    for (int i = 0; i < activityBossConfig_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, activityBossConfig_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.SpiritTrialActivityConfig)) {
      return super.equals(obj);
    }
    xddq.pb.SpiritTrialActivityConfig other = (xddq.pb.SpiritTrialActivityConfig) obj;

    if (!getActivityBossConfigList()
        .equals(other.getActivityBossConfigList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (getActivityBossConfigCount() > 0) {
      hash = (37 * hash) + ACTIVITYBOSSCONFIG_FIELD_NUMBER;
      hash = (53 * hash) + getActivityBossConfigList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.SpiritTrialActivityConfig parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.SpiritTrialActivityConfig parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.SpiritTrialActivityConfig parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.SpiritTrialActivityConfig parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.SpiritTrialActivityConfig parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.SpiritTrialActivityConfig parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.SpiritTrialActivityConfig parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.SpiritTrialActivityConfig parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.SpiritTrialActivityConfig parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.SpiritTrialActivityConfig parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.SpiritTrialActivityConfig parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.SpiritTrialActivityConfig parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.SpiritTrialActivityConfig prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.SpiritTrialActivityConfig}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.SpiritTrialActivityConfig)
      xddq.pb.SpiritTrialActivityConfigOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_SpiritTrialActivityConfig_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_SpiritTrialActivityConfig_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.SpiritTrialActivityConfig.class, xddq.pb.SpiritTrialActivityConfig.Builder.class);
    }

    // Construct using xddq.pb.SpiritTrialActivityConfig.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      if (activityBossConfigBuilder_ == null) {
        activityBossConfig_ = java.util.Collections.emptyList();
      } else {
        activityBossConfig_ = null;
        activityBossConfigBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000001);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_SpiritTrialActivityConfig_descriptor;
    }

    @java.lang.Override
    public xddq.pb.SpiritTrialActivityConfig getDefaultInstanceForType() {
      return xddq.pb.SpiritTrialActivityConfig.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.SpiritTrialActivityConfig build() {
      xddq.pb.SpiritTrialActivityConfig result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.SpiritTrialActivityConfig buildPartial() {
      xddq.pb.SpiritTrialActivityConfig result = new xddq.pb.SpiritTrialActivityConfig(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.SpiritTrialActivityConfig result) {
      if (activityBossConfigBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          activityBossConfig_ = java.util.Collections.unmodifiableList(activityBossConfig_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.activityBossConfig_ = activityBossConfig_;
      } else {
        result.activityBossConfig_ = activityBossConfigBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.SpiritTrialActivityConfig result) {
      int from_bitField0_ = bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.SpiritTrialActivityConfig) {
        return mergeFrom((xddq.pb.SpiritTrialActivityConfig)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.SpiritTrialActivityConfig other) {
      if (other == xddq.pb.SpiritTrialActivityConfig.getDefaultInstance()) return this;
      if (activityBossConfigBuilder_ == null) {
        if (!other.activityBossConfig_.isEmpty()) {
          if (activityBossConfig_.isEmpty()) {
            activityBossConfig_ = other.activityBossConfig_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureActivityBossConfigIsMutable();
            activityBossConfig_.addAll(other.activityBossConfig_);
          }
          onChanged();
        }
      } else {
        if (!other.activityBossConfig_.isEmpty()) {
          if (activityBossConfigBuilder_.isEmpty()) {
            activityBossConfigBuilder_.dispose();
            activityBossConfigBuilder_ = null;
            activityBossConfig_ = other.activityBossConfig_;
            bitField0_ = (bitField0_ & ~0x00000001);
            activityBossConfigBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetActivityBossConfigFieldBuilder() : null;
          } else {
            activityBossConfigBuilder_.addAllMessages(other.activityBossConfig_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              xddq.pb.ActivityBossConfig m =
                  input.readMessage(
                      xddq.pb.ActivityBossConfig.parser(),
                      extensionRegistry);
              if (activityBossConfigBuilder_ == null) {
                ensureActivityBossConfigIsMutable();
                activityBossConfig_.add(m);
              } else {
                activityBossConfigBuilder_.addMessage(m);
              }
              break;
            } // case 10
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.util.List<xddq.pb.ActivityBossConfig> activityBossConfig_ =
      java.util.Collections.emptyList();
    private void ensureActivityBossConfigIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        activityBossConfig_ = new java.util.ArrayList<xddq.pb.ActivityBossConfig>(activityBossConfig_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.ActivityBossConfig, xddq.pb.ActivityBossConfig.Builder, xddq.pb.ActivityBossConfigOrBuilder> activityBossConfigBuilder_;

    /**
     * <code>repeated .xddq.pb.ActivityBossConfig activityBossConfig = 1;</code>
     */
    public java.util.List<xddq.pb.ActivityBossConfig> getActivityBossConfigList() {
      if (activityBossConfigBuilder_ == null) {
        return java.util.Collections.unmodifiableList(activityBossConfig_);
      } else {
        return activityBossConfigBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.ActivityBossConfig activityBossConfig = 1;</code>
     */
    public int getActivityBossConfigCount() {
      if (activityBossConfigBuilder_ == null) {
        return activityBossConfig_.size();
      } else {
        return activityBossConfigBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.ActivityBossConfig activityBossConfig = 1;</code>
     */
    public xddq.pb.ActivityBossConfig getActivityBossConfig(int index) {
      if (activityBossConfigBuilder_ == null) {
        return activityBossConfig_.get(index);
      } else {
        return activityBossConfigBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.ActivityBossConfig activityBossConfig = 1;</code>
     */
    public Builder setActivityBossConfig(
        int index, xddq.pb.ActivityBossConfig value) {
      if (activityBossConfigBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureActivityBossConfigIsMutable();
        activityBossConfig_.set(index, value);
        onChanged();
      } else {
        activityBossConfigBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ActivityBossConfig activityBossConfig = 1;</code>
     */
    public Builder setActivityBossConfig(
        int index, xddq.pb.ActivityBossConfig.Builder builderForValue) {
      if (activityBossConfigBuilder_ == null) {
        ensureActivityBossConfigIsMutable();
        activityBossConfig_.set(index, builderForValue.build());
        onChanged();
      } else {
        activityBossConfigBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ActivityBossConfig activityBossConfig = 1;</code>
     */
    public Builder addActivityBossConfig(xddq.pb.ActivityBossConfig value) {
      if (activityBossConfigBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureActivityBossConfigIsMutable();
        activityBossConfig_.add(value);
        onChanged();
      } else {
        activityBossConfigBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ActivityBossConfig activityBossConfig = 1;</code>
     */
    public Builder addActivityBossConfig(
        int index, xddq.pb.ActivityBossConfig value) {
      if (activityBossConfigBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureActivityBossConfigIsMutable();
        activityBossConfig_.add(index, value);
        onChanged();
      } else {
        activityBossConfigBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ActivityBossConfig activityBossConfig = 1;</code>
     */
    public Builder addActivityBossConfig(
        xddq.pb.ActivityBossConfig.Builder builderForValue) {
      if (activityBossConfigBuilder_ == null) {
        ensureActivityBossConfigIsMutable();
        activityBossConfig_.add(builderForValue.build());
        onChanged();
      } else {
        activityBossConfigBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ActivityBossConfig activityBossConfig = 1;</code>
     */
    public Builder addActivityBossConfig(
        int index, xddq.pb.ActivityBossConfig.Builder builderForValue) {
      if (activityBossConfigBuilder_ == null) {
        ensureActivityBossConfigIsMutable();
        activityBossConfig_.add(index, builderForValue.build());
        onChanged();
      } else {
        activityBossConfigBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ActivityBossConfig activityBossConfig = 1;</code>
     */
    public Builder addAllActivityBossConfig(
        java.lang.Iterable<? extends xddq.pb.ActivityBossConfig> values) {
      if (activityBossConfigBuilder_ == null) {
        ensureActivityBossConfigIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, activityBossConfig_);
        onChanged();
      } else {
        activityBossConfigBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ActivityBossConfig activityBossConfig = 1;</code>
     */
    public Builder clearActivityBossConfig() {
      if (activityBossConfigBuilder_ == null) {
        activityBossConfig_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        activityBossConfigBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ActivityBossConfig activityBossConfig = 1;</code>
     */
    public Builder removeActivityBossConfig(int index) {
      if (activityBossConfigBuilder_ == null) {
        ensureActivityBossConfigIsMutable();
        activityBossConfig_.remove(index);
        onChanged();
      } else {
        activityBossConfigBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ActivityBossConfig activityBossConfig = 1;</code>
     */
    public xddq.pb.ActivityBossConfig.Builder getActivityBossConfigBuilder(
        int index) {
      return internalGetActivityBossConfigFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.ActivityBossConfig activityBossConfig = 1;</code>
     */
    public xddq.pb.ActivityBossConfigOrBuilder getActivityBossConfigOrBuilder(
        int index) {
      if (activityBossConfigBuilder_ == null) {
        return activityBossConfig_.get(index);  } else {
        return activityBossConfigBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.ActivityBossConfig activityBossConfig = 1;</code>
     */
    public java.util.List<? extends xddq.pb.ActivityBossConfigOrBuilder> 
         getActivityBossConfigOrBuilderList() {
      if (activityBossConfigBuilder_ != null) {
        return activityBossConfigBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(activityBossConfig_);
      }
    }
    /**
     * <code>repeated .xddq.pb.ActivityBossConfig activityBossConfig = 1;</code>
     */
    public xddq.pb.ActivityBossConfig.Builder addActivityBossConfigBuilder() {
      return internalGetActivityBossConfigFieldBuilder().addBuilder(
          xddq.pb.ActivityBossConfig.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.ActivityBossConfig activityBossConfig = 1;</code>
     */
    public xddq.pb.ActivityBossConfig.Builder addActivityBossConfigBuilder(
        int index) {
      return internalGetActivityBossConfigFieldBuilder().addBuilder(
          index, xddq.pb.ActivityBossConfig.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.ActivityBossConfig activityBossConfig = 1;</code>
     */
    public java.util.List<xddq.pb.ActivityBossConfig.Builder> 
         getActivityBossConfigBuilderList() {
      return internalGetActivityBossConfigFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.ActivityBossConfig, xddq.pb.ActivityBossConfig.Builder, xddq.pb.ActivityBossConfigOrBuilder> 
        internalGetActivityBossConfigFieldBuilder() {
      if (activityBossConfigBuilder_ == null) {
        activityBossConfigBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.ActivityBossConfig, xddq.pb.ActivityBossConfig.Builder, xddq.pb.ActivityBossConfigOrBuilder>(
                activityBossConfig_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        activityBossConfig_ = null;
      }
      return activityBossConfigBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.SpiritTrialActivityConfig)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.SpiritTrialActivityConfig)
  private static final xddq.pb.SpiritTrialActivityConfig DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.SpiritTrialActivityConfig();
  }

  public static xddq.pb.SpiritTrialActivityConfig getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<SpiritTrialActivityConfig>
      PARSER = new com.google.protobuf.AbstractParser<SpiritTrialActivityConfig>() {
    @java.lang.Override
    public SpiritTrialActivityConfig parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<SpiritTrialActivityConfig> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<SpiritTrialActivityConfig> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.SpiritTrialActivityConfig getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

