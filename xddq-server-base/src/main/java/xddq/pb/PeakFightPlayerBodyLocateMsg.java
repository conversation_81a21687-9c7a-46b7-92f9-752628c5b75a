// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.PeakFightPlayerBodyLocateMsg}
 */
public final class PeakFightPlayerBodyLocateMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.PeakFightPlayerBodyLocateMsg)
    PeakFightPlayerBodyLocateMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      PeakFightPlayerBodyLocateMsg.class.getName());
  }
  // Use PeakFightPlayerBodyLocateMsg.newBuilder() to construct.
  private PeakFightPlayerBodyLocateMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private PeakFightPlayerBodyLocateMsg() {
    fight_ = "";
    attr_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_PeakFightPlayerBodyLocateMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_PeakFightPlayerBodyLocateMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.PeakFightPlayerBodyLocateMsg.class, xddq.pb.PeakFightPlayerBodyLocateMsg.Builder.class);
  }

  private int bitField0_;
  public static final int HEAD_FIELD_NUMBER = 1;
  private xddq.pb.PlayerHeadDataMsg head_;
  /**
   * <code>optional .xddq.pb.PlayerHeadDataMsg head = 1;</code>
   * @return Whether the head field is set.
   */
  @java.lang.Override
  public boolean hasHead() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional .xddq.pb.PlayerHeadDataMsg head = 1;</code>
   * @return The head.
   */
  @java.lang.Override
  public xddq.pb.PlayerHeadDataMsg getHead() {
    return head_ == null ? xddq.pb.PlayerHeadDataMsg.getDefaultInstance() : head_;
  }
  /**
   * <code>optional .xddq.pb.PlayerHeadDataMsg head = 1;</code>
   */
  @java.lang.Override
  public xddq.pb.PlayerHeadDataMsgOrBuilder getHeadOrBuilder() {
    return head_ == null ? xddq.pb.PlayerHeadDataMsg.getDefaultInstance() : head_;
  }

  public static final int BODY_FIELD_NUMBER = 2;
  private int body_ = 0;
  /**
   * <code>optional int32 body = 2;</code>
   * @return Whether the body field is set.
   */
  @java.lang.Override
  public boolean hasBody() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int32 body = 2;</code>
   * @return The body.
   */
  @java.lang.Override
  public int getBody() {
    return body_;
  }

  public static final int ORDER_FIELD_NUMBER = 3;
  private int order_ = 0;
  /**
   * <code>optional int32 order = 3;</code>
   * @return Whether the order field is set.
   */
  @java.lang.Override
  public boolean hasOrder() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional int32 order = 3;</code>
   * @return The order.
   */
  @java.lang.Override
  public int getOrder() {
    return order_;
  }

  public static final int SITE_FIELD_NUMBER = 4;
  private int site_ = 0;
  /**
   * <code>optional int32 site = 4;</code>
   * @return Whether the site field is set.
   */
  @java.lang.Override
  public boolean hasSite() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional int32 site = 4;</code>
   * @return The site.
   */
  @java.lang.Override
  public int getSite() {
    return site_;
  }

  public static final int FIGHT_FIELD_NUMBER = 5;
  @SuppressWarnings("serial")
  private volatile java.lang.Object fight_ = "";
  /**
   * <code>optional string fight = 5;</code>
   * @return Whether the fight field is set.
   */
  @java.lang.Override
  public boolean hasFight() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>optional string fight = 5;</code>
   * @return The fight.
   */
  @java.lang.Override
  public java.lang.String getFight() {
    java.lang.Object ref = fight_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        fight_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string fight = 5;</code>
   * @return The bytes for fight.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getFightBytes() {
    java.lang.Object ref = fight_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      fight_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ATTR_FIELD_NUMBER = 8;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.AttributeDataMsg> attr_;
  /**
   * <code>repeated .xddq.pb.AttributeDataMsg attr = 8;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.AttributeDataMsg> getAttrList() {
    return attr_;
  }
  /**
   * <code>repeated .xddq.pb.AttributeDataMsg attr = 8;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.AttributeDataMsgOrBuilder> 
      getAttrOrBuilderList() {
    return attr_;
  }
  /**
   * <code>repeated .xddq.pb.AttributeDataMsg attr = 8;</code>
   */
  @java.lang.Override
  public int getAttrCount() {
    return attr_.size();
  }
  /**
   * <code>repeated .xddq.pb.AttributeDataMsg attr = 8;</code>
   */
  @java.lang.Override
  public xddq.pb.AttributeDataMsg getAttr(int index) {
    return attr_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.AttributeDataMsg attr = 8;</code>
   */
  @java.lang.Override
  public xddq.pb.AttributeDataMsgOrBuilder getAttrOrBuilder(
      int index) {
    return attr_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    for (int i = 0; i < getAttrCount(); i++) {
      if (!getAttr(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeMessage(1, getHead());
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(2, body_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt32(3, order_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt32(4, site_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 5, fight_);
    }
    for (int i = 0; i < attr_.size(); i++) {
      output.writeMessage(8, attr_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, getHead());
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, body_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(3, order_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(4, site_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(5, fight_);
    }
    for (int i = 0; i < attr_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(8, attr_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.PeakFightPlayerBodyLocateMsg)) {
      return super.equals(obj);
    }
    xddq.pb.PeakFightPlayerBodyLocateMsg other = (xddq.pb.PeakFightPlayerBodyLocateMsg) obj;

    if (hasHead() != other.hasHead()) return false;
    if (hasHead()) {
      if (!getHead()
          .equals(other.getHead())) return false;
    }
    if (hasBody() != other.hasBody()) return false;
    if (hasBody()) {
      if (getBody()
          != other.getBody()) return false;
    }
    if (hasOrder() != other.hasOrder()) return false;
    if (hasOrder()) {
      if (getOrder()
          != other.getOrder()) return false;
    }
    if (hasSite() != other.hasSite()) return false;
    if (hasSite()) {
      if (getSite()
          != other.getSite()) return false;
    }
    if (hasFight() != other.hasFight()) return false;
    if (hasFight()) {
      if (!getFight()
          .equals(other.getFight())) return false;
    }
    if (!getAttrList()
        .equals(other.getAttrList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasHead()) {
      hash = (37 * hash) + HEAD_FIELD_NUMBER;
      hash = (53 * hash) + getHead().hashCode();
    }
    if (hasBody()) {
      hash = (37 * hash) + BODY_FIELD_NUMBER;
      hash = (53 * hash) + getBody();
    }
    if (hasOrder()) {
      hash = (37 * hash) + ORDER_FIELD_NUMBER;
      hash = (53 * hash) + getOrder();
    }
    if (hasSite()) {
      hash = (37 * hash) + SITE_FIELD_NUMBER;
      hash = (53 * hash) + getSite();
    }
    if (hasFight()) {
      hash = (37 * hash) + FIGHT_FIELD_NUMBER;
      hash = (53 * hash) + getFight().hashCode();
    }
    if (getAttrCount() > 0) {
      hash = (37 * hash) + ATTR_FIELD_NUMBER;
      hash = (53 * hash) + getAttrList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.PeakFightPlayerBodyLocateMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PeakFightPlayerBodyLocateMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PeakFightPlayerBodyLocateMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PeakFightPlayerBodyLocateMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PeakFightPlayerBodyLocateMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PeakFightPlayerBodyLocateMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PeakFightPlayerBodyLocateMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.PeakFightPlayerBodyLocateMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.PeakFightPlayerBodyLocateMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.PeakFightPlayerBodyLocateMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.PeakFightPlayerBodyLocateMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.PeakFightPlayerBodyLocateMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.PeakFightPlayerBodyLocateMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.PeakFightPlayerBodyLocateMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.PeakFightPlayerBodyLocateMsg)
      xddq.pb.PeakFightPlayerBodyLocateMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PeakFightPlayerBodyLocateMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PeakFightPlayerBodyLocateMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.PeakFightPlayerBodyLocateMsg.class, xddq.pb.PeakFightPlayerBodyLocateMsg.Builder.class);
    }

    // Construct using xddq.pb.PeakFightPlayerBodyLocateMsg.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetHeadFieldBuilder();
        internalGetAttrFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      head_ = null;
      if (headBuilder_ != null) {
        headBuilder_.dispose();
        headBuilder_ = null;
      }
      body_ = 0;
      order_ = 0;
      site_ = 0;
      fight_ = "";
      if (attrBuilder_ == null) {
        attr_ = java.util.Collections.emptyList();
      } else {
        attr_ = null;
        attrBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000020);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PeakFightPlayerBodyLocateMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.PeakFightPlayerBodyLocateMsg getDefaultInstanceForType() {
      return xddq.pb.PeakFightPlayerBodyLocateMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.PeakFightPlayerBodyLocateMsg build() {
      xddq.pb.PeakFightPlayerBodyLocateMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.PeakFightPlayerBodyLocateMsg buildPartial() {
      xddq.pb.PeakFightPlayerBodyLocateMsg result = new xddq.pb.PeakFightPlayerBodyLocateMsg(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.PeakFightPlayerBodyLocateMsg result) {
      if (attrBuilder_ == null) {
        if (((bitField0_ & 0x00000020) != 0)) {
          attr_ = java.util.Collections.unmodifiableList(attr_);
          bitField0_ = (bitField0_ & ~0x00000020);
        }
        result.attr_ = attr_;
      } else {
        result.attr_ = attrBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.PeakFightPlayerBodyLocateMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.head_ = headBuilder_ == null
            ? head_
            : headBuilder_.build();
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.body_ = body_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.order_ = order_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.site_ = site_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.fight_ = fight_;
        to_bitField0_ |= 0x00000010;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.PeakFightPlayerBodyLocateMsg) {
        return mergeFrom((xddq.pb.PeakFightPlayerBodyLocateMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.PeakFightPlayerBodyLocateMsg other) {
      if (other == xddq.pb.PeakFightPlayerBodyLocateMsg.getDefaultInstance()) return this;
      if (other.hasHead()) {
        mergeHead(other.getHead());
      }
      if (other.hasBody()) {
        setBody(other.getBody());
      }
      if (other.hasOrder()) {
        setOrder(other.getOrder());
      }
      if (other.hasSite()) {
        setSite(other.getSite());
      }
      if (other.hasFight()) {
        fight_ = other.fight_;
        bitField0_ |= 0x00000010;
        onChanged();
      }
      if (attrBuilder_ == null) {
        if (!other.attr_.isEmpty()) {
          if (attr_.isEmpty()) {
            attr_ = other.attr_;
            bitField0_ = (bitField0_ & ~0x00000020);
          } else {
            ensureAttrIsMutable();
            attr_.addAll(other.attr_);
          }
          onChanged();
        }
      } else {
        if (!other.attr_.isEmpty()) {
          if (attrBuilder_.isEmpty()) {
            attrBuilder_.dispose();
            attrBuilder_ = null;
            attr_ = other.attr_;
            bitField0_ = (bitField0_ & ~0x00000020);
            attrBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetAttrFieldBuilder() : null;
          } else {
            attrBuilder_.addAllMessages(other.attr_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      for (int i = 0; i < getAttrCount(); i++) {
        if (!getAttr(i).isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              input.readMessage(
                  internalGetHeadFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 16: {
              body_ = input.readInt32();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 24: {
              order_ = input.readInt32();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 32: {
              site_ = input.readInt32();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            case 42: {
              fight_ = input.readBytes();
              bitField0_ |= 0x00000010;
              break;
            } // case 42
            case 66: {
              xddq.pb.AttributeDataMsg m =
                  input.readMessage(
                      xddq.pb.AttributeDataMsg.parser(),
                      extensionRegistry);
              if (attrBuilder_ == null) {
                ensureAttrIsMutable();
                attr_.add(m);
              } else {
                attrBuilder_.addMessage(m);
              }
              break;
            } // case 66
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private xddq.pb.PlayerHeadDataMsg head_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PlayerHeadDataMsg, xddq.pb.PlayerHeadDataMsg.Builder, xddq.pb.PlayerHeadDataMsgOrBuilder> headBuilder_;
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg head = 1;</code>
     * @return Whether the head field is set.
     */
    public boolean hasHead() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg head = 1;</code>
     * @return The head.
     */
    public xddq.pb.PlayerHeadDataMsg getHead() {
      if (headBuilder_ == null) {
        return head_ == null ? xddq.pb.PlayerHeadDataMsg.getDefaultInstance() : head_;
      } else {
        return headBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg head = 1;</code>
     */
    public Builder setHead(xddq.pb.PlayerHeadDataMsg value) {
      if (headBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        head_ = value;
      } else {
        headBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg head = 1;</code>
     */
    public Builder setHead(
        xddq.pb.PlayerHeadDataMsg.Builder builderForValue) {
      if (headBuilder_ == null) {
        head_ = builderForValue.build();
      } else {
        headBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg head = 1;</code>
     */
    public Builder mergeHead(xddq.pb.PlayerHeadDataMsg value) {
      if (headBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0) &&
          head_ != null &&
          head_ != xddq.pb.PlayerHeadDataMsg.getDefaultInstance()) {
          getHeadBuilder().mergeFrom(value);
        } else {
          head_ = value;
        }
      } else {
        headBuilder_.mergeFrom(value);
      }
      if (head_ != null) {
        bitField0_ |= 0x00000001;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg head = 1;</code>
     */
    public Builder clearHead() {
      bitField0_ = (bitField0_ & ~0x00000001);
      head_ = null;
      if (headBuilder_ != null) {
        headBuilder_.dispose();
        headBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg head = 1;</code>
     */
    public xddq.pb.PlayerHeadDataMsg.Builder getHeadBuilder() {
      bitField0_ |= 0x00000001;
      onChanged();
      return internalGetHeadFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg head = 1;</code>
     */
    public xddq.pb.PlayerHeadDataMsgOrBuilder getHeadOrBuilder() {
      if (headBuilder_ != null) {
        return headBuilder_.getMessageOrBuilder();
      } else {
        return head_ == null ?
            xddq.pb.PlayerHeadDataMsg.getDefaultInstance() : head_;
      }
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg head = 1;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PlayerHeadDataMsg, xddq.pb.PlayerHeadDataMsg.Builder, xddq.pb.PlayerHeadDataMsgOrBuilder> 
        internalGetHeadFieldBuilder() {
      if (headBuilder_ == null) {
        headBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.PlayerHeadDataMsg, xddq.pb.PlayerHeadDataMsg.Builder, xddq.pb.PlayerHeadDataMsgOrBuilder>(
                getHead(),
                getParentForChildren(),
                isClean());
        head_ = null;
      }
      return headBuilder_;
    }

    private int body_ ;
    /**
     * <code>optional int32 body = 2;</code>
     * @return Whether the body field is set.
     */
    @java.lang.Override
    public boolean hasBody() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 body = 2;</code>
     * @return The body.
     */
    @java.lang.Override
    public int getBody() {
      return body_;
    }
    /**
     * <code>optional int32 body = 2;</code>
     * @param value The body to set.
     * @return This builder for chaining.
     */
    public Builder setBody(int value) {

      body_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 body = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearBody() {
      bitField0_ = (bitField0_ & ~0x00000002);
      body_ = 0;
      onChanged();
      return this;
    }

    private int order_ ;
    /**
     * <code>optional int32 order = 3;</code>
     * @return Whether the order field is set.
     */
    @java.lang.Override
    public boolean hasOrder() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int32 order = 3;</code>
     * @return The order.
     */
    @java.lang.Override
    public int getOrder() {
      return order_;
    }
    /**
     * <code>optional int32 order = 3;</code>
     * @param value The order to set.
     * @return This builder for chaining.
     */
    public Builder setOrder(int value) {

      order_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 order = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearOrder() {
      bitField0_ = (bitField0_ & ~0x00000004);
      order_ = 0;
      onChanged();
      return this;
    }

    private int site_ ;
    /**
     * <code>optional int32 site = 4;</code>
     * @return Whether the site field is set.
     */
    @java.lang.Override
    public boolean hasSite() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int32 site = 4;</code>
     * @return The site.
     */
    @java.lang.Override
    public int getSite() {
      return site_;
    }
    /**
     * <code>optional int32 site = 4;</code>
     * @param value The site to set.
     * @return This builder for chaining.
     */
    public Builder setSite(int value) {

      site_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 site = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearSite() {
      bitField0_ = (bitField0_ & ~0x00000008);
      site_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object fight_ = "";
    /**
     * <code>optional string fight = 5;</code>
     * @return Whether the fight field is set.
     */
    public boolean hasFight() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional string fight = 5;</code>
     * @return The fight.
     */
    public java.lang.String getFight() {
      java.lang.Object ref = fight_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          fight_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string fight = 5;</code>
     * @return The bytes for fight.
     */
    public com.google.protobuf.ByteString
        getFightBytes() {
      java.lang.Object ref = fight_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        fight_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string fight = 5;</code>
     * @param value The fight to set.
     * @return This builder for chaining.
     */
    public Builder setFight(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      fight_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional string fight = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearFight() {
      fight_ = getDefaultInstance().getFight();
      bitField0_ = (bitField0_ & ~0x00000010);
      onChanged();
      return this;
    }
    /**
     * <code>optional string fight = 5;</code>
     * @param value The bytes for fight to set.
     * @return This builder for chaining.
     */
    public Builder setFightBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      fight_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.AttributeDataMsg> attr_ =
      java.util.Collections.emptyList();
    private void ensureAttrIsMutable() {
      if (!((bitField0_ & 0x00000020) != 0)) {
        attr_ = new java.util.ArrayList<xddq.pb.AttributeDataMsg>(attr_);
        bitField0_ |= 0x00000020;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.AttributeDataMsg, xddq.pb.AttributeDataMsg.Builder, xddq.pb.AttributeDataMsgOrBuilder> attrBuilder_;

    /**
     * <code>repeated .xddq.pb.AttributeDataMsg attr = 8;</code>
     */
    public java.util.List<xddq.pb.AttributeDataMsg> getAttrList() {
      if (attrBuilder_ == null) {
        return java.util.Collections.unmodifiableList(attr_);
      } else {
        return attrBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.AttributeDataMsg attr = 8;</code>
     */
    public int getAttrCount() {
      if (attrBuilder_ == null) {
        return attr_.size();
      } else {
        return attrBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.AttributeDataMsg attr = 8;</code>
     */
    public xddq.pb.AttributeDataMsg getAttr(int index) {
      if (attrBuilder_ == null) {
        return attr_.get(index);
      } else {
        return attrBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.AttributeDataMsg attr = 8;</code>
     */
    public Builder setAttr(
        int index, xddq.pb.AttributeDataMsg value) {
      if (attrBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureAttrIsMutable();
        attr_.set(index, value);
        onChanged();
      } else {
        attrBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.AttributeDataMsg attr = 8;</code>
     */
    public Builder setAttr(
        int index, xddq.pb.AttributeDataMsg.Builder builderForValue) {
      if (attrBuilder_ == null) {
        ensureAttrIsMutable();
        attr_.set(index, builderForValue.build());
        onChanged();
      } else {
        attrBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.AttributeDataMsg attr = 8;</code>
     */
    public Builder addAttr(xddq.pb.AttributeDataMsg value) {
      if (attrBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureAttrIsMutable();
        attr_.add(value);
        onChanged();
      } else {
        attrBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.AttributeDataMsg attr = 8;</code>
     */
    public Builder addAttr(
        int index, xddq.pb.AttributeDataMsg value) {
      if (attrBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureAttrIsMutable();
        attr_.add(index, value);
        onChanged();
      } else {
        attrBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.AttributeDataMsg attr = 8;</code>
     */
    public Builder addAttr(
        xddq.pb.AttributeDataMsg.Builder builderForValue) {
      if (attrBuilder_ == null) {
        ensureAttrIsMutable();
        attr_.add(builderForValue.build());
        onChanged();
      } else {
        attrBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.AttributeDataMsg attr = 8;</code>
     */
    public Builder addAttr(
        int index, xddq.pb.AttributeDataMsg.Builder builderForValue) {
      if (attrBuilder_ == null) {
        ensureAttrIsMutable();
        attr_.add(index, builderForValue.build());
        onChanged();
      } else {
        attrBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.AttributeDataMsg attr = 8;</code>
     */
    public Builder addAllAttr(
        java.lang.Iterable<? extends xddq.pb.AttributeDataMsg> values) {
      if (attrBuilder_ == null) {
        ensureAttrIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, attr_);
        onChanged();
      } else {
        attrBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.AttributeDataMsg attr = 8;</code>
     */
    public Builder clearAttr() {
      if (attrBuilder_ == null) {
        attr_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000020);
        onChanged();
      } else {
        attrBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.AttributeDataMsg attr = 8;</code>
     */
    public Builder removeAttr(int index) {
      if (attrBuilder_ == null) {
        ensureAttrIsMutable();
        attr_.remove(index);
        onChanged();
      } else {
        attrBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.AttributeDataMsg attr = 8;</code>
     */
    public xddq.pb.AttributeDataMsg.Builder getAttrBuilder(
        int index) {
      return internalGetAttrFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.AttributeDataMsg attr = 8;</code>
     */
    public xddq.pb.AttributeDataMsgOrBuilder getAttrOrBuilder(
        int index) {
      if (attrBuilder_ == null) {
        return attr_.get(index);  } else {
        return attrBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.AttributeDataMsg attr = 8;</code>
     */
    public java.util.List<? extends xddq.pb.AttributeDataMsgOrBuilder> 
         getAttrOrBuilderList() {
      if (attrBuilder_ != null) {
        return attrBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(attr_);
      }
    }
    /**
     * <code>repeated .xddq.pb.AttributeDataMsg attr = 8;</code>
     */
    public xddq.pb.AttributeDataMsg.Builder addAttrBuilder() {
      return internalGetAttrFieldBuilder().addBuilder(
          xddq.pb.AttributeDataMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.AttributeDataMsg attr = 8;</code>
     */
    public xddq.pb.AttributeDataMsg.Builder addAttrBuilder(
        int index) {
      return internalGetAttrFieldBuilder().addBuilder(
          index, xddq.pb.AttributeDataMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.AttributeDataMsg attr = 8;</code>
     */
    public java.util.List<xddq.pb.AttributeDataMsg.Builder> 
         getAttrBuilderList() {
      return internalGetAttrFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.AttributeDataMsg, xddq.pb.AttributeDataMsg.Builder, xddq.pb.AttributeDataMsgOrBuilder> 
        internalGetAttrFieldBuilder() {
      if (attrBuilder_ == null) {
        attrBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.AttributeDataMsg, xddq.pb.AttributeDataMsg.Builder, xddq.pb.AttributeDataMsgOrBuilder>(
                attr_,
                ((bitField0_ & 0x00000020) != 0),
                getParentForChildren(),
                isClean());
        attr_ = null;
      }
      return attrBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.PeakFightPlayerBodyLocateMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.PeakFightPlayerBodyLocateMsg)
  private static final xddq.pb.PeakFightPlayerBodyLocateMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.PeakFightPlayerBodyLocateMsg();
  }

  public static xddq.pb.PeakFightPlayerBodyLocateMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<PeakFightPlayerBodyLocateMsg>
      PARSER = new com.google.protobuf.AbstractParser<PeakFightPlayerBodyLocateMsg>() {
    @java.lang.Override
    public PeakFightPlayerBodyLocateMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<PeakFightPlayerBodyLocateMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<PeakFightPlayerBodyLocateMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.PeakFightPlayerBodyLocateMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

