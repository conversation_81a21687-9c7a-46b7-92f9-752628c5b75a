// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

public interface MonopolyEventHandleRespOrBuilder extends
    // @@protoc_insertion_point(interface_extends:xddq.pb.MonopolyEventHandleResp)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  boolean hasRet();
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  int getRet();

  /**
   * <code>optional int32 index = 2;</code>
   * @return Whether the index field is set.
   */
  boolean hasIndex();
  /**
   * <code>optional int32 index = 2;</code>
   * @return The index.
   */
  int getIndex();

  /**
   * <code>optional bytes resp = 3;</code>
   * @return Whether the resp field is set.
   */
  boolean hasResp();
  /**
   * <code>optional bytes resp = 3;</code>
   * @return The resp.
   */
  com.google.protobuf.ByteString getResp();
}
