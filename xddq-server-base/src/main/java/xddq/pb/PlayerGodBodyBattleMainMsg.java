// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.PlayerGodBodyBattleMainMsg}
 */
public final class PlayerGodBodyBattleMainMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.PlayerGodBodyBattleMainMsg)
    PlayerGodBodyBattleMainMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      PlayerGodBodyBattleMainMsg.class.getName());
  }
  // Use PlayerGodBodyBattleMainMsg.newBuilder() to construct.
  private PlayerGodBodyBattleMainMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private PlayerGodBodyBattleMainMsg() {
    battleMainStr_ = "";
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_PlayerGodBodyBattleMainMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_PlayerGodBodyBattleMainMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.PlayerGodBodyBattleMainMsg.class, xddq.pb.PlayerGodBodyBattleMainMsg.Builder.class);
  }

  private int bitField0_;
  public static final int BATTLEMAINSTR_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private volatile java.lang.Object battleMainStr_ = "";
  /**
   * <code>optional string battleMainStr = 1;</code>
   * @return Whether the battleMainStr field is set.
   */
  @java.lang.Override
  public boolean hasBattleMainStr() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional string battleMainStr = 1;</code>
   * @return The battleMainStr.
   */
  @java.lang.Override
  public java.lang.String getBattleMainStr() {
    java.lang.Object ref = battleMainStr_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        battleMainStr_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string battleMainStr = 1;</code>
   * @return The bytes for battleMainStr.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getBattleMainStrBytes() {
    java.lang.Object ref = battleMainStr_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      battleMainStr_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int INDEX_FIELD_NUMBER = 2;
  private int index_ = 0;
  /**
   * <code>optional int32 index = 2;</code>
   * @return Whether the index field is set.
   */
  @java.lang.Override
  public boolean hasIndex() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int32 index = 2;</code>
   * @return The index.
   */
  @java.lang.Override
  public int getIndex() {
    return index_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 1, battleMainStr_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(2, index_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(1, battleMainStr_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, index_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.PlayerGodBodyBattleMainMsg)) {
      return super.equals(obj);
    }
    xddq.pb.PlayerGodBodyBattleMainMsg other = (xddq.pb.PlayerGodBodyBattleMainMsg) obj;

    if (hasBattleMainStr() != other.hasBattleMainStr()) return false;
    if (hasBattleMainStr()) {
      if (!getBattleMainStr()
          .equals(other.getBattleMainStr())) return false;
    }
    if (hasIndex() != other.hasIndex()) return false;
    if (hasIndex()) {
      if (getIndex()
          != other.getIndex()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasBattleMainStr()) {
      hash = (37 * hash) + BATTLEMAINSTR_FIELD_NUMBER;
      hash = (53 * hash) + getBattleMainStr().hashCode();
    }
    if (hasIndex()) {
      hash = (37 * hash) + INDEX_FIELD_NUMBER;
      hash = (53 * hash) + getIndex();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.PlayerGodBodyBattleMainMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PlayerGodBodyBattleMainMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PlayerGodBodyBattleMainMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PlayerGodBodyBattleMainMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PlayerGodBodyBattleMainMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PlayerGodBodyBattleMainMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PlayerGodBodyBattleMainMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.PlayerGodBodyBattleMainMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.PlayerGodBodyBattleMainMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.PlayerGodBodyBattleMainMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.PlayerGodBodyBattleMainMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.PlayerGodBodyBattleMainMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.PlayerGodBodyBattleMainMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.PlayerGodBodyBattleMainMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.PlayerGodBodyBattleMainMsg)
      xddq.pb.PlayerGodBodyBattleMainMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PlayerGodBodyBattleMainMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PlayerGodBodyBattleMainMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.PlayerGodBodyBattleMainMsg.class, xddq.pb.PlayerGodBodyBattleMainMsg.Builder.class);
    }

    // Construct using xddq.pb.PlayerGodBodyBattleMainMsg.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      battleMainStr_ = "";
      index_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PlayerGodBodyBattleMainMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.PlayerGodBodyBattleMainMsg getDefaultInstanceForType() {
      return xddq.pb.PlayerGodBodyBattleMainMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.PlayerGodBodyBattleMainMsg build() {
      xddq.pb.PlayerGodBodyBattleMainMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.PlayerGodBodyBattleMainMsg buildPartial() {
      xddq.pb.PlayerGodBodyBattleMainMsg result = new xddq.pb.PlayerGodBodyBattleMainMsg(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.PlayerGodBodyBattleMainMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.battleMainStr_ = battleMainStr_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.index_ = index_;
        to_bitField0_ |= 0x00000002;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.PlayerGodBodyBattleMainMsg) {
        return mergeFrom((xddq.pb.PlayerGodBodyBattleMainMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.PlayerGodBodyBattleMainMsg other) {
      if (other == xddq.pb.PlayerGodBodyBattleMainMsg.getDefaultInstance()) return this;
      if (other.hasBattleMainStr()) {
        battleMainStr_ = other.battleMainStr_;
        bitField0_ |= 0x00000001;
        onChanged();
      }
      if (other.hasIndex()) {
        setIndex(other.getIndex());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              battleMainStr_ = input.readBytes();
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 16: {
              index_ = input.readInt32();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.lang.Object battleMainStr_ = "";
    /**
     * <code>optional string battleMainStr = 1;</code>
     * @return Whether the battleMainStr field is set.
     */
    public boolean hasBattleMainStr() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional string battleMainStr = 1;</code>
     * @return The battleMainStr.
     */
    public java.lang.String getBattleMainStr() {
      java.lang.Object ref = battleMainStr_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          battleMainStr_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string battleMainStr = 1;</code>
     * @return The bytes for battleMainStr.
     */
    public com.google.protobuf.ByteString
        getBattleMainStrBytes() {
      java.lang.Object ref = battleMainStr_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        battleMainStr_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string battleMainStr = 1;</code>
     * @param value The battleMainStr to set.
     * @return This builder for chaining.
     */
    public Builder setBattleMainStr(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      battleMainStr_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional string battleMainStr = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearBattleMainStr() {
      battleMainStr_ = getDefaultInstance().getBattleMainStr();
      bitField0_ = (bitField0_ & ~0x00000001);
      onChanged();
      return this;
    }
    /**
     * <code>optional string battleMainStr = 1;</code>
     * @param value The bytes for battleMainStr to set.
     * @return This builder for chaining.
     */
    public Builder setBattleMainStrBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      battleMainStr_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }

    private int index_ ;
    /**
     * <code>optional int32 index = 2;</code>
     * @return Whether the index field is set.
     */
    @java.lang.Override
    public boolean hasIndex() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 index = 2;</code>
     * @return The index.
     */
    @java.lang.Override
    public int getIndex() {
      return index_;
    }
    /**
     * <code>optional int32 index = 2;</code>
     * @param value The index to set.
     * @return This builder for chaining.
     */
    public Builder setIndex(int value) {

      index_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 index = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearIndex() {
      bitField0_ = (bitField0_ & ~0x00000002);
      index_ = 0;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.PlayerGodBodyBattleMainMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.PlayerGodBodyBattleMainMsg)
  private static final xddq.pb.PlayerGodBodyBattleMainMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.PlayerGodBodyBattleMainMsg();
  }

  public static xddq.pb.PlayerGodBodyBattleMainMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<PlayerGodBodyBattleMainMsg>
      PARSER = new com.google.protobuf.AbstractParser<PlayerGodBodyBattleMainMsg>() {
    @java.lang.Override
    public PlayerGodBodyBattleMainMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<PlayerGodBodyBattleMainMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<PlayerGodBodyBattleMainMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.PlayerGodBodyBattleMainMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

