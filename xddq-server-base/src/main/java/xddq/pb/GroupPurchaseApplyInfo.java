// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.GroupPurchaseApplyInfo}
 */
public final class GroupPurchaseApplyInfo extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.GroupPurchaseApplyInfo)
    GroupPurchaseApplyInfoOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      GroupPurchaseApplyInfo.class.getName());
  }
  // Use GroupPurchaseApplyInfo.newBuilder() to construct.
  private GroupPurchaseApplyInfo(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private GroupPurchaseApplyInfo() {
    fightVal_ = "";
    nickName_ = "";
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_GroupPurchaseApplyInfo_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_GroupPurchaseApplyInfo_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.GroupPurchaseApplyInfo.class, xddq.pb.GroupPurchaseApplyInfo.Builder.class);
  }

  private int bitField0_;
  public static final int HEADDATA_FIELD_NUMBER = 1;
  private xddq.pb.PlayerHeadDataMsg headData_;
  /**
   * <code>optional .xddq.pb.PlayerHeadDataMsg headData = 1;</code>
   * @return Whether the headData field is set.
   */
  @java.lang.Override
  public boolean hasHeadData() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional .xddq.pb.PlayerHeadDataMsg headData = 1;</code>
   * @return The headData.
   */
  @java.lang.Override
  public xddq.pb.PlayerHeadDataMsg getHeadData() {
    return headData_ == null ? xddq.pb.PlayerHeadDataMsg.getDefaultInstance() : headData_;
  }
  /**
   * <code>optional .xddq.pb.PlayerHeadDataMsg headData = 1;</code>
   */
  @java.lang.Override
  public xddq.pb.PlayerHeadDataMsgOrBuilder getHeadDataOrBuilder() {
    return headData_ == null ? xddq.pb.PlayerHeadDataMsg.getDefaultInstance() : headData_;
  }

  public static final int FIGHTVAL_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private volatile java.lang.Object fightVal_ = "";
  /**
   * <code>optional string fightVal = 2;</code>
   * @return Whether the fightVal field is set.
   */
  @java.lang.Override
  public boolean hasFightVal() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional string fightVal = 2;</code>
   * @return The fightVal.
   */
  @java.lang.Override
  public java.lang.String getFightVal() {
    java.lang.Object ref = fightVal_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        fightVal_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string fightVal = 2;</code>
   * @return The bytes for fightVal.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getFightValBytes() {
    java.lang.Object ref = fightVal_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      fightVal_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int APPLYTIME_FIELD_NUMBER = 3;
  private long applyTime_ = 0L;
  /**
   * <code>optional int64 applyTime = 3;</code>
   * @return Whether the applyTime field is set.
   */
  @java.lang.Override
  public boolean hasApplyTime() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional int64 applyTime = 3;</code>
   * @return The applyTime.
   */
  @java.lang.Override
  public long getApplyTime() {
    return applyTime_;
  }

  public static final int NICKNAME_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private volatile java.lang.Object nickName_ = "";
  /**
   * <code>optional string nickName = 4;</code>
   * @return Whether the nickName field is set.
   */
  @java.lang.Override
  public boolean hasNickName() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional string nickName = 4;</code>
   * @return The nickName.
   */
  @java.lang.Override
  public java.lang.String getNickName() {
    java.lang.Object ref = nickName_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        nickName_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string nickName = 4;</code>
   * @return The bytes for nickName.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getNickNameBytes() {
    java.lang.Object ref = nickName_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      nickName_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeMessage(1, getHeadData());
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 2, fightVal_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt64(3, applyTime_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 4, nickName_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, getHeadData());
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(2, fightVal_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(3, applyTime_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(4, nickName_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.GroupPurchaseApplyInfo)) {
      return super.equals(obj);
    }
    xddq.pb.GroupPurchaseApplyInfo other = (xddq.pb.GroupPurchaseApplyInfo) obj;

    if (hasHeadData() != other.hasHeadData()) return false;
    if (hasHeadData()) {
      if (!getHeadData()
          .equals(other.getHeadData())) return false;
    }
    if (hasFightVal() != other.hasFightVal()) return false;
    if (hasFightVal()) {
      if (!getFightVal()
          .equals(other.getFightVal())) return false;
    }
    if (hasApplyTime() != other.hasApplyTime()) return false;
    if (hasApplyTime()) {
      if (getApplyTime()
          != other.getApplyTime()) return false;
    }
    if (hasNickName() != other.hasNickName()) return false;
    if (hasNickName()) {
      if (!getNickName()
          .equals(other.getNickName())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasHeadData()) {
      hash = (37 * hash) + HEADDATA_FIELD_NUMBER;
      hash = (53 * hash) + getHeadData().hashCode();
    }
    if (hasFightVal()) {
      hash = (37 * hash) + FIGHTVAL_FIELD_NUMBER;
      hash = (53 * hash) + getFightVal().hashCode();
    }
    if (hasApplyTime()) {
      hash = (37 * hash) + APPLYTIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getApplyTime());
    }
    if (hasNickName()) {
      hash = (37 * hash) + NICKNAME_FIELD_NUMBER;
      hash = (53 * hash) + getNickName().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.GroupPurchaseApplyInfo parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GroupPurchaseApplyInfo parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GroupPurchaseApplyInfo parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GroupPurchaseApplyInfo parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GroupPurchaseApplyInfo parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GroupPurchaseApplyInfo parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GroupPurchaseApplyInfo parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.GroupPurchaseApplyInfo parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.GroupPurchaseApplyInfo parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.GroupPurchaseApplyInfo parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.GroupPurchaseApplyInfo parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.GroupPurchaseApplyInfo parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.GroupPurchaseApplyInfo prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.GroupPurchaseApplyInfo}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.GroupPurchaseApplyInfo)
      xddq.pb.GroupPurchaseApplyInfoOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GroupPurchaseApplyInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GroupPurchaseApplyInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.GroupPurchaseApplyInfo.class, xddq.pb.GroupPurchaseApplyInfo.Builder.class);
    }

    // Construct using xddq.pb.GroupPurchaseApplyInfo.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetHeadDataFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      headData_ = null;
      if (headDataBuilder_ != null) {
        headDataBuilder_.dispose();
        headDataBuilder_ = null;
      }
      fightVal_ = "";
      applyTime_ = 0L;
      nickName_ = "";
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GroupPurchaseApplyInfo_descriptor;
    }

    @java.lang.Override
    public xddq.pb.GroupPurchaseApplyInfo getDefaultInstanceForType() {
      return xddq.pb.GroupPurchaseApplyInfo.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.GroupPurchaseApplyInfo build() {
      xddq.pb.GroupPurchaseApplyInfo result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.GroupPurchaseApplyInfo buildPartial() {
      xddq.pb.GroupPurchaseApplyInfo result = new xddq.pb.GroupPurchaseApplyInfo(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.GroupPurchaseApplyInfo result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.headData_ = headDataBuilder_ == null
            ? headData_
            : headDataBuilder_.build();
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.fightVal_ = fightVal_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.applyTime_ = applyTime_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.nickName_ = nickName_;
        to_bitField0_ |= 0x00000008;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.GroupPurchaseApplyInfo) {
        return mergeFrom((xddq.pb.GroupPurchaseApplyInfo)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.GroupPurchaseApplyInfo other) {
      if (other == xddq.pb.GroupPurchaseApplyInfo.getDefaultInstance()) return this;
      if (other.hasHeadData()) {
        mergeHeadData(other.getHeadData());
      }
      if (other.hasFightVal()) {
        fightVal_ = other.fightVal_;
        bitField0_ |= 0x00000002;
        onChanged();
      }
      if (other.hasApplyTime()) {
        setApplyTime(other.getApplyTime());
      }
      if (other.hasNickName()) {
        nickName_ = other.nickName_;
        bitField0_ |= 0x00000008;
        onChanged();
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              input.readMessage(
                  internalGetHeadDataFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 18: {
              fightVal_ = input.readBytes();
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 24: {
              applyTime_ = input.readInt64();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 34: {
              nickName_ = input.readBytes();
              bitField0_ |= 0x00000008;
              break;
            } // case 34
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private xddq.pb.PlayerHeadDataMsg headData_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PlayerHeadDataMsg, xddq.pb.PlayerHeadDataMsg.Builder, xddq.pb.PlayerHeadDataMsgOrBuilder> headDataBuilder_;
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg headData = 1;</code>
     * @return Whether the headData field is set.
     */
    public boolean hasHeadData() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg headData = 1;</code>
     * @return The headData.
     */
    public xddq.pb.PlayerHeadDataMsg getHeadData() {
      if (headDataBuilder_ == null) {
        return headData_ == null ? xddq.pb.PlayerHeadDataMsg.getDefaultInstance() : headData_;
      } else {
        return headDataBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg headData = 1;</code>
     */
    public Builder setHeadData(xddq.pb.PlayerHeadDataMsg value) {
      if (headDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        headData_ = value;
      } else {
        headDataBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg headData = 1;</code>
     */
    public Builder setHeadData(
        xddq.pb.PlayerHeadDataMsg.Builder builderForValue) {
      if (headDataBuilder_ == null) {
        headData_ = builderForValue.build();
      } else {
        headDataBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg headData = 1;</code>
     */
    public Builder mergeHeadData(xddq.pb.PlayerHeadDataMsg value) {
      if (headDataBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0) &&
          headData_ != null &&
          headData_ != xddq.pb.PlayerHeadDataMsg.getDefaultInstance()) {
          getHeadDataBuilder().mergeFrom(value);
        } else {
          headData_ = value;
        }
      } else {
        headDataBuilder_.mergeFrom(value);
      }
      if (headData_ != null) {
        bitField0_ |= 0x00000001;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg headData = 1;</code>
     */
    public Builder clearHeadData() {
      bitField0_ = (bitField0_ & ~0x00000001);
      headData_ = null;
      if (headDataBuilder_ != null) {
        headDataBuilder_.dispose();
        headDataBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg headData = 1;</code>
     */
    public xddq.pb.PlayerHeadDataMsg.Builder getHeadDataBuilder() {
      bitField0_ |= 0x00000001;
      onChanged();
      return internalGetHeadDataFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg headData = 1;</code>
     */
    public xddq.pb.PlayerHeadDataMsgOrBuilder getHeadDataOrBuilder() {
      if (headDataBuilder_ != null) {
        return headDataBuilder_.getMessageOrBuilder();
      } else {
        return headData_ == null ?
            xddq.pb.PlayerHeadDataMsg.getDefaultInstance() : headData_;
      }
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg headData = 1;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PlayerHeadDataMsg, xddq.pb.PlayerHeadDataMsg.Builder, xddq.pb.PlayerHeadDataMsgOrBuilder> 
        internalGetHeadDataFieldBuilder() {
      if (headDataBuilder_ == null) {
        headDataBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.PlayerHeadDataMsg, xddq.pb.PlayerHeadDataMsg.Builder, xddq.pb.PlayerHeadDataMsgOrBuilder>(
                getHeadData(),
                getParentForChildren(),
                isClean());
        headData_ = null;
      }
      return headDataBuilder_;
    }

    private java.lang.Object fightVal_ = "";
    /**
     * <code>optional string fightVal = 2;</code>
     * @return Whether the fightVal field is set.
     */
    public boolean hasFightVal() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional string fightVal = 2;</code>
     * @return The fightVal.
     */
    public java.lang.String getFightVal() {
      java.lang.Object ref = fightVal_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          fightVal_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string fightVal = 2;</code>
     * @return The bytes for fightVal.
     */
    public com.google.protobuf.ByteString
        getFightValBytes() {
      java.lang.Object ref = fightVal_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        fightVal_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string fightVal = 2;</code>
     * @param value The fightVal to set.
     * @return This builder for chaining.
     */
    public Builder setFightVal(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      fightVal_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional string fightVal = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearFightVal() {
      fightVal_ = getDefaultInstance().getFightVal();
      bitField0_ = (bitField0_ & ~0x00000002);
      onChanged();
      return this;
    }
    /**
     * <code>optional string fightVal = 2;</code>
     * @param value The bytes for fightVal to set.
     * @return This builder for chaining.
     */
    public Builder setFightValBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      fightVal_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }

    private long applyTime_ ;
    /**
     * <code>optional int64 applyTime = 3;</code>
     * @return Whether the applyTime field is set.
     */
    @java.lang.Override
    public boolean hasApplyTime() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int64 applyTime = 3;</code>
     * @return The applyTime.
     */
    @java.lang.Override
    public long getApplyTime() {
      return applyTime_;
    }
    /**
     * <code>optional int64 applyTime = 3;</code>
     * @param value The applyTime to set.
     * @return This builder for chaining.
     */
    public Builder setApplyTime(long value) {

      applyTime_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 applyTime = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearApplyTime() {
      bitField0_ = (bitField0_ & ~0x00000004);
      applyTime_ = 0L;
      onChanged();
      return this;
    }

    private java.lang.Object nickName_ = "";
    /**
     * <code>optional string nickName = 4;</code>
     * @return Whether the nickName field is set.
     */
    public boolean hasNickName() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional string nickName = 4;</code>
     * @return The nickName.
     */
    public java.lang.String getNickName() {
      java.lang.Object ref = nickName_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          nickName_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string nickName = 4;</code>
     * @return The bytes for nickName.
     */
    public com.google.protobuf.ByteString
        getNickNameBytes() {
      java.lang.Object ref = nickName_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        nickName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string nickName = 4;</code>
     * @param value The nickName to set.
     * @return This builder for chaining.
     */
    public Builder setNickName(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      nickName_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional string nickName = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearNickName() {
      nickName_ = getDefaultInstance().getNickName();
      bitField0_ = (bitField0_ & ~0x00000008);
      onChanged();
      return this;
    }
    /**
     * <code>optional string nickName = 4;</code>
     * @param value The bytes for nickName to set.
     * @return This builder for chaining.
     */
    public Builder setNickNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      nickName_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.GroupPurchaseApplyInfo)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.GroupPurchaseApplyInfo)
  private static final xddq.pb.GroupPurchaseApplyInfo DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.GroupPurchaseApplyInfo();
  }

  public static xddq.pb.GroupPurchaseApplyInfo getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<GroupPurchaseApplyInfo>
      PARSER = new com.google.protobuf.AbstractParser<GroupPurchaseApplyInfo>() {
    @java.lang.Override
    public GroupPurchaseApplyInfo parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<GroupPurchaseApplyInfo> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<GroupPurchaseApplyInfo> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.GroupPurchaseApplyInfo getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

