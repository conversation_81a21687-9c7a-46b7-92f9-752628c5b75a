// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

public interface UnionTreasureAskForReqOrBuilder extends
    // @@protoc_insertion_point(interface_extends:xddq.pb.UnionTreasureAskForReq)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>optional int32 itemId = 1;</code>
   * @return Whether the itemId field is set.
   */
  boolean hasItemId();
  /**
   * <code>optional int32 itemId = 1;</code>
   * @return The itemId.
   */
  int getItemId();
}
