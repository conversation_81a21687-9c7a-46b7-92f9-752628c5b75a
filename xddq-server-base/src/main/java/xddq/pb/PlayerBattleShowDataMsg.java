// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.PlayerBattleShowDataMsg}
 */
public final class PlayerBattleShowDataMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.PlayerBattleShowDataMsg)
    PlayerBattleShowDataMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      PlayerBattleShowDataMsg.class.getName());
  }
  // Use PlayerBattleShowDataMsg.newBuilder() to construct.
  private PlayerBattleShowDataMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private PlayerBattleShowDataMsg() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_PlayerBattleShowDataMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_PlayerBattleShowDataMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.PlayerBattleShowDataMsg.class, xddq.pb.PlayerBattleShowDataMsg.Builder.class);
  }

  private int bitField0_;
  public static final int PLAYERBASEDATAMSG_FIELD_NUMBER = 1;
  private xddq.pb.PlayerBaseDataMsg playerBaseDataMsg_;
  /**
   * <code>optional .xddq.pb.PlayerBaseDataMsg playerBaseDataMsg = 1;</code>
   * @return Whether the playerBaseDataMsg field is set.
   */
  @java.lang.Override
  public boolean hasPlayerBaseDataMsg() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional .xddq.pb.PlayerBaseDataMsg playerBaseDataMsg = 1;</code>
   * @return The playerBaseDataMsg.
   */
  @java.lang.Override
  public xddq.pb.PlayerBaseDataMsg getPlayerBaseDataMsg() {
    return playerBaseDataMsg_ == null ? xddq.pb.PlayerBaseDataMsg.getDefaultInstance() : playerBaseDataMsg_;
  }
  /**
   * <code>optional .xddq.pb.PlayerBaseDataMsg playerBaseDataMsg = 1;</code>
   */
  @java.lang.Override
  public xddq.pb.PlayerBaseDataMsgOrBuilder getPlayerBaseDataMsgOrBuilder() {
    return playerBaseDataMsg_ == null ? xddq.pb.PlayerBaseDataMsg.getDefaultInstance() : playerBaseDataMsg_;
  }

  public static final int SCORE_FIELD_NUMBER = 2;
  private long score_ = 0L;
  /**
   * <code>optional int64 score = 2;</code>
   * @return Whether the score field is set.
   */
  @java.lang.Override
  public boolean hasScore() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int64 score = 2;</code>
   * @return The score.
   */
  @java.lang.Override
  public long getScore() {
    return score_;
  }

  public static final int PROTECTENDTIME_FIELD_NUMBER = 3;
  private long protectEndTime_ = 0L;
  /**
   * <code>optional int64 protectEndTime = 3;</code>
   * @return Whether the protectEndTime field is set.
   */
  @java.lang.Override
  public boolean hasProtectEndTime() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional int64 protectEndTime = 3;</code>
   * @return The protectEndTime.
   */
  @java.lang.Override
  public long getProtectEndTime() {
    return protectEndTime_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeMessage(1, getPlayerBaseDataMsg());
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt64(2, score_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt64(3, protectEndTime_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, getPlayerBaseDataMsg());
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(2, score_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(3, protectEndTime_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.PlayerBattleShowDataMsg)) {
      return super.equals(obj);
    }
    xddq.pb.PlayerBattleShowDataMsg other = (xddq.pb.PlayerBattleShowDataMsg) obj;

    if (hasPlayerBaseDataMsg() != other.hasPlayerBaseDataMsg()) return false;
    if (hasPlayerBaseDataMsg()) {
      if (!getPlayerBaseDataMsg()
          .equals(other.getPlayerBaseDataMsg())) return false;
    }
    if (hasScore() != other.hasScore()) return false;
    if (hasScore()) {
      if (getScore()
          != other.getScore()) return false;
    }
    if (hasProtectEndTime() != other.hasProtectEndTime()) return false;
    if (hasProtectEndTime()) {
      if (getProtectEndTime()
          != other.getProtectEndTime()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasPlayerBaseDataMsg()) {
      hash = (37 * hash) + PLAYERBASEDATAMSG_FIELD_NUMBER;
      hash = (53 * hash) + getPlayerBaseDataMsg().hashCode();
    }
    if (hasScore()) {
      hash = (37 * hash) + SCORE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getScore());
    }
    if (hasProtectEndTime()) {
      hash = (37 * hash) + PROTECTENDTIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getProtectEndTime());
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.PlayerBattleShowDataMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PlayerBattleShowDataMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PlayerBattleShowDataMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PlayerBattleShowDataMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PlayerBattleShowDataMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PlayerBattleShowDataMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PlayerBattleShowDataMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.PlayerBattleShowDataMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.PlayerBattleShowDataMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.PlayerBattleShowDataMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.PlayerBattleShowDataMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.PlayerBattleShowDataMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.PlayerBattleShowDataMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.PlayerBattleShowDataMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.PlayerBattleShowDataMsg)
      xddq.pb.PlayerBattleShowDataMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PlayerBattleShowDataMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PlayerBattleShowDataMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.PlayerBattleShowDataMsg.class, xddq.pb.PlayerBattleShowDataMsg.Builder.class);
    }

    // Construct using xddq.pb.PlayerBattleShowDataMsg.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetPlayerBaseDataMsgFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      playerBaseDataMsg_ = null;
      if (playerBaseDataMsgBuilder_ != null) {
        playerBaseDataMsgBuilder_.dispose();
        playerBaseDataMsgBuilder_ = null;
      }
      score_ = 0L;
      protectEndTime_ = 0L;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PlayerBattleShowDataMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.PlayerBattleShowDataMsg getDefaultInstanceForType() {
      return xddq.pb.PlayerBattleShowDataMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.PlayerBattleShowDataMsg build() {
      xddq.pb.PlayerBattleShowDataMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.PlayerBattleShowDataMsg buildPartial() {
      xddq.pb.PlayerBattleShowDataMsg result = new xddq.pb.PlayerBattleShowDataMsg(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.PlayerBattleShowDataMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.playerBaseDataMsg_ = playerBaseDataMsgBuilder_ == null
            ? playerBaseDataMsg_
            : playerBaseDataMsgBuilder_.build();
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.score_ = score_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.protectEndTime_ = protectEndTime_;
        to_bitField0_ |= 0x00000004;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.PlayerBattleShowDataMsg) {
        return mergeFrom((xddq.pb.PlayerBattleShowDataMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.PlayerBattleShowDataMsg other) {
      if (other == xddq.pb.PlayerBattleShowDataMsg.getDefaultInstance()) return this;
      if (other.hasPlayerBaseDataMsg()) {
        mergePlayerBaseDataMsg(other.getPlayerBaseDataMsg());
      }
      if (other.hasScore()) {
        setScore(other.getScore());
      }
      if (other.hasProtectEndTime()) {
        setProtectEndTime(other.getProtectEndTime());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              input.readMessage(
                  internalGetPlayerBaseDataMsgFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 16: {
              score_ = input.readInt64();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 24: {
              protectEndTime_ = input.readInt64();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private xddq.pb.PlayerBaseDataMsg playerBaseDataMsg_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PlayerBaseDataMsg, xddq.pb.PlayerBaseDataMsg.Builder, xddq.pb.PlayerBaseDataMsgOrBuilder> playerBaseDataMsgBuilder_;
    /**
     * <code>optional .xddq.pb.PlayerBaseDataMsg playerBaseDataMsg = 1;</code>
     * @return Whether the playerBaseDataMsg field is set.
     */
    public boolean hasPlayerBaseDataMsg() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .xddq.pb.PlayerBaseDataMsg playerBaseDataMsg = 1;</code>
     * @return The playerBaseDataMsg.
     */
    public xddq.pb.PlayerBaseDataMsg getPlayerBaseDataMsg() {
      if (playerBaseDataMsgBuilder_ == null) {
        return playerBaseDataMsg_ == null ? xddq.pb.PlayerBaseDataMsg.getDefaultInstance() : playerBaseDataMsg_;
      } else {
        return playerBaseDataMsgBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.PlayerBaseDataMsg playerBaseDataMsg = 1;</code>
     */
    public Builder setPlayerBaseDataMsg(xddq.pb.PlayerBaseDataMsg value) {
      if (playerBaseDataMsgBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        playerBaseDataMsg_ = value;
      } else {
        playerBaseDataMsgBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerBaseDataMsg playerBaseDataMsg = 1;</code>
     */
    public Builder setPlayerBaseDataMsg(
        xddq.pb.PlayerBaseDataMsg.Builder builderForValue) {
      if (playerBaseDataMsgBuilder_ == null) {
        playerBaseDataMsg_ = builderForValue.build();
      } else {
        playerBaseDataMsgBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerBaseDataMsg playerBaseDataMsg = 1;</code>
     */
    public Builder mergePlayerBaseDataMsg(xddq.pb.PlayerBaseDataMsg value) {
      if (playerBaseDataMsgBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0) &&
          playerBaseDataMsg_ != null &&
          playerBaseDataMsg_ != xddq.pb.PlayerBaseDataMsg.getDefaultInstance()) {
          getPlayerBaseDataMsgBuilder().mergeFrom(value);
        } else {
          playerBaseDataMsg_ = value;
        }
      } else {
        playerBaseDataMsgBuilder_.mergeFrom(value);
      }
      if (playerBaseDataMsg_ != null) {
        bitField0_ |= 0x00000001;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerBaseDataMsg playerBaseDataMsg = 1;</code>
     */
    public Builder clearPlayerBaseDataMsg() {
      bitField0_ = (bitField0_ & ~0x00000001);
      playerBaseDataMsg_ = null;
      if (playerBaseDataMsgBuilder_ != null) {
        playerBaseDataMsgBuilder_.dispose();
        playerBaseDataMsgBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerBaseDataMsg playerBaseDataMsg = 1;</code>
     */
    public xddq.pb.PlayerBaseDataMsg.Builder getPlayerBaseDataMsgBuilder() {
      bitField0_ |= 0x00000001;
      onChanged();
      return internalGetPlayerBaseDataMsgFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.PlayerBaseDataMsg playerBaseDataMsg = 1;</code>
     */
    public xddq.pb.PlayerBaseDataMsgOrBuilder getPlayerBaseDataMsgOrBuilder() {
      if (playerBaseDataMsgBuilder_ != null) {
        return playerBaseDataMsgBuilder_.getMessageOrBuilder();
      } else {
        return playerBaseDataMsg_ == null ?
            xddq.pb.PlayerBaseDataMsg.getDefaultInstance() : playerBaseDataMsg_;
      }
    }
    /**
     * <code>optional .xddq.pb.PlayerBaseDataMsg playerBaseDataMsg = 1;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PlayerBaseDataMsg, xddq.pb.PlayerBaseDataMsg.Builder, xddq.pb.PlayerBaseDataMsgOrBuilder> 
        internalGetPlayerBaseDataMsgFieldBuilder() {
      if (playerBaseDataMsgBuilder_ == null) {
        playerBaseDataMsgBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.PlayerBaseDataMsg, xddq.pb.PlayerBaseDataMsg.Builder, xddq.pb.PlayerBaseDataMsgOrBuilder>(
                getPlayerBaseDataMsg(),
                getParentForChildren(),
                isClean());
        playerBaseDataMsg_ = null;
      }
      return playerBaseDataMsgBuilder_;
    }

    private long score_ ;
    /**
     * <code>optional int64 score = 2;</code>
     * @return Whether the score field is set.
     */
    @java.lang.Override
    public boolean hasScore() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int64 score = 2;</code>
     * @return The score.
     */
    @java.lang.Override
    public long getScore() {
      return score_;
    }
    /**
     * <code>optional int64 score = 2;</code>
     * @param value The score to set.
     * @return This builder for chaining.
     */
    public Builder setScore(long value) {

      score_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 score = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearScore() {
      bitField0_ = (bitField0_ & ~0x00000002);
      score_ = 0L;
      onChanged();
      return this;
    }

    private long protectEndTime_ ;
    /**
     * <code>optional int64 protectEndTime = 3;</code>
     * @return Whether the protectEndTime field is set.
     */
    @java.lang.Override
    public boolean hasProtectEndTime() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int64 protectEndTime = 3;</code>
     * @return The protectEndTime.
     */
    @java.lang.Override
    public long getProtectEndTime() {
      return protectEndTime_;
    }
    /**
     * <code>optional int64 protectEndTime = 3;</code>
     * @param value The protectEndTime to set.
     * @return This builder for chaining.
     */
    public Builder setProtectEndTime(long value) {

      protectEndTime_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 protectEndTime = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearProtectEndTime() {
      bitField0_ = (bitField0_ & ~0x00000004);
      protectEndTime_ = 0L;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.PlayerBattleShowDataMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.PlayerBattleShowDataMsg)
  private static final xddq.pb.PlayerBattleShowDataMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.PlayerBattleShowDataMsg();
  }

  public static xddq.pb.PlayerBattleShowDataMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<PlayerBattleShowDataMsg>
      PARSER = new com.google.protobuf.AbstractParser<PlayerBattleShowDataMsg>() {
    @java.lang.Override
    public PlayerBattleShowDataMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<PlayerBattleShowDataMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<PlayerBattleShowDataMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.PlayerBattleShowDataMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

