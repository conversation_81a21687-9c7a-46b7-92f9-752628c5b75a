// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

public interface AssistantIngotHomeDepositResultOrBuilder extends
    // @@protoc_insertion_point(interface_extends:xddq.pb.AssistantIngotHomeDepositResult)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  boolean hasRet();
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  int getRet();

  /**
   * <code>optional int32 depositIndex = 2;</code>
   * @return Whether the depositIndex field is set.
   */
  boolean hasDepositIndex();
  /**
   * <code>optional int32 depositIndex = 2;</code>
   * @return The depositIndex.
   */
  int getDepositIndex();

  /**
   * <code>optional string baseIngot = 3;</code>
   * @return Whether the baseIngot field is set.
   */
  boolean hasBaseIngot();
  /**
   * <code>optional string baseIngot = 3;</code>
   * @return The baseIngot.
   */
  java.lang.String getBaseIngot();
  /**
   * <code>optional string baseIngot = 3;</code>
   * @return The bytes for baseIngot.
   */
  com.google.protobuf.ByteString
      getBaseIngotBytes();
}
