// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.GodDemonBattleMatchConfig}
 */
public final class GodDemonBattleMatchConfig extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.GodDemonBattleMatchConfig)
    GodDemonBattleMatchConfigOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      GodDemonBattleMatchConfig.class.getName());
  }
  // Use GodDemonBattleMatchConfig.newBuilder() to construct.
  private GodDemonBattleMatchConfig(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private GodDemonBattleMatchConfig() {
    name_ = "";
    minNum_ = "";
    groupNum_ = "";
    protectNum_ = "";
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_GodDemonBattleMatchConfig_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_GodDemonBattleMatchConfig_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.GodDemonBattleMatchConfig.class, xddq.pb.GodDemonBattleMatchConfig.Builder.class);
  }

  private int bitField0_;
  public static final int ACTIVITYID_FIELD_NUMBER = 1;
  private int activityId_ = 0;
  /**
   * <code>required int32 activityId = 1;</code>
   * @return Whether the activityId field is set.
   */
  @java.lang.Override
  public boolean hasActivityId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 activityId = 1;</code>
   * @return The activityId.
   */
  @java.lang.Override
  public int getActivityId() {
    return activityId_;
  }

  public static final int ID_FIELD_NUMBER = 2;
  private int id_ = 0;
  /**
   * <code>required int32 id = 2;</code>
   * @return Whether the id field is set.
   */
  @java.lang.Override
  public boolean hasId() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>required int32 id = 2;</code>
   * @return The id.
   */
  @java.lang.Override
  public int getId() {
    return id_;
  }

  public static final int NAME_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private volatile java.lang.Object name_ = "";
  /**
   * <code>required string name = 3;</code>
   * @return Whether the name field is set.
   */
  @java.lang.Override
  public boolean hasName() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>required string name = 3;</code>
   * @return The name.
   */
  @java.lang.Override
  public java.lang.String getName() {
    java.lang.Object ref = name_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        name_ = s;
      }
      return s;
    }
  }
  /**
   * <code>required string name = 3;</code>
   * @return The bytes for name.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getNameBytes() {
    java.lang.Object ref = name_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      name_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int MINLEVEL_FIELD_NUMBER = 4;
  private int minLevel_ = 0;
  /**
   * <code>required int32 minLevel = 4;</code>
   * @return Whether the minLevel field is set.
   */
  @java.lang.Override
  public boolean hasMinLevel() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>required int32 minLevel = 4;</code>
   * @return The minLevel.
   */
  @java.lang.Override
  public int getMinLevel() {
    return minLevel_;
  }

  public static final int MAXLEVEL_FIELD_NUMBER = 5;
  private int maxLevel_ = 0;
  /**
   * <code>required int32 maxLevel = 5;</code>
   * @return Whether the maxLevel field is set.
   */
  @java.lang.Override
  public boolean hasMaxLevel() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>required int32 maxLevel = 5;</code>
   * @return The maxLevel.
   */
  @java.lang.Override
  public int getMaxLevel() {
    return maxLevel_;
  }

  public static final int STANDARDLEVEL_FIELD_NUMBER = 6;
  private int standardLevel_ = 0;
  /**
   * <code>required int32 standardLevel = 6;</code>
   * @return Whether the standardLevel field is set.
   */
  @java.lang.Override
  public boolean hasStandardLevel() {
    return ((bitField0_ & 0x00000020) != 0);
  }
  /**
   * <code>required int32 standardLevel = 6;</code>
   * @return The standardLevel.
   */
  @java.lang.Override
  public int getStandardLevel() {
    return standardLevel_;
  }

  public static final int MINNUM_FIELD_NUMBER = 7;
  @SuppressWarnings("serial")
  private volatile java.lang.Object minNum_ = "";
  /**
   * <code>required string minNum = 7;</code>
   * @return Whether the minNum field is set.
   */
  @java.lang.Override
  public boolean hasMinNum() {
    return ((bitField0_ & 0x00000040) != 0);
  }
  /**
   * <code>required string minNum = 7;</code>
   * @return The minNum.
   */
  @java.lang.Override
  public java.lang.String getMinNum() {
    java.lang.Object ref = minNum_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        minNum_ = s;
      }
      return s;
    }
  }
  /**
   * <code>required string minNum = 7;</code>
   * @return The bytes for minNum.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getMinNumBytes() {
    java.lang.Object ref = minNum_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      minNum_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int GROUPNUM_FIELD_NUMBER = 8;
  @SuppressWarnings("serial")
  private volatile java.lang.Object groupNum_ = "";
  /**
   * <code>required string groupNum = 8;</code>
   * @return Whether the groupNum field is set.
   */
  @java.lang.Override
  public boolean hasGroupNum() {
    return ((bitField0_ & 0x00000080) != 0);
  }
  /**
   * <code>required string groupNum = 8;</code>
   * @return The groupNum.
   */
  @java.lang.Override
  public java.lang.String getGroupNum() {
    java.lang.Object ref = groupNum_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        groupNum_ = s;
      }
      return s;
    }
  }
  /**
   * <code>required string groupNum = 8;</code>
   * @return The bytes for groupNum.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getGroupNumBytes() {
    java.lang.Object ref = groupNum_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      groupNum_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PROTECTNUM_FIELD_NUMBER = 9;
  @SuppressWarnings("serial")
  private volatile java.lang.Object protectNum_ = "";
  /**
   * <code>required string protectNum = 9;</code>
   * @return Whether the protectNum field is set.
   */
  @java.lang.Override
  public boolean hasProtectNum() {
    return ((bitField0_ & 0x00000100) != 0);
  }
  /**
   * <code>required string protectNum = 9;</code>
   * @return The protectNum.
   */
  @java.lang.Override
  public java.lang.String getProtectNum() {
    java.lang.Object ref = protectNum_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        protectNum_ = s;
      }
      return s;
    }
  }
  /**
   * <code>required string protectNum = 9;</code>
   * @return The bytes for protectNum.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getProtectNumBytes() {
    java.lang.Object ref = protectNum_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      protectNum_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int TIMEOFFSET_FIELD_NUMBER = 10;
  private int timeOffset_ = 0;
  /**
   * <code>required int32 timeOffset = 10;</code>
   * @return Whether the timeOffset field is set.
   */
  @java.lang.Override
  public boolean hasTimeOffset() {
    return ((bitField0_ & 0x00000200) != 0);
  }
  /**
   * <code>required int32 timeOffset = 10;</code>
   * @return The timeOffset.
   */
  @java.lang.Override
  public int getTimeOffset() {
    return timeOffset_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasActivityId()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasId()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasName()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasMinLevel()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasMaxLevel()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasStandardLevel()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasMinNum()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasGroupNum()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasProtectNum()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasTimeOffset()) {
      memoizedIsInitialized = 0;
      return false;
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, activityId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(2, id_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 3, name_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt32(4, minLevel_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      output.writeInt32(5, maxLevel_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      output.writeInt32(6, standardLevel_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 7, minNum_);
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 8, groupNum_);
    }
    if (((bitField0_ & 0x00000100) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 9, protectNum_);
    }
    if (((bitField0_ & 0x00000200) != 0)) {
      output.writeInt32(10, timeOffset_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, activityId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, id_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(3, name_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(4, minLevel_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(5, maxLevel_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(6, standardLevel_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(7, minNum_);
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(8, groupNum_);
    }
    if (((bitField0_ & 0x00000100) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(9, protectNum_);
    }
    if (((bitField0_ & 0x00000200) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(10, timeOffset_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.GodDemonBattleMatchConfig)) {
      return super.equals(obj);
    }
    xddq.pb.GodDemonBattleMatchConfig other = (xddq.pb.GodDemonBattleMatchConfig) obj;

    if (hasActivityId() != other.hasActivityId()) return false;
    if (hasActivityId()) {
      if (getActivityId()
          != other.getActivityId()) return false;
    }
    if (hasId() != other.hasId()) return false;
    if (hasId()) {
      if (getId()
          != other.getId()) return false;
    }
    if (hasName() != other.hasName()) return false;
    if (hasName()) {
      if (!getName()
          .equals(other.getName())) return false;
    }
    if (hasMinLevel() != other.hasMinLevel()) return false;
    if (hasMinLevel()) {
      if (getMinLevel()
          != other.getMinLevel()) return false;
    }
    if (hasMaxLevel() != other.hasMaxLevel()) return false;
    if (hasMaxLevel()) {
      if (getMaxLevel()
          != other.getMaxLevel()) return false;
    }
    if (hasStandardLevel() != other.hasStandardLevel()) return false;
    if (hasStandardLevel()) {
      if (getStandardLevel()
          != other.getStandardLevel()) return false;
    }
    if (hasMinNum() != other.hasMinNum()) return false;
    if (hasMinNum()) {
      if (!getMinNum()
          .equals(other.getMinNum())) return false;
    }
    if (hasGroupNum() != other.hasGroupNum()) return false;
    if (hasGroupNum()) {
      if (!getGroupNum()
          .equals(other.getGroupNum())) return false;
    }
    if (hasProtectNum() != other.hasProtectNum()) return false;
    if (hasProtectNum()) {
      if (!getProtectNum()
          .equals(other.getProtectNum())) return false;
    }
    if (hasTimeOffset() != other.hasTimeOffset()) return false;
    if (hasTimeOffset()) {
      if (getTimeOffset()
          != other.getTimeOffset()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasActivityId()) {
      hash = (37 * hash) + ACTIVITYID_FIELD_NUMBER;
      hash = (53 * hash) + getActivityId();
    }
    if (hasId()) {
      hash = (37 * hash) + ID_FIELD_NUMBER;
      hash = (53 * hash) + getId();
    }
    if (hasName()) {
      hash = (37 * hash) + NAME_FIELD_NUMBER;
      hash = (53 * hash) + getName().hashCode();
    }
    if (hasMinLevel()) {
      hash = (37 * hash) + MINLEVEL_FIELD_NUMBER;
      hash = (53 * hash) + getMinLevel();
    }
    if (hasMaxLevel()) {
      hash = (37 * hash) + MAXLEVEL_FIELD_NUMBER;
      hash = (53 * hash) + getMaxLevel();
    }
    if (hasStandardLevel()) {
      hash = (37 * hash) + STANDARDLEVEL_FIELD_NUMBER;
      hash = (53 * hash) + getStandardLevel();
    }
    if (hasMinNum()) {
      hash = (37 * hash) + MINNUM_FIELD_NUMBER;
      hash = (53 * hash) + getMinNum().hashCode();
    }
    if (hasGroupNum()) {
      hash = (37 * hash) + GROUPNUM_FIELD_NUMBER;
      hash = (53 * hash) + getGroupNum().hashCode();
    }
    if (hasProtectNum()) {
      hash = (37 * hash) + PROTECTNUM_FIELD_NUMBER;
      hash = (53 * hash) + getProtectNum().hashCode();
    }
    if (hasTimeOffset()) {
      hash = (37 * hash) + TIMEOFFSET_FIELD_NUMBER;
      hash = (53 * hash) + getTimeOffset();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.GodDemonBattleMatchConfig parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GodDemonBattleMatchConfig parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GodDemonBattleMatchConfig parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GodDemonBattleMatchConfig parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GodDemonBattleMatchConfig parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GodDemonBattleMatchConfig parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GodDemonBattleMatchConfig parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.GodDemonBattleMatchConfig parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.GodDemonBattleMatchConfig parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.GodDemonBattleMatchConfig parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.GodDemonBattleMatchConfig parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.GodDemonBattleMatchConfig parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.GodDemonBattleMatchConfig prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.GodDemonBattleMatchConfig}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.GodDemonBattleMatchConfig)
      xddq.pb.GodDemonBattleMatchConfigOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GodDemonBattleMatchConfig_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GodDemonBattleMatchConfig_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.GodDemonBattleMatchConfig.class, xddq.pb.GodDemonBattleMatchConfig.Builder.class);
    }

    // Construct using xddq.pb.GodDemonBattleMatchConfig.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      activityId_ = 0;
      id_ = 0;
      name_ = "";
      minLevel_ = 0;
      maxLevel_ = 0;
      standardLevel_ = 0;
      minNum_ = "";
      groupNum_ = "";
      protectNum_ = "";
      timeOffset_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GodDemonBattleMatchConfig_descriptor;
    }

    @java.lang.Override
    public xddq.pb.GodDemonBattleMatchConfig getDefaultInstanceForType() {
      return xddq.pb.GodDemonBattleMatchConfig.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.GodDemonBattleMatchConfig build() {
      xddq.pb.GodDemonBattleMatchConfig result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.GodDemonBattleMatchConfig buildPartial() {
      xddq.pb.GodDemonBattleMatchConfig result = new xddq.pb.GodDemonBattleMatchConfig(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.GodDemonBattleMatchConfig result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.activityId_ = activityId_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.id_ = id_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.name_ = name_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.minLevel_ = minLevel_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.maxLevel_ = maxLevel_;
        to_bitField0_ |= 0x00000010;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.standardLevel_ = standardLevel_;
        to_bitField0_ |= 0x00000020;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.minNum_ = minNum_;
        to_bitField0_ |= 0x00000040;
      }
      if (((from_bitField0_ & 0x00000080) != 0)) {
        result.groupNum_ = groupNum_;
        to_bitField0_ |= 0x00000080;
      }
      if (((from_bitField0_ & 0x00000100) != 0)) {
        result.protectNum_ = protectNum_;
        to_bitField0_ |= 0x00000100;
      }
      if (((from_bitField0_ & 0x00000200) != 0)) {
        result.timeOffset_ = timeOffset_;
        to_bitField0_ |= 0x00000200;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.GodDemonBattleMatchConfig) {
        return mergeFrom((xddq.pb.GodDemonBattleMatchConfig)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.GodDemonBattleMatchConfig other) {
      if (other == xddq.pb.GodDemonBattleMatchConfig.getDefaultInstance()) return this;
      if (other.hasActivityId()) {
        setActivityId(other.getActivityId());
      }
      if (other.hasId()) {
        setId(other.getId());
      }
      if (other.hasName()) {
        name_ = other.name_;
        bitField0_ |= 0x00000004;
        onChanged();
      }
      if (other.hasMinLevel()) {
        setMinLevel(other.getMinLevel());
      }
      if (other.hasMaxLevel()) {
        setMaxLevel(other.getMaxLevel());
      }
      if (other.hasStandardLevel()) {
        setStandardLevel(other.getStandardLevel());
      }
      if (other.hasMinNum()) {
        minNum_ = other.minNum_;
        bitField0_ |= 0x00000040;
        onChanged();
      }
      if (other.hasGroupNum()) {
        groupNum_ = other.groupNum_;
        bitField0_ |= 0x00000080;
        onChanged();
      }
      if (other.hasProtectNum()) {
        protectNum_ = other.protectNum_;
        bitField0_ |= 0x00000100;
        onChanged();
      }
      if (other.hasTimeOffset()) {
        setTimeOffset(other.getTimeOffset());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasActivityId()) {
        return false;
      }
      if (!hasId()) {
        return false;
      }
      if (!hasName()) {
        return false;
      }
      if (!hasMinLevel()) {
        return false;
      }
      if (!hasMaxLevel()) {
        return false;
      }
      if (!hasStandardLevel()) {
        return false;
      }
      if (!hasMinNum()) {
        return false;
      }
      if (!hasGroupNum()) {
        return false;
      }
      if (!hasProtectNum()) {
        return false;
      }
      if (!hasTimeOffset()) {
        return false;
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              activityId_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              id_ = input.readInt32();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 26: {
              name_ = input.readBytes();
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 32: {
              minLevel_ = input.readInt32();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            case 40: {
              maxLevel_ = input.readInt32();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            case 48: {
              standardLevel_ = input.readInt32();
              bitField0_ |= 0x00000020;
              break;
            } // case 48
            case 58: {
              minNum_ = input.readBytes();
              bitField0_ |= 0x00000040;
              break;
            } // case 58
            case 66: {
              groupNum_ = input.readBytes();
              bitField0_ |= 0x00000080;
              break;
            } // case 66
            case 74: {
              protectNum_ = input.readBytes();
              bitField0_ |= 0x00000100;
              break;
            } // case 74
            case 80: {
              timeOffset_ = input.readInt32();
              bitField0_ |= 0x00000200;
              break;
            } // case 80
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int activityId_ ;
    /**
     * <code>required int32 activityId = 1;</code>
     * @return Whether the activityId field is set.
     */
    @java.lang.Override
    public boolean hasActivityId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 activityId = 1;</code>
     * @return The activityId.
     */
    @java.lang.Override
    public int getActivityId() {
      return activityId_;
    }
    /**
     * <code>required int32 activityId = 1;</code>
     * @param value The activityId to set.
     * @return This builder for chaining.
     */
    public Builder setActivityId(int value) {

      activityId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 activityId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearActivityId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      activityId_ = 0;
      onChanged();
      return this;
    }

    private int id_ ;
    /**
     * <code>required int32 id = 2;</code>
     * @return Whether the id field is set.
     */
    @java.lang.Override
    public boolean hasId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>required int32 id = 2;</code>
     * @return The id.
     */
    @java.lang.Override
    public int getId() {
      return id_;
    }
    /**
     * <code>required int32 id = 2;</code>
     * @param value The id to set.
     * @return This builder for chaining.
     */
    public Builder setId(int value) {

      id_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 id = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearId() {
      bitField0_ = (bitField0_ & ~0x00000002);
      id_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object name_ = "";
    /**
     * <code>required string name = 3;</code>
     * @return Whether the name field is set.
     */
    public boolean hasName() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>required string name = 3;</code>
     * @return The name.
     */
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          name_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>required string name = 3;</code>
     * @return The bytes for name.
     */
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>required string name = 3;</code>
     * @param value The name to set.
     * @return This builder for chaining.
     */
    public Builder setName(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      name_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>required string name = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearName() {
      name_ = getDefaultInstance().getName();
      bitField0_ = (bitField0_ & ~0x00000004);
      onChanged();
      return this;
    }
    /**
     * <code>required string name = 3;</code>
     * @param value The bytes for name to set.
     * @return This builder for chaining.
     */
    public Builder setNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      name_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }

    private int minLevel_ ;
    /**
     * <code>required int32 minLevel = 4;</code>
     * @return Whether the minLevel field is set.
     */
    @java.lang.Override
    public boolean hasMinLevel() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>required int32 minLevel = 4;</code>
     * @return The minLevel.
     */
    @java.lang.Override
    public int getMinLevel() {
      return minLevel_;
    }
    /**
     * <code>required int32 minLevel = 4;</code>
     * @param value The minLevel to set.
     * @return This builder for chaining.
     */
    public Builder setMinLevel(int value) {

      minLevel_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 minLevel = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearMinLevel() {
      bitField0_ = (bitField0_ & ~0x00000008);
      minLevel_ = 0;
      onChanged();
      return this;
    }

    private int maxLevel_ ;
    /**
     * <code>required int32 maxLevel = 5;</code>
     * @return Whether the maxLevel field is set.
     */
    @java.lang.Override
    public boolean hasMaxLevel() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>required int32 maxLevel = 5;</code>
     * @return The maxLevel.
     */
    @java.lang.Override
    public int getMaxLevel() {
      return maxLevel_;
    }
    /**
     * <code>required int32 maxLevel = 5;</code>
     * @param value The maxLevel to set.
     * @return This builder for chaining.
     */
    public Builder setMaxLevel(int value) {

      maxLevel_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 maxLevel = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearMaxLevel() {
      bitField0_ = (bitField0_ & ~0x00000010);
      maxLevel_ = 0;
      onChanged();
      return this;
    }

    private int standardLevel_ ;
    /**
     * <code>required int32 standardLevel = 6;</code>
     * @return Whether the standardLevel field is set.
     */
    @java.lang.Override
    public boolean hasStandardLevel() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>required int32 standardLevel = 6;</code>
     * @return The standardLevel.
     */
    @java.lang.Override
    public int getStandardLevel() {
      return standardLevel_;
    }
    /**
     * <code>required int32 standardLevel = 6;</code>
     * @param value The standardLevel to set.
     * @return This builder for chaining.
     */
    public Builder setStandardLevel(int value) {

      standardLevel_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 standardLevel = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearStandardLevel() {
      bitField0_ = (bitField0_ & ~0x00000020);
      standardLevel_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object minNum_ = "";
    /**
     * <code>required string minNum = 7;</code>
     * @return Whether the minNum field is set.
     */
    public boolean hasMinNum() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <code>required string minNum = 7;</code>
     * @return The minNum.
     */
    public java.lang.String getMinNum() {
      java.lang.Object ref = minNum_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          minNum_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>required string minNum = 7;</code>
     * @return The bytes for minNum.
     */
    public com.google.protobuf.ByteString
        getMinNumBytes() {
      java.lang.Object ref = minNum_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        minNum_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>required string minNum = 7;</code>
     * @param value The minNum to set.
     * @return This builder for chaining.
     */
    public Builder setMinNum(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      minNum_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>required string minNum = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearMinNum() {
      minNum_ = getDefaultInstance().getMinNum();
      bitField0_ = (bitField0_ & ~0x00000040);
      onChanged();
      return this;
    }
    /**
     * <code>required string minNum = 7;</code>
     * @param value The bytes for minNum to set.
     * @return This builder for chaining.
     */
    public Builder setMinNumBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      minNum_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }

    private java.lang.Object groupNum_ = "";
    /**
     * <code>required string groupNum = 8;</code>
     * @return Whether the groupNum field is set.
     */
    public boolean hasGroupNum() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <code>required string groupNum = 8;</code>
     * @return The groupNum.
     */
    public java.lang.String getGroupNum() {
      java.lang.Object ref = groupNum_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          groupNum_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>required string groupNum = 8;</code>
     * @return The bytes for groupNum.
     */
    public com.google.protobuf.ByteString
        getGroupNumBytes() {
      java.lang.Object ref = groupNum_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        groupNum_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>required string groupNum = 8;</code>
     * @param value The groupNum to set.
     * @return This builder for chaining.
     */
    public Builder setGroupNum(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      groupNum_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }
    /**
     * <code>required string groupNum = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearGroupNum() {
      groupNum_ = getDefaultInstance().getGroupNum();
      bitField0_ = (bitField0_ & ~0x00000080);
      onChanged();
      return this;
    }
    /**
     * <code>required string groupNum = 8;</code>
     * @param value The bytes for groupNum to set.
     * @return This builder for chaining.
     */
    public Builder setGroupNumBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      groupNum_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }

    private java.lang.Object protectNum_ = "";
    /**
     * <code>required string protectNum = 9;</code>
     * @return Whether the protectNum field is set.
     */
    public boolean hasProtectNum() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <code>required string protectNum = 9;</code>
     * @return The protectNum.
     */
    public java.lang.String getProtectNum() {
      java.lang.Object ref = protectNum_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          protectNum_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>required string protectNum = 9;</code>
     * @return The bytes for protectNum.
     */
    public com.google.protobuf.ByteString
        getProtectNumBytes() {
      java.lang.Object ref = protectNum_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        protectNum_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>required string protectNum = 9;</code>
     * @param value The protectNum to set.
     * @return This builder for chaining.
     */
    public Builder setProtectNum(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      protectNum_ = value;
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }
    /**
     * <code>required string protectNum = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearProtectNum() {
      protectNum_ = getDefaultInstance().getProtectNum();
      bitField0_ = (bitField0_ & ~0x00000100);
      onChanged();
      return this;
    }
    /**
     * <code>required string protectNum = 9;</code>
     * @param value The bytes for protectNum to set.
     * @return This builder for chaining.
     */
    public Builder setProtectNumBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      protectNum_ = value;
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }

    private int timeOffset_ ;
    /**
     * <code>required int32 timeOffset = 10;</code>
     * @return Whether the timeOffset field is set.
     */
    @java.lang.Override
    public boolean hasTimeOffset() {
      return ((bitField0_ & 0x00000200) != 0);
    }
    /**
     * <code>required int32 timeOffset = 10;</code>
     * @return The timeOffset.
     */
    @java.lang.Override
    public int getTimeOffset() {
      return timeOffset_;
    }
    /**
     * <code>required int32 timeOffset = 10;</code>
     * @param value The timeOffset to set.
     * @return This builder for chaining.
     */
    public Builder setTimeOffset(int value) {

      timeOffset_ = value;
      bitField0_ |= 0x00000200;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 timeOffset = 10;</code>
     * @return This builder for chaining.
     */
    public Builder clearTimeOffset() {
      bitField0_ = (bitField0_ & ~0x00000200);
      timeOffset_ = 0;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.GodDemonBattleMatchConfig)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.GodDemonBattleMatchConfig)
  private static final xddq.pb.GodDemonBattleMatchConfig DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.GodDemonBattleMatchConfig();
  }

  public static xddq.pb.GodDemonBattleMatchConfig getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<GodDemonBattleMatchConfig>
      PARSER = new com.google.protobuf.AbstractParser<GodDemonBattleMatchConfig>() {
    @java.lang.Override
    public GodDemonBattleMatchConfig parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<GodDemonBattleMatchConfig> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<GodDemonBattleMatchConfig> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.GodDemonBattleMatchConfig getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

