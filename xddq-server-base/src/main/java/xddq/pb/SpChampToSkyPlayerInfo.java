// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.SpChampToSkyPlayerInfo}
 */
public final class SpChampToSkyPlayerInfo extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.SpChampToSkyPlayerInfo)
    SpChampToSkyPlayerInfoOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      SpChampToSkyPlayerInfo.class.getName());
  }
  // Use SpChampToSkyPlayerInfo.newBuilder() to construct.
  private SpChampToSkyPlayerInfo(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private SpChampToSkyPlayerInfo() {
    fightValue_ = "";
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_SpChampToSkyPlayerInfo_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_SpChampToSkyPlayerInfo_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.SpChampToSkyPlayerInfo.class, xddq.pb.SpChampToSkyPlayerInfo.Builder.class);
  }

  private int bitField0_;
  public static final int HEADINFO_FIELD_NUMBER = 1;
  private xddq.pb.PlayerBaseDataMsg headInfo_;
  /**
   * <code>optional .xddq.pb.PlayerBaseDataMsg headInfo = 1;</code>
   * @return Whether the headInfo field is set.
   */
  @java.lang.Override
  public boolean hasHeadInfo() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional .xddq.pb.PlayerBaseDataMsg headInfo = 1;</code>
   * @return The headInfo.
   */
  @java.lang.Override
  public xddq.pb.PlayerBaseDataMsg getHeadInfo() {
    return headInfo_ == null ? xddq.pb.PlayerBaseDataMsg.getDefaultInstance() : headInfo_;
  }
  /**
   * <code>optional .xddq.pb.PlayerBaseDataMsg headInfo = 1;</code>
   */
  @java.lang.Override
  public xddq.pb.PlayerBaseDataMsgOrBuilder getHeadInfoOrBuilder() {
    return headInfo_ == null ? xddq.pb.PlayerBaseDataMsg.getDefaultInstance() : headInfo_;
  }

  public static final int HEATVALUE_FIELD_NUMBER = 2;
  private long heatValue_ = 0L;
  /**
   * <code>optional int64 heatValue = 2;</code>
   * @return Whether the heatValue field is set.
   */
  @java.lang.Override
  public boolean hasHeatValue() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int64 heatValue = 2;</code>
   * @return The heatValue.
   */
  @java.lang.Override
  public long getHeatValue() {
    return heatValue_;
  }

  public static final int FIGHTVALUE_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private volatile java.lang.Object fightValue_ = "";
  /**
   * <code>optional string fightValue = 3;</code>
   * @return Whether the fightValue field is set.
   */
  @java.lang.Override
  public boolean hasFightValue() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional string fightValue = 3;</code>
   * @return The fightValue.
   */
  @java.lang.Override
  public java.lang.String getFightValue() {
    java.lang.Object ref = fightValue_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        fightValue_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string fightValue = 3;</code>
   * @return The bytes for fightValue.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getFightValueBytes() {
    java.lang.Object ref = fightValue_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      fightValue_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeMessage(1, getHeadInfo());
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt64(2, heatValue_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 3, fightValue_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, getHeadInfo());
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(2, heatValue_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(3, fightValue_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.SpChampToSkyPlayerInfo)) {
      return super.equals(obj);
    }
    xddq.pb.SpChampToSkyPlayerInfo other = (xddq.pb.SpChampToSkyPlayerInfo) obj;

    if (hasHeadInfo() != other.hasHeadInfo()) return false;
    if (hasHeadInfo()) {
      if (!getHeadInfo()
          .equals(other.getHeadInfo())) return false;
    }
    if (hasHeatValue() != other.hasHeatValue()) return false;
    if (hasHeatValue()) {
      if (getHeatValue()
          != other.getHeatValue()) return false;
    }
    if (hasFightValue() != other.hasFightValue()) return false;
    if (hasFightValue()) {
      if (!getFightValue()
          .equals(other.getFightValue())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasHeadInfo()) {
      hash = (37 * hash) + HEADINFO_FIELD_NUMBER;
      hash = (53 * hash) + getHeadInfo().hashCode();
    }
    if (hasHeatValue()) {
      hash = (37 * hash) + HEATVALUE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getHeatValue());
    }
    if (hasFightValue()) {
      hash = (37 * hash) + FIGHTVALUE_FIELD_NUMBER;
      hash = (53 * hash) + getFightValue().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.SpChampToSkyPlayerInfo parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.SpChampToSkyPlayerInfo parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.SpChampToSkyPlayerInfo parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.SpChampToSkyPlayerInfo parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.SpChampToSkyPlayerInfo parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.SpChampToSkyPlayerInfo parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.SpChampToSkyPlayerInfo parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.SpChampToSkyPlayerInfo parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.SpChampToSkyPlayerInfo parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.SpChampToSkyPlayerInfo parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.SpChampToSkyPlayerInfo parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.SpChampToSkyPlayerInfo parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.SpChampToSkyPlayerInfo prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.SpChampToSkyPlayerInfo}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.SpChampToSkyPlayerInfo)
      xddq.pb.SpChampToSkyPlayerInfoOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_SpChampToSkyPlayerInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_SpChampToSkyPlayerInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.SpChampToSkyPlayerInfo.class, xddq.pb.SpChampToSkyPlayerInfo.Builder.class);
    }

    // Construct using xddq.pb.SpChampToSkyPlayerInfo.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetHeadInfoFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      headInfo_ = null;
      if (headInfoBuilder_ != null) {
        headInfoBuilder_.dispose();
        headInfoBuilder_ = null;
      }
      heatValue_ = 0L;
      fightValue_ = "";
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_SpChampToSkyPlayerInfo_descriptor;
    }

    @java.lang.Override
    public xddq.pb.SpChampToSkyPlayerInfo getDefaultInstanceForType() {
      return xddq.pb.SpChampToSkyPlayerInfo.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.SpChampToSkyPlayerInfo build() {
      xddq.pb.SpChampToSkyPlayerInfo result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.SpChampToSkyPlayerInfo buildPartial() {
      xddq.pb.SpChampToSkyPlayerInfo result = new xddq.pb.SpChampToSkyPlayerInfo(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.SpChampToSkyPlayerInfo result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.headInfo_ = headInfoBuilder_ == null
            ? headInfo_
            : headInfoBuilder_.build();
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.heatValue_ = heatValue_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.fightValue_ = fightValue_;
        to_bitField0_ |= 0x00000004;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.SpChampToSkyPlayerInfo) {
        return mergeFrom((xddq.pb.SpChampToSkyPlayerInfo)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.SpChampToSkyPlayerInfo other) {
      if (other == xddq.pb.SpChampToSkyPlayerInfo.getDefaultInstance()) return this;
      if (other.hasHeadInfo()) {
        mergeHeadInfo(other.getHeadInfo());
      }
      if (other.hasHeatValue()) {
        setHeatValue(other.getHeatValue());
      }
      if (other.hasFightValue()) {
        fightValue_ = other.fightValue_;
        bitField0_ |= 0x00000004;
        onChanged();
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              input.readMessage(
                  internalGetHeadInfoFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 16: {
              heatValue_ = input.readInt64();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 26: {
              fightValue_ = input.readBytes();
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private xddq.pb.PlayerBaseDataMsg headInfo_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PlayerBaseDataMsg, xddq.pb.PlayerBaseDataMsg.Builder, xddq.pb.PlayerBaseDataMsgOrBuilder> headInfoBuilder_;
    /**
     * <code>optional .xddq.pb.PlayerBaseDataMsg headInfo = 1;</code>
     * @return Whether the headInfo field is set.
     */
    public boolean hasHeadInfo() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .xddq.pb.PlayerBaseDataMsg headInfo = 1;</code>
     * @return The headInfo.
     */
    public xddq.pb.PlayerBaseDataMsg getHeadInfo() {
      if (headInfoBuilder_ == null) {
        return headInfo_ == null ? xddq.pb.PlayerBaseDataMsg.getDefaultInstance() : headInfo_;
      } else {
        return headInfoBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.PlayerBaseDataMsg headInfo = 1;</code>
     */
    public Builder setHeadInfo(xddq.pb.PlayerBaseDataMsg value) {
      if (headInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        headInfo_ = value;
      } else {
        headInfoBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerBaseDataMsg headInfo = 1;</code>
     */
    public Builder setHeadInfo(
        xddq.pb.PlayerBaseDataMsg.Builder builderForValue) {
      if (headInfoBuilder_ == null) {
        headInfo_ = builderForValue.build();
      } else {
        headInfoBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerBaseDataMsg headInfo = 1;</code>
     */
    public Builder mergeHeadInfo(xddq.pb.PlayerBaseDataMsg value) {
      if (headInfoBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0) &&
          headInfo_ != null &&
          headInfo_ != xddq.pb.PlayerBaseDataMsg.getDefaultInstance()) {
          getHeadInfoBuilder().mergeFrom(value);
        } else {
          headInfo_ = value;
        }
      } else {
        headInfoBuilder_.mergeFrom(value);
      }
      if (headInfo_ != null) {
        bitField0_ |= 0x00000001;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerBaseDataMsg headInfo = 1;</code>
     */
    public Builder clearHeadInfo() {
      bitField0_ = (bitField0_ & ~0x00000001);
      headInfo_ = null;
      if (headInfoBuilder_ != null) {
        headInfoBuilder_.dispose();
        headInfoBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerBaseDataMsg headInfo = 1;</code>
     */
    public xddq.pb.PlayerBaseDataMsg.Builder getHeadInfoBuilder() {
      bitField0_ |= 0x00000001;
      onChanged();
      return internalGetHeadInfoFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.PlayerBaseDataMsg headInfo = 1;</code>
     */
    public xddq.pb.PlayerBaseDataMsgOrBuilder getHeadInfoOrBuilder() {
      if (headInfoBuilder_ != null) {
        return headInfoBuilder_.getMessageOrBuilder();
      } else {
        return headInfo_ == null ?
            xddq.pb.PlayerBaseDataMsg.getDefaultInstance() : headInfo_;
      }
    }
    /**
     * <code>optional .xddq.pb.PlayerBaseDataMsg headInfo = 1;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PlayerBaseDataMsg, xddq.pb.PlayerBaseDataMsg.Builder, xddq.pb.PlayerBaseDataMsgOrBuilder> 
        internalGetHeadInfoFieldBuilder() {
      if (headInfoBuilder_ == null) {
        headInfoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.PlayerBaseDataMsg, xddq.pb.PlayerBaseDataMsg.Builder, xddq.pb.PlayerBaseDataMsgOrBuilder>(
                getHeadInfo(),
                getParentForChildren(),
                isClean());
        headInfo_ = null;
      }
      return headInfoBuilder_;
    }

    private long heatValue_ ;
    /**
     * <code>optional int64 heatValue = 2;</code>
     * @return Whether the heatValue field is set.
     */
    @java.lang.Override
    public boolean hasHeatValue() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int64 heatValue = 2;</code>
     * @return The heatValue.
     */
    @java.lang.Override
    public long getHeatValue() {
      return heatValue_;
    }
    /**
     * <code>optional int64 heatValue = 2;</code>
     * @param value The heatValue to set.
     * @return This builder for chaining.
     */
    public Builder setHeatValue(long value) {

      heatValue_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 heatValue = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearHeatValue() {
      bitField0_ = (bitField0_ & ~0x00000002);
      heatValue_ = 0L;
      onChanged();
      return this;
    }

    private java.lang.Object fightValue_ = "";
    /**
     * <code>optional string fightValue = 3;</code>
     * @return Whether the fightValue field is set.
     */
    public boolean hasFightValue() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional string fightValue = 3;</code>
     * @return The fightValue.
     */
    public java.lang.String getFightValue() {
      java.lang.Object ref = fightValue_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          fightValue_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string fightValue = 3;</code>
     * @return The bytes for fightValue.
     */
    public com.google.protobuf.ByteString
        getFightValueBytes() {
      java.lang.Object ref = fightValue_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        fightValue_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string fightValue = 3;</code>
     * @param value The fightValue to set.
     * @return This builder for chaining.
     */
    public Builder setFightValue(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      fightValue_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional string fightValue = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearFightValue() {
      fightValue_ = getDefaultInstance().getFightValue();
      bitField0_ = (bitField0_ & ~0x00000004);
      onChanged();
      return this;
    }
    /**
     * <code>optional string fightValue = 3;</code>
     * @param value The bytes for fightValue to set.
     * @return This builder for chaining.
     */
    public Builder setFightValueBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      fightValue_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.SpChampToSkyPlayerInfo)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.SpChampToSkyPlayerInfo)
  private static final xddq.pb.SpChampToSkyPlayerInfo DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.SpChampToSkyPlayerInfo();
  }

  public static xddq.pb.SpChampToSkyPlayerInfo getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<SpChampToSkyPlayerInfo>
      PARSER = new com.google.protobuf.AbstractParser<SpChampToSkyPlayerInfo>() {
    @java.lang.Override
    public SpChampToSkyPlayerInfo parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<SpChampToSkyPlayerInfo> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<SpChampToSkyPlayerInfo> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.SpChampToSkyPlayerInfo getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

