// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.GetFairyPoolApplyListResp}
 */
public final class GetFairyPoolApplyListResp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.GetFairyPoolApplyListResp)
    GetFairyPoolApplyListRespOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      GetFairyPoolApplyListResp.class.getName());
  }
  // Use GetFairyPoolApplyListResp.newBuilder() to construct.
  private GetFairyPoolApplyListResp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private GetFairyPoolApplyListResp() {
    dataList_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_GetFairyPoolApplyListResp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_GetFairyPoolApplyListResp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.GetFairyPoolApplyListResp.class, xddq.pb.GetFairyPoolApplyListResp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int DATALIST_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.FairyPoolApplyData> dataList_;
  /**
   * <code>repeated .xddq.pb.FairyPoolApplyData dataList = 2;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.FairyPoolApplyData> getDataListList() {
    return dataList_;
  }
  /**
   * <code>repeated .xddq.pb.FairyPoolApplyData dataList = 2;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.FairyPoolApplyDataOrBuilder> 
      getDataListOrBuilderList() {
    return dataList_;
  }
  /**
   * <code>repeated .xddq.pb.FairyPoolApplyData dataList = 2;</code>
   */
  @java.lang.Override
  public int getDataListCount() {
    return dataList_.size();
  }
  /**
   * <code>repeated .xddq.pb.FairyPoolApplyData dataList = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.FairyPoolApplyData getDataList(int index) {
    return dataList_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.FairyPoolApplyData dataList = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.FairyPoolApplyDataOrBuilder getDataListOrBuilder(
      int index) {
    return dataList_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    for (int i = 0; i < dataList_.size(); i++) {
      output.writeMessage(2, dataList_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    for (int i = 0; i < dataList_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, dataList_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.GetFairyPoolApplyListResp)) {
      return super.equals(obj);
    }
    xddq.pb.GetFairyPoolApplyListResp other = (xddq.pb.GetFairyPoolApplyListResp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (!getDataListList()
        .equals(other.getDataListList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (getDataListCount() > 0) {
      hash = (37 * hash) + DATALIST_FIELD_NUMBER;
      hash = (53 * hash) + getDataListList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.GetFairyPoolApplyListResp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GetFairyPoolApplyListResp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GetFairyPoolApplyListResp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GetFairyPoolApplyListResp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GetFairyPoolApplyListResp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GetFairyPoolApplyListResp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GetFairyPoolApplyListResp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.GetFairyPoolApplyListResp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.GetFairyPoolApplyListResp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.GetFairyPoolApplyListResp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.GetFairyPoolApplyListResp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.GetFairyPoolApplyListResp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.GetFairyPoolApplyListResp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.GetFairyPoolApplyListResp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.GetFairyPoolApplyListResp)
      xddq.pb.GetFairyPoolApplyListRespOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GetFairyPoolApplyListResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GetFairyPoolApplyListResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.GetFairyPoolApplyListResp.class, xddq.pb.GetFairyPoolApplyListResp.Builder.class);
    }

    // Construct using xddq.pb.GetFairyPoolApplyListResp.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      if (dataListBuilder_ == null) {
        dataList_ = java.util.Collections.emptyList();
      } else {
        dataList_ = null;
        dataListBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000002);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GetFairyPoolApplyListResp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.GetFairyPoolApplyListResp getDefaultInstanceForType() {
      return xddq.pb.GetFairyPoolApplyListResp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.GetFairyPoolApplyListResp build() {
      xddq.pb.GetFairyPoolApplyListResp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.GetFairyPoolApplyListResp buildPartial() {
      xddq.pb.GetFairyPoolApplyListResp result = new xddq.pb.GetFairyPoolApplyListResp(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.GetFairyPoolApplyListResp result) {
      if (dataListBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          dataList_ = java.util.Collections.unmodifiableList(dataList_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.dataList_ = dataList_;
      } else {
        result.dataList_ = dataListBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.GetFairyPoolApplyListResp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.GetFairyPoolApplyListResp) {
        return mergeFrom((xddq.pb.GetFairyPoolApplyListResp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.GetFairyPoolApplyListResp other) {
      if (other == xddq.pb.GetFairyPoolApplyListResp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (dataListBuilder_ == null) {
        if (!other.dataList_.isEmpty()) {
          if (dataList_.isEmpty()) {
            dataList_ = other.dataList_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureDataListIsMutable();
            dataList_.addAll(other.dataList_);
          }
          onChanged();
        }
      } else {
        if (!other.dataList_.isEmpty()) {
          if (dataListBuilder_.isEmpty()) {
            dataListBuilder_.dispose();
            dataListBuilder_ = null;
            dataList_ = other.dataList_;
            bitField0_ = (bitField0_ & ~0x00000002);
            dataListBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetDataListFieldBuilder() : null;
          } else {
            dataListBuilder_.addAllMessages(other.dataList_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              xddq.pb.FairyPoolApplyData m =
                  input.readMessage(
                      xddq.pb.FairyPoolApplyData.parser(),
                      extensionRegistry);
              if (dataListBuilder_ == null) {
                ensureDataListIsMutable();
                dataList_.add(m);
              } else {
                dataListBuilder_.addMessage(m);
              }
              break;
            } // case 18
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.FairyPoolApplyData> dataList_ =
      java.util.Collections.emptyList();
    private void ensureDataListIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        dataList_ = new java.util.ArrayList<xddq.pb.FairyPoolApplyData>(dataList_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.FairyPoolApplyData, xddq.pb.FairyPoolApplyData.Builder, xddq.pb.FairyPoolApplyDataOrBuilder> dataListBuilder_;

    /**
     * <code>repeated .xddq.pb.FairyPoolApplyData dataList = 2;</code>
     */
    public java.util.List<xddq.pb.FairyPoolApplyData> getDataListList() {
      if (dataListBuilder_ == null) {
        return java.util.Collections.unmodifiableList(dataList_);
      } else {
        return dataListBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.FairyPoolApplyData dataList = 2;</code>
     */
    public int getDataListCount() {
      if (dataListBuilder_ == null) {
        return dataList_.size();
      } else {
        return dataListBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.FairyPoolApplyData dataList = 2;</code>
     */
    public xddq.pb.FairyPoolApplyData getDataList(int index) {
      if (dataListBuilder_ == null) {
        return dataList_.get(index);
      } else {
        return dataListBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.FairyPoolApplyData dataList = 2;</code>
     */
    public Builder setDataList(
        int index, xddq.pb.FairyPoolApplyData value) {
      if (dataListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDataListIsMutable();
        dataList_.set(index, value);
        onChanged();
      } else {
        dataListBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.FairyPoolApplyData dataList = 2;</code>
     */
    public Builder setDataList(
        int index, xddq.pb.FairyPoolApplyData.Builder builderForValue) {
      if (dataListBuilder_ == null) {
        ensureDataListIsMutable();
        dataList_.set(index, builderForValue.build());
        onChanged();
      } else {
        dataListBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.FairyPoolApplyData dataList = 2;</code>
     */
    public Builder addDataList(xddq.pb.FairyPoolApplyData value) {
      if (dataListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDataListIsMutable();
        dataList_.add(value);
        onChanged();
      } else {
        dataListBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.FairyPoolApplyData dataList = 2;</code>
     */
    public Builder addDataList(
        int index, xddq.pb.FairyPoolApplyData value) {
      if (dataListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDataListIsMutable();
        dataList_.add(index, value);
        onChanged();
      } else {
        dataListBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.FairyPoolApplyData dataList = 2;</code>
     */
    public Builder addDataList(
        xddq.pb.FairyPoolApplyData.Builder builderForValue) {
      if (dataListBuilder_ == null) {
        ensureDataListIsMutable();
        dataList_.add(builderForValue.build());
        onChanged();
      } else {
        dataListBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.FairyPoolApplyData dataList = 2;</code>
     */
    public Builder addDataList(
        int index, xddq.pb.FairyPoolApplyData.Builder builderForValue) {
      if (dataListBuilder_ == null) {
        ensureDataListIsMutable();
        dataList_.add(index, builderForValue.build());
        onChanged();
      } else {
        dataListBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.FairyPoolApplyData dataList = 2;</code>
     */
    public Builder addAllDataList(
        java.lang.Iterable<? extends xddq.pb.FairyPoolApplyData> values) {
      if (dataListBuilder_ == null) {
        ensureDataListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, dataList_);
        onChanged();
      } else {
        dataListBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.FairyPoolApplyData dataList = 2;</code>
     */
    public Builder clearDataList() {
      if (dataListBuilder_ == null) {
        dataList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        dataListBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.FairyPoolApplyData dataList = 2;</code>
     */
    public Builder removeDataList(int index) {
      if (dataListBuilder_ == null) {
        ensureDataListIsMutable();
        dataList_.remove(index);
        onChanged();
      } else {
        dataListBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.FairyPoolApplyData dataList = 2;</code>
     */
    public xddq.pb.FairyPoolApplyData.Builder getDataListBuilder(
        int index) {
      return internalGetDataListFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.FairyPoolApplyData dataList = 2;</code>
     */
    public xddq.pb.FairyPoolApplyDataOrBuilder getDataListOrBuilder(
        int index) {
      if (dataListBuilder_ == null) {
        return dataList_.get(index);  } else {
        return dataListBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.FairyPoolApplyData dataList = 2;</code>
     */
    public java.util.List<? extends xddq.pb.FairyPoolApplyDataOrBuilder> 
         getDataListOrBuilderList() {
      if (dataListBuilder_ != null) {
        return dataListBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(dataList_);
      }
    }
    /**
     * <code>repeated .xddq.pb.FairyPoolApplyData dataList = 2;</code>
     */
    public xddq.pb.FairyPoolApplyData.Builder addDataListBuilder() {
      return internalGetDataListFieldBuilder().addBuilder(
          xddq.pb.FairyPoolApplyData.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.FairyPoolApplyData dataList = 2;</code>
     */
    public xddq.pb.FairyPoolApplyData.Builder addDataListBuilder(
        int index) {
      return internalGetDataListFieldBuilder().addBuilder(
          index, xddq.pb.FairyPoolApplyData.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.FairyPoolApplyData dataList = 2;</code>
     */
    public java.util.List<xddq.pb.FairyPoolApplyData.Builder> 
         getDataListBuilderList() {
      return internalGetDataListFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.FairyPoolApplyData, xddq.pb.FairyPoolApplyData.Builder, xddq.pb.FairyPoolApplyDataOrBuilder> 
        internalGetDataListFieldBuilder() {
      if (dataListBuilder_ == null) {
        dataListBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.FairyPoolApplyData, xddq.pb.FairyPoolApplyData.Builder, xddq.pb.FairyPoolApplyDataOrBuilder>(
                dataList_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        dataList_ = null;
      }
      return dataListBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.GetFairyPoolApplyListResp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.GetFairyPoolApplyListResp)
  private static final xddq.pb.GetFairyPoolApplyListResp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.GetFairyPoolApplyListResp();
  }

  public static xddq.pb.GetFairyPoolApplyListResp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<GetFairyPoolApplyListResp>
      PARSER = new com.google.protobuf.AbstractParser<GetFairyPoolApplyListResp>() {
    @java.lang.Override
    public GetFairyPoolApplyListResp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<GetFairyPoolApplyListResp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<GetFairyPoolApplyListResp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.GetFairyPoolApplyListResp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

