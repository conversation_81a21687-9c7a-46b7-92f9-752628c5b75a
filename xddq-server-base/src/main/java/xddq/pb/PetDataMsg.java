// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.PetDataMsg}
 */
public final class PetDataMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.PetDataMsg)
    PetDataMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      PetDataMsg.class.getName());
  }
  // Use PetDataMsg.newBuilder() to construct.
  private PetDataMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private PetDataMsg() {
    petPassiveSkill_ = java.util.Collections.emptyList();
    petSoulShapePassiveSkill_ = java.util.Collections.emptyList();
    waitSelectSkill_ = emptyIntList();
    equipKernelId_ = emptyIntList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_PetDataMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_PetDataMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.PetDataMsg.class, xddq.pb.PetDataMsg.Builder.class);
  }

  private int bitField0_;
  public static final int ID_FIELD_NUMBER = 1;
  private int id_ = 0;
  /**
   * <code>required int32 id = 1;</code>
   * @return Whether the id field is set.
   */
  @java.lang.Override
  public boolean hasId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 id = 1;</code>
   * @return The id.
   */
  @java.lang.Override
  public int getId() {
    return id_;
  }

  public static final int CONFIGID_FIELD_NUMBER = 2;
  private int configId_ = 0;
  /**
   * <code>required int32 configId = 2;</code>
   * @return Whether the configId field is set.
   */
  @java.lang.Override
  public boolean hasConfigId() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>required int32 configId = 2;</code>
   * @return The configId.
   */
  @java.lang.Override
  public int getConfigId() {
    return configId_;
  }

  public static final int LV_FIELD_NUMBER = 3;
  private int lv_ = 0;
  /**
   * <code>required int32 lv = 3;</code>
   * @return Whether the lv field is set.
   */
  @java.lang.Override
  public boolean hasLv() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>required int32 lv = 3;</code>
   * @return The lv.
   */
  @java.lang.Override
  public int getLv() {
    return lv_;
  }

  public static final int STAR_FIELD_NUMBER = 4;
  private int star_ = 0;
  /**
   * <code>required int32 star = 4;</code>
   * @return Whether the star field is set.
   */
  @java.lang.Override
  public boolean hasStar() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>required int32 star = 4;</code>
   * @return The star.
   */
  @java.lang.Override
  public int getStar() {
    return star_;
  }

  public static final int PETPASSIVESKILL_FIELD_NUMBER = 5;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.PetPassiveSkillMsg> petPassiveSkill_;
  /**
   * <code>repeated .xddq.pb.PetPassiveSkillMsg petPassiveSkill = 5;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.PetPassiveSkillMsg> getPetPassiveSkillList() {
    return petPassiveSkill_;
  }
  /**
   * <code>repeated .xddq.pb.PetPassiveSkillMsg petPassiveSkill = 5;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.PetPassiveSkillMsgOrBuilder> 
      getPetPassiveSkillOrBuilderList() {
    return petPassiveSkill_;
  }
  /**
   * <code>repeated .xddq.pb.PetPassiveSkillMsg petPassiveSkill = 5;</code>
   */
  @java.lang.Override
  public int getPetPassiveSkillCount() {
    return petPassiveSkill_.size();
  }
  /**
   * <code>repeated .xddq.pb.PetPassiveSkillMsg petPassiveSkill = 5;</code>
   */
  @java.lang.Override
  public xddq.pb.PetPassiveSkillMsg getPetPassiveSkill(int index) {
    return petPassiveSkill_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.PetPassiveSkillMsg petPassiveSkill = 5;</code>
   */
  @java.lang.Override
  public xddq.pb.PetPassiveSkillMsgOrBuilder getPetPassiveSkillOrBuilder(
      int index) {
    return petPassiveSkill_.get(index);
  }

  public static final int ASSISTANTPETID_FIELD_NUMBER = 6;
  private int assistantPetId_ = 0;
  /**
   * <code>optional int32 assistantPetId = 6;</code>
   * @return Whether the assistantPetId field is set.
   */
  @java.lang.Override
  public boolean hasAssistantPetId() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>optional int32 assistantPetId = 6;</code>
   * @return The assistantPetId.
   */
  @java.lang.Override
  public int getAssistantPetId() {
    return assistantPetId_;
  }

  public static final int ASSISTANTCONFIGID_FIELD_NUMBER = 7;
  private int assistantConfigId_ = 0;
  /**
   * <code>optional int32 assistantConfigId = 7;</code>
   * @return Whether the assistantConfigId field is set.
   */
  @java.lang.Override
  public boolean hasAssistantConfigId() {
    return ((bitField0_ & 0x00000020) != 0);
  }
  /**
   * <code>optional int32 assistantConfigId = 7;</code>
   * @return The assistantConfigId.
   */
  @java.lang.Override
  public int getAssistantConfigId() {
    return assistantConfigId_;
  }

  public static final int PETSOULSHAPEPASSIVESKILL_FIELD_NUMBER = 8;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.PetPassiveSkillMsg> petSoulShapePassiveSkill_;
  /**
   * <code>repeated .xddq.pb.PetPassiveSkillMsg petSoulShapePassiveSkill = 8;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.PetPassiveSkillMsg> getPetSoulShapePassiveSkillList() {
    return petSoulShapePassiveSkill_;
  }
  /**
   * <code>repeated .xddq.pb.PetPassiveSkillMsg petSoulShapePassiveSkill = 8;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.PetPassiveSkillMsgOrBuilder> 
      getPetSoulShapePassiveSkillOrBuilderList() {
    return petSoulShapePassiveSkill_;
  }
  /**
   * <code>repeated .xddq.pb.PetPassiveSkillMsg petSoulShapePassiveSkill = 8;</code>
   */
  @java.lang.Override
  public int getPetSoulShapePassiveSkillCount() {
    return petSoulShapePassiveSkill_.size();
  }
  /**
   * <code>repeated .xddq.pb.PetPassiveSkillMsg petSoulShapePassiveSkill = 8;</code>
   */
  @java.lang.Override
  public xddq.pb.PetPassiveSkillMsg getPetSoulShapePassiveSkill(int index) {
    return petSoulShapePassiveSkill_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.PetPassiveSkillMsg petSoulShapePassiveSkill = 8;</code>
   */
  @java.lang.Override
  public xddq.pb.PetPassiveSkillMsgOrBuilder getPetSoulShapePassiveSkillOrBuilder(
      int index) {
    return petSoulShapePassiveSkill_.get(index);
  }

  public static final int WAITSELECTSKILL_FIELD_NUMBER = 9;
  @SuppressWarnings("serial")
  private com.google.protobuf.Internal.IntList waitSelectSkill_ =
      emptyIntList();
  /**
   * <code>repeated int32 waitSelectSkill = 9;</code>
   * @return A list containing the waitSelectSkill.
   */
  @java.lang.Override
  public java.util.List<java.lang.Integer>
      getWaitSelectSkillList() {
    return waitSelectSkill_;
  }
  /**
   * <code>repeated int32 waitSelectSkill = 9;</code>
   * @return The count of waitSelectSkill.
   */
  public int getWaitSelectSkillCount() {
    return waitSelectSkill_.size();
  }
  /**
   * <code>repeated int32 waitSelectSkill = 9;</code>
   * @param index The index of the element to return.
   * @return The waitSelectSkill at the given index.
   */
  public int getWaitSelectSkill(int index) {
    return waitSelectSkill_.getInt(index);
  }

  public static final int SOULSHAPELV_FIELD_NUMBER = 10;
  private int soulShapeLv_ = 0;
  /**
   * <code>optional int32 soulShapeLv = 10;</code>
   * @return Whether the soulShapeLv field is set.
   */
  @java.lang.Override
  public boolean hasSoulShapeLv() {
    return ((bitField0_ & 0x00000040) != 0);
  }
  /**
   * <code>optional int32 soulShapeLv = 10;</code>
   * @return The soulShapeLv.
   */
  @java.lang.Override
  public int getSoulShapeLv() {
    return soulShapeLv_;
  }

  public static final int ASSISTANTPETSOULSHAPELV_FIELD_NUMBER = 11;
  private int assistantPetSoulShapeLv_ = 0;
  /**
   * <code>optional int32 assistantPetSoulShapeLv = 11;</code>
   * @return Whether the assistantPetSoulShapeLv field is set.
   */
  @java.lang.Override
  public boolean hasAssistantPetSoulShapeLv() {
    return ((bitField0_ & 0x00000080) != 0);
  }
  /**
   * <code>optional int32 assistantPetSoulShapeLv = 11;</code>
   * @return The assistantPetSoulShapeLv.
   */
  @java.lang.Override
  public int getAssistantPetSoulShapeLv() {
    return assistantPetSoulShapeLv_;
  }

  public static final int HAVEWAITREFRESHSKILL_FIELD_NUMBER = 12;
  private int haveWaitRefreshSkill_ = 0;
  /**
   * <code>optional int32 haveWaitRefreshSkill = 12;</code>
   * @return Whether the haveWaitRefreshSkill field is set.
   */
  @java.lang.Override
  public boolean hasHaveWaitRefreshSkill() {
    return ((bitField0_ & 0x00000100) != 0);
  }
  /**
   * <code>optional int32 haveWaitRefreshSkill = 12;</code>
   * @return The haveWaitRefreshSkill.
   */
  @java.lang.Override
  public int getHaveWaitRefreshSkill() {
    return haveWaitRefreshSkill_;
  }

  public static final int LOCKSTATE_FIELD_NUMBER = 13;
  private int lockState_ = 0;
  /**
   * <code>optional int32 lockState = 13;</code>
   * @return Whether the lockState field is set.
   */
  @java.lang.Override
  public boolean hasLockState() {
    return ((bitField0_ & 0x00000200) != 0);
  }
  /**
   * <code>optional int32 lockState = 13;</code>
   * @return The lockState.
   */
  @java.lang.Override
  public int getLockState() {
    return lockState_;
  }

  public static final int EQUIPLINKAGEID_FIELD_NUMBER = 14;
  private int equipLinkageId_ = 0;
  /**
   * <code>optional int32 equipLinkageId = 14;</code>
   * @return Whether the equipLinkageId field is set.
   */
  @java.lang.Override
  public boolean hasEquipLinkageId() {
    return ((bitField0_ & 0x00000400) != 0);
  }
  /**
   * <code>optional int32 equipLinkageId = 14;</code>
   * @return The equipLinkageId.
   */
  @java.lang.Override
  public int getEquipLinkageId() {
    return equipLinkageId_;
  }

  public static final int SYNERGYLINKAGEID_FIELD_NUMBER = 15;
  private int synergyLinkageId_ = 0;
  /**
   * <code>optional int32 synergyLinkageId = 15;</code>
   * @return Whether the synergyLinkageId field is set.
   */
  @java.lang.Override
  public boolean hasSynergyLinkageId() {
    return ((bitField0_ & 0x00000800) != 0);
  }
  /**
   * <code>optional int32 synergyLinkageId = 15;</code>
   * @return The synergyLinkageId.
   */
  @java.lang.Override
  public int getSynergyLinkageId() {
    return synergyLinkageId_;
  }

  public static final int KERNELLV_FIELD_NUMBER = 16;
  private int kernelLv_ = 0;
  /**
   * <code>optional int32 kernelLv = 16;</code>
   * @return Whether the kernelLv field is set.
   */
  @java.lang.Override
  public boolean hasKernelLv() {
    return ((bitField0_ & 0x00001000) != 0);
  }
  /**
   * <code>optional int32 kernelLv = 16;</code>
   * @return The kernelLv.
   */
  @java.lang.Override
  public int getKernelLv() {
    return kernelLv_;
  }

  public static final int EQUIPKERNELID_FIELD_NUMBER = 17;
  @SuppressWarnings("serial")
  private com.google.protobuf.Internal.IntList equipKernelId_ =
      emptyIntList();
  /**
   * <code>repeated int32 equipKernelId = 17;</code>
   * @return A list containing the equipKernelId.
   */
  @java.lang.Override
  public java.util.List<java.lang.Integer>
      getEquipKernelIdList() {
    return equipKernelId_;
  }
  /**
   * <code>repeated int32 equipKernelId = 17;</code>
   * @return The count of equipKernelId.
   */
  public int getEquipKernelIdCount() {
    return equipKernelId_.size();
  }
  /**
   * <code>repeated int32 equipKernelId = 17;</code>
   * @param index The index of the element to return.
   * @return The equipKernelId at the given index.
   */
  public int getEquipKernelId(int index) {
    return equipKernelId_.getInt(index);
  }

  public static final int ROOTLV_FIELD_NUMBER = 18;
  private int rootLv_ = 0;
  /**
   * <code>optional int32 rootLv = 18;</code>
   * @return Whether the rootLv field is set.
   */
  @java.lang.Override
  public boolean hasRootLv() {
    return ((bitField0_ & 0x00002000) != 0);
  }
  /**
   * <code>optional int32 rootLv = 18;</code>
   * @return The rootLv.
   */
  @java.lang.Override
  public int getRootLv() {
    return rootLv_;
  }

  public static final int AWAKELV_FIELD_NUMBER = 19;
  private int awakeLv_ = 0;
  /**
   * <code>optional int32 awakeLv = 19;</code>
   * @return Whether the awakeLv field is set.
   */
  @java.lang.Override
  public boolean hasAwakeLv() {
    return ((bitField0_ & 0x00004000) != 0);
  }
  /**
   * <code>optional int32 awakeLv = 19;</code>
   * @return The awakeLv.
   */
  @java.lang.Override
  public int getAwakeLv() {
    return awakeLv_;
  }

  public static final int STAGELV_FIELD_NUMBER = 20;
  private int stageLv_ = 0;
  /**
   * <code>optional int32 stageLv = 20;</code>
   * @return Whether the stageLv field is set.
   */
  @java.lang.Override
  public boolean hasStageLv() {
    return ((bitField0_ & 0x00008000) != 0);
  }
  /**
   * <code>optional int32 stageLv = 20;</code>
   * @return The stageLv.
   */
  @java.lang.Override
  public int getStageLv() {
    return stageLv_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasId()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasConfigId()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasLv()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasStar()) {
      memoizedIsInitialized = 0;
      return false;
    }
    for (int i = 0; i < getPetPassiveSkillCount(); i++) {
      if (!getPetPassiveSkill(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    for (int i = 0; i < getPetSoulShapePassiveSkillCount(); i++) {
      if (!getPetSoulShapePassiveSkill(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, id_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(2, configId_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt32(3, lv_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt32(4, star_);
    }
    for (int i = 0; i < petPassiveSkill_.size(); i++) {
      output.writeMessage(5, petPassiveSkill_.get(i));
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      output.writeInt32(6, assistantPetId_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      output.writeInt32(7, assistantConfigId_);
    }
    for (int i = 0; i < petSoulShapePassiveSkill_.size(); i++) {
      output.writeMessage(8, petSoulShapePassiveSkill_.get(i));
    }
    for (int i = 0; i < waitSelectSkill_.size(); i++) {
      output.writeInt32(9, waitSelectSkill_.getInt(i));
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      output.writeInt32(10, soulShapeLv_);
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      output.writeInt32(11, assistantPetSoulShapeLv_);
    }
    if (((bitField0_ & 0x00000100) != 0)) {
      output.writeInt32(12, haveWaitRefreshSkill_);
    }
    if (((bitField0_ & 0x00000200) != 0)) {
      output.writeInt32(13, lockState_);
    }
    if (((bitField0_ & 0x00000400) != 0)) {
      output.writeInt32(14, equipLinkageId_);
    }
    if (((bitField0_ & 0x00000800) != 0)) {
      output.writeInt32(15, synergyLinkageId_);
    }
    if (((bitField0_ & 0x00001000) != 0)) {
      output.writeInt32(16, kernelLv_);
    }
    for (int i = 0; i < equipKernelId_.size(); i++) {
      output.writeInt32(17, equipKernelId_.getInt(i));
    }
    if (((bitField0_ & 0x00002000) != 0)) {
      output.writeInt32(18, rootLv_);
    }
    if (((bitField0_ & 0x00004000) != 0)) {
      output.writeInt32(19, awakeLv_);
    }
    if (((bitField0_ & 0x00008000) != 0)) {
      output.writeInt32(20, stageLv_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, id_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, configId_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(3, lv_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(4, star_);
    }
    for (int i = 0; i < petPassiveSkill_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(5, petPassiveSkill_.get(i));
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(6, assistantPetId_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(7, assistantConfigId_);
    }
    for (int i = 0; i < petSoulShapePassiveSkill_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(8, petSoulShapePassiveSkill_.get(i));
    }
    {
      int dataSize = 0;
      for (int i = 0; i < waitSelectSkill_.size(); i++) {
        dataSize += com.google.protobuf.CodedOutputStream
          .computeInt32SizeNoTag(waitSelectSkill_.getInt(i));
      }
      size += dataSize;
      size += 1 * getWaitSelectSkillList().size();
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(10, soulShapeLv_);
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(11, assistantPetSoulShapeLv_);
    }
    if (((bitField0_ & 0x00000100) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(12, haveWaitRefreshSkill_);
    }
    if (((bitField0_ & 0x00000200) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(13, lockState_);
    }
    if (((bitField0_ & 0x00000400) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(14, equipLinkageId_);
    }
    if (((bitField0_ & 0x00000800) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(15, synergyLinkageId_);
    }
    if (((bitField0_ & 0x00001000) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(16, kernelLv_);
    }
    {
      int dataSize = 0;
      for (int i = 0; i < equipKernelId_.size(); i++) {
        dataSize += com.google.protobuf.CodedOutputStream
          .computeInt32SizeNoTag(equipKernelId_.getInt(i));
      }
      size += dataSize;
      size += 2 * getEquipKernelIdList().size();
    }
    if (((bitField0_ & 0x00002000) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(18, rootLv_);
    }
    if (((bitField0_ & 0x00004000) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(19, awakeLv_);
    }
    if (((bitField0_ & 0x00008000) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(20, stageLv_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.PetDataMsg)) {
      return super.equals(obj);
    }
    xddq.pb.PetDataMsg other = (xddq.pb.PetDataMsg) obj;

    if (hasId() != other.hasId()) return false;
    if (hasId()) {
      if (getId()
          != other.getId()) return false;
    }
    if (hasConfigId() != other.hasConfigId()) return false;
    if (hasConfigId()) {
      if (getConfigId()
          != other.getConfigId()) return false;
    }
    if (hasLv() != other.hasLv()) return false;
    if (hasLv()) {
      if (getLv()
          != other.getLv()) return false;
    }
    if (hasStar() != other.hasStar()) return false;
    if (hasStar()) {
      if (getStar()
          != other.getStar()) return false;
    }
    if (!getPetPassiveSkillList()
        .equals(other.getPetPassiveSkillList())) return false;
    if (hasAssistantPetId() != other.hasAssistantPetId()) return false;
    if (hasAssistantPetId()) {
      if (getAssistantPetId()
          != other.getAssistantPetId()) return false;
    }
    if (hasAssistantConfigId() != other.hasAssistantConfigId()) return false;
    if (hasAssistantConfigId()) {
      if (getAssistantConfigId()
          != other.getAssistantConfigId()) return false;
    }
    if (!getPetSoulShapePassiveSkillList()
        .equals(other.getPetSoulShapePassiveSkillList())) return false;
    if (!getWaitSelectSkillList()
        .equals(other.getWaitSelectSkillList())) return false;
    if (hasSoulShapeLv() != other.hasSoulShapeLv()) return false;
    if (hasSoulShapeLv()) {
      if (getSoulShapeLv()
          != other.getSoulShapeLv()) return false;
    }
    if (hasAssistantPetSoulShapeLv() != other.hasAssistantPetSoulShapeLv()) return false;
    if (hasAssistantPetSoulShapeLv()) {
      if (getAssistantPetSoulShapeLv()
          != other.getAssistantPetSoulShapeLv()) return false;
    }
    if (hasHaveWaitRefreshSkill() != other.hasHaveWaitRefreshSkill()) return false;
    if (hasHaveWaitRefreshSkill()) {
      if (getHaveWaitRefreshSkill()
          != other.getHaveWaitRefreshSkill()) return false;
    }
    if (hasLockState() != other.hasLockState()) return false;
    if (hasLockState()) {
      if (getLockState()
          != other.getLockState()) return false;
    }
    if (hasEquipLinkageId() != other.hasEquipLinkageId()) return false;
    if (hasEquipLinkageId()) {
      if (getEquipLinkageId()
          != other.getEquipLinkageId()) return false;
    }
    if (hasSynergyLinkageId() != other.hasSynergyLinkageId()) return false;
    if (hasSynergyLinkageId()) {
      if (getSynergyLinkageId()
          != other.getSynergyLinkageId()) return false;
    }
    if (hasKernelLv() != other.hasKernelLv()) return false;
    if (hasKernelLv()) {
      if (getKernelLv()
          != other.getKernelLv()) return false;
    }
    if (!getEquipKernelIdList()
        .equals(other.getEquipKernelIdList())) return false;
    if (hasRootLv() != other.hasRootLv()) return false;
    if (hasRootLv()) {
      if (getRootLv()
          != other.getRootLv()) return false;
    }
    if (hasAwakeLv() != other.hasAwakeLv()) return false;
    if (hasAwakeLv()) {
      if (getAwakeLv()
          != other.getAwakeLv()) return false;
    }
    if (hasStageLv() != other.hasStageLv()) return false;
    if (hasStageLv()) {
      if (getStageLv()
          != other.getStageLv()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasId()) {
      hash = (37 * hash) + ID_FIELD_NUMBER;
      hash = (53 * hash) + getId();
    }
    if (hasConfigId()) {
      hash = (37 * hash) + CONFIGID_FIELD_NUMBER;
      hash = (53 * hash) + getConfigId();
    }
    if (hasLv()) {
      hash = (37 * hash) + LV_FIELD_NUMBER;
      hash = (53 * hash) + getLv();
    }
    if (hasStar()) {
      hash = (37 * hash) + STAR_FIELD_NUMBER;
      hash = (53 * hash) + getStar();
    }
    if (getPetPassiveSkillCount() > 0) {
      hash = (37 * hash) + PETPASSIVESKILL_FIELD_NUMBER;
      hash = (53 * hash) + getPetPassiveSkillList().hashCode();
    }
    if (hasAssistantPetId()) {
      hash = (37 * hash) + ASSISTANTPETID_FIELD_NUMBER;
      hash = (53 * hash) + getAssistantPetId();
    }
    if (hasAssistantConfigId()) {
      hash = (37 * hash) + ASSISTANTCONFIGID_FIELD_NUMBER;
      hash = (53 * hash) + getAssistantConfigId();
    }
    if (getPetSoulShapePassiveSkillCount() > 0) {
      hash = (37 * hash) + PETSOULSHAPEPASSIVESKILL_FIELD_NUMBER;
      hash = (53 * hash) + getPetSoulShapePassiveSkillList().hashCode();
    }
    if (getWaitSelectSkillCount() > 0) {
      hash = (37 * hash) + WAITSELECTSKILL_FIELD_NUMBER;
      hash = (53 * hash) + getWaitSelectSkillList().hashCode();
    }
    if (hasSoulShapeLv()) {
      hash = (37 * hash) + SOULSHAPELV_FIELD_NUMBER;
      hash = (53 * hash) + getSoulShapeLv();
    }
    if (hasAssistantPetSoulShapeLv()) {
      hash = (37 * hash) + ASSISTANTPETSOULSHAPELV_FIELD_NUMBER;
      hash = (53 * hash) + getAssistantPetSoulShapeLv();
    }
    if (hasHaveWaitRefreshSkill()) {
      hash = (37 * hash) + HAVEWAITREFRESHSKILL_FIELD_NUMBER;
      hash = (53 * hash) + getHaveWaitRefreshSkill();
    }
    if (hasLockState()) {
      hash = (37 * hash) + LOCKSTATE_FIELD_NUMBER;
      hash = (53 * hash) + getLockState();
    }
    if (hasEquipLinkageId()) {
      hash = (37 * hash) + EQUIPLINKAGEID_FIELD_NUMBER;
      hash = (53 * hash) + getEquipLinkageId();
    }
    if (hasSynergyLinkageId()) {
      hash = (37 * hash) + SYNERGYLINKAGEID_FIELD_NUMBER;
      hash = (53 * hash) + getSynergyLinkageId();
    }
    if (hasKernelLv()) {
      hash = (37 * hash) + KERNELLV_FIELD_NUMBER;
      hash = (53 * hash) + getKernelLv();
    }
    if (getEquipKernelIdCount() > 0) {
      hash = (37 * hash) + EQUIPKERNELID_FIELD_NUMBER;
      hash = (53 * hash) + getEquipKernelIdList().hashCode();
    }
    if (hasRootLv()) {
      hash = (37 * hash) + ROOTLV_FIELD_NUMBER;
      hash = (53 * hash) + getRootLv();
    }
    if (hasAwakeLv()) {
      hash = (37 * hash) + AWAKELV_FIELD_NUMBER;
      hash = (53 * hash) + getAwakeLv();
    }
    if (hasStageLv()) {
      hash = (37 * hash) + STAGELV_FIELD_NUMBER;
      hash = (53 * hash) + getStageLv();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.PetDataMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PetDataMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PetDataMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PetDataMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PetDataMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PetDataMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PetDataMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.PetDataMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.PetDataMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.PetDataMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.PetDataMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.PetDataMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.PetDataMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.PetDataMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.PetDataMsg)
      xddq.pb.PetDataMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PetDataMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PetDataMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.PetDataMsg.class, xddq.pb.PetDataMsg.Builder.class);
    }

    // Construct using xddq.pb.PetDataMsg.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      id_ = 0;
      configId_ = 0;
      lv_ = 0;
      star_ = 0;
      if (petPassiveSkillBuilder_ == null) {
        petPassiveSkill_ = java.util.Collections.emptyList();
      } else {
        petPassiveSkill_ = null;
        petPassiveSkillBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000010);
      assistantPetId_ = 0;
      assistantConfigId_ = 0;
      if (petSoulShapePassiveSkillBuilder_ == null) {
        petSoulShapePassiveSkill_ = java.util.Collections.emptyList();
      } else {
        petSoulShapePassiveSkill_ = null;
        petSoulShapePassiveSkillBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000080);
      waitSelectSkill_ = emptyIntList();
      soulShapeLv_ = 0;
      assistantPetSoulShapeLv_ = 0;
      haveWaitRefreshSkill_ = 0;
      lockState_ = 0;
      equipLinkageId_ = 0;
      synergyLinkageId_ = 0;
      kernelLv_ = 0;
      equipKernelId_ = emptyIntList();
      rootLv_ = 0;
      awakeLv_ = 0;
      stageLv_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PetDataMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.PetDataMsg getDefaultInstanceForType() {
      return xddq.pb.PetDataMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.PetDataMsg build() {
      xddq.pb.PetDataMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.PetDataMsg buildPartial() {
      xddq.pb.PetDataMsg result = new xddq.pb.PetDataMsg(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.PetDataMsg result) {
      if (petPassiveSkillBuilder_ == null) {
        if (((bitField0_ & 0x00000010) != 0)) {
          petPassiveSkill_ = java.util.Collections.unmodifiableList(petPassiveSkill_);
          bitField0_ = (bitField0_ & ~0x00000010);
        }
        result.petPassiveSkill_ = petPassiveSkill_;
      } else {
        result.petPassiveSkill_ = petPassiveSkillBuilder_.build();
      }
      if (petSoulShapePassiveSkillBuilder_ == null) {
        if (((bitField0_ & 0x00000080) != 0)) {
          petSoulShapePassiveSkill_ = java.util.Collections.unmodifiableList(petSoulShapePassiveSkill_);
          bitField0_ = (bitField0_ & ~0x00000080);
        }
        result.petSoulShapePassiveSkill_ = petSoulShapePassiveSkill_;
      } else {
        result.petSoulShapePassiveSkill_ = petSoulShapePassiveSkillBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.PetDataMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.id_ = id_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.configId_ = configId_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.lv_ = lv_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.star_ = star_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.assistantPetId_ = assistantPetId_;
        to_bitField0_ |= 0x00000010;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.assistantConfigId_ = assistantConfigId_;
        to_bitField0_ |= 0x00000020;
      }
      if (((from_bitField0_ & 0x00000100) != 0)) {
        waitSelectSkill_.makeImmutable();
        result.waitSelectSkill_ = waitSelectSkill_;
      }
      if (((from_bitField0_ & 0x00000200) != 0)) {
        result.soulShapeLv_ = soulShapeLv_;
        to_bitField0_ |= 0x00000040;
      }
      if (((from_bitField0_ & 0x00000400) != 0)) {
        result.assistantPetSoulShapeLv_ = assistantPetSoulShapeLv_;
        to_bitField0_ |= 0x00000080;
      }
      if (((from_bitField0_ & 0x00000800) != 0)) {
        result.haveWaitRefreshSkill_ = haveWaitRefreshSkill_;
        to_bitField0_ |= 0x00000100;
      }
      if (((from_bitField0_ & 0x00001000) != 0)) {
        result.lockState_ = lockState_;
        to_bitField0_ |= 0x00000200;
      }
      if (((from_bitField0_ & 0x00002000) != 0)) {
        result.equipLinkageId_ = equipLinkageId_;
        to_bitField0_ |= 0x00000400;
      }
      if (((from_bitField0_ & 0x00004000) != 0)) {
        result.synergyLinkageId_ = synergyLinkageId_;
        to_bitField0_ |= 0x00000800;
      }
      if (((from_bitField0_ & 0x00008000) != 0)) {
        result.kernelLv_ = kernelLv_;
        to_bitField0_ |= 0x00001000;
      }
      if (((from_bitField0_ & 0x00010000) != 0)) {
        equipKernelId_.makeImmutable();
        result.equipKernelId_ = equipKernelId_;
      }
      if (((from_bitField0_ & 0x00020000) != 0)) {
        result.rootLv_ = rootLv_;
        to_bitField0_ |= 0x00002000;
      }
      if (((from_bitField0_ & 0x00040000) != 0)) {
        result.awakeLv_ = awakeLv_;
        to_bitField0_ |= 0x00004000;
      }
      if (((from_bitField0_ & 0x00080000) != 0)) {
        result.stageLv_ = stageLv_;
        to_bitField0_ |= 0x00008000;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.PetDataMsg) {
        return mergeFrom((xddq.pb.PetDataMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.PetDataMsg other) {
      if (other == xddq.pb.PetDataMsg.getDefaultInstance()) return this;
      if (other.hasId()) {
        setId(other.getId());
      }
      if (other.hasConfigId()) {
        setConfigId(other.getConfigId());
      }
      if (other.hasLv()) {
        setLv(other.getLv());
      }
      if (other.hasStar()) {
        setStar(other.getStar());
      }
      if (petPassiveSkillBuilder_ == null) {
        if (!other.petPassiveSkill_.isEmpty()) {
          if (petPassiveSkill_.isEmpty()) {
            petPassiveSkill_ = other.petPassiveSkill_;
            bitField0_ = (bitField0_ & ~0x00000010);
          } else {
            ensurePetPassiveSkillIsMutable();
            petPassiveSkill_.addAll(other.petPassiveSkill_);
          }
          onChanged();
        }
      } else {
        if (!other.petPassiveSkill_.isEmpty()) {
          if (petPassiveSkillBuilder_.isEmpty()) {
            petPassiveSkillBuilder_.dispose();
            petPassiveSkillBuilder_ = null;
            petPassiveSkill_ = other.petPassiveSkill_;
            bitField0_ = (bitField0_ & ~0x00000010);
            petPassiveSkillBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetPetPassiveSkillFieldBuilder() : null;
          } else {
            petPassiveSkillBuilder_.addAllMessages(other.petPassiveSkill_);
          }
        }
      }
      if (other.hasAssistantPetId()) {
        setAssistantPetId(other.getAssistantPetId());
      }
      if (other.hasAssistantConfigId()) {
        setAssistantConfigId(other.getAssistantConfigId());
      }
      if (petSoulShapePassiveSkillBuilder_ == null) {
        if (!other.petSoulShapePassiveSkill_.isEmpty()) {
          if (petSoulShapePassiveSkill_.isEmpty()) {
            petSoulShapePassiveSkill_ = other.petSoulShapePassiveSkill_;
            bitField0_ = (bitField0_ & ~0x00000080);
          } else {
            ensurePetSoulShapePassiveSkillIsMutable();
            petSoulShapePassiveSkill_.addAll(other.petSoulShapePassiveSkill_);
          }
          onChanged();
        }
      } else {
        if (!other.petSoulShapePassiveSkill_.isEmpty()) {
          if (petSoulShapePassiveSkillBuilder_.isEmpty()) {
            petSoulShapePassiveSkillBuilder_.dispose();
            petSoulShapePassiveSkillBuilder_ = null;
            petSoulShapePassiveSkill_ = other.petSoulShapePassiveSkill_;
            bitField0_ = (bitField0_ & ~0x00000080);
            petSoulShapePassiveSkillBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetPetSoulShapePassiveSkillFieldBuilder() : null;
          } else {
            petSoulShapePassiveSkillBuilder_.addAllMessages(other.petSoulShapePassiveSkill_);
          }
        }
      }
      if (!other.waitSelectSkill_.isEmpty()) {
        if (waitSelectSkill_.isEmpty()) {
          waitSelectSkill_ = other.waitSelectSkill_;
          waitSelectSkill_.makeImmutable();
          bitField0_ |= 0x00000100;
        } else {
          ensureWaitSelectSkillIsMutable();
          waitSelectSkill_.addAll(other.waitSelectSkill_);
        }
        onChanged();
      }
      if (other.hasSoulShapeLv()) {
        setSoulShapeLv(other.getSoulShapeLv());
      }
      if (other.hasAssistantPetSoulShapeLv()) {
        setAssistantPetSoulShapeLv(other.getAssistantPetSoulShapeLv());
      }
      if (other.hasHaveWaitRefreshSkill()) {
        setHaveWaitRefreshSkill(other.getHaveWaitRefreshSkill());
      }
      if (other.hasLockState()) {
        setLockState(other.getLockState());
      }
      if (other.hasEquipLinkageId()) {
        setEquipLinkageId(other.getEquipLinkageId());
      }
      if (other.hasSynergyLinkageId()) {
        setSynergyLinkageId(other.getSynergyLinkageId());
      }
      if (other.hasKernelLv()) {
        setKernelLv(other.getKernelLv());
      }
      if (!other.equipKernelId_.isEmpty()) {
        if (equipKernelId_.isEmpty()) {
          equipKernelId_ = other.equipKernelId_;
          equipKernelId_.makeImmutable();
          bitField0_ |= 0x00010000;
        } else {
          ensureEquipKernelIdIsMutable();
          equipKernelId_.addAll(other.equipKernelId_);
        }
        onChanged();
      }
      if (other.hasRootLv()) {
        setRootLv(other.getRootLv());
      }
      if (other.hasAwakeLv()) {
        setAwakeLv(other.getAwakeLv());
      }
      if (other.hasStageLv()) {
        setStageLv(other.getStageLv());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasId()) {
        return false;
      }
      if (!hasConfigId()) {
        return false;
      }
      if (!hasLv()) {
        return false;
      }
      if (!hasStar()) {
        return false;
      }
      for (int i = 0; i < getPetPassiveSkillCount(); i++) {
        if (!getPetPassiveSkill(i).isInitialized()) {
          return false;
        }
      }
      for (int i = 0; i < getPetSoulShapePassiveSkillCount(); i++) {
        if (!getPetSoulShapePassiveSkill(i).isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              id_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              configId_ = input.readInt32();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 24: {
              lv_ = input.readInt32();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 32: {
              star_ = input.readInt32();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            case 42: {
              xddq.pb.PetPassiveSkillMsg m =
                  input.readMessage(
                      xddq.pb.PetPassiveSkillMsg.parser(),
                      extensionRegistry);
              if (petPassiveSkillBuilder_ == null) {
                ensurePetPassiveSkillIsMutable();
                petPassiveSkill_.add(m);
              } else {
                petPassiveSkillBuilder_.addMessage(m);
              }
              break;
            } // case 42
            case 48: {
              assistantPetId_ = input.readInt32();
              bitField0_ |= 0x00000020;
              break;
            } // case 48
            case 56: {
              assistantConfigId_ = input.readInt32();
              bitField0_ |= 0x00000040;
              break;
            } // case 56
            case 66: {
              xddq.pb.PetPassiveSkillMsg m =
                  input.readMessage(
                      xddq.pb.PetPassiveSkillMsg.parser(),
                      extensionRegistry);
              if (petSoulShapePassiveSkillBuilder_ == null) {
                ensurePetSoulShapePassiveSkillIsMutable();
                petSoulShapePassiveSkill_.add(m);
              } else {
                petSoulShapePassiveSkillBuilder_.addMessage(m);
              }
              break;
            } // case 66
            case 72: {
              int v = input.readInt32();
              ensureWaitSelectSkillIsMutable();
              waitSelectSkill_.addInt(v);
              break;
            } // case 72
            case 74: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              ensureWaitSelectSkillIsMutable();
              while (input.getBytesUntilLimit() > 0) {
                waitSelectSkill_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            } // case 74
            case 80: {
              soulShapeLv_ = input.readInt32();
              bitField0_ |= 0x00000200;
              break;
            } // case 80
            case 88: {
              assistantPetSoulShapeLv_ = input.readInt32();
              bitField0_ |= 0x00000400;
              break;
            } // case 88
            case 96: {
              haveWaitRefreshSkill_ = input.readInt32();
              bitField0_ |= 0x00000800;
              break;
            } // case 96
            case 104: {
              lockState_ = input.readInt32();
              bitField0_ |= 0x00001000;
              break;
            } // case 104
            case 112: {
              equipLinkageId_ = input.readInt32();
              bitField0_ |= 0x00002000;
              break;
            } // case 112
            case 120: {
              synergyLinkageId_ = input.readInt32();
              bitField0_ |= 0x00004000;
              break;
            } // case 120
            case 128: {
              kernelLv_ = input.readInt32();
              bitField0_ |= 0x00008000;
              break;
            } // case 128
            case 136: {
              int v = input.readInt32();
              ensureEquipKernelIdIsMutable();
              equipKernelId_.addInt(v);
              break;
            } // case 136
            case 138: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              ensureEquipKernelIdIsMutable();
              while (input.getBytesUntilLimit() > 0) {
                equipKernelId_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            } // case 138
            case 144: {
              rootLv_ = input.readInt32();
              bitField0_ |= 0x00020000;
              break;
            } // case 144
            case 152: {
              awakeLv_ = input.readInt32();
              bitField0_ |= 0x00040000;
              break;
            } // case 152
            case 160: {
              stageLv_ = input.readInt32();
              bitField0_ |= 0x00080000;
              break;
            } // case 160
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int id_ ;
    /**
     * <code>required int32 id = 1;</code>
     * @return Whether the id field is set.
     */
    @java.lang.Override
    public boolean hasId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 id = 1;</code>
     * @return The id.
     */
    @java.lang.Override
    public int getId() {
      return id_;
    }
    /**
     * <code>required int32 id = 1;</code>
     * @param value The id to set.
     * @return This builder for chaining.
     */
    public Builder setId(int value) {

      id_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 id = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      id_ = 0;
      onChanged();
      return this;
    }

    private int configId_ ;
    /**
     * <code>required int32 configId = 2;</code>
     * @return Whether the configId field is set.
     */
    @java.lang.Override
    public boolean hasConfigId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>required int32 configId = 2;</code>
     * @return The configId.
     */
    @java.lang.Override
    public int getConfigId() {
      return configId_;
    }
    /**
     * <code>required int32 configId = 2;</code>
     * @param value The configId to set.
     * @return This builder for chaining.
     */
    public Builder setConfigId(int value) {

      configId_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 configId = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearConfigId() {
      bitField0_ = (bitField0_ & ~0x00000002);
      configId_ = 0;
      onChanged();
      return this;
    }

    private int lv_ ;
    /**
     * <code>required int32 lv = 3;</code>
     * @return Whether the lv field is set.
     */
    @java.lang.Override
    public boolean hasLv() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>required int32 lv = 3;</code>
     * @return The lv.
     */
    @java.lang.Override
    public int getLv() {
      return lv_;
    }
    /**
     * <code>required int32 lv = 3;</code>
     * @param value The lv to set.
     * @return This builder for chaining.
     */
    public Builder setLv(int value) {

      lv_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 lv = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearLv() {
      bitField0_ = (bitField0_ & ~0x00000004);
      lv_ = 0;
      onChanged();
      return this;
    }

    private int star_ ;
    /**
     * <code>required int32 star = 4;</code>
     * @return Whether the star field is set.
     */
    @java.lang.Override
    public boolean hasStar() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>required int32 star = 4;</code>
     * @return The star.
     */
    @java.lang.Override
    public int getStar() {
      return star_;
    }
    /**
     * <code>required int32 star = 4;</code>
     * @param value The star to set.
     * @return This builder for chaining.
     */
    public Builder setStar(int value) {

      star_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 star = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearStar() {
      bitField0_ = (bitField0_ & ~0x00000008);
      star_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.PetPassiveSkillMsg> petPassiveSkill_ =
      java.util.Collections.emptyList();
    private void ensurePetPassiveSkillIsMutable() {
      if (!((bitField0_ & 0x00000010) != 0)) {
        petPassiveSkill_ = new java.util.ArrayList<xddq.pb.PetPassiveSkillMsg>(petPassiveSkill_);
        bitField0_ |= 0x00000010;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.PetPassiveSkillMsg, xddq.pb.PetPassiveSkillMsg.Builder, xddq.pb.PetPassiveSkillMsgOrBuilder> petPassiveSkillBuilder_;

    /**
     * <code>repeated .xddq.pb.PetPassiveSkillMsg petPassiveSkill = 5;</code>
     */
    public java.util.List<xddq.pb.PetPassiveSkillMsg> getPetPassiveSkillList() {
      if (petPassiveSkillBuilder_ == null) {
        return java.util.Collections.unmodifiableList(petPassiveSkill_);
      } else {
        return petPassiveSkillBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.PetPassiveSkillMsg petPassiveSkill = 5;</code>
     */
    public int getPetPassiveSkillCount() {
      if (petPassiveSkillBuilder_ == null) {
        return petPassiveSkill_.size();
      } else {
        return petPassiveSkillBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.PetPassiveSkillMsg petPassiveSkill = 5;</code>
     */
    public xddq.pb.PetPassiveSkillMsg getPetPassiveSkill(int index) {
      if (petPassiveSkillBuilder_ == null) {
        return petPassiveSkill_.get(index);
      } else {
        return petPassiveSkillBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.PetPassiveSkillMsg petPassiveSkill = 5;</code>
     */
    public Builder setPetPassiveSkill(
        int index, xddq.pb.PetPassiveSkillMsg value) {
      if (petPassiveSkillBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePetPassiveSkillIsMutable();
        petPassiveSkill_.set(index, value);
        onChanged();
      } else {
        petPassiveSkillBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PetPassiveSkillMsg petPassiveSkill = 5;</code>
     */
    public Builder setPetPassiveSkill(
        int index, xddq.pb.PetPassiveSkillMsg.Builder builderForValue) {
      if (petPassiveSkillBuilder_ == null) {
        ensurePetPassiveSkillIsMutable();
        petPassiveSkill_.set(index, builderForValue.build());
        onChanged();
      } else {
        petPassiveSkillBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PetPassiveSkillMsg petPassiveSkill = 5;</code>
     */
    public Builder addPetPassiveSkill(xddq.pb.PetPassiveSkillMsg value) {
      if (petPassiveSkillBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePetPassiveSkillIsMutable();
        petPassiveSkill_.add(value);
        onChanged();
      } else {
        petPassiveSkillBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PetPassiveSkillMsg petPassiveSkill = 5;</code>
     */
    public Builder addPetPassiveSkill(
        int index, xddq.pb.PetPassiveSkillMsg value) {
      if (petPassiveSkillBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePetPassiveSkillIsMutable();
        petPassiveSkill_.add(index, value);
        onChanged();
      } else {
        petPassiveSkillBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PetPassiveSkillMsg petPassiveSkill = 5;</code>
     */
    public Builder addPetPassiveSkill(
        xddq.pb.PetPassiveSkillMsg.Builder builderForValue) {
      if (petPassiveSkillBuilder_ == null) {
        ensurePetPassiveSkillIsMutable();
        petPassiveSkill_.add(builderForValue.build());
        onChanged();
      } else {
        petPassiveSkillBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PetPassiveSkillMsg petPassiveSkill = 5;</code>
     */
    public Builder addPetPassiveSkill(
        int index, xddq.pb.PetPassiveSkillMsg.Builder builderForValue) {
      if (petPassiveSkillBuilder_ == null) {
        ensurePetPassiveSkillIsMutable();
        petPassiveSkill_.add(index, builderForValue.build());
        onChanged();
      } else {
        petPassiveSkillBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PetPassiveSkillMsg petPassiveSkill = 5;</code>
     */
    public Builder addAllPetPassiveSkill(
        java.lang.Iterable<? extends xddq.pb.PetPassiveSkillMsg> values) {
      if (petPassiveSkillBuilder_ == null) {
        ensurePetPassiveSkillIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, petPassiveSkill_);
        onChanged();
      } else {
        petPassiveSkillBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PetPassiveSkillMsg petPassiveSkill = 5;</code>
     */
    public Builder clearPetPassiveSkill() {
      if (petPassiveSkillBuilder_ == null) {
        petPassiveSkill_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000010);
        onChanged();
      } else {
        petPassiveSkillBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PetPassiveSkillMsg petPassiveSkill = 5;</code>
     */
    public Builder removePetPassiveSkill(int index) {
      if (petPassiveSkillBuilder_ == null) {
        ensurePetPassiveSkillIsMutable();
        petPassiveSkill_.remove(index);
        onChanged();
      } else {
        petPassiveSkillBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PetPassiveSkillMsg petPassiveSkill = 5;</code>
     */
    public xddq.pb.PetPassiveSkillMsg.Builder getPetPassiveSkillBuilder(
        int index) {
      return internalGetPetPassiveSkillFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.PetPassiveSkillMsg petPassiveSkill = 5;</code>
     */
    public xddq.pb.PetPassiveSkillMsgOrBuilder getPetPassiveSkillOrBuilder(
        int index) {
      if (petPassiveSkillBuilder_ == null) {
        return petPassiveSkill_.get(index);  } else {
        return petPassiveSkillBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.PetPassiveSkillMsg petPassiveSkill = 5;</code>
     */
    public java.util.List<? extends xddq.pb.PetPassiveSkillMsgOrBuilder> 
         getPetPassiveSkillOrBuilderList() {
      if (petPassiveSkillBuilder_ != null) {
        return petPassiveSkillBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(petPassiveSkill_);
      }
    }
    /**
     * <code>repeated .xddq.pb.PetPassiveSkillMsg petPassiveSkill = 5;</code>
     */
    public xddq.pb.PetPassiveSkillMsg.Builder addPetPassiveSkillBuilder() {
      return internalGetPetPassiveSkillFieldBuilder().addBuilder(
          xddq.pb.PetPassiveSkillMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.PetPassiveSkillMsg petPassiveSkill = 5;</code>
     */
    public xddq.pb.PetPassiveSkillMsg.Builder addPetPassiveSkillBuilder(
        int index) {
      return internalGetPetPassiveSkillFieldBuilder().addBuilder(
          index, xddq.pb.PetPassiveSkillMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.PetPassiveSkillMsg petPassiveSkill = 5;</code>
     */
    public java.util.List<xddq.pb.PetPassiveSkillMsg.Builder> 
         getPetPassiveSkillBuilderList() {
      return internalGetPetPassiveSkillFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.PetPassiveSkillMsg, xddq.pb.PetPassiveSkillMsg.Builder, xddq.pb.PetPassiveSkillMsgOrBuilder> 
        internalGetPetPassiveSkillFieldBuilder() {
      if (petPassiveSkillBuilder_ == null) {
        petPassiveSkillBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.PetPassiveSkillMsg, xddq.pb.PetPassiveSkillMsg.Builder, xddq.pb.PetPassiveSkillMsgOrBuilder>(
                petPassiveSkill_,
                ((bitField0_ & 0x00000010) != 0),
                getParentForChildren(),
                isClean());
        petPassiveSkill_ = null;
      }
      return petPassiveSkillBuilder_;
    }

    private int assistantPetId_ ;
    /**
     * <code>optional int32 assistantPetId = 6;</code>
     * @return Whether the assistantPetId field is set.
     */
    @java.lang.Override
    public boolean hasAssistantPetId() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional int32 assistantPetId = 6;</code>
     * @return The assistantPetId.
     */
    @java.lang.Override
    public int getAssistantPetId() {
      return assistantPetId_;
    }
    /**
     * <code>optional int32 assistantPetId = 6;</code>
     * @param value The assistantPetId to set.
     * @return This builder for chaining.
     */
    public Builder setAssistantPetId(int value) {

      assistantPetId_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 assistantPetId = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearAssistantPetId() {
      bitField0_ = (bitField0_ & ~0x00000020);
      assistantPetId_ = 0;
      onChanged();
      return this;
    }

    private int assistantConfigId_ ;
    /**
     * <code>optional int32 assistantConfigId = 7;</code>
     * @return Whether the assistantConfigId field is set.
     */
    @java.lang.Override
    public boolean hasAssistantConfigId() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <code>optional int32 assistantConfigId = 7;</code>
     * @return The assistantConfigId.
     */
    @java.lang.Override
    public int getAssistantConfigId() {
      return assistantConfigId_;
    }
    /**
     * <code>optional int32 assistantConfigId = 7;</code>
     * @param value The assistantConfigId to set.
     * @return This builder for chaining.
     */
    public Builder setAssistantConfigId(int value) {

      assistantConfigId_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 assistantConfigId = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearAssistantConfigId() {
      bitField0_ = (bitField0_ & ~0x00000040);
      assistantConfigId_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.PetPassiveSkillMsg> petSoulShapePassiveSkill_ =
      java.util.Collections.emptyList();
    private void ensurePetSoulShapePassiveSkillIsMutable() {
      if (!((bitField0_ & 0x00000080) != 0)) {
        petSoulShapePassiveSkill_ = new java.util.ArrayList<xddq.pb.PetPassiveSkillMsg>(petSoulShapePassiveSkill_);
        bitField0_ |= 0x00000080;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.PetPassiveSkillMsg, xddq.pb.PetPassiveSkillMsg.Builder, xddq.pb.PetPassiveSkillMsgOrBuilder> petSoulShapePassiveSkillBuilder_;

    /**
     * <code>repeated .xddq.pb.PetPassiveSkillMsg petSoulShapePassiveSkill = 8;</code>
     */
    public java.util.List<xddq.pb.PetPassiveSkillMsg> getPetSoulShapePassiveSkillList() {
      if (petSoulShapePassiveSkillBuilder_ == null) {
        return java.util.Collections.unmodifiableList(petSoulShapePassiveSkill_);
      } else {
        return petSoulShapePassiveSkillBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.PetPassiveSkillMsg petSoulShapePassiveSkill = 8;</code>
     */
    public int getPetSoulShapePassiveSkillCount() {
      if (petSoulShapePassiveSkillBuilder_ == null) {
        return petSoulShapePassiveSkill_.size();
      } else {
        return petSoulShapePassiveSkillBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.PetPassiveSkillMsg petSoulShapePassiveSkill = 8;</code>
     */
    public xddq.pb.PetPassiveSkillMsg getPetSoulShapePassiveSkill(int index) {
      if (petSoulShapePassiveSkillBuilder_ == null) {
        return petSoulShapePassiveSkill_.get(index);
      } else {
        return petSoulShapePassiveSkillBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.PetPassiveSkillMsg petSoulShapePassiveSkill = 8;</code>
     */
    public Builder setPetSoulShapePassiveSkill(
        int index, xddq.pb.PetPassiveSkillMsg value) {
      if (petSoulShapePassiveSkillBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePetSoulShapePassiveSkillIsMutable();
        petSoulShapePassiveSkill_.set(index, value);
        onChanged();
      } else {
        petSoulShapePassiveSkillBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PetPassiveSkillMsg petSoulShapePassiveSkill = 8;</code>
     */
    public Builder setPetSoulShapePassiveSkill(
        int index, xddq.pb.PetPassiveSkillMsg.Builder builderForValue) {
      if (petSoulShapePassiveSkillBuilder_ == null) {
        ensurePetSoulShapePassiveSkillIsMutable();
        petSoulShapePassiveSkill_.set(index, builderForValue.build());
        onChanged();
      } else {
        petSoulShapePassiveSkillBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PetPassiveSkillMsg petSoulShapePassiveSkill = 8;</code>
     */
    public Builder addPetSoulShapePassiveSkill(xddq.pb.PetPassiveSkillMsg value) {
      if (petSoulShapePassiveSkillBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePetSoulShapePassiveSkillIsMutable();
        petSoulShapePassiveSkill_.add(value);
        onChanged();
      } else {
        petSoulShapePassiveSkillBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PetPassiveSkillMsg petSoulShapePassiveSkill = 8;</code>
     */
    public Builder addPetSoulShapePassiveSkill(
        int index, xddq.pb.PetPassiveSkillMsg value) {
      if (petSoulShapePassiveSkillBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePetSoulShapePassiveSkillIsMutable();
        petSoulShapePassiveSkill_.add(index, value);
        onChanged();
      } else {
        petSoulShapePassiveSkillBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PetPassiveSkillMsg petSoulShapePassiveSkill = 8;</code>
     */
    public Builder addPetSoulShapePassiveSkill(
        xddq.pb.PetPassiveSkillMsg.Builder builderForValue) {
      if (petSoulShapePassiveSkillBuilder_ == null) {
        ensurePetSoulShapePassiveSkillIsMutable();
        petSoulShapePassiveSkill_.add(builderForValue.build());
        onChanged();
      } else {
        petSoulShapePassiveSkillBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PetPassiveSkillMsg petSoulShapePassiveSkill = 8;</code>
     */
    public Builder addPetSoulShapePassiveSkill(
        int index, xddq.pb.PetPassiveSkillMsg.Builder builderForValue) {
      if (petSoulShapePassiveSkillBuilder_ == null) {
        ensurePetSoulShapePassiveSkillIsMutable();
        petSoulShapePassiveSkill_.add(index, builderForValue.build());
        onChanged();
      } else {
        petSoulShapePassiveSkillBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PetPassiveSkillMsg petSoulShapePassiveSkill = 8;</code>
     */
    public Builder addAllPetSoulShapePassiveSkill(
        java.lang.Iterable<? extends xddq.pb.PetPassiveSkillMsg> values) {
      if (petSoulShapePassiveSkillBuilder_ == null) {
        ensurePetSoulShapePassiveSkillIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, petSoulShapePassiveSkill_);
        onChanged();
      } else {
        petSoulShapePassiveSkillBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PetPassiveSkillMsg petSoulShapePassiveSkill = 8;</code>
     */
    public Builder clearPetSoulShapePassiveSkill() {
      if (petSoulShapePassiveSkillBuilder_ == null) {
        petSoulShapePassiveSkill_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000080);
        onChanged();
      } else {
        petSoulShapePassiveSkillBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PetPassiveSkillMsg petSoulShapePassiveSkill = 8;</code>
     */
    public Builder removePetSoulShapePassiveSkill(int index) {
      if (petSoulShapePassiveSkillBuilder_ == null) {
        ensurePetSoulShapePassiveSkillIsMutable();
        petSoulShapePassiveSkill_.remove(index);
        onChanged();
      } else {
        petSoulShapePassiveSkillBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PetPassiveSkillMsg petSoulShapePassiveSkill = 8;</code>
     */
    public xddq.pb.PetPassiveSkillMsg.Builder getPetSoulShapePassiveSkillBuilder(
        int index) {
      return internalGetPetSoulShapePassiveSkillFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.PetPassiveSkillMsg petSoulShapePassiveSkill = 8;</code>
     */
    public xddq.pb.PetPassiveSkillMsgOrBuilder getPetSoulShapePassiveSkillOrBuilder(
        int index) {
      if (petSoulShapePassiveSkillBuilder_ == null) {
        return petSoulShapePassiveSkill_.get(index);  } else {
        return petSoulShapePassiveSkillBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.PetPassiveSkillMsg petSoulShapePassiveSkill = 8;</code>
     */
    public java.util.List<? extends xddq.pb.PetPassiveSkillMsgOrBuilder> 
         getPetSoulShapePassiveSkillOrBuilderList() {
      if (petSoulShapePassiveSkillBuilder_ != null) {
        return petSoulShapePassiveSkillBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(petSoulShapePassiveSkill_);
      }
    }
    /**
     * <code>repeated .xddq.pb.PetPassiveSkillMsg petSoulShapePassiveSkill = 8;</code>
     */
    public xddq.pb.PetPassiveSkillMsg.Builder addPetSoulShapePassiveSkillBuilder() {
      return internalGetPetSoulShapePassiveSkillFieldBuilder().addBuilder(
          xddq.pb.PetPassiveSkillMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.PetPassiveSkillMsg petSoulShapePassiveSkill = 8;</code>
     */
    public xddq.pb.PetPassiveSkillMsg.Builder addPetSoulShapePassiveSkillBuilder(
        int index) {
      return internalGetPetSoulShapePassiveSkillFieldBuilder().addBuilder(
          index, xddq.pb.PetPassiveSkillMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.PetPassiveSkillMsg petSoulShapePassiveSkill = 8;</code>
     */
    public java.util.List<xddq.pb.PetPassiveSkillMsg.Builder> 
         getPetSoulShapePassiveSkillBuilderList() {
      return internalGetPetSoulShapePassiveSkillFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.PetPassiveSkillMsg, xddq.pb.PetPassiveSkillMsg.Builder, xddq.pb.PetPassiveSkillMsgOrBuilder> 
        internalGetPetSoulShapePassiveSkillFieldBuilder() {
      if (petSoulShapePassiveSkillBuilder_ == null) {
        petSoulShapePassiveSkillBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.PetPassiveSkillMsg, xddq.pb.PetPassiveSkillMsg.Builder, xddq.pb.PetPassiveSkillMsgOrBuilder>(
                petSoulShapePassiveSkill_,
                ((bitField0_ & 0x00000080) != 0),
                getParentForChildren(),
                isClean());
        petSoulShapePassiveSkill_ = null;
      }
      return petSoulShapePassiveSkillBuilder_;
    }

    private com.google.protobuf.Internal.IntList waitSelectSkill_ = emptyIntList();
    private void ensureWaitSelectSkillIsMutable() {
      if (!waitSelectSkill_.isModifiable()) {
        waitSelectSkill_ = makeMutableCopy(waitSelectSkill_);
      }
      bitField0_ |= 0x00000100;
    }
    /**
     * <code>repeated int32 waitSelectSkill = 9;</code>
     * @return A list containing the waitSelectSkill.
     */
    public java.util.List<java.lang.Integer>
        getWaitSelectSkillList() {
      waitSelectSkill_.makeImmutable();
      return waitSelectSkill_;
    }
    /**
     * <code>repeated int32 waitSelectSkill = 9;</code>
     * @return The count of waitSelectSkill.
     */
    public int getWaitSelectSkillCount() {
      return waitSelectSkill_.size();
    }
    /**
     * <code>repeated int32 waitSelectSkill = 9;</code>
     * @param index The index of the element to return.
     * @return The waitSelectSkill at the given index.
     */
    public int getWaitSelectSkill(int index) {
      return waitSelectSkill_.getInt(index);
    }
    /**
     * <code>repeated int32 waitSelectSkill = 9;</code>
     * @param index The index to set the value at.
     * @param value The waitSelectSkill to set.
     * @return This builder for chaining.
     */
    public Builder setWaitSelectSkill(
        int index, int value) {

      ensureWaitSelectSkillIsMutable();
      waitSelectSkill_.setInt(index, value);
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 waitSelectSkill = 9;</code>
     * @param value The waitSelectSkill to add.
     * @return This builder for chaining.
     */
    public Builder addWaitSelectSkill(int value) {

      ensureWaitSelectSkillIsMutable();
      waitSelectSkill_.addInt(value);
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 waitSelectSkill = 9;</code>
     * @param values The waitSelectSkill to add.
     * @return This builder for chaining.
     */
    public Builder addAllWaitSelectSkill(
        java.lang.Iterable<? extends java.lang.Integer> values) {
      ensureWaitSelectSkillIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, waitSelectSkill_);
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 waitSelectSkill = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearWaitSelectSkill() {
      waitSelectSkill_ = emptyIntList();
      bitField0_ = (bitField0_ & ~0x00000100);
      onChanged();
      return this;
    }

    private int soulShapeLv_ ;
    /**
     * <code>optional int32 soulShapeLv = 10;</code>
     * @return Whether the soulShapeLv field is set.
     */
    @java.lang.Override
    public boolean hasSoulShapeLv() {
      return ((bitField0_ & 0x00000200) != 0);
    }
    /**
     * <code>optional int32 soulShapeLv = 10;</code>
     * @return The soulShapeLv.
     */
    @java.lang.Override
    public int getSoulShapeLv() {
      return soulShapeLv_;
    }
    /**
     * <code>optional int32 soulShapeLv = 10;</code>
     * @param value The soulShapeLv to set.
     * @return This builder for chaining.
     */
    public Builder setSoulShapeLv(int value) {

      soulShapeLv_ = value;
      bitField0_ |= 0x00000200;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 soulShapeLv = 10;</code>
     * @return This builder for chaining.
     */
    public Builder clearSoulShapeLv() {
      bitField0_ = (bitField0_ & ~0x00000200);
      soulShapeLv_ = 0;
      onChanged();
      return this;
    }

    private int assistantPetSoulShapeLv_ ;
    /**
     * <code>optional int32 assistantPetSoulShapeLv = 11;</code>
     * @return Whether the assistantPetSoulShapeLv field is set.
     */
    @java.lang.Override
    public boolean hasAssistantPetSoulShapeLv() {
      return ((bitField0_ & 0x00000400) != 0);
    }
    /**
     * <code>optional int32 assistantPetSoulShapeLv = 11;</code>
     * @return The assistantPetSoulShapeLv.
     */
    @java.lang.Override
    public int getAssistantPetSoulShapeLv() {
      return assistantPetSoulShapeLv_;
    }
    /**
     * <code>optional int32 assistantPetSoulShapeLv = 11;</code>
     * @param value The assistantPetSoulShapeLv to set.
     * @return This builder for chaining.
     */
    public Builder setAssistantPetSoulShapeLv(int value) {

      assistantPetSoulShapeLv_ = value;
      bitField0_ |= 0x00000400;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 assistantPetSoulShapeLv = 11;</code>
     * @return This builder for chaining.
     */
    public Builder clearAssistantPetSoulShapeLv() {
      bitField0_ = (bitField0_ & ~0x00000400);
      assistantPetSoulShapeLv_ = 0;
      onChanged();
      return this;
    }

    private int haveWaitRefreshSkill_ ;
    /**
     * <code>optional int32 haveWaitRefreshSkill = 12;</code>
     * @return Whether the haveWaitRefreshSkill field is set.
     */
    @java.lang.Override
    public boolean hasHaveWaitRefreshSkill() {
      return ((bitField0_ & 0x00000800) != 0);
    }
    /**
     * <code>optional int32 haveWaitRefreshSkill = 12;</code>
     * @return The haveWaitRefreshSkill.
     */
    @java.lang.Override
    public int getHaveWaitRefreshSkill() {
      return haveWaitRefreshSkill_;
    }
    /**
     * <code>optional int32 haveWaitRefreshSkill = 12;</code>
     * @param value The haveWaitRefreshSkill to set.
     * @return This builder for chaining.
     */
    public Builder setHaveWaitRefreshSkill(int value) {

      haveWaitRefreshSkill_ = value;
      bitField0_ |= 0x00000800;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 haveWaitRefreshSkill = 12;</code>
     * @return This builder for chaining.
     */
    public Builder clearHaveWaitRefreshSkill() {
      bitField0_ = (bitField0_ & ~0x00000800);
      haveWaitRefreshSkill_ = 0;
      onChanged();
      return this;
    }

    private int lockState_ ;
    /**
     * <code>optional int32 lockState = 13;</code>
     * @return Whether the lockState field is set.
     */
    @java.lang.Override
    public boolean hasLockState() {
      return ((bitField0_ & 0x00001000) != 0);
    }
    /**
     * <code>optional int32 lockState = 13;</code>
     * @return The lockState.
     */
    @java.lang.Override
    public int getLockState() {
      return lockState_;
    }
    /**
     * <code>optional int32 lockState = 13;</code>
     * @param value The lockState to set.
     * @return This builder for chaining.
     */
    public Builder setLockState(int value) {

      lockState_ = value;
      bitField0_ |= 0x00001000;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 lockState = 13;</code>
     * @return This builder for chaining.
     */
    public Builder clearLockState() {
      bitField0_ = (bitField0_ & ~0x00001000);
      lockState_ = 0;
      onChanged();
      return this;
    }

    private int equipLinkageId_ ;
    /**
     * <code>optional int32 equipLinkageId = 14;</code>
     * @return Whether the equipLinkageId field is set.
     */
    @java.lang.Override
    public boolean hasEquipLinkageId() {
      return ((bitField0_ & 0x00002000) != 0);
    }
    /**
     * <code>optional int32 equipLinkageId = 14;</code>
     * @return The equipLinkageId.
     */
    @java.lang.Override
    public int getEquipLinkageId() {
      return equipLinkageId_;
    }
    /**
     * <code>optional int32 equipLinkageId = 14;</code>
     * @param value The equipLinkageId to set.
     * @return This builder for chaining.
     */
    public Builder setEquipLinkageId(int value) {

      equipLinkageId_ = value;
      bitField0_ |= 0x00002000;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 equipLinkageId = 14;</code>
     * @return This builder for chaining.
     */
    public Builder clearEquipLinkageId() {
      bitField0_ = (bitField0_ & ~0x00002000);
      equipLinkageId_ = 0;
      onChanged();
      return this;
    }

    private int synergyLinkageId_ ;
    /**
     * <code>optional int32 synergyLinkageId = 15;</code>
     * @return Whether the synergyLinkageId field is set.
     */
    @java.lang.Override
    public boolean hasSynergyLinkageId() {
      return ((bitField0_ & 0x00004000) != 0);
    }
    /**
     * <code>optional int32 synergyLinkageId = 15;</code>
     * @return The synergyLinkageId.
     */
    @java.lang.Override
    public int getSynergyLinkageId() {
      return synergyLinkageId_;
    }
    /**
     * <code>optional int32 synergyLinkageId = 15;</code>
     * @param value The synergyLinkageId to set.
     * @return This builder for chaining.
     */
    public Builder setSynergyLinkageId(int value) {

      synergyLinkageId_ = value;
      bitField0_ |= 0x00004000;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 synergyLinkageId = 15;</code>
     * @return This builder for chaining.
     */
    public Builder clearSynergyLinkageId() {
      bitField0_ = (bitField0_ & ~0x00004000);
      synergyLinkageId_ = 0;
      onChanged();
      return this;
    }

    private int kernelLv_ ;
    /**
     * <code>optional int32 kernelLv = 16;</code>
     * @return Whether the kernelLv field is set.
     */
    @java.lang.Override
    public boolean hasKernelLv() {
      return ((bitField0_ & 0x00008000) != 0);
    }
    /**
     * <code>optional int32 kernelLv = 16;</code>
     * @return The kernelLv.
     */
    @java.lang.Override
    public int getKernelLv() {
      return kernelLv_;
    }
    /**
     * <code>optional int32 kernelLv = 16;</code>
     * @param value The kernelLv to set.
     * @return This builder for chaining.
     */
    public Builder setKernelLv(int value) {

      kernelLv_ = value;
      bitField0_ |= 0x00008000;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 kernelLv = 16;</code>
     * @return This builder for chaining.
     */
    public Builder clearKernelLv() {
      bitField0_ = (bitField0_ & ~0x00008000);
      kernelLv_ = 0;
      onChanged();
      return this;
    }

    private com.google.protobuf.Internal.IntList equipKernelId_ = emptyIntList();
    private void ensureEquipKernelIdIsMutable() {
      if (!equipKernelId_.isModifiable()) {
        equipKernelId_ = makeMutableCopy(equipKernelId_);
      }
      bitField0_ |= 0x00010000;
    }
    /**
     * <code>repeated int32 equipKernelId = 17;</code>
     * @return A list containing the equipKernelId.
     */
    public java.util.List<java.lang.Integer>
        getEquipKernelIdList() {
      equipKernelId_.makeImmutable();
      return equipKernelId_;
    }
    /**
     * <code>repeated int32 equipKernelId = 17;</code>
     * @return The count of equipKernelId.
     */
    public int getEquipKernelIdCount() {
      return equipKernelId_.size();
    }
    /**
     * <code>repeated int32 equipKernelId = 17;</code>
     * @param index The index of the element to return.
     * @return The equipKernelId at the given index.
     */
    public int getEquipKernelId(int index) {
      return equipKernelId_.getInt(index);
    }
    /**
     * <code>repeated int32 equipKernelId = 17;</code>
     * @param index The index to set the value at.
     * @param value The equipKernelId to set.
     * @return This builder for chaining.
     */
    public Builder setEquipKernelId(
        int index, int value) {

      ensureEquipKernelIdIsMutable();
      equipKernelId_.setInt(index, value);
      bitField0_ |= 0x00010000;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 equipKernelId = 17;</code>
     * @param value The equipKernelId to add.
     * @return This builder for chaining.
     */
    public Builder addEquipKernelId(int value) {

      ensureEquipKernelIdIsMutable();
      equipKernelId_.addInt(value);
      bitField0_ |= 0x00010000;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 equipKernelId = 17;</code>
     * @param values The equipKernelId to add.
     * @return This builder for chaining.
     */
    public Builder addAllEquipKernelId(
        java.lang.Iterable<? extends java.lang.Integer> values) {
      ensureEquipKernelIdIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, equipKernelId_);
      bitField0_ |= 0x00010000;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 equipKernelId = 17;</code>
     * @return This builder for chaining.
     */
    public Builder clearEquipKernelId() {
      equipKernelId_ = emptyIntList();
      bitField0_ = (bitField0_ & ~0x00010000);
      onChanged();
      return this;
    }

    private int rootLv_ ;
    /**
     * <code>optional int32 rootLv = 18;</code>
     * @return Whether the rootLv field is set.
     */
    @java.lang.Override
    public boolean hasRootLv() {
      return ((bitField0_ & 0x00020000) != 0);
    }
    /**
     * <code>optional int32 rootLv = 18;</code>
     * @return The rootLv.
     */
    @java.lang.Override
    public int getRootLv() {
      return rootLv_;
    }
    /**
     * <code>optional int32 rootLv = 18;</code>
     * @param value The rootLv to set.
     * @return This builder for chaining.
     */
    public Builder setRootLv(int value) {

      rootLv_ = value;
      bitField0_ |= 0x00020000;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 rootLv = 18;</code>
     * @return This builder for chaining.
     */
    public Builder clearRootLv() {
      bitField0_ = (bitField0_ & ~0x00020000);
      rootLv_ = 0;
      onChanged();
      return this;
    }

    private int awakeLv_ ;
    /**
     * <code>optional int32 awakeLv = 19;</code>
     * @return Whether the awakeLv field is set.
     */
    @java.lang.Override
    public boolean hasAwakeLv() {
      return ((bitField0_ & 0x00040000) != 0);
    }
    /**
     * <code>optional int32 awakeLv = 19;</code>
     * @return The awakeLv.
     */
    @java.lang.Override
    public int getAwakeLv() {
      return awakeLv_;
    }
    /**
     * <code>optional int32 awakeLv = 19;</code>
     * @param value The awakeLv to set.
     * @return This builder for chaining.
     */
    public Builder setAwakeLv(int value) {

      awakeLv_ = value;
      bitField0_ |= 0x00040000;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 awakeLv = 19;</code>
     * @return This builder for chaining.
     */
    public Builder clearAwakeLv() {
      bitField0_ = (bitField0_ & ~0x00040000);
      awakeLv_ = 0;
      onChanged();
      return this;
    }

    private int stageLv_ ;
    /**
     * <code>optional int32 stageLv = 20;</code>
     * @return Whether the stageLv field is set.
     */
    @java.lang.Override
    public boolean hasStageLv() {
      return ((bitField0_ & 0x00080000) != 0);
    }
    /**
     * <code>optional int32 stageLv = 20;</code>
     * @return The stageLv.
     */
    @java.lang.Override
    public int getStageLv() {
      return stageLv_;
    }
    /**
     * <code>optional int32 stageLv = 20;</code>
     * @param value The stageLv to set.
     * @return This builder for chaining.
     */
    public Builder setStageLv(int value) {

      stageLv_ = value;
      bitField0_ |= 0x00080000;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 stageLv = 20;</code>
     * @return This builder for chaining.
     */
    public Builder clearStageLv() {
      bitField0_ = (bitField0_ & ~0x00080000);
      stageLv_ = 0;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.PetDataMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.PetDataMsg)
  private static final xddq.pb.PetDataMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.PetDataMsg();
  }

  public static xddq.pb.PetDataMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<PetDataMsg>
      PARSER = new com.google.protobuf.AbstractParser<PetDataMsg>() {
    @java.lang.Override
    public PetDataMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<PetDataMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<PetDataMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.PetDataMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

