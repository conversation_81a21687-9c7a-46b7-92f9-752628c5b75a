// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

public interface ElementalBondsSyncNoticeRespMsgOrBuilder extends
    // @@protoc_insertion_point(interface_extends:xddq.pb.ElementalBondsSyncNoticeRespMsg)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>required int32 type = 1;</code>
   * @return Whether the type field is set.
   */
  boolean hasType();
  /**
   * <code>required int32 type = 1;</code>
   * @return The type.
   */
  int getType();

  /**
   * <code>optional int64 dropLineWaitTime = 2;</code>
   * @return Whether the dropLineWaitTime field is set.
   */
  boolean hasDropLineWaitTime();
  /**
   * <code>optional int64 dropLineWaitTime = 2;</code>
   * @return The dropLineWaitTime.
   */
  long getDropLineWaitTime();

  /**
   * <code>optional int64 expireTime = 3;</code>
   * @return Whether the expireTime field is set.
   */
  boolean hasExpireTime();
  /**
   * <code>optional int64 expireTime = 3;</code>
   * @return The expireTime.
   */
  long getExpireTime();

  /**
   * <code>optional int32 expressionId = 4;</code>
   * @return Whether the expressionId field is set.
   */
  boolean hasExpressionId();
  /**
   * <code>optional int32 expressionId = 4;</code>
   * @return The expressionId.
   */
  int getExpressionId();

  /**
   * <code>optional .xddq.pb.ElementalBondsGameOverMsg over = 5;</code>
   * @return Whether the over field is set.
   */
  boolean hasOver();
  /**
   * <code>optional .xddq.pb.ElementalBondsGameOverMsg over = 5;</code>
   * @return The over.
   */
  xddq.pb.ElementalBondsGameOverMsg getOver();
  /**
   * <code>optional .xddq.pb.ElementalBondsGameOverMsg over = 5;</code>
   */
  xddq.pb.ElementalBondsGameOverMsgOrBuilder getOverOrBuilder();

  /**
   * <code>optional .xddq.pb.ElementalBondsOperateResultMsg operate = 6;</code>
   * @return Whether the operate field is set.
   */
  boolean hasOperate();
  /**
   * <code>optional .xddq.pb.ElementalBondsOperateResultMsg operate = 6;</code>
   * @return The operate.
   */
  xddq.pb.ElementalBondsOperateResultMsg getOperate();
  /**
   * <code>optional .xddq.pb.ElementalBondsOperateResultMsg operate = 6;</code>
   */
  xddq.pb.ElementalBondsOperateResultMsgOrBuilder getOperateOrBuilder();
}
