// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

public interface ElementalBondsGetHistoryChampionRespMsgOrBuilder extends
    // @@protoc_insertion_point(interface_extends:xddq.pb.ElementalBondsGetHistoryChampionRespMsg)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  boolean hasRet();
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  int getRet();

  /**
   * <code>repeated .xddq.pb.ElementalBondsChampionMsg champion = 2;</code>
   */
  java.util.List<xddq.pb.ElementalBondsChampionMsg> 
      getChampionList();
  /**
   * <code>repeated .xddq.pb.ElementalBondsChampionMsg champion = 2;</code>
   */
  xddq.pb.ElementalBondsChampionMsg getChampion(int index);
  /**
   * <code>repeated .xddq.pb.ElementalBondsChampionMsg champion = 2;</code>
   */
  int getChampionCount();
  /**
   * <code>repeated .xddq.pb.ElementalBondsChampionMsg champion = 2;</code>
   */
  java.util.List<? extends xddq.pb.ElementalBondsChampionMsgOrBuilder> 
      getChampionOrBuilderList();
  /**
   * <code>repeated .xddq.pb.ElementalBondsChampionMsg champion = 2;</code>
   */
  xddq.pb.ElementalBondsChampionMsgOrBuilder getChampionOrBuilder(
      int index);
}
