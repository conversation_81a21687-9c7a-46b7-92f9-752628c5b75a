// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.AssistantSkinUpgradeResp}
 */
public final class AssistantSkinUpgradeResp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.AssistantSkinUpgradeResp)
    AssistantSkinUpgradeRespOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      AssistantSkinUpgradeResp.class.getName());
  }
  // Use AssistantSkinUpgradeResp.newBuilder() to construct.
  private AssistantSkinUpgradeResp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private AssistantSkinUpgradeResp() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_AssistantSkinUpgradeResp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_AssistantSkinUpgradeResp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.AssistantSkinUpgradeResp.class, xddq.pb.AssistantSkinUpgradeResp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int ASSISTANTSKINMSG_FIELD_NUMBER = 2;
  private xddq.pb.AssistantSkinMsg assistantSkinMsg_;
  /**
   * <code>optional .xddq.pb.AssistantSkinMsg assistantSkinMsg = 2;</code>
   * @return Whether the assistantSkinMsg field is set.
   */
  @java.lang.Override
  public boolean hasAssistantSkinMsg() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional .xddq.pb.AssistantSkinMsg assistantSkinMsg = 2;</code>
   * @return The assistantSkinMsg.
   */
  @java.lang.Override
  public xddq.pb.AssistantSkinMsg getAssistantSkinMsg() {
    return assistantSkinMsg_ == null ? xddq.pb.AssistantSkinMsg.getDefaultInstance() : assistantSkinMsg_;
  }
  /**
   * <code>optional .xddq.pb.AssistantSkinMsg assistantSkinMsg = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.AssistantSkinMsgOrBuilder getAssistantSkinMsgOrBuilder() {
    return assistantSkinMsg_ == null ? xddq.pb.AssistantSkinMsg.getDefaultInstance() : assistantSkinMsg_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (hasAssistantSkinMsg()) {
      if (!getAssistantSkinMsg().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeMessage(2, getAssistantSkinMsg());
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getAssistantSkinMsg());
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.AssistantSkinUpgradeResp)) {
      return super.equals(obj);
    }
    xddq.pb.AssistantSkinUpgradeResp other = (xddq.pb.AssistantSkinUpgradeResp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (hasAssistantSkinMsg() != other.hasAssistantSkinMsg()) return false;
    if (hasAssistantSkinMsg()) {
      if (!getAssistantSkinMsg()
          .equals(other.getAssistantSkinMsg())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (hasAssistantSkinMsg()) {
      hash = (37 * hash) + ASSISTANTSKINMSG_FIELD_NUMBER;
      hash = (53 * hash) + getAssistantSkinMsg().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.AssistantSkinUpgradeResp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.AssistantSkinUpgradeResp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.AssistantSkinUpgradeResp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.AssistantSkinUpgradeResp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.AssistantSkinUpgradeResp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.AssistantSkinUpgradeResp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.AssistantSkinUpgradeResp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.AssistantSkinUpgradeResp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.AssistantSkinUpgradeResp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.AssistantSkinUpgradeResp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.AssistantSkinUpgradeResp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.AssistantSkinUpgradeResp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.AssistantSkinUpgradeResp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.AssistantSkinUpgradeResp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.AssistantSkinUpgradeResp)
      xddq.pb.AssistantSkinUpgradeRespOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_AssistantSkinUpgradeResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_AssistantSkinUpgradeResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.AssistantSkinUpgradeResp.class, xddq.pb.AssistantSkinUpgradeResp.Builder.class);
    }

    // Construct using xddq.pb.AssistantSkinUpgradeResp.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetAssistantSkinMsgFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      assistantSkinMsg_ = null;
      if (assistantSkinMsgBuilder_ != null) {
        assistantSkinMsgBuilder_.dispose();
        assistantSkinMsgBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_AssistantSkinUpgradeResp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.AssistantSkinUpgradeResp getDefaultInstanceForType() {
      return xddq.pb.AssistantSkinUpgradeResp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.AssistantSkinUpgradeResp build() {
      xddq.pb.AssistantSkinUpgradeResp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.AssistantSkinUpgradeResp buildPartial() {
      xddq.pb.AssistantSkinUpgradeResp result = new xddq.pb.AssistantSkinUpgradeResp(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.AssistantSkinUpgradeResp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.assistantSkinMsg_ = assistantSkinMsgBuilder_ == null
            ? assistantSkinMsg_
            : assistantSkinMsgBuilder_.build();
        to_bitField0_ |= 0x00000002;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.AssistantSkinUpgradeResp) {
        return mergeFrom((xddq.pb.AssistantSkinUpgradeResp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.AssistantSkinUpgradeResp other) {
      if (other == xddq.pb.AssistantSkinUpgradeResp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (other.hasAssistantSkinMsg()) {
        mergeAssistantSkinMsg(other.getAssistantSkinMsg());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      if (hasAssistantSkinMsg()) {
        if (!getAssistantSkinMsg().isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              input.readMessage(
                  internalGetAssistantSkinMsgFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private xddq.pb.AssistantSkinMsg assistantSkinMsg_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.AssistantSkinMsg, xddq.pb.AssistantSkinMsg.Builder, xddq.pb.AssistantSkinMsgOrBuilder> assistantSkinMsgBuilder_;
    /**
     * <code>optional .xddq.pb.AssistantSkinMsg assistantSkinMsg = 2;</code>
     * @return Whether the assistantSkinMsg field is set.
     */
    public boolean hasAssistantSkinMsg() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .xddq.pb.AssistantSkinMsg assistantSkinMsg = 2;</code>
     * @return The assistantSkinMsg.
     */
    public xddq.pb.AssistantSkinMsg getAssistantSkinMsg() {
      if (assistantSkinMsgBuilder_ == null) {
        return assistantSkinMsg_ == null ? xddq.pb.AssistantSkinMsg.getDefaultInstance() : assistantSkinMsg_;
      } else {
        return assistantSkinMsgBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.AssistantSkinMsg assistantSkinMsg = 2;</code>
     */
    public Builder setAssistantSkinMsg(xddq.pb.AssistantSkinMsg value) {
      if (assistantSkinMsgBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        assistantSkinMsg_ = value;
      } else {
        assistantSkinMsgBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.AssistantSkinMsg assistantSkinMsg = 2;</code>
     */
    public Builder setAssistantSkinMsg(
        xddq.pb.AssistantSkinMsg.Builder builderForValue) {
      if (assistantSkinMsgBuilder_ == null) {
        assistantSkinMsg_ = builderForValue.build();
      } else {
        assistantSkinMsgBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.AssistantSkinMsg assistantSkinMsg = 2;</code>
     */
    public Builder mergeAssistantSkinMsg(xddq.pb.AssistantSkinMsg value) {
      if (assistantSkinMsgBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0) &&
          assistantSkinMsg_ != null &&
          assistantSkinMsg_ != xddq.pb.AssistantSkinMsg.getDefaultInstance()) {
          getAssistantSkinMsgBuilder().mergeFrom(value);
        } else {
          assistantSkinMsg_ = value;
        }
      } else {
        assistantSkinMsgBuilder_.mergeFrom(value);
      }
      if (assistantSkinMsg_ != null) {
        bitField0_ |= 0x00000002;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.AssistantSkinMsg assistantSkinMsg = 2;</code>
     */
    public Builder clearAssistantSkinMsg() {
      bitField0_ = (bitField0_ & ~0x00000002);
      assistantSkinMsg_ = null;
      if (assistantSkinMsgBuilder_ != null) {
        assistantSkinMsgBuilder_.dispose();
        assistantSkinMsgBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.AssistantSkinMsg assistantSkinMsg = 2;</code>
     */
    public xddq.pb.AssistantSkinMsg.Builder getAssistantSkinMsgBuilder() {
      bitField0_ |= 0x00000002;
      onChanged();
      return internalGetAssistantSkinMsgFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.AssistantSkinMsg assistantSkinMsg = 2;</code>
     */
    public xddq.pb.AssistantSkinMsgOrBuilder getAssistantSkinMsgOrBuilder() {
      if (assistantSkinMsgBuilder_ != null) {
        return assistantSkinMsgBuilder_.getMessageOrBuilder();
      } else {
        return assistantSkinMsg_ == null ?
            xddq.pb.AssistantSkinMsg.getDefaultInstance() : assistantSkinMsg_;
      }
    }
    /**
     * <code>optional .xddq.pb.AssistantSkinMsg assistantSkinMsg = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.AssistantSkinMsg, xddq.pb.AssistantSkinMsg.Builder, xddq.pb.AssistantSkinMsgOrBuilder> 
        internalGetAssistantSkinMsgFieldBuilder() {
      if (assistantSkinMsgBuilder_ == null) {
        assistantSkinMsgBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.AssistantSkinMsg, xddq.pb.AssistantSkinMsg.Builder, xddq.pb.AssistantSkinMsgOrBuilder>(
                getAssistantSkinMsg(),
                getParentForChildren(),
                isClean());
        assistantSkinMsg_ = null;
      }
      return assistantSkinMsgBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.AssistantSkinUpgradeResp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.AssistantSkinUpgradeResp)
  private static final xddq.pb.AssistantSkinUpgradeResp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.AssistantSkinUpgradeResp();
  }

  public static xddq.pb.AssistantSkinUpgradeResp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<AssistantSkinUpgradeResp>
      PARSER = new com.google.protobuf.AbstractParser<AssistantSkinUpgradeResp>() {
    @java.lang.Override
    public AssistantSkinUpgradeResp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<AssistantSkinUpgradeResp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<AssistantSkinUpgradeResp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.AssistantSkinUpgradeResp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

