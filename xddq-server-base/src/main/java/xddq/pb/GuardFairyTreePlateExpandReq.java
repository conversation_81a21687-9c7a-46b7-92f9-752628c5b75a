// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.GuardFairyTreePlateExpandReq}
 */
public final class GuardFairyTreePlateExpandReq extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.GuardFairyTreePlateExpandReq)
    GuardFairyTreePlateExpandReqOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      GuardFairyTreePlateExpandReq.class.getName());
  }
  // Use GuardFairyTreePlateExpandReq.newBuilder() to construct.
  private GuardFairyTreePlateExpandReq(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private GuardFairyTreePlateExpandReq() {
    newPlateGridList_ = java.util.Collections.emptyList();
    newPlateGoods_ = java.util.Collections.emptyList();
    goodsList_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_GuardFairyTreePlateExpandReq_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_GuardFairyTreePlateExpandReq_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.GuardFairyTreePlateExpandReq.class, xddq.pb.GuardFairyTreePlateExpandReq.Builder.class);
  }

  private int bitField0_;
  public static final int ACTIVITYID_FIELD_NUMBER = 1;
  private int activityId_ = 0;
  /**
   * <code>required int32 activityId = 1;</code>
   * @return Whether the activityId field is set.
   */
  @java.lang.Override
  public boolean hasActivityId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 activityId = 1;</code>
   * @return The activityId.
   */
  @java.lang.Override
  public int getActivityId() {
    return activityId_;
  }

  public static final int NEWPLATEGRIDLIST_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.GuardFairyTreeGridData> newPlateGridList_;
  /**
   * <code>repeated .xddq.pb.GuardFairyTreeGridData newPlateGridList = 2;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.GuardFairyTreeGridData> getNewPlateGridListList() {
    return newPlateGridList_;
  }
  /**
   * <code>repeated .xddq.pb.GuardFairyTreeGridData newPlateGridList = 2;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.GuardFairyTreeGridDataOrBuilder> 
      getNewPlateGridListOrBuilderList() {
    return newPlateGridList_;
  }
  /**
   * <code>repeated .xddq.pb.GuardFairyTreeGridData newPlateGridList = 2;</code>
   */
  @java.lang.Override
  public int getNewPlateGridListCount() {
    return newPlateGridList_.size();
  }
  /**
   * <code>repeated .xddq.pb.GuardFairyTreeGridData newPlateGridList = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.GuardFairyTreeGridData getNewPlateGridList(int index) {
    return newPlateGridList_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.GuardFairyTreeGridData newPlateGridList = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.GuardFairyTreeGridDataOrBuilder getNewPlateGridListOrBuilder(
      int index) {
    return newPlateGridList_.get(index);
  }

  public static final int NEWPLATEGOODS_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.GuardFairyTreeGoodsItemInfo> newPlateGoods_;
  /**
   * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo newPlateGoods = 3;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.GuardFairyTreeGoodsItemInfo> getNewPlateGoodsList() {
    return newPlateGoods_;
  }
  /**
   * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo newPlateGoods = 3;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.GuardFairyTreeGoodsItemInfoOrBuilder> 
      getNewPlateGoodsOrBuilderList() {
    return newPlateGoods_;
  }
  /**
   * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo newPlateGoods = 3;</code>
   */
  @java.lang.Override
  public int getNewPlateGoodsCount() {
    return newPlateGoods_.size();
  }
  /**
   * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo newPlateGoods = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.GuardFairyTreeGoodsItemInfo getNewPlateGoods(int index) {
    return newPlateGoods_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo newPlateGoods = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.GuardFairyTreeGoodsItemInfoOrBuilder getNewPlateGoodsOrBuilder(
      int index) {
    return newPlateGoods_.get(index);
  }

  public static final int GOODSLIST_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.GuardFairyTreeGoodsItemInfo> goodsList_;
  /**
   * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo goodsList = 4;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.GuardFairyTreeGoodsItemInfo> getGoodsListList() {
    return goodsList_;
  }
  /**
   * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo goodsList = 4;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.GuardFairyTreeGoodsItemInfoOrBuilder> 
      getGoodsListOrBuilderList() {
    return goodsList_;
  }
  /**
   * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo goodsList = 4;</code>
   */
  @java.lang.Override
  public int getGoodsListCount() {
    return goodsList_.size();
  }
  /**
   * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo goodsList = 4;</code>
   */
  @java.lang.Override
  public xddq.pb.GuardFairyTreeGoodsItemInfo getGoodsList(int index) {
    return goodsList_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo goodsList = 4;</code>
   */
  @java.lang.Override
  public xddq.pb.GuardFairyTreeGoodsItemInfoOrBuilder getGoodsListOrBuilder(
      int index) {
    return goodsList_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasActivityId()) {
      memoizedIsInitialized = 0;
      return false;
    }
    for (int i = 0; i < getNewPlateGoodsCount(); i++) {
      if (!getNewPlateGoods(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    for (int i = 0; i < getGoodsListCount(); i++) {
      if (!getGoodsList(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, activityId_);
    }
    for (int i = 0; i < newPlateGridList_.size(); i++) {
      output.writeMessage(2, newPlateGridList_.get(i));
    }
    for (int i = 0; i < newPlateGoods_.size(); i++) {
      output.writeMessage(3, newPlateGoods_.get(i));
    }
    for (int i = 0; i < goodsList_.size(); i++) {
      output.writeMessage(4, goodsList_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, activityId_);
    }
    for (int i = 0; i < newPlateGridList_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, newPlateGridList_.get(i));
    }
    for (int i = 0; i < newPlateGoods_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, newPlateGoods_.get(i));
    }
    for (int i = 0; i < goodsList_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(4, goodsList_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.GuardFairyTreePlateExpandReq)) {
      return super.equals(obj);
    }
    xddq.pb.GuardFairyTreePlateExpandReq other = (xddq.pb.GuardFairyTreePlateExpandReq) obj;

    if (hasActivityId() != other.hasActivityId()) return false;
    if (hasActivityId()) {
      if (getActivityId()
          != other.getActivityId()) return false;
    }
    if (!getNewPlateGridListList()
        .equals(other.getNewPlateGridListList())) return false;
    if (!getNewPlateGoodsList()
        .equals(other.getNewPlateGoodsList())) return false;
    if (!getGoodsListList()
        .equals(other.getGoodsListList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasActivityId()) {
      hash = (37 * hash) + ACTIVITYID_FIELD_NUMBER;
      hash = (53 * hash) + getActivityId();
    }
    if (getNewPlateGridListCount() > 0) {
      hash = (37 * hash) + NEWPLATEGRIDLIST_FIELD_NUMBER;
      hash = (53 * hash) + getNewPlateGridListList().hashCode();
    }
    if (getNewPlateGoodsCount() > 0) {
      hash = (37 * hash) + NEWPLATEGOODS_FIELD_NUMBER;
      hash = (53 * hash) + getNewPlateGoodsList().hashCode();
    }
    if (getGoodsListCount() > 0) {
      hash = (37 * hash) + GOODSLIST_FIELD_NUMBER;
      hash = (53 * hash) + getGoodsListList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.GuardFairyTreePlateExpandReq parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GuardFairyTreePlateExpandReq parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GuardFairyTreePlateExpandReq parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GuardFairyTreePlateExpandReq parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GuardFairyTreePlateExpandReq parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GuardFairyTreePlateExpandReq parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GuardFairyTreePlateExpandReq parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.GuardFairyTreePlateExpandReq parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.GuardFairyTreePlateExpandReq parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.GuardFairyTreePlateExpandReq parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.GuardFairyTreePlateExpandReq parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.GuardFairyTreePlateExpandReq parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.GuardFairyTreePlateExpandReq prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.GuardFairyTreePlateExpandReq}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.GuardFairyTreePlateExpandReq)
      xddq.pb.GuardFairyTreePlateExpandReqOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GuardFairyTreePlateExpandReq_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GuardFairyTreePlateExpandReq_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.GuardFairyTreePlateExpandReq.class, xddq.pb.GuardFairyTreePlateExpandReq.Builder.class);
    }

    // Construct using xddq.pb.GuardFairyTreePlateExpandReq.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      activityId_ = 0;
      if (newPlateGridListBuilder_ == null) {
        newPlateGridList_ = java.util.Collections.emptyList();
      } else {
        newPlateGridList_ = null;
        newPlateGridListBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000002);
      if (newPlateGoodsBuilder_ == null) {
        newPlateGoods_ = java.util.Collections.emptyList();
      } else {
        newPlateGoods_ = null;
        newPlateGoodsBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000004);
      if (goodsListBuilder_ == null) {
        goodsList_ = java.util.Collections.emptyList();
      } else {
        goodsList_ = null;
        goodsListBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000008);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GuardFairyTreePlateExpandReq_descriptor;
    }

    @java.lang.Override
    public xddq.pb.GuardFairyTreePlateExpandReq getDefaultInstanceForType() {
      return xddq.pb.GuardFairyTreePlateExpandReq.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.GuardFairyTreePlateExpandReq build() {
      xddq.pb.GuardFairyTreePlateExpandReq result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.GuardFairyTreePlateExpandReq buildPartial() {
      xddq.pb.GuardFairyTreePlateExpandReq result = new xddq.pb.GuardFairyTreePlateExpandReq(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.GuardFairyTreePlateExpandReq result) {
      if (newPlateGridListBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          newPlateGridList_ = java.util.Collections.unmodifiableList(newPlateGridList_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.newPlateGridList_ = newPlateGridList_;
      } else {
        result.newPlateGridList_ = newPlateGridListBuilder_.build();
      }
      if (newPlateGoodsBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0)) {
          newPlateGoods_ = java.util.Collections.unmodifiableList(newPlateGoods_);
          bitField0_ = (bitField0_ & ~0x00000004);
        }
        result.newPlateGoods_ = newPlateGoods_;
      } else {
        result.newPlateGoods_ = newPlateGoodsBuilder_.build();
      }
      if (goodsListBuilder_ == null) {
        if (((bitField0_ & 0x00000008) != 0)) {
          goodsList_ = java.util.Collections.unmodifiableList(goodsList_);
          bitField0_ = (bitField0_ & ~0x00000008);
        }
        result.goodsList_ = goodsList_;
      } else {
        result.goodsList_ = goodsListBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.GuardFairyTreePlateExpandReq result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.activityId_ = activityId_;
        to_bitField0_ |= 0x00000001;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.GuardFairyTreePlateExpandReq) {
        return mergeFrom((xddq.pb.GuardFairyTreePlateExpandReq)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.GuardFairyTreePlateExpandReq other) {
      if (other == xddq.pb.GuardFairyTreePlateExpandReq.getDefaultInstance()) return this;
      if (other.hasActivityId()) {
        setActivityId(other.getActivityId());
      }
      if (newPlateGridListBuilder_ == null) {
        if (!other.newPlateGridList_.isEmpty()) {
          if (newPlateGridList_.isEmpty()) {
            newPlateGridList_ = other.newPlateGridList_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureNewPlateGridListIsMutable();
            newPlateGridList_.addAll(other.newPlateGridList_);
          }
          onChanged();
        }
      } else {
        if (!other.newPlateGridList_.isEmpty()) {
          if (newPlateGridListBuilder_.isEmpty()) {
            newPlateGridListBuilder_.dispose();
            newPlateGridListBuilder_ = null;
            newPlateGridList_ = other.newPlateGridList_;
            bitField0_ = (bitField0_ & ~0x00000002);
            newPlateGridListBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetNewPlateGridListFieldBuilder() : null;
          } else {
            newPlateGridListBuilder_.addAllMessages(other.newPlateGridList_);
          }
        }
      }
      if (newPlateGoodsBuilder_ == null) {
        if (!other.newPlateGoods_.isEmpty()) {
          if (newPlateGoods_.isEmpty()) {
            newPlateGoods_ = other.newPlateGoods_;
            bitField0_ = (bitField0_ & ~0x00000004);
          } else {
            ensureNewPlateGoodsIsMutable();
            newPlateGoods_.addAll(other.newPlateGoods_);
          }
          onChanged();
        }
      } else {
        if (!other.newPlateGoods_.isEmpty()) {
          if (newPlateGoodsBuilder_.isEmpty()) {
            newPlateGoodsBuilder_.dispose();
            newPlateGoodsBuilder_ = null;
            newPlateGoods_ = other.newPlateGoods_;
            bitField0_ = (bitField0_ & ~0x00000004);
            newPlateGoodsBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetNewPlateGoodsFieldBuilder() : null;
          } else {
            newPlateGoodsBuilder_.addAllMessages(other.newPlateGoods_);
          }
        }
      }
      if (goodsListBuilder_ == null) {
        if (!other.goodsList_.isEmpty()) {
          if (goodsList_.isEmpty()) {
            goodsList_ = other.goodsList_;
            bitField0_ = (bitField0_ & ~0x00000008);
          } else {
            ensureGoodsListIsMutable();
            goodsList_.addAll(other.goodsList_);
          }
          onChanged();
        }
      } else {
        if (!other.goodsList_.isEmpty()) {
          if (goodsListBuilder_.isEmpty()) {
            goodsListBuilder_.dispose();
            goodsListBuilder_ = null;
            goodsList_ = other.goodsList_;
            bitField0_ = (bitField0_ & ~0x00000008);
            goodsListBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetGoodsListFieldBuilder() : null;
          } else {
            goodsListBuilder_.addAllMessages(other.goodsList_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasActivityId()) {
        return false;
      }
      for (int i = 0; i < getNewPlateGoodsCount(); i++) {
        if (!getNewPlateGoods(i).isInitialized()) {
          return false;
        }
      }
      for (int i = 0; i < getGoodsListCount(); i++) {
        if (!getGoodsList(i).isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              activityId_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              xddq.pb.GuardFairyTreeGridData m =
                  input.readMessage(
                      xddq.pb.GuardFairyTreeGridData.parser(),
                      extensionRegistry);
              if (newPlateGridListBuilder_ == null) {
                ensureNewPlateGridListIsMutable();
                newPlateGridList_.add(m);
              } else {
                newPlateGridListBuilder_.addMessage(m);
              }
              break;
            } // case 18
            case 26: {
              xddq.pb.GuardFairyTreeGoodsItemInfo m =
                  input.readMessage(
                      xddq.pb.GuardFairyTreeGoodsItemInfo.parser(),
                      extensionRegistry);
              if (newPlateGoodsBuilder_ == null) {
                ensureNewPlateGoodsIsMutable();
                newPlateGoods_.add(m);
              } else {
                newPlateGoodsBuilder_.addMessage(m);
              }
              break;
            } // case 26
            case 34: {
              xddq.pb.GuardFairyTreeGoodsItemInfo m =
                  input.readMessage(
                      xddq.pb.GuardFairyTreeGoodsItemInfo.parser(),
                      extensionRegistry);
              if (goodsListBuilder_ == null) {
                ensureGoodsListIsMutable();
                goodsList_.add(m);
              } else {
                goodsListBuilder_.addMessage(m);
              }
              break;
            } // case 34
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int activityId_ ;
    /**
     * <code>required int32 activityId = 1;</code>
     * @return Whether the activityId field is set.
     */
    @java.lang.Override
    public boolean hasActivityId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 activityId = 1;</code>
     * @return The activityId.
     */
    @java.lang.Override
    public int getActivityId() {
      return activityId_;
    }
    /**
     * <code>required int32 activityId = 1;</code>
     * @param value The activityId to set.
     * @return This builder for chaining.
     */
    public Builder setActivityId(int value) {

      activityId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 activityId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearActivityId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      activityId_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.GuardFairyTreeGridData> newPlateGridList_ =
      java.util.Collections.emptyList();
    private void ensureNewPlateGridListIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        newPlateGridList_ = new java.util.ArrayList<xddq.pb.GuardFairyTreeGridData>(newPlateGridList_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.GuardFairyTreeGridData, xddq.pb.GuardFairyTreeGridData.Builder, xddq.pb.GuardFairyTreeGridDataOrBuilder> newPlateGridListBuilder_;

    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGridData newPlateGridList = 2;</code>
     */
    public java.util.List<xddq.pb.GuardFairyTreeGridData> getNewPlateGridListList() {
      if (newPlateGridListBuilder_ == null) {
        return java.util.Collections.unmodifiableList(newPlateGridList_);
      } else {
        return newPlateGridListBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGridData newPlateGridList = 2;</code>
     */
    public int getNewPlateGridListCount() {
      if (newPlateGridListBuilder_ == null) {
        return newPlateGridList_.size();
      } else {
        return newPlateGridListBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGridData newPlateGridList = 2;</code>
     */
    public xddq.pb.GuardFairyTreeGridData getNewPlateGridList(int index) {
      if (newPlateGridListBuilder_ == null) {
        return newPlateGridList_.get(index);
      } else {
        return newPlateGridListBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGridData newPlateGridList = 2;</code>
     */
    public Builder setNewPlateGridList(
        int index, xddq.pb.GuardFairyTreeGridData value) {
      if (newPlateGridListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureNewPlateGridListIsMutable();
        newPlateGridList_.set(index, value);
        onChanged();
      } else {
        newPlateGridListBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGridData newPlateGridList = 2;</code>
     */
    public Builder setNewPlateGridList(
        int index, xddq.pb.GuardFairyTreeGridData.Builder builderForValue) {
      if (newPlateGridListBuilder_ == null) {
        ensureNewPlateGridListIsMutable();
        newPlateGridList_.set(index, builderForValue.build());
        onChanged();
      } else {
        newPlateGridListBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGridData newPlateGridList = 2;</code>
     */
    public Builder addNewPlateGridList(xddq.pb.GuardFairyTreeGridData value) {
      if (newPlateGridListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureNewPlateGridListIsMutable();
        newPlateGridList_.add(value);
        onChanged();
      } else {
        newPlateGridListBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGridData newPlateGridList = 2;</code>
     */
    public Builder addNewPlateGridList(
        int index, xddq.pb.GuardFairyTreeGridData value) {
      if (newPlateGridListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureNewPlateGridListIsMutable();
        newPlateGridList_.add(index, value);
        onChanged();
      } else {
        newPlateGridListBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGridData newPlateGridList = 2;</code>
     */
    public Builder addNewPlateGridList(
        xddq.pb.GuardFairyTreeGridData.Builder builderForValue) {
      if (newPlateGridListBuilder_ == null) {
        ensureNewPlateGridListIsMutable();
        newPlateGridList_.add(builderForValue.build());
        onChanged();
      } else {
        newPlateGridListBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGridData newPlateGridList = 2;</code>
     */
    public Builder addNewPlateGridList(
        int index, xddq.pb.GuardFairyTreeGridData.Builder builderForValue) {
      if (newPlateGridListBuilder_ == null) {
        ensureNewPlateGridListIsMutable();
        newPlateGridList_.add(index, builderForValue.build());
        onChanged();
      } else {
        newPlateGridListBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGridData newPlateGridList = 2;</code>
     */
    public Builder addAllNewPlateGridList(
        java.lang.Iterable<? extends xddq.pb.GuardFairyTreeGridData> values) {
      if (newPlateGridListBuilder_ == null) {
        ensureNewPlateGridListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, newPlateGridList_);
        onChanged();
      } else {
        newPlateGridListBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGridData newPlateGridList = 2;</code>
     */
    public Builder clearNewPlateGridList() {
      if (newPlateGridListBuilder_ == null) {
        newPlateGridList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        newPlateGridListBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGridData newPlateGridList = 2;</code>
     */
    public Builder removeNewPlateGridList(int index) {
      if (newPlateGridListBuilder_ == null) {
        ensureNewPlateGridListIsMutable();
        newPlateGridList_.remove(index);
        onChanged();
      } else {
        newPlateGridListBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGridData newPlateGridList = 2;</code>
     */
    public xddq.pb.GuardFairyTreeGridData.Builder getNewPlateGridListBuilder(
        int index) {
      return internalGetNewPlateGridListFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGridData newPlateGridList = 2;</code>
     */
    public xddq.pb.GuardFairyTreeGridDataOrBuilder getNewPlateGridListOrBuilder(
        int index) {
      if (newPlateGridListBuilder_ == null) {
        return newPlateGridList_.get(index);  } else {
        return newPlateGridListBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGridData newPlateGridList = 2;</code>
     */
    public java.util.List<? extends xddq.pb.GuardFairyTreeGridDataOrBuilder> 
         getNewPlateGridListOrBuilderList() {
      if (newPlateGridListBuilder_ != null) {
        return newPlateGridListBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(newPlateGridList_);
      }
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGridData newPlateGridList = 2;</code>
     */
    public xddq.pb.GuardFairyTreeGridData.Builder addNewPlateGridListBuilder() {
      return internalGetNewPlateGridListFieldBuilder().addBuilder(
          xddq.pb.GuardFairyTreeGridData.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGridData newPlateGridList = 2;</code>
     */
    public xddq.pb.GuardFairyTreeGridData.Builder addNewPlateGridListBuilder(
        int index) {
      return internalGetNewPlateGridListFieldBuilder().addBuilder(
          index, xddq.pb.GuardFairyTreeGridData.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGridData newPlateGridList = 2;</code>
     */
    public java.util.List<xddq.pb.GuardFairyTreeGridData.Builder> 
         getNewPlateGridListBuilderList() {
      return internalGetNewPlateGridListFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.GuardFairyTreeGridData, xddq.pb.GuardFairyTreeGridData.Builder, xddq.pb.GuardFairyTreeGridDataOrBuilder> 
        internalGetNewPlateGridListFieldBuilder() {
      if (newPlateGridListBuilder_ == null) {
        newPlateGridListBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.GuardFairyTreeGridData, xddq.pb.GuardFairyTreeGridData.Builder, xddq.pb.GuardFairyTreeGridDataOrBuilder>(
                newPlateGridList_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        newPlateGridList_ = null;
      }
      return newPlateGridListBuilder_;
    }

    private java.util.List<xddq.pb.GuardFairyTreeGoodsItemInfo> newPlateGoods_ =
      java.util.Collections.emptyList();
    private void ensureNewPlateGoodsIsMutable() {
      if (!((bitField0_ & 0x00000004) != 0)) {
        newPlateGoods_ = new java.util.ArrayList<xddq.pb.GuardFairyTreeGoodsItemInfo>(newPlateGoods_);
        bitField0_ |= 0x00000004;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.GuardFairyTreeGoodsItemInfo, xddq.pb.GuardFairyTreeGoodsItemInfo.Builder, xddq.pb.GuardFairyTreeGoodsItemInfoOrBuilder> newPlateGoodsBuilder_;

    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo newPlateGoods = 3;</code>
     */
    public java.util.List<xddq.pb.GuardFairyTreeGoodsItemInfo> getNewPlateGoodsList() {
      if (newPlateGoodsBuilder_ == null) {
        return java.util.Collections.unmodifiableList(newPlateGoods_);
      } else {
        return newPlateGoodsBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo newPlateGoods = 3;</code>
     */
    public int getNewPlateGoodsCount() {
      if (newPlateGoodsBuilder_ == null) {
        return newPlateGoods_.size();
      } else {
        return newPlateGoodsBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo newPlateGoods = 3;</code>
     */
    public xddq.pb.GuardFairyTreeGoodsItemInfo getNewPlateGoods(int index) {
      if (newPlateGoodsBuilder_ == null) {
        return newPlateGoods_.get(index);
      } else {
        return newPlateGoodsBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo newPlateGoods = 3;</code>
     */
    public Builder setNewPlateGoods(
        int index, xddq.pb.GuardFairyTreeGoodsItemInfo value) {
      if (newPlateGoodsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureNewPlateGoodsIsMutable();
        newPlateGoods_.set(index, value);
        onChanged();
      } else {
        newPlateGoodsBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo newPlateGoods = 3;</code>
     */
    public Builder setNewPlateGoods(
        int index, xddq.pb.GuardFairyTreeGoodsItemInfo.Builder builderForValue) {
      if (newPlateGoodsBuilder_ == null) {
        ensureNewPlateGoodsIsMutable();
        newPlateGoods_.set(index, builderForValue.build());
        onChanged();
      } else {
        newPlateGoodsBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo newPlateGoods = 3;</code>
     */
    public Builder addNewPlateGoods(xddq.pb.GuardFairyTreeGoodsItemInfo value) {
      if (newPlateGoodsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureNewPlateGoodsIsMutable();
        newPlateGoods_.add(value);
        onChanged();
      } else {
        newPlateGoodsBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo newPlateGoods = 3;</code>
     */
    public Builder addNewPlateGoods(
        int index, xddq.pb.GuardFairyTreeGoodsItemInfo value) {
      if (newPlateGoodsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureNewPlateGoodsIsMutable();
        newPlateGoods_.add(index, value);
        onChanged();
      } else {
        newPlateGoodsBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo newPlateGoods = 3;</code>
     */
    public Builder addNewPlateGoods(
        xddq.pb.GuardFairyTreeGoodsItemInfo.Builder builderForValue) {
      if (newPlateGoodsBuilder_ == null) {
        ensureNewPlateGoodsIsMutable();
        newPlateGoods_.add(builderForValue.build());
        onChanged();
      } else {
        newPlateGoodsBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo newPlateGoods = 3;</code>
     */
    public Builder addNewPlateGoods(
        int index, xddq.pb.GuardFairyTreeGoodsItemInfo.Builder builderForValue) {
      if (newPlateGoodsBuilder_ == null) {
        ensureNewPlateGoodsIsMutable();
        newPlateGoods_.add(index, builderForValue.build());
        onChanged();
      } else {
        newPlateGoodsBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo newPlateGoods = 3;</code>
     */
    public Builder addAllNewPlateGoods(
        java.lang.Iterable<? extends xddq.pb.GuardFairyTreeGoodsItemInfo> values) {
      if (newPlateGoodsBuilder_ == null) {
        ensureNewPlateGoodsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, newPlateGoods_);
        onChanged();
      } else {
        newPlateGoodsBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo newPlateGoods = 3;</code>
     */
    public Builder clearNewPlateGoods() {
      if (newPlateGoodsBuilder_ == null) {
        newPlateGoods_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
      } else {
        newPlateGoodsBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo newPlateGoods = 3;</code>
     */
    public Builder removeNewPlateGoods(int index) {
      if (newPlateGoodsBuilder_ == null) {
        ensureNewPlateGoodsIsMutable();
        newPlateGoods_.remove(index);
        onChanged();
      } else {
        newPlateGoodsBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo newPlateGoods = 3;</code>
     */
    public xddq.pb.GuardFairyTreeGoodsItemInfo.Builder getNewPlateGoodsBuilder(
        int index) {
      return internalGetNewPlateGoodsFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo newPlateGoods = 3;</code>
     */
    public xddq.pb.GuardFairyTreeGoodsItemInfoOrBuilder getNewPlateGoodsOrBuilder(
        int index) {
      if (newPlateGoodsBuilder_ == null) {
        return newPlateGoods_.get(index);  } else {
        return newPlateGoodsBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo newPlateGoods = 3;</code>
     */
    public java.util.List<? extends xddq.pb.GuardFairyTreeGoodsItemInfoOrBuilder> 
         getNewPlateGoodsOrBuilderList() {
      if (newPlateGoodsBuilder_ != null) {
        return newPlateGoodsBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(newPlateGoods_);
      }
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo newPlateGoods = 3;</code>
     */
    public xddq.pb.GuardFairyTreeGoodsItemInfo.Builder addNewPlateGoodsBuilder() {
      return internalGetNewPlateGoodsFieldBuilder().addBuilder(
          xddq.pb.GuardFairyTreeGoodsItemInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo newPlateGoods = 3;</code>
     */
    public xddq.pb.GuardFairyTreeGoodsItemInfo.Builder addNewPlateGoodsBuilder(
        int index) {
      return internalGetNewPlateGoodsFieldBuilder().addBuilder(
          index, xddq.pb.GuardFairyTreeGoodsItemInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo newPlateGoods = 3;</code>
     */
    public java.util.List<xddq.pb.GuardFairyTreeGoodsItemInfo.Builder> 
         getNewPlateGoodsBuilderList() {
      return internalGetNewPlateGoodsFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.GuardFairyTreeGoodsItemInfo, xddq.pb.GuardFairyTreeGoodsItemInfo.Builder, xddq.pb.GuardFairyTreeGoodsItemInfoOrBuilder> 
        internalGetNewPlateGoodsFieldBuilder() {
      if (newPlateGoodsBuilder_ == null) {
        newPlateGoodsBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.GuardFairyTreeGoodsItemInfo, xddq.pb.GuardFairyTreeGoodsItemInfo.Builder, xddq.pb.GuardFairyTreeGoodsItemInfoOrBuilder>(
                newPlateGoods_,
                ((bitField0_ & 0x00000004) != 0),
                getParentForChildren(),
                isClean());
        newPlateGoods_ = null;
      }
      return newPlateGoodsBuilder_;
    }

    private java.util.List<xddq.pb.GuardFairyTreeGoodsItemInfo> goodsList_ =
      java.util.Collections.emptyList();
    private void ensureGoodsListIsMutable() {
      if (!((bitField0_ & 0x00000008) != 0)) {
        goodsList_ = new java.util.ArrayList<xddq.pb.GuardFairyTreeGoodsItemInfo>(goodsList_);
        bitField0_ |= 0x00000008;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.GuardFairyTreeGoodsItemInfo, xddq.pb.GuardFairyTreeGoodsItemInfo.Builder, xddq.pb.GuardFairyTreeGoodsItemInfoOrBuilder> goodsListBuilder_;

    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo goodsList = 4;</code>
     */
    public java.util.List<xddq.pb.GuardFairyTreeGoodsItemInfo> getGoodsListList() {
      if (goodsListBuilder_ == null) {
        return java.util.Collections.unmodifiableList(goodsList_);
      } else {
        return goodsListBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo goodsList = 4;</code>
     */
    public int getGoodsListCount() {
      if (goodsListBuilder_ == null) {
        return goodsList_.size();
      } else {
        return goodsListBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo goodsList = 4;</code>
     */
    public xddq.pb.GuardFairyTreeGoodsItemInfo getGoodsList(int index) {
      if (goodsListBuilder_ == null) {
        return goodsList_.get(index);
      } else {
        return goodsListBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo goodsList = 4;</code>
     */
    public Builder setGoodsList(
        int index, xddq.pb.GuardFairyTreeGoodsItemInfo value) {
      if (goodsListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureGoodsListIsMutable();
        goodsList_.set(index, value);
        onChanged();
      } else {
        goodsListBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo goodsList = 4;</code>
     */
    public Builder setGoodsList(
        int index, xddq.pb.GuardFairyTreeGoodsItemInfo.Builder builderForValue) {
      if (goodsListBuilder_ == null) {
        ensureGoodsListIsMutable();
        goodsList_.set(index, builderForValue.build());
        onChanged();
      } else {
        goodsListBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo goodsList = 4;</code>
     */
    public Builder addGoodsList(xddq.pb.GuardFairyTreeGoodsItemInfo value) {
      if (goodsListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureGoodsListIsMutable();
        goodsList_.add(value);
        onChanged();
      } else {
        goodsListBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo goodsList = 4;</code>
     */
    public Builder addGoodsList(
        int index, xddq.pb.GuardFairyTreeGoodsItemInfo value) {
      if (goodsListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureGoodsListIsMutable();
        goodsList_.add(index, value);
        onChanged();
      } else {
        goodsListBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo goodsList = 4;</code>
     */
    public Builder addGoodsList(
        xddq.pb.GuardFairyTreeGoodsItemInfo.Builder builderForValue) {
      if (goodsListBuilder_ == null) {
        ensureGoodsListIsMutable();
        goodsList_.add(builderForValue.build());
        onChanged();
      } else {
        goodsListBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo goodsList = 4;</code>
     */
    public Builder addGoodsList(
        int index, xddq.pb.GuardFairyTreeGoodsItemInfo.Builder builderForValue) {
      if (goodsListBuilder_ == null) {
        ensureGoodsListIsMutable();
        goodsList_.add(index, builderForValue.build());
        onChanged();
      } else {
        goodsListBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo goodsList = 4;</code>
     */
    public Builder addAllGoodsList(
        java.lang.Iterable<? extends xddq.pb.GuardFairyTreeGoodsItemInfo> values) {
      if (goodsListBuilder_ == null) {
        ensureGoodsListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, goodsList_);
        onChanged();
      } else {
        goodsListBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo goodsList = 4;</code>
     */
    public Builder clearGoodsList() {
      if (goodsListBuilder_ == null) {
        goodsList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000008);
        onChanged();
      } else {
        goodsListBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo goodsList = 4;</code>
     */
    public Builder removeGoodsList(int index) {
      if (goodsListBuilder_ == null) {
        ensureGoodsListIsMutable();
        goodsList_.remove(index);
        onChanged();
      } else {
        goodsListBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo goodsList = 4;</code>
     */
    public xddq.pb.GuardFairyTreeGoodsItemInfo.Builder getGoodsListBuilder(
        int index) {
      return internalGetGoodsListFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo goodsList = 4;</code>
     */
    public xddq.pb.GuardFairyTreeGoodsItemInfoOrBuilder getGoodsListOrBuilder(
        int index) {
      if (goodsListBuilder_ == null) {
        return goodsList_.get(index);  } else {
        return goodsListBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo goodsList = 4;</code>
     */
    public java.util.List<? extends xddq.pb.GuardFairyTreeGoodsItemInfoOrBuilder> 
         getGoodsListOrBuilderList() {
      if (goodsListBuilder_ != null) {
        return goodsListBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(goodsList_);
      }
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo goodsList = 4;</code>
     */
    public xddq.pb.GuardFairyTreeGoodsItemInfo.Builder addGoodsListBuilder() {
      return internalGetGoodsListFieldBuilder().addBuilder(
          xddq.pb.GuardFairyTreeGoodsItemInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo goodsList = 4;</code>
     */
    public xddq.pb.GuardFairyTreeGoodsItemInfo.Builder addGoodsListBuilder(
        int index) {
      return internalGetGoodsListFieldBuilder().addBuilder(
          index, xddq.pb.GuardFairyTreeGoodsItemInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo goodsList = 4;</code>
     */
    public java.util.List<xddq.pb.GuardFairyTreeGoodsItemInfo.Builder> 
         getGoodsListBuilderList() {
      return internalGetGoodsListFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.GuardFairyTreeGoodsItemInfo, xddq.pb.GuardFairyTreeGoodsItemInfo.Builder, xddq.pb.GuardFairyTreeGoodsItemInfoOrBuilder> 
        internalGetGoodsListFieldBuilder() {
      if (goodsListBuilder_ == null) {
        goodsListBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.GuardFairyTreeGoodsItemInfo, xddq.pb.GuardFairyTreeGoodsItemInfo.Builder, xddq.pb.GuardFairyTreeGoodsItemInfoOrBuilder>(
                goodsList_,
                ((bitField0_ & 0x00000008) != 0),
                getParentForChildren(),
                isClean());
        goodsList_ = null;
      }
      return goodsListBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.GuardFairyTreePlateExpandReq)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.GuardFairyTreePlateExpandReq)
  private static final xddq.pb.GuardFairyTreePlateExpandReq DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.GuardFairyTreePlateExpandReq();
  }

  public static xddq.pb.GuardFairyTreePlateExpandReq getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<GuardFairyTreePlateExpandReq>
      PARSER = new com.google.protobuf.AbstractParser<GuardFairyTreePlateExpandReq>() {
    @java.lang.Override
    public GuardFairyTreePlateExpandReq parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<GuardFairyTreePlateExpandReq> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<GuardFairyTreePlateExpandReq> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.GuardFairyTreePlateExpandReq getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

