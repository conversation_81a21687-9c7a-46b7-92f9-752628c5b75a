// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.HolyLandUnionRankData}
 */
public final class HolyLandUnionRankData extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.HolyLandUnionRankData)
    HolyLandUnionRankDataOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      HolyLandUnionRankData.class.getName());
  }
  // Use HolyLandUnionRankData.newBuilder() to construct.
  private HolyLandUnionRankData(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private HolyLandUnionRankData() {
    unionName_ = "";
    masterName_ = "";
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_HolyLandUnionRankData_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_HolyLandUnionRankData_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.HolyLandUnionRankData.class, xddq.pb.HolyLandUnionRankData.Builder.class);
  }

  private int bitField0_;
  public static final int UNIONRANK_FIELD_NUMBER = 1;
  private int unionRank_ = 0;
  /**
   * <code>optional int32 unionRank = 1;</code>
   * @return Whether the unionRank field is set.
   */
  @java.lang.Override
  public boolean hasUnionRank() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int32 unionRank = 1;</code>
   * @return The unionRank.
   */
  @java.lang.Override
  public int getUnionRank() {
    return unionRank_;
  }

  public static final int UNIONFLAG_FIELD_NUMBER = 2;
  private int unionFlag_ = 0;
  /**
   * <code>optional int32 unionFlag = 2;</code>
   * @return Whether the unionFlag field is set.
   */
  @java.lang.Override
  public boolean hasUnionFlag() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int32 unionFlag = 2;</code>
   * @return The unionFlag.
   */
  @java.lang.Override
  public int getUnionFlag() {
    return unionFlag_;
  }

  public static final int UNIONNAME_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private volatile java.lang.Object unionName_ = "";
  /**
   * <code>optional string unionName = 3;</code>
   * @return Whether the unionName field is set.
   */
  @java.lang.Override
  public boolean hasUnionName() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional string unionName = 3;</code>
   * @return The unionName.
   */
  @java.lang.Override
  public java.lang.String getUnionName() {
    java.lang.Object ref = unionName_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        unionName_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string unionName = 3;</code>
   * @return The bytes for unionName.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getUnionNameBytes() {
    java.lang.Object ref = unionName_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      unionName_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int MASTERNAME_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private volatile java.lang.Object masterName_ = "";
  /**
   * <code>optional string masterName = 4;</code>
   * @return Whether the masterName field is set.
   */
  @java.lang.Override
  public boolean hasMasterName() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional string masterName = 4;</code>
   * @return The masterName.
   */
  @java.lang.Override
  public java.lang.String getMasterName() {
    java.lang.Object ref = masterName_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        masterName_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string masterName = 4;</code>
   * @return The bytes for masterName.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getMasterNameBytes() {
    java.lang.Object ref = masterName_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      masterName_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int SERVERID_FIELD_NUMBER = 5;
  private long serverId_ = 0L;
  /**
   * <code>optional int64 serverId = 5;</code>
   * @return Whether the serverId field is set.
   */
  @java.lang.Override
  public boolean hasServerId() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>optional int64 serverId = 5;</code>
   * @return The serverId.
   */
  @java.lang.Override
  public long getServerId() {
    return serverId_;
  }

  public static final int UNIONSCORE_FIELD_NUMBER = 6;
  private int unionScore_ = 0;
  /**
   * <code>optional int32 unionScore = 6;</code>
   * @return Whether the unionScore field is set.
   */
  @java.lang.Override
  public boolean hasUnionScore() {
    return ((bitField0_ & 0x00000020) != 0);
  }
  /**
   * <code>optional int32 unionScore = 6;</code>
   * @return The unionScore.
   */
  @java.lang.Override
  public int getUnionScore() {
    return unionScore_;
  }

  public static final int UNIONID_FIELD_NUMBER = 7;
  private long unionId_ = 0L;
  /**
   * <code>optional int64 unionId = 7;</code>
   * @return Whether the unionId field is set.
   */
  @java.lang.Override
  public boolean hasUnionId() {
    return ((bitField0_ & 0x00000040) != 0);
  }
  /**
   * <code>optional int64 unionId = 7;</code>
   * @return The unionId.
   */
  @java.lang.Override
  public long getUnionId() {
    return unionId_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, unionRank_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(2, unionFlag_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 3, unionName_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 4, masterName_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      output.writeInt64(5, serverId_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      output.writeInt32(6, unionScore_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      output.writeInt64(7, unionId_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, unionRank_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, unionFlag_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(3, unionName_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(4, masterName_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(5, serverId_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(6, unionScore_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(7, unionId_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.HolyLandUnionRankData)) {
      return super.equals(obj);
    }
    xddq.pb.HolyLandUnionRankData other = (xddq.pb.HolyLandUnionRankData) obj;

    if (hasUnionRank() != other.hasUnionRank()) return false;
    if (hasUnionRank()) {
      if (getUnionRank()
          != other.getUnionRank()) return false;
    }
    if (hasUnionFlag() != other.hasUnionFlag()) return false;
    if (hasUnionFlag()) {
      if (getUnionFlag()
          != other.getUnionFlag()) return false;
    }
    if (hasUnionName() != other.hasUnionName()) return false;
    if (hasUnionName()) {
      if (!getUnionName()
          .equals(other.getUnionName())) return false;
    }
    if (hasMasterName() != other.hasMasterName()) return false;
    if (hasMasterName()) {
      if (!getMasterName()
          .equals(other.getMasterName())) return false;
    }
    if (hasServerId() != other.hasServerId()) return false;
    if (hasServerId()) {
      if (getServerId()
          != other.getServerId()) return false;
    }
    if (hasUnionScore() != other.hasUnionScore()) return false;
    if (hasUnionScore()) {
      if (getUnionScore()
          != other.getUnionScore()) return false;
    }
    if (hasUnionId() != other.hasUnionId()) return false;
    if (hasUnionId()) {
      if (getUnionId()
          != other.getUnionId()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasUnionRank()) {
      hash = (37 * hash) + UNIONRANK_FIELD_NUMBER;
      hash = (53 * hash) + getUnionRank();
    }
    if (hasUnionFlag()) {
      hash = (37 * hash) + UNIONFLAG_FIELD_NUMBER;
      hash = (53 * hash) + getUnionFlag();
    }
    if (hasUnionName()) {
      hash = (37 * hash) + UNIONNAME_FIELD_NUMBER;
      hash = (53 * hash) + getUnionName().hashCode();
    }
    if (hasMasterName()) {
      hash = (37 * hash) + MASTERNAME_FIELD_NUMBER;
      hash = (53 * hash) + getMasterName().hashCode();
    }
    if (hasServerId()) {
      hash = (37 * hash) + SERVERID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getServerId());
    }
    if (hasUnionScore()) {
      hash = (37 * hash) + UNIONSCORE_FIELD_NUMBER;
      hash = (53 * hash) + getUnionScore();
    }
    if (hasUnionId()) {
      hash = (37 * hash) + UNIONID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getUnionId());
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.HolyLandUnionRankData parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.HolyLandUnionRankData parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.HolyLandUnionRankData parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.HolyLandUnionRankData parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.HolyLandUnionRankData parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.HolyLandUnionRankData parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.HolyLandUnionRankData parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.HolyLandUnionRankData parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.HolyLandUnionRankData parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.HolyLandUnionRankData parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.HolyLandUnionRankData parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.HolyLandUnionRankData parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.HolyLandUnionRankData prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.HolyLandUnionRankData}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.HolyLandUnionRankData)
      xddq.pb.HolyLandUnionRankDataOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_HolyLandUnionRankData_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_HolyLandUnionRankData_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.HolyLandUnionRankData.class, xddq.pb.HolyLandUnionRankData.Builder.class);
    }

    // Construct using xddq.pb.HolyLandUnionRankData.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      unionRank_ = 0;
      unionFlag_ = 0;
      unionName_ = "";
      masterName_ = "";
      serverId_ = 0L;
      unionScore_ = 0;
      unionId_ = 0L;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_HolyLandUnionRankData_descriptor;
    }

    @java.lang.Override
    public xddq.pb.HolyLandUnionRankData getDefaultInstanceForType() {
      return xddq.pb.HolyLandUnionRankData.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.HolyLandUnionRankData build() {
      xddq.pb.HolyLandUnionRankData result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.HolyLandUnionRankData buildPartial() {
      xddq.pb.HolyLandUnionRankData result = new xddq.pb.HolyLandUnionRankData(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.HolyLandUnionRankData result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.unionRank_ = unionRank_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.unionFlag_ = unionFlag_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.unionName_ = unionName_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.masterName_ = masterName_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.serverId_ = serverId_;
        to_bitField0_ |= 0x00000010;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.unionScore_ = unionScore_;
        to_bitField0_ |= 0x00000020;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.unionId_ = unionId_;
        to_bitField0_ |= 0x00000040;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.HolyLandUnionRankData) {
        return mergeFrom((xddq.pb.HolyLandUnionRankData)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.HolyLandUnionRankData other) {
      if (other == xddq.pb.HolyLandUnionRankData.getDefaultInstance()) return this;
      if (other.hasUnionRank()) {
        setUnionRank(other.getUnionRank());
      }
      if (other.hasUnionFlag()) {
        setUnionFlag(other.getUnionFlag());
      }
      if (other.hasUnionName()) {
        unionName_ = other.unionName_;
        bitField0_ |= 0x00000004;
        onChanged();
      }
      if (other.hasMasterName()) {
        masterName_ = other.masterName_;
        bitField0_ |= 0x00000008;
        onChanged();
      }
      if (other.hasServerId()) {
        setServerId(other.getServerId());
      }
      if (other.hasUnionScore()) {
        setUnionScore(other.getUnionScore());
      }
      if (other.hasUnionId()) {
        setUnionId(other.getUnionId());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              unionRank_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              unionFlag_ = input.readInt32();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 26: {
              unionName_ = input.readBytes();
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 34: {
              masterName_ = input.readBytes();
              bitField0_ |= 0x00000008;
              break;
            } // case 34
            case 40: {
              serverId_ = input.readInt64();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            case 48: {
              unionScore_ = input.readInt32();
              bitField0_ |= 0x00000020;
              break;
            } // case 48
            case 56: {
              unionId_ = input.readInt64();
              bitField0_ |= 0x00000040;
              break;
            } // case 56
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int unionRank_ ;
    /**
     * <code>optional int32 unionRank = 1;</code>
     * @return Whether the unionRank field is set.
     */
    @java.lang.Override
    public boolean hasUnionRank() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 unionRank = 1;</code>
     * @return The unionRank.
     */
    @java.lang.Override
    public int getUnionRank() {
      return unionRank_;
    }
    /**
     * <code>optional int32 unionRank = 1;</code>
     * @param value The unionRank to set.
     * @return This builder for chaining.
     */
    public Builder setUnionRank(int value) {

      unionRank_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 unionRank = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearUnionRank() {
      bitField0_ = (bitField0_ & ~0x00000001);
      unionRank_ = 0;
      onChanged();
      return this;
    }

    private int unionFlag_ ;
    /**
     * <code>optional int32 unionFlag = 2;</code>
     * @return Whether the unionFlag field is set.
     */
    @java.lang.Override
    public boolean hasUnionFlag() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 unionFlag = 2;</code>
     * @return The unionFlag.
     */
    @java.lang.Override
    public int getUnionFlag() {
      return unionFlag_;
    }
    /**
     * <code>optional int32 unionFlag = 2;</code>
     * @param value The unionFlag to set.
     * @return This builder for chaining.
     */
    public Builder setUnionFlag(int value) {

      unionFlag_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 unionFlag = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearUnionFlag() {
      bitField0_ = (bitField0_ & ~0x00000002);
      unionFlag_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object unionName_ = "";
    /**
     * <code>optional string unionName = 3;</code>
     * @return Whether the unionName field is set.
     */
    public boolean hasUnionName() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional string unionName = 3;</code>
     * @return The unionName.
     */
    public java.lang.String getUnionName() {
      java.lang.Object ref = unionName_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          unionName_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string unionName = 3;</code>
     * @return The bytes for unionName.
     */
    public com.google.protobuf.ByteString
        getUnionNameBytes() {
      java.lang.Object ref = unionName_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        unionName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string unionName = 3;</code>
     * @param value The unionName to set.
     * @return This builder for chaining.
     */
    public Builder setUnionName(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      unionName_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional string unionName = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearUnionName() {
      unionName_ = getDefaultInstance().getUnionName();
      bitField0_ = (bitField0_ & ~0x00000004);
      onChanged();
      return this;
    }
    /**
     * <code>optional string unionName = 3;</code>
     * @param value The bytes for unionName to set.
     * @return This builder for chaining.
     */
    public Builder setUnionNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      unionName_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }

    private java.lang.Object masterName_ = "";
    /**
     * <code>optional string masterName = 4;</code>
     * @return Whether the masterName field is set.
     */
    public boolean hasMasterName() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional string masterName = 4;</code>
     * @return The masterName.
     */
    public java.lang.String getMasterName() {
      java.lang.Object ref = masterName_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          masterName_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string masterName = 4;</code>
     * @return The bytes for masterName.
     */
    public com.google.protobuf.ByteString
        getMasterNameBytes() {
      java.lang.Object ref = masterName_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        masterName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string masterName = 4;</code>
     * @param value The masterName to set.
     * @return This builder for chaining.
     */
    public Builder setMasterName(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      masterName_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional string masterName = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearMasterName() {
      masterName_ = getDefaultInstance().getMasterName();
      bitField0_ = (bitField0_ & ~0x00000008);
      onChanged();
      return this;
    }
    /**
     * <code>optional string masterName = 4;</code>
     * @param value The bytes for masterName to set.
     * @return This builder for chaining.
     */
    public Builder setMasterNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      masterName_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }

    private long serverId_ ;
    /**
     * <code>optional int64 serverId = 5;</code>
     * @return Whether the serverId field is set.
     */
    @java.lang.Override
    public boolean hasServerId() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional int64 serverId = 5;</code>
     * @return The serverId.
     */
    @java.lang.Override
    public long getServerId() {
      return serverId_;
    }
    /**
     * <code>optional int64 serverId = 5;</code>
     * @param value The serverId to set.
     * @return This builder for chaining.
     */
    public Builder setServerId(long value) {

      serverId_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 serverId = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearServerId() {
      bitField0_ = (bitField0_ & ~0x00000010);
      serverId_ = 0L;
      onChanged();
      return this;
    }

    private int unionScore_ ;
    /**
     * <code>optional int32 unionScore = 6;</code>
     * @return Whether the unionScore field is set.
     */
    @java.lang.Override
    public boolean hasUnionScore() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional int32 unionScore = 6;</code>
     * @return The unionScore.
     */
    @java.lang.Override
    public int getUnionScore() {
      return unionScore_;
    }
    /**
     * <code>optional int32 unionScore = 6;</code>
     * @param value The unionScore to set.
     * @return This builder for chaining.
     */
    public Builder setUnionScore(int value) {

      unionScore_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 unionScore = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearUnionScore() {
      bitField0_ = (bitField0_ & ~0x00000020);
      unionScore_ = 0;
      onChanged();
      return this;
    }

    private long unionId_ ;
    /**
     * <code>optional int64 unionId = 7;</code>
     * @return Whether the unionId field is set.
     */
    @java.lang.Override
    public boolean hasUnionId() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <code>optional int64 unionId = 7;</code>
     * @return The unionId.
     */
    @java.lang.Override
    public long getUnionId() {
      return unionId_;
    }
    /**
     * <code>optional int64 unionId = 7;</code>
     * @param value The unionId to set.
     * @return This builder for chaining.
     */
    public Builder setUnionId(long value) {

      unionId_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 unionId = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearUnionId() {
      bitField0_ = (bitField0_ & ~0x00000040);
      unionId_ = 0L;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.HolyLandUnionRankData)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.HolyLandUnionRankData)
  private static final xddq.pb.HolyLandUnionRankData DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.HolyLandUnionRankData();
  }

  public static xddq.pb.HolyLandUnionRankData getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<HolyLandUnionRankData>
      PARSER = new com.google.protobuf.AbstractParser<HolyLandUnionRankData>() {
    @java.lang.Override
    public HolyLandUnionRankData parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<HolyLandUnionRankData> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<HolyLandUnionRankData> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.HolyLandUnionRankData getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

