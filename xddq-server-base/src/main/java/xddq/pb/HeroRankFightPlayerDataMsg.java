// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.HeroRankFightPlayerDataMsg}
 */
public final class HeroRankFightPlayerDataMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.HeroRankFightPlayerDataMsg)
    HeroRankFightPlayerDataMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      HeroRankFightPlayerDataMsg.class.getName());
  }
  // Use HeroRankFightPlayerDataMsg.newBuilder() to construct.
  private HeroRankFightPlayerDataMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private HeroRankFightPlayerDataMsg() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_HeroRankFightPlayerDataMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_HeroRankFightPlayerDataMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.HeroRankFightPlayerDataMsg.class, xddq.pb.HeroRankFightPlayerDataMsg.Builder.class);
  }

  private int bitField0_;
  public static final int RANK_FIELD_NUMBER = 1;
  private int rank_ = 0;
  /**
   * <code>optional int32 rank = 1;</code>
   * @return Whether the rank field is set.
   */
  @java.lang.Override
  public boolean hasRank() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int32 rank = 1;</code>
   * @return The rank.
   */
  @java.lang.Override
  public int getRank() {
    return rank_;
  }

  public static final int SHOWINFO_FIELD_NUMBER = 2;
  private xddq.pb.PlayerAppearanceDataMsg showInfo_;
  /**
   * <code>optional .xddq.pb.PlayerAppearanceDataMsg showInfo = 2;</code>
   * @return Whether the showInfo field is set.
   */
  @java.lang.Override
  public boolean hasShowInfo() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional .xddq.pb.PlayerAppearanceDataMsg showInfo = 2;</code>
   * @return The showInfo.
   */
  @java.lang.Override
  public xddq.pb.PlayerAppearanceDataMsg getShowInfo() {
    return showInfo_ == null ? xddq.pb.PlayerAppearanceDataMsg.getDefaultInstance() : showInfo_;
  }
  /**
   * <code>optional .xddq.pb.PlayerAppearanceDataMsg showInfo = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.PlayerAppearanceDataMsgOrBuilder getShowInfoOrBuilder() {
    return showInfo_ == null ? xddq.pb.PlayerAppearanceDataMsg.getDefaultInstance() : showInfo_;
  }

  public static final int MASTERID_FIELD_NUMBER = 3;
  private int masterId_ = 0;
  /**
   * <code>optional int32 masterId = 3;</code>
   * @return Whether the masterId field is set.
   */
  @java.lang.Override
  public boolean hasMasterId() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional int32 masterId = 3;</code>
   * @return The masterId.
   */
  @java.lang.Override
  public int getMasterId() {
    return masterId_;
  }

  public static final int MASTERLV_FIELD_NUMBER = 4;
  private int masterLv_ = 0;
  /**
   * <code>optional int32 masterLv = 4;</code>
   * @return Whether the masterLv field is set.
   */
  @java.lang.Override
  public boolean hasMasterLv() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional int32 masterLv = 4;</code>
   * @return The masterLv.
   */
  @java.lang.Override
  public int getMasterLv() {
    return masterLv_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, rank_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeMessage(2, getShowInfo());
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt32(3, masterId_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt32(4, masterLv_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, rank_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getShowInfo());
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(3, masterId_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(4, masterLv_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.HeroRankFightPlayerDataMsg)) {
      return super.equals(obj);
    }
    xddq.pb.HeroRankFightPlayerDataMsg other = (xddq.pb.HeroRankFightPlayerDataMsg) obj;

    if (hasRank() != other.hasRank()) return false;
    if (hasRank()) {
      if (getRank()
          != other.getRank()) return false;
    }
    if (hasShowInfo() != other.hasShowInfo()) return false;
    if (hasShowInfo()) {
      if (!getShowInfo()
          .equals(other.getShowInfo())) return false;
    }
    if (hasMasterId() != other.hasMasterId()) return false;
    if (hasMasterId()) {
      if (getMasterId()
          != other.getMasterId()) return false;
    }
    if (hasMasterLv() != other.hasMasterLv()) return false;
    if (hasMasterLv()) {
      if (getMasterLv()
          != other.getMasterLv()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRank()) {
      hash = (37 * hash) + RANK_FIELD_NUMBER;
      hash = (53 * hash) + getRank();
    }
    if (hasShowInfo()) {
      hash = (37 * hash) + SHOWINFO_FIELD_NUMBER;
      hash = (53 * hash) + getShowInfo().hashCode();
    }
    if (hasMasterId()) {
      hash = (37 * hash) + MASTERID_FIELD_NUMBER;
      hash = (53 * hash) + getMasterId();
    }
    if (hasMasterLv()) {
      hash = (37 * hash) + MASTERLV_FIELD_NUMBER;
      hash = (53 * hash) + getMasterLv();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.HeroRankFightPlayerDataMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.HeroRankFightPlayerDataMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.HeroRankFightPlayerDataMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.HeroRankFightPlayerDataMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.HeroRankFightPlayerDataMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.HeroRankFightPlayerDataMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.HeroRankFightPlayerDataMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.HeroRankFightPlayerDataMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.HeroRankFightPlayerDataMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.HeroRankFightPlayerDataMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.HeroRankFightPlayerDataMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.HeroRankFightPlayerDataMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.HeroRankFightPlayerDataMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.HeroRankFightPlayerDataMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.HeroRankFightPlayerDataMsg)
      xddq.pb.HeroRankFightPlayerDataMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_HeroRankFightPlayerDataMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_HeroRankFightPlayerDataMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.HeroRankFightPlayerDataMsg.class, xddq.pb.HeroRankFightPlayerDataMsg.Builder.class);
    }

    // Construct using xddq.pb.HeroRankFightPlayerDataMsg.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetShowInfoFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      rank_ = 0;
      showInfo_ = null;
      if (showInfoBuilder_ != null) {
        showInfoBuilder_.dispose();
        showInfoBuilder_ = null;
      }
      masterId_ = 0;
      masterLv_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_HeroRankFightPlayerDataMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.HeroRankFightPlayerDataMsg getDefaultInstanceForType() {
      return xddq.pb.HeroRankFightPlayerDataMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.HeroRankFightPlayerDataMsg build() {
      xddq.pb.HeroRankFightPlayerDataMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.HeroRankFightPlayerDataMsg buildPartial() {
      xddq.pb.HeroRankFightPlayerDataMsg result = new xddq.pb.HeroRankFightPlayerDataMsg(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.HeroRankFightPlayerDataMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.rank_ = rank_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.showInfo_ = showInfoBuilder_ == null
            ? showInfo_
            : showInfoBuilder_.build();
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.masterId_ = masterId_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.masterLv_ = masterLv_;
        to_bitField0_ |= 0x00000008;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.HeroRankFightPlayerDataMsg) {
        return mergeFrom((xddq.pb.HeroRankFightPlayerDataMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.HeroRankFightPlayerDataMsg other) {
      if (other == xddq.pb.HeroRankFightPlayerDataMsg.getDefaultInstance()) return this;
      if (other.hasRank()) {
        setRank(other.getRank());
      }
      if (other.hasShowInfo()) {
        mergeShowInfo(other.getShowInfo());
      }
      if (other.hasMasterId()) {
        setMasterId(other.getMasterId());
      }
      if (other.hasMasterLv()) {
        setMasterLv(other.getMasterLv());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              rank_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              input.readMessage(
                  internalGetShowInfoFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 24: {
              masterId_ = input.readInt32();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 32: {
              masterLv_ = input.readInt32();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int rank_ ;
    /**
     * <code>optional int32 rank = 1;</code>
     * @return Whether the rank field is set.
     */
    @java.lang.Override
    public boolean hasRank() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 rank = 1;</code>
     * @return The rank.
     */
    @java.lang.Override
    public int getRank() {
      return rank_;
    }
    /**
     * <code>optional int32 rank = 1;</code>
     * @param value The rank to set.
     * @return This builder for chaining.
     */
    public Builder setRank(int value) {

      rank_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 rank = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRank() {
      bitField0_ = (bitField0_ & ~0x00000001);
      rank_ = 0;
      onChanged();
      return this;
    }

    private xddq.pb.PlayerAppearanceDataMsg showInfo_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PlayerAppearanceDataMsg, xddq.pb.PlayerAppearanceDataMsg.Builder, xddq.pb.PlayerAppearanceDataMsgOrBuilder> showInfoBuilder_;
    /**
     * <code>optional .xddq.pb.PlayerAppearanceDataMsg showInfo = 2;</code>
     * @return Whether the showInfo field is set.
     */
    public boolean hasShowInfo() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .xddq.pb.PlayerAppearanceDataMsg showInfo = 2;</code>
     * @return The showInfo.
     */
    public xddq.pb.PlayerAppearanceDataMsg getShowInfo() {
      if (showInfoBuilder_ == null) {
        return showInfo_ == null ? xddq.pb.PlayerAppearanceDataMsg.getDefaultInstance() : showInfo_;
      } else {
        return showInfoBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.PlayerAppearanceDataMsg showInfo = 2;</code>
     */
    public Builder setShowInfo(xddq.pb.PlayerAppearanceDataMsg value) {
      if (showInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        showInfo_ = value;
      } else {
        showInfoBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerAppearanceDataMsg showInfo = 2;</code>
     */
    public Builder setShowInfo(
        xddq.pb.PlayerAppearanceDataMsg.Builder builderForValue) {
      if (showInfoBuilder_ == null) {
        showInfo_ = builderForValue.build();
      } else {
        showInfoBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerAppearanceDataMsg showInfo = 2;</code>
     */
    public Builder mergeShowInfo(xddq.pb.PlayerAppearanceDataMsg value) {
      if (showInfoBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0) &&
          showInfo_ != null &&
          showInfo_ != xddq.pb.PlayerAppearanceDataMsg.getDefaultInstance()) {
          getShowInfoBuilder().mergeFrom(value);
        } else {
          showInfo_ = value;
        }
      } else {
        showInfoBuilder_.mergeFrom(value);
      }
      if (showInfo_ != null) {
        bitField0_ |= 0x00000002;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerAppearanceDataMsg showInfo = 2;</code>
     */
    public Builder clearShowInfo() {
      bitField0_ = (bitField0_ & ~0x00000002);
      showInfo_ = null;
      if (showInfoBuilder_ != null) {
        showInfoBuilder_.dispose();
        showInfoBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerAppearanceDataMsg showInfo = 2;</code>
     */
    public xddq.pb.PlayerAppearanceDataMsg.Builder getShowInfoBuilder() {
      bitField0_ |= 0x00000002;
      onChanged();
      return internalGetShowInfoFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.PlayerAppearanceDataMsg showInfo = 2;</code>
     */
    public xddq.pb.PlayerAppearanceDataMsgOrBuilder getShowInfoOrBuilder() {
      if (showInfoBuilder_ != null) {
        return showInfoBuilder_.getMessageOrBuilder();
      } else {
        return showInfo_ == null ?
            xddq.pb.PlayerAppearanceDataMsg.getDefaultInstance() : showInfo_;
      }
    }
    /**
     * <code>optional .xddq.pb.PlayerAppearanceDataMsg showInfo = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PlayerAppearanceDataMsg, xddq.pb.PlayerAppearanceDataMsg.Builder, xddq.pb.PlayerAppearanceDataMsgOrBuilder> 
        internalGetShowInfoFieldBuilder() {
      if (showInfoBuilder_ == null) {
        showInfoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.PlayerAppearanceDataMsg, xddq.pb.PlayerAppearanceDataMsg.Builder, xddq.pb.PlayerAppearanceDataMsgOrBuilder>(
                getShowInfo(),
                getParentForChildren(),
                isClean());
        showInfo_ = null;
      }
      return showInfoBuilder_;
    }

    private int masterId_ ;
    /**
     * <code>optional int32 masterId = 3;</code>
     * @return Whether the masterId field is set.
     */
    @java.lang.Override
    public boolean hasMasterId() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int32 masterId = 3;</code>
     * @return The masterId.
     */
    @java.lang.Override
    public int getMasterId() {
      return masterId_;
    }
    /**
     * <code>optional int32 masterId = 3;</code>
     * @param value The masterId to set.
     * @return This builder for chaining.
     */
    public Builder setMasterId(int value) {

      masterId_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 masterId = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearMasterId() {
      bitField0_ = (bitField0_ & ~0x00000004);
      masterId_ = 0;
      onChanged();
      return this;
    }

    private int masterLv_ ;
    /**
     * <code>optional int32 masterLv = 4;</code>
     * @return Whether the masterLv field is set.
     */
    @java.lang.Override
    public boolean hasMasterLv() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int32 masterLv = 4;</code>
     * @return The masterLv.
     */
    @java.lang.Override
    public int getMasterLv() {
      return masterLv_;
    }
    /**
     * <code>optional int32 masterLv = 4;</code>
     * @param value The masterLv to set.
     * @return This builder for chaining.
     */
    public Builder setMasterLv(int value) {

      masterLv_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 masterLv = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearMasterLv() {
      bitField0_ = (bitField0_ & ~0x00000008);
      masterLv_ = 0;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.HeroRankFightPlayerDataMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.HeroRankFightPlayerDataMsg)
  private static final xddq.pb.HeroRankFightPlayerDataMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.HeroRankFightPlayerDataMsg();
  }

  public static xddq.pb.HeroRankFightPlayerDataMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<HeroRankFightPlayerDataMsg>
      PARSER = new com.google.protobuf.AbstractParser<HeroRankFightPlayerDataMsg>() {
    @java.lang.Override
    public HeroRankFightPlayerDataMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<HeroRankFightPlayerDataMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<HeroRankFightPlayerDataMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.HeroRankFightPlayerDataMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

