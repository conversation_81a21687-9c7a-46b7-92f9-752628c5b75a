// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.ADGiftTriggerGiftDataMsg}
 */
public final class ADGiftTriggerGiftDataMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.ADGiftTriggerGiftDataMsg)
    ADGiftTriggerGiftDataMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      ADGiftTriggerGiftDataMsg.class.getName());
  }
  // Use ADGiftTriggerGiftDataMsg.newBuilder() to construct.
  private ADGiftTriggerGiftDataMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private ADGiftTriggerGiftDataMsg() {
    hasBeenBought_ = emptyIntList();
    triggerMallId_ = emptyIntList();
    reward_ = "";
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_ADGiftTriggerGiftDataMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_ADGiftTriggerGiftDataMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.ADGiftTriggerGiftDataMsg.class, xddq.pb.ADGiftTriggerGiftDataMsg.Builder.class);
  }

  private int bitField0_;
  public static final int CONDITIONID_FIELD_NUMBER = 1;
  private int conditionId_ = 0;
  /**
   * <code>required int32 conditionId = 1;</code>
   * @return Whether the conditionId field is set.
   */
  @java.lang.Override
  public boolean hasConditionId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 conditionId = 1;</code>
   * @return The conditionId.
   */
  @java.lang.Override
  public int getConditionId() {
    return conditionId_;
  }

  public static final int GIFTID_FIELD_NUMBER = 2;
  private int giftId_ = 0;
  /**
   * <code>required int32 giftId = 2;</code>
   * @return Whether the giftId field is set.
   */
  @java.lang.Override
  public boolean hasGiftId() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>required int32 giftId = 2;</code>
   * @return The giftId.
   */
  @java.lang.Override
  public int getGiftId() {
    return giftId_;
  }

  public static final int ENDTIME_FIELD_NUMBER = 3;
  private long endTime_ = 0L;
  /**
   * <code>required int64 endTime = 3;</code>
   * @return Whether the endTime field is set.
   */
  @java.lang.Override
  public boolean hasEndTime() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>required int64 endTime = 3;</code>
   * @return The endTime.
   */
  @java.lang.Override
  public long getEndTime() {
    return endTime_;
  }

  public static final int HASBEENBOUGHT_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private com.google.protobuf.Internal.IntList hasBeenBought_ =
      emptyIntList();
  /**
   * <code>repeated int32 hasBeenBought = 4;</code>
   * @return A list containing the hasBeenBought.
   */
  @java.lang.Override
  public java.util.List<java.lang.Integer>
      getHasBeenBoughtList() {
    return hasBeenBought_;
  }
  /**
   * <code>repeated int32 hasBeenBought = 4;</code>
   * @return The count of hasBeenBought.
   */
  public int getHasBeenBoughtCount() {
    return hasBeenBought_.size();
  }
  /**
   * <code>repeated int32 hasBeenBought = 4;</code>
   * @param index The index of the element to return.
   * @return The hasBeenBought at the given index.
   */
  public int getHasBeenBought(int index) {
    return hasBeenBought_.getInt(index);
  }

  public static final int TRIGGERMALLID_FIELD_NUMBER = 5;
  @SuppressWarnings("serial")
  private com.google.protobuf.Internal.IntList triggerMallId_ =
      emptyIntList();
  /**
   * <code>repeated int32 triggerMallId = 5;</code>
   * @return A list containing the triggerMallId.
   */
  @java.lang.Override
  public java.util.List<java.lang.Integer>
      getTriggerMallIdList() {
    return triggerMallId_;
  }
  /**
   * <code>repeated int32 triggerMallId = 5;</code>
   * @return The count of triggerMallId.
   */
  public int getTriggerMallIdCount() {
    return triggerMallId_.size();
  }
  /**
   * <code>repeated int32 triggerMallId = 5;</code>
   * @param index The index of the element to return.
   * @return The triggerMallId at the given index.
   */
  public int getTriggerMallId(int index) {
    return triggerMallId_.getInt(index);
  }

  public static final int REWARD_FIELD_NUMBER = 6;
  @SuppressWarnings("serial")
  private volatile java.lang.Object reward_ = "";
  /**
   * <code>optional string reward = 6;</code>
   * @return Whether the reward field is set.
   */
  @java.lang.Override
  public boolean hasReward() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional string reward = 6;</code>
   * @return The reward.
   */
  @java.lang.Override
  public java.lang.String getReward() {
    java.lang.Object ref = reward_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        reward_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string reward = 6;</code>
   * @return The bytes for reward.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getRewardBytes() {
    java.lang.Object ref = reward_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      reward_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasConditionId()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasGiftId()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasEndTime()) {
      memoizedIsInitialized = 0;
      return false;
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, conditionId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(2, giftId_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt64(3, endTime_);
    }
    for (int i = 0; i < hasBeenBought_.size(); i++) {
      output.writeInt32(4, hasBeenBought_.getInt(i));
    }
    for (int i = 0; i < triggerMallId_.size(); i++) {
      output.writeInt32(5, triggerMallId_.getInt(i));
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 6, reward_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, conditionId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, giftId_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(3, endTime_);
    }
    {
      int dataSize = 0;
      for (int i = 0; i < hasBeenBought_.size(); i++) {
        dataSize += com.google.protobuf.CodedOutputStream
          .computeInt32SizeNoTag(hasBeenBought_.getInt(i));
      }
      size += dataSize;
      size += 1 * getHasBeenBoughtList().size();
    }
    {
      int dataSize = 0;
      for (int i = 0; i < triggerMallId_.size(); i++) {
        dataSize += com.google.protobuf.CodedOutputStream
          .computeInt32SizeNoTag(triggerMallId_.getInt(i));
      }
      size += dataSize;
      size += 1 * getTriggerMallIdList().size();
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(6, reward_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.ADGiftTriggerGiftDataMsg)) {
      return super.equals(obj);
    }
    xddq.pb.ADGiftTriggerGiftDataMsg other = (xddq.pb.ADGiftTriggerGiftDataMsg) obj;

    if (hasConditionId() != other.hasConditionId()) return false;
    if (hasConditionId()) {
      if (getConditionId()
          != other.getConditionId()) return false;
    }
    if (hasGiftId() != other.hasGiftId()) return false;
    if (hasGiftId()) {
      if (getGiftId()
          != other.getGiftId()) return false;
    }
    if (hasEndTime() != other.hasEndTime()) return false;
    if (hasEndTime()) {
      if (getEndTime()
          != other.getEndTime()) return false;
    }
    if (!getHasBeenBoughtList()
        .equals(other.getHasBeenBoughtList())) return false;
    if (!getTriggerMallIdList()
        .equals(other.getTriggerMallIdList())) return false;
    if (hasReward() != other.hasReward()) return false;
    if (hasReward()) {
      if (!getReward()
          .equals(other.getReward())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasConditionId()) {
      hash = (37 * hash) + CONDITIONID_FIELD_NUMBER;
      hash = (53 * hash) + getConditionId();
    }
    if (hasGiftId()) {
      hash = (37 * hash) + GIFTID_FIELD_NUMBER;
      hash = (53 * hash) + getGiftId();
    }
    if (hasEndTime()) {
      hash = (37 * hash) + ENDTIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getEndTime());
    }
    if (getHasBeenBoughtCount() > 0) {
      hash = (37 * hash) + HASBEENBOUGHT_FIELD_NUMBER;
      hash = (53 * hash) + getHasBeenBoughtList().hashCode();
    }
    if (getTriggerMallIdCount() > 0) {
      hash = (37 * hash) + TRIGGERMALLID_FIELD_NUMBER;
      hash = (53 * hash) + getTriggerMallIdList().hashCode();
    }
    if (hasReward()) {
      hash = (37 * hash) + REWARD_FIELD_NUMBER;
      hash = (53 * hash) + getReward().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.ADGiftTriggerGiftDataMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ADGiftTriggerGiftDataMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ADGiftTriggerGiftDataMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ADGiftTriggerGiftDataMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ADGiftTriggerGiftDataMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ADGiftTriggerGiftDataMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ADGiftTriggerGiftDataMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.ADGiftTriggerGiftDataMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.ADGiftTriggerGiftDataMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.ADGiftTriggerGiftDataMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.ADGiftTriggerGiftDataMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.ADGiftTriggerGiftDataMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.ADGiftTriggerGiftDataMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.ADGiftTriggerGiftDataMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.ADGiftTriggerGiftDataMsg)
      xddq.pb.ADGiftTriggerGiftDataMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ADGiftTriggerGiftDataMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ADGiftTriggerGiftDataMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.ADGiftTriggerGiftDataMsg.class, xddq.pb.ADGiftTriggerGiftDataMsg.Builder.class);
    }

    // Construct using xddq.pb.ADGiftTriggerGiftDataMsg.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      conditionId_ = 0;
      giftId_ = 0;
      endTime_ = 0L;
      hasBeenBought_ = emptyIntList();
      triggerMallId_ = emptyIntList();
      reward_ = "";
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ADGiftTriggerGiftDataMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.ADGiftTriggerGiftDataMsg getDefaultInstanceForType() {
      return xddq.pb.ADGiftTriggerGiftDataMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.ADGiftTriggerGiftDataMsg build() {
      xddq.pb.ADGiftTriggerGiftDataMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.ADGiftTriggerGiftDataMsg buildPartial() {
      xddq.pb.ADGiftTriggerGiftDataMsg result = new xddq.pb.ADGiftTriggerGiftDataMsg(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.ADGiftTriggerGiftDataMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.conditionId_ = conditionId_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.giftId_ = giftId_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.endTime_ = endTime_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        hasBeenBought_.makeImmutable();
        result.hasBeenBought_ = hasBeenBought_;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        triggerMallId_.makeImmutable();
        result.triggerMallId_ = triggerMallId_;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.reward_ = reward_;
        to_bitField0_ |= 0x00000008;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.ADGiftTriggerGiftDataMsg) {
        return mergeFrom((xddq.pb.ADGiftTriggerGiftDataMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.ADGiftTriggerGiftDataMsg other) {
      if (other == xddq.pb.ADGiftTriggerGiftDataMsg.getDefaultInstance()) return this;
      if (other.hasConditionId()) {
        setConditionId(other.getConditionId());
      }
      if (other.hasGiftId()) {
        setGiftId(other.getGiftId());
      }
      if (other.hasEndTime()) {
        setEndTime(other.getEndTime());
      }
      if (!other.hasBeenBought_.isEmpty()) {
        if (hasBeenBought_.isEmpty()) {
          hasBeenBought_ = other.hasBeenBought_;
          hasBeenBought_.makeImmutable();
          bitField0_ |= 0x00000008;
        } else {
          ensureHasBeenBoughtIsMutable();
          hasBeenBought_.addAll(other.hasBeenBought_);
        }
        onChanged();
      }
      if (!other.triggerMallId_.isEmpty()) {
        if (triggerMallId_.isEmpty()) {
          triggerMallId_ = other.triggerMallId_;
          triggerMallId_.makeImmutable();
          bitField0_ |= 0x00000010;
        } else {
          ensureTriggerMallIdIsMutable();
          triggerMallId_.addAll(other.triggerMallId_);
        }
        onChanged();
      }
      if (other.hasReward()) {
        reward_ = other.reward_;
        bitField0_ |= 0x00000020;
        onChanged();
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasConditionId()) {
        return false;
      }
      if (!hasGiftId()) {
        return false;
      }
      if (!hasEndTime()) {
        return false;
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              conditionId_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              giftId_ = input.readInt32();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 24: {
              endTime_ = input.readInt64();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 32: {
              int v = input.readInt32();
              ensureHasBeenBoughtIsMutable();
              hasBeenBought_.addInt(v);
              break;
            } // case 32
            case 34: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              ensureHasBeenBoughtIsMutable();
              while (input.getBytesUntilLimit() > 0) {
                hasBeenBought_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            } // case 34
            case 40: {
              int v = input.readInt32();
              ensureTriggerMallIdIsMutable();
              triggerMallId_.addInt(v);
              break;
            } // case 40
            case 42: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              ensureTriggerMallIdIsMutable();
              while (input.getBytesUntilLimit() > 0) {
                triggerMallId_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            } // case 42
            case 50: {
              reward_ = input.readBytes();
              bitField0_ |= 0x00000020;
              break;
            } // case 50
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int conditionId_ ;
    /**
     * <code>required int32 conditionId = 1;</code>
     * @return Whether the conditionId field is set.
     */
    @java.lang.Override
    public boolean hasConditionId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 conditionId = 1;</code>
     * @return The conditionId.
     */
    @java.lang.Override
    public int getConditionId() {
      return conditionId_;
    }
    /**
     * <code>required int32 conditionId = 1;</code>
     * @param value The conditionId to set.
     * @return This builder for chaining.
     */
    public Builder setConditionId(int value) {

      conditionId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 conditionId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearConditionId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      conditionId_ = 0;
      onChanged();
      return this;
    }

    private int giftId_ ;
    /**
     * <code>required int32 giftId = 2;</code>
     * @return Whether the giftId field is set.
     */
    @java.lang.Override
    public boolean hasGiftId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>required int32 giftId = 2;</code>
     * @return The giftId.
     */
    @java.lang.Override
    public int getGiftId() {
      return giftId_;
    }
    /**
     * <code>required int32 giftId = 2;</code>
     * @param value The giftId to set.
     * @return This builder for chaining.
     */
    public Builder setGiftId(int value) {

      giftId_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 giftId = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearGiftId() {
      bitField0_ = (bitField0_ & ~0x00000002);
      giftId_ = 0;
      onChanged();
      return this;
    }

    private long endTime_ ;
    /**
     * <code>required int64 endTime = 3;</code>
     * @return Whether the endTime field is set.
     */
    @java.lang.Override
    public boolean hasEndTime() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>required int64 endTime = 3;</code>
     * @return The endTime.
     */
    @java.lang.Override
    public long getEndTime() {
      return endTime_;
    }
    /**
     * <code>required int64 endTime = 3;</code>
     * @param value The endTime to set.
     * @return This builder for chaining.
     */
    public Builder setEndTime(long value) {

      endTime_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>required int64 endTime = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearEndTime() {
      bitField0_ = (bitField0_ & ~0x00000004);
      endTime_ = 0L;
      onChanged();
      return this;
    }

    private com.google.protobuf.Internal.IntList hasBeenBought_ = emptyIntList();
    private void ensureHasBeenBoughtIsMutable() {
      if (!hasBeenBought_.isModifiable()) {
        hasBeenBought_ = makeMutableCopy(hasBeenBought_);
      }
      bitField0_ |= 0x00000008;
    }
    /**
     * <code>repeated int32 hasBeenBought = 4;</code>
     * @return A list containing the hasBeenBought.
     */
    public java.util.List<java.lang.Integer>
        getHasBeenBoughtList() {
      hasBeenBought_.makeImmutable();
      return hasBeenBought_;
    }
    /**
     * <code>repeated int32 hasBeenBought = 4;</code>
     * @return The count of hasBeenBought.
     */
    public int getHasBeenBoughtCount() {
      return hasBeenBought_.size();
    }
    /**
     * <code>repeated int32 hasBeenBought = 4;</code>
     * @param index The index of the element to return.
     * @return The hasBeenBought at the given index.
     */
    public int getHasBeenBought(int index) {
      return hasBeenBought_.getInt(index);
    }
    /**
     * <code>repeated int32 hasBeenBought = 4;</code>
     * @param index The index to set the value at.
     * @param value The hasBeenBought to set.
     * @return This builder for chaining.
     */
    public Builder setHasBeenBought(
        int index, int value) {

      ensureHasBeenBoughtIsMutable();
      hasBeenBought_.setInt(index, value);
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 hasBeenBought = 4;</code>
     * @param value The hasBeenBought to add.
     * @return This builder for chaining.
     */
    public Builder addHasBeenBought(int value) {

      ensureHasBeenBoughtIsMutable();
      hasBeenBought_.addInt(value);
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 hasBeenBought = 4;</code>
     * @param values The hasBeenBought to add.
     * @return This builder for chaining.
     */
    public Builder addAllHasBeenBought(
        java.lang.Iterable<? extends java.lang.Integer> values) {
      ensureHasBeenBoughtIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, hasBeenBought_);
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 hasBeenBought = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearHasBeenBought() {
      hasBeenBought_ = emptyIntList();
      bitField0_ = (bitField0_ & ~0x00000008);
      onChanged();
      return this;
    }

    private com.google.protobuf.Internal.IntList triggerMallId_ = emptyIntList();
    private void ensureTriggerMallIdIsMutable() {
      if (!triggerMallId_.isModifiable()) {
        triggerMallId_ = makeMutableCopy(triggerMallId_);
      }
      bitField0_ |= 0x00000010;
    }
    /**
     * <code>repeated int32 triggerMallId = 5;</code>
     * @return A list containing the triggerMallId.
     */
    public java.util.List<java.lang.Integer>
        getTriggerMallIdList() {
      triggerMallId_.makeImmutable();
      return triggerMallId_;
    }
    /**
     * <code>repeated int32 triggerMallId = 5;</code>
     * @return The count of triggerMallId.
     */
    public int getTriggerMallIdCount() {
      return triggerMallId_.size();
    }
    /**
     * <code>repeated int32 triggerMallId = 5;</code>
     * @param index The index of the element to return.
     * @return The triggerMallId at the given index.
     */
    public int getTriggerMallId(int index) {
      return triggerMallId_.getInt(index);
    }
    /**
     * <code>repeated int32 triggerMallId = 5;</code>
     * @param index The index to set the value at.
     * @param value The triggerMallId to set.
     * @return This builder for chaining.
     */
    public Builder setTriggerMallId(
        int index, int value) {

      ensureTriggerMallIdIsMutable();
      triggerMallId_.setInt(index, value);
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 triggerMallId = 5;</code>
     * @param value The triggerMallId to add.
     * @return This builder for chaining.
     */
    public Builder addTriggerMallId(int value) {

      ensureTriggerMallIdIsMutable();
      triggerMallId_.addInt(value);
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 triggerMallId = 5;</code>
     * @param values The triggerMallId to add.
     * @return This builder for chaining.
     */
    public Builder addAllTriggerMallId(
        java.lang.Iterable<? extends java.lang.Integer> values) {
      ensureTriggerMallIdIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, triggerMallId_);
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 triggerMallId = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearTriggerMallId() {
      triggerMallId_ = emptyIntList();
      bitField0_ = (bitField0_ & ~0x00000010);
      onChanged();
      return this;
    }

    private java.lang.Object reward_ = "";
    /**
     * <code>optional string reward = 6;</code>
     * @return Whether the reward field is set.
     */
    public boolean hasReward() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional string reward = 6;</code>
     * @return The reward.
     */
    public java.lang.String getReward() {
      java.lang.Object ref = reward_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          reward_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string reward = 6;</code>
     * @return The bytes for reward.
     */
    public com.google.protobuf.ByteString
        getRewardBytes() {
      java.lang.Object ref = reward_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        reward_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string reward = 6;</code>
     * @param value The reward to set.
     * @return This builder for chaining.
     */
    public Builder setReward(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      reward_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>optional string reward = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearReward() {
      reward_ = getDefaultInstance().getReward();
      bitField0_ = (bitField0_ & ~0x00000020);
      onChanged();
      return this;
    }
    /**
     * <code>optional string reward = 6;</code>
     * @param value The bytes for reward to set.
     * @return This builder for chaining.
     */
    public Builder setRewardBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      reward_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.ADGiftTriggerGiftDataMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.ADGiftTriggerGiftDataMsg)
  private static final xddq.pb.ADGiftTriggerGiftDataMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.ADGiftTriggerGiftDataMsg();
  }

  public static xddq.pb.ADGiftTriggerGiftDataMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ADGiftTriggerGiftDataMsg>
      PARSER = new com.google.protobuf.AbstractParser<ADGiftTriggerGiftDataMsg>() {
    @java.lang.Override
    public ADGiftTriggerGiftDataMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<ADGiftTriggerGiftDataMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ADGiftTriggerGiftDataMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.ADGiftTriggerGiftDataMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

