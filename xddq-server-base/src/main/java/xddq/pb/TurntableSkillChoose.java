// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.TurntableSkillChoose}
 */
public final class TurntableSkillChoose extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.TurntableSkillChoose)
    TurntableSkillChooseOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      TurntableSkillChoose.class.getName());
  }
  // Use TurntableSkillChoose.newBuilder() to construct.
  private TurntableSkillChoose(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private TurntableSkillChoose() {
    options_ = java.util.Collections.emptyList();
    selectIndex_ = emptyIntList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_TurntableSkillChoose_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_TurntableSkillChoose_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.TurntableSkillChoose.class, xddq.pb.TurntableSkillChoose.Builder.class);
  }

  private int bitField0_;
  public static final int OPTIONS_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.SkillChooseOption> options_;
  /**
   * <code>repeated .xddq.pb.SkillChooseOption options = 1;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.SkillChooseOption> getOptionsList() {
    return options_;
  }
  /**
   * <code>repeated .xddq.pb.SkillChooseOption options = 1;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.SkillChooseOptionOrBuilder> 
      getOptionsOrBuilderList() {
    return options_;
  }
  /**
   * <code>repeated .xddq.pb.SkillChooseOption options = 1;</code>
   */
  @java.lang.Override
  public int getOptionsCount() {
    return options_.size();
  }
  /**
   * <code>repeated .xddq.pb.SkillChooseOption options = 1;</code>
   */
  @java.lang.Override
  public xddq.pb.SkillChooseOption getOptions(int index) {
    return options_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.SkillChooseOption options = 1;</code>
   */
  @java.lang.Override
  public xddq.pb.SkillChooseOptionOrBuilder getOptionsOrBuilder(
      int index) {
    return options_.get(index);
  }

  public static final int MULTISELECT_FIELD_NUMBER = 2;
  private int multiSelect_ = 0;
  /**
   * <code>optional int32 multiSelect = 2;</code>
   * @return Whether the multiSelect field is set.
   */
  @java.lang.Override
  public boolean hasMultiSelect() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int32 multiSelect = 2;</code>
   * @return The multiSelect.
   */
  @java.lang.Override
  public int getMultiSelect() {
    return multiSelect_;
  }

  public static final int SELECTINDEX_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private com.google.protobuf.Internal.IntList selectIndex_ =
      emptyIntList();
  /**
   * <code>repeated int32 selectIndex = 3;</code>
   * @return A list containing the selectIndex.
   */
  @java.lang.Override
  public java.util.List<java.lang.Integer>
      getSelectIndexList() {
    return selectIndex_;
  }
  /**
   * <code>repeated int32 selectIndex = 3;</code>
   * @return The count of selectIndex.
   */
  public int getSelectIndexCount() {
    return selectIndex_.size();
  }
  /**
   * <code>repeated int32 selectIndex = 3;</code>
   * @param index The index of the element to return.
   * @return The selectIndex at the given index.
   */
  public int getSelectIndex(int index) {
    return selectIndex_.getInt(index);
  }

  public static final int ADRELOADNUM_FIELD_NUMBER = 4;
  private int adReloadNum_ = 0;
  /**
   * <code>optional int32 adReloadNum = 4;</code>
   * @return Whether the adReloadNum field is set.
   */
  @java.lang.Override
  public boolean hasAdReloadNum() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int32 adReloadNum = 4;</code>
   * @return The adReloadNum.
   */
  @java.lang.Override
  public int getAdReloadNum() {
    return adReloadNum_;
  }

  public static final int ADRELOADLIMIT_FIELD_NUMBER = 5;
  private int adReloadLimit_ = 0;
  /**
   * <code>optional int32 adReloadLimit = 5;</code>
   * @return Whether the adReloadLimit field is set.
   */
  @java.lang.Override
  public boolean hasAdReloadLimit() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional int32 adReloadLimit = 5;</code>
   * @return The adReloadLimit.
   */
  @java.lang.Override
  public int getAdReloadLimit() {
    return adReloadLimit_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    for (int i = 0; i < options_.size(); i++) {
      output.writeMessage(1, options_.get(i));
    }
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(2, multiSelect_);
    }
    for (int i = 0; i < selectIndex_.size(); i++) {
      output.writeInt32(3, selectIndex_.getInt(i));
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(4, adReloadNum_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt32(5, adReloadLimit_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    for (int i = 0; i < options_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, options_.get(i));
    }
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, multiSelect_);
    }
    {
      int dataSize = 0;
      for (int i = 0; i < selectIndex_.size(); i++) {
        dataSize += com.google.protobuf.CodedOutputStream
          .computeInt32SizeNoTag(selectIndex_.getInt(i));
      }
      size += dataSize;
      size += 1 * getSelectIndexList().size();
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(4, adReloadNum_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(5, adReloadLimit_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.TurntableSkillChoose)) {
      return super.equals(obj);
    }
    xddq.pb.TurntableSkillChoose other = (xddq.pb.TurntableSkillChoose) obj;

    if (!getOptionsList()
        .equals(other.getOptionsList())) return false;
    if (hasMultiSelect() != other.hasMultiSelect()) return false;
    if (hasMultiSelect()) {
      if (getMultiSelect()
          != other.getMultiSelect()) return false;
    }
    if (!getSelectIndexList()
        .equals(other.getSelectIndexList())) return false;
    if (hasAdReloadNum() != other.hasAdReloadNum()) return false;
    if (hasAdReloadNum()) {
      if (getAdReloadNum()
          != other.getAdReloadNum()) return false;
    }
    if (hasAdReloadLimit() != other.hasAdReloadLimit()) return false;
    if (hasAdReloadLimit()) {
      if (getAdReloadLimit()
          != other.getAdReloadLimit()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (getOptionsCount() > 0) {
      hash = (37 * hash) + OPTIONS_FIELD_NUMBER;
      hash = (53 * hash) + getOptionsList().hashCode();
    }
    if (hasMultiSelect()) {
      hash = (37 * hash) + MULTISELECT_FIELD_NUMBER;
      hash = (53 * hash) + getMultiSelect();
    }
    if (getSelectIndexCount() > 0) {
      hash = (37 * hash) + SELECTINDEX_FIELD_NUMBER;
      hash = (53 * hash) + getSelectIndexList().hashCode();
    }
    if (hasAdReloadNum()) {
      hash = (37 * hash) + ADRELOADNUM_FIELD_NUMBER;
      hash = (53 * hash) + getAdReloadNum();
    }
    if (hasAdReloadLimit()) {
      hash = (37 * hash) + ADRELOADLIMIT_FIELD_NUMBER;
      hash = (53 * hash) + getAdReloadLimit();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.TurntableSkillChoose parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.TurntableSkillChoose parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.TurntableSkillChoose parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.TurntableSkillChoose parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.TurntableSkillChoose parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.TurntableSkillChoose parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.TurntableSkillChoose parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.TurntableSkillChoose parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.TurntableSkillChoose parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.TurntableSkillChoose parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.TurntableSkillChoose parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.TurntableSkillChoose parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.TurntableSkillChoose prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.TurntableSkillChoose}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.TurntableSkillChoose)
      xddq.pb.TurntableSkillChooseOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_TurntableSkillChoose_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_TurntableSkillChoose_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.TurntableSkillChoose.class, xddq.pb.TurntableSkillChoose.Builder.class);
    }

    // Construct using xddq.pb.TurntableSkillChoose.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      if (optionsBuilder_ == null) {
        options_ = java.util.Collections.emptyList();
      } else {
        options_ = null;
        optionsBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000001);
      multiSelect_ = 0;
      selectIndex_ = emptyIntList();
      adReloadNum_ = 0;
      adReloadLimit_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_TurntableSkillChoose_descriptor;
    }

    @java.lang.Override
    public xddq.pb.TurntableSkillChoose getDefaultInstanceForType() {
      return xddq.pb.TurntableSkillChoose.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.TurntableSkillChoose build() {
      xddq.pb.TurntableSkillChoose result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.TurntableSkillChoose buildPartial() {
      xddq.pb.TurntableSkillChoose result = new xddq.pb.TurntableSkillChoose(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.TurntableSkillChoose result) {
      if (optionsBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          options_ = java.util.Collections.unmodifiableList(options_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.options_ = options_;
      } else {
        result.options_ = optionsBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.TurntableSkillChoose result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.multiSelect_ = multiSelect_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        selectIndex_.makeImmutable();
        result.selectIndex_ = selectIndex_;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.adReloadNum_ = adReloadNum_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.adReloadLimit_ = adReloadLimit_;
        to_bitField0_ |= 0x00000004;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.TurntableSkillChoose) {
        return mergeFrom((xddq.pb.TurntableSkillChoose)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.TurntableSkillChoose other) {
      if (other == xddq.pb.TurntableSkillChoose.getDefaultInstance()) return this;
      if (optionsBuilder_ == null) {
        if (!other.options_.isEmpty()) {
          if (options_.isEmpty()) {
            options_ = other.options_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureOptionsIsMutable();
            options_.addAll(other.options_);
          }
          onChanged();
        }
      } else {
        if (!other.options_.isEmpty()) {
          if (optionsBuilder_.isEmpty()) {
            optionsBuilder_.dispose();
            optionsBuilder_ = null;
            options_ = other.options_;
            bitField0_ = (bitField0_ & ~0x00000001);
            optionsBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetOptionsFieldBuilder() : null;
          } else {
            optionsBuilder_.addAllMessages(other.options_);
          }
        }
      }
      if (other.hasMultiSelect()) {
        setMultiSelect(other.getMultiSelect());
      }
      if (!other.selectIndex_.isEmpty()) {
        if (selectIndex_.isEmpty()) {
          selectIndex_ = other.selectIndex_;
          selectIndex_.makeImmutable();
          bitField0_ |= 0x00000004;
        } else {
          ensureSelectIndexIsMutable();
          selectIndex_.addAll(other.selectIndex_);
        }
        onChanged();
      }
      if (other.hasAdReloadNum()) {
        setAdReloadNum(other.getAdReloadNum());
      }
      if (other.hasAdReloadLimit()) {
        setAdReloadLimit(other.getAdReloadLimit());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              xddq.pb.SkillChooseOption m =
                  input.readMessage(
                      xddq.pb.SkillChooseOption.parser(),
                      extensionRegistry);
              if (optionsBuilder_ == null) {
                ensureOptionsIsMutable();
                options_.add(m);
              } else {
                optionsBuilder_.addMessage(m);
              }
              break;
            } // case 10
            case 16: {
              multiSelect_ = input.readInt32();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 24: {
              int v = input.readInt32();
              ensureSelectIndexIsMutable();
              selectIndex_.addInt(v);
              break;
            } // case 24
            case 26: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              ensureSelectIndexIsMutable();
              while (input.getBytesUntilLimit() > 0) {
                selectIndex_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            } // case 26
            case 32: {
              adReloadNum_ = input.readInt32();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            case 40: {
              adReloadLimit_ = input.readInt32();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.util.List<xddq.pb.SkillChooseOption> options_ =
      java.util.Collections.emptyList();
    private void ensureOptionsIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        options_ = new java.util.ArrayList<xddq.pb.SkillChooseOption>(options_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.SkillChooseOption, xddq.pb.SkillChooseOption.Builder, xddq.pb.SkillChooseOptionOrBuilder> optionsBuilder_;

    /**
     * <code>repeated .xddq.pb.SkillChooseOption options = 1;</code>
     */
    public java.util.List<xddq.pb.SkillChooseOption> getOptionsList() {
      if (optionsBuilder_ == null) {
        return java.util.Collections.unmodifiableList(options_);
      } else {
        return optionsBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.SkillChooseOption options = 1;</code>
     */
    public int getOptionsCount() {
      if (optionsBuilder_ == null) {
        return options_.size();
      } else {
        return optionsBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.SkillChooseOption options = 1;</code>
     */
    public xddq.pb.SkillChooseOption getOptions(int index) {
      if (optionsBuilder_ == null) {
        return options_.get(index);
      } else {
        return optionsBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.SkillChooseOption options = 1;</code>
     */
    public Builder setOptions(
        int index, xddq.pb.SkillChooseOption value) {
      if (optionsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureOptionsIsMutable();
        options_.set(index, value);
        onChanged();
      } else {
        optionsBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.SkillChooseOption options = 1;</code>
     */
    public Builder setOptions(
        int index, xddq.pb.SkillChooseOption.Builder builderForValue) {
      if (optionsBuilder_ == null) {
        ensureOptionsIsMutable();
        options_.set(index, builderForValue.build());
        onChanged();
      } else {
        optionsBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.SkillChooseOption options = 1;</code>
     */
    public Builder addOptions(xddq.pb.SkillChooseOption value) {
      if (optionsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureOptionsIsMutable();
        options_.add(value);
        onChanged();
      } else {
        optionsBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.SkillChooseOption options = 1;</code>
     */
    public Builder addOptions(
        int index, xddq.pb.SkillChooseOption value) {
      if (optionsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureOptionsIsMutable();
        options_.add(index, value);
        onChanged();
      } else {
        optionsBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.SkillChooseOption options = 1;</code>
     */
    public Builder addOptions(
        xddq.pb.SkillChooseOption.Builder builderForValue) {
      if (optionsBuilder_ == null) {
        ensureOptionsIsMutable();
        options_.add(builderForValue.build());
        onChanged();
      } else {
        optionsBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.SkillChooseOption options = 1;</code>
     */
    public Builder addOptions(
        int index, xddq.pb.SkillChooseOption.Builder builderForValue) {
      if (optionsBuilder_ == null) {
        ensureOptionsIsMutable();
        options_.add(index, builderForValue.build());
        onChanged();
      } else {
        optionsBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.SkillChooseOption options = 1;</code>
     */
    public Builder addAllOptions(
        java.lang.Iterable<? extends xddq.pb.SkillChooseOption> values) {
      if (optionsBuilder_ == null) {
        ensureOptionsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, options_);
        onChanged();
      } else {
        optionsBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.SkillChooseOption options = 1;</code>
     */
    public Builder clearOptions() {
      if (optionsBuilder_ == null) {
        options_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        optionsBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.SkillChooseOption options = 1;</code>
     */
    public Builder removeOptions(int index) {
      if (optionsBuilder_ == null) {
        ensureOptionsIsMutable();
        options_.remove(index);
        onChanged();
      } else {
        optionsBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.SkillChooseOption options = 1;</code>
     */
    public xddq.pb.SkillChooseOption.Builder getOptionsBuilder(
        int index) {
      return internalGetOptionsFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.SkillChooseOption options = 1;</code>
     */
    public xddq.pb.SkillChooseOptionOrBuilder getOptionsOrBuilder(
        int index) {
      if (optionsBuilder_ == null) {
        return options_.get(index);  } else {
        return optionsBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.SkillChooseOption options = 1;</code>
     */
    public java.util.List<? extends xddq.pb.SkillChooseOptionOrBuilder> 
         getOptionsOrBuilderList() {
      if (optionsBuilder_ != null) {
        return optionsBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(options_);
      }
    }
    /**
     * <code>repeated .xddq.pb.SkillChooseOption options = 1;</code>
     */
    public xddq.pb.SkillChooseOption.Builder addOptionsBuilder() {
      return internalGetOptionsFieldBuilder().addBuilder(
          xddq.pb.SkillChooseOption.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.SkillChooseOption options = 1;</code>
     */
    public xddq.pb.SkillChooseOption.Builder addOptionsBuilder(
        int index) {
      return internalGetOptionsFieldBuilder().addBuilder(
          index, xddq.pb.SkillChooseOption.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.SkillChooseOption options = 1;</code>
     */
    public java.util.List<xddq.pb.SkillChooseOption.Builder> 
         getOptionsBuilderList() {
      return internalGetOptionsFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.SkillChooseOption, xddq.pb.SkillChooseOption.Builder, xddq.pb.SkillChooseOptionOrBuilder> 
        internalGetOptionsFieldBuilder() {
      if (optionsBuilder_ == null) {
        optionsBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.SkillChooseOption, xddq.pb.SkillChooseOption.Builder, xddq.pb.SkillChooseOptionOrBuilder>(
                options_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        options_ = null;
      }
      return optionsBuilder_;
    }

    private int multiSelect_ ;
    /**
     * <code>optional int32 multiSelect = 2;</code>
     * @return Whether the multiSelect field is set.
     */
    @java.lang.Override
    public boolean hasMultiSelect() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 multiSelect = 2;</code>
     * @return The multiSelect.
     */
    @java.lang.Override
    public int getMultiSelect() {
      return multiSelect_;
    }
    /**
     * <code>optional int32 multiSelect = 2;</code>
     * @param value The multiSelect to set.
     * @return This builder for chaining.
     */
    public Builder setMultiSelect(int value) {

      multiSelect_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 multiSelect = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearMultiSelect() {
      bitField0_ = (bitField0_ & ~0x00000002);
      multiSelect_ = 0;
      onChanged();
      return this;
    }

    private com.google.protobuf.Internal.IntList selectIndex_ = emptyIntList();
    private void ensureSelectIndexIsMutable() {
      if (!selectIndex_.isModifiable()) {
        selectIndex_ = makeMutableCopy(selectIndex_);
      }
      bitField0_ |= 0x00000004;
    }
    /**
     * <code>repeated int32 selectIndex = 3;</code>
     * @return A list containing the selectIndex.
     */
    public java.util.List<java.lang.Integer>
        getSelectIndexList() {
      selectIndex_.makeImmutable();
      return selectIndex_;
    }
    /**
     * <code>repeated int32 selectIndex = 3;</code>
     * @return The count of selectIndex.
     */
    public int getSelectIndexCount() {
      return selectIndex_.size();
    }
    /**
     * <code>repeated int32 selectIndex = 3;</code>
     * @param index The index of the element to return.
     * @return The selectIndex at the given index.
     */
    public int getSelectIndex(int index) {
      return selectIndex_.getInt(index);
    }
    /**
     * <code>repeated int32 selectIndex = 3;</code>
     * @param index The index to set the value at.
     * @param value The selectIndex to set.
     * @return This builder for chaining.
     */
    public Builder setSelectIndex(
        int index, int value) {

      ensureSelectIndexIsMutable();
      selectIndex_.setInt(index, value);
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 selectIndex = 3;</code>
     * @param value The selectIndex to add.
     * @return This builder for chaining.
     */
    public Builder addSelectIndex(int value) {

      ensureSelectIndexIsMutable();
      selectIndex_.addInt(value);
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 selectIndex = 3;</code>
     * @param values The selectIndex to add.
     * @return This builder for chaining.
     */
    public Builder addAllSelectIndex(
        java.lang.Iterable<? extends java.lang.Integer> values) {
      ensureSelectIndexIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, selectIndex_);
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 selectIndex = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearSelectIndex() {
      selectIndex_ = emptyIntList();
      bitField0_ = (bitField0_ & ~0x00000004);
      onChanged();
      return this;
    }

    private int adReloadNum_ ;
    /**
     * <code>optional int32 adReloadNum = 4;</code>
     * @return Whether the adReloadNum field is set.
     */
    @java.lang.Override
    public boolean hasAdReloadNum() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int32 adReloadNum = 4;</code>
     * @return The adReloadNum.
     */
    @java.lang.Override
    public int getAdReloadNum() {
      return adReloadNum_;
    }
    /**
     * <code>optional int32 adReloadNum = 4;</code>
     * @param value The adReloadNum to set.
     * @return This builder for chaining.
     */
    public Builder setAdReloadNum(int value) {

      adReloadNum_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 adReloadNum = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearAdReloadNum() {
      bitField0_ = (bitField0_ & ~0x00000008);
      adReloadNum_ = 0;
      onChanged();
      return this;
    }

    private int adReloadLimit_ ;
    /**
     * <code>optional int32 adReloadLimit = 5;</code>
     * @return Whether the adReloadLimit field is set.
     */
    @java.lang.Override
    public boolean hasAdReloadLimit() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional int32 adReloadLimit = 5;</code>
     * @return The adReloadLimit.
     */
    @java.lang.Override
    public int getAdReloadLimit() {
      return adReloadLimit_;
    }
    /**
     * <code>optional int32 adReloadLimit = 5;</code>
     * @param value The adReloadLimit to set.
     * @return This builder for chaining.
     */
    public Builder setAdReloadLimit(int value) {

      adReloadLimit_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 adReloadLimit = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearAdReloadLimit() {
      bitField0_ = (bitField0_ & ~0x00000010);
      adReloadLimit_ = 0;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.TurntableSkillChoose)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.TurntableSkillChoose)
  private static final xddq.pb.TurntableSkillChoose DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.TurntableSkillChoose();
  }

  public static xddq.pb.TurntableSkillChoose getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<TurntableSkillChoose>
      PARSER = new com.google.protobuf.AbstractParser<TurntableSkillChoose>() {
    @java.lang.Override
    public TurntableSkillChoose parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<TurntableSkillChoose> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<TurntableSkillChoose> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.TurntableSkillChoose getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

