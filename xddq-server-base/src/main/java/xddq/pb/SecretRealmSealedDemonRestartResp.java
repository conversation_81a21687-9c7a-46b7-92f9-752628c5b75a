// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.SecretRealmSealedDemonRestartResp}
 */
public final class SecretRealmSealedDemonRestartResp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.SecretRealmSealedDemonRestartResp)
    SecretRealmSealedDemonRestartRespOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      SecretRealmSealedDemonRestartResp.class.getName());
  }
  // Use SecretRealmSealedDemonRestartResp.newBuilder() to construct.
  private SecretRealmSealedDemonRestartResp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private SecretRealmSealedDemonRestartResp() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_SecretRealmSealedDemonRestartResp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_SecretRealmSealedDemonRestartResp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.SecretRealmSealedDemonRestartResp.class, xddq.pb.SecretRealmSealedDemonRestartResp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int PLAYERDATAMSG_FIELD_NUMBER = 2;
  private xddq.pb.SecretRealmSealedDemonActivityPlayerDataMsg playerDataMsg_;
  /**
   * <code>optional .xddq.pb.SecretRealmSealedDemonActivityPlayerDataMsg playerDataMsg = 2;</code>
   * @return Whether the playerDataMsg field is set.
   */
  @java.lang.Override
  public boolean hasPlayerDataMsg() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional .xddq.pb.SecretRealmSealedDemonActivityPlayerDataMsg playerDataMsg = 2;</code>
   * @return The playerDataMsg.
   */
  @java.lang.Override
  public xddq.pb.SecretRealmSealedDemonActivityPlayerDataMsg getPlayerDataMsg() {
    return playerDataMsg_ == null ? xddq.pb.SecretRealmSealedDemonActivityPlayerDataMsg.getDefaultInstance() : playerDataMsg_;
  }
  /**
   * <code>optional .xddq.pb.SecretRealmSealedDemonActivityPlayerDataMsg playerDataMsg = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.SecretRealmSealedDemonActivityPlayerDataMsgOrBuilder getPlayerDataMsgOrBuilder() {
    return playerDataMsg_ == null ? xddq.pb.SecretRealmSealedDemonActivityPlayerDataMsg.getDefaultInstance() : playerDataMsg_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (hasPlayerDataMsg()) {
      if (!getPlayerDataMsg().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeMessage(2, getPlayerDataMsg());
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getPlayerDataMsg());
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.SecretRealmSealedDemonRestartResp)) {
      return super.equals(obj);
    }
    xddq.pb.SecretRealmSealedDemonRestartResp other = (xddq.pb.SecretRealmSealedDemonRestartResp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (hasPlayerDataMsg() != other.hasPlayerDataMsg()) return false;
    if (hasPlayerDataMsg()) {
      if (!getPlayerDataMsg()
          .equals(other.getPlayerDataMsg())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (hasPlayerDataMsg()) {
      hash = (37 * hash) + PLAYERDATAMSG_FIELD_NUMBER;
      hash = (53 * hash) + getPlayerDataMsg().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.SecretRealmSealedDemonRestartResp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.SecretRealmSealedDemonRestartResp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.SecretRealmSealedDemonRestartResp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.SecretRealmSealedDemonRestartResp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.SecretRealmSealedDemonRestartResp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.SecretRealmSealedDemonRestartResp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.SecretRealmSealedDemonRestartResp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.SecretRealmSealedDemonRestartResp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.SecretRealmSealedDemonRestartResp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.SecretRealmSealedDemonRestartResp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.SecretRealmSealedDemonRestartResp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.SecretRealmSealedDemonRestartResp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.SecretRealmSealedDemonRestartResp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.SecretRealmSealedDemonRestartResp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.SecretRealmSealedDemonRestartResp)
      xddq.pb.SecretRealmSealedDemonRestartRespOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_SecretRealmSealedDemonRestartResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_SecretRealmSealedDemonRestartResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.SecretRealmSealedDemonRestartResp.class, xddq.pb.SecretRealmSealedDemonRestartResp.Builder.class);
    }

    // Construct using xddq.pb.SecretRealmSealedDemonRestartResp.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetPlayerDataMsgFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      playerDataMsg_ = null;
      if (playerDataMsgBuilder_ != null) {
        playerDataMsgBuilder_.dispose();
        playerDataMsgBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_SecretRealmSealedDemonRestartResp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.SecretRealmSealedDemonRestartResp getDefaultInstanceForType() {
      return xddq.pb.SecretRealmSealedDemonRestartResp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.SecretRealmSealedDemonRestartResp build() {
      xddq.pb.SecretRealmSealedDemonRestartResp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.SecretRealmSealedDemonRestartResp buildPartial() {
      xddq.pb.SecretRealmSealedDemonRestartResp result = new xddq.pb.SecretRealmSealedDemonRestartResp(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.SecretRealmSealedDemonRestartResp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.playerDataMsg_ = playerDataMsgBuilder_ == null
            ? playerDataMsg_
            : playerDataMsgBuilder_.build();
        to_bitField0_ |= 0x00000002;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.SecretRealmSealedDemonRestartResp) {
        return mergeFrom((xddq.pb.SecretRealmSealedDemonRestartResp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.SecretRealmSealedDemonRestartResp other) {
      if (other == xddq.pb.SecretRealmSealedDemonRestartResp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (other.hasPlayerDataMsg()) {
        mergePlayerDataMsg(other.getPlayerDataMsg());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      if (hasPlayerDataMsg()) {
        if (!getPlayerDataMsg().isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              input.readMessage(
                  internalGetPlayerDataMsgFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private xddq.pb.SecretRealmSealedDemonActivityPlayerDataMsg playerDataMsg_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.SecretRealmSealedDemonActivityPlayerDataMsg, xddq.pb.SecretRealmSealedDemonActivityPlayerDataMsg.Builder, xddq.pb.SecretRealmSealedDemonActivityPlayerDataMsgOrBuilder> playerDataMsgBuilder_;
    /**
     * <code>optional .xddq.pb.SecretRealmSealedDemonActivityPlayerDataMsg playerDataMsg = 2;</code>
     * @return Whether the playerDataMsg field is set.
     */
    public boolean hasPlayerDataMsg() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .xddq.pb.SecretRealmSealedDemonActivityPlayerDataMsg playerDataMsg = 2;</code>
     * @return The playerDataMsg.
     */
    public xddq.pb.SecretRealmSealedDemonActivityPlayerDataMsg getPlayerDataMsg() {
      if (playerDataMsgBuilder_ == null) {
        return playerDataMsg_ == null ? xddq.pb.SecretRealmSealedDemonActivityPlayerDataMsg.getDefaultInstance() : playerDataMsg_;
      } else {
        return playerDataMsgBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.SecretRealmSealedDemonActivityPlayerDataMsg playerDataMsg = 2;</code>
     */
    public Builder setPlayerDataMsg(xddq.pb.SecretRealmSealedDemonActivityPlayerDataMsg value) {
      if (playerDataMsgBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        playerDataMsg_ = value;
      } else {
        playerDataMsgBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.SecretRealmSealedDemonActivityPlayerDataMsg playerDataMsg = 2;</code>
     */
    public Builder setPlayerDataMsg(
        xddq.pb.SecretRealmSealedDemonActivityPlayerDataMsg.Builder builderForValue) {
      if (playerDataMsgBuilder_ == null) {
        playerDataMsg_ = builderForValue.build();
      } else {
        playerDataMsgBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.SecretRealmSealedDemonActivityPlayerDataMsg playerDataMsg = 2;</code>
     */
    public Builder mergePlayerDataMsg(xddq.pb.SecretRealmSealedDemonActivityPlayerDataMsg value) {
      if (playerDataMsgBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0) &&
          playerDataMsg_ != null &&
          playerDataMsg_ != xddq.pb.SecretRealmSealedDemonActivityPlayerDataMsg.getDefaultInstance()) {
          getPlayerDataMsgBuilder().mergeFrom(value);
        } else {
          playerDataMsg_ = value;
        }
      } else {
        playerDataMsgBuilder_.mergeFrom(value);
      }
      if (playerDataMsg_ != null) {
        bitField0_ |= 0x00000002;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.SecretRealmSealedDemonActivityPlayerDataMsg playerDataMsg = 2;</code>
     */
    public Builder clearPlayerDataMsg() {
      bitField0_ = (bitField0_ & ~0x00000002);
      playerDataMsg_ = null;
      if (playerDataMsgBuilder_ != null) {
        playerDataMsgBuilder_.dispose();
        playerDataMsgBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.SecretRealmSealedDemonActivityPlayerDataMsg playerDataMsg = 2;</code>
     */
    public xddq.pb.SecretRealmSealedDemonActivityPlayerDataMsg.Builder getPlayerDataMsgBuilder() {
      bitField0_ |= 0x00000002;
      onChanged();
      return internalGetPlayerDataMsgFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.SecretRealmSealedDemonActivityPlayerDataMsg playerDataMsg = 2;</code>
     */
    public xddq.pb.SecretRealmSealedDemonActivityPlayerDataMsgOrBuilder getPlayerDataMsgOrBuilder() {
      if (playerDataMsgBuilder_ != null) {
        return playerDataMsgBuilder_.getMessageOrBuilder();
      } else {
        return playerDataMsg_ == null ?
            xddq.pb.SecretRealmSealedDemonActivityPlayerDataMsg.getDefaultInstance() : playerDataMsg_;
      }
    }
    /**
     * <code>optional .xddq.pb.SecretRealmSealedDemonActivityPlayerDataMsg playerDataMsg = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.SecretRealmSealedDemonActivityPlayerDataMsg, xddq.pb.SecretRealmSealedDemonActivityPlayerDataMsg.Builder, xddq.pb.SecretRealmSealedDemonActivityPlayerDataMsgOrBuilder> 
        internalGetPlayerDataMsgFieldBuilder() {
      if (playerDataMsgBuilder_ == null) {
        playerDataMsgBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.SecretRealmSealedDemonActivityPlayerDataMsg, xddq.pb.SecretRealmSealedDemonActivityPlayerDataMsg.Builder, xddq.pb.SecretRealmSealedDemonActivityPlayerDataMsgOrBuilder>(
                getPlayerDataMsg(),
                getParentForChildren(),
                isClean());
        playerDataMsg_ = null;
      }
      return playerDataMsgBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.SecretRealmSealedDemonRestartResp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.SecretRealmSealedDemonRestartResp)
  private static final xddq.pb.SecretRealmSealedDemonRestartResp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.SecretRealmSealedDemonRestartResp();
  }

  public static xddq.pb.SecretRealmSealedDemonRestartResp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<SecretRealmSealedDemonRestartResp>
      PARSER = new com.google.protobuf.AbstractParser<SecretRealmSealedDemonRestartResp>() {
    @java.lang.Override
    public SecretRealmSealedDemonRestartResp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<SecretRealmSealedDemonRestartResp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<SecretRealmSealedDemonRestartResp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.SecretRealmSealedDemonRestartResp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

