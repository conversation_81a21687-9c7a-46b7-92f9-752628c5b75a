// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.UnionRechargeUserRspMsg}
 */
public final class UnionRechargeUserRspMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.UnionRechargeUserRspMsg)
    UnionRechargeUserRspMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      UnionRechargeUserRspMsg.class.getName());
  }
  // Use UnionRechargeUserRspMsg.newBuilder() to construct.
  private UnionRechargeUserRspMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private UnionRechargeUserRspMsg() {
    rechargeUser_ = java.util.Collections.emptyList();
    getRewardData_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_UnionRechargeUserRspMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_UnionRechargeUserRspMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.UnionRechargeUserRspMsg.class, xddq.pb.UnionRechargeUserRspMsg.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int ACTIVITYID_FIELD_NUMBER = 2;
  private int activityId_ = 0;
  /**
   * <code>optional int32 activityId = 2;</code>
   * @return Whether the activityId field is set.
   */
  @java.lang.Override
  public boolean hasActivityId() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int32 activityId = 2;</code>
   * @return The activityId.
   */
  @java.lang.Override
  public int getActivityId() {
    return activityId_;
  }

  public static final int RECHARGEUSER_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.UnionRechargeUserMsg> rechargeUser_;
  /**
   * <code>repeated .xddq.pb.UnionRechargeUserMsg rechargeUser = 3;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.UnionRechargeUserMsg> getRechargeUserList() {
    return rechargeUser_;
  }
  /**
   * <code>repeated .xddq.pb.UnionRechargeUserMsg rechargeUser = 3;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.UnionRechargeUserMsgOrBuilder> 
      getRechargeUserOrBuilderList() {
    return rechargeUser_;
  }
  /**
   * <code>repeated .xddq.pb.UnionRechargeUserMsg rechargeUser = 3;</code>
   */
  @java.lang.Override
  public int getRechargeUserCount() {
    return rechargeUser_.size();
  }
  /**
   * <code>repeated .xddq.pb.UnionRechargeUserMsg rechargeUser = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.UnionRechargeUserMsg getRechargeUser(int index) {
    return rechargeUser_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.UnionRechargeUserMsg rechargeUser = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.UnionRechargeUserMsgOrBuilder getRechargeUserOrBuilder(
      int index) {
    return rechargeUser_.get(index);
  }

  public static final int GETREWARDDATA_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.UnionRechargeGetRewardDataMsg> getRewardData_;
  /**
   * <code>repeated .xddq.pb.UnionRechargeGetRewardDataMsg getRewardData = 4;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.UnionRechargeGetRewardDataMsg> getGetRewardDataList() {
    return getRewardData_;
  }
  /**
   * <code>repeated .xddq.pb.UnionRechargeGetRewardDataMsg getRewardData = 4;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.UnionRechargeGetRewardDataMsgOrBuilder> 
      getGetRewardDataOrBuilderList() {
    return getRewardData_;
  }
  /**
   * <code>repeated .xddq.pb.UnionRechargeGetRewardDataMsg getRewardData = 4;</code>
   */
  @java.lang.Override
  public int getGetRewardDataCount() {
    return getRewardData_.size();
  }
  /**
   * <code>repeated .xddq.pb.UnionRechargeGetRewardDataMsg getRewardData = 4;</code>
   */
  @java.lang.Override
  public xddq.pb.UnionRechargeGetRewardDataMsg getGetRewardData(int index) {
    return getRewardData_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.UnionRechargeGetRewardDataMsg getRewardData = 4;</code>
   */
  @java.lang.Override
  public xddq.pb.UnionRechargeGetRewardDataMsgOrBuilder getGetRewardDataOrBuilder(
      int index) {
    return getRewardData_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    for (int i = 0; i < getRechargeUserCount(); i++) {
      if (!getRechargeUser(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    for (int i = 0; i < getGetRewardDataCount(); i++) {
      if (!getGetRewardData(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(2, activityId_);
    }
    for (int i = 0; i < rechargeUser_.size(); i++) {
      output.writeMessage(3, rechargeUser_.get(i));
    }
    for (int i = 0; i < getRewardData_.size(); i++) {
      output.writeMessage(4, getRewardData_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, activityId_);
    }
    for (int i = 0; i < rechargeUser_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, rechargeUser_.get(i));
    }
    for (int i = 0; i < getRewardData_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(4, getRewardData_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.UnionRechargeUserRspMsg)) {
      return super.equals(obj);
    }
    xddq.pb.UnionRechargeUserRspMsg other = (xddq.pb.UnionRechargeUserRspMsg) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (hasActivityId() != other.hasActivityId()) return false;
    if (hasActivityId()) {
      if (getActivityId()
          != other.getActivityId()) return false;
    }
    if (!getRechargeUserList()
        .equals(other.getRechargeUserList())) return false;
    if (!getGetRewardDataList()
        .equals(other.getGetRewardDataList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (hasActivityId()) {
      hash = (37 * hash) + ACTIVITYID_FIELD_NUMBER;
      hash = (53 * hash) + getActivityId();
    }
    if (getRechargeUserCount() > 0) {
      hash = (37 * hash) + RECHARGEUSER_FIELD_NUMBER;
      hash = (53 * hash) + getRechargeUserList().hashCode();
    }
    if (getGetRewardDataCount() > 0) {
      hash = (37 * hash) + GETREWARDDATA_FIELD_NUMBER;
      hash = (53 * hash) + getGetRewardDataList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.UnionRechargeUserRspMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.UnionRechargeUserRspMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.UnionRechargeUserRspMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.UnionRechargeUserRspMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.UnionRechargeUserRspMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.UnionRechargeUserRspMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.UnionRechargeUserRspMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.UnionRechargeUserRspMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.UnionRechargeUserRspMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.UnionRechargeUserRspMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.UnionRechargeUserRspMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.UnionRechargeUserRspMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.UnionRechargeUserRspMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.UnionRechargeUserRspMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.UnionRechargeUserRspMsg)
      xddq.pb.UnionRechargeUserRspMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_UnionRechargeUserRspMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_UnionRechargeUserRspMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.UnionRechargeUserRspMsg.class, xddq.pb.UnionRechargeUserRspMsg.Builder.class);
    }

    // Construct using xddq.pb.UnionRechargeUserRspMsg.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      activityId_ = 0;
      if (rechargeUserBuilder_ == null) {
        rechargeUser_ = java.util.Collections.emptyList();
      } else {
        rechargeUser_ = null;
        rechargeUserBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000004);
      if (getRewardDataBuilder_ == null) {
        getRewardData_ = java.util.Collections.emptyList();
      } else {
        getRewardData_ = null;
        getRewardDataBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000008);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_UnionRechargeUserRspMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.UnionRechargeUserRspMsg getDefaultInstanceForType() {
      return xddq.pb.UnionRechargeUserRspMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.UnionRechargeUserRspMsg build() {
      xddq.pb.UnionRechargeUserRspMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.UnionRechargeUserRspMsg buildPartial() {
      xddq.pb.UnionRechargeUserRspMsg result = new xddq.pb.UnionRechargeUserRspMsg(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.UnionRechargeUserRspMsg result) {
      if (rechargeUserBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0)) {
          rechargeUser_ = java.util.Collections.unmodifiableList(rechargeUser_);
          bitField0_ = (bitField0_ & ~0x00000004);
        }
        result.rechargeUser_ = rechargeUser_;
      } else {
        result.rechargeUser_ = rechargeUserBuilder_.build();
      }
      if (getRewardDataBuilder_ == null) {
        if (((bitField0_ & 0x00000008) != 0)) {
          getRewardData_ = java.util.Collections.unmodifiableList(getRewardData_);
          bitField0_ = (bitField0_ & ~0x00000008);
        }
        result.getRewardData_ = getRewardData_;
      } else {
        result.getRewardData_ = getRewardDataBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.UnionRechargeUserRspMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.activityId_ = activityId_;
        to_bitField0_ |= 0x00000002;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.UnionRechargeUserRspMsg) {
        return mergeFrom((xddq.pb.UnionRechargeUserRspMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.UnionRechargeUserRspMsg other) {
      if (other == xddq.pb.UnionRechargeUserRspMsg.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (other.hasActivityId()) {
        setActivityId(other.getActivityId());
      }
      if (rechargeUserBuilder_ == null) {
        if (!other.rechargeUser_.isEmpty()) {
          if (rechargeUser_.isEmpty()) {
            rechargeUser_ = other.rechargeUser_;
            bitField0_ = (bitField0_ & ~0x00000004);
          } else {
            ensureRechargeUserIsMutable();
            rechargeUser_.addAll(other.rechargeUser_);
          }
          onChanged();
        }
      } else {
        if (!other.rechargeUser_.isEmpty()) {
          if (rechargeUserBuilder_.isEmpty()) {
            rechargeUserBuilder_.dispose();
            rechargeUserBuilder_ = null;
            rechargeUser_ = other.rechargeUser_;
            bitField0_ = (bitField0_ & ~0x00000004);
            rechargeUserBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetRechargeUserFieldBuilder() : null;
          } else {
            rechargeUserBuilder_.addAllMessages(other.rechargeUser_);
          }
        }
      }
      if (getRewardDataBuilder_ == null) {
        if (!other.getRewardData_.isEmpty()) {
          if (getRewardData_.isEmpty()) {
            getRewardData_ = other.getRewardData_;
            bitField0_ = (bitField0_ & ~0x00000008);
          } else {
            ensureGetRewardDataIsMutable();
            getRewardData_.addAll(other.getRewardData_);
          }
          onChanged();
        }
      } else {
        if (!other.getRewardData_.isEmpty()) {
          if (getRewardDataBuilder_.isEmpty()) {
            getRewardDataBuilder_.dispose();
            getRewardDataBuilder_ = null;
            getRewardData_ = other.getRewardData_;
            bitField0_ = (bitField0_ & ~0x00000008);
            getRewardDataBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetGetRewardDataFieldBuilder() : null;
          } else {
            getRewardDataBuilder_.addAllMessages(other.getRewardData_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      for (int i = 0; i < getRechargeUserCount(); i++) {
        if (!getRechargeUser(i).isInitialized()) {
          return false;
        }
      }
      for (int i = 0; i < getGetRewardDataCount(); i++) {
        if (!getGetRewardData(i).isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              activityId_ = input.readInt32();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 26: {
              xddq.pb.UnionRechargeUserMsg m =
                  input.readMessage(
                      xddq.pb.UnionRechargeUserMsg.parser(),
                      extensionRegistry);
              if (rechargeUserBuilder_ == null) {
                ensureRechargeUserIsMutable();
                rechargeUser_.add(m);
              } else {
                rechargeUserBuilder_.addMessage(m);
              }
              break;
            } // case 26
            case 34: {
              xddq.pb.UnionRechargeGetRewardDataMsg m =
                  input.readMessage(
                      xddq.pb.UnionRechargeGetRewardDataMsg.parser(),
                      extensionRegistry);
              if (getRewardDataBuilder_ == null) {
                ensureGetRewardDataIsMutable();
                getRewardData_.add(m);
              } else {
                getRewardDataBuilder_.addMessage(m);
              }
              break;
            } // case 34
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private int activityId_ ;
    /**
     * <code>optional int32 activityId = 2;</code>
     * @return Whether the activityId field is set.
     */
    @java.lang.Override
    public boolean hasActivityId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 activityId = 2;</code>
     * @return The activityId.
     */
    @java.lang.Override
    public int getActivityId() {
      return activityId_;
    }
    /**
     * <code>optional int32 activityId = 2;</code>
     * @param value The activityId to set.
     * @return This builder for chaining.
     */
    public Builder setActivityId(int value) {

      activityId_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 activityId = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearActivityId() {
      bitField0_ = (bitField0_ & ~0x00000002);
      activityId_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.UnionRechargeUserMsg> rechargeUser_ =
      java.util.Collections.emptyList();
    private void ensureRechargeUserIsMutable() {
      if (!((bitField0_ & 0x00000004) != 0)) {
        rechargeUser_ = new java.util.ArrayList<xddq.pb.UnionRechargeUserMsg>(rechargeUser_);
        bitField0_ |= 0x00000004;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.UnionRechargeUserMsg, xddq.pb.UnionRechargeUserMsg.Builder, xddq.pb.UnionRechargeUserMsgOrBuilder> rechargeUserBuilder_;

    /**
     * <code>repeated .xddq.pb.UnionRechargeUserMsg rechargeUser = 3;</code>
     */
    public java.util.List<xddq.pb.UnionRechargeUserMsg> getRechargeUserList() {
      if (rechargeUserBuilder_ == null) {
        return java.util.Collections.unmodifiableList(rechargeUser_);
      } else {
        return rechargeUserBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.UnionRechargeUserMsg rechargeUser = 3;</code>
     */
    public int getRechargeUserCount() {
      if (rechargeUserBuilder_ == null) {
        return rechargeUser_.size();
      } else {
        return rechargeUserBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.UnionRechargeUserMsg rechargeUser = 3;</code>
     */
    public xddq.pb.UnionRechargeUserMsg getRechargeUser(int index) {
      if (rechargeUserBuilder_ == null) {
        return rechargeUser_.get(index);
      } else {
        return rechargeUserBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.UnionRechargeUserMsg rechargeUser = 3;</code>
     */
    public Builder setRechargeUser(
        int index, xddq.pb.UnionRechargeUserMsg value) {
      if (rechargeUserBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRechargeUserIsMutable();
        rechargeUser_.set(index, value);
        onChanged();
      } else {
        rechargeUserBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionRechargeUserMsg rechargeUser = 3;</code>
     */
    public Builder setRechargeUser(
        int index, xddq.pb.UnionRechargeUserMsg.Builder builderForValue) {
      if (rechargeUserBuilder_ == null) {
        ensureRechargeUserIsMutable();
        rechargeUser_.set(index, builderForValue.build());
        onChanged();
      } else {
        rechargeUserBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionRechargeUserMsg rechargeUser = 3;</code>
     */
    public Builder addRechargeUser(xddq.pb.UnionRechargeUserMsg value) {
      if (rechargeUserBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRechargeUserIsMutable();
        rechargeUser_.add(value);
        onChanged();
      } else {
        rechargeUserBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionRechargeUserMsg rechargeUser = 3;</code>
     */
    public Builder addRechargeUser(
        int index, xddq.pb.UnionRechargeUserMsg value) {
      if (rechargeUserBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRechargeUserIsMutable();
        rechargeUser_.add(index, value);
        onChanged();
      } else {
        rechargeUserBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionRechargeUserMsg rechargeUser = 3;</code>
     */
    public Builder addRechargeUser(
        xddq.pb.UnionRechargeUserMsg.Builder builderForValue) {
      if (rechargeUserBuilder_ == null) {
        ensureRechargeUserIsMutable();
        rechargeUser_.add(builderForValue.build());
        onChanged();
      } else {
        rechargeUserBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionRechargeUserMsg rechargeUser = 3;</code>
     */
    public Builder addRechargeUser(
        int index, xddq.pb.UnionRechargeUserMsg.Builder builderForValue) {
      if (rechargeUserBuilder_ == null) {
        ensureRechargeUserIsMutable();
        rechargeUser_.add(index, builderForValue.build());
        onChanged();
      } else {
        rechargeUserBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionRechargeUserMsg rechargeUser = 3;</code>
     */
    public Builder addAllRechargeUser(
        java.lang.Iterable<? extends xddq.pb.UnionRechargeUserMsg> values) {
      if (rechargeUserBuilder_ == null) {
        ensureRechargeUserIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, rechargeUser_);
        onChanged();
      } else {
        rechargeUserBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionRechargeUserMsg rechargeUser = 3;</code>
     */
    public Builder clearRechargeUser() {
      if (rechargeUserBuilder_ == null) {
        rechargeUser_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
      } else {
        rechargeUserBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionRechargeUserMsg rechargeUser = 3;</code>
     */
    public Builder removeRechargeUser(int index) {
      if (rechargeUserBuilder_ == null) {
        ensureRechargeUserIsMutable();
        rechargeUser_.remove(index);
        onChanged();
      } else {
        rechargeUserBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionRechargeUserMsg rechargeUser = 3;</code>
     */
    public xddq.pb.UnionRechargeUserMsg.Builder getRechargeUserBuilder(
        int index) {
      return internalGetRechargeUserFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.UnionRechargeUserMsg rechargeUser = 3;</code>
     */
    public xddq.pb.UnionRechargeUserMsgOrBuilder getRechargeUserOrBuilder(
        int index) {
      if (rechargeUserBuilder_ == null) {
        return rechargeUser_.get(index);  } else {
        return rechargeUserBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.UnionRechargeUserMsg rechargeUser = 3;</code>
     */
    public java.util.List<? extends xddq.pb.UnionRechargeUserMsgOrBuilder> 
         getRechargeUserOrBuilderList() {
      if (rechargeUserBuilder_ != null) {
        return rechargeUserBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(rechargeUser_);
      }
    }
    /**
     * <code>repeated .xddq.pb.UnionRechargeUserMsg rechargeUser = 3;</code>
     */
    public xddq.pb.UnionRechargeUserMsg.Builder addRechargeUserBuilder() {
      return internalGetRechargeUserFieldBuilder().addBuilder(
          xddq.pb.UnionRechargeUserMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.UnionRechargeUserMsg rechargeUser = 3;</code>
     */
    public xddq.pb.UnionRechargeUserMsg.Builder addRechargeUserBuilder(
        int index) {
      return internalGetRechargeUserFieldBuilder().addBuilder(
          index, xddq.pb.UnionRechargeUserMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.UnionRechargeUserMsg rechargeUser = 3;</code>
     */
    public java.util.List<xddq.pb.UnionRechargeUserMsg.Builder> 
         getRechargeUserBuilderList() {
      return internalGetRechargeUserFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.UnionRechargeUserMsg, xddq.pb.UnionRechargeUserMsg.Builder, xddq.pb.UnionRechargeUserMsgOrBuilder> 
        internalGetRechargeUserFieldBuilder() {
      if (rechargeUserBuilder_ == null) {
        rechargeUserBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.UnionRechargeUserMsg, xddq.pb.UnionRechargeUserMsg.Builder, xddq.pb.UnionRechargeUserMsgOrBuilder>(
                rechargeUser_,
                ((bitField0_ & 0x00000004) != 0),
                getParentForChildren(),
                isClean());
        rechargeUser_ = null;
      }
      return rechargeUserBuilder_;
    }

    private java.util.List<xddq.pb.UnionRechargeGetRewardDataMsg> getRewardData_ =
      java.util.Collections.emptyList();
    private void ensureGetRewardDataIsMutable() {
      if (!((bitField0_ & 0x00000008) != 0)) {
        getRewardData_ = new java.util.ArrayList<xddq.pb.UnionRechargeGetRewardDataMsg>(getRewardData_);
        bitField0_ |= 0x00000008;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.UnionRechargeGetRewardDataMsg, xddq.pb.UnionRechargeGetRewardDataMsg.Builder, xddq.pb.UnionRechargeGetRewardDataMsgOrBuilder> getRewardDataBuilder_;

    /**
     * <code>repeated .xddq.pb.UnionRechargeGetRewardDataMsg getRewardData = 4;</code>
     */
    public java.util.List<xddq.pb.UnionRechargeGetRewardDataMsg> getGetRewardDataList() {
      if (getRewardDataBuilder_ == null) {
        return java.util.Collections.unmodifiableList(getRewardData_);
      } else {
        return getRewardDataBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.UnionRechargeGetRewardDataMsg getRewardData = 4;</code>
     */
    public int getGetRewardDataCount() {
      if (getRewardDataBuilder_ == null) {
        return getRewardData_.size();
      } else {
        return getRewardDataBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.UnionRechargeGetRewardDataMsg getRewardData = 4;</code>
     */
    public xddq.pb.UnionRechargeGetRewardDataMsg getGetRewardData(int index) {
      if (getRewardDataBuilder_ == null) {
        return getRewardData_.get(index);
      } else {
        return getRewardDataBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.UnionRechargeGetRewardDataMsg getRewardData = 4;</code>
     */
    public Builder setGetRewardData(
        int index, xddq.pb.UnionRechargeGetRewardDataMsg value) {
      if (getRewardDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureGetRewardDataIsMutable();
        getRewardData_.set(index, value);
        onChanged();
      } else {
        getRewardDataBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionRechargeGetRewardDataMsg getRewardData = 4;</code>
     */
    public Builder setGetRewardData(
        int index, xddq.pb.UnionRechargeGetRewardDataMsg.Builder builderForValue) {
      if (getRewardDataBuilder_ == null) {
        ensureGetRewardDataIsMutable();
        getRewardData_.set(index, builderForValue.build());
        onChanged();
      } else {
        getRewardDataBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionRechargeGetRewardDataMsg getRewardData = 4;</code>
     */
    public Builder addGetRewardData(xddq.pb.UnionRechargeGetRewardDataMsg value) {
      if (getRewardDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureGetRewardDataIsMutable();
        getRewardData_.add(value);
        onChanged();
      } else {
        getRewardDataBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionRechargeGetRewardDataMsg getRewardData = 4;</code>
     */
    public Builder addGetRewardData(
        int index, xddq.pb.UnionRechargeGetRewardDataMsg value) {
      if (getRewardDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureGetRewardDataIsMutable();
        getRewardData_.add(index, value);
        onChanged();
      } else {
        getRewardDataBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionRechargeGetRewardDataMsg getRewardData = 4;</code>
     */
    public Builder addGetRewardData(
        xddq.pb.UnionRechargeGetRewardDataMsg.Builder builderForValue) {
      if (getRewardDataBuilder_ == null) {
        ensureGetRewardDataIsMutable();
        getRewardData_.add(builderForValue.build());
        onChanged();
      } else {
        getRewardDataBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionRechargeGetRewardDataMsg getRewardData = 4;</code>
     */
    public Builder addGetRewardData(
        int index, xddq.pb.UnionRechargeGetRewardDataMsg.Builder builderForValue) {
      if (getRewardDataBuilder_ == null) {
        ensureGetRewardDataIsMutable();
        getRewardData_.add(index, builderForValue.build());
        onChanged();
      } else {
        getRewardDataBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionRechargeGetRewardDataMsg getRewardData = 4;</code>
     */
    public Builder addAllGetRewardData(
        java.lang.Iterable<? extends xddq.pb.UnionRechargeGetRewardDataMsg> values) {
      if (getRewardDataBuilder_ == null) {
        ensureGetRewardDataIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, getRewardData_);
        onChanged();
      } else {
        getRewardDataBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionRechargeGetRewardDataMsg getRewardData = 4;</code>
     */
    public Builder clearGetRewardData() {
      if (getRewardDataBuilder_ == null) {
        getRewardData_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000008);
        onChanged();
      } else {
        getRewardDataBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionRechargeGetRewardDataMsg getRewardData = 4;</code>
     */
    public Builder removeGetRewardData(int index) {
      if (getRewardDataBuilder_ == null) {
        ensureGetRewardDataIsMutable();
        getRewardData_.remove(index);
        onChanged();
      } else {
        getRewardDataBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionRechargeGetRewardDataMsg getRewardData = 4;</code>
     */
    public xddq.pb.UnionRechargeGetRewardDataMsg.Builder getGetRewardDataBuilder(
        int index) {
      return internalGetGetRewardDataFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.UnionRechargeGetRewardDataMsg getRewardData = 4;</code>
     */
    public xddq.pb.UnionRechargeGetRewardDataMsgOrBuilder getGetRewardDataOrBuilder(
        int index) {
      if (getRewardDataBuilder_ == null) {
        return getRewardData_.get(index);  } else {
        return getRewardDataBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.UnionRechargeGetRewardDataMsg getRewardData = 4;</code>
     */
    public java.util.List<? extends xddq.pb.UnionRechargeGetRewardDataMsgOrBuilder> 
         getGetRewardDataOrBuilderList() {
      if (getRewardDataBuilder_ != null) {
        return getRewardDataBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(getRewardData_);
      }
    }
    /**
     * <code>repeated .xddq.pb.UnionRechargeGetRewardDataMsg getRewardData = 4;</code>
     */
    public xddq.pb.UnionRechargeGetRewardDataMsg.Builder addGetRewardDataBuilder() {
      return internalGetGetRewardDataFieldBuilder().addBuilder(
          xddq.pb.UnionRechargeGetRewardDataMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.UnionRechargeGetRewardDataMsg getRewardData = 4;</code>
     */
    public xddq.pb.UnionRechargeGetRewardDataMsg.Builder addGetRewardDataBuilder(
        int index) {
      return internalGetGetRewardDataFieldBuilder().addBuilder(
          index, xddq.pb.UnionRechargeGetRewardDataMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.UnionRechargeGetRewardDataMsg getRewardData = 4;</code>
     */
    public java.util.List<xddq.pb.UnionRechargeGetRewardDataMsg.Builder> 
         getGetRewardDataBuilderList() {
      return internalGetGetRewardDataFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.UnionRechargeGetRewardDataMsg, xddq.pb.UnionRechargeGetRewardDataMsg.Builder, xddq.pb.UnionRechargeGetRewardDataMsgOrBuilder> 
        internalGetGetRewardDataFieldBuilder() {
      if (getRewardDataBuilder_ == null) {
        getRewardDataBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.UnionRechargeGetRewardDataMsg, xddq.pb.UnionRechargeGetRewardDataMsg.Builder, xddq.pb.UnionRechargeGetRewardDataMsgOrBuilder>(
                getRewardData_,
                ((bitField0_ & 0x00000008) != 0),
                getParentForChildren(),
                isClean());
        getRewardData_ = null;
      }
      return getRewardDataBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.UnionRechargeUserRspMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.UnionRechargeUserRspMsg)
  private static final xddq.pb.UnionRechargeUserRspMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.UnionRechargeUserRspMsg();
  }

  public static xddq.pb.UnionRechargeUserRspMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<UnionRechargeUserRspMsg>
      PARSER = new com.google.protobuf.AbstractParser<UnionRechargeUserRspMsg>() {
    @java.lang.Override
    public UnionRechargeUserRspMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<UnionRechargeUserRspMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<UnionRechargeUserRspMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.UnionRechargeUserRspMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

