// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

public interface GroupPurchaseConfigSyncOrBuilder extends
    // @@protoc_insertion_point(interface_extends:xddq.pb.GroupPurchaseConfigSync)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>required int32 activityId = 1;</code>
   * @return Whether the activityId field is set.
   */
  boolean hasActivityId();
  /**
   * <code>required int32 activityId = 1;</code>
   * @return The activityId.
   */
  int getActivityId();

  /**
   * <code>repeated .xddq.pb.GroupPurchaseBaseConfig baseConfig = 2;</code>
   */
  java.util.List<xddq.pb.GroupPurchaseBaseConfig> 
      getBaseConfigList();
  /**
   * <code>repeated .xddq.pb.GroupPurchaseBaseConfig baseConfig = 2;</code>
   */
  xddq.pb.GroupPurchaseBaseConfig getBaseConfig(int index);
  /**
   * <code>repeated .xddq.pb.GroupPurchaseBaseConfig baseConfig = 2;</code>
   */
  int getBaseConfigCount();
  /**
   * <code>repeated .xddq.pb.GroupPurchaseBaseConfig baseConfig = 2;</code>
   */
  java.util.List<? extends xddq.pb.GroupPurchaseBaseConfigOrBuilder> 
      getBaseConfigOrBuilderList();
  /**
   * <code>repeated .xddq.pb.GroupPurchaseBaseConfig baseConfig = 2;</code>
   */
  xddq.pb.GroupPurchaseBaseConfigOrBuilder getBaseConfigOrBuilder(
      int index);
}
