// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.UnionRechargeGetRewardDataMsg}
 */
public final class UnionRechargeGetRewardDataMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.UnionRechargeGetRewardDataMsg)
    UnionRechargeGetRewardDataMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      UnionRechargeGetRewardDataMsg.class.getName());
  }
  // Use UnionRechargeGetRewardDataMsg.newBuilder() to construct.
  private UnionRechargeGetRewardDataMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private UnionRechargeGetRewardDataMsg() {
    playerList_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_UnionRechargeGetRewardDataMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_UnionRechargeGetRewardDataMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.UnionRechargeGetRewardDataMsg.class, xddq.pb.UnionRechargeGetRewardDataMsg.Builder.class);
  }

  private int bitField0_;
  public static final int CONDITIONID_FIELD_NUMBER = 1;
  private int conditionId_ = 0;
  /**
   * <code>required int32 conditionId = 1;</code>
   * @return Whether the conditionId field is set.
   */
  @java.lang.Override
  public boolean hasConditionId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 conditionId = 1;</code>
   * @return The conditionId.
   */
  @java.lang.Override
  public int getConditionId() {
    return conditionId_;
  }

  public static final int PLAYERLIST_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.UnionPlayerMsg> playerList_;
  /**
   * <code>repeated .xddq.pb.UnionPlayerMsg playerList = 2;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.UnionPlayerMsg> getPlayerListList() {
    return playerList_;
  }
  /**
   * <code>repeated .xddq.pb.UnionPlayerMsg playerList = 2;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.UnionPlayerMsgOrBuilder> 
      getPlayerListOrBuilderList() {
    return playerList_;
  }
  /**
   * <code>repeated .xddq.pb.UnionPlayerMsg playerList = 2;</code>
   */
  @java.lang.Override
  public int getPlayerListCount() {
    return playerList_.size();
  }
  /**
   * <code>repeated .xddq.pb.UnionPlayerMsg playerList = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.UnionPlayerMsg getPlayerList(int index) {
    return playerList_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.UnionPlayerMsg playerList = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.UnionPlayerMsgOrBuilder getPlayerListOrBuilder(
      int index) {
    return playerList_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasConditionId()) {
      memoizedIsInitialized = 0;
      return false;
    }
    for (int i = 0; i < getPlayerListCount(); i++) {
      if (!getPlayerList(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, conditionId_);
    }
    for (int i = 0; i < playerList_.size(); i++) {
      output.writeMessage(2, playerList_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, conditionId_);
    }
    for (int i = 0; i < playerList_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, playerList_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.UnionRechargeGetRewardDataMsg)) {
      return super.equals(obj);
    }
    xddq.pb.UnionRechargeGetRewardDataMsg other = (xddq.pb.UnionRechargeGetRewardDataMsg) obj;

    if (hasConditionId() != other.hasConditionId()) return false;
    if (hasConditionId()) {
      if (getConditionId()
          != other.getConditionId()) return false;
    }
    if (!getPlayerListList()
        .equals(other.getPlayerListList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasConditionId()) {
      hash = (37 * hash) + CONDITIONID_FIELD_NUMBER;
      hash = (53 * hash) + getConditionId();
    }
    if (getPlayerListCount() > 0) {
      hash = (37 * hash) + PLAYERLIST_FIELD_NUMBER;
      hash = (53 * hash) + getPlayerListList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.UnionRechargeGetRewardDataMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.UnionRechargeGetRewardDataMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.UnionRechargeGetRewardDataMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.UnionRechargeGetRewardDataMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.UnionRechargeGetRewardDataMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.UnionRechargeGetRewardDataMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.UnionRechargeGetRewardDataMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.UnionRechargeGetRewardDataMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.UnionRechargeGetRewardDataMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.UnionRechargeGetRewardDataMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.UnionRechargeGetRewardDataMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.UnionRechargeGetRewardDataMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.UnionRechargeGetRewardDataMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.UnionRechargeGetRewardDataMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.UnionRechargeGetRewardDataMsg)
      xddq.pb.UnionRechargeGetRewardDataMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_UnionRechargeGetRewardDataMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_UnionRechargeGetRewardDataMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.UnionRechargeGetRewardDataMsg.class, xddq.pb.UnionRechargeGetRewardDataMsg.Builder.class);
    }

    // Construct using xddq.pb.UnionRechargeGetRewardDataMsg.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      conditionId_ = 0;
      if (playerListBuilder_ == null) {
        playerList_ = java.util.Collections.emptyList();
      } else {
        playerList_ = null;
        playerListBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000002);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_UnionRechargeGetRewardDataMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.UnionRechargeGetRewardDataMsg getDefaultInstanceForType() {
      return xddq.pb.UnionRechargeGetRewardDataMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.UnionRechargeGetRewardDataMsg build() {
      xddq.pb.UnionRechargeGetRewardDataMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.UnionRechargeGetRewardDataMsg buildPartial() {
      xddq.pb.UnionRechargeGetRewardDataMsg result = new xddq.pb.UnionRechargeGetRewardDataMsg(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.UnionRechargeGetRewardDataMsg result) {
      if (playerListBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          playerList_ = java.util.Collections.unmodifiableList(playerList_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.playerList_ = playerList_;
      } else {
        result.playerList_ = playerListBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.UnionRechargeGetRewardDataMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.conditionId_ = conditionId_;
        to_bitField0_ |= 0x00000001;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.UnionRechargeGetRewardDataMsg) {
        return mergeFrom((xddq.pb.UnionRechargeGetRewardDataMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.UnionRechargeGetRewardDataMsg other) {
      if (other == xddq.pb.UnionRechargeGetRewardDataMsg.getDefaultInstance()) return this;
      if (other.hasConditionId()) {
        setConditionId(other.getConditionId());
      }
      if (playerListBuilder_ == null) {
        if (!other.playerList_.isEmpty()) {
          if (playerList_.isEmpty()) {
            playerList_ = other.playerList_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensurePlayerListIsMutable();
            playerList_.addAll(other.playerList_);
          }
          onChanged();
        }
      } else {
        if (!other.playerList_.isEmpty()) {
          if (playerListBuilder_.isEmpty()) {
            playerListBuilder_.dispose();
            playerListBuilder_ = null;
            playerList_ = other.playerList_;
            bitField0_ = (bitField0_ & ~0x00000002);
            playerListBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetPlayerListFieldBuilder() : null;
          } else {
            playerListBuilder_.addAllMessages(other.playerList_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasConditionId()) {
        return false;
      }
      for (int i = 0; i < getPlayerListCount(); i++) {
        if (!getPlayerList(i).isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              conditionId_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              xddq.pb.UnionPlayerMsg m =
                  input.readMessage(
                      xddq.pb.UnionPlayerMsg.parser(),
                      extensionRegistry);
              if (playerListBuilder_ == null) {
                ensurePlayerListIsMutable();
                playerList_.add(m);
              } else {
                playerListBuilder_.addMessage(m);
              }
              break;
            } // case 18
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int conditionId_ ;
    /**
     * <code>required int32 conditionId = 1;</code>
     * @return Whether the conditionId field is set.
     */
    @java.lang.Override
    public boolean hasConditionId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 conditionId = 1;</code>
     * @return The conditionId.
     */
    @java.lang.Override
    public int getConditionId() {
      return conditionId_;
    }
    /**
     * <code>required int32 conditionId = 1;</code>
     * @param value The conditionId to set.
     * @return This builder for chaining.
     */
    public Builder setConditionId(int value) {

      conditionId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 conditionId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearConditionId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      conditionId_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.UnionPlayerMsg> playerList_ =
      java.util.Collections.emptyList();
    private void ensurePlayerListIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        playerList_ = new java.util.ArrayList<xddq.pb.UnionPlayerMsg>(playerList_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.UnionPlayerMsg, xddq.pb.UnionPlayerMsg.Builder, xddq.pb.UnionPlayerMsgOrBuilder> playerListBuilder_;

    /**
     * <code>repeated .xddq.pb.UnionPlayerMsg playerList = 2;</code>
     */
    public java.util.List<xddq.pb.UnionPlayerMsg> getPlayerListList() {
      if (playerListBuilder_ == null) {
        return java.util.Collections.unmodifiableList(playerList_);
      } else {
        return playerListBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.UnionPlayerMsg playerList = 2;</code>
     */
    public int getPlayerListCount() {
      if (playerListBuilder_ == null) {
        return playerList_.size();
      } else {
        return playerListBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.UnionPlayerMsg playerList = 2;</code>
     */
    public xddq.pb.UnionPlayerMsg getPlayerList(int index) {
      if (playerListBuilder_ == null) {
        return playerList_.get(index);
      } else {
        return playerListBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.UnionPlayerMsg playerList = 2;</code>
     */
    public Builder setPlayerList(
        int index, xddq.pb.UnionPlayerMsg value) {
      if (playerListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePlayerListIsMutable();
        playerList_.set(index, value);
        onChanged();
      } else {
        playerListBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionPlayerMsg playerList = 2;</code>
     */
    public Builder setPlayerList(
        int index, xddq.pb.UnionPlayerMsg.Builder builderForValue) {
      if (playerListBuilder_ == null) {
        ensurePlayerListIsMutable();
        playerList_.set(index, builderForValue.build());
        onChanged();
      } else {
        playerListBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionPlayerMsg playerList = 2;</code>
     */
    public Builder addPlayerList(xddq.pb.UnionPlayerMsg value) {
      if (playerListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePlayerListIsMutable();
        playerList_.add(value);
        onChanged();
      } else {
        playerListBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionPlayerMsg playerList = 2;</code>
     */
    public Builder addPlayerList(
        int index, xddq.pb.UnionPlayerMsg value) {
      if (playerListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePlayerListIsMutable();
        playerList_.add(index, value);
        onChanged();
      } else {
        playerListBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionPlayerMsg playerList = 2;</code>
     */
    public Builder addPlayerList(
        xddq.pb.UnionPlayerMsg.Builder builderForValue) {
      if (playerListBuilder_ == null) {
        ensurePlayerListIsMutable();
        playerList_.add(builderForValue.build());
        onChanged();
      } else {
        playerListBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionPlayerMsg playerList = 2;</code>
     */
    public Builder addPlayerList(
        int index, xddq.pb.UnionPlayerMsg.Builder builderForValue) {
      if (playerListBuilder_ == null) {
        ensurePlayerListIsMutable();
        playerList_.add(index, builderForValue.build());
        onChanged();
      } else {
        playerListBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionPlayerMsg playerList = 2;</code>
     */
    public Builder addAllPlayerList(
        java.lang.Iterable<? extends xddq.pb.UnionPlayerMsg> values) {
      if (playerListBuilder_ == null) {
        ensurePlayerListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, playerList_);
        onChanged();
      } else {
        playerListBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionPlayerMsg playerList = 2;</code>
     */
    public Builder clearPlayerList() {
      if (playerListBuilder_ == null) {
        playerList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        playerListBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionPlayerMsg playerList = 2;</code>
     */
    public Builder removePlayerList(int index) {
      if (playerListBuilder_ == null) {
        ensurePlayerListIsMutable();
        playerList_.remove(index);
        onChanged();
      } else {
        playerListBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionPlayerMsg playerList = 2;</code>
     */
    public xddq.pb.UnionPlayerMsg.Builder getPlayerListBuilder(
        int index) {
      return internalGetPlayerListFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.UnionPlayerMsg playerList = 2;</code>
     */
    public xddq.pb.UnionPlayerMsgOrBuilder getPlayerListOrBuilder(
        int index) {
      if (playerListBuilder_ == null) {
        return playerList_.get(index);  } else {
        return playerListBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.UnionPlayerMsg playerList = 2;</code>
     */
    public java.util.List<? extends xddq.pb.UnionPlayerMsgOrBuilder> 
         getPlayerListOrBuilderList() {
      if (playerListBuilder_ != null) {
        return playerListBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(playerList_);
      }
    }
    /**
     * <code>repeated .xddq.pb.UnionPlayerMsg playerList = 2;</code>
     */
    public xddq.pb.UnionPlayerMsg.Builder addPlayerListBuilder() {
      return internalGetPlayerListFieldBuilder().addBuilder(
          xddq.pb.UnionPlayerMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.UnionPlayerMsg playerList = 2;</code>
     */
    public xddq.pb.UnionPlayerMsg.Builder addPlayerListBuilder(
        int index) {
      return internalGetPlayerListFieldBuilder().addBuilder(
          index, xddq.pb.UnionPlayerMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.UnionPlayerMsg playerList = 2;</code>
     */
    public java.util.List<xddq.pb.UnionPlayerMsg.Builder> 
         getPlayerListBuilderList() {
      return internalGetPlayerListFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.UnionPlayerMsg, xddq.pb.UnionPlayerMsg.Builder, xddq.pb.UnionPlayerMsgOrBuilder> 
        internalGetPlayerListFieldBuilder() {
      if (playerListBuilder_ == null) {
        playerListBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.UnionPlayerMsg, xddq.pb.UnionPlayerMsg.Builder, xddq.pb.UnionPlayerMsgOrBuilder>(
                playerList_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        playerList_ = null;
      }
      return playerListBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.UnionRechargeGetRewardDataMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.UnionRechargeGetRewardDataMsg)
  private static final xddq.pb.UnionRechargeGetRewardDataMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.UnionRechargeGetRewardDataMsg();
  }

  public static xddq.pb.UnionRechargeGetRewardDataMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<UnionRechargeGetRewardDataMsg>
      PARSER = new com.google.protobuf.AbstractParser<UnionRechargeGetRewardDataMsg>() {
    @java.lang.Override
    public UnionRechargeGetRewardDataMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<UnionRechargeGetRewardDataMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<UnionRechargeGetRewardDataMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.UnionRechargeGetRewardDataMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

