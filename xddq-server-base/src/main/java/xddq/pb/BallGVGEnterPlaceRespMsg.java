// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.BallGVGEnterPlaceRespMsg}
 */
public final class BallGVGEnterPlaceRespMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.BallGVGEnterPlaceRespMsg)
    BallGVGEnterPlaceRespMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      BallGVGEnterPlaceRespMsg.class.getName());
  }
  // Use BallGVGEnterPlaceRespMsg.newBuilder() to construct.
  private BallGVGEnterPlaceRespMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private BallGVGEnterPlaceRespMsg() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_BallGVGEnterPlaceRespMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_BallGVGEnterPlaceRespMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.BallGVGEnterPlaceRespMsg.class, xddq.pb.BallGVGEnterPlaceRespMsg.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int PLACEDATA_FIELD_NUMBER = 2;
  private xddq.pb.BallGVGPlaceDataTemp placeData_;
  /**
   * <code>optional .xddq.pb.BallGVGPlaceDataTemp placeData = 2;</code>
   * @return Whether the placeData field is set.
   */
  @java.lang.Override
  public boolean hasPlaceData() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional .xddq.pb.BallGVGPlaceDataTemp placeData = 2;</code>
   * @return The placeData.
   */
  @java.lang.Override
  public xddq.pb.BallGVGPlaceDataTemp getPlaceData() {
    return placeData_ == null ? xddq.pb.BallGVGPlaceDataTemp.getDefaultInstance() : placeData_;
  }
  /**
   * <code>optional .xddq.pb.BallGVGPlaceDataTemp placeData = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.BallGVGPlaceDataTempOrBuilder getPlaceDataOrBuilder() {
    return placeData_ == null ? xddq.pb.BallGVGPlaceDataTemp.getDefaultInstance() : placeData_;
  }

  public static final int PLACEID_FIELD_NUMBER = 3;
  private int placeId_ = 0;
  /**
   * <code>optional int32 placeId = 3;</code>
   * @return Whether the placeId field is set.
   */
  @java.lang.Override
  public boolean hasPlaceId() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional int32 placeId = 3;</code>
   * @return The placeId.
   */
  @java.lang.Override
  public int getPlaceId() {
    return placeId_;
  }

  public static final int TRANSFERTIME_FIELD_NUMBER = 4;
  private long transferTime_ = 0L;
  /**
   * <code>optional int64 transferTime = 4;</code>
   * @return Whether the transferTime field is set.
   */
  @java.lang.Override
  public boolean hasTransferTime() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional int64 transferTime = 4;</code>
   * @return The transferTime.
   */
  @java.lang.Override
  public long getTransferTime() {
    return transferTime_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (hasPlaceData()) {
      if (!getPlaceData().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeMessage(2, getPlaceData());
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt32(3, placeId_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt64(4, transferTime_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getPlaceData());
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(3, placeId_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(4, transferTime_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.BallGVGEnterPlaceRespMsg)) {
      return super.equals(obj);
    }
    xddq.pb.BallGVGEnterPlaceRespMsg other = (xddq.pb.BallGVGEnterPlaceRespMsg) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (hasPlaceData() != other.hasPlaceData()) return false;
    if (hasPlaceData()) {
      if (!getPlaceData()
          .equals(other.getPlaceData())) return false;
    }
    if (hasPlaceId() != other.hasPlaceId()) return false;
    if (hasPlaceId()) {
      if (getPlaceId()
          != other.getPlaceId()) return false;
    }
    if (hasTransferTime() != other.hasTransferTime()) return false;
    if (hasTransferTime()) {
      if (getTransferTime()
          != other.getTransferTime()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (hasPlaceData()) {
      hash = (37 * hash) + PLACEDATA_FIELD_NUMBER;
      hash = (53 * hash) + getPlaceData().hashCode();
    }
    if (hasPlaceId()) {
      hash = (37 * hash) + PLACEID_FIELD_NUMBER;
      hash = (53 * hash) + getPlaceId();
    }
    if (hasTransferTime()) {
      hash = (37 * hash) + TRANSFERTIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTransferTime());
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.BallGVGEnterPlaceRespMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.BallGVGEnterPlaceRespMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.BallGVGEnterPlaceRespMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.BallGVGEnterPlaceRespMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.BallGVGEnterPlaceRespMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.BallGVGEnterPlaceRespMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.BallGVGEnterPlaceRespMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.BallGVGEnterPlaceRespMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.BallGVGEnterPlaceRespMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.BallGVGEnterPlaceRespMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.BallGVGEnterPlaceRespMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.BallGVGEnterPlaceRespMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.BallGVGEnterPlaceRespMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.BallGVGEnterPlaceRespMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.BallGVGEnterPlaceRespMsg)
      xddq.pb.BallGVGEnterPlaceRespMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_BallGVGEnterPlaceRespMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_BallGVGEnterPlaceRespMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.BallGVGEnterPlaceRespMsg.class, xddq.pb.BallGVGEnterPlaceRespMsg.Builder.class);
    }

    // Construct using xddq.pb.BallGVGEnterPlaceRespMsg.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetPlaceDataFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      placeData_ = null;
      if (placeDataBuilder_ != null) {
        placeDataBuilder_.dispose();
        placeDataBuilder_ = null;
      }
      placeId_ = 0;
      transferTime_ = 0L;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_BallGVGEnterPlaceRespMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.BallGVGEnterPlaceRespMsg getDefaultInstanceForType() {
      return xddq.pb.BallGVGEnterPlaceRespMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.BallGVGEnterPlaceRespMsg build() {
      xddq.pb.BallGVGEnterPlaceRespMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.BallGVGEnterPlaceRespMsg buildPartial() {
      xddq.pb.BallGVGEnterPlaceRespMsg result = new xddq.pb.BallGVGEnterPlaceRespMsg(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.BallGVGEnterPlaceRespMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.placeData_ = placeDataBuilder_ == null
            ? placeData_
            : placeDataBuilder_.build();
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.placeId_ = placeId_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.transferTime_ = transferTime_;
        to_bitField0_ |= 0x00000008;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.BallGVGEnterPlaceRespMsg) {
        return mergeFrom((xddq.pb.BallGVGEnterPlaceRespMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.BallGVGEnterPlaceRespMsg other) {
      if (other == xddq.pb.BallGVGEnterPlaceRespMsg.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (other.hasPlaceData()) {
        mergePlaceData(other.getPlaceData());
      }
      if (other.hasPlaceId()) {
        setPlaceId(other.getPlaceId());
      }
      if (other.hasTransferTime()) {
        setTransferTime(other.getTransferTime());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      if (hasPlaceData()) {
        if (!getPlaceData().isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              input.readMessage(
                  internalGetPlaceDataFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 24: {
              placeId_ = input.readInt32();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 32: {
              transferTime_ = input.readInt64();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private xddq.pb.BallGVGPlaceDataTemp placeData_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.BallGVGPlaceDataTemp, xddq.pb.BallGVGPlaceDataTemp.Builder, xddq.pb.BallGVGPlaceDataTempOrBuilder> placeDataBuilder_;
    /**
     * <code>optional .xddq.pb.BallGVGPlaceDataTemp placeData = 2;</code>
     * @return Whether the placeData field is set.
     */
    public boolean hasPlaceData() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .xddq.pb.BallGVGPlaceDataTemp placeData = 2;</code>
     * @return The placeData.
     */
    public xddq.pb.BallGVGPlaceDataTemp getPlaceData() {
      if (placeDataBuilder_ == null) {
        return placeData_ == null ? xddq.pb.BallGVGPlaceDataTemp.getDefaultInstance() : placeData_;
      } else {
        return placeDataBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.BallGVGPlaceDataTemp placeData = 2;</code>
     */
    public Builder setPlaceData(xddq.pb.BallGVGPlaceDataTemp value) {
      if (placeDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        placeData_ = value;
      } else {
        placeDataBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.BallGVGPlaceDataTemp placeData = 2;</code>
     */
    public Builder setPlaceData(
        xddq.pb.BallGVGPlaceDataTemp.Builder builderForValue) {
      if (placeDataBuilder_ == null) {
        placeData_ = builderForValue.build();
      } else {
        placeDataBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.BallGVGPlaceDataTemp placeData = 2;</code>
     */
    public Builder mergePlaceData(xddq.pb.BallGVGPlaceDataTemp value) {
      if (placeDataBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0) &&
          placeData_ != null &&
          placeData_ != xddq.pb.BallGVGPlaceDataTemp.getDefaultInstance()) {
          getPlaceDataBuilder().mergeFrom(value);
        } else {
          placeData_ = value;
        }
      } else {
        placeDataBuilder_.mergeFrom(value);
      }
      if (placeData_ != null) {
        bitField0_ |= 0x00000002;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.BallGVGPlaceDataTemp placeData = 2;</code>
     */
    public Builder clearPlaceData() {
      bitField0_ = (bitField0_ & ~0x00000002);
      placeData_ = null;
      if (placeDataBuilder_ != null) {
        placeDataBuilder_.dispose();
        placeDataBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.BallGVGPlaceDataTemp placeData = 2;</code>
     */
    public xddq.pb.BallGVGPlaceDataTemp.Builder getPlaceDataBuilder() {
      bitField0_ |= 0x00000002;
      onChanged();
      return internalGetPlaceDataFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.BallGVGPlaceDataTemp placeData = 2;</code>
     */
    public xddq.pb.BallGVGPlaceDataTempOrBuilder getPlaceDataOrBuilder() {
      if (placeDataBuilder_ != null) {
        return placeDataBuilder_.getMessageOrBuilder();
      } else {
        return placeData_ == null ?
            xddq.pb.BallGVGPlaceDataTemp.getDefaultInstance() : placeData_;
      }
    }
    /**
     * <code>optional .xddq.pb.BallGVGPlaceDataTemp placeData = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.BallGVGPlaceDataTemp, xddq.pb.BallGVGPlaceDataTemp.Builder, xddq.pb.BallGVGPlaceDataTempOrBuilder> 
        internalGetPlaceDataFieldBuilder() {
      if (placeDataBuilder_ == null) {
        placeDataBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.BallGVGPlaceDataTemp, xddq.pb.BallGVGPlaceDataTemp.Builder, xddq.pb.BallGVGPlaceDataTempOrBuilder>(
                getPlaceData(),
                getParentForChildren(),
                isClean());
        placeData_ = null;
      }
      return placeDataBuilder_;
    }

    private int placeId_ ;
    /**
     * <code>optional int32 placeId = 3;</code>
     * @return Whether the placeId field is set.
     */
    @java.lang.Override
    public boolean hasPlaceId() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int32 placeId = 3;</code>
     * @return The placeId.
     */
    @java.lang.Override
    public int getPlaceId() {
      return placeId_;
    }
    /**
     * <code>optional int32 placeId = 3;</code>
     * @param value The placeId to set.
     * @return This builder for chaining.
     */
    public Builder setPlaceId(int value) {

      placeId_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 placeId = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearPlaceId() {
      bitField0_ = (bitField0_ & ~0x00000004);
      placeId_ = 0;
      onChanged();
      return this;
    }

    private long transferTime_ ;
    /**
     * <code>optional int64 transferTime = 4;</code>
     * @return Whether the transferTime field is set.
     */
    @java.lang.Override
    public boolean hasTransferTime() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int64 transferTime = 4;</code>
     * @return The transferTime.
     */
    @java.lang.Override
    public long getTransferTime() {
      return transferTime_;
    }
    /**
     * <code>optional int64 transferTime = 4;</code>
     * @param value The transferTime to set.
     * @return This builder for chaining.
     */
    public Builder setTransferTime(long value) {

      transferTime_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 transferTime = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearTransferTime() {
      bitField0_ = (bitField0_ & ~0x00000008);
      transferTime_ = 0L;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.BallGVGEnterPlaceRespMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.BallGVGEnterPlaceRespMsg)
  private static final xddq.pb.BallGVGEnterPlaceRespMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.BallGVGEnterPlaceRespMsg();
  }

  public static xddq.pb.BallGVGEnterPlaceRespMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<BallGVGEnterPlaceRespMsg>
      PARSER = new com.google.protobuf.AbstractParser<BallGVGEnterPlaceRespMsg>() {
    @java.lang.Override
    public BallGVGEnterPlaceRespMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<BallGVGEnterPlaceRespMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<BallGVGEnterPlaceRespMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.BallGVGEnterPlaceRespMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

