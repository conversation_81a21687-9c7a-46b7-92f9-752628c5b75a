// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.RebornTrialChallengeResp}
 */
public final class RebornTrialChallengeResp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.RebornTrialChallengeResp)
    RebornTrialChallengeRespOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      RebornTrialChallengeResp.class.getName());
  }
  // Use RebornTrialChallengeResp.newBuilder() to construct.
  private RebornTrialChallengeResp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private RebornTrialChallengeResp() {
    rewards_ = "";
    result_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_RebornTrialChallengeResp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_RebornTrialChallengeResp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.RebornTrialChallengeResp.class, xddq.pb.RebornTrialChallengeResp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int FREE_FIELD_NUMBER = 2;
  private int free_ = 0;
  /**
   * <code>optional int32 free = 2;</code>
   * @return Whether the free field is set.
   */
  @java.lang.Override
  public boolean hasFree() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int32 free = 2;</code>
   * @return The free.
   */
  @java.lang.Override
  public int getFree() {
    return free_;
  }

  public static final int BATTLERECOVERTIME_FIELD_NUMBER = 3;
  private long battleRecoverTime_ = 0L;
  /**
   * <code>optional int64 battleRecoverTime = 3;</code>
   * @return Whether the battleRecoverTime field is set.
   */
  @java.lang.Override
  public boolean hasBattleRecoverTime() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional int64 battleRecoverTime = 3;</code>
   * @return The battleRecoverTime.
   */
  @java.lang.Override
  public long getBattleRecoverTime() {
    return battleRecoverTime_;
  }

  public static final int REWARDS_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private volatile java.lang.Object rewards_ = "";
  /**
   * <code>optional string rewards = 4;</code>
   * @return Whether the rewards field is set.
   */
  @java.lang.Override
  public boolean hasRewards() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional string rewards = 4;</code>
   * @return The rewards.
   */
  @java.lang.Override
  public java.lang.String getRewards() {
    java.lang.Object ref = rewards_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        rewards_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string rewards = 4;</code>
   * @return The bytes for rewards.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getRewardsBytes() {
    java.lang.Object ref = rewards_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      rewards_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ADDSCORE_FIELD_NUMBER = 5;
  private long addScore_ = 0L;
  /**
   * <code>optional int64 addScore = 5;</code>
   * @return Whether the addScore field is set.
   */
  @java.lang.Override
  public boolean hasAddScore() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>optional int64 addScore = 5;</code>
   * @return The addScore.
   */
  @java.lang.Override
  public long getAddScore() {
    return addScore_;
  }

  public static final int REDUCESCORE_FIELD_NUMBER = 6;
  private long reduceScore_ = 0L;
  /**
   * <code>optional int64 reduceScore = 6;</code>
   * @return Whether the reduceScore field is set.
   */
  @java.lang.Override
  public boolean hasReduceScore() {
    return ((bitField0_ & 0x00000020) != 0);
  }
  /**
   * <code>optional int64 reduceScore = 6;</code>
   * @return The reduceScore.
   */
  @java.lang.Override
  public long getReduceScore() {
    return reduceScore_;
  }

  public static final int RESULT_FIELD_NUMBER = 7;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.RebornTrialUnionBattlePlayerMsg> result_;
  /**
   * <code>repeated .xddq.pb.RebornTrialUnionBattlePlayerMsg result = 7;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.RebornTrialUnionBattlePlayerMsg> getResultList() {
    return result_;
  }
  /**
   * <code>repeated .xddq.pb.RebornTrialUnionBattlePlayerMsg result = 7;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.RebornTrialUnionBattlePlayerMsgOrBuilder> 
      getResultOrBuilderList() {
    return result_;
  }
  /**
   * <code>repeated .xddq.pb.RebornTrialUnionBattlePlayerMsg result = 7;</code>
   */
  @java.lang.Override
  public int getResultCount() {
    return result_.size();
  }
  /**
   * <code>repeated .xddq.pb.RebornTrialUnionBattlePlayerMsg result = 7;</code>
   */
  @java.lang.Override
  public xddq.pb.RebornTrialUnionBattlePlayerMsg getResult(int index) {
    return result_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.RebornTrialUnionBattlePlayerMsg result = 7;</code>
   */
  @java.lang.Override
  public xddq.pb.RebornTrialUnionBattlePlayerMsgOrBuilder getResultOrBuilder(
      int index) {
    return result_.get(index);
  }

  public static final int DEFEATNUM_FIELD_NUMBER = 8;
  private int defeatNum_ = 0;
  /**
   * <code>optional int32 defeatNum = 8;</code>
   * @return Whether the defeatNum field is set.
   */
  @java.lang.Override
  public boolean hasDefeatNum() {
    return ((bitField0_ & 0x00000040) != 0);
  }
  /**
   * <code>optional int32 defeatNum = 8;</code>
   * @return The defeatNum.
   */
  @java.lang.Override
  public int getDefeatNum() {
    return defeatNum_;
  }

  public static final int ACTIVITYID_FIELD_NUMBER = 9;
  private int activityId_ = 0;
  /**
   * <code>optional int32 activityId = 9;</code>
   * @return Whether the activityId field is set.
   */
  @java.lang.Override
  public boolean hasActivityId() {
    return ((bitField0_ & 0x00000080) != 0);
  }
  /**
   * <code>optional int32 activityId = 9;</code>
   * @return The activityId.
   */
  @java.lang.Override
  public int getActivityId() {
    return activityId_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(2, free_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt64(3, battleRecoverTime_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 4, rewards_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      output.writeInt64(5, addScore_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      output.writeInt64(6, reduceScore_);
    }
    for (int i = 0; i < result_.size(); i++) {
      output.writeMessage(7, result_.get(i));
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      output.writeInt32(8, defeatNum_);
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      output.writeInt32(9, activityId_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, free_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(3, battleRecoverTime_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(4, rewards_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(5, addScore_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(6, reduceScore_);
    }
    for (int i = 0; i < result_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(7, result_.get(i));
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(8, defeatNum_);
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(9, activityId_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.RebornTrialChallengeResp)) {
      return super.equals(obj);
    }
    xddq.pb.RebornTrialChallengeResp other = (xddq.pb.RebornTrialChallengeResp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (hasFree() != other.hasFree()) return false;
    if (hasFree()) {
      if (getFree()
          != other.getFree()) return false;
    }
    if (hasBattleRecoverTime() != other.hasBattleRecoverTime()) return false;
    if (hasBattleRecoverTime()) {
      if (getBattleRecoverTime()
          != other.getBattleRecoverTime()) return false;
    }
    if (hasRewards() != other.hasRewards()) return false;
    if (hasRewards()) {
      if (!getRewards()
          .equals(other.getRewards())) return false;
    }
    if (hasAddScore() != other.hasAddScore()) return false;
    if (hasAddScore()) {
      if (getAddScore()
          != other.getAddScore()) return false;
    }
    if (hasReduceScore() != other.hasReduceScore()) return false;
    if (hasReduceScore()) {
      if (getReduceScore()
          != other.getReduceScore()) return false;
    }
    if (!getResultList()
        .equals(other.getResultList())) return false;
    if (hasDefeatNum() != other.hasDefeatNum()) return false;
    if (hasDefeatNum()) {
      if (getDefeatNum()
          != other.getDefeatNum()) return false;
    }
    if (hasActivityId() != other.hasActivityId()) return false;
    if (hasActivityId()) {
      if (getActivityId()
          != other.getActivityId()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (hasFree()) {
      hash = (37 * hash) + FREE_FIELD_NUMBER;
      hash = (53 * hash) + getFree();
    }
    if (hasBattleRecoverTime()) {
      hash = (37 * hash) + BATTLERECOVERTIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getBattleRecoverTime());
    }
    if (hasRewards()) {
      hash = (37 * hash) + REWARDS_FIELD_NUMBER;
      hash = (53 * hash) + getRewards().hashCode();
    }
    if (hasAddScore()) {
      hash = (37 * hash) + ADDSCORE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getAddScore());
    }
    if (hasReduceScore()) {
      hash = (37 * hash) + REDUCESCORE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getReduceScore());
    }
    if (getResultCount() > 0) {
      hash = (37 * hash) + RESULT_FIELD_NUMBER;
      hash = (53 * hash) + getResultList().hashCode();
    }
    if (hasDefeatNum()) {
      hash = (37 * hash) + DEFEATNUM_FIELD_NUMBER;
      hash = (53 * hash) + getDefeatNum();
    }
    if (hasActivityId()) {
      hash = (37 * hash) + ACTIVITYID_FIELD_NUMBER;
      hash = (53 * hash) + getActivityId();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.RebornTrialChallengeResp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.RebornTrialChallengeResp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.RebornTrialChallengeResp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.RebornTrialChallengeResp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.RebornTrialChallengeResp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.RebornTrialChallengeResp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.RebornTrialChallengeResp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.RebornTrialChallengeResp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.RebornTrialChallengeResp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.RebornTrialChallengeResp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.RebornTrialChallengeResp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.RebornTrialChallengeResp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.RebornTrialChallengeResp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.RebornTrialChallengeResp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.RebornTrialChallengeResp)
      xddq.pb.RebornTrialChallengeRespOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_RebornTrialChallengeResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_RebornTrialChallengeResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.RebornTrialChallengeResp.class, xddq.pb.RebornTrialChallengeResp.Builder.class);
    }

    // Construct using xddq.pb.RebornTrialChallengeResp.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      free_ = 0;
      battleRecoverTime_ = 0L;
      rewards_ = "";
      addScore_ = 0L;
      reduceScore_ = 0L;
      if (resultBuilder_ == null) {
        result_ = java.util.Collections.emptyList();
      } else {
        result_ = null;
        resultBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000040);
      defeatNum_ = 0;
      activityId_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_RebornTrialChallengeResp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.RebornTrialChallengeResp getDefaultInstanceForType() {
      return xddq.pb.RebornTrialChallengeResp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.RebornTrialChallengeResp build() {
      xddq.pb.RebornTrialChallengeResp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.RebornTrialChallengeResp buildPartial() {
      xddq.pb.RebornTrialChallengeResp result = new xddq.pb.RebornTrialChallengeResp(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.RebornTrialChallengeResp result) {
      if (resultBuilder_ == null) {
        if (((bitField0_ & 0x00000040) != 0)) {
          result_ = java.util.Collections.unmodifiableList(result_);
          bitField0_ = (bitField0_ & ~0x00000040);
        }
        result.result_ = result_;
      } else {
        result.result_ = resultBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.RebornTrialChallengeResp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.free_ = free_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.battleRecoverTime_ = battleRecoverTime_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.rewards_ = rewards_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.addScore_ = addScore_;
        to_bitField0_ |= 0x00000010;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.reduceScore_ = reduceScore_;
        to_bitField0_ |= 0x00000020;
      }
      if (((from_bitField0_ & 0x00000080) != 0)) {
        result.defeatNum_ = defeatNum_;
        to_bitField0_ |= 0x00000040;
      }
      if (((from_bitField0_ & 0x00000100) != 0)) {
        result.activityId_ = activityId_;
        to_bitField0_ |= 0x00000080;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.RebornTrialChallengeResp) {
        return mergeFrom((xddq.pb.RebornTrialChallengeResp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.RebornTrialChallengeResp other) {
      if (other == xddq.pb.RebornTrialChallengeResp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (other.hasFree()) {
        setFree(other.getFree());
      }
      if (other.hasBattleRecoverTime()) {
        setBattleRecoverTime(other.getBattleRecoverTime());
      }
      if (other.hasRewards()) {
        rewards_ = other.rewards_;
        bitField0_ |= 0x00000008;
        onChanged();
      }
      if (other.hasAddScore()) {
        setAddScore(other.getAddScore());
      }
      if (other.hasReduceScore()) {
        setReduceScore(other.getReduceScore());
      }
      if (resultBuilder_ == null) {
        if (!other.result_.isEmpty()) {
          if (result_.isEmpty()) {
            result_ = other.result_;
            bitField0_ = (bitField0_ & ~0x00000040);
          } else {
            ensureResultIsMutable();
            result_.addAll(other.result_);
          }
          onChanged();
        }
      } else {
        if (!other.result_.isEmpty()) {
          if (resultBuilder_.isEmpty()) {
            resultBuilder_.dispose();
            resultBuilder_ = null;
            result_ = other.result_;
            bitField0_ = (bitField0_ & ~0x00000040);
            resultBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetResultFieldBuilder() : null;
          } else {
            resultBuilder_.addAllMessages(other.result_);
          }
        }
      }
      if (other.hasDefeatNum()) {
        setDefeatNum(other.getDefeatNum());
      }
      if (other.hasActivityId()) {
        setActivityId(other.getActivityId());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              free_ = input.readInt32();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 24: {
              battleRecoverTime_ = input.readInt64();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 34: {
              rewards_ = input.readBytes();
              bitField0_ |= 0x00000008;
              break;
            } // case 34
            case 40: {
              addScore_ = input.readInt64();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            case 48: {
              reduceScore_ = input.readInt64();
              bitField0_ |= 0x00000020;
              break;
            } // case 48
            case 58: {
              xddq.pb.RebornTrialUnionBattlePlayerMsg m =
                  input.readMessage(
                      xddq.pb.RebornTrialUnionBattlePlayerMsg.parser(),
                      extensionRegistry);
              if (resultBuilder_ == null) {
                ensureResultIsMutable();
                result_.add(m);
              } else {
                resultBuilder_.addMessage(m);
              }
              break;
            } // case 58
            case 64: {
              defeatNum_ = input.readInt32();
              bitField0_ |= 0x00000080;
              break;
            } // case 64
            case 72: {
              activityId_ = input.readInt32();
              bitField0_ |= 0x00000100;
              break;
            } // case 72
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private int free_ ;
    /**
     * <code>optional int32 free = 2;</code>
     * @return Whether the free field is set.
     */
    @java.lang.Override
    public boolean hasFree() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 free = 2;</code>
     * @return The free.
     */
    @java.lang.Override
    public int getFree() {
      return free_;
    }
    /**
     * <code>optional int32 free = 2;</code>
     * @param value The free to set.
     * @return This builder for chaining.
     */
    public Builder setFree(int value) {

      free_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 free = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearFree() {
      bitField0_ = (bitField0_ & ~0x00000002);
      free_ = 0;
      onChanged();
      return this;
    }

    private long battleRecoverTime_ ;
    /**
     * <code>optional int64 battleRecoverTime = 3;</code>
     * @return Whether the battleRecoverTime field is set.
     */
    @java.lang.Override
    public boolean hasBattleRecoverTime() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int64 battleRecoverTime = 3;</code>
     * @return The battleRecoverTime.
     */
    @java.lang.Override
    public long getBattleRecoverTime() {
      return battleRecoverTime_;
    }
    /**
     * <code>optional int64 battleRecoverTime = 3;</code>
     * @param value The battleRecoverTime to set.
     * @return This builder for chaining.
     */
    public Builder setBattleRecoverTime(long value) {

      battleRecoverTime_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 battleRecoverTime = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearBattleRecoverTime() {
      bitField0_ = (bitField0_ & ~0x00000004);
      battleRecoverTime_ = 0L;
      onChanged();
      return this;
    }

    private java.lang.Object rewards_ = "";
    /**
     * <code>optional string rewards = 4;</code>
     * @return Whether the rewards field is set.
     */
    public boolean hasRewards() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional string rewards = 4;</code>
     * @return The rewards.
     */
    public java.lang.String getRewards() {
      java.lang.Object ref = rewards_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          rewards_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string rewards = 4;</code>
     * @return The bytes for rewards.
     */
    public com.google.protobuf.ByteString
        getRewardsBytes() {
      java.lang.Object ref = rewards_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        rewards_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string rewards = 4;</code>
     * @param value The rewards to set.
     * @return This builder for chaining.
     */
    public Builder setRewards(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      rewards_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional string rewards = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearRewards() {
      rewards_ = getDefaultInstance().getRewards();
      bitField0_ = (bitField0_ & ~0x00000008);
      onChanged();
      return this;
    }
    /**
     * <code>optional string rewards = 4;</code>
     * @param value The bytes for rewards to set.
     * @return This builder for chaining.
     */
    public Builder setRewardsBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      rewards_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }

    private long addScore_ ;
    /**
     * <code>optional int64 addScore = 5;</code>
     * @return Whether the addScore field is set.
     */
    @java.lang.Override
    public boolean hasAddScore() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional int64 addScore = 5;</code>
     * @return The addScore.
     */
    @java.lang.Override
    public long getAddScore() {
      return addScore_;
    }
    /**
     * <code>optional int64 addScore = 5;</code>
     * @param value The addScore to set.
     * @return This builder for chaining.
     */
    public Builder setAddScore(long value) {

      addScore_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 addScore = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearAddScore() {
      bitField0_ = (bitField0_ & ~0x00000010);
      addScore_ = 0L;
      onChanged();
      return this;
    }

    private long reduceScore_ ;
    /**
     * <code>optional int64 reduceScore = 6;</code>
     * @return Whether the reduceScore field is set.
     */
    @java.lang.Override
    public boolean hasReduceScore() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional int64 reduceScore = 6;</code>
     * @return The reduceScore.
     */
    @java.lang.Override
    public long getReduceScore() {
      return reduceScore_;
    }
    /**
     * <code>optional int64 reduceScore = 6;</code>
     * @param value The reduceScore to set.
     * @return This builder for chaining.
     */
    public Builder setReduceScore(long value) {

      reduceScore_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 reduceScore = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearReduceScore() {
      bitField0_ = (bitField0_ & ~0x00000020);
      reduceScore_ = 0L;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.RebornTrialUnionBattlePlayerMsg> result_ =
      java.util.Collections.emptyList();
    private void ensureResultIsMutable() {
      if (!((bitField0_ & 0x00000040) != 0)) {
        result_ = new java.util.ArrayList<xddq.pb.RebornTrialUnionBattlePlayerMsg>(result_);
        bitField0_ |= 0x00000040;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.RebornTrialUnionBattlePlayerMsg, xddq.pb.RebornTrialUnionBattlePlayerMsg.Builder, xddq.pb.RebornTrialUnionBattlePlayerMsgOrBuilder> resultBuilder_;

    /**
     * <code>repeated .xddq.pb.RebornTrialUnionBattlePlayerMsg result = 7;</code>
     */
    public java.util.List<xddq.pb.RebornTrialUnionBattlePlayerMsg> getResultList() {
      if (resultBuilder_ == null) {
        return java.util.Collections.unmodifiableList(result_);
      } else {
        return resultBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.RebornTrialUnionBattlePlayerMsg result = 7;</code>
     */
    public int getResultCount() {
      if (resultBuilder_ == null) {
        return result_.size();
      } else {
        return resultBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.RebornTrialUnionBattlePlayerMsg result = 7;</code>
     */
    public xddq.pb.RebornTrialUnionBattlePlayerMsg getResult(int index) {
      if (resultBuilder_ == null) {
        return result_.get(index);
      } else {
        return resultBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.RebornTrialUnionBattlePlayerMsg result = 7;</code>
     */
    public Builder setResult(
        int index, xddq.pb.RebornTrialUnionBattlePlayerMsg value) {
      if (resultBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureResultIsMutable();
        result_.set(index, value);
        onChanged();
      } else {
        resultBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.RebornTrialUnionBattlePlayerMsg result = 7;</code>
     */
    public Builder setResult(
        int index, xddq.pb.RebornTrialUnionBattlePlayerMsg.Builder builderForValue) {
      if (resultBuilder_ == null) {
        ensureResultIsMutable();
        result_.set(index, builderForValue.build());
        onChanged();
      } else {
        resultBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.RebornTrialUnionBattlePlayerMsg result = 7;</code>
     */
    public Builder addResult(xddq.pb.RebornTrialUnionBattlePlayerMsg value) {
      if (resultBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureResultIsMutable();
        result_.add(value);
        onChanged();
      } else {
        resultBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.RebornTrialUnionBattlePlayerMsg result = 7;</code>
     */
    public Builder addResult(
        int index, xddq.pb.RebornTrialUnionBattlePlayerMsg value) {
      if (resultBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureResultIsMutable();
        result_.add(index, value);
        onChanged();
      } else {
        resultBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.RebornTrialUnionBattlePlayerMsg result = 7;</code>
     */
    public Builder addResult(
        xddq.pb.RebornTrialUnionBattlePlayerMsg.Builder builderForValue) {
      if (resultBuilder_ == null) {
        ensureResultIsMutable();
        result_.add(builderForValue.build());
        onChanged();
      } else {
        resultBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.RebornTrialUnionBattlePlayerMsg result = 7;</code>
     */
    public Builder addResult(
        int index, xddq.pb.RebornTrialUnionBattlePlayerMsg.Builder builderForValue) {
      if (resultBuilder_ == null) {
        ensureResultIsMutable();
        result_.add(index, builderForValue.build());
        onChanged();
      } else {
        resultBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.RebornTrialUnionBattlePlayerMsg result = 7;</code>
     */
    public Builder addAllResult(
        java.lang.Iterable<? extends xddq.pb.RebornTrialUnionBattlePlayerMsg> values) {
      if (resultBuilder_ == null) {
        ensureResultIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, result_);
        onChanged();
      } else {
        resultBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.RebornTrialUnionBattlePlayerMsg result = 7;</code>
     */
    public Builder clearResult() {
      if (resultBuilder_ == null) {
        result_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000040);
        onChanged();
      } else {
        resultBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.RebornTrialUnionBattlePlayerMsg result = 7;</code>
     */
    public Builder removeResult(int index) {
      if (resultBuilder_ == null) {
        ensureResultIsMutable();
        result_.remove(index);
        onChanged();
      } else {
        resultBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.RebornTrialUnionBattlePlayerMsg result = 7;</code>
     */
    public xddq.pb.RebornTrialUnionBattlePlayerMsg.Builder getResultBuilder(
        int index) {
      return internalGetResultFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.RebornTrialUnionBattlePlayerMsg result = 7;</code>
     */
    public xddq.pb.RebornTrialUnionBattlePlayerMsgOrBuilder getResultOrBuilder(
        int index) {
      if (resultBuilder_ == null) {
        return result_.get(index);  } else {
        return resultBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.RebornTrialUnionBattlePlayerMsg result = 7;</code>
     */
    public java.util.List<? extends xddq.pb.RebornTrialUnionBattlePlayerMsgOrBuilder> 
         getResultOrBuilderList() {
      if (resultBuilder_ != null) {
        return resultBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(result_);
      }
    }
    /**
     * <code>repeated .xddq.pb.RebornTrialUnionBattlePlayerMsg result = 7;</code>
     */
    public xddq.pb.RebornTrialUnionBattlePlayerMsg.Builder addResultBuilder() {
      return internalGetResultFieldBuilder().addBuilder(
          xddq.pb.RebornTrialUnionBattlePlayerMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.RebornTrialUnionBattlePlayerMsg result = 7;</code>
     */
    public xddq.pb.RebornTrialUnionBattlePlayerMsg.Builder addResultBuilder(
        int index) {
      return internalGetResultFieldBuilder().addBuilder(
          index, xddq.pb.RebornTrialUnionBattlePlayerMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.RebornTrialUnionBattlePlayerMsg result = 7;</code>
     */
    public java.util.List<xddq.pb.RebornTrialUnionBattlePlayerMsg.Builder> 
         getResultBuilderList() {
      return internalGetResultFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.RebornTrialUnionBattlePlayerMsg, xddq.pb.RebornTrialUnionBattlePlayerMsg.Builder, xddq.pb.RebornTrialUnionBattlePlayerMsgOrBuilder> 
        internalGetResultFieldBuilder() {
      if (resultBuilder_ == null) {
        resultBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.RebornTrialUnionBattlePlayerMsg, xddq.pb.RebornTrialUnionBattlePlayerMsg.Builder, xddq.pb.RebornTrialUnionBattlePlayerMsgOrBuilder>(
                result_,
                ((bitField0_ & 0x00000040) != 0),
                getParentForChildren(),
                isClean());
        result_ = null;
      }
      return resultBuilder_;
    }

    private int defeatNum_ ;
    /**
     * <code>optional int32 defeatNum = 8;</code>
     * @return Whether the defeatNum field is set.
     */
    @java.lang.Override
    public boolean hasDefeatNum() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <code>optional int32 defeatNum = 8;</code>
     * @return The defeatNum.
     */
    @java.lang.Override
    public int getDefeatNum() {
      return defeatNum_;
    }
    /**
     * <code>optional int32 defeatNum = 8;</code>
     * @param value The defeatNum to set.
     * @return This builder for chaining.
     */
    public Builder setDefeatNum(int value) {

      defeatNum_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 defeatNum = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearDefeatNum() {
      bitField0_ = (bitField0_ & ~0x00000080);
      defeatNum_ = 0;
      onChanged();
      return this;
    }

    private int activityId_ ;
    /**
     * <code>optional int32 activityId = 9;</code>
     * @return Whether the activityId field is set.
     */
    @java.lang.Override
    public boolean hasActivityId() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <code>optional int32 activityId = 9;</code>
     * @return The activityId.
     */
    @java.lang.Override
    public int getActivityId() {
      return activityId_;
    }
    /**
     * <code>optional int32 activityId = 9;</code>
     * @param value The activityId to set.
     * @return This builder for chaining.
     */
    public Builder setActivityId(int value) {

      activityId_ = value;
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 activityId = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearActivityId() {
      bitField0_ = (bitField0_ & ~0x00000100);
      activityId_ = 0;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.RebornTrialChallengeResp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.RebornTrialChallengeResp)
  private static final xddq.pb.RebornTrialChallengeResp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.RebornTrialChallengeResp();
  }

  public static xddq.pb.RebornTrialChallengeResp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<RebornTrialChallengeResp>
      PARSER = new com.google.protobuf.AbstractParser<RebornTrialChallengeResp>() {
    @java.lang.Override
    public RebornTrialChallengeResp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<RebornTrialChallengeResp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<RebornTrialChallengeResp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.RebornTrialChallengeResp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

