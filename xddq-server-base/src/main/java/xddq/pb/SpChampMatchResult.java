// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.SpChampMatchResult}
 */
public final class SpChampMatchResult extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.SpChampMatchResult)
    SpChampMatchResultOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      SpChampMatchResult.class.getName());
  }
  // Use SpChampMatchResult.newBuilder() to construct.
  private SpChampMatchResult(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private SpChampMatchResult() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_SpChampMatchResult_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_SpChampMatchResult_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.SpChampMatchResult.class, xddq.pb.SpChampMatchResult.Builder.class);
  }

  private int bitField0_;
  public static final int MATCHID_FIELD_NUMBER = 1;
  private long matchId_ = 0L;
  /**
   * <code>optional int64 matchId = 1;</code>
   * @return Whether the matchId field is set.
   */
  @java.lang.Override
  public boolean hasMatchId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int64 matchId = 1;</code>
   * @return The matchId.
   */
  @java.lang.Override
  public long getMatchId() {
    return matchId_;
  }

  public static final int LEFTPLAYERINFO_FIELD_NUMBER = 2;
  private xddq.pb.SpChampToSkyPlayerInfo leftPlayerInfo_;
  /**
   * <code>optional .xddq.pb.SpChampToSkyPlayerInfo leftPlayerInfo = 2;</code>
   * @return Whether the leftPlayerInfo field is set.
   */
  @java.lang.Override
  public boolean hasLeftPlayerInfo() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional .xddq.pb.SpChampToSkyPlayerInfo leftPlayerInfo = 2;</code>
   * @return The leftPlayerInfo.
   */
  @java.lang.Override
  public xddq.pb.SpChampToSkyPlayerInfo getLeftPlayerInfo() {
    return leftPlayerInfo_ == null ? xddq.pb.SpChampToSkyPlayerInfo.getDefaultInstance() : leftPlayerInfo_;
  }
  /**
   * <code>optional .xddq.pb.SpChampToSkyPlayerInfo leftPlayerInfo = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.SpChampToSkyPlayerInfoOrBuilder getLeftPlayerInfoOrBuilder() {
    return leftPlayerInfo_ == null ? xddq.pb.SpChampToSkyPlayerInfo.getDefaultInstance() : leftPlayerInfo_;
  }

  public static final int RIGHTPLAYERINFO_FIELD_NUMBER = 3;
  private xddq.pb.SpChampToSkyPlayerInfo rightPlayerInfo_;
  /**
   * <code>optional .xddq.pb.SpChampToSkyPlayerInfo rightPlayerInfo = 3;</code>
   * @return Whether the rightPlayerInfo field is set.
   */
  @java.lang.Override
  public boolean hasRightPlayerInfo() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional .xddq.pb.SpChampToSkyPlayerInfo rightPlayerInfo = 3;</code>
   * @return The rightPlayerInfo.
   */
  @java.lang.Override
  public xddq.pb.SpChampToSkyPlayerInfo getRightPlayerInfo() {
    return rightPlayerInfo_ == null ? xddq.pb.SpChampToSkyPlayerInfo.getDefaultInstance() : rightPlayerInfo_;
  }
  /**
   * <code>optional .xddq.pb.SpChampToSkyPlayerInfo rightPlayerInfo = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.SpChampToSkyPlayerInfoOrBuilder getRightPlayerInfoOrBuilder() {
    return rightPlayerInfo_ == null ? xddq.pb.SpChampToSkyPlayerInfo.getDefaultInstance() : rightPlayerInfo_;
  }

  public static final int WINNERID_FIELD_NUMBER = 4;
  private long winnerId_ = 0L;
  /**
   * <code>optional int64 winnerId = 4;</code>
   * @return Whether the winnerId field is set.
   */
  @java.lang.Override
  public boolean hasWinnerId() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional int64 winnerId = 4;</code>
   * @return The winnerId.
   */
  @java.lang.Override
  public long getWinnerId() {
    return winnerId_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt64(1, matchId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeMessage(2, getLeftPlayerInfo());
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeMessage(3, getRightPlayerInfo());
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt64(4, winnerId_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(1, matchId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getLeftPlayerInfo());
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, getRightPlayerInfo());
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(4, winnerId_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.SpChampMatchResult)) {
      return super.equals(obj);
    }
    xddq.pb.SpChampMatchResult other = (xddq.pb.SpChampMatchResult) obj;

    if (hasMatchId() != other.hasMatchId()) return false;
    if (hasMatchId()) {
      if (getMatchId()
          != other.getMatchId()) return false;
    }
    if (hasLeftPlayerInfo() != other.hasLeftPlayerInfo()) return false;
    if (hasLeftPlayerInfo()) {
      if (!getLeftPlayerInfo()
          .equals(other.getLeftPlayerInfo())) return false;
    }
    if (hasRightPlayerInfo() != other.hasRightPlayerInfo()) return false;
    if (hasRightPlayerInfo()) {
      if (!getRightPlayerInfo()
          .equals(other.getRightPlayerInfo())) return false;
    }
    if (hasWinnerId() != other.hasWinnerId()) return false;
    if (hasWinnerId()) {
      if (getWinnerId()
          != other.getWinnerId()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasMatchId()) {
      hash = (37 * hash) + MATCHID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getMatchId());
    }
    if (hasLeftPlayerInfo()) {
      hash = (37 * hash) + LEFTPLAYERINFO_FIELD_NUMBER;
      hash = (53 * hash) + getLeftPlayerInfo().hashCode();
    }
    if (hasRightPlayerInfo()) {
      hash = (37 * hash) + RIGHTPLAYERINFO_FIELD_NUMBER;
      hash = (53 * hash) + getRightPlayerInfo().hashCode();
    }
    if (hasWinnerId()) {
      hash = (37 * hash) + WINNERID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getWinnerId());
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.SpChampMatchResult parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.SpChampMatchResult parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.SpChampMatchResult parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.SpChampMatchResult parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.SpChampMatchResult parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.SpChampMatchResult parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.SpChampMatchResult parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.SpChampMatchResult parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.SpChampMatchResult parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.SpChampMatchResult parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.SpChampMatchResult parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.SpChampMatchResult parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.SpChampMatchResult prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.SpChampMatchResult}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.SpChampMatchResult)
      xddq.pb.SpChampMatchResultOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_SpChampMatchResult_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_SpChampMatchResult_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.SpChampMatchResult.class, xddq.pb.SpChampMatchResult.Builder.class);
    }

    // Construct using xddq.pb.SpChampMatchResult.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetLeftPlayerInfoFieldBuilder();
        internalGetRightPlayerInfoFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      matchId_ = 0L;
      leftPlayerInfo_ = null;
      if (leftPlayerInfoBuilder_ != null) {
        leftPlayerInfoBuilder_.dispose();
        leftPlayerInfoBuilder_ = null;
      }
      rightPlayerInfo_ = null;
      if (rightPlayerInfoBuilder_ != null) {
        rightPlayerInfoBuilder_.dispose();
        rightPlayerInfoBuilder_ = null;
      }
      winnerId_ = 0L;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_SpChampMatchResult_descriptor;
    }

    @java.lang.Override
    public xddq.pb.SpChampMatchResult getDefaultInstanceForType() {
      return xddq.pb.SpChampMatchResult.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.SpChampMatchResult build() {
      xddq.pb.SpChampMatchResult result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.SpChampMatchResult buildPartial() {
      xddq.pb.SpChampMatchResult result = new xddq.pb.SpChampMatchResult(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.SpChampMatchResult result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.matchId_ = matchId_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.leftPlayerInfo_ = leftPlayerInfoBuilder_ == null
            ? leftPlayerInfo_
            : leftPlayerInfoBuilder_.build();
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.rightPlayerInfo_ = rightPlayerInfoBuilder_ == null
            ? rightPlayerInfo_
            : rightPlayerInfoBuilder_.build();
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.winnerId_ = winnerId_;
        to_bitField0_ |= 0x00000008;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.SpChampMatchResult) {
        return mergeFrom((xddq.pb.SpChampMatchResult)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.SpChampMatchResult other) {
      if (other == xddq.pb.SpChampMatchResult.getDefaultInstance()) return this;
      if (other.hasMatchId()) {
        setMatchId(other.getMatchId());
      }
      if (other.hasLeftPlayerInfo()) {
        mergeLeftPlayerInfo(other.getLeftPlayerInfo());
      }
      if (other.hasRightPlayerInfo()) {
        mergeRightPlayerInfo(other.getRightPlayerInfo());
      }
      if (other.hasWinnerId()) {
        setWinnerId(other.getWinnerId());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              matchId_ = input.readInt64();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              input.readMessage(
                  internalGetLeftPlayerInfoFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 26: {
              input.readMessage(
                  internalGetRightPlayerInfoFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 32: {
              winnerId_ = input.readInt64();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private long matchId_ ;
    /**
     * <code>optional int64 matchId = 1;</code>
     * @return Whether the matchId field is set.
     */
    @java.lang.Override
    public boolean hasMatchId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 matchId = 1;</code>
     * @return The matchId.
     */
    @java.lang.Override
    public long getMatchId() {
      return matchId_;
    }
    /**
     * <code>optional int64 matchId = 1;</code>
     * @param value The matchId to set.
     * @return This builder for chaining.
     */
    public Builder setMatchId(long value) {

      matchId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 matchId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearMatchId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      matchId_ = 0L;
      onChanged();
      return this;
    }

    private xddq.pb.SpChampToSkyPlayerInfo leftPlayerInfo_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.SpChampToSkyPlayerInfo, xddq.pb.SpChampToSkyPlayerInfo.Builder, xddq.pb.SpChampToSkyPlayerInfoOrBuilder> leftPlayerInfoBuilder_;
    /**
     * <code>optional .xddq.pb.SpChampToSkyPlayerInfo leftPlayerInfo = 2;</code>
     * @return Whether the leftPlayerInfo field is set.
     */
    public boolean hasLeftPlayerInfo() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .xddq.pb.SpChampToSkyPlayerInfo leftPlayerInfo = 2;</code>
     * @return The leftPlayerInfo.
     */
    public xddq.pb.SpChampToSkyPlayerInfo getLeftPlayerInfo() {
      if (leftPlayerInfoBuilder_ == null) {
        return leftPlayerInfo_ == null ? xddq.pb.SpChampToSkyPlayerInfo.getDefaultInstance() : leftPlayerInfo_;
      } else {
        return leftPlayerInfoBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.SpChampToSkyPlayerInfo leftPlayerInfo = 2;</code>
     */
    public Builder setLeftPlayerInfo(xddq.pb.SpChampToSkyPlayerInfo value) {
      if (leftPlayerInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        leftPlayerInfo_ = value;
      } else {
        leftPlayerInfoBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.SpChampToSkyPlayerInfo leftPlayerInfo = 2;</code>
     */
    public Builder setLeftPlayerInfo(
        xddq.pb.SpChampToSkyPlayerInfo.Builder builderForValue) {
      if (leftPlayerInfoBuilder_ == null) {
        leftPlayerInfo_ = builderForValue.build();
      } else {
        leftPlayerInfoBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.SpChampToSkyPlayerInfo leftPlayerInfo = 2;</code>
     */
    public Builder mergeLeftPlayerInfo(xddq.pb.SpChampToSkyPlayerInfo value) {
      if (leftPlayerInfoBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0) &&
          leftPlayerInfo_ != null &&
          leftPlayerInfo_ != xddq.pb.SpChampToSkyPlayerInfo.getDefaultInstance()) {
          getLeftPlayerInfoBuilder().mergeFrom(value);
        } else {
          leftPlayerInfo_ = value;
        }
      } else {
        leftPlayerInfoBuilder_.mergeFrom(value);
      }
      if (leftPlayerInfo_ != null) {
        bitField0_ |= 0x00000002;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.SpChampToSkyPlayerInfo leftPlayerInfo = 2;</code>
     */
    public Builder clearLeftPlayerInfo() {
      bitField0_ = (bitField0_ & ~0x00000002);
      leftPlayerInfo_ = null;
      if (leftPlayerInfoBuilder_ != null) {
        leftPlayerInfoBuilder_.dispose();
        leftPlayerInfoBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.SpChampToSkyPlayerInfo leftPlayerInfo = 2;</code>
     */
    public xddq.pb.SpChampToSkyPlayerInfo.Builder getLeftPlayerInfoBuilder() {
      bitField0_ |= 0x00000002;
      onChanged();
      return internalGetLeftPlayerInfoFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.SpChampToSkyPlayerInfo leftPlayerInfo = 2;</code>
     */
    public xddq.pb.SpChampToSkyPlayerInfoOrBuilder getLeftPlayerInfoOrBuilder() {
      if (leftPlayerInfoBuilder_ != null) {
        return leftPlayerInfoBuilder_.getMessageOrBuilder();
      } else {
        return leftPlayerInfo_ == null ?
            xddq.pb.SpChampToSkyPlayerInfo.getDefaultInstance() : leftPlayerInfo_;
      }
    }
    /**
     * <code>optional .xddq.pb.SpChampToSkyPlayerInfo leftPlayerInfo = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.SpChampToSkyPlayerInfo, xddq.pb.SpChampToSkyPlayerInfo.Builder, xddq.pb.SpChampToSkyPlayerInfoOrBuilder> 
        internalGetLeftPlayerInfoFieldBuilder() {
      if (leftPlayerInfoBuilder_ == null) {
        leftPlayerInfoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.SpChampToSkyPlayerInfo, xddq.pb.SpChampToSkyPlayerInfo.Builder, xddq.pb.SpChampToSkyPlayerInfoOrBuilder>(
                getLeftPlayerInfo(),
                getParentForChildren(),
                isClean());
        leftPlayerInfo_ = null;
      }
      return leftPlayerInfoBuilder_;
    }

    private xddq.pb.SpChampToSkyPlayerInfo rightPlayerInfo_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.SpChampToSkyPlayerInfo, xddq.pb.SpChampToSkyPlayerInfo.Builder, xddq.pb.SpChampToSkyPlayerInfoOrBuilder> rightPlayerInfoBuilder_;
    /**
     * <code>optional .xddq.pb.SpChampToSkyPlayerInfo rightPlayerInfo = 3;</code>
     * @return Whether the rightPlayerInfo field is set.
     */
    public boolean hasRightPlayerInfo() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional .xddq.pb.SpChampToSkyPlayerInfo rightPlayerInfo = 3;</code>
     * @return The rightPlayerInfo.
     */
    public xddq.pb.SpChampToSkyPlayerInfo getRightPlayerInfo() {
      if (rightPlayerInfoBuilder_ == null) {
        return rightPlayerInfo_ == null ? xddq.pb.SpChampToSkyPlayerInfo.getDefaultInstance() : rightPlayerInfo_;
      } else {
        return rightPlayerInfoBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.SpChampToSkyPlayerInfo rightPlayerInfo = 3;</code>
     */
    public Builder setRightPlayerInfo(xddq.pb.SpChampToSkyPlayerInfo value) {
      if (rightPlayerInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        rightPlayerInfo_ = value;
      } else {
        rightPlayerInfoBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.SpChampToSkyPlayerInfo rightPlayerInfo = 3;</code>
     */
    public Builder setRightPlayerInfo(
        xddq.pb.SpChampToSkyPlayerInfo.Builder builderForValue) {
      if (rightPlayerInfoBuilder_ == null) {
        rightPlayerInfo_ = builderForValue.build();
      } else {
        rightPlayerInfoBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.SpChampToSkyPlayerInfo rightPlayerInfo = 3;</code>
     */
    public Builder mergeRightPlayerInfo(xddq.pb.SpChampToSkyPlayerInfo value) {
      if (rightPlayerInfoBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0) &&
          rightPlayerInfo_ != null &&
          rightPlayerInfo_ != xddq.pb.SpChampToSkyPlayerInfo.getDefaultInstance()) {
          getRightPlayerInfoBuilder().mergeFrom(value);
        } else {
          rightPlayerInfo_ = value;
        }
      } else {
        rightPlayerInfoBuilder_.mergeFrom(value);
      }
      if (rightPlayerInfo_ != null) {
        bitField0_ |= 0x00000004;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.SpChampToSkyPlayerInfo rightPlayerInfo = 3;</code>
     */
    public Builder clearRightPlayerInfo() {
      bitField0_ = (bitField0_ & ~0x00000004);
      rightPlayerInfo_ = null;
      if (rightPlayerInfoBuilder_ != null) {
        rightPlayerInfoBuilder_.dispose();
        rightPlayerInfoBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.SpChampToSkyPlayerInfo rightPlayerInfo = 3;</code>
     */
    public xddq.pb.SpChampToSkyPlayerInfo.Builder getRightPlayerInfoBuilder() {
      bitField0_ |= 0x00000004;
      onChanged();
      return internalGetRightPlayerInfoFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.SpChampToSkyPlayerInfo rightPlayerInfo = 3;</code>
     */
    public xddq.pb.SpChampToSkyPlayerInfoOrBuilder getRightPlayerInfoOrBuilder() {
      if (rightPlayerInfoBuilder_ != null) {
        return rightPlayerInfoBuilder_.getMessageOrBuilder();
      } else {
        return rightPlayerInfo_ == null ?
            xddq.pb.SpChampToSkyPlayerInfo.getDefaultInstance() : rightPlayerInfo_;
      }
    }
    /**
     * <code>optional .xddq.pb.SpChampToSkyPlayerInfo rightPlayerInfo = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.SpChampToSkyPlayerInfo, xddq.pb.SpChampToSkyPlayerInfo.Builder, xddq.pb.SpChampToSkyPlayerInfoOrBuilder> 
        internalGetRightPlayerInfoFieldBuilder() {
      if (rightPlayerInfoBuilder_ == null) {
        rightPlayerInfoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.SpChampToSkyPlayerInfo, xddq.pb.SpChampToSkyPlayerInfo.Builder, xddq.pb.SpChampToSkyPlayerInfoOrBuilder>(
                getRightPlayerInfo(),
                getParentForChildren(),
                isClean());
        rightPlayerInfo_ = null;
      }
      return rightPlayerInfoBuilder_;
    }

    private long winnerId_ ;
    /**
     * <code>optional int64 winnerId = 4;</code>
     * @return Whether the winnerId field is set.
     */
    @java.lang.Override
    public boolean hasWinnerId() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int64 winnerId = 4;</code>
     * @return The winnerId.
     */
    @java.lang.Override
    public long getWinnerId() {
      return winnerId_;
    }
    /**
     * <code>optional int64 winnerId = 4;</code>
     * @param value The winnerId to set.
     * @return This builder for chaining.
     */
    public Builder setWinnerId(long value) {

      winnerId_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 winnerId = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearWinnerId() {
      bitField0_ = (bitField0_ & ~0x00000008);
      winnerId_ = 0L;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.SpChampMatchResult)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.SpChampMatchResult)
  private static final xddq.pb.SpChampMatchResult DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.SpChampMatchResult();
  }

  public static xddq.pb.SpChampMatchResult getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<SpChampMatchResult>
      PARSER = new com.google.protobuf.AbstractParser<SpChampMatchResult>() {
    @java.lang.Override
    public SpChampMatchResult parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<SpChampMatchResult> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<SpChampMatchResult> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.SpChampMatchResult getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

