// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

public interface HeavenBattleGetTreasureInfoRespOrBuilder extends
    // @@protoc_insertion_point(interface_extends:xddq.pb.HeavenBattleGetTreasureInfoResp)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>optional int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  boolean hasRet();
  /**
   * <code>optional int32 ret = 1;</code>
   * @return The ret.
   */
  int getRet();

  /**
   * <code>repeated .xddq.pb.HeavenBattleTreasureBoxRecord openedBoxList = 2;</code>
   */
  java.util.List<xddq.pb.HeavenBattleTreasureBoxRecord> 
      getOpenedBoxListList();
  /**
   * <code>repeated .xddq.pb.HeavenBattleTreasureBoxRecord openedBoxList = 2;</code>
   */
  xddq.pb.HeavenBattleTreasureBoxRecord getOpenedBoxList(int index);
  /**
   * <code>repeated .xddq.pb.HeavenBattleTreasureBoxRecord openedBoxList = 2;</code>
   */
  int getOpenedBoxListCount();
  /**
   * <code>repeated .xddq.pb.HeavenBattleTreasureBoxRecord openedBoxList = 2;</code>
   */
  java.util.List<? extends xddq.pb.HeavenBattleTreasureBoxRecordOrBuilder> 
      getOpenedBoxListOrBuilderList();
  /**
   * <code>repeated .xddq.pb.HeavenBattleTreasureBoxRecord openedBoxList = 2;</code>
   */
  xddq.pb.HeavenBattleTreasureBoxRecordOrBuilder getOpenedBoxListOrBuilder(
      int index);

  /**
   * <code>optional int32 receivedBoxId = 3;</code>
   * @return Whether the receivedBoxId field is set.
   */
  boolean hasReceivedBoxId();
  /**
   * <code>optional int32 receivedBoxId = 3;</code>
   * @return The receivedBoxId.
   */
  int getReceivedBoxId();
}
