// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.BodyTrialSelectBuffInfo}
 */
public final class BodyTrialSelectBuffInfo extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.BodyTrialSelectBuffInfo)
    BodyTrialSelectBuffInfoOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      BodyTrialSelectBuffInfo.class.getName());
  }
  // Use BodyTrialSelectBuffInfo.newBuilder() to construct.
  private BodyTrialSelectBuffInfo(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private BodyTrialSelectBuffInfo() {
    canSelectBuff_ = emptyIntList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_BodyTrialSelectBuffInfo_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_BodyTrialSelectBuffInfo_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.BodyTrialSelectBuffInfo.class, xddq.pb.BodyTrialSelectBuffInfo.Builder.class);
  }

  private int bitField0_;
  public static final int CANSELECTBUFF_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private com.google.protobuf.Internal.IntList canSelectBuff_ =
      emptyIntList();
  /**
   * <code>repeated int32 canSelectBuff = 1;</code>
   * @return A list containing the canSelectBuff.
   */
  @java.lang.Override
  public java.util.List<java.lang.Integer>
      getCanSelectBuffList() {
    return canSelectBuff_;
  }
  /**
   * <code>repeated int32 canSelectBuff = 1;</code>
   * @return The count of canSelectBuff.
   */
  public int getCanSelectBuffCount() {
    return canSelectBuff_.size();
  }
  /**
   * <code>repeated int32 canSelectBuff = 1;</code>
   * @param index The index of the element to return.
   * @return The canSelectBuff at the given index.
   */
  public int getCanSelectBuff(int index) {
    return canSelectBuff_.getInt(index);
  }

  public static final int BUFF_FIELD_NUMBER = 2;
  private int buff_ = 0;
  /**
   * <code>optional int32 buff = 2;</code>
   * @return Whether the buff field is set.
   */
  @java.lang.Override
  public boolean hasBuff() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int32 buff = 2;</code>
   * @return The buff.
   */
  @java.lang.Override
  public int getBuff() {
    return buff_;
  }

  public static final int STARTSELECTTIME_FIELD_NUMBER = 3;
  private long startSelectTime_ = 0L;
  /**
   * <code>optional int64 startSelectTime = 3;</code>
   * @return Whether the startSelectTime field is set.
   */
  @java.lang.Override
  public boolean hasStartSelectTime() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int64 startSelectTime = 3;</code>
   * @return The startSelectTime.
   */
  @java.lang.Override
  public long getStartSelectTime() {
    return startSelectTime_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    for (int i = 0; i < canSelectBuff_.size(); i++) {
      output.writeInt32(1, canSelectBuff_.getInt(i));
    }
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(2, buff_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt64(3, startSelectTime_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    {
      int dataSize = 0;
      for (int i = 0; i < canSelectBuff_.size(); i++) {
        dataSize += com.google.protobuf.CodedOutputStream
          .computeInt32SizeNoTag(canSelectBuff_.getInt(i));
      }
      size += dataSize;
      size += 1 * getCanSelectBuffList().size();
    }
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, buff_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(3, startSelectTime_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.BodyTrialSelectBuffInfo)) {
      return super.equals(obj);
    }
    xddq.pb.BodyTrialSelectBuffInfo other = (xddq.pb.BodyTrialSelectBuffInfo) obj;

    if (!getCanSelectBuffList()
        .equals(other.getCanSelectBuffList())) return false;
    if (hasBuff() != other.hasBuff()) return false;
    if (hasBuff()) {
      if (getBuff()
          != other.getBuff()) return false;
    }
    if (hasStartSelectTime() != other.hasStartSelectTime()) return false;
    if (hasStartSelectTime()) {
      if (getStartSelectTime()
          != other.getStartSelectTime()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (getCanSelectBuffCount() > 0) {
      hash = (37 * hash) + CANSELECTBUFF_FIELD_NUMBER;
      hash = (53 * hash) + getCanSelectBuffList().hashCode();
    }
    if (hasBuff()) {
      hash = (37 * hash) + BUFF_FIELD_NUMBER;
      hash = (53 * hash) + getBuff();
    }
    if (hasStartSelectTime()) {
      hash = (37 * hash) + STARTSELECTTIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getStartSelectTime());
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.BodyTrialSelectBuffInfo parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.BodyTrialSelectBuffInfo parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.BodyTrialSelectBuffInfo parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.BodyTrialSelectBuffInfo parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.BodyTrialSelectBuffInfo parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.BodyTrialSelectBuffInfo parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.BodyTrialSelectBuffInfo parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.BodyTrialSelectBuffInfo parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.BodyTrialSelectBuffInfo parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.BodyTrialSelectBuffInfo parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.BodyTrialSelectBuffInfo parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.BodyTrialSelectBuffInfo parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.BodyTrialSelectBuffInfo prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.BodyTrialSelectBuffInfo}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.BodyTrialSelectBuffInfo)
      xddq.pb.BodyTrialSelectBuffInfoOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_BodyTrialSelectBuffInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_BodyTrialSelectBuffInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.BodyTrialSelectBuffInfo.class, xddq.pb.BodyTrialSelectBuffInfo.Builder.class);
    }

    // Construct using xddq.pb.BodyTrialSelectBuffInfo.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      canSelectBuff_ = emptyIntList();
      buff_ = 0;
      startSelectTime_ = 0L;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_BodyTrialSelectBuffInfo_descriptor;
    }

    @java.lang.Override
    public xddq.pb.BodyTrialSelectBuffInfo getDefaultInstanceForType() {
      return xddq.pb.BodyTrialSelectBuffInfo.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.BodyTrialSelectBuffInfo build() {
      xddq.pb.BodyTrialSelectBuffInfo result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.BodyTrialSelectBuffInfo buildPartial() {
      xddq.pb.BodyTrialSelectBuffInfo result = new xddq.pb.BodyTrialSelectBuffInfo(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.BodyTrialSelectBuffInfo result) {
      int from_bitField0_ = bitField0_;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        canSelectBuff_.makeImmutable();
        result.canSelectBuff_ = canSelectBuff_;
      }
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.buff_ = buff_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.startSelectTime_ = startSelectTime_;
        to_bitField0_ |= 0x00000002;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.BodyTrialSelectBuffInfo) {
        return mergeFrom((xddq.pb.BodyTrialSelectBuffInfo)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.BodyTrialSelectBuffInfo other) {
      if (other == xddq.pb.BodyTrialSelectBuffInfo.getDefaultInstance()) return this;
      if (!other.canSelectBuff_.isEmpty()) {
        if (canSelectBuff_.isEmpty()) {
          canSelectBuff_ = other.canSelectBuff_;
          canSelectBuff_.makeImmutable();
          bitField0_ |= 0x00000001;
        } else {
          ensureCanSelectBuffIsMutable();
          canSelectBuff_.addAll(other.canSelectBuff_);
        }
        onChanged();
      }
      if (other.hasBuff()) {
        setBuff(other.getBuff());
      }
      if (other.hasStartSelectTime()) {
        setStartSelectTime(other.getStartSelectTime());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              int v = input.readInt32();
              ensureCanSelectBuffIsMutable();
              canSelectBuff_.addInt(v);
              break;
            } // case 8
            case 10: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              ensureCanSelectBuffIsMutable();
              while (input.getBytesUntilLimit() > 0) {
                canSelectBuff_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            } // case 10
            case 16: {
              buff_ = input.readInt32();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 24: {
              startSelectTime_ = input.readInt64();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private com.google.protobuf.Internal.IntList canSelectBuff_ = emptyIntList();
    private void ensureCanSelectBuffIsMutable() {
      if (!canSelectBuff_.isModifiable()) {
        canSelectBuff_ = makeMutableCopy(canSelectBuff_);
      }
      bitField0_ |= 0x00000001;
    }
    /**
     * <code>repeated int32 canSelectBuff = 1;</code>
     * @return A list containing the canSelectBuff.
     */
    public java.util.List<java.lang.Integer>
        getCanSelectBuffList() {
      canSelectBuff_.makeImmutable();
      return canSelectBuff_;
    }
    /**
     * <code>repeated int32 canSelectBuff = 1;</code>
     * @return The count of canSelectBuff.
     */
    public int getCanSelectBuffCount() {
      return canSelectBuff_.size();
    }
    /**
     * <code>repeated int32 canSelectBuff = 1;</code>
     * @param index The index of the element to return.
     * @return The canSelectBuff at the given index.
     */
    public int getCanSelectBuff(int index) {
      return canSelectBuff_.getInt(index);
    }
    /**
     * <code>repeated int32 canSelectBuff = 1;</code>
     * @param index The index to set the value at.
     * @param value The canSelectBuff to set.
     * @return This builder for chaining.
     */
    public Builder setCanSelectBuff(
        int index, int value) {

      ensureCanSelectBuffIsMutable();
      canSelectBuff_.setInt(index, value);
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 canSelectBuff = 1;</code>
     * @param value The canSelectBuff to add.
     * @return This builder for chaining.
     */
    public Builder addCanSelectBuff(int value) {

      ensureCanSelectBuffIsMutable();
      canSelectBuff_.addInt(value);
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 canSelectBuff = 1;</code>
     * @param values The canSelectBuff to add.
     * @return This builder for chaining.
     */
    public Builder addAllCanSelectBuff(
        java.lang.Iterable<? extends java.lang.Integer> values) {
      ensureCanSelectBuffIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, canSelectBuff_);
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 canSelectBuff = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearCanSelectBuff() {
      canSelectBuff_ = emptyIntList();
      bitField0_ = (bitField0_ & ~0x00000001);
      onChanged();
      return this;
    }

    private int buff_ ;
    /**
     * <code>optional int32 buff = 2;</code>
     * @return Whether the buff field is set.
     */
    @java.lang.Override
    public boolean hasBuff() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 buff = 2;</code>
     * @return The buff.
     */
    @java.lang.Override
    public int getBuff() {
      return buff_;
    }
    /**
     * <code>optional int32 buff = 2;</code>
     * @param value The buff to set.
     * @return This builder for chaining.
     */
    public Builder setBuff(int value) {

      buff_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 buff = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearBuff() {
      bitField0_ = (bitField0_ & ~0x00000002);
      buff_ = 0;
      onChanged();
      return this;
    }

    private long startSelectTime_ ;
    /**
     * <code>optional int64 startSelectTime = 3;</code>
     * @return Whether the startSelectTime field is set.
     */
    @java.lang.Override
    public boolean hasStartSelectTime() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int64 startSelectTime = 3;</code>
     * @return The startSelectTime.
     */
    @java.lang.Override
    public long getStartSelectTime() {
      return startSelectTime_;
    }
    /**
     * <code>optional int64 startSelectTime = 3;</code>
     * @param value The startSelectTime to set.
     * @return This builder for chaining.
     */
    public Builder setStartSelectTime(long value) {

      startSelectTime_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 startSelectTime = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearStartSelectTime() {
      bitField0_ = (bitField0_ & ~0x00000004);
      startSelectTime_ = 0L;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.BodyTrialSelectBuffInfo)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.BodyTrialSelectBuffInfo)
  private static final xddq.pb.BodyTrialSelectBuffInfo DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.BodyTrialSelectBuffInfo();
  }

  public static xddq.pb.BodyTrialSelectBuffInfo getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<BodyTrialSelectBuffInfo>
      PARSER = new com.google.protobuf.AbstractParser<BodyTrialSelectBuffInfo>() {
    @java.lang.Override
    public BodyTrialSelectBuffInfo parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<BodyTrialSelectBuffInfo> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<BodyTrialSelectBuffInfo> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.BodyTrialSelectBuffInfo getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

