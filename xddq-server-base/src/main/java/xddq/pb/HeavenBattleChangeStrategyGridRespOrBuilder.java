// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

public interface HeavenBattleChangeStrategyGridRespOrBuilder extends
    // @@protoc_insertion_point(interface_extends:xddq.pb.HeavenBattleChangeStrategyGridResp)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>optional int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  boolean hasRet();
  /**
   * <code>optional int32 ret = 1;</code>
   * @return The ret.
   */
  int getRet();

  /**
   * <code>repeated .xddq.pb.HeavenBattleCampTargerMsg list = 2;</code>
   */
  java.util.List<xddq.pb.HeavenBattleCampTargerMsg> 
      getListList();
  /**
   * <code>repeated .xddq.pb.HeavenBattleCampTargerMsg list = 2;</code>
   */
  xddq.pb.HeavenBattleCampTargerMsg getList(int index);
  /**
   * <code>repeated .xddq.pb.HeavenBattleCampTargerMsg list = 2;</code>
   */
  int getListCount();
  /**
   * <code>repeated .xddq.pb.HeavenBattleCampTargerMsg list = 2;</code>
   */
  java.util.List<? extends xddq.pb.HeavenBattleCampTargerMsgOrBuilder> 
      getListOrBuilderList();
  /**
   * <code>repeated .xddq.pb.HeavenBattleCampTargerMsg list = 2;</code>
   */
  xddq.pb.HeavenBattleCampTargerMsgOrBuilder getListOrBuilder(
      int index);
}
