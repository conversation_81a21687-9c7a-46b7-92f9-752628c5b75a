// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

public interface RebornTrialUnionScoreRespOrBuilder extends
    // @@protoc_insertion_point(interface_extends:xddq.pb.RebornTrialUnionScoreResp)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  boolean hasRet();
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  int getRet();

  /**
   * <code>repeated .xddq.pb.RebornTrialPlayerScoreInfo scoreInfo = 2;</code>
   */
  java.util.List<xddq.pb.RebornTrialPlayerScoreInfo> 
      getScoreInfoList();
  /**
   * <code>repeated .xddq.pb.RebornTrialPlayerScoreInfo scoreInfo = 2;</code>
   */
  xddq.pb.RebornTrialPlayerScoreInfo getScoreInfo(int index);
  /**
   * <code>repeated .xddq.pb.RebornTrialPlayerScoreInfo scoreInfo = 2;</code>
   */
  int getScoreInfoCount();
  /**
   * <code>repeated .xddq.pb.RebornTrialPlayerScoreInfo scoreInfo = 2;</code>
   */
  java.util.List<? extends xddq.pb.RebornTrialPlayerScoreInfoOrBuilder> 
      getScoreInfoOrBuilderList();
  /**
   * <code>repeated .xddq.pb.RebornTrialPlayerScoreInfo scoreInfo = 2;</code>
   */
  xddq.pb.RebornTrialPlayerScoreInfoOrBuilder getScoreInfoOrBuilder(
      int index);

  /**
   * <code>optional int64 reduceScore = 3;</code>
   * @return Whether the reduceScore field is set.
   */
  boolean hasReduceScore();
  /**
   * <code>optional int64 reduceScore = 3;</code>
   * @return The reduceScore.
   */
  long getReduceScore();
}
