// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.SkyTradeUnionChallengeMsg}
 */
public final class SkyTradeUnionChallengeMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.SkyTradeUnionChallengeMsg)
    SkyTradeUnionChallengeMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      SkyTradeUnionChallengeMsg.class.getName());
  }
  // Use SkyTradeUnionChallengeMsg.newBuilder() to construct.
  private SkyTradeUnionChallengeMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private SkyTradeUnionChallengeMsg() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_SkyTradeUnionChallengeMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_SkyTradeUnionChallengeMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.SkyTradeUnionChallengeMsg.class, xddq.pb.SkyTradeUnionChallengeMsg.Builder.class);
  }

  private int bitField0_;
  public static final int BASE_FIELD_NUMBER = 1;
  private xddq.pb.SkyTradeUnionBaseMsg base_;
  /**
   * <code>optional .xddq.pb.SkyTradeUnionBaseMsg base = 1;</code>
   * @return Whether the base field is set.
   */
  @java.lang.Override
  public boolean hasBase() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional .xddq.pb.SkyTradeUnionBaseMsg base = 1;</code>
   * @return The base.
   */
  @java.lang.Override
  public xddq.pb.SkyTradeUnionBaseMsg getBase() {
    return base_ == null ? xddq.pb.SkyTradeUnionBaseMsg.getDefaultInstance() : base_;
  }
  /**
   * <code>optional .xddq.pb.SkyTradeUnionBaseMsg base = 1;</code>
   */
  @java.lang.Override
  public xddq.pb.SkyTradeUnionBaseMsgOrBuilder getBaseOrBuilder() {
    return base_ == null ? xddq.pb.SkyTradeUnionBaseMsg.getDefaultInstance() : base_;
  }

  public static final int REDUCEBLOOD_FIELD_NUMBER = 2;
  private long reduceBlood_ = 0L;
  /**
   * <code>optional int64 reduceBlood = 2;</code>
   * @return Whether the reduceBlood field is set.
   */
  @java.lang.Override
  public boolean hasReduceBlood() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int64 reduceBlood = 2;</code>
   * @return The reduceBlood.
   */
  @java.lang.Override
  public long getReduceBlood() {
    return reduceBlood_;
  }

  public static final int LASTBEATTACKTIME_FIELD_NUMBER = 3;
  private long lastBeAttackTime_ = 0L;
  /**
   * <code>optional int64 lastBeAttackTime = 3;</code>
   * @return Whether the lastBeAttackTime field is set.
   */
  @java.lang.Override
  public boolean hasLastBeAttackTime() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional int64 lastBeAttackTime = 3;</code>
   * @return The lastBeAttackTime.
   */
  @java.lang.Override
  public long getLastBeAttackTime() {
    return lastBeAttackTime_;
  }

  public static final int RANK_FIELD_NUMBER = 4;
  private int rank_ = 0;
  /**
   * <code>optional int32 rank = 4;</code>
   * @return Whether the rank field is set.
   */
  @java.lang.Override
  public boolean hasRank() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional int32 rank = 4;</code>
   * @return The rank.
   */
  @java.lang.Override
  public int getRank() {
    return rank_;
  }

  public static final int HASDEFEATED_FIELD_NUMBER = 5;
  private boolean hasDefeated_ = false;
  /**
   * <code>optional bool hasDefeated = 5;</code>
   * @return Whether the hasDefeated field is set.
   */
  @java.lang.Override
  public boolean hasHasDefeated() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>optional bool hasDefeated = 5;</code>
   * @return The hasDefeated.
   */
  @java.lang.Override
  public boolean getHasDefeated() {
    return hasDefeated_;
  }

  public static final int SHIELDTRIGGERTIME_FIELD_NUMBER = 7;
  private long shieldTriggerTime_ = 0L;
  /**
   * <code>optional int64 shieldTriggerTime = 7;</code>
   * @return Whether the shieldTriggerTime field is set.
   */
  @java.lang.Override
  public boolean hasShieldTriggerTime() {
    return ((bitField0_ & 0x00000020) != 0);
  }
  /**
   * <code>optional int64 shieldTriggerTime = 7;</code>
   * @return The shieldTriggerTime.
   */
  @java.lang.Override
  public long getShieldTriggerTime() {
    return shieldTriggerTime_;
  }

  public static final int POSITION_FIELD_NUMBER = 8;
  private int position_ = 0;
  /**
   * <code>optional int32 position = 8;</code>
   * @return Whether the position field is set.
   */
  @java.lang.Override
  public boolean hasPosition() {
    return ((bitField0_ & 0x00000040) != 0);
  }
  /**
   * <code>optional int32 position = 8;</code>
   * @return The position.
   */
  @java.lang.Override
  public int getPosition() {
    return position_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeMessage(1, getBase());
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt64(2, reduceBlood_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt64(3, lastBeAttackTime_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt32(4, rank_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      output.writeBool(5, hasDefeated_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      output.writeInt64(7, shieldTriggerTime_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      output.writeInt32(8, position_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, getBase());
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(2, reduceBlood_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(3, lastBeAttackTime_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(4, rank_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(5, hasDefeated_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(7, shieldTriggerTime_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(8, position_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.SkyTradeUnionChallengeMsg)) {
      return super.equals(obj);
    }
    xddq.pb.SkyTradeUnionChallengeMsg other = (xddq.pb.SkyTradeUnionChallengeMsg) obj;

    if (hasBase() != other.hasBase()) return false;
    if (hasBase()) {
      if (!getBase()
          .equals(other.getBase())) return false;
    }
    if (hasReduceBlood() != other.hasReduceBlood()) return false;
    if (hasReduceBlood()) {
      if (getReduceBlood()
          != other.getReduceBlood()) return false;
    }
    if (hasLastBeAttackTime() != other.hasLastBeAttackTime()) return false;
    if (hasLastBeAttackTime()) {
      if (getLastBeAttackTime()
          != other.getLastBeAttackTime()) return false;
    }
    if (hasRank() != other.hasRank()) return false;
    if (hasRank()) {
      if (getRank()
          != other.getRank()) return false;
    }
    if (hasHasDefeated() != other.hasHasDefeated()) return false;
    if (hasHasDefeated()) {
      if (getHasDefeated()
          != other.getHasDefeated()) return false;
    }
    if (hasShieldTriggerTime() != other.hasShieldTriggerTime()) return false;
    if (hasShieldTriggerTime()) {
      if (getShieldTriggerTime()
          != other.getShieldTriggerTime()) return false;
    }
    if (hasPosition() != other.hasPosition()) return false;
    if (hasPosition()) {
      if (getPosition()
          != other.getPosition()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasBase()) {
      hash = (37 * hash) + BASE_FIELD_NUMBER;
      hash = (53 * hash) + getBase().hashCode();
    }
    if (hasReduceBlood()) {
      hash = (37 * hash) + REDUCEBLOOD_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getReduceBlood());
    }
    if (hasLastBeAttackTime()) {
      hash = (37 * hash) + LASTBEATTACKTIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getLastBeAttackTime());
    }
    if (hasRank()) {
      hash = (37 * hash) + RANK_FIELD_NUMBER;
      hash = (53 * hash) + getRank();
    }
    if (hasHasDefeated()) {
      hash = (37 * hash) + HASDEFEATED_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getHasDefeated());
    }
    if (hasShieldTriggerTime()) {
      hash = (37 * hash) + SHIELDTRIGGERTIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getShieldTriggerTime());
    }
    if (hasPosition()) {
      hash = (37 * hash) + POSITION_FIELD_NUMBER;
      hash = (53 * hash) + getPosition();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.SkyTradeUnionChallengeMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.SkyTradeUnionChallengeMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.SkyTradeUnionChallengeMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.SkyTradeUnionChallengeMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.SkyTradeUnionChallengeMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.SkyTradeUnionChallengeMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.SkyTradeUnionChallengeMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.SkyTradeUnionChallengeMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.SkyTradeUnionChallengeMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.SkyTradeUnionChallengeMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.SkyTradeUnionChallengeMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.SkyTradeUnionChallengeMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.SkyTradeUnionChallengeMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.SkyTradeUnionChallengeMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.SkyTradeUnionChallengeMsg)
      xddq.pb.SkyTradeUnionChallengeMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_SkyTradeUnionChallengeMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_SkyTradeUnionChallengeMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.SkyTradeUnionChallengeMsg.class, xddq.pb.SkyTradeUnionChallengeMsg.Builder.class);
    }

    // Construct using xddq.pb.SkyTradeUnionChallengeMsg.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetBaseFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      base_ = null;
      if (baseBuilder_ != null) {
        baseBuilder_.dispose();
        baseBuilder_ = null;
      }
      reduceBlood_ = 0L;
      lastBeAttackTime_ = 0L;
      rank_ = 0;
      hasDefeated_ = false;
      shieldTriggerTime_ = 0L;
      position_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_SkyTradeUnionChallengeMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.SkyTradeUnionChallengeMsg getDefaultInstanceForType() {
      return xddq.pb.SkyTradeUnionChallengeMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.SkyTradeUnionChallengeMsg build() {
      xddq.pb.SkyTradeUnionChallengeMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.SkyTradeUnionChallengeMsg buildPartial() {
      xddq.pb.SkyTradeUnionChallengeMsg result = new xddq.pb.SkyTradeUnionChallengeMsg(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.SkyTradeUnionChallengeMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.base_ = baseBuilder_ == null
            ? base_
            : baseBuilder_.build();
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.reduceBlood_ = reduceBlood_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.lastBeAttackTime_ = lastBeAttackTime_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.rank_ = rank_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.hasDefeated_ = hasDefeated_;
        to_bitField0_ |= 0x00000010;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.shieldTriggerTime_ = shieldTriggerTime_;
        to_bitField0_ |= 0x00000020;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.position_ = position_;
        to_bitField0_ |= 0x00000040;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.SkyTradeUnionChallengeMsg) {
        return mergeFrom((xddq.pb.SkyTradeUnionChallengeMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.SkyTradeUnionChallengeMsg other) {
      if (other == xddq.pb.SkyTradeUnionChallengeMsg.getDefaultInstance()) return this;
      if (other.hasBase()) {
        mergeBase(other.getBase());
      }
      if (other.hasReduceBlood()) {
        setReduceBlood(other.getReduceBlood());
      }
      if (other.hasLastBeAttackTime()) {
        setLastBeAttackTime(other.getLastBeAttackTime());
      }
      if (other.hasRank()) {
        setRank(other.getRank());
      }
      if (other.hasHasDefeated()) {
        setHasDefeated(other.getHasDefeated());
      }
      if (other.hasShieldTriggerTime()) {
        setShieldTriggerTime(other.getShieldTriggerTime());
      }
      if (other.hasPosition()) {
        setPosition(other.getPosition());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              input.readMessage(
                  internalGetBaseFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 16: {
              reduceBlood_ = input.readInt64();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 24: {
              lastBeAttackTime_ = input.readInt64();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 32: {
              rank_ = input.readInt32();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            case 40: {
              hasDefeated_ = input.readBool();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            case 56: {
              shieldTriggerTime_ = input.readInt64();
              bitField0_ |= 0x00000020;
              break;
            } // case 56
            case 64: {
              position_ = input.readInt32();
              bitField0_ |= 0x00000040;
              break;
            } // case 64
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private xddq.pb.SkyTradeUnionBaseMsg base_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.SkyTradeUnionBaseMsg, xddq.pb.SkyTradeUnionBaseMsg.Builder, xddq.pb.SkyTradeUnionBaseMsgOrBuilder> baseBuilder_;
    /**
     * <code>optional .xddq.pb.SkyTradeUnionBaseMsg base = 1;</code>
     * @return Whether the base field is set.
     */
    public boolean hasBase() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .xddq.pb.SkyTradeUnionBaseMsg base = 1;</code>
     * @return The base.
     */
    public xddq.pb.SkyTradeUnionBaseMsg getBase() {
      if (baseBuilder_ == null) {
        return base_ == null ? xddq.pb.SkyTradeUnionBaseMsg.getDefaultInstance() : base_;
      } else {
        return baseBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.SkyTradeUnionBaseMsg base = 1;</code>
     */
    public Builder setBase(xddq.pb.SkyTradeUnionBaseMsg value) {
      if (baseBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        base_ = value;
      } else {
        baseBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.SkyTradeUnionBaseMsg base = 1;</code>
     */
    public Builder setBase(
        xddq.pb.SkyTradeUnionBaseMsg.Builder builderForValue) {
      if (baseBuilder_ == null) {
        base_ = builderForValue.build();
      } else {
        baseBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.SkyTradeUnionBaseMsg base = 1;</code>
     */
    public Builder mergeBase(xddq.pb.SkyTradeUnionBaseMsg value) {
      if (baseBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0) &&
          base_ != null &&
          base_ != xddq.pb.SkyTradeUnionBaseMsg.getDefaultInstance()) {
          getBaseBuilder().mergeFrom(value);
        } else {
          base_ = value;
        }
      } else {
        baseBuilder_.mergeFrom(value);
      }
      if (base_ != null) {
        bitField0_ |= 0x00000001;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.SkyTradeUnionBaseMsg base = 1;</code>
     */
    public Builder clearBase() {
      bitField0_ = (bitField0_ & ~0x00000001);
      base_ = null;
      if (baseBuilder_ != null) {
        baseBuilder_.dispose();
        baseBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.SkyTradeUnionBaseMsg base = 1;</code>
     */
    public xddq.pb.SkyTradeUnionBaseMsg.Builder getBaseBuilder() {
      bitField0_ |= 0x00000001;
      onChanged();
      return internalGetBaseFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.SkyTradeUnionBaseMsg base = 1;</code>
     */
    public xddq.pb.SkyTradeUnionBaseMsgOrBuilder getBaseOrBuilder() {
      if (baseBuilder_ != null) {
        return baseBuilder_.getMessageOrBuilder();
      } else {
        return base_ == null ?
            xddq.pb.SkyTradeUnionBaseMsg.getDefaultInstance() : base_;
      }
    }
    /**
     * <code>optional .xddq.pb.SkyTradeUnionBaseMsg base = 1;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.SkyTradeUnionBaseMsg, xddq.pb.SkyTradeUnionBaseMsg.Builder, xddq.pb.SkyTradeUnionBaseMsgOrBuilder> 
        internalGetBaseFieldBuilder() {
      if (baseBuilder_ == null) {
        baseBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.SkyTradeUnionBaseMsg, xddq.pb.SkyTradeUnionBaseMsg.Builder, xddq.pb.SkyTradeUnionBaseMsgOrBuilder>(
                getBase(),
                getParentForChildren(),
                isClean());
        base_ = null;
      }
      return baseBuilder_;
    }

    private long reduceBlood_ ;
    /**
     * <code>optional int64 reduceBlood = 2;</code>
     * @return Whether the reduceBlood field is set.
     */
    @java.lang.Override
    public boolean hasReduceBlood() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int64 reduceBlood = 2;</code>
     * @return The reduceBlood.
     */
    @java.lang.Override
    public long getReduceBlood() {
      return reduceBlood_;
    }
    /**
     * <code>optional int64 reduceBlood = 2;</code>
     * @param value The reduceBlood to set.
     * @return This builder for chaining.
     */
    public Builder setReduceBlood(long value) {

      reduceBlood_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 reduceBlood = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearReduceBlood() {
      bitField0_ = (bitField0_ & ~0x00000002);
      reduceBlood_ = 0L;
      onChanged();
      return this;
    }

    private long lastBeAttackTime_ ;
    /**
     * <code>optional int64 lastBeAttackTime = 3;</code>
     * @return Whether the lastBeAttackTime field is set.
     */
    @java.lang.Override
    public boolean hasLastBeAttackTime() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int64 lastBeAttackTime = 3;</code>
     * @return The lastBeAttackTime.
     */
    @java.lang.Override
    public long getLastBeAttackTime() {
      return lastBeAttackTime_;
    }
    /**
     * <code>optional int64 lastBeAttackTime = 3;</code>
     * @param value The lastBeAttackTime to set.
     * @return This builder for chaining.
     */
    public Builder setLastBeAttackTime(long value) {

      lastBeAttackTime_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 lastBeAttackTime = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearLastBeAttackTime() {
      bitField0_ = (bitField0_ & ~0x00000004);
      lastBeAttackTime_ = 0L;
      onChanged();
      return this;
    }

    private int rank_ ;
    /**
     * <code>optional int32 rank = 4;</code>
     * @return Whether the rank field is set.
     */
    @java.lang.Override
    public boolean hasRank() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int32 rank = 4;</code>
     * @return The rank.
     */
    @java.lang.Override
    public int getRank() {
      return rank_;
    }
    /**
     * <code>optional int32 rank = 4;</code>
     * @param value The rank to set.
     * @return This builder for chaining.
     */
    public Builder setRank(int value) {

      rank_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 rank = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearRank() {
      bitField0_ = (bitField0_ & ~0x00000008);
      rank_ = 0;
      onChanged();
      return this;
    }

    private boolean hasDefeated_ ;
    /**
     * <code>optional bool hasDefeated = 5;</code>
     * @return Whether the hasDefeated field is set.
     */
    @java.lang.Override
    public boolean hasHasDefeated() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional bool hasDefeated = 5;</code>
     * @return The hasDefeated.
     */
    @java.lang.Override
    public boolean getHasDefeated() {
      return hasDefeated_;
    }
    /**
     * <code>optional bool hasDefeated = 5;</code>
     * @param value The hasDefeated to set.
     * @return This builder for chaining.
     */
    public Builder setHasDefeated(boolean value) {

      hasDefeated_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional bool hasDefeated = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearHasDefeated() {
      bitField0_ = (bitField0_ & ~0x00000010);
      hasDefeated_ = false;
      onChanged();
      return this;
    }

    private long shieldTriggerTime_ ;
    /**
     * <code>optional int64 shieldTriggerTime = 7;</code>
     * @return Whether the shieldTriggerTime field is set.
     */
    @java.lang.Override
    public boolean hasShieldTriggerTime() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional int64 shieldTriggerTime = 7;</code>
     * @return The shieldTriggerTime.
     */
    @java.lang.Override
    public long getShieldTriggerTime() {
      return shieldTriggerTime_;
    }
    /**
     * <code>optional int64 shieldTriggerTime = 7;</code>
     * @param value The shieldTriggerTime to set.
     * @return This builder for chaining.
     */
    public Builder setShieldTriggerTime(long value) {

      shieldTriggerTime_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 shieldTriggerTime = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearShieldTriggerTime() {
      bitField0_ = (bitField0_ & ~0x00000020);
      shieldTriggerTime_ = 0L;
      onChanged();
      return this;
    }

    private int position_ ;
    /**
     * <code>optional int32 position = 8;</code>
     * @return Whether the position field is set.
     */
    @java.lang.Override
    public boolean hasPosition() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <code>optional int32 position = 8;</code>
     * @return The position.
     */
    @java.lang.Override
    public int getPosition() {
      return position_;
    }
    /**
     * <code>optional int32 position = 8;</code>
     * @param value The position to set.
     * @return This builder for chaining.
     */
    public Builder setPosition(int value) {

      position_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 position = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearPosition() {
      bitField0_ = (bitField0_ & ~0x00000040);
      position_ = 0;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.SkyTradeUnionChallengeMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.SkyTradeUnionChallengeMsg)
  private static final xddq.pb.SkyTradeUnionChallengeMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.SkyTradeUnionChallengeMsg();
  }

  public static xddq.pb.SkyTradeUnionChallengeMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<SkyTradeUnionChallengeMsg>
      PARSER = new com.google.protobuf.AbstractParser<SkyTradeUnionChallengeMsg>() {
    @java.lang.Override
    public SkyTradeUnionChallengeMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<SkyTradeUnionChallengeMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<SkyTradeUnionChallengeMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.SkyTradeUnionChallengeMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

