// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.PlayerCultivationShowBaseData}
 */
public final class PlayerCultivationShowBaseData extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.PlayerCultivationShowBaseData)
    PlayerCultivationShowBaseDataOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      PlayerCultivationShowBaseData.class.getName());
  }
  // Use PlayerCultivationShowBaseData.newBuilder() to construct.
  private PlayerCultivationShowBaseData(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private PlayerCultivationShowBaseData() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_PlayerCultivationShowBaseData_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_PlayerCultivationShowBaseData_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.PlayerCultivationShowBaseData.class, xddq.pb.PlayerCultivationShowBaseData.Builder.class);
  }

  private int bitField0_;
  public static final int TYPE_FIELD_NUMBER = 1;
  private int type_ = 0;
  /**
   * <code>optional int32 type = 1;</code>
   * @return Whether the type field is set.
   */
  @java.lang.Override
  public boolean hasType() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int32 type = 1;</code>
   * @return The type.
   */
  @java.lang.Override
  public int getType() {
    return type_;
  }

  public static final int ID_FIELD_NUMBER = 2;
  private int id_ = 0;
  /**
   * <code>optional int32 id = 2;</code>
   * @return Whether the id field is set.
   */
  @java.lang.Override
  public boolean hasId() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int32 id = 2;</code>
   * @return The id.
   */
  @java.lang.Override
  public int getId() {
    return id_;
  }

  public static final int PLAYERBASEDATA_FIELD_NUMBER = 3;
  private xddq.pb.PlayerBaseDataMsg playerBaseData_;
  /**
   * <code>optional .xddq.pb.PlayerBaseDataMsg playerBaseData = 3;</code>
   * @return Whether the playerBaseData field is set.
   */
  @java.lang.Override
  public boolean hasPlayerBaseData() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional .xddq.pb.PlayerBaseDataMsg playerBaseData = 3;</code>
   * @return The playerBaseData.
   */
  @java.lang.Override
  public xddq.pb.PlayerBaseDataMsg getPlayerBaseData() {
    return playerBaseData_ == null ? xddq.pb.PlayerBaseDataMsg.getDefaultInstance() : playerBaseData_;
  }
  /**
   * <code>optional .xddq.pb.PlayerBaseDataMsg playerBaseData = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.PlayerBaseDataMsgOrBuilder getPlayerBaseDataOrBuilder() {
    return playerBaseData_ == null ? xddq.pb.PlayerBaseDataMsg.getDefaultInstance() : playerBaseData_;
  }

  public static final int CULTIVATIONSTARTTIME_FIELD_NUMBER = 4;
  private long cultivationStartTime_ = 0L;
  /**
   * <code>optional int64 cultivationStartTime = 4;</code>
   * @return Whether the cultivationStartTime field is set.
   */
  @java.lang.Override
  public boolean hasCultivationStartTime() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional int64 cultivationStartTime = 4;</code>
   * @return The cultivationStartTime.
   */
  @java.lang.Override
  public long getCultivationStartTime() {
    return cultivationStartTime_;
  }

  public static final int CULTIVATIONENDTIME_FIELD_NUMBER = 5;
  private long cultivationEndTime_ = 0L;
  /**
   * <code>optional int64 cultivationEndTime = 5;</code>
   * @return Whether the cultivationEndTime field is set.
   */
  @java.lang.Override
  public boolean hasCultivationEndTime() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>optional int64 cultivationEndTime = 5;</code>
   * @return The cultivationEndTime.
   */
  @java.lang.Override
  public long getCultivationEndTime() {
    return cultivationEndTime_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, type_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(2, id_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeMessage(3, getPlayerBaseData());
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt64(4, cultivationStartTime_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      output.writeInt64(5, cultivationEndTime_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, type_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, id_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, getPlayerBaseData());
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(4, cultivationStartTime_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(5, cultivationEndTime_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.PlayerCultivationShowBaseData)) {
      return super.equals(obj);
    }
    xddq.pb.PlayerCultivationShowBaseData other = (xddq.pb.PlayerCultivationShowBaseData) obj;

    if (hasType() != other.hasType()) return false;
    if (hasType()) {
      if (getType()
          != other.getType()) return false;
    }
    if (hasId() != other.hasId()) return false;
    if (hasId()) {
      if (getId()
          != other.getId()) return false;
    }
    if (hasPlayerBaseData() != other.hasPlayerBaseData()) return false;
    if (hasPlayerBaseData()) {
      if (!getPlayerBaseData()
          .equals(other.getPlayerBaseData())) return false;
    }
    if (hasCultivationStartTime() != other.hasCultivationStartTime()) return false;
    if (hasCultivationStartTime()) {
      if (getCultivationStartTime()
          != other.getCultivationStartTime()) return false;
    }
    if (hasCultivationEndTime() != other.hasCultivationEndTime()) return false;
    if (hasCultivationEndTime()) {
      if (getCultivationEndTime()
          != other.getCultivationEndTime()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasType()) {
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
    }
    if (hasId()) {
      hash = (37 * hash) + ID_FIELD_NUMBER;
      hash = (53 * hash) + getId();
    }
    if (hasPlayerBaseData()) {
      hash = (37 * hash) + PLAYERBASEDATA_FIELD_NUMBER;
      hash = (53 * hash) + getPlayerBaseData().hashCode();
    }
    if (hasCultivationStartTime()) {
      hash = (37 * hash) + CULTIVATIONSTARTTIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getCultivationStartTime());
    }
    if (hasCultivationEndTime()) {
      hash = (37 * hash) + CULTIVATIONENDTIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getCultivationEndTime());
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.PlayerCultivationShowBaseData parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PlayerCultivationShowBaseData parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PlayerCultivationShowBaseData parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PlayerCultivationShowBaseData parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PlayerCultivationShowBaseData parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PlayerCultivationShowBaseData parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PlayerCultivationShowBaseData parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.PlayerCultivationShowBaseData parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.PlayerCultivationShowBaseData parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.PlayerCultivationShowBaseData parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.PlayerCultivationShowBaseData parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.PlayerCultivationShowBaseData parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.PlayerCultivationShowBaseData prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.PlayerCultivationShowBaseData}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.PlayerCultivationShowBaseData)
      xddq.pb.PlayerCultivationShowBaseDataOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PlayerCultivationShowBaseData_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PlayerCultivationShowBaseData_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.PlayerCultivationShowBaseData.class, xddq.pb.PlayerCultivationShowBaseData.Builder.class);
    }

    // Construct using xddq.pb.PlayerCultivationShowBaseData.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetPlayerBaseDataFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      type_ = 0;
      id_ = 0;
      playerBaseData_ = null;
      if (playerBaseDataBuilder_ != null) {
        playerBaseDataBuilder_.dispose();
        playerBaseDataBuilder_ = null;
      }
      cultivationStartTime_ = 0L;
      cultivationEndTime_ = 0L;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PlayerCultivationShowBaseData_descriptor;
    }

    @java.lang.Override
    public xddq.pb.PlayerCultivationShowBaseData getDefaultInstanceForType() {
      return xddq.pb.PlayerCultivationShowBaseData.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.PlayerCultivationShowBaseData build() {
      xddq.pb.PlayerCultivationShowBaseData result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.PlayerCultivationShowBaseData buildPartial() {
      xddq.pb.PlayerCultivationShowBaseData result = new xddq.pb.PlayerCultivationShowBaseData(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.PlayerCultivationShowBaseData result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.type_ = type_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.id_ = id_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.playerBaseData_ = playerBaseDataBuilder_ == null
            ? playerBaseData_
            : playerBaseDataBuilder_.build();
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.cultivationStartTime_ = cultivationStartTime_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.cultivationEndTime_ = cultivationEndTime_;
        to_bitField0_ |= 0x00000010;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.PlayerCultivationShowBaseData) {
        return mergeFrom((xddq.pb.PlayerCultivationShowBaseData)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.PlayerCultivationShowBaseData other) {
      if (other == xddq.pb.PlayerCultivationShowBaseData.getDefaultInstance()) return this;
      if (other.hasType()) {
        setType(other.getType());
      }
      if (other.hasId()) {
        setId(other.getId());
      }
      if (other.hasPlayerBaseData()) {
        mergePlayerBaseData(other.getPlayerBaseData());
      }
      if (other.hasCultivationStartTime()) {
        setCultivationStartTime(other.getCultivationStartTime());
      }
      if (other.hasCultivationEndTime()) {
        setCultivationEndTime(other.getCultivationEndTime());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              type_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              id_ = input.readInt32();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 26: {
              input.readMessage(
                  internalGetPlayerBaseDataFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 32: {
              cultivationStartTime_ = input.readInt64();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            case 40: {
              cultivationEndTime_ = input.readInt64();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int type_ ;
    /**
     * <code>optional int32 type = 1;</code>
     * @return Whether the type field is set.
     */
    @java.lang.Override
    public boolean hasType() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 type = 1;</code>
     * @return The type.
     */
    @java.lang.Override
    public int getType() {
      return type_;
    }
    /**
     * <code>optional int32 type = 1;</code>
     * @param value The type to set.
     * @return This builder for chaining.
     */
    public Builder setType(int value) {

      type_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 type = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearType() {
      bitField0_ = (bitField0_ & ~0x00000001);
      type_ = 0;
      onChanged();
      return this;
    }

    private int id_ ;
    /**
     * <code>optional int32 id = 2;</code>
     * @return Whether the id field is set.
     */
    @java.lang.Override
    public boolean hasId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 id = 2;</code>
     * @return The id.
     */
    @java.lang.Override
    public int getId() {
      return id_;
    }
    /**
     * <code>optional int32 id = 2;</code>
     * @param value The id to set.
     * @return This builder for chaining.
     */
    public Builder setId(int value) {

      id_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 id = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearId() {
      bitField0_ = (bitField0_ & ~0x00000002);
      id_ = 0;
      onChanged();
      return this;
    }

    private xddq.pb.PlayerBaseDataMsg playerBaseData_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PlayerBaseDataMsg, xddq.pb.PlayerBaseDataMsg.Builder, xddq.pb.PlayerBaseDataMsgOrBuilder> playerBaseDataBuilder_;
    /**
     * <code>optional .xddq.pb.PlayerBaseDataMsg playerBaseData = 3;</code>
     * @return Whether the playerBaseData field is set.
     */
    public boolean hasPlayerBaseData() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional .xddq.pb.PlayerBaseDataMsg playerBaseData = 3;</code>
     * @return The playerBaseData.
     */
    public xddq.pb.PlayerBaseDataMsg getPlayerBaseData() {
      if (playerBaseDataBuilder_ == null) {
        return playerBaseData_ == null ? xddq.pb.PlayerBaseDataMsg.getDefaultInstance() : playerBaseData_;
      } else {
        return playerBaseDataBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.PlayerBaseDataMsg playerBaseData = 3;</code>
     */
    public Builder setPlayerBaseData(xddq.pb.PlayerBaseDataMsg value) {
      if (playerBaseDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        playerBaseData_ = value;
      } else {
        playerBaseDataBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerBaseDataMsg playerBaseData = 3;</code>
     */
    public Builder setPlayerBaseData(
        xddq.pb.PlayerBaseDataMsg.Builder builderForValue) {
      if (playerBaseDataBuilder_ == null) {
        playerBaseData_ = builderForValue.build();
      } else {
        playerBaseDataBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerBaseDataMsg playerBaseData = 3;</code>
     */
    public Builder mergePlayerBaseData(xddq.pb.PlayerBaseDataMsg value) {
      if (playerBaseDataBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0) &&
          playerBaseData_ != null &&
          playerBaseData_ != xddq.pb.PlayerBaseDataMsg.getDefaultInstance()) {
          getPlayerBaseDataBuilder().mergeFrom(value);
        } else {
          playerBaseData_ = value;
        }
      } else {
        playerBaseDataBuilder_.mergeFrom(value);
      }
      if (playerBaseData_ != null) {
        bitField0_ |= 0x00000004;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerBaseDataMsg playerBaseData = 3;</code>
     */
    public Builder clearPlayerBaseData() {
      bitField0_ = (bitField0_ & ~0x00000004);
      playerBaseData_ = null;
      if (playerBaseDataBuilder_ != null) {
        playerBaseDataBuilder_.dispose();
        playerBaseDataBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerBaseDataMsg playerBaseData = 3;</code>
     */
    public xddq.pb.PlayerBaseDataMsg.Builder getPlayerBaseDataBuilder() {
      bitField0_ |= 0x00000004;
      onChanged();
      return internalGetPlayerBaseDataFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.PlayerBaseDataMsg playerBaseData = 3;</code>
     */
    public xddq.pb.PlayerBaseDataMsgOrBuilder getPlayerBaseDataOrBuilder() {
      if (playerBaseDataBuilder_ != null) {
        return playerBaseDataBuilder_.getMessageOrBuilder();
      } else {
        return playerBaseData_ == null ?
            xddq.pb.PlayerBaseDataMsg.getDefaultInstance() : playerBaseData_;
      }
    }
    /**
     * <code>optional .xddq.pb.PlayerBaseDataMsg playerBaseData = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PlayerBaseDataMsg, xddq.pb.PlayerBaseDataMsg.Builder, xddq.pb.PlayerBaseDataMsgOrBuilder> 
        internalGetPlayerBaseDataFieldBuilder() {
      if (playerBaseDataBuilder_ == null) {
        playerBaseDataBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.PlayerBaseDataMsg, xddq.pb.PlayerBaseDataMsg.Builder, xddq.pb.PlayerBaseDataMsgOrBuilder>(
                getPlayerBaseData(),
                getParentForChildren(),
                isClean());
        playerBaseData_ = null;
      }
      return playerBaseDataBuilder_;
    }

    private long cultivationStartTime_ ;
    /**
     * <code>optional int64 cultivationStartTime = 4;</code>
     * @return Whether the cultivationStartTime field is set.
     */
    @java.lang.Override
    public boolean hasCultivationStartTime() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int64 cultivationStartTime = 4;</code>
     * @return The cultivationStartTime.
     */
    @java.lang.Override
    public long getCultivationStartTime() {
      return cultivationStartTime_;
    }
    /**
     * <code>optional int64 cultivationStartTime = 4;</code>
     * @param value The cultivationStartTime to set.
     * @return This builder for chaining.
     */
    public Builder setCultivationStartTime(long value) {

      cultivationStartTime_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 cultivationStartTime = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearCultivationStartTime() {
      bitField0_ = (bitField0_ & ~0x00000008);
      cultivationStartTime_ = 0L;
      onChanged();
      return this;
    }

    private long cultivationEndTime_ ;
    /**
     * <code>optional int64 cultivationEndTime = 5;</code>
     * @return Whether the cultivationEndTime field is set.
     */
    @java.lang.Override
    public boolean hasCultivationEndTime() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional int64 cultivationEndTime = 5;</code>
     * @return The cultivationEndTime.
     */
    @java.lang.Override
    public long getCultivationEndTime() {
      return cultivationEndTime_;
    }
    /**
     * <code>optional int64 cultivationEndTime = 5;</code>
     * @param value The cultivationEndTime to set.
     * @return This builder for chaining.
     */
    public Builder setCultivationEndTime(long value) {

      cultivationEndTime_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 cultivationEndTime = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearCultivationEndTime() {
      bitField0_ = (bitField0_ & ~0x00000010);
      cultivationEndTime_ = 0L;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.PlayerCultivationShowBaseData)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.PlayerCultivationShowBaseData)
  private static final xddq.pb.PlayerCultivationShowBaseData DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.PlayerCultivationShowBaseData();
  }

  public static xddq.pb.PlayerCultivationShowBaseData getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<PlayerCultivationShowBaseData>
      PARSER = new com.google.protobuf.AbstractParser<PlayerCultivationShowBaseData>() {
    @java.lang.Override
    public PlayerCultivationShowBaseData parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<PlayerCultivationShowBaseData> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<PlayerCultivationShowBaseData> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.PlayerCultivationShowBaseData getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

