// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.PalaceMiracleDataMsg}
 */
public final class PalaceMiracleDataMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.PalaceMiracleDataMsg)
    PalaceMiracleDataMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      PalaceMiracleDataMsg.class.getName());
  }
  // Use PalaceMiracleDataMsg.newBuilder() to construct.
  private PalaceMiracleDataMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private PalaceMiracleDataMsg() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_PalaceMiracleDataMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_PalaceMiracleDataMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.PalaceMiracleDataMsg.class, xddq.pb.PalaceMiracleDataMsg.Builder.class);
  }

  private int bitField0_;
  public static final int MIRACLEID_FIELD_NUMBER = 1;
  private int miracleId_ = 0;
  /**
   * <code>required int32 miracleId = 1;</code>
   * @return Whether the miracleId field is set.
   */
  @java.lang.Override
  public boolean hasMiracleId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 miracleId = 1;</code>
   * @return The miracleId.
   */
  @java.lang.Override
  public int getMiracleId() {
    return miracleId_;
  }

  public static final int USEDTIMES_FIELD_NUMBER = 2;
  private int usedTimes_ = 0;
  /**
   * <code>required int32 usedTimes = 2;</code>
   * @return Whether the usedTimes field is set.
   */
  @java.lang.Override
  public boolean hasUsedTimes() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>required int32 usedTimes = 2;</code>
   * @return The usedTimes.
   */
  @java.lang.Override
  public int getUsedTimes() {
    return usedTimes_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasMiracleId()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasUsedTimes()) {
      memoizedIsInitialized = 0;
      return false;
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, miracleId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(2, usedTimes_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, miracleId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, usedTimes_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.PalaceMiracleDataMsg)) {
      return super.equals(obj);
    }
    xddq.pb.PalaceMiracleDataMsg other = (xddq.pb.PalaceMiracleDataMsg) obj;

    if (hasMiracleId() != other.hasMiracleId()) return false;
    if (hasMiracleId()) {
      if (getMiracleId()
          != other.getMiracleId()) return false;
    }
    if (hasUsedTimes() != other.hasUsedTimes()) return false;
    if (hasUsedTimes()) {
      if (getUsedTimes()
          != other.getUsedTimes()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasMiracleId()) {
      hash = (37 * hash) + MIRACLEID_FIELD_NUMBER;
      hash = (53 * hash) + getMiracleId();
    }
    if (hasUsedTimes()) {
      hash = (37 * hash) + USEDTIMES_FIELD_NUMBER;
      hash = (53 * hash) + getUsedTimes();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.PalaceMiracleDataMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PalaceMiracleDataMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PalaceMiracleDataMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PalaceMiracleDataMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PalaceMiracleDataMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PalaceMiracleDataMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PalaceMiracleDataMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.PalaceMiracleDataMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.PalaceMiracleDataMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.PalaceMiracleDataMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.PalaceMiracleDataMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.PalaceMiracleDataMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.PalaceMiracleDataMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.PalaceMiracleDataMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.PalaceMiracleDataMsg)
      xddq.pb.PalaceMiracleDataMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PalaceMiracleDataMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PalaceMiracleDataMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.PalaceMiracleDataMsg.class, xddq.pb.PalaceMiracleDataMsg.Builder.class);
    }

    // Construct using xddq.pb.PalaceMiracleDataMsg.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      miracleId_ = 0;
      usedTimes_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PalaceMiracleDataMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.PalaceMiracleDataMsg getDefaultInstanceForType() {
      return xddq.pb.PalaceMiracleDataMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.PalaceMiracleDataMsg build() {
      xddq.pb.PalaceMiracleDataMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.PalaceMiracleDataMsg buildPartial() {
      xddq.pb.PalaceMiracleDataMsg result = new xddq.pb.PalaceMiracleDataMsg(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.PalaceMiracleDataMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.miracleId_ = miracleId_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.usedTimes_ = usedTimes_;
        to_bitField0_ |= 0x00000002;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.PalaceMiracleDataMsg) {
        return mergeFrom((xddq.pb.PalaceMiracleDataMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.PalaceMiracleDataMsg other) {
      if (other == xddq.pb.PalaceMiracleDataMsg.getDefaultInstance()) return this;
      if (other.hasMiracleId()) {
        setMiracleId(other.getMiracleId());
      }
      if (other.hasUsedTimes()) {
        setUsedTimes(other.getUsedTimes());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasMiracleId()) {
        return false;
      }
      if (!hasUsedTimes()) {
        return false;
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              miracleId_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              usedTimes_ = input.readInt32();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int miracleId_ ;
    /**
     * <code>required int32 miracleId = 1;</code>
     * @return Whether the miracleId field is set.
     */
    @java.lang.Override
    public boolean hasMiracleId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 miracleId = 1;</code>
     * @return The miracleId.
     */
    @java.lang.Override
    public int getMiracleId() {
      return miracleId_;
    }
    /**
     * <code>required int32 miracleId = 1;</code>
     * @param value The miracleId to set.
     * @return This builder for chaining.
     */
    public Builder setMiracleId(int value) {

      miracleId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 miracleId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearMiracleId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      miracleId_ = 0;
      onChanged();
      return this;
    }

    private int usedTimes_ ;
    /**
     * <code>required int32 usedTimes = 2;</code>
     * @return Whether the usedTimes field is set.
     */
    @java.lang.Override
    public boolean hasUsedTimes() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>required int32 usedTimes = 2;</code>
     * @return The usedTimes.
     */
    @java.lang.Override
    public int getUsedTimes() {
      return usedTimes_;
    }
    /**
     * <code>required int32 usedTimes = 2;</code>
     * @param value The usedTimes to set.
     * @return This builder for chaining.
     */
    public Builder setUsedTimes(int value) {

      usedTimes_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 usedTimes = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearUsedTimes() {
      bitField0_ = (bitField0_ & ~0x00000002);
      usedTimes_ = 0;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.PalaceMiracleDataMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.PalaceMiracleDataMsg)
  private static final xddq.pb.PalaceMiracleDataMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.PalaceMiracleDataMsg();
  }

  public static xddq.pb.PalaceMiracleDataMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<PalaceMiracleDataMsg>
      PARSER = new com.google.protobuf.AbstractParser<PalaceMiracleDataMsg>() {
    @java.lang.Override
    public PalaceMiracleDataMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<PalaceMiracleDataMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<PalaceMiracleDataMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.PalaceMiracleDataMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

