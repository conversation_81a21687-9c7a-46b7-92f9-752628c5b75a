// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.CastSwordRespBaseDataEntity}
 */
public final class CastSwordRespBaseDataEntity extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.CastSwordRespBaseDataEntity)
    CastSwordRespBaseDataEntityOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      CastSwordRespBaseDataEntity.class.getName());
  }
  // Use CastSwordRespBaseDataEntity.newBuilder() to construct.
  private CastSwordRespBaseDataEntity(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private CastSwordRespBaseDataEntity() {
    paint_ = java.util.Collections.emptyList();
    itemList_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_CastSwordRespBaseDataEntity_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_CastSwordRespBaseDataEntity_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.CastSwordRespBaseDataEntity.class, xddq.pb.CastSwordRespBaseDataEntity.Builder.class);
  }

  private int bitField0_;
  public static final int SCORE_FIELD_NUMBER = 1;
  private int score_ = 0;
  /**
   * <code>optional int32 score = 1;</code>
   * @return Whether the score field is set.
   */
  @java.lang.Override
  public boolean hasScore() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int32 score = 1;</code>
   * @return The score.
   */
  @java.lang.Override
  public int getScore() {
    return score_;
  }

  public static final int HIGHSCORE_FIELD_NUMBER = 2;
  private int highScore_ = 0;
  /**
   * <code>optional int32 highScore = 2;</code>
   * @return Whether the highScore field is set.
   */
  @java.lang.Override
  public boolean hasHighScore() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int32 highScore = 2;</code>
   * @return The highScore.
   */
  @java.lang.Override
  public int getHighScore() {
    return highScore_;
  }

  public static final int STRENGTH_FIELD_NUMBER = 3;
  private int strength_ = 0;
  /**
   * <code>optional int32 strength = 3;</code>
   * @return Whether the strength field is set.
   */
  @java.lang.Override
  public boolean hasStrength() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional int32 strength = 3;</code>
   * @return The strength.
   */
  @java.lang.Override
  public int getStrength() {
    return strength_;
  }

  public static final int PAINT_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.CastSwordPaintMsg> paint_;
  /**
   * <code>repeated .xddq.pb.CastSwordPaintMsg paint = 4;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.CastSwordPaintMsg> getPaintList() {
    return paint_;
  }
  /**
   * <code>repeated .xddq.pb.CastSwordPaintMsg paint = 4;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.CastSwordPaintMsgOrBuilder> 
      getPaintOrBuilderList() {
    return paint_;
  }
  /**
   * <code>repeated .xddq.pb.CastSwordPaintMsg paint = 4;</code>
   */
  @java.lang.Override
  public int getPaintCount() {
    return paint_.size();
  }
  /**
   * <code>repeated .xddq.pb.CastSwordPaintMsg paint = 4;</code>
   */
  @java.lang.Override
  public xddq.pb.CastSwordPaintMsg getPaint(int index) {
    return paint_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.CastSwordPaintMsg paint = 4;</code>
   */
  @java.lang.Override
  public xddq.pb.CastSwordPaintMsgOrBuilder getPaintOrBuilder(
      int index) {
    return paint_.get(index);
  }

  public static final int ITEMLIST_FIELD_NUMBER = 5;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.CastSwordItemUseMsg> itemList_;
  /**
   * <code>repeated .xddq.pb.CastSwordItemUseMsg itemList = 5;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.CastSwordItemUseMsg> getItemListList() {
    return itemList_;
  }
  /**
   * <code>repeated .xddq.pb.CastSwordItemUseMsg itemList = 5;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.CastSwordItemUseMsgOrBuilder> 
      getItemListOrBuilderList() {
    return itemList_;
  }
  /**
   * <code>repeated .xddq.pb.CastSwordItemUseMsg itemList = 5;</code>
   */
  @java.lang.Override
  public int getItemListCount() {
    return itemList_.size();
  }
  /**
   * <code>repeated .xddq.pb.CastSwordItemUseMsg itemList = 5;</code>
   */
  @java.lang.Override
  public xddq.pb.CastSwordItemUseMsg getItemList(int index) {
    return itemList_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.CastSwordItemUseMsg itemList = 5;</code>
   */
  @java.lang.Override
  public xddq.pb.CastSwordItemUseMsgOrBuilder getItemListOrBuilder(
      int index) {
    return itemList_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    for (int i = 0; i < getPaintCount(); i++) {
      if (!getPaint(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    for (int i = 0; i < getItemListCount(); i++) {
      if (!getItemList(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, score_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(2, highScore_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt32(3, strength_);
    }
    for (int i = 0; i < paint_.size(); i++) {
      output.writeMessage(4, paint_.get(i));
    }
    for (int i = 0; i < itemList_.size(); i++) {
      output.writeMessage(5, itemList_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, score_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, highScore_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(3, strength_);
    }
    for (int i = 0; i < paint_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(4, paint_.get(i));
    }
    for (int i = 0; i < itemList_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(5, itemList_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.CastSwordRespBaseDataEntity)) {
      return super.equals(obj);
    }
    xddq.pb.CastSwordRespBaseDataEntity other = (xddq.pb.CastSwordRespBaseDataEntity) obj;

    if (hasScore() != other.hasScore()) return false;
    if (hasScore()) {
      if (getScore()
          != other.getScore()) return false;
    }
    if (hasHighScore() != other.hasHighScore()) return false;
    if (hasHighScore()) {
      if (getHighScore()
          != other.getHighScore()) return false;
    }
    if (hasStrength() != other.hasStrength()) return false;
    if (hasStrength()) {
      if (getStrength()
          != other.getStrength()) return false;
    }
    if (!getPaintList()
        .equals(other.getPaintList())) return false;
    if (!getItemListList()
        .equals(other.getItemListList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasScore()) {
      hash = (37 * hash) + SCORE_FIELD_NUMBER;
      hash = (53 * hash) + getScore();
    }
    if (hasHighScore()) {
      hash = (37 * hash) + HIGHSCORE_FIELD_NUMBER;
      hash = (53 * hash) + getHighScore();
    }
    if (hasStrength()) {
      hash = (37 * hash) + STRENGTH_FIELD_NUMBER;
      hash = (53 * hash) + getStrength();
    }
    if (getPaintCount() > 0) {
      hash = (37 * hash) + PAINT_FIELD_NUMBER;
      hash = (53 * hash) + getPaintList().hashCode();
    }
    if (getItemListCount() > 0) {
      hash = (37 * hash) + ITEMLIST_FIELD_NUMBER;
      hash = (53 * hash) + getItemListList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.CastSwordRespBaseDataEntity parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.CastSwordRespBaseDataEntity parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.CastSwordRespBaseDataEntity parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.CastSwordRespBaseDataEntity parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.CastSwordRespBaseDataEntity parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.CastSwordRespBaseDataEntity parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.CastSwordRespBaseDataEntity parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.CastSwordRespBaseDataEntity parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.CastSwordRespBaseDataEntity parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.CastSwordRespBaseDataEntity parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.CastSwordRespBaseDataEntity parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.CastSwordRespBaseDataEntity parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.CastSwordRespBaseDataEntity prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.CastSwordRespBaseDataEntity}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.CastSwordRespBaseDataEntity)
      xddq.pb.CastSwordRespBaseDataEntityOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_CastSwordRespBaseDataEntity_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_CastSwordRespBaseDataEntity_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.CastSwordRespBaseDataEntity.class, xddq.pb.CastSwordRespBaseDataEntity.Builder.class);
    }

    // Construct using xddq.pb.CastSwordRespBaseDataEntity.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      score_ = 0;
      highScore_ = 0;
      strength_ = 0;
      if (paintBuilder_ == null) {
        paint_ = java.util.Collections.emptyList();
      } else {
        paint_ = null;
        paintBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000008);
      if (itemListBuilder_ == null) {
        itemList_ = java.util.Collections.emptyList();
      } else {
        itemList_ = null;
        itemListBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000010);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_CastSwordRespBaseDataEntity_descriptor;
    }

    @java.lang.Override
    public xddq.pb.CastSwordRespBaseDataEntity getDefaultInstanceForType() {
      return xddq.pb.CastSwordRespBaseDataEntity.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.CastSwordRespBaseDataEntity build() {
      xddq.pb.CastSwordRespBaseDataEntity result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.CastSwordRespBaseDataEntity buildPartial() {
      xddq.pb.CastSwordRespBaseDataEntity result = new xddq.pb.CastSwordRespBaseDataEntity(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.CastSwordRespBaseDataEntity result) {
      if (paintBuilder_ == null) {
        if (((bitField0_ & 0x00000008) != 0)) {
          paint_ = java.util.Collections.unmodifiableList(paint_);
          bitField0_ = (bitField0_ & ~0x00000008);
        }
        result.paint_ = paint_;
      } else {
        result.paint_ = paintBuilder_.build();
      }
      if (itemListBuilder_ == null) {
        if (((bitField0_ & 0x00000010) != 0)) {
          itemList_ = java.util.Collections.unmodifiableList(itemList_);
          bitField0_ = (bitField0_ & ~0x00000010);
        }
        result.itemList_ = itemList_;
      } else {
        result.itemList_ = itemListBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.CastSwordRespBaseDataEntity result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.score_ = score_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.highScore_ = highScore_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.strength_ = strength_;
        to_bitField0_ |= 0x00000004;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.CastSwordRespBaseDataEntity) {
        return mergeFrom((xddq.pb.CastSwordRespBaseDataEntity)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.CastSwordRespBaseDataEntity other) {
      if (other == xddq.pb.CastSwordRespBaseDataEntity.getDefaultInstance()) return this;
      if (other.hasScore()) {
        setScore(other.getScore());
      }
      if (other.hasHighScore()) {
        setHighScore(other.getHighScore());
      }
      if (other.hasStrength()) {
        setStrength(other.getStrength());
      }
      if (paintBuilder_ == null) {
        if (!other.paint_.isEmpty()) {
          if (paint_.isEmpty()) {
            paint_ = other.paint_;
            bitField0_ = (bitField0_ & ~0x00000008);
          } else {
            ensurePaintIsMutable();
            paint_.addAll(other.paint_);
          }
          onChanged();
        }
      } else {
        if (!other.paint_.isEmpty()) {
          if (paintBuilder_.isEmpty()) {
            paintBuilder_.dispose();
            paintBuilder_ = null;
            paint_ = other.paint_;
            bitField0_ = (bitField0_ & ~0x00000008);
            paintBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetPaintFieldBuilder() : null;
          } else {
            paintBuilder_.addAllMessages(other.paint_);
          }
        }
      }
      if (itemListBuilder_ == null) {
        if (!other.itemList_.isEmpty()) {
          if (itemList_.isEmpty()) {
            itemList_ = other.itemList_;
            bitField0_ = (bitField0_ & ~0x00000010);
          } else {
            ensureItemListIsMutable();
            itemList_.addAll(other.itemList_);
          }
          onChanged();
        }
      } else {
        if (!other.itemList_.isEmpty()) {
          if (itemListBuilder_.isEmpty()) {
            itemListBuilder_.dispose();
            itemListBuilder_ = null;
            itemList_ = other.itemList_;
            bitField0_ = (bitField0_ & ~0x00000010);
            itemListBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetItemListFieldBuilder() : null;
          } else {
            itemListBuilder_.addAllMessages(other.itemList_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      for (int i = 0; i < getPaintCount(); i++) {
        if (!getPaint(i).isInitialized()) {
          return false;
        }
      }
      for (int i = 0; i < getItemListCount(); i++) {
        if (!getItemList(i).isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              score_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              highScore_ = input.readInt32();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 24: {
              strength_ = input.readInt32();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 34: {
              xddq.pb.CastSwordPaintMsg m =
                  input.readMessage(
                      xddq.pb.CastSwordPaintMsg.parser(),
                      extensionRegistry);
              if (paintBuilder_ == null) {
                ensurePaintIsMutable();
                paint_.add(m);
              } else {
                paintBuilder_.addMessage(m);
              }
              break;
            } // case 34
            case 42: {
              xddq.pb.CastSwordItemUseMsg m =
                  input.readMessage(
                      xddq.pb.CastSwordItemUseMsg.parser(),
                      extensionRegistry);
              if (itemListBuilder_ == null) {
                ensureItemListIsMutable();
                itemList_.add(m);
              } else {
                itemListBuilder_.addMessage(m);
              }
              break;
            } // case 42
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int score_ ;
    /**
     * <code>optional int32 score = 1;</code>
     * @return Whether the score field is set.
     */
    @java.lang.Override
    public boolean hasScore() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 score = 1;</code>
     * @return The score.
     */
    @java.lang.Override
    public int getScore() {
      return score_;
    }
    /**
     * <code>optional int32 score = 1;</code>
     * @param value The score to set.
     * @return This builder for chaining.
     */
    public Builder setScore(int value) {

      score_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 score = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearScore() {
      bitField0_ = (bitField0_ & ~0x00000001);
      score_ = 0;
      onChanged();
      return this;
    }

    private int highScore_ ;
    /**
     * <code>optional int32 highScore = 2;</code>
     * @return Whether the highScore field is set.
     */
    @java.lang.Override
    public boolean hasHighScore() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 highScore = 2;</code>
     * @return The highScore.
     */
    @java.lang.Override
    public int getHighScore() {
      return highScore_;
    }
    /**
     * <code>optional int32 highScore = 2;</code>
     * @param value The highScore to set.
     * @return This builder for chaining.
     */
    public Builder setHighScore(int value) {

      highScore_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 highScore = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearHighScore() {
      bitField0_ = (bitField0_ & ~0x00000002);
      highScore_ = 0;
      onChanged();
      return this;
    }

    private int strength_ ;
    /**
     * <code>optional int32 strength = 3;</code>
     * @return Whether the strength field is set.
     */
    @java.lang.Override
    public boolean hasStrength() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int32 strength = 3;</code>
     * @return The strength.
     */
    @java.lang.Override
    public int getStrength() {
      return strength_;
    }
    /**
     * <code>optional int32 strength = 3;</code>
     * @param value The strength to set.
     * @return This builder for chaining.
     */
    public Builder setStrength(int value) {

      strength_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 strength = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearStrength() {
      bitField0_ = (bitField0_ & ~0x00000004);
      strength_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.CastSwordPaintMsg> paint_ =
      java.util.Collections.emptyList();
    private void ensurePaintIsMutable() {
      if (!((bitField0_ & 0x00000008) != 0)) {
        paint_ = new java.util.ArrayList<xddq.pb.CastSwordPaintMsg>(paint_);
        bitField0_ |= 0x00000008;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.CastSwordPaintMsg, xddq.pb.CastSwordPaintMsg.Builder, xddq.pb.CastSwordPaintMsgOrBuilder> paintBuilder_;

    /**
     * <code>repeated .xddq.pb.CastSwordPaintMsg paint = 4;</code>
     */
    public java.util.List<xddq.pb.CastSwordPaintMsg> getPaintList() {
      if (paintBuilder_ == null) {
        return java.util.Collections.unmodifiableList(paint_);
      } else {
        return paintBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.CastSwordPaintMsg paint = 4;</code>
     */
    public int getPaintCount() {
      if (paintBuilder_ == null) {
        return paint_.size();
      } else {
        return paintBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.CastSwordPaintMsg paint = 4;</code>
     */
    public xddq.pb.CastSwordPaintMsg getPaint(int index) {
      if (paintBuilder_ == null) {
        return paint_.get(index);
      } else {
        return paintBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.CastSwordPaintMsg paint = 4;</code>
     */
    public Builder setPaint(
        int index, xddq.pb.CastSwordPaintMsg value) {
      if (paintBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePaintIsMutable();
        paint_.set(index, value);
        onChanged();
      } else {
        paintBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.CastSwordPaintMsg paint = 4;</code>
     */
    public Builder setPaint(
        int index, xddq.pb.CastSwordPaintMsg.Builder builderForValue) {
      if (paintBuilder_ == null) {
        ensurePaintIsMutable();
        paint_.set(index, builderForValue.build());
        onChanged();
      } else {
        paintBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.CastSwordPaintMsg paint = 4;</code>
     */
    public Builder addPaint(xddq.pb.CastSwordPaintMsg value) {
      if (paintBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePaintIsMutable();
        paint_.add(value);
        onChanged();
      } else {
        paintBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.CastSwordPaintMsg paint = 4;</code>
     */
    public Builder addPaint(
        int index, xddq.pb.CastSwordPaintMsg value) {
      if (paintBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePaintIsMutable();
        paint_.add(index, value);
        onChanged();
      } else {
        paintBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.CastSwordPaintMsg paint = 4;</code>
     */
    public Builder addPaint(
        xddq.pb.CastSwordPaintMsg.Builder builderForValue) {
      if (paintBuilder_ == null) {
        ensurePaintIsMutable();
        paint_.add(builderForValue.build());
        onChanged();
      } else {
        paintBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.CastSwordPaintMsg paint = 4;</code>
     */
    public Builder addPaint(
        int index, xddq.pb.CastSwordPaintMsg.Builder builderForValue) {
      if (paintBuilder_ == null) {
        ensurePaintIsMutable();
        paint_.add(index, builderForValue.build());
        onChanged();
      } else {
        paintBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.CastSwordPaintMsg paint = 4;</code>
     */
    public Builder addAllPaint(
        java.lang.Iterable<? extends xddq.pb.CastSwordPaintMsg> values) {
      if (paintBuilder_ == null) {
        ensurePaintIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, paint_);
        onChanged();
      } else {
        paintBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.CastSwordPaintMsg paint = 4;</code>
     */
    public Builder clearPaint() {
      if (paintBuilder_ == null) {
        paint_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000008);
        onChanged();
      } else {
        paintBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.CastSwordPaintMsg paint = 4;</code>
     */
    public Builder removePaint(int index) {
      if (paintBuilder_ == null) {
        ensurePaintIsMutable();
        paint_.remove(index);
        onChanged();
      } else {
        paintBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.CastSwordPaintMsg paint = 4;</code>
     */
    public xddq.pb.CastSwordPaintMsg.Builder getPaintBuilder(
        int index) {
      return internalGetPaintFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.CastSwordPaintMsg paint = 4;</code>
     */
    public xddq.pb.CastSwordPaintMsgOrBuilder getPaintOrBuilder(
        int index) {
      if (paintBuilder_ == null) {
        return paint_.get(index);  } else {
        return paintBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.CastSwordPaintMsg paint = 4;</code>
     */
    public java.util.List<? extends xddq.pb.CastSwordPaintMsgOrBuilder> 
         getPaintOrBuilderList() {
      if (paintBuilder_ != null) {
        return paintBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(paint_);
      }
    }
    /**
     * <code>repeated .xddq.pb.CastSwordPaintMsg paint = 4;</code>
     */
    public xddq.pb.CastSwordPaintMsg.Builder addPaintBuilder() {
      return internalGetPaintFieldBuilder().addBuilder(
          xddq.pb.CastSwordPaintMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.CastSwordPaintMsg paint = 4;</code>
     */
    public xddq.pb.CastSwordPaintMsg.Builder addPaintBuilder(
        int index) {
      return internalGetPaintFieldBuilder().addBuilder(
          index, xddq.pb.CastSwordPaintMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.CastSwordPaintMsg paint = 4;</code>
     */
    public java.util.List<xddq.pb.CastSwordPaintMsg.Builder> 
         getPaintBuilderList() {
      return internalGetPaintFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.CastSwordPaintMsg, xddq.pb.CastSwordPaintMsg.Builder, xddq.pb.CastSwordPaintMsgOrBuilder> 
        internalGetPaintFieldBuilder() {
      if (paintBuilder_ == null) {
        paintBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.CastSwordPaintMsg, xddq.pb.CastSwordPaintMsg.Builder, xddq.pb.CastSwordPaintMsgOrBuilder>(
                paint_,
                ((bitField0_ & 0x00000008) != 0),
                getParentForChildren(),
                isClean());
        paint_ = null;
      }
      return paintBuilder_;
    }

    private java.util.List<xddq.pb.CastSwordItemUseMsg> itemList_ =
      java.util.Collections.emptyList();
    private void ensureItemListIsMutable() {
      if (!((bitField0_ & 0x00000010) != 0)) {
        itemList_ = new java.util.ArrayList<xddq.pb.CastSwordItemUseMsg>(itemList_);
        bitField0_ |= 0x00000010;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.CastSwordItemUseMsg, xddq.pb.CastSwordItemUseMsg.Builder, xddq.pb.CastSwordItemUseMsgOrBuilder> itemListBuilder_;

    /**
     * <code>repeated .xddq.pb.CastSwordItemUseMsg itemList = 5;</code>
     */
    public java.util.List<xddq.pb.CastSwordItemUseMsg> getItemListList() {
      if (itemListBuilder_ == null) {
        return java.util.Collections.unmodifiableList(itemList_);
      } else {
        return itemListBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.CastSwordItemUseMsg itemList = 5;</code>
     */
    public int getItemListCount() {
      if (itemListBuilder_ == null) {
        return itemList_.size();
      } else {
        return itemListBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.CastSwordItemUseMsg itemList = 5;</code>
     */
    public xddq.pb.CastSwordItemUseMsg getItemList(int index) {
      if (itemListBuilder_ == null) {
        return itemList_.get(index);
      } else {
        return itemListBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.CastSwordItemUseMsg itemList = 5;</code>
     */
    public Builder setItemList(
        int index, xddq.pb.CastSwordItemUseMsg value) {
      if (itemListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureItemListIsMutable();
        itemList_.set(index, value);
        onChanged();
      } else {
        itemListBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.CastSwordItemUseMsg itemList = 5;</code>
     */
    public Builder setItemList(
        int index, xddq.pb.CastSwordItemUseMsg.Builder builderForValue) {
      if (itemListBuilder_ == null) {
        ensureItemListIsMutable();
        itemList_.set(index, builderForValue.build());
        onChanged();
      } else {
        itemListBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.CastSwordItemUseMsg itemList = 5;</code>
     */
    public Builder addItemList(xddq.pb.CastSwordItemUseMsg value) {
      if (itemListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureItemListIsMutable();
        itemList_.add(value);
        onChanged();
      } else {
        itemListBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.CastSwordItemUseMsg itemList = 5;</code>
     */
    public Builder addItemList(
        int index, xddq.pb.CastSwordItemUseMsg value) {
      if (itemListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureItemListIsMutable();
        itemList_.add(index, value);
        onChanged();
      } else {
        itemListBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.CastSwordItemUseMsg itemList = 5;</code>
     */
    public Builder addItemList(
        xddq.pb.CastSwordItemUseMsg.Builder builderForValue) {
      if (itemListBuilder_ == null) {
        ensureItemListIsMutable();
        itemList_.add(builderForValue.build());
        onChanged();
      } else {
        itemListBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.CastSwordItemUseMsg itemList = 5;</code>
     */
    public Builder addItemList(
        int index, xddq.pb.CastSwordItemUseMsg.Builder builderForValue) {
      if (itemListBuilder_ == null) {
        ensureItemListIsMutable();
        itemList_.add(index, builderForValue.build());
        onChanged();
      } else {
        itemListBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.CastSwordItemUseMsg itemList = 5;</code>
     */
    public Builder addAllItemList(
        java.lang.Iterable<? extends xddq.pb.CastSwordItemUseMsg> values) {
      if (itemListBuilder_ == null) {
        ensureItemListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, itemList_);
        onChanged();
      } else {
        itemListBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.CastSwordItemUseMsg itemList = 5;</code>
     */
    public Builder clearItemList() {
      if (itemListBuilder_ == null) {
        itemList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000010);
        onChanged();
      } else {
        itemListBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.CastSwordItemUseMsg itemList = 5;</code>
     */
    public Builder removeItemList(int index) {
      if (itemListBuilder_ == null) {
        ensureItemListIsMutable();
        itemList_.remove(index);
        onChanged();
      } else {
        itemListBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.CastSwordItemUseMsg itemList = 5;</code>
     */
    public xddq.pb.CastSwordItemUseMsg.Builder getItemListBuilder(
        int index) {
      return internalGetItemListFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.CastSwordItemUseMsg itemList = 5;</code>
     */
    public xddq.pb.CastSwordItemUseMsgOrBuilder getItemListOrBuilder(
        int index) {
      if (itemListBuilder_ == null) {
        return itemList_.get(index);  } else {
        return itemListBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.CastSwordItemUseMsg itemList = 5;</code>
     */
    public java.util.List<? extends xddq.pb.CastSwordItemUseMsgOrBuilder> 
         getItemListOrBuilderList() {
      if (itemListBuilder_ != null) {
        return itemListBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(itemList_);
      }
    }
    /**
     * <code>repeated .xddq.pb.CastSwordItemUseMsg itemList = 5;</code>
     */
    public xddq.pb.CastSwordItemUseMsg.Builder addItemListBuilder() {
      return internalGetItemListFieldBuilder().addBuilder(
          xddq.pb.CastSwordItemUseMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.CastSwordItemUseMsg itemList = 5;</code>
     */
    public xddq.pb.CastSwordItemUseMsg.Builder addItemListBuilder(
        int index) {
      return internalGetItemListFieldBuilder().addBuilder(
          index, xddq.pb.CastSwordItemUseMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.CastSwordItemUseMsg itemList = 5;</code>
     */
    public java.util.List<xddq.pb.CastSwordItemUseMsg.Builder> 
         getItemListBuilderList() {
      return internalGetItemListFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.CastSwordItemUseMsg, xddq.pb.CastSwordItemUseMsg.Builder, xddq.pb.CastSwordItemUseMsgOrBuilder> 
        internalGetItemListFieldBuilder() {
      if (itemListBuilder_ == null) {
        itemListBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.CastSwordItemUseMsg, xddq.pb.CastSwordItemUseMsg.Builder, xddq.pb.CastSwordItemUseMsgOrBuilder>(
                itemList_,
                ((bitField0_ & 0x00000010) != 0),
                getParentForChildren(),
                isClean());
        itemList_ = null;
      }
      return itemListBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.CastSwordRespBaseDataEntity)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.CastSwordRespBaseDataEntity)
  private static final xddq.pb.CastSwordRespBaseDataEntity DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.CastSwordRespBaseDataEntity();
  }

  public static xddq.pb.CastSwordRespBaseDataEntity getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<CastSwordRespBaseDataEntity>
      PARSER = new com.google.protobuf.AbstractParser<CastSwordRespBaseDataEntity>() {
    @java.lang.Override
    public CastSwordRespBaseDataEntity parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<CastSwordRespBaseDataEntity> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<CastSwordRespBaseDataEntity> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.CastSwordRespBaseDataEntity getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

