// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.DemonTowerOneKeyDrawData}
 */
public final class DemonTowerOneKeyDrawData extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.DemonTowerOneKeyDrawData)
    DemonTowerOneKeyDrawDataOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      DemonTowerOneKeyDrawData.class.getName());
  }
  // Use DemonTowerOneKeyDrawData.newBuilder() to construct.
  private DemonTowerOneKeyDrawData(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private DemonTowerOneKeyDrawData() {
    drawIndexArr_ = emptyIntList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_DemonTowerOneKeyDrawData_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_DemonTowerOneKeyDrawData_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.DemonTowerOneKeyDrawData.class, xddq.pb.DemonTowerOneKeyDrawData.Builder.class);
  }

  private int bitField0_;
  public static final int FLOOR_FIELD_NUMBER = 1;
  private int floor_ = 0;
  /**
   * <code>required int32 floor = 1;</code>
   * @return Whether the floor field is set.
   */
  @java.lang.Override
  public boolean hasFloor() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 floor = 1;</code>
   * @return The floor.
   */
  @java.lang.Override
  public int getFloor() {
    return floor_;
  }

  public static final int DRAWINDEXARR_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private com.google.protobuf.Internal.IntList drawIndexArr_ =
      emptyIntList();
  /**
   * <code>repeated int32 drawIndexArr = 2;</code>
   * @return A list containing the drawIndexArr.
   */
  @java.lang.Override
  public java.util.List<java.lang.Integer>
      getDrawIndexArrList() {
    return drawIndexArr_;
  }
  /**
   * <code>repeated int32 drawIndexArr = 2;</code>
   * @return The count of drawIndexArr.
   */
  public int getDrawIndexArrCount() {
    return drawIndexArr_.size();
  }
  /**
   * <code>repeated int32 drawIndexArr = 2;</code>
   * @param index The index of the element to return.
   * @return The drawIndexArr at the given index.
   */
  public int getDrawIndexArr(int index) {
    return drawIndexArr_.getInt(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasFloor()) {
      memoizedIsInitialized = 0;
      return false;
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, floor_);
    }
    for (int i = 0; i < drawIndexArr_.size(); i++) {
      output.writeInt32(2, drawIndexArr_.getInt(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, floor_);
    }
    {
      int dataSize = 0;
      for (int i = 0; i < drawIndexArr_.size(); i++) {
        dataSize += com.google.protobuf.CodedOutputStream
          .computeInt32SizeNoTag(drawIndexArr_.getInt(i));
      }
      size += dataSize;
      size += 1 * getDrawIndexArrList().size();
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.DemonTowerOneKeyDrawData)) {
      return super.equals(obj);
    }
    xddq.pb.DemonTowerOneKeyDrawData other = (xddq.pb.DemonTowerOneKeyDrawData) obj;

    if (hasFloor() != other.hasFloor()) return false;
    if (hasFloor()) {
      if (getFloor()
          != other.getFloor()) return false;
    }
    if (!getDrawIndexArrList()
        .equals(other.getDrawIndexArrList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasFloor()) {
      hash = (37 * hash) + FLOOR_FIELD_NUMBER;
      hash = (53 * hash) + getFloor();
    }
    if (getDrawIndexArrCount() > 0) {
      hash = (37 * hash) + DRAWINDEXARR_FIELD_NUMBER;
      hash = (53 * hash) + getDrawIndexArrList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.DemonTowerOneKeyDrawData parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.DemonTowerOneKeyDrawData parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.DemonTowerOneKeyDrawData parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.DemonTowerOneKeyDrawData parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.DemonTowerOneKeyDrawData parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.DemonTowerOneKeyDrawData parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.DemonTowerOneKeyDrawData parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.DemonTowerOneKeyDrawData parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.DemonTowerOneKeyDrawData parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.DemonTowerOneKeyDrawData parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.DemonTowerOneKeyDrawData parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.DemonTowerOneKeyDrawData parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.DemonTowerOneKeyDrawData prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.DemonTowerOneKeyDrawData}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.DemonTowerOneKeyDrawData)
      xddq.pb.DemonTowerOneKeyDrawDataOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_DemonTowerOneKeyDrawData_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_DemonTowerOneKeyDrawData_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.DemonTowerOneKeyDrawData.class, xddq.pb.DemonTowerOneKeyDrawData.Builder.class);
    }

    // Construct using xddq.pb.DemonTowerOneKeyDrawData.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      floor_ = 0;
      drawIndexArr_ = emptyIntList();
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_DemonTowerOneKeyDrawData_descriptor;
    }

    @java.lang.Override
    public xddq.pb.DemonTowerOneKeyDrawData getDefaultInstanceForType() {
      return xddq.pb.DemonTowerOneKeyDrawData.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.DemonTowerOneKeyDrawData build() {
      xddq.pb.DemonTowerOneKeyDrawData result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.DemonTowerOneKeyDrawData buildPartial() {
      xddq.pb.DemonTowerOneKeyDrawData result = new xddq.pb.DemonTowerOneKeyDrawData(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.DemonTowerOneKeyDrawData result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.floor_ = floor_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        drawIndexArr_.makeImmutable();
        result.drawIndexArr_ = drawIndexArr_;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.DemonTowerOneKeyDrawData) {
        return mergeFrom((xddq.pb.DemonTowerOneKeyDrawData)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.DemonTowerOneKeyDrawData other) {
      if (other == xddq.pb.DemonTowerOneKeyDrawData.getDefaultInstance()) return this;
      if (other.hasFloor()) {
        setFloor(other.getFloor());
      }
      if (!other.drawIndexArr_.isEmpty()) {
        if (drawIndexArr_.isEmpty()) {
          drawIndexArr_ = other.drawIndexArr_;
          drawIndexArr_.makeImmutable();
          bitField0_ |= 0x00000002;
        } else {
          ensureDrawIndexArrIsMutable();
          drawIndexArr_.addAll(other.drawIndexArr_);
        }
        onChanged();
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasFloor()) {
        return false;
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              floor_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              int v = input.readInt32();
              ensureDrawIndexArrIsMutable();
              drawIndexArr_.addInt(v);
              break;
            } // case 16
            case 18: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              ensureDrawIndexArrIsMutable();
              while (input.getBytesUntilLimit() > 0) {
                drawIndexArr_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            } // case 18
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int floor_ ;
    /**
     * <code>required int32 floor = 1;</code>
     * @return Whether the floor field is set.
     */
    @java.lang.Override
    public boolean hasFloor() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 floor = 1;</code>
     * @return The floor.
     */
    @java.lang.Override
    public int getFloor() {
      return floor_;
    }
    /**
     * <code>required int32 floor = 1;</code>
     * @param value The floor to set.
     * @return This builder for chaining.
     */
    public Builder setFloor(int value) {

      floor_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 floor = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearFloor() {
      bitField0_ = (bitField0_ & ~0x00000001);
      floor_ = 0;
      onChanged();
      return this;
    }

    private com.google.protobuf.Internal.IntList drawIndexArr_ = emptyIntList();
    private void ensureDrawIndexArrIsMutable() {
      if (!drawIndexArr_.isModifiable()) {
        drawIndexArr_ = makeMutableCopy(drawIndexArr_);
      }
      bitField0_ |= 0x00000002;
    }
    /**
     * <code>repeated int32 drawIndexArr = 2;</code>
     * @return A list containing the drawIndexArr.
     */
    public java.util.List<java.lang.Integer>
        getDrawIndexArrList() {
      drawIndexArr_.makeImmutable();
      return drawIndexArr_;
    }
    /**
     * <code>repeated int32 drawIndexArr = 2;</code>
     * @return The count of drawIndexArr.
     */
    public int getDrawIndexArrCount() {
      return drawIndexArr_.size();
    }
    /**
     * <code>repeated int32 drawIndexArr = 2;</code>
     * @param index The index of the element to return.
     * @return The drawIndexArr at the given index.
     */
    public int getDrawIndexArr(int index) {
      return drawIndexArr_.getInt(index);
    }
    /**
     * <code>repeated int32 drawIndexArr = 2;</code>
     * @param index The index to set the value at.
     * @param value The drawIndexArr to set.
     * @return This builder for chaining.
     */
    public Builder setDrawIndexArr(
        int index, int value) {

      ensureDrawIndexArrIsMutable();
      drawIndexArr_.setInt(index, value);
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 drawIndexArr = 2;</code>
     * @param value The drawIndexArr to add.
     * @return This builder for chaining.
     */
    public Builder addDrawIndexArr(int value) {

      ensureDrawIndexArrIsMutable();
      drawIndexArr_.addInt(value);
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 drawIndexArr = 2;</code>
     * @param values The drawIndexArr to add.
     * @return This builder for chaining.
     */
    public Builder addAllDrawIndexArr(
        java.lang.Iterable<? extends java.lang.Integer> values) {
      ensureDrawIndexArrIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, drawIndexArr_);
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 drawIndexArr = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearDrawIndexArr() {
      drawIndexArr_ = emptyIntList();
      bitField0_ = (bitField0_ & ~0x00000002);
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.DemonTowerOneKeyDrawData)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.DemonTowerOneKeyDrawData)
  private static final xddq.pb.DemonTowerOneKeyDrawData DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.DemonTowerOneKeyDrawData();
  }

  public static xddq.pb.DemonTowerOneKeyDrawData getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<DemonTowerOneKeyDrawData>
      PARSER = new com.google.protobuf.AbstractParser<DemonTowerOneKeyDrawData>() {
    @java.lang.Override
    public DemonTowerOneKeyDrawData parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<DemonTowerOneKeyDrawData> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<DemonTowerOneKeyDrawData> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.DemonTowerOneKeyDrawData getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

