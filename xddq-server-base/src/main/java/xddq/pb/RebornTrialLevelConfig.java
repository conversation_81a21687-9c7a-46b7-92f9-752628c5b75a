// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.RebornTrialLevelConfig}
 */
public final class RebornTrialLevelConfig extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.RebornTrialLevelConfig)
    RebornTrialLevelConfigOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      RebornTrialLevelConfig.class.getName());
  }
  // Use RebornTrialLevelConfig.newBuilder() to construct.
  private RebornTrialLevelConfig(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private RebornTrialLevelConfig() {
    treasure_ = "";
    goodsLevel_ = "";
    event_ = "";
    refusePower_ = "";
    moneyRange_ = "";
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_RebornTrialLevelConfig_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_RebornTrialLevelConfig_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.RebornTrialLevelConfig.class, xddq.pb.RebornTrialLevelConfig.Builder.class);
  }

  private int bitField0_;
  public static final int LEVEL_FIELD_NUMBER = 1;
  private int level_ = 0;
  /**
   * <code>optional int32 level = 1;</code>
   * @return Whether the level field is set.
   */
  @java.lang.Override
  public boolean hasLevel() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int32 level = 1;</code>
   * @return The level.
   */
  @java.lang.Override
  public int getLevel() {
    return level_;
  }

  public static final int EXP_FIELD_NUMBER = 2;
  private long exp_ = 0L;
  /**
   * <code>optional int64 exp = 2;</code>
   * @return Whether the exp field is set.
   */
  @java.lang.Override
  public boolean hasExp() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int64 exp = 2;</code>
   * @return The exp.
   */
  @java.lang.Override
  public long getExp() {
    return exp_;
  }

  public static final int MAP_FIELD_NUMBER = 3;
  private int map_ = 0;
  /**
   * <code>optional int32 map = 3;</code>
   * @return Whether the map field is set.
   */
  @java.lang.Override
  public boolean hasMap() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional int32 map = 3;</code>
   * @return The map.
   */
  @java.lang.Override
  public int getMap() {
    return map_;
  }

  public static final int TREASURE_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private volatile java.lang.Object treasure_ = "";
  /**
   * <code>optional string treasure = 4;</code>
   * @return Whether the treasure field is set.
   */
  @java.lang.Override
  public boolean hasTreasure() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional string treasure = 4;</code>
   * @return The treasure.
   */
  @java.lang.Override
  public java.lang.String getTreasure() {
    java.lang.Object ref = treasure_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        treasure_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string treasure = 4;</code>
   * @return The bytes for treasure.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getTreasureBytes() {
    java.lang.Object ref = treasure_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      treasure_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int COST_FIELD_NUMBER = 5;
  private int cost_ = 0;
  /**
   * <code>optional int32 cost = 5;</code>
   * @return Whether the cost field is set.
   */
  @java.lang.Override
  public boolean hasCost() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>optional int32 cost = 5;</code>
   * @return The cost.
   */
  @java.lang.Override
  public int getCost() {
    return cost_;
  }

  public static final int GOODSLEVEL_FIELD_NUMBER = 6;
  @SuppressWarnings("serial")
  private volatile java.lang.Object goodsLevel_ = "";
  /**
   * <code>optional string goodsLevel = 6;</code>
   * @return Whether the goodsLevel field is set.
   */
  @java.lang.Override
  public boolean hasGoodsLevel() {
    return ((bitField0_ & 0x00000020) != 0);
  }
  /**
   * <code>optional string goodsLevel = 6;</code>
   * @return The goodsLevel.
   */
  @java.lang.Override
  public java.lang.String getGoodsLevel() {
    java.lang.Object ref = goodsLevel_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        goodsLevel_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string goodsLevel = 6;</code>
   * @return The bytes for goodsLevel.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getGoodsLevelBytes() {
    java.lang.Object ref = goodsLevel_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      goodsLevel_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int EVENT_FIELD_NUMBER = 7;
  @SuppressWarnings("serial")
  private volatile java.lang.Object event_ = "";
  /**
   * <code>optional string event = 7;</code>
   * @return Whether the event field is set.
   */
  @java.lang.Override
  public boolean hasEvent() {
    return ((bitField0_ & 0x00000040) != 0);
  }
  /**
   * <code>optional string event = 7;</code>
   * @return The event.
   */
  @java.lang.Override
  public java.lang.String getEvent() {
    java.lang.Object ref = event_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        event_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string event = 7;</code>
   * @return The bytes for event.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getEventBytes() {
    java.lang.Object ref = event_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      event_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int REFUSEPOWER_FIELD_NUMBER = 8;
  @SuppressWarnings("serial")
  private volatile java.lang.Object refusePower_ = "";
  /**
   * <code>optional string refusePower = 8;</code>
   * @return Whether the refusePower field is set.
   */
  @java.lang.Override
  public boolean hasRefusePower() {
    return ((bitField0_ & 0x00000080) != 0);
  }
  /**
   * <code>optional string refusePower = 8;</code>
   * @return The refusePower.
   */
  @java.lang.Override
  public java.lang.String getRefusePower() {
    java.lang.Object ref = refusePower_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        refusePower_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string refusePower = 8;</code>
   * @return The bytes for refusePower.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getRefusePowerBytes() {
    java.lang.Object ref = refusePower_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      refusePower_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int MONEYRANGE_FIELD_NUMBER = 9;
  @SuppressWarnings("serial")
  private volatile java.lang.Object moneyRange_ = "";
  /**
   * <code>optional string moneyRange = 9;</code>
   * @return Whether the moneyRange field is set.
   */
  @java.lang.Override
  public boolean hasMoneyRange() {
    return ((bitField0_ & 0x00000100) != 0);
  }
  /**
   * <code>optional string moneyRange = 9;</code>
   * @return The moneyRange.
   */
  @java.lang.Override
  public java.lang.String getMoneyRange() {
    java.lang.Object ref = moneyRange_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        moneyRange_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string moneyRange = 9;</code>
   * @return The bytes for moneyRange.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getMoneyRangeBytes() {
    java.lang.Object ref = moneyRange_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      moneyRange_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int MONEYTRIGGERBOUND_FIELD_NUMBER = 10;
  private long moneyTriggerBound_ = 0L;
  /**
   * <code>optional int64 moneyTriggerBound = 10;</code>
   * @return Whether the moneyTriggerBound field is set.
   */
  @java.lang.Override
  public boolean hasMoneyTriggerBound() {
    return ((bitField0_ & 0x00000200) != 0);
  }
  /**
   * <code>optional int64 moneyTriggerBound = 10;</code>
   * @return The moneyTriggerBound.
   */
  @java.lang.Override
  public long getMoneyTriggerBound() {
    return moneyTriggerBound_;
  }

  public static final int SCORE_FIELD_NUMBER = 11;
  private long score_ = 0L;
  /**
   * <code>optional int64 score = 11;</code>
   * @return Whether the score field is set.
   */
  @java.lang.Override
  public boolean hasScore() {
    return ((bitField0_ & 0x00000400) != 0);
  }
  /**
   * <code>optional int64 score = 11;</code>
   * @return The score.
   */
  @java.lang.Override
  public long getScore() {
    return score_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, level_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt64(2, exp_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt32(3, map_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 4, treasure_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      output.writeInt32(5, cost_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 6, goodsLevel_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 7, event_);
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 8, refusePower_);
    }
    if (((bitField0_ & 0x00000100) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 9, moneyRange_);
    }
    if (((bitField0_ & 0x00000200) != 0)) {
      output.writeInt64(10, moneyTriggerBound_);
    }
    if (((bitField0_ & 0x00000400) != 0)) {
      output.writeInt64(11, score_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, level_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(2, exp_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(3, map_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(4, treasure_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(5, cost_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(6, goodsLevel_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(7, event_);
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(8, refusePower_);
    }
    if (((bitField0_ & 0x00000100) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(9, moneyRange_);
    }
    if (((bitField0_ & 0x00000200) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(10, moneyTriggerBound_);
    }
    if (((bitField0_ & 0x00000400) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(11, score_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.RebornTrialLevelConfig)) {
      return super.equals(obj);
    }
    xddq.pb.RebornTrialLevelConfig other = (xddq.pb.RebornTrialLevelConfig) obj;

    if (hasLevel() != other.hasLevel()) return false;
    if (hasLevel()) {
      if (getLevel()
          != other.getLevel()) return false;
    }
    if (hasExp() != other.hasExp()) return false;
    if (hasExp()) {
      if (getExp()
          != other.getExp()) return false;
    }
    if (hasMap() != other.hasMap()) return false;
    if (hasMap()) {
      if (getMap()
          != other.getMap()) return false;
    }
    if (hasTreasure() != other.hasTreasure()) return false;
    if (hasTreasure()) {
      if (!getTreasure()
          .equals(other.getTreasure())) return false;
    }
    if (hasCost() != other.hasCost()) return false;
    if (hasCost()) {
      if (getCost()
          != other.getCost()) return false;
    }
    if (hasGoodsLevel() != other.hasGoodsLevel()) return false;
    if (hasGoodsLevel()) {
      if (!getGoodsLevel()
          .equals(other.getGoodsLevel())) return false;
    }
    if (hasEvent() != other.hasEvent()) return false;
    if (hasEvent()) {
      if (!getEvent()
          .equals(other.getEvent())) return false;
    }
    if (hasRefusePower() != other.hasRefusePower()) return false;
    if (hasRefusePower()) {
      if (!getRefusePower()
          .equals(other.getRefusePower())) return false;
    }
    if (hasMoneyRange() != other.hasMoneyRange()) return false;
    if (hasMoneyRange()) {
      if (!getMoneyRange()
          .equals(other.getMoneyRange())) return false;
    }
    if (hasMoneyTriggerBound() != other.hasMoneyTriggerBound()) return false;
    if (hasMoneyTriggerBound()) {
      if (getMoneyTriggerBound()
          != other.getMoneyTriggerBound()) return false;
    }
    if (hasScore() != other.hasScore()) return false;
    if (hasScore()) {
      if (getScore()
          != other.getScore()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasLevel()) {
      hash = (37 * hash) + LEVEL_FIELD_NUMBER;
      hash = (53 * hash) + getLevel();
    }
    if (hasExp()) {
      hash = (37 * hash) + EXP_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getExp());
    }
    if (hasMap()) {
      hash = (37 * hash) + MAP_FIELD_NUMBER;
      hash = (53 * hash) + getMap();
    }
    if (hasTreasure()) {
      hash = (37 * hash) + TREASURE_FIELD_NUMBER;
      hash = (53 * hash) + getTreasure().hashCode();
    }
    if (hasCost()) {
      hash = (37 * hash) + COST_FIELD_NUMBER;
      hash = (53 * hash) + getCost();
    }
    if (hasGoodsLevel()) {
      hash = (37 * hash) + GOODSLEVEL_FIELD_NUMBER;
      hash = (53 * hash) + getGoodsLevel().hashCode();
    }
    if (hasEvent()) {
      hash = (37 * hash) + EVENT_FIELD_NUMBER;
      hash = (53 * hash) + getEvent().hashCode();
    }
    if (hasRefusePower()) {
      hash = (37 * hash) + REFUSEPOWER_FIELD_NUMBER;
      hash = (53 * hash) + getRefusePower().hashCode();
    }
    if (hasMoneyRange()) {
      hash = (37 * hash) + MONEYRANGE_FIELD_NUMBER;
      hash = (53 * hash) + getMoneyRange().hashCode();
    }
    if (hasMoneyTriggerBound()) {
      hash = (37 * hash) + MONEYTRIGGERBOUND_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getMoneyTriggerBound());
    }
    if (hasScore()) {
      hash = (37 * hash) + SCORE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getScore());
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.RebornTrialLevelConfig parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.RebornTrialLevelConfig parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.RebornTrialLevelConfig parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.RebornTrialLevelConfig parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.RebornTrialLevelConfig parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.RebornTrialLevelConfig parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.RebornTrialLevelConfig parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.RebornTrialLevelConfig parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.RebornTrialLevelConfig parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.RebornTrialLevelConfig parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.RebornTrialLevelConfig parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.RebornTrialLevelConfig parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.RebornTrialLevelConfig prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.RebornTrialLevelConfig}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.RebornTrialLevelConfig)
      xddq.pb.RebornTrialLevelConfigOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_RebornTrialLevelConfig_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_RebornTrialLevelConfig_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.RebornTrialLevelConfig.class, xddq.pb.RebornTrialLevelConfig.Builder.class);
    }

    // Construct using xddq.pb.RebornTrialLevelConfig.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      level_ = 0;
      exp_ = 0L;
      map_ = 0;
      treasure_ = "";
      cost_ = 0;
      goodsLevel_ = "";
      event_ = "";
      refusePower_ = "";
      moneyRange_ = "";
      moneyTriggerBound_ = 0L;
      score_ = 0L;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_RebornTrialLevelConfig_descriptor;
    }

    @java.lang.Override
    public xddq.pb.RebornTrialLevelConfig getDefaultInstanceForType() {
      return xddq.pb.RebornTrialLevelConfig.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.RebornTrialLevelConfig build() {
      xddq.pb.RebornTrialLevelConfig result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.RebornTrialLevelConfig buildPartial() {
      xddq.pb.RebornTrialLevelConfig result = new xddq.pb.RebornTrialLevelConfig(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.RebornTrialLevelConfig result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.level_ = level_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.exp_ = exp_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.map_ = map_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.treasure_ = treasure_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.cost_ = cost_;
        to_bitField0_ |= 0x00000010;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.goodsLevel_ = goodsLevel_;
        to_bitField0_ |= 0x00000020;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.event_ = event_;
        to_bitField0_ |= 0x00000040;
      }
      if (((from_bitField0_ & 0x00000080) != 0)) {
        result.refusePower_ = refusePower_;
        to_bitField0_ |= 0x00000080;
      }
      if (((from_bitField0_ & 0x00000100) != 0)) {
        result.moneyRange_ = moneyRange_;
        to_bitField0_ |= 0x00000100;
      }
      if (((from_bitField0_ & 0x00000200) != 0)) {
        result.moneyTriggerBound_ = moneyTriggerBound_;
        to_bitField0_ |= 0x00000200;
      }
      if (((from_bitField0_ & 0x00000400) != 0)) {
        result.score_ = score_;
        to_bitField0_ |= 0x00000400;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.RebornTrialLevelConfig) {
        return mergeFrom((xddq.pb.RebornTrialLevelConfig)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.RebornTrialLevelConfig other) {
      if (other == xddq.pb.RebornTrialLevelConfig.getDefaultInstance()) return this;
      if (other.hasLevel()) {
        setLevel(other.getLevel());
      }
      if (other.hasExp()) {
        setExp(other.getExp());
      }
      if (other.hasMap()) {
        setMap(other.getMap());
      }
      if (other.hasTreasure()) {
        treasure_ = other.treasure_;
        bitField0_ |= 0x00000008;
        onChanged();
      }
      if (other.hasCost()) {
        setCost(other.getCost());
      }
      if (other.hasGoodsLevel()) {
        goodsLevel_ = other.goodsLevel_;
        bitField0_ |= 0x00000020;
        onChanged();
      }
      if (other.hasEvent()) {
        event_ = other.event_;
        bitField0_ |= 0x00000040;
        onChanged();
      }
      if (other.hasRefusePower()) {
        refusePower_ = other.refusePower_;
        bitField0_ |= 0x00000080;
        onChanged();
      }
      if (other.hasMoneyRange()) {
        moneyRange_ = other.moneyRange_;
        bitField0_ |= 0x00000100;
        onChanged();
      }
      if (other.hasMoneyTriggerBound()) {
        setMoneyTriggerBound(other.getMoneyTriggerBound());
      }
      if (other.hasScore()) {
        setScore(other.getScore());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              level_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              exp_ = input.readInt64();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 24: {
              map_ = input.readInt32();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 34: {
              treasure_ = input.readBytes();
              bitField0_ |= 0x00000008;
              break;
            } // case 34
            case 40: {
              cost_ = input.readInt32();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            case 50: {
              goodsLevel_ = input.readBytes();
              bitField0_ |= 0x00000020;
              break;
            } // case 50
            case 58: {
              event_ = input.readBytes();
              bitField0_ |= 0x00000040;
              break;
            } // case 58
            case 66: {
              refusePower_ = input.readBytes();
              bitField0_ |= 0x00000080;
              break;
            } // case 66
            case 74: {
              moneyRange_ = input.readBytes();
              bitField0_ |= 0x00000100;
              break;
            } // case 74
            case 80: {
              moneyTriggerBound_ = input.readInt64();
              bitField0_ |= 0x00000200;
              break;
            } // case 80
            case 88: {
              score_ = input.readInt64();
              bitField0_ |= 0x00000400;
              break;
            } // case 88
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int level_ ;
    /**
     * <code>optional int32 level = 1;</code>
     * @return Whether the level field is set.
     */
    @java.lang.Override
    public boolean hasLevel() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 level = 1;</code>
     * @return The level.
     */
    @java.lang.Override
    public int getLevel() {
      return level_;
    }
    /**
     * <code>optional int32 level = 1;</code>
     * @param value The level to set.
     * @return This builder for chaining.
     */
    public Builder setLevel(int value) {

      level_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 level = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearLevel() {
      bitField0_ = (bitField0_ & ~0x00000001);
      level_ = 0;
      onChanged();
      return this;
    }

    private long exp_ ;
    /**
     * <code>optional int64 exp = 2;</code>
     * @return Whether the exp field is set.
     */
    @java.lang.Override
    public boolean hasExp() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int64 exp = 2;</code>
     * @return The exp.
     */
    @java.lang.Override
    public long getExp() {
      return exp_;
    }
    /**
     * <code>optional int64 exp = 2;</code>
     * @param value The exp to set.
     * @return This builder for chaining.
     */
    public Builder setExp(long value) {

      exp_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 exp = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearExp() {
      bitField0_ = (bitField0_ & ~0x00000002);
      exp_ = 0L;
      onChanged();
      return this;
    }

    private int map_ ;
    /**
     * <code>optional int32 map = 3;</code>
     * @return Whether the map field is set.
     */
    @java.lang.Override
    public boolean hasMap() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int32 map = 3;</code>
     * @return The map.
     */
    @java.lang.Override
    public int getMap() {
      return map_;
    }
    /**
     * <code>optional int32 map = 3;</code>
     * @param value The map to set.
     * @return This builder for chaining.
     */
    public Builder setMap(int value) {

      map_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 map = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearMap() {
      bitField0_ = (bitField0_ & ~0x00000004);
      map_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object treasure_ = "";
    /**
     * <code>optional string treasure = 4;</code>
     * @return Whether the treasure field is set.
     */
    public boolean hasTreasure() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional string treasure = 4;</code>
     * @return The treasure.
     */
    public java.lang.String getTreasure() {
      java.lang.Object ref = treasure_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          treasure_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string treasure = 4;</code>
     * @return The bytes for treasure.
     */
    public com.google.protobuf.ByteString
        getTreasureBytes() {
      java.lang.Object ref = treasure_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        treasure_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string treasure = 4;</code>
     * @param value The treasure to set.
     * @return This builder for chaining.
     */
    public Builder setTreasure(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      treasure_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional string treasure = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearTreasure() {
      treasure_ = getDefaultInstance().getTreasure();
      bitField0_ = (bitField0_ & ~0x00000008);
      onChanged();
      return this;
    }
    /**
     * <code>optional string treasure = 4;</code>
     * @param value The bytes for treasure to set.
     * @return This builder for chaining.
     */
    public Builder setTreasureBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      treasure_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }

    private int cost_ ;
    /**
     * <code>optional int32 cost = 5;</code>
     * @return Whether the cost field is set.
     */
    @java.lang.Override
    public boolean hasCost() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional int32 cost = 5;</code>
     * @return The cost.
     */
    @java.lang.Override
    public int getCost() {
      return cost_;
    }
    /**
     * <code>optional int32 cost = 5;</code>
     * @param value The cost to set.
     * @return This builder for chaining.
     */
    public Builder setCost(int value) {

      cost_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 cost = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearCost() {
      bitField0_ = (bitField0_ & ~0x00000010);
      cost_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object goodsLevel_ = "";
    /**
     * <code>optional string goodsLevel = 6;</code>
     * @return Whether the goodsLevel field is set.
     */
    public boolean hasGoodsLevel() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional string goodsLevel = 6;</code>
     * @return The goodsLevel.
     */
    public java.lang.String getGoodsLevel() {
      java.lang.Object ref = goodsLevel_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          goodsLevel_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string goodsLevel = 6;</code>
     * @return The bytes for goodsLevel.
     */
    public com.google.protobuf.ByteString
        getGoodsLevelBytes() {
      java.lang.Object ref = goodsLevel_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        goodsLevel_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string goodsLevel = 6;</code>
     * @param value The goodsLevel to set.
     * @return This builder for chaining.
     */
    public Builder setGoodsLevel(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      goodsLevel_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>optional string goodsLevel = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearGoodsLevel() {
      goodsLevel_ = getDefaultInstance().getGoodsLevel();
      bitField0_ = (bitField0_ & ~0x00000020);
      onChanged();
      return this;
    }
    /**
     * <code>optional string goodsLevel = 6;</code>
     * @param value The bytes for goodsLevel to set.
     * @return This builder for chaining.
     */
    public Builder setGoodsLevelBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      goodsLevel_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }

    private java.lang.Object event_ = "";
    /**
     * <code>optional string event = 7;</code>
     * @return Whether the event field is set.
     */
    public boolean hasEvent() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <code>optional string event = 7;</code>
     * @return The event.
     */
    public java.lang.String getEvent() {
      java.lang.Object ref = event_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          event_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string event = 7;</code>
     * @return The bytes for event.
     */
    public com.google.protobuf.ByteString
        getEventBytes() {
      java.lang.Object ref = event_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        event_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string event = 7;</code>
     * @param value The event to set.
     * @return This builder for chaining.
     */
    public Builder setEvent(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      event_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>optional string event = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearEvent() {
      event_ = getDefaultInstance().getEvent();
      bitField0_ = (bitField0_ & ~0x00000040);
      onChanged();
      return this;
    }
    /**
     * <code>optional string event = 7;</code>
     * @param value The bytes for event to set.
     * @return This builder for chaining.
     */
    public Builder setEventBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      event_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }

    private java.lang.Object refusePower_ = "";
    /**
     * <code>optional string refusePower = 8;</code>
     * @return Whether the refusePower field is set.
     */
    public boolean hasRefusePower() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <code>optional string refusePower = 8;</code>
     * @return The refusePower.
     */
    public java.lang.String getRefusePower() {
      java.lang.Object ref = refusePower_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          refusePower_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string refusePower = 8;</code>
     * @return The bytes for refusePower.
     */
    public com.google.protobuf.ByteString
        getRefusePowerBytes() {
      java.lang.Object ref = refusePower_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        refusePower_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string refusePower = 8;</code>
     * @param value The refusePower to set.
     * @return This builder for chaining.
     */
    public Builder setRefusePower(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      refusePower_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }
    /**
     * <code>optional string refusePower = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearRefusePower() {
      refusePower_ = getDefaultInstance().getRefusePower();
      bitField0_ = (bitField0_ & ~0x00000080);
      onChanged();
      return this;
    }
    /**
     * <code>optional string refusePower = 8;</code>
     * @param value The bytes for refusePower to set.
     * @return This builder for chaining.
     */
    public Builder setRefusePowerBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      refusePower_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }

    private java.lang.Object moneyRange_ = "";
    /**
     * <code>optional string moneyRange = 9;</code>
     * @return Whether the moneyRange field is set.
     */
    public boolean hasMoneyRange() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <code>optional string moneyRange = 9;</code>
     * @return The moneyRange.
     */
    public java.lang.String getMoneyRange() {
      java.lang.Object ref = moneyRange_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          moneyRange_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string moneyRange = 9;</code>
     * @return The bytes for moneyRange.
     */
    public com.google.protobuf.ByteString
        getMoneyRangeBytes() {
      java.lang.Object ref = moneyRange_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        moneyRange_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string moneyRange = 9;</code>
     * @param value The moneyRange to set.
     * @return This builder for chaining.
     */
    public Builder setMoneyRange(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      moneyRange_ = value;
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }
    /**
     * <code>optional string moneyRange = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearMoneyRange() {
      moneyRange_ = getDefaultInstance().getMoneyRange();
      bitField0_ = (bitField0_ & ~0x00000100);
      onChanged();
      return this;
    }
    /**
     * <code>optional string moneyRange = 9;</code>
     * @param value The bytes for moneyRange to set.
     * @return This builder for chaining.
     */
    public Builder setMoneyRangeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      moneyRange_ = value;
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }

    private long moneyTriggerBound_ ;
    /**
     * <code>optional int64 moneyTriggerBound = 10;</code>
     * @return Whether the moneyTriggerBound field is set.
     */
    @java.lang.Override
    public boolean hasMoneyTriggerBound() {
      return ((bitField0_ & 0x00000200) != 0);
    }
    /**
     * <code>optional int64 moneyTriggerBound = 10;</code>
     * @return The moneyTriggerBound.
     */
    @java.lang.Override
    public long getMoneyTriggerBound() {
      return moneyTriggerBound_;
    }
    /**
     * <code>optional int64 moneyTriggerBound = 10;</code>
     * @param value The moneyTriggerBound to set.
     * @return This builder for chaining.
     */
    public Builder setMoneyTriggerBound(long value) {

      moneyTriggerBound_ = value;
      bitField0_ |= 0x00000200;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 moneyTriggerBound = 10;</code>
     * @return This builder for chaining.
     */
    public Builder clearMoneyTriggerBound() {
      bitField0_ = (bitField0_ & ~0x00000200);
      moneyTriggerBound_ = 0L;
      onChanged();
      return this;
    }

    private long score_ ;
    /**
     * <code>optional int64 score = 11;</code>
     * @return Whether the score field is set.
     */
    @java.lang.Override
    public boolean hasScore() {
      return ((bitField0_ & 0x00000400) != 0);
    }
    /**
     * <code>optional int64 score = 11;</code>
     * @return The score.
     */
    @java.lang.Override
    public long getScore() {
      return score_;
    }
    /**
     * <code>optional int64 score = 11;</code>
     * @param value The score to set.
     * @return This builder for chaining.
     */
    public Builder setScore(long value) {

      score_ = value;
      bitField0_ |= 0x00000400;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 score = 11;</code>
     * @return This builder for chaining.
     */
    public Builder clearScore() {
      bitField0_ = (bitField0_ & ~0x00000400);
      score_ = 0L;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.RebornTrialLevelConfig)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.RebornTrialLevelConfig)
  private static final xddq.pb.RebornTrialLevelConfig DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.RebornTrialLevelConfig();
  }

  public static xddq.pb.RebornTrialLevelConfig getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<RebornTrialLevelConfig>
      PARSER = new com.google.protobuf.AbstractParser<RebornTrialLevelConfig>() {
    @java.lang.Override
    public RebornTrialLevelConfig parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<RebornTrialLevelConfig> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<RebornTrialLevelConfig> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.RebornTrialLevelConfig getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

