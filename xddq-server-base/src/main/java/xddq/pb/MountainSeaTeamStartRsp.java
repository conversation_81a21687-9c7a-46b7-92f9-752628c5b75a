// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.MountainSeaTeamStartRsp}
 */
public final class MountainSeaTeamStartRsp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.MountainSeaTeamStartRsp)
    MountainSeaTeamStartRspOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      MountainSeaTeamStartRsp.class.getName());
  }
  // Use MountainSeaTeamStartRsp.newBuilder() to construct.
  private MountainSeaTeamStartRsp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private MountainSeaTeamStartRsp() {
    applyJoinTeamIdList_ = emptyLongList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_MountainSeaTeamStartRsp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_MountainSeaTeamStartRsp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.MountainSeaTeamStartRsp.class, xddq.pb.MountainSeaTeamStartRsp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int LASTLEAVETEAM_FIELD_NUMBER = 2;
  private long lastLeaveTeam_ = 0L;
  /**
   * <code>optional int64 lastLeaveTeam = 2;</code>
   * @return Whether the lastLeaveTeam field is set.
   */
  @java.lang.Override
  public boolean hasLastLeaveTeam() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int64 lastLeaveTeam = 2;</code>
   * @return The lastLeaveTeam.
   */
  @java.lang.Override
  public long getLastLeaveTeam() {
    return lastLeaveTeam_;
  }

  public static final int APPLYJOINTEAMIDLIST_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private com.google.protobuf.Internal.LongList applyJoinTeamIdList_ =
      emptyLongList();
  /**
   * <code>repeated int64 applyJoinTeamIdList = 3;</code>
   * @return A list containing the applyJoinTeamIdList.
   */
  @java.lang.Override
  public java.util.List<java.lang.Long>
      getApplyJoinTeamIdListList() {
    return applyJoinTeamIdList_;
  }
  /**
   * <code>repeated int64 applyJoinTeamIdList = 3;</code>
   * @return The count of applyJoinTeamIdList.
   */
  public int getApplyJoinTeamIdListCount() {
    return applyJoinTeamIdList_.size();
  }
  /**
   * <code>repeated int64 applyJoinTeamIdList = 3;</code>
   * @param index The index of the element to return.
   * @return The applyJoinTeamIdList at the given index.
   */
  public long getApplyJoinTeamIdList(int index) {
    return applyJoinTeamIdList_.getLong(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt64(2, lastLeaveTeam_);
    }
    for (int i = 0; i < applyJoinTeamIdList_.size(); i++) {
      output.writeInt64(3, applyJoinTeamIdList_.getLong(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(2, lastLeaveTeam_);
    }
    {
      int dataSize = 0;
      for (int i = 0; i < applyJoinTeamIdList_.size(); i++) {
        dataSize += com.google.protobuf.CodedOutputStream
          .computeInt64SizeNoTag(applyJoinTeamIdList_.getLong(i));
      }
      size += dataSize;
      size += 1 * getApplyJoinTeamIdListList().size();
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.MountainSeaTeamStartRsp)) {
      return super.equals(obj);
    }
    xddq.pb.MountainSeaTeamStartRsp other = (xddq.pb.MountainSeaTeamStartRsp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (hasLastLeaveTeam() != other.hasLastLeaveTeam()) return false;
    if (hasLastLeaveTeam()) {
      if (getLastLeaveTeam()
          != other.getLastLeaveTeam()) return false;
    }
    if (!getApplyJoinTeamIdListList()
        .equals(other.getApplyJoinTeamIdListList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (hasLastLeaveTeam()) {
      hash = (37 * hash) + LASTLEAVETEAM_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getLastLeaveTeam());
    }
    if (getApplyJoinTeamIdListCount() > 0) {
      hash = (37 * hash) + APPLYJOINTEAMIDLIST_FIELD_NUMBER;
      hash = (53 * hash) + getApplyJoinTeamIdListList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.MountainSeaTeamStartRsp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.MountainSeaTeamStartRsp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.MountainSeaTeamStartRsp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.MountainSeaTeamStartRsp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.MountainSeaTeamStartRsp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.MountainSeaTeamStartRsp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.MountainSeaTeamStartRsp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.MountainSeaTeamStartRsp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.MountainSeaTeamStartRsp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.MountainSeaTeamStartRsp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.MountainSeaTeamStartRsp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.MountainSeaTeamStartRsp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.MountainSeaTeamStartRsp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.MountainSeaTeamStartRsp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.MountainSeaTeamStartRsp)
      xddq.pb.MountainSeaTeamStartRspOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_MountainSeaTeamStartRsp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_MountainSeaTeamStartRsp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.MountainSeaTeamStartRsp.class, xddq.pb.MountainSeaTeamStartRsp.Builder.class);
    }

    // Construct using xddq.pb.MountainSeaTeamStartRsp.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      lastLeaveTeam_ = 0L;
      applyJoinTeamIdList_ = emptyLongList();
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_MountainSeaTeamStartRsp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.MountainSeaTeamStartRsp getDefaultInstanceForType() {
      return xddq.pb.MountainSeaTeamStartRsp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.MountainSeaTeamStartRsp build() {
      xddq.pb.MountainSeaTeamStartRsp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.MountainSeaTeamStartRsp buildPartial() {
      xddq.pb.MountainSeaTeamStartRsp result = new xddq.pb.MountainSeaTeamStartRsp(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.MountainSeaTeamStartRsp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.lastLeaveTeam_ = lastLeaveTeam_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        applyJoinTeamIdList_.makeImmutable();
        result.applyJoinTeamIdList_ = applyJoinTeamIdList_;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.MountainSeaTeamStartRsp) {
        return mergeFrom((xddq.pb.MountainSeaTeamStartRsp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.MountainSeaTeamStartRsp other) {
      if (other == xddq.pb.MountainSeaTeamStartRsp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (other.hasLastLeaveTeam()) {
        setLastLeaveTeam(other.getLastLeaveTeam());
      }
      if (!other.applyJoinTeamIdList_.isEmpty()) {
        if (applyJoinTeamIdList_.isEmpty()) {
          applyJoinTeamIdList_ = other.applyJoinTeamIdList_;
          applyJoinTeamIdList_.makeImmutable();
          bitField0_ |= 0x00000004;
        } else {
          ensureApplyJoinTeamIdListIsMutable();
          applyJoinTeamIdList_.addAll(other.applyJoinTeamIdList_);
        }
        onChanged();
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              lastLeaveTeam_ = input.readInt64();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 24: {
              long v = input.readInt64();
              ensureApplyJoinTeamIdListIsMutable();
              applyJoinTeamIdList_.addLong(v);
              break;
            } // case 24
            case 26: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              ensureApplyJoinTeamIdListIsMutable();
              while (input.getBytesUntilLimit() > 0) {
                applyJoinTeamIdList_.addLong(input.readInt64());
              }
              input.popLimit(limit);
              break;
            } // case 26
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private long lastLeaveTeam_ ;
    /**
     * <code>optional int64 lastLeaveTeam = 2;</code>
     * @return Whether the lastLeaveTeam field is set.
     */
    @java.lang.Override
    public boolean hasLastLeaveTeam() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int64 lastLeaveTeam = 2;</code>
     * @return The lastLeaveTeam.
     */
    @java.lang.Override
    public long getLastLeaveTeam() {
      return lastLeaveTeam_;
    }
    /**
     * <code>optional int64 lastLeaveTeam = 2;</code>
     * @param value The lastLeaveTeam to set.
     * @return This builder for chaining.
     */
    public Builder setLastLeaveTeam(long value) {

      lastLeaveTeam_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 lastLeaveTeam = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearLastLeaveTeam() {
      bitField0_ = (bitField0_ & ~0x00000002);
      lastLeaveTeam_ = 0L;
      onChanged();
      return this;
    }

    private com.google.protobuf.Internal.LongList applyJoinTeamIdList_ = emptyLongList();
    private void ensureApplyJoinTeamIdListIsMutable() {
      if (!applyJoinTeamIdList_.isModifiable()) {
        applyJoinTeamIdList_ = makeMutableCopy(applyJoinTeamIdList_);
      }
      bitField0_ |= 0x00000004;
    }
    /**
     * <code>repeated int64 applyJoinTeamIdList = 3;</code>
     * @return A list containing the applyJoinTeamIdList.
     */
    public java.util.List<java.lang.Long>
        getApplyJoinTeamIdListList() {
      applyJoinTeamIdList_.makeImmutable();
      return applyJoinTeamIdList_;
    }
    /**
     * <code>repeated int64 applyJoinTeamIdList = 3;</code>
     * @return The count of applyJoinTeamIdList.
     */
    public int getApplyJoinTeamIdListCount() {
      return applyJoinTeamIdList_.size();
    }
    /**
     * <code>repeated int64 applyJoinTeamIdList = 3;</code>
     * @param index The index of the element to return.
     * @return The applyJoinTeamIdList at the given index.
     */
    public long getApplyJoinTeamIdList(int index) {
      return applyJoinTeamIdList_.getLong(index);
    }
    /**
     * <code>repeated int64 applyJoinTeamIdList = 3;</code>
     * @param index The index to set the value at.
     * @param value The applyJoinTeamIdList to set.
     * @return This builder for chaining.
     */
    public Builder setApplyJoinTeamIdList(
        int index, long value) {

      ensureApplyJoinTeamIdListIsMutable();
      applyJoinTeamIdList_.setLong(index, value);
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int64 applyJoinTeamIdList = 3;</code>
     * @param value The applyJoinTeamIdList to add.
     * @return This builder for chaining.
     */
    public Builder addApplyJoinTeamIdList(long value) {

      ensureApplyJoinTeamIdListIsMutable();
      applyJoinTeamIdList_.addLong(value);
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int64 applyJoinTeamIdList = 3;</code>
     * @param values The applyJoinTeamIdList to add.
     * @return This builder for chaining.
     */
    public Builder addAllApplyJoinTeamIdList(
        java.lang.Iterable<? extends java.lang.Long> values) {
      ensureApplyJoinTeamIdListIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, applyJoinTeamIdList_);
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int64 applyJoinTeamIdList = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearApplyJoinTeamIdList() {
      applyJoinTeamIdList_ = emptyLongList();
      bitField0_ = (bitField0_ & ~0x00000004);
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.MountainSeaTeamStartRsp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.MountainSeaTeamStartRsp)
  private static final xddq.pb.MountainSeaTeamStartRsp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.MountainSeaTeamStartRsp();
  }

  public static xddq.pb.MountainSeaTeamStartRsp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<MountainSeaTeamStartRsp>
      PARSER = new com.google.protobuf.AbstractParser<MountainSeaTeamStartRsp>() {
    @java.lang.Override
    public MountainSeaTeamStartRsp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<MountainSeaTeamStartRsp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<MountainSeaTeamStartRsp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.MountainSeaTeamStartRsp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

