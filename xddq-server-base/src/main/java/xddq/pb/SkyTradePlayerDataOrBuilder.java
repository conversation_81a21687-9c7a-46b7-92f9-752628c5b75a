// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

public interface SkyTradePlayerDataOrBuilder extends
    // @@protoc_insertion_point(interface_extends:xddq.pb.SkyTradePlayerData)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>optional .xddq.pb.SkyTradeAirshipInfo airshipInfo = 1;</code>
   * @return Whether the airshipInfo field is set.
   */
  boolean hasAirshipInfo();
  /**
   * <code>optional .xddq.pb.SkyTradeAirshipInfo airshipInfo = 1;</code>
   * @return The airshipInfo.
   */
  xddq.pb.SkyTradeAirshipInfo getAirshipInfo();
  /**
   * <code>optional .xddq.pb.SkyTradeAirshipInfo airshipInfo = 1;</code>
   */
  xddq.pb.SkyTradeAirshipInfoOrBuilder getAirshipInfoOrBuilder();

  /**
   * <code>optional int64 availableFunds = 2;</code>
   * @return Whether the availableFunds field is set.
   */
  boolean hasAvailableFunds();
  /**
   * <code>optional int64 availableFunds = 2;</code>
   * @return The availableFunds.
   */
  long getAvailableFunds();

  /**
   * <code>repeated .xddq.pb.SkyTradeGoods goods = 3;</code>
   */
  java.util.List<xddq.pb.SkyTradeGoods> 
      getGoodsList();
  /**
   * <code>repeated .xddq.pb.SkyTradeGoods goods = 3;</code>
   */
  xddq.pb.SkyTradeGoods getGoods(int index);
  /**
   * <code>repeated .xddq.pb.SkyTradeGoods goods = 3;</code>
   */
  int getGoodsCount();
  /**
   * <code>repeated .xddq.pb.SkyTradeGoods goods = 3;</code>
   */
  java.util.List<? extends xddq.pb.SkyTradeGoodsOrBuilder> 
      getGoodsOrBuilderList();
  /**
   * <code>repeated .xddq.pb.SkyTradeGoods goods = 3;</code>
   */
  xddq.pb.SkyTradeGoodsOrBuilder getGoodsOrBuilder(
      int index);

  /**
   * <code>optional int32 stockFundsLevel = 4;</code>
   * @return Whether the stockFundsLevel field is set.
   */
  boolean hasStockFundsLevel();
  /**
   * <code>optional int32 stockFundsLevel = 4;</code>
   * @return The stockFundsLevel.
   */
  int getStockFundsLevel();
}
