// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.PeakFightEnterActivityResp}
 */
public final class PeakFightEnterActivityResp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.PeakFightEnterActivityResp)
    PeakFightEnterActivityRespOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      PeakFightEnterActivityResp.class.getName());
  }
  // Use PeakFightEnterActivityResp.newBuilder() to construct.
  private PeakFightEnterActivityResp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private PeakFightEnterActivityResp() {
    top3Unions_ = java.util.Collections.emptyList();
    roundGroups_ = java.util.Collections.emptyList();
    showStageUnion_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_PeakFightEnterActivityResp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_PeakFightEnterActivityResp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.PeakFightEnterActivityResp.class, xddq.pb.PeakFightEnterActivityResp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int GROUP_FIELD_NUMBER = 2;
  private int group_ = 0;
  /**
   * <code>optional int32 group = 2;</code>
   * @return Whether the group field is set.
   */
  @java.lang.Override
  public boolean hasGroup() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int32 group = 2;</code>
   * @return The group.
   */
  @java.lang.Override
  public int getGroup() {
    return group_;
  }

  public static final int WORSHIPPLAYER_FIELD_NUMBER = 3;
  private xddq.pb.PalacePlayerShowMsg worshipPlayer_;
  /**
   * <code>optional .xddq.pb.PalacePlayerShowMsg worshipPlayer = 3;</code>
   * @return Whether the worshipPlayer field is set.
   */
  @java.lang.Override
  public boolean hasWorshipPlayer() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional .xddq.pb.PalacePlayerShowMsg worshipPlayer = 3;</code>
   * @return The worshipPlayer.
   */
  @java.lang.Override
  public xddq.pb.PalacePlayerShowMsg getWorshipPlayer() {
    return worshipPlayer_ == null ? xddq.pb.PalacePlayerShowMsg.getDefaultInstance() : worshipPlayer_;
  }
  /**
   * <code>optional .xddq.pb.PalacePlayerShowMsg worshipPlayer = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.PalacePlayerShowMsgOrBuilder getWorshipPlayerOrBuilder() {
    return worshipPlayer_ == null ? xddq.pb.PalacePlayerShowMsg.getDefaultInstance() : worshipPlayer_;
  }

  public static final int TOP3UNIONS_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.PeakFightUnionBaseMsg> top3Unions_;
  /**
   * <code>repeated .xddq.pb.PeakFightUnionBaseMsg top3Unions = 4;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.PeakFightUnionBaseMsg> getTop3UnionsList() {
    return top3Unions_;
  }
  /**
   * <code>repeated .xddq.pb.PeakFightUnionBaseMsg top3Unions = 4;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.PeakFightUnionBaseMsgOrBuilder> 
      getTop3UnionsOrBuilderList() {
    return top3Unions_;
  }
  /**
   * <code>repeated .xddq.pb.PeakFightUnionBaseMsg top3Unions = 4;</code>
   */
  @java.lang.Override
  public int getTop3UnionsCount() {
    return top3Unions_.size();
  }
  /**
   * <code>repeated .xddq.pb.PeakFightUnionBaseMsg top3Unions = 4;</code>
   */
  @java.lang.Override
  public xddq.pb.PeakFightUnionBaseMsg getTop3Unions(int index) {
    return top3Unions_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.PeakFightUnionBaseMsg top3Unions = 4;</code>
   */
  @java.lang.Override
  public xddq.pb.PeakFightUnionBaseMsgOrBuilder getTop3UnionsOrBuilder(
      int index) {
    return top3Unions_.get(index);
  }

  public static final int HASWORSHIP_FIELD_NUMBER = 5;
  private boolean hasWorship_ = false;
  /**
   * <code>optional bool hasWorship = 5;</code>
   * @return Whether the hasWorship field is set.
   */
  @java.lang.Override
  public boolean hasHasWorship() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional bool hasWorship = 5;</code>
   * @return The hasWorship.
   */
  @java.lang.Override
  public boolean getHasWorship() {
    return hasWorship_;
  }

  public static final int POSITION_FIELD_NUMBER = 6;
  private int position_ = 0;
  /**
   * <code>optional int32 position = 6;</code>
   * @return Whether the position field is set.
   */
  @java.lang.Override
  public boolean hasPosition() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>optional int32 position = 6;</code>
   * @return The position.
   */
  @java.lang.Override
  public int getPosition() {
    return position_;
  }

  public static final int UNIONID_FIELD_NUMBER = 7;
  private long unionId_ = 0L;
  /**
   * <code>optional int64 unionId = 7;</code>
   * @return Whether the unionId field is set.
   */
  @java.lang.Override
  public boolean hasUnionId() {
    return ((bitField0_ & 0x00000020) != 0);
  }
  /**
   * <code>optional int64 unionId = 7;</code>
   * @return The unionId.
   */
  @java.lang.Override
  public long getUnionId() {
    return unionId_;
  }

  public static final int APPLYRANKING_FIELD_NUMBER = 8;
  private int applyRanking_ = 0;
  /**
   * <code>optional int32 applyRanking = 8;</code>
   * @return Whether the applyRanking field is set.
   */
  @java.lang.Override
  public boolean hasApplyRanking() {
    return ((bitField0_ & 0x00000040) != 0);
  }
  /**
   * <code>optional int32 applyRanking = 8;</code>
   * @return The applyRanking.
   */
  @java.lang.Override
  public int getApplyRanking() {
    return applyRanking_;
  }

  public static final int ROUNDGROUPS_FIELD_NUMBER = 9;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.PeakFightRoundGroupInfo> roundGroups_;
  /**
   * <code>repeated .xddq.pb.PeakFightRoundGroupInfo roundGroups = 9;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.PeakFightRoundGroupInfo> getRoundGroupsList() {
    return roundGroups_;
  }
  /**
   * <code>repeated .xddq.pb.PeakFightRoundGroupInfo roundGroups = 9;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.PeakFightRoundGroupInfoOrBuilder> 
      getRoundGroupsOrBuilderList() {
    return roundGroups_;
  }
  /**
   * <code>repeated .xddq.pb.PeakFightRoundGroupInfo roundGroups = 9;</code>
   */
  @java.lang.Override
  public int getRoundGroupsCount() {
    return roundGroups_.size();
  }
  /**
   * <code>repeated .xddq.pb.PeakFightRoundGroupInfo roundGroups = 9;</code>
   */
  @java.lang.Override
  public xddq.pb.PeakFightRoundGroupInfo getRoundGroups(int index) {
    return roundGroups_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.PeakFightRoundGroupInfo roundGroups = 9;</code>
   */
  @java.lang.Override
  public xddq.pb.PeakFightRoundGroupInfoOrBuilder getRoundGroupsOrBuilder(
      int index) {
    return roundGroups_.get(index);
  }

  public static final int SETTLEMSG_FIELD_NUMBER = 10;
  private xddq.pb.PeakFightSettleMsg settleMsg_;
  /**
   * <code>optional .xddq.pb.PeakFightSettleMsg settleMsg = 10;</code>
   * @return Whether the settleMsg field is set.
   */
  @java.lang.Override
  public boolean hasSettleMsg() {
    return ((bitField0_ & 0x00000080) != 0);
  }
  /**
   * <code>optional .xddq.pb.PeakFightSettleMsg settleMsg = 10;</code>
   * @return The settleMsg.
   */
  @java.lang.Override
  public xddq.pb.PeakFightSettleMsg getSettleMsg() {
    return settleMsg_ == null ? xddq.pb.PeakFightSettleMsg.getDefaultInstance() : settleMsg_;
  }
  /**
   * <code>optional .xddq.pb.PeakFightSettleMsg settleMsg = 10;</code>
   */
  @java.lang.Override
  public xddq.pb.PeakFightSettleMsgOrBuilder getSettleMsgOrBuilder() {
    return settleMsg_ == null ? xddq.pb.PeakFightSettleMsg.getDefaultInstance() : settleMsg_;
  }

  public static final int ROOMID_FIELD_NUMBER = 11;
  private int roomId_ = 0;
  /**
   * <code>optional int32 roomId = 11;</code>
   * @return Whether the roomId field is set.
   */
  @java.lang.Override
  public boolean hasRoomId() {
    return ((bitField0_ & 0x00000100) != 0);
  }
  /**
   * <code>optional int32 roomId = 11;</code>
   * @return The roomId.
   */
  @java.lang.Override
  public int getRoomId() {
    return roomId_;
  }

  public static final int SHOWSTAGEUNION_FIELD_NUMBER = 12;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.PeakFightUnionBaseMsg> showStageUnion_;
  /**
   * <code>repeated .xddq.pb.PeakFightUnionBaseMsg showStageUnion = 12;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.PeakFightUnionBaseMsg> getShowStageUnionList() {
    return showStageUnion_;
  }
  /**
   * <code>repeated .xddq.pb.PeakFightUnionBaseMsg showStageUnion = 12;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.PeakFightUnionBaseMsgOrBuilder> 
      getShowStageUnionOrBuilderList() {
    return showStageUnion_;
  }
  /**
   * <code>repeated .xddq.pb.PeakFightUnionBaseMsg showStageUnion = 12;</code>
   */
  @java.lang.Override
  public int getShowStageUnionCount() {
    return showStageUnion_.size();
  }
  /**
   * <code>repeated .xddq.pb.PeakFightUnionBaseMsg showStageUnion = 12;</code>
   */
  @java.lang.Override
  public xddq.pb.PeakFightUnionBaseMsg getShowStageUnion(int index) {
    return showStageUnion_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.PeakFightUnionBaseMsg showStageUnion = 12;</code>
   */
  @java.lang.Override
  public xddq.pb.PeakFightUnionBaseMsgOrBuilder getShowStageUnionOrBuilder(
      int index) {
    return showStageUnion_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (hasWorshipPlayer()) {
      if (!getWorshipPlayer().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(2, group_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeMessage(3, getWorshipPlayer());
    }
    for (int i = 0; i < top3Unions_.size(); i++) {
      output.writeMessage(4, top3Unions_.get(i));
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeBool(5, hasWorship_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      output.writeInt32(6, position_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      output.writeInt64(7, unionId_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      output.writeInt32(8, applyRanking_);
    }
    for (int i = 0; i < roundGroups_.size(); i++) {
      output.writeMessage(9, roundGroups_.get(i));
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      output.writeMessage(10, getSettleMsg());
    }
    if (((bitField0_ & 0x00000100) != 0)) {
      output.writeInt32(11, roomId_);
    }
    for (int i = 0; i < showStageUnion_.size(); i++) {
      output.writeMessage(12, showStageUnion_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, group_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, getWorshipPlayer());
    }
    for (int i = 0; i < top3Unions_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(4, top3Unions_.get(i));
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(5, hasWorship_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(6, position_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(7, unionId_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(8, applyRanking_);
    }
    for (int i = 0; i < roundGroups_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(9, roundGroups_.get(i));
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(10, getSettleMsg());
    }
    if (((bitField0_ & 0x00000100) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(11, roomId_);
    }
    for (int i = 0; i < showStageUnion_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(12, showStageUnion_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.PeakFightEnterActivityResp)) {
      return super.equals(obj);
    }
    xddq.pb.PeakFightEnterActivityResp other = (xddq.pb.PeakFightEnterActivityResp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (hasGroup() != other.hasGroup()) return false;
    if (hasGroup()) {
      if (getGroup()
          != other.getGroup()) return false;
    }
    if (hasWorshipPlayer() != other.hasWorshipPlayer()) return false;
    if (hasWorshipPlayer()) {
      if (!getWorshipPlayer()
          .equals(other.getWorshipPlayer())) return false;
    }
    if (!getTop3UnionsList()
        .equals(other.getTop3UnionsList())) return false;
    if (hasHasWorship() != other.hasHasWorship()) return false;
    if (hasHasWorship()) {
      if (getHasWorship()
          != other.getHasWorship()) return false;
    }
    if (hasPosition() != other.hasPosition()) return false;
    if (hasPosition()) {
      if (getPosition()
          != other.getPosition()) return false;
    }
    if (hasUnionId() != other.hasUnionId()) return false;
    if (hasUnionId()) {
      if (getUnionId()
          != other.getUnionId()) return false;
    }
    if (hasApplyRanking() != other.hasApplyRanking()) return false;
    if (hasApplyRanking()) {
      if (getApplyRanking()
          != other.getApplyRanking()) return false;
    }
    if (!getRoundGroupsList()
        .equals(other.getRoundGroupsList())) return false;
    if (hasSettleMsg() != other.hasSettleMsg()) return false;
    if (hasSettleMsg()) {
      if (!getSettleMsg()
          .equals(other.getSettleMsg())) return false;
    }
    if (hasRoomId() != other.hasRoomId()) return false;
    if (hasRoomId()) {
      if (getRoomId()
          != other.getRoomId()) return false;
    }
    if (!getShowStageUnionList()
        .equals(other.getShowStageUnionList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (hasGroup()) {
      hash = (37 * hash) + GROUP_FIELD_NUMBER;
      hash = (53 * hash) + getGroup();
    }
    if (hasWorshipPlayer()) {
      hash = (37 * hash) + WORSHIPPLAYER_FIELD_NUMBER;
      hash = (53 * hash) + getWorshipPlayer().hashCode();
    }
    if (getTop3UnionsCount() > 0) {
      hash = (37 * hash) + TOP3UNIONS_FIELD_NUMBER;
      hash = (53 * hash) + getTop3UnionsList().hashCode();
    }
    if (hasHasWorship()) {
      hash = (37 * hash) + HASWORSHIP_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getHasWorship());
    }
    if (hasPosition()) {
      hash = (37 * hash) + POSITION_FIELD_NUMBER;
      hash = (53 * hash) + getPosition();
    }
    if (hasUnionId()) {
      hash = (37 * hash) + UNIONID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getUnionId());
    }
    if (hasApplyRanking()) {
      hash = (37 * hash) + APPLYRANKING_FIELD_NUMBER;
      hash = (53 * hash) + getApplyRanking();
    }
    if (getRoundGroupsCount() > 0) {
      hash = (37 * hash) + ROUNDGROUPS_FIELD_NUMBER;
      hash = (53 * hash) + getRoundGroupsList().hashCode();
    }
    if (hasSettleMsg()) {
      hash = (37 * hash) + SETTLEMSG_FIELD_NUMBER;
      hash = (53 * hash) + getSettleMsg().hashCode();
    }
    if (hasRoomId()) {
      hash = (37 * hash) + ROOMID_FIELD_NUMBER;
      hash = (53 * hash) + getRoomId();
    }
    if (getShowStageUnionCount() > 0) {
      hash = (37 * hash) + SHOWSTAGEUNION_FIELD_NUMBER;
      hash = (53 * hash) + getShowStageUnionList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.PeakFightEnterActivityResp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PeakFightEnterActivityResp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PeakFightEnterActivityResp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PeakFightEnterActivityResp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PeakFightEnterActivityResp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PeakFightEnterActivityResp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PeakFightEnterActivityResp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.PeakFightEnterActivityResp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.PeakFightEnterActivityResp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.PeakFightEnterActivityResp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.PeakFightEnterActivityResp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.PeakFightEnterActivityResp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.PeakFightEnterActivityResp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.PeakFightEnterActivityResp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.PeakFightEnterActivityResp)
      xddq.pb.PeakFightEnterActivityRespOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PeakFightEnterActivityResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PeakFightEnterActivityResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.PeakFightEnterActivityResp.class, xddq.pb.PeakFightEnterActivityResp.Builder.class);
    }

    // Construct using xddq.pb.PeakFightEnterActivityResp.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetWorshipPlayerFieldBuilder();
        internalGetTop3UnionsFieldBuilder();
        internalGetRoundGroupsFieldBuilder();
        internalGetSettleMsgFieldBuilder();
        internalGetShowStageUnionFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      group_ = 0;
      worshipPlayer_ = null;
      if (worshipPlayerBuilder_ != null) {
        worshipPlayerBuilder_.dispose();
        worshipPlayerBuilder_ = null;
      }
      if (top3UnionsBuilder_ == null) {
        top3Unions_ = java.util.Collections.emptyList();
      } else {
        top3Unions_ = null;
        top3UnionsBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000008);
      hasWorship_ = false;
      position_ = 0;
      unionId_ = 0L;
      applyRanking_ = 0;
      if (roundGroupsBuilder_ == null) {
        roundGroups_ = java.util.Collections.emptyList();
      } else {
        roundGroups_ = null;
        roundGroupsBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000100);
      settleMsg_ = null;
      if (settleMsgBuilder_ != null) {
        settleMsgBuilder_.dispose();
        settleMsgBuilder_ = null;
      }
      roomId_ = 0;
      if (showStageUnionBuilder_ == null) {
        showStageUnion_ = java.util.Collections.emptyList();
      } else {
        showStageUnion_ = null;
        showStageUnionBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000800);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PeakFightEnterActivityResp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.PeakFightEnterActivityResp getDefaultInstanceForType() {
      return xddq.pb.PeakFightEnterActivityResp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.PeakFightEnterActivityResp build() {
      xddq.pb.PeakFightEnterActivityResp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.PeakFightEnterActivityResp buildPartial() {
      xddq.pb.PeakFightEnterActivityResp result = new xddq.pb.PeakFightEnterActivityResp(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.PeakFightEnterActivityResp result) {
      if (top3UnionsBuilder_ == null) {
        if (((bitField0_ & 0x00000008) != 0)) {
          top3Unions_ = java.util.Collections.unmodifiableList(top3Unions_);
          bitField0_ = (bitField0_ & ~0x00000008);
        }
        result.top3Unions_ = top3Unions_;
      } else {
        result.top3Unions_ = top3UnionsBuilder_.build();
      }
      if (roundGroupsBuilder_ == null) {
        if (((bitField0_ & 0x00000100) != 0)) {
          roundGroups_ = java.util.Collections.unmodifiableList(roundGroups_);
          bitField0_ = (bitField0_ & ~0x00000100);
        }
        result.roundGroups_ = roundGroups_;
      } else {
        result.roundGroups_ = roundGroupsBuilder_.build();
      }
      if (showStageUnionBuilder_ == null) {
        if (((bitField0_ & 0x00000800) != 0)) {
          showStageUnion_ = java.util.Collections.unmodifiableList(showStageUnion_);
          bitField0_ = (bitField0_ & ~0x00000800);
        }
        result.showStageUnion_ = showStageUnion_;
      } else {
        result.showStageUnion_ = showStageUnionBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.PeakFightEnterActivityResp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.group_ = group_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.worshipPlayer_ = worshipPlayerBuilder_ == null
            ? worshipPlayer_
            : worshipPlayerBuilder_.build();
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.hasWorship_ = hasWorship_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.position_ = position_;
        to_bitField0_ |= 0x00000010;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.unionId_ = unionId_;
        to_bitField0_ |= 0x00000020;
      }
      if (((from_bitField0_ & 0x00000080) != 0)) {
        result.applyRanking_ = applyRanking_;
        to_bitField0_ |= 0x00000040;
      }
      if (((from_bitField0_ & 0x00000200) != 0)) {
        result.settleMsg_ = settleMsgBuilder_ == null
            ? settleMsg_
            : settleMsgBuilder_.build();
        to_bitField0_ |= 0x00000080;
      }
      if (((from_bitField0_ & 0x00000400) != 0)) {
        result.roomId_ = roomId_;
        to_bitField0_ |= 0x00000100;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.PeakFightEnterActivityResp) {
        return mergeFrom((xddq.pb.PeakFightEnterActivityResp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.PeakFightEnterActivityResp other) {
      if (other == xddq.pb.PeakFightEnterActivityResp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (other.hasGroup()) {
        setGroup(other.getGroup());
      }
      if (other.hasWorshipPlayer()) {
        mergeWorshipPlayer(other.getWorshipPlayer());
      }
      if (top3UnionsBuilder_ == null) {
        if (!other.top3Unions_.isEmpty()) {
          if (top3Unions_.isEmpty()) {
            top3Unions_ = other.top3Unions_;
            bitField0_ = (bitField0_ & ~0x00000008);
          } else {
            ensureTop3UnionsIsMutable();
            top3Unions_.addAll(other.top3Unions_);
          }
          onChanged();
        }
      } else {
        if (!other.top3Unions_.isEmpty()) {
          if (top3UnionsBuilder_.isEmpty()) {
            top3UnionsBuilder_.dispose();
            top3UnionsBuilder_ = null;
            top3Unions_ = other.top3Unions_;
            bitField0_ = (bitField0_ & ~0x00000008);
            top3UnionsBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetTop3UnionsFieldBuilder() : null;
          } else {
            top3UnionsBuilder_.addAllMessages(other.top3Unions_);
          }
        }
      }
      if (other.hasHasWorship()) {
        setHasWorship(other.getHasWorship());
      }
      if (other.hasPosition()) {
        setPosition(other.getPosition());
      }
      if (other.hasUnionId()) {
        setUnionId(other.getUnionId());
      }
      if (other.hasApplyRanking()) {
        setApplyRanking(other.getApplyRanking());
      }
      if (roundGroupsBuilder_ == null) {
        if (!other.roundGroups_.isEmpty()) {
          if (roundGroups_.isEmpty()) {
            roundGroups_ = other.roundGroups_;
            bitField0_ = (bitField0_ & ~0x00000100);
          } else {
            ensureRoundGroupsIsMutable();
            roundGroups_.addAll(other.roundGroups_);
          }
          onChanged();
        }
      } else {
        if (!other.roundGroups_.isEmpty()) {
          if (roundGroupsBuilder_.isEmpty()) {
            roundGroupsBuilder_.dispose();
            roundGroupsBuilder_ = null;
            roundGroups_ = other.roundGroups_;
            bitField0_ = (bitField0_ & ~0x00000100);
            roundGroupsBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetRoundGroupsFieldBuilder() : null;
          } else {
            roundGroupsBuilder_.addAllMessages(other.roundGroups_);
          }
        }
      }
      if (other.hasSettleMsg()) {
        mergeSettleMsg(other.getSettleMsg());
      }
      if (other.hasRoomId()) {
        setRoomId(other.getRoomId());
      }
      if (showStageUnionBuilder_ == null) {
        if (!other.showStageUnion_.isEmpty()) {
          if (showStageUnion_.isEmpty()) {
            showStageUnion_ = other.showStageUnion_;
            bitField0_ = (bitField0_ & ~0x00000800);
          } else {
            ensureShowStageUnionIsMutable();
            showStageUnion_.addAll(other.showStageUnion_);
          }
          onChanged();
        }
      } else {
        if (!other.showStageUnion_.isEmpty()) {
          if (showStageUnionBuilder_.isEmpty()) {
            showStageUnionBuilder_.dispose();
            showStageUnionBuilder_ = null;
            showStageUnion_ = other.showStageUnion_;
            bitField0_ = (bitField0_ & ~0x00000800);
            showStageUnionBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetShowStageUnionFieldBuilder() : null;
          } else {
            showStageUnionBuilder_.addAllMessages(other.showStageUnion_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      if (hasWorshipPlayer()) {
        if (!getWorshipPlayer().isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              group_ = input.readInt32();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 26: {
              input.readMessage(
                  internalGetWorshipPlayerFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 34: {
              xddq.pb.PeakFightUnionBaseMsg m =
                  input.readMessage(
                      xddq.pb.PeakFightUnionBaseMsg.parser(),
                      extensionRegistry);
              if (top3UnionsBuilder_ == null) {
                ensureTop3UnionsIsMutable();
                top3Unions_.add(m);
              } else {
                top3UnionsBuilder_.addMessage(m);
              }
              break;
            } // case 34
            case 40: {
              hasWorship_ = input.readBool();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            case 48: {
              position_ = input.readInt32();
              bitField0_ |= 0x00000020;
              break;
            } // case 48
            case 56: {
              unionId_ = input.readInt64();
              bitField0_ |= 0x00000040;
              break;
            } // case 56
            case 64: {
              applyRanking_ = input.readInt32();
              bitField0_ |= 0x00000080;
              break;
            } // case 64
            case 74: {
              xddq.pb.PeakFightRoundGroupInfo m =
                  input.readMessage(
                      xddq.pb.PeakFightRoundGroupInfo.parser(),
                      extensionRegistry);
              if (roundGroupsBuilder_ == null) {
                ensureRoundGroupsIsMutable();
                roundGroups_.add(m);
              } else {
                roundGroupsBuilder_.addMessage(m);
              }
              break;
            } // case 74
            case 82: {
              input.readMessage(
                  internalGetSettleMsgFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000200;
              break;
            } // case 82
            case 88: {
              roomId_ = input.readInt32();
              bitField0_ |= 0x00000400;
              break;
            } // case 88
            case 98: {
              xddq.pb.PeakFightUnionBaseMsg m =
                  input.readMessage(
                      xddq.pb.PeakFightUnionBaseMsg.parser(),
                      extensionRegistry);
              if (showStageUnionBuilder_ == null) {
                ensureShowStageUnionIsMutable();
                showStageUnion_.add(m);
              } else {
                showStageUnionBuilder_.addMessage(m);
              }
              break;
            } // case 98
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private int group_ ;
    /**
     * <code>optional int32 group = 2;</code>
     * @return Whether the group field is set.
     */
    @java.lang.Override
    public boolean hasGroup() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 group = 2;</code>
     * @return The group.
     */
    @java.lang.Override
    public int getGroup() {
      return group_;
    }
    /**
     * <code>optional int32 group = 2;</code>
     * @param value The group to set.
     * @return This builder for chaining.
     */
    public Builder setGroup(int value) {

      group_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 group = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearGroup() {
      bitField0_ = (bitField0_ & ~0x00000002);
      group_ = 0;
      onChanged();
      return this;
    }

    private xddq.pb.PalacePlayerShowMsg worshipPlayer_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PalacePlayerShowMsg, xddq.pb.PalacePlayerShowMsg.Builder, xddq.pb.PalacePlayerShowMsgOrBuilder> worshipPlayerBuilder_;
    /**
     * <code>optional .xddq.pb.PalacePlayerShowMsg worshipPlayer = 3;</code>
     * @return Whether the worshipPlayer field is set.
     */
    public boolean hasWorshipPlayer() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional .xddq.pb.PalacePlayerShowMsg worshipPlayer = 3;</code>
     * @return The worshipPlayer.
     */
    public xddq.pb.PalacePlayerShowMsg getWorshipPlayer() {
      if (worshipPlayerBuilder_ == null) {
        return worshipPlayer_ == null ? xddq.pb.PalacePlayerShowMsg.getDefaultInstance() : worshipPlayer_;
      } else {
        return worshipPlayerBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.PalacePlayerShowMsg worshipPlayer = 3;</code>
     */
    public Builder setWorshipPlayer(xddq.pb.PalacePlayerShowMsg value) {
      if (worshipPlayerBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        worshipPlayer_ = value;
      } else {
        worshipPlayerBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PalacePlayerShowMsg worshipPlayer = 3;</code>
     */
    public Builder setWorshipPlayer(
        xddq.pb.PalacePlayerShowMsg.Builder builderForValue) {
      if (worshipPlayerBuilder_ == null) {
        worshipPlayer_ = builderForValue.build();
      } else {
        worshipPlayerBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PalacePlayerShowMsg worshipPlayer = 3;</code>
     */
    public Builder mergeWorshipPlayer(xddq.pb.PalacePlayerShowMsg value) {
      if (worshipPlayerBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0) &&
          worshipPlayer_ != null &&
          worshipPlayer_ != xddq.pb.PalacePlayerShowMsg.getDefaultInstance()) {
          getWorshipPlayerBuilder().mergeFrom(value);
        } else {
          worshipPlayer_ = value;
        }
      } else {
        worshipPlayerBuilder_.mergeFrom(value);
      }
      if (worshipPlayer_ != null) {
        bitField0_ |= 0x00000004;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.PalacePlayerShowMsg worshipPlayer = 3;</code>
     */
    public Builder clearWorshipPlayer() {
      bitField0_ = (bitField0_ & ~0x00000004);
      worshipPlayer_ = null;
      if (worshipPlayerBuilder_ != null) {
        worshipPlayerBuilder_.dispose();
        worshipPlayerBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PalacePlayerShowMsg worshipPlayer = 3;</code>
     */
    public xddq.pb.PalacePlayerShowMsg.Builder getWorshipPlayerBuilder() {
      bitField0_ |= 0x00000004;
      onChanged();
      return internalGetWorshipPlayerFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.PalacePlayerShowMsg worshipPlayer = 3;</code>
     */
    public xddq.pb.PalacePlayerShowMsgOrBuilder getWorshipPlayerOrBuilder() {
      if (worshipPlayerBuilder_ != null) {
        return worshipPlayerBuilder_.getMessageOrBuilder();
      } else {
        return worshipPlayer_ == null ?
            xddq.pb.PalacePlayerShowMsg.getDefaultInstance() : worshipPlayer_;
      }
    }
    /**
     * <code>optional .xddq.pb.PalacePlayerShowMsg worshipPlayer = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PalacePlayerShowMsg, xddq.pb.PalacePlayerShowMsg.Builder, xddq.pb.PalacePlayerShowMsgOrBuilder> 
        internalGetWorshipPlayerFieldBuilder() {
      if (worshipPlayerBuilder_ == null) {
        worshipPlayerBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.PalacePlayerShowMsg, xddq.pb.PalacePlayerShowMsg.Builder, xddq.pb.PalacePlayerShowMsgOrBuilder>(
                getWorshipPlayer(),
                getParentForChildren(),
                isClean());
        worshipPlayer_ = null;
      }
      return worshipPlayerBuilder_;
    }

    private java.util.List<xddq.pb.PeakFightUnionBaseMsg> top3Unions_ =
      java.util.Collections.emptyList();
    private void ensureTop3UnionsIsMutable() {
      if (!((bitField0_ & 0x00000008) != 0)) {
        top3Unions_ = new java.util.ArrayList<xddq.pb.PeakFightUnionBaseMsg>(top3Unions_);
        bitField0_ |= 0x00000008;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.PeakFightUnionBaseMsg, xddq.pb.PeakFightUnionBaseMsg.Builder, xddq.pb.PeakFightUnionBaseMsgOrBuilder> top3UnionsBuilder_;

    /**
     * <code>repeated .xddq.pb.PeakFightUnionBaseMsg top3Unions = 4;</code>
     */
    public java.util.List<xddq.pb.PeakFightUnionBaseMsg> getTop3UnionsList() {
      if (top3UnionsBuilder_ == null) {
        return java.util.Collections.unmodifiableList(top3Unions_);
      } else {
        return top3UnionsBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.PeakFightUnionBaseMsg top3Unions = 4;</code>
     */
    public int getTop3UnionsCount() {
      if (top3UnionsBuilder_ == null) {
        return top3Unions_.size();
      } else {
        return top3UnionsBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.PeakFightUnionBaseMsg top3Unions = 4;</code>
     */
    public xddq.pb.PeakFightUnionBaseMsg getTop3Unions(int index) {
      if (top3UnionsBuilder_ == null) {
        return top3Unions_.get(index);
      } else {
        return top3UnionsBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.PeakFightUnionBaseMsg top3Unions = 4;</code>
     */
    public Builder setTop3Unions(
        int index, xddq.pb.PeakFightUnionBaseMsg value) {
      if (top3UnionsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureTop3UnionsIsMutable();
        top3Unions_.set(index, value);
        onChanged();
      } else {
        top3UnionsBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PeakFightUnionBaseMsg top3Unions = 4;</code>
     */
    public Builder setTop3Unions(
        int index, xddq.pb.PeakFightUnionBaseMsg.Builder builderForValue) {
      if (top3UnionsBuilder_ == null) {
        ensureTop3UnionsIsMutable();
        top3Unions_.set(index, builderForValue.build());
        onChanged();
      } else {
        top3UnionsBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PeakFightUnionBaseMsg top3Unions = 4;</code>
     */
    public Builder addTop3Unions(xddq.pb.PeakFightUnionBaseMsg value) {
      if (top3UnionsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureTop3UnionsIsMutable();
        top3Unions_.add(value);
        onChanged();
      } else {
        top3UnionsBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PeakFightUnionBaseMsg top3Unions = 4;</code>
     */
    public Builder addTop3Unions(
        int index, xddq.pb.PeakFightUnionBaseMsg value) {
      if (top3UnionsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureTop3UnionsIsMutable();
        top3Unions_.add(index, value);
        onChanged();
      } else {
        top3UnionsBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PeakFightUnionBaseMsg top3Unions = 4;</code>
     */
    public Builder addTop3Unions(
        xddq.pb.PeakFightUnionBaseMsg.Builder builderForValue) {
      if (top3UnionsBuilder_ == null) {
        ensureTop3UnionsIsMutable();
        top3Unions_.add(builderForValue.build());
        onChanged();
      } else {
        top3UnionsBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PeakFightUnionBaseMsg top3Unions = 4;</code>
     */
    public Builder addTop3Unions(
        int index, xddq.pb.PeakFightUnionBaseMsg.Builder builderForValue) {
      if (top3UnionsBuilder_ == null) {
        ensureTop3UnionsIsMutable();
        top3Unions_.add(index, builderForValue.build());
        onChanged();
      } else {
        top3UnionsBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PeakFightUnionBaseMsg top3Unions = 4;</code>
     */
    public Builder addAllTop3Unions(
        java.lang.Iterable<? extends xddq.pb.PeakFightUnionBaseMsg> values) {
      if (top3UnionsBuilder_ == null) {
        ensureTop3UnionsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, top3Unions_);
        onChanged();
      } else {
        top3UnionsBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PeakFightUnionBaseMsg top3Unions = 4;</code>
     */
    public Builder clearTop3Unions() {
      if (top3UnionsBuilder_ == null) {
        top3Unions_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000008);
        onChanged();
      } else {
        top3UnionsBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PeakFightUnionBaseMsg top3Unions = 4;</code>
     */
    public Builder removeTop3Unions(int index) {
      if (top3UnionsBuilder_ == null) {
        ensureTop3UnionsIsMutable();
        top3Unions_.remove(index);
        onChanged();
      } else {
        top3UnionsBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PeakFightUnionBaseMsg top3Unions = 4;</code>
     */
    public xddq.pb.PeakFightUnionBaseMsg.Builder getTop3UnionsBuilder(
        int index) {
      return internalGetTop3UnionsFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.PeakFightUnionBaseMsg top3Unions = 4;</code>
     */
    public xddq.pb.PeakFightUnionBaseMsgOrBuilder getTop3UnionsOrBuilder(
        int index) {
      if (top3UnionsBuilder_ == null) {
        return top3Unions_.get(index);  } else {
        return top3UnionsBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.PeakFightUnionBaseMsg top3Unions = 4;</code>
     */
    public java.util.List<? extends xddq.pb.PeakFightUnionBaseMsgOrBuilder> 
         getTop3UnionsOrBuilderList() {
      if (top3UnionsBuilder_ != null) {
        return top3UnionsBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(top3Unions_);
      }
    }
    /**
     * <code>repeated .xddq.pb.PeakFightUnionBaseMsg top3Unions = 4;</code>
     */
    public xddq.pb.PeakFightUnionBaseMsg.Builder addTop3UnionsBuilder() {
      return internalGetTop3UnionsFieldBuilder().addBuilder(
          xddq.pb.PeakFightUnionBaseMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.PeakFightUnionBaseMsg top3Unions = 4;</code>
     */
    public xddq.pb.PeakFightUnionBaseMsg.Builder addTop3UnionsBuilder(
        int index) {
      return internalGetTop3UnionsFieldBuilder().addBuilder(
          index, xddq.pb.PeakFightUnionBaseMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.PeakFightUnionBaseMsg top3Unions = 4;</code>
     */
    public java.util.List<xddq.pb.PeakFightUnionBaseMsg.Builder> 
         getTop3UnionsBuilderList() {
      return internalGetTop3UnionsFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.PeakFightUnionBaseMsg, xddq.pb.PeakFightUnionBaseMsg.Builder, xddq.pb.PeakFightUnionBaseMsgOrBuilder> 
        internalGetTop3UnionsFieldBuilder() {
      if (top3UnionsBuilder_ == null) {
        top3UnionsBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.PeakFightUnionBaseMsg, xddq.pb.PeakFightUnionBaseMsg.Builder, xddq.pb.PeakFightUnionBaseMsgOrBuilder>(
                top3Unions_,
                ((bitField0_ & 0x00000008) != 0),
                getParentForChildren(),
                isClean());
        top3Unions_ = null;
      }
      return top3UnionsBuilder_;
    }

    private boolean hasWorship_ ;
    /**
     * <code>optional bool hasWorship = 5;</code>
     * @return Whether the hasWorship field is set.
     */
    @java.lang.Override
    public boolean hasHasWorship() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional bool hasWorship = 5;</code>
     * @return The hasWorship.
     */
    @java.lang.Override
    public boolean getHasWorship() {
      return hasWorship_;
    }
    /**
     * <code>optional bool hasWorship = 5;</code>
     * @param value The hasWorship to set.
     * @return This builder for chaining.
     */
    public Builder setHasWorship(boolean value) {

      hasWorship_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional bool hasWorship = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearHasWorship() {
      bitField0_ = (bitField0_ & ~0x00000010);
      hasWorship_ = false;
      onChanged();
      return this;
    }

    private int position_ ;
    /**
     * <code>optional int32 position = 6;</code>
     * @return Whether the position field is set.
     */
    @java.lang.Override
    public boolean hasPosition() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional int32 position = 6;</code>
     * @return The position.
     */
    @java.lang.Override
    public int getPosition() {
      return position_;
    }
    /**
     * <code>optional int32 position = 6;</code>
     * @param value The position to set.
     * @return This builder for chaining.
     */
    public Builder setPosition(int value) {

      position_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 position = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearPosition() {
      bitField0_ = (bitField0_ & ~0x00000020);
      position_ = 0;
      onChanged();
      return this;
    }

    private long unionId_ ;
    /**
     * <code>optional int64 unionId = 7;</code>
     * @return Whether the unionId field is set.
     */
    @java.lang.Override
    public boolean hasUnionId() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <code>optional int64 unionId = 7;</code>
     * @return The unionId.
     */
    @java.lang.Override
    public long getUnionId() {
      return unionId_;
    }
    /**
     * <code>optional int64 unionId = 7;</code>
     * @param value The unionId to set.
     * @return This builder for chaining.
     */
    public Builder setUnionId(long value) {

      unionId_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 unionId = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearUnionId() {
      bitField0_ = (bitField0_ & ~0x00000040);
      unionId_ = 0L;
      onChanged();
      return this;
    }

    private int applyRanking_ ;
    /**
     * <code>optional int32 applyRanking = 8;</code>
     * @return Whether the applyRanking field is set.
     */
    @java.lang.Override
    public boolean hasApplyRanking() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <code>optional int32 applyRanking = 8;</code>
     * @return The applyRanking.
     */
    @java.lang.Override
    public int getApplyRanking() {
      return applyRanking_;
    }
    /**
     * <code>optional int32 applyRanking = 8;</code>
     * @param value The applyRanking to set.
     * @return This builder for chaining.
     */
    public Builder setApplyRanking(int value) {

      applyRanking_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 applyRanking = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearApplyRanking() {
      bitField0_ = (bitField0_ & ~0x00000080);
      applyRanking_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.PeakFightRoundGroupInfo> roundGroups_ =
      java.util.Collections.emptyList();
    private void ensureRoundGroupsIsMutable() {
      if (!((bitField0_ & 0x00000100) != 0)) {
        roundGroups_ = new java.util.ArrayList<xddq.pb.PeakFightRoundGroupInfo>(roundGroups_);
        bitField0_ |= 0x00000100;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.PeakFightRoundGroupInfo, xddq.pb.PeakFightRoundGroupInfo.Builder, xddq.pb.PeakFightRoundGroupInfoOrBuilder> roundGroupsBuilder_;

    /**
     * <code>repeated .xddq.pb.PeakFightRoundGroupInfo roundGroups = 9;</code>
     */
    public java.util.List<xddq.pb.PeakFightRoundGroupInfo> getRoundGroupsList() {
      if (roundGroupsBuilder_ == null) {
        return java.util.Collections.unmodifiableList(roundGroups_);
      } else {
        return roundGroupsBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.PeakFightRoundGroupInfo roundGroups = 9;</code>
     */
    public int getRoundGroupsCount() {
      if (roundGroupsBuilder_ == null) {
        return roundGroups_.size();
      } else {
        return roundGroupsBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.PeakFightRoundGroupInfo roundGroups = 9;</code>
     */
    public xddq.pb.PeakFightRoundGroupInfo getRoundGroups(int index) {
      if (roundGroupsBuilder_ == null) {
        return roundGroups_.get(index);
      } else {
        return roundGroupsBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.PeakFightRoundGroupInfo roundGroups = 9;</code>
     */
    public Builder setRoundGroups(
        int index, xddq.pb.PeakFightRoundGroupInfo value) {
      if (roundGroupsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRoundGroupsIsMutable();
        roundGroups_.set(index, value);
        onChanged();
      } else {
        roundGroupsBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PeakFightRoundGroupInfo roundGroups = 9;</code>
     */
    public Builder setRoundGroups(
        int index, xddq.pb.PeakFightRoundGroupInfo.Builder builderForValue) {
      if (roundGroupsBuilder_ == null) {
        ensureRoundGroupsIsMutable();
        roundGroups_.set(index, builderForValue.build());
        onChanged();
      } else {
        roundGroupsBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PeakFightRoundGroupInfo roundGroups = 9;</code>
     */
    public Builder addRoundGroups(xddq.pb.PeakFightRoundGroupInfo value) {
      if (roundGroupsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRoundGroupsIsMutable();
        roundGroups_.add(value);
        onChanged();
      } else {
        roundGroupsBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PeakFightRoundGroupInfo roundGroups = 9;</code>
     */
    public Builder addRoundGroups(
        int index, xddq.pb.PeakFightRoundGroupInfo value) {
      if (roundGroupsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRoundGroupsIsMutable();
        roundGroups_.add(index, value);
        onChanged();
      } else {
        roundGroupsBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PeakFightRoundGroupInfo roundGroups = 9;</code>
     */
    public Builder addRoundGroups(
        xddq.pb.PeakFightRoundGroupInfo.Builder builderForValue) {
      if (roundGroupsBuilder_ == null) {
        ensureRoundGroupsIsMutable();
        roundGroups_.add(builderForValue.build());
        onChanged();
      } else {
        roundGroupsBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PeakFightRoundGroupInfo roundGroups = 9;</code>
     */
    public Builder addRoundGroups(
        int index, xddq.pb.PeakFightRoundGroupInfo.Builder builderForValue) {
      if (roundGroupsBuilder_ == null) {
        ensureRoundGroupsIsMutable();
        roundGroups_.add(index, builderForValue.build());
        onChanged();
      } else {
        roundGroupsBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PeakFightRoundGroupInfo roundGroups = 9;</code>
     */
    public Builder addAllRoundGroups(
        java.lang.Iterable<? extends xddq.pb.PeakFightRoundGroupInfo> values) {
      if (roundGroupsBuilder_ == null) {
        ensureRoundGroupsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, roundGroups_);
        onChanged();
      } else {
        roundGroupsBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PeakFightRoundGroupInfo roundGroups = 9;</code>
     */
    public Builder clearRoundGroups() {
      if (roundGroupsBuilder_ == null) {
        roundGroups_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000100);
        onChanged();
      } else {
        roundGroupsBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PeakFightRoundGroupInfo roundGroups = 9;</code>
     */
    public Builder removeRoundGroups(int index) {
      if (roundGroupsBuilder_ == null) {
        ensureRoundGroupsIsMutable();
        roundGroups_.remove(index);
        onChanged();
      } else {
        roundGroupsBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PeakFightRoundGroupInfo roundGroups = 9;</code>
     */
    public xddq.pb.PeakFightRoundGroupInfo.Builder getRoundGroupsBuilder(
        int index) {
      return internalGetRoundGroupsFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.PeakFightRoundGroupInfo roundGroups = 9;</code>
     */
    public xddq.pb.PeakFightRoundGroupInfoOrBuilder getRoundGroupsOrBuilder(
        int index) {
      if (roundGroupsBuilder_ == null) {
        return roundGroups_.get(index);  } else {
        return roundGroupsBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.PeakFightRoundGroupInfo roundGroups = 9;</code>
     */
    public java.util.List<? extends xddq.pb.PeakFightRoundGroupInfoOrBuilder> 
         getRoundGroupsOrBuilderList() {
      if (roundGroupsBuilder_ != null) {
        return roundGroupsBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(roundGroups_);
      }
    }
    /**
     * <code>repeated .xddq.pb.PeakFightRoundGroupInfo roundGroups = 9;</code>
     */
    public xddq.pb.PeakFightRoundGroupInfo.Builder addRoundGroupsBuilder() {
      return internalGetRoundGroupsFieldBuilder().addBuilder(
          xddq.pb.PeakFightRoundGroupInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.PeakFightRoundGroupInfo roundGroups = 9;</code>
     */
    public xddq.pb.PeakFightRoundGroupInfo.Builder addRoundGroupsBuilder(
        int index) {
      return internalGetRoundGroupsFieldBuilder().addBuilder(
          index, xddq.pb.PeakFightRoundGroupInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.PeakFightRoundGroupInfo roundGroups = 9;</code>
     */
    public java.util.List<xddq.pb.PeakFightRoundGroupInfo.Builder> 
         getRoundGroupsBuilderList() {
      return internalGetRoundGroupsFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.PeakFightRoundGroupInfo, xddq.pb.PeakFightRoundGroupInfo.Builder, xddq.pb.PeakFightRoundGroupInfoOrBuilder> 
        internalGetRoundGroupsFieldBuilder() {
      if (roundGroupsBuilder_ == null) {
        roundGroupsBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.PeakFightRoundGroupInfo, xddq.pb.PeakFightRoundGroupInfo.Builder, xddq.pb.PeakFightRoundGroupInfoOrBuilder>(
                roundGroups_,
                ((bitField0_ & 0x00000100) != 0),
                getParentForChildren(),
                isClean());
        roundGroups_ = null;
      }
      return roundGroupsBuilder_;
    }

    private xddq.pb.PeakFightSettleMsg settleMsg_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PeakFightSettleMsg, xddq.pb.PeakFightSettleMsg.Builder, xddq.pb.PeakFightSettleMsgOrBuilder> settleMsgBuilder_;
    /**
     * <code>optional .xddq.pb.PeakFightSettleMsg settleMsg = 10;</code>
     * @return Whether the settleMsg field is set.
     */
    public boolean hasSettleMsg() {
      return ((bitField0_ & 0x00000200) != 0);
    }
    /**
     * <code>optional .xddq.pb.PeakFightSettleMsg settleMsg = 10;</code>
     * @return The settleMsg.
     */
    public xddq.pb.PeakFightSettleMsg getSettleMsg() {
      if (settleMsgBuilder_ == null) {
        return settleMsg_ == null ? xddq.pb.PeakFightSettleMsg.getDefaultInstance() : settleMsg_;
      } else {
        return settleMsgBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.PeakFightSettleMsg settleMsg = 10;</code>
     */
    public Builder setSettleMsg(xddq.pb.PeakFightSettleMsg value) {
      if (settleMsgBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        settleMsg_ = value;
      } else {
        settleMsgBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000200;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PeakFightSettleMsg settleMsg = 10;</code>
     */
    public Builder setSettleMsg(
        xddq.pb.PeakFightSettleMsg.Builder builderForValue) {
      if (settleMsgBuilder_ == null) {
        settleMsg_ = builderForValue.build();
      } else {
        settleMsgBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000200;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PeakFightSettleMsg settleMsg = 10;</code>
     */
    public Builder mergeSettleMsg(xddq.pb.PeakFightSettleMsg value) {
      if (settleMsgBuilder_ == null) {
        if (((bitField0_ & 0x00000200) != 0) &&
          settleMsg_ != null &&
          settleMsg_ != xddq.pb.PeakFightSettleMsg.getDefaultInstance()) {
          getSettleMsgBuilder().mergeFrom(value);
        } else {
          settleMsg_ = value;
        }
      } else {
        settleMsgBuilder_.mergeFrom(value);
      }
      if (settleMsg_ != null) {
        bitField0_ |= 0x00000200;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.PeakFightSettleMsg settleMsg = 10;</code>
     */
    public Builder clearSettleMsg() {
      bitField0_ = (bitField0_ & ~0x00000200);
      settleMsg_ = null;
      if (settleMsgBuilder_ != null) {
        settleMsgBuilder_.dispose();
        settleMsgBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PeakFightSettleMsg settleMsg = 10;</code>
     */
    public xddq.pb.PeakFightSettleMsg.Builder getSettleMsgBuilder() {
      bitField0_ |= 0x00000200;
      onChanged();
      return internalGetSettleMsgFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.PeakFightSettleMsg settleMsg = 10;</code>
     */
    public xddq.pb.PeakFightSettleMsgOrBuilder getSettleMsgOrBuilder() {
      if (settleMsgBuilder_ != null) {
        return settleMsgBuilder_.getMessageOrBuilder();
      } else {
        return settleMsg_ == null ?
            xddq.pb.PeakFightSettleMsg.getDefaultInstance() : settleMsg_;
      }
    }
    /**
     * <code>optional .xddq.pb.PeakFightSettleMsg settleMsg = 10;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PeakFightSettleMsg, xddq.pb.PeakFightSettleMsg.Builder, xddq.pb.PeakFightSettleMsgOrBuilder> 
        internalGetSettleMsgFieldBuilder() {
      if (settleMsgBuilder_ == null) {
        settleMsgBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.PeakFightSettleMsg, xddq.pb.PeakFightSettleMsg.Builder, xddq.pb.PeakFightSettleMsgOrBuilder>(
                getSettleMsg(),
                getParentForChildren(),
                isClean());
        settleMsg_ = null;
      }
      return settleMsgBuilder_;
    }

    private int roomId_ ;
    /**
     * <code>optional int32 roomId = 11;</code>
     * @return Whether the roomId field is set.
     */
    @java.lang.Override
    public boolean hasRoomId() {
      return ((bitField0_ & 0x00000400) != 0);
    }
    /**
     * <code>optional int32 roomId = 11;</code>
     * @return The roomId.
     */
    @java.lang.Override
    public int getRoomId() {
      return roomId_;
    }
    /**
     * <code>optional int32 roomId = 11;</code>
     * @param value The roomId to set.
     * @return This builder for chaining.
     */
    public Builder setRoomId(int value) {

      roomId_ = value;
      bitField0_ |= 0x00000400;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 roomId = 11;</code>
     * @return This builder for chaining.
     */
    public Builder clearRoomId() {
      bitField0_ = (bitField0_ & ~0x00000400);
      roomId_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.PeakFightUnionBaseMsg> showStageUnion_ =
      java.util.Collections.emptyList();
    private void ensureShowStageUnionIsMutable() {
      if (!((bitField0_ & 0x00000800) != 0)) {
        showStageUnion_ = new java.util.ArrayList<xddq.pb.PeakFightUnionBaseMsg>(showStageUnion_);
        bitField0_ |= 0x00000800;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.PeakFightUnionBaseMsg, xddq.pb.PeakFightUnionBaseMsg.Builder, xddq.pb.PeakFightUnionBaseMsgOrBuilder> showStageUnionBuilder_;

    /**
     * <code>repeated .xddq.pb.PeakFightUnionBaseMsg showStageUnion = 12;</code>
     */
    public java.util.List<xddq.pb.PeakFightUnionBaseMsg> getShowStageUnionList() {
      if (showStageUnionBuilder_ == null) {
        return java.util.Collections.unmodifiableList(showStageUnion_);
      } else {
        return showStageUnionBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.PeakFightUnionBaseMsg showStageUnion = 12;</code>
     */
    public int getShowStageUnionCount() {
      if (showStageUnionBuilder_ == null) {
        return showStageUnion_.size();
      } else {
        return showStageUnionBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.PeakFightUnionBaseMsg showStageUnion = 12;</code>
     */
    public xddq.pb.PeakFightUnionBaseMsg getShowStageUnion(int index) {
      if (showStageUnionBuilder_ == null) {
        return showStageUnion_.get(index);
      } else {
        return showStageUnionBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.PeakFightUnionBaseMsg showStageUnion = 12;</code>
     */
    public Builder setShowStageUnion(
        int index, xddq.pb.PeakFightUnionBaseMsg value) {
      if (showStageUnionBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureShowStageUnionIsMutable();
        showStageUnion_.set(index, value);
        onChanged();
      } else {
        showStageUnionBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PeakFightUnionBaseMsg showStageUnion = 12;</code>
     */
    public Builder setShowStageUnion(
        int index, xddq.pb.PeakFightUnionBaseMsg.Builder builderForValue) {
      if (showStageUnionBuilder_ == null) {
        ensureShowStageUnionIsMutable();
        showStageUnion_.set(index, builderForValue.build());
        onChanged();
      } else {
        showStageUnionBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PeakFightUnionBaseMsg showStageUnion = 12;</code>
     */
    public Builder addShowStageUnion(xddq.pb.PeakFightUnionBaseMsg value) {
      if (showStageUnionBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureShowStageUnionIsMutable();
        showStageUnion_.add(value);
        onChanged();
      } else {
        showStageUnionBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PeakFightUnionBaseMsg showStageUnion = 12;</code>
     */
    public Builder addShowStageUnion(
        int index, xddq.pb.PeakFightUnionBaseMsg value) {
      if (showStageUnionBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureShowStageUnionIsMutable();
        showStageUnion_.add(index, value);
        onChanged();
      } else {
        showStageUnionBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PeakFightUnionBaseMsg showStageUnion = 12;</code>
     */
    public Builder addShowStageUnion(
        xddq.pb.PeakFightUnionBaseMsg.Builder builderForValue) {
      if (showStageUnionBuilder_ == null) {
        ensureShowStageUnionIsMutable();
        showStageUnion_.add(builderForValue.build());
        onChanged();
      } else {
        showStageUnionBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PeakFightUnionBaseMsg showStageUnion = 12;</code>
     */
    public Builder addShowStageUnion(
        int index, xddq.pb.PeakFightUnionBaseMsg.Builder builderForValue) {
      if (showStageUnionBuilder_ == null) {
        ensureShowStageUnionIsMutable();
        showStageUnion_.add(index, builderForValue.build());
        onChanged();
      } else {
        showStageUnionBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PeakFightUnionBaseMsg showStageUnion = 12;</code>
     */
    public Builder addAllShowStageUnion(
        java.lang.Iterable<? extends xddq.pb.PeakFightUnionBaseMsg> values) {
      if (showStageUnionBuilder_ == null) {
        ensureShowStageUnionIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, showStageUnion_);
        onChanged();
      } else {
        showStageUnionBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PeakFightUnionBaseMsg showStageUnion = 12;</code>
     */
    public Builder clearShowStageUnion() {
      if (showStageUnionBuilder_ == null) {
        showStageUnion_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000800);
        onChanged();
      } else {
        showStageUnionBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PeakFightUnionBaseMsg showStageUnion = 12;</code>
     */
    public Builder removeShowStageUnion(int index) {
      if (showStageUnionBuilder_ == null) {
        ensureShowStageUnionIsMutable();
        showStageUnion_.remove(index);
        onChanged();
      } else {
        showStageUnionBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PeakFightUnionBaseMsg showStageUnion = 12;</code>
     */
    public xddq.pb.PeakFightUnionBaseMsg.Builder getShowStageUnionBuilder(
        int index) {
      return internalGetShowStageUnionFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.PeakFightUnionBaseMsg showStageUnion = 12;</code>
     */
    public xddq.pb.PeakFightUnionBaseMsgOrBuilder getShowStageUnionOrBuilder(
        int index) {
      if (showStageUnionBuilder_ == null) {
        return showStageUnion_.get(index);  } else {
        return showStageUnionBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.PeakFightUnionBaseMsg showStageUnion = 12;</code>
     */
    public java.util.List<? extends xddq.pb.PeakFightUnionBaseMsgOrBuilder> 
         getShowStageUnionOrBuilderList() {
      if (showStageUnionBuilder_ != null) {
        return showStageUnionBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(showStageUnion_);
      }
    }
    /**
     * <code>repeated .xddq.pb.PeakFightUnionBaseMsg showStageUnion = 12;</code>
     */
    public xddq.pb.PeakFightUnionBaseMsg.Builder addShowStageUnionBuilder() {
      return internalGetShowStageUnionFieldBuilder().addBuilder(
          xddq.pb.PeakFightUnionBaseMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.PeakFightUnionBaseMsg showStageUnion = 12;</code>
     */
    public xddq.pb.PeakFightUnionBaseMsg.Builder addShowStageUnionBuilder(
        int index) {
      return internalGetShowStageUnionFieldBuilder().addBuilder(
          index, xddq.pb.PeakFightUnionBaseMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.PeakFightUnionBaseMsg showStageUnion = 12;</code>
     */
    public java.util.List<xddq.pb.PeakFightUnionBaseMsg.Builder> 
         getShowStageUnionBuilderList() {
      return internalGetShowStageUnionFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.PeakFightUnionBaseMsg, xddq.pb.PeakFightUnionBaseMsg.Builder, xddq.pb.PeakFightUnionBaseMsgOrBuilder> 
        internalGetShowStageUnionFieldBuilder() {
      if (showStageUnionBuilder_ == null) {
        showStageUnionBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.PeakFightUnionBaseMsg, xddq.pb.PeakFightUnionBaseMsg.Builder, xddq.pb.PeakFightUnionBaseMsgOrBuilder>(
                showStageUnion_,
                ((bitField0_ & 0x00000800) != 0),
                getParentForChildren(),
                isClean());
        showStageUnion_ = null;
      }
      return showStageUnionBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.PeakFightEnterActivityResp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.PeakFightEnterActivityResp)
  private static final xddq.pb.PeakFightEnterActivityResp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.PeakFightEnterActivityResp();
  }

  public static xddq.pb.PeakFightEnterActivityResp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<PeakFightEnterActivityResp>
      PARSER = new com.google.protobuf.AbstractParser<PeakFightEnterActivityResp>() {
    @java.lang.Override
    public PeakFightEnterActivityResp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<PeakFightEnterActivityResp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<PeakFightEnterActivityResp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.PeakFightEnterActivityResp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

