// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.MarriageApplyTempMsg}
 */
public final class MarriageApplyTempMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.MarriageApplyTempMsg)
    MarriageApplyTempMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      MarriageApplyTempMsg.class.getName());
  }
  // Use MarriageApplyTempMsg.newBuilder() to construct.
  private MarriageApplyTempMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private MarriageApplyTempMsg() {
    playerNickName_ = "";
    type_ = 0;
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_MarriageApplyTempMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_MarriageApplyTempMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.MarriageApplyTempMsg.class, xddq.pb.MarriageApplyTempMsg.Builder.class);
  }

  private int bitField0_;
  public static final int PLAYERID_FIELD_NUMBER = 1;
  private long playerId_ = 0L;
  /**
   * <code>required int64 playerId = 1;</code>
   * @return Whether the playerId field is set.
   */
  @java.lang.Override
  public boolean hasPlayerId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int64 playerId = 1;</code>
   * @return The playerId.
   */
  @java.lang.Override
  public long getPlayerId() {
    return playerId_;
  }

  public static final int PLAYERNICKNAME_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private volatile java.lang.Object playerNickName_ = "";
  /**
   * <code>required string playerNickName = 2;</code>
   * @return Whether the playerNickName field is set.
   */
  @java.lang.Override
  public boolean hasPlayerNickName() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>required string playerNickName = 2;</code>
   * @return The playerNickName.
   */
  @java.lang.Override
  public java.lang.String getPlayerNickName() {
    java.lang.Object ref = playerNickName_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        playerNickName_ = s;
      }
      return s;
    }
  }
  /**
   * <code>required string playerNickName = 2;</code>
   * @return The bytes for playerNickName.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getPlayerNickNameBytes() {
    java.lang.Object ref = playerNickName_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      playerNickName_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PUPILINFO_FIELD_NUMBER = 3;
  private xddq.pb.PupilDataMsg pupilInfo_;
  /**
   * <code>required .xddq.pb.PupilDataMsg pupilInfo = 3;</code>
   * @return Whether the pupilInfo field is set.
   */
  @java.lang.Override
  public boolean hasPupilInfo() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>required .xddq.pb.PupilDataMsg pupilInfo = 3;</code>
   * @return The pupilInfo.
   */
  @java.lang.Override
  public xddq.pb.PupilDataMsg getPupilInfo() {
    return pupilInfo_ == null ? xddq.pb.PupilDataMsg.getDefaultInstance() : pupilInfo_;
  }
  /**
   * <code>required .xddq.pb.PupilDataMsg pupilInfo = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.PupilDataMsgOrBuilder getPupilInfoOrBuilder() {
    return pupilInfo_ == null ? xddq.pb.PupilDataMsg.getDefaultInstance() : pupilInfo_;
  }

  public static final int REQUESTMARRYTIME_FIELD_NUMBER = 4;
  private long requestMarryTime_ = 0L;
  /**
   * <code>required int64 requestMarryTime = 4;</code>
   * @return Whether the requestMarryTime field is set.
   */
  @java.lang.Override
  public boolean hasRequestMarryTime() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>required int64 requestMarryTime = 4;</code>
   * @return The requestMarryTime.
   */
  @java.lang.Override
  public long getRequestMarryTime() {
    return requestMarryTime_;
  }

  public static final int APPOINTUSERID_FIELD_NUMBER = 5;
  private long appointUserId_ = 0L;
  /**
   * <code>required int64 appointUserId = 5;</code>
   * @return Whether the appointUserId field is set.
   */
  @java.lang.Override
  public boolean hasAppointUserId() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>required int64 appointUserId = 5;</code>
   * @return The appointUserId.
   */
  @java.lang.Override
  public long getAppointUserId() {
    return appointUserId_;
  }

  public static final int TYPE_FIELD_NUMBER = 6;
  private int type_ = 0;
  /**
   * <code>required .xddq.pb.ApplyType type = 6;</code>
   * @return Whether the type field is set.
   */
  @java.lang.Override public boolean hasType() {
    return ((bitField0_ & 0x00000020) != 0);
  }
  /**
   * <code>required .xddq.pb.ApplyType type = 6;</code>
   * @return The type.
   */
  @java.lang.Override public xddq.pb.ApplyType getType() {
    xddq.pb.ApplyType result = xddq.pb.ApplyType.forNumber(type_);
    return result == null ? xddq.pb.ApplyType.UnApply : result;
  }

  public static final int SERVERID_FIELD_NUMBER = 7;
  private long serverId_ = 0L;
  /**
   * <code>optional int64 serverId = 7;</code>
   * @return Whether the serverId field is set.
   */
  @java.lang.Override
  public boolean hasServerId() {
    return ((bitField0_ & 0x00000040) != 0);
  }
  /**
   * <code>optional int64 serverId = 7;</code>
   * @return The serverId.
   */
  @java.lang.Override
  public long getServerId() {
    return serverId_;
  }

  public static final int LEVELLIMIT_FIELD_NUMBER = 8;
  private int levelLimit_ = 0;
  /**
   * <code>optional int32 levelLimit = 8;</code>
   * @return Whether the levelLimit field is set.
   */
  @java.lang.Override
  public boolean hasLevelLimit() {
    return ((bitField0_ & 0x00000080) != 0);
  }
  /**
   * <code>optional int32 levelLimit = 8;</code>
   * @return The levelLimit.
   */
  @java.lang.Override
  public int getLevelLimit() {
    return levelLimit_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasPlayerId()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasPlayerNickName()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasPupilInfo()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasRequestMarryTime()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasAppointUserId()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasType()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!getPupilInfo().isInitialized()) {
      memoizedIsInitialized = 0;
      return false;
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt64(1, playerId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 2, playerNickName_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeMessage(3, getPupilInfo());
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt64(4, requestMarryTime_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      output.writeInt64(5, appointUserId_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      output.writeEnum(6, type_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      output.writeInt64(7, serverId_);
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      output.writeInt32(8, levelLimit_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(1, playerId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(2, playerNickName_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, getPupilInfo());
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(4, requestMarryTime_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(5, appointUserId_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(6, type_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(7, serverId_);
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(8, levelLimit_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.MarriageApplyTempMsg)) {
      return super.equals(obj);
    }
    xddq.pb.MarriageApplyTempMsg other = (xddq.pb.MarriageApplyTempMsg) obj;

    if (hasPlayerId() != other.hasPlayerId()) return false;
    if (hasPlayerId()) {
      if (getPlayerId()
          != other.getPlayerId()) return false;
    }
    if (hasPlayerNickName() != other.hasPlayerNickName()) return false;
    if (hasPlayerNickName()) {
      if (!getPlayerNickName()
          .equals(other.getPlayerNickName())) return false;
    }
    if (hasPupilInfo() != other.hasPupilInfo()) return false;
    if (hasPupilInfo()) {
      if (!getPupilInfo()
          .equals(other.getPupilInfo())) return false;
    }
    if (hasRequestMarryTime() != other.hasRequestMarryTime()) return false;
    if (hasRequestMarryTime()) {
      if (getRequestMarryTime()
          != other.getRequestMarryTime()) return false;
    }
    if (hasAppointUserId() != other.hasAppointUserId()) return false;
    if (hasAppointUserId()) {
      if (getAppointUserId()
          != other.getAppointUserId()) return false;
    }
    if (hasType() != other.hasType()) return false;
    if (hasType()) {
      if (type_ != other.type_) return false;
    }
    if (hasServerId() != other.hasServerId()) return false;
    if (hasServerId()) {
      if (getServerId()
          != other.getServerId()) return false;
    }
    if (hasLevelLimit() != other.hasLevelLimit()) return false;
    if (hasLevelLimit()) {
      if (getLevelLimit()
          != other.getLevelLimit()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasPlayerId()) {
      hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getPlayerId());
    }
    if (hasPlayerNickName()) {
      hash = (37 * hash) + PLAYERNICKNAME_FIELD_NUMBER;
      hash = (53 * hash) + getPlayerNickName().hashCode();
    }
    if (hasPupilInfo()) {
      hash = (37 * hash) + PUPILINFO_FIELD_NUMBER;
      hash = (53 * hash) + getPupilInfo().hashCode();
    }
    if (hasRequestMarryTime()) {
      hash = (37 * hash) + REQUESTMARRYTIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getRequestMarryTime());
    }
    if (hasAppointUserId()) {
      hash = (37 * hash) + APPOINTUSERID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getAppointUserId());
    }
    if (hasType()) {
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + type_;
    }
    if (hasServerId()) {
      hash = (37 * hash) + SERVERID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getServerId());
    }
    if (hasLevelLimit()) {
      hash = (37 * hash) + LEVELLIMIT_FIELD_NUMBER;
      hash = (53 * hash) + getLevelLimit();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.MarriageApplyTempMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.MarriageApplyTempMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.MarriageApplyTempMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.MarriageApplyTempMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.MarriageApplyTempMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.MarriageApplyTempMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.MarriageApplyTempMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.MarriageApplyTempMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.MarriageApplyTempMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.MarriageApplyTempMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.MarriageApplyTempMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.MarriageApplyTempMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.MarriageApplyTempMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.MarriageApplyTempMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.MarriageApplyTempMsg)
      xddq.pb.MarriageApplyTempMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_MarriageApplyTempMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_MarriageApplyTempMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.MarriageApplyTempMsg.class, xddq.pb.MarriageApplyTempMsg.Builder.class);
    }

    // Construct using xddq.pb.MarriageApplyTempMsg.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetPupilInfoFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      playerId_ = 0L;
      playerNickName_ = "";
      pupilInfo_ = null;
      if (pupilInfoBuilder_ != null) {
        pupilInfoBuilder_.dispose();
        pupilInfoBuilder_ = null;
      }
      requestMarryTime_ = 0L;
      appointUserId_ = 0L;
      type_ = 0;
      serverId_ = 0L;
      levelLimit_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_MarriageApplyTempMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.MarriageApplyTempMsg getDefaultInstanceForType() {
      return xddq.pb.MarriageApplyTempMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.MarriageApplyTempMsg build() {
      xddq.pb.MarriageApplyTempMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.MarriageApplyTempMsg buildPartial() {
      xddq.pb.MarriageApplyTempMsg result = new xddq.pb.MarriageApplyTempMsg(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.MarriageApplyTempMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.playerId_ = playerId_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.playerNickName_ = playerNickName_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.pupilInfo_ = pupilInfoBuilder_ == null
            ? pupilInfo_
            : pupilInfoBuilder_.build();
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.requestMarryTime_ = requestMarryTime_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.appointUserId_ = appointUserId_;
        to_bitField0_ |= 0x00000010;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.type_ = type_;
        to_bitField0_ |= 0x00000020;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.serverId_ = serverId_;
        to_bitField0_ |= 0x00000040;
      }
      if (((from_bitField0_ & 0x00000080) != 0)) {
        result.levelLimit_ = levelLimit_;
        to_bitField0_ |= 0x00000080;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.MarriageApplyTempMsg) {
        return mergeFrom((xddq.pb.MarriageApplyTempMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.MarriageApplyTempMsg other) {
      if (other == xddq.pb.MarriageApplyTempMsg.getDefaultInstance()) return this;
      if (other.hasPlayerId()) {
        setPlayerId(other.getPlayerId());
      }
      if (other.hasPlayerNickName()) {
        playerNickName_ = other.playerNickName_;
        bitField0_ |= 0x00000002;
        onChanged();
      }
      if (other.hasPupilInfo()) {
        mergePupilInfo(other.getPupilInfo());
      }
      if (other.hasRequestMarryTime()) {
        setRequestMarryTime(other.getRequestMarryTime());
      }
      if (other.hasAppointUserId()) {
        setAppointUserId(other.getAppointUserId());
      }
      if (other.hasType()) {
        setType(other.getType());
      }
      if (other.hasServerId()) {
        setServerId(other.getServerId());
      }
      if (other.hasLevelLimit()) {
        setLevelLimit(other.getLevelLimit());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasPlayerId()) {
        return false;
      }
      if (!hasPlayerNickName()) {
        return false;
      }
      if (!hasPupilInfo()) {
        return false;
      }
      if (!hasRequestMarryTime()) {
        return false;
      }
      if (!hasAppointUserId()) {
        return false;
      }
      if (!hasType()) {
        return false;
      }
      if (!getPupilInfo().isInitialized()) {
        return false;
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              playerId_ = input.readInt64();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              playerNickName_ = input.readBytes();
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 26: {
              input.readMessage(
                  internalGetPupilInfoFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 32: {
              requestMarryTime_ = input.readInt64();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            case 40: {
              appointUserId_ = input.readInt64();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            case 48: {
              int tmpRaw = input.readEnum();
              xddq.pb.ApplyType tmpValue =
                  xddq.pb.ApplyType.forNumber(tmpRaw);
              if (tmpValue == null) {
                mergeUnknownVarintField(6, tmpRaw);
              } else {
                type_ = tmpRaw;
                bitField0_ |= 0x00000020;
              }
              break;
            } // case 48
            case 56: {
              serverId_ = input.readInt64();
              bitField0_ |= 0x00000040;
              break;
            } // case 56
            case 64: {
              levelLimit_ = input.readInt32();
              bitField0_ |= 0x00000080;
              break;
            } // case 64
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private long playerId_ ;
    /**
     * <code>required int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int64 playerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }
    /**
     * <code>required int64 playerId = 1;</code>
     * @param value The playerId to set.
     * @return This builder for chaining.
     */
    public Builder setPlayerId(long value) {

      playerId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int64 playerId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearPlayerId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      playerId_ = 0L;
      onChanged();
      return this;
    }

    private java.lang.Object playerNickName_ = "";
    /**
     * <code>required string playerNickName = 2;</code>
     * @return Whether the playerNickName field is set.
     */
    public boolean hasPlayerNickName() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>required string playerNickName = 2;</code>
     * @return The playerNickName.
     */
    public java.lang.String getPlayerNickName() {
      java.lang.Object ref = playerNickName_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          playerNickName_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>required string playerNickName = 2;</code>
     * @return The bytes for playerNickName.
     */
    public com.google.protobuf.ByteString
        getPlayerNickNameBytes() {
      java.lang.Object ref = playerNickName_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        playerNickName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>required string playerNickName = 2;</code>
     * @param value The playerNickName to set.
     * @return This builder for chaining.
     */
    public Builder setPlayerNickName(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      playerNickName_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>required string playerNickName = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearPlayerNickName() {
      playerNickName_ = getDefaultInstance().getPlayerNickName();
      bitField0_ = (bitField0_ & ~0x00000002);
      onChanged();
      return this;
    }
    /**
     * <code>required string playerNickName = 2;</code>
     * @param value The bytes for playerNickName to set.
     * @return This builder for chaining.
     */
    public Builder setPlayerNickNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      playerNickName_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }

    private xddq.pb.PupilDataMsg pupilInfo_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PupilDataMsg, xddq.pb.PupilDataMsg.Builder, xddq.pb.PupilDataMsgOrBuilder> pupilInfoBuilder_;
    /**
     * <code>required .xddq.pb.PupilDataMsg pupilInfo = 3;</code>
     * @return Whether the pupilInfo field is set.
     */
    public boolean hasPupilInfo() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>required .xddq.pb.PupilDataMsg pupilInfo = 3;</code>
     * @return The pupilInfo.
     */
    public xddq.pb.PupilDataMsg getPupilInfo() {
      if (pupilInfoBuilder_ == null) {
        return pupilInfo_ == null ? xddq.pb.PupilDataMsg.getDefaultInstance() : pupilInfo_;
      } else {
        return pupilInfoBuilder_.getMessage();
      }
    }
    /**
     * <code>required .xddq.pb.PupilDataMsg pupilInfo = 3;</code>
     */
    public Builder setPupilInfo(xddq.pb.PupilDataMsg value) {
      if (pupilInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        pupilInfo_ = value;
      } else {
        pupilInfoBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>required .xddq.pb.PupilDataMsg pupilInfo = 3;</code>
     */
    public Builder setPupilInfo(
        xddq.pb.PupilDataMsg.Builder builderForValue) {
      if (pupilInfoBuilder_ == null) {
        pupilInfo_ = builderForValue.build();
      } else {
        pupilInfoBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>required .xddq.pb.PupilDataMsg pupilInfo = 3;</code>
     */
    public Builder mergePupilInfo(xddq.pb.PupilDataMsg value) {
      if (pupilInfoBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0) &&
          pupilInfo_ != null &&
          pupilInfo_ != xddq.pb.PupilDataMsg.getDefaultInstance()) {
          getPupilInfoBuilder().mergeFrom(value);
        } else {
          pupilInfo_ = value;
        }
      } else {
        pupilInfoBuilder_.mergeFrom(value);
      }
      if (pupilInfo_ != null) {
        bitField0_ |= 0x00000004;
        onChanged();
      }
      return this;
    }
    /**
     * <code>required .xddq.pb.PupilDataMsg pupilInfo = 3;</code>
     */
    public Builder clearPupilInfo() {
      bitField0_ = (bitField0_ & ~0x00000004);
      pupilInfo_ = null;
      if (pupilInfoBuilder_ != null) {
        pupilInfoBuilder_.dispose();
        pupilInfoBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>required .xddq.pb.PupilDataMsg pupilInfo = 3;</code>
     */
    public xddq.pb.PupilDataMsg.Builder getPupilInfoBuilder() {
      bitField0_ |= 0x00000004;
      onChanged();
      return internalGetPupilInfoFieldBuilder().getBuilder();
    }
    /**
     * <code>required .xddq.pb.PupilDataMsg pupilInfo = 3;</code>
     */
    public xddq.pb.PupilDataMsgOrBuilder getPupilInfoOrBuilder() {
      if (pupilInfoBuilder_ != null) {
        return pupilInfoBuilder_.getMessageOrBuilder();
      } else {
        return pupilInfo_ == null ?
            xddq.pb.PupilDataMsg.getDefaultInstance() : pupilInfo_;
      }
    }
    /**
     * <code>required .xddq.pb.PupilDataMsg pupilInfo = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PupilDataMsg, xddq.pb.PupilDataMsg.Builder, xddq.pb.PupilDataMsgOrBuilder> 
        internalGetPupilInfoFieldBuilder() {
      if (pupilInfoBuilder_ == null) {
        pupilInfoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.PupilDataMsg, xddq.pb.PupilDataMsg.Builder, xddq.pb.PupilDataMsgOrBuilder>(
                getPupilInfo(),
                getParentForChildren(),
                isClean());
        pupilInfo_ = null;
      }
      return pupilInfoBuilder_;
    }

    private long requestMarryTime_ ;
    /**
     * <code>required int64 requestMarryTime = 4;</code>
     * @return Whether the requestMarryTime field is set.
     */
    @java.lang.Override
    public boolean hasRequestMarryTime() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>required int64 requestMarryTime = 4;</code>
     * @return The requestMarryTime.
     */
    @java.lang.Override
    public long getRequestMarryTime() {
      return requestMarryTime_;
    }
    /**
     * <code>required int64 requestMarryTime = 4;</code>
     * @param value The requestMarryTime to set.
     * @return This builder for chaining.
     */
    public Builder setRequestMarryTime(long value) {

      requestMarryTime_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>required int64 requestMarryTime = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearRequestMarryTime() {
      bitField0_ = (bitField0_ & ~0x00000008);
      requestMarryTime_ = 0L;
      onChanged();
      return this;
    }

    private long appointUserId_ ;
    /**
     * <code>required int64 appointUserId = 5;</code>
     * @return Whether the appointUserId field is set.
     */
    @java.lang.Override
    public boolean hasAppointUserId() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>required int64 appointUserId = 5;</code>
     * @return The appointUserId.
     */
    @java.lang.Override
    public long getAppointUserId() {
      return appointUserId_;
    }
    /**
     * <code>required int64 appointUserId = 5;</code>
     * @param value The appointUserId to set.
     * @return This builder for chaining.
     */
    public Builder setAppointUserId(long value) {

      appointUserId_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>required int64 appointUserId = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearAppointUserId() {
      bitField0_ = (bitField0_ & ~0x00000010);
      appointUserId_ = 0L;
      onChanged();
      return this;
    }

    private int type_ = 0;
    /**
     * <code>required .xddq.pb.ApplyType type = 6;</code>
     * @return Whether the type field is set.
     */
    @java.lang.Override public boolean hasType() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>required .xddq.pb.ApplyType type = 6;</code>
     * @return The type.
     */
    @java.lang.Override
    public xddq.pb.ApplyType getType() {
      xddq.pb.ApplyType result = xddq.pb.ApplyType.forNumber(type_);
      return result == null ? xddq.pb.ApplyType.UnApply : result;
    }
    /**
     * <code>required .xddq.pb.ApplyType type = 6;</code>
     * @param value The type to set.
     * @return This builder for chaining.
     */
    public Builder setType(xddq.pb.ApplyType value) {
      if (value == null) {
        throw new NullPointerException();
      }
      bitField0_ |= 0x00000020;
      type_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <code>required .xddq.pb.ApplyType type = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearType() {
      bitField0_ = (bitField0_ & ~0x00000020);
      type_ = 0;
      onChanged();
      return this;
    }

    private long serverId_ ;
    /**
     * <code>optional int64 serverId = 7;</code>
     * @return Whether the serverId field is set.
     */
    @java.lang.Override
    public boolean hasServerId() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <code>optional int64 serverId = 7;</code>
     * @return The serverId.
     */
    @java.lang.Override
    public long getServerId() {
      return serverId_;
    }
    /**
     * <code>optional int64 serverId = 7;</code>
     * @param value The serverId to set.
     * @return This builder for chaining.
     */
    public Builder setServerId(long value) {

      serverId_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 serverId = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearServerId() {
      bitField0_ = (bitField0_ & ~0x00000040);
      serverId_ = 0L;
      onChanged();
      return this;
    }

    private int levelLimit_ ;
    /**
     * <code>optional int32 levelLimit = 8;</code>
     * @return Whether the levelLimit field is set.
     */
    @java.lang.Override
    public boolean hasLevelLimit() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <code>optional int32 levelLimit = 8;</code>
     * @return The levelLimit.
     */
    @java.lang.Override
    public int getLevelLimit() {
      return levelLimit_;
    }
    /**
     * <code>optional int32 levelLimit = 8;</code>
     * @param value The levelLimit to set.
     * @return This builder for chaining.
     */
    public Builder setLevelLimit(int value) {

      levelLimit_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 levelLimit = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearLevelLimit() {
      bitField0_ = (bitField0_ & ~0x00000080);
      levelLimit_ = 0;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.MarriageApplyTempMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.MarriageApplyTempMsg)
  private static final xddq.pb.MarriageApplyTempMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.MarriageApplyTempMsg();
  }

  public static xddq.pb.MarriageApplyTempMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<MarriageApplyTempMsg>
      PARSER = new com.google.protobuf.AbstractParser<MarriageApplyTempMsg>() {
    @java.lang.Override
    public MarriageApplyTempMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<MarriageApplyTempMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<MarriageApplyTempMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.MarriageApplyTempMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

