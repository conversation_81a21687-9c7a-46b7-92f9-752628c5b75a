// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.BodyTrialTeamUpMemberInfo}
 */
public final class BodyTrialTeamUpMemberInfo extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.BodyTrialTeamUpMemberInfo)
    BodyTrialTeamUpMemberInfoOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      BodyTrialTeamUpMemberInfo.class.getName());
  }
  // Use BodyTrialTeamUpMemberInfo.newBuilder() to construct.
  private BodyTrialTeamUpMemberInfo(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private BodyTrialTeamUpMemberInfo() {
    fightValue_ = "";
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_BodyTrialTeamUpMemberInfo_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_BodyTrialTeamUpMemberInfo_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.BodyTrialTeamUpMemberInfo.class, xddq.pb.BodyTrialTeamUpMemberInfo.Builder.class);
  }

  private int bitField0_;
  public static final int MEMBERINFO_FIELD_NUMBER = 1;
  private xddq.pb.PlayerHeadAndNameMsg memberInfo_;
  /**
   * <code>optional .xddq.pb.PlayerHeadAndNameMsg memberInfo = 1;</code>
   * @return Whether the memberInfo field is set.
   */
  @java.lang.Override
  public boolean hasMemberInfo() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional .xddq.pb.PlayerHeadAndNameMsg memberInfo = 1;</code>
   * @return The memberInfo.
   */
  @java.lang.Override
  public xddq.pb.PlayerHeadAndNameMsg getMemberInfo() {
    return memberInfo_ == null ? xddq.pb.PlayerHeadAndNameMsg.getDefaultInstance() : memberInfo_;
  }
  /**
   * <code>optional .xddq.pb.PlayerHeadAndNameMsg memberInfo = 1;</code>
   */
  @java.lang.Override
  public xddq.pb.PlayerHeadAndNameMsgOrBuilder getMemberInfoOrBuilder() {
    return memberInfo_ == null ? xddq.pb.PlayerHeadAndNameMsg.getDefaultInstance() : memberInfo_;
  }

  public static final int FIGHTVALUE_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private volatile java.lang.Object fightValue_ = "";
  /**
   * <code>optional string fightValue = 2;</code>
   * @return Whether the fightValue field is set.
   */
  @java.lang.Override
  public boolean hasFightValue() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional string fightValue = 2;</code>
   * @return The fightValue.
   */
  @java.lang.Override
  public java.lang.String getFightValue() {
    java.lang.Object ref = fightValue_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        fightValue_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string fightValue = 2;</code>
   * @return The bytes for fightValue.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getFightValueBytes() {
    java.lang.Object ref = fightValue_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      fightValue_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ISROBOT_FIELD_NUMBER = 3;
  private boolean isRobot_ = false;
  /**
   * <code>optional bool isRobot = 3;</code>
   * @return Whether the isRobot field is set.
   */
  @java.lang.Override
  public boolean hasIsRobot() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional bool isRobot = 3;</code>
   * @return The isRobot.
   */
  @java.lang.Override
  public boolean getIsRobot() {
    return isRobot_;
  }

  public static final int MAXFLOOR_FIELD_NUMBER = 4;
  private int maxFloor_ = 0;
  /**
   * <code>optional int32 maxFloor = 4;</code>
   * @return Whether the maxFloor field is set.
   */
  @java.lang.Override
  public boolean hasMaxFloor() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional int32 maxFloor = 4;</code>
   * @return The maxFloor.
   */
  @java.lang.Override
  public int getMaxFloor() {
    return maxFloor_;
  }

  public static final int ISINVITE_FIELD_NUMBER = 5;
  private boolean isInvite_ = false;
  /**
   * <code>optional bool isInvite = 5;</code>
   * @return Whether the isInvite field is set.
   */
  @java.lang.Override
  public boolean hasIsInvite() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>optional bool isInvite = 5;</code>
   * @return The isInvite.
   */
  @java.lang.Override
  public boolean getIsInvite() {
    return isInvite_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeMessage(1, getMemberInfo());
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 2, fightValue_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeBool(3, isRobot_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt32(4, maxFloor_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      output.writeBool(5, isInvite_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, getMemberInfo());
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(2, fightValue_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(3, isRobot_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(4, maxFloor_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(5, isInvite_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.BodyTrialTeamUpMemberInfo)) {
      return super.equals(obj);
    }
    xddq.pb.BodyTrialTeamUpMemberInfo other = (xddq.pb.BodyTrialTeamUpMemberInfo) obj;

    if (hasMemberInfo() != other.hasMemberInfo()) return false;
    if (hasMemberInfo()) {
      if (!getMemberInfo()
          .equals(other.getMemberInfo())) return false;
    }
    if (hasFightValue() != other.hasFightValue()) return false;
    if (hasFightValue()) {
      if (!getFightValue()
          .equals(other.getFightValue())) return false;
    }
    if (hasIsRobot() != other.hasIsRobot()) return false;
    if (hasIsRobot()) {
      if (getIsRobot()
          != other.getIsRobot()) return false;
    }
    if (hasMaxFloor() != other.hasMaxFloor()) return false;
    if (hasMaxFloor()) {
      if (getMaxFloor()
          != other.getMaxFloor()) return false;
    }
    if (hasIsInvite() != other.hasIsInvite()) return false;
    if (hasIsInvite()) {
      if (getIsInvite()
          != other.getIsInvite()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasMemberInfo()) {
      hash = (37 * hash) + MEMBERINFO_FIELD_NUMBER;
      hash = (53 * hash) + getMemberInfo().hashCode();
    }
    if (hasFightValue()) {
      hash = (37 * hash) + FIGHTVALUE_FIELD_NUMBER;
      hash = (53 * hash) + getFightValue().hashCode();
    }
    if (hasIsRobot()) {
      hash = (37 * hash) + ISROBOT_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getIsRobot());
    }
    if (hasMaxFloor()) {
      hash = (37 * hash) + MAXFLOOR_FIELD_NUMBER;
      hash = (53 * hash) + getMaxFloor();
    }
    if (hasIsInvite()) {
      hash = (37 * hash) + ISINVITE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getIsInvite());
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.BodyTrialTeamUpMemberInfo parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.BodyTrialTeamUpMemberInfo parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.BodyTrialTeamUpMemberInfo parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.BodyTrialTeamUpMemberInfo parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.BodyTrialTeamUpMemberInfo parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.BodyTrialTeamUpMemberInfo parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.BodyTrialTeamUpMemberInfo parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.BodyTrialTeamUpMemberInfo parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.BodyTrialTeamUpMemberInfo parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.BodyTrialTeamUpMemberInfo parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.BodyTrialTeamUpMemberInfo parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.BodyTrialTeamUpMemberInfo parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.BodyTrialTeamUpMemberInfo prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.BodyTrialTeamUpMemberInfo}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.BodyTrialTeamUpMemberInfo)
      xddq.pb.BodyTrialTeamUpMemberInfoOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_BodyTrialTeamUpMemberInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_BodyTrialTeamUpMemberInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.BodyTrialTeamUpMemberInfo.class, xddq.pb.BodyTrialTeamUpMemberInfo.Builder.class);
    }

    // Construct using xddq.pb.BodyTrialTeamUpMemberInfo.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetMemberInfoFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      memberInfo_ = null;
      if (memberInfoBuilder_ != null) {
        memberInfoBuilder_.dispose();
        memberInfoBuilder_ = null;
      }
      fightValue_ = "";
      isRobot_ = false;
      maxFloor_ = 0;
      isInvite_ = false;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_BodyTrialTeamUpMemberInfo_descriptor;
    }

    @java.lang.Override
    public xddq.pb.BodyTrialTeamUpMemberInfo getDefaultInstanceForType() {
      return xddq.pb.BodyTrialTeamUpMemberInfo.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.BodyTrialTeamUpMemberInfo build() {
      xddq.pb.BodyTrialTeamUpMemberInfo result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.BodyTrialTeamUpMemberInfo buildPartial() {
      xddq.pb.BodyTrialTeamUpMemberInfo result = new xddq.pb.BodyTrialTeamUpMemberInfo(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.BodyTrialTeamUpMemberInfo result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.memberInfo_ = memberInfoBuilder_ == null
            ? memberInfo_
            : memberInfoBuilder_.build();
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.fightValue_ = fightValue_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.isRobot_ = isRobot_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.maxFloor_ = maxFloor_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.isInvite_ = isInvite_;
        to_bitField0_ |= 0x00000010;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.BodyTrialTeamUpMemberInfo) {
        return mergeFrom((xddq.pb.BodyTrialTeamUpMemberInfo)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.BodyTrialTeamUpMemberInfo other) {
      if (other == xddq.pb.BodyTrialTeamUpMemberInfo.getDefaultInstance()) return this;
      if (other.hasMemberInfo()) {
        mergeMemberInfo(other.getMemberInfo());
      }
      if (other.hasFightValue()) {
        fightValue_ = other.fightValue_;
        bitField0_ |= 0x00000002;
        onChanged();
      }
      if (other.hasIsRobot()) {
        setIsRobot(other.getIsRobot());
      }
      if (other.hasMaxFloor()) {
        setMaxFloor(other.getMaxFloor());
      }
      if (other.hasIsInvite()) {
        setIsInvite(other.getIsInvite());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              input.readMessage(
                  internalGetMemberInfoFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 18: {
              fightValue_ = input.readBytes();
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 24: {
              isRobot_ = input.readBool();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 32: {
              maxFloor_ = input.readInt32();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            case 40: {
              isInvite_ = input.readBool();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private xddq.pb.PlayerHeadAndNameMsg memberInfo_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PlayerHeadAndNameMsg, xddq.pb.PlayerHeadAndNameMsg.Builder, xddq.pb.PlayerHeadAndNameMsgOrBuilder> memberInfoBuilder_;
    /**
     * <code>optional .xddq.pb.PlayerHeadAndNameMsg memberInfo = 1;</code>
     * @return Whether the memberInfo field is set.
     */
    public boolean hasMemberInfo() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadAndNameMsg memberInfo = 1;</code>
     * @return The memberInfo.
     */
    public xddq.pb.PlayerHeadAndNameMsg getMemberInfo() {
      if (memberInfoBuilder_ == null) {
        return memberInfo_ == null ? xddq.pb.PlayerHeadAndNameMsg.getDefaultInstance() : memberInfo_;
      } else {
        return memberInfoBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadAndNameMsg memberInfo = 1;</code>
     */
    public Builder setMemberInfo(xddq.pb.PlayerHeadAndNameMsg value) {
      if (memberInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        memberInfo_ = value;
      } else {
        memberInfoBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadAndNameMsg memberInfo = 1;</code>
     */
    public Builder setMemberInfo(
        xddq.pb.PlayerHeadAndNameMsg.Builder builderForValue) {
      if (memberInfoBuilder_ == null) {
        memberInfo_ = builderForValue.build();
      } else {
        memberInfoBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadAndNameMsg memberInfo = 1;</code>
     */
    public Builder mergeMemberInfo(xddq.pb.PlayerHeadAndNameMsg value) {
      if (memberInfoBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0) &&
          memberInfo_ != null &&
          memberInfo_ != xddq.pb.PlayerHeadAndNameMsg.getDefaultInstance()) {
          getMemberInfoBuilder().mergeFrom(value);
        } else {
          memberInfo_ = value;
        }
      } else {
        memberInfoBuilder_.mergeFrom(value);
      }
      if (memberInfo_ != null) {
        bitField0_ |= 0x00000001;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadAndNameMsg memberInfo = 1;</code>
     */
    public Builder clearMemberInfo() {
      bitField0_ = (bitField0_ & ~0x00000001);
      memberInfo_ = null;
      if (memberInfoBuilder_ != null) {
        memberInfoBuilder_.dispose();
        memberInfoBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadAndNameMsg memberInfo = 1;</code>
     */
    public xddq.pb.PlayerHeadAndNameMsg.Builder getMemberInfoBuilder() {
      bitField0_ |= 0x00000001;
      onChanged();
      return internalGetMemberInfoFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadAndNameMsg memberInfo = 1;</code>
     */
    public xddq.pb.PlayerHeadAndNameMsgOrBuilder getMemberInfoOrBuilder() {
      if (memberInfoBuilder_ != null) {
        return memberInfoBuilder_.getMessageOrBuilder();
      } else {
        return memberInfo_ == null ?
            xddq.pb.PlayerHeadAndNameMsg.getDefaultInstance() : memberInfo_;
      }
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadAndNameMsg memberInfo = 1;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PlayerHeadAndNameMsg, xddq.pb.PlayerHeadAndNameMsg.Builder, xddq.pb.PlayerHeadAndNameMsgOrBuilder> 
        internalGetMemberInfoFieldBuilder() {
      if (memberInfoBuilder_ == null) {
        memberInfoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.PlayerHeadAndNameMsg, xddq.pb.PlayerHeadAndNameMsg.Builder, xddq.pb.PlayerHeadAndNameMsgOrBuilder>(
                getMemberInfo(),
                getParentForChildren(),
                isClean());
        memberInfo_ = null;
      }
      return memberInfoBuilder_;
    }

    private java.lang.Object fightValue_ = "";
    /**
     * <code>optional string fightValue = 2;</code>
     * @return Whether the fightValue field is set.
     */
    public boolean hasFightValue() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional string fightValue = 2;</code>
     * @return The fightValue.
     */
    public java.lang.String getFightValue() {
      java.lang.Object ref = fightValue_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          fightValue_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string fightValue = 2;</code>
     * @return The bytes for fightValue.
     */
    public com.google.protobuf.ByteString
        getFightValueBytes() {
      java.lang.Object ref = fightValue_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        fightValue_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string fightValue = 2;</code>
     * @param value The fightValue to set.
     * @return This builder for chaining.
     */
    public Builder setFightValue(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      fightValue_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional string fightValue = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearFightValue() {
      fightValue_ = getDefaultInstance().getFightValue();
      bitField0_ = (bitField0_ & ~0x00000002);
      onChanged();
      return this;
    }
    /**
     * <code>optional string fightValue = 2;</code>
     * @param value The bytes for fightValue to set.
     * @return This builder for chaining.
     */
    public Builder setFightValueBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      fightValue_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }

    private boolean isRobot_ ;
    /**
     * <code>optional bool isRobot = 3;</code>
     * @return Whether the isRobot field is set.
     */
    @java.lang.Override
    public boolean hasIsRobot() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional bool isRobot = 3;</code>
     * @return The isRobot.
     */
    @java.lang.Override
    public boolean getIsRobot() {
      return isRobot_;
    }
    /**
     * <code>optional bool isRobot = 3;</code>
     * @param value The isRobot to set.
     * @return This builder for chaining.
     */
    public Builder setIsRobot(boolean value) {

      isRobot_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional bool isRobot = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearIsRobot() {
      bitField0_ = (bitField0_ & ~0x00000004);
      isRobot_ = false;
      onChanged();
      return this;
    }

    private int maxFloor_ ;
    /**
     * <code>optional int32 maxFloor = 4;</code>
     * @return Whether the maxFloor field is set.
     */
    @java.lang.Override
    public boolean hasMaxFloor() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int32 maxFloor = 4;</code>
     * @return The maxFloor.
     */
    @java.lang.Override
    public int getMaxFloor() {
      return maxFloor_;
    }
    /**
     * <code>optional int32 maxFloor = 4;</code>
     * @param value The maxFloor to set.
     * @return This builder for chaining.
     */
    public Builder setMaxFloor(int value) {

      maxFloor_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 maxFloor = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearMaxFloor() {
      bitField0_ = (bitField0_ & ~0x00000008);
      maxFloor_ = 0;
      onChanged();
      return this;
    }

    private boolean isInvite_ ;
    /**
     * <code>optional bool isInvite = 5;</code>
     * @return Whether the isInvite field is set.
     */
    @java.lang.Override
    public boolean hasIsInvite() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional bool isInvite = 5;</code>
     * @return The isInvite.
     */
    @java.lang.Override
    public boolean getIsInvite() {
      return isInvite_;
    }
    /**
     * <code>optional bool isInvite = 5;</code>
     * @param value The isInvite to set.
     * @return This builder for chaining.
     */
    public Builder setIsInvite(boolean value) {

      isInvite_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional bool isInvite = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearIsInvite() {
      bitField0_ = (bitField0_ & ~0x00000010);
      isInvite_ = false;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.BodyTrialTeamUpMemberInfo)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.BodyTrialTeamUpMemberInfo)
  private static final xddq.pb.BodyTrialTeamUpMemberInfo DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.BodyTrialTeamUpMemberInfo();
  }

  public static xddq.pb.BodyTrialTeamUpMemberInfo getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<BodyTrialTeamUpMemberInfo>
      PARSER = new com.google.protobuf.AbstractParser<BodyTrialTeamUpMemberInfo>() {
    @java.lang.Override
    public BodyTrialTeamUpMemberInfo parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<BodyTrialTeamUpMemberInfo> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<BodyTrialTeamUpMemberInfo> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.BodyTrialTeamUpMemberInfo getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

