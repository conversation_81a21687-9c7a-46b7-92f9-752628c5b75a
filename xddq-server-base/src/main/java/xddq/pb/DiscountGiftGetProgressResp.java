// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.DiscountGiftGetProgressResp}
 */
public final class DiscountGiftGetProgressResp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.DiscountGiftGetProgressResp)
    DiscountGiftGetProgressRespOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      DiscountGiftGetProgressResp.class.getName());
  }
  // Use DiscountGiftGetProgressResp.newBuilder() to construct.
  private DiscountGiftGetProgressResp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private DiscountGiftGetProgressResp() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_DiscountGiftGetProgressResp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_DiscountGiftGetProgressResp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.DiscountGiftGetProgressResp.class, xddq.pb.DiscountGiftGetProgressResp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int CURDISCOUNTVAL_FIELD_NUMBER = 2;
  private int curDiscountVal_ = 0;
  /**
   * <code>optional int32 curDiscountVal = 2;</code>
   * @return Whether the curDiscountVal field is set.
   */
  @java.lang.Override
  public boolean hasCurDiscountVal() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int32 curDiscountVal = 2;</code>
   * @return The curDiscountVal.
   */
  @java.lang.Override
  public int getCurDiscountVal() {
    return curDiscountVal_;
  }

  public static final int CURCONDITIONVAL_FIELD_NUMBER = 3;
  private long curConditionVal_ = 0L;
  /**
   * <code>optional int64 curConditionVal = 3;</code>
   * @return Whether the curConditionVal field is set.
   */
  @java.lang.Override
  public boolean hasCurConditionVal() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional int64 curConditionVal = 3;</code>
   * @return The curConditionVal.
   */
  @java.lang.Override
  public long getCurConditionVal() {
    return curConditionVal_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(2, curDiscountVal_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt64(3, curConditionVal_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, curDiscountVal_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(3, curConditionVal_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.DiscountGiftGetProgressResp)) {
      return super.equals(obj);
    }
    xddq.pb.DiscountGiftGetProgressResp other = (xddq.pb.DiscountGiftGetProgressResp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (hasCurDiscountVal() != other.hasCurDiscountVal()) return false;
    if (hasCurDiscountVal()) {
      if (getCurDiscountVal()
          != other.getCurDiscountVal()) return false;
    }
    if (hasCurConditionVal() != other.hasCurConditionVal()) return false;
    if (hasCurConditionVal()) {
      if (getCurConditionVal()
          != other.getCurConditionVal()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (hasCurDiscountVal()) {
      hash = (37 * hash) + CURDISCOUNTVAL_FIELD_NUMBER;
      hash = (53 * hash) + getCurDiscountVal();
    }
    if (hasCurConditionVal()) {
      hash = (37 * hash) + CURCONDITIONVAL_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getCurConditionVal());
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.DiscountGiftGetProgressResp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.DiscountGiftGetProgressResp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.DiscountGiftGetProgressResp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.DiscountGiftGetProgressResp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.DiscountGiftGetProgressResp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.DiscountGiftGetProgressResp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.DiscountGiftGetProgressResp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.DiscountGiftGetProgressResp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.DiscountGiftGetProgressResp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.DiscountGiftGetProgressResp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.DiscountGiftGetProgressResp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.DiscountGiftGetProgressResp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.DiscountGiftGetProgressResp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.DiscountGiftGetProgressResp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.DiscountGiftGetProgressResp)
      xddq.pb.DiscountGiftGetProgressRespOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_DiscountGiftGetProgressResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_DiscountGiftGetProgressResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.DiscountGiftGetProgressResp.class, xddq.pb.DiscountGiftGetProgressResp.Builder.class);
    }

    // Construct using xddq.pb.DiscountGiftGetProgressResp.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      curDiscountVal_ = 0;
      curConditionVal_ = 0L;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_DiscountGiftGetProgressResp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.DiscountGiftGetProgressResp getDefaultInstanceForType() {
      return xddq.pb.DiscountGiftGetProgressResp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.DiscountGiftGetProgressResp build() {
      xddq.pb.DiscountGiftGetProgressResp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.DiscountGiftGetProgressResp buildPartial() {
      xddq.pb.DiscountGiftGetProgressResp result = new xddq.pb.DiscountGiftGetProgressResp(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.DiscountGiftGetProgressResp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.curDiscountVal_ = curDiscountVal_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.curConditionVal_ = curConditionVal_;
        to_bitField0_ |= 0x00000004;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.DiscountGiftGetProgressResp) {
        return mergeFrom((xddq.pb.DiscountGiftGetProgressResp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.DiscountGiftGetProgressResp other) {
      if (other == xddq.pb.DiscountGiftGetProgressResp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (other.hasCurDiscountVal()) {
        setCurDiscountVal(other.getCurDiscountVal());
      }
      if (other.hasCurConditionVal()) {
        setCurConditionVal(other.getCurConditionVal());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              curDiscountVal_ = input.readInt32();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 24: {
              curConditionVal_ = input.readInt64();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private int curDiscountVal_ ;
    /**
     * <code>optional int32 curDiscountVal = 2;</code>
     * @return Whether the curDiscountVal field is set.
     */
    @java.lang.Override
    public boolean hasCurDiscountVal() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 curDiscountVal = 2;</code>
     * @return The curDiscountVal.
     */
    @java.lang.Override
    public int getCurDiscountVal() {
      return curDiscountVal_;
    }
    /**
     * <code>optional int32 curDiscountVal = 2;</code>
     * @param value The curDiscountVal to set.
     * @return This builder for chaining.
     */
    public Builder setCurDiscountVal(int value) {

      curDiscountVal_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 curDiscountVal = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearCurDiscountVal() {
      bitField0_ = (bitField0_ & ~0x00000002);
      curDiscountVal_ = 0;
      onChanged();
      return this;
    }

    private long curConditionVal_ ;
    /**
     * <code>optional int64 curConditionVal = 3;</code>
     * @return Whether the curConditionVal field is set.
     */
    @java.lang.Override
    public boolean hasCurConditionVal() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int64 curConditionVal = 3;</code>
     * @return The curConditionVal.
     */
    @java.lang.Override
    public long getCurConditionVal() {
      return curConditionVal_;
    }
    /**
     * <code>optional int64 curConditionVal = 3;</code>
     * @param value The curConditionVal to set.
     * @return This builder for chaining.
     */
    public Builder setCurConditionVal(long value) {

      curConditionVal_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 curConditionVal = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearCurConditionVal() {
      bitField0_ = (bitField0_ & ~0x00000004);
      curConditionVal_ = 0L;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.DiscountGiftGetProgressResp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.DiscountGiftGetProgressResp)
  private static final xddq.pb.DiscountGiftGetProgressResp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.DiscountGiftGetProgressResp();
  }

  public static xddq.pb.DiscountGiftGetProgressResp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<DiscountGiftGetProgressResp>
      PARSER = new com.google.protobuf.AbstractParser<DiscountGiftGetProgressResp>() {
    @java.lang.Override
    public DiscountGiftGetProgressResp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<DiscountGiftGetProgressResp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<DiscountGiftGetProgressResp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.DiscountGiftGetProgressResp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

