// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

public interface PlayerBattleShowDataMsgOrBuilder extends
    // @@protoc_insertion_point(interface_extends:xddq.pb.PlayerBattleShowDataMsg)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>optional .xddq.pb.PlayerBaseDataMsg playerBaseDataMsg = 1;</code>
   * @return Whether the playerBaseDataMsg field is set.
   */
  boolean hasPlayerBaseDataMsg();
  /**
   * <code>optional .xddq.pb.PlayerBaseDataMsg playerBaseDataMsg = 1;</code>
   * @return The playerBaseDataMsg.
   */
  xddq.pb.PlayerBaseDataMsg getPlayerBaseDataMsg();
  /**
   * <code>optional .xddq.pb.PlayerBaseDataMsg playerBaseDataMsg = 1;</code>
   */
  xddq.pb.PlayerBaseDataMsgOrBuilder getPlayerBaseDataMsgOrBuilder();

  /**
   * <code>optional int64 score = 2;</code>
   * @return Whether the score field is set.
   */
  boolean hasScore();
  /**
   * <code>optional int64 score = 2;</code>
   * @return The score.
   */
  long getScore();

  /**
   * <code>optional int64 protectEndTime = 3;</code>
   * @return Whether the protectEndTime field is set.
   */
  boolean hasProtectEndTime();
  /**
   * <code>optional int64 protectEndTime = 3;</code>
   * @return The protectEndTime.
   */
  long getProtectEndTime();
}
