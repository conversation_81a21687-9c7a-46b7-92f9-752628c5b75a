// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.CastSwordSwordConfigMsg}
 */
public final class CastSwordSwordConfigMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.CastSwordSwordConfigMsg)
    CastSwordSwordConfigMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      CastSwordSwordConfigMsg.class.getName());
  }
  // Use CastSwordSwordConfigMsg.newBuilder() to construct.
  private CastSwordSwordConfigMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private CastSwordSwordConfigMsg() {
    destroyed_ = "";
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_CastSwordSwordConfigMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_CastSwordSwordConfigMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.CastSwordSwordConfigMsg.class, xddq.pb.CastSwordSwordConfigMsg.Builder.class);
  }

  private int bitField0_;
  public static final int ACTIVITYID_FIELD_NUMBER = 1;
  private int activityId_ = 0;
  /**
   * <code>required int32 activityId = 1;</code>
   * @return Whether the activityId field is set.
   */
  @java.lang.Override
  public boolean hasActivityId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 activityId = 1;</code>
   * @return The activityId.
   */
  @java.lang.Override
  public int getActivityId() {
    return activityId_;
  }

  public static final int ID_FIELD_NUMBER = 2;
  private int id_ = 0;
  /**
   * <code>required int32 id = 2;</code>
   * @return Whether the id field is set.
   */
  @java.lang.Override
  public boolean hasId() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>required int32 id = 2;</code>
   * @return The id.
   */
  @java.lang.Override
  public int getId() {
    return id_;
  }

  public static final int LEVEL_FIELD_NUMBER = 3;
  private int level_ = 0;
  /**
   * <code>required int32 level = 3;</code>
   * @return Whether the level field is set.
   */
  @java.lang.Override
  public boolean hasLevel() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>required int32 level = 3;</code>
   * @return The level.
   */
  @java.lang.Override
  public int getLevel() {
    return level_;
  }

  public static final int POINTS_FIELD_NUMBER = 4;
  private int points_ = 0;
  /**
   * <code>required int32 points = 4;</code>
   * @return Whether the points field is set.
   */
  @java.lang.Override
  public boolean hasPoints() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>required int32 points = 4;</code>
   * @return The points.
   */
  @java.lang.Override
  public int getPoints() {
    return points_;
  }

  public static final int WEIGHT_FIELD_NUMBER = 5;
  private int weight_ = 0;
  /**
   * <code>required int32 weight = 5;</code>
   * @return Whether the weight field is set.
   */
  @java.lang.Override
  public boolean hasWeight() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>required int32 weight = 5;</code>
   * @return The weight.
   */
  @java.lang.Override
  public int getWeight() {
    return weight_;
  }

  public static final int UNLOCK_FIELD_NUMBER = 6;
  private int unlock_ = 0;
  /**
   * <code>required int32 unlock = 6;</code>
   * @return Whether the unlock field is set.
   */
  @java.lang.Override
  public boolean hasUnlock() {
    return ((bitField0_ & 0x00000020) != 0);
  }
  /**
   * <code>required int32 unlock = 6;</code>
   * @return The unlock.
   */
  @java.lang.Override
  public int getUnlock() {
    return unlock_;
  }

  public static final int ICONID_FIELD_NUMBER = 7;
  private int iconID_ = 0;
  /**
   * <code>required int32 iconID = 7;</code>
   * @return Whether the iconID field is set.
   */
  @java.lang.Override
  public boolean hasIconID() {
    return ((bitField0_ & 0x00000040) != 0);
  }
  /**
   * <code>required int32 iconID = 7;</code>
   * @return The iconID.
   */
  @java.lang.Override
  public int getIconID() {
    return iconID_;
  }

  public static final int DESTROYED_FIELD_NUMBER = 8;
  @SuppressWarnings("serial")
  private volatile java.lang.Object destroyed_ = "";
  /**
   * <code>required string destroyed = 8;</code>
   * @return Whether the destroyed field is set.
   */
  @java.lang.Override
  public boolean hasDestroyed() {
    return ((bitField0_ & 0x00000080) != 0);
  }
  /**
   * <code>required string destroyed = 8;</code>
   * @return The destroyed.
   */
  @java.lang.Override
  public java.lang.String getDestroyed() {
    java.lang.Object ref = destroyed_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        destroyed_ = s;
      }
      return s;
    }
  }
  /**
   * <code>required string destroyed = 8;</code>
   * @return The bytes for destroyed.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getDestroyedBytes() {
    java.lang.Object ref = destroyed_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      destroyed_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int IMPACTFACTOR_FIELD_NUMBER = 9;
  private int impactFactor_ = 0;
  /**
   * <code>required int32 impactFactor = 9;</code>
   * @return Whether the impactFactor field is set.
   */
  @java.lang.Override
  public boolean hasImpactFactor() {
    return ((bitField0_ & 0x00000100) != 0);
  }
  /**
   * <code>required int32 impactFactor = 9;</code>
   * @return The impactFactor.
   */
  @java.lang.Override
  public int getImpactFactor() {
    return impactFactor_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasActivityId()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasId()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasLevel()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasPoints()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasWeight()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasUnlock()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasIconID()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasDestroyed()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasImpactFactor()) {
      memoizedIsInitialized = 0;
      return false;
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, activityId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(2, id_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt32(3, level_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt32(4, points_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      output.writeInt32(5, weight_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      output.writeInt32(6, unlock_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      output.writeInt32(7, iconID_);
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 8, destroyed_);
    }
    if (((bitField0_ & 0x00000100) != 0)) {
      output.writeInt32(9, impactFactor_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, activityId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, id_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(3, level_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(4, points_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(5, weight_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(6, unlock_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(7, iconID_);
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(8, destroyed_);
    }
    if (((bitField0_ & 0x00000100) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(9, impactFactor_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.CastSwordSwordConfigMsg)) {
      return super.equals(obj);
    }
    xddq.pb.CastSwordSwordConfigMsg other = (xddq.pb.CastSwordSwordConfigMsg) obj;

    if (hasActivityId() != other.hasActivityId()) return false;
    if (hasActivityId()) {
      if (getActivityId()
          != other.getActivityId()) return false;
    }
    if (hasId() != other.hasId()) return false;
    if (hasId()) {
      if (getId()
          != other.getId()) return false;
    }
    if (hasLevel() != other.hasLevel()) return false;
    if (hasLevel()) {
      if (getLevel()
          != other.getLevel()) return false;
    }
    if (hasPoints() != other.hasPoints()) return false;
    if (hasPoints()) {
      if (getPoints()
          != other.getPoints()) return false;
    }
    if (hasWeight() != other.hasWeight()) return false;
    if (hasWeight()) {
      if (getWeight()
          != other.getWeight()) return false;
    }
    if (hasUnlock() != other.hasUnlock()) return false;
    if (hasUnlock()) {
      if (getUnlock()
          != other.getUnlock()) return false;
    }
    if (hasIconID() != other.hasIconID()) return false;
    if (hasIconID()) {
      if (getIconID()
          != other.getIconID()) return false;
    }
    if (hasDestroyed() != other.hasDestroyed()) return false;
    if (hasDestroyed()) {
      if (!getDestroyed()
          .equals(other.getDestroyed())) return false;
    }
    if (hasImpactFactor() != other.hasImpactFactor()) return false;
    if (hasImpactFactor()) {
      if (getImpactFactor()
          != other.getImpactFactor()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasActivityId()) {
      hash = (37 * hash) + ACTIVITYID_FIELD_NUMBER;
      hash = (53 * hash) + getActivityId();
    }
    if (hasId()) {
      hash = (37 * hash) + ID_FIELD_NUMBER;
      hash = (53 * hash) + getId();
    }
    if (hasLevel()) {
      hash = (37 * hash) + LEVEL_FIELD_NUMBER;
      hash = (53 * hash) + getLevel();
    }
    if (hasPoints()) {
      hash = (37 * hash) + POINTS_FIELD_NUMBER;
      hash = (53 * hash) + getPoints();
    }
    if (hasWeight()) {
      hash = (37 * hash) + WEIGHT_FIELD_NUMBER;
      hash = (53 * hash) + getWeight();
    }
    if (hasUnlock()) {
      hash = (37 * hash) + UNLOCK_FIELD_NUMBER;
      hash = (53 * hash) + getUnlock();
    }
    if (hasIconID()) {
      hash = (37 * hash) + ICONID_FIELD_NUMBER;
      hash = (53 * hash) + getIconID();
    }
    if (hasDestroyed()) {
      hash = (37 * hash) + DESTROYED_FIELD_NUMBER;
      hash = (53 * hash) + getDestroyed().hashCode();
    }
    if (hasImpactFactor()) {
      hash = (37 * hash) + IMPACTFACTOR_FIELD_NUMBER;
      hash = (53 * hash) + getImpactFactor();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.CastSwordSwordConfigMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.CastSwordSwordConfigMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.CastSwordSwordConfigMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.CastSwordSwordConfigMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.CastSwordSwordConfigMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.CastSwordSwordConfigMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.CastSwordSwordConfigMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.CastSwordSwordConfigMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.CastSwordSwordConfigMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.CastSwordSwordConfigMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.CastSwordSwordConfigMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.CastSwordSwordConfigMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.CastSwordSwordConfigMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.CastSwordSwordConfigMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.CastSwordSwordConfigMsg)
      xddq.pb.CastSwordSwordConfigMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_CastSwordSwordConfigMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_CastSwordSwordConfigMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.CastSwordSwordConfigMsg.class, xddq.pb.CastSwordSwordConfigMsg.Builder.class);
    }

    // Construct using xddq.pb.CastSwordSwordConfigMsg.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      activityId_ = 0;
      id_ = 0;
      level_ = 0;
      points_ = 0;
      weight_ = 0;
      unlock_ = 0;
      iconID_ = 0;
      destroyed_ = "";
      impactFactor_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_CastSwordSwordConfigMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.CastSwordSwordConfigMsg getDefaultInstanceForType() {
      return xddq.pb.CastSwordSwordConfigMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.CastSwordSwordConfigMsg build() {
      xddq.pb.CastSwordSwordConfigMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.CastSwordSwordConfigMsg buildPartial() {
      xddq.pb.CastSwordSwordConfigMsg result = new xddq.pb.CastSwordSwordConfigMsg(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.CastSwordSwordConfigMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.activityId_ = activityId_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.id_ = id_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.level_ = level_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.points_ = points_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.weight_ = weight_;
        to_bitField0_ |= 0x00000010;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.unlock_ = unlock_;
        to_bitField0_ |= 0x00000020;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.iconID_ = iconID_;
        to_bitField0_ |= 0x00000040;
      }
      if (((from_bitField0_ & 0x00000080) != 0)) {
        result.destroyed_ = destroyed_;
        to_bitField0_ |= 0x00000080;
      }
      if (((from_bitField0_ & 0x00000100) != 0)) {
        result.impactFactor_ = impactFactor_;
        to_bitField0_ |= 0x00000100;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.CastSwordSwordConfigMsg) {
        return mergeFrom((xddq.pb.CastSwordSwordConfigMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.CastSwordSwordConfigMsg other) {
      if (other == xddq.pb.CastSwordSwordConfigMsg.getDefaultInstance()) return this;
      if (other.hasActivityId()) {
        setActivityId(other.getActivityId());
      }
      if (other.hasId()) {
        setId(other.getId());
      }
      if (other.hasLevel()) {
        setLevel(other.getLevel());
      }
      if (other.hasPoints()) {
        setPoints(other.getPoints());
      }
      if (other.hasWeight()) {
        setWeight(other.getWeight());
      }
      if (other.hasUnlock()) {
        setUnlock(other.getUnlock());
      }
      if (other.hasIconID()) {
        setIconID(other.getIconID());
      }
      if (other.hasDestroyed()) {
        destroyed_ = other.destroyed_;
        bitField0_ |= 0x00000080;
        onChanged();
      }
      if (other.hasImpactFactor()) {
        setImpactFactor(other.getImpactFactor());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasActivityId()) {
        return false;
      }
      if (!hasId()) {
        return false;
      }
      if (!hasLevel()) {
        return false;
      }
      if (!hasPoints()) {
        return false;
      }
      if (!hasWeight()) {
        return false;
      }
      if (!hasUnlock()) {
        return false;
      }
      if (!hasIconID()) {
        return false;
      }
      if (!hasDestroyed()) {
        return false;
      }
      if (!hasImpactFactor()) {
        return false;
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              activityId_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              id_ = input.readInt32();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 24: {
              level_ = input.readInt32();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 32: {
              points_ = input.readInt32();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            case 40: {
              weight_ = input.readInt32();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            case 48: {
              unlock_ = input.readInt32();
              bitField0_ |= 0x00000020;
              break;
            } // case 48
            case 56: {
              iconID_ = input.readInt32();
              bitField0_ |= 0x00000040;
              break;
            } // case 56
            case 66: {
              destroyed_ = input.readBytes();
              bitField0_ |= 0x00000080;
              break;
            } // case 66
            case 72: {
              impactFactor_ = input.readInt32();
              bitField0_ |= 0x00000100;
              break;
            } // case 72
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int activityId_ ;
    /**
     * <code>required int32 activityId = 1;</code>
     * @return Whether the activityId field is set.
     */
    @java.lang.Override
    public boolean hasActivityId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 activityId = 1;</code>
     * @return The activityId.
     */
    @java.lang.Override
    public int getActivityId() {
      return activityId_;
    }
    /**
     * <code>required int32 activityId = 1;</code>
     * @param value The activityId to set.
     * @return This builder for chaining.
     */
    public Builder setActivityId(int value) {

      activityId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 activityId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearActivityId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      activityId_ = 0;
      onChanged();
      return this;
    }

    private int id_ ;
    /**
     * <code>required int32 id = 2;</code>
     * @return Whether the id field is set.
     */
    @java.lang.Override
    public boolean hasId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>required int32 id = 2;</code>
     * @return The id.
     */
    @java.lang.Override
    public int getId() {
      return id_;
    }
    /**
     * <code>required int32 id = 2;</code>
     * @param value The id to set.
     * @return This builder for chaining.
     */
    public Builder setId(int value) {

      id_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 id = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearId() {
      bitField0_ = (bitField0_ & ~0x00000002);
      id_ = 0;
      onChanged();
      return this;
    }

    private int level_ ;
    /**
     * <code>required int32 level = 3;</code>
     * @return Whether the level field is set.
     */
    @java.lang.Override
    public boolean hasLevel() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>required int32 level = 3;</code>
     * @return The level.
     */
    @java.lang.Override
    public int getLevel() {
      return level_;
    }
    /**
     * <code>required int32 level = 3;</code>
     * @param value The level to set.
     * @return This builder for chaining.
     */
    public Builder setLevel(int value) {

      level_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 level = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearLevel() {
      bitField0_ = (bitField0_ & ~0x00000004);
      level_ = 0;
      onChanged();
      return this;
    }

    private int points_ ;
    /**
     * <code>required int32 points = 4;</code>
     * @return Whether the points field is set.
     */
    @java.lang.Override
    public boolean hasPoints() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>required int32 points = 4;</code>
     * @return The points.
     */
    @java.lang.Override
    public int getPoints() {
      return points_;
    }
    /**
     * <code>required int32 points = 4;</code>
     * @param value The points to set.
     * @return This builder for chaining.
     */
    public Builder setPoints(int value) {

      points_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 points = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearPoints() {
      bitField0_ = (bitField0_ & ~0x00000008);
      points_ = 0;
      onChanged();
      return this;
    }

    private int weight_ ;
    /**
     * <code>required int32 weight = 5;</code>
     * @return Whether the weight field is set.
     */
    @java.lang.Override
    public boolean hasWeight() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>required int32 weight = 5;</code>
     * @return The weight.
     */
    @java.lang.Override
    public int getWeight() {
      return weight_;
    }
    /**
     * <code>required int32 weight = 5;</code>
     * @param value The weight to set.
     * @return This builder for chaining.
     */
    public Builder setWeight(int value) {

      weight_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 weight = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearWeight() {
      bitField0_ = (bitField0_ & ~0x00000010);
      weight_ = 0;
      onChanged();
      return this;
    }

    private int unlock_ ;
    /**
     * <code>required int32 unlock = 6;</code>
     * @return Whether the unlock field is set.
     */
    @java.lang.Override
    public boolean hasUnlock() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>required int32 unlock = 6;</code>
     * @return The unlock.
     */
    @java.lang.Override
    public int getUnlock() {
      return unlock_;
    }
    /**
     * <code>required int32 unlock = 6;</code>
     * @param value The unlock to set.
     * @return This builder for chaining.
     */
    public Builder setUnlock(int value) {

      unlock_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 unlock = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearUnlock() {
      bitField0_ = (bitField0_ & ~0x00000020);
      unlock_ = 0;
      onChanged();
      return this;
    }

    private int iconID_ ;
    /**
     * <code>required int32 iconID = 7;</code>
     * @return Whether the iconID field is set.
     */
    @java.lang.Override
    public boolean hasIconID() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <code>required int32 iconID = 7;</code>
     * @return The iconID.
     */
    @java.lang.Override
    public int getIconID() {
      return iconID_;
    }
    /**
     * <code>required int32 iconID = 7;</code>
     * @param value The iconID to set.
     * @return This builder for chaining.
     */
    public Builder setIconID(int value) {

      iconID_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 iconID = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearIconID() {
      bitField0_ = (bitField0_ & ~0x00000040);
      iconID_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object destroyed_ = "";
    /**
     * <code>required string destroyed = 8;</code>
     * @return Whether the destroyed field is set.
     */
    public boolean hasDestroyed() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <code>required string destroyed = 8;</code>
     * @return The destroyed.
     */
    public java.lang.String getDestroyed() {
      java.lang.Object ref = destroyed_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          destroyed_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>required string destroyed = 8;</code>
     * @return The bytes for destroyed.
     */
    public com.google.protobuf.ByteString
        getDestroyedBytes() {
      java.lang.Object ref = destroyed_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        destroyed_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>required string destroyed = 8;</code>
     * @param value The destroyed to set.
     * @return This builder for chaining.
     */
    public Builder setDestroyed(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      destroyed_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }
    /**
     * <code>required string destroyed = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearDestroyed() {
      destroyed_ = getDefaultInstance().getDestroyed();
      bitField0_ = (bitField0_ & ~0x00000080);
      onChanged();
      return this;
    }
    /**
     * <code>required string destroyed = 8;</code>
     * @param value The bytes for destroyed to set.
     * @return This builder for chaining.
     */
    public Builder setDestroyedBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      destroyed_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }

    private int impactFactor_ ;
    /**
     * <code>required int32 impactFactor = 9;</code>
     * @return Whether the impactFactor field is set.
     */
    @java.lang.Override
    public boolean hasImpactFactor() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <code>required int32 impactFactor = 9;</code>
     * @return The impactFactor.
     */
    @java.lang.Override
    public int getImpactFactor() {
      return impactFactor_;
    }
    /**
     * <code>required int32 impactFactor = 9;</code>
     * @param value The impactFactor to set.
     * @return This builder for chaining.
     */
    public Builder setImpactFactor(int value) {

      impactFactor_ = value;
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 impactFactor = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearImpactFactor() {
      bitField0_ = (bitField0_ & ~0x00000100);
      impactFactor_ = 0;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.CastSwordSwordConfigMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.CastSwordSwordConfigMsg)
  private static final xddq.pb.CastSwordSwordConfigMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.CastSwordSwordConfigMsg();
  }

  public static xddq.pb.CastSwordSwordConfigMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<CastSwordSwordConfigMsg>
      PARSER = new com.google.protobuf.AbstractParser<CastSwordSwordConfigMsg>() {
    @java.lang.Override
    public CastSwordSwordConfigMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<CastSwordSwordConfigMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<CastSwordSwordConfigMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.CastSwordSwordConfigMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

