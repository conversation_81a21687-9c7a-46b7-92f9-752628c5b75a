// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.RuleTrialBossPowerData}
 */
public final class RuleTrialBossPowerData extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.RuleTrialBossPowerData)
    RuleTrialBossPowerDataOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      RuleTrialBossPowerData.class.getName());
  }
  // Use RuleTrialBossPowerData.newBuilder() to construct.
  private RuleTrialBossPowerData(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private RuleTrialBossPowerData() {
    power_ = "";
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_RuleTrialBossPowerData_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_RuleTrialBossPowerData_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.RuleTrialBossPowerData.class, xddq.pb.RuleTrialBossPowerData.Builder.class);
  }

  private int bitField0_;
  public static final int BOSSID_FIELD_NUMBER = 1;
  private int bossId_ = 0;
  /**
   * <code>required int32 bossId = 1;</code>
   * @return Whether the bossId field is set.
   */
  @java.lang.Override
  public boolean hasBossId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 bossId = 1;</code>
   * @return The bossId.
   */
  @java.lang.Override
  public int getBossId() {
    return bossId_;
  }

  public static final int POWER_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private volatile java.lang.Object power_ = "";
  /**
   * <code>required string power = 2;</code>
   * @return Whether the power field is set.
   */
  @java.lang.Override
  public boolean hasPower() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>required string power = 2;</code>
   * @return The power.
   */
  @java.lang.Override
  public java.lang.String getPower() {
    java.lang.Object ref = power_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        power_ = s;
      }
      return s;
    }
  }
  /**
   * <code>required string power = 2;</code>
   * @return The bytes for power.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getPowerBytes() {
    java.lang.Object ref = power_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      power_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasBossId()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasPower()) {
      memoizedIsInitialized = 0;
      return false;
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, bossId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 2, power_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, bossId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(2, power_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.RuleTrialBossPowerData)) {
      return super.equals(obj);
    }
    xddq.pb.RuleTrialBossPowerData other = (xddq.pb.RuleTrialBossPowerData) obj;

    if (hasBossId() != other.hasBossId()) return false;
    if (hasBossId()) {
      if (getBossId()
          != other.getBossId()) return false;
    }
    if (hasPower() != other.hasPower()) return false;
    if (hasPower()) {
      if (!getPower()
          .equals(other.getPower())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasBossId()) {
      hash = (37 * hash) + BOSSID_FIELD_NUMBER;
      hash = (53 * hash) + getBossId();
    }
    if (hasPower()) {
      hash = (37 * hash) + POWER_FIELD_NUMBER;
      hash = (53 * hash) + getPower().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.RuleTrialBossPowerData parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.RuleTrialBossPowerData parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.RuleTrialBossPowerData parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.RuleTrialBossPowerData parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.RuleTrialBossPowerData parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.RuleTrialBossPowerData parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.RuleTrialBossPowerData parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.RuleTrialBossPowerData parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.RuleTrialBossPowerData parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.RuleTrialBossPowerData parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.RuleTrialBossPowerData parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.RuleTrialBossPowerData parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.RuleTrialBossPowerData prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.RuleTrialBossPowerData}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.RuleTrialBossPowerData)
      xddq.pb.RuleTrialBossPowerDataOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_RuleTrialBossPowerData_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_RuleTrialBossPowerData_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.RuleTrialBossPowerData.class, xddq.pb.RuleTrialBossPowerData.Builder.class);
    }

    // Construct using xddq.pb.RuleTrialBossPowerData.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      bossId_ = 0;
      power_ = "";
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_RuleTrialBossPowerData_descriptor;
    }

    @java.lang.Override
    public xddq.pb.RuleTrialBossPowerData getDefaultInstanceForType() {
      return xddq.pb.RuleTrialBossPowerData.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.RuleTrialBossPowerData build() {
      xddq.pb.RuleTrialBossPowerData result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.RuleTrialBossPowerData buildPartial() {
      xddq.pb.RuleTrialBossPowerData result = new xddq.pb.RuleTrialBossPowerData(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.RuleTrialBossPowerData result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.bossId_ = bossId_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.power_ = power_;
        to_bitField0_ |= 0x00000002;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.RuleTrialBossPowerData) {
        return mergeFrom((xddq.pb.RuleTrialBossPowerData)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.RuleTrialBossPowerData other) {
      if (other == xddq.pb.RuleTrialBossPowerData.getDefaultInstance()) return this;
      if (other.hasBossId()) {
        setBossId(other.getBossId());
      }
      if (other.hasPower()) {
        power_ = other.power_;
        bitField0_ |= 0x00000002;
        onChanged();
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasBossId()) {
        return false;
      }
      if (!hasPower()) {
        return false;
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bossId_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              power_ = input.readBytes();
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int bossId_ ;
    /**
     * <code>required int32 bossId = 1;</code>
     * @return Whether the bossId field is set.
     */
    @java.lang.Override
    public boolean hasBossId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 bossId = 1;</code>
     * @return The bossId.
     */
    @java.lang.Override
    public int getBossId() {
      return bossId_;
    }
    /**
     * <code>required int32 bossId = 1;</code>
     * @param value The bossId to set.
     * @return This builder for chaining.
     */
    public Builder setBossId(int value) {

      bossId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 bossId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearBossId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      bossId_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object power_ = "";
    /**
     * <code>required string power = 2;</code>
     * @return Whether the power field is set.
     */
    public boolean hasPower() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>required string power = 2;</code>
     * @return The power.
     */
    public java.lang.String getPower() {
      java.lang.Object ref = power_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          power_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>required string power = 2;</code>
     * @return The bytes for power.
     */
    public com.google.protobuf.ByteString
        getPowerBytes() {
      java.lang.Object ref = power_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        power_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>required string power = 2;</code>
     * @param value The power to set.
     * @return This builder for chaining.
     */
    public Builder setPower(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      power_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>required string power = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearPower() {
      power_ = getDefaultInstance().getPower();
      bitField0_ = (bitField0_ & ~0x00000002);
      onChanged();
      return this;
    }
    /**
     * <code>required string power = 2;</code>
     * @param value The bytes for power to set.
     * @return This builder for chaining.
     */
    public Builder setPowerBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      power_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.RuleTrialBossPowerData)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.RuleTrialBossPowerData)
  private static final xddq.pb.RuleTrialBossPowerData DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.RuleTrialBossPowerData();
  }

  public static xddq.pb.RuleTrialBossPowerData getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<RuleTrialBossPowerData>
      PARSER = new com.google.protobuf.AbstractParser<RuleTrialBossPowerData>() {
    @java.lang.Override
    public RuleTrialBossPowerData parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<RuleTrialBossPowerData> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<RuleTrialBossPowerData> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.RuleTrialBossPowerData getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

