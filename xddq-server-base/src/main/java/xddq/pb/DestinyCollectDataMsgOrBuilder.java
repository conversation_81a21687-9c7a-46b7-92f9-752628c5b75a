// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

public interface DestinyCollectDataMsgOrBuilder extends
    // @@protoc_insertion_point(interface_extends:xddq.pb.DestinyCollectDataMsg)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>required int32 destinyId = 1;</code>
   * @return Whether the destinyId field is set.
   */
  boolean hasDestinyId();
  /**
   * <code>required int32 destinyId = 1;</code>
   * @return The destinyId.
   */
  int getDestinyId();

  /**
   * <code>repeated .xddq.pb.DestinyCollectCardDataMsg destinyCollectCardData = 2;</code>
   */
  java.util.List<xddq.pb.DestinyCollectCardDataMsg> 
      getDestinyCollectCardDataList();
  /**
   * <code>repeated .xddq.pb.DestinyCollectCardDataMsg destinyCollectCardData = 2;</code>
   */
  xddq.pb.DestinyCollectCardDataMsg getDestinyCollectCardData(int index);
  /**
   * <code>repeated .xddq.pb.DestinyCollectCardDataMsg destinyCollectCardData = 2;</code>
   */
  int getDestinyCollectCardDataCount();
  /**
   * <code>repeated .xddq.pb.DestinyCollectCardDataMsg destinyCollectCardData = 2;</code>
   */
  java.util.List<? extends xddq.pb.DestinyCollectCardDataMsgOrBuilder> 
      getDestinyCollectCardDataOrBuilderList();
  /**
   * <code>repeated .xddq.pb.DestinyCollectCardDataMsg destinyCollectCardData = 2;</code>
   */
  xddq.pb.DestinyCollectCardDataMsgOrBuilder getDestinyCollectCardDataOrBuilder(
      int index);

  /**
   * <code>repeated .xddq.pb.DestinyCollectCardGroupDataMsg destinyCollectCardGroupDataMsg = 3;</code>
   */
  java.util.List<xddq.pb.DestinyCollectCardGroupDataMsg> 
      getDestinyCollectCardGroupDataMsgList();
  /**
   * <code>repeated .xddq.pb.DestinyCollectCardGroupDataMsg destinyCollectCardGroupDataMsg = 3;</code>
   */
  xddq.pb.DestinyCollectCardGroupDataMsg getDestinyCollectCardGroupDataMsg(int index);
  /**
   * <code>repeated .xddq.pb.DestinyCollectCardGroupDataMsg destinyCollectCardGroupDataMsg = 3;</code>
   */
  int getDestinyCollectCardGroupDataMsgCount();
  /**
   * <code>repeated .xddq.pb.DestinyCollectCardGroupDataMsg destinyCollectCardGroupDataMsg = 3;</code>
   */
  java.util.List<? extends xddq.pb.DestinyCollectCardGroupDataMsgOrBuilder> 
      getDestinyCollectCardGroupDataMsgOrBuilderList();
  /**
   * <code>repeated .xddq.pb.DestinyCollectCardGroupDataMsg destinyCollectCardGroupDataMsg = 3;</code>
   */
  xddq.pb.DestinyCollectCardGroupDataMsgOrBuilder getDestinyCollectCardGroupDataMsgOrBuilder(
      int index);
}
