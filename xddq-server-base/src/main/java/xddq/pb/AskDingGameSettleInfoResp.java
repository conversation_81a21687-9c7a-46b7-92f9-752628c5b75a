// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.AskDingGameSettleInfoResp}
 */
public final class AskDingGameSettleInfoResp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.AskDingGameSettleInfoResp)
    AskDingGameSettleInfoRespOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      AskDingGameSettleInfoResp.class.getName());
  }
  // Use AskDingGameSettleInfoResp.newBuilder() to construct.
  private AskDingGameSettleInfoResp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private AskDingGameSettleInfoResp() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_AskDingGameSettleInfoResp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_AskDingGameSettleInfoResp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.AskDingGameSettleInfoResp.class, xddq.pb.AskDingGameSettleInfoResp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>optional int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int GAMEID_FIELD_NUMBER = 2;
  private long gameId_ = 0L;
  /**
   * <code>optional int64 gameId = 2;</code>
   * @return Whether the gameId field is set.
   */
  @java.lang.Override
  public boolean hasGameId() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int64 gameId = 2;</code>
   * @return The gameId.
   */
  @java.lang.Override
  public long getGameId() {
    return gameId_;
  }

  public static final int ENDDATA_FIELD_NUMBER = 3;
  private xddq.pb.AskDingEndDataMsg endData_;
  /**
   * <code>optional .xddq.pb.AskDingEndDataMsg endData = 3;</code>
   * @return Whether the endData field is set.
   */
  @java.lang.Override
  public boolean hasEndData() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional .xddq.pb.AskDingEndDataMsg endData = 3;</code>
   * @return The endData.
   */
  @java.lang.Override
  public xddq.pb.AskDingEndDataMsg getEndData() {
    return endData_ == null ? xddq.pb.AskDingEndDataMsg.getDefaultInstance() : endData_;
  }
  /**
   * <code>optional .xddq.pb.AskDingEndDataMsg endData = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.AskDingEndDataMsgOrBuilder getEndDataOrBuilder() {
    return endData_ == null ? xddq.pb.AskDingEndDataMsg.getDefaultInstance() : endData_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt64(2, gameId_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeMessage(3, getEndData());
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(2, gameId_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, getEndData());
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.AskDingGameSettleInfoResp)) {
      return super.equals(obj);
    }
    xddq.pb.AskDingGameSettleInfoResp other = (xddq.pb.AskDingGameSettleInfoResp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (hasGameId() != other.hasGameId()) return false;
    if (hasGameId()) {
      if (getGameId()
          != other.getGameId()) return false;
    }
    if (hasEndData() != other.hasEndData()) return false;
    if (hasEndData()) {
      if (!getEndData()
          .equals(other.getEndData())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (hasGameId()) {
      hash = (37 * hash) + GAMEID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getGameId());
    }
    if (hasEndData()) {
      hash = (37 * hash) + ENDDATA_FIELD_NUMBER;
      hash = (53 * hash) + getEndData().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.AskDingGameSettleInfoResp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.AskDingGameSettleInfoResp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.AskDingGameSettleInfoResp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.AskDingGameSettleInfoResp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.AskDingGameSettleInfoResp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.AskDingGameSettleInfoResp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.AskDingGameSettleInfoResp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.AskDingGameSettleInfoResp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.AskDingGameSettleInfoResp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.AskDingGameSettleInfoResp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.AskDingGameSettleInfoResp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.AskDingGameSettleInfoResp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.AskDingGameSettleInfoResp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.AskDingGameSettleInfoResp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.AskDingGameSettleInfoResp)
      xddq.pb.AskDingGameSettleInfoRespOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_AskDingGameSettleInfoResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_AskDingGameSettleInfoResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.AskDingGameSettleInfoResp.class, xddq.pb.AskDingGameSettleInfoResp.Builder.class);
    }

    // Construct using xddq.pb.AskDingGameSettleInfoResp.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetEndDataFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      gameId_ = 0L;
      endData_ = null;
      if (endDataBuilder_ != null) {
        endDataBuilder_.dispose();
        endDataBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_AskDingGameSettleInfoResp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.AskDingGameSettleInfoResp getDefaultInstanceForType() {
      return xddq.pb.AskDingGameSettleInfoResp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.AskDingGameSettleInfoResp build() {
      xddq.pb.AskDingGameSettleInfoResp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.AskDingGameSettleInfoResp buildPartial() {
      xddq.pb.AskDingGameSettleInfoResp result = new xddq.pb.AskDingGameSettleInfoResp(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.AskDingGameSettleInfoResp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.gameId_ = gameId_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.endData_ = endDataBuilder_ == null
            ? endData_
            : endDataBuilder_.build();
        to_bitField0_ |= 0x00000004;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.AskDingGameSettleInfoResp) {
        return mergeFrom((xddq.pb.AskDingGameSettleInfoResp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.AskDingGameSettleInfoResp other) {
      if (other == xddq.pb.AskDingGameSettleInfoResp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (other.hasGameId()) {
        setGameId(other.getGameId());
      }
      if (other.hasEndData()) {
        mergeEndData(other.getEndData());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              gameId_ = input.readInt64();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 26: {
              input.readMessage(
                  internalGetEndDataFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>optional int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>optional int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private long gameId_ ;
    /**
     * <code>optional int64 gameId = 2;</code>
     * @return Whether the gameId field is set.
     */
    @java.lang.Override
    public boolean hasGameId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int64 gameId = 2;</code>
     * @return The gameId.
     */
    @java.lang.Override
    public long getGameId() {
      return gameId_;
    }
    /**
     * <code>optional int64 gameId = 2;</code>
     * @param value The gameId to set.
     * @return This builder for chaining.
     */
    public Builder setGameId(long value) {

      gameId_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 gameId = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearGameId() {
      bitField0_ = (bitField0_ & ~0x00000002);
      gameId_ = 0L;
      onChanged();
      return this;
    }

    private xddq.pb.AskDingEndDataMsg endData_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.AskDingEndDataMsg, xddq.pb.AskDingEndDataMsg.Builder, xddq.pb.AskDingEndDataMsgOrBuilder> endDataBuilder_;
    /**
     * <code>optional .xddq.pb.AskDingEndDataMsg endData = 3;</code>
     * @return Whether the endData field is set.
     */
    public boolean hasEndData() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional .xddq.pb.AskDingEndDataMsg endData = 3;</code>
     * @return The endData.
     */
    public xddq.pb.AskDingEndDataMsg getEndData() {
      if (endDataBuilder_ == null) {
        return endData_ == null ? xddq.pb.AskDingEndDataMsg.getDefaultInstance() : endData_;
      } else {
        return endDataBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.AskDingEndDataMsg endData = 3;</code>
     */
    public Builder setEndData(xddq.pb.AskDingEndDataMsg value) {
      if (endDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        endData_ = value;
      } else {
        endDataBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.AskDingEndDataMsg endData = 3;</code>
     */
    public Builder setEndData(
        xddq.pb.AskDingEndDataMsg.Builder builderForValue) {
      if (endDataBuilder_ == null) {
        endData_ = builderForValue.build();
      } else {
        endDataBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.AskDingEndDataMsg endData = 3;</code>
     */
    public Builder mergeEndData(xddq.pb.AskDingEndDataMsg value) {
      if (endDataBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0) &&
          endData_ != null &&
          endData_ != xddq.pb.AskDingEndDataMsg.getDefaultInstance()) {
          getEndDataBuilder().mergeFrom(value);
        } else {
          endData_ = value;
        }
      } else {
        endDataBuilder_.mergeFrom(value);
      }
      if (endData_ != null) {
        bitField0_ |= 0x00000004;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.AskDingEndDataMsg endData = 3;</code>
     */
    public Builder clearEndData() {
      bitField0_ = (bitField0_ & ~0x00000004);
      endData_ = null;
      if (endDataBuilder_ != null) {
        endDataBuilder_.dispose();
        endDataBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.AskDingEndDataMsg endData = 3;</code>
     */
    public xddq.pb.AskDingEndDataMsg.Builder getEndDataBuilder() {
      bitField0_ |= 0x00000004;
      onChanged();
      return internalGetEndDataFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.AskDingEndDataMsg endData = 3;</code>
     */
    public xddq.pb.AskDingEndDataMsgOrBuilder getEndDataOrBuilder() {
      if (endDataBuilder_ != null) {
        return endDataBuilder_.getMessageOrBuilder();
      } else {
        return endData_ == null ?
            xddq.pb.AskDingEndDataMsg.getDefaultInstance() : endData_;
      }
    }
    /**
     * <code>optional .xddq.pb.AskDingEndDataMsg endData = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.AskDingEndDataMsg, xddq.pb.AskDingEndDataMsg.Builder, xddq.pb.AskDingEndDataMsgOrBuilder> 
        internalGetEndDataFieldBuilder() {
      if (endDataBuilder_ == null) {
        endDataBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.AskDingEndDataMsg, xddq.pb.AskDingEndDataMsg.Builder, xddq.pb.AskDingEndDataMsgOrBuilder>(
                getEndData(),
                getParentForChildren(),
                isClean());
        endData_ = null;
      }
      return endDataBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.AskDingGameSettleInfoResp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.AskDingGameSettleInfoResp)
  private static final xddq.pb.AskDingGameSettleInfoResp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.AskDingGameSettleInfoResp();
  }

  public static xddq.pb.AskDingGameSettleInfoResp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<AskDingGameSettleInfoResp>
      PARSER = new com.google.protobuf.AbstractParser<AskDingGameSettleInfoResp>() {
    @java.lang.Override
    public AskDingGameSettleInfoResp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<AskDingGameSettleInfoResp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<AskDingGameSettleInfoResp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.AskDingGameSettleInfoResp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

