// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.WarSeasonGetGodBodyDataResp}
 */
public final class WarSeasonGetGodBodyDataResp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.WarSeasonGetGodBodyDataResp)
    WarSeasonGetGodBodyDataRespOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      WarSeasonGetGodBodyDataResp.class.getName());
  }
  // Use WarSeasonGetGodBodyDataResp.newBuilder() to construct.
  private WarSeasonGetGodBodyDataResp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private WarSeasonGetGodBodyDataResp() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_WarSeasonGetGodBodyDataResp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_WarSeasonGetGodBodyDataResp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.WarSeasonGetGodBodyDataResp.class, xddq.pb.WarSeasonGetGodBodyDataResp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int DATA_FIELD_NUMBER = 2;
  private xddq.pb.GodBodyDetailDataMsg data_;
  /**
   * <code>optional .xddq.pb.GodBodyDetailDataMsg data = 2;</code>
   * @return Whether the data field is set.
   */
  @java.lang.Override
  public boolean hasData() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional .xddq.pb.GodBodyDetailDataMsg data = 2;</code>
   * @return The data.
   */
  @java.lang.Override
  public xddq.pb.GodBodyDetailDataMsg getData() {
    return data_ == null ? xddq.pb.GodBodyDetailDataMsg.getDefaultInstance() : data_;
  }
  /**
   * <code>optional .xddq.pb.GodBodyDetailDataMsg data = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.GodBodyDetailDataMsgOrBuilder getDataOrBuilder() {
    return data_ == null ? xddq.pb.GodBodyDetailDataMsg.getDefaultInstance() : data_;
  }

  public static final int SEASONEXP_FIELD_NUMBER = 3;
  private long seasonExp_ = 0L;
  /**
   * <code>optional int64 seasonExp = 3;</code>
   * @return Whether the seasonExp field is set.
   */
  @java.lang.Override
  public boolean hasSeasonExp() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional int64 seasonExp = 3;</code>
   * @return The seasonExp.
   */
  @java.lang.Override
  public long getSeasonExp() {
    return seasonExp_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (hasData()) {
      if (!getData().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeMessage(2, getData());
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt64(3, seasonExp_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getData());
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(3, seasonExp_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.WarSeasonGetGodBodyDataResp)) {
      return super.equals(obj);
    }
    xddq.pb.WarSeasonGetGodBodyDataResp other = (xddq.pb.WarSeasonGetGodBodyDataResp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (hasData() != other.hasData()) return false;
    if (hasData()) {
      if (!getData()
          .equals(other.getData())) return false;
    }
    if (hasSeasonExp() != other.hasSeasonExp()) return false;
    if (hasSeasonExp()) {
      if (getSeasonExp()
          != other.getSeasonExp()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (hasData()) {
      hash = (37 * hash) + DATA_FIELD_NUMBER;
      hash = (53 * hash) + getData().hashCode();
    }
    if (hasSeasonExp()) {
      hash = (37 * hash) + SEASONEXP_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getSeasonExp());
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.WarSeasonGetGodBodyDataResp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.WarSeasonGetGodBodyDataResp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.WarSeasonGetGodBodyDataResp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.WarSeasonGetGodBodyDataResp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.WarSeasonGetGodBodyDataResp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.WarSeasonGetGodBodyDataResp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.WarSeasonGetGodBodyDataResp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.WarSeasonGetGodBodyDataResp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.WarSeasonGetGodBodyDataResp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.WarSeasonGetGodBodyDataResp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.WarSeasonGetGodBodyDataResp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.WarSeasonGetGodBodyDataResp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.WarSeasonGetGodBodyDataResp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.WarSeasonGetGodBodyDataResp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.WarSeasonGetGodBodyDataResp)
      xddq.pb.WarSeasonGetGodBodyDataRespOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_WarSeasonGetGodBodyDataResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_WarSeasonGetGodBodyDataResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.WarSeasonGetGodBodyDataResp.class, xddq.pb.WarSeasonGetGodBodyDataResp.Builder.class);
    }

    // Construct using xddq.pb.WarSeasonGetGodBodyDataResp.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetDataFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      data_ = null;
      if (dataBuilder_ != null) {
        dataBuilder_.dispose();
        dataBuilder_ = null;
      }
      seasonExp_ = 0L;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_WarSeasonGetGodBodyDataResp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.WarSeasonGetGodBodyDataResp getDefaultInstanceForType() {
      return xddq.pb.WarSeasonGetGodBodyDataResp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.WarSeasonGetGodBodyDataResp build() {
      xddq.pb.WarSeasonGetGodBodyDataResp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.WarSeasonGetGodBodyDataResp buildPartial() {
      xddq.pb.WarSeasonGetGodBodyDataResp result = new xddq.pb.WarSeasonGetGodBodyDataResp(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.WarSeasonGetGodBodyDataResp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.data_ = dataBuilder_ == null
            ? data_
            : dataBuilder_.build();
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.seasonExp_ = seasonExp_;
        to_bitField0_ |= 0x00000004;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.WarSeasonGetGodBodyDataResp) {
        return mergeFrom((xddq.pb.WarSeasonGetGodBodyDataResp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.WarSeasonGetGodBodyDataResp other) {
      if (other == xddq.pb.WarSeasonGetGodBodyDataResp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (other.hasData()) {
        mergeData(other.getData());
      }
      if (other.hasSeasonExp()) {
        setSeasonExp(other.getSeasonExp());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      if (hasData()) {
        if (!getData().isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              input.readMessage(
                  internalGetDataFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 24: {
              seasonExp_ = input.readInt64();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private xddq.pb.GodBodyDetailDataMsg data_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.GodBodyDetailDataMsg, xddq.pb.GodBodyDetailDataMsg.Builder, xddq.pb.GodBodyDetailDataMsgOrBuilder> dataBuilder_;
    /**
     * <code>optional .xddq.pb.GodBodyDetailDataMsg data = 2;</code>
     * @return Whether the data field is set.
     */
    public boolean hasData() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .xddq.pb.GodBodyDetailDataMsg data = 2;</code>
     * @return The data.
     */
    public xddq.pb.GodBodyDetailDataMsg getData() {
      if (dataBuilder_ == null) {
        return data_ == null ? xddq.pb.GodBodyDetailDataMsg.getDefaultInstance() : data_;
      } else {
        return dataBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.GodBodyDetailDataMsg data = 2;</code>
     */
    public Builder setData(xddq.pb.GodBodyDetailDataMsg value) {
      if (dataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        data_ = value;
      } else {
        dataBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.GodBodyDetailDataMsg data = 2;</code>
     */
    public Builder setData(
        xddq.pb.GodBodyDetailDataMsg.Builder builderForValue) {
      if (dataBuilder_ == null) {
        data_ = builderForValue.build();
      } else {
        dataBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.GodBodyDetailDataMsg data = 2;</code>
     */
    public Builder mergeData(xddq.pb.GodBodyDetailDataMsg value) {
      if (dataBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0) &&
          data_ != null &&
          data_ != xddq.pb.GodBodyDetailDataMsg.getDefaultInstance()) {
          getDataBuilder().mergeFrom(value);
        } else {
          data_ = value;
        }
      } else {
        dataBuilder_.mergeFrom(value);
      }
      if (data_ != null) {
        bitField0_ |= 0x00000002;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.GodBodyDetailDataMsg data = 2;</code>
     */
    public Builder clearData() {
      bitField0_ = (bitField0_ & ~0x00000002);
      data_ = null;
      if (dataBuilder_ != null) {
        dataBuilder_.dispose();
        dataBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.GodBodyDetailDataMsg data = 2;</code>
     */
    public xddq.pb.GodBodyDetailDataMsg.Builder getDataBuilder() {
      bitField0_ |= 0x00000002;
      onChanged();
      return internalGetDataFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.GodBodyDetailDataMsg data = 2;</code>
     */
    public xddq.pb.GodBodyDetailDataMsgOrBuilder getDataOrBuilder() {
      if (dataBuilder_ != null) {
        return dataBuilder_.getMessageOrBuilder();
      } else {
        return data_ == null ?
            xddq.pb.GodBodyDetailDataMsg.getDefaultInstance() : data_;
      }
    }
    /**
     * <code>optional .xddq.pb.GodBodyDetailDataMsg data = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.GodBodyDetailDataMsg, xddq.pb.GodBodyDetailDataMsg.Builder, xddq.pb.GodBodyDetailDataMsgOrBuilder> 
        internalGetDataFieldBuilder() {
      if (dataBuilder_ == null) {
        dataBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.GodBodyDetailDataMsg, xddq.pb.GodBodyDetailDataMsg.Builder, xddq.pb.GodBodyDetailDataMsgOrBuilder>(
                getData(),
                getParentForChildren(),
                isClean());
        data_ = null;
      }
      return dataBuilder_;
    }

    private long seasonExp_ ;
    /**
     * <code>optional int64 seasonExp = 3;</code>
     * @return Whether the seasonExp field is set.
     */
    @java.lang.Override
    public boolean hasSeasonExp() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int64 seasonExp = 3;</code>
     * @return The seasonExp.
     */
    @java.lang.Override
    public long getSeasonExp() {
      return seasonExp_;
    }
    /**
     * <code>optional int64 seasonExp = 3;</code>
     * @param value The seasonExp to set.
     * @return This builder for chaining.
     */
    public Builder setSeasonExp(long value) {

      seasonExp_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 seasonExp = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearSeasonExp() {
      bitField0_ = (bitField0_ & ~0x00000004);
      seasonExp_ = 0L;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.WarSeasonGetGodBodyDataResp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.WarSeasonGetGodBodyDataResp)
  private static final xddq.pb.WarSeasonGetGodBodyDataResp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.WarSeasonGetGodBodyDataResp();
  }

  public static xddq.pb.WarSeasonGetGodBodyDataResp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<WarSeasonGetGodBodyDataResp>
      PARSER = new com.google.protobuf.AbstractParser<WarSeasonGetGodBodyDataResp>() {
    @java.lang.Override
    public WarSeasonGetGodBodyDataResp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<WarSeasonGetGodBodyDataResp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<WarSeasonGetGodBodyDataResp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.WarSeasonGetGodBodyDataResp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

