// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.ElementalBondsUserDataMsg}
 */
public final class ElementalBondsUserDataMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.ElementalBondsUserDataMsg)
    ElementalBondsUserDataMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      ElementalBondsUserDataMsg.class.getName());
  }
  // Use ElementalBondsUserDataMsg.newBuilder() to construct.
  private ElementalBondsUserDataMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private ElementalBondsUserDataMsg() {
    unlockCard_ = emptyIntList();
    battlePass_ = emptyIntList();
    mall_ = java.util.Collections.emptyList();
    todaySceneMode_ = emptyIntList();
    unlockSkill_ = emptyIntList();
    selectedSkill_ = emptyIntList();
    equipEmoji_ = emptyIntList();
    systemOpenValue_ = "";
    privilegeCardMsg_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_ElementalBondsUserDataMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_ElementalBondsUserDataMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.ElementalBondsUserDataMsg.class, xddq.pb.ElementalBondsUserDataMsg.Builder.class);
  }

  private int bitField0_;
  public static final int UNLOCKCARD_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private com.google.protobuf.Internal.IntList unlockCard_ =
      emptyIntList();
  /**
   * <code>repeated int32 unlockCard = 1;</code>
   * @return A list containing the unlockCard.
   */
  @java.lang.Override
  public java.util.List<java.lang.Integer>
      getUnlockCardList() {
    return unlockCard_;
  }
  /**
   * <code>repeated int32 unlockCard = 1;</code>
   * @return The count of unlockCard.
   */
  public int getUnlockCardCount() {
    return unlockCard_.size();
  }
  /**
   * <code>repeated int32 unlockCard = 1;</code>
   * @param index The index of the element to return.
   * @return The unlockCard at the given index.
   */
  public int getUnlockCard(int index) {
    return unlockCard_.getInt(index);
  }

  public static final int SCORE_FIELD_NUMBER = 2;
  private long score_ = 0L;
  /**
   * <code>required int64 score = 2;</code>
   * @return Whether the score field is set.
   */
  @java.lang.Override
  public boolean hasScore() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int64 score = 2;</code>
   * @return The score.
   */
  @java.lang.Override
  public long getScore() {
    return score_;
  }

  public static final int BATTLEPASS_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private com.google.protobuf.Internal.IntList battlePass_ =
      emptyIntList();
  /**
   * <code>repeated int32 battlePass = 3;</code>
   * @return A list containing the battlePass.
   */
  @java.lang.Override
  public java.util.List<java.lang.Integer>
      getBattlePassList() {
    return battlePass_;
  }
  /**
   * <code>repeated int32 battlePass = 3;</code>
   * @return The count of battlePass.
   */
  public int getBattlePassCount() {
    return battlePass_.size();
  }
  /**
   * <code>repeated int32 battlePass = 3;</code>
   * @param index The index of the element to return.
   * @return The battlePass at the given index.
   */
  public int getBattlePass(int index) {
    return battlePass_.getInt(index);
  }

  public static final int DATASEASON_FIELD_NUMBER = 4;
  private int dataSeason_ = 0;
  /**
   * <code>required int32 dataSeason = 4;</code>
   * @return Whether the dataSeason field is set.
   */
  @java.lang.Override
  public boolean hasDataSeason() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>required int32 dataSeason = 4;</code>
   * @return The dataSeason.
   */
  @java.lang.Override
  public int getDataSeason() {
    return dataSeason_;
  }

  public static final int BUFFCOLLECTTIMES_FIELD_NUMBER = 5;
  private int buffCollectTimes_ = 0;
  /**
   * <code>required int32 buffCollectTimes = 5;</code>
   * @return Whether the buffCollectTimes field is set.
   */
  @java.lang.Override
  public boolean hasBuffCollectTimes() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>required int32 buffCollectTimes = 5;</code>
   * @return The buffCollectTimes.
   */
  @java.lang.Override
  public int getBuffCollectTimes() {
    return buffCollectTimes_;
  }

  public static final int GUIDE_FIELD_NUMBER = 6;
  private boolean guide_ = false;
  /**
   * <code>required bool guide = 6;</code>
   * @return Whether the guide field is set.
   */
  @java.lang.Override
  public boolean hasGuide() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>required bool guide = 6;</code>
   * @return The guide.
   */
  @java.lang.Override
  public boolean getGuide() {
    return guide_;
  }

  public static final int OVERMSG_FIELD_NUMBER = 7;
  private xddq.pb.ElementalBondsGameOverMsg overMsg_;
  /**
   * <code>optional .xddq.pb.ElementalBondsGameOverMsg overMsg = 7;</code>
   * @return Whether the overMsg field is set.
   */
  @java.lang.Override
  public boolean hasOverMsg() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>optional .xddq.pb.ElementalBondsGameOverMsg overMsg = 7;</code>
   * @return The overMsg.
   */
  @java.lang.Override
  public xddq.pb.ElementalBondsGameOverMsg getOverMsg() {
    return overMsg_ == null ? xddq.pb.ElementalBondsGameOverMsg.getDefaultInstance() : overMsg_;
  }
  /**
   * <code>optional .xddq.pb.ElementalBondsGameOverMsg overMsg = 7;</code>
   */
  @java.lang.Override
  public xddq.pb.ElementalBondsGameOverMsgOrBuilder getOverMsgOrBuilder() {
    return overMsg_ == null ? xddq.pb.ElementalBondsGameOverMsg.getDefaultInstance() : overMsg_;
  }

  public static final int MALL_FIELD_NUMBER = 8;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.ElementalBondsMallMsg> mall_;
  /**
   * <code>repeated .xddq.pb.ElementalBondsMallMsg mall = 8;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.ElementalBondsMallMsg> getMallList() {
    return mall_;
  }
  /**
   * <code>repeated .xddq.pb.ElementalBondsMallMsg mall = 8;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.ElementalBondsMallMsgOrBuilder> 
      getMallOrBuilderList() {
    return mall_;
  }
  /**
   * <code>repeated .xddq.pb.ElementalBondsMallMsg mall = 8;</code>
   */
  @java.lang.Override
  public int getMallCount() {
    return mall_.size();
  }
  /**
   * <code>repeated .xddq.pb.ElementalBondsMallMsg mall = 8;</code>
   */
  @java.lang.Override
  public xddq.pb.ElementalBondsMallMsg getMall(int index) {
    return mall_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.ElementalBondsMallMsg mall = 8;</code>
   */
  @java.lang.Override
  public xddq.pb.ElementalBondsMallMsgOrBuilder getMallOrBuilder(
      int index) {
    return mall_.get(index);
  }

  public static final int GUIDE2_FIELD_NUMBER = 9;
  private boolean guide2_ = false;
  /**
   * <code>optional bool guide2 = 9;</code>
   * @return Whether the guide2 field is set.
   */
  @java.lang.Override
  public boolean hasGuide2() {
    return ((bitField0_ & 0x00000020) != 0);
  }
  /**
   * <code>optional bool guide2 = 9;</code>
   * @return The guide2.
   */
  @java.lang.Override
  public boolean getGuide2() {
    return guide2_;
  }

  public static final int UNLOCKSCENEMODE_FIELD_NUMBER = 10;
  private boolean unlockSceneMode_ = false;
  /**
   * <code>optional bool unlockSceneMode = 10;</code>
   * @return Whether the unlockSceneMode field is set.
   */
  @java.lang.Override
  public boolean hasUnlockSceneMode() {
    return ((bitField0_ & 0x00000040) != 0);
  }
  /**
   * <code>optional bool unlockSceneMode = 10;</code>
   * @return The unlockSceneMode.
   */
  @java.lang.Override
  public boolean getUnlockSceneMode() {
    return unlockSceneMode_;
  }

  public static final int TODAYSCENEMODE_FIELD_NUMBER = 11;
  @SuppressWarnings("serial")
  private com.google.protobuf.Internal.IntList todaySceneMode_ =
      emptyIntList();
  /**
   * <code>repeated int32 todaySceneMode = 11;</code>
   * @return A list containing the todaySceneMode.
   */
  @java.lang.Override
  public java.util.List<java.lang.Integer>
      getTodaySceneModeList() {
    return todaySceneMode_;
  }
  /**
   * <code>repeated int32 todaySceneMode = 11;</code>
   * @return The count of todaySceneMode.
   */
  public int getTodaySceneModeCount() {
    return todaySceneMode_.size();
  }
  /**
   * <code>repeated int32 todaySceneMode = 11;</code>
   * @param index The index of the element to return.
   * @return The todaySceneMode at the given index.
   */
  public int getTodaySceneMode(int index) {
    return todaySceneMode_.getInt(index);
  }

  public static final int CANSURRENDERTIMES_FIELD_NUMBER = 12;
  private int canSurrenderTimes_ = 0;
  /**
   * <code>optional int32 canSurrenderTimes = 12;</code>
   * @return Whether the canSurrenderTimes field is set.
   */
  @java.lang.Override
  public boolean hasCanSurrenderTimes() {
    return ((bitField0_ & 0x00000080) != 0);
  }
  /**
   * <code>optional int32 canSurrenderTimes = 12;</code>
   * @return The canSurrenderTimes.
   */
  @java.lang.Override
  public int getCanSurrenderTimes() {
    return canSurrenderTimes_;
  }

  public static final int UNLOCKSKILL_FIELD_NUMBER = 13;
  @SuppressWarnings("serial")
  private com.google.protobuf.Internal.IntList unlockSkill_ =
      emptyIntList();
  /**
   * <code>repeated int32 unlockSkill = 13;</code>
   * @return A list containing the unlockSkill.
   */
  @java.lang.Override
  public java.util.List<java.lang.Integer>
      getUnlockSkillList() {
    return unlockSkill_;
  }
  /**
   * <code>repeated int32 unlockSkill = 13;</code>
   * @return The count of unlockSkill.
   */
  public int getUnlockSkillCount() {
    return unlockSkill_.size();
  }
  /**
   * <code>repeated int32 unlockSkill = 13;</code>
   * @param index The index of the element to return.
   * @return The unlockSkill at the given index.
   */
  public int getUnlockSkill(int index) {
    return unlockSkill_.getInt(index);
  }

  public static final int SELECTEDSKILL_FIELD_NUMBER = 14;
  @SuppressWarnings("serial")
  private com.google.protobuf.Internal.IntList selectedSkill_ =
      emptyIntList();
  /**
   * <code>repeated int32 selectedSkill = 14;</code>
   * @return A list containing the selectedSkill.
   */
  @java.lang.Override
  public java.util.List<java.lang.Integer>
      getSelectedSkillList() {
    return selectedSkill_;
  }
  /**
   * <code>repeated int32 selectedSkill = 14;</code>
   * @return The count of selectedSkill.
   */
  public int getSelectedSkillCount() {
    return selectedSkill_.size();
  }
  /**
   * <code>repeated int32 selectedSkill = 14;</code>
   * @param index The index of the element to return.
   * @return The selectedSkill at the given index.
   */
  public int getSelectedSkill(int index) {
    return selectedSkill_.getInt(index);
  }

  public static final int LINKGUIDE_FIELD_NUMBER = 15;
  private boolean linkGuide_ = false;
  /**
   * <code>optional bool linkGuide = 15;</code>
   * @return Whether the linkGuide field is set.
   */
  @java.lang.Override
  public boolean hasLinkGuide() {
    return ((bitField0_ & 0x00000100) != 0);
  }
  /**
   * <code>optional bool linkGuide = 15;</code>
   * @return The linkGuide.
   */
  @java.lang.Override
  public boolean getLinkGuide() {
    return linkGuide_;
  }

  public static final int USEBOARDSKIN_FIELD_NUMBER = 16;
  private int useBoardSkin_ = 0;
  /**
   * <code>optional int32 useBoardSkin = 16;</code>
   * @return Whether the useBoardSkin field is set.
   */
  @java.lang.Override
  public boolean hasUseBoardSkin() {
    return ((bitField0_ & 0x00000200) != 0);
  }
  /**
   * <code>optional int32 useBoardSkin = 16;</code>
   * @return The useBoardSkin.
   */
  @java.lang.Override
  public int getUseBoardSkin() {
    return useBoardSkin_;
  }

  public static final int USEANIMATION_FIELD_NUMBER = 17;
  private int useAnimation_ = 0;
  /**
   * <code>optional int32 useAnimation = 17;</code>
   * @return Whether the useAnimation field is set.
   */
  @java.lang.Override
  public boolean hasUseAnimation() {
    return ((bitField0_ & 0x00000400) != 0);
  }
  /**
   * <code>optional int32 useAnimation = 17;</code>
   * @return The useAnimation.
   */
  @java.lang.Override
  public int getUseAnimation() {
    return useAnimation_;
  }

  public static final int USEEFFECT_FIELD_NUMBER = 18;
  private int useEffect_ = 0;
  /**
   * <code>optional int32 useEffect = 18;</code>
   * @return Whether the useEffect field is set.
   */
  @java.lang.Override
  public boolean hasUseEffect() {
    return ((bitField0_ & 0x00000800) != 0);
  }
  /**
   * <code>optional int32 useEffect = 18;</code>
   * @return The useEffect.
   */
  @java.lang.Override
  public int getUseEffect() {
    return useEffect_;
  }

  public static final int EQUIPEMOJI_FIELD_NUMBER = 20;
  @SuppressWarnings("serial")
  private com.google.protobuf.Internal.IntList equipEmoji_ =
      emptyIntList();
  /**
   * <code>repeated int32 equipEmoji = 20;</code>
   * @return A list containing the equipEmoji.
   */
  @java.lang.Override
  public java.util.List<java.lang.Integer>
      getEquipEmojiList() {
    return equipEmoji_;
  }
  /**
   * <code>repeated int32 equipEmoji = 20;</code>
   * @return The count of equipEmoji.
   */
  public int getEquipEmojiCount() {
    return equipEmoji_.size();
  }
  /**
   * <code>repeated int32 equipEmoji = 20;</code>
   * @param index The index of the element to return.
   * @return The equipEmoji at the given index.
   */
  public int getEquipEmoji(int index) {
    return equipEmoji_.getInt(index);
  }

  public static final int INGROUP_FIELD_NUMBER = 21;
  private boolean inGroup_ = false;
  /**
   * <code>optional bool inGroup = 21;</code>
   * @return Whether the inGroup field is set.
   */
  @java.lang.Override
  public boolean hasInGroup() {
    return ((bitField0_ & 0x00001000) != 0);
  }
  /**
   * <code>optional bool inGroup = 21;</code>
   * @return The inGroup.
   */
  @java.lang.Override
  public boolean getInGroup() {
    return inGroup_;
  }

  public static final int SYSTEMOPENVALUE_FIELD_NUMBER = 22;
  @SuppressWarnings("serial")
  private volatile java.lang.Object systemOpenValue_ = "";
  /**
   * <code>optional string systemOpenValue = 22;</code>
   * @return Whether the systemOpenValue field is set.
   */
  @java.lang.Override
  public boolean hasSystemOpenValue() {
    return ((bitField0_ & 0x00002000) != 0);
  }
  /**
   * <code>optional string systemOpenValue = 22;</code>
   * @return The systemOpenValue.
   */
  @java.lang.Override
  public java.lang.String getSystemOpenValue() {
    java.lang.Object ref = systemOpenValue_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        systemOpenValue_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string systemOpenValue = 22;</code>
   * @return The bytes for systemOpenValue.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getSystemOpenValueBytes() {
    java.lang.Object ref = systemOpenValue_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      systemOpenValue_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int LIMITTASKID_FIELD_NUMBER = 23;
  private int limitTaskId_ = 0;
  /**
   * <code>optional int32 limitTaskId = 23;</code>
   * @return Whether the limitTaskId field is set.
   */
  @java.lang.Override
  public boolean hasLimitTaskId() {
    return ((bitField0_ & 0x00004000) != 0);
  }
  /**
   * <code>optional int32 limitTaskId = 23;</code>
   * @return The limitTaskId.
   */
  @java.lang.Override
  public int getLimitTaskId() {
    return limitTaskId_;
  }

  public static final int PRIVILEGECARDMSG_FIELD_NUMBER = 24;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.ElementalBondsPrivilegeCardMsg> privilegeCardMsg_;
  /**
   * <code>repeated .xddq.pb.ElementalBondsPrivilegeCardMsg privilegeCardMsg = 24;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.ElementalBondsPrivilegeCardMsg> getPrivilegeCardMsgList() {
    return privilegeCardMsg_;
  }
  /**
   * <code>repeated .xddq.pb.ElementalBondsPrivilegeCardMsg privilegeCardMsg = 24;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.ElementalBondsPrivilegeCardMsgOrBuilder> 
      getPrivilegeCardMsgOrBuilderList() {
    return privilegeCardMsg_;
  }
  /**
   * <code>repeated .xddq.pb.ElementalBondsPrivilegeCardMsg privilegeCardMsg = 24;</code>
   */
  @java.lang.Override
  public int getPrivilegeCardMsgCount() {
    return privilegeCardMsg_.size();
  }
  /**
   * <code>repeated .xddq.pb.ElementalBondsPrivilegeCardMsg privilegeCardMsg = 24;</code>
   */
  @java.lang.Override
  public xddq.pb.ElementalBondsPrivilegeCardMsg getPrivilegeCardMsg(int index) {
    return privilegeCardMsg_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.ElementalBondsPrivilegeCardMsg privilegeCardMsg = 24;</code>
   */
  @java.lang.Override
  public xddq.pb.ElementalBondsPrivilegeCardMsgOrBuilder getPrivilegeCardMsgOrBuilder(
      int index) {
    return privilegeCardMsg_.get(index);
  }

  public static final int LIMITTASKENDTIME_FIELD_NUMBER = 25;
  private long limitTaskEndTime_ = 0L;
  /**
   * <code>optional int64 limitTaskEndTime = 25;</code>
   * @return Whether the limitTaskEndTime field is set.
   */
  @java.lang.Override
  public boolean hasLimitTaskEndTime() {
    return ((bitField0_ & 0x00008000) != 0);
  }
  /**
   * <code>optional int64 limitTaskEndTime = 25;</code>
   * @return The limitTaskEndTime.
   */
  @java.lang.Override
  public long getLimitTaskEndTime() {
    return limitTaskEndTime_;
  }

  public static final int CUR_FIELD_NUMBER = 26;
  private xddq.pb.ElementalBondsBadgeMsg cur_;
  /**
   * <code>optional .xddq.pb.ElementalBondsBadgeMsg cur = 26;</code>
   * @return Whether the cur field is set.
   */
  @java.lang.Override
  public boolean hasCur() {
    return ((bitField0_ & 0x00010000) != 0);
  }
  /**
   * <code>optional .xddq.pb.ElementalBondsBadgeMsg cur = 26;</code>
   * @return The cur.
   */
  @java.lang.Override
  public xddq.pb.ElementalBondsBadgeMsg getCur() {
    return cur_ == null ? xddq.pb.ElementalBondsBadgeMsg.getDefaultInstance() : cur_;
  }
  /**
   * <code>optional .xddq.pb.ElementalBondsBadgeMsg cur = 26;</code>
   */
  @java.lang.Override
  public xddq.pb.ElementalBondsBadgeMsgOrBuilder getCurOrBuilder() {
    return cur_ == null ? xddq.pb.ElementalBondsBadgeMsg.getDefaultInstance() : cur_;
  }

  public static final int PROHIBITMATHTIME_FIELD_NUMBER = 27;
  private long prohibitMathTime_ = 0L;
  /**
   * <code>optional int64 prohibitMathTime = 27;</code>
   * @return Whether the prohibitMathTime field is set.
   */
  @java.lang.Override
  public boolean hasProhibitMathTime() {
    return ((bitField0_ & 0x00020000) != 0);
  }
  /**
   * <code>optional int64 prohibitMathTime = 27;</code>
   * @return The prohibitMathTime.
   */
  @java.lang.Override
  public long getProhibitMathTime() {
    return prohibitMathTime_;
  }

  public static final int VERIFYINFO_FIELD_NUMBER = 28;
  private xddq.pb.ElementalBondsVerifyInfo verifyInfo_;
  /**
   * <code>optional .xddq.pb.ElementalBondsVerifyInfo verifyInfo = 28;</code>
   * @return Whether the verifyInfo field is set.
   */
  @java.lang.Override
  public boolean hasVerifyInfo() {
    return ((bitField0_ & 0x00040000) != 0);
  }
  /**
   * <code>optional .xddq.pb.ElementalBondsVerifyInfo verifyInfo = 28;</code>
   * @return The verifyInfo.
   */
  @java.lang.Override
  public xddq.pb.ElementalBondsVerifyInfo getVerifyInfo() {
    return verifyInfo_ == null ? xddq.pb.ElementalBondsVerifyInfo.getDefaultInstance() : verifyInfo_;
  }
  /**
   * <code>optional .xddq.pb.ElementalBondsVerifyInfo verifyInfo = 28;</code>
   */
  @java.lang.Override
  public xddq.pb.ElementalBondsVerifyInfoOrBuilder getVerifyInfoOrBuilder() {
    return verifyInfo_ == null ? xddq.pb.ElementalBondsVerifyInfo.getDefaultInstance() : verifyInfo_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasScore()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasDataSeason()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasBuffCollectTimes()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasGuide()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (hasOverMsg()) {
      if (!getOverMsg().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    for (int i = 0; i < getMallCount(); i++) {
      if (!getMall(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    for (int i = 0; i < getPrivilegeCardMsgCount(); i++) {
      if (!getPrivilegeCardMsg(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    for (int i = 0; i < unlockCard_.size(); i++) {
      output.writeInt32(1, unlockCard_.getInt(i));
    }
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt64(2, score_);
    }
    for (int i = 0; i < battlePass_.size(); i++) {
      output.writeInt32(3, battlePass_.getInt(i));
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(4, dataSeason_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt32(5, buffCollectTimes_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeBool(6, guide_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      output.writeMessage(7, getOverMsg());
    }
    for (int i = 0; i < mall_.size(); i++) {
      output.writeMessage(8, mall_.get(i));
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      output.writeBool(9, guide2_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      output.writeBool(10, unlockSceneMode_);
    }
    for (int i = 0; i < todaySceneMode_.size(); i++) {
      output.writeInt32(11, todaySceneMode_.getInt(i));
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      output.writeInt32(12, canSurrenderTimes_);
    }
    for (int i = 0; i < unlockSkill_.size(); i++) {
      output.writeInt32(13, unlockSkill_.getInt(i));
    }
    for (int i = 0; i < selectedSkill_.size(); i++) {
      output.writeInt32(14, selectedSkill_.getInt(i));
    }
    if (((bitField0_ & 0x00000100) != 0)) {
      output.writeBool(15, linkGuide_);
    }
    if (((bitField0_ & 0x00000200) != 0)) {
      output.writeInt32(16, useBoardSkin_);
    }
    if (((bitField0_ & 0x00000400) != 0)) {
      output.writeInt32(17, useAnimation_);
    }
    if (((bitField0_ & 0x00000800) != 0)) {
      output.writeInt32(18, useEffect_);
    }
    for (int i = 0; i < equipEmoji_.size(); i++) {
      output.writeInt32(20, equipEmoji_.getInt(i));
    }
    if (((bitField0_ & 0x00001000) != 0)) {
      output.writeBool(21, inGroup_);
    }
    if (((bitField0_ & 0x00002000) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 22, systemOpenValue_);
    }
    if (((bitField0_ & 0x00004000) != 0)) {
      output.writeInt32(23, limitTaskId_);
    }
    for (int i = 0; i < privilegeCardMsg_.size(); i++) {
      output.writeMessage(24, privilegeCardMsg_.get(i));
    }
    if (((bitField0_ & 0x00008000) != 0)) {
      output.writeInt64(25, limitTaskEndTime_);
    }
    if (((bitField0_ & 0x00010000) != 0)) {
      output.writeMessage(26, getCur());
    }
    if (((bitField0_ & 0x00020000) != 0)) {
      output.writeInt64(27, prohibitMathTime_);
    }
    if (((bitField0_ & 0x00040000) != 0)) {
      output.writeMessage(28, getVerifyInfo());
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    {
      int dataSize = 0;
      for (int i = 0; i < unlockCard_.size(); i++) {
        dataSize += com.google.protobuf.CodedOutputStream
          .computeInt32SizeNoTag(unlockCard_.getInt(i));
      }
      size += dataSize;
      size += 1 * getUnlockCardList().size();
    }
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(2, score_);
    }
    {
      int dataSize = 0;
      for (int i = 0; i < battlePass_.size(); i++) {
        dataSize += com.google.protobuf.CodedOutputStream
          .computeInt32SizeNoTag(battlePass_.getInt(i));
      }
      size += dataSize;
      size += 1 * getBattlePassList().size();
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(4, dataSeason_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(5, buffCollectTimes_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(6, guide_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(7, getOverMsg());
    }
    for (int i = 0; i < mall_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(8, mall_.get(i));
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(9, guide2_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(10, unlockSceneMode_);
    }
    {
      int dataSize = 0;
      for (int i = 0; i < todaySceneMode_.size(); i++) {
        dataSize += com.google.protobuf.CodedOutputStream
          .computeInt32SizeNoTag(todaySceneMode_.getInt(i));
      }
      size += dataSize;
      size += 1 * getTodaySceneModeList().size();
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(12, canSurrenderTimes_);
    }
    {
      int dataSize = 0;
      for (int i = 0; i < unlockSkill_.size(); i++) {
        dataSize += com.google.protobuf.CodedOutputStream
          .computeInt32SizeNoTag(unlockSkill_.getInt(i));
      }
      size += dataSize;
      size += 1 * getUnlockSkillList().size();
    }
    {
      int dataSize = 0;
      for (int i = 0; i < selectedSkill_.size(); i++) {
        dataSize += com.google.protobuf.CodedOutputStream
          .computeInt32SizeNoTag(selectedSkill_.getInt(i));
      }
      size += dataSize;
      size += 1 * getSelectedSkillList().size();
    }
    if (((bitField0_ & 0x00000100) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(15, linkGuide_);
    }
    if (((bitField0_ & 0x00000200) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(16, useBoardSkin_);
    }
    if (((bitField0_ & 0x00000400) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(17, useAnimation_);
    }
    if (((bitField0_ & 0x00000800) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(18, useEffect_);
    }
    {
      int dataSize = 0;
      for (int i = 0; i < equipEmoji_.size(); i++) {
        dataSize += com.google.protobuf.CodedOutputStream
          .computeInt32SizeNoTag(equipEmoji_.getInt(i));
      }
      size += dataSize;
      size += 2 * getEquipEmojiList().size();
    }
    if (((bitField0_ & 0x00001000) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(21, inGroup_);
    }
    if (((bitField0_ & 0x00002000) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(22, systemOpenValue_);
    }
    if (((bitField0_ & 0x00004000) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(23, limitTaskId_);
    }
    for (int i = 0; i < privilegeCardMsg_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(24, privilegeCardMsg_.get(i));
    }
    if (((bitField0_ & 0x00008000) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(25, limitTaskEndTime_);
    }
    if (((bitField0_ & 0x00010000) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(26, getCur());
    }
    if (((bitField0_ & 0x00020000) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(27, prohibitMathTime_);
    }
    if (((bitField0_ & 0x00040000) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(28, getVerifyInfo());
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.ElementalBondsUserDataMsg)) {
      return super.equals(obj);
    }
    xddq.pb.ElementalBondsUserDataMsg other = (xddq.pb.ElementalBondsUserDataMsg) obj;

    if (!getUnlockCardList()
        .equals(other.getUnlockCardList())) return false;
    if (hasScore() != other.hasScore()) return false;
    if (hasScore()) {
      if (getScore()
          != other.getScore()) return false;
    }
    if (!getBattlePassList()
        .equals(other.getBattlePassList())) return false;
    if (hasDataSeason() != other.hasDataSeason()) return false;
    if (hasDataSeason()) {
      if (getDataSeason()
          != other.getDataSeason()) return false;
    }
    if (hasBuffCollectTimes() != other.hasBuffCollectTimes()) return false;
    if (hasBuffCollectTimes()) {
      if (getBuffCollectTimes()
          != other.getBuffCollectTimes()) return false;
    }
    if (hasGuide() != other.hasGuide()) return false;
    if (hasGuide()) {
      if (getGuide()
          != other.getGuide()) return false;
    }
    if (hasOverMsg() != other.hasOverMsg()) return false;
    if (hasOverMsg()) {
      if (!getOverMsg()
          .equals(other.getOverMsg())) return false;
    }
    if (!getMallList()
        .equals(other.getMallList())) return false;
    if (hasGuide2() != other.hasGuide2()) return false;
    if (hasGuide2()) {
      if (getGuide2()
          != other.getGuide2()) return false;
    }
    if (hasUnlockSceneMode() != other.hasUnlockSceneMode()) return false;
    if (hasUnlockSceneMode()) {
      if (getUnlockSceneMode()
          != other.getUnlockSceneMode()) return false;
    }
    if (!getTodaySceneModeList()
        .equals(other.getTodaySceneModeList())) return false;
    if (hasCanSurrenderTimes() != other.hasCanSurrenderTimes()) return false;
    if (hasCanSurrenderTimes()) {
      if (getCanSurrenderTimes()
          != other.getCanSurrenderTimes()) return false;
    }
    if (!getUnlockSkillList()
        .equals(other.getUnlockSkillList())) return false;
    if (!getSelectedSkillList()
        .equals(other.getSelectedSkillList())) return false;
    if (hasLinkGuide() != other.hasLinkGuide()) return false;
    if (hasLinkGuide()) {
      if (getLinkGuide()
          != other.getLinkGuide()) return false;
    }
    if (hasUseBoardSkin() != other.hasUseBoardSkin()) return false;
    if (hasUseBoardSkin()) {
      if (getUseBoardSkin()
          != other.getUseBoardSkin()) return false;
    }
    if (hasUseAnimation() != other.hasUseAnimation()) return false;
    if (hasUseAnimation()) {
      if (getUseAnimation()
          != other.getUseAnimation()) return false;
    }
    if (hasUseEffect() != other.hasUseEffect()) return false;
    if (hasUseEffect()) {
      if (getUseEffect()
          != other.getUseEffect()) return false;
    }
    if (!getEquipEmojiList()
        .equals(other.getEquipEmojiList())) return false;
    if (hasInGroup() != other.hasInGroup()) return false;
    if (hasInGroup()) {
      if (getInGroup()
          != other.getInGroup()) return false;
    }
    if (hasSystemOpenValue() != other.hasSystemOpenValue()) return false;
    if (hasSystemOpenValue()) {
      if (!getSystemOpenValue()
          .equals(other.getSystemOpenValue())) return false;
    }
    if (hasLimitTaskId() != other.hasLimitTaskId()) return false;
    if (hasLimitTaskId()) {
      if (getLimitTaskId()
          != other.getLimitTaskId()) return false;
    }
    if (!getPrivilegeCardMsgList()
        .equals(other.getPrivilegeCardMsgList())) return false;
    if (hasLimitTaskEndTime() != other.hasLimitTaskEndTime()) return false;
    if (hasLimitTaskEndTime()) {
      if (getLimitTaskEndTime()
          != other.getLimitTaskEndTime()) return false;
    }
    if (hasCur() != other.hasCur()) return false;
    if (hasCur()) {
      if (!getCur()
          .equals(other.getCur())) return false;
    }
    if (hasProhibitMathTime() != other.hasProhibitMathTime()) return false;
    if (hasProhibitMathTime()) {
      if (getProhibitMathTime()
          != other.getProhibitMathTime()) return false;
    }
    if (hasVerifyInfo() != other.hasVerifyInfo()) return false;
    if (hasVerifyInfo()) {
      if (!getVerifyInfo()
          .equals(other.getVerifyInfo())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (getUnlockCardCount() > 0) {
      hash = (37 * hash) + UNLOCKCARD_FIELD_NUMBER;
      hash = (53 * hash) + getUnlockCardList().hashCode();
    }
    if (hasScore()) {
      hash = (37 * hash) + SCORE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getScore());
    }
    if (getBattlePassCount() > 0) {
      hash = (37 * hash) + BATTLEPASS_FIELD_NUMBER;
      hash = (53 * hash) + getBattlePassList().hashCode();
    }
    if (hasDataSeason()) {
      hash = (37 * hash) + DATASEASON_FIELD_NUMBER;
      hash = (53 * hash) + getDataSeason();
    }
    if (hasBuffCollectTimes()) {
      hash = (37 * hash) + BUFFCOLLECTTIMES_FIELD_NUMBER;
      hash = (53 * hash) + getBuffCollectTimes();
    }
    if (hasGuide()) {
      hash = (37 * hash) + GUIDE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getGuide());
    }
    if (hasOverMsg()) {
      hash = (37 * hash) + OVERMSG_FIELD_NUMBER;
      hash = (53 * hash) + getOverMsg().hashCode();
    }
    if (getMallCount() > 0) {
      hash = (37 * hash) + MALL_FIELD_NUMBER;
      hash = (53 * hash) + getMallList().hashCode();
    }
    if (hasGuide2()) {
      hash = (37 * hash) + GUIDE2_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getGuide2());
    }
    if (hasUnlockSceneMode()) {
      hash = (37 * hash) + UNLOCKSCENEMODE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getUnlockSceneMode());
    }
    if (getTodaySceneModeCount() > 0) {
      hash = (37 * hash) + TODAYSCENEMODE_FIELD_NUMBER;
      hash = (53 * hash) + getTodaySceneModeList().hashCode();
    }
    if (hasCanSurrenderTimes()) {
      hash = (37 * hash) + CANSURRENDERTIMES_FIELD_NUMBER;
      hash = (53 * hash) + getCanSurrenderTimes();
    }
    if (getUnlockSkillCount() > 0) {
      hash = (37 * hash) + UNLOCKSKILL_FIELD_NUMBER;
      hash = (53 * hash) + getUnlockSkillList().hashCode();
    }
    if (getSelectedSkillCount() > 0) {
      hash = (37 * hash) + SELECTEDSKILL_FIELD_NUMBER;
      hash = (53 * hash) + getSelectedSkillList().hashCode();
    }
    if (hasLinkGuide()) {
      hash = (37 * hash) + LINKGUIDE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getLinkGuide());
    }
    if (hasUseBoardSkin()) {
      hash = (37 * hash) + USEBOARDSKIN_FIELD_NUMBER;
      hash = (53 * hash) + getUseBoardSkin();
    }
    if (hasUseAnimation()) {
      hash = (37 * hash) + USEANIMATION_FIELD_NUMBER;
      hash = (53 * hash) + getUseAnimation();
    }
    if (hasUseEffect()) {
      hash = (37 * hash) + USEEFFECT_FIELD_NUMBER;
      hash = (53 * hash) + getUseEffect();
    }
    if (getEquipEmojiCount() > 0) {
      hash = (37 * hash) + EQUIPEMOJI_FIELD_NUMBER;
      hash = (53 * hash) + getEquipEmojiList().hashCode();
    }
    if (hasInGroup()) {
      hash = (37 * hash) + INGROUP_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getInGroup());
    }
    if (hasSystemOpenValue()) {
      hash = (37 * hash) + SYSTEMOPENVALUE_FIELD_NUMBER;
      hash = (53 * hash) + getSystemOpenValue().hashCode();
    }
    if (hasLimitTaskId()) {
      hash = (37 * hash) + LIMITTASKID_FIELD_NUMBER;
      hash = (53 * hash) + getLimitTaskId();
    }
    if (getPrivilegeCardMsgCount() > 0) {
      hash = (37 * hash) + PRIVILEGECARDMSG_FIELD_NUMBER;
      hash = (53 * hash) + getPrivilegeCardMsgList().hashCode();
    }
    if (hasLimitTaskEndTime()) {
      hash = (37 * hash) + LIMITTASKENDTIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getLimitTaskEndTime());
    }
    if (hasCur()) {
      hash = (37 * hash) + CUR_FIELD_NUMBER;
      hash = (53 * hash) + getCur().hashCode();
    }
    if (hasProhibitMathTime()) {
      hash = (37 * hash) + PROHIBITMATHTIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getProhibitMathTime());
    }
    if (hasVerifyInfo()) {
      hash = (37 * hash) + VERIFYINFO_FIELD_NUMBER;
      hash = (53 * hash) + getVerifyInfo().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.ElementalBondsUserDataMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ElementalBondsUserDataMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ElementalBondsUserDataMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ElementalBondsUserDataMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ElementalBondsUserDataMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ElementalBondsUserDataMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ElementalBondsUserDataMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.ElementalBondsUserDataMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.ElementalBondsUserDataMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.ElementalBondsUserDataMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.ElementalBondsUserDataMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.ElementalBondsUserDataMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.ElementalBondsUserDataMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.ElementalBondsUserDataMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.ElementalBondsUserDataMsg)
      xddq.pb.ElementalBondsUserDataMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ElementalBondsUserDataMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ElementalBondsUserDataMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.ElementalBondsUserDataMsg.class, xddq.pb.ElementalBondsUserDataMsg.Builder.class);
    }

    // Construct using xddq.pb.ElementalBondsUserDataMsg.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetOverMsgFieldBuilder();
        internalGetMallFieldBuilder();
        internalGetPrivilegeCardMsgFieldBuilder();
        internalGetCurFieldBuilder();
        internalGetVerifyInfoFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      unlockCard_ = emptyIntList();
      score_ = 0L;
      battlePass_ = emptyIntList();
      dataSeason_ = 0;
      buffCollectTimes_ = 0;
      guide_ = false;
      overMsg_ = null;
      if (overMsgBuilder_ != null) {
        overMsgBuilder_.dispose();
        overMsgBuilder_ = null;
      }
      if (mallBuilder_ == null) {
        mall_ = java.util.Collections.emptyList();
      } else {
        mall_ = null;
        mallBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000080);
      guide2_ = false;
      unlockSceneMode_ = false;
      todaySceneMode_ = emptyIntList();
      canSurrenderTimes_ = 0;
      unlockSkill_ = emptyIntList();
      selectedSkill_ = emptyIntList();
      linkGuide_ = false;
      useBoardSkin_ = 0;
      useAnimation_ = 0;
      useEffect_ = 0;
      equipEmoji_ = emptyIntList();
      inGroup_ = false;
      systemOpenValue_ = "";
      limitTaskId_ = 0;
      if (privilegeCardMsgBuilder_ == null) {
        privilegeCardMsg_ = java.util.Collections.emptyList();
      } else {
        privilegeCardMsg_ = null;
        privilegeCardMsgBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00400000);
      limitTaskEndTime_ = 0L;
      cur_ = null;
      if (curBuilder_ != null) {
        curBuilder_.dispose();
        curBuilder_ = null;
      }
      prohibitMathTime_ = 0L;
      verifyInfo_ = null;
      if (verifyInfoBuilder_ != null) {
        verifyInfoBuilder_.dispose();
        verifyInfoBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ElementalBondsUserDataMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.ElementalBondsUserDataMsg getDefaultInstanceForType() {
      return xddq.pb.ElementalBondsUserDataMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.ElementalBondsUserDataMsg build() {
      xddq.pb.ElementalBondsUserDataMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.ElementalBondsUserDataMsg buildPartial() {
      xddq.pb.ElementalBondsUserDataMsg result = new xddq.pb.ElementalBondsUserDataMsg(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.ElementalBondsUserDataMsg result) {
      if (mallBuilder_ == null) {
        if (((bitField0_ & 0x00000080) != 0)) {
          mall_ = java.util.Collections.unmodifiableList(mall_);
          bitField0_ = (bitField0_ & ~0x00000080);
        }
        result.mall_ = mall_;
      } else {
        result.mall_ = mallBuilder_.build();
      }
      if (privilegeCardMsgBuilder_ == null) {
        if (((bitField0_ & 0x00400000) != 0)) {
          privilegeCardMsg_ = java.util.Collections.unmodifiableList(privilegeCardMsg_);
          bitField0_ = (bitField0_ & ~0x00400000);
        }
        result.privilegeCardMsg_ = privilegeCardMsg_;
      } else {
        result.privilegeCardMsg_ = privilegeCardMsgBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.ElementalBondsUserDataMsg result) {
      int from_bitField0_ = bitField0_;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        unlockCard_.makeImmutable();
        result.unlockCard_ = unlockCard_;
      }
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.score_ = score_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        battlePass_.makeImmutable();
        result.battlePass_ = battlePass_;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.dataSeason_ = dataSeason_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.buffCollectTimes_ = buffCollectTimes_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.guide_ = guide_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.overMsg_ = overMsgBuilder_ == null
            ? overMsg_
            : overMsgBuilder_.build();
        to_bitField0_ |= 0x00000010;
      }
      if (((from_bitField0_ & 0x00000100) != 0)) {
        result.guide2_ = guide2_;
        to_bitField0_ |= 0x00000020;
      }
      if (((from_bitField0_ & 0x00000200) != 0)) {
        result.unlockSceneMode_ = unlockSceneMode_;
        to_bitField0_ |= 0x00000040;
      }
      if (((from_bitField0_ & 0x00000400) != 0)) {
        todaySceneMode_.makeImmutable();
        result.todaySceneMode_ = todaySceneMode_;
      }
      if (((from_bitField0_ & 0x00000800) != 0)) {
        result.canSurrenderTimes_ = canSurrenderTimes_;
        to_bitField0_ |= 0x00000080;
      }
      if (((from_bitField0_ & 0x00001000) != 0)) {
        unlockSkill_.makeImmutable();
        result.unlockSkill_ = unlockSkill_;
      }
      if (((from_bitField0_ & 0x00002000) != 0)) {
        selectedSkill_.makeImmutable();
        result.selectedSkill_ = selectedSkill_;
      }
      if (((from_bitField0_ & 0x00004000) != 0)) {
        result.linkGuide_ = linkGuide_;
        to_bitField0_ |= 0x00000100;
      }
      if (((from_bitField0_ & 0x00008000) != 0)) {
        result.useBoardSkin_ = useBoardSkin_;
        to_bitField0_ |= 0x00000200;
      }
      if (((from_bitField0_ & 0x00010000) != 0)) {
        result.useAnimation_ = useAnimation_;
        to_bitField0_ |= 0x00000400;
      }
      if (((from_bitField0_ & 0x00020000) != 0)) {
        result.useEffect_ = useEffect_;
        to_bitField0_ |= 0x00000800;
      }
      if (((from_bitField0_ & 0x00040000) != 0)) {
        equipEmoji_.makeImmutable();
        result.equipEmoji_ = equipEmoji_;
      }
      if (((from_bitField0_ & 0x00080000) != 0)) {
        result.inGroup_ = inGroup_;
        to_bitField0_ |= 0x00001000;
      }
      if (((from_bitField0_ & 0x00100000) != 0)) {
        result.systemOpenValue_ = systemOpenValue_;
        to_bitField0_ |= 0x00002000;
      }
      if (((from_bitField0_ & 0x00200000) != 0)) {
        result.limitTaskId_ = limitTaskId_;
        to_bitField0_ |= 0x00004000;
      }
      if (((from_bitField0_ & 0x00800000) != 0)) {
        result.limitTaskEndTime_ = limitTaskEndTime_;
        to_bitField0_ |= 0x00008000;
      }
      if (((from_bitField0_ & 0x01000000) != 0)) {
        result.cur_ = curBuilder_ == null
            ? cur_
            : curBuilder_.build();
        to_bitField0_ |= 0x00010000;
      }
      if (((from_bitField0_ & 0x02000000) != 0)) {
        result.prohibitMathTime_ = prohibitMathTime_;
        to_bitField0_ |= 0x00020000;
      }
      if (((from_bitField0_ & 0x04000000) != 0)) {
        result.verifyInfo_ = verifyInfoBuilder_ == null
            ? verifyInfo_
            : verifyInfoBuilder_.build();
        to_bitField0_ |= 0x00040000;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.ElementalBondsUserDataMsg) {
        return mergeFrom((xddq.pb.ElementalBondsUserDataMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.ElementalBondsUserDataMsg other) {
      if (other == xddq.pb.ElementalBondsUserDataMsg.getDefaultInstance()) return this;
      if (!other.unlockCard_.isEmpty()) {
        if (unlockCard_.isEmpty()) {
          unlockCard_ = other.unlockCard_;
          unlockCard_.makeImmutable();
          bitField0_ |= 0x00000001;
        } else {
          ensureUnlockCardIsMutable();
          unlockCard_.addAll(other.unlockCard_);
        }
        onChanged();
      }
      if (other.hasScore()) {
        setScore(other.getScore());
      }
      if (!other.battlePass_.isEmpty()) {
        if (battlePass_.isEmpty()) {
          battlePass_ = other.battlePass_;
          battlePass_.makeImmutable();
          bitField0_ |= 0x00000004;
        } else {
          ensureBattlePassIsMutable();
          battlePass_.addAll(other.battlePass_);
        }
        onChanged();
      }
      if (other.hasDataSeason()) {
        setDataSeason(other.getDataSeason());
      }
      if (other.hasBuffCollectTimes()) {
        setBuffCollectTimes(other.getBuffCollectTimes());
      }
      if (other.hasGuide()) {
        setGuide(other.getGuide());
      }
      if (other.hasOverMsg()) {
        mergeOverMsg(other.getOverMsg());
      }
      if (mallBuilder_ == null) {
        if (!other.mall_.isEmpty()) {
          if (mall_.isEmpty()) {
            mall_ = other.mall_;
            bitField0_ = (bitField0_ & ~0x00000080);
          } else {
            ensureMallIsMutable();
            mall_.addAll(other.mall_);
          }
          onChanged();
        }
      } else {
        if (!other.mall_.isEmpty()) {
          if (mallBuilder_.isEmpty()) {
            mallBuilder_.dispose();
            mallBuilder_ = null;
            mall_ = other.mall_;
            bitField0_ = (bitField0_ & ~0x00000080);
            mallBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetMallFieldBuilder() : null;
          } else {
            mallBuilder_.addAllMessages(other.mall_);
          }
        }
      }
      if (other.hasGuide2()) {
        setGuide2(other.getGuide2());
      }
      if (other.hasUnlockSceneMode()) {
        setUnlockSceneMode(other.getUnlockSceneMode());
      }
      if (!other.todaySceneMode_.isEmpty()) {
        if (todaySceneMode_.isEmpty()) {
          todaySceneMode_ = other.todaySceneMode_;
          todaySceneMode_.makeImmutable();
          bitField0_ |= 0x00000400;
        } else {
          ensureTodaySceneModeIsMutable();
          todaySceneMode_.addAll(other.todaySceneMode_);
        }
        onChanged();
      }
      if (other.hasCanSurrenderTimes()) {
        setCanSurrenderTimes(other.getCanSurrenderTimes());
      }
      if (!other.unlockSkill_.isEmpty()) {
        if (unlockSkill_.isEmpty()) {
          unlockSkill_ = other.unlockSkill_;
          unlockSkill_.makeImmutable();
          bitField0_ |= 0x00001000;
        } else {
          ensureUnlockSkillIsMutable();
          unlockSkill_.addAll(other.unlockSkill_);
        }
        onChanged();
      }
      if (!other.selectedSkill_.isEmpty()) {
        if (selectedSkill_.isEmpty()) {
          selectedSkill_ = other.selectedSkill_;
          selectedSkill_.makeImmutable();
          bitField0_ |= 0x00002000;
        } else {
          ensureSelectedSkillIsMutable();
          selectedSkill_.addAll(other.selectedSkill_);
        }
        onChanged();
      }
      if (other.hasLinkGuide()) {
        setLinkGuide(other.getLinkGuide());
      }
      if (other.hasUseBoardSkin()) {
        setUseBoardSkin(other.getUseBoardSkin());
      }
      if (other.hasUseAnimation()) {
        setUseAnimation(other.getUseAnimation());
      }
      if (other.hasUseEffect()) {
        setUseEffect(other.getUseEffect());
      }
      if (!other.equipEmoji_.isEmpty()) {
        if (equipEmoji_.isEmpty()) {
          equipEmoji_ = other.equipEmoji_;
          equipEmoji_.makeImmutable();
          bitField0_ |= 0x00040000;
        } else {
          ensureEquipEmojiIsMutable();
          equipEmoji_.addAll(other.equipEmoji_);
        }
        onChanged();
      }
      if (other.hasInGroup()) {
        setInGroup(other.getInGroup());
      }
      if (other.hasSystemOpenValue()) {
        systemOpenValue_ = other.systemOpenValue_;
        bitField0_ |= 0x00100000;
        onChanged();
      }
      if (other.hasLimitTaskId()) {
        setLimitTaskId(other.getLimitTaskId());
      }
      if (privilegeCardMsgBuilder_ == null) {
        if (!other.privilegeCardMsg_.isEmpty()) {
          if (privilegeCardMsg_.isEmpty()) {
            privilegeCardMsg_ = other.privilegeCardMsg_;
            bitField0_ = (bitField0_ & ~0x00400000);
          } else {
            ensurePrivilegeCardMsgIsMutable();
            privilegeCardMsg_.addAll(other.privilegeCardMsg_);
          }
          onChanged();
        }
      } else {
        if (!other.privilegeCardMsg_.isEmpty()) {
          if (privilegeCardMsgBuilder_.isEmpty()) {
            privilegeCardMsgBuilder_.dispose();
            privilegeCardMsgBuilder_ = null;
            privilegeCardMsg_ = other.privilegeCardMsg_;
            bitField0_ = (bitField0_ & ~0x00400000);
            privilegeCardMsgBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetPrivilegeCardMsgFieldBuilder() : null;
          } else {
            privilegeCardMsgBuilder_.addAllMessages(other.privilegeCardMsg_);
          }
        }
      }
      if (other.hasLimitTaskEndTime()) {
        setLimitTaskEndTime(other.getLimitTaskEndTime());
      }
      if (other.hasCur()) {
        mergeCur(other.getCur());
      }
      if (other.hasProhibitMathTime()) {
        setProhibitMathTime(other.getProhibitMathTime());
      }
      if (other.hasVerifyInfo()) {
        mergeVerifyInfo(other.getVerifyInfo());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasScore()) {
        return false;
      }
      if (!hasDataSeason()) {
        return false;
      }
      if (!hasBuffCollectTimes()) {
        return false;
      }
      if (!hasGuide()) {
        return false;
      }
      if (hasOverMsg()) {
        if (!getOverMsg().isInitialized()) {
          return false;
        }
      }
      for (int i = 0; i < getMallCount(); i++) {
        if (!getMall(i).isInitialized()) {
          return false;
        }
      }
      for (int i = 0; i < getPrivilegeCardMsgCount(); i++) {
        if (!getPrivilegeCardMsg(i).isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              int v = input.readInt32();
              ensureUnlockCardIsMutable();
              unlockCard_.addInt(v);
              break;
            } // case 8
            case 10: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              ensureUnlockCardIsMutable();
              while (input.getBytesUntilLimit() > 0) {
                unlockCard_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            } // case 10
            case 16: {
              score_ = input.readInt64();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 24: {
              int v = input.readInt32();
              ensureBattlePassIsMutable();
              battlePass_.addInt(v);
              break;
            } // case 24
            case 26: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              ensureBattlePassIsMutable();
              while (input.getBytesUntilLimit() > 0) {
                battlePass_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            } // case 26
            case 32: {
              dataSeason_ = input.readInt32();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            case 40: {
              buffCollectTimes_ = input.readInt32();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            case 48: {
              guide_ = input.readBool();
              bitField0_ |= 0x00000020;
              break;
            } // case 48
            case 58: {
              input.readMessage(
                  internalGetOverMsgFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000040;
              break;
            } // case 58
            case 66: {
              xddq.pb.ElementalBondsMallMsg m =
                  input.readMessage(
                      xddq.pb.ElementalBondsMallMsg.parser(),
                      extensionRegistry);
              if (mallBuilder_ == null) {
                ensureMallIsMutable();
                mall_.add(m);
              } else {
                mallBuilder_.addMessage(m);
              }
              break;
            } // case 66
            case 72: {
              guide2_ = input.readBool();
              bitField0_ |= 0x00000100;
              break;
            } // case 72
            case 80: {
              unlockSceneMode_ = input.readBool();
              bitField0_ |= 0x00000200;
              break;
            } // case 80
            case 88: {
              int v = input.readInt32();
              ensureTodaySceneModeIsMutable();
              todaySceneMode_.addInt(v);
              break;
            } // case 88
            case 90: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              ensureTodaySceneModeIsMutable();
              while (input.getBytesUntilLimit() > 0) {
                todaySceneMode_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            } // case 90
            case 96: {
              canSurrenderTimes_ = input.readInt32();
              bitField0_ |= 0x00000800;
              break;
            } // case 96
            case 104: {
              int v = input.readInt32();
              ensureUnlockSkillIsMutable();
              unlockSkill_.addInt(v);
              break;
            } // case 104
            case 106: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              ensureUnlockSkillIsMutable();
              while (input.getBytesUntilLimit() > 0) {
                unlockSkill_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            } // case 106
            case 112: {
              int v = input.readInt32();
              ensureSelectedSkillIsMutable();
              selectedSkill_.addInt(v);
              break;
            } // case 112
            case 114: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              ensureSelectedSkillIsMutable();
              while (input.getBytesUntilLimit() > 0) {
                selectedSkill_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            } // case 114
            case 120: {
              linkGuide_ = input.readBool();
              bitField0_ |= 0x00004000;
              break;
            } // case 120
            case 128: {
              useBoardSkin_ = input.readInt32();
              bitField0_ |= 0x00008000;
              break;
            } // case 128
            case 136: {
              useAnimation_ = input.readInt32();
              bitField0_ |= 0x00010000;
              break;
            } // case 136
            case 144: {
              useEffect_ = input.readInt32();
              bitField0_ |= 0x00020000;
              break;
            } // case 144
            case 160: {
              int v = input.readInt32();
              ensureEquipEmojiIsMutable();
              equipEmoji_.addInt(v);
              break;
            } // case 160
            case 162: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              ensureEquipEmojiIsMutable();
              while (input.getBytesUntilLimit() > 0) {
                equipEmoji_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            } // case 162
            case 168: {
              inGroup_ = input.readBool();
              bitField0_ |= 0x00080000;
              break;
            } // case 168
            case 178: {
              systemOpenValue_ = input.readBytes();
              bitField0_ |= 0x00100000;
              break;
            } // case 178
            case 184: {
              limitTaskId_ = input.readInt32();
              bitField0_ |= 0x00200000;
              break;
            } // case 184
            case 194: {
              xddq.pb.ElementalBondsPrivilegeCardMsg m =
                  input.readMessage(
                      xddq.pb.ElementalBondsPrivilegeCardMsg.parser(),
                      extensionRegistry);
              if (privilegeCardMsgBuilder_ == null) {
                ensurePrivilegeCardMsgIsMutable();
                privilegeCardMsg_.add(m);
              } else {
                privilegeCardMsgBuilder_.addMessage(m);
              }
              break;
            } // case 194
            case 200: {
              limitTaskEndTime_ = input.readInt64();
              bitField0_ |= 0x00800000;
              break;
            } // case 200
            case 210: {
              input.readMessage(
                  internalGetCurFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x01000000;
              break;
            } // case 210
            case 216: {
              prohibitMathTime_ = input.readInt64();
              bitField0_ |= 0x02000000;
              break;
            } // case 216
            case 226: {
              input.readMessage(
                  internalGetVerifyInfoFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x04000000;
              break;
            } // case 226
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private com.google.protobuf.Internal.IntList unlockCard_ = emptyIntList();
    private void ensureUnlockCardIsMutable() {
      if (!unlockCard_.isModifiable()) {
        unlockCard_ = makeMutableCopy(unlockCard_);
      }
      bitField0_ |= 0x00000001;
    }
    /**
     * <code>repeated int32 unlockCard = 1;</code>
     * @return A list containing the unlockCard.
     */
    public java.util.List<java.lang.Integer>
        getUnlockCardList() {
      unlockCard_.makeImmutable();
      return unlockCard_;
    }
    /**
     * <code>repeated int32 unlockCard = 1;</code>
     * @return The count of unlockCard.
     */
    public int getUnlockCardCount() {
      return unlockCard_.size();
    }
    /**
     * <code>repeated int32 unlockCard = 1;</code>
     * @param index The index of the element to return.
     * @return The unlockCard at the given index.
     */
    public int getUnlockCard(int index) {
      return unlockCard_.getInt(index);
    }
    /**
     * <code>repeated int32 unlockCard = 1;</code>
     * @param index The index to set the value at.
     * @param value The unlockCard to set.
     * @return This builder for chaining.
     */
    public Builder setUnlockCard(
        int index, int value) {

      ensureUnlockCardIsMutable();
      unlockCard_.setInt(index, value);
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 unlockCard = 1;</code>
     * @param value The unlockCard to add.
     * @return This builder for chaining.
     */
    public Builder addUnlockCard(int value) {

      ensureUnlockCardIsMutable();
      unlockCard_.addInt(value);
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 unlockCard = 1;</code>
     * @param values The unlockCard to add.
     * @return This builder for chaining.
     */
    public Builder addAllUnlockCard(
        java.lang.Iterable<? extends java.lang.Integer> values) {
      ensureUnlockCardIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, unlockCard_);
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 unlockCard = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearUnlockCard() {
      unlockCard_ = emptyIntList();
      bitField0_ = (bitField0_ & ~0x00000001);
      onChanged();
      return this;
    }

    private long score_ ;
    /**
     * <code>required int64 score = 2;</code>
     * @return Whether the score field is set.
     */
    @java.lang.Override
    public boolean hasScore() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>required int64 score = 2;</code>
     * @return The score.
     */
    @java.lang.Override
    public long getScore() {
      return score_;
    }
    /**
     * <code>required int64 score = 2;</code>
     * @param value The score to set.
     * @return This builder for chaining.
     */
    public Builder setScore(long value) {

      score_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>required int64 score = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearScore() {
      bitField0_ = (bitField0_ & ~0x00000002);
      score_ = 0L;
      onChanged();
      return this;
    }

    private com.google.protobuf.Internal.IntList battlePass_ = emptyIntList();
    private void ensureBattlePassIsMutable() {
      if (!battlePass_.isModifiable()) {
        battlePass_ = makeMutableCopy(battlePass_);
      }
      bitField0_ |= 0x00000004;
    }
    /**
     * <code>repeated int32 battlePass = 3;</code>
     * @return A list containing the battlePass.
     */
    public java.util.List<java.lang.Integer>
        getBattlePassList() {
      battlePass_.makeImmutable();
      return battlePass_;
    }
    /**
     * <code>repeated int32 battlePass = 3;</code>
     * @return The count of battlePass.
     */
    public int getBattlePassCount() {
      return battlePass_.size();
    }
    /**
     * <code>repeated int32 battlePass = 3;</code>
     * @param index The index of the element to return.
     * @return The battlePass at the given index.
     */
    public int getBattlePass(int index) {
      return battlePass_.getInt(index);
    }
    /**
     * <code>repeated int32 battlePass = 3;</code>
     * @param index The index to set the value at.
     * @param value The battlePass to set.
     * @return This builder for chaining.
     */
    public Builder setBattlePass(
        int index, int value) {

      ensureBattlePassIsMutable();
      battlePass_.setInt(index, value);
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 battlePass = 3;</code>
     * @param value The battlePass to add.
     * @return This builder for chaining.
     */
    public Builder addBattlePass(int value) {

      ensureBattlePassIsMutable();
      battlePass_.addInt(value);
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 battlePass = 3;</code>
     * @param values The battlePass to add.
     * @return This builder for chaining.
     */
    public Builder addAllBattlePass(
        java.lang.Iterable<? extends java.lang.Integer> values) {
      ensureBattlePassIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, battlePass_);
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 battlePass = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearBattlePass() {
      battlePass_ = emptyIntList();
      bitField0_ = (bitField0_ & ~0x00000004);
      onChanged();
      return this;
    }

    private int dataSeason_ ;
    /**
     * <code>required int32 dataSeason = 4;</code>
     * @return Whether the dataSeason field is set.
     */
    @java.lang.Override
    public boolean hasDataSeason() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>required int32 dataSeason = 4;</code>
     * @return The dataSeason.
     */
    @java.lang.Override
    public int getDataSeason() {
      return dataSeason_;
    }
    /**
     * <code>required int32 dataSeason = 4;</code>
     * @param value The dataSeason to set.
     * @return This builder for chaining.
     */
    public Builder setDataSeason(int value) {

      dataSeason_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 dataSeason = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearDataSeason() {
      bitField0_ = (bitField0_ & ~0x00000008);
      dataSeason_ = 0;
      onChanged();
      return this;
    }

    private int buffCollectTimes_ ;
    /**
     * <code>required int32 buffCollectTimes = 5;</code>
     * @return Whether the buffCollectTimes field is set.
     */
    @java.lang.Override
    public boolean hasBuffCollectTimes() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>required int32 buffCollectTimes = 5;</code>
     * @return The buffCollectTimes.
     */
    @java.lang.Override
    public int getBuffCollectTimes() {
      return buffCollectTimes_;
    }
    /**
     * <code>required int32 buffCollectTimes = 5;</code>
     * @param value The buffCollectTimes to set.
     * @return This builder for chaining.
     */
    public Builder setBuffCollectTimes(int value) {

      buffCollectTimes_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 buffCollectTimes = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearBuffCollectTimes() {
      bitField0_ = (bitField0_ & ~0x00000010);
      buffCollectTimes_ = 0;
      onChanged();
      return this;
    }

    private boolean guide_ ;
    /**
     * <code>required bool guide = 6;</code>
     * @return Whether the guide field is set.
     */
    @java.lang.Override
    public boolean hasGuide() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>required bool guide = 6;</code>
     * @return The guide.
     */
    @java.lang.Override
    public boolean getGuide() {
      return guide_;
    }
    /**
     * <code>required bool guide = 6;</code>
     * @param value The guide to set.
     * @return This builder for chaining.
     */
    public Builder setGuide(boolean value) {

      guide_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>required bool guide = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearGuide() {
      bitField0_ = (bitField0_ & ~0x00000020);
      guide_ = false;
      onChanged();
      return this;
    }

    private xddq.pb.ElementalBondsGameOverMsg overMsg_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.ElementalBondsGameOverMsg, xddq.pb.ElementalBondsGameOverMsg.Builder, xddq.pb.ElementalBondsGameOverMsgOrBuilder> overMsgBuilder_;
    /**
     * <code>optional .xddq.pb.ElementalBondsGameOverMsg overMsg = 7;</code>
     * @return Whether the overMsg field is set.
     */
    public boolean hasOverMsg() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <code>optional .xddq.pb.ElementalBondsGameOverMsg overMsg = 7;</code>
     * @return The overMsg.
     */
    public xddq.pb.ElementalBondsGameOverMsg getOverMsg() {
      if (overMsgBuilder_ == null) {
        return overMsg_ == null ? xddq.pb.ElementalBondsGameOverMsg.getDefaultInstance() : overMsg_;
      } else {
        return overMsgBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.ElementalBondsGameOverMsg overMsg = 7;</code>
     */
    public Builder setOverMsg(xddq.pb.ElementalBondsGameOverMsg value) {
      if (overMsgBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        overMsg_ = value;
      } else {
        overMsgBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ElementalBondsGameOverMsg overMsg = 7;</code>
     */
    public Builder setOverMsg(
        xddq.pb.ElementalBondsGameOverMsg.Builder builderForValue) {
      if (overMsgBuilder_ == null) {
        overMsg_ = builderForValue.build();
      } else {
        overMsgBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ElementalBondsGameOverMsg overMsg = 7;</code>
     */
    public Builder mergeOverMsg(xddq.pb.ElementalBondsGameOverMsg value) {
      if (overMsgBuilder_ == null) {
        if (((bitField0_ & 0x00000040) != 0) &&
          overMsg_ != null &&
          overMsg_ != xddq.pb.ElementalBondsGameOverMsg.getDefaultInstance()) {
          getOverMsgBuilder().mergeFrom(value);
        } else {
          overMsg_ = value;
        }
      } else {
        overMsgBuilder_.mergeFrom(value);
      }
      if (overMsg_ != null) {
        bitField0_ |= 0x00000040;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.ElementalBondsGameOverMsg overMsg = 7;</code>
     */
    public Builder clearOverMsg() {
      bitField0_ = (bitField0_ & ~0x00000040);
      overMsg_ = null;
      if (overMsgBuilder_ != null) {
        overMsgBuilder_.dispose();
        overMsgBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ElementalBondsGameOverMsg overMsg = 7;</code>
     */
    public xddq.pb.ElementalBondsGameOverMsg.Builder getOverMsgBuilder() {
      bitField0_ |= 0x00000040;
      onChanged();
      return internalGetOverMsgFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.ElementalBondsGameOverMsg overMsg = 7;</code>
     */
    public xddq.pb.ElementalBondsGameOverMsgOrBuilder getOverMsgOrBuilder() {
      if (overMsgBuilder_ != null) {
        return overMsgBuilder_.getMessageOrBuilder();
      } else {
        return overMsg_ == null ?
            xddq.pb.ElementalBondsGameOverMsg.getDefaultInstance() : overMsg_;
      }
    }
    /**
     * <code>optional .xddq.pb.ElementalBondsGameOverMsg overMsg = 7;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.ElementalBondsGameOverMsg, xddq.pb.ElementalBondsGameOverMsg.Builder, xddq.pb.ElementalBondsGameOverMsgOrBuilder> 
        internalGetOverMsgFieldBuilder() {
      if (overMsgBuilder_ == null) {
        overMsgBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.ElementalBondsGameOverMsg, xddq.pb.ElementalBondsGameOverMsg.Builder, xddq.pb.ElementalBondsGameOverMsgOrBuilder>(
                getOverMsg(),
                getParentForChildren(),
                isClean());
        overMsg_ = null;
      }
      return overMsgBuilder_;
    }

    private java.util.List<xddq.pb.ElementalBondsMallMsg> mall_ =
      java.util.Collections.emptyList();
    private void ensureMallIsMutable() {
      if (!((bitField0_ & 0x00000080) != 0)) {
        mall_ = new java.util.ArrayList<xddq.pb.ElementalBondsMallMsg>(mall_);
        bitField0_ |= 0x00000080;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.ElementalBondsMallMsg, xddq.pb.ElementalBondsMallMsg.Builder, xddq.pb.ElementalBondsMallMsgOrBuilder> mallBuilder_;

    /**
     * <code>repeated .xddq.pb.ElementalBondsMallMsg mall = 8;</code>
     */
    public java.util.List<xddq.pb.ElementalBondsMallMsg> getMallList() {
      if (mallBuilder_ == null) {
        return java.util.Collections.unmodifiableList(mall_);
      } else {
        return mallBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsMallMsg mall = 8;</code>
     */
    public int getMallCount() {
      if (mallBuilder_ == null) {
        return mall_.size();
      } else {
        return mallBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsMallMsg mall = 8;</code>
     */
    public xddq.pb.ElementalBondsMallMsg getMall(int index) {
      if (mallBuilder_ == null) {
        return mall_.get(index);
      } else {
        return mallBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsMallMsg mall = 8;</code>
     */
    public Builder setMall(
        int index, xddq.pb.ElementalBondsMallMsg value) {
      if (mallBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMallIsMutable();
        mall_.set(index, value);
        onChanged();
      } else {
        mallBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsMallMsg mall = 8;</code>
     */
    public Builder setMall(
        int index, xddq.pb.ElementalBondsMallMsg.Builder builderForValue) {
      if (mallBuilder_ == null) {
        ensureMallIsMutable();
        mall_.set(index, builderForValue.build());
        onChanged();
      } else {
        mallBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsMallMsg mall = 8;</code>
     */
    public Builder addMall(xddq.pb.ElementalBondsMallMsg value) {
      if (mallBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMallIsMutable();
        mall_.add(value);
        onChanged();
      } else {
        mallBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsMallMsg mall = 8;</code>
     */
    public Builder addMall(
        int index, xddq.pb.ElementalBondsMallMsg value) {
      if (mallBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMallIsMutable();
        mall_.add(index, value);
        onChanged();
      } else {
        mallBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsMallMsg mall = 8;</code>
     */
    public Builder addMall(
        xddq.pb.ElementalBondsMallMsg.Builder builderForValue) {
      if (mallBuilder_ == null) {
        ensureMallIsMutable();
        mall_.add(builderForValue.build());
        onChanged();
      } else {
        mallBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsMallMsg mall = 8;</code>
     */
    public Builder addMall(
        int index, xddq.pb.ElementalBondsMallMsg.Builder builderForValue) {
      if (mallBuilder_ == null) {
        ensureMallIsMutable();
        mall_.add(index, builderForValue.build());
        onChanged();
      } else {
        mallBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsMallMsg mall = 8;</code>
     */
    public Builder addAllMall(
        java.lang.Iterable<? extends xddq.pb.ElementalBondsMallMsg> values) {
      if (mallBuilder_ == null) {
        ensureMallIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, mall_);
        onChanged();
      } else {
        mallBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsMallMsg mall = 8;</code>
     */
    public Builder clearMall() {
      if (mallBuilder_ == null) {
        mall_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000080);
        onChanged();
      } else {
        mallBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsMallMsg mall = 8;</code>
     */
    public Builder removeMall(int index) {
      if (mallBuilder_ == null) {
        ensureMallIsMutable();
        mall_.remove(index);
        onChanged();
      } else {
        mallBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsMallMsg mall = 8;</code>
     */
    public xddq.pb.ElementalBondsMallMsg.Builder getMallBuilder(
        int index) {
      return internalGetMallFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsMallMsg mall = 8;</code>
     */
    public xddq.pb.ElementalBondsMallMsgOrBuilder getMallOrBuilder(
        int index) {
      if (mallBuilder_ == null) {
        return mall_.get(index);  } else {
        return mallBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsMallMsg mall = 8;</code>
     */
    public java.util.List<? extends xddq.pb.ElementalBondsMallMsgOrBuilder> 
         getMallOrBuilderList() {
      if (mallBuilder_ != null) {
        return mallBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(mall_);
      }
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsMallMsg mall = 8;</code>
     */
    public xddq.pb.ElementalBondsMallMsg.Builder addMallBuilder() {
      return internalGetMallFieldBuilder().addBuilder(
          xddq.pb.ElementalBondsMallMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsMallMsg mall = 8;</code>
     */
    public xddq.pb.ElementalBondsMallMsg.Builder addMallBuilder(
        int index) {
      return internalGetMallFieldBuilder().addBuilder(
          index, xddq.pb.ElementalBondsMallMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsMallMsg mall = 8;</code>
     */
    public java.util.List<xddq.pb.ElementalBondsMallMsg.Builder> 
         getMallBuilderList() {
      return internalGetMallFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.ElementalBondsMallMsg, xddq.pb.ElementalBondsMallMsg.Builder, xddq.pb.ElementalBondsMallMsgOrBuilder> 
        internalGetMallFieldBuilder() {
      if (mallBuilder_ == null) {
        mallBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.ElementalBondsMallMsg, xddq.pb.ElementalBondsMallMsg.Builder, xddq.pb.ElementalBondsMallMsgOrBuilder>(
                mall_,
                ((bitField0_ & 0x00000080) != 0),
                getParentForChildren(),
                isClean());
        mall_ = null;
      }
      return mallBuilder_;
    }

    private boolean guide2_ ;
    /**
     * <code>optional bool guide2 = 9;</code>
     * @return Whether the guide2 field is set.
     */
    @java.lang.Override
    public boolean hasGuide2() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <code>optional bool guide2 = 9;</code>
     * @return The guide2.
     */
    @java.lang.Override
    public boolean getGuide2() {
      return guide2_;
    }
    /**
     * <code>optional bool guide2 = 9;</code>
     * @param value The guide2 to set.
     * @return This builder for chaining.
     */
    public Builder setGuide2(boolean value) {

      guide2_ = value;
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }
    /**
     * <code>optional bool guide2 = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearGuide2() {
      bitField0_ = (bitField0_ & ~0x00000100);
      guide2_ = false;
      onChanged();
      return this;
    }

    private boolean unlockSceneMode_ ;
    /**
     * <code>optional bool unlockSceneMode = 10;</code>
     * @return Whether the unlockSceneMode field is set.
     */
    @java.lang.Override
    public boolean hasUnlockSceneMode() {
      return ((bitField0_ & 0x00000200) != 0);
    }
    /**
     * <code>optional bool unlockSceneMode = 10;</code>
     * @return The unlockSceneMode.
     */
    @java.lang.Override
    public boolean getUnlockSceneMode() {
      return unlockSceneMode_;
    }
    /**
     * <code>optional bool unlockSceneMode = 10;</code>
     * @param value The unlockSceneMode to set.
     * @return This builder for chaining.
     */
    public Builder setUnlockSceneMode(boolean value) {

      unlockSceneMode_ = value;
      bitField0_ |= 0x00000200;
      onChanged();
      return this;
    }
    /**
     * <code>optional bool unlockSceneMode = 10;</code>
     * @return This builder for chaining.
     */
    public Builder clearUnlockSceneMode() {
      bitField0_ = (bitField0_ & ~0x00000200);
      unlockSceneMode_ = false;
      onChanged();
      return this;
    }

    private com.google.protobuf.Internal.IntList todaySceneMode_ = emptyIntList();
    private void ensureTodaySceneModeIsMutable() {
      if (!todaySceneMode_.isModifiable()) {
        todaySceneMode_ = makeMutableCopy(todaySceneMode_);
      }
      bitField0_ |= 0x00000400;
    }
    /**
     * <code>repeated int32 todaySceneMode = 11;</code>
     * @return A list containing the todaySceneMode.
     */
    public java.util.List<java.lang.Integer>
        getTodaySceneModeList() {
      todaySceneMode_.makeImmutable();
      return todaySceneMode_;
    }
    /**
     * <code>repeated int32 todaySceneMode = 11;</code>
     * @return The count of todaySceneMode.
     */
    public int getTodaySceneModeCount() {
      return todaySceneMode_.size();
    }
    /**
     * <code>repeated int32 todaySceneMode = 11;</code>
     * @param index The index of the element to return.
     * @return The todaySceneMode at the given index.
     */
    public int getTodaySceneMode(int index) {
      return todaySceneMode_.getInt(index);
    }
    /**
     * <code>repeated int32 todaySceneMode = 11;</code>
     * @param index The index to set the value at.
     * @param value The todaySceneMode to set.
     * @return This builder for chaining.
     */
    public Builder setTodaySceneMode(
        int index, int value) {

      ensureTodaySceneModeIsMutable();
      todaySceneMode_.setInt(index, value);
      bitField0_ |= 0x00000400;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 todaySceneMode = 11;</code>
     * @param value The todaySceneMode to add.
     * @return This builder for chaining.
     */
    public Builder addTodaySceneMode(int value) {

      ensureTodaySceneModeIsMutable();
      todaySceneMode_.addInt(value);
      bitField0_ |= 0x00000400;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 todaySceneMode = 11;</code>
     * @param values The todaySceneMode to add.
     * @return This builder for chaining.
     */
    public Builder addAllTodaySceneMode(
        java.lang.Iterable<? extends java.lang.Integer> values) {
      ensureTodaySceneModeIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, todaySceneMode_);
      bitField0_ |= 0x00000400;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 todaySceneMode = 11;</code>
     * @return This builder for chaining.
     */
    public Builder clearTodaySceneMode() {
      todaySceneMode_ = emptyIntList();
      bitField0_ = (bitField0_ & ~0x00000400);
      onChanged();
      return this;
    }

    private int canSurrenderTimes_ ;
    /**
     * <code>optional int32 canSurrenderTimes = 12;</code>
     * @return Whether the canSurrenderTimes field is set.
     */
    @java.lang.Override
    public boolean hasCanSurrenderTimes() {
      return ((bitField0_ & 0x00000800) != 0);
    }
    /**
     * <code>optional int32 canSurrenderTimes = 12;</code>
     * @return The canSurrenderTimes.
     */
    @java.lang.Override
    public int getCanSurrenderTimes() {
      return canSurrenderTimes_;
    }
    /**
     * <code>optional int32 canSurrenderTimes = 12;</code>
     * @param value The canSurrenderTimes to set.
     * @return This builder for chaining.
     */
    public Builder setCanSurrenderTimes(int value) {

      canSurrenderTimes_ = value;
      bitField0_ |= 0x00000800;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 canSurrenderTimes = 12;</code>
     * @return This builder for chaining.
     */
    public Builder clearCanSurrenderTimes() {
      bitField0_ = (bitField0_ & ~0x00000800);
      canSurrenderTimes_ = 0;
      onChanged();
      return this;
    }

    private com.google.protobuf.Internal.IntList unlockSkill_ = emptyIntList();
    private void ensureUnlockSkillIsMutable() {
      if (!unlockSkill_.isModifiable()) {
        unlockSkill_ = makeMutableCopy(unlockSkill_);
      }
      bitField0_ |= 0x00001000;
    }
    /**
     * <code>repeated int32 unlockSkill = 13;</code>
     * @return A list containing the unlockSkill.
     */
    public java.util.List<java.lang.Integer>
        getUnlockSkillList() {
      unlockSkill_.makeImmutable();
      return unlockSkill_;
    }
    /**
     * <code>repeated int32 unlockSkill = 13;</code>
     * @return The count of unlockSkill.
     */
    public int getUnlockSkillCount() {
      return unlockSkill_.size();
    }
    /**
     * <code>repeated int32 unlockSkill = 13;</code>
     * @param index The index of the element to return.
     * @return The unlockSkill at the given index.
     */
    public int getUnlockSkill(int index) {
      return unlockSkill_.getInt(index);
    }
    /**
     * <code>repeated int32 unlockSkill = 13;</code>
     * @param index The index to set the value at.
     * @param value The unlockSkill to set.
     * @return This builder for chaining.
     */
    public Builder setUnlockSkill(
        int index, int value) {

      ensureUnlockSkillIsMutable();
      unlockSkill_.setInt(index, value);
      bitField0_ |= 0x00001000;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 unlockSkill = 13;</code>
     * @param value The unlockSkill to add.
     * @return This builder for chaining.
     */
    public Builder addUnlockSkill(int value) {

      ensureUnlockSkillIsMutable();
      unlockSkill_.addInt(value);
      bitField0_ |= 0x00001000;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 unlockSkill = 13;</code>
     * @param values The unlockSkill to add.
     * @return This builder for chaining.
     */
    public Builder addAllUnlockSkill(
        java.lang.Iterable<? extends java.lang.Integer> values) {
      ensureUnlockSkillIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, unlockSkill_);
      bitField0_ |= 0x00001000;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 unlockSkill = 13;</code>
     * @return This builder for chaining.
     */
    public Builder clearUnlockSkill() {
      unlockSkill_ = emptyIntList();
      bitField0_ = (bitField0_ & ~0x00001000);
      onChanged();
      return this;
    }

    private com.google.protobuf.Internal.IntList selectedSkill_ = emptyIntList();
    private void ensureSelectedSkillIsMutable() {
      if (!selectedSkill_.isModifiable()) {
        selectedSkill_ = makeMutableCopy(selectedSkill_);
      }
      bitField0_ |= 0x00002000;
    }
    /**
     * <code>repeated int32 selectedSkill = 14;</code>
     * @return A list containing the selectedSkill.
     */
    public java.util.List<java.lang.Integer>
        getSelectedSkillList() {
      selectedSkill_.makeImmutable();
      return selectedSkill_;
    }
    /**
     * <code>repeated int32 selectedSkill = 14;</code>
     * @return The count of selectedSkill.
     */
    public int getSelectedSkillCount() {
      return selectedSkill_.size();
    }
    /**
     * <code>repeated int32 selectedSkill = 14;</code>
     * @param index The index of the element to return.
     * @return The selectedSkill at the given index.
     */
    public int getSelectedSkill(int index) {
      return selectedSkill_.getInt(index);
    }
    /**
     * <code>repeated int32 selectedSkill = 14;</code>
     * @param index The index to set the value at.
     * @param value The selectedSkill to set.
     * @return This builder for chaining.
     */
    public Builder setSelectedSkill(
        int index, int value) {

      ensureSelectedSkillIsMutable();
      selectedSkill_.setInt(index, value);
      bitField0_ |= 0x00002000;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 selectedSkill = 14;</code>
     * @param value The selectedSkill to add.
     * @return This builder for chaining.
     */
    public Builder addSelectedSkill(int value) {

      ensureSelectedSkillIsMutable();
      selectedSkill_.addInt(value);
      bitField0_ |= 0x00002000;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 selectedSkill = 14;</code>
     * @param values The selectedSkill to add.
     * @return This builder for chaining.
     */
    public Builder addAllSelectedSkill(
        java.lang.Iterable<? extends java.lang.Integer> values) {
      ensureSelectedSkillIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, selectedSkill_);
      bitField0_ |= 0x00002000;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 selectedSkill = 14;</code>
     * @return This builder for chaining.
     */
    public Builder clearSelectedSkill() {
      selectedSkill_ = emptyIntList();
      bitField0_ = (bitField0_ & ~0x00002000);
      onChanged();
      return this;
    }

    private boolean linkGuide_ ;
    /**
     * <code>optional bool linkGuide = 15;</code>
     * @return Whether the linkGuide field is set.
     */
    @java.lang.Override
    public boolean hasLinkGuide() {
      return ((bitField0_ & 0x00004000) != 0);
    }
    /**
     * <code>optional bool linkGuide = 15;</code>
     * @return The linkGuide.
     */
    @java.lang.Override
    public boolean getLinkGuide() {
      return linkGuide_;
    }
    /**
     * <code>optional bool linkGuide = 15;</code>
     * @param value The linkGuide to set.
     * @return This builder for chaining.
     */
    public Builder setLinkGuide(boolean value) {

      linkGuide_ = value;
      bitField0_ |= 0x00004000;
      onChanged();
      return this;
    }
    /**
     * <code>optional bool linkGuide = 15;</code>
     * @return This builder for chaining.
     */
    public Builder clearLinkGuide() {
      bitField0_ = (bitField0_ & ~0x00004000);
      linkGuide_ = false;
      onChanged();
      return this;
    }

    private int useBoardSkin_ ;
    /**
     * <code>optional int32 useBoardSkin = 16;</code>
     * @return Whether the useBoardSkin field is set.
     */
    @java.lang.Override
    public boolean hasUseBoardSkin() {
      return ((bitField0_ & 0x00008000) != 0);
    }
    /**
     * <code>optional int32 useBoardSkin = 16;</code>
     * @return The useBoardSkin.
     */
    @java.lang.Override
    public int getUseBoardSkin() {
      return useBoardSkin_;
    }
    /**
     * <code>optional int32 useBoardSkin = 16;</code>
     * @param value The useBoardSkin to set.
     * @return This builder for chaining.
     */
    public Builder setUseBoardSkin(int value) {

      useBoardSkin_ = value;
      bitField0_ |= 0x00008000;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 useBoardSkin = 16;</code>
     * @return This builder for chaining.
     */
    public Builder clearUseBoardSkin() {
      bitField0_ = (bitField0_ & ~0x00008000);
      useBoardSkin_ = 0;
      onChanged();
      return this;
    }

    private int useAnimation_ ;
    /**
     * <code>optional int32 useAnimation = 17;</code>
     * @return Whether the useAnimation field is set.
     */
    @java.lang.Override
    public boolean hasUseAnimation() {
      return ((bitField0_ & 0x00010000) != 0);
    }
    /**
     * <code>optional int32 useAnimation = 17;</code>
     * @return The useAnimation.
     */
    @java.lang.Override
    public int getUseAnimation() {
      return useAnimation_;
    }
    /**
     * <code>optional int32 useAnimation = 17;</code>
     * @param value The useAnimation to set.
     * @return This builder for chaining.
     */
    public Builder setUseAnimation(int value) {

      useAnimation_ = value;
      bitField0_ |= 0x00010000;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 useAnimation = 17;</code>
     * @return This builder for chaining.
     */
    public Builder clearUseAnimation() {
      bitField0_ = (bitField0_ & ~0x00010000);
      useAnimation_ = 0;
      onChanged();
      return this;
    }

    private int useEffect_ ;
    /**
     * <code>optional int32 useEffect = 18;</code>
     * @return Whether the useEffect field is set.
     */
    @java.lang.Override
    public boolean hasUseEffect() {
      return ((bitField0_ & 0x00020000) != 0);
    }
    /**
     * <code>optional int32 useEffect = 18;</code>
     * @return The useEffect.
     */
    @java.lang.Override
    public int getUseEffect() {
      return useEffect_;
    }
    /**
     * <code>optional int32 useEffect = 18;</code>
     * @param value The useEffect to set.
     * @return This builder for chaining.
     */
    public Builder setUseEffect(int value) {

      useEffect_ = value;
      bitField0_ |= 0x00020000;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 useEffect = 18;</code>
     * @return This builder for chaining.
     */
    public Builder clearUseEffect() {
      bitField0_ = (bitField0_ & ~0x00020000);
      useEffect_ = 0;
      onChanged();
      return this;
    }

    private com.google.protobuf.Internal.IntList equipEmoji_ = emptyIntList();
    private void ensureEquipEmojiIsMutable() {
      if (!equipEmoji_.isModifiable()) {
        equipEmoji_ = makeMutableCopy(equipEmoji_);
      }
      bitField0_ |= 0x00040000;
    }
    /**
     * <code>repeated int32 equipEmoji = 20;</code>
     * @return A list containing the equipEmoji.
     */
    public java.util.List<java.lang.Integer>
        getEquipEmojiList() {
      equipEmoji_.makeImmutable();
      return equipEmoji_;
    }
    /**
     * <code>repeated int32 equipEmoji = 20;</code>
     * @return The count of equipEmoji.
     */
    public int getEquipEmojiCount() {
      return equipEmoji_.size();
    }
    /**
     * <code>repeated int32 equipEmoji = 20;</code>
     * @param index The index of the element to return.
     * @return The equipEmoji at the given index.
     */
    public int getEquipEmoji(int index) {
      return equipEmoji_.getInt(index);
    }
    /**
     * <code>repeated int32 equipEmoji = 20;</code>
     * @param index The index to set the value at.
     * @param value The equipEmoji to set.
     * @return This builder for chaining.
     */
    public Builder setEquipEmoji(
        int index, int value) {

      ensureEquipEmojiIsMutable();
      equipEmoji_.setInt(index, value);
      bitField0_ |= 0x00040000;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 equipEmoji = 20;</code>
     * @param value The equipEmoji to add.
     * @return This builder for chaining.
     */
    public Builder addEquipEmoji(int value) {

      ensureEquipEmojiIsMutable();
      equipEmoji_.addInt(value);
      bitField0_ |= 0x00040000;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 equipEmoji = 20;</code>
     * @param values The equipEmoji to add.
     * @return This builder for chaining.
     */
    public Builder addAllEquipEmoji(
        java.lang.Iterable<? extends java.lang.Integer> values) {
      ensureEquipEmojiIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, equipEmoji_);
      bitField0_ |= 0x00040000;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 equipEmoji = 20;</code>
     * @return This builder for chaining.
     */
    public Builder clearEquipEmoji() {
      equipEmoji_ = emptyIntList();
      bitField0_ = (bitField0_ & ~0x00040000);
      onChanged();
      return this;
    }

    private boolean inGroup_ ;
    /**
     * <code>optional bool inGroup = 21;</code>
     * @return Whether the inGroup field is set.
     */
    @java.lang.Override
    public boolean hasInGroup() {
      return ((bitField0_ & 0x00080000) != 0);
    }
    /**
     * <code>optional bool inGroup = 21;</code>
     * @return The inGroup.
     */
    @java.lang.Override
    public boolean getInGroup() {
      return inGroup_;
    }
    /**
     * <code>optional bool inGroup = 21;</code>
     * @param value The inGroup to set.
     * @return This builder for chaining.
     */
    public Builder setInGroup(boolean value) {

      inGroup_ = value;
      bitField0_ |= 0x00080000;
      onChanged();
      return this;
    }
    /**
     * <code>optional bool inGroup = 21;</code>
     * @return This builder for chaining.
     */
    public Builder clearInGroup() {
      bitField0_ = (bitField0_ & ~0x00080000);
      inGroup_ = false;
      onChanged();
      return this;
    }

    private java.lang.Object systemOpenValue_ = "";
    /**
     * <code>optional string systemOpenValue = 22;</code>
     * @return Whether the systemOpenValue field is set.
     */
    public boolean hasSystemOpenValue() {
      return ((bitField0_ & 0x00100000) != 0);
    }
    /**
     * <code>optional string systemOpenValue = 22;</code>
     * @return The systemOpenValue.
     */
    public java.lang.String getSystemOpenValue() {
      java.lang.Object ref = systemOpenValue_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          systemOpenValue_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string systemOpenValue = 22;</code>
     * @return The bytes for systemOpenValue.
     */
    public com.google.protobuf.ByteString
        getSystemOpenValueBytes() {
      java.lang.Object ref = systemOpenValue_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        systemOpenValue_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string systemOpenValue = 22;</code>
     * @param value The systemOpenValue to set.
     * @return This builder for chaining.
     */
    public Builder setSystemOpenValue(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      systemOpenValue_ = value;
      bitField0_ |= 0x00100000;
      onChanged();
      return this;
    }
    /**
     * <code>optional string systemOpenValue = 22;</code>
     * @return This builder for chaining.
     */
    public Builder clearSystemOpenValue() {
      systemOpenValue_ = getDefaultInstance().getSystemOpenValue();
      bitField0_ = (bitField0_ & ~0x00100000);
      onChanged();
      return this;
    }
    /**
     * <code>optional string systemOpenValue = 22;</code>
     * @param value The bytes for systemOpenValue to set.
     * @return This builder for chaining.
     */
    public Builder setSystemOpenValueBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      systemOpenValue_ = value;
      bitField0_ |= 0x00100000;
      onChanged();
      return this;
    }

    private int limitTaskId_ ;
    /**
     * <code>optional int32 limitTaskId = 23;</code>
     * @return Whether the limitTaskId field is set.
     */
    @java.lang.Override
    public boolean hasLimitTaskId() {
      return ((bitField0_ & 0x00200000) != 0);
    }
    /**
     * <code>optional int32 limitTaskId = 23;</code>
     * @return The limitTaskId.
     */
    @java.lang.Override
    public int getLimitTaskId() {
      return limitTaskId_;
    }
    /**
     * <code>optional int32 limitTaskId = 23;</code>
     * @param value The limitTaskId to set.
     * @return This builder for chaining.
     */
    public Builder setLimitTaskId(int value) {

      limitTaskId_ = value;
      bitField0_ |= 0x00200000;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 limitTaskId = 23;</code>
     * @return This builder for chaining.
     */
    public Builder clearLimitTaskId() {
      bitField0_ = (bitField0_ & ~0x00200000);
      limitTaskId_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.ElementalBondsPrivilegeCardMsg> privilegeCardMsg_ =
      java.util.Collections.emptyList();
    private void ensurePrivilegeCardMsgIsMutable() {
      if (!((bitField0_ & 0x00400000) != 0)) {
        privilegeCardMsg_ = new java.util.ArrayList<xddq.pb.ElementalBondsPrivilegeCardMsg>(privilegeCardMsg_);
        bitField0_ |= 0x00400000;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.ElementalBondsPrivilegeCardMsg, xddq.pb.ElementalBondsPrivilegeCardMsg.Builder, xddq.pb.ElementalBondsPrivilegeCardMsgOrBuilder> privilegeCardMsgBuilder_;

    /**
     * <code>repeated .xddq.pb.ElementalBondsPrivilegeCardMsg privilegeCardMsg = 24;</code>
     */
    public java.util.List<xddq.pb.ElementalBondsPrivilegeCardMsg> getPrivilegeCardMsgList() {
      if (privilegeCardMsgBuilder_ == null) {
        return java.util.Collections.unmodifiableList(privilegeCardMsg_);
      } else {
        return privilegeCardMsgBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsPrivilegeCardMsg privilegeCardMsg = 24;</code>
     */
    public int getPrivilegeCardMsgCount() {
      if (privilegeCardMsgBuilder_ == null) {
        return privilegeCardMsg_.size();
      } else {
        return privilegeCardMsgBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsPrivilegeCardMsg privilegeCardMsg = 24;</code>
     */
    public xddq.pb.ElementalBondsPrivilegeCardMsg getPrivilegeCardMsg(int index) {
      if (privilegeCardMsgBuilder_ == null) {
        return privilegeCardMsg_.get(index);
      } else {
        return privilegeCardMsgBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsPrivilegeCardMsg privilegeCardMsg = 24;</code>
     */
    public Builder setPrivilegeCardMsg(
        int index, xddq.pb.ElementalBondsPrivilegeCardMsg value) {
      if (privilegeCardMsgBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePrivilegeCardMsgIsMutable();
        privilegeCardMsg_.set(index, value);
        onChanged();
      } else {
        privilegeCardMsgBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsPrivilegeCardMsg privilegeCardMsg = 24;</code>
     */
    public Builder setPrivilegeCardMsg(
        int index, xddq.pb.ElementalBondsPrivilegeCardMsg.Builder builderForValue) {
      if (privilegeCardMsgBuilder_ == null) {
        ensurePrivilegeCardMsgIsMutable();
        privilegeCardMsg_.set(index, builderForValue.build());
        onChanged();
      } else {
        privilegeCardMsgBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsPrivilegeCardMsg privilegeCardMsg = 24;</code>
     */
    public Builder addPrivilegeCardMsg(xddq.pb.ElementalBondsPrivilegeCardMsg value) {
      if (privilegeCardMsgBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePrivilegeCardMsgIsMutable();
        privilegeCardMsg_.add(value);
        onChanged();
      } else {
        privilegeCardMsgBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsPrivilegeCardMsg privilegeCardMsg = 24;</code>
     */
    public Builder addPrivilegeCardMsg(
        int index, xddq.pb.ElementalBondsPrivilegeCardMsg value) {
      if (privilegeCardMsgBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePrivilegeCardMsgIsMutable();
        privilegeCardMsg_.add(index, value);
        onChanged();
      } else {
        privilegeCardMsgBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsPrivilegeCardMsg privilegeCardMsg = 24;</code>
     */
    public Builder addPrivilegeCardMsg(
        xddq.pb.ElementalBondsPrivilegeCardMsg.Builder builderForValue) {
      if (privilegeCardMsgBuilder_ == null) {
        ensurePrivilegeCardMsgIsMutable();
        privilegeCardMsg_.add(builderForValue.build());
        onChanged();
      } else {
        privilegeCardMsgBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsPrivilegeCardMsg privilegeCardMsg = 24;</code>
     */
    public Builder addPrivilegeCardMsg(
        int index, xddq.pb.ElementalBondsPrivilegeCardMsg.Builder builderForValue) {
      if (privilegeCardMsgBuilder_ == null) {
        ensurePrivilegeCardMsgIsMutable();
        privilegeCardMsg_.add(index, builderForValue.build());
        onChanged();
      } else {
        privilegeCardMsgBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsPrivilegeCardMsg privilegeCardMsg = 24;</code>
     */
    public Builder addAllPrivilegeCardMsg(
        java.lang.Iterable<? extends xddq.pb.ElementalBondsPrivilegeCardMsg> values) {
      if (privilegeCardMsgBuilder_ == null) {
        ensurePrivilegeCardMsgIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, privilegeCardMsg_);
        onChanged();
      } else {
        privilegeCardMsgBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsPrivilegeCardMsg privilegeCardMsg = 24;</code>
     */
    public Builder clearPrivilegeCardMsg() {
      if (privilegeCardMsgBuilder_ == null) {
        privilegeCardMsg_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00400000);
        onChanged();
      } else {
        privilegeCardMsgBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsPrivilegeCardMsg privilegeCardMsg = 24;</code>
     */
    public Builder removePrivilegeCardMsg(int index) {
      if (privilegeCardMsgBuilder_ == null) {
        ensurePrivilegeCardMsgIsMutable();
        privilegeCardMsg_.remove(index);
        onChanged();
      } else {
        privilegeCardMsgBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsPrivilegeCardMsg privilegeCardMsg = 24;</code>
     */
    public xddq.pb.ElementalBondsPrivilegeCardMsg.Builder getPrivilegeCardMsgBuilder(
        int index) {
      return internalGetPrivilegeCardMsgFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsPrivilegeCardMsg privilegeCardMsg = 24;</code>
     */
    public xddq.pb.ElementalBondsPrivilegeCardMsgOrBuilder getPrivilegeCardMsgOrBuilder(
        int index) {
      if (privilegeCardMsgBuilder_ == null) {
        return privilegeCardMsg_.get(index);  } else {
        return privilegeCardMsgBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsPrivilegeCardMsg privilegeCardMsg = 24;</code>
     */
    public java.util.List<? extends xddq.pb.ElementalBondsPrivilegeCardMsgOrBuilder> 
         getPrivilegeCardMsgOrBuilderList() {
      if (privilegeCardMsgBuilder_ != null) {
        return privilegeCardMsgBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(privilegeCardMsg_);
      }
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsPrivilegeCardMsg privilegeCardMsg = 24;</code>
     */
    public xddq.pb.ElementalBondsPrivilegeCardMsg.Builder addPrivilegeCardMsgBuilder() {
      return internalGetPrivilegeCardMsgFieldBuilder().addBuilder(
          xddq.pb.ElementalBondsPrivilegeCardMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsPrivilegeCardMsg privilegeCardMsg = 24;</code>
     */
    public xddq.pb.ElementalBondsPrivilegeCardMsg.Builder addPrivilegeCardMsgBuilder(
        int index) {
      return internalGetPrivilegeCardMsgFieldBuilder().addBuilder(
          index, xddq.pb.ElementalBondsPrivilegeCardMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsPrivilegeCardMsg privilegeCardMsg = 24;</code>
     */
    public java.util.List<xddq.pb.ElementalBondsPrivilegeCardMsg.Builder> 
         getPrivilegeCardMsgBuilderList() {
      return internalGetPrivilegeCardMsgFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.ElementalBondsPrivilegeCardMsg, xddq.pb.ElementalBondsPrivilegeCardMsg.Builder, xddq.pb.ElementalBondsPrivilegeCardMsgOrBuilder> 
        internalGetPrivilegeCardMsgFieldBuilder() {
      if (privilegeCardMsgBuilder_ == null) {
        privilegeCardMsgBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.ElementalBondsPrivilegeCardMsg, xddq.pb.ElementalBondsPrivilegeCardMsg.Builder, xddq.pb.ElementalBondsPrivilegeCardMsgOrBuilder>(
                privilegeCardMsg_,
                ((bitField0_ & 0x00400000) != 0),
                getParentForChildren(),
                isClean());
        privilegeCardMsg_ = null;
      }
      return privilegeCardMsgBuilder_;
    }

    private long limitTaskEndTime_ ;
    /**
     * <code>optional int64 limitTaskEndTime = 25;</code>
     * @return Whether the limitTaskEndTime field is set.
     */
    @java.lang.Override
    public boolean hasLimitTaskEndTime() {
      return ((bitField0_ & 0x00800000) != 0);
    }
    /**
     * <code>optional int64 limitTaskEndTime = 25;</code>
     * @return The limitTaskEndTime.
     */
    @java.lang.Override
    public long getLimitTaskEndTime() {
      return limitTaskEndTime_;
    }
    /**
     * <code>optional int64 limitTaskEndTime = 25;</code>
     * @param value The limitTaskEndTime to set.
     * @return This builder for chaining.
     */
    public Builder setLimitTaskEndTime(long value) {

      limitTaskEndTime_ = value;
      bitField0_ |= 0x00800000;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 limitTaskEndTime = 25;</code>
     * @return This builder for chaining.
     */
    public Builder clearLimitTaskEndTime() {
      bitField0_ = (bitField0_ & ~0x00800000);
      limitTaskEndTime_ = 0L;
      onChanged();
      return this;
    }

    private xddq.pb.ElementalBondsBadgeMsg cur_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.ElementalBondsBadgeMsg, xddq.pb.ElementalBondsBadgeMsg.Builder, xddq.pb.ElementalBondsBadgeMsgOrBuilder> curBuilder_;
    /**
     * <code>optional .xddq.pb.ElementalBondsBadgeMsg cur = 26;</code>
     * @return Whether the cur field is set.
     */
    public boolean hasCur() {
      return ((bitField0_ & 0x01000000) != 0);
    }
    /**
     * <code>optional .xddq.pb.ElementalBondsBadgeMsg cur = 26;</code>
     * @return The cur.
     */
    public xddq.pb.ElementalBondsBadgeMsg getCur() {
      if (curBuilder_ == null) {
        return cur_ == null ? xddq.pb.ElementalBondsBadgeMsg.getDefaultInstance() : cur_;
      } else {
        return curBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.ElementalBondsBadgeMsg cur = 26;</code>
     */
    public Builder setCur(xddq.pb.ElementalBondsBadgeMsg value) {
      if (curBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        cur_ = value;
      } else {
        curBuilder_.setMessage(value);
      }
      bitField0_ |= 0x01000000;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ElementalBondsBadgeMsg cur = 26;</code>
     */
    public Builder setCur(
        xddq.pb.ElementalBondsBadgeMsg.Builder builderForValue) {
      if (curBuilder_ == null) {
        cur_ = builderForValue.build();
      } else {
        curBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x01000000;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ElementalBondsBadgeMsg cur = 26;</code>
     */
    public Builder mergeCur(xddq.pb.ElementalBondsBadgeMsg value) {
      if (curBuilder_ == null) {
        if (((bitField0_ & 0x01000000) != 0) &&
          cur_ != null &&
          cur_ != xddq.pb.ElementalBondsBadgeMsg.getDefaultInstance()) {
          getCurBuilder().mergeFrom(value);
        } else {
          cur_ = value;
        }
      } else {
        curBuilder_.mergeFrom(value);
      }
      if (cur_ != null) {
        bitField0_ |= 0x01000000;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.ElementalBondsBadgeMsg cur = 26;</code>
     */
    public Builder clearCur() {
      bitField0_ = (bitField0_ & ~0x01000000);
      cur_ = null;
      if (curBuilder_ != null) {
        curBuilder_.dispose();
        curBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ElementalBondsBadgeMsg cur = 26;</code>
     */
    public xddq.pb.ElementalBondsBadgeMsg.Builder getCurBuilder() {
      bitField0_ |= 0x01000000;
      onChanged();
      return internalGetCurFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.ElementalBondsBadgeMsg cur = 26;</code>
     */
    public xddq.pb.ElementalBondsBadgeMsgOrBuilder getCurOrBuilder() {
      if (curBuilder_ != null) {
        return curBuilder_.getMessageOrBuilder();
      } else {
        return cur_ == null ?
            xddq.pb.ElementalBondsBadgeMsg.getDefaultInstance() : cur_;
      }
    }
    /**
     * <code>optional .xddq.pb.ElementalBondsBadgeMsg cur = 26;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.ElementalBondsBadgeMsg, xddq.pb.ElementalBondsBadgeMsg.Builder, xddq.pb.ElementalBondsBadgeMsgOrBuilder> 
        internalGetCurFieldBuilder() {
      if (curBuilder_ == null) {
        curBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.ElementalBondsBadgeMsg, xddq.pb.ElementalBondsBadgeMsg.Builder, xddq.pb.ElementalBondsBadgeMsgOrBuilder>(
                getCur(),
                getParentForChildren(),
                isClean());
        cur_ = null;
      }
      return curBuilder_;
    }

    private long prohibitMathTime_ ;
    /**
     * <code>optional int64 prohibitMathTime = 27;</code>
     * @return Whether the prohibitMathTime field is set.
     */
    @java.lang.Override
    public boolean hasProhibitMathTime() {
      return ((bitField0_ & 0x02000000) != 0);
    }
    /**
     * <code>optional int64 prohibitMathTime = 27;</code>
     * @return The prohibitMathTime.
     */
    @java.lang.Override
    public long getProhibitMathTime() {
      return prohibitMathTime_;
    }
    /**
     * <code>optional int64 prohibitMathTime = 27;</code>
     * @param value The prohibitMathTime to set.
     * @return This builder for chaining.
     */
    public Builder setProhibitMathTime(long value) {

      prohibitMathTime_ = value;
      bitField0_ |= 0x02000000;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 prohibitMathTime = 27;</code>
     * @return This builder for chaining.
     */
    public Builder clearProhibitMathTime() {
      bitField0_ = (bitField0_ & ~0x02000000);
      prohibitMathTime_ = 0L;
      onChanged();
      return this;
    }

    private xddq.pb.ElementalBondsVerifyInfo verifyInfo_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.ElementalBondsVerifyInfo, xddq.pb.ElementalBondsVerifyInfo.Builder, xddq.pb.ElementalBondsVerifyInfoOrBuilder> verifyInfoBuilder_;
    /**
     * <code>optional .xddq.pb.ElementalBondsVerifyInfo verifyInfo = 28;</code>
     * @return Whether the verifyInfo field is set.
     */
    public boolean hasVerifyInfo() {
      return ((bitField0_ & 0x04000000) != 0);
    }
    /**
     * <code>optional .xddq.pb.ElementalBondsVerifyInfo verifyInfo = 28;</code>
     * @return The verifyInfo.
     */
    public xddq.pb.ElementalBondsVerifyInfo getVerifyInfo() {
      if (verifyInfoBuilder_ == null) {
        return verifyInfo_ == null ? xddq.pb.ElementalBondsVerifyInfo.getDefaultInstance() : verifyInfo_;
      } else {
        return verifyInfoBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.ElementalBondsVerifyInfo verifyInfo = 28;</code>
     */
    public Builder setVerifyInfo(xddq.pb.ElementalBondsVerifyInfo value) {
      if (verifyInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        verifyInfo_ = value;
      } else {
        verifyInfoBuilder_.setMessage(value);
      }
      bitField0_ |= 0x04000000;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ElementalBondsVerifyInfo verifyInfo = 28;</code>
     */
    public Builder setVerifyInfo(
        xddq.pb.ElementalBondsVerifyInfo.Builder builderForValue) {
      if (verifyInfoBuilder_ == null) {
        verifyInfo_ = builderForValue.build();
      } else {
        verifyInfoBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x04000000;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ElementalBondsVerifyInfo verifyInfo = 28;</code>
     */
    public Builder mergeVerifyInfo(xddq.pb.ElementalBondsVerifyInfo value) {
      if (verifyInfoBuilder_ == null) {
        if (((bitField0_ & 0x04000000) != 0) &&
          verifyInfo_ != null &&
          verifyInfo_ != xddq.pb.ElementalBondsVerifyInfo.getDefaultInstance()) {
          getVerifyInfoBuilder().mergeFrom(value);
        } else {
          verifyInfo_ = value;
        }
      } else {
        verifyInfoBuilder_.mergeFrom(value);
      }
      if (verifyInfo_ != null) {
        bitField0_ |= 0x04000000;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.ElementalBondsVerifyInfo verifyInfo = 28;</code>
     */
    public Builder clearVerifyInfo() {
      bitField0_ = (bitField0_ & ~0x04000000);
      verifyInfo_ = null;
      if (verifyInfoBuilder_ != null) {
        verifyInfoBuilder_.dispose();
        verifyInfoBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ElementalBondsVerifyInfo verifyInfo = 28;</code>
     */
    public xddq.pb.ElementalBondsVerifyInfo.Builder getVerifyInfoBuilder() {
      bitField0_ |= 0x04000000;
      onChanged();
      return internalGetVerifyInfoFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.ElementalBondsVerifyInfo verifyInfo = 28;</code>
     */
    public xddq.pb.ElementalBondsVerifyInfoOrBuilder getVerifyInfoOrBuilder() {
      if (verifyInfoBuilder_ != null) {
        return verifyInfoBuilder_.getMessageOrBuilder();
      } else {
        return verifyInfo_ == null ?
            xddq.pb.ElementalBondsVerifyInfo.getDefaultInstance() : verifyInfo_;
      }
    }
    /**
     * <code>optional .xddq.pb.ElementalBondsVerifyInfo verifyInfo = 28;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.ElementalBondsVerifyInfo, xddq.pb.ElementalBondsVerifyInfo.Builder, xddq.pb.ElementalBondsVerifyInfoOrBuilder> 
        internalGetVerifyInfoFieldBuilder() {
      if (verifyInfoBuilder_ == null) {
        verifyInfoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.ElementalBondsVerifyInfo, xddq.pb.ElementalBondsVerifyInfo.Builder, xddq.pb.ElementalBondsVerifyInfoOrBuilder>(
                getVerifyInfo(),
                getParentForChildren(),
                isClean());
        verifyInfo_ = null;
      }
      return verifyInfoBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.ElementalBondsUserDataMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.ElementalBondsUserDataMsg)
  private static final xddq.pb.ElementalBondsUserDataMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.ElementalBondsUserDataMsg();
  }

  public static xddq.pb.ElementalBondsUserDataMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ElementalBondsUserDataMsg>
      PARSER = new com.google.protobuf.AbstractParser<ElementalBondsUserDataMsg>() {
    @java.lang.Override
    public ElementalBondsUserDataMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<ElementalBondsUserDataMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ElementalBondsUserDataMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.ElementalBondsUserDataMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

