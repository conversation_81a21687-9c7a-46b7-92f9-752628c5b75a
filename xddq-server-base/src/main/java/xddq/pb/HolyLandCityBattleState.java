// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.HolyLandCityBattleState}
 */
public final class HolyLandCityBattleState extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.HolyLandCityBattleState)
    HolyLandCityBattleStateOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      HolyLandCityBattleState.class.getName());
  }
  // Use HolyLandCityBattleState.newBuilder() to construct.
  private HolyLandCityBattleState(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private HolyLandCityBattleState() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_HolyLandCityBattleState_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_HolyLandCityBattleState_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.HolyLandCityBattleState.class, xddq.pb.HolyLandCityBattleState.Builder.class);
  }

  private int bitField0_;
  public static final int CITYID_FIELD_NUMBER = 1;
  private int cityId_ = 0;
  /**
   * <code>optional int32 cityId = 1;</code>
   * @return Whether the cityId field is set.
   */
  @java.lang.Override
  public boolean hasCityId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int32 cityId = 1;</code>
   * @return The cityId.
   */
  @java.lang.Override
  public int getCityId() {
    return cityId_;
  }

  public static final int ISBATTLE_FIELD_NUMBER = 2;
  private boolean isBattle_ = false;
  /**
   * <code>optional bool isBattle = 2;</code>
   * @return Whether the isBattle field is set.
   */
  @java.lang.Override
  public boolean hasIsBattle() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional bool isBattle = 2;</code>
   * @return The isBattle.
   */
  @java.lang.Override
  public boolean getIsBattle() {
    return isBattle_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, cityId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeBool(2, isBattle_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, cityId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(2, isBattle_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.HolyLandCityBattleState)) {
      return super.equals(obj);
    }
    xddq.pb.HolyLandCityBattleState other = (xddq.pb.HolyLandCityBattleState) obj;

    if (hasCityId() != other.hasCityId()) return false;
    if (hasCityId()) {
      if (getCityId()
          != other.getCityId()) return false;
    }
    if (hasIsBattle() != other.hasIsBattle()) return false;
    if (hasIsBattle()) {
      if (getIsBattle()
          != other.getIsBattle()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasCityId()) {
      hash = (37 * hash) + CITYID_FIELD_NUMBER;
      hash = (53 * hash) + getCityId();
    }
    if (hasIsBattle()) {
      hash = (37 * hash) + ISBATTLE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getIsBattle());
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.HolyLandCityBattleState parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.HolyLandCityBattleState parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.HolyLandCityBattleState parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.HolyLandCityBattleState parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.HolyLandCityBattleState parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.HolyLandCityBattleState parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.HolyLandCityBattleState parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.HolyLandCityBattleState parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.HolyLandCityBattleState parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.HolyLandCityBattleState parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.HolyLandCityBattleState parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.HolyLandCityBattleState parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.HolyLandCityBattleState prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.HolyLandCityBattleState}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.HolyLandCityBattleState)
      xddq.pb.HolyLandCityBattleStateOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_HolyLandCityBattleState_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_HolyLandCityBattleState_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.HolyLandCityBattleState.class, xddq.pb.HolyLandCityBattleState.Builder.class);
    }

    // Construct using xddq.pb.HolyLandCityBattleState.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      cityId_ = 0;
      isBattle_ = false;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_HolyLandCityBattleState_descriptor;
    }

    @java.lang.Override
    public xddq.pb.HolyLandCityBattleState getDefaultInstanceForType() {
      return xddq.pb.HolyLandCityBattleState.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.HolyLandCityBattleState build() {
      xddq.pb.HolyLandCityBattleState result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.HolyLandCityBattleState buildPartial() {
      xddq.pb.HolyLandCityBattleState result = new xddq.pb.HolyLandCityBattleState(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.HolyLandCityBattleState result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.cityId_ = cityId_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.isBattle_ = isBattle_;
        to_bitField0_ |= 0x00000002;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.HolyLandCityBattleState) {
        return mergeFrom((xddq.pb.HolyLandCityBattleState)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.HolyLandCityBattleState other) {
      if (other == xddq.pb.HolyLandCityBattleState.getDefaultInstance()) return this;
      if (other.hasCityId()) {
        setCityId(other.getCityId());
      }
      if (other.hasIsBattle()) {
        setIsBattle(other.getIsBattle());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              cityId_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              isBattle_ = input.readBool();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int cityId_ ;
    /**
     * <code>optional int32 cityId = 1;</code>
     * @return Whether the cityId field is set.
     */
    @java.lang.Override
    public boolean hasCityId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 cityId = 1;</code>
     * @return The cityId.
     */
    @java.lang.Override
    public int getCityId() {
      return cityId_;
    }
    /**
     * <code>optional int32 cityId = 1;</code>
     * @param value The cityId to set.
     * @return This builder for chaining.
     */
    public Builder setCityId(int value) {

      cityId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 cityId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearCityId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      cityId_ = 0;
      onChanged();
      return this;
    }

    private boolean isBattle_ ;
    /**
     * <code>optional bool isBattle = 2;</code>
     * @return Whether the isBattle field is set.
     */
    @java.lang.Override
    public boolean hasIsBattle() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional bool isBattle = 2;</code>
     * @return The isBattle.
     */
    @java.lang.Override
    public boolean getIsBattle() {
      return isBattle_;
    }
    /**
     * <code>optional bool isBattle = 2;</code>
     * @param value The isBattle to set.
     * @return This builder for chaining.
     */
    public Builder setIsBattle(boolean value) {

      isBattle_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional bool isBattle = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearIsBattle() {
      bitField0_ = (bitField0_ & ~0x00000002);
      isBattle_ = false;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.HolyLandCityBattleState)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.HolyLandCityBattleState)
  private static final xddq.pb.HolyLandCityBattleState DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.HolyLandCityBattleState();
  }

  public static xddq.pb.HolyLandCityBattleState getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<HolyLandCityBattleState>
      PARSER = new com.google.protobuf.AbstractParser<HolyLandCityBattleState>() {
    @java.lang.Override
    public HolyLandCityBattleState parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<HolyLandCityBattleState> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<HolyLandCityBattleState> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.HolyLandCityBattleState getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

