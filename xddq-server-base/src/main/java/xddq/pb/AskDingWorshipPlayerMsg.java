// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.AskDingWorshipPlayerMsg}
 */
public final class AskDingWorshipPlayerMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.AskDingWorshipPlayerMsg)
    AskDingWorshipPlayerMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      AskDingWorshipPlayerMsg.class.getName());
  }
  // Use AskDingWorshipPlayerMsg.newBuilder() to construct.
  private AskDingWorshipPlayerMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private AskDingWorshipPlayerMsg() {
    nickName_ = "";
    pet_ = emptyIntList();
    spirit_ = emptyIntList();
    petSoulShapeList_ = emptyIntList();
    petLinkageId_ = emptyIntList();
    spiritLinkageId_ = emptyIntList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_AskDingWorshipPlayerMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_AskDingWorshipPlayerMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.AskDingWorshipPlayerMsg.class, xddq.pb.AskDingWorshipPlayerMsg.Builder.class);
  }

  private int bitField0_;
  public static final int PLAYERID_FIELD_NUMBER = 1;
  private long playerId_ = 0L;
  /**
   * <code>required int64 playerId = 1;</code>
   * @return Whether the playerId field is set.
   */
  @java.lang.Override
  public boolean hasPlayerId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int64 playerId = 1;</code>
   * @return The playerId.
   */
  @java.lang.Override
  public long getPlayerId() {
    return playerId_;
  }

  public static final int SERVERID_FIELD_NUMBER = 2;
  private long serverId_ = 0L;
  /**
   * <code>required int64 serverId = 2;</code>
   * @return Whether the serverId field is set.
   */
  @java.lang.Override
  public boolean hasServerId() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>required int64 serverId = 2;</code>
   * @return The serverId.
   */
  @java.lang.Override
  public long getServerId() {
    return serverId_;
  }

  public static final int NICKNAME_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private volatile java.lang.Object nickName_ = "";
  /**
   * <code>required string nickName = 3;</code>
   * @return Whether the nickName field is set.
   */
  @java.lang.Override
  public boolean hasNickName() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>required string nickName = 3;</code>
   * @return The nickName.
   */
  @java.lang.Override
  public java.lang.String getNickName() {
    java.lang.Object ref = nickName_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        nickName_ = s;
      }
      return s;
    }
  }
  /**
   * <code>required string nickName = 3;</code>
   * @return The bytes for nickName.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getNickNameBytes() {
    java.lang.Object ref = nickName_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      nickName_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PET_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private com.google.protobuf.Internal.IntList pet_ =
      emptyIntList();
  /**
   * <code>repeated int32 pet = 4;</code>
   * @return A list containing the pet.
   */
  @java.lang.Override
  public java.util.List<java.lang.Integer>
      getPetList() {
    return pet_;
  }
  /**
   * <code>repeated int32 pet = 4;</code>
   * @return The count of pet.
   */
  public int getPetCount() {
    return pet_.size();
  }
  /**
   * <code>repeated int32 pet = 4;</code>
   * @param index The index of the element to return.
   * @return The pet at the given index.
   */
  public int getPet(int index) {
    return pet_.getInt(index);
  }

  public static final int SPIRIT_FIELD_NUMBER = 5;
  @SuppressWarnings("serial")
  private com.google.protobuf.Internal.IntList spirit_ =
      emptyIntList();
  /**
   * <code>repeated int32 spirit = 5;</code>
   * @return A list containing the spirit.
   */
  @java.lang.Override
  public java.util.List<java.lang.Integer>
      getSpiritList() {
    return spirit_;
  }
  /**
   * <code>repeated int32 spirit = 5;</code>
   * @return The count of spirit.
   */
  public int getSpiritCount() {
    return spirit_.size();
  }
  /**
   * <code>repeated int32 spirit = 5;</code>
   * @param index The index of the element to return.
   * @return The spirit at the given index.
   */
  public int getSpirit(int index) {
    return spirit_.getInt(index);
  }

  public static final int APPEARANCEID_FIELD_NUMBER = 6;
  private int appearanceId_ = 0;
  /**
   * <code>optional int32 appearanceId = 6;</code>
   * @return Whether the appearanceId field is set.
   */
  @java.lang.Override
  public boolean hasAppearanceId() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional int32 appearanceId = 6;</code>
   * @return The appearanceId.
   */
  @java.lang.Override
  public int getAppearanceId() {
    return appearanceId_;
  }

  public static final int EQUIPCLOUDID_FIELD_NUMBER = 7;
  private int equipCloudId_ = 0;
  /**
   * <code>optional int32 equipCloudId = 7;</code>
   * @return Whether the equipCloudId field is set.
   */
  @java.lang.Override
  public boolean hasEquipCloudId() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>optional int32 equipCloudId = 7;</code>
   * @return The equipCloudId.
   */
  @java.lang.Override
  public int getEquipCloudId() {
    return equipCloudId_;
  }

  public static final int REALMSID_FIELD_NUMBER = 8;
  private int realmsId_ = 0;
  /**
   * <code>optional int32 realmsId = 8;</code>
   * @return Whether the realmsId field is set.
   */
  @java.lang.Override
  public boolean hasRealmsId() {
    return ((bitField0_ & 0x00000020) != 0);
  }
  /**
   * <code>optional int32 realmsId = 8;</code>
   * @return The realmsId.
   */
  @java.lang.Override
  public int getRealmsId() {
    return realmsId_;
  }

  public static final int HEADINFO_FIELD_NUMBER = 9;
  private xddq.pb.PlayerHeadDataMsg headInfo_;
  /**
   * <code>optional .xddq.pb.PlayerHeadDataMsg headInfo = 9;</code>
   * @return Whether the headInfo field is set.
   */
  @java.lang.Override
  public boolean hasHeadInfo() {
    return ((bitField0_ & 0x00000040) != 0);
  }
  /**
   * <code>optional .xddq.pb.PlayerHeadDataMsg headInfo = 9;</code>
   * @return The headInfo.
   */
  @java.lang.Override
  public xddq.pb.PlayerHeadDataMsg getHeadInfo() {
    return headInfo_ == null ? xddq.pb.PlayerHeadDataMsg.getDefaultInstance() : headInfo_;
  }
  /**
   * <code>optional .xddq.pb.PlayerHeadDataMsg headInfo = 9;</code>
   */
  @java.lang.Override
  public xddq.pb.PlayerHeadDataMsgOrBuilder getHeadInfoOrBuilder() {
    return headInfo_ == null ? xddq.pb.PlayerHeadDataMsg.getDefaultInstance() : headInfo_;
  }

  public static final int PETSOULSHAPELIST_FIELD_NUMBER = 10;
  @SuppressWarnings("serial")
  private com.google.protobuf.Internal.IntList petSoulShapeList_ =
      emptyIntList();
  /**
   * <code>repeated int32 petSoulShapeList = 10;</code>
   * @return A list containing the petSoulShapeList.
   */
  @java.lang.Override
  public java.util.List<java.lang.Integer>
      getPetSoulShapeListList() {
    return petSoulShapeList_;
  }
  /**
   * <code>repeated int32 petSoulShapeList = 10;</code>
   * @return The count of petSoulShapeList.
   */
  public int getPetSoulShapeListCount() {
    return petSoulShapeList_.size();
  }
  /**
   * <code>repeated int32 petSoulShapeList = 10;</code>
   * @param index The index of the element to return.
   * @return The petSoulShapeList at the given index.
   */
  public int getPetSoulShapeList(int index) {
    return petSoulShapeList_.getInt(index);
  }

  public static final int TITLEID_FIELD_NUMBER = 11;
  private int titleId_ = 0;
  /**
   * <code>optional int32 titleId = 11;</code>
   * @return Whether the titleId field is set.
   */
  @java.lang.Override
  public boolean hasTitleId() {
    return ((bitField0_ & 0x00000080) != 0);
  }
  /**
   * <code>optional int32 titleId = 11;</code>
   * @return The titleId.
   */
  @java.lang.Override
  public int getTitleId() {
    return titleId_;
  }

  public static final int PETLINKAGEID_FIELD_NUMBER = 12;
  @SuppressWarnings("serial")
  private com.google.protobuf.Internal.IntList petLinkageId_ =
      emptyIntList();
  /**
   * <code>repeated int32 petLinkageId = 12;</code>
   * @return A list containing the petLinkageId.
   */
  @java.lang.Override
  public java.util.List<java.lang.Integer>
      getPetLinkageIdList() {
    return petLinkageId_;
  }
  /**
   * <code>repeated int32 petLinkageId = 12;</code>
   * @return The count of petLinkageId.
   */
  public int getPetLinkageIdCount() {
    return petLinkageId_.size();
  }
  /**
   * <code>repeated int32 petLinkageId = 12;</code>
   * @param index The index of the element to return.
   * @return The petLinkageId at the given index.
   */
  public int getPetLinkageId(int index) {
    return petLinkageId_.getInt(index);
  }

  public static final int SPIRITLINKAGEID_FIELD_NUMBER = 13;
  @SuppressWarnings("serial")
  private com.google.protobuf.Internal.IntList spiritLinkageId_ =
      emptyIntList();
  /**
   * <code>repeated int32 spiritLinkageId = 13;</code>
   * @return A list containing the spiritLinkageId.
   */
  @java.lang.Override
  public java.util.List<java.lang.Integer>
      getSpiritLinkageIdList() {
    return spiritLinkageId_;
  }
  /**
   * <code>repeated int32 spiritLinkageId = 13;</code>
   * @return The count of spiritLinkageId.
   */
  public int getSpiritLinkageIdCount() {
    return spiritLinkageId_.size();
  }
  /**
   * <code>repeated int32 spiritLinkageId = 13;</code>
   * @param index The index of the element to return.
   * @return The spiritLinkageId at the given index.
   */
  public int getSpiritLinkageId(int index) {
    return spiritLinkageId_.getInt(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasPlayerId()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasServerId()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasNickName()) {
      memoizedIsInitialized = 0;
      return false;
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt64(1, playerId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt64(2, serverId_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 3, nickName_);
    }
    for (int i = 0; i < pet_.size(); i++) {
      output.writeInt32(4, pet_.getInt(i));
    }
    for (int i = 0; i < spirit_.size(); i++) {
      output.writeInt32(5, spirit_.getInt(i));
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt32(6, appearanceId_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      output.writeInt32(7, equipCloudId_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      output.writeInt32(8, realmsId_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      output.writeMessage(9, getHeadInfo());
    }
    for (int i = 0; i < petSoulShapeList_.size(); i++) {
      output.writeInt32(10, petSoulShapeList_.getInt(i));
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      output.writeInt32(11, titleId_);
    }
    for (int i = 0; i < petLinkageId_.size(); i++) {
      output.writeInt32(12, petLinkageId_.getInt(i));
    }
    for (int i = 0; i < spiritLinkageId_.size(); i++) {
      output.writeInt32(13, spiritLinkageId_.getInt(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(1, playerId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(2, serverId_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(3, nickName_);
    }
    {
      int dataSize = 0;
      for (int i = 0; i < pet_.size(); i++) {
        dataSize += com.google.protobuf.CodedOutputStream
          .computeInt32SizeNoTag(pet_.getInt(i));
      }
      size += dataSize;
      size += 1 * getPetList().size();
    }
    {
      int dataSize = 0;
      for (int i = 0; i < spirit_.size(); i++) {
        dataSize += com.google.protobuf.CodedOutputStream
          .computeInt32SizeNoTag(spirit_.getInt(i));
      }
      size += dataSize;
      size += 1 * getSpiritList().size();
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(6, appearanceId_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(7, equipCloudId_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(8, realmsId_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(9, getHeadInfo());
    }
    {
      int dataSize = 0;
      for (int i = 0; i < petSoulShapeList_.size(); i++) {
        dataSize += com.google.protobuf.CodedOutputStream
          .computeInt32SizeNoTag(petSoulShapeList_.getInt(i));
      }
      size += dataSize;
      size += 1 * getPetSoulShapeListList().size();
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(11, titleId_);
    }
    {
      int dataSize = 0;
      for (int i = 0; i < petLinkageId_.size(); i++) {
        dataSize += com.google.protobuf.CodedOutputStream
          .computeInt32SizeNoTag(petLinkageId_.getInt(i));
      }
      size += dataSize;
      size += 1 * getPetLinkageIdList().size();
    }
    {
      int dataSize = 0;
      for (int i = 0; i < spiritLinkageId_.size(); i++) {
        dataSize += com.google.protobuf.CodedOutputStream
          .computeInt32SizeNoTag(spiritLinkageId_.getInt(i));
      }
      size += dataSize;
      size += 1 * getSpiritLinkageIdList().size();
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.AskDingWorshipPlayerMsg)) {
      return super.equals(obj);
    }
    xddq.pb.AskDingWorshipPlayerMsg other = (xddq.pb.AskDingWorshipPlayerMsg) obj;

    if (hasPlayerId() != other.hasPlayerId()) return false;
    if (hasPlayerId()) {
      if (getPlayerId()
          != other.getPlayerId()) return false;
    }
    if (hasServerId() != other.hasServerId()) return false;
    if (hasServerId()) {
      if (getServerId()
          != other.getServerId()) return false;
    }
    if (hasNickName() != other.hasNickName()) return false;
    if (hasNickName()) {
      if (!getNickName()
          .equals(other.getNickName())) return false;
    }
    if (!getPetList()
        .equals(other.getPetList())) return false;
    if (!getSpiritList()
        .equals(other.getSpiritList())) return false;
    if (hasAppearanceId() != other.hasAppearanceId()) return false;
    if (hasAppearanceId()) {
      if (getAppearanceId()
          != other.getAppearanceId()) return false;
    }
    if (hasEquipCloudId() != other.hasEquipCloudId()) return false;
    if (hasEquipCloudId()) {
      if (getEquipCloudId()
          != other.getEquipCloudId()) return false;
    }
    if (hasRealmsId() != other.hasRealmsId()) return false;
    if (hasRealmsId()) {
      if (getRealmsId()
          != other.getRealmsId()) return false;
    }
    if (hasHeadInfo() != other.hasHeadInfo()) return false;
    if (hasHeadInfo()) {
      if (!getHeadInfo()
          .equals(other.getHeadInfo())) return false;
    }
    if (!getPetSoulShapeListList()
        .equals(other.getPetSoulShapeListList())) return false;
    if (hasTitleId() != other.hasTitleId()) return false;
    if (hasTitleId()) {
      if (getTitleId()
          != other.getTitleId()) return false;
    }
    if (!getPetLinkageIdList()
        .equals(other.getPetLinkageIdList())) return false;
    if (!getSpiritLinkageIdList()
        .equals(other.getSpiritLinkageIdList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasPlayerId()) {
      hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getPlayerId());
    }
    if (hasServerId()) {
      hash = (37 * hash) + SERVERID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getServerId());
    }
    if (hasNickName()) {
      hash = (37 * hash) + NICKNAME_FIELD_NUMBER;
      hash = (53 * hash) + getNickName().hashCode();
    }
    if (getPetCount() > 0) {
      hash = (37 * hash) + PET_FIELD_NUMBER;
      hash = (53 * hash) + getPetList().hashCode();
    }
    if (getSpiritCount() > 0) {
      hash = (37 * hash) + SPIRIT_FIELD_NUMBER;
      hash = (53 * hash) + getSpiritList().hashCode();
    }
    if (hasAppearanceId()) {
      hash = (37 * hash) + APPEARANCEID_FIELD_NUMBER;
      hash = (53 * hash) + getAppearanceId();
    }
    if (hasEquipCloudId()) {
      hash = (37 * hash) + EQUIPCLOUDID_FIELD_NUMBER;
      hash = (53 * hash) + getEquipCloudId();
    }
    if (hasRealmsId()) {
      hash = (37 * hash) + REALMSID_FIELD_NUMBER;
      hash = (53 * hash) + getRealmsId();
    }
    if (hasHeadInfo()) {
      hash = (37 * hash) + HEADINFO_FIELD_NUMBER;
      hash = (53 * hash) + getHeadInfo().hashCode();
    }
    if (getPetSoulShapeListCount() > 0) {
      hash = (37 * hash) + PETSOULSHAPELIST_FIELD_NUMBER;
      hash = (53 * hash) + getPetSoulShapeListList().hashCode();
    }
    if (hasTitleId()) {
      hash = (37 * hash) + TITLEID_FIELD_NUMBER;
      hash = (53 * hash) + getTitleId();
    }
    if (getPetLinkageIdCount() > 0) {
      hash = (37 * hash) + PETLINKAGEID_FIELD_NUMBER;
      hash = (53 * hash) + getPetLinkageIdList().hashCode();
    }
    if (getSpiritLinkageIdCount() > 0) {
      hash = (37 * hash) + SPIRITLINKAGEID_FIELD_NUMBER;
      hash = (53 * hash) + getSpiritLinkageIdList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.AskDingWorshipPlayerMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.AskDingWorshipPlayerMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.AskDingWorshipPlayerMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.AskDingWorshipPlayerMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.AskDingWorshipPlayerMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.AskDingWorshipPlayerMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.AskDingWorshipPlayerMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.AskDingWorshipPlayerMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.AskDingWorshipPlayerMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.AskDingWorshipPlayerMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.AskDingWorshipPlayerMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.AskDingWorshipPlayerMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.AskDingWorshipPlayerMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.AskDingWorshipPlayerMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.AskDingWorshipPlayerMsg)
      xddq.pb.AskDingWorshipPlayerMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_AskDingWorshipPlayerMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_AskDingWorshipPlayerMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.AskDingWorshipPlayerMsg.class, xddq.pb.AskDingWorshipPlayerMsg.Builder.class);
    }

    // Construct using xddq.pb.AskDingWorshipPlayerMsg.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetHeadInfoFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      playerId_ = 0L;
      serverId_ = 0L;
      nickName_ = "";
      pet_ = emptyIntList();
      spirit_ = emptyIntList();
      appearanceId_ = 0;
      equipCloudId_ = 0;
      realmsId_ = 0;
      headInfo_ = null;
      if (headInfoBuilder_ != null) {
        headInfoBuilder_.dispose();
        headInfoBuilder_ = null;
      }
      petSoulShapeList_ = emptyIntList();
      titleId_ = 0;
      petLinkageId_ = emptyIntList();
      spiritLinkageId_ = emptyIntList();
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_AskDingWorshipPlayerMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.AskDingWorshipPlayerMsg getDefaultInstanceForType() {
      return xddq.pb.AskDingWorshipPlayerMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.AskDingWorshipPlayerMsg build() {
      xddq.pb.AskDingWorshipPlayerMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.AskDingWorshipPlayerMsg buildPartial() {
      xddq.pb.AskDingWorshipPlayerMsg result = new xddq.pb.AskDingWorshipPlayerMsg(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.AskDingWorshipPlayerMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.playerId_ = playerId_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.serverId_ = serverId_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.nickName_ = nickName_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        pet_.makeImmutable();
        result.pet_ = pet_;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        spirit_.makeImmutable();
        result.spirit_ = spirit_;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.appearanceId_ = appearanceId_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.equipCloudId_ = equipCloudId_;
        to_bitField0_ |= 0x00000010;
      }
      if (((from_bitField0_ & 0x00000080) != 0)) {
        result.realmsId_ = realmsId_;
        to_bitField0_ |= 0x00000020;
      }
      if (((from_bitField0_ & 0x00000100) != 0)) {
        result.headInfo_ = headInfoBuilder_ == null
            ? headInfo_
            : headInfoBuilder_.build();
        to_bitField0_ |= 0x00000040;
      }
      if (((from_bitField0_ & 0x00000200) != 0)) {
        petSoulShapeList_.makeImmutable();
        result.petSoulShapeList_ = petSoulShapeList_;
      }
      if (((from_bitField0_ & 0x00000400) != 0)) {
        result.titleId_ = titleId_;
        to_bitField0_ |= 0x00000080;
      }
      if (((from_bitField0_ & 0x00000800) != 0)) {
        petLinkageId_.makeImmutable();
        result.petLinkageId_ = petLinkageId_;
      }
      if (((from_bitField0_ & 0x00001000) != 0)) {
        spiritLinkageId_.makeImmutable();
        result.spiritLinkageId_ = spiritLinkageId_;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.AskDingWorshipPlayerMsg) {
        return mergeFrom((xddq.pb.AskDingWorshipPlayerMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.AskDingWorshipPlayerMsg other) {
      if (other == xddq.pb.AskDingWorshipPlayerMsg.getDefaultInstance()) return this;
      if (other.hasPlayerId()) {
        setPlayerId(other.getPlayerId());
      }
      if (other.hasServerId()) {
        setServerId(other.getServerId());
      }
      if (other.hasNickName()) {
        nickName_ = other.nickName_;
        bitField0_ |= 0x00000004;
        onChanged();
      }
      if (!other.pet_.isEmpty()) {
        if (pet_.isEmpty()) {
          pet_ = other.pet_;
          pet_.makeImmutable();
          bitField0_ |= 0x00000008;
        } else {
          ensurePetIsMutable();
          pet_.addAll(other.pet_);
        }
        onChanged();
      }
      if (!other.spirit_.isEmpty()) {
        if (spirit_.isEmpty()) {
          spirit_ = other.spirit_;
          spirit_.makeImmutable();
          bitField0_ |= 0x00000010;
        } else {
          ensureSpiritIsMutable();
          spirit_.addAll(other.spirit_);
        }
        onChanged();
      }
      if (other.hasAppearanceId()) {
        setAppearanceId(other.getAppearanceId());
      }
      if (other.hasEquipCloudId()) {
        setEquipCloudId(other.getEquipCloudId());
      }
      if (other.hasRealmsId()) {
        setRealmsId(other.getRealmsId());
      }
      if (other.hasHeadInfo()) {
        mergeHeadInfo(other.getHeadInfo());
      }
      if (!other.petSoulShapeList_.isEmpty()) {
        if (petSoulShapeList_.isEmpty()) {
          petSoulShapeList_ = other.petSoulShapeList_;
          petSoulShapeList_.makeImmutable();
          bitField0_ |= 0x00000200;
        } else {
          ensurePetSoulShapeListIsMutable();
          petSoulShapeList_.addAll(other.petSoulShapeList_);
        }
        onChanged();
      }
      if (other.hasTitleId()) {
        setTitleId(other.getTitleId());
      }
      if (!other.petLinkageId_.isEmpty()) {
        if (petLinkageId_.isEmpty()) {
          petLinkageId_ = other.petLinkageId_;
          petLinkageId_.makeImmutable();
          bitField0_ |= 0x00000800;
        } else {
          ensurePetLinkageIdIsMutable();
          petLinkageId_.addAll(other.petLinkageId_);
        }
        onChanged();
      }
      if (!other.spiritLinkageId_.isEmpty()) {
        if (spiritLinkageId_.isEmpty()) {
          spiritLinkageId_ = other.spiritLinkageId_;
          spiritLinkageId_.makeImmutable();
          bitField0_ |= 0x00001000;
        } else {
          ensureSpiritLinkageIdIsMutable();
          spiritLinkageId_.addAll(other.spiritLinkageId_);
        }
        onChanged();
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasPlayerId()) {
        return false;
      }
      if (!hasServerId()) {
        return false;
      }
      if (!hasNickName()) {
        return false;
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              playerId_ = input.readInt64();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              serverId_ = input.readInt64();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 26: {
              nickName_ = input.readBytes();
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 32: {
              int v = input.readInt32();
              ensurePetIsMutable();
              pet_.addInt(v);
              break;
            } // case 32
            case 34: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              ensurePetIsMutable();
              while (input.getBytesUntilLimit() > 0) {
                pet_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            } // case 34
            case 40: {
              int v = input.readInt32();
              ensureSpiritIsMutable();
              spirit_.addInt(v);
              break;
            } // case 40
            case 42: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              ensureSpiritIsMutable();
              while (input.getBytesUntilLimit() > 0) {
                spirit_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            } // case 42
            case 48: {
              appearanceId_ = input.readInt32();
              bitField0_ |= 0x00000020;
              break;
            } // case 48
            case 56: {
              equipCloudId_ = input.readInt32();
              bitField0_ |= 0x00000040;
              break;
            } // case 56
            case 64: {
              realmsId_ = input.readInt32();
              bitField0_ |= 0x00000080;
              break;
            } // case 64
            case 74: {
              input.readMessage(
                  internalGetHeadInfoFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000100;
              break;
            } // case 74
            case 80: {
              int v = input.readInt32();
              ensurePetSoulShapeListIsMutable();
              petSoulShapeList_.addInt(v);
              break;
            } // case 80
            case 82: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              ensurePetSoulShapeListIsMutable();
              while (input.getBytesUntilLimit() > 0) {
                petSoulShapeList_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            } // case 82
            case 88: {
              titleId_ = input.readInt32();
              bitField0_ |= 0x00000400;
              break;
            } // case 88
            case 96: {
              int v = input.readInt32();
              ensurePetLinkageIdIsMutable();
              petLinkageId_.addInt(v);
              break;
            } // case 96
            case 98: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              ensurePetLinkageIdIsMutable();
              while (input.getBytesUntilLimit() > 0) {
                petLinkageId_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            } // case 98
            case 104: {
              int v = input.readInt32();
              ensureSpiritLinkageIdIsMutable();
              spiritLinkageId_.addInt(v);
              break;
            } // case 104
            case 106: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              ensureSpiritLinkageIdIsMutable();
              while (input.getBytesUntilLimit() > 0) {
                spiritLinkageId_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            } // case 106
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private long playerId_ ;
    /**
     * <code>required int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int64 playerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }
    /**
     * <code>required int64 playerId = 1;</code>
     * @param value The playerId to set.
     * @return This builder for chaining.
     */
    public Builder setPlayerId(long value) {

      playerId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int64 playerId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearPlayerId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      playerId_ = 0L;
      onChanged();
      return this;
    }

    private long serverId_ ;
    /**
     * <code>required int64 serverId = 2;</code>
     * @return Whether the serverId field is set.
     */
    @java.lang.Override
    public boolean hasServerId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>required int64 serverId = 2;</code>
     * @return The serverId.
     */
    @java.lang.Override
    public long getServerId() {
      return serverId_;
    }
    /**
     * <code>required int64 serverId = 2;</code>
     * @param value The serverId to set.
     * @return This builder for chaining.
     */
    public Builder setServerId(long value) {

      serverId_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>required int64 serverId = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearServerId() {
      bitField0_ = (bitField0_ & ~0x00000002);
      serverId_ = 0L;
      onChanged();
      return this;
    }

    private java.lang.Object nickName_ = "";
    /**
     * <code>required string nickName = 3;</code>
     * @return Whether the nickName field is set.
     */
    public boolean hasNickName() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>required string nickName = 3;</code>
     * @return The nickName.
     */
    public java.lang.String getNickName() {
      java.lang.Object ref = nickName_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          nickName_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>required string nickName = 3;</code>
     * @return The bytes for nickName.
     */
    public com.google.protobuf.ByteString
        getNickNameBytes() {
      java.lang.Object ref = nickName_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        nickName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>required string nickName = 3;</code>
     * @param value The nickName to set.
     * @return This builder for chaining.
     */
    public Builder setNickName(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      nickName_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>required string nickName = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearNickName() {
      nickName_ = getDefaultInstance().getNickName();
      bitField0_ = (bitField0_ & ~0x00000004);
      onChanged();
      return this;
    }
    /**
     * <code>required string nickName = 3;</code>
     * @param value The bytes for nickName to set.
     * @return This builder for chaining.
     */
    public Builder setNickNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      nickName_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }

    private com.google.protobuf.Internal.IntList pet_ = emptyIntList();
    private void ensurePetIsMutable() {
      if (!pet_.isModifiable()) {
        pet_ = makeMutableCopy(pet_);
      }
      bitField0_ |= 0x00000008;
    }
    /**
     * <code>repeated int32 pet = 4;</code>
     * @return A list containing the pet.
     */
    public java.util.List<java.lang.Integer>
        getPetList() {
      pet_.makeImmutable();
      return pet_;
    }
    /**
     * <code>repeated int32 pet = 4;</code>
     * @return The count of pet.
     */
    public int getPetCount() {
      return pet_.size();
    }
    /**
     * <code>repeated int32 pet = 4;</code>
     * @param index The index of the element to return.
     * @return The pet at the given index.
     */
    public int getPet(int index) {
      return pet_.getInt(index);
    }
    /**
     * <code>repeated int32 pet = 4;</code>
     * @param index The index to set the value at.
     * @param value The pet to set.
     * @return This builder for chaining.
     */
    public Builder setPet(
        int index, int value) {

      ensurePetIsMutable();
      pet_.setInt(index, value);
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 pet = 4;</code>
     * @param value The pet to add.
     * @return This builder for chaining.
     */
    public Builder addPet(int value) {

      ensurePetIsMutable();
      pet_.addInt(value);
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 pet = 4;</code>
     * @param values The pet to add.
     * @return This builder for chaining.
     */
    public Builder addAllPet(
        java.lang.Iterable<? extends java.lang.Integer> values) {
      ensurePetIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, pet_);
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 pet = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearPet() {
      pet_ = emptyIntList();
      bitField0_ = (bitField0_ & ~0x00000008);
      onChanged();
      return this;
    }

    private com.google.protobuf.Internal.IntList spirit_ = emptyIntList();
    private void ensureSpiritIsMutable() {
      if (!spirit_.isModifiable()) {
        spirit_ = makeMutableCopy(spirit_);
      }
      bitField0_ |= 0x00000010;
    }
    /**
     * <code>repeated int32 spirit = 5;</code>
     * @return A list containing the spirit.
     */
    public java.util.List<java.lang.Integer>
        getSpiritList() {
      spirit_.makeImmutable();
      return spirit_;
    }
    /**
     * <code>repeated int32 spirit = 5;</code>
     * @return The count of spirit.
     */
    public int getSpiritCount() {
      return spirit_.size();
    }
    /**
     * <code>repeated int32 spirit = 5;</code>
     * @param index The index of the element to return.
     * @return The spirit at the given index.
     */
    public int getSpirit(int index) {
      return spirit_.getInt(index);
    }
    /**
     * <code>repeated int32 spirit = 5;</code>
     * @param index The index to set the value at.
     * @param value The spirit to set.
     * @return This builder for chaining.
     */
    public Builder setSpirit(
        int index, int value) {

      ensureSpiritIsMutable();
      spirit_.setInt(index, value);
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 spirit = 5;</code>
     * @param value The spirit to add.
     * @return This builder for chaining.
     */
    public Builder addSpirit(int value) {

      ensureSpiritIsMutable();
      spirit_.addInt(value);
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 spirit = 5;</code>
     * @param values The spirit to add.
     * @return This builder for chaining.
     */
    public Builder addAllSpirit(
        java.lang.Iterable<? extends java.lang.Integer> values) {
      ensureSpiritIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, spirit_);
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 spirit = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearSpirit() {
      spirit_ = emptyIntList();
      bitField0_ = (bitField0_ & ~0x00000010);
      onChanged();
      return this;
    }

    private int appearanceId_ ;
    /**
     * <code>optional int32 appearanceId = 6;</code>
     * @return Whether the appearanceId field is set.
     */
    @java.lang.Override
    public boolean hasAppearanceId() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional int32 appearanceId = 6;</code>
     * @return The appearanceId.
     */
    @java.lang.Override
    public int getAppearanceId() {
      return appearanceId_;
    }
    /**
     * <code>optional int32 appearanceId = 6;</code>
     * @param value The appearanceId to set.
     * @return This builder for chaining.
     */
    public Builder setAppearanceId(int value) {

      appearanceId_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 appearanceId = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearAppearanceId() {
      bitField0_ = (bitField0_ & ~0x00000020);
      appearanceId_ = 0;
      onChanged();
      return this;
    }

    private int equipCloudId_ ;
    /**
     * <code>optional int32 equipCloudId = 7;</code>
     * @return Whether the equipCloudId field is set.
     */
    @java.lang.Override
    public boolean hasEquipCloudId() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <code>optional int32 equipCloudId = 7;</code>
     * @return The equipCloudId.
     */
    @java.lang.Override
    public int getEquipCloudId() {
      return equipCloudId_;
    }
    /**
     * <code>optional int32 equipCloudId = 7;</code>
     * @param value The equipCloudId to set.
     * @return This builder for chaining.
     */
    public Builder setEquipCloudId(int value) {

      equipCloudId_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 equipCloudId = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearEquipCloudId() {
      bitField0_ = (bitField0_ & ~0x00000040);
      equipCloudId_ = 0;
      onChanged();
      return this;
    }

    private int realmsId_ ;
    /**
     * <code>optional int32 realmsId = 8;</code>
     * @return Whether the realmsId field is set.
     */
    @java.lang.Override
    public boolean hasRealmsId() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <code>optional int32 realmsId = 8;</code>
     * @return The realmsId.
     */
    @java.lang.Override
    public int getRealmsId() {
      return realmsId_;
    }
    /**
     * <code>optional int32 realmsId = 8;</code>
     * @param value The realmsId to set.
     * @return This builder for chaining.
     */
    public Builder setRealmsId(int value) {

      realmsId_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 realmsId = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearRealmsId() {
      bitField0_ = (bitField0_ & ~0x00000080);
      realmsId_ = 0;
      onChanged();
      return this;
    }

    private xddq.pb.PlayerHeadDataMsg headInfo_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PlayerHeadDataMsg, xddq.pb.PlayerHeadDataMsg.Builder, xddq.pb.PlayerHeadDataMsgOrBuilder> headInfoBuilder_;
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg headInfo = 9;</code>
     * @return Whether the headInfo field is set.
     */
    public boolean hasHeadInfo() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg headInfo = 9;</code>
     * @return The headInfo.
     */
    public xddq.pb.PlayerHeadDataMsg getHeadInfo() {
      if (headInfoBuilder_ == null) {
        return headInfo_ == null ? xddq.pb.PlayerHeadDataMsg.getDefaultInstance() : headInfo_;
      } else {
        return headInfoBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg headInfo = 9;</code>
     */
    public Builder setHeadInfo(xddq.pb.PlayerHeadDataMsg value) {
      if (headInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        headInfo_ = value;
      } else {
        headInfoBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg headInfo = 9;</code>
     */
    public Builder setHeadInfo(
        xddq.pb.PlayerHeadDataMsg.Builder builderForValue) {
      if (headInfoBuilder_ == null) {
        headInfo_ = builderForValue.build();
      } else {
        headInfoBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg headInfo = 9;</code>
     */
    public Builder mergeHeadInfo(xddq.pb.PlayerHeadDataMsg value) {
      if (headInfoBuilder_ == null) {
        if (((bitField0_ & 0x00000100) != 0) &&
          headInfo_ != null &&
          headInfo_ != xddq.pb.PlayerHeadDataMsg.getDefaultInstance()) {
          getHeadInfoBuilder().mergeFrom(value);
        } else {
          headInfo_ = value;
        }
      } else {
        headInfoBuilder_.mergeFrom(value);
      }
      if (headInfo_ != null) {
        bitField0_ |= 0x00000100;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg headInfo = 9;</code>
     */
    public Builder clearHeadInfo() {
      bitField0_ = (bitField0_ & ~0x00000100);
      headInfo_ = null;
      if (headInfoBuilder_ != null) {
        headInfoBuilder_.dispose();
        headInfoBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg headInfo = 9;</code>
     */
    public xddq.pb.PlayerHeadDataMsg.Builder getHeadInfoBuilder() {
      bitField0_ |= 0x00000100;
      onChanged();
      return internalGetHeadInfoFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg headInfo = 9;</code>
     */
    public xddq.pb.PlayerHeadDataMsgOrBuilder getHeadInfoOrBuilder() {
      if (headInfoBuilder_ != null) {
        return headInfoBuilder_.getMessageOrBuilder();
      } else {
        return headInfo_ == null ?
            xddq.pb.PlayerHeadDataMsg.getDefaultInstance() : headInfo_;
      }
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg headInfo = 9;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PlayerHeadDataMsg, xddq.pb.PlayerHeadDataMsg.Builder, xddq.pb.PlayerHeadDataMsgOrBuilder> 
        internalGetHeadInfoFieldBuilder() {
      if (headInfoBuilder_ == null) {
        headInfoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.PlayerHeadDataMsg, xddq.pb.PlayerHeadDataMsg.Builder, xddq.pb.PlayerHeadDataMsgOrBuilder>(
                getHeadInfo(),
                getParentForChildren(),
                isClean());
        headInfo_ = null;
      }
      return headInfoBuilder_;
    }

    private com.google.protobuf.Internal.IntList petSoulShapeList_ = emptyIntList();
    private void ensurePetSoulShapeListIsMutable() {
      if (!petSoulShapeList_.isModifiable()) {
        petSoulShapeList_ = makeMutableCopy(petSoulShapeList_);
      }
      bitField0_ |= 0x00000200;
    }
    /**
     * <code>repeated int32 petSoulShapeList = 10;</code>
     * @return A list containing the petSoulShapeList.
     */
    public java.util.List<java.lang.Integer>
        getPetSoulShapeListList() {
      petSoulShapeList_.makeImmutable();
      return petSoulShapeList_;
    }
    /**
     * <code>repeated int32 petSoulShapeList = 10;</code>
     * @return The count of petSoulShapeList.
     */
    public int getPetSoulShapeListCount() {
      return petSoulShapeList_.size();
    }
    /**
     * <code>repeated int32 petSoulShapeList = 10;</code>
     * @param index The index of the element to return.
     * @return The petSoulShapeList at the given index.
     */
    public int getPetSoulShapeList(int index) {
      return petSoulShapeList_.getInt(index);
    }
    /**
     * <code>repeated int32 petSoulShapeList = 10;</code>
     * @param index The index to set the value at.
     * @param value The petSoulShapeList to set.
     * @return This builder for chaining.
     */
    public Builder setPetSoulShapeList(
        int index, int value) {

      ensurePetSoulShapeListIsMutable();
      petSoulShapeList_.setInt(index, value);
      bitField0_ |= 0x00000200;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 petSoulShapeList = 10;</code>
     * @param value The petSoulShapeList to add.
     * @return This builder for chaining.
     */
    public Builder addPetSoulShapeList(int value) {

      ensurePetSoulShapeListIsMutable();
      petSoulShapeList_.addInt(value);
      bitField0_ |= 0x00000200;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 petSoulShapeList = 10;</code>
     * @param values The petSoulShapeList to add.
     * @return This builder for chaining.
     */
    public Builder addAllPetSoulShapeList(
        java.lang.Iterable<? extends java.lang.Integer> values) {
      ensurePetSoulShapeListIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, petSoulShapeList_);
      bitField0_ |= 0x00000200;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 petSoulShapeList = 10;</code>
     * @return This builder for chaining.
     */
    public Builder clearPetSoulShapeList() {
      petSoulShapeList_ = emptyIntList();
      bitField0_ = (bitField0_ & ~0x00000200);
      onChanged();
      return this;
    }

    private int titleId_ ;
    /**
     * <code>optional int32 titleId = 11;</code>
     * @return Whether the titleId field is set.
     */
    @java.lang.Override
    public boolean hasTitleId() {
      return ((bitField0_ & 0x00000400) != 0);
    }
    /**
     * <code>optional int32 titleId = 11;</code>
     * @return The titleId.
     */
    @java.lang.Override
    public int getTitleId() {
      return titleId_;
    }
    /**
     * <code>optional int32 titleId = 11;</code>
     * @param value The titleId to set.
     * @return This builder for chaining.
     */
    public Builder setTitleId(int value) {

      titleId_ = value;
      bitField0_ |= 0x00000400;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 titleId = 11;</code>
     * @return This builder for chaining.
     */
    public Builder clearTitleId() {
      bitField0_ = (bitField0_ & ~0x00000400);
      titleId_ = 0;
      onChanged();
      return this;
    }

    private com.google.protobuf.Internal.IntList petLinkageId_ = emptyIntList();
    private void ensurePetLinkageIdIsMutable() {
      if (!petLinkageId_.isModifiable()) {
        petLinkageId_ = makeMutableCopy(petLinkageId_);
      }
      bitField0_ |= 0x00000800;
    }
    /**
     * <code>repeated int32 petLinkageId = 12;</code>
     * @return A list containing the petLinkageId.
     */
    public java.util.List<java.lang.Integer>
        getPetLinkageIdList() {
      petLinkageId_.makeImmutable();
      return petLinkageId_;
    }
    /**
     * <code>repeated int32 petLinkageId = 12;</code>
     * @return The count of petLinkageId.
     */
    public int getPetLinkageIdCount() {
      return petLinkageId_.size();
    }
    /**
     * <code>repeated int32 petLinkageId = 12;</code>
     * @param index The index of the element to return.
     * @return The petLinkageId at the given index.
     */
    public int getPetLinkageId(int index) {
      return petLinkageId_.getInt(index);
    }
    /**
     * <code>repeated int32 petLinkageId = 12;</code>
     * @param index The index to set the value at.
     * @param value The petLinkageId to set.
     * @return This builder for chaining.
     */
    public Builder setPetLinkageId(
        int index, int value) {

      ensurePetLinkageIdIsMutable();
      petLinkageId_.setInt(index, value);
      bitField0_ |= 0x00000800;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 petLinkageId = 12;</code>
     * @param value The petLinkageId to add.
     * @return This builder for chaining.
     */
    public Builder addPetLinkageId(int value) {

      ensurePetLinkageIdIsMutable();
      petLinkageId_.addInt(value);
      bitField0_ |= 0x00000800;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 petLinkageId = 12;</code>
     * @param values The petLinkageId to add.
     * @return This builder for chaining.
     */
    public Builder addAllPetLinkageId(
        java.lang.Iterable<? extends java.lang.Integer> values) {
      ensurePetLinkageIdIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, petLinkageId_);
      bitField0_ |= 0x00000800;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 petLinkageId = 12;</code>
     * @return This builder for chaining.
     */
    public Builder clearPetLinkageId() {
      petLinkageId_ = emptyIntList();
      bitField0_ = (bitField0_ & ~0x00000800);
      onChanged();
      return this;
    }

    private com.google.protobuf.Internal.IntList spiritLinkageId_ = emptyIntList();
    private void ensureSpiritLinkageIdIsMutable() {
      if (!spiritLinkageId_.isModifiable()) {
        spiritLinkageId_ = makeMutableCopy(spiritLinkageId_);
      }
      bitField0_ |= 0x00001000;
    }
    /**
     * <code>repeated int32 spiritLinkageId = 13;</code>
     * @return A list containing the spiritLinkageId.
     */
    public java.util.List<java.lang.Integer>
        getSpiritLinkageIdList() {
      spiritLinkageId_.makeImmutable();
      return spiritLinkageId_;
    }
    /**
     * <code>repeated int32 spiritLinkageId = 13;</code>
     * @return The count of spiritLinkageId.
     */
    public int getSpiritLinkageIdCount() {
      return spiritLinkageId_.size();
    }
    /**
     * <code>repeated int32 spiritLinkageId = 13;</code>
     * @param index The index of the element to return.
     * @return The spiritLinkageId at the given index.
     */
    public int getSpiritLinkageId(int index) {
      return spiritLinkageId_.getInt(index);
    }
    /**
     * <code>repeated int32 spiritLinkageId = 13;</code>
     * @param index The index to set the value at.
     * @param value The spiritLinkageId to set.
     * @return This builder for chaining.
     */
    public Builder setSpiritLinkageId(
        int index, int value) {

      ensureSpiritLinkageIdIsMutable();
      spiritLinkageId_.setInt(index, value);
      bitField0_ |= 0x00001000;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 spiritLinkageId = 13;</code>
     * @param value The spiritLinkageId to add.
     * @return This builder for chaining.
     */
    public Builder addSpiritLinkageId(int value) {

      ensureSpiritLinkageIdIsMutable();
      spiritLinkageId_.addInt(value);
      bitField0_ |= 0x00001000;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 spiritLinkageId = 13;</code>
     * @param values The spiritLinkageId to add.
     * @return This builder for chaining.
     */
    public Builder addAllSpiritLinkageId(
        java.lang.Iterable<? extends java.lang.Integer> values) {
      ensureSpiritLinkageIdIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, spiritLinkageId_);
      bitField0_ |= 0x00001000;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 spiritLinkageId = 13;</code>
     * @return This builder for chaining.
     */
    public Builder clearSpiritLinkageId() {
      spiritLinkageId_ = emptyIntList();
      bitField0_ = (bitField0_ & ~0x00001000);
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.AskDingWorshipPlayerMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.AskDingWorshipPlayerMsg)
  private static final xddq.pb.AskDingWorshipPlayerMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.AskDingWorshipPlayerMsg();
  }

  public static xddq.pb.AskDingWorshipPlayerMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<AskDingWorshipPlayerMsg>
      PARSER = new com.google.protobuf.AbstractParser<AskDingWorshipPlayerMsg>() {
    @java.lang.Override
    public AskDingWorshipPlayerMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<AskDingWorshipPlayerMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<AskDingWorshipPlayerMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.AskDingWorshipPlayerMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

