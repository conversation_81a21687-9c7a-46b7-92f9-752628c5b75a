// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.GroupPurchaseApplyLeaderInfo}
 */
public final class GroupPurchaseApplyLeaderInfo extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.GroupPurchaseApplyLeaderInfo)
    GroupPurchaseApplyLeaderInfoOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      GroupPurchaseApplyLeaderInfo.class.getName());
  }
  // Use GroupPurchaseApplyLeaderInfo.newBuilder() to construct.
  private GroupPurchaseApplyLeaderInfo(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private GroupPurchaseApplyLeaderInfo() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_GroupPurchaseApplyLeaderInfo_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_GroupPurchaseApplyLeaderInfo_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.GroupPurchaseApplyLeaderInfo.class, xddq.pb.GroupPurchaseApplyLeaderInfo.Builder.class);
  }

  private int bitField0_;
  public static final int APPLYPLAYERID_FIELD_NUMBER = 1;
  private long applyPlayerId_ = 0L;
  /**
   * <code>optional int64 applyPlayerId = 1;</code>
   * @return Whether the applyPlayerId field is set.
   */
  @java.lang.Override
  public boolean hasApplyPlayerId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int64 applyPlayerId = 1;</code>
   * @return The applyPlayerId.
   */
  @java.lang.Override
  public long getApplyPlayerId() {
    return applyPlayerId_;
  }

  public static final int APPLYENDTIME_FIELD_NUMBER = 2;
  private long applyEndTime_ = 0L;
  /**
   * <code>optional int64 applyEndTime = 2;</code>
   * @return Whether the applyEndTime field is set.
   */
  @java.lang.Override
  public boolean hasApplyEndTime() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int64 applyEndTime = 2;</code>
   * @return The applyEndTime.
   */
  @java.lang.Override
  public long getApplyEndTime() {
    return applyEndTime_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt64(1, applyPlayerId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt64(2, applyEndTime_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(1, applyPlayerId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(2, applyEndTime_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.GroupPurchaseApplyLeaderInfo)) {
      return super.equals(obj);
    }
    xddq.pb.GroupPurchaseApplyLeaderInfo other = (xddq.pb.GroupPurchaseApplyLeaderInfo) obj;

    if (hasApplyPlayerId() != other.hasApplyPlayerId()) return false;
    if (hasApplyPlayerId()) {
      if (getApplyPlayerId()
          != other.getApplyPlayerId()) return false;
    }
    if (hasApplyEndTime() != other.hasApplyEndTime()) return false;
    if (hasApplyEndTime()) {
      if (getApplyEndTime()
          != other.getApplyEndTime()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasApplyPlayerId()) {
      hash = (37 * hash) + APPLYPLAYERID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getApplyPlayerId());
    }
    if (hasApplyEndTime()) {
      hash = (37 * hash) + APPLYENDTIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getApplyEndTime());
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.GroupPurchaseApplyLeaderInfo parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GroupPurchaseApplyLeaderInfo parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GroupPurchaseApplyLeaderInfo parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GroupPurchaseApplyLeaderInfo parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GroupPurchaseApplyLeaderInfo parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GroupPurchaseApplyLeaderInfo parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GroupPurchaseApplyLeaderInfo parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.GroupPurchaseApplyLeaderInfo parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.GroupPurchaseApplyLeaderInfo parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.GroupPurchaseApplyLeaderInfo parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.GroupPurchaseApplyLeaderInfo parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.GroupPurchaseApplyLeaderInfo parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.GroupPurchaseApplyLeaderInfo prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.GroupPurchaseApplyLeaderInfo}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.GroupPurchaseApplyLeaderInfo)
      xddq.pb.GroupPurchaseApplyLeaderInfoOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GroupPurchaseApplyLeaderInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GroupPurchaseApplyLeaderInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.GroupPurchaseApplyLeaderInfo.class, xddq.pb.GroupPurchaseApplyLeaderInfo.Builder.class);
    }

    // Construct using xddq.pb.GroupPurchaseApplyLeaderInfo.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      applyPlayerId_ = 0L;
      applyEndTime_ = 0L;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GroupPurchaseApplyLeaderInfo_descriptor;
    }

    @java.lang.Override
    public xddq.pb.GroupPurchaseApplyLeaderInfo getDefaultInstanceForType() {
      return xddq.pb.GroupPurchaseApplyLeaderInfo.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.GroupPurchaseApplyLeaderInfo build() {
      xddq.pb.GroupPurchaseApplyLeaderInfo result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.GroupPurchaseApplyLeaderInfo buildPartial() {
      xddq.pb.GroupPurchaseApplyLeaderInfo result = new xddq.pb.GroupPurchaseApplyLeaderInfo(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.GroupPurchaseApplyLeaderInfo result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.applyPlayerId_ = applyPlayerId_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.applyEndTime_ = applyEndTime_;
        to_bitField0_ |= 0x00000002;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.GroupPurchaseApplyLeaderInfo) {
        return mergeFrom((xddq.pb.GroupPurchaseApplyLeaderInfo)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.GroupPurchaseApplyLeaderInfo other) {
      if (other == xddq.pb.GroupPurchaseApplyLeaderInfo.getDefaultInstance()) return this;
      if (other.hasApplyPlayerId()) {
        setApplyPlayerId(other.getApplyPlayerId());
      }
      if (other.hasApplyEndTime()) {
        setApplyEndTime(other.getApplyEndTime());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              applyPlayerId_ = input.readInt64();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              applyEndTime_ = input.readInt64();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private long applyPlayerId_ ;
    /**
     * <code>optional int64 applyPlayerId = 1;</code>
     * @return Whether the applyPlayerId field is set.
     */
    @java.lang.Override
    public boolean hasApplyPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 applyPlayerId = 1;</code>
     * @return The applyPlayerId.
     */
    @java.lang.Override
    public long getApplyPlayerId() {
      return applyPlayerId_;
    }
    /**
     * <code>optional int64 applyPlayerId = 1;</code>
     * @param value The applyPlayerId to set.
     * @return This builder for chaining.
     */
    public Builder setApplyPlayerId(long value) {

      applyPlayerId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 applyPlayerId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearApplyPlayerId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      applyPlayerId_ = 0L;
      onChanged();
      return this;
    }

    private long applyEndTime_ ;
    /**
     * <code>optional int64 applyEndTime = 2;</code>
     * @return Whether the applyEndTime field is set.
     */
    @java.lang.Override
    public boolean hasApplyEndTime() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int64 applyEndTime = 2;</code>
     * @return The applyEndTime.
     */
    @java.lang.Override
    public long getApplyEndTime() {
      return applyEndTime_;
    }
    /**
     * <code>optional int64 applyEndTime = 2;</code>
     * @param value The applyEndTime to set.
     * @return This builder for chaining.
     */
    public Builder setApplyEndTime(long value) {

      applyEndTime_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 applyEndTime = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearApplyEndTime() {
      bitField0_ = (bitField0_ & ~0x00000002);
      applyEndTime_ = 0L;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.GroupPurchaseApplyLeaderInfo)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.GroupPurchaseApplyLeaderInfo)
  private static final xddq.pb.GroupPurchaseApplyLeaderInfo DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.GroupPurchaseApplyLeaderInfo();
  }

  public static xddq.pb.GroupPurchaseApplyLeaderInfo getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<GroupPurchaseApplyLeaderInfo>
      PARSER = new com.google.protobuf.AbstractParser<GroupPurchaseApplyLeaderInfo>() {
    @java.lang.Override
    public GroupPurchaseApplyLeaderInfo parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<GroupPurchaseApplyLeaderInfo> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<GroupPurchaseApplyLeaderInfo> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.GroupPurchaseApplyLeaderInfo getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

