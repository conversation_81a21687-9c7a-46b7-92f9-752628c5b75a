// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.DragonHomeMonsterLogMsg}
 */
public final class DragonHomeMonsterLogMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.DragonHomeMonsterLogMsg)
    DragonHomeMonsterLogMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      DragonHomeMonsterLogMsg.class.getName());
  }
  // Use DragonHomeMonsterLogMsg.newBuilder() to construct.
  private DragonHomeMonsterLogMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private DragonHomeMonsterLogMsg() {
    assistName_ = "";
    reward_ = "";
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_DragonHomeMonsterLogMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_DragonHomeMonsterLogMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.DragonHomeMonsterLogMsg.class, xddq.pb.DragonHomeMonsterLogMsg.Builder.class);
  }

  private int bitField0_;
  public static final int MONSTERUID_FIELD_NUMBER = 1;
  private long monsterUid_ = 0L;
  /**
   * <code>optional int64 monsterUid = 1;</code>
   * @return Whether the monsterUid field is set.
   */
  @java.lang.Override
  public boolean hasMonsterUid() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int64 monsterUid = 1;</code>
   * @return The monsterUid.
   */
  @java.lang.Override
  public long getMonsterUid() {
    return monsterUid_;
  }

  public static final int MONSTERID_FIELD_NUMBER = 2;
  private long monsterId_ = 0L;
  /**
   * <code>optional int64 monsterId = 2;</code>
   * @return Whether the monsterId field is set.
   */
  @java.lang.Override
  public boolean hasMonsterId() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int64 monsterId = 2;</code>
   * @return The monsterId.
   */
  @java.lang.Override
  public long getMonsterId() {
    return monsterId_;
  }

  public static final int ASSISTNAME_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private volatile java.lang.Object assistName_ = "";
  /**
   * <code>optional string assistName = 3;</code>
   * @return Whether the assistName field is set.
   */
  @java.lang.Override
  public boolean hasAssistName() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional string assistName = 3;</code>
   * @return The assistName.
   */
  @java.lang.Override
  public java.lang.String getAssistName() {
    java.lang.Object ref = assistName_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        assistName_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string assistName = 3;</code>
   * @return The bytes for assistName.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getAssistNameBytes() {
    java.lang.Object ref = assistName_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      assistName_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int STATE_FIELD_NUMBER = 4;
  private int state_ = 0;
  /**
   * <code>optional int32 state = 4;</code>
   * @return Whether the state field is set.
   */
  @java.lang.Override
  public boolean hasState() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional int32 state = 4;</code>
   * @return The state.
   */
  @java.lang.Override
  public int getState() {
    return state_;
  }

  public static final int REWARD_FIELD_NUMBER = 5;
  @SuppressWarnings("serial")
  private volatile java.lang.Object reward_ = "";
  /**
   * <code>optional string reward = 5;</code>
   * @return Whether the reward field is set.
   */
  @java.lang.Override
  public boolean hasReward() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>optional string reward = 5;</code>
   * @return The reward.
   */
  @java.lang.Override
  public java.lang.String getReward() {
    java.lang.Object ref = reward_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        reward_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string reward = 5;</code>
   * @return The bytes for reward.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getRewardBytes() {
    java.lang.Object ref = reward_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      reward_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt64(1, monsterUid_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt64(2, monsterId_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 3, assistName_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt32(4, state_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 5, reward_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(1, monsterUid_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(2, monsterId_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(3, assistName_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(4, state_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(5, reward_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.DragonHomeMonsterLogMsg)) {
      return super.equals(obj);
    }
    xddq.pb.DragonHomeMonsterLogMsg other = (xddq.pb.DragonHomeMonsterLogMsg) obj;

    if (hasMonsterUid() != other.hasMonsterUid()) return false;
    if (hasMonsterUid()) {
      if (getMonsterUid()
          != other.getMonsterUid()) return false;
    }
    if (hasMonsterId() != other.hasMonsterId()) return false;
    if (hasMonsterId()) {
      if (getMonsterId()
          != other.getMonsterId()) return false;
    }
    if (hasAssistName() != other.hasAssistName()) return false;
    if (hasAssistName()) {
      if (!getAssistName()
          .equals(other.getAssistName())) return false;
    }
    if (hasState() != other.hasState()) return false;
    if (hasState()) {
      if (getState()
          != other.getState()) return false;
    }
    if (hasReward() != other.hasReward()) return false;
    if (hasReward()) {
      if (!getReward()
          .equals(other.getReward())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasMonsterUid()) {
      hash = (37 * hash) + MONSTERUID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getMonsterUid());
    }
    if (hasMonsterId()) {
      hash = (37 * hash) + MONSTERID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getMonsterId());
    }
    if (hasAssistName()) {
      hash = (37 * hash) + ASSISTNAME_FIELD_NUMBER;
      hash = (53 * hash) + getAssistName().hashCode();
    }
    if (hasState()) {
      hash = (37 * hash) + STATE_FIELD_NUMBER;
      hash = (53 * hash) + getState();
    }
    if (hasReward()) {
      hash = (37 * hash) + REWARD_FIELD_NUMBER;
      hash = (53 * hash) + getReward().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.DragonHomeMonsterLogMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.DragonHomeMonsterLogMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.DragonHomeMonsterLogMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.DragonHomeMonsterLogMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.DragonHomeMonsterLogMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.DragonHomeMonsterLogMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.DragonHomeMonsterLogMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.DragonHomeMonsterLogMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.DragonHomeMonsterLogMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.DragonHomeMonsterLogMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.DragonHomeMonsterLogMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.DragonHomeMonsterLogMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.DragonHomeMonsterLogMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.DragonHomeMonsterLogMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.DragonHomeMonsterLogMsg)
      xddq.pb.DragonHomeMonsterLogMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_DragonHomeMonsterLogMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_DragonHomeMonsterLogMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.DragonHomeMonsterLogMsg.class, xddq.pb.DragonHomeMonsterLogMsg.Builder.class);
    }

    // Construct using xddq.pb.DragonHomeMonsterLogMsg.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      monsterUid_ = 0L;
      monsterId_ = 0L;
      assistName_ = "";
      state_ = 0;
      reward_ = "";
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_DragonHomeMonsterLogMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.DragonHomeMonsterLogMsg getDefaultInstanceForType() {
      return xddq.pb.DragonHomeMonsterLogMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.DragonHomeMonsterLogMsg build() {
      xddq.pb.DragonHomeMonsterLogMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.DragonHomeMonsterLogMsg buildPartial() {
      xddq.pb.DragonHomeMonsterLogMsg result = new xddq.pb.DragonHomeMonsterLogMsg(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.DragonHomeMonsterLogMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.monsterUid_ = monsterUid_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.monsterId_ = monsterId_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.assistName_ = assistName_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.state_ = state_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.reward_ = reward_;
        to_bitField0_ |= 0x00000010;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.DragonHomeMonsterLogMsg) {
        return mergeFrom((xddq.pb.DragonHomeMonsterLogMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.DragonHomeMonsterLogMsg other) {
      if (other == xddq.pb.DragonHomeMonsterLogMsg.getDefaultInstance()) return this;
      if (other.hasMonsterUid()) {
        setMonsterUid(other.getMonsterUid());
      }
      if (other.hasMonsterId()) {
        setMonsterId(other.getMonsterId());
      }
      if (other.hasAssistName()) {
        assistName_ = other.assistName_;
        bitField0_ |= 0x00000004;
        onChanged();
      }
      if (other.hasState()) {
        setState(other.getState());
      }
      if (other.hasReward()) {
        reward_ = other.reward_;
        bitField0_ |= 0x00000010;
        onChanged();
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              monsterUid_ = input.readInt64();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              monsterId_ = input.readInt64();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 26: {
              assistName_ = input.readBytes();
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 32: {
              state_ = input.readInt32();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            case 42: {
              reward_ = input.readBytes();
              bitField0_ |= 0x00000010;
              break;
            } // case 42
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private long monsterUid_ ;
    /**
     * <code>optional int64 monsterUid = 1;</code>
     * @return Whether the monsterUid field is set.
     */
    @java.lang.Override
    public boolean hasMonsterUid() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 monsterUid = 1;</code>
     * @return The monsterUid.
     */
    @java.lang.Override
    public long getMonsterUid() {
      return monsterUid_;
    }
    /**
     * <code>optional int64 monsterUid = 1;</code>
     * @param value The monsterUid to set.
     * @return This builder for chaining.
     */
    public Builder setMonsterUid(long value) {

      monsterUid_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 monsterUid = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearMonsterUid() {
      bitField0_ = (bitField0_ & ~0x00000001);
      monsterUid_ = 0L;
      onChanged();
      return this;
    }

    private long monsterId_ ;
    /**
     * <code>optional int64 monsterId = 2;</code>
     * @return Whether the monsterId field is set.
     */
    @java.lang.Override
    public boolean hasMonsterId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int64 monsterId = 2;</code>
     * @return The monsterId.
     */
    @java.lang.Override
    public long getMonsterId() {
      return monsterId_;
    }
    /**
     * <code>optional int64 monsterId = 2;</code>
     * @param value The monsterId to set.
     * @return This builder for chaining.
     */
    public Builder setMonsterId(long value) {

      monsterId_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 monsterId = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearMonsterId() {
      bitField0_ = (bitField0_ & ~0x00000002);
      monsterId_ = 0L;
      onChanged();
      return this;
    }

    private java.lang.Object assistName_ = "";
    /**
     * <code>optional string assistName = 3;</code>
     * @return Whether the assistName field is set.
     */
    public boolean hasAssistName() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional string assistName = 3;</code>
     * @return The assistName.
     */
    public java.lang.String getAssistName() {
      java.lang.Object ref = assistName_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          assistName_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string assistName = 3;</code>
     * @return The bytes for assistName.
     */
    public com.google.protobuf.ByteString
        getAssistNameBytes() {
      java.lang.Object ref = assistName_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        assistName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string assistName = 3;</code>
     * @param value The assistName to set.
     * @return This builder for chaining.
     */
    public Builder setAssistName(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      assistName_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional string assistName = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearAssistName() {
      assistName_ = getDefaultInstance().getAssistName();
      bitField0_ = (bitField0_ & ~0x00000004);
      onChanged();
      return this;
    }
    /**
     * <code>optional string assistName = 3;</code>
     * @param value The bytes for assistName to set.
     * @return This builder for chaining.
     */
    public Builder setAssistNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      assistName_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }

    private int state_ ;
    /**
     * <code>optional int32 state = 4;</code>
     * @return Whether the state field is set.
     */
    @java.lang.Override
    public boolean hasState() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int32 state = 4;</code>
     * @return The state.
     */
    @java.lang.Override
    public int getState() {
      return state_;
    }
    /**
     * <code>optional int32 state = 4;</code>
     * @param value The state to set.
     * @return This builder for chaining.
     */
    public Builder setState(int value) {

      state_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 state = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearState() {
      bitField0_ = (bitField0_ & ~0x00000008);
      state_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object reward_ = "";
    /**
     * <code>optional string reward = 5;</code>
     * @return Whether the reward field is set.
     */
    public boolean hasReward() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional string reward = 5;</code>
     * @return The reward.
     */
    public java.lang.String getReward() {
      java.lang.Object ref = reward_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          reward_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string reward = 5;</code>
     * @return The bytes for reward.
     */
    public com.google.protobuf.ByteString
        getRewardBytes() {
      java.lang.Object ref = reward_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        reward_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string reward = 5;</code>
     * @param value The reward to set.
     * @return This builder for chaining.
     */
    public Builder setReward(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      reward_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional string reward = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearReward() {
      reward_ = getDefaultInstance().getReward();
      bitField0_ = (bitField0_ & ~0x00000010);
      onChanged();
      return this;
    }
    /**
     * <code>optional string reward = 5;</code>
     * @param value The bytes for reward to set.
     * @return This builder for chaining.
     */
    public Builder setRewardBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      reward_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.DragonHomeMonsterLogMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.DragonHomeMonsterLogMsg)
  private static final xddq.pb.DragonHomeMonsterLogMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.DragonHomeMonsterLogMsg();
  }

  public static xddq.pb.DragonHomeMonsterLogMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<DragonHomeMonsterLogMsg>
      PARSER = new com.google.protobuf.AbstractParser<DragonHomeMonsterLogMsg>() {
    @java.lang.Override
    public DragonHomeMonsterLogMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<DragonHomeMonsterLogMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<DragonHomeMonsterLogMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.DragonHomeMonsterLogMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

