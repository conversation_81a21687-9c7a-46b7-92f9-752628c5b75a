// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.WeYoundReportDetailMsg}
 */
public final class WeYoundReportDetailMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.WeYoundReportDetailMsg)
    WeYoundReportDetailMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      WeYoundReportDetailMsg.class.getName());
  }
  // Use WeYoundReportDetailMsg.newBuilder() to construct.
  private WeYoundReportDetailMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private WeYoundReportDetailMsg() {
    nickName_ = "";
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_WeYoundReportDetailMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_WeYoundReportDetailMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.WeYoundReportDetailMsg.class, xddq.pb.WeYoundReportDetailMsg.Builder.class);
  }

  private int bitField0_;
  public static final int NICKNAME_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private volatile java.lang.Object nickName_ = "";
  /**
   * <code>optional string nickName = 1;</code>
   * @return Whether the nickName field is set.
   */
  @java.lang.Override
  public boolean hasNickName() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional string nickName = 1;</code>
   * @return The nickName.
   */
  @java.lang.Override
  public java.lang.String getNickName() {
    java.lang.Object ref = nickName_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        nickName_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string nickName = 1;</code>
   * @return The bytes for nickName.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getNickNameBytes() {
    java.lang.Object ref = nickName_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      nickName_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ATKTIMES_FIELD_NUMBER = 2;
  private int atkTimes_ = 0;
  /**
   * <code>optional int32 atkTimes = 2;</code>
   * @return Whether the atkTimes field is set.
   */
  @java.lang.Override
  public boolean hasAtkTimes() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int32 atkTimes = 2;</code>
   * @return The atkTimes.
   */
  @java.lang.Override
  public int getAtkTimes() {
    return atkTimes_;
  }

  public static final int DAMAGE_FIELD_NUMBER = 3;
  private long damage_ = 0L;
  /**
   * <code>optional int64 damage = 3;</code>
   * @return Whether the damage field is set.
   */
  @java.lang.Override
  public boolean hasDamage() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional int64 damage = 3;</code>
   * @return The damage.
   */
  @java.lang.Override
  public long getDamage() {
    return damage_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 1, nickName_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(2, atkTimes_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt64(3, damage_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(1, nickName_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, atkTimes_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(3, damage_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.WeYoundReportDetailMsg)) {
      return super.equals(obj);
    }
    xddq.pb.WeYoundReportDetailMsg other = (xddq.pb.WeYoundReportDetailMsg) obj;

    if (hasNickName() != other.hasNickName()) return false;
    if (hasNickName()) {
      if (!getNickName()
          .equals(other.getNickName())) return false;
    }
    if (hasAtkTimes() != other.hasAtkTimes()) return false;
    if (hasAtkTimes()) {
      if (getAtkTimes()
          != other.getAtkTimes()) return false;
    }
    if (hasDamage() != other.hasDamage()) return false;
    if (hasDamage()) {
      if (getDamage()
          != other.getDamage()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasNickName()) {
      hash = (37 * hash) + NICKNAME_FIELD_NUMBER;
      hash = (53 * hash) + getNickName().hashCode();
    }
    if (hasAtkTimes()) {
      hash = (37 * hash) + ATKTIMES_FIELD_NUMBER;
      hash = (53 * hash) + getAtkTimes();
    }
    if (hasDamage()) {
      hash = (37 * hash) + DAMAGE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getDamage());
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.WeYoundReportDetailMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.WeYoundReportDetailMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.WeYoundReportDetailMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.WeYoundReportDetailMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.WeYoundReportDetailMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.WeYoundReportDetailMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.WeYoundReportDetailMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.WeYoundReportDetailMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.WeYoundReportDetailMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.WeYoundReportDetailMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.WeYoundReportDetailMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.WeYoundReportDetailMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.WeYoundReportDetailMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.WeYoundReportDetailMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.WeYoundReportDetailMsg)
      xddq.pb.WeYoundReportDetailMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_WeYoundReportDetailMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_WeYoundReportDetailMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.WeYoundReportDetailMsg.class, xddq.pb.WeYoundReportDetailMsg.Builder.class);
    }

    // Construct using xddq.pb.WeYoundReportDetailMsg.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      nickName_ = "";
      atkTimes_ = 0;
      damage_ = 0L;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_WeYoundReportDetailMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.WeYoundReportDetailMsg getDefaultInstanceForType() {
      return xddq.pb.WeYoundReportDetailMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.WeYoundReportDetailMsg build() {
      xddq.pb.WeYoundReportDetailMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.WeYoundReportDetailMsg buildPartial() {
      xddq.pb.WeYoundReportDetailMsg result = new xddq.pb.WeYoundReportDetailMsg(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.WeYoundReportDetailMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.nickName_ = nickName_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.atkTimes_ = atkTimes_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.damage_ = damage_;
        to_bitField0_ |= 0x00000004;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.WeYoundReportDetailMsg) {
        return mergeFrom((xddq.pb.WeYoundReportDetailMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.WeYoundReportDetailMsg other) {
      if (other == xddq.pb.WeYoundReportDetailMsg.getDefaultInstance()) return this;
      if (other.hasNickName()) {
        nickName_ = other.nickName_;
        bitField0_ |= 0x00000001;
        onChanged();
      }
      if (other.hasAtkTimes()) {
        setAtkTimes(other.getAtkTimes());
      }
      if (other.hasDamage()) {
        setDamage(other.getDamage());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              nickName_ = input.readBytes();
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 16: {
              atkTimes_ = input.readInt32();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 24: {
              damage_ = input.readInt64();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.lang.Object nickName_ = "";
    /**
     * <code>optional string nickName = 1;</code>
     * @return Whether the nickName field is set.
     */
    public boolean hasNickName() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional string nickName = 1;</code>
     * @return The nickName.
     */
    public java.lang.String getNickName() {
      java.lang.Object ref = nickName_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          nickName_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string nickName = 1;</code>
     * @return The bytes for nickName.
     */
    public com.google.protobuf.ByteString
        getNickNameBytes() {
      java.lang.Object ref = nickName_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        nickName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string nickName = 1;</code>
     * @param value The nickName to set.
     * @return This builder for chaining.
     */
    public Builder setNickName(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      nickName_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional string nickName = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearNickName() {
      nickName_ = getDefaultInstance().getNickName();
      bitField0_ = (bitField0_ & ~0x00000001);
      onChanged();
      return this;
    }
    /**
     * <code>optional string nickName = 1;</code>
     * @param value The bytes for nickName to set.
     * @return This builder for chaining.
     */
    public Builder setNickNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      nickName_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }

    private int atkTimes_ ;
    /**
     * <code>optional int32 atkTimes = 2;</code>
     * @return Whether the atkTimes field is set.
     */
    @java.lang.Override
    public boolean hasAtkTimes() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 atkTimes = 2;</code>
     * @return The atkTimes.
     */
    @java.lang.Override
    public int getAtkTimes() {
      return atkTimes_;
    }
    /**
     * <code>optional int32 atkTimes = 2;</code>
     * @param value The atkTimes to set.
     * @return This builder for chaining.
     */
    public Builder setAtkTimes(int value) {

      atkTimes_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 atkTimes = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearAtkTimes() {
      bitField0_ = (bitField0_ & ~0x00000002);
      atkTimes_ = 0;
      onChanged();
      return this;
    }

    private long damage_ ;
    /**
     * <code>optional int64 damage = 3;</code>
     * @return Whether the damage field is set.
     */
    @java.lang.Override
    public boolean hasDamage() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int64 damage = 3;</code>
     * @return The damage.
     */
    @java.lang.Override
    public long getDamage() {
      return damage_;
    }
    /**
     * <code>optional int64 damage = 3;</code>
     * @param value The damage to set.
     * @return This builder for chaining.
     */
    public Builder setDamage(long value) {

      damage_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 damage = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearDamage() {
      bitField0_ = (bitField0_ & ~0x00000004);
      damage_ = 0L;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.WeYoundReportDetailMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.WeYoundReportDetailMsg)
  private static final xddq.pb.WeYoundReportDetailMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.WeYoundReportDetailMsg();
  }

  public static xddq.pb.WeYoundReportDetailMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<WeYoundReportDetailMsg>
      PARSER = new com.google.protobuf.AbstractParser<WeYoundReportDetailMsg>() {
    @java.lang.Override
    public WeYoundReportDetailMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<WeYoundReportDetailMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<WeYoundReportDetailMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.WeYoundReportDetailMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

