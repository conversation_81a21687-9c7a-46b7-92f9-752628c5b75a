// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.PetKernelDrawResp}
 */
public final class PetKernelDrawResp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.PetKernelDrawResp)
    PetKernelDrawRespOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      PetKernelDrawResp.class.getName());
  }
  // Use PetKernelDrawResp.newBuilder() to construct.
  private PetKernelDrawResp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private PetKernelDrawResp() {
    drawResult_ = java.util.Collections.emptyList();
    syncKernelMsg_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_PetKernelDrawResp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_PetKernelDrawResp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.PetKernelDrawResp.class, xddq.pb.PetKernelDrawResp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int DRAWRESULT_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.PetKernelDrawResult> drawResult_;
  /**
   * <code>repeated .xddq.pb.PetKernelDrawResult drawResult = 2;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.PetKernelDrawResult> getDrawResultList() {
    return drawResult_;
  }
  /**
   * <code>repeated .xddq.pb.PetKernelDrawResult drawResult = 2;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.PetKernelDrawResultOrBuilder> 
      getDrawResultOrBuilderList() {
    return drawResult_;
  }
  /**
   * <code>repeated .xddq.pb.PetKernelDrawResult drawResult = 2;</code>
   */
  @java.lang.Override
  public int getDrawResultCount() {
    return drawResult_.size();
  }
  /**
   * <code>repeated .xddq.pb.PetKernelDrawResult drawResult = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.PetKernelDrawResult getDrawResult(int index) {
    return drawResult_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.PetKernelDrawResult drawResult = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.PetKernelDrawResultOrBuilder getDrawResultOrBuilder(
      int index) {
    return drawResult_.get(index);
  }

  public static final int SYNCKERNELMSG_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.PetKernelDataMsg> syncKernelMsg_;
  /**
   * <code>repeated .xddq.pb.PetKernelDataMsg syncKernelMsg = 3;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.PetKernelDataMsg> getSyncKernelMsgList() {
    return syncKernelMsg_;
  }
  /**
   * <code>repeated .xddq.pb.PetKernelDataMsg syncKernelMsg = 3;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.PetKernelDataMsgOrBuilder> 
      getSyncKernelMsgOrBuilderList() {
    return syncKernelMsg_;
  }
  /**
   * <code>repeated .xddq.pb.PetKernelDataMsg syncKernelMsg = 3;</code>
   */
  @java.lang.Override
  public int getSyncKernelMsgCount() {
    return syncKernelMsg_.size();
  }
  /**
   * <code>repeated .xddq.pb.PetKernelDataMsg syncKernelMsg = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.PetKernelDataMsg getSyncKernelMsg(int index) {
    return syncKernelMsg_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.PetKernelDataMsg syncKernelMsg = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.PetKernelDataMsgOrBuilder getSyncKernelMsgOrBuilder(
      int index) {
    return syncKernelMsg_.get(index);
  }

  public static final int FREEDRAWTIMES_FIELD_NUMBER = 4;
  private int freeDrawTimes_ = 0;
  /**
   * <code>optional int32 freeDrawTimes = 4;</code>
   * @return Whether the freeDrawTimes field is set.
   */
  @java.lang.Override
  public boolean hasFreeDrawTimes() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int32 freeDrawTimes = 4;</code>
   * @return The freeDrawTimes.
   */
  @java.lang.Override
  public int getFreeDrawTimes() {
    return freeDrawTimes_;
  }

  public static final int DRAWCOUNT_FIELD_NUMBER = 5;
  private int drawCount_ = 0;
  /**
   * <code>optional int32 drawCount = 5;</code>
   * @return Whether the drawCount field is set.
   */
  @java.lang.Override
  public boolean hasDrawCount() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional int32 drawCount = 5;</code>
   * @return The drawCount.
   */
  @java.lang.Override
  public int getDrawCount() {
    return drawCount_;
  }

  public static final int ENSURECOUNT_FIELD_NUMBER = 6;
  private int ensureCount_ = 0;
  /**
   * <code>optional int32 ensureCount = 6;</code>
   * @return Whether the ensureCount field is set.
   */
  @java.lang.Override
  public boolean hasEnsureCount() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional int32 ensureCount = 6;</code>
   * @return The ensureCount.
   */
  @java.lang.Override
  public int getEnsureCount() {
    return ensureCount_;
  }

  public static final int PIECESHOPOPEN_FIELD_NUMBER = 7;
  private boolean pieceShopOpen_ = false;
  /**
   * <code>optional bool pieceShopOpen = 7;</code>
   * @return Whether the pieceShopOpen field is set.
   */
  @java.lang.Override
  public boolean hasPieceShopOpen() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>optional bool pieceShopOpen = 7;</code>
   * @return The pieceShopOpen.
   */
  @java.lang.Override
  public boolean getPieceShopOpen() {
    return pieceShopOpen_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    for (int i = 0; i < getSyncKernelMsgCount(); i++) {
      if (!getSyncKernelMsg(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    for (int i = 0; i < drawResult_.size(); i++) {
      output.writeMessage(2, drawResult_.get(i));
    }
    for (int i = 0; i < syncKernelMsg_.size(); i++) {
      output.writeMessage(3, syncKernelMsg_.get(i));
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(4, freeDrawTimes_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt32(5, drawCount_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt32(6, ensureCount_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      output.writeBool(7, pieceShopOpen_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    for (int i = 0; i < drawResult_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, drawResult_.get(i));
    }
    for (int i = 0; i < syncKernelMsg_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, syncKernelMsg_.get(i));
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(4, freeDrawTimes_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(5, drawCount_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(6, ensureCount_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(7, pieceShopOpen_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.PetKernelDrawResp)) {
      return super.equals(obj);
    }
    xddq.pb.PetKernelDrawResp other = (xddq.pb.PetKernelDrawResp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (!getDrawResultList()
        .equals(other.getDrawResultList())) return false;
    if (!getSyncKernelMsgList()
        .equals(other.getSyncKernelMsgList())) return false;
    if (hasFreeDrawTimes() != other.hasFreeDrawTimes()) return false;
    if (hasFreeDrawTimes()) {
      if (getFreeDrawTimes()
          != other.getFreeDrawTimes()) return false;
    }
    if (hasDrawCount() != other.hasDrawCount()) return false;
    if (hasDrawCount()) {
      if (getDrawCount()
          != other.getDrawCount()) return false;
    }
    if (hasEnsureCount() != other.hasEnsureCount()) return false;
    if (hasEnsureCount()) {
      if (getEnsureCount()
          != other.getEnsureCount()) return false;
    }
    if (hasPieceShopOpen() != other.hasPieceShopOpen()) return false;
    if (hasPieceShopOpen()) {
      if (getPieceShopOpen()
          != other.getPieceShopOpen()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (getDrawResultCount() > 0) {
      hash = (37 * hash) + DRAWRESULT_FIELD_NUMBER;
      hash = (53 * hash) + getDrawResultList().hashCode();
    }
    if (getSyncKernelMsgCount() > 0) {
      hash = (37 * hash) + SYNCKERNELMSG_FIELD_NUMBER;
      hash = (53 * hash) + getSyncKernelMsgList().hashCode();
    }
    if (hasFreeDrawTimes()) {
      hash = (37 * hash) + FREEDRAWTIMES_FIELD_NUMBER;
      hash = (53 * hash) + getFreeDrawTimes();
    }
    if (hasDrawCount()) {
      hash = (37 * hash) + DRAWCOUNT_FIELD_NUMBER;
      hash = (53 * hash) + getDrawCount();
    }
    if (hasEnsureCount()) {
      hash = (37 * hash) + ENSURECOUNT_FIELD_NUMBER;
      hash = (53 * hash) + getEnsureCount();
    }
    if (hasPieceShopOpen()) {
      hash = (37 * hash) + PIECESHOPOPEN_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getPieceShopOpen());
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.PetKernelDrawResp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PetKernelDrawResp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PetKernelDrawResp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PetKernelDrawResp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PetKernelDrawResp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PetKernelDrawResp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PetKernelDrawResp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.PetKernelDrawResp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.PetKernelDrawResp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.PetKernelDrawResp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.PetKernelDrawResp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.PetKernelDrawResp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.PetKernelDrawResp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.PetKernelDrawResp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.PetKernelDrawResp)
      xddq.pb.PetKernelDrawRespOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PetKernelDrawResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PetKernelDrawResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.PetKernelDrawResp.class, xddq.pb.PetKernelDrawResp.Builder.class);
    }

    // Construct using xddq.pb.PetKernelDrawResp.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      if (drawResultBuilder_ == null) {
        drawResult_ = java.util.Collections.emptyList();
      } else {
        drawResult_ = null;
        drawResultBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000002);
      if (syncKernelMsgBuilder_ == null) {
        syncKernelMsg_ = java.util.Collections.emptyList();
      } else {
        syncKernelMsg_ = null;
        syncKernelMsgBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000004);
      freeDrawTimes_ = 0;
      drawCount_ = 0;
      ensureCount_ = 0;
      pieceShopOpen_ = false;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PetKernelDrawResp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.PetKernelDrawResp getDefaultInstanceForType() {
      return xddq.pb.PetKernelDrawResp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.PetKernelDrawResp build() {
      xddq.pb.PetKernelDrawResp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.PetKernelDrawResp buildPartial() {
      xddq.pb.PetKernelDrawResp result = new xddq.pb.PetKernelDrawResp(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.PetKernelDrawResp result) {
      if (drawResultBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          drawResult_ = java.util.Collections.unmodifiableList(drawResult_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.drawResult_ = drawResult_;
      } else {
        result.drawResult_ = drawResultBuilder_.build();
      }
      if (syncKernelMsgBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0)) {
          syncKernelMsg_ = java.util.Collections.unmodifiableList(syncKernelMsg_);
          bitField0_ = (bitField0_ & ~0x00000004);
        }
        result.syncKernelMsg_ = syncKernelMsg_;
      } else {
        result.syncKernelMsg_ = syncKernelMsgBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.PetKernelDrawResp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.freeDrawTimes_ = freeDrawTimes_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.drawCount_ = drawCount_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.ensureCount_ = ensureCount_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.pieceShopOpen_ = pieceShopOpen_;
        to_bitField0_ |= 0x00000010;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.PetKernelDrawResp) {
        return mergeFrom((xddq.pb.PetKernelDrawResp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.PetKernelDrawResp other) {
      if (other == xddq.pb.PetKernelDrawResp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (drawResultBuilder_ == null) {
        if (!other.drawResult_.isEmpty()) {
          if (drawResult_.isEmpty()) {
            drawResult_ = other.drawResult_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureDrawResultIsMutable();
            drawResult_.addAll(other.drawResult_);
          }
          onChanged();
        }
      } else {
        if (!other.drawResult_.isEmpty()) {
          if (drawResultBuilder_.isEmpty()) {
            drawResultBuilder_.dispose();
            drawResultBuilder_ = null;
            drawResult_ = other.drawResult_;
            bitField0_ = (bitField0_ & ~0x00000002);
            drawResultBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetDrawResultFieldBuilder() : null;
          } else {
            drawResultBuilder_.addAllMessages(other.drawResult_);
          }
        }
      }
      if (syncKernelMsgBuilder_ == null) {
        if (!other.syncKernelMsg_.isEmpty()) {
          if (syncKernelMsg_.isEmpty()) {
            syncKernelMsg_ = other.syncKernelMsg_;
            bitField0_ = (bitField0_ & ~0x00000004);
          } else {
            ensureSyncKernelMsgIsMutable();
            syncKernelMsg_.addAll(other.syncKernelMsg_);
          }
          onChanged();
        }
      } else {
        if (!other.syncKernelMsg_.isEmpty()) {
          if (syncKernelMsgBuilder_.isEmpty()) {
            syncKernelMsgBuilder_.dispose();
            syncKernelMsgBuilder_ = null;
            syncKernelMsg_ = other.syncKernelMsg_;
            bitField0_ = (bitField0_ & ~0x00000004);
            syncKernelMsgBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetSyncKernelMsgFieldBuilder() : null;
          } else {
            syncKernelMsgBuilder_.addAllMessages(other.syncKernelMsg_);
          }
        }
      }
      if (other.hasFreeDrawTimes()) {
        setFreeDrawTimes(other.getFreeDrawTimes());
      }
      if (other.hasDrawCount()) {
        setDrawCount(other.getDrawCount());
      }
      if (other.hasEnsureCount()) {
        setEnsureCount(other.getEnsureCount());
      }
      if (other.hasPieceShopOpen()) {
        setPieceShopOpen(other.getPieceShopOpen());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      for (int i = 0; i < getSyncKernelMsgCount(); i++) {
        if (!getSyncKernelMsg(i).isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              xddq.pb.PetKernelDrawResult m =
                  input.readMessage(
                      xddq.pb.PetKernelDrawResult.parser(),
                      extensionRegistry);
              if (drawResultBuilder_ == null) {
                ensureDrawResultIsMutable();
                drawResult_.add(m);
              } else {
                drawResultBuilder_.addMessage(m);
              }
              break;
            } // case 18
            case 26: {
              xddq.pb.PetKernelDataMsg m =
                  input.readMessage(
                      xddq.pb.PetKernelDataMsg.parser(),
                      extensionRegistry);
              if (syncKernelMsgBuilder_ == null) {
                ensureSyncKernelMsgIsMutable();
                syncKernelMsg_.add(m);
              } else {
                syncKernelMsgBuilder_.addMessage(m);
              }
              break;
            } // case 26
            case 32: {
              freeDrawTimes_ = input.readInt32();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            case 40: {
              drawCount_ = input.readInt32();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            case 48: {
              ensureCount_ = input.readInt32();
              bitField0_ |= 0x00000020;
              break;
            } // case 48
            case 56: {
              pieceShopOpen_ = input.readBool();
              bitField0_ |= 0x00000040;
              break;
            } // case 56
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.PetKernelDrawResult> drawResult_ =
      java.util.Collections.emptyList();
    private void ensureDrawResultIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        drawResult_ = new java.util.ArrayList<xddq.pb.PetKernelDrawResult>(drawResult_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.PetKernelDrawResult, xddq.pb.PetKernelDrawResult.Builder, xddq.pb.PetKernelDrawResultOrBuilder> drawResultBuilder_;

    /**
     * <code>repeated .xddq.pb.PetKernelDrawResult drawResult = 2;</code>
     */
    public java.util.List<xddq.pb.PetKernelDrawResult> getDrawResultList() {
      if (drawResultBuilder_ == null) {
        return java.util.Collections.unmodifiableList(drawResult_);
      } else {
        return drawResultBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.PetKernelDrawResult drawResult = 2;</code>
     */
    public int getDrawResultCount() {
      if (drawResultBuilder_ == null) {
        return drawResult_.size();
      } else {
        return drawResultBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.PetKernelDrawResult drawResult = 2;</code>
     */
    public xddq.pb.PetKernelDrawResult getDrawResult(int index) {
      if (drawResultBuilder_ == null) {
        return drawResult_.get(index);
      } else {
        return drawResultBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.PetKernelDrawResult drawResult = 2;</code>
     */
    public Builder setDrawResult(
        int index, xddq.pb.PetKernelDrawResult value) {
      if (drawResultBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDrawResultIsMutable();
        drawResult_.set(index, value);
        onChanged();
      } else {
        drawResultBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PetKernelDrawResult drawResult = 2;</code>
     */
    public Builder setDrawResult(
        int index, xddq.pb.PetKernelDrawResult.Builder builderForValue) {
      if (drawResultBuilder_ == null) {
        ensureDrawResultIsMutable();
        drawResult_.set(index, builderForValue.build());
        onChanged();
      } else {
        drawResultBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PetKernelDrawResult drawResult = 2;</code>
     */
    public Builder addDrawResult(xddq.pb.PetKernelDrawResult value) {
      if (drawResultBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDrawResultIsMutable();
        drawResult_.add(value);
        onChanged();
      } else {
        drawResultBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PetKernelDrawResult drawResult = 2;</code>
     */
    public Builder addDrawResult(
        int index, xddq.pb.PetKernelDrawResult value) {
      if (drawResultBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDrawResultIsMutable();
        drawResult_.add(index, value);
        onChanged();
      } else {
        drawResultBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PetKernelDrawResult drawResult = 2;</code>
     */
    public Builder addDrawResult(
        xddq.pb.PetKernelDrawResult.Builder builderForValue) {
      if (drawResultBuilder_ == null) {
        ensureDrawResultIsMutable();
        drawResult_.add(builderForValue.build());
        onChanged();
      } else {
        drawResultBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PetKernelDrawResult drawResult = 2;</code>
     */
    public Builder addDrawResult(
        int index, xddq.pb.PetKernelDrawResult.Builder builderForValue) {
      if (drawResultBuilder_ == null) {
        ensureDrawResultIsMutable();
        drawResult_.add(index, builderForValue.build());
        onChanged();
      } else {
        drawResultBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PetKernelDrawResult drawResult = 2;</code>
     */
    public Builder addAllDrawResult(
        java.lang.Iterable<? extends xddq.pb.PetKernelDrawResult> values) {
      if (drawResultBuilder_ == null) {
        ensureDrawResultIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, drawResult_);
        onChanged();
      } else {
        drawResultBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PetKernelDrawResult drawResult = 2;</code>
     */
    public Builder clearDrawResult() {
      if (drawResultBuilder_ == null) {
        drawResult_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        drawResultBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PetKernelDrawResult drawResult = 2;</code>
     */
    public Builder removeDrawResult(int index) {
      if (drawResultBuilder_ == null) {
        ensureDrawResultIsMutable();
        drawResult_.remove(index);
        onChanged();
      } else {
        drawResultBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PetKernelDrawResult drawResult = 2;</code>
     */
    public xddq.pb.PetKernelDrawResult.Builder getDrawResultBuilder(
        int index) {
      return internalGetDrawResultFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.PetKernelDrawResult drawResult = 2;</code>
     */
    public xddq.pb.PetKernelDrawResultOrBuilder getDrawResultOrBuilder(
        int index) {
      if (drawResultBuilder_ == null) {
        return drawResult_.get(index);  } else {
        return drawResultBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.PetKernelDrawResult drawResult = 2;</code>
     */
    public java.util.List<? extends xddq.pb.PetKernelDrawResultOrBuilder> 
         getDrawResultOrBuilderList() {
      if (drawResultBuilder_ != null) {
        return drawResultBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(drawResult_);
      }
    }
    /**
     * <code>repeated .xddq.pb.PetKernelDrawResult drawResult = 2;</code>
     */
    public xddq.pb.PetKernelDrawResult.Builder addDrawResultBuilder() {
      return internalGetDrawResultFieldBuilder().addBuilder(
          xddq.pb.PetKernelDrawResult.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.PetKernelDrawResult drawResult = 2;</code>
     */
    public xddq.pb.PetKernelDrawResult.Builder addDrawResultBuilder(
        int index) {
      return internalGetDrawResultFieldBuilder().addBuilder(
          index, xddq.pb.PetKernelDrawResult.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.PetKernelDrawResult drawResult = 2;</code>
     */
    public java.util.List<xddq.pb.PetKernelDrawResult.Builder> 
         getDrawResultBuilderList() {
      return internalGetDrawResultFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.PetKernelDrawResult, xddq.pb.PetKernelDrawResult.Builder, xddq.pb.PetKernelDrawResultOrBuilder> 
        internalGetDrawResultFieldBuilder() {
      if (drawResultBuilder_ == null) {
        drawResultBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.PetKernelDrawResult, xddq.pb.PetKernelDrawResult.Builder, xddq.pb.PetKernelDrawResultOrBuilder>(
                drawResult_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        drawResult_ = null;
      }
      return drawResultBuilder_;
    }

    private java.util.List<xddq.pb.PetKernelDataMsg> syncKernelMsg_ =
      java.util.Collections.emptyList();
    private void ensureSyncKernelMsgIsMutable() {
      if (!((bitField0_ & 0x00000004) != 0)) {
        syncKernelMsg_ = new java.util.ArrayList<xddq.pb.PetKernelDataMsg>(syncKernelMsg_);
        bitField0_ |= 0x00000004;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.PetKernelDataMsg, xddq.pb.PetKernelDataMsg.Builder, xddq.pb.PetKernelDataMsgOrBuilder> syncKernelMsgBuilder_;

    /**
     * <code>repeated .xddq.pb.PetKernelDataMsg syncKernelMsg = 3;</code>
     */
    public java.util.List<xddq.pb.PetKernelDataMsg> getSyncKernelMsgList() {
      if (syncKernelMsgBuilder_ == null) {
        return java.util.Collections.unmodifiableList(syncKernelMsg_);
      } else {
        return syncKernelMsgBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.PetKernelDataMsg syncKernelMsg = 3;</code>
     */
    public int getSyncKernelMsgCount() {
      if (syncKernelMsgBuilder_ == null) {
        return syncKernelMsg_.size();
      } else {
        return syncKernelMsgBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.PetKernelDataMsg syncKernelMsg = 3;</code>
     */
    public xddq.pb.PetKernelDataMsg getSyncKernelMsg(int index) {
      if (syncKernelMsgBuilder_ == null) {
        return syncKernelMsg_.get(index);
      } else {
        return syncKernelMsgBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.PetKernelDataMsg syncKernelMsg = 3;</code>
     */
    public Builder setSyncKernelMsg(
        int index, xddq.pb.PetKernelDataMsg value) {
      if (syncKernelMsgBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureSyncKernelMsgIsMutable();
        syncKernelMsg_.set(index, value);
        onChanged();
      } else {
        syncKernelMsgBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PetKernelDataMsg syncKernelMsg = 3;</code>
     */
    public Builder setSyncKernelMsg(
        int index, xddq.pb.PetKernelDataMsg.Builder builderForValue) {
      if (syncKernelMsgBuilder_ == null) {
        ensureSyncKernelMsgIsMutable();
        syncKernelMsg_.set(index, builderForValue.build());
        onChanged();
      } else {
        syncKernelMsgBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PetKernelDataMsg syncKernelMsg = 3;</code>
     */
    public Builder addSyncKernelMsg(xddq.pb.PetKernelDataMsg value) {
      if (syncKernelMsgBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureSyncKernelMsgIsMutable();
        syncKernelMsg_.add(value);
        onChanged();
      } else {
        syncKernelMsgBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PetKernelDataMsg syncKernelMsg = 3;</code>
     */
    public Builder addSyncKernelMsg(
        int index, xddq.pb.PetKernelDataMsg value) {
      if (syncKernelMsgBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureSyncKernelMsgIsMutable();
        syncKernelMsg_.add(index, value);
        onChanged();
      } else {
        syncKernelMsgBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PetKernelDataMsg syncKernelMsg = 3;</code>
     */
    public Builder addSyncKernelMsg(
        xddq.pb.PetKernelDataMsg.Builder builderForValue) {
      if (syncKernelMsgBuilder_ == null) {
        ensureSyncKernelMsgIsMutable();
        syncKernelMsg_.add(builderForValue.build());
        onChanged();
      } else {
        syncKernelMsgBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PetKernelDataMsg syncKernelMsg = 3;</code>
     */
    public Builder addSyncKernelMsg(
        int index, xddq.pb.PetKernelDataMsg.Builder builderForValue) {
      if (syncKernelMsgBuilder_ == null) {
        ensureSyncKernelMsgIsMutable();
        syncKernelMsg_.add(index, builderForValue.build());
        onChanged();
      } else {
        syncKernelMsgBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PetKernelDataMsg syncKernelMsg = 3;</code>
     */
    public Builder addAllSyncKernelMsg(
        java.lang.Iterable<? extends xddq.pb.PetKernelDataMsg> values) {
      if (syncKernelMsgBuilder_ == null) {
        ensureSyncKernelMsgIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, syncKernelMsg_);
        onChanged();
      } else {
        syncKernelMsgBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PetKernelDataMsg syncKernelMsg = 3;</code>
     */
    public Builder clearSyncKernelMsg() {
      if (syncKernelMsgBuilder_ == null) {
        syncKernelMsg_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
      } else {
        syncKernelMsgBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PetKernelDataMsg syncKernelMsg = 3;</code>
     */
    public Builder removeSyncKernelMsg(int index) {
      if (syncKernelMsgBuilder_ == null) {
        ensureSyncKernelMsgIsMutable();
        syncKernelMsg_.remove(index);
        onChanged();
      } else {
        syncKernelMsgBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PetKernelDataMsg syncKernelMsg = 3;</code>
     */
    public xddq.pb.PetKernelDataMsg.Builder getSyncKernelMsgBuilder(
        int index) {
      return internalGetSyncKernelMsgFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.PetKernelDataMsg syncKernelMsg = 3;</code>
     */
    public xddq.pb.PetKernelDataMsgOrBuilder getSyncKernelMsgOrBuilder(
        int index) {
      if (syncKernelMsgBuilder_ == null) {
        return syncKernelMsg_.get(index);  } else {
        return syncKernelMsgBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.PetKernelDataMsg syncKernelMsg = 3;</code>
     */
    public java.util.List<? extends xddq.pb.PetKernelDataMsgOrBuilder> 
         getSyncKernelMsgOrBuilderList() {
      if (syncKernelMsgBuilder_ != null) {
        return syncKernelMsgBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(syncKernelMsg_);
      }
    }
    /**
     * <code>repeated .xddq.pb.PetKernelDataMsg syncKernelMsg = 3;</code>
     */
    public xddq.pb.PetKernelDataMsg.Builder addSyncKernelMsgBuilder() {
      return internalGetSyncKernelMsgFieldBuilder().addBuilder(
          xddq.pb.PetKernelDataMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.PetKernelDataMsg syncKernelMsg = 3;</code>
     */
    public xddq.pb.PetKernelDataMsg.Builder addSyncKernelMsgBuilder(
        int index) {
      return internalGetSyncKernelMsgFieldBuilder().addBuilder(
          index, xddq.pb.PetKernelDataMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.PetKernelDataMsg syncKernelMsg = 3;</code>
     */
    public java.util.List<xddq.pb.PetKernelDataMsg.Builder> 
         getSyncKernelMsgBuilderList() {
      return internalGetSyncKernelMsgFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.PetKernelDataMsg, xddq.pb.PetKernelDataMsg.Builder, xddq.pb.PetKernelDataMsgOrBuilder> 
        internalGetSyncKernelMsgFieldBuilder() {
      if (syncKernelMsgBuilder_ == null) {
        syncKernelMsgBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.PetKernelDataMsg, xddq.pb.PetKernelDataMsg.Builder, xddq.pb.PetKernelDataMsgOrBuilder>(
                syncKernelMsg_,
                ((bitField0_ & 0x00000004) != 0),
                getParentForChildren(),
                isClean());
        syncKernelMsg_ = null;
      }
      return syncKernelMsgBuilder_;
    }

    private int freeDrawTimes_ ;
    /**
     * <code>optional int32 freeDrawTimes = 4;</code>
     * @return Whether the freeDrawTimes field is set.
     */
    @java.lang.Override
    public boolean hasFreeDrawTimes() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int32 freeDrawTimes = 4;</code>
     * @return The freeDrawTimes.
     */
    @java.lang.Override
    public int getFreeDrawTimes() {
      return freeDrawTimes_;
    }
    /**
     * <code>optional int32 freeDrawTimes = 4;</code>
     * @param value The freeDrawTimes to set.
     * @return This builder for chaining.
     */
    public Builder setFreeDrawTimes(int value) {

      freeDrawTimes_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 freeDrawTimes = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearFreeDrawTimes() {
      bitField0_ = (bitField0_ & ~0x00000008);
      freeDrawTimes_ = 0;
      onChanged();
      return this;
    }

    private int drawCount_ ;
    /**
     * <code>optional int32 drawCount = 5;</code>
     * @return Whether the drawCount field is set.
     */
    @java.lang.Override
    public boolean hasDrawCount() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional int32 drawCount = 5;</code>
     * @return The drawCount.
     */
    @java.lang.Override
    public int getDrawCount() {
      return drawCount_;
    }
    /**
     * <code>optional int32 drawCount = 5;</code>
     * @param value The drawCount to set.
     * @return This builder for chaining.
     */
    public Builder setDrawCount(int value) {

      drawCount_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 drawCount = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearDrawCount() {
      bitField0_ = (bitField0_ & ~0x00000010);
      drawCount_ = 0;
      onChanged();
      return this;
    }

    private int ensureCount_ ;
    /**
     * <code>optional int32 ensureCount = 6;</code>
     * @return Whether the ensureCount field is set.
     */
    @java.lang.Override
    public boolean hasEnsureCount() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional int32 ensureCount = 6;</code>
     * @return The ensureCount.
     */
    @java.lang.Override
    public int getEnsureCount() {
      return ensureCount_;
    }
    /**
     * <code>optional int32 ensureCount = 6;</code>
     * @param value The ensureCount to set.
     * @return This builder for chaining.
     */
    public Builder setEnsureCount(int value) {

      ensureCount_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 ensureCount = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearEnsureCount() {
      bitField0_ = (bitField0_ & ~0x00000020);
      ensureCount_ = 0;
      onChanged();
      return this;
    }

    private boolean pieceShopOpen_ ;
    /**
     * <code>optional bool pieceShopOpen = 7;</code>
     * @return Whether the pieceShopOpen field is set.
     */
    @java.lang.Override
    public boolean hasPieceShopOpen() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <code>optional bool pieceShopOpen = 7;</code>
     * @return The pieceShopOpen.
     */
    @java.lang.Override
    public boolean getPieceShopOpen() {
      return pieceShopOpen_;
    }
    /**
     * <code>optional bool pieceShopOpen = 7;</code>
     * @param value The pieceShopOpen to set.
     * @return This builder for chaining.
     */
    public Builder setPieceShopOpen(boolean value) {

      pieceShopOpen_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>optional bool pieceShopOpen = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearPieceShopOpen() {
      bitField0_ = (bitField0_ & ~0x00000040);
      pieceShopOpen_ = false;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.PetKernelDrawResp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.PetKernelDrawResp)
  private static final xddq.pb.PetKernelDrawResp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.PetKernelDrawResp();
  }

  public static xddq.pb.PetKernelDrawResp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<PetKernelDrawResp>
      PARSER = new com.google.protobuf.AbstractParser<PetKernelDrawResp>() {
    @java.lang.Override
    public PetKernelDrawResp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<PetKernelDrawResp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<PetKernelDrawResp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.PetKernelDrawResp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

