// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.TownDemonGetGuessInfoResp}
 */
public final class TownDemonGetGuessInfoResp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.TownDemonGetGuessInfoResp)
    TownDemonGetGuessInfoRespOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      TownDemonGetGuessInfoResp.class.getName());
  }
  // Use TownDemonGetGuessInfoResp.newBuilder() to construct.
  private TownDemonGetGuessInfoResp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private TownDemonGetGuessInfoResp() {
    infoList_ = java.util.Collections.emptyList();
    unionIdArr_ = emptyLongList();
    rewardRankAchi_ = emptyIntList();
    totalSettleResult_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_TownDemonGetGuessInfoResp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_TownDemonGetGuessInfoResp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.TownDemonGetGuessInfoResp.class, xddq.pb.TownDemonGetGuessInfoResp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int INFOLIST_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.TownDemonBetUnionInfo> infoList_;
  /**
   * <code>repeated .xddq.pb.TownDemonBetUnionInfo infoList = 2;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.TownDemonBetUnionInfo> getInfoListList() {
    return infoList_;
  }
  /**
   * <code>repeated .xddq.pb.TownDemonBetUnionInfo infoList = 2;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.TownDemonBetUnionInfoOrBuilder> 
      getInfoListOrBuilderList() {
    return infoList_;
  }
  /**
   * <code>repeated .xddq.pb.TownDemonBetUnionInfo infoList = 2;</code>
   */
  @java.lang.Override
  public int getInfoListCount() {
    return infoList_.size();
  }
  /**
   * <code>repeated .xddq.pb.TownDemonBetUnionInfo infoList = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.TownDemonBetUnionInfo getInfoList(int index) {
    return infoList_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.TownDemonBetUnionInfo infoList = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.TownDemonBetUnionInfoOrBuilder getInfoListOrBuilder(
      int index) {
    return infoList_.get(index);
  }

  public static final int UNIONIDARR_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private com.google.protobuf.Internal.LongList unionIdArr_ =
      emptyLongList();
  /**
   * <code>repeated int64 unionIdArr = 3;</code>
   * @return A list containing the unionIdArr.
   */
  @java.lang.Override
  public java.util.List<java.lang.Long>
      getUnionIdArrList() {
    return unionIdArr_;
  }
  /**
   * <code>repeated int64 unionIdArr = 3;</code>
   * @return The count of unionIdArr.
   */
  public int getUnionIdArrCount() {
    return unionIdArr_.size();
  }
  /**
   * <code>repeated int64 unionIdArr = 3;</code>
   * @param index The index of the element to return.
   * @return The unionIdArr at the given index.
   */
  public long getUnionIdArr(int index) {
    return unionIdArr_.getLong(index);
  }

  public static final int REWARDRANKACHI_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private com.google.protobuf.Internal.IntList rewardRankAchi_ =
      emptyIntList();
  /**
   * <code>repeated int32 rewardRankAchi = 4;</code>
   * @return A list containing the rewardRankAchi.
   */
  @java.lang.Override
  public java.util.List<java.lang.Integer>
      getRewardRankAchiList() {
    return rewardRankAchi_;
  }
  /**
   * <code>repeated int32 rewardRankAchi = 4;</code>
   * @return The count of rewardRankAchi.
   */
  public int getRewardRankAchiCount() {
    return rewardRankAchi_.size();
  }
  /**
   * <code>repeated int32 rewardRankAchi = 4;</code>
   * @param index The index of the element to return.
   * @return The rewardRankAchi at the given index.
   */
  public int getRewardRankAchi(int index) {
    return rewardRankAchi_.getInt(index);
  }

  public static final int ISRECEIVE_FIELD_NUMBER = 5;
  private boolean isReceive_ = false;
  /**
   * <code>optional bool isReceive = 5;</code>
   * @return Whether the isReceive field is set.
   */
  @java.lang.Override
  public boolean hasIsReceive() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional bool isReceive = 5;</code>
   * @return The isReceive.
   */
  @java.lang.Override
  public boolean getIsReceive() {
    return isReceive_;
  }

  public static final int REWARDPARAM_FIELD_NUMBER = 6;
  private int rewardParam_ = 0;
  /**
   * <code>optional int32 rewardParam = 6;</code>
   * @return Whether the rewardParam field is set.
   */
  @java.lang.Override
  public boolean hasRewardParam() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional int32 rewardParam = 6;</code>
   * @return The rewardParam.
   */
  @java.lang.Override
  public int getRewardParam() {
    return rewardParam_;
  }

  public static final int TOTALSETTLERESULT_FIELD_NUMBER = 7;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.TownDemonGuessTotalResulMsg> totalSettleResult_;
  /**
   * <code>repeated .xddq.pb.TownDemonGuessTotalResulMsg totalSettleResult = 7;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.TownDemonGuessTotalResulMsg> getTotalSettleResultList() {
    return totalSettleResult_;
  }
  /**
   * <code>repeated .xddq.pb.TownDemonGuessTotalResulMsg totalSettleResult = 7;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.TownDemonGuessTotalResulMsgOrBuilder> 
      getTotalSettleResultOrBuilderList() {
    return totalSettleResult_;
  }
  /**
   * <code>repeated .xddq.pb.TownDemonGuessTotalResulMsg totalSettleResult = 7;</code>
   */
  @java.lang.Override
  public int getTotalSettleResultCount() {
    return totalSettleResult_.size();
  }
  /**
   * <code>repeated .xddq.pb.TownDemonGuessTotalResulMsg totalSettleResult = 7;</code>
   */
  @java.lang.Override
  public xddq.pb.TownDemonGuessTotalResulMsg getTotalSettleResult(int index) {
    return totalSettleResult_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.TownDemonGuessTotalResulMsg totalSettleResult = 7;</code>
   */
  @java.lang.Override
  public xddq.pb.TownDemonGuessTotalResulMsgOrBuilder getTotalSettleResultOrBuilder(
      int index) {
    return totalSettleResult_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    for (int i = 0; i < getInfoListCount(); i++) {
      if (!getInfoList(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    for (int i = 0; i < infoList_.size(); i++) {
      output.writeMessage(2, infoList_.get(i));
    }
    for (int i = 0; i < unionIdArr_.size(); i++) {
      output.writeInt64(3, unionIdArr_.getLong(i));
    }
    for (int i = 0; i < rewardRankAchi_.size(); i++) {
      output.writeInt32(4, rewardRankAchi_.getInt(i));
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeBool(5, isReceive_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt32(6, rewardParam_);
    }
    for (int i = 0; i < totalSettleResult_.size(); i++) {
      output.writeMessage(7, totalSettleResult_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    for (int i = 0; i < infoList_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, infoList_.get(i));
    }
    {
      int dataSize = 0;
      for (int i = 0; i < unionIdArr_.size(); i++) {
        dataSize += com.google.protobuf.CodedOutputStream
          .computeInt64SizeNoTag(unionIdArr_.getLong(i));
      }
      size += dataSize;
      size += 1 * getUnionIdArrList().size();
    }
    {
      int dataSize = 0;
      for (int i = 0; i < rewardRankAchi_.size(); i++) {
        dataSize += com.google.protobuf.CodedOutputStream
          .computeInt32SizeNoTag(rewardRankAchi_.getInt(i));
      }
      size += dataSize;
      size += 1 * getRewardRankAchiList().size();
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(5, isReceive_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(6, rewardParam_);
    }
    for (int i = 0; i < totalSettleResult_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(7, totalSettleResult_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.TownDemonGetGuessInfoResp)) {
      return super.equals(obj);
    }
    xddq.pb.TownDemonGetGuessInfoResp other = (xddq.pb.TownDemonGetGuessInfoResp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (!getInfoListList()
        .equals(other.getInfoListList())) return false;
    if (!getUnionIdArrList()
        .equals(other.getUnionIdArrList())) return false;
    if (!getRewardRankAchiList()
        .equals(other.getRewardRankAchiList())) return false;
    if (hasIsReceive() != other.hasIsReceive()) return false;
    if (hasIsReceive()) {
      if (getIsReceive()
          != other.getIsReceive()) return false;
    }
    if (hasRewardParam() != other.hasRewardParam()) return false;
    if (hasRewardParam()) {
      if (getRewardParam()
          != other.getRewardParam()) return false;
    }
    if (!getTotalSettleResultList()
        .equals(other.getTotalSettleResultList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (getInfoListCount() > 0) {
      hash = (37 * hash) + INFOLIST_FIELD_NUMBER;
      hash = (53 * hash) + getInfoListList().hashCode();
    }
    if (getUnionIdArrCount() > 0) {
      hash = (37 * hash) + UNIONIDARR_FIELD_NUMBER;
      hash = (53 * hash) + getUnionIdArrList().hashCode();
    }
    if (getRewardRankAchiCount() > 0) {
      hash = (37 * hash) + REWARDRANKACHI_FIELD_NUMBER;
      hash = (53 * hash) + getRewardRankAchiList().hashCode();
    }
    if (hasIsReceive()) {
      hash = (37 * hash) + ISRECEIVE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getIsReceive());
    }
    if (hasRewardParam()) {
      hash = (37 * hash) + REWARDPARAM_FIELD_NUMBER;
      hash = (53 * hash) + getRewardParam();
    }
    if (getTotalSettleResultCount() > 0) {
      hash = (37 * hash) + TOTALSETTLERESULT_FIELD_NUMBER;
      hash = (53 * hash) + getTotalSettleResultList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.TownDemonGetGuessInfoResp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.TownDemonGetGuessInfoResp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.TownDemonGetGuessInfoResp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.TownDemonGetGuessInfoResp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.TownDemonGetGuessInfoResp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.TownDemonGetGuessInfoResp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.TownDemonGetGuessInfoResp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.TownDemonGetGuessInfoResp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.TownDemonGetGuessInfoResp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.TownDemonGetGuessInfoResp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.TownDemonGetGuessInfoResp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.TownDemonGetGuessInfoResp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.TownDemonGetGuessInfoResp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.TownDemonGetGuessInfoResp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.TownDemonGetGuessInfoResp)
      xddq.pb.TownDemonGetGuessInfoRespOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_TownDemonGetGuessInfoResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_TownDemonGetGuessInfoResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.TownDemonGetGuessInfoResp.class, xddq.pb.TownDemonGetGuessInfoResp.Builder.class);
    }

    // Construct using xddq.pb.TownDemonGetGuessInfoResp.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      if (infoListBuilder_ == null) {
        infoList_ = java.util.Collections.emptyList();
      } else {
        infoList_ = null;
        infoListBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000002);
      unionIdArr_ = emptyLongList();
      rewardRankAchi_ = emptyIntList();
      isReceive_ = false;
      rewardParam_ = 0;
      if (totalSettleResultBuilder_ == null) {
        totalSettleResult_ = java.util.Collections.emptyList();
      } else {
        totalSettleResult_ = null;
        totalSettleResultBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000040);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_TownDemonGetGuessInfoResp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.TownDemonGetGuessInfoResp getDefaultInstanceForType() {
      return xddq.pb.TownDemonGetGuessInfoResp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.TownDemonGetGuessInfoResp build() {
      xddq.pb.TownDemonGetGuessInfoResp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.TownDemonGetGuessInfoResp buildPartial() {
      xddq.pb.TownDemonGetGuessInfoResp result = new xddq.pb.TownDemonGetGuessInfoResp(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.TownDemonGetGuessInfoResp result) {
      if (infoListBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          infoList_ = java.util.Collections.unmodifiableList(infoList_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.infoList_ = infoList_;
      } else {
        result.infoList_ = infoListBuilder_.build();
      }
      if (totalSettleResultBuilder_ == null) {
        if (((bitField0_ & 0x00000040) != 0)) {
          totalSettleResult_ = java.util.Collections.unmodifiableList(totalSettleResult_);
          bitField0_ = (bitField0_ & ~0x00000040);
        }
        result.totalSettleResult_ = totalSettleResult_;
      } else {
        result.totalSettleResult_ = totalSettleResultBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.TownDemonGetGuessInfoResp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        unionIdArr_.makeImmutable();
        result.unionIdArr_ = unionIdArr_;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        rewardRankAchi_.makeImmutable();
        result.rewardRankAchi_ = rewardRankAchi_;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.isReceive_ = isReceive_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.rewardParam_ = rewardParam_;
        to_bitField0_ |= 0x00000004;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.TownDemonGetGuessInfoResp) {
        return mergeFrom((xddq.pb.TownDemonGetGuessInfoResp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.TownDemonGetGuessInfoResp other) {
      if (other == xddq.pb.TownDemonGetGuessInfoResp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (infoListBuilder_ == null) {
        if (!other.infoList_.isEmpty()) {
          if (infoList_.isEmpty()) {
            infoList_ = other.infoList_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureInfoListIsMutable();
            infoList_.addAll(other.infoList_);
          }
          onChanged();
        }
      } else {
        if (!other.infoList_.isEmpty()) {
          if (infoListBuilder_.isEmpty()) {
            infoListBuilder_.dispose();
            infoListBuilder_ = null;
            infoList_ = other.infoList_;
            bitField0_ = (bitField0_ & ~0x00000002);
            infoListBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetInfoListFieldBuilder() : null;
          } else {
            infoListBuilder_.addAllMessages(other.infoList_);
          }
        }
      }
      if (!other.unionIdArr_.isEmpty()) {
        if (unionIdArr_.isEmpty()) {
          unionIdArr_ = other.unionIdArr_;
          unionIdArr_.makeImmutable();
          bitField0_ |= 0x00000004;
        } else {
          ensureUnionIdArrIsMutable();
          unionIdArr_.addAll(other.unionIdArr_);
        }
        onChanged();
      }
      if (!other.rewardRankAchi_.isEmpty()) {
        if (rewardRankAchi_.isEmpty()) {
          rewardRankAchi_ = other.rewardRankAchi_;
          rewardRankAchi_.makeImmutable();
          bitField0_ |= 0x00000008;
        } else {
          ensureRewardRankAchiIsMutable();
          rewardRankAchi_.addAll(other.rewardRankAchi_);
        }
        onChanged();
      }
      if (other.hasIsReceive()) {
        setIsReceive(other.getIsReceive());
      }
      if (other.hasRewardParam()) {
        setRewardParam(other.getRewardParam());
      }
      if (totalSettleResultBuilder_ == null) {
        if (!other.totalSettleResult_.isEmpty()) {
          if (totalSettleResult_.isEmpty()) {
            totalSettleResult_ = other.totalSettleResult_;
            bitField0_ = (bitField0_ & ~0x00000040);
          } else {
            ensureTotalSettleResultIsMutable();
            totalSettleResult_.addAll(other.totalSettleResult_);
          }
          onChanged();
        }
      } else {
        if (!other.totalSettleResult_.isEmpty()) {
          if (totalSettleResultBuilder_.isEmpty()) {
            totalSettleResultBuilder_.dispose();
            totalSettleResultBuilder_ = null;
            totalSettleResult_ = other.totalSettleResult_;
            bitField0_ = (bitField0_ & ~0x00000040);
            totalSettleResultBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetTotalSettleResultFieldBuilder() : null;
          } else {
            totalSettleResultBuilder_.addAllMessages(other.totalSettleResult_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      for (int i = 0; i < getInfoListCount(); i++) {
        if (!getInfoList(i).isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              xddq.pb.TownDemonBetUnionInfo m =
                  input.readMessage(
                      xddq.pb.TownDemonBetUnionInfo.parser(),
                      extensionRegistry);
              if (infoListBuilder_ == null) {
                ensureInfoListIsMutable();
                infoList_.add(m);
              } else {
                infoListBuilder_.addMessage(m);
              }
              break;
            } // case 18
            case 24: {
              long v = input.readInt64();
              ensureUnionIdArrIsMutable();
              unionIdArr_.addLong(v);
              break;
            } // case 24
            case 26: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              ensureUnionIdArrIsMutable();
              while (input.getBytesUntilLimit() > 0) {
                unionIdArr_.addLong(input.readInt64());
              }
              input.popLimit(limit);
              break;
            } // case 26
            case 32: {
              int v = input.readInt32();
              ensureRewardRankAchiIsMutable();
              rewardRankAchi_.addInt(v);
              break;
            } // case 32
            case 34: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              ensureRewardRankAchiIsMutable();
              while (input.getBytesUntilLimit() > 0) {
                rewardRankAchi_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            } // case 34
            case 40: {
              isReceive_ = input.readBool();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            case 48: {
              rewardParam_ = input.readInt32();
              bitField0_ |= 0x00000020;
              break;
            } // case 48
            case 58: {
              xddq.pb.TownDemonGuessTotalResulMsg m =
                  input.readMessage(
                      xddq.pb.TownDemonGuessTotalResulMsg.parser(),
                      extensionRegistry);
              if (totalSettleResultBuilder_ == null) {
                ensureTotalSettleResultIsMutable();
                totalSettleResult_.add(m);
              } else {
                totalSettleResultBuilder_.addMessage(m);
              }
              break;
            } // case 58
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.TownDemonBetUnionInfo> infoList_ =
      java.util.Collections.emptyList();
    private void ensureInfoListIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        infoList_ = new java.util.ArrayList<xddq.pb.TownDemonBetUnionInfo>(infoList_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.TownDemonBetUnionInfo, xddq.pb.TownDemonBetUnionInfo.Builder, xddq.pb.TownDemonBetUnionInfoOrBuilder> infoListBuilder_;

    /**
     * <code>repeated .xddq.pb.TownDemonBetUnionInfo infoList = 2;</code>
     */
    public java.util.List<xddq.pb.TownDemonBetUnionInfo> getInfoListList() {
      if (infoListBuilder_ == null) {
        return java.util.Collections.unmodifiableList(infoList_);
      } else {
        return infoListBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.TownDemonBetUnionInfo infoList = 2;</code>
     */
    public int getInfoListCount() {
      if (infoListBuilder_ == null) {
        return infoList_.size();
      } else {
        return infoListBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.TownDemonBetUnionInfo infoList = 2;</code>
     */
    public xddq.pb.TownDemonBetUnionInfo getInfoList(int index) {
      if (infoListBuilder_ == null) {
        return infoList_.get(index);
      } else {
        return infoListBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.TownDemonBetUnionInfo infoList = 2;</code>
     */
    public Builder setInfoList(
        int index, xddq.pb.TownDemonBetUnionInfo value) {
      if (infoListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureInfoListIsMutable();
        infoList_.set(index, value);
        onChanged();
      } else {
        infoListBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.TownDemonBetUnionInfo infoList = 2;</code>
     */
    public Builder setInfoList(
        int index, xddq.pb.TownDemonBetUnionInfo.Builder builderForValue) {
      if (infoListBuilder_ == null) {
        ensureInfoListIsMutable();
        infoList_.set(index, builderForValue.build());
        onChanged();
      } else {
        infoListBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.TownDemonBetUnionInfo infoList = 2;</code>
     */
    public Builder addInfoList(xddq.pb.TownDemonBetUnionInfo value) {
      if (infoListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureInfoListIsMutable();
        infoList_.add(value);
        onChanged();
      } else {
        infoListBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.TownDemonBetUnionInfo infoList = 2;</code>
     */
    public Builder addInfoList(
        int index, xddq.pb.TownDemonBetUnionInfo value) {
      if (infoListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureInfoListIsMutable();
        infoList_.add(index, value);
        onChanged();
      } else {
        infoListBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.TownDemonBetUnionInfo infoList = 2;</code>
     */
    public Builder addInfoList(
        xddq.pb.TownDemonBetUnionInfo.Builder builderForValue) {
      if (infoListBuilder_ == null) {
        ensureInfoListIsMutable();
        infoList_.add(builderForValue.build());
        onChanged();
      } else {
        infoListBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.TownDemonBetUnionInfo infoList = 2;</code>
     */
    public Builder addInfoList(
        int index, xddq.pb.TownDemonBetUnionInfo.Builder builderForValue) {
      if (infoListBuilder_ == null) {
        ensureInfoListIsMutable();
        infoList_.add(index, builderForValue.build());
        onChanged();
      } else {
        infoListBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.TownDemonBetUnionInfo infoList = 2;</code>
     */
    public Builder addAllInfoList(
        java.lang.Iterable<? extends xddq.pb.TownDemonBetUnionInfo> values) {
      if (infoListBuilder_ == null) {
        ensureInfoListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, infoList_);
        onChanged();
      } else {
        infoListBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.TownDemonBetUnionInfo infoList = 2;</code>
     */
    public Builder clearInfoList() {
      if (infoListBuilder_ == null) {
        infoList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        infoListBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.TownDemonBetUnionInfo infoList = 2;</code>
     */
    public Builder removeInfoList(int index) {
      if (infoListBuilder_ == null) {
        ensureInfoListIsMutable();
        infoList_.remove(index);
        onChanged();
      } else {
        infoListBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.TownDemonBetUnionInfo infoList = 2;</code>
     */
    public xddq.pb.TownDemonBetUnionInfo.Builder getInfoListBuilder(
        int index) {
      return internalGetInfoListFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.TownDemonBetUnionInfo infoList = 2;</code>
     */
    public xddq.pb.TownDemonBetUnionInfoOrBuilder getInfoListOrBuilder(
        int index) {
      if (infoListBuilder_ == null) {
        return infoList_.get(index);  } else {
        return infoListBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.TownDemonBetUnionInfo infoList = 2;</code>
     */
    public java.util.List<? extends xddq.pb.TownDemonBetUnionInfoOrBuilder> 
         getInfoListOrBuilderList() {
      if (infoListBuilder_ != null) {
        return infoListBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(infoList_);
      }
    }
    /**
     * <code>repeated .xddq.pb.TownDemonBetUnionInfo infoList = 2;</code>
     */
    public xddq.pb.TownDemonBetUnionInfo.Builder addInfoListBuilder() {
      return internalGetInfoListFieldBuilder().addBuilder(
          xddq.pb.TownDemonBetUnionInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.TownDemonBetUnionInfo infoList = 2;</code>
     */
    public xddq.pb.TownDemonBetUnionInfo.Builder addInfoListBuilder(
        int index) {
      return internalGetInfoListFieldBuilder().addBuilder(
          index, xddq.pb.TownDemonBetUnionInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.TownDemonBetUnionInfo infoList = 2;</code>
     */
    public java.util.List<xddq.pb.TownDemonBetUnionInfo.Builder> 
         getInfoListBuilderList() {
      return internalGetInfoListFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.TownDemonBetUnionInfo, xddq.pb.TownDemonBetUnionInfo.Builder, xddq.pb.TownDemonBetUnionInfoOrBuilder> 
        internalGetInfoListFieldBuilder() {
      if (infoListBuilder_ == null) {
        infoListBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.TownDemonBetUnionInfo, xddq.pb.TownDemonBetUnionInfo.Builder, xddq.pb.TownDemonBetUnionInfoOrBuilder>(
                infoList_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        infoList_ = null;
      }
      return infoListBuilder_;
    }

    private com.google.protobuf.Internal.LongList unionIdArr_ = emptyLongList();
    private void ensureUnionIdArrIsMutable() {
      if (!unionIdArr_.isModifiable()) {
        unionIdArr_ = makeMutableCopy(unionIdArr_);
      }
      bitField0_ |= 0x00000004;
    }
    /**
     * <code>repeated int64 unionIdArr = 3;</code>
     * @return A list containing the unionIdArr.
     */
    public java.util.List<java.lang.Long>
        getUnionIdArrList() {
      unionIdArr_.makeImmutable();
      return unionIdArr_;
    }
    /**
     * <code>repeated int64 unionIdArr = 3;</code>
     * @return The count of unionIdArr.
     */
    public int getUnionIdArrCount() {
      return unionIdArr_.size();
    }
    /**
     * <code>repeated int64 unionIdArr = 3;</code>
     * @param index The index of the element to return.
     * @return The unionIdArr at the given index.
     */
    public long getUnionIdArr(int index) {
      return unionIdArr_.getLong(index);
    }
    /**
     * <code>repeated int64 unionIdArr = 3;</code>
     * @param index The index to set the value at.
     * @param value The unionIdArr to set.
     * @return This builder for chaining.
     */
    public Builder setUnionIdArr(
        int index, long value) {

      ensureUnionIdArrIsMutable();
      unionIdArr_.setLong(index, value);
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int64 unionIdArr = 3;</code>
     * @param value The unionIdArr to add.
     * @return This builder for chaining.
     */
    public Builder addUnionIdArr(long value) {

      ensureUnionIdArrIsMutable();
      unionIdArr_.addLong(value);
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int64 unionIdArr = 3;</code>
     * @param values The unionIdArr to add.
     * @return This builder for chaining.
     */
    public Builder addAllUnionIdArr(
        java.lang.Iterable<? extends java.lang.Long> values) {
      ensureUnionIdArrIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, unionIdArr_);
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int64 unionIdArr = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearUnionIdArr() {
      unionIdArr_ = emptyLongList();
      bitField0_ = (bitField0_ & ~0x00000004);
      onChanged();
      return this;
    }

    private com.google.protobuf.Internal.IntList rewardRankAchi_ = emptyIntList();
    private void ensureRewardRankAchiIsMutable() {
      if (!rewardRankAchi_.isModifiable()) {
        rewardRankAchi_ = makeMutableCopy(rewardRankAchi_);
      }
      bitField0_ |= 0x00000008;
    }
    /**
     * <code>repeated int32 rewardRankAchi = 4;</code>
     * @return A list containing the rewardRankAchi.
     */
    public java.util.List<java.lang.Integer>
        getRewardRankAchiList() {
      rewardRankAchi_.makeImmutable();
      return rewardRankAchi_;
    }
    /**
     * <code>repeated int32 rewardRankAchi = 4;</code>
     * @return The count of rewardRankAchi.
     */
    public int getRewardRankAchiCount() {
      return rewardRankAchi_.size();
    }
    /**
     * <code>repeated int32 rewardRankAchi = 4;</code>
     * @param index The index of the element to return.
     * @return The rewardRankAchi at the given index.
     */
    public int getRewardRankAchi(int index) {
      return rewardRankAchi_.getInt(index);
    }
    /**
     * <code>repeated int32 rewardRankAchi = 4;</code>
     * @param index The index to set the value at.
     * @param value The rewardRankAchi to set.
     * @return This builder for chaining.
     */
    public Builder setRewardRankAchi(
        int index, int value) {

      ensureRewardRankAchiIsMutable();
      rewardRankAchi_.setInt(index, value);
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 rewardRankAchi = 4;</code>
     * @param value The rewardRankAchi to add.
     * @return This builder for chaining.
     */
    public Builder addRewardRankAchi(int value) {

      ensureRewardRankAchiIsMutable();
      rewardRankAchi_.addInt(value);
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 rewardRankAchi = 4;</code>
     * @param values The rewardRankAchi to add.
     * @return This builder for chaining.
     */
    public Builder addAllRewardRankAchi(
        java.lang.Iterable<? extends java.lang.Integer> values) {
      ensureRewardRankAchiIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, rewardRankAchi_);
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 rewardRankAchi = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearRewardRankAchi() {
      rewardRankAchi_ = emptyIntList();
      bitField0_ = (bitField0_ & ~0x00000008);
      onChanged();
      return this;
    }

    private boolean isReceive_ ;
    /**
     * <code>optional bool isReceive = 5;</code>
     * @return Whether the isReceive field is set.
     */
    @java.lang.Override
    public boolean hasIsReceive() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional bool isReceive = 5;</code>
     * @return The isReceive.
     */
    @java.lang.Override
    public boolean getIsReceive() {
      return isReceive_;
    }
    /**
     * <code>optional bool isReceive = 5;</code>
     * @param value The isReceive to set.
     * @return This builder for chaining.
     */
    public Builder setIsReceive(boolean value) {

      isReceive_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional bool isReceive = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearIsReceive() {
      bitField0_ = (bitField0_ & ~0x00000010);
      isReceive_ = false;
      onChanged();
      return this;
    }

    private int rewardParam_ ;
    /**
     * <code>optional int32 rewardParam = 6;</code>
     * @return Whether the rewardParam field is set.
     */
    @java.lang.Override
    public boolean hasRewardParam() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional int32 rewardParam = 6;</code>
     * @return The rewardParam.
     */
    @java.lang.Override
    public int getRewardParam() {
      return rewardParam_;
    }
    /**
     * <code>optional int32 rewardParam = 6;</code>
     * @param value The rewardParam to set.
     * @return This builder for chaining.
     */
    public Builder setRewardParam(int value) {

      rewardParam_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 rewardParam = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearRewardParam() {
      bitField0_ = (bitField0_ & ~0x00000020);
      rewardParam_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.TownDemonGuessTotalResulMsg> totalSettleResult_ =
      java.util.Collections.emptyList();
    private void ensureTotalSettleResultIsMutable() {
      if (!((bitField0_ & 0x00000040) != 0)) {
        totalSettleResult_ = new java.util.ArrayList<xddq.pb.TownDemonGuessTotalResulMsg>(totalSettleResult_);
        bitField0_ |= 0x00000040;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.TownDemonGuessTotalResulMsg, xddq.pb.TownDemonGuessTotalResulMsg.Builder, xddq.pb.TownDemonGuessTotalResulMsgOrBuilder> totalSettleResultBuilder_;

    /**
     * <code>repeated .xddq.pb.TownDemonGuessTotalResulMsg totalSettleResult = 7;</code>
     */
    public java.util.List<xddq.pb.TownDemonGuessTotalResulMsg> getTotalSettleResultList() {
      if (totalSettleResultBuilder_ == null) {
        return java.util.Collections.unmodifiableList(totalSettleResult_);
      } else {
        return totalSettleResultBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.TownDemonGuessTotalResulMsg totalSettleResult = 7;</code>
     */
    public int getTotalSettleResultCount() {
      if (totalSettleResultBuilder_ == null) {
        return totalSettleResult_.size();
      } else {
        return totalSettleResultBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.TownDemonGuessTotalResulMsg totalSettleResult = 7;</code>
     */
    public xddq.pb.TownDemonGuessTotalResulMsg getTotalSettleResult(int index) {
      if (totalSettleResultBuilder_ == null) {
        return totalSettleResult_.get(index);
      } else {
        return totalSettleResultBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.TownDemonGuessTotalResulMsg totalSettleResult = 7;</code>
     */
    public Builder setTotalSettleResult(
        int index, xddq.pb.TownDemonGuessTotalResulMsg value) {
      if (totalSettleResultBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureTotalSettleResultIsMutable();
        totalSettleResult_.set(index, value);
        onChanged();
      } else {
        totalSettleResultBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.TownDemonGuessTotalResulMsg totalSettleResult = 7;</code>
     */
    public Builder setTotalSettleResult(
        int index, xddq.pb.TownDemonGuessTotalResulMsg.Builder builderForValue) {
      if (totalSettleResultBuilder_ == null) {
        ensureTotalSettleResultIsMutable();
        totalSettleResult_.set(index, builderForValue.build());
        onChanged();
      } else {
        totalSettleResultBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.TownDemonGuessTotalResulMsg totalSettleResult = 7;</code>
     */
    public Builder addTotalSettleResult(xddq.pb.TownDemonGuessTotalResulMsg value) {
      if (totalSettleResultBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureTotalSettleResultIsMutable();
        totalSettleResult_.add(value);
        onChanged();
      } else {
        totalSettleResultBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.TownDemonGuessTotalResulMsg totalSettleResult = 7;</code>
     */
    public Builder addTotalSettleResult(
        int index, xddq.pb.TownDemonGuessTotalResulMsg value) {
      if (totalSettleResultBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureTotalSettleResultIsMutable();
        totalSettleResult_.add(index, value);
        onChanged();
      } else {
        totalSettleResultBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.TownDemonGuessTotalResulMsg totalSettleResult = 7;</code>
     */
    public Builder addTotalSettleResult(
        xddq.pb.TownDemonGuessTotalResulMsg.Builder builderForValue) {
      if (totalSettleResultBuilder_ == null) {
        ensureTotalSettleResultIsMutable();
        totalSettleResult_.add(builderForValue.build());
        onChanged();
      } else {
        totalSettleResultBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.TownDemonGuessTotalResulMsg totalSettleResult = 7;</code>
     */
    public Builder addTotalSettleResult(
        int index, xddq.pb.TownDemonGuessTotalResulMsg.Builder builderForValue) {
      if (totalSettleResultBuilder_ == null) {
        ensureTotalSettleResultIsMutable();
        totalSettleResult_.add(index, builderForValue.build());
        onChanged();
      } else {
        totalSettleResultBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.TownDemonGuessTotalResulMsg totalSettleResult = 7;</code>
     */
    public Builder addAllTotalSettleResult(
        java.lang.Iterable<? extends xddq.pb.TownDemonGuessTotalResulMsg> values) {
      if (totalSettleResultBuilder_ == null) {
        ensureTotalSettleResultIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, totalSettleResult_);
        onChanged();
      } else {
        totalSettleResultBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.TownDemonGuessTotalResulMsg totalSettleResult = 7;</code>
     */
    public Builder clearTotalSettleResult() {
      if (totalSettleResultBuilder_ == null) {
        totalSettleResult_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000040);
        onChanged();
      } else {
        totalSettleResultBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.TownDemonGuessTotalResulMsg totalSettleResult = 7;</code>
     */
    public Builder removeTotalSettleResult(int index) {
      if (totalSettleResultBuilder_ == null) {
        ensureTotalSettleResultIsMutable();
        totalSettleResult_.remove(index);
        onChanged();
      } else {
        totalSettleResultBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.TownDemonGuessTotalResulMsg totalSettleResult = 7;</code>
     */
    public xddq.pb.TownDemonGuessTotalResulMsg.Builder getTotalSettleResultBuilder(
        int index) {
      return internalGetTotalSettleResultFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.TownDemonGuessTotalResulMsg totalSettleResult = 7;</code>
     */
    public xddq.pb.TownDemonGuessTotalResulMsgOrBuilder getTotalSettleResultOrBuilder(
        int index) {
      if (totalSettleResultBuilder_ == null) {
        return totalSettleResult_.get(index);  } else {
        return totalSettleResultBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.TownDemonGuessTotalResulMsg totalSettleResult = 7;</code>
     */
    public java.util.List<? extends xddq.pb.TownDemonGuessTotalResulMsgOrBuilder> 
         getTotalSettleResultOrBuilderList() {
      if (totalSettleResultBuilder_ != null) {
        return totalSettleResultBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(totalSettleResult_);
      }
    }
    /**
     * <code>repeated .xddq.pb.TownDemonGuessTotalResulMsg totalSettleResult = 7;</code>
     */
    public xddq.pb.TownDemonGuessTotalResulMsg.Builder addTotalSettleResultBuilder() {
      return internalGetTotalSettleResultFieldBuilder().addBuilder(
          xddq.pb.TownDemonGuessTotalResulMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.TownDemonGuessTotalResulMsg totalSettleResult = 7;</code>
     */
    public xddq.pb.TownDemonGuessTotalResulMsg.Builder addTotalSettleResultBuilder(
        int index) {
      return internalGetTotalSettleResultFieldBuilder().addBuilder(
          index, xddq.pb.TownDemonGuessTotalResulMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.TownDemonGuessTotalResulMsg totalSettleResult = 7;</code>
     */
    public java.util.List<xddq.pb.TownDemonGuessTotalResulMsg.Builder> 
         getTotalSettleResultBuilderList() {
      return internalGetTotalSettleResultFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.TownDemonGuessTotalResulMsg, xddq.pb.TownDemonGuessTotalResulMsg.Builder, xddq.pb.TownDemonGuessTotalResulMsgOrBuilder> 
        internalGetTotalSettleResultFieldBuilder() {
      if (totalSettleResultBuilder_ == null) {
        totalSettleResultBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.TownDemonGuessTotalResulMsg, xddq.pb.TownDemonGuessTotalResulMsg.Builder, xddq.pb.TownDemonGuessTotalResulMsgOrBuilder>(
                totalSettleResult_,
                ((bitField0_ & 0x00000040) != 0),
                getParentForChildren(),
                isClean());
        totalSettleResult_ = null;
      }
      return totalSettleResultBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.TownDemonGetGuessInfoResp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.TownDemonGetGuessInfoResp)
  private static final xddq.pb.TownDemonGetGuessInfoResp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.TownDemonGetGuessInfoResp();
  }

  public static xddq.pb.TownDemonGetGuessInfoResp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<TownDemonGetGuessInfoResp>
      PARSER = new com.google.protobuf.AbstractParser<TownDemonGetGuessInfoResp>() {
    @java.lang.Override
    public TownDemonGetGuessInfoResp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<TownDemonGetGuessInfoResp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<TownDemonGetGuessInfoResp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.TownDemonGetGuessInfoResp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

