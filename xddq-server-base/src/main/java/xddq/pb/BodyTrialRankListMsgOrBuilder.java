// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

public interface BodyTrialRankListMsgOrBuilder extends
    // @@protoc_insertion_point(interface_extends:xddq.pb.BodyTrialRankListMsg)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>repeated .xddq.pb.BodyTrialRankData rankList = 1;</code>
   */
  java.util.List<xddq.pb.BodyTrialRankData> 
      getRankListList();
  /**
   * <code>repeated .xddq.pb.BodyTrialRankData rankList = 1;</code>
   */
  xddq.pb.BodyTrialRankData getRankList(int index);
  /**
   * <code>repeated .xddq.pb.BodyTrialRankData rankList = 1;</code>
   */
  int getRankListCount();
  /**
   * <code>repeated .xddq.pb.BodyTrialRankData rankList = 1;</code>
   */
  java.util.List<? extends xddq.pb.BodyTrialRankDataOrBuilder> 
      getRankListOrBuilderList();
  /**
   * <code>repeated .xddq.pb.BodyTrialRankData rankList = 1;</code>
   */
  xddq.pb.BodyTrialRankDataOrBuilder getRankListOrBuilder(
      int index);
}
