// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.WarSeasonMapCityMarkSyncMsg}
 */
public final class WarSeasonMapCityMarkSyncMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.WarSeasonMapCityMarkSyncMsg)
    WarSeasonMapCityMarkSyncMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      WarSeasonMapCityMarkSyncMsg.class.getName());
  }
  // Use WarSeasonMapCityMarkSyncMsg.newBuilder() to construct.
  private WarSeasonMapCityMarkSyncMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private WarSeasonMapCityMarkSyncMsg() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_WarSeasonMapCityMarkSyncMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_WarSeasonMapCityMarkSyncMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.WarSeasonMapCityMarkSyncMsg.class, xddq.pb.WarSeasonMapCityMarkSyncMsg.Builder.class);
  }

  private int bitField0_;
  public static final int INFO_FIELD_NUMBER = 1;
  private xddq.pb.WarSeasonCityDetailMsg info_;
  /**
   * <code>optional .xddq.pb.WarSeasonCityDetailMsg info = 1;</code>
   * @return Whether the info field is set.
   */
  @java.lang.Override
  public boolean hasInfo() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional .xddq.pb.WarSeasonCityDetailMsg info = 1;</code>
   * @return The info.
   */
  @java.lang.Override
  public xddq.pb.WarSeasonCityDetailMsg getInfo() {
    return info_ == null ? xddq.pb.WarSeasonCityDetailMsg.getDefaultInstance() : info_;
  }
  /**
   * <code>optional .xddq.pb.WarSeasonCityDetailMsg info = 1;</code>
   */
  @java.lang.Override
  public xddq.pb.WarSeasonCityDetailMsgOrBuilder getInfoOrBuilder() {
    return info_ == null ? xddq.pb.WarSeasonCityDetailMsg.getDefaultInstance() : info_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeMessage(1, getInfo());
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, getInfo());
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.WarSeasonMapCityMarkSyncMsg)) {
      return super.equals(obj);
    }
    xddq.pb.WarSeasonMapCityMarkSyncMsg other = (xddq.pb.WarSeasonMapCityMarkSyncMsg) obj;

    if (hasInfo() != other.hasInfo()) return false;
    if (hasInfo()) {
      if (!getInfo()
          .equals(other.getInfo())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasInfo()) {
      hash = (37 * hash) + INFO_FIELD_NUMBER;
      hash = (53 * hash) + getInfo().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.WarSeasonMapCityMarkSyncMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.WarSeasonMapCityMarkSyncMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.WarSeasonMapCityMarkSyncMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.WarSeasonMapCityMarkSyncMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.WarSeasonMapCityMarkSyncMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.WarSeasonMapCityMarkSyncMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.WarSeasonMapCityMarkSyncMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.WarSeasonMapCityMarkSyncMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.WarSeasonMapCityMarkSyncMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.WarSeasonMapCityMarkSyncMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.WarSeasonMapCityMarkSyncMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.WarSeasonMapCityMarkSyncMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.WarSeasonMapCityMarkSyncMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.WarSeasonMapCityMarkSyncMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.WarSeasonMapCityMarkSyncMsg)
      xddq.pb.WarSeasonMapCityMarkSyncMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_WarSeasonMapCityMarkSyncMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_WarSeasonMapCityMarkSyncMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.WarSeasonMapCityMarkSyncMsg.class, xddq.pb.WarSeasonMapCityMarkSyncMsg.Builder.class);
    }

    // Construct using xddq.pb.WarSeasonMapCityMarkSyncMsg.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetInfoFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      info_ = null;
      if (infoBuilder_ != null) {
        infoBuilder_.dispose();
        infoBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_WarSeasonMapCityMarkSyncMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.WarSeasonMapCityMarkSyncMsg getDefaultInstanceForType() {
      return xddq.pb.WarSeasonMapCityMarkSyncMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.WarSeasonMapCityMarkSyncMsg build() {
      xddq.pb.WarSeasonMapCityMarkSyncMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.WarSeasonMapCityMarkSyncMsg buildPartial() {
      xddq.pb.WarSeasonMapCityMarkSyncMsg result = new xddq.pb.WarSeasonMapCityMarkSyncMsg(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.WarSeasonMapCityMarkSyncMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.info_ = infoBuilder_ == null
            ? info_
            : infoBuilder_.build();
        to_bitField0_ |= 0x00000001;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.WarSeasonMapCityMarkSyncMsg) {
        return mergeFrom((xddq.pb.WarSeasonMapCityMarkSyncMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.WarSeasonMapCityMarkSyncMsg other) {
      if (other == xddq.pb.WarSeasonMapCityMarkSyncMsg.getDefaultInstance()) return this;
      if (other.hasInfo()) {
        mergeInfo(other.getInfo());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              input.readMessage(
                  internalGetInfoFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private xddq.pb.WarSeasonCityDetailMsg info_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.WarSeasonCityDetailMsg, xddq.pb.WarSeasonCityDetailMsg.Builder, xddq.pb.WarSeasonCityDetailMsgOrBuilder> infoBuilder_;
    /**
     * <code>optional .xddq.pb.WarSeasonCityDetailMsg info = 1;</code>
     * @return Whether the info field is set.
     */
    public boolean hasInfo() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .xddq.pb.WarSeasonCityDetailMsg info = 1;</code>
     * @return The info.
     */
    public xddq.pb.WarSeasonCityDetailMsg getInfo() {
      if (infoBuilder_ == null) {
        return info_ == null ? xddq.pb.WarSeasonCityDetailMsg.getDefaultInstance() : info_;
      } else {
        return infoBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.WarSeasonCityDetailMsg info = 1;</code>
     */
    public Builder setInfo(xddq.pb.WarSeasonCityDetailMsg value) {
      if (infoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        info_ = value;
      } else {
        infoBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.WarSeasonCityDetailMsg info = 1;</code>
     */
    public Builder setInfo(
        xddq.pb.WarSeasonCityDetailMsg.Builder builderForValue) {
      if (infoBuilder_ == null) {
        info_ = builderForValue.build();
      } else {
        infoBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.WarSeasonCityDetailMsg info = 1;</code>
     */
    public Builder mergeInfo(xddq.pb.WarSeasonCityDetailMsg value) {
      if (infoBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0) &&
          info_ != null &&
          info_ != xddq.pb.WarSeasonCityDetailMsg.getDefaultInstance()) {
          getInfoBuilder().mergeFrom(value);
        } else {
          info_ = value;
        }
      } else {
        infoBuilder_.mergeFrom(value);
      }
      if (info_ != null) {
        bitField0_ |= 0x00000001;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.WarSeasonCityDetailMsg info = 1;</code>
     */
    public Builder clearInfo() {
      bitField0_ = (bitField0_ & ~0x00000001);
      info_ = null;
      if (infoBuilder_ != null) {
        infoBuilder_.dispose();
        infoBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.WarSeasonCityDetailMsg info = 1;</code>
     */
    public xddq.pb.WarSeasonCityDetailMsg.Builder getInfoBuilder() {
      bitField0_ |= 0x00000001;
      onChanged();
      return internalGetInfoFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.WarSeasonCityDetailMsg info = 1;</code>
     */
    public xddq.pb.WarSeasonCityDetailMsgOrBuilder getInfoOrBuilder() {
      if (infoBuilder_ != null) {
        return infoBuilder_.getMessageOrBuilder();
      } else {
        return info_ == null ?
            xddq.pb.WarSeasonCityDetailMsg.getDefaultInstance() : info_;
      }
    }
    /**
     * <code>optional .xddq.pb.WarSeasonCityDetailMsg info = 1;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.WarSeasonCityDetailMsg, xddq.pb.WarSeasonCityDetailMsg.Builder, xddq.pb.WarSeasonCityDetailMsgOrBuilder> 
        internalGetInfoFieldBuilder() {
      if (infoBuilder_ == null) {
        infoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.WarSeasonCityDetailMsg, xddq.pb.WarSeasonCityDetailMsg.Builder, xddq.pb.WarSeasonCityDetailMsgOrBuilder>(
                getInfo(),
                getParentForChildren(),
                isClean());
        info_ = null;
      }
      return infoBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.WarSeasonMapCityMarkSyncMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.WarSeasonMapCityMarkSyncMsg)
  private static final xddq.pb.WarSeasonMapCityMarkSyncMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.WarSeasonMapCityMarkSyncMsg();
  }

  public static xddq.pb.WarSeasonMapCityMarkSyncMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<WarSeasonMapCityMarkSyncMsg>
      PARSER = new com.google.protobuf.AbstractParser<WarSeasonMapCityMarkSyncMsg>() {
    @java.lang.Override
    public WarSeasonMapCityMarkSyncMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<WarSeasonMapCityMarkSyncMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<WarSeasonMapCityMarkSyncMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.WarSeasonMapCityMarkSyncMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

