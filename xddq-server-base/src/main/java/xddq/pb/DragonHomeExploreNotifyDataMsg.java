// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.DragonHomeExploreNotifyDataMsg}
 */
public final class DragonHomeExploreNotifyDataMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.DragonHomeExploreNotifyDataMsg)
    DragonHomeExploreNotifyDataMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      DragonHomeExploreNotifyDataMsg.class.getName());
  }
  // Use DragonHomeExploreNotifyDataMsg.newBuilder() to construct.
  private DragonHomeExploreNotifyDataMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private DragonHomeExploreNotifyDataMsg() {
    data_ = java.util.Collections.emptyList();
    monsterInfo_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_DragonHomeExploreNotifyDataMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_DragonHomeExploreNotifyDataMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.DragonHomeExploreNotifyDataMsg.class, xddq.pb.DragonHomeExploreNotifyDataMsg.Builder.class);
  }

  public static final int DATA_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.MapTile> data_;
  /**
   * <code>repeated .xddq.pb.MapTile data = 1;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.MapTile> getDataList() {
    return data_;
  }
  /**
   * <code>repeated .xddq.pb.MapTile data = 1;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.MapTileOrBuilder> 
      getDataOrBuilderList() {
    return data_;
  }
  /**
   * <code>repeated .xddq.pb.MapTile data = 1;</code>
   */
  @java.lang.Override
  public int getDataCount() {
    return data_.size();
  }
  /**
   * <code>repeated .xddq.pb.MapTile data = 1;</code>
   */
  @java.lang.Override
  public xddq.pb.MapTile getData(int index) {
    return data_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.MapTile data = 1;</code>
   */
  @java.lang.Override
  public xddq.pb.MapTileOrBuilder getDataOrBuilder(
      int index) {
    return data_.get(index);
  }

  public static final int MONSTERINFO_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.DragonHomeMonsterMsg> monsterInfo_;
  /**
   * <code>repeated .xddq.pb.DragonHomeMonsterMsg monsterInfo = 2;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.DragonHomeMonsterMsg> getMonsterInfoList() {
    return monsterInfo_;
  }
  /**
   * <code>repeated .xddq.pb.DragonHomeMonsterMsg monsterInfo = 2;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.DragonHomeMonsterMsgOrBuilder> 
      getMonsterInfoOrBuilderList() {
    return monsterInfo_;
  }
  /**
   * <code>repeated .xddq.pb.DragonHomeMonsterMsg monsterInfo = 2;</code>
   */
  @java.lang.Override
  public int getMonsterInfoCount() {
    return monsterInfo_.size();
  }
  /**
   * <code>repeated .xddq.pb.DragonHomeMonsterMsg monsterInfo = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.DragonHomeMonsterMsg getMonsterInfo(int index) {
    return monsterInfo_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.DragonHomeMonsterMsg monsterInfo = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.DragonHomeMonsterMsgOrBuilder getMonsterInfoOrBuilder(
      int index) {
    return monsterInfo_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    for (int i = 0; i < getDataCount(); i++) {
      if (!getData(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    for (int i = 0; i < getMonsterInfoCount(); i++) {
      if (!getMonsterInfo(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    for (int i = 0; i < data_.size(); i++) {
      output.writeMessage(1, data_.get(i));
    }
    for (int i = 0; i < monsterInfo_.size(); i++) {
      output.writeMessage(2, monsterInfo_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    for (int i = 0; i < data_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, data_.get(i));
    }
    for (int i = 0; i < monsterInfo_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, monsterInfo_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.DragonHomeExploreNotifyDataMsg)) {
      return super.equals(obj);
    }
    xddq.pb.DragonHomeExploreNotifyDataMsg other = (xddq.pb.DragonHomeExploreNotifyDataMsg) obj;

    if (!getDataList()
        .equals(other.getDataList())) return false;
    if (!getMonsterInfoList()
        .equals(other.getMonsterInfoList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (getDataCount() > 0) {
      hash = (37 * hash) + DATA_FIELD_NUMBER;
      hash = (53 * hash) + getDataList().hashCode();
    }
    if (getMonsterInfoCount() > 0) {
      hash = (37 * hash) + MONSTERINFO_FIELD_NUMBER;
      hash = (53 * hash) + getMonsterInfoList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.DragonHomeExploreNotifyDataMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.DragonHomeExploreNotifyDataMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.DragonHomeExploreNotifyDataMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.DragonHomeExploreNotifyDataMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.DragonHomeExploreNotifyDataMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.DragonHomeExploreNotifyDataMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.DragonHomeExploreNotifyDataMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.DragonHomeExploreNotifyDataMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.DragonHomeExploreNotifyDataMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.DragonHomeExploreNotifyDataMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.DragonHomeExploreNotifyDataMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.DragonHomeExploreNotifyDataMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.DragonHomeExploreNotifyDataMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.DragonHomeExploreNotifyDataMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.DragonHomeExploreNotifyDataMsg)
      xddq.pb.DragonHomeExploreNotifyDataMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_DragonHomeExploreNotifyDataMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_DragonHomeExploreNotifyDataMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.DragonHomeExploreNotifyDataMsg.class, xddq.pb.DragonHomeExploreNotifyDataMsg.Builder.class);
    }

    // Construct using xddq.pb.DragonHomeExploreNotifyDataMsg.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      if (dataBuilder_ == null) {
        data_ = java.util.Collections.emptyList();
      } else {
        data_ = null;
        dataBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000001);
      if (monsterInfoBuilder_ == null) {
        monsterInfo_ = java.util.Collections.emptyList();
      } else {
        monsterInfo_ = null;
        monsterInfoBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000002);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_DragonHomeExploreNotifyDataMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.DragonHomeExploreNotifyDataMsg getDefaultInstanceForType() {
      return xddq.pb.DragonHomeExploreNotifyDataMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.DragonHomeExploreNotifyDataMsg build() {
      xddq.pb.DragonHomeExploreNotifyDataMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.DragonHomeExploreNotifyDataMsg buildPartial() {
      xddq.pb.DragonHomeExploreNotifyDataMsg result = new xddq.pb.DragonHomeExploreNotifyDataMsg(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.DragonHomeExploreNotifyDataMsg result) {
      if (dataBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          data_ = java.util.Collections.unmodifiableList(data_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.data_ = data_;
      } else {
        result.data_ = dataBuilder_.build();
      }
      if (monsterInfoBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          monsterInfo_ = java.util.Collections.unmodifiableList(monsterInfo_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.monsterInfo_ = monsterInfo_;
      } else {
        result.monsterInfo_ = monsterInfoBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.DragonHomeExploreNotifyDataMsg result) {
      int from_bitField0_ = bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.DragonHomeExploreNotifyDataMsg) {
        return mergeFrom((xddq.pb.DragonHomeExploreNotifyDataMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.DragonHomeExploreNotifyDataMsg other) {
      if (other == xddq.pb.DragonHomeExploreNotifyDataMsg.getDefaultInstance()) return this;
      if (dataBuilder_ == null) {
        if (!other.data_.isEmpty()) {
          if (data_.isEmpty()) {
            data_ = other.data_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureDataIsMutable();
            data_.addAll(other.data_);
          }
          onChanged();
        }
      } else {
        if (!other.data_.isEmpty()) {
          if (dataBuilder_.isEmpty()) {
            dataBuilder_.dispose();
            dataBuilder_ = null;
            data_ = other.data_;
            bitField0_ = (bitField0_ & ~0x00000001);
            dataBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetDataFieldBuilder() : null;
          } else {
            dataBuilder_.addAllMessages(other.data_);
          }
        }
      }
      if (monsterInfoBuilder_ == null) {
        if (!other.monsterInfo_.isEmpty()) {
          if (monsterInfo_.isEmpty()) {
            monsterInfo_ = other.monsterInfo_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureMonsterInfoIsMutable();
            monsterInfo_.addAll(other.monsterInfo_);
          }
          onChanged();
        }
      } else {
        if (!other.monsterInfo_.isEmpty()) {
          if (monsterInfoBuilder_.isEmpty()) {
            monsterInfoBuilder_.dispose();
            monsterInfoBuilder_ = null;
            monsterInfo_ = other.monsterInfo_;
            bitField0_ = (bitField0_ & ~0x00000002);
            monsterInfoBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetMonsterInfoFieldBuilder() : null;
          } else {
            monsterInfoBuilder_.addAllMessages(other.monsterInfo_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      for (int i = 0; i < getDataCount(); i++) {
        if (!getData(i).isInitialized()) {
          return false;
        }
      }
      for (int i = 0; i < getMonsterInfoCount(); i++) {
        if (!getMonsterInfo(i).isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              xddq.pb.MapTile m =
                  input.readMessage(
                      xddq.pb.MapTile.parser(),
                      extensionRegistry);
              if (dataBuilder_ == null) {
                ensureDataIsMutable();
                data_.add(m);
              } else {
                dataBuilder_.addMessage(m);
              }
              break;
            } // case 10
            case 18: {
              xddq.pb.DragonHomeMonsterMsg m =
                  input.readMessage(
                      xddq.pb.DragonHomeMonsterMsg.parser(),
                      extensionRegistry);
              if (monsterInfoBuilder_ == null) {
                ensureMonsterInfoIsMutable();
                monsterInfo_.add(m);
              } else {
                monsterInfoBuilder_.addMessage(m);
              }
              break;
            } // case 18
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.util.List<xddq.pb.MapTile> data_ =
      java.util.Collections.emptyList();
    private void ensureDataIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        data_ = new java.util.ArrayList<xddq.pb.MapTile>(data_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.MapTile, xddq.pb.MapTile.Builder, xddq.pb.MapTileOrBuilder> dataBuilder_;

    /**
     * <code>repeated .xddq.pb.MapTile data = 1;</code>
     */
    public java.util.List<xddq.pb.MapTile> getDataList() {
      if (dataBuilder_ == null) {
        return java.util.Collections.unmodifiableList(data_);
      } else {
        return dataBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.MapTile data = 1;</code>
     */
    public int getDataCount() {
      if (dataBuilder_ == null) {
        return data_.size();
      } else {
        return dataBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.MapTile data = 1;</code>
     */
    public xddq.pb.MapTile getData(int index) {
      if (dataBuilder_ == null) {
        return data_.get(index);
      } else {
        return dataBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.MapTile data = 1;</code>
     */
    public Builder setData(
        int index, xddq.pb.MapTile value) {
      if (dataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDataIsMutable();
        data_.set(index, value);
        onChanged();
      } else {
        dataBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MapTile data = 1;</code>
     */
    public Builder setData(
        int index, xddq.pb.MapTile.Builder builderForValue) {
      if (dataBuilder_ == null) {
        ensureDataIsMutable();
        data_.set(index, builderForValue.build());
        onChanged();
      } else {
        dataBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MapTile data = 1;</code>
     */
    public Builder addData(xddq.pb.MapTile value) {
      if (dataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDataIsMutable();
        data_.add(value);
        onChanged();
      } else {
        dataBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MapTile data = 1;</code>
     */
    public Builder addData(
        int index, xddq.pb.MapTile value) {
      if (dataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDataIsMutable();
        data_.add(index, value);
        onChanged();
      } else {
        dataBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MapTile data = 1;</code>
     */
    public Builder addData(
        xddq.pb.MapTile.Builder builderForValue) {
      if (dataBuilder_ == null) {
        ensureDataIsMutable();
        data_.add(builderForValue.build());
        onChanged();
      } else {
        dataBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MapTile data = 1;</code>
     */
    public Builder addData(
        int index, xddq.pb.MapTile.Builder builderForValue) {
      if (dataBuilder_ == null) {
        ensureDataIsMutable();
        data_.add(index, builderForValue.build());
        onChanged();
      } else {
        dataBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MapTile data = 1;</code>
     */
    public Builder addAllData(
        java.lang.Iterable<? extends xddq.pb.MapTile> values) {
      if (dataBuilder_ == null) {
        ensureDataIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, data_);
        onChanged();
      } else {
        dataBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MapTile data = 1;</code>
     */
    public Builder clearData() {
      if (dataBuilder_ == null) {
        data_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        dataBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MapTile data = 1;</code>
     */
    public Builder removeData(int index) {
      if (dataBuilder_ == null) {
        ensureDataIsMutable();
        data_.remove(index);
        onChanged();
      } else {
        dataBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MapTile data = 1;</code>
     */
    public xddq.pb.MapTile.Builder getDataBuilder(
        int index) {
      return internalGetDataFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.MapTile data = 1;</code>
     */
    public xddq.pb.MapTileOrBuilder getDataOrBuilder(
        int index) {
      if (dataBuilder_ == null) {
        return data_.get(index);  } else {
        return dataBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.MapTile data = 1;</code>
     */
    public java.util.List<? extends xddq.pb.MapTileOrBuilder> 
         getDataOrBuilderList() {
      if (dataBuilder_ != null) {
        return dataBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(data_);
      }
    }
    /**
     * <code>repeated .xddq.pb.MapTile data = 1;</code>
     */
    public xddq.pb.MapTile.Builder addDataBuilder() {
      return internalGetDataFieldBuilder().addBuilder(
          xddq.pb.MapTile.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.MapTile data = 1;</code>
     */
    public xddq.pb.MapTile.Builder addDataBuilder(
        int index) {
      return internalGetDataFieldBuilder().addBuilder(
          index, xddq.pb.MapTile.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.MapTile data = 1;</code>
     */
    public java.util.List<xddq.pb.MapTile.Builder> 
         getDataBuilderList() {
      return internalGetDataFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.MapTile, xddq.pb.MapTile.Builder, xddq.pb.MapTileOrBuilder> 
        internalGetDataFieldBuilder() {
      if (dataBuilder_ == null) {
        dataBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.MapTile, xddq.pb.MapTile.Builder, xddq.pb.MapTileOrBuilder>(
                data_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        data_ = null;
      }
      return dataBuilder_;
    }

    private java.util.List<xddq.pb.DragonHomeMonsterMsg> monsterInfo_ =
      java.util.Collections.emptyList();
    private void ensureMonsterInfoIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        monsterInfo_ = new java.util.ArrayList<xddq.pb.DragonHomeMonsterMsg>(monsterInfo_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.DragonHomeMonsterMsg, xddq.pb.DragonHomeMonsterMsg.Builder, xddq.pb.DragonHomeMonsterMsgOrBuilder> monsterInfoBuilder_;

    /**
     * <code>repeated .xddq.pb.DragonHomeMonsterMsg monsterInfo = 2;</code>
     */
    public java.util.List<xddq.pb.DragonHomeMonsterMsg> getMonsterInfoList() {
      if (monsterInfoBuilder_ == null) {
        return java.util.Collections.unmodifiableList(monsterInfo_);
      } else {
        return monsterInfoBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMonsterMsg monsterInfo = 2;</code>
     */
    public int getMonsterInfoCount() {
      if (monsterInfoBuilder_ == null) {
        return monsterInfo_.size();
      } else {
        return monsterInfoBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMonsterMsg monsterInfo = 2;</code>
     */
    public xddq.pb.DragonHomeMonsterMsg getMonsterInfo(int index) {
      if (monsterInfoBuilder_ == null) {
        return monsterInfo_.get(index);
      } else {
        return monsterInfoBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMonsterMsg monsterInfo = 2;</code>
     */
    public Builder setMonsterInfo(
        int index, xddq.pb.DragonHomeMonsterMsg value) {
      if (monsterInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMonsterInfoIsMutable();
        monsterInfo_.set(index, value);
        onChanged();
      } else {
        monsterInfoBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMonsterMsg monsterInfo = 2;</code>
     */
    public Builder setMonsterInfo(
        int index, xddq.pb.DragonHomeMonsterMsg.Builder builderForValue) {
      if (monsterInfoBuilder_ == null) {
        ensureMonsterInfoIsMutable();
        monsterInfo_.set(index, builderForValue.build());
        onChanged();
      } else {
        monsterInfoBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMonsterMsg monsterInfo = 2;</code>
     */
    public Builder addMonsterInfo(xddq.pb.DragonHomeMonsterMsg value) {
      if (monsterInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMonsterInfoIsMutable();
        monsterInfo_.add(value);
        onChanged();
      } else {
        monsterInfoBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMonsterMsg monsterInfo = 2;</code>
     */
    public Builder addMonsterInfo(
        int index, xddq.pb.DragonHomeMonsterMsg value) {
      if (monsterInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMonsterInfoIsMutable();
        monsterInfo_.add(index, value);
        onChanged();
      } else {
        monsterInfoBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMonsterMsg monsterInfo = 2;</code>
     */
    public Builder addMonsterInfo(
        xddq.pb.DragonHomeMonsterMsg.Builder builderForValue) {
      if (monsterInfoBuilder_ == null) {
        ensureMonsterInfoIsMutable();
        monsterInfo_.add(builderForValue.build());
        onChanged();
      } else {
        monsterInfoBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMonsterMsg monsterInfo = 2;</code>
     */
    public Builder addMonsterInfo(
        int index, xddq.pb.DragonHomeMonsterMsg.Builder builderForValue) {
      if (monsterInfoBuilder_ == null) {
        ensureMonsterInfoIsMutable();
        monsterInfo_.add(index, builderForValue.build());
        onChanged();
      } else {
        monsterInfoBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMonsterMsg monsterInfo = 2;</code>
     */
    public Builder addAllMonsterInfo(
        java.lang.Iterable<? extends xddq.pb.DragonHomeMonsterMsg> values) {
      if (monsterInfoBuilder_ == null) {
        ensureMonsterInfoIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, monsterInfo_);
        onChanged();
      } else {
        monsterInfoBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMonsterMsg monsterInfo = 2;</code>
     */
    public Builder clearMonsterInfo() {
      if (monsterInfoBuilder_ == null) {
        monsterInfo_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        monsterInfoBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMonsterMsg monsterInfo = 2;</code>
     */
    public Builder removeMonsterInfo(int index) {
      if (monsterInfoBuilder_ == null) {
        ensureMonsterInfoIsMutable();
        monsterInfo_.remove(index);
        onChanged();
      } else {
        monsterInfoBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMonsterMsg monsterInfo = 2;</code>
     */
    public xddq.pb.DragonHomeMonsterMsg.Builder getMonsterInfoBuilder(
        int index) {
      return internalGetMonsterInfoFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMonsterMsg monsterInfo = 2;</code>
     */
    public xddq.pb.DragonHomeMonsterMsgOrBuilder getMonsterInfoOrBuilder(
        int index) {
      if (monsterInfoBuilder_ == null) {
        return monsterInfo_.get(index);  } else {
        return monsterInfoBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMonsterMsg monsterInfo = 2;</code>
     */
    public java.util.List<? extends xddq.pb.DragonHomeMonsterMsgOrBuilder> 
         getMonsterInfoOrBuilderList() {
      if (monsterInfoBuilder_ != null) {
        return monsterInfoBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(monsterInfo_);
      }
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMonsterMsg monsterInfo = 2;</code>
     */
    public xddq.pb.DragonHomeMonsterMsg.Builder addMonsterInfoBuilder() {
      return internalGetMonsterInfoFieldBuilder().addBuilder(
          xddq.pb.DragonHomeMonsterMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMonsterMsg monsterInfo = 2;</code>
     */
    public xddq.pb.DragonHomeMonsterMsg.Builder addMonsterInfoBuilder(
        int index) {
      return internalGetMonsterInfoFieldBuilder().addBuilder(
          index, xddq.pb.DragonHomeMonsterMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMonsterMsg monsterInfo = 2;</code>
     */
    public java.util.List<xddq.pb.DragonHomeMonsterMsg.Builder> 
         getMonsterInfoBuilderList() {
      return internalGetMonsterInfoFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.DragonHomeMonsterMsg, xddq.pb.DragonHomeMonsterMsg.Builder, xddq.pb.DragonHomeMonsterMsgOrBuilder> 
        internalGetMonsterInfoFieldBuilder() {
      if (monsterInfoBuilder_ == null) {
        monsterInfoBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.DragonHomeMonsterMsg, xddq.pb.DragonHomeMonsterMsg.Builder, xddq.pb.DragonHomeMonsterMsgOrBuilder>(
                monsterInfo_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        monsterInfo_ = null;
      }
      return monsterInfoBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.DragonHomeExploreNotifyDataMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.DragonHomeExploreNotifyDataMsg)
  private static final xddq.pb.DragonHomeExploreNotifyDataMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.DragonHomeExploreNotifyDataMsg();
  }

  public static xddq.pb.DragonHomeExploreNotifyDataMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<DragonHomeExploreNotifyDataMsg>
      PARSER = new com.google.protobuf.AbstractParser<DragonHomeExploreNotifyDataMsg>() {
    @java.lang.Override
    public DragonHomeExploreNotifyDataMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<DragonHomeExploreNotifyDataMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<DragonHomeExploreNotifyDataMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.DragonHomeExploreNotifyDataMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

