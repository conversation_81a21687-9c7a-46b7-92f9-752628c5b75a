// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.PupilExploreGetSectMsgResp}
 */
public final class PupilExploreGetSectMsgResp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.PupilExploreGetSectMsgResp)
    PupilExploreGetSectMsgRespOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      PupilExploreGetSectMsgResp.class.getName());
  }
  // Use PupilExploreGetSectMsgResp.newBuilder() to construct.
  private PupilExploreGetSectMsgResp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private PupilExploreGetSectMsgResp() {
    msgList_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_PupilExploreGetSectMsgResp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_PupilExploreGetSectMsgResp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.PupilExploreGetSectMsgResp.class, xddq.pb.PupilExploreGetSectMsgResp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>optional int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int MSGLIST_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.PupilExploreSectMsg> msgList_;
  /**
   * <code>repeated .xddq.pb.PupilExploreSectMsg msgList = 2;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.PupilExploreSectMsg> getMsgListList() {
    return msgList_;
  }
  /**
   * <code>repeated .xddq.pb.PupilExploreSectMsg msgList = 2;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.PupilExploreSectMsgOrBuilder> 
      getMsgListOrBuilderList() {
    return msgList_;
  }
  /**
   * <code>repeated .xddq.pb.PupilExploreSectMsg msgList = 2;</code>
   */
  @java.lang.Override
  public int getMsgListCount() {
    return msgList_.size();
  }
  /**
   * <code>repeated .xddq.pb.PupilExploreSectMsg msgList = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.PupilExploreSectMsg getMsgList(int index) {
    return msgList_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.PupilExploreSectMsg msgList = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.PupilExploreSectMsgOrBuilder getMsgListOrBuilder(
      int index) {
    return msgList_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    for (int i = 0; i < msgList_.size(); i++) {
      output.writeMessage(2, msgList_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    for (int i = 0; i < msgList_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, msgList_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.PupilExploreGetSectMsgResp)) {
      return super.equals(obj);
    }
    xddq.pb.PupilExploreGetSectMsgResp other = (xddq.pb.PupilExploreGetSectMsgResp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (!getMsgListList()
        .equals(other.getMsgListList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (getMsgListCount() > 0) {
      hash = (37 * hash) + MSGLIST_FIELD_NUMBER;
      hash = (53 * hash) + getMsgListList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.PupilExploreGetSectMsgResp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PupilExploreGetSectMsgResp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PupilExploreGetSectMsgResp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PupilExploreGetSectMsgResp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PupilExploreGetSectMsgResp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PupilExploreGetSectMsgResp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PupilExploreGetSectMsgResp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.PupilExploreGetSectMsgResp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.PupilExploreGetSectMsgResp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.PupilExploreGetSectMsgResp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.PupilExploreGetSectMsgResp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.PupilExploreGetSectMsgResp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.PupilExploreGetSectMsgResp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.PupilExploreGetSectMsgResp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.PupilExploreGetSectMsgResp)
      xddq.pb.PupilExploreGetSectMsgRespOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PupilExploreGetSectMsgResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PupilExploreGetSectMsgResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.PupilExploreGetSectMsgResp.class, xddq.pb.PupilExploreGetSectMsgResp.Builder.class);
    }

    // Construct using xddq.pb.PupilExploreGetSectMsgResp.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      if (msgListBuilder_ == null) {
        msgList_ = java.util.Collections.emptyList();
      } else {
        msgList_ = null;
        msgListBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000002);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PupilExploreGetSectMsgResp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.PupilExploreGetSectMsgResp getDefaultInstanceForType() {
      return xddq.pb.PupilExploreGetSectMsgResp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.PupilExploreGetSectMsgResp build() {
      xddq.pb.PupilExploreGetSectMsgResp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.PupilExploreGetSectMsgResp buildPartial() {
      xddq.pb.PupilExploreGetSectMsgResp result = new xddq.pb.PupilExploreGetSectMsgResp(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.PupilExploreGetSectMsgResp result) {
      if (msgListBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          msgList_ = java.util.Collections.unmodifiableList(msgList_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.msgList_ = msgList_;
      } else {
        result.msgList_ = msgListBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.PupilExploreGetSectMsgResp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.PupilExploreGetSectMsgResp) {
        return mergeFrom((xddq.pb.PupilExploreGetSectMsgResp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.PupilExploreGetSectMsgResp other) {
      if (other == xddq.pb.PupilExploreGetSectMsgResp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (msgListBuilder_ == null) {
        if (!other.msgList_.isEmpty()) {
          if (msgList_.isEmpty()) {
            msgList_ = other.msgList_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureMsgListIsMutable();
            msgList_.addAll(other.msgList_);
          }
          onChanged();
        }
      } else {
        if (!other.msgList_.isEmpty()) {
          if (msgListBuilder_.isEmpty()) {
            msgListBuilder_.dispose();
            msgListBuilder_ = null;
            msgList_ = other.msgList_;
            bitField0_ = (bitField0_ & ~0x00000002);
            msgListBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetMsgListFieldBuilder() : null;
          } else {
            msgListBuilder_.addAllMessages(other.msgList_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              xddq.pb.PupilExploreSectMsg m =
                  input.readMessage(
                      xddq.pb.PupilExploreSectMsg.parser(),
                      extensionRegistry);
              if (msgListBuilder_ == null) {
                ensureMsgListIsMutable();
                msgList_.add(m);
              } else {
                msgListBuilder_.addMessage(m);
              }
              break;
            } // case 18
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>optional int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>optional int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.PupilExploreSectMsg> msgList_ =
      java.util.Collections.emptyList();
    private void ensureMsgListIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        msgList_ = new java.util.ArrayList<xddq.pb.PupilExploreSectMsg>(msgList_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.PupilExploreSectMsg, xddq.pb.PupilExploreSectMsg.Builder, xddq.pb.PupilExploreSectMsgOrBuilder> msgListBuilder_;

    /**
     * <code>repeated .xddq.pb.PupilExploreSectMsg msgList = 2;</code>
     */
    public java.util.List<xddq.pb.PupilExploreSectMsg> getMsgListList() {
      if (msgListBuilder_ == null) {
        return java.util.Collections.unmodifiableList(msgList_);
      } else {
        return msgListBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.PupilExploreSectMsg msgList = 2;</code>
     */
    public int getMsgListCount() {
      if (msgListBuilder_ == null) {
        return msgList_.size();
      } else {
        return msgListBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.PupilExploreSectMsg msgList = 2;</code>
     */
    public xddq.pb.PupilExploreSectMsg getMsgList(int index) {
      if (msgListBuilder_ == null) {
        return msgList_.get(index);
      } else {
        return msgListBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.PupilExploreSectMsg msgList = 2;</code>
     */
    public Builder setMsgList(
        int index, xddq.pb.PupilExploreSectMsg value) {
      if (msgListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMsgListIsMutable();
        msgList_.set(index, value);
        onChanged();
      } else {
        msgListBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PupilExploreSectMsg msgList = 2;</code>
     */
    public Builder setMsgList(
        int index, xddq.pb.PupilExploreSectMsg.Builder builderForValue) {
      if (msgListBuilder_ == null) {
        ensureMsgListIsMutable();
        msgList_.set(index, builderForValue.build());
        onChanged();
      } else {
        msgListBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PupilExploreSectMsg msgList = 2;</code>
     */
    public Builder addMsgList(xddq.pb.PupilExploreSectMsg value) {
      if (msgListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMsgListIsMutable();
        msgList_.add(value);
        onChanged();
      } else {
        msgListBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PupilExploreSectMsg msgList = 2;</code>
     */
    public Builder addMsgList(
        int index, xddq.pb.PupilExploreSectMsg value) {
      if (msgListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMsgListIsMutable();
        msgList_.add(index, value);
        onChanged();
      } else {
        msgListBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PupilExploreSectMsg msgList = 2;</code>
     */
    public Builder addMsgList(
        xddq.pb.PupilExploreSectMsg.Builder builderForValue) {
      if (msgListBuilder_ == null) {
        ensureMsgListIsMutable();
        msgList_.add(builderForValue.build());
        onChanged();
      } else {
        msgListBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PupilExploreSectMsg msgList = 2;</code>
     */
    public Builder addMsgList(
        int index, xddq.pb.PupilExploreSectMsg.Builder builderForValue) {
      if (msgListBuilder_ == null) {
        ensureMsgListIsMutable();
        msgList_.add(index, builderForValue.build());
        onChanged();
      } else {
        msgListBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PupilExploreSectMsg msgList = 2;</code>
     */
    public Builder addAllMsgList(
        java.lang.Iterable<? extends xddq.pb.PupilExploreSectMsg> values) {
      if (msgListBuilder_ == null) {
        ensureMsgListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, msgList_);
        onChanged();
      } else {
        msgListBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PupilExploreSectMsg msgList = 2;</code>
     */
    public Builder clearMsgList() {
      if (msgListBuilder_ == null) {
        msgList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        msgListBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PupilExploreSectMsg msgList = 2;</code>
     */
    public Builder removeMsgList(int index) {
      if (msgListBuilder_ == null) {
        ensureMsgListIsMutable();
        msgList_.remove(index);
        onChanged();
      } else {
        msgListBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PupilExploreSectMsg msgList = 2;</code>
     */
    public xddq.pb.PupilExploreSectMsg.Builder getMsgListBuilder(
        int index) {
      return internalGetMsgListFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.PupilExploreSectMsg msgList = 2;</code>
     */
    public xddq.pb.PupilExploreSectMsgOrBuilder getMsgListOrBuilder(
        int index) {
      if (msgListBuilder_ == null) {
        return msgList_.get(index);  } else {
        return msgListBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.PupilExploreSectMsg msgList = 2;</code>
     */
    public java.util.List<? extends xddq.pb.PupilExploreSectMsgOrBuilder> 
         getMsgListOrBuilderList() {
      if (msgListBuilder_ != null) {
        return msgListBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(msgList_);
      }
    }
    /**
     * <code>repeated .xddq.pb.PupilExploreSectMsg msgList = 2;</code>
     */
    public xddq.pb.PupilExploreSectMsg.Builder addMsgListBuilder() {
      return internalGetMsgListFieldBuilder().addBuilder(
          xddq.pb.PupilExploreSectMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.PupilExploreSectMsg msgList = 2;</code>
     */
    public xddq.pb.PupilExploreSectMsg.Builder addMsgListBuilder(
        int index) {
      return internalGetMsgListFieldBuilder().addBuilder(
          index, xddq.pb.PupilExploreSectMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.PupilExploreSectMsg msgList = 2;</code>
     */
    public java.util.List<xddq.pb.PupilExploreSectMsg.Builder> 
         getMsgListBuilderList() {
      return internalGetMsgListFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.PupilExploreSectMsg, xddq.pb.PupilExploreSectMsg.Builder, xddq.pb.PupilExploreSectMsgOrBuilder> 
        internalGetMsgListFieldBuilder() {
      if (msgListBuilder_ == null) {
        msgListBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.PupilExploreSectMsg, xddq.pb.PupilExploreSectMsg.Builder, xddq.pb.PupilExploreSectMsgOrBuilder>(
                msgList_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        msgList_ = null;
      }
      return msgListBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.PupilExploreGetSectMsgResp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.PupilExploreGetSectMsgResp)
  private static final xddq.pb.PupilExploreGetSectMsgResp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.PupilExploreGetSectMsgResp();
  }

  public static xddq.pb.PupilExploreGetSectMsgResp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<PupilExploreGetSectMsgResp>
      PARSER = new com.google.protobuf.AbstractParser<PupilExploreGetSectMsgResp>() {
    @java.lang.Override
    public PupilExploreGetSectMsgResp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<PupilExploreGetSectMsgResp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<PupilExploreGetSectMsgResp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.PupilExploreGetSectMsgResp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

