// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.CloudSoulUnlockResp}
 */
public final class CloudSoulUnlockResp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.CloudSoulUnlockResp)
    CloudSoulUnlockRespOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      CloudSoulUnlockResp.class.getName());
  }
  // Use CloudSoulUnlockResp.newBuilder() to construct.
  private CloudSoulUnlockResp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private CloudSoulUnlockResp() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_CloudSoulUnlockResp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_CloudSoulUnlockResp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.CloudSoulUnlockResp.class, xddq.pb.CloudSoulUnlockResp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int CLOUDSOULMSG_FIELD_NUMBER = 2;
  private xddq.pb.CloudSoulMsg cloudSoulMsg_;
  /**
   * <code>optional .xddq.pb.CloudSoulMsg cloudSoulMsg = 2;</code>
   * @return Whether the cloudSoulMsg field is set.
   */
  @java.lang.Override
  public boolean hasCloudSoulMsg() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional .xddq.pb.CloudSoulMsg cloudSoulMsg = 2;</code>
   * @return The cloudSoulMsg.
   */
  @java.lang.Override
  public xddq.pb.CloudSoulMsg getCloudSoulMsg() {
    return cloudSoulMsg_ == null ? xddq.pb.CloudSoulMsg.getDefaultInstance() : cloudSoulMsg_;
  }
  /**
   * <code>optional .xddq.pb.CloudSoulMsg cloudSoulMsg = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.CloudSoulMsgOrBuilder getCloudSoulMsgOrBuilder() {
    return cloudSoulMsg_ == null ? xddq.pb.CloudSoulMsg.getDefaultInstance() : cloudSoulMsg_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (hasCloudSoulMsg()) {
      if (!getCloudSoulMsg().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeMessage(2, getCloudSoulMsg());
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getCloudSoulMsg());
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.CloudSoulUnlockResp)) {
      return super.equals(obj);
    }
    xddq.pb.CloudSoulUnlockResp other = (xddq.pb.CloudSoulUnlockResp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (hasCloudSoulMsg() != other.hasCloudSoulMsg()) return false;
    if (hasCloudSoulMsg()) {
      if (!getCloudSoulMsg()
          .equals(other.getCloudSoulMsg())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (hasCloudSoulMsg()) {
      hash = (37 * hash) + CLOUDSOULMSG_FIELD_NUMBER;
      hash = (53 * hash) + getCloudSoulMsg().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.CloudSoulUnlockResp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.CloudSoulUnlockResp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.CloudSoulUnlockResp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.CloudSoulUnlockResp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.CloudSoulUnlockResp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.CloudSoulUnlockResp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.CloudSoulUnlockResp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.CloudSoulUnlockResp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.CloudSoulUnlockResp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.CloudSoulUnlockResp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.CloudSoulUnlockResp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.CloudSoulUnlockResp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.CloudSoulUnlockResp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.CloudSoulUnlockResp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.CloudSoulUnlockResp)
      xddq.pb.CloudSoulUnlockRespOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_CloudSoulUnlockResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_CloudSoulUnlockResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.CloudSoulUnlockResp.class, xddq.pb.CloudSoulUnlockResp.Builder.class);
    }

    // Construct using xddq.pb.CloudSoulUnlockResp.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetCloudSoulMsgFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      cloudSoulMsg_ = null;
      if (cloudSoulMsgBuilder_ != null) {
        cloudSoulMsgBuilder_.dispose();
        cloudSoulMsgBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_CloudSoulUnlockResp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.CloudSoulUnlockResp getDefaultInstanceForType() {
      return xddq.pb.CloudSoulUnlockResp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.CloudSoulUnlockResp build() {
      xddq.pb.CloudSoulUnlockResp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.CloudSoulUnlockResp buildPartial() {
      xddq.pb.CloudSoulUnlockResp result = new xddq.pb.CloudSoulUnlockResp(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.CloudSoulUnlockResp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.cloudSoulMsg_ = cloudSoulMsgBuilder_ == null
            ? cloudSoulMsg_
            : cloudSoulMsgBuilder_.build();
        to_bitField0_ |= 0x00000002;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.CloudSoulUnlockResp) {
        return mergeFrom((xddq.pb.CloudSoulUnlockResp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.CloudSoulUnlockResp other) {
      if (other == xddq.pb.CloudSoulUnlockResp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (other.hasCloudSoulMsg()) {
        mergeCloudSoulMsg(other.getCloudSoulMsg());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      if (hasCloudSoulMsg()) {
        if (!getCloudSoulMsg().isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              input.readMessage(
                  internalGetCloudSoulMsgFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private xddq.pb.CloudSoulMsg cloudSoulMsg_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.CloudSoulMsg, xddq.pb.CloudSoulMsg.Builder, xddq.pb.CloudSoulMsgOrBuilder> cloudSoulMsgBuilder_;
    /**
     * <code>optional .xddq.pb.CloudSoulMsg cloudSoulMsg = 2;</code>
     * @return Whether the cloudSoulMsg field is set.
     */
    public boolean hasCloudSoulMsg() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .xddq.pb.CloudSoulMsg cloudSoulMsg = 2;</code>
     * @return The cloudSoulMsg.
     */
    public xddq.pb.CloudSoulMsg getCloudSoulMsg() {
      if (cloudSoulMsgBuilder_ == null) {
        return cloudSoulMsg_ == null ? xddq.pb.CloudSoulMsg.getDefaultInstance() : cloudSoulMsg_;
      } else {
        return cloudSoulMsgBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.CloudSoulMsg cloudSoulMsg = 2;</code>
     */
    public Builder setCloudSoulMsg(xddq.pb.CloudSoulMsg value) {
      if (cloudSoulMsgBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        cloudSoulMsg_ = value;
      } else {
        cloudSoulMsgBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.CloudSoulMsg cloudSoulMsg = 2;</code>
     */
    public Builder setCloudSoulMsg(
        xddq.pb.CloudSoulMsg.Builder builderForValue) {
      if (cloudSoulMsgBuilder_ == null) {
        cloudSoulMsg_ = builderForValue.build();
      } else {
        cloudSoulMsgBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.CloudSoulMsg cloudSoulMsg = 2;</code>
     */
    public Builder mergeCloudSoulMsg(xddq.pb.CloudSoulMsg value) {
      if (cloudSoulMsgBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0) &&
          cloudSoulMsg_ != null &&
          cloudSoulMsg_ != xddq.pb.CloudSoulMsg.getDefaultInstance()) {
          getCloudSoulMsgBuilder().mergeFrom(value);
        } else {
          cloudSoulMsg_ = value;
        }
      } else {
        cloudSoulMsgBuilder_.mergeFrom(value);
      }
      if (cloudSoulMsg_ != null) {
        bitField0_ |= 0x00000002;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.CloudSoulMsg cloudSoulMsg = 2;</code>
     */
    public Builder clearCloudSoulMsg() {
      bitField0_ = (bitField0_ & ~0x00000002);
      cloudSoulMsg_ = null;
      if (cloudSoulMsgBuilder_ != null) {
        cloudSoulMsgBuilder_.dispose();
        cloudSoulMsgBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.CloudSoulMsg cloudSoulMsg = 2;</code>
     */
    public xddq.pb.CloudSoulMsg.Builder getCloudSoulMsgBuilder() {
      bitField0_ |= 0x00000002;
      onChanged();
      return internalGetCloudSoulMsgFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.CloudSoulMsg cloudSoulMsg = 2;</code>
     */
    public xddq.pb.CloudSoulMsgOrBuilder getCloudSoulMsgOrBuilder() {
      if (cloudSoulMsgBuilder_ != null) {
        return cloudSoulMsgBuilder_.getMessageOrBuilder();
      } else {
        return cloudSoulMsg_ == null ?
            xddq.pb.CloudSoulMsg.getDefaultInstance() : cloudSoulMsg_;
      }
    }
    /**
     * <code>optional .xddq.pb.CloudSoulMsg cloudSoulMsg = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.CloudSoulMsg, xddq.pb.CloudSoulMsg.Builder, xddq.pb.CloudSoulMsgOrBuilder> 
        internalGetCloudSoulMsgFieldBuilder() {
      if (cloudSoulMsgBuilder_ == null) {
        cloudSoulMsgBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.CloudSoulMsg, xddq.pb.CloudSoulMsg.Builder, xddq.pb.CloudSoulMsgOrBuilder>(
                getCloudSoulMsg(),
                getParentForChildren(),
                isClean());
        cloudSoulMsg_ = null;
      }
      return cloudSoulMsgBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.CloudSoulUnlockResp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.CloudSoulUnlockResp)
  private static final xddq.pb.CloudSoulUnlockResp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.CloudSoulUnlockResp();
  }

  public static xddq.pb.CloudSoulUnlockResp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<CloudSoulUnlockResp>
      PARSER = new com.google.protobuf.AbstractParser<CloudSoulUnlockResp>() {
    @java.lang.Override
    public CloudSoulUnlockResp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<CloudSoulUnlockResp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<CloudSoulUnlockResp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.CloudSoulUnlockResp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

