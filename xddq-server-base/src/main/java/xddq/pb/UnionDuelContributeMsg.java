// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.UnionDuelContributeMsg}
 */
public final class UnionDuelContributeMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.UnionDuelContributeMsg)
    UnionDuelContributeMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      UnionDuelContributeMsg.class.getName());
  }
  // Use UnionDuelContributeMsg.newBuilder() to construct.
  private UnionDuelContributeMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private UnionDuelContributeMsg() {
    nickName_ = "";
    wxHeadUrl_ = "";
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_UnionDuelContributeMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_UnionDuelContributeMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.UnionDuelContributeMsg.class, xddq.pb.UnionDuelContributeMsg.Builder.class);
  }

  private int bitField0_;
  public static final int PLAYERID_FIELD_NUMBER = 1;
  private long playerId_ = 0L;
  /**
   * <code>optional int64 playerId = 1;</code>
   * @return Whether the playerId field is set.
   */
  @java.lang.Override
  public boolean hasPlayerId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int64 playerId = 1;</code>
   * @return The playerId.
   */
  @java.lang.Override
  public long getPlayerId() {
    return playerId_;
  }

  public static final int SERVERID_FIELD_NUMBER = 2;
  private long serverId_ = 0L;
  /**
   * <code>optional int64 serverId = 2;</code>
   * @return Whether the serverId field is set.
   */
  @java.lang.Override
  public boolean hasServerId() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int64 serverId = 2;</code>
   * @return The serverId.
   */
  @java.lang.Override
  public long getServerId() {
    return serverId_;
  }

  public static final int NICKNAME_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private volatile java.lang.Object nickName_ = "";
  /**
   * <code>optional string nickName = 3;</code>
   * @return Whether the nickName field is set.
   */
  @java.lang.Override
  public boolean hasNickName() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional string nickName = 3;</code>
   * @return The nickName.
   */
  @java.lang.Override
  public java.lang.String getNickName() {
    java.lang.Object ref = nickName_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        nickName_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string nickName = 3;</code>
   * @return The bytes for nickName.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getNickNameBytes() {
    java.lang.Object ref = nickName_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      nickName_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int REALMSID_FIELD_NUMBER = 4;
  private int realmsId_ = 0;
  /**
   * <code>optional int32 realmsId = 4;</code>
   * @return Whether the realmsId field is set.
   */
  @java.lang.Override
  public boolean hasRealmsId() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional int32 realmsId = 4;</code>
   * @return The realmsId.
   */
  @java.lang.Override
  public int getRealmsId() {
    return realmsId_;
  }

  public static final int TITLEID_FIELD_NUMBER = 5;
  private int titleId_ = 0;
  /**
   * <code>optional int32 titleId = 5;</code>
   * @return Whether the titleId field is set.
   */
  @java.lang.Override
  public boolean hasTitleId() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>optional int32 titleId = 5;</code>
   * @return The titleId.
   */
  @java.lang.Override
  public int getTitleId() {
    return titleId_;
  }

  public static final int HEADICON_FIELD_NUMBER = 6;
  private int headIcon_ = 0;
  /**
   * <code>optional int32 headIcon = 6;</code>
   * @return Whether the headIcon field is set.
   */
  @java.lang.Override
  public boolean hasHeadIcon() {
    return ((bitField0_ & 0x00000020) != 0);
  }
  /**
   * <code>optional int32 headIcon = 6;</code>
   * @return The headIcon.
   */
  @java.lang.Override
  public int getHeadIcon() {
    return headIcon_;
  }

  public static final int WXHEADURL_FIELD_NUMBER = 7;
  @SuppressWarnings("serial")
  private volatile java.lang.Object wxHeadUrl_ = "";
  /**
   * <code>optional string wxHeadUrl = 7;</code>
   * @return Whether the wxHeadUrl field is set.
   */
  @java.lang.Override
  public boolean hasWxHeadUrl() {
    return ((bitField0_ & 0x00000040) != 0);
  }
  /**
   * <code>optional string wxHeadUrl = 7;</code>
   * @return The wxHeadUrl.
   */
  @java.lang.Override
  public java.lang.String getWxHeadUrl() {
    java.lang.Object ref = wxHeadUrl_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        wxHeadUrl_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string wxHeadUrl = 7;</code>
   * @return The bytes for wxHeadUrl.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getWxHeadUrlBytes() {
    java.lang.Object ref = wxHeadUrl_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      wxHeadUrl_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int EQUIPHEADICONFRAME_FIELD_NUMBER = 8;
  private int equipHeadIconFrame_ = 0;
  /**
   * <code>optional int32 equipHeadIconFrame = 8;</code>
   * @return Whether the equipHeadIconFrame field is set.
   */
  @java.lang.Override
  public boolean hasEquipHeadIconFrame() {
    return ((bitField0_ & 0x00000080) != 0);
  }
  /**
   * <code>optional int32 equipHeadIconFrame = 8;</code>
   * @return The equipHeadIconFrame.
   */
  @java.lang.Override
  public int getEquipHeadIconFrame() {
    return equipHeadIconFrame_;
  }

  public static final int TIMES_FIELD_NUMBER = 9;
  private int times_ = 0;
  /**
   * <code>optional int32 Times = 9;</code>
   * @return Whether the times field is set.
   */
  @java.lang.Override
  public boolean hasTimes() {
    return ((bitField0_ & 0x00000100) != 0);
  }
  /**
   * <code>optional int32 Times = 9;</code>
   * @return The times.
   */
  @java.lang.Override
  public int getTimes() {
    return times_;
  }

  public static final int STAR_FIELD_NUMBER = 10;
  private int star_ = 0;
  /**
   * <code>optional int32 star = 10;</code>
   * @return Whether the star field is set.
   */
  @java.lang.Override
  public boolean hasStar() {
    return ((bitField0_ & 0x00000200) != 0);
  }
  /**
   * <code>optional int32 star = 10;</code>
   * @return The star.
   */
  @java.lang.Override
  public int getStar() {
    return star_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt64(1, playerId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt64(2, serverId_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 3, nickName_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt32(4, realmsId_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      output.writeInt32(5, titleId_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      output.writeInt32(6, headIcon_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 7, wxHeadUrl_);
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      output.writeInt32(8, equipHeadIconFrame_);
    }
    if (((bitField0_ & 0x00000100) != 0)) {
      output.writeInt32(9, times_);
    }
    if (((bitField0_ & 0x00000200) != 0)) {
      output.writeInt32(10, star_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(1, playerId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(2, serverId_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(3, nickName_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(4, realmsId_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(5, titleId_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(6, headIcon_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(7, wxHeadUrl_);
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(8, equipHeadIconFrame_);
    }
    if (((bitField0_ & 0x00000100) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(9, times_);
    }
    if (((bitField0_ & 0x00000200) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(10, star_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.UnionDuelContributeMsg)) {
      return super.equals(obj);
    }
    xddq.pb.UnionDuelContributeMsg other = (xddq.pb.UnionDuelContributeMsg) obj;

    if (hasPlayerId() != other.hasPlayerId()) return false;
    if (hasPlayerId()) {
      if (getPlayerId()
          != other.getPlayerId()) return false;
    }
    if (hasServerId() != other.hasServerId()) return false;
    if (hasServerId()) {
      if (getServerId()
          != other.getServerId()) return false;
    }
    if (hasNickName() != other.hasNickName()) return false;
    if (hasNickName()) {
      if (!getNickName()
          .equals(other.getNickName())) return false;
    }
    if (hasRealmsId() != other.hasRealmsId()) return false;
    if (hasRealmsId()) {
      if (getRealmsId()
          != other.getRealmsId()) return false;
    }
    if (hasTitleId() != other.hasTitleId()) return false;
    if (hasTitleId()) {
      if (getTitleId()
          != other.getTitleId()) return false;
    }
    if (hasHeadIcon() != other.hasHeadIcon()) return false;
    if (hasHeadIcon()) {
      if (getHeadIcon()
          != other.getHeadIcon()) return false;
    }
    if (hasWxHeadUrl() != other.hasWxHeadUrl()) return false;
    if (hasWxHeadUrl()) {
      if (!getWxHeadUrl()
          .equals(other.getWxHeadUrl())) return false;
    }
    if (hasEquipHeadIconFrame() != other.hasEquipHeadIconFrame()) return false;
    if (hasEquipHeadIconFrame()) {
      if (getEquipHeadIconFrame()
          != other.getEquipHeadIconFrame()) return false;
    }
    if (hasTimes() != other.hasTimes()) return false;
    if (hasTimes()) {
      if (getTimes()
          != other.getTimes()) return false;
    }
    if (hasStar() != other.hasStar()) return false;
    if (hasStar()) {
      if (getStar()
          != other.getStar()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasPlayerId()) {
      hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getPlayerId());
    }
    if (hasServerId()) {
      hash = (37 * hash) + SERVERID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getServerId());
    }
    if (hasNickName()) {
      hash = (37 * hash) + NICKNAME_FIELD_NUMBER;
      hash = (53 * hash) + getNickName().hashCode();
    }
    if (hasRealmsId()) {
      hash = (37 * hash) + REALMSID_FIELD_NUMBER;
      hash = (53 * hash) + getRealmsId();
    }
    if (hasTitleId()) {
      hash = (37 * hash) + TITLEID_FIELD_NUMBER;
      hash = (53 * hash) + getTitleId();
    }
    if (hasHeadIcon()) {
      hash = (37 * hash) + HEADICON_FIELD_NUMBER;
      hash = (53 * hash) + getHeadIcon();
    }
    if (hasWxHeadUrl()) {
      hash = (37 * hash) + WXHEADURL_FIELD_NUMBER;
      hash = (53 * hash) + getWxHeadUrl().hashCode();
    }
    if (hasEquipHeadIconFrame()) {
      hash = (37 * hash) + EQUIPHEADICONFRAME_FIELD_NUMBER;
      hash = (53 * hash) + getEquipHeadIconFrame();
    }
    if (hasTimes()) {
      hash = (37 * hash) + TIMES_FIELD_NUMBER;
      hash = (53 * hash) + getTimes();
    }
    if (hasStar()) {
      hash = (37 * hash) + STAR_FIELD_NUMBER;
      hash = (53 * hash) + getStar();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.UnionDuelContributeMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.UnionDuelContributeMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.UnionDuelContributeMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.UnionDuelContributeMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.UnionDuelContributeMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.UnionDuelContributeMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.UnionDuelContributeMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.UnionDuelContributeMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.UnionDuelContributeMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.UnionDuelContributeMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.UnionDuelContributeMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.UnionDuelContributeMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.UnionDuelContributeMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.UnionDuelContributeMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.UnionDuelContributeMsg)
      xddq.pb.UnionDuelContributeMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_UnionDuelContributeMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_UnionDuelContributeMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.UnionDuelContributeMsg.class, xddq.pb.UnionDuelContributeMsg.Builder.class);
    }

    // Construct using xddq.pb.UnionDuelContributeMsg.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      playerId_ = 0L;
      serverId_ = 0L;
      nickName_ = "";
      realmsId_ = 0;
      titleId_ = 0;
      headIcon_ = 0;
      wxHeadUrl_ = "";
      equipHeadIconFrame_ = 0;
      times_ = 0;
      star_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_UnionDuelContributeMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.UnionDuelContributeMsg getDefaultInstanceForType() {
      return xddq.pb.UnionDuelContributeMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.UnionDuelContributeMsg build() {
      xddq.pb.UnionDuelContributeMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.UnionDuelContributeMsg buildPartial() {
      xddq.pb.UnionDuelContributeMsg result = new xddq.pb.UnionDuelContributeMsg(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.UnionDuelContributeMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.playerId_ = playerId_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.serverId_ = serverId_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.nickName_ = nickName_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.realmsId_ = realmsId_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.titleId_ = titleId_;
        to_bitField0_ |= 0x00000010;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.headIcon_ = headIcon_;
        to_bitField0_ |= 0x00000020;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.wxHeadUrl_ = wxHeadUrl_;
        to_bitField0_ |= 0x00000040;
      }
      if (((from_bitField0_ & 0x00000080) != 0)) {
        result.equipHeadIconFrame_ = equipHeadIconFrame_;
        to_bitField0_ |= 0x00000080;
      }
      if (((from_bitField0_ & 0x00000100) != 0)) {
        result.times_ = times_;
        to_bitField0_ |= 0x00000100;
      }
      if (((from_bitField0_ & 0x00000200) != 0)) {
        result.star_ = star_;
        to_bitField0_ |= 0x00000200;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.UnionDuelContributeMsg) {
        return mergeFrom((xddq.pb.UnionDuelContributeMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.UnionDuelContributeMsg other) {
      if (other == xddq.pb.UnionDuelContributeMsg.getDefaultInstance()) return this;
      if (other.hasPlayerId()) {
        setPlayerId(other.getPlayerId());
      }
      if (other.hasServerId()) {
        setServerId(other.getServerId());
      }
      if (other.hasNickName()) {
        nickName_ = other.nickName_;
        bitField0_ |= 0x00000004;
        onChanged();
      }
      if (other.hasRealmsId()) {
        setRealmsId(other.getRealmsId());
      }
      if (other.hasTitleId()) {
        setTitleId(other.getTitleId());
      }
      if (other.hasHeadIcon()) {
        setHeadIcon(other.getHeadIcon());
      }
      if (other.hasWxHeadUrl()) {
        wxHeadUrl_ = other.wxHeadUrl_;
        bitField0_ |= 0x00000040;
        onChanged();
      }
      if (other.hasEquipHeadIconFrame()) {
        setEquipHeadIconFrame(other.getEquipHeadIconFrame());
      }
      if (other.hasTimes()) {
        setTimes(other.getTimes());
      }
      if (other.hasStar()) {
        setStar(other.getStar());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              playerId_ = input.readInt64();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              serverId_ = input.readInt64();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 26: {
              nickName_ = input.readBytes();
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 32: {
              realmsId_ = input.readInt32();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            case 40: {
              titleId_ = input.readInt32();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            case 48: {
              headIcon_ = input.readInt32();
              bitField0_ |= 0x00000020;
              break;
            } // case 48
            case 58: {
              wxHeadUrl_ = input.readBytes();
              bitField0_ |= 0x00000040;
              break;
            } // case 58
            case 64: {
              equipHeadIconFrame_ = input.readInt32();
              bitField0_ |= 0x00000080;
              break;
            } // case 64
            case 72: {
              times_ = input.readInt32();
              bitField0_ |= 0x00000100;
              break;
            } // case 72
            case 80: {
              star_ = input.readInt32();
              bitField0_ |= 0x00000200;
              break;
            } // case 80
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private long playerId_ ;
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }
    /**
     * <code>optional int64 playerId = 1;</code>
     * @param value The playerId to set.
     * @return This builder for chaining.
     */
    public Builder setPlayerId(long value) {

      playerId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearPlayerId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      playerId_ = 0L;
      onChanged();
      return this;
    }

    private long serverId_ ;
    /**
     * <code>optional int64 serverId = 2;</code>
     * @return Whether the serverId field is set.
     */
    @java.lang.Override
    public boolean hasServerId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int64 serverId = 2;</code>
     * @return The serverId.
     */
    @java.lang.Override
    public long getServerId() {
      return serverId_;
    }
    /**
     * <code>optional int64 serverId = 2;</code>
     * @param value The serverId to set.
     * @return This builder for chaining.
     */
    public Builder setServerId(long value) {

      serverId_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 serverId = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearServerId() {
      bitField0_ = (bitField0_ & ~0x00000002);
      serverId_ = 0L;
      onChanged();
      return this;
    }

    private java.lang.Object nickName_ = "";
    /**
     * <code>optional string nickName = 3;</code>
     * @return Whether the nickName field is set.
     */
    public boolean hasNickName() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional string nickName = 3;</code>
     * @return The nickName.
     */
    public java.lang.String getNickName() {
      java.lang.Object ref = nickName_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          nickName_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string nickName = 3;</code>
     * @return The bytes for nickName.
     */
    public com.google.protobuf.ByteString
        getNickNameBytes() {
      java.lang.Object ref = nickName_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        nickName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string nickName = 3;</code>
     * @param value The nickName to set.
     * @return This builder for chaining.
     */
    public Builder setNickName(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      nickName_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional string nickName = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearNickName() {
      nickName_ = getDefaultInstance().getNickName();
      bitField0_ = (bitField0_ & ~0x00000004);
      onChanged();
      return this;
    }
    /**
     * <code>optional string nickName = 3;</code>
     * @param value The bytes for nickName to set.
     * @return This builder for chaining.
     */
    public Builder setNickNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      nickName_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }

    private int realmsId_ ;
    /**
     * <code>optional int32 realmsId = 4;</code>
     * @return Whether the realmsId field is set.
     */
    @java.lang.Override
    public boolean hasRealmsId() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int32 realmsId = 4;</code>
     * @return The realmsId.
     */
    @java.lang.Override
    public int getRealmsId() {
      return realmsId_;
    }
    /**
     * <code>optional int32 realmsId = 4;</code>
     * @param value The realmsId to set.
     * @return This builder for chaining.
     */
    public Builder setRealmsId(int value) {

      realmsId_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 realmsId = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearRealmsId() {
      bitField0_ = (bitField0_ & ~0x00000008);
      realmsId_ = 0;
      onChanged();
      return this;
    }

    private int titleId_ ;
    /**
     * <code>optional int32 titleId = 5;</code>
     * @return Whether the titleId field is set.
     */
    @java.lang.Override
    public boolean hasTitleId() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional int32 titleId = 5;</code>
     * @return The titleId.
     */
    @java.lang.Override
    public int getTitleId() {
      return titleId_;
    }
    /**
     * <code>optional int32 titleId = 5;</code>
     * @param value The titleId to set.
     * @return This builder for chaining.
     */
    public Builder setTitleId(int value) {

      titleId_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 titleId = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearTitleId() {
      bitField0_ = (bitField0_ & ~0x00000010);
      titleId_ = 0;
      onChanged();
      return this;
    }

    private int headIcon_ ;
    /**
     * <code>optional int32 headIcon = 6;</code>
     * @return Whether the headIcon field is set.
     */
    @java.lang.Override
    public boolean hasHeadIcon() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional int32 headIcon = 6;</code>
     * @return The headIcon.
     */
    @java.lang.Override
    public int getHeadIcon() {
      return headIcon_;
    }
    /**
     * <code>optional int32 headIcon = 6;</code>
     * @param value The headIcon to set.
     * @return This builder for chaining.
     */
    public Builder setHeadIcon(int value) {

      headIcon_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 headIcon = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearHeadIcon() {
      bitField0_ = (bitField0_ & ~0x00000020);
      headIcon_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object wxHeadUrl_ = "";
    /**
     * <code>optional string wxHeadUrl = 7;</code>
     * @return Whether the wxHeadUrl field is set.
     */
    public boolean hasWxHeadUrl() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <code>optional string wxHeadUrl = 7;</code>
     * @return The wxHeadUrl.
     */
    public java.lang.String getWxHeadUrl() {
      java.lang.Object ref = wxHeadUrl_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          wxHeadUrl_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string wxHeadUrl = 7;</code>
     * @return The bytes for wxHeadUrl.
     */
    public com.google.protobuf.ByteString
        getWxHeadUrlBytes() {
      java.lang.Object ref = wxHeadUrl_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        wxHeadUrl_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string wxHeadUrl = 7;</code>
     * @param value The wxHeadUrl to set.
     * @return This builder for chaining.
     */
    public Builder setWxHeadUrl(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      wxHeadUrl_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>optional string wxHeadUrl = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearWxHeadUrl() {
      wxHeadUrl_ = getDefaultInstance().getWxHeadUrl();
      bitField0_ = (bitField0_ & ~0x00000040);
      onChanged();
      return this;
    }
    /**
     * <code>optional string wxHeadUrl = 7;</code>
     * @param value The bytes for wxHeadUrl to set.
     * @return This builder for chaining.
     */
    public Builder setWxHeadUrlBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      wxHeadUrl_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }

    private int equipHeadIconFrame_ ;
    /**
     * <code>optional int32 equipHeadIconFrame = 8;</code>
     * @return Whether the equipHeadIconFrame field is set.
     */
    @java.lang.Override
    public boolean hasEquipHeadIconFrame() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <code>optional int32 equipHeadIconFrame = 8;</code>
     * @return The equipHeadIconFrame.
     */
    @java.lang.Override
    public int getEquipHeadIconFrame() {
      return equipHeadIconFrame_;
    }
    /**
     * <code>optional int32 equipHeadIconFrame = 8;</code>
     * @param value The equipHeadIconFrame to set.
     * @return This builder for chaining.
     */
    public Builder setEquipHeadIconFrame(int value) {

      equipHeadIconFrame_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 equipHeadIconFrame = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearEquipHeadIconFrame() {
      bitField0_ = (bitField0_ & ~0x00000080);
      equipHeadIconFrame_ = 0;
      onChanged();
      return this;
    }

    private int times_ ;
    /**
     * <code>optional int32 Times = 9;</code>
     * @return Whether the times field is set.
     */
    @java.lang.Override
    public boolean hasTimes() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <code>optional int32 Times = 9;</code>
     * @return The times.
     */
    @java.lang.Override
    public int getTimes() {
      return times_;
    }
    /**
     * <code>optional int32 Times = 9;</code>
     * @param value The times to set.
     * @return This builder for chaining.
     */
    public Builder setTimes(int value) {

      times_ = value;
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 Times = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearTimes() {
      bitField0_ = (bitField0_ & ~0x00000100);
      times_ = 0;
      onChanged();
      return this;
    }

    private int star_ ;
    /**
     * <code>optional int32 star = 10;</code>
     * @return Whether the star field is set.
     */
    @java.lang.Override
    public boolean hasStar() {
      return ((bitField0_ & 0x00000200) != 0);
    }
    /**
     * <code>optional int32 star = 10;</code>
     * @return The star.
     */
    @java.lang.Override
    public int getStar() {
      return star_;
    }
    /**
     * <code>optional int32 star = 10;</code>
     * @param value The star to set.
     * @return This builder for chaining.
     */
    public Builder setStar(int value) {

      star_ = value;
      bitField0_ |= 0x00000200;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 star = 10;</code>
     * @return This builder for chaining.
     */
    public Builder clearStar() {
      bitField0_ = (bitField0_ & ~0x00000200);
      star_ = 0;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.UnionDuelContributeMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.UnionDuelContributeMsg)
  private static final xddq.pb.UnionDuelContributeMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.UnionDuelContributeMsg();
  }

  public static xddq.pb.UnionDuelContributeMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<UnionDuelContributeMsg>
      PARSER = new com.google.protobuf.AbstractParser<UnionDuelContributeMsg>() {
    @java.lang.Override
    public UnionDuelContributeMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<UnionDuelContributeMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<UnionDuelContributeMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.UnionDuelContributeMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

