// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.GodIslandSyncFriendUnionMsg}
 */
public final class GodIslandSyncFriendUnionMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.GodIslandSyncFriendUnionMsg)
    GodIslandSyncFriendUnionMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      GodIslandSyncFriendUnionMsg.class.getName());
  }
  // Use GodIslandSyncFriendUnionMsg.newBuilder() to construct.
  private GodIslandSyncFriendUnionMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private GodIslandSyncFriendUnionMsg() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_GodIslandSyncFriendUnionMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_GodIslandSyncFriendUnionMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.GodIslandSyncFriendUnionMsg.class, xddq.pb.GodIslandSyncFriendUnionMsg.Builder.class);
  }

  private int bitField0_;
  public static final int TARGETID_FIELD_NUMBER = 1;
  private long targetId_ = 0L;
  /**
   * <code>required int64 targetId = 1;</code>
   * @return Whether the targetId field is set.
   */
  @java.lang.Override
  public boolean hasTargetId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int64 targetId = 1;</code>
   * @return The targetId.
   */
  @java.lang.Override
  public long getTargetId() {
    return targetId_;
  }

  public static final int FRIEND_FIELD_NUMBER = 2;
  private boolean friend_ = false;
  /**
   * <code>required bool friend = 2;</code>
   * @return Whether the friend field is set.
   */
  @java.lang.Override
  public boolean hasFriend() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>required bool friend = 2;</code>
   * @return The friend.
   */
  @java.lang.Override
  public boolean getFriend() {
    return friend_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasTargetId()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasFriend()) {
      memoizedIsInitialized = 0;
      return false;
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt64(1, targetId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeBool(2, friend_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(1, targetId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(2, friend_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.GodIslandSyncFriendUnionMsg)) {
      return super.equals(obj);
    }
    xddq.pb.GodIslandSyncFriendUnionMsg other = (xddq.pb.GodIslandSyncFriendUnionMsg) obj;

    if (hasTargetId() != other.hasTargetId()) return false;
    if (hasTargetId()) {
      if (getTargetId()
          != other.getTargetId()) return false;
    }
    if (hasFriend() != other.hasFriend()) return false;
    if (hasFriend()) {
      if (getFriend()
          != other.getFriend()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasTargetId()) {
      hash = (37 * hash) + TARGETID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTargetId());
    }
    if (hasFriend()) {
      hash = (37 * hash) + FRIEND_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getFriend());
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.GodIslandSyncFriendUnionMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GodIslandSyncFriendUnionMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GodIslandSyncFriendUnionMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GodIslandSyncFriendUnionMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GodIslandSyncFriendUnionMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GodIslandSyncFriendUnionMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GodIslandSyncFriendUnionMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.GodIslandSyncFriendUnionMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.GodIslandSyncFriendUnionMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.GodIslandSyncFriendUnionMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.GodIslandSyncFriendUnionMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.GodIslandSyncFriendUnionMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.GodIslandSyncFriendUnionMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.GodIslandSyncFriendUnionMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.GodIslandSyncFriendUnionMsg)
      xddq.pb.GodIslandSyncFriendUnionMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GodIslandSyncFriendUnionMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GodIslandSyncFriendUnionMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.GodIslandSyncFriendUnionMsg.class, xddq.pb.GodIslandSyncFriendUnionMsg.Builder.class);
    }

    // Construct using xddq.pb.GodIslandSyncFriendUnionMsg.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      targetId_ = 0L;
      friend_ = false;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GodIslandSyncFriendUnionMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.GodIslandSyncFriendUnionMsg getDefaultInstanceForType() {
      return xddq.pb.GodIslandSyncFriendUnionMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.GodIslandSyncFriendUnionMsg build() {
      xddq.pb.GodIslandSyncFriendUnionMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.GodIslandSyncFriendUnionMsg buildPartial() {
      xddq.pb.GodIslandSyncFriendUnionMsg result = new xddq.pb.GodIslandSyncFriendUnionMsg(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.GodIslandSyncFriendUnionMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.targetId_ = targetId_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.friend_ = friend_;
        to_bitField0_ |= 0x00000002;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.GodIslandSyncFriendUnionMsg) {
        return mergeFrom((xddq.pb.GodIslandSyncFriendUnionMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.GodIslandSyncFriendUnionMsg other) {
      if (other == xddq.pb.GodIslandSyncFriendUnionMsg.getDefaultInstance()) return this;
      if (other.hasTargetId()) {
        setTargetId(other.getTargetId());
      }
      if (other.hasFriend()) {
        setFriend(other.getFriend());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasTargetId()) {
        return false;
      }
      if (!hasFriend()) {
        return false;
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              targetId_ = input.readInt64();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              friend_ = input.readBool();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private long targetId_ ;
    /**
     * <code>required int64 targetId = 1;</code>
     * @return Whether the targetId field is set.
     */
    @java.lang.Override
    public boolean hasTargetId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int64 targetId = 1;</code>
     * @return The targetId.
     */
    @java.lang.Override
    public long getTargetId() {
      return targetId_;
    }
    /**
     * <code>required int64 targetId = 1;</code>
     * @param value The targetId to set.
     * @return This builder for chaining.
     */
    public Builder setTargetId(long value) {

      targetId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int64 targetId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearTargetId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      targetId_ = 0L;
      onChanged();
      return this;
    }

    private boolean friend_ ;
    /**
     * <code>required bool friend = 2;</code>
     * @return Whether the friend field is set.
     */
    @java.lang.Override
    public boolean hasFriend() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>required bool friend = 2;</code>
     * @return The friend.
     */
    @java.lang.Override
    public boolean getFriend() {
      return friend_;
    }
    /**
     * <code>required bool friend = 2;</code>
     * @param value The friend to set.
     * @return This builder for chaining.
     */
    public Builder setFriend(boolean value) {

      friend_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>required bool friend = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearFriend() {
      bitField0_ = (bitField0_ & ~0x00000002);
      friend_ = false;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.GodIslandSyncFriendUnionMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.GodIslandSyncFriendUnionMsg)
  private static final xddq.pb.GodIslandSyncFriendUnionMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.GodIslandSyncFriendUnionMsg();
  }

  public static xddq.pb.GodIslandSyncFriendUnionMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<GodIslandSyncFriendUnionMsg>
      PARSER = new com.google.protobuf.AbstractParser<GodIslandSyncFriendUnionMsg>() {
    @java.lang.Override
    public GodIslandSyncFriendUnionMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<GodIslandSyncFriendUnionMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<GodIslandSyncFriendUnionMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.GodIslandSyncFriendUnionMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

