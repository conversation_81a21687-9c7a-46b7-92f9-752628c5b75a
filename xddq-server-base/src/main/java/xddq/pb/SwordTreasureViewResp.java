// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.SwordTreasureViewResp}
 */
public final class SwordTreasureViewResp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.SwordTreasureViewResp)
    SwordTreasureViewRespOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      SwordTreasureViewResp.class.getName());
  }
  // Use SwordTreasureViewResp.newBuilder() to construct.
  private SwordTreasureViewResp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private SwordTreasureViewResp() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_SwordTreasureViewResp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_SwordTreasureViewResp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.SwordTreasureViewResp.class, xddq.pb.SwordTreasureViewResp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int ACTIVITYDATA_FIELD_NUMBER = 2;
  private xddq.pb.SwordTreasureActivityData activityData_;
  /**
   * <code>optional .xddq.pb.SwordTreasureActivityData activityData = 2;</code>
   * @return Whether the activityData field is set.
   */
  @java.lang.Override
  public boolean hasActivityData() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional .xddq.pb.SwordTreasureActivityData activityData = 2;</code>
   * @return The activityData.
   */
  @java.lang.Override
  public xddq.pb.SwordTreasureActivityData getActivityData() {
    return activityData_ == null ? xddq.pb.SwordTreasureActivityData.getDefaultInstance() : activityData_;
  }
  /**
   * <code>optional .xddq.pb.SwordTreasureActivityData activityData = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.SwordTreasureActivityDataOrBuilder getActivityDataOrBuilder() {
    return activityData_ == null ? xddq.pb.SwordTreasureActivityData.getDefaultInstance() : activityData_;
  }

  public static final int DRAWNOTICEMSG_FIELD_NUMBER = 3;
  private xddq.pb.SwordTreasureNoticeMsg drawNoticeMsg_;
  /**
   * <code>optional .xddq.pb.SwordTreasureNoticeMsg drawNoticeMsg = 3;</code>
   * @return Whether the drawNoticeMsg field is set.
   */
  @java.lang.Override
  public boolean hasDrawNoticeMsg() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional .xddq.pb.SwordTreasureNoticeMsg drawNoticeMsg = 3;</code>
   * @return The drawNoticeMsg.
   */
  @java.lang.Override
  public xddq.pb.SwordTreasureNoticeMsg getDrawNoticeMsg() {
    return drawNoticeMsg_ == null ? xddq.pb.SwordTreasureNoticeMsg.getDefaultInstance() : drawNoticeMsg_;
  }
  /**
   * <code>optional .xddq.pb.SwordTreasureNoticeMsg drawNoticeMsg = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.SwordTreasureNoticeMsgOrBuilder getDrawNoticeMsgOrBuilder() {
    return drawNoticeMsg_ == null ? xddq.pb.SwordTreasureNoticeMsg.getDefaultInstance() : drawNoticeMsg_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (hasActivityData()) {
      if (!getActivityData().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeMessage(2, getActivityData());
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeMessage(3, getDrawNoticeMsg());
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getActivityData());
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, getDrawNoticeMsg());
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.SwordTreasureViewResp)) {
      return super.equals(obj);
    }
    xddq.pb.SwordTreasureViewResp other = (xddq.pb.SwordTreasureViewResp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (hasActivityData() != other.hasActivityData()) return false;
    if (hasActivityData()) {
      if (!getActivityData()
          .equals(other.getActivityData())) return false;
    }
    if (hasDrawNoticeMsg() != other.hasDrawNoticeMsg()) return false;
    if (hasDrawNoticeMsg()) {
      if (!getDrawNoticeMsg()
          .equals(other.getDrawNoticeMsg())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (hasActivityData()) {
      hash = (37 * hash) + ACTIVITYDATA_FIELD_NUMBER;
      hash = (53 * hash) + getActivityData().hashCode();
    }
    if (hasDrawNoticeMsg()) {
      hash = (37 * hash) + DRAWNOTICEMSG_FIELD_NUMBER;
      hash = (53 * hash) + getDrawNoticeMsg().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.SwordTreasureViewResp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.SwordTreasureViewResp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.SwordTreasureViewResp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.SwordTreasureViewResp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.SwordTreasureViewResp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.SwordTreasureViewResp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.SwordTreasureViewResp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.SwordTreasureViewResp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.SwordTreasureViewResp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.SwordTreasureViewResp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.SwordTreasureViewResp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.SwordTreasureViewResp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.SwordTreasureViewResp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.SwordTreasureViewResp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.SwordTreasureViewResp)
      xddq.pb.SwordTreasureViewRespOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_SwordTreasureViewResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_SwordTreasureViewResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.SwordTreasureViewResp.class, xddq.pb.SwordTreasureViewResp.Builder.class);
    }

    // Construct using xddq.pb.SwordTreasureViewResp.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetActivityDataFieldBuilder();
        internalGetDrawNoticeMsgFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      activityData_ = null;
      if (activityDataBuilder_ != null) {
        activityDataBuilder_.dispose();
        activityDataBuilder_ = null;
      }
      drawNoticeMsg_ = null;
      if (drawNoticeMsgBuilder_ != null) {
        drawNoticeMsgBuilder_.dispose();
        drawNoticeMsgBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_SwordTreasureViewResp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.SwordTreasureViewResp getDefaultInstanceForType() {
      return xddq.pb.SwordTreasureViewResp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.SwordTreasureViewResp build() {
      xddq.pb.SwordTreasureViewResp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.SwordTreasureViewResp buildPartial() {
      xddq.pb.SwordTreasureViewResp result = new xddq.pb.SwordTreasureViewResp(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.SwordTreasureViewResp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.activityData_ = activityDataBuilder_ == null
            ? activityData_
            : activityDataBuilder_.build();
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.drawNoticeMsg_ = drawNoticeMsgBuilder_ == null
            ? drawNoticeMsg_
            : drawNoticeMsgBuilder_.build();
        to_bitField0_ |= 0x00000004;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.SwordTreasureViewResp) {
        return mergeFrom((xddq.pb.SwordTreasureViewResp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.SwordTreasureViewResp other) {
      if (other == xddq.pb.SwordTreasureViewResp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (other.hasActivityData()) {
        mergeActivityData(other.getActivityData());
      }
      if (other.hasDrawNoticeMsg()) {
        mergeDrawNoticeMsg(other.getDrawNoticeMsg());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      if (hasActivityData()) {
        if (!getActivityData().isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              input.readMessage(
                  internalGetActivityDataFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 26: {
              input.readMessage(
                  internalGetDrawNoticeMsgFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private xddq.pb.SwordTreasureActivityData activityData_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.SwordTreasureActivityData, xddq.pb.SwordTreasureActivityData.Builder, xddq.pb.SwordTreasureActivityDataOrBuilder> activityDataBuilder_;
    /**
     * <code>optional .xddq.pb.SwordTreasureActivityData activityData = 2;</code>
     * @return Whether the activityData field is set.
     */
    public boolean hasActivityData() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .xddq.pb.SwordTreasureActivityData activityData = 2;</code>
     * @return The activityData.
     */
    public xddq.pb.SwordTreasureActivityData getActivityData() {
      if (activityDataBuilder_ == null) {
        return activityData_ == null ? xddq.pb.SwordTreasureActivityData.getDefaultInstance() : activityData_;
      } else {
        return activityDataBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.SwordTreasureActivityData activityData = 2;</code>
     */
    public Builder setActivityData(xddq.pb.SwordTreasureActivityData value) {
      if (activityDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        activityData_ = value;
      } else {
        activityDataBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.SwordTreasureActivityData activityData = 2;</code>
     */
    public Builder setActivityData(
        xddq.pb.SwordTreasureActivityData.Builder builderForValue) {
      if (activityDataBuilder_ == null) {
        activityData_ = builderForValue.build();
      } else {
        activityDataBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.SwordTreasureActivityData activityData = 2;</code>
     */
    public Builder mergeActivityData(xddq.pb.SwordTreasureActivityData value) {
      if (activityDataBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0) &&
          activityData_ != null &&
          activityData_ != xddq.pb.SwordTreasureActivityData.getDefaultInstance()) {
          getActivityDataBuilder().mergeFrom(value);
        } else {
          activityData_ = value;
        }
      } else {
        activityDataBuilder_.mergeFrom(value);
      }
      if (activityData_ != null) {
        bitField0_ |= 0x00000002;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.SwordTreasureActivityData activityData = 2;</code>
     */
    public Builder clearActivityData() {
      bitField0_ = (bitField0_ & ~0x00000002);
      activityData_ = null;
      if (activityDataBuilder_ != null) {
        activityDataBuilder_.dispose();
        activityDataBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.SwordTreasureActivityData activityData = 2;</code>
     */
    public xddq.pb.SwordTreasureActivityData.Builder getActivityDataBuilder() {
      bitField0_ |= 0x00000002;
      onChanged();
      return internalGetActivityDataFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.SwordTreasureActivityData activityData = 2;</code>
     */
    public xddq.pb.SwordTreasureActivityDataOrBuilder getActivityDataOrBuilder() {
      if (activityDataBuilder_ != null) {
        return activityDataBuilder_.getMessageOrBuilder();
      } else {
        return activityData_ == null ?
            xddq.pb.SwordTreasureActivityData.getDefaultInstance() : activityData_;
      }
    }
    /**
     * <code>optional .xddq.pb.SwordTreasureActivityData activityData = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.SwordTreasureActivityData, xddq.pb.SwordTreasureActivityData.Builder, xddq.pb.SwordTreasureActivityDataOrBuilder> 
        internalGetActivityDataFieldBuilder() {
      if (activityDataBuilder_ == null) {
        activityDataBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.SwordTreasureActivityData, xddq.pb.SwordTreasureActivityData.Builder, xddq.pb.SwordTreasureActivityDataOrBuilder>(
                getActivityData(),
                getParentForChildren(),
                isClean());
        activityData_ = null;
      }
      return activityDataBuilder_;
    }

    private xddq.pb.SwordTreasureNoticeMsg drawNoticeMsg_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.SwordTreasureNoticeMsg, xddq.pb.SwordTreasureNoticeMsg.Builder, xddq.pb.SwordTreasureNoticeMsgOrBuilder> drawNoticeMsgBuilder_;
    /**
     * <code>optional .xddq.pb.SwordTreasureNoticeMsg drawNoticeMsg = 3;</code>
     * @return Whether the drawNoticeMsg field is set.
     */
    public boolean hasDrawNoticeMsg() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional .xddq.pb.SwordTreasureNoticeMsg drawNoticeMsg = 3;</code>
     * @return The drawNoticeMsg.
     */
    public xddq.pb.SwordTreasureNoticeMsg getDrawNoticeMsg() {
      if (drawNoticeMsgBuilder_ == null) {
        return drawNoticeMsg_ == null ? xddq.pb.SwordTreasureNoticeMsg.getDefaultInstance() : drawNoticeMsg_;
      } else {
        return drawNoticeMsgBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.SwordTreasureNoticeMsg drawNoticeMsg = 3;</code>
     */
    public Builder setDrawNoticeMsg(xddq.pb.SwordTreasureNoticeMsg value) {
      if (drawNoticeMsgBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        drawNoticeMsg_ = value;
      } else {
        drawNoticeMsgBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.SwordTreasureNoticeMsg drawNoticeMsg = 3;</code>
     */
    public Builder setDrawNoticeMsg(
        xddq.pb.SwordTreasureNoticeMsg.Builder builderForValue) {
      if (drawNoticeMsgBuilder_ == null) {
        drawNoticeMsg_ = builderForValue.build();
      } else {
        drawNoticeMsgBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.SwordTreasureNoticeMsg drawNoticeMsg = 3;</code>
     */
    public Builder mergeDrawNoticeMsg(xddq.pb.SwordTreasureNoticeMsg value) {
      if (drawNoticeMsgBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0) &&
          drawNoticeMsg_ != null &&
          drawNoticeMsg_ != xddq.pb.SwordTreasureNoticeMsg.getDefaultInstance()) {
          getDrawNoticeMsgBuilder().mergeFrom(value);
        } else {
          drawNoticeMsg_ = value;
        }
      } else {
        drawNoticeMsgBuilder_.mergeFrom(value);
      }
      if (drawNoticeMsg_ != null) {
        bitField0_ |= 0x00000004;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.SwordTreasureNoticeMsg drawNoticeMsg = 3;</code>
     */
    public Builder clearDrawNoticeMsg() {
      bitField0_ = (bitField0_ & ~0x00000004);
      drawNoticeMsg_ = null;
      if (drawNoticeMsgBuilder_ != null) {
        drawNoticeMsgBuilder_.dispose();
        drawNoticeMsgBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.SwordTreasureNoticeMsg drawNoticeMsg = 3;</code>
     */
    public xddq.pb.SwordTreasureNoticeMsg.Builder getDrawNoticeMsgBuilder() {
      bitField0_ |= 0x00000004;
      onChanged();
      return internalGetDrawNoticeMsgFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.SwordTreasureNoticeMsg drawNoticeMsg = 3;</code>
     */
    public xddq.pb.SwordTreasureNoticeMsgOrBuilder getDrawNoticeMsgOrBuilder() {
      if (drawNoticeMsgBuilder_ != null) {
        return drawNoticeMsgBuilder_.getMessageOrBuilder();
      } else {
        return drawNoticeMsg_ == null ?
            xddq.pb.SwordTreasureNoticeMsg.getDefaultInstance() : drawNoticeMsg_;
      }
    }
    /**
     * <code>optional .xddq.pb.SwordTreasureNoticeMsg drawNoticeMsg = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.SwordTreasureNoticeMsg, xddq.pb.SwordTreasureNoticeMsg.Builder, xddq.pb.SwordTreasureNoticeMsgOrBuilder> 
        internalGetDrawNoticeMsgFieldBuilder() {
      if (drawNoticeMsgBuilder_ == null) {
        drawNoticeMsgBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.SwordTreasureNoticeMsg, xddq.pb.SwordTreasureNoticeMsg.Builder, xddq.pb.SwordTreasureNoticeMsgOrBuilder>(
                getDrawNoticeMsg(),
                getParentForChildren(),
                isClean());
        drawNoticeMsg_ = null;
      }
      return drawNoticeMsgBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.SwordTreasureViewResp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.SwordTreasureViewResp)
  private static final xddq.pb.SwordTreasureViewResp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.SwordTreasureViewResp();
  }

  public static xddq.pb.SwordTreasureViewResp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<SwordTreasureViewResp>
      PARSER = new com.google.protobuf.AbstractParser<SwordTreasureViewResp>() {
    @java.lang.Override
    public SwordTreasureViewResp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<SwordTreasureViewResp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<SwordTreasureViewResp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.SwordTreasureViewResp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

