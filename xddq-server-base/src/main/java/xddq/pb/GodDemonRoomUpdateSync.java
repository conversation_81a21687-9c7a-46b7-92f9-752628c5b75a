// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.GodDemonRoomUpdateSync}
 */
public final class GodDemonRoomUpdateSync extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.GodDemonRoomUpdateSync)
    GodDemonRoomUpdateSyncOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      GodDemonRoomUpdateSync.class.getName());
  }
  // Use GodDemonRoomUpdateSync.newBuilder() to construct.
  private GodDemonRoomUpdateSync(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private GodDemonRoomUpdateSync() {
    chooseBuffVec_ = emptyIntList();
    playerSyncList_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_GodDemonRoomUpdateSync_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_GodDemonRoomUpdateSync_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.GodDemonRoomUpdateSync.class, xddq.pb.GodDemonRoomUpdateSync.Builder.class);
  }

  private int bitField0_;
  public static final int ROOMID_FIELD_NUMBER = 1;
  private long roomId_ = 0L;
  /**
   * <code>optional int64 roomId = 1;</code>
   * @return Whether the roomId field is set.
   */
  @java.lang.Override
  public boolean hasRoomId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int64 roomId = 1;</code>
   * @return The roomId.
   */
  @java.lang.Override
  public long getRoomId() {
    return roomId_;
  }

  public static final int ROUNDBEGINTIME_FIELD_NUMBER = 2;
  private long roundBeginTime_ = 0L;
  /**
   * <code>optional int64 roundBeginTime = 2;</code>
   * @return Whether the roundBeginTime field is set.
   */
  @java.lang.Override
  public boolean hasRoundBeginTime() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int64 roundBeginTime = 2;</code>
   * @return The roundBeginTime.
   */
  @java.lang.Override
  public long getRoundBeginTime() {
    return roundBeginTime_;
  }

  public static final int ROUND_FIELD_NUMBER = 3;
  private int round_ = 0;
  /**
   * <code>optional int32 round = 3;</code>
   * @return Whether the round field is set.
   */
  @java.lang.Override
  public boolean hasRound() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional int32 round = 3;</code>
   * @return The round.
   */
  @java.lang.Override
  public int getRound() {
    return round_;
  }

  public static final int TIMEID_FIELD_NUMBER = 4;
  private long timeId_ = 0L;
  /**
   * <code>optional int64 timeId = 4;</code>
   * @return Whether the timeId field is set.
   */
  @java.lang.Override
  public boolean hasTimeId() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional int64 timeId = 4;</code>
   * @return The timeId.
   */
  @java.lang.Override
  public long getTimeId() {
    return timeId_;
  }

  public static final int CHOOSEBUFFVEC_FIELD_NUMBER = 5;
  @SuppressWarnings("serial")
  private com.google.protobuf.Internal.IntList chooseBuffVec_ =
      emptyIntList();
  /**
   * <code>repeated int32 chooseBuffVec = 5;</code>
   * @return A list containing the chooseBuffVec.
   */
  @java.lang.Override
  public java.util.List<java.lang.Integer>
      getChooseBuffVecList() {
    return chooseBuffVec_;
  }
  /**
   * <code>repeated int32 chooseBuffVec = 5;</code>
   * @return The count of chooseBuffVec.
   */
  public int getChooseBuffVecCount() {
    return chooseBuffVec_.size();
  }
  /**
   * <code>repeated int32 chooseBuffVec = 5;</code>
   * @param index The index of the element to return.
   * @return The chooseBuffVec at the given index.
   */
  public int getChooseBuffVec(int index) {
    return chooseBuffVec_.getInt(index);
  }

  public static final int PLAYERSYNCLIST_FIELD_NUMBER = 6;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.GodDemonRoomPlayerSyncMsg> playerSyncList_;
  /**
   * <code>repeated .xddq.pb.GodDemonRoomPlayerSyncMsg playerSyncList = 6;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.GodDemonRoomPlayerSyncMsg> getPlayerSyncListList() {
    return playerSyncList_;
  }
  /**
   * <code>repeated .xddq.pb.GodDemonRoomPlayerSyncMsg playerSyncList = 6;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.GodDemonRoomPlayerSyncMsgOrBuilder> 
      getPlayerSyncListOrBuilderList() {
    return playerSyncList_;
  }
  /**
   * <code>repeated .xddq.pb.GodDemonRoomPlayerSyncMsg playerSyncList = 6;</code>
   */
  @java.lang.Override
  public int getPlayerSyncListCount() {
    return playerSyncList_.size();
  }
  /**
   * <code>repeated .xddq.pb.GodDemonRoomPlayerSyncMsg playerSyncList = 6;</code>
   */
  @java.lang.Override
  public xddq.pb.GodDemonRoomPlayerSyncMsg getPlayerSyncList(int index) {
    return playerSyncList_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.GodDemonRoomPlayerSyncMsg playerSyncList = 6;</code>
   */
  @java.lang.Override
  public xddq.pb.GodDemonRoomPlayerSyncMsgOrBuilder getPlayerSyncListOrBuilder(
      int index) {
    return playerSyncList_.get(index);
  }

  public static final int END_FIELD_NUMBER = 7;
  private boolean end_ = false;
  /**
   * <code>optional bool end = 7;</code>
   * @return Whether the end field is set.
   */
  @java.lang.Override
  public boolean hasEnd() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>optional bool end = 7;</code>
   * @return The end.
   */
  @java.lang.Override
  public boolean getEnd() {
    return end_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt64(1, roomId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt64(2, roundBeginTime_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt32(3, round_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt64(4, timeId_);
    }
    for (int i = 0; i < chooseBuffVec_.size(); i++) {
      output.writeInt32(5, chooseBuffVec_.getInt(i));
    }
    for (int i = 0; i < playerSyncList_.size(); i++) {
      output.writeMessage(6, playerSyncList_.get(i));
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      output.writeBool(7, end_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(1, roomId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(2, roundBeginTime_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(3, round_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(4, timeId_);
    }
    {
      int dataSize = 0;
      for (int i = 0; i < chooseBuffVec_.size(); i++) {
        dataSize += com.google.protobuf.CodedOutputStream
          .computeInt32SizeNoTag(chooseBuffVec_.getInt(i));
      }
      size += dataSize;
      size += 1 * getChooseBuffVecList().size();
    }
    for (int i = 0; i < playerSyncList_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(6, playerSyncList_.get(i));
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(7, end_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.GodDemonRoomUpdateSync)) {
      return super.equals(obj);
    }
    xddq.pb.GodDemonRoomUpdateSync other = (xddq.pb.GodDemonRoomUpdateSync) obj;

    if (hasRoomId() != other.hasRoomId()) return false;
    if (hasRoomId()) {
      if (getRoomId()
          != other.getRoomId()) return false;
    }
    if (hasRoundBeginTime() != other.hasRoundBeginTime()) return false;
    if (hasRoundBeginTime()) {
      if (getRoundBeginTime()
          != other.getRoundBeginTime()) return false;
    }
    if (hasRound() != other.hasRound()) return false;
    if (hasRound()) {
      if (getRound()
          != other.getRound()) return false;
    }
    if (hasTimeId() != other.hasTimeId()) return false;
    if (hasTimeId()) {
      if (getTimeId()
          != other.getTimeId()) return false;
    }
    if (!getChooseBuffVecList()
        .equals(other.getChooseBuffVecList())) return false;
    if (!getPlayerSyncListList()
        .equals(other.getPlayerSyncListList())) return false;
    if (hasEnd() != other.hasEnd()) return false;
    if (hasEnd()) {
      if (getEnd()
          != other.getEnd()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRoomId()) {
      hash = (37 * hash) + ROOMID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getRoomId());
    }
    if (hasRoundBeginTime()) {
      hash = (37 * hash) + ROUNDBEGINTIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getRoundBeginTime());
    }
    if (hasRound()) {
      hash = (37 * hash) + ROUND_FIELD_NUMBER;
      hash = (53 * hash) + getRound();
    }
    if (hasTimeId()) {
      hash = (37 * hash) + TIMEID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTimeId());
    }
    if (getChooseBuffVecCount() > 0) {
      hash = (37 * hash) + CHOOSEBUFFVEC_FIELD_NUMBER;
      hash = (53 * hash) + getChooseBuffVecList().hashCode();
    }
    if (getPlayerSyncListCount() > 0) {
      hash = (37 * hash) + PLAYERSYNCLIST_FIELD_NUMBER;
      hash = (53 * hash) + getPlayerSyncListList().hashCode();
    }
    if (hasEnd()) {
      hash = (37 * hash) + END_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getEnd());
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.GodDemonRoomUpdateSync parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GodDemonRoomUpdateSync parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GodDemonRoomUpdateSync parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GodDemonRoomUpdateSync parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GodDemonRoomUpdateSync parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GodDemonRoomUpdateSync parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GodDemonRoomUpdateSync parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.GodDemonRoomUpdateSync parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.GodDemonRoomUpdateSync parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.GodDemonRoomUpdateSync parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.GodDemonRoomUpdateSync parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.GodDemonRoomUpdateSync parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.GodDemonRoomUpdateSync prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.GodDemonRoomUpdateSync}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.GodDemonRoomUpdateSync)
      xddq.pb.GodDemonRoomUpdateSyncOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GodDemonRoomUpdateSync_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GodDemonRoomUpdateSync_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.GodDemonRoomUpdateSync.class, xddq.pb.GodDemonRoomUpdateSync.Builder.class);
    }

    // Construct using xddq.pb.GodDemonRoomUpdateSync.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      roomId_ = 0L;
      roundBeginTime_ = 0L;
      round_ = 0;
      timeId_ = 0L;
      chooseBuffVec_ = emptyIntList();
      if (playerSyncListBuilder_ == null) {
        playerSyncList_ = java.util.Collections.emptyList();
      } else {
        playerSyncList_ = null;
        playerSyncListBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000020);
      end_ = false;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GodDemonRoomUpdateSync_descriptor;
    }

    @java.lang.Override
    public xddq.pb.GodDemonRoomUpdateSync getDefaultInstanceForType() {
      return xddq.pb.GodDemonRoomUpdateSync.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.GodDemonRoomUpdateSync build() {
      xddq.pb.GodDemonRoomUpdateSync result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.GodDemonRoomUpdateSync buildPartial() {
      xddq.pb.GodDemonRoomUpdateSync result = new xddq.pb.GodDemonRoomUpdateSync(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.GodDemonRoomUpdateSync result) {
      if (playerSyncListBuilder_ == null) {
        if (((bitField0_ & 0x00000020) != 0)) {
          playerSyncList_ = java.util.Collections.unmodifiableList(playerSyncList_);
          bitField0_ = (bitField0_ & ~0x00000020);
        }
        result.playerSyncList_ = playerSyncList_;
      } else {
        result.playerSyncList_ = playerSyncListBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.GodDemonRoomUpdateSync result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.roomId_ = roomId_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.roundBeginTime_ = roundBeginTime_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.round_ = round_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.timeId_ = timeId_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        chooseBuffVec_.makeImmutable();
        result.chooseBuffVec_ = chooseBuffVec_;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.end_ = end_;
        to_bitField0_ |= 0x00000010;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.GodDemonRoomUpdateSync) {
        return mergeFrom((xddq.pb.GodDemonRoomUpdateSync)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.GodDemonRoomUpdateSync other) {
      if (other == xddq.pb.GodDemonRoomUpdateSync.getDefaultInstance()) return this;
      if (other.hasRoomId()) {
        setRoomId(other.getRoomId());
      }
      if (other.hasRoundBeginTime()) {
        setRoundBeginTime(other.getRoundBeginTime());
      }
      if (other.hasRound()) {
        setRound(other.getRound());
      }
      if (other.hasTimeId()) {
        setTimeId(other.getTimeId());
      }
      if (!other.chooseBuffVec_.isEmpty()) {
        if (chooseBuffVec_.isEmpty()) {
          chooseBuffVec_ = other.chooseBuffVec_;
          chooseBuffVec_.makeImmutable();
          bitField0_ |= 0x00000010;
        } else {
          ensureChooseBuffVecIsMutable();
          chooseBuffVec_.addAll(other.chooseBuffVec_);
        }
        onChanged();
      }
      if (playerSyncListBuilder_ == null) {
        if (!other.playerSyncList_.isEmpty()) {
          if (playerSyncList_.isEmpty()) {
            playerSyncList_ = other.playerSyncList_;
            bitField0_ = (bitField0_ & ~0x00000020);
          } else {
            ensurePlayerSyncListIsMutable();
            playerSyncList_.addAll(other.playerSyncList_);
          }
          onChanged();
        }
      } else {
        if (!other.playerSyncList_.isEmpty()) {
          if (playerSyncListBuilder_.isEmpty()) {
            playerSyncListBuilder_.dispose();
            playerSyncListBuilder_ = null;
            playerSyncList_ = other.playerSyncList_;
            bitField0_ = (bitField0_ & ~0x00000020);
            playerSyncListBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetPlayerSyncListFieldBuilder() : null;
          } else {
            playerSyncListBuilder_.addAllMessages(other.playerSyncList_);
          }
        }
      }
      if (other.hasEnd()) {
        setEnd(other.getEnd());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              roomId_ = input.readInt64();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              roundBeginTime_ = input.readInt64();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 24: {
              round_ = input.readInt32();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 32: {
              timeId_ = input.readInt64();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            case 40: {
              int v = input.readInt32();
              ensureChooseBuffVecIsMutable();
              chooseBuffVec_.addInt(v);
              break;
            } // case 40
            case 42: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              ensureChooseBuffVecIsMutable();
              while (input.getBytesUntilLimit() > 0) {
                chooseBuffVec_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            } // case 42
            case 50: {
              xddq.pb.GodDemonRoomPlayerSyncMsg m =
                  input.readMessage(
                      xddq.pb.GodDemonRoomPlayerSyncMsg.parser(),
                      extensionRegistry);
              if (playerSyncListBuilder_ == null) {
                ensurePlayerSyncListIsMutable();
                playerSyncList_.add(m);
              } else {
                playerSyncListBuilder_.addMessage(m);
              }
              break;
            } // case 50
            case 56: {
              end_ = input.readBool();
              bitField0_ |= 0x00000040;
              break;
            } // case 56
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private long roomId_ ;
    /**
     * <code>optional int64 roomId = 1;</code>
     * @return Whether the roomId field is set.
     */
    @java.lang.Override
    public boolean hasRoomId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 roomId = 1;</code>
     * @return The roomId.
     */
    @java.lang.Override
    public long getRoomId() {
      return roomId_;
    }
    /**
     * <code>optional int64 roomId = 1;</code>
     * @param value The roomId to set.
     * @return This builder for chaining.
     */
    public Builder setRoomId(long value) {

      roomId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 roomId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRoomId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      roomId_ = 0L;
      onChanged();
      return this;
    }

    private long roundBeginTime_ ;
    /**
     * <code>optional int64 roundBeginTime = 2;</code>
     * @return Whether the roundBeginTime field is set.
     */
    @java.lang.Override
    public boolean hasRoundBeginTime() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int64 roundBeginTime = 2;</code>
     * @return The roundBeginTime.
     */
    @java.lang.Override
    public long getRoundBeginTime() {
      return roundBeginTime_;
    }
    /**
     * <code>optional int64 roundBeginTime = 2;</code>
     * @param value The roundBeginTime to set.
     * @return This builder for chaining.
     */
    public Builder setRoundBeginTime(long value) {

      roundBeginTime_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 roundBeginTime = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearRoundBeginTime() {
      bitField0_ = (bitField0_ & ~0x00000002);
      roundBeginTime_ = 0L;
      onChanged();
      return this;
    }

    private int round_ ;
    /**
     * <code>optional int32 round = 3;</code>
     * @return Whether the round field is set.
     */
    @java.lang.Override
    public boolean hasRound() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int32 round = 3;</code>
     * @return The round.
     */
    @java.lang.Override
    public int getRound() {
      return round_;
    }
    /**
     * <code>optional int32 round = 3;</code>
     * @param value The round to set.
     * @return This builder for chaining.
     */
    public Builder setRound(int value) {

      round_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 round = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearRound() {
      bitField0_ = (bitField0_ & ~0x00000004);
      round_ = 0;
      onChanged();
      return this;
    }

    private long timeId_ ;
    /**
     * <code>optional int64 timeId = 4;</code>
     * @return Whether the timeId field is set.
     */
    @java.lang.Override
    public boolean hasTimeId() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int64 timeId = 4;</code>
     * @return The timeId.
     */
    @java.lang.Override
    public long getTimeId() {
      return timeId_;
    }
    /**
     * <code>optional int64 timeId = 4;</code>
     * @param value The timeId to set.
     * @return This builder for chaining.
     */
    public Builder setTimeId(long value) {

      timeId_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 timeId = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearTimeId() {
      bitField0_ = (bitField0_ & ~0x00000008);
      timeId_ = 0L;
      onChanged();
      return this;
    }

    private com.google.protobuf.Internal.IntList chooseBuffVec_ = emptyIntList();
    private void ensureChooseBuffVecIsMutable() {
      if (!chooseBuffVec_.isModifiable()) {
        chooseBuffVec_ = makeMutableCopy(chooseBuffVec_);
      }
      bitField0_ |= 0x00000010;
    }
    /**
     * <code>repeated int32 chooseBuffVec = 5;</code>
     * @return A list containing the chooseBuffVec.
     */
    public java.util.List<java.lang.Integer>
        getChooseBuffVecList() {
      chooseBuffVec_.makeImmutable();
      return chooseBuffVec_;
    }
    /**
     * <code>repeated int32 chooseBuffVec = 5;</code>
     * @return The count of chooseBuffVec.
     */
    public int getChooseBuffVecCount() {
      return chooseBuffVec_.size();
    }
    /**
     * <code>repeated int32 chooseBuffVec = 5;</code>
     * @param index The index of the element to return.
     * @return The chooseBuffVec at the given index.
     */
    public int getChooseBuffVec(int index) {
      return chooseBuffVec_.getInt(index);
    }
    /**
     * <code>repeated int32 chooseBuffVec = 5;</code>
     * @param index The index to set the value at.
     * @param value The chooseBuffVec to set.
     * @return This builder for chaining.
     */
    public Builder setChooseBuffVec(
        int index, int value) {

      ensureChooseBuffVecIsMutable();
      chooseBuffVec_.setInt(index, value);
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 chooseBuffVec = 5;</code>
     * @param value The chooseBuffVec to add.
     * @return This builder for chaining.
     */
    public Builder addChooseBuffVec(int value) {

      ensureChooseBuffVecIsMutable();
      chooseBuffVec_.addInt(value);
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 chooseBuffVec = 5;</code>
     * @param values The chooseBuffVec to add.
     * @return This builder for chaining.
     */
    public Builder addAllChooseBuffVec(
        java.lang.Iterable<? extends java.lang.Integer> values) {
      ensureChooseBuffVecIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, chooseBuffVec_);
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 chooseBuffVec = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearChooseBuffVec() {
      chooseBuffVec_ = emptyIntList();
      bitField0_ = (bitField0_ & ~0x00000010);
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.GodDemonRoomPlayerSyncMsg> playerSyncList_ =
      java.util.Collections.emptyList();
    private void ensurePlayerSyncListIsMutable() {
      if (!((bitField0_ & 0x00000020) != 0)) {
        playerSyncList_ = new java.util.ArrayList<xddq.pb.GodDemonRoomPlayerSyncMsg>(playerSyncList_);
        bitField0_ |= 0x00000020;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.GodDemonRoomPlayerSyncMsg, xddq.pb.GodDemonRoomPlayerSyncMsg.Builder, xddq.pb.GodDemonRoomPlayerSyncMsgOrBuilder> playerSyncListBuilder_;

    /**
     * <code>repeated .xddq.pb.GodDemonRoomPlayerSyncMsg playerSyncList = 6;</code>
     */
    public java.util.List<xddq.pb.GodDemonRoomPlayerSyncMsg> getPlayerSyncListList() {
      if (playerSyncListBuilder_ == null) {
        return java.util.Collections.unmodifiableList(playerSyncList_);
      } else {
        return playerSyncListBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRoomPlayerSyncMsg playerSyncList = 6;</code>
     */
    public int getPlayerSyncListCount() {
      if (playerSyncListBuilder_ == null) {
        return playerSyncList_.size();
      } else {
        return playerSyncListBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRoomPlayerSyncMsg playerSyncList = 6;</code>
     */
    public xddq.pb.GodDemonRoomPlayerSyncMsg getPlayerSyncList(int index) {
      if (playerSyncListBuilder_ == null) {
        return playerSyncList_.get(index);
      } else {
        return playerSyncListBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRoomPlayerSyncMsg playerSyncList = 6;</code>
     */
    public Builder setPlayerSyncList(
        int index, xddq.pb.GodDemonRoomPlayerSyncMsg value) {
      if (playerSyncListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePlayerSyncListIsMutable();
        playerSyncList_.set(index, value);
        onChanged();
      } else {
        playerSyncListBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRoomPlayerSyncMsg playerSyncList = 6;</code>
     */
    public Builder setPlayerSyncList(
        int index, xddq.pb.GodDemonRoomPlayerSyncMsg.Builder builderForValue) {
      if (playerSyncListBuilder_ == null) {
        ensurePlayerSyncListIsMutable();
        playerSyncList_.set(index, builderForValue.build());
        onChanged();
      } else {
        playerSyncListBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRoomPlayerSyncMsg playerSyncList = 6;</code>
     */
    public Builder addPlayerSyncList(xddq.pb.GodDemonRoomPlayerSyncMsg value) {
      if (playerSyncListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePlayerSyncListIsMutable();
        playerSyncList_.add(value);
        onChanged();
      } else {
        playerSyncListBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRoomPlayerSyncMsg playerSyncList = 6;</code>
     */
    public Builder addPlayerSyncList(
        int index, xddq.pb.GodDemonRoomPlayerSyncMsg value) {
      if (playerSyncListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePlayerSyncListIsMutable();
        playerSyncList_.add(index, value);
        onChanged();
      } else {
        playerSyncListBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRoomPlayerSyncMsg playerSyncList = 6;</code>
     */
    public Builder addPlayerSyncList(
        xddq.pb.GodDemonRoomPlayerSyncMsg.Builder builderForValue) {
      if (playerSyncListBuilder_ == null) {
        ensurePlayerSyncListIsMutable();
        playerSyncList_.add(builderForValue.build());
        onChanged();
      } else {
        playerSyncListBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRoomPlayerSyncMsg playerSyncList = 6;</code>
     */
    public Builder addPlayerSyncList(
        int index, xddq.pb.GodDemonRoomPlayerSyncMsg.Builder builderForValue) {
      if (playerSyncListBuilder_ == null) {
        ensurePlayerSyncListIsMutable();
        playerSyncList_.add(index, builderForValue.build());
        onChanged();
      } else {
        playerSyncListBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRoomPlayerSyncMsg playerSyncList = 6;</code>
     */
    public Builder addAllPlayerSyncList(
        java.lang.Iterable<? extends xddq.pb.GodDemonRoomPlayerSyncMsg> values) {
      if (playerSyncListBuilder_ == null) {
        ensurePlayerSyncListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, playerSyncList_);
        onChanged();
      } else {
        playerSyncListBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRoomPlayerSyncMsg playerSyncList = 6;</code>
     */
    public Builder clearPlayerSyncList() {
      if (playerSyncListBuilder_ == null) {
        playerSyncList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000020);
        onChanged();
      } else {
        playerSyncListBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRoomPlayerSyncMsg playerSyncList = 6;</code>
     */
    public Builder removePlayerSyncList(int index) {
      if (playerSyncListBuilder_ == null) {
        ensurePlayerSyncListIsMutable();
        playerSyncList_.remove(index);
        onChanged();
      } else {
        playerSyncListBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRoomPlayerSyncMsg playerSyncList = 6;</code>
     */
    public xddq.pb.GodDemonRoomPlayerSyncMsg.Builder getPlayerSyncListBuilder(
        int index) {
      return internalGetPlayerSyncListFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRoomPlayerSyncMsg playerSyncList = 6;</code>
     */
    public xddq.pb.GodDemonRoomPlayerSyncMsgOrBuilder getPlayerSyncListOrBuilder(
        int index) {
      if (playerSyncListBuilder_ == null) {
        return playerSyncList_.get(index);  } else {
        return playerSyncListBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRoomPlayerSyncMsg playerSyncList = 6;</code>
     */
    public java.util.List<? extends xddq.pb.GodDemonRoomPlayerSyncMsgOrBuilder> 
         getPlayerSyncListOrBuilderList() {
      if (playerSyncListBuilder_ != null) {
        return playerSyncListBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(playerSyncList_);
      }
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRoomPlayerSyncMsg playerSyncList = 6;</code>
     */
    public xddq.pb.GodDemonRoomPlayerSyncMsg.Builder addPlayerSyncListBuilder() {
      return internalGetPlayerSyncListFieldBuilder().addBuilder(
          xddq.pb.GodDemonRoomPlayerSyncMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRoomPlayerSyncMsg playerSyncList = 6;</code>
     */
    public xddq.pb.GodDemonRoomPlayerSyncMsg.Builder addPlayerSyncListBuilder(
        int index) {
      return internalGetPlayerSyncListFieldBuilder().addBuilder(
          index, xddq.pb.GodDemonRoomPlayerSyncMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.GodDemonRoomPlayerSyncMsg playerSyncList = 6;</code>
     */
    public java.util.List<xddq.pb.GodDemonRoomPlayerSyncMsg.Builder> 
         getPlayerSyncListBuilderList() {
      return internalGetPlayerSyncListFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.GodDemonRoomPlayerSyncMsg, xddq.pb.GodDemonRoomPlayerSyncMsg.Builder, xddq.pb.GodDemonRoomPlayerSyncMsgOrBuilder> 
        internalGetPlayerSyncListFieldBuilder() {
      if (playerSyncListBuilder_ == null) {
        playerSyncListBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.GodDemonRoomPlayerSyncMsg, xddq.pb.GodDemonRoomPlayerSyncMsg.Builder, xddq.pb.GodDemonRoomPlayerSyncMsgOrBuilder>(
                playerSyncList_,
                ((bitField0_ & 0x00000020) != 0),
                getParentForChildren(),
                isClean());
        playerSyncList_ = null;
      }
      return playerSyncListBuilder_;
    }

    private boolean end_ ;
    /**
     * <code>optional bool end = 7;</code>
     * @return Whether the end field is set.
     */
    @java.lang.Override
    public boolean hasEnd() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <code>optional bool end = 7;</code>
     * @return The end.
     */
    @java.lang.Override
    public boolean getEnd() {
      return end_;
    }
    /**
     * <code>optional bool end = 7;</code>
     * @param value The end to set.
     * @return This builder for chaining.
     */
    public Builder setEnd(boolean value) {

      end_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>optional bool end = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearEnd() {
      bitField0_ = (bitField0_ & ~0x00000040);
      end_ = false;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.GodDemonRoomUpdateSync)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.GodDemonRoomUpdateSync)
  private static final xddq.pb.GodDemonRoomUpdateSync DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.GodDemonRoomUpdateSync();
  }

  public static xddq.pb.GodDemonRoomUpdateSync getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<GodDemonRoomUpdateSync>
      PARSER = new com.google.protobuf.AbstractParser<GodDemonRoomUpdateSync>() {
    @java.lang.Override
    public GodDemonRoomUpdateSync parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<GodDemonRoomUpdateSync> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<GodDemonRoomUpdateSync> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.GodDemonRoomUpdateSync getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

