// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.MagicTreasureResultItemMsg}
 */
public final class MagicTreasureResultItemMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.MagicTreasureResultItemMsg)
    MagicTreasureResultItemMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      MagicTreasureResultItemMsg.class.getName());
  }
  // Use MagicTreasureResultItemMsg.newBuilder() to construct.
  private MagicTreasureResultItemMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private MagicTreasureResultItemMsg() {
    reward_ = "";
    item_ = "";
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_MagicTreasureResultItemMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_MagicTreasureResultItemMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.MagicTreasureResultItemMsg.class, xddq.pb.MagicTreasureResultItemMsg.Builder.class);
  }

  private int bitField0_;
  public static final int REWARD_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private volatile java.lang.Object reward_ = "";
  /**
   * <code>required string reward = 1;</code>
   * @return Whether the reward field is set.
   */
  @java.lang.Override
  public boolean hasReward() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required string reward = 1;</code>
   * @return The reward.
   */
  @java.lang.Override
  public java.lang.String getReward() {
    java.lang.Object ref = reward_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        reward_ = s;
      }
      return s;
    }
  }
  /**
   * <code>required string reward = 1;</code>
   * @return The bytes for reward.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getRewardBytes() {
    java.lang.Object ref = reward_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      reward_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ITEM_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private volatile java.lang.Object item_ = "";
  /**
   * <code>optional string item = 2;</code>
   * @return Whether the item field is set.
   */
  @java.lang.Override
  public boolean hasItem() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional string item = 2;</code>
   * @return The item.
   */
  @java.lang.Override
  public java.lang.String getItem() {
    java.lang.Object ref = item_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        item_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string item = 2;</code>
   * @return The bytes for item.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getItemBytes() {
    java.lang.Object ref = item_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      item_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasReward()) {
      memoizedIsInitialized = 0;
      return false;
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 1, reward_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 2, item_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(1, reward_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(2, item_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.MagicTreasureResultItemMsg)) {
      return super.equals(obj);
    }
    xddq.pb.MagicTreasureResultItemMsg other = (xddq.pb.MagicTreasureResultItemMsg) obj;

    if (hasReward() != other.hasReward()) return false;
    if (hasReward()) {
      if (!getReward()
          .equals(other.getReward())) return false;
    }
    if (hasItem() != other.hasItem()) return false;
    if (hasItem()) {
      if (!getItem()
          .equals(other.getItem())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasReward()) {
      hash = (37 * hash) + REWARD_FIELD_NUMBER;
      hash = (53 * hash) + getReward().hashCode();
    }
    if (hasItem()) {
      hash = (37 * hash) + ITEM_FIELD_NUMBER;
      hash = (53 * hash) + getItem().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.MagicTreasureResultItemMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.MagicTreasureResultItemMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.MagicTreasureResultItemMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.MagicTreasureResultItemMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.MagicTreasureResultItemMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.MagicTreasureResultItemMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.MagicTreasureResultItemMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.MagicTreasureResultItemMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.MagicTreasureResultItemMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.MagicTreasureResultItemMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.MagicTreasureResultItemMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.MagicTreasureResultItemMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.MagicTreasureResultItemMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.MagicTreasureResultItemMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.MagicTreasureResultItemMsg)
      xddq.pb.MagicTreasureResultItemMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_MagicTreasureResultItemMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_MagicTreasureResultItemMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.MagicTreasureResultItemMsg.class, xddq.pb.MagicTreasureResultItemMsg.Builder.class);
    }

    // Construct using xddq.pb.MagicTreasureResultItemMsg.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      reward_ = "";
      item_ = "";
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_MagicTreasureResultItemMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.MagicTreasureResultItemMsg getDefaultInstanceForType() {
      return xddq.pb.MagicTreasureResultItemMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.MagicTreasureResultItemMsg build() {
      xddq.pb.MagicTreasureResultItemMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.MagicTreasureResultItemMsg buildPartial() {
      xddq.pb.MagicTreasureResultItemMsg result = new xddq.pb.MagicTreasureResultItemMsg(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.MagicTreasureResultItemMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.reward_ = reward_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.item_ = item_;
        to_bitField0_ |= 0x00000002;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.MagicTreasureResultItemMsg) {
        return mergeFrom((xddq.pb.MagicTreasureResultItemMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.MagicTreasureResultItemMsg other) {
      if (other == xddq.pb.MagicTreasureResultItemMsg.getDefaultInstance()) return this;
      if (other.hasReward()) {
        reward_ = other.reward_;
        bitField0_ |= 0x00000001;
        onChanged();
      }
      if (other.hasItem()) {
        item_ = other.item_;
        bitField0_ |= 0x00000002;
        onChanged();
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasReward()) {
        return false;
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              reward_ = input.readBytes();
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 18: {
              item_ = input.readBytes();
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.lang.Object reward_ = "";
    /**
     * <code>required string reward = 1;</code>
     * @return Whether the reward field is set.
     */
    public boolean hasReward() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required string reward = 1;</code>
     * @return The reward.
     */
    public java.lang.String getReward() {
      java.lang.Object ref = reward_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          reward_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>required string reward = 1;</code>
     * @return The bytes for reward.
     */
    public com.google.protobuf.ByteString
        getRewardBytes() {
      java.lang.Object ref = reward_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        reward_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>required string reward = 1;</code>
     * @param value The reward to set.
     * @return This builder for chaining.
     */
    public Builder setReward(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      reward_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required string reward = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearReward() {
      reward_ = getDefaultInstance().getReward();
      bitField0_ = (bitField0_ & ~0x00000001);
      onChanged();
      return this;
    }
    /**
     * <code>required string reward = 1;</code>
     * @param value The bytes for reward to set.
     * @return This builder for chaining.
     */
    public Builder setRewardBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      reward_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }

    private java.lang.Object item_ = "";
    /**
     * <code>optional string item = 2;</code>
     * @return Whether the item field is set.
     */
    public boolean hasItem() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional string item = 2;</code>
     * @return The item.
     */
    public java.lang.String getItem() {
      java.lang.Object ref = item_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          item_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string item = 2;</code>
     * @return The bytes for item.
     */
    public com.google.protobuf.ByteString
        getItemBytes() {
      java.lang.Object ref = item_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        item_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string item = 2;</code>
     * @param value The item to set.
     * @return This builder for chaining.
     */
    public Builder setItem(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      item_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional string item = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearItem() {
      item_ = getDefaultInstance().getItem();
      bitField0_ = (bitField0_ & ~0x00000002);
      onChanged();
      return this;
    }
    /**
     * <code>optional string item = 2;</code>
     * @param value The bytes for item to set.
     * @return This builder for chaining.
     */
    public Builder setItemBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      item_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.MagicTreasureResultItemMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.MagicTreasureResultItemMsg)
  private static final xddq.pb.MagicTreasureResultItemMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.MagicTreasureResultItemMsg();
  }

  public static xddq.pb.MagicTreasureResultItemMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<MagicTreasureResultItemMsg>
      PARSER = new com.google.protobuf.AbstractParser<MagicTreasureResultItemMsg>() {
    @java.lang.Override
    public MagicTreasureResultItemMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<MagicTreasureResultItemMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<MagicTreasureResultItemMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.MagicTreasureResultItemMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

