// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.HolyLandBaseInfoRespMsg}
 */
public final class HolyLandBaseInfoRespMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.HolyLandBaseInfoRespMsg)
    HolyLandBaseInfoRespMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      HolyLandBaseInfoRespMsg.class.getName());
  }
  // Use HolyLandBaseInfoRespMsg.newBuilder() to construct.
  private HolyLandBaseInfoRespMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private HolyLandBaseInfoRespMsg() {
    gameTypeGroupDataArray_ = java.util.Collections.emptyList();
    top3Union_ = java.util.Collections.emptyList();
    unionServerIdList_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_HolyLandBaseInfoRespMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_HolyLandBaseInfoRespMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.HolyLandBaseInfoRespMsg.class, xddq.pb.HolyLandBaseInfoRespMsg.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int SELFRANK_FIELD_NUMBER = 2;
  private int selfRank_ = 0;
  /**
   * <code>optional int32 selfRank = 2;</code>
   * @return Whether the selfRank field is set.
   */
  @java.lang.Override
  public boolean hasSelfRank() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int32 selfRank = 2;</code>
   * @return The selfRank.
   */
  @java.lang.Override
  public int getSelfRank() {
    return selfRank_;
  }

  public static final int GROUP_FIELD_NUMBER = 3;
  private int group_ = 0;
  /**
   * <code>optional int32 group = 3;</code>
   * @return Whether the group field is set.
   */
  @java.lang.Override
  public boolean hasGroup() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional int32 group = 3;</code>
   * @return The group.
   */
  @java.lang.Override
  public int getGroup() {
    return group_;
  }

  public static final int GAMETYPE_FIELD_NUMBER = 4;
  private int gameType_ = 0;
  /**
   * <code>optional int32 gameType = 4;</code>
   * @return Whether the gameType field is set.
   */
  @java.lang.Override
  public boolean hasGameType() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional int32 gameType = 4;</code>
   * @return The gameType.
   */
  @java.lang.Override
  public int getGameType() {
    return gameType_;
  }

  public static final int UNIONID_FIELD_NUMBER = 5;
  private long unionId_ = 0L;
  /**
   * <code>optional int64 unionId = 5;</code>
   * @return Whether the unionId field is set.
   */
  @java.lang.Override
  public boolean hasUnionId() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>optional int64 unionId = 5;</code>
   * @return The unionId.
   */
  @java.lang.Override
  public long getUnionId() {
    return unionId_;
  }

  public static final int POSITION_FIELD_NUMBER = 6;
  private int position_ = 0;
  /**
   * <code>optional int32 position = 6;</code>
   * @return Whether the position field is set.
   */
  @java.lang.Override
  public boolean hasPosition() {
    return ((bitField0_ & 0x00000020) != 0);
  }
  /**
   * <code>optional int32 position = 6;</code>
   * @return The position.
   */
  @java.lang.Override
  public int getPosition() {
    return position_;
  }

  public static final int ROUNDSETTLEINFO_FIELD_NUMBER = 7;
  private xddq.pb.HolyLandRoundSettleData roundSettleInfo_;
  /**
   * <code>optional .xddq.pb.HolyLandRoundSettleData roundSettleInfo = 7;</code>
   * @return Whether the roundSettleInfo field is set.
   */
  @java.lang.Override
  public boolean hasRoundSettleInfo() {
    return ((bitField0_ & 0x00000040) != 0);
  }
  /**
   * <code>optional .xddq.pb.HolyLandRoundSettleData roundSettleInfo = 7;</code>
   * @return The roundSettleInfo.
   */
  @java.lang.Override
  public xddq.pb.HolyLandRoundSettleData getRoundSettleInfo() {
    return roundSettleInfo_ == null ? xddq.pb.HolyLandRoundSettleData.getDefaultInstance() : roundSettleInfo_;
  }
  /**
   * <code>optional .xddq.pb.HolyLandRoundSettleData roundSettleInfo = 7;</code>
   */
  @java.lang.Override
  public xddq.pb.HolyLandRoundSettleDataOrBuilder getRoundSettleInfoOrBuilder() {
    return roundSettleInfo_ == null ? xddq.pb.HolyLandRoundSettleData.getDefaultInstance() : roundSettleInfo_;
  }

  public static final int GAMETYPEGROUPDATAARRAY_FIELD_NUMBER = 8;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.HolyLandGameTypeGroupData> gameTypeGroupDataArray_;
  /**
   * <code>repeated .xddq.pb.HolyLandGameTypeGroupData gameTypeGroupDataArray = 8;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.HolyLandGameTypeGroupData> getGameTypeGroupDataArrayList() {
    return gameTypeGroupDataArray_;
  }
  /**
   * <code>repeated .xddq.pb.HolyLandGameTypeGroupData gameTypeGroupDataArray = 8;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.HolyLandGameTypeGroupDataOrBuilder> 
      getGameTypeGroupDataArrayOrBuilderList() {
    return gameTypeGroupDataArray_;
  }
  /**
   * <code>repeated .xddq.pb.HolyLandGameTypeGroupData gameTypeGroupDataArray = 8;</code>
   */
  @java.lang.Override
  public int getGameTypeGroupDataArrayCount() {
    return gameTypeGroupDataArray_.size();
  }
  /**
   * <code>repeated .xddq.pb.HolyLandGameTypeGroupData gameTypeGroupDataArray = 8;</code>
   */
  @java.lang.Override
  public xddq.pb.HolyLandGameTypeGroupData getGameTypeGroupDataArray(int index) {
    return gameTypeGroupDataArray_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.HolyLandGameTypeGroupData gameTypeGroupDataArray = 8;</code>
   */
  @java.lang.Override
  public xddq.pb.HolyLandGameTypeGroupDataOrBuilder getGameTypeGroupDataArrayOrBuilder(
      int index) {
    return gameTypeGroupDataArray_.get(index);
  }

  public static final int TOP3UNION_FIELD_NUMBER = 10;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.UnionBaseData> top3Union_;
  /**
   * <code>repeated .xddq.pb.UnionBaseData top3Union = 10;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.UnionBaseData> getTop3UnionList() {
    return top3Union_;
  }
  /**
   * <code>repeated .xddq.pb.UnionBaseData top3Union = 10;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.UnionBaseDataOrBuilder> 
      getTop3UnionOrBuilderList() {
    return top3Union_;
  }
  /**
   * <code>repeated .xddq.pb.UnionBaseData top3Union = 10;</code>
   */
  @java.lang.Override
  public int getTop3UnionCount() {
    return top3Union_.size();
  }
  /**
   * <code>repeated .xddq.pb.UnionBaseData top3Union = 10;</code>
   */
  @java.lang.Override
  public xddq.pb.UnionBaseData getTop3Union(int index) {
    return top3Union_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.UnionBaseData top3Union = 10;</code>
   */
  @java.lang.Override
  public xddq.pb.UnionBaseDataOrBuilder getTop3UnionOrBuilder(
      int index) {
    return top3Union_.get(index);
  }

  public static final int CANWORSHIP_FIELD_NUMBER = 11;
  private boolean canWorship_ = false;
  /**
   * <code>optional bool canWorship = 11;</code>
   * @return Whether the canWorship field is set.
   */
  @java.lang.Override
  public boolean hasCanWorship() {
    return ((bitField0_ & 0x00000080) != 0);
  }
  /**
   * <code>optional bool canWorship = 11;</code>
   * @return The canWorship.
   */
  @java.lang.Override
  public boolean getCanWorship() {
    return canWorship_;
  }

  public static final int BIGGROUP_FIELD_NUMBER = 12;
  private int bigGroup_ = 0;
  /**
   * <code>optional int32 bigGroup = 12;</code>
   * @return Whether the bigGroup field is set.
   */
  @java.lang.Override
  public boolean hasBigGroup() {
    return ((bitField0_ & 0x00000100) != 0);
  }
  /**
   * <code>optional int32 bigGroup = 12;</code>
   * @return The bigGroup.
   */
  @java.lang.Override
  public int getBigGroup() {
    return bigGroup_;
  }

  public static final int ACTIVE_FIELD_NUMBER = 13;
  private int active_ = 0;
  /**
   * <code>optional int32 active = 13;</code>
   * @return Whether the active field is set.
   */
  @java.lang.Override
  public boolean hasActive() {
    return ((bitField0_ & 0x00000200) != 0);
  }
  /**
   * <code>optional int32 active = 13;</code>
   * @return The active.
   */
  @java.lang.Override
  public int getActive() {
    return active_;
  }

  public static final int NEXTWEEKGAMETYPE_FIELD_NUMBER = 14;
  private int nextWeekGameType_ = 0;
  /**
   * <code>optional int32 nextWeekGameType = 14;</code>
   * @return Whether the nextWeekGameType field is set.
   */
  @java.lang.Override
  public boolean hasNextWeekGameType() {
    return ((bitField0_ & 0x00000400) != 0);
  }
  /**
   * <code>optional int32 nextWeekGameType = 14;</code>
   * @return The nextWeekGameType.
   */
  @java.lang.Override
  public int getNextWeekGameType() {
    return nextWeekGameType_;
  }

  public static final int GROUPTYPE_FIELD_NUMBER = 15;
  private int groupType_ = 0;
  /**
   * <code>optional int32 groupType = 15;</code>
   * @return Whether the groupType field is set.
   */
  @java.lang.Override
  public boolean hasGroupType() {
    return ((bitField0_ & 0x00000800) != 0);
  }
  /**
   * <code>optional int32 groupType = 15;</code>
   * @return The groupType.
   */
  @java.lang.Override
  public int getGroupType() {
    return groupType_;
  }

  public static final int UNIONSERVERIDLIST_FIELD_NUMBER = 16;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.UnionNameServerIdData> unionServerIdList_;
  /**
   * <code>repeated .xddq.pb.UnionNameServerIdData unionServerIdList = 16;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.UnionNameServerIdData> getUnionServerIdListList() {
    return unionServerIdList_;
  }
  /**
   * <code>repeated .xddq.pb.UnionNameServerIdData unionServerIdList = 16;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.UnionNameServerIdDataOrBuilder> 
      getUnionServerIdListOrBuilderList() {
    return unionServerIdList_;
  }
  /**
   * <code>repeated .xddq.pb.UnionNameServerIdData unionServerIdList = 16;</code>
   */
  @java.lang.Override
  public int getUnionServerIdListCount() {
    return unionServerIdList_.size();
  }
  /**
   * <code>repeated .xddq.pb.UnionNameServerIdData unionServerIdList = 16;</code>
   */
  @java.lang.Override
  public xddq.pb.UnionNameServerIdData getUnionServerIdList(int index) {
    return unionServerIdList_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.UnionNameServerIdData unionServerIdList = 16;</code>
   */
  @java.lang.Override
  public xddq.pb.UnionNameServerIdDataOrBuilder getUnionServerIdListOrBuilder(
      int index) {
    return unionServerIdList_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(2, selfRank_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt32(3, group_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt32(4, gameType_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      output.writeInt64(5, unionId_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      output.writeInt32(6, position_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      output.writeMessage(7, getRoundSettleInfo());
    }
    for (int i = 0; i < gameTypeGroupDataArray_.size(); i++) {
      output.writeMessage(8, gameTypeGroupDataArray_.get(i));
    }
    for (int i = 0; i < top3Union_.size(); i++) {
      output.writeMessage(10, top3Union_.get(i));
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      output.writeBool(11, canWorship_);
    }
    if (((bitField0_ & 0x00000100) != 0)) {
      output.writeInt32(12, bigGroup_);
    }
    if (((bitField0_ & 0x00000200) != 0)) {
      output.writeInt32(13, active_);
    }
    if (((bitField0_ & 0x00000400) != 0)) {
      output.writeInt32(14, nextWeekGameType_);
    }
    if (((bitField0_ & 0x00000800) != 0)) {
      output.writeInt32(15, groupType_);
    }
    for (int i = 0; i < unionServerIdList_.size(); i++) {
      output.writeMessage(16, unionServerIdList_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, selfRank_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(3, group_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(4, gameType_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(5, unionId_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(6, position_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(7, getRoundSettleInfo());
    }
    for (int i = 0; i < gameTypeGroupDataArray_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(8, gameTypeGroupDataArray_.get(i));
    }
    for (int i = 0; i < top3Union_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(10, top3Union_.get(i));
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(11, canWorship_);
    }
    if (((bitField0_ & 0x00000100) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(12, bigGroup_);
    }
    if (((bitField0_ & 0x00000200) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(13, active_);
    }
    if (((bitField0_ & 0x00000400) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(14, nextWeekGameType_);
    }
    if (((bitField0_ & 0x00000800) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(15, groupType_);
    }
    for (int i = 0; i < unionServerIdList_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(16, unionServerIdList_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.HolyLandBaseInfoRespMsg)) {
      return super.equals(obj);
    }
    xddq.pb.HolyLandBaseInfoRespMsg other = (xddq.pb.HolyLandBaseInfoRespMsg) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (hasSelfRank() != other.hasSelfRank()) return false;
    if (hasSelfRank()) {
      if (getSelfRank()
          != other.getSelfRank()) return false;
    }
    if (hasGroup() != other.hasGroup()) return false;
    if (hasGroup()) {
      if (getGroup()
          != other.getGroup()) return false;
    }
    if (hasGameType() != other.hasGameType()) return false;
    if (hasGameType()) {
      if (getGameType()
          != other.getGameType()) return false;
    }
    if (hasUnionId() != other.hasUnionId()) return false;
    if (hasUnionId()) {
      if (getUnionId()
          != other.getUnionId()) return false;
    }
    if (hasPosition() != other.hasPosition()) return false;
    if (hasPosition()) {
      if (getPosition()
          != other.getPosition()) return false;
    }
    if (hasRoundSettleInfo() != other.hasRoundSettleInfo()) return false;
    if (hasRoundSettleInfo()) {
      if (!getRoundSettleInfo()
          .equals(other.getRoundSettleInfo())) return false;
    }
    if (!getGameTypeGroupDataArrayList()
        .equals(other.getGameTypeGroupDataArrayList())) return false;
    if (!getTop3UnionList()
        .equals(other.getTop3UnionList())) return false;
    if (hasCanWorship() != other.hasCanWorship()) return false;
    if (hasCanWorship()) {
      if (getCanWorship()
          != other.getCanWorship()) return false;
    }
    if (hasBigGroup() != other.hasBigGroup()) return false;
    if (hasBigGroup()) {
      if (getBigGroup()
          != other.getBigGroup()) return false;
    }
    if (hasActive() != other.hasActive()) return false;
    if (hasActive()) {
      if (getActive()
          != other.getActive()) return false;
    }
    if (hasNextWeekGameType() != other.hasNextWeekGameType()) return false;
    if (hasNextWeekGameType()) {
      if (getNextWeekGameType()
          != other.getNextWeekGameType()) return false;
    }
    if (hasGroupType() != other.hasGroupType()) return false;
    if (hasGroupType()) {
      if (getGroupType()
          != other.getGroupType()) return false;
    }
    if (!getUnionServerIdListList()
        .equals(other.getUnionServerIdListList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (hasSelfRank()) {
      hash = (37 * hash) + SELFRANK_FIELD_NUMBER;
      hash = (53 * hash) + getSelfRank();
    }
    if (hasGroup()) {
      hash = (37 * hash) + GROUP_FIELD_NUMBER;
      hash = (53 * hash) + getGroup();
    }
    if (hasGameType()) {
      hash = (37 * hash) + GAMETYPE_FIELD_NUMBER;
      hash = (53 * hash) + getGameType();
    }
    if (hasUnionId()) {
      hash = (37 * hash) + UNIONID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getUnionId());
    }
    if (hasPosition()) {
      hash = (37 * hash) + POSITION_FIELD_NUMBER;
      hash = (53 * hash) + getPosition();
    }
    if (hasRoundSettleInfo()) {
      hash = (37 * hash) + ROUNDSETTLEINFO_FIELD_NUMBER;
      hash = (53 * hash) + getRoundSettleInfo().hashCode();
    }
    if (getGameTypeGroupDataArrayCount() > 0) {
      hash = (37 * hash) + GAMETYPEGROUPDATAARRAY_FIELD_NUMBER;
      hash = (53 * hash) + getGameTypeGroupDataArrayList().hashCode();
    }
    if (getTop3UnionCount() > 0) {
      hash = (37 * hash) + TOP3UNION_FIELD_NUMBER;
      hash = (53 * hash) + getTop3UnionList().hashCode();
    }
    if (hasCanWorship()) {
      hash = (37 * hash) + CANWORSHIP_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getCanWorship());
    }
    if (hasBigGroup()) {
      hash = (37 * hash) + BIGGROUP_FIELD_NUMBER;
      hash = (53 * hash) + getBigGroup();
    }
    if (hasActive()) {
      hash = (37 * hash) + ACTIVE_FIELD_NUMBER;
      hash = (53 * hash) + getActive();
    }
    if (hasNextWeekGameType()) {
      hash = (37 * hash) + NEXTWEEKGAMETYPE_FIELD_NUMBER;
      hash = (53 * hash) + getNextWeekGameType();
    }
    if (hasGroupType()) {
      hash = (37 * hash) + GROUPTYPE_FIELD_NUMBER;
      hash = (53 * hash) + getGroupType();
    }
    if (getUnionServerIdListCount() > 0) {
      hash = (37 * hash) + UNIONSERVERIDLIST_FIELD_NUMBER;
      hash = (53 * hash) + getUnionServerIdListList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.HolyLandBaseInfoRespMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.HolyLandBaseInfoRespMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.HolyLandBaseInfoRespMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.HolyLandBaseInfoRespMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.HolyLandBaseInfoRespMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.HolyLandBaseInfoRespMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.HolyLandBaseInfoRespMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.HolyLandBaseInfoRespMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.HolyLandBaseInfoRespMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.HolyLandBaseInfoRespMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.HolyLandBaseInfoRespMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.HolyLandBaseInfoRespMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.HolyLandBaseInfoRespMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.HolyLandBaseInfoRespMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.HolyLandBaseInfoRespMsg)
      xddq.pb.HolyLandBaseInfoRespMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_HolyLandBaseInfoRespMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_HolyLandBaseInfoRespMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.HolyLandBaseInfoRespMsg.class, xddq.pb.HolyLandBaseInfoRespMsg.Builder.class);
    }

    // Construct using xddq.pb.HolyLandBaseInfoRespMsg.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetRoundSettleInfoFieldBuilder();
        internalGetGameTypeGroupDataArrayFieldBuilder();
        internalGetTop3UnionFieldBuilder();
        internalGetUnionServerIdListFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      selfRank_ = 0;
      group_ = 0;
      gameType_ = 0;
      unionId_ = 0L;
      position_ = 0;
      roundSettleInfo_ = null;
      if (roundSettleInfoBuilder_ != null) {
        roundSettleInfoBuilder_.dispose();
        roundSettleInfoBuilder_ = null;
      }
      if (gameTypeGroupDataArrayBuilder_ == null) {
        gameTypeGroupDataArray_ = java.util.Collections.emptyList();
      } else {
        gameTypeGroupDataArray_ = null;
        gameTypeGroupDataArrayBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000080);
      if (top3UnionBuilder_ == null) {
        top3Union_ = java.util.Collections.emptyList();
      } else {
        top3Union_ = null;
        top3UnionBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000100);
      canWorship_ = false;
      bigGroup_ = 0;
      active_ = 0;
      nextWeekGameType_ = 0;
      groupType_ = 0;
      if (unionServerIdListBuilder_ == null) {
        unionServerIdList_ = java.util.Collections.emptyList();
      } else {
        unionServerIdList_ = null;
        unionServerIdListBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00004000);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_HolyLandBaseInfoRespMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.HolyLandBaseInfoRespMsg getDefaultInstanceForType() {
      return xddq.pb.HolyLandBaseInfoRespMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.HolyLandBaseInfoRespMsg build() {
      xddq.pb.HolyLandBaseInfoRespMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.HolyLandBaseInfoRespMsg buildPartial() {
      xddq.pb.HolyLandBaseInfoRespMsg result = new xddq.pb.HolyLandBaseInfoRespMsg(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.HolyLandBaseInfoRespMsg result) {
      if (gameTypeGroupDataArrayBuilder_ == null) {
        if (((bitField0_ & 0x00000080) != 0)) {
          gameTypeGroupDataArray_ = java.util.Collections.unmodifiableList(gameTypeGroupDataArray_);
          bitField0_ = (bitField0_ & ~0x00000080);
        }
        result.gameTypeGroupDataArray_ = gameTypeGroupDataArray_;
      } else {
        result.gameTypeGroupDataArray_ = gameTypeGroupDataArrayBuilder_.build();
      }
      if (top3UnionBuilder_ == null) {
        if (((bitField0_ & 0x00000100) != 0)) {
          top3Union_ = java.util.Collections.unmodifiableList(top3Union_);
          bitField0_ = (bitField0_ & ~0x00000100);
        }
        result.top3Union_ = top3Union_;
      } else {
        result.top3Union_ = top3UnionBuilder_.build();
      }
      if (unionServerIdListBuilder_ == null) {
        if (((bitField0_ & 0x00004000) != 0)) {
          unionServerIdList_ = java.util.Collections.unmodifiableList(unionServerIdList_);
          bitField0_ = (bitField0_ & ~0x00004000);
        }
        result.unionServerIdList_ = unionServerIdList_;
      } else {
        result.unionServerIdList_ = unionServerIdListBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.HolyLandBaseInfoRespMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.selfRank_ = selfRank_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.group_ = group_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.gameType_ = gameType_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.unionId_ = unionId_;
        to_bitField0_ |= 0x00000010;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.position_ = position_;
        to_bitField0_ |= 0x00000020;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.roundSettleInfo_ = roundSettleInfoBuilder_ == null
            ? roundSettleInfo_
            : roundSettleInfoBuilder_.build();
        to_bitField0_ |= 0x00000040;
      }
      if (((from_bitField0_ & 0x00000200) != 0)) {
        result.canWorship_ = canWorship_;
        to_bitField0_ |= 0x00000080;
      }
      if (((from_bitField0_ & 0x00000400) != 0)) {
        result.bigGroup_ = bigGroup_;
        to_bitField0_ |= 0x00000100;
      }
      if (((from_bitField0_ & 0x00000800) != 0)) {
        result.active_ = active_;
        to_bitField0_ |= 0x00000200;
      }
      if (((from_bitField0_ & 0x00001000) != 0)) {
        result.nextWeekGameType_ = nextWeekGameType_;
        to_bitField0_ |= 0x00000400;
      }
      if (((from_bitField0_ & 0x00002000) != 0)) {
        result.groupType_ = groupType_;
        to_bitField0_ |= 0x00000800;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.HolyLandBaseInfoRespMsg) {
        return mergeFrom((xddq.pb.HolyLandBaseInfoRespMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.HolyLandBaseInfoRespMsg other) {
      if (other == xddq.pb.HolyLandBaseInfoRespMsg.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (other.hasSelfRank()) {
        setSelfRank(other.getSelfRank());
      }
      if (other.hasGroup()) {
        setGroup(other.getGroup());
      }
      if (other.hasGameType()) {
        setGameType(other.getGameType());
      }
      if (other.hasUnionId()) {
        setUnionId(other.getUnionId());
      }
      if (other.hasPosition()) {
        setPosition(other.getPosition());
      }
      if (other.hasRoundSettleInfo()) {
        mergeRoundSettleInfo(other.getRoundSettleInfo());
      }
      if (gameTypeGroupDataArrayBuilder_ == null) {
        if (!other.gameTypeGroupDataArray_.isEmpty()) {
          if (gameTypeGroupDataArray_.isEmpty()) {
            gameTypeGroupDataArray_ = other.gameTypeGroupDataArray_;
            bitField0_ = (bitField0_ & ~0x00000080);
          } else {
            ensureGameTypeGroupDataArrayIsMutable();
            gameTypeGroupDataArray_.addAll(other.gameTypeGroupDataArray_);
          }
          onChanged();
        }
      } else {
        if (!other.gameTypeGroupDataArray_.isEmpty()) {
          if (gameTypeGroupDataArrayBuilder_.isEmpty()) {
            gameTypeGroupDataArrayBuilder_.dispose();
            gameTypeGroupDataArrayBuilder_ = null;
            gameTypeGroupDataArray_ = other.gameTypeGroupDataArray_;
            bitField0_ = (bitField0_ & ~0x00000080);
            gameTypeGroupDataArrayBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetGameTypeGroupDataArrayFieldBuilder() : null;
          } else {
            gameTypeGroupDataArrayBuilder_.addAllMessages(other.gameTypeGroupDataArray_);
          }
        }
      }
      if (top3UnionBuilder_ == null) {
        if (!other.top3Union_.isEmpty()) {
          if (top3Union_.isEmpty()) {
            top3Union_ = other.top3Union_;
            bitField0_ = (bitField0_ & ~0x00000100);
          } else {
            ensureTop3UnionIsMutable();
            top3Union_.addAll(other.top3Union_);
          }
          onChanged();
        }
      } else {
        if (!other.top3Union_.isEmpty()) {
          if (top3UnionBuilder_.isEmpty()) {
            top3UnionBuilder_.dispose();
            top3UnionBuilder_ = null;
            top3Union_ = other.top3Union_;
            bitField0_ = (bitField0_ & ~0x00000100);
            top3UnionBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetTop3UnionFieldBuilder() : null;
          } else {
            top3UnionBuilder_.addAllMessages(other.top3Union_);
          }
        }
      }
      if (other.hasCanWorship()) {
        setCanWorship(other.getCanWorship());
      }
      if (other.hasBigGroup()) {
        setBigGroup(other.getBigGroup());
      }
      if (other.hasActive()) {
        setActive(other.getActive());
      }
      if (other.hasNextWeekGameType()) {
        setNextWeekGameType(other.getNextWeekGameType());
      }
      if (other.hasGroupType()) {
        setGroupType(other.getGroupType());
      }
      if (unionServerIdListBuilder_ == null) {
        if (!other.unionServerIdList_.isEmpty()) {
          if (unionServerIdList_.isEmpty()) {
            unionServerIdList_ = other.unionServerIdList_;
            bitField0_ = (bitField0_ & ~0x00004000);
          } else {
            ensureUnionServerIdListIsMutable();
            unionServerIdList_.addAll(other.unionServerIdList_);
          }
          onChanged();
        }
      } else {
        if (!other.unionServerIdList_.isEmpty()) {
          if (unionServerIdListBuilder_.isEmpty()) {
            unionServerIdListBuilder_.dispose();
            unionServerIdListBuilder_ = null;
            unionServerIdList_ = other.unionServerIdList_;
            bitField0_ = (bitField0_ & ~0x00004000);
            unionServerIdListBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetUnionServerIdListFieldBuilder() : null;
          } else {
            unionServerIdListBuilder_.addAllMessages(other.unionServerIdList_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              selfRank_ = input.readInt32();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 24: {
              group_ = input.readInt32();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 32: {
              gameType_ = input.readInt32();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            case 40: {
              unionId_ = input.readInt64();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            case 48: {
              position_ = input.readInt32();
              bitField0_ |= 0x00000020;
              break;
            } // case 48
            case 58: {
              input.readMessage(
                  internalGetRoundSettleInfoFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000040;
              break;
            } // case 58
            case 66: {
              xddq.pb.HolyLandGameTypeGroupData m =
                  input.readMessage(
                      xddq.pb.HolyLandGameTypeGroupData.parser(),
                      extensionRegistry);
              if (gameTypeGroupDataArrayBuilder_ == null) {
                ensureGameTypeGroupDataArrayIsMutable();
                gameTypeGroupDataArray_.add(m);
              } else {
                gameTypeGroupDataArrayBuilder_.addMessage(m);
              }
              break;
            } // case 66
            case 82: {
              xddq.pb.UnionBaseData m =
                  input.readMessage(
                      xddq.pb.UnionBaseData.parser(),
                      extensionRegistry);
              if (top3UnionBuilder_ == null) {
                ensureTop3UnionIsMutable();
                top3Union_.add(m);
              } else {
                top3UnionBuilder_.addMessage(m);
              }
              break;
            } // case 82
            case 88: {
              canWorship_ = input.readBool();
              bitField0_ |= 0x00000200;
              break;
            } // case 88
            case 96: {
              bigGroup_ = input.readInt32();
              bitField0_ |= 0x00000400;
              break;
            } // case 96
            case 104: {
              active_ = input.readInt32();
              bitField0_ |= 0x00000800;
              break;
            } // case 104
            case 112: {
              nextWeekGameType_ = input.readInt32();
              bitField0_ |= 0x00001000;
              break;
            } // case 112
            case 120: {
              groupType_ = input.readInt32();
              bitField0_ |= 0x00002000;
              break;
            } // case 120
            case 130: {
              xddq.pb.UnionNameServerIdData m =
                  input.readMessage(
                      xddq.pb.UnionNameServerIdData.parser(),
                      extensionRegistry);
              if (unionServerIdListBuilder_ == null) {
                ensureUnionServerIdListIsMutable();
                unionServerIdList_.add(m);
              } else {
                unionServerIdListBuilder_.addMessage(m);
              }
              break;
            } // case 130
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private int selfRank_ ;
    /**
     * <code>optional int32 selfRank = 2;</code>
     * @return Whether the selfRank field is set.
     */
    @java.lang.Override
    public boolean hasSelfRank() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 selfRank = 2;</code>
     * @return The selfRank.
     */
    @java.lang.Override
    public int getSelfRank() {
      return selfRank_;
    }
    /**
     * <code>optional int32 selfRank = 2;</code>
     * @param value The selfRank to set.
     * @return This builder for chaining.
     */
    public Builder setSelfRank(int value) {

      selfRank_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 selfRank = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearSelfRank() {
      bitField0_ = (bitField0_ & ~0x00000002);
      selfRank_ = 0;
      onChanged();
      return this;
    }

    private int group_ ;
    /**
     * <code>optional int32 group = 3;</code>
     * @return Whether the group field is set.
     */
    @java.lang.Override
    public boolean hasGroup() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int32 group = 3;</code>
     * @return The group.
     */
    @java.lang.Override
    public int getGroup() {
      return group_;
    }
    /**
     * <code>optional int32 group = 3;</code>
     * @param value The group to set.
     * @return This builder for chaining.
     */
    public Builder setGroup(int value) {

      group_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 group = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearGroup() {
      bitField0_ = (bitField0_ & ~0x00000004);
      group_ = 0;
      onChanged();
      return this;
    }

    private int gameType_ ;
    /**
     * <code>optional int32 gameType = 4;</code>
     * @return Whether the gameType field is set.
     */
    @java.lang.Override
    public boolean hasGameType() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int32 gameType = 4;</code>
     * @return The gameType.
     */
    @java.lang.Override
    public int getGameType() {
      return gameType_;
    }
    /**
     * <code>optional int32 gameType = 4;</code>
     * @param value The gameType to set.
     * @return This builder for chaining.
     */
    public Builder setGameType(int value) {

      gameType_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 gameType = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearGameType() {
      bitField0_ = (bitField0_ & ~0x00000008);
      gameType_ = 0;
      onChanged();
      return this;
    }

    private long unionId_ ;
    /**
     * <code>optional int64 unionId = 5;</code>
     * @return Whether the unionId field is set.
     */
    @java.lang.Override
    public boolean hasUnionId() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional int64 unionId = 5;</code>
     * @return The unionId.
     */
    @java.lang.Override
    public long getUnionId() {
      return unionId_;
    }
    /**
     * <code>optional int64 unionId = 5;</code>
     * @param value The unionId to set.
     * @return This builder for chaining.
     */
    public Builder setUnionId(long value) {

      unionId_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 unionId = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearUnionId() {
      bitField0_ = (bitField0_ & ~0x00000010);
      unionId_ = 0L;
      onChanged();
      return this;
    }

    private int position_ ;
    /**
     * <code>optional int32 position = 6;</code>
     * @return Whether the position field is set.
     */
    @java.lang.Override
    public boolean hasPosition() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional int32 position = 6;</code>
     * @return The position.
     */
    @java.lang.Override
    public int getPosition() {
      return position_;
    }
    /**
     * <code>optional int32 position = 6;</code>
     * @param value The position to set.
     * @return This builder for chaining.
     */
    public Builder setPosition(int value) {

      position_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 position = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearPosition() {
      bitField0_ = (bitField0_ & ~0x00000020);
      position_ = 0;
      onChanged();
      return this;
    }

    private xddq.pb.HolyLandRoundSettleData roundSettleInfo_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.HolyLandRoundSettleData, xddq.pb.HolyLandRoundSettleData.Builder, xddq.pb.HolyLandRoundSettleDataOrBuilder> roundSettleInfoBuilder_;
    /**
     * <code>optional .xddq.pb.HolyLandRoundSettleData roundSettleInfo = 7;</code>
     * @return Whether the roundSettleInfo field is set.
     */
    public boolean hasRoundSettleInfo() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <code>optional .xddq.pb.HolyLandRoundSettleData roundSettleInfo = 7;</code>
     * @return The roundSettleInfo.
     */
    public xddq.pb.HolyLandRoundSettleData getRoundSettleInfo() {
      if (roundSettleInfoBuilder_ == null) {
        return roundSettleInfo_ == null ? xddq.pb.HolyLandRoundSettleData.getDefaultInstance() : roundSettleInfo_;
      } else {
        return roundSettleInfoBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.HolyLandRoundSettleData roundSettleInfo = 7;</code>
     */
    public Builder setRoundSettleInfo(xddq.pb.HolyLandRoundSettleData value) {
      if (roundSettleInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        roundSettleInfo_ = value;
      } else {
        roundSettleInfoBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.HolyLandRoundSettleData roundSettleInfo = 7;</code>
     */
    public Builder setRoundSettleInfo(
        xddq.pb.HolyLandRoundSettleData.Builder builderForValue) {
      if (roundSettleInfoBuilder_ == null) {
        roundSettleInfo_ = builderForValue.build();
      } else {
        roundSettleInfoBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.HolyLandRoundSettleData roundSettleInfo = 7;</code>
     */
    public Builder mergeRoundSettleInfo(xddq.pb.HolyLandRoundSettleData value) {
      if (roundSettleInfoBuilder_ == null) {
        if (((bitField0_ & 0x00000040) != 0) &&
          roundSettleInfo_ != null &&
          roundSettleInfo_ != xddq.pb.HolyLandRoundSettleData.getDefaultInstance()) {
          getRoundSettleInfoBuilder().mergeFrom(value);
        } else {
          roundSettleInfo_ = value;
        }
      } else {
        roundSettleInfoBuilder_.mergeFrom(value);
      }
      if (roundSettleInfo_ != null) {
        bitField0_ |= 0x00000040;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.HolyLandRoundSettleData roundSettleInfo = 7;</code>
     */
    public Builder clearRoundSettleInfo() {
      bitField0_ = (bitField0_ & ~0x00000040);
      roundSettleInfo_ = null;
      if (roundSettleInfoBuilder_ != null) {
        roundSettleInfoBuilder_.dispose();
        roundSettleInfoBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.HolyLandRoundSettleData roundSettleInfo = 7;</code>
     */
    public xddq.pb.HolyLandRoundSettleData.Builder getRoundSettleInfoBuilder() {
      bitField0_ |= 0x00000040;
      onChanged();
      return internalGetRoundSettleInfoFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.HolyLandRoundSettleData roundSettleInfo = 7;</code>
     */
    public xddq.pb.HolyLandRoundSettleDataOrBuilder getRoundSettleInfoOrBuilder() {
      if (roundSettleInfoBuilder_ != null) {
        return roundSettleInfoBuilder_.getMessageOrBuilder();
      } else {
        return roundSettleInfo_ == null ?
            xddq.pb.HolyLandRoundSettleData.getDefaultInstance() : roundSettleInfo_;
      }
    }
    /**
     * <code>optional .xddq.pb.HolyLandRoundSettleData roundSettleInfo = 7;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.HolyLandRoundSettleData, xddq.pb.HolyLandRoundSettleData.Builder, xddq.pb.HolyLandRoundSettleDataOrBuilder> 
        internalGetRoundSettleInfoFieldBuilder() {
      if (roundSettleInfoBuilder_ == null) {
        roundSettleInfoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.HolyLandRoundSettleData, xddq.pb.HolyLandRoundSettleData.Builder, xddq.pb.HolyLandRoundSettleDataOrBuilder>(
                getRoundSettleInfo(),
                getParentForChildren(),
                isClean());
        roundSettleInfo_ = null;
      }
      return roundSettleInfoBuilder_;
    }

    private java.util.List<xddq.pb.HolyLandGameTypeGroupData> gameTypeGroupDataArray_ =
      java.util.Collections.emptyList();
    private void ensureGameTypeGroupDataArrayIsMutable() {
      if (!((bitField0_ & 0x00000080) != 0)) {
        gameTypeGroupDataArray_ = new java.util.ArrayList<xddq.pb.HolyLandGameTypeGroupData>(gameTypeGroupDataArray_);
        bitField0_ |= 0x00000080;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.HolyLandGameTypeGroupData, xddq.pb.HolyLandGameTypeGroupData.Builder, xddq.pb.HolyLandGameTypeGroupDataOrBuilder> gameTypeGroupDataArrayBuilder_;

    /**
     * <code>repeated .xddq.pb.HolyLandGameTypeGroupData gameTypeGroupDataArray = 8;</code>
     */
    public java.util.List<xddq.pb.HolyLandGameTypeGroupData> getGameTypeGroupDataArrayList() {
      if (gameTypeGroupDataArrayBuilder_ == null) {
        return java.util.Collections.unmodifiableList(gameTypeGroupDataArray_);
      } else {
        return gameTypeGroupDataArrayBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.HolyLandGameTypeGroupData gameTypeGroupDataArray = 8;</code>
     */
    public int getGameTypeGroupDataArrayCount() {
      if (gameTypeGroupDataArrayBuilder_ == null) {
        return gameTypeGroupDataArray_.size();
      } else {
        return gameTypeGroupDataArrayBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.HolyLandGameTypeGroupData gameTypeGroupDataArray = 8;</code>
     */
    public xddq.pb.HolyLandGameTypeGroupData getGameTypeGroupDataArray(int index) {
      if (gameTypeGroupDataArrayBuilder_ == null) {
        return gameTypeGroupDataArray_.get(index);
      } else {
        return gameTypeGroupDataArrayBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.HolyLandGameTypeGroupData gameTypeGroupDataArray = 8;</code>
     */
    public Builder setGameTypeGroupDataArray(
        int index, xddq.pb.HolyLandGameTypeGroupData value) {
      if (gameTypeGroupDataArrayBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureGameTypeGroupDataArrayIsMutable();
        gameTypeGroupDataArray_.set(index, value);
        onChanged();
      } else {
        gameTypeGroupDataArrayBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.HolyLandGameTypeGroupData gameTypeGroupDataArray = 8;</code>
     */
    public Builder setGameTypeGroupDataArray(
        int index, xddq.pb.HolyLandGameTypeGroupData.Builder builderForValue) {
      if (gameTypeGroupDataArrayBuilder_ == null) {
        ensureGameTypeGroupDataArrayIsMutable();
        gameTypeGroupDataArray_.set(index, builderForValue.build());
        onChanged();
      } else {
        gameTypeGroupDataArrayBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.HolyLandGameTypeGroupData gameTypeGroupDataArray = 8;</code>
     */
    public Builder addGameTypeGroupDataArray(xddq.pb.HolyLandGameTypeGroupData value) {
      if (gameTypeGroupDataArrayBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureGameTypeGroupDataArrayIsMutable();
        gameTypeGroupDataArray_.add(value);
        onChanged();
      } else {
        gameTypeGroupDataArrayBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.HolyLandGameTypeGroupData gameTypeGroupDataArray = 8;</code>
     */
    public Builder addGameTypeGroupDataArray(
        int index, xddq.pb.HolyLandGameTypeGroupData value) {
      if (gameTypeGroupDataArrayBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureGameTypeGroupDataArrayIsMutable();
        gameTypeGroupDataArray_.add(index, value);
        onChanged();
      } else {
        gameTypeGroupDataArrayBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.HolyLandGameTypeGroupData gameTypeGroupDataArray = 8;</code>
     */
    public Builder addGameTypeGroupDataArray(
        xddq.pb.HolyLandGameTypeGroupData.Builder builderForValue) {
      if (gameTypeGroupDataArrayBuilder_ == null) {
        ensureGameTypeGroupDataArrayIsMutable();
        gameTypeGroupDataArray_.add(builderForValue.build());
        onChanged();
      } else {
        gameTypeGroupDataArrayBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.HolyLandGameTypeGroupData gameTypeGroupDataArray = 8;</code>
     */
    public Builder addGameTypeGroupDataArray(
        int index, xddq.pb.HolyLandGameTypeGroupData.Builder builderForValue) {
      if (gameTypeGroupDataArrayBuilder_ == null) {
        ensureGameTypeGroupDataArrayIsMutable();
        gameTypeGroupDataArray_.add(index, builderForValue.build());
        onChanged();
      } else {
        gameTypeGroupDataArrayBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.HolyLandGameTypeGroupData gameTypeGroupDataArray = 8;</code>
     */
    public Builder addAllGameTypeGroupDataArray(
        java.lang.Iterable<? extends xddq.pb.HolyLandGameTypeGroupData> values) {
      if (gameTypeGroupDataArrayBuilder_ == null) {
        ensureGameTypeGroupDataArrayIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, gameTypeGroupDataArray_);
        onChanged();
      } else {
        gameTypeGroupDataArrayBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.HolyLandGameTypeGroupData gameTypeGroupDataArray = 8;</code>
     */
    public Builder clearGameTypeGroupDataArray() {
      if (gameTypeGroupDataArrayBuilder_ == null) {
        gameTypeGroupDataArray_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000080);
        onChanged();
      } else {
        gameTypeGroupDataArrayBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.HolyLandGameTypeGroupData gameTypeGroupDataArray = 8;</code>
     */
    public Builder removeGameTypeGroupDataArray(int index) {
      if (gameTypeGroupDataArrayBuilder_ == null) {
        ensureGameTypeGroupDataArrayIsMutable();
        gameTypeGroupDataArray_.remove(index);
        onChanged();
      } else {
        gameTypeGroupDataArrayBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.HolyLandGameTypeGroupData gameTypeGroupDataArray = 8;</code>
     */
    public xddq.pb.HolyLandGameTypeGroupData.Builder getGameTypeGroupDataArrayBuilder(
        int index) {
      return internalGetGameTypeGroupDataArrayFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.HolyLandGameTypeGroupData gameTypeGroupDataArray = 8;</code>
     */
    public xddq.pb.HolyLandGameTypeGroupDataOrBuilder getGameTypeGroupDataArrayOrBuilder(
        int index) {
      if (gameTypeGroupDataArrayBuilder_ == null) {
        return gameTypeGroupDataArray_.get(index);  } else {
        return gameTypeGroupDataArrayBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.HolyLandGameTypeGroupData gameTypeGroupDataArray = 8;</code>
     */
    public java.util.List<? extends xddq.pb.HolyLandGameTypeGroupDataOrBuilder> 
         getGameTypeGroupDataArrayOrBuilderList() {
      if (gameTypeGroupDataArrayBuilder_ != null) {
        return gameTypeGroupDataArrayBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(gameTypeGroupDataArray_);
      }
    }
    /**
     * <code>repeated .xddq.pb.HolyLandGameTypeGroupData gameTypeGroupDataArray = 8;</code>
     */
    public xddq.pb.HolyLandGameTypeGroupData.Builder addGameTypeGroupDataArrayBuilder() {
      return internalGetGameTypeGroupDataArrayFieldBuilder().addBuilder(
          xddq.pb.HolyLandGameTypeGroupData.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.HolyLandGameTypeGroupData gameTypeGroupDataArray = 8;</code>
     */
    public xddq.pb.HolyLandGameTypeGroupData.Builder addGameTypeGroupDataArrayBuilder(
        int index) {
      return internalGetGameTypeGroupDataArrayFieldBuilder().addBuilder(
          index, xddq.pb.HolyLandGameTypeGroupData.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.HolyLandGameTypeGroupData gameTypeGroupDataArray = 8;</code>
     */
    public java.util.List<xddq.pb.HolyLandGameTypeGroupData.Builder> 
         getGameTypeGroupDataArrayBuilderList() {
      return internalGetGameTypeGroupDataArrayFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.HolyLandGameTypeGroupData, xddq.pb.HolyLandGameTypeGroupData.Builder, xddq.pb.HolyLandGameTypeGroupDataOrBuilder> 
        internalGetGameTypeGroupDataArrayFieldBuilder() {
      if (gameTypeGroupDataArrayBuilder_ == null) {
        gameTypeGroupDataArrayBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.HolyLandGameTypeGroupData, xddq.pb.HolyLandGameTypeGroupData.Builder, xddq.pb.HolyLandGameTypeGroupDataOrBuilder>(
                gameTypeGroupDataArray_,
                ((bitField0_ & 0x00000080) != 0),
                getParentForChildren(),
                isClean());
        gameTypeGroupDataArray_ = null;
      }
      return gameTypeGroupDataArrayBuilder_;
    }

    private java.util.List<xddq.pb.UnionBaseData> top3Union_ =
      java.util.Collections.emptyList();
    private void ensureTop3UnionIsMutable() {
      if (!((bitField0_ & 0x00000100) != 0)) {
        top3Union_ = new java.util.ArrayList<xddq.pb.UnionBaseData>(top3Union_);
        bitField0_ |= 0x00000100;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.UnionBaseData, xddq.pb.UnionBaseData.Builder, xddq.pb.UnionBaseDataOrBuilder> top3UnionBuilder_;

    /**
     * <code>repeated .xddq.pb.UnionBaseData top3Union = 10;</code>
     */
    public java.util.List<xddq.pb.UnionBaseData> getTop3UnionList() {
      if (top3UnionBuilder_ == null) {
        return java.util.Collections.unmodifiableList(top3Union_);
      } else {
        return top3UnionBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.UnionBaseData top3Union = 10;</code>
     */
    public int getTop3UnionCount() {
      if (top3UnionBuilder_ == null) {
        return top3Union_.size();
      } else {
        return top3UnionBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.UnionBaseData top3Union = 10;</code>
     */
    public xddq.pb.UnionBaseData getTop3Union(int index) {
      if (top3UnionBuilder_ == null) {
        return top3Union_.get(index);
      } else {
        return top3UnionBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.UnionBaseData top3Union = 10;</code>
     */
    public Builder setTop3Union(
        int index, xddq.pb.UnionBaseData value) {
      if (top3UnionBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureTop3UnionIsMutable();
        top3Union_.set(index, value);
        onChanged();
      } else {
        top3UnionBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionBaseData top3Union = 10;</code>
     */
    public Builder setTop3Union(
        int index, xddq.pb.UnionBaseData.Builder builderForValue) {
      if (top3UnionBuilder_ == null) {
        ensureTop3UnionIsMutable();
        top3Union_.set(index, builderForValue.build());
        onChanged();
      } else {
        top3UnionBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionBaseData top3Union = 10;</code>
     */
    public Builder addTop3Union(xddq.pb.UnionBaseData value) {
      if (top3UnionBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureTop3UnionIsMutable();
        top3Union_.add(value);
        onChanged();
      } else {
        top3UnionBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionBaseData top3Union = 10;</code>
     */
    public Builder addTop3Union(
        int index, xddq.pb.UnionBaseData value) {
      if (top3UnionBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureTop3UnionIsMutable();
        top3Union_.add(index, value);
        onChanged();
      } else {
        top3UnionBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionBaseData top3Union = 10;</code>
     */
    public Builder addTop3Union(
        xddq.pb.UnionBaseData.Builder builderForValue) {
      if (top3UnionBuilder_ == null) {
        ensureTop3UnionIsMutable();
        top3Union_.add(builderForValue.build());
        onChanged();
      } else {
        top3UnionBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionBaseData top3Union = 10;</code>
     */
    public Builder addTop3Union(
        int index, xddq.pb.UnionBaseData.Builder builderForValue) {
      if (top3UnionBuilder_ == null) {
        ensureTop3UnionIsMutable();
        top3Union_.add(index, builderForValue.build());
        onChanged();
      } else {
        top3UnionBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionBaseData top3Union = 10;</code>
     */
    public Builder addAllTop3Union(
        java.lang.Iterable<? extends xddq.pb.UnionBaseData> values) {
      if (top3UnionBuilder_ == null) {
        ensureTop3UnionIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, top3Union_);
        onChanged();
      } else {
        top3UnionBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionBaseData top3Union = 10;</code>
     */
    public Builder clearTop3Union() {
      if (top3UnionBuilder_ == null) {
        top3Union_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000100);
        onChanged();
      } else {
        top3UnionBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionBaseData top3Union = 10;</code>
     */
    public Builder removeTop3Union(int index) {
      if (top3UnionBuilder_ == null) {
        ensureTop3UnionIsMutable();
        top3Union_.remove(index);
        onChanged();
      } else {
        top3UnionBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionBaseData top3Union = 10;</code>
     */
    public xddq.pb.UnionBaseData.Builder getTop3UnionBuilder(
        int index) {
      return internalGetTop3UnionFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.UnionBaseData top3Union = 10;</code>
     */
    public xddq.pb.UnionBaseDataOrBuilder getTop3UnionOrBuilder(
        int index) {
      if (top3UnionBuilder_ == null) {
        return top3Union_.get(index);  } else {
        return top3UnionBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.UnionBaseData top3Union = 10;</code>
     */
    public java.util.List<? extends xddq.pb.UnionBaseDataOrBuilder> 
         getTop3UnionOrBuilderList() {
      if (top3UnionBuilder_ != null) {
        return top3UnionBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(top3Union_);
      }
    }
    /**
     * <code>repeated .xddq.pb.UnionBaseData top3Union = 10;</code>
     */
    public xddq.pb.UnionBaseData.Builder addTop3UnionBuilder() {
      return internalGetTop3UnionFieldBuilder().addBuilder(
          xddq.pb.UnionBaseData.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.UnionBaseData top3Union = 10;</code>
     */
    public xddq.pb.UnionBaseData.Builder addTop3UnionBuilder(
        int index) {
      return internalGetTop3UnionFieldBuilder().addBuilder(
          index, xddq.pb.UnionBaseData.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.UnionBaseData top3Union = 10;</code>
     */
    public java.util.List<xddq.pb.UnionBaseData.Builder> 
         getTop3UnionBuilderList() {
      return internalGetTop3UnionFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.UnionBaseData, xddq.pb.UnionBaseData.Builder, xddq.pb.UnionBaseDataOrBuilder> 
        internalGetTop3UnionFieldBuilder() {
      if (top3UnionBuilder_ == null) {
        top3UnionBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.UnionBaseData, xddq.pb.UnionBaseData.Builder, xddq.pb.UnionBaseDataOrBuilder>(
                top3Union_,
                ((bitField0_ & 0x00000100) != 0),
                getParentForChildren(),
                isClean());
        top3Union_ = null;
      }
      return top3UnionBuilder_;
    }

    private boolean canWorship_ ;
    /**
     * <code>optional bool canWorship = 11;</code>
     * @return Whether the canWorship field is set.
     */
    @java.lang.Override
    public boolean hasCanWorship() {
      return ((bitField0_ & 0x00000200) != 0);
    }
    /**
     * <code>optional bool canWorship = 11;</code>
     * @return The canWorship.
     */
    @java.lang.Override
    public boolean getCanWorship() {
      return canWorship_;
    }
    /**
     * <code>optional bool canWorship = 11;</code>
     * @param value The canWorship to set.
     * @return This builder for chaining.
     */
    public Builder setCanWorship(boolean value) {

      canWorship_ = value;
      bitField0_ |= 0x00000200;
      onChanged();
      return this;
    }
    /**
     * <code>optional bool canWorship = 11;</code>
     * @return This builder for chaining.
     */
    public Builder clearCanWorship() {
      bitField0_ = (bitField0_ & ~0x00000200);
      canWorship_ = false;
      onChanged();
      return this;
    }

    private int bigGroup_ ;
    /**
     * <code>optional int32 bigGroup = 12;</code>
     * @return Whether the bigGroup field is set.
     */
    @java.lang.Override
    public boolean hasBigGroup() {
      return ((bitField0_ & 0x00000400) != 0);
    }
    /**
     * <code>optional int32 bigGroup = 12;</code>
     * @return The bigGroup.
     */
    @java.lang.Override
    public int getBigGroup() {
      return bigGroup_;
    }
    /**
     * <code>optional int32 bigGroup = 12;</code>
     * @param value The bigGroup to set.
     * @return This builder for chaining.
     */
    public Builder setBigGroup(int value) {

      bigGroup_ = value;
      bitField0_ |= 0x00000400;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 bigGroup = 12;</code>
     * @return This builder for chaining.
     */
    public Builder clearBigGroup() {
      bitField0_ = (bitField0_ & ~0x00000400);
      bigGroup_ = 0;
      onChanged();
      return this;
    }

    private int active_ ;
    /**
     * <code>optional int32 active = 13;</code>
     * @return Whether the active field is set.
     */
    @java.lang.Override
    public boolean hasActive() {
      return ((bitField0_ & 0x00000800) != 0);
    }
    /**
     * <code>optional int32 active = 13;</code>
     * @return The active.
     */
    @java.lang.Override
    public int getActive() {
      return active_;
    }
    /**
     * <code>optional int32 active = 13;</code>
     * @param value The active to set.
     * @return This builder for chaining.
     */
    public Builder setActive(int value) {

      active_ = value;
      bitField0_ |= 0x00000800;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 active = 13;</code>
     * @return This builder for chaining.
     */
    public Builder clearActive() {
      bitField0_ = (bitField0_ & ~0x00000800);
      active_ = 0;
      onChanged();
      return this;
    }

    private int nextWeekGameType_ ;
    /**
     * <code>optional int32 nextWeekGameType = 14;</code>
     * @return Whether the nextWeekGameType field is set.
     */
    @java.lang.Override
    public boolean hasNextWeekGameType() {
      return ((bitField0_ & 0x00001000) != 0);
    }
    /**
     * <code>optional int32 nextWeekGameType = 14;</code>
     * @return The nextWeekGameType.
     */
    @java.lang.Override
    public int getNextWeekGameType() {
      return nextWeekGameType_;
    }
    /**
     * <code>optional int32 nextWeekGameType = 14;</code>
     * @param value The nextWeekGameType to set.
     * @return This builder for chaining.
     */
    public Builder setNextWeekGameType(int value) {

      nextWeekGameType_ = value;
      bitField0_ |= 0x00001000;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 nextWeekGameType = 14;</code>
     * @return This builder for chaining.
     */
    public Builder clearNextWeekGameType() {
      bitField0_ = (bitField0_ & ~0x00001000);
      nextWeekGameType_ = 0;
      onChanged();
      return this;
    }

    private int groupType_ ;
    /**
     * <code>optional int32 groupType = 15;</code>
     * @return Whether the groupType field is set.
     */
    @java.lang.Override
    public boolean hasGroupType() {
      return ((bitField0_ & 0x00002000) != 0);
    }
    /**
     * <code>optional int32 groupType = 15;</code>
     * @return The groupType.
     */
    @java.lang.Override
    public int getGroupType() {
      return groupType_;
    }
    /**
     * <code>optional int32 groupType = 15;</code>
     * @param value The groupType to set.
     * @return This builder for chaining.
     */
    public Builder setGroupType(int value) {

      groupType_ = value;
      bitField0_ |= 0x00002000;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 groupType = 15;</code>
     * @return This builder for chaining.
     */
    public Builder clearGroupType() {
      bitField0_ = (bitField0_ & ~0x00002000);
      groupType_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.UnionNameServerIdData> unionServerIdList_ =
      java.util.Collections.emptyList();
    private void ensureUnionServerIdListIsMutable() {
      if (!((bitField0_ & 0x00004000) != 0)) {
        unionServerIdList_ = new java.util.ArrayList<xddq.pb.UnionNameServerIdData>(unionServerIdList_);
        bitField0_ |= 0x00004000;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.UnionNameServerIdData, xddq.pb.UnionNameServerIdData.Builder, xddq.pb.UnionNameServerIdDataOrBuilder> unionServerIdListBuilder_;

    /**
     * <code>repeated .xddq.pb.UnionNameServerIdData unionServerIdList = 16;</code>
     */
    public java.util.List<xddq.pb.UnionNameServerIdData> getUnionServerIdListList() {
      if (unionServerIdListBuilder_ == null) {
        return java.util.Collections.unmodifiableList(unionServerIdList_);
      } else {
        return unionServerIdListBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.UnionNameServerIdData unionServerIdList = 16;</code>
     */
    public int getUnionServerIdListCount() {
      if (unionServerIdListBuilder_ == null) {
        return unionServerIdList_.size();
      } else {
        return unionServerIdListBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.UnionNameServerIdData unionServerIdList = 16;</code>
     */
    public xddq.pb.UnionNameServerIdData getUnionServerIdList(int index) {
      if (unionServerIdListBuilder_ == null) {
        return unionServerIdList_.get(index);
      } else {
        return unionServerIdListBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.UnionNameServerIdData unionServerIdList = 16;</code>
     */
    public Builder setUnionServerIdList(
        int index, xddq.pb.UnionNameServerIdData value) {
      if (unionServerIdListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureUnionServerIdListIsMutable();
        unionServerIdList_.set(index, value);
        onChanged();
      } else {
        unionServerIdListBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionNameServerIdData unionServerIdList = 16;</code>
     */
    public Builder setUnionServerIdList(
        int index, xddq.pb.UnionNameServerIdData.Builder builderForValue) {
      if (unionServerIdListBuilder_ == null) {
        ensureUnionServerIdListIsMutable();
        unionServerIdList_.set(index, builderForValue.build());
        onChanged();
      } else {
        unionServerIdListBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionNameServerIdData unionServerIdList = 16;</code>
     */
    public Builder addUnionServerIdList(xddq.pb.UnionNameServerIdData value) {
      if (unionServerIdListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureUnionServerIdListIsMutable();
        unionServerIdList_.add(value);
        onChanged();
      } else {
        unionServerIdListBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionNameServerIdData unionServerIdList = 16;</code>
     */
    public Builder addUnionServerIdList(
        int index, xddq.pb.UnionNameServerIdData value) {
      if (unionServerIdListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureUnionServerIdListIsMutable();
        unionServerIdList_.add(index, value);
        onChanged();
      } else {
        unionServerIdListBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionNameServerIdData unionServerIdList = 16;</code>
     */
    public Builder addUnionServerIdList(
        xddq.pb.UnionNameServerIdData.Builder builderForValue) {
      if (unionServerIdListBuilder_ == null) {
        ensureUnionServerIdListIsMutable();
        unionServerIdList_.add(builderForValue.build());
        onChanged();
      } else {
        unionServerIdListBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionNameServerIdData unionServerIdList = 16;</code>
     */
    public Builder addUnionServerIdList(
        int index, xddq.pb.UnionNameServerIdData.Builder builderForValue) {
      if (unionServerIdListBuilder_ == null) {
        ensureUnionServerIdListIsMutable();
        unionServerIdList_.add(index, builderForValue.build());
        onChanged();
      } else {
        unionServerIdListBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionNameServerIdData unionServerIdList = 16;</code>
     */
    public Builder addAllUnionServerIdList(
        java.lang.Iterable<? extends xddq.pb.UnionNameServerIdData> values) {
      if (unionServerIdListBuilder_ == null) {
        ensureUnionServerIdListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, unionServerIdList_);
        onChanged();
      } else {
        unionServerIdListBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionNameServerIdData unionServerIdList = 16;</code>
     */
    public Builder clearUnionServerIdList() {
      if (unionServerIdListBuilder_ == null) {
        unionServerIdList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00004000);
        onChanged();
      } else {
        unionServerIdListBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionNameServerIdData unionServerIdList = 16;</code>
     */
    public Builder removeUnionServerIdList(int index) {
      if (unionServerIdListBuilder_ == null) {
        ensureUnionServerIdListIsMutable();
        unionServerIdList_.remove(index);
        onChanged();
      } else {
        unionServerIdListBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionNameServerIdData unionServerIdList = 16;</code>
     */
    public xddq.pb.UnionNameServerIdData.Builder getUnionServerIdListBuilder(
        int index) {
      return internalGetUnionServerIdListFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.UnionNameServerIdData unionServerIdList = 16;</code>
     */
    public xddq.pb.UnionNameServerIdDataOrBuilder getUnionServerIdListOrBuilder(
        int index) {
      if (unionServerIdListBuilder_ == null) {
        return unionServerIdList_.get(index);  } else {
        return unionServerIdListBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.UnionNameServerIdData unionServerIdList = 16;</code>
     */
    public java.util.List<? extends xddq.pb.UnionNameServerIdDataOrBuilder> 
         getUnionServerIdListOrBuilderList() {
      if (unionServerIdListBuilder_ != null) {
        return unionServerIdListBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(unionServerIdList_);
      }
    }
    /**
     * <code>repeated .xddq.pb.UnionNameServerIdData unionServerIdList = 16;</code>
     */
    public xddq.pb.UnionNameServerIdData.Builder addUnionServerIdListBuilder() {
      return internalGetUnionServerIdListFieldBuilder().addBuilder(
          xddq.pb.UnionNameServerIdData.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.UnionNameServerIdData unionServerIdList = 16;</code>
     */
    public xddq.pb.UnionNameServerIdData.Builder addUnionServerIdListBuilder(
        int index) {
      return internalGetUnionServerIdListFieldBuilder().addBuilder(
          index, xddq.pb.UnionNameServerIdData.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.UnionNameServerIdData unionServerIdList = 16;</code>
     */
    public java.util.List<xddq.pb.UnionNameServerIdData.Builder> 
         getUnionServerIdListBuilderList() {
      return internalGetUnionServerIdListFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.UnionNameServerIdData, xddq.pb.UnionNameServerIdData.Builder, xddq.pb.UnionNameServerIdDataOrBuilder> 
        internalGetUnionServerIdListFieldBuilder() {
      if (unionServerIdListBuilder_ == null) {
        unionServerIdListBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.UnionNameServerIdData, xddq.pb.UnionNameServerIdData.Builder, xddq.pb.UnionNameServerIdDataOrBuilder>(
                unionServerIdList_,
                ((bitField0_ & 0x00004000) != 0),
                getParentForChildren(),
                isClean());
        unionServerIdList_ = null;
      }
      return unionServerIdListBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.HolyLandBaseInfoRespMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.HolyLandBaseInfoRespMsg)
  private static final xddq.pb.HolyLandBaseInfoRespMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.HolyLandBaseInfoRespMsg();
  }

  public static xddq.pb.HolyLandBaseInfoRespMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<HolyLandBaseInfoRespMsg>
      PARSER = new com.google.protobuf.AbstractParser<HolyLandBaseInfoRespMsg>() {
    @java.lang.Override
    public HolyLandBaseInfoRespMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<HolyLandBaseInfoRespMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<HolyLandBaseInfoRespMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.HolyLandBaseInfoRespMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

