// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

public interface UnionCeremonyCongratulationsGetReqOrBuilder extends
    // @@protoc_insertion_point(interface_extends:xddq.pb.UnionCeremonyCongratulationsGetReq)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>required int64 unionId = 1;</code>
   * @return Whether the unionId field is set.
   */
  boolean hasUnionId();
  /**
   * <code>required int64 unionId = 1;</code>
   * @return The unionId.
   */
  long getUnionId();

  /**
   * <code>optional int64 lastCongratulationsTime = 2;</code>
   * @return Whether the lastCongratulationsTime field is set.
   */
  boolean hasLastCongratulationsTime();
  /**
   * <code>optional int64 lastCongratulationsTime = 2;</code>
   * @return The lastCongratulationsTime.
   */
  long getLastCongratulationsTime();
}
