// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.GatherEnergyEnterNewReq}
 */
public final class GatherEnergyEnterNewReq extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.GatherEnergyEnterNewReq)
    GatherEnergyEnterNewReqOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      GatherEnergyEnterNewReq.class.getName());
  }
  // Use GatherEnergyEnterNewReq.newBuilder() to construct.
  private GatherEnergyEnterNewReq(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private GatherEnergyEnterNewReq() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_GatherEnergyEnterNewReq_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_GatherEnergyEnterNewReq_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.GatherEnergyEnterNewReq.class, xddq.pb.GatherEnergyEnterNewReq.Builder.class);
  }

  private int bitField0_;
  public static final int TOTALNUM_FIELD_NUMBER = 1;
  private int totalNum_ = 0;
  /**
   * <code>optional int32 totalNum = 1;</code>
   * @return Whether the totalNum field is set.
   */
  @java.lang.Override
  public boolean hasTotalNum() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int32 totalNum = 1;</code>
   * @return The totalNum.
   */
  @java.lang.Override
  public int getTotalNum() {
    return totalNum_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, totalNum_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, totalNum_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.GatherEnergyEnterNewReq)) {
      return super.equals(obj);
    }
    xddq.pb.GatherEnergyEnterNewReq other = (xddq.pb.GatherEnergyEnterNewReq) obj;

    if (hasTotalNum() != other.hasTotalNum()) return false;
    if (hasTotalNum()) {
      if (getTotalNum()
          != other.getTotalNum()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasTotalNum()) {
      hash = (37 * hash) + TOTALNUM_FIELD_NUMBER;
      hash = (53 * hash) + getTotalNum();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.GatherEnergyEnterNewReq parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GatherEnergyEnterNewReq parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GatherEnergyEnterNewReq parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GatherEnergyEnterNewReq parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GatherEnergyEnterNewReq parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GatherEnergyEnterNewReq parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GatherEnergyEnterNewReq parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.GatherEnergyEnterNewReq parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.GatherEnergyEnterNewReq parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.GatherEnergyEnterNewReq parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.GatherEnergyEnterNewReq parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.GatherEnergyEnterNewReq parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.GatherEnergyEnterNewReq prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.GatherEnergyEnterNewReq}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.GatherEnergyEnterNewReq)
      xddq.pb.GatherEnergyEnterNewReqOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GatherEnergyEnterNewReq_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GatherEnergyEnterNewReq_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.GatherEnergyEnterNewReq.class, xddq.pb.GatherEnergyEnterNewReq.Builder.class);
    }

    // Construct using xddq.pb.GatherEnergyEnterNewReq.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      totalNum_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GatherEnergyEnterNewReq_descriptor;
    }

    @java.lang.Override
    public xddq.pb.GatherEnergyEnterNewReq getDefaultInstanceForType() {
      return xddq.pb.GatherEnergyEnterNewReq.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.GatherEnergyEnterNewReq build() {
      xddq.pb.GatherEnergyEnterNewReq result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.GatherEnergyEnterNewReq buildPartial() {
      xddq.pb.GatherEnergyEnterNewReq result = new xddq.pb.GatherEnergyEnterNewReq(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.GatherEnergyEnterNewReq result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.totalNum_ = totalNum_;
        to_bitField0_ |= 0x00000001;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.GatherEnergyEnterNewReq) {
        return mergeFrom((xddq.pb.GatherEnergyEnterNewReq)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.GatherEnergyEnterNewReq other) {
      if (other == xddq.pb.GatherEnergyEnterNewReq.getDefaultInstance()) return this;
      if (other.hasTotalNum()) {
        setTotalNum(other.getTotalNum());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              totalNum_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int totalNum_ ;
    /**
     * <code>optional int32 totalNum = 1;</code>
     * @return Whether the totalNum field is set.
     */
    @java.lang.Override
    public boolean hasTotalNum() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 totalNum = 1;</code>
     * @return The totalNum.
     */
    @java.lang.Override
    public int getTotalNum() {
      return totalNum_;
    }
    /**
     * <code>optional int32 totalNum = 1;</code>
     * @param value The totalNum to set.
     * @return This builder for chaining.
     */
    public Builder setTotalNum(int value) {

      totalNum_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 totalNum = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearTotalNum() {
      bitField0_ = (bitField0_ & ~0x00000001);
      totalNum_ = 0;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.GatherEnergyEnterNewReq)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.GatherEnergyEnterNewReq)
  private static final xddq.pb.GatherEnergyEnterNewReq DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.GatherEnergyEnterNewReq();
  }

  public static xddq.pb.GatherEnergyEnterNewReq getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<GatherEnergyEnterNewReq>
      PARSER = new com.google.protobuf.AbstractParser<GatherEnergyEnterNewReq>() {
    @java.lang.Override
    public GatherEnergyEnterNewReq parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<GatherEnergyEnterNewReq> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<GatherEnergyEnterNewReq> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.GatherEnergyEnterNewReq getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

