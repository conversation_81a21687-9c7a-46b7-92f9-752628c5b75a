// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

public interface HolyLandChampionMemberInfoReqOrBuilder extends
    // @@protoc_insertion_point(interface_extends:xddq.pb.HolyLandChampionMemberInfoReq)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>required int32 index = 1;</code>
   * @return Whether the index field is set.
   */
  boolean hasIndex();
  /**
   * <code>required int32 index = 1;</code>
   * @return The index.
   */
  int getIndex();

  /**
   * <code>required int64 unionId = 2;</code>
   * @return Whether the unionId field is set.
   */
  boolean hasUnionId();
  /**
   * <code>required int64 unionId = 2;</code>
   * @return The unionId.
   */
  long getUnionId();

  /**
   * <code>required int64 wonTime = 3;</code>
   * @return Whether the wonTime field is set.
   */
  boolean hasWonTime();
  /**
   * <code>required int64 wonTime = 3;</code>
   * @return The wonTime.
   */
  long getWonTime();
}
