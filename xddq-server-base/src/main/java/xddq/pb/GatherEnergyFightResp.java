// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.GatherEnergyFightResp}
 */
public final class GatherEnergyFightResp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.GatherEnergyFightResp)
    GatherEnergyFightRespOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      GatherEnergyFightResp.class.getName());
  }
  // Use GatherEnergyFightResp.newBuilder() to construct.
  private GatherEnergyFightResp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private GatherEnergyFightResp() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_GatherEnergyFightResp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_GatherEnergyFightResp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.GatherEnergyFightResp.class, xddq.pb.GatherEnergyFightResp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int STATE_FIELD_NUMBER = 2;
  private int state_ = 0;
  /**
   * <code>optional int32 state = 2;</code>
   * @return Whether the state field is set.
   */
  @java.lang.Override
  public boolean hasState() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int32 state = 2;</code>
   * @return The state.
   */
  @java.lang.Override
  public int getState() {
    return state_;
  }

  public static final int OPPOPLAYERINFO_FIELD_NUMBER = 3;
  private xddq.pb.PlayerCharacterImageMsg oppoPlayerInfo_;
  /**
   * <code>optional .xddq.pb.PlayerCharacterImageMsg oppoPlayerInfo = 3;</code>
   * @return Whether the oppoPlayerInfo field is set.
   */
  @java.lang.Override
  public boolean hasOppoPlayerInfo() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional .xddq.pb.PlayerCharacterImageMsg oppoPlayerInfo = 3;</code>
   * @return The oppoPlayerInfo.
   */
  @java.lang.Override
  public xddq.pb.PlayerCharacterImageMsg getOppoPlayerInfo() {
    return oppoPlayerInfo_ == null ? xddq.pb.PlayerCharacterImageMsg.getDefaultInstance() : oppoPlayerInfo_;
  }
  /**
   * <code>optional .xddq.pb.PlayerCharacterImageMsg oppoPlayerInfo = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.PlayerCharacterImageMsgOrBuilder getOppoPlayerInfoOrBuilder() {
    return oppoPlayerInfo_ == null ? xddq.pb.PlayerCharacterImageMsg.getDefaultInstance() : oppoPlayerInfo_;
  }

  public static final int MYPLAYERINFO_FIELD_NUMBER = 4;
  private xddq.pb.PlayerCharacterImageMsg myPlayerInfo_;
  /**
   * <code>optional .xddq.pb.PlayerCharacterImageMsg myPlayerInfo = 4;</code>
   * @return Whether the myPlayerInfo field is set.
   */
  @java.lang.Override
  public boolean hasMyPlayerInfo() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional .xddq.pb.PlayerCharacterImageMsg myPlayerInfo = 4;</code>
   * @return The myPlayerInfo.
   */
  @java.lang.Override
  public xddq.pb.PlayerCharacterImageMsg getMyPlayerInfo() {
    return myPlayerInfo_ == null ? xddq.pb.PlayerCharacterImageMsg.getDefaultInstance() : myPlayerInfo_;
  }
  /**
   * <code>optional .xddq.pb.PlayerCharacterImageMsg myPlayerInfo = 4;</code>
   */
  @java.lang.Override
  public xddq.pb.PlayerCharacterImageMsgOrBuilder getMyPlayerInfoOrBuilder() {
    return myPlayerInfo_ == null ? xddq.pb.PlayerCharacterImageMsg.getDefaultInstance() : myPlayerInfo_;
  }

  public static final int ALLBATTLERECORD_FIELD_NUMBER = 5;
  private xddq.pb.BattleRecordMsg allBattleRecord_;
  /**
   * <code>optional .xddq.pb.BattleRecordMsg allBattleRecord = 5;</code>
   * @return Whether the allBattleRecord field is set.
   */
  @java.lang.Override
  public boolean hasAllBattleRecord() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>optional .xddq.pb.BattleRecordMsg allBattleRecord = 5;</code>
   * @return The allBattleRecord.
   */
  @java.lang.Override
  public xddq.pb.BattleRecordMsg getAllBattleRecord() {
    return allBattleRecord_ == null ? xddq.pb.BattleRecordMsg.getDefaultInstance() : allBattleRecord_;
  }
  /**
   * <code>optional .xddq.pb.BattleRecordMsg allBattleRecord = 5;</code>
   */
  @java.lang.Override
  public xddq.pb.BattleRecordMsgOrBuilder getAllBattleRecordOrBuilder() {
    return allBattleRecord_ == null ? xddq.pb.BattleRecordMsg.getDefaultInstance() : allBattleRecord_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (hasAllBattleRecord()) {
      if (!getAllBattleRecord().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(2, state_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeMessage(3, getOppoPlayerInfo());
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeMessage(4, getMyPlayerInfo());
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      output.writeMessage(5, getAllBattleRecord());
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, state_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, getOppoPlayerInfo());
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(4, getMyPlayerInfo());
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(5, getAllBattleRecord());
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.GatherEnergyFightResp)) {
      return super.equals(obj);
    }
    xddq.pb.GatherEnergyFightResp other = (xddq.pb.GatherEnergyFightResp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (hasState() != other.hasState()) return false;
    if (hasState()) {
      if (getState()
          != other.getState()) return false;
    }
    if (hasOppoPlayerInfo() != other.hasOppoPlayerInfo()) return false;
    if (hasOppoPlayerInfo()) {
      if (!getOppoPlayerInfo()
          .equals(other.getOppoPlayerInfo())) return false;
    }
    if (hasMyPlayerInfo() != other.hasMyPlayerInfo()) return false;
    if (hasMyPlayerInfo()) {
      if (!getMyPlayerInfo()
          .equals(other.getMyPlayerInfo())) return false;
    }
    if (hasAllBattleRecord() != other.hasAllBattleRecord()) return false;
    if (hasAllBattleRecord()) {
      if (!getAllBattleRecord()
          .equals(other.getAllBattleRecord())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (hasState()) {
      hash = (37 * hash) + STATE_FIELD_NUMBER;
      hash = (53 * hash) + getState();
    }
    if (hasOppoPlayerInfo()) {
      hash = (37 * hash) + OPPOPLAYERINFO_FIELD_NUMBER;
      hash = (53 * hash) + getOppoPlayerInfo().hashCode();
    }
    if (hasMyPlayerInfo()) {
      hash = (37 * hash) + MYPLAYERINFO_FIELD_NUMBER;
      hash = (53 * hash) + getMyPlayerInfo().hashCode();
    }
    if (hasAllBattleRecord()) {
      hash = (37 * hash) + ALLBATTLERECORD_FIELD_NUMBER;
      hash = (53 * hash) + getAllBattleRecord().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.GatherEnergyFightResp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GatherEnergyFightResp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GatherEnergyFightResp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GatherEnergyFightResp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GatherEnergyFightResp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GatherEnergyFightResp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GatherEnergyFightResp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.GatherEnergyFightResp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.GatherEnergyFightResp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.GatherEnergyFightResp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.GatherEnergyFightResp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.GatherEnergyFightResp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.GatherEnergyFightResp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.GatherEnergyFightResp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.GatherEnergyFightResp)
      xddq.pb.GatherEnergyFightRespOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GatherEnergyFightResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GatherEnergyFightResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.GatherEnergyFightResp.class, xddq.pb.GatherEnergyFightResp.Builder.class);
    }

    // Construct using xddq.pb.GatherEnergyFightResp.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetOppoPlayerInfoFieldBuilder();
        internalGetMyPlayerInfoFieldBuilder();
        internalGetAllBattleRecordFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      state_ = 0;
      oppoPlayerInfo_ = null;
      if (oppoPlayerInfoBuilder_ != null) {
        oppoPlayerInfoBuilder_.dispose();
        oppoPlayerInfoBuilder_ = null;
      }
      myPlayerInfo_ = null;
      if (myPlayerInfoBuilder_ != null) {
        myPlayerInfoBuilder_.dispose();
        myPlayerInfoBuilder_ = null;
      }
      allBattleRecord_ = null;
      if (allBattleRecordBuilder_ != null) {
        allBattleRecordBuilder_.dispose();
        allBattleRecordBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GatherEnergyFightResp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.GatherEnergyFightResp getDefaultInstanceForType() {
      return xddq.pb.GatherEnergyFightResp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.GatherEnergyFightResp build() {
      xddq.pb.GatherEnergyFightResp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.GatherEnergyFightResp buildPartial() {
      xddq.pb.GatherEnergyFightResp result = new xddq.pb.GatherEnergyFightResp(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.GatherEnergyFightResp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.state_ = state_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.oppoPlayerInfo_ = oppoPlayerInfoBuilder_ == null
            ? oppoPlayerInfo_
            : oppoPlayerInfoBuilder_.build();
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.myPlayerInfo_ = myPlayerInfoBuilder_ == null
            ? myPlayerInfo_
            : myPlayerInfoBuilder_.build();
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.allBattleRecord_ = allBattleRecordBuilder_ == null
            ? allBattleRecord_
            : allBattleRecordBuilder_.build();
        to_bitField0_ |= 0x00000010;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.GatherEnergyFightResp) {
        return mergeFrom((xddq.pb.GatherEnergyFightResp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.GatherEnergyFightResp other) {
      if (other == xddq.pb.GatherEnergyFightResp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (other.hasState()) {
        setState(other.getState());
      }
      if (other.hasOppoPlayerInfo()) {
        mergeOppoPlayerInfo(other.getOppoPlayerInfo());
      }
      if (other.hasMyPlayerInfo()) {
        mergeMyPlayerInfo(other.getMyPlayerInfo());
      }
      if (other.hasAllBattleRecord()) {
        mergeAllBattleRecord(other.getAllBattleRecord());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      if (hasAllBattleRecord()) {
        if (!getAllBattleRecord().isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              state_ = input.readInt32();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 26: {
              input.readMessage(
                  internalGetOppoPlayerInfoFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 34: {
              input.readMessage(
                  internalGetMyPlayerInfoFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000008;
              break;
            } // case 34
            case 42: {
              input.readMessage(
                  internalGetAllBattleRecordFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000010;
              break;
            } // case 42
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private int state_ ;
    /**
     * <code>optional int32 state = 2;</code>
     * @return Whether the state field is set.
     */
    @java.lang.Override
    public boolean hasState() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 state = 2;</code>
     * @return The state.
     */
    @java.lang.Override
    public int getState() {
      return state_;
    }
    /**
     * <code>optional int32 state = 2;</code>
     * @param value The state to set.
     * @return This builder for chaining.
     */
    public Builder setState(int value) {

      state_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 state = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearState() {
      bitField0_ = (bitField0_ & ~0x00000002);
      state_ = 0;
      onChanged();
      return this;
    }

    private xddq.pb.PlayerCharacterImageMsg oppoPlayerInfo_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PlayerCharacterImageMsg, xddq.pb.PlayerCharacterImageMsg.Builder, xddq.pb.PlayerCharacterImageMsgOrBuilder> oppoPlayerInfoBuilder_;
    /**
     * <code>optional .xddq.pb.PlayerCharacterImageMsg oppoPlayerInfo = 3;</code>
     * @return Whether the oppoPlayerInfo field is set.
     */
    public boolean hasOppoPlayerInfo() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional .xddq.pb.PlayerCharacterImageMsg oppoPlayerInfo = 3;</code>
     * @return The oppoPlayerInfo.
     */
    public xddq.pb.PlayerCharacterImageMsg getOppoPlayerInfo() {
      if (oppoPlayerInfoBuilder_ == null) {
        return oppoPlayerInfo_ == null ? xddq.pb.PlayerCharacterImageMsg.getDefaultInstance() : oppoPlayerInfo_;
      } else {
        return oppoPlayerInfoBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.PlayerCharacterImageMsg oppoPlayerInfo = 3;</code>
     */
    public Builder setOppoPlayerInfo(xddq.pb.PlayerCharacterImageMsg value) {
      if (oppoPlayerInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        oppoPlayerInfo_ = value;
      } else {
        oppoPlayerInfoBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerCharacterImageMsg oppoPlayerInfo = 3;</code>
     */
    public Builder setOppoPlayerInfo(
        xddq.pb.PlayerCharacterImageMsg.Builder builderForValue) {
      if (oppoPlayerInfoBuilder_ == null) {
        oppoPlayerInfo_ = builderForValue.build();
      } else {
        oppoPlayerInfoBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerCharacterImageMsg oppoPlayerInfo = 3;</code>
     */
    public Builder mergeOppoPlayerInfo(xddq.pb.PlayerCharacterImageMsg value) {
      if (oppoPlayerInfoBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0) &&
          oppoPlayerInfo_ != null &&
          oppoPlayerInfo_ != xddq.pb.PlayerCharacterImageMsg.getDefaultInstance()) {
          getOppoPlayerInfoBuilder().mergeFrom(value);
        } else {
          oppoPlayerInfo_ = value;
        }
      } else {
        oppoPlayerInfoBuilder_.mergeFrom(value);
      }
      if (oppoPlayerInfo_ != null) {
        bitField0_ |= 0x00000004;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerCharacterImageMsg oppoPlayerInfo = 3;</code>
     */
    public Builder clearOppoPlayerInfo() {
      bitField0_ = (bitField0_ & ~0x00000004);
      oppoPlayerInfo_ = null;
      if (oppoPlayerInfoBuilder_ != null) {
        oppoPlayerInfoBuilder_.dispose();
        oppoPlayerInfoBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerCharacterImageMsg oppoPlayerInfo = 3;</code>
     */
    public xddq.pb.PlayerCharacterImageMsg.Builder getOppoPlayerInfoBuilder() {
      bitField0_ |= 0x00000004;
      onChanged();
      return internalGetOppoPlayerInfoFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.PlayerCharacterImageMsg oppoPlayerInfo = 3;</code>
     */
    public xddq.pb.PlayerCharacterImageMsgOrBuilder getOppoPlayerInfoOrBuilder() {
      if (oppoPlayerInfoBuilder_ != null) {
        return oppoPlayerInfoBuilder_.getMessageOrBuilder();
      } else {
        return oppoPlayerInfo_ == null ?
            xddq.pb.PlayerCharacterImageMsg.getDefaultInstance() : oppoPlayerInfo_;
      }
    }
    /**
     * <code>optional .xddq.pb.PlayerCharacterImageMsg oppoPlayerInfo = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PlayerCharacterImageMsg, xddq.pb.PlayerCharacterImageMsg.Builder, xddq.pb.PlayerCharacterImageMsgOrBuilder> 
        internalGetOppoPlayerInfoFieldBuilder() {
      if (oppoPlayerInfoBuilder_ == null) {
        oppoPlayerInfoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.PlayerCharacterImageMsg, xddq.pb.PlayerCharacterImageMsg.Builder, xddq.pb.PlayerCharacterImageMsgOrBuilder>(
                getOppoPlayerInfo(),
                getParentForChildren(),
                isClean());
        oppoPlayerInfo_ = null;
      }
      return oppoPlayerInfoBuilder_;
    }

    private xddq.pb.PlayerCharacterImageMsg myPlayerInfo_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PlayerCharacterImageMsg, xddq.pb.PlayerCharacterImageMsg.Builder, xddq.pb.PlayerCharacterImageMsgOrBuilder> myPlayerInfoBuilder_;
    /**
     * <code>optional .xddq.pb.PlayerCharacterImageMsg myPlayerInfo = 4;</code>
     * @return Whether the myPlayerInfo field is set.
     */
    public boolean hasMyPlayerInfo() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional .xddq.pb.PlayerCharacterImageMsg myPlayerInfo = 4;</code>
     * @return The myPlayerInfo.
     */
    public xddq.pb.PlayerCharacterImageMsg getMyPlayerInfo() {
      if (myPlayerInfoBuilder_ == null) {
        return myPlayerInfo_ == null ? xddq.pb.PlayerCharacterImageMsg.getDefaultInstance() : myPlayerInfo_;
      } else {
        return myPlayerInfoBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.PlayerCharacterImageMsg myPlayerInfo = 4;</code>
     */
    public Builder setMyPlayerInfo(xddq.pb.PlayerCharacterImageMsg value) {
      if (myPlayerInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        myPlayerInfo_ = value;
      } else {
        myPlayerInfoBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerCharacterImageMsg myPlayerInfo = 4;</code>
     */
    public Builder setMyPlayerInfo(
        xddq.pb.PlayerCharacterImageMsg.Builder builderForValue) {
      if (myPlayerInfoBuilder_ == null) {
        myPlayerInfo_ = builderForValue.build();
      } else {
        myPlayerInfoBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerCharacterImageMsg myPlayerInfo = 4;</code>
     */
    public Builder mergeMyPlayerInfo(xddq.pb.PlayerCharacterImageMsg value) {
      if (myPlayerInfoBuilder_ == null) {
        if (((bitField0_ & 0x00000008) != 0) &&
          myPlayerInfo_ != null &&
          myPlayerInfo_ != xddq.pb.PlayerCharacterImageMsg.getDefaultInstance()) {
          getMyPlayerInfoBuilder().mergeFrom(value);
        } else {
          myPlayerInfo_ = value;
        }
      } else {
        myPlayerInfoBuilder_.mergeFrom(value);
      }
      if (myPlayerInfo_ != null) {
        bitField0_ |= 0x00000008;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerCharacterImageMsg myPlayerInfo = 4;</code>
     */
    public Builder clearMyPlayerInfo() {
      bitField0_ = (bitField0_ & ~0x00000008);
      myPlayerInfo_ = null;
      if (myPlayerInfoBuilder_ != null) {
        myPlayerInfoBuilder_.dispose();
        myPlayerInfoBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerCharacterImageMsg myPlayerInfo = 4;</code>
     */
    public xddq.pb.PlayerCharacterImageMsg.Builder getMyPlayerInfoBuilder() {
      bitField0_ |= 0x00000008;
      onChanged();
      return internalGetMyPlayerInfoFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.PlayerCharacterImageMsg myPlayerInfo = 4;</code>
     */
    public xddq.pb.PlayerCharacterImageMsgOrBuilder getMyPlayerInfoOrBuilder() {
      if (myPlayerInfoBuilder_ != null) {
        return myPlayerInfoBuilder_.getMessageOrBuilder();
      } else {
        return myPlayerInfo_ == null ?
            xddq.pb.PlayerCharacterImageMsg.getDefaultInstance() : myPlayerInfo_;
      }
    }
    /**
     * <code>optional .xddq.pb.PlayerCharacterImageMsg myPlayerInfo = 4;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PlayerCharacterImageMsg, xddq.pb.PlayerCharacterImageMsg.Builder, xddq.pb.PlayerCharacterImageMsgOrBuilder> 
        internalGetMyPlayerInfoFieldBuilder() {
      if (myPlayerInfoBuilder_ == null) {
        myPlayerInfoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.PlayerCharacterImageMsg, xddq.pb.PlayerCharacterImageMsg.Builder, xddq.pb.PlayerCharacterImageMsgOrBuilder>(
                getMyPlayerInfo(),
                getParentForChildren(),
                isClean());
        myPlayerInfo_ = null;
      }
      return myPlayerInfoBuilder_;
    }

    private xddq.pb.BattleRecordMsg allBattleRecord_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.BattleRecordMsg, xddq.pb.BattleRecordMsg.Builder, xddq.pb.BattleRecordMsgOrBuilder> allBattleRecordBuilder_;
    /**
     * <code>optional .xddq.pb.BattleRecordMsg allBattleRecord = 5;</code>
     * @return Whether the allBattleRecord field is set.
     */
    public boolean hasAllBattleRecord() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional .xddq.pb.BattleRecordMsg allBattleRecord = 5;</code>
     * @return The allBattleRecord.
     */
    public xddq.pb.BattleRecordMsg getAllBattleRecord() {
      if (allBattleRecordBuilder_ == null) {
        return allBattleRecord_ == null ? xddq.pb.BattleRecordMsg.getDefaultInstance() : allBattleRecord_;
      } else {
        return allBattleRecordBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.BattleRecordMsg allBattleRecord = 5;</code>
     */
    public Builder setAllBattleRecord(xddq.pb.BattleRecordMsg value) {
      if (allBattleRecordBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        allBattleRecord_ = value;
      } else {
        allBattleRecordBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.BattleRecordMsg allBattleRecord = 5;</code>
     */
    public Builder setAllBattleRecord(
        xddq.pb.BattleRecordMsg.Builder builderForValue) {
      if (allBattleRecordBuilder_ == null) {
        allBattleRecord_ = builderForValue.build();
      } else {
        allBattleRecordBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.BattleRecordMsg allBattleRecord = 5;</code>
     */
    public Builder mergeAllBattleRecord(xddq.pb.BattleRecordMsg value) {
      if (allBattleRecordBuilder_ == null) {
        if (((bitField0_ & 0x00000010) != 0) &&
          allBattleRecord_ != null &&
          allBattleRecord_ != xddq.pb.BattleRecordMsg.getDefaultInstance()) {
          getAllBattleRecordBuilder().mergeFrom(value);
        } else {
          allBattleRecord_ = value;
        }
      } else {
        allBattleRecordBuilder_.mergeFrom(value);
      }
      if (allBattleRecord_ != null) {
        bitField0_ |= 0x00000010;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.BattleRecordMsg allBattleRecord = 5;</code>
     */
    public Builder clearAllBattleRecord() {
      bitField0_ = (bitField0_ & ~0x00000010);
      allBattleRecord_ = null;
      if (allBattleRecordBuilder_ != null) {
        allBattleRecordBuilder_.dispose();
        allBattleRecordBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.BattleRecordMsg allBattleRecord = 5;</code>
     */
    public xddq.pb.BattleRecordMsg.Builder getAllBattleRecordBuilder() {
      bitField0_ |= 0x00000010;
      onChanged();
      return internalGetAllBattleRecordFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.BattleRecordMsg allBattleRecord = 5;</code>
     */
    public xddq.pb.BattleRecordMsgOrBuilder getAllBattleRecordOrBuilder() {
      if (allBattleRecordBuilder_ != null) {
        return allBattleRecordBuilder_.getMessageOrBuilder();
      } else {
        return allBattleRecord_ == null ?
            xddq.pb.BattleRecordMsg.getDefaultInstance() : allBattleRecord_;
      }
    }
    /**
     * <code>optional .xddq.pb.BattleRecordMsg allBattleRecord = 5;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.BattleRecordMsg, xddq.pb.BattleRecordMsg.Builder, xddq.pb.BattleRecordMsgOrBuilder> 
        internalGetAllBattleRecordFieldBuilder() {
      if (allBattleRecordBuilder_ == null) {
        allBattleRecordBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.BattleRecordMsg, xddq.pb.BattleRecordMsg.Builder, xddq.pb.BattleRecordMsgOrBuilder>(
                getAllBattleRecord(),
                getParentForChildren(),
                isClean());
        allBattleRecord_ = null;
      }
      return allBattleRecordBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.GatherEnergyFightResp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.GatherEnergyFightResp)
  private static final xddq.pb.GatherEnergyFightResp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.GatherEnergyFightResp();
  }

  public static xddq.pb.GatherEnergyFightResp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<GatherEnergyFightResp>
      PARSER = new com.google.protobuf.AbstractParser<GatherEnergyFightResp>() {
    @java.lang.Override
    public GatherEnergyFightResp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<GatherEnergyFightResp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<GatherEnergyFightResp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.GatherEnergyFightResp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

