// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.MonopolyBlessingListResp}
 */
public final class MonopolyBlessingListResp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.MonopolyBlessingListResp)
    MonopolyBlessingListRespOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      MonopolyBlessingListResp.class.getName());
  }
  // Use MonopolyBlessingListResp.newBuilder() to construct.
  private MonopolyBlessingListResp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private MonopolyBlessingListResp() {
    blessingList_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_MonopolyBlessingListResp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_MonopolyBlessingListResp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.MonopolyBlessingListResp.class, xddq.pb.MonopolyBlessingListResp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int BLESSINGLIST_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.MonopolyBlessedNoticeInfo> blessingList_;
  /**
   * <code>repeated .xddq.pb.MonopolyBlessedNoticeInfo blessingList = 2;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.MonopolyBlessedNoticeInfo> getBlessingListList() {
    return blessingList_;
  }
  /**
   * <code>repeated .xddq.pb.MonopolyBlessedNoticeInfo blessingList = 2;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.MonopolyBlessedNoticeInfoOrBuilder> 
      getBlessingListOrBuilderList() {
    return blessingList_;
  }
  /**
   * <code>repeated .xddq.pb.MonopolyBlessedNoticeInfo blessingList = 2;</code>
   */
  @java.lang.Override
  public int getBlessingListCount() {
    return blessingList_.size();
  }
  /**
   * <code>repeated .xddq.pb.MonopolyBlessedNoticeInfo blessingList = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.MonopolyBlessedNoticeInfo getBlessingList(int index) {
    return blessingList_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.MonopolyBlessedNoticeInfo blessingList = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.MonopolyBlessedNoticeInfoOrBuilder getBlessingListOrBuilder(
      int index) {
    return blessingList_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    for (int i = 0; i < getBlessingListCount(); i++) {
      if (!getBlessingList(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    for (int i = 0; i < blessingList_.size(); i++) {
      output.writeMessage(2, blessingList_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    for (int i = 0; i < blessingList_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, blessingList_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.MonopolyBlessingListResp)) {
      return super.equals(obj);
    }
    xddq.pb.MonopolyBlessingListResp other = (xddq.pb.MonopolyBlessingListResp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (!getBlessingListList()
        .equals(other.getBlessingListList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (getBlessingListCount() > 0) {
      hash = (37 * hash) + BLESSINGLIST_FIELD_NUMBER;
      hash = (53 * hash) + getBlessingListList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.MonopolyBlessingListResp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.MonopolyBlessingListResp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.MonopolyBlessingListResp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.MonopolyBlessingListResp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.MonopolyBlessingListResp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.MonopolyBlessingListResp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.MonopolyBlessingListResp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.MonopolyBlessingListResp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.MonopolyBlessingListResp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.MonopolyBlessingListResp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.MonopolyBlessingListResp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.MonopolyBlessingListResp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.MonopolyBlessingListResp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.MonopolyBlessingListResp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.MonopolyBlessingListResp)
      xddq.pb.MonopolyBlessingListRespOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_MonopolyBlessingListResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_MonopolyBlessingListResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.MonopolyBlessingListResp.class, xddq.pb.MonopolyBlessingListResp.Builder.class);
    }

    // Construct using xddq.pb.MonopolyBlessingListResp.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      if (blessingListBuilder_ == null) {
        blessingList_ = java.util.Collections.emptyList();
      } else {
        blessingList_ = null;
        blessingListBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000002);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_MonopolyBlessingListResp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.MonopolyBlessingListResp getDefaultInstanceForType() {
      return xddq.pb.MonopolyBlessingListResp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.MonopolyBlessingListResp build() {
      xddq.pb.MonopolyBlessingListResp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.MonopolyBlessingListResp buildPartial() {
      xddq.pb.MonopolyBlessingListResp result = new xddq.pb.MonopolyBlessingListResp(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.MonopolyBlessingListResp result) {
      if (blessingListBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          blessingList_ = java.util.Collections.unmodifiableList(blessingList_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.blessingList_ = blessingList_;
      } else {
        result.blessingList_ = blessingListBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.MonopolyBlessingListResp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.MonopolyBlessingListResp) {
        return mergeFrom((xddq.pb.MonopolyBlessingListResp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.MonopolyBlessingListResp other) {
      if (other == xddq.pb.MonopolyBlessingListResp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (blessingListBuilder_ == null) {
        if (!other.blessingList_.isEmpty()) {
          if (blessingList_.isEmpty()) {
            blessingList_ = other.blessingList_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureBlessingListIsMutable();
            blessingList_.addAll(other.blessingList_);
          }
          onChanged();
        }
      } else {
        if (!other.blessingList_.isEmpty()) {
          if (blessingListBuilder_.isEmpty()) {
            blessingListBuilder_.dispose();
            blessingListBuilder_ = null;
            blessingList_ = other.blessingList_;
            bitField0_ = (bitField0_ & ~0x00000002);
            blessingListBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetBlessingListFieldBuilder() : null;
          } else {
            blessingListBuilder_.addAllMessages(other.blessingList_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      for (int i = 0; i < getBlessingListCount(); i++) {
        if (!getBlessingList(i).isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              xddq.pb.MonopolyBlessedNoticeInfo m =
                  input.readMessage(
                      xddq.pb.MonopolyBlessedNoticeInfo.parser(),
                      extensionRegistry);
              if (blessingListBuilder_ == null) {
                ensureBlessingListIsMutable();
                blessingList_.add(m);
              } else {
                blessingListBuilder_.addMessage(m);
              }
              break;
            } // case 18
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.MonopolyBlessedNoticeInfo> blessingList_ =
      java.util.Collections.emptyList();
    private void ensureBlessingListIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        blessingList_ = new java.util.ArrayList<xddq.pb.MonopolyBlessedNoticeInfo>(blessingList_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.MonopolyBlessedNoticeInfo, xddq.pb.MonopolyBlessedNoticeInfo.Builder, xddq.pb.MonopolyBlessedNoticeInfoOrBuilder> blessingListBuilder_;

    /**
     * <code>repeated .xddq.pb.MonopolyBlessedNoticeInfo blessingList = 2;</code>
     */
    public java.util.List<xddq.pb.MonopolyBlessedNoticeInfo> getBlessingListList() {
      if (blessingListBuilder_ == null) {
        return java.util.Collections.unmodifiableList(blessingList_);
      } else {
        return blessingListBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.MonopolyBlessedNoticeInfo blessingList = 2;</code>
     */
    public int getBlessingListCount() {
      if (blessingListBuilder_ == null) {
        return blessingList_.size();
      } else {
        return blessingListBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.MonopolyBlessedNoticeInfo blessingList = 2;</code>
     */
    public xddq.pb.MonopolyBlessedNoticeInfo getBlessingList(int index) {
      if (blessingListBuilder_ == null) {
        return blessingList_.get(index);
      } else {
        return blessingListBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.MonopolyBlessedNoticeInfo blessingList = 2;</code>
     */
    public Builder setBlessingList(
        int index, xddq.pb.MonopolyBlessedNoticeInfo value) {
      if (blessingListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureBlessingListIsMutable();
        blessingList_.set(index, value);
        onChanged();
      } else {
        blessingListBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MonopolyBlessedNoticeInfo blessingList = 2;</code>
     */
    public Builder setBlessingList(
        int index, xddq.pb.MonopolyBlessedNoticeInfo.Builder builderForValue) {
      if (blessingListBuilder_ == null) {
        ensureBlessingListIsMutable();
        blessingList_.set(index, builderForValue.build());
        onChanged();
      } else {
        blessingListBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MonopolyBlessedNoticeInfo blessingList = 2;</code>
     */
    public Builder addBlessingList(xddq.pb.MonopolyBlessedNoticeInfo value) {
      if (blessingListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureBlessingListIsMutable();
        blessingList_.add(value);
        onChanged();
      } else {
        blessingListBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MonopolyBlessedNoticeInfo blessingList = 2;</code>
     */
    public Builder addBlessingList(
        int index, xddq.pb.MonopolyBlessedNoticeInfo value) {
      if (blessingListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureBlessingListIsMutable();
        blessingList_.add(index, value);
        onChanged();
      } else {
        blessingListBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MonopolyBlessedNoticeInfo blessingList = 2;</code>
     */
    public Builder addBlessingList(
        xddq.pb.MonopolyBlessedNoticeInfo.Builder builderForValue) {
      if (blessingListBuilder_ == null) {
        ensureBlessingListIsMutable();
        blessingList_.add(builderForValue.build());
        onChanged();
      } else {
        blessingListBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MonopolyBlessedNoticeInfo blessingList = 2;</code>
     */
    public Builder addBlessingList(
        int index, xddq.pb.MonopolyBlessedNoticeInfo.Builder builderForValue) {
      if (blessingListBuilder_ == null) {
        ensureBlessingListIsMutable();
        blessingList_.add(index, builderForValue.build());
        onChanged();
      } else {
        blessingListBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MonopolyBlessedNoticeInfo blessingList = 2;</code>
     */
    public Builder addAllBlessingList(
        java.lang.Iterable<? extends xddq.pb.MonopolyBlessedNoticeInfo> values) {
      if (blessingListBuilder_ == null) {
        ensureBlessingListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, blessingList_);
        onChanged();
      } else {
        blessingListBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MonopolyBlessedNoticeInfo blessingList = 2;</code>
     */
    public Builder clearBlessingList() {
      if (blessingListBuilder_ == null) {
        blessingList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        blessingListBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MonopolyBlessedNoticeInfo blessingList = 2;</code>
     */
    public Builder removeBlessingList(int index) {
      if (blessingListBuilder_ == null) {
        ensureBlessingListIsMutable();
        blessingList_.remove(index);
        onChanged();
      } else {
        blessingListBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MonopolyBlessedNoticeInfo blessingList = 2;</code>
     */
    public xddq.pb.MonopolyBlessedNoticeInfo.Builder getBlessingListBuilder(
        int index) {
      return internalGetBlessingListFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.MonopolyBlessedNoticeInfo blessingList = 2;</code>
     */
    public xddq.pb.MonopolyBlessedNoticeInfoOrBuilder getBlessingListOrBuilder(
        int index) {
      if (blessingListBuilder_ == null) {
        return blessingList_.get(index);  } else {
        return blessingListBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.MonopolyBlessedNoticeInfo blessingList = 2;</code>
     */
    public java.util.List<? extends xddq.pb.MonopolyBlessedNoticeInfoOrBuilder> 
         getBlessingListOrBuilderList() {
      if (blessingListBuilder_ != null) {
        return blessingListBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(blessingList_);
      }
    }
    /**
     * <code>repeated .xddq.pb.MonopolyBlessedNoticeInfo blessingList = 2;</code>
     */
    public xddq.pb.MonopolyBlessedNoticeInfo.Builder addBlessingListBuilder() {
      return internalGetBlessingListFieldBuilder().addBuilder(
          xddq.pb.MonopolyBlessedNoticeInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.MonopolyBlessedNoticeInfo blessingList = 2;</code>
     */
    public xddq.pb.MonopolyBlessedNoticeInfo.Builder addBlessingListBuilder(
        int index) {
      return internalGetBlessingListFieldBuilder().addBuilder(
          index, xddq.pb.MonopolyBlessedNoticeInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.MonopolyBlessedNoticeInfo blessingList = 2;</code>
     */
    public java.util.List<xddq.pb.MonopolyBlessedNoticeInfo.Builder> 
         getBlessingListBuilderList() {
      return internalGetBlessingListFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.MonopolyBlessedNoticeInfo, xddq.pb.MonopolyBlessedNoticeInfo.Builder, xddq.pb.MonopolyBlessedNoticeInfoOrBuilder> 
        internalGetBlessingListFieldBuilder() {
      if (blessingListBuilder_ == null) {
        blessingListBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.MonopolyBlessedNoticeInfo, xddq.pb.MonopolyBlessedNoticeInfo.Builder, xddq.pb.MonopolyBlessedNoticeInfoOrBuilder>(
                blessingList_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        blessingList_ = null;
      }
      return blessingListBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.MonopolyBlessingListResp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.MonopolyBlessingListResp)
  private static final xddq.pb.MonopolyBlessingListResp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.MonopolyBlessingListResp();
  }

  public static xddq.pb.MonopolyBlessingListResp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<MonopolyBlessingListResp>
      PARSER = new com.google.protobuf.AbstractParser<MonopolyBlessingListResp>() {
    @java.lang.Override
    public MonopolyBlessingListResp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<MonopolyBlessingListResp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<MonopolyBlessingListResp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.MonopolyBlessingListResp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

