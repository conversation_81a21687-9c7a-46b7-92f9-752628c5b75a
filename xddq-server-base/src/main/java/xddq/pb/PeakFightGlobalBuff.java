// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.PeakFightGlobalBuff}
 */
public final class PeakFightGlobalBuff extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.PeakFightGlobalBuff)
    PeakFightGlobalBuffOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      PeakFightGlobalBuff.class.getName());
  }
  // Use PeakFightGlobalBuff.newBuilder() to construct.
  private PeakFightGlobalBuff(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private PeakFightGlobalBuff() {
    buff_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_PeakFightGlobalBuff_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_PeakFightGlobalBuff_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.PeakFightGlobalBuff.class, xddq.pb.PeakFightGlobalBuff.Builder.class);
  }

  private int bitField0_;
  public static final int UNIONID_FIELD_NUMBER = 1;
  private long unionId_ = 0L;
  /**
   * <code>optional int64 unionId = 1;</code>
   * @return Whether the unionId field is set.
   */
  @java.lang.Override
  public boolean hasUnionId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int64 unionId = 1;</code>
   * @return The unionId.
   */
  @java.lang.Override
  public long getUnionId() {
    return unionId_;
  }

  public static final int BUFF_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.PeakFightBuffMsg> buff_;
  /**
   * <code>repeated .xddq.pb.PeakFightBuffMsg buff = 2;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.PeakFightBuffMsg> getBuffList() {
    return buff_;
  }
  /**
   * <code>repeated .xddq.pb.PeakFightBuffMsg buff = 2;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.PeakFightBuffMsgOrBuilder> 
      getBuffOrBuilderList() {
    return buff_;
  }
  /**
   * <code>repeated .xddq.pb.PeakFightBuffMsg buff = 2;</code>
   */
  @java.lang.Override
  public int getBuffCount() {
    return buff_.size();
  }
  /**
   * <code>repeated .xddq.pb.PeakFightBuffMsg buff = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.PeakFightBuffMsg getBuff(int index) {
    return buff_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.PeakFightBuffMsg buff = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.PeakFightBuffMsgOrBuilder getBuffOrBuilder(
      int index) {
    return buff_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt64(1, unionId_);
    }
    for (int i = 0; i < buff_.size(); i++) {
      output.writeMessage(2, buff_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(1, unionId_);
    }
    for (int i = 0; i < buff_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, buff_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.PeakFightGlobalBuff)) {
      return super.equals(obj);
    }
    xddq.pb.PeakFightGlobalBuff other = (xddq.pb.PeakFightGlobalBuff) obj;

    if (hasUnionId() != other.hasUnionId()) return false;
    if (hasUnionId()) {
      if (getUnionId()
          != other.getUnionId()) return false;
    }
    if (!getBuffList()
        .equals(other.getBuffList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasUnionId()) {
      hash = (37 * hash) + UNIONID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getUnionId());
    }
    if (getBuffCount() > 0) {
      hash = (37 * hash) + BUFF_FIELD_NUMBER;
      hash = (53 * hash) + getBuffList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.PeakFightGlobalBuff parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PeakFightGlobalBuff parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PeakFightGlobalBuff parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PeakFightGlobalBuff parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PeakFightGlobalBuff parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PeakFightGlobalBuff parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PeakFightGlobalBuff parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.PeakFightGlobalBuff parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.PeakFightGlobalBuff parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.PeakFightGlobalBuff parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.PeakFightGlobalBuff parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.PeakFightGlobalBuff parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.PeakFightGlobalBuff prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.PeakFightGlobalBuff}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.PeakFightGlobalBuff)
      xddq.pb.PeakFightGlobalBuffOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PeakFightGlobalBuff_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PeakFightGlobalBuff_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.PeakFightGlobalBuff.class, xddq.pb.PeakFightGlobalBuff.Builder.class);
    }

    // Construct using xddq.pb.PeakFightGlobalBuff.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      unionId_ = 0L;
      if (buffBuilder_ == null) {
        buff_ = java.util.Collections.emptyList();
      } else {
        buff_ = null;
        buffBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000002);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PeakFightGlobalBuff_descriptor;
    }

    @java.lang.Override
    public xddq.pb.PeakFightGlobalBuff getDefaultInstanceForType() {
      return xddq.pb.PeakFightGlobalBuff.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.PeakFightGlobalBuff build() {
      xddq.pb.PeakFightGlobalBuff result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.PeakFightGlobalBuff buildPartial() {
      xddq.pb.PeakFightGlobalBuff result = new xddq.pb.PeakFightGlobalBuff(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.PeakFightGlobalBuff result) {
      if (buffBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          buff_ = java.util.Collections.unmodifiableList(buff_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.buff_ = buff_;
      } else {
        result.buff_ = buffBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.PeakFightGlobalBuff result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.unionId_ = unionId_;
        to_bitField0_ |= 0x00000001;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.PeakFightGlobalBuff) {
        return mergeFrom((xddq.pb.PeakFightGlobalBuff)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.PeakFightGlobalBuff other) {
      if (other == xddq.pb.PeakFightGlobalBuff.getDefaultInstance()) return this;
      if (other.hasUnionId()) {
        setUnionId(other.getUnionId());
      }
      if (buffBuilder_ == null) {
        if (!other.buff_.isEmpty()) {
          if (buff_.isEmpty()) {
            buff_ = other.buff_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureBuffIsMutable();
            buff_.addAll(other.buff_);
          }
          onChanged();
        }
      } else {
        if (!other.buff_.isEmpty()) {
          if (buffBuilder_.isEmpty()) {
            buffBuilder_.dispose();
            buffBuilder_ = null;
            buff_ = other.buff_;
            bitField0_ = (bitField0_ & ~0x00000002);
            buffBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetBuffFieldBuilder() : null;
          } else {
            buffBuilder_.addAllMessages(other.buff_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              unionId_ = input.readInt64();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              xddq.pb.PeakFightBuffMsg m =
                  input.readMessage(
                      xddq.pb.PeakFightBuffMsg.parser(),
                      extensionRegistry);
              if (buffBuilder_ == null) {
                ensureBuffIsMutable();
                buff_.add(m);
              } else {
                buffBuilder_.addMessage(m);
              }
              break;
            } // case 18
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private long unionId_ ;
    /**
     * <code>optional int64 unionId = 1;</code>
     * @return Whether the unionId field is set.
     */
    @java.lang.Override
    public boolean hasUnionId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 unionId = 1;</code>
     * @return The unionId.
     */
    @java.lang.Override
    public long getUnionId() {
      return unionId_;
    }
    /**
     * <code>optional int64 unionId = 1;</code>
     * @param value The unionId to set.
     * @return This builder for chaining.
     */
    public Builder setUnionId(long value) {

      unionId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 unionId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearUnionId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      unionId_ = 0L;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.PeakFightBuffMsg> buff_ =
      java.util.Collections.emptyList();
    private void ensureBuffIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        buff_ = new java.util.ArrayList<xddq.pb.PeakFightBuffMsg>(buff_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.PeakFightBuffMsg, xddq.pb.PeakFightBuffMsg.Builder, xddq.pb.PeakFightBuffMsgOrBuilder> buffBuilder_;

    /**
     * <code>repeated .xddq.pb.PeakFightBuffMsg buff = 2;</code>
     */
    public java.util.List<xddq.pb.PeakFightBuffMsg> getBuffList() {
      if (buffBuilder_ == null) {
        return java.util.Collections.unmodifiableList(buff_);
      } else {
        return buffBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.PeakFightBuffMsg buff = 2;</code>
     */
    public int getBuffCount() {
      if (buffBuilder_ == null) {
        return buff_.size();
      } else {
        return buffBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.PeakFightBuffMsg buff = 2;</code>
     */
    public xddq.pb.PeakFightBuffMsg getBuff(int index) {
      if (buffBuilder_ == null) {
        return buff_.get(index);
      } else {
        return buffBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.PeakFightBuffMsg buff = 2;</code>
     */
    public Builder setBuff(
        int index, xddq.pb.PeakFightBuffMsg value) {
      if (buffBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureBuffIsMutable();
        buff_.set(index, value);
        onChanged();
      } else {
        buffBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PeakFightBuffMsg buff = 2;</code>
     */
    public Builder setBuff(
        int index, xddq.pb.PeakFightBuffMsg.Builder builderForValue) {
      if (buffBuilder_ == null) {
        ensureBuffIsMutable();
        buff_.set(index, builderForValue.build());
        onChanged();
      } else {
        buffBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PeakFightBuffMsg buff = 2;</code>
     */
    public Builder addBuff(xddq.pb.PeakFightBuffMsg value) {
      if (buffBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureBuffIsMutable();
        buff_.add(value);
        onChanged();
      } else {
        buffBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PeakFightBuffMsg buff = 2;</code>
     */
    public Builder addBuff(
        int index, xddq.pb.PeakFightBuffMsg value) {
      if (buffBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureBuffIsMutable();
        buff_.add(index, value);
        onChanged();
      } else {
        buffBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PeakFightBuffMsg buff = 2;</code>
     */
    public Builder addBuff(
        xddq.pb.PeakFightBuffMsg.Builder builderForValue) {
      if (buffBuilder_ == null) {
        ensureBuffIsMutable();
        buff_.add(builderForValue.build());
        onChanged();
      } else {
        buffBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PeakFightBuffMsg buff = 2;</code>
     */
    public Builder addBuff(
        int index, xddq.pb.PeakFightBuffMsg.Builder builderForValue) {
      if (buffBuilder_ == null) {
        ensureBuffIsMutable();
        buff_.add(index, builderForValue.build());
        onChanged();
      } else {
        buffBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PeakFightBuffMsg buff = 2;</code>
     */
    public Builder addAllBuff(
        java.lang.Iterable<? extends xddq.pb.PeakFightBuffMsg> values) {
      if (buffBuilder_ == null) {
        ensureBuffIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, buff_);
        onChanged();
      } else {
        buffBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PeakFightBuffMsg buff = 2;</code>
     */
    public Builder clearBuff() {
      if (buffBuilder_ == null) {
        buff_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        buffBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PeakFightBuffMsg buff = 2;</code>
     */
    public Builder removeBuff(int index) {
      if (buffBuilder_ == null) {
        ensureBuffIsMutable();
        buff_.remove(index);
        onChanged();
      } else {
        buffBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PeakFightBuffMsg buff = 2;</code>
     */
    public xddq.pb.PeakFightBuffMsg.Builder getBuffBuilder(
        int index) {
      return internalGetBuffFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.PeakFightBuffMsg buff = 2;</code>
     */
    public xddq.pb.PeakFightBuffMsgOrBuilder getBuffOrBuilder(
        int index) {
      if (buffBuilder_ == null) {
        return buff_.get(index);  } else {
        return buffBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.PeakFightBuffMsg buff = 2;</code>
     */
    public java.util.List<? extends xddq.pb.PeakFightBuffMsgOrBuilder> 
         getBuffOrBuilderList() {
      if (buffBuilder_ != null) {
        return buffBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(buff_);
      }
    }
    /**
     * <code>repeated .xddq.pb.PeakFightBuffMsg buff = 2;</code>
     */
    public xddq.pb.PeakFightBuffMsg.Builder addBuffBuilder() {
      return internalGetBuffFieldBuilder().addBuilder(
          xddq.pb.PeakFightBuffMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.PeakFightBuffMsg buff = 2;</code>
     */
    public xddq.pb.PeakFightBuffMsg.Builder addBuffBuilder(
        int index) {
      return internalGetBuffFieldBuilder().addBuilder(
          index, xddq.pb.PeakFightBuffMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.PeakFightBuffMsg buff = 2;</code>
     */
    public java.util.List<xddq.pb.PeakFightBuffMsg.Builder> 
         getBuffBuilderList() {
      return internalGetBuffFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.PeakFightBuffMsg, xddq.pb.PeakFightBuffMsg.Builder, xddq.pb.PeakFightBuffMsgOrBuilder> 
        internalGetBuffFieldBuilder() {
      if (buffBuilder_ == null) {
        buffBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.PeakFightBuffMsg, xddq.pb.PeakFightBuffMsg.Builder, xddq.pb.PeakFightBuffMsgOrBuilder>(
                buff_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        buff_ = null;
      }
      return buffBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.PeakFightGlobalBuff)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.PeakFightGlobalBuff)
  private static final xddq.pb.PeakFightGlobalBuff DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.PeakFightGlobalBuff();
  }

  public static xddq.pb.PeakFightGlobalBuff getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<PeakFightGlobalBuff>
      PARSER = new com.google.protobuf.AbstractParser<PeakFightGlobalBuff>() {
    @java.lang.Override
    public PeakFightGlobalBuff parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<PeakFightGlobalBuff> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<PeakFightGlobalBuff> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.PeakFightGlobalBuff getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

