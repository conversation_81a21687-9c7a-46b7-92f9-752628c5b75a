// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.GodDemonBattleDuelVideoInfoResp}
 */
public final class GodDemonBattleDuelVideoInfoResp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.GodDemonBattleDuelVideoInfoResp)
    GodDemonBattleDuelVideoInfoRespOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      GodDemonBattleDuelVideoInfoResp.class.getName());
  }
  // Use GodDemonBattleDuelVideoInfoResp.newBuilder() to construct.
  private GodDemonBattleDuelVideoInfoResp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private GodDemonBattleDuelVideoInfoResp() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_GodDemonBattleDuelVideoInfoResp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_GodDemonBattleDuelVideoInfoResp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.GodDemonBattleDuelVideoInfoResp.class, xddq.pb.GodDemonBattleDuelVideoInfoResp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int ALLBATTLENUM_FIELD_NUMBER = 2;
  private int allBattleNum_ = 0;
  /**
   * <code>optional int32 allBattleNum = 2;</code>
   * @return Whether the allBattleNum field is set.
   */
  @java.lang.Override
  public boolean hasAllBattleNum() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int32 allBattleNum = 2;</code>
   * @return The allBattleNum.
   */
  @java.lang.Override
  public int getAllBattleNum() {
    return allBattleNum_;
  }

  public static final int VIDEODATA_FIELD_NUMBER = 3;
  private xddq.pb.GodDemonBattleGetVideoMsg videoData_;
  /**
   * <code>optional .xddq.pb.GodDemonBattleGetVideoMsg videoData = 3;</code>
   * @return Whether the videoData field is set.
   */
  @java.lang.Override
  public boolean hasVideoData() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional .xddq.pb.GodDemonBattleGetVideoMsg videoData = 3;</code>
   * @return The videoData.
   */
  @java.lang.Override
  public xddq.pb.GodDemonBattleGetVideoMsg getVideoData() {
    return videoData_ == null ? xddq.pb.GodDemonBattleGetVideoMsg.getDefaultInstance() : videoData_;
  }
  /**
   * <code>optional .xddq.pb.GodDemonBattleGetVideoMsg videoData = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.GodDemonBattleGetVideoMsgOrBuilder getVideoDataOrBuilder() {
    return videoData_ == null ? xddq.pb.GodDemonBattleGetVideoMsg.getDefaultInstance() : videoData_;
  }

  public static final int CURBATTLEIDX_FIELD_NUMBER = 4;
  private int curBattleIdx_ = 0;
  /**
   * <code>optional int32 curBattleIdx = 4;</code>
   * @return Whether the curBattleIdx field is set.
   */
  @java.lang.Override
  public boolean hasCurBattleIdx() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional int32 curBattleIdx = 4;</code>
   * @return The curBattleIdx.
   */
  @java.lang.Override
  public int getCurBattleIdx() {
    return curBattleIdx_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (hasVideoData()) {
      if (!getVideoData().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(2, allBattleNum_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeMessage(3, getVideoData());
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt32(4, curBattleIdx_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, allBattleNum_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, getVideoData());
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(4, curBattleIdx_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.GodDemonBattleDuelVideoInfoResp)) {
      return super.equals(obj);
    }
    xddq.pb.GodDemonBattleDuelVideoInfoResp other = (xddq.pb.GodDemonBattleDuelVideoInfoResp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (hasAllBattleNum() != other.hasAllBattleNum()) return false;
    if (hasAllBattleNum()) {
      if (getAllBattleNum()
          != other.getAllBattleNum()) return false;
    }
    if (hasVideoData() != other.hasVideoData()) return false;
    if (hasVideoData()) {
      if (!getVideoData()
          .equals(other.getVideoData())) return false;
    }
    if (hasCurBattleIdx() != other.hasCurBattleIdx()) return false;
    if (hasCurBattleIdx()) {
      if (getCurBattleIdx()
          != other.getCurBattleIdx()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (hasAllBattleNum()) {
      hash = (37 * hash) + ALLBATTLENUM_FIELD_NUMBER;
      hash = (53 * hash) + getAllBattleNum();
    }
    if (hasVideoData()) {
      hash = (37 * hash) + VIDEODATA_FIELD_NUMBER;
      hash = (53 * hash) + getVideoData().hashCode();
    }
    if (hasCurBattleIdx()) {
      hash = (37 * hash) + CURBATTLEIDX_FIELD_NUMBER;
      hash = (53 * hash) + getCurBattleIdx();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.GodDemonBattleDuelVideoInfoResp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GodDemonBattleDuelVideoInfoResp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GodDemonBattleDuelVideoInfoResp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GodDemonBattleDuelVideoInfoResp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GodDemonBattleDuelVideoInfoResp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GodDemonBattleDuelVideoInfoResp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GodDemonBattleDuelVideoInfoResp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.GodDemonBattleDuelVideoInfoResp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.GodDemonBattleDuelVideoInfoResp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.GodDemonBattleDuelVideoInfoResp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.GodDemonBattleDuelVideoInfoResp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.GodDemonBattleDuelVideoInfoResp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.GodDemonBattleDuelVideoInfoResp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.GodDemonBattleDuelVideoInfoResp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.GodDemonBattleDuelVideoInfoResp)
      xddq.pb.GodDemonBattleDuelVideoInfoRespOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GodDemonBattleDuelVideoInfoResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GodDemonBattleDuelVideoInfoResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.GodDemonBattleDuelVideoInfoResp.class, xddq.pb.GodDemonBattleDuelVideoInfoResp.Builder.class);
    }

    // Construct using xddq.pb.GodDemonBattleDuelVideoInfoResp.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetVideoDataFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      allBattleNum_ = 0;
      videoData_ = null;
      if (videoDataBuilder_ != null) {
        videoDataBuilder_.dispose();
        videoDataBuilder_ = null;
      }
      curBattleIdx_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GodDemonBattleDuelVideoInfoResp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.GodDemonBattleDuelVideoInfoResp getDefaultInstanceForType() {
      return xddq.pb.GodDemonBattleDuelVideoInfoResp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.GodDemonBattleDuelVideoInfoResp build() {
      xddq.pb.GodDemonBattleDuelVideoInfoResp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.GodDemonBattleDuelVideoInfoResp buildPartial() {
      xddq.pb.GodDemonBattleDuelVideoInfoResp result = new xddq.pb.GodDemonBattleDuelVideoInfoResp(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.GodDemonBattleDuelVideoInfoResp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.allBattleNum_ = allBattleNum_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.videoData_ = videoDataBuilder_ == null
            ? videoData_
            : videoDataBuilder_.build();
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.curBattleIdx_ = curBattleIdx_;
        to_bitField0_ |= 0x00000008;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.GodDemonBattleDuelVideoInfoResp) {
        return mergeFrom((xddq.pb.GodDemonBattleDuelVideoInfoResp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.GodDemonBattleDuelVideoInfoResp other) {
      if (other == xddq.pb.GodDemonBattleDuelVideoInfoResp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (other.hasAllBattleNum()) {
        setAllBattleNum(other.getAllBattleNum());
      }
      if (other.hasVideoData()) {
        mergeVideoData(other.getVideoData());
      }
      if (other.hasCurBattleIdx()) {
        setCurBattleIdx(other.getCurBattleIdx());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      if (hasVideoData()) {
        if (!getVideoData().isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              allBattleNum_ = input.readInt32();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 26: {
              input.readMessage(
                  internalGetVideoDataFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 32: {
              curBattleIdx_ = input.readInt32();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private int allBattleNum_ ;
    /**
     * <code>optional int32 allBattleNum = 2;</code>
     * @return Whether the allBattleNum field is set.
     */
    @java.lang.Override
    public boolean hasAllBattleNum() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 allBattleNum = 2;</code>
     * @return The allBattleNum.
     */
    @java.lang.Override
    public int getAllBattleNum() {
      return allBattleNum_;
    }
    /**
     * <code>optional int32 allBattleNum = 2;</code>
     * @param value The allBattleNum to set.
     * @return This builder for chaining.
     */
    public Builder setAllBattleNum(int value) {

      allBattleNum_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 allBattleNum = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearAllBattleNum() {
      bitField0_ = (bitField0_ & ~0x00000002);
      allBattleNum_ = 0;
      onChanged();
      return this;
    }

    private xddq.pb.GodDemonBattleGetVideoMsg videoData_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.GodDemonBattleGetVideoMsg, xddq.pb.GodDemonBattleGetVideoMsg.Builder, xddq.pb.GodDemonBattleGetVideoMsgOrBuilder> videoDataBuilder_;
    /**
     * <code>optional .xddq.pb.GodDemonBattleGetVideoMsg videoData = 3;</code>
     * @return Whether the videoData field is set.
     */
    public boolean hasVideoData() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional .xddq.pb.GodDemonBattleGetVideoMsg videoData = 3;</code>
     * @return The videoData.
     */
    public xddq.pb.GodDemonBattleGetVideoMsg getVideoData() {
      if (videoDataBuilder_ == null) {
        return videoData_ == null ? xddq.pb.GodDemonBattleGetVideoMsg.getDefaultInstance() : videoData_;
      } else {
        return videoDataBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.GodDemonBattleGetVideoMsg videoData = 3;</code>
     */
    public Builder setVideoData(xddq.pb.GodDemonBattleGetVideoMsg value) {
      if (videoDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        videoData_ = value;
      } else {
        videoDataBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.GodDemonBattleGetVideoMsg videoData = 3;</code>
     */
    public Builder setVideoData(
        xddq.pb.GodDemonBattleGetVideoMsg.Builder builderForValue) {
      if (videoDataBuilder_ == null) {
        videoData_ = builderForValue.build();
      } else {
        videoDataBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.GodDemonBattleGetVideoMsg videoData = 3;</code>
     */
    public Builder mergeVideoData(xddq.pb.GodDemonBattleGetVideoMsg value) {
      if (videoDataBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0) &&
          videoData_ != null &&
          videoData_ != xddq.pb.GodDemonBattleGetVideoMsg.getDefaultInstance()) {
          getVideoDataBuilder().mergeFrom(value);
        } else {
          videoData_ = value;
        }
      } else {
        videoDataBuilder_.mergeFrom(value);
      }
      if (videoData_ != null) {
        bitField0_ |= 0x00000004;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.GodDemonBattleGetVideoMsg videoData = 3;</code>
     */
    public Builder clearVideoData() {
      bitField0_ = (bitField0_ & ~0x00000004);
      videoData_ = null;
      if (videoDataBuilder_ != null) {
        videoDataBuilder_.dispose();
        videoDataBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.GodDemonBattleGetVideoMsg videoData = 3;</code>
     */
    public xddq.pb.GodDemonBattleGetVideoMsg.Builder getVideoDataBuilder() {
      bitField0_ |= 0x00000004;
      onChanged();
      return internalGetVideoDataFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.GodDemonBattleGetVideoMsg videoData = 3;</code>
     */
    public xddq.pb.GodDemonBattleGetVideoMsgOrBuilder getVideoDataOrBuilder() {
      if (videoDataBuilder_ != null) {
        return videoDataBuilder_.getMessageOrBuilder();
      } else {
        return videoData_ == null ?
            xddq.pb.GodDemonBattleGetVideoMsg.getDefaultInstance() : videoData_;
      }
    }
    /**
     * <code>optional .xddq.pb.GodDemonBattleGetVideoMsg videoData = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.GodDemonBattleGetVideoMsg, xddq.pb.GodDemonBattleGetVideoMsg.Builder, xddq.pb.GodDemonBattleGetVideoMsgOrBuilder> 
        internalGetVideoDataFieldBuilder() {
      if (videoDataBuilder_ == null) {
        videoDataBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.GodDemonBattleGetVideoMsg, xddq.pb.GodDemonBattleGetVideoMsg.Builder, xddq.pb.GodDemonBattleGetVideoMsgOrBuilder>(
                getVideoData(),
                getParentForChildren(),
                isClean());
        videoData_ = null;
      }
      return videoDataBuilder_;
    }

    private int curBattleIdx_ ;
    /**
     * <code>optional int32 curBattleIdx = 4;</code>
     * @return Whether the curBattleIdx field is set.
     */
    @java.lang.Override
    public boolean hasCurBattleIdx() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int32 curBattleIdx = 4;</code>
     * @return The curBattleIdx.
     */
    @java.lang.Override
    public int getCurBattleIdx() {
      return curBattleIdx_;
    }
    /**
     * <code>optional int32 curBattleIdx = 4;</code>
     * @param value The curBattleIdx to set.
     * @return This builder for chaining.
     */
    public Builder setCurBattleIdx(int value) {

      curBattleIdx_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 curBattleIdx = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearCurBattleIdx() {
      bitField0_ = (bitField0_ & ~0x00000008);
      curBattleIdx_ = 0;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.GodDemonBattleDuelVideoInfoResp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.GodDemonBattleDuelVideoInfoResp)
  private static final xddq.pb.GodDemonBattleDuelVideoInfoResp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.GodDemonBattleDuelVideoInfoResp();
  }

  public static xddq.pb.GodDemonBattleDuelVideoInfoResp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<GodDemonBattleDuelVideoInfoResp>
      PARSER = new com.google.protobuf.AbstractParser<GodDemonBattleDuelVideoInfoResp>() {
    @java.lang.Override
    public GodDemonBattleDuelVideoInfoResp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<GodDemonBattleDuelVideoInfoResp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<GodDemonBattleDuelVideoInfoResp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.GodDemonBattleDuelVideoInfoResp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

