// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.DragonHomeExploreDataMsg}
 */
public final class DragonHomeExploreDataMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.DragonHomeExploreDataMsg)
    DragonHomeExploreDataMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      DragonHomeExploreDataMsg.class.getName());
  }
  // Use DragonHomeExploreDataMsg.newBuilder() to construct.
  private DragonHomeExploreDataMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private DragonHomeExploreDataMsg() {
    data_ = java.util.Collections.emptyList();
    playerData_ = java.util.Collections.emptyList();
    exitPosition_ = java.util.Collections.emptyList();
    monsterInfo_ = java.util.Collections.emptyList();
    mallInfo_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_DragonHomeExploreDataMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_DragonHomeExploreDataMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.DragonHomeExploreDataMsg.class, xddq.pb.DragonHomeExploreDataMsg.Builder.class);
  }

  private int bitField0_;
  public static final int FLOOR_FIELD_NUMBER = 1;
  private int floor_ = 0;
  /**
   * <code>required int32 floor = 1;</code>
   * @return Whether the floor field is set.
   */
  @java.lang.Override
  public boolean hasFloor() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 floor = 1;</code>
   * @return The floor.
   */
  @java.lang.Override
  public int getFloor() {
    return floor_;
  }

  public static final int DATA_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.MapTile> data_;
  /**
   * <code>repeated .xddq.pb.MapTile data = 2;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.MapTile> getDataList() {
    return data_;
  }
  /**
   * <code>repeated .xddq.pb.MapTile data = 2;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.MapTileOrBuilder> 
      getDataOrBuilderList() {
    return data_;
  }
  /**
   * <code>repeated .xddq.pb.MapTile data = 2;</code>
   */
  @java.lang.Override
  public int getDataCount() {
    return data_.size();
  }
  /**
   * <code>repeated .xddq.pb.MapTile data = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.MapTile getData(int index) {
    return data_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.MapTile data = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.MapTileOrBuilder getDataOrBuilder(
      int index) {
    return data_.get(index);
  }

  public static final int PLAYERDATA_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.DragonHomeExplorePlayerDataMsg> playerData_;
  /**
   * <code>repeated .xddq.pb.DragonHomeExplorePlayerDataMsg playerData = 3;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.DragonHomeExplorePlayerDataMsg> getPlayerDataList() {
    return playerData_;
  }
  /**
   * <code>repeated .xddq.pb.DragonHomeExplorePlayerDataMsg playerData = 3;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.DragonHomeExplorePlayerDataMsgOrBuilder> 
      getPlayerDataOrBuilderList() {
    return playerData_;
  }
  /**
   * <code>repeated .xddq.pb.DragonHomeExplorePlayerDataMsg playerData = 3;</code>
   */
  @java.lang.Override
  public int getPlayerDataCount() {
    return playerData_.size();
  }
  /**
   * <code>repeated .xddq.pb.DragonHomeExplorePlayerDataMsg playerData = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.DragonHomeExplorePlayerDataMsg getPlayerData(int index) {
    return playerData_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.DragonHomeExplorePlayerDataMsg playerData = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.DragonHomeExplorePlayerDataMsgOrBuilder getPlayerDataOrBuilder(
      int index) {
    return playerData_.get(index);
  }

  public static final int RANK_FIELD_NUMBER = 4;
  private int rank_ = 0;
  /**
   * <code>optional int32 rank = 4;</code>
   * @return Whether the rank field is set.
   */
  @java.lang.Override
  public boolean hasRank() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int32 rank = 4;</code>
   * @return The rank.
   */
  @java.lang.Override
  public int getRank() {
    return rank_;
  }

  public static final int KEYCOUNT_FIELD_NUMBER = 5;
  private int keyCount_ = 0;
  /**
   * <code>optional int32 keyCount = 5;</code>
   * @return Whether the keyCount field is set.
   */
  @java.lang.Override
  public boolean hasKeyCount() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional int32 keyCount = 5;</code>
   * @return The keyCount.
   */
  @java.lang.Override
  public int getKeyCount() {
    return keyCount_;
  }

  public static final int ISAUTOUNLOCK_FIELD_NUMBER = 7;
  private boolean isAutoUnLock_ = false;
  /**
   * <code>optional bool isAutoUnLock = 7;</code>
   * @return Whether the isAutoUnLock field is set.
   */
  @java.lang.Override
  public boolean hasIsAutoUnLock() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional bool isAutoUnLock = 7;</code>
   * @return The isAutoUnLock.
   */
  @java.lang.Override
  public boolean getIsAutoUnLock() {
    return isAutoUnLock_;
  }

  public static final int EXITPOSITION_FIELD_NUMBER = 8;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.DragonHomePositionMsg> exitPosition_;
  /**
   * <code>repeated .xddq.pb.DragonHomePositionMsg exitPosition = 8;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.DragonHomePositionMsg> getExitPositionList() {
    return exitPosition_;
  }
  /**
   * <code>repeated .xddq.pb.DragonHomePositionMsg exitPosition = 8;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.DragonHomePositionMsgOrBuilder> 
      getExitPositionOrBuilderList() {
    return exitPosition_;
  }
  /**
   * <code>repeated .xddq.pb.DragonHomePositionMsg exitPosition = 8;</code>
   */
  @java.lang.Override
  public int getExitPositionCount() {
    return exitPosition_.size();
  }
  /**
   * <code>repeated .xddq.pb.DragonHomePositionMsg exitPosition = 8;</code>
   */
  @java.lang.Override
  public xddq.pb.DragonHomePositionMsg getExitPosition(int index) {
    return exitPosition_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.DragonHomePositionMsg exitPosition = 8;</code>
   */
  @java.lang.Override
  public xddq.pb.DragonHomePositionMsgOrBuilder getExitPositionOrBuilder(
      int index) {
    return exitPosition_.get(index);
  }

  public static final int MONSTERINFO_FIELD_NUMBER = 9;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.DragonHomeMonsterMsg> monsterInfo_;
  /**
   * <code>repeated .xddq.pb.DragonHomeMonsterMsg monsterInfo = 9;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.DragonHomeMonsterMsg> getMonsterInfoList() {
    return monsterInfo_;
  }
  /**
   * <code>repeated .xddq.pb.DragonHomeMonsterMsg monsterInfo = 9;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.DragonHomeMonsterMsgOrBuilder> 
      getMonsterInfoOrBuilderList() {
    return monsterInfo_;
  }
  /**
   * <code>repeated .xddq.pb.DragonHomeMonsterMsg monsterInfo = 9;</code>
   */
  @java.lang.Override
  public int getMonsterInfoCount() {
    return monsterInfo_.size();
  }
  /**
   * <code>repeated .xddq.pb.DragonHomeMonsterMsg monsterInfo = 9;</code>
   */
  @java.lang.Override
  public xddq.pb.DragonHomeMonsterMsg getMonsterInfo(int index) {
    return monsterInfo_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.DragonHomeMonsterMsg monsterInfo = 9;</code>
   */
  @java.lang.Override
  public xddq.pb.DragonHomeMonsterMsgOrBuilder getMonsterInfoOrBuilder(
      int index) {
    return monsterInfo_.get(index);
  }

  public static final int HAVEKEY1_FIELD_NUMBER = 10;
  private boolean haveKey1_ = false;
  /**
   * <code>optional bool haveKey1 = 10;</code>
   * @return Whether the haveKey1 field is set.
   */
  @java.lang.Override
  public boolean hasHaveKey1() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>optional bool haveKey1 = 10;</code>
   * @return The haveKey1.
   */
  @java.lang.Override
  public boolean getHaveKey1() {
    return haveKey1_;
  }

  public static final int HAVEKEY2_FIELD_NUMBER = 11;
  private boolean haveKey2_ = false;
  /**
   * <code>optional bool haveKey2 = 11;</code>
   * @return Whether the haveKey2 field is set.
   */
  @java.lang.Override
  public boolean hasHaveKey2() {
    return ((bitField0_ & 0x00000020) != 0);
  }
  /**
   * <code>optional bool haveKey2 = 11;</code>
   * @return The haveKey2.
   */
  @java.lang.Override
  public boolean getHaveKey2() {
    return haveKey2_;
  }

  public static final int MALLINFO_FIELD_NUMBER = 12;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.DragonHomeMallInfo> mallInfo_;
  /**
   * <code>repeated .xddq.pb.DragonHomeMallInfo mallInfo = 12;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.DragonHomeMallInfo> getMallInfoList() {
    return mallInfo_;
  }
  /**
   * <code>repeated .xddq.pb.DragonHomeMallInfo mallInfo = 12;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.DragonHomeMallInfoOrBuilder> 
      getMallInfoOrBuilderList() {
    return mallInfo_;
  }
  /**
   * <code>repeated .xddq.pb.DragonHomeMallInfo mallInfo = 12;</code>
   */
  @java.lang.Override
  public int getMallInfoCount() {
    return mallInfo_.size();
  }
  /**
   * <code>repeated .xddq.pb.DragonHomeMallInfo mallInfo = 12;</code>
   */
  @java.lang.Override
  public xddq.pb.DragonHomeMallInfo getMallInfo(int index) {
    return mallInfo_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.DragonHomeMallInfo mallInfo = 12;</code>
   */
  @java.lang.Override
  public xddq.pb.DragonHomeMallInfoOrBuilder getMallInfoOrBuilder(
      int index) {
    return mallInfo_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasFloor()) {
      memoizedIsInitialized = 0;
      return false;
    }
    for (int i = 0; i < getDataCount(); i++) {
      if (!getData(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    for (int i = 0; i < getPlayerDataCount(); i++) {
      if (!getPlayerData(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    for (int i = 0; i < getExitPositionCount(); i++) {
      if (!getExitPosition(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    for (int i = 0; i < getMonsterInfoCount(); i++) {
      if (!getMonsterInfo(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, floor_);
    }
    for (int i = 0; i < data_.size(); i++) {
      output.writeMessage(2, data_.get(i));
    }
    for (int i = 0; i < playerData_.size(); i++) {
      output.writeMessage(3, playerData_.get(i));
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(4, rank_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt32(5, keyCount_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeBool(7, isAutoUnLock_);
    }
    for (int i = 0; i < exitPosition_.size(); i++) {
      output.writeMessage(8, exitPosition_.get(i));
    }
    for (int i = 0; i < monsterInfo_.size(); i++) {
      output.writeMessage(9, monsterInfo_.get(i));
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      output.writeBool(10, haveKey1_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      output.writeBool(11, haveKey2_);
    }
    for (int i = 0; i < mallInfo_.size(); i++) {
      output.writeMessage(12, mallInfo_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, floor_);
    }
    for (int i = 0; i < data_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, data_.get(i));
    }
    for (int i = 0; i < playerData_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, playerData_.get(i));
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(4, rank_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(5, keyCount_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(7, isAutoUnLock_);
    }
    for (int i = 0; i < exitPosition_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(8, exitPosition_.get(i));
    }
    for (int i = 0; i < monsterInfo_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(9, monsterInfo_.get(i));
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(10, haveKey1_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(11, haveKey2_);
    }
    for (int i = 0; i < mallInfo_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(12, mallInfo_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.DragonHomeExploreDataMsg)) {
      return super.equals(obj);
    }
    xddq.pb.DragonHomeExploreDataMsg other = (xddq.pb.DragonHomeExploreDataMsg) obj;

    if (hasFloor() != other.hasFloor()) return false;
    if (hasFloor()) {
      if (getFloor()
          != other.getFloor()) return false;
    }
    if (!getDataList()
        .equals(other.getDataList())) return false;
    if (!getPlayerDataList()
        .equals(other.getPlayerDataList())) return false;
    if (hasRank() != other.hasRank()) return false;
    if (hasRank()) {
      if (getRank()
          != other.getRank()) return false;
    }
    if (hasKeyCount() != other.hasKeyCount()) return false;
    if (hasKeyCount()) {
      if (getKeyCount()
          != other.getKeyCount()) return false;
    }
    if (hasIsAutoUnLock() != other.hasIsAutoUnLock()) return false;
    if (hasIsAutoUnLock()) {
      if (getIsAutoUnLock()
          != other.getIsAutoUnLock()) return false;
    }
    if (!getExitPositionList()
        .equals(other.getExitPositionList())) return false;
    if (!getMonsterInfoList()
        .equals(other.getMonsterInfoList())) return false;
    if (hasHaveKey1() != other.hasHaveKey1()) return false;
    if (hasHaveKey1()) {
      if (getHaveKey1()
          != other.getHaveKey1()) return false;
    }
    if (hasHaveKey2() != other.hasHaveKey2()) return false;
    if (hasHaveKey2()) {
      if (getHaveKey2()
          != other.getHaveKey2()) return false;
    }
    if (!getMallInfoList()
        .equals(other.getMallInfoList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasFloor()) {
      hash = (37 * hash) + FLOOR_FIELD_NUMBER;
      hash = (53 * hash) + getFloor();
    }
    if (getDataCount() > 0) {
      hash = (37 * hash) + DATA_FIELD_NUMBER;
      hash = (53 * hash) + getDataList().hashCode();
    }
    if (getPlayerDataCount() > 0) {
      hash = (37 * hash) + PLAYERDATA_FIELD_NUMBER;
      hash = (53 * hash) + getPlayerDataList().hashCode();
    }
    if (hasRank()) {
      hash = (37 * hash) + RANK_FIELD_NUMBER;
      hash = (53 * hash) + getRank();
    }
    if (hasKeyCount()) {
      hash = (37 * hash) + KEYCOUNT_FIELD_NUMBER;
      hash = (53 * hash) + getKeyCount();
    }
    if (hasIsAutoUnLock()) {
      hash = (37 * hash) + ISAUTOUNLOCK_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getIsAutoUnLock());
    }
    if (getExitPositionCount() > 0) {
      hash = (37 * hash) + EXITPOSITION_FIELD_NUMBER;
      hash = (53 * hash) + getExitPositionList().hashCode();
    }
    if (getMonsterInfoCount() > 0) {
      hash = (37 * hash) + MONSTERINFO_FIELD_NUMBER;
      hash = (53 * hash) + getMonsterInfoList().hashCode();
    }
    if (hasHaveKey1()) {
      hash = (37 * hash) + HAVEKEY1_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getHaveKey1());
    }
    if (hasHaveKey2()) {
      hash = (37 * hash) + HAVEKEY2_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getHaveKey2());
    }
    if (getMallInfoCount() > 0) {
      hash = (37 * hash) + MALLINFO_FIELD_NUMBER;
      hash = (53 * hash) + getMallInfoList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.DragonHomeExploreDataMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.DragonHomeExploreDataMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.DragonHomeExploreDataMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.DragonHomeExploreDataMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.DragonHomeExploreDataMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.DragonHomeExploreDataMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.DragonHomeExploreDataMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.DragonHomeExploreDataMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.DragonHomeExploreDataMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.DragonHomeExploreDataMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.DragonHomeExploreDataMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.DragonHomeExploreDataMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.DragonHomeExploreDataMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.DragonHomeExploreDataMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.DragonHomeExploreDataMsg)
      xddq.pb.DragonHomeExploreDataMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_DragonHomeExploreDataMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_DragonHomeExploreDataMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.DragonHomeExploreDataMsg.class, xddq.pb.DragonHomeExploreDataMsg.Builder.class);
    }

    // Construct using xddq.pb.DragonHomeExploreDataMsg.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      floor_ = 0;
      if (dataBuilder_ == null) {
        data_ = java.util.Collections.emptyList();
      } else {
        data_ = null;
        dataBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000002);
      if (playerDataBuilder_ == null) {
        playerData_ = java.util.Collections.emptyList();
      } else {
        playerData_ = null;
        playerDataBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000004);
      rank_ = 0;
      keyCount_ = 0;
      isAutoUnLock_ = false;
      if (exitPositionBuilder_ == null) {
        exitPosition_ = java.util.Collections.emptyList();
      } else {
        exitPosition_ = null;
        exitPositionBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000040);
      if (monsterInfoBuilder_ == null) {
        monsterInfo_ = java.util.Collections.emptyList();
      } else {
        monsterInfo_ = null;
        monsterInfoBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000080);
      haveKey1_ = false;
      haveKey2_ = false;
      if (mallInfoBuilder_ == null) {
        mallInfo_ = java.util.Collections.emptyList();
      } else {
        mallInfo_ = null;
        mallInfoBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000400);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_DragonHomeExploreDataMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.DragonHomeExploreDataMsg getDefaultInstanceForType() {
      return xddq.pb.DragonHomeExploreDataMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.DragonHomeExploreDataMsg build() {
      xddq.pb.DragonHomeExploreDataMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.DragonHomeExploreDataMsg buildPartial() {
      xddq.pb.DragonHomeExploreDataMsg result = new xddq.pb.DragonHomeExploreDataMsg(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.DragonHomeExploreDataMsg result) {
      if (dataBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          data_ = java.util.Collections.unmodifiableList(data_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.data_ = data_;
      } else {
        result.data_ = dataBuilder_.build();
      }
      if (playerDataBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0)) {
          playerData_ = java.util.Collections.unmodifiableList(playerData_);
          bitField0_ = (bitField0_ & ~0x00000004);
        }
        result.playerData_ = playerData_;
      } else {
        result.playerData_ = playerDataBuilder_.build();
      }
      if (exitPositionBuilder_ == null) {
        if (((bitField0_ & 0x00000040) != 0)) {
          exitPosition_ = java.util.Collections.unmodifiableList(exitPosition_);
          bitField0_ = (bitField0_ & ~0x00000040);
        }
        result.exitPosition_ = exitPosition_;
      } else {
        result.exitPosition_ = exitPositionBuilder_.build();
      }
      if (monsterInfoBuilder_ == null) {
        if (((bitField0_ & 0x00000080) != 0)) {
          monsterInfo_ = java.util.Collections.unmodifiableList(monsterInfo_);
          bitField0_ = (bitField0_ & ~0x00000080);
        }
        result.monsterInfo_ = monsterInfo_;
      } else {
        result.monsterInfo_ = monsterInfoBuilder_.build();
      }
      if (mallInfoBuilder_ == null) {
        if (((bitField0_ & 0x00000400) != 0)) {
          mallInfo_ = java.util.Collections.unmodifiableList(mallInfo_);
          bitField0_ = (bitField0_ & ~0x00000400);
        }
        result.mallInfo_ = mallInfo_;
      } else {
        result.mallInfo_ = mallInfoBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.DragonHomeExploreDataMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.floor_ = floor_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.rank_ = rank_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.keyCount_ = keyCount_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.isAutoUnLock_ = isAutoUnLock_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000100) != 0)) {
        result.haveKey1_ = haveKey1_;
        to_bitField0_ |= 0x00000010;
      }
      if (((from_bitField0_ & 0x00000200) != 0)) {
        result.haveKey2_ = haveKey2_;
        to_bitField0_ |= 0x00000020;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.DragonHomeExploreDataMsg) {
        return mergeFrom((xddq.pb.DragonHomeExploreDataMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.DragonHomeExploreDataMsg other) {
      if (other == xddq.pb.DragonHomeExploreDataMsg.getDefaultInstance()) return this;
      if (other.hasFloor()) {
        setFloor(other.getFloor());
      }
      if (dataBuilder_ == null) {
        if (!other.data_.isEmpty()) {
          if (data_.isEmpty()) {
            data_ = other.data_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureDataIsMutable();
            data_.addAll(other.data_);
          }
          onChanged();
        }
      } else {
        if (!other.data_.isEmpty()) {
          if (dataBuilder_.isEmpty()) {
            dataBuilder_.dispose();
            dataBuilder_ = null;
            data_ = other.data_;
            bitField0_ = (bitField0_ & ~0x00000002);
            dataBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetDataFieldBuilder() : null;
          } else {
            dataBuilder_.addAllMessages(other.data_);
          }
        }
      }
      if (playerDataBuilder_ == null) {
        if (!other.playerData_.isEmpty()) {
          if (playerData_.isEmpty()) {
            playerData_ = other.playerData_;
            bitField0_ = (bitField0_ & ~0x00000004);
          } else {
            ensurePlayerDataIsMutable();
            playerData_.addAll(other.playerData_);
          }
          onChanged();
        }
      } else {
        if (!other.playerData_.isEmpty()) {
          if (playerDataBuilder_.isEmpty()) {
            playerDataBuilder_.dispose();
            playerDataBuilder_ = null;
            playerData_ = other.playerData_;
            bitField0_ = (bitField0_ & ~0x00000004);
            playerDataBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetPlayerDataFieldBuilder() : null;
          } else {
            playerDataBuilder_.addAllMessages(other.playerData_);
          }
        }
      }
      if (other.hasRank()) {
        setRank(other.getRank());
      }
      if (other.hasKeyCount()) {
        setKeyCount(other.getKeyCount());
      }
      if (other.hasIsAutoUnLock()) {
        setIsAutoUnLock(other.getIsAutoUnLock());
      }
      if (exitPositionBuilder_ == null) {
        if (!other.exitPosition_.isEmpty()) {
          if (exitPosition_.isEmpty()) {
            exitPosition_ = other.exitPosition_;
            bitField0_ = (bitField0_ & ~0x00000040);
          } else {
            ensureExitPositionIsMutable();
            exitPosition_.addAll(other.exitPosition_);
          }
          onChanged();
        }
      } else {
        if (!other.exitPosition_.isEmpty()) {
          if (exitPositionBuilder_.isEmpty()) {
            exitPositionBuilder_.dispose();
            exitPositionBuilder_ = null;
            exitPosition_ = other.exitPosition_;
            bitField0_ = (bitField0_ & ~0x00000040);
            exitPositionBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetExitPositionFieldBuilder() : null;
          } else {
            exitPositionBuilder_.addAllMessages(other.exitPosition_);
          }
        }
      }
      if (monsterInfoBuilder_ == null) {
        if (!other.monsterInfo_.isEmpty()) {
          if (monsterInfo_.isEmpty()) {
            monsterInfo_ = other.monsterInfo_;
            bitField0_ = (bitField0_ & ~0x00000080);
          } else {
            ensureMonsterInfoIsMutable();
            monsterInfo_.addAll(other.monsterInfo_);
          }
          onChanged();
        }
      } else {
        if (!other.monsterInfo_.isEmpty()) {
          if (monsterInfoBuilder_.isEmpty()) {
            monsterInfoBuilder_.dispose();
            monsterInfoBuilder_ = null;
            monsterInfo_ = other.monsterInfo_;
            bitField0_ = (bitField0_ & ~0x00000080);
            monsterInfoBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetMonsterInfoFieldBuilder() : null;
          } else {
            monsterInfoBuilder_.addAllMessages(other.monsterInfo_);
          }
        }
      }
      if (other.hasHaveKey1()) {
        setHaveKey1(other.getHaveKey1());
      }
      if (other.hasHaveKey2()) {
        setHaveKey2(other.getHaveKey2());
      }
      if (mallInfoBuilder_ == null) {
        if (!other.mallInfo_.isEmpty()) {
          if (mallInfo_.isEmpty()) {
            mallInfo_ = other.mallInfo_;
            bitField0_ = (bitField0_ & ~0x00000400);
          } else {
            ensureMallInfoIsMutable();
            mallInfo_.addAll(other.mallInfo_);
          }
          onChanged();
        }
      } else {
        if (!other.mallInfo_.isEmpty()) {
          if (mallInfoBuilder_.isEmpty()) {
            mallInfoBuilder_.dispose();
            mallInfoBuilder_ = null;
            mallInfo_ = other.mallInfo_;
            bitField0_ = (bitField0_ & ~0x00000400);
            mallInfoBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetMallInfoFieldBuilder() : null;
          } else {
            mallInfoBuilder_.addAllMessages(other.mallInfo_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasFloor()) {
        return false;
      }
      for (int i = 0; i < getDataCount(); i++) {
        if (!getData(i).isInitialized()) {
          return false;
        }
      }
      for (int i = 0; i < getPlayerDataCount(); i++) {
        if (!getPlayerData(i).isInitialized()) {
          return false;
        }
      }
      for (int i = 0; i < getExitPositionCount(); i++) {
        if (!getExitPosition(i).isInitialized()) {
          return false;
        }
      }
      for (int i = 0; i < getMonsterInfoCount(); i++) {
        if (!getMonsterInfo(i).isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              floor_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              xddq.pb.MapTile m =
                  input.readMessage(
                      xddq.pb.MapTile.parser(),
                      extensionRegistry);
              if (dataBuilder_ == null) {
                ensureDataIsMutable();
                data_.add(m);
              } else {
                dataBuilder_.addMessage(m);
              }
              break;
            } // case 18
            case 26: {
              xddq.pb.DragonHomeExplorePlayerDataMsg m =
                  input.readMessage(
                      xddq.pb.DragonHomeExplorePlayerDataMsg.parser(),
                      extensionRegistry);
              if (playerDataBuilder_ == null) {
                ensurePlayerDataIsMutable();
                playerData_.add(m);
              } else {
                playerDataBuilder_.addMessage(m);
              }
              break;
            } // case 26
            case 32: {
              rank_ = input.readInt32();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            case 40: {
              keyCount_ = input.readInt32();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            case 56: {
              isAutoUnLock_ = input.readBool();
              bitField0_ |= 0x00000020;
              break;
            } // case 56
            case 66: {
              xddq.pb.DragonHomePositionMsg m =
                  input.readMessage(
                      xddq.pb.DragonHomePositionMsg.parser(),
                      extensionRegistry);
              if (exitPositionBuilder_ == null) {
                ensureExitPositionIsMutable();
                exitPosition_.add(m);
              } else {
                exitPositionBuilder_.addMessage(m);
              }
              break;
            } // case 66
            case 74: {
              xddq.pb.DragonHomeMonsterMsg m =
                  input.readMessage(
                      xddq.pb.DragonHomeMonsterMsg.parser(),
                      extensionRegistry);
              if (monsterInfoBuilder_ == null) {
                ensureMonsterInfoIsMutable();
                monsterInfo_.add(m);
              } else {
                monsterInfoBuilder_.addMessage(m);
              }
              break;
            } // case 74
            case 80: {
              haveKey1_ = input.readBool();
              bitField0_ |= 0x00000100;
              break;
            } // case 80
            case 88: {
              haveKey2_ = input.readBool();
              bitField0_ |= 0x00000200;
              break;
            } // case 88
            case 98: {
              xddq.pb.DragonHomeMallInfo m =
                  input.readMessage(
                      xddq.pb.DragonHomeMallInfo.parser(),
                      extensionRegistry);
              if (mallInfoBuilder_ == null) {
                ensureMallInfoIsMutable();
                mallInfo_.add(m);
              } else {
                mallInfoBuilder_.addMessage(m);
              }
              break;
            } // case 98
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int floor_ ;
    /**
     * <code>required int32 floor = 1;</code>
     * @return Whether the floor field is set.
     */
    @java.lang.Override
    public boolean hasFloor() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 floor = 1;</code>
     * @return The floor.
     */
    @java.lang.Override
    public int getFloor() {
      return floor_;
    }
    /**
     * <code>required int32 floor = 1;</code>
     * @param value The floor to set.
     * @return This builder for chaining.
     */
    public Builder setFloor(int value) {

      floor_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 floor = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearFloor() {
      bitField0_ = (bitField0_ & ~0x00000001);
      floor_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.MapTile> data_ =
      java.util.Collections.emptyList();
    private void ensureDataIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        data_ = new java.util.ArrayList<xddq.pb.MapTile>(data_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.MapTile, xddq.pb.MapTile.Builder, xddq.pb.MapTileOrBuilder> dataBuilder_;

    /**
     * <code>repeated .xddq.pb.MapTile data = 2;</code>
     */
    public java.util.List<xddq.pb.MapTile> getDataList() {
      if (dataBuilder_ == null) {
        return java.util.Collections.unmodifiableList(data_);
      } else {
        return dataBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.MapTile data = 2;</code>
     */
    public int getDataCount() {
      if (dataBuilder_ == null) {
        return data_.size();
      } else {
        return dataBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.MapTile data = 2;</code>
     */
    public xddq.pb.MapTile getData(int index) {
      if (dataBuilder_ == null) {
        return data_.get(index);
      } else {
        return dataBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.MapTile data = 2;</code>
     */
    public Builder setData(
        int index, xddq.pb.MapTile value) {
      if (dataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDataIsMutable();
        data_.set(index, value);
        onChanged();
      } else {
        dataBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MapTile data = 2;</code>
     */
    public Builder setData(
        int index, xddq.pb.MapTile.Builder builderForValue) {
      if (dataBuilder_ == null) {
        ensureDataIsMutable();
        data_.set(index, builderForValue.build());
        onChanged();
      } else {
        dataBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MapTile data = 2;</code>
     */
    public Builder addData(xddq.pb.MapTile value) {
      if (dataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDataIsMutable();
        data_.add(value);
        onChanged();
      } else {
        dataBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MapTile data = 2;</code>
     */
    public Builder addData(
        int index, xddq.pb.MapTile value) {
      if (dataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDataIsMutable();
        data_.add(index, value);
        onChanged();
      } else {
        dataBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MapTile data = 2;</code>
     */
    public Builder addData(
        xddq.pb.MapTile.Builder builderForValue) {
      if (dataBuilder_ == null) {
        ensureDataIsMutable();
        data_.add(builderForValue.build());
        onChanged();
      } else {
        dataBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MapTile data = 2;</code>
     */
    public Builder addData(
        int index, xddq.pb.MapTile.Builder builderForValue) {
      if (dataBuilder_ == null) {
        ensureDataIsMutable();
        data_.add(index, builderForValue.build());
        onChanged();
      } else {
        dataBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MapTile data = 2;</code>
     */
    public Builder addAllData(
        java.lang.Iterable<? extends xddq.pb.MapTile> values) {
      if (dataBuilder_ == null) {
        ensureDataIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, data_);
        onChanged();
      } else {
        dataBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MapTile data = 2;</code>
     */
    public Builder clearData() {
      if (dataBuilder_ == null) {
        data_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        dataBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MapTile data = 2;</code>
     */
    public Builder removeData(int index) {
      if (dataBuilder_ == null) {
        ensureDataIsMutable();
        data_.remove(index);
        onChanged();
      } else {
        dataBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MapTile data = 2;</code>
     */
    public xddq.pb.MapTile.Builder getDataBuilder(
        int index) {
      return internalGetDataFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.MapTile data = 2;</code>
     */
    public xddq.pb.MapTileOrBuilder getDataOrBuilder(
        int index) {
      if (dataBuilder_ == null) {
        return data_.get(index);  } else {
        return dataBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.MapTile data = 2;</code>
     */
    public java.util.List<? extends xddq.pb.MapTileOrBuilder> 
         getDataOrBuilderList() {
      if (dataBuilder_ != null) {
        return dataBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(data_);
      }
    }
    /**
     * <code>repeated .xddq.pb.MapTile data = 2;</code>
     */
    public xddq.pb.MapTile.Builder addDataBuilder() {
      return internalGetDataFieldBuilder().addBuilder(
          xddq.pb.MapTile.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.MapTile data = 2;</code>
     */
    public xddq.pb.MapTile.Builder addDataBuilder(
        int index) {
      return internalGetDataFieldBuilder().addBuilder(
          index, xddq.pb.MapTile.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.MapTile data = 2;</code>
     */
    public java.util.List<xddq.pb.MapTile.Builder> 
         getDataBuilderList() {
      return internalGetDataFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.MapTile, xddq.pb.MapTile.Builder, xddq.pb.MapTileOrBuilder> 
        internalGetDataFieldBuilder() {
      if (dataBuilder_ == null) {
        dataBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.MapTile, xddq.pb.MapTile.Builder, xddq.pb.MapTileOrBuilder>(
                data_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        data_ = null;
      }
      return dataBuilder_;
    }

    private java.util.List<xddq.pb.DragonHomeExplorePlayerDataMsg> playerData_ =
      java.util.Collections.emptyList();
    private void ensurePlayerDataIsMutable() {
      if (!((bitField0_ & 0x00000004) != 0)) {
        playerData_ = new java.util.ArrayList<xddq.pb.DragonHomeExplorePlayerDataMsg>(playerData_);
        bitField0_ |= 0x00000004;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.DragonHomeExplorePlayerDataMsg, xddq.pb.DragonHomeExplorePlayerDataMsg.Builder, xddq.pb.DragonHomeExplorePlayerDataMsgOrBuilder> playerDataBuilder_;

    /**
     * <code>repeated .xddq.pb.DragonHomeExplorePlayerDataMsg playerData = 3;</code>
     */
    public java.util.List<xddq.pb.DragonHomeExplorePlayerDataMsg> getPlayerDataList() {
      if (playerDataBuilder_ == null) {
        return java.util.Collections.unmodifiableList(playerData_);
      } else {
        return playerDataBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeExplorePlayerDataMsg playerData = 3;</code>
     */
    public int getPlayerDataCount() {
      if (playerDataBuilder_ == null) {
        return playerData_.size();
      } else {
        return playerDataBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeExplorePlayerDataMsg playerData = 3;</code>
     */
    public xddq.pb.DragonHomeExplorePlayerDataMsg getPlayerData(int index) {
      if (playerDataBuilder_ == null) {
        return playerData_.get(index);
      } else {
        return playerDataBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeExplorePlayerDataMsg playerData = 3;</code>
     */
    public Builder setPlayerData(
        int index, xddq.pb.DragonHomeExplorePlayerDataMsg value) {
      if (playerDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePlayerDataIsMutable();
        playerData_.set(index, value);
        onChanged();
      } else {
        playerDataBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeExplorePlayerDataMsg playerData = 3;</code>
     */
    public Builder setPlayerData(
        int index, xddq.pb.DragonHomeExplorePlayerDataMsg.Builder builderForValue) {
      if (playerDataBuilder_ == null) {
        ensurePlayerDataIsMutable();
        playerData_.set(index, builderForValue.build());
        onChanged();
      } else {
        playerDataBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeExplorePlayerDataMsg playerData = 3;</code>
     */
    public Builder addPlayerData(xddq.pb.DragonHomeExplorePlayerDataMsg value) {
      if (playerDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePlayerDataIsMutable();
        playerData_.add(value);
        onChanged();
      } else {
        playerDataBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeExplorePlayerDataMsg playerData = 3;</code>
     */
    public Builder addPlayerData(
        int index, xddq.pb.DragonHomeExplorePlayerDataMsg value) {
      if (playerDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePlayerDataIsMutable();
        playerData_.add(index, value);
        onChanged();
      } else {
        playerDataBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeExplorePlayerDataMsg playerData = 3;</code>
     */
    public Builder addPlayerData(
        xddq.pb.DragonHomeExplorePlayerDataMsg.Builder builderForValue) {
      if (playerDataBuilder_ == null) {
        ensurePlayerDataIsMutable();
        playerData_.add(builderForValue.build());
        onChanged();
      } else {
        playerDataBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeExplorePlayerDataMsg playerData = 3;</code>
     */
    public Builder addPlayerData(
        int index, xddq.pb.DragonHomeExplorePlayerDataMsg.Builder builderForValue) {
      if (playerDataBuilder_ == null) {
        ensurePlayerDataIsMutable();
        playerData_.add(index, builderForValue.build());
        onChanged();
      } else {
        playerDataBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeExplorePlayerDataMsg playerData = 3;</code>
     */
    public Builder addAllPlayerData(
        java.lang.Iterable<? extends xddq.pb.DragonHomeExplorePlayerDataMsg> values) {
      if (playerDataBuilder_ == null) {
        ensurePlayerDataIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, playerData_);
        onChanged();
      } else {
        playerDataBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeExplorePlayerDataMsg playerData = 3;</code>
     */
    public Builder clearPlayerData() {
      if (playerDataBuilder_ == null) {
        playerData_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
      } else {
        playerDataBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeExplorePlayerDataMsg playerData = 3;</code>
     */
    public Builder removePlayerData(int index) {
      if (playerDataBuilder_ == null) {
        ensurePlayerDataIsMutable();
        playerData_.remove(index);
        onChanged();
      } else {
        playerDataBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeExplorePlayerDataMsg playerData = 3;</code>
     */
    public xddq.pb.DragonHomeExplorePlayerDataMsg.Builder getPlayerDataBuilder(
        int index) {
      return internalGetPlayerDataFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeExplorePlayerDataMsg playerData = 3;</code>
     */
    public xddq.pb.DragonHomeExplorePlayerDataMsgOrBuilder getPlayerDataOrBuilder(
        int index) {
      if (playerDataBuilder_ == null) {
        return playerData_.get(index);  } else {
        return playerDataBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeExplorePlayerDataMsg playerData = 3;</code>
     */
    public java.util.List<? extends xddq.pb.DragonHomeExplorePlayerDataMsgOrBuilder> 
         getPlayerDataOrBuilderList() {
      if (playerDataBuilder_ != null) {
        return playerDataBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(playerData_);
      }
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeExplorePlayerDataMsg playerData = 3;</code>
     */
    public xddq.pb.DragonHomeExplorePlayerDataMsg.Builder addPlayerDataBuilder() {
      return internalGetPlayerDataFieldBuilder().addBuilder(
          xddq.pb.DragonHomeExplorePlayerDataMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeExplorePlayerDataMsg playerData = 3;</code>
     */
    public xddq.pb.DragonHomeExplorePlayerDataMsg.Builder addPlayerDataBuilder(
        int index) {
      return internalGetPlayerDataFieldBuilder().addBuilder(
          index, xddq.pb.DragonHomeExplorePlayerDataMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeExplorePlayerDataMsg playerData = 3;</code>
     */
    public java.util.List<xddq.pb.DragonHomeExplorePlayerDataMsg.Builder> 
         getPlayerDataBuilderList() {
      return internalGetPlayerDataFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.DragonHomeExplorePlayerDataMsg, xddq.pb.DragonHomeExplorePlayerDataMsg.Builder, xddq.pb.DragonHomeExplorePlayerDataMsgOrBuilder> 
        internalGetPlayerDataFieldBuilder() {
      if (playerDataBuilder_ == null) {
        playerDataBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.DragonHomeExplorePlayerDataMsg, xddq.pb.DragonHomeExplorePlayerDataMsg.Builder, xddq.pb.DragonHomeExplorePlayerDataMsgOrBuilder>(
                playerData_,
                ((bitField0_ & 0x00000004) != 0),
                getParentForChildren(),
                isClean());
        playerData_ = null;
      }
      return playerDataBuilder_;
    }

    private int rank_ ;
    /**
     * <code>optional int32 rank = 4;</code>
     * @return Whether the rank field is set.
     */
    @java.lang.Override
    public boolean hasRank() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int32 rank = 4;</code>
     * @return The rank.
     */
    @java.lang.Override
    public int getRank() {
      return rank_;
    }
    /**
     * <code>optional int32 rank = 4;</code>
     * @param value The rank to set.
     * @return This builder for chaining.
     */
    public Builder setRank(int value) {

      rank_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 rank = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearRank() {
      bitField0_ = (bitField0_ & ~0x00000008);
      rank_ = 0;
      onChanged();
      return this;
    }

    private int keyCount_ ;
    /**
     * <code>optional int32 keyCount = 5;</code>
     * @return Whether the keyCount field is set.
     */
    @java.lang.Override
    public boolean hasKeyCount() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional int32 keyCount = 5;</code>
     * @return The keyCount.
     */
    @java.lang.Override
    public int getKeyCount() {
      return keyCount_;
    }
    /**
     * <code>optional int32 keyCount = 5;</code>
     * @param value The keyCount to set.
     * @return This builder for chaining.
     */
    public Builder setKeyCount(int value) {

      keyCount_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 keyCount = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearKeyCount() {
      bitField0_ = (bitField0_ & ~0x00000010);
      keyCount_ = 0;
      onChanged();
      return this;
    }

    private boolean isAutoUnLock_ ;
    /**
     * <code>optional bool isAutoUnLock = 7;</code>
     * @return Whether the isAutoUnLock field is set.
     */
    @java.lang.Override
    public boolean hasIsAutoUnLock() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional bool isAutoUnLock = 7;</code>
     * @return The isAutoUnLock.
     */
    @java.lang.Override
    public boolean getIsAutoUnLock() {
      return isAutoUnLock_;
    }
    /**
     * <code>optional bool isAutoUnLock = 7;</code>
     * @param value The isAutoUnLock to set.
     * @return This builder for chaining.
     */
    public Builder setIsAutoUnLock(boolean value) {

      isAutoUnLock_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>optional bool isAutoUnLock = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearIsAutoUnLock() {
      bitField0_ = (bitField0_ & ~0x00000020);
      isAutoUnLock_ = false;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.DragonHomePositionMsg> exitPosition_ =
      java.util.Collections.emptyList();
    private void ensureExitPositionIsMutable() {
      if (!((bitField0_ & 0x00000040) != 0)) {
        exitPosition_ = new java.util.ArrayList<xddq.pb.DragonHomePositionMsg>(exitPosition_);
        bitField0_ |= 0x00000040;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.DragonHomePositionMsg, xddq.pb.DragonHomePositionMsg.Builder, xddq.pb.DragonHomePositionMsgOrBuilder> exitPositionBuilder_;

    /**
     * <code>repeated .xddq.pb.DragonHomePositionMsg exitPosition = 8;</code>
     */
    public java.util.List<xddq.pb.DragonHomePositionMsg> getExitPositionList() {
      if (exitPositionBuilder_ == null) {
        return java.util.Collections.unmodifiableList(exitPosition_);
      } else {
        return exitPositionBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.DragonHomePositionMsg exitPosition = 8;</code>
     */
    public int getExitPositionCount() {
      if (exitPositionBuilder_ == null) {
        return exitPosition_.size();
      } else {
        return exitPositionBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.DragonHomePositionMsg exitPosition = 8;</code>
     */
    public xddq.pb.DragonHomePositionMsg getExitPosition(int index) {
      if (exitPositionBuilder_ == null) {
        return exitPosition_.get(index);
      } else {
        return exitPositionBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.DragonHomePositionMsg exitPosition = 8;</code>
     */
    public Builder setExitPosition(
        int index, xddq.pb.DragonHomePositionMsg value) {
      if (exitPositionBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureExitPositionIsMutable();
        exitPosition_.set(index, value);
        onChanged();
      } else {
        exitPositionBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomePositionMsg exitPosition = 8;</code>
     */
    public Builder setExitPosition(
        int index, xddq.pb.DragonHomePositionMsg.Builder builderForValue) {
      if (exitPositionBuilder_ == null) {
        ensureExitPositionIsMutable();
        exitPosition_.set(index, builderForValue.build());
        onChanged();
      } else {
        exitPositionBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomePositionMsg exitPosition = 8;</code>
     */
    public Builder addExitPosition(xddq.pb.DragonHomePositionMsg value) {
      if (exitPositionBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureExitPositionIsMutable();
        exitPosition_.add(value);
        onChanged();
      } else {
        exitPositionBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomePositionMsg exitPosition = 8;</code>
     */
    public Builder addExitPosition(
        int index, xddq.pb.DragonHomePositionMsg value) {
      if (exitPositionBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureExitPositionIsMutable();
        exitPosition_.add(index, value);
        onChanged();
      } else {
        exitPositionBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomePositionMsg exitPosition = 8;</code>
     */
    public Builder addExitPosition(
        xddq.pb.DragonHomePositionMsg.Builder builderForValue) {
      if (exitPositionBuilder_ == null) {
        ensureExitPositionIsMutable();
        exitPosition_.add(builderForValue.build());
        onChanged();
      } else {
        exitPositionBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomePositionMsg exitPosition = 8;</code>
     */
    public Builder addExitPosition(
        int index, xddq.pb.DragonHomePositionMsg.Builder builderForValue) {
      if (exitPositionBuilder_ == null) {
        ensureExitPositionIsMutable();
        exitPosition_.add(index, builderForValue.build());
        onChanged();
      } else {
        exitPositionBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomePositionMsg exitPosition = 8;</code>
     */
    public Builder addAllExitPosition(
        java.lang.Iterable<? extends xddq.pb.DragonHomePositionMsg> values) {
      if (exitPositionBuilder_ == null) {
        ensureExitPositionIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, exitPosition_);
        onChanged();
      } else {
        exitPositionBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomePositionMsg exitPosition = 8;</code>
     */
    public Builder clearExitPosition() {
      if (exitPositionBuilder_ == null) {
        exitPosition_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000040);
        onChanged();
      } else {
        exitPositionBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomePositionMsg exitPosition = 8;</code>
     */
    public Builder removeExitPosition(int index) {
      if (exitPositionBuilder_ == null) {
        ensureExitPositionIsMutable();
        exitPosition_.remove(index);
        onChanged();
      } else {
        exitPositionBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomePositionMsg exitPosition = 8;</code>
     */
    public xddq.pb.DragonHomePositionMsg.Builder getExitPositionBuilder(
        int index) {
      return internalGetExitPositionFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.DragonHomePositionMsg exitPosition = 8;</code>
     */
    public xddq.pb.DragonHomePositionMsgOrBuilder getExitPositionOrBuilder(
        int index) {
      if (exitPositionBuilder_ == null) {
        return exitPosition_.get(index);  } else {
        return exitPositionBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.DragonHomePositionMsg exitPosition = 8;</code>
     */
    public java.util.List<? extends xddq.pb.DragonHomePositionMsgOrBuilder> 
         getExitPositionOrBuilderList() {
      if (exitPositionBuilder_ != null) {
        return exitPositionBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(exitPosition_);
      }
    }
    /**
     * <code>repeated .xddq.pb.DragonHomePositionMsg exitPosition = 8;</code>
     */
    public xddq.pb.DragonHomePositionMsg.Builder addExitPositionBuilder() {
      return internalGetExitPositionFieldBuilder().addBuilder(
          xddq.pb.DragonHomePositionMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.DragonHomePositionMsg exitPosition = 8;</code>
     */
    public xddq.pb.DragonHomePositionMsg.Builder addExitPositionBuilder(
        int index) {
      return internalGetExitPositionFieldBuilder().addBuilder(
          index, xddq.pb.DragonHomePositionMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.DragonHomePositionMsg exitPosition = 8;</code>
     */
    public java.util.List<xddq.pb.DragonHomePositionMsg.Builder> 
         getExitPositionBuilderList() {
      return internalGetExitPositionFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.DragonHomePositionMsg, xddq.pb.DragonHomePositionMsg.Builder, xddq.pb.DragonHomePositionMsgOrBuilder> 
        internalGetExitPositionFieldBuilder() {
      if (exitPositionBuilder_ == null) {
        exitPositionBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.DragonHomePositionMsg, xddq.pb.DragonHomePositionMsg.Builder, xddq.pb.DragonHomePositionMsgOrBuilder>(
                exitPosition_,
                ((bitField0_ & 0x00000040) != 0),
                getParentForChildren(),
                isClean());
        exitPosition_ = null;
      }
      return exitPositionBuilder_;
    }

    private java.util.List<xddq.pb.DragonHomeMonsterMsg> monsterInfo_ =
      java.util.Collections.emptyList();
    private void ensureMonsterInfoIsMutable() {
      if (!((bitField0_ & 0x00000080) != 0)) {
        monsterInfo_ = new java.util.ArrayList<xddq.pb.DragonHomeMonsterMsg>(monsterInfo_);
        bitField0_ |= 0x00000080;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.DragonHomeMonsterMsg, xddq.pb.DragonHomeMonsterMsg.Builder, xddq.pb.DragonHomeMonsterMsgOrBuilder> monsterInfoBuilder_;

    /**
     * <code>repeated .xddq.pb.DragonHomeMonsterMsg monsterInfo = 9;</code>
     */
    public java.util.List<xddq.pb.DragonHomeMonsterMsg> getMonsterInfoList() {
      if (monsterInfoBuilder_ == null) {
        return java.util.Collections.unmodifiableList(monsterInfo_);
      } else {
        return monsterInfoBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMonsterMsg monsterInfo = 9;</code>
     */
    public int getMonsterInfoCount() {
      if (monsterInfoBuilder_ == null) {
        return monsterInfo_.size();
      } else {
        return monsterInfoBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMonsterMsg monsterInfo = 9;</code>
     */
    public xddq.pb.DragonHomeMonsterMsg getMonsterInfo(int index) {
      if (monsterInfoBuilder_ == null) {
        return monsterInfo_.get(index);
      } else {
        return monsterInfoBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMonsterMsg monsterInfo = 9;</code>
     */
    public Builder setMonsterInfo(
        int index, xddq.pb.DragonHomeMonsterMsg value) {
      if (monsterInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMonsterInfoIsMutable();
        monsterInfo_.set(index, value);
        onChanged();
      } else {
        monsterInfoBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMonsterMsg monsterInfo = 9;</code>
     */
    public Builder setMonsterInfo(
        int index, xddq.pb.DragonHomeMonsterMsg.Builder builderForValue) {
      if (monsterInfoBuilder_ == null) {
        ensureMonsterInfoIsMutable();
        monsterInfo_.set(index, builderForValue.build());
        onChanged();
      } else {
        monsterInfoBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMonsterMsg monsterInfo = 9;</code>
     */
    public Builder addMonsterInfo(xddq.pb.DragonHomeMonsterMsg value) {
      if (monsterInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMonsterInfoIsMutable();
        monsterInfo_.add(value);
        onChanged();
      } else {
        monsterInfoBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMonsterMsg monsterInfo = 9;</code>
     */
    public Builder addMonsterInfo(
        int index, xddq.pb.DragonHomeMonsterMsg value) {
      if (monsterInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMonsterInfoIsMutable();
        monsterInfo_.add(index, value);
        onChanged();
      } else {
        monsterInfoBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMonsterMsg monsterInfo = 9;</code>
     */
    public Builder addMonsterInfo(
        xddq.pb.DragonHomeMonsterMsg.Builder builderForValue) {
      if (monsterInfoBuilder_ == null) {
        ensureMonsterInfoIsMutable();
        monsterInfo_.add(builderForValue.build());
        onChanged();
      } else {
        monsterInfoBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMonsterMsg monsterInfo = 9;</code>
     */
    public Builder addMonsterInfo(
        int index, xddq.pb.DragonHomeMonsterMsg.Builder builderForValue) {
      if (monsterInfoBuilder_ == null) {
        ensureMonsterInfoIsMutable();
        monsterInfo_.add(index, builderForValue.build());
        onChanged();
      } else {
        monsterInfoBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMonsterMsg monsterInfo = 9;</code>
     */
    public Builder addAllMonsterInfo(
        java.lang.Iterable<? extends xddq.pb.DragonHomeMonsterMsg> values) {
      if (monsterInfoBuilder_ == null) {
        ensureMonsterInfoIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, monsterInfo_);
        onChanged();
      } else {
        monsterInfoBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMonsterMsg monsterInfo = 9;</code>
     */
    public Builder clearMonsterInfo() {
      if (monsterInfoBuilder_ == null) {
        monsterInfo_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000080);
        onChanged();
      } else {
        monsterInfoBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMonsterMsg monsterInfo = 9;</code>
     */
    public Builder removeMonsterInfo(int index) {
      if (monsterInfoBuilder_ == null) {
        ensureMonsterInfoIsMutable();
        monsterInfo_.remove(index);
        onChanged();
      } else {
        monsterInfoBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMonsterMsg monsterInfo = 9;</code>
     */
    public xddq.pb.DragonHomeMonsterMsg.Builder getMonsterInfoBuilder(
        int index) {
      return internalGetMonsterInfoFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMonsterMsg monsterInfo = 9;</code>
     */
    public xddq.pb.DragonHomeMonsterMsgOrBuilder getMonsterInfoOrBuilder(
        int index) {
      if (monsterInfoBuilder_ == null) {
        return monsterInfo_.get(index);  } else {
        return monsterInfoBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMonsterMsg monsterInfo = 9;</code>
     */
    public java.util.List<? extends xddq.pb.DragonHomeMonsterMsgOrBuilder> 
         getMonsterInfoOrBuilderList() {
      if (monsterInfoBuilder_ != null) {
        return monsterInfoBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(monsterInfo_);
      }
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMonsterMsg monsterInfo = 9;</code>
     */
    public xddq.pb.DragonHomeMonsterMsg.Builder addMonsterInfoBuilder() {
      return internalGetMonsterInfoFieldBuilder().addBuilder(
          xddq.pb.DragonHomeMonsterMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMonsterMsg monsterInfo = 9;</code>
     */
    public xddq.pb.DragonHomeMonsterMsg.Builder addMonsterInfoBuilder(
        int index) {
      return internalGetMonsterInfoFieldBuilder().addBuilder(
          index, xddq.pb.DragonHomeMonsterMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMonsterMsg monsterInfo = 9;</code>
     */
    public java.util.List<xddq.pb.DragonHomeMonsterMsg.Builder> 
         getMonsterInfoBuilderList() {
      return internalGetMonsterInfoFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.DragonHomeMonsterMsg, xddq.pb.DragonHomeMonsterMsg.Builder, xddq.pb.DragonHomeMonsterMsgOrBuilder> 
        internalGetMonsterInfoFieldBuilder() {
      if (monsterInfoBuilder_ == null) {
        monsterInfoBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.DragonHomeMonsterMsg, xddq.pb.DragonHomeMonsterMsg.Builder, xddq.pb.DragonHomeMonsterMsgOrBuilder>(
                monsterInfo_,
                ((bitField0_ & 0x00000080) != 0),
                getParentForChildren(),
                isClean());
        monsterInfo_ = null;
      }
      return monsterInfoBuilder_;
    }

    private boolean haveKey1_ ;
    /**
     * <code>optional bool haveKey1 = 10;</code>
     * @return Whether the haveKey1 field is set.
     */
    @java.lang.Override
    public boolean hasHaveKey1() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <code>optional bool haveKey1 = 10;</code>
     * @return The haveKey1.
     */
    @java.lang.Override
    public boolean getHaveKey1() {
      return haveKey1_;
    }
    /**
     * <code>optional bool haveKey1 = 10;</code>
     * @param value The haveKey1 to set.
     * @return This builder for chaining.
     */
    public Builder setHaveKey1(boolean value) {

      haveKey1_ = value;
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }
    /**
     * <code>optional bool haveKey1 = 10;</code>
     * @return This builder for chaining.
     */
    public Builder clearHaveKey1() {
      bitField0_ = (bitField0_ & ~0x00000100);
      haveKey1_ = false;
      onChanged();
      return this;
    }

    private boolean haveKey2_ ;
    /**
     * <code>optional bool haveKey2 = 11;</code>
     * @return Whether the haveKey2 field is set.
     */
    @java.lang.Override
    public boolean hasHaveKey2() {
      return ((bitField0_ & 0x00000200) != 0);
    }
    /**
     * <code>optional bool haveKey2 = 11;</code>
     * @return The haveKey2.
     */
    @java.lang.Override
    public boolean getHaveKey2() {
      return haveKey2_;
    }
    /**
     * <code>optional bool haveKey2 = 11;</code>
     * @param value The haveKey2 to set.
     * @return This builder for chaining.
     */
    public Builder setHaveKey2(boolean value) {

      haveKey2_ = value;
      bitField0_ |= 0x00000200;
      onChanged();
      return this;
    }
    /**
     * <code>optional bool haveKey2 = 11;</code>
     * @return This builder for chaining.
     */
    public Builder clearHaveKey2() {
      bitField0_ = (bitField0_ & ~0x00000200);
      haveKey2_ = false;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.DragonHomeMallInfo> mallInfo_ =
      java.util.Collections.emptyList();
    private void ensureMallInfoIsMutable() {
      if (!((bitField0_ & 0x00000400) != 0)) {
        mallInfo_ = new java.util.ArrayList<xddq.pb.DragonHomeMallInfo>(mallInfo_);
        bitField0_ |= 0x00000400;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.DragonHomeMallInfo, xddq.pb.DragonHomeMallInfo.Builder, xddq.pb.DragonHomeMallInfoOrBuilder> mallInfoBuilder_;

    /**
     * <code>repeated .xddq.pb.DragonHomeMallInfo mallInfo = 12;</code>
     */
    public java.util.List<xddq.pb.DragonHomeMallInfo> getMallInfoList() {
      if (mallInfoBuilder_ == null) {
        return java.util.Collections.unmodifiableList(mallInfo_);
      } else {
        return mallInfoBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMallInfo mallInfo = 12;</code>
     */
    public int getMallInfoCount() {
      if (mallInfoBuilder_ == null) {
        return mallInfo_.size();
      } else {
        return mallInfoBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMallInfo mallInfo = 12;</code>
     */
    public xddq.pb.DragonHomeMallInfo getMallInfo(int index) {
      if (mallInfoBuilder_ == null) {
        return mallInfo_.get(index);
      } else {
        return mallInfoBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMallInfo mallInfo = 12;</code>
     */
    public Builder setMallInfo(
        int index, xddq.pb.DragonHomeMallInfo value) {
      if (mallInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMallInfoIsMutable();
        mallInfo_.set(index, value);
        onChanged();
      } else {
        mallInfoBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMallInfo mallInfo = 12;</code>
     */
    public Builder setMallInfo(
        int index, xddq.pb.DragonHomeMallInfo.Builder builderForValue) {
      if (mallInfoBuilder_ == null) {
        ensureMallInfoIsMutable();
        mallInfo_.set(index, builderForValue.build());
        onChanged();
      } else {
        mallInfoBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMallInfo mallInfo = 12;</code>
     */
    public Builder addMallInfo(xddq.pb.DragonHomeMallInfo value) {
      if (mallInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMallInfoIsMutable();
        mallInfo_.add(value);
        onChanged();
      } else {
        mallInfoBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMallInfo mallInfo = 12;</code>
     */
    public Builder addMallInfo(
        int index, xddq.pb.DragonHomeMallInfo value) {
      if (mallInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMallInfoIsMutable();
        mallInfo_.add(index, value);
        onChanged();
      } else {
        mallInfoBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMallInfo mallInfo = 12;</code>
     */
    public Builder addMallInfo(
        xddq.pb.DragonHomeMallInfo.Builder builderForValue) {
      if (mallInfoBuilder_ == null) {
        ensureMallInfoIsMutable();
        mallInfo_.add(builderForValue.build());
        onChanged();
      } else {
        mallInfoBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMallInfo mallInfo = 12;</code>
     */
    public Builder addMallInfo(
        int index, xddq.pb.DragonHomeMallInfo.Builder builderForValue) {
      if (mallInfoBuilder_ == null) {
        ensureMallInfoIsMutable();
        mallInfo_.add(index, builderForValue.build());
        onChanged();
      } else {
        mallInfoBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMallInfo mallInfo = 12;</code>
     */
    public Builder addAllMallInfo(
        java.lang.Iterable<? extends xddq.pb.DragonHomeMallInfo> values) {
      if (mallInfoBuilder_ == null) {
        ensureMallInfoIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, mallInfo_);
        onChanged();
      } else {
        mallInfoBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMallInfo mallInfo = 12;</code>
     */
    public Builder clearMallInfo() {
      if (mallInfoBuilder_ == null) {
        mallInfo_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000400);
        onChanged();
      } else {
        mallInfoBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMallInfo mallInfo = 12;</code>
     */
    public Builder removeMallInfo(int index) {
      if (mallInfoBuilder_ == null) {
        ensureMallInfoIsMutable();
        mallInfo_.remove(index);
        onChanged();
      } else {
        mallInfoBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMallInfo mallInfo = 12;</code>
     */
    public xddq.pb.DragonHomeMallInfo.Builder getMallInfoBuilder(
        int index) {
      return internalGetMallInfoFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMallInfo mallInfo = 12;</code>
     */
    public xddq.pb.DragonHomeMallInfoOrBuilder getMallInfoOrBuilder(
        int index) {
      if (mallInfoBuilder_ == null) {
        return mallInfo_.get(index);  } else {
        return mallInfoBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMallInfo mallInfo = 12;</code>
     */
    public java.util.List<? extends xddq.pb.DragonHomeMallInfoOrBuilder> 
         getMallInfoOrBuilderList() {
      if (mallInfoBuilder_ != null) {
        return mallInfoBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(mallInfo_);
      }
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMallInfo mallInfo = 12;</code>
     */
    public xddq.pb.DragonHomeMallInfo.Builder addMallInfoBuilder() {
      return internalGetMallInfoFieldBuilder().addBuilder(
          xddq.pb.DragonHomeMallInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMallInfo mallInfo = 12;</code>
     */
    public xddq.pb.DragonHomeMallInfo.Builder addMallInfoBuilder(
        int index) {
      return internalGetMallInfoFieldBuilder().addBuilder(
          index, xddq.pb.DragonHomeMallInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.DragonHomeMallInfo mallInfo = 12;</code>
     */
    public java.util.List<xddq.pb.DragonHomeMallInfo.Builder> 
         getMallInfoBuilderList() {
      return internalGetMallInfoFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.DragonHomeMallInfo, xddq.pb.DragonHomeMallInfo.Builder, xddq.pb.DragonHomeMallInfoOrBuilder> 
        internalGetMallInfoFieldBuilder() {
      if (mallInfoBuilder_ == null) {
        mallInfoBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.DragonHomeMallInfo, xddq.pb.DragonHomeMallInfo.Builder, xddq.pb.DragonHomeMallInfoOrBuilder>(
                mallInfo_,
                ((bitField0_ & 0x00000400) != 0),
                getParentForChildren(),
                isClean());
        mallInfo_ = null;
      }
      return mallInfoBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.DragonHomeExploreDataMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.DragonHomeExploreDataMsg)
  private static final xddq.pb.DragonHomeExploreDataMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.DragonHomeExploreDataMsg();
  }

  public static xddq.pb.DragonHomeExploreDataMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<DragonHomeExploreDataMsg>
      PARSER = new com.google.protobuf.AbstractParser<DragonHomeExploreDataMsg>() {
    @java.lang.Override
    public DragonHomeExploreDataMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<DragonHomeExploreDataMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<DragonHomeExploreDataMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.DragonHomeExploreDataMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

