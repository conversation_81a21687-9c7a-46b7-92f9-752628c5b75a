// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.HeavenCampRankMsgList}
 */
public final class HeavenCampRankMsgList extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.HeavenCampRankMsgList)
    HeavenCampRankMsgListOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      HeavenCampRankMsgList.class.getName());
  }
  // Use HeavenCampRankMsgList.newBuilder() to construct.
  private HeavenCampRankMsgList(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private HeavenCampRankMsgList() {
    campList_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_HeavenCampRankMsgList_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_HeavenCampRankMsgList_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.HeavenCampRankMsgList.class, xddq.pb.HeavenCampRankMsgList.Builder.class);
  }

  public static final int CAMPLIST_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.HeavenBattleCampRankMsg> campList_;
  /**
   * <code>repeated .xddq.pb.HeavenBattleCampRankMsg campList = 1;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.HeavenBattleCampRankMsg> getCampListList() {
    return campList_;
  }
  /**
   * <code>repeated .xddq.pb.HeavenBattleCampRankMsg campList = 1;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.HeavenBattleCampRankMsgOrBuilder> 
      getCampListOrBuilderList() {
    return campList_;
  }
  /**
   * <code>repeated .xddq.pb.HeavenBattleCampRankMsg campList = 1;</code>
   */
  @java.lang.Override
  public int getCampListCount() {
    return campList_.size();
  }
  /**
   * <code>repeated .xddq.pb.HeavenBattleCampRankMsg campList = 1;</code>
   */
  @java.lang.Override
  public xddq.pb.HeavenBattleCampRankMsg getCampList(int index) {
    return campList_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.HeavenBattleCampRankMsg campList = 1;</code>
   */
  @java.lang.Override
  public xddq.pb.HeavenBattleCampRankMsgOrBuilder getCampListOrBuilder(
      int index) {
    return campList_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    for (int i = 0; i < campList_.size(); i++) {
      output.writeMessage(1, campList_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    for (int i = 0; i < campList_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, campList_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.HeavenCampRankMsgList)) {
      return super.equals(obj);
    }
    xddq.pb.HeavenCampRankMsgList other = (xddq.pb.HeavenCampRankMsgList) obj;

    if (!getCampListList()
        .equals(other.getCampListList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (getCampListCount() > 0) {
      hash = (37 * hash) + CAMPLIST_FIELD_NUMBER;
      hash = (53 * hash) + getCampListList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.HeavenCampRankMsgList parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.HeavenCampRankMsgList parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.HeavenCampRankMsgList parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.HeavenCampRankMsgList parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.HeavenCampRankMsgList parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.HeavenCampRankMsgList parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.HeavenCampRankMsgList parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.HeavenCampRankMsgList parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.HeavenCampRankMsgList parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.HeavenCampRankMsgList parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.HeavenCampRankMsgList parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.HeavenCampRankMsgList parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.HeavenCampRankMsgList prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.HeavenCampRankMsgList}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.HeavenCampRankMsgList)
      xddq.pb.HeavenCampRankMsgListOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_HeavenCampRankMsgList_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_HeavenCampRankMsgList_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.HeavenCampRankMsgList.class, xddq.pb.HeavenCampRankMsgList.Builder.class);
    }

    // Construct using xddq.pb.HeavenCampRankMsgList.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      if (campListBuilder_ == null) {
        campList_ = java.util.Collections.emptyList();
      } else {
        campList_ = null;
        campListBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000001);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_HeavenCampRankMsgList_descriptor;
    }

    @java.lang.Override
    public xddq.pb.HeavenCampRankMsgList getDefaultInstanceForType() {
      return xddq.pb.HeavenCampRankMsgList.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.HeavenCampRankMsgList build() {
      xddq.pb.HeavenCampRankMsgList result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.HeavenCampRankMsgList buildPartial() {
      xddq.pb.HeavenCampRankMsgList result = new xddq.pb.HeavenCampRankMsgList(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.HeavenCampRankMsgList result) {
      if (campListBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          campList_ = java.util.Collections.unmodifiableList(campList_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.campList_ = campList_;
      } else {
        result.campList_ = campListBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.HeavenCampRankMsgList result) {
      int from_bitField0_ = bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.HeavenCampRankMsgList) {
        return mergeFrom((xddq.pb.HeavenCampRankMsgList)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.HeavenCampRankMsgList other) {
      if (other == xddq.pb.HeavenCampRankMsgList.getDefaultInstance()) return this;
      if (campListBuilder_ == null) {
        if (!other.campList_.isEmpty()) {
          if (campList_.isEmpty()) {
            campList_ = other.campList_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureCampListIsMutable();
            campList_.addAll(other.campList_);
          }
          onChanged();
        }
      } else {
        if (!other.campList_.isEmpty()) {
          if (campListBuilder_.isEmpty()) {
            campListBuilder_.dispose();
            campListBuilder_ = null;
            campList_ = other.campList_;
            bitField0_ = (bitField0_ & ~0x00000001);
            campListBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetCampListFieldBuilder() : null;
          } else {
            campListBuilder_.addAllMessages(other.campList_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              xddq.pb.HeavenBattleCampRankMsg m =
                  input.readMessage(
                      xddq.pb.HeavenBattleCampRankMsg.parser(),
                      extensionRegistry);
              if (campListBuilder_ == null) {
                ensureCampListIsMutable();
                campList_.add(m);
              } else {
                campListBuilder_.addMessage(m);
              }
              break;
            } // case 10
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.util.List<xddq.pb.HeavenBattleCampRankMsg> campList_ =
      java.util.Collections.emptyList();
    private void ensureCampListIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        campList_ = new java.util.ArrayList<xddq.pb.HeavenBattleCampRankMsg>(campList_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.HeavenBattleCampRankMsg, xddq.pb.HeavenBattleCampRankMsg.Builder, xddq.pb.HeavenBattleCampRankMsgOrBuilder> campListBuilder_;

    /**
     * <code>repeated .xddq.pb.HeavenBattleCampRankMsg campList = 1;</code>
     */
    public java.util.List<xddq.pb.HeavenBattleCampRankMsg> getCampListList() {
      if (campListBuilder_ == null) {
        return java.util.Collections.unmodifiableList(campList_);
      } else {
        return campListBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleCampRankMsg campList = 1;</code>
     */
    public int getCampListCount() {
      if (campListBuilder_ == null) {
        return campList_.size();
      } else {
        return campListBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleCampRankMsg campList = 1;</code>
     */
    public xddq.pb.HeavenBattleCampRankMsg getCampList(int index) {
      if (campListBuilder_ == null) {
        return campList_.get(index);
      } else {
        return campListBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleCampRankMsg campList = 1;</code>
     */
    public Builder setCampList(
        int index, xddq.pb.HeavenBattleCampRankMsg value) {
      if (campListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureCampListIsMutable();
        campList_.set(index, value);
        onChanged();
      } else {
        campListBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleCampRankMsg campList = 1;</code>
     */
    public Builder setCampList(
        int index, xddq.pb.HeavenBattleCampRankMsg.Builder builderForValue) {
      if (campListBuilder_ == null) {
        ensureCampListIsMutable();
        campList_.set(index, builderForValue.build());
        onChanged();
      } else {
        campListBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleCampRankMsg campList = 1;</code>
     */
    public Builder addCampList(xddq.pb.HeavenBattleCampRankMsg value) {
      if (campListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureCampListIsMutable();
        campList_.add(value);
        onChanged();
      } else {
        campListBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleCampRankMsg campList = 1;</code>
     */
    public Builder addCampList(
        int index, xddq.pb.HeavenBattleCampRankMsg value) {
      if (campListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureCampListIsMutable();
        campList_.add(index, value);
        onChanged();
      } else {
        campListBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleCampRankMsg campList = 1;</code>
     */
    public Builder addCampList(
        xddq.pb.HeavenBattleCampRankMsg.Builder builderForValue) {
      if (campListBuilder_ == null) {
        ensureCampListIsMutable();
        campList_.add(builderForValue.build());
        onChanged();
      } else {
        campListBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleCampRankMsg campList = 1;</code>
     */
    public Builder addCampList(
        int index, xddq.pb.HeavenBattleCampRankMsg.Builder builderForValue) {
      if (campListBuilder_ == null) {
        ensureCampListIsMutable();
        campList_.add(index, builderForValue.build());
        onChanged();
      } else {
        campListBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleCampRankMsg campList = 1;</code>
     */
    public Builder addAllCampList(
        java.lang.Iterable<? extends xddq.pb.HeavenBattleCampRankMsg> values) {
      if (campListBuilder_ == null) {
        ensureCampListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, campList_);
        onChanged();
      } else {
        campListBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleCampRankMsg campList = 1;</code>
     */
    public Builder clearCampList() {
      if (campListBuilder_ == null) {
        campList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        campListBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleCampRankMsg campList = 1;</code>
     */
    public Builder removeCampList(int index) {
      if (campListBuilder_ == null) {
        ensureCampListIsMutable();
        campList_.remove(index);
        onChanged();
      } else {
        campListBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleCampRankMsg campList = 1;</code>
     */
    public xddq.pb.HeavenBattleCampRankMsg.Builder getCampListBuilder(
        int index) {
      return internalGetCampListFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleCampRankMsg campList = 1;</code>
     */
    public xddq.pb.HeavenBattleCampRankMsgOrBuilder getCampListOrBuilder(
        int index) {
      if (campListBuilder_ == null) {
        return campList_.get(index);  } else {
        return campListBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleCampRankMsg campList = 1;</code>
     */
    public java.util.List<? extends xddq.pb.HeavenBattleCampRankMsgOrBuilder> 
         getCampListOrBuilderList() {
      if (campListBuilder_ != null) {
        return campListBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(campList_);
      }
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleCampRankMsg campList = 1;</code>
     */
    public xddq.pb.HeavenBattleCampRankMsg.Builder addCampListBuilder() {
      return internalGetCampListFieldBuilder().addBuilder(
          xddq.pb.HeavenBattleCampRankMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleCampRankMsg campList = 1;</code>
     */
    public xddq.pb.HeavenBattleCampRankMsg.Builder addCampListBuilder(
        int index) {
      return internalGetCampListFieldBuilder().addBuilder(
          index, xddq.pb.HeavenBattleCampRankMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleCampRankMsg campList = 1;</code>
     */
    public java.util.List<xddq.pb.HeavenBattleCampRankMsg.Builder> 
         getCampListBuilderList() {
      return internalGetCampListFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.HeavenBattleCampRankMsg, xddq.pb.HeavenBattleCampRankMsg.Builder, xddq.pb.HeavenBattleCampRankMsgOrBuilder> 
        internalGetCampListFieldBuilder() {
      if (campListBuilder_ == null) {
        campListBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.HeavenBattleCampRankMsg, xddq.pb.HeavenBattleCampRankMsg.Builder, xddq.pb.HeavenBattleCampRankMsgOrBuilder>(
                campList_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        campList_ = null;
      }
      return campListBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.HeavenCampRankMsgList)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.HeavenCampRankMsgList)
  private static final xddq.pb.HeavenCampRankMsgList DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.HeavenCampRankMsgList();
  }

  public static xddq.pb.HeavenCampRankMsgList getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<HeavenCampRankMsgList>
      PARSER = new com.google.protobuf.AbstractParser<HeavenCampRankMsgList>() {
    @java.lang.Override
    public HeavenCampRankMsgList parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<HeavenCampRankMsgList> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<HeavenCampRankMsgList> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.HeavenCampRankMsgList getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

