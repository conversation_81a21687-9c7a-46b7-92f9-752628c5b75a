// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

public interface BossChallengeBattleRespOrBuilder extends
    // @@protoc_insertion_point(interface_extends:xddq.pb.BossChallengeBattleResp)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  boolean hasRet();
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  int getRet();

  /**
   * <code>optional .xddq.pb.PupilRogueBattleRecordMsg battle = 2;</code>
   * @return Whether the battle field is set.
   */
  boolean hasBattle();
  /**
   * <code>optional .xddq.pb.PupilRogueBattleRecordMsg battle = 2;</code>
   * @return The battle.
   */
  xddq.pb.PupilRogueBattleRecordMsg getBattle();
  /**
   * <code>optional .xddq.pb.PupilRogueBattleRecordMsg battle = 2;</code>
   */
  xddq.pb.PupilRogueBattleRecordMsgOrBuilder getBattleOrBuilder();

  /**
   * <code>optional int64 totalDamage = 3;</code>
   * @return Whether the totalDamage field is set.
   */
  boolean hasTotalDamage();
  /**
   * <code>optional int64 totalDamage = 3;</code>
   * @return The totalDamage.
   */
  long getTotalDamage();

  /**
   * <code>optional .xddq.pb.PlayerExploreMsg data = 4;</code>
   * @return Whether the data field is set.
   */
  boolean hasData();
  /**
   * <code>optional .xddq.pb.PlayerExploreMsg data = 4;</code>
   * @return The data.
   */
  xddq.pb.PlayerExploreMsg getData();
  /**
   * <code>optional .xddq.pb.PlayerExploreMsg data = 4;</code>
   */
  xddq.pb.PlayerExploreMsgOrBuilder getDataOrBuilder();

  /**
   * <code>optional int32 rank = 5;</code>
   * @return Whether the rank field is set.
   */
  boolean hasRank();
  /**
   * <code>optional int32 rank = 5;</code>
   * @return The rank.
   */
  int getRank();

  /**
   * <code>optional int64 maxDamage = 6;</code>
   * @return Whether the maxDamage field is set.
   */
  boolean hasMaxDamage();
  /**
   * <code>optional int64 maxDamage = 6;</code>
   * @return The maxDamage.
   */
  long getMaxDamage();
}
