// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.CastSwordSettlementSwordResp}
 */
public final class CastSwordSettlementSwordResp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.CastSwordSettlementSwordResp)
    CastSwordSettlementSwordRespOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      CastSwordSettlementSwordResp.class.getName());
  }
  // Use CastSwordSettlementSwordResp.newBuilder() to construct.
  private CastSwordSettlementSwordResp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private CastSwordSettlementSwordResp() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_CastSwordSettlementSwordResp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_CastSwordSettlementSwordResp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.CastSwordSettlementSwordResp.class, xddq.pb.CastSwordSettlementSwordResp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int SCORE_FIELD_NUMBER = 2;
  private int score_ = 0;
  /**
   * <code>optional int32 score = 2;</code>
   * @return Whether the score field is set.
   */
  @java.lang.Override
  public boolean hasScore() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int32 score = 2;</code>
   * @return The score.
   */
  @java.lang.Override
  public int getScore() {
    return score_;
  }

  public static final int ENTITY_FIELD_NUMBER = 3;
  private xddq.pb.CastSwordRespBaseDataEntity entity_;
  /**
   * <code>optional .xddq.pb.CastSwordRespBaseDataEntity entity = 3;</code>
   * @return Whether the entity field is set.
   */
  @java.lang.Override
  public boolean hasEntity() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional .xddq.pb.CastSwordRespBaseDataEntity entity = 3;</code>
   * @return The entity.
   */
  @java.lang.Override
  public xddq.pb.CastSwordRespBaseDataEntity getEntity() {
    return entity_ == null ? xddq.pb.CastSwordRespBaseDataEntity.getDefaultInstance() : entity_;
  }
  /**
   * <code>optional .xddq.pb.CastSwordRespBaseDataEntity entity = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.CastSwordRespBaseDataEntityOrBuilder getEntityOrBuilder() {
    return entity_ == null ? xddq.pb.CastSwordRespBaseDataEntity.getDefaultInstance() : entity_;
  }

  public static final int RANK_FIELD_NUMBER = 4;
  private int rank_ = 0;
  /**
   * <code>optional int32 rank = 4;</code>
   * @return Whether the rank field is set.
   */
  @java.lang.Override
  public boolean hasRank() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional int32 rank = 4;</code>
   * @return The rank.
   */
  @java.lang.Override
  public int getRank() {
    return rank_;
  }

  public static final int NEXTRANKSCORE_FIELD_NUMBER = 5;
  private int nextRankScore_ = 0;
  /**
   * <code>optional int32 nextRankScore = 5;</code>
   * @return Whether the nextRankScore field is set.
   */
  @java.lang.Override
  public boolean hasNextRankScore() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>optional int32 nextRankScore = 5;</code>
   * @return The nextRankScore.
   */
  @java.lang.Override
  public int getNextRankScore() {
    return nextRankScore_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (hasEntity()) {
      if (!getEntity().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(2, score_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeMessage(3, getEntity());
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt32(4, rank_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      output.writeInt32(5, nextRankScore_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, score_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, getEntity());
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(4, rank_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(5, nextRankScore_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.CastSwordSettlementSwordResp)) {
      return super.equals(obj);
    }
    xddq.pb.CastSwordSettlementSwordResp other = (xddq.pb.CastSwordSettlementSwordResp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (hasScore() != other.hasScore()) return false;
    if (hasScore()) {
      if (getScore()
          != other.getScore()) return false;
    }
    if (hasEntity() != other.hasEntity()) return false;
    if (hasEntity()) {
      if (!getEntity()
          .equals(other.getEntity())) return false;
    }
    if (hasRank() != other.hasRank()) return false;
    if (hasRank()) {
      if (getRank()
          != other.getRank()) return false;
    }
    if (hasNextRankScore() != other.hasNextRankScore()) return false;
    if (hasNextRankScore()) {
      if (getNextRankScore()
          != other.getNextRankScore()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (hasScore()) {
      hash = (37 * hash) + SCORE_FIELD_NUMBER;
      hash = (53 * hash) + getScore();
    }
    if (hasEntity()) {
      hash = (37 * hash) + ENTITY_FIELD_NUMBER;
      hash = (53 * hash) + getEntity().hashCode();
    }
    if (hasRank()) {
      hash = (37 * hash) + RANK_FIELD_NUMBER;
      hash = (53 * hash) + getRank();
    }
    if (hasNextRankScore()) {
      hash = (37 * hash) + NEXTRANKSCORE_FIELD_NUMBER;
      hash = (53 * hash) + getNextRankScore();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.CastSwordSettlementSwordResp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.CastSwordSettlementSwordResp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.CastSwordSettlementSwordResp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.CastSwordSettlementSwordResp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.CastSwordSettlementSwordResp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.CastSwordSettlementSwordResp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.CastSwordSettlementSwordResp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.CastSwordSettlementSwordResp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.CastSwordSettlementSwordResp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.CastSwordSettlementSwordResp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.CastSwordSettlementSwordResp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.CastSwordSettlementSwordResp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.CastSwordSettlementSwordResp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.CastSwordSettlementSwordResp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.CastSwordSettlementSwordResp)
      xddq.pb.CastSwordSettlementSwordRespOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_CastSwordSettlementSwordResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_CastSwordSettlementSwordResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.CastSwordSettlementSwordResp.class, xddq.pb.CastSwordSettlementSwordResp.Builder.class);
    }

    // Construct using xddq.pb.CastSwordSettlementSwordResp.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetEntityFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      score_ = 0;
      entity_ = null;
      if (entityBuilder_ != null) {
        entityBuilder_.dispose();
        entityBuilder_ = null;
      }
      rank_ = 0;
      nextRankScore_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_CastSwordSettlementSwordResp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.CastSwordSettlementSwordResp getDefaultInstanceForType() {
      return xddq.pb.CastSwordSettlementSwordResp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.CastSwordSettlementSwordResp build() {
      xddq.pb.CastSwordSettlementSwordResp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.CastSwordSettlementSwordResp buildPartial() {
      xddq.pb.CastSwordSettlementSwordResp result = new xddq.pb.CastSwordSettlementSwordResp(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.CastSwordSettlementSwordResp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.score_ = score_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.entity_ = entityBuilder_ == null
            ? entity_
            : entityBuilder_.build();
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.rank_ = rank_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.nextRankScore_ = nextRankScore_;
        to_bitField0_ |= 0x00000010;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.CastSwordSettlementSwordResp) {
        return mergeFrom((xddq.pb.CastSwordSettlementSwordResp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.CastSwordSettlementSwordResp other) {
      if (other == xddq.pb.CastSwordSettlementSwordResp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (other.hasScore()) {
        setScore(other.getScore());
      }
      if (other.hasEntity()) {
        mergeEntity(other.getEntity());
      }
      if (other.hasRank()) {
        setRank(other.getRank());
      }
      if (other.hasNextRankScore()) {
        setNextRankScore(other.getNextRankScore());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      if (hasEntity()) {
        if (!getEntity().isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              score_ = input.readInt32();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 26: {
              input.readMessage(
                  internalGetEntityFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 32: {
              rank_ = input.readInt32();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            case 40: {
              nextRankScore_ = input.readInt32();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private int score_ ;
    /**
     * <code>optional int32 score = 2;</code>
     * @return Whether the score field is set.
     */
    @java.lang.Override
    public boolean hasScore() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 score = 2;</code>
     * @return The score.
     */
    @java.lang.Override
    public int getScore() {
      return score_;
    }
    /**
     * <code>optional int32 score = 2;</code>
     * @param value The score to set.
     * @return This builder for chaining.
     */
    public Builder setScore(int value) {

      score_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 score = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearScore() {
      bitField0_ = (bitField0_ & ~0x00000002);
      score_ = 0;
      onChanged();
      return this;
    }

    private xddq.pb.CastSwordRespBaseDataEntity entity_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.CastSwordRespBaseDataEntity, xddq.pb.CastSwordRespBaseDataEntity.Builder, xddq.pb.CastSwordRespBaseDataEntityOrBuilder> entityBuilder_;
    /**
     * <code>optional .xddq.pb.CastSwordRespBaseDataEntity entity = 3;</code>
     * @return Whether the entity field is set.
     */
    public boolean hasEntity() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional .xddq.pb.CastSwordRespBaseDataEntity entity = 3;</code>
     * @return The entity.
     */
    public xddq.pb.CastSwordRespBaseDataEntity getEntity() {
      if (entityBuilder_ == null) {
        return entity_ == null ? xddq.pb.CastSwordRespBaseDataEntity.getDefaultInstance() : entity_;
      } else {
        return entityBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.CastSwordRespBaseDataEntity entity = 3;</code>
     */
    public Builder setEntity(xddq.pb.CastSwordRespBaseDataEntity value) {
      if (entityBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        entity_ = value;
      } else {
        entityBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.CastSwordRespBaseDataEntity entity = 3;</code>
     */
    public Builder setEntity(
        xddq.pb.CastSwordRespBaseDataEntity.Builder builderForValue) {
      if (entityBuilder_ == null) {
        entity_ = builderForValue.build();
      } else {
        entityBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.CastSwordRespBaseDataEntity entity = 3;</code>
     */
    public Builder mergeEntity(xddq.pb.CastSwordRespBaseDataEntity value) {
      if (entityBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0) &&
          entity_ != null &&
          entity_ != xddq.pb.CastSwordRespBaseDataEntity.getDefaultInstance()) {
          getEntityBuilder().mergeFrom(value);
        } else {
          entity_ = value;
        }
      } else {
        entityBuilder_.mergeFrom(value);
      }
      if (entity_ != null) {
        bitField0_ |= 0x00000004;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.CastSwordRespBaseDataEntity entity = 3;</code>
     */
    public Builder clearEntity() {
      bitField0_ = (bitField0_ & ~0x00000004);
      entity_ = null;
      if (entityBuilder_ != null) {
        entityBuilder_.dispose();
        entityBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.CastSwordRespBaseDataEntity entity = 3;</code>
     */
    public xddq.pb.CastSwordRespBaseDataEntity.Builder getEntityBuilder() {
      bitField0_ |= 0x00000004;
      onChanged();
      return internalGetEntityFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.CastSwordRespBaseDataEntity entity = 3;</code>
     */
    public xddq.pb.CastSwordRespBaseDataEntityOrBuilder getEntityOrBuilder() {
      if (entityBuilder_ != null) {
        return entityBuilder_.getMessageOrBuilder();
      } else {
        return entity_ == null ?
            xddq.pb.CastSwordRespBaseDataEntity.getDefaultInstance() : entity_;
      }
    }
    /**
     * <code>optional .xddq.pb.CastSwordRespBaseDataEntity entity = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.CastSwordRespBaseDataEntity, xddq.pb.CastSwordRespBaseDataEntity.Builder, xddq.pb.CastSwordRespBaseDataEntityOrBuilder> 
        internalGetEntityFieldBuilder() {
      if (entityBuilder_ == null) {
        entityBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.CastSwordRespBaseDataEntity, xddq.pb.CastSwordRespBaseDataEntity.Builder, xddq.pb.CastSwordRespBaseDataEntityOrBuilder>(
                getEntity(),
                getParentForChildren(),
                isClean());
        entity_ = null;
      }
      return entityBuilder_;
    }

    private int rank_ ;
    /**
     * <code>optional int32 rank = 4;</code>
     * @return Whether the rank field is set.
     */
    @java.lang.Override
    public boolean hasRank() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int32 rank = 4;</code>
     * @return The rank.
     */
    @java.lang.Override
    public int getRank() {
      return rank_;
    }
    /**
     * <code>optional int32 rank = 4;</code>
     * @param value The rank to set.
     * @return This builder for chaining.
     */
    public Builder setRank(int value) {

      rank_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 rank = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearRank() {
      bitField0_ = (bitField0_ & ~0x00000008);
      rank_ = 0;
      onChanged();
      return this;
    }

    private int nextRankScore_ ;
    /**
     * <code>optional int32 nextRankScore = 5;</code>
     * @return Whether the nextRankScore field is set.
     */
    @java.lang.Override
    public boolean hasNextRankScore() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional int32 nextRankScore = 5;</code>
     * @return The nextRankScore.
     */
    @java.lang.Override
    public int getNextRankScore() {
      return nextRankScore_;
    }
    /**
     * <code>optional int32 nextRankScore = 5;</code>
     * @param value The nextRankScore to set.
     * @return This builder for chaining.
     */
    public Builder setNextRankScore(int value) {

      nextRankScore_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 nextRankScore = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearNextRankScore() {
      bitField0_ = (bitField0_ & ~0x00000010);
      nextRankScore_ = 0;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.CastSwordSettlementSwordResp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.CastSwordSettlementSwordResp)
  private static final xddq.pb.CastSwordSettlementSwordResp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.CastSwordSettlementSwordResp();
  }

  public static xddq.pb.CastSwordSettlementSwordResp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<CastSwordSettlementSwordResp>
      PARSER = new com.google.protobuf.AbstractParser<CastSwordSettlementSwordResp>() {
    @java.lang.Override
    public CastSwordSettlementSwordResp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<CastSwordSettlementSwordResp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<CastSwordSettlementSwordResp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.CastSwordSettlementSwordResp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

