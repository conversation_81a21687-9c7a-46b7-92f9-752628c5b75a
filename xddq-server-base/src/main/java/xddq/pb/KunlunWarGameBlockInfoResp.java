// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.KunlunWarGameBlockInfoResp}
 */
public final class KunlunWarGameBlockInfoResp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.KunlunWarGameBlockInfoResp)
    KunlunWarGameBlockInfoRespOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      KunlunWarGameBlockInfoResp.class.getName());
  }
  // Use KunlunWarGameBlockInfoResp.newBuilder() to construct.
  private KunlunWarGameBlockInfoResp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private KunlunWarGameBlockInfoResp() {
    cityInfo_ = java.util.Collections.emptyList();
    routeInfo_ = java.util.Collections.emptyList();
    blockInfo_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_KunlunWarGameBlockInfoResp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_KunlunWarGameBlockInfoResp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.KunlunWarGameBlockInfoResp.class, xddq.pb.KunlunWarGameBlockInfoResp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int CITYINFO_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.KunlunWarGameCityInfo> cityInfo_;
  /**
   * <code>repeated .xddq.pb.KunlunWarGameCityInfo cityInfo = 2;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.KunlunWarGameCityInfo> getCityInfoList() {
    return cityInfo_;
  }
  /**
   * <code>repeated .xddq.pb.KunlunWarGameCityInfo cityInfo = 2;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.KunlunWarGameCityInfoOrBuilder> 
      getCityInfoOrBuilderList() {
    return cityInfo_;
  }
  /**
   * <code>repeated .xddq.pb.KunlunWarGameCityInfo cityInfo = 2;</code>
   */
  @java.lang.Override
  public int getCityInfoCount() {
    return cityInfo_.size();
  }
  /**
   * <code>repeated .xddq.pb.KunlunWarGameCityInfo cityInfo = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.KunlunWarGameCityInfo getCityInfo(int index) {
    return cityInfo_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.KunlunWarGameCityInfo cityInfo = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.KunlunWarGameCityInfoOrBuilder getCityInfoOrBuilder(
      int index) {
    return cityInfo_.get(index);
  }

  public static final int ROUTEINFO_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.KunlunWarGameRouteInfo> routeInfo_;
  /**
   * <code>repeated .xddq.pb.KunlunWarGameRouteInfo routeInfo = 3;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.KunlunWarGameRouteInfo> getRouteInfoList() {
    return routeInfo_;
  }
  /**
   * <code>repeated .xddq.pb.KunlunWarGameRouteInfo routeInfo = 3;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.KunlunWarGameRouteInfoOrBuilder> 
      getRouteInfoOrBuilderList() {
    return routeInfo_;
  }
  /**
   * <code>repeated .xddq.pb.KunlunWarGameRouteInfo routeInfo = 3;</code>
   */
  @java.lang.Override
  public int getRouteInfoCount() {
    return routeInfo_.size();
  }
  /**
   * <code>repeated .xddq.pb.KunlunWarGameRouteInfo routeInfo = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.KunlunWarGameRouteInfo getRouteInfo(int index) {
    return routeInfo_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.KunlunWarGameRouteInfo routeInfo = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.KunlunWarGameRouteInfoOrBuilder getRouteInfoOrBuilder(
      int index) {
    return routeInfo_.get(index);
  }

  public static final int FLOORID_FIELD_NUMBER = 4;
  private int floorId_ = 0;
  /**
   * <code>optional int32 floorId = 4;</code>
   * @return Whether the floorId field is set.
   */
  @java.lang.Override
  public boolean hasFloorId() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int32 floorId = 4;</code>
   * @return The floorId.
   */
  @java.lang.Override
  public int getFloorId() {
    return floorId_;
  }

  public static final int GROUPID_FIELD_NUMBER = 5;
  private int groupId_ = 0;
  /**
   * <code>optional int32 groupId = 5;</code>
   * @return Whether the groupId field is set.
   */
  @java.lang.Override
  public boolean hasGroupId() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional int32 groupId = 5;</code>
   * @return The groupId.
   */
  @java.lang.Override
  public int getGroupId() {
    return groupId_;
  }

  public static final int BLOCKINFO_FIELD_NUMBER = 6;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.KunlunWarBlockInfo> blockInfo_;
  /**
   * <code>repeated .xddq.pb.KunlunWarBlockInfo blockInfo = 6;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.KunlunWarBlockInfo> getBlockInfoList() {
    return blockInfo_;
  }
  /**
   * <code>repeated .xddq.pb.KunlunWarBlockInfo blockInfo = 6;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.KunlunWarBlockInfoOrBuilder> 
      getBlockInfoOrBuilderList() {
    return blockInfo_;
  }
  /**
   * <code>repeated .xddq.pb.KunlunWarBlockInfo blockInfo = 6;</code>
   */
  @java.lang.Override
  public int getBlockInfoCount() {
    return blockInfo_.size();
  }
  /**
   * <code>repeated .xddq.pb.KunlunWarBlockInfo blockInfo = 6;</code>
   */
  @java.lang.Override
  public xddq.pb.KunlunWarBlockInfo getBlockInfo(int index) {
    return blockInfo_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.KunlunWarBlockInfo blockInfo = 6;</code>
   */
  @java.lang.Override
  public xddq.pb.KunlunWarBlockInfoOrBuilder getBlockInfoOrBuilder(
      int index) {
    return blockInfo_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    for (int i = 0; i < getCityInfoCount(); i++) {
      if (!getCityInfo(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    for (int i = 0; i < cityInfo_.size(); i++) {
      output.writeMessage(2, cityInfo_.get(i));
    }
    for (int i = 0; i < routeInfo_.size(); i++) {
      output.writeMessage(3, routeInfo_.get(i));
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(4, floorId_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt32(5, groupId_);
    }
    for (int i = 0; i < blockInfo_.size(); i++) {
      output.writeMessage(6, blockInfo_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    for (int i = 0; i < cityInfo_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, cityInfo_.get(i));
    }
    for (int i = 0; i < routeInfo_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, routeInfo_.get(i));
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(4, floorId_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(5, groupId_);
    }
    for (int i = 0; i < blockInfo_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(6, blockInfo_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.KunlunWarGameBlockInfoResp)) {
      return super.equals(obj);
    }
    xddq.pb.KunlunWarGameBlockInfoResp other = (xddq.pb.KunlunWarGameBlockInfoResp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (!getCityInfoList()
        .equals(other.getCityInfoList())) return false;
    if (!getRouteInfoList()
        .equals(other.getRouteInfoList())) return false;
    if (hasFloorId() != other.hasFloorId()) return false;
    if (hasFloorId()) {
      if (getFloorId()
          != other.getFloorId()) return false;
    }
    if (hasGroupId() != other.hasGroupId()) return false;
    if (hasGroupId()) {
      if (getGroupId()
          != other.getGroupId()) return false;
    }
    if (!getBlockInfoList()
        .equals(other.getBlockInfoList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (getCityInfoCount() > 0) {
      hash = (37 * hash) + CITYINFO_FIELD_NUMBER;
      hash = (53 * hash) + getCityInfoList().hashCode();
    }
    if (getRouteInfoCount() > 0) {
      hash = (37 * hash) + ROUTEINFO_FIELD_NUMBER;
      hash = (53 * hash) + getRouteInfoList().hashCode();
    }
    if (hasFloorId()) {
      hash = (37 * hash) + FLOORID_FIELD_NUMBER;
      hash = (53 * hash) + getFloorId();
    }
    if (hasGroupId()) {
      hash = (37 * hash) + GROUPID_FIELD_NUMBER;
      hash = (53 * hash) + getGroupId();
    }
    if (getBlockInfoCount() > 0) {
      hash = (37 * hash) + BLOCKINFO_FIELD_NUMBER;
      hash = (53 * hash) + getBlockInfoList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.KunlunWarGameBlockInfoResp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.KunlunWarGameBlockInfoResp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.KunlunWarGameBlockInfoResp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.KunlunWarGameBlockInfoResp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.KunlunWarGameBlockInfoResp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.KunlunWarGameBlockInfoResp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.KunlunWarGameBlockInfoResp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.KunlunWarGameBlockInfoResp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.KunlunWarGameBlockInfoResp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.KunlunWarGameBlockInfoResp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.KunlunWarGameBlockInfoResp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.KunlunWarGameBlockInfoResp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.KunlunWarGameBlockInfoResp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.KunlunWarGameBlockInfoResp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.KunlunWarGameBlockInfoResp)
      xddq.pb.KunlunWarGameBlockInfoRespOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_KunlunWarGameBlockInfoResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_KunlunWarGameBlockInfoResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.KunlunWarGameBlockInfoResp.class, xddq.pb.KunlunWarGameBlockInfoResp.Builder.class);
    }

    // Construct using xddq.pb.KunlunWarGameBlockInfoResp.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      if (cityInfoBuilder_ == null) {
        cityInfo_ = java.util.Collections.emptyList();
      } else {
        cityInfo_ = null;
        cityInfoBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000002);
      if (routeInfoBuilder_ == null) {
        routeInfo_ = java.util.Collections.emptyList();
      } else {
        routeInfo_ = null;
        routeInfoBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000004);
      floorId_ = 0;
      groupId_ = 0;
      if (blockInfoBuilder_ == null) {
        blockInfo_ = java.util.Collections.emptyList();
      } else {
        blockInfo_ = null;
        blockInfoBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000020);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_KunlunWarGameBlockInfoResp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.KunlunWarGameBlockInfoResp getDefaultInstanceForType() {
      return xddq.pb.KunlunWarGameBlockInfoResp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.KunlunWarGameBlockInfoResp build() {
      xddq.pb.KunlunWarGameBlockInfoResp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.KunlunWarGameBlockInfoResp buildPartial() {
      xddq.pb.KunlunWarGameBlockInfoResp result = new xddq.pb.KunlunWarGameBlockInfoResp(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.KunlunWarGameBlockInfoResp result) {
      if (cityInfoBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          cityInfo_ = java.util.Collections.unmodifiableList(cityInfo_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.cityInfo_ = cityInfo_;
      } else {
        result.cityInfo_ = cityInfoBuilder_.build();
      }
      if (routeInfoBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0)) {
          routeInfo_ = java.util.Collections.unmodifiableList(routeInfo_);
          bitField0_ = (bitField0_ & ~0x00000004);
        }
        result.routeInfo_ = routeInfo_;
      } else {
        result.routeInfo_ = routeInfoBuilder_.build();
      }
      if (blockInfoBuilder_ == null) {
        if (((bitField0_ & 0x00000020) != 0)) {
          blockInfo_ = java.util.Collections.unmodifiableList(blockInfo_);
          bitField0_ = (bitField0_ & ~0x00000020);
        }
        result.blockInfo_ = blockInfo_;
      } else {
        result.blockInfo_ = blockInfoBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.KunlunWarGameBlockInfoResp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.floorId_ = floorId_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.groupId_ = groupId_;
        to_bitField0_ |= 0x00000004;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.KunlunWarGameBlockInfoResp) {
        return mergeFrom((xddq.pb.KunlunWarGameBlockInfoResp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.KunlunWarGameBlockInfoResp other) {
      if (other == xddq.pb.KunlunWarGameBlockInfoResp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (cityInfoBuilder_ == null) {
        if (!other.cityInfo_.isEmpty()) {
          if (cityInfo_.isEmpty()) {
            cityInfo_ = other.cityInfo_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureCityInfoIsMutable();
            cityInfo_.addAll(other.cityInfo_);
          }
          onChanged();
        }
      } else {
        if (!other.cityInfo_.isEmpty()) {
          if (cityInfoBuilder_.isEmpty()) {
            cityInfoBuilder_.dispose();
            cityInfoBuilder_ = null;
            cityInfo_ = other.cityInfo_;
            bitField0_ = (bitField0_ & ~0x00000002);
            cityInfoBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetCityInfoFieldBuilder() : null;
          } else {
            cityInfoBuilder_.addAllMessages(other.cityInfo_);
          }
        }
      }
      if (routeInfoBuilder_ == null) {
        if (!other.routeInfo_.isEmpty()) {
          if (routeInfo_.isEmpty()) {
            routeInfo_ = other.routeInfo_;
            bitField0_ = (bitField0_ & ~0x00000004);
          } else {
            ensureRouteInfoIsMutable();
            routeInfo_.addAll(other.routeInfo_);
          }
          onChanged();
        }
      } else {
        if (!other.routeInfo_.isEmpty()) {
          if (routeInfoBuilder_.isEmpty()) {
            routeInfoBuilder_.dispose();
            routeInfoBuilder_ = null;
            routeInfo_ = other.routeInfo_;
            bitField0_ = (bitField0_ & ~0x00000004);
            routeInfoBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetRouteInfoFieldBuilder() : null;
          } else {
            routeInfoBuilder_.addAllMessages(other.routeInfo_);
          }
        }
      }
      if (other.hasFloorId()) {
        setFloorId(other.getFloorId());
      }
      if (other.hasGroupId()) {
        setGroupId(other.getGroupId());
      }
      if (blockInfoBuilder_ == null) {
        if (!other.blockInfo_.isEmpty()) {
          if (blockInfo_.isEmpty()) {
            blockInfo_ = other.blockInfo_;
            bitField0_ = (bitField0_ & ~0x00000020);
          } else {
            ensureBlockInfoIsMutable();
            blockInfo_.addAll(other.blockInfo_);
          }
          onChanged();
        }
      } else {
        if (!other.blockInfo_.isEmpty()) {
          if (blockInfoBuilder_.isEmpty()) {
            blockInfoBuilder_.dispose();
            blockInfoBuilder_ = null;
            blockInfo_ = other.blockInfo_;
            bitField0_ = (bitField0_ & ~0x00000020);
            blockInfoBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetBlockInfoFieldBuilder() : null;
          } else {
            blockInfoBuilder_.addAllMessages(other.blockInfo_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      for (int i = 0; i < getCityInfoCount(); i++) {
        if (!getCityInfo(i).isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              xddq.pb.KunlunWarGameCityInfo m =
                  input.readMessage(
                      xddq.pb.KunlunWarGameCityInfo.parser(),
                      extensionRegistry);
              if (cityInfoBuilder_ == null) {
                ensureCityInfoIsMutable();
                cityInfo_.add(m);
              } else {
                cityInfoBuilder_.addMessage(m);
              }
              break;
            } // case 18
            case 26: {
              xddq.pb.KunlunWarGameRouteInfo m =
                  input.readMessage(
                      xddq.pb.KunlunWarGameRouteInfo.parser(),
                      extensionRegistry);
              if (routeInfoBuilder_ == null) {
                ensureRouteInfoIsMutable();
                routeInfo_.add(m);
              } else {
                routeInfoBuilder_.addMessage(m);
              }
              break;
            } // case 26
            case 32: {
              floorId_ = input.readInt32();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            case 40: {
              groupId_ = input.readInt32();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            case 50: {
              xddq.pb.KunlunWarBlockInfo m =
                  input.readMessage(
                      xddq.pb.KunlunWarBlockInfo.parser(),
                      extensionRegistry);
              if (blockInfoBuilder_ == null) {
                ensureBlockInfoIsMutable();
                blockInfo_.add(m);
              } else {
                blockInfoBuilder_.addMessage(m);
              }
              break;
            } // case 50
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.KunlunWarGameCityInfo> cityInfo_ =
      java.util.Collections.emptyList();
    private void ensureCityInfoIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        cityInfo_ = new java.util.ArrayList<xddq.pb.KunlunWarGameCityInfo>(cityInfo_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.KunlunWarGameCityInfo, xddq.pb.KunlunWarGameCityInfo.Builder, xddq.pb.KunlunWarGameCityInfoOrBuilder> cityInfoBuilder_;

    /**
     * <code>repeated .xddq.pb.KunlunWarGameCityInfo cityInfo = 2;</code>
     */
    public java.util.List<xddq.pb.KunlunWarGameCityInfo> getCityInfoList() {
      if (cityInfoBuilder_ == null) {
        return java.util.Collections.unmodifiableList(cityInfo_);
      } else {
        return cityInfoBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGameCityInfo cityInfo = 2;</code>
     */
    public int getCityInfoCount() {
      if (cityInfoBuilder_ == null) {
        return cityInfo_.size();
      } else {
        return cityInfoBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGameCityInfo cityInfo = 2;</code>
     */
    public xddq.pb.KunlunWarGameCityInfo getCityInfo(int index) {
      if (cityInfoBuilder_ == null) {
        return cityInfo_.get(index);
      } else {
        return cityInfoBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGameCityInfo cityInfo = 2;</code>
     */
    public Builder setCityInfo(
        int index, xddq.pb.KunlunWarGameCityInfo value) {
      if (cityInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureCityInfoIsMutable();
        cityInfo_.set(index, value);
        onChanged();
      } else {
        cityInfoBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGameCityInfo cityInfo = 2;</code>
     */
    public Builder setCityInfo(
        int index, xddq.pb.KunlunWarGameCityInfo.Builder builderForValue) {
      if (cityInfoBuilder_ == null) {
        ensureCityInfoIsMutable();
        cityInfo_.set(index, builderForValue.build());
        onChanged();
      } else {
        cityInfoBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGameCityInfo cityInfo = 2;</code>
     */
    public Builder addCityInfo(xddq.pb.KunlunWarGameCityInfo value) {
      if (cityInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureCityInfoIsMutable();
        cityInfo_.add(value);
        onChanged();
      } else {
        cityInfoBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGameCityInfo cityInfo = 2;</code>
     */
    public Builder addCityInfo(
        int index, xddq.pb.KunlunWarGameCityInfo value) {
      if (cityInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureCityInfoIsMutable();
        cityInfo_.add(index, value);
        onChanged();
      } else {
        cityInfoBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGameCityInfo cityInfo = 2;</code>
     */
    public Builder addCityInfo(
        xddq.pb.KunlunWarGameCityInfo.Builder builderForValue) {
      if (cityInfoBuilder_ == null) {
        ensureCityInfoIsMutable();
        cityInfo_.add(builderForValue.build());
        onChanged();
      } else {
        cityInfoBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGameCityInfo cityInfo = 2;</code>
     */
    public Builder addCityInfo(
        int index, xddq.pb.KunlunWarGameCityInfo.Builder builderForValue) {
      if (cityInfoBuilder_ == null) {
        ensureCityInfoIsMutable();
        cityInfo_.add(index, builderForValue.build());
        onChanged();
      } else {
        cityInfoBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGameCityInfo cityInfo = 2;</code>
     */
    public Builder addAllCityInfo(
        java.lang.Iterable<? extends xddq.pb.KunlunWarGameCityInfo> values) {
      if (cityInfoBuilder_ == null) {
        ensureCityInfoIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, cityInfo_);
        onChanged();
      } else {
        cityInfoBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGameCityInfo cityInfo = 2;</code>
     */
    public Builder clearCityInfo() {
      if (cityInfoBuilder_ == null) {
        cityInfo_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        cityInfoBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGameCityInfo cityInfo = 2;</code>
     */
    public Builder removeCityInfo(int index) {
      if (cityInfoBuilder_ == null) {
        ensureCityInfoIsMutable();
        cityInfo_.remove(index);
        onChanged();
      } else {
        cityInfoBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGameCityInfo cityInfo = 2;</code>
     */
    public xddq.pb.KunlunWarGameCityInfo.Builder getCityInfoBuilder(
        int index) {
      return internalGetCityInfoFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGameCityInfo cityInfo = 2;</code>
     */
    public xddq.pb.KunlunWarGameCityInfoOrBuilder getCityInfoOrBuilder(
        int index) {
      if (cityInfoBuilder_ == null) {
        return cityInfo_.get(index);  } else {
        return cityInfoBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGameCityInfo cityInfo = 2;</code>
     */
    public java.util.List<? extends xddq.pb.KunlunWarGameCityInfoOrBuilder> 
         getCityInfoOrBuilderList() {
      if (cityInfoBuilder_ != null) {
        return cityInfoBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(cityInfo_);
      }
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGameCityInfo cityInfo = 2;</code>
     */
    public xddq.pb.KunlunWarGameCityInfo.Builder addCityInfoBuilder() {
      return internalGetCityInfoFieldBuilder().addBuilder(
          xddq.pb.KunlunWarGameCityInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGameCityInfo cityInfo = 2;</code>
     */
    public xddq.pb.KunlunWarGameCityInfo.Builder addCityInfoBuilder(
        int index) {
      return internalGetCityInfoFieldBuilder().addBuilder(
          index, xddq.pb.KunlunWarGameCityInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGameCityInfo cityInfo = 2;</code>
     */
    public java.util.List<xddq.pb.KunlunWarGameCityInfo.Builder> 
         getCityInfoBuilderList() {
      return internalGetCityInfoFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.KunlunWarGameCityInfo, xddq.pb.KunlunWarGameCityInfo.Builder, xddq.pb.KunlunWarGameCityInfoOrBuilder> 
        internalGetCityInfoFieldBuilder() {
      if (cityInfoBuilder_ == null) {
        cityInfoBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.KunlunWarGameCityInfo, xddq.pb.KunlunWarGameCityInfo.Builder, xddq.pb.KunlunWarGameCityInfoOrBuilder>(
                cityInfo_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        cityInfo_ = null;
      }
      return cityInfoBuilder_;
    }

    private java.util.List<xddq.pb.KunlunWarGameRouteInfo> routeInfo_ =
      java.util.Collections.emptyList();
    private void ensureRouteInfoIsMutable() {
      if (!((bitField0_ & 0x00000004) != 0)) {
        routeInfo_ = new java.util.ArrayList<xddq.pb.KunlunWarGameRouteInfo>(routeInfo_);
        bitField0_ |= 0x00000004;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.KunlunWarGameRouteInfo, xddq.pb.KunlunWarGameRouteInfo.Builder, xddq.pb.KunlunWarGameRouteInfoOrBuilder> routeInfoBuilder_;

    /**
     * <code>repeated .xddq.pb.KunlunWarGameRouteInfo routeInfo = 3;</code>
     */
    public java.util.List<xddq.pb.KunlunWarGameRouteInfo> getRouteInfoList() {
      if (routeInfoBuilder_ == null) {
        return java.util.Collections.unmodifiableList(routeInfo_);
      } else {
        return routeInfoBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGameRouteInfo routeInfo = 3;</code>
     */
    public int getRouteInfoCount() {
      if (routeInfoBuilder_ == null) {
        return routeInfo_.size();
      } else {
        return routeInfoBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGameRouteInfo routeInfo = 3;</code>
     */
    public xddq.pb.KunlunWarGameRouteInfo getRouteInfo(int index) {
      if (routeInfoBuilder_ == null) {
        return routeInfo_.get(index);
      } else {
        return routeInfoBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGameRouteInfo routeInfo = 3;</code>
     */
    public Builder setRouteInfo(
        int index, xddq.pb.KunlunWarGameRouteInfo value) {
      if (routeInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRouteInfoIsMutable();
        routeInfo_.set(index, value);
        onChanged();
      } else {
        routeInfoBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGameRouteInfo routeInfo = 3;</code>
     */
    public Builder setRouteInfo(
        int index, xddq.pb.KunlunWarGameRouteInfo.Builder builderForValue) {
      if (routeInfoBuilder_ == null) {
        ensureRouteInfoIsMutable();
        routeInfo_.set(index, builderForValue.build());
        onChanged();
      } else {
        routeInfoBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGameRouteInfo routeInfo = 3;</code>
     */
    public Builder addRouteInfo(xddq.pb.KunlunWarGameRouteInfo value) {
      if (routeInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRouteInfoIsMutable();
        routeInfo_.add(value);
        onChanged();
      } else {
        routeInfoBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGameRouteInfo routeInfo = 3;</code>
     */
    public Builder addRouteInfo(
        int index, xddq.pb.KunlunWarGameRouteInfo value) {
      if (routeInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRouteInfoIsMutable();
        routeInfo_.add(index, value);
        onChanged();
      } else {
        routeInfoBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGameRouteInfo routeInfo = 3;</code>
     */
    public Builder addRouteInfo(
        xddq.pb.KunlunWarGameRouteInfo.Builder builderForValue) {
      if (routeInfoBuilder_ == null) {
        ensureRouteInfoIsMutable();
        routeInfo_.add(builderForValue.build());
        onChanged();
      } else {
        routeInfoBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGameRouteInfo routeInfo = 3;</code>
     */
    public Builder addRouteInfo(
        int index, xddq.pb.KunlunWarGameRouteInfo.Builder builderForValue) {
      if (routeInfoBuilder_ == null) {
        ensureRouteInfoIsMutable();
        routeInfo_.add(index, builderForValue.build());
        onChanged();
      } else {
        routeInfoBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGameRouteInfo routeInfo = 3;</code>
     */
    public Builder addAllRouteInfo(
        java.lang.Iterable<? extends xddq.pb.KunlunWarGameRouteInfo> values) {
      if (routeInfoBuilder_ == null) {
        ensureRouteInfoIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, routeInfo_);
        onChanged();
      } else {
        routeInfoBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGameRouteInfo routeInfo = 3;</code>
     */
    public Builder clearRouteInfo() {
      if (routeInfoBuilder_ == null) {
        routeInfo_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
      } else {
        routeInfoBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGameRouteInfo routeInfo = 3;</code>
     */
    public Builder removeRouteInfo(int index) {
      if (routeInfoBuilder_ == null) {
        ensureRouteInfoIsMutable();
        routeInfo_.remove(index);
        onChanged();
      } else {
        routeInfoBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGameRouteInfo routeInfo = 3;</code>
     */
    public xddq.pb.KunlunWarGameRouteInfo.Builder getRouteInfoBuilder(
        int index) {
      return internalGetRouteInfoFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGameRouteInfo routeInfo = 3;</code>
     */
    public xddq.pb.KunlunWarGameRouteInfoOrBuilder getRouteInfoOrBuilder(
        int index) {
      if (routeInfoBuilder_ == null) {
        return routeInfo_.get(index);  } else {
        return routeInfoBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGameRouteInfo routeInfo = 3;</code>
     */
    public java.util.List<? extends xddq.pb.KunlunWarGameRouteInfoOrBuilder> 
         getRouteInfoOrBuilderList() {
      if (routeInfoBuilder_ != null) {
        return routeInfoBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(routeInfo_);
      }
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGameRouteInfo routeInfo = 3;</code>
     */
    public xddq.pb.KunlunWarGameRouteInfo.Builder addRouteInfoBuilder() {
      return internalGetRouteInfoFieldBuilder().addBuilder(
          xddq.pb.KunlunWarGameRouteInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGameRouteInfo routeInfo = 3;</code>
     */
    public xddq.pb.KunlunWarGameRouteInfo.Builder addRouteInfoBuilder(
        int index) {
      return internalGetRouteInfoFieldBuilder().addBuilder(
          index, xddq.pb.KunlunWarGameRouteInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGameRouteInfo routeInfo = 3;</code>
     */
    public java.util.List<xddq.pb.KunlunWarGameRouteInfo.Builder> 
         getRouteInfoBuilderList() {
      return internalGetRouteInfoFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.KunlunWarGameRouteInfo, xddq.pb.KunlunWarGameRouteInfo.Builder, xddq.pb.KunlunWarGameRouteInfoOrBuilder> 
        internalGetRouteInfoFieldBuilder() {
      if (routeInfoBuilder_ == null) {
        routeInfoBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.KunlunWarGameRouteInfo, xddq.pb.KunlunWarGameRouteInfo.Builder, xddq.pb.KunlunWarGameRouteInfoOrBuilder>(
                routeInfo_,
                ((bitField0_ & 0x00000004) != 0),
                getParentForChildren(),
                isClean());
        routeInfo_ = null;
      }
      return routeInfoBuilder_;
    }

    private int floorId_ ;
    /**
     * <code>optional int32 floorId = 4;</code>
     * @return Whether the floorId field is set.
     */
    @java.lang.Override
    public boolean hasFloorId() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int32 floorId = 4;</code>
     * @return The floorId.
     */
    @java.lang.Override
    public int getFloorId() {
      return floorId_;
    }
    /**
     * <code>optional int32 floorId = 4;</code>
     * @param value The floorId to set.
     * @return This builder for chaining.
     */
    public Builder setFloorId(int value) {

      floorId_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 floorId = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearFloorId() {
      bitField0_ = (bitField0_ & ~0x00000008);
      floorId_ = 0;
      onChanged();
      return this;
    }

    private int groupId_ ;
    /**
     * <code>optional int32 groupId = 5;</code>
     * @return Whether the groupId field is set.
     */
    @java.lang.Override
    public boolean hasGroupId() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional int32 groupId = 5;</code>
     * @return The groupId.
     */
    @java.lang.Override
    public int getGroupId() {
      return groupId_;
    }
    /**
     * <code>optional int32 groupId = 5;</code>
     * @param value The groupId to set.
     * @return This builder for chaining.
     */
    public Builder setGroupId(int value) {

      groupId_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 groupId = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearGroupId() {
      bitField0_ = (bitField0_ & ~0x00000010);
      groupId_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.KunlunWarBlockInfo> blockInfo_ =
      java.util.Collections.emptyList();
    private void ensureBlockInfoIsMutable() {
      if (!((bitField0_ & 0x00000020) != 0)) {
        blockInfo_ = new java.util.ArrayList<xddq.pb.KunlunWarBlockInfo>(blockInfo_);
        bitField0_ |= 0x00000020;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.KunlunWarBlockInfo, xddq.pb.KunlunWarBlockInfo.Builder, xddq.pb.KunlunWarBlockInfoOrBuilder> blockInfoBuilder_;

    /**
     * <code>repeated .xddq.pb.KunlunWarBlockInfo blockInfo = 6;</code>
     */
    public java.util.List<xddq.pb.KunlunWarBlockInfo> getBlockInfoList() {
      if (blockInfoBuilder_ == null) {
        return java.util.Collections.unmodifiableList(blockInfo_);
      } else {
        return blockInfoBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarBlockInfo blockInfo = 6;</code>
     */
    public int getBlockInfoCount() {
      if (blockInfoBuilder_ == null) {
        return blockInfo_.size();
      } else {
        return blockInfoBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarBlockInfo blockInfo = 6;</code>
     */
    public xddq.pb.KunlunWarBlockInfo getBlockInfo(int index) {
      if (blockInfoBuilder_ == null) {
        return blockInfo_.get(index);
      } else {
        return blockInfoBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarBlockInfo blockInfo = 6;</code>
     */
    public Builder setBlockInfo(
        int index, xddq.pb.KunlunWarBlockInfo value) {
      if (blockInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureBlockInfoIsMutable();
        blockInfo_.set(index, value);
        onChanged();
      } else {
        blockInfoBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarBlockInfo blockInfo = 6;</code>
     */
    public Builder setBlockInfo(
        int index, xddq.pb.KunlunWarBlockInfo.Builder builderForValue) {
      if (blockInfoBuilder_ == null) {
        ensureBlockInfoIsMutable();
        blockInfo_.set(index, builderForValue.build());
        onChanged();
      } else {
        blockInfoBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarBlockInfo blockInfo = 6;</code>
     */
    public Builder addBlockInfo(xddq.pb.KunlunWarBlockInfo value) {
      if (blockInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureBlockInfoIsMutable();
        blockInfo_.add(value);
        onChanged();
      } else {
        blockInfoBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarBlockInfo blockInfo = 6;</code>
     */
    public Builder addBlockInfo(
        int index, xddq.pb.KunlunWarBlockInfo value) {
      if (blockInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureBlockInfoIsMutable();
        blockInfo_.add(index, value);
        onChanged();
      } else {
        blockInfoBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarBlockInfo blockInfo = 6;</code>
     */
    public Builder addBlockInfo(
        xddq.pb.KunlunWarBlockInfo.Builder builderForValue) {
      if (blockInfoBuilder_ == null) {
        ensureBlockInfoIsMutable();
        blockInfo_.add(builderForValue.build());
        onChanged();
      } else {
        blockInfoBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarBlockInfo blockInfo = 6;</code>
     */
    public Builder addBlockInfo(
        int index, xddq.pb.KunlunWarBlockInfo.Builder builderForValue) {
      if (blockInfoBuilder_ == null) {
        ensureBlockInfoIsMutable();
        blockInfo_.add(index, builderForValue.build());
        onChanged();
      } else {
        blockInfoBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarBlockInfo blockInfo = 6;</code>
     */
    public Builder addAllBlockInfo(
        java.lang.Iterable<? extends xddq.pb.KunlunWarBlockInfo> values) {
      if (blockInfoBuilder_ == null) {
        ensureBlockInfoIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, blockInfo_);
        onChanged();
      } else {
        blockInfoBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarBlockInfo blockInfo = 6;</code>
     */
    public Builder clearBlockInfo() {
      if (blockInfoBuilder_ == null) {
        blockInfo_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000020);
        onChanged();
      } else {
        blockInfoBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarBlockInfo blockInfo = 6;</code>
     */
    public Builder removeBlockInfo(int index) {
      if (blockInfoBuilder_ == null) {
        ensureBlockInfoIsMutable();
        blockInfo_.remove(index);
        onChanged();
      } else {
        blockInfoBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarBlockInfo blockInfo = 6;</code>
     */
    public xddq.pb.KunlunWarBlockInfo.Builder getBlockInfoBuilder(
        int index) {
      return internalGetBlockInfoFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarBlockInfo blockInfo = 6;</code>
     */
    public xddq.pb.KunlunWarBlockInfoOrBuilder getBlockInfoOrBuilder(
        int index) {
      if (blockInfoBuilder_ == null) {
        return blockInfo_.get(index);  } else {
        return blockInfoBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarBlockInfo blockInfo = 6;</code>
     */
    public java.util.List<? extends xddq.pb.KunlunWarBlockInfoOrBuilder> 
         getBlockInfoOrBuilderList() {
      if (blockInfoBuilder_ != null) {
        return blockInfoBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(blockInfo_);
      }
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarBlockInfo blockInfo = 6;</code>
     */
    public xddq.pb.KunlunWarBlockInfo.Builder addBlockInfoBuilder() {
      return internalGetBlockInfoFieldBuilder().addBuilder(
          xddq.pb.KunlunWarBlockInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarBlockInfo blockInfo = 6;</code>
     */
    public xddq.pb.KunlunWarBlockInfo.Builder addBlockInfoBuilder(
        int index) {
      return internalGetBlockInfoFieldBuilder().addBuilder(
          index, xddq.pb.KunlunWarBlockInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarBlockInfo blockInfo = 6;</code>
     */
    public java.util.List<xddq.pb.KunlunWarBlockInfo.Builder> 
         getBlockInfoBuilderList() {
      return internalGetBlockInfoFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.KunlunWarBlockInfo, xddq.pb.KunlunWarBlockInfo.Builder, xddq.pb.KunlunWarBlockInfoOrBuilder> 
        internalGetBlockInfoFieldBuilder() {
      if (blockInfoBuilder_ == null) {
        blockInfoBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.KunlunWarBlockInfo, xddq.pb.KunlunWarBlockInfo.Builder, xddq.pb.KunlunWarBlockInfoOrBuilder>(
                blockInfo_,
                ((bitField0_ & 0x00000020) != 0),
                getParentForChildren(),
                isClean());
        blockInfo_ = null;
      }
      return blockInfoBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.KunlunWarGameBlockInfoResp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.KunlunWarGameBlockInfoResp)
  private static final xddq.pb.KunlunWarGameBlockInfoResp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.KunlunWarGameBlockInfoResp();
  }

  public static xddq.pb.KunlunWarGameBlockInfoResp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<KunlunWarGameBlockInfoResp>
      PARSER = new com.google.protobuf.AbstractParser<KunlunWarGameBlockInfoResp>() {
    @java.lang.Override
    public KunlunWarGameBlockInfoResp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<KunlunWarGameBlockInfoResp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<KunlunWarGameBlockInfoResp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.KunlunWarGameBlockInfoResp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

