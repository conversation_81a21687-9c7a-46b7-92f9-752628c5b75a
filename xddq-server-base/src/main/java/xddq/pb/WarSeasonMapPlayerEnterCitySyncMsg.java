// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.WarSeasonMapPlayerEnterCitySyncMsg}
 */
public final class WarSeasonMapPlayerEnterCitySyncMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.WarSeasonMapPlayerEnterCitySyncMsg)
    WarSeasonMapPlayerEnterCitySyncMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      WarSeasonMapPlayerEnterCitySyncMsg.class.getName());
  }
  // Use WarSeasonMapPlayerEnterCitySyncMsg.newBuilder() to construct.
  private WarSeasonMapPlayerEnterCitySyncMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private WarSeasonMapPlayerEnterCitySyncMsg() {
    data_ = java.util.Collections.emptyList();
    moveCityPlayerIds_ = emptyLongList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_WarSeasonMapPlayerEnterCitySyncMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_WarSeasonMapPlayerEnterCitySyncMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.WarSeasonMapPlayerEnterCitySyncMsg.class, xddq.pb.WarSeasonMapPlayerEnterCitySyncMsg.Builder.class);
  }

  private int bitField0_;
  public static final int CITYID_FIELD_NUMBER = 1;
  private int cityId_ = 0;
  /**
   * <code>optional int32 cityId = 1;</code>
   * @return Whether the cityId field is set.
   */
  @java.lang.Override
  public boolean hasCityId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int32 cityId = 1;</code>
   * @return The cityId.
   */
  @java.lang.Override
  public int getCityId() {
    return cityId_;
  }

  public static final int ROLENUM_FIELD_NUMBER = 2;
  private int roleNum_ = 0;
  /**
   * <code>optional int32 roleNum = 2;</code>
   * @return Whether the roleNum field is set.
   */
  @java.lang.Override
  public boolean hasRoleNum() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int32 roleNum = 2;</code>
   * @return The roleNum.
   */
  @java.lang.Override
  public int getRoleNum() {
    return roleNum_;
  }

  public static final int DATA_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.WarSeasonMapBattleDataMsg> data_;
  /**
   * <code>repeated .xddq.pb.WarSeasonMapBattleDataMsg data = 3;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.WarSeasonMapBattleDataMsg> getDataList() {
    return data_;
  }
  /**
   * <code>repeated .xddq.pb.WarSeasonMapBattleDataMsg data = 3;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.WarSeasonMapBattleDataMsgOrBuilder> 
      getDataOrBuilderList() {
    return data_;
  }
  /**
   * <code>repeated .xddq.pb.WarSeasonMapBattleDataMsg data = 3;</code>
   */
  @java.lang.Override
  public int getDataCount() {
    return data_.size();
  }
  /**
   * <code>repeated .xddq.pb.WarSeasonMapBattleDataMsg data = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.WarSeasonMapBattleDataMsg getData(int index) {
    return data_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.WarSeasonMapBattleDataMsg data = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.WarSeasonMapBattleDataMsgOrBuilder getDataOrBuilder(
      int index) {
    return data_.get(index);
  }

  public static final int ISMOVEBORN_FIELD_NUMBER = 4;
  private boolean isMoveBorn_ = false;
  /**
   * <code>optional bool isMoveBorn = 4;</code>
   * @return Whether the isMoveBorn field is set.
   */
  @java.lang.Override
  public boolean hasIsMoveBorn() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional bool isMoveBorn = 4;</code>
   * @return The isMoveBorn.
   */
  @java.lang.Override
  public boolean getIsMoveBorn() {
    return isMoveBorn_;
  }

  public static final int UNIONID_FIELD_NUMBER = 5;
  private long unionId_ = 0L;
  /**
   * <code>optional int64 unionId = 5;</code>
   * @return Whether the unionId field is set.
   */
  @java.lang.Override
  public boolean hasUnionId() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional int64 unionId = 5;</code>
   * @return The unionId.
   */
  @java.lang.Override
  public long getUnionId() {
    return unionId_;
  }

  public static final int MOVECITYPLAYERIDS_FIELD_NUMBER = 6;
  @SuppressWarnings("serial")
  private com.google.protobuf.Internal.LongList moveCityPlayerIds_ =
      emptyLongList();
  /**
   * <code>repeated int64 moveCityPlayerIds = 6;</code>
   * @return A list containing the moveCityPlayerIds.
   */
  @java.lang.Override
  public java.util.List<java.lang.Long>
      getMoveCityPlayerIdsList() {
    return moveCityPlayerIds_;
  }
  /**
   * <code>repeated int64 moveCityPlayerIds = 6;</code>
   * @return The count of moveCityPlayerIds.
   */
  public int getMoveCityPlayerIdsCount() {
    return moveCityPlayerIds_.size();
  }
  /**
   * <code>repeated int64 moveCityPlayerIds = 6;</code>
   * @param index The index of the element to return.
   * @return The moveCityPlayerIds at the given index.
   */
  public long getMoveCityPlayerIds(int index) {
    return moveCityPlayerIds_.getLong(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, cityId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(2, roleNum_);
    }
    for (int i = 0; i < data_.size(); i++) {
      output.writeMessage(3, data_.get(i));
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeBool(4, isMoveBorn_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt64(5, unionId_);
    }
    for (int i = 0; i < moveCityPlayerIds_.size(); i++) {
      output.writeInt64(6, moveCityPlayerIds_.getLong(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, cityId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, roleNum_);
    }
    for (int i = 0; i < data_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, data_.get(i));
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(4, isMoveBorn_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(5, unionId_);
    }
    {
      int dataSize = 0;
      for (int i = 0; i < moveCityPlayerIds_.size(); i++) {
        dataSize += com.google.protobuf.CodedOutputStream
          .computeInt64SizeNoTag(moveCityPlayerIds_.getLong(i));
      }
      size += dataSize;
      size += 1 * getMoveCityPlayerIdsList().size();
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.WarSeasonMapPlayerEnterCitySyncMsg)) {
      return super.equals(obj);
    }
    xddq.pb.WarSeasonMapPlayerEnterCitySyncMsg other = (xddq.pb.WarSeasonMapPlayerEnterCitySyncMsg) obj;

    if (hasCityId() != other.hasCityId()) return false;
    if (hasCityId()) {
      if (getCityId()
          != other.getCityId()) return false;
    }
    if (hasRoleNum() != other.hasRoleNum()) return false;
    if (hasRoleNum()) {
      if (getRoleNum()
          != other.getRoleNum()) return false;
    }
    if (!getDataList()
        .equals(other.getDataList())) return false;
    if (hasIsMoveBorn() != other.hasIsMoveBorn()) return false;
    if (hasIsMoveBorn()) {
      if (getIsMoveBorn()
          != other.getIsMoveBorn()) return false;
    }
    if (hasUnionId() != other.hasUnionId()) return false;
    if (hasUnionId()) {
      if (getUnionId()
          != other.getUnionId()) return false;
    }
    if (!getMoveCityPlayerIdsList()
        .equals(other.getMoveCityPlayerIdsList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasCityId()) {
      hash = (37 * hash) + CITYID_FIELD_NUMBER;
      hash = (53 * hash) + getCityId();
    }
    if (hasRoleNum()) {
      hash = (37 * hash) + ROLENUM_FIELD_NUMBER;
      hash = (53 * hash) + getRoleNum();
    }
    if (getDataCount() > 0) {
      hash = (37 * hash) + DATA_FIELD_NUMBER;
      hash = (53 * hash) + getDataList().hashCode();
    }
    if (hasIsMoveBorn()) {
      hash = (37 * hash) + ISMOVEBORN_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getIsMoveBorn());
    }
    if (hasUnionId()) {
      hash = (37 * hash) + UNIONID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getUnionId());
    }
    if (getMoveCityPlayerIdsCount() > 0) {
      hash = (37 * hash) + MOVECITYPLAYERIDS_FIELD_NUMBER;
      hash = (53 * hash) + getMoveCityPlayerIdsList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.WarSeasonMapPlayerEnterCitySyncMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.WarSeasonMapPlayerEnterCitySyncMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.WarSeasonMapPlayerEnterCitySyncMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.WarSeasonMapPlayerEnterCitySyncMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.WarSeasonMapPlayerEnterCitySyncMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.WarSeasonMapPlayerEnterCitySyncMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.WarSeasonMapPlayerEnterCitySyncMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.WarSeasonMapPlayerEnterCitySyncMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.WarSeasonMapPlayerEnterCitySyncMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.WarSeasonMapPlayerEnterCitySyncMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.WarSeasonMapPlayerEnterCitySyncMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.WarSeasonMapPlayerEnterCitySyncMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.WarSeasonMapPlayerEnterCitySyncMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.WarSeasonMapPlayerEnterCitySyncMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.WarSeasonMapPlayerEnterCitySyncMsg)
      xddq.pb.WarSeasonMapPlayerEnterCitySyncMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_WarSeasonMapPlayerEnterCitySyncMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_WarSeasonMapPlayerEnterCitySyncMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.WarSeasonMapPlayerEnterCitySyncMsg.class, xddq.pb.WarSeasonMapPlayerEnterCitySyncMsg.Builder.class);
    }

    // Construct using xddq.pb.WarSeasonMapPlayerEnterCitySyncMsg.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      cityId_ = 0;
      roleNum_ = 0;
      if (dataBuilder_ == null) {
        data_ = java.util.Collections.emptyList();
      } else {
        data_ = null;
        dataBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000004);
      isMoveBorn_ = false;
      unionId_ = 0L;
      moveCityPlayerIds_ = emptyLongList();
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_WarSeasonMapPlayerEnterCitySyncMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.WarSeasonMapPlayerEnterCitySyncMsg getDefaultInstanceForType() {
      return xddq.pb.WarSeasonMapPlayerEnterCitySyncMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.WarSeasonMapPlayerEnterCitySyncMsg build() {
      xddq.pb.WarSeasonMapPlayerEnterCitySyncMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.WarSeasonMapPlayerEnterCitySyncMsg buildPartial() {
      xddq.pb.WarSeasonMapPlayerEnterCitySyncMsg result = new xddq.pb.WarSeasonMapPlayerEnterCitySyncMsg(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.WarSeasonMapPlayerEnterCitySyncMsg result) {
      if (dataBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0)) {
          data_ = java.util.Collections.unmodifiableList(data_);
          bitField0_ = (bitField0_ & ~0x00000004);
        }
        result.data_ = data_;
      } else {
        result.data_ = dataBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.WarSeasonMapPlayerEnterCitySyncMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.cityId_ = cityId_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.roleNum_ = roleNum_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.isMoveBorn_ = isMoveBorn_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.unionId_ = unionId_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        moveCityPlayerIds_.makeImmutable();
        result.moveCityPlayerIds_ = moveCityPlayerIds_;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.WarSeasonMapPlayerEnterCitySyncMsg) {
        return mergeFrom((xddq.pb.WarSeasonMapPlayerEnterCitySyncMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.WarSeasonMapPlayerEnterCitySyncMsg other) {
      if (other == xddq.pb.WarSeasonMapPlayerEnterCitySyncMsg.getDefaultInstance()) return this;
      if (other.hasCityId()) {
        setCityId(other.getCityId());
      }
      if (other.hasRoleNum()) {
        setRoleNum(other.getRoleNum());
      }
      if (dataBuilder_ == null) {
        if (!other.data_.isEmpty()) {
          if (data_.isEmpty()) {
            data_ = other.data_;
            bitField0_ = (bitField0_ & ~0x00000004);
          } else {
            ensureDataIsMutable();
            data_.addAll(other.data_);
          }
          onChanged();
        }
      } else {
        if (!other.data_.isEmpty()) {
          if (dataBuilder_.isEmpty()) {
            dataBuilder_.dispose();
            dataBuilder_ = null;
            data_ = other.data_;
            bitField0_ = (bitField0_ & ~0x00000004);
            dataBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetDataFieldBuilder() : null;
          } else {
            dataBuilder_.addAllMessages(other.data_);
          }
        }
      }
      if (other.hasIsMoveBorn()) {
        setIsMoveBorn(other.getIsMoveBorn());
      }
      if (other.hasUnionId()) {
        setUnionId(other.getUnionId());
      }
      if (!other.moveCityPlayerIds_.isEmpty()) {
        if (moveCityPlayerIds_.isEmpty()) {
          moveCityPlayerIds_ = other.moveCityPlayerIds_;
          moveCityPlayerIds_.makeImmutable();
          bitField0_ |= 0x00000020;
        } else {
          ensureMoveCityPlayerIdsIsMutable();
          moveCityPlayerIds_.addAll(other.moveCityPlayerIds_);
        }
        onChanged();
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              cityId_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              roleNum_ = input.readInt32();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 26: {
              xddq.pb.WarSeasonMapBattleDataMsg m =
                  input.readMessage(
                      xddq.pb.WarSeasonMapBattleDataMsg.parser(),
                      extensionRegistry);
              if (dataBuilder_ == null) {
                ensureDataIsMutable();
                data_.add(m);
              } else {
                dataBuilder_.addMessage(m);
              }
              break;
            } // case 26
            case 32: {
              isMoveBorn_ = input.readBool();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            case 40: {
              unionId_ = input.readInt64();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            case 48: {
              long v = input.readInt64();
              ensureMoveCityPlayerIdsIsMutable();
              moveCityPlayerIds_.addLong(v);
              break;
            } // case 48
            case 50: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              ensureMoveCityPlayerIdsIsMutable();
              while (input.getBytesUntilLimit() > 0) {
                moveCityPlayerIds_.addLong(input.readInt64());
              }
              input.popLimit(limit);
              break;
            } // case 50
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int cityId_ ;
    /**
     * <code>optional int32 cityId = 1;</code>
     * @return Whether the cityId field is set.
     */
    @java.lang.Override
    public boolean hasCityId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 cityId = 1;</code>
     * @return The cityId.
     */
    @java.lang.Override
    public int getCityId() {
      return cityId_;
    }
    /**
     * <code>optional int32 cityId = 1;</code>
     * @param value The cityId to set.
     * @return This builder for chaining.
     */
    public Builder setCityId(int value) {

      cityId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 cityId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearCityId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      cityId_ = 0;
      onChanged();
      return this;
    }

    private int roleNum_ ;
    /**
     * <code>optional int32 roleNum = 2;</code>
     * @return Whether the roleNum field is set.
     */
    @java.lang.Override
    public boolean hasRoleNum() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 roleNum = 2;</code>
     * @return The roleNum.
     */
    @java.lang.Override
    public int getRoleNum() {
      return roleNum_;
    }
    /**
     * <code>optional int32 roleNum = 2;</code>
     * @param value The roleNum to set.
     * @return This builder for chaining.
     */
    public Builder setRoleNum(int value) {

      roleNum_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 roleNum = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearRoleNum() {
      bitField0_ = (bitField0_ & ~0x00000002);
      roleNum_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.WarSeasonMapBattleDataMsg> data_ =
      java.util.Collections.emptyList();
    private void ensureDataIsMutable() {
      if (!((bitField0_ & 0x00000004) != 0)) {
        data_ = new java.util.ArrayList<xddq.pb.WarSeasonMapBattleDataMsg>(data_);
        bitField0_ |= 0x00000004;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.WarSeasonMapBattleDataMsg, xddq.pb.WarSeasonMapBattleDataMsg.Builder, xddq.pb.WarSeasonMapBattleDataMsgOrBuilder> dataBuilder_;

    /**
     * <code>repeated .xddq.pb.WarSeasonMapBattleDataMsg data = 3;</code>
     */
    public java.util.List<xddq.pb.WarSeasonMapBattleDataMsg> getDataList() {
      if (dataBuilder_ == null) {
        return java.util.Collections.unmodifiableList(data_);
      } else {
        return dataBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapBattleDataMsg data = 3;</code>
     */
    public int getDataCount() {
      if (dataBuilder_ == null) {
        return data_.size();
      } else {
        return dataBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapBattleDataMsg data = 3;</code>
     */
    public xddq.pb.WarSeasonMapBattleDataMsg getData(int index) {
      if (dataBuilder_ == null) {
        return data_.get(index);
      } else {
        return dataBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapBattleDataMsg data = 3;</code>
     */
    public Builder setData(
        int index, xddq.pb.WarSeasonMapBattleDataMsg value) {
      if (dataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDataIsMutable();
        data_.set(index, value);
        onChanged();
      } else {
        dataBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapBattleDataMsg data = 3;</code>
     */
    public Builder setData(
        int index, xddq.pb.WarSeasonMapBattleDataMsg.Builder builderForValue) {
      if (dataBuilder_ == null) {
        ensureDataIsMutable();
        data_.set(index, builderForValue.build());
        onChanged();
      } else {
        dataBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapBattleDataMsg data = 3;</code>
     */
    public Builder addData(xddq.pb.WarSeasonMapBattleDataMsg value) {
      if (dataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDataIsMutable();
        data_.add(value);
        onChanged();
      } else {
        dataBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapBattleDataMsg data = 3;</code>
     */
    public Builder addData(
        int index, xddq.pb.WarSeasonMapBattleDataMsg value) {
      if (dataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDataIsMutable();
        data_.add(index, value);
        onChanged();
      } else {
        dataBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapBattleDataMsg data = 3;</code>
     */
    public Builder addData(
        xddq.pb.WarSeasonMapBattleDataMsg.Builder builderForValue) {
      if (dataBuilder_ == null) {
        ensureDataIsMutable();
        data_.add(builderForValue.build());
        onChanged();
      } else {
        dataBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapBattleDataMsg data = 3;</code>
     */
    public Builder addData(
        int index, xddq.pb.WarSeasonMapBattleDataMsg.Builder builderForValue) {
      if (dataBuilder_ == null) {
        ensureDataIsMutable();
        data_.add(index, builderForValue.build());
        onChanged();
      } else {
        dataBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapBattleDataMsg data = 3;</code>
     */
    public Builder addAllData(
        java.lang.Iterable<? extends xddq.pb.WarSeasonMapBattleDataMsg> values) {
      if (dataBuilder_ == null) {
        ensureDataIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, data_);
        onChanged();
      } else {
        dataBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapBattleDataMsg data = 3;</code>
     */
    public Builder clearData() {
      if (dataBuilder_ == null) {
        data_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
      } else {
        dataBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapBattleDataMsg data = 3;</code>
     */
    public Builder removeData(int index) {
      if (dataBuilder_ == null) {
        ensureDataIsMutable();
        data_.remove(index);
        onChanged();
      } else {
        dataBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapBattleDataMsg data = 3;</code>
     */
    public xddq.pb.WarSeasonMapBattleDataMsg.Builder getDataBuilder(
        int index) {
      return internalGetDataFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapBattleDataMsg data = 3;</code>
     */
    public xddq.pb.WarSeasonMapBattleDataMsgOrBuilder getDataOrBuilder(
        int index) {
      if (dataBuilder_ == null) {
        return data_.get(index);  } else {
        return dataBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapBattleDataMsg data = 3;</code>
     */
    public java.util.List<? extends xddq.pb.WarSeasonMapBattleDataMsgOrBuilder> 
         getDataOrBuilderList() {
      if (dataBuilder_ != null) {
        return dataBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(data_);
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapBattleDataMsg data = 3;</code>
     */
    public xddq.pb.WarSeasonMapBattleDataMsg.Builder addDataBuilder() {
      return internalGetDataFieldBuilder().addBuilder(
          xddq.pb.WarSeasonMapBattleDataMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapBattleDataMsg data = 3;</code>
     */
    public xddq.pb.WarSeasonMapBattleDataMsg.Builder addDataBuilder(
        int index) {
      return internalGetDataFieldBuilder().addBuilder(
          index, xddq.pb.WarSeasonMapBattleDataMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonMapBattleDataMsg data = 3;</code>
     */
    public java.util.List<xddq.pb.WarSeasonMapBattleDataMsg.Builder> 
         getDataBuilderList() {
      return internalGetDataFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.WarSeasonMapBattleDataMsg, xddq.pb.WarSeasonMapBattleDataMsg.Builder, xddq.pb.WarSeasonMapBattleDataMsgOrBuilder> 
        internalGetDataFieldBuilder() {
      if (dataBuilder_ == null) {
        dataBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.WarSeasonMapBattleDataMsg, xddq.pb.WarSeasonMapBattleDataMsg.Builder, xddq.pb.WarSeasonMapBattleDataMsgOrBuilder>(
                data_,
                ((bitField0_ & 0x00000004) != 0),
                getParentForChildren(),
                isClean());
        data_ = null;
      }
      return dataBuilder_;
    }

    private boolean isMoveBorn_ ;
    /**
     * <code>optional bool isMoveBorn = 4;</code>
     * @return Whether the isMoveBorn field is set.
     */
    @java.lang.Override
    public boolean hasIsMoveBorn() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional bool isMoveBorn = 4;</code>
     * @return The isMoveBorn.
     */
    @java.lang.Override
    public boolean getIsMoveBorn() {
      return isMoveBorn_;
    }
    /**
     * <code>optional bool isMoveBorn = 4;</code>
     * @param value The isMoveBorn to set.
     * @return This builder for chaining.
     */
    public Builder setIsMoveBorn(boolean value) {

      isMoveBorn_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional bool isMoveBorn = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearIsMoveBorn() {
      bitField0_ = (bitField0_ & ~0x00000008);
      isMoveBorn_ = false;
      onChanged();
      return this;
    }

    private long unionId_ ;
    /**
     * <code>optional int64 unionId = 5;</code>
     * @return Whether the unionId field is set.
     */
    @java.lang.Override
    public boolean hasUnionId() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional int64 unionId = 5;</code>
     * @return The unionId.
     */
    @java.lang.Override
    public long getUnionId() {
      return unionId_;
    }
    /**
     * <code>optional int64 unionId = 5;</code>
     * @param value The unionId to set.
     * @return This builder for chaining.
     */
    public Builder setUnionId(long value) {

      unionId_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 unionId = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearUnionId() {
      bitField0_ = (bitField0_ & ~0x00000010);
      unionId_ = 0L;
      onChanged();
      return this;
    }

    private com.google.protobuf.Internal.LongList moveCityPlayerIds_ = emptyLongList();
    private void ensureMoveCityPlayerIdsIsMutable() {
      if (!moveCityPlayerIds_.isModifiable()) {
        moveCityPlayerIds_ = makeMutableCopy(moveCityPlayerIds_);
      }
      bitField0_ |= 0x00000020;
    }
    /**
     * <code>repeated int64 moveCityPlayerIds = 6;</code>
     * @return A list containing the moveCityPlayerIds.
     */
    public java.util.List<java.lang.Long>
        getMoveCityPlayerIdsList() {
      moveCityPlayerIds_.makeImmutable();
      return moveCityPlayerIds_;
    }
    /**
     * <code>repeated int64 moveCityPlayerIds = 6;</code>
     * @return The count of moveCityPlayerIds.
     */
    public int getMoveCityPlayerIdsCount() {
      return moveCityPlayerIds_.size();
    }
    /**
     * <code>repeated int64 moveCityPlayerIds = 6;</code>
     * @param index The index of the element to return.
     * @return The moveCityPlayerIds at the given index.
     */
    public long getMoveCityPlayerIds(int index) {
      return moveCityPlayerIds_.getLong(index);
    }
    /**
     * <code>repeated int64 moveCityPlayerIds = 6;</code>
     * @param index The index to set the value at.
     * @param value The moveCityPlayerIds to set.
     * @return This builder for chaining.
     */
    public Builder setMoveCityPlayerIds(
        int index, long value) {

      ensureMoveCityPlayerIdsIsMutable();
      moveCityPlayerIds_.setLong(index, value);
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int64 moveCityPlayerIds = 6;</code>
     * @param value The moveCityPlayerIds to add.
     * @return This builder for chaining.
     */
    public Builder addMoveCityPlayerIds(long value) {

      ensureMoveCityPlayerIdsIsMutable();
      moveCityPlayerIds_.addLong(value);
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int64 moveCityPlayerIds = 6;</code>
     * @param values The moveCityPlayerIds to add.
     * @return This builder for chaining.
     */
    public Builder addAllMoveCityPlayerIds(
        java.lang.Iterable<? extends java.lang.Long> values) {
      ensureMoveCityPlayerIdsIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, moveCityPlayerIds_);
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int64 moveCityPlayerIds = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearMoveCityPlayerIds() {
      moveCityPlayerIds_ = emptyLongList();
      bitField0_ = (bitField0_ & ~0x00000020);
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.WarSeasonMapPlayerEnterCitySyncMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.WarSeasonMapPlayerEnterCitySyncMsg)
  private static final xddq.pb.WarSeasonMapPlayerEnterCitySyncMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.WarSeasonMapPlayerEnterCitySyncMsg();
  }

  public static xddq.pb.WarSeasonMapPlayerEnterCitySyncMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<WarSeasonMapPlayerEnterCitySyncMsg>
      PARSER = new com.google.protobuf.AbstractParser<WarSeasonMapPlayerEnterCitySyncMsg>() {
    @java.lang.Override
    public WarSeasonMapPlayerEnterCitySyncMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<WarSeasonMapPlayerEnterCitySyncMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<WarSeasonMapPlayerEnterCitySyncMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.WarSeasonMapPlayerEnterCitySyncMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

