// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

public interface PharmacyTableRespMsgOrBuilder extends
    // @@protoc_insertion_point(interface_extends:xddq.pb.PharmacyTableRespMsg)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  boolean hasRet();
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  int getRet();

  /**
   * <code>optional .xddq.pb.PharmacySelfTableSyncMsg selfTable = 2;</code>
   * @return Whether the selfTable field is set.
   */
  boolean hasSelfTable();
  /**
   * <code>optional .xddq.pb.PharmacySelfTableSyncMsg selfTable = 2;</code>
   * @return The selfTable.
   */
  xddq.pb.PharmacySelfTableSyncMsg getSelfTable();
  /**
   * <code>optional .xddq.pb.PharmacySelfTableSyncMsg selfTable = 2;</code>
   */
  xddq.pb.PharmacySelfTableSyncMsgOrBuilder getSelfTableOrBuilder();
}
