// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.HeavenBattleReportResp}
 */
public final class HeavenBattleReportResp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.HeavenBattleReportResp)
    HeavenBattleReportRespOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      HeavenBattleReportResp.class.getName());
  }
  // Use HeavenBattleReportResp.newBuilder() to construct.
  private HeavenBattleReportResp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private HeavenBattleReportResp() {
    report_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_HeavenBattleReportResp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_HeavenBattleReportResp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.HeavenBattleReportResp.class, xddq.pb.HeavenBattleReportResp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>optional int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int REPORT_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.HeavenBattleReportDataMsg> report_;
  /**
   * <code>repeated .xddq.pb.HeavenBattleReportDataMsg report = 2;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.HeavenBattleReportDataMsg> getReportList() {
    return report_;
  }
  /**
   * <code>repeated .xddq.pb.HeavenBattleReportDataMsg report = 2;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.HeavenBattleReportDataMsgOrBuilder> 
      getReportOrBuilderList() {
    return report_;
  }
  /**
   * <code>repeated .xddq.pb.HeavenBattleReportDataMsg report = 2;</code>
   */
  @java.lang.Override
  public int getReportCount() {
    return report_.size();
  }
  /**
   * <code>repeated .xddq.pb.HeavenBattleReportDataMsg report = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.HeavenBattleReportDataMsg getReport(int index) {
    return report_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.HeavenBattleReportDataMsg report = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.HeavenBattleReportDataMsgOrBuilder getReportOrBuilder(
      int index) {
    return report_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    for (int i = 0; i < report_.size(); i++) {
      output.writeMessage(2, report_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    for (int i = 0; i < report_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, report_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.HeavenBattleReportResp)) {
      return super.equals(obj);
    }
    xddq.pb.HeavenBattleReportResp other = (xddq.pb.HeavenBattleReportResp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (!getReportList()
        .equals(other.getReportList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (getReportCount() > 0) {
      hash = (37 * hash) + REPORT_FIELD_NUMBER;
      hash = (53 * hash) + getReportList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.HeavenBattleReportResp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.HeavenBattleReportResp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.HeavenBattleReportResp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.HeavenBattleReportResp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.HeavenBattleReportResp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.HeavenBattleReportResp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.HeavenBattleReportResp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.HeavenBattleReportResp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.HeavenBattleReportResp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.HeavenBattleReportResp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.HeavenBattleReportResp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.HeavenBattleReportResp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.HeavenBattleReportResp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.HeavenBattleReportResp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.HeavenBattleReportResp)
      xddq.pb.HeavenBattleReportRespOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_HeavenBattleReportResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_HeavenBattleReportResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.HeavenBattleReportResp.class, xddq.pb.HeavenBattleReportResp.Builder.class);
    }

    // Construct using xddq.pb.HeavenBattleReportResp.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      if (reportBuilder_ == null) {
        report_ = java.util.Collections.emptyList();
      } else {
        report_ = null;
        reportBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000002);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_HeavenBattleReportResp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.HeavenBattleReportResp getDefaultInstanceForType() {
      return xddq.pb.HeavenBattleReportResp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.HeavenBattleReportResp build() {
      xddq.pb.HeavenBattleReportResp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.HeavenBattleReportResp buildPartial() {
      xddq.pb.HeavenBattleReportResp result = new xddq.pb.HeavenBattleReportResp(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.HeavenBattleReportResp result) {
      if (reportBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          report_ = java.util.Collections.unmodifiableList(report_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.report_ = report_;
      } else {
        result.report_ = reportBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.HeavenBattleReportResp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.HeavenBattleReportResp) {
        return mergeFrom((xddq.pb.HeavenBattleReportResp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.HeavenBattleReportResp other) {
      if (other == xddq.pb.HeavenBattleReportResp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (reportBuilder_ == null) {
        if (!other.report_.isEmpty()) {
          if (report_.isEmpty()) {
            report_ = other.report_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureReportIsMutable();
            report_.addAll(other.report_);
          }
          onChanged();
        }
      } else {
        if (!other.report_.isEmpty()) {
          if (reportBuilder_.isEmpty()) {
            reportBuilder_.dispose();
            reportBuilder_ = null;
            report_ = other.report_;
            bitField0_ = (bitField0_ & ~0x00000002);
            reportBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetReportFieldBuilder() : null;
          } else {
            reportBuilder_.addAllMessages(other.report_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              xddq.pb.HeavenBattleReportDataMsg m =
                  input.readMessage(
                      xddq.pb.HeavenBattleReportDataMsg.parser(),
                      extensionRegistry);
              if (reportBuilder_ == null) {
                ensureReportIsMutable();
                report_.add(m);
              } else {
                reportBuilder_.addMessage(m);
              }
              break;
            } // case 18
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>optional int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>optional int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.HeavenBattleReportDataMsg> report_ =
      java.util.Collections.emptyList();
    private void ensureReportIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        report_ = new java.util.ArrayList<xddq.pb.HeavenBattleReportDataMsg>(report_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.HeavenBattleReportDataMsg, xddq.pb.HeavenBattleReportDataMsg.Builder, xddq.pb.HeavenBattleReportDataMsgOrBuilder> reportBuilder_;

    /**
     * <code>repeated .xddq.pb.HeavenBattleReportDataMsg report = 2;</code>
     */
    public java.util.List<xddq.pb.HeavenBattleReportDataMsg> getReportList() {
      if (reportBuilder_ == null) {
        return java.util.Collections.unmodifiableList(report_);
      } else {
        return reportBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleReportDataMsg report = 2;</code>
     */
    public int getReportCount() {
      if (reportBuilder_ == null) {
        return report_.size();
      } else {
        return reportBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleReportDataMsg report = 2;</code>
     */
    public xddq.pb.HeavenBattleReportDataMsg getReport(int index) {
      if (reportBuilder_ == null) {
        return report_.get(index);
      } else {
        return reportBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleReportDataMsg report = 2;</code>
     */
    public Builder setReport(
        int index, xddq.pb.HeavenBattleReportDataMsg value) {
      if (reportBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureReportIsMutable();
        report_.set(index, value);
        onChanged();
      } else {
        reportBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleReportDataMsg report = 2;</code>
     */
    public Builder setReport(
        int index, xddq.pb.HeavenBattleReportDataMsg.Builder builderForValue) {
      if (reportBuilder_ == null) {
        ensureReportIsMutable();
        report_.set(index, builderForValue.build());
        onChanged();
      } else {
        reportBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleReportDataMsg report = 2;</code>
     */
    public Builder addReport(xddq.pb.HeavenBattleReportDataMsg value) {
      if (reportBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureReportIsMutable();
        report_.add(value);
        onChanged();
      } else {
        reportBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleReportDataMsg report = 2;</code>
     */
    public Builder addReport(
        int index, xddq.pb.HeavenBattleReportDataMsg value) {
      if (reportBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureReportIsMutable();
        report_.add(index, value);
        onChanged();
      } else {
        reportBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleReportDataMsg report = 2;</code>
     */
    public Builder addReport(
        xddq.pb.HeavenBattleReportDataMsg.Builder builderForValue) {
      if (reportBuilder_ == null) {
        ensureReportIsMutable();
        report_.add(builderForValue.build());
        onChanged();
      } else {
        reportBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleReportDataMsg report = 2;</code>
     */
    public Builder addReport(
        int index, xddq.pb.HeavenBattleReportDataMsg.Builder builderForValue) {
      if (reportBuilder_ == null) {
        ensureReportIsMutable();
        report_.add(index, builderForValue.build());
        onChanged();
      } else {
        reportBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleReportDataMsg report = 2;</code>
     */
    public Builder addAllReport(
        java.lang.Iterable<? extends xddq.pb.HeavenBattleReportDataMsg> values) {
      if (reportBuilder_ == null) {
        ensureReportIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, report_);
        onChanged();
      } else {
        reportBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleReportDataMsg report = 2;</code>
     */
    public Builder clearReport() {
      if (reportBuilder_ == null) {
        report_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        reportBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleReportDataMsg report = 2;</code>
     */
    public Builder removeReport(int index) {
      if (reportBuilder_ == null) {
        ensureReportIsMutable();
        report_.remove(index);
        onChanged();
      } else {
        reportBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleReportDataMsg report = 2;</code>
     */
    public xddq.pb.HeavenBattleReportDataMsg.Builder getReportBuilder(
        int index) {
      return internalGetReportFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleReportDataMsg report = 2;</code>
     */
    public xddq.pb.HeavenBattleReportDataMsgOrBuilder getReportOrBuilder(
        int index) {
      if (reportBuilder_ == null) {
        return report_.get(index);  } else {
        return reportBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleReportDataMsg report = 2;</code>
     */
    public java.util.List<? extends xddq.pb.HeavenBattleReportDataMsgOrBuilder> 
         getReportOrBuilderList() {
      if (reportBuilder_ != null) {
        return reportBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(report_);
      }
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleReportDataMsg report = 2;</code>
     */
    public xddq.pb.HeavenBattleReportDataMsg.Builder addReportBuilder() {
      return internalGetReportFieldBuilder().addBuilder(
          xddq.pb.HeavenBattleReportDataMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleReportDataMsg report = 2;</code>
     */
    public xddq.pb.HeavenBattleReportDataMsg.Builder addReportBuilder(
        int index) {
      return internalGetReportFieldBuilder().addBuilder(
          index, xddq.pb.HeavenBattleReportDataMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleReportDataMsg report = 2;</code>
     */
    public java.util.List<xddq.pb.HeavenBattleReportDataMsg.Builder> 
         getReportBuilderList() {
      return internalGetReportFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.HeavenBattleReportDataMsg, xddq.pb.HeavenBattleReportDataMsg.Builder, xddq.pb.HeavenBattleReportDataMsgOrBuilder> 
        internalGetReportFieldBuilder() {
      if (reportBuilder_ == null) {
        reportBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.HeavenBattleReportDataMsg, xddq.pb.HeavenBattleReportDataMsg.Builder, xddq.pb.HeavenBattleReportDataMsgOrBuilder>(
                report_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        report_ = null;
      }
      return reportBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.HeavenBattleReportResp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.HeavenBattleReportResp)
  private static final xddq.pb.HeavenBattleReportResp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.HeavenBattleReportResp();
  }

  public static xddq.pb.HeavenBattleReportResp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<HeavenBattleReportResp>
      PARSER = new com.google.protobuf.AbstractParser<HeavenBattleReportResp>() {
    @java.lang.Override
    public HeavenBattleReportResp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<HeavenBattleReportResp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<HeavenBattleReportResp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.HeavenBattleReportResp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

