// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.GroupPurchaseSearchTeamResp}
 */
public final class GroupPurchaseSearchTeamResp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.GroupPurchaseSearchTeamResp)
    GroupPurchaseSearchTeamRespOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      GroupPurchaseSearchTeamResp.class.getName());
  }
  // Use GroupPurchaseSearchTeamResp.newBuilder() to construct.
  private GroupPurchaseSearchTeamResp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private GroupPurchaseSearchTeamResp() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_GroupPurchaseSearchTeamResp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_GroupPurchaseSearchTeamResp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.GroupPurchaseSearchTeamResp.class, xddq.pb.GroupPurchaseSearchTeamResp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int ORDERMSG_FIELD_NUMBER = 2;
  private xddq.pb.GroupPurchaseOrderMsg orderMsg_;
  /**
   * <code>optional .xddq.pb.GroupPurchaseOrderMsg orderMsg = 2;</code>
   * @return Whether the orderMsg field is set.
   */
  @java.lang.Override
  public boolean hasOrderMsg() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional .xddq.pb.GroupPurchaseOrderMsg orderMsg = 2;</code>
   * @return The orderMsg.
   */
  @java.lang.Override
  public xddq.pb.GroupPurchaseOrderMsg getOrderMsg() {
    return orderMsg_ == null ? xddq.pb.GroupPurchaseOrderMsg.getDefaultInstance() : orderMsg_;
  }
  /**
   * <code>optional .xddq.pb.GroupPurchaseOrderMsg orderMsg = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.GroupPurchaseOrderMsgOrBuilder getOrderMsgOrBuilder() {
    return orderMsg_ == null ? xddq.pb.GroupPurchaseOrderMsg.getDefaultInstance() : orderMsg_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeMessage(2, getOrderMsg());
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getOrderMsg());
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.GroupPurchaseSearchTeamResp)) {
      return super.equals(obj);
    }
    xddq.pb.GroupPurchaseSearchTeamResp other = (xddq.pb.GroupPurchaseSearchTeamResp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (hasOrderMsg() != other.hasOrderMsg()) return false;
    if (hasOrderMsg()) {
      if (!getOrderMsg()
          .equals(other.getOrderMsg())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (hasOrderMsg()) {
      hash = (37 * hash) + ORDERMSG_FIELD_NUMBER;
      hash = (53 * hash) + getOrderMsg().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.GroupPurchaseSearchTeamResp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GroupPurchaseSearchTeamResp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GroupPurchaseSearchTeamResp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GroupPurchaseSearchTeamResp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GroupPurchaseSearchTeamResp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GroupPurchaseSearchTeamResp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GroupPurchaseSearchTeamResp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.GroupPurchaseSearchTeamResp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.GroupPurchaseSearchTeamResp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.GroupPurchaseSearchTeamResp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.GroupPurchaseSearchTeamResp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.GroupPurchaseSearchTeamResp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.GroupPurchaseSearchTeamResp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.GroupPurchaseSearchTeamResp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.GroupPurchaseSearchTeamResp)
      xddq.pb.GroupPurchaseSearchTeamRespOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GroupPurchaseSearchTeamResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GroupPurchaseSearchTeamResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.GroupPurchaseSearchTeamResp.class, xddq.pb.GroupPurchaseSearchTeamResp.Builder.class);
    }

    // Construct using xddq.pb.GroupPurchaseSearchTeamResp.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetOrderMsgFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      orderMsg_ = null;
      if (orderMsgBuilder_ != null) {
        orderMsgBuilder_.dispose();
        orderMsgBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GroupPurchaseSearchTeamResp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.GroupPurchaseSearchTeamResp getDefaultInstanceForType() {
      return xddq.pb.GroupPurchaseSearchTeamResp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.GroupPurchaseSearchTeamResp build() {
      xddq.pb.GroupPurchaseSearchTeamResp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.GroupPurchaseSearchTeamResp buildPartial() {
      xddq.pb.GroupPurchaseSearchTeamResp result = new xddq.pb.GroupPurchaseSearchTeamResp(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.GroupPurchaseSearchTeamResp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.orderMsg_ = orderMsgBuilder_ == null
            ? orderMsg_
            : orderMsgBuilder_.build();
        to_bitField0_ |= 0x00000002;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.GroupPurchaseSearchTeamResp) {
        return mergeFrom((xddq.pb.GroupPurchaseSearchTeamResp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.GroupPurchaseSearchTeamResp other) {
      if (other == xddq.pb.GroupPurchaseSearchTeamResp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (other.hasOrderMsg()) {
        mergeOrderMsg(other.getOrderMsg());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              input.readMessage(
                  internalGetOrderMsgFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private xddq.pb.GroupPurchaseOrderMsg orderMsg_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.GroupPurchaseOrderMsg, xddq.pb.GroupPurchaseOrderMsg.Builder, xddq.pb.GroupPurchaseOrderMsgOrBuilder> orderMsgBuilder_;
    /**
     * <code>optional .xddq.pb.GroupPurchaseOrderMsg orderMsg = 2;</code>
     * @return Whether the orderMsg field is set.
     */
    public boolean hasOrderMsg() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .xddq.pb.GroupPurchaseOrderMsg orderMsg = 2;</code>
     * @return The orderMsg.
     */
    public xddq.pb.GroupPurchaseOrderMsg getOrderMsg() {
      if (orderMsgBuilder_ == null) {
        return orderMsg_ == null ? xddq.pb.GroupPurchaseOrderMsg.getDefaultInstance() : orderMsg_;
      } else {
        return orderMsgBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.GroupPurchaseOrderMsg orderMsg = 2;</code>
     */
    public Builder setOrderMsg(xddq.pb.GroupPurchaseOrderMsg value) {
      if (orderMsgBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        orderMsg_ = value;
      } else {
        orderMsgBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.GroupPurchaseOrderMsg orderMsg = 2;</code>
     */
    public Builder setOrderMsg(
        xddq.pb.GroupPurchaseOrderMsg.Builder builderForValue) {
      if (orderMsgBuilder_ == null) {
        orderMsg_ = builderForValue.build();
      } else {
        orderMsgBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.GroupPurchaseOrderMsg orderMsg = 2;</code>
     */
    public Builder mergeOrderMsg(xddq.pb.GroupPurchaseOrderMsg value) {
      if (orderMsgBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0) &&
          orderMsg_ != null &&
          orderMsg_ != xddq.pb.GroupPurchaseOrderMsg.getDefaultInstance()) {
          getOrderMsgBuilder().mergeFrom(value);
        } else {
          orderMsg_ = value;
        }
      } else {
        orderMsgBuilder_.mergeFrom(value);
      }
      if (orderMsg_ != null) {
        bitField0_ |= 0x00000002;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.GroupPurchaseOrderMsg orderMsg = 2;</code>
     */
    public Builder clearOrderMsg() {
      bitField0_ = (bitField0_ & ~0x00000002);
      orderMsg_ = null;
      if (orderMsgBuilder_ != null) {
        orderMsgBuilder_.dispose();
        orderMsgBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.GroupPurchaseOrderMsg orderMsg = 2;</code>
     */
    public xddq.pb.GroupPurchaseOrderMsg.Builder getOrderMsgBuilder() {
      bitField0_ |= 0x00000002;
      onChanged();
      return internalGetOrderMsgFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.GroupPurchaseOrderMsg orderMsg = 2;</code>
     */
    public xddq.pb.GroupPurchaseOrderMsgOrBuilder getOrderMsgOrBuilder() {
      if (orderMsgBuilder_ != null) {
        return orderMsgBuilder_.getMessageOrBuilder();
      } else {
        return orderMsg_ == null ?
            xddq.pb.GroupPurchaseOrderMsg.getDefaultInstance() : orderMsg_;
      }
    }
    /**
     * <code>optional .xddq.pb.GroupPurchaseOrderMsg orderMsg = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.GroupPurchaseOrderMsg, xddq.pb.GroupPurchaseOrderMsg.Builder, xddq.pb.GroupPurchaseOrderMsgOrBuilder> 
        internalGetOrderMsgFieldBuilder() {
      if (orderMsgBuilder_ == null) {
        orderMsgBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.GroupPurchaseOrderMsg, xddq.pb.GroupPurchaseOrderMsg.Builder, xddq.pb.GroupPurchaseOrderMsgOrBuilder>(
                getOrderMsg(),
                getParentForChildren(),
                isClean());
        orderMsg_ = null;
      }
      return orderMsgBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.GroupPurchaseSearchTeamResp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.GroupPurchaseSearchTeamResp)
  private static final xddq.pb.GroupPurchaseSearchTeamResp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.GroupPurchaseSearchTeamResp();
  }

  public static xddq.pb.GroupPurchaseSearchTeamResp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<GroupPurchaseSearchTeamResp>
      PARSER = new com.google.protobuf.AbstractParser<GroupPurchaseSearchTeamResp>() {
    @java.lang.Override
    public GroupPurchaseSearchTeamResp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<GroupPurchaseSearchTeamResp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<GroupPurchaseSearchTeamResp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.GroupPurchaseSearchTeamResp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

