// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

public interface UnionAreaWarPlayerInfoOrBuilder extends
    // @@protoc_insertion_point(interface_extends:xddq.pb.UnionAreaWarPlayerInfo)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>optional int32 energy = 1;</code>
   * @return Whether the energy field is set.
   */
  boolean hasEnergy();
  /**
   * <code>optional int32 energy = 1;</code>
   * @return The energy.
   */
  int getEnergy();

  /**
   * <code>optional int64 lastRecoveryEnergyTime = 2;</code>
   * @return Whether the lastRecoveryEnergyTime field is set.
   */
  boolean hasLastRecoveryEnergyTime();
  /**
   * <code>optional int64 lastRecoveryEnergyTime = 2;</code>
   * @return The lastRecoveryEnergyTime.
   */
  long getLastRecoveryEnergyTime();

  /**
   * <code>optional string score = 3;</code>
   * @return Whether the score field is set.
   */
  boolean hasScore();
  /**
   * <code>optional string score = 3;</code>
   * @return The score.
   */
  java.lang.String getScore();
  /**
   * <code>optional string score = 3;</code>
   * @return The bytes for score.
   */
  com.google.protobuf.ByteString
      getScoreBytes();

  /**
   * <code>optional int32 attackCount = 4;</code>
   * @return Whether the attackCount field is set.
   */
  boolean hasAttackCount();
  /**
   * <code>optional int32 attackCount = 4;</code>
   * @return The attackCount.
   */
  int getAttackCount();

  /**
   * <code>optional int32 buyEnergyCount = 5;</code>
   * @return Whether the buyEnergyCount field is set.
   */
  boolean hasBuyEnergyCount();
  /**
   * <code>optional int32 buyEnergyCount = 5;</code>
   * @return The buyEnergyCount.
   */
  int getBuyEnergyCount();
}
