// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.MagicTreasureDrawRsp}
 */
public final class MagicTreasureDrawRsp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.MagicTreasureDrawRsp)
    MagicTreasureDrawRspOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      MagicTreasureDrawRsp.class.getName());
  }
  // Use MagicTreasureDrawRsp.newBuilder() to construct.
  private MagicTreasureDrawRsp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private MagicTreasureDrawRsp() {
    rewards_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_MagicTreasureDrawRsp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_MagicTreasureDrawRsp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.MagicTreasureDrawRsp.class, xddq.pb.MagicTreasureDrawRsp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int REWARDS_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.MagicTreasureResultItemMsg> rewards_;
  /**
   * <code>repeated .xddq.pb.MagicTreasureResultItemMsg rewards = 2;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.MagicTreasureResultItemMsg> getRewardsList() {
    return rewards_;
  }
  /**
   * <code>repeated .xddq.pb.MagicTreasureResultItemMsg rewards = 2;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.MagicTreasureResultItemMsgOrBuilder> 
      getRewardsOrBuilderList() {
    return rewards_;
  }
  /**
   * <code>repeated .xddq.pb.MagicTreasureResultItemMsg rewards = 2;</code>
   */
  @java.lang.Override
  public int getRewardsCount() {
    return rewards_.size();
  }
  /**
   * <code>repeated .xddq.pb.MagicTreasureResultItemMsg rewards = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.MagicTreasureResultItemMsg getRewards(int index) {
    return rewards_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.MagicTreasureResultItemMsg rewards = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.MagicTreasureResultItemMsgOrBuilder getRewardsOrBuilder(
      int index) {
    return rewards_.get(index);
  }

  public static final int SYNCMSG_FIELD_NUMBER = 3;
  private xddq.pb.MagicTreasurePlayerDataMsg syncMsg_;
  /**
   * <code>optional .xddq.pb.MagicTreasurePlayerDataMsg syncMsg = 3;</code>
   * @return Whether the syncMsg field is set.
   */
  @java.lang.Override
  public boolean hasSyncMsg() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional .xddq.pb.MagicTreasurePlayerDataMsg syncMsg = 3;</code>
   * @return The syncMsg.
   */
  @java.lang.Override
  public xddq.pb.MagicTreasurePlayerDataMsg getSyncMsg() {
    return syncMsg_ == null ? xddq.pb.MagicTreasurePlayerDataMsg.getDefaultInstance() : syncMsg_;
  }
  /**
   * <code>optional .xddq.pb.MagicTreasurePlayerDataMsg syncMsg = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.MagicTreasurePlayerDataMsgOrBuilder getSyncMsgOrBuilder() {
    return syncMsg_ == null ? xddq.pb.MagicTreasurePlayerDataMsg.getDefaultInstance() : syncMsg_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    for (int i = 0; i < getRewardsCount(); i++) {
      if (!getRewards(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    if (hasSyncMsg()) {
      if (!getSyncMsg().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    for (int i = 0; i < rewards_.size(); i++) {
      output.writeMessage(2, rewards_.get(i));
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeMessage(3, getSyncMsg());
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    for (int i = 0; i < rewards_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, rewards_.get(i));
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, getSyncMsg());
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.MagicTreasureDrawRsp)) {
      return super.equals(obj);
    }
    xddq.pb.MagicTreasureDrawRsp other = (xddq.pb.MagicTreasureDrawRsp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (!getRewardsList()
        .equals(other.getRewardsList())) return false;
    if (hasSyncMsg() != other.hasSyncMsg()) return false;
    if (hasSyncMsg()) {
      if (!getSyncMsg()
          .equals(other.getSyncMsg())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (getRewardsCount() > 0) {
      hash = (37 * hash) + REWARDS_FIELD_NUMBER;
      hash = (53 * hash) + getRewardsList().hashCode();
    }
    if (hasSyncMsg()) {
      hash = (37 * hash) + SYNCMSG_FIELD_NUMBER;
      hash = (53 * hash) + getSyncMsg().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.MagicTreasureDrawRsp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.MagicTreasureDrawRsp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.MagicTreasureDrawRsp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.MagicTreasureDrawRsp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.MagicTreasureDrawRsp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.MagicTreasureDrawRsp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.MagicTreasureDrawRsp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.MagicTreasureDrawRsp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.MagicTreasureDrawRsp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.MagicTreasureDrawRsp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.MagicTreasureDrawRsp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.MagicTreasureDrawRsp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.MagicTreasureDrawRsp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.MagicTreasureDrawRsp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.MagicTreasureDrawRsp)
      xddq.pb.MagicTreasureDrawRspOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_MagicTreasureDrawRsp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_MagicTreasureDrawRsp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.MagicTreasureDrawRsp.class, xddq.pb.MagicTreasureDrawRsp.Builder.class);
    }

    // Construct using xddq.pb.MagicTreasureDrawRsp.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetRewardsFieldBuilder();
        internalGetSyncMsgFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      if (rewardsBuilder_ == null) {
        rewards_ = java.util.Collections.emptyList();
      } else {
        rewards_ = null;
        rewardsBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000002);
      syncMsg_ = null;
      if (syncMsgBuilder_ != null) {
        syncMsgBuilder_.dispose();
        syncMsgBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_MagicTreasureDrawRsp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.MagicTreasureDrawRsp getDefaultInstanceForType() {
      return xddq.pb.MagicTreasureDrawRsp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.MagicTreasureDrawRsp build() {
      xddq.pb.MagicTreasureDrawRsp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.MagicTreasureDrawRsp buildPartial() {
      xddq.pb.MagicTreasureDrawRsp result = new xddq.pb.MagicTreasureDrawRsp(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.MagicTreasureDrawRsp result) {
      if (rewardsBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          rewards_ = java.util.Collections.unmodifiableList(rewards_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.rewards_ = rewards_;
      } else {
        result.rewards_ = rewardsBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.MagicTreasureDrawRsp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.syncMsg_ = syncMsgBuilder_ == null
            ? syncMsg_
            : syncMsgBuilder_.build();
        to_bitField0_ |= 0x00000002;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.MagicTreasureDrawRsp) {
        return mergeFrom((xddq.pb.MagicTreasureDrawRsp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.MagicTreasureDrawRsp other) {
      if (other == xddq.pb.MagicTreasureDrawRsp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (rewardsBuilder_ == null) {
        if (!other.rewards_.isEmpty()) {
          if (rewards_.isEmpty()) {
            rewards_ = other.rewards_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureRewardsIsMutable();
            rewards_.addAll(other.rewards_);
          }
          onChanged();
        }
      } else {
        if (!other.rewards_.isEmpty()) {
          if (rewardsBuilder_.isEmpty()) {
            rewardsBuilder_.dispose();
            rewardsBuilder_ = null;
            rewards_ = other.rewards_;
            bitField0_ = (bitField0_ & ~0x00000002);
            rewardsBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetRewardsFieldBuilder() : null;
          } else {
            rewardsBuilder_.addAllMessages(other.rewards_);
          }
        }
      }
      if (other.hasSyncMsg()) {
        mergeSyncMsg(other.getSyncMsg());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      for (int i = 0; i < getRewardsCount(); i++) {
        if (!getRewards(i).isInitialized()) {
          return false;
        }
      }
      if (hasSyncMsg()) {
        if (!getSyncMsg().isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              xddq.pb.MagicTreasureResultItemMsg m =
                  input.readMessage(
                      xddq.pb.MagicTreasureResultItemMsg.parser(),
                      extensionRegistry);
              if (rewardsBuilder_ == null) {
                ensureRewardsIsMutable();
                rewards_.add(m);
              } else {
                rewardsBuilder_.addMessage(m);
              }
              break;
            } // case 18
            case 26: {
              input.readMessage(
                  internalGetSyncMsgFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.MagicTreasureResultItemMsg> rewards_ =
      java.util.Collections.emptyList();
    private void ensureRewardsIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        rewards_ = new java.util.ArrayList<xddq.pb.MagicTreasureResultItemMsg>(rewards_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.MagicTreasureResultItemMsg, xddq.pb.MagicTreasureResultItemMsg.Builder, xddq.pb.MagicTreasureResultItemMsgOrBuilder> rewardsBuilder_;

    /**
     * <code>repeated .xddq.pb.MagicTreasureResultItemMsg rewards = 2;</code>
     */
    public java.util.List<xddq.pb.MagicTreasureResultItemMsg> getRewardsList() {
      if (rewardsBuilder_ == null) {
        return java.util.Collections.unmodifiableList(rewards_);
      } else {
        return rewardsBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.MagicTreasureResultItemMsg rewards = 2;</code>
     */
    public int getRewardsCount() {
      if (rewardsBuilder_ == null) {
        return rewards_.size();
      } else {
        return rewardsBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.MagicTreasureResultItemMsg rewards = 2;</code>
     */
    public xddq.pb.MagicTreasureResultItemMsg getRewards(int index) {
      if (rewardsBuilder_ == null) {
        return rewards_.get(index);
      } else {
        return rewardsBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.MagicTreasureResultItemMsg rewards = 2;</code>
     */
    public Builder setRewards(
        int index, xddq.pb.MagicTreasureResultItemMsg value) {
      if (rewardsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRewardsIsMutable();
        rewards_.set(index, value);
        onChanged();
      } else {
        rewardsBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MagicTreasureResultItemMsg rewards = 2;</code>
     */
    public Builder setRewards(
        int index, xddq.pb.MagicTreasureResultItemMsg.Builder builderForValue) {
      if (rewardsBuilder_ == null) {
        ensureRewardsIsMutable();
        rewards_.set(index, builderForValue.build());
        onChanged();
      } else {
        rewardsBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MagicTreasureResultItemMsg rewards = 2;</code>
     */
    public Builder addRewards(xddq.pb.MagicTreasureResultItemMsg value) {
      if (rewardsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRewardsIsMutable();
        rewards_.add(value);
        onChanged();
      } else {
        rewardsBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MagicTreasureResultItemMsg rewards = 2;</code>
     */
    public Builder addRewards(
        int index, xddq.pb.MagicTreasureResultItemMsg value) {
      if (rewardsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRewardsIsMutable();
        rewards_.add(index, value);
        onChanged();
      } else {
        rewardsBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MagicTreasureResultItemMsg rewards = 2;</code>
     */
    public Builder addRewards(
        xddq.pb.MagicTreasureResultItemMsg.Builder builderForValue) {
      if (rewardsBuilder_ == null) {
        ensureRewardsIsMutable();
        rewards_.add(builderForValue.build());
        onChanged();
      } else {
        rewardsBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MagicTreasureResultItemMsg rewards = 2;</code>
     */
    public Builder addRewards(
        int index, xddq.pb.MagicTreasureResultItemMsg.Builder builderForValue) {
      if (rewardsBuilder_ == null) {
        ensureRewardsIsMutable();
        rewards_.add(index, builderForValue.build());
        onChanged();
      } else {
        rewardsBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MagicTreasureResultItemMsg rewards = 2;</code>
     */
    public Builder addAllRewards(
        java.lang.Iterable<? extends xddq.pb.MagicTreasureResultItemMsg> values) {
      if (rewardsBuilder_ == null) {
        ensureRewardsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, rewards_);
        onChanged();
      } else {
        rewardsBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MagicTreasureResultItemMsg rewards = 2;</code>
     */
    public Builder clearRewards() {
      if (rewardsBuilder_ == null) {
        rewards_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        rewardsBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MagicTreasureResultItemMsg rewards = 2;</code>
     */
    public Builder removeRewards(int index) {
      if (rewardsBuilder_ == null) {
        ensureRewardsIsMutable();
        rewards_.remove(index);
        onChanged();
      } else {
        rewardsBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.MagicTreasureResultItemMsg rewards = 2;</code>
     */
    public xddq.pb.MagicTreasureResultItemMsg.Builder getRewardsBuilder(
        int index) {
      return internalGetRewardsFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.MagicTreasureResultItemMsg rewards = 2;</code>
     */
    public xddq.pb.MagicTreasureResultItemMsgOrBuilder getRewardsOrBuilder(
        int index) {
      if (rewardsBuilder_ == null) {
        return rewards_.get(index);  } else {
        return rewardsBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.MagicTreasureResultItemMsg rewards = 2;</code>
     */
    public java.util.List<? extends xddq.pb.MagicTreasureResultItemMsgOrBuilder> 
         getRewardsOrBuilderList() {
      if (rewardsBuilder_ != null) {
        return rewardsBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(rewards_);
      }
    }
    /**
     * <code>repeated .xddq.pb.MagicTreasureResultItemMsg rewards = 2;</code>
     */
    public xddq.pb.MagicTreasureResultItemMsg.Builder addRewardsBuilder() {
      return internalGetRewardsFieldBuilder().addBuilder(
          xddq.pb.MagicTreasureResultItemMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.MagicTreasureResultItemMsg rewards = 2;</code>
     */
    public xddq.pb.MagicTreasureResultItemMsg.Builder addRewardsBuilder(
        int index) {
      return internalGetRewardsFieldBuilder().addBuilder(
          index, xddq.pb.MagicTreasureResultItemMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.MagicTreasureResultItemMsg rewards = 2;</code>
     */
    public java.util.List<xddq.pb.MagicTreasureResultItemMsg.Builder> 
         getRewardsBuilderList() {
      return internalGetRewardsFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.MagicTreasureResultItemMsg, xddq.pb.MagicTreasureResultItemMsg.Builder, xddq.pb.MagicTreasureResultItemMsgOrBuilder> 
        internalGetRewardsFieldBuilder() {
      if (rewardsBuilder_ == null) {
        rewardsBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.MagicTreasureResultItemMsg, xddq.pb.MagicTreasureResultItemMsg.Builder, xddq.pb.MagicTreasureResultItemMsgOrBuilder>(
                rewards_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        rewards_ = null;
      }
      return rewardsBuilder_;
    }

    private xddq.pb.MagicTreasurePlayerDataMsg syncMsg_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.MagicTreasurePlayerDataMsg, xddq.pb.MagicTreasurePlayerDataMsg.Builder, xddq.pb.MagicTreasurePlayerDataMsgOrBuilder> syncMsgBuilder_;
    /**
     * <code>optional .xddq.pb.MagicTreasurePlayerDataMsg syncMsg = 3;</code>
     * @return Whether the syncMsg field is set.
     */
    public boolean hasSyncMsg() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional .xddq.pb.MagicTreasurePlayerDataMsg syncMsg = 3;</code>
     * @return The syncMsg.
     */
    public xddq.pb.MagicTreasurePlayerDataMsg getSyncMsg() {
      if (syncMsgBuilder_ == null) {
        return syncMsg_ == null ? xddq.pb.MagicTreasurePlayerDataMsg.getDefaultInstance() : syncMsg_;
      } else {
        return syncMsgBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.MagicTreasurePlayerDataMsg syncMsg = 3;</code>
     */
    public Builder setSyncMsg(xddq.pb.MagicTreasurePlayerDataMsg value) {
      if (syncMsgBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        syncMsg_ = value;
      } else {
        syncMsgBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.MagicTreasurePlayerDataMsg syncMsg = 3;</code>
     */
    public Builder setSyncMsg(
        xddq.pb.MagicTreasurePlayerDataMsg.Builder builderForValue) {
      if (syncMsgBuilder_ == null) {
        syncMsg_ = builderForValue.build();
      } else {
        syncMsgBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.MagicTreasurePlayerDataMsg syncMsg = 3;</code>
     */
    public Builder mergeSyncMsg(xddq.pb.MagicTreasurePlayerDataMsg value) {
      if (syncMsgBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0) &&
          syncMsg_ != null &&
          syncMsg_ != xddq.pb.MagicTreasurePlayerDataMsg.getDefaultInstance()) {
          getSyncMsgBuilder().mergeFrom(value);
        } else {
          syncMsg_ = value;
        }
      } else {
        syncMsgBuilder_.mergeFrom(value);
      }
      if (syncMsg_ != null) {
        bitField0_ |= 0x00000004;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.MagicTreasurePlayerDataMsg syncMsg = 3;</code>
     */
    public Builder clearSyncMsg() {
      bitField0_ = (bitField0_ & ~0x00000004);
      syncMsg_ = null;
      if (syncMsgBuilder_ != null) {
        syncMsgBuilder_.dispose();
        syncMsgBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.MagicTreasurePlayerDataMsg syncMsg = 3;</code>
     */
    public xddq.pb.MagicTreasurePlayerDataMsg.Builder getSyncMsgBuilder() {
      bitField0_ |= 0x00000004;
      onChanged();
      return internalGetSyncMsgFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.MagicTreasurePlayerDataMsg syncMsg = 3;</code>
     */
    public xddq.pb.MagicTreasurePlayerDataMsgOrBuilder getSyncMsgOrBuilder() {
      if (syncMsgBuilder_ != null) {
        return syncMsgBuilder_.getMessageOrBuilder();
      } else {
        return syncMsg_ == null ?
            xddq.pb.MagicTreasurePlayerDataMsg.getDefaultInstance() : syncMsg_;
      }
    }
    /**
     * <code>optional .xddq.pb.MagicTreasurePlayerDataMsg syncMsg = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.MagicTreasurePlayerDataMsg, xddq.pb.MagicTreasurePlayerDataMsg.Builder, xddq.pb.MagicTreasurePlayerDataMsgOrBuilder> 
        internalGetSyncMsgFieldBuilder() {
      if (syncMsgBuilder_ == null) {
        syncMsgBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.MagicTreasurePlayerDataMsg, xddq.pb.MagicTreasurePlayerDataMsg.Builder, xddq.pb.MagicTreasurePlayerDataMsgOrBuilder>(
                getSyncMsg(),
                getParentForChildren(),
                isClean());
        syncMsg_ = null;
      }
      return syncMsgBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.MagicTreasureDrawRsp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.MagicTreasureDrawRsp)
  private static final xddq.pb.MagicTreasureDrawRsp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.MagicTreasureDrawRsp();
  }

  public static xddq.pb.MagicTreasureDrawRsp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<MagicTreasureDrawRsp>
      PARSER = new com.google.protobuf.AbstractParser<MagicTreasureDrawRsp>() {
    @java.lang.Override
    public MagicTreasureDrawRsp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<MagicTreasureDrawRsp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<MagicTreasureDrawRsp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.MagicTreasureDrawRsp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

