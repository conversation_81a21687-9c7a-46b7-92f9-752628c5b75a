// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

public interface HeavenBattleAppointCommanderRespOrBuilder extends
    // @@protoc_insertion_point(interface_extends:xddq.pb.HeavenBattleAppointCommanderResp)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>optional int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  boolean hasRet();
  /**
   * <code>optional int32 ret = 1;</code>
   * @return The ret.
   */
  int getRet();

  /**
   * <code>repeated int64 commanderList = 2;</code>
   * @return A list containing the commanderList.
   */
  java.util.List<java.lang.Long> getCommanderListList();
  /**
   * <code>repeated int64 commanderList = 2;</code>
   * @return The count of commanderList.
   */
  int getCommanderListCount();
  /**
   * <code>repeated int64 commanderList = 2;</code>
   * @param index The index of the element to return.
   * @return The commanderList at the given index.
   */
  long getCommanderList(int index);
}
