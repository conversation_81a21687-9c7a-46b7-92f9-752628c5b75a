// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.PlanesTrialReceiveGrandPrizeResp}
 */
public final class PlanesTrialReceiveGrandPrizeResp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.PlanesTrialReceiveGrandPrizeResp)
    PlanesTrialReceiveGrandPrizeRespOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      PlanesTrialReceiveGrandPrizeResp.class.getName());
  }
  // Use PlanesTrialReceiveGrandPrizeResp.newBuilder() to construct.
  private PlanesTrialReceiveGrandPrizeResp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private PlanesTrialReceiveGrandPrizeResp() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_PlanesTrialReceiveGrandPrizeResp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_PlanesTrialReceiveGrandPrizeResp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.PlanesTrialReceiveGrandPrizeResp.class, xddq.pb.PlanesTrialReceiveGrandPrizeResp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int REWARDDATA_FIELD_NUMBER = 2;
  private xddq.pb.PlanesTrialGrandPrizeData rewardData_;
  /**
   * <code>optional .xddq.pb.PlanesTrialGrandPrizeData rewardData = 2;</code>
   * @return Whether the rewardData field is set.
   */
  @java.lang.Override
  public boolean hasRewardData() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional .xddq.pb.PlanesTrialGrandPrizeData rewardData = 2;</code>
   * @return The rewardData.
   */
  @java.lang.Override
  public xddq.pb.PlanesTrialGrandPrizeData getRewardData() {
    return rewardData_ == null ? xddq.pb.PlanesTrialGrandPrizeData.getDefaultInstance() : rewardData_;
  }
  /**
   * <code>optional .xddq.pb.PlanesTrialGrandPrizeData rewardData = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.PlanesTrialGrandPrizeDataOrBuilder getRewardDataOrBuilder() {
    return rewardData_ == null ? xddq.pb.PlanesTrialGrandPrizeData.getDefaultInstance() : rewardData_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeMessage(2, getRewardData());
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getRewardData());
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.PlanesTrialReceiveGrandPrizeResp)) {
      return super.equals(obj);
    }
    xddq.pb.PlanesTrialReceiveGrandPrizeResp other = (xddq.pb.PlanesTrialReceiveGrandPrizeResp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (hasRewardData() != other.hasRewardData()) return false;
    if (hasRewardData()) {
      if (!getRewardData()
          .equals(other.getRewardData())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (hasRewardData()) {
      hash = (37 * hash) + REWARDDATA_FIELD_NUMBER;
      hash = (53 * hash) + getRewardData().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.PlanesTrialReceiveGrandPrizeResp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PlanesTrialReceiveGrandPrizeResp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PlanesTrialReceiveGrandPrizeResp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PlanesTrialReceiveGrandPrizeResp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PlanesTrialReceiveGrandPrizeResp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PlanesTrialReceiveGrandPrizeResp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PlanesTrialReceiveGrandPrizeResp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.PlanesTrialReceiveGrandPrizeResp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.PlanesTrialReceiveGrandPrizeResp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.PlanesTrialReceiveGrandPrizeResp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.PlanesTrialReceiveGrandPrizeResp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.PlanesTrialReceiveGrandPrizeResp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.PlanesTrialReceiveGrandPrizeResp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.PlanesTrialReceiveGrandPrizeResp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.PlanesTrialReceiveGrandPrizeResp)
      xddq.pb.PlanesTrialReceiveGrandPrizeRespOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PlanesTrialReceiveGrandPrizeResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PlanesTrialReceiveGrandPrizeResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.PlanesTrialReceiveGrandPrizeResp.class, xddq.pb.PlanesTrialReceiveGrandPrizeResp.Builder.class);
    }

    // Construct using xddq.pb.PlanesTrialReceiveGrandPrizeResp.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetRewardDataFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      rewardData_ = null;
      if (rewardDataBuilder_ != null) {
        rewardDataBuilder_.dispose();
        rewardDataBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PlanesTrialReceiveGrandPrizeResp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.PlanesTrialReceiveGrandPrizeResp getDefaultInstanceForType() {
      return xddq.pb.PlanesTrialReceiveGrandPrizeResp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.PlanesTrialReceiveGrandPrizeResp build() {
      xddq.pb.PlanesTrialReceiveGrandPrizeResp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.PlanesTrialReceiveGrandPrizeResp buildPartial() {
      xddq.pb.PlanesTrialReceiveGrandPrizeResp result = new xddq.pb.PlanesTrialReceiveGrandPrizeResp(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.PlanesTrialReceiveGrandPrizeResp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.rewardData_ = rewardDataBuilder_ == null
            ? rewardData_
            : rewardDataBuilder_.build();
        to_bitField0_ |= 0x00000002;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.PlanesTrialReceiveGrandPrizeResp) {
        return mergeFrom((xddq.pb.PlanesTrialReceiveGrandPrizeResp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.PlanesTrialReceiveGrandPrizeResp other) {
      if (other == xddq.pb.PlanesTrialReceiveGrandPrizeResp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (other.hasRewardData()) {
        mergeRewardData(other.getRewardData());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              input.readMessage(
                  internalGetRewardDataFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private xddq.pb.PlanesTrialGrandPrizeData rewardData_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PlanesTrialGrandPrizeData, xddq.pb.PlanesTrialGrandPrizeData.Builder, xddq.pb.PlanesTrialGrandPrizeDataOrBuilder> rewardDataBuilder_;
    /**
     * <code>optional .xddq.pb.PlanesTrialGrandPrizeData rewardData = 2;</code>
     * @return Whether the rewardData field is set.
     */
    public boolean hasRewardData() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .xddq.pb.PlanesTrialGrandPrizeData rewardData = 2;</code>
     * @return The rewardData.
     */
    public xddq.pb.PlanesTrialGrandPrizeData getRewardData() {
      if (rewardDataBuilder_ == null) {
        return rewardData_ == null ? xddq.pb.PlanesTrialGrandPrizeData.getDefaultInstance() : rewardData_;
      } else {
        return rewardDataBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.PlanesTrialGrandPrizeData rewardData = 2;</code>
     */
    public Builder setRewardData(xddq.pb.PlanesTrialGrandPrizeData value) {
      if (rewardDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        rewardData_ = value;
      } else {
        rewardDataBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlanesTrialGrandPrizeData rewardData = 2;</code>
     */
    public Builder setRewardData(
        xddq.pb.PlanesTrialGrandPrizeData.Builder builderForValue) {
      if (rewardDataBuilder_ == null) {
        rewardData_ = builderForValue.build();
      } else {
        rewardDataBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlanesTrialGrandPrizeData rewardData = 2;</code>
     */
    public Builder mergeRewardData(xddq.pb.PlanesTrialGrandPrizeData value) {
      if (rewardDataBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0) &&
          rewardData_ != null &&
          rewardData_ != xddq.pb.PlanesTrialGrandPrizeData.getDefaultInstance()) {
          getRewardDataBuilder().mergeFrom(value);
        } else {
          rewardData_ = value;
        }
      } else {
        rewardDataBuilder_.mergeFrom(value);
      }
      if (rewardData_ != null) {
        bitField0_ |= 0x00000002;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlanesTrialGrandPrizeData rewardData = 2;</code>
     */
    public Builder clearRewardData() {
      bitField0_ = (bitField0_ & ~0x00000002);
      rewardData_ = null;
      if (rewardDataBuilder_ != null) {
        rewardDataBuilder_.dispose();
        rewardDataBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlanesTrialGrandPrizeData rewardData = 2;</code>
     */
    public xddq.pb.PlanesTrialGrandPrizeData.Builder getRewardDataBuilder() {
      bitField0_ |= 0x00000002;
      onChanged();
      return internalGetRewardDataFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.PlanesTrialGrandPrizeData rewardData = 2;</code>
     */
    public xddq.pb.PlanesTrialGrandPrizeDataOrBuilder getRewardDataOrBuilder() {
      if (rewardDataBuilder_ != null) {
        return rewardDataBuilder_.getMessageOrBuilder();
      } else {
        return rewardData_ == null ?
            xddq.pb.PlanesTrialGrandPrizeData.getDefaultInstance() : rewardData_;
      }
    }
    /**
     * <code>optional .xddq.pb.PlanesTrialGrandPrizeData rewardData = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PlanesTrialGrandPrizeData, xddq.pb.PlanesTrialGrandPrizeData.Builder, xddq.pb.PlanesTrialGrandPrizeDataOrBuilder> 
        internalGetRewardDataFieldBuilder() {
      if (rewardDataBuilder_ == null) {
        rewardDataBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.PlanesTrialGrandPrizeData, xddq.pb.PlanesTrialGrandPrizeData.Builder, xddq.pb.PlanesTrialGrandPrizeDataOrBuilder>(
                getRewardData(),
                getParentForChildren(),
                isClean());
        rewardData_ = null;
      }
      return rewardDataBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.PlanesTrialReceiveGrandPrizeResp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.PlanesTrialReceiveGrandPrizeResp)
  private static final xddq.pb.PlanesTrialReceiveGrandPrizeResp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.PlanesTrialReceiveGrandPrizeResp();
  }

  public static xddq.pb.PlanesTrialReceiveGrandPrizeResp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<PlanesTrialReceiveGrandPrizeResp>
      PARSER = new com.google.protobuf.AbstractParser<PlanesTrialReceiveGrandPrizeResp>() {
    @java.lang.Override
    public PlanesTrialReceiveGrandPrizeResp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<PlanesTrialReceiveGrandPrizeResp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<PlanesTrialReceiveGrandPrizeResp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.PlanesTrialReceiveGrandPrizeResp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

