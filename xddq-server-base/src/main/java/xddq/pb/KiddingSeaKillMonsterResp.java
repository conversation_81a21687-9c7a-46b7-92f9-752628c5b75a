// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.KiddingSeaKillMonsterResp}
 */
public final class KiddingSeaKillMonsterResp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.KiddingSeaKillMonsterResp)
    KiddingSeaKillMonsterRespOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      KiddingSeaKillMonsterResp.class.getName());
  }
  // Use KiddingSeaKillMonsterResp.newBuilder() to construct.
  private KiddingSeaKillMonsterResp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private KiddingSeaKillMonsterResp() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_KiddingSeaKillMonsterResp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_KiddingSeaKillMonsterResp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.KiddingSeaKillMonsterResp.class, xddq.pb.KiddingSeaKillMonsterResp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int ENTITYID_FIELD_NUMBER = 2;
  private int entityId_ = 0;
  /**
   * <code>optional int32 entityId = 2;</code>
   * @return Whether the entityId field is set.
   */
  @java.lang.Override
  public boolean hasEntityId() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int32 entityId = 2;</code>
   * @return The entityId.
   */
  @java.lang.Override
  public int getEntityId() {
    return entityId_;
  }

  public static final int EVENTID_FIELD_NUMBER = 3;
  private int eventId_ = 0;
  /**
   * <code>optional int32 eventId = 3;</code>
   * @return Whether the eventId field is set.
   */
  @java.lang.Override
  public boolean hasEventId() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional int32 eventId = 3;</code>
   * @return The eventId.
   */
  @java.lang.Override
  public int getEventId() {
    return eventId_;
  }

  public static final int MONSTERID_FIELD_NUMBER = 4;
  private int monsterId_ = 0;
  /**
   * <code>optional int32 monsterId = 4;</code>
   * @return Whether the monsterId field is set.
   */
  @java.lang.Override
  public boolean hasMonsterId() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional int32 monsterId = 4;</code>
   * @return The monsterId.
   */
  @java.lang.Override
  public int getMonsterId() {
    return monsterId_;
  }

  public static final int KILLMONSTERNUM_FIELD_NUMBER = 5;
  private int killMonsterNum_ = 0;
  /**
   * <code>optional int32 killMonsterNum = 5;</code>
   * @return Whether the killMonsterNum field is set.
   */
  @java.lang.Override
  public boolean hasKillMonsterNum() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>optional int32 killMonsterNum = 5;</code>
   * @return The killMonsterNum.
   */
  @java.lang.Override
  public int getKillMonsterNum() {
    return killMonsterNum_;
  }

  public static final int EXP_FIELD_NUMBER = 6;
  private int exp_ = 0;
  /**
   * <code>optional int32 exp = 6;</code>
   * @return Whether the exp field is set.
   */
  @java.lang.Override
  public boolean hasExp() {
    return ((bitField0_ & 0x00000020) != 0);
  }
  /**
   * <code>optional int32 exp = 6;</code>
   * @return The exp.
   */
  @java.lang.Override
  public int getExp() {
    return exp_;
  }

  public static final int LEVEL_FIELD_NUMBER = 7;
  private int level_ = 0;
  /**
   * <code>optional int32 level = 7;</code>
   * @return Whether the level field is set.
   */
  @java.lang.Override
  public boolean hasLevel() {
    return ((bitField0_ & 0x00000040) != 0);
  }
  /**
   * <code>optional int32 level = 7;</code>
   * @return The level.
   */
  @java.lang.Override
  public int getLevel() {
    return level_;
  }

  public static final int NEZEEXP_FIELD_NUMBER = 8;
  private int nezeExp_ = 0;
  /**
   * <code>optional int32 nezeExp = 8;</code>
   * @return Whether the nezeExp field is set.
   */
  @java.lang.Override
  public boolean hasNezeExp() {
    return ((bitField0_ & 0x00000080) != 0);
  }
  /**
   * <code>optional int32 nezeExp = 8;</code>
   * @return The nezeExp.
   */
  @java.lang.Override
  public int getNezeExp() {
    return nezeExp_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(2, entityId_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt32(3, eventId_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt32(4, monsterId_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      output.writeInt32(5, killMonsterNum_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      output.writeInt32(6, exp_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      output.writeInt32(7, level_);
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      output.writeInt32(8, nezeExp_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, entityId_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(3, eventId_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(4, monsterId_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(5, killMonsterNum_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(6, exp_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(7, level_);
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(8, nezeExp_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.KiddingSeaKillMonsterResp)) {
      return super.equals(obj);
    }
    xddq.pb.KiddingSeaKillMonsterResp other = (xddq.pb.KiddingSeaKillMonsterResp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (hasEntityId() != other.hasEntityId()) return false;
    if (hasEntityId()) {
      if (getEntityId()
          != other.getEntityId()) return false;
    }
    if (hasEventId() != other.hasEventId()) return false;
    if (hasEventId()) {
      if (getEventId()
          != other.getEventId()) return false;
    }
    if (hasMonsterId() != other.hasMonsterId()) return false;
    if (hasMonsterId()) {
      if (getMonsterId()
          != other.getMonsterId()) return false;
    }
    if (hasKillMonsterNum() != other.hasKillMonsterNum()) return false;
    if (hasKillMonsterNum()) {
      if (getKillMonsterNum()
          != other.getKillMonsterNum()) return false;
    }
    if (hasExp() != other.hasExp()) return false;
    if (hasExp()) {
      if (getExp()
          != other.getExp()) return false;
    }
    if (hasLevel() != other.hasLevel()) return false;
    if (hasLevel()) {
      if (getLevel()
          != other.getLevel()) return false;
    }
    if (hasNezeExp() != other.hasNezeExp()) return false;
    if (hasNezeExp()) {
      if (getNezeExp()
          != other.getNezeExp()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (hasEntityId()) {
      hash = (37 * hash) + ENTITYID_FIELD_NUMBER;
      hash = (53 * hash) + getEntityId();
    }
    if (hasEventId()) {
      hash = (37 * hash) + EVENTID_FIELD_NUMBER;
      hash = (53 * hash) + getEventId();
    }
    if (hasMonsterId()) {
      hash = (37 * hash) + MONSTERID_FIELD_NUMBER;
      hash = (53 * hash) + getMonsterId();
    }
    if (hasKillMonsterNum()) {
      hash = (37 * hash) + KILLMONSTERNUM_FIELD_NUMBER;
      hash = (53 * hash) + getKillMonsterNum();
    }
    if (hasExp()) {
      hash = (37 * hash) + EXP_FIELD_NUMBER;
      hash = (53 * hash) + getExp();
    }
    if (hasLevel()) {
      hash = (37 * hash) + LEVEL_FIELD_NUMBER;
      hash = (53 * hash) + getLevel();
    }
    if (hasNezeExp()) {
      hash = (37 * hash) + NEZEEXP_FIELD_NUMBER;
      hash = (53 * hash) + getNezeExp();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.KiddingSeaKillMonsterResp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.KiddingSeaKillMonsterResp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.KiddingSeaKillMonsterResp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.KiddingSeaKillMonsterResp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.KiddingSeaKillMonsterResp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.KiddingSeaKillMonsterResp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.KiddingSeaKillMonsterResp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.KiddingSeaKillMonsterResp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.KiddingSeaKillMonsterResp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.KiddingSeaKillMonsterResp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.KiddingSeaKillMonsterResp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.KiddingSeaKillMonsterResp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.KiddingSeaKillMonsterResp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.KiddingSeaKillMonsterResp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.KiddingSeaKillMonsterResp)
      xddq.pb.KiddingSeaKillMonsterRespOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_KiddingSeaKillMonsterResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_KiddingSeaKillMonsterResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.KiddingSeaKillMonsterResp.class, xddq.pb.KiddingSeaKillMonsterResp.Builder.class);
    }

    // Construct using xddq.pb.KiddingSeaKillMonsterResp.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      entityId_ = 0;
      eventId_ = 0;
      monsterId_ = 0;
      killMonsterNum_ = 0;
      exp_ = 0;
      level_ = 0;
      nezeExp_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_KiddingSeaKillMonsterResp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.KiddingSeaKillMonsterResp getDefaultInstanceForType() {
      return xddq.pb.KiddingSeaKillMonsterResp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.KiddingSeaKillMonsterResp build() {
      xddq.pb.KiddingSeaKillMonsterResp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.KiddingSeaKillMonsterResp buildPartial() {
      xddq.pb.KiddingSeaKillMonsterResp result = new xddq.pb.KiddingSeaKillMonsterResp(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.KiddingSeaKillMonsterResp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.entityId_ = entityId_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.eventId_ = eventId_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.monsterId_ = monsterId_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.killMonsterNum_ = killMonsterNum_;
        to_bitField0_ |= 0x00000010;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.exp_ = exp_;
        to_bitField0_ |= 0x00000020;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.level_ = level_;
        to_bitField0_ |= 0x00000040;
      }
      if (((from_bitField0_ & 0x00000080) != 0)) {
        result.nezeExp_ = nezeExp_;
        to_bitField0_ |= 0x00000080;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.KiddingSeaKillMonsterResp) {
        return mergeFrom((xddq.pb.KiddingSeaKillMonsterResp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.KiddingSeaKillMonsterResp other) {
      if (other == xddq.pb.KiddingSeaKillMonsterResp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (other.hasEntityId()) {
        setEntityId(other.getEntityId());
      }
      if (other.hasEventId()) {
        setEventId(other.getEventId());
      }
      if (other.hasMonsterId()) {
        setMonsterId(other.getMonsterId());
      }
      if (other.hasKillMonsterNum()) {
        setKillMonsterNum(other.getKillMonsterNum());
      }
      if (other.hasExp()) {
        setExp(other.getExp());
      }
      if (other.hasLevel()) {
        setLevel(other.getLevel());
      }
      if (other.hasNezeExp()) {
        setNezeExp(other.getNezeExp());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              entityId_ = input.readInt32();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 24: {
              eventId_ = input.readInt32();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 32: {
              monsterId_ = input.readInt32();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            case 40: {
              killMonsterNum_ = input.readInt32();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            case 48: {
              exp_ = input.readInt32();
              bitField0_ |= 0x00000020;
              break;
            } // case 48
            case 56: {
              level_ = input.readInt32();
              bitField0_ |= 0x00000040;
              break;
            } // case 56
            case 64: {
              nezeExp_ = input.readInt32();
              bitField0_ |= 0x00000080;
              break;
            } // case 64
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private int entityId_ ;
    /**
     * <code>optional int32 entityId = 2;</code>
     * @return Whether the entityId field is set.
     */
    @java.lang.Override
    public boolean hasEntityId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 entityId = 2;</code>
     * @return The entityId.
     */
    @java.lang.Override
    public int getEntityId() {
      return entityId_;
    }
    /**
     * <code>optional int32 entityId = 2;</code>
     * @param value The entityId to set.
     * @return This builder for chaining.
     */
    public Builder setEntityId(int value) {

      entityId_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 entityId = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearEntityId() {
      bitField0_ = (bitField0_ & ~0x00000002);
      entityId_ = 0;
      onChanged();
      return this;
    }

    private int eventId_ ;
    /**
     * <code>optional int32 eventId = 3;</code>
     * @return Whether the eventId field is set.
     */
    @java.lang.Override
    public boolean hasEventId() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int32 eventId = 3;</code>
     * @return The eventId.
     */
    @java.lang.Override
    public int getEventId() {
      return eventId_;
    }
    /**
     * <code>optional int32 eventId = 3;</code>
     * @param value The eventId to set.
     * @return This builder for chaining.
     */
    public Builder setEventId(int value) {

      eventId_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 eventId = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearEventId() {
      bitField0_ = (bitField0_ & ~0x00000004);
      eventId_ = 0;
      onChanged();
      return this;
    }

    private int monsterId_ ;
    /**
     * <code>optional int32 monsterId = 4;</code>
     * @return Whether the monsterId field is set.
     */
    @java.lang.Override
    public boolean hasMonsterId() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int32 monsterId = 4;</code>
     * @return The monsterId.
     */
    @java.lang.Override
    public int getMonsterId() {
      return monsterId_;
    }
    /**
     * <code>optional int32 monsterId = 4;</code>
     * @param value The monsterId to set.
     * @return This builder for chaining.
     */
    public Builder setMonsterId(int value) {

      monsterId_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 monsterId = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearMonsterId() {
      bitField0_ = (bitField0_ & ~0x00000008);
      monsterId_ = 0;
      onChanged();
      return this;
    }

    private int killMonsterNum_ ;
    /**
     * <code>optional int32 killMonsterNum = 5;</code>
     * @return Whether the killMonsterNum field is set.
     */
    @java.lang.Override
    public boolean hasKillMonsterNum() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional int32 killMonsterNum = 5;</code>
     * @return The killMonsterNum.
     */
    @java.lang.Override
    public int getKillMonsterNum() {
      return killMonsterNum_;
    }
    /**
     * <code>optional int32 killMonsterNum = 5;</code>
     * @param value The killMonsterNum to set.
     * @return This builder for chaining.
     */
    public Builder setKillMonsterNum(int value) {

      killMonsterNum_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 killMonsterNum = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearKillMonsterNum() {
      bitField0_ = (bitField0_ & ~0x00000010);
      killMonsterNum_ = 0;
      onChanged();
      return this;
    }

    private int exp_ ;
    /**
     * <code>optional int32 exp = 6;</code>
     * @return Whether the exp field is set.
     */
    @java.lang.Override
    public boolean hasExp() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional int32 exp = 6;</code>
     * @return The exp.
     */
    @java.lang.Override
    public int getExp() {
      return exp_;
    }
    /**
     * <code>optional int32 exp = 6;</code>
     * @param value The exp to set.
     * @return This builder for chaining.
     */
    public Builder setExp(int value) {

      exp_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 exp = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearExp() {
      bitField0_ = (bitField0_ & ~0x00000020);
      exp_ = 0;
      onChanged();
      return this;
    }

    private int level_ ;
    /**
     * <code>optional int32 level = 7;</code>
     * @return Whether the level field is set.
     */
    @java.lang.Override
    public boolean hasLevel() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <code>optional int32 level = 7;</code>
     * @return The level.
     */
    @java.lang.Override
    public int getLevel() {
      return level_;
    }
    /**
     * <code>optional int32 level = 7;</code>
     * @param value The level to set.
     * @return This builder for chaining.
     */
    public Builder setLevel(int value) {

      level_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 level = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearLevel() {
      bitField0_ = (bitField0_ & ~0x00000040);
      level_ = 0;
      onChanged();
      return this;
    }

    private int nezeExp_ ;
    /**
     * <code>optional int32 nezeExp = 8;</code>
     * @return Whether the nezeExp field is set.
     */
    @java.lang.Override
    public boolean hasNezeExp() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <code>optional int32 nezeExp = 8;</code>
     * @return The nezeExp.
     */
    @java.lang.Override
    public int getNezeExp() {
      return nezeExp_;
    }
    /**
     * <code>optional int32 nezeExp = 8;</code>
     * @param value The nezeExp to set.
     * @return This builder for chaining.
     */
    public Builder setNezeExp(int value) {

      nezeExp_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 nezeExp = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearNezeExp() {
      bitField0_ = (bitField0_ & ~0x00000080);
      nezeExp_ = 0;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.KiddingSeaKillMonsterResp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.KiddingSeaKillMonsterResp)
  private static final xddq.pb.KiddingSeaKillMonsterResp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.KiddingSeaKillMonsterResp();
  }

  public static xddq.pb.KiddingSeaKillMonsterResp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<KiddingSeaKillMonsterResp>
      PARSER = new com.google.protobuf.AbstractParser<KiddingSeaKillMonsterResp>() {
    @java.lang.Override
    public KiddingSeaKillMonsterResp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<KiddingSeaKillMonsterResp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<KiddingSeaKillMonsterResp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.KiddingSeaKillMonsterResp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

