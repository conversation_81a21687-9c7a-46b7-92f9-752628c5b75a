// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.SyncPlayerCornucopiaChangeMsg}
 */
public final class SyncPlayerCornucopiaChangeMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.SyncPlayerCornucopiaChangeMsg)
    SyncPlayerCornucopiaChangeMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      SyncPlayerCornucopiaChangeMsg.class.getName());
  }
  // Use SyncPlayerCornucopiaChangeMsg.newBuilder() to construct.
  private SyncPlayerCornucopiaChangeMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private SyncPlayerCornucopiaChangeMsg() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_SyncPlayerCornucopiaChangeMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_SyncPlayerCornucopiaChangeMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.SyncPlayerCornucopiaChangeMsg.class, xddq.pb.SyncPlayerCornucopiaChangeMsg.Builder.class);
  }

  private int bitField0_;
  public static final int CORNUCOPIA_FIELD_NUMBER = 1;
  private xddq.pb.CornucopiaMsg cornucopia_;
  /**
   * <code>required .xddq.pb.CornucopiaMsg cornucopia = 1;</code>
   * @return Whether the cornucopia field is set.
   */
  @java.lang.Override
  public boolean hasCornucopia() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required .xddq.pb.CornucopiaMsg cornucopia = 1;</code>
   * @return The cornucopia.
   */
  @java.lang.Override
  public xddq.pb.CornucopiaMsg getCornucopia() {
    return cornucopia_ == null ? xddq.pb.CornucopiaMsg.getDefaultInstance() : cornucopia_;
  }
  /**
   * <code>required .xddq.pb.CornucopiaMsg cornucopia = 1;</code>
   */
  @java.lang.Override
  public xddq.pb.CornucopiaMsgOrBuilder getCornucopiaOrBuilder() {
    return cornucopia_ == null ? xddq.pb.CornucopiaMsg.getDefaultInstance() : cornucopia_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasCornucopia()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!getCornucopia().isInitialized()) {
      memoizedIsInitialized = 0;
      return false;
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeMessage(1, getCornucopia());
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, getCornucopia());
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.SyncPlayerCornucopiaChangeMsg)) {
      return super.equals(obj);
    }
    xddq.pb.SyncPlayerCornucopiaChangeMsg other = (xddq.pb.SyncPlayerCornucopiaChangeMsg) obj;

    if (hasCornucopia() != other.hasCornucopia()) return false;
    if (hasCornucopia()) {
      if (!getCornucopia()
          .equals(other.getCornucopia())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasCornucopia()) {
      hash = (37 * hash) + CORNUCOPIA_FIELD_NUMBER;
      hash = (53 * hash) + getCornucopia().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.SyncPlayerCornucopiaChangeMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.SyncPlayerCornucopiaChangeMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.SyncPlayerCornucopiaChangeMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.SyncPlayerCornucopiaChangeMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.SyncPlayerCornucopiaChangeMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.SyncPlayerCornucopiaChangeMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.SyncPlayerCornucopiaChangeMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.SyncPlayerCornucopiaChangeMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.SyncPlayerCornucopiaChangeMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.SyncPlayerCornucopiaChangeMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.SyncPlayerCornucopiaChangeMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.SyncPlayerCornucopiaChangeMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.SyncPlayerCornucopiaChangeMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.SyncPlayerCornucopiaChangeMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.SyncPlayerCornucopiaChangeMsg)
      xddq.pb.SyncPlayerCornucopiaChangeMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_SyncPlayerCornucopiaChangeMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_SyncPlayerCornucopiaChangeMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.SyncPlayerCornucopiaChangeMsg.class, xddq.pb.SyncPlayerCornucopiaChangeMsg.Builder.class);
    }

    // Construct using xddq.pb.SyncPlayerCornucopiaChangeMsg.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetCornucopiaFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      cornucopia_ = null;
      if (cornucopiaBuilder_ != null) {
        cornucopiaBuilder_.dispose();
        cornucopiaBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_SyncPlayerCornucopiaChangeMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.SyncPlayerCornucopiaChangeMsg getDefaultInstanceForType() {
      return xddq.pb.SyncPlayerCornucopiaChangeMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.SyncPlayerCornucopiaChangeMsg build() {
      xddq.pb.SyncPlayerCornucopiaChangeMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.SyncPlayerCornucopiaChangeMsg buildPartial() {
      xddq.pb.SyncPlayerCornucopiaChangeMsg result = new xddq.pb.SyncPlayerCornucopiaChangeMsg(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.SyncPlayerCornucopiaChangeMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.cornucopia_ = cornucopiaBuilder_ == null
            ? cornucopia_
            : cornucopiaBuilder_.build();
        to_bitField0_ |= 0x00000001;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.SyncPlayerCornucopiaChangeMsg) {
        return mergeFrom((xddq.pb.SyncPlayerCornucopiaChangeMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.SyncPlayerCornucopiaChangeMsg other) {
      if (other == xddq.pb.SyncPlayerCornucopiaChangeMsg.getDefaultInstance()) return this;
      if (other.hasCornucopia()) {
        mergeCornucopia(other.getCornucopia());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasCornucopia()) {
        return false;
      }
      if (!getCornucopia().isInitialized()) {
        return false;
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              input.readMessage(
                  internalGetCornucopiaFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private xddq.pb.CornucopiaMsg cornucopia_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.CornucopiaMsg, xddq.pb.CornucopiaMsg.Builder, xddq.pb.CornucopiaMsgOrBuilder> cornucopiaBuilder_;
    /**
     * <code>required .xddq.pb.CornucopiaMsg cornucopia = 1;</code>
     * @return Whether the cornucopia field is set.
     */
    public boolean hasCornucopia() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required .xddq.pb.CornucopiaMsg cornucopia = 1;</code>
     * @return The cornucopia.
     */
    public xddq.pb.CornucopiaMsg getCornucopia() {
      if (cornucopiaBuilder_ == null) {
        return cornucopia_ == null ? xddq.pb.CornucopiaMsg.getDefaultInstance() : cornucopia_;
      } else {
        return cornucopiaBuilder_.getMessage();
      }
    }
    /**
     * <code>required .xddq.pb.CornucopiaMsg cornucopia = 1;</code>
     */
    public Builder setCornucopia(xddq.pb.CornucopiaMsg value) {
      if (cornucopiaBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        cornucopia_ = value;
      } else {
        cornucopiaBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required .xddq.pb.CornucopiaMsg cornucopia = 1;</code>
     */
    public Builder setCornucopia(
        xddq.pb.CornucopiaMsg.Builder builderForValue) {
      if (cornucopiaBuilder_ == null) {
        cornucopia_ = builderForValue.build();
      } else {
        cornucopiaBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required .xddq.pb.CornucopiaMsg cornucopia = 1;</code>
     */
    public Builder mergeCornucopia(xddq.pb.CornucopiaMsg value) {
      if (cornucopiaBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0) &&
          cornucopia_ != null &&
          cornucopia_ != xddq.pb.CornucopiaMsg.getDefaultInstance()) {
          getCornucopiaBuilder().mergeFrom(value);
        } else {
          cornucopia_ = value;
        }
      } else {
        cornucopiaBuilder_.mergeFrom(value);
      }
      if (cornucopia_ != null) {
        bitField0_ |= 0x00000001;
        onChanged();
      }
      return this;
    }
    /**
     * <code>required .xddq.pb.CornucopiaMsg cornucopia = 1;</code>
     */
    public Builder clearCornucopia() {
      bitField0_ = (bitField0_ & ~0x00000001);
      cornucopia_ = null;
      if (cornucopiaBuilder_ != null) {
        cornucopiaBuilder_.dispose();
        cornucopiaBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>required .xddq.pb.CornucopiaMsg cornucopia = 1;</code>
     */
    public xddq.pb.CornucopiaMsg.Builder getCornucopiaBuilder() {
      bitField0_ |= 0x00000001;
      onChanged();
      return internalGetCornucopiaFieldBuilder().getBuilder();
    }
    /**
     * <code>required .xddq.pb.CornucopiaMsg cornucopia = 1;</code>
     */
    public xddq.pb.CornucopiaMsgOrBuilder getCornucopiaOrBuilder() {
      if (cornucopiaBuilder_ != null) {
        return cornucopiaBuilder_.getMessageOrBuilder();
      } else {
        return cornucopia_ == null ?
            xddq.pb.CornucopiaMsg.getDefaultInstance() : cornucopia_;
      }
    }
    /**
     * <code>required .xddq.pb.CornucopiaMsg cornucopia = 1;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.CornucopiaMsg, xddq.pb.CornucopiaMsg.Builder, xddq.pb.CornucopiaMsgOrBuilder> 
        internalGetCornucopiaFieldBuilder() {
      if (cornucopiaBuilder_ == null) {
        cornucopiaBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.CornucopiaMsg, xddq.pb.CornucopiaMsg.Builder, xddq.pb.CornucopiaMsgOrBuilder>(
                getCornucopia(),
                getParentForChildren(),
                isClean());
        cornucopia_ = null;
      }
      return cornucopiaBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.SyncPlayerCornucopiaChangeMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.SyncPlayerCornucopiaChangeMsg)
  private static final xddq.pb.SyncPlayerCornucopiaChangeMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.SyncPlayerCornucopiaChangeMsg();
  }

  public static xddq.pb.SyncPlayerCornucopiaChangeMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<SyncPlayerCornucopiaChangeMsg>
      PARSER = new com.google.protobuf.AbstractParser<SyncPlayerCornucopiaChangeMsg>() {
    @java.lang.Override
    public SyncPlayerCornucopiaChangeMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<SyncPlayerCornucopiaChangeMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<SyncPlayerCornucopiaChangeMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.SyncPlayerCornucopiaChangeMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

