// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.WeYoundAssistTrapSync}
 */
public final class WeYoundAssistTrapSync extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.WeYoundAssistTrapSync)
    WeYoundAssistTrapSyncOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      WeYoundAssistTrapSync.class.getName());
  }
  // Use WeYoundAssistTrapSync.newBuilder() to construct.
  private WeYoundAssistTrapSync(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private WeYoundAssistTrapSync() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_WeYoundAssistTrapSync_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_WeYoundAssistTrapSync_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.WeYoundAssistTrapSync.class, xddq.pb.WeYoundAssistTrapSync.Builder.class);
  }

  private int bitField0_;
  public static final int PLAYERDATA_FIELD_NUMBER = 1;
  private xddq.pb.PlayerCharacterImageMsg playerData_;
  /**
   * <code>optional .xddq.pb.PlayerCharacterImageMsg playerData = 1;</code>
   * @return Whether the playerData field is set.
   */
  @java.lang.Override
  public boolean hasPlayerData() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional .xddq.pb.PlayerCharacterImageMsg playerData = 1;</code>
   * @return The playerData.
   */
  @java.lang.Override
  public xddq.pb.PlayerCharacterImageMsg getPlayerData() {
    return playerData_ == null ? xddq.pb.PlayerCharacterImageMsg.getDefaultInstance() : playerData_;
  }
  /**
   * <code>optional .xddq.pb.PlayerCharacterImageMsg playerData = 1;</code>
   */
  @java.lang.Override
  public xddq.pb.PlayerCharacterImageMsgOrBuilder getPlayerDataOrBuilder() {
    return playerData_ == null ? xddq.pb.PlayerCharacterImageMsg.getDefaultInstance() : playerData_;
  }

  public static final int PLAYERID_FIELD_NUMBER = 2;
  private long playerId_ = 0L;
  /**
   * <code>optional int64 playerId = 2;</code>
   * @return Whether the playerId field is set.
   */
  @java.lang.Override
  public boolean hasPlayerId() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int64 playerId = 2;</code>
   * @return The playerId.
   */
  @java.lang.Override
  public long getPlayerId() {
    return playerId_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeMessage(1, getPlayerData());
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt64(2, playerId_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, getPlayerData());
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(2, playerId_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.WeYoundAssistTrapSync)) {
      return super.equals(obj);
    }
    xddq.pb.WeYoundAssistTrapSync other = (xddq.pb.WeYoundAssistTrapSync) obj;

    if (hasPlayerData() != other.hasPlayerData()) return false;
    if (hasPlayerData()) {
      if (!getPlayerData()
          .equals(other.getPlayerData())) return false;
    }
    if (hasPlayerId() != other.hasPlayerId()) return false;
    if (hasPlayerId()) {
      if (getPlayerId()
          != other.getPlayerId()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasPlayerData()) {
      hash = (37 * hash) + PLAYERDATA_FIELD_NUMBER;
      hash = (53 * hash) + getPlayerData().hashCode();
    }
    if (hasPlayerId()) {
      hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getPlayerId());
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.WeYoundAssistTrapSync parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.WeYoundAssistTrapSync parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.WeYoundAssistTrapSync parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.WeYoundAssistTrapSync parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.WeYoundAssistTrapSync parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.WeYoundAssistTrapSync parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.WeYoundAssistTrapSync parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.WeYoundAssistTrapSync parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.WeYoundAssistTrapSync parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.WeYoundAssistTrapSync parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.WeYoundAssistTrapSync parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.WeYoundAssistTrapSync parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.WeYoundAssistTrapSync prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.WeYoundAssistTrapSync}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.WeYoundAssistTrapSync)
      xddq.pb.WeYoundAssistTrapSyncOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_WeYoundAssistTrapSync_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_WeYoundAssistTrapSync_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.WeYoundAssistTrapSync.class, xddq.pb.WeYoundAssistTrapSync.Builder.class);
    }

    // Construct using xddq.pb.WeYoundAssistTrapSync.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetPlayerDataFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      playerData_ = null;
      if (playerDataBuilder_ != null) {
        playerDataBuilder_.dispose();
        playerDataBuilder_ = null;
      }
      playerId_ = 0L;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_WeYoundAssistTrapSync_descriptor;
    }

    @java.lang.Override
    public xddq.pb.WeYoundAssistTrapSync getDefaultInstanceForType() {
      return xddq.pb.WeYoundAssistTrapSync.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.WeYoundAssistTrapSync build() {
      xddq.pb.WeYoundAssistTrapSync result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.WeYoundAssistTrapSync buildPartial() {
      xddq.pb.WeYoundAssistTrapSync result = new xddq.pb.WeYoundAssistTrapSync(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.WeYoundAssistTrapSync result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.playerData_ = playerDataBuilder_ == null
            ? playerData_
            : playerDataBuilder_.build();
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.playerId_ = playerId_;
        to_bitField0_ |= 0x00000002;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.WeYoundAssistTrapSync) {
        return mergeFrom((xddq.pb.WeYoundAssistTrapSync)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.WeYoundAssistTrapSync other) {
      if (other == xddq.pb.WeYoundAssistTrapSync.getDefaultInstance()) return this;
      if (other.hasPlayerData()) {
        mergePlayerData(other.getPlayerData());
      }
      if (other.hasPlayerId()) {
        setPlayerId(other.getPlayerId());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              input.readMessage(
                  internalGetPlayerDataFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 16: {
              playerId_ = input.readInt64();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private xddq.pb.PlayerCharacterImageMsg playerData_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PlayerCharacterImageMsg, xddq.pb.PlayerCharacterImageMsg.Builder, xddq.pb.PlayerCharacterImageMsgOrBuilder> playerDataBuilder_;
    /**
     * <code>optional .xddq.pb.PlayerCharacterImageMsg playerData = 1;</code>
     * @return Whether the playerData field is set.
     */
    public boolean hasPlayerData() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .xddq.pb.PlayerCharacterImageMsg playerData = 1;</code>
     * @return The playerData.
     */
    public xddq.pb.PlayerCharacterImageMsg getPlayerData() {
      if (playerDataBuilder_ == null) {
        return playerData_ == null ? xddq.pb.PlayerCharacterImageMsg.getDefaultInstance() : playerData_;
      } else {
        return playerDataBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.PlayerCharacterImageMsg playerData = 1;</code>
     */
    public Builder setPlayerData(xddq.pb.PlayerCharacterImageMsg value) {
      if (playerDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        playerData_ = value;
      } else {
        playerDataBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerCharacterImageMsg playerData = 1;</code>
     */
    public Builder setPlayerData(
        xddq.pb.PlayerCharacterImageMsg.Builder builderForValue) {
      if (playerDataBuilder_ == null) {
        playerData_ = builderForValue.build();
      } else {
        playerDataBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerCharacterImageMsg playerData = 1;</code>
     */
    public Builder mergePlayerData(xddq.pb.PlayerCharacterImageMsg value) {
      if (playerDataBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0) &&
          playerData_ != null &&
          playerData_ != xddq.pb.PlayerCharacterImageMsg.getDefaultInstance()) {
          getPlayerDataBuilder().mergeFrom(value);
        } else {
          playerData_ = value;
        }
      } else {
        playerDataBuilder_.mergeFrom(value);
      }
      if (playerData_ != null) {
        bitField0_ |= 0x00000001;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerCharacterImageMsg playerData = 1;</code>
     */
    public Builder clearPlayerData() {
      bitField0_ = (bitField0_ & ~0x00000001);
      playerData_ = null;
      if (playerDataBuilder_ != null) {
        playerDataBuilder_.dispose();
        playerDataBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerCharacterImageMsg playerData = 1;</code>
     */
    public xddq.pb.PlayerCharacterImageMsg.Builder getPlayerDataBuilder() {
      bitField0_ |= 0x00000001;
      onChanged();
      return internalGetPlayerDataFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.PlayerCharacterImageMsg playerData = 1;</code>
     */
    public xddq.pb.PlayerCharacterImageMsgOrBuilder getPlayerDataOrBuilder() {
      if (playerDataBuilder_ != null) {
        return playerDataBuilder_.getMessageOrBuilder();
      } else {
        return playerData_ == null ?
            xddq.pb.PlayerCharacterImageMsg.getDefaultInstance() : playerData_;
      }
    }
    /**
     * <code>optional .xddq.pb.PlayerCharacterImageMsg playerData = 1;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PlayerCharacterImageMsg, xddq.pb.PlayerCharacterImageMsg.Builder, xddq.pb.PlayerCharacterImageMsgOrBuilder> 
        internalGetPlayerDataFieldBuilder() {
      if (playerDataBuilder_ == null) {
        playerDataBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.PlayerCharacterImageMsg, xddq.pb.PlayerCharacterImageMsg.Builder, xddq.pb.PlayerCharacterImageMsgOrBuilder>(
                getPlayerData(),
                getParentForChildren(),
                isClean());
        playerData_ = null;
      }
      return playerDataBuilder_;
    }

    private long playerId_ ;
    /**
     * <code>optional int64 playerId = 2;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int64 playerId = 2;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }
    /**
     * <code>optional int64 playerId = 2;</code>
     * @param value The playerId to set.
     * @return This builder for chaining.
     */
    public Builder setPlayerId(long value) {

      playerId_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 playerId = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearPlayerId() {
      bitField0_ = (bitField0_ & ~0x00000002);
      playerId_ = 0L;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.WeYoundAssistTrapSync)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.WeYoundAssistTrapSync)
  private static final xddq.pb.WeYoundAssistTrapSync DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.WeYoundAssistTrapSync();
  }

  public static xddq.pb.WeYoundAssistTrapSync getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<WeYoundAssistTrapSync>
      PARSER = new com.google.protobuf.AbstractParser<WeYoundAssistTrapSync>() {
    @java.lang.Override
    public WeYoundAssistTrapSync parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<WeYoundAssistTrapSync> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<WeYoundAssistTrapSync> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.WeYoundAssistTrapSync getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

