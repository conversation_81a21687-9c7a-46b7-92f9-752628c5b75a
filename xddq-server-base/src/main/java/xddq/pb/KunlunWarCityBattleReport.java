// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.KunlunWarCityBattleReport}
 */
public final class KunlunWarCityBattleReport extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.KunlunWarCityBattleReport)
    KunlunWarCityBattleReportOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      KunlunWarCityBattleReport.class.getName());
  }
  // Use KunlunWarCityBattleReport.newBuilder() to construct.
  private KunlunWarCityBattleReport(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private KunlunWarCityBattleReport() {
    enemyUnionName_ = "";
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_KunlunWarCityBattleReport_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_KunlunWarCityBattleReport_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.KunlunWarCityBattleReport.class, xddq.pb.KunlunWarCityBattleReport.Builder.class);
  }

  private int bitField0_;
  public static final int REPORTUID_FIELD_NUMBER = 1;
  private long reportUid_ = 0L;
  /**
   * <code>optional int64 reportUid = 1;</code>
   * @return Whether the reportUid field is set.
   */
  @java.lang.Override
  public boolean hasReportUid() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int64 reportUid = 1;</code>
   * @return The reportUid.
   */
  @java.lang.Override
  public long getReportUid() {
    return reportUid_;
  }

  public static final int WIN_FIELD_NUMBER = 2;
  private boolean win_ = false;
  /**
   * <code>optional bool win = 2;</code>
   * @return Whether the win field is set.
   */
  @java.lang.Override
  public boolean hasWin() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional bool win = 2;</code>
   * @return The win.
   */
  @java.lang.Override
  public boolean getWin() {
    return win_;
  }

  public static final int TIME_FIELD_NUMBER = 3;
  private long time_ = 0L;
  /**
   * <code>optional int64 time = 3;</code>
   * @return Whether the time field is set.
   */
  @java.lang.Override
  public boolean hasTime() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional int64 time = 3;</code>
   * @return The time.
   */
  @java.lang.Override
  public long getTime() {
    return time_;
  }

  public static final int CITYID_FIELD_NUMBER = 4;
  private int cityId_ = 0;
  /**
   * <code>optional int32 cityId = 4;</code>
   * @return Whether the cityId field is set.
   */
  @java.lang.Override
  public boolean hasCityId() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional int32 cityId = 4;</code>
   * @return The cityId.
   */
  @java.lang.Override
  public int getCityId() {
    return cityId_;
  }

  public static final int ENEMYUNIONSID_FIELD_NUMBER = 5;
  private long enemyUnionSid_ = 0L;
  /**
   * <code>optional int64 enemyUnionSid = 5;</code>
   * @return Whether the enemyUnionSid field is set.
   */
  @java.lang.Override
  public boolean hasEnemyUnionSid() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>optional int64 enemyUnionSid = 5;</code>
   * @return The enemyUnionSid.
   */
  @java.lang.Override
  public long getEnemyUnionSid() {
    return enemyUnionSid_;
  }

  public static final int ENEMYUNIONNAME_FIELD_NUMBER = 6;
  @SuppressWarnings("serial")
  private volatile java.lang.Object enemyUnionName_ = "";
  /**
   * <code>optional string enemyUnionName = 6;</code>
   * @return Whether the enemyUnionName field is set.
   */
  @java.lang.Override
  public boolean hasEnemyUnionName() {
    return ((bitField0_ & 0x00000020) != 0);
  }
  /**
   * <code>optional string enemyUnionName = 6;</code>
   * @return The enemyUnionName.
   */
  @java.lang.Override
  public java.lang.String getEnemyUnionName() {
    java.lang.Object ref = enemyUnionName_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        enemyUnionName_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string enemyUnionName = 6;</code>
   * @return The bytes for enemyUnionName.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getEnemyUnionNameBytes() {
    java.lang.Object ref = enemyUnionName_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      enemyUnionName_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int DETAILCOUNT_FIELD_NUMBER = 8;
  private int detailCount_ = 0;
  /**
   * <code>optional int32 detailCount = 8;</code>
   * @return Whether the detailCount field is set.
   */
  @java.lang.Override
  public boolean hasDetailCount() {
    return ((bitField0_ & 0x00000040) != 0);
  }
  /**
   * <code>optional int32 detailCount = 8;</code>
   * @return The detailCount.
   */
  @java.lang.Override
  public int getDetailCount() {
    return detailCount_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt64(1, reportUid_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeBool(2, win_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt64(3, time_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt32(4, cityId_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      output.writeInt64(5, enemyUnionSid_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 6, enemyUnionName_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      output.writeInt32(8, detailCount_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(1, reportUid_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(2, win_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(3, time_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(4, cityId_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(5, enemyUnionSid_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(6, enemyUnionName_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(8, detailCount_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.KunlunWarCityBattleReport)) {
      return super.equals(obj);
    }
    xddq.pb.KunlunWarCityBattleReport other = (xddq.pb.KunlunWarCityBattleReport) obj;

    if (hasReportUid() != other.hasReportUid()) return false;
    if (hasReportUid()) {
      if (getReportUid()
          != other.getReportUid()) return false;
    }
    if (hasWin() != other.hasWin()) return false;
    if (hasWin()) {
      if (getWin()
          != other.getWin()) return false;
    }
    if (hasTime() != other.hasTime()) return false;
    if (hasTime()) {
      if (getTime()
          != other.getTime()) return false;
    }
    if (hasCityId() != other.hasCityId()) return false;
    if (hasCityId()) {
      if (getCityId()
          != other.getCityId()) return false;
    }
    if (hasEnemyUnionSid() != other.hasEnemyUnionSid()) return false;
    if (hasEnemyUnionSid()) {
      if (getEnemyUnionSid()
          != other.getEnemyUnionSid()) return false;
    }
    if (hasEnemyUnionName() != other.hasEnemyUnionName()) return false;
    if (hasEnemyUnionName()) {
      if (!getEnemyUnionName()
          .equals(other.getEnemyUnionName())) return false;
    }
    if (hasDetailCount() != other.hasDetailCount()) return false;
    if (hasDetailCount()) {
      if (getDetailCount()
          != other.getDetailCount()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasReportUid()) {
      hash = (37 * hash) + REPORTUID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getReportUid());
    }
    if (hasWin()) {
      hash = (37 * hash) + WIN_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getWin());
    }
    if (hasTime()) {
      hash = (37 * hash) + TIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTime());
    }
    if (hasCityId()) {
      hash = (37 * hash) + CITYID_FIELD_NUMBER;
      hash = (53 * hash) + getCityId();
    }
    if (hasEnemyUnionSid()) {
      hash = (37 * hash) + ENEMYUNIONSID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getEnemyUnionSid());
    }
    if (hasEnemyUnionName()) {
      hash = (37 * hash) + ENEMYUNIONNAME_FIELD_NUMBER;
      hash = (53 * hash) + getEnemyUnionName().hashCode();
    }
    if (hasDetailCount()) {
      hash = (37 * hash) + DETAILCOUNT_FIELD_NUMBER;
      hash = (53 * hash) + getDetailCount();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.KunlunWarCityBattleReport parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.KunlunWarCityBattleReport parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.KunlunWarCityBattleReport parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.KunlunWarCityBattleReport parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.KunlunWarCityBattleReport parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.KunlunWarCityBattleReport parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.KunlunWarCityBattleReport parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.KunlunWarCityBattleReport parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.KunlunWarCityBattleReport parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.KunlunWarCityBattleReport parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.KunlunWarCityBattleReport parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.KunlunWarCityBattleReport parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.KunlunWarCityBattleReport prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.KunlunWarCityBattleReport}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.KunlunWarCityBattleReport)
      xddq.pb.KunlunWarCityBattleReportOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_KunlunWarCityBattleReport_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_KunlunWarCityBattleReport_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.KunlunWarCityBattleReport.class, xddq.pb.KunlunWarCityBattleReport.Builder.class);
    }

    // Construct using xddq.pb.KunlunWarCityBattleReport.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      reportUid_ = 0L;
      win_ = false;
      time_ = 0L;
      cityId_ = 0;
      enemyUnionSid_ = 0L;
      enemyUnionName_ = "";
      detailCount_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_KunlunWarCityBattleReport_descriptor;
    }

    @java.lang.Override
    public xddq.pb.KunlunWarCityBattleReport getDefaultInstanceForType() {
      return xddq.pb.KunlunWarCityBattleReport.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.KunlunWarCityBattleReport build() {
      xddq.pb.KunlunWarCityBattleReport result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.KunlunWarCityBattleReport buildPartial() {
      xddq.pb.KunlunWarCityBattleReport result = new xddq.pb.KunlunWarCityBattleReport(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.KunlunWarCityBattleReport result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.reportUid_ = reportUid_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.win_ = win_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.time_ = time_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.cityId_ = cityId_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.enemyUnionSid_ = enemyUnionSid_;
        to_bitField0_ |= 0x00000010;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.enemyUnionName_ = enemyUnionName_;
        to_bitField0_ |= 0x00000020;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.detailCount_ = detailCount_;
        to_bitField0_ |= 0x00000040;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.KunlunWarCityBattleReport) {
        return mergeFrom((xddq.pb.KunlunWarCityBattleReport)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.KunlunWarCityBattleReport other) {
      if (other == xddq.pb.KunlunWarCityBattleReport.getDefaultInstance()) return this;
      if (other.hasReportUid()) {
        setReportUid(other.getReportUid());
      }
      if (other.hasWin()) {
        setWin(other.getWin());
      }
      if (other.hasTime()) {
        setTime(other.getTime());
      }
      if (other.hasCityId()) {
        setCityId(other.getCityId());
      }
      if (other.hasEnemyUnionSid()) {
        setEnemyUnionSid(other.getEnemyUnionSid());
      }
      if (other.hasEnemyUnionName()) {
        enemyUnionName_ = other.enemyUnionName_;
        bitField0_ |= 0x00000020;
        onChanged();
      }
      if (other.hasDetailCount()) {
        setDetailCount(other.getDetailCount());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              reportUid_ = input.readInt64();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              win_ = input.readBool();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 24: {
              time_ = input.readInt64();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 32: {
              cityId_ = input.readInt32();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            case 40: {
              enemyUnionSid_ = input.readInt64();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            case 50: {
              enemyUnionName_ = input.readBytes();
              bitField0_ |= 0x00000020;
              break;
            } // case 50
            case 64: {
              detailCount_ = input.readInt32();
              bitField0_ |= 0x00000040;
              break;
            } // case 64
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private long reportUid_ ;
    /**
     * <code>optional int64 reportUid = 1;</code>
     * @return Whether the reportUid field is set.
     */
    @java.lang.Override
    public boolean hasReportUid() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 reportUid = 1;</code>
     * @return The reportUid.
     */
    @java.lang.Override
    public long getReportUid() {
      return reportUid_;
    }
    /**
     * <code>optional int64 reportUid = 1;</code>
     * @param value The reportUid to set.
     * @return This builder for chaining.
     */
    public Builder setReportUid(long value) {

      reportUid_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 reportUid = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearReportUid() {
      bitField0_ = (bitField0_ & ~0x00000001);
      reportUid_ = 0L;
      onChanged();
      return this;
    }

    private boolean win_ ;
    /**
     * <code>optional bool win = 2;</code>
     * @return Whether the win field is set.
     */
    @java.lang.Override
    public boolean hasWin() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional bool win = 2;</code>
     * @return The win.
     */
    @java.lang.Override
    public boolean getWin() {
      return win_;
    }
    /**
     * <code>optional bool win = 2;</code>
     * @param value The win to set.
     * @return This builder for chaining.
     */
    public Builder setWin(boolean value) {

      win_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional bool win = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearWin() {
      bitField0_ = (bitField0_ & ~0x00000002);
      win_ = false;
      onChanged();
      return this;
    }

    private long time_ ;
    /**
     * <code>optional int64 time = 3;</code>
     * @return Whether the time field is set.
     */
    @java.lang.Override
    public boolean hasTime() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int64 time = 3;</code>
     * @return The time.
     */
    @java.lang.Override
    public long getTime() {
      return time_;
    }
    /**
     * <code>optional int64 time = 3;</code>
     * @param value The time to set.
     * @return This builder for chaining.
     */
    public Builder setTime(long value) {

      time_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 time = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearTime() {
      bitField0_ = (bitField0_ & ~0x00000004);
      time_ = 0L;
      onChanged();
      return this;
    }

    private int cityId_ ;
    /**
     * <code>optional int32 cityId = 4;</code>
     * @return Whether the cityId field is set.
     */
    @java.lang.Override
    public boolean hasCityId() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int32 cityId = 4;</code>
     * @return The cityId.
     */
    @java.lang.Override
    public int getCityId() {
      return cityId_;
    }
    /**
     * <code>optional int32 cityId = 4;</code>
     * @param value The cityId to set.
     * @return This builder for chaining.
     */
    public Builder setCityId(int value) {

      cityId_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 cityId = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearCityId() {
      bitField0_ = (bitField0_ & ~0x00000008);
      cityId_ = 0;
      onChanged();
      return this;
    }

    private long enemyUnionSid_ ;
    /**
     * <code>optional int64 enemyUnionSid = 5;</code>
     * @return Whether the enemyUnionSid field is set.
     */
    @java.lang.Override
    public boolean hasEnemyUnionSid() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional int64 enemyUnionSid = 5;</code>
     * @return The enemyUnionSid.
     */
    @java.lang.Override
    public long getEnemyUnionSid() {
      return enemyUnionSid_;
    }
    /**
     * <code>optional int64 enemyUnionSid = 5;</code>
     * @param value The enemyUnionSid to set.
     * @return This builder for chaining.
     */
    public Builder setEnemyUnionSid(long value) {

      enemyUnionSid_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 enemyUnionSid = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearEnemyUnionSid() {
      bitField0_ = (bitField0_ & ~0x00000010);
      enemyUnionSid_ = 0L;
      onChanged();
      return this;
    }

    private java.lang.Object enemyUnionName_ = "";
    /**
     * <code>optional string enemyUnionName = 6;</code>
     * @return Whether the enemyUnionName field is set.
     */
    public boolean hasEnemyUnionName() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional string enemyUnionName = 6;</code>
     * @return The enemyUnionName.
     */
    public java.lang.String getEnemyUnionName() {
      java.lang.Object ref = enemyUnionName_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          enemyUnionName_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string enemyUnionName = 6;</code>
     * @return The bytes for enemyUnionName.
     */
    public com.google.protobuf.ByteString
        getEnemyUnionNameBytes() {
      java.lang.Object ref = enemyUnionName_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        enemyUnionName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string enemyUnionName = 6;</code>
     * @param value The enemyUnionName to set.
     * @return This builder for chaining.
     */
    public Builder setEnemyUnionName(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      enemyUnionName_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>optional string enemyUnionName = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearEnemyUnionName() {
      enemyUnionName_ = getDefaultInstance().getEnemyUnionName();
      bitField0_ = (bitField0_ & ~0x00000020);
      onChanged();
      return this;
    }
    /**
     * <code>optional string enemyUnionName = 6;</code>
     * @param value The bytes for enemyUnionName to set.
     * @return This builder for chaining.
     */
    public Builder setEnemyUnionNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      enemyUnionName_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }

    private int detailCount_ ;
    /**
     * <code>optional int32 detailCount = 8;</code>
     * @return Whether the detailCount field is set.
     */
    @java.lang.Override
    public boolean hasDetailCount() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <code>optional int32 detailCount = 8;</code>
     * @return The detailCount.
     */
    @java.lang.Override
    public int getDetailCount() {
      return detailCount_;
    }
    /**
     * <code>optional int32 detailCount = 8;</code>
     * @param value The detailCount to set.
     * @return This builder for chaining.
     */
    public Builder setDetailCount(int value) {

      detailCount_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 detailCount = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearDetailCount() {
      bitField0_ = (bitField0_ & ~0x00000040);
      detailCount_ = 0;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.KunlunWarCityBattleReport)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.KunlunWarCityBattleReport)
  private static final xddq.pb.KunlunWarCityBattleReport DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.KunlunWarCityBattleReport();
  }

  public static xddq.pb.KunlunWarCityBattleReport getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<KunlunWarCityBattleReport>
      PARSER = new com.google.protobuf.AbstractParser<KunlunWarCityBattleReport>() {
    @java.lang.Override
    public KunlunWarCityBattleReport parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<KunlunWarCityBattleReport> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<KunlunWarCityBattleReport> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.KunlunWarCityBattleReport getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

