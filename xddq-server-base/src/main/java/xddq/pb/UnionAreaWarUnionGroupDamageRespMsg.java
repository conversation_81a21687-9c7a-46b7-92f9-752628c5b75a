// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.UnionAreaWarUnionGroupDamageRespMsg}
 */
public final class UnionAreaWarUnionGroupDamageRespMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.UnionAreaWarUnionGroupDamageRespMsg)
    UnionAreaWarUnionGroupDamageRespMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      UnionAreaWarUnionGroupDamageRespMsg.class.getName());
  }
  // Use UnionAreaWarUnionGroupDamageRespMsg.newBuilder() to construct.
  private UnionAreaWarUnionGroupDamageRespMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private UnionAreaWarUnionGroupDamageRespMsg() {
    unionName_ = "";
    info_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_UnionAreaWarUnionGroupDamageRespMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_UnionAreaWarUnionGroupDamageRespMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.UnionAreaWarUnionGroupDamageRespMsg.class, xddq.pb.UnionAreaWarUnionGroupDamageRespMsg.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int UNIONID_FIELD_NUMBER = 2;
  private long unionId_ = 0L;
  /**
   * <code>optional int64 unionId = 2;</code>
   * @return Whether the unionId field is set.
   */
  @java.lang.Override
  public boolean hasUnionId() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int64 unionId = 2;</code>
   * @return The unionId.
   */
  @java.lang.Override
  public long getUnionId() {
    return unionId_;
  }

  public static final int UNIONNAME_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private volatile java.lang.Object unionName_ = "";
  /**
   * <code>optional string unionName = 3;</code>
   * @return Whether the unionName field is set.
   */
  @java.lang.Override
  public boolean hasUnionName() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional string unionName = 3;</code>
   * @return The unionName.
   */
  @java.lang.Override
  public java.lang.String getUnionName() {
    java.lang.Object ref = unionName_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        unionName_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string unionName = 3;</code>
   * @return The bytes for unionName.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getUnionNameBytes() {
    java.lang.Object ref = unionName_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      unionName_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int INFO_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.UnionAreaWarGroupBaseInfo> info_;
  /**
   * <code>repeated .xddq.pb.UnionAreaWarGroupBaseInfo info = 4;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.UnionAreaWarGroupBaseInfo> getInfoList() {
    return info_;
  }
  /**
   * <code>repeated .xddq.pb.UnionAreaWarGroupBaseInfo info = 4;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.UnionAreaWarGroupBaseInfoOrBuilder> 
      getInfoOrBuilderList() {
    return info_;
  }
  /**
   * <code>repeated .xddq.pb.UnionAreaWarGroupBaseInfo info = 4;</code>
   */
  @java.lang.Override
  public int getInfoCount() {
    return info_.size();
  }
  /**
   * <code>repeated .xddq.pb.UnionAreaWarGroupBaseInfo info = 4;</code>
   */
  @java.lang.Override
  public xddq.pb.UnionAreaWarGroupBaseInfo getInfo(int index) {
    return info_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.UnionAreaWarGroupBaseInfo info = 4;</code>
   */
  @java.lang.Override
  public xddq.pb.UnionAreaWarGroupBaseInfoOrBuilder getInfoOrBuilder(
      int index) {
    return info_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    for (int i = 0; i < getInfoCount(); i++) {
      if (!getInfo(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt64(2, unionId_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 3, unionName_);
    }
    for (int i = 0; i < info_.size(); i++) {
      output.writeMessage(4, info_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(2, unionId_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(3, unionName_);
    }
    for (int i = 0; i < info_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(4, info_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.UnionAreaWarUnionGroupDamageRespMsg)) {
      return super.equals(obj);
    }
    xddq.pb.UnionAreaWarUnionGroupDamageRespMsg other = (xddq.pb.UnionAreaWarUnionGroupDamageRespMsg) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (hasUnionId() != other.hasUnionId()) return false;
    if (hasUnionId()) {
      if (getUnionId()
          != other.getUnionId()) return false;
    }
    if (hasUnionName() != other.hasUnionName()) return false;
    if (hasUnionName()) {
      if (!getUnionName()
          .equals(other.getUnionName())) return false;
    }
    if (!getInfoList()
        .equals(other.getInfoList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (hasUnionId()) {
      hash = (37 * hash) + UNIONID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getUnionId());
    }
    if (hasUnionName()) {
      hash = (37 * hash) + UNIONNAME_FIELD_NUMBER;
      hash = (53 * hash) + getUnionName().hashCode();
    }
    if (getInfoCount() > 0) {
      hash = (37 * hash) + INFO_FIELD_NUMBER;
      hash = (53 * hash) + getInfoList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.UnionAreaWarUnionGroupDamageRespMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.UnionAreaWarUnionGroupDamageRespMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.UnionAreaWarUnionGroupDamageRespMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.UnionAreaWarUnionGroupDamageRespMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.UnionAreaWarUnionGroupDamageRespMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.UnionAreaWarUnionGroupDamageRespMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.UnionAreaWarUnionGroupDamageRespMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.UnionAreaWarUnionGroupDamageRespMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.UnionAreaWarUnionGroupDamageRespMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.UnionAreaWarUnionGroupDamageRespMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.UnionAreaWarUnionGroupDamageRespMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.UnionAreaWarUnionGroupDamageRespMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.UnionAreaWarUnionGroupDamageRespMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.UnionAreaWarUnionGroupDamageRespMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.UnionAreaWarUnionGroupDamageRespMsg)
      xddq.pb.UnionAreaWarUnionGroupDamageRespMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_UnionAreaWarUnionGroupDamageRespMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_UnionAreaWarUnionGroupDamageRespMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.UnionAreaWarUnionGroupDamageRespMsg.class, xddq.pb.UnionAreaWarUnionGroupDamageRespMsg.Builder.class);
    }

    // Construct using xddq.pb.UnionAreaWarUnionGroupDamageRespMsg.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      unionId_ = 0L;
      unionName_ = "";
      if (infoBuilder_ == null) {
        info_ = java.util.Collections.emptyList();
      } else {
        info_ = null;
        infoBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000008);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_UnionAreaWarUnionGroupDamageRespMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.UnionAreaWarUnionGroupDamageRespMsg getDefaultInstanceForType() {
      return xddq.pb.UnionAreaWarUnionGroupDamageRespMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.UnionAreaWarUnionGroupDamageRespMsg build() {
      xddq.pb.UnionAreaWarUnionGroupDamageRespMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.UnionAreaWarUnionGroupDamageRespMsg buildPartial() {
      xddq.pb.UnionAreaWarUnionGroupDamageRespMsg result = new xddq.pb.UnionAreaWarUnionGroupDamageRespMsg(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.UnionAreaWarUnionGroupDamageRespMsg result) {
      if (infoBuilder_ == null) {
        if (((bitField0_ & 0x00000008) != 0)) {
          info_ = java.util.Collections.unmodifiableList(info_);
          bitField0_ = (bitField0_ & ~0x00000008);
        }
        result.info_ = info_;
      } else {
        result.info_ = infoBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.UnionAreaWarUnionGroupDamageRespMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.unionId_ = unionId_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.unionName_ = unionName_;
        to_bitField0_ |= 0x00000004;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.UnionAreaWarUnionGroupDamageRespMsg) {
        return mergeFrom((xddq.pb.UnionAreaWarUnionGroupDamageRespMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.UnionAreaWarUnionGroupDamageRespMsg other) {
      if (other == xddq.pb.UnionAreaWarUnionGroupDamageRespMsg.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (other.hasUnionId()) {
        setUnionId(other.getUnionId());
      }
      if (other.hasUnionName()) {
        unionName_ = other.unionName_;
        bitField0_ |= 0x00000004;
        onChanged();
      }
      if (infoBuilder_ == null) {
        if (!other.info_.isEmpty()) {
          if (info_.isEmpty()) {
            info_ = other.info_;
            bitField0_ = (bitField0_ & ~0x00000008);
          } else {
            ensureInfoIsMutable();
            info_.addAll(other.info_);
          }
          onChanged();
        }
      } else {
        if (!other.info_.isEmpty()) {
          if (infoBuilder_.isEmpty()) {
            infoBuilder_.dispose();
            infoBuilder_ = null;
            info_ = other.info_;
            bitField0_ = (bitField0_ & ~0x00000008);
            infoBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetInfoFieldBuilder() : null;
          } else {
            infoBuilder_.addAllMessages(other.info_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      for (int i = 0; i < getInfoCount(); i++) {
        if (!getInfo(i).isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              unionId_ = input.readInt64();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 26: {
              unionName_ = input.readBytes();
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 34: {
              xddq.pb.UnionAreaWarGroupBaseInfo m =
                  input.readMessage(
                      xddq.pb.UnionAreaWarGroupBaseInfo.parser(),
                      extensionRegistry);
              if (infoBuilder_ == null) {
                ensureInfoIsMutable();
                info_.add(m);
              } else {
                infoBuilder_.addMessage(m);
              }
              break;
            } // case 34
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private long unionId_ ;
    /**
     * <code>optional int64 unionId = 2;</code>
     * @return Whether the unionId field is set.
     */
    @java.lang.Override
    public boolean hasUnionId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int64 unionId = 2;</code>
     * @return The unionId.
     */
    @java.lang.Override
    public long getUnionId() {
      return unionId_;
    }
    /**
     * <code>optional int64 unionId = 2;</code>
     * @param value The unionId to set.
     * @return This builder for chaining.
     */
    public Builder setUnionId(long value) {

      unionId_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 unionId = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearUnionId() {
      bitField0_ = (bitField0_ & ~0x00000002);
      unionId_ = 0L;
      onChanged();
      return this;
    }

    private java.lang.Object unionName_ = "";
    /**
     * <code>optional string unionName = 3;</code>
     * @return Whether the unionName field is set.
     */
    public boolean hasUnionName() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional string unionName = 3;</code>
     * @return The unionName.
     */
    public java.lang.String getUnionName() {
      java.lang.Object ref = unionName_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          unionName_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string unionName = 3;</code>
     * @return The bytes for unionName.
     */
    public com.google.protobuf.ByteString
        getUnionNameBytes() {
      java.lang.Object ref = unionName_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        unionName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string unionName = 3;</code>
     * @param value The unionName to set.
     * @return This builder for chaining.
     */
    public Builder setUnionName(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      unionName_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional string unionName = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearUnionName() {
      unionName_ = getDefaultInstance().getUnionName();
      bitField0_ = (bitField0_ & ~0x00000004);
      onChanged();
      return this;
    }
    /**
     * <code>optional string unionName = 3;</code>
     * @param value The bytes for unionName to set.
     * @return This builder for chaining.
     */
    public Builder setUnionNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      unionName_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.UnionAreaWarGroupBaseInfo> info_ =
      java.util.Collections.emptyList();
    private void ensureInfoIsMutable() {
      if (!((bitField0_ & 0x00000008) != 0)) {
        info_ = new java.util.ArrayList<xddq.pb.UnionAreaWarGroupBaseInfo>(info_);
        bitField0_ |= 0x00000008;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.UnionAreaWarGroupBaseInfo, xddq.pb.UnionAreaWarGroupBaseInfo.Builder, xddq.pb.UnionAreaWarGroupBaseInfoOrBuilder> infoBuilder_;

    /**
     * <code>repeated .xddq.pb.UnionAreaWarGroupBaseInfo info = 4;</code>
     */
    public java.util.List<xddq.pb.UnionAreaWarGroupBaseInfo> getInfoList() {
      if (infoBuilder_ == null) {
        return java.util.Collections.unmodifiableList(info_);
      } else {
        return infoBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.UnionAreaWarGroupBaseInfo info = 4;</code>
     */
    public int getInfoCount() {
      if (infoBuilder_ == null) {
        return info_.size();
      } else {
        return infoBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.UnionAreaWarGroupBaseInfo info = 4;</code>
     */
    public xddq.pb.UnionAreaWarGroupBaseInfo getInfo(int index) {
      if (infoBuilder_ == null) {
        return info_.get(index);
      } else {
        return infoBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.UnionAreaWarGroupBaseInfo info = 4;</code>
     */
    public Builder setInfo(
        int index, xddq.pb.UnionAreaWarGroupBaseInfo value) {
      if (infoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureInfoIsMutable();
        info_.set(index, value);
        onChanged();
      } else {
        infoBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionAreaWarGroupBaseInfo info = 4;</code>
     */
    public Builder setInfo(
        int index, xddq.pb.UnionAreaWarGroupBaseInfo.Builder builderForValue) {
      if (infoBuilder_ == null) {
        ensureInfoIsMutable();
        info_.set(index, builderForValue.build());
        onChanged();
      } else {
        infoBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionAreaWarGroupBaseInfo info = 4;</code>
     */
    public Builder addInfo(xddq.pb.UnionAreaWarGroupBaseInfo value) {
      if (infoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureInfoIsMutable();
        info_.add(value);
        onChanged();
      } else {
        infoBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionAreaWarGroupBaseInfo info = 4;</code>
     */
    public Builder addInfo(
        int index, xddq.pb.UnionAreaWarGroupBaseInfo value) {
      if (infoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureInfoIsMutable();
        info_.add(index, value);
        onChanged();
      } else {
        infoBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionAreaWarGroupBaseInfo info = 4;</code>
     */
    public Builder addInfo(
        xddq.pb.UnionAreaWarGroupBaseInfo.Builder builderForValue) {
      if (infoBuilder_ == null) {
        ensureInfoIsMutable();
        info_.add(builderForValue.build());
        onChanged();
      } else {
        infoBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionAreaWarGroupBaseInfo info = 4;</code>
     */
    public Builder addInfo(
        int index, xddq.pb.UnionAreaWarGroupBaseInfo.Builder builderForValue) {
      if (infoBuilder_ == null) {
        ensureInfoIsMutable();
        info_.add(index, builderForValue.build());
        onChanged();
      } else {
        infoBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionAreaWarGroupBaseInfo info = 4;</code>
     */
    public Builder addAllInfo(
        java.lang.Iterable<? extends xddq.pb.UnionAreaWarGroupBaseInfo> values) {
      if (infoBuilder_ == null) {
        ensureInfoIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, info_);
        onChanged();
      } else {
        infoBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionAreaWarGroupBaseInfo info = 4;</code>
     */
    public Builder clearInfo() {
      if (infoBuilder_ == null) {
        info_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000008);
        onChanged();
      } else {
        infoBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionAreaWarGroupBaseInfo info = 4;</code>
     */
    public Builder removeInfo(int index) {
      if (infoBuilder_ == null) {
        ensureInfoIsMutable();
        info_.remove(index);
        onChanged();
      } else {
        infoBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionAreaWarGroupBaseInfo info = 4;</code>
     */
    public xddq.pb.UnionAreaWarGroupBaseInfo.Builder getInfoBuilder(
        int index) {
      return internalGetInfoFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.UnionAreaWarGroupBaseInfo info = 4;</code>
     */
    public xddq.pb.UnionAreaWarGroupBaseInfoOrBuilder getInfoOrBuilder(
        int index) {
      if (infoBuilder_ == null) {
        return info_.get(index);  } else {
        return infoBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.UnionAreaWarGroupBaseInfo info = 4;</code>
     */
    public java.util.List<? extends xddq.pb.UnionAreaWarGroupBaseInfoOrBuilder> 
         getInfoOrBuilderList() {
      if (infoBuilder_ != null) {
        return infoBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(info_);
      }
    }
    /**
     * <code>repeated .xddq.pb.UnionAreaWarGroupBaseInfo info = 4;</code>
     */
    public xddq.pb.UnionAreaWarGroupBaseInfo.Builder addInfoBuilder() {
      return internalGetInfoFieldBuilder().addBuilder(
          xddq.pb.UnionAreaWarGroupBaseInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.UnionAreaWarGroupBaseInfo info = 4;</code>
     */
    public xddq.pb.UnionAreaWarGroupBaseInfo.Builder addInfoBuilder(
        int index) {
      return internalGetInfoFieldBuilder().addBuilder(
          index, xddq.pb.UnionAreaWarGroupBaseInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.UnionAreaWarGroupBaseInfo info = 4;</code>
     */
    public java.util.List<xddq.pb.UnionAreaWarGroupBaseInfo.Builder> 
         getInfoBuilderList() {
      return internalGetInfoFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.UnionAreaWarGroupBaseInfo, xddq.pb.UnionAreaWarGroupBaseInfo.Builder, xddq.pb.UnionAreaWarGroupBaseInfoOrBuilder> 
        internalGetInfoFieldBuilder() {
      if (infoBuilder_ == null) {
        infoBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.UnionAreaWarGroupBaseInfo, xddq.pb.UnionAreaWarGroupBaseInfo.Builder, xddq.pb.UnionAreaWarGroupBaseInfoOrBuilder>(
                info_,
                ((bitField0_ & 0x00000008) != 0),
                getParentForChildren(),
                isClean());
        info_ = null;
      }
      return infoBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.UnionAreaWarUnionGroupDamageRespMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.UnionAreaWarUnionGroupDamageRespMsg)
  private static final xddq.pb.UnionAreaWarUnionGroupDamageRespMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.UnionAreaWarUnionGroupDamageRespMsg();
  }

  public static xddq.pb.UnionAreaWarUnionGroupDamageRespMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<UnionAreaWarUnionGroupDamageRespMsg>
      PARSER = new com.google.protobuf.AbstractParser<UnionAreaWarUnionGroupDamageRespMsg>() {
    @java.lang.Override
    public UnionAreaWarUnionGroupDamageRespMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<UnionAreaWarUnionGroupDamageRespMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<UnionAreaWarUnionGroupDamageRespMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.UnionAreaWarUnionGroupDamageRespMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

