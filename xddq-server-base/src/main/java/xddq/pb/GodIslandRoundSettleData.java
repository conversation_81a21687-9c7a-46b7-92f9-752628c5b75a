// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.GodIslandRoundSettleData}
 */
public final class GodIslandRoundSettleData extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.GodIslandRoundSettleData)
    GodIslandRoundSettleDataOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      GodIslandRoundSettleData.class.getName());
  }
  // Use GodIslandRoundSettleData.newBuilder() to construct.
  private GodIslandRoundSettleData(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private GodIslandRoundSettleData() {
    unionName_ = "";
    firstUnionName_ = "";
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_GodIslandRoundSettleData_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_GodIslandRoundSettleData_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.GodIslandRoundSettleData.class, xddq.pb.GodIslandRoundSettleData.Builder.class);
  }

  private int bitField0_;
  public static final int ROUND_FIELD_NUMBER = 1;
  private int round_ = 0;
  /**
   * <code>optional int32 round = 1;</code>
   * @return Whether the round field is set.
   */
  @java.lang.Override
  public boolean hasRound() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int32 round = 1;</code>
   * @return The round.
   */
  @java.lang.Override
  public int getRound() {
    return round_;
  }

  public static final int STATUES_FIELD_NUMBER = 2;
  private int statues_ = 0;
  /**
   * <code>optional int32 statues = 2;</code>
   * @return Whether the statues field is set.
   */
  @java.lang.Override
  public boolean hasStatues() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int32 statues = 2;</code>
   * @return The statues.
   */
  @java.lang.Override
  public int getStatues() {
    return statues_;
  }

  public static final int GROUPRANK_FIELD_NUMBER = 3;
  private int groupRank_ = 0;
  /**
   * <code>optional int32 groupRank = 3;</code>
   * @return Whether the groupRank field is set.
   */
  @java.lang.Override
  public boolean hasGroupRank() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional int32 groupRank = 3;</code>
   * @return The groupRank.
   */
  @java.lang.Override
  public int getGroupRank() {
    return groupRank_;
  }

  public static final int RANK_FIELD_NUMBER = 4;
  private int rank_ = 0;
  /**
   * <code>optional int32 rank = 4;</code>
   * @return Whether the rank field is set.
   */
  @java.lang.Override
  public boolean hasRank() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional int32 rank = 4;</code>
   * @return The rank.
   */
  @java.lang.Override
  public int getRank() {
    return rank_;
  }

  public static final int RANKSCORE_FIELD_NUMBER = 5;
  private int rankScore_ = 0;
  /**
   * <code>optional int32 rankScore = 5;</code>
   * @return Whether the rankScore field is set.
   */
  @java.lang.Override
  public boolean hasRankScore() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>optional int32 rankScore = 5;</code>
   * @return The rankScore.
   */
  @java.lang.Override
  public int getRankScore() {
    return rankScore_;
  }

  public static final int TOTALSCORE_FIELD_NUMBER = 6;
  private int totalScore_ = 0;
  /**
   * <code>optional int32 totalScore = 6;</code>
   * @return Whether the totalScore field is set.
   */
  @java.lang.Override
  public boolean hasTotalScore() {
    return ((bitField0_ & 0x00000020) != 0);
  }
  /**
   * <code>optional int32 totalScore = 6;</code>
   * @return The totalScore.
   */
  @java.lang.Override
  public int getTotalScore() {
    return totalScore_;
  }

  public static final int CITYCOUNT_FIELD_NUMBER = 7;
  private int cityCount_ = 0;
  /**
   * <code>optional int32 cityCount = 7;</code>
   * @return Whether the cityCount field is set.
   */
  @java.lang.Override
  public boolean hasCityCount() {
    return ((bitField0_ & 0x00000040) != 0);
  }
  /**
   * <code>optional int32 cityCount = 7;</code>
   * @return The cityCount.
   */
  @java.lang.Override
  public int getCityCount() {
    return cityCount_;
  }

  public static final int PLAYERKILLCOUNT_FIELD_NUMBER = 8;
  private int playerKillCount_ = 0;
  /**
   * <code>optional int32 playerKillCount = 8;</code>
   * @return Whether the playerKillCount field is set.
   */
  @java.lang.Override
  public boolean hasPlayerKillCount() {
    return ((bitField0_ & 0x00000080) != 0);
  }
  /**
   * <code>optional int32 playerKillCount = 8;</code>
   * @return The playerKillCount.
   */
  @java.lang.Override
  public int getPlayerKillCount() {
    return playerKillCount_;
  }

  public static final int UNIONNAME_FIELD_NUMBER = 9;
  @SuppressWarnings("serial")
  private volatile java.lang.Object unionName_ = "";
  /**
   * <code>optional string unionName = 9;</code>
   * @return Whether the unionName field is set.
   */
  @java.lang.Override
  public boolean hasUnionName() {
    return ((bitField0_ & 0x00000100) != 0);
  }
  /**
   * <code>optional string unionName = 9;</code>
   * @return The unionName.
   */
  @java.lang.Override
  public java.lang.String getUnionName() {
    java.lang.Object ref = unionName_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        unionName_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string unionName = 9;</code>
   * @return The bytes for unionName.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getUnionNameBytes() {
    java.lang.Object ref = unionName_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      unionName_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int FIRST_UNIONNAME_FIELD_NUMBER = 10;
  @SuppressWarnings("serial")
  private volatile java.lang.Object firstUnionName_ = "";
  /**
   * <code>optional string first_unionName = 10;</code>
   * @return Whether the firstUnionName field is set.
   */
  @java.lang.Override
  public boolean hasFirstUnionName() {
    return ((bitField0_ & 0x00000200) != 0);
  }
  /**
   * <code>optional string first_unionName = 10;</code>
   * @return The firstUnionName.
   */
  @java.lang.Override
  public java.lang.String getFirstUnionName() {
    java.lang.Object ref = firstUnionName_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        firstUnionName_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string first_unionName = 10;</code>
   * @return The bytes for firstUnionName.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getFirstUnionNameBytes() {
    java.lang.Object ref = firstUnionName_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      firstUnionName_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int FIRST_SERVERID_FIELD_NUMBER = 11;
  private long firstServerId_ = 0L;
  /**
   * <code>optional int64 first_serverId = 11;</code>
   * @return Whether the firstServerId field is set.
   */
  @java.lang.Override
  public boolean hasFirstServerId() {
    return ((bitField0_ & 0x00000400) != 0);
  }
  /**
   * <code>optional int64 first_serverId = 11;</code>
   * @return The firstServerId.
   */
  @java.lang.Override
  public long getFirstServerId() {
    return firstServerId_;
  }

  public static final int FIRST_UNIONFLAG_FIELD_NUMBER = 12;
  private long firstUnionFlag_ = 0L;
  /**
   * <code>optional int64 first_unionFlag = 12;</code>
   * @return Whether the firstUnionFlag field is set.
   */
  @java.lang.Override
  public boolean hasFirstUnionFlag() {
    return ((bitField0_ & 0x00000800) != 0);
  }
  /**
   * <code>optional int64 first_unionFlag = 12;</code>
   * @return The firstUnionFlag.
   */
  @java.lang.Override
  public long getFirstUnionFlag() {
    return firstUnionFlag_;
  }

  public static final int FLOOR_FIELD_NUMBER = 13;
  private int floor_ = 0;
  /**
   * <code>optional int32 floor = 13;</code>
   * @return Whether the floor field is set.
   */
  @java.lang.Override
  public boolean hasFloor() {
    return ((bitField0_ & 0x00001000) != 0);
  }
  /**
   * <code>optional int32 floor = 13;</code>
   * @return The floor.
   */
  @java.lang.Override
  public int getFloor() {
    return floor_;
  }

  public static final int GROUP_FIELD_NUMBER = 14;
  private int group_ = 0;
  /**
   * <code>optional int32 group = 14;</code>
   * @return Whether the group field is set.
   */
  @java.lang.Override
  public boolean hasGroup() {
    return ((bitField0_ & 0x00002000) != 0);
  }
  /**
   * <code>optional int32 group = 14;</code>
   * @return The group.
   */
  @java.lang.Override
  public int getGroup() {
    return group_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, round_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(2, statues_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt32(3, groupRank_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt32(4, rank_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      output.writeInt32(5, rankScore_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      output.writeInt32(6, totalScore_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      output.writeInt32(7, cityCount_);
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      output.writeInt32(8, playerKillCount_);
    }
    if (((bitField0_ & 0x00000100) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 9, unionName_);
    }
    if (((bitField0_ & 0x00000200) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 10, firstUnionName_);
    }
    if (((bitField0_ & 0x00000400) != 0)) {
      output.writeInt64(11, firstServerId_);
    }
    if (((bitField0_ & 0x00000800) != 0)) {
      output.writeInt64(12, firstUnionFlag_);
    }
    if (((bitField0_ & 0x00001000) != 0)) {
      output.writeInt32(13, floor_);
    }
    if (((bitField0_ & 0x00002000) != 0)) {
      output.writeInt32(14, group_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, round_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, statues_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(3, groupRank_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(4, rank_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(5, rankScore_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(6, totalScore_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(7, cityCount_);
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(8, playerKillCount_);
    }
    if (((bitField0_ & 0x00000100) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(9, unionName_);
    }
    if (((bitField0_ & 0x00000200) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(10, firstUnionName_);
    }
    if (((bitField0_ & 0x00000400) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(11, firstServerId_);
    }
    if (((bitField0_ & 0x00000800) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(12, firstUnionFlag_);
    }
    if (((bitField0_ & 0x00001000) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(13, floor_);
    }
    if (((bitField0_ & 0x00002000) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(14, group_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.GodIslandRoundSettleData)) {
      return super.equals(obj);
    }
    xddq.pb.GodIslandRoundSettleData other = (xddq.pb.GodIslandRoundSettleData) obj;

    if (hasRound() != other.hasRound()) return false;
    if (hasRound()) {
      if (getRound()
          != other.getRound()) return false;
    }
    if (hasStatues() != other.hasStatues()) return false;
    if (hasStatues()) {
      if (getStatues()
          != other.getStatues()) return false;
    }
    if (hasGroupRank() != other.hasGroupRank()) return false;
    if (hasGroupRank()) {
      if (getGroupRank()
          != other.getGroupRank()) return false;
    }
    if (hasRank() != other.hasRank()) return false;
    if (hasRank()) {
      if (getRank()
          != other.getRank()) return false;
    }
    if (hasRankScore() != other.hasRankScore()) return false;
    if (hasRankScore()) {
      if (getRankScore()
          != other.getRankScore()) return false;
    }
    if (hasTotalScore() != other.hasTotalScore()) return false;
    if (hasTotalScore()) {
      if (getTotalScore()
          != other.getTotalScore()) return false;
    }
    if (hasCityCount() != other.hasCityCount()) return false;
    if (hasCityCount()) {
      if (getCityCount()
          != other.getCityCount()) return false;
    }
    if (hasPlayerKillCount() != other.hasPlayerKillCount()) return false;
    if (hasPlayerKillCount()) {
      if (getPlayerKillCount()
          != other.getPlayerKillCount()) return false;
    }
    if (hasUnionName() != other.hasUnionName()) return false;
    if (hasUnionName()) {
      if (!getUnionName()
          .equals(other.getUnionName())) return false;
    }
    if (hasFirstUnionName() != other.hasFirstUnionName()) return false;
    if (hasFirstUnionName()) {
      if (!getFirstUnionName()
          .equals(other.getFirstUnionName())) return false;
    }
    if (hasFirstServerId() != other.hasFirstServerId()) return false;
    if (hasFirstServerId()) {
      if (getFirstServerId()
          != other.getFirstServerId()) return false;
    }
    if (hasFirstUnionFlag() != other.hasFirstUnionFlag()) return false;
    if (hasFirstUnionFlag()) {
      if (getFirstUnionFlag()
          != other.getFirstUnionFlag()) return false;
    }
    if (hasFloor() != other.hasFloor()) return false;
    if (hasFloor()) {
      if (getFloor()
          != other.getFloor()) return false;
    }
    if (hasGroup() != other.hasGroup()) return false;
    if (hasGroup()) {
      if (getGroup()
          != other.getGroup()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRound()) {
      hash = (37 * hash) + ROUND_FIELD_NUMBER;
      hash = (53 * hash) + getRound();
    }
    if (hasStatues()) {
      hash = (37 * hash) + STATUES_FIELD_NUMBER;
      hash = (53 * hash) + getStatues();
    }
    if (hasGroupRank()) {
      hash = (37 * hash) + GROUPRANK_FIELD_NUMBER;
      hash = (53 * hash) + getGroupRank();
    }
    if (hasRank()) {
      hash = (37 * hash) + RANK_FIELD_NUMBER;
      hash = (53 * hash) + getRank();
    }
    if (hasRankScore()) {
      hash = (37 * hash) + RANKSCORE_FIELD_NUMBER;
      hash = (53 * hash) + getRankScore();
    }
    if (hasTotalScore()) {
      hash = (37 * hash) + TOTALSCORE_FIELD_NUMBER;
      hash = (53 * hash) + getTotalScore();
    }
    if (hasCityCount()) {
      hash = (37 * hash) + CITYCOUNT_FIELD_NUMBER;
      hash = (53 * hash) + getCityCount();
    }
    if (hasPlayerKillCount()) {
      hash = (37 * hash) + PLAYERKILLCOUNT_FIELD_NUMBER;
      hash = (53 * hash) + getPlayerKillCount();
    }
    if (hasUnionName()) {
      hash = (37 * hash) + UNIONNAME_FIELD_NUMBER;
      hash = (53 * hash) + getUnionName().hashCode();
    }
    if (hasFirstUnionName()) {
      hash = (37 * hash) + FIRST_UNIONNAME_FIELD_NUMBER;
      hash = (53 * hash) + getFirstUnionName().hashCode();
    }
    if (hasFirstServerId()) {
      hash = (37 * hash) + FIRST_SERVERID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getFirstServerId());
    }
    if (hasFirstUnionFlag()) {
      hash = (37 * hash) + FIRST_UNIONFLAG_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getFirstUnionFlag());
    }
    if (hasFloor()) {
      hash = (37 * hash) + FLOOR_FIELD_NUMBER;
      hash = (53 * hash) + getFloor();
    }
    if (hasGroup()) {
      hash = (37 * hash) + GROUP_FIELD_NUMBER;
      hash = (53 * hash) + getGroup();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.GodIslandRoundSettleData parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GodIslandRoundSettleData parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GodIslandRoundSettleData parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GodIslandRoundSettleData parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GodIslandRoundSettleData parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GodIslandRoundSettleData parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GodIslandRoundSettleData parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.GodIslandRoundSettleData parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.GodIslandRoundSettleData parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.GodIslandRoundSettleData parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.GodIslandRoundSettleData parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.GodIslandRoundSettleData parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.GodIslandRoundSettleData prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.GodIslandRoundSettleData}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.GodIslandRoundSettleData)
      xddq.pb.GodIslandRoundSettleDataOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GodIslandRoundSettleData_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GodIslandRoundSettleData_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.GodIslandRoundSettleData.class, xddq.pb.GodIslandRoundSettleData.Builder.class);
    }

    // Construct using xddq.pb.GodIslandRoundSettleData.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      round_ = 0;
      statues_ = 0;
      groupRank_ = 0;
      rank_ = 0;
      rankScore_ = 0;
      totalScore_ = 0;
      cityCount_ = 0;
      playerKillCount_ = 0;
      unionName_ = "";
      firstUnionName_ = "";
      firstServerId_ = 0L;
      firstUnionFlag_ = 0L;
      floor_ = 0;
      group_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GodIslandRoundSettleData_descriptor;
    }

    @java.lang.Override
    public xddq.pb.GodIslandRoundSettleData getDefaultInstanceForType() {
      return xddq.pb.GodIslandRoundSettleData.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.GodIslandRoundSettleData build() {
      xddq.pb.GodIslandRoundSettleData result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.GodIslandRoundSettleData buildPartial() {
      xddq.pb.GodIslandRoundSettleData result = new xddq.pb.GodIslandRoundSettleData(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.GodIslandRoundSettleData result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.round_ = round_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.statues_ = statues_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.groupRank_ = groupRank_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.rank_ = rank_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.rankScore_ = rankScore_;
        to_bitField0_ |= 0x00000010;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.totalScore_ = totalScore_;
        to_bitField0_ |= 0x00000020;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.cityCount_ = cityCount_;
        to_bitField0_ |= 0x00000040;
      }
      if (((from_bitField0_ & 0x00000080) != 0)) {
        result.playerKillCount_ = playerKillCount_;
        to_bitField0_ |= 0x00000080;
      }
      if (((from_bitField0_ & 0x00000100) != 0)) {
        result.unionName_ = unionName_;
        to_bitField0_ |= 0x00000100;
      }
      if (((from_bitField0_ & 0x00000200) != 0)) {
        result.firstUnionName_ = firstUnionName_;
        to_bitField0_ |= 0x00000200;
      }
      if (((from_bitField0_ & 0x00000400) != 0)) {
        result.firstServerId_ = firstServerId_;
        to_bitField0_ |= 0x00000400;
      }
      if (((from_bitField0_ & 0x00000800) != 0)) {
        result.firstUnionFlag_ = firstUnionFlag_;
        to_bitField0_ |= 0x00000800;
      }
      if (((from_bitField0_ & 0x00001000) != 0)) {
        result.floor_ = floor_;
        to_bitField0_ |= 0x00001000;
      }
      if (((from_bitField0_ & 0x00002000) != 0)) {
        result.group_ = group_;
        to_bitField0_ |= 0x00002000;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.GodIslandRoundSettleData) {
        return mergeFrom((xddq.pb.GodIslandRoundSettleData)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.GodIslandRoundSettleData other) {
      if (other == xddq.pb.GodIslandRoundSettleData.getDefaultInstance()) return this;
      if (other.hasRound()) {
        setRound(other.getRound());
      }
      if (other.hasStatues()) {
        setStatues(other.getStatues());
      }
      if (other.hasGroupRank()) {
        setGroupRank(other.getGroupRank());
      }
      if (other.hasRank()) {
        setRank(other.getRank());
      }
      if (other.hasRankScore()) {
        setRankScore(other.getRankScore());
      }
      if (other.hasTotalScore()) {
        setTotalScore(other.getTotalScore());
      }
      if (other.hasCityCount()) {
        setCityCount(other.getCityCount());
      }
      if (other.hasPlayerKillCount()) {
        setPlayerKillCount(other.getPlayerKillCount());
      }
      if (other.hasUnionName()) {
        unionName_ = other.unionName_;
        bitField0_ |= 0x00000100;
        onChanged();
      }
      if (other.hasFirstUnionName()) {
        firstUnionName_ = other.firstUnionName_;
        bitField0_ |= 0x00000200;
        onChanged();
      }
      if (other.hasFirstServerId()) {
        setFirstServerId(other.getFirstServerId());
      }
      if (other.hasFirstUnionFlag()) {
        setFirstUnionFlag(other.getFirstUnionFlag());
      }
      if (other.hasFloor()) {
        setFloor(other.getFloor());
      }
      if (other.hasGroup()) {
        setGroup(other.getGroup());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              round_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              statues_ = input.readInt32();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 24: {
              groupRank_ = input.readInt32();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 32: {
              rank_ = input.readInt32();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            case 40: {
              rankScore_ = input.readInt32();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            case 48: {
              totalScore_ = input.readInt32();
              bitField0_ |= 0x00000020;
              break;
            } // case 48
            case 56: {
              cityCount_ = input.readInt32();
              bitField0_ |= 0x00000040;
              break;
            } // case 56
            case 64: {
              playerKillCount_ = input.readInt32();
              bitField0_ |= 0x00000080;
              break;
            } // case 64
            case 74: {
              unionName_ = input.readBytes();
              bitField0_ |= 0x00000100;
              break;
            } // case 74
            case 82: {
              firstUnionName_ = input.readBytes();
              bitField0_ |= 0x00000200;
              break;
            } // case 82
            case 88: {
              firstServerId_ = input.readInt64();
              bitField0_ |= 0x00000400;
              break;
            } // case 88
            case 96: {
              firstUnionFlag_ = input.readInt64();
              bitField0_ |= 0x00000800;
              break;
            } // case 96
            case 104: {
              floor_ = input.readInt32();
              bitField0_ |= 0x00001000;
              break;
            } // case 104
            case 112: {
              group_ = input.readInt32();
              bitField0_ |= 0x00002000;
              break;
            } // case 112
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int round_ ;
    /**
     * <code>optional int32 round = 1;</code>
     * @return Whether the round field is set.
     */
    @java.lang.Override
    public boolean hasRound() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 round = 1;</code>
     * @return The round.
     */
    @java.lang.Override
    public int getRound() {
      return round_;
    }
    /**
     * <code>optional int32 round = 1;</code>
     * @param value The round to set.
     * @return This builder for chaining.
     */
    public Builder setRound(int value) {

      round_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 round = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRound() {
      bitField0_ = (bitField0_ & ~0x00000001);
      round_ = 0;
      onChanged();
      return this;
    }

    private int statues_ ;
    /**
     * <code>optional int32 statues = 2;</code>
     * @return Whether the statues field is set.
     */
    @java.lang.Override
    public boolean hasStatues() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 statues = 2;</code>
     * @return The statues.
     */
    @java.lang.Override
    public int getStatues() {
      return statues_;
    }
    /**
     * <code>optional int32 statues = 2;</code>
     * @param value The statues to set.
     * @return This builder for chaining.
     */
    public Builder setStatues(int value) {

      statues_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 statues = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearStatues() {
      bitField0_ = (bitField0_ & ~0x00000002);
      statues_ = 0;
      onChanged();
      return this;
    }

    private int groupRank_ ;
    /**
     * <code>optional int32 groupRank = 3;</code>
     * @return Whether the groupRank field is set.
     */
    @java.lang.Override
    public boolean hasGroupRank() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int32 groupRank = 3;</code>
     * @return The groupRank.
     */
    @java.lang.Override
    public int getGroupRank() {
      return groupRank_;
    }
    /**
     * <code>optional int32 groupRank = 3;</code>
     * @param value The groupRank to set.
     * @return This builder for chaining.
     */
    public Builder setGroupRank(int value) {

      groupRank_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 groupRank = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearGroupRank() {
      bitField0_ = (bitField0_ & ~0x00000004);
      groupRank_ = 0;
      onChanged();
      return this;
    }

    private int rank_ ;
    /**
     * <code>optional int32 rank = 4;</code>
     * @return Whether the rank field is set.
     */
    @java.lang.Override
    public boolean hasRank() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int32 rank = 4;</code>
     * @return The rank.
     */
    @java.lang.Override
    public int getRank() {
      return rank_;
    }
    /**
     * <code>optional int32 rank = 4;</code>
     * @param value The rank to set.
     * @return This builder for chaining.
     */
    public Builder setRank(int value) {

      rank_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 rank = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearRank() {
      bitField0_ = (bitField0_ & ~0x00000008);
      rank_ = 0;
      onChanged();
      return this;
    }

    private int rankScore_ ;
    /**
     * <code>optional int32 rankScore = 5;</code>
     * @return Whether the rankScore field is set.
     */
    @java.lang.Override
    public boolean hasRankScore() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional int32 rankScore = 5;</code>
     * @return The rankScore.
     */
    @java.lang.Override
    public int getRankScore() {
      return rankScore_;
    }
    /**
     * <code>optional int32 rankScore = 5;</code>
     * @param value The rankScore to set.
     * @return This builder for chaining.
     */
    public Builder setRankScore(int value) {

      rankScore_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 rankScore = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearRankScore() {
      bitField0_ = (bitField0_ & ~0x00000010);
      rankScore_ = 0;
      onChanged();
      return this;
    }

    private int totalScore_ ;
    /**
     * <code>optional int32 totalScore = 6;</code>
     * @return Whether the totalScore field is set.
     */
    @java.lang.Override
    public boolean hasTotalScore() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional int32 totalScore = 6;</code>
     * @return The totalScore.
     */
    @java.lang.Override
    public int getTotalScore() {
      return totalScore_;
    }
    /**
     * <code>optional int32 totalScore = 6;</code>
     * @param value The totalScore to set.
     * @return This builder for chaining.
     */
    public Builder setTotalScore(int value) {

      totalScore_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 totalScore = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearTotalScore() {
      bitField0_ = (bitField0_ & ~0x00000020);
      totalScore_ = 0;
      onChanged();
      return this;
    }

    private int cityCount_ ;
    /**
     * <code>optional int32 cityCount = 7;</code>
     * @return Whether the cityCount field is set.
     */
    @java.lang.Override
    public boolean hasCityCount() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <code>optional int32 cityCount = 7;</code>
     * @return The cityCount.
     */
    @java.lang.Override
    public int getCityCount() {
      return cityCount_;
    }
    /**
     * <code>optional int32 cityCount = 7;</code>
     * @param value The cityCount to set.
     * @return This builder for chaining.
     */
    public Builder setCityCount(int value) {

      cityCount_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 cityCount = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearCityCount() {
      bitField0_ = (bitField0_ & ~0x00000040);
      cityCount_ = 0;
      onChanged();
      return this;
    }

    private int playerKillCount_ ;
    /**
     * <code>optional int32 playerKillCount = 8;</code>
     * @return Whether the playerKillCount field is set.
     */
    @java.lang.Override
    public boolean hasPlayerKillCount() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <code>optional int32 playerKillCount = 8;</code>
     * @return The playerKillCount.
     */
    @java.lang.Override
    public int getPlayerKillCount() {
      return playerKillCount_;
    }
    /**
     * <code>optional int32 playerKillCount = 8;</code>
     * @param value The playerKillCount to set.
     * @return This builder for chaining.
     */
    public Builder setPlayerKillCount(int value) {

      playerKillCount_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 playerKillCount = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearPlayerKillCount() {
      bitField0_ = (bitField0_ & ~0x00000080);
      playerKillCount_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object unionName_ = "";
    /**
     * <code>optional string unionName = 9;</code>
     * @return Whether the unionName field is set.
     */
    public boolean hasUnionName() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <code>optional string unionName = 9;</code>
     * @return The unionName.
     */
    public java.lang.String getUnionName() {
      java.lang.Object ref = unionName_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          unionName_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string unionName = 9;</code>
     * @return The bytes for unionName.
     */
    public com.google.protobuf.ByteString
        getUnionNameBytes() {
      java.lang.Object ref = unionName_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        unionName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string unionName = 9;</code>
     * @param value The unionName to set.
     * @return This builder for chaining.
     */
    public Builder setUnionName(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      unionName_ = value;
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }
    /**
     * <code>optional string unionName = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearUnionName() {
      unionName_ = getDefaultInstance().getUnionName();
      bitField0_ = (bitField0_ & ~0x00000100);
      onChanged();
      return this;
    }
    /**
     * <code>optional string unionName = 9;</code>
     * @param value The bytes for unionName to set.
     * @return This builder for chaining.
     */
    public Builder setUnionNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      unionName_ = value;
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }

    private java.lang.Object firstUnionName_ = "";
    /**
     * <code>optional string first_unionName = 10;</code>
     * @return Whether the firstUnionName field is set.
     */
    public boolean hasFirstUnionName() {
      return ((bitField0_ & 0x00000200) != 0);
    }
    /**
     * <code>optional string first_unionName = 10;</code>
     * @return The firstUnionName.
     */
    public java.lang.String getFirstUnionName() {
      java.lang.Object ref = firstUnionName_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          firstUnionName_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string first_unionName = 10;</code>
     * @return The bytes for firstUnionName.
     */
    public com.google.protobuf.ByteString
        getFirstUnionNameBytes() {
      java.lang.Object ref = firstUnionName_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        firstUnionName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string first_unionName = 10;</code>
     * @param value The firstUnionName to set.
     * @return This builder for chaining.
     */
    public Builder setFirstUnionName(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      firstUnionName_ = value;
      bitField0_ |= 0x00000200;
      onChanged();
      return this;
    }
    /**
     * <code>optional string first_unionName = 10;</code>
     * @return This builder for chaining.
     */
    public Builder clearFirstUnionName() {
      firstUnionName_ = getDefaultInstance().getFirstUnionName();
      bitField0_ = (bitField0_ & ~0x00000200);
      onChanged();
      return this;
    }
    /**
     * <code>optional string first_unionName = 10;</code>
     * @param value The bytes for firstUnionName to set.
     * @return This builder for chaining.
     */
    public Builder setFirstUnionNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      firstUnionName_ = value;
      bitField0_ |= 0x00000200;
      onChanged();
      return this;
    }

    private long firstServerId_ ;
    /**
     * <code>optional int64 first_serverId = 11;</code>
     * @return Whether the firstServerId field is set.
     */
    @java.lang.Override
    public boolean hasFirstServerId() {
      return ((bitField0_ & 0x00000400) != 0);
    }
    /**
     * <code>optional int64 first_serverId = 11;</code>
     * @return The firstServerId.
     */
    @java.lang.Override
    public long getFirstServerId() {
      return firstServerId_;
    }
    /**
     * <code>optional int64 first_serverId = 11;</code>
     * @param value The firstServerId to set.
     * @return This builder for chaining.
     */
    public Builder setFirstServerId(long value) {

      firstServerId_ = value;
      bitField0_ |= 0x00000400;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 first_serverId = 11;</code>
     * @return This builder for chaining.
     */
    public Builder clearFirstServerId() {
      bitField0_ = (bitField0_ & ~0x00000400);
      firstServerId_ = 0L;
      onChanged();
      return this;
    }

    private long firstUnionFlag_ ;
    /**
     * <code>optional int64 first_unionFlag = 12;</code>
     * @return Whether the firstUnionFlag field is set.
     */
    @java.lang.Override
    public boolean hasFirstUnionFlag() {
      return ((bitField0_ & 0x00000800) != 0);
    }
    /**
     * <code>optional int64 first_unionFlag = 12;</code>
     * @return The firstUnionFlag.
     */
    @java.lang.Override
    public long getFirstUnionFlag() {
      return firstUnionFlag_;
    }
    /**
     * <code>optional int64 first_unionFlag = 12;</code>
     * @param value The firstUnionFlag to set.
     * @return This builder for chaining.
     */
    public Builder setFirstUnionFlag(long value) {

      firstUnionFlag_ = value;
      bitField0_ |= 0x00000800;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 first_unionFlag = 12;</code>
     * @return This builder for chaining.
     */
    public Builder clearFirstUnionFlag() {
      bitField0_ = (bitField0_ & ~0x00000800);
      firstUnionFlag_ = 0L;
      onChanged();
      return this;
    }

    private int floor_ ;
    /**
     * <code>optional int32 floor = 13;</code>
     * @return Whether the floor field is set.
     */
    @java.lang.Override
    public boolean hasFloor() {
      return ((bitField0_ & 0x00001000) != 0);
    }
    /**
     * <code>optional int32 floor = 13;</code>
     * @return The floor.
     */
    @java.lang.Override
    public int getFloor() {
      return floor_;
    }
    /**
     * <code>optional int32 floor = 13;</code>
     * @param value The floor to set.
     * @return This builder for chaining.
     */
    public Builder setFloor(int value) {

      floor_ = value;
      bitField0_ |= 0x00001000;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 floor = 13;</code>
     * @return This builder for chaining.
     */
    public Builder clearFloor() {
      bitField0_ = (bitField0_ & ~0x00001000);
      floor_ = 0;
      onChanged();
      return this;
    }

    private int group_ ;
    /**
     * <code>optional int32 group = 14;</code>
     * @return Whether the group field is set.
     */
    @java.lang.Override
    public boolean hasGroup() {
      return ((bitField0_ & 0x00002000) != 0);
    }
    /**
     * <code>optional int32 group = 14;</code>
     * @return The group.
     */
    @java.lang.Override
    public int getGroup() {
      return group_;
    }
    /**
     * <code>optional int32 group = 14;</code>
     * @param value The group to set.
     * @return This builder for chaining.
     */
    public Builder setGroup(int value) {

      group_ = value;
      bitField0_ |= 0x00002000;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 group = 14;</code>
     * @return This builder for chaining.
     */
    public Builder clearGroup() {
      bitField0_ = (bitField0_ & ~0x00002000);
      group_ = 0;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.GodIslandRoundSettleData)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.GodIslandRoundSettleData)
  private static final xddq.pb.GodIslandRoundSettleData DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.GodIslandRoundSettleData();
  }

  public static xddq.pb.GodIslandRoundSettleData getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<GodIslandRoundSettleData>
      PARSER = new com.google.protobuf.AbstractParser<GodIslandRoundSettleData>() {
    @java.lang.Override
    public GodIslandRoundSettleData parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<GodIslandRoundSettleData> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<GodIslandRoundSettleData> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.GodIslandRoundSettleData getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

