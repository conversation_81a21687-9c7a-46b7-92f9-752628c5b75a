// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.AskDingEndDataMsg}
 */
public final class AskDingEndDataMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.AskDingEndDataMsg)
    AskDingEndDataMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      AskDingEndDataMsg.class.getName());
  }
  // Use AskDingEndDataMsg.newBuilder() to construct.
  private AskDingEndDataMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private AskDingEndDataMsg() {
    unionResult_ = java.util.Collections.emptyList();
    memberRecord_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_AskDingEndDataMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_AskDingEndDataMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.AskDingEndDataMsg.class, xddq.pb.AskDingEndDataMsg.Builder.class);
  }

  private int bitField0_;
  public static final int UNIONRESULT_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.AskDingUnionScoreMsg> unionResult_;
  /**
   * <code>repeated .xddq.pb.AskDingUnionScoreMsg unionResult = 1;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.AskDingUnionScoreMsg> getUnionResultList() {
    return unionResult_;
  }
  /**
   * <code>repeated .xddq.pb.AskDingUnionScoreMsg unionResult = 1;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.AskDingUnionScoreMsgOrBuilder> 
      getUnionResultOrBuilderList() {
    return unionResult_;
  }
  /**
   * <code>repeated .xddq.pb.AskDingUnionScoreMsg unionResult = 1;</code>
   */
  @java.lang.Override
  public int getUnionResultCount() {
    return unionResult_.size();
  }
  /**
   * <code>repeated .xddq.pb.AskDingUnionScoreMsg unionResult = 1;</code>
   */
  @java.lang.Override
  public xddq.pb.AskDingUnionScoreMsg getUnionResult(int index) {
    return unionResult_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.AskDingUnionScoreMsg unionResult = 1;</code>
   */
  @java.lang.Override
  public xddq.pb.AskDingUnionScoreMsgOrBuilder getUnionResultOrBuilder(
      int index) {
    return unionResult_.get(index);
  }

  public static final int TIMEID_FIELD_NUMBER = 2;
  private int timeId_ = 0;
  /**
   * <code>optional int32 timeId = 2;</code>
   * @return Whether the timeId field is set.
   */
  @java.lang.Override
  public boolean hasTimeId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int32 timeId = 2;</code>
   * @return The timeId.
   */
  @java.lang.Override
  public int getTimeId() {
    return timeId_;
  }

  public static final int TOTALKILLBESTINFO_FIELD_NUMBER = 3;
  private xddq.pb.AskDingBestPerformancePlayerMsg totalKillBestInfo_;
  /**
   * <code>optional .xddq.pb.AskDingBestPerformancePlayerMsg totalKillBestInfo = 3;</code>
   * @return Whether the totalKillBestInfo field is set.
   */
  @java.lang.Override
  public boolean hasTotalKillBestInfo() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional .xddq.pb.AskDingBestPerformancePlayerMsg totalKillBestInfo = 3;</code>
   * @return The totalKillBestInfo.
   */
  @java.lang.Override
  public xddq.pb.AskDingBestPerformancePlayerMsg getTotalKillBestInfo() {
    return totalKillBestInfo_ == null ? xddq.pb.AskDingBestPerformancePlayerMsg.getDefaultInstance() : totalKillBestInfo_;
  }
  /**
   * <code>optional .xddq.pb.AskDingBestPerformancePlayerMsg totalKillBestInfo = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.AskDingBestPerformancePlayerMsgOrBuilder getTotalKillBestInfoOrBuilder() {
    return totalKillBestInfo_ == null ? xddq.pb.AskDingBestPerformancePlayerMsg.getDefaultInstance() : totalKillBestInfo_;
  }

  public static final int TOTALSEIZEBESTINFO_FIELD_NUMBER = 4;
  private xddq.pb.AskDingBestPerformancePlayerMsg totalSeizeBestInfo_;
  /**
   * <code>optional .xddq.pb.AskDingBestPerformancePlayerMsg totalSeizeBestInfo = 4;</code>
   * @return Whether the totalSeizeBestInfo field is set.
   */
  @java.lang.Override
  public boolean hasTotalSeizeBestInfo() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional .xddq.pb.AskDingBestPerformancePlayerMsg totalSeizeBestInfo = 4;</code>
   * @return The totalSeizeBestInfo.
   */
  @java.lang.Override
  public xddq.pb.AskDingBestPerformancePlayerMsg getTotalSeizeBestInfo() {
    return totalSeizeBestInfo_ == null ? xddq.pb.AskDingBestPerformancePlayerMsg.getDefaultInstance() : totalSeizeBestInfo_;
  }
  /**
   * <code>optional .xddq.pb.AskDingBestPerformancePlayerMsg totalSeizeBestInfo = 4;</code>
   */
  @java.lang.Override
  public xddq.pb.AskDingBestPerformancePlayerMsgOrBuilder getTotalSeizeBestInfoOrBuilder() {
    return totalSeizeBestInfo_ == null ? xddq.pb.AskDingBestPerformancePlayerMsg.getDefaultInstance() : totalSeizeBestInfo_;
  }

  public static final int UNIONKILLBESTINFO_FIELD_NUMBER = 5;
  private xddq.pb.AskDingBestPerformancePlayerMsg unionKillBestInfo_;
  /**
   * <code>optional .xddq.pb.AskDingBestPerformancePlayerMsg unionKillBestInfo = 5;</code>
   * @return Whether the unionKillBestInfo field is set.
   */
  @java.lang.Override
  public boolean hasUnionKillBestInfo() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional .xddq.pb.AskDingBestPerformancePlayerMsg unionKillBestInfo = 5;</code>
   * @return The unionKillBestInfo.
   */
  @java.lang.Override
  public xddq.pb.AskDingBestPerformancePlayerMsg getUnionKillBestInfo() {
    return unionKillBestInfo_ == null ? xddq.pb.AskDingBestPerformancePlayerMsg.getDefaultInstance() : unionKillBestInfo_;
  }
  /**
   * <code>optional .xddq.pb.AskDingBestPerformancePlayerMsg unionKillBestInfo = 5;</code>
   */
  @java.lang.Override
  public xddq.pb.AskDingBestPerformancePlayerMsgOrBuilder getUnionKillBestInfoOrBuilder() {
    return unionKillBestInfo_ == null ? xddq.pb.AskDingBestPerformancePlayerMsg.getDefaultInstance() : unionKillBestInfo_;
  }

  public static final int UNIONSEIZEBESTINFO_FIELD_NUMBER = 6;
  private xddq.pb.AskDingBestPerformancePlayerMsg unionSeizeBestInfo_;
  /**
   * <code>optional .xddq.pb.AskDingBestPerformancePlayerMsg unionSeizeBestInfo = 6;</code>
   * @return Whether the unionSeizeBestInfo field is set.
   */
  @java.lang.Override
  public boolean hasUnionSeizeBestInfo() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>optional .xddq.pb.AskDingBestPerformancePlayerMsg unionSeizeBestInfo = 6;</code>
   * @return The unionSeizeBestInfo.
   */
  @java.lang.Override
  public xddq.pb.AskDingBestPerformancePlayerMsg getUnionSeizeBestInfo() {
    return unionSeizeBestInfo_ == null ? xddq.pb.AskDingBestPerformancePlayerMsg.getDefaultInstance() : unionSeizeBestInfo_;
  }
  /**
   * <code>optional .xddq.pb.AskDingBestPerformancePlayerMsg unionSeizeBestInfo = 6;</code>
   */
  @java.lang.Override
  public xddq.pb.AskDingBestPerformancePlayerMsgOrBuilder getUnionSeizeBestInfoOrBuilder() {
    return unionSeizeBestInfo_ == null ? xddq.pb.AskDingBestPerformancePlayerMsg.getDefaultInstance() : unionSeizeBestInfo_;
  }

  public static final int MEMBERRECORD_FIELD_NUMBER = 7;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.AskDingPlayerRoundRecordMsg> memberRecord_;
  /**
   * <code>repeated .xddq.pb.AskDingPlayerRoundRecordMsg memberRecord = 7;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.AskDingPlayerRoundRecordMsg> getMemberRecordList() {
    return memberRecord_;
  }
  /**
   * <code>repeated .xddq.pb.AskDingPlayerRoundRecordMsg memberRecord = 7;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.AskDingPlayerRoundRecordMsgOrBuilder> 
      getMemberRecordOrBuilderList() {
    return memberRecord_;
  }
  /**
   * <code>repeated .xddq.pb.AskDingPlayerRoundRecordMsg memberRecord = 7;</code>
   */
  @java.lang.Override
  public int getMemberRecordCount() {
    return memberRecord_.size();
  }
  /**
   * <code>repeated .xddq.pb.AskDingPlayerRoundRecordMsg memberRecord = 7;</code>
   */
  @java.lang.Override
  public xddq.pb.AskDingPlayerRoundRecordMsg getMemberRecord(int index) {
    return memberRecord_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.AskDingPlayerRoundRecordMsg memberRecord = 7;</code>
   */
  @java.lang.Override
  public xddq.pb.AskDingPlayerRoundRecordMsgOrBuilder getMemberRecordOrBuilder(
      int index) {
    return memberRecord_.get(index);
  }

  public static final int GAMEID_FIELD_NUMBER = 8;
  private long gameId_ = 0L;
  /**
   * <code>optional int64 gameId = 8;</code>
   * @return Whether the gameId field is set.
   */
  @java.lang.Override
  public boolean hasGameId() {
    return ((bitField0_ & 0x00000020) != 0);
  }
  /**
   * <code>optional int64 gameId = 8;</code>
   * @return The gameId.
   */
  @java.lang.Override
  public long getGameId() {
    return gameId_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    for (int i = 0; i < unionResult_.size(); i++) {
      output.writeMessage(1, unionResult_.get(i));
    }
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(2, timeId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeMessage(3, getTotalKillBestInfo());
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeMessage(4, getTotalSeizeBestInfo());
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeMessage(5, getUnionKillBestInfo());
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      output.writeMessage(6, getUnionSeizeBestInfo());
    }
    for (int i = 0; i < memberRecord_.size(); i++) {
      output.writeMessage(7, memberRecord_.get(i));
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      output.writeInt64(8, gameId_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    for (int i = 0; i < unionResult_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, unionResult_.get(i));
    }
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, timeId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, getTotalKillBestInfo());
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(4, getTotalSeizeBestInfo());
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(5, getUnionKillBestInfo());
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(6, getUnionSeizeBestInfo());
    }
    for (int i = 0; i < memberRecord_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(7, memberRecord_.get(i));
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(8, gameId_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.AskDingEndDataMsg)) {
      return super.equals(obj);
    }
    xddq.pb.AskDingEndDataMsg other = (xddq.pb.AskDingEndDataMsg) obj;

    if (!getUnionResultList()
        .equals(other.getUnionResultList())) return false;
    if (hasTimeId() != other.hasTimeId()) return false;
    if (hasTimeId()) {
      if (getTimeId()
          != other.getTimeId()) return false;
    }
    if (hasTotalKillBestInfo() != other.hasTotalKillBestInfo()) return false;
    if (hasTotalKillBestInfo()) {
      if (!getTotalKillBestInfo()
          .equals(other.getTotalKillBestInfo())) return false;
    }
    if (hasTotalSeizeBestInfo() != other.hasTotalSeizeBestInfo()) return false;
    if (hasTotalSeizeBestInfo()) {
      if (!getTotalSeizeBestInfo()
          .equals(other.getTotalSeizeBestInfo())) return false;
    }
    if (hasUnionKillBestInfo() != other.hasUnionKillBestInfo()) return false;
    if (hasUnionKillBestInfo()) {
      if (!getUnionKillBestInfo()
          .equals(other.getUnionKillBestInfo())) return false;
    }
    if (hasUnionSeizeBestInfo() != other.hasUnionSeizeBestInfo()) return false;
    if (hasUnionSeizeBestInfo()) {
      if (!getUnionSeizeBestInfo()
          .equals(other.getUnionSeizeBestInfo())) return false;
    }
    if (!getMemberRecordList()
        .equals(other.getMemberRecordList())) return false;
    if (hasGameId() != other.hasGameId()) return false;
    if (hasGameId()) {
      if (getGameId()
          != other.getGameId()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (getUnionResultCount() > 0) {
      hash = (37 * hash) + UNIONRESULT_FIELD_NUMBER;
      hash = (53 * hash) + getUnionResultList().hashCode();
    }
    if (hasTimeId()) {
      hash = (37 * hash) + TIMEID_FIELD_NUMBER;
      hash = (53 * hash) + getTimeId();
    }
    if (hasTotalKillBestInfo()) {
      hash = (37 * hash) + TOTALKILLBESTINFO_FIELD_NUMBER;
      hash = (53 * hash) + getTotalKillBestInfo().hashCode();
    }
    if (hasTotalSeizeBestInfo()) {
      hash = (37 * hash) + TOTALSEIZEBESTINFO_FIELD_NUMBER;
      hash = (53 * hash) + getTotalSeizeBestInfo().hashCode();
    }
    if (hasUnionKillBestInfo()) {
      hash = (37 * hash) + UNIONKILLBESTINFO_FIELD_NUMBER;
      hash = (53 * hash) + getUnionKillBestInfo().hashCode();
    }
    if (hasUnionSeizeBestInfo()) {
      hash = (37 * hash) + UNIONSEIZEBESTINFO_FIELD_NUMBER;
      hash = (53 * hash) + getUnionSeizeBestInfo().hashCode();
    }
    if (getMemberRecordCount() > 0) {
      hash = (37 * hash) + MEMBERRECORD_FIELD_NUMBER;
      hash = (53 * hash) + getMemberRecordList().hashCode();
    }
    if (hasGameId()) {
      hash = (37 * hash) + GAMEID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getGameId());
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.AskDingEndDataMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.AskDingEndDataMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.AskDingEndDataMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.AskDingEndDataMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.AskDingEndDataMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.AskDingEndDataMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.AskDingEndDataMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.AskDingEndDataMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.AskDingEndDataMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.AskDingEndDataMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.AskDingEndDataMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.AskDingEndDataMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.AskDingEndDataMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.AskDingEndDataMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.AskDingEndDataMsg)
      xddq.pb.AskDingEndDataMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_AskDingEndDataMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_AskDingEndDataMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.AskDingEndDataMsg.class, xddq.pb.AskDingEndDataMsg.Builder.class);
    }

    // Construct using xddq.pb.AskDingEndDataMsg.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetUnionResultFieldBuilder();
        internalGetTotalKillBestInfoFieldBuilder();
        internalGetTotalSeizeBestInfoFieldBuilder();
        internalGetUnionKillBestInfoFieldBuilder();
        internalGetUnionSeizeBestInfoFieldBuilder();
        internalGetMemberRecordFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      if (unionResultBuilder_ == null) {
        unionResult_ = java.util.Collections.emptyList();
      } else {
        unionResult_ = null;
        unionResultBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000001);
      timeId_ = 0;
      totalKillBestInfo_ = null;
      if (totalKillBestInfoBuilder_ != null) {
        totalKillBestInfoBuilder_.dispose();
        totalKillBestInfoBuilder_ = null;
      }
      totalSeizeBestInfo_ = null;
      if (totalSeizeBestInfoBuilder_ != null) {
        totalSeizeBestInfoBuilder_.dispose();
        totalSeizeBestInfoBuilder_ = null;
      }
      unionKillBestInfo_ = null;
      if (unionKillBestInfoBuilder_ != null) {
        unionKillBestInfoBuilder_.dispose();
        unionKillBestInfoBuilder_ = null;
      }
      unionSeizeBestInfo_ = null;
      if (unionSeizeBestInfoBuilder_ != null) {
        unionSeizeBestInfoBuilder_.dispose();
        unionSeizeBestInfoBuilder_ = null;
      }
      if (memberRecordBuilder_ == null) {
        memberRecord_ = java.util.Collections.emptyList();
      } else {
        memberRecord_ = null;
        memberRecordBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000040);
      gameId_ = 0L;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_AskDingEndDataMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.AskDingEndDataMsg getDefaultInstanceForType() {
      return xddq.pb.AskDingEndDataMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.AskDingEndDataMsg build() {
      xddq.pb.AskDingEndDataMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.AskDingEndDataMsg buildPartial() {
      xddq.pb.AskDingEndDataMsg result = new xddq.pb.AskDingEndDataMsg(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.AskDingEndDataMsg result) {
      if (unionResultBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          unionResult_ = java.util.Collections.unmodifiableList(unionResult_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.unionResult_ = unionResult_;
      } else {
        result.unionResult_ = unionResultBuilder_.build();
      }
      if (memberRecordBuilder_ == null) {
        if (((bitField0_ & 0x00000040) != 0)) {
          memberRecord_ = java.util.Collections.unmodifiableList(memberRecord_);
          bitField0_ = (bitField0_ & ~0x00000040);
        }
        result.memberRecord_ = memberRecord_;
      } else {
        result.memberRecord_ = memberRecordBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.AskDingEndDataMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.timeId_ = timeId_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.totalKillBestInfo_ = totalKillBestInfoBuilder_ == null
            ? totalKillBestInfo_
            : totalKillBestInfoBuilder_.build();
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.totalSeizeBestInfo_ = totalSeizeBestInfoBuilder_ == null
            ? totalSeizeBestInfo_
            : totalSeizeBestInfoBuilder_.build();
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.unionKillBestInfo_ = unionKillBestInfoBuilder_ == null
            ? unionKillBestInfo_
            : unionKillBestInfoBuilder_.build();
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.unionSeizeBestInfo_ = unionSeizeBestInfoBuilder_ == null
            ? unionSeizeBestInfo_
            : unionSeizeBestInfoBuilder_.build();
        to_bitField0_ |= 0x00000010;
      }
      if (((from_bitField0_ & 0x00000080) != 0)) {
        result.gameId_ = gameId_;
        to_bitField0_ |= 0x00000020;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.AskDingEndDataMsg) {
        return mergeFrom((xddq.pb.AskDingEndDataMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.AskDingEndDataMsg other) {
      if (other == xddq.pb.AskDingEndDataMsg.getDefaultInstance()) return this;
      if (unionResultBuilder_ == null) {
        if (!other.unionResult_.isEmpty()) {
          if (unionResult_.isEmpty()) {
            unionResult_ = other.unionResult_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureUnionResultIsMutable();
            unionResult_.addAll(other.unionResult_);
          }
          onChanged();
        }
      } else {
        if (!other.unionResult_.isEmpty()) {
          if (unionResultBuilder_.isEmpty()) {
            unionResultBuilder_.dispose();
            unionResultBuilder_ = null;
            unionResult_ = other.unionResult_;
            bitField0_ = (bitField0_ & ~0x00000001);
            unionResultBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetUnionResultFieldBuilder() : null;
          } else {
            unionResultBuilder_.addAllMessages(other.unionResult_);
          }
        }
      }
      if (other.hasTimeId()) {
        setTimeId(other.getTimeId());
      }
      if (other.hasTotalKillBestInfo()) {
        mergeTotalKillBestInfo(other.getTotalKillBestInfo());
      }
      if (other.hasTotalSeizeBestInfo()) {
        mergeTotalSeizeBestInfo(other.getTotalSeizeBestInfo());
      }
      if (other.hasUnionKillBestInfo()) {
        mergeUnionKillBestInfo(other.getUnionKillBestInfo());
      }
      if (other.hasUnionSeizeBestInfo()) {
        mergeUnionSeizeBestInfo(other.getUnionSeizeBestInfo());
      }
      if (memberRecordBuilder_ == null) {
        if (!other.memberRecord_.isEmpty()) {
          if (memberRecord_.isEmpty()) {
            memberRecord_ = other.memberRecord_;
            bitField0_ = (bitField0_ & ~0x00000040);
          } else {
            ensureMemberRecordIsMutable();
            memberRecord_.addAll(other.memberRecord_);
          }
          onChanged();
        }
      } else {
        if (!other.memberRecord_.isEmpty()) {
          if (memberRecordBuilder_.isEmpty()) {
            memberRecordBuilder_.dispose();
            memberRecordBuilder_ = null;
            memberRecord_ = other.memberRecord_;
            bitField0_ = (bitField0_ & ~0x00000040);
            memberRecordBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetMemberRecordFieldBuilder() : null;
          } else {
            memberRecordBuilder_.addAllMessages(other.memberRecord_);
          }
        }
      }
      if (other.hasGameId()) {
        setGameId(other.getGameId());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              xddq.pb.AskDingUnionScoreMsg m =
                  input.readMessage(
                      xddq.pb.AskDingUnionScoreMsg.parser(),
                      extensionRegistry);
              if (unionResultBuilder_ == null) {
                ensureUnionResultIsMutable();
                unionResult_.add(m);
              } else {
                unionResultBuilder_.addMessage(m);
              }
              break;
            } // case 10
            case 16: {
              timeId_ = input.readInt32();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 26: {
              input.readMessage(
                  internalGetTotalKillBestInfoFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 34: {
              input.readMessage(
                  internalGetTotalSeizeBestInfoFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000008;
              break;
            } // case 34
            case 42: {
              input.readMessage(
                  internalGetUnionKillBestInfoFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000010;
              break;
            } // case 42
            case 50: {
              input.readMessage(
                  internalGetUnionSeizeBestInfoFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000020;
              break;
            } // case 50
            case 58: {
              xddq.pb.AskDingPlayerRoundRecordMsg m =
                  input.readMessage(
                      xddq.pb.AskDingPlayerRoundRecordMsg.parser(),
                      extensionRegistry);
              if (memberRecordBuilder_ == null) {
                ensureMemberRecordIsMutable();
                memberRecord_.add(m);
              } else {
                memberRecordBuilder_.addMessage(m);
              }
              break;
            } // case 58
            case 64: {
              gameId_ = input.readInt64();
              bitField0_ |= 0x00000080;
              break;
            } // case 64
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.util.List<xddq.pb.AskDingUnionScoreMsg> unionResult_ =
      java.util.Collections.emptyList();
    private void ensureUnionResultIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        unionResult_ = new java.util.ArrayList<xddq.pb.AskDingUnionScoreMsg>(unionResult_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.AskDingUnionScoreMsg, xddq.pb.AskDingUnionScoreMsg.Builder, xddq.pb.AskDingUnionScoreMsgOrBuilder> unionResultBuilder_;

    /**
     * <code>repeated .xddq.pb.AskDingUnionScoreMsg unionResult = 1;</code>
     */
    public java.util.List<xddq.pb.AskDingUnionScoreMsg> getUnionResultList() {
      if (unionResultBuilder_ == null) {
        return java.util.Collections.unmodifiableList(unionResult_);
      } else {
        return unionResultBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.AskDingUnionScoreMsg unionResult = 1;</code>
     */
    public int getUnionResultCount() {
      if (unionResultBuilder_ == null) {
        return unionResult_.size();
      } else {
        return unionResultBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.AskDingUnionScoreMsg unionResult = 1;</code>
     */
    public xddq.pb.AskDingUnionScoreMsg getUnionResult(int index) {
      if (unionResultBuilder_ == null) {
        return unionResult_.get(index);
      } else {
        return unionResultBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.AskDingUnionScoreMsg unionResult = 1;</code>
     */
    public Builder setUnionResult(
        int index, xddq.pb.AskDingUnionScoreMsg value) {
      if (unionResultBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureUnionResultIsMutable();
        unionResult_.set(index, value);
        onChanged();
      } else {
        unionResultBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.AskDingUnionScoreMsg unionResult = 1;</code>
     */
    public Builder setUnionResult(
        int index, xddq.pb.AskDingUnionScoreMsg.Builder builderForValue) {
      if (unionResultBuilder_ == null) {
        ensureUnionResultIsMutable();
        unionResult_.set(index, builderForValue.build());
        onChanged();
      } else {
        unionResultBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.AskDingUnionScoreMsg unionResult = 1;</code>
     */
    public Builder addUnionResult(xddq.pb.AskDingUnionScoreMsg value) {
      if (unionResultBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureUnionResultIsMutable();
        unionResult_.add(value);
        onChanged();
      } else {
        unionResultBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.AskDingUnionScoreMsg unionResult = 1;</code>
     */
    public Builder addUnionResult(
        int index, xddq.pb.AskDingUnionScoreMsg value) {
      if (unionResultBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureUnionResultIsMutable();
        unionResult_.add(index, value);
        onChanged();
      } else {
        unionResultBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.AskDingUnionScoreMsg unionResult = 1;</code>
     */
    public Builder addUnionResult(
        xddq.pb.AskDingUnionScoreMsg.Builder builderForValue) {
      if (unionResultBuilder_ == null) {
        ensureUnionResultIsMutable();
        unionResult_.add(builderForValue.build());
        onChanged();
      } else {
        unionResultBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.AskDingUnionScoreMsg unionResult = 1;</code>
     */
    public Builder addUnionResult(
        int index, xddq.pb.AskDingUnionScoreMsg.Builder builderForValue) {
      if (unionResultBuilder_ == null) {
        ensureUnionResultIsMutable();
        unionResult_.add(index, builderForValue.build());
        onChanged();
      } else {
        unionResultBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.AskDingUnionScoreMsg unionResult = 1;</code>
     */
    public Builder addAllUnionResult(
        java.lang.Iterable<? extends xddq.pb.AskDingUnionScoreMsg> values) {
      if (unionResultBuilder_ == null) {
        ensureUnionResultIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, unionResult_);
        onChanged();
      } else {
        unionResultBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.AskDingUnionScoreMsg unionResult = 1;</code>
     */
    public Builder clearUnionResult() {
      if (unionResultBuilder_ == null) {
        unionResult_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        unionResultBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.AskDingUnionScoreMsg unionResult = 1;</code>
     */
    public Builder removeUnionResult(int index) {
      if (unionResultBuilder_ == null) {
        ensureUnionResultIsMutable();
        unionResult_.remove(index);
        onChanged();
      } else {
        unionResultBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.AskDingUnionScoreMsg unionResult = 1;</code>
     */
    public xddq.pb.AskDingUnionScoreMsg.Builder getUnionResultBuilder(
        int index) {
      return internalGetUnionResultFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.AskDingUnionScoreMsg unionResult = 1;</code>
     */
    public xddq.pb.AskDingUnionScoreMsgOrBuilder getUnionResultOrBuilder(
        int index) {
      if (unionResultBuilder_ == null) {
        return unionResult_.get(index);  } else {
        return unionResultBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.AskDingUnionScoreMsg unionResult = 1;</code>
     */
    public java.util.List<? extends xddq.pb.AskDingUnionScoreMsgOrBuilder> 
         getUnionResultOrBuilderList() {
      if (unionResultBuilder_ != null) {
        return unionResultBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(unionResult_);
      }
    }
    /**
     * <code>repeated .xddq.pb.AskDingUnionScoreMsg unionResult = 1;</code>
     */
    public xddq.pb.AskDingUnionScoreMsg.Builder addUnionResultBuilder() {
      return internalGetUnionResultFieldBuilder().addBuilder(
          xddq.pb.AskDingUnionScoreMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.AskDingUnionScoreMsg unionResult = 1;</code>
     */
    public xddq.pb.AskDingUnionScoreMsg.Builder addUnionResultBuilder(
        int index) {
      return internalGetUnionResultFieldBuilder().addBuilder(
          index, xddq.pb.AskDingUnionScoreMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.AskDingUnionScoreMsg unionResult = 1;</code>
     */
    public java.util.List<xddq.pb.AskDingUnionScoreMsg.Builder> 
         getUnionResultBuilderList() {
      return internalGetUnionResultFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.AskDingUnionScoreMsg, xddq.pb.AskDingUnionScoreMsg.Builder, xddq.pb.AskDingUnionScoreMsgOrBuilder> 
        internalGetUnionResultFieldBuilder() {
      if (unionResultBuilder_ == null) {
        unionResultBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.AskDingUnionScoreMsg, xddq.pb.AskDingUnionScoreMsg.Builder, xddq.pb.AskDingUnionScoreMsgOrBuilder>(
                unionResult_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        unionResult_ = null;
      }
      return unionResultBuilder_;
    }

    private int timeId_ ;
    /**
     * <code>optional int32 timeId = 2;</code>
     * @return Whether the timeId field is set.
     */
    @java.lang.Override
    public boolean hasTimeId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 timeId = 2;</code>
     * @return The timeId.
     */
    @java.lang.Override
    public int getTimeId() {
      return timeId_;
    }
    /**
     * <code>optional int32 timeId = 2;</code>
     * @param value The timeId to set.
     * @return This builder for chaining.
     */
    public Builder setTimeId(int value) {

      timeId_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 timeId = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearTimeId() {
      bitField0_ = (bitField0_ & ~0x00000002);
      timeId_ = 0;
      onChanged();
      return this;
    }

    private xddq.pb.AskDingBestPerformancePlayerMsg totalKillBestInfo_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.AskDingBestPerformancePlayerMsg, xddq.pb.AskDingBestPerformancePlayerMsg.Builder, xddq.pb.AskDingBestPerformancePlayerMsgOrBuilder> totalKillBestInfoBuilder_;
    /**
     * <code>optional .xddq.pb.AskDingBestPerformancePlayerMsg totalKillBestInfo = 3;</code>
     * @return Whether the totalKillBestInfo field is set.
     */
    public boolean hasTotalKillBestInfo() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional .xddq.pb.AskDingBestPerformancePlayerMsg totalKillBestInfo = 3;</code>
     * @return The totalKillBestInfo.
     */
    public xddq.pb.AskDingBestPerformancePlayerMsg getTotalKillBestInfo() {
      if (totalKillBestInfoBuilder_ == null) {
        return totalKillBestInfo_ == null ? xddq.pb.AskDingBestPerformancePlayerMsg.getDefaultInstance() : totalKillBestInfo_;
      } else {
        return totalKillBestInfoBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.AskDingBestPerformancePlayerMsg totalKillBestInfo = 3;</code>
     */
    public Builder setTotalKillBestInfo(xddq.pb.AskDingBestPerformancePlayerMsg value) {
      if (totalKillBestInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        totalKillBestInfo_ = value;
      } else {
        totalKillBestInfoBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.AskDingBestPerformancePlayerMsg totalKillBestInfo = 3;</code>
     */
    public Builder setTotalKillBestInfo(
        xddq.pb.AskDingBestPerformancePlayerMsg.Builder builderForValue) {
      if (totalKillBestInfoBuilder_ == null) {
        totalKillBestInfo_ = builderForValue.build();
      } else {
        totalKillBestInfoBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.AskDingBestPerformancePlayerMsg totalKillBestInfo = 3;</code>
     */
    public Builder mergeTotalKillBestInfo(xddq.pb.AskDingBestPerformancePlayerMsg value) {
      if (totalKillBestInfoBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0) &&
          totalKillBestInfo_ != null &&
          totalKillBestInfo_ != xddq.pb.AskDingBestPerformancePlayerMsg.getDefaultInstance()) {
          getTotalKillBestInfoBuilder().mergeFrom(value);
        } else {
          totalKillBestInfo_ = value;
        }
      } else {
        totalKillBestInfoBuilder_.mergeFrom(value);
      }
      if (totalKillBestInfo_ != null) {
        bitField0_ |= 0x00000004;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.AskDingBestPerformancePlayerMsg totalKillBestInfo = 3;</code>
     */
    public Builder clearTotalKillBestInfo() {
      bitField0_ = (bitField0_ & ~0x00000004);
      totalKillBestInfo_ = null;
      if (totalKillBestInfoBuilder_ != null) {
        totalKillBestInfoBuilder_.dispose();
        totalKillBestInfoBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.AskDingBestPerformancePlayerMsg totalKillBestInfo = 3;</code>
     */
    public xddq.pb.AskDingBestPerformancePlayerMsg.Builder getTotalKillBestInfoBuilder() {
      bitField0_ |= 0x00000004;
      onChanged();
      return internalGetTotalKillBestInfoFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.AskDingBestPerformancePlayerMsg totalKillBestInfo = 3;</code>
     */
    public xddq.pb.AskDingBestPerformancePlayerMsgOrBuilder getTotalKillBestInfoOrBuilder() {
      if (totalKillBestInfoBuilder_ != null) {
        return totalKillBestInfoBuilder_.getMessageOrBuilder();
      } else {
        return totalKillBestInfo_ == null ?
            xddq.pb.AskDingBestPerformancePlayerMsg.getDefaultInstance() : totalKillBestInfo_;
      }
    }
    /**
     * <code>optional .xddq.pb.AskDingBestPerformancePlayerMsg totalKillBestInfo = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.AskDingBestPerformancePlayerMsg, xddq.pb.AskDingBestPerformancePlayerMsg.Builder, xddq.pb.AskDingBestPerformancePlayerMsgOrBuilder> 
        internalGetTotalKillBestInfoFieldBuilder() {
      if (totalKillBestInfoBuilder_ == null) {
        totalKillBestInfoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.AskDingBestPerformancePlayerMsg, xddq.pb.AskDingBestPerformancePlayerMsg.Builder, xddq.pb.AskDingBestPerformancePlayerMsgOrBuilder>(
                getTotalKillBestInfo(),
                getParentForChildren(),
                isClean());
        totalKillBestInfo_ = null;
      }
      return totalKillBestInfoBuilder_;
    }

    private xddq.pb.AskDingBestPerformancePlayerMsg totalSeizeBestInfo_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.AskDingBestPerformancePlayerMsg, xddq.pb.AskDingBestPerformancePlayerMsg.Builder, xddq.pb.AskDingBestPerformancePlayerMsgOrBuilder> totalSeizeBestInfoBuilder_;
    /**
     * <code>optional .xddq.pb.AskDingBestPerformancePlayerMsg totalSeizeBestInfo = 4;</code>
     * @return Whether the totalSeizeBestInfo field is set.
     */
    public boolean hasTotalSeizeBestInfo() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional .xddq.pb.AskDingBestPerformancePlayerMsg totalSeizeBestInfo = 4;</code>
     * @return The totalSeizeBestInfo.
     */
    public xddq.pb.AskDingBestPerformancePlayerMsg getTotalSeizeBestInfo() {
      if (totalSeizeBestInfoBuilder_ == null) {
        return totalSeizeBestInfo_ == null ? xddq.pb.AskDingBestPerformancePlayerMsg.getDefaultInstance() : totalSeizeBestInfo_;
      } else {
        return totalSeizeBestInfoBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.AskDingBestPerformancePlayerMsg totalSeizeBestInfo = 4;</code>
     */
    public Builder setTotalSeizeBestInfo(xddq.pb.AskDingBestPerformancePlayerMsg value) {
      if (totalSeizeBestInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        totalSeizeBestInfo_ = value;
      } else {
        totalSeizeBestInfoBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.AskDingBestPerformancePlayerMsg totalSeizeBestInfo = 4;</code>
     */
    public Builder setTotalSeizeBestInfo(
        xddq.pb.AskDingBestPerformancePlayerMsg.Builder builderForValue) {
      if (totalSeizeBestInfoBuilder_ == null) {
        totalSeizeBestInfo_ = builderForValue.build();
      } else {
        totalSeizeBestInfoBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.AskDingBestPerformancePlayerMsg totalSeizeBestInfo = 4;</code>
     */
    public Builder mergeTotalSeizeBestInfo(xddq.pb.AskDingBestPerformancePlayerMsg value) {
      if (totalSeizeBestInfoBuilder_ == null) {
        if (((bitField0_ & 0x00000008) != 0) &&
          totalSeizeBestInfo_ != null &&
          totalSeizeBestInfo_ != xddq.pb.AskDingBestPerformancePlayerMsg.getDefaultInstance()) {
          getTotalSeizeBestInfoBuilder().mergeFrom(value);
        } else {
          totalSeizeBestInfo_ = value;
        }
      } else {
        totalSeizeBestInfoBuilder_.mergeFrom(value);
      }
      if (totalSeizeBestInfo_ != null) {
        bitField0_ |= 0x00000008;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.AskDingBestPerformancePlayerMsg totalSeizeBestInfo = 4;</code>
     */
    public Builder clearTotalSeizeBestInfo() {
      bitField0_ = (bitField0_ & ~0x00000008);
      totalSeizeBestInfo_ = null;
      if (totalSeizeBestInfoBuilder_ != null) {
        totalSeizeBestInfoBuilder_.dispose();
        totalSeizeBestInfoBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.AskDingBestPerformancePlayerMsg totalSeizeBestInfo = 4;</code>
     */
    public xddq.pb.AskDingBestPerformancePlayerMsg.Builder getTotalSeizeBestInfoBuilder() {
      bitField0_ |= 0x00000008;
      onChanged();
      return internalGetTotalSeizeBestInfoFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.AskDingBestPerformancePlayerMsg totalSeizeBestInfo = 4;</code>
     */
    public xddq.pb.AskDingBestPerformancePlayerMsgOrBuilder getTotalSeizeBestInfoOrBuilder() {
      if (totalSeizeBestInfoBuilder_ != null) {
        return totalSeizeBestInfoBuilder_.getMessageOrBuilder();
      } else {
        return totalSeizeBestInfo_ == null ?
            xddq.pb.AskDingBestPerformancePlayerMsg.getDefaultInstance() : totalSeizeBestInfo_;
      }
    }
    /**
     * <code>optional .xddq.pb.AskDingBestPerformancePlayerMsg totalSeizeBestInfo = 4;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.AskDingBestPerformancePlayerMsg, xddq.pb.AskDingBestPerformancePlayerMsg.Builder, xddq.pb.AskDingBestPerformancePlayerMsgOrBuilder> 
        internalGetTotalSeizeBestInfoFieldBuilder() {
      if (totalSeizeBestInfoBuilder_ == null) {
        totalSeizeBestInfoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.AskDingBestPerformancePlayerMsg, xddq.pb.AskDingBestPerformancePlayerMsg.Builder, xddq.pb.AskDingBestPerformancePlayerMsgOrBuilder>(
                getTotalSeizeBestInfo(),
                getParentForChildren(),
                isClean());
        totalSeizeBestInfo_ = null;
      }
      return totalSeizeBestInfoBuilder_;
    }

    private xddq.pb.AskDingBestPerformancePlayerMsg unionKillBestInfo_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.AskDingBestPerformancePlayerMsg, xddq.pb.AskDingBestPerformancePlayerMsg.Builder, xddq.pb.AskDingBestPerformancePlayerMsgOrBuilder> unionKillBestInfoBuilder_;
    /**
     * <code>optional .xddq.pb.AskDingBestPerformancePlayerMsg unionKillBestInfo = 5;</code>
     * @return Whether the unionKillBestInfo field is set.
     */
    public boolean hasUnionKillBestInfo() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional .xddq.pb.AskDingBestPerformancePlayerMsg unionKillBestInfo = 5;</code>
     * @return The unionKillBestInfo.
     */
    public xddq.pb.AskDingBestPerformancePlayerMsg getUnionKillBestInfo() {
      if (unionKillBestInfoBuilder_ == null) {
        return unionKillBestInfo_ == null ? xddq.pb.AskDingBestPerformancePlayerMsg.getDefaultInstance() : unionKillBestInfo_;
      } else {
        return unionKillBestInfoBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.AskDingBestPerformancePlayerMsg unionKillBestInfo = 5;</code>
     */
    public Builder setUnionKillBestInfo(xddq.pb.AskDingBestPerformancePlayerMsg value) {
      if (unionKillBestInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        unionKillBestInfo_ = value;
      } else {
        unionKillBestInfoBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.AskDingBestPerformancePlayerMsg unionKillBestInfo = 5;</code>
     */
    public Builder setUnionKillBestInfo(
        xddq.pb.AskDingBestPerformancePlayerMsg.Builder builderForValue) {
      if (unionKillBestInfoBuilder_ == null) {
        unionKillBestInfo_ = builderForValue.build();
      } else {
        unionKillBestInfoBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.AskDingBestPerformancePlayerMsg unionKillBestInfo = 5;</code>
     */
    public Builder mergeUnionKillBestInfo(xddq.pb.AskDingBestPerformancePlayerMsg value) {
      if (unionKillBestInfoBuilder_ == null) {
        if (((bitField0_ & 0x00000010) != 0) &&
          unionKillBestInfo_ != null &&
          unionKillBestInfo_ != xddq.pb.AskDingBestPerformancePlayerMsg.getDefaultInstance()) {
          getUnionKillBestInfoBuilder().mergeFrom(value);
        } else {
          unionKillBestInfo_ = value;
        }
      } else {
        unionKillBestInfoBuilder_.mergeFrom(value);
      }
      if (unionKillBestInfo_ != null) {
        bitField0_ |= 0x00000010;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.AskDingBestPerformancePlayerMsg unionKillBestInfo = 5;</code>
     */
    public Builder clearUnionKillBestInfo() {
      bitField0_ = (bitField0_ & ~0x00000010);
      unionKillBestInfo_ = null;
      if (unionKillBestInfoBuilder_ != null) {
        unionKillBestInfoBuilder_.dispose();
        unionKillBestInfoBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.AskDingBestPerformancePlayerMsg unionKillBestInfo = 5;</code>
     */
    public xddq.pb.AskDingBestPerformancePlayerMsg.Builder getUnionKillBestInfoBuilder() {
      bitField0_ |= 0x00000010;
      onChanged();
      return internalGetUnionKillBestInfoFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.AskDingBestPerformancePlayerMsg unionKillBestInfo = 5;</code>
     */
    public xddq.pb.AskDingBestPerformancePlayerMsgOrBuilder getUnionKillBestInfoOrBuilder() {
      if (unionKillBestInfoBuilder_ != null) {
        return unionKillBestInfoBuilder_.getMessageOrBuilder();
      } else {
        return unionKillBestInfo_ == null ?
            xddq.pb.AskDingBestPerformancePlayerMsg.getDefaultInstance() : unionKillBestInfo_;
      }
    }
    /**
     * <code>optional .xddq.pb.AskDingBestPerformancePlayerMsg unionKillBestInfo = 5;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.AskDingBestPerformancePlayerMsg, xddq.pb.AskDingBestPerformancePlayerMsg.Builder, xddq.pb.AskDingBestPerformancePlayerMsgOrBuilder> 
        internalGetUnionKillBestInfoFieldBuilder() {
      if (unionKillBestInfoBuilder_ == null) {
        unionKillBestInfoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.AskDingBestPerformancePlayerMsg, xddq.pb.AskDingBestPerformancePlayerMsg.Builder, xddq.pb.AskDingBestPerformancePlayerMsgOrBuilder>(
                getUnionKillBestInfo(),
                getParentForChildren(),
                isClean());
        unionKillBestInfo_ = null;
      }
      return unionKillBestInfoBuilder_;
    }

    private xddq.pb.AskDingBestPerformancePlayerMsg unionSeizeBestInfo_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.AskDingBestPerformancePlayerMsg, xddq.pb.AskDingBestPerformancePlayerMsg.Builder, xddq.pb.AskDingBestPerformancePlayerMsgOrBuilder> unionSeizeBestInfoBuilder_;
    /**
     * <code>optional .xddq.pb.AskDingBestPerformancePlayerMsg unionSeizeBestInfo = 6;</code>
     * @return Whether the unionSeizeBestInfo field is set.
     */
    public boolean hasUnionSeizeBestInfo() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional .xddq.pb.AskDingBestPerformancePlayerMsg unionSeizeBestInfo = 6;</code>
     * @return The unionSeizeBestInfo.
     */
    public xddq.pb.AskDingBestPerformancePlayerMsg getUnionSeizeBestInfo() {
      if (unionSeizeBestInfoBuilder_ == null) {
        return unionSeizeBestInfo_ == null ? xddq.pb.AskDingBestPerformancePlayerMsg.getDefaultInstance() : unionSeizeBestInfo_;
      } else {
        return unionSeizeBestInfoBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.AskDingBestPerformancePlayerMsg unionSeizeBestInfo = 6;</code>
     */
    public Builder setUnionSeizeBestInfo(xddq.pb.AskDingBestPerformancePlayerMsg value) {
      if (unionSeizeBestInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        unionSeizeBestInfo_ = value;
      } else {
        unionSeizeBestInfoBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.AskDingBestPerformancePlayerMsg unionSeizeBestInfo = 6;</code>
     */
    public Builder setUnionSeizeBestInfo(
        xddq.pb.AskDingBestPerformancePlayerMsg.Builder builderForValue) {
      if (unionSeizeBestInfoBuilder_ == null) {
        unionSeizeBestInfo_ = builderForValue.build();
      } else {
        unionSeizeBestInfoBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.AskDingBestPerformancePlayerMsg unionSeizeBestInfo = 6;</code>
     */
    public Builder mergeUnionSeizeBestInfo(xddq.pb.AskDingBestPerformancePlayerMsg value) {
      if (unionSeizeBestInfoBuilder_ == null) {
        if (((bitField0_ & 0x00000020) != 0) &&
          unionSeizeBestInfo_ != null &&
          unionSeizeBestInfo_ != xddq.pb.AskDingBestPerformancePlayerMsg.getDefaultInstance()) {
          getUnionSeizeBestInfoBuilder().mergeFrom(value);
        } else {
          unionSeizeBestInfo_ = value;
        }
      } else {
        unionSeizeBestInfoBuilder_.mergeFrom(value);
      }
      if (unionSeizeBestInfo_ != null) {
        bitField0_ |= 0x00000020;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.AskDingBestPerformancePlayerMsg unionSeizeBestInfo = 6;</code>
     */
    public Builder clearUnionSeizeBestInfo() {
      bitField0_ = (bitField0_ & ~0x00000020);
      unionSeizeBestInfo_ = null;
      if (unionSeizeBestInfoBuilder_ != null) {
        unionSeizeBestInfoBuilder_.dispose();
        unionSeizeBestInfoBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.AskDingBestPerformancePlayerMsg unionSeizeBestInfo = 6;</code>
     */
    public xddq.pb.AskDingBestPerformancePlayerMsg.Builder getUnionSeizeBestInfoBuilder() {
      bitField0_ |= 0x00000020;
      onChanged();
      return internalGetUnionSeizeBestInfoFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.AskDingBestPerformancePlayerMsg unionSeizeBestInfo = 6;</code>
     */
    public xddq.pb.AskDingBestPerformancePlayerMsgOrBuilder getUnionSeizeBestInfoOrBuilder() {
      if (unionSeizeBestInfoBuilder_ != null) {
        return unionSeizeBestInfoBuilder_.getMessageOrBuilder();
      } else {
        return unionSeizeBestInfo_ == null ?
            xddq.pb.AskDingBestPerformancePlayerMsg.getDefaultInstance() : unionSeizeBestInfo_;
      }
    }
    /**
     * <code>optional .xddq.pb.AskDingBestPerformancePlayerMsg unionSeizeBestInfo = 6;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.AskDingBestPerformancePlayerMsg, xddq.pb.AskDingBestPerformancePlayerMsg.Builder, xddq.pb.AskDingBestPerformancePlayerMsgOrBuilder> 
        internalGetUnionSeizeBestInfoFieldBuilder() {
      if (unionSeizeBestInfoBuilder_ == null) {
        unionSeizeBestInfoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.AskDingBestPerformancePlayerMsg, xddq.pb.AskDingBestPerformancePlayerMsg.Builder, xddq.pb.AskDingBestPerformancePlayerMsgOrBuilder>(
                getUnionSeizeBestInfo(),
                getParentForChildren(),
                isClean());
        unionSeizeBestInfo_ = null;
      }
      return unionSeizeBestInfoBuilder_;
    }

    private java.util.List<xddq.pb.AskDingPlayerRoundRecordMsg> memberRecord_ =
      java.util.Collections.emptyList();
    private void ensureMemberRecordIsMutable() {
      if (!((bitField0_ & 0x00000040) != 0)) {
        memberRecord_ = new java.util.ArrayList<xddq.pb.AskDingPlayerRoundRecordMsg>(memberRecord_);
        bitField0_ |= 0x00000040;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.AskDingPlayerRoundRecordMsg, xddq.pb.AskDingPlayerRoundRecordMsg.Builder, xddq.pb.AskDingPlayerRoundRecordMsgOrBuilder> memberRecordBuilder_;

    /**
     * <code>repeated .xddq.pb.AskDingPlayerRoundRecordMsg memberRecord = 7;</code>
     */
    public java.util.List<xddq.pb.AskDingPlayerRoundRecordMsg> getMemberRecordList() {
      if (memberRecordBuilder_ == null) {
        return java.util.Collections.unmodifiableList(memberRecord_);
      } else {
        return memberRecordBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.AskDingPlayerRoundRecordMsg memberRecord = 7;</code>
     */
    public int getMemberRecordCount() {
      if (memberRecordBuilder_ == null) {
        return memberRecord_.size();
      } else {
        return memberRecordBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.AskDingPlayerRoundRecordMsg memberRecord = 7;</code>
     */
    public xddq.pb.AskDingPlayerRoundRecordMsg getMemberRecord(int index) {
      if (memberRecordBuilder_ == null) {
        return memberRecord_.get(index);
      } else {
        return memberRecordBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.AskDingPlayerRoundRecordMsg memberRecord = 7;</code>
     */
    public Builder setMemberRecord(
        int index, xddq.pb.AskDingPlayerRoundRecordMsg value) {
      if (memberRecordBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMemberRecordIsMutable();
        memberRecord_.set(index, value);
        onChanged();
      } else {
        memberRecordBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.AskDingPlayerRoundRecordMsg memberRecord = 7;</code>
     */
    public Builder setMemberRecord(
        int index, xddq.pb.AskDingPlayerRoundRecordMsg.Builder builderForValue) {
      if (memberRecordBuilder_ == null) {
        ensureMemberRecordIsMutable();
        memberRecord_.set(index, builderForValue.build());
        onChanged();
      } else {
        memberRecordBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.AskDingPlayerRoundRecordMsg memberRecord = 7;</code>
     */
    public Builder addMemberRecord(xddq.pb.AskDingPlayerRoundRecordMsg value) {
      if (memberRecordBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMemberRecordIsMutable();
        memberRecord_.add(value);
        onChanged();
      } else {
        memberRecordBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.AskDingPlayerRoundRecordMsg memberRecord = 7;</code>
     */
    public Builder addMemberRecord(
        int index, xddq.pb.AskDingPlayerRoundRecordMsg value) {
      if (memberRecordBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMemberRecordIsMutable();
        memberRecord_.add(index, value);
        onChanged();
      } else {
        memberRecordBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.AskDingPlayerRoundRecordMsg memberRecord = 7;</code>
     */
    public Builder addMemberRecord(
        xddq.pb.AskDingPlayerRoundRecordMsg.Builder builderForValue) {
      if (memberRecordBuilder_ == null) {
        ensureMemberRecordIsMutable();
        memberRecord_.add(builderForValue.build());
        onChanged();
      } else {
        memberRecordBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.AskDingPlayerRoundRecordMsg memberRecord = 7;</code>
     */
    public Builder addMemberRecord(
        int index, xddq.pb.AskDingPlayerRoundRecordMsg.Builder builderForValue) {
      if (memberRecordBuilder_ == null) {
        ensureMemberRecordIsMutable();
        memberRecord_.add(index, builderForValue.build());
        onChanged();
      } else {
        memberRecordBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.AskDingPlayerRoundRecordMsg memberRecord = 7;</code>
     */
    public Builder addAllMemberRecord(
        java.lang.Iterable<? extends xddq.pb.AskDingPlayerRoundRecordMsg> values) {
      if (memberRecordBuilder_ == null) {
        ensureMemberRecordIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, memberRecord_);
        onChanged();
      } else {
        memberRecordBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.AskDingPlayerRoundRecordMsg memberRecord = 7;</code>
     */
    public Builder clearMemberRecord() {
      if (memberRecordBuilder_ == null) {
        memberRecord_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000040);
        onChanged();
      } else {
        memberRecordBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.AskDingPlayerRoundRecordMsg memberRecord = 7;</code>
     */
    public Builder removeMemberRecord(int index) {
      if (memberRecordBuilder_ == null) {
        ensureMemberRecordIsMutable();
        memberRecord_.remove(index);
        onChanged();
      } else {
        memberRecordBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.AskDingPlayerRoundRecordMsg memberRecord = 7;</code>
     */
    public xddq.pb.AskDingPlayerRoundRecordMsg.Builder getMemberRecordBuilder(
        int index) {
      return internalGetMemberRecordFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.AskDingPlayerRoundRecordMsg memberRecord = 7;</code>
     */
    public xddq.pb.AskDingPlayerRoundRecordMsgOrBuilder getMemberRecordOrBuilder(
        int index) {
      if (memberRecordBuilder_ == null) {
        return memberRecord_.get(index);  } else {
        return memberRecordBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.AskDingPlayerRoundRecordMsg memberRecord = 7;</code>
     */
    public java.util.List<? extends xddq.pb.AskDingPlayerRoundRecordMsgOrBuilder> 
         getMemberRecordOrBuilderList() {
      if (memberRecordBuilder_ != null) {
        return memberRecordBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(memberRecord_);
      }
    }
    /**
     * <code>repeated .xddq.pb.AskDingPlayerRoundRecordMsg memberRecord = 7;</code>
     */
    public xddq.pb.AskDingPlayerRoundRecordMsg.Builder addMemberRecordBuilder() {
      return internalGetMemberRecordFieldBuilder().addBuilder(
          xddq.pb.AskDingPlayerRoundRecordMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.AskDingPlayerRoundRecordMsg memberRecord = 7;</code>
     */
    public xddq.pb.AskDingPlayerRoundRecordMsg.Builder addMemberRecordBuilder(
        int index) {
      return internalGetMemberRecordFieldBuilder().addBuilder(
          index, xddq.pb.AskDingPlayerRoundRecordMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.AskDingPlayerRoundRecordMsg memberRecord = 7;</code>
     */
    public java.util.List<xddq.pb.AskDingPlayerRoundRecordMsg.Builder> 
         getMemberRecordBuilderList() {
      return internalGetMemberRecordFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.AskDingPlayerRoundRecordMsg, xddq.pb.AskDingPlayerRoundRecordMsg.Builder, xddq.pb.AskDingPlayerRoundRecordMsgOrBuilder> 
        internalGetMemberRecordFieldBuilder() {
      if (memberRecordBuilder_ == null) {
        memberRecordBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.AskDingPlayerRoundRecordMsg, xddq.pb.AskDingPlayerRoundRecordMsg.Builder, xddq.pb.AskDingPlayerRoundRecordMsgOrBuilder>(
                memberRecord_,
                ((bitField0_ & 0x00000040) != 0),
                getParentForChildren(),
                isClean());
        memberRecord_ = null;
      }
      return memberRecordBuilder_;
    }

    private long gameId_ ;
    /**
     * <code>optional int64 gameId = 8;</code>
     * @return Whether the gameId field is set.
     */
    @java.lang.Override
    public boolean hasGameId() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <code>optional int64 gameId = 8;</code>
     * @return The gameId.
     */
    @java.lang.Override
    public long getGameId() {
      return gameId_;
    }
    /**
     * <code>optional int64 gameId = 8;</code>
     * @param value The gameId to set.
     * @return This builder for chaining.
     */
    public Builder setGameId(long value) {

      gameId_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 gameId = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearGameId() {
      bitField0_ = (bitField0_ & ~0x00000080);
      gameId_ = 0L;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.AskDingEndDataMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.AskDingEndDataMsg)
  private static final xddq.pb.AskDingEndDataMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.AskDingEndDataMsg();
  }

  public static xddq.pb.AskDingEndDataMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<AskDingEndDataMsg>
      PARSER = new com.google.protobuf.AbstractParser<AskDingEndDataMsg>() {
    @java.lang.Override
    public AskDingEndDataMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<AskDingEndDataMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<AskDingEndDataMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.AskDingEndDataMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

