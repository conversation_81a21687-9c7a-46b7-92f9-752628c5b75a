// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.DoubleDemonsDoubleData}
 */
public final class DoubleDemonsDoubleData extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.DoubleDemonsDoubleData)
    DoubleDemonsDoubleDataOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      DoubleDemonsDoubleData.class.getName());
  }
  // Use DoubleDemonsDoubleData.newBuilder() to construct.
  private DoubleDemonsDoubleData(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private DoubleDemonsDoubleData() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_DoubleDemonsDoubleData_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_DoubleDemonsDoubleData_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.DoubleDemonsDoubleData.class, xddq.pb.DoubleDemonsDoubleData.Builder.class);
  }

  private int bitField0_;
  public static final int HEIGHT_FIELD_NUMBER = 1;
  private int height_ = 0;
  /**
   * <code>optional int32 height = 1;</code>
   * @return Whether the height field is set.
   */
  @java.lang.Override
  public boolean hasHeight() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int32 height = 1;</code>
   * @return The height.
   */
  @java.lang.Override
  public int getHeight() {
    return height_;
  }

  public static final int PARTNERDATA_FIELD_NUMBER = 3;
  private xddq.pb.PlayerBaseDataMsg partnerData_;
  /**
   * <code>optional .xddq.pb.PlayerBaseDataMsg partnerData = 3;</code>
   * @return Whether the partnerData field is set.
   */
  @java.lang.Override
  public boolean hasPartnerData() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional .xddq.pb.PlayerBaseDataMsg partnerData = 3;</code>
   * @return The partnerData.
   */
  @java.lang.Override
  public xddq.pb.PlayerBaseDataMsg getPartnerData() {
    return partnerData_ == null ? xddq.pb.PlayerBaseDataMsg.getDefaultInstance() : partnerData_;
  }
  /**
   * <code>optional .xddq.pb.PlayerBaseDataMsg partnerData = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.PlayerBaseDataMsgOrBuilder getPartnerDataOrBuilder() {
    return partnerData_ == null ? xddq.pb.PlayerBaseDataMsg.getDefaultInstance() : partnerData_;
  }

  public static final int PARTNERHEIGHT_FIELD_NUMBER = 4;
  private int partnerHeight_ = 0;
  /**
   * <code>optional int32 partnerHeight = 4;</code>
   * @return Whether the partnerHeight field is set.
   */
  @java.lang.Override
  public boolean hasPartnerHeight() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional int32 partnerHeight = 4;</code>
   * @return The partnerHeight.
   */
  @java.lang.Override
  public int getPartnerHeight() {
    return partnerHeight_;
  }

  public static final int COSTITEMNUM_FIELD_NUMBER = 5;
  private int costItemNum_ = 0;
  /**
   * <code>optional int32 costItemNum = 5;</code>
   * @return Whether the costItemNum field is set.
   */
  @java.lang.Override
  public boolean hasCostItemNum() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional int32 costItemNum = 5;</code>
   * @return The costItemNum.
   */
  @java.lang.Override
  public int getCostItemNum() {
    return costItemNum_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, height_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeMessage(3, getPartnerData());
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt32(4, partnerHeight_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt32(5, costItemNum_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, height_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, getPartnerData());
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(4, partnerHeight_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(5, costItemNum_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.DoubleDemonsDoubleData)) {
      return super.equals(obj);
    }
    xddq.pb.DoubleDemonsDoubleData other = (xddq.pb.DoubleDemonsDoubleData) obj;

    if (hasHeight() != other.hasHeight()) return false;
    if (hasHeight()) {
      if (getHeight()
          != other.getHeight()) return false;
    }
    if (hasPartnerData() != other.hasPartnerData()) return false;
    if (hasPartnerData()) {
      if (!getPartnerData()
          .equals(other.getPartnerData())) return false;
    }
    if (hasPartnerHeight() != other.hasPartnerHeight()) return false;
    if (hasPartnerHeight()) {
      if (getPartnerHeight()
          != other.getPartnerHeight()) return false;
    }
    if (hasCostItemNum() != other.hasCostItemNum()) return false;
    if (hasCostItemNum()) {
      if (getCostItemNum()
          != other.getCostItemNum()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasHeight()) {
      hash = (37 * hash) + HEIGHT_FIELD_NUMBER;
      hash = (53 * hash) + getHeight();
    }
    if (hasPartnerData()) {
      hash = (37 * hash) + PARTNERDATA_FIELD_NUMBER;
      hash = (53 * hash) + getPartnerData().hashCode();
    }
    if (hasPartnerHeight()) {
      hash = (37 * hash) + PARTNERHEIGHT_FIELD_NUMBER;
      hash = (53 * hash) + getPartnerHeight();
    }
    if (hasCostItemNum()) {
      hash = (37 * hash) + COSTITEMNUM_FIELD_NUMBER;
      hash = (53 * hash) + getCostItemNum();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.DoubleDemonsDoubleData parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.DoubleDemonsDoubleData parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.DoubleDemonsDoubleData parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.DoubleDemonsDoubleData parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.DoubleDemonsDoubleData parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.DoubleDemonsDoubleData parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.DoubleDemonsDoubleData parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.DoubleDemonsDoubleData parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.DoubleDemonsDoubleData parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.DoubleDemonsDoubleData parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.DoubleDemonsDoubleData parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.DoubleDemonsDoubleData parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.DoubleDemonsDoubleData prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.DoubleDemonsDoubleData}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.DoubleDemonsDoubleData)
      xddq.pb.DoubleDemonsDoubleDataOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_DoubleDemonsDoubleData_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_DoubleDemonsDoubleData_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.DoubleDemonsDoubleData.class, xddq.pb.DoubleDemonsDoubleData.Builder.class);
    }

    // Construct using xddq.pb.DoubleDemonsDoubleData.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetPartnerDataFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      height_ = 0;
      partnerData_ = null;
      if (partnerDataBuilder_ != null) {
        partnerDataBuilder_.dispose();
        partnerDataBuilder_ = null;
      }
      partnerHeight_ = 0;
      costItemNum_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_DoubleDemonsDoubleData_descriptor;
    }

    @java.lang.Override
    public xddq.pb.DoubleDemonsDoubleData getDefaultInstanceForType() {
      return xddq.pb.DoubleDemonsDoubleData.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.DoubleDemonsDoubleData build() {
      xddq.pb.DoubleDemonsDoubleData result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.DoubleDemonsDoubleData buildPartial() {
      xddq.pb.DoubleDemonsDoubleData result = new xddq.pb.DoubleDemonsDoubleData(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.DoubleDemonsDoubleData result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.height_ = height_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.partnerData_ = partnerDataBuilder_ == null
            ? partnerData_
            : partnerDataBuilder_.build();
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.partnerHeight_ = partnerHeight_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.costItemNum_ = costItemNum_;
        to_bitField0_ |= 0x00000008;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.DoubleDemonsDoubleData) {
        return mergeFrom((xddq.pb.DoubleDemonsDoubleData)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.DoubleDemonsDoubleData other) {
      if (other == xddq.pb.DoubleDemonsDoubleData.getDefaultInstance()) return this;
      if (other.hasHeight()) {
        setHeight(other.getHeight());
      }
      if (other.hasPartnerData()) {
        mergePartnerData(other.getPartnerData());
      }
      if (other.hasPartnerHeight()) {
        setPartnerHeight(other.getPartnerHeight());
      }
      if (other.hasCostItemNum()) {
        setCostItemNum(other.getCostItemNum());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              height_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 26: {
              input.readMessage(
                  internalGetPartnerDataFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000002;
              break;
            } // case 26
            case 32: {
              partnerHeight_ = input.readInt32();
              bitField0_ |= 0x00000004;
              break;
            } // case 32
            case 40: {
              costItemNum_ = input.readInt32();
              bitField0_ |= 0x00000008;
              break;
            } // case 40
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int height_ ;
    /**
     * <code>optional int32 height = 1;</code>
     * @return Whether the height field is set.
     */
    @java.lang.Override
    public boolean hasHeight() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 height = 1;</code>
     * @return The height.
     */
    @java.lang.Override
    public int getHeight() {
      return height_;
    }
    /**
     * <code>optional int32 height = 1;</code>
     * @param value The height to set.
     * @return This builder for chaining.
     */
    public Builder setHeight(int value) {

      height_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 height = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearHeight() {
      bitField0_ = (bitField0_ & ~0x00000001);
      height_ = 0;
      onChanged();
      return this;
    }

    private xddq.pb.PlayerBaseDataMsg partnerData_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PlayerBaseDataMsg, xddq.pb.PlayerBaseDataMsg.Builder, xddq.pb.PlayerBaseDataMsgOrBuilder> partnerDataBuilder_;
    /**
     * <code>optional .xddq.pb.PlayerBaseDataMsg partnerData = 3;</code>
     * @return Whether the partnerData field is set.
     */
    public boolean hasPartnerData() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .xddq.pb.PlayerBaseDataMsg partnerData = 3;</code>
     * @return The partnerData.
     */
    public xddq.pb.PlayerBaseDataMsg getPartnerData() {
      if (partnerDataBuilder_ == null) {
        return partnerData_ == null ? xddq.pb.PlayerBaseDataMsg.getDefaultInstance() : partnerData_;
      } else {
        return partnerDataBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.PlayerBaseDataMsg partnerData = 3;</code>
     */
    public Builder setPartnerData(xddq.pb.PlayerBaseDataMsg value) {
      if (partnerDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        partnerData_ = value;
      } else {
        partnerDataBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerBaseDataMsg partnerData = 3;</code>
     */
    public Builder setPartnerData(
        xddq.pb.PlayerBaseDataMsg.Builder builderForValue) {
      if (partnerDataBuilder_ == null) {
        partnerData_ = builderForValue.build();
      } else {
        partnerDataBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerBaseDataMsg partnerData = 3;</code>
     */
    public Builder mergePartnerData(xddq.pb.PlayerBaseDataMsg value) {
      if (partnerDataBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0) &&
          partnerData_ != null &&
          partnerData_ != xddq.pb.PlayerBaseDataMsg.getDefaultInstance()) {
          getPartnerDataBuilder().mergeFrom(value);
        } else {
          partnerData_ = value;
        }
      } else {
        partnerDataBuilder_.mergeFrom(value);
      }
      if (partnerData_ != null) {
        bitField0_ |= 0x00000002;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerBaseDataMsg partnerData = 3;</code>
     */
    public Builder clearPartnerData() {
      bitField0_ = (bitField0_ & ~0x00000002);
      partnerData_ = null;
      if (partnerDataBuilder_ != null) {
        partnerDataBuilder_.dispose();
        partnerDataBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerBaseDataMsg partnerData = 3;</code>
     */
    public xddq.pb.PlayerBaseDataMsg.Builder getPartnerDataBuilder() {
      bitField0_ |= 0x00000002;
      onChanged();
      return internalGetPartnerDataFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.PlayerBaseDataMsg partnerData = 3;</code>
     */
    public xddq.pb.PlayerBaseDataMsgOrBuilder getPartnerDataOrBuilder() {
      if (partnerDataBuilder_ != null) {
        return partnerDataBuilder_.getMessageOrBuilder();
      } else {
        return partnerData_ == null ?
            xddq.pb.PlayerBaseDataMsg.getDefaultInstance() : partnerData_;
      }
    }
    /**
     * <code>optional .xddq.pb.PlayerBaseDataMsg partnerData = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PlayerBaseDataMsg, xddq.pb.PlayerBaseDataMsg.Builder, xddq.pb.PlayerBaseDataMsgOrBuilder> 
        internalGetPartnerDataFieldBuilder() {
      if (partnerDataBuilder_ == null) {
        partnerDataBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.PlayerBaseDataMsg, xddq.pb.PlayerBaseDataMsg.Builder, xddq.pb.PlayerBaseDataMsgOrBuilder>(
                getPartnerData(),
                getParentForChildren(),
                isClean());
        partnerData_ = null;
      }
      return partnerDataBuilder_;
    }

    private int partnerHeight_ ;
    /**
     * <code>optional int32 partnerHeight = 4;</code>
     * @return Whether the partnerHeight field is set.
     */
    @java.lang.Override
    public boolean hasPartnerHeight() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int32 partnerHeight = 4;</code>
     * @return The partnerHeight.
     */
    @java.lang.Override
    public int getPartnerHeight() {
      return partnerHeight_;
    }
    /**
     * <code>optional int32 partnerHeight = 4;</code>
     * @param value The partnerHeight to set.
     * @return This builder for chaining.
     */
    public Builder setPartnerHeight(int value) {

      partnerHeight_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 partnerHeight = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearPartnerHeight() {
      bitField0_ = (bitField0_ & ~0x00000004);
      partnerHeight_ = 0;
      onChanged();
      return this;
    }

    private int costItemNum_ ;
    /**
     * <code>optional int32 costItemNum = 5;</code>
     * @return Whether the costItemNum field is set.
     */
    @java.lang.Override
    public boolean hasCostItemNum() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int32 costItemNum = 5;</code>
     * @return The costItemNum.
     */
    @java.lang.Override
    public int getCostItemNum() {
      return costItemNum_;
    }
    /**
     * <code>optional int32 costItemNum = 5;</code>
     * @param value The costItemNum to set.
     * @return This builder for chaining.
     */
    public Builder setCostItemNum(int value) {

      costItemNum_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 costItemNum = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearCostItemNum() {
      bitField0_ = (bitField0_ & ~0x00000008);
      costItemNum_ = 0;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.DoubleDemonsDoubleData)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.DoubleDemonsDoubleData)
  private static final xddq.pb.DoubleDemonsDoubleData DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.DoubleDemonsDoubleData();
  }

  public static xddq.pb.DoubleDemonsDoubleData getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<DoubleDemonsDoubleData>
      PARSER = new com.google.protobuf.AbstractParser<DoubleDemonsDoubleData>() {
    @java.lang.Override
    public DoubleDemonsDoubleData parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<DoubleDemonsDoubleData> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<DoubleDemonsDoubleData> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.DoubleDemonsDoubleData getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

