// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.GodIslandCityReportDetail}
 */
public final class GodIslandCityReportDetail extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.GodIslandCityReportDetail)
    GodIslandCityReportDetailOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      GodIslandCityReportDetail.class.getName());
  }
  // Use GodIslandCityReportDetail.newBuilder() to construct.
  private GodIslandCityReportDetail(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private GodIslandCityReportDetail() {
    attackName_ = "";
    beAttackName_ = "";
    attackPower_ = "";
    beAttackPower_ = "";
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_GodIslandCityReportDetail_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_GodIslandCityReportDetail_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.GodIslandCityReportDetail.class, xddq.pb.GodIslandCityReportDetail.Builder.class);
  }

  private int bitField0_;
  public static final int ATTACKHEADINFO_FIELD_NUMBER = 1;
  private xddq.pb.PlayerHeadDataMsg attackHeadInfo_;
  /**
   * <code>optional .xddq.pb.PlayerHeadDataMsg attackHeadInfo = 1;</code>
   * @return Whether the attackHeadInfo field is set.
   */
  @java.lang.Override
  public boolean hasAttackHeadInfo() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional .xddq.pb.PlayerHeadDataMsg attackHeadInfo = 1;</code>
   * @return The attackHeadInfo.
   */
  @java.lang.Override
  public xddq.pb.PlayerHeadDataMsg getAttackHeadInfo() {
    return attackHeadInfo_ == null ? xddq.pb.PlayerHeadDataMsg.getDefaultInstance() : attackHeadInfo_;
  }
  /**
   * <code>optional .xddq.pb.PlayerHeadDataMsg attackHeadInfo = 1;</code>
   */
  @java.lang.Override
  public xddq.pb.PlayerHeadDataMsgOrBuilder getAttackHeadInfoOrBuilder() {
    return attackHeadInfo_ == null ? xddq.pb.PlayerHeadDataMsg.getDefaultInstance() : attackHeadInfo_;
  }

  public static final int ATTACKNAME_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private volatile java.lang.Object attackName_ = "";
  /**
   * <code>optional string attackName = 2;</code>
   * @return Whether the attackName field is set.
   */
  @java.lang.Override
  public boolean hasAttackName() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional string attackName = 2;</code>
   * @return The attackName.
   */
  @java.lang.Override
  public java.lang.String getAttackName() {
    java.lang.Object ref = attackName_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        attackName_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string attackName = 2;</code>
   * @return The bytes for attackName.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getAttackNameBytes() {
    java.lang.Object ref = attackName_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      attackName_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int BEATTACKHEADINFO_FIELD_NUMBER = 3;
  private xddq.pb.PlayerHeadDataMsg beAttackHeadInfo_;
  /**
   * <code>optional .xddq.pb.PlayerHeadDataMsg beAttackHeadInfo = 3;</code>
   * @return Whether the beAttackHeadInfo field is set.
   */
  @java.lang.Override
  public boolean hasBeAttackHeadInfo() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional .xddq.pb.PlayerHeadDataMsg beAttackHeadInfo = 3;</code>
   * @return The beAttackHeadInfo.
   */
  @java.lang.Override
  public xddq.pb.PlayerHeadDataMsg getBeAttackHeadInfo() {
    return beAttackHeadInfo_ == null ? xddq.pb.PlayerHeadDataMsg.getDefaultInstance() : beAttackHeadInfo_;
  }
  /**
   * <code>optional .xddq.pb.PlayerHeadDataMsg beAttackHeadInfo = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.PlayerHeadDataMsgOrBuilder getBeAttackHeadInfoOrBuilder() {
    return beAttackHeadInfo_ == null ? xddq.pb.PlayerHeadDataMsg.getDefaultInstance() : beAttackHeadInfo_;
  }

  public static final int BEATTACKNAME_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private volatile java.lang.Object beAttackName_ = "";
  /**
   * <code>optional string beAttackName = 4;</code>
   * @return Whether the beAttackName field is set.
   */
  @java.lang.Override
  public boolean hasBeAttackName() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional string beAttackName = 4;</code>
   * @return The beAttackName.
   */
  @java.lang.Override
  public java.lang.String getBeAttackName() {
    java.lang.Object ref = beAttackName_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        beAttackName_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string beAttackName = 4;</code>
   * @return The bytes for beAttackName.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getBeAttackNameBytes() {
    java.lang.Object ref = beAttackName_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      beAttackName_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ATTACKWIN_FIELD_NUMBER = 5;
  private boolean attackWin_ = false;
  /**
   * <code>optional bool attackWin = 5;</code>
   * @return Whether the attackWin field is set.
   */
  @java.lang.Override
  public boolean hasAttackWin() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>optional bool attackWin = 5;</code>
   * @return The attackWin.
   */
  @java.lang.Override
  public boolean getAttackWin() {
    return attackWin_;
  }

  public static final int ATTACKPOWER_FIELD_NUMBER = 6;
  @SuppressWarnings("serial")
  private volatile java.lang.Object attackPower_ = "";
  /**
   * <code>optional string attackPower = 6;</code>
   * @return Whether the attackPower field is set.
   */
  @java.lang.Override
  public boolean hasAttackPower() {
    return ((bitField0_ & 0x00000020) != 0);
  }
  /**
   * <code>optional string attackPower = 6;</code>
   * @return The attackPower.
   */
  @java.lang.Override
  public java.lang.String getAttackPower() {
    java.lang.Object ref = attackPower_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        attackPower_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string attackPower = 6;</code>
   * @return The bytes for attackPower.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getAttackPowerBytes() {
    java.lang.Object ref = attackPower_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      attackPower_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int BEATTACKPOWER_FIELD_NUMBER = 7;
  @SuppressWarnings("serial")
  private volatile java.lang.Object beAttackPower_ = "";
  /**
   * <code>optional string beAttackPower = 7;</code>
   * @return Whether the beAttackPower field is set.
   */
  @java.lang.Override
  public boolean hasBeAttackPower() {
    return ((bitField0_ & 0x00000040) != 0);
  }
  /**
   * <code>optional string beAttackPower = 7;</code>
   * @return The beAttackPower.
   */
  @java.lang.Override
  public java.lang.String getBeAttackPower() {
    java.lang.Object ref = beAttackPower_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        beAttackPower_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string beAttackPower = 7;</code>
   * @return The bytes for beAttackPower.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getBeAttackPowerBytes() {
    java.lang.Object ref = beAttackPower_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      beAttackPower_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeMessage(1, getAttackHeadInfo());
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 2, attackName_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeMessage(3, getBeAttackHeadInfo());
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 4, beAttackName_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      output.writeBool(5, attackWin_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 6, attackPower_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 7, beAttackPower_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, getAttackHeadInfo());
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(2, attackName_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, getBeAttackHeadInfo());
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(4, beAttackName_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(5, attackWin_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(6, attackPower_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(7, beAttackPower_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.GodIslandCityReportDetail)) {
      return super.equals(obj);
    }
    xddq.pb.GodIslandCityReportDetail other = (xddq.pb.GodIslandCityReportDetail) obj;

    if (hasAttackHeadInfo() != other.hasAttackHeadInfo()) return false;
    if (hasAttackHeadInfo()) {
      if (!getAttackHeadInfo()
          .equals(other.getAttackHeadInfo())) return false;
    }
    if (hasAttackName() != other.hasAttackName()) return false;
    if (hasAttackName()) {
      if (!getAttackName()
          .equals(other.getAttackName())) return false;
    }
    if (hasBeAttackHeadInfo() != other.hasBeAttackHeadInfo()) return false;
    if (hasBeAttackHeadInfo()) {
      if (!getBeAttackHeadInfo()
          .equals(other.getBeAttackHeadInfo())) return false;
    }
    if (hasBeAttackName() != other.hasBeAttackName()) return false;
    if (hasBeAttackName()) {
      if (!getBeAttackName()
          .equals(other.getBeAttackName())) return false;
    }
    if (hasAttackWin() != other.hasAttackWin()) return false;
    if (hasAttackWin()) {
      if (getAttackWin()
          != other.getAttackWin()) return false;
    }
    if (hasAttackPower() != other.hasAttackPower()) return false;
    if (hasAttackPower()) {
      if (!getAttackPower()
          .equals(other.getAttackPower())) return false;
    }
    if (hasBeAttackPower() != other.hasBeAttackPower()) return false;
    if (hasBeAttackPower()) {
      if (!getBeAttackPower()
          .equals(other.getBeAttackPower())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasAttackHeadInfo()) {
      hash = (37 * hash) + ATTACKHEADINFO_FIELD_NUMBER;
      hash = (53 * hash) + getAttackHeadInfo().hashCode();
    }
    if (hasAttackName()) {
      hash = (37 * hash) + ATTACKNAME_FIELD_NUMBER;
      hash = (53 * hash) + getAttackName().hashCode();
    }
    if (hasBeAttackHeadInfo()) {
      hash = (37 * hash) + BEATTACKHEADINFO_FIELD_NUMBER;
      hash = (53 * hash) + getBeAttackHeadInfo().hashCode();
    }
    if (hasBeAttackName()) {
      hash = (37 * hash) + BEATTACKNAME_FIELD_NUMBER;
      hash = (53 * hash) + getBeAttackName().hashCode();
    }
    if (hasAttackWin()) {
      hash = (37 * hash) + ATTACKWIN_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getAttackWin());
    }
    if (hasAttackPower()) {
      hash = (37 * hash) + ATTACKPOWER_FIELD_NUMBER;
      hash = (53 * hash) + getAttackPower().hashCode();
    }
    if (hasBeAttackPower()) {
      hash = (37 * hash) + BEATTACKPOWER_FIELD_NUMBER;
      hash = (53 * hash) + getBeAttackPower().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.GodIslandCityReportDetail parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GodIslandCityReportDetail parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GodIslandCityReportDetail parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GodIslandCityReportDetail parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GodIslandCityReportDetail parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GodIslandCityReportDetail parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GodIslandCityReportDetail parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.GodIslandCityReportDetail parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.GodIslandCityReportDetail parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.GodIslandCityReportDetail parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.GodIslandCityReportDetail parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.GodIslandCityReportDetail parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.GodIslandCityReportDetail prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.GodIslandCityReportDetail}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.GodIslandCityReportDetail)
      xddq.pb.GodIslandCityReportDetailOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GodIslandCityReportDetail_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GodIslandCityReportDetail_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.GodIslandCityReportDetail.class, xddq.pb.GodIslandCityReportDetail.Builder.class);
    }

    // Construct using xddq.pb.GodIslandCityReportDetail.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetAttackHeadInfoFieldBuilder();
        internalGetBeAttackHeadInfoFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      attackHeadInfo_ = null;
      if (attackHeadInfoBuilder_ != null) {
        attackHeadInfoBuilder_.dispose();
        attackHeadInfoBuilder_ = null;
      }
      attackName_ = "";
      beAttackHeadInfo_ = null;
      if (beAttackHeadInfoBuilder_ != null) {
        beAttackHeadInfoBuilder_.dispose();
        beAttackHeadInfoBuilder_ = null;
      }
      beAttackName_ = "";
      attackWin_ = false;
      attackPower_ = "";
      beAttackPower_ = "";
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GodIslandCityReportDetail_descriptor;
    }

    @java.lang.Override
    public xddq.pb.GodIslandCityReportDetail getDefaultInstanceForType() {
      return xddq.pb.GodIslandCityReportDetail.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.GodIslandCityReportDetail build() {
      xddq.pb.GodIslandCityReportDetail result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.GodIslandCityReportDetail buildPartial() {
      xddq.pb.GodIslandCityReportDetail result = new xddq.pb.GodIslandCityReportDetail(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.GodIslandCityReportDetail result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.attackHeadInfo_ = attackHeadInfoBuilder_ == null
            ? attackHeadInfo_
            : attackHeadInfoBuilder_.build();
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.attackName_ = attackName_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.beAttackHeadInfo_ = beAttackHeadInfoBuilder_ == null
            ? beAttackHeadInfo_
            : beAttackHeadInfoBuilder_.build();
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.beAttackName_ = beAttackName_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.attackWin_ = attackWin_;
        to_bitField0_ |= 0x00000010;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.attackPower_ = attackPower_;
        to_bitField0_ |= 0x00000020;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.beAttackPower_ = beAttackPower_;
        to_bitField0_ |= 0x00000040;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.GodIslandCityReportDetail) {
        return mergeFrom((xddq.pb.GodIslandCityReportDetail)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.GodIslandCityReportDetail other) {
      if (other == xddq.pb.GodIslandCityReportDetail.getDefaultInstance()) return this;
      if (other.hasAttackHeadInfo()) {
        mergeAttackHeadInfo(other.getAttackHeadInfo());
      }
      if (other.hasAttackName()) {
        attackName_ = other.attackName_;
        bitField0_ |= 0x00000002;
        onChanged();
      }
      if (other.hasBeAttackHeadInfo()) {
        mergeBeAttackHeadInfo(other.getBeAttackHeadInfo());
      }
      if (other.hasBeAttackName()) {
        beAttackName_ = other.beAttackName_;
        bitField0_ |= 0x00000008;
        onChanged();
      }
      if (other.hasAttackWin()) {
        setAttackWin(other.getAttackWin());
      }
      if (other.hasAttackPower()) {
        attackPower_ = other.attackPower_;
        bitField0_ |= 0x00000020;
        onChanged();
      }
      if (other.hasBeAttackPower()) {
        beAttackPower_ = other.beAttackPower_;
        bitField0_ |= 0x00000040;
        onChanged();
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              input.readMessage(
                  internalGetAttackHeadInfoFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 18: {
              attackName_ = input.readBytes();
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 26: {
              input.readMessage(
                  internalGetBeAttackHeadInfoFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 34: {
              beAttackName_ = input.readBytes();
              bitField0_ |= 0x00000008;
              break;
            } // case 34
            case 40: {
              attackWin_ = input.readBool();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            case 50: {
              attackPower_ = input.readBytes();
              bitField0_ |= 0x00000020;
              break;
            } // case 50
            case 58: {
              beAttackPower_ = input.readBytes();
              bitField0_ |= 0x00000040;
              break;
            } // case 58
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private xddq.pb.PlayerHeadDataMsg attackHeadInfo_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PlayerHeadDataMsg, xddq.pb.PlayerHeadDataMsg.Builder, xddq.pb.PlayerHeadDataMsgOrBuilder> attackHeadInfoBuilder_;
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg attackHeadInfo = 1;</code>
     * @return Whether the attackHeadInfo field is set.
     */
    public boolean hasAttackHeadInfo() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg attackHeadInfo = 1;</code>
     * @return The attackHeadInfo.
     */
    public xddq.pb.PlayerHeadDataMsg getAttackHeadInfo() {
      if (attackHeadInfoBuilder_ == null) {
        return attackHeadInfo_ == null ? xddq.pb.PlayerHeadDataMsg.getDefaultInstance() : attackHeadInfo_;
      } else {
        return attackHeadInfoBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg attackHeadInfo = 1;</code>
     */
    public Builder setAttackHeadInfo(xddq.pb.PlayerHeadDataMsg value) {
      if (attackHeadInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        attackHeadInfo_ = value;
      } else {
        attackHeadInfoBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg attackHeadInfo = 1;</code>
     */
    public Builder setAttackHeadInfo(
        xddq.pb.PlayerHeadDataMsg.Builder builderForValue) {
      if (attackHeadInfoBuilder_ == null) {
        attackHeadInfo_ = builderForValue.build();
      } else {
        attackHeadInfoBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg attackHeadInfo = 1;</code>
     */
    public Builder mergeAttackHeadInfo(xddq.pb.PlayerHeadDataMsg value) {
      if (attackHeadInfoBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0) &&
          attackHeadInfo_ != null &&
          attackHeadInfo_ != xddq.pb.PlayerHeadDataMsg.getDefaultInstance()) {
          getAttackHeadInfoBuilder().mergeFrom(value);
        } else {
          attackHeadInfo_ = value;
        }
      } else {
        attackHeadInfoBuilder_.mergeFrom(value);
      }
      if (attackHeadInfo_ != null) {
        bitField0_ |= 0x00000001;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg attackHeadInfo = 1;</code>
     */
    public Builder clearAttackHeadInfo() {
      bitField0_ = (bitField0_ & ~0x00000001);
      attackHeadInfo_ = null;
      if (attackHeadInfoBuilder_ != null) {
        attackHeadInfoBuilder_.dispose();
        attackHeadInfoBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg attackHeadInfo = 1;</code>
     */
    public xddq.pb.PlayerHeadDataMsg.Builder getAttackHeadInfoBuilder() {
      bitField0_ |= 0x00000001;
      onChanged();
      return internalGetAttackHeadInfoFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg attackHeadInfo = 1;</code>
     */
    public xddq.pb.PlayerHeadDataMsgOrBuilder getAttackHeadInfoOrBuilder() {
      if (attackHeadInfoBuilder_ != null) {
        return attackHeadInfoBuilder_.getMessageOrBuilder();
      } else {
        return attackHeadInfo_ == null ?
            xddq.pb.PlayerHeadDataMsg.getDefaultInstance() : attackHeadInfo_;
      }
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg attackHeadInfo = 1;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PlayerHeadDataMsg, xddq.pb.PlayerHeadDataMsg.Builder, xddq.pb.PlayerHeadDataMsgOrBuilder> 
        internalGetAttackHeadInfoFieldBuilder() {
      if (attackHeadInfoBuilder_ == null) {
        attackHeadInfoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.PlayerHeadDataMsg, xddq.pb.PlayerHeadDataMsg.Builder, xddq.pb.PlayerHeadDataMsgOrBuilder>(
                getAttackHeadInfo(),
                getParentForChildren(),
                isClean());
        attackHeadInfo_ = null;
      }
      return attackHeadInfoBuilder_;
    }

    private java.lang.Object attackName_ = "";
    /**
     * <code>optional string attackName = 2;</code>
     * @return Whether the attackName field is set.
     */
    public boolean hasAttackName() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional string attackName = 2;</code>
     * @return The attackName.
     */
    public java.lang.String getAttackName() {
      java.lang.Object ref = attackName_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          attackName_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string attackName = 2;</code>
     * @return The bytes for attackName.
     */
    public com.google.protobuf.ByteString
        getAttackNameBytes() {
      java.lang.Object ref = attackName_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        attackName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string attackName = 2;</code>
     * @param value The attackName to set.
     * @return This builder for chaining.
     */
    public Builder setAttackName(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      attackName_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional string attackName = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearAttackName() {
      attackName_ = getDefaultInstance().getAttackName();
      bitField0_ = (bitField0_ & ~0x00000002);
      onChanged();
      return this;
    }
    /**
     * <code>optional string attackName = 2;</code>
     * @param value The bytes for attackName to set.
     * @return This builder for chaining.
     */
    public Builder setAttackNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      attackName_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }

    private xddq.pb.PlayerHeadDataMsg beAttackHeadInfo_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PlayerHeadDataMsg, xddq.pb.PlayerHeadDataMsg.Builder, xddq.pb.PlayerHeadDataMsgOrBuilder> beAttackHeadInfoBuilder_;
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg beAttackHeadInfo = 3;</code>
     * @return Whether the beAttackHeadInfo field is set.
     */
    public boolean hasBeAttackHeadInfo() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg beAttackHeadInfo = 3;</code>
     * @return The beAttackHeadInfo.
     */
    public xddq.pb.PlayerHeadDataMsg getBeAttackHeadInfo() {
      if (beAttackHeadInfoBuilder_ == null) {
        return beAttackHeadInfo_ == null ? xddq.pb.PlayerHeadDataMsg.getDefaultInstance() : beAttackHeadInfo_;
      } else {
        return beAttackHeadInfoBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg beAttackHeadInfo = 3;</code>
     */
    public Builder setBeAttackHeadInfo(xddq.pb.PlayerHeadDataMsg value) {
      if (beAttackHeadInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        beAttackHeadInfo_ = value;
      } else {
        beAttackHeadInfoBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg beAttackHeadInfo = 3;</code>
     */
    public Builder setBeAttackHeadInfo(
        xddq.pb.PlayerHeadDataMsg.Builder builderForValue) {
      if (beAttackHeadInfoBuilder_ == null) {
        beAttackHeadInfo_ = builderForValue.build();
      } else {
        beAttackHeadInfoBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg beAttackHeadInfo = 3;</code>
     */
    public Builder mergeBeAttackHeadInfo(xddq.pb.PlayerHeadDataMsg value) {
      if (beAttackHeadInfoBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0) &&
          beAttackHeadInfo_ != null &&
          beAttackHeadInfo_ != xddq.pb.PlayerHeadDataMsg.getDefaultInstance()) {
          getBeAttackHeadInfoBuilder().mergeFrom(value);
        } else {
          beAttackHeadInfo_ = value;
        }
      } else {
        beAttackHeadInfoBuilder_.mergeFrom(value);
      }
      if (beAttackHeadInfo_ != null) {
        bitField0_ |= 0x00000004;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg beAttackHeadInfo = 3;</code>
     */
    public Builder clearBeAttackHeadInfo() {
      bitField0_ = (bitField0_ & ~0x00000004);
      beAttackHeadInfo_ = null;
      if (beAttackHeadInfoBuilder_ != null) {
        beAttackHeadInfoBuilder_.dispose();
        beAttackHeadInfoBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg beAttackHeadInfo = 3;</code>
     */
    public xddq.pb.PlayerHeadDataMsg.Builder getBeAttackHeadInfoBuilder() {
      bitField0_ |= 0x00000004;
      onChanged();
      return internalGetBeAttackHeadInfoFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg beAttackHeadInfo = 3;</code>
     */
    public xddq.pb.PlayerHeadDataMsgOrBuilder getBeAttackHeadInfoOrBuilder() {
      if (beAttackHeadInfoBuilder_ != null) {
        return beAttackHeadInfoBuilder_.getMessageOrBuilder();
      } else {
        return beAttackHeadInfo_ == null ?
            xddq.pb.PlayerHeadDataMsg.getDefaultInstance() : beAttackHeadInfo_;
      }
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg beAttackHeadInfo = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PlayerHeadDataMsg, xddq.pb.PlayerHeadDataMsg.Builder, xddq.pb.PlayerHeadDataMsgOrBuilder> 
        internalGetBeAttackHeadInfoFieldBuilder() {
      if (beAttackHeadInfoBuilder_ == null) {
        beAttackHeadInfoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.PlayerHeadDataMsg, xddq.pb.PlayerHeadDataMsg.Builder, xddq.pb.PlayerHeadDataMsgOrBuilder>(
                getBeAttackHeadInfo(),
                getParentForChildren(),
                isClean());
        beAttackHeadInfo_ = null;
      }
      return beAttackHeadInfoBuilder_;
    }

    private java.lang.Object beAttackName_ = "";
    /**
     * <code>optional string beAttackName = 4;</code>
     * @return Whether the beAttackName field is set.
     */
    public boolean hasBeAttackName() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional string beAttackName = 4;</code>
     * @return The beAttackName.
     */
    public java.lang.String getBeAttackName() {
      java.lang.Object ref = beAttackName_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          beAttackName_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string beAttackName = 4;</code>
     * @return The bytes for beAttackName.
     */
    public com.google.protobuf.ByteString
        getBeAttackNameBytes() {
      java.lang.Object ref = beAttackName_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        beAttackName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string beAttackName = 4;</code>
     * @param value The beAttackName to set.
     * @return This builder for chaining.
     */
    public Builder setBeAttackName(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      beAttackName_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional string beAttackName = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearBeAttackName() {
      beAttackName_ = getDefaultInstance().getBeAttackName();
      bitField0_ = (bitField0_ & ~0x00000008);
      onChanged();
      return this;
    }
    /**
     * <code>optional string beAttackName = 4;</code>
     * @param value The bytes for beAttackName to set.
     * @return This builder for chaining.
     */
    public Builder setBeAttackNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      beAttackName_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }

    private boolean attackWin_ ;
    /**
     * <code>optional bool attackWin = 5;</code>
     * @return Whether the attackWin field is set.
     */
    @java.lang.Override
    public boolean hasAttackWin() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional bool attackWin = 5;</code>
     * @return The attackWin.
     */
    @java.lang.Override
    public boolean getAttackWin() {
      return attackWin_;
    }
    /**
     * <code>optional bool attackWin = 5;</code>
     * @param value The attackWin to set.
     * @return This builder for chaining.
     */
    public Builder setAttackWin(boolean value) {

      attackWin_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional bool attackWin = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearAttackWin() {
      bitField0_ = (bitField0_ & ~0x00000010);
      attackWin_ = false;
      onChanged();
      return this;
    }

    private java.lang.Object attackPower_ = "";
    /**
     * <code>optional string attackPower = 6;</code>
     * @return Whether the attackPower field is set.
     */
    public boolean hasAttackPower() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional string attackPower = 6;</code>
     * @return The attackPower.
     */
    public java.lang.String getAttackPower() {
      java.lang.Object ref = attackPower_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          attackPower_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string attackPower = 6;</code>
     * @return The bytes for attackPower.
     */
    public com.google.protobuf.ByteString
        getAttackPowerBytes() {
      java.lang.Object ref = attackPower_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        attackPower_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string attackPower = 6;</code>
     * @param value The attackPower to set.
     * @return This builder for chaining.
     */
    public Builder setAttackPower(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      attackPower_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>optional string attackPower = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearAttackPower() {
      attackPower_ = getDefaultInstance().getAttackPower();
      bitField0_ = (bitField0_ & ~0x00000020);
      onChanged();
      return this;
    }
    /**
     * <code>optional string attackPower = 6;</code>
     * @param value The bytes for attackPower to set.
     * @return This builder for chaining.
     */
    public Builder setAttackPowerBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      attackPower_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }

    private java.lang.Object beAttackPower_ = "";
    /**
     * <code>optional string beAttackPower = 7;</code>
     * @return Whether the beAttackPower field is set.
     */
    public boolean hasBeAttackPower() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <code>optional string beAttackPower = 7;</code>
     * @return The beAttackPower.
     */
    public java.lang.String getBeAttackPower() {
      java.lang.Object ref = beAttackPower_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          beAttackPower_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string beAttackPower = 7;</code>
     * @return The bytes for beAttackPower.
     */
    public com.google.protobuf.ByteString
        getBeAttackPowerBytes() {
      java.lang.Object ref = beAttackPower_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        beAttackPower_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string beAttackPower = 7;</code>
     * @param value The beAttackPower to set.
     * @return This builder for chaining.
     */
    public Builder setBeAttackPower(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      beAttackPower_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>optional string beAttackPower = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearBeAttackPower() {
      beAttackPower_ = getDefaultInstance().getBeAttackPower();
      bitField0_ = (bitField0_ & ~0x00000040);
      onChanged();
      return this;
    }
    /**
     * <code>optional string beAttackPower = 7;</code>
     * @param value The bytes for beAttackPower to set.
     * @return This builder for chaining.
     */
    public Builder setBeAttackPowerBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      beAttackPower_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.GodIslandCityReportDetail)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.GodIslandCityReportDetail)
  private static final xddq.pb.GodIslandCityReportDetail DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.GodIslandCityReportDetail();
  }

  public static xddq.pb.GodIslandCityReportDetail getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<GodIslandCityReportDetail>
      PARSER = new com.google.protobuf.AbstractParser<GodIslandCityReportDetail>() {
    @java.lang.Override
    public GodIslandCityReportDetail parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<GodIslandCityReportDetail> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<GodIslandCityReportDetail> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.GodIslandCityReportDetail getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

