// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.UnionAreaWarDragonAttackData}
 */
public final class UnionAreaWarDragonAttackData extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.UnionAreaWarDragonAttackData)
    UnionAreaWarDragonAttackDataOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      UnionAreaWarDragonAttackData.class.getName());
  }
  // Use UnionAreaWarDragonAttackData.newBuilder() to construct.
  private UnionAreaWarDragonAttackData(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private UnionAreaWarDragonAttackData() {
    id_ = "";
    areaInfo_ = java.util.Collections.emptyList();
    damageValue_ = "";
    killReward_ = "";
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_UnionAreaWarDragonAttackData_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_UnionAreaWarDragonAttackData_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.UnionAreaWarDragonAttackData.class, xddq.pb.UnionAreaWarDragonAttackData.Builder.class);
  }

  private int bitField0_;
  public static final int ID_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private volatile java.lang.Object id_ = "";
  /**
   * <code>required string id = 1;</code>
   * @return Whether the id field is set.
   */
  @java.lang.Override
  public boolean hasId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required string id = 1;</code>
   * @return The id.
   */
  @java.lang.Override
  public java.lang.String getId() {
    java.lang.Object ref = id_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        id_ = s;
      }
      return s;
    }
  }
  /**
   * <code>required string id = 1;</code>
   * @return The bytes for id.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getIdBytes() {
    java.lang.Object ref = id_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      id_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ATTACKTIME_FIELD_NUMBER = 2;
  private long attackTime_ = 0L;
  /**
   * <code>optional int64 attackTime = 2;</code>
   * @return Whether the attackTime field is set.
   */
  @java.lang.Override
  public boolean hasAttackTime() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int64 attackTime = 2;</code>
   * @return The attackTime.
   */
  @java.lang.Override
  public long getAttackTime() {
    return attackTime_;
  }

  public static final int PLAYER_FIELD_NUMBER = 3;
  private xddq.pb.UnionAreaWarShowMsg player_;
  /**
   * <code>optional .xddq.pb.UnionAreaWarShowMsg player = 3;</code>
   * @return Whether the player field is set.
   */
  @java.lang.Override
  public boolean hasPlayer() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional .xddq.pb.UnionAreaWarShowMsg player = 3;</code>
   * @return The player.
   */
  @java.lang.Override
  public xddq.pb.UnionAreaWarShowMsg getPlayer() {
    return player_ == null ? xddq.pb.UnionAreaWarShowMsg.getDefaultInstance() : player_;
  }
  /**
   * <code>optional .xddq.pb.UnionAreaWarShowMsg player = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.UnionAreaWarShowMsgOrBuilder getPlayerOrBuilder() {
    return player_ == null ? xddq.pb.UnionAreaWarShowMsg.getDefaultInstance() : player_;
  }

  public static final int ATTACKTYPE_FIELD_NUMBER = 4;
  private int attackType_ = 0;
  /**
   * <code>optional int32 attackType = 4;</code>
   * @return Whether the attackType field is set.
   */
  @java.lang.Override
  public boolean hasAttackType() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional int32 attackType = 4;</code>
   * @return The attackType.
   */
  @java.lang.Override
  public int getAttackType() {
    return attackType_;
  }

  public static final int AREAINFO_FIELD_NUMBER = 5;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.UnionAreaInfo> areaInfo_;
  /**
   * <code>repeated .xddq.pb.UnionAreaInfo areaInfo = 5;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.UnionAreaInfo> getAreaInfoList() {
    return areaInfo_;
  }
  /**
   * <code>repeated .xddq.pb.UnionAreaInfo areaInfo = 5;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.UnionAreaInfoOrBuilder> 
      getAreaInfoOrBuilderList() {
    return areaInfo_;
  }
  /**
   * <code>repeated .xddq.pb.UnionAreaInfo areaInfo = 5;</code>
   */
  @java.lang.Override
  public int getAreaInfoCount() {
    return areaInfo_.size();
  }
  /**
   * <code>repeated .xddq.pb.UnionAreaInfo areaInfo = 5;</code>
   */
  @java.lang.Override
  public xddq.pb.UnionAreaInfo getAreaInfo(int index) {
    return areaInfo_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.UnionAreaInfo areaInfo = 5;</code>
   */
  @java.lang.Override
  public xddq.pb.UnionAreaInfoOrBuilder getAreaInfoOrBuilder(
      int index) {
    return areaInfo_.get(index);
  }

  public static final int KILLPLAYERCOUNT_FIELD_NUMBER = 6;
  private int killPlayerCount_ = 0;
  /**
   * <code>optional int32 killPlayerCount = 6;</code>
   * @return Whether the killPlayerCount field is set.
   */
  @java.lang.Override
  public boolean hasKillPlayerCount() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>optional int32 killPlayerCount = 6;</code>
   * @return The killPlayerCount.
   */
  @java.lang.Override
  public int getKillPlayerCount() {
    return killPlayerCount_;
  }

  public static final int DAMAGEVALUE_FIELD_NUMBER = 7;
  @SuppressWarnings("serial")
  private volatile java.lang.Object damageValue_ = "";
  /**
   * <code>optional string damageValue = 7;</code>
   * @return Whether the damageValue field is set.
   */
  @java.lang.Override
  public boolean hasDamageValue() {
    return ((bitField0_ & 0x00000020) != 0);
  }
  /**
   * <code>optional string damageValue = 7;</code>
   * @return The damageValue.
   */
  @java.lang.Override
  public java.lang.String getDamageValue() {
    java.lang.Object ref = damageValue_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        damageValue_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string damageValue = 7;</code>
   * @return The bytes for damageValue.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getDamageValueBytes() {
    java.lang.Object ref = damageValue_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      damageValue_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int KILLREWARD_FIELD_NUMBER = 8;
  @SuppressWarnings("serial")
  private volatile java.lang.Object killReward_ = "";
  /**
   * <code>optional string killReward = 8;</code>
   * @return Whether the killReward field is set.
   */
  @java.lang.Override
  public boolean hasKillReward() {
    return ((bitField0_ & 0x00000040) != 0);
  }
  /**
   * <code>optional string killReward = 8;</code>
   * @return The killReward.
   */
  @java.lang.Override
  public java.lang.String getKillReward() {
    java.lang.Object ref = killReward_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        killReward_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string killReward = 8;</code>
   * @return The bytes for killReward.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getKillRewardBytes() {
    java.lang.Object ref = killReward_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      killReward_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasId()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (hasPlayer()) {
      if (!getPlayer().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    for (int i = 0; i < getAreaInfoCount(); i++) {
      if (!getAreaInfo(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 1, id_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt64(2, attackTime_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeMessage(3, getPlayer());
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt32(4, attackType_);
    }
    for (int i = 0; i < areaInfo_.size(); i++) {
      output.writeMessage(5, areaInfo_.get(i));
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      output.writeInt32(6, killPlayerCount_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 7, damageValue_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 8, killReward_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(1, id_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(2, attackTime_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, getPlayer());
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(4, attackType_);
    }
    for (int i = 0; i < areaInfo_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(5, areaInfo_.get(i));
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(6, killPlayerCount_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(7, damageValue_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(8, killReward_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.UnionAreaWarDragonAttackData)) {
      return super.equals(obj);
    }
    xddq.pb.UnionAreaWarDragonAttackData other = (xddq.pb.UnionAreaWarDragonAttackData) obj;

    if (hasId() != other.hasId()) return false;
    if (hasId()) {
      if (!getId()
          .equals(other.getId())) return false;
    }
    if (hasAttackTime() != other.hasAttackTime()) return false;
    if (hasAttackTime()) {
      if (getAttackTime()
          != other.getAttackTime()) return false;
    }
    if (hasPlayer() != other.hasPlayer()) return false;
    if (hasPlayer()) {
      if (!getPlayer()
          .equals(other.getPlayer())) return false;
    }
    if (hasAttackType() != other.hasAttackType()) return false;
    if (hasAttackType()) {
      if (getAttackType()
          != other.getAttackType()) return false;
    }
    if (!getAreaInfoList()
        .equals(other.getAreaInfoList())) return false;
    if (hasKillPlayerCount() != other.hasKillPlayerCount()) return false;
    if (hasKillPlayerCount()) {
      if (getKillPlayerCount()
          != other.getKillPlayerCount()) return false;
    }
    if (hasDamageValue() != other.hasDamageValue()) return false;
    if (hasDamageValue()) {
      if (!getDamageValue()
          .equals(other.getDamageValue())) return false;
    }
    if (hasKillReward() != other.hasKillReward()) return false;
    if (hasKillReward()) {
      if (!getKillReward()
          .equals(other.getKillReward())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasId()) {
      hash = (37 * hash) + ID_FIELD_NUMBER;
      hash = (53 * hash) + getId().hashCode();
    }
    if (hasAttackTime()) {
      hash = (37 * hash) + ATTACKTIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getAttackTime());
    }
    if (hasPlayer()) {
      hash = (37 * hash) + PLAYER_FIELD_NUMBER;
      hash = (53 * hash) + getPlayer().hashCode();
    }
    if (hasAttackType()) {
      hash = (37 * hash) + ATTACKTYPE_FIELD_NUMBER;
      hash = (53 * hash) + getAttackType();
    }
    if (getAreaInfoCount() > 0) {
      hash = (37 * hash) + AREAINFO_FIELD_NUMBER;
      hash = (53 * hash) + getAreaInfoList().hashCode();
    }
    if (hasKillPlayerCount()) {
      hash = (37 * hash) + KILLPLAYERCOUNT_FIELD_NUMBER;
      hash = (53 * hash) + getKillPlayerCount();
    }
    if (hasDamageValue()) {
      hash = (37 * hash) + DAMAGEVALUE_FIELD_NUMBER;
      hash = (53 * hash) + getDamageValue().hashCode();
    }
    if (hasKillReward()) {
      hash = (37 * hash) + KILLREWARD_FIELD_NUMBER;
      hash = (53 * hash) + getKillReward().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.UnionAreaWarDragonAttackData parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.UnionAreaWarDragonAttackData parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.UnionAreaWarDragonAttackData parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.UnionAreaWarDragonAttackData parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.UnionAreaWarDragonAttackData parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.UnionAreaWarDragonAttackData parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.UnionAreaWarDragonAttackData parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.UnionAreaWarDragonAttackData parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.UnionAreaWarDragonAttackData parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.UnionAreaWarDragonAttackData parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.UnionAreaWarDragonAttackData parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.UnionAreaWarDragonAttackData parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.UnionAreaWarDragonAttackData prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.UnionAreaWarDragonAttackData}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.UnionAreaWarDragonAttackData)
      xddq.pb.UnionAreaWarDragonAttackDataOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_UnionAreaWarDragonAttackData_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_UnionAreaWarDragonAttackData_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.UnionAreaWarDragonAttackData.class, xddq.pb.UnionAreaWarDragonAttackData.Builder.class);
    }

    // Construct using xddq.pb.UnionAreaWarDragonAttackData.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetPlayerFieldBuilder();
        internalGetAreaInfoFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      id_ = "";
      attackTime_ = 0L;
      player_ = null;
      if (playerBuilder_ != null) {
        playerBuilder_.dispose();
        playerBuilder_ = null;
      }
      attackType_ = 0;
      if (areaInfoBuilder_ == null) {
        areaInfo_ = java.util.Collections.emptyList();
      } else {
        areaInfo_ = null;
        areaInfoBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000010);
      killPlayerCount_ = 0;
      damageValue_ = "";
      killReward_ = "";
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_UnionAreaWarDragonAttackData_descriptor;
    }

    @java.lang.Override
    public xddq.pb.UnionAreaWarDragonAttackData getDefaultInstanceForType() {
      return xddq.pb.UnionAreaWarDragonAttackData.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.UnionAreaWarDragonAttackData build() {
      xddq.pb.UnionAreaWarDragonAttackData result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.UnionAreaWarDragonAttackData buildPartial() {
      xddq.pb.UnionAreaWarDragonAttackData result = new xddq.pb.UnionAreaWarDragonAttackData(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.UnionAreaWarDragonAttackData result) {
      if (areaInfoBuilder_ == null) {
        if (((bitField0_ & 0x00000010) != 0)) {
          areaInfo_ = java.util.Collections.unmodifiableList(areaInfo_);
          bitField0_ = (bitField0_ & ~0x00000010);
        }
        result.areaInfo_ = areaInfo_;
      } else {
        result.areaInfo_ = areaInfoBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.UnionAreaWarDragonAttackData result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.id_ = id_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.attackTime_ = attackTime_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.player_ = playerBuilder_ == null
            ? player_
            : playerBuilder_.build();
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.attackType_ = attackType_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.killPlayerCount_ = killPlayerCount_;
        to_bitField0_ |= 0x00000010;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.damageValue_ = damageValue_;
        to_bitField0_ |= 0x00000020;
      }
      if (((from_bitField0_ & 0x00000080) != 0)) {
        result.killReward_ = killReward_;
        to_bitField0_ |= 0x00000040;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.UnionAreaWarDragonAttackData) {
        return mergeFrom((xddq.pb.UnionAreaWarDragonAttackData)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.UnionAreaWarDragonAttackData other) {
      if (other == xddq.pb.UnionAreaWarDragonAttackData.getDefaultInstance()) return this;
      if (other.hasId()) {
        id_ = other.id_;
        bitField0_ |= 0x00000001;
        onChanged();
      }
      if (other.hasAttackTime()) {
        setAttackTime(other.getAttackTime());
      }
      if (other.hasPlayer()) {
        mergePlayer(other.getPlayer());
      }
      if (other.hasAttackType()) {
        setAttackType(other.getAttackType());
      }
      if (areaInfoBuilder_ == null) {
        if (!other.areaInfo_.isEmpty()) {
          if (areaInfo_.isEmpty()) {
            areaInfo_ = other.areaInfo_;
            bitField0_ = (bitField0_ & ~0x00000010);
          } else {
            ensureAreaInfoIsMutable();
            areaInfo_.addAll(other.areaInfo_);
          }
          onChanged();
        }
      } else {
        if (!other.areaInfo_.isEmpty()) {
          if (areaInfoBuilder_.isEmpty()) {
            areaInfoBuilder_.dispose();
            areaInfoBuilder_ = null;
            areaInfo_ = other.areaInfo_;
            bitField0_ = (bitField0_ & ~0x00000010);
            areaInfoBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetAreaInfoFieldBuilder() : null;
          } else {
            areaInfoBuilder_.addAllMessages(other.areaInfo_);
          }
        }
      }
      if (other.hasKillPlayerCount()) {
        setKillPlayerCount(other.getKillPlayerCount());
      }
      if (other.hasDamageValue()) {
        damageValue_ = other.damageValue_;
        bitField0_ |= 0x00000040;
        onChanged();
      }
      if (other.hasKillReward()) {
        killReward_ = other.killReward_;
        bitField0_ |= 0x00000080;
        onChanged();
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasId()) {
        return false;
      }
      if (hasPlayer()) {
        if (!getPlayer().isInitialized()) {
          return false;
        }
      }
      for (int i = 0; i < getAreaInfoCount(); i++) {
        if (!getAreaInfo(i).isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              id_ = input.readBytes();
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 16: {
              attackTime_ = input.readInt64();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 26: {
              input.readMessage(
                  internalGetPlayerFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 32: {
              attackType_ = input.readInt32();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            case 42: {
              xddq.pb.UnionAreaInfo m =
                  input.readMessage(
                      xddq.pb.UnionAreaInfo.parser(),
                      extensionRegistry);
              if (areaInfoBuilder_ == null) {
                ensureAreaInfoIsMutable();
                areaInfo_.add(m);
              } else {
                areaInfoBuilder_.addMessage(m);
              }
              break;
            } // case 42
            case 48: {
              killPlayerCount_ = input.readInt32();
              bitField0_ |= 0x00000020;
              break;
            } // case 48
            case 58: {
              damageValue_ = input.readBytes();
              bitField0_ |= 0x00000040;
              break;
            } // case 58
            case 66: {
              killReward_ = input.readBytes();
              bitField0_ |= 0x00000080;
              break;
            } // case 66
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.lang.Object id_ = "";
    /**
     * <code>required string id = 1;</code>
     * @return Whether the id field is set.
     */
    public boolean hasId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required string id = 1;</code>
     * @return The id.
     */
    public java.lang.String getId() {
      java.lang.Object ref = id_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          id_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>required string id = 1;</code>
     * @return The bytes for id.
     */
    public com.google.protobuf.ByteString
        getIdBytes() {
      java.lang.Object ref = id_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        id_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>required string id = 1;</code>
     * @param value The id to set.
     * @return This builder for chaining.
     */
    public Builder setId(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      id_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required string id = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearId() {
      id_ = getDefaultInstance().getId();
      bitField0_ = (bitField0_ & ~0x00000001);
      onChanged();
      return this;
    }
    /**
     * <code>required string id = 1;</code>
     * @param value The bytes for id to set.
     * @return This builder for chaining.
     */
    public Builder setIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      id_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }

    private long attackTime_ ;
    /**
     * <code>optional int64 attackTime = 2;</code>
     * @return Whether the attackTime field is set.
     */
    @java.lang.Override
    public boolean hasAttackTime() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int64 attackTime = 2;</code>
     * @return The attackTime.
     */
    @java.lang.Override
    public long getAttackTime() {
      return attackTime_;
    }
    /**
     * <code>optional int64 attackTime = 2;</code>
     * @param value The attackTime to set.
     * @return This builder for chaining.
     */
    public Builder setAttackTime(long value) {

      attackTime_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 attackTime = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearAttackTime() {
      bitField0_ = (bitField0_ & ~0x00000002);
      attackTime_ = 0L;
      onChanged();
      return this;
    }

    private xddq.pb.UnionAreaWarShowMsg player_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.UnionAreaWarShowMsg, xddq.pb.UnionAreaWarShowMsg.Builder, xddq.pb.UnionAreaWarShowMsgOrBuilder> playerBuilder_;
    /**
     * <code>optional .xddq.pb.UnionAreaWarShowMsg player = 3;</code>
     * @return Whether the player field is set.
     */
    public boolean hasPlayer() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional .xddq.pb.UnionAreaWarShowMsg player = 3;</code>
     * @return The player.
     */
    public xddq.pb.UnionAreaWarShowMsg getPlayer() {
      if (playerBuilder_ == null) {
        return player_ == null ? xddq.pb.UnionAreaWarShowMsg.getDefaultInstance() : player_;
      } else {
        return playerBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.UnionAreaWarShowMsg player = 3;</code>
     */
    public Builder setPlayer(xddq.pb.UnionAreaWarShowMsg value) {
      if (playerBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        player_ = value;
      } else {
        playerBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.UnionAreaWarShowMsg player = 3;</code>
     */
    public Builder setPlayer(
        xddq.pb.UnionAreaWarShowMsg.Builder builderForValue) {
      if (playerBuilder_ == null) {
        player_ = builderForValue.build();
      } else {
        playerBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.UnionAreaWarShowMsg player = 3;</code>
     */
    public Builder mergePlayer(xddq.pb.UnionAreaWarShowMsg value) {
      if (playerBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0) &&
          player_ != null &&
          player_ != xddq.pb.UnionAreaWarShowMsg.getDefaultInstance()) {
          getPlayerBuilder().mergeFrom(value);
        } else {
          player_ = value;
        }
      } else {
        playerBuilder_.mergeFrom(value);
      }
      if (player_ != null) {
        bitField0_ |= 0x00000004;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.UnionAreaWarShowMsg player = 3;</code>
     */
    public Builder clearPlayer() {
      bitField0_ = (bitField0_ & ~0x00000004);
      player_ = null;
      if (playerBuilder_ != null) {
        playerBuilder_.dispose();
        playerBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.UnionAreaWarShowMsg player = 3;</code>
     */
    public xddq.pb.UnionAreaWarShowMsg.Builder getPlayerBuilder() {
      bitField0_ |= 0x00000004;
      onChanged();
      return internalGetPlayerFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.UnionAreaWarShowMsg player = 3;</code>
     */
    public xddq.pb.UnionAreaWarShowMsgOrBuilder getPlayerOrBuilder() {
      if (playerBuilder_ != null) {
        return playerBuilder_.getMessageOrBuilder();
      } else {
        return player_ == null ?
            xddq.pb.UnionAreaWarShowMsg.getDefaultInstance() : player_;
      }
    }
    /**
     * <code>optional .xddq.pb.UnionAreaWarShowMsg player = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.UnionAreaWarShowMsg, xddq.pb.UnionAreaWarShowMsg.Builder, xddq.pb.UnionAreaWarShowMsgOrBuilder> 
        internalGetPlayerFieldBuilder() {
      if (playerBuilder_ == null) {
        playerBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.UnionAreaWarShowMsg, xddq.pb.UnionAreaWarShowMsg.Builder, xddq.pb.UnionAreaWarShowMsgOrBuilder>(
                getPlayer(),
                getParentForChildren(),
                isClean());
        player_ = null;
      }
      return playerBuilder_;
    }

    private int attackType_ ;
    /**
     * <code>optional int32 attackType = 4;</code>
     * @return Whether the attackType field is set.
     */
    @java.lang.Override
    public boolean hasAttackType() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int32 attackType = 4;</code>
     * @return The attackType.
     */
    @java.lang.Override
    public int getAttackType() {
      return attackType_;
    }
    /**
     * <code>optional int32 attackType = 4;</code>
     * @param value The attackType to set.
     * @return This builder for chaining.
     */
    public Builder setAttackType(int value) {

      attackType_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 attackType = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearAttackType() {
      bitField0_ = (bitField0_ & ~0x00000008);
      attackType_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.UnionAreaInfo> areaInfo_ =
      java.util.Collections.emptyList();
    private void ensureAreaInfoIsMutable() {
      if (!((bitField0_ & 0x00000010) != 0)) {
        areaInfo_ = new java.util.ArrayList<xddq.pb.UnionAreaInfo>(areaInfo_);
        bitField0_ |= 0x00000010;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.UnionAreaInfo, xddq.pb.UnionAreaInfo.Builder, xddq.pb.UnionAreaInfoOrBuilder> areaInfoBuilder_;

    /**
     * <code>repeated .xddq.pb.UnionAreaInfo areaInfo = 5;</code>
     */
    public java.util.List<xddq.pb.UnionAreaInfo> getAreaInfoList() {
      if (areaInfoBuilder_ == null) {
        return java.util.Collections.unmodifiableList(areaInfo_);
      } else {
        return areaInfoBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.UnionAreaInfo areaInfo = 5;</code>
     */
    public int getAreaInfoCount() {
      if (areaInfoBuilder_ == null) {
        return areaInfo_.size();
      } else {
        return areaInfoBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.UnionAreaInfo areaInfo = 5;</code>
     */
    public xddq.pb.UnionAreaInfo getAreaInfo(int index) {
      if (areaInfoBuilder_ == null) {
        return areaInfo_.get(index);
      } else {
        return areaInfoBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.UnionAreaInfo areaInfo = 5;</code>
     */
    public Builder setAreaInfo(
        int index, xddq.pb.UnionAreaInfo value) {
      if (areaInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureAreaInfoIsMutable();
        areaInfo_.set(index, value);
        onChanged();
      } else {
        areaInfoBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionAreaInfo areaInfo = 5;</code>
     */
    public Builder setAreaInfo(
        int index, xddq.pb.UnionAreaInfo.Builder builderForValue) {
      if (areaInfoBuilder_ == null) {
        ensureAreaInfoIsMutable();
        areaInfo_.set(index, builderForValue.build());
        onChanged();
      } else {
        areaInfoBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionAreaInfo areaInfo = 5;</code>
     */
    public Builder addAreaInfo(xddq.pb.UnionAreaInfo value) {
      if (areaInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureAreaInfoIsMutable();
        areaInfo_.add(value);
        onChanged();
      } else {
        areaInfoBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionAreaInfo areaInfo = 5;</code>
     */
    public Builder addAreaInfo(
        int index, xddq.pb.UnionAreaInfo value) {
      if (areaInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureAreaInfoIsMutable();
        areaInfo_.add(index, value);
        onChanged();
      } else {
        areaInfoBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionAreaInfo areaInfo = 5;</code>
     */
    public Builder addAreaInfo(
        xddq.pb.UnionAreaInfo.Builder builderForValue) {
      if (areaInfoBuilder_ == null) {
        ensureAreaInfoIsMutable();
        areaInfo_.add(builderForValue.build());
        onChanged();
      } else {
        areaInfoBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionAreaInfo areaInfo = 5;</code>
     */
    public Builder addAreaInfo(
        int index, xddq.pb.UnionAreaInfo.Builder builderForValue) {
      if (areaInfoBuilder_ == null) {
        ensureAreaInfoIsMutable();
        areaInfo_.add(index, builderForValue.build());
        onChanged();
      } else {
        areaInfoBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionAreaInfo areaInfo = 5;</code>
     */
    public Builder addAllAreaInfo(
        java.lang.Iterable<? extends xddq.pb.UnionAreaInfo> values) {
      if (areaInfoBuilder_ == null) {
        ensureAreaInfoIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, areaInfo_);
        onChanged();
      } else {
        areaInfoBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionAreaInfo areaInfo = 5;</code>
     */
    public Builder clearAreaInfo() {
      if (areaInfoBuilder_ == null) {
        areaInfo_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000010);
        onChanged();
      } else {
        areaInfoBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionAreaInfo areaInfo = 5;</code>
     */
    public Builder removeAreaInfo(int index) {
      if (areaInfoBuilder_ == null) {
        ensureAreaInfoIsMutable();
        areaInfo_.remove(index);
        onChanged();
      } else {
        areaInfoBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.UnionAreaInfo areaInfo = 5;</code>
     */
    public xddq.pb.UnionAreaInfo.Builder getAreaInfoBuilder(
        int index) {
      return internalGetAreaInfoFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.UnionAreaInfo areaInfo = 5;</code>
     */
    public xddq.pb.UnionAreaInfoOrBuilder getAreaInfoOrBuilder(
        int index) {
      if (areaInfoBuilder_ == null) {
        return areaInfo_.get(index);  } else {
        return areaInfoBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.UnionAreaInfo areaInfo = 5;</code>
     */
    public java.util.List<? extends xddq.pb.UnionAreaInfoOrBuilder> 
         getAreaInfoOrBuilderList() {
      if (areaInfoBuilder_ != null) {
        return areaInfoBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(areaInfo_);
      }
    }
    /**
     * <code>repeated .xddq.pb.UnionAreaInfo areaInfo = 5;</code>
     */
    public xddq.pb.UnionAreaInfo.Builder addAreaInfoBuilder() {
      return internalGetAreaInfoFieldBuilder().addBuilder(
          xddq.pb.UnionAreaInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.UnionAreaInfo areaInfo = 5;</code>
     */
    public xddq.pb.UnionAreaInfo.Builder addAreaInfoBuilder(
        int index) {
      return internalGetAreaInfoFieldBuilder().addBuilder(
          index, xddq.pb.UnionAreaInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.UnionAreaInfo areaInfo = 5;</code>
     */
    public java.util.List<xddq.pb.UnionAreaInfo.Builder> 
         getAreaInfoBuilderList() {
      return internalGetAreaInfoFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.UnionAreaInfo, xddq.pb.UnionAreaInfo.Builder, xddq.pb.UnionAreaInfoOrBuilder> 
        internalGetAreaInfoFieldBuilder() {
      if (areaInfoBuilder_ == null) {
        areaInfoBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.UnionAreaInfo, xddq.pb.UnionAreaInfo.Builder, xddq.pb.UnionAreaInfoOrBuilder>(
                areaInfo_,
                ((bitField0_ & 0x00000010) != 0),
                getParentForChildren(),
                isClean());
        areaInfo_ = null;
      }
      return areaInfoBuilder_;
    }

    private int killPlayerCount_ ;
    /**
     * <code>optional int32 killPlayerCount = 6;</code>
     * @return Whether the killPlayerCount field is set.
     */
    @java.lang.Override
    public boolean hasKillPlayerCount() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional int32 killPlayerCount = 6;</code>
     * @return The killPlayerCount.
     */
    @java.lang.Override
    public int getKillPlayerCount() {
      return killPlayerCount_;
    }
    /**
     * <code>optional int32 killPlayerCount = 6;</code>
     * @param value The killPlayerCount to set.
     * @return This builder for chaining.
     */
    public Builder setKillPlayerCount(int value) {

      killPlayerCount_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 killPlayerCount = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearKillPlayerCount() {
      bitField0_ = (bitField0_ & ~0x00000020);
      killPlayerCount_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object damageValue_ = "";
    /**
     * <code>optional string damageValue = 7;</code>
     * @return Whether the damageValue field is set.
     */
    public boolean hasDamageValue() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <code>optional string damageValue = 7;</code>
     * @return The damageValue.
     */
    public java.lang.String getDamageValue() {
      java.lang.Object ref = damageValue_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          damageValue_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string damageValue = 7;</code>
     * @return The bytes for damageValue.
     */
    public com.google.protobuf.ByteString
        getDamageValueBytes() {
      java.lang.Object ref = damageValue_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        damageValue_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string damageValue = 7;</code>
     * @param value The damageValue to set.
     * @return This builder for chaining.
     */
    public Builder setDamageValue(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      damageValue_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>optional string damageValue = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearDamageValue() {
      damageValue_ = getDefaultInstance().getDamageValue();
      bitField0_ = (bitField0_ & ~0x00000040);
      onChanged();
      return this;
    }
    /**
     * <code>optional string damageValue = 7;</code>
     * @param value The bytes for damageValue to set.
     * @return This builder for chaining.
     */
    public Builder setDamageValueBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      damageValue_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }

    private java.lang.Object killReward_ = "";
    /**
     * <code>optional string killReward = 8;</code>
     * @return Whether the killReward field is set.
     */
    public boolean hasKillReward() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <code>optional string killReward = 8;</code>
     * @return The killReward.
     */
    public java.lang.String getKillReward() {
      java.lang.Object ref = killReward_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          killReward_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string killReward = 8;</code>
     * @return The bytes for killReward.
     */
    public com.google.protobuf.ByteString
        getKillRewardBytes() {
      java.lang.Object ref = killReward_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        killReward_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string killReward = 8;</code>
     * @param value The killReward to set.
     * @return This builder for chaining.
     */
    public Builder setKillReward(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      killReward_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }
    /**
     * <code>optional string killReward = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearKillReward() {
      killReward_ = getDefaultInstance().getKillReward();
      bitField0_ = (bitField0_ & ~0x00000080);
      onChanged();
      return this;
    }
    /**
     * <code>optional string killReward = 8;</code>
     * @param value The bytes for killReward to set.
     * @return This builder for chaining.
     */
    public Builder setKillRewardBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      killReward_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.UnionAreaWarDragonAttackData)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.UnionAreaWarDragonAttackData)
  private static final xddq.pb.UnionAreaWarDragonAttackData DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.UnionAreaWarDragonAttackData();
  }

  public static xddq.pb.UnionAreaWarDragonAttackData getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<UnionAreaWarDragonAttackData>
      PARSER = new com.google.protobuf.AbstractParser<UnionAreaWarDragonAttackData>() {
    @java.lang.Override
    public UnionAreaWarDragonAttackData parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<UnionAreaWarDragonAttackData> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<UnionAreaWarDragonAttackData> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.UnionAreaWarDragonAttackData getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

