// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.PlanesTrialVideoInfoReq}
 */
public final class PlanesTrialVideoInfoReq extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.PlanesTrialVideoInfoReq)
    PlanesTrialVideoInfoReqOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      PlanesTrialVideoInfoReq.class.getName());
  }
  // Use PlanesTrialVideoInfoReq.newBuilder() to construct.
  private PlanesTrialVideoInfoReq(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private PlanesTrialVideoInfoReq() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_PlanesTrialVideoInfoReq_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_PlanesTrialVideoInfoReq_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.PlanesTrialVideoInfoReq.class, xddq.pb.PlanesTrialVideoInfoReq.Builder.class);
  }

  private int bitField0_;
  public static final int LEVELID_FIELD_NUMBER = 1;
  private int levelId_ = 0;
  /**
   * <code>required int32 levelId = 1;</code>
   * @return Whether the levelId field is set.
   */
  @java.lang.Override
  public boolean hasLevelId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 levelId = 1;</code>
   * @return The levelId.
   */
  @java.lang.Override
  public int getLevelId() {
    return levelId_;
  }

  public static final int TRIALTYPE_FIELD_NUMBER = 2;
  private int trialType_ = 0;
  /**
   * <code>required int32 trialType = 2;</code>
   * @return Whether the trialType field is set.
   */
  @java.lang.Override
  public boolean hasTrialType() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>required int32 trialType = 2;</code>
   * @return The trialType.
   */
  @java.lang.Override
  public int getTrialType() {
    return trialType_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasLevelId()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasTrialType()) {
      memoizedIsInitialized = 0;
      return false;
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, levelId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(2, trialType_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, levelId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, trialType_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.PlanesTrialVideoInfoReq)) {
      return super.equals(obj);
    }
    xddq.pb.PlanesTrialVideoInfoReq other = (xddq.pb.PlanesTrialVideoInfoReq) obj;

    if (hasLevelId() != other.hasLevelId()) return false;
    if (hasLevelId()) {
      if (getLevelId()
          != other.getLevelId()) return false;
    }
    if (hasTrialType() != other.hasTrialType()) return false;
    if (hasTrialType()) {
      if (getTrialType()
          != other.getTrialType()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasLevelId()) {
      hash = (37 * hash) + LEVELID_FIELD_NUMBER;
      hash = (53 * hash) + getLevelId();
    }
    if (hasTrialType()) {
      hash = (37 * hash) + TRIALTYPE_FIELD_NUMBER;
      hash = (53 * hash) + getTrialType();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.PlanesTrialVideoInfoReq parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PlanesTrialVideoInfoReq parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PlanesTrialVideoInfoReq parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PlanesTrialVideoInfoReq parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PlanesTrialVideoInfoReq parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PlanesTrialVideoInfoReq parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PlanesTrialVideoInfoReq parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.PlanesTrialVideoInfoReq parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.PlanesTrialVideoInfoReq parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.PlanesTrialVideoInfoReq parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.PlanesTrialVideoInfoReq parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.PlanesTrialVideoInfoReq parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.PlanesTrialVideoInfoReq prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.PlanesTrialVideoInfoReq}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.PlanesTrialVideoInfoReq)
      xddq.pb.PlanesTrialVideoInfoReqOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PlanesTrialVideoInfoReq_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PlanesTrialVideoInfoReq_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.PlanesTrialVideoInfoReq.class, xddq.pb.PlanesTrialVideoInfoReq.Builder.class);
    }

    // Construct using xddq.pb.PlanesTrialVideoInfoReq.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      levelId_ = 0;
      trialType_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PlanesTrialVideoInfoReq_descriptor;
    }

    @java.lang.Override
    public xddq.pb.PlanesTrialVideoInfoReq getDefaultInstanceForType() {
      return xddq.pb.PlanesTrialVideoInfoReq.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.PlanesTrialVideoInfoReq build() {
      xddq.pb.PlanesTrialVideoInfoReq result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.PlanesTrialVideoInfoReq buildPartial() {
      xddq.pb.PlanesTrialVideoInfoReq result = new xddq.pb.PlanesTrialVideoInfoReq(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.PlanesTrialVideoInfoReq result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.levelId_ = levelId_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.trialType_ = trialType_;
        to_bitField0_ |= 0x00000002;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.PlanesTrialVideoInfoReq) {
        return mergeFrom((xddq.pb.PlanesTrialVideoInfoReq)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.PlanesTrialVideoInfoReq other) {
      if (other == xddq.pb.PlanesTrialVideoInfoReq.getDefaultInstance()) return this;
      if (other.hasLevelId()) {
        setLevelId(other.getLevelId());
      }
      if (other.hasTrialType()) {
        setTrialType(other.getTrialType());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasLevelId()) {
        return false;
      }
      if (!hasTrialType()) {
        return false;
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              levelId_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              trialType_ = input.readInt32();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int levelId_ ;
    /**
     * <code>required int32 levelId = 1;</code>
     * @return Whether the levelId field is set.
     */
    @java.lang.Override
    public boolean hasLevelId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 levelId = 1;</code>
     * @return The levelId.
     */
    @java.lang.Override
    public int getLevelId() {
      return levelId_;
    }
    /**
     * <code>required int32 levelId = 1;</code>
     * @param value The levelId to set.
     * @return This builder for chaining.
     */
    public Builder setLevelId(int value) {

      levelId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 levelId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearLevelId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      levelId_ = 0;
      onChanged();
      return this;
    }

    private int trialType_ ;
    /**
     * <code>required int32 trialType = 2;</code>
     * @return Whether the trialType field is set.
     */
    @java.lang.Override
    public boolean hasTrialType() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>required int32 trialType = 2;</code>
     * @return The trialType.
     */
    @java.lang.Override
    public int getTrialType() {
      return trialType_;
    }
    /**
     * <code>required int32 trialType = 2;</code>
     * @param value The trialType to set.
     * @return This builder for chaining.
     */
    public Builder setTrialType(int value) {

      trialType_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 trialType = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearTrialType() {
      bitField0_ = (bitField0_ & ~0x00000002);
      trialType_ = 0;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.PlanesTrialVideoInfoReq)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.PlanesTrialVideoInfoReq)
  private static final xddq.pb.PlanesTrialVideoInfoReq DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.PlanesTrialVideoInfoReq();
  }

  public static xddq.pb.PlanesTrialVideoInfoReq getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<PlanesTrialVideoInfoReq>
      PARSER = new com.google.protobuf.AbstractParser<PlanesTrialVideoInfoReq>() {
    @java.lang.Override
    public PlanesTrialVideoInfoReq parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<PlanesTrialVideoInfoReq> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<PlanesTrialVideoInfoReq> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.PlanesTrialVideoInfoReq getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

