// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.SpiritTrialActivityDataMsg}
 */
public final class SpiritTrialActivityDataMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.SpiritTrialActivityDataMsg)
    SpiritTrialActivityDataMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      SpiritTrialActivityDataMsg.class.getName());
  }
  // Use SpiritTrialActivityDataMsg.newBuilder() to construct.
  private SpiritTrialActivityDataMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private SpiritTrialActivityDataMsg() {
    evilThingDataMsg_ = java.util.Collections.emptyList();
    restSpiritId_ = emptyIntList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_SpiritTrialActivityDataMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_SpiritTrialActivityDataMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.SpiritTrialActivityDataMsg.class, xddq.pb.SpiritTrialActivityDataMsg.Builder.class);
  }

  private int bitField0_;
  public static final int BATTLEITEMNUM_FIELD_NUMBER = 1;
  private int battleItemNum_ = 0;
  /**
   * <code>optional int32 battleItemNum = 1;</code>
   * @return Whether the battleItemNum field is set.
   */
  @java.lang.Override
  public boolean hasBattleItemNum() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int32 battleItemNum = 1;</code>
   * @return The battleItemNum.
   */
  @java.lang.Override
  public int getBattleItemNum() {
    return battleItemNum_;
  }

  public static final int BATTLEITEMLASTRECOVERYTIME_FIELD_NUMBER = 2;
  private long battleItemLastRecoveryTime_ = 0L;
  /**
   * <code>optional int64 battleItemLastRecoveryTime = 2;</code>
   * @return Whether the battleItemLastRecoveryTime field is set.
   */
  @java.lang.Override
  public boolean hasBattleItemLastRecoveryTime() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int64 battleItemLastRecoveryTime = 2;</code>
   * @return The battleItemLastRecoveryTime.
   */
  @java.lang.Override
  public long getBattleItemLastRecoveryTime() {
    return battleItemLastRecoveryTime_;
  }

  public static final int EVILTHINGDATAMSG_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.EvilThingDataMsg> evilThingDataMsg_;
  /**
   * <code>repeated .xddq.pb.EvilThingDataMsg evilThingDataMsg = 3;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.EvilThingDataMsg> getEvilThingDataMsgList() {
    return evilThingDataMsg_;
  }
  /**
   * <code>repeated .xddq.pb.EvilThingDataMsg evilThingDataMsg = 3;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.EvilThingDataMsgOrBuilder> 
      getEvilThingDataMsgOrBuilderList() {
    return evilThingDataMsg_;
  }
  /**
   * <code>repeated .xddq.pb.EvilThingDataMsg evilThingDataMsg = 3;</code>
   */
  @java.lang.Override
  public int getEvilThingDataMsgCount() {
    return evilThingDataMsg_.size();
  }
  /**
   * <code>repeated .xddq.pb.EvilThingDataMsg evilThingDataMsg = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.EvilThingDataMsg getEvilThingDataMsg(int index) {
    return evilThingDataMsg_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.EvilThingDataMsg evilThingDataMsg = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.EvilThingDataMsgOrBuilder getEvilThingDataMsgOrBuilder(
      int index) {
    return evilThingDataMsg_.get(index);
  }

  public static final int RESTSPIRITID_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private com.google.protobuf.Internal.IntList restSpiritId_ =
      emptyIntList();
  /**
   * <code>repeated int32 restSpiritId = 4;</code>
   * @return A list containing the restSpiritId.
   */
  @java.lang.Override
  public java.util.List<java.lang.Integer>
      getRestSpiritIdList() {
    return restSpiritId_;
  }
  /**
   * <code>repeated int32 restSpiritId = 4;</code>
   * @return The count of restSpiritId.
   */
  public int getRestSpiritIdCount() {
    return restSpiritId_.size();
  }
  /**
   * <code>repeated int32 restSpiritId = 4;</code>
   * @param index The index of the element to return.
   * @return The restSpiritId at the given index.
   */
  public int getRestSpiritId(int index) {
    return restSpiritId_.getInt(index);
  }

  public static final int ACTIVITYSCORE_FIELD_NUMBER = 5;
  private int activityScore_ = 0;
  /**
   * <code>optional int32 activityScore = 5;</code>
   * @return Whether the activityScore field is set.
   */
  @java.lang.Override
  public boolean hasActivityScore() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional int32 activityScore = 5;</code>
   * @return The activityScore.
   */
  @java.lang.Override
  public int getActivityScore() {
    return activityScore_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, battleItemNum_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt64(2, battleItemLastRecoveryTime_);
    }
    for (int i = 0; i < evilThingDataMsg_.size(); i++) {
      output.writeMessage(3, evilThingDataMsg_.get(i));
    }
    for (int i = 0; i < restSpiritId_.size(); i++) {
      output.writeInt32(4, restSpiritId_.getInt(i));
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt32(5, activityScore_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, battleItemNum_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(2, battleItemLastRecoveryTime_);
    }
    for (int i = 0; i < evilThingDataMsg_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, evilThingDataMsg_.get(i));
    }
    {
      int dataSize = 0;
      for (int i = 0; i < restSpiritId_.size(); i++) {
        dataSize += com.google.protobuf.CodedOutputStream
          .computeInt32SizeNoTag(restSpiritId_.getInt(i));
      }
      size += dataSize;
      size += 1 * getRestSpiritIdList().size();
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(5, activityScore_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.SpiritTrialActivityDataMsg)) {
      return super.equals(obj);
    }
    xddq.pb.SpiritTrialActivityDataMsg other = (xddq.pb.SpiritTrialActivityDataMsg) obj;

    if (hasBattleItemNum() != other.hasBattleItemNum()) return false;
    if (hasBattleItemNum()) {
      if (getBattleItemNum()
          != other.getBattleItemNum()) return false;
    }
    if (hasBattleItemLastRecoveryTime() != other.hasBattleItemLastRecoveryTime()) return false;
    if (hasBattleItemLastRecoveryTime()) {
      if (getBattleItemLastRecoveryTime()
          != other.getBattleItemLastRecoveryTime()) return false;
    }
    if (!getEvilThingDataMsgList()
        .equals(other.getEvilThingDataMsgList())) return false;
    if (!getRestSpiritIdList()
        .equals(other.getRestSpiritIdList())) return false;
    if (hasActivityScore() != other.hasActivityScore()) return false;
    if (hasActivityScore()) {
      if (getActivityScore()
          != other.getActivityScore()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasBattleItemNum()) {
      hash = (37 * hash) + BATTLEITEMNUM_FIELD_NUMBER;
      hash = (53 * hash) + getBattleItemNum();
    }
    if (hasBattleItemLastRecoveryTime()) {
      hash = (37 * hash) + BATTLEITEMLASTRECOVERYTIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getBattleItemLastRecoveryTime());
    }
    if (getEvilThingDataMsgCount() > 0) {
      hash = (37 * hash) + EVILTHINGDATAMSG_FIELD_NUMBER;
      hash = (53 * hash) + getEvilThingDataMsgList().hashCode();
    }
    if (getRestSpiritIdCount() > 0) {
      hash = (37 * hash) + RESTSPIRITID_FIELD_NUMBER;
      hash = (53 * hash) + getRestSpiritIdList().hashCode();
    }
    if (hasActivityScore()) {
      hash = (37 * hash) + ACTIVITYSCORE_FIELD_NUMBER;
      hash = (53 * hash) + getActivityScore();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.SpiritTrialActivityDataMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.SpiritTrialActivityDataMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.SpiritTrialActivityDataMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.SpiritTrialActivityDataMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.SpiritTrialActivityDataMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.SpiritTrialActivityDataMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.SpiritTrialActivityDataMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.SpiritTrialActivityDataMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.SpiritTrialActivityDataMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.SpiritTrialActivityDataMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.SpiritTrialActivityDataMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.SpiritTrialActivityDataMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.SpiritTrialActivityDataMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.SpiritTrialActivityDataMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.SpiritTrialActivityDataMsg)
      xddq.pb.SpiritTrialActivityDataMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_SpiritTrialActivityDataMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_SpiritTrialActivityDataMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.SpiritTrialActivityDataMsg.class, xddq.pb.SpiritTrialActivityDataMsg.Builder.class);
    }

    // Construct using xddq.pb.SpiritTrialActivityDataMsg.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      battleItemNum_ = 0;
      battleItemLastRecoveryTime_ = 0L;
      if (evilThingDataMsgBuilder_ == null) {
        evilThingDataMsg_ = java.util.Collections.emptyList();
      } else {
        evilThingDataMsg_ = null;
        evilThingDataMsgBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000004);
      restSpiritId_ = emptyIntList();
      activityScore_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_SpiritTrialActivityDataMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.SpiritTrialActivityDataMsg getDefaultInstanceForType() {
      return xddq.pb.SpiritTrialActivityDataMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.SpiritTrialActivityDataMsg build() {
      xddq.pb.SpiritTrialActivityDataMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.SpiritTrialActivityDataMsg buildPartial() {
      xddq.pb.SpiritTrialActivityDataMsg result = new xddq.pb.SpiritTrialActivityDataMsg(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.SpiritTrialActivityDataMsg result) {
      if (evilThingDataMsgBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0)) {
          evilThingDataMsg_ = java.util.Collections.unmodifiableList(evilThingDataMsg_);
          bitField0_ = (bitField0_ & ~0x00000004);
        }
        result.evilThingDataMsg_ = evilThingDataMsg_;
      } else {
        result.evilThingDataMsg_ = evilThingDataMsgBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.SpiritTrialActivityDataMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.battleItemNum_ = battleItemNum_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.battleItemLastRecoveryTime_ = battleItemLastRecoveryTime_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        restSpiritId_.makeImmutable();
        result.restSpiritId_ = restSpiritId_;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.activityScore_ = activityScore_;
        to_bitField0_ |= 0x00000004;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.SpiritTrialActivityDataMsg) {
        return mergeFrom((xddq.pb.SpiritTrialActivityDataMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.SpiritTrialActivityDataMsg other) {
      if (other == xddq.pb.SpiritTrialActivityDataMsg.getDefaultInstance()) return this;
      if (other.hasBattleItemNum()) {
        setBattleItemNum(other.getBattleItemNum());
      }
      if (other.hasBattleItemLastRecoveryTime()) {
        setBattleItemLastRecoveryTime(other.getBattleItemLastRecoveryTime());
      }
      if (evilThingDataMsgBuilder_ == null) {
        if (!other.evilThingDataMsg_.isEmpty()) {
          if (evilThingDataMsg_.isEmpty()) {
            evilThingDataMsg_ = other.evilThingDataMsg_;
            bitField0_ = (bitField0_ & ~0x00000004);
          } else {
            ensureEvilThingDataMsgIsMutable();
            evilThingDataMsg_.addAll(other.evilThingDataMsg_);
          }
          onChanged();
        }
      } else {
        if (!other.evilThingDataMsg_.isEmpty()) {
          if (evilThingDataMsgBuilder_.isEmpty()) {
            evilThingDataMsgBuilder_.dispose();
            evilThingDataMsgBuilder_ = null;
            evilThingDataMsg_ = other.evilThingDataMsg_;
            bitField0_ = (bitField0_ & ~0x00000004);
            evilThingDataMsgBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetEvilThingDataMsgFieldBuilder() : null;
          } else {
            evilThingDataMsgBuilder_.addAllMessages(other.evilThingDataMsg_);
          }
        }
      }
      if (!other.restSpiritId_.isEmpty()) {
        if (restSpiritId_.isEmpty()) {
          restSpiritId_ = other.restSpiritId_;
          restSpiritId_.makeImmutable();
          bitField0_ |= 0x00000008;
        } else {
          ensureRestSpiritIdIsMutable();
          restSpiritId_.addAll(other.restSpiritId_);
        }
        onChanged();
      }
      if (other.hasActivityScore()) {
        setActivityScore(other.getActivityScore());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              battleItemNum_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              battleItemLastRecoveryTime_ = input.readInt64();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 26: {
              xddq.pb.EvilThingDataMsg m =
                  input.readMessage(
                      xddq.pb.EvilThingDataMsg.parser(),
                      extensionRegistry);
              if (evilThingDataMsgBuilder_ == null) {
                ensureEvilThingDataMsgIsMutable();
                evilThingDataMsg_.add(m);
              } else {
                evilThingDataMsgBuilder_.addMessage(m);
              }
              break;
            } // case 26
            case 32: {
              int v = input.readInt32();
              ensureRestSpiritIdIsMutable();
              restSpiritId_.addInt(v);
              break;
            } // case 32
            case 34: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              ensureRestSpiritIdIsMutable();
              while (input.getBytesUntilLimit() > 0) {
                restSpiritId_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            } // case 34
            case 40: {
              activityScore_ = input.readInt32();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int battleItemNum_ ;
    /**
     * <code>optional int32 battleItemNum = 1;</code>
     * @return Whether the battleItemNum field is set.
     */
    @java.lang.Override
    public boolean hasBattleItemNum() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 battleItemNum = 1;</code>
     * @return The battleItemNum.
     */
    @java.lang.Override
    public int getBattleItemNum() {
      return battleItemNum_;
    }
    /**
     * <code>optional int32 battleItemNum = 1;</code>
     * @param value The battleItemNum to set.
     * @return This builder for chaining.
     */
    public Builder setBattleItemNum(int value) {

      battleItemNum_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 battleItemNum = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearBattleItemNum() {
      bitField0_ = (bitField0_ & ~0x00000001);
      battleItemNum_ = 0;
      onChanged();
      return this;
    }

    private long battleItemLastRecoveryTime_ ;
    /**
     * <code>optional int64 battleItemLastRecoveryTime = 2;</code>
     * @return Whether the battleItemLastRecoveryTime field is set.
     */
    @java.lang.Override
    public boolean hasBattleItemLastRecoveryTime() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int64 battleItemLastRecoveryTime = 2;</code>
     * @return The battleItemLastRecoveryTime.
     */
    @java.lang.Override
    public long getBattleItemLastRecoveryTime() {
      return battleItemLastRecoveryTime_;
    }
    /**
     * <code>optional int64 battleItemLastRecoveryTime = 2;</code>
     * @param value The battleItemLastRecoveryTime to set.
     * @return This builder for chaining.
     */
    public Builder setBattleItemLastRecoveryTime(long value) {

      battleItemLastRecoveryTime_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 battleItemLastRecoveryTime = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearBattleItemLastRecoveryTime() {
      bitField0_ = (bitField0_ & ~0x00000002);
      battleItemLastRecoveryTime_ = 0L;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.EvilThingDataMsg> evilThingDataMsg_ =
      java.util.Collections.emptyList();
    private void ensureEvilThingDataMsgIsMutable() {
      if (!((bitField0_ & 0x00000004) != 0)) {
        evilThingDataMsg_ = new java.util.ArrayList<xddq.pb.EvilThingDataMsg>(evilThingDataMsg_);
        bitField0_ |= 0x00000004;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.EvilThingDataMsg, xddq.pb.EvilThingDataMsg.Builder, xddq.pb.EvilThingDataMsgOrBuilder> evilThingDataMsgBuilder_;

    /**
     * <code>repeated .xddq.pb.EvilThingDataMsg evilThingDataMsg = 3;</code>
     */
    public java.util.List<xddq.pb.EvilThingDataMsg> getEvilThingDataMsgList() {
      if (evilThingDataMsgBuilder_ == null) {
        return java.util.Collections.unmodifiableList(evilThingDataMsg_);
      } else {
        return evilThingDataMsgBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.EvilThingDataMsg evilThingDataMsg = 3;</code>
     */
    public int getEvilThingDataMsgCount() {
      if (evilThingDataMsgBuilder_ == null) {
        return evilThingDataMsg_.size();
      } else {
        return evilThingDataMsgBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.EvilThingDataMsg evilThingDataMsg = 3;</code>
     */
    public xddq.pb.EvilThingDataMsg getEvilThingDataMsg(int index) {
      if (evilThingDataMsgBuilder_ == null) {
        return evilThingDataMsg_.get(index);
      } else {
        return evilThingDataMsgBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.EvilThingDataMsg evilThingDataMsg = 3;</code>
     */
    public Builder setEvilThingDataMsg(
        int index, xddq.pb.EvilThingDataMsg value) {
      if (evilThingDataMsgBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureEvilThingDataMsgIsMutable();
        evilThingDataMsg_.set(index, value);
        onChanged();
      } else {
        evilThingDataMsgBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.EvilThingDataMsg evilThingDataMsg = 3;</code>
     */
    public Builder setEvilThingDataMsg(
        int index, xddq.pb.EvilThingDataMsg.Builder builderForValue) {
      if (evilThingDataMsgBuilder_ == null) {
        ensureEvilThingDataMsgIsMutable();
        evilThingDataMsg_.set(index, builderForValue.build());
        onChanged();
      } else {
        evilThingDataMsgBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.EvilThingDataMsg evilThingDataMsg = 3;</code>
     */
    public Builder addEvilThingDataMsg(xddq.pb.EvilThingDataMsg value) {
      if (evilThingDataMsgBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureEvilThingDataMsgIsMutable();
        evilThingDataMsg_.add(value);
        onChanged();
      } else {
        evilThingDataMsgBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.EvilThingDataMsg evilThingDataMsg = 3;</code>
     */
    public Builder addEvilThingDataMsg(
        int index, xddq.pb.EvilThingDataMsg value) {
      if (evilThingDataMsgBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureEvilThingDataMsgIsMutable();
        evilThingDataMsg_.add(index, value);
        onChanged();
      } else {
        evilThingDataMsgBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.EvilThingDataMsg evilThingDataMsg = 3;</code>
     */
    public Builder addEvilThingDataMsg(
        xddq.pb.EvilThingDataMsg.Builder builderForValue) {
      if (evilThingDataMsgBuilder_ == null) {
        ensureEvilThingDataMsgIsMutable();
        evilThingDataMsg_.add(builderForValue.build());
        onChanged();
      } else {
        evilThingDataMsgBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.EvilThingDataMsg evilThingDataMsg = 3;</code>
     */
    public Builder addEvilThingDataMsg(
        int index, xddq.pb.EvilThingDataMsg.Builder builderForValue) {
      if (evilThingDataMsgBuilder_ == null) {
        ensureEvilThingDataMsgIsMutable();
        evilThingDataMsg_.add(index, builderForValue.build());
        onChanged();
      } else {
        evilThingDataMsgBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.EvilThingDataMsg evilThingDataMsg = 3;</code>
     */
    public Builder addAllEvilThingDataMsg(
        java.lang.Iterable<? extends xddq.pb.EvilThingDataMsg> values) {
      if (evilThingDataMsgBuilder_ == null) {
        ensureEvilThingDataMsgIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, evilThingDataMsg_);
        onChanged();
      } else {
        evilThingDataMsgBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.EvilThingDataMsg evilThingDataMsg = 3;</code>
     */
    public Builder clearEvilThingDataMsg() {
      if (evilThingDataMsgBuilder_ == null) {
        evilThingDataMsg_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
      } else {
        evilThingDataMsgBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.EvilThingDataMsg evilThingDataMsg = 3;</code>
     */
    public Builder removeEvilThingDataMsg(int index) {
      if (evilThingDataMsgBuilder_ == null) {
        ensureEvilThingDataMsgIsMutable();
        evilThingDataMsg_.remove(index);
        onChanged();
      } else {
        evilThingDataMsgBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.EvilThingDataMsg evilThingDataMsg = 3;</code>
     */
    public xddq.pb.EvilThingDataMsg.Builder getEvilThingDataMsgBuilder(
        int index) {
      return internalGetEvilThingDataMsgFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.EvilThingDataMsg evilThingDataMsg = 3;</code>
     */
    public xddq.pb.EvilThingDataMsgOrBuilder getEvilThingDataMsgOrBuilder(
        int index) {
      if (evilThingDataMsgBuilder_ == null) {
        return evilThingDataMsg_.get(index);  } else {
        return evilThingDataMsgBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.EvilThingDataMsg evilThingDataMsg = 3;</code>
     */
    public java.util.List<? extends xddq.pb.EvilThingDataMsgOrBuilder> 
         getEvilThingDataMsgOrBuilderList() {
      if (evilThingDataMsgBuilder_ != null) {
        return evilThingDataMsgBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(evilThingDataMsg_);
      }
    }
    /**
     * <code>repeated .xddq.pb.EvilThingDataMsg evilThingDataMsg = 3;</code>
     */
    public xddq.pb.EvilThingDataMsg.Builder addEvilThingDataMsgBuilder() {
      return internalGetEvilThingDataMsgFieldBuilder().addBuilder(
          xddq.pb.EvilThingDataMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.EvilThingDataMsg evilThingDataMsg = 3;</code>
     */
    public xddq.pb.EvilThingDataMsg.Builder addEvilThingDataMsgBuilder(
        int index) {
      return internalGetEvilThingDataMsgFieldBuilder().addBuilder(
          index, xddq.pb.EvilThingDataMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.EvilThingDataMsg evilThingDataMsg = 3;</code>
     */
    public java.util.List<xddq.pb.EvilThingDataMsg.Builder> 
         getEvilThingDataMsgBuilderList() {
      return internalGetEvilThingDataMsgFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.EvilThingDataMsg, xddq.pb.EvilThingDataMsg.Builder, xddq.pb.EvilThingDataMsgOrBuilder> 
        internalGetEvilThingDataMsgFieldBuilder() {
      if (evilThingDataMsgBuilder_ == null) {
        evilThingDataMsgBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.EvilThingDataMsg, xddq.pb.EvilThingDataMsg.Builder, xddq.pb.EvilThingDataMsgOrBuilder>(
                evilThingDataMsg_,
                ((bitField0_ & 0x00000004) != 0),
                getParentForChildren(),
                isClean());
        evilThingDataMsg_ = null;
      }
      return evilThingDataMsgBuilder_;
    }

    private com.google.protobuf.Internal.IntList restSpiritId_ = emptyIntList();
    private void ensureRestSpiritIdIsMutable() {
      if (!restSpiritId_.isModifiable()) {
        restSpiritId_ = makeMutableCopy(restSpiritId_);
      }
      bitField0_ |= 0x00000008;
    }
    /**
     * <code>repeated int32 restSpiritId = 4;</code>
     * @return A list containing the restSpiritId.
     */
    public java.util.List<java.lang.Integer>
        getRestSpiritIdList() {
      restSpiritId_.makeImmutable();
      return restSpiritId_;
    }
    /**
     * <code>repeated int32 restSpiritId = 4;</code>
     * @return The count of restSpiritId.
     */
    public int getRestSpiritIdCount() {
      return restSpiritId_.size();
    }
    /**
     * <code>repeated int32 restSpiritId = 4;</code>
     * @param index The index of the element to return.
     * @return The restSpiritId at the given index.
     */
    public int getRestSpiritId(int index) {
      return restSpiritId_.getInt(index);
    }
    /**
     * <code>repeated int32 restSpiritId = 4;</code>
     * @param index The index to set the value at.
     * @param value The restSpiritId to set.
     * @return This builder for chaining.
     */
    public Builder setRestSpiritId(
        int index, int value) {

      ensureRestSpiritIdIsMutable();
      restSpiritId_.setInt(index, value);
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 restSpiritId = 4;</code>
     * @param value The restSpiritId to add.
     * @return This builder for chaining.
     */
    public Builder addRestSpiritId(int value) {

      ensureRestSpiritIdIsMutable();
      restSpiritId_.addInt(value);
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 restSpiritId = 4;</code>
     * @param values The restSpiritId to add.
     * @return This builder for chaining.
     */
    public Builder addAllRestSpiritId(
        java.lang.Iterable<? extends java.lang.Integer> values) {
      ensureRestSpiritIdIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, restSpiritId_);
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 restSpiritId = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearRestSpiritId() {
      restSpiritId_ = emptyIntList();
      bitField0_ = (bitField0_ & ~0x00000008);
      onChanged();
      return this;
    }

    private int activityScore_ ;
    /**
     * <code>optional int32 activityScore = 5;</code>
     * @return Whether the activityScore field is set.
     */
    @java.lang.Override
    public boolean hasActivityScore() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional int32 activityScore = 5;</code>
     * @return The activityScore.
     */
    @java.lang.Override
    public int getActivityScore() {
      return activityScore_;
    }
    /**
     * <code>optional int32 activityScore = 5;</code>
     * @param value The activityScore to set.
     * @return This builder for chaining.
     */
    public Builder setActivityScore(int value) {

      activityScore_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 activityScore = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearActivityScore() {
      bitField0_ = (bitField0_ & ~0x00000010);
      activityScore_ = 0;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.SpiritTrialActivityDataMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.SpiritTrialActivityDataMsg)
  private static final xddq.pb.SpiritTrialActivityDataMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.SpiritTrialActivityDataMsg();
  }

  public static xddq.pb.SpiritTrialActivityDataMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<SpiritTrialActivityDataMsg>
      PARSER = new com.google.protobuf.AbstractParser<SpiritTrialActivityDataMsg>() {
    @java.lang.Override
    public SpiritTrialActivityDataMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<SpiritTrialActivityDataMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<SpiritTrialActivityDataMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.SpiritTrialActivityDataMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

