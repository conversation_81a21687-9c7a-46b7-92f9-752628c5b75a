// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.RebornTrialUnionScoreResp}
 */
public final class RebornTrialUnionScoreResp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.RebornTrialUnionScoreResp)
    RebornTrialUnionScoreRespOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      RebornTrialUnionScoreResp.class.getName());
  }
  // Use RebornTrialUnionScoreResp.newBuilder() to construct.
  private RebornTrialUnionScoreResp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private RebornTrialUnionScoreResp() {
    scoreInfo_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_RebornTrialUnionScoreResp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_RebornTrialUnionScoreResp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.RebornTrialUnionScoreResp.class, xddq.pb.RebornTrialUnionScoreResp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int SCOREINFO_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.RebornTrialPlayerScoreInfo> scoreInfo_;
  /**
   * <code>repeated .xddq.pb.RebornTrialPlayerScoreInfo scoreInfo = 2;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.RebornTrialPlayerScoreInfo> getScoreInfoList() {
    return scoreInfo_;
  }
  /**
   * <code>repeated .xddq.pb.RebornTrialPlayerScoreInfo scoreInfo = 2;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.RebornTrialPlayerScoreInfoOrBuilder> 
      getScoreInfoOrBuilderList() {
    return scoreInfo_;
  }
  /**
   * <code>repeated .xddq.pb.RebornTrialPlayerScoreInfo scoreInfo = 2;</code>
   */
  @java.lang.Override
  public int getScoreInfoCount() {
    return scoreInfo_.size();
  }
  /**
   * <code>repeated .xddq.pb.RebornTrialPlayerScoreInfo scoreInfo = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.RebornTrialPlayerScoreInfo getScoreInfo(int index) {
    return scoreInfo_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.RebornTrialPlayerScoreInfo scoreInfo = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.RebornTrialPlayerScoreInfoOrBuilder getScoreInfoOrBuilder(
      int index) {
    return scoreInfo_.get(index);
  }

  public static final int REDUCESCORE_FIELD_NUMBER = 3;
  private long reduceScore_ = 0L;
  /**
   * <code>optional int64 reduceScore = 3;</code>
   * @return Whether the reduceScore field is set.
   */
  @java.lang.Override
  public boolean hasReduceScore() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int64 reduceScore = 3;</code>
   * @return The reduceScore.
   */
  @java.lang.Override
  public long getReduceScore() {
    return reduceScore_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    for (int i = 0; i < getScoreInfoCount(); i++) {
      if (!getScoreInfo(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    for (int i = 0; i < scoreInfo_.size(); i++) {
      output.writeMessage(2, scoreInfo_.get(i));
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt64(3, reduceScore_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    for (int i = 0; i < scoreInfo_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, scoreInfo_.get(i));
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(3, reduceScore_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.RebornTrialUnionScoreResp)) {
      return super.equals(obj);
    }
    xddq.pb.RebornTrialUnionScoreResp other = (xddq.pb.RebornTrialUnionScoreResp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (!getScoreInfoList()
        .equals(other.getScoreInfoList())) return false;
    if (hasReduceScore() != other.hasReduceScore()) return false;
    if (hasReduceScore()) {
      if (getReduceScore()
          != other.getReduceScore()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (getScoreInfoCount() > 0) {
      hash = (37 * hash) + SCOREINFO_FIELD_NUMBER;
      hash = (53 * hash) + getScoreInfoList().hashCode();
    }
    if (hasReduceScore()) {
      hash = (37 * hash) + REDUCESCORE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getReduceScore());
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.RebornTrialUnionScoreResp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.RebornTrialUnionScoreResp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.RebornTrialUnionScoreResp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.RebornTrialUnionScoreResp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.RebornTrialUnionScoreResp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.RebornTrialUnionScoreResp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.RebornTrialUnionScoreResp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.RebornTrialUnionScoreResp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.RebornTrialUnionScoreResp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.RebornTrialUnionScoreResp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.RebornTrialUnionScoreResp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.RebornTrialUnionScoreResp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.RebornTrialUnionScoreResp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.RebornTrialUnionScoreResp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.RebornTrialUnionScoreResp)
      xddq.pb.RebornTrialUnionScoreRespOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_RebornTrialUnionScoreResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_RebornTrialUnionScoreResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.RebornTrialUnionScoreResp.class, xddq.pb.RebornTrialUnionScoreResp.Builder.class);
    }

    // Construct using xddq.pb.RebornTrialUnionScoreResp.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      if (scoreInfoBuilder_ == null) {
        scoreInfo_ = java.util.Collections.emptyList();
      } else {
        scoreInfo_ = null;
        scoreInfoBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000002);
      reduceScore_ = 0L;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_RebornTrialUnionScoreResp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.RebornTrialUnionScoreResp getDefaultInstanceForType() {
      return xddq.pb.RebornTrialUnionScoreResp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.RebornTrialUnionScoreResp build() {
      xddq.pb.RebornTrialUnionScoreResp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.RebornTrialUnionScoreResp buildPartial() {
      xddq.pb.RebornTrialUnionScoreResp result = new xddq.pb.RebornTrialUnionScoreResp(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.RebornTrialUnionScoreResp result) {
      if (scoreInfoBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          scoreInfo_ = java.util.Collections.unmodifiableList(scoreInfo_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.scoreInfo_ = scoreInfo_;
      } else {
        result.scoreInfo_ = scoreInfoBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.RebornTrialUnionScoreResp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.reduceScore_ = reduceScore_;
        to_bitField0_ |= 0x00000002;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.RebornTrialUnionScoreResp) {
        return mergeFrom((xddq.pb.RebornTrialUnionScoreResp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.RebornTrialUnionScoreResp other) {
      if (other == xddq.pb.RebornTrialUnionScoreResp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (scoreInfoBuilder_ == null) {
        if (!other.scoreInfo_.isEmpty()) {
          if (scoreInfo_.isEmpty()) {
            scoreInfo_ = other.scoreInfo_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureScoreInfoIsMutable();
            scoreInfo_.addAll(other.scoreInfo_);
          }
          onChanged();
        }
      } else {
        if (!other.scoreInfo_.isEmpty()) {
          if (scoreInfoBuilder_.isEmpty()) {
            scoreInfoBuilder_.dispose();
            scoreInfoBuilder_ = null;
            scoreInfo_ = other.scoreInfo_;
            bitField0_ = (bitField0_ & ~0x00000002);
            scoreInfoBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetScoreInfoFieldBuilder() : null;
          } else {
            scoreInfoBuilder_.addAllMessages(other.scoreInfo_);
          }
        }
      }
      if (other.hasReduceScore()) {
        setReduceScore(other.getReduceScore());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      for (int i = 0; i < getScoreInfoCount(); i++) {
        if (!getScoreInfo(i).isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              xddq.pb.RebornTrialPlayerScoreInfo m =
                  input.readMessage(
                      xddq.pb.RebornTrialPlayerScoreInfo.parser(),
                      extensionRegistry);
              if (scoreInfoBuilder_ == null) {
                ensureScoreInfoIsMutable();
                scoreInfo_.add(m);
              } else {
                scoreInfoBuilder_.addMessage(m);
              }
              break;
            } // case 18
            case 24: {
              reduceScore_ = input.readInt64();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.RebornTrialPlayerScoreInfo> scoreInfo_ =
      java.util.Collections.emptyList();
    private void ensureScoreInfoIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        scoreInfo_ = new java.util.ArrayList<xddq.pb.RebornTrialPlayerScoreInfo>(scoreInfo_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.RebornTrialPlayerScoreInfo, xddq.pb.RebornTrialPlayerScoreInfo.Builder, xddq.pb.RebornTrialPlayerScoreInfoOrBuilder> scoreInfoBuilder_;

    /**
     * <code>repeated .xddq.pb.RebornTrialPlayerScoreInfo scoreInfo = 2;</code>
     */
    public java.util.List<xddq.pb.RebornTrialPlayerScoreInfo> getScoreInfoList() {
      if (scoreInfoBuilder_ == null) {
        return java.util.Collections.unmodifiableList(scoreInfo_);
      } else {
        return scoreInfoBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.RebornTrialPlayerScoreInfo scoreInfo = 2;</code>
     */
    public int getScoreInfoCount() {
      if (scoreInfoBuilder_ == null) {
        return scoreInfo_.size();
      } else {
        return scoreInfoBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.RebornTrialPlayerScoreInfo scoreInfo = 2;</code>
     */
    public xddq.pb.RebornTrialPlayerScoreInfo getScoreInfo(int index) {
      if (scoreInfoBuilder_ == null) {
        return scoreInfo_.get(index);
      } else {
        return scoreInfoBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.RebornTrialPlayerScoreInfo scoreInfo = 2;</code>
     */
    public Builder setScoreInfo(
        int index, xddq.pb.RebornTrialPlayerScoreInfo value) {
      if (scoreInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureScoreInfoIsMutable();
        scoreInfo_.set(index, value);
        onChanged();
      } else {
        scoreInfoBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.RebornTrialPlayerScoreInfo scoreInfo = 2;</code>
     */
    public Builder setScoreInfo(
        int index, xddq.pb.RebornTrialPlayerScoreInfo.Builder builderForValue) {
      if (scoreInfoBuilder_ == null) {
        ensureScoreInfoIsMutable();
        scoreInfo_.set(index, builderForValue.build());
        onChanged();
      } else {
        scoreInfoBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.RebornTrialPlayerScoreInfo scoreInfo = 2;</code>
     */
    public Builder addScoreInfo(xddq.pb.RebornTrialPlayerScoreInfo value) {
      if (scoreInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureScoreInfoIsMutable();
        scoreInfo_.add(value);
        onChanged();
      } else {
        scoreInfoBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.RebornTrialPlayerScoreInfo scoreInfo = 2;</code>
     */
    public Builder addScoreInfo(
        int index, xddq.pb.RebornTrialPlayerScoreInfo value) {
      if (scoreInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureScoreInfoIsMutable();
        scoreInfo_.add(index, value);
        onChanged();
      } else {
        scoreInfoBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.RebornTrialPlayerScoreInfo scoreInfo = 2;</code>
     */
    public Builder addScoreInfo(
        xddq.pb.RebornTrialPlayerScoreInfo.Builder builderForValue) {
      if (scoreInfoBuilder_ == null) {
        ensureScoreInfoIsMutable();
        scoreInfo_.add(builderForValue.build());
        onChanged();
      } else {
        scoreInfoBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.RebornTrialPlayerScoreInfo scoreInfo = 2;</code>
     */
    public Builder addScoreInfo(
        int index, xddq.pb.RebornTrialPlayerScoreInfo.Builder builderForValue) {
      if (scoreInfoBuilder_ == null) {
        ensureScoreInfoIsMutable();
        scoreInfo_.add(index, builderForValue.build());
        onChanged();
      } else {
        scoreInfoBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.RebornTrialPlayerScoreInfo scoreInfo = 2;</code>
     */
    public Builder addAllScoreInfo(
        java.lang.Iterable<? extends xddq.pb.RebornTrialPlayerScoreInfo> values) {
      if (scoreInfoBuilder_ == null) {
        ensureScoreInfoIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, scoreInfo_);
        onChanged();
      } else {
        scoreInfoBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.RebornTrialPlayerScoreInfo scoreInfo = 2;</code>
     */
    public Builder clearScoreInfo() {
      if (scoreInfoBuilder_ == null) {
        scoreInfo_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        scoreInfoBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.RebornTrialPlayerScoreInfo scoreInfo = 2;</code>
     */
    public Builder removeScoreInfo(int index) {
      if (scoreInfoBuilder_ == null) {
        ensureScoreInfoIsMutable();
        scoreInfo_.remove(index);
        onChanged();
      } else {
        scoreInfoBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.RebornTrialPlayerScoreInfo scoreInfo = 2;</code>
     */
    public xddq.pb.RebornTrialPlayerScoreInfo.Builder getScoreInfoBuilder(
        int index) {
      return internalGetScoreInfoFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.RebornTrialPlayerScoreInfo scoreInfo = 2;</code>
     */
    public xddq.pb.RebornTrialPlayerScoreInfoOrBuilder getScoreInfoOrBuilder(
        int index) {
      if (scoreInfoBuilder_ == null) {
        return scoreInfo_.get(index);  } else {
        return scoreInfoBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.RebornTrialPlayerScoreInfo scoreInfo = 2;</code>
     */
    public java.util.List<? extends xddq.pb.RebornTrialPlayerScoreInfoOrBuilder> 
         getScoreInfoOrBuilderList() {
      if (scoreInfoBuilder_ != null) {
        return scoreInfoBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(scoreInfo_);
      }
    }
    /**
     * <code>repeated .xddq.pb.RebornTrialPlayerScoreInfo scoreInfo = 2;</code>
     */
    public xddq.pb.RebornTrialPlayerScoreInfo.Builder addScoreInfoBuilder() {
      return internalGetScoreInfoFieldBuilder().addBuilder(
          xddq.pb.RebornTrialPlayerScoreInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.RebornTrialPlayerScoreInfo scoreInfo = 2;</code>
     */
    public xddq.pb.RebornTrialPlayerScoreInfo.Builder addScoreInfoBuilder(
        int index) {
      return internalGetScoreInfoFieldBuilder().addBuilder(
          index, xddq.pb.RebornTrialPlayerScoreInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.RebornTrialPlayerScoreInfo scoreInfo = 2;</code>
     */
    public java.util.List<xddq.pb.RebornTrialPlayerScoreInfo.Builder> 
         getScoreInfoBuilderList() {
      return internalGetScoreInfoFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.RebornTrialPlayerScoreInfo, xddq.pb.RebornTrialPlayerScoreInfo.Builder, xddq.pb.RebornTrialPlayerScoreInfoOrBuilder> 
        internalGetScoreInfoFieldBuilder() {
      if (scoreInfoBuilder_ == null) {
        scoreInfoBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.RebornTrialPlayerScoreInfo, xddq.pb.RebornTrialPlayerScoreInfo.Builder, xddq.pb.RebornTrialPlayerScoreInfoOrBuilder>(
                scoreInfo_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        scoreInfo_ = null;
      }
      return scoreInfoBuilder_;
    }

    private long reduceScore_ ;
    /**
     * <code>optional int64 reduceScore = 3;</code>
     * @return Whether the reduceScore field is set.
     */
    @java.lang.Override
    public boolean hasReduceScore() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int64 reduceScore = 3;</code>
     * @return The reduceScore.
     */
    @java.lang.Override
    public long getReduceScore() {
      return reduceScore_;
    }
    /**
     * <code>optional int64 reduceScore = 3;</code>
     * @param value The reduceScore to set.
     * @return This builder for chaining.
     */
    public Builder setReduceScore(long value) {

      reduceScore_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 reduceScore = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearReduceScore() {
      bitField0_ = (bitField0_ & ~0x00000004);
      reduceScore_ = 0L;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.RebornTrialUnionScoreResp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.RebornTrialUnionScoreResp)
  private static final xddq.pb.RebornTrialUnionScoreResp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.RebornTrialUnionScoreResp();
  }

  public static xddq.pb.RebornTrialUnionScoreResp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<RebornTrialUnionScoreResp>
      PARSER = new com.google.protobuf.AbstractParser<RebornTrialUnionScoreResp>() {
    @java.lang.Override
    public RebornTrialUnionScoreResp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<RebornTrialUnionScoreResp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<RebornTrialUnionScoreResp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.RebornTrialUnionScoreResp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

