// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.ExploreChooseSkillReq}
 */
public final class ExploreChooseSkillReq extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.ExploreChooseSkillReq)
    ExploreChooseSkillReqOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      ExploreChooseSkillReq.class.getName());
  }
  // Use ExploreChooseSkillReq.newBuilder() to construct.
  private ExploreChooseSkillReq(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private ExploreChooseSkillReq() {
    selectIndex_ = emptyIntList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_ExploreChooseSkillReq_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_ExploreChooseSkillReq_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.ExploreChooseSkillReq.class, xddq.pb.ExploreChooseSkillReq.Builder.class);
  }

  private int bitField0_;
  public static final int ACTIVITYID_FIELD_NUMBER = 1;
  private int activityId_ = 0;
  /**
   * <code>required int32 activityId = 1;</code>
   * @return Whether the activityId field is set.
   */
  @java.lang.Override
  public boolean hasActivityId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 activityId = 1;</code>
   * @return The activityId.
   */
  @java.lang.Override
  public int getActivityId() {
    return activityId_;
  }

  public static final int SELECTINDEX_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private com.google.protobuf.Internal.IntList selectIndex_ =
      emptyIntList();
  /**
   * <code>repeated int32 selectIndex = 2;</code>
   * @return A list containing the selectIndex.
   */
  @java.lang.Override
  public java.util.List<java.lang.Integer>
      getSelectIndexList() {
    return selectIndex_;
  }
  /**
   * <code>repeated int32 selectIndex = 2;</code>
   * @return The count of selectIndex.
   */
  public int getSelectIndexCount() {
    return selectIndex_.size();
  }
  /**
   * <code>repeated int32 selectIndex = 2;</code>
   * @param index The index of the element to return.
   * @return The selectIndex at the given index.
   */
  public int getSelectIndex(int index) {
    return selectIndex_.getInt(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasActivityId()) {
      memoizedIsInitialized = 0;
      return false;
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, activityId_);
    }
    for (int i = 0; i < selectIndex_.size(); i++) {
      output.writeInt32(2, selectIndex_.getInt(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, activityId_);
    }
    {
      int dataSize = 0;
      for (int i = 0; i < selectIndex_.size(); i++) {
        dataSize += com.google.protobuf.CodedOutputStream
          .computeInt32SizeNoTag(selectIndex_.getInt(i));
      }
      size += dataSize;
      size += 1 * getSelectIndexList().size();
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.ExploreChooseSkillReq)) {
      return super.equals(obj);
    }
    xddq.pb.ExploreChooseSkillReq other = (xddq.pb.ExploreChooseSkillReq) obj;

    if (hasActivityId() != other.hasActivityId()) return false;
    if (hasActivityId()) {
      if (getActivityId()
          != other.getActivityId()) return false;
    }
    if (!getSelectIndexList()
        .equals(other.getSelectIndexList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasActivityId()) {
      hash = (37 * hash) + ACTIVITYID_FIELD_NUMBER;
      hash = (53 * hash) + getActivityId();
    }
    if (getSelectIndexCount() > 0) {
      hash = (37 * hash) + SELECTINDEX_FIELD_NUMBER;
      hash = (53 * hash) + getSelectIndexList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.ExploreChooseSkillReq parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ExploreChooseSkillReq parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ExploreChooseSkillReq parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ExploreChooseSkillReq parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ExploreChooseSkillReq parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ExploreChooseSkillReq parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ExploreChooseSkillReq parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.ExploreChooseSkillReq parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.ExploreChooseSkillReq parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.ExploreChooseSkillReq parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.ExploreChooseSkillReq parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.ExploreChooseSkillReq parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.ExploreChooseSkillReq prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.ExploreChooseSkillReq}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.ExploreChooseSkillReq)
      xddq.pb.ExploreChooseSkillReqOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ExploreChooseSkillReq_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ExploreChooseSkillReq_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.ExploreChooseSkillReq.class, xddq.pb.ExploreChooseSkillReq.Builder.class);
    }

    // Construct using xddq.pb.ExploreChooseSkillReq.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      activityId_ = 0;
      selectIndex_ = emptyIntList();
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ExploreChooseSkillReq_descriptor;
    }

    @java.lang.Override
    public xddq.pb.ExploreChooseSkillReq getDefaultInstanceForType() {
      return xddq.pb.ExploreChooseSkillReq.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.ExploreChooseSkillReq build() {
      xddq.pb.ExploreChooseSkillReq result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.ExploreChooseSkillReq buildPartial() {
      xddq.pb.ExploreChooseSkillReq result = new xddq.pb.ExploreChooseSkillReq(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.ExploreChooseSkillReq result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.activityId_ = activityId_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        selectIndex_.makeImmutable();
        result.selectIndex_ = selectIndex_;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.ExploreChooseSkillReq) {
        return mergeFrom((xddq.pb.ExploreChooseSkillReq)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.ExploreChooseSkillReq other) {
      if (other == xddq.pb.ExploreChooseSkillReq.getDefaultInstance()) return this;
      if (other.hasActivityId()) {
        setActivityId(other.getActivityId());
      }
      if (!other.selectIndex_.isEmpty()) {
        if (selectIndex_.isEmpty()) {
          selectIndex_ = other.selectIndex_;
          selectIndex_.makeImmutable();
          bitField0_ |= 0x00000002;
        } else {
          ensureSelectIndexIsMutable();
          selectIndex_.addAll(other.selectIndex_);
        }
        onChanged();
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasActivityId()) {
        return false;
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              activityId_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              int v = input.readInt32();
              ensureSelectIndexIsMutable();
              selectIndex_.addInt(v);
              break;
            } // case 16
            case 18: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              ensureSelectIndexIsMutable();
              while (input.getBytesUntilLimit() > 0) {
                selectIndex_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            } // case 18
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int activityId_ ;
    /**
     * <code>required int32 activityId = 1;</code>
     * @return Whether the activityId field is set.
     */
    @java.lang.Override
    public boolean hasActivityId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 activityId = 1;</code>
     * @return The activityId.
     */
    @java.lang.Override
    public int getActivityId() {
      return activityId_;
    }
    /**
     * <code>required int32 activityId = 1;</code>
     * @param value The activityId to set.
     * @return This builder for chaining.
     */
    public Builder setActivityId(int value) {

      activityId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 activityId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearActivityId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      activityId_ = 0;
      onChanged();
      return this;
    }

    private com.google.protobuf.Internal.IntList selectIndex_ = emptyIntList();
    private void ensureSelectIndexIsMutable() {
      if (!selectIndex_.isModifiable()) {
        selectIndex_ = makeMutableCopy(selectIndex_);
      }
      bitField0_ |= 0x00000002;
    }
    /**
     * <code>repeated int32 selectIndex = 2;</code>
     * @return A list containing the selectIndex.
     */
    public java.util.List<java.lang.Integer>
        getSelectIndexList() {
      selectIndex_.makeImmutable();
      return selectIndex_;
    }
    /**
     * <code>repeated int32 selectIndex = 2;</code>
     * @return The count of selectIndex.
     */
    public int getSelectIndexCount() {
      return selectIndex_.size();
    }
    /**
     * <code>repeated int32 selectIndex = 2;</code>
     * @param index The index of the element to return.
     * @return The selectIndex at the given index.
     */
    public int getSelectIndex(int index) {
      return selectIndex_.getInt(index);
    }
    /**
     * <code>repeated int32 selectIndex = 2;</code>
     * @param index The index to set the value at.
     * @param value The selectIndex to set.
     * @return This builder for chaining.
     */
    public Builder setSelectIndex(
        int index, int value) {

      ensureSelectIndexIsMutable();
      selectIndex_.setInt(index, value);
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 selectIndex = 2;</code>
     * @param value The selectIndex to add.
     * @return This builder for chaining.
     */
    public Builder addSelectIndex(int value) {

      ensureSelectIndexIsMutable();
      selectIndex_.addInt(value);
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 selectIndex = 2;</code>
     * @param values The selectIndex to add.
     * @return This builder for chaining.
     */
    public Builder addAllSelectIndex(
        java.lang.Iterable<? extends java.lang.Integer> values) {
      ensureSelectIndexIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, selectIndex_);
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 selectIndex = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearSelectIndex() {
      selectIndex_ = emptyIntList();
      bitField0_ = (bitField0_ & ~0x00000002);
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.ExploreChooseSkillReq)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.ExploreChooseSkillReq)
  private static final xddq.pb.ExploreChooseSkillReq DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.ExploreChooseSkillReq();
  }

  public static xddq.pb.ExploreChooseSkillReq getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ExploreChooseSkillReq>
      PARSER = new com.google.protobuf.AbstractParser<ExploreChooseSkillReq>() {
    @java.lang.Override
    public ExploreChooseSkillReq parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<ExploreChooseSkillReq> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ExploreChooseSkillReq> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.ExploreChooseSkillReq getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

