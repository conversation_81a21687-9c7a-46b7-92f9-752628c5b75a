// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

public interface UnionAssembleUseItemReqOrBuilder extends
    // @@protoc_insertion_point(interface_extends:xddq.pb.UnionAssembleUseItemReq)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>optional int32 activityId = 1;</code>
   * @return Whether the activityId field is set.
   */
  boolean hasActivityId();
  /**
   * <code>optional int32 activityId = 1;</code>
   * @return The activityId.
   */
  int getActivityId();

  /**
   * <code>optional int32 useNum = 2;</code>
   * @return Whether the useNum field is set.
   */
  boolean hasUseNum();
  /**
   * <code>optional int32 useNum = 2;</code>
   * @return The useNum.
   */
  int getUseNum();
}
