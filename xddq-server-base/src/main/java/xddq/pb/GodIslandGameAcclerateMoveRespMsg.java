// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.GodIslandGameAcclerateMoveRespMsg}
 */
public final class GodIslandGameAcclerateMoveRespMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.GodIslandGameAcclerateMoveRespMsg)
    GodIslandGameAcclerateMoveRespMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      GodIslandGameAcclerateMoveRespMsg.class.getName());
  }
  // Use GodIslandGameAcclerateMoveRespMsg.newBuilder() to construct.
  private GodIslandGameAcclerateMoveRespMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private GodIslandGameAcclerateMoveRespMsg() {
    costInfo_ = "";
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_GodIslandGameAcclerateMoveRespMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_GodIslandGameAcclerateMoveRespMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.GodIslandGameAcclerateMoveRespMsg.class, xddq.pb.GodIslandGameAcclerateMoveRespMsg.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int COSTINFO_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private volatile java.lang.Object costInfo_ = "";
  /**
   * <code>optional string costInfo = 2;</code>
   * @return Whether the costInfo field is set.
   */
  @java.lang.Override
  public boolean hasCostInfo() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional string costInfo = 2;</code>
   * @return The costInfo.
   */
  @java.lang.Override
  public java.lang.String getCostInfo() {
    java.lang.Object ref = costInfo_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        costInfo_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string costInfo = 2;</code>
   * @return The bytes for costInfo.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getCostInfoBytes() {
    java.lang.Object ref = costInfo_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      costInfo_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int SPEEDUPCOUNT_FIELD_NUMBER = 3;
  private int speedUpCount_ = 0;
  /**
   * <code>optional int32 speedUpCount = 3;</code>
   * @return Whether the speedUpCount field is set.
   */
  @java.lang.Override
  public boolean hasSpeedUpCount() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional int32 speedUpCount = 3;</code>
   * @return The speedUpCount.
   */
  @java.lang.Override
  public int getSpeedUpCount() {
    return speedUpCount_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 2, costInfo_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt32(3, speedUpCount_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(2, costInfo_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(3, speedUpCount_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.GodIslandGameAcclerateMoveRespMsg)) {
      return super.equals(obj);
    }
    xddq.pb.GodIslandGameAcclerateMoveRespMsg other = (xddq.pb.GodIslandGameAcclerateMoveRespMsg) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (hasCostInfo() != other.hasCostInfo()) return false;
    if (hasCostInfo()) {
      if (!getCostInfo()
          .equals(other.getCostInfo())) return false;
    }
    if (hasSpeedUpCount() != other.hasSpeedUpCount()) return false;
    if (hasSpeedUpCount()) {
      if (getSpeedUpCount()
          != other.getSpeedUpCount()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (hasCostInfo()) {
      hash = (37 * hash) + COSTINFO_FIELD_NUMBER;
      hash = (53 * hash) + getCostInfo().hashCode();
    }
    if (hasSpeedUpCount()) {
      hash = (37 * hash) + SPEEDUPCOUNT_FIELD_NUMBER;
      hash = (53 * hash) + getSpeedUpCount();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.GodIslandGameAcclerateMoveRespMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GodIslandGameAcclerateMoveRespMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GodIslandGameAcclerateMoveRespMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GodIslandGameAcclerateMoveRespMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GodIslandGameAcclerateMoveRespMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GodIslandGameAcclerateMoveRespMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GodIslandGameAcclerateMoveRespMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.GodIslandGameAcclerateMoveRespMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.GodIslandGameAcclerateMoveRespMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.GodIslandGameAcclerateMoveRespMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.GodIslandGameAcclerateMoveRespMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.GodIslandGameAcclerateMoveRespMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.GodIslandGameAcclerateMoveRespMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.GodIslandGameAcclerateMoveRespMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.GodIslandGameAcclerateMoveRespMsg)
      xddq.pb.GodIslandGameAcclerateMoveRespMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GodIslandGameAcclerateMoveRespMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GodIslandGameAcclerateMoveRespMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.GodIslandGameAcclerateMoveRespMsg.class, xddq.pb.GodIslandGameAcclerateMoveRespMsg.Builder.class);
    }

    // Construct using xddq.pb.GodIslandGameAcclerateMoveRespMsg.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      costInfo_ = "";
      speedUpCount_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GodIslandGameAcclerateMoveRespMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.GodIslandGameAcclerateMoveRespMsg getDefaultInstanceForType() {
      return xddq.pb.GodIslandGameAcclerateMoveRespMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.GodIslandGameAcclerateMoveRespMsg build() {
      xddq.pb.GodIslandGameAcclerateMoveRespMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.GodIslandGameAcclerateMoveRespMsg buildPartial() {
      xddq.pb.GodIslandGameAcclerateMoveRespMsg result = new xddq.pb.GodIslandGameAcclerateMoveRespMsg(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.GodIslandGameAcclerateMoveRespMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.costInfo_ = costInfo_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.speedUpCount_ = speedUpCount_;
        to_bitField0_ |= 0x00000004;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.GodIslandGameAcclerateMoveRespMsg) {
        return mergeFrom((xddq.pb.GodIslandGameAcclerateMoveRespMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.GodIslandGameAcclerateMoveRespMsg other) {
      if (other == xddq.pb.GodIslandGameAcclerateMoveRespMsg.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (other.hasCostInfo()) {
        costInfo_ = other.costInfo_;
        bitField0_ |= 0x00000002;
        onChanged();
      }
      if (other.hasSpeedUpCount()) {
        setSpeedUpCount(other.getSpeedUpCount());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              costInfo_ = input.readBytes();
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 24: {
              speedUpCount_ = input.readInt32();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object costInfo_ = "";
    /**
     * <code>optional string costInfo = 2;</code>
     * @return Whether the costInfo field is set.
     */
    public boolean hasCostInfo() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional string costInfo = 2;</code>
     * @return The costInfo.
     */
    public java.lang.String getCostInfo() {
      java.lang.Object ref = costInfo_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          costInfo_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string costInfo = 2;</code>
     * @return The bytes for costInfo.
     */
    public com.google.protobuf.ByteString
        getCostInfoBytes() {
      java.lang.Object ref = costInfo_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        costInfo_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string costInfo = 2;</code>
     * @param value The costInfo to set.
     * @return This builder for chaining.
     */
    public Builder setCostInfo(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      costInfo_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional string costInfo = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearCostInfo() {
      costInfo_ = getDefaultInstance().getCostInfo();
      bitField0_ = (bitField0_ & ~0x00000002);
      onChanged();
      return this;
    }
    /**
     * <code>optional string costInfo = 2;</code>
     * @param value The bytes for costInfo to set.
     * @return This builder for chaining.
     */
    public Builder setCostInfoBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      costInfo_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }

    private int speedUpCount_ ;
    /**
     * <code>optional int32 speedUpCount = 3;</code>
     * @return Whether the speedUpCount field is set.
     */
    @java.lang.Override
    public boolean hasSpeedUpCount() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int32 speedUpCount = 3;</code>
     * @return The speedUpCount.
     */
    @java.lang.Override
    public int getSpeedUpCount() {
      return speedUpCount_;
    }
    /**
     * <code>optional int32 speedUpCount = 3;</code>
     * @param value The speedUpCount to set.
     * @return This builder for chaining.
     */
    public Builder setSpeedUpCount(int value) {

      speedUpCount_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 speedUpCount = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearSpeedUpCount() {
      bitField0_ = (bitField0_ & ~0x00000004);
      speedUpCount_ = 0;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.GodIslandGameAcclerateMoveRespMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.GodIslandGameAcclerateMoveRespMsg)
  private static final xddq.pb.GodIslandGameAcclerateMoveRespMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.GodIslandGameAcclerateMoveRespMsg();
  }

  public static xddq.pb.GodIslandGameAcclerateMoveRespMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<GodIslandGameAcclerateMoveRespMsg>
      PARSER = new com.google.protobuf.AbstractParser<GodIslandGameAcclerateMoveRespMsg>() {
    @java.lang.Override
    public GodIslandGameAcclerateMoveRespMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<GodIslandGameAcclerateMoveRespMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<GodIslandGameAcclerateMoveRespMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.GodIslandGameAcclerateMoveRespMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

