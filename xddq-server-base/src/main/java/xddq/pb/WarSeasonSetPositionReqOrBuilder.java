// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

public interface WarSeasonSetPositionReqOrBuilder extends
    // @@protoc_insertion_point(interface_extends:xddq.pb.WarSeasonSetPositionReq)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>required int32 activityId = 1;</code>
   * @return Whether the activityId field is set.
   */
  boolean hasActivityId();
  /**
   * <code>required int32 activityId = 1;</code>
   * @return The activityId.
   */
  int getActivityId();

  /**
   * <code>optional int32 unionId = 2;</code>
   * @return Whether the unionId field is set.
   */
  boolean hasUnionId();
  /**
   * <code>optional int32 unionId = 2;</code>
   * @return The unionId.
   */
  int getUnionId();

  /**
   * <code>optional int32 rank = 3;</code>
   * @return Whether the rank field is set.
   */
  boolean hasRank();
  /**
   * <code>optional int32 rank = 3;</code>
   * @return The rank.
   */
  int getRank();
}
