// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.MonopolyGuessUnionInfo}
 */
public final class MonopolyGuessUnionInfo extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.MonopolyGuessUnionInfo)
    MonopolyGuessUnionInfoOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      MonopolyGuessUnionInfo.class.getName());
  }
  // Use MonopolyGuessUnionInfo.newBuilder() to construct.
  private MonopolyGuessUnionInfo(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private MonopolyGuessUnionInfo() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_MonopolyGuessUnionInfo_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_MonopolyGuessUnionInfo_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.MonopolyGuessUnionInfo.class, xddq.pb.MonopolyGuessUnionInfo.Builder.class);
  }

  private int bitField0_;
  public static final int RANKINFO_FIELD_NUMBER = 1;
  private xddq.pb.MonopolyRankMsg rankInfo_;
  /**
   * <code>optional .xddq.pb.MonopolyRankMsg rankInfo = 1;</code>
   * @return Whether the rankInfo field is set.
   */
  @java.lang.Override
  public boolean hasRankInfo() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional .xddq.pb.MonopolyRankMsg rankInfo = 1;</code>
   * @return The rankInfo.
   */
  @java.lang.Override
  public xddq.pb.MonopolyRankMsg getRankInfo() {
    return rankInfo_ == null ? xddq.pb.MonopolyRankMsg.getDefaultInstance() : rankInfo_;
  }
  /**
   * <code>optional .xddq.pb.MonopolyRankMsg rankInfo = 1;</code>
   */
  @java.lang.Override
  public xddq.pb.MonopolyRankMsgOrBuilder getRankInfoOrBuilder() {
    return rankInfo_ == null ? xddq.pb.MonopolyRankMsg.getDefaultInstance() : rankInfo_;
  }

  public static final int SELECTEDCOUNT_FIELD_NUMBER = 2;
  private int selectedCount_ = 0;
  /**
   * <code>optional int32 selectedCount = 2;</code>
   * @return Whether the selectedCount field is set.
   */
  @java.lang.Override
  public boolean hasSelectedCount() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int32 selectedCount = 2;</code>
   * @return The selectedCount.
   */
  @java.lang.Override
  public int getSelectedCount() {
    return selectedCount_;
  }

  public static final int RESULT_FIELD_NUMBER = 3;
  private boolean result_ = false;
  /**
   * <code>optional bool result = 3;</code>
   * @return Whether the result field is set.
   */
  @java.lang.Override
  public boolean hasResult() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional bool result = 3;</code>
   * @return The result.
   */
  @java.lang.Override
  public boolean getResult() {
    return result_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (hasRankInfo()) {
      if (!getRankInfo().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeMessage(1, getRankInfo());
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(2, selectedCount_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeBool(3, result_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, getRankInfo());
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, selectedCount_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(3, result_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.MonopolyGuessUnionInfo)) {
      return super.equals(obj);
    }
    xddq.pb.MonopolyGuessUnionInfo other = (xddq.pb.MonopolyGuessUnionInfo) obj;

    if (hasRankInfo() != other.hasRankInfo()) return false;
    if (hasRankInfo()) {
      if (!getRankInfo()
          .equals(other.getRankInfo())) return false;
    }
    if (hasSelectedCount() != other.hasSelectedCount()) return false;
    if (hasSelectedCount()) {
      if (getSelectedCount()
          != other.getSelectedCount()) return false;
    }
    if (hasResult() != other.hasResult()) return false;
    if (hasResult()) {
      if (getResult()
          != other.getResult()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRankInfo()) {
      hash = (37 * hash) + RANKINFO_FIELD_NUMBER;
      hash = (53 * hash) + getRankInfo().hashCode();
    }
    if (hasSelectedCount()) {
      hash = (37 * hash) + SELECTEDCOUNT_FIELD_NUMBER;
      hash = (53 * hash) + getSelectedCount();
    }
    if (hasResult()) {
      hash = (37 * hash) + RESULT_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getResult());
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.MonopolyGuessUnionInfo parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.MonopolyGuessUnionInfo parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.MonopolyGuessUnionInfo parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.MonopolyGuessUnionInfo parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.MonopolyGuessUnionInfo parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.MonopolyGuessUnionInfo parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.MonopolyGuessUnionInfo parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.MonopolyGuessUnionInfo parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.MonopolyGuessUnionInfo parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.MonopolyGuessUnionInfo parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.MonopolyGuessUnionInfo parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.MonopolyGuessUnionInfo parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.MonopolyGuessUnionInfo prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.MonopolyGuessUnionInfo}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.MonopolyGuessUnionInfo)
      xddq.pb.MonopolyGuessUnionInfoOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_MonopolyGuessUnionInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_MonopolyGuessUnionInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.MonopolyGuessUnionInfo.class, xddq.pb.MonopolyGuessUnionInfo.Builder.class);
    }

    // Construct using xddq.pb.MonopolyGuessUnionInfo.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetRankInfoFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      rankInfo_ = null;
      if (rankInfoBuilder_ != null) {
        rankInfoBuilder_.dispose();
        rankInfoBuilder_ = null;
      }
      selectedCount_ = 0;
      result_ = false;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_MonopolyGuessUnionInfo_descriptor;
    }

    @java.lang.Override
    public xddq.pb.MonopolyGuessUnionInfo getDefaultInstanceForType() {
      return xddq.pb.MonopolyGuessUnionInfo.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.MonopolyGuessUnionInfo build() {
      xddq.pb.MonopolyGuessUnionInfo result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.MonopolyGuessUnionInfo buildPartial() {
      xddq.pb.MonopolyGuessUnionInfo result = new xddq.pb.MonopolyGuessUnionInfo(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.MonopolyGuessUnionInfo result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.rankInfo_ = rankInfoBuilder_ == null
            ? rankInfo_
            : rankInfoBuilder_.build();
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.selectedCount_ = selectedCount_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.result_ = result_;
        to_bitField0_ |= 0x00000004;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.MonopolyGuessUnionInfo) {
        return mergeFrom((xddq.pb.MonopolyGuessUnionInfo)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.MonopolyGuessUnionInfo other) {
      if (other == xddq.pb.MonopolyGuessUnionInfo.getDefaultInstance()) return this;
      if (other.hasRankInfo()) {
        mergeRankInfo(other.getRankInfo());
      }
      if (other.hasSelectedCount()) {
        setSelectedCount(other.getSelectedCount());
      }
      if (other.hasResult()) {
        setResult(other.getResult());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (hasRankInfo()) {
        if (!getRankInfo().isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              input.readMessage(
                  internalGetRankInfoFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 16: {
              selectedCount_ = input.readInt32();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 24: {
              result_ = input.readBool();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private xddq.pb.MonopolyRankMsg rankInfo_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.MonopolyRankMsg, xddq.pb.MonopolyRankMsg.Builder, xddq.pb.MonopolyRankMsgOrBuilder> rankInfoBuilder_;
    /**
     * <code>optional .xddq.pb.MonopolyRankMsg rankInfo = 1;</code>
     * @return Whether the rankInfo field is set.
     */
    public boolean hasRankInfo() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .xddq.pb.MonopolyRankMsg rankInfo = 1;</code>
     * @return The rankInfo.
     */
    public xddq.pb.MonopolyRankMsg getRankInfo() {
      if (rankInfoBuilder_ == null) {
        return rankInfo_ == null ? xddq.pb.MonopolyRankMsg.getDefaultInstance() : rankInfo_;
      } else {
        return rankInfoBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.MonopolyRankMsg rankInfo = 1;</code>
     */
    public Builder setRankInfo(xddq.pb.MonopolyRankMsg value) {
      if (rankInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        rankInfo_ = value;
      } else {
        rankInfoBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.MonopolyRankMsg rankInfo = 1;</code>
     */
    public Builder setRankInfo(
        xddq.pb.MonopolyRankMsg.Builder builderForValue) {
      if (rankInfoBuilder_ == null) {
        rankInfo_ = builderForValue.build();
      } else {
        rankInfoBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.MonopolyRankMsg rankInfo = 1;</code>
     */
    public Builder mergeRankInfo(xddq.pb.MonopolyRankMsg value) {
      if (rankInfoBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0) &&
          rankInfo_ != null &&
          rankInfo_ != xddq.pb.MonopolyRankMsg.getDefaultInstance()) {
          getRankInfoBuilder().mergeFrom(value);
        } else {
          rankInfo_ = value;
        }
      } else {
        rankInfoBuilder_.mergeFrom(value);
      }
      if (rankInfo_ != null) {
        bitField0_ |= 0x00000001;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.MonopolyRankMsg rankInfo = 1;</code>
     */
    public Builder clearRankInfo() {
      bitField0_ = (bitField0_ & ~0x00000001);
      rankInfo_ = null;
      if (rankInfoBuilder_ != null) {
        rankInfoBuilder_.dispose();
        rankInfoBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.MonopolyRankMsg rankInfo = 1;</code>
     */
    public xddq.pb.MonopolyRankMsg.Builder getRankInfoBuilder() {
      bitField0_ |= 0x00000001;
      onChanged();
      return internalGetRankInfoFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.MonopolyRankMsg rankInfo = 1;</code>
     */
    public xddq.pb.MonopolyRankMsgOrBuilder getRankInfoOrBuilder() {
      if (rankInfoBuilder_ != null) {
        return rankInfoBuilder_.getMessageOrBuilder();
      } else {
        return rankInfo_ == null ?
            xddq.pb.MonopolyRankMsg.getDefaultInstance() : rankInfo_;
      }
    }
    /**
     * <code>optional .xddq.pb.MonopolyRankMsg rankInfo = 1;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.MonopolyRankMsg, xddq.pb.MonopolyRankMsg.Builder, xddq.pb.MonopolyRankMsgOrBuilder> 
        internalGetRankInfoFieldBuilder() {
      if (rankInfoBuilder_ == null) {
        rankInfoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.MonopolyRankMsg, xddq.pb.MonopolyRankMsg.Builder, xddq.pb.MonopolyRankMsgOrBuilder>(
                getRankInfo(),
                getParentForChildren(),
                isClean());
        rankInfo_ = null;
      }
      return rankInfoBuilder_;
    }

    private int selectedCount_ ;
    /**
     * <code>optional int32 selectedCount = 2;</code>
     * @return Whether the selectedCount field is set.
     */
    @java.lang.Override
    public boolean hasSelectedCount() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 selectedCount = 2;</code>
     * @return The selectedCount.
     */
    @java.lang.Override
    public int getSelectedCount() {
      return selectedCount_;
    }
    /**
     * <code>optional int32 selectedCount = 2;</code>
     * @param value The selectedCount to set.
     * @return This builder for chaining.
     */
    public Builder setSelectedCount(int value) {

      selectedCount_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 selectedCount = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearSelectedCount() {
      bitField0_ = (bitField0_ & ~0x00000002);
      selectedCount_ = 0;
      onChanged();
      return this;
    }

    private boolean result_ ;
    /**
     * <code>optional bool result = 3;</code>
     * @return Whether the result field is set.
     */
    @java.lang.Override
    public boolean hasResult() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional bool result = 3;</code>
     * @return The result.
     */
    @java.lang.Override
    public boolean getResult() {
      return result_;
    }
    /**
     * <code>optional bool result = 3;</code>
     * @param value The result to set.
     * @return This builder for chaining.
     */
    public Builder setResult(boolean value) {

      result_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional bool result = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearResult() {
      bitField0_ = (bitField0_ & ~0x00000004);
      result_ = false;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.MonopolyGuessUnionInfo)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.MonopolyGuessUnionInfo)
  private static final xddq.pb.MonopolyGuessUnionInfo DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.MonopolyGuessUnionInfo();
  }

  public static xddq.pb.MonopolyGuessUnionInfo getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<MonopolyGuessUnionInfo>
      PARSER = new com.google.protobuf.AbstractParser<MonopolyGuessUnionInfo>() {
    @java.lang.Override
    public MonopolyGuessUnionInfo parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<MonopolyGuessUnionInfo> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<MonopolyGuessUnionInfo> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.MonopolyGuessUnionInfo getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

