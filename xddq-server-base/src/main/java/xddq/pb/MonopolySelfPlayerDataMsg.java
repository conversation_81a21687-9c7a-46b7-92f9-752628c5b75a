// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.MonopolySelfPlayerDataMsg}
 */
public final class MonopolySelfPlayerDataMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.MonopolySelfPlayerDataMsg)
    MonopolySelfPlayerDataMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      MonopolySelfPlayerDataMsg.class.getName());
  }
  // Use MonopolySelfPlayerDataMsg.newBuilder() to construct.
  private MonopolySelfPlayerDataMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private MonopolySelfPlayerDataMsg() {
    nickName_ = "";
    fightValue_ = "";
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_MonopolySelfPlayerDataMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_MonopolySelfPlayerDataMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.MonopolySelfPlayerDataMsg.class, xddq.pb.MonopolySelfPlayerDataMsg.Builder.class);
  }

  private int bitField0_;
  public static final int HEADDATA_FIELD_NUMBER = 1;
  private xddq.pb.PlayerHeadDataMsg headData_;
  /**
   * <code>optional .xddq.pb.PlayerHeadDataMsg headData = 1;</code>
   * @return Whether the headData field is set.
   */
  @java.lang.Override
  public boolean hasHeadData() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional .xddq.pb.PlayerHeadDataMsg headData = 1;</code>
   * @return The headData.
   */
  @java.lang.Override
  public xddq.pb.PlayerHeadDataMsg getHeadData() {
    return headData_ == null ? xddq.pb.PlayerHeadDataMsg.getDefaultInstance() : headData_;
  }
  /**
   * <code>optional .xddq.pb.PlayerHeadDataMsg headData = 1;</code>
   */
  @java.lang.Override
  public xddq.pb.PlayerHeadDataMsgOrBuilder getHeadDataOrBuilder() {
    return headData_ == null ? xddq.pb.PlayerHeadDataMsg.getDefaultInstance() : headData_;
  }

  public static final int CURRENTPOSITION_FIELD_NUMBER = 2;
  private int currentPosition_ = 0;
  /**
   * <code>optional int32 currentPosition = 2;</code>
   * @return Whether the currentPosition field is set.
   */
  @java.lang.Override
  public boolean hasCurrentPosition() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int32 currentPosition = 2;</code>
   * @return The currentPosition.
   */
  @java.lang.Override
  public int getCurrentPosition() {
    return currentPosition_;
  }

  public static final int APPEARANCEID_FIELD_NUMBER = 3;
  private int appearanceId_ = 0;
  /**
   * <code>optional int32 appearanceId = 3;</code>
   * @return Whether the appearanceId field is set.
   */
  @java.lang.Override
  public boolean hasAppearanceId() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional int32 appearanceId = 3;</code>
   * @return The appearanceId.
   */
  @java.lang.Override
  public int getAppearanceId() {
    return appearanceId_;
  }

  public static final int CLOUDID_FIELD_NUMBER = 4;
  private int cloudId_ = 0;
  /**
   * <code>optional int32 cloudId = 4;</code>
   * @return Whether the cloudId field is set.
   */
  @java.lang.Override
  public boolean hasCloudId() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional int32 cloudId = 4;</code>
   * @return The cloudId.
   */
  @java.lang.Override
  public int getCloudId() {
    return cloudId_;
  }

  public static final int NICKNAME_FIELD_NUMBER = 5;
  @SuppressWarnings("serial")
  private volatile java.lang.Object nickName_ = "";
  /**
   * <code>optional string nickName = 5;</code>
   * @return Whether the nickName field is set.
   */
  @java.lang.Override
  public boolean hasNickName() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>optional string nickName = 5;</code>
   * @return The nickName.
   */
  @java.lang.Override
  public java.lang.String getNickName() {
    java.lang.Object ref = nickName_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        nickName_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string nickName = 5;</code>
   * @return The bytes for nickName.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getNickNameBytes() {
    java.lang.Object ref = nickName_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      nickName_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int STRENGTH_FIELD_NUMBER = 6;
  private int strength_ = 0;
  /**
   * <code>optional int32 strength = 6;</code>
   * @return Whether the strength field is set.
   */
  @java.lang.Override
  public boolean hasStrength() {
    return ((bitField0_ & 0x00000020) != 0);
  }
  /**
   * <code>optional int32 strength = 6;</code>
   * @return The strength.
   */
  @java.lang.Override
  public int getStrength() {
    return strength_;
  }

  public static final int FIGHTVALUE_FIELD_NUMBER = 7;
  @SuppressWarnings("serial")
  private volatile java.lang.Object fightValue_ = "";
  /**
   * <code>optional string fightValue = 7;</code>
   * @return Whether the fightValue field is set.
   */
  @java.lang.Override
  public boolean hasFightValue() {
    return ((bitField0_ & 0x00000040) != 0);
  }
  /**
   * <code>optional string fightValue = 7;</code>
   * @return The fightValue.
   */
  @java.lang.Override
  public java.lang.String getFightValue() {
    java.lang.Object ref = fightValue_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        fightValue_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string fightValue = 7;</code>
   * @return The bytes for fightValue.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getFightValueBytes() {
    java.lang.Object ref = fightValue_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      fightValue_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int LASTRECOVERYTIME_FIELD_NUMBER = 8;
  private long lastRecoveryTime_ = 0L;
  /**
   * <code>optional int64 lastRecoveryTime = 8;</code>
   * @return Whether the lastRecoveryTime field is set.
   */
  @java.lang.Override
  public boolean hasLastRecoveryTime() {
    return ((bitField0_ & 0x00000080) != 0);
  }
  /**
   * <code>optional int64 lastRecoveryTime = 8;</code>
   * @return The lastRecoveryTime.
   */
  @java.lang.Override
  public long getLastRecoveryTime() {
    return lastRecoveryTime_;
  }

  public static final int TRAPENDTIME_FIELD_NUMBER = 9;
  private long trapEndTime_ = 0L;
  /**
   * <code>optional int64 trapEndTime = 9;</code>
   * @return Whether the trapEndTime field is set.
   */
  @java.lang.Override
  public boolean hasTrapEndTime() {
    return ((bitField0_ & 0x00000100) != 0);
  }
  /**
   * <code>optional int64 trapEndTime = 9;</code>
   * @return The trapEndTime.
   */
  @java.lang.Override
  public long getTrapEndTime() {
    return trapEndTime_;
  }

  public static final int DOUBLEREWARDCOUNT_FIELD_NUMBER = 10;
  private int doubleRewardCount_ = 0;
  /**
   * <code>optional int32 doubleRewardCount = 10;</code>
   * @return Whether the doubleRewardCount field is set.
   */
  @java.lang.Override
  public boolean hasDoubleRewardCount() {
    return ((bitField0_ & 0x00000200) != 0);
  }
  /**
   * <code>optional int32 doubleRewardCount = 10;</code>
   * @return The doubleRewardCount.
   */
  @java.lang.Override
  public int getDoubleRewardCount() {
    return doubleRewardCount_;
  }

  public static final int SCORE_FIELD_NUMBER = 11;
  private int score_ = 0;
  /**
   * <code>optional int32 score = 11;</code>
   * @return Whether the score field is set.
   */
  @java.lang.Override
  public boolean hasScore() {
    return ((bitField0_ & 0x00000400) != 0);
  }
  /**
   * <code>optional int32 score = 11;</code>
   * @return The score.
   */
  @java.lang.Override
  public int getScore() {
    return score_;
  }

  public static final int TOTALCOSTSTRENGTH_FIELD_NUMBER = 12;
  private int totalCostStrength_ = 0;
  /**
   * <code>optional int32 totalCostStrength = 12;</code>
   * @return Whether the totalCostStrength field is set.
   */
  @java.lang.Override
  public boolean hasTotalCostStrength() {
    return ((bitField0_ & 0x00000800) != 0);
  }
  /**
   * <code>optional int32 totalCostStrength = 12;</code>
   * @return The totalCostStrength.
   */
  @java.lang.Override
  public int getTotalCostStrength() {
    return totalCostStrength_;
  }

  public static final int TRIGGERMOVEBUFF_FIELD_NUMBER = 13;
  private int triggerMoveBuff_ = 0;
  /**
   * <code>optional int32 triggerMoveBuff = 13;</code>
   * @return Whether the triggerMoveBuff field is set.
   */
  @java.lang.Override
  public boolean hasTriggerMoveBuff() {
    return ((bitField0_ & 0x00001000) != 0);
  }
  /**
   * <code>optional int32 triggerMoveBuff = 13;</code>
   * @return The triggerMoveBuff.
   */
  @java.lang.Override
  public int getTriggerMoveBuff() {
    return triggerMoveBuff_;
  }

  public static final int NEEDMOVE_FIELD_NUMBER = 14;
  private boolean needMove_ = false;
  /**
   * <code>optional bool needMove = 14;</code>
   * @return Whether the needMove field is set.
   */
  @java.lang.Override
  public boolean hasNeedMove() {
    return ((bitField0_ & 0x00002000) != 0);
  }
  /**
   * <code>optional bool needMove = 14;</code>
   * @return The needMove.
   */
  @java.lang.Override
  public boolean getNeedMove() {
    return needMove_;
  }

  public static final int TITLE_FIELD_NUMBER = 15;
  private int title_ = 0;
  /**
   * <code>optional int32 title = 15;</code>
   * @return Whether the title field is set.
   */
  @java.lang.Override
  public boolean hasTitle() {
    return ((bitField0_ & 0x00004000) != 0);
  }
  /**
   * <code>optional int32 title = 15;</code>
   * @return The title.
   */
  @java.lang.Override
  public int getTitle() {
    return title_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeMessage(1, getHeadData());
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(2, currentPosition_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt32(3, appearanceId_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt32(4, cloudId_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 5, nickName_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      output.writeInt32(6, strength_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 7, fightValue_);
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      output.writeInt64(8, lastRecoveryTime_);
    }
    if (((bitField0_ & 0x00000100) != 0)) {
      output.writeInt64(9, trapEndTime_);
    }
    if (((bitField0_ & 0x00000200) != 0)) {
      output.writeInt32(10, doubleRewardCount_);
    }
    if (((bitField0_ & 0x00000400) != 0)) {
      output.writeInt32(11, score_);
    }
    if (((bitField0_ & 0x00000800) != 0)) {
      output.writeInt32(12, totalCostStrength_);
    }
    if (((bitField0_ & 0x00001000) != 0)) {
      output.writeInt32(13, triggerMoveBuff_);
    }
    if (((bitField0_ & 0x00002000) != 0)) {
      output.writeBool(14, needMove_);
    }
    if (((bitField0_ & 0x00004000) != 0)) {
      output.writeInt32(15, title_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, getHeadData());
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, currentPosition_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(3, appearanceId_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(4, cloudId_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(5, nickName_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(6, strength_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(7, fightValue_);
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(8, lastRecoveryTime_);
    }
    if (((bitField0_ & 0x00000100) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(9, trapEndTime_);
    }
    if (((bitField0_ & 0x00000200) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(10, doubleRewardCount_);
    }
    if (((bitField0_ & 0x00000400) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(11, score_);
    }
    if (((bitField0_ & 0x00000800) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(12, totalCostStrength_);
    }
    if (((bitField0_ & 0x00001000) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(13, triggerMoveBuff_);
    }
    if (((bitField0_ & 0x00002000) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(14, needMove_);
    }
    if (((bitField0_ & 0x00004000) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(15, title_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.MonopolySelfPlayerDataMsg)) {
      return super.equals(obj);
    }
    xddq.pb.MonopolySelfPlayerDataMsg other = (xddq.pb.MonopolySelfPlayerDataMsg) obj;

    if (hasHeadData() != other.hasHeadData()) return false;
    if (hasHeadData()) {
      if (!getHeadData()
          .equals(other.getHeadData())) return false;
    }
    if (hasCurrentPosition() != other.hasCurrentPosition()) return false;
    if (hasCurrentPosition()) {
      if (getCurrentPosition()
          != other.getCurrentPosition()) return false;
    }
    if (hasAppearanceId() != other.hasAppearanceId()) return false;
    if (hasAppearanceId()) {
      if (getAppearanceId()
          != other.getAppearanceId()) return false;
    }
    if (hasCloudId() != other.hasCloudId()) return false;
    if (hasCloudId()) {
      if (getCloudId()
          != other.getCloudId()) return false;
    }
    if (hasNickName() != other.hasNickName()) return false;
    if (hasNickName()) {
      if (!getNickName()
          .equals(other.getNickName())) return false;
    }
    if (hasStrength() != other.hasStrength()) return false;
    if (hasStrength()) {
      if (getStrength()
          != other.getStrength()) return false;
    }
    if (hasFightValue() != other.hasFightValue()) return false;
    if (hasFightValue()) {
      if (!getFightValue()
          .equals(other.getFightValue())) return false;
    }
    if (hasLastRecoveryTime() != other.hasLastRecoveryTime()) return false;
    if (hasLastRecoveryTime()) {
      if (getLastRecoveryTime()
          != other.getLastRecoveryTime()) return false;
    }
    if (hasTrapEndTime() != other.hasTrapEndTime()) return false;
    if (hasTrapEndTime()) {
      if (getTrapEndTime()
          != other.getTrapEndTime()) return false;
    }
    if (hasDoubleRewardCount() != other.hasDoubleRewardCount()) return false;
    if (hasDoubleRewardCount()) {
      if (getDoubleRewardCount()
          != other.getDoubleRewardCount()) return false;
    }
    if (hasScore() != other.hasScore()) return false;
    if (hasScore()) {
      if (getScore()
          != other.getScore()) return false;
    }
    if (hasTotalCostStrength() != other.hasTotalCostStrength()) return false;
    if (hasTotalCostStrength()) {
      if (getTotalCostStrength()
          != other.getTotalCostStrength()) return false;
    }
    if (hasTriggerMoveBuff() != other.hasTriggerMoveBuff()) return false;
    if (hasTriggerMoveBuff()) {
      if (getTriggerMoveBuff()
          != other.getTriggerMoveBuff()) return false;
    }
    if (hasNeedMove() != other.hasNeedMove()) return false;
    if (hasNeedMove()) {
      if (getNeedMove()
          != other.getNeedMove()) return false;
    }
    if (hasTitle() != other.hasTitle()) return false;
    if (hasTitle()) {
      if (getTitle()
          != other.getTitle()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasHeadData()) {
      hash = (37 * hash) + HEADDATA_FIELD_NUMBER;
      hash = (53 * hash) + getHeadData().hashCode();
    }
    if (hasCurrentPosition()) {
      hash = (37 * hash) + CURRENTPOSITION_FIELD_NUMBER;
      hash = (53 * hash) + getCurrentPosition();
    }
    if (hasAppearanceId()) {
      hash = (37 * hash) + APPEARANCEID_FIELD_NUMBER;
      hash = (53 * hash) + getAppearanceId();
    }
    if (hasCloudId()) {
      hash = (37 * hash) + CLOUDID_FIELD_NUMBER;
      hash = (53 * hash) + getCloudId();
    }
    if (hasNickName()) {
      hash = (37 * hash) + NICKNAME_FIELD_NUMBER;
      hash = (53 * hash) + getNickName().hashCode();
    }
    if (hasStrength()) {
      hash = (37 * hash) + STRENGTH_FIELD_NUMBER;
      hash = (53 * hash) + getStrength();
    }
    if (hasFightValue()) {
      hash = (37 * hash) + FIGHTVALUE_FIELD_NUMBER;
      hash = (53 * hash) + getFightValue().hashCode();
    }
    if (hasLastRecoveryTime()) {
      hash = (37 * hash) + LASTRECOVERYTIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getLastRecoveryTime());
    }
    if (hasTrapEndTime()) {
      hash = (37 * hash) + TRAPENDTIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTrapEndTime());
    }
    if (hasDoubleRewardCount()) {
      hash = (37 * hash) + DOUBLEREWARDCOUNT_FIELD_NUMBER;
      hash = (53 * hash) + getDoubleRewardCount();
    }
    if (hasScore()) {
      hash = (37 * hash) + SCORE_FIELD_NUMBER;
      hash = (53 * hash) + getScore();
    }
    if (hasTotalCostStrength()) {
      hash = (37 * hash) + TOTALCOSTSTRENGTH_FIELD_NUMBER;
      hash = (53 * hash) + getTotalCostStrength();
    }
    if (hasTriggerMoveBuff()) {
      hash = (37 * hash) + TRIGGERMOVEBUFF_FIELD_NUMBER;
      hash = (53 * hash) + getTriggerMoveBuff();
    }
    if (hasNeedMove()) {
      hash = (37 * hash) + NEEDMOVE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getNeedMove());
    }
    if (hasTitle()) {
      hash = (37 * hash) + TITLE_FIELD_NUMBER;
      hash = (53 * hash) + getTitle();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.MonopolySelfPlayerDataMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.MonopolySelfPlayerDataMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.MonopolySelfPlayerDataMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.MonopolySelfPlayerDataMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.MonopolySelfPlayerDataMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.MonopolySelfPlayerDataMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.MonopolySelfPlayerDataMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.MonopolySelfPlayerDataMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.MonopolySelfPlayerDataMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.MonopolySelfPlayerDataMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.MonopolySelfPlayerDataMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.MonopolySelfPlayerDataMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.MonopolySelfPlayerDataMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.MonopolySelfPlayerDataMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.MonopolySelfPlayerDataMsg)
      xddq.pb.MonopolySelfPlayerDataMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_MonopolySelfPlayerDataMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_MonopolySelfPlayerDataMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.MonopolySelfPlayerDataMsg.class, xddq.pb.MonopolySelfPlayerDataMsg.Builder.class);
    }

    // Construct using xddq.pb.MonopolySelfPlayerDataMsg.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetHeadDataFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      headData_ = null;
      if (headDataBuilder_ != null) {
        headDataBuilder_.dispose();
        headDataBuilder_ = null;
      }
      currentPosition_ = 0;
      appearanceId_ = 0;
      cloudId_ = 0;
      nickName_ = "";
      strength_ = 0;
      fightValue_ = "";
      lastRecoveryTime_ = 0L;
      trapEndTime_ = 0L;
      doubleRewardCount_ = 0;
      score_ = 0;
      totalCostStrength_ = 0;
      triggerMoveBuff_ = 0;
      needMove_ = false;
      title_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_MonopolySelfPlayerDataMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.MonopolySelfPlayerDataMsg getDefaultInstanceForType() {
      return xddq.pb.MonopolySelfPlayerDataMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.MonopolySelfPlayerDataMsg build() {
      xddq.pb.MonopolySelfPlayerDataMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.MonopolySelfPlayerDataMsg buildPartial() {
      xddq.pb.MonopolySelfPlayerDataMsg result = new xddq.pb.MonopolySelfPlayerDataMsg(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.MonopolySelfPlayerDataMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.headData_ = headDataBuilder_ == null
            ? headData_
            : headDataBuilder_.build();
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.currentPosition_ = currentPosition_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.appearanceId_ = appearanceId_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.cloudId_ = cloudId_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.nickName_ = nickName_;
        to_bitField0_ |= 0x00000010;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.strength_ = strength_;
        to_bitField0_ |= 0x00000020;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.fightValue_ = fightValue_;
        to_bitField0_ |= 0x00000040;
      }
      if (((from_bitField0_ & 0x00000080) != 0)) {
        result.lastRecoveryTime_ = lastRecoveryTime_;
        to_bitField0_ |= 0x00000080;
      }
      if (((from_bitField0_ & 0x00000100) != 0)) {
        result.trapEndTime_ = trapEndTime_;
        to_bitField0_ |= 0x00000100;
      }
      if (((from_bitField0_ & 0x00000200) != 0)) {
        result.doubleRewardCount_ = doubleRewardCount_;
        to_bitField0_ |= 0x00000200;
      }
      if (((from_bitField0_ & 0x00000400) != 0)) {
        result.score_ = score_;
        to_bitField0_ |= 0x00000400;
      }
      if (((from_bitField0_ & 0x00000800) != 0)) {
        result.totalCostStrength_ = totalCostStrength_;
        to_bitField0_ |= 0x00000800;
      }
      if (((from_bitField0_ & 0x00001000) != 0)) {
        result.triggerMoveBuff_ = triggerMoveBuff_;
        to_bitField0_ |= 0x00001000;
      }
      if (((from_bitField0_ & 0x00002000) != 0)) {
        result.needMove_ = needMove_;
        to_bitField0_ |= 0x00002000;
      }
      if (((from_bitField0_ & 0x00004000) != 0)) {
        result.title_ = title_;
        to_bitField0_ |= 0x00004000;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.MonopolySelfPlayerDataMsg) {
        return mergeFrom((xddq.pb.MonopolySelfPlayerDataMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.MonopolySelfPlayerDataMsg other) {
      if (other == xddq.pb.MonopolySelfPlayerDataMsg.getDefaultInstance()) return this;
      if (other.hasHeadData()) {
        mergeHeadData(other.getHeadData());
      }
      if (other.hasCurrentPosition()) {
        setCurrentPosition(other.getCurrentPosition());
      }
      if (other.hasAppearanceId()) {
        setAppearanceId(other.getAppearanceId());
      }
      if (other.hasCloudId()) {
        setCloudId(other.getCloudId());
      }
      if (other.hasNickName()) {
        nickName_ = other.nickName_;
        bitField0_ |= 0x00000010;
        onChanged();
      }
      if (other.hasStrength()) {
        setStrength(other.getStrength());
      }
      if (other.hasFightValue()) {
        fightValue_ = other.fightValue_;
        bitField0_ |= 0x00000040;
        onChanged();
      }
      if (other.hasLastRecoveryTime()) {
        setLastRecoveryTime(other.getLastRecoveryTime());
      }
      if (other.hasTrapEndTime()) {
        setTrapEndTime(other.getTrapEndTime());
      }
      if (other.hasDoubleRewardCount()) {
        setDoubleRewardCount(other.getDoubleRewardCount());
      }
      if (other.hasScore()) {
        setScore(other.getScore());
      }
      if (other.hasTotalCostStrength()) {
        setTotalCostStrength(other.getTotalCostStrength());
      }
      if (other.hasTriggerMoveBuff()) {
        setTriggerMoveBuff(other.getTriggerMoveBuff());
      }
      if (other.hasNeedMove()) {
        setNeedMove(other.getNeedMove());
      }
      if (other.hasTitle()) {
        setTitle(other.getTitle());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              input.readMessage(
                  internalGetHeadDataFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 16: {
              currentPosition_ = input.readInt32();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 24: {
              appearanceId_ = input.readInt32();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 32: {
              cloudId_ = input.readInt32();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            case 42: {
              nickName_ = input.readBytes();
              bitField0_ |= 0x00000010;
              break;
            } // case 42
            case 48: {
              strength_ = input.readInt32();
              bitField0_ |= 0x00000020;
              break;
            } // case 48
            case 58: {
              fightValue_ = input.readBytes();
              bitField0_ |= 0x00000040;
              break;
            } // case 58
            case 64: {
              lastRecoveryTime_ = input.readInt64();
              bitField0_ |= 0x00000080;
              break;
            } // case 64
            case 72: {
              trapEndTime_ = input.readInt64();
              bitField0_ |= 0x00000100;
              break;
            } // case 72
            case 80: {
              doubleRewardCount_ = input.readInt32();
              bitField0_ |= 0x00000200;
              break;
            } // case 80
            case 88: {
              score_ = input.readInt32();
              bitField0_ |= 0x00000400;
              break;
            } // case 88
            case 96: {
              totalCostStrength_ = input.readInt32();
              bitField0_ |= 0x00000800;
              break;
            } // case 96
            case 104: {
              triggerMoveBuff_ = input.readInt32();
              bitField0_ |= 0x00001000;
              break;
            } // case 104
            case 112: {
              needMove_ = input.readBool();
              bitField0_ |= 0x00002000;
              break;
            } // case 112
            case 120: {
              title_ = input.readInt32();
              bitField0_ |= 0x00004000;
              break;
            } // case 120
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private xddq.pb.PlayerHeadDataMsg headData_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PlayerHeadDataMsg, xddq.pb.PlayerHeadDataMsg.Builder, xddq.pb.PlayerHeadDataMsgOrBuilder> headDataBuilder_;
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg headData = 1;</code>
     * @return Whether the headData field is set.
     */
    public boolean hasHeadData() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg headData = 1;</code>
     * @return The headData.
     */
    public xddq.pb.PlayerHeadDataMsg getHeadData() {
      if (headDataBuilder_ == null) {
        return headData_ == null ? xddq.pb.PlayerHeadDataMsg.getDefaultInstance() : headData_;
      } else {
        return headDataBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg headData = 1;</code>
     */
    public Builder setHeadData(xddq.pb.PlayerHeadDataMsg value) {
      if (headDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        headData_ = value;
      } else {
        headDataBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg headData = 1;</code>
     */
    public Builder setHeadData(
        xddq.pb.PlayerHeadDataMsg.Builder builderForValue) {
      if (headDataBuilder_ == null) {
        headData_ = builderForValue.build();
      } else {
        headDataBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg headData = 1;</code>
     */
    public Builder mergeHeadData(xddq.pb.PlayerHeadDataMsg value) {
      if (headDataBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0) &&
          headData_ != null &&
          headData_ != xddq.pb.PlayerHeadDataMsg.getDefaultInstance()) {
          getHeadDataBuilder().mergeFrom(value);
        } else {
          headData_ = value;
        }
      } else {
        headDataBuilder_.mergeFrom(value);
      }
      if (headData_ != null) {
        bitField0_ |= 0x00000001;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg headData = 1;</code>
     */
    public Builder clearHeadData() {
      bitField0_ = (bitField0_ & ~0x00000001);
      headData_ = null;
      if (headDataBuilder_ != null) {
        headDataBuilder_.dispose();
        headDataBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg headData = 1;</code>
     */
    public xddq.pb.PlayerHeadDataMsg.Builder getHeadDataBuilder() {
      bitField0_ |= 0x00000001;
      onChanged();
      return internalGetHeadDataFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg headData = 1;</code>
     */
    public xddq.pb.PlayerHeadDataMsgOrBuilder getHeadDataOrBuilder() {
      if (headDataBuilder_ != null) {
        return headDataBuilder_.getMessageOrBuilder();
      } else {
        return headData_ == null ?
            xddq.pb.PlayerHeadDataMsg.getDefaultInstance() : headData_;
      }
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg headData = 1;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PlayerHeadDataMsg, xddq.pb.PlayerHeadDataMsg.Builder, xddq.pb.PlayerHeadDataMsgOrBuilder> 
        internalGetHeadDataFieldBuilder() {
      if (headDataBuilder_ == null) {
        headDataBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.PlayerHeadDataMsg, xddq.pb.PlayerHeadDataMsg.Builder, xddq.pb.PlayerHeadDataMsgOrBuilder>(
                getHeadData(),
                getParentForChildren(),
                isClean());
        headData_ = null;
      }
      return headDataBuilder_;
    }

    private int currentPosition_ ;
    /**
     * <code>optional int32 currentPosition = 2;</code>
     * @return Whether the currentPosition field is set.
     */
    @java.lang.Override
    public boolean hasCurrentPosition() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 currentPosition = 2;</code>
     * @return The currentPosition.
     */
    @java.lang.Override
    public int getCurrentPosition() {
      return currentPosition_;
    }
    /**
     * <code>optional int32 currentPosition = 2;</code>
     * @param value The currentPosition to set.
     * @return This builder for chaining.
     */
    public Builder setCurrentPosition(int value) {

      currentPosition_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 currentPosition = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearCurrentPosition() {
      bitField0_ = (bitField0_ & ~0x00000002);
      currentPosition_ = 0;
      onChanged();
      return this;
    }

    private int appearanceId_ ;
    /**
     * <code>optional int32 appearanceId = 3;</code>
     * @return Whether the appearanceId field is set.
     */
    @java.lang.Override
    public boolean hasAppearanceId() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int32 appearanceId = 3;</code>
     * @return The appearanceId.
     */
    @java.lang.Override
    public int getAppearanceId() {
      return appearanceId_;
    }
    /**
     * <code>optional int32 appearanceId = 3;</code>
     * @param value The appearanceId to set.
     * @return This builder for chaining.
     */
    public Builder setAppearanceId(int value) {

      appearanceId_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 appearanceId = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearAppearanceId() {
      bitField0_ = (bitField0_ & ~0x00000004);
      appearanceId_ = 0;
      onChanged();
      return this;
    }

    private int cloudId_ ;
    /**
     * <code>optional int32 cloudId = 4;</code>
     * @return Whether the cloudId field is set.
     */
    @java.lang.Override
    public boolean hasCloudId() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int32 cloudId = 4;</code>
     * @return The cloudId.
     */
    @java.lang.Override
    public int getCloudId() {
      return cloudId_;
    }
    /**
     * <code>optional int32 cloudId = 4;</code>
     * @param value The cloudId to set.
     * @return This builder for chaining.
     */
    public Builder setCloudId(int value) {

      cloudId_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 cloudId = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearCloudId() {
      bitField0_ = (bitField0_ & ~0x00000008);
      cloudId_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object nickName_ = "";
    /**
     * <code>optional string nickName = 5;</code>
     * @return Whether the nickName field is set.
     */
    public boolean hasNickName() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional string nickName = 5;</code>
     * @return The nickName.
     */
    public java.lang.String getNickName() {
      java.lang.Object ref = nickName_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          nickName_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string nickName = 5;</code>
     * @return The bytes for nickName.
     */
    public com.google.protobuf.ByteString
        getNickNameBytes() {
      java.lang.Object ref = nickName_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        nickName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string nickName = 5;</code>
     * @param value The nickName to set.
     * @return This builder for chaining.
     */
    public Builder setNickName(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      nickName_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional string nickName = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearNickName() {
      nickName_ = getDefaultInstance().getNickName();
      bitField0_ = (bitField0_ & ~0x00000010);
      onChanged();
      return this;
    }
    /**
     * <code>optional string nickName = 5;</code>
     * @param value The bytes for nickName to set.
     * @return This builder for chaining.
     */
    public Builder setNickNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      nickName_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }

    private int strength_ ;
    /**
     * <code>optional int32 strength = 6;</code>
     * @return Whether the strength field is set.
     */
    @java.lang.Override
    public boolean hasStrength() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional int32 strength = 6;</code>
     * @return The strength.
     */
    @java.lang.Override
    public int getStrength() {
      return strength_;
    }
    /**
     * <code>optional int32 strength = 6;</code>
     * @param value The strength to set.
     * @return This builder for chaining.
     */
    public Builder setStrength(int value) {

      strength_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 strength = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearStrength() {
      bitField0_ = (bitField0_ & ~0x00000020);
      strength_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object fightValue_ = "";
    /**
     * <code>optional string fightValue = 7;</code>
     * @return Whether the fightValue field is set.
     */
    public boolean hasFightValue() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <code>optional string fightValue = 7;</code>
     * @return The fightValue.
     */
    public java.lang.String getFightValue() {
      java.lang.Object ref = fightValue_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          fightValue_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string fightValue = 7;</code>
     * @return The bytes for fightValue.
     */
    public com.google.protobuf.ByteString
        getFightValueBytes() {
      java.lang.Object ref = fightValue_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        fightValue_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string fightValue = 7;</code>
     * @param value The fightValue to set.
     * @return This builder for chaining.
     */
    public Builder setFightValue(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      fightValue_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>optional string fightValue = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearFightValue() {
      fightValue_ = getDefaultInstance().getFightValue();
      bitField0_ = (bitField0_ & ~0x00000040);
      onChanged();
      return this;
    }
    /**
     * <code>optional string fightValue = 7;</code>
     * @param value The bytes for fightValue to set.
     * @return This builder for chaining.
     */
    public Builder setFightValueBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      fightValue_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }

    private long lastRecoveryTime_ ;
    /**
     * <code>optional int64 lastRecoveryTime = 8;</code>
     * @return Whether the lastRecoveryTime field is set.
     */
    @java.lang.Override
    public boolean hasLastRecoveryTime() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <code>optional int64 lastRecoveryTime = 8;</code>
     * @return The lastRecoveryTime.
     */
    @java.lang.Override
    public long getLastRecoveryTime() {
      return lastRecoveryTime_;
    }
    /**
     * <code>optional int64 lastRecoveryTime = 8;</code>
     * @param value The lastRecoveryTime to set.
     * @return This builder for chaining.
     */
    public Builder setLastRecoveryTime(long value) {

      lastRecoveryTime_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 lastRecoveryTime = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearLastRecoveryTime() {
      bitField0_ = (bitField0_ & ~0x00000080);
      lastRecoveryTime_ = 0L;
      onChanged();
      return this;
    }

    private long trapEndTime_ ;
    /**
     * <code>optional int64 trapEndTime = 9;</code>
     * @return Whether the trapEndTime field is set.
     */
    @java.lang.Override
    public boolean hasTrapEndTime() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <code>optional int64 trapEndTime = 9;</code>
     * @return The trapEndTime.
     */
    @java.lang.Override
    public long getTrapEndTime() {
      return trapEndTime_;
    }
    /**
     * <code>optional int64 trapEndTime = 9;</code>
     * @param value The trapEndTime to set.
     * @return This builder for chaining.
     */
    public Builder setTrapEndTime(long value) {

      trapEndTime_ = value;
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 trapEndTime = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearTrapEndTime() {
      bitField0_ = (bitField0_ & ~0x00000100);
      trapEndTime_ = 0L;
      onChanged();
      return this;
    }

    private int doubleRewardCount_ ;
    /**
     * <code>optional int32 doubleRewardCount = 10;</code>
     * @return Whether the doubleRewardCount field is set.
     */
    @java.lang.Override
    public boolean hasDoubleRewardCount() {
      return ((bitField0_ & 0x00000200) != 0);
    }
    /**
     * <code>optional int32 doubleRewardCount = 10;</code>
     * @return The doubleRewardCount.
     */
    @java.lang.Override
    public int getDoubleRewardCount() {
      return doubleRewardCount_;
    }
    /**
     * <code>optional int32 doubleRewardCount = 10;</code>
     * @param value The doubleRewardCount to set.
     * @return This builder for chaining.
     */
    public Builder setDoubleRewardCount(int value) {

      doubleRewardCount_ = value;
      bitField0_ |= 0x00000200;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 doubleRewardCount = 10;</code>
     * @return This builder for chaining.
     */
    public Builder clearDoubleRewardCount() {
      bitField0_ = (bitField0_ & ~0x00000200);
      doubleRewardCount_ = 0;
      onChanged();
      return this;
    }

    private int score_ ;
    /**
     * <code>optional int32 score = 11;</code>
     * @return Whether the score field is set.
     */
    @java.lang.Override
    public boolean hasScore() {
      return ((bitField0_ & 0x00000400) != 0);
    }
    /**
     * <code>optional int32 score = 11;</code>
     * @return The score.
     */
    @java.lang.Override
    public int getScore() {
      return score_;
    }
    /**
     * <code>optional int32 score = 11;</code>
     * @param value The score to set.
     * @return This builder for chaining.
     */
    public Builder setScore(int value) {

      score_ = value;
      bitField0_ |= 0x00000400;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 score = 11;</code>
     * @return This builder for chaining.
     */
    public Builder clearScore() {
      bitField0_ = (bitField0_ & ~0x00000400);
      score_ = 0;
      onChanged();
      return this;
    }

    private int totalCostStrength_ ;
    /**
     * <code>optional int32 totalCostStrength = 12;</code>
     * @return Whether the totalCostStrength field is set.
     */
    @java.lang.Override
    public boolean hasTotalCostStrength() {
      return ((bitField0_ & 0x00000800) != 0);
    }
    /**
     * <code>optional int32 totalCostStrength = 12;</code>
     * @return The totalCostStrength.
     */
    @java.lang.Override
    public int getTotalCostStrength() {
      return totalCostStrength_;
    }
    /**
     * <code>optional int32 totalCostStrength = 12;</code>
     * @param value The totalCostStrength to set.
     * @return This builder for chaining.
     */
    public Builder setTotalCostStrength(int value) {

      totalCostStrength_ = value;
      bitField0_ |= 0x00000800;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 totalCostStrength = 12;</code>
     * @return This builder for chaining.
     */
    public Builder clearTotalCostStrength() {
      bitField0_ = (bitField0_ & ~0x00000800);
      totalCostStrength_ = 0;
      onChanged();
      return this;
    }

    private int triggerMoveBuff_ ;
    /**
     * <code>optional int32 triggerMoveBuff = 13;</code>
     * @return Whether the triggerMoveBuff field is set.
     */
    @java.lang.Override
    public boolean hasTriggerMoveBuff() {
      return ((bitField0_ & 0x00001000) != 0);
    }
    /**
     * <code>optional int32 triggerMoveBuff = 13;</code>
     * @return The triggerMoveBuff.
     */
    @java.lang.Override
    public int getTriggerMoveBuff() {
      return triggerMoveBuff_;
    }
    /**
     * <code>optional int32 triggerMoveBuff = 13;</code>
     * @param value The triggerMoveBuff to set.
     * @return This builder for chaining.
     */
    public Builder setTriggerMoveBuff(int value) {

      triggerMoveBuff_ = value;
      bitField0_ |= 0x00001000;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 triggerMoveBuff = 13;</code>
     * @return This builder for chaining.
     */
    public Builder clearTriggerMoveBuff() {
      bitField0_ = (bitField0_ & ~0x00001000);
      triggerMoveBuff_ = 0;
      onChanged();
      return this;
    }

    private boolean needMove_ ;
    /**
     * <code>optional bool needMove = 14;</code>
     * @return Whether the needMove field is set.
     */
    @java.lang.Override
    public boolean hasNeedMove() {
      return ((bitField0_ & 0x00002000) != 0);
    }
    /**
     * <code>optional bool needMove = 14;</code>
     * @return The needMove.
     */
    @java.lang.Override
    public boolean getNeedMove() {
      return needMove_;
    }
    /**
     * <code>optional bool needMove = 14;</code>
     * @param value The needMove to set.
     * @return This builder for chaining.
     */
    public Builder setNeedMove(boolean value) {

      needMove_ = value;
      bitField0_ |= 0x00002000;
      onChanged();
      return this;
    }
    /**
     * <code>optional bool needMove = 14;</code>
     * @return This builder for chaining.
     */
    public Builder clearNeedMove() {
      bitField0_ = (bitField0_ & ~0x00002000);
      needMove_ = false;
      onChanged();
      return this;
    }

    private int title_ ;
    /**
     * <code>optional int32 title = 15;</code>
     * @return Whether the title field is set.
     */
    @java.lang.Override
    public boolean hasTitle() {
      return ((bitField0_ & 0x00004000) != 0);
    }
    /**
     * <code>optional int32 title = 15;</code>
     * @return The title.
     */
    @java.lang.Override
    public int getTitle() {
      return title_;
    }
    /**
     * <code>optional int32 title = 15;</code>
     * @param value The title to set.
     * @return This builder for chaining.
     */
    public Builder setTitle(int value) {

      title_ = value;
      bitField0_ |= 0x00004000;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 title = 15;</code>
     * @return This builder for chaining.
     */
    public Builder clearTitle() {
      bitField0_ = (bitField0_ & ~0x00004000);
      title_ = 0;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.MonopolySelfPlayerDataMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.MonopolySelfPlayerDataMsg)
  private static final xddq.pb.MonopolySelfPlayerDataMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.MonopolySelfPlayerDataMsg();
  }

  public static xddq.pb.MonopolySelfPlayerDataMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<MonopolySelfPlayerDataMsg>
      PARSER = new com.google.protobuf.AbstractParser<MonopolySelfPlayerDataMsg>() {
    @java.lang.Override
    public MonopolySelfPlayerDataMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<MonopolySelfPlayerDataMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<MonopolySelfPlayerDataMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.MonopolySelfPlayerDataMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

