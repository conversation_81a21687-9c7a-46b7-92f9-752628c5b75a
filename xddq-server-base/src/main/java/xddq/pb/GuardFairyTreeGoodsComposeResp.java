// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.GuardFairyTreeGoodsComposeResp}
 */
public final class GuardFairyTreeGoodsComposeResp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.GuardFairyTreeGoodsComposeResp)
    GuardFairyTreeGoodsComposeRespOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      GuardFairyTreeGoodsComposeResp.class.getName());
  }
  // Use GuardFairyTreeGoodsComposeResp.newBuilder() to construct.
  private GuardFairyTreeGoodsComposeResp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private GuardFairyTreeGoodsComposeResp() {
    plateGoods_ = java.util.Collections.emptyList();
    shopGoods_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_GuardFairyTreeGoodsComposeResp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_GuardFairyTreeGoodsComposeResp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.GuardFairyTreeGoodsComposeResp.class, xddq.pb.GuardFairyTreeGoodsComposeResp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int PLATEGOODS_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.GuardFairyTreeGoodsItemInfo> plateGoods_;
  /**
   * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo plateGoods = 2;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.GuardFairyTreeGoodsItemInfo> getPlateGoodsList() {
    return plateGoods_;
  }
  /**
   * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo plateGoods = 2;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.GuardFairyTreeGoodsItemInfoOrBuilder> 
      getPlateGoodsOrBuilderList() {
    return plateGoods_;
  }
  /**
   * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo plateGoods = 2;</code>
   */
  @java.lang.Override
  public int getPlateGoodsCount() {
    return plateGoods_.size();
  }
  /**
   * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo plateGoods = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.GuardFairyTreeGoodsItemInfo getPlateGoods(int index) {
    return plateGoods_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo plateGoods = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.GuardFairyTreeGoodsItemInfoOrBuilder getPlateGoodsOrBuilder(
      int index) {
    return plateGoods_.get(index);
  }

  public static final int SHOPGOODS_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.GuardFairyTreeGoodsItemInfo> shopGoods_;
  /**
   * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo shopGoods = 3;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.GuardFairyTreeGoodsItemInfo> getShopGoodsList() {
    return shopGoods_;
  }
  /**
   * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo shopGoods = 3;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.GuardFairyTreeGoodsItemInfoOrBuilder> 
      getShopGoodsOrBuilderList() {
    return shopGoods_;
  }
  /**
   * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo shopGoods = 3;</code>
   */
  @java.lang.Override
  public int getShopGoodsCount() {
    return shopGoods_.size();
  }
  /**
   * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo shopGoods = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.GuardFairyTreeGoodsItemInfo getShopGoods(int index) {
    return shopGoods_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo shopGoods = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.GuardFairyTreeGoodsItemInfoOrBuilder getShopGoodsOrBuilder(
      int index) {
    return shopGoods_.get(index);
  }

  public static final int SAVEGOOD_FIELD_NUMBER = 4;
  private xddq.pb.GuardFairyTreeGoodsItemInfo saveGood_;
  /**
   * <code>optional .xddq.pb.GuardFairyTreeGoodsItemInfo saveGood = 4;</code>
   * @return Whether the saveGood field is set.
   */
  @java.lang.Override
  public boolean hasSaveGood() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional .xddq.pb.GuardFairyTreeGoodsItemInfo saveGood = 4;</code>
   * @return The saveGood.
   */
  @java.lang.Override
  public xddq.pb.GuardFairyTreeGoodsItemInfo getSaveGood() {
    return saveGood_ == null ? xddq.pb.GuardFairyTreeGoodsItemInfo.getDefaultInstance() : saveGood_;
  }
  /**
   * <code>optional .xddq.pb.GuardFairyTreeGoodsItemInfo saveGood = 4;</code>
   */
  @java.lang.Override
  public xddq.pb.GuardFairyTreeGoodsItemInfoOrBuilder getSaveGoodOrBuilder() {
    return saveGood_ == null ? xddq.pb.GuardFairyTreeGoodsItemInfo.getDefaultInstance() : saveGood_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    for (int i = 0; i < getPlateGoodsCount(); i++) {
      if (!getPlateGoods(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    for (int i = 0; i < getShopGoodsCount(); i++) {
      if (!getShopGoods(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    if (hasSaveGood()) {
      if (!getSaveGood().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    for (int i = 0; i < plateGoods_.size(); i++) {
      output.writeMessage(2, plateGoods_.get(i));
    }
    for (int i = 0; i < shopGoods_.size(); i++) {
      output.writeMessage(3, shopGoods_.get(i));
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeMessage(4, getSaveGood());
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    for (int i = 0; i < plateGoods_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, plateGoods_.get(i));
    }
    for (int i = 0; i < shopGoods_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, shopGoods_.get(i));
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(4, getSaveGood());
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.GuardFairyTreeGoodsComposeResp)) {
      return super.equals(obj);
    }
    xddq.pb.GuardFairyTreeGoodsComposeResp other = (xddq.pb.GuardFairyTreeGoodsComposeResp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (!getPlateGoodsList()
        .equals(other.getPlateGoodsList())) return false;
    if (!getShopGoodsList()
        .equals(other.getShopGoodsList())) return false;
    if (hasSaveGood() != other.hasSaveGood()) return false;
    if (hasSaveGood()) {
      if (!getSaveGood()
          .equals(other.getSaveGood())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (getPlateGoodsCount() > 0) {
      hash = (37 * hash) + PLATEGOODS_FIELD_NUMBER;
      hash = (53 * hash) + getPlateGoodsList().hashCode();
    }
    if (getShopGoodsCount() > 0) {
      hash = (37 * hash) + SHOPGOODS_FIELD_NUMBER;
      hash = (53 * hash) + getShopGoodsList().hashCode();
    }
    if (hasSaveGood()) {
      hash = (37 * hash) + SAVEGOOD_FIELD_NUMBER;
      hash = (53 * hash) + getSaveGood().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.GuardFairyTreeGoodsComposeResp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GuardFairyTreeGoodsComposeResp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GuardFairyTreeGoodsComposeResp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GuardFairyTreeGoodsComposeResp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GuardFairyTreeGoodsComposeResp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GuardFairyTreeGoodsComposeResp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GuardFairyTreeGoodsComposeResp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.GuardFairyTreeGoodsComposeResp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.GuardFairyTreeGoodsComposeResp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.GuardFairyTreeGoodsComposeResp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.GuardFairyTreeGoodsComposeResp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.GuardFairyTreeGoodsComposeResp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.GuardFairyTreeGoodsComposeResp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.GuardFairyTreeGoodsComposeResp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.GuardFairyTreeGoodsComposeResp)
      xddq.pb.GuardFairyTreeGoodsComposeRespOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GuardFairyTreeGoodsComposeResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GuardFairyTreeGoodsComposeResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.GuardFairyTreeGoodsComposeResp.class, xddq.pb.GuardFairyTreeGoodsComposeResp.Builder.class);
    }

    // Construct using xddq.pb.GuardFairyTreeGoodsComposeResp.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetPlateGoodsFieldBuilder();
        internalGetShopGoodsFieldBuilder();
        internalGetSaveGoodFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      if (plateGoodsBuilder_ == null) {
        plateGoods_ = java.util.Collections.emptyList();
      } else {
        plateGoods_ = null;
        plateGoodsBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000002);
      if (shopGoodsBuilder_ == null) {
        shopGoods_ = java.util.Collections.emptyList();
      } else {
        shopGoods_ = null;
        shopGoodsBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000004);
      saveGood_ = null;
      if (saveGoodBuilder_ != null) {
        saveGoodBuilder_.dispose();
        saveGoodBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GuardFairyTreeGoodsComposeResp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.GuardFairyTreeGoodsComposeResp getDefaultInstanceForType() {
      return xddq.pb.GuardFairyTreeGoodsComposeResp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.GuardFairyTreeGoodsComposeResp build() {
      xddq.pb.GuardFairyTreeGoodsComposeResp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.GuardFairyTreeGoodsComposeResp buildPartial() {
      xddq.pb.GuardFairyTreeGoodsComposeResp result = new xddq.pb.GuardFairyTreeGoodsComposeResp(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.GuardFairyTreeGoodsComposeResp result) {
      if (plateGoodsBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          plateGoods_ = java.util.Collections.unmodifiableList(plateGoods_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.plateGoods_ = plateGoods_;
      } else {
        result.plateGoods_ = plateGoodsBuilder_.build();
      }
      if (shopGoodsBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0)) {
          shopGoods_ = java.util.Collections.unmodifiableList(shopGoods_);
          bitField0_ = (bitField0_ & ~0x00000004);
        }
        result.shopGoods_ = shopGoods_;
      } else {
        result.shopGoods_ = shopGoodsBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.GuardFairyTreeGoodsComposeResp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.saveGood_ = saveGoodBuilder_ == null
            ? saveGood_
            : saveGoodBuilder_.build();
        to_bitField0_ |= 0x00000002;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.GuardFairyTreeGoodsComposeResp) {
        return mergeFrom((xddq.pb.GuardFairyTreeGoodsComposeResp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.GuardFairyTreeGoodsComposeResp other) {
      if (other == xddq.pb.GuardFairyTreeGoodsComposeResp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (plateGoodsBuilder_ == null) {
        if (!other.plateGoods_.isEmpty()) {
          if (plateGoods_.isEmpty()) {
            plateGoods_ = other.plateGoods_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensurePlateGoodsIsMutable();
            plateGoods_.addAll(other.plateGoods_);
          }
          onChanged();
        }
      } else {
        if (!other.plateGoods_.isEmpty()) {
          if (plateGoodsBuilder_.isEmpty()) {
            plateGoodsBuilder_.dispose();
            plateGoodsBuilder_ = null;
            plateGoods_ = other.plateGoods_;
            bitField0_ = (bitField0_ & ~0x00000002);
            plateGoodsBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetPlateGoodsFieldBuilder() : null;
          } else {
            plateGoodsBuilder_.addAllMessages(other.plateGoods_);
          }
        }
      }
      if (shopGoodsBuilder_ == null) {
        if (!other.shopGoods_.isEmpty()) {
          if (shopGoods_.isEmpty()) {
            shopGoods_ = other.shopGoods_;
            bitField0_ = (bitField0_ & ~0x00000004);
          } else {
            ensureShopGoodsIsMutable();
            shopGoods_.addAll(other.shopGoods_);
          }
          onChanged();
        }
      } else {
        if (!other.shopGoods_.isEmpty()) {
          if (shopGoodsBuilder_.isEmpty()) {
            shopGoodsBuilder_.dispose();
            shopGoodsBuilder_ = null;
            shopGoods_ = other.shopGoods_;
            bitField0_ = (bitField0_ & ~0x00000004);
            shopGoodsBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetShopGoodsFieldBuilder() : null;
          } else {
            shopGoodsBuilder_.addAllMessages(other.shopGoods_);
          }
        }
      }
      if (other.hasSaveGood()) {
        mergeSaveGood(other.getSaveGood());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      for (int i = 0; i < getPlateGoodsCount(); i++) {
        if (!getPlateGoods(i).isInitialized()) {
          return false;
        }
      }
      for (int i = 0; i < getShopGoodsCount(); i++) {
        if (!getShopGoods(i).isInitialized()) {
          return false;
        }
      }
      if (hasSaveGood()) {
        if (!getSaveGood().isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              xddq.pb.GuardFairyTreeGoodsItemInfo m =
                  input.readMessage(
                      xddq.pb.GuardFairyTreeGoodsItemInfo.parser(),
                      extensionRegistry);
              if (plateGoodsBuilder_ == null) {
                ensurePlateGoodsIsMutable();
                plateGoods_.add(m);
              } else {
                plateGoodsBuilder_.addMessage(m);
              }
              break;
            } // case 18
            case 26: {
              xddq.pb.GuardFairyTreeGoodsItemInfo m =
                  input.readMessage(
                      xddq.pb.GuardFairyTreeGoodsItemInfo.parser(),
                      extensionRegistry);
              if (shopGoodsBuilder_ == null) {
                ensureShopGoodsIsMutable();
                shopGoods_.add(m);
              } else {
                shopGoodsBuilder_.addMessage(m);
              }
              break;
            } // case 26
            case 34: {
              input.readMessage(
                  internalGetSaveGoodFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000008;
              break;
            } // case 34
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.GuardFairyTreeGoodsItemInfo> plateGoods_ =
      java.util.Collections.emptyList();
    private void ensurePlateGoodsIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        plateGoods_ = new java.util.ArrayList<xddq.pb.GuardFairyTreeGoodsItemInfo>(plateGoods_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.GuardFairyTreeGoodsItemInfo, xddq.pb.GuardFairyTreeGoodsItemInfo.Builder, xddq.pb.GuardFairyTreeGoodsItemInfoOrBuilder> plateGoodsBuilder_;

    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo plateGoods = 2;</code>
     */
    public java.util.List<xddq.pb.GuardFairyTreeGoodsItemInfo> getPlateGoodsList() {
      if (plateGoodsBuilder_ == null) {
        return java.util.Collections.unmodifiableList(plateGoods_);
      } else {
        return plateGoodsBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo plateGoods = 2;</code>
     */
    public int getPlateGoodsCount() {
      if (plateGoodsBuilder_ == null) {
        return plateGoods_.size();
      } else {
        return plateGoodsBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo plateGoods = 2;</code>
     */
    public xddq.pb.GuardFairyTreeGoodsItemInfo getPlateGoods(int index) {
      if (plateGoodsBuilder_ == null) {
        return plateGoods_.get(index);
      } else {
        return plateGoodsBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo plateGoods = 2;</code>
     */
    public Builder setPlateGoods(
        int index, xddq.pb.GuardFairyTreeGoodsItemInfo value) {
      if (plateGoodsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePlateGoodsIsMutable();
        plateGoods_.set(index, value);
        onChanged();
      } else {
        plateGoodsBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo plateGoods = 2;</code>
     */
    public Builder setPlateGoods(
        int index, xddq.pb.GuardFairyTreeGoodsItemInfo.Builder builderForValue) {
      if (plateGoodsBuilder_ == null) {
        ensurePlateGoodsIsMutable();
        plateGoods_.set(index, builderForValue.build());
        onChanged();
      } else {
        plateGoodsBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo plateGoods = 2;</code>
     */
    public Builder addPlateGoods(xddq.pb.GuardFairyTreeGoodsItemInfo value) {
      if (plateGoodsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePlateGoodsIsMutable();
        plateGoods_.add(value);
        onChanged();
      } else {
        plateGoodsBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo plateGoods = 2;</code>
     */
    public Builder addPlateGoods(
        int index, xddq.pb.GuardFairyTreeGoodsItemInfo value) {
      if (plateGoodsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePlateGoodsIsMutable();
        plateGoods_.add(index, value);
        onChanged();
      } else {
        plateGoodsBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo plateGoods = 2;</code>
     */
    public Builder addPlateGoods(
        xddq.pb.GuardFairyTreeGoodsItemInfo.Builder builderForValue) {
      if (plateGoodsBuilder_ == null) {
        ensurePlateGoodsIsMutable();
        plateGoods_.add(builderForValue.build());
        onChanged();
      } else {
        plateGoodsBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo plateGoods = 2;</code>
     */
    public Builder addPlateGoods(
        int index, xddq.pb.GuardFairyTreeGoodsItemInfo.Builder builderForValue) {
      if (plateGoodsBuilder_ == null) {
        ensurePlateGoodsIsMutable();
        plateGoods_.add(index, builderForValue.build());
        onChanged();
      } else {
        plateGoodsBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo plateGoods = 2;</code>
     */
    public Builder addAllPlateGoods(
        java.lang.Iterable<? extends xddq.pb.GuardFairyTreeGoodsItemInfo> values) {
      if (plateGoodsBuilder_ == null) {
        ensurePlateGoodsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, plateGoods_);
        onChanged();
      } else {
        plateGoodsBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo plateGoods = 2;</code>
     */
    public Builder clearPlateGoods() {
      if (plateGoodsBuilder_ == null) {
        plateGoods_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        plateGoodsBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo plateGoods = 2;</code>
     */
    public Builder removePlateGoods(int index) {
      if (plateGoodsBuilder_ == null) {
        ensurePlateGoodsIsMutable();
        plateGoods_.remove(index);
        onChanged();
      } else {
        plateGoodsBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo plateGoods = 2;</code>
     */
    public xddq.pb.GuardFairyTreeGoodsItemInfo.Builder getPlateGoodsBuilder(
        int index) {
      return internalGetPlateGoodsFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo plateGoods = 2;</code>
     */
    public xddq.pb.GuardFairyTreeGoodsItemInfoOrBuilder getPlateGoodsOrBuilder(
        int index) {
      if (plateGoodsBuilder_ == null) {
        return plateGoods_.get(index);  } else {
        return plateGoodsBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo plateGoods = 2;</code>
     */
    public java.util.List<? extends xddq.pb.GuardFairyTreeGoodsItemInfoOrBuilder> 
         getPlateGoodsOrBuilderList() {
      if (plateGoodsBuilder_ != null) {
        return plateGoodsBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(plateGoods_);
      }
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo plateGoods = 2;</code>
     */
    public xddq.pb.GuardFairyTreeGoodsItemInfo.Builder addPlateGoodsBuilder() {
      return internalGetPlateGoodsFieldBuilder().addBuilder(
          xddq.pb.GuardFairyTreeGoodsItemInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo plateGoods = 2;</code>
     */
    public xddq.pb.GuardFairyTreeGoodsItemInfo.Builder addPlateGoodsBuilder(
        int index) {
      return internalGetPlateGoodsFieldBuilder().addBuilder(
          index, xddq.pb.GuardFairyTreeGoodsItemInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo plateGoods = 2;</code>
     */
    public java.util.List<xddq.pb.GuardFairyTreeGoodsItemInfo.Builder> 
         getPlateGoodsBuilderList() {
      return internalGetPlateGoodsFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.GuardFairyTreeGoodsItemInfo, xddq.pb.GuardFairyTreeGoodsItemInfo.Builder, xddq.pb.GuardFairyTreeGoodsItemInfoOrBuilder> 
        internalGetPlateGoodsFieldBuilder() {
      if (plateGoodsBuilder_ == null) {
        plateGoodsBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.GuardFairyTreeGoodsItemInfo, xddq.pb.GuardFairyTreeGoodsItemInfo.Builder, xddq.pb.GuardFairyTreeGoodsItemInfoOrBuilder>(
                plateGoods_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        plateGoods_ = null;
      }
      return plateGoodsBuilder_;
    }

    private java.util.List<xddq.pb.GuardFairyTreeGoodsItemInfo> shopGoods_ =
      java.util.Collections.emptyList();
    private void ensureShopGoodsIsMutable() {
      if (!((bitField0_ & 0x00000004) != 0)) {
        shopGoods_ = new java.util.ArrayList<xddq.pb.GuardFairyTreeGoodsItemInfo>(shopGoods_);
        bitField0_ |= 0x00000004;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.GuardFairyTreeGoodsItemInfo, xddq.pb.GuardFairyTreeGoodsItemInfo.Builder, xddq.pb.GuardFairyTreeGoodsItemInfoOrBuilder> shopGoodsBuilder_;

    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo shopGoods = 3;</code>
     */
    public java.util.List<xddq.pb.GuardFairyTreeGoodsItemInfo> getShopGoodsList() {
      if (shopGoodsBuilder_ == null) {
        return java.util.Collections.unmodifiableList(shopGoods_);
      } else {
        return shopGoodsBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo shopGoods = 3;</code>
     */
    public int getShopGoodsCount() {
      if (shopGoodsBuilder_ == null) {
        return shopGoods_.size();
      } else {
        return shopGoodsBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo shopGoods = 3;</code>
     */
    public xddq.pb.GuardFairyTreeGoodsItemInfo getShopGoods(int index) {
      if (shopGoodsBuilder_ == null) {
        return shopGoods_.get(index);
      } else {
        return shopGoodsBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo shopGoods = 3;</code>
     */
    public Builder setShopGoods(
        int index, xddq.pb.GuardFairyTreeGoodsItemInfo value) {
      if (shopGoodsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureShopGoodsIsMutable();
        shopGoods_.set(index, value);
        onChanged();
      } else {
        shopGoodsBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo shopGoods = 3;</code>
     */
    public Builder setShopGoods(
        int index, xddq.pb.GuardFairyTreeGoodsItemInfo.Builder builderForValue) {
      if (shopGoodsBuilder_ == null) {
        ensureShopGoodsIsMutable();
        shopGoods_.set(index, builderForValue.build());
        onChanged();
      } else {
        shopGoodsBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo shopGoods = 3;</code>
     */
    public Builder addShopGoods(xddq.pb.GuardFairyTreeGoodsItemInfo value) {
      if (shopGoodsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureShopGoodsIsMutable();
        shopGoods_.add(value);
        onChanged();
      } else {
        shopGoodsBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo shopGoods = 3;</code>
     */
    public Builder addShopGoods(
        int index, xddq.pb.GuardFairyTreeGoodsItemInfo value) {
      if (shopGoodsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureShopGoodsIsMutable();
        shopGoods_.add(index, value);
        onChanged();
      } else {
        shopGoodsBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo shopGoods = 3;</code>
     */
    public Builder addShopGoods(
        xddq.pb.GuardFairyTreeGoodsItemInfo.Builder builderForValue) {
      if (shopGoodsBuilder_ == null) {
        ensureShopGoodsIsMutable();
        shopGoods_.add(builderForValue.build());
        onChanged();
      } else {
        shopGoodsBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo shopGoods = 3;</code>
     */
    public Builder addShopGoods(
        int index, xddq.pb.GuardFairyTreeGoodsItemInfo.Builder builderForValue) {
      if (shopGoodsBuilder_ == null) {
        ensureShopGoodsIsMutable();
        shopGoods_.add(index, builderForValue.build());
        onChanged();
      } else {
        shopGoodsBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo shopGoods = 3;</code>
     */
    public Builder addAllShopGoods(
        java.lang.Iterable<? extends xddq.pb.GuardFairyTreeGoodsItemInfo> values) {
      if (shopGoodsBuilder_ == null) {
        ensureShopGoodsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, shopGoods_);
        onChanged();
      } else {
        shopGoodsBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo shopGoods = 3;</code>
     */
    public Builder clearShopGoods() {
      if (shopGoodsBuilder_ == null) {
        shopGoods_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
      } else {
        shopGoodsBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo shopGoods = 3;</code>
     */
    public Builder removeShopGoods(int index) {
      if (shopGoodsBuilder_ == null) {
        ensureShopGoodsIsMutable();
        shopGoods_.remove(index);
        onChanged();
      } else {
        shopGoodsBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo shopGoods = 3;</code>
     */
    public xddq.pb.GuardFairyTreeGoodsItemInfo.Builder getShopGoodsBuilder(
        int index) {
      return internalGetShopGoodsFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo shopGoods = 3;</code>
     */
    public xddq.pb.GuardFairyTreeGoodsItemInfoOrBuilder getShopGoodsOrBuilder(
        int index) {
      if (shopGoodsBuilder_ == null) {
        return shopGoods_.get(index);  } else {
        return shopGoodsBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo shopGoods = 3;</code>
     */
    public java.util.List<? extends xddq.pb.GuardFairyTreeGoodsItemInfoOrBuilder> 
         getShopGoodsOrBuilderList() {
      if (shopGoodsBuilder_ != null) {
        return shopGoodsBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(shopGoods_);
      }
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo shopGoods = 3;</code>
     */
    public xddq.pb.GuardFairyTreeGoodsItemInfo.Builder addShopGoodsBuilder() {
      return internalGetShopGoodsFieldBuilder().addBuilder(
          xddq.pb.GuardFairyTreeGoodsItemInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo shopGoods = 3;</code>
     */
    public xddq.pb.GuardFairyTreeGoodsItemInfo.Builder addShopGoodsBuilder(
        int index) {
      return internalGetShopGoodsFieldBuilder().addBuilder(
          index, xddq.pb.GuardFairyTreeGoodsItemInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.GuardFairyTreeGoodsItemInfo shopGoods = 3;</code>
     */
    public java.util.List<xddq.pb.GuardFairyTreeGoodsItemInfo.Builder> 
         getShopGoodsBuilderList() {
      return internalGetShopGoodsFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.GuardFairyTreeGoodsItemInfo, xddq.pb.GuardFairyTreeGoodsItemInfo.Builder, xddq.pb.GuardFairyTreeGoodsItemInfoOrBuilder> 
        internalGetShopGoodsFieldBuilder() {
      if (shopGoodsBuilder_ == null) {
        shopGoodsBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.GuardFairyTreeGoodsItemInfo, xddq.pb.GuardFairyTreeGoodsItemInfo.Builder, xddq.pb.GuardFairyTreeGoodsItemInfoOrBuilder>(
                shopGoods_,
                ((bitField0_ & 0x00000004) != 0),
                getParentForChildren(),
                isClean());
        shopGoods_ = null;
      }
      return shopGoodsBuilder_;
    }

    private xddq.pb.GuardFairyTreeGoodsItemInfo saveGood_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.GuardFairyTreeGoodsItemInfo, xddq.pb.GuardFairyTreeGoodsItemInfo.Builder, xddq.pb.GuardFairyTreeGoodsItemInfoOrBuilder> saveGoodBuilder_;
    /**
     * <code>optional .xddq.pb.GuardFairyTreeGoodsItemInfo saveGood = 4;</code>
     * @return Whether the saveGood field is set.
     */
    public boolean hasSaveGood() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional .xddq.pb.GuardFairyTreeGoodsItemInfo saveGood = 4;</code>
     * @return The saveGood.
     */
    public xddq.pb.GuardFairyTreeGoodsItemInfo getSaveGood() {
      if (saveGoodBuilder_ == null) {
        return saveGood_ == null ? xddq.pb.GuardFairyTreeGoodsItemInfo.getDefaultInstance() : saveGood_;
      } else {
        return saveGoodBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.GuardFairyTreeGoodsItemInfo saveGood = 4;</code>
     */
    public Builder setSaveGood(xddq.pb.GuardFairyTreeGoodsItemInfo value) {
      if (saveGoodBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        saveGood_ = value;
      } else {
        saveGoodBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.GuardFairyTreeGoodsItemInfo saveGood = 4;</code>
     */
    public Builder setSaveGood(
        xddq.pb.GuardFairyTreeGoodsItemInfo.Builder builderForValue) {
      if (saveGoodBuilder_ == null) {
        saveGood_ = builderForValue.build();
      } else {
        saveGoodBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.GuardFairyTreeGoodsItemInfo saveGood = 4;</code>
     */
    public Builder mergeSaveGood(xddq.pb.GuardFairyTreeGoodsItemInfo value) {
      if (saveGoodBuilder_ == null) {
        if (((bitField0_ & 0x00000008) != 0) &&
          saveGood_ != null &&
          saveGood_ != xddq.pb.GuardFairyTreeGoodsItemInfo.getDefaultInstance()) {
          getSaveGoodBuilder().mergeFrom(value);
        } else {
          saveGood_ = value;
        }
      } else {
        saveGoodBuilder_.mergeFrom(value);
      }
      if (saveGood_ != null) {
        bitField0_ |= 0x00000008;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.GuardFairyTreeGoodsItemInfo saveGood = 4;</code>
     */
    public Builder clearSaveGood() {
      bitField0_ = (bitField0_ & ~0x00000008);
      saveGood_ = null;
      if (saveGoodBuilder_ != null) {
        saveGoodBuilder_.dispose();
        saveGoodBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.GuardFairyTreeGoodsItemInfo saveGood = 4;</code>
     */
    public xddq.pb.GuardFairyTreeGoodsItemInfo.Builder getSaveGoodBuilder() {
      bitField0_ |= 0x00000008;
      onChanged();
      return internalGetSaveGoodFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.GuardFairyTreeGoodsItemInfo saveGood = 4;</code>
     */
    public xddq.pb.GuardFairyTreeGoodsItemInfoOrBuilder getSaveGoodOrBuilder() {
      if (saveGoodBuilder_ != null) {
        return saveGoodBuilder_.getMessageOrBuilder();
      } else {
        return saveGood_ == null ?
            xddq.pb.GuardFairyTreeGoodsItemInfo.getDefaultInstance() : saveGood_;
      }
    }
    /**
     * <code>optional .xddq.pb.GuardFairyTreeGoodsItemInfo saveGood = 4;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.GuardFairyTreeGoodsItemInfo, xddq.pb.GuardFairyTreeGoodsItemInfo.Builder, xddq.pb.GuardFairyTreeGoodsItemInfoOrBuilder> 
        internalGetSaveGoodFieldBuilder() {
      if (saveGoodBuilder_ == null) {
        saveGoodBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.GuardFairyTreeGoodsItemInfo, xddq.pb.GuardFairyTreeGoodsItemInfo.Builder, xddq.pb.GuardFairyTreeGoodsItemInfoOrBuilder>(
                getSaveGood(),
                getParentForChildren(),
                isClean());
        saveGood_ = null;
      }
      return saveGoodBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.GuardFairyTreeGoodsComposeResp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.GuardFairyTreeGoodsComposeResp)
  private static final xddq.pb.GuardFairyTreeGoodsComposeResp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.GuardFairyTreeGoodsComposeResp();
  }

  public static xddq.pb.GuardFairyTreeGoodsComposeResp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<GuardFairyTreeGoodsComposeResp>
      PARSER = new com.google.protobuf.AbstractParser<GuardFairyTreeGoodsComposeResp>() {
    @java.lang.Override
    public GuardFairyTreeGoodsComposeResp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<GuardFairyTreeGoodsComposeResp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<GuardFairyTreeGoodsComposeResp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.GuardFairyTreeGoodsComposeResp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

