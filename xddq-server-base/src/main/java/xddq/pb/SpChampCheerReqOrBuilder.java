// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

public interface SpChampCheerReqOrBuilder extends
    // @@protoc_insertion_point(interface_extends:xddq.pb.SpChampCheerReq)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>required int32 activityId = 1;</code>
   * @return Whether the activityId field is set.
   */
  boolean hasActivityId();
  /**
   * <code>required int32 activityId = 1;</code>
   * @return The activityId.
   */
  int getActivityId();

  /**
   * <code>required int64 playerId = 2;</code>
   * @return Whether the playerId field is set.
   */
  boolean hasPlayerId();
  /**
   * <code>required int64 playerId = 2;</code>
   * @return The playerId.
   */
  long getPlayerId();
}
