// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

public interface YardBuildUpDataOrBuilder extends
    // @@protoc_insertion_point(interface_extends:xddq.pb.YardBuildUpData)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>optional int64 uniqueId = 1;</code>
   * @return Whether the uniqueId field is set.
   */
  boolean hasUniqueId();
  /**
   * <code>optional int64 uniqueId = 1;</code>
   * @return The uniqueId.
   */
  long getUniqueId();

  /**
   * <code>optional int32 curLv = 2;</code>
   * @return Whether the curLv field is set.
   */
  boolean hasCurLv();
  /**
   * <code>optional int32 curLv = 2;</code>
   * @return The curLv.
   */
  int getCurLv();
}
