// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.HeavenBattleResultSyncMsg}
 */
public final class HeavenBattleResultSyncMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.HeavenBattleResultSyncMsg)
    HeavenBattleResultSyncMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      HeavenBattleResultSyncMsg.class.getName());
  }
  // Use HeavenBattleResultSyncMsg.newBuilder() to construct.
  private HeavenBattleResultSyncMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private HeavenBattleResultSyncMsg() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_HeavenBattleResultSyncMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_HeavenBattleResultSyncMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.HeavenBattleResultSyncMsg.class, xddq.pb.HeavenBattleResultSyncMsg.Builder.class);
  }

  private int bitField0_;
  public static final int ENERGY_FIELD_NUMBER = 1;
  private int energy_ = 0;
  /**
   * <code>optional int32 energy = 1;</code>
   * @return Whether the energy field is set.
   */
  @java.lang.Override
  public boolean hasEnergy() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int32 energy = 1;</code>
   * @return The energy.
   */
  @java.lang.Override
  public int getEnergy() {
    return energy_;
  }

  public static final int LASTRECOVERYTIME_FIELD_NUMBER = 2;
  private long lastRecoveryTime_ = 0L;
  /**
   * <code>optional int64 lastRecoveryTime = 2;</code>
   * @return Whether the lastRecoveryTime field is set.
   */
  @java.lang.Override
  public boolean hasLastRecoveryTime() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int64 lastRecoveryTime = 2;</code>
   * @return The lastRecoveryTime.
   */
  @java.lang.Override
  public long getLastRecoveryTime() {
    return lastRecoveryTime_;
  }

  public static final int DAILYLITTLEBOSSCOUNT_FIELD_NUMBER = 3;
  private int dailyLittleBossCount_ = 0;
  /**
   * <code>optional int32 dailyLittleBossCount = 3;</code>
   * @return Whether the dailyLittleBossCount field is set.
   */
  @java.lang.Override
  public boolean hasDailyLittleBossCount() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional int32 dailyLittleBossCount = 3;</code>
   * @return The dailyLittleBossCount.
   */
  @java.lang.Override
  public int getDailyLittleBossCount() {
    return dailyLittleBossCount_;
  }

  public static final int DAILYBIGBOSSCOUNT_FIELD_NUMBER = 4;
  private int dailyBigBossCount_ = 0;
  /**
   * <code>optional int32 dailyBigBossCount = 4;</code>
   * @return Whether the dailyBigBossCount field is set.
   */
  @java.lang.Override
  public boolean hasDailyBigBossCount() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional int32 dailyBigBossCount = 4;</code>
   * @return The dailyBigBossCount.
   */
  @java.lang.Override
  public int getDailyBigBossCount() {
    return dailyBigBossCount_;
  }

  public static final int GRIDID_FIELD_NUMBER = 5;
  private int gridId_ = 0;
  /**
   * <code>optional int32 gridId = 5;</code>
   * @return Whether the gridId field is set.
   */
  @java.lang.Override
  public boolean hasGridId() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>optional int32 gridId = 5;</code>
   * @return The gridId.
   */
  @java.lang.Override
  public int getGridId() {
    return gridId_;
  }

  public static final int TOTALCOSTENERGY_FIELD_NUMBER = 6;
  private int totalCostEnergy_ = 0;
  /**
   * <code>optional int32 totalCostEnergy = 6;</code>
   * @return Whether the totalCostEnergy field is set.
   */
  @java.lang.Override
  public boolean hasTotalCostEnergy() {
    return ((bitField0_ & 0x00000020) != 0);
  }
  /**
   * <code>optional int32 totalCostEnergy = 6;</code>
   * @return The totalCostEnergy.
   */
  @java.lang.Override
  public int getTotalCostEnergy() {
    return totalCostEnergy_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, energy_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt64(2, lastRecoveryTime_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt32(3, dailyLittleBossCount_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt32(4, dailyBigBossCount_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      output.writeInt32(5, gridId_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      output.writeInt32(6, totalCostEnergy_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, energy_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(2, lastRecoveryTime_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(3, dailyLittleBossCount_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(4, dailyBigBossCount_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(5, gridId_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(6, totalCostEnergy_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.HeavenBattleResultSyncMsg)) {
      return super.equals(obj);
    }
    xddq.pb.HeavenBattleResultSyncMsg other = (xddq.pb.HeavenBattleResultSyncMsg) obj;

    if (hasEnergy() != other.hasEnergy()) return false;
    if (hasEnergy()) {
      if (getEnergy()
          != other.getEnergy()) return false;
    }
    if (hasLastRecoveryTime() != other.hasLastRecoveryTime()) return false;
    if (hasLastRecoveryTime()) {
      if (getLastRecoveryTime()
          != other.getLastRecoveryTime()) return false;
    }
    if (hasDailyLittleBossCount() != other.hasDailyLittleBossCount()) return false;
    if (hasDailyLittleBossCount()) {
      if (getDailyLittleBossCount()
          != other.getDailyLittleBossCount()) return false;
    }
    if (hasDailyBigBossCount() != other.hasDailyBigBossCount()) return false;
    if (hasDailyBigBossCount()) {
      if (getDailyBigBossCount()
          != other.getDailyBigBossCount()) return false;
    }
    if (hasGridId() != other.hasGridId()) return false;
    if (hasGridId()) {
      if (getGridId()
          != other.getGridId()) return false;
    }
    if (hasTotalCostEnergy() != other.hasTotalCostEnergy()) return false;
    if (hasTotalCostEnergy()) {
      if (getTotalCostEnergy()
          != other.getTotalCostEnergy()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasEnergy()) {
      hash = (37 * hash) + ENERGY_FIELD_NUMBER;
      hash = (53 * hash) + getEnergy();
    }
    if (hasLastRecoveryTime()) {
      hash = (37 * hash) + LASTRECOVERYTIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getLastRecoveryTime());
    }
    if (hasDailyLittleBossCount()) {
      hash = (37 * hash) + DAILYLITTLEBOSSCOUNT_FIELD_NUMBER;
      hash = (53 * hash) + getDailyLittleBossCount();
    }
    if (hasDailyBigBossCount()) {
      hash = (37 * hash) + DAILYBIGBOSSCOUNT_FIELD_NUMBER;
      hash = (53 * hash) + getDailyBigBossCount();
    }
    if (hasGridId()) {
      hash = (37 * hash) + GRIDID_FIELD_NUMBER;
      hash = (53 * hash) + getGridId();
    }
    if (hasTotalCostEnergy()) {
      hash = (37 * hash) + TOTALCOSTENERGY_FIELD_NUMBER;
      hash = (53 * hash) + getTotalCostEnergy();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.HeavenBattleResultSyncMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.HeavenBattleResultSyncMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.HeavenBattleResultSyncMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.HeavenBattleResultSyncMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.HeavenBattleResultSyncMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.HeavenBattleResultSyncMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.HeavenBattleResultSyncMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.HeavenBattleResultSyncMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.HeavenBattleResultSyncMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.HeavenBattleResultSyncMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.HeavenBattleResultSyncMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.HeavenBattleResultSyncMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.HeavenBattleResultSyncMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.HeavenBattleResultSyncMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.HeavenBattleResultSyncMsg)
      xddq.pb.HeavenBattleResultSyncMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_HeavenBattleResultSyncMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_HeavenBattleResultSyncMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.HeavenBattleResultSyncMsg.class, xddq.pb.HeavenBattleResultSyncMsg.Builder.class);
    }

    // Construct using xddq.pb.HeavenBattleResultSyncMsg.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      energy_ = 0;
      lastRecoveryTime_ = 0L;
      dailyLittleBossCount_ = 0;
      dailyBigBossCount_ = 0;
      gridId_ = 0;
      totalCostEnergy_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_HeavenBattleResultSyncMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.HeavenBattleResultSyncMsg getDefaultInstanceForType() {
      return xddq.pb.HeavenBattleResultSyncMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.HeavenBattleResultSyncMsg build() {
      xddq.pb.HeavenBattleResultSyncMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.HeavenBattleResultSyncMsg buildPartial() {
      xddq.pb.HeavenBattleResultSyncMsg result = new xddq.pb.HeavenBattleResultSyncMsg(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.HeavenBattleResultSyncMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.energy_ = energy_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.lastRecoveryTime_ = lastRecoveryTime_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.dailyLittleBossCount_ = dailyLittleBossCount_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.dailyBigBossCount_ = dailyBigBossCount_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.gridId_ = gridId_;
        to_bitField0_ |= 0x00000010;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.totalCostEnergy_ = totalCostEnergy_;
        to_bitField0_ |= 0x00000020;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.HeavenBattleResultSyncMsg) {
        return mergeFrom((xddq.pb.HeavenBattleResultSyncMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.HeavenBattleResultSyncMsg other) {
      if (other == xddq.pb.HeavenBattleResultSyncMsg.getDefaultInstance()) return this;
      if (other.hasEnergy()) {
        setEnergy(other.getEnergy());
      }
      if (other.hasLastRecoveryTime()) {
        setLastRecoveryTime(other.getLastRecoveryTime());
      }
      if (other.hasDailyLittleBossCount()) {
        setDailyLittleBossCount(other.getDailyLittleBossCount());
      }
      if (other.hasDailyBigBossCount()) {
        setDailyBigBossCount(other.getDailyBigBossCount());
      }
      if (other.hasGridId()) {
        setGridId(other.getGridId());
      }
      if (other.hasTotalCostEnergy()) {
        setTotalCostEnergy(other.getTotalCostEnergy());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              energy_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              lastRecoveryTime_ = input.readInt64();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 24: {
              dailyLittleBossCount_ = input.readInt32();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 32: {
              dailyBigBossCount_ = input.readInt32();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            case 40: {
              gridId_ = input.readInt32();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            case 48: {
              totalCostEnergy_ = input.readInt32();
              bitField0_ |= 0x00000020;
              break;
            } // case 48
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int energy_ ;
    /**
     * <code>optional int32 energy = 1;</code>
     * @return Whether the energy field is set.
     */
    @java.lang.Override
    public boolean hasEnergy() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 energy = 1;</code>
     * @return The energy.
     */
    @java.lang.Override
    public int getEnergy() {
      return energy_;
    }
    /**
     * <code>optional int32 energy = 1;</code>
     * @param value The energy to set.
     * @return This builder for chaining.
     */
    public Builder setEnergy(int value) {

      energy_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 energy = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearEnergy() {
      bitField0_ = (bitField0_ & ~0x00000001);
      energy_ = 0;
      onChanged();
      return this;
    }

    private long lastRecoveryTime_ ;
    /**
     * <code>optional int64 lastRecoveryTime = 2;</code>
     * @return Whether the lastRecoveryTime field is set.
     */
    @java.lang.Override
    public boolean hasLastRecoveryTime() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int64 lastRecoveryTime = 2;</code>
     * @return The lastRecoveryTime.
     */
    @java.lang.Override
    public long getLastRecoveryTime() {
      return lastRecoveryTime_;
    }
    /**
     * <code>optional int64 lastRecoveryTime = 2;</code>
     * @param value The lastRecoveryTime to set.
     * @return This builder for chaining.
     */
    public Builder setLastRecoveryTime(long value) {

      lastRecoveryTime_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 lastRecoveryTime = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearLastRecoveryTime() {
      bitField0_ = (bitField0_ & ~0x00000002);
      lastRecoveryTime_ = 0L;
      onChanged();
      return this;
    }

    private int dailyLittleBossCount_ ;
    /**
     * <code>optional int32 dailyLittleBossCount = 3;</code>
     * @return Whether the dailyLittleBossCount field is set.
     */
    @java.lang.Override
    public boolean hasDailyLittleBossCount() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int32 dailyLittleBossCount = 3;</code>
     * @return The dailyLittleBossCount.
     */
    @java.lang.Override
    public int getDailyLittleBossCount() {
      return dailyLittleBossCount_;
    }
    /**
     * <code>optional int32 dailyLittleBossCount = 3;</code>
     * @param value The dailyLittleBossCount to set.
     * @return This builder for chaining.
     */
    public Builder setDailyLittleBossCount(int value) {

      dailyLittleBossCount_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 dailyLittleBossCount = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearDailyLittleBossCount() {
      bitField0_ = (bitField0_ & ~0x00000004);
      dailyLittleBossCount_ = 0;
      onChanged();
      return this;
    }

    private int dailyBigBossCount_ ;
    /**
     * <code>optional int32 dailyBigBossCount = 4;</code>
     * @return Whether the dailyBigBossCount field is set.
     */
    @java.lang.Override
    public boolean hasDailyBigBossCount() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int32 dailyBigBossCount = 4;</code>
     * @return The dailyBigBossCount.
     */
    @java.lang.Override
    public int getDailyBigBossCount() {
      return dailyBigBossCount_;
    }
    /**
     * <code>optional int32 dailyBigBossCount = 4;</code>
     * @param value The dailyBigBossCount to set.
     * @return This builder for chaining.
     */
    public Builder setDailyBigBossCount(int value) {

      dailyBigBossCount_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 dailyBigBossCount = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearDailyBigBossCount() {
      bitField0_ = (bitField0_ & ~0x00000008);
      dailyBigBossCount_ = 0;
      onChanged();
      return this;
    }

    private int gridId_ ;
    /**
     * <code>optional int32 gridId = 5;</code>
     * @return Whether the gridId field is set.
     */
    @java.lang.Override
    public boolean hasGridId() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional int32 gridId = 5;</code>
     * @return The gridId.
     */
    @java.lang.Override
    public int getGridId() {
      return gridId_;
    }
    /**
     * <code>optional int32 gridId = 5;</code>
     * @param value The gridId to set.
     * @return This builder for chaining.
     */
    public Builder setGridId(int value) {

      gridId_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 gridId = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearGridId() {
      bitField0_ = (bitField0_ & ~0x00000010);
      gridId_ = 0;
      onChanged();
      return this;
    }

    private int totalCostEnergy_ ;
    /**
     * <code>optional int32 totalCostEnergy = 6;</code>
     * @return Whether the totalCostEnergy field is set.
     */
    @java.lang.Override
    public boolean hasTotalCostEnergy() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional int32 totalCostEnergy = 6;</code>
     * @return The totalCostEnergy.
     */
    @java.lang.Override
    public int getTotalCostEnergy() {
      return totalCostEnergy_;
    }
    /**
     * <code>optional int32 totalCostEnergy = 6;</code>
     * @param value The totalCostEnergy to set.
     * @return This builder for chaining.
     */
    public Builder setTotalCostEnergy(int value) {

      totalCostEnergy_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 totalCostEnergy = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearTotalCostEnergy() {
      bitField0_ = (bitField0_ & ~0x00000020);
      totalCostEnergy_ = 0;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.HeavenBattleResultSyncMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.HeavenBattleResultSyncMsg)
  private static final xddq.pb.HeavenBattleResultSyncMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.HeavenBattleResultSyncMsg();
  }

  public static xddq.pb.HeavenBattleResultSyncMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<HeavenBattleResultSyncMsg>
      PARSER = new com.google.protobuf.AbstractParser<HeavenBattleResultSyncMsg>() {
    @java.lang.Override
    public HeavenBattleResultSyncMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<HeavenBattleResultSyncMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<HeavenBattleResultSyncMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.HeavenBattleResultSyncMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

