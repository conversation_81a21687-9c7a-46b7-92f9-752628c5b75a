// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.WeYoundChallengeInfoSync}
 */
public final class WeYoundChallengeInfoSync extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.WeYoundChallengeInfoSync)
    WeYoundChallengeInfoSyncOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      WeYoundChallengeInfoSync.class.getName());
  }
  // Use WeYoundChallengeInfoSync.newBuilder() to construct.
  private WeYoundChallengeInfoSync(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private WeYoundChallengeInfoSync() {
    unionName_ = "";
    name_ = "";
    advanced_ = emptyLongList();
    groupRankMsg_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_WeYoundChallengeInfoSync_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_WeYoundChallengeInfoSync_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.WeYoundChallengeInfoSync.class, xddq.pb.WeYoundChallengeInfoSync.Builder.class);
  }

  private int bitField0_;
  public static final int PLAYERID_FIELD_NUMBER = 1;
  private long playerId_ = 0L;
  /**
   * <code>required int64 playerId = 1;</code>
   * @return Whether the playerId field is set.
   */
  @java.lang.Override
  public boolean hasPlayerId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int64 playerId = 1;</code>
   * @return The playerId.
   */
  @java.lang.Override
  public long getPlayerId() {
    return playerId_;
  }

  public static final int UNIONNAME_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private volatile java.lang.Object unionName_ = "";
  /**
   * <code>optional string unionName = 2;</code>
   * @return Whether the unionName field is set.
   */
  @java.lang.Override
  public boolean hasUnionName() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional string unionName = 2;</code>
   * @return The unionName.
   */
  @java.lang.Override
  public java.lang.String getUnionName() {
    java.lang.Object ref = unionName_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        unionName_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string unionName = 2;</code>
   * @return The bytes for unionName.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getUnionNameBytes() {
    java.lang.Object ref = unionName_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      unionName_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int NAME_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private volatile java.lang.Object name_ = "";
  /**
   * <code>optional string name = 3;</code>
   * @return Whether the name field is set.
   */
  @java.lang.Override
  public boolean hasName() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional string name = 3;</code>
   * @return The name.
   */
  @java.lang.Override
  public java.lang.String getName() {
    java.lang.Object ref = name_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        name_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string name = 3;</code>
   * @return The bytes for name.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getNameBytes() {
    java.lang.Object ref = name_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      name_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ADVANCED_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private com.google.protobuf.Internal.LongList advanced_ =
      emptyLongList();
  /**
   * <code>repeated int64 advanced = 4;</code>
   * @return A list containing the advanced.
   */
  @java.lang.Override
  public java.util.List<java.lang.Long>
      getAdvancedList() {
    return advanced_;
  }
  /**
   * <code>repeated int64 advanced = 4;</code>
   * @return The count of advanced.
   */
  public int getAdvancedCount() {
    return advanced_.size();
  }
  /**
   * <code>repeated int64 advanced = 4;</code>
   * @param index The index of the element to return.
   * @return The advanced at the given index.
   */
  public long getAdvanced(int index) {
    return advanced_.getLong(index);
  }

  public static final int APPEARANCE_FIELD_NUMBER = 5;
  private xddq.pb.WeYoundAppearanceMsg appearance_;
  /**
   * <code>optional .xddq.pb.WeYoundAppearanceMsg appearance = 5;</code>
   * @return Whether the appearance field is set.
   */
  @java.lang.Override
  public boolean hasAppearance() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional .xddq.pb.WeYoundAppearanceMsg appearance = 5;</code>
   * @return The appearance.
   */
  @java.lang.Override
  public xddq.pb.WeYoundAppearanceMsg getAppearance() {
    return appearance_ == null ? xddq.pb.WeYoundAppearanceMsg.getDefaultInstance() : appearance_;
  }
  /**
   * <code>optional .xddq.pb.WeYoundAppearanceMsg appearance = 5;</code>
   */
  @java.lang.Override
  public xddq.pb.WeYoundAppearanceMsgOrBuilder getAppearanceOrBuilder() {
    return appearance_ == null ? xddq.pb.WeYoundAppearanceMsg.getDefaultInstance() : appearance_;
  }

  public static final int TARGETUNION_FIELD_NUMBER = 6;
  private xddq.pb.WeYoundUnionChallengeSimpleMsg targetUnion_;
  /**
   * <pre>
   * 被攻击的妖盟信息 
   * </pre>
   *
   * <code>optional .xddq.pb.WeYoundUnionChallengeSimpleMsg targetUnion = 6;</code>
   * @return Whether the targetUnion field is set.
   */
  @java.lang.Override
  public boolean hasTargetUnion() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <pre>
   * 被攻击的妖盟信息 
   * </pre>
   *
   * <code>optional .xddq.pb.WeYoundUnionChallengeSimpleMsg targetUnion = 6;</code>
   * @return The targetUnion.
   */
  @java.lang.Override
  public xddq.pb.WeYoundUnionChallengeSimpleMsg getTargetUnion() {
    return targetUnion_ == null ? xddq.pb.WeYoundUnionChallengeSimpleMsg.getDefaultInstance() : targetUnion_;
  }
  /**
   * <pre>
   * 被攻击的妖盟信息 
   * </pre>
   *
   * <code>optional .xddq.pb.WeYoundUnionChallengeSimpleMsg targetUnion = 6;</code>
   */
  @java.lang.Override
  public xddq.pb.WeYoundUnionChallengeSimpleMsgOrBuilder getTargetUnionOrBuilder() {
    return targetUnion_ == null ? xddq.pb.WeYoundUnionChallengeSimpleMsg.getDefaultInstance() : targetUnion_;
  }

  public static final int GROUPRANKMSG_FIELD_NUMBER = 7;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.WeYoundUnionGroupRankMsg> groupRankMsg_;
  /**
   * <code>repeated .xddq.pb.WeYoundUnionGroupRankMsg groupRankMsg = 7;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.WeYoundUnionGroupRankMsg> getGroupRankMsgList() {
    return groupRankMsg_;
  }
  /**
   * <code>repeated .xddq.pb.WeYoundUnionGroupRankMsg groupRankMsg = 7;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.WeYoundUnionGroupRankMsgOrBuilder> 
      getGroupRankMsgOrBuilderList() {
    return groupRankMsg_;
  }
  /**
   * <code>repeated .xddq.pb.WeYoundUnionGroupRankMsg groupRankMsg = 7;</code>
   */
  @java.lang.Override
  public int getGroupRankMsgCount() {
    return groupRankMsg_.size();
  }
  /**
   * <code>repeated .xddq.pb.WeYoundUnionGroupRankMsg groupRankMsg = 7;</code>
   */
  @java.lang.Override
  public xddq.pb.WeYoundUnionGroupRankMsg getGroupRankMsg(int index) {
    return groupRankMsg_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.WeYoundUnionGroupRankMsg groupRankMsg = 7;</code>
   */
  @java.lang.Override
  public xddq.pb.WeYoundUnionGroupRankMsgOrBuilder getGroupRankMsgOrBuilder(
      int index) {
    return groupRankMsg_.get(index);
  }

  public static final int DAMAGE_FIELD_NUMBER = 8;
  private long damage_ = 0L;
  /**
   * <code>optional int64 damage = 8;</code>
   * @return Whether the damage field is set.
   */
  @java.lang.Override
  public boolean hasDamage() {
    return ((bitField0_ & 0x00000020) != 0);
  }
  /**
   * <code>optional int64 damage = 8;</code>
   * @return The damage.
   */
  @java.lang.Override
  public long getDamage() {
    return damage_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasPlayerId()) {
      memoizedIsInitialized = 0;
      return false;
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt64(1, playerId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 2, unionName_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 3, name_);
    }
    for (int i = 0; i < advanced_.size(); i++) {
      output.writeInt64(4, advanced_.getLong(i));
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeMessage(5, getAppearance());
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      output.writeMessage(6, getTargetUnion());
    }
    for (int i = 0; i < groupRankMsg_.size(); i++) {
      output.writeMessage(7, groupRankMsg_.get(i));
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      output.writeInt64(8, damage_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(1, playerId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(2, unionName_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(3, name_);
    }
    {
      int dataSize = 0;
      for (int i = 0; i < advanced_.size(); i++) {
        dataSize += com.google.protobuf.CodedOutputStream
          .computeInt64SizeNoTag(advanced_.getLong(i));
      }
      size += dataSize;
      size += 1 * getAdvancedList().size();
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(5, getAppearance());
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(6, getTargetUnion());
    }
    for (int i = 0; i < groupRankMsg_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(7, groupRankMsg_.get(i));
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(8, damage_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.WeYoundChallengeInfoSync)) {
      return super.equals(obj);
    }
    xddq.pb.WeYoundChallengeInfoSync other = (xddq.pb.WeYoundChallengeInfoSync) obj;

    if (hasPlayerId() != other.hasPlayerId()) return false;
    if (hasPlayerId()) {
      if (getPlayerId()
          != other.getPlayerId()) return false;
    }
    if (hasUnionName() != other.hasUnionName()) return false;
    if (hasUnionName()) {
      if (!getUnionName()
          .equals(other.getUnionName())) return false;
    }
    if (hasName() != other.hasName()) return false;
    if (hasName()) {
      if (!getName()
          .equals(other.getName())) return false;
    }
    if (!getAdvancedList()
        .equals(other.getAdvancedList())) return false;
    if (hasAppearance() != other.hasAppearance()) return false;
    if (hasAppearance()) {
      if (!getAppearance()
          .equals(other.getAppearance())) return false;
    }
    if (hasTargetUnion() != other.hasTargetUnion()) return false;
    if (hasTargetUnion()) {
      if (!getTargetUnion()
          .equals(other.getTargetUnion())) return false;
    }
    if (!getGroupRankMsgList()
        .equals(other.getGroupRankMsgList())) return false;
    if (hasDamage() != other.hasDamage()) return false;
    if (hasDamage()) {
      if (getDamage()
          != other.getDamage()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasPlayerId()) {
      hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getPlayerId());
    }
    if (hasUnionName()) {
      hash = (37 * hash) + UNIONNAME_FIELD_NUMBER;
      hash = (53 * hash) + getUnionName().hashCode();
    }
    if (hasName()) {
      hash = (37 * hash) + NAME_FIELD_NUMBER;
      hash = (53 * hash) + getName().hashCode();
    }
    if (getAdvancedCount() > 0) {
      hash = (37 * hash) + ADVANCED_FIELD_NUMBER;
      hash = (53 * hash) + getAdvancedList().hashCode();
    }
    if (hasAppearance()) {
      hash = (37 * hash) + APPEARANCE_FIELD_NUMBER;
      hash = (53 * hash) + getAppearance().hashCode();
    }
    if (hasTargetUnion()) {
      hash = (37 * hash) + TARGETUNION_FIELD_NUMBER;
      hash = (53 * hash) + getTargetUnion().hashCode();
    }
    if (getGroupRankMsgCount() > 0) {
      hash = (37 * hash) + GROUPRANKMSG_FIELD_NUMBER;
      hash = (53 * hash) + getGroupRankMsgList().hashCode();
    }
    if (hasDamage()) {
      hash = (37 * hash) + DAMAGE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getDamage());
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.WeYoundChallengeInfoSync parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.WeYoundChallengeInfoSync parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.WeYoundChallengeInfoSync parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.WeYoundChallengeInfoSync parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.WeYoundChallengeInfoSync parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.WeYoundChallengeInfoSync parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.WeYoundChallengeInfoSync parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.WeYoundChallengeInfoSync parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.WeYoundChallengeInfoSync parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.WeYoundChallengeInfoSync parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.WeYoundChallengeInfoSync parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.WeYoundChallengeInfoSync parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.WeYoundChallengeInfoSync prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.WeYoundChallengeInfoSync}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.WeYoundChallengeInfoSync)
      xddq.pb.WeYoundChallengeInfoSyncOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_WeYoundChallengeInfoSync_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_WeYoundChallengeInfoSync_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.WeYoundChallengeInfoSync.class, xddq.pb.WeYoundChallengeInfoSync.Builder.class);
    }

    // Construct using xddq.pb.WeYoundChallengeInfoSync.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetAppearanceFieldBuilder();
        internalGetTargetUnionFieldBuilder();
        internalGetGroupRankMsgFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      playerId_ = 0L;
      unionName_ = "";
      name_ = "";
      advanced_ = emptyLongList();
      appearance_ = null;
      if (appearanceBuilder_ != null) {
        appearanceBuilder_.dispose();
        appearanceBuilder_ = null;
      }
      targetUnion_ = null;
      if (targetUnionBuilder_ != null) {
        targetUnionBuilder_.dispose();
        targetUnionBuilder_ = null;
      }
      if (groupRankMsgBuilder_ == null) {
        groupRankMsg_ = java.util.Collections.emptyList();
      } else {
        groupRankMsg_ = null;
        groupRankMsgBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000040);
      damage_ = 0L;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_WeYoundChallengeInfoSync_descriptor;
    }

    @java.lang.Override
    public xddq.pb.WeYoundChallengeInfoSync getDefaultInstanceForType() {
      return xddq.pb.WeYoundChallengeInfoSync.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.WeYoundChallengeInfoSync build() {
      xddq.pb.WeYoundChallengeInfoSync result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.WeYoundChallengeInfoSync buildPartial() {
      xddq.pb.WeYoundChallengeInfoSync result = new xddq.pb.WeYoundChallengeInfoSync(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.WeYoundChallengeInfoSync result) {
      if (groupRankMsgBuilder_ == null) {
        if (((bitField0_ & 0x00000040) != 0)) {
          groupRankMsg_ = java.util.Collections.unmodifiableList(groupRankMsg_);
          bitField0_ = (bitField0_ & ~0x00000040);
        }
        result.groupRankMsg_ = groupRankMsg_;
      } else {
        result.groupRankMsg_ = groupRankMsgBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.WeYoundChallengeInfoSync result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.playerId_ = playerId_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.unionName_ = unionName_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.name_ = name_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        advanced_.makeImmutable();
        result.advanced_ = advanced_;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.appearance_ = appearanceBuilder_ == null
            ? appearance_
            : appearanceBuilder_.build();
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.targetUnion_ = targetUnionBuilder_ == null
            ? targetUnion_
            : targetUnionBuilder_.build();
        to_bitField0_ |= 0x00000010;
      }
      if (((from_bitField0_ & 0x00000080) != 0)) {
        result.damage_ = damage_;
        to_bitField0_ |= 0x00000020;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.WeYoundChallengeInfoSync) {
        return mergeFrom((xddq.pb.WeYoundChallengeInfoSync)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.WeYoundChallengeInfoSync other) {
      if (other == xddq.pb.WeYoundChallengeInfoSync.getDefaultInstance()) return this;
      if (other.hasPlayerId()) {
        setPlayerId(other.getPlayerId());
      }
      if (other.hasUnionName()) {
        unionName_ = other.unionName_;
        bitField0_ |= 0x00000002;
        onChanged();
      }
      if (other.hasName()) {
        name_ = other.name_;
        bitField0_ |= 0x00000004;
        onChanged();
      }
      if (!other.advanced_.isEmpty()) {
        if (advanced_.isEmpty()) {
          advanced_ = other.advanced_;
          advanced_.makeImmutable();
          bitField0_ |= 0x00000008;
        } else {
          ensureAdvancedIsMutable();
          advanced_.addAll(other.advanced_);
        }
        onChanged();
      }
      if (other.hasAppearance()) {
        mergeAppearance(other.getAppearance());
      }
      if (other.hasTargetUnion()) {
        mergeTargetUnion(other.getTargetUnion());
      }
      if (groupRankMsgBuilder_ == null) {
        if (!other.groupRankMsg_.isEmpty()) {
          if (groupRankMsg_.isEmpty()) {
            groupRankMsg_ = other.groupRankMsg_;
            bitField0_ = (bitField0_ & ~0x00000040);
          } else {
            ensureGroupRankMsgIsMutable();
            groupRankMsg_.addAll(other.groupRankMsg_);
          }
          onChanged();
        }
      } else {
        if (!other.groupRankMsg_.isEmpty()) {
          if (groupRankMsgBuilder_.isEmpty()) {
            groupRankMsgBuilder_.dispose();
            groupRankMsgBuilder_ = null;
            groupRankMsg_ = other.groupRankMsg_;
            bitField0_ = (bitField0_ & ~0x00000040);
            groupRankMsgBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetGroupRankMsgFieldBuilder() : null;
          } else {
            groupRankMsgBuilder_.addAllMessages(other.groupRankMsg_);
          }
        }
      }
      if (other.hasDamage()) {
        setDamage(other.getDamage());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasPlayerId()) {
        return false;
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              playerId_ = input.readInt64();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              unionName_ = input.readBytes();
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 26: {
              name_ = input.readBytes();
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 32: {
              long v = input.readInt64();
              ensureAdvancedIsMutable();
              advanced_.addLong(v);
              break;
            } // case 32
            case 34: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              ensureAdvancedIsMutable();
              while (input.getBytesUntilLimit() > 0) {
                advanced_.addLong(input.readInt64());
              }
              input.popLimit(limit);
              break;
            } // case 34
            case 42: {
              input.readMessage(
                  internalGetAppearanceFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000010;
              break;
            } // case 42
            case 50: {
              input.readMessage(
                  internalGetTargetUnionFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000020;
              break;
            } // case 50
            case 58: {
              xddq.pb.WeYoundUnionGroupRankMsg m =
                  input.readMessage(
                      xddq.pb.WeYoundUnionGroupRankMsg.parser(),
                      extensionRegistry);
              if (groupRankMsgBuilder_ == null) {
                ensureGroupRankMsgIsMutable();
                groupRankMsg_.add(m);
              } else {
                groupRankMsgBuilder_.addMessage(m);
              }
              break;
            } // case 58
            case 64: {
              damage_ = input.readInt64();
              bitField0_ |= 0x00000080;
              break;
            } // case 64
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private long playerId_ ;
    /**
     * <code>required int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int64 playerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }
    /**
     * <code>required int64 playerId = 1;</code>
     * @param value The playerId to set.
     * @return This builder for chaining.
     */
    public Builder setPlayerId(long value) {

      playerId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int64 playerId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearPlayerId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      playerId_ = 0L;
      onChanged();
      return this;
    }

    private java.lang.Object unionName_ = "";
    /**
     * <code>optional string unionName = 2;</code>
     * @return Whether the unionName field is set.
     */
    public boolean hasUnionName() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional string unionName = 2;</code>
     * @return The unionName.
     */
    public java.lang.String getUnionName() {
      java.lang.Object ref = unionName_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          unionName_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string unionName = 2;</code>
     * @return The bytes for unionName.
     */
    public com.google.protobuf.ByteString
        getUnionNameBytes() {
      java.lang.Object ref = unionName_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        unionName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string unionName = 2;</code>
     * @param value The unionName to set.
     * @return This builder for chaining.
     */
    public Builder setUnionName(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      unionName_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional string unionName = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearUnionName() {
      unionName_ = getDefaultInstance().getUnionName();
      bitField0_ = (bitField0_ & ~0x00000002);
      onChanged();
      return this;
    }
    /**
     * <code>optional string unionName = 2;</code>
     * @param value The bytes for unionName to set.
     * @return This builder for chaining.
     */
    public Builder setUnionNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      unionName_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }

    private java.lang.Object name_ = "";
    /**
     * <code>optional string name = 3;</code>
     * @return Whether the name field is set.
     */
    public boolean hasName() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional string name = 3;</code>
     * @return The name.
     */
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          name_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string name = 3;</code>
     * @return The bytes for name.
     */
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string name = 3;</code>
     * @param value The name to set.
     * @return This builder for chaining.
     */
    public Builder setName(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      name_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional string name = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearName() {
      name_ = getDefaultInstance().getName();
      bitField0_ = (bitField0_ & ~0x00000004);
      onChanged();
      return this;
    }
    /**
     * <code>optional string name = 3;</code>
     * @param value The bytes for name to set.
     * @return This builder for chaining.
     */
    public Builder setNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      name_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }

    private com.google.protobuf.Internal.LongList advanced_ = emptyLongList();
    private void ensureAdvancedIsMutable() {
      if (!advanced_.isModifiable()) {
        advanced_ = makeMutableCopy(advanced_);
      }
      bitField0_ |= 0x00000008;
    }
    /**
     * <code>repeated int64 advanced = 4;</code>
     * @return A list containing the advanced.
     */
    public java.util.List<java.lang.Long>
        getAdvancedList() {
      advanced_.makeImmutable();
      return advanced_;
    }
    /**
     * <code>repeated int64 advanced = 4;</code>
     * @return The count of advanced.
     */
    public int getAdvancedCount() {
      return advanced_.size();
    }
    /**
     * <code>repeated int64 advanced = 4;</code>
     * @param index The index of the element to return.
     * @return The advanced at the given index.
     */
    public long getAdvanced(int index) {
      return advanced_.getLong(index);
    }
    /**
     * <code>repeated int64 advanced = 4;</code>
     * @param index The index to set the value at.
     * @param value The advanced to set.
     * @return This builder for chaining.
     */
    public Builder setAdvanced(
        int index, long value) {

      ensureAdvancedIsMutable();
      advanced_.setLong(index, value);
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int64 advanced = 4;</code>
     * @param value The advanced to add.
     * @return This builder for chaining.
     */
    public Builder addAdvanced(long value) {

      ensureAdvancedIsMutable();
      advanced_.addLong(value);
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int64 advanced = 4;</code>
     * @param values The advanced to add.
     * @return This builder for chaining.
     */
    public Builder addAllAdvanced(
        java.lang.Iterable<? extends java.lang.Long> values) {
      ensureAdvancedIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, advanced_);
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int64 advanced = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearAdvanced() {
      advanced_ = emptyLongList();
      bitField0_ = (bitField0_ & ~0x00000008);
      onChanged();
      return this;
    }

    private xddq.pb.WeYoundAppearanceMsg appearance_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.WeYoundAppearanceMsg, xddq.pb.WeYoundAppearanceMsg.Builder, xddq.pb.WeYoundAppearanceMsgOrBuilder> appearanceBuilder_;
    /**
     * <code>optional .xddq.pb.WeYoundAppearanceMsg appearance = 5;</code>
     * @return Whether the appearance field is set.
     */
    public boolean hasAppearance() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional .xddq.pb.WeYoundAppearanceMsg appearance = 5;</code>
     * @return The appearance.
     */
    public xddq.pb.WeYoundAppearanceMsg getAppearance() {
      if (appearanceBuilder_ == null) {
        return appearance_ == null ? xddq.pb.WeYoundAppearanceMsg.getDefaultInstance() : appearance_;
      } else {
        return appearanceBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.WeYoundAppearanceMsg appearance = 5;</code>
     */
    public Builder setAppearance(xddq.pb.WeYoundAppearanceMsg value) {
      if (appearanceBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        appearance_ = value;
      } else {
        appearanceBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.WeYoundAppearanceMsg appearance = 5;</code>
     */
    public Builder setAppearance(
        xddq.pb.WeYoundAppearanceMsg.Builder builderForValue) {
      if (appearanceBuilder_ == null) {
        appearance_ = builderForValue.build();
      } else {
        appearanceBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.WeYoundAppearanceMsg appearance = 5;</code>
     */
    public Builder mergeAppearance(xddq.pb.WeYoundAppearanceMsg value) {
      if (appearanceBuilder_ == null) {
        if (((bitField0_ & 0x00000010) != 0) &&
          appearance_ != null &&
          appearance_ != xddq.pb.WeYoundAppearanceMsg.getDefaultInstance()) {
          getAppearanceBuilder().mergeFrom(value);
        } else {
          appearance_ = value;
        }
      } else {
        appearanceBuilder_.mergeFrom(value);
      }
      if (appearance_ != null) {
        bitField0_ |= 0x00000010;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.WeYoundAppearanceMsg appearance = 5;</code>
     */
    public Builder clearAppearance() {
      bitField0_ = (bitField0_ & ~0x00000010);
      appearance_ = null;
      if (appearanceBuilder_ != null) {
        appearanceBuilder_.dispose();
        appearanceBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.WeYoundAppearanceMsg appearance = 5;</code>
     */
    public xddq.pb.WeYoundAppearanceMsg.Builder getAppearanceBuilder() {
      bitField0_ |= 0x00000010;
      onChanged();
      return internalGetAppearanceFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.WeYoundAppearanceMsg appearance = 5;</code>
     */
    public xddq.pb.WeYoundAppearanceMsgOrBuilder getAppearanceOrBuilder() {
      if (appearanceBuilder_ != null) {
        return appearanceBuilder_.getMessageOrBuilder();
      } else {
        return appearance_ == null ?
            xddq.pb.WeYoundAppearanceMsg.getDefaultInstance() : appearance_;
      }
    }
    /**
     * <code>optional .xddq.pb.WeYoundAppearanceMsg appearance = 5;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.WeYoundAppearanceMsg, xddq.pb.WeYoundAppearanceMsg.Builder, xddq.pb.WeYoundAppearanceMsgOrBuilder> 
        internalGetAppearanceFieldBuilder() {
      if (appearanceBuilder_ == null) {
        appearanceBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.WeYoundAppearanceMsg, xddq.pb.WeYoundAppearanceMsg.Builder, xddq.pb.WeYoundAppearanceMsgOrBuilder>(
                getAppearance(),
                getParentForChildren(),
                isClean());
        appearance_ = null;
      }
      return appearanceBuilder_;
    }

    private xddq.pb.WeYoundUnionChallengeSimpleMsg targetUnion_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.WeYoundUnionChallengeSimpleMsg, xddq.pb.WeYoundUnionChallengeSimpleMsg.Builder, xddq.pb.WeYoundUnionChallengeSimpleMsgOrBuilder> targetUnionBuilder_;
    /**
     * <pre>
     * 被攻击的妖盟信息 
     * </pre>
     *
     * <code>optional .xddq.pb.WeYoundUnionChallengeSimpleMsg targetUnion = 6;</code>
     * @return Whether the targetUnion field is set.
     */
    public boolean hasTargetUnion() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <pre>
     * 被攻击的妖盟信息 
     * </pre>
     *
     * <code>optional .xddq.pb.WeYoundUnionChallengeSimpleMsg targetUnion = 6;</code>
     * @return The targetUnion.
     */
    public xddq.pb.WeYoundUnionChallengeSimpleMsg getTargetUnion() {
      if (targetUnionBuilder_ == null) {
        return targetUnion_ == null ? xddq.pb.WeYoundUnionChallengeSimpleMsg.getDefaultInstance() : targetUnion_;
      } else {
        return targetUnionBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * 被攻击的妖盟信息 
     * </pre>
     *
     * <code>optional .xddq.pb.WeYoundUnionChallengeSimpleMsg targetUnion = 6;</code>
     */
    public Builder setTargetUnion(xddq.pb.WeYoundUnionChallengeSimpleMsg value) {
      if (targetUnionBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        targetUnion_ = value;
      } else {
        targetUnionBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 被攻击的妖盟信息 
     * </pre>
     *
     * <code>optional .xddq.pb.WeYoundUnionChallengeSimpleMsg targetUnion = 6;</code>
     */
    public Builder setTargetUnion(
        xddq.pb.WeYoundUnionChallengeSimpleMsg.Builder builderForValue) {
      if (targetUnionBuilder_ == null) {
        targetUnion_ = builderForValue.build();
      } else {
        targetUnionBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 被攻击的妖盟信息 
     * </pre>
     *
     * <code>optional .xddq.pb.WeYoundUnionChallengeSimpleMsg targetUnion = 6;</code>
     */
    public Builder mergeTargetUnion(xddq.pb.WeYoundUnionChallengeSimpleMsg value) {
      if (targetUnionBuilder_ == null) {
        if (((bitField0_ & 0x00000020) != 0) &&
          targetUnion_ != null &&
          targetUnion_ != xddq.pb.WeYoundUnionChallengeSimpleMsg.getDefaultInstance()) {
          getTargetUnionBuilder().mergeFrom(value);
        } else {
          targetUnion_ = value;
        }
      } else {
        targetUnionBuilder_.mergeFrom(value);
      }
      if (targetUnion_ != null) {
        bitField0_ |= 0x00000020;
        onChanged();
      }
      return this;
    }
    /**
     * <pre>
     * 被攻击的妖盟信息 
     * </pre>
     *
     * <code>optional .xddq.pb.WeYoundUnionChallengeSimpleMsg targetUnion = 6;</code>
     */
    public Builder clearTargetUnion() {
      bitField0_ = (bitField0_ & ~0x00000020);
      targetUnion_ = null;
      if (targetUnionBuilder_ != null) {
        targetUnionBuilder_.dispose();
        targetUnionBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 被攻击的妖盟信息 
     * </pre>
     *
     * <code>optional .xddq.pb.WeYoundUnionChallengeSimpleMsg targetUnion = 6;</code>
     */
    public xddq.pb.WeYoundUnionChallengeSimpleMsg.Builder getTargetUnionBuilder() {
      bitField0_ |= 0x00000020;
      onChanged();
      return internalGetTargetUnionFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * 被攻击的妖盟信息 
     * </pre>
     *
     * <code>optional .xddq.pb.WeYoundUnionChallengeSimpleMsg targetUnion = 6;</code>
     */
    public xddq.pb.WeYoundUnionChallengeSimpleMsgOrBuilder getTargetUnionOrBuilder() {
      if (targetUnionBuilder_ != null) {
        return targetUnionBuilder_.getMessageOrBuilder();
      } else {
        return targetUnion_ == null ?
            xddq.pb.WeYoundUnionChallengeSimpleMsg.getDefaultInstance() : targetUnion_;
      }
    }
    /**
     * <pre>
     * 被攻击的妖盟信息 
     * </pre>
     *
     * <code>optional .xddq.pb.WeYoundUnionChallengeSimpleMsg targetUnion = 6;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.WeYoundUnionChallengeSimpleMsg, xddq.pb.WeYoundUnionChallengeSimpleMsg.Builder, xddq.pb.WeYoundUnionChallengeSimpleMsgOrBuilder> 
        internalGetTargetUnionFieldBuilder() {
      if (targetUnionBuilder_ == null) {
        targetUnionBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.WeYoundUnionChallengeSimpleMsg, xddq.pb.WeYoundUnionChallengeSimpleMsg.Builder, xddq.pb.WeYoundUnionChallengeSimpleMsgOrBuilder>(
                getTargetUnion(),
                getParentForChildren(),
                isClean());
        targetUnion_ = null;
      }
      return targetUnionBuilder_;
    }

    private java.util.List<xddq.pb.WeYoundUnionGroupRankMsg> groupRankMsg_ =
      java.util.Collections.emptyList();
    private void ensureGroupRankMsgIsMutable() {
      if (!((bitField0_ & 0x00000040) != 0)) {
        groupRankMsg_ = new java.util.ArrayList<xddq.pb.WeYoundUnionGroupRankMsg>(groupRankMsg_);
        bitField0_ |= 0x00000040;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.WeYoundUnionGroupRankMsg, xddq.pb.WeYoundUnionGroupRankMsg.Builder, xddq.pb.WeYoundUnionGroupRankMsgOrBuilder> groupRankMsgBuilder_;

    /**
     * <code>repeated .xddq.pb.WeYoundUnionGroupRankMsg groupRankMsg = 7;</code>
     */
    public java.util.List<xddq.pb.WeYoundUnionGroupRankMsg> getGroupRankMsgList() {
      if (groupRankMsgBuilder_ == null) {
        return java.util.Collections.unmodifiableList(groupRankMsg_);
      } else {
        return groupRankMsgBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.WeYoundUnionGroupRankMsg groupRankMsg = 7;</code>
     */
    public int getGroupRankMsgCount() {
      if (groupRankMsgBuilder_ == null) {
        return groupRankMsg_.size();
      } else {
        return groupRankMsgBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.WeYoundUnionGroupRankMsg groupRankMsg = 7;</code>
     */
    public xddq.pb.WeYoundUnionGroupRankMsg getGroupRankMsg(int index) {
      if (groupRankMsgBuilder_ == null) {
        return groupRankMsg_.get(index);
      } else {
        return groupRankMsgBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.WeYoundUnionGroupRankMsg groupRankMsg = 7;</code>
     */
    public Builder setGroupRankMsg(
        int index, xddq.pb.WeYoundUnionGroupRankMsg value) {
      if (groupRankMsgBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureGroupRankMsgIsMutable();
        groupRankMsg_.set(index, value);
        onChanged();
      } else {
        groupRankMsgBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WeYoundUnionGroupRankMsg groupRankMsg = 7;</code>
     */
    public Builder setGroupRankMsg(
        int index, xddq.pb.WeYoundUnionGroupRankMsg.Builder builderForValue) {
      if (groupRankMsgBuilder_ == null) {
        ensureGroupRankMsgIsMutable();
        groupRankMsg_.set(index, builderForValue.build());
        onChanged();
      } else {
        groupRankMsgBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WeYoundUnionGroupRankMsg groupRankMsg = 7;</code>
     */
    public Builder addGroupRankMsg(xddq.pb.WeYoundUnionGroupRankMsg value) {
      if (groupRankMsgBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureGroupRankMsgIsMutable();
        groupRankMsg_.add(value);
        onChanged();
      } else {
        groupRankMsgBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WeYoundUnionGroupRankMsg groupRankMsg = 7;</code>
     */
    public Builder addGroupRankMsg(
        int index, xddq.pb.WeYoundUnionGroupRankMsg value) {
      if (groupRankMsgBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureGroupRankMsgIsMutable();
        groupRankMsg_.add(index, value);
        onChanged();
      } else {
        groupRankMsgBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WeYoundUnionGroupRankMsg groupRankMsg = 7;</code>
     */
    public Builder addGroupRankMsg(
        xddq.pb.WeYoundUnionGroupRankMsg.Builder builderForValue) {
      if (groupRankMsgBuilder_ == null) {
        ensureGroupRankMsgIsMutable();
        groupRankMsg_.add(builderForValue.build());
        onChanged();
      } else {
        groupRankMsgBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WeYoundUnionGroupRankMsg groupRankMsg = 7;</code>
     */
    public Builder addGroupRankMsg(
        int index, xddq.pb.WeYoundUnionGroupRankMsg.Builder builderForValue) {
      if (groupRankMsgBuilder_ == null) {
        ensureGroupRankMsgIsMutable();
        groupRankMsg_.add(index, builderForValue.build());
        onChanged();
      } else {
        groupRankMsgBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WeYoundUnionGroupRankMsg groupRankMsg = 7;</code>
     */
    public Builder addAllGroupRankMsg(
        java.lang.Iterable<? extends xddq.pb.WeYoundUnionGroupRankMsg> values) {
      if (groupRankMsgBuilder_ == null) {
        ensureGroupRankMsgIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, groupRankMsg_);
        onChanged();
      } else {
        groupRankMsgBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WeYoundUnionGroupRankMsg groupRankMsg = 7;</code>
     */
    public Builder clearGroupRankMsg() {
      if (groupRankMsgBuilder_ == null) {
        groupRankMsg_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000040);
        onChanged();
      } else {
        groupRankMsgBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WeYoundUnionGroupRankMsg groupRankMsg = 7;</code>
     */
    public Builder removeGroupRankMsg(int index) {
      if (groupRankMsgBuilder_ == null) {
        ensureGroupRankMsgIsMutable();
        groupRankMsg_.remove(index);
        onChanged();
      } else {
        groupRankMsgBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WeYoundUnionGroupRankMsg groupRankMsg = 7;</code>
     */
    public xddq.pb.WeYoundUnionGroupRankMsg.Builder getGroupRankMsgBuilder(
        int index) {
      return internalGetGroupRankMsgFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.WeYoundUnionGroupRankMsg groupRankMsg = 7;</code>
     */
    public xddq.pb.WeYoundUnionGroupRankMsgOrBuilder getGroupRankMsgOrBuilder(
        int index) {
      if (groupRankMsgBuilder_ == null) {
        return groupRankMsg_.get(index);  } else {
        return groupRankMsgBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.WeYoundUnionGroupRankMsg groupRankMsg = 7;</code>
     */
    public java.util.List<? extends xddq.pb.WeYoundUnionGroupRankMsgOrBuilder> 
         getGroupRankMsgOrBuilderList() {
      if (groupRankMsgBuilder_ != null) {
        return groupRankMsgBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(groupRankMsg_);
      }
    }
    /**
     * <code>repeated .xddq.pb.WeYoundUnionGroupRankMsg groupRankMsg = 7;</code>
     */
    public xddq.pb.WeYoundUnionGroupRankMsg.Builder addGroupRankMsgBuilder() {
      return internalGetGroupRankMsgFieldBuilder().addBuilder(
          xddq.pb.WeYoundUnionGroupRankMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.WeYoundUnionGroupRankMsg groupRankMsg = 7;</code>
     */
    public xddq.pb.WeYoundUnionGroupRankMsg.Builder addGroupRankMsgBuilder(
        int index) {
      return internalGetGroupRankMsgFieldBuilder().addBuilder(
          index, xddq.pb.WeYoundUnionGroupRankMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.WeYoundUnionGroupRankMsg groupRankMsg = 7;</code>
     */
    public java.util.List<xddq.pb.WeYoundUnionGroupRankMsg.Builder> 
         getGroupRankMsgBuilderList() {
      return internalGetGroupRankMsgFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.WeYoundUnionGroupRankMsg, xddq.pb.WeYoundUnionGroupRankMsg.Builder, xddq.pb.WeYoundUnionGroupRankMsgOrBuilder> 
        internalGetGroupRankMsgFieldBuilder() {
      if (groupRankMsgBuilder_ == null) {
        groupRankMsgBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.WeYoundUnionGroupRankMsg, xddq.pb.WeYoundUnionGroupRankMsg.Builder, xddq.pb.WeYoundUnionGroupRankMsgOrBuilder>(
                groupRankMsg_,
                ((bitField0_ & 0x00000040) != 0),
                getParentForChildren(),
                isClean());
        groupRankMsg_ = null;
      }
      return groupRankMsgBuilder_;
    }

    private long damage_ ;
    /**
     * <code>optional int64 damage = 8;</code>
     * @return Whether the damage field is set.
     */
    @java.lang.Override
    public boolean hasDamage() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <code>optional int64 damage = 8;</code>
     * @return The damage.
     */
    @java.lang.Override
    public long getDamage() {
      return damage_;
    }
    /**
     * <code>optional int64 damage = 8;</code>
     * @param value The damage to set.
     * @return This builder for chaining.
     */
    public Builder setDamage(long value) {

      damage_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 damage = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearDamage() {
      bitField0_ = (bitField0_ & ~0x00000080);
      damage_ = 0L;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.WeYoundChallengeInfoSync)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.WeYoundChallengeInfoSync)
  private static final xddq.pb.WeYoundChallengeInfoSync DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.WeYoundChallengeInfoSync();
  }

  public static xddq.pb.WeYoundChallengeInfoSync getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<WeYoundChallengeInfoSync>
      PARSER = new com.google.protobuf.AbstractParser<WeYoundChallengeInfoSync>() {
    @java.lang.Override
    public WeYoundChallengeInfoSync parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<WeYoundChallengeInfoSync> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<WeYoundChallengeInfoSync> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.WeYoundChallengeInfoSync getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

