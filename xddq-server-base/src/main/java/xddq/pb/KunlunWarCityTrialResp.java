// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.KunlunWarCityTrialResp}
 */
public final class KunlunWarCityTrialResp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.KunlunWarCityTrialResp)
    KunlunWarCityTrialRespOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      KunlunWarCityTrialResp.class.getName());
  }
  // Use KunlunWarCityTrialResp.newBuilder() to construct.
  private KunlunWarCityTrialResp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private KunlunWarCityTrialResp() {
    reward_ = "";
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_KunlunWarCityTrialResp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_KunlunWarCityTrialResp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.KunlunWarCityTrialResp.class, xddq.pb.KunlunWarCityTrialResp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>optional int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int REWARD_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private volatile java.lang.Object reward_ = "";
  /**
   * <code>optional string reward = 2;</code>
   * @return Whether the reward field is set.
   */
  @java.lang.Override
  public boolean hasReward() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional string reward = 2;</code>
   * @return The reward.
   */
  @java.lang.Override
  public java.lang.String getReward() {
    java.lang.Object ref = reward_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        reward_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string reward = 2;</code>
   * @return The bytes for reward.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getRewardBytes() {
    java.lang.Object ref = reward_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      reward_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ID_FIELD_NUMBER = 3;
  private int id_ = 0;
  /**
   * <code>optional int32 id = 3;</code>
   * @return Whether the id field is set.
   */
  @java.lang.Override
  public boolean hasId() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional int32 id = 3;</code>
   * @return The id.
   */
  @java.lang.Override
  public int getId() {
    return id_;
  }

  public static final int ENERGY_FIELD_NUMBER = 4;
  private int energy_ = 0;
  /**
   * <code>optional int32 energy = 4;</code>
   * @return Whether the energy field is set.
   */
  @java.lang.Override
  public boolean hasEnergy() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional int32 energy = 4;</code>
   * @return The energy.
   */
  @java.lang.Override
  public int getEnergy() {
    return energy_;
  }

  public static final int ENERGYRECOVERTIME_FIELD_NUMBER = 5;
  private long energyRecoverTime_ = 0L;
  /**
   * <code>optional int64 energyRecoverTime = 5;</code>
   * @return Whether the energyRecoverTime field is set.
   */
  @java.lang.Override
  public boolean hasEnergyRecoverTime() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>optional int64 energyRecoverTime = 5;</code>
   * @return The energyRecoverTime.
   */
  @java.lang.Override
  public long getEnergyRecoverTime() {
    return energyRecoverTime_;
  }

  public static final int RANK_FIELD_NUMBER = 6;
  private int rank_ = 0;
  /**
   * <code>optional int32 rank = 6;</code>
   * @return Whether the rank field is set.
   */
  @java.lang.Override
  public boolean hasRank() {
    return ((bitField0_ & 0x00000020) != 0);
  }
  /**
   * <code>optional int32 rank = 6;</code>
   * @return The rank.
   */
  @java.lang.Override
  public int getRank() {
    return rank_;
  }

  public static final int RANKSCORE_FIELD_NUMBER = 7;
  private int rankScore_ = 0;
  /**
   * <code>optional int32 rankScore = 7;</code>
   * @return Whether the rankScore field is set.
   */
  @java.lang.Override
  public boolean hasRankScore() {
    return ((bitField0_ & 0x00000040) != 0);
  }
  /**
   * <code>optional int32 rankScore = 7;</code>
   * @return The rankScore.
   */
  @java.lang.Override
  public int getRankScore() {
    return rankScore_;
  }

  public static final int TOTALCOSTENERGY_FIELD_NUMBER = 8;
  private int totalCostEnergy_ = 0;
  /**
   * <code>optional int32 totalCostEnergy = 8;</code>
   * @return Whether the totalCostEnergy field is set.
   */
  @java.lang.Override
  public boolean hasTotalCostEnergy() {
    return ((bitField0_ & 0x00000080) != 0);
  }
  /**
   * <code>optional int32 totalCostEnergy = 8;</code>
   * @return The totalCostEnergy.
   */
  @java.lang.Override
  public int getTotalCostEnergy() {
    return totalCostEnergy_;
  }

  public static final int EXP_FIELD_NUMBER = 9;
  private long exp_ = 0L;
  /**
   * <code>optional int64 exp = 9;</code>
   * @return Whether the exp field is set.
   */
  @java.lang.Override
  public boolean hasExp() {
    return ((bitField0_ & 0x00000100) != 0);
  }
  /**
   * <code>optional int64 exp = 9;</code>
   * @return The exp.
   */
  @java.lang.Override
  public long getExp() {
    return exp_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 2, reward_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt32(3, id_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt32(4, energy_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      output.writeInt64(5, energyRecoverTime_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      output.writeInt32(6, rank_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      output.writeInt32(7, rankScore_);
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      output.writeInt32(8, totalCostEnergy_);
    }
    if (((bitField0_ & 0x00000100) != 0)) {
      output.writeInt64(9, exp_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(2, reward_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(3, id_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(4, energy_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(5, energyRecoverTime_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(6, rank_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(7, rankScore_);
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(8, totalCostEnergy_);
    }
    if (((bitField0_ & 0x00000100) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(9, exp_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.KunlunWarCityTrialResp)) {
      return super.equals(obj);
    }
    xddq.pb.KunlunWarCityTrialResp other = (xddq.pb.KunlunWarCityTrialResp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (hasReward() != other.hasReward()) return false;
    if (hasReward()) {
      if (!getReward()
          .equals(other.getReward())) return false;
    }
    if (hasId() != other.hasId()) return false;
    if (hasId()) {
      if (getId()
          != other.getId()) return false;
    }
    if (hasEnergy() != other.hasEnergy()) return false;
    if (hasEnergy()) {
      if (getEnergy()
          != other.getEnergy()) return false;
    }
    if (hasEnergyRecoverTime() != other.hasEnergyRecoverTime()) return false;
    if (hasEnergyRecoverTime()) {
      if (getEnergyRecoverTime()
          != other.getEnergyRecoverTime()) return false;
    }
    if (hasRank() != other.hasRank()) return false;
    if (hasRank()) {
      if (getRank()
          != other.getRank()) return false;
    }
    if (hasRankScore() != other.hasRankScore()) return false;
    if (hasRankScore()) {
      if (getRankScore()
          != other.getRankScore()) return false;
    }
    if (hasTotalCostEnergy() != other.hasTotalCostEnergy()) return false;
    if (hasTotalCostEnergy()) {
      if (getTotalCostEnergy()
          != other.getTotalCostEnergy()) return false;
    }
    if (hasExp() != other.hasExp()) return false;
    if (hasExp()) {
      if (getExp()
          != other.getExp()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (hasReward()) {
      hash = (37 * hash) + REWARD_FIELD_NUMBER;
      hash = (53 * hash) + getReward().hashCode();
    }
    if (hasId()) {
      hash = (37 * hash) + ID_FIELD_NUMBER;
      hash = (53 * hash) + getId();
    }
    if (hasEnergy()) {
      hash = (37 * hash) + ENERGY_FIELD_NUMBER;
      hash = (53 * hash) + getEnergy();
    }
    if (hasEnergyRecoverTime()) {
      hash = (37 * hash) + ENERGYRECOVERTIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getEnergyRecoverTime());
    }
    if (hasRank()) {
      hash = (37 * hash) + RANK_FIELD_NUMBER;
      hash = (53 * hash) + getRank();
    }
    if (hasRankScore()) {
      hash = (37 * hash) + RANKSCORE_FIELD_NUMBER;
      hash = (53 * hash) + getRankScore();
    }
    if (hasTotalCostEnergy()) {
      hash = (37 * hash) + TOTALCOSTENERGY_FIELD_NUMBER;
      hash = (53 * hash) + getTotalCostEnergy();
    }
    if (hasExp()) {
      hash = (37 * hash) + EXP_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getExp());
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.KunlunWarCityTrialResp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.KunlunWarCityTrialResp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.KunlunWarCityTrialResp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.KunlunWarCityTrialResp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.KunlunWarCityTrialResp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.KunlunWarCityTrialResp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.KunlunWarCityTrialResp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.KunlunWarCityTrialResp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.KunlunWarCityTrialResp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.KunlunWarCityTrialResp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.KunlunWarCityTrialResp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.KunlunWarCityTrialResp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.KunlunWarCityTrialResp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.KunlunWarCityTrialResp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.KunlunWarCityTrialResp)
      xddq.pb.KunlunWarCityTrialRespOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_KunlunWarCityTrialResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_KunlunWarCityTrialResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.KunlunWarCityTrialResp.class, xddq.pb.KunlunWarCityTrialResp.Builder.class);
    }

    // Construct using xddq.pb.KunlunWarCityTrialResp.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      reward_ = "";
      id_ = 0;
      energy_ = 0;
      energyRecoverTime_ = 0L;
      rank_ = 0;
      rankScore_ = 0;
      totalCostEnergy_ = 0;
      exp_ = 0L;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_KunlunWarCityTrialResp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.KunlunWarCityTrialResp getDefaultInstanceForType() {
      return xddq.pb.KunlunWarCityTrialResp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.KunlunWarCityTrialResp build() {
      xddq.pb.KunlunWarCityTrialResp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.KunlunWarCityTrialResp buildPartial() {
      xddq.pb.KunlunWarCityTrialResp result = new xddq.pb.KunlunWarCityTrialResp(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.KunlunWarCityTrialResp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.reward_ = reward_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.id_ = id_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.energy_ = energy_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.energyRecoverTime_ = energyRecoverTime_;
        to_bitField0_ |= 0x00000010;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.rank_ = rank_;
        to_bitField0_ |= 0x00000020;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.rankScore_ = rankScore_;
        to_bitField0_ |= 0x00000040;
      }
      if (((from_bitField0_ & 0x00000080) != 0)) {
        result.totalCostEnergy_ = totalCostEnergy_;
        to_bitField0_ |= 0x00000080;
      }
      if (((from_bitField0_ & 0x00000100) != 0)) {
        result.exp_ = exp_;
        to_bitField0_ |= 0x00000100;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.KunlunWarCityTrialResp) {
        return mergeFrom((xddq.pb.KunlunWarCityTrialResp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.KunlunWarCityTrialResp other) {
      if (other == xddq.pb.KunlunWarCityTrialResp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (other.hasReward()) {
        reward_ = other.reward_;
        bitField0_ |= 0x00000002;
        onChanged();
      }
      if (other.hasId()) {
        setId(other.getId());
      }
      if (other.hasEnergy()) {
        setEnergy(other.getEnergy());
      }
      if (other.hasEnergyRecoverTime()) {
        setEnergyRecoverTime(other.getEnergyRecoverTime());
      }
      if (other.hasRank()) {
        setRank(other.getRank());
      }
      if (other.hasRankScore()) {
        setRankScore(other.getRankScore());
      }
      if (other.hasTotalCostEnergy()) {
        setTotalCostEnergy(other.getTotalCostEnergy());
      }
      if (other.hasExp()) {
        setExp(other.getExp());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              reward_ = input.readBytes();
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 24: {
              id_ = input.readInt32();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 32: {
              energy_ = input.readInt32();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            case 40: {
              energyRecoverTime_ = input.readInt64();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            case 48: {
              rank_ = input.readInt32();
              bitField0_ |= 0x00000020;
              break;
            } // case 48
            case 56: {
              rankScore_ = input.readInt32();
              bitField0_ |= 0x00000040;
              break;
            } // case 56
            case 64: {
              totalCostEnergy_ = input.readInt32();
              bitField0_ |= 0x00000080;
              break;
            } // case 64
            case 72: {
              exp_ = input.readInt64();
              bitField0_ |= 0x00000100;
              break;
            } // case 72
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>optional int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>optional int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object reward_ = "";
    /**
     * <code>optional string reward = 2;</code>
     * @return Whether the reward field is set.
     */
    public boolean hasReward() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional string reward = 2;</code>
     * @return The reward.
     */
    public java.lang.String getReward() {
      java.lang.Object ref = reward_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          reward_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string reward = 2;</code>
     * @return The bytes for reward.
     */
    public com.google.protobuf.ByteString
        getRewardBytes() {
      java.lang.Object ref = reward_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        reward_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string reward = 2;</code>
     * @param value The reward to set.
     * @return This builder for chaining.
     */
    public Builder setReward(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      reward_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional string reward = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearReward() {
      reward_ = getDefaultInstance().getReward();
      bitField0_ = (bitField0_ & ~0x00000002);
      onChanged();
      return this;
    }
    /**
     * <code>optional string reward = 2;</code>
     * @param value The bytes for reward to set.
     * @return This builder for chaining.
     */
    public Builder setRewardBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      reward_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }

    private int id_ ;
    /**
     * <code>optional int32 id = 3;</code>
     * @return Whether the id field is set.
     */
    @java.lang.Override
    public boolean hasId() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int32 id = 3;</code>
     * @return The id.
     */
    @java.lang.Override
    public int getId() {
      return id_;
    }
    /**
     * <code>optional int32 id = 3;</code>
     * @param value The id to set.
     * @return This builder for chaining.
     */
    public Builder setId(int value) {

      id_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 id = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearId() {
      bitField0_ = (bitField0_ & ~0x00000004);
      id_ = 0;
      onChanged();
      return this;
    }

    private int energy_ ;
    /**
     * <code>optional int32 energy = 4;</code>
     * @return Whether the energy field is set.
     */
    @java.lang.Override
    public boolean hasEnergy() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int32 energy = 4;</code>
     * @return The energy.
     */
    @java.lang.Override
    public int getEnergy() {
      return energy_;
    }
    /**
     * <code>optional int32 energy = 4;</code>
     * @param value The energy to set.
     * @return This builder for chaining.
     */
    public Builder setEnergy(int value) {

      energy_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 energy = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearEnergy() {
      bitField0_ = (bitField0_ & ~0x00000008);
      energy_ = 0;
      onChanged();
      return this;
    }

    private long energyRecoverTime_ ;
    /**
     * <code>optional int64 energyRecoverTime = 5;</code>
     * @return Whether the energyRecoverTime field is set.
     */
    @java.lang.Override
    public boolean hasEnergyRecoverTime() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional int64 energyRecoverTime = 5;</code>
     * @return The energyRecoverTime.
     */
    @java.lang.Override
    public long getEnergyRecoverTime() {
      return energyRecoverTime_;
    }
    /**
     * <code>optional int64 energyRecoverTime = 5;</code>
     * @param value The energyRecoverTime to set.
     * @return This builder for chaining.
     */
    public Builder setEnergyRecoverTime(long value) {

      energyRecoverTime_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 energyRecoverTime = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearEnergyRecoverTime() {
      bitField0_ = (bitField0_ & ~0x00000010);
      energyRecoverTime_ = 0L;
      onChanged();
      return this;
    }

    private int rank_ ;
    /**
     * <code>optional int32 rank = 6;</code>
     * @return Whether the rank field is set.
     */
    @java.lang.Override
    public boolean hasRank() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional int32 rank = 6;</code>
     * @return The rank.
     */
    @java.lang.Override
    public int getRank() {
      return rank_;
    }
    /**
     * <code>optional int32 rank = 6;</code>
     * @param value The rank to set.
     * @return This builder for chaining.
     */
    public Builder setRank(int value) {

      rank_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 rank = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearRank() {
      bitField0_ = (bitField0_ & ~0x00000020);
      rank_ = 0;
      onChanged();
      return this;
    }

    private int rankScore_ ;
    /**
     * <code>optional int32 rankScore = 7;</code>
     * @return Whether the rankScore field is set.
     */
    @java.lang.Override
    public boolean hasRankScore() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <code>optional int32 rankScore = 7;</code>
     * @return The rankScore.
     */
    @java.lang.Override
    public int getRankScore() {
      return rankScore_;
    }
    /**
     * <code>optional int32 rankScore = 7;</code>
     * @param value The rankScore to set.
     * @return This builder for chaining.
     */
    public Builder setRankScore(int value) {

      rankScore_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 rankScore = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearRankScore() {
      bitField0_ = (bitField0_ & ~0x00000040);
      rankScore_ = 0;
      onChanged();
      return this;
    }

    private int totalCostEnergy_ ;
    /**
     * <code>optional int32 totalCostEnergy = 8;</code>
     * @return Whether the totalCostEnergy field is set.
     */
    @java.lang.Override
    public boolean hasTotalCostEnergy() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <code>optional int32 totalCostEnergy = 8;</code>
     * @return The totalCostEnergy.
     */
    @java.lang.Override
    public int getTotalCostEnergy() {
      return totalCostEnergy_;
    }
    /**
     * <code>optional int32 totalCostEnergy = 8;</code>
     * @param value The totalCostEnergy to set.
     * @return This builder for chaining.
     */
    public Builder setTotalCostEnergy(int value) {

      totalCostEnergy_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 totalCostEnergy = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearTotalCostEnergy() {
      bitField0_ = (bitField0_ & ~0x00000080);
      totalCostEnergy_ = 0;
      onChanged();
      return this;
    }

    private long exp_ ;
    /**
     * <code>optional int64 exp = 9;</code>
     * @return Whether the exp field is set.
     */
    @java.lang.Override
    public boolean hasExp() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <code>optional int64 exp = 9;</code>
     * @return The exp.
     */
    @java.lang.Override
    public long getExp() {
      return exp_;
    }
    /**
     * <code>optional int64 exp = 9;</code>
     * @param value The exp to set.
     * @return This builder for chaining.
     */
    public Builder setExp(long value) {

      exp_ = value;
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 exp = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearExp() {
      bitField0_ = (bitField0_ & ~0x00000100);
      exp_ = 0L;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.KunlunWarCityTrialResp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.KunlunWarCityTrialResp)
  private static final xddq.pb.KunlunWarCityTrialResp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.KunlunWarCityTrialResp();
  }

  public static xddq.pb.KunlunWarCityTrialResp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<KunlunWarCityTrialResp>
      PARSER = new com.google.protobuf.AbstractParser<KunlunWarCityTrialResp>() {
    @java.lang.Override
    public KunlunWarCityTrialResp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<KunlunWarCityTrialResp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<KunlunWarCityTrialResp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.KunlunWarCityTrialResp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

