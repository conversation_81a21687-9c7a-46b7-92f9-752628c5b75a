// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.ManHuangConfigSyncMsg}
 */
public final class ManHuangConfigSyncMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.ManHuangConfigSyncMsg)
    ManHuangConfigSyncMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      ManHuangConfigSyncMsg.class.getName());
  }
  // Use ManHuangConfigSyncMsg.newBuilder() to construct.
  private ManHuangConfigSyncMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private ManHuangConfigSyncMsg() {
    region_ = java.util.Collections.emptyList();
    event_ = java.util.Collections.emptyList();
    buff_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_ManHuangConfigSyncMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_ManHuangConfigSyncMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.ManHuangConfigSyncMsg.class, xddq.pb.ManHuangConfigSyncMsg.Builder.class);
  }

  public static final int REGION_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.ManHuangRegionInfoTemp> region_;
  /**
   * <code>repeated .xddq.pb.ManHuangRegionInfoTemp region = 1;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.ManHuangRegionInfoTemp> getRegionList() {
    return region_;
  }
  /**
   * <code>repeated .xddq.pb.ManHuangRegionInfoTemp region = 1;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.ManHuangRegionInfoTempOrBuilder> 
      getRegionOrBuilderList() {
    return region_;
  }
  /**
   * <code>repeated .xddq.pb.ManHuangRegionInfoTemp region = 1;</code>
   */
  @java.lang.Override
  public int getRegionCount() {
    return region_.size();
  }
  /**
   * <code>repeated .xddq.pb.ManHuangRegionInfoTemp region = 1;</code>
   */
  @java.lang.Override
  public xddq.pb.ManHuangRegionInfoTemp getRegion(int index) {
    return region_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.ManHuangRegionInfoTemp region = 1;</code>
   */
  @java.lang.Override
  public xddq.pb.ManHuangRegionInfoTempOrBuilder getRegionOrBuilder(
      int index) {
    return region_.get(index);
  }

  public static final int EVENT_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.ManHuangEventInfoTemp> event_;
  /**
   * <code>repeated .xddq.pb.ManHuangEventInfoTemp event = 2;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.ManHuangEventInfoTemp> getEventList() {
    return event_;
  }
  /**
   * <code>repeated .xddq.pb.ManHuangEventInfoTemp event = 2;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.ManHuangEventInfoTempOrBuilder> 
      getEventOrBuilderList() {
    return event_;
  }
  /**
   * <code>repeated .xddq.pb.ManHuangEventInfoTemp event = 2;</code>
   */
  @java.lang.Override
  public int getEventCount() {
    return event_.size();
  }
  /**
   * <code>repeated .xddq.pb.ManHuangEventInfoTemp event = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.ManHuangEventInfoTemp getEvent(int index) {
    return event_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.ManHuangEventInfoTemp event = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.ManHuangEventInfoTempOrBuilder getEventOrBuilder(
      int index) {
    return event_.get(index);
  }

  public static final int BUFF_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.ManHuangBuffInfoTemp> buff_;
  /**
   * <code>repeated .xddq.pb.ManHuangBuffInfoTemp buff = 3;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.ManHuangBuffInfoTemp> getBuffList() {
    return buff_;
  }
  /**
   * <code>repeated .xddq.pb.ManHuangBuffInfoTemp buff = 3;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.ManHuangBuffInfoTempOrBuilder> 
      getBuffOrBuilderList() {
    return buff_;
  }
  /**
   * <code>repeated .xddq.pb.ManHuangBuffInfoTemp buff = 3;</code>
   */
  @java.lang.Override
  public int getBuffCount() {
    return buff_.size();
  }
  /**
   * <code>repeated .xddq.pb.ManHuangBuffInfoTemp buff = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.ManHuangBuffInfoTemp getBuff(int index) {
    return buff_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.ManHuangBuffInfoTemp buff = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.ManHuangBuffInfoTempOrBuilder getBuffOrBuilder(
      int index) {
    return buff_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    for (int i = 0; i < getRegionCount(); i++) {
      if (!getRegion(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    for (int i = 0; i < getEventCount(); i++) {
      if (!getEvent(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    for (int i = 0; i < getBuffCount(); i++) {
      if (!getBuff(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    for (int i = 0; i < region_.size(); i++) {
      output.writeMessage(1, region_.get(i));
    }
    for (int i = 0; i < event_.size(); i++) {
      output.writeMessage(2, event_.get(i));
    }
    for (int i = 0; i < buff_.size(); i++) {
      output.writeMessage(3, buff_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    for (int i = 0; i < region_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, region_.get(i));
    }
    for (int i = 0; i < event_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, event_.get(i));
    }
    for (int i = 0; i < buff_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, buff_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.ManHuangConfigSyncMsg)) {
      return super.equals(obj);
    }
    xddq.pb.ManHuangConfigSyncMsg other = (xddq.pb.ManHuangConfigSyncMsg) obj;

    if (!getRegionList()
        .equals(other.getRegionList())) return false;
    if (!getEventList()
        .equals(other.getEventList())) return false;
    if (!getBuffList()
        .equals(other.getBuffList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (getRegionCount() > 0) {
      hash = (37 * hash) + REGION_FIELD_NUMBER;
      hash = (53 * hash) + getRegionList().hashCode();
    }
    if (getEventCount() > 0) {
      hash = (37 * hash) + EVENT_FIELD_NUMBER;
      hash = (53 * hash) + getEventList().hashCode();
    }
    if (getBuffCount() > 0) {
      hash = (37 * hash) + BUFF_FIELD_NUMBER;
      hash = (53 * hash) + getBuffList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.ManHuangConfigSyncMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ManHuangConfigSyncMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ManHuangConfigSyncMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ManHuangConfigSyncMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ManHuangConfigSyncMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ManHuangConfigSyncMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ManHuangConfigSyncMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.ManHuangConfigSyncMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.ManHuangConfigSyncMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.ManHuangConfigSyncMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.ManHuangConfigSyncMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.ManHuangConfigSyncMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.ManHuangConfigSyncMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.ManHuangConfigSyncMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.ManHuangConfigSyncMsg)
      xddq.pb.ManHuangConfigSyncMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ManHuangConfigSyncMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ManHuangConfigSyncMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.ManHuangConfigSyncMsg.class, xddq.pb.ManHuangConfigSyncMsg.Builder.class);
    }

    // Construct using xddq.pb.ManHuangConfigSyncMsg.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      if (regionBuilder_ == null) {
        region_ = java.util.Collections.emptyList();
      } else {
        region_ = null;
        regionBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000001);
      if (eventBuilder_ == null) {
        event_ = java.util.Collections.emptyList();
      } else {
        event_ = null;
        eventBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000002);
      if (buffBuilder_ == null) {
        buff_ = java.util.Collections.emptyList();
      } else {
        buff_ = null;
        buffBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000004);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ManHuangConfigSyncMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.ManHuangConfigSyncMsg getDefaultInstanceForType() {
      return xddq.pb.ManHuangConfigSyncMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.ManHuangConfigSyncMsg build() {
      xddq.pb.ManHuangConfigSyncMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.ManHuangConfigSyncMsg buildPartial() {
      xddq.pb.ManHuangConfigSyncMsg result = new xddq.pb.ManHuangConfigSyncMsg(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.ManHuangConfigSyncMsg result) {
      if (regionBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          region_ = java.util.Collections.unmodifiableList(region_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.region_ = region_;
      } else {
        result.region_ = regionBuilder_.build();
      }
      if (eventBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          event_ = java.util.Collections.unmodifiableList(event_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.event_ = event_;
      } else {
        result.event_ = eventBuilder_.build();
      }
      if (buffBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0)) {
          buff_ = java.util.Collections.unmodifiableList(buff_);
          bitField0_ = (bitField0_ & ~0x00000004);
        }
        result.buff_ = buff_;
      } else {
        result.buff_ = buffBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.ManHuangConfigSyncMsg result) {
      int from_bitField0_ = bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.ManHuangConfigSyncMsg) {
        return mergeFrom((xddq.pb.ManHuangConfigSyncMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.ManHuangConfigSyncMsg other) {
      if (other == xddq.pb.ManHuangConfigSyncMsg.getDefaultInstance()) return this;
      if (regionBuilder_ == null) {
        if (!other.region_.isEmpty()) {
          if (region_.isEmpty()) {
            region_ = other.region_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureRegionIsMutable();
            region_.addAll(other.region_);
          }
          onChanged();
        }
      } else {
        if (!other.region_.isEmpty()) {
          if (regionBuilder_.isEmpty()) {
            regionBuilder_.dispose();
            regionBuilder_ = null;
            region_ = other.region_;
            bitField0_ = (bitField0_ & ~0x00000001);
            regionBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetRegionFieldBuilder() : null;
          } else {
            regionBuilder_.addAllMessages(other.region_);
          }
        }
      }
      if (eventBuilder_ == null) {
        if (!other.event_.isEmpty()) {
          if (event_.isEmpty()) {
            event_ = other.event_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureEventIsMutable();
            event_.addAll(other.event_);
          }
          onChanged();
        }
      } else {
        if (!other.event_.isEmpty()) {
          if (eventBuilder_.isEmpty()) {
            eventBuilder_.dispose();
            eventBuilder_ = null;
            event_ = other.event_;
            bitField0_ = (bitField0_ & ~0x00000002);
            eventBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetEventFieldBuilder() : null;
          } else {
            eventBuilder_.addAllMessages(other.event_);
          }
        }
      }
      if (buffBuilder_ == null) {
        if (!other.buff_.isEmpty()) {
          if (buff_.isEmpty()) {
            buff_ = other.buff_;
            bitField0_ = (bitField0_ & ~0x00000004);
          } else {
            ensureBuffIsMutable();
            buff_.addAll(other.buff_);
          }
          onChanged();
        }
      } else {
        if (!other.buff_.isEmpty()) {
          if (buffBuilder_.isEmpty()) {
            buffBuilder_.dispose();
            buffBuilder_ = null;
            buff_ = other.buff_;
            bitField0_ = (bitField0_ & ~0x00000004);
            buffBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetBuffFieldBuilder() : null;
          } else {
            buffBuilder_.addAllMessages(other.buff_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      for (int i = 0; i < getRegionCount(); i++) {
        if (!getRegion(i).isInitialized()) {
          return false;
        }
      }
      for (int i = 0; i < getEventCount(); i++) {
        if (!getEvent(i).isInitialized()) {
          return false;
        }
      }
      for (int i = 0; i < getBuffCount(); i++) {
        if (!getBuff(i).isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              xddq.pb.ManHuangRegionInfoTemp m =
                  input.readMessage(
                      xddq.pb.ManHuangRegionInfoTemp.parser(),
                      extensionRegistry);
              if (regionBuilder_ == null) {
                ensureRegionIsMutable();
                region_.add(m);
              } else {
                regionBuilder_.addMessage(m);
              }
              break;
            } // case 10
            case 18: {
              xddq.pb.ManHuangEventInfoTemp m =
                  input.readMessage(
                      xddq.pb.ManHuangEventInfoTemp.parser(),
                      extensionRegistry);
              if (eventBuilder_ == null) {
                ensureEventIsMutable();
                event_.add(m);
              } else {
                eventBuilder_.addMessage(m);
              }
              break;
            } // case 18
            case 26: {
              xddq.pb.ManHuangBuffInfoTemp m =
                  input.readMessage(
                      xddq.pb.ManHuangBuffInfoTemp.parser(),
                      extensionRegistry);
              if (buffBuilder_ == null) {
                ensureBuffIsMutable();
                buff_.add(m);
              } else {
                buffBuilder_.addMessage(m);
              }
              break;
            } // case 26
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.util.List<xddq.pb.ManHuangRegionInfoTemp> region_ =
      java.util.Collections.emptyList();
    private void ensureRegionIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        region_ = new java.util.ArrayList<xddq.pb.ManHuangRegionInfoTemp>(region_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.ManHuangRegionInfoTemp, xddq.pb.ManHuangRegionInfoTemp.Builder, xddq.pb.ManHuangRegionInfoTempOrBuilder> regionBuilder_;

    /**
     * <code>repeated .xddq.pb.ManHuangRegionInfoTemp region = 1;</code>
     */
    public java.util.List<xddq.pb.ManHuangRegionInfoTemp> getRegionList() {
      if (regionBuilder_ == null) {
        return java.util.Collections.unmodifiableList(region_);
      } else {
        return regionBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.ManHuangRegionInfoTemp region = 1;</code>
     */
    public int getRegionCount() {
      if (regionBuilder_ == null) {
        return region_.size();
      } else {
        return regionBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.ManHuangRegionInfoTemp region = 1;</code>
     */
    public xddq.pb.ManHuangRegionInfoTemp getRegion(int index) {
      if (regionBuilder_ == null) {
        return region_.get(index);
      } else {
        return regionBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.ManHuangRegionInfoTemp region = 1;</code>
     */
    public Builder setRegion(
        int index, xddq.pb.ManHuangRegionInfoTemp value) {
      if (regionBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRegionIsMutable();
        region_.set(index, value);
        onChanged();
      } else {
        regionBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangRegionInfoTemp region = 1;</code>
     */
    public Builder setRegion(
        int index, xddq.pb.ManHuangRegionInfoTemp.Builder builderForValue) {
      if (regionBuilder_ == null) {
        ensureRegionIsMutable();
        region_.set(index, builderForValue.build());
        onChanged();
      } else {
        regionBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangRegionInfoTemp region = 1;</code>
     */
    public Builder addRegion(xddq.pb.ManHuangRegionInfoTemp value) {
      if (regionBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRegionIsMutable();
        region_.add(value);
        onChanged();
      } else {
        regionBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangRegionInfoTemp region = 1;</code>
     */
    public Builder addRegion(
        int index, xddq.pb.ManHuangRegionInfoTemp value) {
      if (regionBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRegionIsMutable();
        region_.add(index, value);
        onChanged();
      } else {
        regionBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangRegionInfoTemp region = 1;</code>
     */
    public Builder addRegion(
        xddq.pb.ManHuangRegionInfoTemp.Builder builderForValue) {
      if (regionBuilder_ == null) {
        ensureRegionIsMutable();
        region_.add(builderForValue.build());
        onChanged();
      } else {
        regionBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangRegionInfoTemp region = 1;</code>
     */
    public Builder addRegion(
        int index, xddq.pb.ManHuangRegionInfoTemp.Builder builderForValue) {
      if (regionBuilder_ == null) {
        ensureRegionIsMutable();
        region_.add(index, builderForValue.build());
        onChanged();
      } else {
        regionBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangRegionInfoTemp region = 1;</code>
     */
    public Builder addAllRegion(
        java.lang.Iterable<? extends xddq.pb.ManHuangRegionInfoTemp> values) {
      if (regionBuilder_ == null) {
        ensureRegionIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, region_);
        onChanged();
      } else {
        regionBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangRegionInfoTemp region = 1;</code>
     */
    public Builder clearRegion() {
      if (regionBuilder_ == null) {
        region_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        regionBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangRegionInfoTemp region = 1;</code>
     */
    public Builder removeRegion(int index) {
      if (regionBuilder_ == null) {
        ensureRegionIsMutable();
        region_.remove(index);
        onChanged();
      } else {
        regionBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangRegionInfoTemp region = 1;</code>
     */
    public xddq.pb.ManHuangRegionInfoTemp.Builder getRegionBuilder(
        int index) {
      return internalGetRegionFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.ManHuangRegionInfoTemp region = 1;</code>
     */
    public xddq.pb.ManHuangRegionInfoTempOrBuilder getRegionOrBuilder(
        int index) {
      if (regionBuilder_ == null) {
        return region_.get(index);  } else {
        return regionBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.ManHuangRegionInfoTemp region = 1;</code>
     */
    public java.util.List<? extends xddq.pb.ManHuangRegionInfoTempOrBuilder> 
         getRegionOrBuilderList() {
      if (regionBuilder_ != null) {
        return regionBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(region_);
      }
    }
    /**
     * <code>repeated .xddq.pb.ManHuangRegionInfoTemp region = 1;</code>
     */
    public xddq.pb.ManHuangRegionInfoTemp.Builder addRegionBuilder() {
      return internalGetRegionFieldBuilder().addBuilder(
          xddq.pb.ManHuangRegionInfoTemp.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.ManHuangRegionInfoTemp region = 1;</code>
     */
    public xddq.pb.ManHuangRegionInfoTemp.Builder addRegionBuilder(
        int index) {
      return internalGetRegionFieldBuilder().addBuilder(
          index, xddq.pb.ManHuangRegionInfoTemp.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.ManHuangRegionInfoTemp region = 1;</code>
     */
    public java.util.List<xddq.pb.ManHuangRegionInfoTemp.Builder> 
         getRegionBuilderList() {
      return internalGetRegionFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.ManHuangRegionInfoTemp, xddq.pb.ManHuangRegionInfoTemp.Builder, xddq.pb.ManHuangRegionInfoTempOrBuilder> 
        internalGetRegionFieldBuilder() {
      if (regionBuilder_ == null) {
        regionBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.ManHuangRegionInfoTemp, xddq.pb.ManHuangRegionInfoTemp.Builder, xddq.pb.ManHuangRegionInfoTempOrBuilder>(
                region_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        region_ = null;
      }
      return regionBuilder_;
    }

    private java.util.List<xddq.pb.ManHuangEventInfoTemp> event_ =
      java.util.Collections.emptyList();
    private void ensureEventIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        event_ = new java.util.ArrayList<xddq.pb.ManHuangEventInfoTemp>(event_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.ManHuangEventInfoTemp, xddq.pb.ManHuangEventInfoTemp.Builder, xddq.pb.ManHuangEventInfoTempOrBuilder> eventBuilder_;

    /**
     * <code>repeated .xddq.pb.ManHuangEventInfoTemp event = 2;</code>
     */
    public java.util.List<xddq.pb.ManHuangEventInfoTemp> getEventList() {
      if (eventBuilder_ == null) {
        return java.util.Collections.unmodifiableList(event_);
      } else {
        return eventBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.ManHuangEventInfoTemp event = 2;</code>
     */
    public int getEventCount() {
      if (eventBuilder_ == null) {
        return event_.size();
      } else {
        return eventBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.ManHuangEventInfoTemp event = 2;</code>
     */
    public xddq.pb.ManHuangEventInfoTemp getEvent(int index) {
      if (eventBuilder_ == null) {
        return event_.get(index);
      } else {
        return eventBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.ManHuangEventInfoTemp event = 2;</code>
     */
    public Builder setEvent(
        int index, xddq.pb.ManHuangEventInfoTemp value) {
      if (eventBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureEventIsMutable();
        event_.set(index, value);
        onChanged();
      } else {
        eventBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangEventInfoTemp event = 2;</code>
     */
    public Builder setEvent(
        int index, xddq.pb.ManHuangEventInfoTemp.Builder builderForValue) {
      if (eventBuilder_ == null) {
        ensureEventIsMutable();
        event_.set(index, builderForValue.build());
        onChanged();
      } else {
        eventBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangEventInfoTemp event = 2;</code>
     */
    public Builder addEvent(xddq.pb.ManHuangEventInfoTemp value) {
      if (eventBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureEventIsMutable();
        event_.add(value);
        onChanged();
      } else {
        eventBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangEventInfoTemp event = 2;</code>
     */
    public Builder addEvent(
        int index, xddq.pb.ManHuangEventInfoTemp value) {
      if (eventBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureEventIsMutable();
        event_.add(index, value);
        onChanged();
      } else {
        eventBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangEventInfoTemp event = 2;</code>
     */
    public Builder addEvent(
        xddq.pb.ManHuangEventInfoTemp.Builder builderForValue) {
      if (eventBuilder_ == null) {
        ensureEventIsMutable();
        event_.add(builderForValue.build());
        onChanged();
      } else {
        eventBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangEventInfoTemp event = 2;</code>
     */
    public Builder addEvent(
        int index, xddq.pb.ManHuangEventInfoTemp.Builder builderForValue) {
      if (eventBuilder_ == null) {
        ensureEventIsMutable();
        event_.add(index, builderForValue.build());
        onChanged();
      } else {
        eventBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangEventInfoTemp event = 2;</code>
     */
    public Builder addAllEvent(
        java.lang.Iterable<? extends xddq.pb.ManHuangEventInfoTemp> values) {
      if (eventBuilder_ == null) {
        ensureEventIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, event_);
        onChanged();
      } else {
        eventBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangEventInfoTemp event = 2;</code>
     */
    public Builder clearEvent() {
      if (eventBuilder_ == null) {
        event_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        eventBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangEventInfoTemp event = 2;</code>
     */
    public Builder removeEvent(int index) {
      if (eventBuilder_ == null) {
        ensureEventIsMutable();
        event_.remove(index);
        onChanged();
      } else {
        eventBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangEventInfoTemp event = 2;</code>
     */
    public xddq.pb.ManHuangEventInfoTemp.Builder getEventBuilder(
        int index) {
      return internalGetEventFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.ManHuangEventInfoTemp event = 2;</code>
     */
    public xddq.pb.ManHuangEventInfoTempOrBuilder getEventOrBuilder(
        int index) {
      if (eventBuilder_ == null) {
        return event_.get(index);  } else {
        return eventBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.ManHuangEventInfoTemp event = 2;</code>
     */
    public java.util.List<? extends xddq.pb.ManHuangEventInfoTempOrBuilder> 
         getEventOrBuilderList() {
      if (eventBuilder_ != null) {
        return eventBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(event_);
      }
    }
    /**
     * <code>repeated .xddq.pb.ManHuangEventInfoTemp event = 2;</code>
     */
    public xddq.pb.ManHuangEventInfoTemp.Builder addEventBuilder() {
      return internalGetEventFieldBuilder().addBuilder(
          xddq.pb.ManHuangEventInfoTemp.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.ManHuangEventInfoTemp event = 2;</code>
     */
    public xddq.pb.ManHuangEventInfoTemp.Builder addEventBuilder(
        int index) {
      return internalGetEventFieldBuilder().addBuilder(
          index, xddq.pb.ManHuangEventInfoTemp.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.ManHuangEventInfoTemp event = 2;</code>
     */
    public java.util.List<xddq.pb.ManHuangEventInfoTemp.Builder> 
         getEventBuilderList() {
      return internalGetEventFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.ManHuangEventInfoTemp, xddq.pb.ManHuangEventInfoTemp.Builder, xddq.pb.ManHuangEventInfoTempOrBuilder> 
        internalGetEventFieldBuilder() {
      if (eventBuilder_ == null) {
        eventBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.ManHuangEventInfoTemp, xddq.pb.ManHuangEventInfoTemp.Builder, xddq.pb.ManHuangEventInfoTempOrBuilder>(
                event_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        event_ = null;
      }
      return eventBuilder_;
    }

    private java.util.List<xddq.pb.ManHuangBuffInfoTemp> buff_ =
      java.util.Collections.emptyList();
    private void ensureBuffIsMutable() {
      if (!((bitField0_ & 0x00000004) != 0)) {
        buff_ = new java.util.ArrayList<xddq.pb.ManHuangBuffInfoTemp>(buff_);
        bitField0_ |= 0x00000004;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.ManHuangBuffInfoTemp, xddq.pb.ManHuangBuffInfoTemp.Builder, xddq.pb.ManHuangBuffInfoTempOrBuilder> buffBuilder_;

    /**
     * <code>repeated .xddq.pb.ManHuangBuffInfoTemp buff = 3;</code>
     */
    public java.util.List<xddq.pb.ManHuangBuffInfoTemp> getBuffList() {
      if (buffBuilder_ == null) {
        return java.util.Collections.unmodifiableList(buff_);
      } else {
        return buffBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.ManHuangBuffInfoTemp buff = 3;</code>
     */
    public int getBuffCount() {
      if (buffBuilder_ == null) {
        return buff_.size();
      } else {
        return buffBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.ManHuangBuffInfoTemp buff = 3;</code>
     */
    public xddq.pb.ManHuangBuffInfoTemp getBuff(int index) {
      if (buffBuilder_ == null) {
        return buff_.get(index);
      } else {
        return buffBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.ManHuangBuffInfoTemp buff = 3;</code>
     */
    public Builder setBuff(
        int index, xddq.pb.ManHuangBuffInfoTemp value) {
      if (buffBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureBuffIsMutable();
        buff_.set(index, value);
        onChanged();
      } else {
        buffBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangBuffInfoTemp buff = 3;</code>
     */
    public Builder setBuff(
        int index, xddq.pb.ManHuangBuffInfoTemp.Builder builderForValue) {
      if (buffBuilder_ == null) {
        ensureBuffIsMutable();
        buff_.set(index, builderForValue.build());
        onChanged();
      } else {
        buffBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangBuffInfoTemp buff = 3;</code>
     */
    public Builder addBuff(xddq.pb.ManHuangBuffInfoTemp value) {
      if (buffBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureBuffIsMutable();
        buff_.add(value);
        onChanged();
      } else {
        buffBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangBuffInfoTemp buff = 3;</code>
     */
    public Builder addBuff(
        int index, xddq.pb.ManHuangBuffInfoTemp value) {
      if (buffBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureBuffIsMutable();
        buff_.add(index, value);
        onChanged();
      } else {
        buffBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangBuffInfoTemp buff = 3;</code>
     */
    public Builder addBuff(
        xddq.pb.ManHuangBuffInfoTemp.Builder builderForValue) {
      if (buffBuilder_ == null) {
        ensureBuffIsMutable();
        buff_.add(builderForValue.build());
        onChanged();
      } else {
        buffBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangBuffInfoTemp buff = 3;</code>
     */
    public Builder addBuff(
        int index, xddq.pb.ManHuangBuffInfoTemp.Builder builderForValue) {
      if (buffBuilder_ == null) {
        ensureBuffIsMutable();
        buff_.add(index, builderForValue.build());
        onChanged();
      } else {
        buffBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangBuffInfoTemp buff = 3;</code>
     */
    public Builder addAllBuff(
        java.lang.Iterable<? extends xddq.pb.ManHuangBuffInfoTemp> values) {
      if (buffBuilder_ == null) {
        ensureBuffIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, buff_);
        onChanged();
      } else {
        buffBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangBuffInfoTemp buff = 3;</code>
     */
    public Builder clearBuff() {
      if (buffBuilder_ == null) {
        buff_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
      } else {
        buffBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangBuffInfoTemp buff = 3;</code>
     */
    public Builder removeBuff(int index) {
      if (buffBuilder_ == null) {
        ensureBuffIsMutable();
        buff_.remove(index);
        onChanged();
      } else {
        buffBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangBuffInfoTemp buff = 3;</code>
     */
    public xddq.pb.ManHuangBuffInfoTemp.Builder getBuffBuilder(
        int index) {
      return internalGetBuffFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.ManHuangBuffInfoTemp buff = 3;</code>
     */
    public xddq.pb.ManHuangBuffInfoTempOrBuilder getBuffOrBuilder(
        int index) {
      if (buffBuilder_ == null) {
        return buff_.get(index);  } else {
        return buffBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.ManHuangBuffInfoTemp buff = 3;</code>
     */
    public java.util.List<? extends xddq.pb.ManHuangBuffInfoTempOrBuilder> 
         getBuffOrBuilderList() {
      if (buffBuilder_ != null) {
        return buffBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(buff_);
      }
    }
    /**
     * <code>repeated .xddq.pb.ManHuangBuffInfoTemp buff = 3;</code>
     */
    public xddq.pb.ManHuangBuffInfoTemp.Builder addBuffBuilder() {
      return internalGetBuffFieldBuilder().addBuilder(
          xddq.pb.ManHuangBuffInfoTemp.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.ManHuangBuffInfoTemp buff = 3;</code>
     */
    public xddq.pb.ManHuangBuffInfoTemp.Builder addBuffBuilder(
        int index) {
      return internalGetBuffFieldBuilder().addBuilder(
          index, xddq.pb.ManHuangBuffInfoTemp.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.ManHuangBuffInfoTemp buff = 3;</code>
     */
    public java.util.List<xddq.pb.ManHuangBuffInfoTemp.Builder> 
         getBuffBuilderList() {
      return internalGetBuffFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.ManHuangBuffInfoTemp, xddq.pb.ManHuangBuffInfoTemp.Builder, xddq.pb.ManHuangBuffInfoTempOrBuilder> 
        internalGetBuffFieldBuilder() {
      if (buffBuilder_ == null) {
        buffBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.ManHuangBuffInfoTemp, xddq.pb.ManHuangBuffInfoTemp.Builder, xddq.pb.ManHuangBuffInfoTempOrBuilder>(
                buff_,
                ((bitField0_ & 0x00000004) != 0),
                getParentForChildren(),
                isClean());
        buff_ = null;
      }
      return buffBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.ManHuangConfigSyncMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.ManHuangConfigSyncMsg)
  private static final xddq.pb.ManHuangConfigSyncMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.ManHuangConfigSyncMsg();
  }

  public static xddq.pb.ManHuangConfigSyncMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ManHuangConfigSyncMsg>
      PARSER = new com.google.protobuf.AbstractParser<ManHuangConfigSyncMsg>() {
    @java.lang.Override
    public ManHuangConfigSyncMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<ManHuangConfigSyncMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ManHuangConfigSyncMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.ManHuangConfigSyncMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

