// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.ElementalBondsMallSyncRespMsg}
 */
public final class ElementalBondsMallSyncRespMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.ElementalBondsMallSyncRespMsg)
    ElementalBondsMallSyncRespMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      ElementalBondsMallSyncRespMsg.class.getName());
  }
  // Use ElementalBondsMallSyncRespMsg.newBuilder() to construct.
  private ElementalBondsMallSyncRespMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private ElementalBondsMallSyncRespMsg() {
    mallMsg_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_ElementalBondsMallSyncRespMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_ElementalBondsMallSyncRespMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.ElementalBondsMallSyncRespMsg.class, xddq.pb.ElementalBondsMallSyncRespMsg.Builder.class);
  }

  public static final int MALLMSG_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.ElementalBondsMallMsg> mallMsg_;
  /**
   * <code>repeated .xddq.pb.ElementalBondsMallMsg mallMsg = 1;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.ElementalBondsMallMsg> getMallMsgList() {
    return mallMsg_;
  }
  /**
   * <code>repeated .xddq.pb.ElementalBondsMallMsg mallMsg = 1;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.ElementalBondsMallMsgOrBuilder> 
      getMallMsgOrBuilderList() {
    return mallMsg_;
  }
  /**
   * <code>repeated .xddq.pb.ElementalBondsMallMsg mallMsg = 1;</code>
   */
  @java.lang.Override
  public int getMallMsgCount() {
    return mallMsg_.size();
  }
  /**
   * <code>repeated .xddq.pb.ElementalBondsMallMsg mallMsg = 1;</code>
   */
  @java.lang.Override
  public xddq.pb.ElementalBondsMallMsg getMallMsg(int index) {
    return mallMsg_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.ElementalBondsMallMsg mallMsg = 1;</code>
   */
  @java.lang.Override
  public xddq.pb.ElementalBondsMallMsgOrBuilder getMallMsgOrBuilder(
      int index) {
    return mallMsg_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    for (int i = 0; i < getMallMsgCount(); i++) {
      if (!getMallMsg(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    for (int i = 0; i < mallMsg_.size(); i++) {
      output.writeMessage(1, mallMsg_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    for (int i = 0; i < mallMsg_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, mallMsg_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.ElementalBondsMallSyncRespMsg)) {
      return super.equals(obj);
    }
    xddq.pb.ElementalBondsMallSyncRespMsg other = (xddq.pb.ElementalBondsMallSyncRespMsg) obj;

    if (!getMallMsgList()
        .equals(other.getMallMsgList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (getMallMsgCount() > 0) {
      hash = (37 * hash) + MALLMSG_FIELD_NUMBER;
      hash = (53 * hash) + getMallMsgList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.ElementalBondsMallSyncRespMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ElementalBondsMallSyncRespMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ElementalBondsMallSyncRespMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ElementalBondsMallSyncRespMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ElementalBondsMallSyncRespMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ElementalBondsMallSyncRespMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ElementalBondsMallSyncRespMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.ElementalBondsMallSyncRespMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.ElementalBondsMallSyncRespMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.ElementalBondsMallSyncRespMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.ElementalBondsMallSyncRespMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.ElementalBondsMallSyncRespMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.ElementalBondsMallSyncRespMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.ElementalBondsMallSyncRespMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.ElementalBondsMallSyncRespMsg)
      xddq.pb.ElementalBondsMallSyncRespMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ElementalBondsMallSyncRespMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ElementalBondsMallSyncRespMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.ElementalBondsMallSyncRespMsg.class, xddq.pb.ElementalBondsMallSyncRespMsg.Builder.class);
    }

    // Construct using xddq.pb.ElementalBondsMallSyncRespMsg.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      if (mallMsgBuilder_ == null) {
        mallMsg_ = java.util.Collections.emptyList();
      } else {
        mallMsg_ = null;
        mallMsgBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000001);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ElementalBondsMallSyncRespMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.ElementalBondsMallSyncRespMsg getDefaultInstanceForType() {
      return xddq.pb.ElementalBondsMallSyncRespMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.ElementalBondsMallSyncRespMsg build() {
      xddq.pb.ElementalBondsMallSyncRespMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.ElementalBondsMallSyncRespMsg buildPartial() {
      xddq.pb.ElementalBondsMallSyncRespMsg result = new xddq.pb.ElementalBondsMallSyncRespMsg(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.ElementalBondsMallSyncRespMsg result) {
      if (mallMsgBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          mallMsg_ = java.util.Collections.unmodifiableList(mallMsg_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.mallMsg_ = mallMsg_;
      } else {
        result.mallMsg_ = mallMsgBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.ElementalBondsMallSyncRespMsg result) {
      int from_bitField0_ = bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.ElementalBondsMallSyncRespMsg) {
        return mergeFrom((xddq.pb.ElementalBondsMallSyncRespMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.ElementalBondsMallSyncRespMsg other) {
      if (other == xddq.pb.ElementalBondsMallSyncRespMsg.getDefaultInstance()) return this;
      if (mallMsgBuilder_ == null) {
        if (!other.mallMsg_.isEmpty()) {
          if (mallMsg_.isEmpty()) {
            mallMsg_ = other.mallMsg_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureMallMsgIsMutable();
            mallMsg_.addAll(other.mallMsg_);
          }
          onChanged();
        }
      } else {
        if (!other.mallMsg_.isEmpty()) {
          if (mallMsgBuilder_.isEmpty()) {
            mallMsgBuilder_.dispose();
            mallMsgBuilder_ = null;
            mallMsg_ = other.mallMsg_;
            bitField0_ = (bitField0_ & ~0x00000001);
            mallMsgBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetMallMsgFieldBuilder() : null;
          } else {
            mallMsgBuilder_.addAllMessages(other.mallMsg_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      for (int i = 0; i < getMallMsgCount(); i++) {
        if (!getMallMsg(i).isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              xddq.pb.ElementalBondsMallMsg m =
                  input.readMessage(
                      xddq.pb.ElementalBondsMallMsg.parser(),
                      extensionRegistry);
              if (mallMsgBuilder_ == null) {
                ensureMallMsgIsMutable();
                mallMsg_.add(m);
              } else {
                mallMsgBuilder_.addMessage(m);
              }
              break;
            } // case 10
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.util.List<xddq.pb.ElementalBondsMallMsg> mallMsg_ =
      java.util.Collections.emptyList();
    private void ensureMallMsgIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        mallMsg_ = new java.util.ArrayList<xddq.pb.ElementalBondsMallMsg>(mallMsg_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.ElementalBondsMallMsg, xddq.pb.ElementalBondsMallMsg.Builder, xddq.pb.ElementalBondsMallMsgOrBuilder> mallMsgBuilder_;

    /**
     * <code>repeated .xddq.pb.ElementalBondsMallMsg mallMsg = 1;</code>
     */
    public java.util.List<xddq.pb.ElementalBondsMallMsg> getMallMsgList() {
      if (mallMsgBuilder_ == null) {
        return java.util.Collections.unmodifiableList(mallMsg_);
      } else {
        return mallMsgBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsMallMsg mallMsg = 1;</code>
     */
    public int getMallMsgCount() {
      if (mallMsgBuilder_ == null) {
        return mallMsg_.size();
      } else {
        return mallMsgBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsMallMsg mallMsg = 1;</code>
     */
    public xddq.pb.ElementalBondsMallMsg getMallMsg(int index) {
      if (mallMsgBuilder_ == null) {
        return mallMsg_.get(index);
      } else {
        return mallMsgBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsMallMsg mallMsg = 1;</code>
     */
    public Builder setMallMsg(
        int index, xddq.pb.ElementalBondsMallMsg value) {
      if (mallMsgBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMallMsgIsMutable();
        mallMsg_.set(index, value);
        onChanged();
      } else {
        mallMsgBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsMallMsg mallMsg = 1;</code>
     */
    public Builder setMallMsg(
        int index, xddq.pb.ElementalBondsMallMsg.Builder builderForValue) {
      if (mallMsgBuilder_ == null) {
        ensureMallMsgIsMutable();
        mallMsg_.set(index, builderForValue.build());
        onChanged();
      } else {
        mallMsgBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsMallMsg mallMsg = 1;</code>
     */
    public Builder addMallMsg(xddq.pb.ElementalBondsMallMsg value) {
      if (mallMsgBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMallMsgIsMutable();
        mallMsg_.add(value);
        onChanged();
      } else {
        mallMsgBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsMallMsg mallMsg = 1;</code>
     */
    public Builder addMallMsg(
        int index, xddq.pb.ElementalBondsMallMsg value) {
      if (mallMsgBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMallMsgIsMutable();
        mallMsg_.add(index, value);
        onChanged();
      } else {
        mallMsgBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsMallMsg mallMsg = 1;</code>
     */
    public Builder addMallMsg(
        xddq.pb.ElementalBondsMallMsg.Builder builderForValue) {
      if (mallMsgBuilder_ == null) {
        ensureMallMsgIsMutable();
        mallMsg_.add(builderForValue.build());
        onChanged();
      } else {
        mallMsgBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsMallMsg mallMsg = 1;</code>
     */
    public Builder addMallMsg(
        int index, xddq.pb.ElementalBondsMallMsg.Builder builderForValue) {
      if (mallMsgBuilder_ == null) {
        ensureMallMsgIsMutable();
        mallMsg_.add(index, builderForValue.build());
        onChanged();
      } else {
        mallMsgBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsMallMsg mallMsg = 1;</code>
     */
    public Builder addAllMallMsg(
        java.lang.Iterable<? extends xddq.pb.ElementalBondsMallMsg> values) {
      if (mallMsgBuilder_ == null) {
        ensureMallMsgIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, mallMsg_);
        onChanged();
      } else {
        mallMsgBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsMallMsg mallMsg = 1;</code>
     */
    public Builder clearMallMsg() {
      if (mallMsgBuilder_ == null) {
        mallMsg_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        mallMsgBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsMallMsg mallMsg = 1;</code>
     */
    public Builder removeMallMsg(int index) {
      if (mallMsgBuilder_ == null) {
        ensureMallMsgIsMutable();
        mallMsg_.remove(index);
        onChanged();
      } else {
        mallMsgBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsMallMsg mallMsg = 1;</code>
     */
    public xddq.pb.ElementalBondsMallMsg.Builder getMallMsgBuilder(
        int index) {
      return internalGetMallMsgFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsMallMsg mallMsg = 1;</code>
     */
    public xddq.pb.ElementalBondsMallMsgOrBuilder getMallMsgOrBuilder(
        int index) {
      if (mallMsgBuilder_ == null) {
        return mallMsg_.get(index);  } else {
        return mallMsgBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsMallMsg mallMsg = 1;</code>
     */
    public java.util.List<? extends xddq.pb.ElementalBondsMallMsgOrBuilder> 
         getMallMsgOrBuilderList() {
      if (mallMsgBuilder_ != null) {
        return mallMsgBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(mallMsg_);
      }
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsMallMsg mallMsg = 1;</code>
     */
    public xddq.pb.ElementalBondsMallMsg.Builder addMallMsgBuilder() {
      return internalGetMallMsgFieldBuilder().addBuilder(
          xddq.pb.ElementalBondsMallMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsMallMsg mallMsg = 1;</code>
     */
    public xddq.pb.ElementalBondsMallMsg.Builder addMallMsgBuilder(
        int index) {
      return internalGetMallMsgFieldBuilder().addBuilder(
          index, xddq.pb.ElementalBondsMallMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsMallMsg mallMsg = 1;</code>
     */
    public java.util.List<xddq.pb.ElementalBondsMallMsg.Builder> 
         getMallMsgBuilderList() {
      return internalGetMallMsgFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.ElementalBondsMallMsg, xddq.pb.ElementalBondsMallMsg.Builder, xddq.pb.ElementalBondsMallMsgOrBuilder> 
        internalGetMallMsgFieldBuilder() {
      if (mallMsgBuilder_ == null) {
        mallMsgBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.ElementalBondsMallMsg, xddq.pb.ElementalBondsMallMsg.Builder, xddq.pb.ElementalBondsMallMsgOrBuilder>(
                mallMsg_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        mallMsg_ = null;
      }
      return mallMsgBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.ElementalBondsMallSyncRespMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.ElementalBondsMallSyncRespMsg)
  private static final xddq.pb.ElementalBondsMallSyncRespMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.ElementalBondsMallSyncRespMsg();
  }

  public static xddq.pb.ElementalBondsMallSyncRespMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ElementalBondsMallSyncRespMsg>
      PARSER = new com.google.protobuf.AbstractParser<ElementalBondsMallSyncRespMsg>() {
    @java.lang.Override
    public ElementalBondsMallSyncRespMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<ElementalBondsMallSyncRespMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ElementalBondsMallSyncRespMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.ElementalBondsMallSyncRespMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

