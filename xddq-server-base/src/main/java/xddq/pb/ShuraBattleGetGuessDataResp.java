// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.ShuraBattleGetGuessDataResp}
 */
public final class ShuraBattleGetGuessDataResp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.ShuraBattleGetGuessDataResp)
    ShuraBattleGetGuessDataRespOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      ShuraBattleGetGuessDataResp.class.getName());
  }
  // Use ShuraBattleGetGuessDataResp.newBuilder() to construct.
  private ShuraBattleGetGuessDataResp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private ShuraBattleGetGuessDataResp() {
    optionalList_ = java.util.Collections.emptyList();
    selectedList_ = emptyLongList();
    resultList_ = emptyLongList();
    rewardRankAchi_ = emptyIntList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_ShuraBattleGetGuessDataResp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_ShuraBattleGetGuessDataResp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.ShuraBattleGetGuessDataResp.class, xddq.pb.ShuraBattleGetGuessDataResp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int OPTIONALLIST_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.ShuraBattleGuessTeamData> optionalList_;
  /**
   * <code>repeated .xddq.pb.ShuraBattleGuessTeamData optionalList = 2;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.ShuraBattleGuessTeamData> getOptionalListList() {
    return optionalList_;
  }
  /**
   * <code>repeated .xddq.pb.ShuraBattleGuessTeamData optionalList = 2;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.ShuraBattleGuessTeamDataOrBuilder> 
      getOptionalListOrBuilderList() {
    return optionalList_;
  }
  /**
   * <code>repeated .xddq.pb.ShuraBattleGuessTeamData optionalList = 2;</code>
   */
  @java.lang.Override
  public int getOptionalListCount() {
    return optionalList_.size();
  }
  /**
   * <code>repeated .xddq.pb.ShuraBattleGuessTeamData optionalList = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.ShuraBattleGuessTeamData getOptionalList(int index) {
    return optionalList_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.ShuraBattleGuessTeamData optionalList = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.ShuraBattleGuessTeamDataOrBuilder getOptionalListOrBuilder(
      int index) {
    return optionalList_.get(index);
  }

  public static final int SELECTEDLIST_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private com.google.protobuf.Internal.LongList selectedList_ =
      emptyLongList();
  /**
   * <code>repeated int64 selectedList = 3;</code>
   * @return A list containing the selectedList.
   */
  @java.lang.Override
  public java.util.List<java.lang.Long>
      getSelectedListList() {
    return selectedList_;
  }
  /**
   * <code>repeated int64 selectedList = 3;</code>
   * @return The count of selectedList.
   */
  public int getSelectedListCount() {
    return selectedList_.size();
  }
  /**
   * <code>repeated int64 selectedList = 3;</code>
   * @param index The index of the element to return.
   * @return The selectedList at the given index.
   */
  public long getSelectedList(int index) {
    return selectedList_.getLong(index);
  }

  public static final int RESULTLIST_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private com.google.protobuf.Internal.LongList resultList_ =
      emptyLongList();
  /**
   * <code>repeated int64 resultList = 4;</code>
   * @return A list containing the resultList.
   */
  @java.lang.Override
  public java.util.List<java.lang.Long>
      getResultListList() {
    return resultList_;
  }
  /**
   * <code>repeated int64 resultList = 4;</code>
   * @return The count of resultList.
   */
  public int getResultListCount() {
    return resultList_.size();
  }
  /**
   * <code>repeated int64 resultList = 4;</code>
   * @param index The index of the element to return.
   * @return The resultList at the given index.
   */
  public long getResultList(int index) {
    return resultList_.getLong(index);
  }

  public static final int ISGETREWARD_FIELD_NUMBER = 5;
  private boolean isGetReward_ = false;
  /**
   * <code>optional bool isGetReward = 5;</code>
   * @return Whether the isGetReward field is set.
   */
  @java.lang.Override
  public boolean hasIsGetReward() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional bool isGetReward = 5;</code>
   * @return The isGetReward.
   */
  @java.lang.Override
  public boolean getIsGetReward() {
    return isGetReward_;
  }

  public static final int REWARDPARAM_FIELD_NUMBER = 6;
  private int rewardParam_ = 0;
  /**
   * <code>optional int32 rewardParam = 6;</code>
   * @return Whether the rewardParam field is set.
   */
  @java.lang.Override
  public boolean hasRewardParam() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional int32 rewardParam = 6;</code>
   * @return The rewardParam.
   */
  @java.lang.Override
  public int getRewardParam() {
    return rewardParam_;
  }

  public static final int REWARDRANKACHI_FIELD_NUMBER = 7;
  @SuppressWarnings("serial")
  private com.google.protobuf.Internal.IntList rewardRankAchi_ =
      emptyIntList();
  /**
   * <code>repeated int32 rewardRankAchi = 7;</code>
   * @return A list containing the rewardRankAchi.
   */
  @java.lang.Override
  public java.util.List<java.lang.Integer>
      getRewardRankAchiList() {
    return rewardRankAchi_;
  }
  /**
   * <code>repeated int32 rewardRankAchi = 7;</code>
   * @return The count of rewardRankAchi.
   */
  public int getRewardRankAchiCount() {
    return rewardRankAchi_.size();
  }
  /**
   * <code>repeated int32 rewardRankAchi = 7;</code>
   * @param index The index of the element to return.
   * @return The rewardRankAchi at the given index.
   */
  public int getRewardRankAchi(int index) {
    return rewardRankAchi_.getInt(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    for (int i = 0; i < optionalList_.size(); i++) {
      output.writeMessage(2, optionalList_.get(i));
    }
    for (int i = 0; i < selectedList_.size(); i++) {
      output.writeInt64(3, selectedList_.getLong(i));
    }
    for (int i = 0; i < resultList_.size(); i++) {
      output.writeInt64(4, resultList_.getLong(i));
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeBool(5, isGetReward_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt32(6, rewardParam_);
    }
    for (int i = 0; i < rewardRankAchi_.size(); i++) {
      output.writeInt32(7, rewardRankAchi_.getInt(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    for (int i = 0; i < optionalList_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, optionalList_.get(i));
    }
    {
      int dataSize = 0;
      for (int i = 0; i < selectedList_.size(); i++) {
        dataSize += com.google.protobuf.CodedOutputStream
          .computeInt64SizeNoTag(selectedList_.getLong(i));
      }
      size += dataSize;
      size += 1 * getSelectedListList().size();
    }
    {
      int dataSize = 0;
      for (int i = 0; i < resultList_.size(); i++) {
        dataSize += com.google.protobuf.CodedOutputStream
          .computeInt64SizeNoTag(resultList_.getLong(i));
      }
      size += dataSize;
      size += 1 * getResultListList().size();
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(5, isGetReward_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(6, rewardParam_);
    }
    {
      int dataSize = 0;
      for (int i = 0; i < rewardRankAchi_.size(); i++) {
        dataSize += com.google.protobuf.CodedOutputStream
          .computeInt32SizeNoTag(rewardRankAchi_.getInt(i));
      }
      size += dataSize;
      size += 1 * getRewardRankAchiList().size();
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.ShuraBattleGetGuessDataResp)) {
      return super.equals(obj);
    }
    xddq.pb.ShuraBattleGetGuessDataResp other = (xddq.pb.ShuraBattleGetGuessDataResp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (!getOptionalListList()
        .equals(other.getOptionalListList())) return false;
    if (!getSelectedListList()
        .equals(other.getSelectedListList())) return false;
    if (!getResultListList()
        .equals(other.getResultListList())) return false;
    if (hasIsGetReward() != other.hasIsGetReward()) return false;
    if (hasIsGetReward()) {
      if (getIsGetReward()
          != other.getIsGetReward()) return false;
    }
    if (hasRewardParam() != other.hasRewardParam()) return false;
    if (hasRewardParam()) {
      if (getRewardParam()
          != other.getRewardParam()) return false;
    }
    if (!getRewardRankAchiList()
        .equals(other.getRewardRankAchiList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (getOptionalListCount() > 0) {
      hash = (37 * hash) + OPTIONALLIST_FIELD_NUMBER;
      hash = (53 * hash) + getOptionalListList().hashCode();
    }
    if (getSelectedListCount() > 0) {
      hash = (37 * hash) + SELECTEDLIST_FIELD_NUMBER;
      hash = (53 * hash) + getSelectedListList().hashCode();
    }
    if (getResultListCount() > 0) {
      hash = (37 * hash) + RESULTLIST_FIELD_NUMBER;
      hash = (53 * hash) + getResultListList().hashCode();
    }
    if (hasIsGetReward()) {
      hash = (37 * hash) + ISGETREWARD_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getIsGetReward());
    }
    if (hasRewardParam()) {
      hash = (37 * hash) + REWARDPARAM_FIELD_NUMBER;
      hash = (53 * hash) + getRewardParam();
    }
    if (getRewardRankAchiCount() > 0) {
      hash = (37 * hash) + REWARDRANKACHI_FIELD_NUMBER;
      hash = (53 * hash) + getRewardRankAchiList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.ShuraBattleGetGuessDataResp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ShuraBattleGetGuessDataResp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ShuraBattleGetGuessDataResp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ShuraBattleGetGuessDataResp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ShuraBattleGetGuessDataResp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ShuraBattleGetGuessDataResp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ShuraBattleGetGuessDataResp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.ShuraBattleGetGuessDataResp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.ShuraBattleGetGuessDataResp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.ShuraBattleGetGuessDataResp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.ShuraBattleGetGuessDataResp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.ShuraBattleGetGuessDataResp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.ShuraBattleGetGuessDataResp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.ShuraBattleGetGuessDataResp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.ShuraBattleGetGuessDataResp)
      xddq.pb.ShuraBattleGetGuessDataRespOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ShuraBattleGetGuessDataResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ShuraBattleGetGuessDataResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.ShuraBattleGetGuessDataResp.class, xddq.pb.ShuraBattleGetGuessDataResp.Builder.class);
    }

    // Construct using xddq.pb.ShuraBattleGetGuessDataResp.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      if (optionalListBuilder_ == null) {
        optionalList_ = java.util.Collections.emptyList();
      } else {
        optionalList_ = null;
        optionalListBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000002);
      selectedList_ = emptyLongList();
      resultList_ = emptyLongList();
      isGetReward_ = false;
      rewardParam_ = 0;
      rewardRankAchi_ = emptyIntList();
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ShuraBattleGetGuessDataResp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.ShuraBattleGetGuessDataResp getDefaultInstanceForType() {
      return xddq.pb.ShuraBattleGetGuessDataResp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.ShuraBattleGetGuessDataResp build() {
      xddq.pb.ShuraBattleGetGuessDataResp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.ShuraBattleGetGuessDataResp buildPartial() {
      xddq.pb.ShuraBattleGetGuessDataResp result = new xddq.pb.ShuraBattleGetGuessDataResp(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.ShuraBattleGetGuessDataResp result) {
      if (optionalListBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          optionalList_ = java.util.Collections.unmodifiableList(optionalList_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.optionalList_ = optionalList_;
      } else {
        result.optionalList_ = optionalListBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.ShuraBattleGetGuessDataResp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        selectedList_.makeImmutable();
        result.selectedList_ = selectedList_;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        resultList_.makeImmutable();
        result.resultList_ = resultList_;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.isGetReward_ = isGetReward_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.rewardParam_ = rewardParam_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        rewardRankAchi_.makeImmutable();
        result.rewardRankAchi_ = rewardRankAchi_;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.ShuraBattleGetGuessDataResp) {
        return mergeFrom((xddq.pb.ShuraBattleGetGuessDataResp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.ShuraBattleGetGuessDataResp other) {
      if (other == xddq.pb.ShuraBattleGetGuessDataResp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (optionalListBuilder_ == null) {
        if (!other.optionalList_.isEmpty()) {
          if (optionalList_.isEmpty()) {
            optionalList_ = other.optionalList_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureOptionalListIsMutable();
            optionalList_.addAll(other.optionalList_);
          }
          onChanged();
        }
      } else {
        if (!other.optionalList_.isEmpty()) {
          if (optionalListBuilder_.isEmpty()) {
            optionalListBuilder_.dispose();
            optionalListBuilder_ = null;
            optionalList_ = other.optionalList_;
            bitField0_ = (bitField0_ & ~0x00000002);
            optionalListBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetOptionalListFieldBuilder() : null;
          } else {
            optionalListBuilder_.addAllMessages(other.optionalList_);
          }
        }
      }
      if (!other.selectedList_.isEmpty()) {
        if (selectedList_.isEmpty()) {
          selectedList_ = other.selectedList_;
          selectedList_.makeImmutable();
          bitField0_ |= 0x00000004;
        } else {
          ensureSelectedListIsMutable();
          selectedList_.addAll(other.selectedList_);
        }
        onChanged();
      }
      if (!other.resultList_.isEmpty()) {
        if (resultList_.isEmpty()) {
          resultList_ = other.resultList_;
          resultList_.makeImmutable();
          bitField0_ |= 0x00000008;
        } else {
          ensureResultListIsMutable();
          resultList_.addAll(other.resultList_);
        }
        onChanged();
      }
      if (other.hasIsGetReward()) {
        setIsGetReward(other.getIsGetReward());
      }
      if (other.hasRewardParam()) {
        setRewardParam(other.getRewardParam());
      }
      if (!other.rewardRankAchi_.isEmpty()) {
        if (rewardRankAchi_.isEmpty()) {
          rewardRankAchi_ = other.rewardRankAchi_;
          rewardRankAchi_.makeImmutable();
          bitField0_ |= 0x00000040;
        } else {
          ensureRewardRankAchiIsMutable();
          rewardRankAchi_.addAll(other.rewardRankAchi_);
        }
        onChanged();
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              xddq.pb.ShuraBattleGuessTeamData m =
                  input.readMessage(
                      xddq.pb.ShuraBattleGuessTeamData.parser(),
                      extensionRegistry);
              if (optionalListBuilder_ == null) {
                ensureOptionalListIsMutable();
                optionalList_.add(m);
              } else {
                optionalListBuilder_.addMessage(m);
              }
              break;
            } // case 18
            case 24: {
              long v = input.readInt64();
              ensureSelectedListIsMutable();
              selectedList_.addLong(v);
              break;
            } // case 24
            case 26: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              ensureSelectedListIsMutable();
              while (input.getBytesUntilLimit() > 0) {
                selectedList_.addLong(input.readInt64());
              }
              input.popLimit(limit);
              break;
            } // case 26
            case 32: {
              long v = input.readInt64();
              ensureResultListIsMutable();
              resultList_.addLong(v);
              break;
            } // case 32
            case 34: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              ensureResultListIsMutable();
              while (input.getBytesUntilLimit() > 0) {
                resultList_.addLong(input.readInt64());
              }
              input.popLimit(limit);
              break;
            } // case 34
            case 40: {
              isGetReward_ = input.readBool();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            case 48: {
              rewardParam_ = input.readInt32();
              bitField0_ |= 0x00000020;
              break;
            } // case 48
            case 56: {
              int v = input.readInt32();
              ensureRewardRankAchiIsMutable();
              rewardRankAchi_.addInt(v);
              break;
            } // case 56
            case 58: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              ensureRewardRankAchiIsMutable();
              while (input.getBytesUntilLimit() > 0) {
                rewardRankAchi_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            } // case 58
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.ShuraBattleGuessTeamData> optionalList_ =
      java.util.Collections.emptyList();
    private void ensureOptionalListIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        optionalList_ = new java.util.ArrayList<xddq.pb.ShuraBattleGuessTeamData>(optionalList_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.ShuraBattleGuessTeamData, xddq.pb.ShuraBattleGuessTeamData.Builder, xddq.pb.ShuraBattleGuessTeamDataOrBuilder> optionalListBuilder_;

    /**
     * <code>repeated .xddq.pb.ShuraBattleGuessTeamData optionalList = 2;</code>
     */
    public java.util.List<xddq.pb.ShuraBattleGuessTeamData> getOptionalListList() {
      if (optionalListBuilder_ == null) {
        return java.util.Collections.unmodifiableList(optionalList_);
      } else {
        return optionalListBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.ShuraBattleGuessTeamData optionalList = 2;</code>
     */
    public int getOptionalListCount() {
      if (optionalListBuilder_ == null) {
        return optionalList_.size();
      } else {
        return optionalListBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.ShuraBattleGuessTeamData optionalList = 2;</code>
     */
    public xddq.pb.ShuraBattleGuessTeamData getOptionalList(int index) {
      if (optionalListBuilder_ == null) {
        return optionalList_.get(index);
      } else {
        return optionalListBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.ShuraBattleGuessTeamData optionalList = 2;</code>
     */
    public Builder setOptionalList(
        int index, xddq.pb.ShuraBattleGuessTeamData value) {
      if (optionalListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureOptionalListIsMutable();
        optionalList_.set(index, value);
        onChanged();
      } else {
        optionalListBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ShuraBattleGuessTeamData optionalList = 2;</code>
     */
    public Builder setOptionalList(
        int index, xddq.pb.ShuraBattleGuessTeamData.Builder builderForValue) {
      if (optionalListBuilder_ == null) {
        ensureOptionalListIsMutable();
        optionalList_.set(index, builderForValue.build());
        onChanged();
      } else {
        optionalListBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ShuraBattleGuessTeamData optionalList = 2;</code>
     */
    public Builder addOptionalList(xddq.pb.ShuraBattleGuessTeamData value) {
      if (optionalListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureOptionalListIsMutable();
        optionalList_.add(value);
        onChanged();
      } else {
        optionalListBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ShuraBattleGuessTeamData optionalList = 2;</code>
     */
    public Builder addOptionalList(
        int index, xddq.pb.ShuraBattleGuessTeamData value) {
      if (optionalListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureOptionalListIsMutable();
        optionalList_.add(index, value);
        onChanged();
      } else {
        optionalListBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ShuraBattleGuessTeamData optionalList = 2;</code>
     */
    public Builder addOptionalList(
        xddq.pb.ShuraBattleGuessTeamData.Builder builderForValue) {
      if (optionalListBuilder_ == null) {
        ensureOptionalListIsMutable();
        optionalList_.add(builderForValue.build());
        onChanged();
      } else {
        optionalListBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ShuraBattleGuessTeamData optionalList = 2;</code>
     */
    public Builder addOptionalList(
        int index, xddq.pb.ShuraBattleGuessTeamData.Builder builderForValue) {
      if (optionalListBuilder_ == null) {
        ensureOptionalListIsMutable();
        optionalList_.add(index, builderForValue.build());
        onChanged();
      } else {
        optionalListBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ShuraBattleGuessTeamData optionalList = 2;</code>
     */
    public Builder addAllOptionalList(
        java.lang.Iterable<? extends xddq.pb.ShuraBattleGuessTeamData> values) {
      if (optionalListBuilder_ == null) {
        ensureOptionalListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, optionalList_);
        onChanged();
      } else {
        optionalListBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ShuraBattleGuessTeamData optionalList = 2;</code>
     */
    public Builder clearOptionalList() {
      if (optionalListBuilder_ == null) {
        optionalList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        optionalListBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ShuraBattleGuessTeamData optionalList = 2;</code>
     */
    public Builder removeOptionalList(int index) {
      if (optionalListBuilder_ == null) {
        ensureOptionalListIsMutable();
        optionalList_.remove(index);
        onChanged();
      } else {
        optionalListBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ShuraBattleGuessTeamData optionalList = 2;</code>
     */
    public xddq.pb.ShuraBattleGuessTeamData.Builder getOptionalListBuilder(
        int index) {
      return internalGetOptionalListFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.ShuraBattleGuessTeamData optionalList = 2;</code>
     */
    public xddq.pb.ShuraBattleGuessTeamDataOrBuilder getOptionalListOrBuilder(
        int index) {
      if (optionalListBuilder_ == null) {
        return optionalList_.get(index);  } else {
        return optionalListBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.ShuraBattleGuessTeamData optionalList = 2;</code>
     */
    public java.util.List<? extends xddq.pb.ShuraBattleGuessTeamDataOrBuilder> 
         getOptionalListOrBuilderList() {
      if (optionalListBuilder_ != null) {
        return optionalListBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(optionalList_);
      }
    }
    /**
     * <code>repeated .xddq.pb.ShuraBattleGuessTeamData optionalList = 2;</code>
     */
    public xddq.pb.ShuraBattleGuessTeamData.Builder addOptionalListBuilder() {
      return internalGetOptionalListFieldBuilder().addBuilder(
          xddq.pb.ShuraBattleGuessTeamData.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.ShuraBattleGuessTeamData optionalList = 2;</code>
     */
    public xddq.pb.ShuraBattleGuessTeamData.Builder addOptionalListBuilder(
        int index) {
      return internalGetOptionalListFieldBuilder().addBuilder(
          index, xddq.pb.ShuraBattleGuessTeamData.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.ShuraBattleGuessTeamData optionalList = 2;</code>
     */
    public java.util.List<xddq.pb.ShuraBattleGuessTeamData.Builder> 
         getOptionalListBuilderList() {
      return internalGetOptionalListFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.ShuraBattleGuessTeamData, xddq.pb.ShuraBattleGuessTeamData.Builder, xddq.pb.ShuraBattleGuessTeamDataOrBuilder> 
        internalGetOptionalListFieldBuilder() {
      if (optionalListBuilder_ == null) {
        optionalListBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.ShuraBattleGuessTeamData, xddq.pb.ShuraBattleGuessTeamData.Builder, xddq.pb.ShuraBattleGuessTeamDataOrBuilder>(
                optionalList_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        optionalList_ = null;
      }
      return optionalListBuilder_;
    }

    private com.google.protobuf.Internal.LongList selectedList_ = emptyLongList();
    private void ensureSelectedListIsMutable() {
      if (!selectedList_.isModifiable()) {
        selectedList_ = makeMutableCopy(selectedList_);
      }
      bitField0_ |= 0x00000004;
    }
    /**
     * <code>repeated int64 selectedList = 3;</code>
     * @return A list containing the selectedList.
     */
    public java.util.List<java.lang.Long>
        getSelectedListList() {
      selectedList_.makeImmutable();
      return selectedList_;
    }
    /**
     * <code>repeated int64 selectedList = 3;</code>
     * @return The count of selectedList.
     */
    public int getSelectedListCount() {
      return selectedList_.size();
    }
    /**
     * <code>repeated int64 selectedList = 3;</code>
     * @param index The index of the element to return.
     * @return The selectedList at the given index.
     */
    public long getSelectedList(int index) {
      return selectedList_.getLong(index);
    }
    /**
     * <code>repeated int64 selectedList = 3;</code>
     * @param index The index to set the value at.
     * @param value The selectedList to set.
     * @return This builder for chaining.
     */
    public Builder setSelectedList(
        int index, long value) {

      ensureSelectedListIsMutable();
      selectedList_.setLong(index, value);
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int64 selectedList = 3;</code>
     * @param value The selectedList to add.
     * @return This builder for chaining.
     */
    public Builder addSelectedList(long value) {

      ensureSelectedListIsMutable();
      selectedList_.addLong(value);
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int64 selectedList = 3;</code>
     * @param values The selectedList to add.
     * @return This builder for chaining.
     */
    public Builder addAllSelectedList(
        java.lang.Iterable<? extends java.lang.Long> values) {
      ensureSelectedListIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, selectedList_);
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int64 selectedList = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearSelectedList() {
      selectedList_ = emptyLongList();
      bitField0_ = (bitField0_ & ~0x00000004);
      onChanged();
      return this;
    }

    private com.google.protobuf.Internal.LongList resultList_ = emptyLongList();
    private void ensureResultListIsMutable() {
      if (!resultList_.isModifiable()) {
        resultList_ = makeMutableCopy(resultList_);
      }
      bitField0_ |= 0x00000008;
    }
    /**
     * <code>repeated int64 resultList = 4;</code>
     * @return A list containing the resultList.
     */
    public java.util.List<java.lang.Long>
        getResultListList() {
      resultList_.makeImmutable();
      return resultList_;
    }
    /**
     * <code>repeated int64 resultList = 4;</code>
     * @return The count of resultList.
     */
    public int getResultListCount() {
      return resultList_.size();
    }
    /**
     * <code>repeated int64 resultList = 4;</code>
     * @param index The index of the element to return.
     * @return The resultList at the given index.
     */
    public long getResultList(int index) {
      return resultList_.getLong(index);
    }
    /**
     * <code>repeated int64 resultList = 4;</code>
     * @param index The index to set the value at.
     * @param value The resultList to set.
     * @return This builder for chaining.
     */
    public Builder setResultList(
        int index, long value) {

      ensureResultListIsMutable();
      resultList_.setLong(index, value);
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int64 resultList = 4;</code>
     * @param value The resultList to add.
     * @return This builder for chaining.
     */
    public Builder addResultList(long value) {

      ensureResultListIsMutable();
      resultList_.addLong(value);
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int64 resultList = 4;</code>
     * @param values The resultList to add.
     * @return This builder for chaining.
     */
    public Builder addAllResultList(
        java.lang.Iterable<? extends java.lang.Long> values) {
      ensureResultListIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, resultList_);
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int64 resultList = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearResultList() {
      resultList_ = emptyLongList();
      bitField0_ = (bitField0_ & ~0x00000008);
      onChanged();
      return this;
    }

    private boolean isGetReward_ ;
    /**
     * <code>optional bool isGetReward = 5;</code>
     * @return Whether the isGetReward field is set.
     */
    @java.lang.Override
    public boolean hasIsGetReward() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional bool isGetReward = 5;</code>
     * @return The isGetReward.
     */
    @java.lang.Override
    public boolean getIsGetReward() {
      return isGetReward_;
    }
    /**
     * <code>optional bool isGetReward = 5;</code>
     * @param value The isGetReward to set.
     * @return This builder for chaining.
     */
    public Builder setIsGetReward(boolean value) {

      isGetReward_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional bool isGetReward = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearIsGetReward() {
      bitField0_ = (bitField0_ & ~0x00000010);
      isGetReward_ = false;
      onChanged();
      return this;
    }

    private int rewardParam_ ;
    /**
     * <code>optional int32 rewardParam = 6;</code>
     * @return Whether the rewardParam field is set.
     */
    @java.lang.Override
    public boolean hasRewardParam() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional int32 rewardParam = 6;</code>
     * @return The rewardParam.
     */
    @java.lang.Override
    public int getRewardParam() {
      return rewardParam_;
    }
    /**
     * <code>optional int32 rewardParam = 6;</code>
     * @param value The rewardParam to set.
     * @return This builder for chaining.
     */
    public Builder setRewardParam(int value) {

      rewardParam_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 rewardParam = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearRewardParam() {
      bitField0_ = (bitField0_ & ~0x00000020);
      rewardParam_ = 0;
      onChanged();
      return this;
    }

    private com.google.protobuf.Internal.IntList rewardRankAchi_ = emptyIntList();
    private void ensureRewardRankAchiIsMutable() {
      if (!rewardRankAchi_.isModifiable()) {
        rewardRankAchi_ = makeMutableCopy(rewardRankAchi_);
      }
      bitField0_ |= 0x00000040;
    }
    /**
     * <code>repeated int32 rewardRankAchi = 7;</code>
     * @return A list containing the rewardRankAchi.
     */
    public java.util.List<java.lang.Integer>
        getRewardRankAchiList() {
      rewardRankAchi_.makeImmutable();
      return rewardRankAchi_;
    }
    /**
     * <code>repeated int32 rewardRankAchi = 7;</code>
     * @return The count of rewardRankAchi.
     */
    public int getRewardRankAchiCount() {
      return rewardRankAchi_.size();
    }
    /**
     * <code>repeated int32 rewardRankAchi = 7;</code>
     * @param index The index of the element to return.
     * @return The rewardRankAchi at the given index.
     */
    public int getRewardRankAchi(int index) {
      return rewardRankAchi_.getInt(index);
    }
    /**
     * <code>repeated int32 rewardRankAchi = 7;</code>
     * @param index The index to set the value at.
     * @param value The rewardRankAchi to set.
     * @return This builder for chaining.
     */
    public Builder setRewardRankAchi(
        int index, int value) {

      ensureRewardRankAchiIsMutable();
      rewardRankAchi_.setInt(index, value);
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 rewardRankAchi = 7;</code>
     * @param value The rewardRankAchi to add.
     * @return This builder for chaining.
     */
    public Builder addRewardRankAchi(int value) {

      ensureRewardRankAchiIsMutable();
      rewardRankAchi_.addInt(value);
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 rewardRankAchi = 7;</code>
     * @param values The rewardRankAchi to add.
     * @return This builder for chaining.
     */
    public Builder addAllRewardRankAchi(
        java.lang.Iterable<? extends java.lang.Integer> values) {
      ensureRewardRankAchiIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, rewardRankAchi_);
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 rewardRankAchi = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearRewardRankAchi() {
      rewardRankAchi_ = emptyIntList();
      bitField0_ = (bitField0_ & ~0x00000040);
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.ShuraBattleGetGuessDataResp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.ShuraBattleGetGuessDataResp)
  private static final xddq.pb.ShuraBattleGetGuessDataResp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.ShuraBattleGetGuessDataResp();
  }

  public static xddq.pb.ShuraBattleGetGuessDataResp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ShuraBattleGetGuessDataResp>
      PARSER = new com.google.protobuf.AbstractParser<ShuraBattleGetGuessDataResp>() {
    @java.lang.Override
    public ShuraBattleGetGuessDataResp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<ShuraBattleGetGuessDataResp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ShuraBattleGetGuessDataResp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.ShuraBattleGetGuessDataResp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

