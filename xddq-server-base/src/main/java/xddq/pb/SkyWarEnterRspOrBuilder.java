// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

public interface SkyWarEnterRspOrBuilder extends
    // @@protoc_insertion_point(interface_extends:xddq.pb.SkyWarEnterRsp)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  boolean hasRet();
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  int getRet();

  /**
   * <code>optional int32 myPowerRank = 2;</code>
   * @return Whether the myPowerRank field is set.
   */
  boolean hasMyPowerRank();
  /**
   * <code>optional int32 myPowerRank = 2;</code>
   * @return The myPowerRank.
   */
  int getMyPowerRank();

  /**
   * <code>optional int32 refreshTimes = 3;</code>
   * @return Whether the refreshTimes field is set.
   */
  boolean hasRefreshTimes();
  /**
   * <code>optional int32 refreshTimes = 3;</code>
   * @return The refreshTimes.
   */
  int getRefreshTimes();

  /**
   * <code>repeated .xddq.pb.SkyWarRankData rankData = 4;</code>
   */
  java.util.List<xddq.pb.SkyWarRankData> 
      getRankDataList();
  /**
   * <code>repeated .xddq.pb.SkyWarRankData rankData = 4;</code>
   */
  xddq.pb.SkyWarRankData getRankData(int index);
  /**
   * <code>repeated .xddq.pb.SkyWarRankData rankData = 4;</code>
   */
  int getRankDataCount();
  /**
   * <code>repeated .xddq.pb.SkyWarRankData rankData = 4;</code>
   */
  java.util.List<? extends xddq.pb.SkyWarRankDataOrBuilder> 
      getRankDataOrBuilderList();
  /**
   * <code>repeated .xddq.pb.SkyWarRankData rankData = 4;</code>
   */
  xddq.pb.SkyWarRankDataOrBuilder getRankDataOrBuilder(
      int index);

  /**
   * <code>repeated .xddq.pb.EnemyPlayerData enemyData = 5;</code>
   */
  java.util.List<xddq.pb.EnemyPlayerData> 
      getEnemyDataList();
  /**
   * <code>repeated .xddq.pb.EnemyPlayerData enemyData = 5;</code>
   */
  xddq.pb.EnemyPlayerData getEnemyData(int index);
  /**
   * <code>repeated .xddq.pb.EnemyPlayerData enemyData = 5;</code>
   */
  int getEnemyDataCount();
  /**
   * <code>repeated .xddq.pb.EnemyPlayerData enemyData = 5;</code>
   */
  java.util.List<? extends xddq.pb.EnemyPlayerDataOrBuilder> 
      getEnemyDataOrBuilderList();
  /**
   * <code>repeated .xddq.pb.EnemyPlayerData enemyData = 5;</code>
   */
  xddq.pb.EnemyPlayerDataOrBuilder getEnemyDataOrBuilder(
      int index);

  /**
   * <code>optional int32 myScore = 6;</code>
   * @return Whether the myScore field is set.
   */
  boolean hasMyScore();
  /**
   * <code>optional int32 myScore = 6;</code>
   * @return The myScore.
   */
  int getMyScore();

  /**
   * <code>optional int32 myGroupRank = 7;</code>
   * @return Whether the myGroupRank field is set.
   */
  boolean hasMyGroupRank();
  /**
   * <code>optional int32 myGroupRank = 7;</code>
   * @return The myGroupRank.
   */
  int getMyGroupRank();

  /**
   * <code>optional int32 currentSky = 8;</code>
   * @return Whether the currentSky field is set.
   */
  boolean hasCurrentSky();
  /**
   * <code>optional int32 currentSky = 8;</code>
   * @return The currentSky.
   */
  int getCurrentSky();

  /**
   * <code>optional int32 lastWeekSky = 9;</code>
   * @return Whether the lastWeekSky field is set.
   */
  boolean hasLastWeekSky();
  /**
   * <code>optional int32 lastWeekSky = 9;</code>
   * @return The lastWeekSky.
   */
  int getLastWeekSky();

  /**
   * <code>optional int32 battleTimes = 10;</code>
   * @return Whether the battleTimes field is set.
   */
  boolean hasBattleTimes();
  /**
   * <code>optional int32 battleTimes = 10;</code>
   * @return The battleTimes.
   */
  int getBattleTimes();

  /**
   * <code>optional int32 buyBattleTimes = 11;</code>
   * @return Whether the buyBattleTimes field is set.
   */
  boolean hasBuyBattleTimes();
  /**
   * <code>optional int32 buyBattleTimes = 11;</code>
   * @return The buyBattleTimes.
   */
  int getBuyBattleTimes();

  /**
   * <code>optional int32 buyRefreshTimes = 12;</code>
   * @return Whether the buyRefreshTimes field is set.
   */
  boolean hasBuyRefreshTimes();
  /**
   * <code>optional int32 buyRefreshTimes = 12;</code>
   * @return The buyRefreshTimes.
   */
  int getBuyRefreshTimes();

  /**
   * <code>optional int32 applySky = 13;</code>
   * @return Whether the applySky field is set.
   */
  boolean hasApplySky();
  /**
   * <code>optional int32 applySky = 13;</code>
   * @return The applySky.
   */
  int getApplySky();
}
