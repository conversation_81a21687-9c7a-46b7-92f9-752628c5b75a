// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

public interface WeYoundBonusRecordReqOrBuilder extends
    // @@protoc_insertion_point(interface_extends:xddq.pb.WeYoundBonusRecordReq)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>required int32 activityId = 1;</code>
   * @return Whether the activityId field is set.
   */
  boolean hasActivityId();
  /**
   * <code>required int32 activityId = 1;</code>
   * @return The activityId.
   */
  int getActivityId();

  /**
   * <code>required int32 type = 2;</code>
   * @return Whether the type field is set.
   */
  boolean hasType();
  /**
   * <code>required int32 type = 2;</code>
   * @return The type.
   */
  int getType();
}
