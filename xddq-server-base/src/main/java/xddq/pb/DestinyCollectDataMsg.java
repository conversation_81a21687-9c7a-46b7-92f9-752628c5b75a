// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.DestinyCollectDataMsg}
 */
public final class DestinyCollectDataMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.DestinyCollectDataMsg)
    DestinyCollectDataMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      DestinyCollectDataMsg.class.getName());
  }
  // Use DestinyCollectDataMsg.newBuilder() to construct.
  private DestinyCollectDataMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private DestinyCollectDataMsg() {
    destinyCollectCardData_ = java.util.Collections.emptyList();
    destinyCollectCardGroupDataMsg_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_DestinyCollectDataMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_DestinyCollectDataMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.DestinyCollectDataMsg.class, xddq.pb.DestinyCollectDataMsg.Builder.class);
  }

  private int bitField0_;
  public static final int DESTINYID_FIELD_NUMBER = 1;
  private int destinyId_ = 0;
  /**
   * <code>required int32 destinyId = 1;</code>
   * @return Whether the destinyId field is set.
   */
  @java.lang.Override
  public boolean hasDestinyId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 destinyId = 1;</code>
   * @return The destinyId.
   */
  @java.lang.Override
  public int getDestinyId() {
    return destinyId_;
  }

  public static final int DESTINYCOLLECTCARDDATA_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.DestinyCollectCardDataMsg> destinyCollectCardData_;
  /**
   * <code>repeated .xddq.pb.DestinyCollectCardDataMsg destinyCollectCardData = 2;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.DestinyCollectCardDataMsg> getDestinyCollectCardDataList() {
    return destinyCollectCardData_;
  }
  /**
   * <code>repeated .xddq.pb.DestinyCollectCardDataMsg destinyCollectCardData = 2;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.DestinyCollectCardDataMsgOrBuilder> 
      getDestinyCollectCardDataOrBuilderList() {
    return destinyCollectCardData_;
  }
  /**
   * <code>repeated .xddq.pb.DestinyCollectCardDataMsg destinyCollectCardData = 2;</code>
   */
  @java.lang.Override
  public int getDestinyCollectCardDataCount() {
    return destinyCollectCardData_.size();
  }
  /**
   * <code>repeated .xddq.pb.DestinyCollectCardDataMsg destinyCollectCardData = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.DestinyCollectCardDataMsg getDestinyCollectCardData(int index) {
    return destinyCollectCardData_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.DestinyCollectCardDataMsg destinyCollectCardData = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.DestinyCollectCardDataMsgOrBuilder getDestinyCollectCardDataOrBuilder(
      int index) {
    return destinyCollectCardData_.get(index);
  }

  public static final int DESTINYCOLLECTCARDGROUPDATAMSG_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.DestinyCollectCardGroupDataMsg> destinyCollectCardGroupDataMsg_;
  /**
   * <code>repeated .xddq.pb.DestinyCollectCardGroupDataMsg destinyCollectCardGroupDataMsg = 3;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.DestinyCollectCardGroupDataMsg> getDestinyCollectCardGroupDataMsgList() {
    return destinyCollectCardGroupDataMsg_;
  }
  /**
   * <code>repeated .xddq.pb.DestinyCollectCardGroupDataMsg destinyCollectCardGroupDataMsg = 3;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.DestinyCollectCardGroupDataMsgOrBuilder> 
      getDestinyCollectCardGroupDataMsgOrBuilderList() {
    return destinyCollectCardGroupDataMsg_;
  }
  /**
   * <code>repeated .xddq.pb.DestinyCollectCardGroupDataMsg destinyCollectCardGroupDataMsg = 3;</code>
   */
  @java.lang.Override
  public int getDestinyCollectCardGroupDataMsgCount() {
    return destinyCollectCardGroupDataMsg_.size();
  }
  /**
   * <code>repeated .xddq.pb.DestinyCollectCardGroupDataMsg destinyCollectCardGroupDataMsg = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.DestinyCollectCardGroupDataMsg getDestinyCollectCardGroupDataMsg(int index) {
    return destinyCollectCardGroupDataMsg_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.DestinyCollectCardGroupDataMsg destinyCollectCardGroupDataMsg = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.DestinyCollectCardGroupDataMsgOrBuilder getDestinyCollectCardGroupDataMsgOrBuilder(
      int index) {
    return destinyCollectCardGroupDataMsg_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasDestinyId()) {
      memoizedIsInitialized = 0;
      return false;
    }
    for (int i = 0; i < getDestinyCollectCardDataCount(); i++) {
      if (!getDestinyCollectCardData(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    for (int i = 0; i < getDestinyCollectCardGroupDataMsgCount(); i++) {
      if (!getDestinyCollectCardGroupDataMsg(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, destinyId_);
    }
    for (int i = 0; i < destinyCollectCardData_.size(); i++) {
      output.writeMessage(2, destinyCollectCardData_.get(i));
    }
    for (int i = 0; i < destinyCollectCardGroupDataMsg_.size(); i++) {
      output.writeMessage(3, destinyCollectCardGroupDataMsg_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, destinyId_);
    }
    for (int i = 0; i < destinyCollectCardData_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, destinyCollectCardData_.get(i));
    }
    for (int i = 0; i < destinyCollectCardGroupDataMsg_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, destinyCollectCardGroupDataMsg_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.DestinyCollectDataMsg)) {
      return super.equals(obj);
    }
    xddq.pb.DestinyCollectDataMsg other = (xddq.pb.DestinyCollectDataMsg) obj;

    if (hasDestinyId() != other.hasDestinyId()) return false;
    if (hasDestinyId()) {
      if (getDestinyId()
          != other.getDestinyId()) return false;
    }
    if (!getDestinyCollectCardDataList()
        .equals(other.getDestinyCollectCardDataList())) return false;
    if (!getDestinyCollectCardGroupDataMsgList()
        .equals(other.getDestinyCollectCardGroupDataMsgList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasDestinyId()) {
      hash = (37 * hash) + DESTINYID_FIELD_NUMBER;
      hash = (53 * hash) + getDestinyId();
    }
    if (getDestinyCollectCardDataCount() > 0) {
      hash = (37 * hash) + DESTINYCOLLECTCARDDATA_FIELD_NUMBER;
      hash = (53 * hash) + getDestinyCollectCardDataList().hashCode();
    }
    if (getDestinyCollectCardGroupDataMsgCount() > 0) {
      hash = (37 * hash) + DESTINYCOLLECTCARDGROUPDATAMSG_FIELD_NUMBER;
      hash = (53 * hash) + getDestinyCollectCardGroupDataMsgList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.DestinyCollectDataMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.DestinyCollectDataMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.DestinyCollectDataMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.DestinyCollectDataMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.DestinyCollectDataMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.DestinyCollectDataMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.DestinyCollectDataMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.DestinyCollectDataMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.DestinyCollectDataMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.DestinyCollectDataMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.DestinyCollectDataMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.DestinyCollectDataMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.DestinyCollectDataMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.DestinyCollectDataMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.DestinyCollectDataMsg)
      xddq.pb.DestinyCollectDataMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_DestinyCollectDataMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_DestinyCollectDataMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.DestinyCollectDataMsg.class, xddq.pb.DestinyCollectDataMsg.Builder.class);
    }

    // Construct using xddq.pb.DestinyCollectDataMsg.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      destinyId_ = 0;
      if (destinyCollectCardDataBuilder_ == null) {
        destinyCollectCardData_ = java.util.Collections.emptyList();
      } else {
        destinyCollectCardData_ = null;
        destinyCollectCardDataBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000002);
      if (destinyCollectCardGroupDataMsgBuilder_ == null) {
        destinyCollectCardGroupDataMsg_ = java.util.Collections.emptyList();
      } else {
        destinyCollectCardGroupDataMsg_ = null;
        destinyCollectCardGroupDataMsgBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000004);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_DestinyCollectDataMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.DestinyCollectDataMsg getDefaultInstanceForType() {
      return xddq.pb.DestinyCollectDataMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.DestinyCollectDataMsg build() {
      xddq.pb.DestinyCollectDataMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.DestinyCollectDataMsg buildPartial() {
      xddq.pb.DestinyCollectDataMsg result = new xddq.pb.DestinyCollectDataMsg(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.DestinyCollectDataMsg result) {
      if (destinyCollectCardDataBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          destinyCollectCardData_ = java.util.Collections.unmodifiableList(destinyCollectCardData_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.destinyCollectCardData_ = destinyCollectCardData_;
      } else {
        result.destinyCollectCardData_ = destinyCollectCardDataBuilder_.build();
      }
      if (destinyCollectCardGroupDataMsgBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0)) {
          destinyCollectCardGroupDataMsg_ = java.util.Collections.unmodifiableList(destinyCollectCardGroupDataMsg_);
          bitField0_ = (bitField0_ & ~0x00000004);
        }
        result.destinyCollectCardGroupDataMsg_ = destinyCollectCardGroupDataMsg_;
      } else {
        result.destinyCollectCardGroupDataMsg_ = destinyCollectCardGroupDataMsgBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.DestinyCollectDataMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.destinyId_ = destinyId_;
        to_bitField0_ |= 0x00000001;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.DestinyCollectDataMsg) {
        return mergeFrom((xddq.pb.DestinyCollectDataMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.DestinyCollectDataMsg other) {
      if (other == xddq.pb.DestinyCollectDataMsg.getDefaultInstance()) return this;
      if (other.hasDestinyId()) {
        setDestinyId(other.getDestinyId());
      }
      if (destinyCollectCardDataBuilder_ == null) {
        if (!other.destinyCollectCardData_.isEmpty()) {
          if (destinyCollectCardData_.isEmpty()) {
            destinyCollectCardData_ = other.destinyCollectCardData_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureDestinyCollectCardDataIsMutable();
            destinyCollectCardData_.addAll(other.destinyCollectCardData_);
          }
          onChanged();
        }
      } else {
        if (!other.destinyCollectCardData_.isEmpty()) {
          if (destinyCollectCardDataBuilder_.isEmpty()) {
            destinyCollectCardDataBuilder_.dispose();
            destinyCollectCardDataBuilder_ = null;
            destinyCollectCardData_ = other.destinyCollectCardData_;
            bitField0_ = (bitField0_ & ~0x00000002);
            destinyCollectCardDataBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetDestinyCollectCardDataFieldBuilder() : null;
          } else {
            destinyCollectCardDataBuilder_.addAllMessages(other.destinyCollectCardData_);
          }
        }
      }
      if (destinyCollectCardGroupDataMsgBuilder_ == null) {
        if (!other.destinyCollectCardGroupDataMsg_.isEmpty()) {
          if (destinyCollectCardGroupDataMsg_.isEmpty()) {
            destinyCollectCardGroupDataMsg_ = other.destinyCollectCardGroupDataMsg_;
            bitField0_ = (bitField0_ & ~0x00000004);
          } else {
            ensureDestinyCollectCardGroupDataMsgIsMutable();
            destinyCollectCardGroupDataMsg_.addAll(other.destinyCollectCardGroupDataMsg_);
          }
          onChanged();
        }
      } else {
        if (!other.destinyCollectCardGroupDataMsg_.isEmpty()) {
          if (destinyCollectCardGroupDataMsgBuilder_.isEmpty()) {
            destinyCollectCardGroupDataMsgBuilder_.dispose();
            destinyCollectCardGroupDataMsgBuilder_ = null;
            destinyCollectCardGroupDataMsg_ = other.destinyCollectCardGroupDataMsg_;
            bitField0_ = (bitField0_ & ~0x00000004);
            destinyCollectCardGroupDataMsgBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetDestinyCollectCardGroupDataMsgFieldBuilder() : null;
          } else {
            destinyCollectCardGroupDataMsgBuilder_.addAllMessages(other.destinyCollectCardGroupDataMsg_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasDestinyId()) {
        return false;
      }
      for (int i = 0; i < getDestinyCollectCardDataCount(); i++) {
        if (!getDestinyCollectCardData(i).isInitialized()) {
          return false;
        }
      }
      for (int i = 0; i < getDestinyCollectCardGroupDataMsgCount(); i++) {
        if (!getDestinyCollectCardGroupDataMsg(i).isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              destinyId_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              xddq.pb.DestinyCollectCardDataMsg m =
                  input.readMessage(
                      xddq.pb.DestinyCollectCardDataMsg.parser(),
                      extensionRegistry);
              if (destinyCollectCardDataBuilder_ == null) {
                ensureDestinyCollectCardDataIsMutable();
                destinyCollectCardData_.add(m);
              } else {
                destinyCollectCardDataBuilder_.addMessage(m);
              }
              break;
            } // case 18
            case 26: {
              xddq.pb.DestinyCollectCardGroupDataMsg m =
                  input.readMessage(
                      xddq.pb.DestinyCollectCardGroupDataMsg.parser(),
                      extensionRegistry);
              if (destinyCollectCardGroupDataMsgBuilder_ == null) {
                ensureDestinyCollectCardGroupDataMsgIsMutable();
                destinyCollectCardGroupDataMsg_.add(m);
              } else {
                destinyCollectCardGroupDataMsgBuilder_.addMessage(m);
              }
              break;
            } // case 26
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int destinyId_ ;
    /**
     * <code>required int32 destinyId = 1;</code>
     * @return Whether the destinyId field is set.
     */
    @java.lang.Override
    public boolean hasDestinyId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 destinyId = 1;</code>
     * @return The destinyId.
     */
    @java.lang.Override
    public int getDestinyId() {
      return destinyId_;
    }
    /**
     * <code>required int32 destinyId = 1;</code>
     * @param value The destinyId to set.
     * @return This builder for chaining.
     */
    public Builder setDestinyId(int value) {

      destinyId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 destinyId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearDestinyId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      destinyId_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.DestinyCollectCardDataMsg> destinyCollectCardData_ =
      java.util.Collections.emptyList();
    private void ensureDestinyCollectCardDataIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        destinyCollectCardData_ = new java.util.ArrayList<xddq.pb.DestinyCollectCardDataMsg>(destinyCollectCardData_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.DestinyCollectCardDataMsg, xddq.pb.DestinyCollectCardDataMsg.Builder, xddq.pb.DestinyCollectCardDataMsgOrBuilder> destinyCollectCardDataBuilder_;

    /**
     * <code>repeated .xddq.pb.DestinyCollectCardDataMsg destinyCollectCardData = 2;</code>
     */
    public java.util.List<xddq.pb.DestinyCollectCardDataMsg> getDestinyCollectCardDataList() {
      if (destinyCollectCardDataBuilder_ == null) {
        return java.util.Collections.unmodifiableList(destinyCollectCardData_);
      } else {
        return destinyCollectCardDataBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.DestinyCollectCardDataMsg destinyCollectCardData = 2;</code>
     */
    public int getDestinyCollectCardDataCount() {
      if (destinyCollectCardDataBuilder_ == null) {
        return destinyCollectCardData_.size();
      } else {
        return destinyCollectCardDataBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.DestinyCollectCardDataMsg destinyCollectCardData = 2;</code>
     */
    public xddq.pb.DestinyCollectCardDataMsg getDestinyCollectCardData(int index) {
      if (destinyCollectCardDataBuilder_ == null) {
        return destinyCollectCardData_.get(index);
      } else {
        return destinyCollectCardDataBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.DestinyCollectCardDataMsg destinyCollectCardData = 2;</code>
     */
    public Builder setDestinyCollectCardData(
        int index, xddq.pb.DestinyCollectCardDataMsg value) {
      if (destinyCollectCardDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDestinyCollectCardDataIsMutable();
        destinyCollectCardData_.set(index, value);
        onChanged();
      } else {
        destinyCollectCardDataBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DestinyCollectCardDataMsg destinyCollectCardData = 2;</code>
     */
    public Builder setDestinyCollectCardData(
        int index, xddq.pb.DestinyCollectCardDataMsg.Builder builderForValue) {
      if (destinyCollectCardDataBuilder_ == null) {
        ensureDestinyCollectCardDataIsMutable();
        destinyCollectCardData_.set(index, builderForValue.build());
        onChanged();
      } else {
        destinyCollectCardDataBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DestinyCollectCardDataMsg destinyCollectCardData = 2;</code>
     */
    public Builder addDestinyCollectCardData(xddq.pb.DestinyCollectCardDataMsg value) {
      if (destinyCollectCardDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDestinyCollectCardDataIsMutable();
        destinyCollectCardData_.add(value);
        onChanged();
      } else {
        destinyCollectCardDataBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DestinyCollectCardDataMsg destinyCollectCardData = 2;</code>
     */
    public Builder addDestinyCollectCardData(
        int index, xddq.pb.DestinyCollectCardDataMsg value) {
      if (destinyCollectCardDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDestinyCollectCardDataIsMutable();
        destinyCollectCardData_.add(index, value);
        onChanged();
      } else {
        destinyCollectCardDataBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DestinyCollectCardDataMsg destinyCollectCardData = 2;</code>
     */
    public Builder addDestinyCollectCardData(
        xddq.pb.DestinyCollectCardDataMsg.Builder builderForValue) {
      if (destinyCollectCardDataBuilder_ == null) {
        ensureDestinyCollectCardDataIsMutable();
        destinyCollectCardData_.add(builderForValue.build());
        onChanged();
      } else {
        destinyCollectCardDataBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DestinyCollectCardDataMsg destinyCollectCardData = 2;</code>
     */
    public Builder addDestinyCollectCardData(
        int index, xddq.pb.DestinyCollectCardDataMsg.Builder builderForValue) {
      if (destinyCollectCardDataBuilder_ == null) {
        ensureDestinyCollectCardDataIsMutable();
        destinyCollectCardData_.add(index, builderForValue.build());
        onChanged();
      } else {
        destinyCollectCardDataBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DestinyCollectCardDataMsg destinyCollectCardData = 2;</code>
     */
    public Builder addAllDestinyCollectCardData(
        java.lang.Iterable<? extends xddq.pb.DestinyCollectCardDataMsg> values) {
      if (destinyCollectCardDataBuilder_ == null) {
        ensureDestinyCollectCardDataIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, destinyCollectCardData_);
        onChanged();
      } else {
        destinyCollectCardDataBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DestinyCollectCardDataMsg destinyCollectCardData = 2;</code>
     */
    public Builder clearDestinyCollectCardData() {
      if (destinyCollectCardDataBuilder_ == null) {
        destinyCollectCardData_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        destinyCollectCardDataBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DestinyCollectCardDataMsg destinyCollectCardData = 2;</code>
     */
    public Builder removeDestinyCollectCardData(int index) {
      if (destinyCollectCardDataBuilder_ == null) {
        ensureDestinyCollectCardDataIsMutable();
        destinyCollectCardData_.remove(index);
        onChanged();
      } else {
        destinyCollectCardDataBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DestinyCollectCardDataMsg destinyCollectCardData = 2;</code>
     */
    public xddq.pb.DestinyCollectCardDataMsg.Builder getDestinyCollectCardDataBuilder(
        int index) {
      return internalGetDestinyCollectCardDataFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.DestinyCollectCardDataMsg destinyCollectCardData = 2;</code>
     */
    public xddq.pb.DestinyCollectCardDataMsgOrBuilder getDestinyCollectCardDataOrBuilder(
        int index) {
      if (destinyCollectCardDataBuilder_ == null) {
        return destinyCollectCardData_.get(index);  } else {
        return destinyCollectCardDataBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.DestinyCollectCardDataMsg destinyCollectCardData = 2;</code>
     */
    public java.util.List<? extends xddq.pb.DestinyCollectCardDataMsgOrBuilder> 
         getDestinyCollectCardDataOrBuilderList() {
      if (destinyCollectCardDataBuilder_ != null) {
        return destinyCollectCardDataBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(destinyCollectCardData_);
      }
    }
    /**
     * <code>repeated .xddq.pb.DestinyCollectCardDataMsg destinyCollectCardData = 2;</code>
     */
    public xddq.pb.DestinyCollectCardDataMsg.Builder addDestinyCollectCardDataBuilder() {
      return internalGetDestinyCollectCardDataFieldBuilder().addBuilder(
          xddq.pb.DestinyCollectCardDataMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.DestinyCollectCardDataMsg destinyCollectCardData = 2;</code>
     */
    public xddq.pb.DestinyCollectCardDataMsg.Builder addDestinyCollectCardDataBuilder(
        int index) {
      return internalGetDestinyCollectCardDataFieldBuilder().addBuilder(
          index, xddq.pb.DestinyCollectCardDataMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.DestinyCollectCardDataMsg destinyCollectCardData = 2;</code>
     */
    public java.util.List<xddq.pb.DestinyCollectCardDataMsg.Builder> 
         getDestinyCollectCardDataBuilderList() {
      return internalGetDestinyCollectCardDataFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.DestinyCollectCardDataMsg, xddq.pb.DestinyCollectCardDataMsg.Builder, xddq.pb.DestinyCollectCardDataMsgOrBuilder> 
        internalGetDestinyCollectCardDataFieldBuilder() {
      if (destinyCollectCardDataBuilder_ == null) {
        destinyCollectCardDataBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.DestinyCollectCardDataMsg, xddq.pb.DestinyCollectCardDataMsg.Builder, xddq.pb.DestinyCollectCardDataMsgOrBuilder>(
                destinyCollectCardData_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        destinyCollectCardData_ = null;
      }
      return destinyCollectCardDataBuilder_;
    }

    private java.util.List<xddq.pb.DestinyCollectCardGroupDataMsg> destinyCollectCardGroupDataMsg_ =
      java.util.Collections.emptyList();
    private void ensureDestinyCollectCardGroupDataMsgIsMutable() {
      if (!((bitField0_ & 0x00000004) != 0)) {
        destinyCollectCardGroupDataMsg_ = new java.util.ArrayList<xddq.pb.DestinyCollectCardGroupDataMsg>(destinyCollectCardGroupDataMsg_);
        bitField0_ |= 0x00000004;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.DestinyCollectCardGroupDataMsg, xddq.pb.DestinyCollectCardGroupDataMsg.Builder, xddq.pb.DestinyCollectCardGroupDataMsgOrBuilder> destinyCollectCardGroupDataMsgBuilder_;

    /**
     * <code>repeated .xddq.pb.DestinyCollectCardGroupDataMsg destinyCollectCardGroupDataMsg = 3;</code>
     */
    public java.util.List<xddq.pb.DestinyCollectCardGroupDataMsg> getDestinyCollectCardGroupDataMsgList() {
      if (destinyCollectCardGroupDataMsgBuilder_ == null) {
        return java.util.Collections.unmodifiableList(destinyCollectCardGroupDataMsg_);
      } else {
        return destinyCollectCardGroupDataMsgBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.DestinyCollectCardGroupDataMsg destinyCollectCardGroupDataMsg = 3;</code>
     */
    public int getDestinyCollectCardGroupDataMsgCount() {
      if (destinyCollectCardGroupDataMsgBuilder_ == null) {
        return destinyCollectCardGroupDataMsg_.size();
      } else {
        return destinyCollectCardGroupDataMsgBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.DestinyCollectCardGroupDataMsg destinyCollectCardGroupDataMsg = 3;</code>
     */
    public xddq.pb.DestinyCollectCardGroupDataMsg getDestinyCollectCardGroupDataMsg(int index) {
      if (destinyCollectCardGroupDataMsgBuilder_ == null) {
        return destinyCollectCardGroupDataMsg_.get(index);
      } else {
        return destinyCollectCardGroupDataMsgBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.DestinyCollectCardGroupDataMsg destinyCollectCardGroupDataMsg = 3;</code>
     */
    public Builder setDestinyCollectCardGroupDataMsg(
        int index, xddq.pb.DestinyCollectCardGroupDataMsg value) {
      if (destinyCollectCardGroupDataMsgBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDestinyCollectCardGroupDataMsgIsMutable();
        destinyCollectCardGroupDataMsg_.set(index, value);
        onChanged();
      } else {
        destinyCollectCardGroupDataMsgBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DestinyCollectCardGroupDataMsg destinyCollectCardGroupDataMsg = 3;</code>
     */
    public Builder setDestinyCollectCardGroupDataMsg(
        int index, xddq.pb.DestinyCollectCardGroupDataMsg.Builder builderForValue) {
      if (destinyCollectCardGroupDataMsgBuilder_ == null) {
        ensureDestinyCollectCardGroupDataMsgIsMutable();
        destinyCollectCardGroupDataMsg_.set(index, builderForValue.build());
        onChanged();
      } else {
        destinyCollectCardGroupDataMsgBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DestinyCollectCardGroupDataMsg destinyCollectCardGroupDataMsg = 3;</code>
     */
    public Builder addDestinyCollectCardGroupDataMsg(xddq.pb.DestinyCollectCardGroupDataMsg value) {
      if (destinyCollectCardGroupDataMsgBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDestinyCollectCardGroupDataMsgIsMutable();
        destinyCollectCardGroupDataMsg_.add(value);
        onChanged();
      } else {
        destinyCollectCardGroupDataMsgBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DestinyCollectCardGroupDataMsg destinyCollectCardGroupDataMsg = 3;</code>
     */
    public Builder addDestinyCollectCardGroupDataMsg(
        int index, xddq.pb.DestinyCollectCardGroupDataMsg value) {
      if (destinyCollectCardGroupDataMsgBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDestinyCollectCardGroupDataMsgIsMutable();
        destinyCollectCardGroupDataMsg_.add(index, value);
        onChanged();
      } else {
        destinyCollectCardGroupDataMsgBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DestinyCollectCardGroupDataMsg destinyCollectCardGroupDataMsg = 3;</code>
     */
    public Builder addDestinyCollectCardGroupDataMsg(
        xddq.pb.DestinyCollectCardGroupDataMsg.Builder builderForValue) {
      if (destinyCollectCardGroupDataMsgBuilder_ == null) {
        ensureDestinyCollectCardGroupDataMsgIsMutable();
        destinyCollectCardGroupDataMsg_.add(builderForValue.build());
        onChanged();
      } else {
        destinyCollectCardGroupDataMsgBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DestinyCollectCardGroupDataMsg destinyCollectCardGroupDataMsg = 3;</code>
     */
    public Builder addDestinyCollectCardGroupDataMsg(
        int index, xddq.pb.DestinyCollectCardGroupDataMsg.Builder builderForValue) {
      if (destinyCollectCardGroupDataMsgBuilder_ == null) {
        ensureDestinyCollectCardGroupDataMsgIsMutable();
        destinyCollectCardGroupDataMsg_.add(index, builderForValue.build());
        onChanged();
      } else {
        destinyCollectCardGroupDataMsgBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DestinyCollectCardGroupDataMsg destinyCollectCardGroupDataMsg = 3;</code>
     */
    public Builder addAllDestinyCollectCardGroupDataMsg(
        java.lang.Iterable<? extends xddq.pb.DestinyCollectCardGroupDataMsg> values) {
      if (destinyCollectCardGroupDataMsgBuilder_ == null) {
        ensureDestinyCollectCardGroupDataMsgIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, destinyCollectCardGroupDataMsg_);
        onChanged();
      } else {
        destinyCollectCardGroupDataMsgBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DestinyCollectCardGroupDataMsg destinyCollectCardGroupDataMsg = 3;</code>
     */
    public Builder clearDestinyCollectCardGroupDataMsg() {
      if (destinyCollectCardGroupDataMsgBuilder_ == null) {
        destinyCollectCardGroupDataMsg_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
      } else {
        destinyCollectCardGroupDataMsgBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DestinyCollectCardGroupDataMsg destinyCollectCardGroupDataMsg = 3;</code>
     */
    public Builder removeDestinyCollectCardGroupDataMsg(int index) {
      if (destinyCollectCardGroupDataMsgBuilder_ == null) {
        ensureDestinyCollectCardGroupDataMsgIsMutable();
        destinyCollectCardGroupDataMsg_.remove(index);
        onChanged();
      } else {
        destinyCollectCardGroupDataMsgBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DestinyCollectCardGroupDataMsg destinyCollectCardGroupDataMsg = 3;</code>
     */
    public xddq.pb.DestinyCollectCardGroupDataMsg.Builder getDestinyCollectCardGroupDataMsgBuilder(
        int index) {
      return internalGetDestinyCollectCardGroupDataMsgFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.DestinyCollectCardGroupDataMsg destinyCollectCardGroupDataMsg = 3;</code>
     */
    public xddq.pb.DestinyCollectCardGroupDataMsgOrBuilder getDestinyCollectCardGroupDataMsgOrBuilder(
        int index) {
      if (destinyCollectCardGroupDataMsgBuilder_ == null) {
        return destinyCollectCardGroupDataMsg_.get(index);  } else {
        return destinyCollectCardGroupDataMsgBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.DestinyCollectCardGroupDataMsg destinyCollectCardGroupDataMsg = 3;</code>
     */
    public java.util.List<? extends xddq.pb.DestinyCollectCardGroupDataMsgOrBuilder> 
         getDestinyCollectCardGroupDataMsgOrBuilderList() {
      if (destinyCollectCardGroupDataMsgBuilder_ != null) {
        return destinyCollectCardGroupDataMsgBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(destinyCollectCardGroupDataMsg_);
      }
    }
    /**
     * <code>repeated .xddq.pb.DestinyCollectCardGroupDataMsg destinyCollectCardGroupDataMsg = 3;</code>
     */
    public xddq.pb.DestinyCollectCardGroupDataMsg.Builder addDestinyCollectCardGroupDataMsgBuilder() {
      return internalGetDestinyCollectCardGroupDataMsgFieldBuilder().addBuilder(
          xddq.pb.DestinyCollectCardGroupDataMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.DestinyCollectCardGroupDataMsg destinyCollectCardGroupDataMsg = 3;</code>
     */
    public xddq.pb.DestinyCollectCardGroupDataMsg.Builder addDestinyCollectCardGroupDataMsgBuilder(
        int index) {
      return internalGetDestinyCollectCardGroupDataMsgFieldBuilder().addBuilder(
          index, xddq.pb.DestinyCollectCardGroupDataMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.DestinyCollectCardGroupDataMsg destinyCollectCardGroupDataMsg = 3;</code>
     */
    public java.util.List<xddq.pb.DestinyCollectCardGroupDataMsg.Builder> 
         getDestinyCollectCardGroupDataMsgBuilderList() {
      return internalGetDestinyCollectCardGroupDataMsgFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.DestinyCollectCardGroupDataMsg, xddq.pb.DestinyCollectCardGroupDataMsg.Builder, xddq.pb.DestinyCollectCardGroupDataMsgOrBuilder> 
        internalGetDestinyCollectCardGroupDataMsgFieldBuilder() {
      if (destinyCollectCardGroupDataMsgBuilder_ == null) {
        destinyCollectCardGroupDataMsgBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.DestinyCollectCardGroupDataMsg, xddq.pb.DestinyCollectCardGroupDataMsg.Builder, xddq.pb.DestinyCollectCardGroupDataMsgOrBuilder>(
                destinyCollectCardGroupDataMsg_,
                ((bitField0_ & 0x00000004) != 0),
                getParentForChildren(),
                isClean());
        destinyCollectCardGroupDataMsg_ = null;
      }
      return destinyCollectCardGroupDataMsgBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.DestinyCollectDataMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.DestinyCollectDataMsg)
  private static final xddq.pb.DestinyCollectDataMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.DestinyCollectDataMsg();
  }

  public static xddq.pb.DestinyCollectDataMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<DestinyCollectDataMsg>
      PARSER = new com.google.protobuf.AbstractParser<DestinyCollectDataMsg>() {
    @java.lang.Override
    public DestinyCollectDataMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<DestinyCollectDataMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<DestinyCollectDataMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.DestinyCollectDataMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

