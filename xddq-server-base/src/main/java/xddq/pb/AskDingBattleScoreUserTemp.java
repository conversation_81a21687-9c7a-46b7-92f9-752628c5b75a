// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.AskDingBattleScoreUserTemp}
 */
public final class AskDingBattleScoreUserTemp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.AskDingBattleScoreUserTemp)
    AskDingBattleScoreUserTempOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      AskDingBattleScoreUserTemp.class.getName());
  }
  // Use AskDingBattleScoreUserTemp.newBuilder() to construct.
  private AskDingBattleScoreUserTemp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private AskDingBattleScoreUserTemp() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_AskDingBattleScoreUserTemp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_AskDingBattleScoreUserTemp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.AskDingBattleScoreUserTemp.class, xddq.pb.AskDingBattleScoreUserTemp.Builder.class);
  }

  private int bitField0_;
  public static final int PLAYERID_FIELD_NUMBER = 1;
  private long playerId_ = 0L;
  /**
   * <code>required int64 playerId = 1;</code>
   * @return Whether the playerId field is set.
   */
  @java.lang.Override
  public boolean hasPlayerId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int64 playerId = 1;</code>
   * @return The playerId.
   */
  @java.lang.Override
  public long getPlayerId() {
    return playerId_;
  }

  public static final int USER_FIELD_NUMBER = 2;
  private xddq.pb.PlayerCharacterImageMsg user_;
  /**
   * <code>required .xddq.pb.PlayerCharacterImageMsg user = 2;</code>
   * @return Whether the user field is set.
   */
  @java.lang.Override
  public boolean hasUser() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>required .xddq.pb.PlayerCharacterImageMsg user = 2;</code>
   * @return The user.
   */
  @java.lang.Override
  public xddq.pb.PlayerCharacterImageMsg getUser() {
    return user_ == null ? xddq.pb.PlayerCharacterImageMsg.getDefaultInstance() : user_;
  }
  /**
   * <code>required .xddq.pb.PlayerCharacterImageMsg user = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.PlayerCharacterImageMsgOrBuilder getUserOrBuilder() {
    return user_ == null ? xddq.pb.PlayerCharacterImageMsg.getDefaultInstance() : user_;
  }

  public static final int SCORE_FIELD_NUMBER = 3;
  private xddq.pb.AskDingBattleScoreTemp score_;
  /**
   * <code>required .xddq.pb.AskDingBattleScoreTemp score = 3;</code>
   * @return Whether the score field is set.
   */
  @java.lang.Override
  public boolean hasScore() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>required .xddq.pb.AskDingBattleScoreTemp score = 3;</code>
   * @return The score.
   */
  @java.lang.Override
  public xddq.pb.AskDingBattleScoreTemp getScore() {
    return score_ == null ? xddq.pb.AskDingBattleScoreTemp.getDefaultInstance() : score_;
  }
  /**
   * <code>required .xddq.pb.AskDingBattleScoreTemp score = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.AskDingBattleScoreTempOrBuilder getScoreOrBuilder() {
    return score_ == null ? xddq.pb.AskDingBattleScoreTemp.getDefaultInstance() : score_;
  }

  public static final int UNIONID_FIELD_NUMBER = 4;
  private int unionId_ = 0;
  /**
   * <code>required int32 unionId = 4;</code>
   * @return Whether the unionId field is set.
   */
  @java.lang.Override
  public boolean hasUnionId() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>required int32 unionId = 4;</code>
   * @return The unionId.
   */
  @java.lang.Override
  public int getUnionId() {
    return unionId_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasPlayerId()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasUser()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasScore()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasUnionId()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!getScore().isInitialized()) {
      memoizedIsInitialized = 0;
      return false;
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt64(1, playerId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeMessage(2, getUser());
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeMessage(3, getScore());
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt32(4, unionId_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(1, playerId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getUser());
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, getScore());
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(4, unionId_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.AskDingBattleScoreUserTemp)) {
      return super.equals(obj);
    }
    xddq.pb.AskDingBattleScoreUserTemp other = (xddq.pb.AskDingBattleScoreUserTemp) obj;

    if (hasPlayerId() != other.hasPlayerId()) return false;
    if (hasPlayerId()) {
      if (getPlayerId()
          != other.getPlayerId()) return false;
    }
    if (hasUser() != other.hasUser()) return false;
    if (hasUser()) {
      if (!getUser()
          .equals(other.getUser())) return false;
    }
    if (hasScore() != other.hasScore()) return false;
    if (hasScore()) {
      if (!getScore()
          .equals(other.getScore())) return false;
    }
    if (hasUnionId() != other.hasUnionId()) return false;
    if (hasUnionId()) {
      if (getUnionId()
          != other.getUnionId()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasPlayerId()) {
      hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getPlayerId());
    }
    if (hasUser()) {
      hash = (37 * hash) + USER_FIELD_NUMBER;
      hash = (53 * hash) + getUser().hashCode();
    }
    if (hasScore()) {
      hash = (37 * hash) + SCORE_FIELD_NUMBER;
      hash = (53 * hash) + getScore().hashCode();
    }
    if (hasUnionId()) {
      hash = (37 * hash) + UNIONID_FIELD_NUMBER;
      hash = (53 * hash) + getUnionId();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.AskDingBattleScoreUserTemp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.AskDingBattleScoreUserTemp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.AskDingBattleScoreUserTemp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.AskDingBattleScoreUserTemp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.AskDingBattleScoreUserTemp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.AskDingBattleScoreUserTemp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.AskDingBattleScoreUserTemp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.AskDingBattleScoreUserTemp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.AskDingBattleScoreUserTemp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.AskDingBattleScoreUserTemp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.AskDingBattleScoreUserTemp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.AskDingBattleScoreUserTemp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.AskDingBattleScoreUserTemp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.AskDingBattleScoreUserTemp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.AskDingBattleScoreUserTemp)
      xddq.pb.AskDingBattleScoreUserTempOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_AskDingBattleScoreUserTemp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_AskDingBattleScoreUserTemp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.AskDingBattleScoreUserTemp.class, xddq.pb.AskDingBattleScoreUserTemp.Builder.class);
    }

    // Construct using xddq.pb.AskDingBattleScoreUserTemp.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetUserFieldBuilder();
        internalGetScoreFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      playerId_ = 0L;
      user_ = null;
      if (userBuilder_ != null) {
        userBuilder_.dispose();
        userBuilder_ = null;
      }
      score_ = null;
      if (scoreBuilder_ != null) {
        scoreBuilder_.dispose();
        scoreBuilder_ = null;
      }
      unionId_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_AskDingBattleScoreUserTemp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.AskDingBattleScoreUserTemp getDefaultInstanceForType() {
      return xddq.pb.AskDingBattleScoreUserTemp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.AskDingBattleScoreUserTemp build() {
      xddq.pb.AskDingBattleScoreUserTemp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.AskDingBattleScoreUserTemp buildPartial() {
      xddq.pb.AskDingBattleScoreUserTemp result = new xddq.pb.AskDingBattleScoreUserTemp(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.AskDingBattleScoreUserTemp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.playerId_ = playerId_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.user_ = userBuilder_ == null
            ? user_
            : userBuilder_.build();
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.score_ = scoreBuilder_ == null
            ? score_
            : scoreBuilder_.build();
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.unionId_ = unionId_;
        to_bitField0_ |= 0x00000008;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.AskDingBattleScoreUserTemp) {
        return mergeFrom((xddq.pb.AskDingBattleScoreUserTemp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.AskDingBattleScoreUserTemp other) {
      if (other == xddq.pb.AskDingBattleScoreUserTemp.getDefaultInstance()) return this;
      if (other.hasPlayerId()) {
        setPlayerId(other.getPlayerId());
      }
      if (other.hasUser()) {
        mergeUser(other.getUser());
      }
      if (other.hasScore()) {
        mergeScore(other.getScore());
      }
      if (other.hasUnionId()) {
        setUnionId(other.getUnionId());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasPlayerId()) {
        return false;
      }
      if (!hasUser()) {
        return false;
      }
      if (!hasScore()) {
        return false;
      }
      if (!hasUnionId()) {
        return false;
      }
      if (!getScore().isInitialized()) {
        return false;
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              playerId_ = input.readInt64();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              input.readMessage(
                  internalGetUserFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 26: {
              input.readMessage(
                  internalGetScoreFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 32: {
              unionId_ = input.readInt32();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private long playerId_ ;
    /**
     * <code>required int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int64 playerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }
    /**
     * <code>required int64 playerId = 1;</code>
     * @param value The playerId to set.
     * @return This builder for chaining.
     */
    public Builder setPlayerId(long value) {

      playerId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int64 playerId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearPlayerId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      playerId_ = 0L;
      onChanged();
      return this;
    }

    private xddq.pb.PlayerCharacterImageMsg user_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PlayerCharacterImageMsg, xddq.pb.PlayerCharacterImageMsg.Builder, xddq.pb.PlayerCharacterImageMsgOrBuilder> userBuilder_;
    /**
     * <code>required .xddq.pb.PlayerCharacterImageMsg user = 2;</code>
     * @return Whether the user field is set.
     */
    public boolean hasUser() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>required .xddq.pb.PlayerCharacterImageMsg user = 2;</code>
     * @return The user.
     */
    public xddq.pb.PlayerCharacterImageMsg getUser() {
      if (userBuilder_ == null) {
        return user_ == null ? xddq.pb.PlayerCharacterImageMsg.getDefaultInstance() : user_;
      } else {
        return userBuilder_.getMessage();
      }
    }
    /**
     * <code>required .xddq.pb.PlayerCharacterImageMsg user = 2;</code>
     */
    public Builder setUser(xddq.pb.PlayerCharacterImageMsg value) {
      if (userBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        user_ = value;
      } else {
        userBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>required .xddq.pb.PlayerCharacterImageMsg user = 2;</code>
     */
    public Builder setUser(
        xddq.pb.PlayerCharacterImageMsg.Builder builderForValue) {
      if (userBuilder_ == null) {
        user_ = builderForValue.build();
      } else {
        userBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>required .xddq.pb.PlayerCharacterImageMsg user = 2;</code>
     */
    public Builder mergeUser(xddq.pb.PlayerCharacterImageMsg value) {
      if (userBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0) &&
          user_ != null &&
          user_ != xddq.pb.PlayerCharacterImageMsg.getDefaultInstance()) {
          getUserBuilder().mergeFrom(value);
        } else {
          user_ = value;
        }
      } else {
        userBuilder_.mergeFrom(value);
      }
      if (user_ != null) {
        bitField0_ |= 0x00000002;
        onChanged();
      }
      return this;
    }
    /**
     * <code>required .xddq.pb.PlayerCharacterImageMsg user = 2;</code>
     */
    public Builder clearUser() {
      bitField0_ = (bitField0_ & ~0x00000002);
      user_ = null;
      if (userBuilder_ != null) {
        userBuilder_.dispose();
        userBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>required .xddq.pb.PlayerCharacterImageMsg user = 2;</code>
     */
    public xddq.pb.PlayerCharacterImageMsg.Builder getUserBuilder() {
      bitField0_ |= 0x00000002;
      onChanged();
      return internalGetUserFieldBuilder().getBuilder();
    }
    /**
     * <code>required .xddq.pb.PlayerCharacterImageMsg user = 2;</code>
     */
    public xddq.pb.PlayerCharacterImageMsgOrBuilder getUserOrBuilder() {
      if (userBuilder_ != null) {
        return userBuilder_.getMessageOrBuilder();
      } else {
        return user_ == null ?
            xddq.pb.PlayerCharacterImageMsg.getDefaultInstance() : user_;
      }
    }
    /**
     * <code>required .xddq.pb.PlayerCharacterImageMsg user = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PlayerCharacterImageMsg, xddq.pb.PlayerCharacterImageMsg.Builder, xddq.pb.PlayerCharacterImageMsgOrBuilder> 
        internalGetUserFieldBuilder() {
      if (userBuilder_ == null) {
        userBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.PlayerCharacterImageMsg, xddq.pb.PlayerCharacterImageMsg.Builder, xddq.pb.PlayerCharacterImageMsgOrBuilder>(
                getUser(),
                getParentForChildren(),
                isClean());
        user_ = null;
      }
      return userBuilder_;
    }

    private xddq.pb.AskDingBattleScoreTemp score_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.AskDingBattleScoreTemp, xddq.pb.AskDingBattleScoreTemp.Builder, xddq.pb.AskDingBattleScoreTempOrBuilder> scoreBuilder_;
    /**
     * <code>required .xddq.pb.AskDingBattleScoreTemp score = 3;</code>
     * @return Whether the score field is set.
     */
    public boolean hasScore() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>required .xddq.pb.AskDingBattleScoreTemp score = 3;</code>
     * @return The score.
     */
    public xddq.pb.AskDingBattleScoreTemp getScore() {
      if (scoreBuilder_ == null) {
        return score_ == null ? xddq.pb.AskDingBattleScoreTemp.getDefaultInstance() : score_;
      } else {
        return scoreBuilder_.getMessage();
      }
    }
    /**
     * <code>required .xddq.pb.AskDingBattleScoreTemp score = 3;</code>
     */
    public Builder setScore(xddq.pb.AskDingBattleScoreTemp value) {
      if (scoreBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        score_ = value;
      } else {
        scoreBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>required .xddq.pb.AskDingBattleScoreTemp score = 3;</code>
     */
    public Builder setScore(
        xddq.pb.AskDingBattleScoreTemp.Builder builderForValue) {
      if (scoreBuilder_ == null) {
        score_ = builderForValue.build();
      } else {
        scoreBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>required .xddq.pb.AskDingBattleScoreTemp score = 3;</code>
     */
    public Builder mergeScore(xddq.pb.AskDingBattleScoreTemp value) {
      if (scoreBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0) &&
          score_ != null &&
          score_ != xddq.pb.AskDingBattleScoreTemp.getDefaultInstance()) {
          getScoreBuilder().mergeFrom(value);
        } else {
          score_ = value;
        }
      } else {
        scoreBuilder_.mergeFrom(value);
      }
      if (score_ != null) {
        bitField0_ |= 0x00000004;
        onChanged();
      }
      return this;
    }
    /**
     * <code>required .xddq.pb.AskDingBattleScoreTemp score = 3;</code>
     */
    public Builder clearScore() {
      bitField0_ = (bitField0_ & ~0x00000004);
      score_ = null;
      if (scoreBuilder_ != null) {
        scoreBuilder_.dispose();
        scoreBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>required .xddq.pb.AskDingBattleScoreTemp score = 3;</code>
     */
    public xddq.pb.AskDingBattleScoreTemp.Builder getScoreBuilder() {
      bitField0_ |= 0x00000004;
      onChanged();
      return internalGetScoreFieldBuilder().getBuilder();
    }
    /**
     * <code>required .xddq.pb.AskDingBattleScoreTemp score = 3;</code>
     */
    public xddq.pb.AskDingBattleScoreTempOrBuilder getScoreOrBuilder() {
      if (scoreBuilder_ != null) {
        return scoreBuilder_.getMessageOrBuilder();
      } else {
        return score_ == null ?
            xddq.pb.AskDingBattleScoreTemp.getDefaultInstance() : score_;
      }
    }
    /**
     * <code>required .xddq.pb.AskDingBattleScoreTemp score = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.AskDingBattleScoreTemp, xddq.pb.AskDingBattleScoreTemp.Builder, xddq.pb.AskDingBattleScoreTempOrBuilder> 
        internalGetScoreFieldBuilder() {
      if (scoreBuilder_ == null) {
        scoreBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.AskDingBattleScoreTemp, xddq.pb.AskDingBattleScoreTemp.Builder, xddq.pb.AskDingBattleScoreTempOrBuilder>(
                getScore(),
                getParentForChildren(),
                isClean());
        score_ = null;
      }
      return scoreBuilder_;
    }

    private int unionId_ ;
    /**
     * <code>required int32 unionId = 4;</code>
     * @return Whether the unionId field is set.
     */
    @java.lang.Override
    public boolean hasUnionId() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>required int32 unionId = 4;</code>
     * @return The unionId.
     */
    @java.lang.Override
    public int getUnionId() {
      return unionId_;
    }
    /**
     * <code>required int32 unionId = 4;</code>
     * @param value The unionId to set.
     * @return This builder for chaining.
     */
    public Builder setUnionId(int value) {

      unionId_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 unionId = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearUnionId() {
      bitField0_ = (bitField0_ & ~0x00000008);
      unionId_ = 0;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.AskDingBattleScoreUserTemp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.AskDingBattleScoreUserTemp)
  private static final xddq.pb.AskDingBattleScoreUserTemp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.AskDingBattleScoreUserTemp();
  }

  public static xddq.pb.AskDingBattleScoreUserTemp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<AskDingBattleScoreUserTemp>
      PARSER = new com.google.protobuf.AbstractParser<AskDingBattleScoreUserTemp>() {
    @java.lang.Override
    public AskDingBattleScoreUserTemp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<AskDingBattleScoreUserTemp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<AskDingBattleScoreUserTemp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.AskDingBattleScoreUserTemp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

