// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.ExploreTravelPassTodayResp}
 */
public final class ExploreTravelPassTodayResp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.ExploreTravelPassTodayResp)
    ExploreTravelPassTodayRespOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      ExploreTravelPassTodayResp.class.getName());
  }
  // Use ExploreTravelPassTodayResp.newBuilder() to construct.
  private ExploreTravelPassTodayResp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private ExploreTravelPassTodayResp() {
    todayReward_ = java.util.Collections.emptyList();
    params_ = emptyIntList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_ExploreTravelPassTodayResp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_ExploreTravelPassTodayResp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.ExploreTravelPassTodayResp.class, xddq.pb.ExploreTravelPassTodayResp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>optional int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int TRAVEL_FIELD_NUMBER = 2;
  private xddq.pb.ExploreTravelMsg travel_;
  /**
   * <code>optional .xddq.pb.ExploreTravelMsg travel = 2;</code>
   * @return Whether the travel field is set.
   */
  @java.lang.Override
  public boolean hasTravel() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional .xddq.pb.ExploreTravelMsg travel = 2;</code>
   * @return The travel.
   */
  @java.lang.Override
  public xddq.pb.ExploreTravelMsg getTravel() {
    return travel_ == null ? xddq.pb.ExploreTravelMsg.getDefaultInstance() : travel_;
  }
  /**
   * <code>optional .xddq.pb.ExploreTravelMsg travel = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.ExploreTravelMsgOrBuilder getTravelOrBuilder() {
    return travel_ == null ? xddq.pb.ExploreTravelMsg.getDefaultInstance() : travel_;
  }

  public static final int END_FIELD_NUMBER = 3;
  private xddq.pb.ExploreTravelEndMsg end_;
  /**
   * <code>optional .xddq.pb.ExploreTravelEndMsg end = 3;</code>
   * @return Whether the end field is set.
   */
  @java.lang.Override
  public boolean hasEnd() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional .xddq.pb.ExploreTravelEndMsg end = 3;</code>
   * @return The end.
   */
  @java.lang.Override
  public xddq.pb.ExploreTravelEndMsg getEnd() {
    return end_ == null ? xddq.pb.ExploreTravelEndMsg.getDefaultInstance() : end_;
  }
  /**
   * <code>optional .xddq.pb.ExploreTravelEndMsg end = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.ExploreTravelEndMsgOrBuilder getEndOrBuilder() {
    return end_ == null ? xddq.pb.ExploreTravelEndMsg.getDefaultInstance() : end_;
  }

  public static final int TODAYREWARD_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.AwardInfo> todayReward_;
  /**
   * <code>repeated .xddq.pb.AwardInfo todayReward = 4;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.AwardInfo> getTodayRewardList() {
    return todayReward_;
  }
  /**
   * <code>repeated .xddq.pb.AwardInfo todayReward = 4;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.AwardInfoOrBuilder> 
      getTodayRewardOrBuilderList() {
    return todayReward_;
  }
  /**
   * <code>repeated .xddq.pb.AwardInfo todayReward = 4;</code>
   */
  @java.lang.Override
  public int getTodayRewardCount() {
    return todayReward_.size();
  }
  /**
   * <code>repeated .xddq.pb.AwardInfo todayReward = 4;</code>
   */
  @java.lang.Override
  public xddq.pb.AwardInfo getTodayReward(int index) {
    return todayReward_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.AwardInfo todayReward = 4;</code>
   */
  @java.lang.Override
  public xddq.pb.AwardInfoOrBuilder getTodayRewardOrBuilder(
      int index) {
    return todayReward_.get(index);
  }

  public static final int PARAMS_FIELD_NUMBER = 5;
  @SuppressWarnings("serial")
  private com.google.protobuf.Internal.IntList params_ =
      emptyIntList();
  /**
   * <code>repeated int32 params = 5;</code>
   * @return A list containing the params.
   */
  @java.lang.Override
  public java.util.List<java.lang.Integer>
      getParamsList() {
    return params_;
  }
  /**
   * <code>repeated int32 params = 5;</code>
   * @return The count of params.
   */
  public int getParamsCount() {
    return params_.size();
  }
  /**
   * <code>repeated int32 params = 5;</code>
   * @param index The index of the element to return.
   * @return The params at the given index.
   */
  public int getParams(int index) {
    return params_.getInt(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeMessage(2, getTravel());
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeMessage(3, getEnd());
    }
    for (int i = 0; i < todayReward_.size(); i++) {
      output.writeMessage(4, todayReward_.get(i));
    }
    for (int i = 0; i < params_.size(); i++) {
      output.writeInt32(5, params_.getInt(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getTravel());
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, getEnd());
    }
    for (int i = 0; i < todayReward_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(4, todayReward_.get(i));
    }
    {
      int dataSize = 0;
      for (int i = 0; i < params_.size(); i++) {
        dataSize += com.google.protobuf.CodedOutputStream
          .computeInt32SizeNoTag(params_.getInt(i));
      }
      size += dataSize;
      size += 1 * getParamsList().size();
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.ExploreTravelPassTodayResp)) {
      return super.equals(obj);
    }
    xddq.pb.ExploreTravelPassTodayResp other = (xddq.pb.ExploreTravelPassTodayResp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (hasTravel() != other.hasTravel()) return false;
    if (hasTravel()) {
      if (!getTravel()
          .equals(other.getTravel())) return false;
    }
    if (hasEnd() != other.hasEnd()) return false;
    if (hasEnd()) {
      if (!getEnd()
          .equals(other.getEnd())) return false;
    }
    if (!getTodayRewardList()
        .equals(other.getTodayRewardList())) return false;
    if (!getParamsList()
        .equals(other.getParamsList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (hasTravel()) {
      hash = (37 * hash) + TRAVEL_FIELD_NUMBER;
      hash = (53 * hash) + getTravel().hashCode();
    }
    if (hasEnd()) {
      hash = (37 * hash) + END_FIELD_NUMBER;
      hash = (53 * hash) + getEnd().hashCode();
    }
    if (getTodayRewardCount() > 0) {
      hash = (37 * hash) + TODAYREWARD_FIELD_NUMBER;
      hash = (53 * hash) + getTodayRewardList().hashCode();
    }
    if (getParamsCount() > 0) {
      hash = (37 * hash) + PARAMS_FIELD_NUMBER;
      hash = (53 * hash) + getParamsList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.ExploreTravelPassTodayResp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ExploreTravelPassTodayResp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ExploreTravelPassTodayResp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ExploreTravelPassTodayResp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ExploreTravelPassTodayResp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ExploreTravelPassTodayResp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ExploreTravelPassTodayResp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.ExploreTravelPassTodayResp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.ExploreTravelPassTodayResp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.ExploreTravelPassTodayResp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.ExploreTravelPassTodayResp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.ExploreTravelPassTodayResp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.ExploreTravelPassTodayResp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.ExploreTravelPassTodayResp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.ExploreTravelPassTodayResp)
      xddq.pb.ExploreTravelPassTodayRespOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ExploreTravelPassTodayResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ExploreTravelPassTodayResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.ExploreTravelPassTodayResp.class, xddq.pb.ExploreTravelPassTodayResp.Builder.class);
    }

    // Construct using xddq.pb.ExploreTravelPassTodayResp.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetTravelFieldBuilder();
        internalGetEndFieldBuilder();
        internalGetTodayRewardFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      travel_ = null;
      if (travelBuilder_ != null) {
        travelBuilder_.dispose();
        travelBuilder_ = null;
      }
      end_ = null;
      if (endBuilder_ != null) {
        endBuilder_.dispose();
        endBuilder_ = null;
      }
      if (todayRewardBuilder_ == null) {
        todayReward_ = java.util.Collections.emptyList();
      } else {
        todayReward_ = null;
        todayRewardBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000008);
      params_ = emptyIntList();
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ExploreTravelPassTodayResp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.ExploreTravelPassTodayResp getDefaultInstanceForType() {
      return xddq.pb.ExploreTravelPassTodayResp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.ExploreTravelPassTodayResp build() {
      xddq.pb.ExploreTravelPassTodayResp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.ExploreTravelPassTodayResp buildPartial() {
      xddq.pb.ExploreTravelPassTodayResp result = new xddq.pb.ExploreTravelPassTodayResp(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.ExploreTravelPassTodayResp result) {
      if (todayRewardBuilder_ == null) {
        if (((bitField0_ & 0x00000008) != 0)) {
          todayReward_ = java.util.Collections.unmodifiableList(todayReward_);
          bitField0_ = (bitField0_ & ~0x00000008);
        }
        result.todayReward_ = todayReward_;
      } else {
        result.todayReward_ = todayRewardBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.ExploreTravelPassTodayResp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.travel_ = travelBuilder_ == null
            ? travel_
            : travelBuilder_.build();
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.end_ = endBuilder_ == null
            ? end_
            : endBuilder_.build();
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        params_.makeImmutable();
        result.params_ = params_;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.ExploreTravelPassTodayResp) {
        return mergeFrom((xddq.pb.ExploreTravelPassTodayResp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.ExploreTravelPassTodayResp other) {
      if (other == xddq.pb.ExploreTravelPassTodayResp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (other.hasTravel()) {
        mergeTravel(other.getTravel());
      }
      if (other.hasEnd()) {
        mergeEnd(other.getEnd());
      }
      if (todayRewardBuilder_ == null) {
        if (!other.todayReward_.isEmpty()) {
          if (todayReward_.isEmpty()) {
            todayReward_ = other.todayReward_;
            bitField0_ = (bitField0_ & ~0x00000008);
          } else {
            ensureTodayRewardIsMutable();
            todayReward_.addAll(other.todayReward_);
          }
          onChanged();
        }
      } else {
        if (!other.todayReward_.isEmpty()) {
          if (todayRewardBuilder_.isEmpty()) {
            todayRewardBuilder_.dispose();
            todayRewardBuilder_ = null;
            todayReward_ = other.todayReward_;
            bitField0_ = (bitField0_ & ~0x00000008);
            todayRewardBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetTodayRewardFieldBuilder() : null;
          } else {
            todayRewardBuilder_.addAllMessages(other.todayReward_);
          }
        }
      }
      if (!other.params_.isEmpty()) {
        if (params_.isEmpty()) {
          params_ = other.params_;
          params_.makeImmutable();
          bitField0_ |= 0x00000010;
        } else {
          ensureParamsIsMutable();
          params_.addAll(other.params_);
        }
        onChanged();
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              input.readMessage(
                  internalGetTravelFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 26: {
              input.readMessage(
                  internalGetEndFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 34: {
              xddq.pb.AwardInfo m =
                  input.readMessage(
                      xddq.pb.AwardInfo.parser(),
                      extensionRegistry);
              if (todayRewardBuilder_ == null) {
                ensureTodayRewardIsMutable();
                todayReward_.add(m);
              } else {
                todayRewardBuilder_.addMessage(m);
              }
              break;
            } // case 34
            case 40: {
              int v = input.readInt32();
              ensureParamsIsMutable();
              params_.addInt(v);
              break;
            } // case 40
            case 42: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              ensureParamsIsMutable();
              while (input.getBytesUntilLimit() > 0) {
                params_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            } // case 42
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>optional int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>optional int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private xddq.pb.ExploreTravelMsg travel_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.ExploreTravelMsg, xddq.pb.ExploreTravelMsg.Builder, xddq.pb.ExploreTravelMsgOrBuilder> travelBuilder_;
    /**
     * <code>optional .xddq.pb.ExploreTravelMsg travel = 2;</code>
     * @return Whether the travel field is set.
     */
    public boolean hasTravel() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .xddq.pb.ExploreTravelMsg travel = 2;</code>
     * @return The travel.
     */
    public xddq.pb.ExploreTravelMsg getTravel() {
      if (travelBuilder_ == null) {
        return travel_ == null ? xddq.pb.ExploreTravelMsg.getDefaultInstance() : travel_;
      } else {
        return travelBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.ExploreTravelMsg travel = 2;</code>
     */
    public Builder setTravel(xddq.pb.ExploreTravelMsg value) {
      if (travelBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        travel_ = value;
      } else {
        travelBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ExploreTravelMsg travel = 2;</code>
     */
    public Builder setTravel(
        xddq.pb.ExploreTravelMsg.Builder builderForValue) {
      if (travelBuilder_ == null) {
        travel_ = builderForValue.build();
      } else {
        travelBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ExploreTravelMsg travel = 2;</code>
     */
    public Builder mergeTravel(xddq.pb.ExploreTravelMsg value) {
      if (travelBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0) &&
          travel_ != null &&
          travel_ != xddq.pb.ExploreTravelMsg.getDefaultInstance()) {
          getTravelBuilder().mergeFrom(value);
        } else {
          travel_ = value;
        }
      } else {
        travelBuilder_.mergeFrom(value);
      }
      if (travel_ != null) {
        bitField0_ |= 0x00000002;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.ExploreTravelMsg travel = 2;</code>
     */
    public Builder clearTravel() {
      bitField0_ = (bitField0_ & ~0x00000002);
      travel_ = null;
      if (travelBuilder_ != null) {
        travelBuilder_.dispose();
        travelBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ExploreTravelMsg travel = 2;</code>
     */
    public xddq.pb.ExploreTravelMsg.Builder getTravelBuilder() {
      bitField0_ |= 0x00000002;
      onChanged();
      return internalGetTravelFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.ExploreTravelMsg travel = 2;</code>
     */
    public xddq.pb.ExploreTravelMsgOrBuilder getTravelOrBuilder() {
      if (travelBuilder_ != null) {
        return travelBuilder_.getMessageOrBuilder();
      } else {
        return travel_ == null ?
            xddq.pb.ExploreTravelMsg.getDefaultInstance() : travel_;
      }
    }
    /**
     * <code>optional .xddq.pb.ExploreTravelMsg travel = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.ExploreTravelMsg, xddq.pb.ExploreTravelMsg.Builder, xddq.pb.ExploreTravelMsgOrBuilder> 
        internalGetTravelFieldBuilder() {
      if (travelBuilder_ == null) {
        travelBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.ExploreTravelMsg, xddq.pb.ExploreTravelMsg.Builder, xddq.pb.ExploreTravelMsgOrBuilder>(
                getTravel(),
                getParentForChildren(),
                isClean());
        travel_ = null;
      }
      return travelBuilder_;
    }

    private xddq.pb.ExploreTravelEndMsg end_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.ExploreTravelEndMsg, xddq.pb.ExploreTravelEndMsg.Builder, xddq.pb.ExploreTravelEndMsgOrBuilder> endBuilder_;
    /**
     * <code>optional .xddq.pb.ExploreTravelEndMsg end = 3;</code>
     * @return Whether the end field is set.
     */
    public boolean hasEnd() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional .xddq.pb.ExploreTravelEndMsg end = 3;</code>
     * @return The end.
     */
    public xddq.pb.ExploreTravelEndMsg getEnd() {
      if (endBuilder_ == null) {
        return end_ == null ? xddq.pb.ExploreTravelEndMsg.getDefaultInstance() : end_;
      } else {
        return endBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.ExploreTravelEndMsg end = 3;</code>
     */
    public Builder setEnd(xddq.pb.ExploreTravelEndMsg value) {
      if (endBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        end_ = value;
      } else {
        endBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ExploreTravelEndMsg end = 3;</code>
     */
    public Builder setEnd(
        xddq.pb.ExploreTravelEndMsg.Builder builderForValue) {
      if (endBuilder_ == null) {
        end_ = builderForValue.build();
      } else {
        endBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ExploreTravelEndMsg end = 3;</code>
     */
    public Builder mergeEnd(xddq.pb.ExploreTravelEndMsg value) {
      if (endBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0) &&
          end_ != null &&
          end_ != xddq.pb.ExploreTravelEndMsg.getDefaultInstance()) {
          getEndBuilder().mergeFrom(value);
        } else {
          end_ = value;
        }
      } else {
        endBuilder_.mergeFrom(value);
      }
      if (end_ != null) {
        bitField0_ |= 0x00000004;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.ExploreTravelEndMsg end = 3;</code>
     */
    public Builder clearEnd() {
      bitField0_ = (bitField0_ & ~0x00000004);
      end_ = null;
      if (endBuilder_ != null) {
        endBuilder_.dispose();
        endBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ExploreTravelEndMsg end = 3;</code>
     */
    public xddq.pb.ExploreTravelEndMsg.Builder getEndBuilder() {
      bitField0_ |= 0x00000004;
      onChanged();
      return internalGetEndFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.ExploreTravelEndMsg end = 3;</code>
     */
    public xddq.pb.ExploreTravelEndMsgOrBuilder getEndOrBuilder() {
      if (endBuilder_ != null) {
        return endBuilder_.getMessageOrBuilder();
      } else {
        return end_ == null ?
            xddq.pb.ExploreTravelEndMsg.getDefaultInstance() : end_;
      }
    }
    /**
     * <code>optional .xddq.pb.ExploreTravelEndMsg end = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.ExploreTravelEndMsg, xddq.pb.ExploreTravelEndMsg.Builder, xddq.pb.ExploreTravelEndMsgOrBuilder> 
        internalGetEndFieldBuilder() {
      if (endBuilder_ == null) {
        endBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.ExploreTravelEndMsg, xddq.pb.ExploreTravelEndMsg.Builder, xddq.pb.ExploreTravelEndMsgOrBuilder>(
                getEnd(),
                getParentForChildren(),
                isClean());
        end_ = null;
      }
      return endBuilder_;
    }

    private java.util.List<xddq.pb.AwardInfo> todayReward_ =
      java.util.Collections.emptyList();
    private void ensureTodayRewardIsMutable() {
      if (!((bitField0_ & 0x00000008) != 0)) {
        todayReward_ = new java.util.ArrayList<xddq.pb.AwardInfo>(todayReward_);
        bitField0_ |= 0x00000008;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.AwardInfo, xddq.pb.AwardInfo.Builder, xddq.pb.AwardInfoOrBuilder> todayRewardBuilder_;

    /**
     * <code>repeated .xddq.pb.AwardInfo todayReward = 4;</code>
     */
    public java.util.List<xddq.pb.AwardInfo> getTodayRewardList() {
      if (todayRewardBuilder_ == null) {
        return java.util.Collections.unmodifiableList(todayReward_);
      } else {
        return todayRewardBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.AwardInfo todayReward = 4;</code>
     */
    public int getTodayRewardCount() {
      if (todayRewardBuilder_ == null) {
        return todayReward_.size();
      } else {
        return todayRewardBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.AwardInfo todayReward = 4;</code>
     */
    public xddq.pb.AwardInfo getTodayReward(int index) {
      if (todayRewardBuilder_ == null) {
        return todayReward_.get(index);
      } else {
        return todayRewardBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.AwardInfo todayReward = 4;</code>
     */
    public Builder setTodayReward(
        int index, xddq.pb.AwardInfo value) {
      if (todayRewardBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureTodayRewardIsMutable();
        todayReward_.set(index, value);
        onChanged();
      } else {
        todayRewardBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.AwardInfo todayReward = 4;</code>
     */
    public Builder setTodayReward(
        int index, xddq.pb.AwardInfo.Builder builderForValue) {
      if (todayRewardBuilder_ == null) {
        ensureTodayRewardIsMutable();
        todayReward_.set(index, builderForValue.build());
        onChanged();
      } else {
        todayRewardBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.AwardInfo todayReward = 4;</code>
     */
    public Builder addTodayReward(xddq.pb.AwardInfo value) {
      if (todayRewardBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureTodayRewardIsMutable();
        todayReward_.add(value);
        onChanged();
      } else {
        todayRewardBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.AwardInfo todayReward = 4;</code>
     */
    public Builder addTodayReward(
        int index, xddq.pb.AwardInfo value) {
      if (todayRewardBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureTodayRewardIsMutable();
        todayReward_.add(index, value);
        onChanged();
      } else {
        todayRewardBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.AwardInfo todayReward = 4;</code>
     */
    public Builder addTodayReward(
        xddq.pb.AwardInfo.Builder builderForValue) {
      if (todayRewardBuilder_ == null) {
        ensureTodayRewardIsMutable();
        todayReward_.add(builderForValue.build());
        onChanged();
      } else {
        todayRewardBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.AwardInfo todayReward = 4;</code>
     */
    public Builder addTodayReward(
        int index, xddq.pb.AwardInfo.Builder builderForValue) {
      if (todayRewardBuilder_ == null) {
        ensureTodayRewardIsMutable();
        todayReward_.add(index, builderForValue.build());
        onChanged();
      } else {
        todayRewardBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.AwardInfo todayReward = 4;</code>
     */
    public Builder addAllTodayReward(
        java.lang.Iterable<? extends xddq.pb.AwardInfo> values) {
      if (todayRewardBuilder_ == null) {
        ensureTodayRewardIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, todayReward_);
        onChanged();
      } else {
        todayRewardBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.AwardInfo todayReward = 4;</code>
     */
    public Builder clearTodayReward() {
      if (todayRewardBuilder_ == null) {
        todayReward_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000008);
        onChanged();
      } else {
        todayRewardBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.AwardInfo todayReward = 4;</code>
     */
    public Builder removeTodayReward(int index) {
      if (todayRewardBuilder_ == null) {
        ensureTodayRewardIsMutable();
        todayReward_.remove(index);
        onChanged();
      } else {
        todayRewardBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.AwardInfo todayReward = 4;</code>
     */
    public xddq.pb.AwardInfo.Builder getTodayRewardBuilder(
        int index) {
      return internalGetTodayRewardFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.AwardInfo todayReward = 4;</code>
     */
    public xddq.pb.AwardInfoOrBuilder getTodayRewardOrBuilder(
        int index) {
      if (todayRewardBuilder_ == null) {
        return todayReward_.get(index);  } else {
        return todayRewardBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.AwardInfo todayReward = 4;</code>
     */
    public java.util.List<? extends xddq.pb.AwardInfoOrBuilder> 
         getTodayRewardOrBuilderList() {
      if (todayRewardBuilder_ != null) {
        return todayRewardBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(todayReward_);
      }
    }
    /**
     * <code>repeated .xddq.pb.AwardInfo todayReward = 4;</code>
     */
    public xddq.pb.AwardInfo.Builder addTodayRewardBuilder() {
      return internalGetTodayRewardFieldBuilder().addBuilder(
          xddq.pb.AwardInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.AwardInfo todayReward = 4;</code>
     */
    public xddq.pb.AwardInfo.Builder addTodayRewardBuilder(
        int index) {
      return internalGetTodayRewardFieldBuilder().addBuilder(
          index, xddq.pb.AwardInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.AwardInfo todayReward = 4;</code>
     */
    public java.util.List<xddq.pb.AwardInfo.Builder> 
         getTodayRewardBuilderList() {
      return internalGetTodayRewardFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.AwardInfo, xddq.pb.AwardInfo.Builder, xddq.pb.AwardInfoOrBuilder> 
        internalGetTodayRewardFieldBuilder() {
      if (todayRewardBuilder_ == null) {
        todayRewardBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.AwardInfo, xddq.pb.AwardInfo.Builder, xddq.pb.AwardInfoOrBuilder>(
                todayReward_,
                ((bitField0_ & 0x00000008) != 0),
                getParentForChildren(),
                isClean());
        todayReward_ = null;
      }
      return todayRewardBuilder_;
    }

    private com.google.protobuf.Internal.IntList params_ = emptyIntList();
    private void ensureParamsIsMutable() {
      if (!params_.isModifiable()) {
        params_ = makeMutableCopy(params_);
      }
      bitField0_ |= 0x00000010;
    }
    /**
     * <code>repeated int32 params = 5;</code>
     * @return A list containing the params.
     */
    public java.util.List<java.lang.Integer>
        getParamsList() {
      params_.makeImmutable();
      return params_;
    }
    /**
     * <code>repeated int32 params = 5;</code>
     * @return The count of params.
     */
    public int getParamsCount() {
      return params_.size();
    }
    /**
     * <code>repeated int32 params = 5;</code>
     * @param index The index of the element to return.
     * @return The params at the given index.
     */
    public int getParams(int index) {
      return params_.getInt(index);
    }
    /**
     * <code>repeated int32 params = 5;</code>
     * @param index The index to set the value at.
     * @param value The params to set.
     * @return This builder for chaining.
     */
    public Builder setParams(
        int index, int value) {

      ensureParamsIsMutable();
      params_.setInt(index, value);
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 params = 5;</code>
     * @param value The params to add.
     * @return This builder for chaining.
     */
    public Builder addParams(int value) {

      ensureParamsIsMutable();
      params_.addInt(value);
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 params = 5;</code>
     * @param values The params to add.
     * @return This builder for chaining.
     */
    public Builder addAllParams(
        java.lang.Iterable<? extends java.lang.Integer> values) {
      ensureParamsIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, params_);
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 params = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearParams() {
      params_ = emptyIntList();
      bitField0_ = (bitField0_ & ~0x00000010);
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.ExploreTravelPassTodayResp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.ExploreTravelPassTodayResp)
  private static final xddq.pb.ExploreTravelPassTodayResp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.ExploreTravelPassTodayResp();
  }

  public static xddq.pb.ExploreTravelPassTodayResp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ExploreTravelPassTodayResp>
      PARSER = new com.google.protobuf.AbstractParser<ExploreTravelPassTodayResp>() {
    @java.lang.Override
    public ExploreTravelPassTodayResp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<ExploreTravelPassTodayResp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ExploreTravelPassTodayResp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.ExploreTravelPassTodayResp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

