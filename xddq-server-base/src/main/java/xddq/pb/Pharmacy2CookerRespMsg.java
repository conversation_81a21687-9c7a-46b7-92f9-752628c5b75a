// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.Pharmacy2CookerRespMsg}
 */
public final class Pharmacy2CookerRespMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.Pharmacy2CookerRespMsg)
    Pharmacy2CookerRespMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      Pharmacy2CookerRespMsg.class.getName());
  }
  // Use Pharmacy2CookerRespMsg.newBuilder() to construct.
  private Pharmacy2CookerRespMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private Pharmacy2CookerRespMsg() {
    cookerList_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_Pharmacy2CookerRespMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_Pharmacy2CookerRespMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.Pharmacy2CookerRespMsg.class, xddq.pb.Pharmacy2CookerRespMsg.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int COOKERLIST_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.Pharmacy2CookerTempMsg> cookerList_;
  /**
   * <code>repeated .xddq.pb.Pharmacy2CookerTempMsg cookerList = 2;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.Pharmacy2CookerTempMsg> getCookerListList() {
    return cookerList_;
  }
  /**
   * <code>repeated .xddq.pb.Pharmacy2CookerTempMsg cookerList = 2;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.Pharmacy2CookerTempMsgOrBuilder> 
      getCookerListOrBuilderList() {
    return cookerList_;
  }
  /**
   * <code>repeated .xddq.pb.Pharmacy2CookerTempMsg cookerList = 2;</code>
   */
  @java.lang.Override
  public int getCookerListCount() {
    return cookerList_.size();
  }
  /**
   * <code>repeated .xddq.pb.Pharmacy2CookerTempMsg cookerList = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.Pharmacy2CookerTempMsg getCookerList(int index) {
    return cookerList_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.Pharmacy2CookerTempMsg cookerList = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.Pharmacy2CookerTempMsgOrBuilder getCookerListOrBuilder(
      int index) {
    return cookerList_.get(index);
  }

  public static final int ACTIVITYID_FIELD_NUMBER = 3;
  private int activityId_ = 0;
  /**
   * <code>optional int32 activityId = 3;</code>
   * @return Whether the activityId field is set.
   */
  @java.lang.Override
  public boolean hasActivityId() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int32 activityId = 3;</code>
   * @return The activityId.
   */
  @java.lang.Override
  public int getActivityId() {
    return activityId_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    for (int i = 0; i < getCookerListCount(); i++) {
      if (!getCookerList(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    for (int i = 0; i < cookerList_.size(); i++) {
      output.writeMessage(2, cookerList_.get(i));
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(3, activityId_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    for (int i = 0; i < cookerList_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, cookerList_.get(i));
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(3, activityId_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.Pharmacy2CookerRespMsg)) {
      return super.equals(obj);
    }
    xddq.pb.Pharmacy2CookerRespMsg other = (xddq.pb.Pharmacy2CookerRespMsg) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (!getCookerListList()
        .equals(other.getCookerListList())) return false;
    if (hasActivityId() != other.hasActivityId()) return false;
    if (hasActivityId()) {
      if (getActivityId()
          != other.getActivityId()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (getCookerListCount() > 0) {
      hash = (37 * hash) + COOKERLIST_FIELD_NUMBER;
      hash = (53 * hash) + getCookerListList().hashCode();
    }
    if (hasActivityId()) {
      hash = (37 * hash) + ACTIVITYID_FIELD_NUMBER;
      hash = (53 * hash) + getActivityId();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.Pharmacy2CookerRespMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.Pharmacy2CookerRespMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.Pharmacy2CookerRespMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.Pharmacy2CookerRespMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.Pharmacy2CookerRespMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.Pharmacy2CookerRespMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.Pharmacy2CookerRespMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.Pharmacy2CookerRespMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.Pharmacy2CookerRespMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.Pharmacy2CookerRespMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.Pharmacy2CookerRespMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.Pharmacy2CookerRespMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.Pharmacy2CookerRespMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.Pharmacy2CookerRespMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.Pharmacy2CookerRespMsg)
      xddq.pb.Pharmacy2CookerRespMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_Pharmacy2CookerRespMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_Pharmacy2CookerRespMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.Pharmacy2CookerRespMsg.class, xddq.pb.Pharmacy2CookerRespMsg.Builder.class);
    }

    // Construct using xddq.pb.Pharmacy2CookerRespMsg.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      if (cookerListBuilder_ == null) {
        cookerList_ = java.util.Collections.emptyList();
      } else {
        cookerList_ = null;
        cookerListBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000002);
      activityId_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_Pharmacy2CookerRespMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.Pharmacy2CookerRespMsg getDefaultInstanceForType() {
      return xddq.pb.Pharmacy2CookerRespMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.Pharmacy2CookerRespMsg build() {
      xddq.pb.Pharmacy2CookerRespMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.Pharmacy2CookerRespMsg buildPartial() {
      xddq.pb.Pharmacy2CookerRespMsg result = new xddq.pb.Pharmacy2CookerRespMsg(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.Pharmacy2CookerRespMsg result) {
      if (cookerListBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          cookerList_ = java.util.Collections.unmodifiableList(cookerList_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.cookerList_ = cookerList_;
      } else {
        result.cookerList_ = cookerListBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.Pharmacy2CookerRespMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.activityId_ = activityId_;
        to_bitField0_ |= 0x00000002;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.Pharmacy2CookerRespMsg) {
        return mergeFrom((xddq.pb.Pharmacy2CookerRespMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.Pharmacy2CookerRespMsg other) {
      if (other == xddq.pb.Pharmacy2CookerRespMsg.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (cookerListBuilder_ == null) {
        if (!other.cookerList_.isEmpty()) {
          if (cookerList_.isEmpty()) {
            cookerList_ = other.cookerList_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureCookerListIsMutable();
            cookerList_.addAll(other.cookerList_);
          }
          onChanged();
        }
      } else {
        if (!other.cookerList_.isEmpty()) {
          if (cookerListBuilder_.isEmpty()) {
            cookerListBuilder_.dispose();
            cookerListBuilder_ = null;
            cookerList_ = other.cookerList_;
            bitField0_ = (bitField0_ & ~0x00000002);
            cookerListBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetCookerListFieldBuilder() : null;
          } else {
            cookerListBuilder_.addAllMessages(other.cookerList_);
          }
        }
      }
      if (other.hasActivityId()) {
        setActivityId(other.getActivityId());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      for (int i = 0; i < getCookerListCount(); i++) {
        if (!getCookerList(i).isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              xddq.pb.Pharmacy2CookerTempMsg m =
                  input.readMessage(
                      xddq.pb.Pharmacy2CookerTempMsg.parser(),
                      extensionRegistry);
              if (cookerListBuilder_ == null) {
                ensureCookerListIsMutable();
                cookerList_.add(m);
              } else {
                cookerListBuilder_.addMessage(m);
              }
              break;
            } // case 18
            case 24: {
              activityId_ = input.readInt32();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.Pharmacy2CookerTempMsg> cookerList_ =
      java.util.Collections.emptyList();
    private void ensureCookerListIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        cookerList_ = new java.util.ArrayList<xddq.pb.Pharmacy2CookerTempMsg>(cookerList_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.Pharmacy2CookerTempMsg, xddq.pb.Pharmacy2CookerTempMsg.Builder, xddq.pb.Pharmacy2CookerTempMsgOrBuilder> cookerListBuilder_;

    /**
     * <code>repeated .xddq.pb.Pharmacy2CookerTempMsg cookerList = 2;</code>
     */
    public java.util.List<xddq.pb.Pharmacy2CookerTempMsg> getCookerListList() {
      if (cookerListBuilder_ == null) {
        return java.util.Collections.unmodifiableList(cookerList_);
      } else {
        return cookerListBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.Pharmacy2CookerTempMsg cookerList = 2;</code>
     */
    public int getCookerListCount() {
      if (cookerListBuilder_ == null) {
        return cookerList_.size();
      } else {
        return cookerListBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.Pharmacy2CookerTempMsg cookerList = 2;</code>
     */
    public xddq.pb.Pharmacy2CookerTempMsg getCookerList(int index) {
      if (cookerListBuilder_ == null) {
        return cookerList_.get(index);
      } else {
        return cookerListBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.Pharmacy2CookerTempMsg cookerList = 2;</code>
     */
    public Builder setCookerList(
        int index, xddq.pb.Pharmacy2CookerTempMsg value) {
      if (cookerListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureCookerListIsMutable();
        cookerList_.set(index, value);
        onChanged();
      } else {
        cookerListBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.Pharmacy2CookerTempMsg cookerList = 2;</code>
     */
    public Builder setCookerList(
        int index, xddq.pb.Pharmacy2CookerTempMsg.Builder builderForValue) {
      if (cookerListBuilder_ == null) {
        ensureCookerListIsMutable();
        cookerList_.set(index, builderForValue.build());
        onChanged();
      } else {
        cookerListBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.Pharmacy2CookerTempMsg cookerList = 2;</code>
     */
    public Builder addCookerList(xddq.pb.Pharmacy2CookerTempMsg value) {
      if (cookerListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureCookerListIsMutable();
        cookerList_.add(value);
        onChanged();
      } else {
        cookerListBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.Pharmacy2CookerTempMsg cookerList = 2;</code>
     */
    public Builder addCookerList(
        int index, xddq.pb.Pharmacy2CookerTempMsg value) {
      if (cookerListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureCookerListIsMutable();
        cookerList_.add(index, value);
        onChanged();
      } else {
        cookerListBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.Pharmacy2CookerTempMsg cookerList = 2;</code>
     */
    public Builder addCookerList(
        xddq.pb.Pharmacy2CookerTempMsg.Builder builderForValue) {
      if (cookerListBuilder_ == null) {
        ensureCookerListIsMutable();
        cookerList_.add(builderForValue.build());
        onChanged();
      } else {
        cookerListBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.Pharmacy2CookerTempMsg cookerList = 2;</code>
     */
    public Builder addCookerList(
        int index, xddq.pb.Pharmacy2CookerTempMsg.Builder builderForValue) {
      if (cookerListBuilder_ == null) {
        ensureCookerListIsMutable();
        cookerList_.add(index, builderForValue.build());
        onChanged();
      } else {
        cookerListBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.Pharmacy2CookerTempMsg cookerList = 2;</code>
     */
    public Builder addAllCookerList(
        java.lang.Iterable<? extends xddq.pb.Pharmacy2CookerTempMsg> values) {
      if (cookerListBuilder_ == null) {
        ensureCookerListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, cookerList_);
        onChanged();
      } else {
        cookerListBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.Pharmacy2CookerTempMsg cookerList = 2;</code>
     */
    public Builder clearCookerList() {
      if (cookerListBuilder_ == null) {
        cookerList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        cookerListBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.Pharmacy2CookerTempMsg cookerList = 2;</code>
     */
    public Builder removeCookerList(int index) {
      if (cookerListBuilder_ == null) {
        ensureCookerListIsMutable();
        cookerList_.remove(index);
        onChanged();
      } else {
        cookerListBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.Pharmacy2CookerTempMsg cookerList = 2;</code>
     */
    public xddq.pb.Pharmacy2CookerTempMsg.Builder getCookerListBuilder(
        int index) {
      return internalGetCookerListFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.Pharmacy2CookerTempMsg cookerList = 2;</code>
     */
    public xddq.pb.Pharmacy2CookerTempMsgOrBuilder getCookerListOrBuilder(
        int index) {
      if (cookerListBuilder_ == null) {
        return cookerList_.get(index);  } else {
        return cookerListBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.Pharmacy2CookerTempMsg cookerList = 2;</code>
     */
    public java.util.List<? extends xddq.pb.Pharmacy2CookerTempMsgOrBuilder> 
         getCookerListOrBuilderList() {
      if (cookerListBuilder_ != null) {
        return cookerListBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(cookerList_);
      }
    }
    /**
     * <code>repeated .xddq.pb.Pharmacy2CookerTempMsg cookerList = 2;</code>
     */
    public xddq.pb.Pharmacy2CookerTempMsg.Builder addCookerListBuilder() {
      return internalGetCookerListFieldBuilder().addBuilder(
          xddq.pb.Pharmacy2CookerTempMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.Pharmacy2CookerTempMsg cookerList = 2;</code>
     */
    public xddq.pb.Pharmacy2CookerTempMsg.Builder addCookerListBuilder(
        int index) {
      return internalGetCookerListFieldBuilder().addBuilder(
          index, xddq.pb.Pharmacy2CookerTempMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.Pharmacy2CookerTempMsg cookerList = 2;</code>
     */
    public java.util.List<xddq.pb.Pharmacy2CookerTempMsg.Builder> 
         getCookerListBuilderList() {
      return internalGetCookerListFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.Pharmacy2CookerTempMsg, xddq.pb.Pharmacy2CookerTempMsg.Builder, xddq.pb.Pharmacy2CookerTempMsgOrBuilder> 
        internalGetCookerListFieldBuilder() {
      if (cookerListBuilder_ == null) {
        cookerListBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.Pharmacy2CookerTempMsg, xddq.pb.Pharmacy2CookerTempMsg.Builder, xddq.pb.Pharmacy2CookerTempMsgOrBuilder>(
                cookerList_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        cookerList_ = null;
      }
      return cookerListBuilder_;
    }

    private int activityId_ ;
    /**
     * <code>optional int32 activityId = 3;</code>
     * @return Whether the activityId field is set.
     */
    @java.lang.Override
    public boolean hasActivityId() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int32 activityId = 3;</code>
     * @return The activityId.
     */
    @java.lang.Override
    public int getActivityId() {
      return activityId_;
    }
    /**
     * <code>optional int32 activityId = 3;</code>
     * @param value The activityId to set.
     * @return This builder for chaining.
     */
    public Builder setActivityId(int value) {

      activityId_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 activityId = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearActivityId() {
      bitField0_ = (bitField0_ & ~0x00000004);
      activityId_ = 0;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.Pharmacy2CookerRespMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.Pharmacy2CookerRespMsg)
  private static final xddq.pb.Pharmacy2CookerRespMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.Pharmacy2CookerRespMsg();
  }

  public static xddq.pb.Pharmacy2CookerRespMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<Pharmacy2CookerRespMsg>
      PARSER = new com.google.protobuf.AbstractParser<Pharmacy2CookerRespMsg>() {
    @java.lang.Override
    public Pharmacy2CookerRespMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<Pharmacy2CookerRespMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<Pharmacy2CookerRespMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.Pharmacy2CookerRespMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

