// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.PlanesTrialChangePosNotify}
 */
public final class PlanesTrialChangePosNotify extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.PlanesTrialChangePosNotify)
    PlanesTrialChangePosNotifyOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      PlanesTrialChangePosNotify.class.getName());
  }
  // Use PlanesTrialChangePosNotify.newBuilder() to construct.
  private PlanesTrialChangePosNotify(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private PlanesTrialChangePosNotify() {
    datas_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_PlanesTrialChangePosNotify_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_PlanesTrialChangePosNotify_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.PlanesTrialChangePosNotify.class, xddq.pb.PlanesTrialChangePosNotify.Builder.class);
  }

  public static final int DATAS_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.PlanesTrialChangePosData> datas_;
  /**
   * <code>repeated .xddq.pb.PlanesTrialChangePosData datas = 1;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.PlanesTrialChangePosData> getDatasList() {
    return datas_;
  }
  /**
   * <code>repeated .xddq.pb.PlanesTrialChangePosData datas = 1;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.PlanesTrialChangePosDataOrBuilder> 
      getDatasOrBuilderList() {
    return datas_;
  }
  /**
   * <code>repeated .xddq.pb.PlanesTrialChangePosData datas = 1;</code>
   */
  @java.lang.Override
  public int getDatasCount() {
    return datas_.size();
  }
  /**
   * <code>repeated .xddq.pb.PlanesTrialChangePosData datas = 1;</code>
   */
  @java.lang.Override
  public xddq.pb.PlanesTrialChangePosData getDatas(int index) {
    return datas_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.PlanesTrialChangePosData datas = 1;</code>
   */
  @java.lang.Override
  public xddq.pb.PlanesTrialChangePosDataOrBuilder getDatasOrBuilder(
      int index) {
    return datas_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    for (int i = 0; i < getDatasCount(); i++) {
      if (!getDatas(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    for (int i = 0; i < datas_.size(); i++) {
      output.writeMessage(1, datas_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    for (int i = 0; i < datas_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, datas_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.PlanesTrialChangePosNotify)) {
      return super.equals(obj);
    }
    xddq.pb.PlanesTrialChangePosNotify other = (xddq.pb.PlanesTrialChangePosNotify) obj;

    if (!getDatasList()
        .equals(other.getDatasList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (getDatasCount() > 0) {
      hash = (37 * hash) + DATAS_FIELD_NUMBER;
      hash = (53 * hash) + getDatasList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.PlanesTrialChangePosNotify parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PlanesTrialChangePosNotify parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PlanesTrialChangePosNotify parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PlanesTrialChangePosNotify parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PlanesTrialChangePosNotify parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PlanesTrialChangePosNotify parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PlanesTrialChangePosNotify parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.PlanesTrialChangePosNotify parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.PlanesTrialChangePosNotify parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.PlanesTrialChangePosNotify parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.PlanesTrialChangePosNotify parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.PlanesTrialChangePosNotify parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.PlanesTrialChangePosNotify prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.PlanesTrialChangePosNotify}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.PlanesTrialChangePosNotify)
      xddq.pb.PlanesTrialChangePosNotifyOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PlanesTrialChangePosNotify_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PlanesTrialChangePosNotify_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.PlanesTrialChangePosNotify.class, xddq.pb.PlanesTrialChangePosNotify.Builder.class);
    }

    // Construct using xddq.pb.PlanesTrialChangePosNotify.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      if (datasBuilder_ == null) {
        datas_ = java.util.Collections.emptyList();
      } else {
        datas_ = null;
        datasBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000001);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PlanesTrialChangePosNotify_descriptor;
    }

    @java.lang.Override
    public xddq.pb.PlanesTrialChangePosNotify getDefaultInstanceForType() {
      return xddq.pb.PlanesTrialChangePosNotify.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.PlanesTrialChangePosNotify build() {
      xddq.pb.PlanesTrialChangePosNotify result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.PlanesTrialChangePosNotify buildPartial() {
      xddq.pb.PlanesTrialChangePosNotify result = new xddq.pb.PlanesTrialChangePosNotify(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.PlanesTrialChangePosNotify result) {
      if (datasBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          datas_ = java.util.Collections.unmodifiableList(datas_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.datas_ = datas_;
      } else {
        result.datas_ = datasBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.PlanesTrialChangePosNotify result) {
      int from_bitField0_ = bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.PlanesTrialChangePosNotify) {
        return mergeFrom((xddq.pb.PlanesTrialChangePosNotify)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.PlanesTrialChangePosNotify other) {
      if (other == xddq.pb.PlanesTrialChangePosNotify.getDefaultInstance()) return this;
      if (datasBuilder_ == null) {
        if (!other.datas_.isEmpty()) {
          if (datas_.isEmpty()) {
            datas_ = other.datas_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureDatasIsMutable();
            datas_.addAll(other.datas_);
          }
          onChanged();
        }
      } else {
        if (!other.datas_.isEmpty()) {
          if (datasBuilder_.isEmpty()) {
            datasBuilder_.dispose();
            datasBuilder_ = null;
            datas_ = other.datas_;
            bitField0_ = (bitField0_ & ~0x00000001);
            datasBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetDatasFieldBuilder() : null;
          } else {
            datasBuilder_.addAllMessages(other.datas_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      for (int i = 0; i < getDatasCount(); i++) {
        if (!getDatas(i).isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              xddq.pb.PlanesTrialChangePosData m =
                  input.readMessage(
                      xddq.pb.PlanesTrialChangePosData.parser(),
                      extensionRegistry);
              if (datasBuilder_ == null) {
                ensureDatasIsMutable();
                datas_.add(m);
              } else {
                datasBuilder_.addMessage(m);
              }
              break;
            } // case 10
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.util.List<xddq.pb.PlanesTrialChangePosData> datas_ =
      java.util.Collections.emptyList();
    private void ensureDatasIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        datas_ = new java.util.ArrayList<xddq.pb.PlanesTrialChangePosData>(datas_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.PlanesTrialChangePosData, xddq.pb.PlanesTrialChangePosData.Builder, xddq.pb.PlanesTrialChangePosDataOrBuilder> datasBuilder_;

    /**
     * <code>repeated .xddq.pb.PlanesTrialChangePosData datas = 1;</code>
     */
    public java.util.List<xddq.pb.PlanesTrialChangePosData> getDatasList() {
      if (datasBuilder_ == null) {
        return java.util.Collections.unmodifiableList(datas_);
      } else {
        return datasBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.PlanesTrialChangePosData datas = 1;</code>
     */
    public int getDatasCount() {
      if (datasBuilder_ == null) {
        return datas_.size();
      } else {
        return datasBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.PlanesTrialChangePosData datas = 1;</code>
     */
    public xddq.pb.PlanesTrialChangePosData getDatas(int index) {
      if (datasBuilder_ == null) {
        return datas_.get(index);
      } else {
        return datasBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.PlanesTrialChangePosData datas = 1;</code>
     */
    public Builder setDatas(
        int index, xddq.pb.PlanesTrialChangePosData value) {
      if (datasBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDatasIsMutable();
        datas_.set(index, value);
        onChanged();
      } else {
        datasBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PlanesTrialChangePosData datas = 1;</code>
     */
    public Builder setDatas(
        int index, xddq.pb.PlanesTrialChangePosData.Builder builderForValue) {
      if (datasBuilder_ == null) {
        ensureDatasIsMutable();
        datas_.set(index, builderForValue.build());
        onChanged();
      } else {
        datasBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PlanesTrialChangePosData datas = 1;</code>
     */
    public Builder addDatas(xddq.pb.PlanesTrialChangePosData value) {
      if (datasBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDatasIsMutable();
        datas_.add(value);
        onChanged();
      } else {
        datasBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PlanesTrialChangePosData datas = 1;</code>
     */
    public Builder addDatas(
        int index, xddq.pb.PlanesTrialChangePosData value) {
      if (datasBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDatasIsMutable();
        datas_.add(index, value);
        onChanged();
      } else {
        datasBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PlanesTrialChangePosData datas = 1;</code>
     */
    public Builder addDatas(
        xddq.pb.PlanesTrialChangePosData.Builder builderForValue) {
      if (datasBuilder_ == null) {
        ensureDatasIsMutable();
        datas_.add(builderForValue.build());
        onChanged();
      } else {
        datasBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PlanesTrialChangePosData datas = 1;</code>
     */
    public Builder addDatas(
        int index, xddq.pb.PlanesTrialChangePosData.Builder builderForValue) {
      if (datasBuilder_ == null) {
        ensureDatasIsMutable();
        datas_.add(index, builderForValue.build());
        onChanged();
      } else {
        datasBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PlanesTrialChangePosData datas = 1;</code>
     */
    public Builder addAllDatas(
        java.lang.Iterable<? extends xddq.pb.PlanesTrialChangePosData> values) {
      if (datasBuilder_ == null) {
        ensureDatasIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, datas_);
        onChanged();
      } else {
        datasBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PlanesTrialChangePosData datas = 1;</code>
     */
    public Builder clearDatas() {
      if (datasBuilder_ == null) {
        datas_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        datasBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PlanesTrialChangePosData datas = 1;</code>
     */
    public Builder removeDatas(int index) {
      if (datasBuilder_ == null) {
        ensureDatasIsMutable();
        datas_.remove(index);
        onChanged();
      } else {
        datasBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PlanesTrialChangePosData datas = 1;</code>
     */
    public xddq.pb.PlanesTrialChangePosData.Builder getDatasBuilder(
        int index) {
      return internalGetDatasFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.PlanesTrialChangePosData datas = 1;</code>
     */
    public xddq.pb.PlanesTrialChangePosDataOrBuilder getDatasOrBuilder(
        int index) {
      if (datasBuilder_ == null) {
        return datas_.get(index);  } else {
        return datasBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.PlanesTrialChangePosData datas = 1;</code>
     */
    public java.util.List<? extends xddq.pb.PlanesTrialChangePosDataOrBuilder> 
         getDatasOrBuilderList() {
      if (datasBuilder_ != null) {
        return datasBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(datas_);
      }
    }
    /**
     * <code>repeated .xddq.pb.PlanesTrialChangePosData datas = 1;</code>
     */
    public xddq.pb.PlanesTrialChangePosData.Builder addDatasBuilder() {
      return internalGetDatasFieldBuilder().addBuilder(
          xddq.pb.PlanesTrialChangePosData.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.PlanesTrialChangePosData datas = 1;</code>
     */
    public xddq.pb.PlanesTrialChangePosData.Builder addDatasBuilder(
        int index) {
      return internalGetDatasFieldBuilder().addBuilder(
          index, xddq.pb.PlanesTrialChangePosData.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.PlanesTrialChangePosData datas = 1;</code>
     */
    public java.util.List<xddq.pb.PlanesTrialChangePosData.Builder> 
         getDatasBuilderList() {
      return internalGetDatasFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.PlanesTrialChangePosData, xddq.pb.PlanesTrialChangePosData.Builder, xddq.pb.PlanesTrialChangePosDataOrBuilder> 
        internalGetDatasFieldBuilder() {
      if (datasBuilder_ == null) {
        datasBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.PlanesTrialChangePosData, xddq.pb.PlanesTrialChangePosData.Builder, xddq.pb.PlanesTrialChangePosDataOrBuilder>(
                datas_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        datas_ = null;
      }
      return datasBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.PlanesTrialChangePosNotify)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.PlanesTrialChangePosNotify)
  private static final xddq.pb.PlanesTrialChangePosNotify DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.PlanesTrialChangePosNotify();
  }

  public static xddq.pb.PlanesTrialChangePosNotify getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<PlanesTrialChangePosNotify>
      PARSER = new com.google.protobuf.AbstractParser<PlanesTrialChangePosNotify>() {
    @java.lang.Override
    public PlanesTrialChangePosNotify parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<PlanesTrialChangePosNotify> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<PlanesTrialChangePosNotify> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.PlanesTrialChangePosNotify getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

