// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.ManHuangUserDataEntity}
 */
public final class ManHuangUserDataEntity extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.ManHuangUserDataEntity)
    ManHuangUserDataEntityOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      ManHuangUserDataEntity.class.getName());
  }
  // Use ManHuangUserDataEntity.newBuilder() to construct.
  private ManHuangUserDataEntity(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private ManHuangUserDataEntity() {
    fightValue_ = "";
    unlockRegionId_ = emptyIntList();
    mallInfo_ = java.util.Collections.emptyList();
    applyJoinTeamIdList_ = emptyLongList();
    totalBlood_ = "";
    blood_ = "";
    enemyNotify_ = java.util.Collections.emptyList();
    monsterBall_ = java.util.Collections.emptyList();
    grass_ = java.util.Collections.emptyList();
    box_ = java.util.Collections.emptyList();
    eventDone_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_ManHuangUserDataEntity_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_ManHuangUserDataEntity_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.ManHuangUserDataEntity.class, xddq.pb.ManHuangUserDataEntity.Builder.class);
  }

  private int bitField0_;
  public static final int USERID_FIELD_NUMBER = 1;
  private long userId_ = 0L;
  /**
   * <code>optional int64 userId = 1;</code>
   * @return Whether the userId field is set.
   */
  @java.lang.Override
  public boolean hasUserId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int64 userId = 1;</code>
   * @return The userId.
   */
  @java.lang.Override
  public long getUserId() {
    return userId_;
  }

  public static final int TEAMID_FIELD_NUMBER = 2;
  private long teamId_ = 0L;
  /**
   * <code>optional int64 teamId = 2;</code>
   * @return Whether the teamId field is set.
   */
  @java.lang.Override
  public boolean hasTeamId() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int64 teamId = 2;</code>
   * @return The teamId.
   */
  @java.lang.Override
  public long getTeamId() {
    return teamId_;
  }

  public static final int PLAYERINFO_FIELD_NUMBER = 3;
  private xddq.pb.PlayerCharacterImageMsg playerInfo_;
  /**
   * <code>optional .xddq.pb.PlayerCharacterImageMsg playerInfo = 3;</code>
   * @return Whether the playerInfo field is set.
   */
  @java.lang.Override
  public boolean hasPlayerInfo() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional .xddq.pb.PlayerCharacterImageMsg playerInfo = 3;</code>
   * @return The playerInfo.
   */
  @java.lang.Override
  public xddq.pb.PlayerCharacterImageMsg getPlayerInfo() {
    return playerInfo_ == null ? xddq.pb.PlayerCharacterImageMsg.getDefaultInstance() : playerInfo_;
  }
  /**
   * <code>optional .xddq.pb.PlayerCharacterImageMsg playerInfo = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.PlayerCharacterImageMsgOrBuilder getPlayerInfoOrBuilder() {
    return playerInfo_ == null ? xddq.pb.PlayerCharacterImageMsg.getDefaultInstance() : playerInfo_;
  }

  public static final int FIGHTVALUE_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private volatile java.lang.Object fightValue_ = "";
  /**
   * <code>optional string fightValue = 4;</code>
   * @return Whether the fightValue field is set.
   */
  @java.lang.Override
  public boolean hasFightValue() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional string fightValue = 4;</code>
   * @return The fightValue.
   */
  @java.lang.Override
  public java.lang.String getFightValue() {
    java.lang.Object ref = fightValue_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        fightValue_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string fightValue = 4;</code>
   * @return The bytes for fightValue.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getFightValueBytes() {
    java.lang.Object ref = fightValue_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      fightValue_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int REGIONID_FIELD_NUMBER = 5;
  private int regionId_ = 0;
  /**
   * <code>optional int32 regionId = 5;</code>
   * @return Whether the regionId field is set.
   */
  @java.lang.Override
  public boolean hasRegionId() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>optional int32 regionId = 5;</code>
   * @return The regionId.
   */
  @java.lang.Override
  public int getRegionId() {
    return regionId_;
  }

  public static final int UNLOCKREGIONID_FIELD_NUMBER = 6;
  @SuppressWarnings("serial")
  private com.google.protobuf.Internal.IntList unlockRegionId_ =
      emptyIntList();
  /**
   * <code>repeated int32 unlockRegionId = 6;</code>
   * @return A list containing the unlockRegionId.
   */
  @java.lang.Override
  public java.util.List<java.lang.Integer>
      getUnlockRegionIdList() {
    return unlockRegionId_;
  }
  /**
   * <code>repeated int32 unlockRegionId = 6;</code>
   * @return The count of unlockRegionId.
   */
  public int getUnlockRegionIdCount() {
    return unlockRegionId_.size();
  }
  /**
   * <code>repeated int32 unlockRegionId = 6;</code>
   * @param index The index of the element to return.
   * @return The unlockRegionId at the given index.
   */
  public int getUnlockRegionId(int index) {
    return unlockRegionId_.getInt(index);
  }

  public static final int ENERGY_FIELD_NUMBER = 7;
  private int energy_ = 0;
  /**
   * <code>optional int32 energy = 7;</code>
   * @return Whether the energy field is set.
   */
  @java.lang.Override
  public boolean hasEnergy() {
    return ((bitField0_ & 0x00000020) != 0);
  }
  /**
   * <code>optional int32 energy = 7;</code>
   * @return The energy.
   */
  @java.lang.Override
  public int getEnergy() {
    return energy_;
  }

  public static final int INTEGRAL_FIELD_NUMBER = 8;
  private long integral_ = 0L;
  /**
   * <code>optional int64 integral = 8;</code>
   * @return Whether the integral field is set.
   */
  @java.lang.Override
  public boolean hasIntegral() {
    return ((bitField0_ & 0x00000040) != 0);
  }
  /**
   * <code>optional int64 integral = 8;</code>
   * @return The integral.
   */
  @java.lang.Override
  public long getIntegral() {
    return integral_;
  }

  public static final int RANK_FIELD_NUMBER = 9;
  private long rank_ = 0L;
  /**
   * <code>optional int64 rank = 9;</code>
   * @return Whether the rank field is set.
   */
  @java.lang.Override
  public boolean hasRank() {
    return ((bitField0_ & 0x00000080) != 0);
  }
  /**
   * <code>optional int64 rank = 9;</code>
   * @return The rank.
   */
  @java.lang.Override
  public long getRank() {
    return rank_;
  }

  public static final int TEAMINFO_FIELD_NUMBER = 10;
  private xddq.pb.ManHuangTeamEntity teamInfo_;
  /**
   * <code>optional .xddq.pb.ManHuangTeamEntity teamInfo = 10;</code>
   * @return Whether the teamInfo field is set.
   */
  @java.lang.Override
  public boolean hasTeamInfo() {
    return ((bitField0_ & 0x00000100) != 0);
  }
  /**
   * <code>optional .xddq.pb.ManHuangTeamEntity teamInfo = 10;</code>
   * @return The teamInfo.
   */
  @java.lang.Override
  public xddq.pb.ManHuangTeamEntity getTeamInfo() {
    return teamInfo_ == null ? xddq.pb.ManHuangTeamEntity.getDefaultInstance() : teamInfo_;
  }
  /**
   * <code>optional .xddq.pb.ManHuangTeamEntity teamInfo = 10;</code>
   */
  @java.lang.Override
  public xddq.pb.ManHuangTeamEntityOrBuilder getTeamInfoOrBuilder() {
    return teamInfo_ == null ? xddq.pb.ManHuangTeamEntity.getDefaultInstance() : teamInfo_;
  }

  public static final int MALLINFO_FIELD_NUMBER = 11;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.ManHuangUserMallInfo> mallInfo_;
  /**
   * <code>repeated .xddq.pb.ManHuangUserMallInfo mallInfo = 11;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.ManHuangUserMallInfo> getMallInfoList() {
    return mallInfo_;
  }
  /**
   * <code>repeated .xddq.pb.ManHuangUserMallInfo mallInfo = 11;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.ManHuangUserMallInfoOrBuilder> 
      getMallInfoOrBuilderList() {
    return mallInfo_;
  }
  /**
   * <code>repeated .xddq.pb.ManHuangUserMallInfo mallInfo = 11;</code>
   */
  @java.lang.Override
  public int getMallInfoCount() {
    return mallInfo_.size();
  }
  /**
   * <code>repeated .xddq.pb.ManHuangUserMallInfo mallInfo = 11;</code>
   */
  @java.lang.Override
  public xddq.pb.ManHuangUserMallInfo getMallInfo(int index) {
    return mallInfo_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.ManHuangUserMallInfo mallInfo = 11;</code>
   */
  @java.lang.Override
  public xddq.pb.ManHuangUserMallInfoOrBuilder getMallInfoOrBuilder(
      int index) {
    return mallInfo_.get(index);
  }

  public static final int LASTLEAVETEAM_FIELD_NUMBER = 12;
  private long lastLeaveTeam_ = 0L;
  /**
   * <code>optional int64 lastLeaveTeam = 12;</code>
   * @return Whether the lastLeaveTeam field is set.
   */
  @java.lang.Override
  public boolean hasLastLeaveTeam() {
    return ((bitField0_ & 0x00000200) != 0);
  }
  /**
   * <code>optional int64 lastLeaveTeam = 12;</code>
   * @return The lastLeaveTeam.
   */
  @java.lang.Override
  public long getLastLeaveTeam() {
    return lastLeaveTeam_;
  }

  public static final int LASTCREATETEAM_FIELD_NUMBER = 13;
  private long lastCreateTeam_ = 0L;
  /**
   * <code>optional int64 lastCreateTeam = 13;</code>
   * @return Whether the lastCreateTeam field is set.
   */
  @java.lang.Override
  public boolean hasLastCreateTeam() {
    return ((bitField0_ & 0x00000400) != 0);
  }
  /**
   * <code>optional int64 lastCreateTeam = 13;</code>
   * @return The lastCreateTeam.
   */
  @java.lang.Override
  public long getLastCreateTeam() {
    return lastCreateTeam_;
  }

  public static final int APPLYJOINTEAMIDLIST_FIELD_NUMBER = 14;
  @SuppressWarnings("serial")
  private com.google.protobuf.Internal.LongList applyJoinTeamIdList_ =
      emptyLongList();
  /**
   * <code>repeated int64 applyJoinTeamIdList = 14;</code>
   * @return A list containing the applyJoinTeamIdList.
   */
  @java.lang.Override
  public java.util.List<java.lang.Long>
      getApplyJoinTeamIdListList() {
    return applyJoinTeamIdList_;
  }
  /**
   * <code>repeated int64 applyJoinTeamIdList = 14;</code>
   * @return The count of applyJoinTeamIdList.
   */
  public int getApplyJoinTeamIdListCount() {
    return applyJoinTeamIdList_.size();
  }
  /**
   * <code>repeated int64 applyJoinTeamIdList = 14;</code>
   * @param index The index of the element to return.
   * @return The applyJoinTeamIdList at the given index.
   */
  public long getApplyJoinTeamIdList(int index) {
    return applyJoinTeamIdList_.getLong(index);
  }

  public static final int ISENTER_FIELD_NUMBER = 15;
  private boolean isEnter_ = false;
  /**
   * <code>optional bool isEnter = 15;</code>
   * @return Whether the isEnter field is set.
   */
  @java.lang.Override
  public boolean hasIsEnter() {
    return ((bitField0_ & 0x00000800) != 0);
  }
  /**
   * <code>optional bool isEnter = 15;</code>
   * @return The isEnter.
   */
  @java.lang.Override
  public boolean getIsEnter() {
    return isEnter_;
  }

  public static final int ENERGYLASTTIME_FIELD_NUMBER = 16;
  private long energyLastTime_ = 0L;
  /**
   * <code>optional int64 energyLastTime = 16;</code>
   * @return Whether the energyLastTime field is set.
   */
  @java.lang.Override
  public boolean hasEnergyLastTime() {
    return ((bitField0_ & 0x00001000) != 0);
  }
  /**
   * <code>optional int64 energyLastTime = 16;</code>
   * @return The energyLastTime.
   */
  @java.lang.Override
  public long getEnergyLastTime() {
    return energyLastTime_;
  }

  public static final int TOTALBLOOD_FIELD_NUMBER = 17;
  @SuppressWarnings("serial")
  private volatile java.lang.Object totalBlood_ = "";
  /**
   * <code>optional string totalBlood = 17;</code>
   * @return Whether the totalBlood field is set.
   */
  @java.lang.Override
  public boolean hasTotalBlood() {
    return ((bitField0_ & 0x00002000) != 0);
  }
  /**
   * <code>optional string totalBlood = 17;</code>
   * @return The totalBlood.
   */
  @java.lang.Override
  public java.lang.String getTotalBlood() {
    java.lang.Object ref = totalBlood_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        totalBlood_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string totalBlood = 17;</code>
   * @return The bytes for totalBlood.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getTotalBloodBytes() {
    java.lang.Object ref = totalBlood_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      totalBlood_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int BLOOD_FIELD_NUMBER = 18;
  @SuppressWarnings("serial")
  private volatile java.lang.Object blood_ = "";
  /**
   * <code>optional string blood = 18;</code>
   * @return Whether the blood field is set.
   */
  @java.lang.Override
  public boolean hasBlood() {
    return ((bitField0_ & 0x00004000) != 0);
  }
  /**
   * <code>optional string blood = 18;</code>
   * @return The blood.
   */
  @java.lang.Override
  public java.lang.String getBlood() {
    java.lang.Object ref = blood_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        blood_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string blood = 18;</code>
   * @return The bytes for blood.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getBloodBytes() {
    java.lang.Object ref = blood_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      blood_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int TEAMMEMBERNOTIFY_FIELD_NUMBER = 19;
  private xddq.pb.ManHuangTeamMemberNotify teamMemberNotify_;
  /**
   * <code>optional .xddq.pb.ManHuangTeamMemberNotify teamMemberNotify = 19;</code>
   * @return Whether the teamMemberNotify field is set.
   */
  @java.lang.Override
  public boolean hasTeamMemberNotify() {
    return ((bitField0_ & 0x00008000) != 0);
  }
  /**
   * <code>optional .xddq.pb.ManHuangTeamMemberNotify teamMemberNotify = 19;</code>
   * @return The teamMemberNotify.
   */
  @java.lang.Override
  public xddq.pb.ManHuangTeamMemberNotify getTeamMemberNotify() {
    return teamMemberNotify_ == null ? xddq.pb.ManHuangTeamMemberNotify.getDefaultInstance() : teamMemberNotify_;
  }
  /**
   * <code>optional .xddq.pb.ManHuangTeamMemberNotify teamMemberNotify = 19;</code>
   */
  @java.lang.Override
  public xddq.pb.ManHuangTeamMemberNotifyOrBuilder getTeamMemberNotifyOrBuilder() {
    return teamMemberNotify_ == null ? xddq.pb.ManHuangTeamMemberNotify.getDefaultInstance() : teamMemberNotify_;
  }

  public static final int ENEMYNOTIFY_FIELD_NUMBER = 20;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.ManHuangEnemyNotify> enemyNotify_;
  /**
   * <code>repeated .xddq.pb.ManHuangEnemyNotify enemyNotify = 20;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.ManHuangEnemyNotify> getEnemyNotifyList() {
    return enemyNotify_;
  }
  /**
   * <code>repeated .xddq.pb.ManHuangEnemyNotify enemyNotify = 20;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.ManHuangEnemyNotifyOrBuilder> 
      getEnemyNotifyOrBuilderList() {
    return enemyNotify_;
  }
  /**
   * <code>repeated .xddq.pb.ManHuangEnemyNotify enemyNotify = 20;</code>
   */
  @java.lang.Override
  public int getEnemyNotifyCount() {
    return enemyNotify_.size();
  }
  /**
   * <code>repeated .xddq.pb.ManHuangEnemyNotify enemyNotify = 20;</code>
   */
  @java.lang.Override
  public xddq.pb.ManHuangEnemyNotify getEnemyNotify(int index) {
    return enemyNotify_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.ManHuangEnemyNotify enemyNotify = 20;</code>
   */
  @java.lang.Override
  public xddq.pb.ManHuangEnemyNotifyOrBuilder getEnemyNotifyOrBuilder(
      int index) {
    return enemyNotify_.get(index);
  }

  public static final int MONSTERBALL_FIELD_NUMBER = 21;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.ManHuangUserItemInfoTemp> monsterBall_;
  /**
   * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp monsterBall = 21;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.ManHuangUserItemInfoTemp> getMonsterBallList() {
    return monsterBall_;
  }
  /**
   * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp monsterBall = 21;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.ManHuangUserItemInfoTempOrBuilder> 
      getMonsterBallOrBuilderList() {
    return monsterBall_;
  }
  /**
   * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp monsterBall = 21;</code>
   */
  @java.lang.Override
  public int getMonsterBallCount() {
    return monsterBall_.size();
  }
  /**
   * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp monsterBall = 21;</code>
   */
  @java.lang.Override
  public xddq.pb.ManHuangUserItemInfoTemp getMonsterBall(int index) {
    return monsterBall_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp monsterBall = 21;</code>
   */
  @java.lang.Override
  public xddq.pb.ManHuangUserItemInfoTempOrBuilder getMonsterBallOrBuilder(
      int index) {
    return monsterBall_.get(index);
  }

  public static final int GRASS_FIELD_NUMBER = 22;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.ManHuangUserItemInfoTemp> grass_;
  /**
   * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp grass = 22;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.ManHuangUserItemInfoTemp> getGrassList() {
    return grass_;
  }
  /**
   * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp grass = 22;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.ManHuangUserItemInfoTempOrBuilder> 
      getGrassOrBuilderList() {
    return grass_;
  }
  /**
   * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp grass = 22;</code>
   */
  @java.lang.Override
  public int getGrassCount() {
    return grass_.size();
  }
  /**
   * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp grass = 22;</code>
   */
  @java.lang.Override
  public xddq.pb.ManHuangUserItemInfoTemp getGrass(int index) {
    return grass_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp grass = 22;</code>
   */
  @java.lang.Override
  public xddq.pb.ManHuangUserItemInfoTempOrBuilder getGrassOrBuilder(
      int index) {
    return grass_.get(index);
  }

  public static final int BOX_FIELD_NUMBER = 23;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.ManHuangUserItemInfoTemp> box_;
  /**
   * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp box = 23;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.ManHuangUserItemInfoTemp> getBoxList() {
    return box_;
  }
  /**
   * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp box = 23;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.ManHuangUserItemInfoTempOrBuilder> 
      getBoxOrBuilderList() {
    return box_;
  }
  /**
   * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp box = 23;</code>
   */
  @java.lang.Override
  public int getBoxCount() {
    return box_.size();
  }
  /**
   * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp box = 23;</code>
   */
  @java.lang.Override
  public xddq.pb.ManHuangUserItemInfoTemp getBox(int index) {
    return box_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp box = 23;</code>
   */
  @java.lang.Override
  public xddq.pb.ManHuangUserItemInfoTempOrBuilder getBoxOrBuilder(
      int index) {
    return box_.get(index);
  }

  public static final int EVENTDONE_FIELD_NUMBER = 24;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.ManHuangUserItemInfoTemp> eventDone_;
  /**
   * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp eventDone = 24;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.ManHuangUserItemInfoTemp> getEventDoneList() {
    return eventDone_;
  }
  /**
   * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp eventDone = 24;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.ManHuangUserItemInfoTempOrBuilder> 
      getEventDoneOrBuilderList() {
    return eventDone_;
  }
  /**
   * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp eventDone = 24;</code>
   */
  @java.lang.Override
  public int getEventDoneCount() {
    return eventDone_.size();
  }
  /**
   * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp eventDone = 24;</code>
   */
  @java.lang.Override
  public xddq.pb.ManHuangUserItemInfoTemp getEventDone(int index) {
    return eventDone_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp eventDone = 24;</code>
   */
  @java.lang.Override
  public xddq.pb.ManHuangUserItemInfoTempOrBuilder getEventDoneOrBuilder(
      int index) {
    return eventDone_.get(index);
  }

  public static final int EVENTDOINGINFO_FIELD_NUMBER = 25;
  private xddq.pb.ManHuangEventDoingInfoEntity eventDoingInfo_;
  /**
   * <code>optional .xddq.pb.ManHuangEventDoingInfoEntity eventDoingInfo = 25;</code>
   * @return Whether the eventDoingInfo field is set.
   */
  @java.lang.Override
  public boolean hasEventDoingInfo() {
    return ((bitField0_ & 0x00010000) != 0);
  }
  /**
   * <code>optional .xddq.pb.ManHuangEventDoingInfoEntity eventDoingInfo = 25;</code>
   * @return The eventDoingInfo.
   */
  @java.lang.Override
  public xddq.pb.ManHuangEventDoingInfoEntity getEventDoingInfo() {
    return eventDoingInfo_ == null ? xddq.pb.ManHuangEventDoingInfoEntity.getDefaultInstance() : eventDoingInfo_;
  }
  /**
   * <code>optional .xddq.pb.ManHuangEventDoingInfoEntity eventDoingInfo = 25;</code>
   */
  @java.lang.Override
  public xddq.pb.ManHuangEventDoingInfoEntityOrBuilder getEventDoingInfoOrBuilder() {
    return eventDoingInfo_ == null ? xddq.pb.ManHuangEventDoingInfoEntity.getDefaultInstance() : eventDoingInfo_;
  }

  public static final int SIGNINFO_FIELD_NUMBER = 26;
  private xddq.pb.ManHuangEventDoingInfoEntity signInfo_;
  /**
   * <code>optional .xddq.pb.ManHuangEventDoingInfoEntity signInfo = 26;</code>
   * @return Whether the signInfo field is set.
   */
  @java.lang.Override
  public boolean hasSignInfo() {
    return ((bitField0_ & 0x00020000) != 0);
  }
  /**
   * <code>optional .xddq.pb.ManHuangEventDoingInfoEntity signInfo = 26;</code>
   * @return The signInfo.
   */
  @java.lang.Override
  public xddq.pb.ManHuangEventDoingInfoEntity getSignInfo() {
    return signInfo_ == null ? xddq.pb.ManHuangEventDoingInfoEntity.getDefaultInstance() : signInfo_;
  }
  /**
   * <code>optional .xddq.pb.ManHuangEventDoingInfoEntity signInfo = 26;</code>
   */
  @java.lang.Override
  public xddq.pb.ManHuangEventDoingInfoEntityOrBuilder getSignInfoOrBuilder() {
    return signInfo_ == null ? xddq.pb.ManHuangEventDoingInfoEntity.getDefaultInstance() : signInfo_;
  }

  public static final int ICESOUL_FIELD_NUMBER = 27;
  private int iceSoul_ = 0;
  /**
   * <code>optional int32 iceSoul = 27;</code>
   * @return Whether the iceSoul field is set.
   */
  @java.lang.Override
  public boolean hasIceSoul() {
    return ((bitField0_ & 0x00040000) != 0);
  }
  /**
   * <code>optional int32 iceSoul = 27;</code>
   * @return The iceSoul.
   */
  @java.lang.Override
  public int getIceSoul() {
    return iceSoul_;
  }

  public static final int WININFO_FIELD_NUMBER = 28;
  private xddq.pb.ManHuangUserWinInfo winInfo_;
  /**
   * <code>optional .xddq.pb.ManHuangUserWinInfo winInfo = 28;</code>
   * @return Whether the winInfo field is set.
   */
  @java.lang.Override
  public boolean hasWinInfo() {
    return ((bitField0_ & 0x00080000) != 0);
  }
  /**
   * <code>optional .xddq.pb.ManHuangUserWinInfo winInfo = 28;</code>
   * @return The winInfo.
   */
  @java.lang.Override
  public xddq.pb.ManHuangUserWinInfo getWinInfo() {
    return winInfo_ == null ? xddq.pb.ManHuangUserWinInfo.getDefaultInstance() : winInfo_;
  }
  /**
   * <code>optional .xddq.pb.ManHuangUserWinInfo winInfo = 28;</code>
   */
  @java.lang.Override
  public xddq.pb.ManHuangUserWinInfoOrBuilder getWinInfoOrBuilder() {
    return winInfo_ == null ? xddq.pb.ManHuangUserWinInfo.getDefaultInstance() : winInfo_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (hasTeamMemberNotify()) {
      if (!getTeamMemberNotify().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    for (int i = 0; i < getEnemyNotifyCount(); i++) {
      if (!getEnemyNotify(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    for (int i = 0; i < getMonsterBallCount(); i++) {
      if (!getMonsterBall(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    for (int i = 0; i < getGrassCount(); i++) {
      if (!getGrass(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    for (int i = 0; i < getBoxCount(); i++) {
      if (!getBox(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    for (int i = 0; i < getEventDoneCount(); i++) {
      if (!getEventDone(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    if (hasEventDoingInfo()) {
      if (!getEventDoingInfo().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    if (hasSignInfo()) {
      if (!getSignInfo().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    if (hasWinInfo()) {
      if (!getWinInfo().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt64(1, userId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt64(2, teamId_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeMessage(3, getPlayerInfo());
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 4, fightValue_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      output.writeInt32(5, regionId_);
    }
    for (int i = 0; i < unlockRegionId_.size(); i++) {
      output.writeInt32(6, unlockRegionId_.getInt(i));
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      output.writeInt32(7, energy_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      output.writeInt64(8, integral_);
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      output.writeInt64(9, rank_);
    }
    if (((bitField0_ & 0x00000100) != 0)) {
      output.writeMessage(10, getTeamInfo());
    }
    for (int i = 0; i < mallInfo_.size(); i++) {
      output.writeMessage(11, mallInfo_.get(i));
    }
    if (((bitField0_ & 0x00000200) != 0)) {
      output.writeInt64(12, lastLeaveTeam_);
    }
    if (((bitField0_ & 0x00000400) != 0)) {
      output.writeInt64(13, lastCreateTeam_);
    }
    for (int i = 0; i < applyJoinTeamIdList_.size(); i++) {
      output.writeInt64(14, applyJoinTeamIdList_.getLong(i));
    }
    if (((bitField0_ & 0x00000800) != 0)) {
      output.writeBool(15, isEnter_);
    }
    if (((bitField0_ & 0x00001000) != 0)) {
      output.writeInt64(16, energyLastTime_);
    }
    if (((bitField0_ & 0x00002000) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 17, totalBlood_);
    }
    if (((bitField0_ & 0x00004000) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 18, blood_);
    }
    if (((bitField0_ & 0x00008000) != 0)) {
      output.writeMessage(19, getTeamMemberNotify());
    }
    for (int i = 0; i < enemyNotify_.size(); i++) {
      output.writeMessage(20, enemyNotify_.get(i));
    }
    for (int i = 0; i < monsterBall_.size(); i++) {
      output.writeMessage(21, monsterBall_.get(i));
    }
    for (int i = 0; i < grass_.size(); i++) {
      output.writeMessage(22, grass_.get(i));
    }
    for (int i = 0; i < box_.size(); i++) {
      output.writeMessage(23, box_.get(i));
    }
    for (int i = 0; i < eventDone_.size(); i++) {
      output.writeMessage(24, eventDone_.get(i));
    }
    if (((bitField0_ & 0x00010000) != 0)) {
      output.writeMessage(25, getEventDoingInfo());
    }
    if (((bitField0_ & 0x00020000) != 0)) {
      output.writeMessage(26, getSignInfo());
    }
    if (((bitField0_ & 0x00040000) != 0)) {
      output.writeInt32(27, iceSoul_);
    }
    if (((bitField0_ & 0x00080000) != 0)) {
      output.writeMessage(28, getWinInfo());
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(1, userId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(2, teamId_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, getPlayerInfo());
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(4, fightValue_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(5, regionId_);
    }
    {
      int dataSize = 0;
      for (int i = 0; i < unlockRegionId_.size(); i++) {
        dataSize += com.google.protobuf.CodedOutputStream
          .computeInt32SizeNoTag(unlockRegionId_.getInt(i));
      }
      size += dataSize;
      size += 1 * getUnlockRegionIdList().size();
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(7, energy_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(8, integral_);
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(9, rank_);
    }
    if (((bitField0_ & 0x00000100) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(10, getTeamInfo());
    }
    for (int i = 0; i < mallInfo_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(11, mallInfo_.get(i));
    }
    if (((bitField0_ & 0x00000200) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(12, lastLeaveTeam_);
    }
    if (((bitField0_ & 0x00000400) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(13, lastCreateTeam_);
    }
    {
      int dataSize = 0;
      for (int i = 0; i < applyJoinTeamIdList_.size(); i++) {
        dataSize += com.google.protobuf.CodedOutputStream
          .computeInt64SizeNoTag(applyJoinTeamIdList_.getLong(i));
      }
      size += dataSize;
      size += 1 * getApplyJoinTeamIdListList().size();
    }
    if (((bitField0_ & 0x00000800) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(15, isEnter_);
    }
    if (((bitField0_ & 0x00001000) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(16, energyLastTime_);
    }
    if (((bitField0_ & 0x00002000) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(17, totalBlood_);
    }
    if (((bitField0_ & 0x00004000) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(18, blood_);
    }
    if (((bitField0_ & 0x00008000) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(19, getTeamMemberNotify());
    }
    for (int i = 0; i < enemyNotify_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(20, enemyNotify_.get(i));
    }
    for (int i = 0; i < monsterBall_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(21, monsterBall_.get(i));
    }
    for (int i = 0; i < grass_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(22, grass_.get(i));
    }
    for (int i = 0; i < box_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(23, box_.get(i));
    }
    for (int i = 0; i < eventDone_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(24, eventDone_.get(i));
    }
    if (((bitField0_ & 0x00010000) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(25, getEventDoingInfo());
    }
    if (((bitField0_ & 0x00020000) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(26, getSignInfo());
    }
    if (((bitField0_ & 0x00040000) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(27, iceSoul_);
    }
    if (((bitField0_ & 0x00080000) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(28, getWinInfo());
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.ManHuangUserDataEntity)) {
      return super.equals(obj);
    }
    xddq.pb.ManHuangUserDataEntity other = (xddq.pb.ManHuangUserDataEntity) obj;

    if (hasUserId() != other.hasUserId()) return false;
    if (hasUserId()) {
      if (getUserId()
          != other.getUserId()) return false;
    }
    if (hasTeamId() != other.hasTeamId()) return false;
    if (hasTeamId()) {
      if (getTeamId()
          != other.getTeamId()) return false;
    }
    if (hasPlayerInfo() != other.hasPlayerInfo()) return false;
    if (hasPlayerInfo()) {
      if (!getPlayerInfo()
          .equals(other.getPlayerInfo())) return false;
    }
    if (hasFightValue() != other.hasFightValue()) return false;
    if (hasFightValue()) {
      if (!getFightValue()
          .equals(other.getFightValue())) return false;
    }
    if (hasRegionId() != other.hasRegionId()) return false;
    if (hasRegionId()) {
      if (getRegionId()
          != other.getRegionId()) return false;
    }
    if (!getUnlockRegionIdList()
        .equals(other.getUnlockRegionIdList())) return false;
    if (hasEnergy() != other.hasEnergy()) return false;
    if (hasEnergy()) {
      if (getEnergy()
          != other.getEnergy()) return false;
    }
    if (hasIntegral() != other.hasIntegral()) return false;
    if (hasIntegral()) {
      if (getIntegral()
          != other.getIntegral()) return false;
    }
    if (hasRank() != other.hasRank()) return false;
    if (hasRank()) {
      if (getRank()
          != other.getRank()) return false;
    }
    if (hasTeamInfo() != other.hasTeamInfo()) return false;
    if (hasTeamInfo()) {
      if (!getTeamInfo()
          .equals(other.getTeamInfo())) return false;
    }
    if (!getMallInfoList()
        .equals(other.getMallInfoList())) return false;
    if (hasLastLeaveTeam() != other.hasLastLeaveTeam()) return false;
    if (hasLastLeaveTeam()) {
      if (getLastLeaveTeam()
          != other.getLastLeaveTeam()) return false;
    }
    if (hasLastCreateTeam() != other.hasLastCreateTeam()) return false;
    if (hasLastCreateTeam()) {
      if (getLastCreateTeam()
          != other.getLastCreateTeam()) return false;
    }
    if (!getApplyJoinTeamIdListList()
        .equals(other.getApplyJoinTeamIdListList())) return false;
    if (hasIsEnter() != other.hasIsEnter()) return false;
    if (hasIsEnter()) {
      if (getIsEnter()
          != other.getIsEnter()) return false;
    }
    if (hasEnergyLastTime() != other.hasEnergyLastTime()) return false;
    if (hasEnergyLastTime()) {
      if (getEnergyLastTime()
          != other.getEnergyLastTime()) return false;
    }
    if (hasTotalBlood() != other.hasTotalBlood()) return false;
    if (hasTotalBlood()) {
      if (!getTotalBlood()
          .equals(other.getTotalBlood())) return false;
    }
    if (hasBlood() != other.hasBlood()) return false;
    if (hasBlood()) {
      if (!getBlood()
          .equals(other.getBlood())) return false;
    }
    if (hasTeamMemberNotify() != other.hasTeamMemberNotify()) return false;
    if (hasTeamMemberNotify()) {
      if (!getTeamMemberNotify()
          .equals(other.getTeamMemberNotify())) return false;
    }
    if (!getEnemyNotifyList()
        .equals(other.getEnemyNotifyList())) return false;
    if (!getMonsterBallList()
        .equals(other.getMonsterBallList())) return false;
    if (!getGrassList()
        .equals(other.getGrassList())) return false;
    if (!getBoxList()
        .equals(other.getBoxList())) return false;
    if (!getEventDoneList()
        .equals(other.getEventDoneList())) return false;
    if (hasEventDoingInfo() != other.hasEventDoingInfo()) return false;
    if (hasEventDoingInfo()) {
      if (!getEventDoingInfo()
          .equals(other.getEventDoingInfo())) return false;
    }
    if (hasSignInfo() != other.hasSignInfo()) return false;
    if (hasSignInfo()) {
      if (!getSignInfo()
          .equals(other.getSignInfo())) return false;
    }
    if (hasIceSoul() != other.hasIceSoul()) return false;
    if (hasIceSoul()) {
      if (getIceSoul()
          != other.getIceSoul()) return false;
    }
    if (hasWinInfo() != other.hasWinInfo()) return false;
    if (hasWinInfo()) {
      if (!getWinInfo()
          .equals(other.getWinInfo())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasUserId()) {
      hash = (37 * hash) + USERID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getUserId());
    }
    if (hasTeamId()) {
      hash = (37 * hash) + TEAMID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTeamId());
    }
    if (hasPlayerInfo()) {
      hash = (37 * hash) + PLAYERINFO_FIELD_NUMBER;
      hash = (53 * hash) + getPlayerInfo().hashCode();
    }
    if (hasFightValue()) {
      hash = (37 * hash) + FIGHTVALUE_FIELD_NUMBER;
      hash = (53 * hash) + getFightValue().hashCode();
    }
    if (hasRegionId()) {
      hash = (37 * hash) + REGIONID_FIELD_NUMBER;
      hash = (53 * hash) + getRegionId();
    }
    if (getUnlockRegionIdCount() > 0) {
      hash = (37 * hash) + UNLOCKREGIONID_FIELD_NUMBER;
      hash = (53 * hash) + getUnlockRegionIdList().hashCode();
    }
    if (hasEnergy()) {
      hash = (37 * hash) + ENERGY_FIELD_NUMBER;
      hash = (53 * hash) + getEnergy();
    }
    if (hasIntegral()) {
      hash = (37 * hash) + INTEGRAL_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getIntegral());
    }
    if (hasRank()) {
      hash = (37 * hash) + RANK_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getRank());
    }
    if (hasTeamInfo()) {
      hash = (37 * hash) + TEAMINFO_FIELD_NUMBER;
      hash = (53 * hash) + getTeamInfo().hashCode();
    }
    if (getMallInfoCount() > 0) {
      hash = (37 * hash) + MALLINFO_FIELD_NUMBER;
      hash = (53 * hash) + getMallInfoList().hashCode();
    }
    if (hasLastLeaveTeam()) {
      hash = (37 * hash) + LASTLEAVETEAM_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getLastLeaveTeam());
    }
    if (hasLastCreateTeam()) {
      hash = (37 * hash) + LASTCREATETEAM_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getLastCreateTeam());
    }
    if (getApplyJoinTeamIdListCount() > 0) {
      hash = (37 * hash) + APPLYJOINTEAMIDLIST_FIELD_NUMBER;
      hash = (53 * hash) + getApplyJoinTeamIdListList().hashCode();
    }
    if (hasIsEnter()) {
      hash = (37 * hash) + ISENTER_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getIsEnter());
    }
    if (hasEnergyLastTime()) {
      hash = (37 * hash) + ENERGYLASTTIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getEnergyLastTime());
    }
    if (hasTotalBlood()) {
      hash = (37 * hash) + TOTALBLOOD_FIELD_NUMBER;
      hash = (53 * hash) + getTotalBlood().hashCode();
    }
    if (hasBlood()) {
      hash = (37 * hash) + BLOOD_FIELD_NUMBER;
      hash = (53 * hash) + getBlood().hashCode();
    }
    if (hasTeamMemberNotify()) {
      hash = (37 * hash) + TEAMMEMBERNOTIFY_FIELD_NUMBER;
      hash = (53 * hash) + getTeamMemberNotify().hashCode();
    }
    if (getEnemyNotifyCount() > 0) {
      hash = (37 * hash) + ENEMYNOTIFY_FIELD_NUMBER;
      hash = (53 * hash) + getEnemyNotifyList().hashCode();
    }
    if (getMonsterBallCount() > 0) {
      hash = (37 * hash) + MONSTERBALL_FIELD_NUMBER;
      hash = (53 * hash) + getMonsterBallList().hashCode();
    }
    if (getGrassCount() > 0) {
      hash = (37 * hash) + GRASS_FIELD_NUMBER;
      hash = (53 * hash) + getGrassList().hashCode();
    }
    if (getBoxCount() > 0) {
      hash = (37 * hash) + BOX_FIELD_NUMBER;
      hash = (53 * hash) + getBoxList().hashCode();
    }
    if (getEventDoneCount() > 0) {
      hash = (37 * hash) + EVENTDONE_FIELD_NUMBER;
      hash = (53 * hash) + getEventDoneList().hashCode();
    }
    if (hasEventDoingInfo()) {
      hash = (37 * hash) + EVENTDOINGINFO_FIELD_NUMBER;
      hash = (53 * hash) + getEventDoingInfo().hashCode();
    }
    if (hasSignInfo()) {
      hash = (37 * hash) + SIGNINFO_FIELD_NUMBER;
      hash = (53 * hash) + getSignInfo().hashCode();
    }
    if (hasIceSoul()) {
      hash = (37 * hash) + ICESOUL_FIELD_NUMBER;
      hash = (53 * hash) + getIceSoul();
    }
    if (hasWinInfo()) {
      hash = (37 * hash) + WININFO_FIELD_NUMBER;
      hash = (53 * hash) + getWinInfo().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.ManHuangUserDataEntity parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ManHuangUserDataEntity parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ManHuangUserDataEntity parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ManHuangUserDataEntity parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ManHuangUserDataEntity parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ManHuangUserDataEntity parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ManHuangUserDataEntity parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.ManHuangUserDataEntity parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.ManHuangUserDataEntity parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.ManHuangUserDataEntity parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.ManHuangUserDataEntity parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.ManHuangUserDataEntity parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.ManHuangUserDataEntity prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.ManHuangUserDataEntity}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.ManHuangUserDataEntity)
      xddq.pb.ManHuangUserDataEntityOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ManHuangUserDataEntity_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ManHuangUserDataEntity_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.ManHuangUserDataEntity.class, xddq.pb.ManHuangUserDataEntity.Builder.class);
    }

    // Construct using xddq.pb.ManHuangUserDataEntity.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetPlayerInfoFieldBuilder();
        internalGetTeamInfoFieldBuilder();
        internalGetMallInfoFieldBuilder();
        internalGetTeamMemberNotifyFieldBuilder();
        internalGetEnemyNotifyFieldBuilder();
        internalGetMonsterBallFieldBuilder();
        internalGetGrassFieldBuilder();
        internalGetBoxFieldBuilder();
        internalGetEventDoneFieldBuilder();
        internalGetEventDoingInfoFieldBuilder();
        internalGetSignInfoFieldBuilder();
        internalGetWinInfoFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      userId_ = 0L;
      teamId_ = 0L;
      playerInfo_ = null;
      if (playerInfoBuilder_ != null) {
        playerInfoBuilder_.dispose();
        playerInfoBuilder_ = null;
      }
      fightValue_ = "";
      regionId_ = 0;
      unlockRegionId_ = emptyIntList();
      energy_ = 0;
      integral_ = 0L;
      rank_ = 0L;
      teamInfo_ = null;
      if (teamInfoBuilder_ != null) {
        teamInfoBuilder_.dispose();
        teamInfoBuilder_ = null;
      }
      if (mallInfoBuilder_ == null) {
        mallInfo_ = java.util.Collections.emptyList();
      } else {
        mallInfo_ = null;
        mallInfoBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000400);
      lastLeaveTeam_ = 0L;
      lastCreateTeam_ = 0L;
      applyJoinTeamIdList_ = emptyLongList();
      isEnter_ = false;
      energyLastTime_ = 0L;
      totalBlood_ = "";
      blood_ = "";
      teamMemberNotify_ = null;
      if (teamMemberNotifyBuilder_ != null) {
        teamMemberNotifyBuilder_.dispose();
        teamMemberNotifyBuilder_ = null;
      }
      if (enemyNotifyBuilder_ == null) {
        enemyNotify_ = java.util.Collections.emptyList();
      } else {
        enemyNotify_ = null;
        enemyNotifyBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00080000);
      if (monsterBallBuilder_ == null) {
        monsterBall_ = java.util.Collections.emptyList();
      } else {
        monsterBall_ = null;
        monsterBallBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00100000);
      if (grassBuilder_ == null) {
        grass_ = java.util.Collections.emptyList();
      } else {
        grass_ = null;
        grassBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00200000);
      if (boxBuilder_ == null) {
        box_ = java.util.Collections.emptyList();
      } else {
        box_ = null;
        boxBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00400000);
      if (eventDoneBuilder_ == null) {
        eventDone_ = java.util.Collections.emptyList();
      } else {
        eventDone_ = null;
        eventDoneBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00800000);
      eventDoingInfo_ = null;
      if (eventDoingInfoBuilder_ != null) {
        eventDoingInfoBuilder_.dispose();
        eventDoingInfoBuilder_ = null;
      }
      signInfo_ = null;
      if (signInfoBuilder_ != null) {
        signInfoBuilder_.dispose();
        signInfoBuilder_ = null;
      }
      iceSoul_ = 0;
      winInfo_ = null;
      if (winInfoBuilder_ != null) {
        winInfoBuilder_.dispose();
        winInfoBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ManHuangUserDataEntity_descriptor;
    }

    @java.lang.Override
    public xddq.pb.ManHuangUserDataEntity getDefaultInstanceForType() {
      return xddq.pb.ManHuangUserDataEntity.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.ManHuangUserDataEntity build() {
      xddq.pb.ManHuangUserDataEntity result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.ManHuangUserDataEntity buildPartial() {
      xddq.pb.ManHuangUserDataEntity result = new xddq.pb.ManHuangUserDataEntity(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.ManHuangUserDataEntity result) {
      if (mallInfoBuilder_ == null) {
        if (((bitField0_ & 0x00000400) != 0)) {
          mallInfo_ = java.util.Collections.unmodifiableList(mallInfo_);
          bitField0_ = (bitField0_ & ~0x00000400);
        }
        result.mallInfo_ = mallInfo_;
      } else {
        result.mallInfo_ = mallInfoBuilder_.build();
      }
      if (enemyNotifyBuilder_ == null) {
        if (((bitField0_ & 0x00080000) != 0)) {
          enemyNotify_ = java.util.Collections.unmodifiableList(enemyNotify_);
          bitField0_ = (bitField0_ & ~0x00080000);
        }
        result.enemyNotify_ = enemyNotify_;
      } else {
        result.enemyNotify_ = enemyNotifyBuilder_.build();
      }
      if (monsterBallBuilder_ == null) {
        if (((bitField0_ & 0x00100000) != 0)) {
          monsterBall_ = java.util.Collections.unmodifiableList(monsterBall_);
          bitField0_ = (bitField0_ & ~0x00100000);
        }
        result.monsterBall_ = monsterBall_;
      } else {
        result.monsterBall_ = monsterBallBuilder_.build();
      }
      if (grassBuilder_ == null) {
        if (((bitField0_ & 0x00200000) != 0)) {
          grass_ = java.util.Collections.unmodifiableList(grass_);
          bitField0_ = (bitField0_ & ~0x00200000);
        }
        result.grass_ = grass_;
      } else {
        result.grass_ = grassBuilder_.build();
      }
      if (boxBuilder_ == null) {
        if (((bitField0_ & 0x00400000) != 0)) {
          box_ = java.util.Collections.unmodifiableList(box_);
          bitField0_ = (bitField0_ & ~0x00400000);
        }
        result.box_ = box_;
      } else {
        result.box_ = boxBuilder_.build();
      }
      if (eventDoneBuilder_ == null) {
        if (((bitField0_ & 0x00800000) != 0)) {
          eventDone_ = java.util.Collections.unmodifiableList(eventDone_);
          bitField0_ = (bitField0_ & ~0x00800000);
        }
        result.eventDone_ = eventDone_;
      } else {
        result.eventDone_ = eventDoneBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.ManHuangUserDataEntity result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.userId_ = userId_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.teamId_ = teamId_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.playerInfo_ = playerInfoBuilder_ == null
            ? playerInfo_
            : playerInfoBuilder_.build();
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.fightValue_ = fightValue_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.regionId_ = regionId_;
        to_bitField0_ |= 0x00000010;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        unlockRegionId_.makeImmutable();
        result.unlockRegionId_ = unlockRegionId_;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.energy_ = energy_;
        to_bitField0_ |= 0x00000020;
      }
      if (((from_bitField0_ & 0x00000080) != 0)) {
        result.integral_ = integral_;
        to_bitField0_ |= 0x00000040;
      }
      if (((from_bitField0_ & 0x00000100) != 0)) {
        result.rank_ = rank_;
        to_bitField0_ |= 0x00000080;
      }
      if (((from_bitField0_ & 0x00000200) != 0)) {
        result.teamInfo_ = teamInfoBuilder_ == null
            ? teamInfo_
            : teamInfoBuilder_.build();
        to_bitField0_ |= 0x00000100;
      }
      if (((from_bitField0_ & 0x00000800) != 0)) {
        result.lastLeaveTeam_ = lastLeaveTeam_;
        to_bitField0_ |= 0x00000200;
      }
      if (((from_bitField0_ & 0x00001000) != 0)) {
        result.lastCreateTeam_ = lastCreateTeam_;
        to_bitField0_ |= 0x00000400;
      }
      if (((from_bitField0_ & 0x00002000) != 0)) {
        applyJoinTeamIdList_.makeImmutable();
        result.applyJoinTeamIdList_ = applyJoinTeamIdList_;
      }
      if (((from_bitField0_ & 0x00004000) != 0)) {
        result.isEnter_ = isEnter_;
        to_bitField0_ |= 0x00000800;
      }
      if (((from_bitField0_ & 0x00008000) != 0)) {
        result.energyLastTime_ = energyLastTime_;
        to_bitField0_ |= 0x00001000;
      }
      if (((from_bitField0_ & 0x00010000) != 0)) {
        result.totalBlood_ = totalBlood_;
        to_bitField0_ |= 0x00002000;
      }
      if (((from_bitField0_ & 0x00020000) != 0)) {
        result.blood_ = blood_;
        to_bitField0_ |= 0x00004000;
      }
      if (((from_bitField0_ & 0x00040000) != 0)) {
        result.teamMemberNotify_ = teamMemberNotifyBuilder_ == null
            ? teamMemberNotify_
            : teamMemberNotifyBuilder_.build();
        to_bitField0_ |= 0x00008000;
      }
      if (((from_bitField0_ & 0x01000000) != 0)) {
        result.eventDoingInfo_ = eventDoingInfoBuilder_ == null
            ? eventDoingInfo_
            : eventDoingInfoBuilder_.build();
        to_bitField0_ |= 0x00010000;
      }
      if (((from_bitField0_ & 0x02000000) != 0)) {
        result.signInfo_ = signInfoBuilder_ == null
            ? signInfo_
            : signInfoBuilder_.build();
        to_bitField0_ |= 0x00020000;
      }
      if (((from_bitField0_ & 0x04000000) != 0)) {
        result.iceSoul_ = iceSoul_;
        to_bitField0_ |= 0x00040000;
      }
      if (((from_bitField0_ & 0x08000000) != 0)) {
        result.winInfo_ = winInfoBuilder_ == null
            ? winInfo_
            : winInfoBuilder_.build();
        to_bitField0_ |= 0x00080000;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.ManHuangUserDataEntity) {
        return mergeFrom((xddq.pb.ManHuangUserDataEntity)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.ManHuangUserDataEntity other) {
      if (other == xddq.pb.ManHuangUserDataEntity.getDefaultInstance()) return this;
      if (other.hasUserId()) {
        setUserId(other.getUserId());
      }
      if (other.hasTeamId()) {
        setTeamId(other.getTeamId());
      }
      if (other.hasPlayerInfo()) {
        mergePlayerInfo(other.getPlayerInfo());
      }
      if (other.hasFightValue()) {
        fightValue_ = other.fightValue_;
        bitField0_ |= 0x00000008;
        onChanged();
      }
      if (other.hasRegionId()) {
        setRegionId(other.getRegionId());
      }
      if (!other.unlockRegionId_.isEmpty()) {
        if (unlockRegionId_.isEmpty()) {
          unlockRegionId_ = other.unlockRegionId_;
          unlockRegionId_.makeImmutable();
          bitField0_ |= 0x00000020;
        } else {
          ensureUnlockRegionIdIsMutable();
          unlockRegionId_.addAll(other.unlockRegionId_);
        }
        onChanged();
      }
      if (other.hasEnergy()) {
        setEnergy(other.getEnergy());
      }
      if (other.hasIntegral()) {
        setIntegral(other.getIntegral());
      }
      if (other.hasRank()) {
        setRank(other.getRank());
      }
      if (other.hasTeamInfo()) {
        mergeTeamInfo(other.getTeamInfo());
      }
      if (mallInfoBuilder_ == null) {
        if (!other.mallInfo_.isEmpty()) {
          if (mallInfo_.isEmpty()) {
            mallInfo_ = other.mallInfo_;
            bitField0_ = (bitField0_ & ~0x00000400);
          } else {
            ensureMallInfoIsMutable();
            mallInfo_.addAll(other.mallInfo_);
          }
          onChanged();
        }
      } else {
        if (!other.mallInfo_.isEmpty()) {
          if (mallInfoBuilder_.isEmpty()) {
            mallInfoBuilder_.dispose();
            mallInfoBuilder_ = null;
            mallInfo_ = other.mallInfo_;
            bitField0_ = (bitField0_ & ~0x00000400);
            mallInfoBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetMallInfoFieldBuilder() : null;
          } else {
            mallInfoBuilder_.addAllMessages(other.mallInfo_);
          }
        }
      }
      if (other.hasLastLeaveTeam()) {
        setLastLeaveTeam(other.getLastLeaveTeam());
      }
      if (other.hasLastCreateTeam()) {
        setLastCreateTeam(other.getLastCreateTeam());
      }
      if (!other.applyJoinTeamIdList_.isEmpty()) {
        if (applyJoinTeamIdList_.isEmpty()) {
          applyJoinTeamIdList_ = other.applyJoinTeamIdList_;
          applyJoinTeamIdList_.makeImmutable();
          bitField0_ |= 0x00002000;
        } else {
          ensureApplyJoinTeamIdListIsMutable();
          applyJoinTeamIdList_.addAll(other.applyJoinTeamIdList_);
        }
        onChanged();
      }
      if (other.hasIsEnter()) {
        setIsEnter(other.getIsEnter());
      }
      if (other.hasEnergyLastTime()) {
        setEnergyLastTime(other.getEnergyLastTime());
      }
      if (other.hasTotalBlood()) {
        totalBlood_ = other.totalBlood_;
        bitField0_ |= 0x00010000;
        onChanged();
      }
      if (other.hasBlood()) {
        blood_ = other.blood_;
        bitField0_ |= 0x00020000;
        onChanged();
      }
      if (other.hasTeamMemberNotify()) {
        mergeTeamMemberNotify(other.getTeamMemberNotify());
      }
      if (enemyNotifyBuilder_ == null) {
        if (!other.enemyNotify_.isEmpty()) {
          if (enemyNotify_.isEmpty()) {
            enemyNotify_ = other.enemyNotify_;
            bitField0_ = (bitField0_ & ~0x00080000);
          } else {
            ensureEnemyNotifyIsMutable();
            enemyNotify_.addAll(other.enemyNotify_);
          }
          onChanged();
        }
      } else {
        if (!other.enemyNotify_.isEmpty()) {
          if (enemyNotifyBuilder_.isEmpty()) {
            enemyNotifyBuilder_.dispose();
            enemyNotifyBuilder_ = null;
            enemyNotify_ = other.enemyNotify_;
            bitField0_ = (bitField0_ & ~0x00080000);
            enemyNotifyBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetEnemyNotifyFieldBuilder() : null;
          } else {
            enemyNotifyBuilder_.addAllMessages(other.enemyNotify_);
          }
        }
      }
      if (monsterBallBuilder_ == null) {
        if (!other.monsterBall_.isEmpty()) {
          if (monsterBall_.isEmpty()) {
            monsterBall_ = other.monsterBall_;
            bitField0_ = (bitField0_ & ~0x00100000);
          } else {
            ensureMonsterBallIsMutable();
            monsterBall_.addAll(other.monsterBall_);
          }
          onChanged();
        }
      } else {
        if (!other.monsterBall_.isEmpty()) {
          if (monsterBallBuilder_.isEmpty()) {
            monsterBallBuilder_.dispose();
            monsterBallBuilder_ = null;
            monsterBall_ = other.monsterBall_;
            bitField0_ = (bitField0_ & ~0x00100000);
            monsterBallBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetMonsterBallFieldBuilder() : null;
          } else {
            monsterBallBuilder_.addAllMessages(other.monsterBall_);
          }
        }
      }
      if (grassBuilder_ == null) {
        if (!other.grass_.isEmpty()) {
          if (grass_.isEmpty()) {
            grass_ = other.grass_;
            bitField0_ = (bitField0_ & ~0x00200000);
          } else {
            ensureGrassIsMutable();
            grass_.addAll(other.grass_);
          }
          onChanged();
        }
      } else {
        if (!other.grass_.isEmpty()) {
          if (grassBuilder_.isEmpty()) {
            grassBuilder_.dispose();
            grassBuilder_ = null;
            grass_ = other.grass_;
            bitField0_ = (bitField0_ & ~0x00200000);
            grassBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetGrassFieldBuilder() : null;
          } else {
            grassBuilder_.addAllMessages(other.grass_);
          }
        }
      }
      if (boxBuilder_ == null) {
        if (!other.box_.isEmpty()) {
          if (box_.isEmpty()) {
            box_ = other.box_;
            bitField0_ = (bitField0_ & ~0x00400000);
          } else {
            ensureBoxIsMutable();
            box_.addAll(other.box_);
          }
          onChanged();
        }
      } else {
        if (!other.box_.isEmpty()) {
          if (boxBuilder_.isEmpty()) {
            boxBuilder_.dispose();
            boxBuilder_ = null;
            box_ = other.box_;
            bitField0_ = (bitField0_ & ~0x00400000);
            boxBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetBoxFieldBuilder() : null;
          } else {
            boxBuilder_.addAllMessages(other.box_);
          }
        }
      }
      if (eventDoneBuilder_ == null) {
        if (!other.eventDone_.isEmpty()) {
          if (eventDone_.isEmpty()) {
            eventDone_ = other.eventDone_;
            bitField0_ = (bitField0_ & ~0x00800000);
          } else {
            ensureEventDoneIsMutable();
            eventDone_.addAll(other.eventDone_);
          }
          onChanged();
        }
      } else {
        if (!other.eventDone_.isEmpty()) {
          if (eventDoneBuilder_.isEmpty()) {
            eventDoneBuilder_.dispose();
            eventDoneBuilder_ = null;
            eventDone_ = other.eventDone_;
            bitField0_ = (bitField0_ & ~0x00800000);
            eventDoneBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetEventDoneFieldBuilder() : null;
          } else {
            eventDoneBuilder_.addAllMessages(other.eventDone_);
          }
        }
      }
      if (other.hasEventDoingInfo()) {
        mergeEventDoingInfo(other.getEventDoingInfo());
      }
      if (other.hasSignInfo()) {
        mergeSignInfo(other.getSignInfo());
      }
      if (other.hasIceSoul()) {
        setIceSoul(other.getIceSoul());
      }
      if (other.hasWinInfo()) {
        mergeWinInfo(other.getWinInfo());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (hasTeamMemberNotify()) {
        if (!getTeamMemberNotify().isInitialized()) {
          return false;
        }
      }
      for (int i = 0; i < getEnemyNotifyCount(); i++) {
        if (!getEnemyNotify(i).isInitialized()) {
          return false;
        }
      }
      for (int i = 0; i < getMonsterBallCount(); i++) {
        if (!getMonsterBall(i).isInitialized()) {
          return false;
        }
      }
      for (int i = 0; i < getGrassCount(); i++) {
        if (!getGrass(i).isInitialized()) {
          return false;
        }
      }
      for (int i = 0; i < getBoxCount(); i++) {
        if (!getBox(i).isInitialized()) {
          return false;
        }
      }
      for (int i = 0; i < getEventDoneCount(); i++) {
        if (!getEventDone(i).isInitialized()) {
          return false;
        }
      }
      if (hasEventDoingInfo()) {
        if (!getEventDoingInfo().isInitialized()) {
          return false;
        }
      }
      if (hasSignInfo()) {
        if (!getSignInfo().isInitialized()) {
          return false;
        }
      }
      if (hasWinInfo()) {
        if (!getWinInfo().isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              userId_ = input.readInt64();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              teamId_ = input.readInt64();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 26: {
              input.readMessage(
                  internalGetPlayerInfoFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 34: {
              fightValue_ = input.readBytes();
              bitField0_ |= 0x00000008;
              break;
            } // case 34
            case 40: {
              regionId_ = input.readInt32();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            case 48: {
              int v = input.readInt32();
              ensureUnlockRegionIdIsMutable();
              unlockRegionId_.addInt(v);
              break;
            } // case 48
            case 50: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              ensureUnlockRegionIdIsMutable();
              while (input.getBytesUntilLimit() > 0) {
                unlockRegionId_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            } // case 50
            case 56: {
              energy_ = input.readInt32();
              bitField0_ |= 0x00000040;
              break;
            } // case 56
            case 64: {
              integral_ = input.readInt64();
              bitField0_ |= 0x00000080;
              break;
            } // case 64
            case 72: {
              rank_ = input.readInt64();
              bitField0_ |= 0x00000100;
              break;
            } // case 72
            case 82: {
              input.readMessage(
                  internalGetTeamInfoFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000200;
              break;
            } // case 82
            case 90: {
              xddq.pb.ManHuangUserMallInfo m =
                  input.readMessage(
                      xddq.pb.ManHuangUserMallInfo.parser(),
                      extensionRegistry);
              if (mallInfoBuilder_ == null) {
                ensureMallInfoIsMutable();
                mallInfo_.add(m);
              } else {
                mallInfoBuilder_.addMessage(m);
              }
              break;
            } // case 90
            case 96: {
              lastLeaveTeam_ = input.readInt64();
              bitField0_ |= 0x00000800;
              break;
            } // case 96
            case 104: {
              lastCreateTeam_ = input.readInt64();
              bitField0_ |= 0x00001000;
              break;
            } // case 104
            case 112: {
              long v = input.readInt64();
              ensureApplyJoinTeamIdListIsMutable();
              applyJoinTeamIdList_.addLong(v);
              break;
            } // case 112
            case 114: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              ensureApplyJoinTeamIdListIsMutable();
              while (input.getBytesUntilLimit() > 0) {
                applyJoinTeamIdList_.addLong(input.readInt64());
              }
              input.popLimit(limit);
              break;
            } // case 114
            case 120: {
              isEnter_ = input.readBool();
              bitField0_ |= 0x00004000;
              break;
            } // case 120
            case 128: {
              energyLastTime_ = input.readInt64();
              bitField0_ |= 0x00008000;
              break;
            } // case 128
            case 138: {
              totalBlood_ = input.readBytes();
              bitField0_ |= 0x00010000;
              break;
            } // case 138
            case 146: {
              blood_ = input.readBytes();
              bitField0_ |= 0x00020000;
              break;
            } // case 146
            case 154: {
              input.readMessage(
                  internalGetTeamMemberNotifyFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00040000;
              break;
            } // case 154
            case 162: {
              xddq.pb.ManHuangEnemyNotify m =
                  input.readMessage(
                      xddq.pb.ManHuangEnemyNotify.parser(),
                      extensionRegistry);
              if (enemyNotifyBuilder_ == null) {
                ensureEnemyNotifyIsMutable();
                enemyNotify_.add(m);
              } else {
                enemyNotifyBuilder_.addMessage(m);
              }
              break;
            } // case 162
            case 170: {
              xddq.pb.ManHuangUserItemInfoTemp m =
                  input.readMessage(
                      xddq.pb.ManHuangUserItemInfoTemp.parser(),
                      extensionRegistry);
              if (monsterBallBuilder_ == null) {
                ensureMonsterBallIsMutable();
                monsterBall_.add(m);
              } else {
                monsterBallBuilder_.addMessage(m);
              }
              break;
            } // case 170
            case 178: {
              xddq.pb.ManHuangUserItemInfoTemp m =
                  input.readMessage(
                      xddq.pb.ManHuangUserItemInfoTemp.parser(),
                      extensionRegistry);
              if (grassBuilder_ == null) {
                ensureGrassIsMutable();
                grass_.add(m);
              } else {
                grassBuilder_.addMessage(m);
              }
              break;
            } // case 178
            case 186: {
              xddq.pb.ManHuangUserItemInfoTemp m =
                  input.readMessage(
                      xddq.pb.ManHuangUserItemInfoTemp.parser(),
                      extensionRegistry);
              if (boxBuilder_ == null) {
                ensureBoxIsMutable();
                box_.add(m);
              } else {
                boxBuilder_.addMessage(m);
              }
              break;
            } // case 186
            case 194: {
              xddq.pb.ManHuangUserItemInfoTemp m =
                  input.readMessage(
                      xddq.pb.ManHuangUserItemInfoTemp.parser(),
                      extensionRegistry);
              if (eventDoneBuilder_ == null) {
                ensureEventDoneIsMutable();
                eventDone_.add(m);
              } else {
                eventDoneBuilder_.addMessage(m);
              }
              break;
            } // case 194
            case 202: {
              input.readMessage(
                  internalGetEventDoingInfoFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x01000000;
              break;
            } // case 202
            case 210: {
              input.readMessage(
                  internalGetSignInfoFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x02000000;
              break;
            } // case 210
            case 216: {
              iceSoul_ = input.readInt32();
              bitField0_ |= 0x04000000;
              break;
            } // case 216
            case 226: {
              input.readMessage(
                  internalGetWinInfoFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x08000000;
              break;
            } // case 226
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private long userId_ ;
    /**
     * <code>optional int64 userId = 1;</code>
     * @return Whether the userId field is set.
     */
    @java.lang.Override
    public boolean hasUserId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 userId = 1;</code>
     * @return The userId.
     */
    @java.lang.Override
    public long getUserId() {
      return userId_;
    }
    /**
     * <code>optional int64 userId = 1;</code>
     * @param value The userId to set.
     * @return This builder for chaining.
     */
    public Builder setUserId(long value) {

      userId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 userId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearUserId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      userId_ = 0L;
      onChanged();
      return this;
    }

    private long teamId_ ;
    /**
     * <code>optional int64 teamId = 2;</code>
     * @return Whether the teamId field is set.
     */
    @java.lang.Override
    public boolean hasTeamId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int64 teamId = 2;</code>
     * @return The teamId.
     */
    @java.lang.Override
    public long getTeamId() {
      return teamId_;
    }
    /**
     * <code>optional int64 teamId = 2;</code>
     * @param value The teamId to set.
     * @return This builder for chaining.
     */
    public Builder setTeamId(long value) {

      teamId_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 teamId = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearTeamId() {
      bitField0_ = (bitField0_ & ~0x00000002);
      teamId_ = 0L;
      onChanged();
      return this;
    }

    private xddq.pb.PlayerCharacterImageMsg playerInfo_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PlayerCharacterImageMsg, xddq.pb.PlayerCharacterImageMsg.Builder, xddq.pb.PlayerCharacterImageMsgOrBuilder> playerInfoBuilder_;
    /**
     * <code>optional .xddq.pb.PlayerCharacterImageMsg playerInfo = 3;</code>
     * @return Whether the playerInfo field is set.
     */
    public boolean hasPlayerInfo() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional .xddq.pb.PlayerCharacterImageMsg playerInfo = 3;</code>
     * @return The playerInfo.
     */
    public xddq.pb.PlayerCharacterImageMsg getPlayerInfo() {
      if (playerInfoBuilder_ == null) {
        return playerInfo_ == null ? xddq.pb.PlayerCharacterImageMsg.getDefaultInstance() : playerInfo_;
      } else {
        return playerInfoBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.PlayerCharacterImageMsg playerInfo = 3;</code>
     */
    public Builder setPlayerInfo(xddq.pb.PlayerCharacterImageMsg value) {
      if (playerInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        playerInfo_ = value;
      } else {
        playerInfoBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerCharacterImageMsg playerInfo = 3;</code>
     */
    public Builder setPlayerInfo(
        xddq.pb.PlayerCharacterImageMsg.Builder builderForValue) {
      if (playerInfoBuilder_ == null) {
        playerInfo_ = builderForValue.build();
      } else {
        playerInfoBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerCharacterImageMsg playerInfo = 3;</code>
     */
    public Builder mergePlayerInfo(xddq.pb.PlayerCharacterImageMsg value) {
      if (playerInfoBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0) &&
          playerInfo_ != null &&
          playerInfo_ != xddq.pb.PlayerCharacterImageMsg.getDefaultInstance()) {
          getPlayerInfoBuilder().mergeFrom(value);
        } else {
          playerInfo_ = value;
        }
      } else {
        playerInfoBuilder_.mergeFrom(value);
      }
      if (playerInfo_ != null) {
        bitField0_ |= 0x00000004;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerCharacterImageMsg playerInfo = 3;</code>
     */
    public Builder clearPlayerInfo() {
      bitField0_ = (bitField0_ & ~0x00000004);
      playerInfo_ = null;
      if (playerInfoBuilder_ != null) {
        playerInfoBuilder_.dispose();
        playerInfoBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerCharacterImageMsg playerInfo = 3;</code>
     */
    public xddq.pb.PlayerCharacterImageMsg.Builder getPlayerInfoBuilder() {
      bitField0_ |= 0x00000004;
      onChanged();
      return internalGetPlayerInfoFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.PlayerCharacterImageMsg playerInfo = 3;</code>
     */
    public xddq.pb.PlayerCharacterImageMsgOrBuilder getPlayerInfoOrBuilder() {
      if (playerInfoBuilder_ != null) {
        return playerInfoBuilder_.getMessageOrBuilder();
      } else {
        return playerInfo_ == null ?
            xddq.pb.PlayerCharacterImageMsg.getDefaultInstance() : playerInfo_;
      }
    }
    /**
     * <code>optional .xddq.pb.PlayerCharacterImageMsg playerInfo = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PlayerCharacterImageMsg, xddq.pb.PlayerCharacterImageMsg.Builder, xddq.pb.PlayerCharacterImageMsgOrBuilder> 
        internalGetPlayerInfoFieldBuilder() {
      if (playerInfoBuilder_ == null) {
        playerInfoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.PlayerCharacterImageMsg, xddq.pb.PlayerCharacterImageMsg.Builder, xddq.pb.PlayerCharacterImageMsgOrBuilder>(
                getPlayerInfo(),
                getParentForChildren(),
                isClean());
        playerInfo_ = null;
      }
      return playerInfoBuilder_;
    }

    private java.lang.Object fightValue_ = "";
    /**
     * <code>optional string fightValue = 4;</code>
     * @return Whether the fightValue field is set.
     */
    public boolean hasFightValue() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional string fightValue = 4;</code>
     * @return The fightValue.
     */
    public java.lang.String getFightValue() {
      java.lang.Object ref = fightValue_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          fightValue_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string fightValue = 4;</code>
     * @return The bytes for fightValue.
     */
    public com.google.protobuf.ByteString
        getFightValueBytes() {
      java.lang.Object ref = fightValue_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        fightValue_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string fightValue = 4;</code>
     * @param value The fightValue to set.
     * @return This builder for chaining.
     */
    public Builder setFightValue(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      fightValue_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional string fightValue = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearFightValue() {
      fightValue_ = getDefaultInstance().getFightValue();
      bitField0_ = (bitField0_ & ~0x00000008);
      onChanged();
      return this;
    }
    /**
     * <code>optional string fightValue = 4;</code>
     * @param value The bytes for fightValue to set.
     * @return This builder for chaining.
     */
    public Builder setFightValueBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      fightValue_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }

    private int regionId_ ;
    /**
     * <code>optional int32 regionId = 5;</code>
     * @return Whether the regionId field is set.
     */
    @java.lang.Override
    public boolean hasRegionId() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional int32 regionId = 5;</code>
     * @return The regionId.
     */
    @java.lang.Override
    public int getRegionId() {
      return regionId_;
    }
    /**
     * <code>optional int32 regionId = 5;</code>
     * @param value The regionId to set.
     * @return This builder for chaining.
     */
    public Builder setRegionId(int value) {

      regionId_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 regionId = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearRegionId() {
      bitField0_ = (bitField0_ & ~0x00000010);
      regionId_ = 0;
      onChanged();
      return this;
    }

    private com.google.protobuf.Internal.IntList unlockRegionId_ = emptyIntList();
    private void ensureUnlockRegionIdIsMutable() {
      if (!unlockRegionId_.isModifiable()) {
        unlockRegionId_ = makeMutableCopy(unlockRegionId_);
      }
      bitField0_ |= 0x00000020;
    }
    /**
     * <code>repeated int32 unlockRegionId = 6;</code>
     * @return A list containing the unlockRegionId.
     */
    public java.util.List<java.lang.Integer>
        getUnlockRegionIdList() {
      unlockRegionId_.makeImmutable();
      return unlockRegionId_;
    }
    /**
     * <code>repeated int32 unlockRegionId = 6;</code>
     * @return The count of unlockRegionId.
     */
    public int getUnlockRegionIdCount() {
      return unlockRegionId_.size();
    }
    /**
     * <code>repeated int32 unlockRegionId = 6;</code>
     * @param index The index of the element to return.
     * @return The unlockRegionId at the given index.
     */
    public int getUnlockRegionId(int index) {
      return unlockRegionId_.getInt(index);
    }
    /**
     * <code>repeated int32 unlockRegionId = 6;</code>
     * @param index The index to set the value at.
     * @param value The unlockRegionId to set.
     * @return This builder for chaining.
     */
    public Builder setUnlockRegionId(
        int index, int value) {

      ensureUnlockRegionIdIsMutable();
      unlockRegionId_.setInt(index, value);
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 unlockRegionId = 6;</code>
     * @param value The unlockRegionId to add.
     * @return This builder for chaining.
     */
    public Builder addUnlockRegionId(int value) {

      ensureUnlockRegionIdIsMutable();
      unlockRegionId_.addInt(value);
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 unlockRegionId = 6;</code>
     * @param values The unlockRegionId to add.
     * @return This builder for chaining.
     */
    public Builder addAllUnlockRegionId(
        java.lang.Iterable<? extends java.lang.Integer> values) {
      ensureUnlockRegionIdIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, unlockRegionId_);
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 unlockRegionId = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearUnlockRegionId() {
      unlockRegionId_ = emptyIntList();
      bitField0_ = (bitField0_ & ~0x00000020);
      onChanged();
      return this;
    }

    private int energy_ ;
    /**
     * <code>optional int32 energy = 7;</code>
     * @return Whether the energy field is set.
     */
    @java.lang.Override
    public boolean hasEnergy() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <code>optional int32 energy = 7;</code>
     * @return The energy.
     */
    @java.lang.Override
    public int getEnergy() {
      return energy_;
    }
    /**
     * <code>optional int32 energy = 7;</code>
     * @param value The energy to set.
     * @return This builder for chaining.
     */
    public Builder setEnergy(int value) {

      energy_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 energy = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearEnergy() {
      bitField0_ = (bitField0_ & ~0x00000040);
      energy_ = 0;
      onChanged();
      return this;
    }

    private long integral_ ;
    /**
     * <code>optional int64 integral = 8;</code>
     * @return Whether the integral field is set.
     */
    @java.lang.Override
    public boolean hasIntegral() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <code>optional int64 integral = 8;</code>
     * @return The integral.
     */
    @java.lang.Override
    public long getIntegral() {
      return integral_;
    }
    /**
     * <code>optional int64 integral = 8;</code>
     * @param value The integral to set.
     * @return This builder for chaining.
     */
    public Builder setIntegral(long value) {

      integral_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 integral = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearIntegral() {
      bitField0_ = (bitField0_ & ~0x00000080);
      integral_ = 0L;
      onChanged();
      return this;
    }

    private long rank_ ;
    /**
     * <code>optional int64 rank = 9;</code>
     * @return Whether the rank field is set.
     */
    @java.lang.Override
    public boolean hasRank() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <code>optional int64 rank = 9;</code>
     * @return The rank.
     */
    @java.lang.Override
    public long getRank() {
      return rank_;
    }
    /**
     * <code>optional int64 rank = 9;</code>
     * @param value The rank to set.
     * @return This builder for chaining.
     */
    public Builder setRank(long value) {

      rank_ = value;
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 rank = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearRank() {
      bitField0_ = (bitField0_ & ~0x00000100);
      rank_ = 0L;
      onChanged();
      return this;
    }

    private xddq.pb.ManHuangTeamEntity teamInfo_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.ManHuangTeamEntity, xddq.pb.ManHuangTeamEntity.Builder, xddq.pb.ManHuangTeamEntityOrBuilder> teamInfoBuilder_;
    /**
     * <code>optional .xddq.pb.ManHuangTeamEntity teamInfo = 10;</code>
     * @return Whether the teamInfo field is set.
     */
    public boolean hasTeamInfo() {
      return ((bitField0_ & 0x00000200) != 0);
    }
    /**
     * <code>optional .xddq.pb.ManHuangTeamEntity teamInfo = 10;</code>
     * @return The teamInfo.
     */
    public xddq.pb.ManHuangTeamEntity getTeamInfo() {
      if (teamInfoBuilder_ == null) {
        return teamInfo_ == null ? xddq.pb.ManHuangTeamEntity.getDefaultInstance() : teamInfo_;
      } else {
        return teamInfoBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.ManHuangTeamEntity teamInfo = 10;</code>
     */
    public Builder setTeamInfo(xddq.pb.ManHuangTeamEntity value) {
      if (teamInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        teamInfo_ = value;
      } else {
        teamInfoBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000200;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ManHuangTeamEntity teamInfo = 10;</code>
     */
    public Builder setTeamInfo(
        xddq.pb.ManHuangTeamEntity.Builder builderForValue) {
      if (teamInfoBuilder_ == null) {
        teamInfo_ = builderForValue.build();
      } else {
        teamInfoBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000200;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ManHuangTeamEntity teamInfo = 10;</code>
     */
    public Builder mergeTeamInfo(xddq.pb.ManHuangTeamEntity value) {
      if (teamInfoBuilder_ == null) {
        if (((bitField0_ & 0x00000200) != 0) &&
          teamInfo_ != null &&
          teamInfo_ != xddq.pb.ManHuangTeamEntity.getDefaultInstance()) {
          getTeamInfoBuilder().mergeFrom(value);
        } else {
          teamInfo_ = value;
        }
      } else {
        teamInfoBuilder_.mergeFrom(value);
      }
      if (teamInfo_ != null) {
        bitField0_ |= 0x00000200;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.ManHuangTeamEntity teamInfo = 10;</code>
     */
    public Builder clearTeamInfo() {
      bitField0_ = (bitField0_ & ~0x00000200);
      teamInfo_ = null;
      if (teamInfoBuilder_ != null) {
        teamInfoBuilder_.dispose();
        teamInfoBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ManHuangTeamEntity teamInfo = 10;</code>
     */
    public xddq.pb.ManHuangTeamEntity.Builder getTeamInfoBuilder() {
      bitField0_ |= 0x00000200;
      onChanged();
      return internalGetTeamInfoFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.ManHuangTeamEntity teamInfo = 10;</code>
     */
    public xddq.pb.ManHuangTeamEntityOrBuilder getTeamInfoOrBuilder() {
      if (teamInfoBuilder_ != null) {
        return teamInfoBuilder_.getMessageOrBuilder();
      } else {
        return teamInfo_ == null ?
            xddq.pb.ManHuangTeamEntity.getDefaultInstance() : teamInfo_;
      }
    }
    /**
     * <code>optional .xddq.pb.ManHuangTeamEntity teamInfo = 10;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.ManHuangTeamEntity, xddq.pb.ManHuangTeamEntity.Builder, xddq.pb.ManHuangTeamEntityOrBuilder> 
        internalGetTeamInfoFieldBuilder() {
      if (teamInfoBuilder_ == null) {
        teamInfoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.ManHuangTeamEntity, xddq.pb.ManHuangTeamEntity.Builder, xddq.pb.ManHuangTeamEntityOrBuilder>(
                getTeamInfo(),
                getParentForChildren(),
                isClean());
        teamInfo_ = null;
      }
      return teamInfoBuilder_;
    }

    private java.util.List<xddq.pb.ManHuangUserMallInfo> mallInfo_ =
      java.util.Collections.emptyList();
    private void ensureMallInfoIsMutable() {
      if (!((bitField0_ & 0x00000400) != 0)) {
        mallInfo_ = new java.util.ArrayList<xddq.pb.ManHuangUserMallInfo>(mallInfo_);
        bitField0_ |= 0x00000400;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.ManHuangUserMallInfo, xddq.pb.ManHuangUserMallInfo.Builder, xddq.pb.ManHuangUserMallInfoOrBuilder> mallInfoBuilder_;

    /**
     * <code>repeated .xddq.pb.ManHuangUserMallInfo mallInfo = 11;</code>
     */
    public java.util.List<xddq.pb.ManHuangUserMallInfo> getMallInfoList() {
      if (mallInfoBuilder_ == null) {
        return java.util.Collections.unmodifiableList(mallInfo_);
      } else {
        return mallInfoBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserMallInfo mallInfo = 11;</code>
     */
    public int getMallInfoCount() {
      if (mallInfoBuilder_ == null) {
        return mallInfo_.size();
      } else {
        return mallInfoBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserMallInfo mallInfo = 11;</code>
     */
    public xddq.pb.ManHuangUserMallInfo getMallInfo(int index) {
      if (mallInfoBuilder_ == null) {
        return mallInfo_.get(index);
      } else {
        return mallInfoBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserMallInfo mallInfo = 11;</code>
     */
    public Builder setMallInfo(
        int index, xddq.pb.ManHuangUserMallInfo value) {
      if (mallInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMallInfoIsMutable();
        mallInfo_.set(index, value);
        onChanged();
      } else {
        mallInfoBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserMallInfo mallInfo = 11;</code>
     */
    public Builder setMallInfo(
        int index, xddq.pb.ManHuangUserMallInfo.Builder builderForValue) {
      if (mallInfoBuilder_ == null) {
        ensureMallInfoIsMutable();
        mallInfo_.set(index, builderForValue.build());
        onChanged();
      } else {
        mallInfoBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserMallInfo mallInfo = 11;</code>
     */
    public Builder addMallInfo(xddq.pb.ManHuangUserMallInfo value) {
      if (mallInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMallInfoIsMutable();
        mallInfo_.add(value);
        onChanged();
      } else {
        mallInfoBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserMallInfo mallInfo = 11;</code>
     */
    public Builder addMallInfo(
        int index, xddq.pb.ManHuangUserMallInfo value) {
      if (mallInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMallInfoIsMutable();
        mallInfo_.add(index, value);
        onChanged();
      } else {
        mallInfoBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserMallInfo mallInfo = 11;</code>
     */
    public Builder addMallInfo(
        xddq.pb.ManHuangUserMallInfo.Builder builderForValue) {
      if (mallInfoBuilder_ == null) {
        ensureMallInfoIsMutable();
        mallInfo_.add(builderForValue.build());
        onChanged();
      } else {
        mallInfoBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserMallInfo mallInfo = 11;</code>
     */
    public Builder addMallInfo(
        int index, xddq.pb.ManHuangUserMallInfo.Builder builderForValue) {
      if (mallInfoBuilder_ == null) {
        ensureMallInfoIsMutable();
        mallInfo_.add(index, builderForValue.build());
        onChanged();
      } else {
        mallInfoBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserMallInfo mallInfo = 11;</code>
     */
    public Builder addAllMallInfo(
        java.lang.Iterable<? extends xddq.pb.ManHuangUserMallInfo> values) {
      if (mallInfoBuilder_ == null) {
        ensureMallInfoIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, mallInfo_);
        onChanged();
      } else {
        mallInfoBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserMallInfo mallInfo = 11;</code>
     */
    public Builder clearMallInfo() {
      if (mallInfoBuilder_ == null) {
        mallInfo_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000400);
        onChanged();
      } else {
        mallInfoBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserMallInfo mallInfo = 11;</code>
     */
    public Builder removeMallInfo(int index) {
      if (mallInfoBuilder_ == null) {
        ensureMallInfoIsMutable();
        mallInfo_.remove(index);
        onChanged();
      } else {
        mallInfoBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserMallInfo mallInfo = 11;</code>
     */
    public xddq.pb.ManHuangUserMallInfo.Builder getMallInfoBuilder(
        int index) {
      return internalGetMallInfoFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserMallInfo mallInfo = 11;</code>
     */
    public xddq.pb.ManHuangUserMallInfoOrBuilder getMallInfoOrBuilder(
        int index) {
      if (mallInfoBuilder_ == null) {
        return mallInfo_.get(index);  } else {
        return mallInfoBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserMallInfo mallInfo = 11;</code>
     */
    public java.util.List<? extends xddq.pb.ManHuangUserMallInfoOrBuilder> 
         getMallInfoOrBuilderList() {
      if (mallInfoBuilder_ != null) {
        return mallInfoBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(mallInfo_);
      }
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserMallInfo mallInfo = 11;</code>
     */
    public xddq.pb.ManHuangUserMallInfo.Builder addMallInfoBuilder() {
      return internalGetMallInfoFieldBuilder().addBuilder(
          xddq.pb.ManHuangUserMallInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserMallInfo mallInfo = 11;</code>
     */
    public xddq.pb.ManHuangUserMallInfo.Builder addMallInfoBuilder(
        int index) {
      return internalGetMallInfoFieldBuilder().addBuilder(
          index, xddq.pb.ManHuangUserMallInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserMallInfo mallInfo = 11;</code>
     */
    public java.util.List<xddq.pb.ManHuangUserMallInfo.Builder> 
         getMallInfoBuilderList() {
      return internalGetMallInfoFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.ManHuangUserMallInfo, xddq.pb.ManHuangUserMallInfo.Builder, xddq.pb.ManHuangUserMallInfoOrBuilder> 
        internalGetMallInfoFieldBuilder() {
      if (mallInfoBuilder_ == null) {
        mallInfoBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.ManHuangUserMallInfo, xddq.pb.ManHuangUserMallInfo.Builder, xddq.pb.ManHuangUserMallInfoOrBuilder>(
                mallInfo_,
                ((bitField0_ & 0x00000400) != 0),
                getParentForChildren(),
                isClean());
        mallInfo_ = null;
      }
      return mallInfoBuilder_;
    }

    private long lastLeaveTeam_ ;
    /**
     * <code>optional int64 lastLeaveTeam = 12;</code>
     * @return Whether the lastLeaveTeam field is set.
     */
    @java.lang.Override
    public boolean hasLastLeaveTeam() {
      return ((bitField0_ & 0x00000800) != 0);
    }
    /**
     * <code>optional int64 lastLeaveTeam = 12;</code>
     * @return The lastLeaveTeam.
     */
    @java.lang.Override
    public long getLastLeaveTeam() {
      return lastLeaveTeam_;
    }
    /**
     * <code>optional int64 lastLeaveTeam = 12;</code>
     * @param value The lastLeaveTeam to set.
     * @return This builder for chaining.
     */
    public Builder setLastLeaveTeam(long value) {

      lastLeaveTeam_ = value;
      bitField0_ |= 0x00000800;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 lastLeaveTeam = 12;</code>
     * @return This builder for chaining.
     */
    public Builder clearLastLeaveTeam() {
      bitField0_ = (bitField0_ & ~0x00000800);
      lastLeaveTeam_ = 0L;
      onChanged();
      return this;
    }

    private long lastCreateTeam_ ;
    /**
     * <code>optional int64 lastCreateTeam = 13;</code>
     * @return Whether the lastCreateTeam field is set.
     */
    @java.lang.Override
    public boolean hasLastCreateTeam() {
      return ((bitField0_ & 0x00001000) != 0);
    }
    /**
     * <code>optional int64 lastCreateTeam = 13;</code>
     * @return The lastCreateTeam.
     */
    @java.lang.Override
    public long getLastCreateTeam() {
      return lastCreateTeam_;
    }
    /**
     * <code>optional int64 lastCreateTeam = 13;</code>
     * @param value The lastCreateTeam to set.
     * @return This builder for chaining.
     */
    public Builder setLastCreateTeam(long value) {

      lastCreateTeam_ = value;
      bitField0_ |= 0x00001000;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 lastCreateTeam = 13;</code>
     * @return This builder for chaining.
     */
    public Builder clearLastCreateTeam() {
      bitField0_ = (bitField0_ & ~0x00001000);
      lastCreateTeam_ = 0L;
      onChanged();
      return this;
    }

    private com.google.protobuf.Internal.LongList applyJoinTeamIdList_ = emptyLongList();
    private void ensureApplyJoinTeamIdListIsMutable() {
      if (!applyJoinTeamIdList_.isModifiable()) {
        applyJoinTeamIdList_ = makeMutableCopy(applyJoinTeamIdList_);
      }
      bitField0_ |= 0x00002000;
    }
    /**
     * <code>repeated int64 applyJoinTeamIdList = 14;</code>
     * @return A list containing the applyJoinTeamIdList.
     */
    public java.util.List<java.lang.Long>
        getApplyJoinTeamIdListList() {
      applyJoinTeamIdList_.makeImmutable();
      return applyJoinTeamIdList_;
    }
    /**
     * <code>repeated int64 applyJoinTeamIdList = 14;</code>
     * @return The count of applyJoinTeamIdList.
     */
    public int getApplyJoinTeamIdListCount() {
      return applyJoinTeamIdList_.size();
    }
    /**
     * <code>repeated int64 applyJoinTeamIdList = 14;</code>
     * @param index The index of the element to return.
     * @return The applyJoinTeamIdList at the given index.
     */
    public long getApplyJoinTeamIdList(int index) {
      return applyJoinTeamIdList_.getLong(index);
    }
    /**
     * <code>repeated int64 applyJoinTeamIdList = 14;</code>
     * @param index The index to set the value at.
     * @param value The applyJoinTeamIdList to set.
     * @return This builder for chaining.
     */
    public Builder setApplyJoinTeamIdList(
        int index, long value) {

      ensureApplyJoinTeamIdListIsMutable();
      applyJoinTeamIdList_.setLong(index, value);
      bitField0_ |= 0x00002000;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int64 applyJoinTeamIdList = 14;</code>
     * @param value The applyJoinTeamIdList to add.
     * @return This builder for chaining.
     */
    public Builder addApplyJoinTeamIdList(long value) {

      ensureApplyJoinTeamIdListIsMutable();
      applyJoinTeamIdList_.addLong(value);
      bitField0_ |= 0x00002000;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int64 applyJoinTeamIdList = 14;</code>
     * @param values The applyJoinTeamIdList to add.
     * @return This builder for chaining.
     */
    public Builder addAllApplyJoinTeamIdList(
        java.lang.Iterable<? extends java.lang.Long> values) {
      ensureApplyJoinTeamIdListIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, applyJoinTeamIdList_);
      bitField0_ |= 0x00002000;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int64 applyJoinTeamIdList = 14;</code>
     * @return This builder for chaining.
     */
    public Builder clearApplyJoinTeamIdList() {
      applyJoinTeamIdList_ = emptyLongList();
      bitField0_ = (bitField0_ & ~0x00002000);
      onChanged();
      return this;
    }

    private boolean isEnter_ ;
    /**
     * <code>optional bool isEnter = 15;</code>
     * @return Whether the isEnter field is set.
     */
    @java.lang.Override
    public boolean hasIsEnter() {
      return ((bitField0_ & 0x00004000) != 0);
    }
    /**
     * <code>optional bool isEnter = 15;</code>
     * @return The isEnter.
     */
    @java.lang.Override
    public boolean getIsEnter() {
      return isEnter_;
    }
    /**
     * <code>optional bool isEnter = 15;</code>
     * @param value The isEnter to set.
     * @return This builder for chaining.
     */
    public Builder setIsEnter(boolean value) {

      isEnter_ = value;
      bitField0_ |= 0x00004000;
      onChanged();
      return this;
    }
    /**
     * <code>optional bool isEnter = 15;</code>
     * @return This builder for chaining.
     */
    public Builder clearIsEnter() {
      bitField0_ = (bitField0_ & ~0x00004000);
      isEnter_ = false;
      onChanged();
      return this;
    }

    private long energyLastTime_ ;
    /**
     * <code>optional int64 energyLastTime = 16;</code>
     * @return Whether the energyLastTime field is set.
     */
    @java.lang.Override
    public boolean hasEnergyLastTime() {
      return ((bitField0_ & 0x00008000) != 0);
    }
    /**
     * <code>optional int64 energyLastTime = 16;</code>
     * @return The energyLastTime.
     */
    @java.lang.Override
    public long getEnergyLastTime() {
      return energyLastTime_;
    }
    /**
     * <code>optional int64 energyLastTime = 16;</code>
     * @param value The energyLastTime to set.
     * @return This builder for chaining.
     */
    public Builder setEnergyLastTime(long value) {

      energyLastTime_ = value;
      bitField0_ |= 0x00008000;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 energyLastTime = 16;</code>
     * @return This builder for chaining.
     */
    public Builder clearEnergyLastTime() {
      bitField0_ = (bitField0_ & ~0x00008000);
      energyLastTime_ = 0L;
      onChanged();
      return this;
    }

    private java.lang.Object totalBlood_ = "";
    /**
     * <code>optional string totalBlood = 17;</code>
     * @return Whether the totalBlood field is set.
     */
    public boolean hasTotalBlood() {
      return ((bitField0_ & 0x00010000) != 0);
    }
    /**
     * <code>optional string totalBlood = 17;</code>
     * @return The totalBlood.
     */
    public java.lang.String getTotalBlood() {
      java.lang.Object ref = totalBlood_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          totalBlood_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string totalBlood = 17;</code>
     * @return The bytes for totalBlood.
     */
    public com.google.protobuf.ByteString
        getTotalBloodBytes() {
      java.lang.Object ref = totalBlood_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        totalBlood_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string totalBlood = 17;</code>
     * @param value The totalBlood to set.
     * @return This builder for chaining.
     */
    public Builder setTotalBlood(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      totalBlood_ = value;
      bitField0_ |= 0x00010000;
      onChanged();
      return this;
    }
    /**
     * <code>optional string totalBlood = 17;</code>
     * @return This builder for chaining.
     */
    public Builder clearTotalBlood() {
      totalBlood_ = getDefaultInstance().getTotalBlood();
      bitField0_ = (bitField0_ & ~0x00010000);
      onChanged();
      return this;
    }
    /**
     * <code>optional string totalBlood = 17;</code>
     * @param value The bytes for totalBlood to set.
     * @return This builder for chaining.
     */
    public Builder setTotalBloodBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      totalBlood_ = value;
      bitField0_ |= 0x00010000;
      onChanged();
      return this;
    }

    private java.lang.Object blood_ = "";
    /**
     * <code>optional string blood = 18;</code>
     * @return Whether the blood field is set.
     */
    public boolean hasBlood() {
      return ((bitField0_ & 0x00020000) != 0);
    }
    /**
     * <code>optional string blood = 18;</code>
     * @return The blood.
     */
    public java.lang.String getBlood() {
      java.lang.Object ref = blood_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          blood_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string blood = 18;</code>
     * @return The bytes for blood.
     */
    public com.google.protobuf.ByteString
        getBloodBytes() {
      java.lang.Object ref = blood_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        blood_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string blood = 18;</code>
     * @param value The blood to set.
     * @return This builder for chaining.
     */
    public Builder setBlood(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      blood_ = value;
      bitField0_ |= 0x00020000;
      onChanged();
      return this;
    }
    /**
     * <code>optional string blood = 18;</code>
     * @return This builder for chaining.
     */
    public Builder clearBlood() {
      blood_ = getDefaultInstance().getBlood();
      bitField0_ = (bitField0_ & ~0x00020000);
      onChanged();
      return this;
    }
    /**
     * <code>optional string blood = 18;</code>
     * @param value The bytes for blood to set.
     * @return This builder for chaining.
     */
    public Builder setBloodBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      blood_ = value;
      bitField0_ |= 0x00020000;
      onChanged();
      return this;
    }

    private xddq.pb.ManHuangTeamMemberNotify teamMemberNotify_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.ManHuangTeamMemberNotify, xddq.pb.ManHuangTeamMemberNotify.Builder, xddq.pb.ManHuangTeamMemberNotifyOrBuilder> teamMemberNotifyBuilder_;
    /**
     * <code>optional .xddq.pb.ManHuangTeamMemberNotify teamMemberNotify = 19;</code>
     * @return Whether the teamMemberNotify field is set.
     */
    public boolean hasTeamMemberNotify() {
      return ((bitField0_ & 0x00040000) != 0);
    }
    /**
     * <code>optional .xddq.pb.ManHuangTeamMemberNotify teamMemberNotify = 19;</code>
     * @return The teamMemberNotify.
     */
    public xddq.pb.ManHuangTeamMemberNotify getTeamMemberNotify() {
      if (teamMemberNotifyBuilder_ == null) {
        return teamMemberNotify_ == null ? xddq.pb.ManHuangTeamMemberNotify.getDefaultInstance() : teamMemberNotify_;
      } else {
        return teamMemberNotifyBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.ManHuangTeamMemberNotify teamMemberNotify = 19;</code>
     */
    public Builder setTeamMemberNotify(xddq.pb.ManHuangTeamMemberNotify value) {
      if (teamMemberNotifyBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        teamMemberNotify_ = value;
      } else {
        teamMemberNotifyBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00040000;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ManHuangTeamMemberNotify teamMemberNotify = 19;</code>
     */
    public Builder setTeamMemberNotify(
        xddq.pb.ManHuangTeamMemberNotify.Builder builderForValue) {
      if (teamMemberNotifyBuilder_ == null) {
        teamMemberNotify_ = builderForValue.build();
      } else {
        teamMemberNotifyBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00040000;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ManHuangTeamMemberNotify teamMemberNotify = 19;</code>
     */
    public Builder mergeTeamMemberNotify(xddq.pb.ManHuangTeamMemberNotify value) {
      if (teamMemberNotifyBuilder_ == null) {
        if (((bitField0_ & 0x00040000) != 0) &&
          teamMemberNotify_ != null &&
          teamMemberNotify_ != xddq.pb.ManHuangTeamMemberNotify.getDefaultInstance()) {
          getTeamMemberNotifyBuilder().mergeFrom(value);
        } else {
          teamMemberNotify_ = value;
        }
      } else {
        teamMemberNotifyBuilder_.mergeFrom(value);
      }
      if (teamMemberNotify_ != null) {
        bitField0_ |= 0x00040000;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.ManHuangTeamMemberNotify teamMemberNotify = 19;</code>
     */
    public Builder clearTeamMemberNotify() {
      bitField0_ = (bitField0_ & ~0x00040000);
      teamMemberNotify_ = null;
      if (teamMemberNotifyBuilder_ != null) {
        teamMemberNotifyBuilder_.dispose();
        teamMemberNotifyBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ManHuangTeamMemberNotify teamMemberNotify = 19;</code>
     */
    public xddq.pb.ManHuangTeamMemberNotify.Builder getTeamMemberNotifyBuilder() {
      bitField0_ |= 0x00040000;
      onChanged();
      return internalGetTeamMemberNotifyFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.ManHuangTeamMemberNotify teamMemberNotify = 19;</code>
     */
    public xddq.pb.ManHuangTeamMemberNotifyOrBuilder getTeamMemberNotifyOrBuilder() {
      if (teamMemberNotifyBuilder_ != null) {
        return teamMemberNotifyBuilder_.getMessageOrBuilder();
      } else {
        return teamMemberNotify_ == null ?
            xddq.pb.ManHuangTeamMemberNotify.getDefaultInstance() : teamMemberNotify_;
      }
    }
    /**
     * <code>optional .xddq.pb.ManHuangTeamMemberNotify teamMemberNotify = 19;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.ManHuangTeamMemberNotify, xddq.pb.ManHuangTeamMemberNotify.Builder, xddq.pb.ManHuangTeamMemberNotifyOrBuilder> 
        internalGetTeamMemberNotifyFieldBuilder() {
      if (teamMemberNotifyBuilder_ == null) {
        teamMemberNotifyBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.ManHuangTeamMemberNotify, xddq.pb.ManHuangTeamMemberNotify.Builder, xddq.pb.ManHuangTeamMemberNotifyOrBuilder>(
                getTeamMemberNotify(),
                getParentForChildren(),
                isClean());
        teamMemberNotify_ = null;
      }
      return teamMemberNotifyBuilder_;
    }

    private java.util.List<xddq.pb.ManHuangEnemyNotify> enemyNotify_ =
      java.util.Collections.emptyList();
    private void ensureEnemyNotifyIsMutable() {
      if (!((bitField0_ & 0x00080000) != 0)) {
        enemyNotify_ = new java.util.ArrayList<xddq.pb.ManHuangEnemyNotify>(enemyNotify_);
        bitField0_ |= 0x00080000;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.ManHuangEnemyNotify, xddq.pb.ManHuangEnemyNotify.Builder, xddq.pb.ManHuangEnemyNotifyOrBuilder> enemyNotifyBuilder_;

    /**
     * <code>repeated .xddq.pb.ManHuangEnemyNotify enemyNotify = 20;</code>
     */
    public java.util.List<xddq.pb.ManHuangEnemyNotify> getEnemyNotifyList() {
      if (enemyNotifyBuilder_ == null) {
        return java.util.Collections.unmodifiableList(enemyNotify_);
      } else {
        return enemyNotifyBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.ManHuangEnemyNotify enemyNotify = 20;</code>
     */
    public int getEnemyNotifyCount() {
      if (enemyNotifyBuilder_ == null) {
        return enemyNotify_.size();
      } else {
        return enemyNotifyBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.ManHuangEnemyNotify enemyNotify = 20;</code>
     */
    public xddq.pb.ManHuangEnemyNotify getEnemyNotify(int index) {
      if (enemyNotifyBuilder_ == null) {
        return enemyNotify_.get(index);
      } else {
        return enemyNotifyBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.ManHuangEnemyNotify enemyNotify = 20;</code>
     */
    public Builder setEnemyNotify(
        int index, xddq.pb.ManHuangEnemyNotify value) {
      if (enemyNotifyBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureEnemyNotifyIsMutable();
        enemyNotify_.set(index, value);
        onChanged();
      } else {
        enemyNotifyBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangEnemyNotify enemyNotify = 20;</code>
     */
    public Builder setEnemyNotify(
        int index, xddq.pb.ManHuangEnemyNotify.Builder builderForValue) {
      if (enemyNotifyBuilder_ == null) {
        ensureEnemyNotifyIsMutable();
        enemyNotify_.set(index, builderForValue.build());
        onChanged();
      } else {
        enemyNotifyBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangEnemyNotify enemyNotify = 20;</code>
     */
    public Builder addEnemyNotify(xddq.pb.ManHuangEnemyNotify value) {
      if (enemyNotifyBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureEnemyNotifyIsMutable();
        enemyNotify_.add(value);
        onChanged();
      } else {
        enemyNotifyBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangEnemyNotify enemyNotify = 20;</code>
     */
    public Builder addEnemyNotify(
        int index, xddq.pb.ManHuangEnemyNotify value) {
      if (enemyNotifyBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureEnemyNotifyIsMutable();
        enemyNotify_.add(index, value);
        onChanged();
      } else {
        enemyNotifyBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangEnemyNotify enemyNotify = 20;</code>
     */
    public Builder addEnemyNotify(
        xddq.pb.ManHuangEnemyNotify.Builder builderForValue) {
      if (enemyNotifyBuilder_ == null) {
        ensureEnemyNotifyIsMutable();
        enemyNotify_.add(builderForValue.build());
        onChanged();
      } else {
        enemyNotifyBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangEnemyNotify enemyNotify = 20;</code>
     */
    public Builder addEnemyNotify(
        int index, xddq.pb.ManHuangEnemyNotify.Builder builderForValue) {
      if (enemyNotifyBuilder_ == null) {
        ensureEnemyNotifyIsMutable();
        enemyNotify_.add(index, builderForValue.build());
        onChanged();
      } else {
        enemyNotifyBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangEnemyNotify enemyNotify = 20;</code>
     */
    public Builder addAllEnemyNotify(
        java.lang.Iterable<? extends xddq.pb.ManHuangEnemyNotify> values) {
      if (enemyNotifyBuilder_ == null) {
        ensureEnemyNotifyIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, enemyNotify_);
        onChanged();
      } else {
        enemyNotifyBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangEnemyNotify enemyNotify = 20;</code>
     */
    public Builder clearEnemyNotify() {
      if (enemyNotifyBuilder_ == null) {
        enemyNotify_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00080000);
        onChanged();
      } else {
        enemyNotifyBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangEnemyNotify enemyNotify = 20;</code>
     */
    public Builder removeEnemyNotify(int index) {
      if (enemyNotifyBuilder_ == null) {
        ensureEnemyNotifyIsMutable();
        enemyNotify_.remove(index);
        onChanged();
      } else {
        enemyNotifyBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangEnemyNotify enemyNotify = 20;</code>
     */
    public xddq.pb.ManHuangEnemyNotify.Builder getEnemyNotifyBuilder(
        int index) {
      return internalGetEnemyNotifyFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.ManHuangEnemyNotify enemyNotify = 20;</code>
     */
    public xddq.pb.ManHuangEnemyNotifyOrBuilder getEnemyNotifyOrBuilder(
        int index) {
      if (enemyNotifyBuilder_ == null) {
        return enemyNotify_.get(index);  } else {
        return enemyNotifyBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.ManHuangEnemyNotify enemyNotify = 20;</code>
     */
    public java.util.List<? extends xddq.pb.ManHuangEnemyNotifyOrBuilder> 
         getEnemyNotifyOrBuilderList() {
      if (enemyNotifyBuilder_ != null) {
        return enemyNotifyBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(enemyNotify_);
      }
    }
    /**
     * <code>repeated .xddq.pb.ManHuangEnemyNotify enemyNotify = 20;</code>
     */
    public xddq.pb.ManHuangEnemyNotify.Builder addEnemyNotifyBuilder() {
      return internalGetEnemyNotifyFieldBuilder().addBuilder(
          xddq.pb.ManHuangEnemyNotify.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.ManHuangEnemyNotify enemyNotify = 20;</code>
     */
    public xddq.pb.ManHuangEnemyNotify.Builder addEnemyNotifyBuilder(
        int index) {
      return internalGetEnemyNotifyFieldBuilder().addBuilder(
          index, xddq.pb.ManHuangEnemyNotify.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.ManHuangEnemyNotify enemyNotify = 20;</code>
     */
    public java.util.List<xddq.pb.ManHuangEnemyNotify.Builder> 
         getEnemyNotifyBuilderList() {
      return internalGetEnemyNotifyFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.ManHuangEnemyNotify, xddq.pb.ManHuangEnemyNotify.Builder, xddq.pb.ManHuangEnemyNotifyOrBuilder> 
        internalGetEnemyNotifyFieldBuilder() {
      if (enemyNotifyBuilder_ == null) {
        enemyNotifyBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.ManHuangEnemyNotify, xddq.pb.ManHuangEnemyNotify.Builder, xddq.pb.ManHuangEnemyNotifyOrBuilder>(
                enemyNotify_,
                ((bitField0_ & 0x00080000) != 0),
                getParentForChildren(),
                isClean());
        enemyNotify_ = null;
      }
      return enemyNotifyBuilder_;
    }

    private java.util.List<xddq.pb.ManHuangUserItemInfoTemp> monsterBall_ =
      java.util.Collections.emptyList();
    private void ensureMonsterBallIsMutable() {
      if (!((bitField0_ & 0x00100000) != 0)) {
        monsterBall_ = new java.util.ArrayList<xddq.pb.ManHuangUserItemInfoTemp>(monsterBall_);
        bitField0_ |= 0x00100000;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.ManHuangUserItemInfoTemp, xddq.pb.ManHuangUserItemInfoTemp.Builder, xddq.pb.ManHuangUserItemInfoTempOrBuilder> monsterBallBuilder_;

    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp monsterBall = 21;</code>
     */
    public java.util.List<xddq.pb.ManHuangUserItemInfoTemp> getMonsterBallList() {
      if (monsterBallBuilder_ == null) {
        return java.util.Collections.unmodifiableList(monsterBall_);
      } else {
        return monsterBallBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp monsterBall = 21;</code>
     */
    public int getMonsterBallCount() {
      if (monsterBallBuilder_ == null) {
        return monsterBall_.size();
      } else {
        return monsterBallBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp monsterBall = 21;</code>
     */
    public xddq.pb.ManHuangUserItemInfoTemp getMonsterBall(int index) {
      if (monsterBallBuilder_ == null) {
        return monsterBall_.get(index);
      } else {
        return monsterBallBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp monsterBall = 21;</code>
     */
    public Builder setMonsterBall(
        int index, xddq.pb.ManHuangUserItemInfoTemp value) {
      if (monsterBallBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMonsterBallIsMutable();
        monsterBall_.set(index, value);
        onChanged();
      } else {
        monsterBallBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp monsterBall = 21;</code>
     */
    public Builder setMonsterBall(
        int index, xddq.pb.ManHuangUserItemInfoTemp.Builder builderForValue) {
      if (monsterBallBuilder_ == null) {
        ensureMonsterBallIsMutable();
        monsterBall_.set(index, builderForValue.build());
        onChanged();
      } else {
        monsterBallBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp monsterBall = 21;</code>
     */
    public Builder addMonsterBall(xddq.pb.ManHuangUserItemInfoTemp value) {
      if (monsterBallBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMonsterBallIsMutable();
        monsterBall_.add(value);
        onChanged();
      } else {
        monsterBallBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp monsterBall = 21;</code>
     */
    public Builder addMonsterBall(
        int index, xddq.pb.ManHuangUserItemInfoTemp value) {
      if (monsterBallBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMonsterBallIsMutable();
        monsterBall_.add(index, value);
        onChanged();
      } else {
        monsterBallBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp monsterBall = 21;</code>
     */
    public Builder addMonsterBall(
        xddq.pb.ManHuangUserItemInfoTemp.Builder builderForValue) {
      if (monsterBallBuilder_ == null) {
        ensureMonsterBallIsMutable();
        monsterBall_.add(builderForValue.build());
        onChanged();
      } else {
        monsterBallBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp monsterBall = 21;</code>
     */
    public Builder addMonsterBall(
        int index, xddq.pb.ManHuangUserItemInfoTemp.Builder builderForValue) {
      if (monsterBallBuilder_ == null) {
        ensureMonsterBallIsMutable();
        monsterBall_.add(index, builderForValue.build());
        onChanged();
      } else {
        monsterBallBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp monsterBall = 21;</code>
     */
    public Builder addAllMonsterBall(
        java.lang.Iterable<? extends xddq.pb.ManHuangUserItemInfoTemp> values) {
      if (monsterBallBuilder_ == null) {
        ensureMonsterBallIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, monsterBall_);
        onChanged();
      } else {
        monsterBallBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp monsterBall = 21;</code>
     */
    public Builder clearMonsterBall() {
      if (monsterBallBuilder_ == null) {
        monsterBall_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00100000);
        onChanged();
      } else {
        monsterBallBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp monsterBall = 21;</code>
     */
    public Builder removeMonsterBall(int index) {
      if (monsterBallBuilder_ == null) {
        ensureMonsterBallIsMutable();
        monsterBall_.remove(index);
        onChanged();
      } else {
        monsterBallBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp monsterBall = 21;</code>
     */
    public xddq.pb.ManHuangUserItemInfoTemp.Builder getMonsterBallBuilder(
        int index) {
      return internalGetMonsterBallFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp monsterBall = 21;</code>
     */
    public xddq.pb.ManHuangUserItemInfoTempOrBuilder getMonsterBallOrBuilder(
        int index) {
      if (monsterBallBuilder_ == null) {
        return monsterBall_.get(index);  } else {
        return monsterBallBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp monsterBall = 21;</code>
     */
    public java.util.List<? extends xddq.pb.ManHuangUserItemInfoTempOrBuilder> 
         getMonsterBallOrBuilderList() {
      if (monsterBallBuilder_ != null) {
        return monsterBallBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(monsterBall_);
      }
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp monsterBall = 21;</code>
     */
    public xddq.pb.ManHuangUserItemInfoTemp.Builder addMonsterBallBuilder() {
      return internalGetMonsterBallFieldBuilder().addBuilder(
          xddq.pb.ManHuangUserItemInfoTemp.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp monsterBall = 21;</code>
     */
    public xddq.pb.ManHuangUserItemInfoTemp.Builder addMonsterBallBuilder(
        int index) {
      return internalGetMonsterBallFieldBuilder().addBuilder(
          index, xddq.pb.ManHuangUserItemInfoTemp.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp monsterBall = 21;</code>
     */
    public java.util.List<xddq.pb.ManHuangUserItemInfoTemp.Builder> 
         getMonsterBallBuilderList() {
      return internalGetMonsterBallFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.ManHuangUserItemInfoTemp, xddq.pb.ManHuangUserItemInfoTemp.Builder, xddq.pb.ManHuangUserItemInfoTempOrBuilder> 
        internalGetMonsterBallFieldBuilder() {
      if (monsterBallBuilder_ == null) {
        monsterBallBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.ManHuangUserItemInfoTemp, xddq.pb.ManHuangUserItemInfoTemp.Builder, xddq.pb.ManHuangUserItemInfoTempOrBuilder>(
                monsterBall_,
                ((bitField0_ & 0x00100000) != 0),
                getParentForChildren(),
                isClean());
        monsterBall_ = null;
      }
      return monsterBallBuilder_;
    }

    private java.util.List<xddq.pb.ManHuangUserItemInfoTemp> grass_ =
      java.util.Collections.emptyList();
    private void ensureGrassIsMutable() {
      if (!((bitField0_ & 0x00200000) != 0)) {
        grass_ = new java.util.ArrayList<xddq.pb.ManHuangUserItemInfoTemp>(grass_);
        bitField0_ |= 0x00200000;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.ManHuangUserItemInfoTemp, xddq.pb.ManHuangUserItemInfoTemp.Builder, xddq.pb.ManHuangUserItemInfoTempOrBuilder> grassBuilder_;

    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp grass = 22;</code>
     */
    public java.util.List<xddq.pb.ManHuangUserItemInfoTemp> getGrassList() {
      if (grassBuilder_ == null) {
        return java.util.Collections.unmodifiableList(grass_);
      } else {
        return grassBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp grass = 22;</code>
     */
    public int getGrassCount() {
      if (grassBuilder_ == null) {
        return grass_.size();
      } else {
        return grassBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp grass = 22;</code>
     */
    public xddq.pb.ManHuangUserItemInfoTemp getGrass(int index) {
      if (grassBuilder_ == null) {
        return grass_.get(index);
      } else {
        return grassBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp grass = 22;</code>
     */
    public Builder setGrass(
        int index, xddq.pb.ManHuangUserItemInfoTemp value) {
      if (grassBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureGrassIsMutable();
        grass_.set(index, value);
        onChanged();
      } else {
        grassBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp grass = 22;</code>
     */
    public Builder setGrass(
        int index, xddq.pb.ManHuangUserItemInfoTemp.Builder builderForValue) {
      if (grassBuilder_ == null) {
        ensureGrassIsMutable();
        grass_.set(index, builderForValue.build());
        onChanged();
      } else {
        grassBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp grass = 22;</code>
     */
    public Builder addGrass(xddq.pb.ManHuangUserItemInfoTemp value) {
      if (grassBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureGrassIsMutable();
        grass_.add(value);
        onChanged();
      } else {
        grassBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp grass = 22;</code>
     */
    public Builder addGrass(
        int index, xddq.pb.ManHuangUserItemInfoTemp value) {
      if (grassBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureGrassIsMutable();
        grass_.add(index, value);
        onChanged();
      } else {
        grassBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp grass = 22;</code>
     */
    public Builder addGrass(
        xddq.pb.ManHuangUserItemInfoTemp.Builder builderForValue) {
      if (grassBuilder_ == null) {
        ensureGrassIsMutable();
        grass_.add(builderForValue.build());
        onChanged();
      } else {
        grassBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp grass = 22;</code>
     */
    public Builder addGrass(
        int index, xddq.pb.ManHuangUserItemInfoTemp.Builder builderForValue) {
      if (grassBuilder_ == null) {
        ensureGrassIsMutable();
        grass_.add(index, builderForValue.build());
        onChanged();
      } else {
        grassBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp grass = 22;</code>
     */
    public Builder addAllGrass(
        java.lang.Iterable<? extends xddq.pb.ManHuangUserItemInfoTemp> values) {
      if (grassBuilder_ == null) {
        ensureGrassIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, grass_);
        onChanged();
      } else {
        grassBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp grass = 22;</code>
     */
    public Builder clearGrass() {
      if (grassBuilder_ == null) {
        grass_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00200000);
        onChanged();
      } else {
        grassBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp grass = 22;</code>
     */
    public Builder removeGrass(int index) {
      if (grassBuilder_ == null) {
        ensureGrassIsMutable();
        grass_.remove(index);
        onChanged();
      } else {
        grassBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp grass = 22;</code>
     */
    public xddq.pb.ManHuangUserItemInfoTemp.Builder getGrassBuilder(
        int index) {
      return internalGetGrassFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp grass = 22;</code>
     */
    public xddq.pb.ManHuangUserItemInfoTempOrBuilder getGrassOrBuilder(
        int index) {
      if (grassBuilder_ == null) {
        return grass_.get(index);  } else {
        return grassBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp grass = 22;</code>
     */
    public java.util.List<? extends xddq.pb.ManHuangUserItemInfoTempOrBuilder> 
         getGrassOrBuilderList() {
      if (grassBuilder_ != null) {
        return grassBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(grass_);
      }
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp grass = 22;</code>
     */
    public xddq.pb.ManHuangUserItemInfoTemp.Builder addGrassBuilder() {
      return internalGetGrassFieldBuilder().addBuilder(
          xddq.pb.ManHuangUserItemInfoTemp.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp grass = 22;</code>
     */
    public xddq.pb.ManHuangUserItemInfoTemp.Builder addGrassBuilder(
        int index) {
      return internalGetGrassFieldBuilder().addBuilder(
          index, xddq.pb.ManHuangUserItemInfoTemp.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp grass = 22;</code>
     */
    public java.util.List<xddq.pb.ManHuangUserItemInfoTemp.Builder> 
         getGrassBuilderList() {
      return internalGetGrassFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.ManHuangUserItemInfoTemp, xddq.pb.ManHuangUserItemInfoTemp.Builder, xddq.pb.ManHuangUserItemInfoTempOrBuilder> 
        internalGetGrassFieldBuilder() {
      if (grassBuilder_ == null) {
        grassBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.ManHuangUserItemInfoTemp, xddq.pb.ManHuangUserItemInfoTemp.Builder, xddq.pb.ManHuangUserItemInfoTempOrBuilder>(
                grass_,
                ((bitField0_ & 0x00200000) != 0),
                getParentForChildren(),
                isClean());
        grass_ = null;
      }
      return grassBuilder_;
    }

    private java.util.List<xddq.pb.ManHuangUserItemInfoTemp> box_ =
      java.util.Collections.emptyList();
    private void ensureBoxIsMutable() {
      if (!((bitField0_ & 0x00400000) != 0)) {
        box_ = new java.util.ArrayList<xddq.pb.ManHuangUserItemInfoTemp>(box_);
        bitField0_ |= 0x00400000;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.ManHuangUserItemInfoTemp, xddq.pb.ManHuangUserItemInfoTemp.Builder, xddq.pb.ManHuangUserItemInfoTempOrBuilder> boxBuilder_;

    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp box = 23;</code>
     */
    public java.util.List<xddq.pb.ManHuangUserItemInfoTemp> getBoxList() {
      if (boxBuilder_ == null) {
        return java.util.Collections.unmodifiableList(box_);
      } else {
        return boxBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp box = 23;</code>
     */
    public int getBoxCount() {
      if (boxBuilder_ == null) {
        return box_.size();
      } else {
        return boxBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp box = 23;</code>
     */
    public xddq.pb.ManHuangUserItemInfoTemp getBox(int index) {
      if (boxBuilder_ == null) {
        return box_.get(index);
      } else {
        return boxBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp box = 23;</code>
     */
    public Builder setBox(
        int index, xddq.pb.ManHuangUserItemInfoTemp value) {
      if (boxBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureBoxIsMutable();
        box_.set(index, value);
        onChanged();
      } else {
        boxBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp box = 23;</code>
     */
    public Builder setBox(
        int index, xddq.pb.ManHuangUserItemInfoTemp.Builder builderForValue) {
      if (boxBuilder_ == null) {
        ensureBoxIsMutable();
        box_.set(index, builderForValue.build());
        onChanged();
      } else {
        boxBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp box = 23;</code>
     */
    public Builder addBox(xddq.pb.ManHuangUserItemInfoTemp value) {
      if (boxBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureBoxIsMutable();
        box_.add(value);
        onChanged();
      } else {
        boxBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp box = 23;</code>
     */
    public Builder addBox(
        int index, xddq.pb.ManHuangUserItemInfoTemp value) {
      if (boxBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureBoxIsMutable();
        box_.add(index, value);
        onChanged();
      } else {
        boxBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp box = 23;</code>
     */
    public Builder addBox(
        xddq.pb.ManHuangUserItemInfoTemp.Builder builderForValue) {
      if (boxBuilder_ == null) {
        ensureBoxIsMutable();
        box_.add(builderForValue.build());
        onChanged();
      } else {
        boxBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp box = 23;</code>
     */
    public Builder addBox(
        int index, xddq.pb.ManHuangUserItemInfoTemp.Builder builderForValue) {
      if (boxBuilder_ == null) {
        ensureBoxIsMutable();
        box_.add(index, builderForValue.build());
        onChanged();
      } else {
        boxBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp box = 23;</code>
     */
    public Builder addAllBox(
        java.lang.Iterable<? extends xddq.pb.ManHuangUserItemInfoTemp> values) {
      if (boxBuilder_ == null) {
        ensureBoxIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, box_);
        onChanged();
      } else {
        boxBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp box = 23;</code>
     */
    public Builder clearBox() {
      if (boxBuilder_ == null) {
        box_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00400000);
        onChanged();
      } else {
        boxBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp box = 23;</code>
     */
    public Builder removeBox(int index) {
      if (boxBuilder_ == null) {
        ensureBoxIsMutable();
        box_.remove(index);
        onChanged();
      } else {
        boxBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp box = 23;</code>
     */
    public xddq.pb.ManHuangUserItemInfoTemp.Builder getBoxBuilder(
        int index) {
      return internalGetBoxFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp box = 23;</code>
     */
    public xddq.pb.ManHuangUserItemInfoTempOrBuilder getBoxOrBuilder(
        int index) {
      if (boxBuilder_ == null) {
        return box_.get(index);  } else {
        return boxBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp box = 23;</code>
     */
    public java.util.List<? extends xddq.pb.ManHuangUserItemInfoTempOrBuilder> 
         getBoxOrBuilderList() {
      if (boxBuilder_ != null) {
        return boxBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(box_);
      }
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp box = 23;</code>
     */
    public xddq.pb.ManHuangUserItemInfoTemp.Builder addBoxBuilder() {
      return internalGetBoxFieldBuilder().addBuilder(
          xddq.pb.ManHuangUserItemInfoTemp.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp box = 23;</code>
     */
    public xddq.pb.ManHuangUserItemInfoTemp.Builder addBoxBuilder(
        int index) {
      return internalGetBoxFieldBuilder().addBuilder(
          index, xddq.pb.ManHuangUserItemInfoTemp.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp box = 23;</code>
     */
    public java.util.List<xddq.pb.ManHuangUserItemInfoTemp.Builder> 
         getBoxBuilderList() {
      return internalGetBoxFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.ManHuangUserItemInfoTemp, xddq.pb.ManHuangUserItemInfoTemp.Builder, xddq.pb.ManHuangUserItemInfoTempOrBuilder> 
        internalGetBoxFieldBuilder() {
      if (boxBuilder_ == null) {
        boxBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.ManHuangUserItemInfoTemp, xddq.pb.ManHuangUserItemInfoTemp.Builder, xddq.pb.ManHuangUserItemInfoTempOrBuilder>(
                box_,
                ((bitField0_ & 0x00400000) != 0),
                getParentForChildren(),
                isClean());
        box_ = null;
      }
      return boxBuilder_;
    }

    private java.util.List<xddq.pb.ManHuangUserItemInfoTemp> eventDone_ =
      java.util.Collections.emptyList();
    private void ensureEventDoneIsMutable() {
      if (!((bitField0_ & 0x00800000) != 0)) {
        eventDone_ = new java.util.ArrayList<xddq.pb.ManHuangUserItemInfoTemp>(eventDone_);
        bitField0_ |= 0x00800000;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.ManHuangUserItemInfoTemp, xddq.pb.ManHuangUserItemInfoTemp.Builder, xddq.pb.ManHuangUserItemInfoTempOrBuilder> eventDoneBuilder_;

    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp eventDone = 24;</code>
     */
    public java.util.List<xddq.pb.ManHuangUserItemInfoTemp> getEventDoneList() {
      if (eventDoneBuilder_ == null) {
        return java.util.Collections.unmodifiableList(eventDone_);
      } else {
        return eventDoneBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp eventDone = 24;</code>
     */
    public int getEventDoneCount() {
      if (eventDoneBuilder_ == null) {
        return eventDone_.size();
      } else {
        return eventDoneBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp eventDone = 24;</code>
     */
    public xddq.pb.ManHuangUserItemInfoTemp getEventDone(int index) {
      if (eventDoneBuilder_ == null) {
        return eventDone_.get(index);
      } else {
        return eventDoneBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp eventDone = 24;</code>
     */
    public Builder setEventDone(
        int index, xddq.pb.ManHuangUserItemInfoTemp value) {
      if (eventDoneBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureEventDoneIsMutable();
        eventDone_.set(index, value);
        onChanged();
      } else {
        eventDoneBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp eventDone = 24;</code>
     */
    public Builder setEventDone(
        int index, xddq.pb.ManHuangUserItemInfoTemp.Builder builderForValue) {
      if (eventDoneBuilder_ == null) {
        ensureEventDoneIsMutable();
        eventDone_.set(index, builderForValue.build());
        onChanged();
      } else {
        eventDoneBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp eventDone = 24;</code>
     */
    public Builder addEventDone(xddq.pb.ManHuangUserItemInfoTemp value) {
      if (eventDoneBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureEventDoneIsMutable();
        eventDone_.add(value);
        onChanged();
      } else {
        eventDoneBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp eventDone = 24;</code>
     */
    public Builder addEventDone(
        int index, xddq.pb.ManHuangUserItemInfoTemp value) {
      if (eventDoneBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureEventDoneIsMutable();
        eventDone_.add(index, value);
        onChanged();
      } else {
        eventDoneBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp eventDone = 24;</code>
     */
    public Builder addEventDone(
        xddq.pb.ManHuangUserItemInfoTemp.Builder builderForValue) {
      if (eventDoneBuilder_ == null) {
        ensureEventDoneIsMutable();
        eventDone_.add(builderForValue.build());
        onChanged();
      } else {
        eventDoneBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp eventDone = 24;</code>
     */
    public Builder addEventDone(
        int index, xddq.pb.ManHuangUserItemInfoTemp.Builder builderForValue) {
      if (eventDoneBuilder_ == null) {
        ensureEventDoneIsMutable();
        eventDone_.add(index, builderForValue.build());
        onChanged();
      } else {
        eventDoneBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp eventDone = 24;</code>
     */
    public Builder addAllEventDone(
        java.lang.Iterable<? extends xddq.pb.ManHuangUserItemInfoTemp> values) {
      if (eventDoneBuilder_ == null) {
        ensureEventDoneIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, eventDone_);
        onChanged();
      } else {
        eventDoneBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp eventDone = 24;</code>
     */
    public Builder clearEventDone() {
      if (eventDoneBuilder_ == null) {
        eventDone_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00800000);
        onChanged();
      } else {
        eventDoneBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp eventDone = 24;</code>
     */
    public Builder removeEventDone(int index) {
      if (eventDoneBuilder_ == null) {
        ensureEventDoneIsMutable();
        eventDone_.remove(index);
        onChanged();
      } else {
        eventDoneBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp eventDone = 24;</code>
     */
    public xddq.pb.ManHuangUserItemInfoTemp.Builder getEventDoneBuilder(
        int index) {
      return internalGetEventDoneFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp eventDone = 24;</code>
     */
    public xddq.pb.ManHuangUserItemInfoTempOrBuilder getEventDoneOrBuilder(
        int index) {
      if (eventDoneBuilder_ == null) {
        return eventDone_.get(index);  } else {
        return eventDoneBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp eventDone = 24;</code>
     */
    public java.util.List<? extends xddq.pb.ManHuangUserItemInfoTempOrBuilder> 
         getEventDoneOrBuilderList() {
      if (eventDoneBuilder_ != null) {
        return eventDoneBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(eventDone_);
      }
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp eventDone = 24;</code>
     */
    public xddq.pb.ManHuangUserItemInfoTemp.Builder addEventDoneBuilder() {
      return internalGetEventDoneFieldBuilder().addBuilder(
          xddq.pb.ManHuangUserItemInfoTemp.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp eventDone = 24;</code>
     */
    public xddq.pb.ManHuangUserItemInfoTemp.Builder addEventDoneBuilder(
        int index) {
      return internalGetEventDoneFieldBuilder().addBuilder(
          index, xddq.pb.ManHuangUserItemInfoTemp.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.ManHuangUserItemInfoTemp eventDone = 24;</code>
     */
    public java.util.List<xddq.pb.ManHuangUserItemInfoTemp.Builder> 
         getEventDoneBuilderList() {
      return internalGetEventDoneFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.ManHuangUserItemInfoTemp, xddq.pb.ManHuangUserItemInfoTemp.Builder, xddq.pb.ManHuangUserItemInfoTempOrBuilder> 
        internalGetEventDoneFieldBuilder() {
      if (eventDoneBuilder_ == null) {
        eventDoneBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.ManHuangUserItemInfoTemp, xddq.pb.ManHuangUserItemInfoTemp.Builder, xddq.pb.ManHuangUserItemInfoTempOrBuilder>(
                eventDone_,
                ((bitField0_ & 0x00800000) != 0),
                getParentForChildren(),
                isClean());
        eventDone_ = null;
      }
      return eventDoneBuilder_;
    }

    private xddq.pb.ManHuangEventDoingInfoEntity eventDoingInfo_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.ManHuangEventDoingInfoEntity, xddq.pb.ManHuangEventDoingInfoEntity.Builder, xddq.pb.ManHuangEventDoingInfoEntityOrBuilder> eventDoingInfoBuilder_;
    /**
     * <code>optional .xddq.pb.ManHuangEventDoingInfoEntity eventDoingInfo = 25;</code>
     * @return Whether the eventDoingInfo field is set.
     */
    public boolean hasEventDoingInfo() {
      return ((bitField0_ & 0x01000000) != 0);
    }
    /**
     * <code>optional .xddq.pb.ManHuangEventDoingInfoEntity eventDoingInfo = 25;</code>
     * @return The eventDoingInfo.
     */
    public xddq.pb.ManHuangEventDoingInfoEntity getEventDoingInfo() {
      if (eventDoingInfoBuilder_ == null) {
        return eventDoingInfo_ == null ? xddq.pb.ManHuangEventDoingInfoEntity.getDefaultInstance() : eventDoingInfo_;
      } else {
        return eventDoingInfoBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.ManHuangEventDoingInfoEntity eventDoingInfo = 25;</code>
     */
    public Builder setEventDoingInfo(xddq.pb.ManHuangEventDoingInfoEntity value) {
      if (eventDoingInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        eventDoingInfo_ = value;
      } else {
        eventDoingInfoBuilder_.setMessage(value);
      }
      bitField0_ |= 0x01000000;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ManHuangEventDoingInfoEntity eventDoingInfo = 25;</code>
     */
    public Builder setEventDoingInfo(
        xddq.pb.ManHuangEventDoingInfoEntity.Builder builderForValue) {
      if (eventDoingInfoBuilder_ == null) {
        eventDoingInfo_ = builderForValue.build();
      } else {
        eventDoingInfoBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x01000000;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ManHuangEventDoingInfoEntity eventDoingInfo = 25;</code>
     */
    public Builder mergeEventDoingInfo(xddq.pb.ManHuangEventDoingInfoEntity value) {
      if (eventDoingInfoBuilder_ == null) {
        if (((bitField0_ & 0x01000000) != 0) &&
          eventDoingInfo_ != null &&
          eventDoingInfo_ != xddq.pb.ManHuangEventDoingInfoEntity.getDefaultInstance()) {
          getEventDoingInfoBuilder().mergeFrom(value);
        } else {
          eventDoingInfo_ = value;
        }
      } else {
        eventDoingInfoBuilder_.mergeFrom(value);
      }
      if (eventDoingInfo_ != null) {
        bitField0_ |= 0x01000000;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.ManHuangEventDoingInfoEntity eventDoingInfo = 25;</code>
     */
    public Builder clearEventDoingInfo() {
      bitField0_ = (bitField0_ & ~0x01000000);
      eventDoingInfo_ = null;
      if (eventDoingInfoBuilder_ != null) {
        eventDoingInfoBuilder_.dispose();
        eventDoingInfoBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ManHuangEventDoingInfoEntity eventDoingInfo = 25;</code>
     */
    public xddq.pb.ManHuangEventDoingInfoEntity.Builder getEventDoingInfoBuilder() {
      bitField0_ |= 0x01000000;
      onChanged();
      return internalGetEventDoingInfoFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.ManHuangEventDoingInfoEntity eventDoingInfo = 25;</code>
     */
    public xddq.pb.ManHuangEventDoingInfoEntityOrBuilder getEventDoingInfoOrBuilder() {
      if (eventDoingInfoBuilder_ != null) {
        return eventDoingInfoBuilder_.getMessageOrBuilder();
      } else {
        return eventDoingInfo_ == null ?
            xddq.pb.ManHuangEventDoingInfoEntity.getDefaultInstance() : eventDoingInfo_;
      }
    }
    /**
     * <code>optional .xddq.pb.ManHuangEventDoingInfoEntity eventDoingInfo = 25;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.ManHuangEventDoingInfoEntity, xddq.pb.ManHuangEventDoingInfoEntity.Builder, xddq.pb.ManHuangEventDoingInfoEntityOrBuilder> 
        internalGetEventDoingInfoFieldBuilder() {
      if (eventDoingInfoBuilder_ == null) {
        eventDoingInfoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.ManHuangEventDoingInfoEntity, xddq.pb.ManHuangEventDoingInfoEntity.Builder, xddq.pb.ManHuangEventDoingInfoEntityOrBuilder>(
                getEventDoingInfo(),
                getParentForChildren(),
                isClean());
        eventDoingInfo_ = null;
      }
      return eventDoingInfoBuilder_;
    }

    private xddq.pb.ManHuangEventDoingInfoEntity signInfo_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.ManHuangEventDoingInfoEntity, xddq.pb.ManHuangEventDoingInfoEntity.Builder, xddq.pb.ManHuangEventDoingInfoEntityOrBuilder> signInfoBuilder_;
    /**
     * <code>optional .xddq.pb.ManHuangEventDoingInfoEntity signInfo = 26;</code>
     * @return Whether the signInfo field is set.
     */
    public boolean hasSignInfo() {
      return ((bitField0_ & 0x02000000) != 0);
    }
    /**
     * <code>optional .xddq.pb.ManHuangEventDoingInfoEntity signInfo = 26;</code>
     * @return The signInfo.
     */
    public xddq.pb.ManHuangEventDoingInfoEntity getSignInfo() {
      if (signInfoBuilder_ == null) {
        return signInfo_ == null ? xddq.pb.ManHuangEventDoingInfoEntity.getDefaultInstance() : signInfo_;
      } else {
        return signInfoBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.ManHuangEventDoingInfoEntity signInfo = 26;</code>
     */
    public Builder setSignInfo(xddq.pb.ManHuangEventDoingInfoEntity value) {
      if (signInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        signInfo_ = value;
      } else {
        signInfoBuilder_.setMessage(value);
      }
      bitField0_ |= 0x02000000;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ManHuangEventDoingInfoEntity signInfo = 26;</code>
     */
    public Builder setSignInfo(
        xddq.pb.ManHuangEventDoingInfoEntity.Builder builderForValue) {
      if (signInfoBuilder_ == null) {
        signInfo_ = builderForValue.build();
      } else {
        signInfoBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x02000000;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ManHuangEventDoingInfoEntity signInfo = 26;</code>
     */
    public Builder mergeSignInfo(xddq.pb.ManHuangEventDoingInfoEntity value) {
      if (signInfoBuilder_ == null) {
        if (((bitField0_ & 0x02000000) != 0) &&
          signInfo_ != null &&
          signInfo_ != xddq.pb.ManHuangEventDoingInfoEntity.getDefaultInstance()) {
          getSignInfoBuilder().mergeFrom(value);
        } else {
          signInfo_ = value;
        }
      } else {
        signInfoBuilder_.mergeFrom(value);
      }
      if (signInfo_ != null) {
        bitField0_ |= 0x02000000;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.ManHuangEventDoingInfoEntity signInfo = 26;</code>
     */
    public Builder clearSignInfo() {
      bitField0_ = (bitField0_ & ~0x02000000);
      signInfo_ = null;
      if (signInfoBuilder_ != null) {
        signInfoBuilder_.dispose();
        signInfoBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ManHuangEventDoingInfoEntity signInfo = 26;</code>
     */
    public xddq.pb.ManHuangEventDoingInfoEntity.Builder getSignInfoBuilder() {
      bitField0_ |= 0x02000000;
      onChanged();
      return internalGetSignInfoFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.ManHuangEventDoingInfoEntity signInfo = 26;</code>
     */
    public xddq.pb.ManHuangEventDoingInfoEntityOrBuilder getSignInfoOrBuilder() {
      if (signInfoBuilder_ != null) {
        return signInfoBuilder_.getMessageOrBuilder();
      } else {
        return signInfo_ == null ?
            xddq.pb.ManHuangEventDoingInfoEntity.getDefaultInstance() : signInfo_;
      }
    }
    /**
     * <code>optional .xddq.pb.ManHuangEventDoingInfoEntity signInfo = 26;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.ManHuangEventDoingInfoEntity, xddq.pb.ManHuangEventDoingInfoEntity.Builder, xddq.pb.ManHuangEventDoingInfoEntityOrBuilder> 
        internalGetSignInfoFieldBuilder() {
      if (signInfoBuilder_ == null) {
        signInfoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.ManHuangEventDoingInfoEntity, xddq.pb.ManHuangEventDoingInfoEntity.Builder, xddq.pb.ManHuangEventDoingInfoEntityOrBuilder>(
                getSignInfo(),
                getParentForChildren(),
                isClean());
        signInfo_ = null;
      }
      return signInfoBuilder_;
    }

    private int iceSoul_ ;
    /**
     * <code>optional int32 iceSoul = 27;</code>
     * @return Whether the iceSoul field is set.
     */
    @java.lang.Override
    public boolean hasIceSoul() {
      return ((bitField0_ & 0x04000000) != 0);
    }
    /**
     * <code>optional int32 iceSoul = 27;</code>
     * @return The iceSoul.
     */
    @java.lang.Override
    public int getIceSoul() {
      return iceSoul_;
    }
    /**
     * <code>optional int32 iceSoul = 27;</code>
     * @param value The iceSoul to set.
     * @return This builder for chaining.
     */
    public Builder setIceSoul(int value) {

      iceSoul_ = value;
      bitField0_ |= 0x04000000;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 iceSoul = 27;</code>
     * @return This builder for chaining.
     */
    public Builder clearIceSoul() {
      bitField0_ = (bitField0_ & ~0x04000000);
      iceSoul_ = 0;
      onChanged();
      return this;
    }

    private xddq.pb.ManHuangUserWinInfo winInfo_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.ManHuangUserWinInfo, xddq.pb.ManHuangUserWinInfo.Builder, xddq.pb.ManHuangUserWinInfoOrBuilder> winInfoBuilder_;
    /**
     * <code>optional .xddq.pb.ManHuangUserWinInfo winInfo = 28;</code>
     * @return Whether the winInfo field is set.
     */
    public boolean hasWinInfo() {
      return ((bitField0_ & 0x08000000) != 0);
    }
    /**
     * <code>optional .xddq.pb.ManHuangUserWinInfo winInfo = 28;</code>
     * @return The winInfo.
     */
    public xddq.pb.ManHuangUserWinInfo getWinInfo() {
      if (winInfoBuilder_ == null) {
        return winInfo_ == null ? xddq.pb.ManHuangUserWinInfo.getDefaultInstance() : winInfo_;
      } else {
        return winInfoBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.ManHuangUserWinInfo winInfo = 28;</code>
     */
    public Builder setWinInfo(xddq.pb.ManHuangUserWinInfo value) {
      if (winInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        winInfo_ = value;
      } else {
        winInfoBuilder_.setMessage(value);
      }
      bitField0_ |= 0x08000000;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ManHuangUserWinInfo winInfo = 28;</code>
     */
    public Builder setWinInfo(
        xddq.pb.ManHuangUserWinInfo.Builder builderForValue) {
      if (winInfoBuilder_ == null) {
        winInfo_ = builderForValue.build();
      } else {
        winInfoBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x08000000;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ManHuangUserWinInfo winInfo = 28;</code>
     */
    public Builder mergeWinInfo(xddq.pb.ManHuangUserWinInfo value) {
      if (winInfoBuilder_ == null) {
        if (((bitField0_ & 0x08000000) != 0) &&
          winInfo_ != null &&
          winInfo_ != xddq.pb.ManHuangUserWinInfo.getDefaultInstance()) {
          getWinInfoBuilder().mergeFrom(value);
        } else {
          winInfo_ = value;
        }
      } else {
        winInfoBuilder_.mergeFrom(value);
      }
      if (winInfo_ != null) {
        bitField0_ |= 0x08000000;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.ManHuangUserWinInfo winInfo = 28;</code>
     */
    public Builder clearWinInfo() {
      bitField0_ = (bitField0_ & ~0x08000000);
      winInfo_ = null;
      if (winInfoBuilder_ != null) {
        winInfoBuilder_.dispose();
        winInfoBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ManHuangUserWinInfo winInfo = 28;</code>
     */
    public xddq.pb.ManHuangUserWinInfo.Builder getWinInfoBuilder() {
      bitField0_ |= 0x08000000;
      onChanged();
      return internalGetWinInfoFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.ManHuangUserWinInfo winInfo = 28;</code>
     */
    public xddq.pb.ManHuangUserWinInfoOrBuilder getWinInfoOrBuilder() {
      if (winInfoBuilder_ != null) {
        return winInfoBuilder_.getMessageOrBuilder();
      } else {
        return winInfo_ == null ?
            xddq.pb.ManHuangUserWinInfo.getDefaultInstance() : winInfo_;
      }
    }
    /**
     * <code>optional .xddq.pb.ManHuangUserWinInfo winInfo = 28;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.ManHuangUserWinInfo, xddq.pb.ManHuangUserWinInfo.Builder, xddq.pb.ManHuangUserWinInfoOrBuilder> 
        internalGetWinInfoFieldBuilder() {
      if (winInfoBuilder_ == null) {
        winInfoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.ManHuangUserWinInfo, xddq.pb.ManHuangUserWinInfo.Builder, xddq.pb.ManHuangUserWinInfoOrBuilder>(
                getWinInfo(),
                getParentForChildren(),
                isClean());
        winInfo_ = null;
      }
      return winInfoBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.ManHuangUserDataEntity)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.ManHuangUserDataEntity)
  private static final xddq.pb.ManHuangUserDataEntity DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.ManHuangUserDataEntity();
  }

  public static xddq.pb.ManHuangUserDataEntity getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ManHuangUserDataEntity>
      PARSER = new com.google.protobuf.AbstractParser<ManHuangUserDataEntity>() {
    @java.lang.Override
    public ManHuangUserDataEntity parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<ManHuangUserDataEntity> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ManHuangUserDataEntity> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.ManHuangUserDataEntity getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

