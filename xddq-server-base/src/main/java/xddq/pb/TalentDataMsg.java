// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.TalentDataMsg}
 */
public final class TalentDataMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.TalentDataMsg)
    TalentDataMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      TalentDataMsg.class.getName());
  }
  // Use TalentDataMsg.newBuilder() to construct.
  private TalentDataMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private TalentDataMsg() {
    attributeData_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_TalentDataMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_TalentDataMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.TalentDataMsg.class, xddq.pb.TalentDataMsg.Builder.class);
  }

  private int bitField0_;
  public static final int TYPE_FIELD_NUMBER = 1;
  private int type_ = 0;
  /**
   * <code>required int32 type = 1;</code>
   * @return Whether the type field is set.
   */
  @java.lang.Override
  public boolean hasType() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 type = 1;</code>
   * @return The type.
   */
  @java.lang.Override
  public int getType() {
    return type_;
  }

  public static final int TALENTID_FIELD_NUMBER = 2;
  private int talentId_ = 0;
  /**
   * <code>required int32 talentId = 2;</code>
   * @return Whether the talentId field is set.
   */
  @java.lang.Override
  public boolean hasTalentId() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>required int32 talentId = 2;</code>
   * @return The talentId.
   */
  @java.lang.Override
  public int getTalentId() {
    return talentId_;
  }

  public static final int LEVEL_FIELD_NUMBER = 3;
  private int level_ = 0;
  /**
   * <code>required int32 level = 3;</code>
   * @return Whether the level field is set.
   */
  @java.lang.Override
  public boolean hasLevel() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>required int32 level = 3;</code>
   * @return The level.
   */
  @java.lang.Override
  public int getLevel() {
    return level_;
  }

  public static final int QUALITY_FIELD_NUMBER = 4;
  private int quality_ = 0;
  /**
   * <code>required int32 quality = 4;</code>
   * @return Whether the quality field is set.
   */
  @java.lang.Override
  public boolean hasQuality() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>required int32 quality = 4;</code>
   * @return The quality.
   */
  @java.lang.Override
  public int getQuality() {
    return quality_;
  }

  public static final int ATTRIBUTEDATA_FIELD_NUMBER = 5;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.AttributeDataMsg> attributeData_;
  /**
   * <code>repeated .xddq.pb.AttributeDataMsg attributeData = 5;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.AttributeDataMsg> getAttributeDataList() {
    return attributeData_;
  }
  /**
   * <code>repeated .xddq.pb.AttributeDataMsg attributeData = 5;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.AttributeDataMsgOrBuilder> 
      getAttributeDataOrBuilderList() {
    return attributeData_;
  }
  /**
   * <code>repeated .xddq.pb.AttributeDataMsg attributeData = 5;</code>
   */
  @java.lang.Override
  public int getAttributeDataCount() {
    return attributeData_.size();
  }
  /**
   * <code>repeated .xddq.pb.AttributeDataMsg attributeData = 5;</code>
   */
  @java.lang.Override
  public xddq.pb.AttributeDataMsg getAttributeData(int index) {
    return attributeData_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.AttributeDataMsg attributeData = 5;</code>
   */
  @java.lang.Override
  public xddq.pb.AttributeDataMsgOrBuilder getAttributeDataOrBuilder(
      int index) {
    return attributeData_.get(index);
  }

  public static final int SKILLID_FIELD_NUMBER = 6;
  private int skillId_ = 0;
  /**
   * <code>optional int32 skillId = 6;</code>
   * @return Whether the skillId field is set.
   */
  @java.lang.Override
  public boolean hasSkillId() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>optional int32 skillId = 6;</code>
   * @return The skillId.
   */
  @java.lang.Override
  public int getSkillId() {
    return skillId_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasType()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasTalentId()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasLevel()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasQuality()) {
      memoizedIsInitialized = 0;
      return false;
    }
    for (int i = 0; i < getAttributeDataCount(); i++) {
      if (!getAttributeData(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, type_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(2, talentId_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt32(3, level_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt32(4, quality_);
    }
    for (int i = 0; i < attributeData_.size(); i++) {
      output.writeMessage(5, attributeData_.get(i));
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      output.writeInt32(6, skillId_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, type_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, talentId_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(3, level_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(4, quality_);
    }
    for (int i = 0; i < attributeData_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(5, attributeData_.get(i));
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(6, skillId_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.TalentDataMsg)) {
      return super.equals(obj);
    }
    xddq.pb.TalentDataMsg other = (xddq.pb.TalentDataMsg) obj;

    if (hasType() != other.hasType()) return false;
    if (hasType()) {
      if (getType()
          != other.getType()) return false;
    }
    if (hasTalentId() != other.hasTalentId()) return false;
    if (hasTalentId()) {
      if (getTalentId()
          != other.getTalentId()) return false;
    }
    if (hasLevel() != other.hasLevel()) return false;
    if (hasLevel()) {
      if (getLevel()
          != other.getLevel()) return false;
    }
    if (hasQuality() != other.hasQuality()) return false;
    if (hasQuality()) {
      if (getQuality()
          != other.getQuality()) return false;
    }
    if (!getAttributeDataList()
        .equals(other.getAttributeDataList())) return false;
    if (hasSkillId() != other.hasSkillId()) return false;
    if (hasSkillId()) {
      if (getSkillId()
          != other.getSkillId()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasType()) {
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
    }
    if (hasTalentId()) {
      hash = (37 * hash) + TALENTID_FIELD_NUMBER;
      hash = (53 * hash) + getTalentId();
    }
    if (hasLevel()) {
      hash = (37 * hash) + LEVEL_FIELD_NUMBER;
      hash = (53 * hash) + getLevel();
    }
    if (hasQuality()) {
      hash = (37 * hash) + QUALITY_FIELD_NUMBER;
      hash = (53 * hash) + getQuality();
    }
    if (getAttributeDataCount() > 0) {
      hash = (37 * hash) + ATTRIBUTEDATA_FIELD_NUMBER;
      hash = (53 * hash) + getAttributeDataList().hashCode();
    }
    if (hasSkillId()) {
      hash = (37 * hash) + SKILLID_FIELD_NUMBER;
      hash = (53 * hash) + getSkillId();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.TalentDataMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.TalentDataMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.TalentDataMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.TalentDataMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.TalentDataMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.TalentDataMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.TalentDataMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.TalentDataMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.TalentDataMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.TalentDataMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.TalentDataMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.TalentDataMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.TalentDataMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.TalentDataMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.TalentDataMsg)
      xddq.pb.TalentDataMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_TalentDataMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_TalentDataMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.TalentDataMsg.class, xddq.pb.TalentDataMsg.Builder.class);
    }

    // Construct using xddq.pb.TalentDataMsg.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      type_ = 0;
      talentId_ = 0;
      level_ = 0;
      quality_ = 0;
      if (attributeDataBuilder_ == null) {
        attributeData_ = java.util.Collections.emptyList();
      } else {
        attributeData_ = null;
        attributeDataBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000010);
      skillId_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_TalentDataMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.TalentDataMsg getDefaultInstanceForType() {
      return xddq.pb.TalentDataMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.TalentDataMsg build() {
      xddq.pb.TalentDataMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.TalentDataMsg buildPartial() {
      xddq.pb.TalentDataMsg result = new xddq.pb.TalentDataMsg(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.TalentDataMsg result) {
      if (attributeDataBuilder_ == null) {
        if (((bitField0_ & 0x00000010) != 0)) {
          attributeData_ = java.util.Collections.unmodifiableList(attributeData_);
          bitField0_ = (bitField0_ & ~0x00000010);
        }
        result.attributeData_ = attributeData_;
      } else {
        result.attributeData_ = attributeDataBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.TalentDataMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.type_ = type_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.talentId_ = talentId_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.level_ = level_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.quality_ = quality_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.skillId_ = skillId_;
        to_bitField0_ |= 0x00000010;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.TalentDataMsg) {
        return mergeFrom((xddq.pb.TalentDataMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.TalentDataMsg other) {
      if (other == xddq.pb.TalentDataMsg.getDefaultInstance()) return this;
      if (other.hasType()) {
        setType(other.getType());
      }
      if (other.hasTalentId()) {
        setTalentId(other.getTalentId());
      }
      if (other.hasLevel()) {
        setLevel(other.getLevel());
      }
      if (other.hasQuality()) {
        setQuality(other.getQuality());
      }
      if (attributeDataBuilder_ == null) {
        if (!other.attributeData_.isEmpty()) {
          if (attributeData_.isEmpty()) {
            attributeData_ = other.attributeData_;
            bitField0_ = (bitField0_ & ~0x00000010);
          } else {
            ensureAttributeDataIsMutable();
            attributeData_.addAll(other.attributeData_);
          }
          onChanged();
        }
      } else {
        if (!other.attributeData_.isEmpty()) {
          if (attributeDataBuilder_.isEmpty()) {
            attributeDataBuilder_.dispose();
            attributeDataBuilder_ = null;
            attributeData_ = other.attributeData_;
            bitField0_ = (bitField0_ & ~0x00000010);
            attributeDataBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetAttributeDataFieldBuilder() : null;
          } else {
            attributeDataBuilder_.addAllMessages(other.attributeData_);
          }
        }
      }
      if (other.hasSkillId()) {
        setSkillId(other.getSkillId());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasType()) {
        return false;
      }
      if (!hasTalentId()) {
        return false;
      }
      if (!hasLevel()) {
        return false;
      }
      if (!hasQuality()) {
        return false;
      }
      for (int i = 0; i < getAttributeDataCount(); i++) {
        if (!getAttributeData(i).isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              type_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              talentId_ = input.readInt32();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 24: {
              level_ = input.readInt32();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 32: {
              quality_ = input.readInt32();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            case 42: {
              xddq.pb.AttributeDataMsg m =
                  input.readMessage(
                      xddq.pb.AttributeDataMsg.parser(),
                      extensionRegistry);
              if (attributeDataBuilder_ == null) {
                ensureAttributeDataIsMutable();
                attributeData_.add(m);
              } else {
                attributeDataBuilder_.addMessage(m);
              }
              break;
            } // case 42
            case 48: {
              skillId_ = input.readInt32();
              bitField0_ |= 0x00000020;
              break;
            } // case 48
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int type_ ;
    /**
     * <code>required int32 type = 1;</code>
     * @return Whether the type field is set.
     */
    @java.lang.Override
    public boolean hasType() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 type = 1;</code>
     * @return The type.
     */
    @java.lang.Override
    public int getType() {
      return type_;
    }
    /**
     * <code>required int32 type = 1;</code>
     * @param value The type to set.
     * @return This builder for chaining.
     */
    public Builder setType(int value) {

      type_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 type = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearType() {
      bitField0_ = (bitField0_ & ~0x00000001);
      type_ = 0;
      onChanged();
      return this;
    }

    private int talentId_ ;
    /**
     * <code>required int32 talentId = 2;</code>
     * @return Whether the talentId field is set.
     */
    @java.lang.Override
    public boolean hasTalentId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>required int32 talentId = 2;</code>
     * @return The talentId.
     */
    @java.lang.Override
    public int getTalentId() {
      return talentId_;
    }
    /**
     * <code>required int32 talentId = 2;</code>
     * @param value The talentId to set.
     * @return This builder for chaining.
     */
    public Builder setTalentId(int value) {

      talentId_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 talentId = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearTalentId() {
      bitField0_ = (bitField0_ & ~0x00000002);
      talentId_ = 0;
      onChanged();
      return this;
    }

    private int level_ ;
    /**
     * <code>required int32 level = 3;</code>
     * @return Whether the level field is set.
     */
    @java.lang.Override
    public boolean hasLevel() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>required int32 level = 3;</code>
     * @return The level.
     */
    @java.lang.Override
    public int getLevel() {
      return level_;
    }
    /**
     * <code>required int32 level = 3;</code>
     * @param value The level to set.
     * @return This builder for chaining.
     */
    public Builder setLevel(int value) {

      level_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 level = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearLevel() {
      bitField0_ = (bitField0_ & ~0x00000004);
      level_ = 0;
      onChanged();
      return this;
    }

    private int quality_ ;
    /**
     * <code>required int32 quality = 4;</code>
     * @return Whether the quality field is set.
     */
    @java.lang.Override
    public boolean hasQuality() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>required int32 quality = 4;</code>
     * @return The quality.
     */
    @java.lang.Override
    public int getQuality() {
      return quality_;
    }
    /**
     * <code>required int32 quality = 4;</code>
     * @param value The quality to set.
     * @return This builder for chaining.
     */
    public Builder setQuality(int value) {

      quality_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 quality = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearQuality() {
      bitField0_ = (bitField0_ & ~0x00000008);
      quality_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.AttributeDataMsg> attributeData_ =
      java.util.Collections.emptyList();
    private void ensureAttributeDataIsMutable() {
      if (!((bitField0_ & 0x00000010) != 0)) {
        attributeData_ = new java.util.ArrayList<xddq.pb.AttributeDataMsg>(attributeData_);
        bitField0_ |= 0x00000010;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.AttributeDataMsg, xddq.pb.AttributeDataMsg.Builder, xddq.pb.AttributeDataMsgOrBuilder> attributeDataBuilder_;

    /**
     * <code>repeated .xddq.pb.AttributeDataMsg attributeData = 5;</code>
     */
    public java.util.List<xddq.pb.AttributeDataMsg> getAttributeDataList() {
      if (attributeDataBuilder_ == null) {
        return java.util.Collections.unmodifiableList(attributeData_);
      } else {
        return attributeDataBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.AttributeDataMsg attributeData = 5;</code>
     */
    public int getAttributeDataCount() {
      if (attributeDataBuilder_ == null) {
        return attributeData_.size();
      } else {
        return attributeDataBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.AttributeDataMsg attributeData = 5;</code>
     */
    public xddq.pb.AttributeDataMsg getAttributeData(int index) {
      if (attributeDataBuilder_ == null) {
        return attributeData_.get(index);
      } else {
        return attributeDataBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.AttributeDataMsg attributeData = 5;</code>
     */
    public Builder setAttributeData(
        int index, xddq.pb.AttributeDataMsg value) {
      if (attributeDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureAttributeDataIsMutable();
        attributeData_.set(index, value);
        onChanged();
      } else {
        attributeDataBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.AttributeDataMsg attributeData = 5;</code>
     */
    public Builder setAttributeData(
        int index, xddq.pb.AttributeDataMsg.Builder builderForValue) {
      if (attributeDataBuilder_ == null) {
        ensureAttributeDataIsMutable();
        attributeData_.set(index, builderForValue.build());
        onChanged();
      } else {
        attributeDataBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.AttributeDataMsg attributeData = 5;</code>
     */
    public Builder addAttributeData(xddq.pb.AttributeDataMsg value) {
      if (attributeDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureAttributeDataIsMutable();
        attributeData_.add(value);
        onChanged();
      } else {
        attributeDataBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.AttributeDataMsg attributeData = 5;</code>
     */
    public Builder addAttributeData(
        int index, xddq.pb.AttributeDataMsg value) {
      if (attributeDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureAttributeDataIsMutable();
        attributeData_.add(index, value);
        onChanged();
      } else {
        attributeDataBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.AttributeDataMsg attributeData = 5;</code>
     */
    public Builder addAttributeData(
        xddq.pb.AttributeDataMsg.Builder builderForValue) {
      if (attributeDataBuilder_ == null) {
        ensureAttributeDataIsMutable();
        attributeData_.add(builderForValue.build());
        onChanged();
      } else {
        attributeDataBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.AttributeDataMsg attributeData = 5;</code>
     */
    public Builder addAttributeData(
        int index, xddq.pb.AttributeDataMsg.Builder builderForValue) {
      if (attributeDataBuilder_ == null) {
        ensureAttributeDataIsMutable();
        attributeData_.add(index, builderForValue.build());
        onChanged();
      } else {
        attributeDataBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.AttributeDataMsg attributeData = 5;</code>
     */
    public Builder addAllAttributeData(
        java.lang.Iterable<? extends xddq.pb.AttributeDataMsg> values) {
      if (attributeDataBuilder_ == null) {
        ensureAttributeDataIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, attributeData_);
        onChanged();
      } else {
        attributeDataBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.AttributeDataMsg attributeData = 5;</code>
     */
    public Builder clearAttributeData() {
      if (attributeDataBuilder_ == null) {
        attributeData_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000010);
        onChanged();
      } else {
        attributeDataBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.AttributeDataMsg attributeData = 5;</code>
     */
    public Builder removeAttributeData(int index) {
      if (attributeDataBuilder_ == null) {
        ensureAttributeDataIsMutable();
        attributeData_.remove(index);
        onChanged();
      } else {
        attributeDataBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.AttributeDataMsg attributeData = 5;</code>
     */
    public xddq.pb.AttributeDataMsg.Builder getAttributeDataBuilder(
        int index) {
      return internalGetAttributeDataFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.AttributeDataMsg attributeData = 5;</code>
     */
    public xddq.pb.AttributeDataMsgOrBuilder getAttributeDataOrBuilder(
        int index) {
      if (attributeDataBuilder_ == null) {
        return attributeData_.get(index);  } else {
        return attributeDataBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.AttributeDataMsg attributeData = 5;</code>
     */
    public java.util.List<? extends xddq.pb.AttributeDataMsgOrBuilder> 
         getAttributeDataOrBuilderList() {
      if (attributeDataBuilder_ != null) {
        return attributeDataBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(attributeData_);
      }
    }
    /**
     * <code>repeated .xddq.pb.AttributeDataMsg attributeData = 5;</code>
     */
    public xddq.pb.AttributeDataMsg.Builder addAttributeDataBuilder() {
      return internalGetAttributeDataFieldBuilder().addBuilder(
          xddq.pb.AttributeDataMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.AttributeDataMsg attributeData = 5;</code>
     */
    public xddq.pb.AttributeDataMsg.Builder addAttributeDataBuilder(
        int index) {
      return internalGetAttributeDataFieldBuilder().addBuilder(
          index, xddq.pb.AttributeDataMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.AttributeDataMsg attributeData = 5;</code>
     */
    public java.util.List<xddq.pb.AttributeDataMsg.Builder> 
         getAttributeDataBuilderList() {
      return internalGetAttributeDataFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.AttributeDataMsg, xddq.pb.AttributeDataMsg.Builder, xddq.pb.AttributeDataMsgOrBuilder> 
        internalGetAttributeDataFieldBuilder() {
      if (attributeDataBuilder_ == null) {
        attributeDataBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.AttributeDataMsg, xddq.pb.AttributeDataMsg.Builder, xddq.pb.AttributeDataMsgOrBuilder>(
                attributeData_,
                ((bitField0_ & 0x00000010) != 0),
                getParentForChildren(),
                isClean());
        attributeData_ = null;
      }
      return attributeDataBuilder_;
    }

    private int skillId_ ;
    /**
     * <code>optional int32 skillId = 6;</code>
     * @return Whether the skillId field is set.
     */
    @java.lang.Override
    public boolean hasSkillId() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional int32 skillId = 6;</code>
     * @return The skillId.
     */
    @java.lang.Override
    public int getSkillId() {
      return skillId_;
    }
    /**
     * <code>optional int32 skillId = 6;</code>
     * @param value The skillId to set.
     * @return This builder for chaining.
     */
    public Builder setSkillId(int value) {

      skillId_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 skillId = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearSkillId() {
      bitField0_ = (bitField0_ & ~0x00000020);
      skillId_ = 0;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.TalentDataMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.TalentDataMsg)
  private static final xddq.pb.TalentDataMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.TalentDataMsg();
  }

  public static xddq.pb.TalentDataMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<TalentDataMsg>
      PARSER = new com.google.protobuf.AbstractParser<TalentDataMsg>() {
    @java.lang.Override
    public TalentDataMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<TalentDataMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<TalentDataMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.TalentDataMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

