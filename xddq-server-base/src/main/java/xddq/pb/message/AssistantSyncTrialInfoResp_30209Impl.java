package xddq.pb.message;

import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import xddq.message.pb.PbMessage;


public class AssistantSyncTrialInfoResp_30209Impl extends PbMessage {

    private xddq.pb.AssistantSyncTrialInfoResp msg;

    private xddq.pb.AssistantSyncTrialInfoResp.Builder builder;

    public AssistantSyncTrialInfoResp_30209Impl() {
    }

    public AssistantSyncTrialInfoResp_30209Impl(xddq.pb.AssistantSyncTrialInfoResp.Builder builder) {
        this.builder = builder;
    }

    public boolean write(ByteBuf buf) throws Exception {
        buf.writeBytes(this.getMsg().toByteArray());
        return true;
    }

    public boolean read(ByteBuf buf) throws Exception {
        int length = buf.readableBytes();
        byte[] array;
        int offset;
        if (buf.hasArray()) {
            array = buf.array();
            offset = buf.arrayOffset() + buf.readerIndex();
        } else {
            array = ByteBufUtil.getBytes(buf, buf.readerIndex(), length, false);
            offset = 0;
        }
        this.msg = xddq.pb.AssistantSyncTrialInfoResp.newBuilder().mergeFrom(array, offset, length).build();
        return true;
    }

    public xddq.pb.AssistantSyncTrialInfoResp getMsg() {
        if (msg == null) {
            msg = builder.build();
        }
        return msg;
    }

    public AssistantSyncTrialInfoResp_30209Impl setMsg(xddq.pb.AssistantSyncTrialInfoResp msg) {
        this.msg = msg;
        return this;
    }

    public int length() throws Exception {
        return this.getMsg().getSerializedSize();
    }

    @Override
    public int getId() {
        return 30209;
    }

    @Override
    public String toString() {
        try {
            return String.format("%d %s%s", this.getId(), getName(), format(JsonFormat.printer().print(this.getMsg())));
        } catch (InvalidProtocolBufferException ignored) {
        }
        return String.format("%d error message", this.getId());
    }
}
