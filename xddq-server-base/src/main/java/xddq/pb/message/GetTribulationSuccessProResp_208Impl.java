package xddq.pb.message;

import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import xddq.message.pb.PbMessage;


public class GetTribulationSuccessProResp_208Impl extends PbMessage {

    private xddq.pb.GetTribulationSuccessProResp msg;

    private xddq.pb.GetTribulationSuccessProResp.Builder builder;

    public GetTribulationSuccessProResp_208Impl() {
    }

    public GetTribulationSuccessProResp_208Impl(xddq.pb.GetTribulationSuccessProResp.Builder builder) {
        this.builder = builder;
    }

    public boolean write(ByteBuf buf) throws Exception {
        buf.writeBytes(this.getMsg().toByteArray());
        return true;
    }

    public boolean read(ByteBuf buf) throws Exception {
        int length = buf.readableBytes();
        byte[] array;
        int offset;
        if (buf.hasArray()) {
            array = buf.array();
            offset = buf.arrayOffset() + buf.readerIndex();
        } else {
            array = ByteBufUtil.getBytes(buf, buf.readerIndex(), length, false);
            offset = 0;
        }
        this.msg = xddq.pb.GetTribulationSuccessProResp.newBuilder().mergeFrom(array, offset, length).build();
        return true;
    }

    public xddq.pb.GetTribulationSuccessProResp getMsg() {
        if (msg == null) {
            msg = builder.build();
        }
        return msg;
    }

    public GetTribulationSuccessProResp_208Impl setMsg(xddq.pb.GetTribulationSuccessProResp msg) {
        this.msg = msg;
        return this;
    }

    public int length() throws Exception {
        return this.getMsg().getSerializedSize();
    }

    @Override
    public int getId() {
        return 208;
    }

    @Override
    public String toString() {
        try {
            return String.format("%d %s%s", this.getId(), getName(), format(JsonFormat.printer().print(this.getMsg())));
        } catch (InvalidProtocolBufferException ignored) {
        }
        return String.format("%d error message", this.getId());
    }
}
