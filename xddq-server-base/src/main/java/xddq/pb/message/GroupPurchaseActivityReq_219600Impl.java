package xddq.pb.message;

import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import xddq.message.pb.PbMessage;


public class GroupPurchaseActivityReq_219600Impl extends PbMessage {

    private xddq.pb.GroupPurchaseActivityReq msg;

    private xddq.pb.GroupPurchaseActivityReq.Builder builder;

    public GroupPurchaseActivityReq_219600Impl() {
    }

    public GroupPurchaseActivityReq_219600Impl(xddq.pb.GroupPurchaseActivityReq.Builder builder) {
        this.builder = builder;
    }

    public boolean write(ByteBuf buf) throws Exception {
        buf.writeBytes(this.getMsg().toByteArray());
        return true;
    }

    public boolean read(ByteBuf buf) throws Exception {
        int length = buf.readableBytes();
        byte[] array;
        int offset;
        if (buf.hasArray()) {
            array = buf.array();
            offset = buf.arrayOffset() + buf.readerIndex();
        } else {
            array = ByteBufUtil.getBytes(buf, buf.readerIndex(), length, false);
            offset = 0;
        }
        this.msg = xddq.pb.GroupPurchaseActivityReq.newBuilder().mergeFrom(array, offset, length).build();
        return true;
    }

    public xddq.pb.GroupPurchaseActivityReq getMsg() {
        if (msg == null) {
            msg = builder.build();
        }
        return msg;
    }

    public GroupPurchaseActivityReq_219600Impl setMsg(xddq.pb.GroupPurchaseActivityReq msg) {
        this.msg = msg;
        return this;
    }

    public int length() throws Exception {
        return this.getMsg().getSerializedSize();
    }

    @Override
    public int getId() {
        return 219600;
    }

    @Override
    public String toString() {
        try {
            return String.format("%d %s%s", this.getId(), getName(), format(JsonFormat.printer().print(this.getMsg())));
        } catch (InvalidProtocolBufferException ignored) {
        }
        return String.format("%d error message", this.getId());
    }
}
