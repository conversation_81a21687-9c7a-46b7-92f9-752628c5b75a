package xddq.pb.message;

import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import xddq.message.pb.PbMessage;


public class ShuraBattleSearchTeamResp_15917Impl extends PbMessage {

    private xddq.pb.ShuraBattleSearchTeamResp msg;

    private xddq.pb.ShuraBattleSearchTeamResp.Builder builder;

    public ShuraBattleSearchTeamResp_15917Impl() {
    }

    public ShuraBattleSearchTeamResp_15917Impl(xddq.pb.ShuraBattleSearchTeamResp.Builder builder) {
        this.builder = builder;
    }

    public boolean write(ByteBuf buf) throws Exception {
        buf.writeBytes(this.getMsg().toByteArray());
        return true;
    }

    public boolean read(ByteBuf buf) throws Exception {
        int length = buf.readableBytes();
        byte[] array;
        int offset;
        if (buf.hasArray()) {
            array = buf.array();
            offset = buf.arrayOffset() + buf.readerIndex();
        } else {
            array = ByteBufUtil.getBytes(buf, buf.readerIndex(), length, false);
            offset = 0;
        }
        this.msg = xddq.pb.ShuraBattleSearchTeamResp.newBuilder().mergeFrom(array, offset, length).build();
        return true;
    }

    public xddq.pb.ShuraBattleSearchTeamResp getMsg() {
        if (msg == null) {
            msg = builder.build();
        }
        return msg;
    }

    public ShuraBattleSearchTeamResp_15917Impl setMsg(xddq.pb.ShuraBattleSearchTeamResp msg) {
        this.msg = msg;
        return this;
    }

    public int length() throws Exception {
        return this.getMsg().getSerializedSize();
    }

    @Override
    public int getId() {
        return 15917;
    }

    @Override
    public String toString() {
        try {
            return String.format("%d %s%s", this.getId(), getName(), format(JsonFormat.printer().print(this.getMsg())));
        } catch (InvalidProtocolBufferException ignored) {
        }
        return String.format("%d error message", this.getId());
    }
}
