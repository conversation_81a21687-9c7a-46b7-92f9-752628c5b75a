package xddq.pb.message;

import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import xddq.message.pb.PbMessage;


public class GatherEnergyGetADAwardReq_207019Impl extends PbMessage {

    private xddq.pb.GatherEnergyGetADAwardReq msg;

    private xddq.pb.GatherEnergyGetADAwardReq.Builder builder;

    public GatherEnergyGetADAwardReq_207019Impl() {
    }

    public GatherEnergyGetADAwardReq_207019Impl(xddq.pb.GatherEnergyGetADAwardReq.Builder builder) {
        this.builder = builder;
    }

    public boolean write(ByteBuf buf) throws Exception {
        buf.writeBytes(this.getMsg().toByteArray());
        return true;
    }

    public boolean read(ByteBuf buf) throws Exception {
        int length = buf.readableBytes();
        byte[] array;
        int offset;
        if (buf.hasArray()) {
            array = buf.array();
            offset = buf.arrayOffset() + buf.readerIndex();
        } else {
            array = ByteBufUtil.getBytes(buf, buf.readerIndex(), length, false);
            offset = 0;
        }
        this.msg = xddq.pb.GatherEnergyGetADAwardReq.newBuilder().mergeFrom(array, offset, length).build();
        return true;
    }

    public xddq.pb.GatherEnergyGetADAwardReq getMsg() {
        if (msg == null) {
            msg = builder.build();
        }
        return msg;
    }

    public GatherEnergyGetADAwardReq_207019Impl setMsg(xddq.pb.GatherEnergyGetADAwardReq msg) {
        this.msg = msg;
        return this;
    }

    public int length() throws Exception {
        return this.getMsg().getSerializedSize();
    }

    @Override
    public int getId() {
        return 207019;
    }

    @Override
    public String toString() {
        try {
            return String.format("%d %s%s", this.getId(), getName(), format(JsonFormat.printer().print(this.getMsg())));
        } catch (InvalidProtocolBufferException ignored) {
        }
        return String.format("%d error message", this.getId());
    }
}
