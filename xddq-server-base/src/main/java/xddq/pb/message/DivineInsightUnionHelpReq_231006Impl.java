package xddq.pb.message;

import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import xddq.message.pb.PbMessage;


public class DivineInsightUnionHelpReq_231006Impl extends PbMessage {

    private xddq.pb.DivineInsightUnionHelpReq msg;

    private xddq.pb.DivineInsightUnionHelpReq.Builder builder;

    public DivineInsightUnionHelpReq_231006Impl() {
    }

    public DivineInsightUnionHelpReq_231006Impl(xddq.pb.DivineInsightUnionHelpReq.Builder builder) {
        this.builder = builder;
    }

    public boolean write(ByteBuf buf) throws Exception {
        buf.writeBytes(this.getMsg().toByteArray());
        return true;
    }

    public boolean read(ByteBuf buf) throws Exception {
        int length = buf.readableBytes();
        byte[] array;
        int offset;
        if (buf.hasArray()) {
            array = buf.array();
            offset = buf.arrayOffset() + buf.readerIndex();
        } else {
            array = ByteBufUtil.getBytes(buf, buf.readerIndex(), length, false);
            offset = 0;
        }
        this.msg = xddq.pb.DivineInsightUnionHelpReq.newBuilder().mergeFrom(array, offset, length).build();
        return true;
    }

    public xddq.pb.DivineInsightUnionHelpReq getMsg() {
        if (msg == null) {
            msg = builder.build();
        }
        return msg;
    }

    public DivineInsightUnionHelpReq_231006Impl setMsg(xddq.pb.DivineInsightUnionHelpReq msg) {
        this.msg = msg;
        return this;
    }

    public int length() throws Exception {
        return this.getMsg().getSerializedSize();
    }

    @Override
    public int getId() {
        return 231006;
    }

    @Override
    public String toString() {
        try {
            return String.format("%d %s%s", this.getId(), getName(), format(JsonFormat.printer().print(this.getMsg())));
        } catch (InvalidProtocolBufferException ignored) {
        }
        return String.format("%d error message", this.getId());
    }
}
