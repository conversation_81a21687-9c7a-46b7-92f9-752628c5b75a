package xddq.pb.message;

import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import xddq.message.pb.PbMessage;


public class ElementalBondsGetTaskDataReqMsg_218249Impl extends PbMessage {

    private xddq.pb.ElementalBondsGetTaskDataReqMsg msg;

    private xddq.pb.ElementalBondsGetTaskDataReqMsg.Builder builder;

    public ElementalBondsGetTaskDataReqMsg_218249Impl() {
    }

    public ElementalBondsGetTaskDataReqMsg_218249Impl(xddq.pb.ElementalBondsGetTaskDataReqMsg.Builder builder) {
        this.builder = builder;
    }

    public boolean write(ByteBuf buf) throws Exception {
        buf.writeBytes(this.getMsg().toByteArray());
        return true;
    }

    public boolean read(ByteBuf buf) throws Exception {
        int length = buf.readableBytes();
        byte[] array;
        int offset;
        if (buf.hasArray()) {
            array = buf.array();
            offset = buf.arrayOffset() + buf.readerIndex();
        } else {
            array = ByteBufUtil.getBytes(buf, buf.readerIndex(), length, false);
            offset = 0;
        }
        this.msg = xddq.pb.ElementalBondsGetTaskDataReqMsg.newBuilder().mergeFrom(array, offset, length).build();
        return true;
    }

    public xddq.pb.ElementalBondsGetTaskDataReqMsg getMsg() {
        if (msg == null) {
            msg = builder.build();
        }
        return msg;
    }

    public ElementalBondsGetTaskDataReqMsg_218249Impl setMsg(xddq.pb.ElementalBondsGetTaskDataReqMsg msg) {
        this.msg = msg;
        return this;
    }

    public int length() throws Exception {
        return this.getMsg().getSerializedSize();
    }

    @Override
    public int getId() {
        return 218249;
    }

    @Override
    public String toString() {
        try {
            return String.format("%d %s%s", this.getId(), getName(), format(JsonFormat.printer().print(this.getMsg())));
        } catch (InvalidProtocolBufferException ignored) {
        }
        return String.format("%d error message", this.getId());
    }
}
