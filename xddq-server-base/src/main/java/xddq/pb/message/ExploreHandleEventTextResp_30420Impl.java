package xddq.pb.message;

import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import xddq.message.pb.PbMessage;


public class ExploreHandleEventTextResp_30420Impl extends PbMessage {

    private xddq.pb.ExploreHandleEventTextResp msg;

    private xddq.pb.ExploreHandleEventTextResp.Builder builder;

    public ExploreHandleEventTextResp_30420Impl() {
    }

    public ExploreHandleEventTextResp_30420Impl(xddq.pb.ExploreHandleEventTextResp.Builder builder) {
        this.builder = builder;
    }

    public boolean write(ByteBuf buf) throws Exception {
        buf.writeBytes(this.getMsg().toByteArray());
        return true;
    }

    public boolean read(ByteBuf buf) throws Exception {
        int length = buf.readableBytes();
        byte[] array;
        int offset;
        if (buf.hasArray()) {
            array = buf.array();
            offset = buf.arrayOffset() + buf.readerIndex();
        } else {
            array = ByteBufUtil.getBytes(buf, buf.readerIndex(), length, false);
            offset = 0;
        }
        this.msg = xddq.pb.ExploreHandleEventTextResp.newBuilder().mergeFrom(array, offset, length).build();
        return true;
    }

    public xddq.pb.ExploreHandleEventTextResp getMsg() {
        if (msg == null) {
            msg = builder.build();
        }
        return msg;
    }

    public ExploreHandleEventTextResp_30420Impl setMsg(xddq.pb.ExploreHandleEventTextResp msg) {
        this.msg = msg;
        return this;
    }

    public int length() throws Exception {
        return this.getMsg().getSerializedSize();
    }

    @Override
    public int getId() {
        return 30420;
    }

    @Override
    public String toString() {
        try {
            return String.format("%d %s%s", this.getId(), getName(), format(JsonFormat.printer().print(this.getMsg())));
        } catch (InvalidProtocolBufferException ignored) {
        }
        return String.format("%d error message", this.getId());
    }
}
