package xddq.pb.message;

import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import xddq.message.pb.PbMessage;


public class RspHeroRankFightPlayerList_3702Impl extends PbMessage {

    private xddq.pb.RspHeroRankFightPlayerList msg;

    private xddq.pb.RspHeroRankFightPlayerList.Builder builder;

    public RspHeroRankFightPlayerList_3702Impl() {
    }

    public RspHeroRankFightPlayerList_3702Impl(xddq.pb.RspHeroRankFightPlayerList.Builder builder) {
        this.builder = builder;
    }

    public boolean write(ByteBuf buf) throws Exception {
        buf.writeBytes(this.getMsg().toByteArray());
        return true;
    }

    public boolean read(ByteBuf buf) throws Exception {
        int length = buf.readableBytes();
        byte[] array;
        int offset;
        if (buf.hasArray()) {
            array = buf.array();
            offset = buf.arrayOffset() + buf.readerIndex();
        } else {
            array = ByteBufUtil.getBytes(buf, buf.readerIndex(), length, false);
            offset = 0;
        }
        this.msg = xddq.pb.RspHeroRankFightPlayerList.newBuilder().mergeFrom(array, offset, length).build();
        return true;
    }

    public xddq.pb.RspHeroRankFightPlayerList getMsg() {
        if (msg == null) {
            msg = builder.build();
        }
        return msg;
    }

    public RspHeroRankFightPlayerList_3702Impl setMsg(xddq.pb.RspHeroRankFightPlayerList msg) {
        this.msg = msg;
        return this;
    }

    public int length() throws Exception {
        return this.getMsg().getSerializedSize();
    }

    @Override
    public int getId() {
        return 3702;
    }

    @Override
    public String toString() {
        try {
            return String.format("%d %s%s", this.getId(), getName(), format(JsonFormat.printer().print(this.getMsg())));
        } catch (InvalidProtocolBufferException ignored) {
        }
        return String.format("%d error message", this.getId());
    }
}
