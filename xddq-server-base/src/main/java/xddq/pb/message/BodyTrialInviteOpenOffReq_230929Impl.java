package xddq.pb.message;

import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import xddq.message.pb.PbMessage;


public class BodyTrialInviteOpenOffReq_230929Impl extends PbMessage {

    private xddq.pb.BodyTrialInviteOpenOffReq msg;

    private xddq.pb.BodyTrialInviteOpenOffReq.Builder builder;

    public BodyTrialInviteOpenOffReq_230929Impl() {
    }

    public BodyTrialInviteOpenOffReq_230929Impl(xddq.pb.BodyTrialInviteOpenOffReq.Builder builder) {
        this.builder = builder;
    }

    public boolean write(ByteBuf buf) throws Exception {
        buf.writeBytes(this.getMsg().toByteArray());
        return true;
    }

    public boolean read(ByteBuf buf) throws Exception {
        int length = buf.readableBytes();
        byte[] array;
        int offset;
        if (buf.hasArray()) {
            array = buf.array();
            offset = buf.arrayOffset() + buf.readerIndex();
        } else {
            array = ByteBufUtil.getBytes(buf, buf.readerIndex(), length, false);
            offset = 0;
        }
        this.msg = xddq.pb.BodyTrialInviteOpenOffReq.newBuilder().mergeFrom(array, offset, length).build();
        return true;
    }

    public xddq.pb.BodyTrialInviteOpenOffReq getMsg() {
        if (msg == null) {
            msg = builder.build();
        }
        return msg;
    }

    public BodyTrialInviteOpenOffReq_230929Impl setMsg(xddq.pb.BodyTrialInviteOpenOffReq msg) {
        this.msg = msg;
        return this;
    }

    public int length() throws Exception {
        return this.getMsg().getSerializedSize();
    }

    @Override
    public int getId() {
        return 230929;
    }

    @Override
    public String toString() {
        try {
            return String.format("%d %s%s", this.getId(), getName(), format(JsonFormat.printer().print(this.getMsg())));
        } catch (InvalidProtocolBufferException ignored) {
        }
        return String.format("%d error message", this.getId());
    }
}
