package xddq.pb.message;

import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import xddq.message.pb.PbMessage;


public class DragonHomeInvitePlayerReq_210857Impl extends PbMessage {

    private xddq.pb.DragonHomeInvitePlayerReq msg;

    private xddq.pb.DragonHomeInvitePlayerReq.Builder builder;

    public DragonHomeInvitePlayerReq_210857Impl() {
    }

    public DragonHomeInvitePlayerReq_210857Impl(xddq.pb.DragonHomeInvitePlayerReq.Builder builder) {
        this.builder = builder;
    }

    public boolean write(ByteBuf buf) throws Exception {
        buf.writeBytes(this.getMsg().toByteArray());
        return true;
    }

    public boolean read(ByteBuf buf) throws Exception {
        int length = buf.readableBytes();
        byte[] array;
        int offset;
        if (buf.hasArray()) {
            array = buf.array();
            offset = buf.arrayOffset() + buf.readerIndex();
        } else {
            array = ByteBufUtil.getBytes(buf, buf.readerIndex(), length, false);
            offset = 0;
        }
        this.msg = xddq.pb.DragonHomeInvitePlayerReq.newBuilder().mergeFrom(array, offset, length).build();
        return true;
    }

    public xddq.pb.DragonHomeInvitePlayerReq getMsg() {
        if (msg == null) {
            msg = builder.build();
        }
        return msg;
    }

    public DragonHomeInvitePlayerReq_210857Impl setMsg(xddq.pb.DragonHomeInvitePlayerReq msg) {
        this.msg = msg;
        return this;
    }

    public int length() throws Exception {
        return this.getMsg().getSerializedSize();
    }

    @Override
    public int getId() {
        return 210857;
    }

    @Override
    public String toString() {
        try {
            return String.format("%d %s%s", this.getId(), getName(), format(JsonFormat.printer().print(this.getMsg())));
        } catch (InvalidProtocolBufferException ignored) {
        }
        return String.format("%d error message", this.getId());
    }
}
