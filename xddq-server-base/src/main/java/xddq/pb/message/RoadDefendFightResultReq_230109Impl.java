package xddq.pb.message;

import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import xddq.message.pb.PbMessage;


public class RoadDefendFightResultReq_230109Impl extends PbMessage {

    private xddq.pb.RoadDefendFightResultReq msg;

    private xddq.pb.RoadDefendFightResultReq.Builder builder;

    public RoadDefendFightResultReq_230109Impl() {
    }

    public RoadDefendFightResultReq_230109Impl(xddq.pb.RoadDefendFightResultReq.Builder builder) {
        this.builder = builder;
    }

    public boolean write(ByteBuf buf) throws Exception {
        buf.writeBytes(this.getMsg().toByteArray());
        return true;
    }

    public boolean read(ByteBuf buf) throws Exception {
        int length = buf.readableBytes();
        byte[] array;
        int offset;
        if (buf.hasArray()) {
            array = buf.array();
            offset = buf.arrayOffset() + buf.readerIndex();
        } else {
            array = ByteBufUtil.getBytes(buf, buf.readerIndex(), length, false);
            offset = 0;
        }
        this.msg = xddq.pb.RoadDefendFightResultReq.newBuilder().mergeFrom(array, offset, length).build();
        return true;
    }

    public xddq.pb.RoadDefendFightResultReq getMsg() {
        if (msg == null) {
            msg = builder.build();
        }
        return msg;
    }

    public RoadDefendFightResultReq_230109Impl setMsg(xddq.pb.RoadDefendFightResultReq msg) {
        this.msg = msg;
        return this;
    }

    public int length() throws Exception {
        return this.getMsg().getSerializedSize();
    }

    @Override
    public int getId() {
        return 230109;
    }

    @Override
    public String toString() {
        try {
            return String.format("%d %s%s", this.getId(), getName(), format(JsonFormat.printer().print(this.getMsg())));
        } catch (InvalidProtocolBufferException ignored) {
        }
        return String.format("%d error message", this.getId());
    }
}
