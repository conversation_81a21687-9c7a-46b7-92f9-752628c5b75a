package xddq.pb.message;

import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import xddq.message.pb.PbMessage;


public class UnionTreasureDrawChipReq_216201Impl extends PbMessage {

    private xddq.pb.UnionTreasureDrawChipReq msg;

    private xddq.pb.UnionTreasureDrawChipReq.Builder builder;

    public UnionTreasureDrawChipReq_216201Impl() {
    }

    public UnionTreasureDrawChipReq_216201Impl(xddq.pb.UnionTreasureDrawChipReq.Builder builder) {
        this.builder = builder;
    }

    public boolean write(ByteBuf buf) throws Exception {
        buf.writeBytes(this.getMsg().toByteArray());
        return true;
    }

    public boolean read(ByteBuf buf) throws Exception {
        int length = buf.readableBytes();
        byte[] array;
        int offset;
        if (buf.hasArray()) {
            array = buf.array();
            offset = buf.arrayOffset() + buf.readerIndex();
        } else {
            array = ByteBufUtil.getBytes(buf, buf.readerIndex(), length, false);
            offset = 0;
        }
        this.msg = xddq.pb.UnionTreasureDrawChipReq.newBuilder().mergeFrom(array, offset, length).build();
        return true;
    }

    public xddq.pb.UnionTreasureDrawChipReq getMsg() {
        if (msg == null) {
            msg = builder.build();
        }
        return msg;
    }

    public UnionTreasureDrawChipReq_216201Impl setMsg(xddq.pb.UnionTreasureDrawChipReq msg) {
        this.msg = msg;
        return this;
    }

    public int length() throws Exception {
        return this.getMsg().getSerializedSize();
    }

    @Override
    public int getId() {
        return 216201;
    }

    @Override
    public String toString() {
        try {
            return String.format("%d %s%s", this.getId(), getName(), format(JsonFormat.printer().print(this.getMsg())));
        } catch (InvalidProtocolBufferException ignored) {
        }
        return String.format("%d error message", this.getId());
    }
}
