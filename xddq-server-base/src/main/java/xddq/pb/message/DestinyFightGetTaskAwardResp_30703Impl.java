package xddq.pb.message;

import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import xddq.message.pb.PbMessage;


public class DestinyFightGetTaskAwardResp_30703Impl extends PbMessage {

    private xddq.pb.DestinyFightGetTaskAwardResp msg;

    private xddq.pb.DestinyFightGetTaskAwardResp.Builder builder;

    public DestinyFightGetTaskAwardResp_30703Impl() {
    }

    public DestinyFightGetTaskAwardResp_30703Impl(xddq.pb.DestinyFightGetTaskAwardResp.Builder builder) {
        this.builder = builder;
    }

    public boolean write(ByteBuf buf) throws Exception {
        buf.writeBytes(this.getMsg().toByteArray());
        return true;
    }

    public boolean read(ByteBuf buf) throws Exception {
        int length = buf.readableBytes();
        byte[] array;
        int offset;
        if (buf.hasArray()) {
            array = buf.array();
            offset = buf.arrayOffset() + buf.readerIndex();
        } else {
            array = ByteBufUtil.getBytes(buf, buf.readerIndex(), length, false);
            offset = 0;
        }
        this.msg = xddq.pb.DestinyFightGetTaskAwardResp.newBuilder().mergeFrom(array, offset, length).build();
        return true;
    }

    public xddq.pb.DestinyFightGetTaskAwardResp getMsg() {
        if (msg == null) {
            msg = builder.build();
        }
        return msg;
    }

    public DestinyFightGetTaskAwardResp_30703Impl setMsg(xddq.pb.DestinyFightGetTaskAwardResp msg) {
        this.msg = msg;
        return this;
    }

    public int length() throws Exception {
        return this.getMsg().getSerializedSize();
    }

    @Override
    public int getId() {
        return 30703;
    }

    @Override
    public String toString() {
        try {
            return String.format("%d %s%s", this.getId(), getName(), format(JsonFormat.printer().print(this.getMsg())));
        } catch (InvalidProtocolBufferException ignored) {
        }
        return String.format("%d error message", this.getId());
    }
}
