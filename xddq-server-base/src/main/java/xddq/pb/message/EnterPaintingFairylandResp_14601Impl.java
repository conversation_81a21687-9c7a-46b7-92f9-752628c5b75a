package xddq.pb.message;

import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import xddq.message.pb.PbMessage;


public class EnterPaintingFairylandResp_14601Impl extends PbMessage {

    private xddq.pb.EnterPaintingFairylandResp msg;

    private xddq.pb.EnterPaintingFairylandResp.Builder builder;

    public EnterPaintingFairylandResp_14601Impl() {
    }

    public EnterPaintingFairylandResp_14601Impl(xddq.pb.EnterPaintingFairylandResp.Builder builder) {
        this.builder = builder;
    }

    public boolean write(ByteBuf buf) throws Exception {
        buf.writeBytes(this.getMsg().toByteArray());
        return true;
    }

    public boolean read(ByteBuf buf) throws Exception {
        int length = buf.readableBytes();
        byte[] array;
        int offset;
        if (buf.hasArray()) {
            array = buf.array();
            offset = buf.arrayOffset() + buf.readerIndex();
        } else {
            array = ByteBufUtil.getBytes(buf, buf.readerIndex(), length, false);
            offset = 0;
        }
        this.msg = xddq.pb.EnterPaintingFairylandResp.newBuilder().mergeFrom(array, offset, length).build();
        return true;
    }

    public xddq.pb.EnterPaintingFairylandResp getMsg() {
        if (msg == null) {
            msg = builder.build();
        }
        return msg;
    }

    public EnterPaintingFairylandResp_14601Impl setMsg(xddq.pb.EnterPaintingFairylandResp msg) {
        this.msg = msg;
        return this;
    }

    public int length() throws Exception {
        return this.getMsg().getSerializedSize();
    }

    @Override
    public int getId() {
        return 14601;
    }

    @Override
    public String toString() {
        try {
            return String.format("%d %s%s", this.getId(), getName(), format(JsonFormat.printer().print(this.getMsg())));
        } catch (InvalidProtocolBufferException ignored) {
        }
        return String.format("%d error message", this.getId());
    }
}
