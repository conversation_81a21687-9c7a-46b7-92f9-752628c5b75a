package xddq.pb.message;

import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import xddq.message.pb.PbMessage;


public class WarSeasonMapManorListReq_217507Impl extends PbMessage {

    private xddq.pb.WarSeasonMapManorListReq msg;

    private xddq.pb.WarSeasonMapManorListReq.Builder builder;

    public WarSeasonMapManorListReq_217507Impl() {
    }

    public WarSeasonMapManorListReq_217507Impl(xddq.pb.WarSeasonMapManorListReq.Builder builder) {
        this.builder = builder;
    }

    public boolean write(ByteBuf buf) throws Exception {
        buf.writeBytes(this.getMsg().toByteArray());
        return true;
    }

    public boolean read(ByteBuf buf) throws Exception {
        int length = buf.readableBytes();
        byte[] array;
        int offset;
        if (buf.hasArray()) {
            array = buf.array();
            offset = buf.arrayOffset() + buf.readerIndex();
        } else {
            array = ByteBufUtil.getBytes(buf, buf.readerIndex(), length, false);
            offset = 0;
        }
        this.msg = xddq.pb.WarSeasonMapManorListReq.newBuilder().mergeFrom(array, offset, length).build();
        return true;
    }

    public xddq.pb.WarSeasonMapManorListReq getMsg() {
        if (msg == null) {
            msg = builder.build();
        }
        return msg;
    }

    public WarSeasonMapManorListReq_217507Impl setMsg(xddq.pb.WarSeasonMapManorListReq msg) {
        this.msg = msg;
        return this;
    }

    public int length() throws Exception {
        return this.getMsg().getSerializedSize();
    }

    @Override
    public int getId() {
        return 217507;
    }

    @Override
    public String toString() {
        try {
            return String.format("%d %s%s", this.getId(), getName(), format(JsonFormat.printer().print(this.getMsg())));
        } catch (InvalidProtocolBufferException ignored) {
        }
        return String.format("%d error message", this.getId());
    }
}
