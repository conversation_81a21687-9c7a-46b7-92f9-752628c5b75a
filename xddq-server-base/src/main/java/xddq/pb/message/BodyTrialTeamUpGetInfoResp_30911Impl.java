package xddq.pb.message;

import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import xddq.message.pb.PbMessage;


public class BodyTrialTeamUpGetInfoResp_30911Impl extends PbMessage {

    private xddq.pb.BodyTrialTeamUpGetInfoResp msg;

    private xddq.pb.BodyTrialTeamUpGetInfoResp.Builder builder;

    public BodyTrialTeamUpGetInfoResp_30911Impl() {
    }

    public BodyTrialTeamUpGetInfoResp_30911Impl(xddq.pb.BodyTrialTeamUpGetInfoResp.Builder builder) {
        this.builder = builder;
    }

    public boolean write(ByteBuf buf) throws Exception {
        buf.writeBytes(this.getMsg().toByteArray());
        return true;
    }

    public boolean read(ByteBuf buf) throws Exception {
        int length = buf.readableBytes();
        byte[] array;
        int offset;
        if (buf.hasArray()) {
            array = buf.array();
            offset = buf.arrayOffset() + buf.readerIndex();
        } else {
            array = ByteBufUtil.getBytes(buf, buf.readerIndex(), length, false);
            offset = 0;
        }
        this.msg = xddq.pb.BodyTrialTeamUpGetInfoResp.newBuilder().mergeFrom(array, offset, length).build();
        return true;
    }

    public xddq.pb.BodyTrialTeamUpGetInfoResp getMsg() {
        if (msg == null) {
            msg = builder.build();
        }
        return msg;
    }

    public BodyTrialTeamUpGetInfoResp_30911Impl setMsg(xddq.pb.BodyTrialTeamUpGetInfoResp msg) {
        this.msg = msg;
        return this;
    }

    public int length() throws Exception {
        return this.getMsg().getSerializedSize();
    }

    @Override
    public int getId() {
        return 30911;
    }

    @Override
    public String toString() {
        try {
            return String.format("%d %s%s", this.getId(), getName(), format(JsonFormat.printer().print(this.getMsg())));
        } catch (InvalidProtocolBufferException ignored) {
        }
        return String.format("%d error message", this.getId());
    }
}
