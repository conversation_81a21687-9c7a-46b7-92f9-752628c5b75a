package xddq.pb.message;

import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import xddq.message.pb.PbMessage;


public class PlanesTrialMatchMemberRsp_16015Impl extends PbMessage {

    private xddq.pb.PlanesTrialMatchMemberRsp msg;

    private xddq.pb.PlanesTrialMatchMemberRsp.Builder builder;

    public PlanesTrialMatchMemberRsp_16015Impl() {
    }

    public PlanesTrialMatchMemberRsp_16015Impl(xddq.pb.PlanesTrialMatchMemberRsp.Builder builder) {
        this.builder = builder;
    }

    public boolean write(ByteBuf buf) throws Exception {
        buf.writeBytes(this.getMsg().toByteArray());
        return true;
    }

    public boolean read(ByteBuf buf) throws Exception {
        int length = buf.readableBytes();
        byte[] array;
        int offset;
        if (buf.hasArray()) {
            array = buf.array();
            offset = buf.arrayOffset() + buf.readerIndex();
        } else {
            array = ByteBufUtil.getBytes(buf, buf.readerIndex(), length, false);
            offset = 0;
        }
        this.msg = xddq.pb.PlanesTrialMatchMemberRsp.newBuilder().mergeFrom(array, offset, length).build();
        return true;
    }

    public xddq.pb.PlanesTrialMatchMemberRsp getMsg() {
        if (msg == null) {
            msg = builder.build();
        }
        return msg;
    }

    public PlanesTrialMatchMemberRsp_16015Impl setMsg(xddq.pb.PlanesTrialMatchMemberRsp msg) {
        this.msg = msg;
        return this;
    }

    public int length() throws Exception {
        return this.getMsg().getSerializedSize();
    }

    @Override
    public int getId() {
        return 16015;
    }

    @Override
    public String toString() {
        try {
            return String.format("%d %s%s", this.getId(), getName(), format(JsonFormat.printer().print(this.getMsg())));
        } catch (InvalidProtocolBufferException ignored) {
        }
        return String.format("%d error message", this.getId());
    }
}
