package xddq.pb.message;

import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import xddq.message.pb.PbMessage;


public class GroupPurchaseApplyListReq_219660Impl extends PbMessage {

    private xddq.pb.GroupPurchaseApplyListReq msg;

    private xddq.pb.GroupPurchaseApplyListReq.Builder builder;

    public GroupPurchaseApplyListReq_219660Impl() {
    }

    public GroupPurchaseApplyListReq_219660Impl(xddq.pb.GroupPurchaseApplyListReq.Builder builder) {
        this.builder = builder;
    }

    public boolean write(ByteBuf buf) throws Exception {
        buf.writeBytes(this.getMsg().toByteArray());
        return true;
    }

    public boolean read(ByteBuf buf) throws Exception {
        int length = buf.readableBytes();
        byte[] array;
        int offset;
        if (buf.hasArray()) {
            array = buf.array();
            offset = buf.arrayOffset() + buf.readerIndex();
        } else {
            array = ByteBufUtil.getBytes(buf, buf.readerIndex(), length, false);
            offset = 0;
        }
        this.msg = xddq.pb.GroupPurchaseApplyListReq.newBuilder().mergeFrom(array, offset, length).build();
        return true;
    }

    public xddq.pb.GroupPurchaseApplyListReq getMsg() {
        if (msg == null) {
            msg = builder.build();
        }
        return msg;
    }

    public GroupPurchaseApplyListReq_219660Impl setMsg(xddq.pb.GroupPurchaseApplyListReq msg) {
        this.msg = msg;
        return this;
    }

    public int length() throws Exception {
        return this.getMsg().getSerializedSize();
    }

    @Override
    public int getId() {
        return 219660;
    }

    @Override
    public String toString() {
        try {
            return String.format("%d %s%s", this.getId(), getName(), format(JsonFormat.printer().print(this.getMsg())));
        } catch (InvalidProtocolBufferException ignored) {
        }
        return String.format("%d error message", this.getId());
    }
}
