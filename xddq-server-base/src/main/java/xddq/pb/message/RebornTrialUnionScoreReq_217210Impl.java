package xddq.pb.message;

import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import xddq.message.pb.PbMessage;


public class RebornTrialUnionScoreReq_217210Impl extends PbMessage {

    private xddq.pb.RebornTrialUnionScoreReq msg;

    private xddq.pb.RebornTrialUnionScoreReq.Builder builder;

    public RebornTrialUnionScoreReq_217210Impl() {
    }

    public RebornTrialUnionScoreReq_217210Impl(xddq.pb.RebornTrialUnionScoreReq.Builder builder) {
        this.builder = builder;
    }

    public boolean write(ByteBuf buf) throws Exception {
        buf.writeBytes(this.getMsg().toByteArray());
        return true;
    }

    public boolean read(ByteBuf buf) throws Exception {
        int length = buf.readableBytes();
        byte[] array;
        int offset;
        if (buf.hasArray()) {
            array = buf.array();
            offset = buf.arrayOffset() + buf.readerIndex();
        } else {
            array = ByteBufUtil.getBytes(buf, buf.readerIndex(), length, false);
            offset = 0;
        }
        this.msg = xddq.pb.RebornTrialUnionScoreReq.newBuilder().mergeFrom(array, offset, length).build();
        return true;
    }

    public xddq.pb.RebornTrialUnionScoreReq getMsg() {
        if (msg == null) {
            msg = builder.build();
        }
        return msg;
    }

    public RebornTrialUnionScoreReq_217210Impl setMsg(xddq.pb.RebornTrialUnionScoreReq msg) {
        this.msg = msg;
        return this;
    }

    public int length() throws Exception {
        return this.getMsg().getSerializedSize();
    }

    @Override
    public int getId() {
        return 217210;
    }

    @Override
    public String toString() {
        try {
            return String.format("%d %s%s", this.getId(), getName(), format(JsonFormat.printer().print(this.getMsg())));
        } catch (InvalidProtocolBufferException ignored) {
        }
        return String.format("%d error message", this.getId());
    }
}
