package xddq.pb.message;

import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import xddq.message.pb.PbMessage;


public class HeavenBattleCampDetailResp_30508Impl extends PbMessage {

    private xddq.pb.HeavenBattleCampDetailResp msg;

    private xddq.pb.HeavenBattleCampDetailResp.Builder builder;

    public HeavenBattleCampDetailResp_30508Impl() {
    }

    public HeavenBattleCampDetailResp_30508Impl(xddq.pb.HeavenBattleCampDetailResp.Builder builder) {
        this.builder = builder;
    }

    public boolean write(ByteBuf buf) throws Exception {
        buf.writeBytes(this.getMsg().toByteArray());
        return true;
    }

    public boolean read(ByteBuf buf) throws Exception {
        int length = buf.readableBytes();
        byte[] array;
        int offset;
        if (buf.hasArray()) {
            array = buf.array();
            offset = buf.arrayOffset() + buf.readerIndex();
        } else {
            array = ByteBufUtil.getBytes(buf, buf.readerIndex(), length, false);
            offset = 0;
        }
        this.msg = xddq.pb.HeavenBattleCampDetailResp.newBuilder().mergeFrom(array, offset, length).build();
        return true;
    }

    public xddq.pb.HeavenBattleCampDetailResp getMsg() {
        if (msg == null) {
            msg = builder.build();
        }
        return msg;
    }

    public HeavenBattleCampDetailResp_30508Impl setMsg(xddq.pb.HeavenBattleCampDetailResp msg) {
        this.msg = msg;
        return this;
    }

    public int length() throws Exception {
        return this.getMsg().getSerializedSize();
    }

    @Override
    public int getId() {
        return 30508;
    }

    @Override
    public String toString() {
        try {
            return String.format("%d %s%s", this.getId(), getName(), format(JsonFormat.printer().print(this.getMsg())));
        } catch (InvalidProtocolBufferException ignored) {
        }
        return String.format("%d error message", this.getId());
    }
}
