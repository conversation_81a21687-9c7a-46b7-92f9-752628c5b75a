package xddq.pb.message;

import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import xddq.message.pb.PbMessage;


public class DoubleDemonsFindFriendResp_11107Impl extends PbMessage {

    private xddq.pb.DoubleDemonsFindFriendResp msg;

    private xddq.pb.DoubleDemonsFindFriendResp.Builder builder;

    public DoubleDemonsFindFriendResp_11107Impl() {
    }

    public DoubleDemonsFindFriendResp_11107Impl(xddq.pb.DoubleDemonsFindFriendResp.Builder builder) {
        this.builder = builder;
    }

    public boolean write(ByteBuf buf) throws Exception {
        buf.writeBytes(this.getMsg().toByteArray());
        return true;
    }

    public boolean read(ByteBuf buf) throws Exception {
        int length = buf.readableBytes();
        byte[] array;
        int offset;
        if (buf.hasArray()) {
            array = buf.array();
            offset = buf.arrayOffset() + buf.readerIndex();
        } else {
            array = ByteBufUtil.getBytes(buf, buf.readerIndex(), length, false);
            offset = 0;
        }
        this.msg = xddq.pb.DoubleDemonsFindFriendResp.newBuilder().mergeFrom(array, offset, length).build();
        return true;
    }

    public xddq.pb.DoubleDemonsFindFriendResp getMsg() {
        if (msg == null) {
            msg = builder.build();
        }
        return msg;
    }

    public DoubleDemonsFindFriendResp_11107Impl setMsg(xddq.pb.DoubleDemonsFindFriendResp msg) {
        this.msg = msg;
        return this;
    }

    public int length() throws Exception {
        return this.getMsg().getSerializedSize();
    }

    @Override
    public int getId() {
        return 11107;
    }

    @Override
    public String toString() {
        try {
            return String.format("%d %s%s", this.getId(), getName(), format(JsonFormat.printer().print(this.getMsg())));
        } catch (InvalidProtocolBufferException ignored) {
        }
        return String.format("%d error message", this.getId());
    }
}
