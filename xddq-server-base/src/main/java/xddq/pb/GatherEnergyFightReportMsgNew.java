// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.GatherEnergyFightReportMsgNew}
 */
public final class GatherEnergyFightReportMsgNew extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.GatherEnergyFightReportMsgNew)
    GatherEnergyFightReportMsgNewOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      GatherEnergyFightReportMsgNew.class.getName());
  }
  // Use GatherEnergyFightReportMsgNew.newBuilder() to construct.
  private GatherEnergyFightReportMsgNew(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private GatherEnergyFightReportMsgNew() {
    msg_ = "";
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_GatherEnergyFightReportMsgNew_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_GatherEnergyFightReportMsgNew_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.GatherEnergyFightReportMsgNew.class, xddq.pb.GatherEnergyFightReportMsgNew.Builder.class);
  }

  private int bitField0_;
  public static final int TIME_FIELD_NUMBER = 1;
  private long time_ = 0L;
  /**
   * <code>optional int64 time = 1;</code>
   * @return Whether the time field is set.
   */
  @java.lang.Override
  public boolean hasTime() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int64 time = 1;</code>
   * @return The time.
   */
  @java.lang.Override
  public long getTime() {
    return time_;
  }

  public static final int MSG_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private volatile java.lang.Object msg_ = "";
  /**
   * <code>optional string msg = 2;</code>
   * @return Whether the msg field is set.
   */
  @java.lang.Override
  public boolean hasMsg() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional string msg = 2;</code>
   * @return The msg.
   */
  @java.lang.Override
  public java.lang.String getMsg() {
    java.lang.Object ref = msg_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        msg_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string msg = 2;</code>
   * @return The bytes for msg.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getMsgBytes() {
    java.lang.Object ref = msg_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      msg_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PLAYERINFO_FIELD_NUMBER = 3;
  private xddq.pb.ReportPlayerInfo playerInfo_;
  /**
   * <code>optional .xddq.pb.ReportPlayerInfo playerInfo = 3;</code>
   * @return Whether the playerInfo field is set.
   */
  @java.lang.Override
  public boolean hasPlayerInfo() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional .xddq.pb.ReportPlayerInfo playerInfo = 3;</code>
   * @return The playerInfo.
   */
  @java.lang.Override
  public xddq.pb.ReportPlayerInfo getPlayerInfo() {
    return playerInfo_ == null ? xddq.pb.ReportPlayerInfo.getDefaultInstance() : playerInfo_;
  }
  /**
   * <code>optional .xddq.pb.ReportPlayerInfo playerInfo = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.ReportPlayerInfoOrBuilder getPlayerInfoOrBuilder() {
    return playerInfo_ == null ? xddq.pb.ReportPlayerInfo.getDefaultInstance() : playerInfo_;
  }

  public static final int STATE_FIELD_NUMBER = 4;
  private int state_ = 0;
  /**
   * <code>optional int32 state = 4;</code>
   * @return Whether the state field is set.
   */
  @java.lang.Override
  public boolean hasState() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional int32 state = 4;</code>
   * @return The state.
   */
  @java.lang.Override
  public int getState() {
    return state_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt64(1, time_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 2, msg_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeMessage(3, getPlayerInfo());
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt32(4, state_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(1, time_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(2, msg_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, getPlayerInfo());
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(4, state_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.GatherEnergyFightReportMsgNew)) {
      return super.equals(obj);
    }
    xddq.pb.GatherEnergyFightReportMsgNew other = (xddq.pb.GatherEnergyFightReportMsgNew) obj;

    if (hasTime() != other.hasTime()) return false;
    if (hasTime()) {
      if (getTime()
          != other.getTime()) return false;
    }
    if (hasMsg() != other.hasMsg()) return false;
    if (hasMsg()) {
      if (!getMsg()
          .equals(other.getMsg())) return false;
    }
    if (hasPlayerInfo() != other.hasPlayerInfo()) return false;
    if (hasPlayerInfo()) {
      if (!getPlayerInfo()
          .equals(other.getPlayerInfo())) return false;
    }
    if (hasState() != other.hasState()) return false;
    if (hasState()) {
      if (getState()
          != other.getState()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasTime()) {
      hash = (37 * hash) + TIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTime());
    }
    if (hasMsg()) {
      hash = (37 * hash) + MSG_FIELD_NUMBER;
      hash = (53 * hash) + getMsg().hashCode();
    }
    if (hasPlayerInfo()) {
      hash = (37 * hash) + PLAYERINFO_FIELD_NUMBER;
      hash = (53 * hash) + getPlayerInfo().hashCode();
    }
    if (hasState()) {
      hash = (37 * hash) + STATE_FIELD_NUMBER;
      hash = (53 * hash) + getState();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.GatherEnergyFightReportMsgNew parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GatherEnergyFightReportMsgNew parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GatherEnergyFightReportMsgNew parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GatherEnergyFightReportMsgNew parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GatherEnergyFightReportMsgNew parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GatherEnergyFightReportMsgNew parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GatherEnergyFightReportMsgNew parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.GatherEnergyFightReportMsgNew parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.GatherEnergyFightReportMsgNew parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.GatherEnergyFightReportMsgNew parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.GatherEnergyFightReportMsgNew parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.GatherEnergyFightReportMsgNew parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.GatherEnergyFightReportMsgNew prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.GatherEnergyFightReportMsgNew}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.GatherEnergyFightReportMsgNew)
      xddq.pb.GatherEnergyFightReportMsgNewOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GatherEnergyFightReportMsgNew_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GatherEnergyFightReportMsgNew_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.GatherEnergyFightReportMsgNew.class, xddq.pb.GatherEnergyFightReportMsgNew.Builder.class);
    }

    // Construct using xddq.pb.GatherEnergyFightReportMsgNew.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetPlayerInfoFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      time_ = 0L;
      msg_ = "";
      playerInfo_ = null;
      if (playerInfoBuilder_ != null) {
        playerInfoBuilder_.dispose();
        playerInfoBuilder_ = null;
      }
      state_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GatherEnergyFightReportMsgNew_descriptor;
    }

    @java.lang.Override
    public xddq.pb.GatherEnergyFightReportMsgNew getDefaultInstanceForType() {
      return xddq.pb.GatherEnergyFightReportMsgNew.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.GatherEnergyFightReportMsgNew build() {
      xddq.pb.GatherEnergyFightReportMsgNew result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.GatherEnergyFightReportMsgNew buildPartial() {
      xddq.pb.GatherEnergyFightReportMsgNew result = new xddq.pb.GatherEnergyFightReportMsgNew(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.GatherEnergyFightReportMsgNew result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.time_ = time_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.msg_ = msg_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.playerInfo_ = playerInfoBuilder_ == null
            ? playerInfo_
            : playerInfoBuilder_.build();
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.state_ = state_;
        to_bitField0_ |= 0x00000008;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.GatherEnergyFightReportMsgNew) {
        return mergeFrom((xddq.pb.GatherEnergyFightReportMsgNew)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.GatherEnergyFightReportMsgNew other) {
      if (other == xddq.pb.GatherEnergyFightReportMsgNew.getDefaultInstance()) return this;
      if (other.hasTime()) {
        setTime(other.getTime());
      }
      if (other.hasMsg()) {
        msg_ = other.msg_;
        bitField0_ |= 0x00000002;
        onChanged();
      }
      if (other.hasPlayerInfo()) {
        mergePlayerInfo(other.getPlayerInfo());
      }
      if (other.hasState()) {
        setState(other.getState());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              time_ = input.readInt64();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              msg_ = input.readBytes();
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 26: {
              input.readMessage(
                  internalGetPlayerInfoFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 32: {
              state_ = input.readInt32();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private long time_ ;
    /**
     * <code>optional int64 time = 1;</code>
     * @return Whether the time field is set.
     */
    @java.lang.Override
    public boolean hasTime() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 time = 1;</code>
     * @return The time.
     */
    @java.lang.Override
    public long getTime() {
      return time_;
    }
    /**
     * <code>optional int64 time = 1;</code>
     * @param value The time to set.
     * @return This builder for chaining.
     */
    public Builder setTime(long value) {

      time_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 time = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearTime() {
      bitField0_ = (bitField0_ & ~0x00000001);
      time_ = 0L;
      onChanged();
      return this;
    }

    private java.lang.Object msg_ = "";
    /**
     * <code>optional string msg = 2;</code>
     * @return Whether the msg field is set.
     */
    public boolean hasMsg() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional string msg = 2;</code>
     * @return The msg.
     */
    public java.lang.String getMsg() {
      java.lang.Object ref = msg_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          msg_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string msg = 2;</code>
     * @return The bytes for msg.
     */
    public com.google.protobuf.ByteString
        getMsgBytes() {
      java.lang.Object ref = msg_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        msg_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string msg = 2;</code>
     * @param value The msg to set.
     * @return This builder for chaining.
     */
    public Builder setMsg(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      msg_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional string msg = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearMsg() {
      msg_ = getDefaultInstance().getMsg();
      bitField0_ = (bitField0_ & ~0x00000002);
      onChanged();
      return this;
    }
    /**
     * <code>optional string msg = 2;</code>
     * @param value The bytes for msg to set.
     * @return This builder for chaining.
     */
    public Builder setMsgBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      msg_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }

    private xddq.pb.ReportPlayerInfo playerInfo_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.ReportPlayerInfo, xddq.pb.ReportPlayerInfo.Builder, xddq.pb.ReportPlayerInfoOrBuilder> playerInfoBuilder_;
    /**
     * <code>optional .xddq.pb.ReportPlayerInfo playerInfo = 3;</code>
     * @return Whether the playerInfo field is set.
     */
    public boolean hasPlayerInfo() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional .xddq.pb.ReportPlayerInfo playerInfo = 3;</code>
     * @return The playerInfo.
     */
    public xddq.pb.ReportPlayerInfo getPlayerInfo() {
      if (playerInfoBuilder_ == null) {
        return playerInfo_ == null ? xddq.pb.ReportPlayerInfo.getDefaultInstance() : playerInfo_;
      } else {
        return playerInfoBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.ReportPlayerInfo playerInfo = 3;</code>
     */
    public Builder setPlayerInfo(xddq.pb.ReportPlayerInfo value) {
      if (playerInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        playerInfo_ = value;
      } else {
        playerInfoBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ReportPlayerInfo playerInfo = 3;</code>
     */
    public Builder setPlayerInfo(
        xddq.pb.ReportPlayerInfo.Builder builderForValue) {
      if (playerInfoBuilder_ == null) {
        playerInfo_ = builderForValue.build();
      } else {
        playerInfoBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ReportPlayerInfo playerInfo = 3;</code>
     */
    public Builder mergePlayerInfo(xddq.pb.ReportPlayerInfo value) {
      if (playerInfoBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0) &&
          playerInfo_ != null &&
          playerInfo_ != xddq.pb.ReportPlayerInfo.getDefaultInstance()) {
          getPlayerInfoBuilder().mergeFrom(value);
        } else {
          playerInfo_ = value;
        }
      } else {
        playerInfoBuilder_.mergeFrom(value);
      }
      if (playerInfo_ != null) {
        bitField0_ |= 0x00000004;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.ReportPlayerInfo playerInfo = 3;</code>
     */
    public Builder clearPlayerInfo() {
      bitField0_ = (bitField0_ & ~0x00000004);
      playerInfo_ = null;
      if (playerInfoBuilder_ != null) {
        playerInfoBuilder_.dispose();
        playerInfoBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ReportPlayerInfo playerInfo = 3;</code>
     */
    public xddq.pb.ReportPlayerInfo.Builder getPlayerInfoBuilder() {
      bitField0_ |= 0x00000004;
      onChanged();
      return internalGetPlayerInfoFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.ReportPlayerInfo playerInfo = 3;</code>
     */
    public xddq.pb.ReportPlayerInfoOrBuilder getPlayerInfoOrBuilder() {
      if (playerInfoBuilder_ != null) {
        return playerInfoBuilder_.getMessageOrBuilder();
      } else {
        return playerInfo_ == null ?
            xddq.pb.ReportPlayerInfo.getDefaultInstance() : playerInfo_;
      }
    }
    /**
     * <code>optional .xddq.pb.ReportPlayerInfo playerInfo = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.ReportPlayerInfo, xddq.pb.ReportPlayerInfo.Builder, xddq.pb.ReportPlayerInfoOrBuilder> 
        internalGetPlayerInfoFieldBuilder() {
      if (playerInfoBuilder_ == null) {
        playerInfoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.ReportPlayerInfo, xddq.pb.ReportPlayerInfo.Builder, xddq.pb.ReportPlayerInfoOrBuilder>(
                getPlayerInfo(),
                getParentForChildren(),
                isClean());
        playerInfo_ = null;
      }
      return playerInfoBuilder_;
    }

    private int state_ ;
    /**
     * <code>optional int32 state = 4;</code>
     * @return Whether the state field is set.
     */
    @java.lang.Override
    public boolean hasState() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int32 state = 4;</code>
     * @return The state.
     */
    @java.lang.Override
    public int getState() {
      return state_;
    }
    /**
     * <code>optional int32 state = 4;</code>
     * @param value The state to set.
     * @return This builder for chaining.
     */
    public Builder setState(int value) {

      state_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 state = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearState() {
      bitField0_ = (bitField0_ & ~0x00000008);
      state_ = 0;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.GatherEnergyFightReportMsgNew)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.GatherEnergyFightReportMsgNew)
  private static final xddq.pb.GatherEnergyFightReportMsgNew DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.GatherEnergyFightReportMsgNew();
  }

  public static xddq.pb.GatherEnergyFightReportMsgNew getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<GatherEnergyFightReportMsgNew>
      PARSER = new com.google.protobuf.AbstractParser<GatherEnergyFightReportMsgNew>() {
    @java.lang.Override
    public GatherEnergyFightReportMsgNew parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<GatherEnergyFightReportMsgNew> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<GatherEnergyFightReportMsgNew> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.GatherEnergyFightReportMsgNew getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

