// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.DreamLvUpSpeedUpResp}
 */
public final class DreamLvUpSpeedUpResp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.DreamLvUpSpeedUpResp)
    DreamLvUpSpeedUpRespOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      DreamLvUpSpeedUpResp.class.getName());
  }
  // Use DreamLvUpSpeedUpResp.newBuilder() to construct.
  private DreamLvUpSpeedUpResp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private DreamLvUpSpeedUpResp() {
    reward_ = "";
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_DreamLvUpSpeedUpResp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_DreamLvUpSpeedUpResp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.DreamLvUpSpeedUpResp.class, xddq.pb.DreamLvUpSpeedUpResp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int DREAMDATAMSG_FIELD_NUMBER = 2;
  private xddq.pb.DreamDataMsg dreamDataMsg_;
  /**
   * <code>optional .xddq.pb.DreamDataMsg dreamDataMsg = 2;</code>
   * @return Whether the dreamDataMsg field is set.
   */
  @java.lang.Override
  public boolean hasDreamDataMsg() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional .xddq.pb.DreamDataMsg dreamDataMsg = 2;</code>
   * @return The dreamDataMsg.
   */
  @java.lang.Override
  public xddq.pb.DreamDataMsg getDreamDataMsg() {
    return dreamDataMsg_ == null ? xddq.pb.DreamDataMsg.getDefaultInstance() : dreamDataMsg_;
  }
  /**
   * <code>optional .xddq.pb.DreamDataMsg dreamDataMsg = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.DreamDataMsgOrBuilder getDreamDataMsgOrBuilder() {
    return dreamDataMsg_ == null ? xddq.pb.DreamDataMsg.getDefaultInstance() : dreamDataMsg_;
  }

  public static final int REWARD_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private volatile java.lang.Object reward_ = "";
  /**
   * <code>optional string reward = 3;</code>
   * @return Whether the reward field is set.
   */
  @java.lang.Override
  public boolean hasReward() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional string reward = 3;</code>
   * @return The reward.
   */
  @java.lang.Override
  public java.lang.String getReward() {
    java.lang.Object ref = reward_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        reward_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string reward = 3;</code>
   * @return The bytes for reward.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getRewardBytes() {
    java.lang.Object ref = reward_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      reward_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (hasDreamDataMsg()) {
      if (!getDreamDataMsg().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeMessage(2, getDreamDataMsg());
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 3, reward_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getDreamDataMsg());
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(3, reward_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.DreamLvUpSpeedUpResp)) {
      return super.equals(obj);
    }
    xddq.pb.DreamLvUpSpeedUpResp other = (xddq.pb.DreamLvUpSpeedUpResp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (hasDreamDataMsg() != other.hasDreamDataMsg()) return false;
    if (hasDreamDataMsg()) {
      if (!getDreamDataMsg()
          .equals(other.getDreamDataMsg())) return false;
    }
    if (hasReward() != other.hasReward()) return false;
    if (hasReward()) {
      if (!getReward()
          .equals(other.getReward())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (hasDreamDataMsg()) {
      hash = (37 * hash) + DREAMDATAMSG_FIELD_NUMBER;
      hash = (53 * hash) + getDreamDataMsg().hashCode();
    }
    if (hasReward()) {
      hash = (37 * hash) + REWARD_FIELD_NUMBER;
      hash = (53 * hash) + getReward().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.DreamLvUpSpeedUpResp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.DreamLvUpSpeedUpResp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.DreamLvUpSpeedUpResp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.DreamLvUpSpeedUpResp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.DreamLvUpSpeedUpResp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.DreamLvUpSpeedUpResp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.DreamLvUpSpeedUpResp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.DreamLvUpSpeedUpResp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.DreamLvUpSpeedUpResp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.DreamLvUpSpeedUpResp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.DreamLvUpSpeedUpResp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.DreamLvUpSpeedUpResp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.DreamLvUpSpeedUpResp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.DreamLvUpSpeedUpResp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.DreamLvUpSpeedUpResp)
      xddq.pb.DreamLvUpSpeedUpRespOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_DreamLvUpSpeedUpResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_DreamLvUpSpeedUpResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.DreamLvUpSpeedUpResp.class, xddq.pb.DreamLvUpSpeedUpResp.Builder.class);
    }

    // Construct using xddq.pb.DreamLvUpSpeedUpResp.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetDreamDataMsgFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      dreamDataMsg_ = null;
      if (dreamDataMsgBuilder_ != null) {
        dreamDataMsgBuilder_.dispose();
        dreamDataMsgBuilder_ = null;
      }
      reward_ = "";
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_DreamLvUpSpeedUpResp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.DreamLvUpSpeedUpResp getDefaultInstanceForType() {
      return xddq.pb.DreamLvUpSpeedUpResp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.DreamLvUpSpeedUpResp build() {
      xddq.pb.DreamLvUpSpeedUpResp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.DreamLvUpSpeedUpResp buildPartial() {
      xddq.pb.DreamLvUpSpeedUpResp result = new xddq.pb.DreamLvUpSpeedUpResp(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.DreamLvUpSpeedUpResp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.dreamDataMsg_ = dreamDataMsgBuilder_ == null
            ? dreamDataMsg_
            : dreamDataMsgBuilder_.build();
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.reward_ = reward_;
        to_bitField0_ |= 0x00000004;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.DreamLvUpSpeedUpResp) {
        return mergeFrom((xddq.pb.DreamLvUpSpeedUpResp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.DreamLvUpSpeedUpResp other) {
      if (other == xddq.pb.DreamLvUpSpeedUpResp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (other.hasDreamDataMsg()) {
        mergeDreamDataMsg(other.getDreamDataMsg());
      }
      if (other.hasReward()) {
        reward_ = other.reward_;
        bitField0_ |= 0x00000004;
        onChanged();
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      if (hasDreamDataMsg()) {
        if (!getDreamDataMsg().isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              input.readMessage(
                  internalGetDreamDataMsgFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 26: {
              reward_ = input.readBytes();
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private xddq.pb.DreamDataMsg dreamDataMsg_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.DreamDataMsg, xddq.pb.DreamDataMsg.Builder, xddq.pb.DreamDataMsgOrBuilder> dreamDataMsgBuilder_;
    /**
     * <code>optional .xddq.pb.DreamDataMsg dreamDataMsg = 2;</code>
     * @return Whether the dreamDataMsg field is set.
     */
    public boolean hasDreamDataMsg() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .xddq.pb.DreamDataMsg dreamDataMsg = 2;</code>
     * @return The dreamDataMsg.
     */
    public xddq.pb.DreamDataMsg getDreamDataMsg() {
      if (dreamDataMsgBuilder_ == null) {
        return dreamDataMsg_ == null ? xddq.pb.DreamDataMsg.getDefaultInstance() : dreamDataMsg_;
      } else {
        return dreamDataMsgBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.DreamDataMsg dreamDataMsg = 2;</code>
     */
    public Builder setDreamDataMsg(xddq.pb.DreamDataMsg value) {
      if (dreamDataMsgBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        dreamDataMsg_ = value;
      } else {
        dreamDataMsgBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.DreamDataMsg dreamDataMsg = 2;</code>
     */
    public Builder setDreamDataMsg(
        xddq.pb.DreamDataMsg.Builder builderForValue) {
      if (dreamDataMsgBuilder_ == null) {
        dreamDataMsg_ = builderForValue.build();
      } else {
        dreamDataMsgBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.DreamDataMsg dreamDataMsg = 2;</code>
     */
    public Builder mergeDreamDataMsg(xddq.pb.DreamDataMsg value) {
      if (dreamDataMsgBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0) &&
          dreamDataMsg_ != null &&
          dreamDataMsg_ != xddq.pb.DreamDataMsg.getDefaultInstance()) {
          getDreamDataMsgBuilder().mergeFrom(value);
        } else {
          dreamDataMsg_ = value;
        }
      } else {
        dreamDataMsgBuilder_.mergeFrom(value);
      }
      if (dreamDataMsg_ != null) {
        bitField0_ |= 0x00000002;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.DreamDataMsg dreamDataMsg = 2;</code>
     */
    public Builder clearDreamDataMsg() {
      bitField0_ = (bitField0_ & ~0x00000002);
      dreamDataMsg_ = null;
      if (dreamDataMsgBuilder_ != null) {
        dreamDataMsgBuilder_.dispose();
        dreamDataMsgBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.DreamDataMsg dreamDataMsg = 2;</code>
     */
    public xddq.pb.DreamDataMsg.Builder getDreamDataMsgBuilder() {
      bitField0_ |= 0x00000002;
      onChanged();
      return internalGetDreamDataMsgFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.DreamDataMsg dreamDataMsg = 2;</code>
     */
    public xddq.pb.DreamDataMsgOrBuilder getDreamDataMsgOrBuilder() {
      if (dreamDataMsgBuilder_ != null) {
        return dreamDataMsgBuilder_.getMessageOrBuilder();
      } else {
        return dreamDataMsg_ == null ?
            xddq.pb.DreamDataMsg.getDefaultInstance() : dreamDataMsg_;
      }
    }
    /**
     * <code>optional .xddq.pb.DreamDataMsg dreamDataMsg = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.DreamDataMsg, xddq.pb.DreamDataMsg.Builder, xddq.pb.DreamDataMsgOrBuilder> 
        internalGetDreamDataMsgFieldBuilder() {
      if (dreamDataMsgBuilder_ == null) {
        dreamDataMsgBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.DreamDataMsg, xddq.pb.DreamDataMsg.Builder, xddq.pb.DreamDataMsgOrBuilder>(
                getDreamDataMsg(),
                getParentForChildren(),
                isClean());
        dreamDataMsg_ = null;
      }
      return dreamDataMsgBuilder_;
    }

    private java.lang.Object reward_ = "";
    /**
     * <code>optional string reward = 3;</code>
     * @return Whether the reward field is set.
     */
    public boolean hasReward() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional string reward = 3;</code>
     * @return The reward.
     */
    public java.lang.String getReward() {
      java.lang.Object ref = reward_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          reward_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string reward = 3;</code>
     * @return The bytes for reward.
     */
    public com.google.protobuf.ByteString
        getRewardBytes() {
      java.lang.Object ref = reward_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        reward_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string reward = 3;</code>
     * @param value The reward to set.
     * @return This builder for chaining.
     */
    public Builder setReward(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      reward_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional string reward = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearReward() {
      reward_ = getDefaultInstance().getReward();
      bitField0_ = (bitField0_ & ~0x00000004);
      onChanged();
      return this;
    }
    /**
     * <code>optional string reward = 3;</code>
     * @param value The bytes for reward to set.
     * @return This builder for chaining.
     */
    public Builder setRewardBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      reward_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.DreamLvUpSpeedUpResp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.DreamLvUpSpeedUpResp)
  private static final xddq.pb.DreamLvUpSpeedUpResp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.DreamLvUpSpeedUpResp();
  }

  public static xddq.pb.DreamLvUpSpeedUpResp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<DreamLvUpSpeedUpResp>
      PARSER = new com.google.protobuf.AbstractParser<DreamLvUpSpeedUpResp>() {
    @java.lang.Override
    public DreamLvUpSpeedUpResp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<DreamLvUpSpeedUpResp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<DreamLvUpSpeedUpResp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.DreamLvUpSpeedUpResp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

