// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.AssistantSyncTrialInfoResp}
 */
public final class AssistantSyncTrialInfoResp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.AssistantSyncTrialInfoResp)
    AssistantSyncTrialInfoRespOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      AssistantSyncTrialInfoResp.class.getName());
  }
  // Use AssistantSyncTrialInfoResp.newBuilder() to construct.
  private AssistantSyncTrialInfoResp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private AssistantSyncTrialInfoResp() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_AssistantSyncTrialInfoResp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_AssistantSyncTrialInfoResp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.AssistantSyncTrialInfoResp.class, xddq.pb.AssistantSyncTrialInfoResp.Builder.class);
  }

  private int bitField0_;
  public static final int ASSISTANTTRIALMSG_FIELD_NUMBER = 1;
  private xddq.pb.AssistantTrialMsg assistantTrialMsg_;
  /**
   * <code>required .xddq.pb.AssistantTrialMsg assistantTrialMsg = 1;</code>
   * @return Whether the assistantTrialMsg field is set.
   */
  @java.lang.Override
  public boolean hasAssistantTrialMsg() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required .xddq.pb.AssistantTrialMsg assistantTrialMsg = 1;</code>
   * @return The assistantTrialMsg.
   */
  @java.lang.Override
  public xddq.pb.AssistantTrialMsg getAssistantTrialMsg() {
    return assistantTrialMsg_ == null ? xddq.pb.AssistantTrialMsg.getDefaultInstance() : assistantTrialMsg_;
  }
  /**
   * <code>required .xddq.pb.AssistantTrialMsg assistantTrialMsg = 1;</code>
   */
  @java.lang.Override
  public xddq.pb.AssistantTrialMsgOrBuilder getAssistantTrialMsgOrBuilder() {
    return assistantTrialMsg_ == null ? xddq.pb.AssistantTrialMsg.getDefaultInstance() : assistantTrialMsg_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasAssistantTrialMsg()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!getAssistantTrialMsg().isInitialized()) {
      memoizedIsInitialized = 0;
      return false;
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeMessage(1, getAssistantTrialMsg());
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, getAssistantTrialMsg());
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.AssistantSyncTrialInfoResp)) {
      return super.equals(obj);
    }
    xddq.pb.AssistantSyncTrialInfoResp other = (xddq.pb.AssistantSyncTrialInfoResp) obj;

    if (hasAssistantTrialMsg() != other.hasAssistantTrialMsg()) return false;
    if (hasAssistantTrialMsg()) {
      if (!getAssistantTrialMsg()
          .equals(other.getAssistantTrialMsg())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasAssistantTrialMsg()) {
      hash = (37 * hash) + ASSISTANTTRIALMSG_FIELD_NUMBER;
      hash = (53 * hash) + getAssistantTrialMsg().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.AssistantSyncTrialInfoResp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.AssistantSyncTrialInfoResp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.AssistantSyncTrialInfoResp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.AssistantSyncTrialInfoResp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.AssistantSyncTrialInfoResp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.AssistantSyncTrialInfoResp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.AssistantSyncTrialInfoResp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.AssistantSyncTrialInfoResp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.AssistantSyncTrialInfoResp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.AssistantSyncTrialInfoResp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.AssistantSyncTrialInfoResp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.AssistantSyncTrialInfoResp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.AssistantSyncTrialInfoResp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.AssistantSyncTrialInfoResp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.AssistantSyncTrialInfoResp)
      xddq.pb.AssistantSyncTrialInfoRespOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_AssistantSyncTrialInfoResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_AssistantSyncTrialInfoResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.AssistantSyncTrialInfoResp.class, xddq.pb.AssistantSyncTrialInfoResp.Builder.class);
    }

    // Construct using xddq.pb.AssistantSyncTrialInfoResp.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetAssistantTrialMsgFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      assistantTrialMsg_ = null;
      if (assistantTrialMsgBuilder_ != null) {
        assistantTrialMsgBuilder_.dispose();
        assistantTrialMsgBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_AssistantSyncTrialInfoResp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.AssistantSyncTrialInfoResp getDefaultInstanceForType() {
      return xddq.pb.AssistantSyncTrialInfoResp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.AssistantSyncTrialInfoResp build() {
      xddq.pb.AssistantSyncTrialInfoResp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.AssistantSyncTrialInfoResp buildPartial() {
      xddq.pb.AssistantSyncTrialInfoResp result = new xddq.pb.AssistantSyncTrialInfoResp(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.AssistantSyncTrialInfoResp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.assistantTrialMsg_ = assistantTrialMsgBuilder_ == null
            ? assistantTrialMsg_
            : assistantTrialMsgBuilder_.build();
        to_bitField0_ |= 0x00000001;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.AssistantSyncTrialInfoResp) {
        return mergeFrom((xddq.pb.AssistantSyncTrialInfoResp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.AssistantSyncTrialInfoResp other) {
      if (other == xddq.pb.AssistantSyncTrialInfoResp.getDefaultInstance()) return this;
      if (other.hasAssistantTrialMsg()) {
        mergeAssistantTrialMsg(other.getAssistantTrialMsg());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasAssistantTrialMsg()) {
        return false;
      }
      if (!getAssistantTrialMsg().isInitialized()) {
        return false;
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              input.readMessage(
                  internalGetAssistantTrialMsgFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private xddq.pb.AssistantTrialMsg assistantTrialMsg_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.AssistantTrialMsg, xddq.pb.AssistantTrialMsg.Builder, xddq.pb.AssistantTrialMsgOrBuilder> assistantTrialMsgBuilder_;
    /**
     * <code>required .xddq.pb.AssistantTrialMsg assistantTrialMsg = 1;</code>
     * @return Whether the assistantTrialMsg field is set.
     */
    public boolean hasAssistantTrialMsg() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required .xddq.pb.AssistantTrialMsg assistantTrialMsg = 1;</code>
     * @return The assistantTrialMsg.
     */
    public xddq.pb.AssistantTrialMsg getAssistantTrialMsg() {
      if (assistantTrialMsgBuilder_ == null) {
        return assistantTrialMsg_ == null ? xddq.pb.AssistantTrialMsg.getDefaultInstance() : assistantTrialMsg_;
      } else {
        return assistantTrialMsgBuilder_.getMessage();
      }
    }
    /**
     * <code>required .xddq.pb.AssistantTrialMsg assistantTrialMsg = 1;</code>
     */
    public Builder setAssistantTrialMsg(xddq.pb.AssistantTrialMsg value) {
      if (assistantTrialMsgBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        assistantTrialMsg_ = value;
      } else {
        assistantTrialMsgBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required .xddq.pb.AssistantTrialMsg assistantTrialMsg = 1;</code>
     */
    public Builder setAssistantTrialMsg(
        xddq.pb.AssistantTrialMsg.Builder builderForValue) {
      if (assistantTrialMsgBuilder_ == null) {
        assistantTrialMsg_ = builderForValue.build();
      } else {
        assistantTrialMsgBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required .xddq.pb.AssistantTrialMsg assistantTrialMsg = 1;</code>
     */
    public Builder mergeAssistantTrialMsg(xddq.pb.AssistantTrialMsg value) {
      if (assistantTrialMsgBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0) &&
          assistantTrialMsg_ != null &&
          assistantTrialMsg_ != xddq.pb.AssistantTrialMsg.getDefaultInstance()) {
          getAssistantTrialMsgBuilder().mergeFrom(value);
        } else {
          assistantTrialMsg_ = value;
        }
      } else {
        assistantTrialMsgBuilder_.mergeFrom(value);
      }
      if (assistantTrialMsg_ != null) {
        bitField0_ |= 0x00000001;
        onChanged();
      }
      return this;
    }
    /**
     * <code>required .xddq.pb.AssistantTrialMsg assistantTrialMsg = 1;</code>
     */
    public Builder clearAssistantTrialMsg() {
      bitField0_ = (bitField0_ & ~0x00000001);
      assistantTrialMsg_ = null;
      if (assistantTrialMsgBuilder_ != null) {
        assistantTrialMsgBuilder_.dispose();
        assistantTrialMsgBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>required .xddq.pb.AssistantTrialMsg assistantTrialMsg = 1;</code>
     */
    public xddq.pb.AssistantTrialMsg.Builder getAssistantTrialMsgBuilder() {
      bitField0_ |= 0x00000001;
      onChanged();
      return internalGetAssistantTrialMsgFieldBuilder().getBuilder();
    }
    /**
     * <code>required .xddq.pb.AssistantTrialMsg assistantTrialMsg = 1;</code>
     */
    public xddq.pb.AssistantTrialMsgOrBuilder getAssistantTrialMsgOrBuilder() {
      if (assistantTrialMsgBuilder_ != null) {
        return assistantTrialMsgBuilder_.getMessageOrBuilder();
      } else {
        return assistantTrialMsg_ == null ?
            xddq.pb.AssistantTrialMsg.getDefaultInstance() : assistantTrialMsg_;
      }
    }
    /**
     * <code>required .xddq.pb.AssistantTrialMsg assistantTrialMsg = 1;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.AssistantTrialMsg, xddq.pb.AssistantTrialMsg.Builder, xddq.pb.AssistantTrialMsgOrBuilder> 
        internalGetAssistantTrialMsgFieldBuilder() {
      if (assistantTrialMsgBuilder_ == null) {
        assistantTrialMsgBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.AssistantTrialMsg, xddq.pb.AssistantTrialMsg.Builder, xddq.pb.AssistantTrialMsgOrBuilder>(
                getAssistantTrialMsg(),
                getParentForChildren(),
                isClean());
        assistantTrialMsg_ = null;
      }
      return assistantTrialMsgBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.AssistantSyncTrialInfoResp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.AssistantSyncTrialInfoResp)
  private static final xddq.pb.AssistantSyncTrialInfoResp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.AssistantSyncTrialInfoResp();
  }

  public static xddq.pb.AssistantSyncTrialInfoResp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<AssistantSyncTrialInfoResp>
      PARSER = new com.google.protobuf.AbstractParser<AssistantSyncTrialInfoResp>() {
    @java.lang.Override
    public AssistantSyncTrialInfoResp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<AssistantSyncTrialInfoResp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<AssistantSyncTrialInfoResp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.AssistantSyncTrialInfoResp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

