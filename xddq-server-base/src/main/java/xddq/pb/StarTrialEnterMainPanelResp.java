// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.StarTrialEnterMainPanelResp}
 */
public final class StarTrialEnterMainPanelResp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.StarTrialEnterMainPanelResp)
    StarTrialEnterMainPanelRespOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      StarTrialEnterMainPanelResp.class.getName());
  }
  // Use StarTrialEnterMainPanelResp.newBuilder() to construct.
  private StarTrialEnterMainPanelResp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private StarTrialEnterMainPanelResp() {
    playerHeadAndNameMsg_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_StarTrialEnterMainPanelResp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_StarTrialEnterMainPanelResp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.StarTrialEnterMainPanelResp.class, xddq.pb.StarTrialEnterMainPanelResp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>optional int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int PLAYERHEADANDNAMEMSG_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.PlayerHeadAndNameMsg> playerHeadAndNameMsg_;
  /**
   * <code>repeated .xddq.pb.PlayerHeadAndNameMsg playerHeadAndNameMsg = 2;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.PlayerHeadAndNameMsg> getPlayerHeadAndNameMsgList() {
    return playerHeadAndNameMsg_;
  }
  /**
   * <code>repeated .xddq.pb.PlayerHeadAndNameMsg playerHeadAndNameMsg = 2;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.PlayerHeadAndNameMsgOrBuilder> 
      getPlayerHeadAndNameMsgOrBuilderList() {
    return playerHeadAndNameMsg_;
  }
  /**
   * <code>repeated .xddq.pb.PlayerHeadAndNameMsg playerHeadAndNameMsg = 2;</code>
   */
  @java.lang.Override
  public int getPlayerHeadAndNameMsgCount() {
    return playerHeadAndNameMsg_.size();
  }
  /**
   * <code>repeated .xddq.pb.PlayerHeadAndNameMsg playerHeadAndNameMsg = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.PlayerHeadAndNameMsg getPlayerHeadAndNameMsg(int index) {
    return playerHeadAndNameMsg_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.PlayerHeadAndNameMsg playerHeadAndNameMsg = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.PlayerHeadAndNameMsgOrBuilder getPlayerHeadAndNameMsgOrBuilder(
      int index) {
    return playerHeadAndNameMsg_.get(index);
  }

  public static final int DATAMSG_FIELD_NUMBER = 3;
  private xddq.pb.StarTrialDataMsg dataMsg_;
  /**
   * <code>optional .xddq.pb.StarTrialDataMsg dataMsg = 3;</code>
   * @return Whether the dataMsg field is set.
   */
  @java.lang.Override
  public boolean hasDataMsg() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional .xddq.pb.StarTrialDataMsg dataMsg = 3;</code>
   * @return The dataMsg.
   */
  @java.lang.Override
  public xddq.pb.StarTrialDataMsg getDataMsg() {
    return dataMsg_ == null ? xddq.pb.StarTrialDataMsg.getDefaultInstance() : dataMsg_;
  }
  /**
   * <code>optional .xddq.pb.StarTrialDataMsg dataMsg = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.StarTrialDataMsgOrBuilder getDataMsgOrBuilder() {
    return dataMsg_ == null ? xddq.pb.StarTrialDataMsg.getDefaultInstance() : dataMsg_;
  }

  public static final int CONSUMESCORE_FIELD_NUMBER = 4;
  private long consumeScore_ = 0L;
  /**
   * <code>optional int64 consumeScore = 4;</code>
   * @return Whether the consumeScore field is set.
   */
  @java.lang.Override
  public boolean hasConsumeScore() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional int64 consumeScore = 4;</code>
   * @return The consumeScore.
   */
  @java.lang.Override
  public long getConsumeScore() {
    return consumeScore_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (hasDataMsg()) {
      if (!getDataMsg().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    for (int i = 0; i < playerHeadAndNameMsg_.size(); i++) {
      output.writeMessage(2, playerHeadAndNameMsg_.get(i));
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeMessage(3, getDataMsg());
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt64(4, consumeScore_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    for (int i = 0; i < playerHeadAndNameMsg_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, playerHeadAndNameMsg_.get(i));
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, getDataMsg());
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(4, consumeScore_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.StarTrialEnterMainPanelResp)) {
      return super.equals(obj);
    }
    xddq.pb.StarTrialEnterMainPanelResp other = (xddq.pb.StarTrialEnterMainPanelResp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (!getPlayerHeadAndNameMsgList()
        .equals(other.getPlayerHeadAndNameMsgList())) return false;
    if (hasDataMsg() != other.hasDataMsg()) return false;
    if (hasDataMsg()) {
      if (!getDataMsg()
          .equals(other.getDataMsg())) return false;
    }
    if (hasConsumeScore() != other.hasConsumeScore()) return false;
    if (hasConsumeScore()) {
      if (getConsumeScore()
          != other.getConsumeScore()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (getPlayerHeadAndNameMsgCount() > 0) {
      hash = (37 * hash) + PLAYERHEADANDNAMEMSG_FIELD_NUMBER;
      hash = (53 * hash) + getPlayerHeadAndNameMsgList().hashCode();
    }
    if (hasDataMsg()) {
      hash = (37 * hash) + DATAMSG_FIELD_NUMBER;
      hash = (53 * hash) + getDataMsg().hashCode();
    }
    if (hasConsumeScore()) {
      hash = (37 * hash) + CONSUMESCORE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getConsumeScore());
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.StarTrialEnterMainPanelResp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.StarTrialEnterMainPanelResp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.StarTrialEnterMainPanelResp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.StarTrialEnterMainPanelResp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.StarTrialEnterMainPanelResp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.StarTrialEnterMainPanelResp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.StarTrialEnterMainPanelResp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.StarTrialEnterMainPanelResp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.StarTrialEnterMainPanelResp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.StarTrialEnterMainPanelResp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.StarTrialEnterMainPanelResp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.StarTrialEnterMainPanelResp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.StarTrialEnterMainPanelResp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.StarTrialEnterMainPanelResp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.StarTrialEnterMainPanelResp)
      xddq.pb.StarTrialEnterMainPanelRespOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_StarTrialEnterMainPanelResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_StarTrialEnterMainPanelResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.StarTrialEnterMainPanelResp.class, xddq.pb.StarTrialEnterMainPanelResp.Builder.class);
    }

    // Construct using xddq.pb.StarTrialEnterMainPanelResp.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetPlayerHeadAndNameMsgFieldBuilder();
        internalGetDataMsgFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      if (playerHeadAndNameMsgBuilder_ == null) {
        playerHeadAndNameMsg_ = java.util.Collections.emptyList();
      } else {
        playerHeadAndNameMsg_ = null;
        playerHeadAndNameMsgBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000002);
      dataMsg_ = null;
      if (dataMsgBuilder_ != null) {
        dataMsgBuilder_.dispose();
        dataMsgBuilder_ = null;
      }
      consumeScore_ = 0L;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_StarTrialEnterMainPanelResp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.StarTrialEnterMainPanelResp getDefaultInstanceForType() {
      return xddq.pb.StarTrialEnterMainPanelResp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.StarTrialEnterMainPanelResp build() {
      xddq.pb.StarTrialEnterMainPanelResp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.StarTrialEnterMainPanelResp buildPartial() {
      xddq.pb.StarTrialEnterMainPanelResp result = new xddq.pb.StarTrialEnterMainPanelResp(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.StarTrialEnterMainPanelResp result) {
      if (playerHeadAndNameMsgBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          playerHeadAndNameMsg_ = java.util.Collections.unmodifiableList(playerHeadAndNameMsg_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.playerHeadAndNameMsg_ = playerHeadAndNameMsg_;
      } else {
        result.playerHeadAndNameMsg_ = playerHeadAndNameMsgBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.StarTrialEnterMainPanelResp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.dataMsg_ = dataMsgBuilder_ == null
            ? dataMsg_
            : dataMsgBuilder_.build();
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.consumeScore_ = consumeScore_;
        to_bitField0_ |= 0x00000004;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.StarTrialEnterMainPanelResp) {
        return mergeFrom((xddq.pb.StarTrialEnterMainPanelResp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.StarTrialEnterMainPanelResp other) {
      if (other == xddq.pb.StarTrialEnterMainPanelResp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (playerHeadAndNameMsgBuilder_ == null) {
        if (!other.playerHeadAndNameMsg_.isEmpty()) {
          if (playerHeadAndNameMsg_.isEmpty()) {
            playerHeadAndNameMsg_ = other.playerHeadAndNameMsg_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensurePlayerHeadAndNameMsgIsMutable();
            playerHeadAndNameMsg_.addAll(other.playerHeadAndNameMsg_);
          }
          onChanged();
        }
      } else {
        if (!other.playerHeadAndNameMsg_.isEmpty()) {
          if (playerHeadAndNameMsgBuilder_.isEmpty()) {
            playerHeadAndNameMsgBuilder_.dispose();
            playerHeadAndNameMsgBuilder_ = null;
            playerHeadAndNameMsg_ = other.playerHeadAndNameMsg_;
            bitField0_ = (bitField0_ & ~0x00000002);
            playerHeadAndNameMsgBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetPlayerHeadAndNameMsgFieldBuilder() : null;
          } else {
            playerHeadAndNameMsgBuilder_.addAllMessages(other.playerHeadAndNameMsg_);
          }
        }
      }
      if (other.hasDataMsg()) {
        mergeDataMsg(other.getDataMsg());
      }
      if (other.hasConsumeScore()) {
        setConsumeScore(other.getConsumeScore());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (hasDataMsg()) {
        if (!getDataMsg().isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              xddq.pb.PlayerHeadAndNameMsg m =
                  input.readMessage(
                      xddq.pb.PlayerHeadAndNameMsg.parser(),
                      extensionRegistry);
              if (playerHeadAndNameMsgBuilder_ == null) {
                ensurePlayerHeadAndNameMsgIsMutable();
                playerHeadAndNameMsg_.add(m);
              } else {
                playerHeadAndNameMsgBuilder_.addMessage(m);
              }
              break;
            } // case 18
            case 26: {
              input.readMessage(
                  internalGetDataMsgFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 32: {
              consumeScore_ = input.readInt64();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>optional int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>optional int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.PlayerHeadAndNameMsg> playerHeadAndNameMsg_ =
      java.util.Collections.emptyList();
    private void ensurePlayerHeadAndNameMsgIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        playerHeadAndNameMsg_ = new java.util.ArrayList<xddq.pb.PlayerHeadAndNameMsg>(playerHeadAndNameMsg_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.PlayerHeadAndNameMsg, xddq.pb.PlayerHeadAndNameMsg.Builder, xddq.pb.PlayerHeadAndNameMsgOrBuilder> playerHeadAndNameMsgBuilder_;

    /**
     * <code>repeated .xddq.pb.PlayerHeadAndNameMsg playerHeadAndNameMsg = 2;</code>
     */
    public java.util.List<xddq.pb.PlayerHeadAndNameMsg> getPlayerHeadAndNameMsgList() {
      if (playerHeadAndNameMsgBuilder_ == null) {
        return java.util.Collections.unmodifiableList(playerHeadAndNameMsg_);
      } else {
        return playerHeadAndNameMsgBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.PlayerHeadAndNameMsg playerHeadAndNameMsg = 2;</code>
     */
    public int getPlayerHeadAndNameMsgCount() {
      if (playerHeadAndNameMsgBuilder_ == null) {
        return playerHeadAndNameMsg_.size();
      } else {
        return playerHeadAndNameMsgBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.PlayerHeadAndNameMsg playerHeadAndNameMsg = 2;</code>
     */
    public xddq.pb.PlayerHeadAndNameMsg getPlayerHeadAndNameMsg(int index) {
      if (playerHeadAndNameMsgBuilder_ == null) {
        return playerHeadAndNameMsg_.get(index);
      } else {
        return playerHeadAndNameMsgBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.PlayerHeadAndNameMsg playerHeadAndNameMsg = 2;</code>
     */
    public Builder setPlayerHeadAndNameMsg(
        int index, xddq.pb.PlayerHeadAndNameMsg value) {
      if (playerHeadAndNameMsgBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePlayerHeadAndNameMsgIsMutable();
        playerHeadAndNameMsg_.set(index, value);
        onChanged();
      } else {
        playerHeadAndNameMsgBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PlayerHeadAndNameMsg playerHeadAndNameMsg = 2;</code>
     */
    public Builder setPlayerHeadAndNameMsg(
        int index, xddq.pb.PlayerHeadAndNameMsg.Builder builderForValue) {
      if (playerHeadAndNameMsgBuilder_ == null) {
        ensurePlayerHeadAndNameMsgIsMutable();
        playerHeadAndNameMsg_.set(index, builderForValue.build());
        onChanged();
      } else {
        playerHeadAndNameMsgBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PlayerHeadAndNameMsg playerHeadAndNameMsg = 2;</code>
     */
    public Builder addPlayerHeadAndNameMsg(xddq.pb.PlayerHeadAndNameMsg value) {
      if (playerHeadAndNameMsgBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePlayerHeadAndNameMsgIsMutable();
        playerHeadAndNameMsg_.add(value);
        onChanged();
      } else {
        playerHeadAndNameMsgBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PlayerHeadAndNameMsg playerHeadAndNameMsg = 2;</code>
     */
    public Builder addPlayerHeadAndNameMsg(
        int index, xddq.pb.PlayerHeadAndNameMsg value) {
      if (playerHeadAndNameMsgBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePlayerHeadAndNameMsgIsMutable();
        playerHeadAndNameMsg_.add(index, value);
        onChanged();
      } else {
        playerHeadAndNameMsgBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PlayerHeadAndNameMsg playerHeadAndNameMsg = 2;</code>
     */
    public Builder addPlayerHeadAndNameMsg(
        xddq.pb.PlayerHeadAndNameMsg.Builder builderForValue) {
      if (playerHeadAndNameMsgBuilder_ == null) {
        ensurePlayerHeadAndNameMsgIsMutable();
        playerHeadAndNameMsg_.add(builderForValue.build());
        onChanged();
      } else {
        playerHeadAndNameMsgBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PlayerHeadAndNameMsg playerHeadAndNameMsg = 2;</code>
     */
    public Builder addPlayerHeadAndNameMsg(
        int index, xddq.pb.PlayerHeadAndNameMsg.Builder builderForValue) {
      if (playerHeadAndNameMsgBuilder_ == null) {
        ensurePlayerHeadAndNameMsgIsMutable();
        playerHeadAndNameMsg_.add(index, builderForValue.build());
        onChanged();
      } else {
        playerHeadAndNameMsgBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PlayerHeadAndNameMsg playerHeadAndNameMsg = 2;</code>
     */
    public Builder addAllPlayerHeadAndNameMsg(
        java.lang.Iterable<? extends xddq.pb.PlayerHeadAndNameMsg> values) {
      if (playerHeadAndNameMsgBuilder_ == null) {
        ensurePlayerHeadAndNameMsgIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, playerHeadAndNameMsg_);
        onChanged();
      } else {
        playerHeadAndNameMsgBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PlayerHeadAndNameMsg playerHeadAndNameMsg = 2;</code>
     */
    public Builder clearPlayerHeadAndNameMsg() {
      if (playerHeadAndNameMsgBuilder_ == null) {
        playerHeadAndNameMsg_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        playerHeadAndNameMsgBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PlayerHeadAndNameMsg playerHeadAndNameMsg = 2;</code>
     */
    public Builder removePlayerHeadAndNameMsg(int index) {
      if (playerHeadAndNameMsgBuilder_ == null) {
        ensurePlayerHeadAndNameMsgIsMutable();
        playerHeadAndNameMsg_.remove(index);
        onChanged();
      } else {
        playerHeadAndNameMsgBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PlayerHeadAndNameMsg playerHeadAndNameMsg = 2;</code>
     */
    public xddq.pb.PlayerHeadAndNameMsg.Builder getPlayerHeadAndNameMsgBuilder(
        int index) {
      return internalGetPlayerHeadAndNameMsgFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.PlayerHeadAndNameMsg playerHeadAndNameMsg = 2;</code>
     */
    public xddq.pb.PlayerHeadAndNameMsgOrBuilder getPlayerHeadAndNameMsgOrBuilder(
        int index) {
      if (playerHeadAndNameMsgBuilder_ == null) {
        return playerHeadAndNameMsg_.get(index);  } else {
        return playerHeadAndNameMsgBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.PlayerHeadAndNameMsg playerHeadAndNameMsg = 2;</code>
     */
    public java.util.List<? extends xddq.pb.PlayerHeadAndNameMsgOrBuilder> 
         getPlayerHeadAndNameMsgOrBuilderList() {
      if (playerHeadAndNameMsgBuilder_ != null) {
        return playerHeadAndNameMsgBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(playerHeadAndNameMsg_);
      }
    }
    /**
     * <code>repeated .xddq.pb.PlayerHeadAndNameMsg playerHeadAndNameMsg = 2;</code>
     */
    public xddq.pb.PlayerHeadAndNameMsg.Builder addPlayerHeadAndNameMsgBuilder() {
      return internalGetPlayerHeadAndNameMsgFieldBuilder().addBuilder(
          xddq.pb.PlayerHeadAndNameMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.PlayerHeadAndNameMsg playerHeadAndNameMsg = 2;</code>
     */
    public xddq.pb.PlayerHeadAndNameMsg.Builder addPlayerHeadAndNameMsgBuilder(
        int index) {
      return internalGetPlayerHeadAndNameMsgFieldBuilder().addBuilder(
          index, xddq.pb.PlayerHeadAndNameMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.PlayerHeadAndNameMsg playerHeadAndNameMsg = 2;</code>
     */
    public java.util.List<xddq.pb.PlayerHeadAndNameMsg.Builder> 
         getPlayerHeadAndNameMsgBuilderList() {
      return internalGetPlayerHeadAndNameMsgFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.PlayerHeadAndNameMsg, xddq.pb.PlayerHeadAndNameMsg.Builder, xddq.pb.PlayerHeadAndNameMsgOrBuilder> 
        internalGetPlayerHeadAndNameMsgFieldBuilder() {
      if (playerHeadAndNameMsgBuilder_ == null) {
        playerHeadAndNameMsgBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.PlayerHeadAndNameMsg, xddq.pb.PlayerHeadAndNameMsg.Builder, xddq.pb.PlayerHeadAndNameMsgOrBuilder>(
                playerHeadAndNameMsg_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        playerHeadAndNameMsg_ = null;
      }
      return playerHeadAndNameMsgBuilder_;
    }

    private xddq.pb.StarTrialDataMsg dataMsg_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.StarTrialDataMsg, xddq.pb.StarTrialDataMsg.Builder, xddq.pb.StarTrialDataMsgOrBuilder> dataMsgBuilder_;
    /**
     * <code>optional .xddq.pb.StarTrialDataMsg dataMsg = 3;</code>
     * @return Whether the dataMsg field is set.
     */
    public boolean hasDataMsg() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional .xddq.pb.StarTrialDataMsg dataMsg = 3;</code>
     * @return The dataMsg.
     */
    public xddq.pb.StarTrialDataMsg getDataMsg() {
      if (dataMsgBuilder_ == null) {
        return dataMsg_ == null ? xddq.pb.StarTrialDataMsg.getDefaultInstance() : dataMsg_;
      } else {
        return dataMsgBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.StarTrialDataMsg dataMsg = 3;</code>
     */
    public Builder setDataMsg(xddq.pb.StarTrialDataMsg value) {
      if (dataMsgBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        dataMsg_ = value;
      } else {
        dataMsgBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.StarTrialDataMsg dataMsg = 3;</code>
     */
    public Builder setDataMsg(
        xddq.pb.StarTrialDataMsg.Builder builderForValue) {
      if (dataMsgBuilder_ == null) {
        dataMsg_ = builderForValue.build();
      } else {
        dataMsgBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.StarTrialDataMsg dataMsg = 3;</code>
     */
    public Builder mergeDataMsg(xddq.pb.StarTrialDataMsg value) {
      if (dataMsgBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0) &&
          dataMsg_ != null &&
          dataMsg_ != xddq.pb.StarTrialDataMsg.getDefaultInstance()) {
          getDataMsgBuilder().mergeFrom(value);
        } else {
          dataMsg_ = value;
        }
      } else {
        dataMsgBuilder_.mergeFrom(value);
      }
      if (dataMsg_ != null) {
        bitField0_ |= 0x00000004;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.StarTrialDataMsg dataMsg = 3;</code>
     */
    public Builder clearDataMsg() {
      bitField0_ = (bitField0_ & ~0x00000004);
      dataMsg_ = null;
      if (dataMsgBuilder_ != null) {
        dataMsgBuilder_.dispose();
        dataMsgBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.StarTrialDataMsg dataMsg = 3;</code>
     */
    public xddq.pb.StarTrialDataMsg.Builder getDataMsgBuilder() {
      bitField0_ |= 0x00000004;
      onChanged();
      return internalGetDataMsgFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.StarTrialDataMsg dataMsg = 3;</code>
     */
    public xddq.pb.StarTrialDataMsgOrBuilder getDataMsgOrBuilder() {
      if (dataMsgBuilder_ != null) {
        return dataMsgBuilder_.getMessageOrBuilder();
      } else {
        return dataMsg_ == null ?
            xddq.pb.StarTrialDataMsg.getDefaultInstance() : dataMsg_;
      }
    }
    /**
     * <code>optional .xddq.pb.StarTrialDataMsg dataMsg = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.StarTrialDataMsg, xddq.pb.StarTrialDataMsg.Builder, xddq.pb.StarTrialDataMsgOrBuilder> 
        internalGetDataMsgFieldBuilder() {
      if (dataMsgBuilder_ == null) {
        dataMsgBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.StarTrialDataMsg, xddq.pb.StarTrialDataMsg.Builder, xddq.pb.StarTrialDataMsgOrBuilder>(
                getDataMsg(),
                getParentForChildren(),
                isClean());
        dataMsg_ = null;
      }
      return dataMsgBuilder_;
    }

    private long consumeScore_ ;
    /**
     * <code>optional int64 consumeScore = 4;</code>
     * @return Whether the consumeScore field is set.
     */
    @java.lang.Override
    public boolean hasConsumeScore() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int64 consumeScore = 4;</code>
     * @return The consumeScore.
     */
    @java.lang.Override
    public long getConsumeScore() {
      return consumeScore_;
    }
    /**
     * <code>optional int64 consumeScore = 4;</code>
     * @param value The consumeScore to set.
     * @return This builder for chaining.
     */
    public Builder setConsumeScore(long value) {

      consumeScore_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 consumeScore = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearConsumeScore() {
      bitField0_ = (bitField0_ & ~0x00000008);
      consumeScore_ = 0L;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.StarTrialEnterMainPanelResp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.StarTrialEnterMainPanelResp)
  private static final xddq.pb.StarTrialEnterMainPanelResp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.StarTrialEnterMainPanelResp();
  }

  public static xddq.pb.StarTrialEnterMainPanelResp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<StarTrialEnterMainPanelResp>
      PARSER = new com.google.protobuf.AbstractParser<StarTrialEnterMainPanelResp>() {
    @java.lang.Override
    public StarTrialEnterMainPanelResp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<StarTrialEnterMainPanelResp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<StarTrialEnterMainPanelResp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.StarTrialEnterMainPanelResp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

