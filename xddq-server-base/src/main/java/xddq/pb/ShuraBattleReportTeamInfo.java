// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.ShuraBattleReportTeamInfo}
 */
public final class ShuraBattleReportTeamInfo extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.ShuraBattleReportTeamInfo)
    ShuraBattleReportTeamInfoOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      ShuraBattleReportTeamInfo.class.getName());
  }
  // Use ShuraBattleReportTeamInfo.newBuilder() to construct.
  private ShuraBattleReportTeamInfo(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private ShuraBattleReportTeamInfo() {
    headInfos_ = java.util.Collections.emptyList();
    teamName_ = "";
    fightValue_ = "";
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_ShuraBattleReportTeamInfo_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_ShuraBattleReportTeamInfo_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.ShuraBattleReportTeamInfo.class, xddq.pb.ShuraBattleReportTeamInfo.Builder.class);
  }

  private int bitField0_;
  public static final int HEADINFOS_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.PlayerHeadAndNameMsg> headInfos_;
  /**
   * <code>repeated .xddq.pb.PlayerHeadAndNameMsg headInfos = 1;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.PlayerHeadAndNameMsg> getHeadInfosList() {
    return headInfos_;
  }
  /**
   * <code>repeated .xddq.pb.PlayerHeadAndNameMsg headInfos = 1;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.PlayerHeadAndNameMsgOrBuilder> 
      getHeadInfosOrBuilderList() {
    return headInfos_;
  }
  /**
   * <code>repeated .xddq.pb.PlayerHeadAndNameMsg headInfos = 1;</code>
   */
  @java.lang.Override
  public int getHeadInfosCount() {
    return headInfos_.size();
  }
  /**
   * <code>repeated .xddq.pb.PlayerHeadAndNameMsg headInfos = 1;</code>
   */
  @java.lang.Override
  public xddq.pb.PlayerHeadAndNameMsg getHeadInfos(int index) {
    return headInfos_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.PlayerHeadAndNameMsg headInfos = 1;</code>
   */
  @java.lang.Override
  public xddq.pb.PlayerHeadAndNameMsgOrBuilder getHeadInfosOrBuilder(
      int index) {
    return headInfos_.get(index);
  }

  public static final int TEAMID_FIELD_NUMBER = 2;
  private long teamId_ = 0L;
  /**
   * <code>optional int64 teamId = 2;</code>
   * @return Whether the teamId field is set.
   */
  @java.lang.Override
  public boolean hasTeamId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int64 teamId = 2;</code>
   * @return The teamId.
   */
  @java.lang.Override
  public long getTeamId() {
    return teamId_;
  }

  public static final int TEAMNAME_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private volatile java.lang.Object teamName_ = "";
  /**
   * <code>optional string teamName = 3;</code>
   * @return Whether the teamName field is set.
   */
  @java.lang.Override
  public boolean hasTeamName() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional string teamName = 3;</code>
   * @return The teamName.
   */
  @java.lang.Override
  public java.lang.String getTeamName() {
    java.lang.Object ref = teamName_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        teamName_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string teamName = 3;</code>
   * @return The bytes for teamName.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getTeamNameBytes() {
    java.lang.Object ref = teamName_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      teamName_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int FIGHTVALUE_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private volatile java.lang.Object fightValue_ = "";
  /**
   * <code>optional string fightValue = 4;</code>
   * @return Whether the fightValue field is set.
   */
  @java.lang.Override
  public boolean hasFightValue() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional string fightValue = 4;</code>
   * @return The fightValue.
   */
  @java.lang.Override
  public java.lang.String getFightValue() {
    java.lang.Object ref = fightValue_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        fightValue_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string fightValue = 4;</code>
   * @return The bytes for fightValue.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getFightValueBytes() {
    java.lang.Object ref = fightValue_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      fightValue_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CAPTAINID_FIELD_NUMBER = 5;
  private long captainId_ = 0L;
  /**
   * <code>optional int64 captainId = 5;</code>
   * @return Whether the captainId field is set.
   */
  @java.lang.Override
  public boolean hasCaptainId() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional int64 captainId = 5;</code>
   * @return The captainId.
   */
  @java.lang.Override
  public long getCaptainId() {
    return captainId_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    for (int i = 0; i < headInfos_.size(); i++) {
      output.writeMessage(1, headInfos_.get(i));
    }
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt64(2, teamId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 3, teamName_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 4, fightValue_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt64(5, captainId_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    for (int i = 0; i < headInfos_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, headInfos_.get(i));
    }
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(2, teamId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(3, teamName_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(4, fightValue_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(5, captainId_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.ShuraBattleReportTeamInfo)) {
      return super.equals(obj);
    }
    xddq.pb.ShuraBattleReportTeamInfo other = (xddq.pb.ShuraBattleReportTeamInfo) obj;

    if (!getHeadInfosList()
        .equals(other.getHeadInfosList())) return false;
    if (hasTeamId() != other.hasTeamId()) return false;
    if (hasTeamId()) {
      if (getTeamId()
          != other.getTeamId()) return false;
    }
    if (hasTeamName() != other.hasTeamName()) return false;
    if (hasTeamName()) {
      if (!getTeamName()
          .equals(other.getTeamName())) return false;
    }
    if (hasFightValue() != other.hasFightValue()) return false;
    if (hasFightValue()) {
      if (!getFightValue()
          .equals(other.getFightValue())) return false;
    }
    if (hasCaptainId() != other.hasCaptainId()) return false;
    if (hasCaptainId()) {
      if (getCaptainId()
          != other.getCaptainId()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (getHeadInfosCount() > 0) {
      hash = (37 * hash) + HEADINFOS_FIELD_NUMBER;
      hash = (53 * hash) + getHeadInfosList().hashCode();
    }
    if (hasTeamId()) {
      hash = (37 * hash) + TEAMID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTeamId());
    }
    if (hasTeamName()) {
      hash = (37 * hash) + TEAMNAME_FIELD_NUMBER;
      hash = (53 * hash) + getTeamName().hashCode();
    }
    if (hasFightValue()) {
      hash = (37 * hash) + FIGHTVALUE_FIELD_NUMBER;
      hash = (53 * hash) + getFightValue().hashCode();
    }
    if (hasCaptainId()) {
      hash = (37 * hash) + CAPTAINID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getCaptainId());
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.ShuraBattleReportTeamInfo parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ShuraBattleReportTeamInfo parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ShuraBattleReportTeamInfo parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ShuraBattleReportTeamInfo parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ShuraBattleReportTeamInfo parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ShuraBattleReportTeamInfo parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ShuraBattleReportTeamInfo parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.ShuraBattleReportTeamInfo parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.ShuraBattleReportTeamInfo parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.ShuraBattleReportTeamInfo parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.ShuraBattleReportTeamInfo parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.ShuraBattleReportTeamInfo parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.ShuraBattleReportTeamInfo prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.ShuraBattleReportTeamInfo}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.ShuraBattleReportTeamInfo)
      xddq.pb.ShuraBattleReportTeamInfoOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ShuraBattleReportTeamInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ShuraBattleReportTeamInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.ShuraBattleReportTeamInfo.class, xddq.pb.ShuraBattleReportTeamInfo.Builder.class);
    }

    // Construct using xddq.pb.ShuraBattleReportTeamInfo.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      if (headInfosBuilder_ == null) {
        headInfos_ = java.util.Collections.emptyList();
      } else {
        headInfos_ = null;
        headInfosBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000001);
      teamId_ = 0L;
      teamName_ = "";
      fightValue_ = "";
      captainId_ = 0L;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ShuraBattleReportTeamInfo_descriptor;
    }

    @java.lang.Override
    public xddq.pb.ShuraBattleReportTeamInfo getDefaultInstanceForType() {
      return xddq.pb.ShuraBattleReportTeamInfo.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.ShuraBattleReportTeamInfo build() {
      xddq.pb.ShuraBattleReportTeamInfo result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.ShuraBattleReportTeamInfo buildPartial() {
      xddq.pb.ShuraBattleReportTeamInfo result = new xddq.pb.ShuraBattleReportTeamInfo(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.ShuraBattleReportTeamInfo result) {
      if (headInfosBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          headInfos_ = java.util.Collections.unmodifiableList(headInfos_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.headInfos_ = headInfos_;
      } else {
        result.headInfos_ = headInfosBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.ShuraBattleReportTeamInfo result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.teamId_ = teamId_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.teamName_ = teamName_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.fightValue_ = fightValue_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.captainId_ = captainId_;
        to_bitField0_ |= 0x00000008;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.ShuraBattleReportTeamInfo) {
        return mergeFrom((xddq.pb.ShuraBattleReportTeamInfo)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.ShuraBattleReportTeamInfo other) {
      if (other == xddq.pb.ShuraBattleReportTeamInfo.getDefaultInstance()) return this;
      if (headInfosBuilder_ == null) {
        if (!other.headInfos_.isEmpty()) {
          if (headInfos_.isEmpty()) {
            headInfos_ = other.headInfos_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureHeadInfosIsMutable();
            headInfos_.addAll(other.headInfos_);
          }
          onChanged();
        }
      } else {
        if (!other.headInfos_.isEmpty()) {
          if (headInfosBuilder_.isEmpty()) {
            headInfosBuilder_.dispose();
            headInfosBuilder_ = null;
            headInfos_ = other.headInfos_;
            bitField0_ = (bitField0_ & ~0x00000001);
            headInfosBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetHeadInfosFieldBuilder() : null;
          } else {
            headInfosBuilder_.addAllMessages(other.headInfos_);
          }
        }
      }
      if (other.hasTeamId()) {
        setTeamId(other.getTeamId());
      }
      if (other.hasTeamName()) {
        teamName_ = other.teamName_;
        bitField0_ |= 0x00000004;
        onChanged();
      }
      if (other.hasFightValue()) {
        fightValue_ = other.fightValue_;
        bitField0_ |= 0x00000008;
        onChanged();
      }
      if (other.hasCaptainId()) {
        setCaptainId(other.getCaptainId());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              xddq.pb.PlayerHeadAndNameMsg m =
                  input.readMessage(
                      xddq.pb.PlayerHeadAndNameMsg.parser(),
                      extensionRegistry);
              if (headInfosBuilder_ == null) {
                ensureHeadInfosIsMutable();
                headInfos_.add(m);
              } else {
                headInfosBuilder_.addMessage(m);
              }
              break;
            } // case 10
            case 16: {
              teamId_ = input.readInt64();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 26: {
              teamName_ = input.readBytes();
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 34: {
              fightValue_ = input.readBytes();
              bitField0_ |= 0x00000008;
              break;
            } // case 34
            case 40: {
              captainId_ = input.readInt64();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.util.List<xddq.pb.PlayerHeadAndNameMsg> headInfos_ =
      java.util.Collections.emptyList();
    private void ensureHeadInfosIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        headInfos_ = new java.util.ArrayList<xddq.pb.PlayerHeadAndNameMsg>(headInfos_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.PlayerHeadAndNameMsg, xddq.pb.PlayerHeadAndNameMsg.Builder, xddq.pb.PlayerHeadAndNameMsgOrBuilder> headInfosBuilder_;

    /**
     * <code>repeated .xddq.pb.PlayerHeadAndNameMsg headInfos = 1;</code>
     */
    public java.util.List<xddq.pb.PlayerHeadAndNameMsg> getHeadInfosList() {
      if (headInfosBuilder_ == null) {
        return java.util.Collections.unmodifiableList(headInfos_);
      } else {
        return headInfosBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.PlayerHeadAndNameMsg headInfos = 1;</code>
     */
    public int getHeadInfosCount() {
      if (headInfosBuilder_ == null) {
        return headInfos_.size();
      } else {
        return headInfosBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.PlayerHeadAndNameMsg headInfos = 1;</code>
     */
    public xddq.pb.PlayerHeadAndNameMsg getHeadInfos(int index) {
      if (headInfosBuilder_ == null) {
        return headInfos_.get(index);
      } else {
        return headInfosBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.PlayerHeadAndNameMsg headInfos = 1;</code>
     */
    public Builder setHeadInfos(
        int index, xddq.pb.PlayerHeadAndNameMsg value) {
      if (headInfosBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureHeadInfosIsMutable();
        headInfos_.set(index, value);
        onChanged();
      } else {
        headInfosBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PlayerHeadAndNameMsg headInfos = 1;</code>
     */
    public Builder setHeadInfos(
        int index, xddq.pb.PlayerHeadAndNameMsg.Builder builderForValue) {
      if (headInfosBuilder_ == null) {
        ensureHeadInfosIsMutable();
        headInfos_.set(index, builderForValue.build());
        onChanged();
      } else {
        headInfosBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PlayerHeadAndNameMsg headInfos = 1;</code>
     */
    public Builder addHeadInfos(xddq.pb.PlayerHeadAndNameMsg value) {
      if (headInfosBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureHeadInfosIsMutable();
        headInfos_.add(value);
        onChanged();
      } else {
        headInfosBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PlayerHeadAndNameMsg headInfos = 1;</code>
     */
    public Builder addHeadInfos(
        int index, xddq.pb.PlayerHeadAndNameMsg value) {
      if (headInfosBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureHeadInfosIsMutable();
        headInfos_.add(index, value);
        onChanged();
      } else {
        headInfosBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PlayerHeadAndNameMsg headInfos = 1;</code>
     */
    public Builder addHeadInfos(
        xddq.pb.PlayerHeadAndNameMsg.Builder builderForValue) {
      if (headInfosBuilder_ == null) {
        ensureHeadInfosIsMutable();
        headInfos_.add(builderForValue.build());
        onChanged();
      } else {
        headInfosBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PlayerHeadAndNameMsg headInfos = 1;</code>
     */
    public Builder addHeadInfos(
        int index, xddq.pb.PlayerHeadAndNameMsg.Builder builderForValue) {
      if (headInfosBuilder_ == null) {
        ensureHeadInfosIsMutable();
        headInfos_.add(index, builderForValue.build());
        onChanged();
      } else {
        headInfosBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PlayerHeadAndNameMsg headInfos = 1;</code>
     */
    public Builder addAllHeadInfos(
        java.lang.Iterable<? extends xddq.pb.PlayerHeadAndNameMsg> values) {
      if (headInfosBuilder_ == null) {
        ensureHeadInfosIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, headInfos_);
        onChanged();
      } else {
        headInfosBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PlayerHeadAndNameMsg headInfos = 1;</code>
     */
    public Builder clearHeadInfos() {
      if (headInfosBuilder_ == null) {
        headInfos_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        headInfosBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PlayerHeadAndNameMsg headInfos = 1;</code>
     */
    public Builder removeHeadInfos(int index) {
      if (headInfosBuilder_ == null) {
        ensureHeadInfosIsMutable();
        headInfos_.remove(index);
        onChanged();
      } else {
        headInfosBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PlayerHeadAndNameMsg headInfos = 1;</code>
     */
    public xddq.pb.PlayerHeadAndNameMsg.Builder getHeadInfosBuilder(
        int index) {
      return internalGetHeadInfosFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.PlayerHeadAndNameMsg headInfos = 1;</code>
     */
    public xddq.pb.PlayerHeadAndNameMsgOrBuilder getHeadInfosOrBuilder(
        int index) {
      if (headInfosBuilder_ == null) {
        return headInfos_.get(index);  } else {
        return headInfosBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.PlayerHeadAndNameMsg headInfos = 1;</code>
     */
    public java.util.List<? extends xddq.pb.PlayerHeadAndNameMsgOrBuilder> 
         getHeadInfosOrBuilderList() {
      if (headInfosBuilder_ != null) {
        return headInfosBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(headInfos_);
      }
    }
    /**
     * <code>repeated .xddq.pb.PlayerHeadAndNameMsg headInfos = 1;</code>
     */
    public xddq.pb.PlayerHeadAndNameMsg.Builder addHeadInfosBuilder() {
      return internalGetHeadInfosFieldBuilder().addBuilder(
          xddq.pb.PlayerHeadAndNameMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.PlayerHeadAndNameMsg headInfos = 1;</code>
     */
    public xddq.pb.PlayerHeadAndNameMsg.Builder addHeadInfosBuilder(
        int index) {
      return internalGetHeadInfosFieldBuilder().addBuilder(
          index, xddq.pb.PlayerHeadAndNameMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.PlayerHeadAndNameMsg headInfos = 1;</code>
     */
    public java.util.List<xddq.pb.PlayerHeadAndNameMsg.Builder> 
         getHeadInfosBuilderList() {
      return internalGetHeadInfosFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.PlayerHeadAndNameMsg, xddq.pb.PlayerHeadAndNameMsg.Builder, xddq.pb.PlayerHeadAndNameMsgOrBuilder> 
        internalGetHeadInfosFieldBuilder() {
      if (headInfosBuilder_ == null) {
        headInfosBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.PlayerHeadAndNameMsg, xddq.pb.PlayerHeadAndNameMsg.Builder, xddq.pb.PlayerHeadAndNameMsgOrBuilder>(
                headInfos_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        headInfos_ = null;
      }
      return headInfosBuilder_;
    }

    private long teamId_ ;
    /**
     * <code>optional int64 teamId = 2;</code>
     * @return Whether the teamId field is set.
     */
    @java.lang.Override
    public boolean hasTeamId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int64 teamId = 2;</code>
     * @return The teamId.
     */
    @java.lang.Override
    public long getTeamId() {
      return teamId_;
    }
    /**
     * <code>optional int64 teamId = 2;</code>
     * @param value The teamId to set.
     * @return This builder for chaining.
     */
    public Builder setTeamId(long value) {

      teamId_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 teamId = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearTeamId() {
      bitField0_ = (bitField0_ & ~0x00000002);
      teamId_ = 0L;
      onChanged();
      return this;
    }

    private java.lang.Object teamName_ = "";
    /**
     * <code>optional string teamName = 3;</code>
     * @return Whether the teamName field is set.
     */
    public boolean hasTeamName() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional string teamName = 3;</code>
     * @return The teamName.
     */
    public java.lang.String getTeamName() {
      java.lang.Object ref = teamName_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          teamName_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string teamName = 3;</code>
     * @return The bytes for teamName.
     */
    public com.google.protobuf.ByteString
        getTeamNameBytes() {
      java.lang.Object ref = teamName_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        teamName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string teamName = 3;</code>
     * @param value The teamName to set.
     * @return This builder for chaining.
     */
    public Builder setTeamName(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      teamName_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional string teamName = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearTeamName() {
      teamName_ = getDefaultInstance().getTeamName();
      bitField0_ = (bitField0_ & ~0x00000004);
      onChanged();
      return this;
    }
    /**
     * <code>optional string teamName = 3;</code>
     * @param value The bytes for teamName to set.
     * @return This builder for chaining.
     */
    public Builder setTeamNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      teamName_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }

    private java.lang.Object fightValue_ = "";
    /**
     * <code>optional string fightValue = 4;</code>
     * @return Whether the fightValue field is set.
     */
    public boolean hasFightValue() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional string fightValue = 4;</code>
     * @return The fightValue.
     */
    public java.lang.String getFightValue() {
      java.lang.Object ref = fightValue_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          fightValue_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string fightValue = 4;</code>
     * @return The bytes for fightValue.
     */
    public com.google.protobuf.ByteString
        getFightValueBytes() {
      java.lang.Object ref = fightValue_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        fightValue_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string fightValue = 4;</code>
     * @param value The fightValue to set.
     * @return This builder for chaining.
     */
    public Builder setFightValue(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      fightValue_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional string fightValue = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearFightValue() {
      fightValue_ = getDefaultInstance().getFightValue();
      bitField0_ = (bitField0_ & ~0x00000008);
      onChanged();
      return this;
    }
    /**
     * <code>optional string fightValue = 4;</code>
     * @param value The bytes for fightValue to set.
     * @return This builder for chaining.
     */
    public Builder setFightValueBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      fightValue_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }

    private long captainId_ ;
    /**
     * <code>optional int64 captainId = 5;</code>
     * @return Whether the captainId field is set.
     */
    @java.lang.Override
    public boolean hasCaptainId() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional int64 captainId = 5;</code>
     * @return The captainId.
     */
    @java.lang.Override
    public long getCaptainId() {
      return captainId_;
    }
    /**
     * <code>optional int64 captainId = 5;</code>
     * @param value The captainId to set.
     * @return This builder for chaining.
     */
    public Builder setCaptainId(long value) {

      captainId_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 captainId = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearCaptainId() {
      bitField0_ = (bitField0_ & ~0x00000010);
      captainId_ = 0L;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.ShuraBattleReportTeamInfo)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.ShuraBattleReportTeamInfo)
  private static final xddq.pb.ShuraBattleReportTeamInfo DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.ShuraBattleReportTeamInfo();
  }

  public static xddq.pb.ShuraBattleReportTeamInfo getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ShuraBattleReportTeamInfo>
      PARSER = new com.google.protobuf.AbstractParser<ShuraBattleReportTeamInfo>() {
    @java.lang.Override
    public ShuraBattleReportTeamInfo parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<ShuraBattleReportTeamInfo> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ShuraBattleReportTeamInfo> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.ShuraBattleReportTeamInfo getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

