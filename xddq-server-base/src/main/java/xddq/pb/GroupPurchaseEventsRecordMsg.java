// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.GroupPurchaseEventsRecordMsg}
 */
public final class GroupPurchaseEventsRecordMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.GroupPurchaseEventsRecordMsg)
    GroupPurchaseEventsRecordMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      GroupPurchaseEventsRecordMsg.class.getName());
  }
  // Use GroupPurchaseEventsRecordMsg.newBuilder() to construct.
  private GroupPurchaseEventsRecordMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private GroupPurchaseEventsRecordMsg() {
    orderId_ = "";
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_GroupPurchaseEventsRecordMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_GroupPurchaseEventsRecordMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.GroupPurchaseEventsRecordMsg.class, xddq.pb.GroupPurchaseEventsRecordMsg.Builder.class);
  }

  private int bitField0_;
  public static final int MALLID_FIELD_NUMBER = 1;
  private long mallId_ = 0L;
  /**
   * <code>optional int64 mallId = 1;</code>
   * @return Whether the mallId field is set.
   */
  @java.lang.Override
  public boolean hasMallId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int64 mallId = 1;</code>
   * @return The mallId.
   */
  @java.lang.Override
  public long getMallId() {
    return mallId_;
  }

  public static final int TIME_FIELD_NUMBER = 2;
  private long time_ = 0L;
  /**
   * <code>optional int64 time = 2;</code>
   * @return Whether the time field is set.
   */
  @java.lang.Override
  public boolean hasTime() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int64 time = 2;</code>
   * @return The time.
   */
  @java.lang.Override
  public long getTime() {
    return time_;
  }

  public static final int MEMBERINFO_FIELD_NUMBER = 3;
  private xddq.pb.PlayerHeadAndNameMsg memberInfo_;
  /**
   * <code>optional .xddq.pb.PlayerHeadAndNameMsg memberInfo = 3;</code>
   * @return Whether the memberInfo field is set.
   */
  @java.lang.Override
  public boolean hasMemberInfo() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional .xddq.pb.PlayerHeadAndNameMsg memberInfo = 3;</code>
   * @return The memberInfo.
   */
  @java.lang.Override
  public xddq.pb.PlayerHeadAndNameMsg getMemberInfo() {
    return memberInfo_ == null ? xddq.pb.PlayerHeadAndNameMsg.getDefaultInstance() : memberInfo_;
  }
  /**
   * <code>optional .xddq.pb.PlayerHeadAndNameMsg memberInfo = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.PlayerHeadAndNameMsgOrBuilder getMemberInfoOrBuilder() {
    return memberInfo_ == null ? xddq.pb.PlayerHeadAndNameMsg.getDefaultInstance() : memberInfo_;
  }

  public static final int TYPE_FIELD_NUMBER = 4;
  private int type_ = 0;
  /**
   * <code>optional int32 type = 4;</code>
   * @return Whether the type field is set.
   */
  @java.lang.Override
  public boolean hasType() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional int32 type = 4;</code>
   * @return The type.
   */
  @java.lang.Override
  public int getType() {
    return type_;
  }

  public static final int ORDERID_FIELD_NUMBER = 5;
  @SuppressWarnings("serial")
  private volatile java.lang.Object orderId_ = "";
  /**
   * <code>optional string orderId = 5;</code>
   * @return Whether the orderId field is set.
   */
  @java.lang.Override
  public boolean hasOrderId() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>optional string orderId = 5;</code>
   * @return The orderId.
   */
  @java.lang.Override
  public java.lang.String getOrderId() {
    java.lang.Object ref = orderId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        orderId_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string orderId = 5;</code>
   * @return The bytes for orderId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getOrderIdBytes() {
    java.lang.Object ref = orderId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      orderId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt64(1, mallId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt64(2, time_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeMessage(3, getMemberInfo());
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt32(4, type_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 5, orderId_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(1, mallId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(2, time_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, getMemberInfo());
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(4, type_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(5, orderId_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.GroupPurchaseEventsRecordMsg)) {
      return super.equals(obj);
    }
    xddq.pb.GroupPurchaseEventsRecordMsg other = (xddq.pb.GroupPurchaseEventsRecordMsg) obj;

    if (hasMallId() != other.hasMallId()) return false;
    if (hasMallId()) {
      if (getMallId()
          != other.getMallId()) return false;
    }
    if (hasTime() != other.hasTime()) return false;
    if (hasTime()) {
      if (getTime()
          != other.getTime()) return false;
    }
    if (hasMemberInfo() != other.hasMemberInfo()) return false;
    if (hasMemberInfo()) {
      if (!getMemberInfo()
          .equals(other.getMemberInfo())) return false;
    }
    if (hasType() != other.hasType()) return false;
    if (hasType()) {
      if (getType()
          != other.getType()) return false;
    }
    if (hasOrderId() != other.hasOrderId()) return false;
    if (hasOrderId()) {
      if (!getOrderId()
          .equals(other.getOrderId())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasMallId()) {
      hash = (37 * hash) + MALLID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getMallId());
    }
    if (hasTime()) {
      hash = (37 * hash) + TIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTime());
    }
    if (hasMemberInfo()) {
      hash = (37 * hash) + MEMBERINFO_FIELD_NUMBER;
      hash = (53 * hash) + getMemberInfo().hashCode();
    }
    if (hasType()) {
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
    }
    if (hasOrderId()) {
      hash = (37 * hash) + ORDERID_FIELD_NUMBER;
      hash = (53 * hash) + getOrderId().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.GroupPurchaseEventsRecordMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GroupPurchaseEventsRecordMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GroupPurchaseEventsRecordMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GroupPurchaseEventsRecordMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GroupPurchaseEventsRecordMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GroupPurchaseEventsRecordMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GroupPurchaseEventsRecordMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.GroupPurchaseEventsRecordMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.GroupPurchaseEventsRecordMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.GroupPurchaseEventsRecordMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.GroupPurchaseEventsRecordMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.GroupPurchaseEventsRecordMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.GroupPurchaseEventsRecordMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.GroupPurchaseEventsRecordMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.GroupPurchaseEventsRecordMsg)
      xddq.pb.GroupPurchaseEventsRecordMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GroupPurchaseEventsRecordMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GroupPurchaseEventsRecordMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.GroupPurchaseEventsRecordMsg.class, xddq.pb.GroupPurchaseEventsRecordMsg.Builder.class);
    }

    // Construct using xddq.pb.GroupPurchaseEventsRecordMsg.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetMemberInfoFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      mallId_ = 0L;
      time_ = 0L;
      memberInfo_ = null;
      if (memberInfoBuilder_ != null) {
        memberInfoBuilder_.dispose();
        memberInfoBuilder_ = null;
      }
      type_ = 0;
      orderId_ = "";
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GroupPurchaseEventsRecordMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.GroupPurchaseEventsRecordMsg getDefaultInstanceForType() {
      return xddq.pb.GroupPurchaseEventsRecordMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.GroupPurchaseEventsRecordMsg build() {
      xddq.pb.GroupPurchaseEventsRecordMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.GroupPurchaseEventsRecordMsg buildPartial() {
      xddq.pb.GroupPurchaseEventsRecordMsg result = new xddq.pb.GroupPurchaseEventsRecordMsg(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.GroupPurchaseEventsRecordMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.mallId_ = mallId_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.time_ = time_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.memberInfo_ = memberInfoBuilder_ == null
            ? memberInfo_
            : memberInfoBuilder_.build();
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.type_ = type_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.orderId_ = orderId_;
        to_bitField0_ |= 0x00000010;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.GroupPurchaseEventsRecordMsg) {
        return mergeFrom((xddq.pb.GroupPurchaseEventsRecordMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.GroupPurchaseEventsRecordMsg other) {
      if (other == xddq.pb.GroupPurchaseEventsRecordMsg.getDefaultInstance()) return this;
      if (other.hasMallId()) {
        setMallId(other.getMallId());
      }
      if (other.hasTime()) {
        setTime(other.getTime());
      }
      if (other.hasMemberInfo()) {
        mergeMemberInfo(other.getMemberInfo());
      }
      if (other.hasType()) {
        setType(other.getType());
      }
      if (other.hasOrderId()) {
        orderId_ = other.orderId_;
        bitField0_ |= 0x00000010;
        onChanged();
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              mallId_ = input.readInt64();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              time_ = input.readInt64();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 26: {
              input.readMessage(
                  internalGetMemberInfoFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 32: {
              type_ = input.readInt32();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            case 42: {
              orderId_ = input.readBytes();
              bitField0_ |= 0x00000010;
              break;
            } // case 42
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private long mallId_ ;
    /**
     * <code>optional int64 mallId = 1;</code>
     * @return Whether the mallId field is set.
     */
    @java.lang.Override
    public boolean hasMallId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 mallId = 1;</code>
     * @return The mallId.
     */
    @java.lang.Override
    public long getMallId() {
      return mallId_;
    }
    /**
     * <code>optional int64 mallId = 1;</code>
     * @param value The mallId to set.
     * @return This builder for chaining.
     */
    public Builder setMallId(long value) {

      mallId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 mallId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearMallId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      mallId_ = 0L;
      onChanged();
      return this;
    }

    private long time_ ;
    /**
     * <code>optional int64 time = 2;</code>
     * @return Whether the time field is set.
     */
    @java.lang.Override
    public boolean hasTime() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int64 time = 2;</code>
     * @return The time.
     */
    @java.lang.Override
    public long getTime() {
      return time_;
    }
    /**
     * <code>optional int64 time = 2;</code>
     * @param value The time to set.
     * @return This builder for chaining.
     */
    public Builder setTime(long value) {

      time_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 time = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearTime() {
      bitField0_ = (bitField0_ & ~0x00000002);
      time_ = 0L;
      onChanged();
      return this;
    }

    private xddq.pb.PlayerHeadAndNameMsg memberInfo_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PlayerHeadAndNameMsg, xddq.pb.PlayerHeadAndNameMsg.Builder, xddq.pb.PlayerHeadAndNameMsgOrBuilder> memberInfoBuilder_;
    /**
     * <code>optional .xddq.pb.PlayerHeadAndNameMsg memberInfo = 3;</code>
     * @return Whether the memberInfo field is set.
     */
    public boolean hasMemberInfo() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadAndNameMsg memberInfo = 3;</code>
     * @return The memberInfo.
     */
    public xddq.pb.PlayerHeadAndNameMsg getMemberInfo() {
      if (memberInfoBuilder_ == null) {
        return memberInfo_ == null ? xddq.pb.PlayerHeadAndNameMsg.getDefaultInstance() : memberInfo_;
      } else {
        return memberInfoBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadAndNameMsg memberInfo = 3;</code>
     */
    public Builder setMemberInfo(xddq.pb.PlayerHeadAndNameMsg value) {
      if (memberInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        memberInfo_ = value;
      } else {
        memberInfoBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadAndNameMsg memberInfo = 3;</code>
     */
    public Builder setMemberInfo(
        xddq.pb.PlayerHeadAndNameMsg.Builder builderForValue) {
      if (memberInfoBuilder_ == null) {
        memberInfo_ = builderForValue.build();
      } else {
        memberInfoBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadAndNameMsg memberInfo = 3;</code>
     */
    public Builder mergeMemberInfo(xddq.pb.PlayerHeadAndNameMsg value) {
      if (memberInfoBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0) &&
          memberInfo_ != null &&
          memberInfo_ != xddq.pb.PlayerHeadAndNameMsg.getDefaultInstance()) {
          getMemberInfoBuilder().mergeFrom(value);
        } else {
          memberInfo_ = value;
        }
      } else {
        memberInfoBuilder_.mergeFrom(value);
      }
      if (memberInfo_ != null) {
        bitField0_ |= 0x00000004;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadAndNameMsg memberInfo = 3;</code>
     */
    public Builder clearMemberInfo() {
      bitField0_ = (bitField0_ & ~0x00000004);
      memberInfo_ = null;
      if (memberInfoBuilder_ != null) {
        memberInfoBuilder_.dispose();
        memberInfoBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadAndNameMsg memberInfo = 3;</code>
     */
    public xddq.pb.PlayerHeadAndNameMsg.Builder getMemberInfoBuilder() {
      bitField0_ |= 0x00000004;
      onChanged();
      return internalGetMemberInfoFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadAndNameMsg memberInfo = 3;</code>
     */
    public xddq.pb.PlayerHeadAndNameMsgOrBuilder getMemberInfoOrBuilder() {
      if (memberInfoBuilder_ != null) {
        return memberInfoBuilder_.getMessageOrBuilder();
      } else {
        return memberInfo_ == null ?
            xddq.pb.PlayerHeadAndNameMsg.getDefaultInstance() : memberInfo_;
      }
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadAndNameMsg memberInfo = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PlayerHeadAndNameMsg, xddq.pb.PlayerHeadAndNameMsg.Builder, xddq.pb.PlayerHeadAndNameMsgOrBuilder> 
        internalGetMemberInfoFieldBuilder() {
      if (memberInfoBuilder_ == null) {
        memberInfoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.PlayerHeadAndNameMsg, xddq.pb.PlayerHeadAndNameMsg.Builder, xddq.pb.PlayerHeadAndNameMsgOrBuilder>(
                getMemberInfo(),
                getParentForChildren(),
                isClean());
        memberInfo_ = null;
      }
      return memberInfoBuilder_;
    }

    private int type_ ;
    /**
     * <code>optional int32 type = 4;</code>
     * @return Whether the type field is set.
     */
    @java.lang.Override
    public boolean hasType() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int32 type = 4;</code>
     * @return The type.
     */
    @java.lang.Override
    public int getType() {
      return type_;
    }
    /**
     * <code>optional int32 type = 4;</code>
     * @param value The type to set.
     * @return This builder for chaining.
     */
    public Builder setType(int value) {

      type_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 type = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearType() {
      bitField0_ = (bitField0_ & ~0x00000008);
      type_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object orderId_ = "";
    /**
     * <code>optional string orderId = 5;</code>
     * @return Whether the orderId field is set.
     */
    public boolean hasOrderId() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional string orderId = 5;</code>
     * @return The orderId.
     */
    public java.lang.String getOrderId() {
      java.lang.Object ref = orderId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          orderId_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string orderId = 5;</code>
     * @return The bytes for orderId.
     */
    public com.google.protobuf.ByteString
        getOrderIdBytes() {
      java.lang.Object ref = orderId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        orderId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string orderId = 5;</code>
     * @param value The orderId to set.
     * @return This builder for chaining.
     */
    public Builder setOrderId(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      orderId_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional string orderId = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearOrderId() {
      orderId_ = getDefaultInstance().getOrderId();
      bitField0_ = (bitField0_ & ~0x00000010);
      onChanged();
      return this;
    }
    /**
     * <code>optional string orderId = 5;</code>
     * @param value The bytes for orderId to set.
     * @return This builder for chaining.
     */
    public Builder setOrderIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      orderId_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.GroupPurchaseEventsRecordMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.GroupPurchaseEventsRecordMsg)
  private static final xddq.pb.GroupPurchaseEventsRecordMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.GroupPurchaseEventsRecordMsg();
  }

  public static xddq.pb.GroupPurchaseEventsRecordMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<GroupPurchaseEventsRecordMsg>
      PARSER = new com.google.protobuf.AbstractParser<GroupPurchaseEventsRecordMsg>() {
    @java.lang.Override
    public GroupPurchaseEventsRecordMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<GroupPurchaseEventsRecordMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<GroupPurchaseEventsRecordMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.GroupPurchaseEventsRecordMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

