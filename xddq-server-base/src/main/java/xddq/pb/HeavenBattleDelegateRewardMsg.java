// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.HeavenBattleDelegateRewardMsg}
 */
public final class HeavenBattleDelegateRewardMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.HeavenBattleDelegateRewardMsg)
    HeavenBattleDelegateRewardMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      HeavenBattleDelegateRewardMsg.class.getName());
  }
  // Use HeavenBattleDelegateRewardMsg.newBuilder() to construct.
  private HeavenBattleDelegateRewardMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private HeavenBattleDelegateRewardMsg() {
    reward_ = "";
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_HeavenBattleDelegateRewardMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_HeavenBattleDelegateRewardMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.HeavenBattleDelegateRewardMsg.class, xddq.pb.HeavenBattleDelegateRewardMsg.Builder.class);
  }

  private int bitField0_;
  public static final int COSTENERGY_FIELD_NUMBER = 1;
  private int costEnergy_ = 0;
  /**
   * <code>optional int32 costEnergy = 1;</code>
   * @return Whether the costEnergy field is set.
   */
  @java.lang.Override
  public boolean hasCostEnergy() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int32 costEnergy = 1;</code>
   * @return The costEnergy.
   */
  @java.lang.Override
  public int getCostEnergy() {
    return costEnergy_;
  }

  public static final int BOSSBATTLECOUNT_FIELD_NUMBER = 2;
  private int bossBattleCount_ = 0;
  /**
   * <code>optional int32 bossBattleCount = 2;</code>
   * @return Whether the bossBattleCount field is set.
   */
  @java.lang.Override
  public boolean hasBossBattleCount() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int32 bossBattleCount = 2;</code>
   * @return The bossBattleCount.
   */
  @java.lang.Override
  public int getBossBattleCount() {
    return bossBattleCount_;
  }

  public static final int GATHERBATTLECOUNT_FIELD_NUMBER = 3;
  private int gatherBattleCount_ = 0;
  /**
   * <code>optional int32 gatherBattleCount = 3;</code>
   * @return Whether the gatherBattleCount field is set.
   */
  @java.lang.Override
  public boolean hasGatherBattleCount() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional int32 gatherBattleCount = 3;</code>
   * @return The gatherBattleCount.
   */
  @java.lang.Override
  public int getGatherBattleCount() {
    return gatherBattleCount_;
  }

  public static final int REWARD_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private volatile java.lang.Object reward_ = "";
  /**
   * <code>optional string reward = 4;</code>
   * @return Whether the reward field is set.
   */
  @java.lang.Override
  public boolean hasReward() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional string reward = 4;</code>
   * @return The reward.
   */
  @java.lang.Override
  public java.lang.String getReward() {
    java.lang.Object ref = reward_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        reward_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string reward = 4;</code>
   * @return The bytes for reward.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getRewardBytes() {
    java.lang.Object ref = reward_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      reward_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, costEnergy_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(2, bossBattleCount_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt32(3, gatherBattleCount_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 4, reward_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, costEnergy_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, bossBattleCount_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(3, gatherBattleCount_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(4, reward_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.HeavenBattleDelegateRewardMsg)) {
      return super.equals(obj);
    }
    xddq.pb.HeavenBattleDelegateRewardMsg other = (xddq.pb.HeavenBattleDelegateRewardMsg) obj;

    if (hasCostEnergy() != other.hasCostEnergy()) return false;
    if (hasCostEnergy()) {
      if (getCostEnergy()
          != other.getCostEnergy()) return false;
    }
    if (hasBossBattleCount() != other.hasBossBattleCount()) return false;
    if (hasBossBattleCount()) {
      if (getBossBattleCount()
          != other.getBossBattleCount()) return false;
    }
    if (hasGatherBattleCount() != other.hasGatherBattleCount()) return false;
    if (hasGatherBattleCount()) {
      if (getGatherBattleCount()
          != other.getGatherBattleCount()) return false;
    }
    if (hasReward() != other.hasReward()) return false;
    if (hasReward()) {
      if (!getReward()
          .equals(other.getReward())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasCostEnergy()) {
      hash = (37 * hash) + COSTENERGY_FIELD_NUMBER;
      hash = (53 * hash) + getCostEnergy();
    }
    if (hasBossBattleCount()) {
      hash = (37 * hash) + BOSSBATTLECOUNT_FIELD_NUMBER;
      hash = (53 * hash) + getBossBattleCount();
    }
    if (hasGatherBattleCount()) {
      hash = (37 * hash) + GATHERBATTLECOUNT_FIELD_NUMBER;
      hash = (53 * hash) + getGatherBattleCount();
    }
    if (hasReward()) {
      hash = (37 * hash) + REWARD_FIELD_NUMBER;
      hash = (53 * hash) + getReward().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.HeavenBattleDelegateRewardMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.HeavenBattleDelegateRewardMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.HeavenBattleDelegateRewardMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.HeavenBattleDelegateRewardMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.HeavenBattleDelegateRewardMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.HeavenBattleDelegateRewardMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.HeavenBattleDelegateRewardMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.HeavenBattleDelegateRewardMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.HeavenBattleDelegateRewardMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.HeavenBattleDelegateRewardMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.HeavenBattleDelegateRewardMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.HeavenBattleDelegateRewardMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.HeavenBattleDelegateRewardMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.HeavenBattleDelegateRewardMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.HeavenBattleDelegateRewardMsg)
      xddq.pb.HeavenBattleDelegateRewardMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_HeavenBattleDelegateRewardMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_HeavenBattleDelegateRewardMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.HeavenBattleDelegateRewardMsg.class, xddq.pb.HeavenBattleDelegateRewardMsg.Builder.class);
    }

    // Construct using xddq.pb.HeavenBattleDelegateRewardMsg.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      costEnergy_ = 0;
      bossBattleCount_ = 0;
      gatherBattleCount_ = 0;
      reward_ = "";
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_HeavenBattleDelegateRewardMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.HeavenBattleDelegateRewardMsg getDefaultInstanceForType() {
      return xddq.pb.HeavenBattleDelegateRewardMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.HeavenBattleDelegateRewardMsg build() {
      xddq.pb.HeavenBattleDelegateRewardMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.HeavenBattleDelegateRewardMsg buildPartial() {
      xddq.pb.HeavenBattleDelegateRewardMsg result = new xddq.pb.HeavenBattleDelegateRewardMsg(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.HeavenBattleDelegateRewardMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.costEnergy_ = costEnergy_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.bossBattleCount_ = bossBattleCount_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.gatherBattleCount_ = gatherBattleCount_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.reward_ = reward_;
        to_bitField0_ |= 0x00000008;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.HeavenBattleDelegateRewardMsg) {
        return mergeFrom((xddq.pb.HeavenBattleDelegateRewardMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.HeavenBattleDelegateRewardMsg other) {
      if (other == xddq.pb.HeavenBattleDelegateRewardMsg.getDefaultInstance()) return this;
      if (other.hasCostEnergy()) {
        setCostEnergy(other.getCostEnergy());
      }
      if (other.hasBossBattleCount()) {
        setBossBattleCount(other.getBossBattleCount());
      }
      if (other.hasGatherBattleCount()) {
        setGatherBattleCount(other.getGatherBattleCount());
      }
      if (other.hasReward()) {
        reward_ = other.reward_;
        bitField0_ |= 0x00000008;
        onChanged();
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              costEnergy_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              bossBattleCount_ = input.readInt32();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 24: {
              gatherBattleCount_ = input.readInt32();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 34: {
              reward_ = input.readBytes();
              bitField0_ |= 0x00000008;
              break;
            } // case 34
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int costEnergy_ ;
    /**
     * <code>optional int32 costEnergy = 1;</code>
     * @return Whether the costEnergy field is set.
     */
    @java.lang.Override
    public boolean hasCostEnergy() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 costEnergy = 1;</code>
     * @return The costEnergy.
     */
    @java.lang.Override
    public int getCostEnergy() {
      return costEnergy_;
    }
    /**
     * <code>optional int32 costEnergy = 1;</code>
     * @param value The costEnergy to set.
     * @return This builder for chaining.
     */
    public Builder setCostEnergy(int value) {

      costEnergy_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 costEnergy = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearCostEnergy() {
      bitField0_ = (bitField0_ & ~0x00000001);
      costEnergy_ = 0;
      onChanged();
      return this;
    }

    private int bossBattleCount_ ;
    /**
     * <code>optional int32 bossBattleCount = 2;</code>
     * @return Whether the bossBattleCount field is set.
     */
    @java.lang.Override
    public boolean hasBossBattleCount() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 bossBattleCount = 2;</code>
     * @return The bossBattleCount.
     */
    @java.lang.Override
    public int getBossBattleCount() {
      return bossBattleCount_;
    }
    /**
     * <code>optional int32 bossBattleCount = 2;</code>
     * @param value The bossBattleCount to set.
     * @return This builder for chaining.
     */
    public Builder setBossBattleCount(int value) {

      bossBattleCount_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 bossBattleCount = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearBossBattleCount() {
      bitField0_ = (bitField0_ & ~0x00000002);
      bossBattleCount_ = 0;
      onChanged();
      return this;
    }

    private int gatherBattleCount_ ;
    /**
     * <code>optional int32 gatherBattleCount = 3;</code>
     * @return Whether the gatherBattleCount field is set.
     */
    @java.lang.Override
    public boolean hasGatherBattleCount() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int32 gatherBattleCount = 3;</code>
     * @return The gatherBattleCount.
     */
    @java.lang.Override
    public int getGatherBattleCount() {
      return gatherBattleCount_;
    }
    /**
     * <code>optional int32 gatherBattleCount = 3;</code>
     * @param value The gatherBattleCount to set.
     * @return This builder for chaining.
     */
    public Builder setGatherBattleCount(int value) {

      gatherBattleCount_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 gatherBattleCount = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearGatherBattleCount() {
      bitField0_ = (bitField0_ & ~0x00000004);
      gatherBattleCount_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object reward_ = "";
    /**
     * <code>optional string reward = 4;</code>
     * @return Whether the reward field is set.
     */
    public boolean hasReward() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional string reward = 4;</code>
     * @return The reward.
     */
    public java.lang.String getReward() {
      java.lang.Object ref = reward_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          reward_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string reward = 4;</code>
     * @return The bytes for reward.
     */
    public com.google.protobuf.ByteString
        getRewardBytes() {
      java.lang.Object ref = reward_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        reward_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string reward = 4;</code>
     * @param value The reward to set.
     * @return This builder for chaining.
     */
    public Builder setReward(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      reward_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional string reward = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearReward() {
      reward_ = getDefaultInstance().getReward();
      bitField0_ = (bitField0_ & ~0x00000008);
      onChanged();
      return this;
    }
    /**
     * <code>optional string reward = 4;</code>
     * @param value The bytes for reward to set.
     * @return This builder for chaining.
     */
    public Builder setRewardBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      reward_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.HeavenBattleDelegateRewardMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.HeavenBattleDelegateRewardMsg)
  private static final xddq.pb.HeavenBattleDelegateRewardMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.HeavenBattleDelegateRewardMsg();
  }

  public static xddq.pb.HeavenBattleDelegateRewardMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<HeavenBattleDelegateRewardMsg>
      PARSER = new com.google.protobuf.AbstractParser<HeavenBattleDelegateRewardMsg>() {
    @java.lang.Override
    public HeavenBattleDelegateRewardMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<HeavenBattleDelegateRewardMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<HeavenBattleDelegateRewardMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.HeavenBattleDelegateRewardMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

