// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.KunlunWarMemberListResp}
 */
public final class KunlunWarMemberListResp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.KunlunWarMemberListResp)
    KunlunWarMemberListRespOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      KunlunWarMemberListResp.class.getName());
  }
  // Use KunlunWarMemberListResp.newBuilder() to construct.
  private KunlunWarMemberListResp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private KunlunWarMemberListResp() {
    memberList_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_KunlunWarMemberListResp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_KunlunWarMemberListResp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.KunlunWarMemberListResp.class, xddq.pb.KunlunWarMemberListResp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>optional int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int MEMBERLIST_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.KunlunWarMemberInfo> memberList_;
  /**
   * <code>repeated .xddq.pb.KunlunWarMemberInfo memberList = 2;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.KunlunWarMemberInfo> getMemberListList() {
    return memberList_;
  }
  /**
   * <code>repeated .xddq.pb.KunlunWarMemberInfo memberList = 2;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.KunlunWarMemberInfoOrBuilder> 
      getMemberListOrBuilderList() {
    return memberList_;
  }
  /**
   * <code>repeated .xddq.pb.KunlunWarMemberInfo memberList = 2;</code>
   */
  @java.lang.Override
  public int getMemberListCount() {
    return memberList_.size();
  }
  /**
   * <code>repeated .xddq.pb.KunlunWarMemberInfo memberList = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.KunlunWarMemberInfo getMemberList(int index) {
    return memberList_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.KunlunWarMemberInfo memberList = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.KunlunWarMemberInfoOrBuilder getMemberListOrBuilder(
      int index) {
    return memberList_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    for (int i = 0; i < memberList_.size(); i++) {
      output.writeMessage(2, memberList_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    for (int i = 0; i < memberList_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, memberList_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.KunlunWarMemberListResp)) {
      return super.equals(obj);
    }
    xddq.pb.KunlunWarMemberListResp other = (xddq.pb.KunlunWarMemberListResp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (!getMemberListList()
        .equals(other.getMemberListList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (getMemberListCount() > 0) {
      hash = (37 * hash) + MEMBERLIST_FIELD_NUMBER;
      hash = (53 * hash) + getMemberListList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.KunlunWarMemberListResp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.KunlunWarMemberListResp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.KunlunWarMemberListResp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.KunlunWarMemberListResp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.KunlunWarMemberListResp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.KunlunWarMemberListResp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.KunlunWarMemberListResp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.KunlunWarMemberListResp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.KunlunWarMemberListResp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.KunlunWarMemberListResp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.KunlunWarMemberListResp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.KunlunWarMemberListResp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.KunlunWarMemberListResp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.KunlunWarMemberListResp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.KunlunWarMemberListResp)
      xddq.pb.KunlunWarMemberListRespOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_KunlunWarMemberListResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_KunlunWarMemberListResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.KunlunWarMemberListResp.class, xddq.pb.KunlunWarMemberListResp.Builder.class);
    }

    // Construct using xddq.pb.KunlunWarMemberListResp.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      if (memberListBuilder_ == null) {
        memberList_ = java.util.Collections.emptyList();
      } else {
        memberList_ = null;
        memberListBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000002);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_KunlunWarMemberListResp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.KunlunWarMemberListResp getDefaultInstanceForType() {
      return xddq.pb.KunlunWarMemberListResp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.KunlunWarMemberListResp build() {
      xddq.pb.KunlunWarMemberListResp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.KunlunWarMemberListResp buildPartial() {
      xddq.pb.KunlunWarMemberListResp result = new xddq.pb.KunlunWarMemberListResp(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.KunlunWarMemberListResp result) {
      if (memberListBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          memberList_ = java.util.Collections.unmodifiableList(memberList_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.memberList_ = memberList_;
      } else {
        result.memberList_ = memberListBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.KunlunWarMemberListResp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.KunlunWarMemberListResp) {
        return mergeFrom((xddq.pb.KunlunWarMemberListResp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.KunlunWarMemberListResp other) {
      if (other == xddq.pb.KunlunWarMemberListResp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (memberListBuilder_ == null) {
        if (!other.memberList_.isEmpty()) {
          if (memberList_.isEmpty()) {
            memberList_ = other.memberList_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureMemberListIsMutable();
            memberList_.addAll(other.memberList_);
          }
          onChanged();
        }
      } else {
        if (!other.memberList_.isEmpty()) {
          if (memberListBuilder_.isEmpty()) {
            memberListBuilder_.dispose();
            memberListBuilder_ = null;
            memberList_ = other.memberList_;
            bitField0_ = (bitField0_ & ~0x00000002);
            memberListBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetMemberListFieldBuilder() : null;
          } else {
            memberListBuilder_.addAllMessages(other.memberList_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              xddq.pb.KunlunWarMemberInfo m =
                  input.readMessage(
                      xddq.pb.KunlunWarMemberInfo.parser(),
                      extensionRegistry);
              if (memberListBuilder_ == null) {
                ensureMemberListIsMutable();
                memberList_.add(m);
              } else {
                memberListBuilder_.addMessage(m);
              }
              break;
            } // case 18
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>optional int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>optional int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.KunlunWarMemberInfo> memberList_ =
      java.util.Collections.emptyList();
    private void ensureMemberListIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        memberList_ = new java.util.ArrayList<xddq.pb.KunlunWarMemberInfo>(memberList_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.KunlunWarMemberInfo, xddq.pb.KunlunWarMemberInfo.Builder, xddq.pb.KunlunWarMemberInfoOrBuilder> memberListBuilder_;

    /**
     * <code>repeated .xddq.pb.KunlunWarMemberInfo memberList = 2;</code>
     */
    public java.util.List<xddq.pb.KunlunWarMemberInfo> getMemberListList() {
      if (memberListBuilder_ == null) {
        return java.util.Collections.unmodifiableList(memberList_);
      } else {
        return memberListBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarMemberInfo memberList = 2;</code>
     */
    public int getMemberListCount() {
      if (memberListBuilder_ == null) {
        return memberList_.size();
      } else {
        return memberListBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarMemberInfo memberList = 2;</code>
     */
    public xddq.pb.KunlunWarMemberInfo getMemberList(int index) {
      if (memberListBuilder_ == null) {
        return memberList_.get(index);
      } else {
        return memberListBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarMemberInfo memberList = 2;</code>
     */
    public Builder setMemberList(
        int index, xddq.pb.KunlunWarMemberInfo value) {
      if (memberListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMemberListIsMutable();
        memberList_.set(index, value);
        onChanged();
      } else {
        memberListBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarMemberInfo memberList = 2;</code>
     */
    public Builder setMemberList(
        int index, xddq.pb.KunlunWarMemberInfo.Builder builderForValue) {
      if (memberListBuilder_ == null) {
        ensureMemberListIsMutable();
        memberList_.set(index, builderForValue.build());
        onChanged();
      } else {
        memberListBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarMemberInfo memberList = 2;</code>
     */
    public Builder addMemberList(xddq.pb.KunlunWarMemberInfo value) {
      if (memberListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMemberListIsMutable();
        memberList_.add(value);
        onChanged();
      } else {
        memberListBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarMemberInfo memberList = 2;</code>
     */
    public Builder addMemberList(
        int index, xddq.pb.KunlunWarMemberInfo value) {
      if (memberListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMemberListIsMutable();
        memberList_.add(index, value);
        onChanged();
      } else {
        memberListBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarMemberInfo memberList = 2;</code>
     */
    public Builder addMemberList(
        xddq.pb.KunlunWarMemberInfo.Builder builderForValue) {
      if (memberListBuilder_ == null) {
        ensureMemberListIsMutable();
        memberList_.add(builderForValue.build());
        onChanged();
      } else {
        memberListBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarMemberInfo memberList = 2;</code>
     */
    public Builder addMemberList(
        int index, xddq.pb.KunlunWarMemberInfo.Builder builderForValue) {
      if (memberListBuilder_ == null) {
        ensureMemberListIsMutable();
        memberList_.add(index, builderForValue.build());
        onChanged();
      } else {
        memberListBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarMemberInfo memberList = 2;</code>
     */
    public Builder addAllMemberList(
        java.lang.Iterable<? extends xddq.pb.KunlunWarMemberInfo> values) {
      if (memberListBuilder_ == null) {
        ensureMemberListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, memberList_);
        onChanged();
      } else {
        memberListBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarMemberInfo memberList = 2;</code>
     */
    public Builder clearMemberList() {
      if (memberListBuilder_ == null) {
        memberList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        memberListBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarMemberInfo memberList = 2;</code>
     */
    public Builder removeMemberList(int index) {
      if (memberListBuilder_ == null) {
        ensureMemberListIsMutable();
        memberList_.remove(index);
        onChanged();
      } else {
        memberListBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarMemberInfo memberList = 2;</code>
     */
    public xddq.pb.KunlunWarMemberInfo.Builder getMemberListBuilder(
        int index) {
      return internalGetMemberListFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarMemberInfo memberList = 2;</code>
     */
    public xddq.pb.KunlunWarMemberInfoOrBuilder getMemberListOrBuilder(
        int index) {
      if (memberListBuilder_ == null) {
        return memberList_.get(index);  } else {
        return memberListBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarMemberInfo memberList = 2;</code>
     */
    public java.util.List<? extends xddq.pb.KunlunWarMemberInfoOrBuilder> 
         getMemberListOrBuilderList() {
      if (memberListBuilder_ != null) {
        return memberListBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(memberList_);
      }
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarMemberInfo memberList = 2;</code>
     */
    public xddq.pb.KunlunWarMemberInfo.Builder addMemberListBuilder() {
      return internalGetMemberListFieldBuilder().addBuilder(
          xddq.pb.KunlunWarMemberInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarMemberInfo memberList = 2;</code>
     */
    public xddq.pb.KunlunWarMemberInfo.Builder addMemberListBuilder(
        int index) {
      return internalGetMemberListFieldBuilder().addBuilder(
          index, xddq.pb.KunlunWarMemberInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarMemberInfo memberList = 2;</code>
     */
    public java.util.List<xddq.pb.KunlunWarMemberInfo.Builder> 
         getMemberListBuilderList() {
      return internalGetMemberListFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.KunlunWarMemberInfo, xddq.pb.KunlunWarMemberInfo.Builder, xddq.pb.KunlunWarMemberInfoOrBuilder> 
        internalGetMemberListFieldBuilder() {
      if (memberListBuilder_ == null) {
        memberListBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.KunlunWarMemberInfo, xddq.pb.KunlunWarMemberInfo.Builder, xddq.pb.KunlunWarMemberInfoOrBuilder>(
                memberList_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        memberList_ = null;
      }
      return memberListBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.KunlunWarMemberListResp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.KunlunWarMemberListResp)
  private static final xddq.pb.KunlunWarMemberListResp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.KunlunWarMemberListResp();
  }

  public static xddq.pb.KunlunWarMemberListResp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<KunlunWarMemberListResp>
      PARSER = new com.google.protobuf.AbstractParser<KunlunWarMemberListResp>() {
    @java.lang.Override
    public KunlunWarMemberListResp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<KunlunWarMemberListResp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<KunlunWarMemberListResp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.KunlunWarMemberListResp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

