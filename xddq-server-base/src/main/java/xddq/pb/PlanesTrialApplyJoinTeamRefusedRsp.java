// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.PlanesTrialApplyJoinTeamRefusedRsp}
 */
public final class PlanesTrialApplyJoinTeamRefusedRsp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.PlanesTrialApplyJoinTeamRefusedRsp)
    PlanesTrialApplyJoinTeamRefusedRspOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      PlanesTrialApplyJoinTeamRefusedRsp.class.getName());
  }
  // Use PlanesTrialApplyJoinTeamRefusedRsp.newBuilder() to construct.
  private PlanesTrialApplyJoinTeamRefusedRsp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private PlanesTrialApplyJoinTeamRefusedRsp() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_PlanesTrialApplyJoinTeamRefusedRsp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_PlanesTrialApplyJoinTeamRefusedRsp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.PlanesTrialApplyJoinTeamRefusedRsp.class, xddq.pb.PlanesTrialApplyJoinTeamRefusedRsp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int TEAMINFO_FIELD_NUMBER = 2;
  private xddq.pb.PlanesTrialInnerTeamEntity teamInfo_;
  /**
   * <code>optional .xddq.pb.PlanesTrialInnerTeamEntity teamInfo = 2;</code>
   * @return Whether the teamInfo field is set.
   */
  @java.lang.Override
  public boolean hasTeamInfo() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional .xddq.pb.PlanesTrialInnerTeamEntity teamInfo = 2;</code>
   * @return The teamInfo.
   */
  @java.lang.Override
  public xddq.pb.PlanesTrialInnerTeamEntity getTeamInfo() {
    return teamInfo_ == null ? xddq.pb.PlanesTrialInnerTeamEntity.getDefaultInstance() : teamInfo_;
  }
  /**
   * <code>optional .xddq.pb.PlanesTrialInnerTeamEntity teamInfo = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.PlanesTrialInnerTeamEntityOrBuilder getTeamInfoOrBuilder() {
    return teamInfo_ == null ? xddq.pb.PlanesTrialInnerTeamEntity.getDefaultInstance() : teamInfo_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeMessage(2, getTeamInfo());
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getTeamInfo());
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.PlanesTrialApplyJoinTeamRefusedRsp)) {
      return super.equals(obj);
    }
    xddq.pb.PlanesTrialApplyJoinTeamRefusedRsp other = (xddq.pb.PlanesTrialApplyJoinTeamRefusedRsp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (hasTeamInfo() != other.hasTeamInfo()) return false;
    if (hasTeamInfo()) {
      if (!getTeamInfo()
          .equals(other.getTeamInfo())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (hasTeamInfo()) {
      hash = (37 * hash) + TEAMINFO_FIELD_NUMBER;
      hash = (53 * hash) + getTeamInfo().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.PlanesTrialApplyJoinTeamRefusedRsp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PlanesTrialApplyJoinTeamRefusedRsp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PlanesTrialApplyJoinTeamRefusedRsp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PlanesTrialApplyJoinTeamRefusedRsp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PlanesTrialApplyJoinTeamRefusedRsp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PlanesTrialApplyJoinTeamRefusedRsp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PlanesTrialApplyJoinTeamRefusedRsp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.PlanesTrialApplyJoinTeamRefusedRsp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.PlanesTrialApplyJoinTeamRefusedRsp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.PlanesTrialApplyJoinTeamRefusedRsp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.PlanesTrialApplyJoinTeamRefusedRsp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.PlanesTrialApplyJoinTeamRefusedRsp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.PlanesTrialApplyJoinTeamRefusedRsp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.PlanesTrialApplyJoinTeamRefusedRsp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.PlanesTrialApplyJoinTeamRefusedRsp)
      xddq.pb.PlanesTrialApplyJoinTeamRefusedRspOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PlanesTrialApplyJoinTeamRefusedRsp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PlanesTrialApplyJoinTeamRefusedRsp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.PlanesTrialApplyJoinTeamRefusedRsp.class, xddq.pb.PlanesTrialApplyJoinTeamRefusedRsp.Builder.class);
    }

    // Construct using xddq.pb.PlanesTrialApplyJoinTeamRefusedRsp.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetTeamInfoFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      teamInfo_ = null;
      if (teamInfoBuilder_ != null) {
        teamInfoBuilder_.dispose();
        teamInfoBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PlanesTrialApplyJoinTeamRefusedRsp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.PlanesTrialApplyJoinTeamRefusedRsp getDefaultInstanceForType() {
      return xddq.pb.PlanesTrialApplyJoinTeamRefusedRsp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.PlanesTrialApplyJoinTeamRefusedRsp build() {
      xddq.pb.PlanesTrialApplyJoinTeamRefusedRsp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.PlanesTrialApplyJoinTeamRefusedRsp buildPartial() {
      xddq.pb.PlanesTrialApplyJoinTeamRefusedRsp result = new xddq.pb.PlanesTrialApplyJoinTeamRefusedRsp(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.PlanesTrialApplyJoinTeamRefusedRsp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.teamInfo_ = teamInfoBuilder_ == null
            ? teamInfo_
            : teamInfoBuilder_.build();
        to_bitField0_ |= 0x00000002;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.PlanesTrialApplyJoinTeamRefusedRsp) {
        return mergeFrom((xddq.pb.PlanesTrialApplyJoinTeamRefusedRsp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.PlanesTrialApplyJoinTeamRefusedRsp other) {
      if (other == xddq.pb.PlanesTrialApplyJoinTeamRefusedRsp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (other.hasTeamInfo()) {
        mergeTeamInfo(other.getTeamInfo());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              input.readMessage(
                  internalGetTeamInfoFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private xddq.pb.PlanesTrialInnerTeamEntity teamInfo_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PlanesTrialInnerTeamEntity, xddq.pb.PlanesTrialInnerTeamEntity.Builder, xddq.pb.PlanesTrialInnerTeamEntityOrBuilder> teamInfoBuilder_;
    /**
     * <code>optional .xddq.pb.PlanesTrialInnerTeamEntity teamInfo = 2;</code>
     * @return Whether the teamInfo field is set.
     */
    public boolean hasTeamInfo() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .xddq.pb.PlanesTrialInnerTeamEntity teamInfo = 2;</code>
     * @return The teamInfo.
     */
    public xddq.pb.PlanesTrialInnerTeamEntity getTeamInfo() {
      if (teamInfoBuilder_ == null) {
        return teamInfo_ == null ? xddq.pb.PlanesTrialInnerTeamEntity.getDefaultInstance() : teamInfo_;
      } else {
        return teamInfoBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.PlanesTrialInnerTeamEntity teamInfo = 2;</code>
     */
    public Builder setTeamInfo(xddq.pb.PlanesTrialInnerTeamEntity value) {
      if (teamInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        teamInfo_ = value;
      } else {
        teamInfoBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlanesTrialInnerTeamEntity teamInfo = 2;</code>
     */
    public Builder setTeamInfo(
        xddq.pb.PlanesTrialInnerTeamEntity.Builder builderForValue) {
      if (teamInfoBuilder_ == null) {
        teamInfo_ = builderForValue.build();
      } else {
        teamInfoBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlanesTrialInnerTeamEntity teamInfo = 2;</code>
     */
    public Builder mergeTeamInfo(xddq.pb.PlanesTrialInnerTeamEntity value) {
      if (teamInfoBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0) &&
          teamInfo_ != null &&
          teamInfo_ != xddq.pb.PlanesTrialInnerTeamEntity.getDefaultInstance()) {
          getTeamInfoBuilder().mergeFrom(value);
        } else {
          teamInfo_ = value;
        }
      } else {
        teamInfoBuilder_.mergeFrom(value);
      }
      if (teamInfo_ != null) {
        bitField0_ |= 0x00000002;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlanesTrialInnerTeamEntity teamInfo = 2;</code>
     */
    public Builder clearTeamInfo() {
      bitField0_ = (bitField0_ & ~0x00000002);
      teamInfo_ = null;
      if (teamInfoBuilder_ != null) {
        teamInfoBuilder_.dispose();
        teamInfoBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlanesTrialInnerTeamEntity teamInfo = 2;</code>
     */
    public xddq.pb.PlanesTrialInnerTeamEntity.Builder getTeamInfoBuilder() {
      bitField0_ |= 0x00000002;
      onChanged();
      return internalGetTeamInfoFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.PlanesTrialInnerTeamEntity teamInfo = 2;</code>
     */
    public xddq.pb.PlanesTrialInnerTeamEntityOrBuilder getTeamInfoOrBuilder() {
      if (teamInfoBuilder_ != null) {
        return teamInfoBuilder_.getMessageOrBuilder();
      } else {
        return teamInfo_ == null ?
            xddq.pb.PlanesTrialInnerTeamEntity.getDefaultInstance() : teamInfo_;
      }
    }
    /**
     * <code>optional .xddq.pb.PlanesTrialInnerTeamEntity teamInfo = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PlanesTrialInnerTeamEntity, xddq.pb.PlanesTrialInnerTeamEntity.Builder, xddq.pb.PlanesTrialInnerTeamEntityOrBuilder> 
        internalGetTeamInfoFieldBuilder() {
      if (teamInfoBuilder_ == null) {
        teamInfoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.PlanesTrialInnerTeamEntity, xddq.pb.PlanesTrialInnerTeamEntity.Builder, xddq.pb.PlanesTrialInnerTeamEntityOrBuilder>(
                getTeamInfo(),
                getParentForChildren(),
                isClean());
        teamInfo_ = null;
      }
      return teamInfoBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.PlanesTrialApplyJoinTeamRefusedRsp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.PlanesTrialApplyJoinTeamRefusedRsp)
  private static final xddq.pb.PlanesTrialApplyJoinTeamRefusedRsp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.PlanesTrialApplyJoinTeamRefusedRsp();
  }

  public static xddq.pb.PlanesTrialApplyJoinTeamRefusedRsp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<PlanesTrialApplyJoinTeamRefusedRsp>
      PARSER = new com.google.protobuf.AbstractParser<PlanesTrialApplyJoinTeamRefusedRsp>() {
    @java.lang.Override
    public PlanesTrialApplyJoinTeamRefusedRsp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<PlanesTrialApplyJoinTeamRefusedRsp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<PlanesTrialApplyJoinTeamRefusedRsp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.PlanesTrialApplyJoinTeamRefusedRsp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

