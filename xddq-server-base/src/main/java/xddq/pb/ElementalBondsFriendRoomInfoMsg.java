// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.ElementalBondsFriendRoomInfoMsg}
 */
public final class ElementalBondsFriendRoomInfoMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.ElementalBondsFriendRoomInfoMsg)
    ElementalBondsFriendRoomInfoMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      ElementalBondsFriendRoomInfoMsg.class.getName());
  }
  // Use ElementalBondsFriendRoomInfoMsg.newBuilder() to construct.
  private ElementalBondsFriendRoomInfoMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private ElementalBondsFriendRoomInfoMsg() {
    roomId_ = "";
    skillId1_ = emptyIntList();
    skillId2_ = emptyIntList();
    cardPool_ = emptyIntList();
    skillPool_ = emptyIntList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_ElementalBondsFriendRoomInfoMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_ElementalBondsFriendRoomInfoMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.ElementalBondsFriendRoomInfoMsg.class, xddq.pb.ElementalBondsFriendRoomInfoMsg.Builder.class);
  }

  private int bitField0_;
  public static final int ROOMID_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private volatile java.lang.Object roomId_ = "";
  /**
   * <code>required string roomId = 1;</code>
   * @return Whether the roomId field is set.
   */
  @java.lang.Override
  public boolean hasRoomId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required string roomId = 1;</code>
   * @return The roomId.
   */
  @java.lang.Override
  public java.lang.String getRoomId() {
    java.lang.Object ref = roomId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        roomId_ = s;
      }
      return s;
    }
  }
  /**
   * <code>required string roomId = 1;</code>
   * @return The bytes for roomId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getRoomIdBytes() {
    java.lang.Object ref = roomId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      roomId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int FRIEND_FIELD_NUMBER = 2;
  private xddq.pb.ElementalBondsOpponentMsg friend_;
  /**
   * <code>optional .xddq.pb.ElementalBondsOpponentMsg friend = 2;</code>
   * @return Whether the friend field is set.
   */
  @java.lang.Override
  public boolean hasFriend() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional .xddq.pb.ElementalBondsOpponentMsg friend = 2;</code>
   * @return The friend.
   */
  @java.lang.Override
  public xddq.pb.ElementalBondsOpponentMsg getFriend() {
    return friend_ == null ? xddq.pb.ElementalBondsOpponentMsg.getDefaultInstance() : friend_;
  }
  /**
   * <code>optional .xddq.pb.ElementalBondsOpponentMsg friend = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.ElementalBondsOpponentMsgOrBuilder getFriendOrBuilder() {
    return friend_ == null ? xddq.pb.ElementalBondsOpponentMsg.getDefaultInstance() : friend_;
  }

  public static final int SCENEID_FIELD_NUMBER = 3;
  private int sceneId_ = 0;
  /**
   * <code>optional int32 sceneId = 3;</code>
   * @return Whether the sceneId field is set.
   */
  @java.lang.Override
  public boolean hasSceneId() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional int32 sceneId = 3;</code>
   * @return The sceneId.
   */
  @java.lang.Override
  public int getSceneId() {
    return sceneId_;
  }

  public static final int STEPOUT_FIELD_NUMBER = 4;
  private boolean stepOut_ = false;
  /**
   * <code>optional bool stepOut = 4;</code>
   * @return Whether the stepOut field is set.
   */
  @java.lang.Override
  public boolean hasStepOut() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional bool stepOut = 4;</code>
   * @return The stepOut.
   */
  @java.lang.Override
  public boolean getStepOut() {
    return stepOut_;
  }

  public static final int MASTERUSERID_FIELD_NUMBER = 5;
  private long masterUserId_ = 0L;
  /**
   * <code>optional int64 masterUserId = 5;</code>
   * @return Whether the masterUserId field is set.
   */
  @java.lang.Override
  public boolean hasMasterUserId() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>optional int64 masterUserId = 5;</code>
   * @return The masterUserId.
   */
  @java.lang.Override
  public long getMasterUserId() {
    return masterUserId_;
  }

  public static final int CARDID1_FIELD_NUMBER = 6;
  private int cardId1_ = 0;
  /**
   * <code>optional int32 cardId1 = 6;</code>
   * @return Whether the cardId1 field is set.
   */
  @java.lang.Override
  public boolean hasCardId1() {
    return ((bitField0_ & 0x00000020) != 0);
  }
  /**
   * <code>optional int32 cardId1 = 6;</code>
   * @return The cardId1.
   */
  @java.lang.Override
  public int getCardId1() {
    return cardId1_;
  }

  public static final int CARDID2_FIELD_NUMBER = 7;
  private int cardId2_ = 0;
  /**
   * <code>optional int32 cardId2 = 7;</code>
   * @return Whether the cardId2 field is set.
   */
  @java.lang.Override
  public boolean hasCardId2() {
    return ((bitField0_ & 0x00000040) != 0);
  }
  /**
   * <code>optional int32 cardId2 = 7;</code>
   * @return The cardId2.
   */
  @java.lang.Override
  public int getCardId2() {
    return cardId2_;
  }

  public static final int READY_FIELD_NUMBER = 8;
  private boolean ready_ = false;
  /**
   * <code>optional bool ready = 8;</code>
   * @return Whether the ready field is set.
   */
  @java.lang.Override
  public boolean hasReady() {
    return ((bitField0_ & 0x00000080) != 0);
  }
  /**
   * <code>optional bool ready = 8;</code>
   * @return The ready.
   */
  @java.lang.Override
  public boolean getReady() {
    return ready_;
  }

  public static final int SKILLID1_FIELD_NUMBER = 9;
  @SuppressWarnings("serial")
  private com.google.protobuf.Internal.IntList skillId1_ =
      emptyIntList();
  /**
   * <code>repeated int32 skillId1 = 9;</code>
   * @return A list containing the skillId1.
   */
  @java.lang.Override
  public java.util.List<java.lang.Integer>
      getSkillId1List() {
    return skillId1_;
  }
  /**
   * <code>repeated int32 skillId1 = 9;</code>
   * @return The count of skillId1.
   */
  public int getSkillId1Count() {
    return skillId1_.size();
  }
  /**
   * <code>repeated int32 skillId1 = 9;</code>
   * @param index The index of the element to return.
   * @return The skillId1 at the given index.
   */
  public int getSkillId1(int index) {
    return skillId1_.getInt(index);
  }

  public static final int SKILLID2_FIELD_NUMBER = 10;
  @SuppressWarnings("serial")
  private com.google.protobuf.Internal.IntList skillId2_ =
      emptyIntList();
  /**
   * <code>repeated int32 skillId2 = 10;</code>
   * @return A list containing the skillId2.
   */
  @java.lang.Override
  public java.util.List<java.lang.Integer>
      getSkillId2List() {
    return skillId2_;
  }
  /**
   * <code>repeated int32 skillId2 = 10;</code>
   * @return The count of skillId2.
   */
  public int getSkillId2Count() {
    return skillId2_.size();
  }
  /**
   * <code>repeated int32 skillId2 = 10;</code>
   * @param index The index of the element to return.
   * @return The skillId2 at the given index.
   */
  public int getSkillId2(int index) {
    return skillId2_.getInt(index);
  }

  public static final int CARDPOOL_FIELD_NUMBER = 11;
  @SuppressWarnings("serial")
  private com.google.protobuf.Internal.IntList cardPool_ =
      emptyIntList();
  /**
   * <code>repeated int32 cardPool = 11;</code>
   * @return A list containing the cardPool.
   */
  @java.lang.Override
  public java.util.List<java.lang.Integer>
      getCardPoolList() {
    return cardPool_;
  }
  /**
   * <code>repeated int32 cardPool = 11;</code>
   * @return The count of cardPool.
   */
  public int getCardPoolCount() {
    return cardPool_.size();
  }
  /**
   * <code>repeated int32 cardPool = 11;</code>
   * @param index The index of the element to return.
   * @return The cardPool at the given index.
   */
  public int getCardPool(int index) {
    return cardPool_.getInt(index);
  }

  public static final int SKILLPOOL_FIELD_NUMBER = 12;
  @SuppressWarnings("serial")
  private com.google.protobuf.Internal.IntList skillPool_ =
      emptyIntList();
  /**
   * <code>repeated int32 skillPool = 12;</code>
   * @return A list containing the skillPool.
   */
  @java.lang.Override
  public java.util.List<java.lang.Integer>
      getSkillPoolList() {
    return skillPool_;
  }
  /**
   * <code>repeated int32 skillPool = 12;</code>
   * @return The count of skillPool.
   */
  public int getSkillPoolCount() {
    return skillPool_.size();
  }
  /**
   * <code>repeated int32 skillPool = 12;</code>
   * @param index The index of the element to return.
   * @return The skillPool at the given index.
   */
  public int getSkillPool(int index) {
    return skillPool_.getInt(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRoomId()) {
      memoizedIsInitialized = 0;
      return false;
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 1, roomId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeMessage(2, getFriend());
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt32(3, sceneId_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeBool(4, stepOut_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      output.writeInt64(5, masterUserId_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      output.writeInt32(6, cardId1_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      output.writeInt32(7, cardId2_);
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      output.writeBool(8, ready_);
    }
    for (int i = 0; i < skillId1_.size(); i++) {
      output.writeInt32(9, skillId1_.getInt(i));
    }
    for (int i = 0; i < skillId2_.size(); i++) {
      output.writeInt32(10, skillId2_.getInt(i));
    }
    for (int i = 0; i < cardPool_.size(); i++) {
      output.writeInt32(11, cardPool_.getInt(i));
    }
    for (int i = 0; i < skillPool_.size(); i++) {
      output.writeInt32(12, skillPool_.getInt(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(1, roomId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getFriend());
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(3, sceneId_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(4, stepOut_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(5, masterUserId_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(6, cardId1_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(7, cardId2_);
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(8, ready_);
    }
    {
      int dataSize = 0;
      for (int i = 0; i < skillId1_.size(); i++) {
        dataSize += com.google.protobuf.CodedOutputStream
          .computeInt32SizeNoTag(skillId1_.getInt(i));
      }
      size += dataSize;
      size += 1 * getSkillId1List().size();
    }
    {
      int dataSize = 0;
      for (int i = 0; i < skillId2_.size(); i++) {
        dataSize += com.google.protobuf.CodedOutputStream
          .computeInt32SizeNoTag(skillId2_.getInt(i));
      }
      size += dataSize;
      size += 1 * getSkillId2List().size();
    }
    {
      int dataSize = 0;
      for (int i = 0; i < cardPool_.size(); i++) {
        dataSize += com.google.protobuf.CodedOutputStream
          .computeInt32SizeNoTag(cardPool_.getInt(i));
      }
      size += dataSize;
      size += 1 * getCardPoolList().size();
    }
    {
      int dataSize = 0;
      for (int i = 0; i < skillPool_.size(); i++) {
        dataSize += com.google.protobuf.CodedOutputStream
          .computeInt32SizeNoTag(skillPool_.getInt(i));
      }
      size += dataSize;
      size += 1 * getSkillPoolList().size();
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.ElementalBondsFriendRoomInfoMsg)) {
      return super.equals(obj);
    }
    xddq.pb.ElementalBondsFriendRoomInfoMsg other = (xddq.pb.ElementalBondsFriendRoomInfoMsg) obj;

    if (hasRoomId() != other.hasRoomId()) return false;
    if (hasRoomId()) {
      if (!getRoomId()
          .equals(other.getRoomId())) return false;
    }
    if (hasFriend() != other.hasFriend()) return false;
    if (hasFriend()) {
      if (!getFriend()
          .equals(other.getFriend())) return false;
    }
    if (hasSceneId() != other.hasSceneId()) return false;
    if (hasSceneId()) {
      if (getSceneId()
          != other.getSceneId()) return false;
    }
    if (hasStepOut() != other.hasStepOut()) return false;
    if (hasStepOut()) {
      if (getStepOut()
          != other.getStepOut()) return false;
    }
    if (hasMasterUserId() != other.hasMasterUserId()) return false;
    if (hasMasterUserId()) {
      if (getMasterUserId()
          != other.getMasterUserId()) return false;
    }
    if (hasCardId1() != other.hasCardId1()) return false;
    if (hasCardId1()) {
      if (getCardId1()
          != other.getCardId1()) return false;
    }
    if (hasCardId2() != other.hasCardId2()) return false;
    if (hasCardId2()) {
      if (getCardId2()
          != other.getCardId2()) return false;
    }
    if (hasReady() != other.hasReady()) return false;
    if (hasReady()) {
      if (getReady()
          != other.getReady()) return false;
    }
    if (!getSkillId1List()
        .equals(other.getSkillId1List())) return false;
    if (!getSkillId2List()
        .equals(other.getSkillId2List())) return false;
    if (!getCardPoolList()
        .equals(other.getCardPoolList())) return false;
    if (!getSkillPoolList()
        .equals(other.getSkillPoolList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRoomId()) {
      hash = (37 * hash) + ROOMID_FIELD_NUMBER;
      hash = (53 * hash) + getRoomId().hashCode();
    }
    if (hasFriend()) {
      hash = (37 * hash) + FRIEND_FIELD_NUMBER;
      hash = (53 * hash) + getFriend().hashCode();
    }
    if (hasSceneId()) {
      hash = (37 * hash) + SCENEID_FIELD_NUMBER;
      hash = (53 * hash) + getSceneId();
    }
    if (hasStepOut()) {
      hash = (37 * hash) + STEPOUT_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getStepOut());
    }
    if (hasMasterUserId()) {
      hash = (37 * hash) + MASTERUSERID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getMasterUserId());
    }
    if (hasCardId1()) {
      hash = (37 * hash) + CARDID1_FIELD_NUMBER;
      hash = (53 * hash) + getCardId1();
    }
    if (hasCardId2()) {
      hash = (37 * hash) + CARDID2_FIELD_NUMBER;
      hash = (53 * hash) + getCardId2();
    }
    if (hasReady()) {
      hash = (37 * hash) + READY_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getReady());
    }
    if (getSkillId1Count() > 0) {
      hash = (37 * hash) + SKILLID1_FIELD_NUMBER;
      hash = (53 * hash) + getSkillId1List().hashCode();
    }
    if (getSkillId2Count() > 0) {
      hash = (37 * hash) + SKILLID2_FIELD_NUMBER;
      hash = (53 * hash) + getSkillId2List().hashCode();
    }
    if (getCardPoolCount() > 0) {
      hash = (37 * hash) + CARDPOOL_FIELD_NUMBER;
      hash = (53 * hash) + getCardPoolList().hashCode();
    }
    if (getSkillPoolCount() > 0) {
      hash = (37 * hash) + SKILLPOOL_FIELD_NUMBER;
      hash = (53 * hash) + getSkillPoolList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.ElementalBondsFriendRoomInfoMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ElementalBondsFriendRoomInfoMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ElementalBondsFriendRoomInfoMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ElementalBondsFriendRoomInfoMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ElementalBondsFriendRoomInfoMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ElementalBondsFriendRoomInfoMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ElementalBondsFriendRoomInfoMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.ElementalBondsFriendRoomInfoMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.ElementalBondsFriendRoomInfoMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.ElementalBondsFriendRoomInfoMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.ElementalBondsFriendRoomInfoMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.ElementalBondsFriendRoomInfoMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.ElementalBondsFriendRoomInfoMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.ElementalBondsFriendRoomInfoMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.ElementalBondsFriendRoomInfoMsg)
      xddq.pb.ElementalBondsFriendRoomInfoMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ElementalBondsFriendRoomInfoMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ElementalBondsFriendRoomInfoMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.ElementalBondsFriendRoomInfoMsg.class, xddq.pb.ElementalBondsFriendRoomInfoMsg.Builder.class);
    }

    // Construct using xddq.pb.ElementalBondsFriendRoomInfoMsg.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetFriendFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      roomId_ = "";
      friend_ = null;
      if (friendBuilder_ != null) {
        friendBuilder_.dispose();
        friendBuilder_ = null;
      }
      sceneId_ = 0;
      stepOut_ = false;
      masterUserId_ = 0L;
      cardId1_ = 0;
      cardId2_ = 0;
      ready_ = false;
      skillId1_ = emptyIntList();
      skillId2_ = emptyIntList();
      cardPool_ = emptyIntList();
      skillPool_ = emptyIntList();
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ElementalBondsFriendRoomInfoMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.ElementalBondsFriendRoomInfoMsg getDefaultInstanceForType() {
      return xddq.pb.ElementalBondsFriendRoomInfoMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.ElementalBondsFriendRoomInfoMsg build() {
      xddq.pb.ElementalBondsFriendRoomInfoMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.ElementalBondsFriendRoomInfoMsg buildPartial() {
      xddq.pb.ElementalBondsFriendRoomInfoMsg result = new xddq.pb.ElementalBondsFriendRoomInfoMsg(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.ElementalBondsFriendRoomInfoMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.roomId_ = roomId_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.friend_ = friendBuilder_ == null
            ? friend_
            : friendBuilder_.build();
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.sceneId_ = sceneId_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.stepOut_ = stepOut_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.masterUserId_ = masterUserId_;
        to_bitField0_ |= 0x00000010;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.cardId1_ = cardId1_;
        to_bitField0_ |= 0x00000020;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.cardId2_ = cardId2_;
        to_bitField0_ |= 0x00000040;
      }
      if (((from_bitField0_ & 0x00000080) != 0)) {
        result.ready_ = ready_;
        to_bitField0_ |= 0x00000080;
      }
      if (((from_bitField0_ & 0x00000100) != 0)) {
        skillId1_.makeImmutable();
        result.skillId1_ = skillId1_;
      }
      if (((from_bitField0_ & 0x00000200) != 0)) {
        skillId2_.makeImmutable();
        result.skillId2_ = skillId2_;
      }
      if (((from_bitField0_ & 0x00000400) != 0)) {
        cardPool_.makeImmutable();
        result.cardPool_ = cardPool_;
      }
      if (((from_bitField0_ & 0x00000800) != 0)) {
        skillPool_.makeImmutable();
        result.skillPool_ = skillPool_;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.ElementalBondsFriendRoomInfoMsg) {
        return mergeFrom((xddq.pb.ElementalBondsFriendRoomInfoMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.ElementalBondsFriendRoomInfoMsg other) {
      if (other == xddq.pb.ElementalBondsFriendRoomInfoMsg.getDefaultInstance()) return this;
      if (other.hasRoomId()) {
        roomId_ = other.roomId_;
        bitField0_ |= 0x00000001;
        onChanged();
      }
      if (other.hasFriend()) {
        mergeFriend(other.getFriend());
      }
      if (other.hasSceneId()) {
        setSceneId(other.getSceneId());
      }
      if (other.hasStepOut()) {
        setStepOut(other.getStepOut());
      }
      if (other.hasMasterUserId()) {
        setMasterUserId(other.getMasterUserId());
      }
      if (other.hasCardId1()) {
        setCardId1(other.getCardId1());
      }
      if (other.hasCardId2()) {
        setCardId2(other.getCardId2());
      }
      if (other.hasReady()) {
        setReady(other.getReady());
      }
      if (!other.skillId1_.isEmpty()) {
        if (skillId1_.isEmpty()) {
          skillId1_ = other.skillId1_;
          skillId1_.makeImmutable();
          bitField0_ |= 0x00000100;
        } else {
          ensureSkillId1IsMutable();
          skillId1_.addAll(other.skillId1_);
        }
        onChanged();
      }
      if (!other.skillId2_.isEmpty()) {
        if (skillId2_.isEmpty()) {
          skillId2_ = other.skillId2_;
          skillId2_.makeImmutable();
          bitField0_ |= 0x00000200;
        } else {
          ensureSkillId2IsMutable();
          skillId2_.addAll(other.skillId2_);
        }
        onChanged();
      }
      if (!other.cardPool_.isEmpty()) {
        if (cardPool_.isEmpty()) {
          cardPool_ = other.cardPool_;
          cardPool_.makeImmutable();
          bitField0_ |= 0x00000400;
        } else {
          ensureCardPoolIsMutable();
          cardPool_.addAll(other.cardPool_);
        }
        onChanged();
      }
      if (!other.skillPool_.isEmpty()) {
        if (skillPool_.isEmpty()) {
          skillPool_ = other.skillPool_;
          skillPool_.makeImmutable();
          bitField0_ |= 0x00000800;
        } else {
          ensureSkillPoolIsMutable();
          skillPool_.addAll(other.skillPool_);
        }
        onChanged();
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRoomId()) {
        return false;
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              roomId_ = input.readBytes();
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 18: {
              input.readMessage(
                  internalGetFriendFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 24: {
              sceneId_ = input.readInt32();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 32: {
              stepOut_ = input.readBool();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            case 40: {
              masterUserId_ = input.readInt64();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            case 48: {
              cardId1_ = input.readInt32();
              bitField0_ |= 0x00000020;
              break;
            } // case 48
            case 56: {
              cardId2_ = input.readInt32();
              bitField0_ |= 0x00000040;
              break;
            } // case 56
            case 64: {
              ready_ = input.readBool();
              bitField0_ |= 0x00000080;
              break;
            } // case 64
            case 72: {
              int v = input.readInt32();
              ensureSkillId1IsMutable();
              skillId1_.addInt(v);
              break;
            } // case 72
            case 74: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              ensureSkillId1IsMutable();
              while (input.getBytesUntilLimit() > 0) {
                skillId1_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            } // case 74
            case 80: {
              int v = input.readInt32();
              ensureSkillId2IsMutable();
              skillId2_.addInt(v);
              break;
            } // case 80
            case 82: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              ensureSkillId2IsMutable();
              while (input.getBytesUntilLimit() > 0) {
                skillId2_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            } // case 82
            case 88: {
              int v = input.readInt32();
              ensureCardPoolIsMutable();
              cardPool_.addInt(v);
              break;
            } // case 88
            case 90: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              ensureCardPoolIsMutable();
              while (input.getBytesUntilLimit() > 0) {
                cardPool_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            } // case 90
            case 96: {
              int v = input.readInt32();
              ensureSkillPoolIsMutable();
              skillPool_.addInt(v);
              break;
            } // case 96
            case 98: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              ensureSkillPoolIsMutable();
              while (input.getBytesUntilLimit() > 0) {
                skillPool_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            } // case 98
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.lang.Object roomId_ = "";
    /**
     * <code>required string roomId = 1;</code>
     * @return Whether the roomId field is set.
     */
    public boolean hasRoomId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required string roomId = 1;</code>
     * @return The roomId.
     */
    public java.lang.String getRoomId() {
      java.lang.Object ref = roomId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          roomId_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>required string roomId = 1;</code>
     * @return The bytes for roomId.
     */
    public com.google.protobuf.ByteString
        getRoomIdBytes() {
      java.lang.Object ref = roomId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        roomId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>required string roomId = 1;</code>
     * @param value The roomId to set.
     * @return This builder for chaining.
     */
    public Builder setRoomId(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      roomId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required string roomId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRoomId() {
      roomId_ = getDefaultInstance().getRoomId();
      bitField0_ = (bitField0_ & ~0x00000001);
      onChanged();
      return this;
    }
    /**
     * <code>required string roomId = 1;</code>
     * @param value The bytes for roomId to set.
     * @return This builder for chaining.
     */
    public Builder setRoomIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      roomId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }

    private xddq.pb.ElementalBondsOpponentMsg friend_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.ElementalBondsOpponentMsg, xddq.pb.ElementalBondsOpponentMsg.Builder, xddq.pb.ElementalBondsOpponentMsgOrBuilder> friendBuilder_;
    /**
     * <code>optional .xddq.pb.ElementalBondsOpponentMsg friend = 2;</code>
     * @return Whether the friend field is set.
     */
    public boolean hasFriend() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .xddq.pb.ElementalBondsOpponentMsg friend = 2;</code>
     * @return The friend.
     */
    public xddq.pb.ElementalBondsOpponentMsg getFriend() {
      if (friendBuilder_ == null) {
        return friend_ == null ? xddq.pb.ElementalBondsOpponentMsg.getDefaultInstance() : friend_;
      } else {
        return friendBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.ElementalBondsOpponentMsg friend = 2;</code>
     */
    public Builder setFriend(xddq.pb.ElementalBondsOpponentMsg value) {
      if (friendBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        friend_ = value;
      } else {
        friendBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ElementalBondsOpponentMsg friend = 2;</code>
     */
    public Builder setFriend(
        xddq.pb.ElementalBondsOpponentMsg.Builder builderForValue) {
      if (friendBuilder_ == null) {
        friend_ = builderForValue.build();
      } else {
        friendBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ElementalBondsOpponentMsg friend = 2;</code>
     */
    public Builder mergeFriend(xddq.pb.ElementalBondsOpponentMsg value) {
      if (friendBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0) &&
          friend_ != null &&
          friend_ != xddq.pb.ElementalBondsOpponentMsg.getDefaultInstance()) {
          getFriendBuilder().mergeFrom(value);
        } else {
          friend_ = value;
        }
      } else {
        friendBuilder_.mergeFrom(value);
      }
      if (friend_ != null) {
        bitField0_ |= 0x00000002;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.ElementalBondsOpponentMsg friend = 2;</code>
     */
    public Builder clearFriend() {
      bitField0_ = (bitField0_ & ~0x00000002);
      friend_ = null;
      if (friendBuilder_ != null) {
        friendBuilder_.dispose();
        friendBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ElementalBondsOpponentMsg friend = 2;</code>
     */
    public xddq.pb.ElementalBondsOpponentMsg.Builder getFriendBuilder() {
      bitField0_ |= 0x00000002;
      onChanged();
      return internalGetFriendFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.ElementalBondsOpponentMsg friend = 2;</code>
     */
    public xddq.pb.ElementalBondsOpponentMsgOrBuilder getFriendOrBuilder() {
      if (friendBuilder_ != null) {
        return friendBuilder_.getMessageOrBuilder();
      } else {
        return friend_ == null ?
            xddq.pb.ElementalBondsOpponentMsg.getDefaultInstance() : friend_;
      }
    }
    /**
     * <code>optional .xddq.pb.ElementalBondsOpponentMsg friend = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.ElementalBondsOpponentMsg, xddq.pb.ElementalBondsOpponentMsg.Builder, xddq.pb.ElementalBondsOpponentMsgOrBuilder> 
        internalGetFriendFieldBuilder() {
      if (friendBuilder_ == null) {
        friendBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.ElementalBondsOpponentMsg, xddq.pb.ElementalBondsOpponentMsg.Builder, xddq.pb.ElementalBondsOpponentMsgOrBuilder>(
                getFriend(),
                getParentForChildren(),
                isClean());
        friend_ = null;
      }
      return friendBuilder_;
    }

    private int sceneId_ ;
    /**
     * <code>optional int32 sceneId = 3;</code>
     * @return Whether the sceneId field is set.
     */
    @java.lang.Override
    public boolean hasSceneId() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int32 sceneId = 3;</code>
     * @return The sceneId.
     */
    @java.lang.Override
    public int getSceneId() {
      return sceneId_;
    }
    /**
     * <code>optional int32 sceneId = 3;</code>
     * @param value The sceneId to set.
     * @return This builder for chaining.
     */
    public Builder setSceneId(int value) {

      sceneId_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 sceneId = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearSceneId() {
      bitField0_ = (bitField0_ & ~0x00000004);
      sceneId_ = 0;
      onChanged();
      return this;
    }

    private boolean stepOut_ ;
    /**
     * <code>optional bool stepOut = 4;</code>
     * @return Whether the stepOut field is set.
     */
    @java.lang.Override
    public boolean hasStepOut() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional bool stepOut = 4;</code>
     * @return The stepOut.
     */
    @java.lang.Override
    public boolean getStepOut() {
      return stepOut_;
    }
    /**
     * <code>optional bool stepOut = 4;</code>
     * @param value The stepOut to set.
     * @return This builder for chaining.
     */
    public Builder setStepOut(boolean value) {

      stepOut_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional bool stepOut = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearStepOut() {
      bitField0_ = (bitField0_ & ~0x00000008);
      stepOut_ = false;
      onChanged();
      return this;
    }

    private long masterUserId_ ;
    /**
     * <code>optional int64 masterUserId = 5;</code>
     * @return Whether the masterUserId field is set.
     */
    @java.lang.Override
    public boolean hasMasterUserId() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional int64 masterUserId = 5;</code>
     * @return The masterUserId.
     */
    @java.lang.Override
    public long getMasterUserId() {
      return masterUserId_;
    }
    /**
     * <code>optional int64 masterUserId = 5;</code>
     * @param value The masterUserId to set.
     * @return This builder for chaining.
     */
    public Builder setMasterUserId(long value) {

      masterUserId_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 masterUserId = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearMasterUserId() {
      bitField0_ = (bitField0_ & ~0x00000010);
      masterUserId_ = 0L;
      onChanged();
      return this;
    }

    private int cardId1_ ;
    /**
     * <code>optional int32 cardId1 = 6;</code>
     * @return Whether the cardId1 field is set.
     */
    @java.lang.Override
    public boolean hasCardId1() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional int32 cardId1 = 6;</code>
     * @return The cardId1.
     */
    @java.lang.Override
    public int getCardId1() {
      return cardId1_;
    }
    /**
     * <code>optional int32 cardId1 = 6;</code>
     * @param value The cardId1 to set.
     * @return This builder for chaining.
     */
    public Builder setCardId1(int value) {

      cardId1_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 cardId1 = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearCardId1() {
      bitField0_ = (bitField0_ & ~0x00000020);
      cardId1_ = 0;
      onChanged();
      return this;
    }

    private int cardId2_ ;
    /**
     * <code>optional int32 cardId2 = 7;</code>
     * @return Whether the cardId2 field is set.
     */
    @java.lang.Override
    public boolean hasCardId2() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <code>optional int32 cardId2 = 7;</code>
     * @return The cardId2.
     */
    @java.lang.Override
    public int getCardId2() {
      return cardId2_;
    }
    /**
     * <code>optional int32 cardId2 = 7;</code>
     * @param value The cardId2 to set.
     * @return This builder for chaining.
     */
    public Builder setCardId2(int value) {

      cardId2_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 cardId2 = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearCardId2() {
      bitField0_ = (bitField0_ & ~0x00000040);
      cardId2_ = 0;
      onChanged();
      return this;
    }

    private boolean ready_ ;
    /**
     * <code>optional bool ready = 8;</code>
     * @return Whether the ready field is set.
     */
    @java.lang.Override
    public boolean hasReady() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <code>optional bool ready = 8;</code>
     * @return The ready.
     */
    @java.lang.Override
    public boolean getReady() {
      return ready_;
    }
    /**
     * <code>optional bool ready = 8;</code>
     * @param value The ready to set.
     * @return This builder for chaining.
     */
    public Builder setReady(boolean value) {

      ready_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }
    /**
     * <code>optional bool ready = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearReady() {
      bitField0_ = (bitField0_ & ~0x00000080);
      ready_ = false;
      onChanged();
      return this;
    }

    private com.google.protobuf.Internal.IntList skillId1_ = emptyIntList();
    private void ensureSkillId1IsMutable() {
      if (!skillId1_.isModifiable()) {
        skillId1_ = makeMutableCopy(skillId1_);
      }
      bitField0_ |= 0x00000100;
    }
    /**
     * <code>repeated int32 skillId1 = 9;</code>
     * @return A list containing the skillId1.
     */
    public java.util.List<java.lang.Integer>
        getSkillId1List() {
      skillId1_.makeImmutable();
      return skillId1_;
    }
    /**
     * <code>repeated int32 skillId1 = 9;</code>
     * @return The count of skillId1.
     */
    public int getSkillId1Count() {
      return skillId1_.size();
    }
    /**
     * <code>repeated int32 skillId1 = 9;</code>
     * @param index The index of the element to return.
     * @return The skillId1 at the given index.
     */
    public int getSkillId1(int index) {
      return skillId1_.getInt(index);
    }
    /**
     * <code>repeated int32 skillId1 = 9;</code>
     * @param index The index to set the value at.
     * @param value The skillId1 to set.
     * @return This builder for chaining.
     */
    public Builder setSkillId1(
        int index, int value) {

      ensureSkillId1IsMutable();
      skillId1_.setInt(index, value);
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 skillId1 = 9;</code>
     * @param value The skillId1 to add.
     * @return This builder for chaining.
     */
    public Builder addSkillId1(int value) {

      ensureSkillId1IsMutable();
      skillId1_.addInt(value);
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 skillId1 = 9;</code>
     * @param values The skillId1 to add.
     * @return This builder for chaining.
     */
    public Builder addAllSkillId1(
        java.lang.Iterable<? extends java.lang.Integer> values) {
      ensureSkillId1IsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, skillId1_);
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 skillId1 = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearSkillId1() {
      skillId1_ = emptyIntList();
      bitField0_ = (bitField0_ & ~0x00000100);
      onChanged();
      return this;
    }

    private com.google.protobuf.Internal.IntList skillId2_ = emptyIntList();
    private void ensureSkillId2IsMutable() {
      if (!skillId2_.isModifiable()) {
        skillId2_ = makeMutableCopy(skillId2_);
      }
      bitField0_ |= 0x00000200;
    }
    /**
     * <code>repeated int32 skillId2 = 10;</code>
     * @return A list containing the skillId2.
     */
    public java.util.List<java.lang.Integer>
        getSkillId2List() {
      skillId2_.makeImmutable();
      return skillId2_;
    }
    /**
     * <code>repeated int32 skillId2 = 10;</code>
     * @return The count of skillId2.
     */
    public int getSkillId2Count() {
      return skillId2_.size();
    }
    /**
     * <code>repeated int32 skillId2 = 10;</code>
     * @param index The index of the element to return.
     * @return The skillId2 at the given index.
     */
    public int getSkillId2(int index) {
      return skillId2_.getInt(index);
    }
    /**
     * <code>repeated int32 skillId2 = 10;</code>
     * @param index The index to set the value at.
     * @param value The skillId2 to set.
     * @return This builder for chaining.
     */
    public Builder setSkillId2(
        int index, int value) {

      ensureSkillId2IsMutable();
      skillId2_.setInt(index, value);
      bitField0_ |= 0x00000200;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 skillId2 = 10;</code>
     * @param value The skillId2 to add.
     * @return This builder for chaining.
     */
    public Builder addSkillId2(int value) {

      ensureSkillId2IsMutable();
      skillId2_.addInt(value);
      bitField0_ |= 0x00000200;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 skillId2 = 10;</code>
     * @param values The skillId2 to add.
     * @return This builder for chaining.
     */
    public Builder addAllSkillId2(
        java.lang.Iterable<? extends java.lang.Integer> values) {
      ensureSkillId2IsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, skillId2_);
      bitField0_ |= 0x00000200;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 skillId2 = 10;</code>
     * @return This builder for chaining.
     */
    public Builder clearSkillId2() {
      skillId2_ = emptyIntList();
      bitField0_ = (bitField0_ & ~0x00000200);
      onChanged();
      return this;
    }

    private com.google.protobuf.Internal.IntList cardPool_ = emptyIntList();
    private void ensureCardPoolIsMutable() {
      if (!cardPool_.isModifiable()) {
        cardPool_ = makeMutableCopy(cardPool_);
      }
      bitField0_ |= 0x00000400;
    }
    /**
     * <code>repeated int32 cardPool = 11;</code>
     * @return A list containing the cardPool.
     */
    public java.util.List<java.lang.Integer>
        getCardPoolList() {
      cardPool_.makeImmutable();
      return cardPool_;
    }
    /**
     * <code>repeated int32 cardPool = 11;</code>
     * @return The count of cardPool.
     */
    public int getCardPoolCount() {
      return cardPool_.size();
    }
    /**
     * <code>repeated int32 cardPool = 11;</code>
     * @param index The index of the element to return.
     * @return The cardPool at the given index.
     */
    public int getCardPool(int index) {
      return cardPool_.getInt(index);
    }
    /**
     * <code>repeated int32 cardPool = 11;</code>
     * @param index The index to set the value at.
     * @param value The cardPool to set.
     * @return This builder for chaining.
     */
    public Builder setCardPool(
        int index, int value) {

      ensureCardPoolIsMutable();
      cardPool_.setInt(index, value);
      bitField0_ |= 0x00000400;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 cardPool = 11;</code>
     * @param value The cardPool to add.
     * @return This builder for chaining.
     */
    public Builder addCardPool(int value) {

      ensureCardPoolIsMutable();
      cardPool_.addInt(value);
      bitField0_ |= 0x00000400;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 cardPool = 11;</code>
     * @param values The cardPool to add.
     * @return This builder for chaining.
     */
    public Builder addAllCardPool(
        java.lang.Iterable<? extends java.lang.Integer> values) {
      ensureCardPoolIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, cardPool_);
      bitField0_ |= 0x00000400;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 cardPool = 11;</code>
     * @return This builder for chaining.
     */
    public Builder clearCardPool() {
      cardPool_ = emptyIntList();
      bitField0_ = (bitField0_ & ~0x00000400);
      onChanged();
      return this;
    }

    private com.google.protobuf.Internal.IntList skillPool_ = emptyIntList();
    private void ensureSkillPoolIsMutable() {
      if (!skillPool_.isModifiable()) {
        skillPool_ = makeMutableCopy(skillPool_);
      }
      bitField0_ |= 0x00000800;
    }
    /**
     * <code>repeated int32 skillPool = 12;</code>
     * @return A list containing the skillPool.
     */
    public java.util.List<java.lang.Integer>
        getSkillPoolList() {
      skillPool_.makeImmutable();
      return skillPool_;
    }
    /**
     * <code>repeated int32 skillPool = 12;</code>
     * @return The count of skillPool.
     */
    public int getSkillPoolCount() {
      return skillPool_.size();
    }
    /**
     * <code>repeated int32 skillPool = 12;</code>
     * @param index The index of the element to return.
     * @return The skillPool at the given index.
     */
    public int getSkillPool(int index) {
      return skillPool_.getInt(index);
    }
    /**
     * <code>repeated int32 skillPool = 12;</code>
     * @param index The index to set the value at.
     * @param value The skillPool to set.
     * @return This builder for chaining.
     */
    public Builder setSkillPool(
        int index, int value) {

      ensureSkillPoolIsMutable();
      skillPool_.setInt(index, value);
      bitField0_ |= 0x00000800;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 skillPool = 12;</code>
     * @param value The skillPool to add.
     * @return This builder for chaining.
     */
    public Builder addSkillPool(int value) {

      ensureSkillPoolIsMutable();
      skillPool_.addInt(value);
      bitField0_ |= 0x00000800;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 skillPool = 12;</code>
     * @param values The skillPool to add.
     * @return This builder for chaining.
     */
    public Builder addAllSkillPool(
        java.lang.Iterable<? extends java.lang.Integer> values) {
      ensureSkillPoolIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, skillPool_);
      bitField0_ |= 0x00000800;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 skillPool = 12;</code>
     * @return This builder for chaining.
     */
    public Builder clearSkillPool() {
      skillPool_ = emptyIntList();
      bitField0_ = (bitField0_ & ~0x00000800);
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.ElementalBondsFriendRoomInfoMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.ElementalBondsFriendRoomInfoMsg)
  private static final xddq.pb.ElementalBondsFriendRoomInfoMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.ElementalBondsFriendRoomInfoMsg();
  }

  public static xddq.pb.ElementalBondsFriendRoomInfoMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ElementalBondsFriendRoomInfoMsg>
      PARSER = new com.google.protobuf.AbstractParser<ElementalBondsFriendRoomInfoMsg>() {
    @java.lang.Override
    public ElementalBondsFriendRoomInfoMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<ElementalBondsFriendRoomInfoMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ElementalBondsFriendRoomInfoMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.ElementalBondsFriendRoomInfoMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

