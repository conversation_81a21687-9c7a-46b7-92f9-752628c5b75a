// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.RoadDefendSelectBuffResp}
 */
public final class RoadDefendSelectBuffResp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.RoadDefendSelectBuffResp)
    RoadDefendSelectBuffRespOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      RoadDefendSelectBuffResp.class.getName());
  }
  // Use RoadDefendSelectBuffResp.newBuilder() to construct.
  private RoadDefendSelectBuffResp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private RoadDefendSelectBuffResp() {
    buffIdList_ = emptyIntList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_RoadDefendSelectBuffResp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_RoadDefendSelectBuffResp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.RoadDefendSelectBuffResp.class, xddq.pb.RoadDefendSelectBuffResp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int BUFFIDLIST_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private com.google.protobuf.Internal.IntList buffIdList_ =
      emptyIntList();
  /**
   * <code>repeated int32 buffIdList = 2;</code>
   * @return A list containing the buffIdList.
   */
  @java.lang.Override
  public java.util.List<java.lang.Integer>
      getBuffIdListList() {
    return buffIdList_;
  }
  /**
   * <code>repeated int32 buffIdList = 2;</code>
   * @return The count of buffIdList.
   */
  public int getBuffIdListCount() {
    return buffIdList_.size();
  }
  /**
   * <code>repeated int32 buffIdList = 2;</code>
   * @param index The index of the element to return.
   * @return The buffIdList at the given index.
   */
  public int getBuffIdList(int index) {
    return buffIdList_.getInt(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    for (int i = 0; i < buffIdList_.size(); i++) {
      output.writeInt32(2, buffIdList_.getInt(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    {
      int dataSize = 0;
      for (int i = 0; i < buffIdList_.size(); i++) {
        dataSize += com.google.protobuf.CodedOutputStream
          .computeInt32SizeNoTag(buffIdList_.getInt(i));
      }
      size += dataSize;
      size += 1 * getBuffIdListList().size();
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.RoadDefendSelectBuffResp)) {
      return super.equals(obj);
    }
    xddq.pb.RoadDefendSelectBuffResp other = (xddq.pb.RoadDefendSelectBuffResp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (!getBuffIdListList()
        .equals(other.getBuffIdListList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (getBuffIdListCount() > 0) {
      hash = (37 * hash) + BUFFIDLIST_FIELD_NUMBER;
      hash = (53 * hash) + getBuffIdListList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.RoadDefendSelectBuffResp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.RoadDefendSelectBuffResp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.RoadDefendSelectBuffResp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.RoadDefendSelectBuffResp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.RoadDefendSelectBuffResp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.RoadDefendSelectBuffResp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.RoadDefendSelectBuffResp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.RoadDefendSelectBuffResp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.RoadDefendSelectBuffResp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.RoadDefendSelectBuffResp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.RoadDefendSelectBuffResp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.RoadDefendSelectBuffResp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.RoadDefendSelectBuffResp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.RoadDefendSelectBuffResp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.RoadDefendSelectBuffResp)
      xddq.pb.RoadDefendSelectBuffRespOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_RoadDefendSelectBuffResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_RoadDefendSelectBuffResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.RoadDefendSelectBuffResp.class, xddq.pb.RoadDefendSelectBuffResp.Builder.class);
    }

    // Construct using xddq.pb.RoadDefendSelectBuffResp.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      buffIdList_ = emptyIntList();
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_RoadDefendSelectBuffResp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.RoadDefendSelectBuffResp getDefaultInstanceForType() {
      return xddq.pb.RoadDefendSelectBuffResp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.RoadDefendSelectBuffResp build() {
      xddq.pb.RoadDefendSelectBuffResp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.RoadDefendSelectBuffResp buildPartial() {
      xddq.pb.RoadDefendSelectBuffResp result = new xddq.pb.RoadDefendSelectBuffResp(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.RoadDefendSelectBuffResp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        buffIdList_.makeImmutable();
        result.buffIdList_ = buffIdList_;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.RoadDefendSelectBuffResp) {
        return mergeFrom((xddq.pb.RoadDefendSelectBuffResp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.RoadDefendSelectBuffResp other) {
      if (other == xddq.pb.RoadDefendSelectBuffResp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (!other.buffIdList_.isEmpty()) {
        if (buffIdList_.isEmpty()) {
          buffIdList_ = other.buffIdList_;
          buffIdList_.makeImmutable();
          bitField0_ |= 0x00000002;
        } else {
          ensureBuffIdListIsMutable();
          buffIdList_.addAll(other.buffIdList_);
        }
        onChanged();
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              int v = input.readInt32();
              ensureBuffIdListIsMutable();
              buffIdList_.addInt(v);
              break;
            } // case 16
            case 18: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              ensureBuffIdListIsMutable();
              while (input.getBytesUntilLimit() > 0) {
                buffIdList_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            } // case 18
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private com.google.protobuf.Internal.IntList buffIdList_ = emptyIntList();
    private void ensureBuffIdListIsMutable() {
      if (!buffIdList_.isModifiable()) {
        buffIdList_ = makeMutableCopy(buffIdList_);
      }
      bitField0_ |= 0x00000002;
    }
    /**
     * <code>repeated int32 buffIdList = 2;</code>
     * @return A list containing the buffIdList.
     */
    public java.util.List<java.lang.Integer>
        getBuffIdListList() {
      buffIdList_.makeImmutable();
      return buffIdList_;
    }
    /**
     * <code>repeated int32 buffIdList = 2;</code>
     * @return The count of buffIdList.
     */
    public int getBuffIdListCount() {
      return buffIdList_.size();
    }
    /**
     * <code>repeated int32 buffIdList = 2;</code>
     * @param index The index of the element to return.
     * @return The buffIdList at the given index.
     */
    public int getBuffIdList(int index) {
      return buffIdList_.getInt(index);
    }
    /**
     * <code>repeated int32 buffIdList = 2;</code>
     * @param index The index to set the value at.
     * @param value The buffIdList to set.
     * @return This builder for chaining.
     */
    public Builder setBuffIdList(
        int index, int value) {

      ensureBuffIdListIsMutable();
      buffIdList_.setInt(index, value);
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 buffIdList = 2;</code>
     * @param value The buffIdList to add.
     * @return This builder for chaining.
     */
    public Builder addBuffIdList(int value) {

      ensureBuffIdListIsMutable();
      buffIdList_.addInt(value);
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 buffIdList = 2;</code>
     * @param values The buffIdList to add.
     * @return This builder for chaining.
     */
    public Builder addAllBuffIdList(
        java.lang.Iterable<? extends java.lang.Integer> values) {
      ensureBuffIdListIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, buffIdList_);
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 buffIdList = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearBuffIdList() {
      buffIdList_ = emptyIntList();
      bitField0_ = (bitField0_ & ~0x00000002);
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.RoadDefendSelectBuffResp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.RoadDefendSelectBuffResp)
  private static final xddq.pb.RoadDefendSelectBuffResp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.RoadDefendSelectBuffResp();
  }

  public static xddq.pb.RoadDefendSelectBuffResp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<RoadDefendSelectBuffResp>
      PARSER = new com.google.protobuf.AbstractParser<RoadDefendSelectBuffResp>() {
    @java.lang.Override
    public RoadDefendSelectBuffResp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<RoadDefendSelectBuffResp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<RoadDefendSelectBuffResp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.RoadDefendSelectBuffResp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

