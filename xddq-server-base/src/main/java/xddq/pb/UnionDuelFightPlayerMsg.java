// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.UnionDuelFightPlayerMsg}
 */
public final class UnionDuelFightPlayerMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.UnionDuelFightPlayerMsg)
    UnionDuelFightPlayerMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      UnionDuelFightPlayerMsg.class.getName());
  }
  // Use UnionDuelFightPlayerMsg.newBuilder() to construct.
  private UnionDuelFightPlayerMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private UnionDuelFightPlayerMsg() {
    nickName_ = "";
    wxHeadUrl_ = "";
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_UnionDuelFightPlayerMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_UnionDuelFightPlayerMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.UnionDuelFightPlayerMsg.class, xddq.pb.UnionDuelFightPlayerMsg.Builder.class);
  }

  private int bitField0_;
  public static final int NICKNAME_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private volatile java.lang.Object nickName_ = "";
  /**
   * <code>optional string nickName = 1;</code>
   * @return Whether the nickName field is set.
   */
  @java.lang.Override
  public boolean hasNickName() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional string nickName = 1;</code>
   * @return The nickName.
   */
  @java.lang.Override
  public java.lang.String getNickName() {
    java.lang.Object ref = nickName_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        nickName_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string nickName = 1;</code>
   * @return The bytes for nickName.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getNickNameBytes() {
    java.lang.Object ref = nickName_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      nickName_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int HEADICON_FIELD_NUMBER = 2;
  private int headIcon_ = 0;
  /**
   * <code>optional int32 headIcon = 2;</code>
   * @return Whether the headIcon field is set.
   */
  @java.lang.Override
  public boolean hasHeadIcon() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int32 headIcon = 2;</code>
   * @return The headIcon.
   */
  @java.lang.Override
  public int getHeadIcon() {
    return headIcon_;
  }

  public static final int WXHEADURL_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private volatile java.lang.Object wxHeadUrl_ = "";
  /**
   * <code>optional string wxHeadUrl = 3;</code>
   * @return Whether the wxHeadUrl field is set.
   */
  @java.lang.Override
  public boolean hasWxHeadUrl() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional string wxHeadUrl = 3;</code>
   * @return The wxHeadUrl.
   */
  @java.lang.Override
  public java.lang.String getWxHeadUrl() {
    java.lang.Object ref = wxHeadUrl_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        wxHeadUrl_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string wxHeadUrl = 3;</code>
   * @return The bytes for wxHeadUrl.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getWxHeadUrlBytes() {
    java.lang.Object ref = wxHeadUrl_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      wxHeadUrl_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int EQUIPHEADICONFRAME_FIELD_NUMBER = 4;
  private int equipHeadIconFrame_ = 0;
  /**
   * <code>optional int32 equipHeadIconFrame = 4;</code>
   * @return Whether the equipHeadIconFrame field is set.
   */
  @java.lang.Override
  public boolean hasEquipHeadIconFrame() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional int32 equipHeadIconFrame = 4;</code>
   * @return The equipHeadIconFrame.
   */
  @java.lang.Override
  public int getEquipHeadIconFrame() {
    return equipHeadIconFrame_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 1, nickName_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(2, headIcon_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 3, wxHeadUrl_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt32(4, equipHeadIconFrame_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(1, nickName_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, headIcon_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(3, wxHeadUrl_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(4, equipHeadIconFrame_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.UnionDuelFightPlayerMsg)) {
      return super.equals(obj);
    }
    xddq.pb.UnionDuelFightPlayerMsg other = (xddq.pb.UnionDuelFightPlayerMsg) obj;

    if (hasNickName() != other.hasNickName()) return false;
    if (hasNickName()) {
      if (!getNickName()
          .equals(other.getNickName())) return false;
    }
    if (hasHeadIcon() != other.hasHeadIcon()) return false;
    if (hasHeadIcon()) {
      if (getHeadIcon()
          != other.getHeadIcon()) return false;
    }
    if (hasWxHeadUrl() != other.hasWxHeadUrl()) return false;
    if (hasWxHeadUrl()) {
      if (!getWxHeadUrl()
          .equals(other.getWxHeadUrl())) return false;
    }
    if (hasEquipHeadIconFrame() != other.hasEquipHeadIconFrame()) return false;
    if (hasEquipHeadIconFrame()) {
      if (getEquipHeadIconFrame()
          != other.getEquipHeadIconFrame()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasNickName()) {
      hash = (37 * hash) + NICKNAME_FIELD_NUMBER;
      hash = (53 * hash) + getNickName().hashCode();
    }
    if (hasHeadIcon()) {
      hash = (37 * hash) + HEADICON_FIELD_NUMBER;
      hash = (53 * hash) + getHeadIcon();
    }
    if (hasWxHeadUrl()) {
      hash = (37 * hash) + WXHEADURL_FIELD_NUMBER;
      hash = (53 * hash) + getWxHeadUrl().hashCode();
    }
    if (hasEquipHeadIconFrame()) {
      hash = (37 * hash) + EQUIPHEADICONFRAME_FIELD_NUMBER;
      hash = (53 * hash) + getEquipHeadIconFrame();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.UnionDuelFightPlayerMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.UnionDuelFightPlayerMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.UnionDuelFightPlayerMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.UnionDuelFightPlayerMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.UnionDuelFightPlayerMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.UnionDuelFightPlayerMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.UnionDuelFightPlayerMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.UnionDuelFightPlayerMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.UnionDuelFightPlayerMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.UnionDuelFightPlayerMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.UnionDuelFightPlayerMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.UnionDuelFightPlayerMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.UnionDuelFightPlayerMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.UnionDuelFightPlayerMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.UnionDuelFightPlayerMsg)
      xddq.pb.UnionDuelFightPlayerMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_UnionDuelFightPlayerMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_UnionDuelFightPlayerMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.UnionDuelFightPlayerMsg.class, xddq.pb.UnionDuelFightPlayerMsg.Builder.class);
    }

    // Construct using xddq.pb.UnionDuelFightPlayerMsg.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      nickName_ = "";
      headIcon_ = 0;
      wxHeadUrl_ = "";
      equipHeadIconFrame_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_UnionDuelFightPlayerMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.UnionDuelFightPlayerMsg getDefaultInstanceForType() {
      return xddq.pb.UnionDuelFightPlayerMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.UnionDuelFightPlayerMsg build() {
      xddq.pb.UnionDuelFightPlayerMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.UnionDuelFightPlayerMsg buildPartial() {
      xddq.pb.UnionDuelFightPlayerMsg result = new xddq.pb.UnionDuelFightPlayerMsg(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.UnionDuelFightPlayerMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.nickName_ = nickName_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.headIcon_ = headIcon_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.wxHeadUrl_ = wxHeadUrl_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.equipHeadIconFrame_ = equipHeadIconFrame_;
        to_bitField0_ |= 0x00000008;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.UnionDuelFightPlayerMsg) {
        return mergeFrom((xddq.pb.UnionDuelFightPlayerMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.UnionDuelFightPlayerMsg other) {
      if (other == xddq.pb.UnionDuelFightPlayerMsg.getDefaultInstance()) return this;
      if (other.hasNickName()) {
        nickName_ = other.nickName_;
        bitField0_ |= 0x00000001;
        onChanged();
      }
      if (other.hasHeadIcon()) {
        setHeadIcon(other.getHeadIcon());
      }
      if (other.hasWxHeadUrl()) {
        wxHeadUrl_ = other.wxHeadUrl_;
        bitField0_ |= 0x00000004;
        onChanged();
      }
      if (other.hasEquipHeadIconFrame()) {
        setEquipHeadIconFrame(other.getEquipHeadIconFrame());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              nickName_ = input.readBytes();
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 16: {
              headIcon_ = input.readInt32();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 26: {
              wxHeadUrl_ = input.readBytes();
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 32: {
              equipHeadIconFrame_ = input.readInt32();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.lang.Object nickName_ = "";
    /**
     * <code>optional string nickName = 1;</code>
     * @return Whether the nickName field is set.
     */
    public boolean hasNickName() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional string nickName = 1;</code>
     * @return The nickName.
     */
    public java.lang.String getNickName() {
      java.lang.Object ref = nickName_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          nickName_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string nickName = 1;</code>
     * @return The bytes for nickName.
     */
    public com.google.protobuf.ByteString
        getNickNameBytes() {
      java.lang.Object ref = nickName_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        nickName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string nickName = 1;</code>
     * @param value The nickName to set.
     * @return This builder for chaining.
     */
    public Builder setNickName(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      nickName_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional string nickName = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearNickName() {
      nickName_ = getDefaultInstance().getNickName();
      bitField0_ = (bitField0_ & ~0x00000001);
      onChanged();
      return this;
    }
    /**
     * <code>optional string nickName = 1;</code>
     * @param value The bytes for nickName to set.
     * @return This builder for chaining.
     */
    public Builder setNickNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      nickName_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }

    private int headIcon_ ;
    /**
     * <code>optional int32 headIcon = 2;</code>
     * @return Whether the headIcon field is set.
     */
    @java.lang.Override
    public boolean hasHeadIcon() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 headIcon = 2;</code>
     * @return The headIcon.
     */
    @java.lang.Override
    public int getHeadIcon() {
      return headIcon_;
    }
    /**
     * <code>optional int32 headIcon = 2;</code>
     * @param value The headIcon to set.
     * @return This builder for chaining.
     */
    public Builder setHeadIcon(int value) {

      headIcon_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 headIcon = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearHeadIcon() {
      bitField0_ = (bitField0_ & ~0x00000002);
      headIcon_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object wxHeadUrl_ = "";
    /**
     * <code>optional string wxHeadUrl = 3;</code>
     * @return Whether the wxHeadUrl field is set.
     */
    public boolean hasWxHeadUrl() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional string wxHeadUrl = 3;</code>
     * @return The wxHeadUrl.
     */
    public java.lang.String getWxHeadUrl() {
      java.lang.Object ref = wxHeadUrl_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          wxHeadUrl_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string wxHeadUrl = 3;</code>
     * @return The bytes for wxHeadUrl.
     */
    public com.google.protobuf.ByteString
        getWxHeadUrlBytes() {
      java.lang.Object ref = wxHeadUrl_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        wxHeadUrl_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string wxHeadUrl = 3;</code>
     * @param value The wxHeadUrl to set.
     * @return This builder for chaining.
     */
    public Builder setWxHeadUrl(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      wxHeadUrl_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional string wxHeadUrl = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearWxHeadUrl() {
      wxHeadUrl_ = getDefaultInstance().getWxHeadUrl();
      bitField0_ = (bitField0_ & ~0x00000004);
      onChanged();
      return this;
    }
    /**
     * <code>optional string wxHeadUrl = 3;</code>
     * @param value The bytes for wxHeadUrl to set.
     * @return This builder for chaining.
     */
    public Builder setWxHeadUrlBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      wxHeadUrl_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }

    private int equipHeadIconFrame_ ;
    /**
     * <code>optional int32 equipHeadIconFrame = 4;</code>
     * @return Whether the equipHeadIconFrame field is set.
     */
    @java.lang.Override
    public boolean hasEquipHeadIconFrame() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int32 equipHeadIconFrame = 4;</code>
     * @return The equipHeadIconFrame.
     */
    @java.lang.Override
    public int getEquipHeadIconFrame() {
      return equipHeadIconFrame_;
    }
    /**
     * <code>optional int32 equipHeadIconFrame = 4;</code>
     * @param value The equipHeadIconFrame to set.
     * @return This builder for chaining.
     */
    public Builder setEquipHeadIconFrame(int value) {

      equipHeadIconFrame_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 equipHeadIconFrame = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearEquipHeadIconFrame() {
      bitField0_ = (bitField0_ & ~0x00000008);
      equipHeadIconFrame_ = 0;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.UnionDuelFightPlayerMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.UnionDuelFightPlayerMsg)
  private static final xddq.pb.UnionDuelFightPlayerMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.UnionDuelFightPlayerMsg();
  }

  public static xddq.pb.UnionDuelFightPlayerMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<UnionDuelFightPlayerMsg>
      PARSER = new com.google.protobuf.AbstractParser<UnionDuelFightPlayerMsg>() {
    @java.lang.Override
    public UnionDuelFightPlayerMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<UnionDuelFightPlayerMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<UnionDuelFightPlayerMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.UnionDuelFightPlayerMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

