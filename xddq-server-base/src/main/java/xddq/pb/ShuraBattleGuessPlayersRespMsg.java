// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.ShuraBattleGuessPlayersRespMsg}
 */
public final class ShuraBattleGuessPlayersRespMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.ShuraBattleGuessPlayersRespMsg)
    ShuraBattleGuessPlayersRespMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      <PERSON>raBattleGuessPlayersRespMsg.class.getName());
  }
  // Use ShuraBattleGuessPlayersRespMsg.newBuilder() to construct.
  private ShuraBattleGuessPlayersRespMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private ShuraBattleGuessPlayersRespMsg() {
    playerList_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_ShuraBattleGuessPlayersRespMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_ShuraBattleGuessPlayersRespMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.ShuraBattleGuessPlayersRespMsg.class, xddq.pb.ShuraBattleGuessPlayersRespMsg.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int PARAM_FIELD_NUMBER = 2;
  private int param_ = 0;
  /**
   * <code>optional int32 param = 2;</code>
   * @return Whether the param field is set.
   */
  @java.lang.Override
  public boolean hasParam() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int32 param = 2;</code>
   * @return The param.
   */
  @java.lang.Override
  public int getParam() {
    return param_;
  }

  public static final int PLAYERLIST_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.ShuraBattleGuessPlayerInfo> playerList_;
  /**
   * <code>repeated .xddq.pb.ShuraBattleGuessPlayerInfo playerList = 3;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.ShuraBattleGuessPlayerInfo> getPlayerListList() {
    return playerList_;
  }
  /**
   * <code>repeated .xddq.pb.ShuraBattleGuessPlayerInfo playerList = 3;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.ShuraBattleGuessPlayerInfoOrBuilder> 
      getPlayerListOrBuilderList() {
    return playerList_;
  }
  /**
   * <code>repeated .xddq.pb.ShuraBattleGuessPlayerInfo playerList = 3;</code>
   */
  @java.lang.Override
  public int getPlayerListCount() {
    return playerList_.size();
  }
  /**
   * <code>repeated .xddq.pb.ShuraBattleGuessPlayerInfo playerList = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.ShuraBattleGuessPlayerInfo getPlayerList(int index) {
    return playerList_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.ShuraBattleGuessPlayerInfo playerList = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.ShuraBattleGuessPlayerInfoOrBuilder getPlayerListOrBuilder(
      int index) {
    return playerList_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(2, param_);
    }
    for (int i = 0; i < playerList_.size(); i++) {
      output.writeMessage(3, playerList_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, param_);
    }
    for (int i = 0; i < playerList_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, playerList_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.ShuraBattleGuessPlayersRespMsg)) {
      return super.equals(obj);
    }
    xddq.pb.ShuraBattleGuessPlayersRespMsg other = (xddq.pb.ShuraBattleGuessPlayersRespMsg) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (hasParam() != other.hasParam()) return false;
    if (hasParam()) {
      if (getParam()
          != other.getParam()) return false;
    }
    if (!getPlayerListList()
        .equals(other.getPlayerListList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (hasParam()) {
      hash = (37 * hash) + PARAM_FIELD_NUMBER;
      hash = (53 * hash) + getParam();
    }
    if (getPlayerListCount() > 0) {
      hash = (37 * hash) + PLAYERLIST_FIELD_NUMBER;
      hash = (53 * hash) + getPlayerListList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.ShuraBattleGuessPlayersRespMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ShuraBattleGuessPlayersRespMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ShuraBattleGuessPlayersRespMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ShuraBattleGuessPlayersRespMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ShuraBattleGuessPlayersRespMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ShuraBattleGuessPlayersRespMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ShuraBattleGuessPlayersRespMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.ShuraBattleGuessPlayersRespMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.ShuraBattleGuessPlayersRespMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.ShuraBattleGuessPlayersRespMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.ShuraBattleGuessPlayersRespMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.ShuraBattleGuessPlayersRespMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.ShuraBattleGuessPlayersRespMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.ShuraBattleGuessPlayersRespMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.ShuraBattleGuessPlayersRespMsg)
      xddq.pb.ShuraBattleGuessPlayersRespMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ShuraBattleGuessPlayersRespMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ShuraBattleGuessPlayersRespMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.ShuraBattleGuessPlayersRespMsg.class, xddq.pb.ShuraBattleGuessPlayersRespMsg.Builder.class);
    }

    // Construct using xddq.pb.ShuraBattleGuessPlayersRespMsg.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      param_ = 0;
      if (playerListBuilder_ == null) {
        playerList_ = java.util.Collections.emptyList();
      } else {
        playerList_ = null;
        playerListBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000004);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ShuraBattleGuessPlayersRespMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.ShuraBattleGuessPlayersRespMsg getDefaultInstanceForType() {
      return xddq.pb.ShuraBattleGuessPlayersRespMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.ShuraBattleGuessPlayersRespMsg build() {
      xddq.pb.ShuraBattleGuessPlayersRespMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.ShuraBattleGuessPlayersRespMsg buildPartial() {
      xddq.pb.ShuraBattleGuessPlayersRespMsg result = new xddq.pb.ShuraBattleGuessPlayersRespMsg(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.ShuraBattleGuessPlayersRespMsg result) {
      if (playerListBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0)) {
          playerList_ = java.util.Collections.unmodifiableList(playerList_);
          bitField0_ = (bitField0_ & ~0x00000004);
        }
        result.playerList_ = playerList_;
      } else {
        result.playerList_ = playerListBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.ShuraBattleGuessPlayersRespMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.param_ = param_;
        to_bitField0_ |= 0x00000002;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.ShuraBattleGuessPlayersRespMsg) {
        return mergeFrom((xddq.pb.ShuraBattleGuessPlayersRespMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.ShuraBattleGuessPlayersRespMsg other) {
      if (other == xddq.pb.ShuraBattleGuessPlayersRespMsg.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (other.hasParam()) {
        setParam(other.getParam());
      }
      if (playerListBuilder_ == null) {
        if (!other.playerList_.isEmpty()) {
          if (playerList_.isEmpty()) {
            playerList_ = other.playerList_;
            bitField0_ = (bitField0_ & ~0x00000004);
          } else {
            ensurePlayerListIsMutable();
            playerList_.addAll(other.playerList_);
          }
          onChanged();
        }
      } else {
        if (!other.playerList_.isEmpty()) {
          if (playerListBuilder_.isEmpty()) {
            playerListBuilder_.dispose();
            playerListBuilder_ = null;
            playerList_ = other.playerList_;
            bitField0_ = (bitField0_ & ~0x00000004);
            playerListBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetPlayerListFieldBuilder() : null;
          } else {
            playerListBuilder_.addAllMessages(other.playerList_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              param_ = input.readInt32();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 26: {
              xddq.pb.ShuraBattleGuessPlayerInfo m =
                  input.readMessage(
                      xddq.pb.ShuraBattleGuessPlayerInfo.parser(),
                      extensionRegistry);
              if (playerListBuilder_ == null) {
                ensurePlayerListIsMutable();
                playerList_.add(m);
              } else {
                playerListBuilder_.addMessage(m);
              }
              break;
            } // case 26
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private int param_ ;
    /**
     * <code>optional int32 param = 2;</code>
     * @return Whether the param field is set.
     */
    @java.lang.Override
    public boolean hasParam() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 param = 2;</code>
     * @return The param.
     */
    @java.lang.Override
    public int getParam() {
      return param_;
    }
    /**
     * <code>optional int32 param = 2;</code>
     * @param value The param to set.
     * @return This builder for chaining.
     */
    public Builder setParam(int value) {

      param_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 param = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearParam() {
      bitField0_ = (bitField0_ & ~0x00000002);
      param_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.ShuraBattleGuessPlayerInfo> playerList_ =
      java.util.Collections.emptyList();
    private void ensurePlayerListIsMutable() {
      if (!((bitField0_ & 0x00000004) != 0)) {
        playerList_ = new java.util.ArrayList<xddq.pb.ShuraBattleGuessPlayerInfo>(playerList_);
        bitField0_ |= 0x00000004;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.ShuraBattleGuessPlayerInfo, xddq.pb.ShuraBattleGuessPlayerInfo.Builder, xddq.pb.ShuraBattleGuessPlayerInfoOrBuilder> playerListBuilder_;

    /**
     * <code>repeated .xddq.pb.ShuraBattleGuessPlayerInfo playerList = 3;</code>
     */
    public java.util.List<xddq.pb.ShuraBattleGuessPlayerInfo> getPlayerListList() {
      if (playerListBuilder_ == null) {
        return java.util.Collections.unmodifiableList(playerList_);
      } else {
        return playerListBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.ShuraBattleGuessPlayerInfo playerList = 3;</code>
     */
    public int getPlayerListCount() {
      if (playerListBuilder_ == null) {
        return playerList_.size();
      } else {
        return playerListBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.ShuraBattleGuessPlayerInfo playerList = 3;</code>
     */
    public xddq.pb.ShuraBattleGuessPlayerInfo getPlayerList(int index) {
      if (playerListBuilder_ == null) {
        return playerList_.get(index);
      } else {
        return playerListBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.ShuraBattleGuessPlayerInfo playerList = 3;</code>
     */
    public Builder setPlayerList(
        int index, xddq.pb.ShuraBattleGuessPlayerInfo value) {
      if (playerListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePlayerListIsMutable();
        playerList_.set(index, value);
        onChanged();
      } else {
        playerListBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ShuraBattleGuessPlayerInfo playerList = 3;</code>
     */
    public Builder setPlayerList(
        int index, xddq.pb.ShuraBattleGuessPlayerInfo.Builder builderForValue) {
      if (playerListBuilder_ == null) {
        ensurePlayerListIsMutable();
        playerList_.set(index, builderForValue.build());
        onChanged();
      } else {
        playerListBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ShuraBattleGuessPlayerInfo playerList = 3;</code>
     */
    public Builder addPlayerList(xddq.pb.ShuraBattleGuessPlayerInfo value) {
      if (playerListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePlayerListIsMutable();
        playerList_.add(value);
        onChanged();
      } else {
        playerListBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ShuraBattleGuessPlayerInfo playerList = 3;</code>
     */
    public Builder addPlayerList(
        int index, xddq.pb.ShuraBattleGuessPlayerInfo value) {
      if (playerListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePlayerListIsMutable();
        playerList_.add(index, value);
        onChanged();
      } else {
        playerListBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ShuraBattleGuessPlayerInfo playerList = 3;</code>
     */
    public Builder addPlayerList(
        xddq.pb.ShuraBattleGuessPlayerInfo.Builder builderForValue) {
      if (playerListBuilder_ == null) {
        ensurePlayerListIsMutable();
        playerList_.add(builderForValue.build());
        onChanged();
      } else {
        playerListBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ShuraBattleGuessPlayerInfo playerList = 3;</code>
     */
    public Builder addPlayerList(
        int index, xddq.pb.ShuraBattleGuessPlayerInfo.Builder builderForValue) {
      if (playerListBuilder_ == null) {
        ensurePlayerListIsMutable();
        playerList_.add(index, builderForValue.build());
        onChanged();
      } else {
        playerListBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ShuraBattleGuessPlayerInfo playerList = 3;</code>
     */
    public Builder addAllPlayerList(
        java.lang.Iterable<? extends xddq.pb.ShuraBattleGuessPlayerInfo> values) {
      if (playerListBuilder_ == null) {
        ensurePlayerListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, playerList_);
        onChanged();
      } else {
        playerListBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ShuraBattleGuessPlayerInfo playerList = 3;</code>
     */
    public Builder clearPlayerList() {
      if (playerListBuilder_ == null) {
        playerList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
      } else {
        playerListBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ShuraBattleGuessPlayerInfo playerList = 3;</code>
     */
    public Builder removePlayerList(int index) {
      if (playerListBuilder_ == null) {
        ensurePlayerListIsMutable();
        playerList_.remove(index);
        onChanged();
      } else {
        playerListBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ShuraBattleGuessPlayerInfo playerList = 3;</code>
     */
    public xddq.pb.ShuraBattleGuessPlayerInfo.Builder getPlayerListBuilder(
        int index) {
      return internalGetPlayerListFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.ShuraBattleGuessPlayerInfo playerList = 3;</code>
     */
    public xddq.pb.ShuraBattleGuessPlayerInfoOrBuilder getPlayerListOrBuilder(
        int index) {
      if (playerListBuilder_ == null) {
        return playerList_.get(index);  } else {
        return playerListBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.ShuraBattleGuessPlayerInfo playerList = 3;</code>
     */
    public java.util.List<? extends xddq.pb.ShuraBattleGuessPlayerInfoOrBuilder> 
         getPlayerListOrBuilderList() {
      if (playerListBuilder_ != null) {
        return playerListBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(playerList_);
      }
    }
    /**
     * <code>repeated .xddq.pb.ShuraBattleGuessPlayerInfo playerList = 3;</code>
     */
    public xddq.pb.ShuraBattleGuessPlayerInfo.Builder addPlayerListBuilder() {
      return internalGetPlayerListFieldBuilder().addBuilder(
          xddq.pb.ShuraBattleGuessPlayerInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.ShuraBattleGuessPlayerInfo playerList = 3;</code>
     */
    public xddq.pb.ShuraBattleGuessPlayerInfo.Builder addPlayerListBuilder(
        int index) {
      return internalGetPlayerListFieldBuilder().addBuilder(
          index, xddq.pb.ShuraBattleGuessPlayerInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.ShuraBattleGuessPlayerInfo playerList = 3;</code>
     */
    public java.util.List<xddq.pb.ShuraBattleGuessPlayerInfo.Builder> 
         getPlayerListBuilderList() {
      return internalGetPlayerListFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.ShuraBattleGuessPlayerInfo, xddq.pb.ShuraBattleGuessPlayerInfo.Builder, xddq.pb.ShuraBattleGuessPlayerInfoOrBuilder> 
        internalGetPlayerListFieldBuilder() {
      if (playerListBuilder_ == null) {
        playerListBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.ShuraBattleGuessPlayerInfo, xddq.pb.ShuraBattleGuessPlayerInfo.Builder, xddq.pb.ShuraBattleGuessPlayerInfoOrBuilder>(
                playerList_,
                ((bitField0_ & 0x00000004) != 0),
                getParentForChildren(),
                isClean());
        playerList_ = null;
      }
      return playerListBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.ShuraBattleGuessPlayersRespMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.ShuraBattleGuessPlayersRespMsg)
  private static final xddq.pb.ShuraBattleGuessPlayersRespMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.ShuraBattleGuessPlayersRespMsg();
  }

  public static xddq.pb.ShuraBattleGuessPlayersRespMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ShuraBattleGuessPlayersRespMsg>
      PARSER = new com.google.protobuf.AbstractParser<ShuraBattleGuessPlayersRespMsg>() {
    @java.lang.Override
    public ShuraBattleGuessPlayersRespMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<ShuraBattleGuessPlayersRespMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ShuraBattleGuessPlayersRespMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.ShuraBattleGuessPlayersRespMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

