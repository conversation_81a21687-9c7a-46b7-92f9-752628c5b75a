// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.CloudSoulCombineActivationResp}
 */
public final class CloudSoulCombineActivationResp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.CloudSoulCombineActivationResp)
    CloudSoulCombineActivationRespOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      CloudSoulCombineActivationResp.class.getName());
  }
  // Use CloudSoulCombineActivationResp.newBuilder() to construct.
  private CloudSoulCombineActivationResp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private CloudSoulCombineActivationResp() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_CloudSoulCombineActivationResp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_CloudSoulCombineActivationResp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.CloudSoulCombineActivationResp.class, xddq.pb.CloudSoulCombineActivationResp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int CLOUDSOULCOMBINEMSG_FIELD_NUMBER = 2;
  private xddq.pb.CloudSoulCombineMsg cloudSoulCombineMsg_;
  /**
   * <code>optional .xddq.pb.CloudSoulCombineMsg cloudSoulCombineMsg = 2;</code>
   * @return Whether the cloudSoulCombineMsg field is set.
   */
  @java.lang.Override
  public boolean hasCloudSoulCombineMsg() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional .xddq.pb.CloudSoulCombineMsg cloudSoulCombineMsg = 2;</code>
   * @return The cloudSoulCombineMsg.
   */
  @java.lang.Override
  public xddq.pb.CloudSoulCombineMsg getCloudSoulCombineMsg() {
    return cloudSoulCombineMsg_ == null ? xddq.pb.CloudSoulCombineMsg.getDefaultInstance() : cloudSoulCombineMsg_;
  }
  /**
   * <code>optional .xddq.pb.CloudSoulCombineMsg cloudSoulCombineMsg = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.CloudSoulCombineMsgOrBuilder getCloudSoulCombineMsgOrBuilder() {
    return cloudSoulCombineMsg_ == null ? xddq.pb.CloudSoulCombineMsg.getDefaultInstance() : cloudSoulCombineMsg_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (hasCloudSoulCombineMsg()) {
      if (!getCloudSoulCombineMsg().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeMessage(2, getCloudSoulCombineMsg());
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getCloudSoulCombineMsg());
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.CloudSoulCombineActivationResp)) {
      return super.equals(obj);
    }
    xddq.pb.CloudSoulCombineActivationResp other = (xddq.pb.CloudSoulCombineActivationResp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (hasCloudSoulCombineMsg() != other.hasCloudSoulCombineMsg()) return false;
    if (hasCloudSoulCombineMsg()) {
      if (!getCloudSoulCombineMsg()
          .equals(other.getCloudSoulCombineMsg())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (hasCloudSoulCombineMsg()) {
      hash = (37 * hash) + CLOUDSOULCOMBINEMSG_FIELD_NUMBER;
      hash = (53 * hash) + getCloudSoulCombineMsg().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.CloudSoulCombineActivationResp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.CloudSoulCombineActivationResp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.CloudSoulCombineActivationResp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.CloudSoulCombineActivationResp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.CloudSoulCombineActivationResp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.CloudSoulCombineActivationResp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.CloudSoulCombineActivationResp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.CloudSoulCombineActivationResp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.CloudSoulCombineActivationResp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.CloudSoulCombineActivationResp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.CloudSoulCombineActivationResp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.CloudSoulCombineActivationResp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.CloudSoulCombineActivationResp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.CloudSoulCombineActivationResp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.CloudSoulCombineActivationResp)
      xddq.pb.CloudSoulCombineActivationRespOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_CloudSoulCombineActivationResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_CloudSoulCombineActivationResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.CloudSoulCombineActivationResp.class, xddq.pb.CloudSoulCombineActivationResp.Builder.class);
    }

    // Construct using xddq.pb.CloudSoulCombineActivationResp.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetCloudSoulCombineMsgFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      cloudSoulCombineMsg_ = null;
      if (cloudSoulCombineMsgBuilder_ != null) {
        cloudSoulCombineMsgBuilder_.dispose();
        cloudSoulCombineMsgBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_CloudSoulCombineActivationResp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.CloudSoulCombineActivationResp getDefaultInstanceForType() {
      return xddq.pb.CloudSoulCombineActivationResp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.CloudSoulCombineActivationResp build() {
      xddq.pb.CloudSoulCombineActivationResp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.CloudSoulCombineActivationResp buildPartial() {
      xddq.pb.CloudSoulCombineActivationResp result = new xddq.pb.CloudSoulCombineActivationResp(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.CloudSoulCombineActivationResp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.cloudSoulCombineMsg_ = cloudSoulCombineMsgBuilder_ == null
            ? cloudSoulCombineMsg_
            : cloudSoulCombineMsgBuilder_.build();
        to_bitField0_ |= 0x00000002;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.CloudSoulCombineActivationResp) {
        return mergeFrom((xddq.pb.CloudSoulCombineActivationResp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.CloudSoulCombineActivationResp other) {
      if (other == xddq.pb.CloudSoulCombineActivationResp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (other.hasCloudSoulCombineMsg()) {
        mergeCloudSoulCombineMsg(other.getCloudSoulCombineMsg());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      if (hasCloudSoulCombineMsg()) {
        if (!getCloudSoulCombineMsg().isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              input.readMessage(
                  internalGetCloudSoulCombineMsgFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private xddq.pb.CloudSoulCombineMsg cloudSoulCombineMsg_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.CloudSoulCombineMsg, xddq.pb.CloudSoulCombineMsg.Builder, xddq.pb.CloudSoulCombineMsgOrBuilder> cloudSoulCombineMsgBuilder_;
    /**
     * <code>optional .xddq.pb.CloudSoulCombineMsg cloudSoulCombineMsg = 2;</code>
     * @return Whether the cloudSoulCombineMsg field is set.
     */
    public boolean hasCloudSoulCombineMsg() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .xddq.pb.CloudSoulCombineMsg cloudSoulCombineMsg = 2;</code>
     * @return The cloudSoulCombineMsg.
     */
    public xddq.pb.CloudSoulCombineMsg getCloudSoulCombineMsg() {
      if (cloudSoulCombineMsgBuilder_ == null) {
        return cloudSoulCombineMsg_ == null ? xddq.pb.CloudSoulCombineMsg.getDefaultInstance() : cloudSoulCombineMsg_;
      } else {
        return cloudSoulCombineMsgBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.CloudSoulCombineMsg cloudSoulCombineMsg = 2;</code>
     */
    public Builder setCloudSoulCombineMsg(xddq.pb.CloudSoulCombineMsg value) {
      if (cloudSoulCombineMsgBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        cloudSoulCombineMsg_ = value;
      } else {
        cloudSoulCombineMsgBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.CloudSoulCombineMsg cloudSoulCombineMsg = 2;</code>
     */
    public Builder setCloudSoulCombineMsg(
        xddq.pb.CloudSoulCombineMsg.Builder builderForValue) {
      if (cloudSoulCombineMsgBuilder_ == null) {
        cloudSoulCombineMsg_ = builderForValue.build();
      } else {
        cloudSoulCombineMsgBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.CloudSoulCombineMsg cloudSoulCombineMsg = 2;</code>
     */
    public Builder mergeCloudSoulCombineMsg(xddq.pb.CloudSoulCombineMsg value) {
      if (cloudSoulCombineMsgBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0) &&
          cloudSoulCombineMsg_ != null &&
          cloudSoulCombineMsg_ != xddq.pb.CloudSoulCombineMsg.getDefaultInstance()) {
          getCloudSoulCombineMsgBuilder().mergeFrom(value);
        } else {
          cloudSoulCombineMsg_ = value;
        }
      } else {
        cloudSoulCombineMsgBuilder_.mergeFrom(value);
      }
      if (cloudSoulCombineMsg_ != null) {
        bitField0_ |= 0x00000002;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.CloudSoulCombineMsg cloudSoulCombineMsg = 2;</code>
     */
    public Builder clearCloudSoulCombineMsg() {
      bitField0_ = (bitField0_ & ~0x00000002);
      cloudSoulCombineMsg_ = null;
      if (cloudSoulCombineMsgBuilder_ != null) {
        cloudSoulCombineMsgBuilder_.dispose();
        cloudSoulCombineMsgBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.CloudSoulCombineMsg cloudSoulCombineMsg = 2;</code>
     */
    public xddq.pb.CloudSoulCombineMsg.Builder getCloudSoulCombineMsgBuilder() {
      bitField0_ |= 0x00000002;
      onChanged();
      return internalGetCloudSoulCombineMsgFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.CloudSoulCombineMsg cloudSoulCombineMsg = 2;</code>
     */
    public xddq.pb.CloudSoulCombineMsgOrBuilder getCloudSoulCombineMsgOrBuilder() {
      if (cloudSoulCombineMsgBuilder_ != null) {
        return cloudSoulCombineMsgBuilder_.getMessageOrBuilder();
      } else {
        return cloudSoulCombineMsg_ == null ?
            xddq.pb.CloudSoulCombineMsg.getDefaultInstance() : cloudSoulCombineMsg_;
      }
    }
    /**
     * <code>optional .xddq.pb.CloudSoulCombineMsg cloudSoulCombineMsg = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.CloudSoulCombineMsg, xddq.pb.CloudSoulCombineMsg.Builder, xddq.pb.CloudSoulCombineMsgOrBuilder> 
        internalGetCloudSoulCombineMsgFieldBuilder() {
      if (cloudSoulCombineMsgBuilder_ == null) {
        cloudSoulCombineMsgBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.CloudSoulCombineMsg, xddq.pb.CloudSoulCombineMsg.Builder, xddq.pb.CloudSoulCombineMsgOrBuilder>(
                getCloudSoulCombineMsg(),
                getParentForChildren(),
                isClean());
        cloudSoulCombineMsg_ = null;
      }
      return cloudSoulCombineMsgBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.CloudSoulCombineActivationResp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.CloudSoulCombineActivationResp)
  private static final xddq.pb.CloudSoulCombineActivationResp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.CloudSoulCombineActivationResp();
  }

  public static xddq.pb.CloudSoulCombineActivationResp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<CloudSoulCombineActivationResp>
      PARSER = new com.google.protobuf.AbstractParser<CloudSoulCombineActivationResp>() {
    @java.lang.Override
    public CloudSoulCombineActivationResp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<CloudSoulCombineActivationResp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<CloudSoulCombineActivationResp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.CloudSoulCombineActivationResp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

