// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

public interface AskDingGameUnionMsgOrBuilder extends
    // @@protoc_insertion_point(interface_extends:xddq.pb.AskDingGameUnionMsg)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>optional .xddq.pb.UnionBaseData unionData = 1;</code>
   * @return Whether the unionData field is set.
   */
  boolean hasUnionData();
  /**
   * <code>optional .xddq.pb.UnionBaseData unionData = 1;</code>
   * @return The unionData.
   */
  xddq.pb.UnionBaseData getUnionData();
  /**
   * <code>optional .xddq.pb.UnionBaseData unionData = 1;</code>
   */
  xddq.pb.UnionBaseDataOrBuilder getUnionDataOrBuilder();

  /**
   * <code>optional int64 rank = 2;</code>
   * @return Whether the rank field is set.
   */
  boolean hasRank();
  /**
   * <code>optional int64 rank = 2;</code>
   * @return The rank.
   */
  long getRank();

  /**
   * <code>optional string power = 3;</code>
   * @return Whether the power field is set.
   */
  boolean hasPower();
  /**
   * <code>optional string power = 3;</code>
   * @return The power.
   */
  java.lang.String getPower();
  /**
   * <code>optional string power = 3;</code>
   * @return The bytes for power.
   */
  com.google.protobuf.ByteString
      getPowerBytes();

  /**
   * <code>optional int64 betHot = 4;</code>
   * @return Whether the betHot field is set.
   */
  boolean hasBetHot();
  /**
   * <code>optional int64 betHot = 4;</code>
   * @return The betHot.
   */
  long getBetHot();

  /**
   * <code>optional int32 birthPlaceId = 5;</code>
   * @return Whether the birthPlaceId field is set.
   */
  boolean hasBirthPlaceId();
  /**
   * <code>optional int32 birthPlaceId = 5;</code>
   * @return The birthPlaceId.
   */
  int getBirthPlaceId();
}
