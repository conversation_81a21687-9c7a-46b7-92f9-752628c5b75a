// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.GoldSnakeBuffConfigMsg}
 */
public final class GoldSnakeBuffConfigMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.GoldSnakeBuffConfigMsg)
    GoldSnakeBuffConfigMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      GoldSnakeBuffConfigMsg.class.getName());
  }
  // Use GoldSnakeBuffConfigMsg.newBuilder() to construct.
  private GoldSnakeBuffConfigMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private GoldSnakeBuffConfigMsg() {
    param_ = "";
    paramDesc_ = "";
    desc_ = "";
    variable_ = "";
    name_ = "";
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_GoldSnakeBuffConfigMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_GoldSnakeBuffConfigMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.GoldSnakeBuffConfigMsg.class, xddq.pb.GoldSnakeBuffConfigMsg.Builder.class);
  }

  private int bitField0_;
  public static final int ID_FIELD_NUMBER = 1;
  private int id_ = 0;
  /**
   * <code>optional int32 id = 1;</code>
   * @return Whether the id field is set.
   */
  @java.lang.Override
  public boolean hasId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int32 id = 1;</code>
   * @return The id.
   */
  @java.lang.Override
  public int getId() {
    return id_;
  }

  public static final int ICON_FIELD_NUMBER = 2;
  private int icon_ = 0;
  /**
   * <code>optional int32 icon = 2;</code>
   * @return Whether the icon field is set.
   */
  @java.lang.Override
  public boolean hasIcon() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int32 icon = 2;</code>
   * @return The icon.
   */
  @java.lang.Override
  public int getIcon() {
    return icon_;
  }

  public static final int TYPE_FIELD_NUMBER = 3;
  private int type_ = 0;
  /**
   * <code>optional int32 type = 3;</code>
   * @return Whether the type field is set.
   */
  @java.lang.Override
  public boolean hasType() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional int32 type = 3;</code>
   * @return The type.
   */
  @java.lang.Override
  public int getType() {
    return type_;
  }

  public static final int QUALITY_FIELD_NUMBER = 4;
  private int quality_ = 0;
  /**
   * <code>optional int32 quality = 4;</code>
   * @return Whether the quality field is set.
   */
  @java.lang.Override
  public boolean hasQuality() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional int32 quality = 4;</code>
   * @return The quality.
   */
  @java.lang.Override
  public int getQuality() {
    return quality_;
  }

  public static final int WEIGHT_FIELD_NUMBER = 5;
  private int weight_ = 0;
  /**
   * <code>optional int32 weight = 5;</code>
   * @return Whether the weight field is set.
   */
  @java.lang.Override
  public boolean hasWeight() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>optional int32 weight = 5;</code>
   * @return The weight.
   */
  @java.lang.Override
  public int getWeight() {
    return weight_;
  }

  public static final int LIMIT_FIELD_NUMBER = 6;
  private int limit_ = 0;
  /**
   * <code>optional int32 limit = 6;</code>
   * @return Whether the limit field is set.
   */
  @java.lang.Override
  public boolean hasLimit() {
    return ((bitField0_ & 0x00000020) != 0);
  }
  /**
   * <code>optional int32 limit = 6;</code>
   * @return The limit.
   */
  @java.lang.Override
  public int getLimit() {
    return limit_;
  }

  public static final int STAR_FIELD_NUMBER = 7;
  private int star_ = 0;
  /**
   * <code>optional int32 star = 7;</code>
   * @return Whether the star field is set.
   */
  @java.lang.Override
  public boolean hasStar() {
    return ((bitField0_ & 0x00000040) != 0);
  }
  /**
   * <code>optional int32 star = 7;</code>
   * @return The star.
   */
  @java.lang.Override
  public int getStar() {
    return star_;
  }

  public static final int FRONT_FIELD_NUMBER = 8;
  private int front_ = 0;
  /**
   * <code>optional int32 front = 8;</code>
   * @return Whether the front field is set.
   */
  @java.lang.Override
  public boolean hasFront() {
    return ((bitField0_ & 0x00000080) != 0);
  }
  /**
   * <code>optional int32 front = 8;</code>
   * @return The front.
   */
  @java.lang.Override
  public int getFront() {
    return front_;
  }

  public static final int GOURD_FIELD_NUMBER = 9;
  private int gourd_ = 0;
  /**
   * <code>optional int32 gourd = 9;</code>
   * @return Whether the gourd field is set.
   */
  @java.lang.Override
  public boolean hasGourd() {
    return ((bitField0_ & 0x00000100) != 0);
  }
  /**
   * <code>optional int32 gourd = 9;</code>
   * @return The gourd.
   */
  @java.lang.Override
  public int getGourd() {
    return gourd_;
  }

  public static final int PARAM_FIELD_NUMBER = 10;
  @SuppressWarnings("serial")
  private volatile java.lang.Object param_ = "";
  /**
   * <code>optional string param = 10;</code>
   * @return Whether the param field is set.
   */
  @java.lang.Override
  public boolean hasParam() {
    return ((bitField0_ & 0x00000200) != 0);
  }
  /**
   * <code>optional string param = 10;</code>
   * @return The param.
   */
  @java.lang.Override
  public java.lang.String getParam() {
    java.lang.Object ref = param_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        param_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string param = 10;</code>
   * @return The bytes for param.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getParamBytes() {
    java.lang.Object ref = param_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      param_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PARAMDESC_FIELD_NUMBER = 11;
  @SuppressWarnings("serial")
  private volatile java.lang.Object paramDesc_ = "";
  /**
   * <code>optional string paramDesc = 11;</code>
   * @return Whether the paramDesc field is set.
   */
  @java.lang.Override
  public boolean hasParamDesc() {
    return ((bitField0_ & 0x00000400) != 0);
  }
  /**
   * <code>optional string paramDesc = 11;</code>
   * @return The paramDesc.
   */
  @java.lang.Override
  public java.lang.String getParamDesc() {
    java.lang.Object ref = paramDesc_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        paramDesc_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string paramDesc = 11;</code>
   * @return The bytes for paramDesc.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getParamDescBytes() {
    java.lang.Object ref = paramDesc_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      paramDesc_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int DESC_FIELD_NUMBER = 12;
  @SuppressWarnings("serial")
  private volatile java.lang.Object desc_ = "";
  /**
   * <code>optional string desc = 12;</code>
   * @return Whether the desc field is set.
   */
  @java.lang.Override
  public boolean hasDesc() {
    return ((bitField0_ & 0x00000800) != 0);
  }
  /**
   * <code>optional string desc = 12;</code>
   * @return The desc.
   */
  @java.lang.Override
  public java.lang.String getDesc() {
    java.lang.Object ref = desc_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        desc_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string desc = 12;</code>
   * @return The bytes for desc.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getDescBytes() {
    java.lang.Object ref = desc_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      desc_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int LITTLETYPE_FIELD_NUMBER = 13;
  private int littleType_ = 0;
  /**
   * <code>optional int32 littleType = 13;</code>
   * @return Whether the littleType field is set.
   */
  @java.lang.Override
  public boolean hasLittleType() {
    return ((bitField0_ & 0x00001000) != 0);
  }
  /**
   * <code>optional int32 littleType = 13;</code>
   * @return The littleType.
   */
  @java.lang.Override
  public int getLittleType() {
    return littleType_;
  }

  public static final int VARIABLE_FIELD_NUMBER = 14;
  @SuppressWarnings("serial")
  private volatile java.lang.Object variable_ = "";
  /**
   * <code>optional string variable = 14;</code>
   * @return Whether the variable field is set.
   */
  @java.lang.Override
  public boolean hasVariable() {
    return ((bitField0_ & 0x00002000) != 0);
  }
  /**
   * <code>optional string variable = 14;</code>
   * @return The variable.
   */
  @java.lang.Override
  public java.lang.String getVariable() {
    java.lang.Object ref = variable_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        variable_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string variable = 14;</code>
   * @return The bytes for variable.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getVariableBytes() {
    java.lang.Object ref = variable_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      variable_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int NAME_FIELD_NUMBER = 15;
  @SuppressWarnings("serial")
  private volatile java.lang.Object name_ = "";
  /**
   * <code>optional string name = 15;</code>
   * @return Whether the name field is set.
   */
  @java.lang.Override
  public boolean hasName() {
    return ((bitField0_ & 0x00004000) != 0);
  }
  /**
   * <code>optional string name = 15;</code>
   * @return The name.
   */
  @java.lang.Override
  public java.lang.String getName() {
    java.lang.Object ref = name_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        name_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string name = 15;</code>
   * @return The bytes for name.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getNameBytes() {
    java.lang.Object ref = name_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      name_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, id_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(2, icon_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt32(3, type_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt32(4, quality_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      output.writeInt32(5, weight_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      output.writeInt32(6, limit_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      output.writeInt32(7, star_);
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      output.writeInt32(8, front_);
    }
    if (((bitField0_ & 0x00000100) != 0)) {
      output.writeInt32(9, gourd_);
    }
    if (((bitField0_ & 0x00000200) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 10, param_);
    }
    if (((bitField0_ & 0x00000400) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 11, paramDesc_);
    }
    if (((bitField0_ & 0x00000800) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 12, desc_);
    }
    if (((bitField0_ & 0x00001000) != 0)) {
      output.writeInt32(13, littleType_);
    }
    if (((bitField0_ & 0x00002000) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 14, variable_);
    }
    if (((bitField0_ & 0x00004000) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 15, name_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, id_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, icon_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(3, type_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(4, quality_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(5, weight_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(6, limit_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(7, star_);
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(8, front_);
    }
    if (((bitField0_ & 0x00000100) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(9, gourd_);
    }
    if (((bitField0_ & 0x00000200) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(10, param_);
    }
    if (((bitField0_ & 0x00000400) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(11, paramDesc_);
    }
    if (((bitField0_ & 0x00000800) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(12, desc_);
    }
    if (((bitField0_ & 0x00001000) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(13, littleType_);
    }
    if (((bitField0_ & 0x00002000) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(14, variable_);
    }
    if (((bitField0_ & 0x00004000) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(15, name_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.GoldSnakeBuffConfigMsg)) {
      return super.equals(obj);
    }
    xddq.pb.GoldSnakeBuffConfigMsg other = (xddq.pb.GoldSnakeBuffConfigMsg) obj;

    if (hasId() != other.hasId()) return false;
    if (hasId()) {
      if (getId()
          != other.getId()) return false;
    }
    if (hasIcon() != other.hasIcon()) return false;
    if (hasIcon()) {
      if (getIcon()
          != other.getIcon()) return false;
    }
    if (hasType() != other.hasType()) return false;
    if (hasType()) {
      if (getType()
          != other.getType()) return false;
    }
    if (hasQuality() != other.hasQuality()) return false;
    if (hasQuality()) {
      if (getQuality()
          != other.getQuality()) return false;
    }
    if (hasWeight() != other.hasWeight()) return false;
    if (hasWeight()) {
      if (getWeight()
          != other.getWeight()) return false;
    }
    if (hasLimit() != other.hasLimit()) return false;
    if (hasLimit()) {
      if (getLimit()
          != other.getLimit()) return false;
    }
    if (hasStar() != other.hasStar()) return false;
    if (hasStar()) {
      if (getStar()
          != other.getStar()) return false;
    }
    if (hasFront() != other.hasFront()) return false;
    if (hasFront()) {
      if (getFront()
          != other.getFront()) return false;
    }
    if (hasGourd() != other.hasGourd()) return false;
    if (hasGourd()) {
      if (getGourd()
          != other.getGourd()) return false;
    }
    if (hasParam() != other.hasParam()) return false;
    if (hasParam()) {
      if (!getParam()
          .equals(other.getParam())) return false;
    }
    if (hasParamDesc() != other.hasParamDesc()) return false;
    if (hasParamDesc()) {
      if (!getParamDesc()
          .equals(other.getParamDesc())) return false;
    }
    if (hasDesc() != other.hasDesc()) return false;
    if (hasDesc()) {
      if (!getDesc()
          .equals(other.getDesc())) return false;
    }
    if (hasLittleType() != other.hasLittleType()) return false;
    if (hasLittleType()) {
      if (getLittleType()
          != other.getLittleType()) return false;
    }
    if (hasVariable() != other.hasVariable()) return false;
    if (hasVariable()) {
      if (!getVariable()
          .equals(other.getVariable())) return false;
    }
    if (hasName() != other.hasName()) return false;
    if (hasName()) {
      if (!getName()
          .equals(other.getName())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasId()) {
      hash = (37 * hash) + ID_FIELD_NUMBER;
      hash = (53 * hash) + getId();
    }
    if (hasIcon()) {
      hash = (37 * hash) + ICON_FIELD_NUMBER;
      hash = (53 * hash) + getIcon();
    }
    if (hasType()) {
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
    }
    if (hasQuality()) {
      hash = (37 * hash) + QUALITY_FIELD_NUMBER;
      hash = (53 * hash) + getQuality();
    }
    if (hasWeight()) {
      hash = (37 * hash) + WEIGHT_FIELD_NUMBER;
      hash = (53 * hash) + getWeight();
    }
    if (hasLimit()) {
      hash = (37 * hash) + LIMIT_FIELD_NUMBER;
      hash = (53 * hash) + getLimit();
    }
    if (hasStar()) {
      hash = (37 * hash) + STAR_FIELD_NUMBER;
      hash = (53 * hash) + getStar();
    }
    if (hasFront()) {
      hash = (37 * hash) + FRONT_FIELD_NUMBER;
      hash = (53 * hash) + getFront();
    }
    if (hasGourd()) {
      hash = (37 * hash) + GOURD_FIELD_NUMBER;
      hash = (53 * hash) + getGourd();
    }
    if (hasParam()) {
      hash = (37 * hash) + PARAM_FIELD_NUMBER;
      hash = (53 * hash) + getParam().hashCode();
    }
    if (hasParamDesc()) {
      hash = (37 * hash) + PARAMDESC_FIELD_NUMBER;
      hash = (53 * hash) + getParamDesc().hashCode();
    }
    if (hasDesc()) {
      hash = (37 * hash) + DESC_FIELD_NUMBER;
      hash = (53 * hash) + getDesc().hashCode();
    }
    if (hasLittleType()) {
      hash = (37 * hash) + LITTLETYPE_FIELD_NUMBER;
      hash = (53 * hash) + getLittleType();
    }
    if (hasVariable()) {
      hash = (37 * hash) + VARIABLE_FIELD_NUMBER;
      hash = (53 * hash) + getVariable().hashCode();
    }
    if (hasName()) {
      hash = (37 * hash) + NAME_FIELD_NUMBER;
      hash = (53 * hash) + getName().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.GoldSnakeBuffConfigMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GoldSnakeBuffConfigMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GoldSnakeBuffConfigMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GoldSnakeBuffConfigMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GoldSnakeBuffConfigMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GoldSnakeBuffConfigMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GoldSnakeBuffConfigMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.GoldSnakeBuffConfigMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.GoldSnakeBuffConfigMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.GoldSnakeBuffConfigMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.GoldSnakeBuffConfigMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.GoldSnakeBuffConfigMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.GoldSnakeBuffConfigMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.GoldSnakeBuffConfigMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.GoldSnakeBuffConfigMsg)
      xddq.pb.GoldSnakeBuffConfigMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GoldSnakeBuffConfigMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GoldSnakeBuffConfigMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.GoldSnakeBuffConfigMsg.class, xddq.pb.GoldSnakeBuffConfigMsg.Builder.class);
    }

    // Construct using xddq.pb.GoldSnakeBuffConfigMsg.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      id_ = 0;
      icon_ = 0;
      type_ = 0;
      quality_ = 0;
      weight_ = 0;
      limit_ = 0;
      star_ = 0;
      front_ = 0;
      gourd_ = 0;
      param_ = "";
      paramDesc_ = "";
      desc_ = "";
      littleType_ = 0;
      variable_ = "";
      name_ = "";
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GoldSnakeBuffConfigMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.GoldSnakeBuffConfigMsg getDefaultInstanceForType() {
      return xddq.pb.GoldSnakeBuffConfigMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.GoldSnakeBuffConfigMsg build() {
      xddq.pb.GoldSnakeBuffConfigMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.GoldSnakeBuffConfigMsg buildPartial() {
      xddq.pb.GoldSnakeBuffConfigMsg result = new xddq.pb.GoldSnakeBuffConfigMsg(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.GoldSnakeBuffConfigMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.id_ = id_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.icon_ = icon_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.type_ = type_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.quality_ = quality_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.weight_ = weight_;
        to_bitField0_ |= 0x00000010;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.limit_ = limit_;
        to_bitField0_ |= 0x00000020;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.star_ = star_;
        to_bitField0_ |= 0x00000040;
      }
      if (((from_bitField0_ & 0x00000080) != 0)) {
        result.front_ = front_;
        to_bitField0_ |= 0x00000080;
      }
      if (((from_bitField0_ & 0x00000100) != 0)) {
        result.gourd_ = gourd_;
        to_bitField0_ |= 0x00000100;
      }
      if (((from_bitField0_ & 0x00000200) != 0)) {
        result.param_ = param_;
        to_bitField0_ |= 0x00000200;
      }
      if (((from_bitField0_ & 0x00000400) != 0)) {
        result.paramDesc_ = paramDesc_;
        to_bitField0_ |= 0x00000400;
      }
      if (((from_bitField0_ & 0x00000800) != 0)) {
        result.desc_ = desc_;
        to_bitField0_ |= 0x00000800;
      }
      if (((from_bitField0_ & 0x00001000) != 0)) {
        result.littleType_ = littleType_;
        to_bitField0_ |= 0x00001000;
      }
      if (((from_bitField0_ & 0x00002000) != 0)) {
        result.variable_ = variable_;
        to_bitField0_ |= 0x00002000;
      }
      if (((from_bitField0_ & 0x00004000) != 0)) {
        result.name_ = name_;
        to_bitField0_ |= 0x00004000;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.GoldSnakeBuffConfigMsg) {
        return mergeFrom((xddq.pb.GoldSnakeBuffConfigMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.GoldSnakeBuffConfigMsg other) {
      if (other == xddq.pb.GoldSnakeBuffConfigMsg.getDefaultInstance()) return this;
      if (other.hasId()) {
        setId(other.getId());
      }
      if (other.hasIcon()) {
        setIcon(other.getIcon());
      }
      if (other.hasType()) {
        setType(other.getType());
      }
      if (other.hasQuality()) {
        setQuality(other.getQuality());
      }
      if (other.hasWeight()) {
        setWeight(other.getWeight());
      }
      if (other.hasLimit()) {
        setLimit(other.getLimit());
      }
      if (other.hasStar()) {
        setStar(other.getStar());
      }
      if (other.hasFront()) {
        setFront(other.getFront());
      }
      if (other.hasGourd()) {
        setGourd(other.getGourd());
      }
      if (other.hasParam()) {
        param_ = other.param_;
        bitField0_ |= 0x00000200;
        onChanged();
      }
      if (other.hasParamDesc()) {
        paramDesc_ = other.paramDesc_;
        bitField0_ |= 0x00000400;
        onChanged();
      }
      if (other.hasDesc()) {
        desc_ = other.desc_;
        bitField0_ |= 0x00000800;
        onChanged();
      }
      if (other.hasLittleType()) {
        setLittleType(other.getLittleType());
      }
      if (other.hasVariable()) {
        variable_ = other.variable_;
        bitField0_ |= 0x00002000;
        onChanged();
      }
      if (other.hasName()) {
        name_ = other.name_;
        bitField0_ |= 0x00004000;
        onChanged();
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              id_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              icon_ = input.readInt32();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 24: {
              type_ = input.readInt32();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 32: {
              quality_ = input.readInt32();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            case 40: {
              weight_ = input.readInt32();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            case 48: {
              limit_ = input.readInt32();
              bitField0_ |= 0x00000020;
              break;
            } // case 48
            case 56: {
              star_ = input.readInt32();
              bitField0_ |= 0x00000040;
              break;
            } // case 56
            case 64: {
              front_ = input.readInt32();
              bitField0_ |= 0x00000080;
              break;
            } // case 64
            case 72: {
              gourd_ = input.readInt32();
              bitField0_ |= 0x00000100;
              break;
            } // case 72
            case 82: {
              param_ = input.readBytes();
              bitField0_ |= 0x00000200;
              break;
            } // case 82
            case 90: {
              paramDesc_ = input.readBytes();
              bitField0_ |= 0x00000400;
              break;
            } // case 90
            case 98: {
              desc_ = input.readBytes();
              bitField0_ |= 0x00000800;
              break;
            } // case 98
            case 104: {
              littleType_ = input.readInt32();
              bitField0_ |= 0x00001000;
              break;
            } // case 104
            case 114: {
              variable_ = input.readBytes();
              bitField0_ |= 0x00002000;
              break;
            } // case 114
            case 122: {
              name_ = input.readBytes();
              bitField0_ |= 0x00004000;
              break;
            } // case 122
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int id_ ;
    /**
     * <code>optional int32 id = 1;</code>
     * @return Whether the id field is set.
     */
    @java.lang.Override
    public boolean hasId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 id = 1;</code>
     * @return The id.
     */
    @java.lang.Override
    public int getId() {
      return id_;
    }
    /**
     * <code>optional int32 id = 1;</code>
     * @param value The id to set.
     * @return This builder for chaining.
     */
    public Builder setId(int value) {

      id_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 id = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      id_ = 0;
      onChanged();
      return this;
    }

    private int icon_ ;
    /**
     * <code>optional int32 icon = 2;</code>
     * @return Whether the icon field is set.
     */
    @java.lang.Override
    public boolean hasIcon() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 icon = 2;</code>
     * @return The icon.
     */
    @java.lang.Override
    public int getIcon() {
      return icon_;
    }
    /**
     * <code>optional int32 icon = 2;</code>
     * @param value The icon to set.
     * @return This builder for chaining.
     */
    public Builder setIcon(int value) {

      icon_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 icon = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearIcon() {
      bitField0_ = (bitField0_ & ~0x00000002);
      icon_ = 0;
      onChanged();
      return this;
    }

    private int type_ ;
    /**
     * <code>optional int32 type = 3;</code>
     * @return Whether the type field is set.
     */
    @java.lang.Override
    public boolean hasType() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int32 type = 3;</code>
     * @return The type.
     */
    @java.lang.Override
    public int getType() {
      return type_;
    }
    /**
     * <code>optional int32 type = 3;</code>
     * @param value The type to set.
     * @return This builder for chaining.
     */
    public Builder setType(int value) {

      type_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 type = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearType() {
      bitField0_ = (bitField0_ & ~0x00000004);
      type_ = 0;
      onChanged();
      return this;
    }

    private int quality_ ;
    /**
     * <code>optional int32 quality = 4;</code>
     * @return Whether the quality field is set.
     */
    @java.lang.Override
    public boolean hasQuality() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int32 quality = 4;</code>
     * @return The quality.
     */
    @java.lang.Override
    public int getQuality() {
      return quality_;
    }
    /**
     * <code>optional int32 quality = 4;</code>
     * @param value The quality to set.
     * @return This builder for chaining.
     */
    public Builder setQuality(int value) {

      quality_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 quality = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearQuality() {
      bitField0_ = (bitField0_ & ~0x00000008);
      quality_ = 0;
      onChanged();
      return this;
    }

    private int weight_ ;
    /**
     * <code>optional int32 weight = 5;</code>
     * @return Whether the weight field is set.
     */
    @java.lang.Override
    public boolean hasWeight() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional int32 weight = 5;</code>
     * @return The weight.
     */
    @java.lang.Override
    public int getWeight() {
      return weight_;
    }
    /**
     * <code>optional int32 weight = 5;</code>
     * @param value The weight to set.
     * @return This builder for chaining.
     */
    public Builder setWeight(int value) {

      weight_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 weight = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearWeight() {
      bitField0_ = (bitField0_ & ~0x00000010);
      weight_ = 0;
      onChanged();
      return this;
    }

    private int limit_ ;
    /**
     * <code>optional int32 limit = 6;</code>
     * @return Whether the limit field is set.
     */
    @java.lang.Override
    public boolean hasLimit() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional int32 limit = 6;</code>
     * @return The limit.
     */
    @java.lang.Override
    public int getLimit() {
      return limit_;
    }
    /**
     * <code>optional int32 limit = 6;</code>
     * @param value The limit to set.
     * @return This builder for chaining.
     */
    public Builder setLimit(int value) {

      limit_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 limit = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearLimit() {
      bitField0_ = (bitField0_ & ~0x00000020);
      limit_ = 0;
      onChanged();
      return this;
    }

    private int star_ ;
    /**
     * <code>optional int32 star = 7;</code>
     * @return Whether the star field is set.
     */
    @java.lang.Override
    public boolean hasStar() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <code>optional int32 star = 7;</code>
     * @return The star.
     */
    @java.lang.Override
    public int getStar() {
      return star_;
    }
    /**
     * <code>optional int32 star = 7;</code>
     * @param value The star to set.
     * @return This builder for chaining.
     */
    public Builder setStar(int value) {

      star_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 star = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearStar() {
      bitField0_ = (bitField0_ & ~0x00000040);
      star_ = 0;
      onChanged();
      return this;
    }

    private int front_ ;
    /**
     * <code>optional int32 front = 8;</code>
     * @return Whether the front field is set.
     */
    @java.lang.Override
    public boolean hasFront() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <code>optional int32 front = 8;</code>
     * @return The front.
     */
    @java.lang.Override
    public int getFront() {
      return front_;
    }
    /**
     * <code>optional int32 front = 8;</code>
     * @param value The front to set.
     * @return This builder for chaining.
     */
    public Builder setFront(int value) {

      front_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 front = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearFront() {
      bitField0_ = (bitField0_ & ~0x00000080);
      front_ = 0;
      onChanged();
      return this;
    }

    private int gourd_ ;
    /**
     * <code>optional int32 gourd = 9;</code>
     * @return Whether the gourd field is set.
     */
    @java.lang.Override
    public boolean hasGourd() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <code>optional int32 gourd = 9;</code>
     * @return The gourd.
     */
    @java.lang.Override
    public int getGourd() {
      return gourd_;
    }
    /**
     * <code>optional int32 gourd = 9;</code>
     * @param value The gourd to set.
     * @return This builder for chaining.
     */
    public Builder setGourd(int value) {

      gourd_ = value;
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 gourd = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearGourd() {
      bitField0_ = (bitField0_ & ~0x00000100);
      gourd_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object param_ = "";
    /**
     * <code>optional string param = 10;</code>
     * @return Whether the param field is set.
     */
    public boolean hasParam() {
      return ((bitField0_ & 0x00000200) != 0);
    }
    /**
     * <code>optional string param = 10;</code>
     * @return The param.
     */
    public java.lang.String getParam() {
      java.lang.Object ref = param_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          param_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string param = 10;</code>
     * @return The bytes for param.
     */
    public com.google.protobuf.ByteString
        getParamBytes() {
      java.lang.Object ref = param_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        param_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string param = 10;</code>
     * @param value The param to set.
     * @return This builder for chaining.
     */
    public Builder setParam(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      param_ = value;
      bitField0_ |= 0x00000200;
      onChanged();
      return this;
    }
    /**
     * <code>optional string param = 10;</code>
     * @return This builder for chaining.
     */
    public Builder clearParam() {
      param_ = getDefaultInstance().getParam();
      bitField0_ = (bitField0_ & ~0x00000200);
      onChanged();
      return this;
    }
    /**
     * <code>optional string param = 10;</code>
     * @param value The bytes for param to set.
     * @return This builder for chaining.
     */
    public Builder setParamBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      param_ = value;
      bitField0_ |= 0x00000200;
      onChanged();
      return this;
    }

    private java.lang.Object paramDesc_ = "";
    /**
     * <code>optional string paramDesc = 11;</code>
     * @return Whether the paramDesc field is set.
     */
    public boolean hasParamDesc() {
      return ((bitField0_ & 0x00000400) != 0);
    }
    /**
     * <code>optional string paramDesc = 11;</code>
     * @return The paramDesc.
     */
    public java.lang.String getParamDesc() {
      java.lang.Object ref = paramDesc_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          paramDesc_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string paramDesc = 11;</code>
     * @return The bytes for paramDesc.
     */
    public com.google.protobuf.ByteString
        getParamDescBytes() {
      java.lang.Object ref = paramDesc_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        paramDesc_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string paramDesc = 11;</code>
     * @param value The paramDesc to set.
     * @return This builder for chaining.
     */
    public Builder setParamDesc(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      paramDesc_ = value;
      bitField0_ |= 0x00000400;
      onChanged();
      return this;
    }
    /**
     * <code>optional string paramDesc = 11;</code>
     * @return This builder for chaining.
     */
    public Builder clearParamDesc() {
      paramDesc_ = getDefaultInstance().getParamDesc();
      bitField0_ = (bitField0_ & ~0x00000400);
      onChanged();
      return this;
    }
    /**
     * <code>optional string paramDesc = 11;</code>
     * @param value The bytes for paramDesc to set.
     * @return This builder for chaining.
     */
    public Builder setParamDescBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      paramDesc_ = value;
      bitField0_ |= 0x00000400;
      onChanged();
      return this;
    }

    private java.lang.Object desc_ = "";
    /**
     * <code>optional string desc = 12;</code>
     * @return Whether the desc field is set.
     */
    public boolean hasDesc() {
      return ((bitField0_ & 0x00000800) != 0);
    }
    /**
     * <code>optional string desc = 12;</code>
     * @return The desc.
     */
    public java.lang.String getDesc() {
      java.lang.Object ref = desc_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          desc_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string desc = 12;</code>
     * @return The bytes for desc.
     */
    public com.google.protobuf.ByteString
        getDescBytes() {
      java.lang.Object ref = desc_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        desc_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string desc = 12;</code>
     * @param value The desc to set.
     * @return This builder for chaining.
     */
    public Builder setDesc(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      desc_ = value;
      bitField0_ |= 0x00000800;
      onChanged();
      return this;
    }
    /**
     * <code>optional string desc = 12;</code>
     * @return This builder for chaining.
     */
    public Builder clearDesc() {
      desc_ = getDefaultInstance().getDesc();
      bitField0_ = (bitField0_ & ~0x00000800);
      onChanged();
      return this;
    }
    /**
     * <code>optional string desc = 12;</code>
     * @param value The bytes for desc to set.
     * @return This builder for chaining.
     */
    public Builder setDescBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      desc_ = value;
      bitField0_ |= 0x00000800;
      onChanged();
      return this;
    }

    private int littleType_ ;
    /**
     * <code>optional int32 littleType = 13;</code>
     * @return Whether the littleType field is set.
     */
    @java.lang.Override
    public boolean hasLittleType() {
      return ((bitField0_ & 0x00001000) != 0);
    }
    /**
     * <code>optional int32 littleType = 13;</code>
     * @return The littleType.
     */
    @java.lang.Override
    public int getLittleType() {
      return littleType_;
    }
    /**
     * <code>optional int32 littleType = 13;</code>
     * @param value The littleType to set.
     * @return This builder for chaining.
     */
    public Builder setLittleType(int value) {

      littleType_ = value;
      bitField0_ |= 0x00001000;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 littleType = 13;</code>
     * @return This builder for chaining.
     */
    public Builder clearLittleType() {
      bitField0_ = (bitField0_ & ~0x00001000);
      littleType_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object variable_ = "";
    /**
     * <code>optional string variable = 14;</code>
     * @return Whether the variable field is set.
     */
    public boolean hasVariable() {
      return ((bitField0_ & 0x00002000) != 0);
    }
    /**
     * <code>optional string variable = 14;</code>
     * @return The variable.
     */
    public java.lang.String getVariable() {
      java.lang.Object ref = variable_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          variable_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string variable = 14;</code>
     * @return The bytes for variable.
     */
    public com.google.protobuf.ByteString
        getVariableBytes() {
      java.lang.Object ref = variable_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        variable_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string variable = 14;</code>
     * @param value The variable to set.
     * @return This builder for chaining.
     */
    public Builder setVariable(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      variable_ = value;
      bitField0_ |= 0x00002000;
      onChanged();
      return this;
    }
    /**
     * <code>optional string variable = 14;</code>
     * @return This builder for chaining.
     */
    public Builder clearVariable() {
      variable_ = getDefaultInstance().getVariable();
      bitField0_ = (bitField0_ & ~0x00002000);
      onChanged();
      return this;
    }
    /**
     * <code>optional string variable = 14;</code>
     * @param value The bytes for variable to set.
     * @return This builder for chaining.
     */
    public Builder setVariableBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      variable_ = value;
      bitField0_ |= 0x00002000;
      onChanged();
      return this;
    }

    private java.lang.Object name_ = "";
    /**
     * <code>optional string name = 15;</code>
     * @return Whether the name field is set.
     */
    public boolean hasName() {
      return ((bitField0_ & 0x00004000) != 0);
    }
    /**
     * <code>optional string name = 15;</code>
     * @return The name.
     */
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          name_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string name = 15;</code>
     * @return The bytes for name.
     */
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string name = 15;</code>
     * @param value The name to set.
     * @return This builder for chaining.
     */
    public Builder setName(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      name_ = value;
      bitField0_ |= 0x00004000;
      onChanged();
      return this;
    }
    /**
     * <code>optional string name = 15;</code>
     * @return This builder for chaining.
     */
    public Builder clearName() {
      name_ = getDefaultInstance().getName();
      bitField0_ = (bitField0_ & ~0x00004000);
      onChanged();
      return this;
    }
    /**
     * <code>optional string name = 15;</code>
     * @param value The bytes for name to set.
     * @return This builder for chaining.
     */
    public Builder setNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      name_ = value;
      bitField0_ |= 0x00004000;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.GoldSnakeBuffConfigMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.GoldSnakeBuffConfigMsg)
  private static final xddq.pb.GoldSnakeBuffConfigMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.GoldSnakeBuffConfigMsg();
  }

  public static xddq.pb.GoldSnakeBuffConfigMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<GoldSnakeBuffConfigMsg>
      PARSER = new com.google.protobuf.AbstractParser<GoldSnakeBuffConfigMsg>() {
    @java.lang.Override
    public GoldSnakeBuffConfigMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<GoldSnakeBuffConfigMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<GoldSnakeBuffConfigMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.GoldSnakeBuffConfigMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

