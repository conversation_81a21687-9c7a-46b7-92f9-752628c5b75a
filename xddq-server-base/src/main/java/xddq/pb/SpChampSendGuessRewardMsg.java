// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.SpChampSendGuessRewardMsg}
 */
public final class SpChampSendGuessRewardMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.SpChampSendGuessRewardMsg)
    SpChampSendGuessRewardMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      SpChampSendGuessRewardMsg.class.getName());
  }
  // Use SpChampSendGuessRewardMsg.newBuilder() to construct.
  private SpChampSendGuessRewardMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private SpChampSendGuessRewardMsg() {
    reward_ = "";
    guessResult_ = java.util.Collections.emptyList();
    finalGuessResult_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_SpChampSendGuessRewardMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_SpChampSendGuessRewardMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.SpChampSendGuessRewardMsg.class, xddq.pb.SpChampSendGuessRewardMsg.Builder.class);
  }

  private int bitField0_;
  public static final int ACTIVITYID_FIELD_NUMBER = 1;
  private int activityId_ = 0;
  /**
   * <code>optional int32 activityId = 1;</code>
   * @return Whether the activityId field is set.
   */
  @java.lang.Override
  public boolean hasActivityId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int32 activityId = 1;</code>
   * @return The activityId.
   */
  @java.lang.Override
  public int getActivityId() {
    return activityId_;
  }

  public static final int REWARD_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private volatile java.lang.Object reward_ = "";
  /**
   * <code>optional string reward = 2;</code>
   * @return Whether the reward field is set.
   */
  @java.lang.Override
  public boolean hasReward() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional string reward = 2;</code>
   * @return The reward.
   */
  @java.lang.Override
  public java.lang.String getReward() {
    java.lang.Object ref = reward_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        reward_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string reward = 2;</code>
   * @return The bytes for reward.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getRewardBytes() {
    java.lang.Object ref = reward_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      reward_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int GUESSRESULT_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.SpChampPlayerGuessInfoMsg> guessResult_;
  /**
   * <code>repeated .xddq.pb.SpChampPlayerGuessInfoMsg guessResult = 3;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.SpChampPlayerGuessInfoMsg> getGuessResultList() {
    return guessResult_;
  }
  /**
   * <code>repeated .xddq.pb.SpChampPlayerGuessInfoMsg guessResult = 3;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.SpChampPlayerGuessInfoMsgOrBuilder> 
      getGuessResultOrBuilderList() {
    return guessResult_;
  }
  /**
   * <code>repeated .xddq.pb.SpChampPlayerGuessInfoMsg guessResult = 3;</code>
   */
  @java.lang.Override
  public int getGuessResultCount() {
    return guessResult_.size();
  }
  /**
   * <code>repeated .xddq.pb.SpChampPlayerGuessInfoMsg guessResult = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.SpChampPlayerGuessInfoMsg getGuessResult(int index) {
    return guessResult_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.SpChampPlayerGuessInfoMsg guessResult = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.SpChampPlayerGuessInfoMsgOrBuilder getGuessResultOrBuilder(
      int index) {
    return guessResult_.get(index);
  }

  public static final int FINALGUESSRESULT_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.SpChampFinalGuessResultMsg> finalGuessResult_;
  /**
   * <code>repeated .xddq.pb.SpChampFinalGuessResultMsg finalGuessResult = 4;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.SpChampFinalGuessResultMsg> getFinalGuessResultList() {
    return finalGuessResult_;
  }
  /**
   * <code>repeated .xddq.pb.SpChampFinalGuessResultMsg finalGuessResult = 4;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.SpChampFinalGuessResultMsgOrBuilder> 
      getFinalGuessResultOrBuilderList() {
    return finalGuessResult_;
  }
  /**
   * <code>repeated .xddq.pb.SpChampFinalGuessResultMsg finalGuessResult = 4;</code>
   */
  @java.lang.Override
  public int getFinalGuessResultCount() {
    return finalGuessResult_.size();
  }
  /**
   * <code>repeated .xddq.pb.SpChampFinalGuessResultMsg finalGuessResult = 4;</code>
   */
  @java.lang.Override
  public xddq.pb.SpChampFinalGuessResultMsg getFinalGuessResult(int index) {
    return finalGuessResult_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.SpChampFinalGuessResultMsg finalGuessResult = 4;</code>
   */
  @java.lang.Override
  public xddq.pb.SpChampFinalGuessResultMsgOrBuilder getFinalGuessResultOrBuilder(
      int index) {
    return finalGuessResult_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, activityId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 2, reward_);
    }
    for (int i = 0; i < guessResult_.size(); i++) {
      output.writeMessage(3, guessResult_.get(i));
    }
    for (int i = 0; i < finalGuessResult_.size(); i++) {
      output.writeMessage(4, finalGuessResult_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, activityId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(2, reward_);
    }
    for (int i = 0; i < guessResult_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, guessResult_.get(i));
    }
    for (int i = 0; i < finalGuessResult_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(4, finalGuessResult_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.SpChampSendGuessRewardMsg)) {
      return super.equals(obj);
    }
    xddq.pb.SpChampSendGuessRewardMsg other = (xddq.pb.SpChampSendGuessRewardMsg) obj;

    if (hasActivityId() != other.hasActivityId()) return false;
    if (hasActivityId()) {
      if (getActivityId()
          != other.getActivityId()) return false;
    }
    if (hasReward() != other.hasReward()) return false;
    if (hasReward()) {
      if (!getReward()
          .equals(other.getReward())) return false;
    }
    if (!getGuessResultList()
        .equals(other.getGuessResultList())) return false;
    if (!getFinalGuessResultList()
        .equals(other.getFinalGuessResultList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasActivityId()) {
      hash = (37 * hash) + ACTIVITYID_FIELD_NUMBER;
      hash = (53 * hash) + getActivityId();
    }
    if (hasReward()) {
      hash = (37 * hash) + REWARD_FIELD_NUMBER;
      hash = (53 * hash) + getReward().hashCode();
    }
    if (getGuessResultCount() > 0) {
      hash = (37 * hash) + GUESSRESULT_FIELD_NUMBER;
      hash = (53 * hash) + getGuessResultList().hashCode();
    }
    if (getFinalGuessResultCount() > 0) {
      hash = (37 * hash) + FINALGUESSRESULT_FIELD_NUMBER;
      hash = (53 * hash) + getFinalGuessResultList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.SpChampSendGuessRewardMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.SpChampSendGuessRewardMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.SpChampSendGuessRewardMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.SpChampSendGuessRewardMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.SpChampSendGuessRewardMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.SpChampSendGuessRewardMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.SpChampSendGuessRewardMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.SpChampSendGuessRewardMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.SpChampSendGuessRewardMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.SpChampSendGuessRewardMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.SpChampSendGuessRewardMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.SpChampSendGuessRewardMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.SpChampSendGuessRewardMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.SpChampSendGuessRewardMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.SpChampSendGuessRewardMsg)
      xddq.pb.SpChampSendGuessRewardMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_SpChampSendGuessRewardMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_SpChampSendGuessRewardMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.SpChampSendGuessRewardMsg.class, xddq.pb.SpChampSendGuessRewardMsg.Builder.class);
    }

    // Construct using xddq.pb.SpChampSendGuessRewardMsg.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      activityId_ = 0;
      reward_ = "";
      if (guessResultBuilder_ == null) {
        guessResult_ = java.util.Collections.emptyList();
      } else {
        guessResult_ = null;
        guessResultBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000004);
      if (finalGuessResultBuilder_ == null) {
        finalGuessResult_ = java.util.Collections.emptyList();
      } else {
        finalGuessResult_ = null;
        finalGuessResultBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000008);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_SpChampSendGuessRewardMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.SpChampSendGuessRewardMsg getDefaultInstanceForType() {
      return xddq.pb.SpChampSendGuessRewardMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.SpChampSendGuessRewardMsg build() {
      xddq.pb.SpChampSendGuessRewardMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.SpChampSendGuessRewardMsg buildPartial() {
      xddq.pb.SpChampSendGuessRewardMsg result = new xddq.pb.SpChampSendGuessRewardMsg(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.SpChampSendGuessRewardMsg result) {
      if (guessResultBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0)) {
          guessResult_ = java.util.Collections.unmodifiableList(guessResult_);
          bitField0_ = (bitField0_ & ~0x00000004);
        }
        result.guessResult_ = guessResult_;
      } else {
        result.guessResult_ = guessResultBuilder_.build();
      }
      if (finalGuessResultBuilder_ == null) {
        if (((bitField0_ & 0x00000008) != 0)) {
          finalGuessResult_ = java.util.Collections.unmodifiableList(finalGuessResult_);
          bitField0_ = (bitField0_ & ~0x00000008);
        }
        result.finalGuessResult_ = finalGuessResult_;
      } else {
        result.finalGuessResult_ = finalGuessResultBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.SpChampSendGuessRewardMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.activityId_ = activityId_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.reward_ = reward_;
        to_bitField0_ |= 0x00000002;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.SpChampSendGuessRewardMsg) {
        return mergeFrom((xddq.pb.SpChampSendGuessRewardMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.SpChampSendGuessRewardMsg other) {
      if (other == xddq.pb.SpChampSendGuessRewardMsg.getDefaultInstance()) return this;
      if (other.hasActivityId()) {
        setActivityId(other.getActivityId());
      }
      if (other.hasReward()) {
        reward_ = other.reward_;
        bitField0_ |= 0x00000002;
        onChanged();
      }
      if (guessResultBuilder_ == null) {
        if (!other.guessResult_.isEmpty()) {
          if (guessResult_.isEmpty()) {
            guessResult_ = other.guessResult_;
            bitField0_ = (bitField0_ & ~0x00000004);
          } else {
            ensureGuessResultIsMutable();
            guessResult_.addAll(other.guessResult_);
          }
          onChanged();
        }
      } else {
        if (!other.guessResult_.isEmpty()) {
          if (guessResultBuilder_.isEmpty()) {
            guessResultBuilder_.dispose();
            guessResultBuilder_ = null;
            guessResult_ = other.guessResult_;
            bitField0_ = (bitField0_ & ~0x00000004);
            guessResultBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetGuessResultFieldBuilder() : null;
          } else {
            guessResultBuilder_.addAllMessages(other.guessResult_);
          }
        }
      }
      if (finalGuessResultBuilder_ == null) {
        if (!other.finalGuessResult_.isEmpty()) {
          if (finalGuessResult_.isEmpty()) {
            finalGuessResult_ = other.finalGuessResult_;
            bitField0_ = (bitField0_ & ~0x00000008);
          } else {
            ensureFinalGuessResultIsMutable();
            finalGuessResult_.addAll(other.finalGuessResult_);
          }
          onChanged();
        }
      } else {
        if (!other.finalGuessResult_.isEmpty()) {
          if (finalGuessResultBuilder_.isEmpty()) {
            finalGuessResultBuilder_.dispose();
            finalGuessResultBuilder_ = null;
            finalGuessResult_ = other.finalGuessResult_;
            bitField0_ = (bitField0_ & ~0x00000008);
            finalGuessResultBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetFinalGuessResultFieldBuilder() : null;
          } else {
            finalGuessResultBuilder_.addAllMessages(other.finalGuessResult_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              activityId_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              reward_ = input.readBytes();
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 26: {
              xddq.pb.SpChampPlayerGuessInfoMsg m =
                  input.readMessage(
                      xddq.pb.SpChampPlayerGuessInfoMsg.parser(),
                      extensionRegistry);
              if (guessResultBuilder_ == null) {
                ensureGuessResultIsMutable();
                guessResult_.add(m);
              } else {
                guessResultBuilder_.addMessage(m);
              }
              break;
            } // case 26
            case 34: {
              xddq.pb.SpChampFinalGuessResultMsg m =
                  input.readMessage(
                      xddq.pb.SpChampFinalGuessResultMsg.parser(),
                      extensionRegistry);
              if (finalGuessResultBuilder_ == null) {
                ensureFinalGuessResultIsMutable();
                finalGuessResult_.add(m);
              } else {
                finalGuessResultBuilder_.addMessage(m);
              }
              break;
            } // case 34
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int activityId_ ;
    /**
     * <code>optional int32 activityId = 1;</code>
     * @return Whether the activityId field is set.
     */
    @java.lang.Override
    public boolean hasActivityId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 activityId = 1;</code>
     * @return The activityId.
     */
    @java.lang.Override
    public int getActivityId() {
      return activityId_;
    }
    /**
     * <code>optional int32 activityId = 1;</code>
     * @param value The activityId to set.
     * @return This builder for chaining.
     */
    public Builder setActivityId(int value) {

      activityId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 activityId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearActivityId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      activityId_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object reward_ = "";
    /**
     * <code>optional string reward = 2;</code>
     * @return Whether the reward field is set.
     */
    public boolean hasReward() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional string reward = 2;</code>
     * @return The reward.
     */
    public java.lang.String getReward() {
      java.lang.Object ref = reward_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          reward_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string reward = 2;</code>
     * @return The bytes for reward.
     */
    public com.google.protobuf.ByteString
        getRewardBytes() {
      java.lang.Object ref = reward_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        reward_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string reward = 2;</code>
     * @param value The reward to set.
     * @return This builder for chaining.
     */
    public Builder setReward(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      reward_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional string reward = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearReward() {
      reward_ = getDefaultInstance().getReward();
      bitField0_ = (bitField0_ & ~0x00000002);
      onChanged();
      return this;
    }
    /**
     * <code>optional string reward = 2;</code>
     * @param value The bytes for reward to set.
     * @return This builder for chaining.
     */
    public Builder setRewardBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      reward_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.SpChampPlayerGuessInfoMsg> guessResult_ =
      java.util.Collections.emptyList();
    private void ensureGuessResultIsMutable() {
      if (!((bitField0_ & 0x00000004) != 0)) {
        guessResult_ = new java.util.ArrayList<xddq.pb.SpChampPlayerGuessInfoMsg>(guessResult_);
        bitField0_ |= 0x00000004;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.SpChampPlayerGuessInfoMsg, xddq.pb.SpChampPlayerGuessInfoMsg.Builder, xddq.pb.SpChampPlayerGuessInfoMsgOrBuilder> guessResultBuilder_;

    /**
     * <code>repeated .xddq.pb.SpChampPlayerGuessInfoMsg guessResult = 3;</code>
     */
    public java.util.List<xddq.pb.SpChampPlayerGuessInfoMsg> getGuessResultList() {
      if (guessResultBuilder_ == null) {
        return java.util.Collections.unmodifiableList(guessResult_);
      } else {
        return guessResultBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.SpChampPlayerGuessInfoMsg guessResult = 3;</code>
     */
    public int getGuessResultCount() {
      if (guessResultBuilder_ == null) {
        return guessResult_.size();
      } else {
        return guessResultBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.SpChampPlayerGuessInfoMsg guessResult = 3;</code>
     */
    public xddq.pb.SpChampPlayerGuessInfoMsg getGuessResult(int index) {
      if (guessResultBuilder_ == null) {
        return guessResult_.get(index);
      } else {
        return guessResultBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.SpChampPlayerGuessInfoMsg guessResult = 3;</code>
     */
    public Builder setGuessResult(
        int index, xddq.pb.SpChampPlayerGuessInfoMsg value) {
      if (guessResultBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureGuessResultIsMutable();
        guessResult_.set(index, value);
        onChanged();
      } else {
        guessResultBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.SpChampPlayerGuessInfoMsg guessResult = 3;</code>
     */
    public Builder setGuessResult(
        int index, xddq.pb.SpChampPlayerGuessInfoMsg.Builder builderForValue) {
      if (guessResultBuilder_ == null) {
        ensureGuessResultIsMutable();
        guessResult_.set(index, builderForValue.build());
        onChanged();
      } else {
        guessResultBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.SpChampPlayerGuessInfoMsg guessResult = 3;</code>
     */
    public Builder addGuessResult(xddq.pb.SpChampPlayerGuessInfoMsg value) {
      if (guessResultBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureGuessResultIsMutable();
        guessResult_.add(value);
        onChanged();
      } else {
        guessResultBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.SpChampPlayerGuessInfoMsg guessResult = 3;</code>
     */
    public Builder addGuessResult(
        int index, xddq.pb.SpChampPlayerGuessInfoMsg value) {
      if (guessResultBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureGuessResultIsMutable();
        guessResult_.add(index, value);
        onChanged();
      } else {
        guessResultBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.SpChampPlayerGuessInfoMsg guessResult = 3;</code>
     */
    public Builder addGuessResult(
        xddq.pb.SpChampPlayerGuessInfoMsg.Builder builderForValue) {
      if (guessResultBuilder_ == null) {
        ensureGuessResultIsMutable();
        guessResult_.add(builderForValue.build());
        onChanged();
      } else {
        guessResultBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.SpChampPlayerGuessInfoMsg guessResult = 3;</code>
     */
    public Builder addGuessResult(
        int index, xddq.pb.SpChampPlayerGuessInfoMsg.Builder builderForValue) {
      if (guessResultBuilder_ == null) {
        ensureGuessResultIsMutable();
        guessResult_.add(index, builderForValue.build());
        onChanged();
      } else {
        guessResultBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.SpChampPlayerGuessInfoMsg guessResult = 3;</code>
     */
    public Builder addAllGuessResult(
        java.lang.Iterable<? extends xddq.pb.SpChampPlayerGuessInfoMsg> values) {
      if (guessResultBuilder_ == null) {
        ensureGuessResultIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, guessResult_);
        onChanged();
      } else {
        guessResultBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.SpChampPlayerGuessInfoMsg guessResult = 3;</code>
     */
    public Builder clearGuessResult() {
      if (guessResultBuilder_ == null) {
        guessResult_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
      } else {
        guessResultBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.SpChampPlayerGuessInfoMsg guessResult = 3;</code>
     */
    public Builder removeGuessResult(int index) {
      if (guessResultBuilder_ == null) {
        ensureGuessResultIsMutable();
        guessResult_.remove(index);
        onChanged();
      } else {
        guessResultBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.SpChampPlayerGuessInfoMsg guessResult = 3;</code>
     */
    public xddq.pb.SpChampPlayerGuessInfoMsg.Builder getGuessResultBuilder(
        int index) {
      return internalGetGuessResultFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.SpChampPlayerGuessInfoMsg guessResult = 3;</code>
     */
    public xddq.pb.SpChampPlayerGuessInfoMsgOrBuilder getGuessResultOrBuilder(
        int index) {
      if (guessResultBuilder_ == null) {
        return guessResult_.get(index);  } else {
        return guessResultBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.SpChampPlayerGuessInfoMsg guessResult = 3;</code>
     */
    public java.util.List<? extends xddq.pb.SpChampPlayerGuessInfoMsgOrBuilder> 
         getGuessResultOrBuilderList() {
      if (guessResultBuilder_ != null) {
        return guessResultBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(guessResult_);
      }
    }
    /**
     * <code>repeated .xddq.pb.SpChampPlayerGuessInfoMsg guessResult = 3;</code>
     */
    public xddq.pb.SpChampPlayerGuessInfoMsg.Builder addGuessResultBuilder() {
      return internalGetGuessResultFieldBuilder().addBuilder(
          xddq.pb.SpChampPlayerGuessInfoMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.SpChampPlayerGuessInfoMsg guessResult = 3;</code>
     */
    public xddq.pb.SpChampPlayerGuessInfoMsg.Builder addGuessResultBuilder(
        int index) {
      return internalGetGuessResultFieldBuilder().addBuilder(
          index, xddq.pb.SpChampPlayerGuessInfoMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.SpChampPlayerGuessInfoMsg guessResult = 3;</code>
     */
    public java.util.List<xddq.pb.SpChampPlayerGuessInfoMsg.Builder> 
         getGuessResultBuilderList() {
      return internalGetGuessResultFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.SpChampPlayerGuessInfoMsg, xddq.pb.SpChampPlayerGuessInfoMsg.Builder, xddq.pb.SpChampPlayerGuessInfoMsgOrBuilder> 
        internalGetGuessResultFieldBuilder() {
      if (guessResultBuilder_ == null) {
        guessResultBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.SpChampPlayerGuessInfoMsg, xddq.pb.SpChampPlayerGuessInfoMsg.Builder, xddq.pb.SpChampPlayerGuessInfoMsgOrBuilder>(
                guessResult_,
                ((bitField0_ & 0x00000004) != 0),
                getParentForChildren(),
                isClean());
        guessResult_ = null;
      }
      return guessResultBuilder_;
    }

    private java.util.List<xddq.pb.SpChampFinalGuessResultMsg> finalGuessResult_ =
      java.util.Collections.emptyList();
    private void ensureFinalGuessResultIsMutable() {
      if (!((bitField0_ & 0x00000008) != 0)) {
        finalGuessResult_ = new java.util.ArrayList<xddq.pb.SpChampFinalGuessResultMsg>(finalGuessResult_);
        bitField0_ |= 0x00000008;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.SpChampFinalGuessResultMsg, xddq.pb.SpChampFinalGuessResultMsg.Builder, xddq.pb.SpChampFinalGuessResultMsgOrBuilder> finalGuessResultBuilder_;

    /**
     * <code>repeated .xddq.pb.SpChampFinalGuessResultMsg finalGuessResult = 4;</code>
     */
    public java.util.List<xddq.pb.SpChampFinalGuessResultMsg> getFinalGuessResultList() {
      if (finalGuessResultBuilder_ == null) {
        return java.util.Collections.unmodifiableList(finalGuessResult_);
      } else {
        return finalGuessResultBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.SpChampFinalGuessResultMsg finalGuessResult = 4;</code>
     */
    public int getFinalGuessResultCount() {
      if (finalGuessResultBuilder_ == null) {
        return finalGuessResult_.size();
      } else {
        return finalGuessResultBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.SpChampFinalGuessResultMsg finalGuessResult = 4;</code>
     */
    public xddq.pb.SpChampFinalGuessResultMsg getFinalGuessResult(int index) {
      if (finalGuessResultBuilder_ == null) {
        return finalGuessResult_.get(index);
      } else {
        return finalGuessResultBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.SpChampFinalGuessResultMsg finalGuessResult = 4;</code>
     */
    public Builder setFinalGuessResult(
        int index, xddq.pb.SpChampFinalGuessResultMsg value) {
      if (finalGuessResultBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureFinalGuessResultIsMutable();
        finalGuessResult_.set(index, value);
        onChanged();
      } else {
        finalGuessResultBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.SpChampFinalGuessResultMsg finalGuessResult = 4;</code>
     */
    public Builder setFinalGuessResult(
        int index, xddq.pb.SpChampFinalGuessResultMsg.Builder builderForValue) {
      if (finalGuessResultBuilder_ == null) {
        ensureFinalGuessResultIsMutable();
        finalGuessResult_.set(index, builderForValue.build());
        onChanged();
      } else {
        finalGuessResultBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.SpChampFinalGuessResultMsg finalGuessResult = 4;</code>
     */
    public Builder addFinalGuessResult(xddq.pb.SpChampFinalGuessResultMsg value) {
      if (finalGuessResultBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureFinalGuessResultIsMutable();
        finalGuessResult_.add(value);
        onChanged();
      } else {
        finalGuessResultBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.SpChampFinalGuessResultMsg finalGuessResult = 4;</code>
     */
    public Builder addFinalGuessResult(
        int index, xddq.pb.SpChampFinalGuessResultMsg value) {
      if (finalGuessResultBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureFinalGuessResultIsMutable();
        finalGuessResult_.add(index, value);
        onChanged();
      } else {
        finalGuessResultBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.SpChampFinalGuessResultMsg finalGuessResult = 4;</code>
     */
    public Builder addFinalGuessResult(
        xddq.pb.SpChampFinalGuessResultMsg.Builder builderForValue) {
      if (finalGuessResultBuilder_ == null) {
        ensureFinalGuessResultIsMutable();
        finalGuessResult_.add(builderForValue.build());
        onChanged();
      } else {
        finalGuessResultBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.SpChampFinalGuessResultMsg finalGuessResult = 4;</code>
     */
    public Builder addFinalGuessResult(
        int index, xddq.pb.SpChampFinalGuessResultMsg.Builder builderForValue) {
      if (finalGuessResultBuilder_ == null) {
        ensureFinalGuessResultIsMutable();
        finalGuessResult_.add(index, builderForValue.build());
        onChanged();
      } else {
        finalGuessResultBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.SpChampFinalGuessResultMsg finalGuessResult = 4;</code>
     */
    public Builder addAllFinalGuessResult(
        java.lang.Iterable<? extends xddq.pb.SpChampFinalGuessResultMsg> values) {
      if (finalGuessResultBuilder_ == null) {
        ensureFinalGuessResultIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, finalGuessResult_);
        onChanged();
      } else {
        finalGuessResultBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.SpChampFinalGuessResultMsg finalGuessResult = 4;</code>
     */
    public Builder clearFinalGuessResult() {
      if (finalGuessResultBuilder_ == null) {
        finalGuessResult_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000008);
        onChanged();
      } else {
        finalGuessResultBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.SpChampFinalGuessResultMsg finalGuessResult = 4;</code>
     */
    public Builder removeFinalGuessResult(int index) {
      if (finalGuessResultBuilder_ == null) {
        ensureFinalGuessResultIsMutable();
        finalGuessResult_.remove(index);
        onChanged();
      } else {
        finalGuessResultBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.SpChampFinalGuessResultMsg finalGuessResult = 4;</code>
     */
    public xddq.pb.SpChampFinalGuessResultMsg.Builder getFinalGuessResultBuilder(
        int index) {
      return internalGetFinalGuessResultFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.SpChampFinalGuessResultMsg finalGuessResult = 4;</code>
     */
    public xddq.pb.SpChampFinalGuessResultMsgOrBuilder getFinalGuessResultOrBuilder(
        int index) {
      if (finalGuessResultBuilder_ == null) {
        return finalGuessResult_.get(index);  } else {
        return finalGuessResultBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.SpChampFinalGuessResultMsg finalGuessResult = 4;</code>
     */
    public java.util.List<? extends xddq.pb.SpChampFinalGuessResultMsgOrBuilder> 
         getFinalGuessResultOrBuilderList() {
      if (finalGuessResultBuilder_ != null) {
        return finalGuessResultBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(finalGuessResult_);
      }
    }
    /**
     * <code>repeated .xddq.pb.SpChampFinalGuessResultMsg finalGuessResult = 4;</code>
     */
    public xddq.pb.SpChampFinalGuessResultMsg.Builder addFinalGuessResultBuilder() {
      return internalGetFinalGuessResultFieldBuilder().addBuilder(
          xddq.pb.SpChampFinalGuessResultMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.SpChampFinalGuessResultMsg finalGuessResult = 4;</code>
     */
    public xddq.pb.SpChampFinalGuessResultMsg.Builder addFinalGuessResultBuilder(
        int index) {
      return internalGetFinalGuessResultFieldBuilder().addBuilder(
          index, xddq.pb.SpChampFinalGuessResultMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.SpChampFinalGuessResultMsg finalGuessResult = 4;</code>
     */
    public java.util.List<xddq.pb.SpChampFinalGuessResultMsg.Builder> 
         getFinalGuessResultBuilderList() {
      return internalGetFinalGuessResultFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.SpChampFinalGuessResultMsg, xddq.pb.SpChampFinalGuessResultMsg.Builder, xddq.pb.SpChampFinalGuessResultMsgOrBuilder> 
        internalGetFinalGuessResultFieldBuilder() {
      if (finalGuessResultBuilder_ == null) {
        finalGuessResultBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.SpChampFinalGuessResultMsg, xddq.pb.SpChampFinalGuessResultMsg.Builder, xddq.pb.SpChampFinalGuessResultMsgOrBuilder>(
                finalGuessResult_,
                ((bitField0_ & 0x00000008) != 0),
                getParentForChildren(),
                isClean());
        finalGuessResult_ = null;
      }
      return finalGuessResultBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.SpChampSendGuessRewardMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.SpChampSendGuessRewardMsg)
  private static final xddq.pb.SpChampSendGuessRewardMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.SpChampSendGuessRewardMsg();
  }

  public static xddq.pb.SpChampSendGuessRewardMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<SpChampSendGuessRewardMsg>
      PARSER = new com.google.protobuf.AbstractParser<SpChampSendGuessRewardMsg>() {
    @java.lang.Override
    public SpChampSendGuessRewardMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<SpChampSendGuessRewardMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<SpChampSendGuessRewardMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.SpChampSendGuessRewardMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

