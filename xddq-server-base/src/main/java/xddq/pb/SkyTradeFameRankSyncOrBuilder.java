// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

public interface SkyTradeFameRankSyncOrBuilder extends
    // @@protoc_insertion_point(interface_extends:xddq.pb.SkyTradeFameRankSync)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>optional int64 unionId = 1;</code>
   * @return Whether the unionId field is set.
   */
  boolean hasUnionId();
  /**
   * <code>optional int64 unionId = 1;</code>
   * @return The unionId.
   */
  long getUnionId();

  /**
   * <code>optional int32 finalFame = 2;</code>
   * @return Whether the finalFame field is set.
   */
  boolean hasFinalFame();
  /**
   * <code>optional int32 finalFame = 2;</code>
   * @return The finalFame.
   */
  int getFinalFame();

  /**
   * <code>repeated .xddq.pb.SkyTradeUnionGroupRankMsg groupRankMsg = 3;</code>
   */
  java.util.List<xddq.pb.SkyTradeUnionGroupRankMsg> 
      getGroupRankMsgList();
  /**
   * <code>repeated .xddq.pb.SkyTradeUnionGroupRankMsg groupRankMsg = 3;</code>
   */
  xddq.pb.SkyTradeUnionGroupRankMsg getGroupRankMsg(int index);
  /**
   * <code>repeated .xddq.pb.SkyTradeUnionGroupRankMsg groupRankMsg = 3;</code>
   */
  int getGroupRankMsgCount();
  /**
   * <code>repeated .xddq.pb.SkyTradeUnionGroupRankMsg groupRankMsg = 3;</code>
   */
  java.util.List<? extends xddq.pb.SkyTradeUnionGroupRankMsgOrBuilder> 
      getGroupRankMsgOrBuilderList();
  /**
   * <code>repeated .xddq.pb.SkyTradeUnionGroupRankMsg groupRankMsg = 3;</code>
   */
  xddq.pb.SkyTradeUnionGroupRankMsgOrBuilder getGroupRankMsgOrBuilder(
      int index);
}
