// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.PlanesTrialApplyUserEntity}
 */
public final class PlanesTrialApplyUserEntity extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.PlanesTrialApplyUserEntity)
    PlanesTrialApplyUserEntityOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      PlanesTrialApplyUserEntity.class.getName());
  }
  // Use PlanesTrialApplyUserEntity.newBuilder() to construct.
  private PlanesTrialApplyUserEntity(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private PlanesTrialApplyUserEntity() {
    fightValue_ = "";
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_PlanesTrialApplyUserEntity_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_PlanesTrialApplyUserEntity_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.PlanesTrialApplyUserEntity.class, xddq.pb.PlanesTrialApplyUserEntity.Builder.class);
  }

  private int bitField0_;
  public static final int PLAYERINFO_FIELD_NUMBER = 1;
  private xddq.pb.PlayerHeadAndNameMsg playerInfo_;
  /**
   * <code>optional .xddq.pb.PlayerHeadAndNameMsg playerInfo = 1;</code>
   * @return Whether the playerInfo field is set.
   */
  @java.lang.Override
  public boolean hasPlayerInfo() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional .xddq.pb.PlayerHeadAndNameMsg playerInfo = 1;</code>
   * @return The playerInfo.
   */
  @java.lang.Override
  public xddq.pb.PlayerHeadAndNameMsg getPlayerInfo() {
    return playerInfo_ == null ? xddq.pb.PlayerHeadAndNameMsg.getDefaultInstance() : playerInfo_;
  }
  /**
   * <code>optional .xddq.pb.PlayerHeadAndNameMsg playerInfo = 1;</code>
   */
  @java.lang.Override
  public xddq.pb.PlayerHeadAndNameMsgOrBuilder getPlayerInfoOrBuilder() {
    return playerInfo_ == null ? xddq.pb.PlayerHeadAndNameMsg.getDefaultInstance() : playerInfo_;
  }

  public static final int APPLYTIME_FIELD_NUMBER = 2;
  private long applyTime_ = 0L;
  /**
   * <code>optional int64 applyTime = 2;</code>
   * @return Whether the applyTime field is set.
   */
  @java.lang.Override
  public boolean hasApplyTime() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int64 applyTime = 2;</code>
   * @return The applyTime.
   */
  @java.lang.Override
  public long getApplyTime() {
    return applyTime_;
  }

  public static final int FIGHTVALUE_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private volatile java.lang.Object fightValue_ = "";
  /**
   * <code>optional string fightValue = 3;</code>
   * @return Whether the fightValue field is set.
   */
  @java.lang.Override
  public boolean hasFightValue() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional string fightValue = 3;</code>
   * @return The fightValue.
   */
  @java.lang.Override
  public java.lang.String getFightValue() {
    java.lang.Object ref = fightValue_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        fightValue_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string fightValue = 3;</code>
   * @return The bytes for fightValue.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getFightValueBytes() {
    java.lang.Object ref = fightValue_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      fightValue_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int MYMAXSTAGEID_FIELD_NUMBER = 4;
  private int myMaxStageId_ = 0;
  /**
   * <code>optional int32 myMaxStageId = 4;</code>
   * @return Whether the myMaxStageId field is set.
   */
  @java.lang.Override
  public boolean hasMyMaxStageId() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional int32 myMaxStageId = 4;</code>
   * @return The myMaxStageId.
   */
  @java.lang.Override
  public int getMyMaxStageId() {
    return myMaxStageId_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeMessage(1, getPlayerInfo());
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt64(2, applyTime_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 3, fightValue_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt32(4, myMaxStageId_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, getPlayerInfo());
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(2, applyTime_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(3, fightValue_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(4, myMaxStageId_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.PlanesTrialApplyUserEntity)) {
      return super.equals(obj);
    }
    xddq.pb.PlanesTrialApplyUserEntity other = (xddq.pb.PlanesTrialApplyUserEntity) obj;

    if (hasPlayerInfo() != other.hasPlayerInfo()) return false;
    if (hasPlayerInfo()) {
      if (!getPlayerInfo()
          .equals(other.getPlayerInfo())) return false;
    }
    if (hasApplyTime() != other.hasApplyTime()) return false;
    if (hasApplyTime()) {
      if (getApplyTime()
          != other.getApplyTime()) return false;
    }
    if (hasFightValue() != other.hasFightValue()) return false;
    if (hasFightValue()) {
      if (!getFightValue()
          .equals(other.getFightValue())) return false;
    }
    if (hasMyMaxStageId() != other.hasMyMaxStageId()) return false;
    if (hasMyMaxStageId()) {
      if (getMyMaxStageId()
          != other.getMyMaxStageId()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasPlayerInfo()) {
      hash = (37 * hash) + PLAYERINFO_FIELD_NUMBER;
      hash = (53 * hash) + getPlayerInfo().hashCode();
    }
    if (hasApplyTime()) {
      hash = (37 * hash) + APPLYTIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getApplyTime());
    }
    if (hasFightValue()) {
      hash = (37 * hash) + FIGHTVALUE_FIELD_NUMBER;
      hash = (53 * hash) + getFightValue().hashCode();
    }
    if (hasMyMaxStageId()) {
      hash = (37 * hash) + MYMAXSTAGEID_FIELD_NUMBER;
      hash = (53 * hash) + getMyMaxStageId();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.PlanesTrialApplyUserEntity parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PlanesTrialApplyUserEntity parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PlanesTrialApplyUserEntity parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PlanesTrialApplyUserEntity parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PlanesTrialApplyUserEntity parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PlanesTrialApplyUserEntity parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PlanesTrialApplyUserEntity parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.PlanesTrialApplyUserEntity parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.PlanesTrialApplyUserEntity parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.PlanesTrialApplyUserEntity parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.PlanesTrialApplyUserEntity parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.PlanesTrialApplyUserEntity parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.PlanesTrialApplyUserEntity prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.PlanesTrialApplyUserEntity}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.PlanesTrialApplyUserEntity)
      xddq.pb.PlanesTrialApplyUserEntityOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PlanesTrialApplyUserEntity_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PlanesTrialApplyUserEntity_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.PlanesTrialApplyUserEntity.class, xddq.pb.PlanesTrialApplyUserEntity.Builder.class);
    }

    // Construct using xddq.pb.PlanesTrialApplyUserEntity.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetPlayerInfoFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      playerInfo_ = null;
      if (playerInfoBuilder_ != null) {
        playerInfoBuilder_.dispose();
        playerInfoBuilder_ = null;
      }
      applyTime_ = 0L;
      fightValue_ = "";
      myMaxStageId_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PlanesTrialApplyUserEntity_descriptor;
    }

    @java.lang.Override
    public xddq.pb.PlanesTrialApplyUserEntity getDefaultInstanceForType() {
      return xddq.pb.PlanesTrialApplyUserEntity.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.PlanesTrialApplyUserEntity build() {
      xddq.pb.PlanesTrialApplyUserEntity result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.PlanesTrialApplyUserEntity buildPartial() {
      xddq.pb.PlanesTrialApplyUserEntity result = new xddq.pb.PlanesTrialApplyUserEntity(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.PlanesTrialApplyUserEntity result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.playerInfo_ = playerInfoBuilder_ == null
            ? playerInfo_
            : playerInfoBuilder_.build();
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.applyTime_ = applyTime_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.fightValue_ = fightValue_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.myMaxStageId_ = myMaxStageId_;
        to_bitField0_ |= 0x00000008;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.PlanesTrialApplyUserEntity) {
        return mergeFrom((xddq.pb.PlanesTrialApplyUserEntity)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.PlanesTrialApplyUserEntity other) {
      if (other == xddq.pb.PlanesTrialApplyUserEntity.getDefaultInstance()) return this;
      if (other.hasPlayerInfo()) {
        mergePlayerInfo(other.getPlayerInfo());
      }
      if (other.hasApplyTime()) {
        setApplyTime(other.getApplyTime());
      }
      if (other.hasFightValue()) {
        fightValue_ = other.fightValue_;
        bitField0_ |= 0x00000004;
        onChanged();
      }
      if (other.hasMyMaxStageId()) {
        setMyMaxStageId(other.getMyMaxStageId());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              input.readMessage(
                  internalGetPlayerInfoFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 16: {
              applyTime_ = input.readInt64();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 26: {
              fightValue_ = input.readBytes();
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 32: {
              myMaxStageId_ = input.readInt32();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private xddq.pb.PlayerHeadAndNameMsg playerInfo_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PlayerHeadAndNameMsg, xddq.pb.PlayerHeadAndNameMsg.Builder, xddq.pb.PlayerHeadAndNameMsgOrBuilder> playerInfoBuilder_;
    /**
     * <code>optional .xddq.pb.PlayerHeadAndNameMsg playerInfo = 1;</code>
     * @return Whether the playerInfo field is set.
     */
    public boolean hasPlayerInfo() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadAndNameMsg playerInfo = 1;</code>
     * @return The playerInfo.
     */
    public xddq.pb.PlayerHeadAndNameMsg getPlayerInfo() {
      if (playerInfoBuilder_ == null) {
        return playerInfo_ == null ? xddq.pb.PlayerHeadAndNameMsg.getDefaultInstance() : playerInfo_;
      } else {
        return playerInfoBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadAndNameMsg playerInfo = 1;</code>
     */
    public Builder setPlayerInfo(xddq.pb.PlayerHeadAndNameMsg value) {
      if (playerInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        playerInfo_ = value;
      } else {
        playerInfoBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadAndNameMsg playerInfo = 1;</code>
     */
    public Builder setPlayerInfo(
        xddq.pb.PlayerHeadAndNameMsg.Builder builderForValue) {
      if (playerInfoBuilder_ == null) {
        playerInfo_ = builderForValue.build();
      } else {
        playerInfoBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadAndNameMsg playerInfo = 1;</code>
     */
    public Builder mergePlayerInfo(xddq.pb.PlayerHeadAndNameMsg value) {
      if (playerInfoBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0) &&
          playerInfo_ != null &&
          playerInfo_ != xddq.pb.PlayerHeadAndNameMsg.getDefaultInstance()) {
          getPlayerInfoBuilder().mergeFrom(value);
        } else {
          playerInfo_ = value;
        }
      } else {
        playerInfoBuilder_.mergeFrom(value);
      }
      if (playerInfo_ != null) {
        bitField0_ |= 0x00000001;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadAndNameMsg playerInfo = 1;</code>
     */
    public Builder clearPlayerInfo() {
      bitField0_ = (bitField0_ & ~0x00000001);
      playerInfo_ = null;
      if (playerInfoBuilder_ != null) {
        playerInfoBuilder_.dispose();
        playerInfoBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadAndNameMsg playerInfo = 1;</code>
     */
    public xddq.pb.PlayerHeadAndNameMsg.Builder getPlayerInfoBuilder() {
      bitField0_ |= 0x00000001;
      onChanged();
      return internalGetPlayerInfoFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadAndNameMsg playerInfo = 1;</code>
     */
    public xddq.pb.PlayerHeadAndNameMsgOrBuilder getPlayerInfoOrBuilder() {
      if (playerInfoBuilder_ != null) {
        return playerInfoBuilder_.getMessageOrBuilder();
      } else {
        return playerInfo_ == null ?
            xddq.pb.PlayerHeadAndNameMsg.getDefaultInstance() : playerInfo_;
      }
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadAndNameMsg playerInfo = 1;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PlayerHeadAndNameMsg, xddq.pb.PlayerHeadAndNameMsg.Builder, xddq.pb.PlayerHeadAndNameMsgOrBuilder> 
        internalGetPlayerInfoFieldBuilder() {
      if (playerInfoBuilder_ == null) {
        playerInfoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.PlayerHeadAndNameMsg, xddq.pb.PlayerHeadAndNameMsg.Builder, xddq.pb.PlayerHeadAndNameMsgOrBuilder>(
                getPlayerInfo(),
                getParentForChildren(),
                isClean());
        playerInfo_ = null;
      }
      return playerInfoBuilder_;
    }

    private long applyTime_ ;
    /**
     * <code>optional int64 applyTime = 2;</code>
     * @return Whether the applyTime field is set.
     */
    @java.lang.Override
    public boolean hasApplyTime() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int64 applyTime = 2;</code>
     * @return The applyTime.
     */
    @java.lang.Override
    public long getApplyTime() {
      return applyTime_;
    }
    /**
     * <code>optional int64 applyTime = 2;</code>
     * @param value The applyTime to set.
     * @return This builder for chaining.
     */
    public Builder setApplyTime(long value) {

      applyTime_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 applyTime = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearApplyTime() {
      bitField0_ = (bitField0_ & ~0x00000002);
      applyTime_ = 0L;
      onChanged();
      return this;
    }

    private java.lang.Object fightValue_ = "";
    /**
     * <code>optional string fightValue = 3;</code>
     * @return Whether the fightValue field is set.
     */
    public boolean hasFightValue() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional string fightValue = 3;</code>
     * @return The fightValue.
     */
    public java.lang.String getFightValue() {
      java.lang.Object ref = fightValue_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          fightValue_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string fightValue = 3;</code>
     * @return The bytes for fightValue.
     */
    public com.google.protobuf.ByteString
        getFightValueBytes() {
      java.lang.Object ref = fightValue_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        fightValue_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string fightValue = 3;</code>
     * @param value The fightValue to set.
     * @return This builder for chaining.
     */
    public Builder setFightValue(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      fightValue_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional string fightValue = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearFightValue() {
      fightValue_ = getDefaultInstance().getFightValue();
      bitField0_ = (bitField0_ & ~0x00000004);
      onChanged();
      return this;
    }
    /**
     * <code>optional string fightValue = 3;</code>
     * @param value The bytes for fightValue to set.
     * @return This builder for chaining.
     */
    public Builder setFightValueBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      fightValue_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }

    private int myMaxStageId_ ;
    /**
     * <code>optional int32 myMaxStageId = 4;</code>
     * @return Whether the myMaxStageId field is set.
     */
    @java.lang.Override
    public boolean hasMyMaxStageId() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int32 myMaxStageId = 4;</code>
     * @return The myMaxStageId.
     */
    @java.lang.Override
    public int getMyMaxStageId() {
      return myMaxStageId_;
    }
    /**
     * <code>optional int32 myMaxStageId = 4;</code>
     * @param value The myMaxStageId to set.
     * @return This builder for chaining.
     */
    public Builder setMyMaxStageId(int value) {

      myMaxStageId_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 myMaxStageId = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearMyMaxStageId() {
      bitField0_ = (bitField0_ & ~0x00000008);
      myMaxStageId_ = 0;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.PlanesTrialApplyUserEntity)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.PlanesTrialApplyUserEntity)
  private static final xddq.pb.PlanesTrialApplyUserEntity DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.PlanesTrialApplyUserEntity();
  }

  public static xddq.pb.PlanesTrialApplyUserEntity getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<PlanesTrialApplyUserEntity>
      PARSER = new com.google.protobuf.AbstractParser<PlanesTrialApplyUserEntity>() {
    @java.lang.Override
    public PlanesTrialApplyUserEntity parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<PlanesTrialApplyUserEntity> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<PlanesTrialApplyUserEntity> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.PlanesTrialApplyUserEntity getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

