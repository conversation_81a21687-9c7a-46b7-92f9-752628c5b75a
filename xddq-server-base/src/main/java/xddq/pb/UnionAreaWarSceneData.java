// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.UnionAreaWarSceneData}
 */
public final class UnionAreaWarSceneData extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.UnionAreaWarSceneData)
    UnionAreaWarSceneDataOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      UnionAreaWarSceneData.class.getName());
  }
  // Use UnionAreaWarSceneData.newBuilder() to construct.
  private UnionAreaWarSceneData(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private UnionAreaWarSceneData() {
    curUnionHp_ = "";
    curDefenderHp_ = "";
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_UnionAreaWarSceneData_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_UnionAreaWarSceneData_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.UnionAreaWarSceneData.class, xddq.pb.UnionAreaWarSceneData.Builder.class);
  }

  private int bitField0_;
  public static final int PLAYERINFO_FIELD_NUMBER = 1;
  private xddq.pb.UnionAreaWarPlayerInfo playerInfo_;
  /**
   * <code>optional .xddq.pb.UnionAreaWarPlayerInfo playerInfo = 1;</code>
   * @return Whether the playerInfo field is set.
   */
  @java.lang.Override
  public boolean hasPlayerInfo() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional .xddq.pb.UnionAreaWarPlayerInfo playerInfo = 1;</code>
   * @return The playerInfo.
   */
  @java.lang.Override
  public xddq.pb.UnionAreaWarPlayerInfo getPlayerInfo() {
    return playerInfo_ == null ? xddq.pb.UnionAreaWarPlayerInfo.getDefaultInstance() : playerInfo_;
  }
  /**
   * <code>optional .xddq.pb.UnionAreaWarPlayerInfo playerInfo = 1;</code>
   */
  @java.lang.Override
  public xddq.pb.UnionAreaWarPlayerInfoOrBuilder getPlayerInfoOrBuilder() {
    return playerInfo_ == null ? xddq.pb.UnionAreaWarPlayerInfo.getDefaultInstance() : playerInfo_;
  }

  public static final int DEFENDUNIONID_FIELD_NUMBER = 2;
  private long defendUnionId_ = 0L;
  /**
   * <code>optional int64 defendUnionId = 2;</code>
   * @return Whether the defendUnionId field is set.
   */
  @java.lang.Override
  public boolean hasDefendUnionId() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int64 defendUnionId = 2;</code>
   * @return The defendUnionId.
   */
  @java.lang.Override
  public long getDefendUnionId() {
    return defendUnionId_;
  }

  public static final int AREAINFO_FIELD_NUMBER = 3;
  private xddq.pb.UnionAreaInfo areaInfo_;
  /**
   * <code>optional .xddq.pb.UnionAreaInfo areaInfo = 3;</code>
   * @return Whether the areaInfo field is set.
   */
  @java.lang.Override
  public boolean hasAreaInfo() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional .xddq.pb.UnionAreaInfo areaInfo = 3;</code>
   * @return The areaInfo.
   */
  @java.lang.Override
  public xddq.pb.UnionAreaInfo getAreaInfo() {
    return areaInfo_ == null ? xddq.pb.UnionAreaInfo.getDefaultInstance() : areaInfo_;
  }
  /**
   * <code>optional .xddq.pb.UnionAreaInfo areaInfo = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.UnionAreaInfoOrBuilder getAreaInfoOrBuilder() {
    return areaInfo_ == null ? xddq.pb.UnionAreaInfo.getDefaultInstance() : areaInfo_;
  }

  public static final int DEFENDERINFO_FIELD_NUMBER = 4;
  private xddq.pb.UnionAreaDefenderInfo defenderInfo_;
  /**
   * <code>optional .xddq.pb.UnionAreaDefenderInfo defenderInfo = 4;</code>
   * @return Whether the defenderInfo field is set.
   */
  @java.lang.Override
  public boolean hasDefenderInfo() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional .xddq.pb.UnionAreaDefenderInfo defenderInfo = 4;</code>
   * @return The defenderInfo.
   */
  @java.lang.Override
  public xddq.pb.UnionAreaDefenderInfo getDefenderInfo() {
    return defenderInfo_ == null ? xddq.pb.UnionAreaDefenderInfo.getDefaultInstance() : defenderInfo_;
  }
  /**
   * <code>optional .xddq.pb.UnionAreaDefenderInfo defenderInfo = 4;</code>
   */
  @java.lang.Override
  public xddq.pb.UnionAreaDefenderInfoOrBuilder getDefenderInfoOrBuilder() {
    return defenderInfo_ == null ? xddq.pb.UnionAreaDefenderInfo.getDefaultInstance() : defenderInfo_;
  }

  public static final int CURUNIONHP_FIELD_NUMBER = 5;
  @SuppressWarnings("serial")
  private volatile java.lang.Object curUnionHp_ = "";
  /**
   * <code>optional string curUnionHp = 5;</code>
   * @return Whether the curUnionHp field is set.
   */
  @java.lang.Override
  public boolean hasCurUnionHp() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>optional string curUnionHp = 5;</code>
   * @return The curUnionHp.
   */
  @java.lang.Override
  public java.lang.String getCurUnionHp() {
    java.lang.Object ref = curUnionHp_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        curUnionHp_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string curUnionHp = 5;</code>
   * @return The bytes for curUnionHp.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getCurUnionHpBytes() {
    java.lang.Object ref = curUnionHp_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      curUnionHp_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CURDEFENDERHP_FIELD_NUMBER = 6;
  @SuppressWarnings("serial")
  private volatile java.lang.Object curDefenderHp_ = "";
  /**
   * <code>optional string curDefenderHp = 6;</code>
   * @return Whether the curDefenderHp field is set.
   */
  @java.lang.Override
  public boolean hasCurDefenderHp() {
    return ((bitField0_ & 0x00000020) != 0);
  }
  /**
   * <code>optional string curDefenderHp = 6;</code>
   * @return The curDefenderHp.
   */
  @java.lang.Override
  public java.lang.String getCurDefenderHp() {
    java.lang.Object ref = curDefenderHp_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        curDefenderHp_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string curDefenderHp = 6;</code>
   * @return The bytes for curDefenderHp.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getCurDefenderHpBytes() {
    java.lang.Object ref = curDefenderHp_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      curDefenderHp_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (hasAreaInfo()) {
      if (!getAreaInfo().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    if (hasDefenderInfo()) {
      if (!getDefenderInfo().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeMessage(1, getPlayerInfo());
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt64(2, defendUnionId_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeMessage(3, getAreaInfo());
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeMessage(4, getDefenderInfo());
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 5, curUnionHp_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 6, curDefenderHp_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, getPlayerInfo());
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(2, defendUnionId_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, getAreaInfo());
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(4, getDefenderInfo());
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(5, curUnionHp_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(6, curDefenderHp_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.UnionAreaWarSceneData)) {
      return super.equals(obj);
    }
    xddq.pb.UnionAreaWarSceneData other = (xddq.pb.UnionAreaWarSceneData) obj;

    if (hasPlayerInfo() != other.hasPlayerInfo()) return false;
    if (hasPlayerInfo()) {
      if (!getPlayerInfo()
          .equals(other.getPlayerInfo())) return false;
    }
    if (hasDefendUnionId() != other.hasDefendUnionId()) return false;
    if (hasDefendUnionId()) {
      if (getDefendUnionId()
          != other.getDefendUnionId()) return false;
    }
    if (hasAreaInfo() != other.hasAreaInfo()) return false;
    if (hasAreaInfo()) {
      if (!getAreaInfo()
          .equals(other.getAreaInfo())) return false;
    }
    if (hasDefenderInfo() != other.hasDefenderInfo()) return false;
    if (hasDefenderInfo()) {
      if (!getDefenderInfo()
          .equals(other.getDefenderInfo())) return false;
    }
    if (hasCurUnionHp() != other.hasCurUnionHp()) return false;
    if (hasCurUnionHp()) {
      if (!getCurUnionHp()
          .equals(other.getCurUnionHp())) return false;
    }
    if (hasCurDefenderHp() != other.hasCurDefenderHp()) return false;
    if (hasCurDefenderHp()) {
      if (!getCurDefenderHp()
          .equals(other.getCurDefenderHp())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasPlayerInfo()) {
      hash = (37 * hash) + PLAYERINFO_FIELD_NUMBER;
      hash = (53 * hash) + getPlayerInfo().hashCode();
    }
    if (hasDefendUnionId()) {
      hash = (37 * hash) + DEFENDUNIONID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getDefendUnionId());
    }
    if (hasAreaInfo()) {
      hash = (37 * hash) + AREAINFO_FIELD_NUMBER;
      hash = (53 * hash) + getAreaInfo().hashCode();
    }
    if (hasDefenderInfo()) {
      hash = (37 * hash) + DEFENDERINFO_FIELD_NUMBER;
      hash = (53 * hash) + getDefenderInfo().hashCode();
    }
    if (hasCurUnionHp()) {
      hash = (37 * hash) + CURUNIONHP_FIELD_NUMBER;
      hash = (53 * hash) + getCurUnionHp().hashCode();
    }
    if (hasCurDefenderHp()) {
      hash = (37 * hash) + CURDEFENDERHP_FIELD_NUMBER;
      hash = (53 * hash) + getCurDefenderHp().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.UnionAreaWarSceneData parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.UnionAreaWarSceneData parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.UnionAreaWarSceneData parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.UnionAreaWarSceneData parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.UnionAreaWarSceneData parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.UnionAreaWarSceneData parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.UnionAreaWarSceneData parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.UnionAreaWarSceneData parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.UnionAreaWarSceneData parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.UnionAreaWarSceneData parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.UnionAreaWarSceneData parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.UnionAreaWarSceneData parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.UnionAreaWarSceneData prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.UnionAreaWarSceneData}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.UnionAreaWarSceneData)
      xddq.pb.UnionAreaWarSceneDataOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_UnionAreaWarSceneData_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_UnionAreaWarSceneData_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.UnionAreaWarSceneData.class, xddq.pb.UnionAreaWarSceneData.Builder.class);
    }

    // Construct using xddq.pb.UnionAreaWarSceneData.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetPlayerInfoFieldBuilder();
        internalGetAreaInfoFieldBuilder();
        internalGetDefenderInfoFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      playerInfo_ = null;
      if (playerInfoBuilder_ != null) {
        playerInfoBuilder_.dispose();
        playerInfoBuilder_ = null;
      }
      defendUnionId_ = 0L;
      areaInfo_ = null;
      if (areaInfoBuilder_ != null) {
        areaInfoBuilder_.dispose();
        areaInfoBuilder_ = null;
      }
      defenderInfo_ = null;
      if (defenderInfoBuilder_ != null) {
        defenderInfoBuilder_.dispose();
        defenderInfoBuilder_ = null;
      }
      curUnionHp_ = "";
      curDefenderHp_ = "";
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_UnionAreaWarSceneData_descriptor;
    }

    @java.lang.Override
    public xddq.pb.UnionAreaWarSceneData getDefaultInstanceForType() {
      return xddq.pb.UnionAreaWarSceneData.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.UnionAreaWarSceneData build() {
      xddq.pb.UnionAreaWarSceneData result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.UnionAreaWarSceneData buildPartial() {
      xddq.pb.UnionAreaWarSceneData result = new xddq.pb.UnionAreaWarSceneData(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.UnionAreaWarSceneData result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.playerInfo_ = playerInfoBuilder_ == null
            ? playerInfo_
            : playerInfoBuilder_.build();
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.defendUnionId_ = defendUnionId_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.areaInfo_ = areaInfoBuilder_ == null
            ? areaInfo_
            : areaInfoBuilder_.build();
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.defenderInfo_ = defenderInfoBuilder_ == null
            ? defenderInfo_
            : defenderInfoBuilder_.build();
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.curUnionHp_ = curUnionHp_;
        to_bitField0_ |= 0x00000010;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.curDefenderHp_ = curDefenderHp_;
        to_bitField0_ |= 0x00000020;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.UnionAreaWarSceneData) {
        return mergeFrom((xddq.pb.UnionAreaWarSceneData)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.UnionAreaWarSceneData other) {
      if (other == xddq.pb.UnionAreaWarSceneData.getDefaultInstance()) return this;
      if (other.hasPlayerInfo()) {
        mergePlayerInfo(other.getPlayerInfo());
      }
      if (other.hasDefendUnionId()) {
        setDefendUnionId(other.getDefendUnionId());
      }
      if (other.hasAreaInfo()) {
        mergeAreaInfo(other.getAreaInfo());
      }
      if (other.hasDefenderInfo()) {
        mergeDefenderInfo(other.getDefenderInfo());
      }
      if (other.hasCurUnionHp()) {
        curUnionHp_ = other.curUnionHp_;
        bitField0_ |= 0x00000010;
        onChanged();
      }
      if (other.hasCurDefenderHp()) {
        curDefenderHp_ = other.curDefenderHp_;
        bitField0_ |= 0x00000020;
        onChanged();
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (hasAreaInfo()) {
        if (!getAreaInfo().isInitialized()) {
          return false;
        }
      }
      if (hasDefenderInfo()) {
        if (!getDefenderInfo().isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              input.readMessage(
                  internalGetPlayerInfoFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 16: {
              defendUnionId_ = input.readInt64();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 26: {
              input.readMessage(
                  internalGetAreaInfoFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 34: {
              input.readMessage(
                  internalGetDefenderInfoFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000008;
              break;
            } // case 34
            case 42: {
              curUnionHp_ = input.readBytes();
              bitField0_ |= 0x00000010;
              break;
            } // case 42
            case 50: {
              curDefenderHp_ = input.readBytes();
              bitField0_ |= 0x00000020;
              break;
            } // case 50
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private xddq.pb.UnionAreaWarPlayerInfo playerInfo_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.UnionAreaWarPlayerInfo, xddq.pb.UnionAreaWarPlayerInfo.Builder, xddq.pb.UnionAreaWarPlayerInfoOrBuilder> playerInfoBuilder_;
    /**
     * <code>optional .xddq.pb.UnionAreaWarPlayerInfo playerInfo = 1;</code>
     * @return Whether the playerInfo field is set.
     */
    public boolean hasPlayerInfo() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .xddq.pb.UnionAreaWarPlayerInfo playerInfo = 1;</code>
     * @return The playerInfo.
     */
    public xddq.pb.UnionAreaWarPlayerInfo getPlayerInfo() {
      if (playerInfoBuilder_ == null) {
        return playerInfo_ == null ? xddq.pb.UnionAreaWarPlayerInfo.getDefaultInstance() : playerInfo_;
      } else {
        return playerInfoBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.UnionAreaWarPlayerInfo playerInfo = 1;</code>
     */
    public Builder setPlayerInfo(xddq.pb.UnionAreaWarPlayerInfo value) {
      if (playerInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        playerInfo_ = value;
      } else {
        playerInfoBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.UnionAreaWarPlayerInfo playerInfo = 1;</code>
     */
    public Builder setPlayerInfo(
        xddq.pb.UnionAreaWarPlayerInfo.Builder builderForValue) {
      if (playerInfoBuilder_ == null) {
        playerInfo_ = builderForValue.build();
      } else {
        playerInfoBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.UnionAreaWarPlayerInfo playerInfo = 1;</code>
     */
    public Builder mergePlayerInfo(xddq.pb.UnionAreaWarPlayerInfo value) {
      if (playerInfoBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0) &&
          playerInfo_ != null &&
          playerInfo_ != xddq.pb.UnionAreaWarPlayerInfo.getDefaultInstance()) {
          getPlayerInfoBuilder().mergeFrom(value);
        } else {
          playerInfo_ = value;
        }
      } else {
        playerInfoBuilder_.mergeFrom(value);
      }
      if (playerInfo_ != null) {
        bitField0_ |= 0x00000001;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.UnionAreaWarPlayerInfo playerInfo = 1;</code>
     */
    public Builder clearPlayerInfo() {
      bitField0_ = (bitField0_ & ~0x00000001);
      playerInfo_ = null;
      if (playerInfoBuilder_ != null) {
        playerInfoBuilder_.dispose();
        playerInfoBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.UnionAreaWarPlayerInfo playerInfo = 1;</code>
     */
    public xddq.pb.UnionAreaWarPlayerInfo.Builder getPlayerInfoBuilder() {
      bitField0_ |= 0x00000001;
      onChanged();
      return internalGetPlayerInfoFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.UnionAreaWarPlayerInfo playerInfo = 1;</code>
     */
    public xddq.pb.UnionAreaWarPlayerInfoOrBuilder getPlayerInfoOrBuilder() {
      if (playerInfoBuilder_ != null) {
        return playerInfoBuilder_.getMessageOrBuilder();
      } else {
        return playerInfo_ == null ?
            xddq.pb.UnionAreaWarPlayerInfo.getDefaultInstance() : playerInfo_;
      }
    }
    /**
     * <code>optional .xddq.pb.UnionAreaWarPlayerInfo playerInfo = 1;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.UnionAreaWarPlayerInfo, xddq.pb.UnionAreaWarPlayerInfo.Builder, xddq.pb.UnionAreaWarPlayerInfoOrBuilder> 
        internalGetPlayerInfoFieldBuilder() {
      if (playerInfoBuilder_ == null) {
        playerInfoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.UnionAreaWarPlayerInfo, xddq.pb.UnionAreaWarPlayerInfo.Builder, xddq.pb.UnionAreaWarPlayerInfoOrBuilder>(
                getPlayerInfo(),
                getParentForChildren(),
                isClean());
        playerInfo_ = null;
      }
      return playerInfoBuilder_;
    }

    private long defendUnionId_ ;
    /**
     * <code>optional int64 defendUnionId = 2;</code>
     * @return Whether the defendUnionId field is set.
     */
    @java.lang.Override
    public boolean hasDefendUnionId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int64 defendUnionId = 2;</code>
     * @return The defendUnionId.
     */
    @java.lang.Override
    public long getDefendUnionId() {
      return defendUnionId_;
    }
    /**
     * <code>optional int64 defendUnionId = 2;</code>
     * @param value The defendUnionId to set.
     * @return This builder for chaining.
     */
    public Builder setDefendUnionId(long value) {

      defendUnionId_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 defendUnionId = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearDefendUnionId() {
      bitField0_ = (bitField0_ & ~0x00000002);
      defendUnionId_ = 0L;
      onChanged();
      return this;
    }

    private xddq.pb.UnionAreaInfo areaInfo_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.UnionAreaInfo, xddq.pb.UnionAreaInfo.Builder, xddq.pb.UnionAreaInfoOrBuilder> areaInfoBuilder_;
    /**
     * <code>optional .xddq.pb.UnionAreaInfo areaInfo = 3;</code>
     * @return Whether the areaInfo field is set.
     */
    public boolean hasAreaInfo() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional .xddq.pb.UnionAreaInfo areaInfo = 3;</code>
     * @return The areaInfo.
     */
    public xddq.pb.UnionAreaInfo getAreaInfo() {
      if (areaInfoBuilder_ == null) {
        return areaInfo_ == null ? xddq.pb.UnionAreaInfo.getDefaultInstance() : areaInfo_;
      } else {
        return areaInfoBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.UnionAreaInfo areaInfo = 3;</code>
     */
    public Builder setAreaInfo(xddq.pb.UnionAreaInfo value) {
      if (areaInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        areaInfo_ = value;
      } else {
        areaInfoBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.UnionAreaInfo areaInfo = 3;</code>
     */
    public Builder setAreaInfo(
        xddq.pb.UnionAreaInfo.Builder builderForValue) {
      if (areaInfoBuilder_ == null) {
        areaInfo_ = builderForValue.build();
      } else {
        areaInfoBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.UnionAreaInfo areaInfo = 3;</code>
     */
    public Builder mergeAreaInfo(xddq.pb.UnionAreaInfo value) {
      if (areaInfoBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0) &&
          areaInfo_ != null &&
          areaInfo_ != xddq.pb.UnionAreaInfo.getDefaultInstance()) {
          getAreaInfoBuilder().mergeFrom(value);
        } else {
          areaInfo_ = value;
        }
      } else {
        areaInfoBuilder_.mergeFrom(value);
      }
      if (areaInfo_ != null) {
        bitField0_ |= 0x00000004;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.UnionAreaInfo areaInfo = 3;</code>
     */
    public Builder clearAreaInfo() {
      bitField0_ = (bitField0_ & ~0x00000004);
      areaInfo_ = null;
      if (areaInfoBuilder_ != null) {
        areaInfoBuilder_.dispose();
        areaInfoBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.UnionAreaInfo areaInfo = 3;</code>
     */
    public xddq.pb.UnionAreaInfo.Builder getAreaInfoBuilder() {
      bitField0_ |= 0x00000004;
      onChanged();
      return internalGetAreaInfoFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.UnionAreaInfo areaInfo = 3;</code>
     */
    public xddq.pb.UnionAreaInfoOrBuilder getAreaInfoOrBuilder() {
      if (areaInfoBuilder_ != null) {
        return areaInfoBuilder_.getMessageOrBuilder();
      } else {
        return areaInfo_ == null ?
            xddq.pb.UnionAreaInfo.getDefaultInstance() : areaInfo_;
      }
    }
    /**
     * <code>optional .xddq.pb.UnionAreaInfo areaInfo = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.UnionAreaInfo, xddq.pb.UnionAreaInfo.Builder, xddq.pb.UnionAreaInfoOrBuilder> 
        internalGetAreaInfoFieldBuilder() {
      if (areaInfoBuilder_ == null) {
        areaInfoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.UnionAreaInfo, xddq.pb.UnionAreaInfo.Builder, xddq.pb.UnionAreaInfoOrBuilder>(
                getAreaInfo(),
                getParentForChildren(),
                isClean());
        areaInfo_ = null;
      }
      return areaInfoBuilder_;
    }

    private xddq.pb.UnionAreaDefenderInfo defenderInfo_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.UnionAreaDefenderInfo, xddq.pb.UnionAreaDefenderInfo.Builder, xddq.pb.UnionAreaDefenderInfoOrBuilder> defenderInfoBuilder_;
    /**
     * <code>optional .xddq.pb.UnionAreaDefenderInfo defenderInfo = 4;</code>
     * @return Whether the defenderInfo field is set.
     */
    public boolean hasDefenderInfo() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional .xddq.pb.UnionAreaDefenderInfo defenderInfo = 4;</code>
     * @return The defenderInfo.
     */
    public xddq.pb.UnionAreaDefenderInfo getDefenderInfo() {
      if (defenderInfoBuilder_ == null) {
        return defenderInfo_ == null ? xddq.pb.UnionAreaDefenderInfo.getDefaultInstance() : defenderInfo_;
      } else {
        return defenderInfoBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.UnionAreaDefenderInfo defenderInfo = 4;</code>
     */
    public Builder setDefenderInfo(xddq.pb.UnionAreaDefenderInfo value) {
      if (defenderInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        defenderInfo_ = value;
      } else {
        defenderInfoBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.UnionAreaDefenderInfo defenderInfo = 4;</code>
     */
    public Builder setDefenderInfo(
        xddq.pb.UnionAreaDefenderInfo.Builder builderForValue) {
      if (defenderInfoBuilder_ == null) {
        defenderInfo_ = builderForValue.build();
      } else {
        defenderInfoBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.UnionAreaDefenderInfo defenderInfo = 4;</code>
     */
    public Builder mergeDefenderInfo(xddq.pb.UnionAreaDefenderInfo value) {
      if (defenderInfoBuilder_ == null) {
        if (((bitField0_ & 0x00000008) != 0) &&
          defenderInfo_ != null &&
          defenderInfo_ != xddq.pb.UnionAreaDefenderInfo.getDefaultInstance()) {
          getDefenderInfoBuilder().mergeFrom(value);
        } else {
          defenderInfo_ = value;
        }
      } else {
        defenderInfoBuilder_.mergeFrom(value);
      }
      if (defenderInfo_ != null) {
        bitField0_ |= 0x00000008;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.UnionAreaDefenderInfo defenderInfo = 4;</code>
     */
    public Builder clearDefenderInfo() {
      bitField0_ = (bitField0_ & ~0x00000008);
      defenderInfo_ = null;
      if (defenderInfoBuilder_ != null) {
        defenderInfoBuilder_.dispose();
        defenderInfoBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.UnionAreaDefenderInfo defenderInfo = 4;</code>
     */
    public xddq.pb.UnionAreaDefenderInfo.Builder getDefenderInfoBuilder() {
      bitField0_ |= 0x00000008;
      onChanged();
      return internalGetDefenderInfoFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.UnionAreaDefenderInfo defenderInfo = 4;</code>
     */
    public xddq.pb.UnionAreaDefenderInfoOrBuilder getDefenderInfoOrBuilder() {
      if (defenderInfoBuilder_ != null) {
        return defenderInfoBuilder_.getMessageOrBuilder();
      } else {
        return defenderInfo_ == null ?
            xddq.pb.UnionAreaDefenderInfo.getDefaultInstance() : defenderInfo_;
      }
    }
    /**
     * <code>optional .xddq.pb.UnionAreaDefenderInfo defenderInfo = 4;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.UnionAreaDefenderInfo, xddq.pb.UnionAreaDefenderInfo.Builder, xddq.pb.UnionAreaDefenderInfoOrBuilder> 
        internalGetDefenderInfoFieldBuilder() {
      if (defenderInfoBuilder_ == null) {
        defenderInfoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.UnionAreaDefenderInfo, xddq.pb.UnionAreaDefenderInfo.Builder, xddq.pb.UnionAreaDefenderInfoOrBuilder>(
                getDefenderInfo(),
                getParentForChildren(),
                isClean());
        defenderInfo_ = null;
      }
      return defenderInfoBuilder_;
    }

    private java.lang.Object curUnionHp_ = "";
    /**
     * <code>optional string curUnionHp = 5;</code>
     * @return Whether the curUnionHp field is set.
     */
    public boolean hasCurUnionHp() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional string curUnionHp = 5;</code>
     * @return The curUnionHp.
     */
    public java.lang.String getCurUnionHp() {
      java.lang.Object ref = curUnionHp_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          curUnionHp_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string curUnionHp = 5;</code>
     * @return The bytes for curUnionHp.
     */
    public com.google.protobuf.ByteString
        getCurUnionHpBytes() {
      java.lang.Object ref = curUnionHp_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        curUnionHp_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string curUnionHp = 5;</code>
     * @param value The curUnionHp to set.
     * @return This builder for chaining.
     */
    public Builder setCurUnionHp(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      curUnionHp_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional string curUnionHp = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearCurUnionHp() {
      curUnionHp_ = getDefaultInstance().getCurUnionHp();
      bitField0_ = (bitField0_ & ~0x00000010);
      onChanged();
      return this;
    }
    /**
     * <code>optional string curUnionHp = 5;</code>
     * @param value The bytes for curUnionHp to set.
     * @return This builder for chaining.
     */
    public Builder setCurUnionHpBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      curUnionHp_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }

    private java.lang.Object curDefenderHp_ = "";
    /**
     * <code>optional string curDefenderHp = 6;</code>
     * @return Whether the curDefenderHp field is set.
     */
    public boolean hasCurDefenderHp() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional string curDefenderHp = 6;</code>
     * @return The curDefenderHp.
     */
    public java.lang.String getCurDefenderHp() {
      java.lang.Object ref = curDefenderHp_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          curDefenderHp_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string curDefenderHp = 6;</code>
     * @return The bytes for curDefenderHp.
     */
    public com.google.protobuf.ByteString
        getCurDefenderHpBytes() {
      java.lang.Object ref = curDefenderHp_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        curDefenderHp_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string curDefenderHp = 6;</code>
     * @param value The curDefenderHp to set.
     * @return This builder for chaining.
     */
    public Builder setCurDefenderHp(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      curDefenderHp_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>optional string curDefenderHp = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearCurDefenderHp() {
      curDefenderHp_ = getDefaultInstance().getCurDefenderHp();
      bitField0_ = (bitField0_ & ~0x00000020);
      onChanged();
      return this;
    }
    /**
     * <code>optional string curDefenderHp = 6;</code>
     * @param value The bytes for curDefenderHp to set.
     * @return This builder for chaining.
     */
    public Builder setCurDefenderHpBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      curDefenderHp_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.UnionAreaWarSceneData)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.UnionAreaWarSceneData)
  private static final xddq.pb.UnionAreaWarSceneData DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.UnionAreaWarSceneData();
  }

  public static xddq.pb.UnionAreaWarSceneData getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<UnionAreaWarSceneData>
      PARSER = new com.google.protobuf.AbstractParser<UnionAreaWarSceneData>() {
    @java.lang.Override
    public UnionAreaWarSceneData parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<UnionAreaWarSceneData> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<UnionAreaWarSceneData> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.UnionAreaWarSceneData getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

