// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

public interface PlanesTrialRankTeamEntityOrBuilder extends
    // @@protoc_insertion_point(interface_extends:xddq.pb.PlanesTrialRankTeamEntity)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>required int64 id = 1;</code>
   * @return Whether the id field is set.
   */
  boolean hasId();
  /**
   * <code>required int64 id = 1;</code>
   * @return The id.
   */
  long getId();

  /**
   * <code>optional string teamName = 2;</code>
   * @return Whether the teamName field is set.
   */
  boolean hasTeamName();
  /**
   * <code>optional string teamName = 2;</code>
   * @return The teamName.
   */
  java.lang.String getTeamName();
  /**
   * <code>optional string teamName = 2;</code>
   * @return The bytes for teamName.
   */
  com.google.protobuf.ByteString
      getTeamNameBytes();

  /**
   * <code>optional int64 leaderServerId = 3;</code>
   * @return Whether the leaderServerId field is set.
   */
  boolean hasLeaderServerId();
  /**
   * <code>optional int64 leaderServerId = 3;</code>
   * @return The leaderServerId.
   */
  long getLeaderServerId();

  /**
   * <code>repeated .xddq.pb.PlayerCharacterImageMsg members = 4;</code>
   */
  java.util.List<xddq.pb.PlayerCharacterImageMsg> 
      getMembersList();
  /**
   * <code>repeated .xddq.pb.PlayerCharacterImageMsg members = 4;</code>
   */
  xddq.pb.PlayerCharacterImageMsg getMembers(int index);
  /**
   * <code>repeated .xddq.pb.PlayerCharacterImageMsg members = 4;</code>
   */
  int getMembersCount();
  /**
   * <code>repeated .xddq.pb.PlayerCharacterImageMsg members = 4;</code>
   */
  java.util.List<? extends xddq.pb.PlayerCharacterImageMsgOrBuilder> 
      getMembersOrBuilderList();
  /**
   * <code>repeated .xddq.pb.PlayerCharacterImageMsg members = 4;</code>
   */
  xddq.pb.PlayerCharacterImageMsgOrBuilder getMembersOrBuilder(
      int index);

  /**
   * <code>optional int64 battleTime = 5;</code>
   * @return Whether the battleTime field is set.
   */
  boolean hasBattleTime();
  /**
   * <code>optional int64 battleTime = 5;</code>
   * @return The battleTime.
   */
  long getBattleTime();

  /**
   * <code>optional string fightValue = 6;</code>
   * @return Whether the fightValue field is set.
   */
  boolean hasFightValue();
  /**
   * <code>optional string fightValue = 6;</code>
   * @return The fightValue.
   */
  java.lang.String getFightValue();
  /**
   * <code>optional string fightValue = 6;</code>
   * @return The bytes for fightValue.
   */
  com.google.protobuf.ByteString
      getFightValueBytes();

  /**
   * <code>optional int32 stageId = 7;</code>
   * @return Whether the stageId field is set.
   */
  boolean hasStageId();
  /**
   * <code>optional int32 stageId = 7;</code>
   * @return The stageId.
   */
  int getStageId();

  /**
   * <code>repeated int64 robotPlayerId = 8;</code>
   * @return A list containing the robotPlayerId.
   */
  java.util.List<java.lang.Long> getRobotPlayerIdList();
  /**
   * <code>repeated int64 robotPlayerId = 8;</code>
   * @return The count of robotPlayerId.
   */
  int getRobotPlayerIdCount();
  /**
   * <code>repeated int64 robotPlayerId = 8;</code>
   * @param index The index of the element to return.
   * @return The robotPlayerId at the given index.
   */
  long getRobotPlayerId(int index);
}
