// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.GodDemonRealsMatchMsg}
 */
public final class GodDemonRealsMatchMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.GodDemonRealsMatchMsg)
    GodDemonRealsMatchMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      GodDemonRealsMatchMsg.class.getName());
  }
  // Use GodDemonRealsMatchMsg.newBuilder() to construct.
  private GodDemonRealsMatchMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private GodDemonRealsMatchMsg() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_GodDemonRealsMatchMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_GodDemonRealsMatchMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.GodDemonRealsMatchMsg.class, xddq.pb.GodDemonRealsMatchMsg.Builder.class);
  }

  private int bitField0_;
  public static final int ID_FIELD_NUMBER = 1;
  private int id_ = 0;
  /**
   * <code>required int32 id = 1;</code>
   * @return Whether the id field is set.
   */
  @java.lang.Override
  public boolean hasId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 id = 1;</code>
   * @return The id.
   */
  @java.lang.Override
  public int getId() {
    return id_;
  }

  public static final int MINLV_FIELD_NUMBER = 2;
  private int minLv_ = 0;
  /**
   * <code>required int32 minLv = 2;</code>
   * @return Whether the minLv field is set.
   */
  @java.lang.Override
  public boolean hasMinLv() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>required int32 minLv = 2;</code>
   * @return The minLv.
   */
  @java.lang.Override
  public int getMinLv() {
    return minLv_;
  }

  public static final int MAXLV_FIELD_NUMBER = 3;
  private int maxLv_ = 0;
  /**
   * <code>required int32 maxLv = 3;</code>
   * @return Whether the maxLv field is set.
   */
  @java.lang.Override
  public boolean hasMaxLv() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>required int32 maxLv = 3;</code>
   * @return The maxLv.
   */
  @java.lang.Override
  public int getMaxLv() {
    return maxLv_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasId()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasMinLv()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasMaxLv()) {
      memoizedIsInitialized = 0;
      return false;
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, id_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(2, minLv_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt32(3, maxLv_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, id_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, minLv_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(3, maxLv_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.GodDemonRealsMatchMsg)) {
      return super.equals(obj);
    }
    xddq.pb.GodDemonRealsMatchMsg other = (xddq.pb.GodDemonRealsMatchMsg) obj;

    if (hasId() != other.hasId()) return false;
    if (hasId()) {
      if (getId()
          != other.getId()) return false;
    }
    if (hasMinLv() != other.hasMinLv()) return false;
    if (hasMinLv()) {
      if (getMinLv()
          != other.getMinLv()) return false;
    }
    if (hasMaxLv() != other.hasMaxLv()) return false;
    if (hasMaxLv()) {
      if (getMaxLv()
          != other.getMaxLv()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasId()) {
      hash = (37 * hash) + ID_FIELD_NUMBER;
      hash = (53 * hash) + getId();
    }
    if (hasMinLv()) {
      hash = (37 * hash) + MINLV_FIELD_NUMBER;
      hash = (53 * hash) + getMinLv();
    }
    if (hasMaxLv()) {
      hash = (37 * hash) + MAXLV_FIELD_NUMBER;
      hash = (53 * hash) + getMaxLv();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.GodDemonRealsMatchMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GodDemonRealsMatchMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GodDemonRealsMatchMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GodDemonRealsMatchMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GodDemonRealsMatchMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.GodDemonRealsMatchMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.GodDemonRealsMatchMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.GodDemonRealsMatchMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.GodDemonRealsMatchMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.GodDemonRealsMatchMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.GodDemonRealsMatchMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.GodDemonRealsMatchMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.GodDemonRealsMatchMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.GodDemonRealsMatchMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.GodDemonRealsMatchMsg)
      xddq.pb.GodDemonRealsMatchMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GodDemonRealsMatchMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GodDemonRealsMatchMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.GodDemonRealsMatchMsg.class, xddq.pb.GodDemonRealsMatchMsg.Builder.class);
    }

    // Construct using xddq.pb.GodDemonRealsMatchMsg.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      id_ = 0;
      minLv_ = 0;
      maxLv_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_GodDemonRealsMatchMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.GodDemonRealsMatchMsg getDefaultInstanceForType() {
      return xddq.pb.GodDemonRealsMatchMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.GodDemonRealsMatchMsg build() {
      xddq.pb.GodDemonRealsMatchMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.GodDemonRealsMatchMsg buildPartial() {
      xddq.pb.GodDemonRealsMatchMsg result = new xddq.pb.GodDemonRealsMatchMsg(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.GodDemonRealsMatchMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.id_ = id_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.minLv_ = minLv_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.maxLv_ = maxLv_;
        to_bitField0_ |= 0x00000004;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.GodDemonRealsMatchMsg) {
        return mergeFrom((xddq.pb.GodDemonRealsMatchMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.GodDemonRealsMatchMsg other) {
      if (other == xddq.pb.GodDemonRealsMatchMsg.getDefaultInstance()) return this;
      if (other.hasId()) {
        setId(other.getId());
      }
      if (other.hasMinLv()) {
        setMinLv(other.getMinLv());
      }
      if (other.hasMaxLv()) {
        setMaxLv(other.getMaxLv());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasId()) {
        return false;
      }
      if (!hasMinLv()) {
        return false;
      }
      if (!hasMaxLv()) {
        return false;
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              id_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              minLv_ = input.readInt32();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 24: {
              maxLv_ = input.readInt32();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int id_ ;
    /**
     * <code>required int32 id = 1;</code>
     * @return Whether the id field is set.
     */
    @java.lang.Override
    public boolean hasId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 id = 1;</code>
     * @return The id.
     */
    @java.lang.Override
    public int getId() {
      return id_;
    }
    /**
     * <code>required int32 id = 1;</code>
     * @param value The id to set.
     * @return This builder for chaining.
     */
    public Builder setId(int value) {

      id_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 id = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      id_ = 0;
      onChanged();
      return this;
    }

    private int minLv_ ;
    /**
     * <code>required int32 minLv = 2;</code>
     * @return Whether the minLv field is set.
     */
    @java.lang.Override
    public boolean hasMinLv() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>required int32 minLv = 2;</code>
     * @return The minLv.
     */
    @java.lang.Override
    public int getMinLv() {
      return minLv_;
    }
    /**
     * <code>required int32 minLv = 2;</code>
     * @param value The minLv to set.
     * @return This builder for chaining.
     */
    public Builder setMinLv(int value) {

      minLv_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 minLv = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearMinLv() {
      bitField0_ = (bitField0_ & ~0x00000002);
      minLv_ = 0;
      onChanged();
      return this;
    }

    private int maxLv_ ;
    /**
     * <code>required int32 maxLv = 3;</code>
     * @return Whether the maxLv field is set.
     */
    @java.lang.Override
    public boolean hasMaxLv() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>required int32 maxLv = 3;</code>
     * @return The maxLv.
     */
    @java.lang.Override
    public int getMaxLv() {
      return maxLv_;
    }
    /**
     * <code>required int32 maxLv = 3;</code>
     * @param value The maxLv to set.
     * @return This builder for chaining.
     */
    public Builder setMaxLv(int value) {

      maxLv_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 maxLv = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearMaxLv() {
      bitField0_ = (bitField0_ & ~0x00000004);
      maxLv_ = 0;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.GodDemonRealsMatchMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.GodDemonRealsMatchMsg)
  private static final xddq.pb.GodDemonRealsMatchMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.GodDemonRealsMatchMsg();
  }

  public static xddq.pb.GodDemonRealsMatchMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<GodDemonRealsMatchMsg>
      PARSER = new com.google.protobuf.AbstractParser<GodDemonRealsMatchMsg>() {
    @java.lang.Override
    public GodDemonRealsMatchMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<GodDemonRealsMatchMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<GodDemonRealsMatchMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.GodDemonRealsMatchMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

