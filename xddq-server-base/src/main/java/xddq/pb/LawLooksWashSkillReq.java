// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.LawLooksWashSkillReq}
 */
public final class LawLooksWashSkillReq extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.LawLooksWashSkillReq)
    LawLooksWashSkillReqOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      LawLooksWashSkillReq.class.getName());
  }
  // Use LawLooksWashSkillReq.newBuilder() to construct.
  private LawLooksWashSkillReq(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private LawLooksWashSkillReq() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_LawLooksWashSkillReq_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_LawLooksWashSkillReq_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.LawLooksWashSkillReq.class, xddq.pb.LawLooksWashSkillReq.Builder.class);
  }

  private int bitField0_;
  public static final int LAWLOOKSID_FIELD_NUMBER = 1;
  private int lawLooksId_ = 0;
  /**
   * <code>required int32 lawLooksId = 1;</code>
   * @return Whether the lawLooksId field is set.
   */
  @java.lang.Override
  public boolean hasLawLooksId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 lawLooksId = 1;</code>
   * @return The lawLooksId.
   */
  @java.lang.Override
  public int getLawLooksId() {
    return lawLooksId_;
  }

  public static final int SKILLINDEX_FIELD_NUMBER = 2;
  private int skillIndex_ = 0;
  /**
   * <code>required int32 skillIndex = 2;</code>
   * @return Whether the skillIndex field is set.
   */
  @java.lang.Override
  public boolean hasSkillIndex() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>required int32 skillIndex = 2;</code>
   * @return The skillIndex.
   */
  @java.lang.Override
  public int getSkillIndex() {
    return skillIndex_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasLawLooksId()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasSkillIndex()) {
      memoizedIsInitialized = 0;
      return false;
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, lawLooksId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(2, skillIndex_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, lawLooksId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, skillIndex_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.LawLooksWashSkillReq)) {
      return super.equals(obj);
    }
    xddq.pb.LawLooksWashSkillReq other = (xddq.pb.LawLooksWashSkillReq) obj;

    if (hasLawLooksId() != other.hasLawLooksId()) return false;
    if (hasLawLooksId()) {
      if (getLawLooksId()
          != other.getLawLooksId()) return false;
    }
    if (hasSkillIndex() != other.hasSkillIndex()) return false;
    if (hasSkillIndex()) {
      if (getSkillIndex()
          != other.getSkillIndex()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasLawLooksId()) {
      hash = (37 * hash) + LAWLOOKSID_FIELD_NUMBER;
      hash = (53 * hash) + getLawLooksId();
    }
    if (hasSkillIndex()) {
      hash = (37 * hash) + SKILLINDEX_FIELD_NUMBER;
      hash = (53 * hash) + getSkillIndex();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.LawLooksWashSkillReq parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.LawLooksWashSkillReq parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.LawLooksWashSkillReq parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.LawLooksWashSkillReq parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.LawLooksWashSkillReq parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.LawLooksWashSkillReq parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.LawLooksWashSkillReq parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.LawLooksWashSkillReq parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.LawLooksWashSkillReq parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.LawLooksWashSkillReq parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.LawLooksWashSkillReq parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.LawLooksWashSkillReq parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.LawLooksWashSkillReq prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.LawLooksWashSkillReq}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.LawLooksWashSkillReq)
      xddq.pb.LawLooksWashSkillReqOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_LawLooksWashSkillReq_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_LawLooksWashSkillReq_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.LawLooksWashSkillReq.class, xddq.pb.LawLooksWashSkillReq.Builder.class);
    }

    // Construct using xddq.pb.LawLooksWashSkillReq.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      lawLooksId_ = 0;
      skillIndex_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_LawLooksWashSkillReq_descriptor;
    }

    @java.lang.Override
    public xddq.pb.LawLooksWashSkillReq getDefaultInstanceForType() {
      return xddq.pb.LawLooksWashSkillReq.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.LawLooksWashSkillReq build() {
      xddq.pb.LawLooksWashSkillReq result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.LawLooksWashSkillReq buildPartial() {
      xddq.pb.LawLooksWashSkillReq result = new xddq.pb.LawLooksWashSkillReq(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.LawLooksWashSkillReq result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.lawLooksId_ = lawLooksId_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.skillIndex_ = skillIndex_;
        to_bitField0_ |= 0x00000002;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.LawLooksWashSkillReq) {
        return mergeFrom((xddq.pb.LawLooksWashSkillReq)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.LawLooksWashSkillReq other) {
      if (other == xddq.pb.LawLooksWashSkillReq.getDefaultInstance()) return this;
      if (other.hasLawLooksId()) {
        setLawLooksId(other.getLawLooksId());
      }
      if (other.hasSkillIndex()) {
        setSkillIndex(other.getSkillIndex());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasLawLooksId()) {
        return false;
      }
      if (!hasSkillIndex()) {
        return false;
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              lawLooksId_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              skillIndex_ = input.readInt32();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int lawLooksId_ ;
    /**
     * <code>required int32 lawLooksId = 1;</code>
     * @return Whether the lawLooksId field is set.
     */
    @java.lang.Override
    public boolean hasLawLooksId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 lawLooksId = 1;</code>
     * @return The lawLooksId.
     */
    @java.lang.Override
    public int getLawLooksId() {
      return lawLooksId_;
    }
    /**
     * <code>required int32 lawLooksId = 1;</code>
     * @param value The lawLooksId to set.
     * @return This builder for chaining.
     */
    public Builder setLawLooksId(int value) {

      lawLooksId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 lawLooksId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearLawLooksId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      lawLooksId_ = 0;
      onChanged();
      return this;
    }

    private int skillIndex_ ;
    /**
     * <code>required int32 skillIndex = 2;</code>
     * @return Whether the skillIndex field is set.
     */
    @java.lang.Override
    public boolean hasSkillIndex() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>required int32 skillIndex = 2;</code>
     * @return The skillIndex.
     */
    @java.lang.Override
    public int getSkillIndex() {
      return skillIndex_;
    }
    /**
     * <code>required int32 skillIndex = 2;</code>
     * @param value The skillIndex to set.
     * @return This builder for chaining.
     */
    public Builder setSkillIndex(int value) {

      skillIndex_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 skillIndex = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearSkillIndex() {
      bitField0_ = (bitField0_ & ~0x00000002);
      skillIndex_ = 0;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.LawLooksWashSkillReq)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.LawLooksWashSkillReq)
  private static final xddq.pb.LawLooksWashSkillReq DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.LawLooksWashSkillReq();
  }

  public static xddq.pb.LawLooksWashSkillReq getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<LawLooksWashSkillReq>
      PARSER = new com.google.protobuf.AbstractParser<LawLooksWashSkillReq>() {
    @java.lang.Override
    public LawLooksWashSkillReq parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<LawLooksWashSkillReq> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<LawLooksWashSkillReq> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.LawLooksWashSkillReq getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

