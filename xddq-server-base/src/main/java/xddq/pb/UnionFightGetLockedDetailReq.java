// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.UnionFightGetLockedDetailReq}
 */
public final class UnionFightGetLockedDetailReq extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.UnionFightGetLockedDetailReq)
    UnionFightGetLockedDetailReqOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      UnionFightGetLockedDetailReq.class.getName());
  }
  // Use UnionFightGetLockedDetailReq.newBuilder() to construct.
  private UnionFightGetLockedDetailReq(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private UnionFightGetLockedDetailReq() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_UnionFightGetLockedDetailReq_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_UnionFightGetLockedDetailReq_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.UnionFightGetLockedDetailReq.class, xddq.pb.UnionFightGetLockedDetailReq.Builder.class);
  }

  private int bitField0_;
  public static final int JUSTGETLOCK_FIELD_NUMBER = 1;
  private int justGetLock_ = 0;
  /**
   * <code>optional int32 justGetLock = 1;</code>
   * @return Whether the justGetLock field is set.
   */
  @java.lang.Override
  public boolean hasJustGetLock() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int32 justGetLock = 1;</code>
   * @return The justGetLock.
   */
  @java.lang.Override
  public int getJustGetLock() {
    return justGetLock_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, justGetLock_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, justGetLock_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.UnionFightGetLockedDetailReq)) {
      return super.equals(obj);
    }
    xddq.pb.UnionFightGetLockedDetailReq other = (xddq.pb.UnionFightGetLockedDetailReq) obj;

    if (hasJustGetLock() != other.hasJustGetLock()) return false;
    if (hasJustGetLock()) {
      if (getJustGetLock()
          != other.getJustGetLock()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasJustGetLock()) {
      hash = (37 * hash) + JUSTGETLOCK_FIELD_NUMBER;
      hash = (53 * hash) + getJustGetLock();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.UnionFightGetLockedDetailReq parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.UnionFightGetLockedDetailReq parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.UnionFightGetLockedDetailReq parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.UnionFightGetLockedDetailReq parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.UnionFightGetLockedDetailReq parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.UnionFightGetLockedDetailReq parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.UnionFightGetLockedDetailReq parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.UnionFightGetLockedDetailReq parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.UnionFightGetLockedDetailReq parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.UnionFightGetLockedDetailReq parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.UnionFightGetLockedDetailReq parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.UnionFightGetLockedDetailReq parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.UnionFightGetLockedDetailReq prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.UnionFightGetLockedDetailReq}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.UnionFightGetLockedDetailReq)
      xddq.pb.UnionFightGetLockedDetailReqOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_UnionFightGetLockedDetailReq_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_UnionFightGetLockedDetailReq_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.UnionFightGetLockedDetailReq.class, xddq.pb.UnionFightGetLockedDetailReq.Builder.class);
    }

    // Construct using xddq.pb.UnionFightGetLockedDetailReq.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      justGetLock_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_UnionFightGetLockedDetailReq_descriptor;
    }

    @java.lang.Override
    public xddq.pb.UnionFightGetLockedDetailReq getDefaultInstanceForType() {
      return xddq.pb.UnionFightGetLockedDetailReq.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.UnionFightGetLockedDetailReq build() {
      xddq.pb.UnionFightGetLockedDetailReq result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.UnionFightGetLockedDetailReq buildPartial() {
      xddq.pb.UnionFightGetLockedDetailReq result = new xddq.pb.UnionFightGetLockedDetailReq(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.UnionFightGetLockedDetailReq result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.justGetLock_ = justGetLock_;
        to_bitField0_ |= 0x00000001;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.UnionFightGetLockedDetailReq) {
        return mergeFrom((xddq.pb.UnionFightGetLockedDetailReq)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.UnionFightGetLockedDetailReq other) {
      if (other == xddq.pb.UnionFightGetLockedDetailReq.getDefaultInstance()) return this;
      if (other.hasJustGetLock()) {
        setJustGetLock(other.getJustGetLock());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              justGetLock_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int justGetLock_ ;
    /**
     * <code>optional int32 justGetLock = 1;</code>
     * @return Whether the justGetLock field is set.
     */
    @java.lang.Override
    public boolean hasJustGetLock() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 justGetLock = 1;</code>
     * @return The justGetLock.
     */
    @java.lang.Override
    public int getJustGetLock() {
      return justGetLock_;
    }
    /**
     * <code>optional int32 justGetLock = 1;</code>
     * @param value The justGetLock to set.
     * @return This builder for chaining.
     */
    public Builder setJustGetLock(int value) {

      justGetLock_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 justGetLock = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearJustGetLock() {
      bitField0_ = (bitField0_ & ~0x00000001);
      justGetLock_ = 0;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.UnionFightGetLockedDetailReq)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.UnionFightGetLockedDetailReq)
  private static final xddq.pb.UnionFightGetLockedDetailReq DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.UnionFightGetLockedDetailReq();
  }

  public static xddq.pb.UnionFightGetLockedDetailReq getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<UnionFightGetLockedDetailReq>
      PARSER = new com.google.protobuf.AbstractParser<UnionFightGetLockedDetailReq>() {
    @java.lang.Override
    public UnionFightGetLockedDetailReq parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<UnionFightGetLockedDetailReq> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<UnionFightGetLockedDetailReq> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.UnionFightGetLockedDetailReq getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

