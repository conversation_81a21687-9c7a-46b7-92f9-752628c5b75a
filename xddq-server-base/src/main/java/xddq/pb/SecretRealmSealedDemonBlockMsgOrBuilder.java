// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

public interface SecretRealmSealedDemonBlockMsgOrBuilder extends
    // @@protoc_insertion_point(interface_extends:xddq.pb.SecretRealmSealedDemonBlockMsg)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>required int32 x = 1;</code>
   * @return Whether the x field is set.
   */
  boolean hasX();
  /**
   * <code>required int32 x = 1;</code>
   * @return The x.
   */
  int getX();

  /**
   * <code>required int32 y = 2;</code>
   * @return Whether the y field is set.
   */
  boolean hasY();
  /**
   * <code>required int32 y = 2;</code>
   * @return The y.
   */
  int getY();

  /**
   * <code>required int32 c = 3;</code>
   * @return Whether the c field is set.
   */
  boolean hasC();
  /**
   * <code>required int32 c = 3;</code>
   * @return The c.
   */
  int getC();

  /**
   * <code>optional int32 damage = 4;</code>
   * @return Whether the damage field is set.
   */
  boolean hasDamage();
  /**
   * <code>optional int32 damage = 4;</code>
   * @return The damage.
   */
  int getDamage();
}
