// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.HolyLandPlayerSimpleInfo}
 */
public final class HolyLandPlayerSimpleInfo extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.HolyLandPlayerSimpleInfo)
    HolyLandPlayerSimpleInfoOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      HolyLandPlayerSimpleInfo.class.getName());
  }
  // Use HolyLandPlayerSimpleInfo.newBuilder() to construct.
  private HolyLandPlayerSimpleInfo(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private HolyLandPlayerSimpleInfo() {
    nickname_ = "";
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_HolyLandPlayerSimpleInfo_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_HolyLandPlayerSimpleInfo_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.HolyLandPlayerSimpleInfo.class, xddq.pb.HolyLandPlayerSimpleInfo.Builder.class);
  }

  private int bitField0_;
  public static final int HEADINFO_FIELD_NUMBER = 1;
  private xddq.pb.PlayerHeadDataMsg headInfo_;
  /**
   * <code>optional .xddq.pb.PlayerHeadDataMsg headInfo = 1;</code>
   * @return Whether the headInfo field is set.
   */
  @java.lang.Override
  public boolean hasHeadInfo() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional .xddq.pb.PlayerHeadDataMsg headInfo = 1;</code>
   * @return The headInfo.
   */
  @java.lang.Override
  public xddq.pb.PlayerHeadDataMsg getHeadInfo() {
    return headInfo_ == null ? xddq.pb.PlayerHeadDataMsg.getDefaultInstance() : headInfo_;
  }
  /**
   * <code>optional .xddq.pb.PlayerHeadDataMsg headInfo = 1;</code>
   */
  @java.lang.Override
  public xddq.pb.PlayerHeadDataMsgOrBuilder getHeadInfoOrBuilder() {
    return headInfo_ == null ? xddq.pb.PlayerHeadDataMsg.getDefaultInstance() : headInfo_;
  }

  public static final int NICKNAME_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private volatile java.lang.Object nickname_ = "";
  /**
   * <code>optional string nickname = 2;</code>
   * @return Whether the nickname field is set.
   */
  @java.lang.Override
  public boolean hasNickname() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional string nickname = 2;</code>
   * @return The nickname.
   */
  @java.lang.Override
  public java.lang.String getNickname() {
    java.lang.Object ref = nickname_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        nickname_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string nickname = 2;</code>
   * @return The bytes for nickname.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getNicknameBytes() {
    java.lang.Object ref = nickname_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      nickname_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeMessage(1, getHeadInfo());
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 2, nickname_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, getHeadInfo());
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(2, nickname_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.HolyLandPlayerSimpleInfo)) {
      return super.equals(obj);
    }
    xddq.pb.HolyLandPlayerSimpleInfo other = (xddq.pb.HolyLandPlayerSimpleInfo) obj;

    if (hasHeadInfo() != other.hasHeadInfo()) return false;
    if (hasHeadInfo()) {
      if (!getHeadInfo()
          .equals(other.getHeadInfo())) return false;
    }
    if (hasNickname() != other.hasNickname()) return false;
    if (hasNickname()) {
      if (!getNickname()
          .equals(other.getNickname())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasHeadInfo()) {
      hash = (37 * hash) + HEADINFO_FIELD_NUMBER;
      hash = (53 * hash) + getHeadInfo().hashCode();
    }
    if (hasNickname()) {
      hash = (37 * hash) + NICKNAME_FIELD_NUMBER;
      hash = (53 * hash) + getNickname().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.HolyLandPlayerSimpleInfo parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.HolyLandPlayerSimpleInfo parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.HolyLandPlayerSimpleInfo parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.HolyLandPlayerSimpleInfo parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.HolyLandPlayerSimpleInfo parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.HolyLandPlayerSimpleInfo parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.HolyLandPlayerSimpleInfo parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.HolyLandPlayerSimpleInfo parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.HolyLandPlayerSimpleInfo parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.HolyLandPlayerSimpleInfo parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.HolyLandPlayerSimpleInfo parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.HolyLandPlayerSimpleInfo parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.HolyLandPlayerSimpleInfo prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.HolyLandPlayerSimpleInfo}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.HolyLandPlayerSimpleInfo)
      xddq.pb.HolyLandPlayerSimpleInfoOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_HolyLandPlayerSimpleInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_HolyLandPlayerSimpleInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.HolyLandPlayerSimpleInfo.class, xddq.pb.HolyLandPlayerSimpleInfo.Builder.class);
    }

    // Construct using xddq.pb.HolyLandPlayerSimpleInfo.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetHeadInfoFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      headInfo_ = null;
      if (headInfoBuilder_ != null) {
        headInfoBuilder_.dispose();
        headInfoBuilder_ = null;
      }
      nickname_ = "";
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_HolyLandPlayerSimpleInfo_descriptor;
    }

    @java.lang.Override
    public xddq.pb.HolyLandPlayerSimpleInfo getDefaultInstanceForType() {
      return xddq.pb.HolyLandPlayerSimpleInfo.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.HolyLandPlayerSimpleInfo build() {
      xddq.pb.HolyLandPlayerSimpleInfo result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.HolyLandPlayerSimpleInfo buildPartial() {
      xddq.pb.HolyLandPlayerSimpleInfo result = new xddq.pb.HolyLandPlayerSimpleInfo(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.HolyLandPlayerSimpleInfo result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.headInfo_ = headInfoBuilder_ == null
            ? headInfo_
            : headInfoBuilder_.build();
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.nickname_ = nickname_;
        to_bitField0_ |= 0x00000002;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.HolyLandPlayerSimpleInfo) {
        return mergeFrom((xddq.pb.HolyLandPlayerSimpleInfo)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.HolyLandPlayerSimpleInfo other) {
      if (other == xddq.pb.HolyLandPlayerSimpleInfo.getDefaultInstance()) return this;
      if (other.hasHeadInfo()) {
        mergeHeadInfo(other.getHeadInfo());
      }
      if (other.hasNickname()) {
        nickname_ = other.nickname_;
        bitField0_ |= 0x00000002;
        onChanged();
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              input.readMessage(
                  internalGetHeadInfoFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 18: {
              nickname_ = input.readBytes();
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private xddq.pb.PlayerHeadDataMsg headInfo_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PlayerHeadDataMsg, xddq.pb.PlayerHeadDataMsg.Builder, xddq.pb.PlayerHeadDataMsgOrBuilder> headInfoBuilder_;
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg headInfo = 1;</code>
     * @return Whether the headInfo field is set.
     */
    public boolean hasHeadInfo() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg headInfo = 1;</code>
     * @return The headInfo.
     */
    public xddq.pb.PlayerHeadDataMsg getHeadInfo() {
      if (headInfoBuilder_ == null) {
        return headInfo_ == null ? xddq.pb.PlayerHeadDataMsg.getDefaultInstance() : headInfo_;
      } else {
        return headInfoBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg headInfo = 1;</code>
     */
    public Builder setHeadInfo(xddq.pb.PlayerHeadDataMsg value) {
      if (headInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        headInfo_ = value;
      } else {
        headInfoBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg headInfo = 1;</code>
     */
    public Builder setHeadInfo(
        xddq.pb.PlayerHeadDataMsg.Builder builderForValue) {
      if (headInfoBuilder_ == null) {
        headInfo_ = builderForValue.build();
      } else {
        headInfoBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg headInfo = 1;</code>
     */
    public Builder mergeHeadInfo(xddq.pb.PlayerHeadDataMsg value) {
      if (headInfoBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0) &&
          headInfo_ != null &&
          headInfo_ != xddq.pb.PlayerHeadDataMsg.getDefaultInstance()) {
          getHeadInfoBuilder().mergeFrom(value);
        } else {
          headInfo_ = value;
        }
      } else {
        headInfoBuilder_.mergeFrom(value);
      }
      if (headInfo_ != null) {
        bitField0_ |= 0x00000001;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg headInfo = 1;</code>
     */
    public Builder clearHeadInfo() {
      bitField0_ = (bitField0_ & ~0x00000001);
      headInfo_ = null;
      if (headInfoBuilder_ != null) {
        headInfoBuilder_.dispose();
        headInfoBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg headInfo = 1;</code>
     */
    public xddq.pb.PlayerHeadDataMsg.Builder getHeadInfoBuilder() {
      bitField0_ |= 0x00000001;
      onChanged();
      return internalGetHeadInfoFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg headInfo = 1;</code>
     */
    public xddq.pb.PlayerHeadDataMsgOrBuilder getHeadInfoOrBuilder() {
      if (headInfoBuilder_ != null) {
        return headInfoBuilder_.getMessageOrBuilder();
      } else {
        return headInfo_ == null ?
            xddq.pb.PlayerHeadDataMsg.getDefaultInstance() : headInfo_;
      }
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadDataMsg headInfo = 1;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PlayerHeadDataMsg, xddq.pb.PlayerHeadDataMsg.Builder, xddq.pb.PlayerHeadDataMsgOrBuilder> 
        internalGetHeadInfoFieldBuilder() {
      if (headInfoBuilder_ == null) {
        headInfoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.PlayerHeadDataMsg, xddq.pb.PlayerHeadDataMsg.Builder, xddq.pb.PlayerHeadDataMsgOrBuilder>(
                getHeadInfo(),
                getParentForChildren(),
                isClean());
        headInfo_ = null;
      }
      return headInfoBuilder_;
    }

    private java.lang.Object nickname_ = "";
    /**
     * <code>optional string nickname = 2;</code>
     * @return Whether the nickname field is set.
     */
    public boolean hasNickname() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional string nickname = 2;</code>
     * @return The nickname.
     */
    public java.lang.String getNickname() {
      java.lang.Object ref = nickname_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          nickname_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string nickname = 2;</code>
     * @return The bytes for nickname.
     */
    public com.google.protobuf.ByteString
        getNicknameBytes() {
      java.lang.Object ref = nickname_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        nickname_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string nickname = 2;</code>
     * @param value The nickname to set.
     * @return This builder for chaining.
     */
    public Builder setNickname(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      nickname_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional string nickname = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearNickname() {
      nickname_ = getDefaultInstance().getNickname();
      bitField0_ = (bitField0_ & ~0x00000002);
      onChanged();
      return this;
    }
    /**
     * <code>optional string nickname = 2;</code>
     * @param value The bytes for nickname to set.
     * @return This builder for chaining.
     */
    public Builder setNicknameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      nickname_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.HolyLandPlayerSimpleInfo)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.HolyLandPlayerSimpleInfo)
  private static final xddq.pb.HolyLandPlayerSimpleInfo DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.HolyLandPlayerSimpleInfo();
  }

  public static xddq.pb.HolyLandPlayerSimpleInfo getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<HolyLandPlayerSimpleInfo>
      PARSER = new com.google.protobuf.AbstractParser<HolyLandPlayerSimpleInfo>() {
    @java.lang.Override
    public HolyLandPlayerSimpleInfo parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<HolyLandPlayerSimpleInfo> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<HolyLandPlayerSimpleInfo> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.HolyLandPlayerSimpleInfo getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

