// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

public interface RefreshPetPoolReqOrBuilder extends
    // @@protoc_insertion_point(interface_extends:xddq.pb.RefreshPetPoolReq)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>required int32 isFree = 1;</code>
   * @return Whether the isFree field is set.
   */
  boolean hasIsFree();
  /**
   * <code>required int32 isFree = 1;</code>
   * @return The isFree.
   */
  int getIsFree();

  /**
   * <code>optional bool isUseADTime = 2;</code>
   * @return Whether the isUseADTime field is set.
   */
  boolean hasIsUseADTime();
  /**
   * <code>optional bool isUseADTime = 2;</code>
   * @return The isUseADTime.
   */
  boolean getIsUseADTime();
}
