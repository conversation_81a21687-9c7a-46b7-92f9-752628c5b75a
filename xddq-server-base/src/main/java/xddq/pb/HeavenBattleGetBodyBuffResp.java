// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.HeavenBattleGetBodyBuffResp}
 */
public final class HeavenBattleGetBodyBuffResp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.HeavenBattleGetBodyBuffResp)
    HeavenBattleGetBodyBuffRespOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      HeavenBattleGetBodyBuffResp.class.getName());
  }
  // Use HeavenBattleGetBodyBuffResp.newBuilder() to construct.
  private HeavenBattleGetBodyBuffResp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private HeavenBattleGetBodyBuffResp() {
    bodyBuffList_ = java.util.Collections.emptyList();
    campBuffList_ = emptyIntList();
    fightValueList_ =
        com.google.protobuf.LazyStringArrayList.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_HeavenBattleGetBodyBuffResp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_HeavenBattleGetBodyBuffResp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.HeavenBattleGetBodyBuffResp.class, xddq.pb.HeavenBattleGetBodyBuffResp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>optional int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int BODYBUFFLIST_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.HeavenBattleBodyBuffMsg> bodyBuffList_;
  /**
   * <code>repeated .xddq.pb.HeavenBattleBodyBuffMsg bodyBuffList = 2;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.HeavenBattleBodyBuffMsg> getBodyBuffListList() {
    return bodyBuffList_;
  }
  /**
   * <code>repeated .xddq.pb.HeavenBattleBodyBuffMsg bodyBuffList = 2;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.HeavenBattleBodyBuffMsgOrBuilder> 
      getBodyBuffListOrBuilderList() {
    return bodyBuffList_;
  }
  /**
   * <code>repeated .xddq.pb.HeavenBattleBodyBuffMsg bodyBuffList = 2;</code>
   */
  @java.lang.Override
  public int getBodyBuffListCount() {
    return bodyBuffList_.size();
  }
  /**
   * <code>repeated .xddq.pb.HeavenBattleBodyBuffMsg bodyBuffList = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.HeavenBattleBodyBuffMsg getBodyBuffList(int index) {
    return bodyBuffList_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.HeavenBattleBodyBuffMsg bodyBuffList = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.HeavenBattleBodyBuffMsgOrBuilder getBodyBuffListOrBuilder(
      int index) {
    return bodyBuffList_.get(index);
  }

  public static final int CAMPBUFFLIST_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private com.google.protobuf.Internal.IntList campBuffList_ =
      emptyIntList();
  /**
   * <code>repeated int32 campBuffList = 3;</code>
   * @return A list containing the campBuffList.
   */
  @java.lang.Override
  public java.util.List<java.lang.Integer>
      getCampBuffListList() {
    return campBuffList_;
  }
  /**
   * <code>repeated int32 campBuffList = 3;</code>
   * @return The count of campBuffList.
   */
  public int getCampBuffListCount() {
    return campBuffList_.size();
  }
  /**
   * <code>repeated int32 campBuffList = 3;</code>
   * @param index The index of the element to return.
   * @return The campBuffList at the given index.
   */
  public int getCampBuffList(int index) {
    return campBuffList_.getInt(index);
  }

  public static final int FIGHTVALUELIST_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private com.google.protobuf.LazyStringArrayList fightValueList_ =
      com.google.protobuf.LazyStringArrayList.emptyList();
  /**
   * <code>repeated string fightValueList = 4;</code>
   * @return A list containing the fightValueList.
   */
  public com.google.protobuf.ProtocolStringList
      getFightValueListList() {
    return fightValueList_;
  }
  /**
   * <code>repeated string fightValueList = 4;</code>
   * @return The count of fightValueList.
   */
  public int getFightValueListCount() {
    return fightValueList_.size();
  }
  /**
   * <code>repeated string fightValueList = 4;</code>
   * @param index The index of the element to return.
   * @return The fightValueList at the given index.
   */
  public java.lang.String getFightValueList(int index) {
    return fightValueList_.get(index);
  }
  /**
   * <code>repeated string fightValueList = 4;</code>
   * @param index The index of the value to return.
   * @return The bytes of the fightValueList at the given index.
   */
  public com.google.protobuf.ByteString
      getFightValueListBytes(int index) {
    return fightValueList_.getByteString(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    for (int i = 0; i < bodyBuffList_.size(); i++) {
      output.writeMessage(2, bodyBuffList_.get(i));
    }
    for (int i = 0; i < campBuffList_.size(); i++) {
      output.writeInt32(3, campBuffList_.getInt(i));
    }
    for (int i = 0; i < fightValueList_.size(); i++) {
      com.google.protobuf.GeneratedMessage.writeString(output, 4, fightValueList_.getRaw(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    for (int i = 0; i < bodyBuffList_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, bodyBuffList_.get(i));
    }
    {
      int dataSize = 0;
      for (int i = 0; i < campBuffList_.size(); i++) {
        dataSize += com.google.protobuf.CodedOutputStream
          .computeInt32SizeNoTag(campBuffList_.getInt(i));
      }
      size += dataSize;
      size += 1 * getCampBuffListList().size();
    }
    {
      int dataSize = 0;
      for (int i = 0; i < fightValueList_.size(); i++) {
        dataSize += computeStringSizeNoTag(fightValueList_.getRaw(i));
      }
      size += dataSize;
      size += 1 * getFightValueListList().size();
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.HeavenBattleGetBodyBuffResp)) {
      return super.equals(obj);
    }
    xddq.pb.HeavenBattleGetBodyBuffResp other = (xddq.pb.HeavenBattleGetBodyBuffResp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (!getBodyBuffListList()
        .equals(other.getBodyBuffListList())) return false;
    if (!getCampBuffListList()
        .equals(other.getCampBuffListList())) return false;
    if (!getFightValueListList()
        .equals(other.getFightValueListList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (getBodyBuffListCount() > 0) {
      hash = (37 * hash) + BODYBUFFLIST_FIELD_NUMBER;
      hash = (53 * hash) + getBodyBuffListList().hashCode();
    }
    if (getCampBuffListCount() > 0) {
      hash = (37 * hash) + CAMPBUFFLIST_FIELD_NUMBER;
      hash = (53 * hash) + getCampBuffListList().hashCode();
    }
    if (getFightValueListCount() > 0) {
      hash = (37 * hash) + FIGHTVALUELIST_FIELD_NUMBER;
      hash = (53 * hash) + getFightValueListList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.HeavenBattleGetBodyBuffResp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.HeavenBattleGetBodyBuffResp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.HeavenBattleGetBodyBuffResp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.HeavenBattleGetBodyBuffResp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.HeavenBattleGetBodyBuffResp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.HeavenBattleGetBodyBuffResp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.HeavenBattleGetBodyBuffResp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.HeavenBattleGetBodyBuffResp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.HeavenBattleGetBodyBuffResp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.HeavenBattleGetBodyBuffResp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.HeavenBattleGetBodyBuffResp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.HeavenBattleGetBodyBuffResp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.HeavenBattleGetBodyBuffResp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.HeavenBattleGetBodyBuffResp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.HeavenBattleGetBodyBuffResp)
      xddq.pb.HeavenBattleGetBodyBuffRespOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_HeavenBattleGetBodyBuffResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_HeavenBattleGetBodyBuffResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.HeavenBattleGetBodyBuffResp.class, xddq.pb.HeavenBattleGetBodyBuffResp.Builder.class);
    }

    // Construct using xddq.pb.HeavenBattleGetBodyBuffResp.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      if (bodyBuffListBuilder_ == null) {
        bodyBuffList_ = java.util.Collections.emptyList();
      } else {
        bodyBuffList_ = null;
        bodyBuffListBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000002);
      campBuffList_ = emptyIntList();
      fightValueList_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_HeavenBattleGetBodyBuffResp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.HeavenBattleGetBodyBuffResp getDefaultInstanceForType() {
      return xddq.pb.HeavenBattleGetBodyBuffResp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.HeavenBattleGetBodyBuffResp build() {
      xddq.pb.HeavenBattleGetBodyBuffResp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.HeavenBattleGetBodyBuffResp buildPartial() {
      xddq.pb.HeavenBattleGetBodyBuffResp result = new xddq.pb.HeavenBattleGetBodyBuffResp(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.HeavenBattleGetBodyBuffResp result) {
      if (bodyBuffListBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          bodyBuffList_ = java.util.Collections.unmodifiableList(bodyBuffList_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.bodyBuffList_ = bodyBuffList_;
      } else {
        result.bodyBuffList_ = bodyBuffListBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.HeavenBattleGetBodyBuffResp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        campBuffList_.makeImmutable();
        result.campBuffList_ = campBuffList_;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        fightValueList_.makeImmutable();
        result.fightValueList_ = fightValueList_;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.HeavenBattleGetBodyBuffResp) {
        return mergeFrom((xddq.pb.HeavenBattleGetBodyBuffResp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.HeavenBattleGetBodyBuffResp other) {
      if (other == xddq.pb.HeavenBattleGetBodyBuffResp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (bodyBuffListBuilder_ == null) {
        if (!other.bodyBuffList_.isEmpty()) {
          if (bodyBuffList_.isEmpty()) {
            bodyBuffList_ = other.bodyBuffList_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureBodyBuffListIsMutable();
            bodyBuffList_.addAll(other.bodyBuffList_);
          }
          onChanged();
        }
      } else {
        if (!other.bodyBuffList_.isEmpty()) {
          if (bodyBuffListBuilder_.isEmpty()) {
            bodyBuffListBuilder_.dispose();
            bodyBuffListBuilder_ = null;
            bodyBuffList_ = other.bodyBuffList_;
            bitField0_ = (bitField0_ & ~0x00000002);
            bodyBuffListBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetBodyBuffListFieldBuilder() : null;
          } else {
            bodyBuffListBuilder_.addAllMessages(other.bodyBuffList_);
          }
        }
      }
      if (!other.campBuffList_.isEmpty()) {
        if (campBuffList_.isEmpty()) {
          campBuffList_ = other.campBuffList_;
          campBuffList_.makeImmutable();
          bitField0_ |= 0x00000004;
        } else {
          ensureCampBuffListIsMutable();
          campBuffList_.addAll(other.campBuffList_);
        }
        onChanged();
      }
      if (!other.fightValueList_.isEmpty()) {
        if (fightValueList_.isEmpty()) {
          fightValueList_ = other.fightValueList_;
          bitField0_ |= 0x00000008;
        } else {
          ensureFightValueListIsMutable();
          fightValueList_.addAll(other.fightValueList_);
        }
        onChanged();
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              xddq.pb.HeavenBattleBodyBuffMsg m =
                  input.readMessage(
                      xddq.pb.HeavenBattleBodyBuffMsg.parser(),
                      extensionRegistry);
              if (bodyBuffListBuilder_ == null) {
                ensureBodyBuffListIsMutable();
                bodyBuffList_.add(m);
              } else {
                bodyBuffListBuilder_.addMessage(m);
              }
              break;
            } // case 18
            case 24: {
              int v = input.readInt32();
              ensureCampBuffListIsMutable();
              campBuffList_.addInt(v);
              break;
            } // case 24
            case 26: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              ensureCampBuffListIsMutable();
              while (input.getBytesUntilLimit() > 0) {
                campBuffList_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            } // case 26
            case 34: {
              com.google.protobuf.ByteString bs = input.readBytes();
              ensureFightValueListIsMutable();
              fightValueList_.add(bs);
              break;
            } // case 34
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>optional int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>optional int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.HeavenBattleBodyBuffMsg> bodyBuffList_ =
      java.util.Collections.emptyList();
    private void ensureBodyBuffListIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        bodyBuffList_ = new java.util.ArrayList<xddq.pb.HeavenBattleBodyBuffMsg>(bodyBuffList_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.HeavenBattleBodyBuffMsg, xddq.pb.HeavenBattleBodyBuffMsg.Builder, xddq.pb.HeavenBattleBodyBuffMsgOrBuilder> bodyBuffListBuilder_;

    /**
     * <code>repeated .xddq.pb.HeavenBattleBodyBuffMsg bodyBuffList = 2;</code>
     */
    public java.util.List<xddq.pb.HeavenBattleBodyBuffMsg> getBodyBuffListList() {
      if (bodyBuffListBuilder_ == null) {
        return java.util.Collections.unmodifiableList(bodyBuffList_);
      } else {
        return bodyBuffListBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleBodyBuffMsg bodyBuffList = 2;</code>
     */
    public int getBodyBuffListCount() {
      if (bodyBuffListBuilder_ == null) {
        return bodyBuffList_.size();
      } else {
        return bodyBuffListBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleBodyBuffMsg bodyBuffList = 2;</code>
     */
    public xddq.pb.HeavenBattleBodyBuffMsg getBodyBuffList(int index) {
      if (bodyBuffListBuilder_ == null) {
        return bodyBuffList_.get(index);
      } else {
        return bodyBuffListBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleBodyBuffMsg bodyBuffList = 2;</code>
     */
    public Builder setBodyBuffList(
        int index, xddq.pb.HeavenBattleBodyBuffMsg value) {
      if (bodyBuffListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureBodyBuffListIsMutable();
        bodyBuffList_.set(index, value);
        onChanged();
      } else {
        bodyBuffListBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleBodyBuffMsg bodyBuffList = 2;</code>
     */
    public Builder setBodyBuffList(
        int index, xddq.pb.HeavenBattleBodyBuffMsg.Builder builderForValue) {
      if (bodyBuffListBuilder_ == null) {
        ensureBodyBuffListIsMutable();
        bodyBuffList_.set(index, builderForValue.build());
        onChanged();
      } else {
        bodyBuffListBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleBodyBuffMsg bodyBuffList = 2;</code>
     */
    public Builder addBodyBuffList(xddq.pb.HeavenBattleBodyBuffMsg value) {
      if (bodyBuffListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureBodyBuffListIsMutable();
        bodyBuffList_.add(value);
        onChanged();
      } else {
        bodyBuffListBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleBodyBuffMsg bodyBuffList = 2;</code>
     */
    public Builder addBodyBuffList(
        int index, xddq.pb.HeavenBattleBodyBuffMsg value) {
      if (bodyBuffListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureBodyBuffListIsMutable();
        bodyBuffList_.add(index, value);
        onChanged();
      } else {
        bodyBuffListBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleBodyBuffMsg bodyBuffList = 2;</code>
     */
    public Builder addBodyBuffList(
        xddq.pb.HeavenBattleBodyBuffMsg.Builder builderForValue) {
      if (bodyBuffListBuilder_ == null) {
        ensureBodyBuffListIsMutable();
        bodyBuffList_.add(builderForValue.build());
        onChanged();
      } else {
        bodyBuffListBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleBodyBuffMsg bodyBuffList = 2;</code>
     */
    public Builder addBodyBuffList(
        int index, xddq.pb.HeavenBattleBodyBuffMsg.Builder builderForValue) {
      if (bodyBuffListBuilder_ == null) {
        ensureBodyBuffListIsMutable();
        bodyBuffList_.add(index, builderForValue.build());
        onChanged();
      } else {
        bodyBuffListBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleBodyBuffMsg bodyBuffList = 2;</code>
     */
    public Builder addAllBodyBuffList(
        java.lang.Iterable<? extends xddq.pb.HeavenBattleBodyBuffMsg> values) {
      if (bodyBuffListBuilder_ == null) {
        ensureBodyBuffListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, bodyBuffList_);
        onChanged();
      } else {
        bodyBuffListBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleBodyBuffMsg bodyBuffList = 2;</code>
     */
    public Builder clearBodyBuffList() {
      if (bodyBuffListBuilder_ == null) {
        bodyBuffList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        bodyBuffListBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleBodyBuffMsg bodyBuffList = 2;</code>
     */
    public Builder removeBodyBuffList(int index) {
      if (bodyBuffListBuilder_ == null) {
        ensureBodyBuffListIsMutable();
        bodyBuffList_.remove(index);
        onChanged();
      } else {
        bodyBuffListBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleBodyBuffMsg bodyBuffList = 2;</code>
     */
    public xddq.pb.HeavenBattleBodyBuffMsg.Builder getBodyBuffListBuilder(
        int index) {
      return internalGetBodyBuffListFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleBodyBuffMsg bodyBuffList = 2;</code>
     */
    public xddq.pb.HeavenBattleBodyBuffMsgOrBuilder getBodyBuffListOrBuilder(
        int index) {
      if (bodyBuffListBuilder_ == null) {
        return bodyBuffList_.get(index);  } else {
        return bodyBuffListBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleBodyBuffMsg bodyBuffList = 2;</code>
     */
    public java.util.List<? extends xddq.pb.HeavenBattleBodyBuffMsgOrBuilder> 
         getBodyBuffListOrBuilderList() {
      if (bodyBuffListBuilder_ != null) {
        return bodyBuffListBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(bodyBuffList_);
      }
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleBodyBuffMsg bodyBuffList = 2;</code>
     */
    public xddq.pb.HeavenBattleBodyBuffMsg.Builder addBodyBuffListBuilder() {
      return internalGetBodyBuffListFieldBuilder().addBuilder(
          xddq.pb.HeavenBattleBodyBuffMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleBodyBuffMsg bodyBuffList = 2;</code>
     */
    public xddq.pb.HeavenBattleBodyBuffMsg.Builder addBodyBuffListBuilder(
        int index) {
      return internalGetBodyBuffListFieldBuilder().addBuilder(
          index, xddq.pb.HeavenBattleBodyBuffMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.HeavenBattleBodyBuffMsg bodyBuffList = 2;</code>
     */
    public java.util.List<xddq.pb.HeavenBattleBodyBuffMsg.Builder> 
         getBodyBuffListBuilderList() {
      return internalGetBodyBuffListFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.HeavenBattleBodyBuffMsg, xddq.pb.HeavenBattleBodyBuffMsg.Builder, xddq.pb.HeavenBattleBodyBuffMsgOrBuilder> 
        internalGetBodyBuffListFieldBuilder() {
      if (bodyBuffListBuilder_ == null) {
        bodyBuffListBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.HeavenBattleBodyBuffMsg, xddq.pb.HeavenBattleBodyBuffMsg.Builder, xddq.pb.HeavenBattleBodyBuffMsgOrBuilder>(
                bodyBuffList_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        bodyBuffList_ = null;
      }
      return bodyBuffListBuilder_;
    }

    private com.google.protobuf.Internal.IntList campBuffList_ = emptyIntList();
    private void ensureCampBuffListIsMutable() {
      if (!campBuffList_.isModifiable()) {
        campBuffList_ = makeMutableCopy(campBuffList_);
      }
      bitField0_ |= 0x00000004;
    }
    /**
     * <code>repeated int32 campBuffList = 3;</code>
     * @return A list containing the campBuffList.
     */
    public java.util.List<java.lang.Integer>
        getCampBuffListList() {
      campBuffList_.makeImmutable();
      return campBuffList_;
    }
    /**
     * <code>repeated int32 campBuffList = 3;</code>
     * @return The count of campBuffList.
     */
    public int getCampBuffListCount() {
      return campBuffList_.size();
    }
    /**
     * <code>repeated int32 campBuffList = 3;</code>
     * @param index The index of the element to return.
     * @return The campBuffList at the given index.
     */
    public int getCampBuffList(int index) {
      return campBuffList_.getInt(index);
    }
    /**
     * <code>repeated int32 campBuffList = 3;</code>
     * @param index The index to set the value at.
     * @param value The campBuffList to set.
     * @return This builder for chaining.
     */
    public Builder setCampBuffList(
        int index, int value) {

      ensureCampBuffListIsMutable();
      campBuffList_.setInt(index, value);
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 campBuffList = 3;</code>
     * @param value The campBuffList to add.
     * @return This builder for chaining.
     */
    public Builder addCampBuffList(int value) {

      ensureCampBuffListIsMutable();
      campBuffList_.addInt(value);
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 campBuffList = 3;</code>
     * @param values The campBuffList to add.
     * @return This builder for chaining.
     */
    public Builder addAllCampBuffList(
        java.lang.Iterable<? extends java.lang.Integer> values) {
      ensureCampBuffListIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, campBuffList_);
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 campBuffList = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearCampBuffList() {
      campBuffList_ = emptyIntList();
      bitField0_ = (bitField0_ & ~0x00000004);
      onChanged();
      return this;
    }

    private com.google.protobuf.LazyStringArrayList fightValueList_ =
        com.google.protobuf.LazyStringArrayList.emptyList();
    private void ensureFightValueListIsMutable() {
      if (!fightValueList_.isModifiable()) {
        fightValueList_ = new com.google.protobuf.LazyStringArrayList(fightValueList_);
      }
      bitField0_ |= 0x00000008;
    }
    /**
     * <code>repeated string fightValueList = 4;</code>
     * @return A list containing the fightValueList.
     */
    public com.google.protobuf.ProtocolStringList
        getFightValueListList() {
      fightValueList_.makeImmutable();
      return fightValueList_;
    }
    /**
     * <code>repeated string fightValueList = 4;</code>
     * @return The count of fightValueList.
     */
    public int getFightValueListCount() {
      return fightValueList_.size();
    }
    /**
     * <code>repeated string fightValueList = 4;</code>
     * @param index The index of the element to return.
     * @return The fightValueList at the given index.
     */
    public java.lang.String getFightValueList(int index) {
      return fightValueList_.get(index);
    }
    /**
     * <code>repeated string fightValueList = 4;</code>
     * @param index The index of the value to return.
     * @return The bytes of the fightValueList at the given index.
     */
    public com.google.protobuf.ByteString
        getFightValueListBytes(int index) {
      return fightValueList_.getByteString(index);
    }
    /**
     * <code>repeated string fightValueList = 4;</code>
     * @param index The index to set the value at.
     * @param value The fightValueList to set.
     * @return This builder for chaining.
     */
    public Builder setFightValueList(
        int index, java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      ensureFightValueListIsMutable();
      fightValueList_.set(index, value);
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>repeated string fightValueList = 4;</code>
     * @param value The fightValueList to add.
     * @return This builder for chaining.
     */
    public Builder addFightValueList(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      ensureFightValueListIsMutable();
      fightValueList_.add(value);
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>repeated string fightValueList = 4;</code>
     * @param values The fightValueList to add.
     * @return This builder for chaining.
     */
    public Builder addAllFightValueList(
        java.lang.Iterable<java.lang.String> values) {
      ensureFightValueListIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, fightValueList_);
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>repeated string fightValueList = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearFightValueList() {
      fightValueList_ =
        com.google.protobuf.LazyStringArrayList.emptyList();
      bitField0_ = (bitField0_ & ~0x00000008);;
      onChanged();
      return this;
    }
    /**
     * <code>repeated string fightValueList = 4;</code>
     * @param value The bytes of the fightValueList to add.
     * @return This builder for chaining.
     */
    public Builder addFightValueListBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      ensureFightValueListIsMutable();
      fightValueList_.add(value);
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.HeavenBattleGetBodyBuffResp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.HeavenBattleGetBodyBuffResp)
  private static final xddq.pb.HeavenBattleGetBodyBuffResp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.HeavenBattleGetBodyBuffResp();
  }

  public static xddq.pb.HeavenBattleGetBodyBuffResp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<HeavenBattleGetBodyBuffResp>
      PARSER = new com.google.protobuf.AbstractParser<HeavenBattleGetBodyBuffResp>() {
    @java.lang.Override
    public HeavenBattleGetBodyBuffResp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<HeavenBattleGetBodyBuffResp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<HeavenBattleGetBodyBuffResp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.HeavenBattleGetBodyBuffResp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

