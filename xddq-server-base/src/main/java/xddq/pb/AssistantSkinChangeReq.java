// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.AssistantSkinChangeReq}
 */
public final class AssistantSkinChangeReq extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.AssistantSkinChangeReq)
    AssistantSkinChangeReqOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      AssistantSkinChangeReq.class.getName());
  }
  // Use AssistantSkinChangeReq.newBuilder() to construct.
  private AssistantSkinChangeReq(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private AssistantSkinChangeReq() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_AssistantSkinChangeReq_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_AssistantSkinChangeReq_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.AssistantSkinChangeReq.class, xddq.pb.AssistantSkinChangeReq.Builder.class);
  }

  private int bitField0_;
  public static final int SKINID_FIELD_NUMBER = 1;
  private int skinId_ = 0;
  /**
   * <code>required int32 skinId = 1;</code>
   * @return Whether the skinId field is set.
   */
  @java.lang.Override
  public boolean hasSkinId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 skinId = 1;</code>
   * @return The skinId.
   */
  @java.lang.Override
  public int getSkinId() {
    return skinId_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasSkinId()) {
      memoizedIsInitialized = 0;
      return false;
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, skinId_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, skinId_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.AssistantSkinChangeReq)) {
      return super.equals(obj);
    }
    xddq.pb.AssistantSkinChangeReq other = (xddq.pb.AssistantSkinChangeReq) obj;

    if (hasSkinId() != other.hasSkinId()) return false;
    if (hasSkinId()) {
      if (getSkinId()
          != other.getSkinId()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasSkinId()) {
      hash = (37 * hash) + SKINID_FIELD_NUMBER;
      hash = (53 * hash) + getSkinId();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.AssistantSkinChangeReq parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.AssistantSkinChangeReq parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.AssistantSkinChangeReq parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.AssistantSkinChangeReq parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.AssistantSkinChangeReq parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.AssistantSkinChangeReq parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.AssistantSkinChangeReq parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.AssistantSkinChangeReq parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.AssistantSkinChangeReq parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.AssistantSkinChangeReq parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.AssistantSkinChangeReq parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.AssistantSkinChangeReq parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.AssistantSkinChangeReq prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.AssistantSkinChangeReq}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.AssistantSkinChangeReq)
      xddq.pb.AssistantSkinChangeReqOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_AssistantSkinChangeReq_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_AssistantSkinChangeReq_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.AssistantSkinChangeReq.class, xddq.pb.AssistantSkinChangeReq.Builder.class);
    }

    // Construct using xddq.pb.AssistantSkinChangeReq.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      skinId_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_AssistantSkinChangeReq_descriptor;
    }

    @java.lang.Override
    public xddq.pb.AssistantSkinChangeReq getDefaultInstanceForType() {
      return xddq.pb.AssistantSkinChangeReq.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.AssistantSkinChangeReq build() {
      xddq.pb.AssistantSkinChangeReq result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.AssistantSkinChangeReq buildPartial() {
      xddq.pb.AssistantSkinChangeReq result = new xddq.pb.AssistantSkinChangeReq(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.AssistantSkinChangeReq result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.skinId_ = skinId_;
        to_bitField0_ |= 0x00000001;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.AssistantSkinChangeReq) {
        return mergeFrom((xddq.pb.AssistantSkinChangeReq)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.AssistantSkinChangeReq other) {
      if (other == xddq.pb.AssistantSkinChangeReq.getDefaultInstance()) return this;
      if (other.hasSkinId()) {
        setSkinId(other.getSkinId());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasSkinId()) {
        return false;
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              skinId_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int skinId_ ;
    /**
     * <code>required int32 skinId = 1;</code>
     * @return Whether the skinId field is set.
     */
    @java.lang.Override
    public boolean hasSkinId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 skinId = 1;</code>
     * @return The skinId.
     */
    @java.lang.Override
    public int getSkinId() {
      return skinId_;
    }
    /**
     * <code>required int32 skinId = 1;</code>
     * @param value The skinId to set.
     * @return This builder for chaining.
     */
    public Builder setSkinId(int value) {

      skinId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 skinId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearSkinId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      skinId_ = 0;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.AssistantSkinChangeReq)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.AssistantSkinChangeReq)
  private static final xddq.pb.AssistantSkinChangeReq DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.AssistantSkinChangeReq();
  }

  public static xddq.pb.AssistantSkinChangeReq getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<AssistantSkinChangeReq>
      PARSER = new com.google.protobuf.AbstractParser<AssistantSkinChangeReq>() {
    @java.lang.Override
    public AssistantSkinChangeReq parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<AssistantSkinChangeReq> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<AssistantSkinChangeReq> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.AssistantSkinChangeReq getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

