// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.UniverseDrawTwiceResp}
 */
public final class UniverseDrawTwiceResp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.UniverseDrawTwiceResp)
    UniverseDrawTwiceRespOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      UniverseDrawTwiceResp.class.getName());
  }
  // Use UniverseDrawTwiceResp.newBuilder() to construct.
  private UniverseDrawTwiceResp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private UniverseDrawTwiceResp() {
    rewards_ = "";
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_UniverseDrawTwiceResp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_UniverseDrawTwiceResp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.UniverseDrawTwiceResp.class, xddq.pb.UniverseDrawTwiceResp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int TYPE_FIELD_NUMBER = 2;
  private int type_ = 0;
  /**
   * <code>optional int32 type = 2;</code>
   * @return Whether the type field is set.
   */
  @java.lang.Override
  public boolean hasType() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int32 type = 2;</code>
   * @return The type.
   */
  @java.lang.Override
  public int getType() {
    return type_;
  }

  public static final int REWARDS_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private volatile java.lang.Object rewards_ = "";
  /**
   * <code>optional string rewards = 3;</code>
   * @return Whether the rewards field is set.
   */
  @java.lang.Override
  public boolean hasRewards() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional string rewards = 3;</code>
   * @return The rewards.
   */
  @java.lang.Override
  public java.lang.String getRewards() {
    java.lang.Object ref = rewards_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        rewards_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string rewards = 3;</code>
   * @return The bytes for rewards.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getRewardsBytes() {
    java.lang.Object ref = rewards_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      rewards_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int DRAWMAP_FIELD_NUMBER = 4;
  private xddq.pb.UniverseDrawMap drawMap_;
  /**
   * <code>optional .xddq.pb.UniverseDrawMap drawMap = 4;</code>
   * @return Whether the drawMap field is set.
   */
  @java.lang.Override
  public boolean hasDrawMap() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional .xddq.pb.UniverseDrawMap drawMap = 4;</code>
   * @return The drawMap.
   */
  @java.lang.Override
  public xddq.pb.UniverseDrawMap getDrawMap() {
    return drawMap_ == null ? xddq.pb.UniverseDrawMap.getDefaultInstance() : drawMap_;
  }
  /**
   * <code>optional .xddq.pb.UniverseDrawMap drawMap = 4;</code>
   */
  @java.lang.Override
  public xddq.pb.UniverseDrawMapOrBuilder getDrawMapOrBuilder() {
    return drawMap_ == null ? xddq.pb.UniverseDrawMap.getDefaultInstance() : drawMap_;
  }

  public static final int INDEX_FIELD_NUMBER = 5;
  private int index_ = 0;
  /**
   * <code>optional int32 index = 5;</code>
   * @return Whether the index field is set.
   */
  @java.lang.Override
  public boolean hasIndex() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>optional int32 index = 5;</code>
   * @return The index.
   */
  @java.lang.Override
  public int getIndex() {
    return index_;
  }

  public static final int COST_FIELD_NUMBER = 6;
  private int cost_ = 0;
  /**
   * <code>optional int32 cost = 6;</code>
   * @return Whether the cost field is set.
   */
  @java.lang.Override
  public boolean hasCost() {
    return ((bitField0_ & 0x00000020) != 0);
  }
  /**
   * <code>optional int32 cost = 6;</code>
   * @return The cost.
   */
  @java.lang.Override
  public int getCost() {
    return cost_;
  }

  public static final int BEILV_FIELD_NUMBER = 7;
  private double beiLv_ = 0D;
  /**
   * <code>optional double beiLv = 7;</code>
   * @return Whether the beiLv field is set.
   */
  @java.lang.Override
  public boolean hasBeiLv() {
    return ((bitField0_ & 0x00000040) != 0);
  }
  /**
   * <code>optional double beiLv = 7;</code>
   * @return The beiLv.
   */
  @java.lang.Override
  public double getBeiLv() {
    return beiLv_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(2, type_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 3, rewards_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeMessage(4, getDrawMap());
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      output.writeInt32(5, index_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      output.writeInt32(6, cost_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      output.writeDouble(7, beiLv_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, type_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(3, rewards_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(4, getDrawMap());
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(5, index_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(6, cost_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(7, beiLv_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.UniverseDrawTwiceResp)) {
      return super.equals(obj);
    }
    xddq.pb.UniverseDrawTwiceResp other = (xddq.pb.UniverseDrawTwiceResp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (hasType() != other.hasType()) return false;
    if (hasType()) {
      if (getType()
          != other.getType()) return false;
    }
    if (hasRewards() != other.hasRewards()) return false;
    if (hasRewards()) {
      if (!getRewards()
          .equals(other.getRewards())) return false;
    }
    if (hasDrawMap() != other.hasDrawMap()) return false;
    if (hasDrawMap()) {
      if (!getDrawMap()
          .equals(other.getDrawMap())) return false;
    }
    if (hasIndex() != other.hasIndex()) return false;
    if (hasIndex()) {
      if (getIndex()
          != other.getIndex()) return false;
    }
    if (hasCost() != other.hasCost()) return false;
    if (hasCost()) {
      if (getCost()
          != other.getCost()) return false;
    }
    if (hasBeiLv() != other.hasBeiLv()) return false;
    if (hasBeiLv()) {
      if (java.lang.Double.doubleToLongBits(getBeiLv())
          != java.lang.Double.doubleToLongBits(
              other.getBeiLv())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (hasType()) {
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
    }
    if (hasRewards()) {
      hash = (37 * hash) + REWARDS_FIELD_NUMBER;
      hash = (53 * hash) + getRewards().hashCode();
    }
    if (hasDrawMap()) {
      hash = (37 * hash) + DRAWMAP_FIELD_NUMBER;
      hash = (53 * hash) + getDrawMap().hashCode();
    }
    if (hasIndex()) {
      hash = (37 * hash) + INDEX_FIELD_NUMBER;
      hash = (53 * hash) + getIndex();
    }
    if (hasCost()) {
      hash = (37 * hash) + COST_FIELD_NUMBER;
      hash = (53 * hash) + getCost();
    }
    if (hasBeiLv()) {
      hash = (37 * hash) + BEILV_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          java.lang.Double.doubleToLongBits(getBeiLv()));
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.UniverseDrawTwiceResp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.UniverseDrawTwiceResp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.UniverseDrawTwiceResp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.UniverseDrawTwiceResp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.UniverseDrawTwiceResp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.UniverseDrawTwiceResp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.UniverseDrawTwiceResp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.UniverseDrawTwiceResp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.UniverseDrawTwiceResp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.UniverseDrawTwiceResp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.UniverseDrawTwiceResp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.UniverseDrawTwiceResp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.UniverseDrawTwiceResp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.UniverseDrawTwiceResp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.UniverseDrawTwiceResp)
      xddq.pb.UniverseDrawTwiceRespOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_UniverseDrawTwiceResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_UniverseDrawTwiceResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.UniverseDrawTwiceResp.class, xddq.pb.UniverseDrawTwiceResp.Builder.class);
    }

    // Construct using xddq.pb.UniverseDrawTwiceResp.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetDrawMapFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      type_ = 0;
      rewards_ = "";
      drawMap_ = null;
      if (drawMapBuilder_ != null) {
        drawMapBuilder_.dispose();
        drawMapBuilder_ = null;
      }
      index_ = 0;
      cost_ = 0;
      beiLv_ = 0D;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_UniverseDrawTwiceResp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.UniverseDrawTwiceResp getDefaultInstanceForType() {
      return xddq.pb.UniverseDrawTwiceResp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.UniverseDrawTwiceResp build() {
      xddq.pb.UniverseDrawTwiceResp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.UniverseDrawTwiceResp buildPartial() {
      xddq.pb.UniverseDrawTwiceResp result = new xddq.pb.UniverseDrawTwiceResp(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.UniverseDrawTwiceResp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.type_ = type_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.rewards_ = rewards_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.drawMap_ = drawMapBuilder_ == null
            ? drawMap_
            : drawMapBuilder_.build();
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.index_ = index_;
        to_bitField0_ |= 0x00000010;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.cost_ = cost_;
        to_bitField0_ |= 0x00000020;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.beiLv_ = beiLv_;
        to_bitField0_ |= 0x00000040;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.UniverseDrawTwiceResp) {
        return mergeFrom((xddq.pb.UniverseDrawTwiceResp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.UniverseDrawTwiceResp other) {
      if (other == xddq.pb.UniverseDrawTwiceResp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (other.hasType()) {
        setType(other.getType());
      }
      if (other.hasRewards()) {
        rewards_ = other.rewards_;
        bitField0_ |= 0x00000004;
        onChanged();
      }
      if (other.hasDrawMap()) {
        mergeDrawMap(other.getDrawMap());
      }
      if (other.hasIndex()) {
        setIndex(other.getIndex());
      }
      if (other.hasCost()) {
        setCost(other.getCost());
      }
      if (other.hasBeiLv()) {
        setBeiLv(other.getBeiLv());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              type_ = input.readInt32();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 26: {
              rewards_ = input.readBytes();
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 34: {
              input.readMessage(
                  internalGetDrawMapFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000008;
              break;
            } // case 34
            case 40: {
              index_ = input.readInt32();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            case 48: {
              cost_ = input.readInt32();
              bitField0_ |= 0x00000020;
              break;
            } // case 48
            case 57: {
              beiLv_ = input.readDouble();
              bitField0_ |= 0x00000040;
              break;
            } // case 57
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private int type_ ;
    /**
     * <code>optional int32 type = 2;</code>
     * @return Whether the type field is set.
     */
    @java.lang.Override
    public boolean hasType() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 type = 2;</code>
     * @return The type.
     */
    @java.lang.Override
    public int getType() {
      return type_;
    }
    /**
     * <code>optional int32 type = 2;</code>
     * @param value The type to set.
     * @return This builder for chaining.
     */
    public Builder setType(int value) {

      type_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 type = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearType() {
      bitField0_ = (bitField0_ & ~0x00000002);
      type_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object rewards_ = "";
    /**
     * <code>optional string rewards = 3;</code>
     * @return Whether the rewards field is set.
     */
    public boolean hasRewards() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional string rewards = 3;</code>
     * @return The rewards.
     */
    public java.lang.String getRewards() {
      java.lang.Object ref = rewards_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          rewards_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string rewards = 3;</code>
     * @return The bytes for rewards.
     */
    public com.google.protobuf.ByteString
        getRewardsBytes() {
      java.lang.Object ref = rewards_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        rewards_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string rewards = 3;</code>
     * @param value The rewards to set.
     * @return This builder for chaining.
     */
    public Builder setRewards(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      rewards_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional string rewards = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearRewards() {
      rewards_ = getDefaultInstance().getRewards();
      bitField0_ = (bitField0_ & ~0x00000004);
      onChanged();
      return this;
    }
    /**
     * <code>optional string rewards = 3;</code>
     * @param value The bytes for rewards to set.
     * @return This builder for chaining.
     */
    public Builder setRewardsBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      rewards_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }

    private xddq.pb.UniverseDrawMap drawMap_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.UniverseDrawMap, xddq.pb.UniverseDrawMap.Builder, xddq.pb.UniverseDrawMapOrBuilder> drawMapBuilder_;
    /**
     * <code>optional .xddq.pb.UniverseDrawMap drawMap = 4;</code>
     * @return Whether the drawMap field is set.
     */
    public boolean hasDrawMap() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional .xddq.pb.UniverseDrawMap drawMap = 4;</code>
     * @return The drawMap.
     */
    public xddq.pb.UniverseDrawMap getDrawMap() {
      if (drawMapBuilder_ == null) {
        return drawMap_ == null ? xddq.pb.UniverseDrawMap.getDefaultInstance() : drawMap_;
      } else {
        return drawMapBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.UniverseDrawMap drawMap = 4;</code>
     */
    public Builder setDrawMap(xddq.pb.UniverseDrawMap value) {
      if (drawMapBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        drawMap_ = value;
      } else {
        drawMapBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.UniverseDrawMap drawMap = 4;</code>
     */
    public Builder setDrawMap(
        xddq.pb.UniverseDrawMap.Builder builderForValue) {
      if (drawMapBuilder_ == null) {
        drawMap_ = builderForValue.build();
      } else {
        drawMapBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.UniverseDrawMap drawMap = 4;</code>
     */
    public Builder mergeDrawMap(xddq.pb.UniverseDrawMap value) {
      if (drawMapBuilder_ == null) {
        if (((bitField0_ & 0x00000008) != 0) &&
          drawMap_ != null &&
          drawMap_ != xddq.pb.UniverseDrawMap.getDefaultInstance()) {
          getDrawMapBuilder().mergeFrom(value);
        } else {
          drawMap_ = value;
        }
      } else {
        drawMapBuilder_.mergeFrom(value);
      }
      if (drawMap_ != null) {
        bitField0_ |= 0x00000008;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.UniverseDrawMap drawMap = 4;</code>
     */
    public Builder clearDrawMap() {
      bitField0_ = (bitField0_ & ~0x00000008);
      drawMap_ = null;
      if (drawMapBuilder_ != null) {
        drawMapBuilder_.dispose();
        drawMapBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.UniverseDrawMap drawMap = 4;</code>
     */
    public xddq.pb.UniverseDrawMap.Builder getDrawMapBuilder() {
      bitField0_ |= 0x00000008;
      onChanged();
      return internalGetDrawMapFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.UniverseDrawMap drawMap = 4;</code>
     */
    public xddq.pb.UniverseDrawMapOrBuilder getDrawMapOrBuilder() {
      if (drawMapBuilder_ != null) {
        return drawMapBuilder_.getMessageOrBuilder();
      } else {
        return drawMap_ == null ?
            xddq.pb.UniverseDrawMap.getDefaultInstance() : drawMap_;
      }
    }
    /**
     * <code>optional .xddq.pb.UniverseDrawMap drawMap = 4;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.UniverseDrawMap, xddq.pb.UniverseDrawMap.Builder, xddq.pb.UniverseDrawMapOrBuilder> 
        internalGetDrawMapFieldBuilder() {
      if (drawMapBuilder_ == null) {
        drawMapBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.UniverseDrawMap, xddq.pb.UniverseDrawMap.Builder, xddq.pb.UniverseDrawMapOrBuilder>(
                getDrawMap(),
                getParentForChildren(),
                isClean());
        drawMap_ = null;
      }
      return drawMapBuilder_;
    }

    private int index_ ;
    /**
     * <code>optional int32 index = 5;</code>
     * @return Whether the index field is set.
     */
    @java.lang.Override
    public boolean hasIndex() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional int32 index = 5;</code>
     * @return The index.
     */
    @java.lang.Override
    public int getIndex() {
      return index_;
    }
    /**
     * <code>optional int32 index = 5;</code>
     * @param value The index to set.
     * @return This builder for chaining.
     */
    public Builder setIndex(int value) {

      index_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 index = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearIndex() {
      bitField0_ = (bitField0_ & ~0x00000010);
      index_ = 0;
      onChanged();
      return this;
    }

    private int cost_ ;
    /**
     * <code>optional int32 cost = 6;</code>
     * @return Whether the cost field is set.
     */
    @java.lang.Override
    public boolean hasCost() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional int32 cost = 6;</code>
     * @return The cost.
     */
    @java.lang.Override
    public int getCost() {
      return cost_;
    }
    /**
     * <code>optional int32 cost = 6;</code>
     * @param value The cost to set.
     * @return This builder for chaining.
     */
    public Builder setCost(int value) {

      cost_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 cost = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearCost() {
      bitField0_ = (bitField0_ & ~0x00000020);
      cost_ = 0;
      onChanged();
      return this;
    }

    private double beiLv_ ;
    /**
     * <code>optional double beiLv = 7;</code>
     * @return Whether the beiLv field is set.
     */
    @java.lang.Override
    public boolean hasBeiLv() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <code>optional double beiLv = 7;</code>
     * @return The beiLv.
     */
    @java.lang.Override
    public double getBeiLv() {
      return beiLv_;
    }
    /**
     * <code>optional double beiLv = 7;</code>
     * @param value The beiLv to set.
     * @return This builder for chaining.
     */
    public Builder setBeiLv(double value) {

      beiLv_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>optional double beiLv = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearBeiLv() {
      bitField0_ = (bitField0_ & ~0x00000040);
      beiLv_ = 0D;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.UniverseDrawTwiceResp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.UniverseDrawTwiceResp)
  private static final xddq.pb.UniverseDrawTwiceResp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.UniverseDrawTwiceResp();
  }

  public static xddq.pb.UniverseDrawTwiceResp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<UniverseDrawTwiceResp>
      PARSER = new com.google.protobuf.AbstractParser<UniverseDrawTwiceResp>() {
    @java.lang.Override
    public UniverseDrawTwiceResp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<UniverseDrawTwiceResp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<UniverseDrawTwiceResp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.UniverseDrawTwiceResp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

