// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

public interface GodIslandGroupConfigOrBuilder extends
    // @@protoc_insertion_point(interface_extends:xddq.pb.GodIslandGroupConfig)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>required int32 activityId = 1;</code>
   * @return Whether the activityId field is set.
   */
  boolean hasActivityId();
  /**
   * <code>required int32 activityId = 1;</code>
   * @return The activityId.
   */
  int getActivityId();

  /**
   * <code>required int32 rankId = 2;</code>
   * @return Whether the rankId field is set.
   */
  boolean hasRankId();
  /**
   * <code>required int32 rankId = 2;</code>
   * @return The rankId.
   */
  int getRankId();

  /**
   * <code>required int32 size = 3;</code>
   * @return Whether the size field is set.
   */
  boolean hasSize();
  /**
   * <code>required int32 size = 3;</code>
   * @return The size.
   */
  int getSize();

  /**
   * <code>required int32 up = 4;</code>
   * @return Whether the up field is set.
   */
  boolean hasUp();
  /**
   * <code>required int32 up = 4;</code>
   * @return The up.
   */
  int getUp();

  /**
   * <code>required int32 down = 5;</code>
   * @return Whether the down field is set.
   */
  boolean hasDown();
  /**
   * <code>required int32 down = 5;</code>
   * @return The down.
   */
  int getDown();

  /**
   * <code>required string score = 6;</code>
   * @return Whether the score field is set.
   */
  boolean hasScore();
  /**
   * <code>required string score = 6;</code>
   * @return The score.
   */
  java.lang.String getScore();
  /**
   * <code>required string score = 6;</code>
   * @return The bytes for score.
   */
  com.google.protobuf.ByteString
      getScoreBytes();
}
