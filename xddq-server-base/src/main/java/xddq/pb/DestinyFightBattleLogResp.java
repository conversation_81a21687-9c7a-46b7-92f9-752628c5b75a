// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.DestinyFightBattleLogResp}
 */
public final class DestinyFightBattleLogResp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.DestinyFightBattleLogResp)
    DestinyFightBattleLogRespOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      DestinyFightBattleLogResp.class.getName());
  }
  // Use DestinyFightBattleLogResp.newBuilder() to construct.
  private DestinyFightBattleLogResp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private DestinyFightBattleLogResp() {
    logInfo_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_DestinyFightBattleLogResp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_DestinyFightBattleLogResp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.DestinyFightBattleLogResp.class, xddq.pb.DestinyFightBattleLogResp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int LOGINFO_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.DestinyFightBattleLogInfo> logInfo_;
  /**
   * <code>repeated .xddq.pb.DestinyFightBattleLogInfo logInfo = 2;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.DestinyFightBattleLogInfo> getLogInfoList() {
    return logInfo_;
  }
  /**
   * <code>repeated .xddq.pb.DestinyFightBattleLogInfo logInfo = 2;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.DestinyFightBattleLogInfoOrBuilder> 
      getLogInfoOrBuilderList() {
    return logInfo_;
  }
  /**
   * <code>repeated .xddq.pb.DestinyFightBattleLogInfo logInfo = 2;</code>
   */
  @java.lang.Override
  public int getLogInfoCount() {
    return logInfo_.size();
  }
  /**
   * <code>repeated .xddq.pb.DestinyFightBattleLogInfo logInfo = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.DestinyFightBattleLogInfo getLogInfo(int index) {
    return logInfo_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.DestinyFightBattleLogInfo logInfo = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.DestinyFightBattleLogInfoOrBuilder getLogInfoOrBuilder(
      int index) {
    return logInfo_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    for (int i = 0; i < getLogInfoCount(); i++) {
      if (!getLogInfo(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    for (int i = 0; i < logInfo_.size(); i++) {
      output.writeMessage(2, logInfo_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    for (int i = 0; i < logInfo_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, logInfo_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.DestinyFightBattleLogResp)) {
      return super.equals(obj);
    }
    xddq.pb.DestinyFightBattleLogResp other = (xddq.pb.DestinyFightBattleLogResp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (!getLogInfoList()
        .equals(other.getLogInfoList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (getLogInfoCount() > 0) {
      hash = (37 * hash) + LOGINFO_FIELD_NUMBER;
      hash = (53 * hash) + getLogInfoList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.DestinyFightBattleLogResp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.DestinyFightBattleLogResp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.DestinyFightBattleLogResp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.DestinyFightBattleLogResp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.DestinyFightBattleLogResp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.DestinyFightBattleLogResp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.DestinyFightBattleLogResp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.DestinyFightBattleLogResp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.DestinyFightBattleLogResp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.DestinyFightBattleLogResp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.DestinyFightBattleLogResp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.DestinyFightBattleLogResp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.DestinyFightBattleLogResp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.DestinyFightBattleLogResp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.DestinyFightBattleLogResp)
      xddq.pb.DestinyFightBattleLogRespOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_DestinyFightBattleLogResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_DestinyFightBattleLogResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.DestinyFightBattleLogResp.class, xddq.pb.DestinyFightBattleLogResp.Builder.class);
    }

    // Construct using xddq.pb.DestinyFightBattleLogResp.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      if (logInfoBuilder_ == null) {
        logInfo_ = java.util.Collections.emptyList();
      } else {
        logInfo_ = null;
        logInfoBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000002);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_DestinyFightBattleLogResp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.DestinyFightBattleLogResp getDefaultInstanceForType() {
      return xddq.pb.DestinyFightBattleLogResp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.DestinyFightBattleLogResp build() {
      xddq.pb.DestinyFightBattleLogResp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.DestinyFightBattleLogResp buildPartial() {
      xddq.pb.DestinyFightBattleLogResp result = new xddq.pb.DestinyFightBattleLogResp(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.DestinyFightBattleLogResp result) {
      if (logInfoBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          logInfo_ = java.util.Collections.unmodifiableList(logInfo_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.logInfo_ = logInfo_;
      } else {
        result.logInfo_ = logInfoBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.DestinyFightBattleLogResp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.DestinyFightBattleLogResp) {
        return mergeFrom((xddq.pb.DestinyFightBattleLogResp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.DestinyFightBattleLogResp other) {
      if (other == xddq.pb.DestinyFightBattleLogResp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (logInfoBuilder_ == null) {
        if (!other.logInfo_.isEmpty()) {
          if (logInfo_.isEmpty()) {
            logInfo_ = other.logInfo_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureLogInfoIsMutable();
            logInfo_.addAll(other.logInfo_);
          }
          onChanged();
        }
      } else {
        if (!other.logInfo_.isEmpty()) {
          if (logInfoBuilder_.isEmpty()) {
            logInfoBuilder_.dispose();
            logInfoBuilder_ = null;
            logInfo_ = other.logInfo_;
            bitField0_ = (bitField0_ & ~0x00000002);
            logInfoBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetLogInfoFieldBuilder() : null;
          } else {
            logInfoBuilder_.addAllMessages(other.logInfo_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      for (int i = 0; i < getLogInfoCount(); i++) {
        if (!getLogInfo(i).isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              xddq.pb.DestinyFightBattleLogInfo m =
                  input.readMessage(
                      xddq.pb.DestinyFightBattleLogInfo.parser(),
                      extensionRegistry);
              if (logInfoBuilder_ == null) {
                ensureLogInfoIsMutable();
                logInfo_.add(m);
              } else {
                logInfoBuilder_.addMessage(m);
              }
              break;
            } // case 18
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.DestinyFightBattleLogInfo> logInfo_ =
      java.util.Collections.emptyList();
    private void ensureLogInfoIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        logInfo_ = new java.util.ArrayList<xddq.pb.DestinyFightBattleLogInfo>(logInfo_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.DestinyFightBattleLogInfo, xddq.pb.DestinyFightBattleLogInfo.Builder, xddq.pb.DestinyFightBattleLogInfoOrBuilder> logInfoBuilder_;

    /**
     * <code>repeated .xddq.pb.DestinyFightBattleLogInfo logInfo = 2;</code>
     */
    public java.util.List<xddq.pb.DestinyFightBattleLogInfo> getLogInfoList() {
      if (logInfoBuilder_ == null) {
        return java.util.Collections.unmodifiableList(logInfo_);
      } else {
        return logInfoBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.DestinyFightBattleLogInfo logInfo = 2;</code>
     */
    public int getLogInfoCount() {
      if (logInfoBuilder_ == null) {
        return logInfo_.size();
      } else {
        return logInfoBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.DestinyFightBattleLogInfo logInfo = 2;</code>
     */
    public xddq.pb.DestinyFightBattleLogInfo getLogInfo(int index) {
      if (logInfoBuilder_ == null) {
        return logInfo_.get(index);
      } else {
        return logInfoBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.DestinyFightBattleLogInfo logInfo = 2;</code>
     */
    public Builder setLogInfo(
        int index, xddq.pb.DestinyFightBattleLogInfo value) {
      if (logInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureLogInfoIsMutable();
        logInfo_.set(index, value);
        onChanged();
      } else {
        logInfoBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DestinyFightBattleLogInfo logInfo = 2;</code>
     */
    public Builder setLogInfo(
        int index, xddq.pb.DestinyFightBattleLogInfo.Builder builderForValue) {
      if (logInfoBuilder_ == null) {
        ensureLogInfoIsMutable();
        logInfo_.set(index, builderForValue.build());
        onChanged();
      } else {
        logInfoBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DestinyFightBattleLogInfo logInfo = 2;</code>
     */
    public Builder addLogInfo(xddq.pb.DestinyFightBattleLogInfo value) {
      if (logInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureLogInfoIsMutable();
        logInfo_.add(value);
        onChanged();
      } else {
        logInfoBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DestinyFightBattleLogInfo logInfo = 2;</code>
     */
    public Builder addLogInfo(
        int index, xddq.pb.DestinyFightBattleLogInfo value) {
      if (logInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureLogInfoIsMutable();
        logInfo_.add(index, value);
        onChanged();
      } else {
        logInfoBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DestinyFightBattleLogInfo logInfo = 2;</code>
     */
    public Builder addLogInfo(
        xddq.pb.DestinyFightBattleLogInfo.Builder builderForValue) {
      if (logInfoBuilder_ == null) {
        ensureLogInfoIsMutable();
        logInfo_.add(builderForValue.build());
        onChanged();
      } else {
        logInfoBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DestinyFightBattleLogInfo logInfo = 2;</code>
     */
    public Builder addLogInfo(
        int index, xddq.pb.DestinyFightBattleLogInfo.Builder builderForValue) {
      if (logInfoBuilder_ == null) {
        ensureLogInfoIsMutable();
        logInfo_.add(index, builderForValue.build());
        onChanged();
      } else {
        logInfoBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DestinyFightBattleLogInfo logInfo = 2;</code>
     */
    public Builder addAllLogInfo(
        java.lang.Iterable<? extends xddq.pb.DestinyFightBattleLogInfo> values) {
      if (logInfoBuilder_ == null) {
        ensureLogInfoIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, logInfo_);
        onChanged();
      } else {
        logInfoBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DestinyFightBattleLogInfo logInfo = 2;</code>
     */
    public Builder clearLogInfo() {
      if (logInfoBuilder_ == null) {
        logInfo_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        logInfoBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DestinyFightBattleLogInfo logInfo = 2;</code>
     */
    public Builder removeLogInfo(int index) {
      if (logInfoBuilder_ == null) {
        ensureLogInfoIsMutable();
        logInfo_.remove(index);
        onChanged();
      } else {
        logInfoBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DestinyFightBattleLogInfo logInfo = 2;</code>
     */
    public xddq.pb.DestinyFightBattleLogInfo.Builder getLogInfoBuilder(
        int index) {
      return internalGetLogInfoFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.DestinyFightBattleLogInfo logInfo = 2;</code>
     */
    public xddq.pb.DestinyFightBattleLogInfoOrBuilder getLogInfoOrBuilder(
        int index) {
      if (logInfoBuilder_ == null) {
        return logInfo_.get(index);  } else {
        return logInfoBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.DestinyFightBattleLogInfo logInfo = 2;</code>
     */
    public java.util.List<? extends xddq.pb.DestinyFightBattleLogInfoOrBuilder> 
         getLogInfoOrBuilderList() {
      if (logInfoBuilder_ != null) {
        return logInfoBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(logInfo_);
      }
    }
    /**
     * <code>repeated .xddq.pb.DestinyFightBattleLogInfo logInfo = 2;</code>
     */
    public xddq.pb.DestinyFightBattleLogInfo.Builder addLogInfoBuilder() {
      return internalGetLogInfoFieldBuilder().addBuilder(
          xddq.pb.DestinyFightBattleLogInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.DestinyFightBattleLogInfo logInfo = 2;</code>
     */
    public xddq.pb.DestinyFightBattleLogInfo.Builder addLogInfoBuilder(
        int index) {
      return internalGetLogInfoFieldBuilder().addBuilder(
          index, xddq.pb.DestinyFightBattleLogInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.DestinyFightBattleLogInfo logInfo = 2;</code>
     */
    public java.util.List<xddq.pb.DestinyFightBattleLogInfo.Builder> 
         getLogInfoBuilderList() {
      return internalGetLogInfoFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.DestinyFightBattleLogInfo, xddq.pb.DestinyFightBattleLogInfo.Builder, xddq.pb.DestinyFightBattleLogInfoOrBuilder> 
        internalGetLogInfoFieldBuilder() {
      if (logInfoBuilder_ == null) {
        logInfoBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.DestinyFightBattleLogInfo, xddq.pb.DestinyFightBattleLogInfo.Builder, xddq.pb.DestinyFightBattleLogInfoOrBuilder>(
                logInfo_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        logInfo_ = null;
      }
      return logInfoBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.DestinyFightBattleLogResp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.DestinyFightBattleLogResp)
  private static final xddq.pb.DestinyFightBattleLogResp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.DestinyFightBattleLogResp();
  }

  public static xddq.pb.DestinyFightBattleLogResp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<DestinyFightBattleLogResp>
      PARSER = new com.google.protobuf.AbstractParser<DestinyFightBattleLogResp>() {
    @java.lang.Override
    public DestinyFightBattleLogResp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<DestinyFightBattleLogResp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<DestinyFightBattleLogResp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.DestinyFightBattleLogResp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

