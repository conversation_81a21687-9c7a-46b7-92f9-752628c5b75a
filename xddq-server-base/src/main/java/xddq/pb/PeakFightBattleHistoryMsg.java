// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.PeakFightBattleHistoryMsg}
 */
public final class PeakFightBattleHistoryMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.PeakFightBattleHistoryMsg)
    PeakFightBattleHistoryMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      PeakFightBattleHistoryMsg.class.getName());
  }
  // Use PeakFightBattleHistoryMsg.newBuilder() to construct.
  private PeakFightBattleHistoryMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private PeakFightBattleHistoryMsg() {
    score_ = emptyIntList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_PeakFightBattleHistoryMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_PeakFightBattleHistoryMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.PeakFightBattleHistoryMsg.class, xddq.pb.PeakFightBattleHistoryMsg.Builder.class);
  }

  private int bitField0_;
  public static final int UNIONBASE_FIELD_NUMBER = 1;
  private xddq.pb.PeakFightUnionBaseMsg unionBase_;
  /**
   * <code>optional .xddq.pb.PeakFightUnionBaseMsg unionBase = 1;</code>
   * @return Whether the unionBase field is set.
   */
  @java.lang.Override
  public boolean hasUnionBase() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional .xddq.pb.PeakFightUnionBaseMsg unionBase = 1;</code>
   * @return The unionBase.
   */
  @java.lang.Override
  public xddq.pb.PeakFightUnionBaseMsg getUnionBase() {
    return unionBase_ == null ? xddq.pb.PeakFightUnionBaseMsg.getDefaultInstance() : unionBase_;
  }
  /**
   * <code>optional .xddq.pb.PeakFightUnionBaseMsg unionBase = 1;</code>
   */
  @java.lang.Override
  public xddq.pb.PeakFightUnionBaseMsgOrBuilder getUnionBaseOrBuilder() {
    return unionBase_ == null ? xddq.pb.PeakFightUnionBaseMsg.getDefaultInstance() : unionBase_;
  }

  public static final int SCORE_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private com.google.protobuf.Internal.IntList score_ =
      emptyIntList();
  /**
   * <code>repeated int32 score = 2;</code>
   * @return A list containing the score.
   */
  @java.lang.Override
  public java.util.List<java.lang.Integer>
      getScoreList() {
    return score_;
  }
  /**
   * <code>repeated int32 score = 2;</code>
   * @return The count of score.
   */
  public int getScoreCount() {
    return score_.size();
  }
  /**
   * <code>repeated int32 score = 2;</code>
   * @param index The index of the element to return.
   * @return The score at the given index.
   */
  public int getScore(int index) {
    return score_.getInt(index);
  }

  public static final int RESULT_FIELD_NUMBER = 3;
  private int result_ = 0;
  /**
   * <code>optional int32 result = 3;</code>
   * @return Whether the result field is set.
   */
  @java.lang.Override
  public boolean hasResult() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int32 result = 3;</code>
   * @return The result.
   */
  @java.lang.Override
  public int getResult() {
    return result_;
  }

  public static final int ADDSCORE_FIELD_NUMBER = 4;
  private int addScore_ = 0;
  /**
   * <code>optional int32 addScore = 4;</code>
   * @return Whether the addScore field is set.
   */
  @java.lang.Override
  public boolean hasAddScore() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional int32 addScore = 4;</code>
   * @return The addScore.
   */
  @java.lang.Override
  public int getAddScore() {
    return addScore_;
  }

  public static final int DAY_FIELD_NUMBER = 5;
  private int day_ = 0;
  /**
   * <code>optional int32 day = 5;</code>
   * @return Whether the day field is set.
   */
  @java.lang.Override
  public boolean hasDay() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional int32 day = 5;</code>
   * @return The day.
   */
  @java.lang.Override
  public int getDay() {
    return day_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeMessage(1, getUnionBase());
    }
    for (int i = 0; i < score_.size(); i++) {
      output.writeInt32(2, score_.getInt(i));
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(3, result_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt32(4, addScore_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt32(5, day_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, getUnionBase());
    }
    {
      int dataSize = 0;
      for (int i = 0; i < score_.size(); i++) {
        dataSize += com.google.protobuf.CodedOutputStream
          .computeInt32SizeNoTag(score_.getInt(i));
      }
      size += dataSize;
      size += 1 * getScoreList().size();
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(3, result_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(4, addScore_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(5, day_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.PeakFightBattleHistoryMsg)) {
      return super.equals(obj);
    }
    xddq.pb.PeakFightBattleHistoryMsg other = (xddq.pb.PeakFightBattleHistoryMsg) obj;

    if (hasUnionBase() != other.hasUnionBase()) return false;
    if (hasUnionBase()) {
      if (!getUnionBase()
          .equals(other.getUnionBase())) return false;
    }
    if (!getScoreList()
        .equals(other.getScoreList())) return false;
    if (hasResult() != other.hasResult()) return false;
    if (hasResult()) {
      if (getResult()
          != other.getResult()) return false;
    }
    if (hasAddScore() != other.hasAddScore()) return false;
    if (hasAddScore()) {
      if (getAddScore()
          != other.getAddScore()) return false;
    }
    if (hasDay() != other.hasDay()) return false;
    if (hasDay()) {
      if (getDay()
          != other.getDay()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasUnionBase()) {
      hash = (37 * hash) + UNIONBASE_FIELD_NUMBER;
      hash = (53 * hash) + getUnionBase().hashCode();
    }
    if (getScoreCount() > 0) {
      hash = (37 * hash) + SCORE_FIELD_NUMBER;
      hash = (53 * hash) + getScoreList().hashCode();
    }
    if (hasResult()) {
      hash = (37 * hash) + RESULT_FIELD_NUMBER;
      hash = (53 * hash) + getResult();
    }
    if (hasAddScore()) {
      hash = (37 * hash) + ADDSCORE_FIELD_NUMBER;
      hash = (53 * hash) + getAddScore();
    }
    if (hasDay()) {
      hash = (37 * hash) + DAY_FIELD_NUMBER;
      hash = (53 * hash) + getDay();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.PeakFightBattleHistoryMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PeakFightBattleHistoryMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PeakFightBattleHistoryMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PeakFightBattleHistoryMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PeakFightBattleHistoryMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PeakFightBattleHistoryMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PeakFightBattleHistoryMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.PeakFightBattleHistoryMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.PeakFightBattleHistoryMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.PeakFightBattleHistoryMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.PeakFightBattleHistoryMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.PeakFightBattleHistoryMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.PeakFightBattleHistoryMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.PeakFightBattleHistoryMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.PeakFightBattleHistoryMsg)
      xddq.pb.PeakFightBattleHistoryMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PeakFightBattleHistoryMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PeakFightBattleHistoryMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.PeakFightBattleHistoryMsg.class, xddq.pb.PeakFightBattleHistoryMsg.Builder.class);
    }

    // Construct using xddq.pb.PeakFightBattleHistoryMsg.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetUnionBaseFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      unionBase_ = null;
      if (unionBaseBuilder_ != null) {
        unionBaseBuilder_.dispose();
        unionBaseBuilder_ = null;
      }
      score_ = emptyIntList();
      result_ = 0;
      addScore_ = 0;
      day_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PeakFightBattleHistoryMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.PeakFightBattleHistoryMsg getDefaultInstanceForType() {
      return xddq.pb.PeakFightBattleHistoryMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.PeakFightBattleHistoryMsg build() {
      xddq.pb.PeakFightBattleHistoryMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.PeakFightBattleHistoryMsg buildPartial() {
      xddq.pb.PeakFightBattleHistoryMsg result = new xddq.pb.PeakFightBattleHistoryMsg(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.PeakFightBattleHistoryMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.unionBase_ = unionBaseBuilder_ == null
            ? unionBase_
            : unionBaseBuilder_.build();
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        score_.makeImmutable();
        result.score_ = score_;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.result_ = result_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.addScore_ = addScore_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.day_ = day_;
        to_bitField0_ |= 0x00000008;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.PeakFightBattleHistoryMsg) {
        return mergeFrom((xddq.pb.PeakFightBattleHistoryMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.PeakFightBattleHistoryMsg other) {
      if (other == xddq.pb.PeakFightBattleHistoryMsg.getDefaultInstance()) return this;
      if (other.hasUnionBase()) {
        mergeUnionBase(other.getUnionBase());
      }
      if (!other.score_.isEmpty()) {
        if (score_.isEmpty()) {
          score_ = other.score_;
          score_.makeImmutable();
          bitField0_ |= 0x00000002;
        } else {
          ensureScoreIsMutable();
          score_.addAll(other.score_);
        }
        onChanged();
      }
      if (other.hasResult()) {
        setResult(other.getResult());
      }
      if (other.hasAddScore()) {
        setAddScore(other.getAddScore());
      }
      if (other.hasDay()) {
        setDay(other.getDay());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              input.readMessage(
                  internalGetUnionBaseFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 16: {
              int v = input.readInt32();
              ensureScoreIsMutable();
              score_.addInt(v);
              break;
            } // case 16
            case 18: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              ensureScoreIsMutable();
              while (input.getBytesUntilLimit() > 0) {
                score_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            } // case 18
            case 24: {
              result_ = input.readInt32();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 32: {
              addScore_ = input.readInt32();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            case 40: {
              day_ = input.readInt32();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private xddq.pb.PeakFightUnionBaseMsg unionBase_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PeakFightUnionBaseMsg, xddq.pb.PeakFightUnionBaseMsg.Builder, xddq.pb.PeakFightUnionBaseMsgOrBuilder> unionBaseBuilder_;
    /**
     * <code>optional .xddq.pb.PeakFightUnionBaseMsg unionBase = 1;</code>
     * @return Whether the unionBase field is set.
     */
    public boolean hasUnionBase() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .xddq.pb.PeakFightUnionBaseMsg unionBase = 1;</code>
     * @return The unionBase.
     */
    public xddq.pb.PeakFightUnionBaseMsg getUnionBase() {
      if (unionBaseBuilder_ == null) {
        return unionBase_ == null ? xddq.pb.PeakFightUnionBaseMsg.getDefaultInstance() : unionBase_;
      } else {
        return unionBaseBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.PeakFightUnionBaseMsg unionBase = 1;</code>
     */
    public Builder setUnionBase(xddq.pb.PeakFightUnionBaseMsg value) {
      if (unionBaseBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        unionBase_ = value;
      } else {
        unionBaseBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PeakFightUnionBaseMsg unionBase = 1;</code>
     */
    public Builder setUnionBase(
        xddq.pb.PeakFightUnionBaseMsg.Builder builderForValue) {
      if (unionBaseBuilder_ == null) {
        unionBase_ = builderForValue.build();
      } else {
        unionBaseBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PeakFightUnionBaseMsg unionBase = 1;</code>
     */
    public Builder mergeUnionBase(xddq.pb.PeakFightUnionBaseMsg value) {
      if (unionBaseBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0) &&
          unionBase_ != null &&
          unionBase_ != xddq.pb.PeakFightUnionBaseMsg.getDefaultInstance()) {
          getUnionBaseBuilder().mergeFrom(value);
        } else {
          unionBase_ = value;
        }
      } else {
        unionBaseBuilder_.mergeFrom(value);
      }
      if (unionBase_ != null) {
        bitField0_ |= 0x00000001;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.PeakFightUnionBaseMsg unionBase = 1;</code>
     */
    public Builder clearUnionBase() {
      bitField0_ = (bitField0_ & ~0x00000001);
      unionBase_ = null;
      if (unionBaseBuilder_ != null) {
        unionBaseBuilder_.dispose();
        unionBaseBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PeakFightUnionBaseMsg unionBase = 1;</code>
     */
    public xddq.pb.PeakFightUnionBaseMsg.Builder getUnionBaseBuilder() {
      bitField0_ |= 0x00000001;
      onChanged();
      return internalGetUnionBaseFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.PeakFightUnionBaseMsg unionBase = 1;</code>
     */
    public xddq.pb.PeakFightUnionBaseMsgOrBuilder getUnionBaseOrBuilder() {
      if (unionBaseBuilder_ != null) {
        return unionBaseBuilder_.getMessageOrBuilder();
      } else {
        return unionBase_ == null ?
            xddq.pb.PeakFightUnionBaseMsg.getDefaultInstance() : unionBase_;
      }
    }
    /**
     * <code>optional .xddq.pb.PeakFightUnionBaseMsg unionBase = 1;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PeakFightUnionBaseMsg, xddq.pb.PeakFightUnionBaseMsg.Builder, xddq.pb.PeakFightUnionBaseMsgOrBuilder> 
        internalGetUnionBaseFieldBuilder() {
      if (unionBaseBuilder_ == null) {
        unionBaseBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.PeakFightUnionBaseMsg, xddq.pb.PeakFightUnionBaseMsg.Builder, xddq.pb.PeakFightUnionBaseMsgOrBuilder>(
                getUnionBase(),
                getParentForChildren(),
                isClean());
        unionBase_ = null;
      }
      return unionBaseBuilder_;
    }

    private com.google.protobuf.Internal.IntList score_ = emptyIntList();
    private void ensureScoreIsMutable() {
      if (!score_.isModifiable()) {
        score_ = makeMutableCopy(score_);
      }
      bitField0_ |= 0x00000002;
    }
    /**
     * <code>repeated int32 score = 2;</code>
     * @return A list containing the score.
     */
    public java.util.List<java.lang.Integer>
        getScoreList() {
      score_.makeImmutable();
      return score_;
    }
    /**
     * <code>repeated int32 score = 2;</code>
     * @return The count of score.
     */
    public int getScoreCount() {
      return score_.size();
    }
    /**
     * <code>repeated int32 score = 2;</code>
     * @param index The index of the element to return.
     * @return The score at the given index.
     */
    public int getScore(int index) {
      return score_.getInt(index);
    }
    /**
     * <code>repeated int32 score = 2;</code>
     * @param index The index to set the value at.
     * @param value The score to set.
     * @return This builder for chaining.
     */
    public Builder setScore(
        int index, int value) {

      ensureScoreIsMutable();
      score_.setInt(index, value);
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 score = 2;</code>
     * @param value The score to add.
     * @return This builder for chaining.
     */
    public Builder addScore(int value) {

      ensureScoreIsMutable();
      score_.addInt(value);
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 score = 2;</code>
     * @param values The score to add.
     * @return This builder for chaining.
     */
    public Builder addAllScore(
        java.lang.Iterable<? extends java.lang.Integer> values) {
      ensureScoreIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, score_);
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 score = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearScore() {
      score_ = emptyIntList();
      bitField0_ = (bitField0_ & ~0x00000002);
      onChanged();
      return this;
    }

    private int result_ ;
    /**
     * <code>optional int32 result = 3;</code>
     * @return Whether the result field is set.
     */
    @java.lang.Override
    public boolean hasResult() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int32 result = 3;</code>
     * @return The result.
     */
    @java.lang.Override
    public int getResult() {
      return result_;
    }
    /**
     * <code>optional int32 result = 3;</code>
     * @param value The result to set.
     * @return This builder for chaining.
     */
    public Builder setResult(int value) {

      result_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 result = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearResult() {
      bitField0_ = (bitField0_ & ~0x00000004);
      result_ = 0;
      onChanged();
      return this;
    }

    private int addScore_ ;
    /**
     * <code>optional int32 addScore = 4;</code>
     * @return Whether the addScore field is set.
     */
    @java.lang.Override
    public boolean hasAddScore() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int32 addScore = 4;</code>
     * @return The addScore.
     */
    @java.lang.Override
    public int getAddScore() {
      return addScore_;
    }
    /**
     * <code>optional int32 addScore = 4;</code>
     * @param value The addScore to set.
     * @return This builder for chaining.
     */
    public Builder setAddScore(int value) {

      addScore_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 addScore = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearAddScore() {
      bitField0_ = (bitField0_ & ~0x00000008);
      addScore_ = 0;
      onChanged();
      return this;
    }

    private int day_ ;
    /**
     * <code>optional int32 day = 5;</code>
     * @return Whether the day field is set.
     */
    @java.lang.Override
    public boolean hasDay() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional int32 day = 5;</code>
     * @return The day.
     */
    @java.lang.Override
    public int getDay() {
      return day_;
    }
    /**
     * <code>optional int32 day = 5;</code>
     * @param value The day to set.
     * @return This builder for chaining.
     */
    public Builder setDay(int value) {

      day_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 day = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearDay() {
      bitField0_ = (bitField0_ & ~0x00000010);
      day_ = 0;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.PeakFightBattleHistoryMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.PeakFightBattleHistoryMsg)
  private static final xddq.pb.PeakFightBattleHistoryMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.PeakFightBattleHistoryMsg();
  }

  public static xddq.pb.PeakFightBattleHistoryMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<PeakFightBattleHistoryMsg>
      PARSER = new com.google.protobuf.AbstractParser<PeakFightBattleHistoryMsg>() {
    @java.lang.Override
    public PeakFightBattleHistoryMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<PeakFightBattleHistoryMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<PeakFightBattleHistoryMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.PeakFightBattleHistoryMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

