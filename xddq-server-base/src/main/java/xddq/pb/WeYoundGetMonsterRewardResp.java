// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.WeYoundGetMonsterRewardResp}
 */
public final class WeYoundGetMonsterRewardResp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.WeYoundGetMonsterRewardResp)
    WeYoundGetMonsterRewardRespOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      WeYoundGetMonsterRewardResp.class.getName());
  }
  // Use WeYoundGetMonsterRewardResp.newBuilder() to construct.
  private WeYoundGetMonsterRewardResp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private WeYoundGetMonsterRewardResp() {
    rewards_ = "";
    buffs_ = emptyIntList();
    elixirs_ = emptyIntList();
    points_ = emptyIntList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_WeYoundGetMonsterRewardResp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_WeYoundGetMonsterRewardResp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.WeYoundGetMonsterRewardResp.class, xddq.pb.WeYoundGetMonsterRewardResp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int REWARDS_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private volatile java.lang.Object rewards_ = "";
  /**
   * <code>optional string rewards = 2;</code>
   * @return Whether the rewards field is set.
   */
  @java.lang.Override
  public boolean hasRewards() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional string rewards = 2;</code>
   * @return The rewards.
   */
  @java.lang.Override
  public java.lang.String getRewards() {
    java.lang.Object ref = rewards_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        rewards_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string rewards = 2;</code>
   * @return The bytes for rewards.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getRewardsBytes() {
    java.lang.Object ref = rewards_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      rewards_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PLAYER_FIELD_NUMBER = 3;
  private xddq.pb.WeYoundPlayerMsg player_;
  /**
   * <code>optional .xddq.pb.WeYoundPlayerMsg player = 3;</code>
   * @return Whether the player field is set.
   */
  @java.lang.Override
  public boolean hasPlayer() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional .xddq.pb.WeYoundPlayerMsg player = 3;</code>
   * @return The player.
   */
  @java.lang.Override
  public xddq.pb.WeYoundPlayerMsg getPlayer() {
    return player_ == null ? xddq.pb.WeYoundPlayerMsg.getDefaultInstance() : player_;
  }
  /**
   * <code>optional .xddq.pb.WeYoundPlayerMsg player = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.WeYoundPlayerMsgOrBuilder getPlayerOrBuilder() {
    return player_ == null ? xddq.pb.WeYoundPlayerMsg.getDefaultInstance() : player_;
  }

  public static final int BUFFS_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private com.google.protobuf.Internal.IntList buffs_ =
      emptyIntList();
  /**
   * <code>repeated int32 buffs = 4;</code>
   * @return A list containing the buffs.
   */
  @java.lang.Override
  public java.util.List<java.lang.Integer>
      getBuffsList() {
    return buffs_;
  }
  /**
   * <code>repeated int32 buffs = 4;</code>
   * @return The count of buffs.
   */
  public int getBuffsCount() {
    return buffs_.size();
  }
  /**
   * <code>repeated int32 buffs = 4;</code>
   * @param index The index of the element to return.
   * @return The buffs at the given index.
   */
  public int getBuffs(int index) {
    return buffs_.getInt(index);
  }

  public static final int ELIXIRS_FIELD_NUMBER = 5;
  @SuppressWarnings("serial")
  private com.google.protobuf.Internal.IntList elixirs_ =
      emptyIntList();
  /**
   * <code>repeated int32 elixirs = 5;</code>
   * @return A list containing the elixirs.
   */
  @java.lang.Override
  public java.util.List<java.lang.Integer>
      getElixirsList() {
    return elixirs_;
  }
  /**
   * <code>repeated int32 elixirs = 5;</code>
   * @return The count of elixirs.
   */
  public int getElixirsCount() {
    return elixirs_.size();
  }
  /**
   * <code>repeated int32 elixirs = 5;</code>
   * @param index The index of the element to return.
   * @return The elixirs at the given index.
   */
  public int getElixirs(int index) {
    return elixirs_.getInt(index);
  }

  public static final int POINTS_FIELD_NUMBER = 6;
  @SuppressWarnings("serial")
  private com.google.protobuf.Internal.IntList points_ =
      emptyIntList();
  /**
   * <code>repeated int32 points = 6;</code>
   * @return A list containing the points.
   */
  @java.lang.Override
  public java.util.List<java.lang.Integer>
      getPointsList() {
    return points_;
  }
  /**
   * <code>repeated int32 points = 6;</code>
   * @return The count of points.
   */
  public int getPointsCount() {
    return points_.size();
  }
  /**
   * <code>repeated int32 points = 6;</code>
   * @param index The index of the element to return.
   * @return The points at the given index.
   */
  public int getPoints(int index) {
    return points_.getInt(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (hasPlayer()) {
      if (!getPlayer().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 2, rewards_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeMessage(3, getPlayer());
    }
    for (int i = 0; i < buffs_.size(); i++) {
      output.writeInt32(4, buffs_.getInt(i));
    }
    for (int i = 0; i < elixirs_.size(); i++) {
      output.writeInt32(5, elixirs_.getInt(i));
    }
    for (int i = 0; i < points_.size(); i++) {
      output.writeInt32(6, points_.getInt(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(2, rewards_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, getPlayer());
    }
    {
      int dataSize = 0;
      for (int i = 0; i < buffs_.size(); i++) {
        dataSize += com.google.protobuf.CodedOutputStream
          .computeInt32SizeNoTag(buffs_.getInt(i));
      }
      size += dataSize;
      size += 1 * getBuffsList().size();
    }
    {
      int dataSize = 0;
      for (int i = 0; i < elixirs_.size(); i++) {
        dataSize += com.google.protobuf.CodedOutputStream
          .computeInt32SizeNoTag(elixirs_.getInt(i));
      }
      size += dataSize;
      size += 1 * getElixirsList().size();
    }
    {
      int dataSize = 0;
      for (int i = 0; i < points_.size(); i++) {
        dataSize += com.google.protobuf.CodedOutputStream
          .computeInt32SizeNoTag(points_.getInt(i));
      }
      size += dataSize;
      size += 1 * getPointsList().size();
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.WeYoundGetMonsterRewardResp)) {
      return super.equals(obj);
    }
    xddq.pb.WeYoundGetMonsterRewardResp other = (xddq.pb.WeYoundGetMonsterRewardResp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (hasRewards() != other.hasRewards()) return false;
    if (hasRewards()) {
      if (!getRewards()
          .equals(other.getRewards())) return false;
    }
    if (hasPlayer() != other.hasPlayer()) return false;
    if (hasPlayer()) {
      if (!getPlayer()
          .equals(other.getPlayer())) return false;
    }
    if (!getBuffsList()
        .equals(other.getBuffsList())) return false;
    if (!getElixirsList()
        .equals(other.getElixirsList())) return false;
    if (!getPointsList()
        .equals(other.getPointsList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (hasRewards()) {
      hash = (37 * hash) + REWARDS_FIELD_NUMBER;
      hash = (53 * hash) + getRewards().hashCode();
    }
    if (hasPlayer()) {
      hash = (37 * hash) + PLAYER_FIELD_NUMBER;
      hash = (53 * hash) + getPlayer().hashCode();
    }
    if (getBuffsCount() > 0) {
      hash = (37 * hash) + BUFFS_FIELD_NUMBER;
      hash = (53 * hash) + getBuffsList().hashCode();
    }
    if (getElixirsCount() > 0) {
      hash = (37 * hash) + ELIXIRS_FIELD_NUMBER;
      hash = (53 * hash) + getElixirsList().hashCode();
    }
    if (getPointsCount() > 0) {
      hash = (37 * hash) + POINTS_FIELD_NUMBER;
      hash = (53 * hash) + getPointsList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.WeYoundGetMonsterRewardResp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.WeYoundGetMonsterRewardResp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.WeYoundGetMonsterRewardResp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.WeYoundGetMonsterRewardResp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.WeYoundGetMonsterRewardResp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.WeYoundGetMonsterRewardResp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.WeYoundGetMonsterRewardResp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.WeYoundGetMonsterRewardResp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.WeYoundGetMonsterRewardResp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.WeYoundGetMonsterRewardResp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.WeYoundGetMonsterRewardResp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.WeYoundGetMonsterRewardResp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.WeYoundGetMonsterRewardResp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.WeYoundGetMonsterRewardResp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.WeYoundGetMonsterRewardResp)
      xddq.pb.WeYoundGetMonsterRewardRespOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_WeYoundGetMonsterRewardResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_WeYoundGetMonsterRewardResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.WeYoundGetMonsterRewardResp.class, xddq.pb.WeYoundGetMonsterRewardResp.Builder.class);
    }

    // Construct using xddq.pb.WeYoundGetMonsterRewardResp.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetPlayerFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      rewards_ = "";
      player_ = null;
      if (playerBuilder_ != null) {
        playerBuilder_.dispose();
        playerBuilder_ = null;
      }
      buffs_ = emptyIntList();
      elixirs_ = emptyIntList();
      points_ = emptyIntList();
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_WeYoundGetMonsterRewardResp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.WeYoundGetMonsterRewardResp getDefaultInstanceForType() {
      return xddq.pb.WeYoundGetMonsterRewardResp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.WeYoundGetMonsterRewardResp build() {
      xddq.pb.WeYoundGetMonsterRewardResp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.WeYoundGetMonsterRewardResp buildPartial() {
      xddq.pb.WeYoundGetMonsterRewardResp result = new xddq.pb.WeYoundGetMonsterRewardResp(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.WeYoundGetMonsterRewardResp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.rewards_ = rewards_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.player_ = playerBuilder_ == null
            ? player_
            : playerBuilder_.build();
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        buffs_.makeImmutable();
        result.buffs_ = buffs_;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        elixirs_.makeImmutable();
        result.elixirs_ = elixirs_;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        points_.makeImmutable();
        result.points_ = points_;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.WeYoundGetMonsterRewardResp) {
        return mergeFrom((xddq.pb.WeYoundGetMonsterRewardResp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.WeYoundGetMonsterRewardResp other) {
      if (other == xddq.pb.WeYoundGetMonsterRewardResp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (other.hasRewards()) {
        rewards_ = other.rewards_;
        bitField0_ |= 0x00000002;
        onChanged();
      }
      if (other.hasPlayer()) {
        mergePlayer(other.getPlayer());
      }
      if (!other.buffs_.isEmpty()) {
        if (buffs_.isEmpty()) {
          buffs_ = other.buffs_;
          buffs_.makeImmutable();
          bitField0_ |= 0x00000008;
        } else {
          ensureBuffsIsMutable();
          buffs_.addAll(other.buffs_);
        }
        onChanged();
      }
      if (!other.elixirs_.isEmpty()) {
        if (elixirs_.isEmpty()) {
          elixirs_ = other.elixirs_;
          elixirs_.makeImmutable();
          bitField0_ |= 0x00000010;
        } else {
          ensureElixirsIsMutable();
          elixirs_.addAll(other.elixirs_);
        }
        onChanged();
      }
      if (!other.points_.isEmpty()) {
        if (points_.isEmpty()) {
          points_ = other.points_;
          points_.makeImmutable();
          bitField0_ |= 0x00000020;
        } else {
          ensurePointsIsMutable();
          points_.addAll(other.points_);
        }
        onChanged();
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      if (hasPlayer()) {
        if (!getPlayer().isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              rewards_ = input.readBytes();
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 26: {
              input.readMessage(
                  internalGetPlayerFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 32: {
              int v = input.readInt32();
              ensureBuffsIsMutable();
              buffs_.addInt(v);
              break;
            } // case 32
            case 34: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              ensureBuffsIsMutable();
              while (input.getBytesUntilLimit() > 0) {
                buffs_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            } // case 34
            case 40: {
              int v = input.readInt32();
              ensureElixirsIsMutable();
              elixirs_.addInt(v);
              break;
            } // case 40
            case 42: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              ensureElixirsIsMutable();
              while (input.getBytesUntilLimit() > 0) {
                elixirs_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            } // case 42
            case 48: {
              int v = input.readInt32();
              ensurePointsIsMutable();
              points_.addInt(v);
              break;
            } // case 48
            case 50: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              ensurePointsIsMutable();
              while (input.getBytesUntilLimit() > 0) {
                points_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            } // case 50
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object rewards_ = "";
    /**
     * <code>optional string rewards = 2;</code>
     * @return Whether the rewards field is set.
     */
    public boolean hasRewards() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional string rewards = 2;</code>
     * @return The rewards.
     */
    public java.lang.String getRewards() {
      java.lang.Object ref = rewards_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          rewards_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string rewards = 2;</code>
     * @return The bytes for rewards.
     */
    public com.google.protobuf.ByteString
        getRewardsBytes() {
      java.lang.Object ref = rewards_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        rewards_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string rewards = 2;</code>
     * @param value The rewards to set.
     * @return This builder for chaining.
     */
    public Builder setRewards(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      rewards_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional string rewards = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearRewards() {
      rewards_ = getDefaultInstance().getRewards();
      bitField0_ = (bitField0_ & ~0x00000002);
      onChanged();
      return this;
    }
    /**
     * <code>optional string rewards = 2;</code>
     * @param value The bytes for rewards to set.
     * @return This builder for chaining.
     */
    public Builder setRewardsBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      rewards_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }

    private xddq.pb.WeYoundPlayerMsg player_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.WeYoundPlayerMsg, xddq.pb.WeYoundPlayerMsg.Builder, xddq.pb.WeYoundPlayerMsgOrBuilder> playerBuilder_;
    /**
     * <code>optional .xddq.pb.WeYoundPlayerMsg player = 3;</code>
     * @return Whether the player field is set.
     */
    public boolean hasPlayer() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional .xddq.pb.WeYoundPlayerMsg player = 3;</code>
     * @return The player.
     */
    public xddq.pb.WeYoundPlayerMsg getPlayer() {
      if (playerBuilder_ == null) {
        return player_ == null ? xddq.pb.WeYoundPlayerMsg.getDefaultInstance() : player_;
      } else {
        return playerBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.WeYoundPlayerMsg player = 3;</code>
     */
    public Builder setPlayer(xddq.pb.WeYoundPlayerMsg value) {
      if (playerBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        player_ = value;
      } else {
        playerBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.WeYoundPlayerMsg player = 3;</code>
     */
    public Builder setPlayer(
        xddq.pb.WeYoundPlayerMsg.Builder builderForValue) {
      if (playerBuilder_ == null) {
        player_ = builderForValue.build();
      } else {
        playerBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.WeYoundPlayerMsg player = 3;</code>
     */
    public Builder mergePlayer(xddq.pb.WeYoundPlayerMsg value) {
      if (playerBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0) &&
          player_ != null &&
          player_ != xddq.pb.WeYoundPlayerMsg.getDefaultInstance()) {
          getPlayerBuilder().mergeFrom(value);
        } else {
          player_ = value;
        }
      } else {
        playerBuilder_.mergeFrom(value);
      }
      if (player_ != null) {
        bitField0_ |= 0x00000004;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.WeYoundPlayerMsg player = 3;</code>
     */
    public Builder clearPlayer() {
      bitField0_ = (bitField0_ & ~0x00000004);
      player_ = null;
      if (playerBuilder_ != null) {
        playerBuilder_.dispose();
        playerBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.WeYoundPlayerMsg player = 3;</code>
     */
    public xddq.pb.WeYoundPlayerMsg.Builder getPlayerBuilder() {
      bitField0_ |= 0x00000004;
      onChanged();
      return internalGetPlayerFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.WeYoundPlayerMsg player = 3;</code>
     */
    public xddq.pb.WeYoundPlayerMsgOrBuilder getPlayerOrBuilder() {
      if (playerBuilder_ != null) {
        return playerBuilder_.getMessageOrBuilder();
      } else {
        return player_ == null ?
            xddq.pb.WeYoundPlayerMsg.getDefaultInstance() : player_;
      }
    }
    /**
     * <code>optional .xddq.pb.WeYoundPlayerMsg player = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.WeYoundPlayerMsg, xddq.pb.WeYoundPlayerMsg.Builder, xddq.pb.WeYoundPlayerMsgOrBuilder> 
        internalGetPlayerFieldBuilder() {
      if (playerBuilder_ == null) {
        playerBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.WeYoundPlayerMsg, xddq.pb.WeYoundPlayerMsg.Builder, xddq.pb.WeYoundPlayerMsgOrBuilder>(
                getPlayer(),
                getParentForChildren(),
                isClean());
        player_ = null;
      }
      return playerBuilder_;
    }

    private com.google.protobuf.Internal.IntList buffs_ = emptyIntList();
    private void ensureBuffsIsMutable() {
      if (!buffs_.isModifiable()) {
        buffs_ = makeMutableCopy(buffs_);
      }
      bitField0_ |= 0x00000008;
    }
    /**
     * <code>repeated int32 buffs = 4;</code>
     * @return A list containing the buffs.
     */
    public java.util.List<java.lang.Integer>
        getBuffsList() {
      buffs_.makeImmutable();
      return buffs_;
    }
    /**
     * <code>repeated int32 buffs = 4;</code>
     * @return The count of buffs.
     */
    public int getBuffsCount() {
      return buffs_.size();
    }
    /**
     * <code>repeated int32 buffs = 4;</code>
     * @param index The index of the element to return.
     * @return The buffs at the given index.
     */
    public int getBuffs(int index) {
      return buffs_.getInt(index);
    }
    /**
     * <code>repeated int32 buffs = 4;</code>
     * @param index The index to set the value at.
     * @param value The buffs to set.
     * @return This builder for chaining.
     */
    public Builder setBuffs(
        int index, int value) {

      ensureBuffsIsMutable();
      buffs_.setInt(index, value);
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 buffs = 4;</code>
     * @param value The buffs to add.
     * @return This builder for chaining.
     */
    public Builder addBuffs(int value) {

      ensureBuffsIsMutable();
      buffs_.addInt(value);
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 buffs = 4;</code>
     * @param values The buffs to add.
     * @return This builder for chaining.
     */
    public Builder addAllBuffs(
        java.lang.Iterable<? extends java.lang.Integer> values) {
      ensureBuffsIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, buffs_);
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 buffs = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearBuffs() {
      buffs_ = emptyIntList();
      bitField0_ = (bitField0_ & ~0x00000008);
      onChanged();
      return this;
    }

    private com.google.protobuf.Internal.IntList elixirs_ = emptyIntList();
    private void ensureElixirsIsMutable() {
      if (!elixirs_.isModifiable()) {
        elixirs_ = makeMutableCopy(elixirs_);
      }
      bitField0_ |= 0x00000010;
    }
    /**
     * <code>repeated int32 elixirs = 5;</code>
     * @return A list containing the elixirs.
     */
    public java.util.List<java.lang.Integer>
        getElixirsList() {
      elixirs_.makeImmutable();
      return elixirs_;
    }
    /**
     * <code>repeated int32 elixirs = 5;</code>
     * @return The count of elixirs.
     */
    public int getElixirsCount() {
      return elixirs_.size();
    }
    /**
     * <code>repeated int32 elixirs = 5;</code>
     * @param index The index of the element to return.
     * @return The elixirs at the given index.
     */
    public int getElixirs(int index) {
      return elixirs_.getInt(index);
    }
    /**
     * <code>repeated int32 elixirs = 5;</code>
     * @param index The index to set the value at.
     * @param value The elixirs to set.
     * @return This builder for chaining.
     */
    public Builder setElixirs(
        int index, int value) {

      ensureElixirsIsMutable();
      elixirs_.setInt(index, value);
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 elixirs = 5;</code>
     * @param value The elixirs to add.
     * @return This builder for chaining.
     */
    public Builder addElixirs(int value) {

      ensureElixirsIsMutable();
      elixirs_.addInt(value);
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 elixirs = 5;</code>
     * @param values The elixirs to add.
     * @return This builder for chaining.
     */
    public Builder addAllElixirs(
        java.lang.Iterable<? extends java.lang.Integer> values) {
      ensureElixirsIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, elixirs_);
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 elixirs = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearElixirs() {
      elixirs_ = emptyIntList();
      bitField0_ = (bitField0_ & ~0x00000010);
      onChanged();
      return this;
    }

    private com.google.protobuf.Internal.IntList points_ = emptyIntList();
    private void ensurePointsIsMutable() {
      if (!points_.isModifiable()) {
        points_ = makeMutableCopy(points_);
      }
      bitField0_ |= 0x00000020;
    }
    /**
     * <code>repeated int32 points = 6;</code>
     * @return A list containing the points.
     */
    public java.util.List<java.lang.Integer>
        getPointsList() {
      points_.makeImmutable();
      return points_;
    }
    /**
     * <code>repeated int32 points = 6;</code>
     * @return The count of points.
     */
    public int getPointsCount() {
      return points_.size();
    }
    /**
     * <code>repeated int32 points = 6;</code>
     * @param index The index of the element to return.
     * @return The points at the given index.
     */
    public int getPoints(int index) {
      return points_.getInt(index);
    }
    /**
     * <code>repeated int32 points = 6;</code>
     * @param index The index to set the value at.
     * @param value The points to set.
     * @return This builder for chaining.
     */
    public Builder setPoints(
        int index, int value) {

      ensurePointsIsMutable();
      points_.setInt(index, value);
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 points = 6;</code>
     * @param value The points to add.
     * @return This builder for chaining.
     */
    public Builder addPoints(int value) {

      ensurePointsIsMutable();
      points_.addInt(value);
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 points = 6;</code>
     * @param values The points to add.
     * @return This builder for chaining.
     */
    public Builder addAllPoints(
        java.lang.Iterable<? extends java.lang.Integer> values) {
      ensurePointsIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, points_);
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 points = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearPoints() {
      points_ = emptyIntList();
      bitField0_ = (bitField0_ & ~0x00000020);
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.WeYoundGetMonsterRewardResp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.WeYoundGetMonsterRewardResp)
  private static final xddq.pb.WeYoundGetMonsterRewardResp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.WeYoundGetMonsterRewardResp();
  }

  public static xddq.pb.WeYoundGetMonsterRewardResp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<WeYoundGetMonsterRewardResp>
      PARSER = new com.google.protobuf.AbstractParser<WeYoundGetMonsterRewardResp>() {
    @java.lang.Override
    public WeYoundGetMonsterRewardResp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<WeYoundGetMonsterRewardResp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<WeYoundGetMonsterRewardResp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.WeYoundGetMonsterRewardResp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

