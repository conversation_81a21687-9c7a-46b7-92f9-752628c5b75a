// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.UnionHonorHallAchieveData}
 */
public final class UnionHonorHallAchieveData extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.UnionHonorHallAchieveData)
    UnionHonorHallAchieveDataOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      UnionHonorHallAchieveData.class.getName());
  }
  // Use UnionHonorHallAchieveData.newBuilder() to construct.
  private UnionHonorHallAchieveData(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private UnionHonorHallAchieveData() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_UnionHonorHallAchieveData_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_UnionHonorHallAchieveData_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.UnionHonorHallAchieveData.class, xddq.pb.UnionHonorHallAchieveData.Builder.class);
  }

  private int bitField0_;
  public static final int ID_FIELD_NUMBER = 1;
  private int id_ = 0;
  /**
   * <code>required int32 id = 1;</code>
   * @return Whether the id field is set.
   */
  @java.lang.Override
  public boolean hasId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 id = 1;</code>
   * @return The id.
   */
  @java.lang.Override
  public int getId() {
    return id_;
  }

  public static final int ALREADYRECEIVE_FIELD_NUMBER = 2;
  private int alreadyReceive_ = 0;
  /**
   * <code>optional int32 alreadyReceive = 2;</code>
   * @return Whether the alreadyReceive field is set.
   */
  @java.lang.Override
  public boolean hasAlreadyReceive() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int32 alreadyReceive = 2;</code>
   * @return The alreadyReceive.
   */
  @java.lang.Override
  public int getAlreadyReceive() {
    return alreadyReceive_;
  }

  public static final int ISRECEIVE_FIELD_NUMBER = 3;
  private boolean isReceive_ = false;
  /**
   * <code>optional bool isReceive = 3;</code>
   * @return Whether the isReceive field is set.
   */
  @java.lang.Override
  public boolean hasIsReceive() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional bool isReceive = 3;</code>
   * @return The isReceive.
   */
  @java.lang.Override
  public boolean getIsReceive() {
    return isReceive_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasId()) {
      memoizedIsInitialized = 0;
      return false;
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, id_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(2, alreadyReceive_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeBool(3, isReceive_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, id_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, alreadyReceive_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(3, isReceive_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.UnionHonorHallAchieveData)) {
      return super.equals(obj);
    }
    xddq.pb.UnionHonorHallAchieveData other = (xddq.pb.UnionHonorHallAchieveData) obj;

    if (hasId() != other.hasId()) return false;
    if (hasId()) {
      if (getId()
          != other.getId()) return false;
    }
    if (hasAlreadyReceive() != other.hasAlreadyReceive()) return false;
    if (hasAlreadyReceive()) {
      if (getAlreadyReceive()
          != other.getAlreadyReceive()) return false;
    }
    if (hasIsReceive() != other.hasIsReceive()) return false;
    if (hasIsReceive()) {
      if (getIsReceive()
          != other.getIsReceive()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasId()) {
      hash = (37 * hash) + ID_FIELD_NUMBER;
      hash = (53 * hash) + getId();
    }
    if (hasAlreadyReceive()) {
      hash = (37 * hash) + ALREADYRECEIVE_FIELD_NUMBER;
      hash = (53 * hash) + getAlreadyReceive();
    }
    if (hasIsReceive()) {
      hash = (37 * hash) + ISRECEIVE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getIsReceive());
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.UnionHonorHallAchieveData parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.UnionHonorHallAchieveData parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.UnionHonorHallAchieveData parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.UnionHonorHallAchieveData parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.UnionHonorHallAchieveData parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.UnionHonorHallAchieveData parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.UnionHonorHallAchieveData parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.UnionHonorHallAchieveData parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.UnionHonorHallAchieveData parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.UnionHonorHallAchieveData parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.UnionHonorHallAchieveData parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.UnionHonorHallAchieveData parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.UnionHonorHallAchieveData prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.UnionHonorHallAchieveData}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.UnionHonorHallAchieveData)
      xddq.pb.UnionHonorHallAchieveDataOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_UnionHonorHallAchieveData_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_UnionHonorHallAchieveData_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.UnionHonorHallAchieveData.class, xddq.pb.UnionHonorHallAchieveData.Builder.class);
    }

    // Construct using xddq.pb.UnionHonorHallAchieveData.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      id_ = 0;
      alreadyReceive_ = 0;
      isReceive_ = false;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_UnionHonorHallAchieveData_descriptor;
    }

    @java.lang.Override
    public xddq.pb.UnionHonorHallAchieveData getDefaultInstanceForType() {
      return xddq.pb.UnionHonorHallAchieveData.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.UnionHonorHallAchieveData build() {
      xddq.pb.UnionHonorHallAchieveData result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.UnionHonorHallAchieveData buildPartial() {
      xddq.pb.UnionHonorHallAchieveData result = new xddq.pb.UnionHonorHallAchieveData(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.UnionHonorHallAchieveData result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.id_ = id_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.alreadyReceive_ = alreadyReceive_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.isReceive_ = isReceive_;
        to_bitField0_ |= 0x00000004;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.UnionHonorHallAchieveData) {
        return mergeFrom((xddq.pb.UnionHonorHallAchieveData)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.UnionHonorHallAchieveData other) {
      if (other == xddq.pb.UnionHonorHallAchieveData.getDefaultInstance()) return this;
      if (other.hasId()) {
        setId(other.getId());
      }
      if (other.hasAlreadyReceive()) {
        setAlreadyReceive(other.getAlreadyReceive());
      }
      if (other.hasIsReceive()) {
        setIsReceive(other.getIsReceive());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasId()) {
        return false;
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              id_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              alreadyReceive_ = input.readInt32();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 24: {
              isReceive_ = input.readBool();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int id_ ;
    /**
     * <code>required int32 id = 1;</code>
     * @return Whether the id field is set.
     */
    @java.lang.Override
    public boolean hasId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 id = 1;</code>
     * @return The id.
     */
    @java.lang.Override
    public int getId() {
      return id_;
    }
    /**
     * <code>required int32 id = 1;</code>
     * @param value The id to set.
     * @return This builder for chaining.
     */
    public Builder setId(int value) {

      id_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 id = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      id_ = 0;
      onChanged();
      return this;
    }

    private int alreadyReceive_ ;
    /**
     * <code>optional int32 alreadyReceive = 2;</code>
     * @return Whether the alreadyReceive field is set.
     */
    @java.lang.Override
    public boolean hasAlreadyReceive() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 alreadyReceive = 2;</code>
     * @return The alreadyReceive.
     */
    @java.lang.Override
    public int getAlreadyReceive() {
      return alreadyReceive_;
    }
    /**
     * <code>optional int32 alreadyReceive = 2;</code>
     * @param value The alreadyReceive to set.
     * @return This builder for chaining.
     */
    public Builder setAlreadyReceive(int value) {

      alreadyReceive_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 alreadyReceive = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearAlreadyReceive() {
      bitField0_ = (bitField0_ & ~0x00000002);
      alreadyReceive_ = 0;
      onChanged();
      return this;
    }

    private boolean isReceive_ ;
    /**
     * <code>optional bool isReceive = 3;</code>
     * @return Whether the isReceive field is set.
     */
    @java.lang.Override
    public boolean hasIsReceive() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional bool isReceive = 3;</code>
     * @return The isReceive.
     */
    @java.lang.Override
    public boolean getIsReceive() {
      return isReceive_;
    }
    /**
     * <code>optional bool isReceive = 3;</code>
     * @param value The isReceive to set.
     * @return This builder for chaining.
     */
    public Builder setIsReceive(boolean value) {

      isReceive_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional bool isReceive = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearIsReceive() {
      bitField0_ = (bitField0_ & ~0x00000004);
      isReceive_ = false;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.UnionHonorHallAchieveData)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.UnionHonorHallAchieveData)
  private static final xddq.pb.UnionHonorHallAchieveData DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.UnionHonorHallAchieveData();
  }

  public static xddq.pb.UnionHonorHallAchieveData getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<UnionHonorHallAchieveData>
      PARSER = new com.google.protobuf.AbstractParser<UnionHonorHallAchieveData>() {
    @java.lang.Override
    public UnionHonorHallAchieveData parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<UnionHonorHallAchieveData> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<UnionHonorHallAchieveData> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.UnionHonorHallAchieveData getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

