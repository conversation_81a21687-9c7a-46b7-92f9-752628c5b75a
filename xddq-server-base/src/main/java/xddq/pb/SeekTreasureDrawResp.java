// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.SeekTreasureDrawResp}
 */
public final class SeekTreasureDrawResp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.SeekTreasureDrawResp)
    SeekTreasureDrawRespOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      SeekTreasureDrawResp.class.getName());
  }
  // Use SeekTreasureDrawResp.newBuilder() to construct.
  private SeekTreasureDrawResp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private SeekTreasureDrawResp() {
    drawResult_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_SeekTreasureDrawResp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_SeekTreasureDrawResp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.SeekTreasureDrawResp.class, xddq.pb.SeekTreasureDrawResp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int DRAWRESULT_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.SeekTreasureDrawResult> drawResult_;
  /**
   * <code>repeated .xddq.pb.SeekTreasureDrawResult drawResult = 2;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.SeekTreasureDrawResult> getDrawResultList() {
    return drawResult_;
  }
  /**
   * <code>repeated .xddq.pb.SeekTreasureDrawResult drawResult = 2;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.SeekTreasureDrawResultOrBuilder> 
      getDrawResultOrBuilderList() {
    return drawResult_;
  }
  /**
   * <code>repeated .xddq.pb.SeekTreasureDrawResult drawResult = 2;</code>
   */
  @java.lang.Override
  public int getDrawResultCount() {
    return drawResult_.size();
  }
  /**
   * <code>repeated .xddq.pb.SeekTreasureDrawResult drawResult = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.SeekTreasureDrawResult getDrawResult(int index) {
    return drawResult_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.SeekTreasureDrawResult drawResult = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.SeekTreasureDrawResultOrBuilder getDrawResultOrBuilder(
      int index) {
    return drawResult_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    for (int i = 0; i < getDrawResultCount(); i++) {
      if (!getDrawResult(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    for (int i = 0; i < drawResult_.size(); i++) {
      output.writeMessage(2, drawResult_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    for (int i = 0; i < drawResult_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, drawResult_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.SeekTreasureDrawResp)) {
      return super.equals(obj);
    }
    xddq.pb.SeekTreasureDrawResp other = (xddq.pb.SeekTreasureDrawResp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (!getDrawResultList()
        .equals(other.getDrawResultList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (getDrawResultCount() > 0) {
      hash = (37 * hash) + DRAWRESULT_FIELD_NUMBER;
      hash = (53 * hash) + getDrawResultList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.SeekTreasureDrawResp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.SeekTreasureDrawResp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.SeekTreasureDrawResp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.SeekTreasureDrawResp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.SeekTreasureDrawResp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.SeekTreasureDrawResp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.SeekTreasureDrawResp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.SeekTreasureDrawResp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.SeekTreasureDrawResp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.SeekTreasureDrawResp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.SeekTreasureDrawResp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.SeekTreasureDrawResp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.SeekTreasureDrawResp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.SeekTreasureDrawResp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.SeekTreasureDrawResp)
      xddq.pb.SeekTreasureDrawRespOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_SeekTreasureDrawResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_SeekTreasureDrawResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.SeekTreasureDrawResp.class, xddq.pb.SeekTreasureDrawResp.Builder.class);
    }

    // Construct using xddq.pb.SeekTreasureDrawResp.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      if (drawResultBuilder_ == null) {
        drawResult_ = java.util.Collections.emptyList();
      } else {
        drawResult_ = null;
        drawResultBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000002);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_SeekTreasureDrawResp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.SeekTreasureDrawResp getDefaultInstanceForType() {
      return xddq.pb.SeekTreasureDrawResp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.SeekTreasureDrawResp build() {
      xddq.pb.SeekTreasureDrawResp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.SeekTreasureDrawResp buildPartial() {
      xddq.pb.SeekTreasureDrawResp result = new xddq.pb.SeekTreasureDrawResp(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.SeekTreasureDrawResp result) {
      if (drawResultBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          drawResult_ = java.util.Collections.unmodifiableList(drawResult_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.drawResult_ = drawResult_;
      } else {
        result.drawResult_ = drawResultBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.SeekTreasureDrawResp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.SeekTreasureDrawResp) {
        return mergeFrom((xddq.pb.SeekTreasureDrawResp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.SeekTreasureDrawResp other) {
      if (other == xddq.pb.SeekTreasureDrawResp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (drawResultBuilder_ == null) {
        if (!other.drawResult_.isEmpty()) {
          if (drawResult_.isEmpty()) {
            drawResult_ = other.drawResult_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureDrawResultIsMutable();
            drawResult_.addAll(other.drawResult_);
          }
          onChanged();
        }
      } else {
        if (!other.drawResult_.isEmpty()) {
          if (drawResultBuilder_.isEmpty()) {
            drawResultBuilder_.dispose();
            drawResultBuilder_ = null;
            drawResult_ = other.drawResult_;
            bitField0_ = (bitField0_ & ~0x00000002);
            drawResultBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetDrawResultFieldBuilder() : null;
          } else {
            drawResultBuilder_.addAllMessages(other.drawResult_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      for (int i = 0; i < getDrawResultCount(); i++) {
        if (!getDrawResult(i).isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              xddq.pb.SeekTreasureDrawResult m =
                  input.readMessage(
                      xddq.pb.SeekTreasureDrawResult.parser(),
                      extensionRegistry);
              if (drawResultBuilder_ == null) {
                ensureDrawResultIsMutable();
                drawResult_.add(m);
              } else {
                drawResultBuilder_.addMessage(m);
              }
              break;
            } // case 18
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.SeekTreasureDrawResult> drawResult_ =
      java.util.Collections.emptyList();
    private void ensureDrawResultIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        drawResult_ = new java.util.ArrayList<xddq.pb.SeekTreasureDrawResult>(drawResult_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.SeekTreasureDrawResult, xddq.pb.SeekTreasureDrawResult.Builder, xddq.pb.SeekTreasureDrawResultOrBuilder> drawResultBuilder_;

    /**
     * <code>repeated .xddq.pb.SeekTreasureDrawResult drawResult = 2;</code>
     */
    public java.util.List<xddq.pb.SeekTreasureDrawResult> getDrawResultList() {
      if (drawResultBuilder_ == null) {
        return java.util.Collections.unmodifiableList(drawResult_);
      } else {
        return drawResultBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.SeekTreasureDrawResult drawResult = 2;</code>
     */
    public int getDrawResultCount() {
      if (drawResultBuilder_ == null) {
        return drawResult_.size();
      } else {
        return drawResultBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.SeekTreasureDrawResult drawResult = 2;</code>
     */
    public xddq.pb.SeekTreasureDrawResult getDrawResult(int index) {
      if (drawResultBuilder_ == null) {
        return drawResult_.get(index);
      } else {
        return drawResultBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.SeekTreasureDrawResult drawResult = 2;</code>
     */
    public Builder setDrawResult(
        int index, xddq.pb.SeekTreasureDrawResult value) {
      if (drawResultBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDrawResultIsMutable();
        drawResult_.set(index, value);
        onChanged();
      } else {
        drawResultBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.SeekTreasureDrawResult drawResult = 2;</code>
     */
    public Builder setDrawResult(
        int index, xddq.pb.SeekTreasureDrawResult.Builder builderForValue) {
      if (drawResultBuilder_ == null) {
        ensureDrawResultIsMutable();
        drawResult_.set(index, builderForValue.build());
        onChanged();
      } else {
        drawResultBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.SeekTreasureDrawResult drawResult = 2;</code>
     */
    public Builder addDrawResult(xddq.pb.SeekTreasureDrawResult value) {
      if (drawResultBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDrawResultIsMutable();
        drawResult_.add(value);
        onChanged();
      } else {
        drawResultBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.SeekTreasureDrawResult drawResult = 2;</code>
     */
    public Builder addDrawResult(
        int index, xddq.pb.SeekTreasureDrawResult value) {
      if (drawResultBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDrawResultIsMutable();
        drawResult_.add(index, value);
        onChanged();
      } else {
        drawResultBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.SeekTreasureDrawResult drawResult = 2;</code>
     */
    public Builder addDrawResult(
        xddq.pb.SeekTreasureDrawResult.Builder builderForValue) {
      if (drawResultBuilder_ == null) {
        ensureDrawResultIsMutable();
        drawResult_.add(builderForValue.build());
        onChanged();
      } else {
        drawResultBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.SeekTreasureDrawResult drawResult = 2;</code>
     */
    public Builder addDrawResult(
        int index, xddq.pb.SeekTreasureDrawResult.Builder builderForValue) {
      if (drawResultBuilder_ == null) {
        ensureDrawResultIsMutable();
        drawResult_.add(index, builderForValue.build());
        onChanged();
      } else {
        drawResultBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.SeekTreasureDrawResult drawResult = 2;</code>
     */
    public Builder addAllDrawResult(
        java.lang.Iterable<? extends xddq.pb.SeekTreasureDrawResult> values) {
      if (drawResultBuilder_ == null) {
        ensureDrawResultIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, drawResult_);
        onChanged();
      } else {
        drawResultBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.SeekTreasureDrawResult drawResult = 2;</code>
     */
    public Builder clearDrawResult() {
      if (drawResultBuilder_ == null) {
        drawResult_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        drawResultBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.SeekTreasureDrawResult drawResult = 2;</code>
     */
    public Builder removeDrawResult(int index) {
      if (drawResultBuilder_ == null) {
        ensureDrawResultIsMutable();
        drawResult_.remove(index);
        onChanged();
      } else {
        drawResultBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.SeekTreasureDrawResult drawResult = 2;</code>
     */
    public xddq.pb.SeekTreasureDrawResult.Builder getDrawResultBuilder(
        int index) {
      return internalGetDrawResultFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.SeekTreasureDrawResult drawResult = 2;</code>
     */
    public xddq.pb.SeekTreasureDrawResultOrBuilder getDrawResultOrBuilder(
        int index) {
      if (drawResultBuilder_ == null) {
        return drawResult_.get(index);  } else {
        return drawResultBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.SeekTreasureDrawResult drawResult = 2;</code>
     */
    public java.util.List<? extends xddq.pb.SeekTreasureDrawResultOrBuilder> 
         getDrawResultOrBuilderList() {
      if (drawResultBuilder_ != null) {
        return drawResultBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(drawResult_);
      }
    }
    /**
     * <code>repeated .xddq.pb.SeekTreasureDrawResult drawResult = 2;</code>
     */
    public xddq.pb.SeekTreasureDrawResult.Builder addDrawResultBuilder() {
      return internalGetDrawResultFieldBuilder().addBuilder(
          xddq.pb.SeekTreasureDrawResult.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.SeekTreasureDrawResult drawResult = 2;</code>
     */
    public xddq.pb.SeekTreasureDrawResult.Builder addDrawResultBuilder(
        int index) {
      return internalGetDrawResultFieldBuilder().addBuilder(
          index, xddq.pb.SeekTreasureDrawResult.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.SeekTreasureDrawResult drawResult = 2;</code>
     */
    public java.util.List<xddq.pb.SeekTreasureDrawResult.Builder> 
         getDrawResultBuilderList() {
      return internalGetDrawResultFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.SeekTreasureDrawResult, xddq.pb.SeekTreasureDrawResult.Builder, xddq.pb.SeekTreasureDrawResultOrBuilder> 
        internalGetDrawResultFieldBuilder() {
      if (drawResultBuilder_ == null) {
        drawResultBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.SeekTreasureDrawResult, xddq.pb.SeekTreasureDrawResult.Builder, xddq.pb.SeekTreasureDrawResultOrBuilder>(
                drawResult_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        drawResult_ = null;
      }
      return drawResultBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.SeekTreasureDrawResp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.SeekTreasureDrawResp)
  private static final xddq.pb.SeekTreasureDrawResp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.SeekTreasureDrawResp();
  }

  public static xddq.pb.SeekTreasureDrawResp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<SeekTreasureDrawResp>
      PARSER = new com.google.protobuf.AbstractParser<SeekTreasureDrawResp>() {
    @java.lang.Override
    public SeekTreasureDrawResp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<SeekTreasureDrawResp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<SeekTreasureDrawResp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.SeekTreasureDrawResp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

