// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

public interface GroupPurchaseExitReqOrBuilder extends
    // @@protoc_insertion_point(interface_extends:xddq.pb.GroupPurchaseExitReq)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>required int32 activityId = 1;</code>
   * @return Whether the activityId field is set.
   */
  boolean hasActivityId();
  /**
   * <code>required int32 activityId = 1;</code>
   * @return The activityId.
   */
  int getActivityId();

  /**
   * <code>optional string orderId = 2;</code>
   * @return Whether the orderId field is set.
   */
  boolean hasOrderId();
  /**
   * <code>optional string orderId = 2;</code>
   * @return The orderId.
   */
  java.lang.String getOrderId();
  /**
   * <code>optional string orderId = 2;</code>
   * @return The bytes for orderId.
   */
  com.google.protobuf.ByteString
      getOrderIdBytes();

  /**
   * <code>optional int64 playerId = 3;</code>
   * @return Whether the playerId field is set.
   */
  boolean hasPlayerId();
  /**
   * <code>optional int64 playerId = 3;</code>
   * @return The playerId.
   */
  long getPlayerId();
}
