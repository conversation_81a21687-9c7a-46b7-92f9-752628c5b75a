// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.KunlunWarGroupInfoResp}
 */
public final class KunlunWarGroupInfoResp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.KunlunWarGroupInfoResp)
    KunlunWarGroupInfoRespOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      KunlunWarGroupInfoResp.class.getName());
  }
  // Use KunlunWarGroupInfoResp.newBuilder() to construct.
  private KunlunWarGroupInfoResp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private KunlunWarGroupInfoResp() {
    groupInfo_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_KunlunWarGroupInfoResp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_KunlunWarGroupInfoResp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.KunlunWarGroupInfoResp.class, xddq.pb.KunlunWarGroupInfoResp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int GROUPINFO_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.KunlunWarGroupBaseMsg> groupInfo_;
  /**
   * <code>repeated .xddq.pb.KunlunWarGroupBaseMsg groupInfo = 2;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.KunlunWarGroupBaseMsg> getGroupInfoList() {
    return groupInfo_;
  }
  /**
   * <code>repeated .xddq.pb.KunlunWarGroupBaseMsg groupInfo = 2;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.KunlunWarGroupBaseMsgOrBuilder> 
      getGroupInfoOrBuilderList() {
    return groupInfo_;
  }
  /**
   * <code>repeated .xddq.pb.KunlunWarGroupBaseMsg groupInfo = 2;</code>
   */
  @java.lang.Override
  public int getGroupInfoCount() {
    return groupInfo_.size();
  }
  /**
   * <code>repeated .xddq.pb.KunlunWarGroupBaseMsg groupInfo = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.KunlunWarGroupBaseMsg getGroupInfo(int index) {
    return groupInfo_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.KunlunWarGroupBaseMsg groupInfo = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.KunlunWarGroupBaseMsgOrBuilder getGroupInfoOrBuilder(
      int index) {
    return groupInfo_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    for (int i = 0; i < groupInfo_.size(); i++) {
      output.writeMessage(2, groupInfo_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    for (int i = 0; i < groupInfo_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, groupInfo_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.KunlunWarGroupInfoResp)) {
      return super.equals(obj);
    }
    xddq.pb.KunlunWarGroupInfoResp other = (xddq.pb.KunlunWarGroupInfoResp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (!getGroupInfoList()
        .equals(other.getGroupInfoList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (getGroupInfoCount() > 0) {
      hash = (37 * hash) + GROUPINFO_FIELD_NUMBER;
      hash = (53 * hash) + getGroupInfoList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.KunlunWarGroupInfoResp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.KunlunWarGroupInfoResp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.KunlunWarGroupInfoResp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.KunlunWarGroupInfoResp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.KunlunWarGroupInfoResp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.KunlunWarGroupInfoResp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.KunlunWarGroupInfoResp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.KunlunWarGroupInfoResp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.KunlunWarGroupInfoResp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.KunlunWarGroupInfoResp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.KunlunWarGroupInfoResp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.KunlunWarGroupInfoResp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.KunlunWarGroupInfoResp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.KunlunWarGroupInfoResp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.KunlunWarGroupInfoResp)
      xddq.pb.KunlunWarGroupInfoRespOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_KunlunWarGroupInfoResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_KunlunWarGroupInfoResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.KunlunWarGroupInfoResp.class, xddq.pb.KunlunWarGroupInfoResp.Builder.class);
    }

    // Construct using xddq.pb.KunlunWarGroupInfoResp.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      if (groupInfoBuilder_ == null) {
        groupInfo_ = java.util.Collections.emptyList();
      } else {
        groupInfo_ = null;
        groupInfoBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000002);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_KunlunWarGroupInfoResp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.KunlunWarGroupInfoResp getDefaultInstanceForType() {
      return xddq.pb.KunlunWarGroupInfoResp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.KunlunWarGroupInfoResp build() {
      xddq.pb.KunlunWarGroupInfoResp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.KunlunWarGroupInfoResp buildPartial() {
      xddq.pb.KunlunWarGroupInfoResp result = new xddq.pb.KunlunWarGroupInfoResp(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.KunlunWarGroupInfoResp result) {
      if (groupInfoBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          groupInfo_ = java.util.Collections.unmodifiableList(groupInfo_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.groupInfo_ = groupInfo_;
      } else {
        result.groupInfo_ = groupInfoBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.KunlunWarGroupInfoResp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.KunlunWarGroupInfoResp) {
        return mergeFrom((xddq.pb.KunlunWarGroupInfoResp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.KunlunWarGroupInfoResp other) {
      if (other == xddq.pb.KunlunWarGroupInfoResp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (groupInfoBuilder_ == null) {
        if (!other.groupInfo_.isEmpty()) {
          if (groupInfo_.isEmpty()) {
            groupInfo_ = other.groupInfo_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureGroupInfoIsMutable();
            groupInfo_.addAll(other.groupInfo_);
          }
          onChanged();
        }
      } else {
        if (!other.groupInfo_.isEmpty()) {
          if (groupInfoBuilder_.isEmpty()) {
            groupInfoBuilder_.dispose();
            groupInfoBuilder_ = null;
            groupInfo_ = other.groupInfo_;
            bitField0_ = (bitField0_ & ~0x00000002);
            groupInfoBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetGroupInfoFieldBuilder() : null;
          } else {
            groupInfoBuilder_.addAllMessages(other.groupInfo_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              xddq.pb.KunlunWarGroupBaseMsg m =
                  input.readMessage(
                      xddq.pb.KunlunWarGroupBaseMsg.parser(),
                      extensionRegistry);
              if (groupInfoBuilder_ == null) {
                ensureGroupInfoIsMutable();
                groupInfo_.add(m);
              } else {
                groupInfoBuilder_.addMessage(m);
              }
              break;
            } // case 18
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.KunlunWarGroupBaseMsg> groupInfo_ =
      java.util.Collections.emptyList();
    private void ensureGroupInfoIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        groupInfo_ = new java.util.ArrayList<xddq.pb.KunlunWarGroupBaseMsg>(groupInfo_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.KunlunWarGroupBaseMsg, xddq.pb.KunlunWarGroupBaseMsg.Builder, xddq.pb.KunlunWarGroupBaseMsgOrBuilder> groupInfoBuilder_;

    /**
     * <code>repeated .xddq.pb.KunlunWarGroupBaseMsg groupInfo = 2;</code>
     */
    public java.util.List<xddq.pb.KunlunWarGroupBaseMsg> getGroupInfoList() {
      if (groupInfoBuilder_ == null) {
        return java.util.Collections.unmodifiableList(groupInfo_);
      } else {
        return groupInfoBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGroupBaseMsg groupInfo = 2;</code>
     */
    public int getGroupInfoCount() {
      if (groupInfoBuilder_ == null) {
        return groupInfo_.size();
      } else {
        return groupInfoBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGroupBaseMsg groupInfo = 2;</code>
     */
    public xddq.pb.KunlunWarGroupBaseMsg getGroupInfo(int index) {
      if (groupInfoBuilder_ == null) {
        return groupInfo_.get(index);
      } else {
        return groupInfoBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGroupBaseMsg groupInfo = 2;</code>
     */
    public Builder setGroupInfo(
        int index, xddq.pb.KunlunWarGroupBaseMsg value) {
      if (groupInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureGroupInfoIsMutable();
        groupInfo_.set(index, value);
        onChanged();
      } else {
        groupInfoBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGroupBaseMsg groupInfo = 2;</code>
     */
    public Builder setGroupInfo(
        int index, xddq.pb.KunlunWarGroupBaseMsg.Builder builderForValue) {
      if (groupInfoBuilder_ == null) {
        ensureGroupInfoIsMutable();
        groupInfo_.set(index, builderForValue.build());
        onChanged();
      } else {
        groupInfoBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGroupBaseMsg groupInfo = 2;</code>
     */
    public Builder addGroupInfo(xddq.pb.KunlunWarGroupBaseMsg value) {
      if (groupInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureGroupInfoIsMutable();
        groupInfo_.add(value);
        onChanged();
      } else {
        groupInfoBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGroupBaseMsg groupInfo = 2;</code>
     */
    public Builder addGroupInfo(
        int index, xddq.pb.KunlunWarGroupBaseMsg value) {
      if (groupInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureGroupInfoIsMutable();
        groupInfo_.add(index, value);
        onChanged();
      } else {
        groupInfoBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGroupBaseMsg groupInfo = 2;</code>
     */
    public Builder addGroupInfo(
        xddq.pb.KunlunWarGroupBaseMsg.Builder builderForValue) {
      if (groupInfoBuilder_ == null) {
        ensureGroupInfoIsMutable();
        groupInfo_.add(builderForValue.build());
        onChanged();
      } else {
        groupInfoBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGroupBaseMsg groupInfo = 2;</code>
     */
    public Builder addGroupInfo(
        int index, xddq.pb.KunlunWarGroupBaseMsg.Builder builderForValue) {
      if (groupInfoBuilder_ == null) {
        ensureGroupInfoIsMutable();
        groupInfo_.add(index, builderForValue.build());
        onChanged();
      } else {
        groupInfoBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGroupBaseMsg groupInfo = 2;</code>
     */
    public Builder addAllGroupInfo(
        java.lang.Iterable<? extends xddq.pb.KunlunWarGroupBaseMsg> values) {
      if (groupInfoBuilder_ == null) {
        ensureGroupInfoIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, groupInfo_);
        onChanged();
      } else {
        groupInfoBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGroupBaseMsg groupInfo = 2;</code>
     */
    public Builder clearGroupInfo() {
      if (groupInfoBuilder_ == null) {
        groupInfo_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        groupInfoBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGroupBaseMsg groupInfo = 2;</code>
     */
    public Builder removeGroupInfo(int index) {
      if (groupInfoBuilder_ == null) {
        ensureGroupInfoIsMutable();
        groupInfo_.remove(index);
        onChanged();
      } else {
        groupInfoBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGroupBaseMsg groupInfo = 2;</code>
     */
    public xddq.pb.KunlunWarGroupBaseMsg.Builder getGroupInfoBuilder(
        int index) {
      return internalGetGroupInfoFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGroupBaseMsg groupInfo = 2;</code>
     */
    public xddq.pb.KunlunWarGroupBaseMsgOrBuilder getGroupInfoOrBuilder(
        int index) {
      if (groupInfoBuilder_ == null) {
        return groupInfo_.get(index);  } else {
        return groupInfoBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGroupBaseMsg groupInfo = 2;</code>
     */
    public java.util.List<? extends xddq.pb.KunlunWarGroupBaseMsgOrBuilder> 
         getGroupInfoOrBuilderList() {
      if (groupInfoBuilder_ != null) {
        return groupInfoBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(groupInfo_);
      }
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGroupBaseMsg groupInfo = 2;</code>
     */
    public xddq.pb.KunlunWarGroupBaseMsg.Builder addGroupInfoBuilder() {
      return internalGetGroupInfoFieldBuilder().addBuilder(
          xddq.pb.KunlunWarGroupBaseMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGroupBaseMsg groupInfo = 2;</code>
     */
    public xddq.pb.KunlunWarGroupBaseMsg.Builder addGroupInfoBuilder(
        int index) {
      return internalGetGroupInfoFieldBuilder().addBuilder(
          index, xddq.pb.KunlunWarGroupBaseMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGroupBaseMsg groupInfo = 2;</code>
     */
    public java.util.List<xddq.pb.KunlunWarGroupBaseMsg.Builder> 
         getGroupInfoBuilderList() {
      return internalGetGroupInfoFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.KunlunWarGroupBaseMsg, xddq.pb.KunlunWarGroupBaseMsg.Builder, xddq.pb.KunlunWarGroupBaseMsgOrBuilder> 
        internalGetGroupInfoFieldBuilder() {
      if (groupInfoBuilder_ == null) {
        groupInfoBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.KunlunWarGroupBaseMsg, xddq.pb.KunlunWarGroupBaseMsg.Builder, xddq.pb.KunlunWarGroupBaseMsgOrBuilder>(
                groupInfo_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        groupInfo_ = null;
      }
      return groupInfoBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.KunlunWarGroupInfoResp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.KunlunWarGroupInfoResp)
  private static final xddq.pb.KunlunWarGroupInfoResp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.KunlunWarGroupInfoResp();
  }

  public static xddq.pb.KunlunWarGroupInfoResp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<KunlunWarGroupInfoResp>
      PARSER = new com.google.protobuf.AbstractParser<KunlunWarGroupInfoResp>() {
    @java.lang.Override
    public KunlunWarGroupInfoResp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<KunlunWarGroupInfoResp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<KunlunWarGroupInfoResp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.KunlunWarGroupInfoResp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

