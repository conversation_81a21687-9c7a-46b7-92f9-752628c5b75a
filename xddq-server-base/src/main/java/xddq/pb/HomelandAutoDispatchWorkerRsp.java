// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.HomelandAutoDispatchWorkerRsp}
 */
public final class HomelandAutoDispatchWorkerRsp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.HomelandAutoDispatchWorkerRsp)
    HomelandAutoDispatchWorkerRspOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      HomelandAutoDispatchWorkerRsp.class.getName());
  }
  // Use HomelandAutoDispatchWorkerRsp.newBuilder() to construct.
  private HomelandAutoDispatchWorkerRsp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private HomelandAutoDispatchWorkerRsp() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_HomelandAutoDispatchWorkerRsp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_HomelandAutoDispatchWorkerRsp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.HomelandAutoDispatchWorkerRsp.class, xddq.pb.HomelandAutoDispatchWorkerRsp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int NEEDSTOP_FIELD_NUMBER = 2;
  private boolean needStop_ = false;
  /**
   * <code>optional bool needStop = 2;</code>
   * @return Whether the needStop field is set.
   */
  @java.lang.Override
  public boolean hasNeedStop() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional bool needStop = 2;</code>
   * @return The needStop.
   */
  @java.lang.Override
  public boolean getNeedStop() {
    return needStop_;
  }

  public static final int NEXTTIME_FIELD_NUMBER = 3;
  private long nextTime_ = 0L;
  /**
   * <code>optional int64 nextTime = 3;</code>
   * @return Whether the nextTime field is set.
   */
  @java.lang.Override
  public boolean hasNextTime() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional int64 nextTime = 3;</code>
   * @return The nextTime.
   */
  @java.lang.Override
  public long getNextTime() {
    return nextTime_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeBool(2, needStop_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt64(3, nextTime_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(2, needStop_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(3, nextTime_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.HomelandAutoDispatchWorkerRsp)) {
      return super.equals(obj);
    }
    xddq.pb.HomelandAutoDispatchWorkerRsp other = (xddq.pb.HomelandAutoDispatchWorkerRsp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (hasNeedStop() != other.hasNeedStop()) return false;
    if (hasNeedStop()) {
      if (getNeedStop()
          != other.getNeedStop()) return false;
    }
    if (hasNextTime() != other.hasNextTime()) return false;
    if (hasNextTime()) {
      if (getNextTime()
          != other.getNextTime()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (hasNeedStop()) {
      hash = (37 * hash) + NEEDSTOP_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getNeedStop());
    }
    if (hasNextTime()) {
      hash = (37 * hash) + NEXTTIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getNextTime());
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.HomelandAutoDispatchWorkerRsp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.HomelandAutoDispatchWorkerRsp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.HomelandAutoDispatchWorkerRsp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.HomelandAutoDispatchWorkerRsp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.HomelandAutoDispatchWorkerRsp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.HomelandAutoDispatchWorkerRsp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.HomelandAutoDispatchWorkerRsp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.HomelandAutoDispatchWorkerRsp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.HomelandAutoDispatchWorkerRsp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.HomelandAutoDispatchWorkerRsp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.HomelandAutoDispatchWorkerRsp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.HomelandAutoDispatchWorkerRsp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.HomelandAutoDispatchWorkerRsp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.HomelandAutoDispatchWorkerRsp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.HomelandAutoDispatchWorkerRsp)
      xddq.pb.HomelandAutoDispatchWorkerRspOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_HomelandAutoDispatchWorkerRsp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_HomelandAutoDispatchWorkerRsp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.HomelandAutoDispatchWorkerRsp.class, xddq.pb.HomelandAutoDispatchWorkerRsp.Builder.class);
    }

    // Construct using xddq.pb.HomelandAutoDispatchWorkerRsp.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      needStop_ = false;
      nextTime_ = 0L;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_HomelandAutoDispatchWorkerRsp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.HomelandAutoDispatchWorkerRsp getDefaultInstanceForType() {
      return xddq.pb.HomelandAutoDispatchWorkerRsp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.HomelandAutoDispatchWorkerRsp build() {
      xddq.pb.HomelandAutoDispatchWorkerRsp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.HomelandAutoDispatchWorkerRsp buildPartial() {
      xddq.pb.HomelandAutoDispatchWorkerRsp result = new xddq.pb.HomelandAutoDispatchWorkerRsp(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.HomelandAutoDispatchWorkerRsp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.needStop_ = needStop_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.nextTime_ = nextTime_;
        to_bitField0_ |= 0x00000004;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.HomelandAutoDispatchWorkerRsp) {
        return mergeFrom((xddq.pb.HomelandAutoDispatchWorkerRsp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.HomelandAutoDispatchWorkerRsp other) {
      if (other == xddq.pb.HomelandAutoDispatchWorkerRsp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (other.hasNeedStop()) {
        setNeedStop(other.getNeedStop());
      }
      if (other.hasNextTime()) {
        setNextTime(other.getNextTime());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              needStop_ = input.readBool();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 24: {
              nextTime_ = input.readInt64();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private boolean needStop_ ;
    /**
     * <code>optional bool needStop = 2;</code>
     * @return Whether the needStop field is set.
     */
    @java.lang.Override
    public boolean hasNeedStop() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional bool needStop = 2;</code>
     * @return The needStop.
     */
    @java.lang.Override
    public boolean getNeedStop() {
      return needStop_;
    }
    /**
     * <code>optional bool needStop = 2;</code>
     * @param value The needStop to set.
     * @return This builder for chaining.
     */
    public Builder setNeedStop(boolean value) {

      needStop_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional bool needStop = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearNeedStop() {
      bitField0_ = (bitField0_ & ~0x00000002);
      needStop_ = false;
      onChanged();
      return this;
    }

    private long nextTime_ ;
    /**
     * <code>optional int64 nextTime = 3;</code>
     * @return Whether the nextTime field is set.
     */
    @java.lang.Override
    public boolean hasNextTime() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int64 nextTime = 3;</code>
     * @return The nextTime.
     */
    @java.lang.Override
    public long getNextTime() {
      return nextTime_;
    }
    /**
     * <code>optional int64 nextTime = 3;</code>
     * @param value The nextTime to set.
     * @return This builder for chaining.
     */
    public Builder setNextTime(long value) {

      nextTime_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 nextTime = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearNextTime() {
      bitField0_ = (bitField0_ & ~0x00000004);
      nextTime_ = 0L;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.HomelandAutoDispatchWorkerRsp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.HomelandAutoDispatchWorkerRsp)
  private static final xddq.pb.HomelandAutoDispatchWorkerRsp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.HomelandAutoDispatchWorkerRsp();
  }

  public static xddq.pb.HomelandAutoDispatchWorkerRsp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<HomelandAutoDispatchWorkerRsp>
      PARSER = new com.google.protobuf.AbstractParser<HomelandAutoDispatchWorkerRsp>() {
    @java.lang.Override
    public HomelandAutoDispatchWorkerRsp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<HomelandAutoDispatchWorkerRsp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<HomelandAutoDispatchWorkerRsp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.HomelandAutoDispatchWorkerRsp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

