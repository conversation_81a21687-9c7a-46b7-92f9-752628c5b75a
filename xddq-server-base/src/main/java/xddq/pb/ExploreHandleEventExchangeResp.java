// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.ExploreHandleEventExchangeResp}
 */
public final class ExploreHandleEventExchangeResp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.ExploreHandleEventExchangeResp)
    ExploreHandleEventExchangeRespOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      ExploreHandleEventExchangeResp.class.getName());
  }
  // Use ExploreHandleEventExchangeResp.newBuilder() to construct.
  private ExploreHandleEventExchangeResp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private ExploreHandleEventExchangeResp() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_ExploreHandleEventExchangeResp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_ExploreHandleEventExchangeResp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.ExploreHandleEventExchangeResp.class, xddq.pb.ExploreHandleEventExchangeResp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>optional int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int STAGE_FIELD_NUMBER = 2;
  private xddq.pb.ExploreStageMsg stage_;
  /**
   * <code>optional .xddq.pb.ExploreStageMsg stage = 2;</code>
   * @return Whether the stage field is set.
   */
  @java.lang.Override
  public boolean hasStage() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional .xddq.pb.ExploreStageMsg stage = 2;</code>
   * @return The stage.
   */
  @java.lang.Override
  public xddq.pb.ExploreStageMsg getStage() {
    return stage_ == null ? xddq.pb.ExploreStageMsg.getDefaultInstance() : stage_;
  }
  /**
   * <code>optional .xddq.pb.ExploreStageMsg stage = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.ExploreStageMsgOrBuilder getStageOrBuilder() {
    return stage_ == null ? xddq.pb.ExploreStageMsg.getDefaultInstance() : stage_;
  }

  public static final int END_FIELD_NUMBER = 3;
  private xddq.pb.ExploreEndMsg end_;
  /**
   * <code>optional .xddq.pb.ExploreEndMsg end = 3;</code>
   * @return Whether the end field is set.
   */
  @java.lang.Override
  public boolean hasEnd() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional .xddq.pb.ExploreEndMsg end = 3;</code>
   * @return The end.
   */
  @java.lang.Override
  public xddq.pb.ExploreEndMsg getEnd() {
    return end_ == null ? xddq.pb.ExploreEndMsg.getDefaultInstance() : end_;
  }
  /**
   * <code>optional .xddq.pb.ExploreEndMsg end = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.ExploreEndMsgOrBuilder getEndOrBuilder() {
    return end_ == null ? xddq.pb.ExploreEndMsg.getDefaultInstance() : end_;
  }

  public static final int SHOW_FIELD_NUMBER = 4;
  private xddq.pb.ExploreEventResultShow show_;
  /**
   * <code>optional .xddq.pb.ExploreEventResultShow show = 4;</code>
   * @return Whether the show field is set.
   */
  @java.lang.Override
  public boolean hasShow() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional .xddq.pb.ExploreEventResultShow show = 4;</code>
   * @return The show.
   */
  @java.lang.Override
  public xddq.pb.ExploreEventResultShow getShow() {
    return show_ == null ? xddq.pb.ExploreEventResultShow.getDefaultInstance() : show_;
  }
  /**
   * <code>optional .xddq.pb.ExploreEventResultShow show = 4;</code>
   */
  @java.lang.Override
  public xddq.pb.ExploreEventResultShowOrBuilder getShowOrBuilder() {
    return show_ == null ? xddq.pb.ExploreEventResultShow.getDefaultInstance() : show_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (hasStage()) {
      if (!getStage().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeMessage(2, getStage());
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeMessage(3, getEnd());
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeMessage(4, getShow());
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getStage());
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, getEnd());
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(4, getShow());
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.ExploreHandleEventExchangeResp)) {
      return super.equals(obj);
    }
    xddq.pb.ExploreHandleEventExchangeResp other = (xddq.pb.ExploreHandleEventExchangeResp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (hasStage() != other.hasStage()) return false;
    if (hasStage()) {
      if (!getStage()
          .equals(other.getStage())) return false;
    }
    if (hasEnd() != other.hasEnd()) return false;
    if (hasEnd()) {
      if (!getEnd()
          .equals(other.getEnd())) return false;
    }
    if (hasShow() != other.hasShow()) return false;
    if (hasShow()) {
      if (!getShow()
          .equals(other.getShow())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (hasStage()) {
      hash = (37 * hash) + STAGE_FIELD_NUMBER;
      hash = (53 * hash) + getStage().hashCode();
    }
    if (hasEnd()) {
      hash = (37 * hash) + END_FIELD_NUMBER;
      hash = (53 * hash) + getEnd().hashCode();
    }
    if (hasShow()) {
      hash = (37 * hash) + SHOW_FIELD_NUMBER;
      hash = (53 * hash) + getShow().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.ExploreHandleEventExchangeResp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ExploreHandleEventExchangeResp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ExploreHandleEventExchangeResp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ExploreHandleEventExchangeResp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ExploreHandleEventExchangeResp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ExploreHandleEventExchangeResp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ExploreHandleEventExchangeResp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.ExploreHandleEventExchangeResp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.ExploreHandleEventExchangeResp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.ExploreHandleEventExchangeResp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.ExploreHandleEventExchangeResp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.ExploreHandleEventExchangeResp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.ExploreHandleEventExchangeResp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.ExploreHandleEventExchangeResp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.ExploreHandleEventExchangeResp)
      xddq.pb.ExploreHandleEventExchangeRespOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ExploreHandleEventExchangeResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ExploreHandleEventExchangeResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.ExploreHandleEventExchangeResp.class, xddq.pb.ExploreHandleEventExchangeResp.Builder.class);
    }

    // Construct using xddq.pb.ExploreHandleEventExchangeResp.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetStageFieldBuilder();
        internalGetEndFieldBuilder();
        internalGetShowFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      stage_ = null;
      if (stageBuilder_ != null) {
        stageBuilder_.dispose();
        stageBuilder_ = null;
      }
      end_ = null;
      if (endBuilder_ != null) {
        endBuilder_.dispose();
        endBuilder_ = null;
      }
      show_ = null;
      if (showBuilder_ != null) {
        showBuilder_.dispose();
        showBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ExploreHandleEventExchangeResp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.ExploreHandleEventExchangeResp getDefaultInstanceForType() {
      return xddq.pb.ExploreHandleEventExchangeResp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.ExploreHandleEventExchangeResp build() {
      xddq.pb.ExploreHandleEventExchangeResp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.ExploreHandleEventExchangeResp buildPartial() {
      xddq.pb.ExploreHandleEventExchangeResp result = new xddq.pb.ExploreHandleEventExchangeResp(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.ExploreHandleEventExchangeResp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.stage_ = stageBuilder_ == null
            ? stage_
            : stageBuilder_.build();
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.end_ = endBuilder_ == null
            ? end_
            : endBuilder_.build();
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.show_ = showBuilder_ == null
            ? show_
            : showBuilder_.build();
        to_bitField0_ |= 0x00000008;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.ExploreHandleEventExchangeResp) {
        return mergeFrom((xddq.pb.ExploreHandleEventExchangeResp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.ExploreHandleEventExchangeResp other) {
      if (other == xddq.pb.ExploreHandleEventExchangeResp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (other.hasStage()) {
        mergeStage(other.getStage());
      }
      if (other.hasEnd()) {
        mergeEnd(other.getEnd());
      }
      if (other.hasShow()) {
        mergeShow(other.getShow());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (hasStage()) {
        if (!getStage().isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              input.readMessage(
                  internalGetStageFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 26: {
              input.readMessage(
                  internalGetEndFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 34: {
              input.readMessage(
                  internalGetShowFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000008;
              break;
            } // case 34
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>optional int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>optional int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private xddq.pb.ExploreStageMsg stage_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.ExploreStageMsg, xddq.pb.ExploreStageMsg.Builder, xddq.pb.ExploreStageMsgOrBuilder> stageBuilder_;
    /**
     * <code>optional .xddq.pb.ExploreStageMsg stage = 2;</code>
     * @return Whether the stage field is set.
     */
    public boolean hasStage() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .xddq.pb.ExploreStageMsg stage = 2;</code>
     * @return The stage.
     */
    public xddq.pb.ExploreStageMsg getStage() {
      if (stageBuilder_ == null) {
        return stage_ == null ? xddq.pb.ExploreStageMsg.getDefaultInstance() : stage_;
      } else {
        return stageBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.ExploreStageMsg stage = 2;</code>
     */
    public Builder setStage(xddq.pb.ExploreStageMsg value) {
      if (stageBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        stage_ = value;
      } else {
        stageBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ExploreStageMsg stage = 2;</code>
     */
    public Builder setStage(
        xddq.pb.ExploreStageMsg.Builder builderForValue) {
      if (stageBuilder_ == null) {
        stage_ = builderForValue.build();
      } else {
        stageBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ExploreStageMsg stage = 2;</code>
     */
    public Builder mergeStage(xddq.pb.ExploreStageMsg value) {
      if (stageBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0) &&
          stage_ != null &&
          stage_ != xddq.pb.ExploreStageMsg.getDefaultInstance()) {
          getStageBuilder().mergeFrom(value);
        } else {
          stage_ = value;
        }
      } else {
        stageBuilder_.mergeFrom(value);
      }
      if (stage_ != null) {
        bitField0_ |= 0x00000002;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.ExploreStageMsg stage = 2;</code>
     */
    public Builder clearStage() {
      bitField0_ = (bitField0_ & ~0x00000002);
      stage_ = null;
      if (stageBuilder_ != null) {
        stageBuilder_.dispose();
        stageBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ExploreStageMsg stage = 2;</code>
     */
    public xddq.pb.ExploreStageMsg.Builder getStageBuilder() {
      bitField0_ |= 0x00000002;
      onChanged();
      return internalGetStageFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.ExploreStageMsg stage = 2;</code>
     */
    public xddq.pb.ExploreStageMsgOrBuilder getStageOrBuilder() {
      if (stageBuilder_ != null) {
        return stageBuilder_.getMessageOrBuilder();
      } else {
        return stage_ == null ?
            xddq.pb.ExploreStageMsg.getDefaultInstance() : stage_;
      }
    }
    /**
     * <code>optional .xddq.pb.ExploreStageMsg stage = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.ExploreStageMsg, xddq.pb.ExploreStageMsg.Builder, xddq.pb.ExploreStageMsgOrBuilder> 
        internalGetStageFieldBuilder() {
      if (stageBuilder_ == null) {
        stageBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.ExploreStageMsg, xddq.pb.ExploreStageMsg.Builder, xddq.pb.ExploreStageMsgOrBuilder>(
                getStage(),
                getParentForChildren(),
                isClean());
        stage_ = null;
      }
      return stageBuilder_;
    }

    private xddq.pb.ExploreEndMsg end_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.ExploreEndMsg, xddq.pb.ExploreEndMsg.Builder, xddq.pb.ExploreEndMsgOrBuilder> endBuilder_;
    /**
     * <code>optional .xddq.pb.ExploreEndMsg end = 3;</code>
     * @return Whether the end field is set.
     */
    public boolean hasEnd() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional .xddq.pb.ExploreEndMsg end = 3;</code>
     * @return The end.
     */
    public xddq.pb.ExploreEndMsg getEnd() {
      if (endBuilder_ == null) {
        return end_ == null ? xddq.pb.ExploreEndMsg.getDefaultInstance() : end_;
      } else {
        return endBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.ExploreEndMsg end = 3;</code>
     */
    public Builder setEnd(xddq.pb.ExploreEndMsg value) {
      if (endBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        end_ = value;
      } else {
        endBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ExploreEndMsg end = 3;</code>
     */
    public Builder setEnd(
        xddq.pb.ExploreEndMsg.Builder builderForValue) {
      if (endBuilder_ == null) {
        end_ = builderForValue.build();
      } else {
        endBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ExploreEndMsg end = 3;</code>
     */
    public Builder mergeEnd(xddq.pb.ExploreEndMsg value) {
      if (endBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0) &&
          end_ != null &&
          end_ != xddq.pb.ExploreEndMsg.getDefaultInstance()) {
          getEndBuilder().mergeFrom(value);
        } else {
          end_ = value;
        }
      } else {
        endBuilder_.mergeFrom(value);
      }
      if (end_ != null) {
        bitField0_ |= 0x00000004;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.ExploreEndMsg end = 3;</code>
     */
    public Builder clearEnd() {
      bitField0_ = (bitField0_ & ~0x00000004);
      end_ = null;
      if (endBuilder_ != null) {
        endBuilder_.dispose();
        endBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ExploreEndMsg end = 3;</code>
     */
    public xddq.pb.ExploreEndMsg.Builder getEndBuilder() {
      bitField0_ |= 0x00000004;
      onChanged();
      return internalGetEndFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.ExploreEndMsg end = 3;</code>
     */
    public xddq.pb.ExploreEndMsgOrBuilder getEndOrBuilder() {
      if (endBuilder_ != null) {
        return endBuilder_.getMessageOrBuilder();
      } else {
        return end_ == null ?
            xddq.pb.ExploreEndMsg.getDefaultInstance() : end_;
      }
    }
    /**
     * <code>optional .xddq.pb.ExploreEndMsg end = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.ExploreEndMsg, xddq.pb.ExploreEndMsg.Builder, xddq.pb.ExploreEndMsgOrBuilder> 
        internalGetEndFieldBuilder() {
      if (endBuilder_ == null) {
        endBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.ExploreEndMsg, xddq.pb.ExploreEndMsg.Builder, xddq.pb.ExploreEndMsgOrBuilder>(
                getEnd(),
                getParentForChildren(),
                isClean());
        end_ = null;
      }
      return endBuilder_;
    }

    private xddq.pb.ExploreEventResultShow show_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.ExploreEventResultShow, xddq.pb.ExploreEventResultShow.Builder, xddq.pb.ExploreEventResultShowOrBuilder> showBuilder_;
    /**
     * <code>optional .xddq.pb.ExploreEventResultShow show = 4;</code>
     * @return Whether the show field is set.
     */
    public boolean hasShow() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional .xddq.pb.ExploreEventResultShow show = 4;</code>
     * @return The show.
     */
    public xddq.pb.ExploreEventResultShow getShow() {
      if (showBuilder_ == null) {
        return show_ == null ? xddq.pb.ExploreEventResultShow.getDefaultInstance() : show_;
      } else {
        return showBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.ExploreEventResultShow show = 4;</code>
     */
    public Builder setShow(xddq.pb.ExploreEventResultShow value) {
      if (showBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        show_ = value;
      } else {
        showBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ExploreEventResultShow show = 4;</code>
     */
    public Builder setShow(
        xddq.pb.ExploreEventResultShow.Builder builderForValue) {
      if (showBuilder_ == null) {
        show_ = builderForValue.build();
      } else {
        showBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ExploreEventResultShow show = 4;</code>
     */
    public Builder mergeShow(xddq.pb.ExploreEventResultShow value) {
      if (showBuilder_ == null) {
        if (((bitField0_ & 0x00000008) != 0) &&
          show_ != null &&
          show_ != xddq.pb.ExploreEventResultShow.getDefaultInstance()) {
          getShowBuilder().mergeFrom(value);
        } else {
          show_ = value;
        }
      } else {
        showBuilder_.mergeFrom(value);
      }
      if (show_ != null) {
        bitField0_ |= 0x00000008;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.ExploreEventResultShow show = 4;</code>
     */
    public Builder clearShow() {
      bitField0_ = (bitField0_ & ~0x00000008);
      show_ = null;
      if (showBuilder_ != null) {
        showBuilder_.dispose();
        showBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ExploreEventResultShow show = 4;</code>
     */
    public xddq.pb.ExploreEventResultShow.Builder getShowBuilder() {
      bitField0_ |= 0x00000008;
      onChanged();
      return internalGetShowFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.ExploreEventResultShow show = 4;</code>
     */
    public xddq.pb.ExploreEventResultShowOrBuilder getShowOrBuilder() {
      if (showBuilder_ != null) {
        return showBuilder_.getMessageOrBuilder();
      } else {
        return show_ == null ?
            xddq.pb.ExploreEventResultShow.getDefaultInstance() : show_;
      }
    }
    /**
     * <code>optional .xddq.pb.ExploreEventResultShow show = 4;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.ExploreEventResultShow, xddq.pb.ExploreEventResultShow.Builder, xddq.pb.ExploreEventResultShowOrBuilder> 
        internalGetShowFieldBuilder() {
      if (showBuilder_ == null) {
        showBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.ExploreEventResultShow, xddq.pb.ExploreEventResultShow.Builder, xddq.pb.ExploreEventResultShowOrBuilder>(
                getShow(),
                getParentForChildren(),
                isClean());
        show_ = null;
      }
      return showBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.ExploreHandleEventExchangeResp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.ExploreHandleEventExchangeResp)
  private static final xddq.pb.ExploreHandleEventExchangeResp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.ExploreHandleEventExchangeResp();
  }

  public static xddq.pb.ExploreHandleEventExchangeResp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ExploreHandleEventExchangeResp>
      PARSER = new com.google.protobuf.AbstractParser<ExploreHandleEventExchangeResp>() {
    @java.lang.Override
    public ExploreHandleEventExchangeResp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<ExploreHandleEventExchangeResp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ExploreHandleEventExchangeResp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.ExploreHandleEventExchangeResp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

