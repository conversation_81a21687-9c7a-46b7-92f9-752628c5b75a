// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

public interface TeamBuyActivityDataOrBuilder extends
    // @@protoc_insertion_point(interface_extends:xddq.pb.TeamBuyActivityData)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>repeated .xddq.pb.TeamBuyhasBuyMallInfo hasBuyMallInfos = 1;</code>
   */
  java.util.List<xddq.pb.TeamBuyhasBuyMallInfo> 
      getHasBuyMallInfosList();
  /**
   * <code>repeated .xddq.pb.TeamBuyhasBuyMallInfo hasBuyMallInfos = 1;</code>
   */
  xddq.pb.TeamBuyhasBuyMallInfo getHasBuyMallInfos(int index);
  /**
   * <code>repeated .xddq.pb.TeamBuyhasBuyMallInfo hasBuyMallInfos = 1;</code>
   */
  int getHasBuyMallInfosCount();
  /**
   * <code>repeated .xddq.pb.TeamBuyhasBuyMallInfo hasBuyMallInfos = 1;</code>
   */
  java.util.List<? extends xddq.pb.TeamBuyhasBuyMallInfoOrBuilder> 
      getHasBuyMallInfosOrBuilderList();
  /**
   * <code>repeated .xddq.pb.TeamBuyhasBuyMallInfo hasBuyMallInfos = 1;</code>
   */
  xddq.pb.TeamBuyhasBuyMallInfoOrBuilder getHasBuyMallInfosOrBuilder(
      int index);

  /**
   * <code>repeated .xddq.pb.TeamBuyMallCountInfo mallCountInfos = 2;</code>
   */
  java.util.List<xddq.pb.TeamBuyMallCountInfo> 
      getMallCountInfosList();
  /**
   * <code>repeated .xddq.pb.TeamBuyMallCountInfo mallCountInfos = 2;</code>
   */
  xddq.pb.TeamBuyMallCountInfo getMallCountInfos(int index);
  /**
   * <code>repeated .xddq.pb.TeamBuyMallCountInfo mallCountInfos = 2;</code>
   */
  int getMallCountInfosCount();
  /**
   * <code>repeated .xddq.pb.TeamBuyMallCountInfo mallCountInfos = 2;</code>
   */
  java.util.List<? extends xddq.pb.TeamBuyMallCountInfoOrBuilder> 
      getMallCountInfosOrBuilderList();
  /**
   * <code>repeated .xddq.pb.TeamBuyMallCountInfo mallCountInfos = 2;</code>
   */
  xddq.pb.TeamBuyMallCountInfoOrBuilder getMallCountInfosOrBuilder(
      int index);
}
