// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.KunlunWarConfigSync}
 */
public final class KunlunWarConfigSync extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.KunlunWarConfigSync)
    KunlunWarConfigSyncOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      KunlunWarConfigSync.class.getName());
  }
  // Use KunlunWarConfigSync.newBuilder() to construct.
  private KunlunWarConfigSync(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private KunlunWarConfigSync() {
    peakHoldTimeConfigs_ = java.util.Collections.emptyList();
    groupConfig_ = java.util.Collections.emptyList();
    placeConfig_ = java.util.Collections.emptyList();
    eventConfig_ = java.util.Collections.emptyList();
    attrConfig_ = java.util.Collections.emptyList();
    treasureConfig_ = java.util.Collections.emptyList();
    cityConfig_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_KunlunWarConfigSync_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_KunlunWarConfigSync_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.KunlunWarConfigSync.class, xddq.pb.KunlunWarConfigSync.Builder.class);
  }

  public static final int PEAKHOLDTIMECONFIGS_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.ActivityPeakHoldTimeConfig> peakHoldTimeConfigs_;
  /**
   * <code>repeated .xddq.pb.ActivityPeakHoldTimeConfig peakHoldTimeConfigs = 1;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.ActivityPeakHoldTimeConfig> getPeakHoldTimeConfigsList() {
    return peakHoldTimeConfigs_;
  }
  /**
   * <code>repeated .xddq.pb.ActivityPeakHoldTimeConfig peakHoldTimeConfigs = 1;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.ActivityPeakHoldTimeConfigOrBuilder> 
      getPeakHoldTimeConfigsOrBuilderList() {
    return peakHoldTimeConfigs_;
  }
  /**
   * <code>repeated .xddq.pb.ActivityPeakHoldTimeConfig peakHoldTimeConfigs = 1;</code>
   */
  @java.lang.Override
  public int getPeakHoldTimeConfigsCount() {
    return peakHoldTimeConfigs_.size();
  }
  /**
   * <code>repeated .xddq.pb.ActivityPeakHoldTimeConfig peakHoldTimeConfigs = 1;</code>
   */
  @java.lang.Override
  public xddq.pb.ActivityPeakHoldTimeConfig getPeakHoldTimeConfigs(int index) {
    return peakHoldTimeConfigs_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.ActivityPeakHoldTimeConfig peakHoldTimeConfigs = 1;</code>
   */
  @java.lang.Override
  public xddq.pb.ActivityPeakHoldTimeConfigOrBuilder getPeakHoldTimeConfigsOrBuilder(
      int index) {
    return peakHoldTimeConfigs_.get(index);
  }

  public static final int GROUPCONFIG_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.KunlunWarGroupConfig> groupConfig_;
  /**
   * <code>repeated .xddq.pb.KunlunWarGroupConfig groupConfig = 2;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.KunlunWarGroupConfig> getGroupConfigList() {
    return groupConfig_;
  }
  /**
   * <code>repeated .xddq.pb.KunlunWarGroupConfig groupConfig = 2;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.KunlunWarGroupConfigOrBuilder> 
      getGroupConfigOrBuilderList() {
    return groupConfig_;
  }
  /**
   * <code>repeated .xddq.pb.KunlunWarGroupConfig groupConfig = 2;</code>
   */
  @java.lang.Override
  public int getGroupConfigCount() {
    return groupConfig_.size();
  }
  /**
   * <code>repeated .xddq.pb.KunlunWarGroupConfig groupConfig = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.KunlunWarGroupConfig getGroupConfig(int index) {
    return groupConfig_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.KunlunWarGroupConfig groupConfig = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.KunlunWarGroupConfigOrBuilder getGroupConfigOrBuilder(
      int index) {
    return groupConfig_.get(index);
  }

  public static final int PLACECONFIG_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.KunlunWarPlaceConfig> placeConfig_;
  /**
   * <code>repeated .xddq.pb.KunlunWarPlaceConfig placeConfig = 3;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.KunlunWarPlaceConfig> getPlaceConfigList() {
    return placeConfig_;
  }
  /**
   * <code>repeated .xddq.pb.KunlunWarPlaceConfig placeConfig = 3;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.KunlunWarPlaceConfigOrBuilder> 
      getPlaceConfigOrBuilderList() {
    return placeConfig_;
  }
  /**
   * <code>repeated .xddq.pb.KunlunWarPlaceConfig placeConfig = 3;</code>
   */
  @java.lang.Override
  public int getPlaceConfigCount() {
    return placeConfig_.size();
  }
  /**
   * <code>repeated .xddq.pb.KunlunWarPlaceConfig placeConfig = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.KunlunWarPlaceConfig getPlaceConfig(int index) {
    return placeConfig_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.KunlunWarPlaceConfig placeConfig = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.KunlunWarPlaceConfigOrBuilder getPlaceConfigOrBuilder(
      int index) {
    return placeConfig_.get(index);
  }

  public static final int EVENTCONFIG_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.KunlunWarEventConfig> eventConfig_;
  /**
   * <code>repeated .xddq.pb.KunlunWarEventConfig eventConfig = 4;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.KunlunWarEventConfig> getEventConfigList() {
    return eventConfig_;
  }
  /**
   * <code>repeated .xddq.pb.KunlunWarEventConfig eventConfig = 4;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.KunlunWarEventConfigOrBuilder> 
      getEventConfigOrBuilderList() {
    return eventConfig_;
  }
  /**
   * <code>repeated .xddq.pb.KunlunWarEventConfig eventConfig = 4;</code>
   */
  @java.lang.Override
  public int getEventConfigCount() {
    return eventConfig_.size();
  }
  /**
   * <code>repeated .xddq.pb.KunlunWarEventConfig eventConfig = 4;</code>
   */
  @java.lang.Override
  public xddq.pb.KunlunWarEventConfig getEventConfig(int index) {
    return eventConfig_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.KunlunWarEventConfig eventConfig = 4;</code>
   */
  @java.lang.Override
  public xddq.pb.KunlunWarEventConfigOrBuilder getEventConfigOrBuilder(
      int index) {
    return eventConfig_.get(index);
  }

  public static final int ATTRCONFIG_FIELD_NUMBER = 5;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.KunlunWarAttrConfig> attrConfig_;
  /**
   * <code>repeated .xddq.pb.KunlunWarAttrConfig attrConfig = 5;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.KunlunWarAttrConfig> getAttrConfigList() {
    return attrConfig_;
  }
  /**
   * <code>repeated .xddq.pb.KunlunWarAttrConfig attrConfig = 5;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.KunlunWarAttrConfigOrBuilder> 
      getAttrConfigOrBuilderList() {
    return attrConfig_;
  }
  /**
   * <code>repeated .xddq.pb.KunlunWarAttrConfig attrConfig = 5;</code>
   */
  @java.lang.Override
  public int getAttrConfigCount() {
    return attrConfig_.size();
  }
  /**
   * <code>repeated .xddq.pb.KunlunWarAttrConfig attrConfig = 5;</code>
   */
  @java.lang.Override
  public xddq.pb.KunlunWarAttrConfig getAttrConfig(int index) {
    return attrConfig_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.KunlunWarAttrConfig attrConfig = 5;</code>
   */
  @java.lang.Override
  public xddq.pb.KunlunWarAttrConfigOrBuilder getAttrConfigOrBuilder(
      int index) {
    return attrConfig_.get(index);
  }

  public static final int TREASURECONFIG_FIELD_NUMBER = 6;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.KunlunWarTreasureConfig> treasureConfig_;
  /**
   * <code>repeated .xddq.pb.KunlunWarTreasureConfig treasureConfig = 6;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.KunlunWarTreasureConfig> getTreasureConfigList() {
    return treasureConfig_;
  }
  /**
   * <code>repeated .xddq.pb.KunlunWarTreasureConfig treasureConfig = 6;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.KunlunWarTreasureConfigOrBuilder> 
      getTreasureConfigOrBuilderList() {
    return treasureConfig_;
  }
  /**
   * <code>repeated .xddq.pb.KunlunWarTreasureConfig treasureConfig = 6;</code>
   */
  @java.lang.Override
  public int getTreasureConfigCount() {
    return treasureConfig_.size();
  }
  /**
   * <code>repeated .xddq.pb.KunlunWarTreasureConfig treasureConfig = 6;</code>
   */
  @java.lang.Override
  public xddq.pb.KunlunWarTreasureConfig getTreasureConfig(int index) {
    return treasureConfig_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.KunlunWarTreasureConfig treasureConfig = 6;</code>
   */
  @java.lang.Override
  public xddq.pb.KunlunWarTreasureConfigOrBuilder getTreasureConfigOrBuilder(
      int index) {
    return treasureConfig_.get(index);
  }

  public static final int CITYCONFIG_FIELD_NUMBER = 7;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.KunlunWarCityConfig> cityConfig_;
  /**
   * <code>repeated .xddq.pb.KunlunWarCityConfig cityConfig = 7;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.KunlunWarCityConfig> getCityConfigList() {
    return cityConfig_;
  }
  /**
   * <code>repeated .xddq.pb.KunlunWarCityConfig cityConfig = 7;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.KunlunWarCityConfigOrBuilder> 
      getCityConfigOrBuilderList() {
    return cityConfig_;
  }
  /**
   * <code>repeated .xddq.pb.KunlunWarCityConfig cityConfig = 7;</code>
   */
  @java.lang.Override
  public int getCityConfigCount() {
    return cityConfig_.size();
  }
  /**
   * <code>repeated .xddq.pb.KunlunWarCityConfig cityConfig = 7;</code>
   */
  @java.lang.Override
  public xddq.pb.KunlunWarCityConfig getCityConfig(int index) {
    return cityConfig_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.KunlunWarCityConfig cityConfig = 7;</code>
   */
  @java.lang.Override
  public xddq.pb.KunlunWarCityConfigOrBuilder getCityConfigOrBuilder(
      int index) {
    return cityConfig_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    for (int i = 0; i < getPeakHoldTimeConfigsCount(); i++) {
      if (!getPeakHoldTimeConfigs(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    for (int i = 0; i < getGroupConfigCount(); i++) {
      if (!getGroupConfig(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    for (int i = 0; i < getPlaceConfigCount(); i++) {
      if (!getPlaceConfig(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    for (int i = 0; i < getEventConfigCount(); i++) {
      if (!getEventConfig(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    for (int i = 0; i < getAttrConfigCount(); i++) {
      if (!getAttrConfig(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    for (int i = 0; i < getTreasureConfigCount(); i++) {
      if (!getTreasureConfig(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    for (int i = 0; i < getCityConfigCount(); i++) {
      if (!getCityConfig(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    for (int i = 0; i < peakHoldTimeConfigs_.size(); i++) {
      output.writeMessage(1, peakHoldTimeConfigs_.get(i));
    }
    for (int i = 0; i < groupConfig_.size(); i++) {
      output.writeMessage(2, groupConfig_.get(i));
    }
    for (int i = 0; i < placeConfig_.size(); i++) {
      output.writeMessage(3, placeConfig_.get(i));
    }
    for (int i = 0; i < eventConfig_.size(); i++) {
      output.writeMessage(4, eventConfig_.get(i));
    }
    for (int i = 0; i < attrConfig_.size(); i++) {
      output.writeMessage(5, attrConfig_.get(i));
    }
    for (int i = 0; i < treasureConfig_.size(); i++) {
      output.writeMessage(6, treasureConfig_.get(i));
    }
    for (int i = 0; i < cityConfig_.size(); i++) {
      output.writeMessage(7, cityConfig_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    for (int i = 0; i < peakHoldTimeConfigs_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, peakHoldTimeConfigs_.get(i));
    }
    for (int i = 0; i < groupConfig_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, groupConfig_.get(i));
    }
    for (int i = 0; i < placeConfig_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, placeConfig_.get(i));
    }
    for (int i = 0; i < eventConfig_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(4, eventConfig_.get(i));
    }
    for (int i = 0; i < attrConfig_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(5, attrConfig_.get(i));
    }
    for (int i = 0; i < treasureConfig_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(6, treasureConfig_.get(i));
    }
    for (int i = 0; i < cityConfig_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(7, cityConfig_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.KunlunWarConfigSync)) {
      return super.equals(obj);
    }
    xddq.pb.KunlunWarConfigSync other = (xddq.pb.KunlunWarConfigSync) obj;

    if (!getPeakHoldTimeConfigsList()
        .equals(other.getPeakHoldTimeConfigsList())) return false;
    if (!getGroupConfigList()
        .equals(other.getGroupConfigList())) return false;
    if (!getPlaceConfigList()
        .equals(other.getPlaceConfigList())) return false;
    if (!getEventConfigList()
        .equals(other.getEventConfigList())) return false;
    if (!getAttrConfigList()
        .equals(other.getAttrConfigList())) return false;
    if (!getTreasureConfigList()
        .equals(other.getTreasureConfigList())) return false;
    if (!getCityConfigList()
        .equals(other.getCityConfigList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (getPeakHoldTimeConfigsCount() > 0) {
      hash = (37 * hash) + PEAKHOLDTIMECONFIGS_FIELD_NUMBER;
      hash = (53 * hash) + getPeakHoldTimeConfigsList().hashCode();
    }
    if (getGroupConfigCount() > 0) {
      hash = (37 * hash) + GROUPCONFIG_FIELD_NUMBER;
      hash = (53 * hash) + getGroupConfigList().hashCode();
    }
    if (getPlaceConfigCount() > 0) {
      hash = (37 * hash) + PLACECONFIG_FIELD_NUMBER;
      hash = (53 * hash) + getPlaceConfigList().hashCode();
    }
    if (getEventConfigCount() > 0) {
      hash = (37 * hash) + EVENTCONFIG_FIELD_NUMBER;
      hash = (53 * hash) + getEventConfigList().hashCode();
    }
    if (getAttrConfigCount() > 0) {
      hash = (37 * hash) + ATTRCONFIG_FIELD_NUMBER;
      hash = (53 * hash) + getAttrConfigList().hashCode();
    }
    if (getTreasureConfigCount() > 0) {
      hash = (37 * hash) + TREASURECONFIG_FIELD_NUMBER;
      hash = (53 * hash) + getTreasureConfigList().hashCode();
    }
    if (getCityConfigCount() > 0) {
      hash = (37 * hash) + CITYCONFIG_FIELD_NUMBER;
      hash = (53 * hash) + getCityConfigList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.KunlunWarConfigSync parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.KunlunWarConfigSync parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.KunlunWarConfigSync parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.KunlunWarConfigSync parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.KunlunWarConfigSync parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.KunlunWarConfigSync parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.KunlunWarConfigSync parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.KunlunWarConfigSync parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.KunlunWarConfigSync parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.KunlunWarConfigSync parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.KunlunWarConfigSync parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.KunlunWarConfigSync parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.KunlunWarConfigSync prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.KunlunWarConfigSync}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.KunlunWarConfigSync)
      xddq.pb.KunlunWarConfigSyncOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_KunlunWarConfigSync_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_KunlunWarConfigSync_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.KunlunWarConfigSync.class, xddq.pb.KunlunWarConfigSync.Builder.class);
    }

    // Construct using xddq.pb.KunlunWarConfigSync.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      if (peakHoldTimeConfigsBuilder_ == null) {
        peakHoldTimeConfigs_ = java.util.Collections.emptyList();
      } else {
        peakHoldTimeConfigs_ = null;
        peakHoldTimeConfigsBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000001);
      if (groupConfigBuilder_ == null) {
        groupConfig_ = java.util.Collections.emptyList();
      } else {
        groupConfig_ = null;
        groupConfigBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000002);
      if (placeConfigBuilder_ == null) {
        placeConfig_ = java.util.Collections.emptyList();
      } else {
        placeConfig_ = null;
        placeConfigBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000004);
      if (eventConfigBuilder_ == null) {
        eventConfig_ = java.util.Collections.emptyList();
      } else {
        eventConfig_ = null;
        eventConfigBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000008);
      if (attrConfigBuilder_ == null) {
        attrConfig_ = java.util.Collections.emptyList();
      } else {
        attrConfig_ = null;
        attrConfigBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000010);
      if (treasureConfigBuilder_ == null) {
        treasureConfig_ = java.util.Collections.emptyList();
      } else {
        treasureConfig_ = null;
        treasureConfigBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000020);
      if (cityConfigBuilder_ == null) {
        cityConfig_ = java.util.Collections.emptyList();
      } else {
        cityConfig_ = null;
        cityConfigBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000040);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_KunlunWarConfigSync_descriptor;
    }

    @java.lang.Override
    public xddq.pb.KunlunWarConfigSync getDefaultInstanceForType() {
      return xddq.pb.KunlunWarConfigSync.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.KunlunWarConfigSync build() {
      xddq.pb.KunlunWarConfigSync result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.KunlunWarConfigSync buildPartial() {
      xddq.pb.KunlunWarConfigSync result = new xddq.pb.KunlunWarConfigSync(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.KunlunWarConfigSync result) {
      if (peakHoldTimeConfigsBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          peakHoldTimeConfigs_ = java.util.Collections.unmodifiableList(peakHoldTimeConfigs_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.peakHoldTimeConfigs_ = peakHoldTimeConfigs_;
      } else {
        result.peakHoldTimeConfigs_ = peakHoldTimeConfigsBuilder_.build();
      }
      if (groupConfigBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          groupConfig_ = java.util.Collections.unmodifiableList(groupConfig_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.groupConfig_ = groupConfig_;
      } else {
        result.groupConfig_ = groupConfigBuilder_.build();
      }
      if (placeConfigBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0)) {
          placeConfig_ = java.util.Collections.unmodifiableList(placeConfig_);
          bitField0_ = (bitField0_ & ~0x00000004);
        }
        result.placeConfig_ = placeConfig_;
      } else {
        result.placeConfig_ = placeConfigBuilder_.build();
      }
      if (eventConfigBuilder_ == null) {
        if (((bitField0_ & 0x00000008) != 0)) {
          eventConfig_ = java.util.Collections.unmodifiableList(eventConfig_);
          bitField0_ = (bitField0_ & ~0x00000008);
        }
        result.eventConfig_ = eventConfig_;
      } else {
        result.eventConfig_ = eventConfigBuilder_.build();
      }
      if (attrConfigBuilder_ == null) {
        if (((bitField0_ & 0x00000010) != 0)) {
          attrConfig_ = java.util.Collections.unmodifiableList(attrConfig_);
          bitField0_ = (bitField0_ & ~0x00000010);
        }
        result.attrConfig_ = attrConfig_;
      } else {
        result.attrConfig_ = attrConfigBuilder_.build();
      }
      if (treasureConfigBuilder_ == null) {
        if (((bitField0_ & 0x00000020) != 0)) {
          treasureConfig_ = java.util.Collections.unmodifiableList(treasureConfig_);
          bitField0_ = (bitField0_ & ~0x00000020);
        }
        result.treasureConfig_ = treasureConfig_;
      } else {
        result.treasureConfig_ = treasureConfigBuilder_.build();
      }
      if (cityConfigBuilder_ == null) {
        if (((bitField0_ & 0x00000040) != 0)) {
          cityConfig_ = java.util.Collections.unmodifiableList(cityConfig_);
          bitField0_ = (bitField0_ & ~0x00000040);
        }
        result.cityConfig_ = cityConfig_;
      } else {
        result.cityConfig_ = cityConfigBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.KunlunWarConfigSync result) {
      int from_bitField0_ = bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.KunlunWarConfigSync) {
        return mergeFrom((xddq.pb.KunlunWarConfigSync)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.KunlunWarConfigSync other) {
      if (other == xddq.pb.KunlunWarConfigSync.getDefaultInstance()) return this;
      if (peakHoldTimeConfigsBuilder_ == null) {
        if (!other.peakHoldTimeConfigs_.isEmpty()) {
          if (peakHoldTimeConfigs_.isEmpty()) {
            peakHoldTimeConfigs_ = other.peakHoldTimeConfigs_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensurePeakHoldTimeConfigsIsMutable();
            peakHoldTimeConfigs_.addAll(other.peakHoldTimeConfigs_);
          }
          onChanged();
        }
      } else {
        if (!other.peakHoldTimeConfigs_.isEmpty()) {
          if (peakHoldTimeConfigsBuilder_.isEmpty()) {
            peakHoldTimeConfigsBuilder_.dispose();
            peakHoldTimeConfigsBuilder_ = null;
            peakHoldTimeConfigs_ = other.peakHoldTimeConfigs_;
            bitField0_ = (bitField0_ & ~0x00000001);
            peakHoldTimeConfigsBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetPeakHoldTimeConfigsFieldBuilder() : null;
          } else {
            peakHoldTimeConfigsBuilder_.addAllMessages(other.peakHoldTimeConfigs_);
          }
        }
      }
      if (groupConfigBuilder_ == null) {
        if (!other.groupConfig_.isEmpty()) {
          if (groupConfig_.isEmpty()) {
            groupConfig_ = other.groupConfig_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureGroupConfigIsMutable();
            groupConfig_.addAll(other.groupConfig_);
          }
          onChanged();
        }
      } else {
        if (!other.groupConfig_.isEmpty()) {
          if (groupConfigBuilder_.isEmpty()) {
            groupConfigBuilder_.dispose();
            groupConfigBuilder_ = null;
            groupConfig_ = other.groupConfig_;
            bitField0_ = (bitField0_ & ~0x00000002);
            groupConfigBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetGroupConfigFieldBuilder() : null;
          } else {
            groupConfigBuilder_.addAllMessages(other.groupConfig_);
          }
        }
      }
      if (placeConfigBuilder_ == null) {
        if (!other.placeConfig_.isEmpty()) {
          if (placeConfig_.isEmpty()) {
            placeConfig_ = other.placeConfig_;
            bitField0_ = (bitField0_ & ~0x00000004);
          } else {
            ensurePlaceConfigIsMutable();
            placeConfig_.addAll(other.placeConfig_);
          }
          onChanged();
        }
      } else {
        if (!other.placeConfig_.isEmpty()) {
          if (placeConfigBuilder_.isEmpty()) {
            placeConfigBuilder_.dispose();
            placeConfigBuilder_ = null;
            placeConfig_ = other.placeConfig_;
            bitField0_ = (bitField0_ & ~0x00000004);
            placeConfigBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetPlaceConfigFieldBuilder() : null;
          } else {
            placeConfigBuilder_.addAllMessages(other.placeConfig_);
          }
        }
      }
      if (eventConfigBuilder_ == null) {
        if (!other.eventConfig_.isEmpty()) {
          if (eventConfig_.isEmpty()) {
            eventConfig_ = other.eventConfig_;
            bitField0_ = (bitField0_ & ~0x00000008);
          } else {
            ensureEventConfigIsMutable();
            eventConfig_.addAll(other.eventConfig_);
          }
          onChanged();
        }
      } else {
        if (!other.eventConfig_.isEmpty()) {
          if (eventConfigBuilder_.isEmpty()) {
            eventConfigBuilder_.dispose();
            eventConfigBuilder_ = null;
            eventConfig_ = other.eventConfig_;
            bitField0_ = (bitField0_ & ~0x00000008);
            eventConfigBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetEventConfigFieldBuilder() : null;
          } else {
            eventConfigBuilder_.addAllMessages(other.eventConfig_);
          }
        }
      }
      if (attrConfigBuilder_ == null) {
        if (!other.attrConfig_.isEmpty()) {
          if (attrConfig_.isEmpty()) {
            attrConfig_ = other.attrConfig_;
            bitField0_ = (bitField0_ & ~0x00000010);
          } else {
            ensureAttrConfigIsMutable();
            attrConfig_.addAll(other.attrConfig_);
          }
          onChanged();
        }
      } else {
        if (!other.attrConfig_.isEmpty()) {
          if (attrConfigBuilder_.isEmpty()) {
            attrConfigBuilder_.dispose();
            attrConfigBuilder_ = null;
            attrConfig_ = other.attrConfig_;
            bitField0_ = (bitField0_ & ~0x00000010);
            attrConfigBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetAttrConfigFieldBuilder() : null;
          } else {
            attrConfigBuilder_.addAllMessages(other.attrConfig_);
          }
        }
      }
      if (treasureConfigBuilder_ == null) {
        if (!other.treasureConfig_.isEmpty()) {
          if (treasureConfig_.isEmpty()) {
            treasureConfig_ = other.treasureConfig_;
            bitField0_ = (bitField0_ & ~0x00000020);
          } else {
            ensureTreasureConfigIsMutable();
            treasureConfig_.addAll(other.treasureConfig_);
          }
          onChanged();
        }
      } else {
        if (!other.treasureConfig_.isEmpty()) {
          if (treasureConfigBuilder_.isEmpty()) {
            treasureConfigBuilder_.dispose();
            treasureConfigBuilder_ = null;
            treasureConfig_ = other.treasureConfig_;
            bitField0_ = (bitField0_ & ~0x00000020);
            treasureConfigBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetTreasureConfigFieldBuilder() : null;
          } else {
            treasureConfigBuilder_.addAllMessages(other.treasureConfig_);
          }
        }
      }
      if (cityConfigBuilder_ == null) {
        if (!other.cityConfig_.isEmpty()) {
          if (cityConfig_.isEmpty()) {
            cityConfig_ = other.cityConfig_;
            bitField0_ = (bitField0_ & ~0x00000040);
          } else {
            ensureCityConfigIsMutable();
            cityConfig_.addAll(other.cityConfig_);
          }
          onChanged();
        }
      } else {
        if (!other.cityConfig_.isEmpty()) {
          if (cityConfigBuilder_.isEmpty()) {
            cityConfigBuilder_.dispose();
            cityConfigBuilder_ = null;
            cityConfig_ = other.cityConfig_;
            bitField0_ = (bitField0_ & ~0x00000040);
            cityConfigBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetCityConfigFieldBuilder() : null;
          } else {
            cityConfigBuilder_.addAllMessages(other.cityConfig_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      for (int i = 0; i < getPeakHoldTimeConfigsCount(); i++) {
        if (!getPeakHoldTimeConfigs(i).isInitialized()) {
          return false;
        }
      }
      for (int i = 0; i < getGroupConfigCount(); i++) {
        if (!getGroupConfig(i).isInitialized()) {
          return false;
        }
      }
      for (int i = 0; i < getPlaceConfigCount(); i++) {
        if (!getPlaceConfig(i).isInitialized()) {
          return false;
        }
      }
      for (int i = 0; i < getEventConfigCount(); i++) {
        if (!getEventConfig(i).isInitialized()) {
          return false;
        }
      }
      for (int i = 0; i < getAttrConfigCount(); i++) {
        if (!getAttrConfig(i).isInitialized()) {
          return false;
        }
      }
      for (int i = 0; i < getTreasureConfigCount(); i++) {
        if (!getTreasureConfig(i).isInitialized()) {
          return false;
        }
      }
      for (int i = 0; i < getCityConfigCount(); i++) {
        if (!getCityConfig(i).isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              xddq.pb.ActivityPeakHoldTimeConfig m =
                  input.readMessage(
                      xddq.pb.ActivityPeakHoldTimeConfig.parser(),
                      extensionRegistry);
              if (peakHoldTimeConfigsBuilder_ == null) {
                ensurePeakHoldTimeConfigsIsMutable();
                peakHoldTimeConfigs_.add(m);
              } else {
                peakHoldTimeConfigsBuilder_.addMessage(m);
              }
              break;
            } // case 10
            case 18: {
              xddq.pb.KunlunWarGroupConfig m =
                  input.readMessage(
                      xddq.pb.KunlunWarGroupConfig.parser(),
                      extensionRegistry);
              if (groupConfigBuilder_ == null) {
                ensureGroupConfigIsMutable();
                groupConfig_.add(m);
              } else {
                groupConfigBuilder_.addMessage(m);
              }
              break;
            } // case 18
            case 26: {
              xddq.pb.KunlunWarPlaceConfig m =
                  input.readMessage(
                      xddq.pb.KunlunWarPlaceConfig.parser(),
                      extensionRegistry);
              if (placeConfigBuilder_ == null) {
                ensurePlaceConfigIsMutable();
                placeConfig_.add(m);
              } else {
                placeConfigBuilder_.addMessage(m);
              }
              break;
            } // case 26
            case 34: {
              xddq.pb.KunlunWarEventConfig m =
                  input.readMessage(
                      xddq.pb.KunlunWarEventConfig.parser(),
                      extensionRegistry);
              if (eventConfigBuilder_ == null) {
                ensureEventConfigIsMutable();
                eventConfig_.add(m);
              } else {
                eventConfigBuilder_.addMessage(m);
              }
              break;
            } // case 34
            case 42: {
              xddq.pb.KunlunWarAttrConfig m =
                  input.readMessage(
                      xddq.pb.KunlunWarAttrConfig.parser(),
                      extensionRegistry);
              if (attrConfigBuilder_ == null) {
                ensureAttrConfigIsMutable();
                attrConfig_.add(m);
              } else {
                attrConfigBuilder_.addMessage(m);
              }
              break;
            } // case 42
            case 50: {
              xddq.pb.KunlunWarTreasureConfig m =
                  input.readMessage(
                      xddq.pb.KunlunWarTreasureConfig.parser(),
                      extensionRegistry);
              if (treasureConfigBuilder_ == null) {
                ensureTreasureConfigIsMutable();
                treasureConfig_.add(m);
              } else {
                treasureConfigBuilder_.addMessage(m);
              }
              break;
            } // case 50
            case 58: {
              xddq.pb.KunlunWarCityConfig m =
                  input.readMessage(
                      xddq.pb.KunlunWarCityConfig.parser(),
                      extensionRegistry);
              if (cityConfigBuilder_ == null) {
                ensureCityConfigIsMutable();
                cityConfig_.add(m);
              } else {
                cityConfigBuilder_.addMessage(m);
              }
              break;
            } // case 58
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.util.List<xddq.pb.ActivityPeakHoldTimeConfig> peakHoldTimeConfigs_ =
      java.util.Collections.emptyList();
    private void ensurePeakHoldTimeConfigsIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        peakHoldTimeConfigs_ = new java.util.ArrayList<xddq.pb.ActivityPeakHoldTimeConfig>(peakHoldTimeConfigs_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.ActivityPeakHoldTimeConfig, xddq.pb.ActivityPeakHoldTimeConfig.Builder, xddq.pb.ActivityPeakHoldTimeConfigOrBuilder> peakHoldTimeConfigsBuilder_;

    /**
     * <code>repeated .xddq.pb.ActivityPeakHoldTimeConfig peakHoldTimeConfigs = 1;</code>
     */
    public java.util.List<xddq.pb.ActivityPeakHoldTimeConfig> getPeakHoldTimeConfigsList() {
      if (peakHoldTimeConfigsBuilder_ == null) {
        return java.util.Collections.unmodifiableList(peakHoldTimeConfigs_);
      } else {
        return peakHoldTimeConfigsBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.ActivityPeakHoldTimeConfig peakHoldTimeConfigs = 1;</code>
     */
    public int getPeakHoldTimeConfigsCount() {
      if (peakHoldTimeConfigsBuilder_ == null) {
        return peakHoldTimeConfigs_.size();
      } else {
        return peakHoldTimeConfigsBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.ActivityPeakHoldTimeConfig peakHoldTimeConfigs = 1;</code>
     */
    public xddq.pb.ActivityPeakHoldTimeConfig getPeakHoldTimeConfigs(int index) {
      if (peakHoldTimeConfigsBuilder_ == null) {
        return peakHoldTimeConfigs_.get(index);
      } else {
        return peakHoldTimeConfigsBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.ActivityPeakHoldTimeConfig peakHoldTimeConfigs = 1;</code>
     */
    public Builder setPeakHoldTimeConfigs(
        int index, xddq.pb.ActivityPeakHoldTimeConfig value) {
      if (peakHoldTimeConfigsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePeakHoldTimeConfigsIsMutable();
        peakHoldTimeConfigs_.set(index, value);
        onChanged();
      } else {
        peakHoldTimeConfigsBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ActivityPeakHoldTimeConfig peakHoldTimeConfigs = 1;</code>
     */
    public Builder setPeakHoldTimeConfigs(
        int index, xddq.pb.ActivityPeakHoldTimeConfig.Builder builderForValue) {
      if (peakHoldTimeConfigsBuilder_ == null) {
        ensurePeakHoldTimeConfigsIsMutable();
        peakHoldTimeConfigs_.set(index, builderForValue.build());
        onChanged();
      } else {
        peakHoldTimeConfigsBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ActivityPeakHoldTimeConfig peakHoldTimeConfigs = 1;</code>
     */
    public Builder addPeakHoldTimeConfigs(xddq.pb.ActivityPeakHoldTimeConfig value) {
      if (peakHoldTimeConfigsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePeakHoldTimeConfigsIsMutable();
        peakHoldTimeConfigs_.add(value);
        onChanged();
      } else {
        peakHoldTimeConfigsBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ActivityPeakHoldTimeConfig peakHoldTimeConfigs = 1;</code>
     */
    public Builder addPeakHoldTimeConfigs(
        int index, xddq.pb.ActivityPeakHoldTimeConfig value) {
      if (peakHoldTimeConfigsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePeakHoldTimeConfigsIsMutable();
        peakHoldTimeConfigs_.add(index, value);
        onChanged();
      } else {
        peakHoldTimeConfigsBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ActivityPeakHoldTimeConfig peakHoldTimeConfigs = 1;</code>
     */
    public Builder addPeakHoldTimeConfigs(
        xddq.pb.ActivityPeakHoldTimeConfig.Builder builderForValue) {
      if (peakHoldTimeConfigsBuilder_ == null) {
        ensurePeakHoldTimeConfigsIsMutable();
        peakHoldTimeConfigs_.add(builderForValue.build());
        onChanged();
      } else {
        peakHoldTimeConfigsBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ActivityPeakHoldTimeConfig peakHoldTimeConfigs = 1;</code>
     */
    public Builder addPeakHoldTimeConfigs(
        int index, xddq.pb.ActivityPeakHoldTimeConfig.Builder builderForValue) {
      if (peakHoldTimeConfigsBuilder_ == null) {
        ensurePeakHoldTimeConfigsIsMutable();
        peakHoldTimeConfigs_.add(index, builderForValue.build());
        onChanged();
      } else {
        peakHoldTimeConfigsBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ActivityPeakHoldTimeConfig peakHoldTimeConfigs = 1;</code>
     */
    public Builder addAllPeakHoldTimeConfigs(
        java.lang.Iterable<? extends xddq.pb.ActivityPeakHoldTimeConfig> values) {
      if (peakHoldTimeConfigsBuilder_ == null) {
        ensurePeakHoldTimeConfigsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, peakHoldTimeConfigs_);
        onChanged();
      } else {
        peakHoldTimeConfigsBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ActivityPeakHoldTimeConfig peakHoldTimeConfigs = 1;</code>
     */
    public Builder clearPeakHoldTimeConfigs() {
      if (peakHoldTimeConfigsBuilder_ == null) {
        peakHoldTimeConfigs_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        peakHoldTimeConfigsBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ActivityPeakHoldTimeConfig peakHoldTimeConfigs = 1;</code>
     */
    public Builder removePeakHoldTimeConfigs(int index) {
      if (peakHoldTimeConfigsBuilder_ == null) {
        ensurePeakHoldTimeConfigsIsMutable();
        peakHoldTimeConfigs_.remove(index);
        onChanged();
      } else {
        peakHoldTimeConfigsBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ActivityPeakHoldTimeConfig peakHoldTimeConfigs = 1;</code>
     */
    public xddq.pb.ActivityPeakHoldTimeConfig.Builder getPeakHoldTimeConfigsBuilder(
        int index) {
      return internalGetPeakHoldTimeConfigsFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.ActivityPeakHoldTimeConfig peakHoldTimeConfigs = 1;</code>
     */
    public xddq.pb.ActivityPeakHoldTimeConfigOrBuilder getPeakHoldTimeConfigsOrBuilder(
        int index) {
      if (peakHoldTimeConfigsBuilder_ == null) {
        return peakHoldTimeConfigs_.get(index);  } else {
        return peakHoldTimeConfigsBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.ActivityPeakHoldTimeConfig peakHoldTimeConfigs = 1;</code>
     */
    public java.util.List<? extends xddq.pb.ActivityPeakHoldTimeConfigOrBuilder> 
         getPeakHoldTimeConfigsOrBuilderList() {
      if (peakHoldTimeConfigsBuilder_ != null) {
        return peakHoldTimeConfigsBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(peakHoldTimeConfigs_);
      }
    }
    /**
     * <code>repeated .xddq.pb.ActivityPeakHoldTimeConfig peakHoldTimeConfigs = 1;</code>
     */
    public xddq.pb.ActivityPeakHoldTimeConfig.Builder addPeakHoldTimeConfigsBuilder() {
      return internalGetPeakHoldTimeConfigsFieldBuilder().addBuilder(
          xddq.pb.ActivityPeakHoldTimeConfig.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.ActivityPeakHoldTimeConfig peakHoldTimeConfigs = 1;</code>
     */
    public xddq.pb.ActivityPeakHoldTimeConfig.Builder addPeakHoldTimeConfigsBuilder(
        int index) {
      return internalGetPeakHoldTimeConfigsFieldBuilder().addBuilder(
          index, xddq.pb.ActivityPeakHoldTimeConfig.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.ActivityPeakHoldTimeConfig peakHoldTimeConfigs = 1;</code>
     */
    public java.util.List<xddq.pb.ActivityPeakHoldTimeConfig.Builder> 
         getPeakHoldTimeConfigsBuilderList() {
      return internalGetPeakHoldTimeConfigsFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.ActivityPeakHoldTimeConfig, xddq.pb.ActivityPeakHoldTimeConfig.Builder, xddq.pb.ActivityPeakHoldTimeConfigOrBuilder> 
        internalGetPeakHoldTimeConfigsFieldBuilder() {
      if (peakHoldTimeConfigsBuilder_ == null) {
        peakHoldTimeConfigsBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.ActivityPeakHoldTimeConfig, xddq.pb.ActivityPeakHoldTimeConfig.Builder, xddq.pb.ActivityPeakHoldTimeConfigOrBuilder>(
                peakHoldTimeConfigs_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        peakHoldTimeConfigs_ = null;
      }
      return peakHoldTimeConfigsBuilder_;
    }

    private java.util.List<xddq.pb.KunlunWarGroupConfig> groupConfig_ =
      java.util.Collections.emptyList();
    private void ensureGroupConfigIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        groupConfig_ = new java.util.ArrayList<xddq.pb.KunlunWarGroupConfig>(groupConfig_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.KunlunWarGroupConfig, xddq.pb.KunlunWarGroupConfig.Builder, xddq.pb.KunlunWarGroupConfigOrBuilder> groupConfigBuilder_;

    /**
     * <code>repeated .xddq.pb.KunlunWarGroupConfig groupConfig = 2;</code>
     */
    public java.util.List<xddq.pb.KunlunWarGroupConfig> getGroupConfigList() {
      if (groupConfigBuilder_ == null) {
        return java.util.Collections.unmodifiableList(groupConfig_);
      } else {
        return groupConfigBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGroupConfig groupConfig = 2;</code>
     */
    public int getGroupConfigCount() {
      if (groupConfigBuilder_ == null) {
        return groupConfig_.size();
      } else {
        return groupConfigBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGroupConfig groupConfig = 2;</code>
     */
    public xddq.pb.KunlunWarGroupConfig getGroupConfig(int index) {
      if (groupConfigBuilder_ == null) {
        return groupConfig_.get(index);
      } else {
        return groupConfigBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGroupConfig groupConfig = 2;</code>
     */
    public Builder setGroupConfig(
        int index, xddq.pb.KunlunWarGroupConfig value) {
      if (groupConfigBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureGroupConfigIsMutable();
        groupConfig_.set(index, value);
        onChanged();
      } else {
        groupConfigBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGroupConfig groupConfig = 2;</code>
     */
    public Builder setGroupConfig(
        int index, xddq.pb.KunlunWarGroupConfig.Builder builderForValue) {
      if (groupConfigBuilder_ == null) {
        ensureGroupConfigIsMutable();
        groupConfig_.set(index, builderForValue.build());
        onChanged();
      } else {
        groupConfigBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGroupConfig groupConfig = 2;</code>
     */
    public Builder addGroupConfig(xddq.pb.KunlunWarGroupConfig value) {
      if (groupConfigBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureGroupConfigIsMutable();
        groupConfig_.add(value);
        onChanged();
      } else {
        groupConfigBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGroupConfig groupConfig = 2;</code>
     */
    public Builder addGroupConfig(
        int index, xddq.pb.KunlunWarGroupConfig value) {
      if (groupConfigBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureGroupConfigIsMutable();
        groupConfig_.add(index, value);
        onChanged();
      } else {
        groupConfigBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGroupConfig groupConfig = 2;</code>
     */
    public Builder addGroupConfig(
        xddq.pb.KunlunWarGroupConfig.Builder builderForValue) {
      if (groupConfigBuilder_ == null) {
        ensureGroupConfigIsMutable();
        groupConfig_.add(builderForValue.build());
        onChanged();
      } else {
        groupConfigBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGroupConfig groupConfig = 2;</code>
     */
    public Builder addGroupConfig(
        int index, xddq.pb.KunlunWarGroupConfig.Builder builderForValue) {
      if (groupConfigBuilder_ == null) {
        ensureGroupConfigIsMutable();
        groupConfig_.add(index, builderForValue.build());
        onChanged();
      } else {
        groupConfigBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGroupConfig groupConfig = 2;</code>
     */
    public Builder addAllGroupConfig(
        java.lang.Iterable<? extends xddq.pb.KunlunWarGroupConfig> values) {
      if (groupConfigBuilder_ == null) {
        ensureGroupConfigIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, groupConfig_);
        onChanged();
      } else {
        groupConfigBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGroupConfig groupConfig = 2;</code>
     */
    public Builder clearGroupConfig() {
      if (groupConfigBuilder_ == null) {
        groupConfig_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        groupConfigBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGroupConfig groupConfig = 2;</code>
     */
    public Builder removeGroupConfig(int index) {
      if (groupConfigBuilder_ == null) {
        ensureGroupConfigIsMutable();
        groupConfig_.remove(index);
        onChanged();
      } else {
        groupConfigBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGroupConfig groupConfig = 2;</code>
     */
    public xddq.pb.KunlunWarGroupConfig.Builder getGroupConfigBuilder(
        int index) {
      return internalGetGroupConfigFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGroupConfig groupConfig = 2;</code>
     */
    public xddq.pb.KunlunWarGroupConfigOrBuilder getGroupConfigOrBuilder(
        int index) {
      if (groupConfigBuilder_ == null) {
        return groupConfig_.get(index);  } else {
        return groupConfigBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGroupConfig groupConfig = 2;</code>
     */
    public java.util.List<? extends xddq.pb.KunlunWarGroupConfigOrBuilder> 
         getGroupConfigOrBuilderList() {
      if (groupConfigBuilder_ != null) {
        return groupConfigBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(groupConfig_);
      }
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGroupConfig groupConfig = 2;</code>
     */
    public xddq.pb.KunlunWarGroupConfig.Builder addGroupConfigBuilder() {
      return internalGetGroupConfigFieldBuilder().addBuilder(
          xddq.pb.KunlunWarGroupConfig.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGroupConfig groupConfig = 2;</code>
     */
    public xddq.pb.KunlunWarGroupConfig.Builder addGroupConfigBuilder(
        int index) {
      return internalGetGroupConfigFieldBuilder().addBuilder(
          index, xddq.pb.KunlunWarGroupConfig.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarGroupConfig groupConfig = 2;</code>
     */
    public java.util.List<xddq.pb.KunlunWarGroupConfig.Builder> 
         getGroupConfigBuilderList() {
      return internalGetGroupConfigFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.KunlunWarGroupConfig, xddq.pb.KunlunWarGroupConfig.Builder, xddq.pb.KunlunWarGroupConfigOrBuilder> 
        internalGetGroupConfigFieldBuilder() {
      if (groupConfigBuilder_ == null) {
        groupConfigBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.KunlunWarGroupConfig, xddq.pb.KunlunWarGroupConfig.Builder, xddq.pb.KunlunWarGroupConfigOrBuilder>(
                groupConfig_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        groupConfig_ = null;
      }
      return groupConfigBuilder_;
    }

    private java.util.List<xddq.pb.KunlunWarPlaceConfig> placeConfig_ =
      java.util.Collections.emptyList();
    private void ensurePlaceConfigIsMutable() {
      if (!((bitField0_ & 0x00000004) != 0)) {
        placeConfig_ = new java.util.ArrayList<xddq.pb.KunlunWarPlaceConfig>(placeConfig_);
        bitField0_ |= 0x00000004;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.KunlunWarPlaceConfig, xddq.pb.KunlunWarPlaceConfig.Builder, xddq.pb.KunlunWarPlaceConfigOrBuilder> placeConfigBuilder_;

    /**
     * <code>repeated .xddq.pb.KunlunWarPlaceConfig placeConfig = 3;</code>
     */
    public java.util.List<xddq.pb.KunlunWarPlaceConfig> getPlaceConfigList() {
      if (placeConfigBuilder_ == null) {
        return java.util.Collections.unmodifiableList(placeConfig_);
      } else {
        return placeConfigBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarPlaceConfig placeConfig = 3;</code>
     */
    public int getPlaceConfigCount() {
      if (placeConfigBuilder_ == null) {
        return placeConfig_.size();
      } else {
        return placeConfigBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarPlaceConfig placeConfig = 3;</code>
     */
    public xddq.pb.KunlunWarPlaceConfig getPlaceConfig(int index) {
      if (placeConfigBuilder_ == null) {
        return placeConfig_.get(index);
      } else {
        return placeConfigBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarPlaceConfig placeConfig = 3;</code>
     */
    public Builder setPlaceConfig(
        int index, xddq.pb.KunlunWarPlaceConfig value) {
      if (placeConfigBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePlaceConfigIsMutable();
        placeConfig_.set(index, value);
        onChanged();
      } else {
        placeConfigBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarPlaceConfig placeConfig = 3;</code>
     */
    public Builder setPlaceConfig(
        int index, xddq.pb.KunlunWarPlaceConfig.Builder builderForValue) {
      if (placeConfigBuilder_ == null) {
        ensurePlaceConfigIsMutable();
        placeConfig_.set(index, builderForValue.build());
        onChanged();
      } else {
        placeConfigBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarPlaceConfig placeConfig = 3;</code>
     */
    public Builder addPlaceConfig(xddq.pb.KunlunWarPlaceConfig value) {
      if (placeConfigBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePlaceConfigIsMutable();
        placeConfig_.add(value);
        onChanged();
      } else {
        placeConfigBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarPlaceConfig placeConfig = 3;</code>
     */
    public Builder addPlaceConfig(
        int index, xddq.pb.KunlunWarPlaceConfig value) {
      if (placeConfigBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePlaceConfigIsMutable();
        placeConfig_.add(index, value);
        onChanged();
      } else {
        placeConfigBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarPlaceConfig placeConfig = 3;</code>
     */
    public Builder addPlaceConfig(
        xddq.pb.KunlunWarPlaceConfig.Builder builderForValue) {
      if (placeConfigBuilder_ == null) {
        ensurePlaceConfigIsMutable();
        placeConfig_.add(builderForValue.build());
        onChanged();
      } else {
        placeConfigBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarPlaceConfig placeConfig = 3;</code>
     */
    public Builder addPlaceConfig(
        int index, xddq.pb.KunlunWarPlaceConfig.Builder builderForValue) {
      if (placeConfigBuilder_ == null) {
        ensurePlaceConfigIsMutable();
        placeConfig_.add(index, builderForValue.build());
        onChanged();
      } else {
        placeConfigBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarPlaceConfig placeConfig = 3;</code>
     */
    public Builder addAllPlaceConfig(
        java.lang.Iterable<? extends xddq.pb.KunlunWarPlaceConfig> values) {
      if (placeConfigBuilder_ == null) {
        ensurePlaceConfigIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, placeConfig_);
        onChanged();
      } else {
        placeConfigBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarPlaceConfig placeConfig = 3;</code>
     */
    public Builder clearPlaceConfig() {
      if (placeConfigBuilder_ == null) {
        placeConfig_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
      } else {
        placeConfigBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarPlaceConfig placeConfig = 3;</code>
     */
    public Builder removePlaceConfig(int index) {
      if (placeConfigBuilder_ == null) {
        ensurePlaceConfigIsMutable();
        placeConfig_.remove(index);
        onChanged();
      } else {
        placeConfigBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarPlaceConfig placeConfig = 3;</code>
     */
    public xddq.pb.KunlunWarPlaceConfig.Builder getPlaceConfigBuilder(
        int index) {
      return internalGetPlaceConfigFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarPlaceConfig placeConfig = 3;</code>
     */
    public xddq.pb.KunlunWarPlaceConfigOrBuilder getPlaceConfigOrBuilder(
        int index) {
      if (placeConfigBuilder_ == null) {
        return placeConfig_.get(index);  } else {
        return placeConfigBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarPlaceConfig placeConfig = 3;</code>
     */
    public java.util.List<? extends xddq.pb.KunlunWarPlaceConfigOrBuilder> 
         getPlaceConfigOrBuilderList() {
      if (placeConfigBuilder_ != null) {
        return placeConfigBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(placeConfig_);
      }
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarPlaceConfig placeConfig = 3;</code>
     */
    public xddq.pb.KunlunWarPlaceConfig.Builder addPlaceConfigBuilder() {
      return internalGetPlaceConfigFieldBuilder().addBuilder(
          xddq.pb.KunlunWarPlaceConfig.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarPlaceConfig placeConfig = 3;</code>
     */
    public xddq.pb.KunlunWarPlaceConfig.Builder addPlaceConfigBuilder(
        int index) {
      return internalGetPlaceConfigFieldBuilder().addBuilder(
          index, xddq.pb.KunlunWarPlaceConfig.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarPlaceConfig placeConfig = 3;</code>
     */
    public java.util.List<xddq.pb.KunlunWarPlaceConfig.Builder> 
         getPlaceConfigBuilderList() {
      return internalGetPlaceConfigFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.KunlunWarPlaceConfig, xddq.pb.KunlunWarPlaceConfig.Builder, xddq.pb.KunlunWarPlaceConfigOrBuilder> 
        internalGetPlaceConfigFieldBuilder() {
      if (placeConfigBuilder_ == null) {
        placeConfigBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.KunlunWarPlaceConfig, xddq.pb.KunlunWarPlaceConfig.Builder, xddq.pb.KunlunWarPlaceConfigOrBuilder>(
                placeConfig_,
                ((bitField0_ & 0x00000004) != 0),
                getParentForChildren(),
                isClean());
        placeConfig_ = null;
      }
      return placeConfigBuilder_;
    }

    private java.util.List<xddq.pb.KunlunWarEventConfig> eventConfig_ =
      java.util.Collections.emptyList();
    private void ensureEventConfigIsMutable() {
      if (!((bitField0_ & 0x00000008) != 0)) {
        eventConfig_ = new java.util.ArrayList<xddq.pb.KunlunWarEventConfig>(eventConfig_);
        bitField0_ |= 0x00000008;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.KunlunWarEventConfig, xddq.pb.KunlunWarEventConfig.Builder, xddq.pb.KunlunWarEventConfigOrBuilder> eventConfigBuilder_;

    /**
     * <code>repeated .xddq.pb.KunlunWarEventConfig eventConfig = 4;</code>
     */
    public java.util.List<xddq.pb.KunlunWarEventConfig> getEventConfigList() {
      if (eventConfigBuilder_ == null) {
        return java.util.Collections.unmodifiableList(eventConfig_);
      } else {
        return eventConfigBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarEventConfig eventConfig = 4;</code>
     */
    public int getEventConfigCount() {
      if (eventConfigBuilder_ == null) {
        return eventConfig_.size();
      } else {
        return eventConfigBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarEventConfig eventConfig = 4;</code>
     */
    public xddq.pb.KunlunWarEventConfig getEventConfig(int index) {
      if (eventConfigBuilder_ == null) {
        return eventConfig_.get(index);
      } else {
        return eventConfigBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarEventConfig eventConfig = 4;</code>
     */
    public Builder setEventConfig(
        int index, xddq.pb.KunlunWarEventConfig value) {
      if (eventConfigBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureEventConfigIsMutable();
        eventConfig_.set(index, value);
        onChanged();
      } else {
        eventConfigBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarEventConfig eventConfig = 4;</code>
     */
    public Builder setEventConfig(
        int index, xddq.pb.KunlunWarEventConfig.Builder builderForValue) {
      if (eventConfigBuilder_ == null) {
        ensureEventConfigIsMutable();
        eventConfig_.set(index, builderForValue.build());
        onChanged();
      } else {
        eventConfigBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarEventConfig eventConfig = 4;</code>
     */
    public Builder addEventConfig(xddq.pb.KunlunWarEventConfig value) {
      if (eventConfigBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureEventConfigIsMutable();
        eventConfig_.add(value);
        onChanged();
      } else {
        eventConfigBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarEventConfig eventConfig = 4;</code>
     */
    public Builder addEventConfig(
        int index, xddq.pb.KunlunWarEventConfig value) {
      if (eventConfigBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureEventConfigIsMutable();
        eventConfig_.add(index, value);
        onChanged();
      } else {
        eventConfigBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarEventConfig eventConfig = 4;</code>
     */
    public Builder addEventConfig(
        xddq.pb.KunlunWarEventConfig.Builder builderForValue) {
      if (eventConfigBuilder_ == null) {
        ensureEventConfigIsMutable();
        eventConfig_.add(builderForValue.build());
        onChanged();
      } else {
        eventConfigBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarEventConfig eventConfig = 4;</code>
     */
    public Builder addEventConfig(
        int index, xddq.pb.KunlunWarEventConfig.Builder builderForValue) {
      if (eventConfigBuilder_ == null) {
        ensureEventConfigIsMutable();
        eventConfig_.add(index, builderForValue.build());
        onChanged();
      } else {
        eventConfigBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarEventConfig eventConfig = 4;</code>
     */
    public Builder addAllEventConfig(
        java.lang.Iterable<? extends xddq.pb.KunlunWarEventConfig> values) {
      if (eventConfigBuilder_ == null) {
        ensureEventConfigIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, eventConfig_);
        onChanged();
      } else {
        eventConfigBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarEventConfig eventConfig = 4;</code>
     */
    public Builder clearEventConfig() {
      if (eventConfigBuilder_ == null) {
        eventConfig_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000008);
        onChanged();
      } else {
        eventConfigBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarEventConfig eventConfig = 4;</code>
     */
    public Builder removeEventConfig(int index) {
      if (eventConfigBuilder_ == null) {
        ensureEventConfigIsMutable();
        eventConfig_.remove(index);
        onChanged();
      } else {
        eventConfigBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarEventConfig eventConfig = 4;</code>
     */
    public xddq.pb.KunlunWarEventConfig.Builder getEventConfigBuilder(
        int index) {
      return internalGetEventConfigFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarEventConfig eventConfig = 4;</code>
     */
    public xddq.pb.KunlunWarEventConfigOrBuilder getEventConfigOrBuilder(
        int index) {
      if (eventConfigBuilder_ == null) {
        return eventConfig_.get(index);  } else {
        return eventConfigBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarEventConfig eventConfig = 4;</code>
     */
    public java.util.List<? extends xddq.pb.KunlunWarEventConfigOrBuilder> 
         getEventConfigOrBuilderList() {
      if (eventConfigBuilder_ != null) {
        return eventConfigBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(eventConfig_);
      }
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarEventConfig eventConfig = 4;</code>
     */
    public xddq.pb.KunlunWarEventConfig.Builder addEventConfigBuilder() {
      return internalGetEventConfigFieldBuilder().addBuilder(
          xddq.pb.KunlunWarEventConfig.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarEventConfig eventConfig = 4;</code>
     */
    public xddq.pb.KunlunWarEventConfig.Builder addEventConfigBuilder(
        int index) {
      return internalGetEventConfigFieldBuilder().addBuilder(
          index, xddq.pb.KunlunWarEventConfig.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarEventConfig eventConfig = 4;</code>
     */
    public java.util.List<xddq.pb.KunlunWarEventConfig.Builder> 
         getEventConfigBuilderList() {
      return internalGetEventConfigFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.KunlunWarEventConfig, xddq.pb.KunlunWarEventConfig.Builder, xddq.pb.KunlunWarEventConfigOrBuilder> 
        internalGetEventConfigFieldBuilder() {
      if (eventConfigBuilder_ == null) {
        eventConfigBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.KunlunWarEventConfig, xddq.pb.KunlunWarEventConfig.Builder, xddq.pb.KunlunWarEventConfigOrBuilder>(
                eventConfig_,
                ((bitField0_ & 0x00000008) != 0),
                getParentForChildren(),
                isClean());
        eventConfig_ = null;
      }
      return eventConfigBuilder_;
    }

    private java.util.List<xddq.pb.KunlunWarAttrConfig> attrConfig_ =
      java.util.Collections.emptyList();
    private void ensureAttrConfigIsMutable() {
      if (!((bitField0_ & 0x00000010) != 0)) {
        attrConfig_ = new java.util.ArrayList<xddq.pb.KunlunWarAttrConfig>(attrConfig_);
        bitField0_ |= 0x00000010;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.KunlunWarAttrConfig, xddq.pb.KunlunWarAttrConfig.Builder, xddq.pb.KunlunWarAttrConfigOrBuilder> attrConfigBuilder_;

    /**
     * <code>repeated .xddq.pb.KunlunWarAttrConfig attrConfig = 5;</code>
     */
    public java.util.List<xddq.pb.KunlunWarAttrConfig> getAttrConfigList() {
      if (attrConfigBuilder_ == null) {
        return java.util.Collections.unmodifiableList(attrConfig_);
      } else {
        return attrConfigBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarAttrConfig attrConfig = 5;</code>
     */
    public int getAttrConfigCount() {
      if (attrConfigBuilder_ == null) {
        return attrConfig_.size();
      } else {
        return attrConfigBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarAttrConfig attrConfig = 5;</code>
     */
    public xddq.pb.KunlunWarAttrConfig getAttrConfig(int index) {
      if (attrConfigBuilder_ == null) {
        return attrConfig_.get(index);
      } else {
        return attrConfigBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarAttrConfig attrConfig = 5;</code>
     */
    public Builder setAttrConfig(
        int index, xddq.pb.KunlunWarAttrConfig value) {
      if (attrConfigBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureAttrConfigIsMutable();
        attrConfig_.set(index, value);
        onChanged();
      } else {
        attrConfigBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarAttrConfig attrConfig = 5;</code>
     */
    public Builder setAttrConfig(
        int index, xddq.pb.KunlunWarAttrConfig.Builder builderForValue) {
      if (attrConfigBuilder_ == null) {
        ensureAttrConfigIsMutable();
        attrConfig_.set(index, builderForValue.build());
        onChanged();
      } else {
        attrConfigBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarAttrConfig attrConfig = 5;</code>
     */
    public Builder addAttrConfig(xddq.pb.KunlunWarAttrConfig value) {
      if (attrConfigBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureAttrConfigIsMutable();
        attrConfig_.add(value);
        onChanged();
      } else {
        attrConfigBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarAttrConfig attrConfig = 5;</code>
     */
    public Builder addAttrConfig(
        int index, xddq.pb.KunlunWarAttrConfig value) {
      if (attrConfigBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureAttrConfigIsMutable();
        attrConfig_.add(index, value);
        onChanged();
      } else {
        attrConfigBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarAttrConfig attrConfig = 5;</code>
     */
    public Builder addAttrConfig(
        xddq.pb.KunlunWarAttrConfig.Builder builderForValue) {
      if (attrConfigBuilder_ == null) {
        ensureAttrConfigIsMutable();
        attrConfig_.add(builderForValue.build());
        onChanged();
      } else {
        attrConfigBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarAttrConfig attrConfig = 5;</code>
     */
    public Builder addAttrConfig(
        int index, xddq.pb.KunlunWarAttrConfig.Builder builderForValue) {
      if (attrConfigBuilder_ == null) {
        ensureAttrConfigIsMutable();
        attrConfig_.add(index, builderForValue.build());
        onChanged();
      } else {
        attrConfigBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarAttrConfig attrConfig = 5;</code>
     */
    public Builder addAllAttrConfig(
        java.lang.Iterable<? extends xddq.pb.KunlunWarAttrConfig> values) {
      if (attrConfigBuilder_ == null) {
        ensureAttrConfigIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, attrConfig_);
        onChanged();
      } else {
        attrConfigBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarAttrConfig attrConfig = 5;</code>
     */
    public Builder clearAttrConfig() {
      if (attrConfigBuilder_ == null) {
        attrConfig_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000010);
        onChanged();
      } else {
        attrConfigBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarAttrConfig attrConfig = 5;</code>
     */
    public Builder removeAttrConfig(int index) {
      if (attrConfigBuilder_ == null) {
        ensureAttrConfigIsMutable();
        attrConfig_.remove(index);
        onChanged();
      } else {
        attrConfigBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarAttrConfig attrConfig = 5;</code>
     */
    public xddq.pb.KunlunWarAttrConfig.Builder getAttrConfigBuilder(
        int index) {
      return internalGetAttrConfigFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarAttrConfig attrConfig = 5;</code>
     */
    public xddq.pb.KunlunWarAttrConfigOrBuilder getAttrConfigOrBuilder(
        int index) {
      if (attrConfigBuilder_ == null) {
        return attrConfig_.get(index);  } else {
        return attrConfigBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarAttrConfig attrConfig = 5;</code>
     */
    public java.util.List<? extends xddq.pb.KunlunWarAttrConfigOrBuilder> 
         getAttrConfigOrBuilderList() {
      if (attrConfigBuilder_ != null) {
        return attrConfigBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(attrConfig_);
      }
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarAttrConfig attrConfig = 5;</code>
     */
    public xddq.pb.KunlunWarAttrConfig.Builder addAttrConfigBuilder() {
      return internalGetAttrConfigFieldBuilder().addBuilder(
          xddq.pb.KunlunWarAttrConfig.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarAttrConfig attrConfig = 5;</code>
     */
    public xddq.pb.KunlunWarAttrConfig.Builder addAttrConfigBuilder(
        int index) {
      return internalGetAttrConfigFieldBuilder().addBuilder(
          index, xddq.pb.KunlunWarAttrConfig.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarAttrConfig attrConfig = 5;</code>
     */
    public java.util.List<xddq.pb.KunlunWarAttrConfig.Builder> 
         getAttrConfigBuilderList() {
      return internalGetAttrConfigFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.KunlunWarAttrConfig, xddq.pb.KunlunWarAttrConfig.Builder, xddq.pb.KunlunWarAttrConfigOrBuilder> 
        internalGetAttrConfigFieldBuilder() {
      if (attrConfigBuilder_ == null) {
        attrConfigBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.KunlunWarAttrConfig, xddq.pb.KunlunWarAttrConfig.Builder, xddq.pb.KunlunWarAttrConfigOrBuilder>(
                attrConfig_,
                ((bitField0_ & 0x00000010) != 0),
                getParentForChildren(),
                isClean());
        attrConfig_ = null;
      }
      return attrConfigBuilder_;
    }

    private java.util.List<xddq.pb.KunlunWarTreasureConfig> treasureConfig_ =
      java.util.Collections.emptyList();
    private void ensureTreasureConfigIsMutable() {
      if (!((bitField0_ & 0x00000020) != 0)) {
        treasureConfig_ = new java.util.ArrayList<xddq.pb.KunlunWarTreasureConfig>(treasureConfig_);
        bitField0_ |= 0x00000020;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.KunlunWarTreasureConfig, xddq.pb.KunlunWarTreasureConfig.Builder, xddq.pb.KunlunWarTreasureConfigOrBuilder> treasureConfigBuilder_;

    /**
     * <code>repeated .xddq.pb.KunlunWarTreasureConfig treasureConfig = 6;</code>
     */
    public java.util.List<xddq.pb.KunlunWarTreasureConfig> getTreasureConfigList() {
      if (treasureConfigBuilder_ == null) {
        return java.util.Collections.unmodifiableList(treasureConfig_);
      } else {
        return treasureConfigBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarTreasureConfig treasureConfig = 6;</code>
     */
    public int getTreasureConfigCount() {
      if (treasureConfigBuilder_ == null) {
        return treasureConfig_.size();
      } else {
        return treasureConfigBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarTreasureConfig treasureConfig = 6;</code>
     */
    public xddq.pb.KunlunWarTreasureConfig getTreasureConfig(int index) {
      if (treasureConfigBuilder_ == null) {
        return treasureConfig_.get(index);
      } else {
        return treasureConfigBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarTreasureConfig treasureConfig = 6;</code>
     */
    public Builder setTreasureConfig(
        int index, xddq.pb.KunlunWarTreasureConfig value) {
      if (treasureConfigBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureTreasureConfigIsMutable();
        treasureConfig_.set(index, value);
        onChanged();
      } else {
        treasureConfigBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarTreasureConfig treasureConfig = 6;</code>
     */
    public Builder setTreasureConfig(
        int index, xddq.pb.KunlunWarTreasureConfig.Builder builderForValue) {
      if (treasureConfigBuilder_ == null) {
        ensureTreasureConfigIsMutable();
        treasureConfig_.set(index, builderForValue.build());
        onChanged();
      } else {
        treasureConfigBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarTreasureConfig treasureConfig = 6;</code>
     */
    public Builder addTreasureConfig(xddq.pb.KunlunWarTreasureConfig value) {
      if (treasureConfigBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureTreasureConfigIsMutable();
        treasureConfig_.add(value);
        onChanged();
      } else {
        treasureConfigBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarTreasureConfig treasureConfig = 6;</code>
     */
    public Builder addTreasureConfig(
        int index, xddq.pb.KunlunWarTreasureConfig value) {
      if (treasureConfigBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureTreasureConfigIsMutable();
        treasureConfig_.add(index, value);
        onChanged();
      } else {
        treasureConfigBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarTreasureConfig treasureConfig = 6;</code>
     */
    public Builder addTreasureConfig(
        xddq.pb.KunlunWarTreasureConfig.Builder builderForValue) {
      if (treasureConfigBuilder_ == null) {
        ensureTreasureConfigIsMutable();
        treasureConfig_.add(builderForValue.build());
        onChanged();
      } else {
        treasureConfigBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarTreasureConfig treasureConfig = 6;</code>
     */
    public Builder addTreasureConfig(
        int index, xddq.pb.KunlunWarTreasureConfig.Builder builderForValue) {
      if (treasureConfigBuilder_ == null) {
        ensureTreasureConfigIsMutable();
        treasureConfig_.add(index, builderForValue.build());
        onChanged();
      } else {
        treasureConfigBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarTreasureConfig treasureConfig = 6;</code>
     */
    public Builder addAllTreasureConfig(
        java.lang.Iterable<? extends xddq.pb.KunlunWarTreasureConfig> values) {
      if (treasureConfigBuilder_ == null) {
        ensureTreasureConfigIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, treasureConfig_);
        onChanged();
      } else {
        treasureConfigBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarTreasureConfig treasureConfig = 6;</code>
     */
    public Builder clearTreasureConfig() {
      if (treasureConfigBuilder_ == null) {
        treasureConfig_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000020);
        onChanged();
      } else {
        treasureConfigBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarTreasureConfig treasureConfig = 6;</code>
     */
    public Builder removeTreasureConfig(int index) {
      if (treasureConfigBuilder_ == null) {
        ensureTreasureConfigIsMutable();
        treasureConfig_.remove(index);
        onChanged();
      } else {
        treasureConfigBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarTreasureConfig treasureConfig = 6;</code>
     */
    public xddq.pb.KunlunWarTreasureConfig.Builder getTreasureConfigBuilder(
        int index) {
      return internalGetTreasureConfigFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarTreasureConfig treasureConfig = 6;</code>
     */
    public xddq.pb.KunlunWarTreasureConfigOrBuilder getTreasureConfigOrBuilder(
        int index) {
      if (treasureConfigBuilder_ == null) {
        return treasureConfig_.get(index);  } else {
        return treasureConfigBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarTreasureConfig treasureConfig = 6;</code>
     */
    public java.util.List<? extends xddq.pb.KunlunWarTreasureConfigOrBuilder> 
         getTreasureConfigOrBuilderList() {
      if (treasureConfigBuilder_ != null) {
        return treasureConfigBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(treasureConfig_);
      }
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarTreasureConfig treasureConfig = 6;</code>
     */
    public xddq.pb.KunlunWarTreasureConfig.Builder addTreasureConfigBuilder() {
      return internalGetTreasureConfigFieldBuilder().addBuilder(
          xddq.pb.KunlunWarTreasureConfig.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarTreasureConfig treasureConfig = 6;</code>
     */
    public xddq.pb.KunlunWarTreasureConfig.Builder addTreasureConfigBuilder(
        int index) {
      return internalGetTreasureConfigFieldBuilder().addBuilder(
          index, xddq.pb.KunlunWarTreasureConfig.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarTreasureConfig treasureConfig = 6;</code>
     */
    public java.util.List<xddq.pb.KunlunWarTreasureConfig.Builder> 
         getTreasureConfigBuilderList() {
      return internalGetTreasureConfigFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.KunlunWarTreasureConfig, xddq.pb.KunlunWarTreasureConfig.Builder, xddq.pb.KunlunWarTreasureConfigOrBuilder> 
        internalGetTreasureConfigFieldBuilder() {
      if (treasureConfigBuilder_ == null) {
        treasureConfigBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.KunlunWarTreasureConfig, xddq.pb.KunlunWarTreasureConfig.Builder, xddq.pb.KunlunWarTreasureConfigOrBuilder>(
                treasureConfig_,
                ((bitField0_ & 0x00000020) != 0),
                getParentForChildren(),
                isClean());
        treasureConfig_ = null;
      }
      return treasureConfigBuilder_;
    }

    private java.util.List<xddq.pb.KunlunWarCityConfig> cityConfig_ =
      java.util.Collections.emptyList();
    private void ensureCityConfigIsMutable() {
      if (!((bitField0_ & 0x00000040) != 0)) {
        cityConfig_ = new java.util.ArrayList<xddq.pb.KunlunWarCityConfig>(cityConfig_);
        bitField0_ |= 0x00000040;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.KunlunWarCityConfig, xddq.pb.KunlunWarCityConfig.Builder, xddq.pb.KunlunWarCityConfigOrBuilder> cityConfigBuilder_;

    /**
     * <code>repeated .xddq.pb.KunlunWarCityConfig cityConfig = 7;</code>
     */
    public java.util.List<xddq.pb.KunlunWarCityConfig> getCityConfigList() {
      if (cityConfigBuilder_ == null) {
        return java.util.Collections.unmodifiableList(cityConfig_);
      } else {
        return cityConfigBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarCityConfig cityConfig = 7;</code>
     */
    public int getCityConfigCount() {
      if (cityConfigBuilder_ == null) {
        return cityConfig_.size();
      } else {
        return cityConfigBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarCityConfig cityConfig = 7;</code>
     */
    public xddq.pb.KunlunWarCityConfig getCityConfig(int index) {
      if (cityConfigBuilder_ == null) {
        return cityConfig_.get(index);
      } else {
        return cityConfigBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarCityConfig cityConfig = 7;</code>
     */
    public Builder setCityConfig(
        int index, xddq.pb.KunlunWarCityConfig value) {
      if (cityConfigBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureCityConfigIsMutable();
        cityConfig_.set(index, value);
        onChanged();
      } else {
        cityConfigBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarCityConfig cityConfig = 7;</code>
     */
    public Builder setCityConfig(
        int index, xddq.pb.KunlunWarCityConfig.Builder builderForValue) {
      if (cityConfigBuilder_ == null) {
        ensureCityConfigIsMutable();
        cityConfig_.set(index, builderForValue.build());
        onChanged();
      } else {
        cityConfigBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarCityConfig cityConfig = 7;</code>
     */
    public Builder addCityConfig(xddq.pb.KunlunWarCityConfig value) {
      if (cityConfigBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureCityConfigIsMutable();
        cityConfig_.add(value);
        onChanged();
      } else {
        cityConfigBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarCityConfig cityConfig = 7;</code>
     */
    public Builder addCityConfig(
        int index, xddq.pb.KunlunWarCityConfig value) {
      if (cityConfigBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureCityConfigIsMutable();
        cityConfig_.add(index, value);
        onChanged();
      } else {
        cityConfigBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarCityConfig cityConfig = 7;</code>
     */
    public Builder addCityConfig(
        xddq.pb.KunlunWarCityConfig.Builder builderForValue) {
      if (cityConfigBuilder_ == null) {
        ensureCityConfigIsMutable();
        cityConfig_.add(builderForValue.build());
        onChanged();
      } else {
        cityConfigBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarCityConfig cityConfig = 7;</code>
     */
    public Builder addCityConfig(
        int index, xddq.pb.KunlunWarCityConfig.Builder builderForValue) {
      if (cityConfigBuilder_ == null) {
        ensureCityConfigIsMutable();
        cityConfig_.add(index, builderForValue.build());
        onChanged();
      } else {
        cityConfigBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarCityConfig cityConfig = 7;</code>
     */
    public Builder addAllCityConfig(
        java.lang.Iterable<? extends xddq.pb.KunlunWarCityConfig> values) {
      if (cityConfigBuilder_ == null) {
        ensureCityConfigIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, cityConfig_);
        onChanged();
      } else {
        cityConfigBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarCityConfig cityConfig = 7;</code>
     */
    public Builder clearCityConfig() {
      if (cityConfigBuilder_ == null) {
        cityConfig_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000040);
        onChanged();
      } else {
        cityConfigBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarCityConfig cityConfig = 7;</code>
     */
    public Builder removeCityConfig(int index) {
      if (cityConfigBuilder_ == null) {
        ensureCityConfigIsMutable();
        cityConfig_.remove(index);
        onChanged();
      } else {
        cityConfigBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarCityConfig cityConfig = 7;</code>
     */
    public xddq.pb.KunlunWarCityConfig.Builder getCityConfigBuilder(
        int index) {
      return internalGetCityConfigFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarCityConfig cityConfig = 7;</code>
     */
    public xddq.pb.KunlunWarCityConfigOrBuilder getCityConfigOrBuilder(
        int index) {
      if (cityConfigBuilder_ == null) {
        return cityConfig_.get(index);  } else {
        return cityConfigBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarCityConfig cityConfig = 7;</code>
     */
    public java.util.List<? extends xddq.pb.KunlunWarCityConfigOrBuilder> 
         getCityConfigOrBuilderList() {
      if (cityConfigBuilder_ != null) {
        return cityConfigBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(cityConfig_);
      }
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarCityConfig cityConfig = 7;</code>
     */
    public xddq.pb.KunlunWarCityConfig.Builder addCityConfigBuilder() {
      return internalGetCityConfigFieldBuilder().addBuilder(
          xddq.pb.KunlunWarCityConfig.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarCityConfig cityConfig = 7;</code>
     */
    public xddq.pb.KunlunWarCityConfig.Builder addCityConfigBuilder(
        int index) {
      return internalGetCityConfigFieldBuilder().addBuilder(
          index, xddq.pb.KunlunWarCityConfig.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarCityConfig cityConfig = 7;</code>
     */
    public java.util.List<xddq.pb.KunlunWarCityConfig.Builder> 
         getCityConfigBuilderList() {
      return internalGetCityConfigFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.KunlunWarCityConfig, xddq.pb.KunlunWarCityConfig.Builder, xddq.pb.KunlunWarCityConfigOrBuilder> 
        internalGetCityConfigFieldBuilder() {
      if (cityConfigBuilder_ == null) {
        cityConfigBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.KunlunWarCityConfig, xddq.pb.KunlunWarCityConfig.Builder, xddq.pb.KunlunWarCityConfigOrBuilder>(
                cityConfig_,
                ((bitField0_ & 0x00000040) != 0),
                getParentForChildren(),
                isClean());
        cityConfig_ = null;
      }
      return cityConfigBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.KunlunWarConfigSync)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.KunlunWarConfigSync)
  private static final xddq.pb.KunlunWarConfigSync DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.KunlunWarConfigSync();
  }

  public static xddq.pb.KunlunWarConfigSync getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<KunlunWarConfigSync>
      PARSER = new com.google.protobuf.AbstractParser<KunlunWarConfigSync>() {
    @java.lang.Override
    public KunlunWarConfigSync parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<KunlunWarConfigSync> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<KunlunWarConfigSync> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.KunlunWarConfigSync getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

