// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.WarSeasonCommonPlayerShowMsg}
 */
public final class WarSeasonCommonPlayerShowMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.WarSeasonCommonPlayerShowMsg)
    WarSeasonCommonPlayerShowMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      WarSeasonCommonPlayerShowMsg.class.getName());
  }
  // Use WarSeasonCommonPlayerShowMsg.newBuilder() to construct.
  private WarSeasonCommonPlayerShowMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private WarSeasonCommonPlayerShowMsg() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_WarSeasonCommonPlayerShowMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_WarSeasonCommonPlayerShowMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.WarSeasonCommonPlayerShowMsg.class, xddq.pb.WarSeasonCommonPlayerShowMsg.Builder.class);
  }

  private int bitField0_;
  public static final int HEADDATA_FIELD_NUMBER = 1;
  private xddq.pb.PlayerHeadAndNameMsg headData_;
  /**
   * <code>optional .xddq.pb.PlayerHeadAndNameMsg headData = 1;</code>
   * @return Whether the headData field is set.
   */
  @java.lang.Override
  public boolean hasHeadData() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional .xddq.pb.PlayerHeadAndNameMsg headData = 1;</code>
   * @return The headData.
   */
  @java.lang.Override
  public xddq.pb.PlayerHeadAndNameMsg getHeadData() {
    return headData_ == null ? xddq.pb.PlayerHeadAndNameMsg.getDefaultInstance() : headData_;
  }
  /**
   * <code>optional .xddq.pb.PlayerHeadAndNameMsg headData = 1;</code>
   */
  @java.lang.Override
  public xddq.pb.PlayerHeadAndNameMsgOrBuilder getHeadDataOrBuilder() {
    return headData_ == null ? xddq.pb.PlayerHeadAndNameMsg.getDefaultInstance() : headData_;
  }

  public static final int GODBODYDATA_FIELD_NUMBER = 2;
  private xddq.pb.WarSeasonCommonGodBodyShowMsg godBodyData_;
  /**
   * <code>optional .xddq.pb.WarSeasonCommonGodBodyShowMsg godBodyData = 2;</code>
   * @return Whether the godBodyData field is set.
   */
  @java.lang.Override
  public boolean hasGodBodyData() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional .xddq.pb.WarSeasonCommonGodBodyShowMsg godBodyData = 2;</code>
   * @return The godBodyData.
   */
  @java.lang.Override
  public xddq.pb.WarSeasonCommonGodBodyShowMsg getGodBodyData() {
    return godBodyData_ == null ? xddq.pb.WarSeasonCommonGodBodyShowMsg.getDefaultInstance() : godBodyData_;
  }
  /**
   * <code>optional .xddq.pb.WarSeasonCommonGodBodyShowMsg godBodyData = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.WarSeasonCommonGodBodyShowMsgOrBuilder getGodBodyDataOrBuilder() {
    return godBodyData_ == null ? xddq.pb.WarSeasonCommonGodBodyShowMsg.getDefaultInstance() : godBodyData_;
  }

  public static final int SEASONEXP_FIELD_NUMBER = 3;
  private long seasonExp_ = 0L;
  /**
   * <code>optional int64 seasonExp = 3;</code>
   * @return Whether the seasonExp field is set.
   */
  @java.lang.Override
  public boolean hasSeasonExp() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional int64 seasonExp = 3;</code>
   * @return The seasonExp.
   */
  @java.lang.Override
  public long getSeasonExp() {
    return seasonExp_;
  }

  public static final int GROUNDNUM_FIELD_NUMBER = 4;
  private int groundNum_ = 0;
  /**
   * <code>optional int32 groundNum = 4;</code>
   * @return Whether the groundNum field is set.
   */
  @java.lang.Override
  public boolean hasGroundNum() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional int32 groundNum = 4;</code>
   * @return The groundNum.
   */
  @java.lang.Override
  public int getGroundNum() {
    return groundNum_;
  }

  public static final int GROUNDOUTPUT_FIELD_NUMBER = 5;
  private long groundOutput_ = 0L;
  /**
   * <code>optional int64 groundOutput = 5;</code>
   * @return Whether the groundOutput field is set.
   */
  @java.lang.Override
  public boolean hasGroundOutput() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>optional int64 groundOutput = 5;</code>
   * @return The groundOutput.
   */
  @java.lang.Override
  public long getGroundOutput() {
    return groundOutput_;
  }

  public static final int UNIONID_FIELD_NUMBER = 6;
  private long unionId_ = 0L;
  /**
   * <code>optional int64 unionId = 6;</code>
   * @return Whether the unionId field is set.
   */
  @java.lang.Override
  public boolean hasUnionId() {
    return ((bitField0_ & 0x00000020) != 0);
  }
  /**
   * <code>optional int64 unionId = 6;</code>
   * @return The unionId.
   */
  @java.lang.Override
  public long getUnionId() {
    return unionId_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (hasGodBodyData()) {
      if (!getGodBodyData().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeMessage(1, getHeadData());
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeMessage(2, getGodBodyData());
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt64(3, seasonExp_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt32(4, groundNum_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      output.writeInt64(5, groundOutput_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      output.writeInt64(6, unionId_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, getHeadData());
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getGodBodyData());
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(3, seasonExp_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(4, groundNum_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(5, groundOutput_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(6, unionId_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.WarSeasonCommonPlayerShowMsg)) {
      return super.equals(obj);
    }
    xddq.pb.WarSeasonCommonPlayerShowMsg other = (xddq.pb.WarSeasonCommonPlayerShowMsg) obj;

    if (hasHeadData() != other.hasHeadData()) return false;
    if (hasHeadData()) {
      if (!getHeadData()
          .equals(other.getHeadData())) return false;
    }
    if (hasGodBodyData() != other.hasGodBodyData()) return false;
    if (hasGodBodyData()) {
      if (!getGodBodyData()
          .equals(other.getGodBodyData())) return false;
    }
    if (hasSeasonExp() != other.hasSeasonExp()) return false;
    if (hasSeasonExp()) {
      if (getSeasonExp()
          != other.getSeasonExp()) return false;
    }
    if (hasGroundNum() != other.hasGroundNum()) return false;
    if (hasGroundNum()) {
      if (getGroundNum()
          != other.getGroundNum()) return false;
    }
    if (hasGroundOutput() != other.hasGroundOutput()) return false;
    if (hasGroundOutput()) {
      if (getGroundOutput()
          != other.getGroundOutput()) return false;
    }
    if (hasUnionId() != other.hasUnionId()) return false;
    if (hasUnionId()) {
      if (getUnionId()
          != other.getUnionId()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasHeadData()) {
      hash = (37 * hash) + HEADDATA_FIELD_NUMBER;
      hash = (53 * hash) + getHeadData().hashCode();
    }
    if (hasGodBodyData()) {
      hash = (37 * hash) + GODBODYDATA_FIELD_NUMBER;
      hash = (53 * hash) + getGodBodyData().hashCode();
    }
    if (hasSeasonExp()) {
      hash = (37 * hash) + SEASONEXP_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getSeasonExp());
    }
    if (hasGroundNum()) {
      hash = (37 * hash) + GROUNDNUM_FIELD_NUMBER;
      hash = (53 * hash) + getGroundNum();
    }
    if (hasGroundOutput()) {
      hash = (37 * hash) + GROUNDOUTPUT_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getGroundOutput());
    }
    if (hasUnionId()) {
      hash = (37 * hash) + UNIONID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getUnionId());
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.WarSeasonCommonPlayerShowMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.WarSeasonCommonPlayerShowMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.WarSeasonCommonPlayerShowMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.WarSeasonCommonPlayerShowMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.WarSeasonCommonPlayerShowMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.WarSeasonCommonPlayerShowMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.WarSeasonCommonPlayerShowMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.WarSeasonCommonPlayerShowMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.WarSeasonCommonPlayerShowMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.WarSeasonCommonPlayerShowMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.WarSeasonCommonPlayerShowMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.WarSeasonCommonPlayerShowMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.WarSeasonCommonPlayerShowMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.WarSeasonCommonPlayerShowMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.WarSeasonCommonPlayerShowMsg)
      xddq.pb.WarSeasonCommonPlayerShowMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_WarSeasonCommonPlayerShowMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_WarSeasonCommonPlayerShowMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.WarSeasonCommonPlayerShowMsg.class, xddq.pb.WarSeasonCommonPlayerShowMsg.Builder.class);
    }

    // Construct using xddq.pb.WarSeasonCommonPlayerShowMsg.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetHeadDataFieldBuilder();
        internalGetGodBodyDataFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      headData_ = null;
      if (headDataBuilder_ != null) {
        headDataBuilder_.dispose();
        headDataBuilder_ = null;
      }
      godBodyData_ = null;
      if (godBodyDataBuilder_ != null) {
        godBodyDataBuilder_.dispose();
        godBodyDataBuilder_ = null;
      }
      seasonExp_ = 0L;
      groundNum_ = 0;
      groundOutput_ = 0L;
      unionId_ = 0L;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_WarSeasonCommonPlayerShowMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.WarSeasonCommonPlayerShowMsg getDefaultInstanceForType() {
      return xddq.pb.WarSeasonCommonPlayerShowMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.WarSeasonCommonPlayerShowMsg build() {
      xddq.pb.WarSeasonCommonPlayerShowMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.WarSeasonCommonPlayerShowMsg buildPartial() {
      xddq.pb.WarSeasonCommonPlayerShowMsg result = new xddq.pb.WarSeasonCommonPlayerShowMsg(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.WarSeasonCommonPlayerShowMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.headData_ = headDataBuilder_ == null
            ? headData_
            : headDataBuilder_.build();
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.godBodyData_ = godBodyDataBuilder_ == null
            ? godBodyData_
            : godBodyDataBuilder_.build();
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.seasonExp_ = seasonExp_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.groundNum_ = groundNum_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.groundOutput_ = groundOutput_;
        to_bitField0_ |= 0x00000010;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.unionId_ = unionId_;
        to_bitField0_ |= 0x00000020;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.WarSeasonCommonPlayerShowMsg) {
        return mergeFrom((xddq.pb.WarSeasonCommonPlayerShowMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.WarSeasonCommonPlayerShowMsg other) {
      if (other == xddq.pb.WarSeasonCommonPlayerShowMsg.getDefaultInstance()) return this;
      if (other.hasHeadData()) {
        mergeHeadData(other.getHeadData());
      }
      if (other.hasGodBodyData()) {
        mergeGodBodyData(other.getGodBodyData());
      }
      if (other.hasSeasonExp()) {
        setSeasonExp(other.getSeasonExp());
      }
      if (other.hasGroundNum()) {
        setGroundNum(other.getGroundNum());
      }
      if (other.hasGroundOutput()) {
        setGroundOutput(other.getGroundOutput());
      }
      if (other.hasUnionId()) {
        setUnionId(other.getUnionId());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (hasGodBodyData()) {
        if (!getGodBodyData().isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              input.readMessage(
                  internalGetHeadDataFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 18: {
              input.readMessage(
                  internalGetGodBodyDataFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 24: {
              seasonExp_ = input.readInt64();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 32: {
              groundNum_ = input.readInt32();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            case 40: {
              groundOutput_ = input.readInt64();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            case 48: {
              unionId_ = input.readInt64();
              bitField0_ |= 0x00000020;
              break;
            } // case 48
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private xddq.pb.PlayerHeadAndNameMsg headData_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PlayerHeadAndNameMsg, xddq.pb.PlayerHeadAndNameMsg.Builder, xddq.pb.PlayerHeadAndNameMsgOrBuilder> headDataBuilder_;
    /**
     * <code>optional .xddq.pb.PlayerHeadAndNameMsg headData = 1;</code>
     * @return Whether the headData field is set.
     */
    public boolean hasHeadData() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadAndNameMsg headData = 1;</code>
     * @return The headData.
     */
    public xddq.pb.PlayerHeadAndNameMsg getHeadData() {
      if (headDataBuilder_ == null) {
        return headData_ == null ? xddq.pb.PlayerHeadAndNameMsg.getDefaultInstance() : headData_;
      } else {
        return headDataBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadAndNameMsg headData = 1;</code>
     */
    public Builder setHeadData(xddq.pb.PlayerHeadAndNameMsg value) {
      if (headDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        headData_ = value;
      } else {
        headDataBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadAndNameMsg headData = 1;</code>
     */
    public Builder setHeadData(
        xddq.pb.PlayerHeadAndNameMsg.Builder builderForValue) {
      if (headDataBuilder_ == null) {
        headData_ = builderForValue.build();
      } else {
        headDataBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadAndNameMsg headData = 1;</code>
     */
    public Builder mergeHeadData(xddq.pb.PlayerHeadAndNameMsg value) {
      if (headDataBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0) &&
          headData_ != null &&
          headData_ != xddq.pb.PlayerHeadAndNameMsg.getDefaultInstance()) {
          getHeadDataBuilder().mergeFrom(value);
        } else {
          headData_ = value;
        }
      } else {
        headDataBuilder_.mergeFrom(value);
      }
      if (headData_ != null) {
        bitField0_ |= 0x00000001;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadAndNameMsg headData = 1;</code>
     */
    public Builder clearHeadData() {
      bitField0_ = (bitField0_ & ~0x00000001);
      headData_ = null;
      if (headDataBuilder_ != null) {
        headDataBuilder_.dispose();
        headDataBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadAndNameMsg headData = 1;</code>
     */
    public xddq.pb.PlayerHeadAndNameMsg.Builder getHeadDataBuilder() {
      bitField0_ |= 0x00000001;
      onChanged();
      return internalGetHeadDataFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadAndNameMsg headData = 1;</code>
     */
    public xddq.pb.PlayerHeadAndNameMsgOrBuilder getHeadDataOrBuilder() {
      if (headDataBuilder_ != null) {
        return headDataBuilder_.getMessageOrBuilder();
      } else {
        return headData_ == null ?
            xddq.pb.PlayerHeadAndNameMsg.getDefaultInstance() : headData_;
      }
    }
    /**
     * <code>optional .xddq.pb.PlayerHeadAndNameMsg headData = 1;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.PlayerHeadAndNameMsg, xddq.pb.PlayerHeadAndNameMsg.Builder, xddq.pb.PlayerHeadAndNameMsgOrBuilder> 
        internalGetHeadDataFieldBuilder() {
      if (headDataBuilder_ == null) {
        headDataBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.PlayerHeadAndNameMsg, xddq.pb.PlayerHeadAndNameMsg.Builder, xddq.pb.PlayerHeadAndNameMsgOrBuilder>(
                getHeadData(),
                getParentForChildren(),
                isClean());
        headData_ = null;
      }
      return headDataBuilder_;
    }

    private xddq.pb.WarSeasonCommonGodBodyShowMsg godBodyData_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.WarSeasonCommonGodBodyShowMsg, xddq.pb.WarSeasonCommonGodBodyShowMsg.Builder, xddq.pb.WarSeasonCommonGodBodyShowMsgOrBuilder> godBodyDataBuilder_;
    /**
     * <code>optional .xddq.pb.WarSeasonCommonGodBodyShowMsg godBodyData = 2;</code>
     * @return Whether the godBodyData field is set.
     */
    public boolean hasGodBodyData() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .xddq.pb.WarSeasonCommonGodBodyShowMsg godBodyData = 2;</code>
     * @return The godBodyData.
     */
    public xddq.pb.WarSeasonCommonGodBodyShowMsg getGodBodyData() {
      if (godBodyDataBuilder_ == null) {
        return godBodyData_ == null ? xddq.pb.WarSeasonCommonGodBodyShowMsg.getDefaultInstance() : godBodyData_;
      } else {
        return godBodyDataBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.WarSeasonCommonGodBodyShowMsg godBodyData = 2;</code>
     */
    public Builder setGodBodyData(xddq.pb.WarSeasonCommonGodBodyShowMsg value) {
      if (godBodyDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        godBodyData_ = value;
      } else {
        godBodyDataBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.WarSeasonCommonGodBodyShowMsg godBodyData = 2;</code>
     */
    public Builder setGodBodyData(
        xddq.pb.WarSeasonCommonGodBodyShowMsg.Builder builderForValue) {
      if (godBodyDataBuilder_ == null) {
        godBodyData_ = builderForValue.build();
      } else {
        godBodyDataBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.WarSeasonCommonGodBodyShowMsg godBodyData = 2;</code>
     */
    public Builder mergeGodBodyData(xddq.pb.WarSeasonCommonGodBodyShowMsg value) {
      if (godBodyDataBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0) &&
          godBodyData_ != null &&
          godBodyData_ != xddq.pb.WarSeasonCommonGodBodyShowMsg.getDefaultInstance()) {
          getGodBodyDataBuilder().mergeFrom(value);
        } else {
          godBodyData_ = value;
        }
      } else {
        godBodyDataBuilder_.mergeFrom(value);
      }
      if (godBodyData_ != null) {
        bitField0_ |= 0x00000002;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.WarSeasonCommonGodBodyShowMsg godBodyData = 2;</code>
     */
    public Builder clearGodBodyData() {
      bitField0_ = (bitField0_ & ~0x00000002);
      godBodyData_ = null;
      if (godBodyDataBuilder_ != null) {
        godBodyDataBuilder_.dispose();
        godBodyDataBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.WarSeasonCommonGodBodyShowMsg godBodyData = 2;</code>
     */
    public xddq.pb.WarSeasonCommonGodBodyShowMsg.Builder getGodBodyDataBuilder() {
      bitField0_ |= 0x00000002;
      onChanged();
      return internalGetGodBodyDataFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.WarSeasonCommonGodBodyShowMsg godBodyData = 2;</code>
     */
    public xddq.pb.WarSeasonCommonGodBodyShowMsgOrBuilder getGodBodyDataOrBuilder() {
      if (godBodyDataBuilder_ != null) {
        return godBodyDataBuilder_.getMessageOrBuilder();
      } else {
        return godBodyData_ == null ?
            xddq.pb.WarSeasonCommonGodBodyShowMsg.getDefaultInstance() : godBodyData_;
      }
    }
    /**
     * <code>optional .xddq.pb.WarSeasonCommonGodBodyShowMsg godBodyData = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.WarSeasonCommonGodBodyShowMsg, xddq.pb.WarSeasonCommonGodBodyShowMsg.Builder, xddq.pb.WarSeasonCommonGodBodyShowMsgOrBuilder> 
        internalGetGodBodyDataFieldBuilder() {
      if (godBodyDataBuilder_ == null) {
        godBodyDataBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.WarSeasonCommonGodBodyShowMsg, xddq.pb.WarSeasonCommonGodBodyShowMsg.Builder, xddq.pb.WarSeasonCommonGodBodyShowMsgOrBuilder>(
                getGodBodyData(),
                getParentForChildren(),
                isClean());
        godBodyData_ = null;
      }
      return godBodyDataBuilder_;
    }

    private long seasonExp_ ;
    /**
     * <code>optional int64 seasonExp = 3;</code>
     * @return Whether the seasonExp field is set.
     */
    @java.lang.Override
    public boolean hasSeasonExp() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int64 seasonExp = 3;</code>
     * @return The seasonExp.
     */
    @java.lang.Override
    public long getSeasonExp() {
      return seasonExp_;
    }
    /**
     * <code>optional int64 seasonExp = 3;</code>
     * @param value The seasonExp to set.
     * @return This builder for chaining.
     */
    public Builder setSeasonExp(long value) {

      seasonExp_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 seasonExp = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearSeasonExp() {
      bitField0_ = (bitField0_ & ~0x00000004);
      seasonExp_ = 0L;
      onChanged();
      return this;
    }

    private int groundNum_ ;
    /**
     * <code>optional int32 groundNum = 4;</code>
     * @return Whether the groundNum field is set.
     */
    @java.lang.Override
    public boolean hasGroundNum() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int32 groundNum = 4;</code>
     * @return The groundNum.
     */
    @java.lang.Override
    public int getGroundNum() {
      return groundNum_;
    }
    /**
     * <code>optional int32 groundNum = 4;</code>
     * @param value The groundNum to set.
     * @return This builder for chaining.
     */
    public Builder setGroundNum(int value) {

      groundNum_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 groundNum = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearGroundNum() {
      bitField0_ = (bitField0_ & ~0x00000008);
      groundNum_ = 0;
      onChanged();
      return this;
    }

    private long groundOutput_ ;
    /**
     * <code>optional int64 groundOutput = 5;</code>
     * @return Whether the groundOutput field is set.
     */
    @java.lang.Override
    public boolean hasGroundOutput() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional int64 groundOutput = 5;</code>
     * @return The groundOutput.
     */
    @java.lang.Override
    public long getGroundOutput() {
      return groundOutput_;
    }
    /**
     * <code>optional int64 groundOutput = 5;</code>
     * @param value The groundOutput to set.
     * @return This builder for chaining.
     */
    public Builder setGroundOutput(long value) {

      groundOutput_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 groundOutput = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearGroundOutput() {
      bitField0_ = (bitField0_ & ~0x00000010);
      groundOutput_ = 0L;
      onChanged();
      return this;
    }

    private long unionId_ ;
    /**
     * <code>optional int64 unionId = 6;</code>
     * @return Whether the unionId field is set.
     */
    @java.lang.Override
    public boolean hasUnionId() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional int64 unionId = 6;</code>
     * @return The unionId.
     */
    @java.lang.Override
    public long getUnionId() {
      return unionId_;
    }
    /**
     * <code>optional int64 unionId = 6;</code>
     * @param value The unionId to set.
     * @return This builder for chaining.
     */
    public Builder setUnionId(long value) {

      unionId_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 unionId = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearUnionId() {
      bitField0_ = (bitField0_ & ~0x00000020);
      unionId_ = 0L;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.WarSeasonCommonPlayerShowMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.WarSeasonCommonPlayerShowMsg)
  private static final xddq.pb.WarSeasonCommonPlayerShowMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.WarSeasonCommonPlayerShowMsg();
  }

  public static xddq.pb.WarSeasonCommonPlayerShowMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<WarSeasonCommonPlayerShowMsg>
      PARSER = new com.google.protobuf.AbstractParser<WarSeasonCommonPlayerShowMsg>() {
    @java.lang.Override
    public WarSeasonCommonPlayerShowMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<WarSeasonCommonPlayerShowMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<WarSeasonCommonPlayerShowMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.WarSeasonCommonPlayerShowMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

