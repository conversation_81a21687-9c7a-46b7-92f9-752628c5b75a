// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.DivineInsightUnionHelpOneRsp}
 */
public final class DivineInsightUnionHelpOneRsp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.DivineInsightUnionHelpOneRsp)
    DivineInsightUnionHelpOneRspOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      DivineInsightUnionHelpOneRsp.class.getName());
  }
  // Use DivineInsightUnionHelpOneRsp.newBuilder() to construct.
  private DivineInsightUnionHelpOneRsp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private DivineInsightUnionHelpOneRsp() {
    divineUnionHelpDataMsg_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_DivineInsightUnionHelpOneRsp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_DivineInsightUnionHelpOneRsp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.DivineInsightUnionHelpOneRsp.class, xddq.pb.DivineInsightUnionHelpOneRsp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int DIVINEUNIONHELPDATAMSG_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.DivineUnionHelpDataMsg> divineUnionHelpDataMsg_;
  /**
   * <code>repeated .xddq.pb.DivineUnionHelpDataMsg divineUnionHelpDataMsg = 3;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.DivineUnionHelpDataMsg> getDivineUnionHelpDataMsgList() {
    return divineUnionHelpDataMsg_;
  }
  /**
   * <code>repeated .xddq.pb.DivineUnionHelpDataMsg divineUnionHelpDataMsg = 3;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.DivineUnionHelpDataMsgOrBuilder> 
      getDivineUnionHelpDataMsgOrBuilderList() {
    return divineUnionHelpDataMsg_;
  }
  /**
   * <code>repeated .xddq.pb.DivineUnionHelpDataMsg divineUnionHelpDataMsg = 3;</code>
   */
  @java.lang.Override
  public int getDivineUnionHelpDataMsgCount() {
    return divineUnionHelpDataMsg_.size();
  }
  /**
   * <code>repeated .xddq.pb.DivineUnionHelpDataMsg divineUnionHelpDataMsg = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.DivineUnionHelpDataMsg getDivineUnionHelpDataMsg(int index) {
    return divineUnionHelpDataMsg_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.DivineUnionHelpDataMsg divineUnionHelpDataMsg = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.DivineUnionHelpDataMsgOrBuilder getDivineUnionHelpDataMsgOrBuilder(
      int index) {
    return divineUnionHelpDataMsg_.get(index);
  }

  public static final int UNIONDIVINEHELPTIMES_FIELD_NUMBER = 4;
  private int unionDivineHelpTimes_ = 0;
  /**
   * <code>optional int32 unionDivineHelpTimes = 4;</code>
   * @return Whether the unionDivineHelpTimes field is set.
   */
  @java.lang.Override
  public boolean hasUnionDivineHelpTimes() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int32 unionDivineHelpTimes = 4;</code>
   * @return The unionDivineHelpTimes.
   */
  @java.lang.Override
  public int getUnionDivineHelpTimes() {
    return unionDivineHelpTimes_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    for (int i = 0; i < getDivineUnionHelpDataMsgCount(); i++) {
      if (!getDivineUnionHelpDataMsg(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    for (int i = 0; i < divineUnionHelpDataMsg_.size(); i++) {
      output.writeMessage(3, divineUnionHelpDataMsg_.get(i));
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(4, unionDivineHelpTimes_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    for (int i = 0; i < divineUnionHelpDataMsg_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, divineUnionHelpDataMsg_.get(i));
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(4, unionDivineHelpTimes_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.DivineInsightUnionHelpOneRsp)) {
      return super.equals(obj);
    }
    xddq.pb.DivineInsightUnionHelpOneRsp other = (xddq.pb.DivineInsightUnionHelpOneRsp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (!getDivineUnionHelpDataMsgList()
        .equals(other.getDivineUnionHelpDataMsgList())) return false;
    if (hasUnionDivineHelpTimes() != other.hasUnionDivineHelpTimes()) return false;
    if (hasUnionDivineHelpTimes()) {
      if (getUnionDivineHelpTimes()
          != other.getUnionDivineHelpTimes()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (getDivineUnionHelpDataMsgCount() > 0) {
      hash = (37 * hash) + DIVINEUNIONHELPDATAMSG_FIELD_NUMBER;
      hash = (53 * hash) + getDivineUnionHelpDataMsgList().hashCode();
    }
    if (hasUnionDivineHelpTimes()) {
      hash = (37 * hash) + UNIONDIVINEHELPTIMES_FIELD_NUMBER;
      hash = (53 * hash) + getUnionDivineHelpTimes();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.DivineInsightUnionHelpOneRsp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.DivineInsightUnionHelpOneRsp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.DivineInsightUnionHelpOneRsp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.DivineInsightUnionHelpOneRsp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.DivineInsightUnionHelpOneRsp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.DivineInsightUnionHelpOneRsp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.DivineInsightUnionHelpOneRsp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.DivineInsightUnionHelpOneRsp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.DivineInsightUnionHelpOneRsp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.DivineInsightUnionHelpOneRsp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.DivineInsightUnionHelpOneRsp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.DivineInsightUnionHelpOneRsp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.DivineInsightUnionHelpOneRsp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.DivineInsightUnionHelpOneRsp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.DivineInsightUnionHelpOneRsp)
      xddq.pb.DivineInsightUnionHelpOneRspOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_DivineInsightUnionHelpOneRsp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_DivineInsightUnionHelpOneRsp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.DivineInsightUnionHelpOneRsp.class, xddq.pb.DivineInsightUnionHelpOneRsp.Builder.class);
    }

    // Construct using xddq.pb.DivineInsightUnionHelpOneRsp.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      if (divineUnionHelpDataMsgBuilder_ == null) {
        divineUnionHelpDataMsg_ = java.util.Collections.emptyList();
      } else {
        divineUnionHelpDataMsg_ = null;
        divineUnionHelpDataMsgBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000002);
      unionDivineHelpTimes_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_DivineInsightUnionHelpOneRsp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.DivineInsightUnionHelpOneRsp getDefaultInstanceForType() {
      return xddq.pb.DivineInsightUnionHelpOneRsp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.DivineInsightUnionHelpOneRsp build() {
      xddq.pb.DivineInsightUnionHelpOneRsp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.DivineInsightUnionHelpOneRsp buildPartial() {
      xddq.pb.DivineInsightUnionHelpOneRsp result = new xddq.pb.DivineInsightUnionHelpOneRsp(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.DivineInsightUnionHelpOneRsp result) {
      if (divineUnionHelpDataMsgBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          divineUnionHelpDataMsg_ = java.util.Collections.unmodifiableList(divineUnionHelpDataMsg_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.divineUnionHelpDataMsg_ = divineUnionHelpDataMsg_;
      } else {
        result.divineUnionHelpDataMsg_ = divineUnionHelpDataMsgBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.DivineInsightUnionHelpOneRsp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.unionDivineHelpTimes_ = unionDivineHelpTimes_;
        to_bitField0_ |= 0x00000002;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.DivineInsightUnionHelpOneRsp) {
        return mergeFrom((xddq.pb.DivineInsightUnionHelpOneRsp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.DivineInsightUnionHelpOneRsp other) {
      if (other == xddq.pb.DivineInsightUnionHelpOneRsp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (divineUnionHelpDataMsgBuilder_ == null) {
        if (!other.divineUnionHelpDataMsg_.isEmpty()) {
          if (divineUnionHelpDataMsg_.isEmpty()) {
            divineUnionHelpDataMsg_ = other.divineUnionHelpDataMsg_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureDivineUnionHelpDataMsgIsMutable();
            divineUnionHelpDataMsg_.addAll(other.divineUnionHelpDataMsg_);
          }
          onChanged();
        }
      } else {
        if (!other.divineUnionHelpDataMsg_.isEmpty()) {
          if (divineUnionHelpDataMsgBuilder_.isEmpty()) {
            divineUnionHelpDataMsgBuilder_.dispose();
            divineUnionHelpDataMsgBuilder_ = null;
            divineUnionHelpDataMsg_ = other.divineUnionHelpDataMsg_;
            bitField0_ = (bitField0_ & ~0x00000002);
            divineUnionHelpDataMsgBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetDivineUnionHelpDataMsgFieldBuilder() : null;
          } else {
            divineUnionHelpDataMsgBuilder_.addAllMessages(other.divineUnionHelpDataMsg_);
          }
        }
      }
      if (other.hasUnionDivineHelpTimes()) {
        setUnionDivineHelpTimes(other.getUnionDivineHelpTimes());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      for (int i = 0; i < getDivineUnionHelpDataMsgCount(); i++) {
        if (!getDivineUnionHelpDataMsg(i).isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 26: {
              xddq.pb.DivineUnionHelpDataMsg m =
                  input.readMessage(
                      xddq.pb.DivineUnionHelpDataMsg.parser(),
                      extensionRegistry);
              if (divineUnionHelpDataMsgBuilder_ == null) {
                ensureDivineUnionHelpDataMsgIsMutable();
                divineUnionHelpDataMsg_.add(m);
              } else {
                divineUnionHelpDataMsgBuilder_.addMessage(m);
              }
              break;
            } // case 26
            case 32: {
              unionDivineHelpTimes_ = input.readInt32();
              bitField0_ |= 0x00000004;
              break;
            } // case 32
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.DivineUnionHelpDataMsg> divineUnionHelpDataMsg_ =
      java.util.Collections.emptyList();
    private void ensureDivineUnionHelpDataMsgIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        divineUnionHelpDataMsg_ = new java.util.ArrayList<xddq.pb.DivineUnionHelpDataMsg>(divineUnionHelpDataMsg_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.DivineUnionHelpDataMsg, xddq.pb.DivineUnionHelpDataMsg.Builder, xddq.pb.DivineUnionHelpDataMsgOrBuilder> divineUnionHelpDataMsgBuilder_;

    /**
     * <code>repeated .xddq.pb.DivineUnionHelpDataMsg divineUnionHelpDataMsg = 3;</code>
     */
    public java.util.List<xddq.pb.DivineUnionHelpDataMsg> getDivineUnionHelpDataMsgList() {
      if (divineUnionHelpDataMsgBuilder_ == null) {
        return java.util.Collections.unmodifiableList(divineUnionHelpDataMsg_);
      } else {
        return divineUnionHelpDataMsgBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.DivineUnionHelpDataMsg divineUnionHelpDataMsg = 3;</code>
     */
    public int getDivineUnionHelpDataMsgCount() {
      if (divineUnionHelpDataMsgBuilder_ == null) {
        return divineUnionHelpDataMsg_.size();
      } else {
        return divineUnionHelpDataMsgBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.DivineUnionHelpDataMsg divineUnionHelpDataMsg = 3;</code>
     */
    public xddq.pb.DivineUnionHelpDataMsg getDivineUnionHelpDataMsg(int index) {
      if (divineUnionHelpDataMsgBuilder_ == null) {
        return divineUnionHelpDataMsg_.get(index);
      } else {
        return divineUnionHelpDataMsgBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.DivineUnionHelpDataMsg divineUnionHelpDataMsg = 3;</code>
     */
    public Builder setDivineUnionHelpDataMsg(
        int index, xddq.pb.DivineUnionHelpDataMsg value) {
      if (divineUnionHelpDataMsgBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDivineUnionHelpDataMsgIsMutable();
        divineUnionHelpDataMsg_.set(index, value);
        onChanged();
      } else {
        divineUnionHelpDataMsgBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DivineUnionHelpDataMsg divineUnionHelpDataMsg = 3;</code>
     */
    public Builder setDivineUnionHelpDataMsg(
        int index, xddq.pb.DivineUnionHelpDataMsg.Builder builderForValue) {
      if (divineUnionHelpDataMsgBuilder_ == null) {
        ensureDivineUnionHelpDataMsgIsMutable();
        divineUnionHelpDataMsg_.set(index, builderForValue.build());
        onChanged();
      } else {
        divineUnionHelpDataMsgBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DivineUnionHelpDataMsg divineUnionHelpDataMsg = 3;</code>
     */
    public Builder addDivineUnionHelpDataMsg(xddq.pb.DivineUnionHelpDataMsg value) {
      if (divineUnionHelpDataMsgBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDivineUnionHelpDataMsgIsMutable();
        divineUnionHelpDataMsg_.add(value);
        onChanged();
      } else {
        divineUnionHelpDataMsgBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DivineUnionHelpDataMsg divineUnionHelpDataMsg = 3;</code>
     */
    public Builder addDivineUnionHelpDataMsg(
        int index, xddq.pb.DivineUnionHelpDataMsg value) {
      if (divineUnionHelpDataMsgBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDivineUnionHelpDataMsgIsMutable();
        divineUnionHelpDataMsg_.add(index, value);
        onChanged();
      } else {
        divineUnionHelpDataMsgBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DivineUnionHelpDataMsg divineUnionHelpDataMsg = 3;</code>
     */
    public Builder addDivineUnionHelpDataMsg(
        xddq.pb.DivineUnionHelpDataMsg.Builder builderForValue) {
      if (divineUnionHelpDataMsgBuilder_ == null) {
        ensureDivineUnionHelpDataMsgIsMutable();
        divineUnionHelpDataMsg_.add(builderForValue.build());
        onChanged();
      } else {
        divineUnionHelpDataMsgBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DivineUnionHelpDataMsg divineUnionHelpDataMsg = 3;</code>
     */
    public Builder addDivineUnionHelpDataMsg(
        int index, xddq.pb.DivineUnionHelpDataMsg.Builder builderForValue) {
      if (divineUnionHelpDataMsgBuilder_ == null) {
        ensureDivineUnionHelpDataMsgIsMutable();
        divineUnionHelpDataMsg_.add(index, builderForValue.build());
        onChanged();
      } else {
        divineUnionHelpDataMsgBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DivineUnionHelpDataMsg divineUnionHelpDataMsg = 3;</code>
     */
    public Builder addAllDivineUnionHelpDataMsg(
        java.lang.Iterable<? extends xddq.pb.DivineUnionHelpDataMsg> values) {
      if (divineUnionHelpDataMsgBuilder_ == null) {
        ensureDivineUnionHelpDataMsgIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, divineUnionHelpDataMsg_);
        onChanged();
      } else {
        divineUnionHelpDataMsgBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DivineUnionHelpDataMsg divineUnionHelpDataMsg = 3;</code>
     */
    public Builder clearDivineUnionHelpDataMsg() {
      if (divineUnionHelpDataMsgBuilder_ == null) {
        divineUnionHelpDataMsg_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        divineUnionHelpDataMsgBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DivineUnionHelpDataMsg divineUnionHelpDataMsg = 3;</code>
     */
    public Builder removeDivineUnionHelpDataMsg(int index) {
      if (divineUnionHelpDataMsgBuilder_ == null) {
        ensureDivineUnionHelpDataMsgIsMutable();
        divineUnionHelpDataMsg_.remove(index);
        onChanged();
      } else {
        divineUnionHelpDataMsgBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.DivineUnionHelpDataMsg divineUnionHelpDataMsg = 3;</code>
     */
    public xddq.pb.DivineUnionHelpDataMsg.Builder getDivineUnionHelpDataMsgBuilder(
        int index) {
      return internalGetDivineUnionHelpDataMsgFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.DivineUnionHelpDataMsg divineUnionHelpDataMsg = 3;</code>
     */
    public xddq.pb.DivineUnionHelpDataMsgOrBuilder getDivineUnionHelpDataMsgOrBuilder(
        int index) {
      if (divineUnionHelpDataMsgBuilder_ == null) {
        return divineUnionHelpDataMsg_.get(index);  } else {
        return divineUnionHelpDataMsgBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.DivineUnionHelpDataMsg divineUnionHelpDataMsg = 3;</code>
     */
    public java.util.List<? extends xddq.pb.DivineUnionHelpDataMsgOrBuilder> 
         getDivineUnionHelpDataMsgOrBuilderList() {
      if (divineUnionHelpDataMsgBuilder_ != null) {
        return divineUnionHelpDataMsgBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(divineUnionHelpDataMsg_);
      }
    }
    /**
     * <code>repeated .xddq.pb.DivineUnionHelpDataMsg divineUnionHelpDataMsg = 3;</code>
     */
    public xddq.pb.DivineUnionHelpDataMsg.Builder addDivineUnionHelpDataMsgBuilder() {
      return internalGetDivineUnionHelpDataMsgFieldBuilder().addBuilder(
          xddq.pb.DivineUnionHelpDataMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.DivineUnionHelpDataMsg divineUnionHelpDataMsg = 3;</code>
     */
    public xddq.pb.DivineUnionHelpDataMsg.Builder addDivineUnionHelpDataMsgBuilder(
        int index) {
      return internalGetDivineUnionHelpDataMsgFieldBuilder().addBuilder(
          index, xddq.pb.DivineUnionHelpDataMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.DivineUnionHelpDataMsg divineUnionHelpDataMsg = 3;</code>
     */
    public java.util.List<xddq.pb.DivineUnionHelpDataMsg.Builder> 
         getDivineUnionHelpDataMsgBuilderList() {
      return internalGetDivineUnionHelpDataMsgFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.DivineUnionHelpDataMsg, xddq.pb.DivineUnionHelpDataMsg.Builder, xddq.pb.DivineUnionHelpDataMsgOrBuilder> 
        internalGetDivineUnionHelpDataMsgFieldBuilder() {
      if (divineUnionHelpDataMsgBuilder_ == null) {
        divineUnionHelpDataMsgBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.DivineUnionHelpDataMsg, xddq.pb.DivineUnionHelpDataMsg.Builder, xddq.pb.DivineUnionHelpDataMsgOrBuilder>(
                divineUnionHelpDataMsg_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        divineUnionHelpDataMsg_ = null;
      }
      return divineUnionHelpDataMsgBuilder_;
    }

    private int unionDivineHelpTimes_ ;
    /**
     * <code>optional int32 unionDivineHelpTimes = 4;</code>
     * @return Whether the unionDivineHelpTimes field is set.
     */
    @java.lang.Override
    public boolean hasUnionDivineHelpTimes() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int32 unionDivineHelpTimes = 4;</code>
     * @return The unionDivineHelpTimes.
     */
    @java.lang.Override
    public int getUnionDivineHelpTimes() {
      return unionDivineHelpTimes_;
    }
    /**
     * <code>optional int32 unionDivineHelpTimes = 4;</code>
     * @param value The unionDivineHelpTimes to set.
     * @return This builder for chaining.
     */
    public Builder setUnionDivineHelpTimes(int value) {

      unionDivineHelpTimes_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 unionDivineHelpTimes = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearUnionDivineHelpTimes() {
      bitField0_ = (bitField0_ & ~0x00000004);
      unionDivineHelpTimes_ = 0;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.DivineInsightUnionHelpOneRsp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.DivineInsightUnionHelpOneRsp)
  private static final xddq.pb.DivineInsightUnionHelpOneRsp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.DivineInsightUnionHelpOneRsp();
  }

  public static xddq.pb.DivineInsightUnionHelpOneRsp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<DivineInsightUnionHelpOneRsp>
      PARSER = new com.google.protobuf.AbstractParser<DivineInsightUnionHelpOneRsp>() {
    @java.lang.Override
    public DivineInsightUnionHelpOneRsp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<DivineInsightUnionHelpOneRsp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<DivineInsightUnionHelpOneRsp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.DivineInsightUnionHelpOneRsp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

