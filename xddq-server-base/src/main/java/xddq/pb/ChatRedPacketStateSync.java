// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.ChatRedPacketStateSync}
 */
public final class ChatRedPacketStateSync extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.ChatRedPacketStateSync)
    ChatRedPacketStateSyncOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      ChatRedPacketStateSync.class.getName());
  }
  // Use ChatRedPacketStateSync.newBuilder() to construct.
  private ChatRedPacketStateSync(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private ChatRedPacketStateSync() {
    classifyTag_ = "";
    stateList_ = java.util.Collections.emptyList();
    drawCount_ = java.util.Collections.emptyList();
    dailyCount_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_ChatRedPacketStateSync_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_ChatRedPacketStateSync_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.ChatRedPacketStateSync.class, xddq.pb.ChatRedPacketStateSync.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int TYPE_FIELD_NUMBER = 2;
  private int type_ = 0;
  /**
   * <code>required int32 type = 2;</code>
   * @return Whether the type field is set.
   */
  @java.lang.Override
  public boolean hasType() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>required int32 type = 2;</code>
   * @return The type.
   */
  @java.lang.Override
  public int getType() {
    return type_;
  }

  public static final int CLASSIFYTAG_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private volatile java.lang.Object classifyTag_ = "";
  /**
   * <code>required string classifyTag = 3;</code>
   * @return Whether the classifyTag field is set.
   */
  @java.lang.Override
  public boolean hasClassifyTag() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>required string classifyTag = 3;</code>
   * @return The classifyTag.
   */
  @java.lang.Override
  public java.lang.String getClassifyTag() {
    java.lang.Object ref = classifyTag_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        classifyTag_ = s;
      }
      return s;
    }
  }
  /**
   * <code>required string classifyTag = 3;</code>
   * @return The bytes for classifyTag.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getClassifyTagBytes() {
    java.lang.Object ref = classifyTag_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      classifyTag_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int STATELIST_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.ChatRedPacketSimpleMsg> stateList_;
  /**
   * <code>repeated .xddq.pb.ChatRedPacketSimpleMsg stateList = 4;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.ChatRedPacketSimpleMsg> getStateListList() {
    return stateList_;
  }
  /**
   * <code>repeated .xddq.pb.ChatRedPacketSimpleMsg stateList = 4;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.ChatRedPacketSimpleMsgOrBuilder> 
      getStateListOrBuilderList() {
    return stateList_;
  }
  /**
   * <code>repeated .xddq.pb.ChatRedPacketSimpleMsg stateList = 4;</code>
   */
  @java.lang.Override
  public int getStateListCount() {
    return stateList_.size();
  }
  /**
   * <code>repeated .xddq.pb.ChatRedPacketSimpleMsg stateList = 4;</code>
   */
  @java.lang.Override
  public xddq.pb.ChatRedPacketSimpleMsg getStateList(int index) {
    return stateList_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.ChatRedPacketSimpleMsg stateList = 4;</code>
   */
  @java.lang.Override
  public xddq.pb.ChatRedPacketSimpleMsgOrBuilder getStateListOrBuilder(
      int index) {
    return stateList_.get(index);
  }

  public static final int DRAWCOUNT_FIELD_NUMBER = 5;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.ChatRedPacketDrawCount> drawCount_;
  /**
   * <code>repeated .xddq.pb.ChatRedPacketDrawCount drawCount = 5;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.ChatRedPacketDrawCount> getDrawCountList() {
    return drawCount_;
  }
  /**
   * <code>repeated .xddq.pb.ChatRedPacketDrawCount drawCount = 5;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.ChatRedPacketDrawCountOrBuilder> 
      getDrawCountOrBuilderList() {
    return drawCount_;
  }
  /**
   * <code>repeated .xddq.pb.ChatRedPacketDrawCount drawCount = 5;</code>
   */
  @java.lang.Override
  public int getDrawCountCount() {
    return drawCount_.size();
  }
  /**
   * <code>repeated .xddq.pb.ChatRedPacketDrawCount drawCount = 5;</code>
   */
  @java.lang.Override
  public xddq.pb.ChatRedPacketDrawCount getDrawCount(int index) {
    return drawCount_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.ChatRedPacketDrawCount drawCount = 5;</code>
   */
  @java.lang.Override
  public xddq.pb.ChatRedPacketDrawCountOrBuilder getDrawCountOrBuilder(
      int index) {
    return drawCount_.get(index);
  }

  public static final int DAILYCOUNT_FIELD_NUMBER = 6;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.ChatRedPacketDrawCount> dailyCount_;
  /**
   * <code>repeated .xddq.pb.ChatRedPacketDrawCount dailyCount = 6;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.ChatRedPacketDrawCount> getDailyCountList() {
    return dailyCount_;
  }
  /**
   * <code>repeated .xddq.pb.ChatRedPacketDrawCount dailyCount = 6;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.ChatRedPacketDrawCountOrBuilder> 
      getDailyCountOrBuilderList() {
    return dailyCount_;
  }
  /**
   * <code>repeated .xddq.pb.ChatRedPacketDrawCount dailyCount = 6;</code>
   */
  @java.lang.Override
  public int getDailyCountCount() {
    return dailyCount_.size();
  }
  /**
   * <code>repeated .xddq.pb.ChatRedPacketDrawCount dailyCount = 6;</code>
   */
  @java.lang.Override
  public xddq.pb.ChatRedPacketDrawCount getDailyCount(int index) {
    return dailyCount_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.ChatRedPacketDrawCount dailyCount = 6;</code>
   */
  @java.lang.Override
  public xddq.pb.ChatRedPacketDrawCountOrBuilder getDailyCountOrBuilder(
      int index) {
    return dailyCount_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasType()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasClassifyTag()) {
      memoizedIsInitialized = 0;
      return false;
    }
    for (int i = 0; i < getStateListCount(); i++) {
      if (!getStateList(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    for (int i = 0; i < getDrawCountCount(); i++) {
      if (!getDrawCount(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    for (int i = 0; i < getDailyCountCount(); i++) {
      if (!getDailyCount(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(2, type_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 3, classifyTag_);
    }
    for (int i = 0; i < stateList_.size(); i++) {
      output.writeMessage(4, stateList_.get(i));
    }
    for (int i = 0; i < drawCount_.size(); i++) {
      output.writeMessage(5, drawCount_.get(i));
    }
    for (int i = 0; i < dailyCount_.size(); i++) {
      output.writeMessage(6, dailyCount_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, type_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(3, classifyTag_);
    }
    for (int i = 0; i < stateList_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(4, stateList_.get(i));
    }
    for (int i = 0; i < drawCount_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(5, drawCount_.get(i));
    }
    for (int i = 0; i < dailyCount_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(6, dailyCount_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.ChatRedPacketStateSync)) {
      return super.equals(obj);
    }
    xddq.pb.ChatRedPacketStateSync other = (xddq.pb.ChatRedPacketStateSync) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (hasType() != other.hasType()) return false;
    if (hasType()) {
      if (getType()
          != other.getType()) return false;
    }
    if (hasClassifyTag() != other.hasClassifyTag()) return false;
    if (hasClassifyTag()) {
      if (!getClassifyTag()
          .equals(other.getClassifyTag())) return false;
    }
    if (!getStateListList()
        .equals(other.getStateListList())) return false;
    if (!getDrawCountList()
        .equals(other.getDrawCountList())) return false;
    if (!getDailyCountList()
        .equals(other.getDailyCountList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (hasType()) {
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
    }
    if (hasClassifyTag()) {
      hash = (37 * hash) + CLASSIFYTAG_FIELD_NUMBER;
      hash = (53 * hash) + getClassifyTag().hashCode();
    }
    if (getStateListCount() > 0) {
      hash = (37 * hash) + STATELIST_FIELD_NUMBER;
      hash = (53 * hash) + getStateListList().hashCode();
    }
    if (getDrawCountCount() > 0) {
      hash = (37 * hash) + DRAWCOUNT_FIELD_NUMBER;
      hash = (53 * hash) + getDrawCountList().hashCode();
    }
    if (getDailyCountCount() > 0) {
      hash = (37 * hash) + DAILYCOUNT_FIELD_NUMBER;
      hash = (53 * hash) + getDailyCountList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.ChatRedPacketStateSync parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ChatRedPacketStateSync parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ChatRedPacketStateSync parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ChatRedPacketStateSync parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ChatRedPacketStateSync parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ChatRedPacketStateSync parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ChatRedPacketStateSync parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.ChatRedPacketStateSync parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.ChatRedPacketStateSync parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.ChatRedPacketStateSync parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.ChatRedPacketStateSync parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.ChatRedPacketStateSync parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.ChatRedPacketStateSync prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.ChatRedPacketStateSync}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.ChatRedPacketStateSync)
      xddq.pb.ChatRedPacketStateSyncOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ChatRedPacketStateSync_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ChatRedPacketStateSync_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.ChatRedPacketStateSync.class, xddq.pb.ChatRedPacketStateSync.Builder.class);
    }

    // Construct using xddq.pb.ChatRedPacketStateSync.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      type_ = 0;
      classifyTag_ = "";
      if (stateListBuilder_ == null) {
        stateList_ = java.util.Collections.emptyList();
      } else {
        stateList_ = null;
        stateListBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000008);
      if (drawCountBuilder_ == null) {
        drawCount_ = java.util.Collections.emptyList();
      } else {
        drawCount_ = null;
        drawCountBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000010);
      if (dailyCountBuilder_ == null) {
        dailyCount_ = java.util.Collections.emptyList();
      } else {
        dailyCount_ = null;
        dailyCountBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000020);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ChatRedPacketStateSync_descriptor;
    }

    @java.lang.Override
    public xddq.pb.ChatRedPacketStateSync getDefaultInstanceForType() {
      return xddq.pb.ChatRedPacketStateSync.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.ChatRedPacketStateSync build() {
      xddq.pb.ChatRedPacketStateSync result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.ChatRedPacketStateSync buildPartial() {
      xddq.pb.ChatRedPacketStateSync result = new xddq.pb.ChatRedPacketStateSync(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.ChatRedPacketStateSync result) {
      if (stateListBuilder_ == null) {
        if (((bitField0_ & 0x00000008) != 0)) {
          stateList_ = java.util.Collections.unmodifiableList(stateList_);
          bitField0_ = (bitField0_ & ~0x00000008);
        }
        result.stateList_ = stateList_;
      } else {
        result.stateList_ = stateListBuilder_.build();
      }
      if (drawCountBuilder_ == null) {
        if (((bitField0_ & 0x00000010) != 0)) {
          drawCount_ = java.util.Collections.unmodifiableList(drawCount_);
          bitField0_ = (bitField0_ & ~0x00000010);
        }
        result.drawCount_ = drawCount_;
      } else {
        result.drawCount_ = drawCountBuilder_.build();
      }
      if (dailyCountBuilder_ == null) {
        if (((bitField0_ & 0x00000020) != 0)) {
          dailyCount_ = java.util.Collections.unmodifiableList(dailyCount_);
          bitField0_ = (bitField0_ & ~0x00000020);
        }
        result.dailyCount_ = dailyCount_;
      } else {
        result.dailyCount_ = dailyCountBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.ChatRedPacketStateSync result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.type_ = type_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.classifyTag_ = classifyTag_;
        to_bitField0_ |= 0x00000004;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.ChatRedPacketStateSync) {
        return mergeFrom((xddq.pb.ChatRedPacketStateSync)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.ChatRedPacketStateSync other) {
      if (other == xddq.pb.ChatRedPacketStateSync.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (other.hasType()) {
        setType(other.getType());
      }
      if (other.hasClassifyTag()) {
        classifyTag_ = other.classifyTag_;
        bitField0_ |= 0x00000004;
        onChanged();
      }
      if (stateListBuilder_ == null) {
        if (!other.stateList_.isEmpty()) {
          if (stateList_.isEmpty()) {
            stateList_ = other.stateList_;
            bitField0_ = (bitField0_ & ~0x00000008);
          } else {
            ensureStateListIsMutable();
            stateList_.addAll(other.stateList_);
          }
          onChanged();
        }
      } else {
        if (!other.stateList_.isEmpty()) {
          if (stateListBuilder_.isEmpty()) {
            stateListBuilder_.dispose();
            stateListBuilder_ = null;
            stateList_ = other.stateList_;
            bitField0_ = (bitField0_ & ~0x00000008);
            stateListBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetStateListFieldBuilder() : null;
          } else {
            stateListBuilder_.addAllMessages(other.stateList_);
          }
        }
      }
      if (drawCountBuilder_ == null) {
        if (!other.drawCount_.isEmpty()) {
          if (drawCount_.isEmpty()) {
            drawCount_ = other.drawCount_;
            bitField0_ = (bitField0_ & ~0x00000010);
          } else {
            ensureDrawCountIsMutable();
            drawCount_.addAll(other.drawCount_);
          }
          onChanged();
        }
      } else {
        if (!other.drawCount_.isEmpty()) {
          if (drawCountBuilder_.isEmpty()) {
            drawCountBuilder_.dispose();
            drawCountBuilder_ = null;
            drawCount_ = other.drawCount_;
            bitField0_ = (bitField0_ & ~0x00000010);
            drawCountBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetDrawCountFieldBuilder() : null;
          } else {
            drawCountBuilder_.addAllMessages(other.drawCount_);
          }
        }
      }
      if (dailyCountBuilder_ == null) {
        if (!other.dailyCount_.isEmpty()) {
          if (dailyCount_.isEmpty()) {
            dailyCount_ = other.dailyCount_;
            bitField0_ = (bitField0_ & ~0x00000020);
          } else {
            ensureDailyCountIsMutable();
            dailyCount_.addAll(other.dailyCount_);
          }
          onChanged();
        }
      } else {
        if (!other.dailyCount_.isEmpty()) {
          if (dailyCountBuilder_.isEmpty()) {
            dailyCountBuilder_.dispose();
            dailyCountBuilder_ = null;
            dailyCount_ = other.dailyCount_;
            bitField0_ = (bitField0_ & ~0x00000020);
            dailyCountBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetDailyCountFieldBuilder() : null;
          } else {
            dailyCountBuilder_.addAllMessages(other.dailyCount_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      if (!hasType()) {
        return false;
      }
      if (!hasClassifyTag()) {
        return false;
      }
      for (int i = 0; i < getStateListCount(); i++) {
        if (!getStateList(i).isInitialized()) {
          return false;
        }
      }
      for (int i = 0; i < getDrawCountCount(); i++) {
        if (!getDrawCount(i).isInitialized()) {
          return false;
        }
      }
      for (int i = 0; i < getDailyCountCount(); i++) {
        if (!getDailyCount(i).isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              type_ = input.readInt32();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 26: {
              classifyTag_ = input.readBytes();
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 34: {
              xddq.pb.ChatRedPacketSimpleMsg m =
                  input.readMessage(
                      xddq.pb.ChatRedPacketSimpleMsg.parser(),
                      extensionRegistry);
              if (stateListBuilder_ == null) {
                ensureStateListIsMutable();
                stateList_.add(m);
              } else {
                stateListBuilder_.addMessage(m);
              }
              break;
            } // case 34
            case 42: {
              xddq.pb.ChatRedPacketDrawCount m =
                  input.readMessage(
                      xddq.pb.ChatRedPacketDrawCount.parser(),
                      extensionRegistry);
              if (drawCountBuilder_ == null) {
                ensureDrawCountIsMutable();
                drawCount_.add(m);
              } else {
                drawCountBuilder_.addMessage(m);
              }
              break;
            } // case 42
            case 50: {
              xddq.pb.ChatRedPacketDrawCount m =
                  input.readMessage(
                      xddq.pb.ChatRedPacketDrawCount.parser(),
                      extensionRegistry);
              if (dailyCountBuilder_ == null) {
                ensureDailyCountIsMutable();
                dailyCount_.add(m);
              } else {
                dailyCountBuilder_.addMessage(m);
              }
              break;
            } // case 50
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private int type_ ;
    /**
     * <code>required int32 type = 2;</code>
     * @return Whether the type field is set.
     */
    @java.lang.Override
    public boolean hasType() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>required int32 type = 2;</code>
     * @return The type.
     */
    @java.lang.Override
    public int getType() {
      return type_;
    }
    /**
     * <code>required int32 type = 2;</code>
     * @param value The type to set.
     * @return This builder for chaining.
     */
    public Builder setType(int value) {

      type_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 type = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearType() {
      bitField0_ = (bitField0_ & ~0x00000002);
      type_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object classifyTag_ = "";
    /**
     * <code>required string classifyTag = 3;</code>
     * @return Whether the classifyTag field is set.
     */
    public boolean hasClassifyTag() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>required string classifyTag = 3;</code>
     * @return The classifyTag.
     */
    public java.lang.String getClassifyTag() {
      java.lang.Object ref = classifyTag_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          classifyTag_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>required string classifyTag = 3;</code>
     * @return The bytes for classifyTag.
     */
    public com.google.protobuf.ByteString
        getClassifyTagBytes() {
      java.lang.Object ref = classifyTag_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        classifyTag_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>required string classifyTag = 3;</code>
     * @param value The classifyTag to set.
     * @return This builder for chaining.
     */
    public Builder setClassifyTag(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      classifyTag_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>required string classifyTag = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearClassifyTag() {
      classifyTag_ = getDefaultInstance().getClassifyTag();
      bitField0_ = (bitField0_ & ~0x00000004);
      onChanged();
      return this;
    }
    /**
     * <code>required string classifyTag = 3;</code>
     * @param value The bytes for classifyTag to set.
     * @return This builder for chaining.
     */
    public Builder setClassifyTagBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      classifyTag_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.ChatRedPacketSimpleMsg> stateList_ =
      java.util.Collections.emptyList();
    private void ensureStateListIsMutable() {
      if (!((bitField0_ & 0x00000008) != 0)) {
        stateList_ = new java.util.ArrayList<xddq.pb.ChatRedPacketSimpleMsg>(stateList_);
        bitField0_ |= 0x00000008;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.ChatRedPacketSimpleMsg, xddq.pb.ChatRedPacketSimpleMsg.Builder, xddq.pb.ChatRedPacketSimpleMsgOrBuilder> stateListBuilder_;

    /**
     * <code>repeated .xddq.pb.ChatRedPacketSimpleMsg stateList = 4;</code>
     */
    public java.util.List<xddq.pb.ChatRedPacketSimpleMsg> getStateListList() {
      if (stateListBuilder_ == null) {
        return java.util.Collections.unmodifiableList(stateList_);
      } else {
        return stateListBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.ChatRedPacketSimpleMsg stateList = 4;</code>
     */
    public int getStateListCount() {
      if (stateListBuilder_ == null) {
        return stateList_.size();
      } else {
        return stateListBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.ChatRedPacketSimpleMsg stateList = 4;</code>
     */
    public xddq.pb.ChatRedPacketSimpleMsg getStateList(int index) {
      if (stateListBuilder_ == null) {
        return stateList_.get(index);
      } else {
        return stateListBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.ChatRedPacketSimpleMsg stateList = 4;</code>
     */
    public Builder setStateList(
        int index, xddq.pb.ChatRedPacketSimpleMsg value) {
      if (stateListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureStateListIsMutable();
        stateList_.set(index, value);
        onChanged();
      } else {
        stateListBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ChatRedPacketSimpleMsg stateList = 4;</code>
     */
    public Builder setStateList(
        int index, xddq.pb.ChatRedPacketSimpleMsg.Builder builderForValue) {
      if (stateListBuilder_ == null) {
        ensureStateListIsMutable();
        stateList_.set(index, builderForValue.build());
        onChanged();
      } else {
        stateListBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ChatRedPacketSimpleMsg stateList = 4;</code>
     */
    public Builder addStateList(xddq.pb.ChatRedPacketSimpleMsg value) {
      if (stateListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureStateListIsMutable();
        stateList_.add(value);
        onChanged();
      } else {
        stateListBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ChatRedPacketSimpleMsg stateList = 4;</code>
     */
    public Builder addStateList(
        int index, xddq.pb.ChatRedPacketSimpleMsg value) {
      if (stateListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureStateListIsMutable();
        stateList_.add(index, value);
        onChanged();
      } else {
        stateListBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ChatRedPacketSimpleMsg stateList = 4;</code>
     */
    public Builder addStateList(
        xddq.pb.ChatRedPacketSimpleMsg.Builder builderForValue) {
      if (stateListBuilder_ == null) {
        ensureStateListIsMutable();
        stateList_.add(builderForValue.build());
        onChanged();
      } else {
        stateListBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ChatRedPacketSimpleMsg stateList = 4;</code>
     */
    public Builder addStateList(
        int index, xddq.pb.ChatRedPacketSimpleMsg.Builder builderForValue) {
      if (stateListBuilder_ == null) {
        ensureStateListIsMutable();
        stateList_.add(index, builderForValue.build());
        onChanged();
      } else {
        stateListBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ChatRedPacketSimpleMsg stateList = 4;</code>
     */
    public Builder addAllStateList(
        java.lang.Iterable<? extends xddq.pb.ChatRedPacketSimpleMsg> values) {
      if (stateListBuilder_ == null) {
        ensureStateListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, stateList_);
        onChanged();
      } else {
        stateListBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ChatRedPacketSimpleMsg stateList = 4;</code>
     */
    public Builder clearStateList() {
      if (stateListBuilder_ == null) {
        stateList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000008);
        onChanged();
      } else {
        stateListBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ChatRedPacketSimpleMsg stateList = 4;</code>
     */
    public Builder removeStateList(int index) {
      if (stateListBuilder_ == null) {
        ensureStateListIsMutable();
        stateList_.remove(index);
        onChanged();
      } else {
        stateListBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ChatRedPacketSimpleMsg stateList = 4;</code>
     */
    public xddq.pb.ChatRedPacketSimpleMsg.Builder getStateListBuilder(
        int index) {
      return internalGetStateListFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.ChatRedPacketSimpleMsg stateList = 4;</code>
     */
    public xddq.pb.ChatRedPacketSimpleMsgOrBuilder getStateListOrBuilder(
        int index) {
      if (stateListBuilder_ == null) {
        return stateList_.get(index);  } else {
        return stateListBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.ChatRedPacketSimpleMsg stateList = 4;</code>
     */
    public java.util.List<? extends xddq.pb.ChatRedPacketSimpleMsgOrBuilder> 
         getStateListOrBuilderList() {
      if (stateListBuilder_ != null) {
        return stateListBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(stateList_);
      }
    }
    /**
     * <code>repeated .xddq.pb.ChatRedPacketSimpleMsg stateList = 4;</code>
     */
    public xddq.pb.ChatRedPacketSimpleMsg.Builder addStateListBuilder() {
      return internalGetStateListFieldBuilder().addBuilder(
          xddq.pb.ChatRedPacketSimpleMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.ChatRedPacketSimpleMsg stateList = 4;</code>
     */
    public xddq.pb.ChatRedPacketSimpleMsg.Builder addStateListBuilder(
        int index) {
      return internalGetStateListFieldBuilder().addBuilder(
          index, xddq.pb.ChatRedPacketSimpleMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.ChatRedPacketSimpleMsg stateList = 4;</code>
     */
    public java.util.List<xddq.pb.ChatRedPacketSimpleMsg.Builder> 
         getStateListBuilderList() {
      return internalGetStateListFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.ChatRedPacketSimpleMsg, xddq.pb.ChatRedPacketSimpleMsg.Builder, xddq.pb.ChatRedPacketSimpleMsgOrBuilder> 
        internalGetStateListFieldBuilder() {
      if (stateListBuilder_ == null) {
        stateListBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.ChatRedPacketSimpleMsg, xddq.pb.ChatRedPacketSimpleMsg.Builder, xddq.pb.ChatRedPacketSimpleMsgOrBuilder>(
                stateList_,
                ((bitField0_ & 0x00000008) != 0),
                getParentForChildren(),
                isClean());
        stateList_ = null;
      }
      return stateListBuilder_;
    }

    private java.util.List<xddq.pb.ChatRedPacketDrawCount> drawCount_ =
      java.util.Collections.emptyList();
    private void ensureDrawCountIsMutable() {
      if (!((bitField0_ & 0x00000010) != 0)) {
        drawCount_ = new java.util.ArrayList<xddq.pb.ChatRedPacketDrawCount>(drawCount_);
        bitField0_ |= 0x00000010;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.ChatRedPacketDrawCount, xddq.pb.ChatRedPacketDrawCount.Builder, xddq.pb.ChatRedPacketDrawCountOrBuilder> drawCountBuilder_;

    /**
     * <code>repeated .xddq.pb.ChatRedPacketDrawCount drawCount = 5;</code>
     */
    public java.util.List<xddq.pb.ChatRedPacketDrawCount> getDrawCountList() {
      if (drawCountBuilder_ == null) {
        return java.util.Collections.unmodifiableList(drawCount_);
      } else {
        return drawCountBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.ChatRedPacketDrawCount drawCount = 5;</code>
     */
    public int getDrawCountCount() {
      if (drawCountBuilder_ == null) {
        return drawCount_.size();
      } else {
        return drawCountBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.ChatRedPacketDrawCount drawCount = 5;</code>
     */
    public xddq.pb.ChatRedPacketDrawCount getDrawCount(int index) {
      if (drawCountBuilder_ == null) {
        return drawCount_.get(index);
      } else {
        return drawCountBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.ChatRedPacketDrawCount drawCount = 5;</code>
     */
    public Builder setDrawCount(
        int index, xddq.pb.ChatRedPacketDrawCount value) {
      if (drawCountBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDrawCountIsMutable();
        drawCount_.set(index, value);
        onChanged();
      } else {
        drawCountBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ChatRedPacketDrawCount drawCount = 5;</code>
     */
    public Builder setDrawCount(
        int index, xddq.pb.ChatRedPacketDrawCount.Builder builderForValue) {
      if (drawCountBuilder_ == null) {
        ensureDrawCountIsMutable();
        drawCount_.set(index, builderForValue.build());
        onChanged();
      } else {
        drawCountBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ChatRedPacketDrawCount drawCount = 5;</code>
     */
    public Builder addDrawCount(xddq.pb.ChatRedPacketDrawCount value) {
      if (drawCountBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDrawCountIsMutable();
        drawCount_.add(value);
        onChanged();
      } else {
        drawCountBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ChatRedPacketDrawCount drawCount = 5;</code>
     */
    public Builder addDrawCount(
        int index, xddq.pb.ChatRedPacketDrawCount value) {
      if (drawCountBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDrawCountIsMutable();
        drawCount_.add(index, value);
        onChanged();
      } else {
        drawCountBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ChatRedPacketDrawCount drawCount = 5;</code>
     */
    public Builder addDrawCount(
        xddq.pb.ChatRedPacketDrawCount.Builder builderForValue) {
      if (drawCountBuilder_ == null) {
        ensureDrawCountIsMutable();
        drawCount_.add(builderForValue.build());
        onChanged();
      } else {
        drawCountBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ChatRedPacketDrawCount drawCount = 5;</code>
     */
    public Builder addDrawCount(
        int index, xddq.pb.ChatRedPacketDrawCount.Builder builderForValue) {
      if (drawCountBuilder_ == null) {
        ensureDrawCountIsMutable();
        drawCount_.add(index, builderForValue.build());
        onChanged();
      } else {
        drawCountBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ChatRedPacketDrawCount drawCount = 5;</code>
     */
    public Builder addAllDrawCount(
        java.lang.Iterable<? extends xddq.pb.ChatRedPacketDrawCount> values) {
      if (drawCountBuilder_ == null) {
        ensureDrawCountIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, drawCount_);
        onChanged();
      } else {
        drawCountBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ChatRedPacketDrawCount drawCount = 5;</code>
     */
    public Builder clearDrawCount() {
      if (drawCountBuilder_ == null) {
        drawCount_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000010);
        onChanged();
      } else {
        drawCountBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ChatRedPacketDrawCount drawCount = 5;</code>
     */
    public Builder removeDrawCount(int index) {
      if (drawCountBuilder_ == null) {
        ensureDrawCountIsMutable();
        drawCount_.remove(index);
        onChanged();
      } else {
        drawCountBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ChatRedPacketDrawCount drawCount = 5;</code>
     */
    public xddq.pb.ChatRedPacketDrawCount.Builder getDrawCountBuilder(
        int index) {
      return internalGetDrawCountFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.ChatRedPacketDrawCount drawCount = 5;</code>
     */
    public xddq.pb.ChatRedPacketDrawCountOrBuilder getDrawCountOrBuilder(
        int index) {
      if (drawCountBuilder_ == null) {
        return drawCount_.get(index);  } else {
        return drawCountBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.ChatRedPacketDrawCount drawCount = 5;</code>
     */
    public java.util.List<? extends xddq.pb.ChatRedPacketDrawCountOrBuilder> 
         getDrawCountOrBuilderList() {
      if (drawCountBuilder_ != null) {
        return drawCountBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(drawCount_);
      }
    }
    /**
     * <code>repeated .xddq.pb.ChatRedPacketDrawCount drawCount = 5;</code>
     */
    public xddq.pb.ChatRedPacketDrawCount.Builder addDrawCountBuilder() {
      return internalGetDrawCountFieldBuilder().addBuilder(
          xddq.pb.ChatRedPacketDrawCount.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.ChatRedPacketDrawCount drawCount = 5;</code>
     */
    public xddq.pb.ChatRedPacketDrawCount.Builder addDrawCountBuilder(
        int index) {
      return internalGetDrawCountFieldBuilder().addBuilder(
          index, xddq.pb.ChatRedPacketDrawCount.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.ChatRedPacketDrawCount drawCount = 5;</code>
     */
    public java.util.List<xddq.pb.ChatRedPacketDrawCount.Builder> 
         getDrawCountBuilderList() {
      return internalGetDrawCountFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.ChatRedPacketDrawCount, xddq.pb.ChatRedPacketDrawCount.Builder, xddq.pb.ChatRedPacketDrawCountOrBuilder> 
        internalGetDrawCountFieldBuilder() {
      if (drawCountBuilder_ == null) {
        drawCountBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.ChatRedPacketDrawCount, xddq.pb.ChatRedPacketDrawCount.Builder, xddq.pb.ChatRedPacketDrawCountOrBuilder>(
                drawCount_,
                ((bitField0_ & 0x00000010) != 0),
                getParentForChildren(),
                isClean());
        drawCount_ = null;
      }
      return drawCountBuilder_;
    }

    private java.util.List<xddq.pb.ChatRedPacketDrawCount> dailyCount_ =
      java.util.Collections.emptyList();
    private void ensureDailyCountIsMutable() {
      if (!((bitField0_ & 0x00000020) != 0)) {
        dailyCount_ = new java.util.ArrayList<xddq.pb.ChatRedPacketDrawCount>(dailyCount_);
        bitField0_ |= 0x00000020;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.ChatRedPacketDrawCount, xddq.pb.ChatRedPacketDrawCount.Builder, xddq.pb.ChatRedPacketDrawCountOrBuilder> dailyCountBuilder_;

    /**
     * <code>repeated .xddq.pb.ChatRedPacketDrawCount dailyCount = 6;</code>
     */
    public java.util.List<xddq.pb.ChatRedPacketDrawCount> getDailyCountList() {
      if (dailyCountBuilder_ == null) {
        return java.util.Collections.unmodifiableList(dailyCount_);
      } else {
        return dailyCountBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.ChatRedPacketDrawCount dailyCount = 6;</code>
     */
    public int getDailyCountCount() {
      if (dailyCountBuilder_ == null) {
        return dailyCount_.size();
      } else {
        return dailyCountBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.ChatRedPacketDrawCount dailyCount = 6;</code>
     */
    public xddq.pb.ChatRedPacketDrawCount getDailyCount(int index) {
      if (dailyCountBuilder_ == null) {
        return dailyCount_.get(index);
      } else {
        return dailyCountBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.ChatRedPacketDrawCount dailyCount = 6;</code>
     */
    public Builder setDailyCount(
        int index, xddq.pb.ChatRedPacketDrawCount value) {
      if (dailyCountBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDailyCountIsMutable();
        dailyCount_.set(index, value);
        onChanged();
      } else {
        dailyCountBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ChatRedPacketDrawCount dailyCount = 6;</code>
     */
    public Builder setDailyCount(
        int index, xddq.pb.ChatRedPacketDrawCount.Builder builderForValue) {
      if (dailyCountBuilder_ == null) {
        ensureDailyCountIsMutable();
        dailyCount_.set(index, builderForValue.build());
        onChanged();
      } else {
        dailyCountBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ChatRedPacketDrawCount dailyCount = 6;</code>
     */
    public Builder addDailyCount(xddq.pb.ChatRedPacketDrawCount value) {
      if (dailyCountBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDailyCountIsMutable();
        dailyCount_.add(value);
        onChanged();
      } else {
        dailyCountBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ChatRedPacketDrawCount dailyCount = 6;</code>
     */
    public Builder addDailyCount(
        int index, xddq.pb.ChatRedPacketDrawCount value) {
      if (dailyCountBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDailyCountIsMutable();
        dailyCount_.add(index, value);
        onChanged();
      } else {
        dailyCountBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ChatRedPacketDrawCount dailyCount = 6;</code>
     */
    public Builder addDailyCount(
        xddq.pb.ChatRedPacketDrawCount.Builder builderForValue) {
      if (dailyCountBuilder_ == null) {
        ensureDailyCountIsMutable();
        dailyCount_.add(builderForValue.build());
        onChanged();
      } else {
        dailyCountBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ChatRedPacketDrawCount dailyCount = 6;</code>
     */
    public Builder addDailyCount(
        int index, xddq.pb.ChatRedPacketDrawCount.Builder builderForValue) {
      if (dailyCountBuilder_ == null) {
        ensureDailyCountIsMutable();
        dailyCount_.add(index, builderForValue.build());
        onChanged();
      } else {
        dailyCountBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ChatRedPacketDrawCount dailyCount = 6;</code>
     */
    public Builder addAllDailyCount(
        java.lang.Iterable<? extends xddq.pb.ChatRedPacketDrawCount> values) {
      if (dailyCountBuilder_ == null) {
        ensureDailyCountIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, dailyCount_);
        onChanged();
      } else {
        dailyCountBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ChatRedPacketDrawCount dailyCount = 6;</code>
     */
    public Builder clearDailyCount() {
      if (dailyCountBuilder_ == null) {
        dailyCount_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000020);
        onChanged();
      } else {
        dailyCountBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ChatRedPacketDrawCount dailyCount = 6;</code>
     */
    public Builder removeDailyCount(int index) {
      if (dailyCountBuilder_ == null) {
        ensureDailyCountIsMutable();
        dailyCount_.remove(index);
        onChanged();
      } else {
        dailyCountBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ChatRedPacketDrawCount dailyCount = 6;</code>
     */
    public xddq.pb.ChatRedPacketDrawCount.Builder getDailyCountBuilder(
        int index) {
      return internalGetDailyCountFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.ChatRedPacketDrawCount dailyCount = 6;</code>
     */
    public xddq.pb.ChatRedPacketDrawCountOrBuilder getDailyCountOrBuilder(
        int index) {
      if (dailyCountBuilder_ == null) {
        return dailyCount_.get(index);  } else {
        return dailyCountBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.ChatRedPacketDrawCount dailyCount = 6;</code>
     */
    public java.util.List<? extends xddq.pb.ChatRedPacketDrawCountOrBuilder> 
         getDailyCountOrBuilderList() {
      if (dailyCountBuilder_ != null) {
        return dailyCountBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(dailyCount_);
      }
    }
    /**
     * <code>repeated .xddq.pb.ChatRedPacketDrawCount dailyCount = 6;</code>
     */
    public xddq.pb.ChatRedPacketDrawCount.Builder addDailyCountBuilder() {
      return internalGetDailyCountFieldBuilder().addBuilder(
          xddq.pb.ChatRedPacketDrawCount.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.ChatRedPacketDrawCount dailyCount = 6;</code>
     */
    public xddq.pb.ChatRedPacketDrawCount.Builder addDailyCountBuilder(
        int index) {
      return internalGetDailyCountFieldBuilder().addBuilder(
          index, xddq.pb.ChatRedPacketDrawCount.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.ChatRedPacketDrawCount dailyCount = 6;</code>
     */
    public java.util.List<xddq.pb.ChatRedPacketDrawCount.Builder> 
         getDailyCountBuilderList() {
      return internalGetDailyCountFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.ChatRedPacketDrawCount, xddq.pb.ChatRedPacketDrawCount.Builder, xddq.pb.ChatRedPacketDrawCountOrBuilder> 
        internalGetDailyCountFieldBuilder() {
      if (dailyCountBuilder_ == null) {
        dailyCountBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.ChatRedPacketDrawCount, xddq.pb.ChatRedPacketDrawCount.Builder, xddq.pb.ChatRedPacketDrawCountOrBuilder>(
                dailyCount_,
                ((bitField0_ & 0x00000020) != 0),
                getParentForChildren(),
                isClean());
        dailyCount_ = null;
      }
      return dailyCountBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.ChatRedPacketStateSync)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.ChatRedPacketStateSync)
  private static final xddq.pb.ChatRedPacketStateSync DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.ChatRedPacketStateSync();
  }

  public static xddq.pb.ChatRedPacketStateSync getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ChatRedPacketStateSync>
      PARSER = new com.google.protobuf.AbstractParser<ChatRedPacketStateSync>() {
    @java.lang.Override
    public ChatRedPacketStateSync parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<ChatRedPacketStateSync> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ChatRedPacketStateSync> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.ChatRedPacketStateSync getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

