// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.WarSeasonStrategyStatusSync}
 */
public final class WarSeasonStrategyStatusSync extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.WarSeasonStrategyStatusSync)
    WarSeasonStrategyStatusSyncOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      WarSeasonStrategyStatusSync.class.getName());
  }
  // Use WarSeasonStrategyStatusSync.newBuilder() to construct.
  private WarSeasonStrategyStatusSync(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private WarSeasonStrategyStatusSync() {
    params_ = "";
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_WarSeasonStrategyStatusSync_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_WarSeasonStrategyStatusSync_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.WarSeasonStrategyStatusSync.class, xddq.pb.WarSeasonStrategyStatusSync.Builder.class);
  }

  private int bitField0_;
  public static final int UNIONID_FIELD_NUMBER = 1;
  private long unionId_ = 0L;
  /**
   * <code>optional int64 unionId = 1;</code>
   * @return Whether the unionId field is set.
   */
  @java.lang.Override
  public boolean hasUnionId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int64 unionId = 1;</code>
   * @return The unionId.
   */
  @java.lang.Override
  public long getUnionId() {
    return unionId_;
  }

  public static final int STRATEGYSTATUS_FIELD_NUMBER = 2;
  private xddq.pb.WarSeasonStrategyStatus strategyStatus_;
  /**
   * <code>optional .xddq.pb.WarSeasonStrategyStatus strategyStatus = 2;</code>
   * @return Whether the strategyStatus field is set.
   */
  @java.lang.Override
  public boolean hasStrategyStatus() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional .xddq.pb.WarSeasonStrategyStatus strategyStatus = 2;</code>
   * @return The strategyStatus.
   */
  @java.lang.Override
  public xddq.pb.WarSeasonStrategyStatus getStrategyStatus() {
    return strategyStatus_ == null ? xddq.pb.WarSeasonStrategyStatus.getDefaultInstance() : strategyStatus_;
  }
  /**
   * <code>optional .xddq.pb.WarSeasonStrategyStatus strategyStatus = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.WarSeasonStrategyStatusOrBuilder getStrategyStatusOrBuilder() {
    return strategyStatus_ == null ? xddq.pb.WarSeasonStrategyStatus.getDefaultInstance() : strategyStatus_;
  }

  public static final int PARAMS_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private volatile java.lang.Object params_ = "";
  /**
   * <code>optional string params = 3;</code>
   * @return Whether the params field is set.
   */
  @java.lang.Override
  public boolean hasParams() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional string params = 3;</code>
   * @return The params.
   */
  @java.lang.Override
  public java.lang.String getParams() {
    java.lang.Object ref = params_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        params_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string params = 3;</code>
   * @return The bytes for params.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getParamsBytes() {
    java.lang.Object ref = params_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      params_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (hasStrategyStatus()) {
      if (!getStrategyStatus().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt64(1, unionId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeMessage(2, getStrategyStatus());
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 3, params_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(1, unionId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getStrategyStatus());
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(3, params_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.WarSeasonStrategyStatusSync)) {
      return super.equals(obj);
    }
    xddq.pb.WarSeasonStrategyStatusSync other = (xddq.pb.WarSeasonStrategyStatusSync) obj;

    if (hasUnionId() != other.hasUnionId()) return false;
    if (hasUnionId()) {
      if (getUnionId()
          != other.getUnionId()) return false;
    }
    if (hasStrategyStatus() != other.hasStrategyStatus()) return false;
    if (hasStrategyStatus()) {
      if (!getStrategyStatus()
          .equals(other.getStrategyStatus())) return false;
    }
    if (hasParams() != other.hasParams()) return false;
    if (hasParams()) {
      if (!getParams()
          .equals(other.getParams())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasUnionId()) {
      hash = (37 * hash) + UNIONID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getUnionId());
    }
    if (hasStrategyStatus()) {
      hash = (37 * hash) + STRATEGYSTATUS_FIELD_NUMBER;
      hash = (53 * hash) + getStrategyStatus().hashCode();
    }
    if (hasParams()) {
      hash = (37 * hash) + PARAMS_FIELD_NUMBER;
      hash = (53 * hash) + getParams().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.WarSeasonStrategyStatusSync parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.WarSeasonStrategyStatusSync parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.WarSeasonStrategyStatusSync parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.WarSeasonStrategyStatusSync parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.WarSeasonStrategyStatusSync parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.WarSeasonStrategyStatusSync parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.WarSeasonStrategyStatusSync parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.WarSeasonStrategyStatusSync parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.WarSeasonStrategyStatusSync parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.WarSeasonStrategyStatusSync parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.WarSeasonStrategyStatusSync parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.WarSeasonStrategyStatusSync parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.WarSeasonStrategyStatusSync prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.WarSeasonStrategyStatusSync}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.WarSeasonStrategyStatusSync)
      xddq.pb.WarSeasonStrategyStatusSyncOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_WarSeasonStrategyStatusSync_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_WarSeasonStrategyStatusSync_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.WarSeasonStrategyStatusSync.class, xddq.pb.WarSeasonStrategyStatusSync.Builder.class);
    }

    // Construct using xddq.pb.WarSeasonStrategyStatusSync.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetStrategyStatusFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      unionId_ = 0L;
      strategyStatus_ = null;
      if (strategyStatusBuilder_ != null) {
        strategyStatusBuilder_.dispose();
        strategyStatusBuilder_ = null;
      }
      params_ = "";
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_WarSeasonStrategyStatusSync_descriptor;
    }

    @java.lang.Override
    public xddq.pb.WarSeasonStrategyStatusSync getDefaultInstanceForType() {
      return xddq.pb.WarSeasonStrategyStatusSync.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.WarSeasonStrategyStatusSync build() {
      xddq.pb.WarSeasonStrategyStatusSync result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.WarSeasonStrategyStatusSync buildPartial() {
      xddq.pb.WarSeasonStrategyStatusSync result = new xddq.pb.WarSeasonStrategyStatusSync(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.WarSeasonStrategyStatusSync result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.unionId_ = unionId_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.strategyStatus_ = strategyStatusBuilder_ == null
            ? strategyStatus_
            : strategyStatusBuilder_.build();
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.params_ = params_;
        to_bitField0_ |= 0x00000004;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.WarSeasonStrategyStatusSync) {
        return mergeFrom((xddq.pb.WarSeasonStrategyStatusSync)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.WarSeasonStrategyStatusSync other) {
      if (other == xddq.pb.WarSeasonStrategyStatusSync.getDefaultInstance()) return this;
      if (other.hasUnionId()) {
        setUnionId(other.getUnionId());
      }
      if (other.hasStrategyStatus()) {
        mergeStrategyStatus(other.getStrategyStatus());
      }
      if (other.hasParams()) {
        params_ = other.params_;
        bitField0_ |= 0x00000004;
        onChanged();
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (hasStrategyStatus()) {
        if (!getStrategyStatus().isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              unionId_ = input.readInt64();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              input.readMessage(
                  internalGetStrategyStatusFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 26: {
              params_ = input.readBytes();
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private long unionId_ ;
    /**
     * <code>optional int64 unionId = 1;</code>
     * @return Whether the unionId field is set.
     */
    @java.lang.Override
    public boolean hasUnionId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 unionId = 1;</code>
     * @return The unionId.
     */
    @java.lang.Override
    public long getUnionId() {
      return unionId_;
    }
    /**
     * <code>optional int64 unionId = 1;</code>
     * @param value The unionId to set.
     * @return This builder for chaining.
     */
    public Builder setUnionId(long value) {

      unionId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 unionId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearUnionId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      unionId_ = 0L;
      onChanged();
      return this;
    }

    private xddq.pb.WarSeasonStrategyStatus strategyStatus_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.WarSeasonStrategyStatus, xddq.pb.WarSeasonStrategyStatus.Builder, xddq.pb.WarSeasonStrategyStatusOrBuilder> strategyStatusBuilder_;
    /**
     * <code>optional .xddq.pb.WarSeasonStrategyStatus strategyStatus = 2;</code>
     * @return Whether the strategyStatus field is set.
     */
    public boolean hasStrategyStatus() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .xddq.pb.WarSeasonStrategyStatus strategyStatus = 2;</code>
     * @return The strategyStatus.
     */
    public xddq.pb.WarSeasonStrategyStatus getStrategyStatus() {
      if (strategyStatusBuilder_ == null) {
        return strategyStatus_ == null ? xddq.pb.WarSeasonStrategyStatus.getDefaultInstance() : strategyStatus_;
      } else {
        return strategyStatusBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.WarSeasonStrategyStatus strategyStatus = 2;</code>
     */
    public Builder setStrategyStatus(xddq.pb.WarSeasonStrategyStatus value) {
      if (strategyStatusBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        strategyStatus_ = value;
      } else {
        strategyStatusBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.WarSeasonStrategyStatus strategyStatus = 2;</code>
     */
    public Builder setStrategyStatus(
        xddq.pb.WarSeasonStrategyStatus.Builder builderForValue) {
      if (strategyStatusBuilder_ == null) {
        strategyStatus_ = builderForValue.build();
      } else {
        strategyStatusBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.WarSeasonStrategyStatus strategyStatus = 2;</code>
     */
    public Builder mergeStrategyStatus(xddq.pb.WarSeasonStrategyStatus value) {
      if (strategyStatusBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0) &&
          strategyStatus_ != null &&
          strategyStatus_ != xddq.pb.WarSeasonStrategyStatus.getDefaultInstance()) {
          getStrategyStatusBuilder().mergeFrom(value);
        } else {
          strategyStatus_ = value;
        }
      } else {
        strategyStatusBuilder_.mergeFrom(value);
      }
      if (strategyStatus_ != null) {
        bitField0_ |= 0x00000002;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.WarSeasonStrategyStatus strategyStatus = 2;</code>
     */
    public Builder clearStrategyStatus() {
      bitField0_ = (bitField0_ & ~0x00000002);
      strategyStatus_ = null;
      if (strategyStatusBuilder_ != null) {
        strategyStatusBuilder_.dispose();
        strategyStatusBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.WarSeasonStrategyStatus strategyStatus = 2;</code>
     */
    public xddq.pb.WarSeasonStrategyStatus.Builder getStrategyStatusBuilder() {
      bitField0_ |= 0x00000002;
      onChanged();
      return internalGetStrategyStatusFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.WarSeasonStrategyStatus strategyStatus = 2;</code>
     */
    public xddq.pb.WarSeasonStrategyStatusOrBuilder getStrategyStatusOrBuilder() {
      if (strategyStatusBuilder_ != null) {
        return strategyStatusBuilder_.getMessageOrBuilder();
      } else {
        return strategyStatus_ == null ?
            xddq.pb.WarSeasonStrategyStatus.getDefaultInstance() : strategyStatus_;
      }
    }
    /**
     * <code>optional .xddq.pb.WarSeasonStrategyStatus strategyStatus = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.WarSeasonStrategyStatus, xddq.pb.WarSeasonStrategyStatus.Builder, xddq.pb.WarSeasonStrategyStatusOrBuilder> 
        internalGetStrategyStatusFieldBuilder() {
      if (strategyStatusBuilder_ == null) {
        strategyStatusBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.WarSeasonStrategyStatus, xddq.pb.WarSeasonStrategyStatus.Builder, xddq.pb.WarSeasonStrategyStatusOrBuilder>(
                getStrategyStatus(),
                getParentForChildren(),
                isClean());
        strategyStatus_ = null;
      }
      return strategyStatusBuilder_;
    }

    private java.lang.Object params_ = "";
    /**
     * <code>optional string params = 3;</code>
     * @return Whether the params field is set.
     */
    public boolean hasParams() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional string params = 3;</code>
     * @return The params.
     */
    public java.lang.String getParams() {
      java.lang.Object ref = params_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          params_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string params = 3;</code>
     * @return The bytes for params.
     */
    public com.google.protobuf.ByteString
        getParamsBytes() {
      java.lang.Object ref = params_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        params_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string params = 3;</code>
     * @param value The params to set.
     * @return This builder for chaining.
     */
    public Builder setParams(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      params_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional string params = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearParams() {
      params_ = getDefaultInstance().getParams();
      bitField0_ = (bitField0_ & ~0x00000004);
      onChanged();
      return this;
    }
    /**
     * <code>optional string params = 3;</code>
     * @param value The bytes for params to set.
     * @return This builder for chaining.
     */
    public Builder setParamsBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      params_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.WarSeasonStrategyStatusSync)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.WarSeasonStrategyStatusSync)
  private static final xddq.pb.WarSeasonStrategyStatusSync DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.WarSeasonStrategyStatusSync();
  }

  public static xddq.pb.WarSeasonStrategyStatusSync getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<WarSeasonStrategyStatusSync>
      PARSER = new com.google.protobuf.AbstractParser<WarSeasonStrategyStatusSync>() {
    @java.lang.Override
    public WarSeasonStrategyStatusSync parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<WarSeasonStrategyStatusSync> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<WarSeasonStrategyStatusSync> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.WarSeasonStrategyStatusSync getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

