// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.ManHuangLogPersonItem}
 */
public final class ManHuangLogPersonItem extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.ManHuangLogPersonItem)
    ManHuangLogPersonItemOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      ManHuangLogPersonItem.class.getName());
  }
  // Use ManHuangLogPersonItem.newBuilder() to construct.
  private ManHuangLogPersonItem(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private ManHuangLogPersonItem() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_ManHuangLogPersonItem_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_ManHuangLogPersonItem_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.ManHuangLogPersonItem.class, xddq.pb.ManHuangLogPersonItem.Builder.class);
  }

  private int bitField0_;
  public static final int TYPE_FIELD_NUMBER = 1;
  private int type_ = 0;
  /**
   * <code>required int32 type = 1;</code>
   * @return Whether the type field is set.
   */
  @java.lang.Override
  public boolean hasType() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 type = 1;</code>
   * @return The type.
   */
  @java.lang.Override
  public int getType() {
    return type_;
  }

  public static final int BATTLEGROUNDITEM_FIELD_NUMBER = 2;
  private xddq.pb.ManHuangLogBattlegroundItem battlegroundItem_;
  /**
   * <code>optional .xddq.pb.ManHuangLogBattlegroundItem BattlegroundItem = 2;</code>
   * @return Whether the battlegroundItem field is set.
   */
  @java.lang.Override
  public boolean hasBattlegroundItem() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional .xddq.pb.ManHuangLogBattlegroundItem BattlegroundItem = 2;</code>
   * @return The battlegroundItem.
   */
  @java.lang.Override
  public xddq.pb.ManHuangLogBattlegroundItem getBattlegroundItem() {
    return battlegroundItem_ == null ? xddq.pb.ManHuangLogBattlegroundItem.getDefaultInstance() : battlegroundItem_;
  }
  /**
   * <code>optional .xddq.pb.ManHuangLogBattlegroundItem BattlegroundItem = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.ManHuangLogBattlegroundItemOrBuilder getBattlegroundItemOrBuilder() {
    return battlegroundItem_ == null ? xddq.pb.ManHuangLogBattlegroundItem.getDefaultInstance() : battlegroundItem_;
  }

  public static final int DEFENDITEM_FIELD_NUMBER = 3;
  private xddq.pb.ManHuangLogDefendItem defendItem_;
  /**
   * <code>optional .xddq.pb.ManHuangLogDefendItem defendItem = 3;</code>
   * @return Whether the defendItem field is set.
   */
  @java.lang.Override
  public boolean hasDefendItem() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional .xddq.pb.ManHuangLogDefendItem defendItem = 3;</code>
   * @return The defendItem.
   */
  @java.lang.Override
  public xddq.pb.ManHuangLogDefendItem getDefendItem() {
    return defendItem_ == null ? xddq.pb.ManHuangLogDefendItem.getDefaultInstance() : defendItem_;
  }
  /**
   * <code>optional .xddq.pb.ManHuangLogDefendItem defendItem = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.ManHuangLogDefendItemOrBuilder getDefendItemOrBuilder() {
    return defendItem_ == null ? xddq.pb.ManHuangLogDefendItem.getDefaultInstance() : defendItem_;
  }

  public static final int CREATETIME_FIELD_NUMBER = 4;
  private long createTime_ = 0L;
  /**
   * <code>optional int64 createTime = 4;</code>
   * @return Whether the createTime field is set.
   */
  @java.lang.Override
  public boolean hasCreateTime() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional int64 createTime = 4;</code>
   * @return The createTime.
   */
  @java.lang.Override
  public long getCreateTime() {
    return createTime_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasType()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (hasBattlegroundItem()) {
      if (!getBattlegroundItem().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    if (hasDefendItem()) {
      if (!getDefendItem().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, type_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeMessage(2, getBattlegroundItem());
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeMessage(3, getDefendItem());
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt64(4, createTime_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, type_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getBattlegroundItem());
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, getDefendItem());
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(4, createTime_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.ManHuangLogPersonItem)) {
      return super.equals(obj);
    }
    xddq.pb.ManHuangLogPersonItem other = (xddq.pb.ManHuangLogPersonItem) obj;

    if (hasType() != other.hasType()) return false;
    if (hasType()) {
      if (getType()
          != other.getType()) return false;
    }
    if (hasBattlegroundItem() != other.hasBattlegroundItem()) return false;
    if (hasBattlegroundItem()) {
      if (!getBattlegroundItem()
          .equals(other.getBattlegroundItem())) return false;
    }
    if (hasDefendItem() != other.hasDefendItem()) return false;
    if (hasDefendItem()) {
      if (!getDefendItem()
          .equals(other.getDefendItem())) return false;
    }
    if (hasCreateTime() != other.hasCreateTime()) return false;
    if (hasCreateTime()) {
      if (getCreateTime()
          != other.getCreateTime()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasType()) {
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
    }
    if (hasBattlegroundItem()) {
      hash = (37 * hash) + BATTLEGROUNDITEM_FIELD_NUMBER;
      hash = (53 * hash) + getBattlegroundItem().hashCode();
    }
    if (hasDefendItem()) {
      hash = (37 * hash) + DEFENDITEM_FIELD_NUMBER;
      hash = (53 * hash) + getDefendItem().hashCode();
    }
    if (hasCreateTime()) {
      hash = (37 * hash) + CREATETIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getCreateTime());
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.ManHuangLogPersonItem parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ManHuangLogPersonItem parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ManHuangLogPersonItem parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ManHuangLogPersonItem parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ManHuangLogPersonItem parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ManHuangLogPersonItem parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ManHuangLogPersonItem parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.ManHuangLogPersonItem parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.ManHuangLogPersonItem parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.ManHuangLogPersonItem parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.ManHuangLogPersonItem parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.ManHuangLogPersonItem parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.ManHuangLogPersonItem prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.ManHuangLogPersonItem}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.ManHuangLogPersonItem)
      xddq.pb.ManHuangLogPersonItemOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ManHuangLogPersonItem_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ManHuangLogPersonItem_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.ManHuangLogPersonItem.class, xddq.pb.ManHuangLogPersonItem.Builder.class);
    }

    // Construct using xddq.pb.ManHuangLogPersonItem.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetBattlegroundItemFieldBuilder();
        internalGetDefendItemFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      type_ = 0;
      battlegroundItem_ = null;
      if (battlegroundItemBuilder_ != null) {
        battlegroundItemBuilder_.dispose();
        battlegroundItemBuilder_ = null;
      }
      defendItem_ = null;
      if (defendItemBuilder_ != null) {
        defendItemBuilder_.dispose();
        defendItemBuilder_ = null;
      }
      createTime_ = 0L;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ManHuangLogPersonItem_descriptor;
    }

    @java.lang.Override
    public xddq.pb.ManHuangLogPersonItem getDefaultInstanceForType() {
      return xddq.pb.ManHuangLogPersonItem.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.ManHuangLogPersonItem build() {
      xddq.pb.ManHuangLogPersonItem result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.ManHuangLogPersonItem buildPartial() {
      xddq.pb.ManHuangLogPersonItem result = new xddq.pb.ManHuangLogPersonItem(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.ManHuangLogPersonItem result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.type_ = type_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.battlegroundItem_ = battlegroundItemBuilder_ == null
            ? battlegroundItem_
            : battlegroundItemBuilder_.build();
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.defendItem_ = defendItemBuilder_ == null
            ? defendItem_
            : defendItemBuilder_.build();
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.createTime_ = createTime_;
        to_bitField0_ |= 0x00000008;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.ManHuangLogPersonItem) {
        return mergeFrom((xddq.pb.ManHuangLogPersonItem)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.ManHuangLogPersonItem other) {
      if (other == xddq.pb.ManHuangLogPersonItem.getDefaultInstance()) return this;
      if (other.hasType()) {
        setType(other.getType());
      }
      if (other.hasBattlegroundItem()) {
        mergeBattlegroundItem(other.getBattlegroundItem());
      }
      if (other.hasDefendItem()) {
        mergeDefendItem(other.getDefendItem());
      }
      if (other.hasCreateTime()) {
        setCreateTime(other.getCreateTime());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasType()) {
        return false;
      }
      if (hasBattlegroundItem()) {
        if (!getBattlegroundItem().isInitialized()) {
          return false;
        }
      }
      if (hasDefendItem()) {
        if (!getDefendItem().isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              type_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              input.readMessage(
                  internalGetBattlegroundItemFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 26: {
              input.readMessage(
                  internalGetDefendItemFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 32: {
              createTime_ = input.readInt64();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int type_ ;
    /**
     * <code>required int32 type = 1;</code>
     * @return Whether the type field is set.
     */
    @java.lang.Override
    public boolean hasType() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 type = 1;</code>
     * @return The type.
     */
    @java.lang.Override
    public int getType() {
      return type_;
    }
    /**
     * <code>required int32 type = 1;</code>
     * @param value The type to set.
     * @return This builder for chaining.
     */
    public Builder setType(int value) {

      type_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 type = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearType() {
      bitField0_ = (bitField0_ & ~0x00000001);
      type_ = 0;
      onChanged();
      return this;
    }

    private xddq.pb.ManHuangLogBattlegroundItem battlegroundItem_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.ManHuangLogBattlegroundItem, xddq.pb.ManHuangLogBattlegroundItem.Builder, xddq.pb.ManHuangLogBattlegroundItemOrBuilder> battlegroundItemBuilder_;
    /**
     * <code>optional .xddq.pb.ManHuangLogBattlegroundItem BattlegroundItem = 2;</code>
     * @return Whether the battlegroundItem field is set.
     */
    public boolean hasBattlegroundItem() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .xddq.pb.ManHuangLogBattlegroundItem BattlegroundItem = 2;</code>
     * @return The battlegroundItem.
     */
    public xddq.pb.ManHuangLogBattlegroundItem getBattlegroundItem() {
      if (battlegroundItemBuilder_ == null) {
        return battlegroundItem_ == null ? xddq.pb.ManHuangLogBattlegroundItem.getDefaultInstance() : battlegroundItem_;
      } else {
        return battlegroundItemBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.ManHuangLogBattlegroundItem BattlegroundItem = 2;</code>
     */
    public Builder setBattlegroundItem(xddq.pb.ManHuangLogBattlegroundItem value) {
      if (battlegroundItemBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        battlegroundItem_ = value;
      } else {
        battlegroundItemBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ManHuangLogBattlegroundItem BattlegroundItem = 2;</code>
     */
    public Builder setBattlegroundItem(
        xddq.pb.ManHuangLogBattlegroundItem.Builder builderForValue) {
      if (battlegroundItemBuilder_ == null) {
        battlegroundItem_ = builderForValue.build();
      } else {
        battlegroundItemBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ManHuangLogBattlegroundItem BattlegroundItem = 2;</code>
     */
    public Builder mergeBattlegroundItem(xddq.pb.ManHuangLogBattlegroundItem value) {
      if (battlegroundItemBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0) &&
          battlegroundItem_ != null &&
          battlegroundItem_ != xddq.pb.ManHuangLogBattlegroundItem.getDefaultInstance()) {
          getBattlegroundItemBuilder().mergeFrom(value);
        } else {
          battlegroundItem_ = value;
        }
      } else {
        battlegroundItemBuilder_.mergeFrom(value);
      }
      if (battlegroundItem_ != null) {
        bitField0_ |= 0x00000002;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.ManHuangLogBattlegroundItem BattlegroundItem = 2;</code>
     */
    public Builder clearBattlegroundItem() {
      bitField0_ = (bitField0_ & ~0x00000002);
      battlegroundItem_ = null;
      if (battlegroundItemBuilder_ != null) {
        battlegroundItemBuilder_.dispose();
        battlegroundItemBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ManHuangLogBattlegroundItem BattlegroundItem = 2;</code>
     */
    public xddq.pb.ManHuangLogBattlegroundItem.Builder getBattlegroundItemBuilder() {
      bitField0_ |= 0x00000002;
      onChanged();
      return internalGetBattlegroundItemFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.ManHuangLogBattlegroundItem BattlegroundItem = 2;</code>
     */
    public xddq.pb.ManHuangLogBattlegroundItemOrBuilder getBattlegroundItemOrBuilder() {
      if (battlegroundItemBuilder_ != null) {
        return battlegroundItemBuilder_.getMessageOrBuilder();
      } else {
        return battlegroundItem_ == null ?
            xddq.pb.ManHuangLogBattlegroundItem.getDefaultInstance() : battlegroundItem_;
      }
    }
    /**
     * <code>optional .xddq.pb.ManHuangLogBattlegroundItem BattlegroundItem = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.ManHuangLogBattlegroundItem, xddq.pb.ManHuangLogBattlegroundItem.Builder, xddq.pb.ManHuangLogBattlegroundItemOrBuilder> 
        internalGetBattlegroundItemFieldBuilder() {
      if (battlegroundItemBuilder_ == null) {
        battlegroundItemBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.ManHuangLogBattlegroundItem, xddq.pb.ManHuangLogBattlegroundItem.Builder, xddq.pb.ManHuangLogBattlegroundItemOrBuilder>(
                getBattlegroundItem(),
                getParentForChildren(),
                isClean());
        battlegroundItem_ = null;
      }
      return battlegroundItemBuilder_;
    }

    private xddq.pb.ManHuangLogDefendItem defendItem_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.ManHuangLogDefendItem, xddq.pb.ManHuangLogDefendItem.Builder, xddq.pb.ManHuangLogDefendItemOrBuilder> defendItemBuilder_;
    /**
     * <code>optional .xddq.pb.ManHuangLogDefendItem defendItem = 3;</code>
     * @return Whether the defendItem field is set.
     */
    public boolean hasDefendItem() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional .xddq.pb.ManHuangLogDefendItem defendItem = 3;</code>
     * @return The defendItem.
     */
    public xddq.pb.ManHuangLogDefendItem getDefendItem() {
      if (defendItemBuilder_ == null) {
        return defendItem_ == null ? xddq.pb.ManHuangLogDefendItem.getDefaultInstance() : defendItem_;
      } else {
        return defendItemBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.ManHuangLogDefendItem defendItem = 3;</code>
     */
    public Builder setDefendItem(xddq.pb.ManHuangLogDefendItem value) {
      if (defendItemBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        defendItem_ = value;
      } else {
        defendItemBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ManHuangLogDefendItem defendItem = 3;</code>
     */
    public Builder setDefendItem(
        xddq.pb.ManHuangLogDefendItem.Builder builderForValue) {
      if (defendItemBuilder_ == null) {
        defendItem_ = builderForValue.build();
      } else {
        defendItemBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ManHuangLogDefendItem defendItem = 3;</code>
     */
    public Builder mergeDefendItem(xddq.pb.ManHuangLogDefendItem value) {
      if (defendItemBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0) &&
          defendItem_ != null &&
          defendItem_ != xddq.pb.ManHuangLogDefendItem.getDefaultInstance()) {
          getDefendItemBuilder().mergeFrom(value);
        } else {
          defendItem_ = value;
        }
      } else {
        defendItemBuilder_.mergeFrom(value);
      }
      if (defendItem_ != null) {
        bitField0_ |= 0x00000004;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.ManHuangLogDefendItem defendItem = 3;</code>
     */
    public Builder clearDefendItem() {
      bitField0_ = (bitField0_ & ~0x00000004);
      defendItem_ = null;
      if (defendItemBuilder_ != null) {
        defendItemBuilder_.dispose();
        defendItemBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.ManHuangLogDefendItem defendItem = 3;</code>
     */
    public xddq.pb.ManHuangLogDefendItem.Builder getDefendItemBuilder() {
      bitField0_ |= 0x00000004;
      onChanged();
      return internalGetDefendItemFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.ManHuangLogDefendItem defendItem = 3;</code>
     */
    public xddq.pb.ManHuangLogDefendItemOrBuilder getDefendItemOrBuilder() {
      if (defendItemBuilder_ != null) {
        return defendItemBuilder_.getMessageOrBuilder();
      } else {
        return defendItem_ == null ?
            xddq.pb.ManHuangLogDefendItem.getDefaultInstance() : defendItem_;
      }
    }
    /**
     * <code>optional .xddq.pb.ManHuangLogDefendItem defendItem = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.ManHuangLogDefendItem, xddq.pb.ManHuangLogDefendItem.Builder, xddq.pb.ManHuangLogDefendItemOrBuilder> 
        internalGetDefendItemFieldBuilder() {
      if (defendItemBuilder_ == null) {
        defendItemBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.ManHuangLogDefendItem, xddq.pb.ManHuangLogDefendItem.Builder, xddq.pb.ManHuangLogDefendItemOrBuilder>(
                getDefendItem(),
                getParentForChildren(),
                isClean());
        defendItem_ = null;
      }
      return defendItemBuilder_;
    }

    private long createTime_ ;
    /**
     * <code>optional int64 createTime = 4;</code>
     * @return Whether the createTime field is set.
     */
    @java.lang.Override
    public boolean hasCreateTime() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int64 createTime = 4;</code>
     * @return The createTime.
     */
    @java.lang.Override
    public long getCreateTime() {
      return createTime_;
    }
    /**
     * <code>optional int64 createTime = 4;</code>
     * @param value The createTime to set.
     * @return This builder for chaining.
     */
    public Builder setCreateTime(long value) {

      createTime_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 createTime = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearCreateTime() {
      bitField0_ = (bitField0_ & ~0x00000008);
      createTime_ = 0L;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.ManHuangLogPersonItem)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.ManHuangLogPersonItem)
  private static final xddq.pb.ManHuangLogPersonItem DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.ManHuangLogPersonItem();
  }

  public static xddq.pb.ManHuangLogPersonItem getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ManHuangLogPersonItem>
      PARSER = new com.google.protobuf.AbstractParser<ManHuangLogPersonItem>() {
    @java.lang.Override
    public ManHuangLogPersonItem parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<ManHuangLogPersonItem> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ManHuangLogPersonItem> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.ManHuangLogPersonItem getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

