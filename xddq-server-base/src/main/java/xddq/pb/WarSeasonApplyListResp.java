// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.WarSeasonApplyListResp}
 */
public final class WarSeasonApplyListResp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.WarSeasonApplyListResp)
    WarSeasonApplyListRespOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      WarSeasonApplyListResp.class.getName());
  }
  // Use WarSeasonApplyListResp.newBuilder() to construct.
  private WarSeasonApplyListResp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private WarSeasonApplyListResp() {
    applyList_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_WarSeasonApplyListResp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_WarSeasonApplyListResp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.WarSeasonApplyListResp.class, xddq.pb.WarSeasonApplyListResp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int APPLYLIST_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.WarSeasonApplyInfo> applyList_;
  /**
   * <code>repeated .xddq.pb.WarSeasonApplyInfo applyList = 2;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.WarSeasonApplyInfo> getApplyListList() {
    return applyList_;
  }
  /**
   * <code>repeated .xddq.pb.WarSeasonApplyInfo applyList = 2;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.WarSeasonApplyInfoOrBuilder> 
      getApplyListOrBuilderList() {
    return applyList_;
  }
  /**
   * <code>repeated .xddq.pb.WarSeasonApplyInfo applyList = 2;</code>
   */
  @java.lang.Override
  public int getApplyListCount() {
    return applyList_.size();
  }
  /**
   * <code>repeated .xddq.pb.WarSeasonApplyInfo applyList = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.WarSeasonApplyInfo getApplyList(int index) {
    return applyList_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.WarSeasonApplyInfo applyList = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.WarSeasonApplyInfoOrBuilder getApplyListOrBuilder(
      int index) {
    return applyList_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    for (int i = 0; i < applyList_.size(); i++) {
      output.writeMessage(2, applyList_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    for (int i = 0; i < applyList_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, applyList_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.WarSeasonApplyListResp)) {
      return super.equals(obj);
    }
    xddq.pb.WarSeasonApplyListResp other = (xddq.pb.WarSeasonApplyListResp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (!getApplyListList()
        .equals(other.getApplyListList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (getApplyListCount() > 0) {
      hash = (37 * hash) + APPLYLIST_FIELD_NUMBER;
      hash = (53 * hash) + getApplyListList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.WarSeasonApplyListResp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.WarSeasonApplyListResp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.WarSeasonApplyListResp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.WarSeasonApplyListResp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.WarSeasonApplyListResp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.WarSeasonApplyListResp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.WarSeasonApplyListResp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.WarSeasonApplyListResp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.WarSeasonApplyListResp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.WarSeasonApplyListResp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.WarSeasonApplyListResp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.WarSeasonApplyListResp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.WarSeasonApplyListResp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.WarSeasonApplyListResp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.WarSeasonApplyListResp)
      xddq.pb.WarSeasonApplyListRespOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_WarSeasonApplyListResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_WarSeasonApplyListResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.WarSeasonApplyListResp.class, xddq.pb.WarSeasonApplyListResp.Builder.class);
    }

    // Construct using xddq.pb.WarSeasonApplyListResp.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      if (applyListBuilder_ == null) {
        applyList_ = java.util.Collections.emptyList();
      } else {
        applyList_ = null;
        applyListBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000002);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_WarSeasonApplyListResp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.WarSeasonApplyListResp getDefaultInstanceForType() {
      return xddq.pb.WarSeasonApplyListResp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.WarSeasonApplyListResp build() {
      xddq.pb.WarSeasonApplyListResp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.WarSeasonApplyListResp buildPartial() {
      xddq.pb.WarSeasonApplyListResp result = new xddq.pb.WarSeasonApplyListResp(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.WarSeasonApplyListResp result) {
      if (applyListBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          applyList_ = java.util.Collections.unmodifiableList(applyList_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.applyList_ = applyList_;
      } else {
        result.applyList_ = applyListBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.WarSeasonApplyListResp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.WarSeasonApplyListResp) {
        return mergeFrom((xddq.pb.WarSeasonApplyListResp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.WarSeasonApplyListResp other) {
      if (other == xddq.pb.WarSeasonApplyListResp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (applyListBuilder_ == null) {
        if (!other.applyList_.isEmpty()) {
          if (applyList_.isEmpty()) {
            applyList_ = other.applyList_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureApplyListIsMutable();
            applyList_.addAll(other.applyList_);
          }
          onChanged();
        }
      } else {
        if (!other.applyList_.isEmpty()) {
          if (applyListBuilder_.isEmpty()) {
            applyListBuilder_.dispose();
            applyListBuilder_ = null;
            applyList_ = other.applyList_;
            bitField0_ = (bitField0_ & ~0x00000002);
            applyListBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetApplyListFieldBuilder() : null;
          } else {
            applyListBuilder_.addAllMessages(other.applyList_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              xddq.pb.WarSeasonApplyInfo m =
                  input.readMessage(
                      xddq.pb.WarSeasonApplyInfo.parser(),
                      extensionRegistry);
              if (applyListBuilder_ == null) {
                ensureApplyListIsMutable();
                applyList_.add(m);
              } else {
                applyListBuilder_.addMessage(m);
              }
              break;
            } // case 18
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.WarSeasonApplyInfo> applyList_ =
      java.util.Collections.emptyList();
    private void ensureApplyListIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        applyList_ = new java.util.ArrayList<xddq.pb.WarSeasonApplyInfo>(applyList_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.WarSeasonApplyInfo, xddq.pb.WarSeasonApplyInfo.Builder, xddq.pb.WarSeasonApplyInfoOrBuilder> applyListBuilder_;

    /**
     * <code>repeated .xddq.pb.WarSeasonApplyInfo applyList = 2;</code>
     */
    public java.util.List<xddq.pb.WarSeasonApplyInfo> getApplyListList() {
      if (applyListBuilder_ == null) {
        return java.util.Collections.unmodifiableList(applyList_);
      } else {
        return applyListBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonApplyInfo applyList = 2;</code>
     */
    public int getApplyListCount() {
      if (applyListBuilder_ == null) {
        return applyList_.size();
      } else {
        return applyListBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonApplyInfo applyList = 2;</code>
     */
    public xddq.pb.WarSeasonApplyInfo getApplyList(int index) {
      if (applyListBuilder_ == null) {
        return applyList_.get(index);
      } else {
        return applyListBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonApplyInfo applyList = 2;</code>
     */
    public Builder setApplyList(
        int index, xddq.pb.WarSeasonApplyInfo value) {
      if (applyListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureApplyListIsMutable();
        applyList_.set(index, value);
        onChanged();
      } else {
        applyListBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonApplyInfo applyList = 2;</code>
     */
    public Builder setApplyList(
        int index, xddq.pb.WarSeasonApplyInfo.Builder builderForValue) {
      if (applyListBuilder_ == null) {
        ensureApplyListIsMutable();
        applyList_.set(index, builderForValue.build());
        onChanged();
      } else {
        applyListBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonApplyInfo applyList = 2;</code>
     */
    public Builder addApplyList(xddq.pb.WarSeasonApplyInfo value) {
      if (applyListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureApplyListIsMutable();
        applyList_.add(value);
        onChanged();
      } else {
        applyListBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonApplyInfo applyList = 2;</code>
     */
    public Builder addApplyList(
        int index, xddq.pb.WarSeasonApplyInfo value) {
      if (applyListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureApplyListIsMutable();
        applyList_.add(index, value);
        onChanged();
      } else {
        applyListBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonApplyInfo applyList = 2;</code>
     */
    public Builder addApplyList(
        xddq.pb.WarSeasonApplyInfo.Builder builderForValue) {
      if (applyListBuilder_ == null) {
        ensureApplyListIsMutable();
        applyList_.add(builderForValue.build());
        onChanged();
      } else {
        applyListBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonApplyInfo applyList = 2;</code>
     */
    public Builder addApplyList(
        int index, xddq.pb.WarSeasonApplyInfo.Builder builderForValue) {
      if (applyListBuilder_ == null) {
        ensureApplyListIsMutable();
        applyList_.add(index, builderForValue.build());
        onChanged();
      } else {
        applyListBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonApplyInfo applyList = 2;</code>
     */
    public Builder addAllApplyList(
        java.lang.Iterable<? extends xddq.pb.WarSeasonApplyInfo> values) {
      if (applyListBuilder_ == null) {
        ensureApplyListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, applyList_);
        onChanged();
      } else {
        applyListBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonApplyInfo applyList = 2;</code>
     */
    public Builder clearApplyList() {
      if (applyListBuilder_ == null) {
        applyList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        applyListBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonApplyInfo applyList = 2;</code>
     */
    public Builder removeApplyList(int index) {
      if (applyListBuilder_ == null) {
        ensureApplyListIsMutable();
        applyList_.remove(index);
        onChanged();
      } else {
        applyListBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonApplyInfo applyList = 2;</code>
     */
    public xddq.pb.WarSeasonApplyInfo.Builder getApplyListBuilder(
        int index) {
      return internalGetApplyListFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonApplyInfo applyList = 2;</code>
     */
    public xddq.pb.WarSeasonApplyInfoOrBuilder getApplyListOrBuilder(
        int index) {
      if (applyListBuilder_ == null) {
        return applyList_.get(index);  } else {
        return applyListBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonApplyInfo applyList = 2;</code>
     */
    public java.util.List<? extends xddq.pb.WarSeasonApplyInfoOrBuilder> 
         getApplyListOrBuilderList() {
      if (applyListBuilder_ != null) {
        return applyListBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(applyList_);
      }
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonApplyInfo applyList = 2;</code>
     */
    public xddq.pb.WarSeasonApplyInfo.Builder addApplyListBuilder() {
      return internalGetApplyListFieldBuilder().addBuilder(
          xddq.pb.WarSeasonApplyInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonApplyInfo applyList = 2;</code>
     */
    public xddq.pb.WarSeasonApplyInfo.Builder addApplyListBuilder(
        int index) {
      return internalGetApplyListFieldBuilder().addBuilder(
          index, xddq.pb.WarSeasonApplyInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.WarSeasonApplyInfo applyList = 2;</code>
     */
    public java.util.List<xddq.pb.WarSeasonApplyInfo.Builder> 
         getApplyListBuilderList() {
      return internalGetApplyListFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.WarSeasonApplyInfo, xddq.pb.WarSeasonApplyInfo.Builder, xddq.pb.WarSeasonApplyInfoOrBuilder> 
        internalGetApplyListFieldBuilder() {
      if (applyListBuilder_ == null) {
        applyListBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.WarSeasonApplyInfo, xddq.pb.WarSeasonApplyInfo.Builder, xddq.pb.WarSeasonApplyInfoOrBuilder>(
                applyList_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        applyList_ = null;
      }
      return applyListBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.WarSeasonApplyListResp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.WarSeasonApplyListResp)
  private static final xddq.pb.WarSeasonApplyListResp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.WarSeasonApplyListResp();
  }

  public static xddq.pb.WarSeasonApplyListResp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<WarSeasonApplyListResp>
      PARSER = new com.google.protobuf.AbstractParser<WarSeasonApplyListResp>() {
    @java.lang.Override
    public WarSeasonApplyListResp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<WarSeasonApplyListResp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<WarSeasonApplyListResp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.WarSeasonApplyListResp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

