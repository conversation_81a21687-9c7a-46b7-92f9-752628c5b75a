// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.ElementalBondsSeasonMemoryMsg}
 */
public final class ElementalBondsSeasonMemoryMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.ElementalBondsSeasonMemoryMsg)
    ElementalBondsSeasonMemoryMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      ElementalBondsSeasonMemoryMsg.class.getName());
  }
  // Use ElementalBondsSeasonMemoryMsg.newBuilder() to construct.
  private ElementalBondsSeasonMemoryMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private ElementalBondsSeasonMemoryMsg() {
    honorMoment_ = java.util.Collections.emptyList();
    topUnlockCardId_ = emptyIntList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_ElementalBondsSeasonMemoryMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_ElementalBondsSeasonMemoryMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.ElementalBondsSeasonMemoryMsg.class, xddq.pb.ElementalBondsSeasonMemoryMsg.Builder.class);
  }

  private int bitField0_;
  public static final int OLDSEASONID_FIELD_NUMBER = 1;
  private int oldSeasonId_ = 0;
  /**
   * <code>required int32 oldSeasonId = 1;</code>
   * @return Whether the oldSeasonId field is set.
   */
  @java.lang.Override
  public boolean hasOldSeasonId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 oldSeasonId = 1;</code>
   * @return The oldSeasonId.
   */
  @java.lang.Override
  public int getOldSeasonId() {
    return oldSeasonId_;
  }

  public static final int SEASONBATTLETIMES_FIELD_NUMBER = 2;
  private long seasonBattleTimes_ = 0L;
  /**
   * <code>required int64 seasonBattleTimes = 2;</code>
   * @return Whether the seasonBattleTimes field is set.
   */
  @java.lang.Override
  public boolean hasSeasonBattleTimes() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>required int64 seasonBattleTimes = 2;</code>
   * @return The seasonBattleTimes.
   */
  @java.lang.Override
  public long getSeasonBattleTimes() {
    return seasonBattleTimes_;
  }

  public static final int HONORMOMENT_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.ElementalBondsHonorMomentMsg> honorMoment_;
  /**
   * <code>repeated .xddq.pb.ElementalBondsHonorMomentMsg honorMoment = 3;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.ElementalBondsHonorMomentMsg> getHonorMomentList() {
    return honorMoment_;
  }
  /**
   * <code>repeated .xddq.pb.ElementalBondsHonorMomentMsg honorMoment = 3;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.ElementalBondsHonorMomentMsgOrBuilder> 
      getHonorMomentOrBuilderList() {
    return honorMoment_;
  }
  /**
   * <code>repeated .xddq.pb.ElementalBondsHonorMomentMsg honorMoment = 3;</code>
   */
  @java.lang.Override
  public int getHonorMomentCount() {
    return honorMoment_.size();
  }
  /**
   * <code>repeated .xddq.pb.ElementalBondsHonorMomentMsg honorMoment = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.ElementalBondsHonorMomentMsg getHonorMoment(int index) {
    return honorMoment_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.ElementalBondsHonorMomentMsg honorMoment = 3;</code>
   */
  @java.lang.Override
  public xddq.pb.ElementalBondsHonorMomentMsgOrBuilder getHonorMomentOrBuilder(
      int index) {
    return honorMoment_.get(index);
  }

  public static final int TOPUNLOCKCARDID_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private com.google.protobuf.Internal.IntList topUnlockCardId_ =
      emptyIntList();
  /**
   * <code>repeated int32 topUnlockCardId = 4;</code>
   * @return A list containing the topUnlockCardId.
   */
  @java.lang.Override
  public java.util.List<java.lang.Integer>
      getTopUnlockCardIdList() {
    return topUnlockCardId_;
  }
  /**
   * <code>repeated int32 topUnlockCardId = 4;</code>
   * @return The count of topUnlockCardId.
   */
  public int getTopUnlockCardIdCount() {
    return topUnlockCardId_.size();
  }
  /**
   * <code>repeated int32 topUnlockCardId = 4;</code>
   * @param index The index of the element to return.
   * @return The topUnlockCardId at the given index.
   */
  public int getTopUnlockCardId(int index) {
    return topUnlockCardId_.getInt(index);
  }

  public static final int TOPUSECARDID_FIELD_NUMBER = 5;
  private int topUseCardId_ = 0;
  /**
   * <code>required int32 topUseCardId = 5;</code>
   * @return Whether the topUseCardId field is set.
   */
  @java.lang.Override
  public boolean hasTopUseCardId() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>required int32 topUseCardId = 5;</code>
   * @return The topUseCardId.
   */
  @java.lang.Override
  public int getTopUseCardId() {
    return topUseCardId_;
  }

  public static final int TOPUSECARDTIMES_FIELD_NUMBER = 6;
  private long topUseCardTimes_ = 0L;
  /**
   * <code>required int64 topUseCardTimes = 6;</code>
   * @return Whether the topUseCardTimes field is set.
   */
  @java.lang.Override
  public boolean hasTopUseCardTimes() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>required int64 topUseCardTimes = 6;</code>
   * @return The topUseCardTimes.
   */
  @java.lang.Override
  public long getTopUseCardTimes() {
    return topUseCardTimes_;
  }

  public static final int SEASONSCORE_FIELD_NUMBER = 7;
  private long seasonScore_ = 0L;
  /**
   * <code>required int64 seasonScore = 7;</code>
   * @return Whether the seasonScore field is set.
   */
  @java.lang.Override
  public boolean hasSeasonScore() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>required int64 seasonScore = 7;</code>
   * @return The seasonScore.
   */
  @java.lang.Override
  public long getSeasonScore() {
    return seasonScore_;
  }

  public static final int SEASONRANK_FIELD_NUMBER = 8;
  private int seasonRank_ = 0;
  /**
   * <code>required int32 seasonRank = 8;</code>
   * @return Whether the seasonRank field is set.
   */
  @java.lang.Override
  public boolean hasSeasonRank() {
    return ((bitField0_ & 0x00000020) != 0);
  }
  /**
   * <code>required int32 seasonRank = 8;</code>
   * @return The seasonRank.
   */
  @java.lang.Override
  public int getSeasonRank() {
    return seasonRank_;
  }

  public static final int ONEACTIONMAXSCORE_FIELD_NUMBER = 9;
  private int oneActionMaxScore_ = 0;
  /**
   * <code>required int32 oneActionMaxScore = 9;</code>
   * @return Whether the oneActionMaxScore field is set.
   */
  @java.lang.Override
  public boolean hasOneActionMaxScore() {
    return ((bitField0_ & 0x00000040) != 0);
  }
  /**
   * <code>required int32 oneActionMaxScore = 9;</code>
   * @return The oneActionMaxScore.
   */
  @java.lang.Override
  public int getOneActionMaxScore() {
    return oneActionMaxScore_;
  }

  public static final int EXPRESSIONID_FIELD_NUMBER = 10;
  private int expressionId_ = 0;
  /**
   * <code>optional int32 expressionId = 10;</code>
   * @return Whether the expressionId field is set.
   */
  @java.lang.Override
  public boolean hasExpressionId() {
    return ((bitField0_ & 0x00000080) != 0);
  }
  /**
   * <code>optional int32 expressionId = 10;</code>
   * @return The expressionId.
   */
  @java.lang.Override
  public int getExpressionId() {
    return expressionId_;
  }

  public static final int TOPSCORE_FIELD_NUMBER = 11;
  private long topScore_ = 0L;
  /**
   * <code>required int64 topScore = 11;</code>
   * @return Whether the topScore field is set.
   */
  @java.lang.Override
  public boolean hasTopScore() {
    return ((bitField0_ & 0x00000100) != 0);
  }
  /**
   * <code>required int64 topScore = 11;</code>
   * @return The topScore.
   */
  @java.lang.Override
  public long getTopScore() {
    return topScore_;
  }

  public static final int EXPRESSIONUSETIMES_FIELD_NUMBER = 12;
  private int expressionUseTimes_ = 0;
  /**
   * <code>optional int32 expressionUseTimes = 12;</code>
   * @return Whether the expressionUseTimes field is set.
   */
  @java.lang.Override
  public boolean hasExpressionUseTimes() {
    return ((bitField0_ & 0x00000200) != 0);
  }
  /**
   * <code>optional int32 expressionUseTimes = 12;</code>
   * @return The expressionUseTimes.
   */
  @java.lang.Override
  public int getExpressionUseTimes() {
    return expressionUseTimes_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasOldSeasonId()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasSeasonBattleTimes()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasTopUseCardId()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasTopUseCardTimes()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasSeasonScore()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasSeasonRank()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasOneActionMaxScore()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasTopScore()) {
      memoizedIsInitialized = 0;
      return false;
    }
    for (int i = 0; i < getHonorMomentCount(); i++) {
      if (!getHonorMoment(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, oldSeasonId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt64(2, seasonBattleTimes_);
    }
    for (int i = 0; i < honorMoment_.size(); i++) {
      output.writeMessage(3, honorMoment_.get(i));
    }
    for (int i = 0; i < topUnlockCardId_.size(); i++) {
      output.writeInt32(4, topUnlockCardId_.getInt(i));
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt32(5, topUseCardId_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt64(6, topUseCardTimes_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      output.writeInt64(7, seasonScore_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      output.writeInt32(8, seasonRank_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      output.writeInt32(9, oneActionMaxScore_);
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      output.writeInt32(10, expressionId_);
    }
    if (((bitField0_ & 0x00000100) != 0)) {
      output.writeInt64(11, topScore_);
    }
    if (((bitField0_ & 0x00000200) != 0)) {
      output.writeInt32(12, expressionUseTimes_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, oldSeasonId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(2, seasonBattleTimes_);
    }
    for (int i = 0; i < honorMoment_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, honorMoment_.get(i));
    }
    {
      int dataSize = 0;
      for (int i = 0; i < topUnlockCardId_.size(); i++) {
        dataSize += com.google.protobuf.CodedOutputStream
          .computeInt32SizeNoTag(topUnlockCardId_.getInt(i));
      }
      size += dataSize;
      size += 1 * getTopUnlockCardIdList().size();
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(5, topUseCardId_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(6, topUseCardTimes_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(7, seasonScore_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(8, seasonRank_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(9, oneActionMaxScore_);
    }
    if (((bitField0_ & 0x00000080) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(10, expressionId_);
    }
    if (((bitField0_ & 0x00000100) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(11, topScore_);
    }
    if (((bitField0_ & 0x00000200) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(12, expressionUseTimes_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.ElementalBondsSeasonMemoryMsg)) {
      return super.equals(obj);
    }
    xddq.pb.ElementalBondsSeasonMemoryMsg other = (xddq.pb.ElementalBondsSeasonMemoryMsg) obj;

    if (hasOldSeasonId() != other.hasOldSeasonId()) return false;
    if (hasOldSeasonId()) {
      if (getOldSeasonId()
          != other.getOldSeasonId()) return false;
    }
    if (hasSeasonBattleTimes() != other.hasSeasonBattleTimes()) return false;
    if (hasSeasonBattleTimes()) {
      if (getSeasonBattleTimes()
          != other.getSeasonBattleTimes()) return false;
    }
    if (!getHonorMomentList()
        .equals(other.getHonorMomentList())) return false;
    if (!getTopUnlockCardIdList()
        .equals(other.getTopUnlockCardIdList())) return false;
    if (hasTopUseCardId() != other.hasTopUseCardId()) return false;
    if (hasTopUseCardId()) {
      if (getTopUseCardId()
          != other.getTopUseCardId()) return false;
    }
    if (hasTopUseCardTimes() != other.hasTopUseCardTimes()) return false;
    if (hasTopUseCardTimes()) {
      if (getTopUseCardTimes()
          != other.getTopUseCardTimes()) return false;
    }
    if (hasSeasonScore() != other.hasSeasonScore()) return false;
    if (hasSeasonScore()) {
      if (getSeasonScore()
          != other.getSeasonScore()) return false;
    }
    if (hasSeasonRank() != other.hasSeasonRank()) return false;
    if (hasSeasonRank()) {
      if (getSeasonRank()
          != other.getSeasonRank()) return false;
    }
    if (hasOneActionMaxScore() != other.hasOneActionMaxScore()) return false;
    if (hasOneActionMaxScore()) {
      if (getOneActionMaxScore()
          != other.getOneActionMaxScore()) return false;
    }
    if (hasExpressionId() != other.hasExpressionId()) return false;
    if (hasExpressionId()) {
      if (getExpressionId()
          != other.getExpressionId()) return false;
    }
    if (hasTopScore() != other.hasTopScore()) return false;
    if (hasTopScore()) {
      if (getTopScore()
          != other.getTopScore()) return false;
    }
    if (hasExpressionUseTimes() != other.hasExpressionUseTimes()) return false;
    if (hasExpressionUseTimes()) {
      if (getExpressionUseTimes()
          != other.getExpressionUseTimes()) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasOldSeasonId()) {
      hash = (37 * hash) + OLDSEASONID_FIELD_NUMBER;
      hash = (53 * hash) + getOldSeasonId();
    }
    if (hasSeasonBattleTimes()) {
      hash = (37 * hash) + SEASONBATTLETIMES_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getSeasonBattleTimes());
    }
    if (getHonorMomentCount() > 0) {
      hash = (37 * hash) + HONORMOMENT_FIELD_NUMBER;
      hash = (53 * hash) + getHonorMomentList().hashCode();
    }
    if (getTopUnlockCardIdCount() > 0) {
      hash = (37 * hash) + TOPUNLOCKCARDID_FIELD_NUMBER;
      hash = (53 * hash) + getTopUnlockCardIdList().hashCode();
    }
    if (hasTopUseCardId()) {
      hash = (37 * hash) + TOPUSECARDID_FIELD_NUMBER;
      hash = (53 * hash) + getTopUseCardId();
    }
    if (hasTopUseCardTimes()) {
      hash = (37 * hash) + TOPUSECARDTIMES_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTopUseCardTimes());
    }
    if (hasSeasonScore()) {
      hash = (37 * hash) + SEASONSCORE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getSeasonScore());
    }
    if (hasSeasonRank()) {
      hash = (37 * hash) + SEASONRANK_FIELD_NUMBER;
      hash = (53 * hash) + getSeasonRank();
    }
    if (hasOneActionMaxScore()) {
      hash = (37 * hash) + ONEACTIONMAXSCORE_FIELD_NUMBER;
      hash = (53 * hash) + getOneActionMaxScore();
    }
    if (hasExpressionId()) {
      hash = (37 * hash) + EXPRESSIONID_FIELD_NUMBER;
      hash = (53 * hash) + getExpressionId();
    }
    if (hasTopScore()) {
      hash = (37 * hash) + TOPSCORE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTopScore());
    }
    if (hasExpressionUseTimes()) {
      hash = (37 * hash) + EXPRESSIONUSETIMES_FIELD_NUMBER;
      hash = (53 * hash) + getExpressionUseTimes();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.ElementalBondsSeasonMemoryMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ElementalBondsSeasonMemoryMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ElementalBondsSeasonMemoryMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ElementalBondsSeasonMemoryMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ElementalBondsSeasonMemoryMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.ElementalBondsSeasonMemoryMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.ElementalBondsSeasonMemoryMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.ElementalBondsSeasonMemoryMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.ElementalBondsSeasonMemoryMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.ElementalBondsSeasonMemoryMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.ElementalBondsSeasonMemoryMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.ElementalBondsSeasonMemoryMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.ElementalBondsSeasonMemoryMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.ElementalBondsSeasonMemoryMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.ElementalBondsSeasonMemoryMsg)
      xddq.pb.ElementalBondsSeasonMemoryMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ElementalBondsSeasonMemoryMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ElementalBondsSeasonMemoryMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.ElementalBondsSeasonMemoryMsg.class, xddq.pb.ElementalBondsSeasonMemoryMsg.Builder.class);
    }

    // Construct using xddq.pb.ElementalBondsSeasonMemoryMsg.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      oldSeasonId_ = 0;
      seasonBattleTimes_ = 0L;
      if (honorMomentBuilder_ == null) {
        honorMoment_ = java.util.Collections.emptyList();
      } else {
        honorMoment_ = null;
        honorMomentBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000004);
      topUnlockCardId_ = emptyIntList();
      topUseCardId_ = 0;
      topUseCardTimes_ = 0L;
      seasonScore_ = 0L;
      seasonRank_ = 0;
      oneActionMaxScore_ = 0;
      expressionId_ = 0;
      topScore_ = 0L;
      expressionUseTimes_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_ElementalBondsSeasonMemoryMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.ElementalBondsSeasonMemoryMsg getDefaultInstanceForType() {
      return xddq.pb.ElementalBondsSeasonMemoryMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.ElementalBondsSeasonMemoryMsg build() {
      xddq.pb.ElementalBondsSeasonMemoryMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.ElementalBondsSeasonMemoryMsg buildPartial() {
      xddq.pb.ElementalBondsSeasonMemoryMsg result = new xddq.pb.ElementalBondsSeasonMemoryMsg(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.ElementalBondsSeasonMemoryMsg result) {
      if (honorMomentBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0)) {
          honorMoment_ = java.util.Collections.unmodifiableList(honorMoment_);
          bitField0_ = (bitField0_ & ~0x00000004);
        }
        result.honorMoment_ = honorMoment_;
      } else {
        result.honorMoment_ = honorMomentBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.ElementalBondsSeasonMemoryMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.oldSeasonId_ = oldSeasonId_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.seasonBattleTimes_ = seasonBattleTimes_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        topUnlockCardId_.makeImmutable();
        result.topUnlockCardId_ = topUnlockCardId_;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.topUseCardId_ = topUseCardId_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.topUseCardTimes_ = topUseCardTimes_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.seasonScore_ = seasonScore_;
        to_bitField0_ |= 0x00000010;
      }
      if (((from_bitField0_ & 0x00000080) != 0)) {
        result.seasonRank_ = seasonRank_;
        to_bitField0_ |= 0x00000020;
      }
      if (((from_bitField0_ & 0x00000100) != 0)) {
        result.oneActionMaxScore_ = oneActionMaxScore_;
        to_bitField0_ |= 0x00000040;
      }
      if (((from_bitField0_ & 0x00000200) != 0)) {
        result.expressionId_ = expressionId_;
        to_bitField0_ |= 0x00000080;
      }
      if (((from_bitField0_ & 0x00000400) != 0)) {
        result.topScore_ = topScore_;
        to_bitField0_ |= 0x00000100;
      }
      if (((from_bitField0_ & 0x00000800) != 0)) {
        result.expressionUseTimes_ = expressionUseTimes_;
        to_bitField0_ |= 0x00000200;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.ElementalBondsSeasonMemoryMsg) {
        return mergeFrom((xddq.pb.ElementalBondsSeasonMemoryMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.ElementalBondsSeasonMemoryMsg other) {
      if (other == xddq.pb.ElementalBondsSeasonMemoryMsg.getDefaultInstance()) return this;
      if (other.hasOldSeasonId()) {
        setOldSeasonId(other.getOldSeasonId());
      }
      if (other.hasSeasonBattleTimes()) {
        setSeasonBattleTimes(other.getSeasonBattleTimes());
      }
      if (honorMomentBuilder_ == null) {
        if (!other.honorMoment_.isEmpty()) {
          if (honorMoment_.isEmpty()) {
            honorMoment_ = other.honorMoment_;
            bitField0_ = (bitField0_ & ~0x00000004);
          } else {
            ensureHonorMomentIsMutable();
            honorMoment_.addAll(other.honorMoment_);
          }
          onChanged();
        }
      } else {
        if (!other.honorMoment_.isEmpty()) {
          if (honorMomentBuilder_.isEmpty()) {
            honorMomentBuilder_.dispose();
            honorMomentBuilder_ = null;
            honorMoment_ = other.honorMoment_;
            bitField0_ = (bitField0_ & ~0x00000004);
            honorMomentBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetHonorMomentFieldBuilder() : null;
          } else {
            honorMomentBuilder_.addAllMessages(other.honorMoment_);
          }
        }
      }
      if (!other.topUnlockCardId_.isEmpty()) {
        if (topUnlockCardId_.isEmpty()) {
          topUnlockCardId_ = other.topUnlockCardId_;
          topUnlockCardId_.makeImmutable();
          bitField0_ |= 0x00000008;
        } else {
          ensureTopUnlockCardIdIsMutable();
          topUnlockCardId_.addAll(other.topUnlockCardId_);
        }
        onChanged();
      }
      if (other.hasTopUseCardId()) {
        setTopUseCardId(other.getTopUseCardId());
      }
      if (other.hasTopUseCardTimes()) {
        setTopUseCardTimes(other.getTopUseCardTimes());
      }
      if (other.hasSeasonScore()) {
        setSeasonScore(other.getSeasonScore());
      }
      if (other.hasSeasonRank()) {
        setSeasonRank(other.getSeasonRank());
      }
      if (other.hasOneActionMaxScore()) {
        setOneActionMaxScore(other.getOneActionMaxScore());
      }
      if (other.hasExpressionId()) {
        setExpressionId(other.getExpressionId());
      }
      if (other.hasTopScore()) {
        setTopScore(other.getTopScore());
      }
      if (other.hasExpressionUseTimes()) {
        setExpressionUseTimes(other.getExpressionUseTimes());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasOldSeasonId()) {
        return false;
      }
      if (!hasSeasonBattleTimes()) {
        return false;
      }
      if (!hasTopUseCardId()) {
        return false;
      }
      if (!hasTopUseCardTimes()) {
        return false;
      }
      if (!hasSeasonScore()) {
        return false;
      }
      if (!hasSeasonRank()) {
        return false;
      }
      if (!hasOneActionMaxScore()) {
        return false;
      }
      if (!hasTopScore()) {
        return false;
      }
      for (int i = 0; i < getHonorMomentCount(); i++) {
        if (!getHonorMoment(i).isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              oldSeasonId_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              seasonBattleTimes_ = input.readInt64();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 26: {
              xddq.pb.ElementalBondsHonorMomentMsg m =
                  input.readMessage(
                      xddq.pb.ElementalBondsHonorMomentMsg.parser(),
                      extensionRegistry);
              if (honorMomentBuilder_ == null) {
                ensureHonorMomentIsMutable();
                honorMoment_.add(m);
              } else {
                honorMomentBuilder_.addMessage(m);
              }
              break;
            } // case 26
            case 32: {
              int v = input.readInt32();
              ensureTopUnlockCardIdIsMutable();
              topUnlockCardId_.addInt(v);
              break;
            } // case 32
            case 34: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              ensureTopUnlockCardIdIsMutable();
              while (input.getBytesUntilLimit() > 0) {
                topUnlockCardId_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            } // case 34
            case 40: {
              topUseCardId_ = input.readInt32();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            case 48: {
              topUseCardTimes_ = input.readInt64();
              bitField0_ |= 0x00000020;
              break;
            } // case 48
            case 56: {
              seasonScore_ = input.readInt64();
              bitField0_ |= 0x00000040;
              break;
            } // case 56
            case 64: {
              seasonRank_ = input.readInt32();
              bitField0_ |= 0x00000080;
              break;
            } // case 64
            case 72: {
              oneActionMaxScore_ = input.readInt32();
              bitField0_ |= 0x00000100;
              break;
            } // case 72
            case 80: {
              expressionId_ = input.readInt32();
              bitField0_ |= 0x00000200;
              break;
            } // case 80
            case 88: {
              topScore_ = input.readInt64();
              bitField0_ |= 0x00000400;
              break;
            } // case 88
            case 96: {
              expressionUseTimes_ = input.readInt32();
              bitField0_ |= 0x00000800;
              break;
            } // case 96
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int oldSeasonId_ ;
    /**
     * <code>required int32 oldSeasonId = 1;</code>
     * @return Whether the oldSeasonId field is set.
     */
    @java.lang.Override
    public boolean hasOldSeasonId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 oldSeasonId = 1;</code>
     * @return The oldSeasonId.
     */
    @java.lang.Override
    public int getOldSeasonId() {
      return oldSeasonId_;
    }
    /**
     * <code>required int32 oldSeasonId = 1;</code>
     * @param value The oldSeasonId to set.
     * @return This builder for chaining.
     */
    public Builder setOldSeasonId(int value) {

      oldSeasonId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 oldSeasonId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearOldSeasonId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      oldSeasonId_ = 0;
      onChanged();
      return this;
    }

    private long seasonBattleTimes_ ;
    /**
     * <code>required int64 seasonBattleTimes = 2;</code>
     * @return Whether the seasonBattleTimes field is set.
     */
    @java.lang.Override
    public boolean hasSeasonBattleTimes() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>required int64 seasonBattleTimes = 2;</code>
     * @return The seasonBattleTimes.
     */
    @java.lang.Override
    public long getSeasonBattleTimes() {
      return seasonBattleTimes_;
    }
    /**
     * <code>required int64 seasonBattleTimes = 2;</code>
     * @param value The seasonBattleTimes to set.
     * @return This builder for chaining.
     */
    public Builder setSeasonBattleTimes(long value) {

      seasonBattleTimes_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>required int64 seasonBattleTimes = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearSeasonBattleTimes() {
      bitField0_ = (bitField0_ & ~0x00000002);
      seasonBattleTimes_ = 0L;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.ElementalBondsHonorMomentMsg> honorMoment_ =
      java.util.Collections.emptyList();
    private void ensureHonorMomentIsMutable() {
      if (!((bitField0_ & 0x00000004) != 0)) {
        honorMoment_ = new java.util.ArrayList<xddq.pb.ElementalBondsHonorMomentMsg>(honorMoment_);
        bitField0_ |= 0x00000004;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.ElementalBondsHonorMomentMsg, xddq.pb.ElementalBondsHonorMomentMsg.Builder, xddq.pb.ElementalBondsHonorMomentMsgOrBuilder> honorMomentBuilder_;

    /**
     * <code>repeated .xddq.pb.ElementalBondsHonorMomentMsg honorMoment = 3;</code>
     */
    public java.util.List<xddq.pb.ElementalBondsHonorMomentMsg> getHonorMomentList() {
      if (honorMomentBuilder_ == null) {
        return java.util.Collections.unmodifiableList(honorMoment_);
      } else {
        return honorMomentBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsHonorMomentMsg honorMoment = 3;</code>
     */
    public int getHonorMomentCount() {
      if (honorMomentBuilder_ == null) {
        return honorMoment_.size();
      } else {
        return honorMomentBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsHonorMomentMsg honorMoment = 3;</code>
     */
    public xddq.pb.ElementalBondsHonorMomentMsg getHonorMoment(int index) {
      if (honorMomentBuilder_ == null) {
        return honorMoment_.get(index);
      } else {
        return honorMomentBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsHonorMomentMsg honorMoment = 3;</code>
     */
    public Builder setHonorMoment(
        int index, xddq.pb.ElementalBondsHonorMomentMsg value) {
      if (honorMomentBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureHonorMomentIsMutable();
        honorMoment_.set(index, value);
        onChanged();
      } else {
        honorMomentBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsHonorMomentMsg honorMoment = 3;</code>
     */
    public Builder setHonorMoment(
        int index, xddq.pb.ElementalBondsHonorMomentMsg.Builder builderForValue) {
      if (honorMomentBuilder_ == null) {
        ensureHonorMomentIsMutable();
        honorMoment_.set(index, builderForValue.build());
        onChanged();
      } else {
        honorMomentBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsHonorMomentMsg honorMoment = 3;</code>
     */
    public Builder addHonorMoment(xddq.pb.ElementalBondsHonorMomentMsg value) {
      if (honorMomentBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureHonorMomentIsMutable();
        honorMoment_.add(value);
        onChanged();
      } else {
        honorMomentBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsHonorMomentMsg honorMoment = 3;</code>
     */
    public Builder addHonorMoment(
        int index, xddq.pb.ElementalBondsHonorMomentMsg value) {
      if (honorMomentBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureHonorMomentIsMutable();
        honorMoment_.add(index, value);
        onChanged();
      } else {
        honorMomentBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsHonorMomentMsg honorMoment = 3;</code>
     */
    public Builder addHonorMoment(
        xddq.pb.ElementalBondsHonorMomentMsg.Builder builderForValue) {
      if (honorMomentBuilder_ == null) {
        ensureHonorMomentIsMutable();
        honorMoment_.add(builderForValue.build());
        onChanged();
      } else {
        honorMomentBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsHonorMomentMsg honorMoment = 3;</code>
     */
    public Builder addHonorMoment(
        int index, xddq.pb.ElementalBondsHonorMomentMsg.Builder builderForValue) {
      if (honorMomentBuilder_ == null) {
        ensureHonorMomentIsMutable();
        honorMoment_.add(index, builderForValue.build());
        onChanged();
      } else {
        honorMomentBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsHonorMomentMsg honorMoment = 3;</code>
     */
    public Builder addAllHonorMoment(
        java.lang.Iterable<? extends xddq.pb.ElementalBondsHonorMomentMsg> values) {
      if (honorMomentBuilder_ == null) {
        ensureHonorMomentIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, honorMoment_);
        onChanged();
      } else {
        honorMomentBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsHonorMomentMsg honorMoment = 3;</code>
     */
    public Builder clearHonorMoment() {
      if (honorMomentBuilder_ == null) {
        honorMoment_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
      } else {
        honorMomentBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsHonorMomentMsg honorMoment = 3;</code>
     */
    public Builder removeHonorMoment(int index) {
      if (honorMomentBuilder_ == null) {
        ensureHonorMomentIsMutable();
        honorMoment_.remove(index);
        onChanged();
      } else {
        honorMomentBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsHonorMomentMsg honorMoment = 3;</code>
     */
    public xddq.pb.ElementalBondsHonorMomentMsg.Builder getHonorMomentBuilder(
        int index) {
      return internalGetHonorMomentFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsHonorMomentMsg honorMoment = 3;</code>
     */
    public xddq.pb.ElementalBondsHonorMomentMsgOrBuilder getHonorMomentOrBuilder(
        int index) {
      if (honorMomentBuilder_ == null) {
        return honorMoment_.get(index);  } else {
        return honorMomentBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsHonorMomentMsg honorMoment = 3;</code>
     */
    public java.util.List<? extends xddq.pb.ElementalBondsHonorMomentMsgOrBuilder> 
         getHonorMomentOrBuilderList() {
      if (honorMomentBuilder_ != null) {
        return honorMomentBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(honorMoment_);
      }
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsHonorMomentMsg honorMoment = 3;</code>
     */
    public xddq.pb.ElementalBondsHonorMomentMsg.Builder addHonorMomentBuilder() {
      return internalGetHonorMomentFieldBuilder().addBuilder(
          xddq.pb.ElementalBondsHonorMomentMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsHonorMomentMsg honorMoment = 3;</code>
     */
    public xddq.pb.ElementalBondsHonorMomentMsg.Builder addHonorMomentBuilder(
        int index) {
      return internalGetHonorMomentFieldBuilder().addBuilder(
          index, xddq.pb.ElementalBondsHonorMomentMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.ElementalBondsHonorMomentMsg honorMoment = 3;</code>
     */
    public java.util.List<xddq.pb.ElementalBondsHonorMomentMsg.Builder> 
         getHonorMomentBuilderList() {
      return internalGetHonorMomentFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.ElementalBondsHonorMomentMsg, xddq.pb.ElementalBondsHonorMomentMsg.Builder, xddq.pb.ElementalBondsHonorMomentMsgOrBuilder> 
        internalGetHonorMomentFieldBuilder() {
      if (honorMomentBuilder_ == null) {
        honorMomentBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.ElementalBondsHonorMomentMsg, xddq.pb.ElementalBondsHonorMomentMsg.Builder, xddq.pb.ElementalBondsHonorMomentMsgOrBuilder>(
                honorMoment_,
                ((bitField0_ & 0x00000004) != 0),
                getParentForChildren(),
                isClean());
        honorMoment_ = null;
      }
      return honorMomentBuilder_;
    }

    private com.google.protobuf.Internal.IntList topUnlockCardId_ = emptyIntList();
    private void ensureTopUnlockCardIdIsMutable() {
      if (!topUnlockCardId_.isModifiable()) {
        topUnlockCardId_ = makeMutableCopy(topUnlockCardId_);
      }
      bitField0_ |= 0x00000008;
    }
    /**
     * <code>repeated int32 topUnlockCardId = 4;</code>
     * @return A list containing the topUnlockCardId.
     */
    public java.util.List<java.lang.Integer>
        getTopUnlockCardIdList() {
      topUnlockCardId_.makeImmutable();
      return topUnlockCardId_;
    }
    /**
     * <code>repeated int32 topUnlockCardId = 4;</code>
     * @return The count of topUnlockCardId.
     */
    public int getTopUnlockCardIdCount() {
      return topUnlockCardId_.size();
    }
    /**
     * <code>repeated int32 topUnlockCardId = 4;</code>
     * @param index The index of the element to return.
     * @return The topUnlockCardId at the given index.
     */
    public int getTopUnlockCardId(int index) {
      return topUnlockCardId_.getInt(index);
    }
    /**
     * <code>repeated int32 topUnlockCardId = 4;</code>
     * @param index The index to set the value at.
     * @param value The topUnlockCardId to set.
     * @return This builder for chaining.
     */
    public Builder setTopUnlockCardId(
        int index, int value) {

      ensureTopUnlockCardIdIsMutable();
      topUnlockCardId_.setInt(index, value);
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 topUnlockCardId = 4;</code>
     * @param value The topUnlockCardId to add.
     * @return This builder for chaining.
     */
    public Builder addTopUnlockCardId(int value) {

      ensureTopUnlockCardIdIsMutable();
      topUnlockCardId_.addInt(value);
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 topUnlockCardId = 4;</code>
     * @param values The topUnlockCardId to add.
     * @return This builder for chaining.
     */
    public Builder addAllTopUnlockCardId(
        java.lang.Iterable<? extends java.lang.Integer> values) {
      ensureTopUnlockCardIdIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, topUnlockCardId_);
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>repeated int32 topUnlockCardId = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearTopUnlockCardId() {
      topUnlockCardId_ = emptyIntList();
      bitField0_ = (bitField0_ & ~0x00000008);
      onChanged();
      return this;
    }

    private int topUseCardId_ ;
    /**
     * <code>required int32 topUseCardId = 5;</code>
     * @return Whether the topUseCardId field is set.
     */
    @java.lang.Override
    public boolean hasTopUseCardId() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>required int32 topUseCardId = 5;</code>
     * @return The topUseCardId.
     */
    @java.lang.Override
    public int getTopUseCardId() {
      return topUseCardId_;
    }
    /**
     * <code>required int32 topUseCardId = 5;</code>
     * @param value The topUseCardId to set.
     * @return This builder for chaining.
     */
    public Builder setTopUseCardId(int value) {

      topUseCardId_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 topUseCardId = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearTopUseCardId() {
      bitField0_ = (bitField0_ & ~0x00000010);
      topUseCardId_ = 0;
      onChanged();
      return this;
    }

    private long topUseCardTimes_ ;
    /**
     * <code>required int64 topUseCardTimes = 6;</code>
     * @return Whether the topUseCardTimes field is set.
     */
    @java.lang.Override
    public boolean hasTopUseCardTimes() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>required int64 topUseCardTimes = 6;</code>
     * @return The topUseCardTimes.
     */
    @java.lang.Override
    public long getTopUseCardTimes() {
      return topUseCardTimes_;
    }
    /**
     * <code>required int64 topUseCardTimes = 6;</code>
     * @param value The topUseCardTimes to set.
     * @return This builder for chaining.
     */
    public Builder setTopUseCardTimes(long value) {

      topUseCardTimes_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>required int64 topUseCardTimes = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearTopUseCardTimes() {
      bitField0_ = (bitField0_ & ~0x00000020);
      topUseCardTimes_ = 0L;
      onChanged();
      return this;
    }

    private long seasonScore_ ;
    /**
     * <code>required int64 seasonScore = 7;</code>
     * @return Whether the seasonScore field is set.
     */
    @java.lang.Override
    public boolean hasSeasonScore() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <code>required int64 seasonScore = 7;</code>
     * @return The seasonScore.
     */
    @java.lang.Override
    public long getSeasonScore() {
      return seasonScore_;
    }
    /**
     * <code>required int64 seasonScore = 7;</code>
     * @param value The seasonScore to set.
     * @return This builder for chaining.
     */
    public Builder setSeasonScore(long value) {

      seasonScore_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>required int64 seasonScore = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearSeasonScore() {
      bitField0_ = (bitField0_ & ~0x00000040);
      seasonScore_ = 0L;
      onChanged();
      return this;
    }

    private int seasonRank_ ;
    /**
     * <code>required int32 seasonRank = 8;</code>
     * @return Whether the seasonRank field is set.
     */
    @java.lang.Override
    public boolean hasSeasonRank() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <code>required int32 seasonRank = 8;</code>
     * @return The seasonRank.
     */
    @java.lang.Override
    public int getSeasonRank() {
      return seasonRank_;
    }
    /**
     * <code>required int32 seasonRank = 8;</code>
     * @param value The seasonRank to set.
     * @return This builder for chaining.
     */
    public Builder setSeasonRank(int value) {

      seasonRank_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 seasonRank = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearSeasonRank() {
      bitField0_ = (bitField0_ & ~0x00000080);
      seasonRank_ = 0;
      onChanged();
      return this;
    }

    private int oneActionMaxScore_ ;
    /**
     * <code>required int32 oneActionMaxScore = 9;</code>
     * @return Whether the oneActionMaxScore field is set.
     */
    @java.lang.Override
    public boolean hasOneActionMaxScore() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <code>required int32 oneActionMaxScore = 9;</code>
     * @return The oneActionMaxScore.
     */
    @java.lang.Override
    public int getOneActionMaxScore() {
      return oneActionMaxScore_;
    }
    /**
     * <code>required int32 oneActionMaxScore = 9;</code>
     * @param value The oneActionMaxScore to set.
     * @return This builder for chaining.
     */
    public Builder setOneActionMaxScore(int value) {

      oneActionMaxScore_ = value;
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 oneActionMaxScore = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearOneActionMaxScore() {
      bitField0_ = (bitField0_ & ~0x00000100);
      oneActionMaxScore_ = 0;
      onChanged();
      return this;
    }

    private int expressionId_ ;
    /**
     * <code>optional int32 expressionId = 10;</code>
     * @return Whether the expressionId field is set.
     */
    @java.lang.Override
    public boolean hasExpressionId() {
      return ((bitField0_ & 0x00000200) != 0);
    }
    /**
     * <code>optional int32 expressionId = 10;</code>
     * @return The expressionId.
     */
    @java.lang.Override
    public int getExpressionId() {
      return expressionId_;
    }
    /**
     * <code>optional int32 expressionId = 10;</code>
     * @param value The expressionId to set.
     * @return This builder for chaining.
     */
    public Builder setExpressionId(int value) {

      expressionId_ = value;
      bitField0_ |= 0x00000200;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 expressionId = 10;</code>
     * @return This builder for chaining.
     */
    public Builder clearExpressionId() {
      bitField0_ = (bitField0_ & ~0x00000200);
      expressionId_ = 0;
      onChanged();
      return this;
    }

    private long topScore_ ;
    /**
     * <code>required int64 topScore = 11;</code>
     * @return Whether the topScore field is set.
     */
    @java.lang.Override
    public boolean hasTopScore() {
      return ((bitField0_ & 0x00000400) != 0);
    }
    /**
     * <code>required int64 topScore = 11;</code>
     * @return The topScore.
     */
    @java.lang.Override
    public long getTopScore() {
      return topScore_;
    }
    /**
     * <code>required int64 topScore = 11;</code>
     * @param value The topScore to set.
     * @return This builder for chaining.
     */
    public Builder setTopScore(long value) {

      topScore_ = value;
      bitField0_ |= 0x00000400;
      onChanged();
      return this;
    }
    /**
     * <code>required int64 topScore = 11;</code>
     * @return This builder for chaining.
     */
    public Builder clearTopScore() {
      bitField0_ = (bitField0_ & ~0x00000400);
      topScore_ = 0L;
      onChanged();
      return this;
    }

    private int expressionUseTimes_ ;
    /**
     * <code>optional int32 expressionUseTimes = 12;</code>
     * @return Whether the expressionUseTimes field is set.
     */
    @java.lang.Override
    public boolean hasExpressionUseTimes() {
      return ((bitField0_ & 0x00000800) != 0);
    }
    /**
     * <code>optional int32 expressionUseTimes = 12;</code>
     * @return The expressionUseTimes.
     */
    @java.lang.Override
    public int getExpressionUseTimes() {
      return expressionUseTimes_;
    }
    /**
     * <code>optional int32 expressionUseTimes = 12;</code>
     * @param value The expressionUseTimes to set.
     * @return This builder for chaining.
     */
    public Builder setExpressionUseTimes(int value) {

      expressionUseTimes_ = value;
      bitField0_ |= 0x00000800;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 expressionUseTimes = 12;</code>
     * @return This builder for chaining.
     */
    public Builder clearExpressionUseTimes() {
      bitField0_ = (bitField0_ & ~0x00000800);
      expressionUseTimes_ = 0;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.ElementalBondsSeasonMemoryMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.ElementalBondsSeasonMemoryMsg)
  private static final xddq.pb.ElementalBondsSeasonMemoryMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.ElementalBondsSeasonMemoryMsg();
  }

  public static xddq.pb.ElementalBondsSeasonMemoryMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ElementalBondsSeasonMemoryMsg>
      PARSER = new com.google.protobuf.AbstractParser<ElementalBondsSeasonMemoryMsg>() {
    @java.lang.Override
    public ElementalBondsSeasonMemoryMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<ElementalBondsSeasonMemoryMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ElementalBondsSeasonMemoryMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.ElementalBondsSeasonMemoryMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

