// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.SpChampDivisionConfig}
 */
public final class SpChampDivisionConfig extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.SpChampDivisionConfig)
    SpChampDivisionConfigOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      SpChampDivisionConfig.class.getName());
  }
  // Use SpChampDivisionConfig.newBuilder() to construct.
  private SpChampDivisionConfig(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private SpChampDivisionConfig() {
    divisionName_ = "";
    reward_ = "";
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_SpChampDivisionConfig_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_SpChampDivisionConfig_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.SpChampDivisionConfig.class, xddq.pb.SpChampDivisionConfig.Builder.class);
  }

  private int bitField0_;
  public static final int ACTIVITYID_FIELD_NUMBER = 1;
  private int activityId_ = 0;
  /**
   * <code>required int32 activityId = 1;</code>
   * @return Whether the activityId field is set.
   */
  @java.lang.Override
  public boolean hasActivityId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 activityId = 1;</code>
   * @return The activityId.
   */
  @java.lang.Override
  public int getActivityId() {
    return activityId_;
  }

  public static final int DIVISIONID_FIELD_NUMBER = 2;
  private int divisionId_ = 0;
  /**
   * <code>required int32 divisionId = 2;</code>
   * @return Whether the divisionId field is set.
   */
  @java.lang.Override
  public boolean hasDivisionId() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>required int32 divisionId = 2;</code>
   * @return The divisionId.
   */
  @java.lang.Override
  public int getDivisionId() {
    return divisionId_;
  }

  public static final int DIVISIONNAME_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private volatile java.lang.Object divisionName_ = "";
  /**
   * <code>required string divisionName = 3;</code>
   * @return Whether the divisionName field is set.
   */
  @java.lang.Override
  public boolean hasDivisionName() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>required string divisionName = 3;</code>
   * @return The divisionName.
   */
  @java.lang.Override
  public java.lang.String getDivisionName() {
    java.lang.Object ref = divisionName_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        divisionName_ = s;
      }
      return s;
    }
  }
  /**
   * <code>required string divisionName = 3;</code>
   * @return The bytes for divisionName.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getDivisionNameBytes() {
    java.lang.Object ref = divisionName_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      divisionName_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int MINSCORE_FIELD_NUMBER = 4;
  private int minScore_ = 0;
  /**
   * <code>required int32 minScore = 4;</code>
   * @return Whether the minScore field is set.
   */
  @java.lang.Override
  public boolean hasMinScore() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>required int32 minScore = 4;</code>
   * @return The minScore.
   */
  @java.lang.Override
  public int getMinScore() {
    return minScore_;
  }

  public static final int REWARD_FIELD_NUMBER = 5;
  @SuppressWarnings("serial")
  private volatile java.lang.Object reward_ = "";
  /**
   * <code>required string reward = 5;</code>
   * @return Whether the reward field is set.
   */
  @java.lang.Override
  public boolean hasReward() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>required string reward = 5;</code>
   * @return The reward.
   */
  @java.lang.Override
  public java.lang.String getReward() {
    java.lang.Object ref = reward_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        reward_ = s;
      }
      return s;
    }
  }
  /**
   * <code>required string reward = 5;</code>
   * @return The bytes for reward.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getRewardBytes() {
    java.lang.Object ref = reward_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      reward_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasActivityId()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasDivisionId()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasDivisionName()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasMinScore()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasReward()) {
      memoizedIsInitialized = 0;
      return false;
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, activityId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(2, divisionId_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 3, divisionName_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt32(4, minScore_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 5, reward_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, activityId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, divisionId_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(3, divisionName_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(4, minScore_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(5, reward_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.SpChampDivisionConfig)) {
      return super.equals(obj);
    }
    xddq.pb.SpChampDivisionConfig other = (xddq.pb.SpChampDivisionConfig) obj;

    if (hasActivityId() != other.hasActivityId()) return false;
    if (hasActivityId()) {
      if (getActivityId()
          != other.getActivityId()) return false;
    }
    if (hasDivisionId() != other.hasDivisionId()) return false;
    if (hasDivisionId()) {
      if (getDivisionId()
          != other.getDivisionId()) return false;
    }
    if (hasDivisionName() != other.hasDivisionName()) return false;
    if (hasDivisionName()) {
      if (!getDivisionName()
          .equals(other.getDivisionName())) return false;
    }
    if (hasMinScore() != other.hasMinScore()) return false;
    if (hasMinScore()) {
      if (getMinScore()
          != other.getMinScore()) return false;
    }
    if (hasReward() != other.hasReward()) return false;
    if (hasReward()) {
      if (!getReward()
          .equals(other.getReward())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasActivityId()) {
      hash = (37 * hash) + ACTIVITYID_FIELD_NUMBER;
      hash = (53 * hash) + getActivityId();
    }
    if (hasDivisionId()) {
      hash = (37 * hash) + DIVISIONID_FIELD_NUMBER;
      hash = (53 * hash) + getDivisionId();
    }
    if (hasDivisionName()) {
      hash = (37 * hash) + DIVISIONNAME_FIELD_NUMBER;
      hash = (53 * hash) + getDivisionName().hashCode();
    }
    if (hasMinScore()) {
      hash = (37 * hash) + MINSCORE_FIELD_NUMBER;
      hash = (53 * hash) + getMinScore();
    }
    if (hasReward()) {
      hash = (37 * hash) + REWARD_FIELD_NUMBER;
      hash = (53 * hash) + getReward().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.SpChampDivisionConfig parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.SpChampDivisionConfig parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.SpChampDivisionConfig parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.SpChampDivisionConfig parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.SpChampDivisionConfig parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.SpChampDivisionConfig parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.SpChampDivisionConfig parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.SpChampDivisionConfig parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.SpChampDivisionConfig parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.SpChampDivisionConfig parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.SpChampDivisionConfig parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.SpChampDivisionConfig parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.SpChampDivisionConfig prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.SpChampDivisionConfig}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.SpChampDivisionConfig)
      xddq.pb.SpChampDivisionConfigOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_SpChampDivisionConfig_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_SpChampDivisionConfig_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.SpChampDivisionConfig.class, xddq.pb.SpChampDivisionConfig.Builder.class);
    }

    // Construct using xddq.pb.SpChampDivisionConfig.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      activityId_ = 0;
      divisionId_ = 0;
      divisionName_ = "";
      minScore_ = 0;
      reward_ = "";
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_SpChampDivisionConfig_descriptor;
    }

    @java.lang.Override
    public xddq.pb.SpChampDivisionConfig getDefaultInstanceForType() {
      return xddq.pb.SpChampDivisionConfig.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.SpChampDivisionConfig build() {
      xddq.pb.SpChampDivisionConfig result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.SpChampDivisionConfig buildPartial() {
      xddq.pb.SpChampDivisionConfig result = new xddq.pb.SpChampDivisionConfig(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.SpChampDivisionConfig result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.activityId_ = activityId_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.divisionId_ = divisionId_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.divisionName_ = divisionName_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.minScore_ = minScore_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.reward_ = reward_;
        to_bitField0_ |= 0x00000010;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.SpChampDivisionConfig) {
        return mergeFrom((xddq.pb.SpChampDivisionConfig)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.SpChampDivisionConfig other) {
      if (other == xddq.pb.SpChampDivisionConfig.getDefaultInstance()) return this;
      if (other.hasActivityId()) {
        setActivityId(other.getActivityId());
      }
      if (other.hasDivisionId()) {
        setDivisionId(other.getDivisionId());
      }
      if (other.hasDivisionName()) {
        divisionName_ = other.divisionName_;
        bitField0_ |= 0x00000004;
        onChanged();
      }
      if (other.hasMinScore()) {
        setMinScore(other.getMinScore());
      }
      if (other.hasReward()) {
        reward_ = other.reward_;
        bitField0_ |= 0x00000010;
        onChanged();
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasActivityId()) {
        return false;
      }
      if (!hasDivisionId()) {
        return false;
      }
      if (!hasDivisionName()) {
        return false;
      }
      if (!hasMinScore()) {
        return false;
      }
      if (!hasReward()) {
        return false;
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              activityId_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              divisionId_ = input.readInt32();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 26: {
              divisionName_ = input.readBytes();
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 32: {
              minScore_ = input.readInt32();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            case 42: {
              reward_ = input.readBytes();
              bitField0_ |= 0x00000010;
              break;
            } // case 42
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int activityId_ ;
    /**
     * <code>required int32 activityId = 1;</code>
     * @return Whether the activityId field is set.
     */
    @java.lang.Override
    public boolean hasActivityId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 activityId = 1;</code>
     * @return The activityId.
     */
    @java.lang.Override
    public int getActivityId() {
      return activityId_;
    }
    /**
     * <code>required int32 activityId = 1;</code>
     * @param value The activityId to set.
     * @return This builder for chaining.
     */
    public Builder setActivityId(int value) {

      activityId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 activityId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearActivityId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      activityId_ = 0;
      onChanged();
      return this;
    }

    private int divisionId_ ;
    /**
     * <code>required int32 divisionId = 2;</code>
     * @return Whether the divisionId field is set.
     */
    @java.lang.Override
    public boolean hasDivisionId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>required int32 divisionId = 2;</code>
     * @return The divisionId.
     */
    @java.lang.Override
    public int getDivisionId() {
      return divisionId_;
    }
    /**
     * <code>required int32 divisionId = 2;</code>
     * @param value The divisionId to set.
     * @return This builder for chaining.
     */
    public Builder setDivisionId(int value) {

      divisionId_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 divisionId = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearDivisionId() {
      bitField0_ = (bitField0_ & ~0x00000002);
      divisionId_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object divisionName_ = "";
    /**
     * <code>required string divisionName = 3;</code>
     * @return Whether the divisionName field is set.
     */
    public boolean hasDivisionName() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>required string divisionName = 3;</code>
     * @return The divisionName.
     */
    public java.lang.String getDivisionName() {
      java.lang.Object ref = divisionName_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          divisionName_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>required string divisionName = 3;</code>
     * @return The bytes for divisionName.
     */
    public com.google.protobuf.ByteString
        getDivisionNameBytes() {
      java.lang.Object ref = divisionName_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        divisionName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>required string divisionName = 3;</code>
     * @param value The divisionName to set.
     * @return This builder for chaining.
     */
    public Builder setDivisionName(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      divisionName_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>required string divisionName = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearDivisionName() {
      divisionName_ = getDefaultInstance().getDivisionName();
      bitField0_ = (bitField0_ & ~0x00000004);
      onChanged();
      return this;
    }
    /**
     * <code>required string divisionName = 3;</code>
     * @param value The bytes for divisionName to set.
     * @return This builder for chaining.
     */
    public Builder setDivisionNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      divisionName_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }

    private int minScore_ ;
    /**
     * <code>required int32 minScore = 4;</code>
     * @return Whether the minScore field is set.
     */
    @java.lang.Override
    public boolean hasMinScore() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>required int32 minScore = 4;</code>
     * @return The minScore.
     */
    @java.lang.Override
    public int getMinScore() {
      return minScore_;
    }
    /**
     * <code>required int32 minScore = 4;</code>
     * @param value The minScore to set.
     * @return This builder for chaining.
     */
    public Builder setMinScore(int value) {

      minScore_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 minScore = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearMinScore() {
      bitField0_ = (bitField0_ & ~0x00000008);
      minScore_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object reward_ = "";
    /**
     * <code>required string reward = 5;</code>
     * @return Whether the reward field is set.
     */
    public boolean hasReward() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>required string reward = 5;</code>
     * @return The reward.
     */
    public java.lang.String getReward() {
      java.lang.Object ref = reward_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          reward_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>required string reward = 5;</code>
     * @return The bytes for reward.
     */
    public com.google.protobuf.ByteString
        getRewardBytes() {
      java.lang.Object ref = reward_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        reward_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>required string reward = 5;</code>
     * @param value The reward to set.
     * @return This builder for chaining.
     */
    public Builder setReward(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      reward_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>required string reward = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearReward() {
      reward_ = getDefaultInstance().getReward();
      bitField0_ = (bitField0_ & ~0x00000010);
      onChanged();
      return this;
    }
    /**
     * <code>required string reward = 5;</code>
     * @param value The bytes for reward to set.
     * @return This builder for chaining.
     */
    public Builder setRewardBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      reward_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.SpChampDivisionConfig)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.SpChampDivisionConfig)
  private static final xddq.pb.SpChampDivisionConfig DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.SpChampDivisionConfig();
  }

  public static xddq.pb.SpChampDivisionConfig getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<SpChampDivisionConfig>
      PARSER = new com.google.protobuf.AbstractParser<SpChampDivisionConfig>() {
    @java.lang.Override
    public SpChampDivisionConfig parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<SpChampDivisionConfig> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<SpChampDivisionConfig> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.SpChampDivisionConfig getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

