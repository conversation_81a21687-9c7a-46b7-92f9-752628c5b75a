// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.PharmacyMatchRankRespMsg}
 */
public final class PharmacyMatchRankRespMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.PharmacyMatchRankRespMsg)
    PharmacyMatchRankRespMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      PharmacyMatchRankRespMsg.class.getName());
  }
  // Use PharmacyMatchRankRespMsg.newBuilder() to construct.
  private PharmacyMatchRankRespMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private PharmacyMatchRankRespMsg() {
    rankList_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_PharmacyMatchRankRespMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_PharmacyMatchRankRespMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.PharmacyMatchRankRespMsg.class, xddq.pb.PharmacyMatchRankRespMsg.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>required int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int RANKLIST_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.PharmacyMatchRankTempMsg> rankList_;
  /**
   * <code>repeated .xddq.pb.PharmacyMatchRankTempMsg rankList = 2;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.PharmacyMatchRankTempMsg> getRankListList() {
    return rankList_;
  }
  /**
   * <code>repeated .xddq.pb.PharmacyMatchRankTempMsg rankList = 2;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.PharmacyMatchRankTempMsgOrBuilder> 
      getRankListOrBuilderList() {
    return rankList_;
  }
  /**
   * <code>repeated .xddq.pb.PharmacyMatchRankTempMsg rankList = 2;</code>
   */
  @java.lang.Override
  public int getRankListCount() {
    return rankList_.size();
  }
  /**
   * <code>repeated .xddq.pb.PharmacyMatchRankTempMsg rankList = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.PharmacyMatchRankTempMsg getRankList(int index) {
    return rankList_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.PharmacyMatchRankTempMsg rankList = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.PharmacyMatchRankTempMsgOrBuilder getRankListOrBuilder(
      int index) {
    return rankList_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasRet()) {
      memoizedIsInitialized = 0;
      return false;
    }
    for (int i = 0; i < getRankListCount(); i++) {
      if (!getRankList(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    for (int i = 0; i < rankList_.size(); i++) {
      output.writeMessage(2, rankList_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    for (int i = 0; i < rankList_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, rankList_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.PharmacyMatchRankRespMsg)) {
      return super.equals(obj);
    }
    xddq.pb.PharmacyMatchRankRespMsg other = (xddq.pb.PharmacyMatchRankRespMsg) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (!getRankListList()
        .equals(other.getRankListList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (getRankListCount() > 0) {
      hash = (37 * hash) + RANKLIST_FIELD_NUMBER;
      hash = (53 * hash) + getRankListList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.PharmacyMatchRankRespMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PharmacyMatchRankRespMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PharmacyMatchRankRespMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PharmacyMatchRankRespMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PharmacyMatchRankRespMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.PharmacyMatchRankRespMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.PharmacyMatchRankRespMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.PharmacyMatchRankRespMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.PharmacyMatchRankRespMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.PharmacyMatchRankRespMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.PharmacyMatchRankRespMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.PharmacyMatchRankRespMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.PharmacyMatchRankRespMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.PharmacyMatchRankRespMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.PharmacyMatchRankRespMsg)
      xddq.pb.PharmacyMatchRankRespMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PharmacyMatchRankRespMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PharmacyMatchRankRespMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.PharmacyMatchRankRespMsg.class, xddq.pb.PharmacyMatchRankRespMsg.Builder.class);
    }

    // Construct using xddq.pb.PharmacyMatchRankRespMsg.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      if (rankListBuilder_ == null) {
        rankList_ = java.util.Collections.emptyList();
      } else {
        rankList_ = null;
        rankListBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000002);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_PharmacyMatchRankRespMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.PharmacyMatchRankRespMsg getDefaultInstanceForType() {
      return xddq.pb.PharmacyMatchRankRespMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.PharmacyMatchRankRespMsg build() {
      xddq.pb.PharmacyMatchRankRespMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.PharmacyMatchRankRespMsg buildPartial() {
      xddq.pb.PharmacyMatchRankRespMsg result = new xddq.pb.PharmacyMatchRankRespMsg(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.PharmacyMatchRankRespMsg result) {
      if (rankListBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          rankList_ = java.util.Collections.unmodifiableList(rankList_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.rankList_ = rankList_;
      } else {
        result.rankList_ = rankListBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.PharmacyMatchRankRespMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.PharmacyMatchRankRespMsg) {
        return mergeFrom((xddq.pb.PharmacyMatchRankRespMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.PharmacyMatchRankRespMsg other) {
      if (other == xddq.pb.PharmacyMatchRankRespMsg.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (rankListBuilder_ == null) {
        if (!other.rankList_.isEmpty()) {
          if (rankList_.isEmpty()) {
            rankList_ = other.rankList_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureRankListIsMutable();
            rankList_.addAll(other.rankList_);
          }
          onChanged();
        }
      } else {
        if (!other.rankList_.isEmpty()) {
          if (rankListBuilder_.isEmpty()) {
            rankListBuilder_.dispose();
            rankListBuilder_ = null;
            rankList_ = other.rankList_;
            bitField0_ = (bitField0_ & ~0x00000002);
            rankListBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetRankListFieldBuilder() : null;
          } else {
            rankListBuilder_.addAllMessages(other.rankList_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasRet()) {
        return false;
      }
      for (int i = 0; i < getRankListCount(); i++) {
        if (!getRankList(i).isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              xddq.pb.PharmacyMatchRankTempMsg m =
                  input.readMessage(
                      xddq.pb.PharmacyMatchRankTempMsg.parser(),
                      extensionRegistry);
              if (rankListBuilder_ == null) {
                ensureRankListIsMutable();
                rankList_.add(m);
              } else {
                rankListBuilder_.addMessage(m);
              }
              break;
            } // case 18
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>required int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.PharmacyMatchRankTempMsg> rankList_ =
      java.util.Collections.emptyList();
    private void ensureRankListIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        rankList_ = new java.util.ArrayList<xddq.pb.PharmacyMatchRankTempMsg>(rankList_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.PharmacyMatchRankTempMsg, xddq.pb.PharmacyMatchRankTempMsg.Builder, xddq.pb.PharmacyMatchRankTempMsgOrBuilder> rankListBuilder_;

    /**
     * <code>repeated .xddq.pb.PharmacyMatchRankTempMsg rankList = 2;</code>
     */
    public java.util.List<xddq.pb.PharmacyMatchRankTempMsg> getRankListList() {
      if (rankListBuilder_ == null) {
        return java.util.Collections.unmodifiableList(rankList_);
      } else {
        return rankListBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.PharmacyMatchRankTempMsg rankList = 2;</code>
     */
    public int getRankListCount() {
      if (rankListBuilder_ == null) {
        return rankList_.size();
      } else {
        return rankListBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.PharmacyMatchRankTempMsg rankList = 2;</code>
     */
    public xddq.pb.PharmacyMatchRankTempMsg getRankList(int index) {
      if (rankListBuilder_ == null) {
        return rankList_.get(index);
      } else {
        return rankListBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.PharmacyMatchRankTempMsg rankList = 2;</code>
     */
    public Builder setRankList(
        int index, xddq.pb.PharmacyMatchRankTempMsg value) {
      if (rankListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRankListIsMutable();
        rankList_.set(index, value);
        onChanged();
      } else {
        rankListBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PharmacyMatchRankTempMsg rankList = 2;</code>
     */
    public Builder setRankList(
        int index, xddq.pb.PharmacyMatchRankTempMsg.Builder builderForValue) {
      if (rankListBuilder_ == null) {
        ensureRankListIsMutable();
        rankList_.set(index, builderForValue.build());
        onChanged();
      } else {
        rankListBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PharmacyMatchRankTempMsg rankList = 2;</code>
     */
    public Builder addRankList(xddq.pb.PharmacyMatchRankTempMsg value) {
      if (rankListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRankListIsMutable();
        rankList_.add(value);
        onChanged();
      } else {
        rankListBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PharmacyMatchRankTempMsg rankList = 2;</code>
     */
    public Builder addRankList(
        int index, xddq.pb.PharmacyMatchRankTempMsg value) {
      if (rankListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRankListIsMutable();
        rankList_.add(index, value);
        onChanged();
      } else {
        rankListBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PharmacyMatchRankTempMsg rankList = 2;</code>
     */
    public Builder addRankList(
        xddq.pb.PharmacyMatchRankTempMsg.Builder builderForValue) {
      if (rankListBuilder_ == null) {
        ensureRankListIsMutable();
        rankList_.add(builderForValue.build());
        onChanged();
      } else {
        rankListBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PharmacyMatchRankTempMsg rankList = 2;</code>
     */
    public Builder addRankList(
        int index, xddq.pb.PharmacyMatchRankTempMsg.Builder builderForValue) {
      if (rankListBuilder_ == null) {
        ensureRankListIsMutable();
        rankList_.add(index, builderForValue.build());
        onChanged();
      } else {
        rankListBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PharmacyMatchRankTempMsg rankList = 2;</code>
     */
    public Builder addAllRankList(
        java.lang.Iterable<? extends xddq.pb.PharmacyMatchRankTempMsg> values) {
      if (rankListBuilder_ == null) {
        ensureRankListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, rankList_);
        onChanged();
      } else {
        rankListBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PharmacyMatchRankTempMsg rankList = 2;</code>
     */
    public Builder clearRankList() {
      if (rankListBuilder_ == null) {
        rankList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        rankListBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PharmacyMatchRankTempMsg rankList = 2;</code>
     */
    public Builder removeRankList(int index) {
      if (rankListBuilder_ == null) {
        ensureRankListIsMutable();
        rankList_.remove(index);
        onChanged();
      } else {
        rankListBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.PharmacyMatchRankTempMsg rankList = 2;</code>
     */
    public xddq.pb.PharmacyMatchRankTempMsg.Builder getRankListBuilder(
        int index) {
      return internalGetRankListFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.PharmacyMatchRankTempMsg rankList = 2;</code>
     */
    public xddq.pb.PharmacyMatchRankTempMsgOrBuilder getRankListOrBuilder(
        int index) {
      if (rankListBuilder_ == null) {
        return rankList_.get(index);  } else {
        return rankListBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.PharmacyMatchRankTempMsg rankList = 2;</code>
     */
    public java.util.List<? extends xddq.pb.PharmacyMatchRankTempMsgOrBuilder> 
         getRankListOrBuilderList() {
      if (rankListBuilder_ != null) {
        return rankListBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(rankList_);
      }
    }
    /**
     * <code>repeated .xddq.pb.PharmacyMatchRankTempMsg rankList = 2;</code>
     */
    public xddq.pb.PharmacyMatchRankTempMsg.Builder addRankListBuilder() {
      return internalGetRankListFieldBuilder().addBuilder(
          xddq.pb.PharmacyMatchRankTempMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.PharmacyMatchRankTempMsg rankList = 2;</code>
     */
    public xddq.pb.PharmacyMatchRankTempMsg.Builder addRankListBuilder(
        int index) {
      return internalGetRankListFieldBuilder().addBuilder(
          index, xddq.pb.PharmacyMatchRankTempMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.PharmacyMatchRankTempMsg rankList = 2;</code>
     */
    public java.util.List<xddq.pb.PharmacyMatchRankTempMsg.Builder> 
         getRankListBuilderList() {
      return internalGetRankListFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.PharmacyMatchRankTempMsg, xddq.pb.PharmacyMatchRankTempMsg.Builder, xddq.pb.PharmacyMatchRankTempMsgOrBuilder> 
        internalGetRankListFieldBuilder() {
      if (rankListBuilder_ == null) {
        rankListBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.PharmacyMatchRankTempMsg, xddq.pb.PharmacyMatchRankTempMsg.Builder, xddq.pb.PharmacyMatchRankTempMsgOrBuilder>(
                rankList_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        rankList_ = null;
      }
      return rankListBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.PharmacyMatchRankRespMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.PharmacyMatchRankRespMsg)
  private static final xddq.pb.PharmacyMatchRankRespMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.PharmacyMatchRankRespMsg();
  }

  public static xddq.pb.PharmacyMatchRankRespMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<PharmacyMatchRankRespMsg>
      PARSER = new com.google.protobuf.AbstractParser<PharmacyMatchRankRespMsg>() {
    @java.lang.Override
    public PharmacyMatchRankRespMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<PharmacyMatchRankRespMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<PharmacyMatchRankRespMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.PharmacyMatchRankRespMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

