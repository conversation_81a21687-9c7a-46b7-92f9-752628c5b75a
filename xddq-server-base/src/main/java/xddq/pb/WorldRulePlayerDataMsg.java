// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.WorldRulePlayerDataMsg}
 */
public final class WorldRulePlayerDataMsg extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.WorldRulePlayerDataMsg)
    WorldRulePlayerDataMsgOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      WorldRulePlayerDataMsg.class.getName());
  }
  // Use WorldRulePlayerDataMsg.newBuilder() to construct.
  private WorldRulePlayerDataMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private WorldRulePlayerDataMsg() {
    ruleListMsg_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_WorldRulePlayerDataMsg_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_WorldRulePlayerDataMsg_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.WorldRulePlayerDataMsg.class, xddq.pb.WorldRulePlayerDataMsg.Builder.class);
  }

  private int bitField0_;
  public static final int RULELISTMSG_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.WorldRuleDetailsMsg> ruleListMsg_;
  /**
   * <code>repeated .xddq.pb.WorldRuleDetailsMsg ruleListMsg = 1;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.WorldRuleDetailsMsg> getRuleListMsgList() {
    return ruleListMsg_;
  }
  /**
   * <code>repeated .xddq.pb.WorldRuleDetailsMsg ruleListMsg = 1;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.WorldRuleDetailsMsgOrBuilder> 
      getRuleListMsgOrBuilderList() {
    return ruleListMsg_;
  }
  /**
   * <code>repeated .xddq.pb.WorldRuleDetailsMsg ruleListMsg = 1;</code>
   */
  @java.lang.Override
  public int getRuleListMsgCount() {
    return ruleListMsg_.size();
  }
  /**
   * <code>repeated .xddq.pb.WorldRuleDetailsMsg ruleListMsg = 1;</code>
   */
  @java.lang.Override
  public xddq.pb.WorldRuleDetailsMsg getRuleListMsg(int index) {
    return ruleListMsg_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.WorldRuleDetailsMsg ruleListMsg = 1;</code>
   */
  @java.lang.Override
  public xddq.pb.WorldRuleDetailsMsgOrBuilder getRuleListMsgOrBuilder(
      int index) {
    return ruleListMsg_.get(index);
  }

  public static final int CURRULEPROGRAMMEIDX_FIELD_NUMBER = 2;
  private int curRuleProgrammeIdx_ = 0;
  /**
   * <code>optional int32 curRuleProgrammeIdx = 2;</code>
   * @return Whether the curRuleProgrammeIdx field is set.
   */
  @java.lang.Override
  public boolean hasCurRuleProgrammeIdx() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int32 curRuleProgrammeIdx = 2;</code>
   * @return The curRuleProgrammeIdx.
   */
  @java.lang.Override
  public int getCurRuleProgrammeIdx() {
    return curRuleProgrammeIdx_;
  }

  public static final int ADTIMES_FIELD_NUMBER = 3;
  private int adTimes_ = 0;
  /**
   * <code>optional int32 adTimes = 3;</code>
   * @return Whether the adTimes field is set.
   */
  @java.lang.Override
  public boolean hasAdTimes() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int32 adTimes = 3;</code>
   * @return The adTimes.
   */
  @java.lang.Override
  public int getAdTimes() {
    return adTimes_;
  }

  public static final int BASEATT_FIELD_NUMBER = 4;
  private xddq.pb.SkillMsg baseAtt_;
  /**
   * <code>optional .xddq.pb.SkillMsg baseAtt = 4;</code>
   * @return Whether the baseAtt field is set.
   */
  @java.lang.Override
  public boolean hasBaseAtt() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional .xddq.pb.SkillMsg baseAtt = 4;</code>
   * @return The baseAtt.
   */
  @java.lang.Override
  public xddq.pb.SkillMsg getBaseAtt() {
    return baseAtt_ == null ? xddq.pb.SkillMsg.getDefaultInstance() : baseAtt_;
  }
  /**
   * <code>optional .xddq.pb.SkillMsg baseAtt = 4;</code>
   */
  @java.lang.Override
  public xddq.pb.SkillMsgOrBuilder getBaseAttOrBuilder() {
    return baseAtt_ == null ? xddq.pb.SkillMsg.getDefaultInstance() : baseAtt_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    for (int i = 0; i < getRuleListMsgCount(); i++) {
      if (!getRuleListMsg(i).isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    if (hasBaseAtt()) {
      if (!getBaseAtt().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    for (int i = 0; i < ruleListMsg_.size(); i++) {
      output.writeMessage(1, ruleListMsg_.get(i));
    }
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(2, curRuleProgrammeIdx_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(3, adTimes_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeMessage(4, getBaseAtt());
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    for (int i = 0; i < ruleListMsg_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, ruleListMsg_.get(i));
    }
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, curRuleProgrammeIdx_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(3, adTimes_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(4, getBaseAtt());
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.WorldRulePlayerDataMsg)) {
      return super.equals(obj);
    }
    xddq.pb.WorldRulePlayerDataMsg other = (xddq.pb.WorldRulePlayerDataMsg) obj;

    if (!getRuleListMsgList()
        .equals(other.getRuleListMsgList())) return false;
    if (hasCurRuleProgrammeIdx() != other.hasCurRuleProgrammeIdx()) return false;
    if (hasCurRuleProgrammeIdx()) {
      if (getCurRuleProgrammeIdx()
          != other.getCurRuleProgrammeIdx()) return false;
    }
    if (hasAdTimes() != other.hasAdTimes()) return false;
    if (hasAdTimes()) {
      if (getAdTimes()
          != other.getAdTimes()) return false;
    }
    if (hasBaseAtt() != other.hasBaseAtt()) return false;
    if (hasBaseAtt()) {
      if (!getBaseAtt()
          .equals(other.getBaseAtt())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (getRuleListMsgCount() > 0) {
      hash = (37 * hash) + RULELISTMSG_FIELD_NUMBER;
      hash = (53 * hash) + getRuleListMsgList().hashCode();
    }
    if (hasCurRuleProgrammeIdx()) {
      hash = (37 * hash) + CURRULEPROGRAMMEIDX_FIELD_NUMBER;
      hash = (53 * hash) + getCurRuleProgrammeIdx();
    }
    if (hasAdTimes()) {
      hash = (37 * hash) + ADTIMES_FIELD_NUMBER;
      hash = (53 * hash) + getAdTimes();
    }
    if (hasBaseAtt()) {
      hash = (37 * hash) + BASEATT_FIELD_NUMBER;
      hash = (53 * hash) + getBaseAtt().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.WorldRulePlayerDataMsg parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.WorldRulePlayerDataMsg parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.WorldRulePlayerDataMsg parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.WorldRulePlayerDataMsg parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.WorldRulePlayerDataMsg parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.WorldRulePlayerDataMsg parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.WorldRulePlayerDataMsg parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.WorldRulePlayerDataMsg parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.WorldRulePlayerDataMsg parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.WorldRulePlayerDataMsg parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.WorldRulePlayerDataMsg parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.WorldRulePlayerDataMsg parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.WorldRulePlayerDataMsg prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.WorldRulePlayerDataMsg}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.WorldRulePlayerDataMsg)
      xddq.pb.WorldRulePlayerDataMsgOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_WorldRulePlayerDataMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_WorldRulePlayerDataMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.WorldRulePlayerDataMsg.class, xddq.pb.WorldRulePlayerDataMsg.Builder.class);
    }

    // Construct using xddq.pb.WorldRulePlayerDataMsg.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetRuleListMsgFieldBuilder();
        internalGetBaseAttFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      if (ruleListMsgBuilder_ == null) {
        ruleListMsg_ = java.util.Collections.emptyList();
      } else {
        ruleListMsg_ = null;
        ruleListMsgBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000001);
      curRuleProgrammeIdx_ = 0;
      adTimes_ = 0;
      baseAtt_ = null;
      if (baseAttBuilder_ != null) {
        baseAttBuilder_.dispose();
        baseAttBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_WorldRulePlayerDataMsg_descriptor;
    }

    @java.lang.Override
    public xddq.pb.WorldRulePlayerDataMsg getDefaultInstanceForType() {
      return xddq.pb.WorldRulePlayerDataMsg.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.WorldRulePlayerDataMsg build() {
      xddq.pb.WorldRulePlayerDataMsg result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.WorldRulePlayerDataMsg buildPartial() {
      xddq.pb.WorldRulePlayerDataMsg result = new xddq.pb.WorldRulePlayerDataMsg(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.WorldRulePlayerDataMsg result) {
      if (ruleListMsgBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          ruleListMsg_ = java.util.Collections.unmodifiableList(ruleListMsg_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.ruleListMsg_ = ruleListMsg_;
      } else {
        result.ruleListMsg_ = ruleListMsgBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.WorldRulePlayerDataMsg result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.curRuleProgrammeIdx_ = curRuleProgrammeIdx_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.adTimes_ = adTimes_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.baseAtt_ = baseAttBuilder_ == null
            ? baseAtt_
            : baseAttBuilder_.build();
        to_bitField0_ |= 0x00000004;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.WorldRulePlayerDataMsg) {
        return mergeFrom((xddq.pb.WorldRulePlayerDataMsg)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.WorldRulePlayerDataMsg other) {
      if (other == xddq.pb.WorldRulePlayerDataMsg.getDefaultInstance()) return this;
      if (ruleListMsgBuilder_ == null) {
        if (!other.ruleListMsg_.isEmpty()) {
          if (ruleListMsg_.isEmpty()) {
            ruleListMsg_ = other.ruleListMsg_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureRuleListMsgIsMutable();
            ruleListMsg_.addAll(other.ruleListMsg_);
          }
          onChanged();
        }
      } else {
        if (!other.ruleListMsg_.isEmpty()) {
          if (ruleListMsgBuilder_.isEmpty()) {
            ruleListMsgBuilder_.dispose();
            ruleListMsgBuilder_ = null;
            ruleListMsg_ = other.ruleListMsg_;
            bitField0_ = (bitField0_ & ~0x00000001);
            ruleListMsgBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetRuleListMsgFieldBuilder() : null;
          } else {
            ruleListMsgBuilder_.addAllMessages(other.ruleListMsg_);
          }
        }
      }
      if (other.hasCurRuleProgrammeIdx()) {
        setCurRuleProgrammeIdx(other.getCurRuleProgrammeIdx());
      }
      if (other.hasAdTimes()) {
        setAdTimes(other.getAdTimes());
      }
      if (other.hasBaseAtt()) {
        mergeBaseAtt(other.getBaseAtt());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      for (int i = 0; i < getRuleListMsgCount(); i++) {
        if (!getRuleListMsg(i).isInitialized()) {
          return false;
        }
      }
      if (hasBaseAtt()) {
        if (!getBaseAtt().isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              xddq.pb.WorldRuleDetailsMsg m =
                  input.readMessage(
                      xddq.pb.WorldRuleDetailsMsg.parser(),
                      extensionRegistry);
              if (ruleListMsgBuilder_ == null) {
                ensureRuleListMsgIsMutable();
                ruleListMsg_.add(m);
              } else {
                ruleListMsgBuilder_.addMessage(m);
              }
              break;
            } // case 10
            case 16: {
              curRuleProgrammeIdx_ = input.readInt32();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 24: {
              adTimes_ = input.readInt32();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 34: {
              input.readMessage(
                  internalGetBaseAttFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000008;
              break;
            } // case 34
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.util.List<xddq.pb.WorldRuleDetailsMsg> ruleListMsg_ =
      java.util.Collections.emptyList();
    private void ensureRuleListMsgIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        ruleListMsg_ = new java.util.ArrayList<xddq.pb.WorldRuleDetailsMsg>(ruleListMsg_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.WorldRuleDetailsMsg, xddq.pb.WorldRuleDetailsMsg.Builder, xddq.pb.WorldRuleDetailsMsgOrBuilder> ruleListMsgBuilder_;

    /**
     * <code>repeated .xddq.pb.WorldRuleDetailsMsg ruleListMsg = 1;</code>
     */
    public java.util.List<xddq.pb.WorldRuleDetailsMsg> getRuleListMsgList() {
      if (ruleListMsgBuilder_ == null) {
        return java.util.Collections.unmodifiableList(ruleListMsg_);
      } else {
        return ruleListMsgBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.WorldRuleDetailsMsg ruleListMsg = 1;</code>
     */
    public int getRuleListMsgCount() {
      if (ruleListMsgBuilder_ == null) {
        return ruleListMsg_.size();
      } else {
        return ruleListMsgBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.WorldRuleDetailsMsg ruleListMsg = 1;</code>
     */
    public xddq.pb.WorldRuleDetailsMsg getRuleListMsg(int index) {
      if (ruleListMsgBuilder_ == null) {
        return ruleListMsg_.get(index);
      } else {
        return ruleListMsgBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.WorldRuleDetailsMsg ruleListMsg = 1;</code>
     */
    public Builder setRuleListMsg(
        int index, xddq.pb.WorldRuleDetailsMsg value) {
      if (ruleListMsgBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRuleListMsgIsMutable();
        ruleListMsg_.set(index, value);
        onChanged();
      } else {
        ruleListMsgBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WorldRuleDetailsMsg ruleListMsg = 1;</code>
     */
    public Builder setRuleListMsg(
        int index, xddq.pb.WorldRuleDetailsMsg.Builder builderForValue) {
      if (ruleListMsgBuilder_ == null) {
        ensureRuleListMsgIsMutable();
        ruleListMsg_.set(index, builderForValue.build());
        onChanged();
      } else {
        ruleListMsgBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WorldRuleDetailsMsg ruleListMsg = 1;</code>
     */
    public Builder addRuleListMsg(xddq.pb.WorldRuleDetailsMsg value) {
      if (ruleListMsgBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRuleListMsgIsMutable();
        ruleListMsg_.add(value);
        onChanged();
      } else {
        ruleListMsgBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WorldRuleDetailsMsg ruleListMsg = 1;</code>
     */
    public Builder addRuleListMsg(
        int index, xddq.pb.WorldRuleDetailsMsg value) {
      if (ruleListMsgBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureRuleListMsgIsMutable();
        ruleListMsg_.add(index, value);
        onChanged();
      } else {
        ruleListMsgBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WorldRuleDetailsMsg ruleListMsg = 1;</code>
     */
    public Builder addRuleListMsg(
        xddq.pb.WorldRuleDetailsMsg.Builder builderForValue) {
      if (ruleListMsgBuilder_ == null) {
        ensureRuleListMsgIsMutable();
        ruleListMsg_.add(builderForValue.build());
        onChanged();
      } else {
        ruleListMsgBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WorldRuleDetailsMsg ruleListMsg = 1;</code>
     */
    public Builder addRuleListMsg(
        int index, xddq.pb.WorldRuleDetailsMsg.Builder builderForValue) {
      if (ruleListMsgBuilder_ == null) {
        ensureRuleListMsgIsMutable();
        ruleListMsg_.add(index, builderForValue.build());
        onChanged();
      } else {
        ruleListMsgBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WorldRuleDetailsMsg ruleListMsg = 1;</code>
     */
    public Builder addAllRuleListMsg(
        java.lang.Iterable<? extends xddq.pb.WorldRuleDetailsMsg> values) {
      if (ruleListMsgBuilder_ == null) {
        ensureRuleListMsgIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, ruleListMsg_);
        onChanged();
      } else {
        ruleListMsgBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WorldRuleDetailsMsg ruleListMsg = 1;</code>
     */
    public Builder clearRuleListMsg() {
      if (ruleListMsgBuilder_ == null) {
        ruleListMsg_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        ruleListMsgBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WorldRuleDetailsMsg ruleListMsg = 1;</code>
     */
    public Builder removeRuleListMsg(int index) {
      if (ruleListMsgBuilder_ == null) {
        ensureRuleListMsgIsMutable();
        ruleListMsg_.remove(index);
        onChanged();
      } else {
        ruleListMsgBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.WorldRuleDetailsMsg ruleListMsg = 1;</code>
     */
    public xddq.pb.WorldRuleDetailsMsg.Builder getRuleListMsgBuilder(
        int index) {
      return internalGetRuleListMsgFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.WorldRuleDetailsMsg ruleListMsg = 1;</code>
     */
    public xddq.pb.WorldRuleDetailsMsgOrBuilder getRuleListMsgOrBuilder(
        int index) {
      if (ruleListMsgBuilder_ == null) {
        return ruleListMsg_.get(index);  } else {
        return ruleListMsgBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.WorldRuleDetailsMsg ruleListMsg = 1;</code>
     */
    public java.util.List<? extends xddq.pb.WorldRuleDetailsMsgOrBuilder> 
         getRuleListMsgOrBuilderList() {
      if (ruleListMsgBuilder_ != null) {
        return ruleListMsgBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(ruleListMsg_);
      }
    }
    /**
     * <code>repeated .xddq.pb.WorldRuleDetailsMsg ruleListMsg = 1;</code>
     */
    public xddq.pb.WorldRuleDetailsMsg.Builder addRuleListMsgBuilder() {
      return internalGetRuleListMsgFieldBuilder().addBuilder(
          xddq.pb.WorldRuleDetailsMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.WorldRuleDetailsMsg ruleListMsg = 1;</code>
     */
    public xddq.pb.WorldRuleDetailsMsg.Builder addRuleListMsgBuilder(
        int index) {
      return internalGetRuleListMsgFieldBuilder().addBuilder(
          index, xddq.pb.WorldRuleDetailsMsg.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.WorldRuleDetailsMsg ruleListMsg = 1;</code>
     */
    public java.util.List<xddq.pb.WorldRuleDetailsMsg.Builder> 
         getRuleListMsgBuilderList() {
      return internalGetRuleListMsgFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.WorldRuleDetailsMsg, xddq.pb.WorldRuleDetailsMsg.Builder, xddq.pb.WorldRuleDetailsMsgOrBuilder> 
        internalGetRuleListMsgFieldBuilder() {
      if (ruleListMsgBuilder_ == null) {
        ruleListMsgBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.WorldRuleDetailsMsg, xddq.pb.WorldRuleDetailsMsg.Builder, xddq.pb.WorldRuleDetailsMsgOrBuilder>(
                ruleListMsg_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        ruleListMsg_ = null;
      }
      return ruleListMsgBuilder_;
    }

    private int curRuleProgrammeIdx_ ;
    /**
     * <code>optional int32 curRuleProgrammeIdx = 2;</code>
     * @return Whether the curRuleProgrammeIdx field is set.
     */
    @java.lang.Override
    public boolean hasCurRuleProgrammeIdx() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 curRuleProgrammeIdx = 2;</code>
     * @return The curRuleProgrammeIdx.
     */
    @java.lang.Override
    public int getCurRuleProgrammeIdx() {
      return curRuleProgrammeIdx_;
    }
    /**
     * <code>optional int32 curRuleProgrammeIdx = 2;</code>
     * @param value The curRuleProgrammeIdx to set.
     * @return This builder for chaining.
     */
    public Builder setCurRuleProgrammeIdx(int value) {

      curRuleProgrammeIdx_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 curRuleProgrammeIdx = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearCurRuleProgrammeIdx() {
      bitField0_ = (bitField0_ & ~0x00000002);
      curRuleProgrammeIdx_ = 0;
      onChanged();
      return this;
    }

    private int adTimes_ ;
    /**
     * <code>optional int32 adTimes = 3;</code>
     * @return Whether the adTimes field is set.
     */
    @java.lang.Override
    public boolean hasAdTimes() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int32 adTimes = 3;</code>
     * @return The adTimes.
     */
    @java.lang.Override
    public int getAdTimes() {
      return adTimes_;
    }
    /**
     * <code>optional int32 adTimes = 3;</code>
     * @param value The adTimes to set.
     * @return This builder for chaining.
     */
    public Builder setAdTimes(int value) {

      adTimes_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 adTimes = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearAdTimes() {
      bitField0_ = (bitField0_ & ~0x00000004);
      adTimes_ = 0;
      onChanged();
      return this;
    }

    private xddq.pb.SkillMsg baseAtt_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.SkillMsg, xddq.pb.SkillMsg.Builder, xddq.pb.SkillMsgOrBuilder> baseAttBuilder_;
    /**
     * <code>optional .xddq.pb.SkillMsg baseAtt = 4;</code>
     * @return Whether the baseAtt field is set.
     */
    public boolean hasBaseAtt() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional .xddq.pb.SkillMsg baseAtt = 4;</code>
     * @return The baseAtt.
     */
    public xddq.pb.SkillMsg getBaseAtt() {
      if (baseAttBuilder_ == null) {
        return baseAtt_ == null ? xddq.pb.SkillMsg.getDefaultInstance() : baseAtt_;
      } else {
        return baseAttBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.SkillMsg baseAtt = 4;</code>
     */
    public Builder setBaseAtt(xddq.pb.SkillMsg value) {
      if (baseAttBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        baseAtt_ = value;
      } else {
        baseAttBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.SkillMsg baseAtt = 4;</code>
     */
    public Builder setBaseAtt(
        xddq.pb.SkillMsg.Builder builderForValue) {
      if (baseAttBuilder_ == null) {
        baseAtt_ = builderForValue.build();
      } else {
        baseAttBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.SkillMsg baseAtt = 4;</code>
     */
    public Builder mergeBaseAtt(xddq.pb.SkillMsg value) {
      if (baseAttBuilder_ == null) {
        if (((bitField0_ & 0x00000008) != 0) &&
          baseAtt_ != null &&
          baseAtt_ != xddq.pb.SkillMsg.getDefaultInstance()) {
          getBaseAttBuilder().mergeFrom(value);
        } else {
          baseAtt_ = value;
        }
      } else {
        baseAttBuilder_.mergeFrom(value);
      }
      if (baseAtt_ != null) {
        bitField0_ |= 0x00000008;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.SkillMsg baseAtt = 4;</code>
     */
    public Builder clearBaseAtt() {
      bitField0_ = (bitField0_ & ~0x00000008);
      baseAtt_ = null;
      if (baseAttBuilder_ != null) {
        baseAttBuilder_.dispose();
        baseAttBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.SkillMsg baseAtt = 4;</code>
     */
    public xddq.pb.SkillMsg.Builder getBaseAttBuilder() {
      bitField0_ |= 0x00000008;
      onChanged();
      return internalGetBaseAttFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.SkillMsg baseAtt = 4;</code>
     */
    public xddq.pb.SkillMsgOrBuilder getBaseAttOrBuilder() {
      if (baseAttBuilder_ != null) {
        return baseAttBuilder_.getMessageOrBuilder();
      } else {
        return baseAtt_ == null ?
            xddq.pb.SkillMsg.getDefaultInstance() : baseAtt_;
      }
    }
    /**
     * <code>optional .xddq.pb.SkillMsg baseAtt = 4;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.SkillMsg, xddq.pb.SkillMsg.Builder, xddq.pb.SkillMsgOrBuilder> 
        internalGetBaseAttFieldBuilder() {
      if (baseAttBuilder_ == null) {
        baseAttBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.SkillMsg, xddq.pb.SkillMsg.Builder, xddq.pb.SkillMsgOrBuilder>(
                getBaseAtt(),
                getParentForChildren(),
                isClean());
        baseAtt_ = null;
      }
      return baseAttBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.WorldRulePlayerDataMsg)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.WorldRulePlayerDataMsg)
  private static final xddq.pb.WorldRulePlayerDataMsg DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.WorldRulePlayerDataMsg();
  }

  public static xddq.pb.WorldRulePlayerDataMsg getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<WorldRulePlayerDataMsg>
      PARSER = new com.google.protobuf.AbstractParser<WorldRulePlayerDataMsg>() {
    @java.lang.Override
    public WorldRulePlayerDataMsg parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<WorldRulePlayerDataMsg> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<WorldRulePlayerDataMsg> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.WorldRulePlayerDataMsg getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

