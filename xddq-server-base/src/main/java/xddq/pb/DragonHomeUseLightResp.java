// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.DragonHomeUseLightResp}
 */
public final class DragonHomeUseLightResp extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.DragonHomeUseLightResp)
    DragonHomeUseLightRespOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      DragonHomeUseLightResp.class.getName());
  }
  // Use DragonHomeUseLightResp.newBuilder() to construct.
  private DragonHomeUseLightResp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private DragonHomeUseLightResp() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_DragonHomeUseLightResp_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_DragonHomeUseLightResp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.DragonHomeUseLightResp.class, xddq.pb.DragonHomeUseLightResp.Builder.class);
  }

  private int bitField0_;
  public static final int RET_FIELD_NUMBER = 1;
  private int ret_ = 0;
  /**
   * <code>optional int32 ret = 1;</code>
   * @return Whether the ret field is set.
   */
  @java.lang.Override
  public boolean hasRet() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int32 ret = 1;</code>
   * @return The ret.
   */
  @java.lang.Override
  public int getRet() {
    return ret_;
  }

  public static final int NOTIFYDATA_FIELD_NUMBER = 2;
  private xddq.pb.DragonHomeExploreNotifyDataMsg notifyData_;
  /**
   * <code>optional .xddq.pb.DragonHomeExploreNotifyDataMsg notifyData = 2;</code>
   * @return Whether the notifyData field is set.
   */
  @java.lang.Override
  public boolean hasNotifyData() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional .xddq.pb.DragonHomeExploreNotifyDataMsg notifyData = 2;</code>
   * @return The notifyData.
   */
  @java.lang.Override
  public xddq.pb.DragonHomeExploreNotifyDataMsg getNotifyData() {
    return notifyData_ == null ? xddq.pb.DragonHomeExploreNotifyDataMsg.getDefaultInstance() : notifyData_;
  }
  /**
   * <code>optional .xddq.pb.DragonHomeExploreNotifyDataMsg notifyData = 2;</code>
   */
  @java.lang.Override
  public xddq.pb.DragonHomeExploreNotifyDataMsgOrBuilder getNotifyDataOrBuilder() {
    return notifyData_ == null ? xddq.pb.DragonHomeExploreNotifyDataMsg.getDefaultInstance() : notifyData_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (hasNotifyData()) {
      if (!getNotifyData().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeMessage(2, getNotifyData());
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, ret_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getNotifyData());
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.DragonHomeUseLightResp)) {
      return super.equals(obj);
    }
    xddq.pb.DragonHomeUseLightResp other = (xddq.pb.DragonHomeUseLightResp) obj;

    if (hasRet() != other.hasRet()) return false;
    if (hasRet()) {
      if (getRet()
          != other.getRet()) return false;
    }
    if (hasNotifyData() != other.hasNotifyData()) return false;
    if (hasNotifyData()) {
      if (!getNotifyData()
          .equals(other.getNotifyData())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasRet()) {
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
    }
    if (hasNotifyData()) {
      hash = (37 * hash) + NOTIFYDATA_FIELD_NUMBER;
      hash = (53 * hash) + getNotifyData().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.DragonHomeUseLightResp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.DragonHomeUseLightResp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.DragonHomeUseLightResp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.DragonHomeUseLightResp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.DragonHomeUseLightResp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.DragonHomeUseLightResp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.DragonHomeUseLightResp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.DragonHomeUseLightResp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.DragonHomeUseLightResp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.DragonHomeUseLightResp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.DragonHomeUseLightResp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.DragonHomeUseLightResp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.DragonHomeUseLightResp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.DragonHomeUseLightResp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.DragonHomeUseLightResp)
      xddq.pb.DragonHomeUseLightRespOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_DragonHomeUseLightResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_DragonHomeUseLightResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.DragonHomeUseLightResp.class, xddq.pb.DragonHomeUseLightResp.Builder.class);
    }

    // Construct using xddq.pb.DragonHomeUseLightResp.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        internalGetNotifyDataFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      ret_ = 0;
      notifyData_ = null;
      if (notifyDataBuilder_ != null) {
        notifyDataBuilder_.dispose();
        notifyDataBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_DragonHomeUseLightResp_descriptor;
    }

    @java.lang.Override
    public xddq.pb.DragonHomeUseLightResp getDefaultInstanceForType() {
      return xddq.pb.DragonHomeUseLightResp.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.DragonHomeUseLightResp build() {
      xddq.pb.DragonHomeUseLightResp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.DragonHomeUseLightResp buildPartial() {
      xddq.pb.DragonHomeUseLightResp result = new xddq.pb.DragonHomeUseLightResp(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.DragonHomeUseLightResp result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.ret_ = ret_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.notifyData_ = notifyDataBuilder_ == null
            ? notifyData_
            : notifyDataBuilder_.build();
        to_bitField0_ |= 0x00000002;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.DragonHomeUseLightResp) {
        return mergeFrom((xddq.pb.DragonHomeUseLightResp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.DragonHomeUseLightResp other) {
      if (other == xddq.pb.DragonHomeUseLightResp.getDefaultInstance()) return this;
      if (other.hasRet()) {
        setRet(other.getRet());
      }
      if (other.hasNotifyData()) {
        mergeNotifyData(other.getNotifyData());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (hasNotifyData()) {
        if (!getNotifyData().isInitialized()) {
          return false;
        }
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              ret_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              input.readMessage(
                  internalGetNotifyDataFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int ret_ ;
    /**
     * <code>optional int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }
    /**
     * <code>optional int32 ret = 1;</code>
     * @param value The ret to set.
     * @return This builder for chaining.
     */
    public Builder setRet(int value) {

      ret_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 ret = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRet() {
      bitField0_ = (bitField0_ & ~0x00000001);
      ret_ = 0;
      onChanged();
      return this;
    }

    private xddq.pb.DragonHomeExploreNotifyDataMsg notifyData_;
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.DragonHomeExploreNotifyDataMsg, xddq.pb.DragonHomeExploreNotifyDataMsg.Builder, xddq.pb.DragonHomeExploreNotifyDataMsgOrBuilder> notifyDataBuilder_;
    /**
     * <code>optional .xddq.pb.DragonHomeExploreNotifyDataMsg notifyData = 2;</code>
     * @return Whether the notifyData field is set.
     */
    public boolean hasNotifyData() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .xddq.pb.DragonHomeExploreNotifyDataMsg notifyData = 2;</code>
     * @return The notifyData.
     */
    public xddq.pb.DragonHomeExploreNotifyDataMsg getNotifyData() {
      if (notifyDataBuilder_ == null) {
        return notifyData_ == null ? xddq.pb.DragonHomeExploreNotifyDataMsg.getDefaultInstance() : notifyData_;
      } else {
        return notifyDataBuilder_.getMessage();
      }
    }
    /**
     * <code>optional .xddq.pb.DragonHomeExploreNotifyDataMsg notifyData = 2;</code>
     */
    public Builder setNotifyData(xddq.pb.DragonHomeExploreNotifyDataMsg value) {
      if (notifyDataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        notifyData_ = value;
      } else {
        notifyDataBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.DragonHomeExploreNotifyDataMsg notifyData = 2;</code>
     */
    public Builder setNotifyData(
        xddq.pb.DragonHomeExploreNotifyDataMsg.Builder builderForValue) {
      if (notifyDataBuilder_ == null) {
        notifyData_ = builderForValue.build();
      } else {
        notifyDataBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.DragonHomeExploreNotifyDataMsg notifyData = 2;</code>
     */
    public Builder mergeNotifyData(xddq.pb.DragonHomeExploreNotifyDataMsg value) {
      if (notifyDataBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0) &&
          notifyData_ != null &&
          notifyData_ != xddq.pb.DragonHomeExploreNotifyDataMsg.getDefaultInstance()) {
          getNotifyDataBuilder().mergeFrom(value);
        } else {
          notifyData_ = value;
        }
      } else {
        notifyDataBuilder_.mergeFrom(value);
      }
      if (notifyData_ != null) {
        bitField0_ |= 0x00000002;
        onChanged();
      }
      return this;
    }
    /**
     * <code>optional .xddq.pb.DragonHomeExploreNotifyDataMsg notifyData = 2;</code>
     */
    public Builder clearNotifyData() {
      bitField0_ = (bitField0_ & ~0x00000002);
      notifyData_ = null;
      if (notifyDataBuilder_ != null) {
        notifyDataBuilder_.dispose();
        notifyDataBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>optional .xddq.pb.DragonHomeExploreNotifyDataMsg notifyData = 2;</code>
     */
    public xddq.pb.DragonHomeExploreNotifyDataMsg.Builder getNotifyDataBuilder() {
      bitField0_ |= 0x00000002;
      onChanged();
      return internalGetNotifyDataFieldBuilder().getBuilder();
    }
    /**
     * <code>optional .xddq.pb.DragonHomeExploreNotifyDataMsg notifyData = 2;</code>
     */
    public xddq.pb.DragonHomeExploreNotifyDataMsgOrBuilder getNotifyDataOrBuilder() {
      if (notifyDataBuilder_ != null) {
        return notifyDataBuilder_.getMessageOrBuilder();
      } else {
        return notifyData_ == null ?
            xddq.pb.DragonHomeExploreNotifyDataMsg.getDefaultInstance() : notifyData_;
      }
    }
    /**
     * <code>optional .xddq.pb.DragonHomeExploreNotifyDataMsg notifyData = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        xddq.pb.DragonHomeExploreNotifyDataMsg, xddq.pb.DragonHomeExploreNotifyDataMsg.Builder, xddq.pb.DragonHomeExploreNotifyDataMsgOrBuilder> 
        internalGetNotifyDataFieldBuilder() {
      if (notifyDataBuilder_ == null) {
        notifyDataBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            xddq.pb.DragonHomeExploreNotifyDataMsg, xddq.pb.DragonHomeExploreNotifyDataMsg.Builder, xddq.pb.DragonHomeExploreNotifyDataMsgOrBuilder>(
                getNotifyData(),
                getParentForChildren(),
                isClean());
        notifyData_ = null;
      }
      return notifyDataBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.DragonHomeUseLightResp)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.DragonHomeUseLightResp)
  private static final xddq.pb.DragonHomeUseLightResp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.DragonHomeUseLightResp();
  }

  public static xddq.pb.DragonHomeUseLightResp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<DragonHomeUseLightResp>
      PARSER = new com.google.protobuf.AbstractParser<DragonHomeUseLightResp>() {
    @java.lang.Override
    public DragonHomeUseLightResp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<DragonHomeUseLightResp> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<DragonHomeUseLightResp> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.DragonHomeUseLightResp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

