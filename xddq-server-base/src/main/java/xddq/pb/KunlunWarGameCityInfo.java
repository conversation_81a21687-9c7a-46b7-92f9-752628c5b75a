// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.KunlunWarGameCityInfo}
 */
public final class KunlunWarGameCityInfo extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.KunlunWarGameCityInfo)
    KunlunWarGameCityInfoOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      KunlunWarGameCityInfo.class.getName());
  }
  // Use KunlunWarGameCityInfo.newBuilder() to construct.
  private KunlunWarGameCityInfo(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private KunlunWarGameCityInfo() {
    displayInfo_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_KunlunWarGameCityInfo_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_KunlunWarGameCityInfo_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.KunlunWarGameCityInfo.class, xddq.pb.KunlunWarGameCityInfo.Builder.class);
  }

  private int bitField0_;
  public static final int CITYID_FIELD_NUMBER = 1;
  private int cityId_ = 0;
  /**
   * <code>required int32 cityId = 1;</code>
   * @return Whether the cityId field is set.
   */
  @java.lang.Override
  public boolean hasCityId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>required int32 cityId = 1;</code>
   * @return The cityId.
   */
  @java.lang.Override
  public int getCityId() {
    return cityId_;
  }

  public static final int HAVEFIGHT_FIELD_NUMBER = 2;
  private boolean haveFight_ = false;
  /**
   * <code>optional bool haveFight = 2;</code>
   * @return Whether the haveFight field is set.
   */
  @java.lang.Override
  public boolean hasHaveFight() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional bool haveFight = 2;</code>
   * @return The haveFight.
   */
  @java.lang.Override
  public boolean getHaveFight() {
    return haveFight_;
  }

  public static final int UNIONID_FIELD_NUMBER = 3;
  private long unionId_ = 0L;
  /**
   * <code>optional int64 unionId = 3;</code>
   * @return Whether the unionId field is set.
   */
  @java.lang.Override
  public boolean hasUnionId() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional int64 unionId = 3;</code>
   * @return The unionId.
   */
  @java.lang.Override
  public long getUnionId() {
    return unionId_;
  }

  public static final int HP_FIELD_NUMBER = 5;
  private int hp_ = 0;
  /**
   * <code>optional int32 hp = 5;</code>
   * @return Whether the hp field is set.
   */
  @java.lang.Override
  public boolean hasHp() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional int32 hp = 5;</code>
   * @return The hp.
   */
  @java.lang.Override
  public int getHp() {
    return hp_;
  }

  public static final int CURHP_FIELD_NUMBER = 6;
  private int curHp_ = 0;
  /**
   * <code>optional int32 curHp = 6;</code>
   * @return Whether the curHp field is set.
   */
  @java.lang.Override
  public boolean hasCurHp() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>optional int32 curHp = 6;</code>
   * @return The curHp.
   */
  @java.lang.Override
  public int getCurHp() {
    return curHp_;
  }

  public static final int UPDATETYPE_FIELD_NUMBER = 7;
  private boolean updateType_ = false;
  /**
   * <code>required bool updateType = 7;</code>
   * @return Whether the updateType field is set.
   */
  @java.lang.Override
  public boolean hasUpdateType() {
    return ((bitField0_ & 0x00000020) != 0);
  }
  /**
   * <code>required bool updateType = 7;</code>
   * @return The updateType.
   */
  @java.lang.Override
  public boolean getUpdateType() {
    return updateType_;
  }

  public static final int DESTROYED_FIELD_NUMBER = 8;
  private boolean destroyed_ = false;
  /**
   * <code>optional bool destroyed = 8;</code>
   * @return Whether the destroyed field is set.
   */
  @java.lang.Override
  public boolean hasDestroyed() {
    return ((bitField0_ & 0x00000040) != 0);
  }
  /**
   * <code>optional bool destroyed = 8;</code>
   * @return The destroyed.
   */
  @java.lang.Override
  public boolean getDestroyed() {
    return destroyed_;
  }

  public static final int DISPLAYINFO_FIELD_NUMBER = 9;
  @SuppressWarnings("serial")
  private java.util.List<xddq.pb.KunlunWarPlayerDisplayInfo> displayInfo_;
  /**
   * <code>repeated .xddq.pb.KunlunWarPlayerDisplayInfo displayInfo = 9;</code>
   */
  @java.lang.Override
  public java.util.List<xddq.pb.KunlunWarPlayerDisplayInfo> getDisplayInfoList() {
    return displayInfo_;
  }
  /**
   * <code>repeated .xddq.pb.KunlunWarPlayerDisplayInfo displayInfo = 9;</code>
   */
  @java.lang.Override
  public java.util.List<? extends xddq.pb.KunlunWarPlayerDisplayInfoOrBuilder> 
      getDisplayInfoOrBuilderList() {
    return displayInfo_;
  }
  /**
   * <code>repeated .xddq.pb.KunlunWarPlayerDisplayInfo displayInfo = 9;</code>
   */
  @java.lang.Override
  public int getDisplayInfoCount() {
    return displayInfo_.size();
  }
  /**
   * <code>repeated .xddq.pb.KunlunWarPlayerDisplayInfo displayInfo = 9;</code>
   */
  @java.lang.Override
  public xddq.pb.KunlunWarPlayerDisplayInfo getDisplayInfo(int index) {
    return displayInfo_.get(index);
  }
  /**
   * <code>repeated .xddq.pb.KunlunWarPlayerDisplayInfo displayInfo = 9;</code>
   */
  @java.lang.Override
  public xddq.pb.KunlunWarPlayerDisplayInfoOrBuilder getDisplayInfoOrBuilder(
      int index) {
    return displayInfo_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    if (!hasCityId()) {
      memoizedIsInitialized = 0;
      return false;
    }
    if (!hasUpdateType()) {
      memoizedIsInitialized = 0;
      return false;
    }
    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, cityId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeBool(2, haveFight_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt64(3, unionId_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      output.writeInt32(5, hp_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      output.writeInt32(6, curHp_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      output.writeBool(7, updateType_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      output.writeBool(8, destroyed_);
    }
    for (int i = 0; i < displayInfo_.size(); i++) {
      output.writeMessage(9, displayInfo_.get(i));
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, cityId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(2, haveFight_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(3, unionId_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(5, hp_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(6, curHp_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(7, updateType_);
    }
    if (((bitField0_ & 0x00000040) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(8, destroyed_);
    }
    for (int i = 0; i < displayInfo_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(9, displayInfo_.get(i));
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.KunlunWarGameCityInfo)) {
      return super.equals(obj);
    }
    xddq.pb.KunlunWarGameCityInfo other = (xddq.pb.KunlunWarGameCityInfo) obj;

    if (hasCityId() != other.hasCityId()) return false;
    if (hasCityId()) {
      if (getCityId()
          != other.getCityId()) return false;
    }
    if (hasHaveFight() != other.hasHaveFight()) return false;
    if (hasHaveFight()) {
      if (getHaveFight()
          != other.getHaveFight()) return false;
    }
    if (hasUnionId() != other.hasUnionId()) return false;
    if (hasUnionId()) {
      if (getUnionId()
          != other.getUnionId()) return false;
    }
    if (hasHp() != other.hasHp()) return false;
    if (hasHp()) {
      if (getHp()
          != other.getHp()) return false;
    }
    if (hasCurHp() != other.hasCurHp()) return false;
    if (hasCurHp()) {
      if (getCurHp()
          != other.getCurHp()) return false;
    }
    if (hasUpdateType() != other.hasUpdateType()) return false;
    if (hasUpdateType()) {
      if (getUpdateType()
          != other.getUpdateType()) return false;
    }
    if (hasDestroyed() != other.hasDestroyed()) return false;
    if (hasDestroyed()) {
      if (getDestroyed()
          != other.getDestroyed()) return false;
    }
    if (!getDisplayInfoList()
        .equals(other.getDisplayInfoList())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasCityId()) {
      hash = (37 * hash) + CITYID_FIELD_NUMBER;
      hash = (53 * hash) + getCityId();
    }
    if (hasHaveFight()) {
      hash = (37 * hash) + HAVEFIGHT_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getHaveFight());
    }
    if (hasUnionId()) {
      hash = (37 * hash) + UNIONID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getUnionId());
    }
    if (hasHp()) {
      hash = (37 * hash) + HP_FIELD_NUMBER;
      hash = (53 * hash) + getHp();
    }
    if (hasCurHp()) {
      hash = (37 * hash) + CURHP_FIELD_NUMBER;
      hash = (53 * hash) + getCurHp();
    }
    if (hasUpdateType()) {
      hash = (37 * hash) + UPDATETYPE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getUpdateType());
    }
    if (hasDestroyed()) {
      hash = (37 * hash) + DESTROYED_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getDestroyed());
    }
    if (getDisplayInfoCount() > 0) {
      hash = (37 * hash) + DISPLAYINFO_FIELD_NUMBER;
      hash = (53 * hash) + getDisplayInfoList().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.KunlunWarGameCityInfo parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.KunlunWarGameCityInfo parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.KunlunWarGameCityInfo parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.KunlunWarGameCityInfo parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.KunlunWarGameCityInfo parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.KunlunWarGameCityInfo parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.KunlunWarGameCityInfo parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.KunlunWarGameCityInfo parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.KunlunWarGameCityInfo parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.KunlunWarGameCityInfo parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.KunlunWarGameCityInfo parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.KunlunWarGameCityInfo parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.KunlunWarGameCityInfo prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.KunlunWarGameCityInfo}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.KunlunWarGameCityInfo)
      xddq.pb.KunlunWarGameCityInfoOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_KunlunWarGameCityInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_KunlunWarGameCityInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.KunlunWarGameCityInfo.class, xddq.pb.KunlunWarGameCityInfo.Builder.class);
    }

    // Construct using xddq.pb.KunlunWarGameCityInfo.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      cityId_ = 0;
      haveFight_ = false;
      unionId_ = 0L;
      hp_ = 0;
      curHp_ = 0;
      updateType_ = false;
      destroyed_ = false;
      if (displayInfoBuilder_ == null) {
        displayInfo_ = java.util.Collections.emptyList();
      } else {
        displayInfo_ = null;
        displayInfoBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000080);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_KunlunWarGameCityInfo_descriptor;
    }

    @java.lang.Override
    public xddq.pb.KunlunWarGameCityInfo getDefaultInstanceForType() {
      return xddq.pb.KunlunWarGameCityInfo.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.KunlunWarGameCityInfo build() {
      xddq.pb.KunlunWarGameCityInfo result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.KunlunWarGameCityInfo buildPartial() {
      xddq.pb.KunlunWarGameCityInfo result = new xddq.pb.KunlunWarGameCityInfo(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(xddq.pb.KunlunWarGameCityInfo result) {
      if (displayInfoBuilder_ == null) {
        if (((bitField0_ & 0x00000080) != 0)) {
          displayInfo_ = java.util.Collections.unmodifiableList(displayInfo_);
          bitField0_ = (bitField0_ & ~0x00000080);
        }
        result.displayInfo_ = displayInfo_;
      } else {
        result.displayInfo_ = displayInfoBuilder_.build();
      }
    }

    private void buildPartial0(xddq.pb.KunlunWarGameCityInfo result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.cityId_ = cityId_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.haveFight_ = haveFight_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.unionId_ = unionId_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.hp_ = hp_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.curHp_ = curHp_;
        to_bitField0_ |= 0x00000010;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.updateType_ = updateType_;
        to_bitField0_ |= 0x00000020;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.destroyed_ = destroyed_;
        to_bitField0_ |= 0x00000040;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.KunlunWarGameCityInfo) {
        return mergeFrom((xddq.pb.KunlunWarGameCityInfo)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.KunlunWarGameCityInfo other) {
      if (other == xddq.pb.KunlunWarGameCityInfo.getDefaultInstance()) return this;
      if (other.hasCityId()) {
        setCityId(other.getCityId());
      }
      if (other.hasHaveFight()) {
        setHaveFight(other.getHaveFight());
      }
      if (other.hasUnionId()) {
        setUnionId(other.getUnionId());
      }
      if (other.hasHp()) {
        setHp(other.getHp());
      }
      if (other.hasCurHp()) {
        setCurHp(other.getCurHp());
      }
      if (other.hasUpdateType()) {
        setUpdateType(other.getUpdateType());
      }
      if (other.hasDestroyed()) {
        setDestroyed(other.getDestroyed());
      }
      if (displayInfoBuilder_ == null) {
        if (!other.displayInfo_.isEmpty()) {
          if (displayInfo_.isEmpty()) {
            displayInfo_ = other.displayInfo_;
            bitField0_ = (bitField0_ & ~0x00000080);
          } else {
            ensureDisplayInfoIsMutable();
            displayInfo_.addAll(other.displayInfo_);
          }
          onChanged();
        }
      } else {
        if (!other.displayInfo_.isEmpty()) {
          if (displayInfoBuilder_.isEmpty()) {
            displayInfoBuilder_.dispose();
            displayInfoBuilder_ = null;
            displayInfo_ = other.displayInfo_;
            bitField0_ = (bitField0_ & ~0x00000080);
            displayInfoBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 internalGetDisplayInfoFieldBuilder() : null;
          } else {
            displayInfoBuilder_.addAllMessages(other.displayInfo_);
          }
        }
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      if (!hasCityId()) {
        return false;
      }
      if (!hasUpdateType()) {
        return false;
      }
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              cityId_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              haveFight_ = input.readBool();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 24: {
              unionId_ = input.readInt64();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 40: {
              hp_ = input.readInt32();
              bitField0_ |= 0x00000008;
              break;
            } // case 40
            case 48: {
              curHp_ = input.readInt32();
              bitField0_ |= 0x00000010;
              break;
            } // case 48
            case 56: {
              updateType_ = input.readBool();
              bitField0_ |= 0x00000020;
              break;
            } // case 56
            case 64: {
              destroyed_ = input.readBool();
              bitField0_ |= 0x00000040;
              break;
            } // case 64
            case 74: {
              xddq.pb.KunlunWarPlayerDisplayInfo m =
                  input.readMessage(
                      xddq.pb.KunlunWarPlayerDisplayInfo.parser(),
                      extensionRegistry);
              if (displayInfoBuilder_ == null) {
                ensureDisplayInfoIsMutable();
                displayInfo_.add(m);
              } else {
                displayInfoBuilder_.addMessage(m);
              }
              break;
            } // case 74
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int cityId_ ;
    /**
     * <code>required int32 cityId = 1;</code>
     * @return Whether the cityId field is set.
     */
    @java.lang.Override
    public boolean hasCityId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required int32 cityId = 1;</code>
     * @return The cityId.
     */
    @java.lang.Override
    public int getCityId() {
      return cityId_;
    }
    /**
     * <code>required int32 cityId = 1;</code>
     * @param value The cityId to set.
     * @return This builder for chaining.
     */
    public Builder setCityId(int value) {

      cityId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>required int32 cityId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearCityId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      cityId_ = 0;
      onChanged();
      return this;
    }

    private boolean haveFight_ ;
    /**
     * <code>optional bool haveFight = 2;</code>
     * @return Whether the haveFight field is set.
     */
    @java.lang.Override
    public boolean hasHaveFight() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional bool haveFight = 2;</code>
     * @return The haveFight.
     */
    @java.lang.Override
    public boolean getHaveFight() {
      return haveFight_;
    }
    /**
     * <code>optional bool haveFight = 2;</code>
     * @param value The haveFight to set.
     * @return This builder for chaining.
     */
    public Builder setHaveFight(boolean value) {

      haveFight_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional bool haveFight = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearHaveFight() {
      bitField0_ = (bitField0_ & ~0x00000002);
      haveFight_ = false;
      onChanged();
      return this;
    }

    private long unionId_ ;
    /**
     * <code>optional int64 unionId = 3;</code>
     * @return Whether the unionId field is set.
     */
    @java.lang.Override
    public boolean hasUnionId() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int64 unionId = 3;</code>
     * @return The unionId.
     */
    @java.lang.Override
    public long getUnionId() {
      return unionId_;
    }
    /**
     * <code>optional int64 unionId = 3;</code>
     * @param value The unionId to set.
     * @return This builder for chaining.
     */
    public Builder setUnionId(long value) {

      unionId_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional int64 unionId = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearUnionId() {
      bitField0_ = (bitField0_ & ~0x00000004);
      unionId_ = 0L;
      onChanged();
      return this;
    }

    private int hp_ ;
    /**
     * <code>optional int32 hp = 5;</code>
     * @return Whether the hp field is set.
     */
    @java.lang.Override
    public boolean hasHp() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int32 hp = 5;</code>
     * @return The hp.
     */
    @java.lang.Override
    public int getHp() {
      return hp_;
    }
    /**
     * <code>optional int32 hp = 5;</code>
     * @param value The hp to set.
     * @return This builder for chaining.
     */
    public Builder setHp(int value) {

      hp_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 hp = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearHp() {
      bitField0_ = (bitField0_ & ~0x00000008);
      hp_ = 0;
      onChanged();
      return this;
    }

    private int curHp_ ;
    /**
     * <code>optional int32 curHp = 6;</code>
     * @return Whether the curHp field is set.
     */
    @java.lang.Override
    public boolean hasCurHp() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional int32 curHp = 6;</code>
     * @return The curHp.
     */
    @java.lang.Override
    public int getCurHp() {
      return curHp_;
    }
    /**
     * <code>optional int32 curHp = 6;</code>
     * @param value The curHp to set.
     * @return This builder for chaining.
     */
    public Builder setCurHp(int value) {

      curHp_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 curHp = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearCurHp() {
      bitField0_ = (bitField0_ & ~0x00000010);
      curHp_ = 0;
      onChanged();
      return this;
    }

    private boolean updateType_ ;
    /**
     * <code>required bool updateType = 7;</code>
     * @return Whether the updateType field is set.
     */
    @java.lang.Override
    public boolean hasUpdateType() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>required bool updateType = 7;</code>
     * @return The updateType.
     */
    @java.lang.Override
    public boolean getUpdateType() {
      return updateType_;
    }
    /**
     * <code>required bool updateType = 7;</code>
     * @param value The updateType to set.
     * @return This builder for chaining.
     */
    public Builder setUpdateType(boolean value) {

      updateType_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>required bool updateType = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearUpdateType() {
      bitField0_ = (bitField0_ & ~0x00000020);
      updateType_ = false;
      onChanged();
      return this;
    }

    private boolean destroyed_ ;
    /**
     * <code>optional bool destroyed = 8;</code>
     * @return Whether the destroyed field is set.
     */
    @java.lang.Override
    public boolean hasDestroyed() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <code>optional bool destroyed = 8;</code>
     * @return The destroyed.
     */
    @java.lang.Override
    public boolean getDestroyed() {
      return destroyed_;
    }
    /**
     * <code>optional bool destroyed = 8;</code>
     * @param value The destroyed to set.
     * @return This builder for chaining.
     */
    public Builder setDestroyed(boolean value) {

      destroyed_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>optional bool destroyed = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearDestroyed() {
      bitField0_ = (bitField0_ & ~0x00000040);
      destroyed_ = false;
      onChanged();
      return this;
    }

    private java.util.List<xddq.pb.KunlunWarPlayerDisplayInfo> displayInfo_ =
      java.util.Collections.emptyList();
    private void ensureDisplayInfoIsMutable() {
      if (!((bitField0_ & 0x00000080) != 0)) {
        displayInfo_ = new java.util.ArrayList<xddq.pb.KunlunWarPlayerDisplayInfo>(displayInfo_);
        bitField0_ |= 0x00000080;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.KunlunWarPlayerDisplayInfo, xddq.pb.KunlunWarPlayerDisplayInfo.Builder, xddq.pb.KunlunWarPlayerDisplayInfoOrBuilder> displayInfoBuilder_;

    /**
     * <code>repeated .xddq.pb.KunlunWarPlayerDisplayInfo displayInfo = 9;</code>
     */
    public java.util.List<xddq.pb.KunlunWarPlayerDisplayInfo> getDisplayInfoList() {
      if (displayInfoBuilder_ == null) {
        return java.util.Collections.unmodifiableList(displayInfo_);
      } else {
        return displayInfoBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarPlayerDisplayInfo displayInfo = 9;</code>
     */
    public int getDisplayInfoCount() {
      if (displayInfoBuilder_ == null) {
        return displayInfo_.size();
      } else {
        return displayInfoBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarPlayerDisplayInfo displayInfo = 9;</code>
     */
    public xddq.pb.KunlunWarPlayerDisplayInfo getDisplayInfo(int index) {
      if (displayInfoBuilder_ == null) {
        return displayInfo_.get(index);
      } else {
        return displayInfoBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarPlayerDisplayInfo displayInfo = 9;</code>
     */
    public Builder setDisplayInfo(
        int index, xddq.pb.KunlunWarPlayerDisplayInfo value) {
      if (displayInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDisplayInfoIsMutable();
        displayInfo_.set(index, value);
        onChanged();
      } else {
        displayInfoBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarPlayerDisplayInfo displayInfo = 9;</code>
     */
    public Builder setDisplayInfo(
        int index, xddq.pb.KunlunWarPlayerDisplayInfo.Builder builderForValue) {
      if (displayInfoBuilder_ == null) {
        ensureDisplayInfoIsMutable();
        displayInfo_.set(index, builderForValue.build());
        onChanged();
      } else {
        displayInfoBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarPlayerDisplayInfo displayInfo = 9;</code>
     */
    public Builder addDisplayInfo(xddq.pb.KunlunWarPlayerDisplayInfo value) {
      if (displayInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDisplayInfoIsMutable();
        displayInfo_.add(value);
        onChanged();
      } else {
        displayInfoBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarPlayerDisplayInfo displayInfo = 9;</code>
     */
    public Builder addDisplayInfo(
        int index, xddq.pb.KunlunWarPlayerDisplayInfo value) {
      if (displayInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDisplayInfoIsMutable();
        displayInfo_.add(index, value);
        onChanged();
      } else {
        displayInfoBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarPlayerDisplayInfo displayInfo = 9;</code>
     */
    public Builder addDisplayInfo(
        xddq.pb.KunlunWarPlayerDisplayInfo.Builder builderForValue) {
      if (displayInfoBuilder_ == null) {
        ensureDisplayInfoIsMutable();
        displayInfo_.add(builderForValue.build());
        onChanged();
      } else {
        displayInfoBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarPlayerDisplayInfo displayInfo = 9;</code>
     */
    public Builder addDisplayInfo(
        int index, xddq.pb.KunlunWarPlayerDisplayInfo.Builder builderForValue) {
      if (displayInfoBuilder_ == null) {
        ensureDisplayInfoIsMutable();
        displayInfo_.add(index, builderForValue.build());
        onChanged();
      } else {
        displayInfoBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarPlayerDisplayInfo displayInfo = 9;</code>
     */
    public Builder addAllDisplayInfo(
        java.lang.Iterable<? extends xddq.pb.KunlunWarPlayerDisplayInfo> values) {
      if (displayInfoBuilder_ == null) {
        ensureDisplayInfoIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, displayInfo_);
        onChanged();
      } else {
        displayInfoBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarPlayerDisplayInfo displayInfo = 9;</code>
     */
    public Builder clearDisplayInfo() {
      if (displayInfoBuilder_ == null) {
        displayInfo_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000080);
        onChanged();
      } else {
        displayInfoBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarPlayerDisplayInfo displayInfo = 9;</code>
     */
    public Builder removeDisplayInfo(int index) {
      if (displayInfoBuilder_ == null) {
        ensureDisplayInfoIsMutable();
        displayInfo_.remove(index);
        onChanged();
      } else {
        displayInfoBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarPlayerDisplayInfo displayInfo = 9;</code>
     */
    public xddq.pb.KunlunWarPlayerDisplayInfo.Builder getDisplayInfoBuilder(
        int index) {
      return internalGetDisplayInfoFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarPlayerDisplayInfo displayInfo = 9;</code>
     */
    public xddq.pb.KunlunWarPlayerDisplayInfoOrBuilder getDisplayInfoOrBuilder(
        int index) {
      if (displayInfoBuilder_ == null) {
        return displayInfo_.get(index);  } else {
        return displayInfoBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarPlayerDisplayInfo displayInfo = 9;</code>
     */
    public java.util.List<? extends xddq.pb.KunlunWarPlayerDisplayInfoOrBuilder> 
         getDisplayInfoOrBuilderList() {
      if (displayInfoBuilder_ != null) {
        return displayInfoBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(displayInfo_);
      }
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarPlayerDisplayInfo displayInfo = 9;</code>
     */
    public xddq.pb.KunlunWarPlayerDisplayInfo.Builder addDisplayInfoBuilder() {
      return internalGetDisplayInfoFieldBuilder().addBuilder(
          xddq.pb.KunlunWarPlayerDisplayInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarPlayerDisplayInfo displayInfo = 9;</code>
     */
    public xddq.pb.KunlunWarPlayerDisplayInfo.Builder addDisplayInfoBuilder(
        int index) {
      return internalGetDisplayInfoFieldBuilder().addBuilder(
          index, xddq.pb.KunlunWarPlayerDisplayInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .xddq.pb.KunlunWarPlayerDisplayInfo displayInfo = 9;</code>
     */
    public java.util.List<xddq.pb.KunlunWarPlayerDisplayInfo.Builder> 
         getDisplayInfoBuilderList() {
      return internalGetDisplayInfoFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        xddq.pb.KunlunWarPlayerDisplayInfo, xddq.pb.KunlunWarPlayerDisplayInfo.Builder, xddq.pb.KunlunWarPlayerDisplayInfoOrBuilder> 
        internalGetDisplayInfoFieldBuilder() {
      if (displayInfoBuilder_ == null) {
        displayInfoBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            xddq.pb.KunlunWarPlayerDisplayInfo, xddq.pb.KunlunWarPlayerDisplayInfo.Builder, xddq.pb.KunlunWarPlayerDisplayInfoOrBuilder>(
                displayInfo_,
                ((bitField0_ & 0x00000080) != 0),
                getParentForChildren(),
                isClean());
        displayInfo_ = null;
      }
      return displayInfoBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.KunlunWarGameCityInfo)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.KunlunWarGameCityInfo)
  private static final xddq.pb.KunlunWarGameCityInfo DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.KunlunWarGameCityInfo();
  }

  public static xddq.pb.KunlunWarGameCityInfo getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<KunlunWarGameCityInfo>
      PARSER = new com.google.protobuf.AbstractParser<KunlunWarGameCityInfo>() {
    @java.lang.Override
    public KunlunWarGameCityInfo parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<KunlunWarGameCityInfo> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<KunlunWarGameCityInfo> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.KunlunWarGameCityInfo getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

