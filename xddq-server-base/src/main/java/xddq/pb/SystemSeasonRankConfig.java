// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: xddq.proto
// Protobuf Java Version: 4.30.2

package xddq.pb;

/**
 * Protobuf type {@code xddq.pb.SystemSeasonRankConfig}
 */
public final class SystemSeasonRankConfig extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:xddq.pb.SystemSeasonRankConfig)
    SystemSeasonRankConfigOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 30,
      /* patch= */ 2,
      /* suffix= */ "",
      SystemSeasonRankConfig.class.getName());
  }
  // Use SystemSeasonRankConfig.newBuilder() to construct.
  private SystemSeasonRankConfig(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private SystemSeasonRankConfig() {
    rewards_ = "";
    masterRewards_ = "";
    params_ = "";
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return xddq.pb.Xddq.internal_static_xddq_pb_SystemSeasonRankConfig_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return xddq.pb.Xddq.internal_static_xddq_pb_SystemSeasonRankConfig_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            xddq.pb.SystemSeasonRankConfig.class, xddq.pb.SystemSeasonRankConfig.Builder.class);
  }

  private int bitField0_;
  public static final int SYSTEMID_FIELD_NUMBER = 1;
  private int systemId_ = 0;
  /**
   * <code>optional int32 systemId = 1;</code>
   * @return Whether the systemId field is set.
   */
  @java.lang.Override
  public boolean hasSystemId() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>optional int32 systemId = 1;</code>
   * @return The systemId.
   */
  @java.lang.Override
  public int getSystemId() {
    return systemId_;
  }

  public static final int ID_FIELD_NUMBER = 2;
  private int id_ = 0;
  /**
   * <code>optional int32 id = 2;</code>
   * @return Whether the id field is set.
   */
  @java.lang.Override
  public boolean hasId() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>optional int32 id = 2;</code>
   * @return The id.
   */
  @java.lang.Override
  public int getId() {
    return id_;
  }

  public static final int TYPE_FIELD_NUMBER = 3;
  private int type_ = 0;
  /**
   * <code>optional int32 type = 3;</code>
   * @return Whether the type field is set.
   */
  @java.lang.Override
  public boolean hasType() {
    return ((bitField0_ & 0x00000004) != 0);
  }
  /**
   * <code>optional int32 type = 3;</code>
   * @return The type.
   */
  @java.lang.Override
  public int getType() {
    return type_;
  }

  public static final int REWARDS_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private volatile java.lang.Object rewards_ = "";
  /**
   * <code>optional string rewards = 4;</code>
   * @return Whether the rewards field is set.
   */
  @java.lang.Override
  public boolean hasRewards() {
    return ((bitField0_ & 0x00000008) != 0);
  }
  /**
   * <code>optional string rewards = 4;</code>
   * @return The rewards.
   */
  @java.lang.Override
  public java.lang.String getRewards() {
    java.lang.Object ref = rewards_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        rewards_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string rewards = 4;</code>
   * @return The bytes for rewards.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getRewardsBytes() {
    java.lang.Object ref = rewards_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      rewards_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int MASTERREWARDS_FIELD_NUMBER = 5;
  @SuppressWarnings("serial")
  private volatile java.lang.Object masterRewards_ = "";
  /**
   * <code>optional string masterRewards = 5;</code>
   * @return Whether the masterRewards field is set.
   */
  @java.lang.Override
  public boolean hasMasterRewards() {
    return ((bitField0_ & 0x00000010) != 0);
  }
  /**
   * <code>optional string masterRewards = 5;</code>
   * @return The masterRewards.
   */
  @java.lang.Override
  public java.lang.String getMasterRewards() {
    java.lang.Object ref = masterRewards_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        masterRewards_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string masterRewards = 5;</code>
   * @return The bytes for masterRewards.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getMasterRewardsBytes() {
    java.lang.Object ref = masterRewards_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      masterRewards_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PARAMS_FIELD_NUMBER = 6;
  @SuppressWarnings("serial")
  private volatile java.lang.Object params_ = "";
  /**
   * <code>optional string params = 6;</code>
   * @return Whether the params field is set.
   */
  @java.lang.Override
  public boolean hasParams() {
    return ((bitField0_ & 0x00000020) != 0);
  }
  /**
   * <code>optional string params = 6;</code>
   * @return The params.
   */
  @java.lang.Override
  public java.lang.String getParams() {
    java.lang.Object ref = params_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      if (bs.isValidUtf8()) {
        params_ = s;
      }
      return s;
    }
  }
  /**
   * <code>optional string params = 6;</code>
   * @return The bytes for params.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getParamsBytes() {
    java.lang.Object ref = params_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      params_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeInt32(1, systemId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeInt32(2, id_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      output.writeInt32(3, type_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 4, rewards_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 5, masterRewards_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 6, params_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, systemId_);
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, id_);
    }
    if (((bitField0_ & 0x00000004) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(3, type_);
    }
    if (((bitField0_ & 0x00000008) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(4, rewards_);
    }
    if (((bitField0_ & 0x00000010) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(5, masterRewards_);
    }
    if (((bitField0_ & 0x00000020) != 0)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(6, params_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof xddq.pb.SystemSeasonRankConfig)) {
      return super.equals(obj);
    }
    xddq.pb.SystemSeasonRankConfig other = (xddq.pb.SystemSeasonRankConfig) obj;

    if (hasSystemId() != other.hasSystemId()) return false;
    if (hasSystemId()) {
      if (getSystemId()
          != other.getSystemId()) return false;
    }
    if (hasId() != other.hasId()) return false;
    if (hasId()) {
      if (getId()
          != other.getId()) return false;
    }
    if (hasType() != other.hasType()) return false;
    if (hasType()) {
      if (getType()
          != other.getType()) return false;
    }
    if (hasRewards() != other.hasRewards()) return false;
    if (hasRewards()) {
      if (!getRewards()
          .equals(other.getRewards())) return false;
    }
    if (hasMasterRewards() != other.hasMasterRewards()) return false;
    if (hasMasterRewards()) {
      if (!getMasterRewards()
          .equals(other.getMasterRewards())) return false;
    }
    if (hasParams() != other.hasParams()) return false;
    if (hasParams()) {
      if (!getParams()
          .equals(other.getParams())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasSystemId()) {
      hash = (37 * hash) + SYSTEMID_FIELD_NUMBER;
      hash = (53 * hash) + getSystemId();
    }
    if (hasId()) {
      hash = (37 * hash) + ID_FIELD_NUMBER;
      hash = (53 * hash) + getId();
    }
    if (hasType()) {
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
    }
    if (hasRewards()) {
      hash = (37 * hash) + REWARDS_FIELD_NUMBER;
      hash = (53 * hash) + getRewards().hashCode();
    }
    if (hasMasterRewards()) {
      hash = (37 * hash) + MASTERREWARDS_FIELD_NUMBER;
      hash = (53 * hash) + getMasterRewards().hashCode();
    }
    if (hasParams()) {
      hash = (37 * hash) + PARAMS_FIELD_NUMBER;
      hash = (53 * hash) + getParams().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static xddq.pb.SystemSeasonRankConfig parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.SystemSeasonRankConfig parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.SystemSeasonRankConfig parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.SystemSeasonRankConfig parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.SystemSeasonRankConfig parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static xddq.pb.SystemSeasonRankConfig parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static xddq.pb.SystemSeasonRankConfig parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.SystemSeasonRankConfig parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static xddq.pb.SystemSeasonRankConfig parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static xddq.pb.SystemSeasonRankConfig parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static xddq.pb.SystemSeasonRankConfig parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static xddq.pb.SystemSeasonRankConfig parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(xddq.pb.SystemSeasonRankConfig prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code xddq.pb.SystemSeasonRankConfig}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:xddq.pb.SystemSeasonRankConfig)
      xddq.pb.SystemSeasonRankConfigOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xddq.pb.Xddq.internal_static_xddq_pb_SystemSeasonRankConfig_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xddq.pb.Xddq.internal_static_xddq_pb_SystemSeasonRankConfig_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xddq.pb.SystemSeasonRankConfig.class, xddq.pb.SystemSeasonRankConfig.Builder.class);
    }

    // Construct using xddq.pb.SystemSeasonRankConfig.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      systemId_ = 0;
      id_ = 0;
      type_ = 0;
      rewards_ = "";
      masterRewards_ = "";
      params_ = "";
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return xddq.pb.Xddq.internal_static_xddq_pb_SystemSeasonRankConfig_descriptor;
    }

    @java.lang.Override
    public xddq.pb.SystemSeasonRankConfig getDefaultInstanceForType() {
      return xddq.pb.SystemSeasonRankConfig.getDefaultInstance();
    }

    @java.lang.Override
    public xddq.pb.SystemSeasonRankConfig build() {
      xddq.pb.SystemSeasonRankConfig result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public xddq.pb.SystemSeasonRankConfig buildPartial() {
      xddq.pb.SystemSeasonRankConfig result = new xddq.pb.SystemSeasonRankConfig(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(xddq.pb.SystemSeasonRankConfig result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.systemId_ = systemId_;
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.id_ = id_;
        to_bitField0_ |= 0x00000002;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.type_ = type_;
        to_bitField0_ |= 0x00000004;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.rewards_ = rewards_;
        to_bitField0_ |= 0x00000008;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.masterRewards_ = masterRewards_;
        to_bitField0_ |= 0x00000010;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.params_ = params_;
        to_bitField0_ |= 0x00000020;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof xddq.pb.SystemSeasonRankConfig) {
        return mergeFrom((xddq.pb.SystemSeasonRankConfig)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(xddq.pb.SystemSeasonRankConfig other) {
      if (other == xddq.pb.SystemSeasonRankConfig.getDefaultInstance()) return this;
      if (other.hasSystemId()) {
        setSystemId(other.getSystemId());
      }
      if (other.hasId()) {
        setId(other.getId());
      }
      if (other.hasType()) {
        setType(other.getType());
      }
      if (other.hasRewards()) {
        rewards_ = other.rewards_;
        bitField0_ |= 0x00000008;
        onChanged();
      }
      if (other.hasMasterRewards()) {
        masterRewards_ = other.masterRewards_;
        bitField0_ |= 0x00000010;
        onChanged();
      }
      if (other.hasParams()) {
        params_ = other.params_;
        bitField0_ |= 0x00000020;
        onChanged();
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              systemId_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              id_ = input.readInt32();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 24: {
              type_ = input.readInt32();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 34: {
              rewards_ = input.readBytes();
              bitField0_ |= 0x00000008;
              break;
            } // case 34
            case 42: {
              masterRewards_ = input.readBytes();
              bitField0_ |= 0x00000010;
              break;
            } // case 42
            case 50: {
              params_ = input.readBytes();
              bitField0_ |= 0x00000020;
              break;
            } // case 50
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int systemId_ ;
    /**
     * <code>optional int32 systemId = 1;</code>
     * @return Whether the systemId field is set.
     */
    @java.lang.Override
    public boolean hasSystemId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 systemId = 1;</code>
     * @return The systemId.
     */
    @java.lang.Override
    public int getSystemId() {
      return systemId_;
    }
    /**
     * <code>optional int32 systemId = 1;</code>
     * @param value The systemId to set.
     * @return This builder for chaining.
     */
    public Builder setSystemId(int value) {

      systemId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 systemId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearSystemId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      systemId_ = 0;
      onChanged();
      return this;
    }

    private int id_ ;
    /**
     * <code>optional int32 id = 2;</code>
     * @return Whether the id field is set.
     */
    @java.lang.Override
    public boolean hasId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 id = 2;</code>
     * @return The id.
     */
    @java.lang.Override
    public int getId() {
      return id_;
    }
    /**
     * <code>optional int32 id = 2;</code>
     * @param value The id to set.
     * @return This builder for chaining.
     */
    public Builder setId(int value) {

      id_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 id = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearId() {
      bitField0_ = (bitField0_ & ~0x00000002);
      id_ = 0;
      onChanged();
      return this;
    }

    private int type_ ;
    /**
     * <code>optional int32 type = 3;</code>
     * @return Whether the type field is set.
     */
    @java.lang.Override
    public boolean hasType() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int32 type = 3;</code>
     * @return The type.
     */
    @java.lang.Override
    public int getType() {
      return type_;
    }
    /**
     * <code>optional int32 type = 3;</code>
     * @param value The type to set.
     * @return This builder for chaining.
     */
    public Builder setType(int value) {

      type_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>optional int32 type = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearType() {
      bitField0_ = (bitField0_ & ~0x00000004);
      type_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object rewards_ = "";
    /**
     * <code>optional string rewards = 4;</code>
     * @return Whether the rewards field is set.
     */
    public boolean hasRewards() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional string rewards = 4;</code>
     * @return The rewards.
     */
    public java.lang.String getRewards() {
      java.lang.Object ref = rewards_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          rewards_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string rewards = 4;</code>
     * @return The bytes for rewards.
     */
    public com.google.protobuf.ByteString
        getRewardsBytes() {
      java.lang.Object ref = rewards_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        rewards_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string rewards = 4;</code>
     * @param value The rewards to set.
     * @return This builder for chaining.
     */
    public Builder setRewards(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      rewards_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>optional string rewards = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearRewards() {
      rewards_ = getDefaultInstance().getRewards();
      bitField0_ = (bitField0_ & ~0x00000008);
      onChanged();
      return this;
    }
    /**
     * <code>optional string rewards = 4;</code>
     * @param value The bytes for rewards to set.
     * @return This builder for chaining.
     */
    public Builder setRewardsBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      rewards_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }

    private java.lang.Object masterRewards_ = "";
    /**
     * <code>optional string masterRewards = 5;</code>
     * @return Whether the masterRewards field is set.
     */
    public boolean hasMasterRewards() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional string masterRewards = 5;</code>
     * @return The masterRewards.
     */
    public java.lang.String getMasterRewards() {
      java.lang.Object ref = masterRewards_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          masterRewards_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string masterRewards = 5;</code>
     * @return The bytes for masterRewards.
     */
    public com.google.protobuf.ByteString
        getMasterRewardsBytes() {
      java.lang.Object ref = masterRewards_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        masterRewards_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string masterRewards = 5;</code>
     * @param value The masterRewards to set.
     * @return This builder for chaining.
     */
    public Builder setMasterRewards(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      masterRewards_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>optional string masterRewards = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearMasterRewards() {
      masterRewards_ = getDefaultInstance().getMasterRewards();
      bitField0_ = (bitField0_ & ~0x00000010);
      onChanged();
      return this;
    }
    /**
     * <code>optional string masterRewards = 5;</code>
     * @param value The bytes for masterRewards to set.
     * @return This builder for chaining.
     */
    public Builder setMasterRewardsBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      masterRewards_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }

    private java.lang.Object params_ = "";
    /**
     * <code>optional string params = 6;</code>
     * @return Whether the params field is set.
     */
    public boolean hasParams() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional string params = 6;</code>
     * @return The params.
     */
    public java.lang.String getParams() {
      java.lang.Object ref = params_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          params_ = s;
        }
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>optional string params = 6;</code>
     * @return The bytes for params.
     */
    public com.google.protobuf.ByteString
        getParamsBytes() {
      java.lang.Object ref = params_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        params_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>optional string params = 6;</code>
     * @param value The params to set.
     * @return This builder for chaining.
     */
    public Builder setParams(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      params_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>optional string params = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearParams() {
      params_ = getDefaultInstance().getParams();
      bitField0_ = (bitField0_ & ~0x00000020);
      onChanged();
      return this;
    }
    /**
     * <code>optional string params = 6;</code>
     * @param value The bytes for params to set.
     * @return This builder for chaining.
     */
    public Builder setParamsBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      params_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:xddq.pb.SystemSeasonRankConfig)
  }

  // @@protoc_insertion_point(class_scope:xddq.pb.SystemSeasonRankConfig)
  private static final xddq.pb.SystemSeasonRankConfig DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new xddq.pb.SystemSeasonRankConfig();
  }

  public static xddq.pb.SystemSeasonRankConfig getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<SystemSeasonRankConfig>
      PARSER = new com.google.protobuf.AbstractParser<SystemSeasonRankConfig>() {
    @java.lang.Override
    public SystemSeasonRankConfig parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<SystemSeasonRankConfig> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<SystemSeasonRankConfig> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public xddq.pb.SystemSeasonRankConfig getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

