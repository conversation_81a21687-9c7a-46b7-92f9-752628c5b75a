package xddq.palace.structs;

import java.util.concurrent.ConcurrentHashMap;

public class PalaceData {
//    message EnterPalaceRsp{
//        required int32 ret= 1;
//        repeated PalaceDataMsg palaceData = 2; //
//        optional bool  worship = 3;//系统 是否可以点赞
//        optional bool worshipRandom = 4;//系统 是否可以随机点赞
//        optional int64 consumeScore = 5;//消耗积分 最小为0
//    }
    // key: titleId
    private ConcurrentHashMap<Integer, Palace> palaceMap = new ConcurrentHashMap<>();
    private boolean worship;
    private boolean worshipRandom;
    private long consumeScore = 0L;

    public ConcurrentHashMap<Integer, Palace> getPalaceMap() {
        return palaceMap;
    }

    public void setPalaceMap(ConcurrentHashMap<Integer, Palace> palaceMap) {
        this.palaceMap = palaceMap;
    }

    public boolean isWorship() {
        return worship;
    }

    public void setWorship(boolean worship) {
        this.worship = worship;
    }

    public boolean isWorshipRandom() {
        return worshipRandom;
    }

    public void setWorshipRandom(boolean worshipRandom) {
        this.worshipRandom = worshipRandom;
    }

    public long getConsumeScore() {
        return consumeScore;
    }

    public void setConsumeScore(long consumeScore) {
        this.consumeScore = consumeScore;
    }
}
