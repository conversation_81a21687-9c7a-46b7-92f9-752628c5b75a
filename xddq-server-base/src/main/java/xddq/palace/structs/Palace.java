package xddq.palace.structs;

public class Palace {
//    message PalaceDataMsg{
//        required int32 titleId = 1;//称号id = 仙宫id
//        required bool  worship = 2;//称号是否可以点赞
//        required bool  show = 3;//仙宫是否展示
//        optional bool worshipRandom = 4; //随机膜拜
//    }

    private int titleId;
    private boolean worship;
    private boolean show;
    private boolean worshipRandom;

    public int getTitleId() {
        return titleId;
    }

    public void setTitleId(int titleId) {
        this.titleId = titleId;
    }

    public boolean isWorship() {
        return worship;
    }

    public void setWorship(boolean worship) {
        this.worship = worship;
    }

    public boolean isShow() {
        return show;
    }

    public void setShow(boolean show) {
        this.show = show;
    }

    public boolean isWorshipRandom() {
        return worshipRandom;
    }

    public void setWorshipRandom(boolean worshipRandom) {
        this.worshipRandom = worshipRandom;
    }
}
