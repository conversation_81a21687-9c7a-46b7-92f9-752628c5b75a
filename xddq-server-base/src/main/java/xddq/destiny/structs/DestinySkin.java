package xddq.destiny.structs;

import xddq.structs.GameObject;

//message DestinySkinData{
//    optional int32 skinId=1;
//    optional int32 lv=2;
//    optional int32 isWear=3;
//}
public class <PERSON>Skin extends GameObject {

    private int level;

    private int wear;

    public int getLevel() {
        return level;
    }

    public DestinySkin setLevel(int level) {
        this.level = level;
        return this;
    }

    public int getWear() {
        return wear;
    }

    public DestinySkin setWear(int wear) {
        this.wear = wear;
        return this;
    }
}
