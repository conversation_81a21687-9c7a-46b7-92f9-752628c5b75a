package xddq.destiny.structs;

import xddq.structs.GameObject;

import java.util.concurrent.ConcurrentHashMap;

//message RelativeData {
//    required int32 id = 1;
//    optional int64 favor = 2;
//    optional int32 challengeId = 3;
//    optional int32 curSkinId = 4;
//    repeated DestinySkinData skinData =5;
//    optional int32 equipLinkageId = 6;
//}
public class Destiny extends GameObject {

    private long favor;

    private int challengeId;

    private int curSkinId;

    private int equipLinkageId;

    private ConcurrentHashMap<Integer, DestinySkin> skins = new ConcurrentHashMap<>();

    public long getFavor() {
        return favor;
    }

    public Destiny setFavor(long favor) {
        this.favor = favor;
        return this;
    }

    public int getChallengeId() {
        return challengeId;
    }

    public Destiny setChallengeId(int challengeId) {
        this.challengeId = challengeId;
        return this;
    }

    public int getCurSkinId() {
        return curSkinId;
    }

    public Destiny setCurSkinId(int curSkinId) {
        this.curSkinId = curSkinId;
        return this;
    }

    public int getEquipLinkageId() {
        return equipLinkageId;
    }

    public Destiny setEquipLinkageId(int equipLinkageId) {
        this.equipLinkageId = equipLinkageId;
        return this;
    }

    public ConcurrentHashMap<Integer, DestinySkin> getSkins() {
        return skins;
    }

    public Destiny setSkins(ConcurrentHashMap<Integer, DestinySkin> skins) {
        this.skins = skins;
        return this;
    }
}
