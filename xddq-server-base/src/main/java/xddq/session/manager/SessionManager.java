package xddq.session.manager;

import io.netty.channel.ChannelHandlerContext;
import xddq.consts.ConstantDefinition;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

public class SessionManager {
    // 角色通信列表
    private final ConcurrentHashMap<Long, ChannelHandlerContext> player_sessions = new ConcurrentHashMap<>();
    // 玩家通信列表
    private final ConcurrentHashMap<Long, ChannelHandlerContext> user_sessions = new ConcurrentHashMap<>();

    /**
     * 注册角色session
     */
    public void registerPlayerSession(long playerId, ChannelHandlerContext session) {
        session.channel().attr(ConstantDefinition.PLAYER_ID).set(playerId);
        player_sessions.put(playerId, session);
    }

    /**
     * 注销角色session
     */
    public void removePlayerSession(long playerId) {
        player_sessions.remove(playerId);
    }

    /**
     * 获取角色session
     */
    public ChannelHandlerContext getPlayerSession(long playerId) {
        return player_sessions.get(playerId);
    }

    /**
     * 获取角色session集合
     */
    public List<ChannelHandlerContext> getPlayerSessions(List<Long> playerIds) {
        List<ChannelHandlerContext> sessions = new ArrayList<>();
        for (long playerId : playerIds) {
            sessions.add(player_sessions.get(playerId));
        }
        return sessions;
    }

    /**
     * 获取全部角色session集合
     */
    public List<ChannelHandlerContext> getAllPlayerSessions() {
        return new ArrayList<>(player_sessions.values());
    }

    /**
     * 获取全部角色session集合大小
     */
    public int getPlayerSessionSize() {
        return player_sessions.size();
    }

    /**
     * 注册帐号session
     */
    public void registerUserSession(long userId, ChannelHandlerContext session) {
        session.channel().attr(ConstantDefinition.USER_ID).set(userId);
        user_sessions.put(userId, session);
    }

    /**
     * 注销账号session
     */
    public void removeUserSession(long userId) {
        ChannelHandlerContext session = user_sessions.remove(userId);
        if (session != null) {
            session.channel().attr(ConstantDefinition.USER_ID).remove();
        }
    }

    /**
     * 获取帐号session
     */
    public ChannelHandlerContext getUserSession(long userId) {
        return user_sessions.get(userId);
    }

    /**
     * 获取全部帐号session集合大小
     */
    public int getUserSessionSize() {
        return user_sessions.size();
    }

    /**
     * 获取玩家所有注册userid
     */
    public Collection<Long> getUserIds() {
        return user_sessions.keySet();
    }
}
