package xddq.session.consts;

import io.netty.util.AttributeKey;

public class SessionConst {

    public static final AttributeKey<String> TOKEN = AttributeKey.newInstance("token");

    public static final AttributeKey<String> USER_NAME = AttributeKey.newInstance("user_name");

    public static final AttributeKey<String> WEB_NAME = AttributeKey.newInstance("web_name");

    public static final AttributeKey<String> DEVICE = AttributeKey.newInstance("device");

    public static final AttributeKey<Long> SESSION_CREATE_TIME = AttributeKey.newInstance("session_create_time");

    public static final AttributeKey<Integer> VIRTUAL_GAME_SERVER_ID = AttributeKey.newInstance("virtual_game_server_id");
}
