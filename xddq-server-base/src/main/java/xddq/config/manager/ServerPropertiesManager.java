package xddq.config.manager;

import org.apache.commons.lang3.StringUtils;

import java.io.FileNotFoundException;
import java.io.IOException;

public class ServerPropertiesManager {

    private final DefaultServerProviser serverProviser = new DefaultServerProviser();

    private static final ServerPropertiesManager manager = new ServerPropertiesManager();

    private ServerPropertiesManager() {

    }

    /**
     * 初始化
     */
    public void initialize(String propertyFile) throws FileNotFoundException, IOException {
        if (!StringUtils.isEmpty(propertyFile)) {
            serverProviser.initialize(propertyFile);
        }
    }

    public static ServerPropertiesManager getInstance() {
        return manager;
    }

    /**
     * 获取系统配置
     */
    public String getProperty(String name, String defaultValue) {
        return serverProviser.getProperty(name, defaultValue);
    }

    /**
     * 设置系统配置
     */
    public void setProperty(String name, String value) {
        if (StringUtils.isEmpty(name) || StringUtils.isEmpty(value)) {
            throw new IllegalArgumentException("name or value is null");
        }

        serverProviser.setProperty(name, value);
    }
}
