package xddq.config.manager;

import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.Properties;

public class DefaultServerProviser {

    private Properties serverProperties = new Properties();

    /**
     * 初始化系统配置
     */
    public void initialize(String propertyFile) throws IOException {
        try (InputStream in = this.getClass().getClassLoader().getResourceAsStream(propertyFile)) {
            serverProperties.load(new InputStreamReader(in, StandardCharsets.UTF_8));
        }
    }

    /**
     * 获取系统配置
     */
    public String getProperty(String name, String defaultValue) {
        String val = serverProperties.getProperty(name, defaultValue);
        return StringUtils.isEmpty(val) ? defaultValue : val.trim();
    }

    /**
     * 设置系统配置
     */
    public void setProperty(String name, String value) {
        serverProperties.setProperty(name, value);
    }
}
