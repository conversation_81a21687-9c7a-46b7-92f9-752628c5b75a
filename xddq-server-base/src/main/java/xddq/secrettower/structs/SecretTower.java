package xddq.secrettower.structs;

import java.util.concurrent.ConcurrentHashMap;

public class SecretTower {
    private int type;
    private int floor;
    //    optional string monsterPower = 3;
    private long passTime;
    // 楼层奖励
    private ConcurrentHashMap<Integer, Integer> stageFloorList = new ConcurrentHashMap<>();
    // 服务器成就奖励
    private ConcurrentHashMap<Integer, Integer> achiFloorList = new ConcurrentHashMap<>();
    private int achiMaxFloor;


    public int getType() {
        return type;
    }

    public SecretTower setType(int type) {
        this.type = type;
        return this;
    }

    public int getFloor() {
        return floor;
    }

    public SecretTower setFloor(int floor) {
        this.floor = floor;
        return this;
    }

    public long getPassTime() {
        return passTime;
    }

    public SecretTower setPassTime(long passTime) {
        this.passTime = passTime;
        return this;
    }

    public ConcurrentHashMap<Integer, Integer> getStageFloorList() {
        return stageFloorList;
    }

    public SecretTower setStageFloorList(ConcurrentHashMap<Integer, Integer> stageFloorList) {
        this.stageFloorList = stageFloorList;
        return this;
    }

    public ConcurrentHashMap<Integer, Integer> getAchiFloorList() {
        return achiFloorList;
    }

    public SecretTower setAchiFloorList(ConcurrentHashMap<Integer, Integer> achiFloorList) {
        this.achiFloorList = achiFloorList;
        return this;
    }

    public int getAchiMaxFloor() {
        return achiMaxFloor;
    }

    public SecretTower setAchiMaxFloor(int achiMaxFloor) {
        this.achiMaxFloor = achiMaxFloor;
        return this;
    }
}
