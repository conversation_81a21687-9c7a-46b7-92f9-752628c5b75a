package xddq.secrettower.structs;

import java.util.concurrent.ConcurrentHashMap;

public class SecretTowerData {

    private ConcurrentHashMap<Integer, SecretTower> towers = new ConcurrentHashMap<>();

    public ConcurrentHashMap<Integer, SecretTower> getTowers() {
        return towers;
    }

    public SecretTowerData setTowers(ConcurrentHashMap<Integer, SecretTower> towers) {
        this.towers = towers;
        return this;
    }
}
