package xddq.career.structs;

public class CareerBossData {
//    message CareerBossData{
//        optional int32 hasPassBossId = 1;
//        optional int32 battleTimesToday = 2;//当日挑战同一关的次数
//        optional int32 repeatTimesToday = 3;//当日扫荡次数
//    }
    private int hasPassBossId;

    public int getHasPassBossId() {
        return hasPassBossId;
    }

    public void setHasPassBossId(int hasPassBossId) {
        this.hasPassBossId = hasPassBossId;
    }
}
