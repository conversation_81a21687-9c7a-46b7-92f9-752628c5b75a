package xddq.career.structs;



import java.util.concurrent.CancellationException;
import java.util.concurrent.ConcurrentHashMap;

public class CareerData {
//    message CareerPlayerDataMsg{
//        optional int32 careerType = 1;//职业类型
//        optional CareerTalentNodeData maxTalentNode = 2;//到达的最高的节点
//        optional int32 changeCareerTimesToday = 3;//今天已转职次数
//        optional CareerBossData bossData = 4;//挑战boss进度
//        optional int64 firstSwitchCareerTime = 5;//游戏生涯初次转职时间
//    }
    private int careerType;
    private CareerTalentNodeData maxTalentNode;
    private int changeCareerTimesToday;

    private CareerBossData bossData = new CareerBossData();
    private long firstSwitchCareerTime;

    private ConcurrentHashMap<Integer, Long> attrMap = new ConcurrentHashMap<>();

    public ConcurrentHashMap<Integer, Long> getAttrMap() {
        return attrMap;
    }

    public void setAttrMap(ConcurrentHashMap<Integer, Long> attrMap) {
        this.attrMap = attrMap;
    }

    public int getCareerType() {
        return careerType;
    }

    public void setCareerType(int careerType) {
        this.careerType = careerType;
    }

    public CareerTalentNodeData getMaxTalentNode() {
        return maxTalentNode;
    }

    public void setMaxTalentNode(CareerTalentNodeData maxTalentNode) {
        this.maxTalentNode = maxTalentNode;
    }

    public int getChangeCareerTimesToday() {
        return changeCareerTimesToday;
    }

    public void setChangeCareerTimesToday(int changeCareerTimesToday) {
        this.changeCareerTimesToday = changeCareerTimesToday;
    }

    public CareerBossData getBossData() {
        return bossData;
    }

    public void setBossData(CareerBossData bossData) {
        this.bossData = bossData;
    }

    public long getFirstSwitchCareerTime() {
        return firstSwitchCareerTime;
    }

    public void setFirstSwitchCareerTime(long firstSwitchCareerTime) {
        this.firstSwitchCareerTime = firstSwitchCareerTime;
    }
}
