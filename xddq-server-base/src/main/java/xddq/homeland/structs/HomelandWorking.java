package xddq.homeland.structs;

public class HomelandWorking {
    private long playerId;
    private String nickName;
    private int headIcon;
    private int workerNum;
    private boolean isWinner;
    private String wxHeadUrl;
    private HeadInfo headInfo = new HeadInfo();

    public long getPlayerId() {
        return playerId;
    }

    public HomelandWorking setPlayerId(long playerId) {
        this.playerId = playerId;
        return this;
    }

    public String getNickName() {
        return nickName;
    }

    public HomelandWorking setNickName(String nickName) {
        this.nickName = nickName;
        return this;
    }

    public int getHeadIcon() {
        return headIcon;
    }

    public HomelandWorking setHeadIcon(int headIcon) {
        this.headIcon = headIcon;
        return this;
    }

    public int getWorkerNum() {
        return workerNum;
    }

    public HomelandWorking setWorkerNum(int workerNum) {
        this.workerNum = workerNum;
        return this;
    }

    public boolean isWinner() {
        return isWinner;
    }

    public HomelandWorking setWinner(boolean winner) {
        isWinner = winner;
        return this;
    }

    public String getWxHeadUrl() {
        return wxHeadUrl;
    }

    public HomelandWorking setWxHeadUrl(String wxHeadUrl) {
        this.wxHeadUrl = wxHeadUrl;
        return this;
    }

    public HeadInfo getHeadInfo() {
        return headInfo;
    }

    public HomelandWorking setHeadInfo(HeadInfo headInfo) {
        this.headInfo = headInfo;
        return this;
    }
}
