package xddq.homeland.structs;

public class ItemSettings {

    private long ItemId;
    private int minItemLv;
    private boolean isCheck;

    public long getItemId() {
        return ItemId;
    }

    public void setItemId(long itemId) {
        ItemId = itemId;
    }

    public int getMinItemLv() {
        return minItemLv;
    }

    public void setMinItemLv(int minItemLv) {
        this.minItemLv = minItemLv;
    }

    public boolean isCheck() {
        return isCheck;
    }

    public void setCheck(boolean check) {
        isCheck = check;
    }
}
