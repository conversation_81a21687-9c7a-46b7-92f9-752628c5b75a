package xddq.homeland.structs;

import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

public class Homeland {
    private long playerId;
    private HomelandPlayerInfo info;
    private int total;
    private int workingNum;
    private int energy;
    private int lv;

    private ConcurrentHashMap<Integer, HomelandItem> items = new ConcurrentHashMap<>();
    private ConcurrentHashMap<Integer, CornucopiaInfo> CornucopiaInfo = new ConcurrentHashMap<>();
    private long nextFreshTime;
    private long lastFreeRefreshTime;
    private long lastExpRefreshTime;
    private int superRefreshTimes;
    private ConcurrentHashMap<Integer, Long> rewards = new ConcurrentHashMap<>();
    private ConcurrentHashMap<Integer, Long> homelandTime = new ConcurrentHashMap<>();
    private ConcurrentHashMap<Long, ConcurrentHashMap<Integer, Long>> roundTime = new ConcurrentHashMap<>();
    private ConcurrentHashMap<Long, Long> rounds = new ConcurrentHashMap<>();
    private ConcurrentHashMap<Long, RoundData> roundData = new ConcurrentHashMap<>();
    private ConcurrentHashMap<Long, Long> enemy = new ConcurrentHashMap<>();
    private long roundRefreshTime;
    // 自动采集相关
    private long expireTime;
    private int freeTimes;
    private ConcurrentHashMap<Integer, HomelandAuto> autoCollects = new ConcurrentHashMap<>();
    private long nextTime;

    public long getPlayerId() {
        return playerId;
    }

    public Homeland setPlayerId(long playerId) {
        this.playerId = playerId;
        return this;
    }

    public HomelandPlayerInfo getInfo() {
        return info;
    }

    public Homeland setInfo(HomelandPlayerInfo info) {
        this.info = info;
        return this;
    }

    public int getTotal() {
        return total;
    }

    public Homeland setTotal(int total) {
        this.total = total;
        return this;
    }

    public int getWorkingNum() {
        return workingNum;
    }

    public Homeland setWorkingNum(int workingNum) {
        this.workingNum = workingNum;
        return this;
    }

    public int getEnergy() {
        return energy;
    }

    public Homeland setEnergy(int energy) {
        this.energy = energy;
        return this;
    }

    public int getLv() {
        return lv;
    }

    public Homeland setLv(int lv) {
        this.lv = lv;
        return this;
    }

    public long getExpireTime() {
        return expireTime;
    }

    public Homeland setExpireTime(long expireTime) {
        this.expireTime = expireTime;
        return this;
    }

    public int getFreeTimes() {
        return freeTimes;
    }

    public Homeland setFreeTimes(int freeTimes) {
        this.freeTimes = freeTimes;
        return this;
    }

    public ConcurrentHashMap<Integer, HomelandAuto> getAutoCollects() {
        return autoCollects;
    }

    public Homeland setAutoCollects(ConcurrentHashMap<Integer, HomelandAuto> autoCollects) {
        this.autoCollects = autoCollects;
        return this;
    }

    public long getNextTime() {
        return nextTime;
    }

    public Homeland setNextTime(long nextTime) {
        this.nextTime = nextTime;
        return this;
    }

    public ConcurrentHashMap<Integer, HomelandItem> getItems() {
        return items;
    }

    public Homeland setItems(ConcurrentHashMap<Integer, HomelandItem> items) {
        this.items = items;
        return this;
    }

    public long getNextFreshTime() {
        return nextFreshTime;
    }

    public Homeland setNextFreshTime(long nextFreshTime) {
        this.nextFreshTime = nextFreshTime;
        return this;
    }

    public long getLastFreeRefreshTime() {
        return lastFreeRefreshTime;
    }

    public Homeland setLastFreeRefreshTime(long lastFreeRefreshTime) {
        this.lastFreeRefreshTime = lastFreeRefreshTime;
        return this;
    }

    public int getSuperRefreshTimes() {
        return superRefreshTimes;
    }

    public Homeland setSuperRefreshTimes(int superRefreshTimes) {
        this.superRefreshTimes = superRefreshTimes;
        return this;
    }

    public ConcurrentHashMap<Integer, Long> getRewards() {
        return rewards;
    }

    public Homeland setRewards(ConcurrentHashMap<Integer, Long> rewards) {
        this.rewards = rewards;
        return this;
    }

    public ConcurrentHashMap<Long, Long> getRounds() {
        return rounds;
    }

    public Homeland setRounds(ConcurrentHashMap<Long, Long> rounds) {
        this.rounds = rounds;
        return this;
    }

    public ConcurrentHashMap<Long, Long> getEnemy() {
        return enemy;
    }

    public Homeland setEnemy(ConcurrentHashMap<Long, Long> enemy) {
        this.enemy = enemy;
        return this;
    }

    public long getRoundRefreshTime() {
        return roundRefreshTime;
    }

    public Homeland setRoundRefreshTime(long roundRefreshTime) {
        this.roundRefreshTime = roundRefreshTime;
        return this;
    }

    public ConcurrentHashMap<Integer, Long> getHomelandTime() {
        return homelandTime;
    }

    public void setHomelandTime(ConcurrentHashMap<Integer, Long> homelandTime) {
        this.homelandTime = homelandTime;
    }

    public ConcurrentHashMap<Integer, CornucopiaInfo> getCornucopiaInfo() {
        return CornucopiaInfo;
    }

    public void setCornucopiaInfo(ConcurrentHashMap<Integer, CornucopiaInfo> cornucopiaInfo) {
        CornucopiaInfo = cornucopiaInfo;
    }

    public ConcurrentHashMap<Long, RoundData> getRoundData() {
        return roundData;
    }

    public void setRoundData(ConcurrentHashMap<Long, RoundData> roundData) {
        this.roundData = roundData;
    }

    public ConcurrentHashMap<Long, ConcurrentHashMap<Integer, Long>> getRoundTime() {
        return roundTime;
    }

    public void setRoundTime(ConcurrentHashMap<Long, ConcurrentHashMap<Integer, Long>> roundTime) {
        this.roundTime = roundTime;
    }

    public long getLastExpRefreshTime() {
        return lastExpRefreshTime;
    }

    public void setLastExpRefreshTime(long lastExpRefreshTime) {
        this.lastExpRefreshTime = lastExpRefreshTime;
    }
}
