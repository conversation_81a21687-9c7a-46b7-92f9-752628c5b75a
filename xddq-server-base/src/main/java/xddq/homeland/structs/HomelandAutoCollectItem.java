package xddq.homeland.structs;

public class HomelandAutoCollectItem {
//    message HomelandAutoCollectItem{
//        optional int32 ItemId = 1;
//        optional int32 minItemLv = 2;
//        optional bool isCheck = 3; //是否已勾选
//    }
    private int ItemId;
    private int minItemLv;
    private boolean isCheck;

    public int getItemId() {
        return ItemId;
    }

    public void setItemId(int itemId) {
        ItemId = itemId;
    }

    public int getMinItemLv() {
        return minItemLv;
    }

    public void setMinItemLv(int minItemLv) {
        this.minItemLv = minItemLv;
    }

    public boolean isCheck() {
        return isCheck;
    }

    public void setCheck(boolean check) {
        isCheck = check;
    }
}
