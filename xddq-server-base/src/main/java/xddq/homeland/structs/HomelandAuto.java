package xddq.homeland.structs;

public class HomelandAuto {
    private int itemId;
    private int minItemLv;
    private boolean check;

    public int getItemId() {
        return itemId;
    }

    public HomelandAuto setItemId(int itemId) {
        this.itemId = itemId;
        return this;
    }

    public int getMinItemLv() {
        return minItemLv;
    }

    public HomelandAuto setMinItemLv(int minItemLv) {
        this.minItemLv = minItemLv;
        return this;
    }

    public boolean isCheck() {
        return check;
    }

    public HomelandAuto setCheck(boolean check) {
        this.check = check;
        return this;
    }
}
