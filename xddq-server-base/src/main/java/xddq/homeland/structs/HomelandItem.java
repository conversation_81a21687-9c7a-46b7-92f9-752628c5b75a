package xddq.homeland.structs;

import xddq.structs.GameObject;

public class HomelandItem extends GameObject {
    private int itemId;
    private int num;
    private int rewardLv;
    private int distance;
    private int pos;
    private int maxWorkerNum;
    private HomelandWorking owner;
    private HomelandWorking enemy;
    private long finishTime;
    private long playerId;
    private boolean onlyOwnerPull;
    private long refreshTime;
    private boolean reward;

    public int getItemId() {
        return itemId;
    }

    public HomelandItem setItemId(int itemId) {
        this.itemId = itemId;
        return this;
    }

    public int getNum() {
        return num;
    }

    public HomelandItem setNum(int num) {
        this.num = num;
        return this;
    }

    public int getRewardLv() {
        return rewardLv;
    }

    public HomelandItem setRewardLv(int rewardLv) {
        this.rewardLv = rewardLv;
        return this;
    }

    public int getDistance() {
        return distance;
    }

    public HomelandItem setDistance(int distance) {
        this.distance = distance;
        return this;
    }

    public int getPos() {
        return pos;
    }

    public HomelandItem setPos(int pos) {
        this.pos = pos;
        return this;
    }

    public int getMaxWorkerNum() {
        return maxWorkerNum;
    }

    public HomelandItem setMaxWorkerNum(int maxWorkerNum) {
        this.maxWorkerNum = maxWorkerNum;
        return this;
    }

    public HomelandWorking getOwner() {
        return owner;
    }

    public HomelandItem setOwner(HomelandWorking owner) {
        this.owner = owner;
        return this;
    }

    public HomelandWorking getEnemy() {
        return enemy;
    }

    public HomelandItem setEnemy(HomelandWorking enemy) {
        this.enemy = enemy;
        return this;
    }

    public long getFinishTime() {
        return finishTime;
    }

    public HomelandItem setFinishTime(long finishTime) {
        this.finishTime = finishTime;
        return this;
    }

    public long getPlayerId() {
        return playerId;
    }

    public HomelandItem setPlayerId(long playerId) {
        this.playerId = playerId;
        return this;
    }

    public boolean isOnlyOwnerPull() {
        return onlyOwnerPull;
    }

    public HomelandItem setOnlyOwnerPull(boolean onlyOwnerPull) {
        this.onlyOwnerPull = onlyOwnerPull;
        return this;
    }

    public long getRefreshTime() {
        return refreshTime;
    }

    public HomelandItem setRefreshTime(long refreshTime) {
        this.refreshTime = refreshTime;
        return this;
    }

    public boolean isReward() {
        return reward;
    }

    public HomelandItem setReward(boolean reward) {
        this.reward = reward;
        return this;
    }
}
