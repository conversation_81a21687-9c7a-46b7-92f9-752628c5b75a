package xddq.homeland.structs;

public class HomelandPlayerInfo {
    private String nickName;
    private int headIcon;
    private String wxHeadUrl;
    private String headInfo;
    private int serverId;
    private int equipHeadIconFrame;

    public String getNickName() {
        return nickName;
    }

    public HomelandPlayerInfo setNickName(String nickName) {
        this.nickName = nickName;
        return this;
    }

    public int getHeadIcon() {
        return headIcon;
    }

    public HomelandPlayerInfo setHeadIcon(int headIcon) {
        this.headIcon = headIcon;
        return this;
    }

    public String getWxHeadUrl() {
        return wxHeadUrl;
    }

    public HomelandPlayerInfo setWxHeadUrl(String wxHeadUrl) {
        this.wxHeadUrl = wxHeadUrl;
        return this;
    }

    public String getHeadInfo() {
        return headInfo;
    }

    public HomelandPlayerInfo setHeadInfo(String headInfo) {
        this.headInfo = headInfo;
        return this;
    }

    public int getServerId() {
        return serverId;
    }

    public HomelandPlayerInfo setServerId(int serverId) {
        this.serverId = serverId;
        return this;
    }

    public int getEquipHeadIconFrame() {
        return equipHeadIconFrame;
    }

    public void setEquipHeadIconFrame(int equipHeadIconFrame) {
        this.equipHeadIconFrame = equipHeadIconFrame;
    }
}
