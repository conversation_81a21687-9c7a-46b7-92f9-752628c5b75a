package xddq.homeland.structs;

import java.util.concurrent.ConcurrentHashMap;

public class RoundData {
    private long playerId;
    private HomelandPlayerInfo info;
    private ConcurrentHashMap<Integer, HomelandItem> items = new ConcurrentHashMap<>();

    public long getPlayerId() {
        return playerId;
    }

    public void setPlayerId(long playerId) {
        this.playerId = playerId;
    }

    public HomelandPlayerInfo getInfo() {
        return info;
    }

    public void setInfo(HomelandPlayerInfo info) {
        this.info = info;
    }

    public ConcurrentHashMap<Integer, HomelandItem> getItems() {
        return items;
    }

    public void setItems(ConcurrentHashMap<Integer, HomelandItem> items) {
        this.items = items;
    }
}
