package xddq.homeland.structs;

public class HeadInfo {
    private long playerId;
    private int headIcon;
    private String wxHeadUrl;
    private int equipHeadIconFrame;

    public long getPlayerId() {
        return playerId;
    }

    public void setPlayerId(long playerId) {
        this.playerId = playerId;
    }

    public int getHeadIcon() {
        return headIcon;
    }

    public void setHeadIcon(int headIcon) {
        this.headIcon = headIcon;
    }

    public String getWxHeadUrl() {
        return wxHeadUrl;
    }

    public void setWxHeadUrl(String wxHeadUrl) {
        this.wxHeadUrl = wxHeadUrl;
    }

    public int getEquipHeadIconFrame() {
        return equipHeadIconFrame;
    }

    public void setEquipHeadIconFrame(int equipHeadIconFrame) {
        this.equipHeadIconFrame = equipHeadIconFrame;
    }
}
