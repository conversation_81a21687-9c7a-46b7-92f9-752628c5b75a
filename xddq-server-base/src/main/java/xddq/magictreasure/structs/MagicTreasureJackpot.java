package xddq.magictreasure.structs;

import xddq.structs.GameObject;

public class MagicTreasureJackpot extends GameObject {

    private int drawTimes;

    private long lastAdTime;

    public int getDrawTimes() {
        return drawTimes;
    }

    public MagicTreasureJackpot setDrawTimes(int drawTimes) {
        this.drawTimes = drawTimes;
        return this;
    }

    public long getLastAdTime() {
        return lastAdTime;
    }

    public MagicTreasureJackpot setLastAdTime(long lastAdTime) {
        this.lastAdTime = lastAdTime;
        return this;
    }
}
