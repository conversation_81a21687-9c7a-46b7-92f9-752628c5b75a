package xddq.magictreasure.structs;

import java.util.concurrent.ConcurrentHashMap;

public class MagicTreasureData {

    private ConcurrentHashMap<Integer, MagicTreasure> magicTreasures = new ConcurrentHashMap<>();

    private ConcurrentHashMap<Integer, MagicTreasureJackpot> jackpots = new ConcurrentHashMap<>();

    public ConcurrentHashMap<Integer, MagicTreasure> getMagicTreasures() {
        return magicTreasures;
    }

    public MagicTreasureData setMagicTreasures(ConcurrentHashMap<Integer, MagicTreasure> magicTreasures) {
        this.magicTreasures = magicTreasures;
        return this;
    }

    public ConcurrentHashMap<Integer, MagicTreasureJackpot> getJackpots() {
        return jackpots;
    }

    public MagicTreasureData setJackpots(ConcurrentHashMap<Integer, MagicTreasureJackpot> jackpots) {
        this.jackpots = jackpots;
        return this;
    }
}
