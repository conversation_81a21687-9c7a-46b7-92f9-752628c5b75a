package xddq.magictreasure.structs;

import xddq.structs.GameObject;

public class MagicTreasure extends GameObject {

    private int starLv;
    private int lv;
    private int cumulativeNum;
    private int equipLinkageId;

    public int getStarLv() {
        return starLv;
    }

    public MagicTreasure setStarLv(int starLv) {
        this.starLv = starLv;
        return this;
    }

    public int getLv() {
        return lv;
    }

    public MagicTreasure setLv(int lv) {
        this.lv = lv;
        return this;
    }

    public int getCumulativeNum() {
        return cumulativeNum;
    }

    public MagicTreasure setCumulativeNum(int cumulativeNum) {
        this.cumulativeNum = cumulativeNum;
        return this;
    }

    public int getEquipLinkageId() {
        return equipLinkageId;
    }

    public MagicTreasure setEquipLinkageId(int equipLinkageId) {
        this.equipLinkageId = equipLinkageId;
        return this;
    }
}
