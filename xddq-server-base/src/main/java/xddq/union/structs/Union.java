package xddq.union.structs;

import xddq.count.structs.Count;
import xddq.count.structs.ICountable;

import java.util.concurrent.ConcurrentHashMap;

public class Union implements ICountable {
    // 基础信息
    private long unionId;
    private long exp;
    private int flag;
    private String name;
    private boolean isFreedom;
    private int limitRealmsId;
    private int serverId;
    private int dan;
    private int danStar;
    private boolean cross;
    private int unionNameTitleId;
    private String notice;
    private String slogan;
    private String wCcode;
    private long createTime;
    private boolean disband;

    private transient long lastSaveTime;
    private ConcurrentHashMap<Integer, Integer> grades = new ConcurrentHashMap<>();

    private long leader;
    private ConcurrentHashMap<Long, UnionMember> members = new ConcurrentHashMap<>();
    private ConcurrentHashMap<Long, Boolean> deputy = new ConcurrentHashMap<>();
    private ConcurrentHashMap<Long, Boolean> elite = new ConcurrentHashMap<>();
    private ConcurrentHashMap<Long, UnionApply> applies = new ConcurrentHashMap<>();
    private UnionBargain bargain = new UnionBargain();

    /************************ 计数属性开始 ************************/
    // 计数
    private ConcurrentHashMap<String, Count> counts = new ConcurrentHashMap<>();
    /************************ 计数属性结束 ************************/

    public long getUnionId() {
        return unionId;
    }

    public Union setUnionId(long unionId) {
        this.unionId = unionId;
        return this;
    }

    public long getExp() {
        return exp;
    }

    public Union setExp(long exp) {
        this.exp = exp;
        return this;
    }

    public int getFlag() {
        return flag;
    }

    public Union setFlag(int flag) {
        this.flag = flag;
        return this;
    }

    public String getName() {
        return name;
    }

    public Union setName(String name) {
        this.name = name;
        return this;
    }

    public boolean isFreedom() {
        return isFreedom;
    }

    public Union setFreedom(boolean freedom) {
        isFreedom = freedom;
        return this;
    }

    public int getLimitRealmsId() {
        return limitRealmsId;
    }

    public Union setLimitRealmsId(int limitRealmsId) {
        this.limitRealmsId = limitRealmsId;
        return this;
    }

    public int getServerId() {
        return serverId;
    }

    public Union setServerId(int serverId) {
        this.serverId = serverId;
        return this;
    }

    public int getDan() {
        return dan;
    }

    public Union setDan(int dan) {
        this.dan = dan;
        return this;
    }

    public int getDanStar() {
        return danStar;
    }

    public Union setDanStar(int danStar) {
        this.danStar = danStar;
        return this;
    }

    public boolean isCross() {
        return cross;
    }

    public Union setCross(boolean cross) {
        this.cross = cross;
        return this;
    }

    public int getUnionNameTitleId() {
        return unionNameTitleId;
    }

    public Union setUnionNameTitleId(int unionNameTitleId) {
        this.unionNameTitleId = unionNameTitleId;
        return this;
    }

    public long getLeader() {
        return leader;
    }

    public Union setLeader(long leader) {
        this.leader = leader;
        return this;
    }

    public ConcurrentHashMap<Long, UnionMember> getMembers() {
        return members;
    }

    public Union setMembers(ConcurrentHashMap<Long, UnionMember> members) {
        this.members = members;
        return this;
    }

    public ConcurrentHashMap<Long, Boolean> getDeputy() {
        return deputy;
    }

    public Union setDeputy(ConcurrentHashMap<Long, Boolean> deputy) {
        this.deputy = deputy;
        return this;
    }

    public ConcurrentHashMap<Long, Boolean> getElite() {
        return elite;
    }

    public Union setElite(ConcurrentHashMap<Long, Boolean> elite) {
        this.elite = elite;
        return this;
    }

    public String getNotice() {
        return notice;
    }

    public Union setNotice(String notice) {
        this.notice = notice;
        return this;
    }

    public String getSlogan() {
        return slogan;
    }

    public Union setSlogan(String slogan) {
        this.slogan = slogan;
        return this;
    }

    public String getwCcode() {
        return wCcode;
    }

    public Union setwCcode(String wCcode) {
        this.wCcode = wCcode;
        return this;
    }

    public long getCreateTime() {
        return createTime;
    }

    public Union setCreateTime(long createTime) {
        this.createTime = createTime;
        return this;
    }

    public boolean isDisband() {
        return disband;
    }

    public Union setDisband(boolean disband) {
        this.disband = disband;
        return this;
    }

    public long getLastSaveTime() {
        return lastSaveTime;
    }

    public Union setLastSaveTime(long lastSaveTime) {
        this.lastSaveTime = lastSaveTime;
        return this;
    }

    public ConcurrentHashMap<Long, UnionApply> getApplies() {
        return applies;
    }

    public Union setApplies(ConcurrentHashMap<Long, UnionApply> applies) {
        this.applies = applies;
        return this;
    }

    public UnionBargain getBargain() {
        return bargain;
    }

    public Union setBargain(UnionBargain bargain) {
        this.bargain = bargain;
        return this;
    }

    public ConcurrentHashMap<Integer, Integer> getGrades() {
        return grades;
    }

    public Union setGrades(ConcurrentHashMap<Integer, Integer> grades) {
        this.grades = grades;
        return this;
    }

    @Override
    public ConcurrentHashMap<String, Count> getCounts() {
        return counts;
    }

    public Union setCounts(ConcurrentHashMap<String, Count> counts) {
        this.counts = counts;
        return this;
    }
}
