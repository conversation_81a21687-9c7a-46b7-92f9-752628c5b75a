package xddq.monster.structs;

import xddq.cloud.structs.CloudData;
import xddq.magic.structs.Magic;
import xddq.pet.structs.Pet;
import xddq.spirit.structs.Spirit;
import xddq.structs.GameObject;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

public class Monster extends GameObject {

    // 当前境界id
    private int realmsId;
    // 全部属性
    private ConcurrentHashMap<Integer, Long> totalAttributes = new ConcurrentHashMap<>();

    private CloudData cloud = new CloudData();

    private List<Pet> pets = new ArrayList<>();

    private List<Spirit> spirits = new ArrayList<>();

    private List<Magic> magics = new ArrayList<>();

    private ConcurrentHashMap<Integer, Integer> otherSkills = new ConcurrentHashMap<>();

    private String name;

    private int appearId;

    private int headId;

    public int getRealmsId() {
        return realmsId;
    }

    private int atkEffectId;
    private int soaringState;
    private int profressionId;


    public Monster setRealmsId(int realmsId) {
        this.realmsId = realmsId;
        return this;
    }

    public ConcurrentHashMap<Integer, Long> getTotalAttributes() {
        return totalAttributes;
    }

    public Monster setTotalAttributes(ConcurrentHashMap<Integer, Long> totalAttributes) {
        this.totalAttributes = totalAttributes;
        return this;
    }

    public CloudData getCloud() {
        return cloud;
    }

    public Monster setCloud(CloudData cloud) {
        this.cloud = cloud;
        return this;
    }

    public List<Pet> getPets() {
        return pets;
    }

    public Monster setPets(List<Pet> pets) {
        this.pets = pets;
        return this;
    }

    public List<Spirit> getSpirits() {
        return spirits;
    }

    public Monster setSpirits(List<Spirit> spirits) {
        this.spirits = spirits;
        return this;
    }

    public List<Magic> getMagics() {
        return magics;
    }

    public Monster setMagics(List<Magic> magics) {
        this.magics = magics;
        return this;
    }

    public ConcurrentHashMap<Integer, Integer> getOtherSkills() {
        return otherSkills;
    }

    public Monster setOtherSkills(ConcurrentHashMap<Integer, Integer> otherSkills) {
        this.otherSkills = otherSkills;
        return this;
    }

    public int getAppearId() {
        return appearId;
    }

    public Monster setAppearId(int appearId) {
        this.appearId = appearId;
        return this;
    }

    public int getHeadId() {
        return headId;
    }

    public Monster setHeadId(int headId) {
        this.headId = headId;
        return this;
    }

    public int getAtkEffectId() {
        return atkEffectId;
    }

    public void setAtkEffectId(int atkEffectId) {
        this.atkEffectId = atkEffectId;
    }

    public int getSoaringState() {
        return soaringState;
    }

    public void setSoaringState(int soaringState) {
        this.soaringState = soaringState;
    }

    public int getProfressionId() {
        return profressionId;
    }

    public void setProfressionId(int professionId) {
        this.profressionId = professionId;
    }

    public String getName() {
        return name;
    }

    public Monster setName(String name) {
        this.name = name;
        return this;
    }
}
