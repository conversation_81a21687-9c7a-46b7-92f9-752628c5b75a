package xddq.battle.structs;

import java.util.Vector;

public class BattleData {

    private Vector<Long> battlePlayerList = new Vector<>();

    private long score;

    public Vector<Long> getBattlePlayerList() {
        return battlePlayerList;
    }

    public BattleData setBattlePlayerList(Vector<Long> battlePlayerList) {
        this.battlePlayerList = battlePlayerList;
        return this;
    }

    public long getScore() {
        return score;
    }

    public BattleData setScore(long score) {
        this.score = score;
        return this;
    }
}
