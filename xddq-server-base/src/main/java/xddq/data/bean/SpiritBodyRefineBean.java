package xddq.data.bean;

import xddq.data.ExcelBean;

public class SpiritBodyRefineBean extends ExcelBean {
    private int id;
    private int type;
    private String specialAttr;
    private String attributePool;
    private String qualityPool;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getSpecialAttr() {
        return specialAttr;
    }

    public void setSpecialAttr(String specialAttr) {
        this.specialAttr = specialAttr;
    }

    public String getAttributePool() {
        return attributePool;
    }

    public void setAttributePool(String attributePool) {
        this.attributePool = attributePool;
    }

    public String getQualityPool() {
        return qualityPool;
    }

    public void setQualityPool(String qualityPool) {
        this.qualityPool = qualityPool;
    }
}
