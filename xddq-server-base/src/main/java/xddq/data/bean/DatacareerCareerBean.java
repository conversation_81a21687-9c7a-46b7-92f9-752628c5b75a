package xddq.data.bean;

import xddq.data.ExcelBean;

public class DatacareerCareerBean extends ExcelBean {

    //id
    private int id;

    //name
    private String name;

    //desc
    private String desc;

    //icon
    private int icon;

    //effect
    private int effect;


    /**
    * get id
    */
    public int getId(){
    return id;
    }

    /**
    * set id
    */
    public void setId(int id){
    this.id = id;
    }

    /**
    * get name
    */
    public String getName(){
    return name;
    }

    /**
    * set name
    */
    public void setName(String name){
    this.name = name;
    }

    /**
    * get desc
    */
    public String getDesc(){
    return desc;
    }

    /**
    * set desc
    */
    public void setDesc(String desc){
    this.desc = desc;
    }

    /**
    * get icon
    */
    public int getIcon(){
    return icon;
    }

    /**
    * set icon
    */
    public void setIcon(int icon){
    this.icon = icon;
    }

    /**
    * get effect
    */
    public int getEffect(){
    return effect;
    }

    /**
    * set effect
    */
    public void setEffect(int effect){
    this.effect = effect;
    }

}