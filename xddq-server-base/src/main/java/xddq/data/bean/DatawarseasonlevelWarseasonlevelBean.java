package xddq.data.bean;

import xddq.data.ExcelBean;

public class DatawarseasonlevelWarseasonlevelBean extends ExcelBean {

    //id
    private int id;

    //level
    private int level;

    //exp
    private int exp;

    //attack
    private String attack;

    //defence
    private long defence;

    //hp
    private String hp;

    //attrProb
    private int attrProb;

    //type
    private int type;


    /**
    * get id
    */
    public int getId(){
    return id;
    }

    /**
    * set id
    */
    public void setId(int id){
    this.id = id;
    }

    /**
    * get level
    */
    public int getLevel(){
    return level;
    }

    /**
    * set level
    */
    public void setLevel(int level){
    this.level = level;
    }

    /**
    * get exp
    */
    public int getExp(){
    return exp;
    }

    /**
    * set exp
    */
    public void setExp(int exp){
    this.exp = exp;
    }

    /**
    * get attack
    */
    public String getAttack(){
    return attack;
    }

    /**
    * set attack
    */
    public void setAttack(String attack){
    this.attack = attack;
    }

    /**
    * get defence
    */
    public long getDefence(){
    return defence;
    }

    /**
    * set defence
    */
    public void setDefence(long defence){
    this.defence = defence;
    }

    /**
    * get hp
    */
    public String getHp(){
    return hp;
    }

    /**
    * set hp
    */
    public void setHp(String hp){
    this.hp = hp;
    }

    /**
    * get attrProb
    */
    public int getAttrProb(){
    return attrProb;
    }

    /**
    * set attrProb
    */
    public void setAttrProb(int attrProb){
    this.attrProb = attrProb;
    }

    /**
    * get type
    */
    public int getType(){
    return type;
    }

    /**
    * set type
    */
    public void setType(int type){
    this.type = type;
    }

}