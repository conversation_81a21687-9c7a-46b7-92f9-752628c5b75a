package xddq.data.container;

import java.util.HashMap;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import xddq.data.BaseContainer;
import xddq.data.bean.DatasysteminfoSysteminfoBean;

public class DatasysteminfoSysteminfoContainer extends BaseContainer {

    private List<DatasysteminfoSysteminfoBean> list;

    private HashMap<Integer, DatasysteminfoSysteminfoBean> map;

    private int maxSystem;

    public void load() throws Exception {
        list = super.load("data_system_info_system_info.json", DatasysteminfoSysteminfoBean.class);
        map = list.stream().collect(Collectors.toMap(DatasysteminfoSysteminfoBean::getId, v -> v, (v1, v2) -> {
            throw new RuntimeException("data_system_info_system_info DUPLICATE KEY:" + v1.getId());
        }, HashMap::new));
        init();
    }

    @SuppressWarnings("unchecked")
    public List<DatasysteminfoSysteminfoBean> getList(){
        return list;
    }

    public DatasysteminfoSysteminfoBean getDatasysteminfoSysteminfoBean(Integer key){
        DatasysteminfoSysteminfoBean bean = map.get(key);
        if(bean != null){
            return bean;
        }
        throwError(this, key);
        return null;
    }

    public int getMaxSystem() {
        return maxSystem;
    }

    AtomicInteger max = new AtomicInteger(0);
    public void init() {
        list.forEach(bean->{
            if(bean.getId() > max.get()){
                max.set(bean.getId());
            }
        });
        maxSystem = max.get() + 1;
    }
}