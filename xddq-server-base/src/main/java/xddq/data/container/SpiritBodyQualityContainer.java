package xddq.data.container;

import xddq.data.BaseContainer;
import xddq.data.bean.SpiritBodyBean;
import xddq.data.bean.SpiritBodyQualityBean;

import java.util.HashMap;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

public class SpiritBodyQualityContainer extends BaseContainer {

    private List<SpiritBodyQualityBean> list;

    private HashMap<Integer, SpiritBodyQualityBean> map;


    public void load() throws Exception {
        list = super.load("spirit_body_quality.json", SpiritBodyQualityBean.class);
        map = list.stream().collect(Collectors.toMap(SpiritBodyQualityBean::getId, v -> v, (v1, v2) -> {
            throw new RuntimeException("spirit_body_quality DUPLICATE KEY:" + v1.getId());
        }, HashMap::new));
        init();
    }

    @SuppressWarnings("unchecked")
    public List<SpiritBodyQualityBean> getList(){
        return list;
    }

    public SpiritBodyQualityBean getSpiritBodyQualityBean(Integer key){
        SpiritBodyQualityBean bean = map.get(key);
        if(bean != null){
            return bean;
        }
        throwError(this, key);
        return null;
    }

    public void init() {

    }
}