package xddq.data.container;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import xddq.data.BaseContainer;
import xddq.data.bean.DatabattleeffectBattleeffectBean;
import xddq.data.bean.SpiritBodyBean;

public class SpiritBodyContainer extends BaseContainer {

    private List<SpiritBodyBean> list;

    private HashMap<Integer, SpiritBodyBean> map;

    private ConcurrentHashMap<Integer, List<Integer>> combineId2IdListMap;
    private ConcurrentHashMap<Integer, Integer> id2CombineIdMap;

    public int getCombineId(int magicTreasureId) {
        return id2CombineIdMap.getOrDefault(magicTreasureId, 0);
    }

    public List<Integer> getIdList(int combineId) {
        return combineId2IdListMap.getOrDefault(combineId, null);
    }

    public void load() throws Exception {
        list = super.load("spirit_body.json", SpiritBodyBean.class);
        map = list.stream().collect(Collectors.toMap(SpiritBodyBean::getId, v -> v, (v1, v2) -> {
            throw new RuntimeException("spirit_body DUPLICATE KEY:" + v1.getId());
        }, HashMap::new));

        init();
    }

    @SuppressWarnings("unchecked")
    public List<SpiritBodyBean> getList(){
        return list;
    }

    public SpiritBodyBean getSpiritBodyBean(Integer key){
        SpiritBodyBean bean = map.get(key);
        if(bean != null){
            return bean;
        }
        throwError(this, key);
        return null;
    }

    public void init() {
        combineId2IdListMap = new ConcurrentHashMap<>();
        list.forEach(bean -> {
            if (bean.getCombine() != 0) {
                if (!combineId2IdListMap.containsKey(bean.getCombine())) {
                    combineId2IdListMap.put(bean.getCombine(), new ArrayList<>());
                }
                combineId2IdListMap.get(bean.getCombine()).add(bean.getId());
            }
        });

        id2CombineIdMap = new ConcurrentHashMap<>();
        list.forEach(bean -> {
            if (!id2CombineIdMap.containsKey(bean.getId())) {
                id2CombineIdMap.put(bean.getId(), bean.getCombine());
            }
        });
    }
}