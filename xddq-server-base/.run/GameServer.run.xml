<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="GameServer" type="Application" factoryName="Application">
    <option name="MAIN_CLASS_NAME" value="xddq.StartLogic" />
    <module name="xddq-server-logic" />
    <option name="PROGRAM_PARAMETERS" value="game.server.port=8001 game.server.key=1 game.debug=true json.dir=./jsons server.http.port=8080 mybatis.mysql.url=127.0.0.1:3306 mybatis.mysql.db=game mybatis.mysql.user=root mybatis.mysql.password=123456 jedis.redis.host=127.0.0.1 jedis.redis.port=6379 jedis.redis.password=123456 game.server.open=2025-06-01 fight.fighter1=./brief/1.json fight.fighter2=./brief/1.json token.key=1234567890 ws.address=ws://127.0.0.1:8888/ws/127.0.0.1:8001 recharge.game.url=http://127.0.0.1:8080/recharge game.server.reward=0" />
    <option name="WORKING_DIRECTORY" value="$MODULE_WORKING_DIR$" />
    <extension name="coverage">
      <pattern>
        <option name="PATTERN" value="xddq.consts.*" />
        <option name="ENABLED" value="true" />
      </pattern>
    </extension>
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>