<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="GateServer" type="Application" factoryName="Application">
    <option name="MAIN_CLASS_NAME" value="xddq.StartGate" />
    <module name="xddq-gate" />
    <option name="PROGRAM_PARAMETERS" value="gate.server.port=8888" />
    <extension name="coverage">
      <pattern>
        <option name="PATTERN" value="xddq.*" />
        <option name="ENABLED" value="true" />
      </pattern>
    </extension>
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>