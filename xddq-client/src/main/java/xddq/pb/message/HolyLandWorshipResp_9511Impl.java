package xddq.pb.message;

import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import xddq.message.pb.PbMessage;


public class HolyLandWorshipResp_9511Impl extends PbMessage {

    private Xddq.HolyLandWorshipResp msg;

    private Xddq.HolyLandWorshipResp.Builder builder;

    public HolyLandWorshipResp_9511Impl() {
    }

    public HolyLandWorshipResp_9511Impl(Xddq.HolyLandWorshipResp.Builder builder) {
        this.builder = builder;
    }

    public boolean write(ByteBuf buf) throws Exception {
        if (msg == null) {
            msg = builder.build();
        }
        buf.writeBytes(msg.toByteArray());
        return true;
    }

    public boolean read(ByteBuf buf) throws Exception {
        int length = buf.readableBytes();
        byte[] array;
        int offset;
        if (buf.hasArray()) {
            array = buf.array();
            offset = buf.arrayOffset() + buf.readerIndex();
        } else {
            array = ByteBufUtil.getBytes(buf, buf.readerIndex(), length, false);
            offset = 0;
        }
        this.msg = Xddq.HolyLandWorshipResp.newBuilder().mergeFrom(array, offset, length).build();
        return true;
    }

    public Xddq.HolyLandWorshipResp getMsg() {
        return msg;
    }

    public HolyLandWorshipResp_9511Impl setMsg(Xddq.HolyLandWorshipResp msg) {
        this.msg = msg;
        return this;
    }

    public int length() throws Exception {
        return msg.getSerializedSize();
    }

    @Override
    public String getQueue() {
        return null;
    }

    @Override
    public int getId() {
        return 9511;
    }

    @Override
    public String toString() {
        try {
            return String.format("%d %s", this.getId(), JsonFormat.printer().print(this.getMsg()));
        } catch (InvalidProtocolBufferException ignored) {
        }
        return String.format("%d error message", this.getId());
    }
}
