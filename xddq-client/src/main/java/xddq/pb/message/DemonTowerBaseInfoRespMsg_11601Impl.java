package xddq.pb.message;

import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import xddq.message.pb.PbMessage;


public class DemonTowerBaseInfoRespMsg_11601Impl extends PbMessage {

    private Xddq.DemonTowerBaseInfoRespMsg msg;

    private Xddq.DemonTowerBaseInfoRespMsg.Builder builder;

    public DemonTowerBaseInfoRespMsg_11601Impl() {
    }

    public DemonTowerBaseInfoRespMsg_11601Impl(Xddq.DemonTowerBaseInfoRespMsg.Builder builder) {
        this.builder = builder;
    }

    public boolean write(ByteBuf buf) throws Exception {
        if (msg == null) {
            msg = builder.build();
        }
        buf.writeBytes(msg.toByteArray());
        return true;
    }

    public boolean read(ByteBuf buf) throws Exception {
        int length = buf.readableBytes();
        byte[] array;
        int offset;
        if (buf.hasArray()) {
            array = buf.array();
            offset = buf.arrayOffset() + buf.readerIndex();
        } else {
            array = ByteBufUtil.getBytes(buf, buf.readerIndex(), length, false);
            offset = 0;
        }
        this.msg = Xddq.DemonTowerBaseInfoRespMsg.newBuilder().mergeFrom(array, offset, length).build();
        return true;
    }

    public Xddq.DemonTowerBaseInfoRespMsg getMsg() {
        return msg;
    }

    public DemonTowerBaseInfoRespMsg_11601Impl setMsg(Xddq.DemonTowerBaseInfoRespMsg msg) {
        this.msg = msg;
        return this;
    }

    public int length() throws Exception {
        return msg.getSerializedSize();
    }

    @Override
    public String getQueue() {
        return null;
    }

    @Override
    public int getId() {
        return 11601;
    }

    @Override
    public String toString() {
        try {
            return String.format("%d %s", this.getId(), JsonFormat.printer().print(this.getMsg()));
        } catch (InvalidProtocolBufferException ignored) {
        }
        return String.format("%d error message", this.getId());
    }
}
