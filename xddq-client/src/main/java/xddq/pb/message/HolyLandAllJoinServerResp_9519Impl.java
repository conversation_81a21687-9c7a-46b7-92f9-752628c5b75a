package xddq.pb.message;

import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import xddq.message.pb.PbMessage;


public class HolyLandAllJoinServerResp_9519Impl extends PbMessage {

    private Xddq.HolyLandAllJoinServerResp msg;

    private Xddq.HolyLandAllJoinServerResp.Builder builder;

    public HolyLandAllJoinServerResp_9519Impl() {
    }

    public HolyLandAllJoinServerResp_9519Impl(Xddq.HolyLandAllJoinServerResp.Builder builder) {
        this.builder = builder;
    }

    public boolean write(ByteBuf buf) throws Exception {
        if (msg == null) {
            msg = builder.build();
        }
        buf.writeBytes(msg.toByteArray());
        return true;
    }

    public boolean read(ByteBuf buf) throws Exception {
        int length = buf.readableBytes();
        byte[] array;
        int offset;
        if (buf.hasArray()) {
            array = buf.array();
            offset = buf.arrayOffset() + buf.readerIndex();
        } else {
            array = ByteBufUtil.getBytes(buf, buf.readerIndex(), length, false);
            offset = 0;
        }
        this.msg = Xddq.HolyLandAllJoinServerResp.newBuilder().mergeFrom(array, offset, length).build();
        return true;
    }

    public Xddq.HolyLandAllJoinServerResp getMsg() {
        return msg;
    }

    public HolyLandAllJoinServerResp_9519Impl setMsg(Xddq.HolyLandAllJoinServerResp msg) {
        this.msg = msg;
        return this;
    }

    public int length() throws Exception {
        return msg.getSerializedSize();
    }

    @Override
    public String getQueue() {
        return null;
    }

    @Override
    public int getId() {
        return 9519;
    }

    @Override
    public String toString() {
        try {
            return String.format("%d %s", this.getId(), JsonFormat.printer().print(this.getMsg()));
        } catch (InvalidProtocolBufferException ignored) {
        }
        return String.format("%d error message", this.getId());
    }
}
