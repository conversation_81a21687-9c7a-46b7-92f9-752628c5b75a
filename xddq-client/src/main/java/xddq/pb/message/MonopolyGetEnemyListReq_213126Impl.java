package xddq.pb.message;

import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import xddq.message.pb.PbMessage;


public class MonopolyGetEnemyListReq_213126Impl extends PbMessage {

    private Xddq.MonopolyGetEnemyListReq msg;

    private Xddq.MonopolyGetEnemyListReq.Builder builder;

    public MonopolyGetEnemyListReq_213126Impl() {
    }

    public MonopolyGetEnemyListReq_213126Impl(Xddq.MonopolyGetEnemyListReq.Builder builder) {
        this.builder = builder;
    }

    public boolean write(ByteBuf buf) throws Exception {
        if (msg == null) {
            msg = builder.build();
        }
        buf.writeBytes(msg.toByteArray());
        return true;
    }

    public boolean read(ByteBuf buf) throws Exception {
        int length = buf.readableBytes();
        byte[] array;
        int offset;
        if (buf.hasArray()) {
            array = buf.array();
            offset = buf.arrayOffset() + buf.readerIndex();
        } else {
            array = ByteBufUtil.getBytes(buf, buf.readerIndex(), length, false);
            offset = 0;
        }
        this.msg = Xddq.MonopolyGetEnemyListReq.newBuilder().mergeFrom(array, offset, length).build();
        return true;
    }

    public Xddq.MonopolyGetEnemyListReq getMsg() {
        return msg;
    }

    public MonopolyGetEnemyListReq_213126Impl setMsg(Xddq.MonopolyGetEnemyListReq msg) {
        this.msg = msg;
        return this;
    }

    public int length() throws Exception {
        return msg.getSerializedSize();
    }

    @Override
    public String getQueue() {
        return null;
    }

    @Override
    public int getId() {
        return 213126;
    }

    @Override
    public String toString() {
        try {
            return String.format("%d %s", this.getId(), JsonFormat.printer().print(this.getMsg()));
        } catch (InvalidProtocolBufferException ignored) {
        }
        return String.format("%d error message", this.getId());
    }
}
