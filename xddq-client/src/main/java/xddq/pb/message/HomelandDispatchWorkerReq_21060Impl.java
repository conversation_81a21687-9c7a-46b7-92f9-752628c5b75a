package xddq.pb.message;

import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import xddq.message.pb.PbMessage;


public class HomelandDispatchWorkerReq_21060Impl extends PbMessage {

    private Xddq.HomelandDispatchWorkerReq msg;

    private Xddq.HomelandDispatchWorkerReq.Builder builder;

    public HomelandDispatchWorkerReq_21060Impl() {
    }

    public HomelandDispatchWorkerReq_21060Impl(Xddq.HomelandDispatchWorkerReq.Builder builder) {
        this.builder = builder;
    }

    public boolean write(ByteBuf buf) throws Exception {
        if (msg == null) {
            msg = builder.build();
        }
        buf.writeBytes(msg.toByteArray());
        return true;
    }

    public boolean read(ByteBuf buf) throws Exception {
        int length = buf.readableBytes();
        byte[] array;
        int offset;
        if (buf.hasArray()) {
            array = buf.array();
            offset = buf.arrayOffset() + buf.readerIndex();
        } else {
            array = ByteBufUtil.getBytes(buf, buf.readerIndex(), length, false);
            offset = 0;
        }
        this.msg = Xddq.HomelandDispatchWorkerReq.newBuilder().mergeFrom(array, offset, length).build();
        return true;
    }

    public Xddq.HomelandDispatchWorkerReq getMsg() {
        return msg;
    }

    public HomelandDispatchWorkerReq_21060Impl setMsg(Xddq.HomelandDispatchWorkerReq msg) {
        this.msg = msg;
        return this;
    }

    public int length() throws Exception {
        return msg.getSerializedSize();
    }

    @Override
    public String getQueue() {
        return null;
    }

    @Override
    public int getId() {
        return 21060;
    }

    @Override
    public String toString() {
        try {
            return String.format("%d %s", this.getId(), JsonFormat.printer().print(this.getMsg()));
        } catch (InvalidProtocolBufferException ignored) {
        }
        return String.format("%d error message", this.getId());
    }
}
