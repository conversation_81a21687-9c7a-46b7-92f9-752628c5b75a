package xddq.pb.message;

import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import xddq.message.pb.PbMessage;


public class MonopolyGetPlayerRankReq_213125Impl extends PbMessage {

    private Xddq.MonopolyGetPlayerRankReq msg;

    private Xddq.MonopolyGetPlayerRankReq.Builder builder;

    public MonopolyGetPlayerRankReq_213125Impl() {
    }

    public MonopolyGetPlayerRankReq_213125Impl(Xddq.MonopolyGetPlayerRankReq.Builder builder) {
        this.builder = builder;
    }

    public boolean write(ByteBuf buf) throws Exception {
        if (msg == null) {
            msg = builder.build();
        }
        buf.writeBytes(msg.toByteArray());
        return true;
    }

    public boolean read(ByteBuf buf) throws Exception {
        int length = buf.readableBytes();
        byte[] array;
        int offset;
        if (buf.hasArray()) {
            array = buf.array();
            offset = buf.arrayOffset() + buf.readerIndex();
        } else {
            array = ByteBufUtil.getBytes(buf, buf.readerIndex(), length, false);
            offset = 0;
        }
        this.msg = Xddq.MonopolyGetPlayerRankReq.newBuilder().mergeFrom(array, offset, length).build();
        return true;
    }

    public Xddq.MonopolyGetPlayerRankReq getMsg() {
        return msg;
    }

    public MonopolyGetPlayerRankReq_213125Impl setMsg(Xddq.MonopolyGetPlayerRankReq msg) {
        this.msg = msg;
        return this;
    }

    public int length() throws Exception {
        return msg.getSerializedSize();
    }

    @Override
    public String getQueue() {
        return null;
    }

    @Override
    public int getId() {
        return 213125;
    }

    @Override
    public String toString() {
        try {
            return String.format("%d %s", this.getId(), JsonFormat.printer().print(this.getMsg()));
        } catch (InvalidProtocolBufferException ignored) {
        }
        return String.format("%d error message", this.getId());
    }
}
