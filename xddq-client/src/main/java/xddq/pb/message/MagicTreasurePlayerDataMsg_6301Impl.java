package xddq.pb.message;

import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import xddq.message.pb.PbMessage;


public class MagicTreasurePlayerDataMsg_6301Impl extends PbMessage {

    private Xddq.MagicTreasurePlayerDataMsg msg;

    private Xddq.MagicTreasurePlayerDataMsg.Builder builder;

    public MagicTreasurePlayerDataMsg_6301Impl() {
    }

    public MagicTreasurePlayerDataMsg_6301Impl(Xddq.MagicTreasurePlayerDataMsg.Builder builder) {
        this.builder = builder;
    }

    public boolean write(ByteBuf buf) throws Exception {
        if (msg == null) {
            msg = builder.build();
        }
        buf.writeBytes(msg.toByteArray());
        return true;
    }

    public boolean read(ByteBuf buf) throws Exception {
        int length = buf.readableBytes();
        byte[] array;
        int offset;
        if (buf.hasArray()) {
            array = buf.array();
            offset = buf.arrayOffset() + buf.readerIndex();
        } else {
            array = ByteBufUtil.getBytes(buf, buf.readerIndex(), length, false);
            offset = 0;
        }
        this.msg = Xddq.MagicTreasurePlayerDataMsg.newBuilder().mergeFrom(array, offset, length).build();
        return true;
    }

    public Xddq.MagicTreasurePlayerDataMsg getMsg() {
        return msg;
    }

    public MagicTreasurePlayerDataMsg_6301Impl setMsg(Xddq.MagicTreasurePlayerDataMsg msg) {
        this.msg = msg;
        return this;
    }

    public int length() throws Exception {
        return msg.getSerializedSize();
    }

    @Override
    public String getQueue() {
        return null;
    }

    @Override
    public int getId() {
        return 6301;
    }

    @Override
    public String toString() {
        try {
            return String.format("%d %s", this.getId(), JsonFormat.printer().print(this.getMsg()));
        } catch (InvalidProtocolBufferException ignored) {
        }
        return String.format("%d error message", this.getId());
    }
}
