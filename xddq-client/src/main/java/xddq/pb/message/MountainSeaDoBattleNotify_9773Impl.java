package xddq.pb.message;

import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import xddq.message.pb.PbMessage;


public class MountainSeaDoBattleNotify_9773Impl extends PbMessage {

    private Xddq.MountainSeaDoBattleNotify msg;

    private Xddq.MountainSeaDoBattleNotify.Builder builder;

    public MountainSeaDoBattleNotify_9773Impl() {
    }

    public MountainSeaDoBattleNotify_9773Impl(Xddq.MountainSeaDoBattleNotify.Builder builder) {
        this.builder = builder;
    }

    public boolean write(ByteBuf buf) throws Exception {
        if (msg == null) {
            msg = builder.build();
        }
        buf.writeBytes(msg.toByteArray());
        return true;
    }

    public boolean read(ByteBuf buf) throws Exception {
        int length = buf.readableBytes();
        byte[] array;
        int offset;
        if (buf.hasArray()) {
            array = buf.array();
            offset = buf.arrayOffset() + buf.readerIndex();
        } else {
            array = ByteBufUtil.getBytes(buf, buf.readerIndex(), length, false);
            offset = 0;
        }
        this.msg = Xddq.MountainSeaDoBattleNotify.newBuilder().mergeFrom(array, offset, length).build();
        return true;
    }

    public Xddq.MountainSeaDoBattleNotify getMsg() {
        return msg;
    }

    public MountainSeaDoBattleNotify_9773Impl setMsg(Xddq.MountainSeaDoBattleNotify msg) {
        this.msg = msg;
        return this;
    }

    public int length() throws Exception {
        return msg.getSerializedSize();
    }

    @Override
    public String getQueue() {
        return null;
    }

    @Override
    public int getId() {
        return 9773;
    }

    @Override
    public String toString() {
        try {
            return String.format("%d %s", this.getId(), JsonFormat.printer().print(this.getMsg()));
        } catch (InvalidProtocolBufferException ignored) {
        }
        return String.format("%d error message", this.getId());
    }
}
