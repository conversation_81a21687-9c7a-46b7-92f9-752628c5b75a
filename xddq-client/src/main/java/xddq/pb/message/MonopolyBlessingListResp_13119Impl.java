package xddq.pb.message;

import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import xddq.message.pb.PbMessage;


public class MonopolyBlessingListResp_13119Impl extends PbMessage {

    private Xddq.MonopolyBlessingListResp msg;

    private Xddq.MonopolyBlessingListResp.Builder builder;

    public MonopolyBlessingListResp_13119Impl() {
    }

    public MonopolyBlessingListResp_13119Impl(Xddq.MonopolyBlessingListResp.Builder builder) {
        this.builder = builder;
    }

    public boolean write(ByteBuf buf) throws Exception {
        if (msg == null) {
            msg = builder.build();
        }
        buf.writeBytes(msg.toByteArray());
        return true;
    }

    public boolean read(ByteBuf buf) throws Exception {
        int length = buf.readableBytes();
        byte[] array;
        int offset;
        if (buf.hasArray()) {
            array = buf.array();
            offset = buf.arrayOffset() + buf.readerIndex();
        } else {
            array = ByteBufUtil.getBytes(buf, buf.readerIndex(), length, false);
            offset = 0;
        }
        this.msg = Xddq.MonopolyBlessingListResp.newBuilder().mergeFrom(array, offset, length).build();
        return true;
    }

    public Xddq.MonopolyBlessingListResp getMsg() {
        return msg;
    }

    public MonopolyBlessingListResp_13119Impl setMsg(Xddq.MonopolyBlessingListResp msg) {
        this.msg = msg;
        return this;
    }

    public int length() throws Exception {
        return msg.getSerializedSize();
    }

    @Override
    public String getQueue() {
        return null;
    }

    @Override
    public int getId() {
        return 13119;
    }

    @Override
    public String toString() {
        try {
            return String.format("%d %s", this.getId(), JsonFormat.printer().print(this.getMsg()));
        } catch (InvalidProtocolBufferException ignored) {
        }
        return String.format("%d error message", this.getId());
    }
}
