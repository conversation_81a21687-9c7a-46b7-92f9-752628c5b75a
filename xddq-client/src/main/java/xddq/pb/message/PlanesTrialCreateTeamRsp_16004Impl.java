package xddq.pb.message;

import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import xddq.message.pb.PbMessage;


public class PlanesTrialCreateTeamRsp_16004Impl extends PbMessage {

    private Xddq.PlanesTrialCreateTeamRsp msg;

    private Xddq.PlanesTrialCreateTeamRsp.Builder builder;

    public PlanesTrialCreateTeamRsp_16004Impl() {
    }

    public PlanesTrialCreateTeamRsp_16004Impl(Xddq.PlanesTrialCreateTeamRsp.Builder builder) {
        this.builder = builder;
    }

    public boolean write(ByteBuf buf) throws Exception {
        if (msg == null) {
            msg = builder.build();
        }
        buf.writeBytes(msg.toByteArray());
        return true;
    }

    public boolean read(ByteBuf buf) throws Exception {
        int length = buf.readableBytes();
        byte[] array;
        int offset;
        if (buf.hasArray()) {
            array = buf.array();
            offset = buf.arrayOffset() + buf.readerIndex();
        } else {
            array = ByteBufUtil.getBytes(buf, buf.readerIndex(), length, false);
            offset = 0;
        }
        this.msg = Xddq.PlanesTrialCreateTeamRsp.newBuilder().mergeFrom(array, offset, length).build();
        return true;
    }

    public Xddq.PlanesTrialCreateTeamRsp getMsg() {
        return msg;
    }

    public PlanesTrialCreateTeamRsp_16004Impl setMsg(Xddq.PlanesTrialCreateTeamRsp msg) {
        this.msg = msg;
        return this;
    }

    public int length() throws Exception {
        return msg.getSerializedSize();
    }

    @Override
    public String getQueue() {
        return null;
    }

    @Override
    public int getId() {
        return 16004;
    }

    @Override
    public String toString() {
        try {
            return String.format("%d %s", this.getId(), JsonFormat.printer().print(this.getMsg()));
        } catch (InvalidProtocolBufferException ignored) {
        }
        return String.format("%d error message", this.getId());
    }
}
