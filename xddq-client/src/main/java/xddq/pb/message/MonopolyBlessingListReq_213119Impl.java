package xddq.pb.message;

import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import xddq.message.pb.PbMessage;


public class MonopolyBlessingListReq_213119Impl extends PbMessage {

    private Xddq.MonopolyBlessingListReq msg;

    private Xddq.MonopolyBlessingListReq.Builder builder;

    public MonopolyBlessingListReq_213119Impl() {
    }

    public MonopolyBlessingListReq_213119Impl(Xddq.MonopolyBlessingListReq.Builder builder) {
        this.builder = builder;
    }

    public boolean write(ByteBuf buf) throws Exception {
        if (msg == null) {
            msg = builder.build();
        }
        buf.writeBytes(msg.toByteArray());
        return true;
    }

    public boolean read(ByteBuf buf) throws Exception {
        int length = buf.readableBytes();
        byte[] array;
        int offset;
        if (buf.hasArray()) {
            array = buf.array();
            offset = buf.arrayOffset() + buf.readerIndex();
        } else {
            array = ByteBufUtil.getBytes(buf, buf.readerIndex(), length, false);
            offset = 0;
        }
        this.msg = Xddq.MonopolyBlessingListReq.newBuilder().mergeFrom(array, offset, length).build();
        return true;
    }

    public Xddq.MonopolyBlessingListReq getMsg() {
        return msg;
    }

    public MonopolyBlessingListReq_213119Impl setMsg(Xddq.MonopolyBlessingListReq msg) {
        this.msg = msg;
        return this;
    }

    public int length() throws Exception {
        return msg.getSerializedSize();
    }

    @Override
    public String getQueue() {
        return null;
    }

    @Override
    public int getId() {
        return 213119;
    }

    @Override
    public String toString() {
        try {
            return String.format("%d %s", this.getId(), JsonFormat.printer().print(this.getMsg()));
        } catch (InvalidProtocolBufferException ignored) {
        }
        return String.format("%d error message", this.getId());
    }
}
