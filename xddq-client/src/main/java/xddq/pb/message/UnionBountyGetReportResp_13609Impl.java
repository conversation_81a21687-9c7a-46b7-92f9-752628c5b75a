package xddq.pb.message;

import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import xddq.message.pb.PbMessage;


public class UnionBountyGetReportResp_13609Impl extends PbMessage {

    private Xddq.UnionBountyGetReportResp msg;

    private Xddq.UnionBountyGetReportResp.Builder builder;

    public UnionBountyGetReportResp_13609Impl() {
    }

    public UnionBountyGetReportResp_13609Impl(Xddq.UnionBountyGetReportResp.Builder builder) {
        this.builder = builder;
    }

    public boolean write(ByteBuf buf) throws Exception {
        if (msg == null) {
            msg = builder.build();
        }
        buf.writeBytes(msg.toByteArray());
        return true;
    }

    public boolean read(ByteBuf buf) throws Exception {
        int length = buf.readableBytes();
        byte[] array;
        int offset;
        if (buf.hasArray()) {
            array = buf.array();
            offset = buf.arrayOffset() + buf.readerIndex();
        } else {
            array = ByteBufUtil.getBytes(buf, buf.readerIndex(), length, false);
            offset = 0;
        }
        this.msg = Xddq.UnionBountyGetReportResp.newBuilder().mergeFrom(array, offset, length).build();
        return true;
    }

    public Xddq.UnionBountyGetReportResp getMsg() {
        return msg;
    }

    public UnionBountyGetReportResp_13609Impl setMsg(Xddq.UnionBountyGetReportResp msg) {
        this.msg = msg;
        return this;
    }

    public int length() throws Exception {
        return msg.getSerializedSize();
    }

    @Override
    public String getQueue() {
        return null;
    }

    @Override
    public int getId() {
        return 13609;
    }

    @Override
    public String toString() {
        try {
            return String.format("%d %s", this.getId(), JsonFormat.printer().print(this.getMsg()));
        } catch (InvalidProtocolBufferException ignored) {
        }
        return String.format("%d error message", this.getId());
    }
}
