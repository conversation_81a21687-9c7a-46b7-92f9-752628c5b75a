package xddq.pb.message;

import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import xddq.message.pb.PbMessage;


public class GodIslandHeartBeatRespMsg_6819Impl extends PbMessage {

    private Xddq.GodIslandHeartBeatRespMsg msg;

    private Xddq.GodIslandHeartBeatRespMsg.Builder builder;

    public GodIslandHeartBeatRespMsg_6819Impl() {
    }

    public GodIslandHeartBeatRespMsg_6819Impl(Xddq.GodIslandHeartBeatRespMsg.Builder builder) {
        this.builder = builder;
    }

    public boolean write(ByteBuf buf) throws Exception {
        if (msg == null) {
            msg = builder.build();
        }
        buf.writeBytes(msg.toByteArray());
        return true;
    }

    public boolean read(ByteBuf buf) throws Exception {
        int length = buf.readableBytes();
        byte[] array;
        int offset;
        if (buf.hasArray()) {
            array = buf.array();
            offset = buf.arrayOffset() + buf.readerIndex();
        } else {
            array = ByteBufUtil.getBytes(buf, buf.readerIndex(), length, false);
            offset = 0;
        }
        this.msg = Xddq.GodIslandHeartBeatRespMsg.newBuilder().mergeFrom(array, offset, length).build();
        return true;
    }

    public Xddq.GodIslandHeartBeatRespMsg getMsg() {
        return msg;
    }

    public GodIslandHeartBeatRespMsg_6819Impl setMsg(Xddq.GodIslandHeartBeatRespMsg msg) {
        this.msg = msg;
        return this;
    }

    public int length() throws Exception {
        return msg.getSerializedSize();
    }

    @Override
    public String getQueue() {
        return null;
    }

    @Override
    public int getId() {
        return 6819;
    }

    @Override
    public String toString() {
        try {
            return String.format("%d %s", this.getId(), JsonFormat.printer().print(this.getMsg()));
        } catch (InvalidProtocolBufferException ignored) {
        }
        return String.format("%d error message", this.getId());
    }
}
