package xddq.pb.message;

import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import xddq.message.pb.PbMessage;


public class DoubleSeventhGiftListReq_216103Impl extends PbMessage {

    private Xddq.DoubleSeventhGiftListReq msg;

    private Xddq.DoubleSeventhGiftListReq.Builder builder;

    public DoubleSeventhGiftListReq_216103Impl() {
    }

    public DoubleSeventhGiftListReq_216103Impl(Xddq.DoubleSeventhGiftListReq.Builder builder) {
        this.builder = builder;
    }

    public boolean write(ByteBuf buf) throws Exception {
        if (msg == null) {
            msg = builder.build();
        }
        buf.writeBytes(msg.toByteArray());
        return true;
    }

    public boolean read(ByteBuf buf) throws Exception {
        int length = buf.readableBytes();
        byte[] array;
        int offset;
        if (buf.hasArray()) {
            array = buf.array();
            offset = buf.arrayOffset() + buf.readerIndex();
        } else {
            array = ByteBufUtil.getBytes(buf, buf.readerIndex(), length, false);
            offset = 0;
        }
        this.msg = Xddq.DoubleSeventhGiftListReq.newBuilder().mergeFrom(array, offset, length).build();
        return true;
    }

    public Xddq.DoubleSeventhGiftListReq getMsg() {
        return msg;
    }

    public DoubleSeventhGiftListReq_216103Impl setMsg(Xddq.DoubleSeventhGiftListReq msg) {
        this.msg = msg;
        return this;
    }

    public int length() throws Exception {
        return msg.getSerializedSize();
    }

    @Override
    public String getQueue() {
        return null;
    }

    @Override
    public int getId() {
        return 216103;
    }

    @Override
    public String toString() {
        try {
            return String.format("%d %s", this.getId(), JsonFormat.printer().print(this.getMsg()));
        } catch (InvalidProtocolBufferException ignored) {
        }
        return String.format("%d error message", this.getId());
    }
}
