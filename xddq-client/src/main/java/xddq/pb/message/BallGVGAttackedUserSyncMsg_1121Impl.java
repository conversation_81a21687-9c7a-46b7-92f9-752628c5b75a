package xddq.pb.message;

import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import xddq.message.pb.PbMessage;


public class BallGVGAttackedUserSyncMsg_1121Impl extends PbMessage {

    private Xddq.BallGVGAttackedUserSyncMsg msg;

    private Xddq.BallGVGAttackedUserSyncMsg.Builder builder;

    public BallGVGAttackedUserSyncMsg_1121Impl() {
    }

    public BallGVGAttackedUserSyncMsg_1121Impl(Xddq.BallGVGAttackedUserSyncMsg.Builder builder) {
        this.builder = builder;
    }

    public boolean write(ByteBuf buf) throws Exception {
        if (msg == null) {
            msg = builder.build();
        }
        buf.writeBytes(msg.toByteArray());
        return true;
    }

    public boolean read(ByteBuf buf) throws Exception {
        int length = buf.readableBytes();
        byte[] array;
        int offset;
        if (buf.hasArray()) {
            array = buf.array();
            offset = buf.arrayOffset() + buf.readerIndex();
        } else {
            array = ByteBufUtil.getBytes(buf, buf.readerIndex(), length, false);
            offset = 0;
        }
        this.msg = Xddq.BallGVGAttackedUserSyncMsg.newBuilder().mergeFrom(array, offset, length).build();
        return true;
    }

    public Xddq.BallGVGAttackedUserSyncMsg getMsg() {
        return msg;
    }

    public BallGVGAttackedUserSyncMsg_1121Impl setMsg(Xddq.BallGVGAttackedUserSyncMsg msg) {
        this.msg = msg;
        return this;
    }

    public int length() throws Exception {
        return msg.getSerializedSize();
    }

    @Override
    public String getQueue() {
        return null;
    }

    @Override
    public int getId() {
        return 1121;
    }

    @Override
    public String toString() {
        try {
            return String.format("%d %s", this.getId(), JsonFormat.printer().print(this.getMsg()));
        } catch (InvalidProtocolBufferException ignored) {
        }
        return String.format("%d error message", this.getId());
    }
}
