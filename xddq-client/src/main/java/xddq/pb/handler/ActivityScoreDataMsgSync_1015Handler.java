package xddq.pb.handler;

import xddq.message.IMessage;
import xddq.message.handler.Handler;
import xddq.pb.message.ActivityScoreDataMsgSync_1015Impl;

public class ActivityScoreDataMsgSync_1015Handler extends Handler {

    @Override
    public void action(IMessage message) {
        try{
            ActivityScoreDataMsgSync_1015Impl msg = (ActivityScoreDataMsgSync_1015Impl)message;
            //TODO 添加消息处理

        }catch(Exception e){
            log.error(e.getMessage(), e);
        }
    }
}
