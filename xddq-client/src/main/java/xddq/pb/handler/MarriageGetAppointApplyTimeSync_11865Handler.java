package xddq.pb.handler;

import xddq.message.IMessage;
import xddq.message.handler.Handler;
import xddq.pb.message.MarriageGetAppointApplyTimeSync_11865Impl;

public class MarriageGetAppointApplyTimeSync_11865H<PERSON><PERSON> extends <PERSON><PERSON> {

    @Override
    public void action(IMessage message) {
        try{
            MarriageGetAppointApplyTimeSync_11865Impl msg = (MarriageGetAppointApplyTimeSync_11865Impl)message;
            //TODO 添加消息处理

        }catch(Exception e){
            log.error(e.getMessage(), e);
        }
    }
}
