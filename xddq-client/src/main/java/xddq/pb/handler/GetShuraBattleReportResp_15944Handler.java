package xddq.pb.handler;

import xddq.message.IMessage;
import xddq.message.handler.Handler;
import xddq.pb.message.GetShuraBattleReportResp_15944Impl;

public class GetShuraBattleReportResp_15944Handler extends Handler {

    @Override
    public void action(IMessage message) {
        try{
            GetShuraBattleReportResp_15944Impl msg = (GetShuraBattleReportResp_15944Impl)message;
            //TODO 添加消息处理

        }catch(Exception e){
            log.error(e.getMessage(), e);
        }
    }
}
