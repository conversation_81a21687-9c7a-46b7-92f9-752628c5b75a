package xddq.pb.handler;

import xddq.message.IMessage;
import xddq.message.handler.Handler;
import xddq.pb.message.MonopolyPlayerScoreChangeNotify_13144Impl;

public class MonopolyPlayerScoreChangeNotify_13144H<PERSON>ler extends Handler {

    @Override
    public void action(IMessage message) {
        try{
            MonopolyPlayerScoreChangeNotify_13144Impl msg = (MonopolyPlayerScoreChangeNotify_13144Impl)message;
            //TODO 添加消息处理

        }catch(Exception e){
            log.error(e.getMessage(), e);
        }
    }
}
