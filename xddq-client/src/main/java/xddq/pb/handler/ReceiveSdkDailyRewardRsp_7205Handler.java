package xddq.pb.handler;

import xddq.message.IMessage;
import xddq.message.handler.Handler;
import xddq.pb.message.ReceiveSdkDailyRewardRsp_7205Impl;

public class ReceiveSdkDailyRewardRsp_7205Handler extends Handler {

    @Override
    public void action(IMessage message) {
        try{
            ReceiveSdkDailyRewardRsp_7205Impl msg = (ReceiveSdkDailyRewardRsp_7205Impl)message;
            //TODO 添加消息处理

        }catch(Exception e){
            log.error(e.getMessage(), e);
        }
    }
}
