package xddq.pb.handler;

import xddq.message.IMessage;
import xddq.message.handler.Handler;
import xddq.pb.message.PalaceSyncMsg_4801Impl;

public class PalaceSyncMsg_4801H<PERSON>ler extends Handler {

    @Override
    public void action(IMessage message) {
        try{
            PalaceSyncMsg_4801Impl msg = (PalaceSyncMsg_4801Impl)message;
            //TODO 添加消息处理

        }catch(Exception e){
            log.error(e.getMessage(), e);
        }
    }
}
