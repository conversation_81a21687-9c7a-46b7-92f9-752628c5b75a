package xddq.pb.handler;

import xddq.message.IMessage;
import xddq.message.handler.Handler;
import xddq.pb.message.QQCardGetRewardRsp_7203Impl;

public class QQCardGetRewardRsp_7203Handler extends Handler {

    @Override
    public void action(IMessage message) {
        try{
            QQCardGetRewardRsp_7203Impl msg = (QQCardGetRewardRsp_7203Impl)message;
            //TODO 添加消息处理

        }catch(Exception e){
            log.error(e.getMessage(), e);
        }
    }
}
