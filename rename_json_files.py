#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
from pathlib import Path

def camel_to_snake(name):
    """
    将驼峰命名转换为下划线分隔的小写格式
    例如: MagicLevelup -> magic_levelup
    """
    # 在大写字母前插入下划线，但不在开头
    s1 = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', name)
    # 处理连续的大写字母
    return re.sub('([a-z0-9])([A-Z])', r'\1_\2', s1).lower()

def generate_new_filename(original_name):
    """
    根据规则生成新的文件名
    """
    # 如果不包含"DB"，则不重命名
    if "DB" not in original_name:
        return original_name
    
    # 移除.json扩展名
    name_without_ext = original_name.replace('.json', '')
    
    # 移除DB后缀
    if name_without_ext.endswith('DB'):
        base_name = name_without_ext[:-2]  # 移除最后的"DB"
    else:
        base_name = name_without_ext
    
    # 转换为下划线分隔的小写格式
    snake_case = camel_to_snake(base_name)
    
    # 获取最后一个单词（用于重复）
    words = snake_case.split('_')
    last_word = words[-1] if words else snake_case
    
    # 构建新文件名: data_ + snake_case + _ + last_word + .json
    new_name = f"data_{snake_case}_{last_word}.json"
    
    return new_name

def main():
    # 设置目标目录
    target_dir = Path("new_jsons_2")
    
    if not target_dir.exists():
        print(f"错误: 目录不存在: {target_dir}")
        return
    
    # 获取所有JSON文件
    json_files = list(target_dir.glob("*.json"))
    
    if not json_files:
        print(f"在 {target_dir} 中没有找到JSON文件")
        return
    
    print(f"找到 {len(json_files)} 个JSON文件，开始重命名...")
    
    rename_count = 0
    skip_count = 0
    
    # 预览重命名计划
    rename_plan = []
    for json_file in json_files:
        original_name = json_file.name
        new_name = generate_new_filename(original_name)
        
        if original_name != new_name:
            rename_plan.append((json_file, new_name))
        else:
            skip_count += 1
    
    print(f"\n重命名计划预览 (共{len(rename_plan)}个文件需要重命名, {skip_count}个文件跳过):")
    print("-" * 80)
    
    for i, (old_file, new_name) in enumerate(rename_plan[:10]):  # 只显示前10个
        print(f"{old_file.name} -> {new_name}")
    
    if len(rename_plan) > 10:
        print(f"... 还有 {len(rename_plan) - 10} 个文件")
    
    print("-" * 80)
    
    # 执行重命名
    confirm = input(f"\n确认执行重命名吗? (y/N): ").strip().lower()
    if confirm != 'y':
        print("取消重命名操作")
        return
    
    print("\n开始执行重命名...")
    
    for old_file, new_name in rename_plan:
        try:
            new_path = old_file.parent / new_name
            old_file.rename(new_path)
            print(f"✓ {old_file.name} -> {new_name}")
            rename_count += 1
        except Exception as e:
            print(f"✗ 重命名失败: {old_file.name} - {str(e)}")
    
    print(f"\n重命名完成: {rename_count}/{len(rename_plan)} 个文件成功重命名")

if __name__ == "__main__":
    main()
