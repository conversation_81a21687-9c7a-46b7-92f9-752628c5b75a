package xddq.const;

/**
 * 游戏配置常量类
 * 自动生成，请勿手动修改
 */
public class GameConfig {

    public static final String CONFIG_VERSION = "3.1.4650";
    public static final String RENAME_COST = "100012=1";
    public static final String RENAME_ITEM_ID = "100012";
    public static final String RENAME_DAILY_TIMES = "2";
    public static final String BORN_REWARD_LIST = "100004=20";
    public static final String BORN_CHARACTER_LIST = "1|2|3|5|6|7";
    public static final String DREAM_AUTO_UNLOCK = "3";
    public static final String DREAM_SPEEDUP_UNLOCK = "7";
    public static final String DREAM_COST = "100004=1";
    public static final String DREAM_UPGRADE_SPEEDUP_ITEM_COST = "100025=1";
    public static final String DREAM_UPGRADE_SPEEDUP_ITEM_TIME = "300";
    public static final String DREAM_UPGRADE_SPEEDUP_AD_TIME = "1800";
    public static final String DREAM_UPGRADE_SPEEDUP_AD_LIMIT = "8";
    public static final String DREAM_UPGRADE_SPEEDUP_AD_COLD_TIME = "1800";
    public static final String DREAM_UPGRADE_SPEEDUP_AD_SKIP = "100000=50";
    public static final String DREAM_HIPE_ITEM_DROP = "16";
    public static final String ATTRIBUTE_POWER_PARS = "4000000|1000|500|6|1|30";
    public static final String ATTRIBUTE_SPEED_VALUE = "1";
    public static final String ATTRIBUTE_VALUE_LIST = "800|800|800|800|800|800|1200|1200|1200|1200|1200|1200";
    public static final String ATTRIBUTE_SPECIAL_VALUE_LIST = "800|800|800|800|800|800|800|800|200|140|800|1200";
    public static final String ATTRIBUTE_MAGICTREASURE_VALUE_LIST = "2000|2000|2000|2000";
    public static final String ATTRIBUTE_WORLDRULE_VALUE_LIST = "600|600|600|600";
    public static final String ATTRIBUTE_PETNEIDAN_VALUE_LIST = "2000|2000|2000|2000";
    public static final String ATTRIBUTE_CHAOTICPET_VALUE_LIST = "2000";
    public static final String ATTRIBUTE_UNIVERSE_VALUE_LIST = "800|800";
    public static final String ATTRIBUTE_CAREER_VALUE_LIST = "2000|2000";
    public static final String ATTRIBUTE_SPIRITBODY_VALUE_LIST = "800|800";
    public static final String ATTRIBUTE_DESTINYCARD_VALUE_LIST = "600|600";
    public static final String ATTRIBUTE_DIVINE_VALUE_LIST = "2000|2000";
    public static final String ATTRIBUTE_CRITICAL_CAP = "1000";
    public static final String ATTRIBUTE_EVASION_CAP = "800";
    public static final String ATTRIBUTE_STAN_CAP = "800";
    public static final String ATTRIBUTE_CONTINUE_CAP = "1000";
    public static final String ATTRIBUTE_CONTINUE_DAMP = "500";
    public static final String ATTRIBUTE_ATKBACK_CAP = "800";
    public static final String ATTRIBUTE_SUCKBLOOD_CAP = "10000";
    public static final String ATTRIBUTE_CRITICAL_DAMAGE_BASE = "2000";
    public static final String ATTRIBUTE_ATTACK_SPEED_PAR = "1000|-600000|500";
    public static final String ATTRIBUTE_DAMAGE_PROTECT_LIMIT = "700";
    public static final String ATTRIBUTE_BLOCK_LIMIT = "5000";
    public static final String ATTRIBUTE_BLOCK_PARAM = "100|300";
    public static final String ATTRIBUTE_SMASHBLOCK_LIMIT = "5000";
    public static final String ATTRIBUTE_SMASHBLOCK_PARAM = "100|300";
    public static final String ATTRIBUTE_BOOSTAGAIN_PARAM = "500";
    public static final String ATTRIBUTE_SHIELD_PARAM = "1000";
    public static final String ATTRIBUTE_PETSUPPRESS_LIMIT = "5000";
    public static final String ATTRIBUTE_PETSUPPRESS_PARAM = "100|300";
    public static final String ATTRIBUTE_PETINSPIRE_LIMIT = "5000";
    public static final String ATTRIBUTE_PETINSPIRE_PARAM = "100|300";
    public static final String ATTRIBUTE_CAREER_PARAM = "10000";
    public static final String ATTRIBUTE_DIVINE_LIMIT = "5000";
    public static final String EQUIPMENT_NOT_USE_MAX_NUM = "1";
    public static final String EQUIPMENT_ENERGY_DROP_TIME = "1";
    public static final String EQUIPMENT_DROP_PARAM = "1000";
    public static final String EQUIPMENT_UNLOCK_PARAM = "1|1|1|1|1|1|1|1|1|1|1|1";
    public static final String EQUIPMENT_SPECIAL_DROP_LIST = "1010101|1010301|1020102|1020602|1020502|1021001|1040602|1031002|1030801|1020701|1010502|1040103|1041002|1050502|1040901";
    public static final String EQUIPMENT_QUALITY_PROTECT_PARAM = "2";
    public static final String EQUIPMENT_LEVEL_PROTECT_PARAM = "1";
    public static final String EQUIPMENT_SEPCIAL_PROTECT_PARAM = "0;0;0;20;100|200|400;500|1000|3000;2000;3000;5000;5000;5000;5000;5000;5000;5000;5000;5000;5000;5000;5000;5000;5000;5000;5000;5000;5000;5000;5000;5000;5000;5000;5000;5000;5000";
    public static final String EQUIPMENT_SEPCIAL_PROTECT_TIME = "1";
    public static final String EQUIPMENT_QUALITY_LOCK = "0|0|0|0|5|5|5|5|5";
    public static final String EQUIPMENT_ADVANCE_LIMIT = "15";
    public static final String STAGE_MAX_TURN = "15";
    public static final String EMAIL_SHOW_MAX_NUM = "50";
    public static final String EMAIL_EXPIRATION_TIME_LIMIT_DAYS = "30";
    public static final String PVP_SPEEDUP_ROUND = "20";
    public static final String PVP_SPEEDUP_PARAM = "100";
    public static final String PVP_ROUND_LIMIT = "20";
    public static final String PVP_ROBOT_COUNT = "20";
    public static final String PVP_ROBOT_LEVEL = "9|10";
    public static final String PVP_CHALLENGE_COST = "100026=1";
    public static final String PVP_CHALLENGE_REWARD = "100005=2|100003=2000";
    public static final String PVP_CHALLENGE_COST_LIMIT = "3";
    public static final String PVP_PROTECT_LEVEL = "3";
    public static final String PVP_REFRESH_COST = "100003=100";
    public static final String PVP_REFRESH_TIME_LIMIT = "5";
    public static final String PVP_INITIAL_SCORE = "1000";
    public static final String PVP_MATCH_PARAM = "10|10";
    public static final String PVP_SCORE_CHANGE_PARAM = "200|0;100|5;0|10;-100|15;-200|20";
    public static final String PVP_SCORE_FAIL_PARAM = "200|0;100|0;0|0;-100|0;-200|0";
    public static final String PVP_SCORE_CHANGE_RATE = "500";
    public static final String PVP_SCORE_BASIC_PARAM = "10";
    public static final String PVP_PROTECT_TIME_PARAM = "5|60|60";
    public static final String PVP_DAILY_RANK_REWARD = "1,1,100005=60|100000=300;2,2,100005=40|100000=200;3,3,100005=30|100000=100;4,4,100005=24|100000=80;5,5,100005=20|100000=50;6,10,100005=10|100000=50;11,20,100005=6|100000=50;21,50,100005=4|100000=50;51,500,100005=2|100000=10;501,9999,100005=1|100000=10";
    public static final String PVP_WEEK_RANK_REWARD = "1,1,100006=50|100000=2000;2,2,100006=40|100000=1500;3,3,100006=30|100000=1000;4,4,100006=25|100000=800;5,5,100006=25|100000=600;6,10,100006=20|100000=400;11,50,100006=20|100004=200;51,500,100006=15|100004=100;501,9999,100006=10|100004=60";
    public static final String PVP_CLOUD_CHALLENGE_REWARD = "100005=2|100116=25";
    public static final String PVP_CLOUD_CHALLENGE_UNLOCK = "129";
    public static final String PVP_PLUS_QUALIFICATION_LOCK_TIME = "1|5";
    public static final String PVP_PLUS_UNLOCK_PARAM = "24";
    public static final String PVP_ROBOT_SCORE_LIMIT = "500";
    public static final String PVP_JUMP_CONDITION = "90";
    public static final String WILDBOSS_REPEAT_COST = "0;100000=56;100000=112;100000=168;100000=224;100000=280";
    public static final String WILDBOSS_REPEAT_COST_PARAM = "0|1000|2000|3000|4000|5000|5000|5000|5000|5000|5000";
    public static final String WILDBOSS_REPEAT_LIMIT = "6";
    public static final String WILDBOSS_ROUND_LIMIT = "15";
    public static final String PET_BACK_PARAM = "800";
    public static final String PET_BAG_SIZE = "6";
    public static final String PET_BAG_ADD_COST = "100000=100;100000=200;100000=300;100000=400;100000=500;100000=500;100000=500;100000=500;100000=500;100000=500;100000=500;100000=500;100000=500;100000=500;100000=500;100000=500;100000=500;100000=500;100000=500;100000=500;100000=500;100000=500;100000=500;100000=500;100000=500;100000=500;100000=500;100000=500;100000=500;100000=500;100000=500;100000=500;100000=500;100000=500;100000=500;100000=500;100000=500;100000=500;100000=500;100000=500;100000=500;100000=500;100000=500;100000=500;100000=500;100000=500;100000=500;100000=500;100000=500;100000=500;100000=500;100000=500;100000=500;100000=500;100000=500;100000=500;100000=500;100000=500;100000=500;100000=500;100000=500;100000=500;100000=500;100000=500;100000=500;100000=500;100000=500;100000=500;100000=500;100000=500;100000=500;100000=500;100000=500;100000=500";
    public static final String PET_BAG_ADD_SHOW_PARAM = "1|50;50|80";
    public static final String PET_FREE_REFRESH_TIME = "4";
    public static final String PET_REFRESH_COST = "100010=10;100010=10;100010=10";
    public static final String PET_LEVELUP_COST_PARAM = "1000|2000|3000|4000|5000|6000|7000|8000|9000|10000|11000|12000|13000|14000|15000|16000|17000|18000|19000|20000|21000|22000|23000|24000|25000|26000|27000|28000|29000|30000|31000|32000|33000|34000|35000|36000|37000|38000|39000|40000|41000|42000|43000|44000|45000|46000|47000|48000|49000|50000|51000|52000|53000|54000|55000|56000|57000|58000|59000|60000|61000|62000|63000|64000|65000|66000|67000|68000|69000|70000|71000|72000|73000|74000|75000|76000|77000|78000|79000|80000|81000|82000|83000|84000|85000|86000|87000|88000|89000|90000|91000|92000|93000|94000|95000|96000|97000|98000|99000|100000|101000|102000|103000|104000|105000|106000|107000|108000|109000|110000|111000|112000|113000|114000|115000|116000|117000|118000|119000|120000|121000|122000|123000|124000|125000|126000|127000|128000|129000|130000|131000|132000|133000|134000|135000|136000|137000|138000|139000|140000|141000|142000|143000|144000|145000|146000|147000|148000|149000|150000|151000|152000|153000|154000|155000|156000|157000|158000|159000|160000|161000|162000|163000|164000|165000|166000|167000|168000|169000|170000|171000|172000|173000|174000|175000|176000|177000|178000|179000|180000|181000|182000|183000|184000|185000|186000|187000|188000|189000|190000|191000|192000|193000|194000|195000|196000|197000|198000|199000|200000";
    public static final String PET_QUALITY_WEIGHT = "500|280|160|58|2";
    public static final String PET_STAR_LIMIT = "40";
    public static final String PET_ACTIVE_SKILL_UPGRADE = "20|40|60|80";
    public static final String PET_PASSIVE_SKILL_LIMIT = "4";
    public static final String PET_RESET_COST = "100000=100;100000=120;100000=150;100000=200;100000=300";
    public static final String PET_DRAW_PROTECT_PARAM = "5";
    public static final String PET_SYNC_MAIN_PARAM = "100|150|200|250|300";
    public static final String PET_SYNC_PASSIVE_PARAM = "100|120|140|160|200";
    public static final String PET_SYNC_ATTACK_PARAM = "200|200|200|200|250";
    public static final String PET_UPGRADE_SKILL_QUALITY_LIMIT = "3";
    public static final String PET_UPGRADE_CHOOSE_SKILL_COUNT = "3";
    public static final String PET_UPGRADE_EFFECT_SYNC_PARAM = "0|0|0|400|500";
    public static final String PET_UPGRADE_RETURN_PARAM = "800";
    public static final String PET_UPGRADE_EAT_LIMIT_ADD = "10";
    public static final String PET_UPGRADE_RESET_COST_PARAM = "500";
    public static final String PET_UPGRADE_RESET_LIMIT = "2";
    public static final String PET_SEAL_UNLOCK = "156|15|10";
    public static final String PET_SEAL_COST = "100166=20|100166=40|100166=100|100166=150";
    public static final String PET_WASH_LIMIT = "5";
    public static final String PET_WASH_COST = "100054=20";
    public static final String PET_WASH_LOCK_COST = "100055=20|100055=40|100055=100|100055=150|100055=300|100055=500|100055=700";
    public static final String PET_NEIDAN_RETURN_PARAM = "800";
    public static final String PET_NEIDAN_EFFECT_SYNC_PARAM = "200|250";
    public static final String PET_NEIDAN_HOLE_UNLOCK = "1|15|20|25|35";
    public static final String PET_NEIDAN_SKILL_UPGRADE_PARAM = "10|30|50|75|100";
    public static final String PET_NEIDAN_TRANSLATE_PARAM = "50";
    public static final String PET_NEIDAN_DRAW_WEIGHT = "405|100|40;40|10|5;100099=40,100|100099=20,300";
    public static final String PET_NEIDAN_DRAW_ITEM = "100100=1";
    public static final String PET_NEIDAN_PULL_ENSURE_NUM = "100|100";
    public static final String PET_NEIDAN_PULL_ENSURE_QUALITY = "2|3;3";
    public static final String PET_NEIDAN_PIECE_TRANSLATE_PARAM = "100112=1|100112=2|100112=4|100112=12";
    public static final String PET_NEIDAN_PIECE_LEVEL = "40";
    public static final String PET_NEIDAN_FREE_DRAW = "2";
    public static final String PET_NEIDAN_SPECIAL_DRAW = "1001";
    public static final String PET_NEIDAN_COMMON_PIECE_ITEM_ID = "100112";
    public static final String PET_RARE_AWAKE_LEVEL_COST_NUM = "1|1|2|2|3|3|4|4|5|5|6|6|7|7|8|8|9|9|10|10|11|11|12|12|13|13|14|14|15|15|16|16|17|17|18|18|19|19|20|20|21|21|22|22|23|23|24|24|25|25|26|26|27|27|28|28|29|29|30|30|31|31|32|32|33|33|34|34|35|35|36|36|37|37|38|38|39|39|40|40|41|41|42|42|43|43|44|44|45|45|46|46|47|47|48|48|49|49|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50|50";
    public static final String PET_RARE_SPECIFIC_LIMIT = "0|0;5|20;10|40;15|60;20|80;25|100;30|120;35|140;40|160;45|180;50|200;55|200;60|200;65|200;70|200;75|200;80|200";
    public static final String PET_LOCK_TRY_TIMES = "5|24";
    public static final String PET_LOCK_FORCE_UNLOCK = "72";
    public static final String PET_CHARA_NUM = "50";
    public static final String GOD_PET_COMPOUND_COST = "100170=1000";
    public static final String GOD_PET_SKILL_UPGRADE_ITEM_ID = "100170";
    public static final String GOD_PET_SKILL_UPGRADE_COST = "60|60|75|75|90|90|105|105|120|120|130|130|150|170|190|210|230|250|270|290|300|310|320|330|340|350|360|370|380|390|400|410|420|430|440|450|460|470|480|490|500|510|520|530|540|550|560|570|580|590|600|610|620|630|640|650|660|670|680|700|720|740|760|780|800|820|840|860|880|900|920|940|960|980|1000|1020|1040|1060|1080|1100";
    public static final String GOD_PET_SKILL_UNLOCK_PARAM = "0|85";
    public static final String GOD_PET_SKILL_RESET_COST = "100000=5000";
    public static final String GOD_PET_COMBINE = "2|531002;3|531002;4|531002";
    public static final String GOD_PET_SKILL_PARAM = "5";
    public static final String GOD_PET_SKILL_SYNC_PARAM = "500";
    public static final String GOD_PET_SKIN_UNLOCK_LEVEL = "20";
    public static final String TOWER_RESET_PARAM = "2";
    public static final String TOWER_MOPPINGUP_LIMIT = "140|120;200|130;400|140;600|150;800|160;1000|170;1200|180;1400|190;1500|200;1600|210;1700|220;1800|230;1900|240;2000|250;2100|260;2200|270;2300|280;2400|290;2500|300";
    public static final String TOWER_QUICK_UNLOCK = "100|96";
    public static final String TOWER_AUTO_FIGHT_UNLOCK = "100|96";
    public static final String TALENT_QUALITY_RATE_FIRST = "49800000|18160000|3140000|0|0|0|0|0|0";
    public static final String TALENT_PULL_COST = "100007=1";
    public static final String TALENT_READ_COST = "100008=1";
    public static final String TALENT_QUICK_UNLOCK = "16";
    public static final String TALENT_SPECIAL_TYPE = "3|5|9|11";
    public static final String TALENT_SPECIAL_EFFECT = "50001|50002|50003|50004|50005|50006";
    public static final String SPIRIT_BOX_UNLOCK = "0|45|55";
    public static final String SPIRIT_PULL_COST = "100023=1";
    public static final String SPIRIT_PULL_WEIGHT = "260|220|160|120|40;90|60|30|15|5";
    public static final String SPIRIT_PULL_ENSURE_NUM = "100|100";
    public static final String SPIRIT_PULL_ENSURE_QUALITY = "4|5;5";
    public static final String SPIRIT_FIRST_PULL_ENSURE_NUM = "5";
    public static final String SPIRIT_FIRST_PULL_ENSURE_QUALITY = "1|2";
    public static final String SPIRIT_TRANS_COMMON_SOUL = "1|2|3|4|10";
    public static final String SPIRIT_COMMON_PIECE_ITEM_ID = "100027";
    public static final String SPIRIT_LEVEL_COST_NUM = "20|30|40|50|60|70|80|90|100|110|240|600";
    public static final String SPIRIT_AD_LIMIT = "2";
    public static final String SPIRIT_AD_CD = "10";
    public static final String SPIRIT_LEVEL_UNLOCK_CONDITION = "5|11";
    public static final String SPIRIT_LEVEL_UNLOCK_COST = "600|600|600|600";
    public static final String SPIRIT_COMBINE_LIMIT = "22";
    public static final String CARD_MONTH_CONTINUE_LIMIT = "3";
    public static final String CARD_MONTH_BUY_ID = "160000001";
    public static final String CARD_MONTH_PARAM = "300|100|0|1|2|0|1|3";
    public static final String CARD_YEAR_BUY_ID = "160000002";
    public static final String CARD_YEAR_PARAM = "1980|100|100|1|1|20|1|3";
    public static final String HOMELAND_ENERGY_DIVIDE = "100|50|25|15";
    public static final String HOMELAND_ENERGY_SPEED = "20|40|80|4000";
    public static final String HOMELAND_ENERGY_COPE_SPEED = "500|300|100|10";
    public static final String HOMELAND_PULL_BASIC_TIME = "2";
    public static final String HOMELAND_ITEM_REFRESH_PARAM = "100|-100";
    public static final String HOMELAND_AUTO_REFRESH_TIME = "10|18|22";
    public static final String HOMELAND_AUTO_REFRESH_TIME_PER = "600";
    public static final String HOMELAND_SINGLE_BOX_MOUSE_LIMIT_CONFIG = "3|3|3|4|4|5|5";
    public static final String HOMELAND_FREE_REFRESH_TIME = "2";
    public static final String HOMELAND_FREE_REFRESH_CD = "1";
    public static final String HOMELAND_PAY_REFRESH_COST = "100000=10;100000=10;100000=10";
    public static final String HOMELAND_BASIC_WORKER_NUM = "1";
    public static final String HOMELAND_WORKER_COST = "100029=30;100029=300;100029=600;100029=900;100029=1200;100029=1500;100029=1800;100029=2100;100029=2400";
    public static final String HOMELAND_TARGET_REFRESH_TIME = "300";
    public static final String HOMELAND_TARGET_REFRESH_COUNT = "3";
    public static final String HOMELAND_TARGET_REFRESH_LIMIT = "100";
    public static final String HOMELAND_TARGET_AROUND_LIMIT = "1|1|1|1|56|96|156|236";
    public static final String HOMELAND_TARGET_ENEMY_LIMIT = "20";
    public static final String HOMELAND_TARGET_HATRED = "1";
    public static final String HOMELAND_TARGET_ATKBACK_HATRED = "1";
    public static final String HOMELAND_TARGET_OLD_HATRED = "3";
    public static final String HOMELAND_TARGET_AROUND_LEVEL = "3750";
    public static final String HOMELAND_TARGET_AROUND_LEVEL_LEAST = "10";
    public static final String HOMELAND_TARGET_PROTECT_TIMES = "10";
    public static final String HOMELAND_TARGET_HATRED_LIMIT = "5";
    public static final String HOMELAND_TARGET_ROBOT_LIMIT = "15=96|26=156|33=236";
    public static final String HOMELAND_NEIGHBOR_LIMIT = "15";
    public static final String HOMELAND_NEIGHBOR_LIMIT_OPEN_TIME = "9999";
    public static final String HOMELAND_NEIGHBOR_ROBOT_PARAM = "30";
    public static final String HOMELAND_AWARD_SCHEDULE_NUM = "1";
    public static final String HOMELAND_WEEK_CARD_BUY_ID = "540000001";
    public static final String HOMELAND_MONTH_CARD_BUY_ID = "540000002";
    public static final String HOMELAND_AUTO_PULL_TIME_LIMIT = "60";
    public static final String HOMELAND_TRY_TIME = "3";
    public static final String HOMELAND_SUPER_REFRESH_COST = "1|100029=50;51|100029=100";
    public static final String UNION_CREATE_INGOTS_COST = "100000=100";
    public static final String UNION_CREATE_ITEMS_COST = "100191=1";
    public static final String UNION_CREATE_ITEMS_LIMIT = "3";
    public static final String UNION_CREATE_NEED_TREE_LEVEL = "4";
    public static final String UNION_FIRST_JOIN_NEED_TIME = "300";
    public static final String UNION_NEXT_JOIN_NEED_TIME = "3600";
    public static final String UNION_KICKED_NEXT_JOIN_NEED_TIME = "300";
    public static final String UNION_QUIT_CONTRIBUTION_LOSE_PARAM = "500";
    public static final String UNION_DEFAULT_OUT_NOTICE = "恭迎小友，加入本盟。携手同行，共赴荣光！";
    public static final String UNION_DEFAULT_INNER_NOTICE = "望诸位齐心协力，共创大业！";
    public static final String UNION_NAME_LENGTH = "12";
    public static final String UNION_OUT_NOTICE_LENGTH = "40";
    public static final String UNION_INNER_NOTICE_LENGTH = "120";
    public static final String UNION_FIRST_JOIN_REWARD = "0";
    public static final String UNION_JOIN_APPLY_MAX_COUNT = "50";
    public static final String UNION_JOIN_APPLY_NEED_TITLE = "3|33";
    public static final String UNION_MASTER_MAX_OFFLINE_TIME = "3";
    public static final String UNION_POSITION_NEED_CONTRIBUTION = "10000|4000|1000";
    public static final String UNION_FLAG_PARAM = "1|2|3|4|5";
    public static final String UNION_FLAG_UNLOCK = "1|1|1|2|3";
    public static final String GAME_ITEM_UNION_CONTRIBUTION = "100032";
    public static final String GAME_ITEM_UNION_WEALTH = "100030";
    public static final String GAME_ITEM_UNION_EXP = "100031";
    public static final String UNION_CHANGE_NAME_COST = "100000=500";
    public static final String UNION_DONATE_COST = "100000=100";
    public static final String UNION_DAILY_PARAM = "150|250|500|750|1000";
    public static final String UNION_DAILY_REWARD = "100025=2|100003=2000;100016=1|100009=200;100000=10|100005=5;100000=15|100023=1;100000=30|100010=10";
    public static final String UNION_NOTICE_MAX_COUNT = "200";
    public static final String UNION_RANK_DEFAULT_MAX_COUNT = "300";
    public static final String UNION_PER_DAY_JOIN_NUM_LIMIT = "50";
    public static final String UNION_HELP_LIMIT = "999|10";
    public static final String UNION_HELP_TIME = "300";
    public static final String UNION_PARERGON_TEXT_PARAM = "700|500|200|0";
    public static final String UNION_PARERGON_TRIGGER_PARAM = "50|50";
    public static final String UNION_PARERGON_TIME_PARAM = "86400";
    public static final String UNION_PARERGON_LIMIT_TIME = "21600";
    public static final String UNION_WX_LIMIT = "156|1";
    public static final String UNION_ACTIVE_LIMIT = "7";
    public static final String UNION_POST_LIMIT = "3";
    public static final String DESTINY_LEVEL_UP = "5|10|15|20|25|30|35|40|45|50|55|60|65|70|75|80|85|90|95|100|110|120|130|140|150|160|170|180|190|200|210|220|230|240|250|260|270|280|290|300|325|350|375|400|425|450|475|500|525|550|575|600|625|650|675|700|725|750|775|800|860|920|980|1040|1100|1160|1220|1280|1340|1400|1460|1520|1580|1640|1700|1760|1820|1880|1940|2000|2200|2400|2600|2800|3000|3200|3400|3600|3800|4000|4200|4400|4600|4800|5000|5200|5400|5600|5800|6000|6500|7000|7500|8000|8500|9000|9500|10000|10500|11000|11500|12000|12500|13000|13500|14000|14500|15000|15500|16000|17200|18400|19600|20800|22000|23200|24400|25600|26800|28000|29200|30400|31600|32800|34000|35200|36400|37600|38800|40000|43000|46000|49000|52000|55000|58000|61000|64000|67000|70000|73000|76000|79000|82000|85000|88000|91000|94000|97000|100000|108000|116000|124000|132000|140000|148000|156000|164000|172000|180000|188000|196000|204000|212000|220000|228000|236000|244000|252000|260000";
    public static final String DESTINY_TWO_PRACTICE_PARAM = "1800|30";
    public static final String DESTINY_SPECIAL_UNLOCK_1 = "1";
    public static final String DESTINY_SPECIAL_UNLOCK_2 = "3";
    public static final String DESTINY_SPECIAL_UNLOCK_3 = "5|10|15";
    public static final String DESTINY_SPECIAL_UNLOCK_4 = "7";
    public static final String DESTINY_SPECIAL_UNLOCK_5 = "12|17";
    public static final String DESTINY_ENERGY_LIMIT = "4|2";
    public static final String DESTINY_DAILY_TALK_GET = "1|5;5|6;7|7;9|8;12|10";
    public static final String DESTINY_ENERGY_COST = "1|2";
    public static final String DESTINY_DAILY_REWARD_COUNT = "1";
    public static final String DESTINY_DAILY_REWARD_TIME = "28800|50400|72000";
    public static final String DESTINY_DIVIDE_PARAM = "1|20|40|60|80|100|120|140|160|180|200";
    public static final String DESTINY_ENERGY_TIME = "1800";
    public static final String DESTINY_ENERGY_ITEM_PARAM = "100011|1";
    public static final String DESTINY_ENERGY_QUICK_UNLOCK = "16";
    public static final String DESTINY_GIFT_HUNDREDSEND = "70";
    public static final String INVADE_MONSTER_ID = "102001|101004|101007";
    public static final String INVADE_CHALLENGE_TIME = "5";
    public static final String INVADE_RANK_REWARD = "1,1,100000=300|100010=300|100009=500;2,2,100000=200|100010=120|100009=450;3,3,100000=100|100010=80|100009=400;4,10,100000=80|100010=60|100009=350;11,50,100000=50|100010=40|100009=300;51,100,100000=20|100010=20|100009=250;101,500,100000=10|100010=10|100009=200;501,9999,100000=5|100010=5|100009=150";
    public static final String INVADE_FIGHT_REWARD = "100009=250,10|100010=4,5|100000=2,2|100004=5,2";
    public static final String AD_REWARD = "100000=30|100004=10";
    public static final String AD_REWARD_CD = "300";
    public static final String AD_REWARD_DAILY_MAX_NUM = "6";
    public static final String NEW_PLAYER_MAIL_REWARD = "100004=20|100012=1|100074=1";
    public static final String NEW_PLAYER_MAIL_ZHONGGUOQITAN_REWARD = "144014=1";
    public static final String NEW_PLAYER_MAIL_DOULUODALU_REWARD = "0";
    public static final String NEW_PLAYER_MAIL_XIYOU_REWARD = "160058=1";
    public static final String NEW_PLAYER_MAIL_FANRENXIUXIAN_REWARD = "0";
    public static final String NEW_PLAYER_MAIL_JP_REWARD = "144051=1";
    public static final String NEW_PLAYER_MAIL_EXTRA_JP_REWARD = "144073=1";
    public static final String NEW_PLAYER_MAIL_NEZHAMOTONG_REWARD = "0";
    public static final String NEW_PLAYER_MAIL_DOUPO_REWARD = "100074=1";
    public static final String NEW_PLAYER_MAIL_SHENMU_REWARD = "100074=1";
    public static final String NEW_PLAYER_MAIL_Calabash_REWARD = "100074=1";
    public static final String BATTLE_SKIP_ROUND = "1|5;40|5;100|5;150|5;200|5";
    public static final String REPORT_SPIRIT_LIMIT = "6";
    public static final String REPORT_PET_LIMIT = "5";
    public static final String TALK_TIME_LIMIT = "30|5|30";
    public static final String FIRST_RECHANGE_GIFT_TRIGGER_CONDITION = "4";
    public static final String REDEMPTION_GIFT_CODES_DAILY_LIMIT = "20";
    public static final String HERORANK_ITEM_COST = "100013=1";
    public static final String HERORANK_ITEM_PARAM = "10|10800";
    public static final String HERORANK_ITEM_BUY_COST = "100000=50";
    public static final String HERORANK_ITEM_BUY_LIMIT = "10";
    public static final String HERORANK_BATTLE_ROUND = "15";
    public static final String HERORANK_VICTORY_AWARD = "100046=25";
    public static final String HERORANK_DEFEAT_AWARD = "0";
    public static final String HERORANK_REFRESH_COST = "100000=10";
    public static final String HERORANK_REFRESH_LIMIT = "99|300";
    public static final String HERORANK_MESSAGE_LIMIT = "20";
    public static final String HERORANK_MESSAGE_TIME_LIMIT = "7";
    public static final String HERORANK_QUICK_LIMIT = "5";
    public static final String HERORANK_PROTECT_TIME = "300";
    public static final String HERORANK_REFRESH_SKIP_TIME = "2";
    public static final String HERORANK_WEEKLY_SETTLE_TIME = "22";
    public static final String MAGIC_LEVELUP_COST = "1000|1000|1000|1000";
    public static final String MAGIC_LEVELUP_PARAM = "1000|1000|1000|1000";
    public static final String MAGIC_UPGRADE_PARAM = "50|50|100|100|150|150|200|200|250|250|300|300|350|350|400|400|450|450|500|1000|2000|5000|10000|20000";
    public static final String MAGIC_REPEAT_PARAM = "50";
    public static final String MAGIC_MAX_PARAM = "1|2|20|60";
    public static final String MAGIC_MAX_ITEM_ID = "100071";
    public static final String MAGIC_PULL_WEIGHT = "100|50|5|1";
    public static final String MAGIC_PULL_PROTECT_TIME = "200";
    public static final String MAGIC_PULL_PROTECT_QUALITY = "4";
    public static final String MAGIC_PULL_COST = "100044=20";
    public static final String MAGIC_PULL_FREE_TIME = "1";
    public static final String MAGIC_BATTLE_PARAM = "0|10000|2000|20";
    public static final String MAGIC_RESET_COST = "100000=100";
    public static final String MAGIC_STONE_QUALITY_WEIGHT = "1000000|500000|200000|40000|8000|1600|320|64|8|1";
    public static final String MAGIC_STONE_COMPOUND = "3";
    public static final String MAGIC_AD_LIMIT = "2";
    public static final String MAGIC_AD_CD = "10";
    public static final String MAGIC_PRESET_LIMIT = "2";
    public static final String MAGIC_BREAK_LIMIT = "72";
    public static final String SECRETTOWER_UNLOCK_PARAM = "156|10;";
    public static final String SECRETTOWER_AUTO_FIGHT_UNLOCK = "400|236";
    public static final String MAMMON_TYPE_PARAM = "50|50|50|50|50";
    public static final String MAMMON_CAN_CONGRATULATE_TIME = "259200";
    public static final String MAMMON_RECORD_MAX_COUNT = "200";
    public static final String MAMMON_CELEBRATE_TIME_LIMIT = "600";
    public static final String MAMMON_WORSHIP_REWARD = "25|50";
    public static final String MAMMON_MIRACLE_WEIGHT = "250001=100|250002=100|250004=100";
    public static final String MAMMON_TITLE_FATE = "1|100;2|125;3|150;4|200;5|250";
    public static final String MAMMON_WORDS_MAX_NAME = "50";
    public static final String UNION_KILL_EVIL_BUFF = "1|10;2|20;3|30;4|40;5|50;6|60;7|70;8|80;9|90;10|100;11|110;12|120;13|130;14|140;15|150;16|160;17|170;18|180;19|190;20|200";
    public static final String UNION_KILL_EVIL_CHALLENGE_TIMES = "1";
    public static final String UNION_KILL_EVIL_FIGHT_ROUND = "15";
    public static final String UNION_KILL_EVIL_OPEN_TIME = "0|0|0;22|0|0";
    public static final String UNION_KILL_EVIL_CLOSE_TIME = "22|0|0;23|59|59";
    public static final String UNION_KILL_EVIL_BUFF_MAX_NUM = "1";
    public static final String UNION_KILL_EVIL_BUFF_BEING_TIME = "3600";
    public static final String BAD_CREDIT_SCORE_VIP_EXP = "100";
    public static final String BAD_CREDIT_SCORE_MUTE = "500|3600;1000|21600;2000|43200";
    public static final String BAD_CREDIT_SCORE_REDUCE_TIME = "1800";
    public static final String BAD_CREDIT_SCORE_PARAM = "100|250";
    public static final String MAGICTREASUER_PULL_REPEAT_PIECE = "50";
    public static final String MAGICTREASUER_QUICK_UNLOCK = "20";
    public static final String MAGICTREASUER_PIECE_ITEM_ID = "100063=1|100062=1|100061=1|100060=1";
    public static final String MAGICTREASUER_PIECE_LEVEL = "5";
    public static final String MAGICTREASUER_STAR_LEVEL_LIMIT = "0|0|0|0|5|10|20|20|20|0;0|0|0|0|5|10|20|30|40|0;0|0|0|0|5|10|20|30|40|0;0|0|0|0|3|5|8|10|10|0";
    public static final String MAGICTREASUER_PULL_WEIGHT = "7200|1000|100|1";
    public static final String FORBIDDEN_ZONE_ATTRIBUTE = "1000|20|500|2";
    public static final String FORBIDDEN_ZONE_SATAE = "1|0;2|500;3|1500";
    public static final String FORBIDDEN_ZONE_EXP_ITEM_NUM = "200";
    public static final String FORBIDDEN_ZONE_EXP_ITEM_EFFECT = "10";
    public static final String FORBIDDEN_ZONE_MONSTER_RANGE = "2";
    public static final String FORBIDDEN_ZONE_MONSTER_WALK_TIME = "3";
    public static final String FORBIDDEN_ZONE_MONSTER_WALK_DISTANCE = "1";
    public static final String FORBIDDEN_ZONE_MONSTER_NUM = "1|6;2|6;3|6;4|6";
    public static final String FORBIDDEN_ZONE_REWARD = "100000=50|100004=50";
    public static final String FORBIDDEN_ZONE_SKILL_POINT = "30";
    public static final String FORBIDDEN_ZONE_SKILL_POINT_EFFECT = "10";
    public static final String CUT_ROPE_REWARD = "100000=50|100004=50";
    public static final String UNION_FIGHT_APPLY_QUALIFICATION = "20";
    public static final String UNION_FIGHT_ENERGY = "1";
    public static final String UNION_FIGHT_CHARACTER_SCORE = "1|10;2|6;3|2";
    public static final String UNION_FIGHT_WORSHIP_REWARD = "100000=30";
    public static final String UNION_FIGHT_REQUEST = "2";
    public static final String UNION_FIGHT_POSTER_REWARD_SHOW = "160012=1|100044=1|100023=1|100004=1";
    public static final String UNION_FIGHT_ACTIVATION = "2800";
    public static final String ENERGY_AREA_OPEN_TIME = "600|720";
    public static final String ENERGY_AREA_FIGHT_TIME = "600|720";
    public static final String ENERGY_AREA_LEVEL_EFFECT = "300";
    public static final String ENERGY_AREA_LEVELUP_PROFIT = "100";
    public static final String ENERGY_AREA_LEVELUP_SPEND = "5000|10000|15000|20000|25000|30000|35000|40000|45000|50000|55000|60000|65000|70000|75000|80000|85000|90000|95000|100000";
    public static final String ENERGY_AREA_RANK_EFFECT = "156|50;236|100;336|150;456|200;596|250;756|300;936|350;1201|400;1501|450;1801|500;2101|550;2551|600;3001|650";
    public static final String ENERGY_AREA_TITLE_EFFECT = "1|200;2|400;3|600;4|800;5|1000;6|1200";
    public static final String ENERGY_AREA_OPEN_NUM = "1";
    public static final String ENERGY_AREA_ATTEND_NUM = "1";
    public static final String ENERGY_AREA_DURATION = "120";
    public static final String ENERGY_AREA_MAX_ITEM = "1|6;336|8;456|10;596|12;756|14";
    public static final String ENERGY_AREA_REWARD_FREQUENCY = "1";
    public static final String ENERGY_AREA_DISLODGE_CD = "5";
    public static final String ENERGY_AREA_PROTECT_CD = "30";
    public static final String ENERGY_AREA_ITEM_ID = "105044";
    public static final String ENERGY_AREA_WORSHIP_REWARD = "100000=30";
    public static final String ENERGY_AREA_BATTLELOG_NUM = "20";
    public static final String ENERGY_AREA_KICK_TIMES = "5";
    public static final String ENERGY_AREA_AD_LIMIT = "3";
    public static final String ENERGY_AREA_AD_COLD_TIME = "10";
    public static final String ENERGY_AREA_AD_REWARD = "105044=1";
    public static final String ENERGY_AREA_GUIDE_REWARD = "105044=1";
    public static final String ENERGY_AREA_SEARCH_COLDTIME = "10";
    public static final String STARCHALLENGE_ROUND_LIMIT = "30";
    public static final String STARCHALLENGE_SAFE_TIME = "300";
    public static final String STARCHALLENGE_RECORD_LIMIT = "10";
    public static final String STARCHALLENGE_FIGHT_LIMIT = "30";
    public static final String STARCHALLENGE_GIFT_PARAM = "20|50";
    public static final String STARCHALLENGE_DAILY_AWARD = "100000=50";
    public static final String QQ_COLOUR_SIGN_REWARD = "100000=200|100004=100|100016=5";
    public static final String QQ_ADD_DESKTOP_REWARD = "100000=200|100004=100|100016=5|100003=1000";
    public static final String MEITUAN_NEW_DAILY_REWARD = "100016=5";
    public static final String TIKTOK_ADD_DESKTOP_REWARD = "100000=200|100004=100|100016=5";
    public static final String TIKTOK_ADD_SIDEBAR_REWARD = "100000=200|100004=100|100016=5";
    public static final String HUAWEI_ADD_DESKTOP_REWARD = "100000=200|100004=100|100016=5";
    public static final String GODBODY_UNLOCK_PARAM = "0|0";
    public static final String GODBODY_SKILL_ID = "280001|280002";
    public static final String GODBODY_MAGICTREASUER_ID = "30046|30047";
    public static final String GODBODY_SCENE_ID = "440001|440002";
    public static final String GODBODY_HEADSHOT_ID = "149003|149004";
    public static final String GODBODY_HEADFRAME_ID = "146008|146009";
    public static final String GODBODY_SWITCH_COST = "1000|2000|3000|4000|5000";
    public static final String GODBODY_SWITCH_LIMIT = "1";
    public static final String GODBODY_BAG_TIME = "86400";
    public static final String BODYCHANGE_ITEM_ID = "100074";
    public static final String LITTLEMONSTER_CREATE_UNLOCK_SKIN = "144014|144015";
    public static final String SKYWAR_QUALIFICATION_LOCK_TIME = "1|0";
    public static final String SKYWAR_GROUP_TIME = "1|570";
    public static final String SKYWAR_FIGHT_START_TIME = "1|600";
    public static final String SKYWAR_SETTLE_TIME = "7|1320";
    public static final String SKYWAR_TRUCE_TIME = "7|1350";
    public static final String SKYWAR_FREE_FIGHT_TIMES = "5";
    public static final String SKYWAR_PAY_FIGHT_TIMES = "5";
    public static final String SKYWAR_TICKET_PRICE = "100000=50";
    public static final String SKYWAR_ROUND_LIMIT = "20";
    public static final String SKYWAR_MATCH_CRITERIA = "5|10;-4|4;-10|-5";
    public static final String SKYWAR_SPECIAL_MATCH_CRITERIA = "-10|3;-15|-11;-20|-16";
    public static final String SKYWAR_SPECIAL_MATCH_TIMES = "2";
    public static final String SKYWAR_FREE_REFRESH_TIMES = "5";
    public static final String SKYWAR_PAID_REFRESH_TIMES = "5";
    public static final String SKYWAR_PAID_REFRESH_PRICE = "100000=10";
    public static final String SKYWAR_WIN_SCORE = "500|260|200";
    public static final String SKYWAR_WIN_SCORE_ADDITION = "100";
    public static final String SKYWAR_FAIL_SCORE = "30";
    public static final String SKYWAR_BATTLELOG_NUM = "20";
    public static final String SKYWAR_INITIAL_SCORE = "2000";
    public static final String SKYWAR_WPRSHIP_REWARD = "100000=50";
    public static final String SKYWAR_JOIN_LIMIT = "156|3";
    public static final String SKYWAR_RANK_NUM = "50";
    public static final String SKYWAR_HIGH_RANK_REWARD = "1|50;149501=1";
    public static final String UNIONDUEL_APPLY_TIME = "1|0";
    public static final String UNIONDUEL_FIRST_MATCH_TIME = "2|0";
    public static final String UNIONDUEL_FIRST_PREPARE_TIME = "2|360";
    public static final String UNIONDUEL_FIRST_FIGHT_TIME = "2|600";
    public static final String UNIONDUEL_FIRST_REWARD_TIME = "3|1320";
    public static final String UNIONDUEL_SECOND_MATCH_TIME = "4|0";
    public static final String UNIONDUEL_SECOND_PREPARE_TIME = "4|360";
    public static final String UNIONDUEL_SECOND_FIGHT_TIME = "4|600";
    public static final String UNIONDUEL_SECOND_REWARD_TIME = "5|1320";
    public static final String UNIONDUEL_JOIN_LIMIT = "15|4200";
    public static final String UNIONDUEL_FIGHT_REWARD = "100032=300|100004=20;100032=200|100004=10";
    public static final String UNIONDUEL_GROUP = "2|10;3|5";
    public static final String UNIONDUEL_HP = "3";
    public static final String UNIONDUEL_FIGHT_TIMES = "3";
    public static final String UNIONDUEL_FIGHT_WIN_SCORE = "1|2;2|5;3|10";
    public static final String UNIONDUEL_FIGHT_FAIL_SCORE = "1";
    public static final String UNIONDUEL_SETTLE_REWARD = "1|99999,100044=200|100010=50|100004=200,100044=100|100010=25|100004=100";
    public static final String UNIONDUEL_SETTLE_REWARD_BUFF = "100|200,200;201|300,400;301|1000,600";
    public static final String UNIONDUEL_CLOUD_SHOW_NUM = "5|6|7";
    public static final String UNIONDUEL_MATCH_LIMIT = "4";
    public static final String UNIONDUEL_CARTOON_TIME = "20";
    public static final String CHARACTER_LENGTH_LIMIT_FOR_PLAYER_NAME = "12";
    public static final String WORLD_CHAT_SEND_WORD_LIMIT = "80";
    public static final String GAMEMARK_JUDGE_CONDITION = "2023|10|20";
    public static final String GAMEMARK_NEW_PLAYER_CONDITION = "20;56|1500;10";
    public static final String GAMEMARK_OLD_PLAYER_CONDITION = "500|10";
    public static final String GAMEMARK_POP_UP_CD = "168";
    public static final String GAMEMARK_REWARD = "100000=200|100004=100|100016=5";
    public static final String WECHAT_POP_UNLOCK = "10";
    public static final String HEADSHOT_RESET_PARAM = "7|30|-1";
    public static final String WORLDRULE_UNLOCK = "0|3|5";
    public static final String WORLDRULE_BASE_SKILL = "294001";
    public static final String WORLDRULE_BASE_SKILL_UPRATE = "250";
    public static final String WORLDRULE_BASE_COST = "100081=10";
    public static final String WORLDRULE_LOCK_COST = "100082=20|100082=40|100082=60|100082=80|100082=100";
    public static final String WORLDRULE_QUICK_COST_UNLOCK = "10";
    public static final String WORLDRULE_FREE_TIME = "2";
    public static final String WORLDRULE_RECORD_LIMIT = "50";
    public static final String WORLDRULEBOSS_QUICK_TIME = "1";
    public static final String WORLDRULEBOSS_UNLOCK_TIME = "0|5|10|15|20";
    public static final String WORLDRULEBOSS_QUICK_UNLOCK = "1";
    public static final String WORLDRULEBOSS_BATTLE_ROUND = "15";
    public static final String BLACKLIST_MAXNUM = "99";
    public static final String BAG_ADTIME_ITEM_ID = "100086";
    public static final String BAG_ADTIME_ITEM_RECYCLE = "100000=100";
    public static final String LINKAGE_SPECIAL_EQUIPMENT_ICON = "3080015,1=2100001|2=2100004;3100025,1=2100002|2=2100005;3100036,1=2100003|2=2100006";
    public static final String HOLYLAND_SENIOR_MATCH_SCORE = "12|11|10|9|8|7|6|5|4|3|2|1";
    public static final String HOLYLAND_JUNIOR_MATCH_SCORE = "12|11|10|9|8|7|6|5|4|3|2|1";
    public static final String HOLYLAND_CITY_OPEN_TIME = "15";
    public static final String HOLYLAND_MOVE_TIME = "10";
    public static final String HOLYLAND_FIGHT_CD = "5";
    public static final String HOLYLAND_ENERGY_LIMIT = "100";
    public static final String HOLYLAND_ENERGY_PLAYER_COST = "-99|1,6;2|2,4;3|99,2";
    public static final String HOLYLAND_ENERGY_NPC_COST = "0";
    public static final String HOLYLAND_FREE_REVIVE_TIMES = "5";
    public static final String HOLYLAND_PAID_REVIVE_TIMES = "5|25";
    public static final String HOLYLAND_QUIZ_INITIAL_SCORE = "200";
    public static final String HOLYLAND_QUIZ_WIN_PARAM = "0|100|50000|-300000;101|300|24000|-40000;301|1000|13700|-5700";
    public static final String HOLYLAND_QUIZ_FAIL_PARAM = "5000";
    public static final String HOLYLAND_QUIZ_HEAT = "20|1000000|50000";
    public static final String HOLYLAND_FIGHT_SETTLE_TIME = "300";
    public static final String HOLYLAND_FIGHT_BEGIN_TIME = "1200";
    public static final String HOLYLAND_FIGHT_DURATION = "60";
    public static final String HOLYLAND_POSTER_SHOW_REWARD = "100075=1|160011=1|100000=1|240014=1";
    public static final String HOLYLAND_JOIN_LIMIT = "4200";
    public static final String HOLYLAND_JOIN_RANK_LIMIT = "4|8,120;9|16,132;17|34,144;35|999,144";
    public static final String HOLYLAND_WEEKLY_MAP = "1001-2001|1002-2002|1003-2003|1004-2004|1005-2005|1006-2006|1007-2007|1008-2008|1009-2009|1010-2010|1011-2011|1012-2012|2001-1001,2002,2012,3001|2002-1002,2001,2003,3001|2003-1003,2002,2004,3002|2004-1004,2003,2005,3002|2005-1005,2004,2006,3003|2006-1006,2005,2007,3003|2007-1007,2006,2008,3004|2008-1008,2007,2009,3004|2009-1009,2008,2010,3005|2010-1010,2009,2011,3005|2011-1011,2010,2012,3006|2012-1012,2011,2001,3006|3001-2001,2002,3006,3002,2013|3002-2003,2004,3001,3003,2014|3003-2005,2006,3002,3004,2014|3004-2007,2008,3003,3005,2015|3005-2009,2010,3004,3006,2015|3006-2011,2012,3005,3001,2013|2013-4001,3006,3001|2014-4001,3002,3003|2015-4001,3004,3005|4001-2013,2014,2015";
    public static final String HOLYLAND_MONTHLY_MAP = "1001-2001|1002-2002|1003-2003|1004-2004|1005-2005|1006-2006|1007-2007|1008-2008|1009-2009|1010-2010|1011-2011|1012-2012|2001-1001,2002,2012,3001|2002-1002,2001,2003,3001|2003-1003,2002,2004,3002|2004-1004,2003,2005,3002|2005-1005,2004,2006,3003|2006-1006,2005,2007,3003|2007-1007,2006,2008,3004|2008-1008,2007,2009,3004|2009-1009,2008,2010,3005|2010-1010,2009,2011,3005|2011-1011,2010,2012,3006|2012-1012,2011,2001,3006|3001-2001,2002,3006,3002,2013|3002-2003,2004,3001,3003,2014|3003-2005,2006,3002,3004,2014|3004-2007,2008,3003,3005,2015|3005-2009,2010,3004,3006,2015|3006-2011,2012,3005,3001,2013|2013-4001,3006,3001|2014-4001,3002,3003|2015-4001,3004,3005|4001-2013,2014,2015";
    public static final String HOLYLAND_GROUP_NUM = "12|12";
    public static final String HOLYLAND_LIST_LOCK = "600";
    public static final String HOLYLAND_SHOW_IMAGE = "10";
    public static final String HOLYLAND_ACCELERATE_LIMIT = "10";
    public static final String HOLYLAND_REVIVE_CD = "60";
    public static final String HOLYLAND_ROUND_LIMIT = "20";
    public static final String HOLYLAND_MULTI_KILL_SHOW = "5|10|15|20|25|30";
    public static final String HOLYLAND_WEEKLY_UPRANK_NUM = "4|8,12;9|16,12;17|34,12;35|999,12";
    public static final String HOLYLAND_MONTHLY_UPRANK_NUM = "6";
    public static final String HOLYLAND_INITIAL_RANKUP_NUM = "4|8,12;9|16,24;17|34,24;35|999,24";
    public static final String HOLYLAND_REVIVE_ITEM = "100090";
    public static final String HOLYLAND_WORSHIP_REWARD = "100000=50";
    public static final String HOLYLAND_SPEEDUP_COST = "100000=20";
    public static final String HOLYLAND_QUIZ_ITEM = "100092";
    public static final String HOLYLAND_ACCELERATE_PERCENT = "500";
    public static final String MOUNTAINSEA_REFRESH_TIME = "7|0";
    public static final String MOUNTAINSEA_TEAM_LIMIT = "10";
    public static final String MOUNTAINSEA_TEAM_LIST_LIMIT = "50";
    public static final String MOUNTAINSEA_TEAM_NAME_LIMIT = "12";
    public static final String MOUNTAINSEA_MATCH_WAIT_TIME = "5";
    public static final String MOUNTAINSEA_KICK_CD_TIME = "10";
    public static final String MOUNTAINSEA_PREPARE_TIME = "300";
    public static final String MOUNTAINSEA_MATCH_PARAM = "1";
    public static final String MOUNTAINSEA_CHALLENGE_TIME = "5";
    public static final String MOUNTAINSEA_HELP_TIME = "8";
    public static final String MOUNTAINSEA_ROUND_RECOVER = "200";
    public static final String MOUNTAINSEA_BIG_SKILL_PARAM = "1000";
    public static final String MOUNTAINSEA_BIG_SKILL_ID = "330010|330020|330030|330040";
    public static final String MOUNTAINSEA_WORSHIP_REWARD = "100000=50";
    public static final String MOUNTAINSEA_INVITE_LIMIT = "50";
    public static final String MOUNTAINSEA_SKIP_LIMIT = "5";
    public static final String SPRING_FESTIVAL_REWARD = "100000=88|100004=88|100009=888|100023=1";
    public static final String SPRING_FESTIVAL_REWARD_LIMIT = "5|1";
    public static final String UNIONGIFT_REWARD = "50|100";
    public static final String BATTLE_SPEED_THREE = "20|156";
    public static final String REVIEW_REWARD = "100004=5";
    public static final String SETHOME_REWARD = "100000=100|100004=200|100025=3";
    public static final String NEWYEARRED_JADEBAG_ID = "150212";
    public static final String NEWYEARRED_TOKENBAG_ID = "150213|150214|150215";
    public static final String NEWYEARRED_ILLUSIONBAG_ID = "144093";
    public static final String NEWYEARRED_RANDOMBOXES_ID = "105089";
    public static final String NEWYEARRED_MEITUANCOIN_ID = "105246";
    public static final String NEWYEARRED_RED_ID = "105072";
    public static final String NEWYEARRED_SHOW = "10";
    public static final String NEWYEARRED_MAIN_SHOW_ITEM_ID = "100000|100045|144093";
    public static final String NEWYEARRED_DRAW_LIMIT = "156";
    public static final String NEWYEARRED_DEVICE_LIMIT = "1";
    public static final String NEWYEARRED_BOXES_LIMIT = "1";
    public static final String NEWYEARRED_TEN_LIMIT = "10";
    public static final String NEWYEARRED_SKIP_LIMIT = "2";
    public static final String NEWYEARRED_DRAW_NOTICE = "160058|105089";
    public static final String NEWYEARRED_RED_OPEN_LIMIT = "10|10|10|10|10|10|10|10|50";
    public static final String NEWYEARRED_RED_GET_LIMIT = "3|5|7|9|11|13|15|17|20|23|26|29|32|35|38|40";
    public static final String NEWYEARRED_ITEM_RECYCLE = "105072-100000=50|105089-100000=100";
    public static final String NEWYEARRED_COIN_GUARANTEE = "4";
    public static final String REALMSTRIBULATION_CONFIRM = "3|335";
    public static final String ADDRAW_DAYLIMIT = "30";
    public static final String WESTJOURNEY_BORN_CHARACTER_LIST = "2=144031|5=144032|1=144033|3=144034";
    public static final String REPORT_CD = "10";
    public static final String PUPIL_SEAT_UNLOCK = "1|2|5|10|15";
    public static final String PUPIL_BATTLE_SEAT_UNLOCK = "1|3|8|13|20";
    public static final String PUPIL_BATTLE_SEAT_PARAM = "1500|1250|1250|1000|1000";
    public static final String PUPIL_ENERGY_RECOVER_TIME = "300";
    public static final String PUPIL_ATTR_PARAM = "5";
    public static final String PUPIL_ENERGY_ITEM_COST = "100101=1";
    public static final String PUPIL_ENERGY_LIMIT = "100|10000";
    public static final String PUPIL_QUICK_PULL_UNLOCK = "2";
    public static final String PUPIL_QUICK_TRAIN_UNLOCK = "3";
    public static final String PUPIL_QUICK_TRAIN_PARAM = "5";
    public static final String PUPIL_QUICK_TRAIN_PARAM2 = "1|5;30|10;40|20";
    public static final String PUPIL_FINISH_SEAT_LIMIT = "20";
    public static final String PUPIL_CONSORT_PUBLIC_TIME = "72";
    public static final String PUPIL_CONSORT_REQUEST_LIMIT = "20";
    public static final String PUPIL_CONSORT_UNION_REQUEST_LIMIT = "10";
    public static final String PUPIL_CONSORT_WORLD_REQUEST_LIMIT = "10";
    public static final String PUPIL_CONSORT_SERVER_REQUEST_LIMIT = "10";
    public static final String PUPIL_CONSORT_FREE_REFRESH_LIMIT = "10";
    public static final String PUPIL_CONSORT_FREE_WORLD_REFRESH_LIMIT = "10";
    public static final String PUPIL_CONSORT_FREE_SERVER_REFRESH_LIMIT = "10";
    public static final String PUPIL_CONSORT_REFRESH_COST = "100000=10";
    public static final String PUPIL_CONSORT_SERVER_FREE_REFRESH_LIMIT = "20";
    public static final String PUPIL_CONSORT_SERVER_REFRESH_COST = "100000=10";
    public static final String PUPIL_RES_CHANGE = "500";
    public static final String PUPIL_CONSORT_ATTR_LIMIT = "3000";
    public static final String PUPIL_CONSORT_SPECIAL_ATTR_LIMIT = "2000";
    public static final String PUPIL_CONSORT_ITEM_PARAM = "20";
    public static final String PUPIL_CONSORT_ITEM_AWARD = "100004=1";
    public static final String PUPIL_CONSORT_NPC_PARAM = "99999999";
    public static final String PUPIL_RANK_SIZE = "200";
    public static final String PUPIL_RANK_NOTICE_LIMIT = "10";
    public static final String PUPIL_RANK_NOTICE_NUM_LIMIT = "20";
    public static final String PUPIL_SYSTEM_LEVEL = "1=1|6=2|11=3|16=4|21=5|26=6|31=8";
    public static final String PUPIL_LIST_LIMIT = "100";
    public static final String PUPIL_NAME_LIMIT = "8";
    public static final String PUPIL_CONSORT_PROTECT_TIME = "300";
    public static final String PUPIL_AD_TIME = "2";
    public static final String PUPIL_AD_AWRAD = "100101=100";
    public static final String PUPIL_FIND_CD = "10";
    public static final String WECHAT_FRIEND_RANK_LIMIT = "100";
    public static final String ALIPAY_FOCUS_REWARD = "100000=200|100004=100|100016=5";
    public static final String CLOUD_REFINE_ITEM = "100116=200";
    public static final String CLOUD_REFINE_RESIS_LIST = "400101|400102|400103|400104|400105|400106";
    public static final String CLOUD_REFINE_ICON = "400001|400002|400003|400004|400005|400006";
    public static final String CLOUD_REFINE_RESIS_WEIGHT = "400101=100|400102=100|400103=100|400104=100|400105=100|400106=100";
    public static final String CLOUD_REFINE_RESIS_PROB = "1000|1000|1000|1000|1000|1000|1000|1000|1000|1000|900|900|900|900|900|900|900|900|900|900|800|800|800|800|800|800|800|800|800|800|700|700|700|700|700|700|700|700|700|700|600|600|600|600|600|600|600|600|600|600|600|600|600|600|600|600|600|600|600|600|500|500|500|500|500|500|500|500|500|500|500|500|500|500|500|500|500|500|500|500|400|400|400|400|400|400|400|400|400|400|400|400|400|400|400|400|400|400|400|400|300|300|300|300|300|300|300|300|300|300|300|300|300|300|300|300|300|300|300|300|200|200|200|200|200|200|200|200|200|200|200|200|200|200|200|200|200|200|200|200|100";
    public static final String CLOUD_REFINE_RESIS_DIFF = "5";
    public static final String CLOUD_ORIGIN_PARAM = "285|1869|44";
    public static final String SUPPRESSEVIL_MATCH_SCORE = "1-1|100;2-2|80;3-3|60;4-5|50;6-10|40;11-20|30;21-30|20;31-50|10;51-10000|5";
    public static final String SUPPRESSEVIL_MATCH_COUNT = "8";
    public static final String SUPPRESSEVIL_JOIN_RANK_LIMIT = "1|3,120;4|8,120;9|16,160;17|34,200;35|999,240";
    public static final String SUPPRESSEVIL_FIGHT_ACTIVATION = "2800";
    public static final String SUPPRESSEVIL_OPEN_DAY = "7";
    public static final String SUPPRESSEVIL_TIME_PARAM = "10|16;16|22";
    public static final String SUPPRESSEVIL_HARRY_PARAM = "200|400|600|800";
    public static final String SUPPRESSEVIL_CHALLENGE_TIME = "3";
    public static final String SUPPRESSEVIL_HARRY_TIME = "3";
    public static final String SUPPRESSEVIL_INVITE_ITEM_ID = "100122";
    public static final String SUPPRESSEVIL_HARRY_TIME_PARAM = "50";
    public static final String SUPPRESSEVIL_HARRY_LIMIT = "500";
    public static final String SUPPRESSEVIL_SETTLE_TIME = "300";
    public static final String SUPPRESSEVIL_BATTLE_ROUND = "20";
    public static final String SUPPRESSEVIL_HARRY_LOG_LIMIT = "50";
    public static final String SUPPRESSEVIL_BATTLE_LOG_LIMIT = "50";
    public static final String SUPPRESSEVIL_MONSTER_SORT_LIST = "1|2|3|4;5|6|7|8;2|3|4|1;6|7|8|5;3|4|1|2;7|8|5|6;4|1|2|3;8|5|6|7";
    public static final String SUPPRESSEVIL_BACKGROUND_TURN = "2|9|17";
    public static final String SUPPRESSEVIL_POSTER_SHOW_REWARD = "146027=1|100004=1|100101=1|100000=1";
    public static final String BANQUET_LUXURY_REWARD = "100004=240;100004=120;100004=60";
    public static final String BANQUET_HIGH_REWARD = "100004=120;100004=60;100004=30";
    public static final String BANQUET_VIP_SEAT_PARAM = "20|10";
    public static final String BANQUET_JOIN_LIMIT = "1|3";
    public static final String BANQUET_ASK_LIMIT = "100";
    public static final String BANQUET_JOIN_LEVEL_LIMIT = "6";
    public static final String BANQUET_JOIN_SETTING_PARAM = "1|10";
    public static final String UNIONBOUNTY_DAILY_TIMES = "2";
    public static final String UNIONBOUNTY_OPEN_DURATION = "8|24";
    public static final String UNIONBOUNTY_TRANSFER_TIME = "14400|14400|14400|14400|14400";
    public static final String UNIONBOUNTY_FREE_ROB_TIMES = "2";
    public static final String UNIONBOUNTY_PAY_ROB_TIMES = "10";
    public static final String UNIONBOUNTY_PAY_ROB_COST = "100000=20;100000=40;100000=60;100000=80;100000=100";
    public static final String UNIONBOUNTY_SHOW_CAR_NUM = "15";
    public static final String UNIONBOUNTY_ROB_LIMIT = "3";
    public static final String UNIONBOUNTY_ROB_REWARD_PERCENTUM = "100";
    public static final String UNIONBOUNTY_INITIAL_ENERGE = "100";
    public static final String UNIONBOUNTY_GUARANTEE_ENERGE = "1";
    public static final String UNIONBOUNTY_ENERGE_COST = "-99|2,10;3|99,0";
    public static final String UNIONBOUNTY_MONSTER_INVITE_CD = "60";
    public static final String UNIONBOUNTY_PERSONAL_BETTLELOG_LIMIT = "50";
    public static final String UNIONBOUNTY_GROUP_BETTLELOG_LIMIT = "50";
    public static final String UNIONBOUNTY_WORSHIP_REWARD = "100000=50";
    public static final String UNIONBOUNTY_FIND_COST = "100000=50";
    public static final String UNIONBOUNTY_REFRESH_COST = "100000=20;100000=40;100000=60;100000=80;100000=100";
    public static final String UNIONBOUNTY_REFRESH_FREE_TIMES = "3";
    public static final String UNIONBOUNTY_STATE_CAR_NUM = "3";
    public static final String UNIONBOUNTY_NPC_PARAM = "130001|141001;130002|142001;130003|143001";
    public static final String UNIONBOUNTY_NPC_LEVEL = "60";
    public static final String UNIONBOUNTY_NPC_CAR_QUALITY = "1|2";
    public static final String UNIONBOUNTY_NPC_POWER = "100000";
    public static final String UNIONBOUNTY_RANK_LIMIT = "100";
    public static final String UNIONBOUNTY_REFRESH_RANGE = "-50|50";
    public static final String UNIONBOUNTY_MONSTER_ENSURE_NUM = "20";
    public static final String UNIONBOUNTY_MONSTER_WAIT_PARAM = "5";
    public static final String UNIONBOUNTY_MONSTER_HELP_REWARD = "100032=200";
    public static final String UNIONBOUNTY_MONSTER_HELP_TIMES = "1";
    public static final String UNIONBOUNTY_MONSTER_ROUND_LIMIT = "20";
    public static final String UNIONBOUNTY_MONSTER_INVITE_LIMIT = "10";
    public static final String UNIONBOUNTY_MONSTER_POSITION_LIMIT = "10";
    public static final String UNIONBOUNTY_MONSTER_QUALITY = "6";
    public static final String FRIEND_MAKE_CONDITION = "3";
    public static final String FRIEND_MAKE_LIMIT = "20";
    public static final String FRIEND_CHATLOG_LIMIT = "50";
    public static final String FRIEND_GIFT_REWARD = "100000=5";
    public static final String FRIEND_CROSS_SERVER_CHAT_LIMIT = "156";
    public static final String FRIEND_CROSS_SERVER_CHATLOG_LIMIT = "50";
    public static final String FRIEND_GET_GIFT_LIMIT = "10";
    public static final String AUTOTALENT_COST_RATE = "1|1;2|20;3|40";
    public static final String SUBSCRIBE_REWARD = "100004=100|100010=20|100005=12|100007=20";
    public static final String FAIRYLAND_BOSS_PARAM = "150001|900;150002|900;150003|900;150004|900;150005|900";
    public static final String FAIRYLAND_BOSS_ROUND_LIMIT = "20";
    public static final String FAIRYLAND_BOSS_CHALENGE_CHANCE = "5";
    public static final String FAIRYLAND_BOSS_CHALENGE_CD = "21600";
    public static final String FAIRYLAND_GUARD_POWER = "250000000";
    public static final String FAIRYLAND_ENERGY_RECOVER = "432";
    public static final String FAIRYLAND_HELP_TIMES = "1";
    public static final String FAIRYLAND_HELP_EFFECT = "250";
    public static final String FAIRYLAND_HELP_LIMIT = "4";
    public static final String FAIRYLAND_HELP_REWARD = "100004=20";
    public static final String FAIRYLAND_ACHIEVE_REWARD = "146033=1";
    public static final String FAIRYLAND_HEADSHOT_ID = "149008=1";
    public static final String FAIRYLAND_BOSS_SPIRIT_SKILL_LEVEL = "150001,3|3|3;150002,3|3|3;150003,3|3|3;150004,3|3|3;150005,3|3|3";
    public static final String FAIRYLAND_HEADSHOT_PET_SKILL_LEVEL = "150001,5;150002,5;150003,5;150004,5;150005,5";
    public static final String UNIVERSE_COST_RATE = "1|1;10|2;30|5;50|10;100|50;150|100";
    public static final String UNIVERSE_SPOT_UNLOCK = "1|10|20";
    public static final String UNIVERSE_DRAW_ITEM = "100124";
    public static final String UNIVERSE_DRAW_RECOVER_LIMIT = "1|10";
    public static final String UNIVERSE_DRAW_RECOVER_RATE = "1|8640";
    public static final String UNIVERSE_DRAW_OBSERVE_RATE = "1|1100;51|1200;101|1300;151|1400;201|1500";
    public static final String UNIVERSE_DRAW_RANK_LIMIT = "50";
    public static final String UNIVERSE_SKILL_DRAW_WEIGHT = "420|240|120|40;120|40|15|5";
    public static final String UNIVERSE_SKILL_TRANSLATE_PARAM = "50";
    public static final String UNIVERSE_SKILL_STAR_LIMIT = "25";
    public static final String UNIVERSE_SKILL_DRAW_ENSURE_NUM = "100|100";
    public static final String UNIVERSE_SKILL_DRAW_ENSURE_QUALITY = "3,80|4,20;4,100";
    public static final String UNIVERSE_SKILL_PIECE_TRANSLATE_PARAM = "100127=1|100127=5|100127=15|100127=30";
    public static final String UNIVERSE_SKILL_DRAW_COST = "100126=1";
    public static final String UNIVERSE_SKILL_DRAW_FREE_TIMES = "2";
    public static final String UNIVERSE_SKILL_UPGRADE_PARAM = "50|50|100|100|150|150|200|200|300|300|400|400|500|500|600|700|800|1000|1500|2000|3000|5000|7000|10000";
    public static final String UNIVERSE_OBSERVE_NPC_PARAM = "1|30|141009;2|50|142009;3|70|143009";
    public static final String UNIVERSE_SKILL_SPECIAL_DRAW = "1011";
    public static final String UNIVERSE_SKILL_ROUND = "3";
    public static final String UNIVERSE_LEVEL_PREVIEW_LIMIT = "1000";
    public static final String TIKTOK_STORE_DESKTOP_REWARD = "100004=5";
    public static final String TIKTOK_STORE_DESKTOP_DAILY_REWARD = "1,100000=50|100004=25|100016=2;2,100000=50|100004=25|100003=1000;5,100000=50|100004=25|100016=1;7,100000=50|100004=25|100016=2";
    public static final String ALIPAY_STORE_DESKTOP_REWARD = "100004=5";
    public static final String ALIPAY_STORE_DESKTOP_DAILY_REWARD = "1,100000=50|100004=25|100016=2;2,100000=50|100004=25|100003=1000;5,100000=50|100004=25|100016=1;7,100000=50|100004=25|100016=2";
    public static final String UNIONGIFT_RESET_TIME = "22";
    public static final String PERSONGIFT_RESET_TIME = "22";
    public static final String IMAGE_UPGRADE_PARAM = "2|100134=50;3|100135=50";
    public static final String PLAYER_NAME_RESET_CD = "7|30|-1";
    public static final String PLAYER_HEAD_RESET_CD = "7|30|-1";
    public static final String CHAT_BLOCK_LIMIT = "0=8|100=80";
    public static final String CHAT_BLOCK_DECISION_TIME = "600";
    public static final String CHAT_BLOCK_FORBIDDEN_WORD = "1";
    public static final String CHAT_BLOCK_ADD_TIME_LIMIT = "24";
    public static final String CHAT_BLOCK_BANNED_LIMIT = "3";
    public static final String ALIPAY_FOLLOW_REWARD = "100000=100|100004=50|100101=50";
    public static final String YARD_BUILD_QUEUE_PARAM = "1|2";
    public static final String YARD_BUILD_HELP_EFFECT = "300";
    public static final String YARD_BUILD_HELP_LIMIT = "10|999";
    public static final String YARD_BUILD_ITEM_ACCELERATE_TIME = "300";
    public static final String YARD_BUILD_ITEM_ACCELERATE_COST = "100003=5000|100003=20000|100003=50000|100003=100000|100003=200000";
    public static final String YARD_BUILD_SETTLE_RATE = "300";
    public static final String YARD_BUILD_TRANSLATE_PARAM = "50";
    public static final String YARD_BUILD_DRAW_WEIGHT = "0|0|0|0;645|320|30|5";
    public static final String YARD_BUILD_DRAW_COST = "100152=1";
    public static final String YARD_BUILD_DRAW_FREE_TIMES = "1";
    public static final String YARD_BUILD_DRAW_AD_TIMES = "2";
    public static final String YARD_BUILD_DRAW_ENSURE_NUM = "100|100";
    public static final String YARD_BUILD_DRAW_ENSURE_QUALITY = "3,80|4,20;4,100";
    public static final String YARD_BUILD_PIECE_TRANSLATE_PARAM = "100153=2|100153=5|100153=20|100153=60|100153=300";
    public static final String YARD_SHOP_PRICE_RANDOM_PARAM = "1|5,1|1;6|10,1|1;11|15,1|1;16|20,1|1;21|25,1|1;26|28,1|1";
    public static final String YARD_SHOP_PRICE_RANDOM_RATE = "1000";
    public static final String YARD_PERSON_WALK_COORD = "0|0;500|500;500|2000;1000|2000;2000|2000";
    public static final String YARD_FAIRY_WALK_COORD = "0|0;500|500;500|2000;1000|2000;2000|2000";
    public static final String YARD_TREE_FREE_PRODUCT_LIMIT = "100004=50";
    public static final String YARD_TREE_HELP_PRODUCT_LIMIT = "100004=10";
    public static final String YARD_TREE_HELP_TIMES = "5";
    public static final String YARD_BOARD_LEVEL_LIMIT = "96";
    public static final String YARD_TREE_FREE_PRODUCT_RATE = "300|1";
    public static final String YARD_TREE_HELP_PRODUCT_TIME = "3600";
    public static final String YARD_TREASURE_PRODUCT_RATE = "300|1";
    public static final String YARD_ELIXIR_COST = "500";
    public static final String YARD_BUILD_SPECIAL_DRAW = "41002";
    public static final String YARD_AREA_MONSTER = "1|180001,50;2|180002,70;3|180003,80;4|180004,90;5|180005,100;6|180006,110;7|180007,130;8|180008,150";
    public static final String YARD_BOARD_MESSAGE_NUM = "10";
    public static final String YARD_BARRIER_PARAM = "1-101=47|47,102=46|50;2-101=37|24,102=32|22";
    public static final String YARD_BOARD_TOP_MESSAGE_NUM = "3";
    public static final String YARD_BUILD_SHOP_REFRESH_LIMIT = "0|0|0|50|50|50";
    public static final String YARD_TREE_INITIAL_LOCATION = "18|26";
    public static final String YARD_BOARD_INITIAL_LOCATION = "21|25";
    public static final String YARD_FIELD_ITEM_ID = "100158";
    public static final String YARD_BUILD_ACCELERATE_ITEM_COST = "100162=1";
    public static final String YARD_BUILD_ACCELERATE_ITEM_EFFECT = "300";
    public static final String YARD_BOARD_CHARACTER_LIMIT = "40";
    public static final String YARD_BUILD_SHOP_REFRESH_PRICE = "1|100141=10000;2|100141=25000;3|100141=150000;4|100141=900000";
    public static final String YARD_BUILD_SHOP_RANGE = "4,44001|44002|44003|44004";
    public static final String YARD_VISIT_LOG_LIMIT = "50";
    public static final String YARD_BOX_RANDOM_WEIGHT = "645|320|30|5";
    public static final String YARD_ELIXIR_PRODUCT_LIMIT = "999";
    public static final String YARD_ELIXIR_USE_LIMIT = "999";
    public static final String YARD_REPORT_COLDTIME = "60";
    public static final String YARD_REPORT_TIMELIMIT = "3600";
    public static final String YARD_COMMON_ITEM_ID = "4|100195;5|100196";
    public static final String YARD_TRANSLATE_COMMON_ITEM_CONDITION = "7";
    public static final String PLANESTRIAL_PLAYER_APPLY_LIMIT = "3";
    public static final String PLANESTRIAL_TEAM_APPLY_LIMIT = "100";
    public static final String PLANESTRIAL_LEAVE_TEAM_CD = "5";
    public static final String PLANESTRIAL_HISTORY_MESSAGE_LIMIT = "50";
    public static final String PLANESTRIAL_RAMDOM_MATCH_LIMIT = "1";
    public static final String PLANESTRIAL_OPEN_CONDITION = "100";
    public static final String PLANESTRIAL_SUPRESS_LEVEL = "1|335;2|595;3|935";
    public static final String PLANESTRIAL_MONSTER_BUFF = "1,490001|490002|490003;2,490004|490005|490006;3,490007|490008|490009;4,490010|490011|490012;5,490013|490014|490015;6,490016|490017|490018";
    public static final String PLANESTRIAL_MONSTER_BUFF_PARAM = "1|31|61";
    public static final String PLANESTRIAL_EQUIPMENT_PARAM = "1000";
    public static final String PLANESTRIAL_GROUP_TIMIES = "3";
    public static final String PLANESTRIAL_DAILY_FAIL_TIMES = "10";
    public static final String PLANESTRIAL_LEVEL_PARAM = "1,7|8;2,9|10;3,11|12";
    public static final String PLANESTRIAL_SHOW_REWARD = "1,160050|100064|100154;2,160050|100064|100154;3,160050|100064|100154";
    public static final String PLANESTRIAL_SETTLE_TIME = "22";
    public static final String PLANESTRIAL_SETTLE_DURATION = "300";
    public static final String PLANESTRIAL_GROUP_TIME = "0|10";
    public static final String PLANESTRIAL_FIGHT_ROUND = "20";
    public static final String PLANESTRIAL_NAME_LIMIT = "12";
    public static final String PLANESTRIAL_BUFF_ID = "491001|491002|491003|491004|491005|491006|491007|491008|491009|491010|491011|491012|491013|491014|491015|491016|491017|491018|491019|491020|491021|491022|491023|491024|491025|491026|491027|491028|491029|491030|491031|491032|491033|491034|491035|491036|491037|491038|491039|491040|491041|491043";
    public static final String PLANESTRIAL_BUFF_CD = "15";
    public static final String PLANESTRIAL_PLAYBACK_LIMIT = "20";
    public static final String PLANESTRIAL_BIG_REWARD = "160050=1,100,1;160050=1,100,1;240010=10,100,1;100004=150,100,0;100004=100,100,0;100025=30,100,0;100025=20,100,0;100064=5,100,0;100064=3,100,0";
    public static final String PLANESTRIAL_BIG_REWARD_UNLOCK = "1,30|50|80|-1|-1|-1|-2|-2|-2;2,15|25|40|55|65|80|-2|-2|-2;3,10|20|30|40|50|60|70|80|90";
    public static final String PLANESTRIAL_BATTLE_SKIP_CD = "10";
    public static final String PLANESTRIAL_RANK_SHOW_LIMIT = "100";
    public static final String UNIONTREASURE_OPEN_TIME = "10|22";
    public static final String UNIONTREASURE_DAILY_DRAW_TIMES = "3";
    public static final String UNIONTREASURE_DRAW_WEIGHT = "100|10";
    public static final String UNIONTREASURE_CHANGE_DRAW_WEIGHT = "100|200";
    public static final String UNIONTREASURE_CHANGE_DRAW_DAY = "4";
    public static final String UNIONTREASURE_MAP_DRAW_LIMIT = "5";
    public static final String UNIONTREASURE_RELIC_PROB = "100|150";
    public static final String UNIONTREASURE_RELIC_REWARD = "100004=50|100032=500,100;100025=10|100032=500,100;100000=200|100032=500,100";
    public static final String UNIONTREASURE_RELIC_SHARE_LIMIT = "2";
    public static final String UNIONTREASURE_RELIC_REWARD_MAX = "1";
    public static final String UNIONTREASURE_BOSS_REWARD = "100023=3|100032=500,100;100010=50|100032=500,100;100005=40|100032=500,100";
    public static final String UNIONTREASURE_BOSS_SHARE_LIMIT = "2";
    public static final String UNIONTREASURE_ITEMS = "100143|100144|100145|100146|100147|100148|100149|100150|100151";
    public static final String UNIONTREASURE_FIND_AREA = "3";
    public static final String UNIONTREASURE_GUARANTEE = "5|6";
    public static final String UNIONTREASURE_LEVELUP_PREVIEW = "8,100047|100007|150060|100044|100064;13,100116|100099|100081|100100|100126";
    public static final String MOTIONSENSE_OBTAIN_RATE = "500";
    public static final String MOTIONSENSE_GOLDEN_USE = "1|1";
    public static final String MOTIONSENSE_EQUIPMENT_TIMES = "5";
    public static final String MOTIONSENSE_GOLDEN_LIMIT = "5";
    public static final String MOTIONSENSE_ORIGIN_SPEED = "120";
    public static final String MOTIONSENSE_ACCEL_SPEED = "120";
    public static final String MOTIONSENSE_BACK_SPEED = "41";
    public static final String MOTIONSENSE_STOP_INTERVAL = "500";
    public static final String MOTIONSENSE_TIME_INTERVAL = "500";
    public static final String MOTIONSENSE_THRESHOLD = "1200";
    public static final String MOTIONSENSE_HUAWEI_THRESHOLD = "3000";
    public static final String MOTIONSENSE_GOLDEN_ITEM_ID = "100155";
    public static final String MEITUAN_SUBSCRIBE_REWARD = "100000=100";
    public static final String MEITUAN_DAILY_REWARD = "1,100000=50|100010=20;2,100016=5|100009=200;3,100000=50|100010=20;4,100016=5|100009=200;5,100000=50|100010=20;6,100016=5|100009=200;7,100000=50|100010=20";
    public static final String SYSTEM_LIST_SHOW_LIMIT = "11";
    public static final String ATTRIBUTE_VS_JUMP = "21,10206=0|10207=0|10208=0|10209=0|10210=0|10211=0;22,10207=0|10208=0|10209=0|10210=0|10211=0;23,10212=0|10207=0|10208=0|10209=0|10210=0;24,10212=0|10207=0|10208=0|10209=0|10210=0;25,10212=0|10207=0|10208=0|10209=0|10210=0|10211=0;26,10212=0|10207=0|10208=0|10209=0|10210=0|10211=0;27,10212=0|10207=0|10208=0|10209=0|10210=0|10211=0;28,10212=0|10207=0|10208=0|10209=0|10210=0|10211=0;31,10207=0|10213=0|10209=0|10210=0|10211=0;34,10207=0|10209=0|10210=0;30,10207=0|10213=0|10209=0;33,10213=0;29,10214=0|10215=0|10216=0|10217=0;32,10213=0;35,10209=0;36,10209=0;37,10209=0;38,10209=0;39,10210=0;40,10210=0;41,10210=0;42,10210=0;43,10207=0;44,10207=0;45,10207=0;46,10207=0;48,10218=0|10219=0;49,10218=0|10219=0;50,10227=0;51,10227=0;52,10242=0;53,10242=0;54,10247=0;55,10247=0;56,10318=0;57,10318=0";
    public static final String MEMORYCOLLECTION_SEASON_SET = "3";
    public static final String MEMORYCOLLECTION_EXCHANGE_STAR_LIMIT = "0";
    public static final String MEMORYCOLLECTION_EXCHANGE_WEEK_LIMIT = "15";
    public static final String MEMORYCOLLECTION_EXCHANGE_OPEN_DAY = "720";
    public static final String MEMORYCOLLECTION_EXCHANGE_CARD_LIMIT = "10";
    public static final String MEMORYCOLLECTION_EXCHANGE_TIME_LIMIT = "1440|1440";
    public static final String MEMORYCOLLECTION_DEMAND_WEEK_LIMIT = "30";
    public static final String MEMORYCOLLECTION_LEVEL = "15=156,100|30=236,100|50=336,100";
    public static final String MEMORYCOLLECTION_LOG_LIMIT = "30";
    public static final String MEMORYCOLLECTION_EXCHANGE_TIMES_LIMIT = "100";
    public static final String MEMORYCOLLECTION_EXCHANGE_SEASON_LIMIT = "5=15|4=25";
    public static final String MEMORYCOLLECTION_ITEM_RECYCLE_LIST = "105168";
    public static final String MEMORYCOLLECTION_SCORE_ID = "105169";
    public static final String MEMORYCOLLECTION_WARNING_LIMIT = "720";
    public static final String MEMORYCOLLECTION_SEASON_POTER-1 = "380009=1|100152=100|100000=750";
    public static final String MEMORYCOLLECTION_SEASON_POTER-2 = "380010=1|100152=100|100000=750";
    public static final String MEMORYCOLLECTION_SEASON_POTER-3 = "380013=1|100152=100|100000=750";
    public static final String WESTJOURNEY_DAILY_REWARD = "100000=10|100004=10";
    public static final String WESTJOURNEY_ROLE_POOL = "144031|144032|144033|144034|144035";
    public static final String WESTJOURNEY_MONSTER_POOL_0 = "100121|100002|100007|101001";
    public static final String WESTJOURNEY_MONSTER_POOL_1 = "100122|100123|101003";
    public static final String WESTJOURNEY_MONSTER_POOL_2 = "100124|100118|100119";
    public static final String WESTJOURNEY_MONSTER_POOL_3 = "100112|100113|100114";
    public static final String WESTJOURNEY_ROPE_LITTLE = "100002|100007|100122|100123|100112|100113|100114|100119";
    public static final String WESTJOURNEY_ROPE_BIG = "101001|101003|100121";
    public static final String WARSEASON_FIGHT_TIME = "2";
    public static final String WARSEASON_CONDUCT_TIME = "12|13";
    public static final String WARSEASON_MARCH_TIME = "7";
    public static final String WARSEASON_PROPERTY_PARAM = "1|100;50|150;100|200;150|300;200|400;275|500;350|600;425|700;500|800;600|900;700|1000";
    public static final String WARSEASON_INITIAL_ENERGY = "100";
    public static final String WARSEASON_ENERGY_COST = "-99|1,8;2|2,6;3|99,4";
    public static final String WARSEASON_RECOVER_FREE_TIMES = "3";
    public static final String WARSEASON_RECOVER_FREE_CD = "300";
    public static final String WARSEASON_RECOVER_PAID_TIMES = "9";
    public static final String WARSEASON_RECOVER_ITEMS_ID = "105164";
    public static final String WARSEASON_FEAT_PARAM = "10|1";
    public static final String WARSEASON_MONEY_ITEMS_ID = "105180";
    public static final String WARSEASON_FIGHT_REWARD = "1000";
    public static final String WARSEASON_FIGHT_ADDITION_REWARD = "1";
    public static final String WARSEASON_GROUND_OCCUPY_LIMIT = "1|20";
    public static final String WARSEASON_TRIAL_FREE_TIMES = "4";
    public static final String WARSEASON_TRIAL_PAID_ITEM_ID = "105165";
    public static final String WARSEASON_TICKET_RECOVER_RATE = "10";
    public static final String WARSEASON_MOVE_CAPITAL_LIMIT = "5";
    public static final String WARSEASON_MOVE_CAPITAL_TIME = "3600";
    public static final String WARSEASON_PLAYER_MOVE_TIME = "3600";
    public static final String WARSEASON_SIGN_CITY_LIMIT = "5";
    public static final String WARSEASON_SIGN_CHARACTER_LIMIT = "48";
    public static final String WARSEASON_TRUST_APPLY_CD = "600";
    public static final String WARSEASON_TRUST_LIMIT = "1|4";
    public static final String WARSEASON_GROUP_WORSHIP_REWARD = "100000=50";
    public static final String WARSEASON_WORSHIP_REWARD = "100000=50";
    public static final String WARSEASON_TASK_TIME = "1|3;2|4;3|3;4|4;5|3;6|4;7|3;8|4;9|3;10|4;11|5;12|5";
    public static final String WARSEASON_BOX_DURATION = "2";
    public static final String WARSEASON_TICKET_INITIAL_LIMIT = "3";
    public static final String WARSEASON_BOX_SUPERIOR_POOL = "150324=1";
    public static final String WARSEASON_BOX_COMMON_POOL = "150326=1";
    public static final String WARSEASON_BOX_SUPERIOR_ITEM_ID = "105175";
    public static final String WARSEASON_BOX_COMMON_ITEM_ID = "105176";
    public static final String WARSEASON_MONSTER_RECPVER_CD = "60";
    public static final String WARSEASON_TICKET_ITEM_ID = "105177";
    public static final String WARSEASON_QUIZ_CONDITION = "11|3";
    public static final String WARSEASON_ITEM_RECYCLE_LIST = "150324;150325;150326;105180;105175;105176;105165-100000=15;105195-100000=1";
    public static final String WARSEASON_CHECK_GROUP_EXP = "100";
    public static final String WARSEASON_MONSTER_TRIAL_PARAM = "1|61001;2|61002;3|61003;4|61004;5|61005;6|61006;7|61007;8|61008;9|61009;10|61010";
    public static final String WARSEASON_MONSTER_TRIAL_REWARD = "1-105178=60000;2-105178=90000;3-105178=120000;4-105178=168000;5-105178=228000;6-105178=300000;7-105178=372000;8-105178=444000;9-105178=516000;10-105178=588000;";
    public static final String WARSEASON_PRESIDENT_TITLE_ID = "145068";
    public static final String WARSEASON_GIVE_TITLE_PARAM = "145052|145053|145054|145055;145057|145058|145059|145060";
    public static final String WARSEASON_ICON_PARAM = "0|50,2000;51|100,1700;101|200,1500;201|400,1200;401|1000,1000";
    public static final String WARSEASON_CITY_HP_SETTLE_RATE = "1";
    public static final String WARSEASON_CITY_SIEGE_SHOW_PARAM = "5000|2000";
    public static final String WARSEASON_CITY_FIGHT_SHOW_PARAM = "3000|750";
    public static final String WARSEASON_MARK_CITY_LIMIT = "10";
    public static final String WARSEASON_MOVE_TIME = "1000";
    public static final String WARSEASON_PRESIDENT_PRIVILEGE = "1|2|3|4|5";
    public static final String WARSEASON_VICE_PRESIDENT_PRIVILEGE = "1|2|3|4|5";
    public static final String WARSEASON_COMMANDER_PRIVILEGE = "1|3|5|6|7|8";
    public static final String WARSEASON_FIGHT_ATTACK_TIMES = "1000|3;2000|1";
    public static final String WARSEASON_SUPERCITY_TRANSLATE_TIME = "10";
    public static final String WARSEASON_SEASON_GIFT_TIME = "1110|1320";
    public static final String WARSEASON_GROUND_MOVE_TIME = "5000";
    public static final String WARSEASON_TRIAL_CHALLENGE_TIMES = "10";
    public static final String WARSEASON_AWARD_ITEM = "149502=1";
    public static final String WARSEASON_GROUND_ACCUMULATE_TIME = "43200";
    public static final String WARSEASON_CITY_SCORE = "5|10;6|15;7|20;8|50;9|80;10|200";
    public static final String WARSEASON_CITY_ICON = "1|1001;2|1002;3|1003;4|1004;5|1005;6|1006;7|1007;8|1008;9|1009;10|1010";
    public static final String WARSEASON_CHALLENGE_GROUND_CD = "60";
    public static final String WARSEASON_EXP_ITEM_ID = "105178";
    public static final String WARSEASON_MOVE_CAPITAL_CD = "14";
    public static final String WARSEASON_MANAGER_LIMIT = "5";
    public static final String WARSEASON_TRIAL_PAID_LIMIT = "20";
    public static final String WARSEASON_STRAGETY_INITIAL_SP = "5";
    public static final String WARSEASON_STRAGETY_RECOVER_CD = "43200";
    public static final String WARSEASON_STRAGETY_MAX_SP = "10";
    public static final String WARSEASON_SHURA_MONSTER_ID = "63001";
    public static final String WARSEASON_SHURA_MONSTER_PARAM = "200|8";
    public static final String WARSEASON_ORDER_CONDUCT_TIME = "1350";
    public static final String WARSEASON_ORDER_CONDUCT_DURATION = "870";
    public static final String WARSEASON_S2_STRETAGE = "1003|1005|1006|1007";
    public static final String WARSEASON_S2_STRETAGE_USE_TIME = "720|1080";
    public static final String WARSEASON_SPECIAL_RECYCLE = "100000=25";
    public static final String WARSEASON_S2_MONSTER_TRIAL_PARAM = "1|65001;2|65002;3|65003;4|65004;5|65005;6|65006;7|65007;8|65008;9|65009;10|65010;11|65011;12|65012;13|65013";
    public static final String WARSEASON_S2_MONSTER_TRIAL_REWARD = "1-105178=60000;2-105178=90000;3-105178=120000;4-105178=168000;5-105178=228000;6-105178=300000;7-105178=372000;8-105178=444000;9-105178=516000;10-105178=588000;11-105178=678000;12-105178=780000;13-105178=900000";
    public static final String WARSEASON_S2_EXP_ITEM_ID = "105264";
    public static final String WARSEASON_AUTO_ENTRUST_CONDITION = "5";
    public static final String WARSEASON_S2_PROPERTY_PARAM = "1|100;50|150;100|200;150|300;200|400;275|500;350|600;425|700;500|750;600|800;700|850;800|900;900|950;1025|1000";
    public static final String WARSEASON_S2_CITY_SCORE = "8|5;9|10;10|30;11|50;12|80;13|200";
    public static final String WARSEASON_S2_CITY_ICON = "1|1001;2|1001;3|1001;4|1002;5|1002;6|1003;7|1003;8|1003;9|1003;10|1004;11|1004;12|1004;13|1005";
    public static final String TIKTOK_PLAY_DREAM_PARAM = "100|200|60|5";
    public static final String TIKTOK_PLAY_TALENT_PARAM = "20|20|210|2";
    public static final String TIKTOK_PLAY_PVP_PARAM = "5|3|150|3";
    public static final String TIKTOK_PLAY_DESTINY_PARAM = "1|1|270|1";
    public static final String UNIONFLYUP_VISIT_REWARDS = "100000=50";
    public static final String UNIONFLYUP_REDBAG-450001 = "100000=388;1;9999|100000=288;2;9999|100000=188;3;9999";
    public static final String UNIONFLYUP_REDBAG-450002 = "100004=200;1;9999|100101=400;5;9999|100025=20;10;9999|100101=200;10;9999|100004=80;10;9999|100025=10;20;9999|100101=100;20;9999|100004=40;20;9999|100025=5;30;9999";
    public static final String UNIONFLYUP_REDBAG-450003 = "100152=10;1;9999|100101=800;3;9999|100004=200;5;9999|100025=40;10;9999|100152=4;10;9999|100101=400;10;9999|100004=100;15;9999|100025=25;15;9999|100101=250;15;9999|100152=1;15;9999";
    public static final String UNIONFLYUP_REDBAG-450004 = "100091=30;1;9999|100152=15;5;9999|100101=1000;10;9999|100010=200;10;9999|100044=400;10;9999|100023=10;10;9999|100091=10;10;9999|100152=5;15;9999|100101=200;30;9999|100010=50;30;9999|100044=100;30;9999|100023=3;30;9999";
    public static final String UNIONFLYUP_JOIN_LIMIT = "236";
    public static final String UNIONFLYUP_CUP_MATERIAL = "1=1|2=21|3=63|4=147|5=273";
    public static final String UNIONFLYUP_POINT_NEED = "16000";
    public static final String UNIONFLYUP_POWER_NEED = "20000000000";
    public static final String UNIONFLYUP_REDBAG_TIME = "48";
    public static final String UNIONFLYUP_CELEBRATION_TIME = "19|7200";
    public static final String HONORHALL_WORSHIP_REWARD = "100000=50|1";
    public static final String CAREERBOSS_CHALLENGE_TIME = "1";
    public static final String CAREERBOSS_CHALLENGE_TRY = "30";
    public static final String CAREERBOSS_CHALLENGE_ROUND = "20";
    public static final String CAREER_CONVERT_NUMLIMIT = "1";
    public static final String CAREER_CONVERT_COST = "100000=500";
    public static final String CAREER_STAGE_VISIBLE = "1=16|16=36|36=56|56=76|76=96|96=116|116=136|136=156|156=177";
    public static final String CAREER_VFX_ID = "1=147003=1130|2=147004=2900|3=147005=4670";
    public static final String ELEMENTALBONDS_TITLE_LIMIT = "300";
    public static final String ELEMENTALBONDS_RANK_LIMIT = "3600";
    public static final String ELEMENTALBONDS_RANK_NUM = "100";
    public static final String ELEMENTALBONDS_DAILY_QUEST_NUM = "3";
    public static final String ELEMENTALBONDS_DAILY_QUEST_LIMIT = "3";
    public static final String ELEMENTALBONDS_WEEKLY_QUEST_NUM = "1";
    public static final String ELEMENTALBONDS_WEEKLY_QUEST_LIMIT = "3";
    public static final String ELEMENTALBONDS_OFFLINE_RECONNECT = "65|30|20|-1";
    public static final String ELEMENTALBONDS_STREAK_WIN = "3|2000";
    public static final String ELEMENTALBONDS_CREATE_BLOCK_PARAM = "50;300;8;600";
    public static final String ELEMENTALBONDS_ITEM_ID_MONEY = "105187";
    public static final String ELEMENTALBONDS_ITEM_ID_POINT = "105189";
    public static final String ELEMENTALBONDS_ITEM_ID_SHIELD = "105191";
    public static final String ELEMENTALBONDS_ITEM_ID_TIMELIMIT = "105199|105200|105201|105202|105203|105265";
    public static final String ELEMENTALBONDS_AI_MOVE_TIME = "1000|2000";
    public static final String ELEMENTALBONDS_AI_ACTION_PARAM = "0";
    public static final String ELEMENTALBONDS_AI_APPEARANCE = "144012|144016|144030|144031|144032|144033|144034|144036|144038|144040|144053|144055|144070|144080|144089|144090|144091|144096|144027|141001|142001|143001|140701";
    public static final String ELEMENTALBONDS_FIGHT_BLOCK_SCORE = "1";
    public static final String ELEMENTALBONDS_FIGHT_SCENE_RANDOM_NUM = "2";
    public static final String ELEMENTALBONDS_FIGHT_SCENE_WEIGHT_BEGIN = "100";
    public static final String ELEMENTALBONDS_FIGHT_SCENE_WEIGHT_ADD = "100";
    public static final String ELEMENTALBONDS_MATCH_NPC_SCORE = "299";
    public static final String ELEMENTALBONDS_STAR_ELIMINATE_NUM = "7";
    public static final String ELEMENTALBONDS_NPC_NAME_NUM = "25";
    public static final String ELEMENTALBONDS_EVALUE_SCORE = "6|9|15";
    public static final String ELEMENTALBONDS_GUIDE_MATCH_NPC_TIME = "20";
    public static final String ELEMENTALBONDS_AI_HEAD = "144012|144016|144030|144031|144032|144033|144034|144036|144038|144040|144053|144055|144070|144080|144089|144090|144091|144096|144027|141001|142001|143001|140701";
    public static final String ELEMENTALBONDS_AI_HARD = "1000|0|0|0";
    public static final String ELEMENTALBONDS_MATCH_TIP_TIME = "60";
    public static final String ELEMENTALBONDS_FIGHT_SCENE_OPEN_LIMIT = "300";
    public static final String ELEMENTALBONDS_MATCH_NEED_PLAYER_NUM_2 = "1;2|15;10";
    public static final String ELEMENTALBONDS_MATCH_TIME_LIMIT = "0;600|1320;1440";
    public static final String ELEMENTALBONDS_MATCH_TIME_LIMIT_SHOW = "1320|720";
    public static final String ELEMENTALBONDS_MATCH_EXTEND_TIME = "0|0|3|6|6|12";
    public static final String ELEMENTALBONDS_DISCONNECT_FAIL_FIRST_LIMIT = "10";
    public static final String ELEMENTALBONDS_MATCH_OUT_TIME = "120";
    public static final String ELEMENTALBONDS_DAY_SURRENDER_TIMES = "99999";
    public static final String ELEMENTALBONDS_AI_MATCH_CARD_2 = "1;1;1000;1;0|2;11;1000;1;0|12;16;500;1;0|17;31;50;100;50|32;9999;1;1;100";
    public static final String ELEMENTALBONDS_MATCH_OUT_SCORE_LIMIT = "92100";
    public static final String ELEMENTALBONDS_AI_RED_CARD_LIMIT = "5000";
    public static final String ELEMENTALBONDS_WAIGUA_CHECK_CONDITION_1 = "3";
    public static final String ELEMENTALBONDS_WAIGUA_CHECK_CONDITION_2 = "3";
    public static final String ELEMENTALBONDS_WAIGUA_CHECK_CONDITION_3 = "10";
    public static final String WAIGUA_CHECK_TRY_NUM = "3";
    public static final String WAIGUA_CHECK_PUNISH_TIME = "1;1800|2;3600|3;43200";
    public static final String WAIGUA_CHECK_REWARD = "101821=5";
    public static final String WAIGUA_CHECK_WAIT_TIME = "30";
    public static final String ELEMENTALBONDS_MATCH_RULE_CARD = "1=1|3;2=1|3;3=1|4;4=3|5;5=4|5";
    public static final String ELEMENTALBONDS_MATCH_RULE_STREAK = "5";
    public static final String ELEMENTALBONDS_MATCH_RULE_SCORE_LINE = "6000000";
    public static final String ELEMENTALBONDS_MATCH_RULE_HIGH_PARAM = "60|1000";
    public static final String ELEMENTALBONDS_MATCH_RULE_HIGH_AROUND = "40|80";
    public static final String ELEMENTALBONDS_MATCH_RULE_HIGH_AVERAGE_NUM = "20";
    public static final String ELEMENTALBONDS_MATCH_RULE_HIGH_WIN_NUM = "5";
    public static final String ELEMENTALBONDS_CREATE_OLD_KEEP_ROUND = "3";
    public static final String ELEMENTALBONDS_CREATE_NEW_RULE_PARAM = "60|10|100|100";
    public static final String ELEMENTALBONDS_CREATE_NEW_RULE_COLOR_NUM = "6|10";
    public static final String ELEMENTALBONDS_BUY_BAG_COLD_TIME = "86400";
    public static final String ELEMENTALBONDS_BAG_CONTINUE_TIME = "36000";
    public static final String ELEMENTALBONDS_TIME_LIMIT_TASK = "5|2;5|2;5|2;5|2;5|2;5|2";
    public static final String ELEMENTALBONDS_EXTRA_BONUS_DAY = "3";
    public static final String ELEMENTALBONDS_SPECIAL_ITEM_ID = "1001|1002";
    public static final String ELEMENTAL_BONDS_PERIOD_TIME = "1:600|2:1560";
    public static final String ELEMENTAL_BONDS_POINTS_PARAM-1 = "600|3|12";
    public static final String ELEMENTAL_BONDS_POINTS_PARAM-2 = "3|5|10|20|20";
    public static final String ELEMENTAL_BONDS_POINTS_PARAM-4 = "20";
    public static final String ELEMENTAL_BONDS_COIN_PARAM-1 = "10|0";
    public static final String ELEMENTAL_BONDS_COIN_PARAM-2 = "0|5|10|20|40";
    public static final String ELEMENTAL_BONDS_SCORE_PARAM = "1=10|2=50|3=100";
    public static final String ELEMENTAL_BONDS_ORIGIN_CARD = "1=2";
    public static final String ELEMENTAL_BONDS_FINAL_RANK_PARAM = "10|5000";
    public static final String ELEMENTAL_BONDS_POINTS_EXTRA_PARAM = "3600=10=2000|6600=20=3000|11100=30=3000";
    public static final String ELEMENTAL_BONDS_GUIDE_LIMIT = "96";
    public static final String ELEMENTAL_BONDS_CARD_DROP = "100";
    public static final String ELEMENTAL_BONDS_TIME_PROTECT = "900";
    public static final String ELEMENTAL_BONDS_DISSOLVE = "1800";
    public static final String ELEMENTAL_BONDS_TIME_POPUP = "1800";
    public static final String ELEMENTAL_BONDS_POSTER_ADVANCE = "7";
    public static final String ELEMENTAL_BONDS_MEMORY_LIMIT = "3";
    public static final String ELEMENTAL_BONDS_WEEK_TASK_REWARD = "2,100004=120|100025=30|100010=60|100044=120|100023=3";
    public static final String ELEMENTAL_BONDS_DAILY_SCORE_LIMIT = "1=10|36=50|42=350";
    public static final String ELEMENTALBONDS_SKILL_ID_USE_LIMIT = "1";
    public static final String ELEMENTALBONDS_PRIVILEGE_LIMIT = "300";
    public static final String ELEMENTALBONDS_ROBOT_LIMIT = "21600|7";
    public static final String ELEMENTALBONDS_VERIFY_REWARD = "105187=50";
    public static final String ELEMENTALBONDS_VERIFY_FAIL_TIMES = "3";
    public static final String ELEMENTALBONDS_VERIFY_FAIL_BANNED = "30|60|720";
    public static final String ELEMENTALBONDS_VERIFY_TRIGGER = "1:3|2:3|3:10";
    public static final String ELEMENTALBONDS_VERIFY_PIC = "100101|100201|100301|100401|100501|100601|100701|100801";
    public static final String ELEMENTALBONDS_CANT_BUY = "7|21";
    public static final String PC_LOG_IN_REWARD = "100000=100|100004=50|100016=5";
    public static final String MOUNTAIN_HANDBOOK_NUM = "4=4|5=3";
    public static final String DESTINYCARD_CARD_UPGRADE_COST = "50|50|50|50|100|100|100|100|100|100|100|100|100|100";
    public static final String DESTINYCARD_CARD_COMPOUND_PARAM = "50";
    public static final String DESTINYCARD_COMBINE_UPGRADE_COST = "1|1;2|2;3|3;5|5;7|7;10|10;15|15";
    public static final String DESTINYCARD_COMBINE_TALENT_EFFECT = "700|900|1100|1300|1500|1700|1900";
    public static final String SPIRITBODY_DRAW_ITEM_ID = "100184";
    public static final String SPIRITBODY_REFINE_ITEM_ID = "100186";
    public static final String SPIRITBODY_COMPOUND_PARAM = "50";
    public static final String SPIRITBODY_STORE_LIMIT = "200";
    public static final String SPIRITBODY_DRAW_WEIGHT = "450|280|70;120|60|10";
    public static final String SPIRITBODY_DRAW_ENSURE_QUALITY = "3-50";
    public static final String SPIRITBODY_DRAW_ENSURE_NUM = "200";
    public static final String SPIRITBODY_REFINE_COST = "100186=10";
    public static final String SPIRITBODY_AUTO_REFINE_LIMIT = "8";
    public static final String SPIRITBODY_FREE_DRAW_TIMES = "2";
    public static final String SPIRITBODY_DRAW_LIMIT = "9999";
    public static final String SPIRITBODY_STAR_RESET_COST = "100000=500";
    public static final String SPIRITBODY_AD_DRAW_TIMES = "0";
    public static final String SPIRITBODY_SKILL_ROUND = "1|3;2|3;3|3;4|3;5|2;6|2";
    public static final String SPIRITBODY_NEIDAN_SPECIAL_DRAW = "1004";
    public static final String SPIRITBODY_BREAK_COST = "100199=100";
    public static final String SPIRITBODY_RESET_LIMIT = "2";
    public static final String SPIRITBODY_REFINE_PROTECT = "500";
    public static final String SPIRITBODY_REFINE_PROPERTY_PARAM = "300";
    public static final String SPIRITBODY_STAR_PROPERTY_PARAM = "300";
    public static final String SPIRITBODY_INHERIT_COST = "4|500;5|2000;6|10000";
    public static final String SPIRITBODY_INHERIT_QUALITY_LIMIT = "4";
    public static final String SPIRITBODY_INHERIT_DAILY_TIMES = "2";
    public static final String SPIRITBODY_GOD_COMMON_ITEM_ID = "100221";
    public static final String SWITCH_BUBBLE_COLD_TIME = "2";
    public static final String SpChamp_TICKIET_ITEM_ID = "105208";
    public static final String SYSTEM_SKIP_DAY = "120";
    public static final String REALMSTRIBULATION_BLOCK = "2550";
    public static final String HUANGSHAN_INITIAL_ATTACK = "10";
    public static final String HUANGSHAN_WEAPON_NUM_LIMIT = "3";
    public static final String HUANGSHAN_BOSS_GRID = "5";
    public static final String HUANGSHAN_DAILY_REWARD = "100000=10|100004=10";
    public static final String FRAME_CHECK_PARAM = "25";
    public static final String KUAISHOU_STORE_DESKTOP_DAILY_REWARD = "1,100000=50|100010=20;2,100016=5|100009=200;3,100000=50|100010=20;4,100016=5|100009=200;5,100000=50|100010=20;6,100016=5|100009=200;7,100000=50|100010=20";
    public static final String LINKAGE_INVITATION = "8|150376;9|150391;10|150397";
    public static final String LINKAGE_FREE_CHARA = "8|144099;9|144120;10|144130";
    public static final String LINKAGE_MAIL = "8|NewPlayer_DOUPO_Mail;9|NewPlayer_SHENMU_Mail;10|NewPlayer_Calabash_Mail";
    public static final String LINKAGE_INVITATION_ACTIVITY = "8|190=10256,194=10267,133=10268,134=10269,195=10272,110=10117";
    public static final String BEGINNER_GUIDE_ID_LIST_CHARA = "10000|10001|10004|10007|23000|23002|23003|11000|22000|22001|25000|25002|12000|12001|13000|24000|21000|21100|21200|17000|17001|14000|14100|15000|16100|16000|18000";
    public static final String BEGINNER_GUIDE_ID_LIST_TALK = "10000|10001|10004|10007|23000|23002|23003|11000|22000|22001|25000|25002|12000|12001|13000|24000|21000|21100|21200|17000|17001|14000|14100|15000|16100|16000|18000|18001|18100|18200";
    public static final String ASSISTANT_FREE_PARAM = "1|72";
    public static final String ASSISTANT_INIT_SKIN = "670001";
    public static final String STATE_SHOW_CONDITION = "2551";
    public static final String CLOUDSOUL_LEVEL_COST_NUM = "20|20|20|20|20|30|30|30|30|30|30|30|30|30|30|40|40|40|40|40|50|50|50|50|50|60|60|60|60|60|70|70|70|70|70|80|80|80|80|80|90|90|90|90|90|100|100|100|100|100|110|110|110|110|110|120|120|120|120";
    public static final String CLOUDSOUL_SOUL_ID = "690001";
    public static final String CLOUDSOUL_INIT_COST = "100000=1000";
    public static final String CLOUDSOUL_ACTIVE_SKILL_UPGRADE = "5|10|15|20|25|30|35|40|45|50|55|60";
    public static final String CLOUDSOUL_SET_LIMIT = "1";
    public static final String DESTINYFIGHT_ROBOT_DAILY = "2";
    public static final String DESTINYFIGHT_SKIP_USE = "3";
    public static final String DESTINYFIGHT_OPEN_DURATION = "8|24";
    public static final String DESTINYFIGHT_QUEST_GET = "1|1|18|28";
    public static final String DESTINYFIGHT_QUEST_NUM_LIMIT = "3";
    public static final String DESTINYFIGHT_QUEST_WEEKLY_ENERGY = "2";
    public static final String DESTINYFIGHT_QUEST_ITEM = "100223=50=50|100224=150=50|100225=400=50";
    public static final String DESTINYFIGHT_REFRESH_AUTO = "3600";
    public static final String DESTINYFIGHT_REFRESH_COLDTIME = "3";
    public static final String DESTINYFIGHT_REFRESH_COST = "100000=20;100000=40;100000=60;100000=80;100000=100";
    public static final String DESTINYFIGHT_REFRESH_FREE_TIMES = "2";
    public static final String DESTINYFIGHT_ROB_LOSS = "100|100|100";
    public static final String DESTINYFIGHT_ROB_NUM_LIMIT = "3";
    public static final String DESTINYFIGHT_ROB_WEEKLY_ENERGY = "2";
    public static final String DESTINYFIGHT_SHOW_CAR_NUM = "8";
    public static final String DESTINYFIGHT_BETTLELOG_LIMIT = "50";
    public static final String DESTINYFIGHT_EXP_ITEM = "100226";
    public static final String DESTINYFIGHT_RANK_PEAK = "-50|50";
    public static final String DESTINYFIGHT_FIND_COST = "100000=50";
    public static final String DESTINYFIGHT_FIND_LIMIT = "10";
    public static final String DESTINYFIGHT_ROB_DAILY_LIMIT = "1=2|11=3|21=4|31=5";
    public static final String DESTINYFIGHT_QUEST_DAILY_LIMIT = "1=1|37=2|81=3;1=1|46=2|81=3;18=1|55=2|81=3;28=1|64=2|81=3";
    public static final String DESTINYFIGHT_QUEST_BUFF = "100=50|120=100|140=150|160=200|180=250";
    public static final String DESTINYFIGHT_WORSHIP_REWARD = "100000=50";
    public static final String DESTINYFIGHT_CHARM_EQUAL = "1000|1000|1000|100";
    public static final String DESTINYFIGHT_SPECIAL_PLOT = "4|7|10|13|16|19|22|25|28|31|34|37|40|43|46|49|52|55|58|61|64|67|70|73|76|79|82|85|88|91|94|97|100|103|106|109|112|115|118|121|124|127|130|133|136|139|142|145|148|151|154|157|160|163|166|169|172|175|178|181|184|187|190|193|196|199|202|205|208|211|214|217|220|223|226|229|232|235|238|241|244|247|250|253|256|259|262|265|268|271|274|277|280|283|286|289|292|295|298|301|304|307|310|313|316|319|322|325|328|331|334|337|340|343|346|349|352|355|358|361|364|367|370|373|376|379|382|385|388|391|394|397|400|403|406|409|412|415|418|421|424|427|430|433|436|439|442|445|448|451|454|457|460|463|466|469|472|475|478|481|484|487|490|493|496|499|502|505|508|511|514|517|520|523|526|529|532|535|538|541|544|547|550|553|556|559|562|565|568|571|574|577|580|583|586|589|592|595|598|601|604|607|610";
    public static final String DESTINYFIGHT_AI_APPEARANCE = "144012|144016|144030|144031|144032|144033|144034|144036|144038|144040|144053|144055|144070|144080|144089|144090|144091|144096|144027|141001|142001|143001|140701";
    public static final String DESTINYFIGHT_AI_HEAD = "144012|144016|144030|144031|144032|144033|144034|144036|144038|144040|144053|144055|144070|144080|144089|144090|144091|144096|144027|141001|142001|143001|140701";
    public static final String DESTINYFIGHT_AI_TIME = "7200|12600";
    public static final String DESTINYFIGHT_AI_FAVOR = "-20|-10=100;-10|0=10";
    public static final String DESTINYFIGHT_AI_DESTINY_ID = "10001|10002|10003|10004|10005|10006";
    public static final String DESTINYFIGHT_SLOT_ATTRIBUTE = "26|28|5|6|7|8|9|10;25|27|31|5|6|7|8|9|10;22|26|28|5|6|7|8|9|10;21|25|27|31|5|6|7|8|9|10;25|26;27|28";
    public static final String DESTINYFIGHT_SLOT_UNLOCK = "4|10|16|22|28|34|40|46|52|58|64|70|76|82|88|94|100|106|112|118|124|130|136|142|148|154|160|166|172|178|184|190|196|202|208|214|220|226|232|238|244|250|256|262|268|274|280|286|292|298|304|310|316|322|328|334|340|346|352|358|364|370|376|382|388|394|400|406|412|418|424|430|436|442|448|454|460|466|472|478|484|490|496|502|508|514|520|526|532|538|544|550|556|562|568|574|580|586|592|598|604|610";
    public static final String DESTINYFIGHT_CHARA_CHANGE = "4|16|34|70";
    public static final String DESTINYFIGHT_SLOT_SHOW = "0=12|12=20|20=28|28=36|36=44|44=52|52=60|60=68|68=76|76=84|84=92|92=102";
    public static final String DESTINYFIGHT_SPECIAL_PLOT_SHOW = "1=70|70=118|118=166|166=214|214=262|262=310|310=358|358=406|406=454|454=502|502=550|550=610";
    public static final String DIVINECRUCIBLE_SETTLE_TIME = "22";
    public static final String DIVINECRUCIBLE_SETTLE_DURATION = "300";
    public static final String DIVINECRUCIBLE_EQUIPMENT_PARAM = "1000";
    public static final String DIVINECRUCIBLE_LEVEL_PARAM = "1,7|8;2,9|10;3,11|12";
    public static final String DIVINECRUCIBLE_SUPRESS_LEVEL = "1|335;2|595;3|935";
    public static final String DIVINECRUCIBLE_OPEN_CONDITION = "100";
    public static final String DIVINECRUCIBLE_GROUP_TIMIES = "5";
    public static final String DIVINECRUCIBLE_DAILY_FAIL_TIMES = "10";
    public static final String DIVINECRUCIBLE_FIGHT_ROUND = "20";
    public static final String DIVINECRUCIBLE_NAME_LIMIT = "12";
    public static final String DIVINECRUCIBLE_PLAYER_APPLY_LIMIT = "3";
    public static final String DIVINECRUCIBLE_TEAM_APPLY_LIMIT = "100";
    public static final String DIVINECRUCIBLE_RAMDOM_MATCH_LIMIT = "1";
    public static final String DIVINECRUCIBLE_LEAVE_TEAM_CD = "5";
    public static final String DIVINECRUCIBLE_HISTORY_MESSAGE_LIMIT = "50";
    public static final String DIVINECRUCIBLE_RANK_SHOW_LIMIT = "100";
    public static final String DIVINECRUCIBLE_BATTLE_SKIP_CD = "10";
    public static final String DIVINECRUCIBLE_AUTO_PREPARE = "10";
    public static final String DIVINECRUCIBLE_BUFF_ID = "700001,3|700002,3|700003,2|700004,2|700005,1|700006,3|700007,2|700008,2|700009,1|700010,1|700011,3|700012,3|700013,3|700014,1|700015,1|700016,2|700017,2|700018,1|700019,2|700020,1|700021,1|700022,3|700023,1|700024,1|700025,2|700026,1|700027,2|700028,1|700029,2|700030,1|700031,1|700032,2|700033,1|700034,1|700035,2|700036,1";
    public static final String DIVINECRUCIBLE_BUFF_CD = "15";
    public static final String DIVINECRUCIBLE_INVITE_PARAM = "100";
    public static final String DIVINECRUCIBLE_INVITE_MAX_NUM = "10";
    public static final String DIVINECRUCIBLE_INVITE_TIME = "1";
    public static final String DIVINECRUCIBLE_INVITE_PEOPLE_LIMIT = "4";
    public static final String DIVINECRUCIBLE_INVITE_ROBOT_LIMIT = "2";
    public static final String DIVINECRUCIBLE_WORSHIP_REWARD = "100000=50";
    public static final String DIVINECRUCIBLE_SENCE_PARAM = "20|2;40|5;60|1;80|4;110|3";
    public static final String DIVINECRUCIBLE_BOSS_SHOW_PARAM = "10|430101;20|430201;30|430301;40|430401;50|430501;60|430601;70|430701;80|430801;90|430901;100|431001;110|431102";
    public static final String DIVINECRUCIBLE_GROUP_TIME = "600";
    public static final String DIVINECRUCIBLE_OPEN_PARAM = "5";
    public static final String DIVINEINSIGHT_PRODUCE_ITEM = "100229";
    public static final String DIVINEINSIGHT_PRODUCE_LIMIT = "720";
    public static final String DIVINEINSIGHT_ACCELERATOR = "100233=300";
    public static final String DIVINEINSIGHT_INSPIRE_ITEM = "100234";
    public static final String DIVINEINSIGHT_BUILD_HELP_EFFECT = "300";
    public static final String DIVINEINSIGHT_BUILD_HELP_LIMIT = "20|999";
    public static final String DIVINEINSIGHT_DANTIAN_UNLOCK_PARAM = "0|1|2|3|4";
    public static final String DIVINEINSIGHT_EXP_ITEM_NUM = "2000";
    public static final String XIYOU_STRENGTH_ITEM = "105280=1";
    public static final String XIYOU_EQUIPMENT_UNLOCK_PARAM = "1|1|1|1|1|1|1|1|1|1|1|1";
    public static final String XIYOU_EQUIPMENT_SPECIAL_DROP_LIST = "1010101|1010301|1020102|1020602|1020502|1021001|1040602|1031002|1030801|1020701|1010502|1040103|1041002|1050502|1040901";
    public static final String XIYOU_EQUIPMENT_QUALITY_PROTECT_PARAM = "20|1";
    public static final String XIYOU_MAGIC_SYSTEM_UNLOCK = "4|13";
    public static final String XIYOU_MAGIC_PASSIVE_UNLOCK = "5|10|15";
    public static final String XIYOU_RUNESTONE_SYSTEM_UNLOCK = "8|46";
    public static final String XIYOU_RUNESTONE_LEVELUP_COST = "20|20|20|20|20|20|20|20|20|30|30|30|30|30|30|30|30|30|30|40|40|40|40|40|40|40|40|40|40|50";
    public static final String XIYOU_RUNESTONE_LEVELUP_ADD = "1";
    public static final String XIYOU_RUNESTONE_UPGRADE_ADD = "5";
    public static final String XIYOU_RUNESTONE_BASE_ADD = "10|20|30|50";
    public static final String XIYOU_RUNESTONE_UP_QUALITY = "4";
    public static final String XIYOU_RUNESTONE_FREE_DRAW = "2";
    public static final String XIYOU_RUNESTONE_DRAW_COST = "105305=1";
    public static final String XIYOU_RUNESTONE_DRAW_GUARANTEE = "100|200";
    public static final String XIYOU_RUNESTONE_DRAW_ENSURE_QUALITY = "3|4;4";
    public static final String XIYOU_RUNESTONE_SHOW_LEVEL = "50|100";
    public static final String XIYOU_ENERGY_SYSTEM_UNLOCK = "12|67";
    public static final String XIYOU_ENERGY_QUALITY_RATE = "1000|1200|1400|1600|1800|2000|1600|1800|2000";
    public static final String XIYOU_ENERGY_STORE_LIMIT = "100";
    public static final String XIYOU_ENERGY_COST = "105286";
    public static final String XIYOU_STAGE_TIME_LIMIT = "45";
    public static final String XIYOU_WILDBOSS_SYSTEM_UNLOCK = "6|35";
    public static final String XIYOU_WILDBOSS_REPEAT_COST_PARAM = "0|1000|2000|3000|4000|5000|5000|5000|5000|5000|5000";
    public static final String XIYOU_WILDBOSS_REPEAT_LIMIT = "6";
    public static final String XIYOU_WILDBOSS_REPEAT_AD = "2";
    public static final String XIYOU_ATTRIBUTE_HIT_RATE_RANGE = "500|1000";
    public static final String XIYOU_ATTRIBUTE_CRITICAL_CHANCE_RANGE = "0|1000";
    public static final String XIYOU_ATTRIBUTE_CRITICAL_DAMAGE_RANGE = "-300|9500";
    public static final String XIYOU_ATTRIBUTE_BLOCK_RATE_RANGE = "0|1000";
    public static final String XIYOU_ATTRIBUTE_BLOCK_VALUE_RANGE = "0|10000";
    public static final String XIYOU_ATTRIBUTE_ADDDAMAGE_RATE_RANGE = "0|1000";
    public static final String XIYOU_ATTRIBUTE_ADDDAMAGE_DAMAGE_RANGE = "0|10000";
    public static final String XIYOU_ATTRIBUTE_THORNS_RATE_RANGE = "0|1000";
    public static final String XIYOU_ATTRIBUTE_THORNS_DAMAGE_RANGE = "0|10000";
    public static final String XIYOU_ATTRIBUTE_HIT_RECOVERY_RANGE = "0|1000";
    public static final String XIYOU_ATTRIBUTE_SECOND_RECOVERY_RANGE = "0|200";
    public static final String XIYOU_ATTRIBUTE_COLDTIME_REDUCTION_RANGE = "0|800";
    public static final String XIYOU_ATTRIBUTE_POWER_PARS = "4000000|1000|500";
    public static final String XIYOU_ATTRIBUTE_BASE_VALUE_LIST = "6|1|30";
    public static final String XIYOU_ATTRIBUTE_SPECIAL_VALUE_LIST = "800|800|800|800|800|800|800|800|800|800|800|800|800|800|800|800|800|800|800|800|800|800|800|800|800|800|800|800|800|800|800";
    public static final String XIYOU_WISH_ACCEL = "105282=300";
    public static final String XIYOU_WISH_SPEEDUP_AD_TIME = "1800";
    public static final String XIYOU_WISH_SPEEDUP_AD_LIMIT = "8";
    public static final String XIYOU_WISH_SPEEDUP_AD_COLD_TIME = "7200";
    public static final String XIYOU_WISH_SPEEDUP_AD_SKIP = "100000=50";
    public static final String XIYOU_ONE_TIME_COST = "1|3|5";
    public static final String XIYOU_BATTLE_SPEED = "1|15|-1";
    public static final String XIYOU_DROP_PROB = "200";
    public static final String XIYOU_EXP_GET = "10";
    public static final String XIYOU_MONSTER_SCALE = "1200|800";
    public static final String XIYOU_SECRET_RATE = "1000|1100|1200|1300|1500";
    public static final String XIYOU_SECRET_PROGRESS = "180|210|240|270|300";
    public static final String XIYOU_SECRET_REWARD = "105311";
    public static final String XIYOU_ROLE_ID = "1";
    public static final String XIYOU_SECRET_RATE_LIMIT = "1=1|26=2|31=3|36=4|40=5";
    public static final String XIYOU_SECRET_BLESS = "26=105288|36=105289,105288|46=105290,105289,105288";
    public static final String XIYOU_SECRET_BLESS_QUALITY = "105288=4,7|105289=5,8|105290=6,9";
    public static final String XIYOU_SECRET_UNLOCK = "1|11|16|21|31|41|26|36|46";
    public static final String XIYOU_SECRET_REBORN_LIMIT = "2";
    public static final String XIYOU_SCENE_BG_OFFSET = "0";
    public static final String XIYOU_INIT_ITEMS = "105280=500|105283=10000|105282=10|105286=5";
    public static final String XIYOU_SECRET_DAILY_FREE = "105286=5";
    public static final String XIYOU_ENERGY_DIFF_LIMIT = "1";
    public static final String ANNIVERSARY_JUMP = "14=10054;68=10071;199=10273";
    public static final String ANNIVERSARY_REWARD = "14,100000=10|100004=10|100010=10|100003=10;68,100000=10|100004=10|100010=10|100003=10;199,100000=10|100004=10|100010=10|100003=10";

}