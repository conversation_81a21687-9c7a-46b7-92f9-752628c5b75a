package xddq.attribute.manager;

import org.apache.commons.lang3.ArrayUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import xddq.attribute.consts.CalculatorTypeEnum;
import xddq.attribute.consts.FighterAttributeEnum;
import xddq.attribute.structs.IAttributeCalculator;
import xddq.career.calculator.CareerCalculator;
import xddq.chara.calculator.CharaCalculator;
import xddq.cloud.calculator.CloudCalculator;
import xddq.consts.Global;
import xddq.destiny.calculator.DestinyCalculator;
import xddq.equip.calculator.EquipCalculator;
import xddq.gm.calculator.GmCalculator;
import xddq.lawlook.calculator.SpiritBodyCalculator;
import xddq.magic.calculator.MagicCalculator;
import xddq.magictreasure.calculator.MagicTreasureCalculator;

import xddq.pet.calculator.PetCalculator;
import xddq.player.calculator.BaseCalculator;
import xddq.player.structs.Player;
import xddq.pupil.calculator.PupilCalculator;
import xddq.spirit.calculator.SpiritCalculator;
import xddq.talent.calculator.TalentCalculator;
import xddq.tower.calculator.TowerCalculator;
import xddq.utils.AttributeUtil;
import xddq.worldrule.calculator.RuleCalculator;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class AttributeManager {

    protected static Logger log = LoggerFactory.getLogger(AttributeManager.class);
    // 属性计算器
    protected ConcurrentHashMap<CalculatorTypeEnum, IAttributeCalculator> playerCalculators = new ConcurrentHashMap<>();

    public void reload() {
        // 初始化战斗者相关属性计算器
        initPlayerAttrCal();
    }

    /**
     * 初始化战斗者属性计算
     */
    protected void initPlayerAttrCal() {
        registerPlayerAttrCal(new BaseCalculator());
        registerPlayerAttrCal(new GmCalculator());
        registerPlayerAttrCal(new EquipCalculator());
        registerPlayerAttrCal(new CloudCalculator());
        registerPlayerAttrCal(new PetCalculator());
        registerPlayerAttrCal(new MagicCalculator());
        registerPlayerAttrCal(new MagicTreasureCalculator());
        registerPlayerAttrCal(new TalentCalculator());
		registerPlayerAttrCal(new SpiritCalculator());
        registerPlayerAttrCal(new TowerCalculator());
        registerPlayerAttrCal(new PupilCalculator());
        registerPlayerAttrCal(new CareerCalculator());
        registerPlayerAttrCal(new DestinyCalculator());
        registerPlayerAttrCal(new CharaCalculator());
        registerPlayerAttrCal(new RuleCalculator());
        registerPlayerAttrCal(new SpiritBodyCalculator());
    }

    protected void registerPlayerAttrCal(IAttributeCalculator calculator) {
        playerCalculators.put(calculator.getType(), calculator);
    }

    /**
     * 初始化战斗者属性
     */
    public void initPlayerAttribute(Player player) {
        // 遍历所有计算器设置到玩家身上
        List<IAttributeCalculator> calculatorList = new ArrayList<>(playerCalculators.values());
        // 按照type大小从小到大排序
        calculatorList.sort(Comparator.comparingInt(o -> o.getType().getValue()));
        for (IAttributeCalculator calculator : calculatorList) {
            if (calculator.isTemp()) {
                continue;
            }
            ConcurrentHashMap<Integer, Long> attribute = new ConcurrentHashMap<>();
            ConcurrentHashMap<Integer, Integer> skill = new ConcurrentHashMap<>();
            calculator.countAttribute(player, attribute, skill);
            player.getAttributeCalculators().put(calculator.getType(), attribute);
            player.getSkillCalculators().put(calculator.getType(), skill);
        }
        setPlayer(player, false, player.getAttributeCalculators(), player.getSkillCalculators());
    }

    /**
     * 设置战斗者属性
     */
    public void setPlayer(Player player, boolean temp, ConcurrentHashMap<CalculatorTypeEnum, ConcurrentHashMap<Integer, Long>> calculators, ConcurrentHashMap<CalculatorTypeEnum, ConcurrentHashMap<Integer, Integer>> skills) {
        ConcurrentHashMap<Integer, Long> totalAttributes = new ConcurrentHashMap<>();
        ConcurrentHashMap<Integer, Integer> totalSkills = new ConcurrentHashMap<>();
        countBase(player, totalAttributes, totalSkills, calculators, skills);
        if (temp) {
            player.setTempTotalAttributes(totalAttributes);
        } else {
            player.setTotalAttributes(totalAttributes);
            player.setTotalSkills(totalSkills);
        }

        long power = calcPlayerPower(totalAttributes);
        if (temp) {
            player.setTempFightValue(power);
        } else {
            player.setFightValue(power);
        }
    }

    /**
     * 计算角色战力
     */
    public long calcPlayerPower(Map<Integer, Long> totalAttributes) {
        double power = 0;
        for (FighterAttributeEnum attr : FighterAttributeEnum.fighterBaseAttributeEnum) {
            Float p = FighterAttributeEnum.fightValues.getOrDefault(attr.getId(), 0f);
            if (p <= 0) {
                continue;
            }
            long v = totalAttributes.getOrDefault(attr.getId(), 0L);
            power += v * p;
        }
        //基础属性妖力=参数2*（参数1+攻击*参数4+生命*参数5+防御*参数6+敏捷*参数7)^参数3
        power = Global.FIGHT_POWER_PATAMETERS.get(2) * Math.pow(Global.FIGHT_POWER_PATAMETERS.get(1) + power, Global.FIGHT_POWER_PATAMETERS.get(3));
        double secPower = 0;
        for (FighterAttributeEnum attr : FighterAttributeEnum.fighterAttributeEnum) {
            if (ArrayUtils.contains(FighterAttributeEnum.fighterBaseAttributeEnum, attr)) {
                continue;
            }
            Float p = FighterAttributeEnum.fightValues.getOrDefault(attr.getId(), 0f);
            if (p <= 0) {
                continue;
            }
            long v = totalAttributes.getOrDefault(attr.getId(), 0L);
            secPower += v * p;
        }
        //其他属性妖力=基础属性妖力/960000*SUM(特殊属性数值*对应妖力系数）
        power += power / 960000 * secPower;

        return (long) power;
    }

    /**
     * 计算战斗者属性
     */
    protected void countBase(Player player, ConcurrentHashMap<Integer, Long> totalAttribute, ConcurrentHashMap<Integer, Integer> totalSkill, ConcurrentHashMap<CalculatorTypeEnum, ConcurrentHashMap<Integer, Long>> calculators, ConcurrentHashMap<CalculatorTypeEnum, ConcurrentHashMap<Integer, Integer>> skills) {
        for (CalculatorTypeEnum calculatortype : CalculatorTypeEnum.calculatorTypeEnum) {
            // 获取该资源线的属性
            ConcurrentHashMap<Integer, Long> attr = calculators.get(calculatortype);
            // 获取属性里具体的值
            AttributeUtil.addAttribute(totalAttribute, attr);
            if (skills != null) {
                // 获取技能
                ConcurrentHashMap<Integer, Integer> skill = skills.get(calculatortype);
                if (skill != null) {
                    totalSkill.putAll(skill);
                }
            }

        }

        // 千分比属性加成
        for (FighterAttributeEnum[] pecrnts : FighterAttributeEnum.fighterPercentAttributeEnum) {
            totalAttribute.put(pecrnts[0].getId(), AttributeUtil.countAttribute(totalAttribute.getOrDefault(pecrnts[0].getId(), 0L), 1000 + totalAttribute.getOrDefault(pecrnts[1].getId(), 0L)));
            totalAttribute.remove(pecrnts[1].getId());
        }
    }

    /**
     * 计算玩家属性
     */
    public void countPlayerAttribute(Player player, CalculatorTypeEnum type, boolean temp, Object... parameters) {
        IAttributeCalculator attributeCalculator = playerCalculators.get(type);
        if (attributeCalculator == null
                || !player.getAttributeCalculators().containsKey(attributeCalculator.getType())) {
            return;
        }
        ConcurrentHashMap<Integer, Long> attr = new ConcurrentHashMap<>();
        ConcurrentHashMap<Integer, Integer> skill = new ConcurrentHashMap<>();
        attributeCalculator.countAttribute(player, attr, skill, parameters);

        ConcurrentHashMap<CalculatorTypeEnum, ConcurrentHashMap<Integer, Long>> calculators;
        ConcurrentHashMap<CalculatorTypeEnum, ConcurrentHashMap<Integer, Integer>> skills = null;
        if (temp) {
            calculators = new ConcurrentHashMap<>(player.getAttributeCalculators());
        } else {
            calculators = player.getAttributeCalculators();
            skills = player.getSkillCalculators();
        }

        calculators.put(type, attr);
        if (skills != null) {
            skills.put(type, skill);
        }

        setPlayer(player, temp, calculators, skills);
    }

    public xddq.pb.AttributeDataMsg.Builder castAttribute2AttriteData(int type, long value) {
        //message AttributeDataMsg{
        //    required int32 type = 1;
        //    required string value = 2;
        //}
        xddq.pb.AttributeDataMsg.Builder builder = xddq.pb.AttributeDataMsg.newBuilder();
        builder.setType(type).setValue(String.valueOf(value));
        return builder;
    }

}
