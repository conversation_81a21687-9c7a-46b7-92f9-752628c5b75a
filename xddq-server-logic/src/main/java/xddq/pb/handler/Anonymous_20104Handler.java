package xddq.pb.handler;

import xddq.message.IMessage;
import xddq.message.handler.Handler;
import xddq.pb.message.Anonymous_20104Impl;

public class Anonymous_20104Handler extends Handler {

    @Override
    public void action(IMessage message) {
        try{
            Anonymous_20104Impl msg = (Anonymous_20104Impl)message;
            //TODO 添加消息处理

        }catch(Exception e){
            log.error(e.getMessage(), e);
        }
    }
}
