package xddq.pb.handler;

import xddq.message.IMessage;
import xddq.message.handler.Handler;
import xddq.pb.message.AskDingLeavePlaceReqMsg_205905Impl;

public class AskDingLeavePlaceReqMsg_205905<PERSON><PERSON><PERSON> extends Handler {

    @Override
    public void action(IMessage message) {
        try{
            AskDingLeavePlaceReqMsg_205905Impl msg = (AskDingLeavePlaceReqMsg_205905Impl)message;
            //TODO 添加消息处理

        }catch(Exception e){
            log.error(e.getMessage(), e);
        }
    }
}
