package xddq.pb.handler;

import xddq.message.IMessage;
import xddq.message.handler.Handler;
import xddq.pb.message.Anonymous_21064Impl;

public class Anonymous_21064Handler extends Handler {

    @Override
    public void action(IMessage message) {
        try{
            Anonymous_21064Impl msg = (Anonymous_21064Impl)message;
            //TODO 添加消息处理

        }catch(Exception e){
            log.error(e.getMessage(), e);
        }
    }
}
