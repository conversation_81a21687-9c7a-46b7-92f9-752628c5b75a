package xddq.pb.handler;

import xddq.message.IMessage;
import xddq.message.handler.Handler;
import xddq.pb.message.ManHuangOpenBoxReqMsg_25027Impl;

public class ManHuangOpenBoxReqMsg_25027Handler extends Handler {

    @Override
    public void action(IMessage message) {
        try{
            ManHuangOpenBoxReqMsg_25027Impl msg = (ManHuangOpenBoxReqMsg_25027Impl)message;
            //TODO 添加消息处理

        }catch(Exception e){
            log.error(e.getMessage(), e);
        }
    }
}
