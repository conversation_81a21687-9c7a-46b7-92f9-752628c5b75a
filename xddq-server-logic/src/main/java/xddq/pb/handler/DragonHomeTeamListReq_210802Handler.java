package xddq.pb.handler;

import xddq.message.IMessage;
import xddq.message.handler.Handler;
import xddq.pb.message.DragonHomeTeamListReq_210802Impl;

public class DragonHomeTeamListReq_210802Handler extends Handler {

    @Override
    public void action(IMessage message) {
        try{
            DragonHomeTeamListReq_210802Impl msg = (DragonHomeTeamListReq_210802Impl)message;
            //TODO 添加消息处理

        }catch(Exception e){
            log.error(e.getMessage(), e);
        }
    }
}
