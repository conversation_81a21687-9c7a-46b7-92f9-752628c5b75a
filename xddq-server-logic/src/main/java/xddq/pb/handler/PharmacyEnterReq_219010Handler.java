package xddq.pb.handler;

import xddq.message.IMessage;
import xddq.message.handler.Handler;
import xddq.pb.message.PharmacyEnterReq_219010Impl;

public class PharmacyEnterReq_219010H<PERSON><PERSON> extends Handler {

    @Override
    public void action(IMessage message) {
        try{
            PharmacyEnterReq_219010Impl msg = (PharmacyEnterReq_219010Impl)message;
            //TODO 添加消息处理

        }catch(Exception e){
            log.error(e.getMessage(), e);
        }
    }
}
