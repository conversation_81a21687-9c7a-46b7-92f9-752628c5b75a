package xddq.pb.handler;

import xddq.message.IMessage;
import xddq.message.handler.Handler;
import xddq.pb.message.PlanesTrialSetBuffPreferenceReq_216029Impl;

public class PlanesTrialSetBuffPreferenceReq_216029<PERSON><PERSON><PERSON> extends <PERSON>ler {

    @Override
    public void action(IMessage message) {
        try{
            PlanesTrialSetBuffPreferenceReq_216029Impl msg = (PlanesTrialSetBuffPreferenceReq_216029Impl)message;
            //TODO 添加消息处理

        }catch(Exception e){
            log.error(e.getMessage(), e);
        }
    }
}
