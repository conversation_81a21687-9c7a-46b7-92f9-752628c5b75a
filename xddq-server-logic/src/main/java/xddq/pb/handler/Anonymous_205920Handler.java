package xddq.pb.handler;

import xddq.message.IMessage;
import xddq.message.handler.Handler;
import xddq.pb.message.Anonymous_205920Impl;

public class Anonymous_205920Handler extends Handler {

    @Override
    public void action(IMessage message) {
        try{
            Anonymous_205920Impl msg = (Anonymous_205920Impl)message;
            //TODO 添加消息处理

        }catch(Exception e){
            log.error(e.getMessage(), e);
        }
    }
}
