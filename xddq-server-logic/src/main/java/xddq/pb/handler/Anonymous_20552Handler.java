package xddq.pb.handler;

import xddq.message.IMessage;
import xddq.message.handler.Handler;
import xddq.pb.message.Anonymous_20552Impl;

public class Anonymous_20552Handler extends Hand<PERSON> {

    @Override
    public void action(IMessage message) {
        try{
            Anonymous_20552Impl msg = (Anonymous_20552Impl)message;
            //TODO 添加消息处理

        }catch(Exception e){
            log.error(e.getMessage(), e);
        }
    }
}
