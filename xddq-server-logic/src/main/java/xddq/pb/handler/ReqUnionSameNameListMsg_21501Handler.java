package xddq.pb.handler;

import xddq.message.IMessage;
import xddq.message.handler.Handler;
import xddq.pb.message.ReqUnionSameNameListMsg_21501Impl;

public class ReqUnionSameNameListMsg_21501H<PERSON><PERSON> extends Handler {

    @Override
    public void action(IMessage message) {
        try{
            ReqUnionSameNameListMsg_21501Impl msg = (ReqUnionSameNameListMsg_21501Impl)message;
            //TODO 添加消息处理

        }catch(Exception e){
            log.error(e.getMessage(), e);
        }
    }
}
