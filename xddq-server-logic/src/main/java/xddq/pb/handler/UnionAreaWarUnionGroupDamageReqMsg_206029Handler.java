package xddq.pb.handler;

import xddq.message.IMessage;
import xddq.message.handler.Handler;
import xddq.pb.message.UnionAreaWarUnionGroupDamageReqMsg_206029Impl;

public class UnionAreaWarUnionGroupDamageReqMsg_206029Handler extends Handler {

    @Override
    public void action(IMessage message) {
        try{
            UnionAreaWarUnionGroupDamageReqMsg_206029Impl msg = (UnionAreaWarUnionGroupDamageReqMsg_206029Impl)message;
            //TODO 添加消息处理

        }catch(Exception e){
            log.error(e.getMessage(), e);
        }
    }
}
