package xddq.pb.handler;

import xddq.message.IMessage;
import xddq.message.handler.Handler;
import xddq.pb.message.Anonymous_218282Impl;

public class Anonymous_218282H<PERSON>ler extends Hand<PERSON> {

    @Override
    public void action(IMessage message) {
        try{
            Anonymous_218282Impl msg = (Anonymous_218282Impl)message;
            //TODO 添加消息处理

        }catch(Exception e){
            log.error(e.getMessage(), e);
        }
    }
}
