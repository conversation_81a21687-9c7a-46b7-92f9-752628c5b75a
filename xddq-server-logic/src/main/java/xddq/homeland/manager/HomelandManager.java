package xddq.homeland.manager;

import com.alibaba.fastjson.JSON;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import xddq.consts.Global;
import xddq.count.manager.CountManager;
import xddq.count.structs.CountTypes;
import xddq.data.DataPool;
import xddq.data.bean.DatahomelandrewardHomelandrewardBean;
import xddq.homeland.structs.*;
import xddq.manager.BaseManagerPool;
import xddq.manager.ManagerPool;

import xddq.pb.message.*;
import xddq.player.structs.Player;
import xddq.server.impl.GameServer;
import xddq.structs.MapList;
import xddq.task.consts.TriggerType;
import xddq.utils.MessageUtil;
import xddq.utils.TimeUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

//福地系统 问题很多 后面整理
//177	HOMELAND_ENERGY_DIVIDE	100|50|25|15
//178	HOMELAND_ENERGY_SPEED	20|40|80|4000
//179	HOMELAND_ENERGY_COPE_SPEED	500|300|100|10
//180	HOMELAND_PULL_BASIC_TIME	2
//181	HOMELAND_ITEM_REFRESH_PARAM	100|-100
//182	HOMELAND_AUTO_REFRESH_TIME	10|18|22
//183	HOMELAND_AUTO_REFRESH_TIME_PER	600
//184	HOMELAND_SINGLE_BOX_MOUSE_LIMIT_CONFIG	3|3|3|4|4|5|5
//185	HOMELAND_FREE_REFRESH_TIME	2
//186	HOMELAND_FREE_REFRESH_CD	1
//187	HOMELAND_PAY_REFRESH_COST	100000=10;100000=10;100000=10
//188	HOMELAND_BASIC_WORKER_NUM	1
//189	HOMELAND_WORKER_COST	100029=30;100029=300;100029=600;100029=900;100029=1200;100029=1500;100029=1800;100029=2100;100029=2400
//190	HOMELAND_TARGET_REFRESH_TIME	300
//191	HOMELAND_TARGET_REFRESH_COUNT	3
//192	HOMELAND_TARGET_REFRESH_LIMIT	100
//193	HOMELAND_TARGET_AROUND_LIMIT	1|1|1|1|56|96|156|236
//194	HOMELAND_TARGET_ENEMY_LIMIT	100
//195	HOMELAND_TARGET_HATRED	1
//196	HOMELAND_TARGET_ATKBACK_HATRED	1
//197	HOMELAND_TARGET_OLD_HATRED	3
//198	HOMELAND_TARGET_AROUND_LEVEL	3750
//199	HOMELAND_TARGET_AROUND_LEVEL_LEAST	10
//200	HOMELAND_TARGET_PROTECT_TIMES	10
//201	HOMELAND_AWARD_SCHEDULE_NUM	1
//202	HOMELAND_WEEK_CARD_BUY_ID	540000001
//203	HOMELAND_MONTH_CARD_BUY_ID	540000002
//204	HOMELAND_AUTO_PULL_TIME_LIMIT	60
//205	HOMELAND_TRY_TIME	3
//206	HOMELAND_SUPER_REFRESH_COST	1|100029=50;51|100029=100
public class HomelandManager {

    protected static final Logger log = LoggerFactory.getLogger(HomelandManager.class);

    private String cron;

    public void init() {
        //182	HOMELAND_AUTO_REFRESH_TIME	10|18|22
        String hours = BaseManagerPool.getInstance().dataManager.datagameconfigGameconfigContainer.getDatagameconfigGameconfigBean(182).getValue().replaceAll("\\|", ",");
        cron = "0 0 " + hours + " * * ?";
    }

    public void sendHomeland(Player player) {
        Homeland homeland = player.getHomeland();
        if (homeland == null) {
            return;
        }
        homeland.setPlayerId(player.getId());
        //message SyncHomelandMsg{
        //required int32 totalWorkerNum = 2;
        //required int32 freeWorkerNum = 1;
        //required int32 energy = 3;
        //required int32 lv = 4;
        //optional  HomelandSyncMouseManagerMsg mouseManager = 5;
        //}
        xddq.pb.SyncHomelandMsg.Builder builder = xddq.pb.SyncHomelandMsg.newBuilder();
        builder.setTotalWorkerNum(homeland.getTotal());
        builder.setFreeWorkerNum(homeland.getTotal() - homeland.getWorkingNum());
        builder.setEnergy(homeland.getEnergy());
        builder.setLv(homeland.getLv());
        builder.setMouseManager(getMouseManager(player, homeland));

        MessageUtil.tellPlayer(player, new SyncHomelandMsg_1051Impl(builder));
    }


    public void sendHomelandChange(Player player) {

        Homeland homeland = player.getHomeland();
        if (homeland == null) {
            return;
        }
        //message SyncHomelandMsg{
        //required int32 totalWorkerNum = 2;
        //required int32 freeWorkerNum = 1;
        //required int32 energy = 3;
        //required int32 lv = 4;
        //optional  HomelandSyncMouseManagerMsg mouseManager = 5;
        //}
        xddq.pb.SyncPlayerHomelandChangeMsg.Builder builder = xddq.pb.SyncPlayerHomelandChangeMsg.newBuilder();
        builder.setHomeland(getHomeland(player, player.getHomeland(), false));
        MessageUtil.tellPlayer(player, new SyncPlayerHomelandChangeMsg_1064Impl(builder));
    }

    public void sendRoundChange(Player player,RoundData roundData) {

        Homeland homeland = player.getHomeland();
        if (homeland == null) {
            return;
        }
        //message SyncHomelandMsg{
        //required int32 totalWorkerNum = 2;
        //required int32 freeWorkerNum = 1;
        //required int32 energy = 3;
        //required int32 lv = 4;
        //optional  HomelandSyncMouseManagerMsg mouseManager = 5;
        //}
        xddq.pb.SyncPlayerHomelandChangeMsg.Builder builder = xddq.pb.SyncPlayerHomelandChangeMsg.newBuilder();
        builder.setHomeland(getHomeland(player, roundData, false));
        MessageUtil.tellPlayer(player, new SyncPlayerHomelandChangeMsg_1064Impl(builder));
    }

    public void sendHasReward(Player player) {

        Homeland homeland = player.getHomeland();
        if (homeland == null) {
            return;
        }
        //message SyncHomelandMsg{
        //required int32 totalWorkerNum = 2;
        //required int32 freeWorkerNum = 1;
        //required int32 energy = 3;
        //required int32 lv = 4;
        //optional  HomelandSyncMouseManagerMsg mouseManager = 5;
        //}
        xddq.pb.SyncHomelandHasRewardMsg.Builder builder = xddq.pb.SyncHomelandHasRewardMsg.newBuilder();
        MessageUtil.tellPlayer(player, new SyncHomelandHasRewardMsg_1062Impl(builder));
    }

    public void unlock(Player player) {
        Homeland homeland = player.getHomeland();
        if (homeland != null) {
            sendHomeland(player);
            return;
        }
        homeland = new Homeland();
        homeland.setPlayerId(player.getId());
        homeland.setInfo(createHomelandPlayerInfo(player));
        refresh(homeland, true);
        homeland.setTotal(Integer.parseInt(BaseManagerPool.getInstance().dataManager.datagameconfigGameconfigContainer.getDatagameconfigGameconfigBean(188).getValue()));
        // TODO 能量计算
//        homeland.setEnergy();
        homeland.setLv(1);
        homeland.setNextFreshTime(TimeUtil.getNextRefreshTime(cron));

        // TODO 保存
        player.setHomeland(homeland);
        BaseManagerPool.getInstance().redisManager.hset("FUDI:" + GameServer.SERVER_KEY, String.valueOf(player.getId()), JSON.toJSONString(homeland));
        sendHomeland(player);
    }

    private HomelandPlayerInfo createHomelandPlayerInfo(Player player) {
        HomelandPlayerInfo info = new HomelandPlayerInfo();
        info.setNickName(player.getName());
        info.setHeadIcon(player.getCharaData().getEquipHeadIcon());
        info.setServerId(player.getVirtualGameServerId());
        //TODO 微信头像
//        info.setWxHeadUrl()
        try {
            info.setHeadInfo(JsonFormat.printer().print(ManagerPool.getInstance().playerManager.getPlayerHeadData(player)));
        } catch (InvalidProtocolBufferException e) {
            log.error(e.getMessage(), e);
        }
        return info;
    }


//    public void refreshAll() {
//        long now = TimeUtil.getCurrentTimeMillis();
//        ArrayList<HashMap<Long, HashMap<Integer, Long>>> rewards = new ArrayList<>();
//        for (Map.Entry<Long, Homeland> entry : homelands.entrySet()) {
//            Homeland v = entry.getValue();
//            synchronized (v) {
//                rewards.add(countReward(v));
//                int count = 0;
//                if (v.getNextFreshTime() < now) {
//                    v.setNextFreshTime(TimeUtil.getNextRefreshTime(cron));
//                    count = refresh(v, true);
//                }
////                if (count > 0) {
////                    waitSave.add(entry.getKey(), v);
////                }
//            }
//        }
//        for (HashMap<Long, HashMap<Integer, Long>> reward : rewards) {
//            for (Map.Entry<Long, HashMap<Integer, Long>> entry : reward.entrySet()) {
//                Homeland homeland = homelands.get(entry.getKey());
//                if (homeland == null) {
//                    continue;
//                }
//                synchronized (homeland) {
//                    for (Map.Entry<Integer, Long> ent : entry.getValue().entrySet()) {
//                        homeland.getRewards().put(ent.getKey(), homeland.getRewards().getOrDefault(ent.getKey(), 0L) + ent.getValue());
//                    }
//                 //   waitSave.add(entry.getKey(), homeland);
//                }
//            }
//        }
//
//        // 定时保存
////        List<Homeland> pop = waitSave.pop(5);
////        for (Homeland homeland : pop) {
////            BaseManagerPool.getInstance().redisManager.hset("FUDI:" + GameServer.SERVER_KEY, String.valueOf(homeland.getPlayerId()), JSON.toJSONString(homeland));
////        }
//    }

    public HashMap<Long, HashMap<Integer, Long>> countReward(Homeland homeland) {
        long now = TimeUtil.getCurrentTimeMillis();
        HashMap<Long, HashMap<Integer, Long>> rewards = new HashMap<>();
        List<Integer> removePos = new ArrayList<>();
        homeland.getItems().forEach((k, v) -> {
            if (!v.isReward() && v.getFinishTime() < now && v.getFinishTime() != 0) {
                if (v.getOwner() != null) {
                    HashMap<Integer, Long> map = rewards.computeIfAbsent(v.getOwner().getPlayerId(), f -> new HashMap<>());
                    map.put(v.getItemId(), map.getOrDefault(v.getItemId(), 0L) + v.getNum());
                    homeland.getRewards().put(v.getItemId(), map.getOrDefault(v.getItemId(), 0L) + v.getNum());
                    if (homeland.getWorkingNum() - v.getOwner().getWorkerNum() >= 0) {
                        homeland.setWorkingNum(homeland.getWorkingNum() - v.getOwner().getWorkerNum()); //返还老鼠
                    }

//                    if (homeland.getHomelandTime().containsKey(v.getPos())) {
//                        homeland.getHomelandTime().remove(v.getPos());
//                    }
                    if (homeland.getRoundTime().get(homeland.getPlayerId()).containsKey(v.getPos())) {
                        homeland.getRoundTime().get(homeland.getPlayerId()).remove(v.getPos());
                    }
                    v.setReward(true);
                    v.setFinishTime(0);
                    removePos.add(v.getPos());

                } else if (v.getEnemy() != null) {//Todo
                    HashMap<Integer, Long> map = rewards.computeIfAbsent(v.getEnemy().getPlayerId(), f -> new HashMap<>());
                    map.put(v.getItemId(), map.getOrDefault(v.getItemId(), 0L) + v.getNum());
                    Player otherPlayer = ManagerPool.getInstance().playerManager.getPlayer(v.getEnemy().getPlayerId());
                    if (otherPlayer != null) {
                        Homeland otherHomeland = otherPlayer.getHomeland();
                        if (otherHomeland != null) {
                            if (otherHomeland.getWorkingNum() - v.getEnemy().getWorkerNum() >= 0) {
                                otherHomeland.setWorkingNum(otherHomeland.getWorkingNum() - v.getEnemy().getWorkerNum()); // 返还他人老鼠
                            }

                        }
                    }
                }
            }
        });

        if (removePos.size() > 0) {
            for (int i = 0; i < removePos.size(); i++) {
                homeland.getItems().remove(removePos.get(i));
            }
        }
        ConcurrentHashMap<Long, Integer>  removeOld = new ConcurrentHashMap<>();
        homeland.getRoundData().forEach((k, v) -> {

            v.getItems().forEach((m, n) -> {
                if (!n.isReward() && n.getFinishTime() < now && n.getFinishTime() != 0) {
                    if (n.getEnemy() != null) {
                        HashMap<Integer, Long> map = rewards.computeIfAbsent(n.getEnemy().getPlayerId(), f -> new HashMap<>());
                        map.put(n.getItemId(), map.getOrDefault(n.getItemId(), 0L) + n.getNum());
                        homeland.getRewards().put(n.getItemId(), map.getOrDefault(n.getItemId(), 0L) + n.getNum());
                        if (homeland.getWorkingNum() - n.getEnemy().getWorkerNum() >= 0) {
                            homeland.setWorkingNum(homeland.getWorkingNum() - n.getEnemy().getWorkerNum()); //返还老鼠
                        }

                        if (homeland.getRoundTime().get(k).containsKey(n.getPos())) {
                            homeland.getRoundTime().get(k).remove(n.getPos());
                        }
                        n.setReward(true);
                        n.setFinishTime(0);
                        removeOld.put(k,n.getPos());
                        }
                    }
                });

            });
        if(removeOld.size()>0){
            removeOld.forEach((m, n) -> {
                homeland.getRoundData().get(m).getItems().remove(n);
            });
        }

            return rewards;
    }


    public int refresh(Homeland homeland, boolean force) {

        List<DatahomelandrewardHomelandrewardBean> weights = BaseManagerPool.getInstance().dataManager.datahomelandrewardHomelandrewardContainer.getWeights();
        int totalWeight = BaseManagerPool.getInstance().dataManager.datahomelandrewardHomelandrewardContainer.getTotalWeight();
        String[] maxs = BaseManagerPool.getInstance().dataManager.datagameconfigGameconfigContainer.getDatagameconfigGameconfigBean(184).getValue().split("\\|");
        int num = 0;
        for (int i = 0; i < 6; i++) {
            HomelandItem homelandItem = homeland.getItems().get(i);
            if (homelandItem == null
                    || homelandItem.isReward() && homelandItem.getFinishTime() == 0 ||force && homelandItem.getFinishTime() == 0
            ) {
                // 刷新物品
                num++;
                DatahomelandrewardHomelandrewardBean bean = random(weights, totalWeight);
                if (bean == null) {
                    continue;
                }
                homelandItem = new HomelandItem();
                homelandItem.setModelId(bean.getId());
                List<long[]> items = ManagerPool.getInstance().itemManager.createItems(bean.getReward());
                homelandItem.setItemId((int) items.getFirst()[0]);
                homelandItem.setNum((int) items.getFirst()[1]);
                homelandItem.setRewardLv(bean.getLevel());
                // TODO 距离
//                homelandItem.setDistance()
                homelandItem.setPos(i);
                homelandItem.setMaxWorkerNum(Integer.parseInt(maxs[homelandItem.getRewardLv() - 1]));
                homeland.getItems().put(i, homelandItem);
            }
        }
        Player player = ManagerPool.getInstance().playerManager.getPlayer(homeland.getPlayerId());
        player.setHomeland(homeland);
        return num;
    }

    public int  refresh(Player player,RoundData roundData, boolean force) {

        List<DatahomelandrewardHomelandrewardBean> weights = BaseManagerPool.getInstance().dataManager.datahomelandrewardHomelandrewardContainer.getWeights();
        int totalWeight = BaseManagerPool.getInstance().dataManager.datahomelandrewardHomelandrewardContainer.getTotalWeight();
        String[] maxs = BaseManagerPool.getInstance().dataManager.datagameconfigGameconfigContainer.getDatagameconfigGameconfigBean(184).getValue().split("\\|");
        int num = 0;
        for (int i = 0; i < 6; i++) {
            HomelandItem homelandItem = roundData.getItems().get(i);
            if (homelandItem == null
                    || homelandItem.isReward() && homelandItem.getFinishTime() == 0 ||force && homelandItem.getFinishTime() == 0
            ) {
                // 刷新物品
                num++;
                DatahomelandrewardHomelandrewardBean bean = random(weights, totalWeight);
                if (bean == null) {
                    continue;
                }
                homelandItem = new HomelandItem();
                homelandItem.setModelId(bean.getId());
                List<long[]> items = ManagerPool.getInstance().itemManager.createItems(bean.getReward());
                homelandItem.setItemId((int) items.getFirst()[0]);
                homelandItem.setNum((int) items.getFirst()[1]);
                homelandItem.setRewardLv(bean.getLevel());
                // TODO 距离
//                homelandItem.setDistance()
                homelandItem.setPos(i);
                homelandItem.setMaxWorkerNum(Integer.parseInt(maxs[homelandItem.getRewardLv() - 1]));
                roundData.getItems().put(i, homelandItem);
            }

        }
        player.getHomeland().getRoundData().put(roundData.getPlayerId(),roundData);
        return num;
    }


    private ConcurrentHashMap<Integer, HomelandItem>  refresh(RoundData roundData, boolean force) {

        List<DatahomelandrewardHomelandrewardBean> weights = BaseManagerPool.getInstance().dataManager.datahomelandrewardHomelandrewardContainer.getWeights();
        int totalWeight = BaseManagerPool.getInstance().dataManager.datahomelandrewardHomelandrewardContainer.getTotalWeight();
        String[] maxs = BaseManagerPool.getInstance().dataManager.datagameconfigGameconfigContainer.getDatagameconfigGameconfigBean(184).getValue().split("\\|");
        int num = 0;
        for (int i = 0; i < 6; i++) {
            HomelandItem homelandItem = roundData.getItems().get(i);
            if (homelandItem == null
                    || homelandItem.isReward() && homelandItem.getFinishTime() == 0 ||force && homelandItem.getFinishTime() == 0
            ) {
                // 刷新物品
                num++;
                DatahomelandrewardHomelandrewardBean bean = random(weights, totalWeight);
                if (bean == null) {
                    continue;
                }
                homelandItem = new HomelandItem();
                homelandItem.setModelId(bean.getId());
                List<long[]> items = ManagerPool.getInstance().itemManager.createItems(bean.getReward());
                homelandItem.setItemId((int) items.getFirst()[0]);
                homelandItem.setNum((int) items.getFirst()[1]);
                homelandItem.setRewardLv(bean.getLevel());
                // TODO 距离
//                homelandItem.setDistance()
                homelandItem.setPos(i);
                homelandItem.setMaxWorkerNum(Integer.parseInt(maxs[homelandItem.getRewardLv() - 1]));
                roundData.getItems().put(i, homelandItem);
            }

        }

        return roundData.getItems();
    }

    private DatahomelandrewardHomelandrewardBean random(List<DatahomelandrewardHomelandrewardBean> weights, int totalWeight) {
        int w = RandomUtils.nextInt(0, totalWeight);
        for (DatahomelandrewardHomelandrewardBean bean : weights) {
            if (w < bean.getWeight()) {
                return bean;
            } else {
                w -= bean.getWeight();
            }
        }
        return null;
    }

    //message HomelandRefreshReq{
    //    required int32 type = 1;
    //    optional int32 position = 2;
    //    optional int32 itemId = 3;
    //    optional int64 otherPlayerID = 4;
    //    optional bool isUseADTime = 5;
    //}
    public xddq.pb.HomelandRefreshResp.Builder homelandRefresh(Player player, xddq.pb.HomelandRefreshReq req) {
        //message HomelandRefreshResp{
        //required int32 ret = 1;
        //optional HomelandMsg homeland = 2;
        //}
        xddq.pb.HomelandRefreshResp.Builder builder = xddq.pb.HomelandRefreshResp.newBuilder();

        Homeland homeland = player.getHomeland();
        if (req.getType() == 1) { //免费刷新
            long count = ManagerPool.getInstance().countManager.getCount(player, CountTypes.FREE_HOMELAND_REFRESH, null);
            if (count >= 2) {
                builder.setRet(12);
                return builder;
            }
            ManagerPool.getInstance().countManager.addCount(player, CountTypes.FREE_HOMELAND_REFRESH, null, CountManager.COUNT_DAY, 1, Global.DAY_REFRESH_TIME);

        } else {
            List<long[]> costs = new ArrayList<>();
            costs.add(new long[]{100000, -10});
            if (!ManagerPool.getInstance().backpackManager.canChangeItem(player, costs)) {
                builder.setRet(101);
                return builder;
            }
            ManagerPool.getInstance().backpackManager.changeItem(player, costs);

        }

        if (homeland == null) {
            return builder;
        }
        //  ArrayList<HashMap<Long, HashMap<Integer, Long>>> rewards = new ArrayList<>();
        synchronized (homeland) {
            //  rewards.add(countReward(homeland));
            int count = refresh(homeland, true);

        }
//        for (HashMap<Long, HashMap<Integer, Long>> reward : rewards) {
//            for (Map.Entry<Long, HashMap<Integer, Long>> entry : reward.entrySet()) {
//                Homeland home = homelands.get(entry.getKey());
//                if (home == null) {
//                    continue;
//                }
//                synchronized (home) {
//                    for (Map.Entry<Integer, Long> ent : entry.getValue().entrySet()) {
//                        home.getRewards().put(ent.getKey(), home.getRewards().getOrDefault(ent.getKey(), 0L) + ent.getValue());
//                    }
//                    waitSave.add(entry.getKey(), home);
//                }
//            }
//        }

        builder.setRet(0);
        builder.setHomeland(getHomelandData(player, homeland));
        sendHomeland(player);
        return builder;
    }

    public xddq.pb.HomelandRefreshExploreResp.Builder homelandRefreshExplore(Player player, xddq.pb.HomelandRefreshExploreReq req) {
        xddq.pb.HomelandRefreshExploreResp.Builder builder = xddq.pb.HomelandRefreshExploreResp.newBuilder();
        long now = TimeUtil.getCurrentTimeMillis();
        Homeland homeland = player.getHomeland();
        homeland.setLastExpRefreshTime(now);
        for(RoundData roundData:homeland.getRoundData().values()){
            refresh(player,roundData,true);
        }
        builder.setRet(0);

        builder.setExploreData(getHomelandExplore(homeland));

        return builder;
    }

    public xddq.pb.HomelandAutoCollectItem.Builder getHomelandAutoCollectItem(HomelandAutoCollectItem homelandAutoCollectItem) {
        xddq.pb.HomelandAutoCollectItem.Builder builder = xddq.pb.HomelandAutoCollectItem.newBuilder();
        builder.setItemId(homelandAutoCollectItem.getItemId());
        builder.setMinItemLv(homelandAutoCollectItem.getMinItemLv());
        builder.setIsCheck(homelandAutoCollectItem.isCheck());
        return builder;
    }

    private xddq.pb.HomelandSyncMouseManagerMsg.Builder getMouseManager(Player player, Homeland homeland) {
        //message HomelandSyncMouseManagerMsg{
        //  optional int64 expireTime = 1;
        //  optional int32 freeTimes = 2;
        //  repeated HomelandAutoCollectItem ItemSettings = 3;
        //  optional int64 nextTime = 4;
        //}
        xddq.pb.HomelandSyncMouseManagerMsg.Builder builder = xddq.pb.HomelandSyncMouseManagerMsg.newBuilder();

        builder.setExpireTime(homeland.getExpireTime()); //自动失效时间
        builder.setFreeTimes(homeland.getFreeTimes());
        //builder.setNextTime(TimeUtil.getCurrentTimeMillis()+3600);
        builder.setNextTime(homeland.getNextTime());
        if (player.getHomelandAutoCollectItem().size() > 0) {
            for (int i = 0; i < player.getHomelandAutoCollectItem().size(); i++) {
                builder.addItemSettings(getHomelandAutoCollectItem(player.getHomelandAutoCollectItem().get(i)));
            }
        } else {
            homeland.getAutoCollects().forEach((k, v) -> {
                builder.addItemSettings(getAutoCollectSetting(v));
            });
        }

        return builder;
    }

    private xddq.pb.HomelandAutoCollectItem.Builder getAutoCollectSetting(HomelandAuto setting) {
        //message HomelandAutoCollectItem{
        //  optional int32 ItemId = 1;
        //  optional int32 minItemLv = 2;
        //  optional bool isCheck = 3;
        //}
        xddq.pb.HomelandAutoCollectItem.Builder builder = xddq.pb.HomelandAutoCollectItem.newBuilder();
        builder.setItemId(setting.getItemId());
        builder.setMinItemLv(setting.getMinItemLv());
        builder.setIsCheck(setting.isCheck());
        return builder;
    }

    private String getRandName() {
        String[] name = {
                "剑仙", "无极", "独孤求败", "琳", "云霄剑客", "沧海明月", "逍遥散仙", "天元道君", "紫霞仙子", "星辰剑主", "碧落仙尊", "白龙霸主",
                "玄天剑心", "凌霄仙君", "幽谷子", "青莲剑仙", "烟雨楼主", "悟道真人", "问天", "隐士", "千年雪狐", "浮生剑客", "cANGl", "沧海一生",
                "通天、教主", "风一样的男人", "无敌剑尊", "道人", "梦幻仙尊", "九霄云客", "剑影、无敌", "L9876"};
        int i = RandomUtils.nextInt(0, 100);
        if (i > 10) {
            return ManagerPool.getInstance().playerManager.generateRandomNickname(10);
        } else {
            return name[RandomUtils.nextInt(0, name.length)];
        }
    }

    public xddq.pb.HomelandEnterResp.Builder homelandEnter(Player player, xddq.pb.HomelandEnterReq req) {
        //message HomelandEnterResp{
        //optional HomelandMsg homeland = 1;
        //}
        xddq.pb.HomelandEnterResp.Builder builder = xddq.pb.HomelandEnterResp.newBuilder();
        player.setCurrentPage(11);
        Homeland homeland = player.getHomeland();
        if(homeland.getRoundData().size()<10){
            initRoundData(player);
        }
        if(homeland.getHomelandTime().size()>0){
            homeland.getRewards().forEach((k, v) ->{
                homeland.getRoundTime().get(player.getId()).put(k,v);
            });
        }

        synchronized (homeland){
            HashMap<Long, HashMap<Integer, Long>> countReward =  ManagerPool.getInstance().homelandManager.countReward(homeland);
            if(countReward.size()>0){
                ManagerPool.getInstance().homelandManager.sendHasReward(player);
                ManagerPool.getInstance().homelandManager.sendHomeland(player);
                ManagerPool.getInstance().homelandManager.sendHomelandChange(player);
                for(RoundData roundData:homeland.getRoundData().values()){
                    ManagerPool.getInstance().homelandManager.sendRoundChange(player,roundData);
                }
            }

        }

        if(req.getPlayerId() == player.getId()){
            builder.setHomeland(getHomeland(player, homeland, false));
            sendHomeland(player);
            syncPlayerCornucopiaChange(player);
        }else{
            if(!homeland.getRoundData().containsKey(req.getPlayerId())){
                return builder;
            }
            builder.setHomeland(getHomeland(player,homeland.getRoundData().get(req.getPlayerId()) , false));
        }


        return builder;
    }

    public xddq.pb.HomelandMsg.Builder getHomeland(Player player, Homeland homeland, boolean isFresh) {
        //message HomelandMsg{
        //required int32 lv = 1;
        //repeated HomelandRewardMsg reward = 2;
        //required int64 nextFreshTime = 3;
        //required HomelandPlayerInfoMsg owner = 4;
        //optional int32 freeRefreshCount = 5;
        //optional int64 lastFreeRefreshTime = 6;
        //optional int32 itemRefreshCount = 7;
        //required int32 superRefreshTimes = 8;
        //}
        xddq.pb.HomelandMsg.Builder builder = xddq.pb.HomelandMsg.newBuilder();

        long now = TimeUtil.getCurrentTimeMillis();
        int count = 0;
        //  if(isFresh){
//        synchronized (homeland) {
//            countReward(player,homeland);
//        }
        homeland = player.getHomeland();
//        if (homeland.getNextFreshTime() < now) {
//            homeland.setNextFreshTime(TimeUtil.getNextRefreshTime(cron));
//            count = refresh(homeland, true);
//        }

        for (HomelandItem homelandItem : homeland.getItems().values()) {
            // HomelandItem homelandItem = homeland.getItems().get(i);
            if (homelandItem != null && !homelandItem.isReward()) {
                homelandItem.setPlayerId(player.getId());
                builder.addReward(getHomelandReward(homelandItem));
            }
        }
        builder.setLv(homeland.getLv());
        builder.setNextFreshTime(homeland.getNextFreshTime());
        builder.setOwner(getHomelandPlayerInfo(player));
        builder.setFreeRefreshCount((int) ManagerPool.getInstance().countManager.getCount(player, CountTypes.FREE_HOMELAND_REFRESH, null));
        builder.setLastFreeRefreshTime(homeland.getLastFreeRefreshTime());
        builder.setItemRefreshCount(count);
        builder.setSuperRefreshTimes(homeland.getSuperRefreshTimes());
        player.setHomeland(homeland);
        return builder;
    }




    public xddq.pb.HomelandMsg.Builder getHomeland(Player player, RoundData roundData, boolean isFresh) {
        //message HomelandMsg{
        //required int32 lv = 1;
        //repeated HomelandRewardMsg reward = 2;
        //required int64 nextFreshTime = 3;
        //required HomelandPlayerInfoMsg owner = 4;
        //optional int32 freeRefreshCount = 5;
        //optional int64 lastFreeRefreshTime = 6;
        //optional int32 itemRefreshCount = 7;
        //required int32 superRefreshTimes = 8;
        //}
        xddq.pb.HomelandMsg.Builder builder = xddq.pb.HomelandMsg.newBuilder();

        Homeland homeland = player.getHomeland();


        for (HomelandItem homelandItem : roundData.getItems().values()) {
            // HomelandItem homelandItem = homeland.getItems().get(i);
            if (homelandItem != null && !homelandItem.isReward()) {
                homelandItem.setPlayerId(roundData.getPlayerId());
                builder.addReward(getHomelandReward(homelandItem));
            }
        }
        builder.setLv(homeland.getLv());
        builder.setNextFreshTime(homeland.getNextFreshTime());
        builder.setOwner(getRoundDataPlayerInfo(roundData));
        // builder.setFreeRefreshCount((int) ManagerPool.getInstance().countManager.getCount(player, CountTypes.FREE_HOMELAND_REFRESH, null));
        builder.setFreeRefreshCount(0);
        // builder.setLastFreeRefreshTime(homeland.getLastFreeRefreshTime());
        builder.setLastFreeRefreshTime(0);
        //  builder.setItemRefreshCount(count);
        builder.setItemRefreshCount(0);
        //   builder.setSuperRefreshTimes(homeland.getSuperRefreshTimes());
        builder.setSuperRefreshTimes(0);

        return builder;
    }




    public xddq.pb.HomelandMsg.Builder getHomelandData(Player player, Homeland homeland) {

        xddq.pb.HomelandMsg.Builder builder = xddq.pb.HomelandMsg.newBuilder();
        int count = 0;
        //  if(isFresh){

        for (HomelandItem homelandItem : homeland.getItems().values()) {
            // HomelandItem homelandItem = homeland.getItems().get(i);
            if (homelandItem != null) {
                homelandItem.setPlayerId(player.getId());
                builder.addReward(getHomelandReward(homelandItem));
            }
        }

        builder.setLv(homeland.getLv());
        builder.setNextFreshTime(homeland.getNextFreshTime());
        builder.setOwner(getHomelandPlayerInfo(player));
        builder.setFreeRefreshCount((int) ManagerPool.getInstance().countManager.getCount(player, CountTypes.FREE_HOMELAND_REFRESH, null));
        builder.setLastFreeRefreshTime(homeland.getLastFreeRefreshTime());
        builder.setItemRefreshCount(count);
        builder.setSuperRefreshTimes(homeland.getSuperRefreshTimes());

        return builder;
    }


    public xddq.pb.HomelandManageResp.Builder getHomelandHomelandManage(Player player) {
        //message HomelandPlayerInfoMsg{
        //  required int64 playerId = 1;
        //  required string nickName = 2;
        //  required int32 headIcon = 3;
        //  optional int64 serverId = 4;
        //  optional string wxHeadUrl = 5;
        //  optional PlayerHeadDataMsg headInfo = 6;
        //}

        xddq.pb.HomelandManageResp.Builder builder = xddq.pb.HomelandManageResp.newBuilder();
        Homeland homeland = player.getHomeland();

        for (HomelandItem homelandItem : homeland.getItems().values()) {
            //  HomelandItem homelandItem = homeland.getItems().get(i);
            if (homelandItem != null) {
                if (homelandItem.getFinishTime() > TimeUtil.getCurrentTimeMillis()) {
                    builder.addReward(getHomelandReward(homelandItem));
                }
            }
        }

        homeland.getRoundData().forEach((k, v) -> {
            for (HomelandItem homelandItem : v.getItems().values()) {
                //  HomelandItem homelandItem = homeland.getItems().get(i);
                if (homelandItem != null) {
                    if (homelandItem.getFinishTime() > TimeUtil.getCurrentTimeMillis()) {
                        builder.addReward(getHomelandReward(homelandItem));
                    }
                }
            }
        });

        return builder;
    }

    private xddq.pb.HomelandPlayerInfoMsg.Builder getHomelandPlayerInfo(Player player) {
        //message HomelandPlayerInfoMsg{
        //  required int64 playerId = 1;
        //  required string nickName = 2;
        //  required int32 headIcon = 3;
        //  optional int64 serverId = 4;
        //  optional string wxHeadUrl = 5;
        //  optional PlayerHeadDataMsg headInfo = 6;
        //}
        xddq.pb.HomelandPlayerInfoMsg.Builder builder = xddq.pb.HomelandPlayerInfoMsg.newBuilder();
        builder.setPlayerId(player.getId());
        builder.setNickName(player.getName());
        builder.setHeadIcon(player.getCharaData().getEquipHeadIcon());
        //builder.setServerId(player.getVirtualGameServerId());
        builder.setWxHeadUrl("");
        builder.setHeadInfo(ManagerPool.getInstance().playerManager.getPlayerHeadData(player));
        return builder;
    }



    private xddq.pb.HomelandPlayerInfoMsg.Builder getRoundDataPlayerInfo(RoundData roundData) {
        //message HomelandPlayerInfoMsg{
        //  required int64 playerId = 1;
        //  required string nickName = 2;
        //  required int32 headIcon = 3;
        //  optional int64 serverId = 4;
        //  optional string wxHeadUrl = 5;
        //  optional PlayerHeadDataMsg headInfo = 6;
        //}
        xddq.pb.HomelandPlayerInfoMsg.Builder builder = xddq.pb.HomelandPlayerInfoMsg.newBuilder();
        builder.setPlayerId(roundData.getPlayerId());
        if (roundData.getInfo() != null) {
            builder.setNickName(roundData.getInfo().getNickName());
            builder.setHeadIcon(roundData.getInfo().getHeadIcon());
            builder.setServerId(roundData.getInfo().getServerId());
            builder.setWxHeadUrl("");
            builder.setHeadInfo(getHeadInfo(roundData.getPlayerId(),roundData.getInfo()));
        }
        return builder;
    }

    private xddq.pb.HomelandRewardMsg.Builder getHomelandReward(HomelandItem homelandItem) {
        //message HomelandRewardMsg{
        //required string reward = 1;
        //required int32 rewardLv = 2;
        //required int32 distance = 3;
        //required int32 pos = 4;
        //required int32 maxWorkerNum = 5;
        //optional HomelandCompetitorMsg owner = 6;
        //optional HomelandCompetitorMsg enemy = 7;
        //optional int64 finishTime = 8;
        //required int64 playerId = 9;
        //optional bool isOnlyOwnerPull = 10;
        //optional int64 refreshTime = 11;
        //}
        xddq.pb.HomelandRewardMsg.Builder builder = xddq.pb.HomelandRewardMsg.newBuilder();
        builder.setReward(homelandItem.getItemId() + "=" + homelandItem.getNum());
        builder.setRewardLv(homelandItem.getRewardLv());
        builder.setDistance(homelandItem.getDistance());
        builder.setPos(homelandItem.getPos());
        builder.setMaxWorkerNum(homelandItem.getMaxWorkerNum());
        if (homelandItem.getOwner() != null) {
            builder.setOwner(getHomelandCompetitor(homelandItem.getOwner()));
        }
        if (homelandItem.getEnemy() != null) {
            builder.setEnemy(getHomelandCompetitor(homelandItem.getEnemy()));
        }
        builder.setFinishTime(homelandItem.getFinishTime());
        builder.setPlayerId(homelandItem.getPlayerId());
        builder.setIsOnlyOwnerPull(homelandItem.isOnlyOwnerPull());
        builder.setRefreshTime(homelandItem.getRefreshTime());
        return builder;
    }

    private xddq.pb.HomelandCompetitorMsg.Builder getHomelandCompetitor(HomelandWorking working) {
        //message HomelandCompetitorMsg{
        //  required int64 playerId = 1;
        //  required string nickName = 2;
        //  required int32 headIcon = 3;
        //  required int32 workerNum = 4;
        //  optional bool isWinner = 5;
        //  optional string wxHeadUrl = 6;
        //  optional PlayerHeadDataMsg headInfo = 7;
        //}
        xddq.pb.HomelandCompetitorMsg.Builder builder = xddq.pb.HomelandCompetitorMsg.newBuilder();
        if (working == null) {
            return builder;
        }
        builder.setPlayerId(working.getPlayerId());
        builder.setNickName(working.getNickName());
        builder.setHeadIcon(working.getHeadIcon());
        builder.setWorkerNum(working.getWorkerNum());
        builder.setIsWinner(working.isWinner());
        builder.setWxHeadUrl(working.getWxHeadUrl());
        builder.setHeadInfo(getHeadInfo(working.getHeadInfo()));
        return builder;
    }

    private xddq.pb.PlayerHeadDataMsg.Builder getHeadInfo(HeadInfo headInfo) {
        xddq.pb.PlayerHeadDataMsg.Builder builder = xddq.pb.PlayerHeadDataMsg.newBuilder();
        builder.setPlayerId(headInfo.getPlayerId());
        builder.setHeadIcon(headInfo.getHeadIcon());
        builder.setWxHeadUrl(headInfo.getWxHeadUrl());
        builder.setEquipHeadIconFrame(headInfo.getEquipHeadIconFrame());
        return builder;
    }

    private xddq.pb.PlayerHeadDataMsg.Builder getHeadInfo(long playerId,HomelandPlayerInfo headInfo) {

        xddq.pb.PlayerHeadDataMsg.Builder builder = xddq.pb.PlayerHeadDataMsg.newBuilder();
        builder.setPlayerId(playerId);
        builder.setHeadIcon(headInfo.getHeadIcon());
        builder.setWxHeadUrl(headInfo.getWxHeadUrl());
        builder.setEquipHeadIconFrame(headInfo.getEquipHeadIconFrame());
        return builder;
    }

    // 获得福地的奖励
    public xddq.pb.HomelandGetRewardResp.Builder homelandGetReward(Player player, xddq.pb.HomelandGetRewardReq req) {
        //message HomelandGetRewardResp{
        //  optional string reward = 1;
        //}

        //ActivityConditionDataListSync
        xddq.pb.HomelandGetRewardResp.Builder builder = xddq.pb.HomelandGetRewardResp.newBuilder();
        Homeland homeland = player.getHomeland();
        if (homeland == null) {
            return builder;
        }
        synchronized (homeland) {
            StringBuilder b = new StringBuilder();
            homeland.getRewards().forEach((k, v) -> b.append("|").append(k).append("=").append(v));
            if (!b.isEmpty()) {
                b.deleteCharAt(0);
            }
            builder.setReward(b.toString());
            List<long[]> items = ManagerPool.getInstance().itemManager.createItems(b.toString());
            ManagerPool.getInstance().backpackManager.changeItem(player, items);
        }
        homeland.getRewards().clear();
        return builder;
    }

    public void homelandLeave(Player player) {
        //message HomelandGetRewardResp{
        //  optional string reward = 1;
        //}
        player.setCurrentPage(0);

    }

    public void setActivityConditionDataListSync(Player player) {
        //message HomelandGetRewardResp{
        //  optional string reward = 1;
        //}
        xddq.pb.ActivityConditionDataListSync.Builder builder = xddq.pb.ActivityConditionDataListSync.newBuilder();
        Homeland homeland = player.getHomeland();
        for (HomelandItem homelandItem : homeland.getItems().values()) {
            // HomelandItem homelandItem = homeland.getItems().get(i);
            if (homelandItem != null) {
                if (homelandItem.getFinishTime() != 0 && homelandItem.getFinishTime() < TimeUtil.getCurrentTimeMillis()) {

                }
            }
        }

        //   activityConditionData

        MessageUtil.tellPlayer(player, new ActivityConditionDataListSync_1007Impl(builder));

    }


    public xddq.pb.HomelandDispatchPreviewResp.Builder homelandDispatchPreview(Player player, xddq.pb.HomelandDispatchPreviewReq req) {
        //message HomelandDispatchPreviewResp{
        //required int32 ret = 1;
        //optional int64 needTime = 2;
        //}
        xddq.pb.HomelandDispatchPreviewResp.Builder builder = xddq.pb.HomelandDispatchPreviewResp.newBuilder();
        Homeland homeland = player.getHomeland();
        HomelandItem homelandItem;
        if(req.getPlayerId() == player.getId()){

            if (homeland.getTotal() <= 0) {
                builder.setRet(1);
                return builder;
            }
            homelandItem = homeland.getItems().get(req.getPos());
        }else{
            homelandItem = homeland.getRoundData().get(req.getPlayerId()).getItems().get(req.getPos());

        }
        if (homelandItem == null) {
            builder.setRet(1);
            return builder;
        }

        int workNum = Math.min(Math.max(req.getWorkerNum(), 1), homelandItem.getMaxWorkerNum());
        DatahomelandrewardHomelandrewardBean rewardBean = BaseManagerPool.getInstance().dataManager.datahomelandrewardHomelandrewardContainer.getDatahomelandrewardHomelandrewardBean(homelandItem.getModelId());
        builder.setNeedTime(countTime(rewardBean.getTimeParam(), workNum));
        builder.setRet(0);
        return builder;
    }

    // 计算需要时间
    private long countTime(long base, int num) {
        return (base / num) * 20;
    }

    private HomelandWorking getHomelandWorking(HomelandWorking working0, int workNum) {
        HomelandWorking working = new HomelandWorking();
        working.setPlayerId(working0.getPlayerId());
        working.setNickName(working0.getNickName());
        working.setHeadIcon(working0.getHeadIcon());
        working.setWorkerNum(workNum);
        working.setWinner(true);
        working.setWxHeadUrl("");
        HeadInfo headInfo = new HeadInfo();
        headInfo.setPlayerId(working0.getHeadInfo().getPlayerId());
        headInfo.setHeadIcon(working0.getHeadInfo().getHeadIcon());
        headInfo.setWxHeadUrl("");
        headInfo.setEquipHeadIconFrame(working0.getHeadInfo().getEquipHeadIconFrame());
        working.setHeadInfo(headInfo);
        return working;
    }

    public xddq.pb.HomelandDispatchWorkerResp.Builder homelandDispatchWorker(Player player, xddq.pb.HomelandDispatchWorkerReq req) {
        //message HomelandDispatchWorkerResp{
        //required int32 ret = 1;
        //optional HomelandMsg homeland = 2;
        //}
        xddq.pb.HomelandDispatchWorkerResp.Builder builder = xddq.pb.HomelandDispatchWorkerResp.newBuilder();
//        Homeland homeland = homelands.get(req.getPlayerId());
//        if (homeland == null) {
//            return builder;
//        }
        Homeland myhomeland = player.getHomeland();
        if (myhomeland == null) {
            return builder;
        }
//        Homeland[] homelands = new Homeland[2];
//        if (req.getPlayerId() > player.getId()) {
//            homelands[0] = homeland;
//            homelands[1] = myhomeland;
//        } else {
//            homelands[0] = myhomeland;
//            homelands[1] = homeland;
//        }
//        synchronized (homelands[0]) {
//            synchronized (homelands[1]) {
//                HomelandItem homelandItem = homeland.getItems().get(req.getPos());
//                if (homelandItem == null) {
//                    return builder;
//                }
//                if (player.getId() != req.getPlayerId() && homelandItem.getEnemy() != null) {
//                    return builder;
//                } else if (player.getId() == req.getPlayerId() && homelandItem.getOwner() != null) {
//                    return builder;
//                }
//                int workNum = Math.min(Math.max(req.getWorkerNum(), 1), homelandItem.getMaxWorkerNum());
//                if (myhomeland.getWorkingNum() + workNum > myhomeland.getTotal()) {
//                    return builder;
//                }
//                DatahomelandrewardHomelandrewardBean rewardBean = BaseManagerPool.getInstance().dataManager.datahomelandrewardHomelandrewardContainer.getDatahomelandrewardHomelandrewardBean(homelandItem.getModelId());
//                long needTime = countTime(rewardBean.getTimeParam(), workNum);
//                // TODO 目前不考虑抢夺
//                HomelandWorking working = new HomelandWorking();
//                working.setPlayerId(player.getId());
//                working.setNickName(player.getName());
//                working.setHeadIcon(player.getHeadIcon());
//                working.setWorkerNum(workNum);
//                // TODO 微信头像
////        working.setWxHeadUrl()
//                try {
//                    working.setHeadInfo(JsonFormat.printer().print(ManagerPool.getInstance().playerManager.getPlayerHeadData(player)));
//                } catch (InvalidProtocolBufferException e) {
//                    log.error(e.getMessage(), e);
//                }
//
//                homelandItem.setFinishTime(TimeUtil.getCurrentTimeMillis() + needTime);
//                myhomeland.setWorkingNum(myhomeland.getWorkingNum() + workNum);
//            }
//        }
        //     synchronized (myhomeland) {
        if (req.getWorkerNum() == 0) {//召回
            builder.setRet(1);
            return builder;
        }
        if (player.getId() != req.getPlayerId() && !myhomeland.getRoundData().containsKey(req.getPlayerId())) {

            builder.setRet(12);
            return builder;
        }

        HomelandItem homelandItem = myhomeland.getItems().get(req.getPos());
        if (req.getPlayerId()!= player.getId()) {
            homelandItem = myhomeland.getRoundData().get(req.getPlayerId()).getItems().get(req.getPos());
            if(homelandItem == null) {
                builder.setRet(12);
                return builder;
            }
        }
        if (player.getId() == req.getPlayerId() && homelandItem.getOwner() != null ) {
            builder.setRet(11);
            return builder;
        }


        int workNum = Math.min(Math.max(req.getWorkerNum(), 1), homelandItem.getMaxWorkerNum());
        if (myhomeland.getWorkingNum() + workNum > myhomeland.getTotal()) {
            return builder;
        }

        DatahomelandrewardHomelandrewardBean rewardBean = BaseManagerPool.getInstance().dataManager.datahomelandrewardHomelandrewardContainer.getDatahomelandrewardHomelandrewardBean(homelandItem.getModelId());
        long needTime = countTime(rewardBean.getTimeParam(), workNum);
        // TODO 目前不考虑抢夺
        HomelandWorking working = new HomelandWorking();

            working.setPlayerId(player.getId());
            working.setNickName(player.getName());
            working.setHeadIcon(player.getCharaData().getEquipHeadIcon());
            //working.setWorkerNum(workNum);
            working.setWxHeadUrl("");
            HeadInfo headInfo = new HeadInfo();
            headInfo.setPlayerId(player.getId());
            headInfo.setHeadIcon(player.getCharaData().getEquipHeadIcon());
            headInfo.setWxHeadUrl("");
            headInfo.setEquipHeadIconFrame(player.getCharaData().getEquipHeadIconFrame());
            working.setHeadInfo(headInfo);


//            homelandItem.setFinishTime(TimeUtil.getCurrentTimeMillis() + needTime);
//            myhomeland.getHomelandTime().put(req.getPos(), homelandItem.getFinishTime());

//            RoundData roundData = myhomeland.getRoundData().get(req.getPlayerId());
//            working.setPlayerId(req.getPlayerId());
//            working.setNickName(roundData.getInfo().getNickName());
//            working.setHeadIcon(roundData.getInfo().getHeadIcon());
//            //working.setWorkerNum(workNum);
//            working.setWxHeadUrl("");
//            HeadInfo headInfo = new HeadInfo();
//            headInfo.setPlayerId(req.getPlayerId());
//            headInfo.setHeadIcon(roundData.getInfo().getHeadIcon());
//            headInfo.setWxHeadUrl("");
//            headInfo.setEquipHeadIconFrame(roundData.getInfo().getEquipHeadIconFrame());
//            working.setHeadInfo(headInfo);
//

        homelandItem.setFinishTime(TimeUtil.getCurrentTimeMillis() + needTime);

    //    if(myhomeland.getRoundTime().containsKey(req.getPlayerId())){
        myhomeland.getRoundTime().get(req.getPlayerId()).put(req.getPos(), homelandItem.getFinishTime());
//        }else{
//            ConcurrentHashMap<Integer, Long> times = new ConcurrentHashMap<>();
//            times.put(req.getPos(), homelandItem.getFinishTime());
//            myhomeland.getRoundTime().put(req.getPlayerId(),times);
//        }

        // TODO 微信头像
//        working.setWxHeadUrl()
//        try {
//            working.setHeadInfo(JsonFormat.printer().print(ManagerPool.getInstance().playerManager.getPlayerHeadData(player)));
//        } catch (InvalidProtocolBufferException e) {
//            log.error(e.getMessage(), e);
//        }

        myhomeland.setWorkingNum(myhomeland.getWorkingNum() + workNum);
        //   }

        int max = player.getActivityData().getCondValueMap().getOrDefault(TriggerType.FUDI_CAIJI_ALL.getValue(), 0);
        player.getActivityData().getCondValueMap().put(TriggerType.FUDI_CAIJI_ALL.getValue(), max + 1);
        // 触发任务
        ManagerPool.getInstance().taskManager.trigger(player, TriggerType.FUDI_CAIJI_ALL, null, max + 1, 0);

        //max = player.getActivityData().getCondValueMap().getOrDefault(TriggerType.FUDI_OTHER2.getValue(), 0);
        //player.getActivityData().getCondValueMap().put(TriggerType.FUDI_OTHER2.getValue(), max + 1);

        // 触发任务
        ManagerPool.getInstance().taskManager.trigger(player, TriggerType.FUDI_OTHER2, null, max + 1, 0);

        // 触发任务
        ManagerPool.getInstance().taskManager.trigger(player, TriggerType.FUDI_CAIJI, null, 0, 1);
        ManagerPool.getInstance().activityManager.addCondValue(player, TriggerType.FUDI_CAIJI, 1);
        ManagerPool.getInstance().activityManager.addCondValue(player, TriggerType.FUDI_SELF, 1);
        ManagerPool.getInstance().activityManager.addCondValue(player, TriggerType.FUDI_SELF2, 1);
        ManagerPool.getInstance().activityManager.addCondValue(player, TriggerType.FUDI_OTHER, 1);
        ManagerPool.getInstance().activityManager.addCondValue(player, TriggerType.FUDI_OTHER2, 1);


        if(req.getPlayerId()==player.getId()){
            homelandItem.setOwner(getHomelandWorking(working, req.getWorkerNum()));
            builder.setHomeland(getHomelandData(player,player.getHomeland()));
            sendHomelandChange(player);

        }else{//采集他人福地
            homelandItem.setEnemy(getHomelandWorking(working, req.getWorkerNum()));
            builder.setHomeland(getHomeland(player,player.getHomeland().getRoundData().get(req.getPlayerId()),false));
            sendRoundChange(player,player.getHomeland().getRoundData().get(req.getPlayerId()));

        }

        sendHomeland(player);
        builder.setRet(0);
        return builder;
    }

    // 周边其他福地
    public xddq.pb.HomelandExploreResp.Builder homelandExplore(Player player, xddq.pb.HomelandExploreReq req) {
        //message HomelandExploreResp{
        //  required int32 ret = 1;
        //  optional HomelandExploreMsg exploreData = 2;
        //}
        xddq.pb.HomelandExploreResp.Builder builder = xddq.pb.HomelandExploreResp.newBuilder();
        Homeland homeland = player.getHomeland();
        if (homeland == null) {
            return builder;
        }

//        if (homeland.getRewards().isEmpty() || homeland.getRoundRefreshTime() + 10 * 60 * 1000 < now) {
//            List<Long> keys = new ArrayList<>();
//            homeland.getRounds().forEach((k, v) -> {
//                if (k != player.getId()) keys.add(k);
//            });
//
//            synchronized (homeland) {
//                int needRefreshNumber = 4;
//                for (int i = 0; i < needRefreshNumber; i++) {
//                    if (keys.isEmpty()) {
//                        break;
//                    }
//                    long key = keys.remove(RandomUtils.nextInt(0, keys.size()));
//                    homeland.getRounds().put(key, 0L);
//                }
//                homeland.setRoundRefreshTime(now);
//            }
//        }

        builder.setRet(0);
        builder.setExploreData(getHomelandExplore(homeland));


        return builder;
    }

    private xddq.pb.HomelandExploreMsg.Builder getHomelandExplore(Homeland homeland) {
        //message HomelandExploreMsg{
        //  repeated HomelandExploreDataMsg nearHomeland = 1;
        //  repeated HomelandExploreDataMsg enemy = 2;
        //  required int64 lastRefreshTime = 3;
        //}
        xddq.pb.HomelandExploreMsg.Builder builder = xddq.pb.HomelandExploreMsg.newBuilder();
        homeland.getRoundData().forEach((k, v) -> {
           // Homeland round = homelands.get(k);
            builder.addNearHomeland(getHomelandExploreData(v));
        });
//        homeland.getEnemy().forEach((k, v) -> {
//            Homeland enemy = homelands.get(k);
//            builder.addNearHomeland(getHomelandExploreData(enemy));
//        });
        builder.setLastRefreshTime(homeland.getLastExpRefreshTime());
        return builder;
    }


    private xddq.pb.HomelandExploreDataMsg.Builder getHomelandExploreData(Player player) {
        //message HomelandExploreDataMsg{
        //required HomelandPlayerInfoMsg playerInfo = 1;
        //repeated int32 rewardId = 2;
        //optional bool isInCollecting = 3;
        //optional int32 enemyValue = 4;
        //}
        xddq.pb.HomelandExploreDataMsg.Builder builder = xddq.pb.HomelandExploreDataMsg.newBuilder();
        builder.setPlayerInfo(getHomelandPlayerInfo(player));
        for (HomelandItem homelandItem : player.getHomeland().getItems().values()) {
            //   HomelandItem homelandItem = homeland.getItems().get(i);
            if (homelandItem == null) {
                continue;
            }
            builder.addRewardId(homelandItem.getModelId());
        }
        return builder;
    }

    private xddq.pb.HomelandExploreDataMsg.Builder getHomelandExploreData(RoundData roundData) {
        //message HomelandExploreDataMsg{
        //required HomelandPlayerInfoMsg playerInfo = 1;
        //repeated int32 rewardId = 2;
        //optional bool isInCollecting = 3;
        //optional int32 enemyValue = 4;
        //}
        xddq.pb.HomelandExploreDataMsg.Builder builder = xddq.pb.HomelandExploreDataMsg.newBuilder();
        builder.setPlayerInfo(getRoundDataPlayerInfo(roundData));
        long now = TimeUtil.getCurrentTimeMillis();
        for (HomelandItem homelandItem : roundData.getItems().values()) {
            //   HomelandItem homelandItem = homeland.getItems().get(i);
            if (homelandItem == null || homelandItem.isReward() ) {
                continue;
            }
            if(homelandItem.getFinishTime()>0&& homelandItem.getFinishTime()>now ){
                builder.setIsInCollecting(true);
            }
            builder.addRewardId(homelandItem.getModelId());
        }
        return builder;
    }


    public xddq.pb.HomelandSaveSettingsRsp.Builder HomelandSaveSettings(Player player, xddq.pb.HomelandSaveSettingsReq req) {
        //message HomelandExploreMsg{
        //  repeated HomelandExploreDataMsg nearHomeland = 1;
        //  repeated HomelandExploreDataMsg enemy = 2;
        //  required int64 lastRefreshTime = 3;
        //}
        xddq.pb.HomelandSaveSettingsRsp.Builder builder = xddq.pb.HomelandSaveSettingsRsp.newBuilder();
        if (req.getItemSettingsCount() > 0) {
            req.getItemSettingsList().forEach(v->{
                HomelandAutoCollectItem item = new HomelandAutoCollectItem();
                item.setItemId(v.getItemId());
                item.setMinItemLv(v.getMinItemLv());
                item.setCheck(v.getIsCheck());
                player.getHomelandAutoCollectItem().add(item);
            });

        }
        builder.setRet(0);
        return builder;
    }


    public xddq.pb.HomelandAutoDispatchWorkerRsp.Builder HomelandAutoDispatchWorker(Player player, xddq.pb.HomelandAutoDispatchWorkerReq req) {
        //message HomelandExploreMsg{
        //  repeated HomelandExploreDataMsg nearHomeland = 1;
        //  repeated HomelandExploreDataMsg enemy = 2;
        //  required int64 lastRefreshTime = 3;
        //}
        xddq.pb.HomelandAutoDispatchWorkerRsp.Builder builder = xddq.pb.HomelandAutoDispatchWorkerRsp.newBuilder();
        Homeland homeland = player.getHomeland();
        if (homeland.getExpireTime() > TimeUtil.getCurrentTimeMillis()) {
            AutoDispatch(player, req.getItemSettingsList());
        }
//        if(req.getFirst()){ //设置有变化
//
//
//        }else{
//
//
//        }
        homeland.setNextTime(TimeUtil.getCurrentTimeMillis() + 30000);
        builder.setRet(0);
        builder.setNextTime(TimeUtil.getCurrentTimeMillis() + 30000);
        return builder;
    }


    public xddq.pb.HomelandBuyWorkerResp.Builder homelandBuyWorker(Player player, xddq.pb.HomelandBuyWorkerReq req) {
        xddq.pb.HomelandBuyWorkerResp.Builder builder = xddq.pb.HomelandBuyWorkerResp.newBuilder();
        Homeland myhomeland = player.getHomeland();
        if (myhomeland == null) {
            builder.setRet(1);
            return builder;
        }
        if (myhomeland.getTotal() >= 10) {
            myhomeland.setTotal(10);
            builder.setRet(7001);
            return builder;
        }

        String[] buyCosts = BaseManagerPool.getInstance().dataManager.datagameconfigGameconfigContainer.getDatagameconfigGameconfigBean(189).getValue().split(";");
        if (myhomeland.getTotal() > buyCosts.length) {
            builder.setRet(7001);
            return builder;
        }
        List<long[]> costs = ManagerPool.getInstance().itemManager.createCosts(buyCosts[myhomeland.getTotal() - 1]);
        if (!ManagerPool.getInstance().backpackManager.canChangeItem(player, costs)) {
            builder.setRet(101);
            return builder;
        }


        ManagerPool.getInstance().backpackManager.changeItem(player, costs);
        myhomeland.setTotal(myhomeland.getTotal() + 1);

        player.getActivityData().getCondValueMap().put(TriggerType.FUDI_MOUSE.getValue(), myhomeland.getTotal());
        // 触发任务
        ManagerPool.getInstance().taskManager.trigger(player, TriggerType.FUDI_MOUSE, null, myhomeland.getTotal(), 0);


        builder.setRet(0);
        sendHomeland(player);
        return builder;
    }


    public boolean AutoDispatch(Player player, List<xddq.pb.HomelandAutoCollectItem> homelandAutoCollectItem) {

        Homeland homeland = player.getHomeland();
        boolean isSuccess = false;
        synchronized (homeland) {
            if (homeland.getTotal() - homeland.getWorkingNum() > 0) {
                for (HomelandItem homelandItem : homeland.getItems().values()) {
                    if (homeland.getTotal() - homeland.getWorkingNum() <= 0) {
                        break;
                    }
                    for (int i = 0; i < homelandAutoCollectItem.size(); i++) {
                        if (homeland.getTotal() - homeland.getWorkingNum() <= 0) {
                            break;
                        }
                        xddq.pb.HomelandAutoCollectItem item = homelandAutoCollectItem.get(i);
                        if (homelandItem.getItemId() == item.getItemId()) {
                            if (item.getIsCheck()) {
                                if (homelandItem.getRewardLv() >= item.getMinItemLv()) {
                                    if (!homelandItem.isReward() && homelandItem.getFinishTime() == 0) {
                                        DatahomelandrewardHomelandrewardBean rewardBean = BaseManagerPool.getInstance().dataManager.datahomelandrewardHomelandrewardContainer.getDatahomelandrewardHomelandrewardBean(homelandItem.getModelId());
                                        long needTime = countTime(rewardBean.getTimeParam(), 1);
                                        homelandItem.setFinishTime(TimeUtil.getCurrentTimeMillis() + needTime);
                                        if(homeland.getRoundTime().containsKey(player.getId())){
                                            homeland.getRoundTime().get(player.getId()).put(homelandItem.getPos(), homelandItem.getFinishTime());
                                        }else{
                                            ConcurrentHashMap<Integer, Long> times = new ConcurrentHashMap<>();
                                            times.put(homelandItem.getPos(), homelandItem.getFinishTime());
                                            homeland.getRoundTime().put(player.getId(),times);
                                        }
                                      //  homeland.getHomelandTime().put(homelandItem.getPos(), homelandItem.getFinishTime());
                                        HomelandWorking working = new HomelandWorking();
                                        working.setPlayerId(player.getId());
                                        working.setNickName(player.getName());
                                        working.setHeadIcon(player.getCharaData().getEquipHeadIcon());
                                        //working.setWorkerNum(workNum);
                                        working.setWxHeadUrl("");
                                        HeadInfo headInfo = new HeadInfo();
                                        headInfo.setPlayerId(player.getId());
                                        headInfo.setHeadIcon(player.getCharaData().getEquipHeadIcon());
                                        headInfo.setWxHeadUrl("");
                                        headInfo.setEquipHeadIconFrame(player.getCharaData().getEquipHeadIconFrame());
                                        working.setHeadInfo(headInfo);
                                        homelandItem.setOwner(getHomelandWorking(working, 1));
                                        homeland.setWorkingNum(homeland.getWorkingNum() + 1);
                                        isSuccess = true;
                                        break;
                                    }
                                }
                            }
                        }

                    }

                }
            }
            if (homeland.getTotal() - homeland.getWorkingNum() > 0) {
                for(RoundData roundData:homeland.getRoundData().values()){
                    for (HomelandItem homelandItem : roundData.getItems().values()) {
                        if (homeland.getTotal() - homeland.getWorkingNum() <= 0) {
                            break;
                        }
                        for (int i = 0; i < homelandAutoCollectItem.size(); i++) {
                            if (homeland.getTotal() - homeland.getWorkingNum() <= 0) {
                                break;
                            }
                            xddq.pb.HomelandAutoCollectItem item = homelandAutoCollectItem.get(i);
                            if (homelandItem.getItemId() == item.getItemId()) {
                                if (item.getIsCheck()) {
                                    if (homelandItem.getRewardLv() >= item.getMinItemLv()) {
                                        if (!homelandItem.isReward() && homelandItem.getFinishTime() == 0) {
                                            DatahomelandrewardHomelandrewardBean rewardBean = BaseManagerPool.getInstance().dataManager.datahomelandrewardHomelandrewardContainer.getDatahomelandrewardHomelandrewardBean(homelandItem.getModelId());
                                            long needTime = countTime(rewardBean.getTimeParam(), 1);
                                            homelandItem.setFinishTime(TimeUtil.getCurrentTimeMillis() + needTime);
                                            if(homeland.getRoundTime().containsKey(roundData.getPlayerId())){
                                                homeland.getRoundTime().get(roundData.getPlayerId()).put(homelandItem.getPos(), homelandItem.getFinishTime());
                                            }else{
                                                ConcurrentHashMap<Integer, Long> times = new ConcurrentHashMap<>();
                                                times.put(homelandItem.getPos(), homelandItem.getFinishTime());
                                                homeland.getRoundTime().put(roundData.getPlayerId(),times);
                                            }
                                            //  homeland.getHomelandTime().put(homelandItem.getPos(), homelandItem.getFinishTime());
                                            HomelandWorking working = new HomelandWorking();
                                            working.setPlayerId(player.getId());
                                            working.setNickName(player.getName());
                                            working.setHeadIcon(player.getCharaData().getEquipHeadIcon());
                                            //working.setWorkerNum(workNum);
                                            working.setWxHeadUrl("");
                                            HeadInfo headInfo = new HeadInfo();
                                            headInfo.setPlayerId(player.getId());
                                            headInfo.setHeadIcon(player.getCharaData().getEquipHeadIcon());
                                            headInfo.setWxHeadUrl("");
                                            headInfo.setEquipHeadIconFrame(player.getCharaData().getEquipHeadIconFrame());
                                            working.setHeadInfo(headInfo);
                                            homelandItem.setEnemy(getHomelandWorking(working, 1));
                                            homeland.setWorkingNum(homeland.getWorkingNum() + 1);
                                            sendRoundChange(player,roundData);
                                            break;
                                        }
                                    }
                                }
                            }

                        }

                    }
                }

            }

            //  homelands.put(player.getId(),homeland);
        }

        if (isSuccess) {
            sendHomelandChange(player);
            sendHomeland(player);
        }


        return isSuccess;
    }

    public void syncPlayerCornucopiaChange(Player player) {
        Homeland homeland = player.getHomeland();
        if (homeland == null) {
            return;
        }
        //message SyncHomelandMsg{
        //required int32 totalWorkerNum = 2;
        //required int32 freeWorkerNum = 1;
        //required int32 energy = 3;
        //required int32 lv = 4;
        //optional  HomelandSyncMouseManagerMsg mouseManager = 5;
        //}
        xddq.pb.SyncPlayerCornucopiaChangeMsg.Builder builder = xddq.pb.SyncPlayerCornucopiaChangeMsg.newBuilder();
        xddq.pb.CornucopiaMsg.Builder Cornucopia = xddq.pb.CornucopiaMsg.newBuilder();
        Cornucopia.setRechargeSlot(360000001);
        if (homeland.getCornucopiaInfo().size() > 0) {
            for (CornucopiaInfo info : homeland.getCornucopiaInfo().values()) {
                if (info.getRechargeSlot() >= Cornucopia.getRechargeSlot()) {
                    Cornucopia.setRechargeSlot(info.getRechargeSlot());
                    Cornucopia.setScheduleNum(info.getScheduleNum());
                    Cornucopia.setRewardTime(info.getRewardTime());
                    Cornucopia.setMoreTimes(info.getMoreTimes());
                }
            }
        }

        builder.setCornucopia(Cornucopia);

        MessageUtil.tellPlayer(player, new SyncPlayerCornucopiaChangeMsg_1066Impl(builder));
    }

    public void initRoundData(Player player){
        long now = TimeUtil.getCurrentTimeMillis();
        ConcurrentHashMap<Integer, Long> times = new ConcurrentHashMap<>();
            for(int i=0;i<10;i++){
                RoundData roundData = new RoundData();
                roundData.setPlayerId(-i-1);
                roundData.setItems(refresh(roundData,true));
                HomelandPlayerInfo info = new HomelandPlayerInfo();
                info.setHeadIcon(149003+i);
                info.setNickName(getRandName());
                info.setServerId(1);
                info.setEquipHeadIconFrame(0);
                info.setWxHeadUrl("");
                roundData.setInfo(info);
                player.getHomeland().getRoundData().put((long)-i-1,roundData);
                player.getHomeland().getRoundTime().put((long)-i-1,times);
            }
        player.getHomeland().getRoundTime().put(player.getId(),times);
        player.getHomeland().setLastExpRefreshTime(now);
    }

    public xddq.pb.CornucopiaGetRewardRsp.Builder cornucopiaGetReward(Player player, xddq.pb.CornucopiaGetRewardReq req) {
        xddq.pb.CornucopiaGetRewardRsp.Builder builder = xddq.pb.CornucopiaGetRewardRsp.newBuilder();
        Homeland homeland = player.getHomeland();
        int count = 0;
        int rewardId = 100000;
        if (homeland.getCornucopiaInfo().size() > 0) {
            for (CornucopiaInfo info : homeland.getCornucopiaInfo().values()) {
                if (info.getRewardTime() == 0) {
                    if (info.getRechargeSlot() == 360000001) {
                        count += 1000;
                        info.setRewardTime((int) (TimeUtil.getCurrentTimeMillis()));
                    } else if (info.getRechargeSlot() == 360000002) {
                        count += 6000;
                        info.setRewardTime((int) (TimeUtil.getCurrentTimeMillis()));
                    } else if (info.getRechargeSlot() == 360000003) {
                        count += 10000;
                        info.setRewardTime((int) (TimeUtil.getCurrentTimeMillis()));
                    }
                }
            }
        }

        homeland.getCornucopiaInfo();
        if (count > 0) {
            builder.setRet(0);
            builder.setReward(rewardId + "=" + count);
            List<long[]> costs = new ArrayList<>();
            costs.add(new long[]{rewardId, count});
            ManagerPool.getInstance().backpackManager.changeItem(player, costs);
        } else {
            builder.setRet(12);
        }

        syncPlayerCornucopiaChange(player);
        return builder;
    }

}
