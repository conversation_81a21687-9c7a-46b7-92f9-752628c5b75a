package xddq.lawlook.manager;

import org.apache.commons.lang3.RandomUtils;
import xddq.attribute.consts.CalculatorTypeEnum;
import xddq.data.bean.SpiritBodyBean;
import xddq.lawlook.structs.SpiritBodyRefine;
import xddq.manager.BaseManagerPool;
import xddq.manager.ManagerPool;
import xddq.pb.*;
import xddq.pb.message.LawLooksLoginSyncMsg_18711Impl;
import xddq.pb.message.LawLooksSyncMsg_18714Impl;
import xddq.player.structs.Player;

import xddq.rule.structs.RuleTrialDataTemp;
import xddq.utils.MessageUtil;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

public class LawlookManager {

    final ConcurrentHashMap<Integer, SpiritBodyRefine> refineList = new ConcurrentHashMap<>();
    final ConcurrentHashMap<Integer, List<Integer>> typeList = new ConcurrentHashMap<>();
    final ConcurrentHashMap<Integer, SpiritBodyBean> spiritBodyList = new ConcurrentHashMap<>();
    final ConcurrentHashMap<Integer, Integer> round50 = new ConcurrentHashMap<>();
    final ConcurrentHashMap<Integer, Integer> round80 = new ConcurrentHashMap<>();
    final ConcurrentHashMap<Integer, Integer> round100 = new ConcurrentHashMap<>();
    final ConcurrentHashMap<Integer, Integer> round150 = new ConcurrentHashMap<>();
    final ConcurrentHashMap<Integer, Integer> typeRound = new ConcurrentHashMap<>();
    final ConcurrentHashMap<Integer, Integer> starUp = new ConcurrentHashMap<>();
    final ConcurrentHashMap<Integer, List<Integer>> combineList = new ConcurrentHashMap<>();
    public void init() {

        BaseManagerPool.getInstance().dataManager.spiritBodyRefineContainer.getList().forEach((k)->{
            SpiritBodyRefine refine = new SpiritBodyRefine();
            refine.setId(k.getId());
            refine.setType(k.getType());
            refine.setAttributePool(k.getAttributePool());
            refine.setSpecialAttr(k.getSpecialAttr());
            refine.setQualityPool(k.getQualityPool());
            refineList.put(k.getId(),refine);

            List<Integer> typelist= new ArrayList<>();
            String[] list2 = k.getAttributePool().split("\\|");
            for(int i = 0; i < list2.length; i++){
                typelist.add(Integer.parseInt(list2[i].split("=")[0]));
            }
            typeList.put(k.getId(),typelist);

        });

        BaseManagerPool.getInstance().dataManager.spiritBodyContainer.getList().forEach((k)->{
            spiritBodyList.put(k.getId(),k);
            if(combineList.get(k.getCombine()) == null){
                List<Integer> list =new ArrayList<>();
                list.add(k.getId());
                combineList.put(k.getCombine(),list);
            }else{
                combineList.get(k.getCombine()).add(k.getId());
            }
        });
    //    "attributePool": "550017=100|550018=100|550019=100|550004=100|550005=100|550006=100|550007=100|550008=100|550009=100|550010=100|550011=100|550012=100|550013=100|550014=100|550015=100|550022=100|550020=100|550018=100|550030=100",

        typeRound.put(550017,80);
        typeRound.put(550018,80);
        typeRound.put(550019,80);
        typeRound.put(550005,80);
        typeRound.put(550006,80);
        typeRound.put(550007,80);
        typeRound.put(550008,80);
        typeRound.put(550009,80);
        typeRound.put(550010,80);
        typeRound.put(550011,80);
        typeRound.put(550012,80);
        typeRound.put(550013,80);
        typeRound.put(550014,80);
        typeRound.put(550015,80);
        typeRound.put(550016,80);
        typeRound.put(550021,50);
        typeRound.put(550022,50);
        typeRound.put(550023,150);
        typeRound.put(550024,150);
        typeRound.put(550025,100);
        typeRound.put(550026,100);
        typeRound.put(550027,100);
        typeRound.put(550028,100);
        typeRound.put(550052,100);//强化法相
        typeRound.put(550053,100);//弱化法相

        round50.put(1,5);
        round50.put(2,10);
        round50.put(3,15);
        round50.put(4,20);
        round50.put(5,25);
        round50.put(6,5);

        round80.put(1,10);
        round80.put(2,20);
        round80.put(3,30);
        round80.put(4,40);
        round80.put(5,50);
        round80.put(6,80);

        round100.put(1,10);
        round100.put(2,20);
        round100.put(3,30);
        round100.put(4,50);
        round100.put(5,80);
        round100.put(6,100);

        round150.put(1,20);
        round150.put(2,40);
        round150.put(3,60);
        round150.put(4,80);
        round150.put(5,100);
        round150.put(6,150);

        starUp.put(2,2);
        starUp.put(3,2);
        starUp.put(4,2);
        starUp.put(5,2);
        starUp.put(6,2);
        starUp.put(7,2);
        starUp.put(8,3);
        starUp.put(9,3);
        starUp.put(10,3);

    }


    public void sendLawlook(Player player) {
        LawLooksLoginSyncMsg.Builder builder = LawLooksLoginSyncMsg.newBuilder();
        xddq.model.LawLooksEnterMsg enterData = player.getLawLookData();
        if(enterData.getNowLawlook()==null ){
            ManagerPool.getInstance().playerManager.initLawlook(player);
            enterData = player.getLawLookData();
        }
        if(enterData.getNowLawlook().getId()==0){
        }else{
            LawLooksDataMsg.Builder builder1 = LawLooksDataMsg.newBuilder();
            xddq.model.LawLooksDataMsg nowLawlook =enterData.getNowLawlook();
            builder1.setId(nowLawlook.getId());
            builder1.setConfigId(nowLawlook.getConfigId());
            builder1.setStar(nowLawlook.getStar());
            builder1.setLock(nowLawlook.isLock());
            if(nowLawlook.getPassiveSkillInfoList().size()>0){
                nowLawlook.getPassiveSkillInfoList().forEach((m,n) -> {
                    LawLookPassiveSkillInfo.Builder builder2 = LawLookPassiveSkillInfo.newBuilder();
                    LawLooksRefreshSkillMsg.Builder builder3 = LawLooksRefreshSkillMsg.newBuilder();
                    builder3.setQuality(n.getRefreshSkillMsg().getQuality());
                    AttributeDataMsg.Builder builder4 = AttributeDataMsg.newBuilder();
                    builder4.setValue(n.getRefreshSkillMsg().getAttributeData().getValue());
                    builder4.setType(n.getRefreshSkillMsg().getAttributeData().getType());
                    builder3.setAttributeData(builder4);
                    builder2.setRefreshSkillMsg(builder3);
                    builder1.addPassiveSkillInfoList(builder2);
                });
            }
            builder.setLawLooksData(builder1);
        }

        builder.setAdDrawTimesToday(enterData.getAdDrawTimesToday());
        builder.setFreeDrawTimesToday(enterData.getFreeDrawTimesToday());
        MessageUtil.tellPlayer(player, new LawLooksLoginSyncMsg_18711Impl(builder));
    }

    public xddq.pb.LawLooksEnterResp.Builder LawLooksEnter(Player player) {

        xddq.pb.LawLooksEnterResp.Builder builder = xddq.pb.LawLooksEnterResp.newBuilder();
        builder.setRet(0);
        builder.setEnterMsg(getEnterMsg(player));
        return builder;
    }
    private LawLooksEnterMsg.Builder getEnterMsg(Player player) {

        LawLooksEnterMsg.Builder builder = LawLooksEnterMsg.newBuilder();
        xddq.model.LawLooksEnterMsg enterData = player.getLawLookData();
        builder.setAdDrawTimesToday(enterData.getAdDrawTimesToday());
        builder.setDrawTimes(enterData.getDrawTimes());
        builder.setEnsureTimes(enterData.getEnsureTimes());
        builder.setResetLawLooksTimesToday(enterData.getResetLawLooksTimesToday());
        builder.setFreeDrawTimesToday(enterData.getFreeDrawTimesToday());
        builder.setRefreshSkillTimes(enterData.getRefreshSkillTimes());

        if(enterData.getCollectInfoList().size()>0){
            enterData.getCollectInfoList().forEach((k, v) -> {
                LawLooksCollectInfo.Builder  builder1 = LawLooksCollectInfo.newBuilder();
                builder1.setCount(v.count);
                builder1.setLawLooksConfigId(v.getLawLooksConfigId());
                builder.addCollectInfoList(builder1);
            });
        }
        if(enterData.getCombatInfoList().size()>0){
            enterData.getCombatInfoList().forEach((k, v) -> {
                LawLooksCombatInfo.Builder  builder1 = LawLooksCombatInfo.newBuilder();
                builder1.setId(v.getId());
                builder1.setBodyIndex(v.getBodyIndex());
                builder.addCombatInfoList(builder1);
            });
        }

        if(enterData.getLawLooksDataList().size()>0){
            enterData.getLawLooksDataList().forEach((k, v) -> {
                builder.addLawLooksDataList( getLawLooksData(v));
            });
        }

        if(enterData.getCombineSkillList().size()>0){
            enterData.getCombineSkillList().forEach((k, v) -> {
                LawLooksCombineSkillMsg.Builder  builder1 = LawLooksCombineSkillMsg.newBuilder();
                builder1.setLv(v.getLv());
                builder1.setCombineId(v.getCombineId());
                builder.addCombineSkillList(builder1);
            });
        }


        return builder;
    }



    public xddq.pb.LawLooksEquipResp.Builder LawLooksEquip(Player player,xddq.pb.LawLooksEquipReq req) {

        xddq.pb.LawLooksEquipResp.Builder builder = xddq.pb.LawLooksEquipResp.newBuilder();
        xddq.model.LawLooksEnterMsg lawLooksEnterMsg = player.getLawLookData();
        if(req.getType()==2){//下阵
            if(lawLooksEnterMsg.getNowLawlook().getId()!= req.getLawLooksId()){
                builder.setRet(12);
                return builder;
            }
            lawLooksEnterMsg.getNowLawlook().setId(0);
            builder.setLawLooksId(0);
        }else{
            if( lawLooksEnterMsg.getLawLooksDataList().get(req.getLawLooksId())!=null){
                nowlookFresh(player,req.getLawLooksId());
            }else {
                builder.setRet(12);
                return builder;
            }

            builder.setLawLooksId(req.getLawLooksId());
        }
        //属性变化
        ManagerPool.getInstance().attributeManager.countPlayerAttribute(player, CalculatorTypeEnum.SPIRIT_BODY, false);
        ManagerPool.getInstance().playerManager.sendPlayerAttrData(player);

        builder.setRet(0);

        return builder;
    }

    public xddq.pb.LawLooksDrawResp.Builder LawLooksDraw(Player player,xddq.pb.LawLooksDrawReq req) {

        xddq.pb.LawLooksDrawResp.Builder builder = xddq.pb.LawLooksDrawResp.newBuilder();
        xddq.model.LawLooksEnterMsg lawLooksEnterMsg = player.getLawLookData();

        if(req.getDrawTimes()>5){
            builder.setRet(12);
            return builder;
        }
        List<long[]> costs = new ArrayList<>();
        costs.add(new long[]{100184, -req.getDrawTimes()}); //引灵灯
        String reward = "";
        List<long[]> rewardlist = new ArrayList<>();

        if (!ManagerPool.getInstance().backpackManager.canChangeItem(player, costs)) {
            builder.setRet(101);
            return builder;
        }

        ManagerPool.getInstance().backpackManager.changeItem(player, costs);
        lawLooksEnterMsg.setDrawTimes(lawLooksEnterMsg.getDrawTimes()+req.getDrawTimes());
        for(int i=0;i<req.getDrawTimes();i++){

           int id = randomdraw();
           if(lawLooksEnterMsg.getDrawTimes()>=200){
               int[] list = {3001,3002,3003,3004,3005};
               id = list[RandomUtils.nextInt(0, 5)];
           }
           if(id<9999){
               if(id>3000){
                   lawLooksEnterMsg.setDrawTimes(0);
               }
               builder.addLawLooksDataList(getLawLooksData(creatLawlook(lawLooksEnterMsg,id)));
           }else{
               rewardlist.add(new long[]{ id, 1});
               reward += id+"=1"+"\\|";
           }
        }
        if(rewardlist.size()>0){
            ManagerPool.getInstance().backpackManager.changeItem(player, rewardlist);
        }
        builder.setRet(0);
        if(reward.length()>0){
            builder.setReward(reward);
        }
        sendLawLooksSync(player);

        return builder;
    }

    public xddq.pb.LawLooksActiveCombineResp.Builder LawLooksActiveCombine(Player player,xddq.pb.LawLooksActiveCombineReq req) {

        xddq.pb.LawLooksActiveCombineResp.Builder builder = xddq.pb.LawLooksActiveCombineResp.newBuilder();
        xddq.model.LawLooksEnterMsg lawLooksEnterMsg = player.getLawLookData();
        List<Integer>  list = combineList.get(req.getCombineId());
        if(lawLooksEnterMsg.getCollectInfoList().get(list.get(0)) == null){
            builder.setRet(12);
            return builder;
        }
        int count = lawLooksEnterMsg.getCollectInfoList().get(list.get(0)).getCount();
        int level = 1;
        for(int i = 1;i<list.size();i++){
            if(lawLooksEnterMsg.getCollectInfoList().get(list.get(i)) == null){
                builder.setRet(12);
                return builder;
            }
            int num = lawLooksEnterMsg.getCollectInfoList().get(list.get(i)).getCount();
            if(num<count){
                count = num;
            }
        }
        if(1<=count && count<3){ // 缘分等级 待添加
        }else if(3<=count && count<5){
            level = 2;
        }else if(5<=count && count<7){
            level = 3;
        }else if(7<=count && count<10){
            level = 4;
        }else if(10<=count  && count<15){
            level = 5;
        }else if(15<=count  && count<25){
            level = 6;
        }else if(25<=count  && count<40){
            level = 7;
        }else if(40<=count  && count<60){
            level = 8;
        }else if(60<=count  && count<100){
            level = 9;
        }else {
            level = 10;
        }

        if(lawLooksEnterMsg.getCombineSkillList().get(req.getCombineId())==null){
            xddq.model.LawLooksCombineSkillMsg skill = new xddq.model.LawLooksCombineSkillMsg();
            skill.setCombineId(req.getCombineId());
            skill.setLv(level);
            lawLooksEnterMsg.getCombineSkillList().put(req.getCombineId(),skill);
        }else{
            lawLooksEnterMsg.getCombineSkillList().get(req.getCombineId()).setLv(level);
        }

        sendLawLooksSync(player);
        //属性变化
        ManagerPool.getInstance().attributeManager.countPlayerAttribute(player, CalculatorTypeEnum.SPIRIT_BODY, false);
        ManagerPool.getInstance().playerManager.sendPlayerAttrData(player);

        builder.setRet(0);
        return builder;
    }



    public int randomdraw(){

        int random =   RandomUtils.nextInt(0, 10000);
        if(random<1212){ //尘阶
            int random1 =RandomUtils.nextInt(1,5);
            return 1000+random1;
        }else if(1212 <= random && random < 5757){
            int random1 =RandomUtils.nextInt(1,5);
            return 511000+random1;
        }else if(5757 <= random && random < 6363){ //凡阶
            int random1 =RandomUtils.nextInt(1,5);
            return 2000+random1;
        }else if(6363 <= random && random < 9191){
            int random1 =RandomUtils.nextInt(1,5);
            return 512000+random1;
        }else if(9191 <= random && random < 9292){ //灵阶
            int random1 =RandomUtils.nextInt(1,6);
            return 3000+random1;
        }else{
            int random1 =RandomUtils.nextInt(1,6);
            return 513000+random1;
        }

    }
    public xddq.model.LawLooksDataMsg creatLawlook(xddq.model.LawLooksEnterMsg lawLooksEnterMsg,int id){
        xddq.model.LawLooksDataMsg lawLooksDataMsg = new xddq.model.LawLooksDataMsg();
        lawLooksDataMsg.setId(lawLooksEnterMsg.getIdCount());
        lawLooksDataMsg.setStar(1);
        lawLooksDataMsg.setLock(false);
        lawLooksDataMsg.setConfigId(id);
        int slot = spiritBodyList.get(id).getQuality()*2;
        ConcurrentHashMap<Integer, xddq.model.LawLookPassiveSkillInfo> passiveSkillInfoList = new ConcurrentHashMap<>();

        for(int m=1; m< slot +1 ; m++){
            passiveSkillInfoList.put(m-1, getPassiveSkillInfo(m,false));
        }
        lawLooksDataMsg.setPassiveSkillInfoList(passiveSkillInfoList);
        lawLooksEnterMsg.getLawLooksDataList().put(lawLooksEnterMsg.getIdCount(),lawLooksDataMsg);
        lawLooksEnterMsg.setIdCount(lawLooksEnterMsg.getIdCount()+1);
        if(lawLooksEnterMsg.getCollectInfoList().get(id)==null){
            xddq.model.LawLooksCollectInfo info = new xddq.model.LawLooksCollectInfo();
            info.setLawLooksConfigId(id);
            info.setCount(1);
            lawLooksEnterMsg.getCollectInfoList().put(id,info);
        }else{
            lawLooksEnterMsg.getCollectInfoList().get(id).setCount(lawLooksEnterMsg.getCollectInfoList().get(id).getCount()+1);
        }
        return lawLooksDataMsg;
    }


    public xddq.model.LawLooksDataMsg creatLawlook(xddq.model.LawLooksEnterMsg lawLooksEnterMsg,int id,int oldid){//继承洗练技能
        xddq.model.LawLooksDataMsg lawLooksDataMsg = new xddq.model.LawLooksDataMsg();
        xddq.model.LawLooksDataMsg oldData = lawLooksEnterMsg.getLawLooksDataList().get(oldid);
        lawLooksDataMsg.setId(lawLooksEnterMsg.getIdCount());
        lawLooksDataMsg.setStar(1);
        lawLooksDataMsg.setLock(false);
        lawLooksDataMsg.setConfigId(id);
        int slot = BaseManagerPool.getInstance().dataManager.spiritBodyQualityContainer.getSpiritBodyQualityBean(spiritBodyList.get(id).getQuality()).getSkillSlot();

        ConcurrentHashMap<Integer, xddq.model.LawLookPassiveSkillInfo> passiveSkillInfoList = new ConcurrentHashMap<>();

        for(int m=1; m< slot +1 ; m++){
            if(m>oldData.getPassiveSkillInfoList().size()){
                passiveSkillInfoList.put(m-1, getPassiveSkillInfo(m,false));
            }else{
                passiveSkillInfoList.put(m-1,getPassiveSkillInfo(oldData.getPassiveSkillInfoList().get(m-1)));
            }
        }
        lawLooksDataMsg.setPassiveSkillInfoList(passiveSkillInfoList);
        lawLooksEnterMsg.getLawLooksDataList().put(lawLooksEnterMsg.getIdCount(),lawLooksDataMsg);
        lawLooksEnterMsg.setIdCount(lawLooksEnterMsg.getIdCount()+1);
        if(lawLooksEnterMsg.getCollectInfoList().get(id)==null){
            xddq.model.LawLooksCollectInfo info = new xddq.model.LawLooksCollectInfo();
            info.setLawLooksConfigId(id);
            info.setCount(1);
            lawLooksEnterMsg.getCollectInfoList().put(id,info);
        }else{
            lawLooksEnterMsg.getCollectInfoList().get(id).setCount(lawLooksEnterMsg.getCollectInfoList().get(id).getCount()+1);
        }

        return lawLooksDataMsg;
    }




    public xddq.model.LawLookPassiveSkillInfo getPassiveSkillInfo(int id,boolean isred) {
        xddq.model.LawLookPassiveSkillInfo info1= new xddq.model.LawLookPassiveSkillInfo();
        int quality = 1;
        int randomquality =  RandomUtils.nextInt(0, 4170);
        xddq.model.LawLooksRefreshSkillMsg refreshSkillMsg = new xddq.model.LawLooksRefreshSkillMsg();
        if(randomquality<2000){
            refreshSkillMsg.setQuality(quality);
        }else if(2000 <= randomquality && randomquality < 3000){
            quality = 2;
        }else if(3000 <= randomquality && randomquality < 3800){
            quality = 3;
        }else if(3800 <= randomquality && randomquality < 4100){
            quality = 4;
        }else if(4100 <= randomquality && randomquality < 4160){
            quality = 5;
        }else{
            quality = 6;
        }
        if(isred){
            quality = 6;
        }
        refreshSkillMsg.setQuality(quality);
        xddq.model.AttributeDataMsg attributeData = new xddq.model.AttributeDataMsg();
        List<Integer> list = new ArrayList<>();
        for(int i =0;i< typeList.get(id).size();i++){
            list.add(typeList.get(id).get(i));
        }
        Collections.shuffle(list);
        int type = list.get(0);
        attributeData.setType(type-550000);
        int value = 0;
        if(typeRound.get(type)==50){
            if(  quality ==1){
                value = RandomUtils.nextInt(1, round50.get(quality)+1);
            }else{
                value = RandomUtils.nextInt(round50.get(quality-1)+1, round50.get(quality)+1);
            }
        }else if(typeRound.get(type)==80){
            if(  quality ==1){
                value = RandomUtils.nextInt(1, round80.get(quality)+1);
            }else{
                value = RandomUtils.nextInt(round80.get(quality-1)+1, round80.get(quality)+1);
            }
        }else if(typeRound.get(type)==100){
            if(  quality ==1){
                value = RandomUtils.nextInt(1, round100.get(quality)+1);
            }else{
                value = RandomUtils.nextInt(round100.get(quality-1)+1, round100.get(quality)+1);
            }
        }else if(typeRound.get(type)==150){
            if(  quality ==1){
                value = RandomUtils.nextInt(1, round150.get(quality)+1);
            }else{
                value = RandomUtils.nextInt(round150.get(quality-1)+1, round150.get(quality)+1);
            }
        }
        attributeData.setValue(Integer.toString(value));
        refreshSkillMsg.setAttributeData(attributeData);
        info1.setRefreshSkillMsg(refreshSkillMsg);
        return info1;
    }
    public LawLooksDataMsg.Builder getLawLooksData( xddq.model.LawLooksDataMsg v){
            LawLooksDataMsg.Builder  builder1 = LawLooksDataMsg.newBuilder();
            builder1.setId(v.getId());
            builder1.setStar(v.getStar());
            builder1.setConfigId(v.getConfigId());
            builder1.setLock(v.isLock());
            if(v.getPassiveSkillInfoList()!=null && v.getPassiveSkillInfoList().size()>0){
            v.getPassiveSkillInfoList().forEach((m,n) -> {
                LawLookPassiveSkillInfo.Builder builder2 = LawLookPassiveSkillInfo.newBuilder();
                LawLooksRefreshSkillMsg.Builder builder3 = LawLooksRefreshSkillMsg.newBuilder();
                LawLooksRefreshSkillMsg.Builder builder5 = LawLooksRefreshSkillMsg.newBuilder();
                builder3.setQuality(n.getRefreshSkillMsg().getQuality());
                AttributeDataMsg.Builder builder4 = AttributeDataMsg.newBuilder();
                builder4.setValue(n.getRefreshSkillMsg().getAttributeData().getValue());
                builder4.setType(n.getRefreshSkillMsg().getAttributeData().getType());
                builder3.setAttributeData(builder4);
                builder2.setRefreshSkillMsg(builder3);
                if(n.getAlternateSkillMsg()!=null){
                    builder5.setQuality(n.getAlternateSkillMsg().getQuality());
                    AttributeDataMsg.Builder builder6 = AttributeDataMsg.newBuilder();
                    builder6.setValue(n.getAlternateSkillMsg().getAttributeData().getValue());
                    builder6.setType(n.getAlternateSkillMsg().getAttributeData().getType());
                    builder5.setAttributeData(builder6);
                    builder2.setAlternateSkillMsg(builder5);
                }
                builder1.addPassiveSkillInfoList(builder2);
            });
        }

        return builder1;
    }
    public void sendLawLooksSync(Player player){
        LawLooksSyncMsg.Builder builder = LawLooksSyncMsg.newBuilder();
        builder.setEnterMsg(getEnterMsg(player))  ;
        MessageUtil.tellPlayer(player, new LawLooksSyncMsg_18714Impl(builder));
    }


    public xddq.model.LawLookPassiveSkillInfo getPassiveSkillInfo(xddq.model.LawLookPassiveSkillInfo v){
        xddq.model.LawLookPassiveSkillInfo info = new xddq.model.LawLookPassiveSkillInfo();
        xddq.model.LawLooksRefreshSkillMsg refreshSkillMsg = new xddq.model.LawLooksRefreshSkillMsg();
        refreshSkillMsg.setQuality(v.getRefreshSkillMsg().getQuality());
        xddq.model.AttributeDataMsg attributeData = new xddq.model.AttributeDataMsg();
        attributeData.setType(v.getRefreshSkillMsg().getAttributeData().getType());
        attributeData.setValue(v.getRefreshSkillMsg().getAttributeData().getValue());
        refreshSkillMsg.setAttributeData(attributeData);
        info.setRefreshSkillMsg(refreshSkillMsg);
        if(v.getAlternateSkillMsg()!=null){
            xddq.model.LawLooksRefreshSkillMsg alterData = new xddq.model.LawLooksRefreshSkillMsg();
            alterData.setQuality(v.getAlternateSkillMsg().getQuality());
            xddq.model.AttributeDataMsg attributeData1 = new xddq.model.AttributeDataMsg();
            attributeData1.setType(v.getAlternateSkillMsg().getAttributeData().getType());
            attributeData1.setValue(v.getAlternateSkillMsg().getAttributeData().getValue());
            alterData.setAttributeData(attributeData1);
            info.setAlternateSkillMsg(alterData);
        }
        return info;
    }

    public xddq.pb.LawLooksExitResp.Builder LawLooksExit(Player player) {

        xddq.pb.LawLooksExitResp.Builder builder = xddq.pb.LawLooksExitResp.newBuilder();
        builder.setRet(0);
        return builder;
    }


    public xddq.pb.LawLooksWashSkillResp.Builder LawLooksWashSkill(Player player,LawLooksWashSkillReq req) {

        xddq.pb.LawLooksWashSkillResp.Builder builder = xddq.pb.LawLooksWashSkillResp.newBuilder();

        xddq.model.LawLooksEnterMsg lawLooksEnterMsg = player.getLawLookData();
        int index = req.getSkillIndex();
        int id = req.getLawLooksId();
        List<long[]> costs = new ArrayList<>();
        costs.add(new long[]{100186, -10});
        if (!ManagerPool.getInstance().backpackManager.canChangeItem(player, costs)) {
            builder.setRet(101);
            return builder;
        }
        ManagerPool.getInstance().backpackManager.changeItem(player, costs);
        lawLooksEnterMsg.setRefreshSkillTimes(lawLooksEnterMsg.getRefreshSkillTimes()+1);
        boolean isred = false;
        if(lawLooksEnterMsg.getRefreshSkillTimes()>=500){
            isred = true;
            lawLooksEnterMsg.setRefreshSkillTimes(0);
        }


        xddq.model.LawLooksRefreshSkillMsg alternateSkillMsg = new xddq.model.LawLooksRefreshSkillMsg();
        xddq.model.LawLookPassiveSkillInfo info1= getPassiveSkillInfo(index+1,isred);
        alternateSkillMsg.setQuality(info1.getRefreshSkillMsg().getQuality());
        alternateSkillMsg.setAttributeData(info1.getRefreshSkillMsg().getAttributeData());

        lawLooksEnterMsg.getLawLooksDataList().get(id).getPassiveSkillInfoList().get(index).setAlternateSkillMsg(alternateSkillMsg);

        builder.setRet(0);
        builder.setLawLooksData(getLawLooksData(lawLooksEnterMsg.getLawLooksDataList().get(id)));
        builder.setRefreshSkillTimes(lawLooksEnterMsg.getRefreshSkillTimes());
        return builder;
    }


    public xddq.pb.LawLooksSaveRefreshResp.Builder LawLooksSaveRefresh(Player player,LawLooksSaveRefreshReq req) {

        xddq.pb.LawLooksSaveRefreshResp.Builder builder = xddq.pb.LawLooksSaveRefreshResp.newBuilder();
        xddq.model.LawLooksEnterMsg lawLooksEnterMsg = player.getLawLookData();
        int index = req.getSkillIndex();
        int id = req.getLawLooksId();
        xddq.model.LawLookPassiveSkillInfo info = lawLooksEnterMsg.getLawLooksDataList().get(id).getPassiveSkillInfoList().get(index);
        if(info.getAlternateSkillMsg() ==null){
            builder.setRet(12);
            return builder;
        }
        int quality = info.getAlternateSkillMsg().getQuality();
        int type = info.getAlternateSkillMsg().getAttributeData().getType();
        String value = info.getAlternateSkillMsg().getAttributeData().getValue();

        info.getRefreshSkillMsg().setQuality(quality);
        info.getRefreshSkillMsg().getAttributeData().setType(type);
        info.getRefreshSkillMsg().getAttributeData().setValue(value);

        lawLooksEnterMsg.getLawLooksDataList().get(id).getPassiveSkillInfoList().get(index).setAlternateSkillMsg(null);
        if(id == lawLooksEnterMsg.getNowLawlook().getId()){
            //属性变化
            ManagerPool.getInstance().attributeManager.countPlayerAttribute(player, CalculatorTypeEnum.SPIRIT_BODY, false);
            ManagerPool.getInstance().playerManager.sendPlayerAttrData(player);
        }
        builder.setRet(0);
        builder.setLawLooksData(getLawLooksData(lawLooksEnterMsg.getLawLooksDataList().get(id)));
        return builder;
    }


    public xddq.pb.LawLooksStarUpResp.Builder LawLooksStarUp(Player player,LawLooksStarUpReq req) {

        xddq.pb.LawLooksStarUpResp.Builder builder = xddq.pb.LawLooksStarUpResp.newBuilder();
        xddq.model.LawLooksEnterMsg lawLooksEnterMsg = player.getLawLookData();
        int id= req.getLawLooksId();
        List<Integer>  list = req.getCostLawLooksIdListList();
        int quality = spiritBodyList.get( lawLooksEnterMsg.getLawLooksDataList().get(id).getConfigId()).getQuality();
        int star = lawLooksEnterMsg.getLawLooksDataList().get(id).getStar();
        int cost = Integer.valueOf(BaseManagerPool.getInstance().dataManager.spiritBodyQualityContainer.getSpiritBodyQualityBean(quality).getStarCost().split("\\|")[star-1]);
        if(cost>list.size()){
            builder.setRet(12);
            return builder;
        }
        for(int i=0;i<list.size();i++){
            if(spiritBodyList.get( lawLooksEnterMsg.getLawLooksDataList().get(list.get(i)).getConfigId()).getQuality()!=quality){
                builder.setRet(12);
                return builder;
            }else{
                xddq.model.LawLooksDataMsg data = lawLooksEnterMsg.getLawLooksDataList().get(list.get(i));
                lawLooksEnterMsg.getDeleteList().put(data.getId(),data);
                lawLooksEnterMsg.getLawLooksDataList().remove(list.get(i));
            }
        }
        lawLooksEnterMsg.getLawLooksDataList().get(id).setStar(star+1);
        int num =  lawLooksEnterMsg.getLawLooksDataList().get(id).getPassiveSkillInfoList().size();
        if(quality==4){
            if(star == 2 || star == 4){
                lawLooksEnterMsg.getLawLooksDataList().get(id).getPassiveSkillInfoList().put(num,getPassiveSkillInfo(num+1,false));
            }
        }else if(quality==5){
            lawLooksEnterMsg.getLawLooksDataList().get(id).getPassiveSkillInfoList().put(num,getPassiveSkillInfo(num+1,false));
        }else if(quality==6){
            int extend = starUp.get(star+1);
            for(int i=0;i<extend;i++) {
                lawLooksEnterMsg.getLawLooksDataList().get(id).getPassiveSkillInfoList().put(num,getPassiveSkillInfo(num+1,false));
                num++;
            }

        }

        lawLooksEnterMsg.getLawLooksDataList().get(id).getCostlist().addAll(list);

        if(id == lawLooksEnterMsg.getNowLawlook().getId() ){
            nowlookFresh(player,id);
        }
        sendLawLooksSync(player);
        //属性变化
        if (player.getLawLookData().getNowLawlook() != null && req.getLawLooksId() == player.getLawLookData().getNowLawlook().getId()) {
            ManagerPool.getInstance().attributeManager.countPlayerAttribute(player, CalculatorTypeEnum.SPIRIT_BODY, false);
            ManagerPool.getInstance().playerManager.sendPlayerAttrData(player);
        }

        builder.setRet(0);
        builder.setLawLooksId(req.getLawLooksId());
        builder.setEquipLawLooksData(getLawLooksData(lawLooksEnterMsg.getNowLawlook()));
        return builder;
    }


    public xddq.pb.LawLooksCompoundResp.Builder LawLooksCompound(Player player,LawLooksCompoundReq req) {

        xddq.pb.LawLooksCompoundResp.Builder builder = xddq.pb.LawLooksCompoundResp.newBuilder();

        xddq.model.LawLooksEnterMsg lawLooksEnterMsg = player.getLawLookData();
        int compoundtype= req.getCompoundType();
        List<Integer>  list = req.getLawLooksIdListList();
        int id = 1001;
        int quality = spiritBodyList.get( lawLooksEnterMsg.getLawLooksDataList().get(list.get(0)).getConfigId()).getQuality()+1;
        if(quality<5){
            if(list.size()!=5){
                builder.setRet(12);
                return builder;
            }
        }else {
            if(list.size()<2){
                builder.setRet(12);
                return builder;
            }
        }
        int oldid =lawLooksEnterMsg.getLawLooksDataList().get(list.get(0)).getId();
        if(compoundtype == 2){ //凡阶 灵阶合成  随机
            if(quality == 2){
                int[] list1 = {2001,2002,2003,2004};
                int random = RandomUtils.nextInt(0,4);
                id = list1[random];
            }else if(quality == 3){
                int[] list1 = {3001,3002,3003,3004,3005};
                int random = RandomUtils.nextInt(0,5);
                id = list1[random];
            }else if(quality == 4){
                int[] list1 = {4001,4002,4003,4004,4005};
                int random = RandomUtils.nextInt(0,5);
                id = list1[random];
            }else if(quality == 5){
                int[] list1 = {5001,5002,5003,5004,5005};
                int random = RandomUtils.nextInt(0,5);
                id = list1[random];
            }else if(quality == 6){
                int[] list1 = {6001,6002,6003,6004,6005};
                int random = RandomUtils.nextInt(0,5);
                id = list1[random];
            }
            if(quality>3){
                builder.setLawLooksData(getLawLooksData(creatLawlook(lawLooksEnterMsg,id,oldid)));
            }else{
                builder.setLawLooksData(getLawLooksData(creatLawlook(lawLooksEnterMsg,id)));
            }

        }else if(compoundtype == 1){ //灵阶合成-指定
            id = req.getTarLawLooksConfigId();
            builder.setLawLooksData(getLawLooksData(creatLawlook(lawLooksEnterMsg,id,oldid)));
        }


        for(int i=0;i<list.size();i++){
            if(spiritBodyList.get( lawLooksEnterMsg.getLawLooksDataList().get(list.get(i)).getConfigId()).getQuality()!=quality-1){
                builder.setRet(12);
                return builder;
            }else{
                lawLooksEnterMsg.getLawLooksDataList().remove(list.get(i));
            }
        }
        sendLawLooksSync(player);
        builder.setRet(0);
        builder.setEquipLawLooksData(getLawLooksData(lawLooksEnterMsg.getNowLawlook()));
        return builder;
    }

    public xddq.pb.LawLooksPieceCompoundResp.Builder LawLooksPieceCompound(Player player,LawLooksPieceCompoundReq req) {

        xddq.pb.LawLooksPieceCompoundResp.Builder builder = xddq.pb.LawLooksPieceCompoundResp.newBuilder();

        xddq.model.LawLooksEnterMsg lawLooksEnterMsg = player.getLawLookData();
        int num = req.getNum();
        int configId = req.getLawLookConfigId();
        List<long[]> costs = new ArrayList<>();
        costs.add(new long[]{configId+510000, -num*50});
        if (!ManagerPool.getInstance().backpackManager.canChangeItem(player, costs)) {
            builder.setRet(101);
            return builder;
        }
        ManagerPool.getInstance().backpackManager.changeItem(player, costs);
        for(int i=0;i<num;i++){
            creatLawlook(lawLooksEnterMsg,configId);
        }

        sendLawLooksSync(player);

        builder.setRet(0);
        builder.setLawLookConfigId(configId);
        builder.setNum(num);
        return builder;
    }

    public xddq.pb.LawLooksChangeLockResp.Builder LawLooksChangeLock(Player player,LawLooksChangeLockReq req) {

        xddq.pb.LawLooksChangeLockResp.Builder builder = xddq.pb.LawLooksChangeLockResp.newBuilder();

        xddq.model.LawLooksEnterMsg lawLooksEnterMsg = player.getLawLookData();
        int srcId = req.getLawLooksId();
        lawLooksEnterMsg.getLawLooksDataList().get(srcId).setLock(req.getLock());

        builder.setRet(0);
        builder.setLawLooksData(getLawLooksData(lawLooksEnterMsg.getLawLooksDataList().get(srcId)));
        return builder;
    }


    public xddq.pb.LawLooksResetPreviewResp.Builder LawLooksResetPreview(Player player,LawLooksResetPreviewReq req) {

        xddq.pb.LawLooksResetPreviewResp.Builder builder = xddq.pb.LawLooksResetPreviewResp.newBuilder();

        xddq.model.LawLooksEnterMsg lawLooksEnterMsg = player.getLawLookData();
        int id = req.getLawLooksId();
        xddq.model.LawLooksDataMsg data  = lawLooksEnterMsg.getLawLooksDataList().get(id);
        if(data.getCostlist().size()>0){
            for(int i =0; i<data.getCostlist().size(); i++){
                xddq.model.LawLooksDataMsg olddata  =  lawLooksEnterMsg.getDeleteList().get(data.getCostlist().get(i));
                builder.addLawLooksDataList(getLawLooksData(olddata));
            }
        }else{
            builder.setRet(12);
            return builder;
        }
        builder.setRet(0);
        return builder;
    }


    public xddq.pb.LawLooksResetResp.Builder LawLooksReset(Player player,LawLooksResetReq req) {

        xddq.pb.LawLooksResetResp.Builder builder = xddq.pb.LawLooksResetResp.newBuilder();

        xddq.model.LawLooksEnterMsg lawLooksEnterMsg = player.getLawLookData();
        int id = req.getLawLooksId();
        List<long[]> costs = new ArrayList<>();
        costs.add(new long[]{100000, -500});
        if (!ManagerPool.getInstance().backpackManager.canChangeItem(player, costs)) {
            builder.setRet(101);
            return builder;
        }
        ManagerPool.getInstance().backpackManager.changeItem(player, costs);
        xddq.model.LawLooksDataMsg data  = lawLooksEnterMsg.getLawLooksDataList().get(id);
        data.setStar(1);
        builder.setLawLooksData(getLawLooksData(data));

        if(data.getCostlist().size()>0){
            for(int i =0; i<data.getCostlist().size(); i++){
                xddq.model.LawLooksDataMsg olddata  =  lawLooksEnterMsg.getDeleteList().get(data.getCostlist().get(i));
                lawLooksEnterMsg.getLawLooksDataList().put(olddata.getId(),olddata);
                builder.addLawLooksDataList(getLawLooksData(olddata));
                lawLooksEnterMsg.getDeleteList().remove(olddata.getId());
            }

            lawLooksEnterMsg.getLawLooksDataList().remove(id);
        }else{
            builder.setRet(12);
            return builder;
        }
        sendLawLooksSync(player);
        //属性变化
        ManagerPool.getInstance().attributeManager.countPlayerAttribute(player, CalculatorTypeEnum.SPIRIT_BODY, false);
        ManagerPool.getInstance().playerManager.sendPlayerAttrData(player);

        lawLooksEnterMsg.setResetLawLooksTimesToday(lawLooksEnterMsg.getResetLawLooksTimesToday()+1);
        builder.setRet(0);

        return builder;
    }

    public xddq.pb.LawLooksExtendsResp.Builder LawLooksExtends(Player player,LawLooksExtendsReq req) {

        xddq.pb.LawLooksExtendsResp.Builder builder = xddq.pb.LawLooksExtendsResp.newBuilder();

        xddq.model.LawLooksEnterMsg lawLooksEnterMsg = player.getLawLookData();
        int srcId = req.getSrcLawLooksId();
        int tarId = req.getTarLawLooksId();
        int quality = spiritBodyList.get(lawLooksEnterMsg.getLawLooksDataList().get(srcId).getConfigId()).getQuality();
        int cost = 500;
        if(quality == 5){
            cost = 2000;
        }else if (quality == 6){
            cost = 5000;
        }
        List<long[]> costs = new ArrayList<>();
        costs.add(new long[]{100000, -cost});
        if (!ManagerPool.getInstance().backpackManager.canChangeItem(player, costs)) {
            builder.setRet(101);
            return builder;
        }
        ManagerPool.getInstance().backpackManager.changeItem(player, costs);

        ConcurrentHashMap<Integer, xddq.model.LawLookPassiveSkillInfo> passiveSkillInfoList = lawLooksEnterMsg.getLawLooksDataList().get(tarId).getPassiveSkillInfoList();
        ConcurrentHashMap<Integer, xddq.model.LawLookPassiveSkillInfo> passiveSkillInfoListsrc = lawLooksEnterMsg.getLawLooksDataList().get(srcId).getPassiveSkillInfoList();
        int size = passiveSkillInfoListsrc.size();
        if(size > passiveSkillInfoList.size()){
            size = passiveSkillInfoList.size();
        }
        for(int m=0; m< size; m++){
            passiveSkillInfoList.put(m, passiveSkillInfoListsrc.get(m));
        }

        for(int m=1; m< passiveSkillInfoListsrc.size() +1 ; m++){
            passiveSkillInfoListsrc.put(m-1, getPassiveSkillInfo(m,false));
        }
       // sendLawLooksSync(player);
        builder.setRet(0);
        builder.setSrcLawLooksData(getLawLooksData(lawLooksEnterMsg.getLawLooksDataList().get(srcId)));
        builder.setTarLawLooksData(getLawLooksData(lawLooksEnterMsg.getLawLooksDataList().get(tarId)));
        return builder;
    }

    public xddq.pb.LawLooksOneKeyMergeResp.Builder LawLooksOneKeyMerge(Player player,LawLooksOneKeyMergeReq req) {

        xddq.pb.LawLooksOneKeyMergeResp.Builder builder = xddq.pb.LawLooksOneKeyMergeResp.newBuilder();
        xddq.model.LawLooksEnterMsg lawLooksEnterMsg = player.getLawLookData();
        List<long[]> costs = new ArrayList<>();
        List<long[]> create = new ArrayList<>();
        player.getBackpack().forEach((m,n) -> {
                if(511001<=m && m<=513005){
                    if(n>=50){
                        long num = n/50;
                        create.add(new long[]{m-510000, num});
                        costs.add(new long[]{m, -num*50});
                    }
                }
        });

        ManagerPool.getInstance().backpackManager.changeItem(player, costs);

        for (long[] item : create) {
            for(int i=0;i<item[1];i++){
                builder.addLawLooksDataList(getLawLooksData( creatLawlook(lawLooksEnterMsg,(int)item[0])));
            }
        }

        sendLawLooksSync(player);
        builder.setRet(0);

        return builder;
    }

    public void nowlookFresh(Player player,int id){
        xddq.model.LawLooksEnterMsg lawLooksEnterMsg = player.getLawLookData();
        xddq.model.LawLooksDataMsg nowLawlook = new xddq.model.LawLooksDataMsg();
        xddq.model.LawLooksDataMsg data = lawLooksEnterMsg.getLawLooksDataList().get(id);
        nowLawlook.setId(data.getId());
        nowLawlook.setConfigId(data.getConfigId());
        nowLawlook.setStar(data.getStar());
        nowLawlook.setLock(data.isLock());
        ConcurrentHashMap<Integer, xddq.model.LawLookPassiveSkillInfo> passiveSkillInfoList = new ConcurrentHashMap<>();
        if(data.getPassiveSkillInfoList().size()>0){
            data.getPassiveSkillInfoList().forEach((m,n) -> {
                xddq.model.LawLookPassiveSkillInfo builder2 = new xddq.model.LawLookPassiveSkillInfo();
                xddq.model.LawLooksRefreshSkillMsg builder3 = new xddq.model.LawLooksRefreshSkillMsg();
                xddq.model.LawLooksRefreshSkillMsg builder5 = new xddq.model.LawLooksRefreshSkillMsg();
                builder3.setQuality(n.getRefreshSkillMsg().getQuality());
                xddq.model.AttributeDataMsg builder4 = new xddq.model.AttributeDataMsg();
                builder4.setValue(n.getRefreshSkillMsg().getAttributeData().getValue());
                builder4.setType(n.getRefreshSkillMsg().getAttributeData().getType());
                builder3.setAttributeData(builder4);
                builder2.setRefreshSkillMsg(builder3);
                if(n.getAlternateSkillMsg()!=null){
                    builder5.setQuality(n.getAlternateSkillMsg().getQuality());
                    xddq.model.AttributeDataMsg builder6 = new xddq.model.AttributeDataMsg();
                    builder6.setValue(n.getAlternateSkillMsg().getAttributeData().getValue());
                    builder6.setType(n.getAlternateSkillMsg().getAttributeData().getType());
                    builder5.setAttributeData(builder6);
                    builder2.setAlternateSkillMsg(builder5);
                }

                passiveSkillInfoList.put(m,builder2);
            });
        }
        nowLawlook.setPassiveSkillInfoList(passiveSkillInfoList);

        lawLooksEnterMsg.setNowLawlook(nowLawlook);
    }

    public xddq.model.LawLooksDataMsg getLawLookData(Player player, int configId) {
        xddq.model.LawLooksDataMsg lawLooksDataMsg = null;
        for (xddq.model.LawLooksDataMsg l : player.getLawLookData().getLawLooksDataList().values()) {
            if (l.getConfigId() == configId) {
                lawLooksDataMsg = l;
            }
        }
        return lawLooksDataMsg;
    }

}
