package xddq.battle.manager;

import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import xddq.consts.GameConfig;
import xddq.fight.consts.FightTypeEnum;
import xddq.fight.manager.FightManagerPool;
import xddq.fight.structs.Fight;
import xddq.manager.BaseManagerPool;
import xddq.manager.ManagerPool;

import xddq.player.structs.Player;
import xddq.rank.consts.RankType;
import xddq.rank.structs.PlayerRank;
import xddq.task.consts.TriggerType;
import xddq.utils.TimeUtil;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.TreeMap;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Set;

//62	PVP_SPEEDUP_ROUND	20
//63	PVP_SPEEDUP_PARAM	100
//64	PVP_ROUND_LIMIT	20
//65	PVP_ROBOT_COUNT	20
//66	PVP_ROBOT_LEVEL	9|10
//67	PVP_CHALLENGE_COST	100026=1
//68	PVP_CHALLENGE_REWARD	100005=2|100003=2000
//69	PVP_CHALLENGE_COST_LIMIT	3
//70	PVP_PROTECT_LEVEL	3
//71	PVP_REFRESH_COST	100003=100
//72	PVP_REFRESH_TIME_LIMIT	5
//73	PVP_INITIAL_SCORE	1000
//74	PVP_MATCH_PARAM	10|10
//75	PVP_SCORE_CHANGE_PARAM	200|0;100|5;0|10;-100|15;-200|20
//76	PVP_SCORE_FAIL_PARAM	200|0;100|0;0|0;-100|0;-200|0
//77	PVP_SCORE_CHANGE_RATE	500
//78	PVP_SCORE_BASIC_PARAM	10
//79	PVP_PROTECT_TIME_PARAM	5|60|60
//80	PVP_DAILY_RANK_REWARD	1,1,100005=60|100000=300;2,2,100005=40|100000=200;3,3,100005=30|100000=100;4,4,100005=24|100000=80;5,5,100005=20|100000=50;6,10,100005=10|100000=50;11,20,100005=6|100000=50;21,50,100005=4|100000=50;51,500,100005=2|100000=10;501,9999,100005=1|100000=10
//81	PVP_WEEK_RANK_REWARD	1,1,100006=50|100000=2000;2,2,100006=40|100000=1500;3,3,100006=30|100000=1000;4,4,100006=25|100000=800;5,5,100006=25|100000=600;6,10,100006=20|100000=400;11,50,100006=20|100004=200;51,500,100006=15|100004=100;501,9999,100006=10|100004=60
//82	PVP_CLOUD_CHALLENGE_REWARD	100005=2|100116=25
//83	PVP_CLOUD_CHALLENGE_UNLOCK	129
//84	PVP_PLUS_QUALIFICATION_LOCK_TIME	1|5
//85	PVP_PLUS_UNLOCK_PARAM	24
//86	PVP_ROBOT_SCORE_LIMIT	500
//87	PVP_JUMP_CONDITION	90
public class BattleManager {

    private static long lastRankDailyRewardTime = 0;

    private static long lastRankWeekRewardTime = 0;

    private final TreeMap<Integer, String> dailyRewards2 = new TreeMap<>();

    private final TreeMap<Integer, String> weekRewards1 = new TreeMap<>();

    private final TreeMap<Integer, String> weekRewards2 = new TreeMap<>();

    public void reload() {
        // 每日奖励
        dailyRewards2.put(1, "100005=100|100000=300");
        dailyRewards2.put(2, "100005=80|100000=200");
        dailyRewards2.put(3, "100005=60|100000=100");
        dailyRewards2.put(4, "100005=40|100000=90");
        dailyRewards2.put(5, "100005=30|100000=80");
        dailyRewards2.put(10, "100005=24|100000=70");
        dailyRewards2.put(20, "100005=20|100000=60");
        dailyRewards2.put(50, "100005=10|100000=50");
        dailyRewards2.put(100, "100005=6|100000=40");
        dailyRewards2.put(200, "100005=4|100000=30");
        dailyRewards2.put(500, "100005=3|100000=20");
        dailyRewards2.put(9999, "100005=2|100000=10");

        weekRewards1.put(1, "100116=6000|100006=25|100004=200");
        weekRewards1.put(2, "100116=5500|100006=20|100004=175");
        weekRewards1.put(3, "100116=5000|100006=15|100004=150");
        weekRewards1.put(4, "100116=4500|100006=12|100004=125");
        weekRewards1.put(5, "100116=4250|100006=10|100004=100");
        weekRewards1.put(10, "100116=4000|100006=8|100004=75");
        weekRewards1.put(50, "100116=3750|100006=6|100004=60");
        weekRewards1.put(100, "100116=3500|100006=5|100004=45");
        weekRewards1.put(200, "100116=3400|100006=4|100004=30");
        weekRewards1.put(500, "100116=3350|100006=3|100004=20");
        weekRewards1.put(9999, "100116=3300|100006=2|100004=10");

        weekRewards2.put(1, "100006=50|100000=2000");
        weekRewards2.put(2, "100006=40|100000=1500");
        weekRewards2.put(3, "100006=30|100000=1000");
        weekRewards2.put(4, "100006=25|100000=800");
        weekRewards2.put(5, "100006=25|100000=600");
        weekRewards2.put(10, "100006=20|100000=400");
        weekRewards2.put(50, "100006=20|100004=200");
        weekRewards2.put(500, "100006=15|100004=100");
        weekRewards2.put(9999, "100006=10|100004=60");
    }

    public void refresh() {
        if (lastRankDailyRewardTime == 0) {
            lastRankDailyRewardTime = TimeUtil.getNextDateTime(TimeUtil.getCurrentTimeMillis());
        }
        if (lastRankDailyRewardTime < TimeUtil.getCurrentTimeMillis()) {
            lastRankDailyRewardTime = TimeUtil.getNextDateTime(TimeUtil.getCurrentTimeMillis());

            // 发放日奖励
            List<PlayerRank> ranks = ManagerPool.getInstance().rankManager.getPreRankLists(RankType.DOUFA2);
            CompletableFuture.runAsync(() -> {
                for (int i = 0; i < ranks.size(); i++) {
                    Player player = ManagerPool.getInstance().playerManager.getPlayer(ranks.get(i).getPlayerId());
                    if (player == null) {
                        continue;
                    }
                    sendDailyReward(player, i + 1);
                }
            });

        }

        if (lastRankWeekRewardTime == 0) {
            lastRankWeekRewardTime = TimeUtil.getNextWeekTime(TimeUtil.getCurrentTimeMillis());
        }
        if (lastRankWeekRewardTime < TimeUtil.getCurrentTimeMillis()) {
            lastRankWeekRewardTime = TimeUtil.getNextWeekTime(TimeUtil.getCurrentTimeMillis());

            // 发放周奖励
            List<PlayerRank> ranks1 = ManagerPool.getInstance().rankManager.getPreRankLists(RankType.DOUFA2);
            CompletableFuture.runAsync(() -> {
                for (int i = 0; i < ranks1.size(); i++) {
                    Player player = ManagerPool.getInstance().playerManager.getPlayer(ranks1.get(i).getPlayerId());
                    if (player == null) {
                        continue;
                    }
                    sendWeekReward2(player, i + 1);
                }
            });
            List<PlayerRank> ranks2 = ManagerPool.getInstance().rankManager.getPreRankLists(RankType.DOUFA1);
            CompletableFuture.runAsync(() -> {
                for (int i = 0; i < ranks2.size(); i++) {
                    Player player = ManagerPool.getInstance().playerManager.getPlayer(ranks2.get(i).getPlayerId());
                    if (player == null) {
                        continue;
                    }
                    sendWeekReward1(player, i + 1);
                }
            });
        }
    }

    public void sendDailyReward(Player player, int rank) {
        String reward = getReward(dailyRewards2, rank);
        if (StringUtils.isEmpty(reward)) {
            return;
        }
        ManagerPool.getInstance().mailManager.sendPlayerMail(player, 1, 0, "修仙老前辈", "今日斗法奖励", String.format("道友在今日斗法中大显神通，排名第%d，小妖们为道友送上好礼~", rank), reward, null);
    }

    public void sendWeekReward1(Player player, int rank) {
        String reward = getReward(weekRewards1, rank);
        if (StringUtils.isEmpty(reward)) {
            return;
        }
        ManagerPool.getInstance().mailManager.sendPlayerMail(player, 1, 0, "修仙老前辈", "本周跨服斗法奖励", String.format("道友在本周的跨服斗法中过关斩将，所向披靡，排名第%d，众妖臣服道友骁勇，特献上好礼~", rank), reward, null);
    }

    public void sendWeekReward2(Player player, int rank) {
        String reward = getReward(weekRewards2, rank);
        if (StringUtils.isEmpty(reward)) {
            return;
        }
        ManagerPool.getInstance().mailManager.sendPlayerMail(player, 1, 0, "修仙老前辈", "本周斗法奖励", String.format("道友在本周的斗法中过关斩将，所向披靡，排名第%d，众妖臣服道友骁勇，特献上好礼~", rank), reward, null);
    }

    private String getReward(TreeMap<Integer, String> rewards, int rank) {
        Integer k = rewards.ceilingKey(rank);
        if (k == null) {
            return null;
        }
        return rewards.get(k);
    }

    public xddq.pb.GetBattleListResp.Builder getBattleList(Player player) {
        //message GetBattleListResp {
        //    optional int32 ret = 1;
        //    repeated PlayerBattleShowDataMsg playerBattleShowDataMsg = 2;
        //    optional bool robotScoreLimit = 3;
        //}
        xddq.pb.GetBattleListResp.Builder builder = xddq.pb.GetBattleListResp.newBuilder();
        builder.setRet(0);
        // 获取排行榜
        List<PlayerRank> rankLists = ManagerPool.getInstance().rankManager.getRankLists(RankType.DOUFA1);


        if (player.getBattleData().getBattlePlayerList().isEmpty()) {
            builder.addAllPlayerBattleShowDataMsg(getPlayerBattle(player, rankLists));
        } else {
            List<Long> removeList = new ArrayList<>();
            player.getBattleData().getBattlePlayerList().forEach(id -> {
                Player p = ManagerPool.getInstance().playerManager.getPlayer(id);
                if (p != null) {
                    long score = 0;
                    for (PlayerRank rank : rankLists) {
                        if (rank.getPlayerId() == id) {
                            score = (long) rank.getScore();
                            break;
                        }
                    }
                    builder.addPlayerBattleShowDataMsg(getPlayerBattleShowData(p, score));
                } else {
                    removeList.add(id);
                }
            });
            for (Long id : removeList) {
                player.getBattleData().getBattlePlayerList().remove(id);
            }
        }

        if (builder.getPlayerBattleShowDataMsgCount() < 6) {
            ConcurrentHashMap<Long, Player> players = ManagerPool.getInstance().playerManager.getPlayers();
//            ArrayList<Player> playerList = new ArrayList<>(players.values());
//            if (!playerList.isEmpty()) {
//                playerList.removeIf(p -> p.getId() == player.getId() || player.getBattleData().getBattlePlayerList().contains(p.getId()) );
//            }
            List<Long> playerIdList = new ArrayList<>(20);
            if (players.size() > 0) {
                int num = 0;
                for (Long id : players.keySet()) {
                    if (num >= 20) {
                        break;
                    }
                    if (id != player.getId() && !playerIdList.contains(id)) {
                        playerIdList.add(id);
                    }
                    num += 1;
                }
                while (builder.getPlayerBattleShowDataMsgCount() < 6 && !playerIdList.isEmpty()) {
                    Long id = playerIdList.remove(RandomUtils.nextInt(0, playerIdList.size()));
                    Player _player = ManagerPool.getInstance().playerManager.getPlayer(id);
                    if (_player != null) {
                        if (!player.getBattleData().getBattlePlayerList().contains(_player.getId())) {
                            player.getBattleData().getBattlePlayerList().add(_player.getId());
                            builder.addPlayerBattleShowDataMsg(getPlayerBattleShowData(_player, 0));
                        }
                    }
                }
            }
        }

        return builder;
    }

    private List<xddq.pb.PlayerBattleShowDataMsg> getPlayerBattle(Player player, List<PlayerRank> rankLists) {
        List<xddq.pb.PlayerBattleShowDataMsg> list = new ArrayList<>();
        player.getBattleData().getBattlePlayerList().clear();
        if (rankLists.size() <= 5) {
            for (PlayerRank v : rankLists) {
                if (v.getPlayerId() == player.getId()) {
                    continue;
                }
                Player p = ManagerPool.getInstance().playerManager.getPlayer(v.getPlayerId());
                if (p != null) {
                    if (!player.getBattleData().getBattlePlayerList().contains(p.getId())) {
                        player.getBattleData().getBattlePlayerList().add(p.getId());
                        list.add(getPlayerBattleShowData(p, (long) v.getScore()).build());
                    }
                }
            }
        } else if (rankLists.size() <= 50) {
            int size = 0;
            List<Integer> sites = new ArrayList<>();
            for (int i = 0; i < rankLists.size(); i++) {
                sites.add(i);
            }
            while (true) {
                if (sites.size() <= 0) {
                    break;
                }
                PlayerRank v = rankLists.get(sites.remove(RandomUtils.nextInt(0, sites.size())));
                if (v.getPlayerId() == player.getId()) {
                    continue;
                }
                Player p = ManagerPool.getInstance().playerManager.getPlayer(v.getPlayerId());
                if (p == null) {
                    continue;
                }
                list.add(getPlayerBattleShowData(p, (long) v.getScore()).build());
                if (!player.getBattleData().getBattlePlayerList().contains(v.getPlayerId())) {
                    player.getBattleData().getBattlePlayerList().add(v.getPlayerId());
                }
                size++;
                if (size >= 5) {
                    break;
                }

            }
        } else {
            int size = 0;
            int duan = rankLists.size() / 5;
            while (true) {
                PlayerRank v = rankLists.get(RandomUtils.nextInt(duan * size, duan * (size + 1)));
                if (v.getPlayerId() == player.getId()) {
                    continue;
                }
                Player p = ManagerPool.getInstance().playerManager.getPlayer(v.getPlayerId());
                list.add(getPlayerBattleShowData(p, (long) v.getScore()).build());
                if (!player.getBattleData().getBattlePlayerList().contains(v.getPlayerId())) {
                    player.getBattleData().getBattlePlayerList().add(v.getPlayerId());
                }
                size++;
                if (size >= 5) {
                    break;
                }
            }
        }

        return list;
    }

    public xddq.pb.PlayerBattleShowDataMsg.Builder getPlayerBattleShowData(Player player, long score) {
        //message PlayerBattleShowDataMsg {
        //    optional PlayerBaseDataMsg playerBaseDataMsg = 1;
        //    optional int64 score = 2;
        //    optional int64 protectEndTime = 3;
        //}
        xddq.pb.PlayerBattleShowDataMsg.Builder builder = xddq.pb.PlayerBattleShowDataMsg.newBuilder();
        builder.setPlayerBaseDataMsg(ManagerPool.getInstance().playerManager.getPLayerBaseData(player));
        builder.setScore(score);
        // TODO protectEndTime
        builder.setProtectEndTime(0);
        return builder;
    }

    //message RankBattleChallengeReq {
    //    required int32 index = 1;
    //}
    public xddq.pb.RankBattleChallengeResp.Builder rankBattleChallenge(Player player, xddq.pb.RankBattleChallengeReq req) throws IOException {
        //message RankBattleChallengeResp {
        //    required int32 ret = 1;
        //    optional RankBattleResultMsg rankBattleResultMsg = 2;
        //}
        //message RankBattleResultMsg {
        //    optional BattleRecordMsg allBattleRecord = 1;
        //    optional bool challengeSuccess = 2;
        //    optional string rewards = 3;
        //    repeated BattleScoreChangeMsg battleScoreChangeMsg = 4;
        //}
        xddq.pb.RankBattleChallengeResp.Builder builder = xddq.pb.RankBattleChallengeResp.newBuilder();
        if (player.getBattleData().getBattlePlayerList().size() <= req.getIndex()) {
            return builder;
        }
        List<long[]> costs = ManagerPool.getInstance().itemManager.createCosts(GameConfig.PVP_CHALLENGE_COST);
        if (!ManagerPool.getInstance().backpackManager.canChangeItem(player, costs)) {
            builder.setRet(101);
            return builder;
        }

        ManagerPool.getInstance().backpackManager.changeItem(player, costs);

        long playerId = player.getBattleData().getBattlePlayerList().get(req.getIndex());
        Player target = ManagerPool.getInstance().playerManager.getPlayer(playerId);

        Fight fight = FightManagerPool.getInstance().fightManager.init("rank_" + player.getId() + "_" + target.getId() + "_" + TimeUtil.getCurrentTimeMillis(), FightTypeEnum.RANK.getValue(), player, target, 15, RandomUtils.nextInt(0, 10000));
        xddq.pb.BattleRecordMsg.Builder record = FightManagerPool.getInstance().fightManager.start(fight);

        long myOldScore = player.getBattleData().getScore();
        long targetOldScore = target.getBattleData().getScore();
        boolean success = (fight.getFinish() & 0x02) != 0;
//        // 增加战斗记录
//        RankRecord rankRecord = new RankRecord();
//        rankRecord.setFightPlayer(new RankPlayer(player.getId()));
//        rankRecord.setDefensePlayer(new RankPlayer(player2.getId()));
//        rankRecord.setFightPlayerRealms(player.getRealmsId());
//        rankRecord.setDefensePlayerRealms(player2.getRealmsId());
//        rankRecord.setFightPlayerRank(myRank);
//        rankRecord.setDefensePlayerRank(targetRank);
//        rankRecord.setWin(success);
//        rankRecord.setCreateTime(TimeUtil.getCurrentTimeMillis());
//
//        addRankRecord(player, rankRecord);
//        addRankRecord(player2, rankRecord);

        xddq.pb.RankBattleResultMsg.Builder build0 = xddq.pb.RankBattleResultMsg.newBuilder();
        if (success) {
            // TODO 临时
            if (player.getBattleData().getScore() == 0) {
                player.getBattleData().setScore(10);
            }
            player.getBattleData().setScore(player.getBattleData().getScore() + 10);
            addRank(player);

            String rewardStr = "100005=2|100003=2000";
            if (player.getUnlock().containsKey(129)) {
                rewardStr = "100005=2|100116=25";
            }
            List<long[]> rewardItems = ManagerPool.getInstance().itemManager.createItems(rewardStr);
            ManagerPool.getInstance().backpackManager.changeItem(player, rewardItems);
            build0.setRewards(rewardStr);
        } else {
            if (target.getBattleData().getScore() >= 0) {
                target.getBattleData().setScore(target.getBattleData().getScore() + 10);
                addRank(target);
            }

            build0.setRewards("");
        }

        // 触发任务
        ManagerPool.getInstance().taskManager.trigger(player, TriggerType.DOUFA, null, 0, 1);

        ManagerPool.getInstance().activityManager.addCondValue(player, TriggerType.DOUFA, 1);

        builder.setRet(0);

        build0.setAllBattleRecord(record);
        build0.setChallengeSuccess(success);
        build0.addBattleScoreChangeMsg(getBattleScoreChange(player, myOldScore));
        build0.addBattleScoreChangeMsg(getBattleScoreChange(target, targetOldScore));

        builder.setRankBattleResultMsg(build0);

        return builder;
    }

    private void addRank(Player player) {
        ManagerPool.getInstance().rankManager.addRank(player, RankType.DOUFA1, player.getBattleData().getScore());
        //ManagerPool.getInstance().rankManager.addRank(player, RankType.DOUFA2, player.getBattleData().getScore());
    }

    private xddq.pb.BattleScoreChangeMsg.Builder getBattleScoreChange(Player player, long oldScore) {
        //message BattleScoreChangeMsg {
        //    optional PlayerBaseDataMsg playerBaseDataMsg = 1;
        //    optional int64 score = 2;
        //    optional int64 changeScore = 3;
        //}
        xddq.pb.BattleScoreChangeMsg.Builder builder = xddq.pb.BattleScoreChangeMsg.newBuilder();

        builder.setPlayerBaseDataMsg(ManagerPool.getInstance().playerManager.getPLayerBaseData(player));
        builder.setScore(player.getBattleData().getScore());
        builder.setChangeScore(player.getBattleData().getScore() - oldScore);
        return builder;
    }

    public xddq.pb.RefreshBattleListResp.Builder refreshBattleList(Player player) {
        //message RefreshBattleListResp {
        //    optional int32 ret = 1;
        //    repeated PlayerBattleShowDataMsg playerBattleShowDataMsg = 2;
        //    optional bool robotScoreLimit = 3;
        //}
        xddq.pb.RefreshBattleListResp.Builder builder = xddq.pb.RefreshBattleListResp.newBuilder();

        // 获取排行榜
        List<PlayerRank> rankLists = ManagerPool.getInstance().rankManager.getRankLists(RankType.DOUFA1);
        List<long[]> costs = ManagerPool.getInstance().itemManager.createCosts(GameConfig.PVP_REFRESH_COST);
        if (!ManagerPool.getInstance().backpackManager.canChangeItem(player, costs)) {
            return builder;
        }

        ManagerPool.getInstance().backpackManager.changeItem(player, costs);
        player.getBattleData().getBattlePlayerList().clear();

        List<xddq.pb.PlayerBattleShowDataMsg> playerBattle = getPlayerBattle(player, rankLists);
        if (playerBattle.size() < 6) {
            ConcurrentHashMap<Long, Player> players = ManagerPool.getInstance().playerManager.getPlayers();
            ArrayList<Player> playerList = new ArrayList<>(players.values());
            if (!playerList.isEmpty()) {
                playerList.removeIf(p -> p.getId() == player.getId() || player.getBattleData().getBattlePlayerList().contains(p.getId()) );
            }
            while (playerBattle.size() < 6 && !playerList.isEmpty()) {
                Player _player = playerList.remove(RandomUtils.nextInt(0, playerList.size()));
                if (_player != null && !player.getBattleData().getBattlePlayerList().contains(_player.getId())) {
                    player.getBattleData().getBattlePlayerList().add(_player.getId());
                    playerBattle.add(getPlayerBattleShowData(_player, 0).build());
                }
            }
        }
        builder.setRet(0);
        builder.addAllPlayerBattleShowDataMsg(playerBattle);

        return builder;
    }
}
