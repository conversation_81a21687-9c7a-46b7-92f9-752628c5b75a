package xddq.chat.manager;

import com.alibaba.fastjson.JSON;
import com.google.protobuf.Message;
import com.google.protobuf.util.JsonFormat;
import xddq.consts.Global;
import xddq.data.DataPool;
import xddq.data.bean.DatagameitemsGameitemsBean;
import xddq.magic.structs.MagicMark;
import xddq.manager.BaseManagerPool;
import xddq.manager.ManagerPool;

import xddq.pb.message.*;
import xddq.player.structs.Player;
import xddq.server.impl.GameServer;
import xddq.task.consts.TriggerType;
import xddq.utils.MessageUtil;
import xddq.utils.TimeUtil;

import java.io.FileReader;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

public class ChatManager {
    public static List<Long> GM_ACC_IDS = new ArrayList<>();

    private static String[] banWords = {"看空间","送充值","Б","转区","球球","夹威","转区","转端","送充值","看名字","加名字","Q","无限","端","君","羊","单机","秒升","①","②","③","④","⑤","⑥","⑦","⑧","⑨","零","珺","壹","叁","肆","伍","福利","返利","死区","VX","V","1","2","3","4","5","6","7","8","9","人气","群","开口","客服","名额","扶持","有限","扶持","无限元宝","七三","折扣"};
    public static volatile HashSet<String> BAN_WORD = new HashSet<>();

    // 最大过期时间30分钟
    public static long overdue = 30 * TimeUtil.MINUTE;
    // 最大保存10条
    public static int max = 10;

    static {
        for (String s : banWords) {
            BAN_WORD.add(s);
        }

        GM_ACC_IDS.add(213375054397862L); // local: 111111
        GM_ACC_IDS.add(107771735933583L); // local: 222222
        GM_ACC_IDS.add(195755004723218L); // local: 121212
        GM_ACC_IDS.add(116579231334413L); // ZB: cx170326
        GM_ACC_IDS.add(116579232203003L); // ZB: PY666
        GM_ACC_IDS.add(116579231580241L); // ZB: XD8888
        GM_ACC_IDS.add(11022976794625L); // test01
        GM_ACC_IDS.add(11079170392070L); // sr88
        GM_ACC_IDS.add(11098118569994L); // 111111

    }

    public static void addGMId(long id) {
        if (!GM_ACC_IDS.contains(id)) {
            GM_ACC_IDS.add(id);
        }
    }

    private Vector<xddq.pb.WorldMessageTempMsg.Builder> worldChatHistory = DataPool.getInstance().chatData.getWorldChatHistory();

    private ConcurrentHashMap<Long, Vector<xddq.pb.WorldMessageTempMsg.Builder>> unionChatHistory = DataPool.getInstance().chatData.getUnionChatHistory();

    public void sendHistory(Player player) {
        xddq.pb.WorldMessageListMsg.Builder builder = xddq.pb.WorldMessageListMsg.newBuilder();
        Iterator<xddq.pb.WorldMessageTempMsg.Builder> iterator = worldChatHistory.iterator();
        long now = TimeUtil.getCurrentTimeMillis();
        while (iterator.hasNext()) {
            xddq.pb.WorldMessageTempMsg.Builder b = iterator.next();
            if (b.getSendTime() + overdue < now) {
                iterator.remove();
                continue;
            }
            builder.addDataList(b);
        }
        MessageUtil.tellPlayer(player, new WorldMessageListMsg_111Impl(builder));
        if (player.getUnionData().getUnionId()!=0) {
            if(unionChatHistory.containsKey(player.getUnionData().getUnionId())) {
                Vector<xddq.pb.WorldMessageTempMsg.Builder> v = unionChatHistory.get(player.getUnionData().getUnionId());
                iterator = v.iterator();
                while (iterator.hasNext()) {
                    xddq.pb.WorldMessageTempMsg.Builder b = iterator.next();
                    if (b.getSendTime() + overdue < now) {
                        iterator.remove();
                        continue;
                    }
                    builder.addDataList(b);
                }
                MessageUtil.tellPlayer(player, new WorldMessageListMsg_114Impl(builder));
            }
        }
    }

    public void chat(Player player, xddq.pb.WorldChatReqMsg req) throws IOException {
        if (Global.BAN_CHAT) {
            return;
        } else {
            xddq.pb.WorldChatRespMsg.Builder rsp = xddq.pb.WorldChatRespMsg.newBuilder();
            ////聊天响应（世界/妖盟） 20120
            //    message WorldChatRespMsg {
            //        required int32 ret =1;        //结果
            //        optional int64 forbiddenTime = 2; //禁言截止时间 单位ms , 0或者null 或者小于当前时间表示没有被禁言
            //
            //    }

            if (!GameServer.DEBUG) { // 正式区
                // 开口费限制
                if (player.getActivityData().getCondValueMap().containsKey(TriggerType.LEIJICHONGZHI.getValue())) {
                    int lcNum = player.getActivityData().getCondValueMap().get(TriggerType.LEIJICHONGZHI.getValue());
                    if (lcNum < Global.KAIKOU) {
                        return;
                    }
                } else {
                    return;
                }
                // 屏蔽词
                for (String word : ChatManager.BAN_WORD) {
                    if (req.getContent().contains(word)) {
                        rsp.setRet(17);
                        MessageUtil.tellPlayer(player, new WorldChatRespMsg_110Impl(rsp));
                        return;
                    }
                }
            }

            if (req.getContent() != null && req.getContent().length() > 100) {
                rsp.setRet(18);
                MessageUtil.tellPlayer(player, new WorldChatRespMsg_110Impl(rsp));
                return;
            }

            boolean forbid = false;
            if (player.getForbiddenTime() >= TimeUtil.getCurrentTimeMillis()) {
                rsp.setRet(27);
                forbid = true;
            } else {
                // 返回聊天成功消息
                rsp.setRet(0);
            }
            MessageUtil.tellPlayer(player, new WorldChatRespMsg_110Impl(rsp));
            if (forbid) {
                return;
            }

            //if (player.getCode() == Global.GM_CODE) {
            if (GameServer.DEBUG && GM_ACC_IDS.contains(player.getUserId())) {
                if (req.getContent().contains("=")) {
                    // GM命令
                    List<long[]> allItems = ManagerPool.getInstance().itemManager.createItems(req.getContent());
                    List<long[]> items = new ArrayList<>();
                    List<Integer> titleIdList = new ArrayList<>();
                    List<long[]> markList = new ArrayList<>();
                    for (int i = 0; i < allItems.size(); i++) {
                        long[] item = allItems.get(i);
                        DatagameitemsGameitemsBean ib = BaseManagerPool.getInstance().dataManager.datagameitemsGameitemsContainer.getDatagameitemsGameitemsBean((int)item[0]);
                        if (ib.getType() == 13) {
                            titleIdList.add((int)item[0]);
                        } else if (ib.getType() == 9) {
                            markList.add(new long[] { item[0], item[1] });
                        }
                        else {
                            items.add(item);
                        }
                    }
                    ManagerPool.getInstance().backpackManager.changeItem(player, items);

                    if (titleIdList.size() > 0) {
                        for (int titleId : titleIdList) {
                            player.getTitleData().getData().put(titleId, 0L);
                        }
                        MessageUtil.tellPlayer(player, new TitleSyncMsg_119Impl(ManagerPool.getInstance().playerManager.getTitleSyncMsg(player)));
                    }

                    if (markList.size() > 0) {
                        for (long[] i : markList) {
                            MagicMark mark = new MagicMark();
                            mark.setModelId((int)i[0]);
                            mark.setNum((int)i[1]);
                            player.getMagicData().getMagicMarks().put(mark.getModelId(), mark);
                            ManagerPool.getInstance().magicManager.sendMagicData(player);
                        }
                    }

                    return;
                }
            }
            //
            //    //服务端下发，登录时下发列表
            //    //消息集合（世界/妖盟）
            //    message WorldMessageListMsg {
            //        repeated WorldMessageTempMsg dataList = 1;
            //    }
            // 广播聊天消息
            xddq.pb.WorldMessageListMsg.Builder builder = xddq.pb.WorldMessageListMsg.newBuilder();
            xddq.pb.WorldMessageTempMsg.Builder b = castWorldChat2WorldMessage(player, req);
            if (req.getType() == 1) {
                worldChatHistory.add(b);
                if (worldChatHistory.size() > max) {
                    worldChatHistory.removeFirst();
                }
            } else if (req.getType() == 2 && player.getUnionData().getUnionId()!=0) {
                if(unionChatHistory.containsKey(player.getUnionData().getUnionId())) {
                    Vector<xddq.pb.WorldMessageTempMsg.Builder> v = unionChatHistory.get(player.getUnionData().getUnionId());
                    v.add(b);
                    if (v.size() > max) {
                        v.removeFirst();
                    }
                }
            }
            builder.addDataList(b);

            ConcurrentHashMap<Long, Player> players = ManagerPool.getInstance().playerManager.getPlayers();
            if (req.getType() == 1) {
                WorldMessageListMsg_111Impl res = new WorldMessageListMsg_111Impl(builder);
                players.forEach((k, _player) -> {
                    MessageUtil.tellPlayer(_player, res);
                });
            } else if (req.getType() == 2) {
                WorldMessageListMsg_114Impl res = new WorldMessageListMsg_114Impl(builder);
                players.forEach((k, _player) -> {
                    if (player.getUnionData().getUnionId() != 0 && _player.getUnionData().getUnionId() == player.getUnionData().getUnionId()) {
                        MessageUtil.tellPlayer(_player, res);
                    }
                });
            }
        }
    }

    ////聊天请求（世界/妖盟）
    //    message WorldChatReqMsg {
    //        required int32  type =1;        //类型，预留着，以后可能有其他的聊天
    //        required string content =2;     //内容
    //        optional int32  activityId =3;  //活动编号（跨服活动聊天，需要活动编号）
    //        optional int32 contentType = 4; //内容类型（1普通2表情）
    //        optional AtPlayerInfo atPlayerInfo = 5;   //@的对象信息  null表示没有@对象
    //        optional int64 friendPlayerId = 6; //私聊好友玩家id
    //    }
    ////@信息
    //    message AtPlayerInfo {
    //        optional int64 playerId = 1; //玩家id
    //        optional string nickName = 2; //玩家昵称
    //    }
    private xddq.pb.WorldMessageTempMsg.Builder castWorldChat2WorldMessage(Player player, xddq.pb.WorldChatReqMsg req) {
        ////消息模板（世界/妖盟）
        //    message WorldMessageTempMsg {
        //        required int32   type =1;                             //类型
        //        required int64   sendTime =2;                         //发送时间
        //        optional string  content =3;                          //内容
        //        optional int64   playerId =4;                         //用户编号
        //        optional PlayerBaseDataMsg playerBaseData =5;		      //基础数据
        //        optional int32   activityId =6;                       //活动编号
        //        optional int32 contentType = 7;                       //内容类型（1普通2表情）
        //        optional string extraParams = 8;                      //TODO 额外参数，区别开内容，需要带的数据放这里，分隔符&#!隔开
        //        optional bytes  extraData = 9;                        //TODO 对extraParams的补充，因为extraParams可能带分割符号，导致分割错误，1.6.1版本添加
        //        optional string logId = 10;                           //TODO 唯一标识  举报需求新增
        //        optional bool  Reported = 11;                         //TODO 是否已举报
        //        optional string ip = 12;                              //TODO ip
        //        optional AtPlayerInfo atPlayerInfo = 13;              //@的对象信息  null表示没有@对象
        //        optional int64 friendPlayerId = 14;                   //私聊好友玩家id
        //    }
        xddq.pb.WorldMessageTempMsg.Builder build = xddq.pb.WorldMessageTempMsg.newBuilder();
        build.setType(req.getType()).setSendTime(TimeUtil.getCurrentTimeMillis())
                .setContent(req.getContent()).setPlayerId(player.getId()).setPlayerBaseData(ManagerPool.getInstance().playerManager.getPLayerBaseData(player))
                .setActivityId(req.getActivityId()).setContentType(req.getContentType())
                .setAtPlayerInfo(req.getAtPlayerInfo()).setFriendPlayerId(req.getFriendPlayerId());
        return build;
    }
}
