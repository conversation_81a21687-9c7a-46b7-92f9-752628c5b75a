{"NewCloudLevelDB": [["id"], ["id", "stage", "level", "attack", "hp", "defence", "basicResist", "towardsResist", "liftCost", "liftExp", "upExp", "advanceCost"], [100001, 1, 10, 15, 100, 3, 0, 120, "100005=1", "1|10;2|0;3|0;4|0;5|0", 3, "100006=10"], [100002, 2, 20, 15, 100, 3, 120, 336, "100005=1", "1|10;2|0;3|0;4|0;5|0", 10, "100006=20"], [100003, 3, 30, 23, 152, 4, 240, 552, "100005=1", "1|10;2|0;3|0;4|0;5|0", 10, "100006=30"], [100004, 4, 60, 30, 201, 5, 360, 768, "100005=1", "1|10;2|0;3|0;4|0;5|0", 20, "100006=120"], [100005, 5, 120, 71, 474, 12, 480, 984, "100005=1", "1|10;2|0;3|0;4|0;5|0", 25, "100006=300"], [100006, 6, 280, 230, 1535, 38, 600, 1200, "100005=1", "1|10;2|0;3|0;4|0;5|0", 30, "100006=840"], [100007, 7, 560, 1765, 11764, 294, 720, 1416, "100005=1", "1|10;2|0;3|0;4|0;5|0", 30, "100006=1680"], [100008, 8, 950, 4626, 30843, 771, 840, 1632, "100005=1", "1|10;2|0;3|0;4|0;5|0", 30, "100006=2820"], [100009, 9, 1400, 7089, 47257, 1181, 960, 1848, "100005=1", "1|10;2|0;3|0;4|0;5|0", 30, "100006=4260"], [100010, 10, 2100, 7089, 47257, 1181, 1080, 2064, "100005=1", "1|10;2|0;3|0;4|0;5|0", 30, "100006=6390"], [100011, 11, 3200, 7089, 47257, 1181, 1200, 2280, "100005=1", "1|10;2|0;3|0;4|0;5|0", 30, "100006=9590"], [100012, 12, 4800, 7089, 47257, 1181, 1320, 2496, "100005=1", "1|10;2|0;3|0;4|0;5|0", 30, "100006=14390"], [100013, 13, 7200, 7089, 47257, 1181, 1440, 2712, "100005=1", "1|10;2|0;3|0;4|0;5|0", 30, "100006=21590"], [100014, 14, 7200, 7089, 47257, 1181, 1560, 2928, "100005=1", "1|10;2|0;3|0;4|0;5|0", 30, "100006=24750"], [100015, 15, 8400, 7089, 47257, 1181, 1680, 3144, "100005=1", "1|10;2|0;3|0;4|0;5|0", 30, "100006=27910"], [100016, 16, 9600, 7089, 47257, 1181, 1800, 3360, "100005=1", "1|10;2|0;3|0;4|0;5|0", 30, "100006=31070"], [100017, 17, 10800, 7089, 47257, 1181, 1920, 3576, "100005=1", "1|10;2|0;3|0;4|0;5|0", 30, "100006=34230"], [100018, 18, 12000, 7089, 47257, 1181, 2040, 3792, "100005=1", "1|10;2|0;3|0;4|0;5|0", 30, "100006=37390"], [100019, 19, 13200, 7089, 47257, 1181, 2160, 4008, "100005=1", "1|10;2|0;3|0;4|0;5|0", 30, "0"]]}